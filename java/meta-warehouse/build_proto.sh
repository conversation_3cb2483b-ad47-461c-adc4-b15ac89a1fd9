#!/bin/bash

cd `dirname $0`
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:../../third_party/cfs_dancenn_thirdparty/builds/third_party/protobuf/output/lib

files=(
  "cloudfs/HAServiceProtocol.proto"
  "cloudfs/IpcConnectionContext.proto"
  "cloudfs/ProtobufRpcEngine.proto"
  "cloudfs/RpcHeader.proto"
  "cloudfs/Security.proto"
  "cloudfs/ReconfigurationProtocol.proto"
  "cloudfs/acl.proto"
  "cloudfs/ClientDatanodeProtocol.proto"
  "cloudfs/ClientNamenodeProtocol.proto"
  "cloudfs/DatanodeProtocol.proto"
  "cloudfs/datatransfer.proto"
  "cloudfs/encryption.proto"
  "cloudfs/fsimage.proto"
  "cloudfs/hdfs.proto"
  "cloudfs/inotify.proto"
  "cloudfs/JournalProtocol.proto"
  "cloudfs/NamenodeProtocol.proto"
  "cloudfs/PlacementDriverProtocol.proto"
  "cloudfs/QJournalProtocol.proto"
  "cloudfs/TestProtocol.proto"
  "cloudfs/xattr.proto"
  "cloudfs/ranger.proto"
  "dancenn/inode.proto"
  "dancenn/block_info_proto.proto"
  "dancenn/audit_log.proto"
  "dancenn/edit_log.proto"
  "dancenn/status_code.proto"
  "btrace/btrace.proto"
  "databus/collector.proto"
)
for file in ${files[@]}
do
  ../../third_party/cfs_dancenn_thirdparty/builds/third_party/protobuf/output/bin/protoc \
    -I ../../src/proto/cloudfs                                                           \
    -I ../../src/proto/dancenn                                                           \
    -I ../../src/proto/btrace                                                            \
    -I ../../src/proto/databus                                                           \
    --java_out=src/main/java                                                             \
    "../../src/proto/${file}"
done
