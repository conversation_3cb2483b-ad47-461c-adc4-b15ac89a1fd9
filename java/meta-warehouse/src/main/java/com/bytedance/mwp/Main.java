package com.bytedance.mwp;

import com.bytedance.cloudfs.proto.AuditLogProtos;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;

public class Main {
  public static void main(String[] args) throws InvalidProtocolBufferException {
    System.out.println(JsonFormat.printer().print(AuditLogProtos.AuditLog.newBuilder().setCfsCluster("").setCfsEnv("").setCfsRegion("").setIsSuccessful(true)
      .setFilesystemId(1).setNamespaceId(1).setTsNanoseconds(1).setMethod(AuditLogProtos.AuditLog.Method.kMkdirs)));
  }
}
