package com.bytedance.mwp;

import com.bytedance.cloudfs.proto.AuditLogProtos.AuditLog;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import org.apache.hudi.common.config.TypedProperties;
import org.apache.hudi.common.util.Option;
import org.apache.hudi.utilities.deltastreamer.HoodieDeltaStreamerMetrics;
import org.apache.hudi.utilities.exception.HoodieSourceTimeoutException;
import org.apache.hudi.utilities.schema.SchemaProvider;
import org.apache.hudi.utilities.sources.InputBatch;
import org.apache.hudi.utilities.sources.JsonSource;
import org.apache.hudi.utilities.sources.helpers.KafkaOffsetGen;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.streaming.kafka010.KafkaUtils;
import org.apache.spark.streaming.kafka010.LocationStrategies;
import org.apache.spark.streaming.kafka010.OffsetRange;

import java.text.SimpleDateFormat;
import java.util.*;

public class AuditLogKafkaSource extends JsonSource {

  private static final Logger LOG = LogManager.getLogger(AuditLogKafkaSource.class);
  private final HoodieDeltaStreamerMetrics metrics;
  private final KafkaOffsetGen offsetGen;

  public AuditLogKafkaSource(TypedProperties properties,
                             JavaSparkContext sparkContext,
                             SparkSession sparkSession,
                             SchemaProvider schemaProvider,
                             HoodieDeltaStreamerMetrics metrics) {
    super(properties, sparkContext, sparkSession, schemaProvider);
    this.metrics = metrics;
    properties.put("key.deserializer", StringDeserializer.class.getName());
    properties.put("value.deserializer", ByteArrayDeserializer.class.getName());
    offsetGen = new KafkaOffsetGen(properties);
    LOG.info("offsetGen is " + offsetGen);
  }

  @Override
  protected InputBatch<JavaRDD<String>> fetchNewData(Option<String> lastCheckpointStr,
                                                     long sourceLimit) {
    try {
      LOG.info("last checkpoint is: " + lastCheckpointStr);
      OffsetRange[] offsetRanges = offsetGen.getNextOffsetRanges(lastCheckpointStr, sourceLimit, metrics);
      long totalNewMsgs = KafkaOffsetGen.CheckpointUtils.totalNewMessages(offsetRanges);
      LOG.info("About to read " + totalNewMsgs + " from Kafka for topic :" + offsetGen.getTopicName());
      if (totalNewMsgs <= 0) {
        return new InputBatch<>(Option.empty(), KafkaOffsetGen.CheckpointUtils.offsetsToStr(offsetRanges));
      }
      JavaRDD<String> newDataRDD = toRDD(offsetRanges);
      return new InputBatch<>(Option.of(newDataRDD), KafkaOffsetGen.CheckpointUtils.offsetsToStr(offsetRanges));
    } catch (org.apache.kafka.common.errors.TimeoutException e) {
      throw new HoodieSourceTimeoutException("Kafka Source timed out " + e.getMessage());
    }
  }

  private JavaRDD<String> toRDD(OffsetRange[] offsetRanges) {
    return KafkaUtils.createRDD(sparkContext,
        offsetGen.getKafkaParams(),
        offsetRanges,
        LocationStrategies.PreferConsistent())
      .map(ConsumerRecord::value)
      .filter(x -> x instanceof byte[])
      .map(x -> (byte[]) x)
      // https://stackoverflow.com/questions/52877723/apache-spark-map-function-org-apache-spark-sparkexception-task-not-serializable
      .map(bytes -> {
        try {
          return AuditLog.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
          e.printStackTrace();
          return null;
        }
      })
      .filter(Objects::nonNull)
      .map(pb -> {
        Map<String, Object> m = new HashMap<String, Object>();
        // We use SimplifiedAuditLog instead of AuditLog because:
        // 1. Avro doesn't support void Record, e.g. MsyncRequestProto.
        // 2. Avro doesn't support recursive schema,
        //    e.g. LocatedBlockProto::originalLocatedBlock.
        m.put("cfs_region", pb.getCfsRegion());
        m.put("cfs_env", pb.getCfsEnv());
        m.put("cfs_cluster", pb.getCfsCluster());
        m.put("filesystem_id", pb.getFilesystemId());
        m.put("namespace_id", pb.getNamespaceId());
        {
          SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
          fmt.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
          m.put("date", fmt.format(new Date(pb.getTsNanoseconds() / 1000 / 1000)));
        }
        m.put("ts_nanoseconds", pb.getTsNanoseconds());
        m.put("thread_id", 0);
        m.put("method", pb.getMethod().getNumber());
        m.put("user", pb.getUser());
        m.put("grp", pb.getGrp());
        m.put("ip_addr", pb.getIpAddr());
        m.put("is_successful", pb.getIsSuccessful());
        m.put("status", pb.getStatus());
        m.put("request", JsonFormat.printer().omittingInsignificantWhitespace().print(pb.getRequest()));
        m.put("response", JsonFormat.printer().omittingInsignificantWhitespace().print(pb.getResponse()));
        return new ObjectMapper().writeValueAsString(m);
      });
  }

  @Override
  public void onCommit(String lastCkptStr) {
    if (this.props.getBoolean(
      KafkaOffsetGen.Config.ENABLE_KAFKA_COMMIT_OFFSET.key(),
      KafkaOffsetGen.Config.ENABLE_KAFKA_COMMIT_OFFSET.defaultValue())) {
      offsetGen.commitOffsetToKafka(lastCkptStr);
    }
  }
}
