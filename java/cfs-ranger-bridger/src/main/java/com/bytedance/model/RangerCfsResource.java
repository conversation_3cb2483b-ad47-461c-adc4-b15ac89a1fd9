package com.bytedance.model;

import java.util.Objects;
import org.apache.ranger.plugin.policyengine.RangerAccessResourceImpl;

class RangerCfsResource extends RangerAccessResourceImpl {

	public static final String KEY_RESOURCE_PATH = "path";

	public RangerCfsResource(String path, String owner) {
		super.setValue(KEY_RESOURCE_PATH, path);
		super.setOwnerUser(owner);
	}

	@Override
	public String getAsString() {
		String ret = super.getStringifiedValue();

		if (ret == null) {
			ret = Objects.toString(super.getValue(KEY_RESOURCE_PATH));
			super.setStringifiedValue(ret);
		}
		return ret;
	}
}
