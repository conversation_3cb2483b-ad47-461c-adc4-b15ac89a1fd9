package com.bytedance.model;

import com.bytedance.CfsAuthorizer;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAccessRequestPB;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.hadoop.fs.permission.FsAction;
import com.volcengine.cloudfs.proto.AclProtos.AclEntryProto.FsActionProto;
import org.apache.ranger.plugin.policyengine.RangerAccessRequestImpl;

import org.apache.hadoop.hdfs.protocol.proto.INodeProtos.INode;

public class RangerCfsAccessRequest extends RangerAccessRequestImpl {

	public final static String FileNameExtensionSeparator = ".";

	public static List<RangerCfsAccessRequest> TransRangerCfsAccessReq(
			RangerAccessRequestPB rangerRequestPB) {
		FsActionProto fsAction = rangerRequestPB.getFsAction();
		FsAction access = FsAction.values()[fsAction.ordinal()];
		// Get fs Types.
		Set<String> accessTypes = CfsAuthorizer.access2ActionListMapper.get(access);
		List<RangerCfsAccessRequest> retList = new ArrayList<>(accessTypes.size());
		for (String accessType : accessTypes) {
			RangerCfsAccessRequest rangerCfsAccessRequest = new RangerCfsAccessRequest(
					rangerRequestPB.getPath(),
					rangerRequestPB.hasPathOwner() ? rangerRequestPB.getPathOwner() : null,
					access.toString(),
					accessType,
					rangerRequestPB.getUser(),
					rangerRequestPB.getRemoteIp());
			if (rangerRequestPB.hasFinalInode()) {
				rangerCfsAccessRequest.buildRequestContext(rangerRequestPB.getFinalInode());
			}
			retList.add(rangerCfsAccessRequest);
		}
		return retList;
	}

	private RangerCfsAccessRequest(String path,
																 String pathOwner,
																 String action,
																 String accessType,
																 String reqUser,
																 String remoteIp) {
		super.setResource(new RangerCfsResource(path, pathOwner));

		super.setAccessType(accessType);
		super.setUser(reqUser);
		super.setAccessTime(new Date());
		super.setClientIPAddress(remoteIp);
		super.setAction(action);
		super.setForwardedAddresses(null);
		super.setRemoteIPAddress(remoteIp);
	}

	public void buildRequestContext(final INode inode) {
		// TODO(wangning.ito): for further audit purpose.
	}
}
