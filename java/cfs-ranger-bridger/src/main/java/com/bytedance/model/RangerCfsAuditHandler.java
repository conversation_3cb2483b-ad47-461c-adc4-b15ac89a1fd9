package com.bytedance.model;

import com.volcengine.cloudfs.proto.RangerProtos.RangerAccessRequestPB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAuditRequestPB;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.ranger.audit.model.AuthzAuditEvent;
import org.apache.ranger.plugin.audit.RangerDefaultAuditHandler;
import org.apache.ranger.plugin.policyengine.RangerAccessResult;

@Slf4j
public class RangerCfsAuditHandler extends RangerDefaultAuditHandler {

  public void flushAudit(RangerAccessResult result,
                         RangerCfsAccessRequest request,
                         RangerAccessRequestPB rangerAccessRequestPB) {
    AuthzAuditEvent auditEvent    = super.getAuthzEvents(result);

    auditEvent.setEventTime(request.getAccessTime() != null ? request.getAccessTime() : new Date());
    auditEvent.setAccessType(request.getAction());

    auditEvent.setResourcePath(rangerAccessRequestPB.getPathToBeVerified());
    auditEvent.setResultReason(rangerAccessRequestPB.getPath());


    auditEvent.setAccessResult((short) (result.getIsAllowed() ? 1 : 0));
    auditEvent.setPolicyId(result.getPolicyId());
    auditEvent.setPolicyVersion(result.getPolicyVersion());

    auditEvent.setAction(request.getAccessType());
    auditEvent.setAdditionalInfo(getAdditionalInfo(request));

    super.logAuthzAudit(auditEvent);
  }
}