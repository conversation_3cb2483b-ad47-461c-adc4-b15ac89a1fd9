package com.bytedance.model;

import com.volcengine.cloudfs.proto.AclProtos.AclEntryProto;
import com.volcengine.cloudfs.proto.AclProtos.AclEntryProto.FsActionProto;

public class FsPermission {

  public FsPermission(int mode) {
    userAction = FsActionProto.valueOf((mode >> 6) & 7);
    groupAction = FsActionProto.valueOf((mode >> 6) & 7);
    otherAction = FsActionProto.valueOf((mode >> 6) & 7);
    stickyBit = ((mode >> 9) & 1) == 1;
  }

  public FsActionProto getUserAction() {
    return userAction;
  }

  public FsActionProto getGroupAction() {
    return groupAction;
  }

  public FsActionProto getOtherAction() {
    return otherAction;
  }

  public boolean isStickyBit() {
    return stickyBit;
  }

  private AclEntryProto.FsActionProto userAction;

  private AclEntryProto.FsActionProto groupAction;

  private AclEntryProto.FsActionProto otherAction;

  private boolean stickyBit;
}
