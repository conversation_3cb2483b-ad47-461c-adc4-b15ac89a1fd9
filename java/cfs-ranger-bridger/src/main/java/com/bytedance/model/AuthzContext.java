package com.bytedance.model;

import java.util.Set;
import java.util.TreeSet;
import org.apache.ranger.plugin.policyengine.RangerAccessResult;
import org.apache.ranger.plugin.service.RangerBasePlugin;

public class AuthzContext {
	public final String                 user;
	public final String                 operationName;
	public final boolean                isTraverseOnlyCheck;
	private      RangerAccessResult     lastResult   = null;

	public AuthzContext(String user, String group, String operationName, boolean isTraverseOnlyCheck) {
		this.user                = user;
		this.operationName       = operationName;
		this.isTraverseOnlyCheck = isTraverseOnlyCheck;
	}

	public void saveResult(RangerAccessResult result) {
		if (result != null) {
			this.lastResult = result;
		}
	}

	public RangerAccessResult getLastResult() {
		return lastResult;
	}
}