package com.bytedance.config;

import java.util.Properties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BridgerProperties {


  public static final String DEFAULT_SOCK_PATH = "/bridger/ranger.sock";

  public static BridgerProperties fromProperties(Properties properties) {
    String serviceType = properties.getProperty("service-type", "hdfs");
    String serviceName = properties.getProperty("service-name", "cfs");
    String appId = properties.getProperty("app-id", "cfs");
    String socketPath = properties.getProperty("bridger-socket", "127.0.0.1:9911");

    return BridgerProperties.builder()
        .serviceName(serviceName)
        .serviceType(serviceType)
        .appId(appId)
        .socketPath(socketPath)
        .build();
  }

  private String serviceType;

  private String serviceName;

  private String appId;

  private String socketPath;

}
