package com.bytedance.config;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Properties;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PropertyHandler {

  private final static String propertyFile = "/ranger-bridger.properties";

  @Getter
  private static BridgerProperties bridgerProperties;

  public static void init() throws IOException {
    Properties properties = new Properties();
    try {
      InputStream resourceAsStream = PropertyHandler.class.getResourceAsStream(propertyFile);
      properties.load(resourceAsStream);
    } catch (IOException e) {
      log.warn("Unable to read from property file, {}", propertyFile);
      throw e;
    }

    bridgerProperties = BridgerProperties.fromProperties(properties);
  }
}
