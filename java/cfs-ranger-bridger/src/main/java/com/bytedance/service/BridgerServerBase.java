package com.bytedance.service;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketException;
import java.util.concurrent.Future;
import lombok.extern.slf4j.Slf4j;
import org.newsclub.net.unix.AFUNIXSocket;
import org.newsclub.net.unix.server.AFUNIXSocketServer;

@Slf4j
abstract class BridgerServerBase extends AFUNIXSocketServer {

  public BridgerServerBase(SocketAddress listenAddress) {
    super(listenAddress);
  }

  private static String millisToHumanReadable(int millis, String zeroValue) {
    if (millis == 0 && zeroValue != null) {
      return "0 [ms] (" + zeroValue + ")";
    } else {
      float secs = millis / 1000f;
      if ((secs - (int) secs) == 0) {
        return millis + " [ms] == " + (int) (secs) + "s";
      } else {
        return millis + " [ms] == " + secs + "s";
      }
    }
  }

  @Override
  protected void onServerStarting() {
    log.info("Creating server: {}", getClass().getName());
    log.info("with the following configuration:");
    log.info("- maxConcurrentConnections: {}", getMaxConcurrentConnections());
    log.info("- serverTimeout: {}", millisToHumanReadable(getServerTimeout(), "none"));
    log.info("- socketTimeout: {}", millisToHumanReadable(getSocketTimeout(), "none"));
    log.info("- serverBusyTimeout: {}", millisToHumanReadable(getServerBusyTimeout(),
                                                              "none"));
  }

  @Override
  protected void onServerBound(SocketAddress address) {
    log.info("Created server -- bound to {}", address);
  }

  @Override
  protected void onServerBusy(long busySince) {
    log.info("Server is busy");
  }

  @Override
  protected void onServerReady(int activeCount) {
    log.info("Active connections: {} ; waiting for the next connection...", activeCount);
  }

  @Override
  protected void onServerStopped(ServerSocket theServerSocket) {
    log.info("Close server " + theServerSocket);
  }

  @Override
  protected void onSubmitted(Socket socket, Future<?> submit) {
    log.info("Accepted: " + socket);
  }

  @Override
  protected void onBeforeServingSocket(Socket socket) {
    log.info("Serving socket: " + socket);
    if (socket instanceof AFUNIXSocket) {
      try {
        log.info("Client's credentials: {}", ((AFUNIXSocket) socket).getPeerCredentials());
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
  }

  @Override
  protected void onServerShuttingDown() {
    log.info("Nothing going on for a long time, I better stop listening");
  }

  @Override
  protected void onSocketExceptionDuringAccept(SocketException e) {
    e.printStackTrace();
  }

  @Override
  protected void onSocketExceptionAfterAccept(Socket socket, SocketException e) {
    log.info("Closed (not executed): " + socket);
  }

  @Override
  protected void onServingException(Socket socket, Exception e) {
    if (socket.isClosed()) {
      // "Broken pipe", etc.
      log.info("The other end disconnected ({}): {}", e.getMessage(), socket);
      return;
    }
    log.error("Exception thrown in {}, connected: {}, {},{},{},{}", socket,
              socket.isConnected(), socket.isBound(), socket.isClosed(),
              socket.isInputShutdown(), socket.isOutputShutdown());
    e.printStackTrace();
  }

  @Override
  protected void onAfterServingSocket(Socket socket) {
    log.info("Closed: {}", socket);
  }

  @Override
  protected void onListenException(Exception e) {
    e.printStackTrace();
  }
}
