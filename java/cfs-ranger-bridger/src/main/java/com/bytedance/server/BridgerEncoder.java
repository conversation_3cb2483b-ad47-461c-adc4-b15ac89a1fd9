package com.bytedance.server;

import com.volcengine.cloudfs.proto.RangerProtos.RangerResponseListPB;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

public class BridgerEncoder extends MessageToByteEncoder<RangerResponseListPB> {

  @Override
  protected void encode(ChannelHandlerContext channelHandlerContext,
                        RangerResponseListPB rangerResponseListPB, ByteBuf byteBuf)
      throws Exception {
    byte[] bodyBytes = rangerResponseListPB.toByteArray();
    byteBuf.writeInt(bodyBytes.length);
    byteBuf.writeBytes(bodyBytes);
  }
}
