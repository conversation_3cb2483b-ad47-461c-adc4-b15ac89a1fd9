package com.bytedance.server;

import com.volcengine.cloudfs.proto.RangerProtos.RangerRequestListPB;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import java.nio.ByteOrder;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BridgerDecoder extends ByteToMessageDecoder {

  @Override
  protected void decode(ChannelHandlerContext channelHandlerContext,
                        ByteBuf byteBuf,
                        List<Object> list) throws Exception {

    if (byteBuf.readableBytes() < 4) {
      return;
    }

    byteBuf.markReaderIndex();

    int len = byteBuf.readInt();

    log.debug("Body size is {}", len);
    if (byteBuf.readableBytes() < len) {
      byteBuf.resetReaderIndex();
      return;
    }

    try {
      byte[] bodyBytes = new byte[len];
      byteBuf.readBytes(bodyBytes, 0, len);
      RangerRequestListPB rangerRequestListPB = RangerRequestListPB.parseFrom(bodyBytes);
      list.add(rangerRequestListPB);
    } catch (Exception e) {
      log.info("Unable to parse. ", e);
    }
  }
}
