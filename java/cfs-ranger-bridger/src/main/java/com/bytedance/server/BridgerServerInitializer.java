package com.bytedance.server;

import com.bytedance.CfsAuthorizer;
import com.bytedance.config.BridgerProperties;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import java.io.IOException;

public class BridgerServerInitializer extends ChannelInitializer<SocketChannel> {

  private final CfsAuthorizer cfsAuthorizer;

  public BridgerServerInitializer(BridgerProperties bridgerProperties) throws IOException {
    cfsAuthorizer = new CfsAuthorizer(bridgerProperties);
    cfsAuthorizer.start();
  }

  @Override
  protected void initChannel(SocketChannel socketChannel) throws Exception {

    ChannelPipeline pipeline = socketChannel.pipeline();

    pipeline.addLast(new BridgerDecoder());
    pipeline.addLast(new BridgerEncoder());
    pipeline.addLast(new BridgerHandler(cfsAuthorizer));
  }
}
