package com.bytedance.server;

import com.bytedance.config.BridgerProperties;
import com.bytedance.config.PropertyHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BridgerNettyServer {

  private final NioEventLoopGroup bossGroup;

  private final NioEventLoopGroup workerGroup;

  private final ServerBootstrap serverBootstrap;

  @Getter
  private int port;

  private BridgerProperties bridgerProperties;

  public BridgerNettyServer() {
    this.bossGroup = new NioEventLoopGroup(1);
    this.workerGroup = new NioEventLoopGroup();

    this.serverBootstrap = new ServerBootstrap();
  }

  public static void main(String[] args) {
    BridgerNettyServer nettyServer = new BridgerNettyServer();
    try {
      nettyServer.init();
      nettyServer.run();
    } catch (Exception e) {
      log.warn("Unable to start server in port: {}", nettyServer.getPort(), e);
      System.exit(-1);
    }
  }

  public void init() throws IOException {
    String propertyFile = "/ranger-bridger.properties";
    Properties properties = new Properties();
    try {
      InputStream resourceAsStream = PropertyHandler.class.getResourceAsStream(propertyFile);
      properties.load(resourceAsStream);
    } catch (Exception e) {
      log.warn("Unable to read from property file, {}. Choosing default properties.", propertyFile);
    }

    bridgerProperties = BridgerProperties.fromProperties(properties);

    String sockPath = bridgerProperties.getSocketPath();
    if (sockPath.contains(":")) {
      String[] splits = sockPath.split(":");
      if (splits.length != 2) {
        log.warn("Bad ranger socket path format: {}. et.c. 127.0.0.1:8080", sockPath);
        throw new IllegalArgumentException("Bad ranger socket path format: " + sockPath);
      }
      port = Integer.parseInt(splits[1]);
    }


    serverBootstrap.group(this.bossGroup, this.workerGroup);

    serverBootstrap.childHandler(new BridgerServerInitializer(bridgerProperties));

    serverBootstrap.channel(NioServerSocketChannel.class);
    serverBootstrap.handler(new LoggingHandler(LogLevel.INFO));
    //接收到的信息处理器
    serverBootstrap.option(ChannelOption.SO_BACKLOG,128);
    serverBootstrap.option(ChannelOption.TCP_NODELAY, true);
    serverBootstrap.option(ChannelOption.SO_REUSEADDR, true);
    serverBootstrap.childOption(ChannelOption.SO_KEEPALIVE, true);
  }

  public void run() {

    try {
      ChannelFuture cf = serverBootstrap.bind(port).sync();
      log.info("Started with port, {}", port);
      cf.channel().closeFuture().sync();
    } catch (Exception e) {
      log.info("Unable to start server at port: {}", port, e);
      throw new IllegalArgumentException(e);
    } finally {
      bossGroup.shutdownGracefully();
      workerGroup.shutdownGracefully();
    }
  }
}
