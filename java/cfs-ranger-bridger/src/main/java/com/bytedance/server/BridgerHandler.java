package com.bytedance.server;

import com.bytedance.CfsAuthorizer;
import com.bytedance.utils.ChannelUtils;
import com.volcengine.cloudfs.proto.RangerProtos.RangerRequestListPB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerResponseListPB;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BridgerHandler extends SimpleChannelInboundHandler<RangerRequestListPB>  {

  private final CfsAuthorizer cfsAuthorizer;

  public BridgerHandler(CfsAuthorizer cfsAuthorizer) {
    this.cfsAuthorizer = cfsAuthorizer;
  }

  @Override
  protected void channelRead0(ChannelHandlerContext ctx,
                              RangerRequestListPB rangerRequestListPB) throws Exception {
    log.debug("Started to reading...., {}", rangerRequestListPB.toString());

    RangerResponseListPB responseListPB;
    try {
      responseListPB = this.cfsAuthorizer.handleBridgerRequest(rangerRequestListPB);
    } catch (Exception e) {
      log.warn("Unable to handle, req: {}.", rangerRequestListPB);
      // Send a empty response, c++ port would consider this as refuse.
      responseListPB = RangerResponseListPB.newBuilder().build();
    }

    ChannelFuture f = ctx.writeAndFlush(responseListPB);
    f.addListener(ChannelFutureListener.CLOSE);
  }

}
