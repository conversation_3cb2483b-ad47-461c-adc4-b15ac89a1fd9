package com.bytedance;

import static org.apache.ranger.authorization.hadoop.constants.RangerHadoopConstants.EXECUTE_ACCCESS_TYPE;
import static org.apache.ranger.authorization.hadoop.constants.RangerHadoopConstants.READ_ACCCESS_TYPE;
import static org.apache.ranger.authorization.hadoop.constants.RangerHadoopConstants.WRITE_ACCCESS_TYPE;

import com.bytedance.config.BridgerProperties;
import com.bytedance.model.RangerCfsAccessRequest;
import com.bytedance.model.RangerCfsAuditHandler;
import com.google.common.collect.Sets;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAccessRequestPB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAccessResponsePB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAccessResponsePB.Builder;
import com.volcengine.cloudfs.proto.RangerProtos.RangerAuditResponsePB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerRequestListPB;
import com.volcengine.cloudfs.proto.RangerProtos.RangerResponseListPB;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.fs.permission.FsAction;
import org.apache.ranger.plugin.policyengine.RangerAccessResult;
import org.apache.ranger.plugin.service.RangerBasePlugin;

@Slf4j
public class CfsAuthorizer {


  public RangerBasePlugin rangerBasePlugin;

  public static final Map<FsAction, Set<String>> access2ActionListMapper = new HashMap<>();

  static {
    access2ActionListMapper.put(FsAction.NONE, new HashSet<>());
    access2ActionListMapper.put(FsAction.ALL, Sets.newHashSet(READ_ACCCESS_TYPE, WRITE_ACCCESS_TYPE,
                                                              EXECUTE_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.READ, Sets.newHashSet(READ_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.READ_WRITE,
                                Sets.newHashSet(READ_ACCCESS_TYPE, WRITE_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.READ_EXECUTE,
                                Sets.newHashSet(READ_ACCCESS_TYPE, EXECUTE_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.WRITE, Sets.newHashSet(WRITE_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.WRITE_EXECUTE,
                                Sets.newHashSet(WRITE_ACCCESS_TYPE, EXECUTE_ACCCESS_TYPE));
    access2ActionListMapper.put(FsAction.EXECUTE, Sets.newHashSet(EXECUTE_ACCCESS_TYPE));
  }

  public final static String WILDCARD = "*";

  private enum AuthzStatus {ALLOW, DENY, NOT_DETERMINED}

  private BridgerProperties bridgerProperties;

  private static CfsAuthorizer cfsAuthorizer;

  public CfsAuthorizer(BridgerProperties bridgerProperties) {
    this.bridgerProperties = bridgerProperties;
  }

  public void start() {
    log.info("Starting ranger plugin. Necessary info: {}.", bridgerProperties);

    // Note: Consider change appid to a customizable conf?
    rangerBasePlugin = new RangerBasePlugin(bridgerProperties.getServiceType(),
                                            bridgerProperties.getServiceName(),
                                            bridgerProperties.getAppId());
    rangerBasePlugin.init();
    log.info("cluster name is {} ", rangerBasePlugin.getClusterName());
    // FIXME: we may need more information. And try to ping the ranger server.
  }

  public RangerResponseListPB handleBridgerRequest(RangerRequestListPB rangerRequestListPB) {
    RangerResponseListPB.Builder builder = RangerResponseListPB.newBuilder();
      RangerAccessResponsePB accessAllowed = isAccessAllowed(rangerRequestListPB.getAccessRequest(),
                                                             rangerRequestListPB.hasFlushAuditIfAllow() ? rangerRequestListPB.getFlushAuditIfAllow() : false);
      builder.setAccessResponse(accessAllowed);
    return builder.build();
  }

  private static void setResponseFromResult(RangerAccessResponsePB.Builder builder,
                                            RangerAccessResult result) {

    builder.setPolicyType(result.getPolicyType());
    builder.setIsAccessDetermined(result.getIsAccessDetermined());
    builder.setIsAllowed(result.getIsAllowed());
    builder.setPolicyId(result.getPolicyId());
    builder.setPolicyPriority(result.getPolicyPriority());
    if (result.getZoneName() != null) {
      builder.setZoneName(result.getZoneName());
    }
    if (result.getPolicyVersion() != null) {
      builder.setPolicyVersion(result.getPolicyVersion());
    }
    builder.setEvaluatedPoliciesCount(result.getEvaluatedPoliciesCount());
    if (result.getReason() != null) {
      builder.setReason(result.getReason());
    }
  }

  public RangerAccessResponsePB isAccessAllowed(RangerAccessRequestPB rangerAccessRequestPB, boolean auditIfAllow) {
    List<RangerCfsAccessRequest> rangerCfsAccessRequests = RangerCfsAccessRequest.TransRangerCfsAccessReq(
        rangerAccessRequestPB);
    Builder builder = RangerAccessResponsePB.newBuilder();
    boolean explicitAllow = false;
    boolean explicitDeny = false;
    RangerAccessResult lastResult = null;
    RangerCfsAccessRequest lastRequest = null;

    for (RangerCfsAccessRequest request : rangerCfsAccessRequests) {
      RangerAccessResult result = rangerBasePlugin.isAccessAllowed(request, null);
      lastResult = result;
      lastRequest = request;

      setResponseFromResult(builder, result);
      if (!result.getIsAllowed()) { // explicit deny
        explicitDeny = true;
        break;
      }
      explicitAllow = true;
    }

    if ((explicitAllow && auditIfAllow) || explicitDeny) {
      flushAudit(lastResult, lastRequest, rangerAccessRequestPB);
    }

    return builder.build();
  }

  public RangerAuditResponsePB flushAudit(RangerAccessResult result,
                                          RangerCfsAccessRequest cfsAccessRequest,
                                          RangerAccessRequestPB rangerAccessRequestPB) {
    RangerCfsAuditHandler rangerCfsAuditHandler = new RangerCfsAuditHandler();
//    rangerCfsAuditHandler.flushAudit(rangerAuditRequestPB);
    RangerAuditResponsePB.Builder builder = RangerAuditResponsePB.newBuilder();

    rangerCfsAuditHandler.flushAudit(result, cfsAccessRequest, rangerAccessRequestPB);

    builder.setRetryCnt(1);
    builder.setSuccess(true);

    return builder.build();
  }

}
