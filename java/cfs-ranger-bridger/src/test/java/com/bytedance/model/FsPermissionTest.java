package com.bytedance.model;

import junit.framework.Assert;
import com.volcengine.cloudfs.proto.AclProtos.AclEntryProto.FsActionProto;
import org.junit.jupiter.api.Test;

public class FsPermissionTest {
  @Test
  public void testWrite() {
    FsPermission fsPermission = new FsPermission(0777);

    Assert.assertEquals(fsPermission.getUserAction(), FsActionProto.PERM_ALL);
    Assert.assertEquals(fsPermission.getGroupAction(), FsActionProto.PERM_ALL);
    Assert.assertEquals(fsPermission.getOtherAction(), FsActionProto.PERM_ALL);
  }
}
