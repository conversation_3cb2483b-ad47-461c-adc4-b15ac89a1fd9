// Copyright 2020 Mu <PERSON> <<EMAIL>>

#include "http/refresh_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/uri.h>
#include <gflags/gflags.h>

#include <utility>
#include <string>
#include <vector>

#include "base/string_utils.h"
#include "base/trusted_ip_table.h"

namespace dancenn {

RefreshHandler::RefreshHandler() {
  // Init
  std::string result;
  RefreshTrustedIpTable(&result);
}

cnetpp::http::HttpResponse RefreshHandler::Handle(
    const cnetpp::http::HttpRequest& request) {

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    cnetpp::http::HttpResponse response;
    response.set_status(HttpStatusCode::kBadRequest);
    return response;
  }

  std::string result;
  auto status_code = HttpStatusCode::kOk;
  if (request.uri().find("/refresh/trusted_ip") == 0) {
    status_code = RefreshTrustedIpTable(&result);
  } else {
    cnetpp::http::HttpResponse response;
    response.set_status(HttpStatusCode::kNotFound);
    return response;
  }

  cnetpp::http::HttpResponse response;
  response.set_status(status_code);
  response.SetHttpHeader("Content-Length", std::to_string(result.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(result);
  return response;
}

HttpStatusCode RefreshHandler::RefreshTrustedIpTable(std::string* result) {
  auto rtn = security::UpdatedTrustedIp();
  *result = security::ListTrustedIpJsonString();
  if (rtn) {
    return HttpStatusCode::kOk;
  } else {
    return HttpStatusCode::kInternalServerError;
  }
}

}  // namespace dancenn
