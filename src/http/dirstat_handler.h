//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <string>
#include <utility>

// Third
#include "cnetpp/http/http_request.h"
#include "cnetpp/http/http_response.h"

// Project
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"

namespace dancenn {

class NameSpace;

class DirStatHandler : public HttpHandler {
public:
  explicit DirStatHandler(std::shared_ptr<NameSpace> ns) : ns_(std::move(ns)) {}
  ~DirStatHandler() override = default;

  const std::string& name() const override { return name_; }

  cnetpp::http::HttpResponse
  Handle(const cnetpp::http::HttpRequest& request) override;

private:
  cnetpp::http::HttpResponse HandleQueryRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleStartScrubRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleStopScrubRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleGetScrubRequest(const ParamMap& params);

private:
  const std::string name_{"/dirstat"};
  std::shared_ptr<NameSpace> ns_{nullptr};
};

} // namespace dancenn
