// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/06
// Description

#include "http/quota_handler.h"

#include <absl/strings/str_format.h>
#include <glog/logging.h>

#include <cstdint>

#include "base/closure.h"
#include "base/logger_metrics.h"
#include "base/metrics.h"
#include "base/status.h"
#include "base/string_utils.h"
#include "edit/sender_base.h"
#include "proto/generated/cloudfs/xattr.pb.h"
#include "proto/generated/dancenn/inode.pb.h"

namespace dancenn {

QuotaHandler::QuotaHandler(std::shared_ptr<NameSpace> ns)
    : ns_(ns), name_("/quota") {
}

const std::string& QuotaHandler::name() const {
  return name_;
}

cnetpp::http::HttpResponse QuotaHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  std::string src;
  QuotaPolicyProto quota_policy;
  auto s = Parse(request, &src, &quota_policy);
  if (!s.Is<PERSON>()) {
    cnetpp::http::HttpResponse response;
    const std::string& body = s.message();
    response.set_status(HttpStatusCode::kBadRequest);
    response.SetHttpHeader("Content-Length", std::to_string(body.size()));
    response.SetHttpHeader("Content-Type", "application/text");
    response.set_http_body(body);
    return response;
  }

  cloudfs::XAttrProto xattr;
  xattr.set_namespace_(cloudfs::XAttrProto::SYSTEM);
  xattr.set_name("hdfs.quota.policy");
  CHECK(quota_policy.IsInitialized())
      << quota_policy.InitializationErrorString();
  xattr.set_value(quota_policy.SerializeAsString());
  CHECK(xattr.IsInitialized()) << xattr.InitializationErrorString();
  SynchronizedRpcClosure done;
  // TODO(ruanjunbin): Set ugi?
  ns_->AsyncSetXAttr(src,
                     xattr,
                     /*create_if_not_existed*/ true,
                     /*replace_if_existed*/ true,
                     LogRpcInfo(),
                     &done);
  done.Await();

  cnetpp::http::HttpResponse response;
  response.set_status(done.status().IsOK() ? HttpStatusCode::kOk : HttpStatusCode::kInternalServerError);
  std::string msg = done.status().ToString();
  response.SetHttpHeader("Content-Length", std::to_string(msg.size()));
  response.SetHttpHeader("Content-Type", "application/text");
  response.set_http_body(msg);
  return response;
}

Status QuotaHandler::Parse(const cnetpp::http::HttpRequest& request,
                           std::string* src,
                           QuotaPolicyProto* quota_policy) {
  CHECK_NOTNULL(src);
  CHECK_NOTNULL(quota_policy);

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    LOG(WARNING) << "Parse uri failed: " << request.uri();
    return Status(Code::kFalse,
                  absl::StrFormat("Invalid uri %s", request.uri()));
  }

  auto params = uri.QueryParams();
  for (const auto& param : params) {
    if (param.first == "path") {
      *src = param.second;
    } else if (param.first == "inode_limit") {
      int64_t inode_limit = 0;
      if (!StringUtils::StringToInt64(
              param.second.c_str(), param.second.size(), &inode_limit)) {
        return Status(Code::kFalse,
                      absl::StrFormat("Invalid inode_limit %s", param.second));
      }
      if (inode_limit >= 0) {
        quota_policy->set_inode_limit(inode_limit);
      }
    } else if (param.first == "data_size_limit") {
      int64_t data_size_limit = 0;
      if (!StringUtils::StringToInt64(
              param.second.c_str(), param.second.size(), &data_size_limit)) {
        return Status(
            Code::kFalse,
            absl::StrFormat("Invalid data_size_limit %s", param.second));
      }
      if (data_size_limit >= 0) {
        quota_policy->set_data_size_limit(data_size_limit);
      }
    } else {
      return Status(Code::kFalse,
                    absl::StrFormat("Invalid param: ", param.first));
    }
  }
  return Status();
}

}  // namespace dancenn
