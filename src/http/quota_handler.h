// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/06
// Description

#ifndef HTTP_QUOTA_HANDLER_H_
#define HTTP_QUOTA_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <memory>
#include <string>

#include "http/http_handler.h"
#include "namespace/namespace.h"

namespace dancenn {

class QuotaHandler : public HttpHandler {
 public:
  explicit QuotaHandler(std::shared_ptr<NameSpace> ns);

  const std::string& name() const override;
  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  Status Parse(const cnetpp::http::HttpRequest& request,
               std::string* src,
               QuotaPolicyProto* quota_policy);

 private:
  const std::string name_;
  std::shared_ptr<NameSpace> ns_;
};

}  // namespace dancenn

#endif  // HTTP_QUOTA_HANDLER_H_
