// Copyright 2024 Chengxu <<EMAIL>>

#include "http/prometheus_handler.h"

#include <cnetpp/base/csonpp.h>
#include <gflags/gflags.h>

#include <utility>
#include <string>
#include <algorithm>
#include <sstream>
#include <vector>
#include <iostream>
#include <iomanip>
#include <cstdlib>

#include "base/metrics.h"

DECLARE_int32(http_port);
DECLARE_string(metrics_prefix);
DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);

namespace dancenn {

std::vector<std::string> SplitString(const std::string& input, char delimiter) {
  std::vector<std::string> result;
  std::stringstream ss(input);
  std::string item;
  while (std::getline(ss, item, delimiter)) {
    result.push_back(item);
  }
  return result;
}

PrometheusHandler::PrometheusHandler() {
  std::stringstream ss;

  const char* host_cstr = std::getenv("MY_HOST_IP");
  if (host_cstr != nullptr) {
    ss << "host=\"" << host_cstr << "\",";
  } else {
    LOG(WARNING) << "env MY_HOST_IP unset, unknown host.";
    ss << "host=\"UNKNOWN_HOST\",";
  }

  ss << "port=\"" << FLAGS_http_port << "\","
     << "cfs_region=\"" << FLAGS_cfs_region << "\","
     << "cfs_env=\"" << FLAGS_cfs_env << "\","
     << "cfs_cluster=\"" << FLAGS_cfs_cluster << "\"";

  tag_prefix_ = ss.str();

  metrics_prefix_ = FLAGS_metrics_prefix;
  std::replace(metrics_prefix_.begin(), metrics_prefix_.end(), '.', '_');
}

void PrometheusHandler::ParseName(const std::string& key, std::string* name, std::string* tag) {
  std::stringstream ss;
  ss << tag_prefix_;
  
  auto segs = SplitString(key, '#');
  std::replace(segs[0].begin(), segs[0].end(), '.', '_');
  *name = segs[0];
  if (segs.size() == 1) {
    *tag = ss.str();
    return;
  }
  for (int i = 1; i < segs.size(); ++i) {
    auto tagkv = SplitString(segs[i], '=');
    if (tagkv.size() != 2) {
        LOG(WARNING) << "Invalid tagkv = " << segs[i];
        continue;
    }
    std::string tk = tagkv[0];
    std::string tv = tagkv[1];
    std::replace(tk.begin(), tk.end(), '.', '_');

    if (segs[0] == "WhiteListHitCount" && tk == "team") {
      std::stringstream ss;
      for (char c : tv) {
        ss << std::hex << std::setw(2) << std::setfill('0') << std::uppercase << static_cast<int>(c);
      }
      tv = ss.str();
    }
    
    // fill tag
    ss << "," << tk << "=\"" << tv << "\"";
  }
  *tag = ss.str();
}

cnetpp::http::HttpResponse PrometheusHandler::Handle(
      const cnetpp::http::HttpRequest& request) {
  (void) request;
  cnetpp::base::Object results;
  MetricsCenter::Instance()->ToJson(&results);
  cnetpp::base::Parser parser;
  std::stringstream ss;

  for (auto it = results.Begin(); it != results.End(); ++it) {
    std::string center = it->first;
    std::replace(center.begin(), center.end(), '.', '_');

    if (!it->second.IsObject()) {
      LOG(WARNING) << "Invalid ms type = " << static_cast<int>(it->second.Type());
      continue;
    }
    const auto& ms = it->second.AsObject();

    for (auto ms_it = ms.Begin(); ms_it != ms.End(); ++ms_it) {
      const auto& type = ms_it->first;
      
      if (!ms_it->second.IsObject()) {
        LOG(WARNING) << "Invalid map type = " << static_cast<int>(ms_it->second.Type());
        continue;
      }
      const auto& map = ms_it->second.AsObject();

      if (type == "counters") {
        for (auto map_it = map.Begin(); map_it != map.End(); ++map_it) {
          const auto& key = map_it->first;
          std::string value;
          parser.Serialize(map_it->second, &value);

          std::string name, tag;
          ParseName(key, &name, &tag);
          ss << "# TYPE " << metrics_prefix_ << "_" << center << "_store_" << name << " gauge\n";
          ss << metrics_prefix_ << "_" << center << "_store_" << name << "{" << tag << "} " << value << "\n"; 
        }
      } else if (type == "gauges") {
        for (auto map_it = map.Begin(); map_it != map.End(); ++map_it) {
          const auto& key = map_it->first;
          std::string value;
          parser.Serialize(map_it->second, &value);

          std::string name, tag;
          ParseName(key, &name, &tag);
          ss << "# TYPE " << metrics_prefix_ << "_" << center << "_store_" << name << " gauge\n";
          ss << metrics_prefix_ << "_" << center << "_store_" << name << "{" << tag << "} " << value << "\n"; 
        }
      } else if (type == "histograms") {
        for (auto map_it = map.Begin(); map_it != map.End(); ++map_it) {
          const auto& key = map_it->first;
          if (!map_it->second.IsObject()) {
            LOG(WARNING) << "Invalid quantile type = " << static_cast<int>(map_it->second.Type()) << " key = " << key;
            continue;
          }
          const auto& quantile_map = map_it->second.AsObject();

          std::string name, tag;
          ParseName(key, &name, &tag);
          for (auto quantile_it = quantile_map.Begin(); quantile_it != quantile_map.End(); ++quantile_it) {
            const auto& quantile = quantile_it->first;
            std::string value;
            parser.Serialize(quantile_it->second, &value);
            ss << "# TYPE " << metrics_prefix_ << "_" << center << "_histograms_" << name << "_" << quantile << " gauge\n";
            ss << metrics_prefix_ << "_" << center << "_histograms_" << name << "_" << quantile << "{" << tag << "} " << value << "\n"; 
          }          
        }
      } else {
        LOG(WARNING) << "unknown type = " << type;
      }
    }
  }
  std::string res = ss.str();
  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(res.size()));
  response.SetHttpHeader("Content-Type", "text/html");
  response.set_http_body(std::move(res));
  return response;
}

}  // namespace dancenn
