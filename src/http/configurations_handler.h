// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_CONFIGURATIONS_HANDLER_H_
#define HTTP_CONFIGURATIONS_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>
#include <unordered_map>

#include "http/http_handler.h"

namespace dancenn {

class ConfigurationsHandler : public HttpHandler {
 public:
  ConfigurationsHandler() {
  }
  ~ConfigurationsHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  HttpStatusCode InternalHandleUpdateConfig(const ParamMap& params,
                                            std::string* result);
  HttpStatusCode InternalHandleForceUpdateConfig(const ParamMap& params,
                                                 std::string* result);
  HttpStatusCode InternalHandleUpdateLogLevel(const ParamMap& params,
                                              std::string* result);

 private:
  const std::string name_{"/config"};

 private:
  friend class ConfigUpdateHandlerTest;
};

}  // namespace dancenn

#endif  // HTTP_CONFIGURATIONS_HANDLER_H_
