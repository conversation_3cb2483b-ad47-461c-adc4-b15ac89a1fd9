// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/http_handler.h"

#include <string>

#include "base/string_utils.h"

namespace dancenn {

cnetpp::http::HttpResponse HttpHandler::NotFound(const std::string& uri) {
  std::string not_found =
      "<!DOCTYPE html>"
      "<html lang=\"en\">"
      "<head>"
      "<title>404 Not Found</title>"
      "<style type=\"text/css\">"
      "body {"
      "\tcolor: #222;"
      "\tbackground-color: #fff;"
      "\tfont-family: sans-serif;"
      "\tline-height: 1.5em;"
      "\t}"
      "</style>"
      "</head>"
      "<body>"
      "<h1>404 Not Found</h1>"
      "<p>"
      "The requested URL (" +
      uri +
      ")"
      "was not found on this server."
      "</p>"
      "</body>"
      "</html>";

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kNotFound);
  response.SetHttpHeader("Content-Length", std::to_string(not_found.size()));
  response.set_http_body(not_found);
  return response;
}

bool HttpHandler::GetBoolFromParams(const ParamMap& params,
                                    const std::string& name, bool* value) {
  auto itr = params.find(name);
  if (itr != params.end()) {
    auto second = itr->second;
    *value = second == "1" || second == "true";
    return true;
  }

  return false;
}

bool HttpHandler::GetInt64FromParams(const ParamMap& params,
                                     const std::string& name, int64_t* value) {
  auto itr = params.find(name);
  if (itr != params.end()) {
    auto second = itr->second;
    return StringUtils::StringToInt64(second.c_str(), second.size(), value);
  }
  return false;
}

bool HttpHandler::GetUInt32FromParams(const ParamMap& params,
                                      const std::string& name,
                                      uint32_t* value) {
  int64_t ret = 0;
  if (!GetInt64FromParams(params, name, &ret)) return false;
  if (ret < 0 || ret > UINT32_MAX) return false;
  *value = (uint32_t)ret;
  return true;
}

bool HttpHandler::GetStringFromParams(const ParamMap& params,
                                      const std::string& name,
                                      std::string* value) {
  auto itr = params.find(name);
  if (itr != params.end()) {
    *value = itr->second;
    return true;
  }

  return false;
}

bool HttpHandler::GetInt64FromParams(const ParamMap& params,
                                     const std::string& name,
                                     int64_t default_value, int64_t* value) {
  auto itr = params.find(name);
  if (itr != params.end()) {
    auto second = itr->second;
    return StringUtils::StringToInt64(second.c_str(), second.size(), value);
  }

  *value = default_value;
  return true;
}

bool HttpHandler::GetUInt32FromParams(const ParamMap& params,
                                      const std::string& name,
                                      uint32_t default_value, uint32_t* value) {
  int64_t ret = 0;
  if (!GetInt64FromParams(params, name, default_value, &ret)) return false;
  if (ret < 0 || ret > UINT32_MAX) return false;
  *value = (uint32_t)ret;
  return true;
}

}  // namespace dancenn
