//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "dancenn_job_handler.h"

#include <absl/strings/str_format.h>
#include <resolv.h>

#include <cstdint>
#include <nlohmann/json.hpp>

#include "base/status.h"
#include "base/string_utils.h"
#include "block_manager/block.h"
#include "cnetpp/base/csonpp.h"
#include "cnetpp/base/uri.h"
#include "job_manager/managed_job_state.h"
#include "job_manager/workflow.h"

namespace dancenn {

using HttpResponse = cnetpp::http::HttpResponse;
using MethodType = cnetpp::http::HttpRequest::MethodType;

cnetpp::http::HttpResponse JobManagerHandler::ReturnError(
    HttpStatusCode code,
    const std::string& error_msg) {
  if (error_msg.empty()) {
    HttpResponse response;
    response.set_status(code);
    return response;
  }

  cnetpp::base::Object results;
  results["message"] = error_msg;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  HttpResponse response;
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse JobManagerHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return ReturnError(HttpStatusCode::kBadRequest);
  }
  auto params = uri.QueryParams();
  for (auto p : params) {
    LOG(INFO) << "Query param: " << p.first << " = " << p.second;
  }
  ParamMap param_map(params.begin(), params.end());

  std::string cmd = "query";
  GetStringFromParams(param_map, "cmd", &cmd);

  if (cmd == "getjob") {
    return HandleLookupJobRequest(param_map);
  }
  if (cmd == "canceljob") {
    return HandleCancelJobRequest(param_map);
  }
  if (cmd == "listjob") {
    return HandleListJobRequest(param_map);
  }

  return ReturnError(HttpStatusCode::kBadRequest,
                     absl::StrFormat("Invalid cmd: %s", cmd));
}

cnetpp::http::HttpResponse JobManagerHandler::HandleLookupJobRequest(
    const ParamMap& params) {
  std::string job_id;
  if (!GetStringFromParams(params, "id", &job_id)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'job_id'"));
  }

  ManagedJobState job_state;
  auto status = job_manager_->LookupJobState(job_id, job_state);
  if (status.IsOK()) {
    cnetpp::base::Object result;
    result["id"] = job_state.GetJobId();
    result["type"] = ManagedJobTypeInfo::GetName(job_state.GetJobType());
    result["state"] = WorkflowStateInfo::GetName(job_state.GetWorkflowState());
    result["create_time"] = job_state.GetCreateTime();
    result["complete_time"] = job_state.GetCompleteTime();
    result["total_tasks"] = job_state.GetTotalTaskNum();
    result["success_tasks"] = job_state.GetSuccessTaskNum();
    result["failed_tasks"] = job_state.GetFailedTaskNum();
    result["canceled_tasks"] = job_state.GetCanceledTaskNum();
    result["timeout_tasks"] = job_state.GetTimeoutTaskNum();
    result["throttled_tasks"] = job_state.GetThrottledTaskNum();

    std::string str_result;
    cnetpp::base::Parser parser;
    parser.Serialize(cnetpp::base::Value(std::move(result)), &str_result);

    cnetpp::http::HttpResponse response;
    response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
    response.SetHttpHeader("Content-Length", std::to_string(str_result.size()));
    response.SetHttpHeader("Content-Type", "application/json");
    response.set_http_body(str_result);
    return response;
  } else if (status.code() == Code::kJobNotFound) {
    return ReturnError(HttpStatusCode::kNotFound,
                       absl::StrFormat("Job %s not found", job_id));
  } else {
    return ReturnError(
        HttpStatusCode::kUnknown,
        absl::StrFormat("Failed to lookup job %s", status.message()));
  }
}

cnetpp::http::HttpResponse JobManagerHandler::HandleCancelJobRequest(
    const ParamMap& params) {
  std::string job_id;
  if (!GetStringFromParams(params, "id", &job_id)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'job_id'"));
  }

  auto status = job_manager_->CancelJob(job_id);
  if (status.IsOK()) {
    return ReturnError(HttpStatusCode::kOk, "Done");
  } else if (status.code() == Code::kJobNotFound) {
    return ReturnError(HttpStatusCode::kNotFound,
                       absl::StrFormat("Job %s not found", job_id));
  } else {
    return ReturnError(
        HttpStatusCode::kUnknown,
        absl::StrFormat("Failed to cancel job %s", status.message()));
  }
}

cnetpp::http::HttpResponse JobManagerHandler::HandleListJobRequest(
    const ParamMap& params) {
  std::string job_type_string;
  GetStringFromParams(params, "type", &job_type_string);

  std::vector<std::string> job_ids;
  ManagedJobType job_type = ManagedJobType::UNKNOWN;
  if (job_type_string == "load_data") {
    job_type = ManagedJobType::LOAD_DATA;
  } else if (job_type_string == "load_metadata") {
    job_type = ManagedJobType::LOAD_METADATA;
  } else if (job_type_string == "free") {
    job_type = ManagedJobType::FREE_DATA;
  } else if (job_type_string == "reconcile_inode_attr") {
    job_type = ManagedJobType::RECONCILE_INODE_ATTRS;
  }

  Status status = job_manager_->ListJob(job_ids, job_type);
  if (!status.IsOK()) {
    return ReturnError(
        HttpStatusCode::kForbidden,
        absl::StrFormat("Failed to list job %s", status.message()));
  }

  std::stringstream ss;
  for (int i = 0; i < job_ids.size(); i++) {
    ss << job_ids.at(i);
    if (i != job_ids.size() - 1) {
      ss << ",";
    }
  }
  ss << "\n";

  std::string && str_result = ss.str();
  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_result.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_result);
  return response;
}

}  // namespace dancenn