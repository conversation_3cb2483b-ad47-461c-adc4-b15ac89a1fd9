//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <string>
#include <utility>

// Third
#include "cnetpp/http/http_request.h"
#include "cnetpp/http/http_response.h"

// Project
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"

namespace dancenn {

class NameSpace;

class LifecycleHandler : public HttpHandler {
public:
  explicit LifecycleHandler(std::shared_ptr<NameSpace> ns)
    : ns_(std::move(ns)) {}
  ~LifecycleHandler() override = default;

  const std::string& name() const override { return name_; }

  cnetpp::http::HttpResponse
  Handle(const cnetpp::http::HttpRequest& request) override;

private:
  cnetpp::http::HttpResponse ReturnError(HttpStatusCode code, const std::string& error_msg = "");
  cnetpp::http::HttpResponse HandleSetRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleUnsetRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleGetRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleStatRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleCheckRequest(const ParamMap& params);

private:
  const std::string name_{"/lifecycle"};
  std::shared_ptr<NameSpace> ns_{nullptr};
};

} // namespace dancenn
