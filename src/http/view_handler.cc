// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/view_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/memory_cache.h>
#include <cnetpp/base/uri.h>
#include <cnetpp/tcp/ring_buffer.h>
#include <gflags/gflags.h>
#include <jemalloc/jemalloc.h>

#include <nlohmann/json.hpp>
#include <string>
#include <utility>
#include <vector>

#include "base/path_util.h"
#include "base/pb_converter.h"
#include "base/rack_aware.h"
#include "base/string_utils.h"
#include "base/trusted_ip_table.h"
#include "block_manager/block_manager.h"
#include "namespace/namespace.h"

DECLARE_int32(view_lease_limit_when_no_condition);
DECLARE_bool(allow_list_many_lease);

namespace dancenn {

ViewHandler::ViewHandler(std::shared_ptr<NameSpace> ns,
                         std::shared_ptr<BlockManager> bm)
    : ns_(std::move(ns)), bm_(std::move(bm)) {
  table_ = {
      {"/view/lease", &ViewHandler::GetLease},
      {"/view/corrupt_files", &ViewHandler::GetCorruptFilesInfo},
      {"/view/corrupt_blocks", &ViewHandler::GetCorruptBlocksInfo},
      {"/view/sealed_blocks", &ViewHandler::GetSealedBlocksInfo},
      {"/view/truncatable_blocks", &ViewHandler::GetTruncatableBlocksInfo},
      {"/view/dn_invalidate_cmd", &ViewHandler::GetDnInvalidateCmd},
      {"/view/dn_truncatable_cmd", &ViewHandler::GetDnTruncatableCmd},
      {"/view/memory_fragment", &ViewHandler::GetMemoryFragmentation},
      {"/view/net_buf_cache", &ViewHandler::GetNetBufferCache},
      {"/view/net_buf", &ViewHandler::GetNetBufferMemory},
      {"/view/block_index", &ViewHandler::GetBlockIndexStats},
      {"/view/block_detail", &ViewHandler::GetBlockIndexDetail},
      {"/view/parent_map", &ViewHandler::GetParentMapStats},
      {"/view/replica_policy", &ViewHandler::GetReplicaPolicy},
      {"/view/read_policy", &ViewHandler::GetReadPolicy},
      {"/view/recycle_policy", &ViewHandler::GetRecyclePolicy},
      {"/view/trusted_ip", &ViewHandler::GetTrustedIp},
      {"/view/retry_cache", &ViewHandler::GetRetryCache},
      {"/view/ongoing_upload_task", &ViewHandler::GetOngoingUploadTask},
  };
}

cnetpp::http::HttpResponse ViewHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    cnetpp::http::HttpResponse response;
    response.set_status(HttpStatusCode::kBadRequest);
    return response;
  }

  auto path = uri.Path();
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());
  auto status_code = HttpStatusCode::kOk;
  std::ostringstream os;

  auto iter = table_.find(path);
  if (iter != table_.end()) {
    auto func = iter->second;
    status_code = (this->*func)(param_map, os);
  } else {
    cnetpp::http::HttpResponse response;
    response.set_status(HttpStatusCode::kNotFound);
    return response;
  }

  auto result = os.str();
  cnetpp::http::HttpResponse response;
  response.set_status(status_code);
  response.SetHttpHeader("Content-Length", std::to_string(result.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(result);
  return response;
}
HttpStatusCode ViewHandler::GetLease(const ParamMap& params,
                                     std::ostringstream& os) {
  nlohmann::json json = {{"result", ns_->GetActiveClients()}};
  os << json.dump();
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetCorruptFilesInfo(const ParamMap& params,
                                                std::ostringstream& os) {
  uint32_t offset = 0;
  uint32_t limit = 0;
  uint32_t total = 0;
  if (!GetUInt32FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetUInt32FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (limit <= 0) {
    limit = 100;
  }

  os << "{ \"offset\": " << std::to_string(offset);
  os << ", \"limit\": " << std::to_string(limit);
  os << ", \"result\": [";
  bm_->GetCorruptFilesInfo(offset, limit, &total, os);
  os << "]";
  os << ", \"total\": " << std::to_string(total);
  os << "}";
  LOG(INFO) << "GetCorruptFilesInfo, result: " << os.str();
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetCorruptBlocksInfo(const ParamMap& params,
                                                 std::ostringstream& os) {
  int64_t offset = 0;
  int64_t limit = 0;
  if (!GetInt64FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetInt64FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }

  size_t total = 0;
  std::deque<BlockID> block_ids;
  std::tie(total, block_ids) = bm_->GetCorruptBlockIDs(offset, limit);
  os << "{\"corrupts\": [\n";
  for (int64_t i = 0; i < ((int64_t)block_ids.size()); i++) {
    os << "\"";
    bm_->DumpBlockSimpleInfo(block_ids[i], os);
    os << "\"";
    if (i != block_ids.size() - 1) {
      os << ",";
    }
  }
  os << "]";
  os << ",\"total\":" << total;
  os << "}";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetSealedBlocksInfo(const ParamMap& params,
                                                std::ostringstream& os) {
  int64_t offset = 0;
  int64_t limit = 0;
  if (!GetInt64FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetInt64FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }

  size_t total = 0;
  std::deque<BlockID> block_ids;
  std::tie(total, block_ids) = bm_->GetSealedBlockIDs(offset, limit);
  os << "{\"sealed\": [";
  for (int64_t i = 0; i < ((int64_t)block_ids.size()); i++) {
    os << "\"";
    bm_->DumpBlockSimpleInfo(block_ids[i], os);
    os << "\"";
    if (i != block_ids.size() - 1) {
      os << ",";
    }
  }
  os << "]";
  os << ",\"total\":" << total;
  os << "}";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetTruncatableBlocksInfo(const ParamMap& params,
                                                     std::ostringstream& os) {
  int64_t offset = 0;
  int64_t limit = 0;
  std::string dn_ip;
  if (!GetInt64FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetInt64FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetStringFromParams(params, "dn_ip", &dn_ip)) {
    os << R"({"error_msg":"need `dn_ip`"})";
    return HttpStatusCode::kBadRequest;
  }

  auto dn = ns_->datanode_manager()->GetDatanodeFromIp(dn_ip);
  if (dn == nullptr) {
    os << R"({"error_msg":"dn_ip is invalid"})";
    return HttpStatusCode::kOk;
  }

  size_t total = 0;
  std::deque<BlockID> block_ids;
  std::tie(total, block_ids) =
      bm_->GetTruncatableBlockIDs(dn->id(), offset, limit);
  os << "{\"truncatable\": [";
  for (int64_t i = 0; i < ((int64_t)block_ids.size()); i++) {
    os << " ";
    os << block_ids[i];
    if (i != block_ids.size() - 1) {
      os << ",";
    }
  }
  os << "]";
  os << ",\"total\":" << total;
  os << "}";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetDnTruncatableCmd(const ParamMap& params,
                                                std::ostringstream& os) {
  int64_t offset = 0;
  int64_t limit = 0;
  std::string dn_ip;
  if (!GetInt64FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetInt64FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetStringFromParams(params, "dn_ip", &dn_ip)) {
    os << R"({"error_msg":"need `dn_ip`"})";
    return HttpStatusCode::kBadRequest;
  }

  auto dn = ns_->datanode_manager()->GetDatanodeFromIp(dn_ip);
  if (dn == nullptr) {
    os << R"({"error_msg":"dn_ip is invalid"})";
    return HttpStatusCode::kOk;
  }

  auto block_ids = dn->PeekInvalidateBlock(offset, limit);
  os << "{\"truncatable_cmd\": [";
  for (int64_t i = 0; i < ((int64_t)block_ids.size()); i++) {
    os << " ";
    os << "\"" << block_ids[i].ToString() << "\"";
    if (i != block_ids.size() - 1) {
      os << ",";
    }
  }
  os << "]}";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetDnInvalidateCmd(const ParamMap& params,
                                               std::ostringstream& os) {
  int64_t offset = 0;
  int64_t limit = 0;
  std::string dn_ip;
  if (!GetInt64FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetInt64FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetStringFromParams(params, "dn_ip", &dn_ip)) {
    os << R"({"error_msg":"need `dn_ip`"})";
    return HttpStatusCode::kBadRequest;
  }

  auto dn = ns_->datanode_manager()->GetDatanodeFromIp(dn_ip);
  if (dn == nullptr) {
    os << R"({"error_msg":"dn_ip is invalid"})";
    return HttpStatusCode::kOk;
  }

  auto block_ids = dn->PeekInvalidateBlock(offset, limit);
  os << "{\"invalidate_cmd\": [";
  for (int64_t i = 0; i < ((int64_t)block_ids.size()); i++) {
    os << " ";
    os << "\"" << block_ids[i].ToString() << "\"";
    if (i != block_ids.size() - 1) {
      os << ",";
    }
  }
  os << "]}";
  return HttpStatusCode::kOk;
}

void ViewHandler::GetJemallocAllocatorInfo(size_t* allocated,
                                           size_t* active,
                                           size_t* resident) {
#if defined(OS_LINUX)
  // clone from redis-5.0.3/zmalloc.c, thanks for redis
  uint64_t epoch = 1;
  size_t sz;
  *allocated = *resident = *active = 0;
  /* Update the statistics cached by mallctl. */
  sz = sizeof(epoch);
  mallctl("epoch", &epoch, &sz, &epoch, sz);
  sz = sizeof(size_t);
  /* Unlike RSS, this does not include RSS from shared libraries and other non
   * heap mappings. */
  mallctl("stats.resident", resident, &sz, NULL, 0);
  /* Unlike resident, this doesn't not include the pages jemalloc reserves
   * for re-use (purge will clean that). */
  mallctl("stats.active", active, &sz, NULL, 0);
  /* Unlike zmalloc_used_memory, this matches the stats.resident by taking
   * into account all allocations done by this process (not only zmalloc). */
  mallctl("stats.allocated", allocated, &sz, NULL, 0);
#else
  *allocated = 0;
  *active = 0;
  *resident = 0;
#endif
}

HttpStatusCode ViewHandler::GetMemoryFragmentation(const ParamMap& params,
                                                   std::ostringstream& os) {
  size_t allocated, active, resident;
  GetJemallocAllocatorInfo(&allocated, &active, &resident);

  float frag_pct = ((float)active / allocated) * 100 - 100;
  size_t frag_bytes = active - allocated;
  float rss_pct = ((float)resident / allocated) * 100 - 100;
  size_t rss_bytes = resident - allocated;

  os << "{\n"
     << "\"allocated\": " << allocated << ","
     << "\"active\": " << active << ","
     << "\"resident\": " << resident << ","
     << "\"frag_pct\": " << frag_pct << ","
     << "\"rss_pct\": " << rss_pct << ","
     << "\"frag_bytes\": " << frag_bytes << ","
     << "\"rss_bytes\": " << rss_bytes << "\n}";

  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetNetBufferMemory(const ParamMap& params,
                                               std::ostringstream& os) {
  os << "{\n"
     << "\"total\": " << cnetpp::tcp::RingBuffer::TotalMemory() << ","
     << "\"may_max\": " << cnetpp::tcp::RingBuffer::MaxRingBuffer() << ","
     << "\"may_min\": " << cnetpp::tcp::RingBuffer::MinRingBuffer() << "\n}";

  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetNetBufferCache(const ParamMap& params,
                                              std::ostringstream& os) {
  auto stats = cnetpp::base::MemoryCache::Instance()->GetStats();
  cnetpp::base::Object r;
  cnetpp::base::Array threads;
  for (auto& stat : stats) {
    cnetpp::base::Array th;
    for (auto& st : stat) {
      cnetpp::base::Object object;
      object["class"] = std::get<0>(st);
      object["count"] = std::get<1>(st);
      object["size"] = std::get<2>(st);
      th.Append(cnetpp::base::Value(object));
    }
    threads.Append(cnetpp::base::Value(th));
  }
  r["threads"] = threads;
  cnetpp::base::Parser parser;
  std::string result;
  parser.Serialize(cnetpp::base::Value(r), &result);
  os << result;
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetBlockIndexStats(const ParamMap& params,
                                               std::ostringstream& os) {
  std::deque<size_t> block_indexes;
  ns_->BlockIndexStats(block_indexes);
  if (block_indexes.size() < 5) {
    os << "[[0,0,0,0,0]]";
    return HttpStatusCode::kOk;
  }

  size_t base = 0, added = 0, removed = 0;
  os << "[\n";
  for (size_t i = 0; i < block_indexes.size(); i += 5) {
    base += block_indexes[i + 2];
    added += block_indexes[i + 3];
    removed += block_indexes[i + 4];
    os << "[" << block_indexes[i] << ","  // datanode_id
       << block_indexes[i + 1] << ","     // ip_id
       << block_indexes[i + 2] << "," << block_indexes[i + 3] << ","
       << block_indexes[i + 4] << "],\n";
  }
  os << "[0,0," << base << "," << added << "," << removed << "]";
  os << "\n]";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetBlockIndexDetail(const ParamMap& params,
                                                std::ostringstream& os) {
  uint32_t id = UINT32_MAX;
  if (!GetUInt32FromParams(params, "dnid", UINT32_MAX, &id) ||
      id == UINT32_MAX) {
    return HttpStatusCode::kBadRequest;
  }

  std::deque<NameBlockIndex*> block_details;
  ns_->BlockIndexDetail(id, block_details);
  if (block_details.empty()) {
    return HttpStatusCode::kNotFound;
  }

  os << "[\n";
  block_details[0]->ToStream(os);
  for (size_t i = 1; i < block_details.size(); i++) {
    os << ",";
    block_details[i]->ToStream(os);
  }
  os << "\n]";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetParentMapStats(const ParamMap& params,
                                              std::ostringstream& os) {
  std::vector<size_t> v;
  ns_->ParentMapStats(v);

  os << "[";
  if (v.size() > 0) {
    os << std::to_string(v[0]);
  }
  for (size_t i = 1; i < v.size(); i++) {
    os << "," << v[i];
  }
  os << "]";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetReplicaPolicy(const ParamMap& params,
                                             std::ostringstream& os) {
  auto policy = ns_->policy_manager()->ReplicaPolicyInstance()->ListPolicy();

  os << "[\n";
  for (size_t i = 0; i < policy.size(); i++) {
    if (i != 0) {
      os << ",";
    }
    auto path = policy[i].first;
    auto policy_str =
        policy[i].second.distributed() ? "DISTRIBUTED" : "CENTRALIZE";
    std::string dc_str = absl::StrJoin(policy[i].second.dc(), ",");
    os << "{\"path\": \"" << path << "\", \"policy\": \"" << policy_str
       << "\", \"dc\": \"" << dc_str << "\"}";
  }
  os << "\n]";
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetReadPolicy(const ParamMap& params,
                                          std::ostringstream& os) {
  auto policy_list =
      ns_->policy_manager()->ReplicaPolicyInstance()->ListPolicy();

  cnetpp::base::Object results;
  for (size_t i = 0; i < policy_list.size(); i++) {
    auto& path = policy_list[i].first;
    auto& policy = policy_list[i].second;

    auto policy_json = PBConverter::ToJson(policy);
    results[path] = policy_json;
  }
  std::string result;
  cnetpp::base::Parser::Serialize(cnetpp::base::Value(std::move(results)),
                                  &result);
  os << result;
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetRecyclePolicy(const ParamMap& params,
                                             std::ostringstream& os) {
  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field. example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field. example: /user/xiongmu/test\n";
    return HttpStatusCode::kOk;
  }

  std::vector<std::pair<std::string, std::shared_ptr<RecyclePolicy>>> ans;
  auto status = ns_->GetRecyclePolicy(normalized_path, &ans);
  if (status.HasException()) {
    os << status.ToString();
    return HttpStatusCode::kOk;
  }

  for (auto& pair : ans) {
    auto& name = pair.first;
    auto& policy_ptr = pair.second;
    os << "'" << name << "'\t"
       << (policy_ptr == nullptr ? "nullptr" : policy_ptr->ShortDebugString())
       << "\n";
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetTrustedIp(const ParamMap& params,
                                         std::ostringstream& os) {
  os << security::ListTrustedIpJsonString();
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetRackAware(const ParamMap& params,
                                         std::ostringstream& os) {
  std::string ip;
  auto itr = params.find("ip");
  if (itr != params.end()) {
    ip = itr->second;
  }

  LOG(INFO) << "Getting Rack for " << ip;

  if (ip.empty()) {
    auto subnet_str = ConfigBasedRackAware::GetSingleton().ListSubnet();
    std::string res = "[";

    for (auto& str : subnet_str) {
      if (res != "[") {
        res += ",";
      }
      res += str;
    }
    res += "]";
    os << res;
  } else {
    auto location = ResolveNetworkLocation(cnetpp::base::IPAddress(ip));
    cnetpp::base::Object jresult;
    jresult["ip"] = ip;
    jresult["location"] = location.ToString();

    std::string result;
    cnetpp::base::Parser parser;
    parser.Serialize(cnetpp::base::Value(std::move(jresult)), &result);
    os << result;
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetRetryCache(const ParamMap& params,
                                          std::ostringstream& os) {
  std::string client_id;
  uint32_t call_id = 0;
  if (!GetStringFromParams(params, "client_id", &client_id)) {
    return HttpStatusCode::kBadRequest;
  }
  if (!GetUInt32FromParams(params, "call_id", &call_id)) {
    return HttpStatusCode::kBadRequest;
  }
  if (!ns_->retry_cache()) {
    return HttpStatusCode::kNoContent;
  }

  cnetpp::base::Object jresult;
  jresult["exist"] = false;
  auto cache_entry = ns_->retry_cache()->GetCacheEntry(client_id, call_id);
  if (cache_entry) {
    jresult["exist"] = true;
    jresult["result"] = cache_entry->ToString();
  }
  std::string result;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(jresult)), &result);
  os << result;
  return HttpStatusCode::kOk;
}

HttpStatusCode ViewHandler::GetOngoingUploadTask(const ParamMap& params,
                                                 std::ostringstream& os) {
  uint32_t offset = 0;
  uint32_t limit = 0;
  uint32_t total = 0;
  if (!GetUInt32FromParams(params, "offset", 0, &offset)) {
    os << R"({"error_msg":"need `offset`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (!GetUInt32FromParams(params, "limit", 0, &limit)) {
    os << R"({"error_msg":"need `limit`"})";
    return HttpStatusCode::kBadRequest;
  }
  if (limit <= 0) {
    limit = 100;
  }

  if (!NameSpace::IsAccMode()) {
    os << R"({"error_msg":"not AccMode"})";
    return HttpStatusCode::kOk;
  }

  auto ongoing_inodes =
      ns_->ufs_uploading_mgr()->copy_upload_ongoing_inode();

  os << "{ \"offset\": " << std::to_string(offset);
  os << ", \"limit\": " << std::to_string(limit);
  os << ", \"result\": [";
  int idx = 0;
  for (auto inode_id : ongoing_inodes) {
    if (idx < offset) {
      idx++;
      continue;
    }

    if (idx >= offset + limit) {
      break;
    }

    if (idx != offset) {
      os << ",";
    }
    os << std::to_string(inode_id);
    idx++;
  }
  os << "]";
  os << ", \"total\": " << std::to_string(ongoing_inodes.size());
  os << "}";
  LOG(INFO) << "GetOngoingUploadTask, result: " << os.str();
  return HttpStatusCode::kOk;
}

}  // namespace dancenn
