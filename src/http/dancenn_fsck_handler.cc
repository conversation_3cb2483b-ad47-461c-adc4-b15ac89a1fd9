// Copyright 2018 <PERSON><PERSON> Huang <<EMAIL>>

#include "http/dancenn_fsck_handler.h"

#include <cnetpp/base/uri.h>

#include <sstream>
#include <string>
#include <unordered_map>

#include "base/path_util.h"
#include "base/string_utils.h"

using HttpStatusCode = cnetpp::http::HttpResponse::StatusCode;

namespace dancenn {
cnetpp::http::HttpResponse DancennFsckHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  cnetpp::base::Uri uri;
  CHECK(uri.ParseUriPath(request.uri()));
  auto params = uri.QueryParams();
  cnetpp::http::HttpResponse response;
  std::string path;
  std::vector<u_int64_t> block_ids;
  bool list_corrupt_blocks = false;
  bool dump_blocks_map = false;
  bool dump_block_detail = false;
  bool need_repair = false;

  bool in_parquet_format = false;
  std::string parquet_compress_type = "";
  std::string block_dump_path = "";
  bool standby_read = false;

  BlockID block_id = 0u;
  int64_t inode_id = 0;
  for (auto& param : params) {
    if (param.first == "path") {
      path = param.second;
    } else if (param.first == "listcorruptfileblocks") {
      list_corrupt_blocks = true;
    } else if (param.first == "dumpblocksmap") {
      dump_blocks_map = true;
    } else if (param.first == "parquet") {
      in_parquet_format = true;
    } else if (param.first == "compress") {
      parquet_compress_type = param.second;
    } else if (param.first == "block_id") {
      int64_t ret = 0;
      if (StringUtils::StringToInt64(
              param.second.c_str(), param.second.size(), &ret)) {
        block_id = ret;
      }
    } else if (param.first == "inode_id") {
      int64_t ret = 0;
      if (StringUtils::StringToInt64(
              param.second.c_str(), param.second.size(), &ret) &&
          ret > 0) {
        inode_id = ret;
      }
    } else if (param.first == "detail") {
      dump_block_detail = param.second == "1" || param.second == "true";
    } else if (param.first == "repair") {
      need_repair = param.second == "1" || param.second == "true";
    } else if (param.first == "dump_path") {
      block_dump_path = param.second;
    } else if (param.first == "standby_read") {
      standby_read = param.second == "1" || param.second == "true";
    }
  }
  dump_block_detail |= need_repair;

  std::ostringstream os;
  auto http_status = HttpStatusCode::kOk;
  if (block_id > 0 && dump_block_detail) {
    // skip block info and dump file info
    inode_id = bm_->GetBlockINodeID(block_id);
  }
  if (inode_id > 0 && path.empty()) {
    path = ns_->BuildFullPath(inode_id);
  }
  std::string normalized_path;
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    os << "Invalid path: " << path;
    auto body = os.str();
    response.set_status(http_status);
    response.SetHttpHeader("Content-Length", std::to_string(body.size()));
    response.SetHttpHeader("Content-Type", "application/text");
    response.set_http_body(body);
    return response;
  }
  if (!path.empty()) {
    auto res = ns_->DumpFileInfo(
        path, dump_block_detail, standby_read, os, &block_ids);
    if (res.HasException()) {
      os << path << " failed to dump info, ex: " << res.message();
    }
    if (list_corrupt_blocks) {
      os << "\nhas no more CORRUPT files\n";
    }
  } else if (block_id > 0) {
    inode_id = bm_->GetBlockINodeID(block_id);
    if (inode_id == kInvalidINodeId) {
      os << "Invalid block: " << block_id;
    } else {
      os << "path: " << ns_->BuildFullPath(inode_id) << " ";
      bm_->DumpBlockInfo(block_id, nullptr, dump_block_detail, os);
      block_ids.push_back(block_id);
    }
  } else if (dump_blocks_map) {
    if (block_dump_path.empty()) {
      os << "Invalid param. block_dump_path should not be empty.";
      http_status = HttpStatusCode::kBadRequest;
    } else if (in_parquet_format) {
      os << "Disabled, otherwise coredump happens.";
      http_status = HttpStatusCode::kBadRequest;
      // bm_->DumpBlocksInParquet(parquet_compress_type, block_dump_path);
    } else {
      os << bm_->DumpBlocks(block_dump_path);
    }
  } else {
    os << "Invalid param";
    http_status = HttpStatusCode::kBadRequest;
  }

  if (need_repair) {
    bm_->ProcessAndDumpFsckBlockReplication(block_ids, os);
  }

  auto body = os.str();
  response.set_status(http_status);
  response.SetHttpHeader("Content-Length", std::to_string(body.size()));
  response.SetHttpHeader("Content-Type", "application/text");
  response.set_http_body(body);
  return response;
}

}  // namespace dancenn
