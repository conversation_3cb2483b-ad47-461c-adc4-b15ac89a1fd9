//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "http/lifecycle_handler.h"

#include <absl/strings/str_format.h>
#include <nlohmann/json.hpp>
#include <resolv.h>

#include "base/path_util.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/namespace.h"

namespace dancenn {

using HttpResponse = cnetpp::http::HttpResponse;
using MethodType = cnetpp::http::HttpRequest::MethodType;

using cloudfs::StorageClassProto;
using cloudfs::ExpirationRuleProto;
using cloudfs::TransitionRuleProto;
using cloudfs::LifecyclePolicyProto;;

HttpResponse
LifecycleHandler::ReturnError(HttpStatusCode code,
                              const std::string& error_msg) {
  if (error_msg.empty()) {
    HttpResponse response;
    response.set_status(code);
    return response;
  }

  cnetpp::base::Object results;
  results["message"] = error_msg;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  HttpResponse response;
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse
LifecycleHandler::Handle(const cnetpp::http::HttpRequest& request) {
  CHECK_NOTNULL(ns_);
  auto ha = ns_->ha_state();
  if (!ha || !ha->IsActive()) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       "Only available at Active.");
  }

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return ReturnError(HttpStatusCode::kBadRequest);
  }
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());

  std::string cmd = "stat";
  GetStringFromParams(param_map, "cmd", &cmd);

  if (cmd == "set") {
    return HandleSetRequest(param_map);
  }
  if (cmd == "unset") {
    return HandleUnsetRequest(param_map);
  }
  if (cmd == "get") {
    return HandleGetRequest(param_map);
  }
  if (cmd == "stat") {
    return HandleStatRequest(param_map);
  }
  if (cmd == "check_storage_class") {
    return HandleCheckRequest(param_map);
  }

  return ReturnError(HttpStatusCode::kBadRequest,
                     absl::StrFormat("Invalid cmd: %s", cmd));
}

cnetpp::http::HttpResponse
LifecycleHandler::HandleSetRequest(const ParamMap& params) {
  std::string path, normalized_path;
  if (!GetStringFromParams(params, "path", &path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Missing argument 'path'"));
  }
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'path' %s", path));
  }

  std::string policy_b64;
  if (!GetStringFromParams(params, "policy", &policy_b64)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'policy'"));
  }
  size_t bufsz = 4096;
  char* buf = new char[bufsz];
  DEFER([buf] { delete[] buf; });
  int len = b64_pton(policy_b64.c_str(),
                     reinterpret_cast<unsigned char*>(buf),
                     bufsz);
  if (len >= bufsz) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Argument policy is too large."));
  }
  buf[len] = '\0';
  std::string policy_str(buf);
  nlohmann::json policy_json;
  LifecyclePolicyProto policy;
  try {
    policy_json = nlohmann::json::parse(policy_str);
  } catch (nlohmann::json::exception& e) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Failed to parse json, %s",
                           e.what()));
  }

  // decode default_class
  try {
    auto cls_name = policy_json["default_class"].get<std::string>();
    StorageClassProto cls;
    bool ok = StorageClassName2ID(cls_name, &cls);
    if (!ok) {
      return ReturnError(HttpStatusCode::kBadRequest,
                         absl::StrFormat("Failed to parse default_class"));
    }
    policy.set_defaultclass(cls);
  } catch (nlohmann::json::exception& e) {
    // not found, do nothing
  }

  // decode expiration_rule
  try {
    auto exprule_json = policy_json["expiration_rule"];
    ExpirationRuleProto exprule;
    if (exprule_json.contains("seconds")) {
      exprule.set_seconds(exprule_json["seconds"].get<int64_t>());
    }
    if (exprule_json.contains("days")) {
      exprule.set_days(exprule_json["days"].get<int64_t>());
    }
    if (exprule_json.contains("recycle_whole_directory")) {
      exprule.set_recycle_whole_directory(
          exprule_json["recycle_whole_directory"].get<bool>());
    }
    policy.mutable_exprule()->CopyFrom(exprule);
  } catch (nlohmann::json::exception& e) {
    // not found, do nothing
  }

  // decode transition_rules
  try {
    auto transrules_json = policy_json["transition_rules"];
    for (auto& elem : transrules_json.items()) {
      TransitionRuleProto trule;
      trule.set_days(elem.value()["days"].get<int64_t>());
      std::string cls_name = elem.value()["target_class"].get<std::string>();
      StorageClassProto cls;
      bool ok = StorageClassName2ID(cls_name, &cls);
      if (!ok) {
        return ReturnError(HttpStatusCode::kBadRequest,
                           absl::StrFormat("Failed to parse transition_rule"));
      }
      trule.set_targetclass(cls);
      policy.add_transrules()->CopyFrom(trule);
    }
  } catch (nlohmann::json::exception& e) {
    // not found, do nothing
  }

  if (!LifecyclePolicyIsValid(policy)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Failed to parse valid policy."));
  }

  SetLifecyclePolicyRequestProto req;
  SetLifecyclePolicyResponseProto resp;
  req.set_path(normalized_path);
  req.mutable_lifecyclepolicy()->CopyFrom(policy);

  SynchronizedRpcClosure done;
  ns_->AsyncSetLifecyclePolicy(normalized_path,
                               req,
                               &resp,
                               ugi,
                               LogRpcInfo(),
                               &done);
  done.Await();

  std::string msg = absl::StrFormat(
      "Set LifecyclePolicy %s, got status %s",
      req.ShortDebugString(), done.status().ToString());
  return ReturnError(HttpStatusCode::kOk, msg);
}

cnetpp::http::HttpResponse
LifecycleHandler::HandleUnsetRequest(const ParamMap& params) {
  std::string path, normalized_path;
  if (!GetStringFromParams(params, "path", &path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Missing argument 'path'"));
  }
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'path' %s", path));
  }

  UnsetLifecyclePolicyRequestProto req;
  UnsetLifecyclePolicyResponseProto resp;
  req.set_path(normalized_path);

  SynchronizedRpcClosure done;
  ns_->AsyncUnsetLifecyclePolicy(normalized_path,
                                 req,
                                 &resp,
                                 ugi,
                                 LogRpcInfo(),
                                 &done);
  done.Await();

  std::string msg = absl::StrFormat(
      "Unset LifecyclePolicy %s, got status %s",
      req.ShortDebugString(), done.status().ToString());
  return ReturnError(HttpStatusCode::kOk, msg);
}

cnetpp::http::HttpResponse
LifecycleHandler::HandleGetRequest(const ParamMap& params) {
  std::string path, normalized_path;
  if (!GetStringFromParams(params, "path", &path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Missing argument 'path'"));
  }
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'path' %s", path));
  }

  LifecyclePolicyProto policy;
  auto st = ns_->GetLifecyclePolicy(normalized_path,
                                    &policy,
                                    ugi,
                                    nullptr);
  if (st.code() == Code::kFileNotFound) {
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Failed to find path %s", normalized_path));
  }
  if (st.code() == Code::kNoEntry) {
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Failed to find LifecyclePolicy for %s",
                        normalized_path));
  }
  if (!st.IsOK()) {
    return ReturnError(HttpStatusCode::kInternalServerError,
                       absl::StrFormat("Failed to get stat for path %s, %s",
                           normalized_path, st.ToString()));
  }

  cnetpp::base::Object result;
  if (policy.has_defaultclass()) {
    std::string cls_name;
    bool ok = StorageClassID2Name(policy.defaultclass(), &cls_name);
    CHECK(ok);
    result["default_class"] = cls_name;
  }
  if (policy.has_exprule()) {
    cnetpp::base::Object exprule;
    if (policy.exprule().has_days()) {
      exprule["days"] = policy.exprule().days();
    }
    if (policy.exprule().has_seconds()) {
      exprule["seconds"] = policy.exprule().seconds();
    }
    if (policy.exprule().has_recycle_whole_directory()) {
      exprule["recycle_whole_directory"] =
          policy.exprule().recycle_whole_directory();
    }
    result["expiration_rule"] = exprule;
  }
  if (policy.transrules_size() > 0) {
    cnetpp::base::Object transrules;
    for (int i = 0; i < policy.transrules_size(); i++) {
      cnetpp::base::Object trule;
      trule["days"] = policy.transrules(i).days();
      trule["target_class"] = policy.transrules(i).targetclass();
      transrules[std::to_string(i)] = trule;
    }
    result["transition_rules"] = transrules;
  }

  std::string str_result;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(result)), &str_result);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_result.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_result);
  return response;
}

cnetpp::http::HttpResponse
LifecycleHandler::HandleStatRequest(const ParamMap& params) {
  std::string path = "/";
  GetStringFromParams(params, "path", &path);
  std::string normalized_path;
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'path' %s", path));
  }

  StorageClassStatProto stat;
  auto st = ns_->GetStorageClassStat(normalized_path, &stat);
  if (st.code() == Code::kFileNotFound) {
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Failed to find path %s", normalized_path));
  }
  if (st.code() == Code::kNoEntry) {
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Failed to find stat for %s", normalized_path));
  }
  if (!st.IsOK()) {
    return ReturnError(
        HttpStatusCode::kInternalServerError,
        absl::StrFormat("Failed to get stat for path %s, %s",
            normalized_path, st.ToString()));
  }
  if (stat.numlogicalbyteexpected_size() == 0) {
    // found old proto data
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Found deprecated stat for %s, try again later",
                        normalized_path));
  }

  cnetpp::base::Object results, tiering_stat;
  for (int cls = StorageClassProto::HOT; cls < StorageClassProto::COLD; cls++) {
    cnetpp::base::Object one_tier;
    std::string cls_name;
    bool ok = StorageClassID2Name(static_cast<StorageClassProto>(cls), &cls_name);
    CHECK(ok);
    one_tier["num_logical_byte_expected"] = stat.numlogicalbyteexpected(cls);
    one_tier["num_logical_byte_effective"] = stat.numlogicalbyte(cls);
    tiering_stat[cls_name] = one_tier;
  }
  results["tiering_stat"] = tiering_stat;
  results["update_timestamp"] = stat.timestampsec();

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse
LifecycleHandler::HandleCheckRequest(const ParamMap& params) {
  std::string path = "/";
  GetStringFromParams(params, "path", &path);
  std::string normalized_path;
  UserGroupInfo ugi;
  if (!NormalizePath(path, ugi.current_user(), &normalized_path)) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       absl::StrFormat("Invalid argument 'path' %s", path));
  }

  StorageClassProto cls;
  auto st = ns_->GetStorageClass(normalized_path, &cls);
  if (st.code() == Code::kFileNotFound) {
    return ReturnError(
        HttpStatusCode::kNotFound,
        absl::StrFormat("Failed to find StorageClass of %s", normalized_path));
  }
  std::string cls_name;
  bool ok = StorageClassID2Name(cls, &cls_name);
  CHECK(ok);

  cnetpp::base::Object results;
  results["StorageClass"] = cls_name;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

} // namespace dancenn
