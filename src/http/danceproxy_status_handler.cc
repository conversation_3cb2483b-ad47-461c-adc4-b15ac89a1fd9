// Copyright 2017 <PERSON><PERSON>i <<EMAIL>>

#include "http/danceproxy_status_handler.h"

#include <cnetpp/base/csonpp.h>

#include <utility>
#include <string>

#include "version.h"  // NOLINT(build/include)

namespace dancenn {

cnetpp::http::HttpResponse DanceproxyStatusHandler::Handle(
    const cnetpp::http::HttpRequest &request) {
  (void) request;
  cnetpp::base::Object results;
  results["vcs_version"] = kVcsVersion;
  results["vcs_build_path"] = kVcsBuildPath;
  results["commit_msg"] = kCommitMsg;
  results["MountsManager"] = mounts_manager_->DumpToJson();
  results["PathTeamSpaceQuotaManager"] = quota_manager_->Dump<PERSON>o<PERSON><PERSON>();
  results["FrozenDirectoryManager"] = frozen_directory_manager_->Dump<PERSON><PERSON><PERSON><PERSON>();
  results["StoragePolicyTTLManager"] =
      storage_policy_ttl_manager_->DumpTo<PERSON>son();
  results["Throttler"] =
      throttler_->Dump<PERSON><PERSON><PERSON><PERSON>();
  cnetpp::http::HttpResponse response;
  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

}  // namespace dancenn

