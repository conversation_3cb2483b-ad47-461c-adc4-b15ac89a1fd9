//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include "acc/acc_namespace.h"
#include "http/http_handler.h"
#include "ufs/ufs_event_manager.h"

namespace dancenn {

class UfsEventHttpHandler : public HttpHandler {
 public:
  UfsEventHttpHandler(std::shared_ptr<UfsEventManager> mgr)
      : mgr_(std::move(mgr)) {
  }
  ~UfsEventHttpHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  cnetpp::http::HttpResponse HandleStatus(const ParamMap& params);
  cnetpp::http::HttpResponse HandleStartConsumer(const ParamMap& params);
  cnetpp::http::HttpResponse HandleStopConsumer(const ParamMap& params);
  cnetpp::http::HttpResponse HandleClearError(const ParamMap& params);

  const std::string name_{"/ufs_event"};
  std::shared_ptr<UfsEventManager> mgr_{nullptr};
};

}  // namespace dancenn
