// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_HTTP_HANDLER_H_
#define HTTP_HTTP_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <memory>
#include <string>
#include <unordered_map>

namespace dancenn {

using HttpStatusCode = cnetpp::http::HttpResponse::StatusCode;
using ParamMap = std::unordered_map<std::string, std::string>;

class HttpHandler {
 public:
  HttpHandler() {}
  virtual ~HttpHandler() = default;

  HttpHandler(const HttpHandler&) = delete;
  HttpHandler& operator=(const HttpHandler&) = delete;

  static cnetpp::http::HttpResponse NotFound(const std::string& uri);

  virtual const std::string& name() const = 0;

  virtual cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) = 0;

 protected:
  bool GetBoolFromParams(const ParamMap& params, const std::string& name,
                         bool* value);
  bool GetUInt32FromParams(const ParamMap& params, const std::string& name,
                           uint32_t* value);
  bool GetInt64FromParams(const ParamMap& params, const std::string& name,
                          int64_t* value);
  bool GetStringFromParams(const ParamMap& params, const std::string& name,
                           std::string* value);
  // with default_value
  bool GetUInt32FromParams(const ParamMap& params, const std::string& name,
                           uint32_t default_value, uint32_t* value);
  bool GetInt64FromParams(const ParamMap& params, const std::string& name,
                          int64_t default_value, int64_t* value);
};

}  // namespace dancenn

#endif  // HTTP_HTTP_HANDLER_H_

