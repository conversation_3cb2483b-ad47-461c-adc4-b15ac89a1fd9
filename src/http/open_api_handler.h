// Copyright 2021 Mu <PERSON> <<EMAIL>>

#ifndef HTTP_OPEN_API_HANDLER_H_
#define HTTP_OPEN_API_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <memory>
#include <string>

#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"
#include "namespace/namespace.h"
#include "op_task/op_task_manager.h"

namespace dancenn {

class OpenAPIHandler : public HttpHandler {
 public:
  OpenAPIHandler(std::shared_ptr<NameSpace> ns,
                 std::shared_ptr<BlockManager> bm,
                 std::shared_ptr<DatanodeManager> dnm,
                 std::shared_ptr<OpTaskManager> optm)
      : namespace_(ns),
        block_manager_(bm),
        datanode_manager_(dnm),
        op_task_manager_(optm) {}
  ~OpenAPIHandler() override = default;

  const std::string& name() const override { return name_; }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  const std::string name_{"/api"};

  HttpStatusCode HandleDnReplacementTask(
      const cnetpp::http::HttpRequest& request,
      const ParamMap& param_map,
      std::ostringstream& oss);
  HttpStatusCode ListDnReplacementTask(const ParamMap& param_map,
                                       std::ostringstream& oss);
  HttpStatusCode GetDnReplacementTask(const std::string& task_id,
                                      std::ostringstream& oss);
  HttpStatusCode PutDnReplacementTask(const std::string& task_id,
                                      const std::string& body,
                                      std::ostringstream& oss);
  HttpStatusCode DelDnReplacementTask(const std::string& task_id,
                                      std::ostringstream& oss);

  HttpStatusCode HandleMkdir(const ParamMap& params, std::ostringstream& oss);
  HttpStatusCode HandleCheckIfDirEmpty(const ParamMap& params,
                                       std::ostringstream& oss);
  HttpStatusCode HandleDelete(const ParamMap& params, std::ostringstream& oss);

  std::shared_ptr<NameSpace> namespace_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<OpTaskManager> op_task_manager_;
};

}  // namespace dancenn

#endif  // HTTP_OPEN_API_HANDLER_H_
