// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_DANCENN_STATUS_HANDLER_H_
#define HTTP_DANCENN_STATUS_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <memory>
#include <string>
#include <utility>

#include "base/runtime_monitor.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"

namespace dancenn {

class DancennStatusHandler : public HttpHandler {
 public:
  explicit DancennStatusHandler(
      std::shared_ptr<NameSpace> ns,
      std::shared_ptr<DatanodeManager> datanode_manager,
      std::shared_ptr<BlockManager> block_manager,
      std::shared_ptr<HAStateBase> ha_state,
      std::shared_ptr<SafeModeBase> safemode,
      RuntimeMonitor* runtime_monitor)
      : ns_(std::move(ns)),
        datanode_manager_(std::move(datanode_manager)),
        block_manager_(std::move(block_manager)),
        ha_state_(std::move(ha_state)),
        safemode_(std::move(safemode)),
        runtime_monitor_(runtime_monitor) {
  }
  ~DancennStatusHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:

  const std::string name_ { "/status" };
  std::shared_ptr<NameSpace> ns_ { nullptr };
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<HAStateBase> ha_state_ { nullptr };
  std::shared_ptr<SafeModeBase> safemode_ { nullptr };
  RuntimeMonitor* runtime_monitor_{nullptr};
};

}  // namespace dancenn

#endif  // HTTP_DANCENN_STATUS_HANDLER_H_

