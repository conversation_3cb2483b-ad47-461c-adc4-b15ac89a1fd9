// Copyright 2024 Chengxu <<EMAIL>>

#pragma once

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>

#include "http/http_handler.h"

namespace dancenn {

class PrometheusHandler : public HttpHandler {
 public:
  PrometheusHandler();
  ~PrometheusHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;
 private:
  void ParseName(const std::string& key, std::string* name, std::string* tag);
  
  const std::string name_ { "/prometheus" };
  std::string tag_prefix_;
  std::string metrics_prefix_;
};

}  // namespace dancenn


