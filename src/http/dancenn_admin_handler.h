// Copyright 2018 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef HTTP_DANCENN_ADMIN_HANDLER_H_
#define HTTP_DANCENN_ADMIN_HANDLER_H_

#include <string>

#include "acc/acc_namespace.h"
#include "acc/mkdir_create_in_ufs_scanner.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"
#include "namespace/meta_scanner_v2.h"
#include "namespace/namespace.h"
#include "ufs/ufs.h"

namespace dancenn {

class DancennAdminHandler : public HttpHandler {
 public:
  DancennAdminHandler(std::shared_ptr<AccNamespace> acc_ns,
                      std::shared_ptr<NameSpace> ns,
                      std::shared_ptr<BlockManager> bm,
                      std::shared_ptr<Ufs> ufs);

  ~DancennAdminHandler() override = default;

  const std::string& name() const override { return name_; }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  HttpStatusCode PurgeLogs(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode PurgeMemory(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode DebugMemory(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode GetLastCkptTxId(const ParamMap& params,
                                 std::ostringstream& os);

  HttpStatusCode GetStackInfo(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode ForceCompactDeletion(const ParamMap& params,
                                      std::ostringstream& os);

  HttpStatusCode ForceCompactAll(const ParamMap& params,
                                 std::ostringstream& os);

  HttpStatusCode ForceCompactSCR(const ParamMap& params,
                                 std::ostringstream& os);

  HttpStatusCode CreateCheckpoint(const ParamMap& params,
                                  std::ostringstream& os);

  // admin?cmd=storage_policy&path=<path>&policy=<policy_name>
  HttpStatusCode SetStoragePolicy(const ParamMap& params,
                                  std::ostringstream& os);

  // admin?cmd=replica_policy&action=[set|remove|get|ls]&
  //       path=<path>&policy=[none|centralize|distribute]&dc=<dc_list>&
  //       local_switch_target=[n]&local_switch_target=[n]
  HttpStatusCode ManageReplicaPolicy(const ParamMap& params,
                                     std::ostringstream& os);

  // admin?cmd=read_policy&action=[set|remove|get|ls]&path=<path>&localdconly=<boolean>&blacklistdc=<dc_list>
  HttpStatusCode ManageReadPolicy(const ParamMap& params,
                                  std::ostringstream& os);

  // admin?cmd=upload_policy&action=[set|remove|get|ls]&path=<path>&upload_interval_ms=[num]
  HttpStatusCode ManageUploadPolicy(const ParamMap& params,
                                    std::ostringstream& os);

  HttpStatusCode ManageDirPolicy(const ParamMap& params,
                                 std::ostringstream& os);

  // admin?cmd=recycle_policy&action[set|remove]&path=<path>&recycle_time=<int64_t>
  HttpStatusCode SetRecyclePolicy(const ParamMap& params,
                                  std::ostringstream& os);

  // admin?cmd=mount_point&path=<path>&allow_entries=<e1,e2,...>&deny_entries=<e1,e2,...>
  HttpStatusCode SetMountPoint(const ParamMap& params, std::ostringstream& os);

  // deprecated
  HttpStatusCode RefreshDatanodeMachineInfo(const ParamMap& params,
                                            std::ostringstream& os);

  // admin?cmd=set_az_blacklist&list=<az_list>
  HttpStatusCode SetAZBlacklist(const ParamMap& params, std::ostringstream& os);

  // admin?cmd=get_az_blacklist
  HttpStatusCode GetAZBlacklist(const ParamMap& params, std::ostringstream& os);

  // admin?cmd=fbr&dn=<dn|all>
  // dn is internal_id, i.e. 1, 2, 3
  HttpStatusCode TriggerFullBlockReport(const ParamMap& params,
                                        std::ostringstream& os);
  // admin?cmd=merge_blocks&inode_id=<inode_id>
  HttpStatusCode MergeBlocks(const ParamMap& params, std::ostringstream& os);

  // admin?cmd=persist_file&inode_id=<inode_id>[&key=<key>&upload_id=<upload_id>]
  HttpStatusCode PersistFile(const ParamMap& params, std::ostringstream& os);

  // admin?cmd=enter_destroy_state
  HttpStatusCode EnterDestroyState(const ParamMap& params,
                                   std::ostringstream& os);
  // admin?cmd=exit_destroy_state
  HttpStatusCode ExitDestroyState(const ParamMap& params,
                                  std::ostringstream& os);

  // admin?cmd=switch_active_to_ha
  HttpStatusCode SwitchNonHAActiveToHAActive(const ParamMap& params,
                                             std::ostringstream& os);
  // admin?cmd=switch_active_to_non_ha
  HttpStatusCode SwitchHAActiveToNonHAActive(const ParamMap& params,
                                             std::ostringstream& os);
  // admin?cmd=switch_standby_to_non_ha&force_active=true/false
  HttpStatusCode SwitchHAStandbyToNonHAActive(const ParamMap& params,
                                              std::ostringstream& os);

  // admin?cmd=enter_safemode
  HttpStatusCode EnterSafeMode(const ParamMap& params, std::ostringstream& os);
  // admin?cmd=leave_safemode
  HttpStatusCode LeaveSafeMode(const ParamMap& params, std::ostringstream& os);

  // Ufs
  // admin?cmd=ufs_trigger_upload&inode_id=[xxx]
  HttpStatusCode UfsTriggerUpload(const ParamMap& params,
                                  std::ostringstream& os);

  HttpStatusCode Pin(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode RemoveBlockStorage(const ParamMap& params,
                                    std::ostringstream& os);

  HttpStatusCode DoSync(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode DoFree(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode AddBlockIdAndGs(const ParamMap& params,
                                 std::ostringstream& os);

  HttpStatusCode RunMkdirSyncTask(const ParamMap& params,
                                  std::ostringstream& os);

  HttpStatusCode PreTransition(const ParamMap& params, std::ostringstream& os);

  HttpStatusCode ExitPreTransition(const ParamMap& params,
                                   std::ostringstream& os);

 private:
  typedef HttpStatusCode (
      DancennAdminHandler::*FuncType)(const ParamMap&, std::ostringstream& os);

  const std::string name_{"/admin"};

  std::shared_ptr<AccNamespace> acc_ns_;
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> bm_;
  std::shared_ptr<Ufs> ufs_;

  std::map<std::string, FuncType> table_;

  std::mutex mutex_;
  std::shared_ptr<MkdirCreateInUfsScanner> mkdir_sync_task_scanner_{nullptr};
  MetaScannerTaskID mkdir_sync_task_task_id_{UINT64_MAX};
};

}  // namespace dancenn

#endif  // HTTP_DANCENN_ADMIN_HANDLER_H_
