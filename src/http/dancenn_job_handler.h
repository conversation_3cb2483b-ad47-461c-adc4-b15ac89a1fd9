//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <string>
#include <utility>

// Third
#include "cnetpp/http/http_request.h"
#include "cnetpp/http/http_response.h"

// Project
#include "http/http_handler.h"
#include "job_manager/job_manager.h"
#include "job_manager/managed_job_state.h"

namespace dancenn {

class JobManagerHandler : public HttpHandler {
 public:
  explicit JobManagerHandler(std::shared_ptr<JobManager> job_manager)
      : job_manager_(job_manager) {
  }
  ~JobManagerHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  cnetpp::http::HttpResponse ReturnError(HttpStatusCode code, const std::string& error_msg = "");
  cnetpp::http::HttpResponse HandleLookupJobRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleCancelJobRequest(const ParamMap& params);
  cnetpp::http::HttpResponse HandleListJobRequest(const ParamMap& params);

 private:
  const std::string name_{"/job"};
  std::shared_ptr<JobManager> job_manager_{nullptr};
};

}  // namespace dancenn
