// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_HTTP_SERVER_H_
#define HTTP_HTTP_SERVER_H_

#include <cnetpp/http/http_server.h>
#include <cnetpp/http/http_connection.h>
#include <cnetpp/concurrency/thread_pool.h>

#include <memory>
#include <string>
#include <map>

#include "http/http_handler.h"
#include "datanode_manager/datanode_manager.h"
#include "block_manager/block_manager.h"

namespace dancenn {

class HttpServer {
 public:
  using Workers = std::shared_ptr<cnetpp::concurrency::ThreadPool>;

  HttpServer() = default;
  virtual ~HttpServer() = default;

  bool Launch(const cnetpp::base::EndPoint& local_address);

  bool FastShutdown();
  bool Shutdown();

  const Workers& workers() const {
    return workers_;
  }

 protected:
  virtual void RegisterHandlers() = 0;

  cnetpp::http::HttpServer http_server_;

  Workers workers_;

  std::map<std::string, std::shared_ptr<HttpHandler>> handlers_;

  std::unordered_map<cnetpp::tcp::ConnectionId,
                     std::shared_ptr<cnetpp::http::HttpConnection>>
      http_connections_;
  ReadWriteLock connections_lock_;
  std::atomic<bool> stopped_{false};

  void SetCallbacks(cnetpp::http::HttpServerOptions* options);

  bool OnConnected(std::shared_ptr<cnetpp::http::HttpConnection> connection);
  bool OnReceived(std::shared_ptr<cnetpp::http::HttpConnection> connection);
  bool OnSent(bool success,
      std::shared_ptr<cnetpp::http::HttpConnection> connection);
  bool OnClosed(std::shared_ptr<cnetpp::http::HttpConnection> connection);
};

}  // namespace dancenn

#endif  // HTTP_HTTP_SERVER_H_

