// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_DANCENN_HTTP_SERVER_H_
#define HTTP_DANCENN_HTTP_SERVER_H_

#include <cnetpp/http/http_server.h>

#include <memory>

#include "http/http_server.h"
#include "server/dancenn.h"

namespace dancenn {

class DanceNNRuntime;

class DancennHttpServer : public HttpServer {
 public:
  DancennHttpServer(DanceNNRuntime* runtime) : runtime_(runtime) {
    CHECK_NOTNULL(runtime_);
  }
  virtual ~DancennHttpServer() = default;

 private:
  void RegisterHandlers() override;

  DanceNNRuntime* runtime_{nullptr};
};

}  // namespace dancenn

#endif  // HTTP_DANCENN_HTTP_SERVER_H_

