// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_DANCEPROXY_STATUS_HANDLER_H_
#define HTTP_DANCEPROXY_STATUS_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>
#include <memory>

#include "http/http_handler.h"
#include "proxy/mounts_manager.h"
#include "proxy/path_team_space_quota_manager.h"
#include "proxy/frozen_directory_manager.h"
#include "proxy/storage_policy_ttl_manager.h"
#include "proxy/proxy_throttler.h"

namespace dancenn {

class DanceproxyStatusHandler : public HttpHandler {
 public:
  explicit DanceproxyStatusHandler(
      std::shared_ptr<MountsManager> mounts_manager,
      std::shared_ptr<PathTeamSpaceQuotaManager> quota_manager,
      std::shared_ptr<FrozenDirectoryManager> frozen_directory_manager,
      std::shared_ptr<StoragePolicyTTLManager> storage_policy_ttl_manager,
      std::shared_ptr<ProxyThrottler> throttler)
      : mounts_manager_(mounts_manager),
        quota_manager_(quota_manager),
        frozen_directory_manager_(frozen_directory_manager),
        storage_policy_ttl_manager_(storage_policy_ttl_manager),
        throttler_(throttler) {}
  ~DanceproxyStatusHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  const std::string name_ { "/status" };
  std::shared_ptr<MountsManager> mounts_manager_;
  std::shared_ptr<PathTeamSpaceQuotaManager> quota_manager_;
  std::shared_ptr<FrozenDirectoryManager> frozen_directory_manager_;
  std::shared_ptr<StoragePolicyTTLManager> storage_policy_ttl_manager_;
  std::shared_ptr<ProxyThrottler> throttler_;
};

}  // namespace dancenn

#endif  // HTTP_DANCEPROXY_STATUS_HANDLER_H_

