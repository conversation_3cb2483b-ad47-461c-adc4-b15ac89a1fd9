// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <string>
#include <memory>
#include <utility>

#include "http/danceproxy_http_server.h"
#include "http/http_handler.h"
#include "http/metrics_handler.h"
#include "http/prometheus_handler.h"
#include "http/configurations_handler.h"
#include "http/danceproxy_status_handler.h"

namespace dancenn {

void DanceproxyHttpServer::RegisterHandlers() {
  auto metrics_handler = std::unique_ptr<HttpHandler>(new MetricsHandler);
  const std::string& metrics_name = metrics_handler->name();
  handlers_.emplace(metrics_name, std::move(metrics_handler));

  auto prometheus_handler = std::unique_ptr<HttpHandler>(new PrometheusHandler);
  const std::string& prometheus_name = prometheus_handler->name();
  handlers_.emplace(prometheus_name, std::move(prometheus_handler));

  auto status_handler = std::unique_ptr<HttpHandler>(
      new DanceproxyStatusHandler(mounts_manager_,
                                  quota_manager_,
                                  frozen_directory_manager_,
                                  storage_policy_ttl_manager_,
                                  throttler_));
  const std::string& status_name = status_handler->name();
  handlers_.emplace(status_name, std::move(status_handler));

  auto configurations_handler =
    std::unique_ptr<HttpHandler>(new ConfigurationsHandler);
  const std::string& config_name = configurations_handler->name();
  handlers_.emplace(config_name, std::move(configurations_handler));
}

}  // namespace dancenn

