// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/dancenn_http_server.h"

#include <memory>
#include <string>
#include <utility>

#include "datanode_manager/datanode_manager.h"
#include "http/configurations_handler.h"
#include "http/dancenn_admin_handler.h"
#include "http/dancenn_fs_handler.h"
#include "http/dancenn_fsck_handler.h"
#include "http/dancenn_job_handler.h"
#include "http/dancenn_status_handler.h"
#include "http/decommission_handler.h"
#include "http/dirstat_handler.h"
#include "http/http_handler.h"
#include "http/lifecycle_handler.h"
#include "http/metrics_handler.h"
#include "http/open_api_handler.h"
#include "http/prometheus_handler.h"
#include "http/quota_handler.h"
#include "http/refresh_handler.h"
#include "http/stale2_handler.h"
#include "http/stale_handler.h"
#include "http/ufs_event_http_handler.h"
#include "http/view_handler.h"

namespace dancenn {

void DancennHttpServer::RegisterHandlers() {
  auto metrics_handler = std::make_shared<MetricsHandler>();
  handlers_.emplace(metrics_handler->name(), std::move(metrics_handler));

  auto prometheus_handler = std::make_shared<PrometheusHandler>();
  handlers_.emplace(prometheus_handler->name(), std::move(prometheus_handler));

  auto status_handler =
      std::make_shared<DancennStatusHandler>(runtime_->ns,
                                             runtime_->datanode_manager,
                                             runtime_->block_manager,
                                             runtime_->ha_state,
                                             runtime_->safemode,
                                             runtime_->runtime_monitor.get());
  handlers_.emplace(status_handler->name(), std::move(status_handler));

  auto configurations_handler = std::make_shared<ConfigurationsHandler>();
  handlers_.emplace(configurations_handler->name(),
                    std::move(configurations_handler));

  auto fsck_handler = std::make_shared<DancennFsckHandler>(
      runtime_->ns, runtime_->block_manager);
  handlers_.emplace(fsck_handler->name(), std::move(fsck_handler));

  auto admin_handler = std::make_shared<DancennAdminHandler>(
      runtime_->acc_namespace,
      runtime_->ns,
      runtime_->block_manager,
      runtime_->ufs_env ? runtime_->ufs_env->get_only_ufs() : nullptr);
  handlers_.emplace(admin_handler->name(), std::move(admin_handler));

  auto view_handler =
      std::make_shared<ViewHandler>(runtime_->ns, runtime_->block_manager);
  handlers_.emplace(view_handler->name(), std::move(view_handler));

  auto stale_handler =
      std::make_shared<StaleHandler>(runtime_->datanode_manager);
  handlers_.emplace(stale_handler->name(), stale_handler);

  auto stale2_handler =
      std::make_shared<Stale2Handler>(runtime_->datanode_manager);
  handlers_.emplace(stale2_handler->name(), stale2_handler);

  auto refresh_handler = std::make_shared<RefreshHandler>();
  handlers_.emplace(refresh_handler->name(), std::move(refresh_handler));

  auto api_handler =
      std::make_shared<OpenAPIHandler>(runtime_->ns,
                                       runtime_->block_manager,
                                       runtime_->datanode_manager,
                                       runtime_->op_task_manager);
  handlers_.emplace(api_handler->name(), std::move(api_handler));

  auto dirstat_handler = std::make_shared<DirStatHandler>(runtime_->ns);
  handlers_.emplace(dirstat_handler->name(), std::move(dirstat_handler));

  auto fs_handler =
      std::make_shared<FSHandler>(runtime_->acc_namespace, runtime_->ns);
  handlers_.emplace(fs_handler->name(), std::move(fs_handler));

  auto quota_handler = std::make_shared<QuotaHandler>(runtime_->ns);
  handlers_.emplace(quota_handler->name(), std::move(quota_handler));

  auto lifecycle_handler = std::make_shared<LifecycleHandler>(runtime_->ns);
  handlers_.emplace(lifecycle_handler->name(), std::move(lifecycle_handler));

  auto job_handler = std::make_shared<JobManagerHandler>(runtime_->job_manager);
  handlers_.emplace(job_handler->name(), std::move(job_handler));

  if (runtime_->ufs_env != nullptr &&
      runtime_->ufs_env->event_manager() != nullptr) {
    auto ufs_event_http_handler = std::make_shared<UfsEventHttpHandler>(
        runtime_->ufs_env->event_manager());
    handlers_.emplace(ufs_event_http_handler->name(),
                      std::move(ufs_event_http_handler));
  }

  // legacy
  auto decommission_handler =
      std::make_shared<DecommissionHandler>(runtime_->datanode_manager);
  handlers_.emplace(decommission_handler->name(),
                    std::move(decommission_handler));
}

}  // namespace dancenn
