// Copyright 2020 Cheng <PERSON> <<EMAIL>>

#ifndef HTTP_DECOMMISSION_HANDLER_H_
#define HTTP_DECOMMISSION_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <mutex>
#include <string>
#include <unordered_set>

#include "http/http_handler.h"
#include "stale_handler.h"

namespace dancenn {

class DecommissionHandler : public HttpHandler {
 public:
  explicit DecommissionHandler(
      std::shared_ptr<DatanodeManager> datanode_manager)
      : datanode_manager_(datanode_manager) {
  }
  ~DecommissionHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  const std::string name_{"/decommission"};
  std::shared_ptr<DatanodeManager> datanode_manager_;
};

}  // namespace dancenn

#endif  // HTTP_DECOMMISSION_HANDLER_H_
