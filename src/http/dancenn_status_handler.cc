// Copyright 2017 <PERSON><PERSON> Lei <<EMAIL>>

#include "http/dancenn_status_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/uri.h>

#include <utility>
#include <string>

#include "hdfs.pb.h"
#include "version.h"  // NOLINT(build/include)
#include "namespace/namespace.h"
#include "datanode_manager/datanode_manager.h"
#include "block_manager/block_manager.h"
#include "base/pb_converter.h"

DECLARE_string(bvc_version);
DECLARE_bool(dancenn_observe_mode_on);

namespace dancenn {
namespace {

std::string SerializeNamespaceType(cloudfs::NamespaceType namespace_type) {
  switch (namespace_type) {
    case cloudfs::NamespaceType::TOS_LOCAL:
      return "local";
    case cloudfs::NamespaceType::TOS_MANAGED:
      return "tos managed";
    case cloudfs::NamespaceType::ACC_TOS:
      return "tos raw";
    case cloudfs::NamespaceType::LOCAL:
      return "no tos";
    case cloudfs::NamespaceType::ACC_HDFS:
      return "hdfs raw";
    default:
      return "unknown";
  }
}

} // namespace

cnetpp::http::HttpResponse DancennStatusHandler::Handle(
    const cnetpp::http::HttpRequest &request) {
  (void) request;
  CHECK_NOTNULL(ha_state_);
  CHECK_NOTNULL(safemode_);

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    cnetpp::http::HttpResponse response;
    response.set_status(HttpStatusCode::kBadRequest);
    return response;
  }
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());
  bool vvv = false;
  GetBoolFromParams(param_map, "vvv", &vvv);
  bool is_detail = false;
  GetBoolFromParams(param_map, "detail", &is_detail);
  bool show_lease_stat = false;
  GetBoolFromParams(param_map, "lease_stat", &show_lease_stat);
  bool show_storage = false;
  GetBoolFromParams(param_map, "storage", &show_storage);
  std::string dns_str;
  GetStringFromParams(param_map, "dns", &dns_str);
  if (vvv) {
    is_detail = true;
    show_lease_stat = true;
    show_storage = true;
  }

  cnetpp::base::Object results;

  results["filesystem_id"] = ns_->filesystem_id();
  results["namespace_id"] = ns_->namespace_id();
  results["namespace_type"] = SerializeNamespaceType(ns_->namespace_type());
  results["persistent_ufs_info"] = ns_->persistent_ufs_info().SerializeToJson();
  results["since_last_catchup_txid"] = ns_->SinceLastCatchupTxid();
  results["last_ckpt_txid"] = ns_->GetLastCkptTxId();
  results["last_inode_id"] = ns_->last_inode_id();
  results["num_inodes"] = ns_->num_inodes();
  results["last_block_id"] = ns_->last_allocated_block_id();
  results["generation_stamp_v1"] = ns_->generation_stamp_v1();
  results["generation_stamp_v1_limit"] = ns_->generation_stamp_v1_limit();
  results["generation_stamp_v2"] = ns_->generation_stamp_v2();
  results["blockpool_id"] = ns_->blockpool_id();
  results["cluster_id"] = ns_->cluster_id();
  results["ctime"] = ns_->ctime();
  results["layout_version"] = ns_->layout_version();
  results["namespace_id"] = ns_->namespace_id();
  results["software_version"] = ns_->software_version();
  results["vcs_version"] = kVcsVersion;
  results["vcs_build_path"] = kVcsBuildPath;
  results["commit_msg"] = kCommitMsg;
  results["bvc_version"] = FLAGS_bvc_version;
  results["dc_blacklist"] = datanode_manager_->GetBlacklistDc();
  results["dc_majority"] = datanode_manager_->GetMajorityDc();

  {
    cnetpp::base::Object block_manager;
    cnetpp::base::Object each_priority;
    auto size_each_priority =
        block_manager_->needed_replications().SizeEachPriority();
    size_t i = 0;
    for (auto& sep : size_each_priority) {
      each_priority[std::to_string(i)] =  static_cast<uint32_t>(sep);
      i++;
    }
    block_manager["needed_replications_each_priority"] = each_priority;
    block_manager["needed_replications"] = static_cast<uint32_t>(
        block_manager_->needed_replications().GetUnderReplicatedBlockCount());
    block_manager["needed_replications_corrupt_blocks"] = static_cast<uint32_t>(
        block_manager_->needed_replications().GetCorruptBlockSize());
    block_manager["needed_replications_corrupt_repl_one_blocks"] =
        static_cast<uint32_t>(
            block_manager_->needed_replications().GetCorruptReplOneBlockSize());

    auto stats = block_manager_->stat();
    block_manager["num_under_construct"] = stats.num_uc;
    block_manager["num_postponed_misreplicated"]
        = stats.num_postponed_misreplicated;
    block_manager["num_invalidate"] = stats.num_invalidate;
    block_manager["num_truncatable"] = stats.num_truncatable;
    block_manager["num_corrupt_block"] = stats.num_corrupt_block;
    block_manager["num_sealed_block"] = stats.num_sealed_block;
    block_manager["num_recover"] = stats.num_recover;
    block_manager["num_excess_replicas"] = stats.num_excess_replicas;
    block_manager["num_replication_queue"] = stats.num_replication_queue;
    block_manager["num_future_blocks"] = stats.num_future_blocks;
    block_manager["num_misinvalidated_blocks"] =
      stats.num_misinvalidated_blocks;
    block_manager["num_pending_replication_blocks"] =
      stats.num_pending_replication;

    results["block_manager"] = block_manager;
  }

  if (show_lease_stat) {
    ns_->lease_manager()->UpdateStats();  // for data freshness
    auto stats = ns_->lease_manager()->Stats();
    cnetpp::base::Object lease_manager;
    lease_manager["holder_num"] = static_cast<uint32_t>(stats.holder_num);
    lease_manager["lease_num"] = static_cast<uint32_t>(stats.lease_num);
    lease_manager["path_num"] = static_cast<uint32_t>(stats.path_num);
    results["lease_manager"] = lease_manager;
  }

  {
    results["dynamic_rwlocks"] =
      static_cast<uint32_t>(ns_->NumDynamicRWLocks());
    results["static_rwlocks"] = static_cast<uint32_t>(ns_->NumStaticRWLocks());
  }

  {
    auto dmstat = datanode_manager_->stat();
    cnetpp::base::Object dn_stat;
    dn_stat["total"] = dmstat.num_total_datanode;
    dn_stat["functional"] = dmstat.num_functional_datanode;
    dn_stat["alive"] = dmstat.num_alive_datanode;
    dn_stat["content_stale"] = dmstat.num_content_stale_datanode;
    dn_stat["stale"] = dmstat.num_stale_datanode;
    dn_stat["decommission"] = dmstat.num_decommission_datanode;
    dn_stat["decommissioned"] = dmstat.num_decommissioned_datanode;
    dn_stat["dead"] = dmstat.num_dead_datanode;
    dn_stat["xceiver_count"] = dmstat.xceiver_count;
    dn_stat["blockpool_used_bytes"] = dmstat.blockpool_used;
    dn_stat["dfs_used_bytes"] = dmstat.dfs_used;
    dn_stat["nondfs_used_bytes"] = dmstat.non_dfs_used;
    dn_stat["remaining_bytes"] = dmstat.remaining;
    dn_stat["temperature_based_remaining"] = dmstat.temp_based_remaining;
    dn_stat["capacity_bytes"] = dmstat.capacity;
    // kb
    dn_stat["blockpool_used_KB"] = dmstat.blockpool_used_kb;
    dn_stat["dfs_used_KB"] = dmstat.dfs_used_kb;
    dn_stat["nondfs_used_KB"] = dmstat.non_dfs_used_kb;
    dn_stat["remaining_KB"] = dmstat.remaining_kb;
    dn_stat["capacity_KB"] = dmstat.capacity_kb;
    // ratio
    dn_stat["space_usage"] = dmstat.capacity_kb ?
                             static_cast<double>(dmstat.dfs_used_kb) /
                             static_cast<double>(dmstat.capacity_kb) : 0;

    cnetpp::base::Object dn_info;
    dn_info["stats"] = dn_stat;

    if (!dns_str.empty() || is_detail) {
      std::vector<std::string> dns_hosts =
          cnetpp::base::StringUtils::SplitByChars(dns_str, ",");
      std::unordered_set<std::string> dns_host_table(dns_hosts.begin(),
                                                     dns_hosts.end());

      auto datanodes = datanode_manager_->GetAllDatanodeInfo();
      cnetpp::base::Array dns;

      for (const auto& dn : datanodes) {
        if (is_detail ||
            dns_host_table.count(dn->ip().ToString()) ||
            dns_host_table.count(dn->socket_address())) {
          auto detail = dn->ToJson(show_storage);
          dns.Append(cnetpp::base::Value(detail));
        }
      }

      dn_info["all"] = dns;
    }

    results["datanodes"] = dn_info;
  }

  results["hadoop_ha_state"] =
      cloudfs::HAServiceStateProto_Name(ns_->GetHAServiceStateInternal());
  {
    std::string HAServiceState =
        cloudfs::HAServiceStateProto_Name(ha_state_->GetState());
    if (HAServiceState == "ACTIVE") {
      results["ha_state"] = "ACTIVE";
    } else if (HAServiceState == "STANDBY") {
      if (FLAGS_dancenn_observe_mode_on) {
        results["ha_state"] = "OBSERVER";
      } else {
        results["ha_state"] = "STANDBY";
      }
    } else {
      results["ha_state"] = HAServiceState;
    }
  }

  results["ha_state_in_transition_to_active"] = ha_state_->InTransition();
  results["ha_state_in_transition"] = ha_state_->InTransition();

  results["ha_destroy_state"] = ha_state_->IsDestroyState();

  results["last_standby_enter_at"] =
    static_cast<uint64_t>(ha_state_->last_enter_standby_time());
  results["last_standby_leave_at"] =
    static_cast<uint64_t>(ha_state_->last_leave_standby_time());

  results["last_safemode_enter_at"] =
    static_cast<uint64_t>(safemode_->last_enter_time());
  results["last_safemode_leave_at"] =
    static_cast<uint64_t>(safemode_->last_leave_time());

  std::string safemode_tips;
  safemode_->GetSafeModeTip(&safemode_tips);
  results["safemode"] = safemode_tips;

  results["block_num"] = block_manager_->GetBlockNum();
  results["zero_replica_block_num"] = block_manager_->GetZeroReplicaBlockNum();

  cnetpp::base::Object brief_metrics;
  uint64_t meta_storage_disk_total = 0;
  uint64_t meta_storage_disk_used = 0;
  double meta_storage_disk_utilization = 0;
  if (runtime_monitor_) {
    runtime_monitor_->GetMetaStorageDiskUsage(&meta_storage_disk_total,
                                              &meta_storage_disk_used,
                                              &meta_storage_disk_utilization);
  }
  brief_metrics["MetaStorageDiskTotal"] = meta_storage_disk_total;
  brief_metrics["MetaStorageDiskUsed"] = meta_storage_disk_used;
  brief_metrics["MetaStorageDiskUtilization"] = meta_storage_disk_utilization;
  results["brief_metrics"] = brief_metrics;

  cnetpp::http::HttpResponse response;
  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

}  // namespace dancenn

