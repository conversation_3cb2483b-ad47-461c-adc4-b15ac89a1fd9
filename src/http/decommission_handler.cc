// Copyright 2021 Mu <PERSON> <<EMAIL>>

#include "http/decommission_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/uri.h>
#include <glog/logging.h>

#include "base/string_utils.h"

namespace dancenn {

cnetpp::http::HttpResponse DecommissionHandler::Handle(
    const cnetpp::http::HttpRequest &request) {
  cnetpp::base::Uri uri;
  CHECK(uri.ParseUriPath(request.uri()));
  auto http_status = HttpStatusCode::kBadRequest;
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());
  std::ostringstream os;
  auto param_iter = param_map.find("dn");
  if (param_iter == param_map.end()) {
    http_status = HttpStatusCode::kBadRequest;
    os << "Expected param: dn=dn_id";
  } else {
    bool force = false;
    {
      auto param_iter = param_map.find("force");
      if (param_iter == param_map.end()) {
        force = false;
      } else {
        if (param_iter->second == "1" || param_iter->second == "true") {
          force = true;
        }
      }
    }

    int64_t dn;
    bool ret = StringUtils::StringToInt64(
        param_iter->second.c_str(), param_iter->second.size(), &dn);
    if (!ret) {
        http_status = HttpStatusCode::kBadRequest;
        os << "Failed to parse dn id";
    } else {
      auto s = datanode_manager_->SetDecommissioned(dn, force);
      if (s.IsOK()) {
        http_status = HttpStatusCode::kOk;
        os << "SetDecommissioned success";
      } else {
        http_status = HttpStatusCode::kInternalServerError;
        os << "SetDecommissioned failed " << s.ToString();
      }
    }
  }
  auto body = os.str();
  cnetpp::http::HttpResponse response;
  response.set_status(http_status);
  response.SetHttpHeader("Content-Length", std::to_string(body.size()));
  response.SetHttpHeader("Content-Type", "application/text");
  response.set_http_body(body);
  return response;
}

}  // namespace dancenn
