// Copyright 2018 <PERSON><PERSON> Huang <<EMAIL>>

#include "http/dancenn_admin_handler.h"

#include <absl/strings/ascii.h>
#include <absl/strings/str_split.h>
#include <cnetpp/base/uri.h>
#include <jemalloc/jemalloc.h>

#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/constants.h"
#include "base/path_util.h"
#include "base/string_utils.h"

DECLARE_string(namespace_meta_storage_ckpt_path);
DECLARE_bool(az_monitor_enable);

namespace dancenn {

DancennAdminHandler::DancennAdminHandler(std::shared_ptr<AccNamespace> acc_ns,
                                         std::shared_ptr<NameSpace> ns,
                                         std::shared_ptr<BlockManager> bm,
                                         std::shared_ptr<Ufs> ufs)
    : acc_ns_(std::move(acc_ns)),
      ns_(std::move(ns)),
      bm_(std::move(bm)),
      ufs_(std::move(ufs)) {
  table_ = {
      {"purge_logs", &DancennAdminHandler::PurgeLogs},
      {"purge_memory", &DancennAdminHandler::PurgeMemory},
      {"debug_memory", &DancennAdminHandler::DebugMemory},
      {"get_last_ckpt_id", &DancennAdminHandler::GetLastCkptTxId},
      {"compact_deletion", &DancennAdminHandler::ForceCompactDeletion},
      {"compact_scr", &DancennAdminHandler::ForceCompactSCR},
      {"compact_all", &DancennAdminHandler::ForceCompactAll},
      {"stack_info", &DancennAdminHandler::GetStackInfo},
      {"checkpoint", &DancennAdminHandler::CreateCheckpoint},
      {"storage_policy", &DancennAdminHandler::SetStoragePolicy},
      {"replica_policy", &DancennAdminHandler::ManageReplicaPolicy},
      {"read_policy", &DancennAdminHandler::ManageReadPolicy},
      {"upload_policy", &DancennAdminHandler::ManageUploadPolicy},
      {"dir_policy", &DancennAdminHandler::ManageDirPolicy},
      {"recycle_policy", &DancennAdminHandler::SetRecyclePolicy},
      {"mount_point", &DancennAdminHandler::SetMountPoint},
      {"refresh_datanode_machine_info",
       &DancennAdminHandler::RefreshDatanodeMachineInfo},
      {"set_az_blacklist", &DancennAdminHandler::SetAZBlacklist},
      {"get_az_blacklist", &DancennAdminHandler::GetAZBlacklist},
      {"fbr", &DancennAdminHandler::TriggerFullBlockReport},
      {"persist_file", &DancennAdminHandler::PersistFile},
      {"merge_blocks", &DancennAdminHandler::MergeBlocks},
      {"enter_destroy", &DancennAdminHandler::EnterDestroyState},
      {"exit_destroy", &DancennAdminHandler::ExitDestroyState},
      {"enter_safemode", &DancennAdminHandler::EnterSafeMode},
      {"leave_safemode", &DancennAdminHandler::LeaveSafeMode},
      {"ufs_trigger_upload", &DancennAdminHandler::UfsTriggerUpload},
      {"pin", &DancennAdminHandler::Pin},
      {"remove_block_storage", &DancennAdminHandler::RemoveBlockStorage},
      {"sync", &DancennAdminHandler::DoSync},
      {"free", &DancennAdminHandler::DoFree},
      {"add_block_id_and_gs", &DancennAdminHandler::AddBlockIdAndGs},
      {"run_mkdir_sync_task", &DancennAdminHandler::RunMkdirSyncTask},
      {"pre_ha_switch", &DancennAdminHandler::PreTransition},
      {"exit_pre_ha_switch", &DancennAdminHandler::ExitPreTransition},
  };
}

cnetpp::http::HttpResponse DancennAdminHandler::Handle(
    const cnetpp::http::HttpRequest &request) {
  cnetpp::base::Uri uri;
  CHECK(uri.ParseUriPath(request.uri()));
  auto http_status = HttpStatusCode::kBadRequest;
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());
  std::ostringstream os;

  auto cmd_iter = param_map.find("cmd");
  if (cmd_iter == param_map.end()) {
    os << "cmd param not found";
    http_status = HttpStatusCode::kBadRequest;
  } else {
    auto iter = table_.find(cmd_iter->second);
    if (iter != table_.end()) {
      auto func = iter->second;
      http_status = (this->*func)(param_map, os);
    } else {
      os << "cmd:" << cmd_iter->second << "not found";
    }
  }

  auto body = os.str();
  cnetpp::http::HttpResponse response;
  response.set_status(http_status);
  response.SetHttpHeader("Content-Length", std::to_string(body.size()));
  response.SetHttpHeader("Content-Type", "application/text");
  response.set_http_body(body);
  return response;
}

HttpStatusCode DancennAdminHandler::PurgeLogs(const ParamMap& params,
                                              std::ostringstream& os) {
  auto iter = params.find("txid");
  int64_t txid = 0;
  if (iter != params.end()) {
    txid = std::stoll(iter->second);
  }
  auto s = ns_->PurgeLogsOlderThan(txid);

  if (s.HasException()) {
    os << s.ToString();
    return HttpStatusCode::kForbidden;
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::PurgeMemory(const ParamMap& params,
                                                std::ostringstream& os) {
#if defined(OS_LINUX)
  char tmp[32];
  int narenas = MALLCTL_ARENAS_ALL;
  sprintf(tmp, "arena.%d.purge", narenas);
  LOG(WARNING) << "purge memory start: " << tmp << "...";
  mallctl(tmp, NULL, 0, NULL, 0);
  LOG(WARNING) << "purge memory finish...";
  return HttpStatusCode::kOk;
#else
  os << "Purge memory does not support on non-linux platform.";
  return HttpStatusCode::kForbidden;
#endif
}

HttpStatusCode DancennAdminHandler::DebugMemory(const ParamMap& params,
                                                std::ostringstream& os) {
#if defined(OS_LINUX)
  const char* ptr = getenv("MALLOC_CONF");
  if (ptr == nullptr) {
    os << "Debug memory should start dancenn with MALLOC_CONF=" << kMallocConf;
    return HttpStatusCode::kForbidden;
  }

  LOG(WARNING) << "Dump Memory start...";
  int err = mallctl("prof.dump", NULL, NULL, NULL, 0);
  LOG(WARNING) << "Dump Memory finish...";
  if (err) {
    if (err == ENOENT) {
      os << "Debug memory used with 'jemalloc build with --enable-prof'";
    } else {
      os << "error: " << err << " # " << strerror(err);
    }
    return HttpStatusCode::kForbidden;
  }

  os << kMallocConf << std::endl << "Memory Dump OK";
  return HttpStatusCode::kOk;
#else
  os << "Debug memory does not support on non-linux platform.";
  return HttpStatusCode::kForbidden;
#endif
}

HttpStatusCode DancennAdminHandler::GetLastCkptTxId(const ParamMap& params,
                                                    std::ostringstream& os) {
  auto last_ckpt_id = ns_->GetLastCkptTxId();
  os << last_ckpt_id;
  LOG(INFO) << "GetLastCkptTxId: " << last_ckpt_id;
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::GetStackInfo(
    const ParamMap& params,
    std::ostringstream& os) {
  std::string stack_info;
  if (ns_->GetStackInfo(&stack_info)) {
    os << stack_info;
  }
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ForceCompactDeletion(
    const ParamMap& params,
    std::ostringstream& os) {
  if (ns_->ForceCompactDeletion()) {
    os << "OK";
  } else {
    os << "Already In Progress";
  }
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ForceCompactSCR(const ParamMap& params,
                                                    std::ostringstream& os) {
  os << "Now start to compact StorageClassReport CF, waiting...";
  ns_->ForceCompactStorageClassReport();
  os << "Done";
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ForceCompactAll(const ParamMap& params,
                                                    std::ostringstream& os) {
  if (ns_->ForceCompactAll()) {
    os << "OK";
  } else {
    os << "Already In Progress";
  }
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::CreateCheckpoint(const ParamMap& params,
                                                     std::ostringstream& os) {
  auto iter = params.find("path");
  std::string checkpoint_path;
  if (iter != params.end()) {
    checkpoint_path = iter->second;
  } else {
    checkpoint_path = FLAGS_namespace_meta_storage_ckpt_path
        + "/ckpt." + std::to_string(
            std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count());
  }
  auto status = ns_->CreateCheckpoint(checkpoint_path);
  os << "Create Checkpoint path: " << checkpoint_path << " status: "
     << status.ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::SetStoragePolicy(const ParamMap& params,
                                                     std::ostringstream& os) {
  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field."
       << " example: /user/xiongmu/test\n";
    return HttpStatusCode::kOk;
  }

  // policy
  int policy_id = 0;
  std::string policy_str;
  if (!GetStringFromParams(params, "policy", &policy_str)) {
    os << "Need `policy` field."
       << " example: ALL_SSD\n";
    return HttpStatusCode::kOk;
  }

  // do
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncSetStoragePolicy(
      normalized_path, policy_str, UserGroupInfo(), &rpc_done);
  rpc_done.Await();

  os << "RpcDone:\n" << rpc_done.status().ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ManageReplicaPolicy(
    const ParamMap& params,
    std::ostringstream& os) {
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " example: [set|remove]\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  std::string normalized_path;
  if (action != "ls") {
    if (!GetStringFromParams(params, "path", &path)) {
      os << "Need `path` field."
         << " example: [/user/xiongmu/test]\n";
      return HttpStatusCode::kOk;
    }
    if (!NormalizePath(path, "", &normalized_path)) {
      os << "Invalid `path` field."
         << " example: /user/xiongmu/test\n";
      return HttpStatusCode::kOk;
    }
  }

  if (action == "set") {
    // policy
    int policy_id = 0;
    std::string policy_str;
    if (!GetStringFromParams(params, "policy", &policy_str)) {
      os << "Need `policy` field."
         << " example: centralize\n";
      return HttpStatusCode::kOk;
    }

    if (policy_str == "Centralize" || policy_str == "centralize" ||
        policy_str == "CENTRALIZE") {
      policy_id = kCentralizePolicy;
    } else if (policy_str == "Distribute" || policy_str == "distribute" ||
               policy_str == "DISTRIBUTE") {
      policy_id = kDistributePolicy;
    } else {
      os << "Invalid `policy` field."
         << " example: centralize\n";
      return HttpStatusCode::kOk;
    }

    // local_switch_target
    uint32_t local_switch_target = 0;
    if (!GetUInt32FromParams(
            params, "local_switch_target", &local_switch_target)) {
      os << "Invalid `local_switch_target` field."
         << " example: 2\n";
      return HttpStatusCode::kOk;
    }

    // other_switch_target
    uint32_t other_switch_target = 0;
    if (!GetUInt32FromParams(
            params, "other_switch_target", &other_switch_target)) {
      os << "Invalid `other_switch_target` field."
         << " example: 2\n";
      return HttpStatusCode::kOk;
    }

    // dc
    std::string dc_str;
    if (!GetStringFromParams(params, "dc", &dc_str)) {
      os << "Need `dc` field."
         << " example: LF,HL\n";
      return HttpStatusCode::kOk;
    }

    ReplicaPolicy policy;
    policy.set_distributed(policy_id == kDistributePolicy);
    auto dcs = StringUtils::SplitByChars(dc_str, ",");
    for (auto dc : dcs) {
      policy.add_dc(dc);
    }
    policy.set_local_switch_target(local_switch_target);
    policy.set_other_switch_target(other_switch_target);

    // do
    SynchronizedRpcClosure rpc_done;
    ns_->AsyncSetReplicaPolicy(normalized_path, policy, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "remove") {
    SynchronizedRpcClosure rpc_done;
    ns_->AsyncRemoveReplicaPolicy(normalized_path, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "get") {
    cloudfs::GetDirPolicyRequestProto request;
    cloudfs::GetDirPolicyResponseProto response;

    auto s = ns_->GetDirPolicy(normalized_path, &request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    os << " ReplicaPolicy=" << response.replica_policy().ShortDebugString()
       << "\n";
    os << "\n";
    return HttpStatusCode::kOk;
  } else if (action == "ls") {
    ::cloudfs::ListDirPolicyRequestProto request;
    ::cloudfs::ListDirPolicyResponseProto response;

    auto s = ns_->ListDirPolicy(&request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    for (auto entry : response.policy_list()) {
      if (entry.has_replica_policy()) {
        os << "path=" << entry.path() << "\n"
           << "  ReplicaPolicy=" << entry.replica_policy().ShortDebugString()
           << "\n";
      }
    }
    os << "\n";
    return HttpStatusCode::kOk;
  } else {
    os << "action should be [set|remove]\n";
    return HttpStatusCode::kOk;
  }
}

HttpStatusCode DancennAdminHandler::ManageReadPolicy(const ParamMap& params,
                                                     std::ostringstream& os) {
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  std::string normalized_path;
  if (action != "ls") {
    if (!GetStringFromParams(params, "path", &path)) {
      os << "Need `path` field."
         << " example: [/user/xiongmu/test]\n";
      return HttpStatusCode::kOk;
    }
    if (!NormalizePath(path, "", &normalized_path)) {
      os << "Invalid `path` field."
         << " example: /user/xiongmu/test\n";
      return HttpStatusCode::kOk;
    }
  }

  // do
  if (action == "set") {
    ReadPolicy policy;

    // localdconly
    bool localdconly = false;
    if (!GetBoolFromParams(params, "localdconly", &localdconly)){
      os << "Need `localdconly` field."
         << " Available values are: [1, 0, true, false]\n";
      return HttpStatusCode::kOk;
    }
    policy.set_localdconly(localdconly);

    // blacklistdc
    std::string blacklistdc;
    if (!GetStringFromParams(params, "blacklistdc", &blacklistdc)) {
      os << "Need `blacklistdc` field."
         << " example: LF,HL\n";
      return HttpStatusCode::kOk;
    }
    std::vector<std::string> dcs =
        cnetpp::base::StringUtils::SplitByChars(blacklistdc, ",");
    for (auto& dc : dcs) {
      if (!dc.empty()) {
        policy.add_blacklistdc(dc);
      }
    }

    // switch prefer
    std::string read_switch_policy;
    if (!GetStringFromParams(
            params, "read_switch_policy", &read_switch_policy)) {
      // pass
    } else {
      if (read_switch_policy == "local") {
        policy.set_read_switch_policy(cloudfs::ReadPolicyProto::PREFER_LOCAL);
      } else if (read_switch_policy == "cached") {
        policy.set_read_switch_policy(cloudfs::ReadPolicyProto::PREFER_CACHED);
      } else {
        os << "Invalid `read_switch_policy` field."
           << " example: [local|cached]\n";
        return HttpStatusCode::kOk;
      }
    }

    SynchronizedRpcClosure rpc_done;
    ns_->AsyncSetReadPolicy(normalized_path, policy, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "remove"){
    SynchronizedRpcClosure rpc_done;
    ns_->AsyncRemoveReadPolicy(normalized_path, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "get") {
    cloudfs::GetDirPolicyRequestProto request;
    cloudfs::GetDirPolicyResponseProto response;

    auto s = ns_->GetDirPolicy(normalized_path, &request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    os << " ReplicaPolicy=" << response.read_policy().ShortDebugString()
       << "\n";
    os << "\n";
    return HttpStatusCode::kOk;
  } else if (action == "ls") {
    ::cloudfs::ListDirPolicyRequestProto request;
    ::cloudfs::ListDirPolicyResponseProto response;

    auto s = ns_->ListDirPolicy(&request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    for (auto entry : response.policy_list()) {
      if (entry.has_read_policy()) {
        os << "path=" << entry.path() << "\n";
        os << "  ReadPolicy=" << entry.read_policy().ShortDebugString();
      }
    }
    os << "\n";
    return HttpStatusCode::kOk;
  } else {
    os << "Invalid `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }
}

HttpStatusCode DancennAdminHandler::ManageUploadPolicy(
    const dancenn::ParamMap& params,
    std::ostringstream& os) {
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  std::string normalized_path;
  if (action != "ls") {
    if (!GetStringFromParams(params, "path", &path)) {
      os << "Need `path` field."
         << " example: [/user/xiongmu/test]\n";
      return HttpStatusCode::kOk;
    }
    if (!NormalizePath(path, "", &normalized_path)) {
      os << "Invalid `path` field."
         << " example: /user/xiongmu/test\n";
      return HttpStatusCode::kOk;
    }
  }

  // sync interval
  int64_t sync_interval = 0;
  if (!GetInt64FromParams(params, "sync_interval", &sync_interval)) {
    LOG(INFO) << "SetDirPolicy on path " << normalized_path
              << ", sync interval not set, default to 0";
  }

  if (sync_interval < -1 ||
      sync_interval > std::numeric_limits<int32_t>::max()) {
    os << "Invalid sync interval " << sync_interval;
    return HttpStatusCode::kBadRequest;
  }

  // do
  if (action == "set") {
    UploadPolicy policy;

    // upload_interval_ms
    int64_t upload_interval_ms = 0;
    if (!GetInt64FromParams(
            params, "upload_interval_ms", &upload_interval_ms)) {
      os << "Invalid upload_interval_ms";
      return HttpStatusCode::kBadRequest;
    }
    policy.set_upload_interval_ms(upload_interval_ms);

    ::cloudfs::SetDirPolicyRequestProto request;
    request.mutable_upload_policy()->CopyFrom(policy);
    SynchronizedRpcClosure rpc_done;
    auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_fs_info.set_syncinterval(static_cast<int32_t>(sync_interval));
    os << "acc_fs_info=" << acc_fs_info.ShortDebugString() << "\n";
    acc_ns_->AsyncSetDirPolicy(
        normalized_path, &request, acc_fs_info, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "remove") {
    ::cloudfs::RemoveDirPolicyRequestProto request;
    request.set_remove_upload_policy(true);
    auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_fs_info.set_syncinterval(static_cast<int32_t>(sync_interval));
    SynchronizedRpcClosure rpc_done;
    acc_ns_->AsyncRemoveDirPolicy(
        normalized_path, &request, acc_fs_info, &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "get") {
    cloudfs::GetDirPolicyRequestProto request;
    cloudfs::GetDirPolicyResponseProto response;
    auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_fs_info.set_syncinterval(static_cast<int32_t>(sync_interval));
    auto s = acc_ns_->GetDirPolicy(
        normalized_path, &request, &response, acc_fs_info);

    os << "RpcDone: " << s.ToString() << "\n";
    os << "  UploadPolicy=" << response.upload_policy().ShortDebugString()
       << "\n";
    os << "\n";
    return HttpStatusCode::kOk;
  } else if (action == "ls") {
    ::cloudfs::ListDirPolicyRequestProto request;
    ::cloudfs::ListDirPolicyResponseProto response;

    auto s = acc_ns_->ListDirPolicy(&request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    for (auto entry : response.policy_list()) {
      if (entry.has_upload_policy()) {
        os << "path=" << entry.path() << "\n";
        os << "  UploadPolicy=" << entry.upload_policy().ShortDebugString()
           << "\n";
      }
    }
    os << "\n";
    return HttpStatusCode::kOk;
  } else {
    os << "Invalid `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }
}

HttpStatusCode DancennAdminHandler::ManageDirPolicy(
    const dancenn::ParamMap& params,
    std::ostringstream& os) {
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " Available values are [get, ls]\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  std::string normalized_path;
  if (action != "ls") {
    if (!GetStringFromParams(params, "path", &path)) {
      os << "Need `path` field."
         << " example: [/user/xiongmu/test]\n";
      return HttpStatusCode::kOk;
    }
    if (!NormalizePath(path, "", &normalized_path)) {
      os << "Invalid `path` field."
         << " example: /user/xiongmu/test\n";
      return HttpStatusCode::kOk;
    }
  }

  // do
  if (action == "get") {
    cloudfs::GetDirPolicyRequestProto request;
    cloudfs::GetDirPolicyResponseProto response;

    auto s = ns_->GetDirPolicy(normalized_path, &request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    os << "  ReplicaPolicy=" << response.replica_policy().ShortDebugString()
       << "\n";
    os << "  ReadPolicy=" << response.read_policy().ShortDebugString() << "\n";
    os << "  UploadPolicy=" << response.upload_policy().ShortDebugString()
       << "\n";
    os << "\n";
    return HttpStatusCode::kOk;
  } else if (action == "ls") {
    ::cloudfs::ListDirPolicyRequestProto request;
    ::cloudfs::ListDirPolicyResponseProto response;

    auto s = ns_->ListDirPolicy(&request, &response);

    os << "RpcDone: " << s.ToString() << "\n";
    for (auto entry : response.policy_list()) {
      os << "path=" << entry.path() << "\n";
      os << "  ReplicaPolicy=" << entry.replica_policy().ShortDebugString()
         << "\n";
      os << "  ReadPolicy=" << entry.read_policy().ShortDebugString() << "\n";
      os << "  UploadPolicy=" << entry.upload_policy().ShortDebugString()
         << "\n";
      os << "\n";
    }
    return HttpStatusCode::kOk;
  } else {
    os << "Invalid `action` field."
       << " Available values are [ls, get]\n";
    return HttpStatusCode::kOk;
  }
}

HttpStatusCode DancennAdminHandler::SetRecyclePolicy(const ParamMap& params,
                                                     std::ostringstream& os) {
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field."
       << " example: /user/xiongmu/test\n";
    return HttpStatusCode::kOk;
  }

  // do
  if (action == "set") {
    RecyclePolicy policy;

    // recycle_time
    uint32_t recycle_time;
    if (!GetUInt32FromParams(params, "recycle_time", &recycle_time)) {
      os << "Need `recycle_time` field."
         << " Available values are: [0, 86400*365)\n";
      return HttpStatusCode::kOk;
    }
    if (recycle_time > 86400 * 365) {
      os << "Invalid `recycle_time` field."
         << " Available values are: [0, 86400*365)\n";
      return HttpStatusCode::kOk;
    }
    policy.set_recycletime(recycle_time);

    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(kRecyclePolicyXAttr, policy.SerializeAsString(), &policy_xattr);
    SynchronizedRpcClosure rpc_done;
    ns_->AsyncSetXAttr(normalized_path, policy_xattr, true, true, LogRpcInfo(),
                       &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else if (action == "remove") {
    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(kRecyclePolicyXAttr, "", &policy_xattr);

    SynchronizedRpcClosure rpc_done;
    ns_->AsyncRemoveXAttr(normalized_path, policy_xattr, LogRpcInfo(),
                          UserGroupInfo("root", "supergroup"),
                          &rpc_done);
    rpc_done.Await();

    os << "RpcDone:\n" << rpc_done.status().ToString();
    return HttpStatusCode::kOk;
  } else {
    os << "Invalid `action` field."
       << " Available values are [set, remove]\n";
    return HttpStatusCode::kOk;
  }
}

HttpStatusCode DancennAdminHandler::SetMountPoint(const ParamMap& params,
                                                  std::ostringstream& os) {
  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field."
       << " example: /user/xiongmu/test\n";
    return HttpStatusCode::kOk;
  }

  // allow_entries
  std::string allow_entries_str;
  if (!GetStringFromParams(params, "allow_entries", &allow_entries_str)) {
    os << "Need `allow_entries` field."
       << " example: [a,b,c]\n";
    return HttpStatusCode::kOk;
  }
  std::vector<std::string> allow_entries_vec;
  if (!allow_entries_str.empty()) {
    allow_entries_vec = StringUtils::SplitByChars(allow_entries_str, ",");
  }
  std::set<std::string> allow_entries_set(allow_entries_vec.begin(),
                                          allow_entries_vec.end());

  // deny_entries
  std::string deny_entries_str;
  if (!GetStringFromParams(params, "deny_entries", &deny_entries_str)) {
    os << "Need `deny_entries` field."
       << " example: [a,b,c]\n";
    return HttpStatusCode::kOk;
  }
  std::vector<std::string> deny_entries_vec;
  if (!deny_entries_str.empty()) {
    deny_entries_vec = StringUtils::SplitByChars(deny_entries_str, ",");
  }
  std::set<std::string> deny_entries_set(deny_entries_vec.begin(),
                                         deny_entries_vec.end());

  // require_path_empty_dir
  bool require_path_empty_dir = false;  // Default value
  GetBoolFromParams(params, "require_path_empty_dir", &require_path_empty_dir);

  // create_dir_if_missing
  bool create_dir_if_missing = false;  // Default value
  PermissionStatus ps;
  if (GetBoolFromParams(params, "create_dir_if_missing", &create_dir_if_missing) &&
      create_dir_if_missing) {
    // If `create_dir_if_missing` is true,
    // then `permission`, `user_name`, `group_name` are required
    uint32_t permission;
    if (!GetUInt32FromParams(params, "permission", &permission)) {
      os << "Need `permission` field."
         << " example: [755]\n";
      return HttpStatusCode::kOk;
    }
    ps.set_permission(permission);

    std::string user_name;
    if (!GetStringFromParams(params, "user_name", &user_name)) {
      os << "Need `user_name` field."
         << " example: [tiger]\n";
      return HttpStatusCode::kOk;
    }
    ps.set_username(user_name);

    std::string group_name;
    if (!GetStringFromParams(params, "group_name", &group_name)) {
      os << "Need `group_name` field."
         << " example: [tiger]\n";
      return HttpStatusCode::kOk;
    }
    ps.set_groupname(group_name);

    UserGroupInfo ugi(ps.username(), ps.groupname());
    GetFileInfoResponseProto resp;
    auto st = ns_->GetFileInfo(normalized_path,
                               NetworkLocationInfo(),
                               false,
                               false,
                               &resp,
                               ugi,
                               nullptr);
    if (!st.IsOK()) {
      os << "Error when checking " << normalized_path
         << ", status: " << st.ToString();
      return HttpStatusCode::kOk;
    }
    if (!resp.has_fs()) {
      // target not exist
      SynchronizedRpcClosure done;
      ns_->AsyncMkDirs(normalized_path, ps, ugi, true, false, nullptr, LogRpcInfo(), &done);
      done.Await();
      if (!done.status().IsOK()) {
        st = done.status();
        os << "Error when preparing directory " << normalized_path
           << ", status: " << st.ToString();
        return HttpStatusCode::kOk;
      }
    }
  }

  SynchronizedRpcClosure rpc_done;
  ns_->AsyncSetMountPointAttr(normalized_path,
                              allow_entries_set,
                              deny_entries_set,
                              require_path_empty_dir,
                              ps,
                              &rpc_done);
  rpc_done.Await();

  os << "RpcDone:\n" << rpc_done.status().ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::RefreshDatanodeMachineInfo(
    const ParamMap& params,
    std::ostringstream& os) {
  ns_->datanode_manager()->RefreshDatanodeMachineInfo();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::SetAZBlacklist(const ParamMap& params,
                                                   std::ostringstream& os) {
  if (!FLAGS_az_monitor_enable) {
    return HttpStatusCode::kForbidden;
  }

  uint64_t inode_id = kInvalidINodeId;
  Status s = Status::OK();

  std::string azs_str;
  if (!GetStringFromParams(params, "list", &azs_str)) {
    os << "Need `list` field, AZ name seperated by comma."
       << " example: [cn-shanghai-a,cn-shanghai-b]\n";
    return HttpStatusCode::kOk;
  }
  std::transform(azs_str.begin(), azs_str.end(), azs_str.begin(),
      [](unsigned char c){ return std::toupper(c); });

  std::set<std::string> azs = AZStr2Set(azs_str);
  std::string azs_str_normalized = AZSet2Str(azs);

  SynchronizedRpcClosure done;
  ns_->AsyncSetAZBlacklist(azs_str_normalized, &done);
  done.Await();
  auto& st = done.status();
  if (!st.IsOK()) {
    os << "Update AZ blacklist failed, err: " << st.ToString();
    return HttpStatusCode::kInternalServerError;
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::GetAZBlacklist(const ParamMap& params,
                                                   std::ostringstream& os) {
  if (!FLAGS_az_monitor_enable) {
    return HttpStatusCode::kForbidden;
  }

  if (!ns_->az_monitor()) {
    return HttpStatusCode::kForbidden;
  }
  auto blacklist_expected = ns_->az_monitor()->GetAZBlacklistExpected();
  auto blacklist_effective = ns_->az_monitor()->GetAZBlacklistEffective();

  cnetpp::base::Object results;
  cnetpp::base::Array expected, effective;
  for (auto& az : blacklist_expected) {
    expected.Append(cnetpp::base::Value(az));
  }
  for (auto& az : blacklist_effective) {
    effective.Append(cnetpp::base::Value(az));
  }
  results["expected"] = expected;
  results["effective"] = effective;

  std::string results_str;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &results_str);
  std::transform(results_str.begin(), results_str.end(), results_str.begin(),
      [](unsigned char c){ return std::tolower(c); });
  os << results_str;

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::TriggerFullBlockReport(
    const ParamMap& params,
    std::ostringstream& os) {
  std::string dn_id;
  if (!GetStringFromParams(params, "dn", &dn_id)) {
    os << "Need `dn` field. "
       << " Available values are [all, 1, 2, 3...]";
    return HttpStatusCode::kBadRequest;
  }

  if (dn_id == "all") {
    bm_->TriggerAllBlockReport();
    return HttpStatusCode::kOk;
  }

  bool fast = false;
  GetBoolFromParams(params, "fast", &fast);

  int id = kInvalidDatanodeID;
  if (StringUtils::StringToInt32(dn_id.c_str(), dn_id.size(), &id)) {
    if (bm_->TriggerBlockReport(id, fast)) {
      return HttpStatusCode::kOk;
    } else {
      os << "dn " << id << " not found";
      return HttpStatusCode::kBadRequest;
    }
  }

  os << "Invalid value for `dn`: " << dn_id
     << ". Available values are [all, 1, 2, 3...]";
  return HttpStatusCode::kBadRequest;
}

HttpStatusCode DancennAdminHandler::PersistFile(const ParamMap& params,
                                                std::ostringstream& os) {
  if (ufs_ == nullptr) {
    os << "no ufs exist";
    return HttpStatusCode::kOk;
  }
  int64_t id = kInvalidINodeId;
  if (!GetInt64FromParams(params, "inode_id", &id)) {
    os << "Invalid inode id";
    return HttpStatusCode::kBadRequest;
  }
  std::string key;
  GetStringFromParams(params, "key", &key);
  std::string uploadId;
  GetStringFromParams(params, "upload_id", &uploadId);

  if (uploadId.empty() ^ key.empty()) {
    return HttpStatusCode::kBadRequest;
  }

  bool ignored;

  RPC_SW_CTX_INIT_FORCE(
      rpc_sw_ctx, "[PersistFile][HTTP]", "inode_id=" + std::to_string(id));
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");
  ns_->PersistUfsFile(
      ufs_, id, key, uploadId, true, true, &ignored, rpc_sw_ctx.get());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "end");

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::MergeBlocks(const ParamMap& params,
                                                std::ostringstream& os) {
  uint64_t inode_id = kInvalidINodeId;
  Status s = Status::OK();

  for (auto& param : params) {
    if (param.first == "inode_id") {
      int64_t ret = 0;
      if (StringUtils::StringToInt64(
              param.second.c_str(), param.second.size(), &ret) &&
          ret > 0) {
        inode_id = ret;
      }
    }
  }

  if (inode_id == kInvalidINodeId) {
    os << "Invalid inode id";
    return HttpStatusCode::kBadRequest;
  }

  s = ns_->MergeBlocks(inode_id);

  if (!s.IsOK()) {
    os << "Merge failed";
    return HttpStatusCode::kInternalServerError;
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::EnterDestroyState(const ParamMap& params,
                                                      std::ostringstream& os) {
  ns_->ha_state()->EnterDestroyState();
  os << "OK, EnterDestroyState";
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ExitDestroyState(const ParamMap& params,
                                                     std::ostringstream& os) {
  ns_->ha_state()->ExitDestroyState();
  os << "OK, ExitDestroyState";
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::SwitchNonHAActiveToHAActive(
    const ParamMap& params,
    std::ostringstream& os) {
  Status s = ns_->ha_state()->SwitchNonHAActiveToHAActive();
  if (s.IsOK()) {
    os << "OK, SwitchNonHAActiveToHAActive";
    return HttpStatusCode::kOk;
  }
  os << s.message();
  return HttpStatusCode::kInternalServerError;
}

HttpStatusCode DancennAdminHandler::SwitchHAActiveToNonHAActive(
    const ParamMap& params,
    std::ostringstream& os) {
  Status s = ns_->ha_state()->SwitchHAActiveToNonHAActive();
  if (s.IsOK()) {
    os << "OK, SwitchHAActiveToNonHAActive";
    return HttpStatusCode::kOk;
  }
  os << s.message();
  return HttpStatusCode::kInternalServerError;
}

HttpStatusCode DancennAdminHandler::SwitchHAStandbyToNonHAActive(
    const ParamMap& params,
    std::ostringstream& os) {
  Status s = ns_->ha_state()->SwitchHAStandbyToNonHAActive();
  if (s.IsOK()) {
    return HttpStatusCode::kOk;
  }
  os << s.message();
  return HttpStatusCode::kInternalServerError;
}
HttpStatusCode DancennAdminHandler::EnterSafeMode(const ParamMap& params,
                                                  std::ostringstream& os) {
  auto action = ::cloudfs::SafeModeActionProto::SAFEMODE_ENTER;
  auto op = OperationsCategory::kRead;
  auto s = ns_->safemode()->SetSafeMode(action, op);
  LOG(INFO) << "EnterSafeMode: result=" << s.ToString();
  os << s.ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::LeaveSafeMode(const ParamMap& params,
                                                  std::ostringstream& os) {
  auto action = ::cloudfs::SafeModeActionProto::SAFEMODE_LEAVE;
  auto op = OperationsCategory::kRead;
  auto s = ns_->safemode()->SetSafeMode(action, op);
  LOG(INFO) << "LeaveSafeMode: result=" << s.ToString();
  os << s.ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::UfsTriggerUpload(const ParamMap& params,
                                                     std::ostringstream& os) {
  // inode_id
  int64_t inode_id = kInvalidINodeId;
  if (!GetInt64FromParams(params, "inode_id", &inode_id)) {
    os << "Invalid inode_id";
    return HttpStatusCode::kBadRequest;
  }

  if (ns_->ufs_env() == nullptr || ns_->ufs_env()->get_only_ufs() == nullptr) {
    os << "No Ufs";
    return HttpStatusCode::kBadRequest;
  }

  auto ufs = ns_->ufs_env()->get_only_ufs();
  auto s = ns_->TriggerUploadUfsFile(ufs, inode_id);

  os << s.ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::Pin(const ParamMap& params,
                                        std::ostringstream& os) {
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/foo/bar]\n";
    return HttpStatusCode::kBadRequest;
  }
  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field."
       << " example: /user/xiongmu/test\n";
    return HttpStatusCode::kBadRequest;
  }

  bool unpin = false;
  if (!GetBoolFromParams(params, "unpin", &unpin)) {
    // ignored
  }

  int64_t ttl = -1;
  if (!GetInt64FromParams(params, "ttl", &ttl)) {
    // ignored
  }

  bool recursive = false;
  if (!GetBoolFromParams(params, "recursive", &recursive)) {
    // ignored
  }

  int64_t sync_interval = 0;
  if (!GetInt64FromParams(
          params, "upload_interval_ms", &sync_interval)) {
    // ignored
  }
  if (sync_interval < -1 ||
      sync_interval > std::numeric_limits<int32_t>::max()) {
    os << "Invalid sync interval " << sync_interval;
    return HttpStatusCode::kBadRequest;
  }

  PinRequestProto req;
  req.set_src(path);
  req.set_ttl(ttl);
  req.set_recursive(recursive);
  req.set_unpin(unpin);
  PinResponseProto res;
  SynchronizedRpcClosure rpc_done;
  auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
  acc_fs_info.set_syncinterval(static_cast<int32_t>(sync_interval));
  os << "acc_fs_info=" << acc_fs_info.ShortDebugString() << "\n";
  acc_ns_->AsyncPin(path, &req, &res, acc_fs_info, LogRpcInfo(), &rpc_done);
  rpc_done.Await();
  os << "RpcDone:\n" << rpc_done.status().ToString();

  if (rpc_done.status().IsOK()) {
    return HttpStatusCode::kOk;
  } else {
    return HttpStatusCode::kInternalServerError;
  }
}

HttpStatusCode DancennAdminHandler::RemoveBlockStorage(const ParamMap& params,
                                                       std::ostringstream& os) {
  // block_id
  int64_t block_id = kInvalidBlockID;
  if (!GetInt64FromParams(params, "block_id", &block_id)) {
    os << "Invalid block_id";
    return HttpStatusCode::kBadRequest;
  }

  // dns_str
  std::string dns_str;
  if (!GetStringFromParams(params, "dns_str", &dns_str)) {
    os << "Invalid dns_str";
    return HttpStatusCode::kBadRequest;
  }

  std::unordered_set<std::string> dn_ips;
  if (dns_str == "all") {
    // pass
  } else {
    auto dn_ip_list = cnetpp::base::StringUtils::SplitByChars(dns_str, ",");
    for (const auto& dn_ip : dn_ip_list) {
      dn_ips.insert(dn_ip);
    }
  }

  // datanode_ip
  auto block_manager = ns_->block_manager();

  auto dns = block_manager->GetBlockStorageSafe(block_id);

  for (auto dn_id : dns) {
    auto dn = ns_->datanode_manager()->GetDatanodeFromId(dn_id);
    if (dn == nullptr) {
      os << "unknown dn_id=" << dn_id << "\n";
      continue;
    }

    auto dn_ip = dn->ip().ToString();
    if (dns_str == "all" || dn_ips.count(dn_ip) > 0) {
      auto res = block_manager->RemoveStorage4Http(block_id, dn_id);
      if (res) {
        os << "RemoveStorageInternal Success, block_id=" << block_id
           << " dn_ip=" << dn_ip << " dn_id=" << dn_id << "\n";
      } else {
        os << "RemoveStorageInternal Failed, block_id=" << block_id
           << " dn_ip=" << dn_ip << " dn_id=" << dn_id << "\n";
      }
    };
  }
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::DoSync(const ParamMap& params,
                                           std::ostringstream& os) {
  if (!NameSpace::IsAccMode() || acc_ns_ == nullptr) {
    os << "not acc mode\n";
    return HttpStatusCode::kOk;
  }

  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  os << "path=" << path << "\n";

  std::string normalized_path;
  if (!NormalizePath(path, "", &normalized_path)) {
    os << "Invalid `path` field."
       << " example: /user/xiongmu/test\n"
       << " path=" << path << "\n";
    return HttpStatusCode::kOk;
  }
  os << "normalized_path=" << normalized_path << "\n";

  auto perm = PermissionStatus();
  auto ugi = UserGroupInfo();
  auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
  os << "acc_fs_info=" << acc_fs_info.ShortDebugString() << "\n";

  std::string inner_path;
  Status s = acc_ns_->VerifyAccFsInfoGetInnerPath(
      normalized_path, acc_fs_info, &inner_path);
  if (!s.IsOK()) {
    os << "s=" << s.ToString() << "\n";
    return HttpStatusCode::kOk;
  }
  os << "inner_path=" << inner_path << "\n";

  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  os << "now_ts=" << now_ts << "\n";

  RPC_SW_CTX_INIT_FORCE(rpc_sw_ctx, "[ManualSync]", "path=" + normalized_path);

  acc_ns_->TriggerDirListingSync(
      normalized_path, inner_path, perm, ugi, acc_fs_info, now_ts, rpc_sw_ctx);

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::DoFree(const ParamMap& params,
                                           std::ostringstream& os) {
  // path
  std::string path;
  if (!GetStringFromParams(params, "path", &path)) {
    os << "Need `path` field."
       << " example: [/user/xiongmu/test]\n";
    return HttpStatusCode::kOk;
  }
  // metadata
  bool metadata = false;
  if (!GetBoolFromParams(params, "metadata", &metadata)) {
    os << "use default metadata=false\n";
  }
  // recursive
  bool recursive = false;
  if (!GetBoolFromParams(params, "recursive", &recursive)) {
    os << "use default recursive=false\n";
  }

  FreeRequestProto request;
  FreeResponseProto response;
  request.set_src(path);
  request.set_metadata(metadata);
  request.set_recursive(recursive);

  SynchronizedRpcClosure rpc_done;
  if (NameSpace::IsAccMode()) {
    if (!NameSpace::IsAccMode() || acc_ns_ == nullptr) {
      os << "not acc mode\n";
      return HttpStatusCode::kOk;
    }

    AccFsInfo acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_ns_->AsyncFree(path,
                       &request,
                       &response,
                       acc_fs_info,
                       UserGroupInfo(),
                       LogRpcInfo(),
                       &rpc_done);
  } else {
    ns_->AsyncFree(path, &request, &response, UserGroupInfo(), &rpc_done);
  }
  rpc_done.Await();

  os << "RpcDone:\n" << rpc_done.status().ToString();
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::AddBlockIdAndGs(const ParamMap& params,
                                                    std::ostringstream& os) {
  int64_t delta = 0;
  if (!GetInt64FromParams(params, "delta", &delta)) {
    os << "Invalid delta";
    return HttpStatusCode::kBadRequest;
  }
  if (delta <= 0 || delta > 1000000) {
    os << "Invalid delta, delta=" << delta;
    return HttpStatusCode::kOk;
  }

  uint64_t block_id = 0;
  uint64_t gsv2 = 0;
  auto s = ns_->NextBlockIdAndGSv2Loop(delta, &block_id, &gsv2);

  os << "RpcDone: " << s.ToString() << "\n";
  os << "last_block_id: " << block_id << "\n";
  os << "last_gs_v2: " << gsv2 << "\n";

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::RunMkdirSyncTask(const ParamMap& params,
                                                     std::ostringstream& os) {
  if (!NameSpace::IsAccMode() || acc_ns_ == nullptr) {
    os << "not acc mode\n";
    return HttpStatusCode::kOk;
  }
  // action
  std::string action;
  if (!GetStringFromParams(params, "action", &action)) {
    os << "Need `action` field."
       << " Available values are: [update, get]\n";
    return HttpStatusCode::kOk;
  }

  if (!ns_->ha_state()->IsActive()) {
    os << "not active";
    return HttpStatusCode::kOk;
  }

  if (action == "update") {
    // dry_run
    bool dry_run = false;
    if (!GetBoolFromParams(params, "dry_run", &dry_run)) {
      os << "Need `dry_run` field."
         << " Available values are: [1, 0, true, false]\n";
      return HttpStatusCode::kOk;
    }
    // should_run
    bool should_run = false;
    if (!GetBoolFromParams(params, "should_run", &should_run)) {
      os << "Need `should_run` field."
         << " Available values are: [1, 0, true, false]\n";
      return HttpStatusCode::kOk;
    }

    std::lock_guard<std::mutex> lock(mutex_);
    if (!mkdir_sync_task_scanner_) {
      mkdir_sync_task_scanner_ =
          std::make_shared<MkdirCreateInUfsScanner>(acc_ns_.get(), dry_run);
      mkdir_sync_task_task_id_ = ns_->AddMetaScannerTask(
          std::dynamic_pointer_cast<MetaScannerTaskBase>(
              mkdir_sync_task_scanner_));
    }
    mkdir_sync_task_scanner_->SetShouldRun(should_run);
    mkdir_sync_task_scanner_->SetDryRun(dry_run);
    if (mkdir_sync_task_scanner_) {
      os << "start mkdir_sync_task_scanner_="
         << mkdir_sync_task_scanner_->ToString() << "\n";
    } else {
      os << "ns_->AddMetaScannerTask failed\n";
    }
  } else if (action == "get") {
    std::lock_guard<std::mutex> lock(mutex_);
    if (mkdir_sync_task_scanner_) {
      os << "mkdir_sync_task_scanner_=" << mkdir_sync_task_scanner_->ToString()
         << "\n";
    } else {
      os << "no inited mkdir task\n";
    }
  } else {
    os << "unknown action, Available values are: [update, get]\n";
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::PreTransition(const ParamMap& params,
                                                  std::ostringstream& os) {
  bool add_lock = false;
  GetBoolFromParams(params, "add_lock", &add_lock);
  ns_->ha_state()->PreTransition(add_lock);

  os << R"({"msg":"success"})";
  return HttpStatusCode::kOk;
}

HttpStatusCode DancennAdminHandler::ExitPreTransition(const ParamMap& params,
                                                      std::ostringstream& os) {
  ns_->ha_state()->ExitPreTransition();

  os << R"({"msg":"success"})";
  return HttpStatusCode::kOk;
}

}  // namespace dancenn
