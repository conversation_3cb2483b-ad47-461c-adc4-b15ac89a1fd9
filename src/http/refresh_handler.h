// Copyright 2020 Mu <PERSON> <<EMAIL>>

#ifndef HTTP_REFRESH_HANDLER_H_
#define HTTP_REFRESH_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>
#include <unordered_map>

#include "http/http_handler.h"

namespace dancenn {

class NameSpace;
class BlockManager;

class RefreshHandler : public HttpHandler {
  public:
    explicit RefreshHandler();
    ~RefreshHandler() override = default;

    const std::string& name() const override {
      return name_;
    }

    cnetpp::http::HttpResponse Handle(
        const cnetpp::http::HttpRequest& request) override;
  private:
    HttpStatusCode RefreshTrustedIpTable(std::string* result);

  private:
    const std::string name_ { "/refresh" };
};

}  // namespace dancenn

#endif  // HTTP_REFRESH_HANDLER_H_

