// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_METRICS_HANDLER_H_
#define HTTP_METRICS_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>

#include "http/http_handler.h"

namespace dancenn {

class MetricsHandler : public HttpHandler {
 public:
  MetricsHandler() {}
  ~MetricsHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;
 private:
  const std::string name_ { "/metrics" };
};

}  // namespace dancenn

#endif  // HTTP_METRICS_HANDLER_H_

