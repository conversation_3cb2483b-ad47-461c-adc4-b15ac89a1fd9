// Copyright 2021 <PERSON> <<EMAIL>>

#include "http/open_api_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/string_utils.h>

#include <string>
#include <utility>

#include "base/metrics.h"
#include "base/path_util.h"

namespace dancenn {
namespace {

const char* kSrc = "src";
const char* kPermission = "permission";
const char* kUserName = "user_name";
const char* kGroupName = "group_name";
const char* kCreateParentIfMissing = "create_parent_if_missing";
const char* kTrue = "true";
const char* kRecursive = "recursive";

}  // namespace

cnetpp::http::HttpResponse OpenAPIHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  cnetpp::base::Uri uri;
  CHECK(uri.ParseUriPath(request.uri()));
  auto http_status = HttpStatusCode::kBadRequest;
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());
  std::ostringstream os;
  if (request.uri().find("/api/dn_replacement") == 0) {
    http_status = HandleDnReplacementTask(request, param_map, os);
  } else if (request.uri().find("/api/mkdir") == 0) {
    http_status = HandleMkdir(param_map, os);
  } else if (request.uri().find("/api/check_if_dir_empty") == 0) {
    http_status = HandleCheckIfDirEmpty(param_map, os);
  } else if (request.uri().find("/api/delete") == 0) {
    http_status = HandleDelete(param_map, os);
  }
  auto body = os.str();
  cnetpp::http::HttpResponse response;
  response.set_status(http_status);
  response.SetHttpHeader("Content-Length", std::to_string(body.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(body);
  return response;
}

std::string MakeJsonResponse(const std::string& msg, bool success,
                             Status* status = nullptr) {
  cnetpp::base::Object obj;
  obj["msg"] = msg;
  obj["success"] = success;

  if (status) {
    obj["status"] = status->exception();
    obj["reason"] = status->message();
  }

  std::string result;
  cnetpp::base::Parser::Serialize(cnetpp::base::Value(std::move(obj)), &result);
  return result;
}

HttpStatusCode OpenAPIHandler::HandleDnReplacementTask(
    const cnetpp::http::HttpRequest& request,
    const ParamMap& param_map,
    std::ostringstream& oss) {
  auto method = request.method();
  auto tokens = cnetpp::base::StringUtils::SplitByChars(request.uri(), "/");

  if (tokens.size() == 2 &&
      method == cnetpp::http::HttpRequest::MethodType::kGet) {
    return ListDnReplacementTask(param_map, oss);
  }

  if (tokens.size() != 3) {
    oss << R"({"msg":"Invalid path, expect: /api/dn_replacement/<task_id>"})";
    return HttpStatusCode::kOk;
  }

  auto task_id = tokens[2];
  switch (method) {
    case cnetpp::http::HttpRequest::MethodType::kGet: {
      return GetDnReplacementTask(task_id, oss);
    }
    case cnetpp::http::HttpRequest::MethodType::kPut: {
      const auto& body = request.http_body();
      return PutDnReplacementTask(task_id, body, oss);
    }
    case cnetpp::http::HttpRequest::MethodType::kDelete: {
      return DelDnReplacementTask(task_id, oss);
    }
    default: {
      oss << R"({"msg":"Invalid HTTP method"})";
    }
  }

  return HttpStatusCode::kOk;
}

HttpStatusCode OpenAPIHandler::ListDnReplacementTask(const ParamMap& param_map,
                                                     std::ostringstream& oss) {
  bool is_detail = false;
  GetBoolFromParams(param_map, "detail", &is_detail);

  auto tasks = op_task_manager_->ListDnReplacementTask();
  cnetpp::base::Array arr;
  oss << "[";
  bool first = true;
  for (auto& task : tasks) {
    if (!first) {
      oss << ",";
    }
    first = false;
    oss << task->ToJsonString(is_detail);
  }
  oss << "]";
  return HttpStatusCode::kOk;
}
HttpStatusCode OpenAPIHandler::GetDnReplacementTask(const std::string& task_id,
                                                    std::ostringstream& oss) {
  auto task = op_task_manager_->GetDnReplacementTask(task_id);
  if (task) {
    oss << task->ToJsonString();
  } else {
    oss << MakeJsonResponse("task not found", false);
  }
  return HttpStatusCode::kOk;
}
HttpStatusCode OpenAPIHandler::PutDnReplacementTask(const std::string& task_id,
                                                    const std::string& body,
                                                    std::ostringstream& oss) {
  auto s = op_task_manager_->TryAddDnReplacementTaskFromJson(task_id, body);
  if (s.HasException()) {
    oss << MakeJsonResponse("add task failed", false, &s);
    return HttpStatusCode::kOk;
  }

  oss << MakeJsonResponse("ok", true);
  return HttpStatusCode::kOk;
}
HttpStatusCode OpenAPIHandler::DelDnReplacementTask(const std::string& task_id,
                                                    std::ostringstream& oss) {
  op_task_manager_->RemoveDnReplacementTask(task_id);
  oss << MakeJsonResponse("ok", true);
  return HttpStatusCode::kOk;
}

// This interface is just for admin service.
// When user mounts /a to a new namespace, admin service will
// use this interface to create /a at new namespace.
HttpStatusCode OpenAPIHandler::HandleMkdir(const ParamMap& params,
                                           std::ostringstream& oss) {
  auto it = params.find(kSrc);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kSrc) + " not found", false);
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(it->second, /*username=*/"", &normalized_path)) {
    oss << MakeJsonResponse("Invalid path: " + it->second, false);
    return HttpStatusCode::kOk;
  }

  it = params.find(kPermission);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kPermission) + " not found", false);
    return HttpStatusCode::kOk;
  }
  uint32_t permission;
  try {
    permission = static_cast<uint32_t>(std::stol(it->second));
  } catch (const std::invalid_argument& e) {
    oss << MakeJsonResponse(it->second + " is invalid", false);
    return HttpStatusCode::kOk;
  } catch (const std::out_of_range& e) {
    oss << MakeJsonResponse(it->second + " is too large", false);
    return HttpStatusCode::kOk;
  }

  PermissionStatus ps;
  ps.set_permission(permission);
  it = params.find(kUserName);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kUserName) + " not found", false);
    return HttpStatusCode::kOk;
  }
  ps.set_username(it->second);
  it = params.find(kGroupName);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kGroupName) + " not found", false);
    return HttpStatusCode::kOk;
  }
  ps.set_groupname(it->second);

  bool create_parent_if_missing = false;
  it = params.find(kCreateParentIfMissing);
  if (it != params.end() && it->second == kTrue) {
    create_parent_if_missing = true;
  }

  SynchronizedRpcClosure done;
  namespace_->AsyncMkDirs(normalized_path,
                          ps,
                          UserGroupInfo("root", "supergroup"),
                          create_parent_if_missing,
                          false,
                          nullptr,
                          LogRpcInfo(),
                          &done);
  done.Await();
  if (done.status().IsOK()) {
    oss << MakeJsonResponse("ok", true);
    return HttpStatusCode::kOk;
  } else {
    auto status = done.status();
    oss << MakeJsonResponse("failed", false, &status);
    return HttpStatusCode::kOk;
  }
}

// NOTICE: outdated comment
// TODO(ruanjunbin)
// AdminService calls HandleCheckIfDirEmpty, then mounts a namespace.
// There is a gap between HandleCheckIfDirEmpty and mounting.
// HandleCheckAndFronzeEmptyDir is better.
HttpStatusCode OpenAPIHandler::HandleCheckIfDirEmpty(const ParamMap& params,
                                                     std::ostringstream& oss) {
  auto it = params.find(kSrc);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kSrc) + " not found", false);
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(it->second, /*username=*/"", &normalized_path)) {
    oss << MakeJsonResponse("Invalid path: " + it->second, false);
    return HttpStatusCode::kOk;
  }
  cloudfs::GetListingRequestProto req;
  cloudfs::GetListingResponseProto resp;
  RpcController controller;
  req.set_src(normalized_path);
  req.set_startafter("");
  req.set_needlocation(false);
  Status s = namespace_->GetListing(normalized_path,
                                    NetworkLocationInfo(),
                                    req,
                                    &resp,
                                    UserGroupInfo("root", "supergroup"),
                                    // Disable standby read.
                                    /*ctx=*/nullptr);
  if (s.HasException()) {
    oss << MakeJsonResponse("failed", false, &s);
    return HttpStatusCode::kOk;
  }
  if (resp.dirlist().partiallisting_size() != 0) {
    oss << MakeJsonResponse("dir is not empty", false);
    return HttpStatusCode::kOk;
  }
  oss << MakeJsonResponse("ok", true);
  return HttpStatusCode::kOk;
}

// This interface is just for admin service.
// When user unmounts /a, admin service will delete /a in root namespace.
HttpStatusCode OpenAPIHandler::HandleDelete(const ParamMap& params,
                                            std::ostringstream& oss) {
  auto it = params.find(kSrc);
  if (it == params.end()) {
    oss << MakeJsonResponse(std::string(kSrc) + " not found", false);
    return HttpStatusCode::kOk;
  }
  std::string normalized_path;
  if (!NormalizePath(it->second, /*username=*/"", &normalized_path)) {
    oss << MakeJsonResponse("Invalid path: " + it->second, false);
    return HttpStatusCode::kOk;
  }

  bool recursive = false;
  it = params.find(kRecursive);
  if (it != params.end() && it->second == kTrue) {
    recursive = true;
  }

  SynchronizedRpcClosure done;
  namespace_->AsyncDelete(normalized_path,
                          recursive,
                          UserGroupInfo(),
                          LogRpcInfo(),
                          &done);
  done.Await();
  if (done.status().IsOK()) {
    oss << MakeJsonResponse("ok", true);
    return HttpStatusCode::kOk;
  } else {
    auto status = done.status();
    oss << MakeJsonResponse("failed", false, &status);
    return HttpStatusCode::kOk;
  }
}

}  // namespace dancenn
