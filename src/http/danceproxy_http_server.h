// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_DANCEPROXY_HTTP_SERVER_H_
#define HTTP_DANCEPROXY_HTTP_SERVER_H_

#include <memory>
#include <proxy/storage_policy_ttl_manager.h>

#include "http/http_server.h"
#include "proxy/mounts_manager.h"
#include "proxy/path_team_space_quota_manager.h"
#include "proxy/frozen_directory_manager.h"
#include "proxy/storage_policy_ttl_manager.h"
#include "proxy/proxy_throttler.h"

namespace dancenn {

class DanceproxyHttpServer : public HttpServer {
 public:
  DanceproxyHttpServer() = default;
  virtual ~DanceproxyHttpServer() = default;

  void set_mounts_manager(std::shared_ptr<MountsManager> mounts_manager) {
    mounts_manager_ = mounts_manager;
  }

  void set_path_team_space_quota_manager(
      std::shared_ptr<PathTeamSpaceQuotaManager> quota_manager) {
    quota_manager_ = quota_manager;
  }

  void set_frozen_directory_manager(
      std::shared_ptr<FrozenDirectoryManager> frozen_directory_manager) {
    frozen_directory_manager_ = frozen_directory_manager;
  }

  void set_storage_policy_ttl_manager(
      std::shared_ptr<StoragePolicyTTLManager> storage_policy_ttl_manager) {
    storage_policy_ttl_manager_ = storage_policy_ttl_manager;
  }

  void set_throttler(
      std::shared_ptr<ProxyThrottler> throttler) {
    throttler_ = throttler;
  }

 protected:
  void RegisterHandlers() override;

  std::shared_ptr<MountsManager> mounts_manager_;
  std::shared_ptr<PathTeamSpaceQuotaManager> quota_manager_;
  std::shared_ptr<FrozenDirectoryManager> frozen_directory_manager_;
  std::shared_ptr<StoragePolicyTTLManager> storage_policy_ttl_manager_;
  std::shared_ptr<ProxyThrottler> throttler_;
};

}  // namespace dancenn

#endif  // HTTP_DANCEPROXY_HTTP_SERVER_H_

