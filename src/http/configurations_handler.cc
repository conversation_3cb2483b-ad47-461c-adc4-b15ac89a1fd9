// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/configurations_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/uri.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <glog/vlog_is_on.h>

#include <string>
#include <utility>
#include <vector>

namespace dancenn {

cnetpp::http::HttpResponse ConfigurationsHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  std::string str_results;
  auto result_code = HttpStatusCode::kOk;

  if (request.uri().find("/config/update") == 0) {
    cnetpp::base::Uri uri;
    if (!uri.ParseUriPath(request.uri())) {
      result_code = HttpStatusCode::kBadRequest;
    } else {
      auto params = uri.QueryParams();
      result_code = InternalHandleUpdateConfig(
          ParamMap(params.begin(), params.end()), &str_results);
    }
  } else if (request.uri().find("/config/force_update") == 0) {
    cnetpp::base::Uri uri;
    if (!uri.ParseUriPath(request.uri())) {
      result_code = HttpStatusCode::kBadRequest;
    } else {
      auto params = uri.QueryParams();
      result_code = InternalHandleForceUpdateConfig(
          ParamMap(params.begin(), params.end()), &str_results);
    }
  } else if (request.uri().find("/config/v") == 0 ||
             request.uri().find("/config/log_level") == 0) {
    cnetpp::base::Uri uri;
    if (!uri.ParseUriPath(request.uri())) {
      result_code = HttpStatusCode::kBadRequest;
    } else {
      auto params = uri.QueryParams();
      result_code = InternalHandleUpdateLogLevel(
          ParamMap(params.begin(), params.end()), &str_results);
    }
  } else {
    std::vector<gflags::CommandLineFlagInfo> output;
    gflags::GetAllFlags(&output);

    cnetpp::base::Array configs;
    for (auto& info : output) {
      cnetpp::base::Object config;
      config["name"] = info.name;
      config["current_value"] = info.current_value;
      config["default_value"] = info.default_value;
      config["type"] = info.type;
      config["is_default"] = info.is_default;
      config["description"] = info.description;
      configs.Append(cnetpp::base::Value(config));
    }

    cnetpp::base::Parser parser;
    parser.Serialize(cnetpp::base::Value(std::move(configs)), &str_results);
  }

  cnetpp::http::HttpResponse response;
  response.set_status(result_code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

HttpStatusCode ConfigurationsHandler::InternalHandleUpdateConfig(
    const ParamMap& params,
    std::string* result) {
  static const std::string ok_body = "{\"code\": 0, \"msg\": \"OK\"}";

  // update (integer,float,double,pointer) is thread-safe on x86/x64
  // if want dynamic update string flags, please ensure no thread-safe problem
  // with directly read the flag or use `GetCommandLineOption` read the flag

  static const std::vector<std::string> flags{
      // /config/update?replication_monitor_interval_ms=number
      "replication_monitor_interval_ms",
      // /config/update?datanode_stale_interval_ms=number
      "datanode_stale_interval_ms",
      // /config/update?datanode_keep_alive_timeout_sec=number
      "datanode_keep_alive_timeout_sec",
      // /config/update?bg_auto_compact_all_deletion_threshold=number
      "bg_auto_compact_all_deletion_threshold",
      // /config/update?bg_auto_compact_interval_in_min=number
      "bg_auto_compact_interval_in_min",
      // /config/update?bg_auto_compact_all_enable=number
      "bg_auto_compact_all_enable",
      // /config/update?bg_auto_compact_all_forbid=number
      "bg_auto_compact_all_forbid",
      // /config/update?block_report_interval_sec=number
      "block_report_interval_sec",
      // /config/update?max_ongoing_block_report_req_count=number
      "max_ongoing_block_report_req_count",
      // /config/update?max_ongoing_block_report_req_count_hard_limit=number
      "max_ongoing_block_report_req_count_hard_limit",
      // /config/update?bg_auto_compact_deletion_enable=number
      "bg_auto_compact_deletion_enable",
      // /config/update?dfs_summary_min_depth=number
      "dfs_summary_min_depth",
      // /config/update?dfs_meta_storage_rocksdb_perf_enabled=number
      "dfs_meta_storage_rocksdb_perf_enabled",
      // /config/update?dfs_meta_storage_rocksdb_perf_threshold_ms=number
      "dfs_meta_storage_rocksdb_perf_threshold_ms",
      // /config/update?dfs_ls_limit=number
      "dfs_ls_limit",
      // /config/update?edit_log_sync_min_wait_time_us=number
      "edit_log_sync_min_wait_time_us",
      // /config/update?edit_log_commit_max_batch_size=number
      "edit_log_commit_max_batch_size",
      // /config/update?edit_log_commit_max_wait_time_us=number
      "edit_log_commit_max_wait_time_us",
      // /config/update?edit_log_assigner_apply_mode=number
      "edit_log_assigner_apply_mode",
      // /config/update?edit_log_logical_apply_check_physical_log_enable=bool
      "edit_log_logical_apply_check_physical_log_enable",
      // /config/update?edit_log_physical_apply_check_db_enable=bool
      "edit_log_physical_apply_check_db_enable",
      // /config/update?edit_log_applyer_wait_no_pending_sleep_us=number
      "edit_log_applyer_wait_no_pending_sleep_us",
      // /config/update?dancenn_observer_submit_editlogger=bool
      "dancenn_observer_submit_editlogger",
      // /config/update?get_additional_datanode_max_excludes=number
      "get_additional_datanode_max_excludes",
      // /config/update?datanode_block_compact_threshold=number
      "datanode_block_compact_threshold",
      // /config/update?v=number
      "v",
      // /config/update?metrics_emit_interval_ms=number
      "metrics_emit_interval_ms",
      // /config/update?metrics_emit_address=string
      "metrics_emit_address",
      // /config/update?blacklist_dc=
      "blacklist_dc",
      // /config/update?majority_dc=HL,LF,LQ
      "majority_dc",
      // /config/update?default_distributed_dc=HL,LF
      "default_distributed_dc",
      // /config/update?append_reuse_last_block=bool,
      "append_reuse_last_block",
      // /config/update?read_policy_enable=true
      "read_policy_enable",
      // /config/update?dfs_meta_storage_max_write_batch_size=number
      "dfs_meta_storage_max_write_batch_size",
      // /config/update?dfs_meta_storage_update_scr_batch_size=number
      "dfs_meta_storage_update_scr_batch_size",
      // /config/update?bg_deletion_process_pending_delete_interval_sec=number
      "bg_deletion_process_pending_delete_interval_sec",
      // /config/update?bg_deletion_batch_remove_inode_threshold=number
      "bg_deletion_batch_remove_inode_threshold",
      // /config/update?bg_deletion_batch_remove_block_threshold=number
      "bg_deletion_batch_remove_block_threshold",
      // /config/update?bg_deletion_batch_remove_sleep_ms=number
      "bg_deletion_batch_remove_sleep_ms",
      // /config/update?blockmap_replication_cross_dc_limit_GBps=number
      "blockmap_replication_cross_dc_limit_GBps",
      // /config/update?fbr_protobuf_stream_limit_mb=number
      "fbr_protobuf_stream_limit_mb",
      // /config/update?blockmap_max_replication_streams=number
      "blockmap_max_replication_streams",
      // /config/update?blockmap_replication_work_multiplier=number
      "blockmap_replication_work_multiplier",
      // /config/update?blockmap_max_replication_streams_hard_limit=number
      "blockmap_max_replication_streams_hard_limit",
      // /config/update?check_untrusted_ip_access=bool
      "check_untrusted_ip_access",
      // /config/update?log_untrusted_ip_access=bool
      "log_untrusted_ip_access",
      // /config/update?forbid_untrusted_ip_access=bool
      "forbid_untrusted_ip_access",
      // /config/update?recycle_bin_enable=bool
      "recycle_bin_enable",
      // /config/update?recycle_bin_scanner_enable=bool
      "recycle_bin_scanner_enable",
      // /config/update?recycle_bin_scanner_interval_sec=number
      "recycle_bin_scanner_interval_sec",
      // /config/update?recycle_bin_retention_day=number
      "recycle_bin_retention_day",
      // /config/update?recycle_bin_default_policy_enable=bool
      "recycle_bin_default_policy_enable",
      // /config/update?recycle_bin_default_policy_time_sec=number
      "recycle_bin_default_policy_time_sec",
      // /config/update?meta_storage_snapshot_read_enabled=bool
      "meta_storage_snapshot_read_enabled",
      // /config/update?standby_read_enabled=bool
      "standby_read_enabled",
      // /config/update?log_stale_read=bool
      "log_stale_read",
      // /config/update?standby_read_enabled_for_main_read=bool
      "standby_read_enabled_for_main_read",
      // /config/update?standby_read_enabled_for_all_read=bool
      "standby_read_enabled_for_all_read",
      // /config/update?standby_read_max_txid_gap_for_wait=number
      "standby_read_max_txid_gap_for_wait",
      // /config/update?standby_read_wait_txid_catchup_time_ms_total=number
      "standby_read_wait_txid_catchup_time_ms_total",
      // /config/update?standby_read_wait_txid_catchup_time_ms=number
      "standby_read_wait_txid_catchup_time_ms",
      // /config/update?standby_read_set_seen_txid=bool
      "standby_read_set_seen_txid",
      // /config/update?log_rpc_app_err=bool
      "log_rpc_app_err",
      // /config/update?security_key_enable=bool
      "security_key_enable",
      // /config/update?retry_cache_enabled=bool
      "retry_cache_enabled",
      // /config/update?retry_cache_expiration_time_ms=number
      "retry_cache_expiration_time_ms",
      // /config/update?block_lifecycle_enable=bool
      "block_lifecycle_enable",
      // /config/update?op_task_max_size=number
      "op_task_max_size",
      // /config/update?op_task_max_dns=number
      "op_task_max_dns",
      // /config/update?blockmap_max_replication_streams_decommission=number
      "blockmap_max_replication_streams_decommission",
      // /config/update?active_write_throttler_limit=number
      "active_write_throttler_limit",
      // /config/update?edit_log_slow_op_us=number
      "edit_log_slow_op_us",
      // /config/update?edit_log_assigner_max_num_pending_tasks=number
      "edit_log_assigner_max_num_pending_tasks",
      // /config/update?edit_log_assigner_add_task_retry_sleep_ms=number
      "edit_log_assigner_add_task_retry_sleep_ms",
      // /config/update?namespace_read_full_detail_blocks=bool
      "namespace_read_full_detail_blocks",
      // /config/update?clear_storage_when_convert_to_uc=bool
      "clear_storage_when_convert_to_uc",
      // /config/update?random_node=bool
      "random_node",
      // /config/update?scan_deprecated_block_max_size=number
      "scan_deprecated_block_max_size",
      // /config/update?block_machine_requirement=number
      "block_machine_requirement",
      // /config/update?block_machine_requirement_for_read=number
      "block_machine_requirement_for_read",
      // /config/update?block_placement_distribution_type=[round-robin, uniform,
      // geometric]
      "block_placement_distribution_type",
      // /config/update?dump_parse_failed_protobuf_path=/var/log/tiger/parse-failed-protobuf.
      "dump_parse_failed_protobuf_path",
      // config/update?observer_endpoint=str
      "observer_endpoint",
      // /config/update?upload_cmds_max_size=number
      "upload_cmds_max_size",
      // /config/update?ne_cmds_max_size=number
      "ne_cmds_max_size",
      // /config/update?merge_cmds_max_size=number
      "merge_cmds_max_size",
      // /config/update?pin_cmds_max_size=number
      "pin_cmds_max_size",
      // /config/update?frozen_path=string
      "frozen_path",
      // /config/update?view_lease_limit_when_no_condition=number
      "view_lease_limit_when_no_condition",
      // /config/update?extra_iam_resouces=string
      "extra_iam_resouces",
      // /config/update?scrub_wait_for_done_sleep_ms=number
      "scrub_wait_for_done_sleep_ms",
      // /config/update?inode_stat_delta_cache_gc_interval_ms=number
      "inode_stat_delta_cache_gc_interval_ms",
      // /config/update?lifecycle_enable=true,
      // XXX only allow to turn off dynamically, in case of emergency
      "lifecycle_enable",
      // /config/update?lifecycle_scanner_force_start_next_group=false
      "lifecycle_scanner_force_start_next_group",
      // /config/update?lifecycle_scanner_force_skip_next_group=bool
      "lifecycle_scanner_force_skip_next_group",
      // /config/update?lifecycle_scanner_start_next_group_period_ms=number
      "lifecycle_scanner_start_next_group_period_ms",
      // /config/update?lifecycle_scanner_filter_depred_report_batch_size=number
      "lifecycle_scanner_filter_depred_report_batch_size",
      // /config/update?lifecycle_scrub_persist_stats_depth=number
      "lifecycle_scrub_persist_stats_depth",
      // /config/update?lifecycle_scrub_remove_depred_xattr_per_scurb=number
      "lifecycle_scrub_remove_depred_xattr_per_scurb",
      // /config/update?blkid_cmd_max_num_blocks=number
      "blkid_cmd_max_num_blocks",
      // /config/update?allow_list_many_lease=bool
      "allow_list_many_lease",
      // /config/update?banned_user=string
      "banned_user",
      // /config/update?dfs_meta_scan_interval_sec=number
      "dfs_meta_scan_interval_sec",
      // /config/update?disable_block_uploader=bool
      "disable_block_uploader",
      // /config/update?block_pufs_info_monitor_interval_ms=number
      "block_pufs_info_monitor_interval_ms",
      // /config/update?delete_tos_block_interval_ms=number
      "delete_tos_block_interval_ms",
      // /config/update?lease_monitor_interval_ms=number
      "lease_monitor_interval_ms",
      // /config/update?snapshot_gc_interval_in_sec=number
      "snapshot_gc_interval_in_sec",
      // /config/update?meta_storage_checkpoint_period_sec=number
      "meta_storage_checkpoint_period_sec",
      // /config/update?quota_expire_interval_sec_soft_limit=number
      "quota_expire_interval_sec_soft_limit",
      // /config/update?quota_expire_interval_sec_hard_limit=number
      "quota_expire_interval_sec_hard_limit",
      // /config/update?quota_expire_txid_gap_soft_limit=number
      "quota_expire_txid_gap_soft_limit",
      // /config/update?quota_expire_txid_gap_hard_limit=number
      "quota_expire_txid_gap_hard_limit",
      // /config/update?quota_heartbeat_interval_sec=number
      "quota_heartbeat_interval_sec",
      // /config/update?az_monitor_enable=bool
      "az_monitor_enable",
      // /config/update?az_monitor_refresh_interval_ms=number
      "az_monitor_refresh_interval_ms",
      // /config/update?client_block_size_support=number
      "client_block_size_support",
      // /config/update?dfs_block_size=number
      "dfs_block_size",

      // https://bytedance.feishu.cn/docx/Fzecd8cvwo6BHCxN0CGcUw0cnrg
      // BlockRecycler
      "del_depring_blks_batch_size",
      "ongoing_op_del_depring_blks_max_size",
      "del_depred_blks_batch_size",
      "wait_age_before_seek_depred_blks_iter_to_first",
      "cached_invalidate_pufs_cmd_max_size",
      "invalidate_pufs_cmd_batch_size",
      "delete_tos_block_interval_ms",
      "ongoing_op_del_depred_blks_max_size",
      "not_full_op_del_depred_blks_mature_age",
      // /config/update?job_manager_enable=bool
      "job_manager_enable",
      // /config/update?managed_job_state_cached_ms=number
      "managed_job_state_cached_ms",
      // /config/update?managed_metadata_job_exec_timeout_ms=number
      "managed_metadata_job_exec_timeout_ms",
      // /config/update?managed_task_load_metadata_max_retry_times=number
      "managed_task_load_metadata_max_retry_times",
      // /config/update?managed_task_load_max_replica=number
      "managed_task_load_max_replica",
      // /config/update?load_cmd_batch_size=number
      "load_cmd_batch_size",
      // /config/update?load_blk_cmd_cache_max_size=number
      "load_blk_cmd_cache_max_size",
      // /config/update?load_cmd_to_job_manager_retry_times=number
      "load_cmd_to_job_manager_retry_times",
      // /config/update?job_tracker_max_pending_autonomous_tasks=number
      "job_tracker_max_pending_autonomous_tasks",
      // /config/update?job_tracker_max_pending_job=number
      "job_tracker_max_pending_job",
      // /config/update?recursive_sync_listing_sleep_us=number
      "recursive_sync_listing_sleep_us",
      // /config/update?ufs_syncengine_recursive_list_timeout_sec=number
      "ufs_syncengine_recursive_list_timeout_sec",
      // /config/update?managed_task_load_execute_timeout_ms=number
      "managed_task_load_execute_timeout_ms",
      // /config/update?managed_task_free_execute_timeout_ms=number
      "managed_task_free_execute_timeout_ms",
      // /config/update?copy_replica_task_execute_timeout_ms=number
      "copy_replica_task_execute_timeout_ms",
      // /config/update?managed_task_load_max_retry_times=number
      "managed_task_load_max_retry_times",
      // /config/update?managed_task_free_max_retry_times=number
      "managed_task_free_max_retry_times",
      // /config/update?job_tracker_max_pending_block_tasks=number
      "job_tracker_max_pending_block_tasks",
      // /config/update?job_tracker_inspect_period_sec=number
      "job_tracker_inspect_period_sec",
      // /config/update?job_tracker_metastorage_inspect_period_sec=number
      "job_tracker_metastorage_inspect_period_sec",
      // /config/update?managed_task_load_metadata_execute_timeout_ms=number
      "managed_task_load_metadata_execute_timeout_ms",
      // /config/update?managed_job_block_task_exec_timeout_ms=number
      "managed_job_block_task_exec_timeout_ms",
      // /config/update?enable_write_back=bool
      "enable_write_back",
      // /config/update?job_tracker_worker_retry_times=number
      "job_tracker_worker_retry_times",
      // /config/update?job_tracker_worker_retry_sleep_us=number
      "job_tracker_worker_retry_sleep_us",

      // /config/update?dfs_meta_scan_enable=bool
      "dfs_meta_scan_enable",
      // /config/update?dfs_meta_scan_interval_sec=number
      "dfs_meta_scan_interval_sec",

      // /config/update?block_manager_allow_transfer_persisted_blocks=bool
      "block_manager_allow_transfer_persisted_blocks",

      // /config/update?write_back_task_v2_max_scan_num=number,
      "write_back_task_v2_max_scan_num",

      // /config/update?tos_event_key_prefix_blacklist=str
      "tos_event_key_prefix_blacklist",
      // /config/update?tos_event_key_prefix_blacklist_enabled=bool
      "tos_event_key_prefix_blacklist_enabled",

      // /config/update?log_dn_negoed_ibr=bool
      "log_dn_negoed_ibr",
      // /config/update?skip_dn_negoed_ibr=bool
      "skip_dn_negoed_ibr",

      // /config/update?blockmap_invalidate_limit=number
      "blockmap_invalidate_limit",

      // /config/update?safemode_check_block_cnt=bool
      "safemode_check_block_cnt",
      // /config/update?safemode_threshold_pct=[0.0-1.0]
      "safemode_threshold_pct",
      // /config/update?safemode_check_dn_cnt=bool
      "safemode_check_dn_cnt",
      // /config/update?safemode_min_datanodes=num
      "safemode_min_datanodes",
      // /config/update?safemode_check_safe_dn_cnt=bool
      "safemode_check_safe_dn_cnt",
      // /config/update?safemode_safe_dn_threshold_pct=[0.0-1.0]
      "safemode_safe_dn_threshold_pct",
      // /config/update?safemode_extension_ms=num
      "safemode_extension_ms",
      // /config/update?enable_ufs_sync_in_rename=bool
      "enable_ufs_sync_in_rename",
      // /config/update?enable_ufs_sync_in_operation=bool
      "enable_ufs_sync_in_operation",
      // /config/update?lifecycle_enable_atime_ttl=bool
      "lifecycle_enable_atime_ttl",
  };

  for (size_t i = 0; i < flags.size(); i++) {
    auto itr = params.find(flags[i]);
    if (itr != params.end()) {
      if (itr->first == "v") {
        try {
          auto level = std::stoi(itr->second);
          // XXX SetVLOGLevel() take effect only when "--vmodule" specified
          // same pattern, which is '*' here.
          google::SetVLOGLevel("*", level);
          LOG(INFO) << "Set Verbose log level: " << level
                    << " enable: " << (VLOG_IS_ON(level));
        } catch (...) {
          return HttpStatusCode::kBadRequest;
        }
      } else if (gflags::SetCommandLineOption(itr->first.c_str(),
                                              itr->second.c_str())
                     .empty()) {
        return HttpStatusCode::kBadRequest;
      }
      *result = ok_body;
      return HttpStatusCode::kOk;
    }
  }

  // /config/update?help
  if (params.find("help") != params.end()) {
    std::stringstream ss;
    ss << "{ \"code\": 0, \"flags\": ["
       << "\"replication_monitor_interval_ms\","
       << "]}";
    *result = ss.str();
    return HttpStatusCode::kOk;
  }

  return HttpStatusCode::kNotFound;
}

HttpStatusCode ConfigurationsHandler::InternalHandleForceUpdateConfig(
    const ParamMap& params,
    std::string* result) {
  static const std::string ok_body = "{\"code\": 0, \"msg\": \"OK\"}";

  // update (integer,float,double,pointer) is thread-safe on x86/x64
  // if want dynamic update string flags, please ensure no thread-safe problem
  // with directly read the flag or use `GetCommandLineOption` read the flag

  for (const auto& itr : params) {
    if (itr.first == "v") {
      try {
        auto level = std::stoi(itr.second);
        // XXX SetVLOGLevel() take effect only when "--vmodule" specified
        // same pattern, which is '*' here.
        google::SetVLOGLevel("*", level);
        LOG(INFO) << "Set Verbose log level: " << level
                  << " enable: " << (VLOG_IS_ON(level));
      } catch (...) {
        return HttpStatusCode::kBadRequest;
      }
    } else if (gflags::SetCommandLineOption(itr.first.c_str(),
                                            itr.second.c_str())
                   .empty()) {
      return HttpStatusCode::kBadRequest;
    }
    *result = ok_body;
    return HttpStatusCode::kOk;
  }
  return HttpStatusCode::kNotFound;
}

HttpStatusCode ConfigurationsHandler::InternalHandleUpdateLogLevel(
    const ParamMap& params,
    std::string* result) {
  static const std::string ok_body = "{\"code\": 0, \"msg\": \"OK\"}";

  // update (integer,float,double,pointer) is thread-safe on x86/x64
  // if want dynamic update string flags, please ensure no thread-safe problem
  // with directly read the flag or use `GetCommandLineOption` read the flag

  for (const auto& itr : params) {
    try {
      auto level = std::stoi(itr.second);
      google::SetVLOGLevel(itr.first.c_str(), level);
      LOG(INFO) << "Set Verbose log level. "
                << " module=" << itr.first << " level=" << level
                << " enable: " << (VLOG_IS_ON(level));
    } catch (...) {
      return HttpStatusCode::kBadRequest;
    }
    *result = ok_body;
    return HttpStatusCode::kOk;
  }
  return HttpStatusCode::kNotFound;
}

}  // namespace dancenn
