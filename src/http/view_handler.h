// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef HTTP_VIEW_HANDLER_H_
#define HTTP_VIEW_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>
#include <unordered_map>

#include "http/http_handler.h"

namespace dancenn {

class NameSpace;
class BlockManager;

class ViewHandler : public HttpHandler {
 public:
  ViewHandler(std::shared_ptr<NameSpace> ns, std::shared_ptr<BlockManager> bm);
  ~ViewHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  typedef HttpStatusCode (ViewHandler::*FuncType)(const ParamMap&,
                                                  std::ostringstream& os);

  void GetJemallocAllocatorInfo(size_t* allocated,
                                size_t* active,
                                size_t* resident);

  HttpStatusCode GetMemoryFragmentation(const ParamMap& params,
                                        std::ostringstream& os);
  HttpStatusCode GetNetBufferMemory(const ParamMap& params,
                                    std::ostringstream& os);
  HttpStatusCode GetNetBufferCache(const ParamMap& params,
                                   std::ostringstream& os);

  // block
  HttpStatusCode GetCorruptFilesInfo(const ParamMap& params,
                                     std::ostringstream& os);
  HttpStatusCode GetCorruptBlocksInfo(const ParamMap& params,
                                      std::ostringstream& os);
  HttpStatusCode GetBlockIndexStats(const ParamMap& params,
                                    std::ostringstream& os);
  HttpStatusCode GetBlockIndexDetail(const ParamMap& params,
                                     std::ostringstream& os);
  HttpStatusCode GetSealedBlocksInfo(const ParamMap& params,
                                     std::ostringstream& os);
  HttpStatusCode GetTruncatableBlocksInfo(const ParamMap& params,
                                          std::ostringstream& os);
  HttpStatusCode GetDnTruncatableCmd(const ParamMap& params,
                                     std::ostringstream& os);
  HttpStatusCode GetDnInvalidateCmd(const ParamMap& params,
                                    std::ostringstream& os);

  // inode
  HttpStatusCode GetParentMapStats(const ParamMap& params,
                                   std::ostringstream& os);
  HttpStatusCode GetLease(const ParamMap& params, std::ostringstream& os);

  // policy
  HttpStatusCode GetReplicaPolicy(const ParamMap& params,
                                  std::ostringstream& os);
  HttpStatusCode GetReadPolicy(const ParamMap& params, std::ostringstream& os);
  HttpStatusCode GetRecyclePolicy(const ParamMap& params,
                                  std::ostringstream& os);

  // other
  HttpStatusCode GetTrustedIp(const ParamMap& params, std::ostringstream& os);
  HttpStatusCode GetRackAware(const ParamMap& params, std::ostringstream& os);
  HttpStatusCode GetRetryCache(const ParamMap& params, std::ostringstream& os);
  HttpStatusCode GetOngoingUploadTask(const ParamMap& params,
                                      std::ostringstream& os);

 private:

  const std::string name_{"/view"};

  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> bm_;

  std::map<std::string, FuncType> table_;
};

}  // namespace dancenn

#endif  // HTTP_VIEW_HANDLER_H_
