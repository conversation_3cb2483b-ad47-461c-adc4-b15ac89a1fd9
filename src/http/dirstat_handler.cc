//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include "http/dirstat_handler.h"

#include "namespace/namespace.h"
#include "namespace/namespace_stat.h"

DECLARE_bool(dancenn_observe_mode_on);

namespace dancenn {

using HttpResponse = cnetpp::http::HttpResponse;
using MethodType = cnetpp::http::HttpRequest::MethodType;

HttpResponse ReturnError(HttpStatusCode code,
                         const std::string& error_msg = "") {
  if (error_msg.empty()) {
    HttpResponse response;
    response.set_status(code);
    return response;
  }

  cnetpp::base::Object results;
  results["message"] = error_msg;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  HttpResponse response;
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse
DirStatHandler::Handle(const cnetpp::http::HttpRequest& request) {
  CHECK_NOTNULL(ns_);

  if (!FLAGS_dancenn_observe_mode_on) {
    return ReturnError(HttpStatusCode::kBadRequest,
                       "Only available in Observe mode.");
  }

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return ReturnError(HttpStatusCode::kBadRequest);
  }
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());

  std::string cmd = "query";
  GetStringFromParams(param_map, "cmd", &cmd);

  if (cmd == "query") {
    return HandleQueryRequest(param_map);
  }

  if (cmd == "startscrub") {
    if (request.method() != MethodType::kPost) {
      return ReturnError(HttpStatusCode::kBadRequest,
                         "Only POST method is allowed for this API.");
    }
    return HandleStartScrubRequest(param_map);
  }
  if (cmd == "stopscrub") {
    if (request.method() != MethodType::kPost) {
      return ReturnError(HttpStatusCode::kBadRequest,
                         "Only POST method is allowed for this API.");
    }
    return HandleStopScrubRequest(param_map);
  }
  if (cmd == "getscrub") {
    return HandleGetScrubRequest(param_map);
  }

  return ReturnError(HttpStatusCode::kBadRequest, "Invalid cmd: " + cmd);
}

cnetpp::http::HttpResponse DirStatHandler::HandleQueryRequest(const ParamMap& params) {
  std::string path = "/";
  GetStringFromParams(params, "path", &path);

  INodeStat stat;
  Status s = ns_->GetDirectoryStat(path, &stat);
  if (s.code() == Code::kFileNotFound) {
    LOG(ERROR) << "DirStatHandler requested path: " << path << " not found.";
    return ReturnError(HttpStatusCode::kBadRequest, "Path not found.");
  }
  if (s.code() == Code::kINodeStatNotFound) {
    LOG(ERROR) << "DirStatHandler stat for requested path: " << path
               << " not found.";
    return ReturnError(HttpStatusCode::kBadRequest, "Dir stat not found.");
  }
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to get INodeStat for path: " << path
               << ", error: " << s.ToString();
    return ReturnError(HttpStatusCode::kInternalServerError, s.ToString());
  }

  cnetpp::base::Object results;

  results["path"] = path;
  results["inode_id"] = stat.inode_id;
  results["inode_num"] = stat.inode_num;
  results["file_num"] = stat.file_num;
  results["dir_num"] = stat.dir_num;
  results["block_num"] = stat.block_num;
  results["data_size"] = stat.data_size;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse DirStatHandler::HandleStartScrubRequest(const ParamMap& params) {
  std::string action = "check";
  GetStringFromParams(params, "action", &action);
  ScrubAction scrub_action = SCRUB_ACTION_CHECK;
  if (action == "check") {
    scrub_action = SCRUB_ACTION_CHECK;
  } else if (action == "overwrite") {
    scrub_action = SCRUB_ACTION_FORCE_OVERWRITE;
  } else {
    return ReturnError(HttpStatusCode::kBadRequest,
                       "Invalid action (check / overwrite): " + action);
  }

  auto s = ns_->StartScrub(SCRUB_OPTYPE_INODE_STAT, scrub_action);
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to start scrub. error: " << s.ToString();
    return ReturnError(HttpStatusCode::kInternalServerError,
                       "Failed to start scrub. error: " + s.ToString());
  }

  return ReturnError(HttpStatusCode::kOk, "Scrub started.");
}

cnetpp::http::HttpResponse DirStatHandler::HandleStopScrubRequest(const ParamMap& params) {
  (void) params;

  auto s = ns_->StopScrub(SCRUB_OPTYPE_INODE_STAT);
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to stop scrub. error: " << s.ToString();
    return ReturnError(HttpStatusCode::kInternalServerError, "Failed to stop scrub. error: " + s.ToString());
  }

  return ReturnError(HttpStatusCode::kOk, "Scrub stopped.");
}

cnetpp::http::HttpResponse DirStatHandler::HandleGetScrubRequest(const ParamMap& params) {
  (void) params;

  ScrubProgress progress;
  auto s = ns_->GetScrubProgress(SCRUB_OPTYPE_INODE_STAT, &progress);
  if (s.IsOK()) {
    cnetpp::base::Object results;

    results["status"] = "running";
    results["start_ms"] = progress.start_ms;
    results["finished_inodes"] = progress.finished_inodes;
    results["finished_dirs"] = progress.finished_dirs;
    results["estimated_time_remain_seconds"] = progress.estimated_time_remain_seconds;

    std::string str_results;
    cnetpp::base::Parser parser;
    parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

    cnetpp::http::HttpResponse response;
    response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
    response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
    response.SetHttpHeader("Content-Type", "application/json");
    response.set_http_body(str_results);
    return response;
  }

  ScrubResult result;
  s = ns_->GetScrubResult(SCRUB_OPTYPE_INODE_STAT, &result);
  if (s.IsOK()) {
    cnetpp::base::Object results;

    results["status"] = "not running";
    results["start_ms"] = result.start_ms;
    results["finished_ms"] = result.finished_ms;
    results["scrub_status"] = result.status.ToString();

    if (result.status.IsOK()) {
      auto inode_stat =
        std::dynamic_pointer_cast<INodeStatScrubOp>(result.result)->GetComputedStat();
      cnetpp::base::Object stat;
      stat["inode_id"] = inode_stat.inode_id;
      stat["inode_num"] = inode_stat.inode_num;
      stat["file_num"] = inode_stat.file_num;
      stat["dir_num"] = inode_stat.dir_num;
      stat["block_num"] = inode_stat.block_num;
      stat["data_size"] = inode_stat.data_size;
    }

    std::string str_results;
    cnetpp::base::Parser parser;
    parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

    cnetpp::http::HttpResponse response;
    response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
    response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
    response.SetHttpHeader("Content-Type", "application/json");
    response.set_http_body(str_results);
    return response;
  }

  return ReturnError(HttpStatusCode::kInternalServerError,
                     "Failed to get scrub. error: " + s.ToString());
}

} // namespace dancenn
