//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <string>
#include <utility>

// Third
#include "cnetpp/http/http_request.h"
#include "cnetpp/http/http_response.h"

// Project
#include "acc/acc_namespace.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"

namespace dancenn {

class NameSpace;

class FSHandler : public HttpHandler {
 public:
  explicit FSHandler(std::shared_ptr<AccNamespace> acc_ns,
                     std::shared_ptr<NameSpace> ns)
      : acc_ns_(std::move(acc_ns)), ns_(std::move(ns)) {
  }
  ~FSHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  void BuildFileStatus(const HdfsFileStatusProto& file,
                       cnetpp::base::Object* file_out);

  cnetpp::http::HttpResponse HandleStatCommand(const std::string& path,
                                                const ParamMap& param_map);
  cnetpp::http::HttpResponse HandleListCommand(const std::string& path,
                                               const ParamMap& param_map,
                                               bool allow_list_file);

 private:
  const std::string name_{"/fs"};

  std::shared_ptr<AccNamespace> acc_ns_{nullptr};
  std::shared_ptr<NameSpace> ns_{nullptr};
};

}  // namespace dancenn