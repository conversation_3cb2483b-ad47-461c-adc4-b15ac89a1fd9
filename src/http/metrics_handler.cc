// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/metrics_handler.h"

#include <cnetpp/base/csonpp.h>

#include <utility>
#include <string>

#include "base/metrics.h"

namespace dancenn {

cnetpp::http::HttpResponse MetricsHandler::Handle(
      const cnetpp::http::HttpRequest& request) {
  (void) request;
  cnetpp::base::Object results;
  MetricsCenter::Instance()->ToJson(&results);
  cnetpp::base::Parser parser;
  std::string str_results;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);
  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

}  // namespace dancenn
