// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <http/http_server.h>

#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/http/http_packet.h>
#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>
#include <cnetpp/base/ip_address.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <cassert>
#include <memory>
#include <utility>

#include "http/http_handler.h"
#include "http/metrics_handler.h"
#include "http/dancenn_status_handler.h"
#include "http/configurations_handler.h"
#include "datanode_manager/datanode_manager.h"

DECLARE_int32(http_port);
DECLARE_int32(http_handler_count);
DECLARE_int32(http_network_thread_count);
DECLARE_int32(http_tcp_send_buffer_size);
DECLARE_int32(http_tcp_recv_buffer_size);
DECLARE_int32(http_send_buffer_size);
DECLARE_int32(http_recv_buffer_size);

namespace dancenn {

std::string RealIP(cnetpp::http::HttpConnection* connection) {
  auto ip = connection->tcp_connection()->remote_end_point().address();
  if (ip.IsIPv4Mapped()) {
    cnetpp::base::IPAddress real_ip;
    ip.GetIPv4FromIPv6(&real_ip);
    return real_ip.ToString();
  } else {
    return ip.ToString();
  }
}

bool HttpServer::Launch(const cnetpp::base::EndPoint& local_address) {
  cnetpp::http::HttpServerOptions options;
  options.set_tcp_send_buffer_size(FLAGS_http_tcp_send_buffer_size);
  options.set_tcp_receive_buffer_size(FLAGS_http_tcp_recv_buffer_size);
  options.set_send_buffer_size(FLAGS_http_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_http_recv_buffer_size);
  options.set_worker_count(FLAGS_http_handler_count);
  SetCallbacks(&options);

  workers_ = std::make_shared<cnetpp::concurrency::ThreadPool>("http-worker");
  workers_->set_num_threads(FLAGS_http_handler_count);
  workers_->Start();

  RegisterHandlers();

  return http_server_.Launch(local_address, options);
}

bool HttpServer::Shutdown() {
  if (!http_server_.Shutdown()) {
    return false;
  }

  workers_->Stop();
  return true;
}

bool HttpServer::FastShutdown() {
  stopped_ = true;
  LOG(INFO) << "prepare to close all remain TCP connections.";
  {
    std::shared_lock<ReadWriteLock> guard(connections_lock_);
    for (auto& conn : http_connections_) {
      conn.second->MarkAsClosed(true);
    }
  }
  std::this_thread::sleep_for(std::chrono::seconds(1));
  if (!http_server_.Shutdown()) {
    return false;
  }
  return true;
}

void HttpServer::SetCallbacks(cnetpp::http::HttpServerOptions* options) {
  options->set_connected_callback(
      [this](std::shared_ptr<cnetpp::http::HttpConnection> c) -> bool {
        return this->OnConnected(c);
      });
  options->set_closed_callback(
      [this](std::shared_ptr<cnetpp::http::HttpConnection> c) -> bool {
        return this->OnClosed(c);
      });
  options->set_received_callback(
      [this](std::shared_ptr<cnetpp::http::HttpConnection> c) -> bool {
        return this->OnReceived(c);
      });
  options->set_sent_callback(
      [this](bool sent,
             std::shared_ptr<cnetpp::http::HttpConnection> c) -> bool {
        return this->OnSent(sent, c);
      });
}

bool HttpServer::OnConnected(
    std::shared_ptr<cnetpp::http::HttpConnection> connection) {
  assert(connection.get());
  {
    std::unique_lock<ReadWriteLock> guard(connections_lock_);
    // Refuse all new connections if we are shutting down
    if (stopped_) {
      return false;
    }
    http_connections_[connection->id()] = connection;
  }
  return true;
}

bool HttpServer::OnReceived(
    std::shared_ptr<cnetpp::http::HttpConnection> connection) {
  assert(connection.get());
  auto http_request_ptr = static_cast<cnetpp::http::HttpRequest*>(
      connection->http_packet().get());
  if (!http_request_ptr) {
    return false;
  }

  // Must hold a copy to http_request instead of directly pass shared_ptr to
  // lambda.
  // Because http_packet will be reset after read_callback is called.
  // See cnetpp::http::HttpConnection#140
  workers_->AddTask([this, connection,
      http_request = *(http_request_ptr)]() {
      LOG(INFO) << "Http request received [IP: " << RealIP(connection.get())
                << ", Method: " << static_cast<int>(http_request.method())
                << ", URI: " << http_request.uri() << "]";

      cnetpp::http::HttpResponse response;
      auto it = handlers_.rbegin();
      for (; it != handlers_.rend(); ++it) {
        if (http_request.uri().find(it->first) == 0) {
          response = std::move(it->second->Handle(http_request));
          break;
        }
      }
      if (it == handlers_.rend()) {
        response = std::move(HttpHandler::NotFound(http_request.uri()));
      }
      std::string str;
      response.ToString(&str);
      connection->SendPacket(str);
      {
        std::unique_lock<ReadWriteLock> guard(connections_lock_);
        http_connections_.erase(connection->id());
      }
      return true;
  });

  return true;
}

bool HttpServer::OnSent(bool success,
    std::shared_ptr<cnetpp::http::HttpConnection> connection) {
  (void) success;
  assert(connection.get());
  return true;
}

bool HttpServer::OnClosed(
    std::shared_ptr<cnetpp::http::HttpConnection> connection) {
  assert(connection.get());
  return true;
}

}  // namespace dancenn

