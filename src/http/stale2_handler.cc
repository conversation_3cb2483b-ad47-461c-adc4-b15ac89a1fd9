// Copyright 2021 <PERSON> <<EMAIL>>

#include "http/stale2_handler.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/uri.h>
#include <cnetpp/base/string_utils.h>
#include <glog/logging.h>

#include <utility>
#include <vector>

#include "base/defer.h"

namespace dancenn {

cnetpp::http::HttpResponse Stale2Handler::Handle(
    const cnetpp::http::HttpRequest &request) {
  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return GetResponse(HttpStatusCode::kBadRequest,
                       "Invalid request method or uri");
  }
  switch (request.method()) {
    case cnetpp::http::HttpRequest::MethodType::kPost:
      return HandlePost(request);
    case cnetpp::http::HttpRequest::MethodType::kGet:
      return HandleGet(request);
    default:
      return GetResponse(HttpStatusCode::kBadRequest, "Invalid request method");
  }
}

cnetpp::http::HttpResponse Stale2Handler::HandleGet(
    const cnetpp::http::HttpRequest &request) {
  std::vector<std::string> hosts;
  {
    std::unique_lock<std::mutex> guard(in_progress_mutex_);
    for (auto& h : all_host_) {
      hosts.push_back(h);
    }
  }
  sort(hosts.begin(), hosts.end());

  // serialization
  cnetpp::base::Array results;
  for (auto& h : hosts) {
    results.Append(cnetpp::base::Value(h));
  }

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  // response
  cnetpp::http::HttpResponse response;
  response.set_status(HttpStatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse Stale2Handler::HandlePost(
    const cnetpp::http::HttpRequest &request) {
  std::unique_lock<std::mutex> guard(in_progress_mutex_, std::try_to_lock);
  if (!guard.owns_lock()) {
    return GetResponse(HttpStatusCode::kBadRequest, "InProgress");
  }

  std::unordered_set<std::string> all_hosts;
  if (!GetHosts(request.http_body(), &all_hosts)) {
    return GetResponse(HttpStatusCode::kBadRequest, "Invalid request body");
  }

  all_host_ = std::move(all_hosts);
  datanode_manager_->UpdateStale2Nodes(all_host_, &stale_dns_);

  return GetResponse(HttpStatusCode::kOk, "OK");
}

cnetpp::http::HttpResponse Stale2Handler::GetResponse(
    const HttpStatusCode code, const std::string& msg) {
  cnetpp::base::Array results;
  cnetpp::base::Object result;
  result["code"] = (code == HttpStatusCode::kOk) ? 0 : -1;
  result["msg"] = msg;
  results.Append(cnetpp::base::Value(result));

  cnetpp::http::HttpResponse response;
  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

bool Stale2Handler::GetHosts(const std::string& body,
    std::unordered_set<std::string>* hosts) {
  auto found = body.find("\r\n\r\n");
  if (found == std::string::npos) {
    return false;
  }
  std::string tmp = body.substr(found + 4);
  found = tmp.find("\r\n");
  if (found == std::string::npos) {
    return false;
  }
  tmp.resize(found);
  size_t last_pos = 0;
  for (size_t i = 0; i <= tmp.length(); i++) {
    if (i == tmp.length() || tmp[i] == ',') {
      if (i - last_pos > 0) {
        std::string host(&tmp[last_pos], i - last_pos);
        cnetpp::base::StringUtils::Trim(&host);
        if (hosts->find(host) == hosts->end()) {
          hosts->emplace(std::move(host));
        }
      }
      last_pos = i + 1;
    }
  }
  return true;
}

}  // namespace dancenn

