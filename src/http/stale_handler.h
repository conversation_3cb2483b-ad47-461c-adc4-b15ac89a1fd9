// Copyright 2021 Mu <PERSON> <<EMAIL>>

#ifndef HTTP_STALE_HANDLER_H_
#define HTTP_STALE_HANDLER_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <string>
#include <mutex>
#include <unordered_set>

#include "http/http_handler.h"
#include "datanode_manager/datanode_manager.h"

namespace dancenn {

class StaleHandler : public HttpHandler {
 public:
  explicit StaleHandler(
      std::shared_ptr<DatanodeManager> datanode_manager)
      : datanode_manager_(datanode_manager) {}
  ~StaleHandler() override = default;

  const std::string& name() const override {
    return name_;
  }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

  // just for unittest
  size_t GetStaleDnSize() const {
    return stale_dns_.size();
  }

 private:
  cnetpp::http::HttpResponse HandleGet(
      const cnetpp::http::HttpRequest& request);

  cnetpp::http::HttpResponse HandlePost(
      const cnetpp::http::HttpRequest& request);

  cnetpp::http::HttpResponse GetResponse(
      const HttpStatusCode code, const std::string& msg);

  bool GetHosts(const std::string& body,
                std::unordered_set<std::string>* hosts);

 private:
  friend class StaleHandlerTest;
  const std::string name_ { "/stale" };
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::mutex in_progress_mutex_;

  std::unordered_set<DatanodeID> stale_dns_;
  std::unordered_set<std::string> all_host_;
};

}  // namespace dancenn

#endif  // HTTP_STALE_HANDLER_H_

