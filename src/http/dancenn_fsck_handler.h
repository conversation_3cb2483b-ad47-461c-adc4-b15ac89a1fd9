// Copyright 2018 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef HTTP_DANCENN_FSCK_HANDLER_H_
#define HTTP_DANCENN_FSCK_HANDLER_H_

#include <string>

#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "http/http_handler.h"
#include "namespace/namespace.h"

namespace dancenn {

class DancennFsckHandler : public HttpHandler {
 public:
  DancennFsckHandler(std::shared_ptr<NameSpace> ns,
                     std::shared_ptr<BlockManager> bm)
      : ns_(ns), bm_(bm) {}
  ~DancennFsckHandler() override = default;

  const std::string& name() const override { return name_; }

  cnetpp::http::HttpResponse Handle(
      const cnetpp::http::HttpRequest& request) override;

 private:
  const std::string name_{"/fsck"};
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> bm_;
};

}  // namespace dancenn

#endif  // HTTP_DANCENN_FSCK_HANDLER_H_
