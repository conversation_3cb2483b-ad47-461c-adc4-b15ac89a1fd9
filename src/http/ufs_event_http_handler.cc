//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

// The Header
#include "http/ufs_event_http_handler.h"

// Project
#include "base/to_json_string.h"

namespace dancenn {

cnetpp::http::HttpResponse Return(HttpStatusCode code,
                                  const std::string& body = "") {
  if (body.empty()) {
    cnetpp::http::HttpResponse response;
    response.set_status(code);
    return response;
  }

  cnetpp::http::HttpResponse response;
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(body.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(body);
  return response;
}

cnetpp::http::HttpResponse UfsEventHttpHandler::Handle(
    const cnetpp::http::HttpRequest& request) {
  CHECK_NOTNULL(mgr_);

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return Return(HttpStatusCode::kBadRequest);
  }
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());

  std::string cmd = "";
  if (!GetStringFromParams(param_map, "cmd", &cmd)) {
    return Return(HttpStatusCode::kBadRequest, "Param `cmd` is required");
  }

  if (cmd == "status") {
    return HandleStatus(param_map);
  } else if (cmd == "start_consumer") {
    return HandleStartConsumer(param_map);
  } else if (cmd == "stop_consumer") {
    return HandleStopConsumer(param_map);
  } else if (cmd == "clear_error") {
    return HandleClearError(param_map);
  } else {
    return Return(HttpStatusCode::kBadRequest,
                  absl::StrFormat("Invalid cmd: %s", cmd));
  }
}

cnetpp::http::HttpResponse UfsEventHttpHandler::HandleStatus(
    const ParamMap& params) {
  return Return(HttpStatusCode::kOk, mgr_->GetStatus().ToJson() + "\n");
}

cnetpp::http::HttpResponse UfsEventHttpHandler::HandleStartConsumer(
    const ParamMap& params) {
  mgr_->StartConsumers();
  return Return(HttpStatusCode::kOk, "OK\n");
}

cnetpp::http::HttpResponse UfsEventHttpHandler::HandleStopConsumer(
    const ParamMap& params) {
  mgr_->StopConsumers();
  return Return(HttpStatusCode::kOk, "OK\n");
}

cnetpp::http::HttpResponse UfsEventHttpHandler::HandleClearError(
    const ParamMap& params) {
  mgr_->ClearError();
  return Return(HttpStatusCode::kOk, "OK\n");
}

}  // namespace dancenn
