//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include "http/dancenn_fs_handler.h"

#include "base/path_util.h"
#include "namespace/namespace.h"
#include "namespace/namespace_stat.h"

DECLARE_bool(dancenn_observe_mode_on);

namespace dancenn {

using HttpResponse = cnetpp::http::HttpResponse;

HttpResponse ReturnError(HttpStatusCode code, const char* error_msg = nullptr) {
  if (nullptr == error_msg) {
    HttpResponse response;
    response.set_status(code);
    return response;
  }

  cnetpp::base::Object results;
  results["error"] = std::string(error_msg);

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  HttpResponse response;
  response.set_status(code);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse
FSHandler::Handle(const cnetpp::http::HttpRequest& request) {
  CHECK_NOTNULL(ns_);

  cnetpp::base::Uri uri;
  if (!uri.ParseUriPath(request.uri())) {
    return ReturnError(HttpStatusCode::kBadRequest);
  }
  auto params = uri.QueryParams();
  ParamMap param_map(params.begin(), params.end());

  std::string cmd = "ls";
  GetStringFromParams(param_map, "cmd", &cmd);
  std::string path = "/";
  GetStringFromParams(param_map, "path", &path);

  if (cmd == "ls") {
    return HandleListCommand(path, param_map, false);
  } else if (cmd == "ls2") {
    return HandleListCommand(path, param_map, true);
  } else if (cmd == "stat") {
    return HandleStatCommand(path, param_map);
  }

  return ReturnError(HttpStatusCode::kBadRequest,
                     ("Invalid cmd: " + cmd).c_str());
}

void FSHandler::BuildFileStatus(const HdfsFileStatusProto& f,
                                cnetpp::base::Object* file_out) {
  (*file_out)["name"] = GetFileNameFromPath(f.path());
  bool is_dir = f.filetype() == cloudfs::HdfsFileStatusProto_FileType_IS_DIR;
  (*file_out)["is_dir"] = is_dir;
  (*file_out)["owner"] = f.owner();
  (*file_out)["group"] = f.group();
  (*file_out)["modify_time"] = f.modification_time();
  (*file_out)["access_time"] = f.access_time();
  (*file_out)["length"] = f.length();
  (*file_out)["is_pinned"] = f.pinned();

  if (f.has_locations()) {
    cnetpp::base::Array is_cached;
    auto&& locations = f.locations();
    for (auto&& block : locations.blocks()) {
      bool block_is_cached = false;
      for (bool cached : block.iscached()) {
        if (cached) {
          block_is_cached = true;
          break;
        }
      }
      is_cached.Append(cnetpp::base::Value(block_is_cached));
    }
    (*file_out)["is_cached"] = is_cached;
  }
}

cnetpp::http::HttpResponse
FSHandler::HandleStatCommand(const std::string& path,
                             const ParamMap& param_map) {
  bool need_location = false;
  GetBoolFromParams(param_map, "need_location", &need_location);

  HdfsFileStatusProto rsp;
  Status s = Status::OK();
  if (ns_->IsAccMode()) {
    auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_fs_info.set_syncinterval(-1);
    s = acc_ns_->GetFileInfoForHttp(path, need_location, acc_fs_info, &rsp);
  } else {
    s = ns_->GetFileInfoForHttp(path, need_location, &rsp);
  }

  if (!s.IsOK()) {
    if (s.code() == Code::kBadParameter) {
      return ReturnError(HttpStatusCode::kBadRequest, s.message().c_str());
    }
    if (s.code() == Code::kFileNotFound) {
      return ReturnError(HttpStatusCode::kNotFound, s.message().c_str());
    }
    return ReturnError(HttpStatusCode::kInternalServerError,
                       s.ToString().c_str());
  }

  cnetpp::base::Object results;

  results["path"] = path;

  cnetpp::base::Object file;
  BuildFileStatus(rsp, &file);
  results["file"] = file;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

cnetpp::http::HttpResponse
FSHandler::HandleListCommand(const std::string& path,
                             const ParamMap& param_map,
                             bool allow_list_file) {
  std::string start_after;
  GetStringFromParams(param_map, "start_after", &start_after);
  uint32_t count = 100;
  GetUInt32FromParams(param_map, "count", &count);
  bool need_location = false;
  GetBoolFromParams(param_map, "need_location", &need_location);

  GetListingForHttpResponse rsp;
  Status s = Status::OK();
  if (ns_->IsAccMode()) {
    auto acc_fs_info = acc_ns_->MakeDefaultAccFsInfo();
    acc_fs_info.set_syncinterval(-1);
    s = acc_ns_->GetListingForHttp(path,
                                   start_after,
                                   count,
                                   need_location,
                                   allow_list_file,
                                   acc_fs_info,
                                   &rsp);
  } else {
    s = ns_->GetListingForHttp(
        path, start_after, count, need_location, allow_list_file, &rsp);
  }
  if (!s.IsOK()) {
    if (s.code() == Code::kBadParameter) {
      return ReturnError(HttpStatusCode::kBadRequest, s.message().c_str());
    }
    if (s.code() == Code::kFileNotFound) {
      return ReturnError(HttpStatusCode::kNotFound, s.message().c_str());
    }
    return ReturnError(HttpStatusCode::kInternalServerError,
                       s.ToString().c_str());
  }

  cnetpp::base::Object results;

  results["path"] = path;
  results["has_more"] = rsp.has_more;
  {
    std::string next_start_after;
    if (rsp.has_more) {
      if (rsp.files.empty()) {
        // Defensive case
        next_start_after = start_after;
      } else {
        next_start_after = GetFileNameFromPath(rsp.files.back().path());
      }
      results["next_start_after"] = next_start_after;
    }
  }

  cnetpp::base::Array files;
  for (auto&& f : rsp.files) {
    cnetpp::base::Object tmp_file;
    BuildFileStatus(f, &tmp_file);
    files.Append(cnetpp::base::Value(tmp_file));
  }
  results["files"] = files;

  std::string str_results;
  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), &str_results);

  cnetpp::http::HttpResponse response;
  response.set_status(cnetpp::http::HttpResponse::StatusCode::kOk);
  response.SetHttpHeader("Content-Length", std::to_string(str_results.size()));
  response.SetHttpHeader("Content-Type", "application/json");
  response.set_http_body(str_results);
  return response;
}

} // namespace dancenn
