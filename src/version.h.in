// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef VERSION_H_
#define VERSION_H_
#include <string>

namespace dancenn {

static const std::string kReleaseVersion =R"(@@version@@)";

static const std::string kVcsVersion =R"(@@build_version@@)";
static const std::string kVcsBuildPath =R"(@@build_path@@)";
static const std::string kCommitMsg = R"(@@commit_msg@@)";

}  // namespace dancenn

#endif  // VERSION_H_
