// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <cnetpp/concurrency/thread_pool.h>

namespace dancenn {
namespace mockdn {

// Deprecated
// We use a thread pool to execute all DN commands
// rather than one thread per DN
class Dispatcher {
 public:
  Dispatcher() {}
  virtual ~Dispatcher() = default;

  void Start() {
    workers_ = std::make_unique<cnetpp::concurrency::ThreadPool>(
        "MDispatcher", true);
    workers_->set_num_threads(16);
    workers_->Start();
  }
  void Stop() {
    if (workers_) {
      workers_->Stop();
      workers_.reset();
    }
  }

  void Dispatch(std::shared_ptr<cnetpp::concurrency::Task> task) {
    workers_->AddTask(task);
  }
  void DispatchAfter(std::shared_ptr<cnetpp::concurrency::Task> task,
                     std::chrono::microseconds after) {
    workers_->AddDelayTask(task, after);
  }

 private:
  std::unique_ptr<cnetpp::concurrency::ThreadPool> workers_;
};

}  // namespace mockdn
}  // namespace dancenn

