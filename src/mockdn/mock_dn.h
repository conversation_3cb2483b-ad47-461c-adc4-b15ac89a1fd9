// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <cnetpp/base/end_point.h>

#include "DatanodeProtocol.pb.h"
#include <service/datanode_service.h>

namespace dancenn {

namespace mockdn {

class Dispacher;

// Deprecated
class MockDN {
 public:
  MockDN(const cnetpp::base::EndPoint& ep,
         size_t disk_capacity,
         size_t ssd_capacity)
      : end_point_(ep),
        disk_capacity_(disk_capacity),
        ssd_capacity_(ssd_capacity) {
  }
  virtual ~MockDN() = default;

  void Start() {}
  void Stop() {}

  void set_dispatcher(Dispacher* dispatcher) {
    dispatcher_ = dispatcher;
  }

  void set_datanode_service(DatanodeService* service) {
    service_ = service;
  }

 private:
  const cnetpp::base::EndPoint end_point_;
  size_t disk_capacity_;
  size_t ssd_capacity_;
  Dispacher* dispatcher_{nullptr};
  DatanodeService* service_{nullptr};

  // std::list<Block> blocks_on_disk_;
  // std::list<Block> blocks_on_ssd_;

  cloudfs::NamespaceInfoProto VersionRequest();
  void Register();
  cloudfs::datanode::HeartbeatResponseProto SendHeartbeat();
  void BlockReceivedAndDeleted();
  void BlockReport();

};

}  // namespace mockdn

}  // namespace dancenn

