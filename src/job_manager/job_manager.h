//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include <cstdint>
#include <memory>
#include <vector>

#include "ClientNamenodeProtocol.pb.h"
#include "base/status.h"
#include "datanode_manager/datanode_info.h"
#include "edit_log.pb.h"
#include "hdfs.pb.h"
#include "job_manager/job_manager_def.h"
#include "job_manager/workflow.h"
#include "job_manager/workflow_id_generator.h"
#include "job_manager_metrics.h"
#include "job_tracker.h"
#include "managed_job_handler.h"
#include "managed_task_handler.h"
#include "managed_task_impl.h"
#include "task_creator.h"

namespace dancenn {

class NameSpace;
class ManagedJobHandler;
class ManagedTaskHandler;
class BlockManager;
class DatanodeManager;
class TaskCreator;

class JobManager {
 public:
  JobManager() = default;
  JobManager(std::shared_ptr<DatanodeManager> datanode_manager,
             std::shared_ptr<BlockManager> block_manager);

  ~JobManager() {
    Stop();
  }

  virtual void SetEditLogContext(
      std::shared_ptr<EditLogContextBase> edit_log_ctx);
  virtual void SetEditLogSender(
      std::shared_ptr<EditLogSenderBase> edit_log_sender);
  virtual void SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage);

  virtual ManagedJobId GenerateJobId();

  virtual Status SubmitLoadMultiReplicaJob(INode* inode,
                                           const std::string& path,
                                           std::shared_ptr<DataJobOption> opt,
                                           ManagedJobId* job_id);

  virtual Status SubmitLoadDataJob(INode* inode,
                                   std::shared_ptr<DataJobOption> opt,
                                   ManagedJobId* job_id,
                                   const std::vector<DatanodeID>& targets = std::vector<DatanodeID>());

  virtual Status SubmitFreeDataJob(INode* inode,
                                   const std::string& path,
                                   std::shared_ptr<DataJobOption> opt,
                                   ManagedJobId* job_id);

  virtual Status SubmitJob(INode* inode,
                           std::shared_ptr<DataJobOption> opt,
                           std::shared_ptr<TaskCreator>& task_creator,
                           ManagedJobId* job_id);

  virtual Status SubmitReconcileINodeAttrsJob(
      const INode& inode,
      const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
      std::shared_ptr<TaskCreator> task_creator,
      ManagedJobId* job_id);

  virtual void set_ns(std::shared_ptr<NameSpace> ns) {
    ns_ = ns;
  }
  virtual std::shared_ptr<NameSpace> get_ns() {
    return ns_.lock();
  }

  virtual void Start();

  virtual void Stop();

  virtual bool IsRunning() const {
    return running_.load();
  }

  virtual const std::shared_ptr<JobTracker>& GetJobTracker() const {
    return job_tracker_;
  }

  virtual void ListSubINodes(INode* curr_inode,
                             std::string& start_after,
                             bool& has_more,
                             std::vector<INode>* inodes);

  virtual void ConstructLocatedBlocks(const std::shared_ptr<INode>& inode,
                                      cloudfs::LocatedBlocksProto* lb,
                                      std::vector<bool>& block_task_need_submit,
                                      const ManagedJobType& task_type,
                                      const std::string* ufs_key);

  virtual DatanodeID GetDatanodeInterId(const std::string& dn_uuid);

  virtual bool AddLoadCmd(
      DatanodeID dn_id,
      const std::shared_ptr<cloudfs::ExtendedBlockProto> block);

  virtual void InvalidBlockCmd(
      const std::shared_ptr<cloudfs::ExtendedBlockProto> block) const;

  virtual void TrackBlockTask(uint64_t blk_id, BlockStatus status) const;
  // bool IsBlockPersisted(const cloudfs::LocatedBlockProto& lb);

  virtual DetailedBlock GetDetailedBlock(uint64_t blk_id) const;

  virtual void CancelTask(uint64_t blk_id);

  virtual void UpdateJobStatusHA(const std::string& job_id,
                                 const ManagedJobType& job_type,
                                 const JobStatusOpBody& job_status,
                                 bool uncached = false,
                                 bool notify_standby = false) const;

  virtual Status LookupJobState(const ManagedJobId& job_id,
                                ManagedJobState& job_state) const;

  virtual Status CancelJob(const ManagedJobId& job_id);

  virtual Status CompleteJob(const ManagedJobId& job_id,
                             const WorkflowState state);

  virtual Status ListJob(std::vector<ManagedJobId>& jobs,
                         ManagedJobType job_type);

  virtual std::unique_ptr<rocksdb::Iterator> GetJobInfoIterator() const;

  std::shared_ptr<BlockManager> GetBlockManager() const;

  std::shared_ptr<NameSpace> GetNameSpace() const;

  std::shared_ptr<DatanodeManager> GetDatanodeManager() const;

  std::shared_ptr<ManagedTaskHandler> GetTaskHandler() const;

  virtual Status CheckJobManagerStatus() const;

  void SetNsIdToPufsNameCB(std::function<std::string(uint64_t)> cb);
  std::function<std::string(uint64_t)>& GetNsIdToPufsNameCB();


 private:
  std::weak_ptr<NameSpace> ns_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::weak_ptr<BlockManager> block_manager_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MetaStorage> meta_storage_;

  std::shared_ptr<ManagedJobHandler> job_handler_;
  std::shared_ptr<ManagedTaskHandler> task_handler_;
  std::shared_ptr<JobTracker> job_tracker_;
  std::atomic<bool> running_{false};

  ReadWriteLock load_cmd_rwlock_;
  std::thread batch_cmd_manager_;
  std::unordered_map<DatanodeID,
                     std::vector<std::shared_ptr<cloudfs::ExtendedBlockProto>>>
      batch_load_cmd_;
  std::function<std::string(uint64_t)> nsid_to_pufs_cb_;

  friend class JobManagerMetrics;
  friend class JobManagerTest;
  friend class ManagedTaskTest;
  std::unique_ptr<JobManagerMetrics> metrics_;
};
}  // namespace dancenn
