#include "task_iterator.h"

namespace dancenn {

ReconcileINodeAttrsTaskIterator::ReconcileINodeAttrsTaskIterator(
    std::shared_ptr<TaskCreator> task_creator,
    std::shared_ptr<ReconcileINodeAttrsJobOption> opt)
    : task_creator_(task_creator), opt_(opt) {
  current_task_ = task_creator_->ConstructTask();
}

bool ReconcileINodeAttrsTaskIterator::HasNext() {
  return current_task_ != nullptr;
}

Status ReconcileINodeAttrsTaskIterator::Next(
    std::shared_ptr<ManagedTask>& task) {
  task = current_task_;
  current_task_ = task_creator_->ConstructTask();
  if (task == nullptr) {
    return Status(Code::kTaskNotFound, "");
  } else {
    return Status::OK();
  }
}

}  // namespace dancenn
