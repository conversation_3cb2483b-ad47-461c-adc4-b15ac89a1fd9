//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "job_tracker.h"

#include <unistd.h>

#include <algorithm>
#include <cstdint>
#include <memory>
#include <shared_mutex>
#include <string>
#include <unordered_set>
#include <utility>

#include "base/read_write_lock.h"
#include "base/logger_metrics.h"
#include "base/status.h"
#include "edit_log.pb.h"
#include "glog/logging.h"
#include "job_manager/managed_job_state.h"
#include "job_manager/workflow.h"
#include "managed_job.h"
#include "managed_job_state.h"
#include "managed_task_impl.h"
#include "workflow.h"
#include "workflow_id_generator.h"

DECLARE_uint32(managed_job_state_cached_ms);
DECLARE_uint32(job_tracker_max_pending_block_tasks);
DECLARE_uint32(job_tracker_max_pending_autonomous_tasks);
DECLARE_uint32(job_tracker_max_pending_job);
DECLARE_uint32(job_tracker_max_shared_task);
DECLARE_uint32(job_tracker_inspect_period_sec);
DECLARE_uint32(job_tracker_metastorage_inspect_period_sec);
DECLARE_uint32(job_tracker_worker_retry_sleep_us);
DECLARE_int32(job_tracker_worker_retry_times);
DECLARE_bool(run_ut);

namespace dancenn {

void JobTracker::PrintStatistic() {
  VLOG(10) << "Statistic for job tracker job_id_to_progress_ : "
           << job_id_to_progress_.size()
           << "; blk_id_to_tasks_ : " << blk_id_to_tasks_.size();
}

void JobTracker::Start() {
  LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker starting.";
  job_tracker_workers_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("JobTracker", true);
  job_tracker_workers_->set_num_threads(FLAGS_track_block_task_thread_num);
  job_tracker_workers_->set_max_num_pending_tasks(
      FLAGS_track_block_task_pool_queue_size);
  job_tracker_workers_->Start();
  LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker workers started.";

  running_.store(true);
  inspect_job_thread_ = std::thread([this]() {
    std::unique_lock<std::mutex> lock(inspect_job_thread_cv_mutex_);

    while (IsRunning()) {
      inspect_job_thread_cv_.wait_for(
          lock,
          std::chrono::seconds(FLAGS_job_tracker_inspect_period_sec),
          [&]() { return !IsRunning(); });

      PrintStatistic();
      if (job_id_to_progress_.empty()) {
        continue;
      }

      VLOG(10) << "Inspect job state";
      StopWatch sw(metrics_->inspect_block_tasks_timer);
      sw.Start();
      InspectBlockTasks();
      sw.NextStep(metrics_->inspect_autonomous_tasks_timer);
      InspectAutonomousTasks();
      sw.NextStep(metrics_->inspect_job_timer);
      InspectManagedJob();

      MFC(metrics_->tracker_inspector_counter)->Inc();
    }
  });

  inspect_job_meta_storage_ = std::thread([this]() {
    std::unique_lock<std::mutex> lock(inspect_job_meta_storage_cv_mutex_);

    while (IsRunning()) {
      VLOG(10) << "Inspect job meta storage";

      inspect_job_meta_storage_cv_.wait_for(
          lock,
          std::chrono::seconds(
              FLAGS_job_tracker_metastorage_inspect_period_sec),
          [&]() { return !IsRunning(); });
      
      if (!IsRunning()) {
        break;
      }

      StopWatch sw(metrics_->inspect_ms_timer);
      sw.Start();
      InspectMetaStorageJobInfo();
    }
  });

  LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker started.";
}

void JobTracker::Stop() {
  LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker stopping.";
  if (!running_.load()) {
    LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker stopped.";
    return;
  }
  running_.store(false);

  {
    std::unique_lock<ReadWriteLock> wlock_at(autonomous_task_rwlock_);
    for (auto& task : autonomous_tasks_) {
      task->Cancel();
    }
  }

  {
    std::unique_lock<ReadWriteLock> wlock_jp(job_progress_rwlock_);
    job_id_to_progress_.clear();
  }

  {
    std::unique_lock<ReadWriteLock> wlock_bt(blk_task_rwlock_);
    blk_id_to_tasks_.clear();
  }

  inspect_job_thread_cv_.notify_all();
  inspect_job_meta_storage_cv_.notify_all();
  inspect_job_thread_.join();
  inspect_job_meta_storage_.join();

  job_tracker_workers_->Stop();
  LOG_IF(INFO, !FLAGS_run_ut) << "Job tracker stopped.";
}

bool JobTracker::ThrottleTaskRegister() {
  if (job_id_to_progress_.size() > FLAGS_job_tracker_max_pending_job) {
    metrics_->task_register_throttled_counter->Inc();
    LOG(INFO) << "Too many pending job in job tracker "
                 << job_id_to_progress_.size();
    return true;
  } else if (blk_id_to_tasks_.size() >
             FLAGS_job_tracker_max_pending_block_tasks) {
    metrics_->task_register_throttled_counter->Inc();
    LOG(INFO) << "Too many pending block tasks in job tracker "
                 << blk_id_to_tasks_.size();
    return true;
  } else if (autonomous_tasks_.size() >
             FLAGS_job_tracker_max_pending_autonomous_tasks) {
    metrics_->task_register_throttled_counter->Inc();
    LOG(INFO) << "Too many pending autonomous tasks in job tracker "
                 << autonomous_tasks_.size();
    return true;
  } else {
    return false;
  }
}

bool JobTracker::RegisterJob(std::shared_ptr<ManagedJob> job) {
  StopWatch sw(metrics_->register_job_timer);
  sw.Start();

  auto&& job_id = job->GetJobId();
  auto&& job_type = job->GetJobType();

  std::unique_lock<ReadWriteLock> wlock(job_progress_rwlock_);
  auto iter = job_id_to_progress_.find(job_id);
  if (iter == job_id_to_progress_.end()) {
    VLOG(10) << "Register Job " << job_id
             << " job_type=" << ManagedJobTypeInfo::GetName(job_type);
    metrics_->job_registed_counter->Inc();

    auto res = job_id_to_progress_.emplace(job_id, job);
    if (!res.second) {
      return false;
    }
  } else {
    LOG(INFO) << "Job " << job_id << " has been registered";
    return false;
  }

  VLOG(10) << "Register job "
           << ManagedJobTypeInfo::GetName(job_type)
           << " id: " << job_id;
  return true;
}

Status JobTracker::RegisterTask(std::shared_ptr<ManagedTask> task) {
  StopWatch sw(metrics_->register_task_timer);
  sw.Start();
  bool throttled = false;
  if (ThrottleTaskRegister()) {
    LOG(INFO) << "Register task is throttled. Job ID : " << task->GetJobId()
                 << " ; task ID : " << task->GetTaskId();
    throttled = true;
  } else {
    metrics_->task_registed_counter->Inc();
  }

  {
    std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);

    auto job_id = task->GetJobId();
    auto iter = job_id_to_progress_.find(job_id);
    if (iter == job_id_to_progress_.end()) {
      LOG(INFO) << "Failed to find job " << job_id;
      return Status(Code::kJobNotFound, "job not found");
    }
    ManagedJobState& job_state = iter->second->GetJobState();
    job_state.AddTotalTaskNum();
    if (throttled) {
      // There is no need to submit the task. Update the status of the job
      // directly
      job_state.AddThrottledTaskNum();
      LOG(INFO) << task->ToString() << " be throttled " << job_id;
      return Status(Code::kThrottled, "throttled");
    }
  }

  auto&& job_type = task->GetTaskType();
  if (job_type == ManagedJobType::LOAD_DATA ||
      job_type == ManagedJobType::FREE_DATA) {
    std::shared_ptr<ManagedBlockTask> block_task =
        std::dynamic_pointer_cast<ManagedBlockTask>(task);
    if (block_task == nullptr) {
      LOG_WITH_LEVEL(ERROR)
          << "Failed to cast to ManagedBlockTask " << task->ToString();
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(Code::kError, "Failed to cast to ManagedBlockTask");
    }
    return RegisterBlockTask(block_task->getTaskBlockId(), task);
  } else if (job_type == ManagedJobType::LOAD_METADATA ||
             job_type == ManagedJobType::SET_REPLICATION || 
             job_type == ManagedJobType::RESIDENT_DATA) {
    return RegisterAutonomousTask(task);
  } else if (job_type == ManagedJobType::RECONCILE_INODE_ATTRS) {
    return RegisterAutonomousTask(task);
  } else if (job_type == ManagedJobType::CHAIN) {
    std::shared_ptr<ChainTask> chain_task =
        std::dynamic_pointer_cast<ChainTask>(task);
    if (chain_task == nullptr) {
      LOG(ERROR) << "Failed to cast to ChainTask " << task->ToString();
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(Code::kError, "Failed to cast to ChainTask");
    }
    for (auto& sub_task : chain_task->SubTasks()) {
      std::shared_ptr<ManagedBlockTask> block_task =
          std::dynamic_pointer_cast<ManagedBlockTask>(sub_task);
      if (block_task != nullptr) {
        return RegisterBlockTask(block_task->getTaskBlockId(), task);
      }
    }
    return RegisterAutonomousTask(task);
  } else {
    LOG(WARNING) << "Failed to register " << task->ToString();
    return Status(Code::kError, "Failed to register");
  }
}

Status JobTracker::RegisterAutonomousTask(std::shared_ptr<ManagedTask>& task) {
  std::unique_lock<ReadWriteLock> wlock(autonomous_task_rwlock_);
  auto res = autonomous_tasks_.emplace(task);
  if (res.second) {
    return Status::OK();
  } else {
    return Status(Code::kError, "Failed to register task");
  }
}

Status JobTracker::RegisterBlockTask(
    uint64_t block_id,
    std::shared_ptr<ManagedTask>& register_task) {
  // Do not submit new task For task with the same job type
  bool shared_task = false;

  {
    std::shared_lock<ReadWriteLock> rlock(blk_task_rwlock_);
    auto blk_id_tasks_iter = blk_id_to_tasks_.find(block_id);
    if (blk_id_tasks_iter != blk_id_to_tasks_.end()) {
      auto tasks_set = blk_id_tasks_iter->second;
      if (tasks_set.size() >= FLAGS_job_tracker_max_shared_task) {
        LOG(INFO) << "Too many shared tasks for " << block_id;
        return Status(Code::kThrottled, "throttled");
      }
      auto register_task_type = register_task->GetTaskType();

      for (auto& task : tasks_set) {
        if (register_task_type == task->GetTaskType()) {
          metrics_->shared_task_counter->Inc();
          LOG(INFO) << ManagedJobTypeInfo::GetName(register_task_type)
                    << " Task shared for " << block_id;
          shared_task = true;
          break;
        }
      }
    }
  }

  {
    std::unique_lock<ReadWriteLock> wlock(blk_task_rwlock_);
    VLOG(10) << register_task->ToString() << " register block task "
             << block_id;
    blk_id_to_tasks_[block_id].emplace(register_task);
  }

  if (!shared_task) {
    return Status::OK();
  } else {
    return Status(Code::kTaskHasSubmitted, "shared task");
  }
}

void JobTracker::SetJobSubmitted(ManagedJobId job_id) {
  std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);
  auto iter = job_id_to_progress_.find(job_id);
  if (iter != job_id_to_progress_.end()) {
    metrics_->job_submitted_counter->Inc();
    ManagedJobState& job_state = iter->second->GetJobState();
    job_state.SetJobSubmitted();
  } else {
    LOG(INFO) << "Not found job info for " << job_id;
  }
}

void JobTracker::InspectMetaStorageJobInfo() {
  auto start = TimeUtil::GetNowEpochMs();
  uint64_t uncached_job = 0;
  uint64_t meta_storage_size = 0;

  auto it = job_manager_->GetJobInfoIterator();
  if (!it) {
    LOG_IF(ERROR, !FLAGS_run_ut) << "Failed to get job info iterator";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }
  it->SeekToFirst();
  for (; it->Valid(); it->Next()) {
    meta_storage_size++;
    JobInfoOpBody job_info_op;
    job_info_op.ParseFromString(it->value().ToString());

    auto is_job_complete =
        WorkflowStateInfo::IsWorkflowDone(job_info_op.job_status().state());
    if (is_job_complete) {
      auto job_status_op = job_info_op.job_status();
      auto complete_time = job_status_op.complete_timestamp();
      auto now = TimeUtil::GetNowEpochMs();
      if ((now - complete_time) > FLAGS_managed_job_state_cached_ms) {
        uncached_job++;
        auto job_id = it->key().ToString();
        auto job_type =
            ManagedJobTypeInfo::ConvertToJobInfoType(job_info_op.job_type());
        job_manager_->UpdateJobStatusHA(job_id, job_type, job_status_op, true);
        MFC(metrics_->tracker_job_metastorage_recycle_counter)->Inc();
      }
    } else {
      auto job_id = it->key().ToString();
      if (!ContainJob(job_id)) {
        // Double check job info in meta storage
        ManagedJobState job_state;
        Status status = job_manager_->LookupJobState(job_id, job_state);
        if (status.IsOK()) {
          if (!job_state.IsComplete()) {
            if (job_info_op.job_type() ==
                JobInfoOpBody_Type_ReconcileINodeAttrsJob) {
              ManagedJobId job_id = job_info_op.job_id();
              std::shared_ptr<ReconcileINodeAttrsJobOption> opt =
                  std::make_shared<ReconcileINodeAttrsJobOption>();
              opt->path = job_info_op.reconcile_inode_attr_job_status().path();
              auto ns = job_manager_->get_ns();
              if (ns == nullptr) {
                LOG(INFO) << "Namespace is null";
                continue;
              }
              std::shared_ptr<TaskCreator> task_creator =
                  std::make_shared<ReconcileINodeAttrsTaskCreator>(
                      ManagedJobType::RECONCILE_INODE_ATTRS,
                      job_info_op.reconcile_inode_attr_job_status().inode(),
                      opt,
                      INVALID_JOB_ID,
                      job_manager_,
                      ns.get(),
                      ns->GetMetaStorage());
              job_manager_->SubmitReconcileINodeAttrsJob(
                  job_info_op.reconcile_inode_attr_job_status().inode(),
                  opt,
                  task_creator,
                  &job_id);
            } else {
              auto job_type = ManagedJobTypeInfo::ConvertToJobInfoType(
                  job_info_op.job_type());
              LOG(INFO) << "Delete invalid job " << job_id
                        << " from metastorage";
              job_manager_->UpdateJobStatusHA(
                  job_id, job_type, job_info_op.job_status(), true);
            }
          }
        } else {
          LOG(WARNING) << "Failed to lookup job for " << job_id;
        }
      }
    }
  }
  auto end = TimeUtil::GetNowEpochMs();
  LOG(INFO) << meta_storage_size << " jobs in metastorage; " << uncached_job
            << " jobs are uncached cost " << (end - start) << " ms";
}

void JobTracker::InspectManagedJob() {
  std::unique_lock<ReadWriteLock> wlock(job_progress_rwlock_);
  int completed_job = 0;
  auto start = TimeUtil::GetNowEpochMs();

  for (auto iter = job_id_to_progress_.begin();
       iter != job_id_to_progress_.end();) {
    auto& job = iter->second;
    job->RefreshWorkflowState();
    if (job->IsComplete()) {
      iter = job_id_to_progress_.erase(iter);
      completed_job++;
    } else {
      ++iter;
      continue;
    }
  }
  if (completed_job > 0) {
    auto end = TimeUtil::GetNowEpochMs();
    VLOG(10) << completed_job << " jobs are completed cost "
              << (end - start) << " ms";
  }
}

void JobTracker::InspectBlockTasks() {
  std::unique_lock<ReadWriteLock> wlock(blk_task_rwlock_);
  int completed_task = 0;
  auto start = TimeUtil::GetNowEpochMs();

  for (auto blk_id_to_tasks_iter = blk_id_to_tasks_.begin();
       blk_id_to_tasks_iter != blk_id_to_tasks_.end();) {
    ManagedTaskSet& tasks = blk_id_to_tasks_iter->second;
    completed_task += InnerInspectTasks(tasks);

    if (tasks.empty()) {
      blk_id_to_tasks_iter = blk_id_to_tasks_.erase(blk_id_to_tasks_iter);
    } else {
      ++blk_id_to_tasks_iter;
    }
  }

  if (completed_task > 0) {
    auto end = TimeUtil::GetNowEpochMs();
    LOG(INFO) << completed_task << " block tasks are completed cost "
              << (end - start) << " ms";
  }
}

void JobTracker::InspectAutonomousTasks() {
  std::unique_lock<ReadWriteLock> wlock(autonomous_task_rwlock_);
  auto start = TimeUtil::GetNowEpochMs();

  std::unordered_set<std::shared_ptr<ManagedTask>>& tasks = autonomous_tasks_;

  uint32_t completed_task = InnerInspectTasks(tasks, true);

  if (completed_task > 0) {
    auto end = TimeUtil::GetNowEpochMs();
    LOG(INFO) << completed_task << " autonomous tasks are completed cost "
              << (end - start) << " ms";
  }
}

uint32_t JobTracker::InnerInspectTasks(ManagedTaskSet& tasks,
                                       bool statsForUncompletedTask) {
  uint32_t completed_task = 0;
  for (auto task_iter = tasks.begin(); task_iter != tasks.end();) {
    auto& task = *task_iter;
    bool jobStateUpdated = CountForJobState(task);
    if (jobStateUpdated) {
      if (!task->IsDone()) {
        LOG(INFO) << "Uncompleted Task " << task->GetTaskId()
                  << " has been updated job state for " << task->GetJobId();
      }
      VLOG(10) << "JobTracker delete task " << task->ToString();
      task_iter = tasks.erase(task_iter);
      completed_task++;
      continue;
    } else {
      ++task_iter;
    }
    if (!task->IsDone()) {
      if (task->ResubmitTaskIfNeeded()) {
        task_handler_->SubmitTask(task);
      }
    }
  }
  return completed_task;
}

void JobTracker::TrackBlockTask(uint64_t blk_id, BlockStatus status) {
  if (!IsRunning()) {
    return;
  }
  bool task_added = false;
  int retry_times = FLAGS_job_tracker_worker_retry_times;
  while (!task_added && retry_times-- >= 0) {
    task_added =
        job_tracker_workers_->AddTask([blk_id, status, this]() -> bool {
          std::shared_lock<ReadWriteLock> rlock(blk_task_rwlock_);
          auto blk_iter = blk_id_to_tasks_.find(blk_id);
          if (blk_iter == blk_id_to_tasks_.end()) {
            return true;
          }

          VLOG(10) << "Track block task blk id : " << blk_id
                   << " status : " << BlockStatusInfo::GetName(status);
          metrics_->tracked_block_task_counter->Inc();

          auto tasks = blk_iter->second;
          std::for_each(
              tasks.begin(),
              tasks.end(),
              [status, this](const std::shared_ptr<ManagedTask> task) {
                task->ProcessBlockReport(status);
              });

          return true;
        });
    if (!task_added) {
      LOG(INFO) << "Track block task failed, retry "
                << retry_times;
      usleep(FLAGS_job_tracker_worker_retry_sleep_us);
    }
  }

  if (!task_added) {
    metrics_->add_task_tracker_failed_counter->Inc();
    LOG(WARNING)
        << "Failed to add track block task. The thread pool running task: "
        << job_tracker_workers_->NumRunningTasks()
        << " pending task: " << job_tracker_workers_->PendingCount();
  }
}

bool JobTracker::CountForJobState(std::shared_ptr<ManagedTask> task) {
  std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);
  auto& job_id = task->GetJobId();
  auto job_iter = job_id_to_progress_.find(job_id);
  if (job_iter == job_id_to_progress_.end()) {
    LOG(INFO) << "Failed to find job info for job " << job_id;
    return true;
  }

  auto& job_state = job_iter->second->GetJobState();
  return task->CountForJobState(job_state);
}

Status JobTracker::LookupJobState(const ManagedJobId& job_id,
                                  ManagedJobState& job_state) {
  std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);
  auto job_iter = job_id_to_progress_.find(job_id);
  if (job_iter != job_id_to_progress_.end()) {
    job_state = job_iter->second->GetJobState();
    return Status();
  } else {
    return Status(Code::kJobNotFound, "Job " + job_id + " not found");
  }
}

Status JobTracker::CompleteJob(const ManagedJobId& job_id,
                               const WorkflowState state) {
  std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);
  auto job_iter = job_id_to_progress_.find(job_id);
  if (job_iter != job_id_to_progress_.end()) {
    ManagedJobState& job_state = job_iter->second->GetJobState();
    job_state.CompleteState(state);
    return Status();
  } else {
    return Status(Code::kJobNotFound, "Job " + job_id + " not found");
  }
}

Status JobTracker::CancelJob(const ManagedJobId& job_id) {
  {
    std::shared_lock<ReadWriteLock> rlock(autonomous_task_rwlock_);
    for (auto& task : autonomous_tasks_) {
      if (task->GetJobId() == job_id) {
        task->Cancel();
        // For now, there is no need to consider the scenario where one job ID
        // is associated with multiple tasks.
        return Status();
      }
    }
  }
  return CompleteJob(job_id, WorkflowState::CANCELED);
}

Status JobTracker::ListJob(std::vector<ManagedJobId>& jobs,
                           ManagedJobType job_type) {
  std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);

  for (auto& iter : job_id_to_progress_) {
    auto job = iter.second;

    if (job_type == ManagedJobType::UNKNOWN) {
      jobs.emplace_back(iter.first);
    } else {
      if (job->GetJobType() == job_type) {
        jobs.emplace_back(iter.first);
      }
    }
  }
  return Status();
}

}  // namespace dancenn
