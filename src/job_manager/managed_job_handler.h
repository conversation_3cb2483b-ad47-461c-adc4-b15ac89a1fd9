//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/concurrency/thread_pool.h>

#include <memory>

#include "base/closure.h"
#include "base/status.h"
#include "managed_job.h"

namespace dancenn {

class ManagedJob;

class ManagedJobHandler {
 public:
  ManagedJobHandler();
  void Start();
  void Stop();
  Status SubmitJob(const std::shared_ptr<ManagedJob>& job);

 private:
  std::unique_ptr<cnetpp::concurrency::ThreadPool> job_workers_;

  friend class ManagedJobHandlerMetrics;
  std::unique_ptr<ManagedJobHandlerMetrics> metrics_;
};
}  // namespace dancenn
