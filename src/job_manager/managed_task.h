//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <chrono>
#include <cstdint>
#include <memory>
#include <unordered_map>

#include "base/time_util.h"
#include "glog/logging.h"
#include "hdfs.pb.h"
#include "job_manager/workflow.h"
#include "job_tracker.h"
#include "managed_job_state.h"
#include "workflow_id_generator.h"

DECLARE_bool(log_managed_task_detail);

namespace dancenn {

class JobManager;
class JobTracker;

class ManagedTask : public cnetpp::concurrency::Task {
 public:
  ManagedTask(ManagedJobType type, ManagedJobId job_id, JobManager* job_manager)
      : type_(type),
        job_id_(job_id),
        job_manager_(job_manager),
        task_id_{WorkflowIdGenerator::Instance().nextTaskId()} {
    metrics_ = std::make_shared<ManagedJobMetrics>(ManagedJobMetrics::Instance());
  }

  bool operator()(void* arg = nullptr) override;

  virtual bool TaskExecTimeout() {
    return false;
  }

  // virtual bool RegisterTask(JobTracker* job_tracker) = 0;

  virtual bool ShouldRetry() {
    return false;
  };

  virtual bool IsTaskPlanIdempotent() {
    return true;
  };

  virtual void AddRetryTimes() {
    retry_times_++;
  }

  virtual bool IsTaskComplete() {
    return false;
  }

  /**
   * @return true if the job status has been successfully computed.
   */
  virtual bool CountForJobState(ManagedJobState& job_state);

  virtual void Cancel() {
    UpdateState(WorkflowState::CANCELED);
  };

  virtual std::string ToString() const {
    std::stringstream ss;
    ss << "task_id: " << task_id_;
    ss << " job_id: " << job_id_;
    ss << " type: " << ManagedJobTypeInfo::GetName(type_);
    ss << " inode_id: " << std::to_string(inode_id_);
    ss << " state: " << WorkflowStateInfo::GetName(state_);
    ss << " last_submit_time: " << last_submit_time_;
    ss << " retry_times: " << retry_times_;
    return ss.str();
  };

  bool ResubmitTaskIfNeeded() {
    if (IsDone()) {
      return false;
    }

    if (IsTaskComplete()) {
      UpdateState(WorkflowState::SUCCESS);
      return false;
    }

    if (!IsTaskPlanIdempotent()) {
      UpdateState(WorkflowState::TIMEOUT);
      return false;
    }

    if (TaskExecTimeout()) {
      if (ShouldRetry()) {
        AddRetryTimes();
        VLOG_OR_IF(10, FLAGS_log_managed_task_detail)
            << "Resubmit task " << ToString() << " for retry";
        return true;
      } else {
        LOG(WARNING) << "Managed task " << ToString() << " timeout and not retry";
        Cancel();
        UpdateState(WorkflowState::FAILED);
        return false;
      }
    } else {
      return false;
    }
  }

  ManagedJobType GetTaskType() const {
    return type_;
  }

  virtual void SetInodeId(uint64_t inode_id) {
    inode_id_ = inode_id;
  }

  uint64_t GetInodeId() const {
    return inode_id_;
  }

  const ManagedJobId& GetJobId() const {
    return job_id_;
  }

  const ManagedTaskId& GetTaskId() const {
    return task_id_;
  }

  virtual void UpdateSubmitTimePoint() {
    last_submit_time_ = TimeUtil::GetNowEpochMs();
  }

  virtual void UpdateState(WorkflowState state, bool track_metrics = true);

  virtual uint64_t GetLastSubmitTime() {
    return last_submit_time_;
  }

  virtual WorkflowState GetWorkflowState() {
    return state_;
  }

  virtual bool IsDone() {
    return WorkflowStateInfo::IsWorkflowDone(state_);
  }

  virtual void ProcessBlockReport(BlockStatus blk_status){};

  uint32_t ScaledRandomNum(uint32_t value, double coefficient);

 protected:
  virtual void SubmitTaskPlan() = 0;

  const ManagedTaskId task_id_;
  const ManagedJobId job_id_;
  const ManagedJobType type_;
  uint64_t inode_id_;
  WorkflowState state_ = WorkflowState::CREATED;
  uint64_t last_submit_time_;
  uint16_t retry_times_{0};
  JobManager* job_manager_{nullptr};
  std::shared_ptr<ManagedJobMetrics> metrics_;
  friend class ManagedTaskTest;
};

}  // namespace dancenn
