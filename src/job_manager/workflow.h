//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <string>
#include <unordered_map>

#include "DatanodeProtocol.pb.h"
#include "edit_log.pb.h"
#include "glog/logging.h"
namespace dancenn {
enum class WorkflowState {
  CREATED = 0,
  PROCESSING = 1,
  SUCCESS = 2,
  FAILED = 3,
  CANCELED = 4,
  TIMEOUT = 5,
  THROTTLED = 6,
};

class WorkflowStateInfo {
 public:
  static const std::unordered_map<std::string, WorkflowState>
      workflow_state_name_to_enum;

  static inline std::string GetName(WorkflowState state) {
    switch (state) {
      case WorkflowState::CREATED:
        return "CREATED";
      case WorkflowState::PROCESSING:
        return "PROCESSING";
      case WorkflowState::SUCCESS:
        return "SUCCESS";
      case WorkflowState::FAILED:
        return "FAILED";
      case WorkflowState::CANCELED:
        return "CANCELED";
      case WorkflowState::TIMEOUT:
        return "TIMEOUT";
      case WorkflowState::THROTTLED:
        return "THROTTLED";
      default:
        return "UNKNOWN";
    }
  }

  static inline JobStatusOpBody::WorkflowState ConvertToJobStatus(
      WorkflowState state) {
    switch (state) {
      case WorkflowState::CREATED:
        return JobStatusOpBody_WorkflowState_CREATED;
      case WorkflowState::PROCESSING:
        return JobStatusOpBody_WorkflowState_PROCESSING;
      case WorkflowState::SUCCESS:
        return JobStatusOpBody_WorkflowState_SUCCESS;
      case WorkflowState::FAILED:
        return JobStatusOpBody_WorkflowState_FAILED;
      case WorkflowState::CANCELED:
        return JobStatusOpBody_WorkflowState_CANCELED;
      case WorkflowState::TIMEOUT:
        return JobStatusOpBody_WorkflowState_TIMEOUT;
      case WorkflowState::THROTTLED:
        return JobStatusOpBody_WorkflowState_THROTTLED;
      default:
        LOG(WARNING) << "Failed to resolve WorkflowState " << GetName(state);
        return JobStatusOpBody_WorkflowState_FAILED;
    }
  }

  static inline WorkflowState ConvertToWorkflowState(
      JobStatusOpBody::WorkflowState state) {
    switch (state) {
      case JobStatusOpBody_WorkflowState_CREATED:
        return WorkflowState::CREATED;
      case JobStatusOpBody_WorkflowState_PROCESSING:
        return WorkflowState::PROCESSING;
      case JobStatusOpBody_WorkflowState_SUCCESS:
        return WorkflowState::SUCCESS;
      case JobStatusOpBody_WorkflowState_FAILED:
        return WorkflowState::FAILED;
      case JobStatusOpBody_WorkflowState_CANCELED:
        return WorkflowState::CANCELED;
      case JobStatusOpBody_WorkflowState_TIMEOUT:
        return WorkflowState::TIMEOUT;
      case JobStatusOpBody_WorkflowState_THROTTLED:
        return WorkflowState::THROTTLED;
      default:
        LOG(WARNING) << "Failed to resolve JobStatusOpBody WorkflowState ";
        return WorkflowState::FAILED;
    }
  }

  static inline bool WorkflowStateName2Enum(std::string name,
                                            WorkflowState* state) {
    std::transform(name.begin(), name.end(), name.begin(), [](unsigned char c) {
      return std::toupper(c);
    });
    auto iter = workflow_state_name_to_enum.find(name);
    if (iter != workflow_state_name_to_enum.end()) {
      *state = iter->second;
      return true;
    } else {
      return false;
    }
  }

  static inline bool IsWorkflowDone(WorkflowState state) {
    return state != WorkflowState::CREATED &&
           state != WorkflowState::PROCESSING;
  }

  static inline bool IsWorkflowDone(JobStatusOpBody_WorkflowState state) {
    return state != JobStatusOpBody_WorkflowState_CREATED &&
           state != JobStatusOpBody_WorkflowState_PROCESSING;
  }

  static inline std::string GetName(JobStatusOpBody_WorkflowState state) {
    switch (state) {
      case JobStatusOpBody_WorkflowState_CREATED:
        return "CREATED";
      case JobStatusOpBody_WorkflowState_PROCESSING:
        return "PROCESSING";
      case JobStatusOpBody_WorkflowState_SUCCESS:
        return "SUCCESS";
      case JobStatusOpBody_WorkflowState_FAILED:
        return "FAILED";
      case JobStatusOpBody_WorkflowState_CANCELED:
        return "CANCELED";
      case JobStatusOpBody_WorkflowState_TIMEOUT:
        return "TIMEOUT";
      case JobStatusOpBody_WorkflowState_THROTTLED:
        return "THROTTLED";
      default:
        return "UNKNOWN";
    }
  }
};

enum class ManagedJobType {
  UNKNOWN = 0,
  LOAD_DATA = 1,
  FREE_DATA = 2,
  LOAD_METADATA = 3,
  RECONCILE_INODE_ATTRS = 4,
  CHAIN = 5,
  COPY_REPLICA = 6,
  SET_REPLICATION = 7,
  RESIDENT_DATA = 8,
};

class ManagedJobTypeInfo {
 public:
  static inline std::string GetName(ManagedJobType type) {
    switch (type) {
      case ManagedJobType::LOAD_DATA:
        return "LOAD_DATA";
      case ManagedJobType::FREE_DATA:
        return "FREE";
      case ManagedJobType::LOAD_METADATA:
        return "LOAD_METADATA";
      case ManagedJobType::RECONCILE_INODE_ATTRS:
        return "RECONCILE_INODE_ATTRS";
      case ManagedJobType::CHAIN:
        return "CHAIN";
      case ManagedJobType::COPY_REPLICA:
        return "COPY_REPLICA";
      case ManagedJobType::SET_REPLICATION:
        return "SET_REPLICATION";
      case ManagedJobType::RESIDENT_DATA:
        return "RESIDENT_DATA";
      default:
        return "UNKNOWN";
    }
  }

  static inline JobInfoOpBody::Type ConvertToOpType(ManagedJobType type) {
    switch (type) {
      case ManagedJobType::LOAD_DATA:
        return JobInfoOpBody_Type_LoadDataJob;
      case ManagedJobType::FREE_DATA:
        return JobInfoOpBody_Type_FreeJob;
      case ManagedJobType::LOAD_METADATA:
        return JobInfoOpBody_Type_LoadMetadataJob;
      case ManagedJobType::RECONCILE_INODE_ATTRS:
        return JobInfoOpBody_Type_ReconcileINodeAttrsJob;
      case ManagedJobType::SET_REPLICATION:
        return JobInfoOpBody_Type_SetReplicaJob;
      case ManagedJobType::RESIDENT_DATA:
        return JobInfoOpBody_Type_ResidentDataJob;
      case ManagedJobType::CHAIN:
        return JobInfoOpBody_Type_ChainJob;
      case ManagedJobType::COPY_REPLICA:
        return JobInfoOpBody_Type_CopyReplica;
      default:
        LOG(WARNING) << "Failed to resolve ManagedJobType " << GetName(type);
        return JobInfoOpBody_Type_UNKNOWN;
    }
  }

  static inline ManagedJobType ConvertToJobInfoType(JobInfoOpBody::Type type) {
    switch (type) {
      case JobInfoOpBody_Type_LoadDataJob:
        return ManagedJobType::LOAD_DATA;
      case JobInfoOpBody_Type_FreeJob:
        return ManagedJobType::FREE_DATA;
      case JobInfoOpBody_Type_LoadMetadataJob:
        return ManagedJobType::LOAD_METADATA;
      case JobInfoOpBody_Type_ReconcileINodeAttrsJob:
        return ManagedJobType::RECONCILE_INODE_ATTRS;
      case JobInfoOpBody_Type_SetReplicaJob:
        return ManagedJobType::SET_REPLICATION;
      case JobInfoOpBody_Type_ResidentDataJob:
        return ManagedJobType::RESIDENT_DATA;
      case JobInfoOpBody_Type_ChainJob:
        return ManagedJobType::CHAIN;
      default:
        return ManagedJobType::UNKNOWN;
    }
  }
};

enum class BlockStatus {
  UNKNOWN = 0,
  CANCELED = 1,
  RECEIVED = 2,
  DELETED = 3,
  FAILED = 4
};

class BlockStatusInfo {
 public:
  static inline std::string GetName(BlockStatus type) {
    switch (type) {
      case BlockStatus::RECEIVED:
        return "RECEIVED";
      case BlockStatus::DELETED:
        return "DELETED";
      case BlockStatus::CANCELED:
        return "CANCELED";
      default:
        return "UNKNOWN";
    }
  }
};

}  // namespace dancenn

namespace std {
template <>
struct hash<dancenn::ManagedJobType> {
  size_t operator()(const dancenn::ManagedJobType& e) const {
    return static_cast<size_t>(e);
  }
};

template <>
struct hash<dancenn::WorkflowState> {
  size_t operator()(const dancenn::WorkflowState& e) const {
    return static_cast<size_t>(e);
  }
};
}  // namespace std
