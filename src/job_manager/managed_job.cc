//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "managed_job.h"

#include <memory>

#include "base/status.h"
#include "gflags/gflags.h"
#include "glog/logging.h"
#include "job_manager/managed_job_impl.h"
#include "managed_task_impl.h"
#include "workflow.h"

DECLARE_int32(submit_task_retry_times);

namespace dancenn {

ManagedJobId ManagedJob::GetJobId() {
  return job_id_;
}

ManagedJobType ManagedJob::GetJobType() {
  return job_type_;
}

ManagedJobState& ManagedJob::GetJobState() {
  return job_state_;
}

void ManagedJob::ConsumeManagedJob() {
  VLOG(10) << "Start consume job " << this->GetJobId();
  job_manager_->GetJobTracker()->RegisterJob(shared_from_this());

  auto start = TimeUtil::GetNowEpochMs();
  while (task_iterator_->HasNext()) {
    std::shared_ptr<ManagedTask> task;
    Status status = task_iterator_->Next(task);
    if (status.IsOK()) {
      Status taskStatus = RegisterTask(task);
      if (taskStatus.IsOK()) {
        SubmitTask(task);
      } else if (taskStatus.code() == Code::kThrottled) {
        VLOG(10) << "Failed to register throttle task for " << this->GetJobId();
        Complete(WorkflowState::THROTTLED);
        break;
      } else if (taskStatus.code() == Code::kJobNotFound) {
        break;
      } else if (taskStatus.code() == Code::kError) {
        Complete(WorkflowState::FAILED);
        break;
      }
    }
  }

  auto timer_iter = metrics_->consume_job_timer.find(job_type_);
  if (timer_iter != metrics_->consume_job_timer.end()) {
    MFH(timer_iter->second)->Update(TimeUtil::GetNowEpochMs() - start);
  }

  job_manager_->GetJobTracker()->SetJobSubmitted(job_id_);
}

bool ManagedJob::operator()(void* arg) {
  ConsumeManagedJob();
  return true;
}

Status ManagedJob::RegisterTask(const std::shared_ptr<ManagedTask>& task) {
  return job_manager_->GetJobTracker()->RegisterTask(task);
}

void ManagedJob::SubmitTask(const std::shared_ptr<ManagedTask>& task) {
  VLOG(10) << "Submit task " << task->GetTaskId() << " for "
           << task->GetInodeId();

  int32_t retry_times = FLAGS_submit_task_retry_times;
  Status status;
  while (true) {
    status = task_handler_->SubmitTask(task);
    if (status.IsOK() || retry_times-- <= 0) {
      break;
    } else {
      VLOG(10) << "Retry submit task " << task->ToString();
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      continue;
    }
  }

  if (!status.IsOK()) {
    VLOG(10) << "Failed to submit " << task->ToString();
    task->UpdateState(WorkflowState::THROTTLED);
  }
}

void ManagedJob::RefreshWorkflowState() {
  if (job_state_.IsComplete() || !job_state_.IsJobSubmitted()) {
    return;
  }

  if (ExecutionTimeout()) {
    LOG(INFO) << ManagedJobTypeInfo::GetName(GetJobType()) << " Job "
              << GetJobId() << " timeout";
    Complete(WorkflowState::TIMEOUT);
    return;
  }

  uint64_t total_task_number = job_state_.GetTotalTaskNum();
  if (total_task_number == job_state_.GetCompletedTaskNum()) {
    // Not enumerate all possible scenarios. As it is the responsibility of the
    // client to determine the status of the job based on the number of each
    // task's status.
    if (job_state_.GetThrottledTaskNum() > 0) {
      Complete(WorkflowState::THROTTLED);
    } else if (total_task_number == job_state_.GetSuccessTaskNum()) {
      Complete(WorkflowState::SUCCESS);
    } else if (total_task_number == job_state_.GetCanceledTaskNum() +
                                        job_state_.GetSuccessTaskNum()) {
      Complete(WorkflowState::SUCCESS);
    } else {
      Complete(WorkflowState::FAILED);
    }
  }
}

void ManagedJob::Complete(WorkflowState state) {
  job_state_.CompleteState(state);

  if (update_metastorage_) {
    JobStatusOpBody job_status;
    job_state_.MakeJobStatusOpBody(state, job_status);
    job_manager_->UpdateJobStatusHA(
        job_id_, job_type_, job_status, false, notify_standby_when_complete_);
  }
}

JobInfoOpBody ManagedJob::ToProto() {
  JobInfoOpBody proto;
  proto.set_job_id(job_id_);
  proto.set_job_type(ManagedJobTypeInfo::ConvertToOpType(job_type_));

  return proto;
}

void OrderedChainJob::ConsumeManagedJob() {
  job_manager_->GetJobTracker()->RegisterJob(shared_from_this());
  if (sub_jobs_.empty()) {
    job_manager_->GetJobTracker()->SetJobSubmitted(job_id_);
    Complete(WorkflowState::SUCCESS);
    return;
  }
  if (curr_job_idx_ >= sub_jobs_.size()) {
    Complete(WorkflowState::FAILED);
    return;
  }
  // Submit first sub-job
  Status status = job_handler_->SubmitJob(sub_jobs_[curr_job_idx_]);
  if (!status.IsOK()) {
    LOG_WITH_LEVEL(ERROR) << "Failed to submit job "
                          << sub_jobs_[curr_job_idx_]->GetJobId()
                          << " with error " << status.ToString();
    MFC(LoggerMetrics::Instance().error_)->Inc();
    sub_jobs_[curr_job_idx_]->Complete(WorkflowState::THROTTLED);
  }

  job_manager_->GetJobTracker()->SetJobSubmitted(job_id_);
}

void OrderedChainJob::RefreshWorkflowState() {
  if (job_state_.IsComplete() || !job_state_.IsJobSubmitted()) {
    return;
  }

  if (curr_job_idx_ >= sub_jobs_.size()) {
    Complete(WorkflowState::FAILED);
    return;
  }
  auto job = sub_jobs_[curr_job_idx_];
  if (job->IsComplete()) {
    WorkflowState state = job->GetJobState().GetWorkflowState();
    job_state_.CountForJobState(state);
    if (state != WorkflowState::SUCCESS) {
      Complete(state);
    } else if (curr_job_idx_ < sub_jobs_.size() - 1) {
      // Submit next sub job
      curr_job_idx_++;
      Status status = job_handler_->SubmitJob(sub_jobs_[curr_job_idx_]);
      if (!status.IsOK()) {
        LOG_WITH_LEVEL(ERROR)
            << "Failed to submit job " << sub_jobs_[curr_job_idx_]->GetJobId()
            << " with error " << status.ToString();
        MFC(LoggerMetrics::Instance().error_)->Inc();
        sub_jobs_[curr_job_idx_]->Complete(WorkflowState::THROTTLED);
      }
    } else {
      Complete(WorkflowState::SUCCESS);
    }
  }
}

WorkflowState OrderedChainJob::SubJobWorkflowState() {
  for (auto& job : sub_jobs_) {
    ManagedJobState& state = job->GetJobState();
    if (state.IsComplete()) {
      if (state.GetWorkflowState() != WorkflowState::SUCCESS) {
        return state.GetWorkflowState();
      }
    } else {
      return state.GetWorkflowState();
    }
  }
  return WorkflowState::SUCCESS;
}

}  // namespace dancenn