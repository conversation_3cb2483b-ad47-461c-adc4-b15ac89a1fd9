
#include "task_iterator.h"

namespace dancenn {

ChainJobTaskIterator::ChainJobTaskIterator(
    std::vector<std::shared_ptr<TaskIterator>>& task_iterators)
    : task_iterators_(task_iterators), creator_idx_(0) {
}

bool ChainJobTaskIterator::HasNext() {
  while (creator_idx_ < task_iterators_.size()) {
    if (task_iterators_[creator_idx_]->HasNext()) {
      return true;
    } else {
      creator_idx_++;
    }
  }
  return false;
}

Status ChainJobTaskIterator::Next(std::shared_ptr<ManagedTask>& task) {
  if (creator_idx_ >= task_iterators_.size()) {
    return Status(Code::kTaskNotFound, task->ToString());
  }
  return task_iterators_[creator_idx_]->Next(task);
}
}  // namespace dancenn