//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "managed_job_state.h"

#include <cmath>
#include <memory>
#include <unordered_map>

#include "base/logger_metrics.h"
#include "job_manager.h"
#include "job_manager/workflow.h"

namespace dancenn {

ManagedJobState::ManagedJobState(ManagedJobState&& that)
    : workflow_state_(that.workflow_state_.load()),
      job_id_(that.job_id_),
      job_type_(that.job_type_),
      total_task_number_(that.total_task_number_.load()),
      success_task_number_(that.success_task_number_.load()),
      failed_task_number_(that.failed_task_number_.load()),
      canceled_task_number_(that.canceled_task_number_.load()),
      timeout_task_number_(that.timeout_task_number_.load()),
      throttled_task_number_(that.throttled_task_number_.load()),
      created_timestamp_(that.created_timestamp_),
      complete_timestamp_(that.complete_timestamp_),
      job_submitted_(that.job_submitted_) {
}

ManagedJobState::ManagedJobState(const ManagedJobState& that)
    : workflow_state_(that.workflow_state_.load()),
      job_id_(that.job_id_),
      job_type_(that.job_type_),
      total_task_number_(that.total_task_number_.load()),
      success_task_number_(that.success_task_number_.load()),
      failed_task_number_(that.failed_task_number_.load()),
      canceled_task_number_(that.canceled_task_number_.load()),
      timeout_task_number_(that.timeout_task_number_.load()),
      throttled_task_number_(that.throttled_task_number_.load()),
      created_timestamp_(that.created_timestamp_),
      complete_timestamp_(that.complete_timestamp_),
      job_submitted_(that.job_submitted_) {
}

ManagedJobState& ManagedJobState::operator=(const ManagedJobState& that) {
  workflow_state_ = that.workflow_state_.load();
  job_id_ = that.job_id_;
  job_type_ = that.job_type_;
  total_task_number_ = that.total_task_number_.load();
  success_task_number_ = that.success_task_number_.load();
  failed_task_number_ = that.failed_task_number_.load();
  canceled_task_number_ = that.canceled_task_number_.load();
  timeout_task_number_ = that.timeout_task_number_.load();
  throttled_task_number_ = that.throttled_task_number_.load();
  complete_timestamp_ = that.complete_timestamp_;
  job_submitted_ = that.job_submitted_;
  return *this;
}

void ManagedJobState::ConvertFrom(const JobInfoOpBody& job_info) {
  if (job_info.has_job_status()) {
    auto job_status_op = job_info.job_status();
    workflow_state_ =
        WorkflowStateInfo::ConvertToWorkflowState(job_status_op.state());
    total_task_number_ = job_status_op.total_task_number();
    success_task_number_ = job_status_op.success_task_number();
    failed_task_number_ = job_status_op.failed_task_number();
    canceled_task_number_ = job_status_op.canceled_task_number();
    timeout_task_number_ = job_status_op.timeout_task_number();
    throttled_task_number_ = job_status_op.throttled_task_number();
    complete_timestamp_ = job_status_op.complete_timestamp();
  }
  job_id_ = job_info.job_id();
  job_type_ = ManagedJobTypeInfo::ConvertToJobInfoType(job_info.job_type());
}

void ManagedJobState::CompleteState(WorkflowState state) {
  VLOG(10) << "Complete job " << job_id_ << " to "
            << WorkflowStateInfo::GetName(state);

  complete_timestamp_ = TimeUtil::GetNowEpochMs();

  if (!IsComplete()) {
    auto counter_iter = metrics_->complete_job_state_counters.find(state);
    if (counter_iter != metrics_->complete_job_state_counters.end()) {
      counter_iter->second->Inc();
    } else {
      metrics_->job_complete_wrong_state_counter->Inc();
    }

    auto timer_iter = metrics_->job_complete_timer.find(job_type_);
    if (timer_iter != metrics_->job_complete_timer.end()) {
      MFH(timer_iter->second)->Update(GetCompleteTime() - GetCreateTime());
    } else {
      LOG_WITH_LEVEL(WARNING)
          << "Complete unknown job type "
          << ManagedJobTypeInfo::GetName(job_type_) << " for job " << job_id_;
      MFC(LoggerMetrics::Instance().warn_)->Inc();
    }
  }

  UpdateWorkflowState(state);
}

void ManagedJobState::MakeJobStatusOpBody(WorkflowState state,
                                          JobStatusOpBody& job_status) const {
  auto job_state = WorkflowStateInfo::ConvertToJobStatus(state);
  job_status.set_state(job_state);
  job_status.set_created_timestamp(created_timestamp_);
  job_status.set_complete_timestamp(complete_timestamp_);
  job_status.set_total_task_number(total_task_number_);
  job_status.set_success_task_number(success_task_number_);
  job_status.set_failed_task_number(failed_task_number_);
  job_status.set_canceled_task_number(canceled_task_number_);
  job_status.set_timeout_task_number(timeout_task_number_);
  job_status.set_throttled_task_number(throttled_task_number_);
}

bool ManagedJobState::CountForJobState(WorkflowState& work_flow_state) {
  bool res = true;
  if (WorkflowState::SUCCESS == work_flow_state) {
    AddSuccessTaskNum();
  } else if (WorkflowState::FAILED == work_flow_state) {
    AddFailedTaskNum();
  } else if (WorkflowState::CANCELED == work_flow_state) {
    AddCanceledTaskNum();
  } else if (WorkflowState::TIMEOUT == work_flow_state) {
    AddTimeoutTaskNum();
  } else if (WorkflowState::THROTTLED == work_flow_state) {
    AddThrottledTaskNum();
  } else {
    res = false;
  }
  return res;
}

std::string ManagedJobState::ToString() const {
  std::stringstream ss;
  ss << "Job : " << job_id_ << " type "
     << ManagedJobTypeInfo::GetName(job_type_) << " workflow State "
     << WorkflowStateInfo::GetName(workflow_state_.load())
     << " created_timestamp " << created_timestamp_ << " complete_timestamp "
     << complete_timestamp_ << " total_task_number " << total_task_number_
     << " success_task_number " << success_task_number_
     << " failed_task_number " << failed_task_number_ << " timeout_task_number "
     << timeout_task_number_ << " throttled_task_number "
     << throttled_task_number_ << " canceled_task_number "
     << canceled_task_number_;
  return ss.str();
}
}  // namespace dancenn