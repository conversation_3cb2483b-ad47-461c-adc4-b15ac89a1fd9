//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <unordered_map>

// Project
#include "base/metric.h"
#include "base/metrics.h"
#include "managed_task_handler.h"
#include "workflow.h"

namespace dancenn {

class JobManager;
class ManagedJobHandler;
class ManagedTaskHandler;
class JobTracker;
class ManagedJobState;

struct JobManagerMetrics {
  JobManagerMetrics(JobManager* job_manager);
  JobManagerMetrics(const JobManagerMetrics&) = delete;
  JobManagerMetrics& operator=(const JobManagerMetrics&) = delete;

  std::shared_ptr<AtomicCounter>
      add_load_cmd_blk_manager_fail_counter;  // counter
  std::shared_ptr<AtomicCounter>
      add_load_cmd_blk_manager_success_counter;  // counter
  std::shared_ptr<AtomicCounter>
      add_load_cmd_job_manager_fail_counter;  // counter

  MetricID construct_located_blocks;  // histogram
  MetricID list_inodes_timer;         // histogram

  std::shared_ptr<Gauge> load_blk_cmd_cache_size_gauge;
};

struct ManagedJobHandlerMetrics {
  ManagedJobHandlerMetrics(ManagedJobHandler* job_handler);
  ManagedJobHandlerMetrics(const ManagedJobHandlerMetrics&) = delete;
  ManagedJobHandlerMetrics& operator=(const ManagedJobHandlerMetrics&) = delete;

  std::shared_ptr<Gauge> pending_jobs_gauge;
  std::shared_ptr<Gauge> running_jobs_gauge;
  std::shared_ptr<AtomicCounter> add_job_failed_counter;  // counter
};

struct ManagedTaskHandlerMetrics {
  ManagedTaskHandlerMetrics(ManagedTaskHandler* task_handler);
  ManagedTaskHandlerMetrics(const ManagedTaskHandlerMetrics&) = delete;
  ManagedTaskHandlerMetrics& operator=(const ManagedTaskHandlerMetrics&) =
      delete;

  std::shared_ptr<Gauge> pending_tasks_gauge;
  std::shared_ptr<Gauge> running_tasks_gauge;
  std::shared_ptr<AtomicCounter> add_task_failed_counter;  // counter
};

struct JobTrackerMetrics {
  JobTrackerMetrics(JobTracker* job_tracker);
  JobTrackerMetrics(const JobTrackerMetrics&) = delete;
  JobTrackerMetrics& operator=(const JobTrackerMetrics&) = delete;

  MetricID tracker_inspector_counter;                              // counter
  MetricID tracker_job_metastorage_recycle_counter;                // counter
  std::shared_ptr<AtomicCounter> task_register_throttled_counter;  // counter
  std::shared_ptr<AtomicCounter> task_registed_counter;            // counter
  std::shared_ptr<AtomicCounter> job_registed_counter;             // counter
  std::shared_ptr<AtomicCounter> shared_task_counter;              // counter
  std::shared_ptr<AtomicCounter> job_submitted_counter;            // counter
  std::shared_ptr<AtomicCounter> tracked_block_task_counter;       // counter
  std::shared_ptr<AtomicCounter> add_task_tracker_failed_counter;  // counter

  MetricID inspect_block_tasks_timer;       // histogram
  MetricID inspect_autonomous_tasks_timer;  // histogram
  MetricID inspect_job_timer;               // histogram
  MetricID inspect_ms_timer;                // histogram
  MetricID register_job_timer;              // histogram
  MetricID register_task_timer;             // histogram

  std::shared_ptr<Gauge> tracking_jobs_gauge;
  std::shared_ptr<Gauge> tracking_tasks_gauge;
  std::shared_ptr<Gauge> job_tracker_pending_gauge;
};

struct ManagedJobMetrics {
  static ManagedJobMetrics& Instance();

  // ManagedJobState METRICS

  // lifecycle of job from create to complete
  MetricID load_data_job_complete_timer;      // histogram
  MetricID load_metadata_job_complete_timer;  // histogram
  MetricID free_job_complete_timer;           // histogram
  MetricID set_replica_job_complete_timer;    // histogram
  MetricID resident_data_job_complete_timer;  // histogram
  MetricID chain_job_complete_timer;          // histogram
  std::unordered_map<ManagedJobType, MetricID> job_complete_timer;

  std::shared_ptr<AtomicCounter> job_complete_success_counter;      // counter
  std::shared_ptr<AtomicCounter> job_complete_canceled_counter;     // counter
  std::shared_ptr<AtomicCounter> job_complete_failed_counter;       // counter
  std::shared_ptr<AtomicCounter> job_complete_timeout_counter;      // counter
  std::shared_ptr<AtomicCounter> job_complete_throttled_counter;    // counter
  std::shared_ptr<AtomicCounter> job_complete_wrong_state_counter;  // counter

  std::unordered_map<WorkflowState, std::shared_ptr<AtomicCounter>>
      complete_job_state_counters;

  // ManagedJob METRICS
  MetricID load_data_consume_job_timer;
  MetricID load_metadata_consume_job_timer;
  MetricID free_task_consume_job_timer;
  std::unordered_map<ManagedJobType, MetricID> consume_job_timer;

  // ManagedTask METRICS
  MetricID task_retry_times_counter;  // counter
  MetricID task_op_timeout_counter;   // counter
  MetricID metadata_task_list_counter;   // counter

  MetricID reconcile_inode_attrs_timer;        // histogram
  MetricID load_data_task_complete_timer;           // histogram
  MetricID load_metadata_task_complete_timer;       // histogram
  MetricID free_task_complete_timer;                // histogram
  MetricID set_replica_task_complete_timer;         // histogram
  MetricID copy_replica_task_complete_timer;        // histogram
  MetricID mark_resident_data_task_complete_timer;  // histogram
  MetricID chain_task_complete_timer;               // histogram
  std::unordered_map<ManagedJobType, MetricID> task_complete_timer;

  std::shared_ptr<AtomicCounter> task_complete_success_counter;      // counter
  std::shared_ptr<AtomicCounter> task_complete_canceled_counter;     // counter
  std::shared_ptr<AtomicCounter> task_complete_failed_counter;       // counter
  std::shared_ptr<AtomicCounter> task_complete_timeout_counter;      // counter
  std::shared_ptr<AtomicCounter> task_complete_throttled_counter;    // counter
  std::shared_ptr<AtomicCounter> task_complete_wrong_state_counter;  // counter

  std::shared_ptr<AtomicCounter> free_data_task_complete_counter;  // counter
  std::shared_ptr<AtomicCounter> load_data_task_complete_counter;  // counter
  std::shared_ptr<AtomicCounter> copy_replica_complete_counter;    // counter

  std::unordered_map<WorkflowState, std::shared_ptr<AtomicCounter>>
      complete_task_state_counters;

  // Load metadata
  MetricID load_metadata_timer;  // histogram

  private:
    ManagedJobMetrics();
};

}  // namespace dancenn