//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "task_iterator.h"

#include <unistd.h>

#include <memory>

#include "base/constants.h"
#include "base/status.h"
#include "glog/logging.h"
#include "inode.pb.h"

namespace dancenn {

CustomTaskIterator::CustomTaskIterator(std::shared_ptr<TaskCreator>& task_creator,
                     std::shared_ptr<DataJobOption>& opt)
    : task_creator_(task_creator),
      job_opt_(opt) {
  current_task_ = task_creator_->ConstructTask();
}

bool CustomTaskIterator::HasNext() {
  return current_task_ != nullptr;
}

Status CustomTaskIterator::Next(std::shared_ptr<ManagedTask>& task) {
  task = current_task_;
  current_task_ = task_creator_->ConstructTask();
  if (task == nullptr) {
    return Status(Code::kTaskNotFound, "");
  } else {
    return Status::OK();
  }
}

}  // namespace dancenn