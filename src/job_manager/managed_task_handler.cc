//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include <memory>

#include "base/status.h"
#include "gflags/gflags.h"
#include "job_manager/job_tracker.h"
#include "job_manager_metrics.h"
#include "managed_task_handler.h"

DECLARE_uint32(task_handler_thread_num);
DECLARE_uint32(task_handler_thread_pool_queue_size);

namespace dancenn {

using ThreadPool = cnetpp::concurrency::ThreadPool;
using ThreadPoolTask = cnetpp::concurrency::Task;

ManagedTaskHandler::ManagedTaskHandler()
    : metrics_(std::make_unique<ManagedTaskHandlerMetrics>(this)) {

}

void ManagedTaskHandler::Start() {
  LOG(INFO) << "ManagedTaskHandler starting.";
  task_workers_ = std::make_unique<ThreadPool>("TaskHandler", true);
  task_workers_->set_num_threads(FLAGS_task_handler_thread_num);
  task_workers_->set_max_num_pending_tasks(
      FLAGS_task_handler_thread_pool_queue_size);
  task_workers_->Start();
  LOG(INFO) << "ManagedTaskHandler started.";
}

void ManagedTaskHandler::Stop() {
  LOG(INFO) << "ManagedTaskHandler stopping.";
  task_workers_->Stop();
  LOG(INFO) << "ManagedTaskHandler stopped.";
}

Status ManagedTaskHandler::SubmitTask(
    const std::shared_ptr<ManagedTask>& task) {
  if (!task) {
    LOG(ERROR) << "Submit null task";
    return Status(Code::kError, "Submit null task");
  }
  VLOG(10) << "Submit task " << task->ToString();

  task->UpdateSubmitTimePoint();
  if (!task_workers_->AddTask(std::static_pointer_cast<ThreadPoolTask>(task))) {
    metrics_->add_task_failed_counter->Inc();
    LOG(WARNING)
        << "Failed to add task to managed task handler. Pending task num: "
        << task_workers_->PendingCount();
    return Status(JavaExceptions::Exception::kThrottlerException,
                  "Pending job out of managed task handler queue size");
  }
  return Status();
}

}  // namespace dancenn
