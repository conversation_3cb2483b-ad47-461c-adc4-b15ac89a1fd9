//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/concurrency/thread_pool.h>

#include <atomic>
#include <cstdint>
#include <memory>

#include "job_manager/workflow.h"
#include "managed_job_state.h"
#include "task_iterator.h"
#include "workflow.h"
#include "workflow_id_generator.h"

namespace dancenn {

class JobManager;
class TaskIterator;

class ManagedJob : public std::enable_shared_from_this<ManagedJob>,
                   public cnetpp::concurrency::Task {
 public:
  ManagedJob(ManagedJobId* id,
             ManagedJobType job_type,
             JobManager* job_manager,
             std::shared_ptr<ManagedTaskHandler> task_handler,
             bool notify_standby_when_complete,
             bool update_metastorage = true)
      : job_type_(job_type),
        job_manager_(job_manager),
        task_handler_(task_handler),
        job_state_(ManagedJobState(*id, job_type)),
        notify_standby_when_complete_(notify_standby_when_complete),
        update_metastorage_(update_metastorage) {
    if (id->empty()) {
      job_id_ = WorkflowIdGenerator::Instance().nextJobId();
      *id = job_id_;
    } else {
      job_id_ = *id;
    }
  };

  ManagedJobId GetJobId();

  ManagedJobType GetJobType();

  ManagedJobState& GetJobState();

  void SetTaskIterator(std::shared_ptr<TaskIterator>& task_iterator) {
    task_iterator_ = task_iterator;
  }

  virtual void ConsumeManagedJob();

  bool operator()(void* arg = nullptr) override;

  virtual bool IsComplete() const {
    return job_state_.IsComplete();
  }

  virtual void RefreshWorkflowState();

  virtual JobInfoOpBody ToProto();

  virtual bool ExecutionTimeout() const = 0;

  void Complete(WorkflowState state = WorkflowState::SUCCESS);

 protected:
  ManagedJobState job_state_;
  JobManager* job_manager_{nullptr};
  ManagedJobId job_id_;
  ManagedJobType job_type_;

 private:
  Status RegisterTask(const std::shared_ptr<ManagedTask>& task);
  void SubmitTask(const std::shared_ptr<ManagedTask>& task);

  std::shared_ptr<TaskIterator> task_iterator_;
  std::shared_ptr<ManagedTaskHandler> task_handler_;
  bool update_metastorage_;

  bool notify_standby_when_complete_;

  std::shared_ptr<ManagedJobMetrics> metrics_ = std::make_shared<ManagedJobMetrics>(ManagedJobMetrics::Instance());
  friend class ManagedJobTest;
};

}  // namespace dancenn
