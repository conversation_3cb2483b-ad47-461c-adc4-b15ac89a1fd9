//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <list>
#include <memory>
#include <vector>

#include "base/constants.h"
#include "base/status.h"
#include "hdfs.pb.h"
#include "inode.pb.h"
#include "job_manager.h"
#include "job_manager_def.h"
#include "managed_task.h"
#include "task_creator.h"

namespace dancenn {

class TaskCreator;

class TaskIterator {
 public:
  virtual bool HasNext() = 0;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) = 0;
};

class BlocksInFileTaskIterator : public TaskIterator {
 public:
  BlocksInFileTaskIterator(const INode* req_inode,
                           JobManager* job_manager,
                           std::shared_ptr<TaskCreator>& task_creator,
                           std::shared_ptr<DataJobOption>& opt);

  virtual bool HasNext() override;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) override;

 private:
  JobManager* job_manager_{nullptr};
  std::shared_ptr<TaskCreator> task_creator_;
  std::vector<std::shared_ptr<ManagedTask>> tasks_;
};

class BlocksInDirectoryTaskIterator : public TaskIterator {
 public:
  BlocksInDirectoryTaskIterator(const INode* req_inode,
                                JobManager* job_manager,
                                std::shared_ptr<TaskCreator>& task_creator,
                                std::shared_ptr<DataJobOption>& opt);

  virtual bool HasNext() override;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) override;

 private:
  JobManager* job_manager_{nullptr};
  bool recursive_traverse_;
  std::string start_after_{kInitStartAfter};
  std::shared_ptr<TaskCreator> task_creator_;
  std::list<std::shared_ptr<INode>> to_process_dir_;
  std::vector<std::shared_ptr<ManagedTask>> to_process_task_;
  std::shared_ptr<DataJobOption> job_opt_;
  std::vector<INode> to_process_inode_;
};

class CustomTaskIterator : public TaskIterator {
 public:
  CustomTaskIterator(std::shared_ptr<TaskCreator>& task_creator,
                     std::shared_ptr<DataJobOption>& opt);

  virtual bool HasNext() override;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) override;

 private:
  std::shared_ptr<DataJobOption> job_opt_;
  std::shared_ptr<TaskCreator> task_creator_;
  std::shared_ptr<ManagedTask> current_task_;
};

class SingleTaskIterator : public TaskIterator {
 public:
  SingleTaskIterator(std::shared_ptr<ManagedTask>& task) : task_(task) {
  }

  bool HasNext() override {
    return task_ != nullptr;
  }

  Status Next(std::shared_ptr<ManagedTask>& task) override {
    if (task_ == nullptr) {
      return Status(Code::kTaskNotFound);
    } else {
      task = task_;
      task_ = nullptr;
      return Status::OK();
    }
  }

 private:
  std::shared_ptr<ManagedTask> task_;
};

class ChainJobTaskIterator : public TaskIterator {
 public:
  ChainJobTaskIterator(
      std::vector<std::shared_ptr<TaskIterator>>& task_iterators);

  virtual bool HasNext() override;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) override;

 private:
  std::vector<std::shared_ptr<TaskIterator>> task_iterators_;
  int creator_idx_;
};

class ReconcileINodeAttrsTaskIterator : public TaskIterator {
 public:
  ReconcileINodeAttrsTaskIterator(
      std::shared_ptr<TaskCreator> task_creator,
      std::shared_ptr<ReconcileINodeAttrsJobOption> opt);

  virtual bool HasNext() override;
  virtual Status Next(std::shared_ptr<ManagedTask>& task) override;

 private:
  std::shared_ptr<ReconcileINodeAttrsJobOption> opt_;
  std::shared_ptr<TaskCreator> task_creator_;
  std::shared_ptr<ManagedTask> current_task_;
};

}  // namespace dancenn
