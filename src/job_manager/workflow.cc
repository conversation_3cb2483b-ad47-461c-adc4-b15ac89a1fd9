//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "workflow.h"

#include <string>
#include <unordered_map>

namespace dancenn {

const std::unordered_map<std::string, WorkflowState>
    WorkflowStateInfo::workflow_state_name_to_enum = {
        {"CREATED", WorkflowState::CREATED},
        {"PROCESSING", WorkflowState::PROCESSING},
        {"SUCCESS", WorkflowState::SUCCESS},
        {"FAILED", WorkflowState::FAILED},
        {"CANCELED", WorkflowState::CANCELED},
        {"TIMEOUT", WorkflowState::TIMEOUT}};

}  // namespace dancenn