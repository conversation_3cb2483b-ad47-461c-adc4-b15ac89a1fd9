//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "task_creator.h"

#include <memory>

#include "block_manager/block_manager.h"
#include "glog/logging.h"
#include "job_manager/job_tracker.h"
#include "job_manager/managed_task_impl.h"
#include "job_manager/workflow.h"

namespace dancenn {

// TODO task_handler_
std::shared_ptr<ManagedTask> LoadMetadataTaskCreator::ConstructTask() {
  if (!task_constructed_) {
    task_constructed_ = true;
    return std::make_shared<ManagedLoadMetadataTask>(
        job_id_, sync_engine_, list_opt_);
  } else {
    return nullptr;
  }
}

std::shared_ptr<ManagedTask> LoadDataTaskCreator::ConstructTask(
    const cloudfs::LocatedBlockProto& lb) {
  auto dns = lb.locs();
  std::unordered_set<DatanodeID> datanode_ids;
  if (target_dns_.empty()) {
    for (auto& dn : dns) {
      auto id = job_manager_->GetDatanodeInterId(dn.id().datanodeuuid());
      if (id != kInvalidDatanodeID) {
        datanode_ids.emplace(id);
      }
    }
  } else {
    for (auto& dn : target_dns_) {
      datanode_ids.emplace(dn);
    }
  }

  auto load_task = std::make_shared<ManagedLoadTask>(
      job_id_, datanode_ids, lb.b(), job_manager_);
  if (opt_->replica_num > 1) {
    auto copy_replica_task = std::make_shared<CopyReplicaTask>(
        job_id_, lb.b(), opt_->replica_num, job_manager_);
    std::shared_ptr<ChainTask> chain_task = std::make_shared<ChainTask>(
        ManagedJobType::CHAIN, job_id_, job_manager_);
    chain_task->AppendTask(load_task)->AppendTask(copy_replica_task);
    return chain_task;
  } else {
    return load_task;
  }
}

std::shared_ptr<ManagedTask> FreeTaskCreator::ConstructTask(
    const cloudfs::LocatedBlockProto& lb) {
  return std::make_shared<ManagedFreeTask>(job_id_, lb.b(), job_manager_);
}

std::shared_ptr<ManagedTask> ReconcileINodeAttrsTaskCreator::ConstructTask() {
  if (!task_constructed_) {
    task_constructed_ = true;
    return std::make_shared<ManagedReconcileINodeAttrsTask>(
        job_id_, inode_, opt_, ns_, ms_);
  } else {
    return nullptr;
  }
}

}  // namespace dancenn