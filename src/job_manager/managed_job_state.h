//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <atomic>
#include <chrono>
#include <cstdint>
#include <memory>

#include "base/time_util.h"
#include "edit_log.pb.h"
#include "gflags/gflags_declare.h"
#include "glog/logging.h"
#include "job_manager/job_manager_metrics.h"
#include "job_manager/workflow_id_generator.h"
#include "workflow.h"

namespace dancenn {

class JobManager;

class ManagedJobState {
 public:
  ManagedJobState() {
  }

  ManagedJobState(ManagedJobId job_id, ManagedJobType job_type)
      : job_id_(job_id), job_type_(job_type) {
  }

  ManagedJobState(ManagedJobState&& that);

  ManagedJobState(const ManagedJobState& that);

  ManagedJobState& operator=(const ManagedJobState& that);

  void ConvertFrom(const JobInfoOpBody& job_info);

  void AddTotalTaskNum(uint64_t num = 1) {
    total_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void AddSuccessTaskNum(uint64_t num = 1) {
    success_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void AddFailedTaskNum(uint64_t num = 1) {
    failed_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void AddCanceledTaskNum(uint64_t num = 1) {
    canceled_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void AddTimeoutTaskNum(uint64_t num = 1) {
    timeout_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void AddThrottledTaskNum(uint64_t num = 1) {
    throttled_task_number_.fetch_add(num);
    PrintStatistics();
  }

  void SetTotalTaskNum(uint64_t num) {
    total_task_number_.store(num);
    PrintStatistics();
  }

  void SetSuccessTaskNum(uint64_t num) {
    success_task_number_.store(num);
    PrintStatistics();
  }

  void PrintStatistics() {
    VLOG(10) << ToString();
  }

  uint64_t GetTotalTaskNum() const {
    return total_task_number_.load();
  }

  uint64_t GetSuccessTaskNum() const {
    return success_task_number_.load();
  }

  uint64_t GetFailedTaskNum() const {
    return failed_task_number_.load();
  }

  uint64_t GetCanceledTaskNum() const {
    return canceled_task_number_.load();
  }

  uint64_t GetTimeoutTaskNum() const {
    return timeout_task_number_.load();
  }

  uint64_t GetThrottledTaskNum() const {
    return throttled_task_number_.load();
  }

  uint64_t GetCompletedTaskNum() const {
    return GetSuccessTaskNum() + GetFailedTaskNum() + GetCanceledTaskNum() +
           GetTimeoutTaskNum() + GetThrottledTaskNum();
  }

  ManagedJobId GetJobId() const {
    return job_id_;
  }

  ManagedJobType GetJobType() const {
    return job_type_;
  }

  uint64_t GetCreateTime() const {
    return created_timestamp_;
  }

  uint64_t GetCompleteTime() const {
    return complete_timestamp_;
  }

  void SetJobSubmitted() {
    job_submitted_ = true;
    VLOG(10) << "All tasks are submitted for job " << job_id_;
  }

  bool IsJobSubmitted() const {
    return job_submitted_;
  }

  bool IsComplete() const {
    return WorkflowStateInfo::IsWorkflowDone(workflow_state_);
  }

  WorkflowState GetWorkflowState() const {
    return workflow_state_;
  }

  void CompleteState(WorkflowState state = WorkflowState::SUCCESS);

  void UpdateWorkflowState(WorkflowState state) {
    VLOG(10) << "Update job workflow state to "
              << WorkflowStateInfo::GetName(state);
    if (!IsComplete()) {
      workflow_state_.store(state);
    }
  }

  long CachedTimeMs() const {
    return TimeUtil::GetNowEpochMs() - complete_timestamp_;
  }

  void MakeJobStatusOpBody(WorkflowState state,
                           JobStatusOpBody& job_status) const;

  bool CountForJobState(WorkflowState& work_flow_state);

  std::string ToString() const;

 private:
  std::atomic<WorkflowState> workflow_state_{WorkflowState::CREATED};
  ManagedJobId job_id_;
  ManagedJobType job_type_;
  uint64_t created_timestamp_ = TimeUtil::GetNowEpochMs();
  uint64_t complete_timestamp_ = TimeUtil::GetNowEpochMs();
  std::atomic<uint64_t> total_task_number_{0};
  std::atomic<uint64_t> success_task_number_{0};
  std::atomic<uint64_t> failed_task_number_{0};
  std::atomic<uint64_t> canceled_task_number_{0};
  std::atomic<uint64_t> timeout_task_number_{0};
  std::atomic<uint64_t> throttled_task_number_{0};
  bool job_submitted_ = false;

  std::shared_ptr<ManagedJobMetrics> metrics_ = std::make_shared<ManagedJobMetrics>(ManagedJobMetrics::Instance());
};
}  // namespace dancenn