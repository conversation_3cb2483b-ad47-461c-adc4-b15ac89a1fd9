//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include <unistd.h>

#include <memory>
#include <string>

#include "base/constants.h"
#include "base/logger_metrics.h"
#include "base/status.h"
#include "glog/logging.h"
#include "inode.pb.h"
#include "task_iterator.h"

namespace dancenn {

BlocksInFileTaskIterator::BlocksInFileTaskIterator(
    const INode* req_inode,
    JobManager* job_manager,
    std::shared_ptr<TaskCreator>& task_creator,
    std::shared_ptr<DataJobOption>& opt)
    : job_manager_(job_manager), task_creator_(task_creator) {
  if (opt == nullptr || req_inode == nullptr) {
    LOG_WITH_LEVEL(ERROR) << "BlocksInFileTaskIterator opt or inode is nullptr";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    // Skip this task
    return;
  }

  cloudfs::LocatedBlocksProto lbs;
  auto task_type = task_creator_->getManagedJobType();
  std::shared_ptr<INode> inode_ptr = std::make_shared<INode>(*req_inode);

  std::vector<bool> block_task_need_submit;
  job_manager_->ConstructLocatedBlocks(
      inode_ptr, &lbs, block_task_need_submit, task_type, &opt->ufs_key);
  for (int i = 0; i < lbs.blocks_size() && i < block_task_need_submit.size();
       i++) {
    if (block_task_need_submit[i]) {
      auto& lb = lbs.blocks(i);
      auto&& constructed_task = task_creator_->ConstructTask(lb);
      if (constructed_task != nullptr) {
        tasks_.emplace_back(constructed_task);
        constructed_task->SetInodeId(inode_ptr->id());
      }
    }
  }
}

bool BlocksInFileTaskIterator::HasNext() {
  return !tasks_.empty();
}

Status BlocksInFileTaskIterator::Next(std::shared_ptr<ManagedTask>& task) {
  if (!HasNext()) {
    return Status(Code::kError, "Task not found.");
  }
  task = tasks_.back();
  tasks_.pop_back();
  return Status::OK();
}

BlocksInDirectoryTaskIterator::BlocksInDirectoryTaskIterator(
    const INode* req_inode,
    JobManager* job_manager,
    std::shared_ptr<TaskCreator>& task_creator,
    std::shared_ptr<DataJobOption>& opt)
    : job_manager_(job_manager),
      task_creator_(task_creator),
      recursive_traverse_(opt->recursive) {
  INode req_inode_tmp = *req_inode;
  std::string& ufs_key = opt->ufs_key;
  if (!ufs_key.empty()) {
    req_inode_tmp.mutable_ufs_file_info()->set_key(ufs_key);
  }
  bool has_more = false;
  job_manager_->ListSubINodes(
      &req_inode_tmp, start_after_, has_more, &to_process_inode_);
  VLOG(10) << "List sub INodes start_after : " << start_after_
           << " has_more : " << has_more;
  if (has_more && !to_process_inode_.empty()) {
    start_after_ = to_process_inode_.back().name();
    to_process_dir_.emplace_front(std::make_shared<INode>(req_inode_tmp));
  } else {
    start_after_ = kInitStartAfter;
  }
}

bool BlocksInDirectoryTaskIterator::HasNext() {
  return to_process_task_.size() > 0 || to_process_dir_.size() > 0 ||
         to_process_inode_.size() > 0;
}

Status BlocksInDirectoryTaskIterator::Next(std::shared_ptr<ManagedTask>& task) {
  while (true) {
    if (!HasNext()) {
      return Status(Code::kError, "Task not found.");
    }
    if (!to_process_task_.empty()) {
      task = to_process_task_.back();
      VLOG(10) << "Processing task inode " << task->GetInodeId();
      to_process_task_.pop_back();
      return Status::OK();
    }

    if (to_process_inode_.empty()) {
      std::shared_ptr<INode> dir_inode = to_process_dir_.back();
      bool has_more = false;
      job_manager_->ListSubINodes(
          dir_inode.get(), start_after_, has_more, &to_process_inode_);
      if (has_more && !to_process_inode_.empty()) {
        start_after_ = to_process_inode_.back().name();
      } else {
        to_process_dir_.pop_back();
        start_after_ = kInitStartAfter;
      }
      VLOG(10) << "List sub INodes start_after : " << start_after_
               << " has_more : " << has_more;
    } else {
      std::shared_ptr<INode> task_inode =
          std::make_shared<INode>(to_process_inode_.back());
      to_process_inode_.pop_back();

      if (recursive_traverse_ && task_inode->type() == INode_Type_kDirectory) {
        VLOG(10) << "Cache directory for recursive traverse "
                 << task_inode->id();
        to_process_dir_.emplace_front(task_inode);
      } else if (task_inode->type() == INode_Type_kFile) {
        // Construct task
        cloudfs::LocatedBlocksProto lbs;
        auto task_type = task_creator_->getManagedJobType();

        std::vector<bool> block_task_need_submit;
        job_manager_->ConstructLocatedBlocks(
          task_inode, &lbs, block_task_need_submit, task_type, &task_inode->ufs_file_info().key());
        for (int i = 0; i < lbs.blocks_size() && i < block_task_need_submit.size(); i++) {
          if (block_task_need_submit[i]) {
            auto& lb = lbs.blocks(i);
            auto&& constructed_task = task_creator_->ConstructTask(lb);
            if (constructed_task != nullptr) {
              to_process_task_.emplace_back(constructed_task);
              constructed_task->SetInodeId(task_inode->id());
            }
          }
        }
      }
    }
  }
}

}  // namespace dancenn