#pragma once

#include <atomic>
#include <sstream>
#include <string>

#include "base/time_util.h"
#include "gflags/gflags_declare.h"

DECLARE_int64(namespace_id);

namespace dancenn {

typedef std::string ManagedTaskId;
typedef std::string ManagedJobId;

static const std::string JOB_ID_PREFIX("JOB");
static const std::string TASK_ID_PREFIX("TASK");
static const std::string WORKFLOW_ID_SPLIT("_");
static const std::string INVALID_JOB_ID("JOB_-1");

class WorkflowIdGenerator {
 public:
  static WorkflowIdGenerator& Instance() {
    static WorkflowIdGenerator job_id_generator;
    return job_id_generator;
  }

  ManagedJobId nextJobId() {
    std::stringstream ss;
    ss << JOB_ID_PREFIX << WORKFLOW_ID_SPLIT << FLAGS_namespace_id
       << WORKFLOW_ID_SPLIT << TimeUtil::GetNowEpochMs() << WORKFLOW_ID_SPLIT
       << job_id_.fetch_add(1);

    return ss.str();
  }

  ManagedTaskId nextTaskId() {
    std::stringstream ss;
    ss << TASK_ID_PREFIX << WORKFLOW_ID_SPLIT << TimeUtil::GetNowEpochMs()
       << WORKFLOW_ID_SPLIT << task_id_.fetch_add(1);

    return ss.str();
  }

 private:
  WorkflowIdGenerator() {
  }
  ~WorkflowIdGenerator() {
  }
  WorkflowIdGenerator(const WorkflowIdGenerator&) = delete;
  WorkflowIdGenerator& operator=(const WorkflowIdGenerator&) = delete;

  std::atomic<uint64_t> job_id_{0};
  std::atomic<uint64_t> task_id_{0};
};

}  // namespace dancenn
