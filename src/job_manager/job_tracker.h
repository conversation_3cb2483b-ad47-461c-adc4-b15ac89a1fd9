//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <gtest/gtest.h>
#include <sys/types.h>

#include <atomic>
#include <cstdint>
#include <memory>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/closure.h"
#include "base/status.h"
#include "block_manager/block.h"
#include "cnetpp/concurrency/thread_pool.h"
#include "gflags/gflags_declare.h"
#include "glog/logging.h"
#include "job_manager/workflow.h"
#include "job_manager/managed_task_handler.h"
#include "job_manager/workflow_id_generator.h"
#include "job_manager/job_manager_metrics.h"
#include "job_manager/managed_job_state.h"
#include "job_manager/managed_task.h"
#include "namespace/meta_storage.h"

DECLARE_uint32(track_block_task_thread_num);
DECLARE_uint32(track_block_task_pool_queue_size);

namespace dancenn {

class ManagedTask;
class ManagedBlockTask;
class JobManager;
class ManagedJob;
class ManagedTaskHandler;

typedef std::unordered_set<std::shared_ptr<ManagedTask>> ManagedTaskSet;

class JobTracker {
 public:
  JobTracker(JobManager* job_manager,
             std::shared_ptr<ManagedTaskHandler> task_handler)
      : job_manager_(job_manager),
        task_handler_(task_handler),
        metrics_(std::make_unique<JobTrackerMetrics>(this)) {
  }

  ~JobTracker() {
    Stop();
  }

  bool RegisterJob(std::shared_ptr<ManagedJob> job);

  Status RegisterTask(std::shared_ptr<ManagedTask> task);

  void SetJobSubmitted(ManagedJobId job_id);

  void TrackBlockTask(uint64_t blk_id, BlockStatus status);

  void Start();

  void Stop();

  bool IsRunning() {
    return running_.load();
  }

  bool ThrottleTaskRegister();

  bool ContainJob(const ManagedJobId& job_id) {
    std::shared_lock<ReadWriteLock> rlock(job_progress_rwlock_);
    return job_id_to_progress_.find(job_id) != job_id_to_progress_.end();
  }

  Status LookupJobState(const ManagedJobId& job_id, ManagedJobState& job_state);

  Status CompleteJob(const ManagedJobId& job_id, const WorkflowState state);

  Status CancelJob(const ManagedJobId& job_id);

  Status ListJob(std::vector<ManagedJobId>& jobs,
                 ManagedJobType job_type);

 private:
  void PrintStatistic();

  Status RegisterBlockTask(uint64_t block_id,
                           std::shared_ptr<ManagedTask>& task);

  Status RegisterAutonomousTask(std::shared_ptr<ManagedTask>& task);

  void InspectManagedJob();

  void InspectBlockTasks();

  void InspectAutonomousTasks();

  uint32_t InnerInspectTasks(ManagedTaskSet& tasks,
                             bool statsForUncompletedTask = false);

  void InspectMetaStorageJobInfo();

  // Complete task and count for Job information
  bool CountForJobState(std::shared_ptr<ManagedTask> task);

  JobManager* job_manager_{nullptr};
  std::shared_ptr<ManagedTaskHandler> task_handler_;

  std::thread inspect_job_thread_;
  std::thread inspect_job_meta_storage_;

  std::condition_variable inspect_job_thread_cv_;
  std::condition_variable inspect_job_meta_storage_cv_;

  std::mutex inspect_job_thread_cv_mutex_;
  std::mutex inspect_job_meta_storage_cv_mutex_;

  std::atomic<bool> running_{false};

  std::unique_ptr<cnetpp::concurrency::ThreadPool> job_tracker_workers_;

  std::map<ManagedJobId, std::shared_ptr<ManagedJob>> job_id_to_progress_;

  // Only ManagedBlockTask accepted
  std::unordered_map<uint64_t, ManagedTaskSet> blk_id_to_tasks_;

  ManagedTaskSet autonomous_tasks_;

  ReadWriteLock job_progress_rwlock_;
  ReadWriteLock blk_task_rwlock_;
  ReadWriteLock autonomous_task_rwlock_;

  friend JobTrackerMetrics;
  std::unique_ptr<JobTrackerMetrics> metrics_;
  friend class JobTrackerTest;
};

}  // namespace dancenn
