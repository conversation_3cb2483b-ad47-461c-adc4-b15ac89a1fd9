//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <atomic>
#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include "block_manager/block.h"
#include "datanode_manager/datanode_info.h"
#include "hdfs.pb.h"
#include "job_manager/workflow.h"
#include "managed_task.h"
#include "namespace/namespace.h"

namespace dancenn {

class JobTracker;
class SyncEngine;
class BlockManager;

class ChainTask : public ManagedTask {
 public:
  ChainTask(ManagedJobType job_type,
            ManagedJobId job_id,
            JobManager* job_manager)
      : ManagedTask(job_type, job_id, job_manager) {
  }

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual bool IsDone() override;

  virtual bool IsTaskPlanIdempotent() override;

  virtual void AddRetryTimes() override;

  virtual void Cancel() override;

  virtual void UpdateSubmitTimePoint() override;

  virtual void UpdateState(WorkflowState state, bool track_metrics = true) override;

  virtual uint64_t GetLastSubmitTime() override;

  virtual WorkflowState GetWorkflowState() override;

  virtual void SubmitTaskPlan() override;

  virtual void ProcessBlockReport(BlockStatus blk_status) override;

  virtual void SetInodeId(uint64_t inode_id) override;

  virtual std::string ToString() const override;

  ChainTask* AppendTask(std::shared_ptr<ManagedTask> task);

  std::vector<std::shared_ptr<ManagedTask>> SubTasks() const {
    return sub_tasks_;
  }

 private:
  std::vector<std::shared_ptr<ManagedTask>> sub_tasks_;
  std::shared_ptr<ManagedTaskHandler> task_handler_;
};

class ManagedBlockTask : public ManagedTask {
 public:
  ManagedBlockTask(ManagedJobType type,
                   ManagedJobId job_id,
                   const cloudfs::ExtendedBlockProto& blk,
                   JobManager* job_manager,
                   const std::unordered_set<DatanodeID>& dn_ids =
                       std::unordered_set<DatanodeID>())
      : datanode_ids_(dn_ids), ManagedTask(type, job_id, job_manager) {
    blk_ = std::make_shared<cloudfs::ExtendedBlockProto>(blk);
  }

  std::string ToString() const override;

  uint64_t getTaskBlockId() {
    if (blk_) {
      return blk_->blockid();
    } else {
      return kInvalidBlockID;
    }
  }

  // virtual bool RegisterTask(JobTracker* job_tracker) override;

 protected:
  std::unordered_set<DatanodeID> datanode_ids_;
  std::shared_ptr<cloudfs::ExtendedBlockProto> blk_;
};

class ManagedLoadTask : public ManagedBlockTask {
 public:
  ManagedLoadTask(ManagedJobId job_id,
                  const std::unordered_set<DatanodeID>& dn_ids,
                  const cloudfs::ExtendedBlockProto& blk,
                  JobManager* job_manager)
      : ManagedBlockTask(ManagedJobType::LOAD_DATA,
                         job_id,
                         blk,
                         job_manager,
                         dn_ids) {
  }

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual void ProcessBlockReport(BlockStatus blk_status) override;

  virtual void SubmitTaskPlan() override;

 private:
  std::unordered_set<DatanodeID> issued_command_dns_;
};

class ManagedFreeTask : public ManagedBlockTask {
 public:
  ManagedFreeTask(ManagedJobId job_id,
                  const cloudfs::ExtendedBlockProto& blk,
                  JobManager* job_manager)
      : ManagedBlockTask(ManagedJobType::FREE_DATA, job_id, blk, job_manager) {
  }

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual void ProcessBlockReport(BlockStatus blk_status) override;

  virtual void SubmitTaskPlan() override;
};

class CopyReplicaTask : public ManagedBlockTask {
 public:
  CopyReplicaTask(ManagedJobId job_id,
                  const cloudfs::ExtendedBlockProto& blk,
                  int32_t required_replica,
                  JobManager* job_manager);

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual void ProcessBlockReport(BlockStatus blk_status) override;

  virtual void SubmitTaskPlan() override;

 private:
  const int32_t required_rep_;
};

class ManagedLoadMetadataTask : public ManagedTask {
 public:
  ManagedLoadMetadataTask(ManagedJobId job_id,
                          const std::shared_ptr<SyncEngine> sync_engine,
                          const std::shared_ptr<RecursiveListingOption> opt)
      : sync_engine_(sync_engine),
        list_opt_(opt),
        ManagedTask(ManagedJobType::LOAD_METADATA, job_id, nullptr) {
    cancel_flag_ = std::make_shared<bool>(false);
    list_count_ = std::make_shared<uint64_t>(0);

    list_opt_->cancel_flag = cancel_flag_;
    list_opt_->list_count = list_count_;
  }

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual void Cancel() override;

  virtual bool CountForJobState(ManagedJobState& job_state) override;

  std::string ToString() const override;

  virtual void SubmitTaskPlan() override;

 private:
  const std::shared_ptr<SyncEngine> sync_engine_;
  const std::shared_ptr<RecursiveListingOption> list_opt_;
  std::shared_ptr<bool> cancel_flag_;
  std::shared_ptr<uint64_t> list_count_;

  friend class ManagedTaskTest;
};

class ManagedReconcileINodeAttrsTask : public ManagedTask {
 public:
  ManagedReconcileINodeAttrsTask(
      ManagedJobId job_id,
      const INode& inode,
      const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
      NameSpace* ns,
      std::shared_ptr<MetaStorage> ms)
      : ManagedTask(ManagedJobType::RECONCILE_INODE_ATTRS, job_id, nullptr),
        inode_(inode),
        opt_(opt),
        ns_(ns),
        ms_(ms),
        cancel_flag_(false) {
  }

  virtual bool TaskExecTimeout() override;

  virtual bool ShouldRetry() override;

  virtual bool IsTaskComplete() override;

  virtual void Cancel() override;

  virtual bool CountForJobState(ManagedJobState& job_state) override;

  std::string ToString() const override;

 private:
  virtual void SubmitTaskPlan() override;

 private:
  INode inode_;
  std::shared_ptr<ReconcileINodeAttrsJobOption> opt_;
  NameSpace* ns_{nullptr};
  std::shared_ptr<MetaStorage> ms_;
  std::atomic<bool> cancel_flag_;
  std::atomic<uint64_t> progress_;
};

class SetReplicationTask : public ManagedTask {
 public:
  SetReplicationTask(ManagedJobId job_id,
                     const std::string& path,
                     const uint16_t replica,
                     JobManager* job_manager)
      : path_(path),
        replica_(replica),
        ManagedTask(ManagedJobType::SET_REPLICATION, job_id, job_manager) {
  }

  virtual void SubmitTaskPlan() override;

 private:
  const std::string path_;
  const uint16_t replica_;
};

class MarkResidentDataTask : public ManagedTask {
 public:
  MarkResidentDataTask(ManagedJobId job_id,
                       const std::string& path,
                       INode* inode,
                       bool is_resident,
                       JobManager* job_manager)
      : inode_(*inode),
        path_(path),
        is_resident_(is_resident),
        ManagedTask(ManagedJobType::RESIDENT_DATA, job_id, job_manager) {
    CHECK_NOTNULL(inode);
    SetInodeId(inode->id());
  }

  virtual void SubmitTaskPlan() override;

 private:
  const std::string path_;
  const INode inode_;
  const bool is_resident_;
};

}  // namespace dancenn
