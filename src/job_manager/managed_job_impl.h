//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/concurrency/thread_pool.h>

#include <atomic>
#include <memory>

#include "inode.pb.h"
#include "job_manager.h"
#include "task_iterator.h"

DECLARE_uint32(managed_job_block_task_exec_timeout_ms);
DECLARE_int32(managed_metadata_job_exec_timeout_ms);
DECLARE_int32(single_job_exec_timeout_ms);

namespace dancenn {

class JobManager;
class TaskIterator;
class TaskCreator;
class ManagedTask;

class LoadDataJob : public ManagedJob {
 public:
  /**
   * @param target_dns Some scenarios it is necessary to explicitly specify the
   * DN to track the management of blocks
   */
  LoadDataJob(
      JobManager* job_manager,
      std::shared_ptr<ManagedTaskHandler> task_handler,
      const INode* inode,
      std::shared_ptr<DataJobOption> opt,
      ManagedJobId* job_id,
      bool update_metastorage = true,
      const std::vector<DatanodeID>& target_dns = std::vector<DatanodeID>())
      : load_opt_(opt),
        ManagedJob(job_id,
                   ManagedJobType::LOAD_DATA,
                   job_manager,
                   task_handler,
                   update_metastorage) {
    std::shared_ptr<TaskCreator> task_creator =
        std::make_shared<LoadDataTaskCreator>(ManagedJobType::LOAD_DATA,
                                              GetJobId(),
                                              load_opt_,
                                              job_manager,
                                              target_dns);

    std::shared_ptr<TaskIterator> task_iterator =
        std::make_shared<BlocksInFileTaskIterator>(
            inode, job_manager, task_creator, load_opt_);
    SetTaskIterator(task_iterator);
  }

  bool ExecutionTimeout() const override {
    auto execTime = TimeUtil::GetNowEpochMs() - job_state_.GetCreateTime();
    return FLAGS_managed_job_block_task_exec_timeout_ms >= 0 &&
           execTime >= FLAGS_managed_job_block_task_exec_timeout_ms;
  }

 private:
  std::shared_ptr<DataJobOption> load_opt_;
};

class FreeDataJob : public ManagedJob {
 public:
  FreeDataJob(JobManager* job_manager,
              std::shared_ptr<ManagedTaskHandler> task_handler,
              const INode* inode,
              std::shared_ptr<DataJobOption> opt,
              ManagedJobId* job_id,
              bool update_metastorage = true)
      : free_opt_(opt),
        ManagedJob(job_id,
                   ManagedJobType::FREE_DATA,
                   job_manager,
                   task_handler,
                   update_metastorage) {
    std::shared_ptr<TaskCreator> task_creator =
        std::make_shared<FreeTaskCreator>(
            ManagedJobType::FREE_DATA, GetJobId(), job_manager);

    std::shared_ptr<TaskIterator> task_iterator =
        std::make_shared<BlocksInFileTaskIterator>(
            inode, job_manager, task_creator, free_opt_);
    SetTaskIterator(task_iterator);
  }

  bool ExecutionTimeout() const override {
    auto execTime = TimeUtil::GetNowEpochMs() - job_state_.GetCreateTime();
    return FLAGS_managed_job_block_task_exec_timeout_ms >= 0 &&
           execTime >= FLAGS_managed_job_block_task_exec_timeout_ms;
  }

 private:
  std::shared_ptr<DataJobOption> free_opt_;
};

class OrderedChainJob : public ManagedJob {
 public:
  OrderedChainJob(JobManager* job_manager,
                  std::shared_ptr<ManagedTaskHandler> task_handler,
                  std::shared_ptr<ManagedJobHandler> job_handler,
                  std::vector<std::shared_ptr<ManagedJob>>& jobs,
                  ManagedJobId* job_id)
      : sub_jobs_(jobs),
        job_handler_(job_handler),
        ManagedJob(job_id,
                   ManagedJobType::CHAIN,
                   job_manager,
                   task_handler,
                   false) {
    job_state_.SetTotalTaskNum(jobs.size());
  }

  bool ExecutionTimeout() const override {
    return false;
  }

  virtual void ConsumeManagedJob() override;
  virtual void RefreshWorkflowState() override;

 private:
  WorkflowState SubJobWorkflowState();
  size_t curr_job_idx_ = 0;
  std::shared_ptr<ManagedJobHandler> job_handler_;
  std::vector<std::shared_ptr<ManagedJob>> sub_jobs_;
  friend class ManagedJobTest;
};

class UnorderedChainJob : public ManagedJob {
 public:
  UnorderedChainJob(JobManager* job_manager,
                    std::shared_ptr<ManagedTaskHandler> task_handler,
                    std::vector<std::shared_ptr<TaskIterator>>& task_iterators,
                    ManagedJobId* job_id)
      : ManagedJob(job_id, ManagedJobType::CHAIN, job_manager, task_handler, false) {
    std::shared_ptr<TaskIterator> chain_task_iter =
        std::make_shared<ChainJobTaskIterator>(task_iterators);
    SetTaskIterator(chain_task_iter);
  }

  bool ExecutionTimeout() const override {
    return false;
  }
};

class SingleTaskJob : public ManagedJob {
 public:
  SingleTaskJob(JobManager* job_manager,
                std::shared_ptr<ManagedTaskHandler> task_handler,
                std::shared_ptr<ManagedTask>& task,
                ManagedJobType job_type,
                ManagedJobId* job_id,
                bool update_metastorage)
      : ManagedJob(job_id,
                   job_type,
                   job_manager,
                   task_handler,
                   update_metastorage) {
    std::shared_ptr<TaskIterator> task_iter =
        std::make_shared<SingleTaskIterator>(task);
    SetTaskIterator(task_iter);
  }

  bool ExecutionTimeout() const override {
    auto execTime = TimeUtil::GetNowEpochMs() - job_state_.GetCreateTime();
    return FLAGS_single_job_exec_timeout_ms >= 0 &&
           execTime >= FLAGS_single_job_exec_timeout_ms;
  }
};

class WrappedJob : public ManagedJob {
 public:
  WrappedJob(JobManager* job_manager,
             std::shared_ptr<ManagedTaskHandler> task_handler,
             const INode* inode,
             std::shared_ptr<DataJobOption> opt,
             std::shared_ptr<TaskCreator> task_creator,
             ManagedJobId* job_id)
      : job_opt_(opt),
        ManagedJob(job_id,
                   task_creator->getManagedJobType(),
                   job_manager,
                   task_handler,
                   false) {
    task_creator->setJobId(GetJobId());
    std::shared_ptr<TaskIterator> task_iterator =
        std::make_shared<CustomTaskIterator>(task_creator, opt);
    SetTaskIterator(task_iterator);
  }

  bool ExecutionTimeout() const override {
    auto execTime = TimeUtil::GetNowEpochMs() - job_state_.GetCreateTime();
    return FLAGS_managed_metadata_job_exec_timeout_ms >= 0 &&
           execTime >= FLAGS_managed_metadata_job_exec_timeout_ms;
  }

 private:
  std::shared_ptr<DataJobOption> job_opt_;
};

class ReconcileINodeAttrsJob : public ManagedJob {
 public:
  ReconcileINodeAttrsJob(
      JobManager* job_manager,
      std::shared_ptr<ManagedTaskHandler> task_handler,
      const INode& inode,
      const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
      std::shared_ptr<TaskCreator> task_creator,
      ManagedJobId* job_id)
      : job_opt_(opt),
        ManagedJob(job_id,
                   ManagedJobType::RECONCILE_INODE_ATTRS,
                   job_manager,
                   task_handler,
                   true) {
    task_creator->setJobId(GetJobId());
    std::shared_ptr<TaskIterator> task_iterator =
        std::make_shared<ReconcileINodeAttrsTaskIterator>(task_creator, opt);
    SetTaskIterator(task_iterator);
  }

  bool ExecutionTimeout() const override {
    return false;
  }

  JobInfoOpBody ToProto() override {
    auto ret = ManagedJob::ToProto();
    ret.mutable_reconcile_inode_attr_job_status()->set_path(job_opt_->path);
    return ret;
  }

 private:
  std::shared_ptr<ReconcileINodeAttrsJobOption> job_opt_;
};

}  // namespace dancenn
