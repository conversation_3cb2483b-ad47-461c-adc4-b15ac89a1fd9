//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/concurrency/thread_pool.h>

#include <memory>

#include "base/closure.h"
#include "base/status.h"
#include "job_manager/job_manager_metrics.h"
#include "job_manager_metrics.h"

namespace dancenn {

class ManagedTask;
class ManagedTaskHandlerMetrics;

class ManagedTaskHandler {
 public:
  ManagedTaskHandler();
  void Start();
  void Stop();
  Status SubmitTask(const std::shared_ptr<ManagedTask>& task);

 private:
  std::unique_ptr<cnetpp::concurrency::ThreadPool> task_workers_;

  friend class ManagedTaskHandlerMetrics;
  std::unique_ptr<ManagedTaskHandlerMetrics> metrics_;
};
}  // namespace dancenn
