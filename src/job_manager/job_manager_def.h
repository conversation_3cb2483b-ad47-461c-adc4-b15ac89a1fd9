//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <string>
#include <sstream>

namespace dancenn {

struct DataJobOption {
  bool recursive{false};
  bool metadata{false};
  bool data{false};
  uint16_t replica_num{1};
  std::string datacenter;
  std::string ufs_key;
};

struct ReconcileINodeAttrsJobOption {
  std::string path;

  std::string ToString() const {
    std::stringstream ss;
    ss << "ReconciliationJobOption: path=" << path;
    return ss.str();
  }
};

}  // namespace dancenn
