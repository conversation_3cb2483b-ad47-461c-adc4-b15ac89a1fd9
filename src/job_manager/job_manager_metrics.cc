//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "job_manager_metrics.h"

#include "job_manager/job_manager.h"
#include "job_manager/job_tracker.h"
#include "job_manager/managed_job_handler.h"

namespace dancenn {
JobManagerMetrics::JobManagerMetrics(JobManager* job_manager) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("JobManager");

  load_blk_cmd_cache_size_gauge =
      metrics->RegisterGauge("LoadBlockCmdCached", [job_manager]() -> int {
        if (!job_manager) {
          return 0;
        }
        return std::accumulate(
            job_manager->batch_load_cmd_.begin(),
            job_manager->batch_load_cmd_.end(),
            0,
            [](size_t acc, const auto& kv) { return acc + kv.second.size(); });
      });

  add_load_cmd_blk_manager_fail_counter =
      metrics->RegisterAtomicCounter("AddLoadCmdBlockManagerFail");
  add_load_cmd_blk_manager_success_counter =
      metrics->RegisterAtomicCounter("AddLoadCmdBlockManagerSuccess");
  add_load_cmd_job_manager_fail_counter =
      metrics->RegisterAtomicCounter("AddLoadCmdJobManagerFail");

  list_inodes_timer = metrics->RegisterHistogram("ListInode");
  construct_located_blocks =
      metrics->RegisterHistogram("ConstructLocatedBlocks");
}

ManagedJobHandlerMetrics::ManagedJobHandlerMetrics(
    ManagedJobHandler* job_handler) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("ManagedJobHandler");

  pending_jobs_gauge =
      metrics->RegisterGauge("PendingJobs", [job_handler]() -> int {
        return job_handler && job_handler->job_workers_
                   ? job_handler->job_workers_->PendingCount()
                   : 0;
      });

  running_jobs_gauge =
      metrics->RegisterGauge("RunningJobs", [job_handler]() -> int {
        return job_handler && job_handler->job_workers_
                   ? job_handler->job_workers_->NumRunningTasks()
                   : 0;
      });

  add_job_failed_counter = metrics->RegisterAtomicCounter("AddJobFailed");
}

ManagedTaskHandlerMetrics::ManagedTaskHandlerMetrics(
    ManagedTaskHandler* task_handler) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("ManagedTaskHandler");

  pending_tasks_gauge =
      metrics->RegisterGauge("PendingTasks", [task_handler]() -> int {
        return task_handler && task_handler->task_workers_
                   ? task_handler->task_workers_->PendingCount()
                   : 0;
      });

  running_tasks_gauge =
      metrics->RegisterGauge("RunningTasks", [task_handler]() -> int {
        return task_handler && task_handler->task_workers_
                   ? task_handler->task_workers_->NumRunningTasks()
                   : 0;
      });

  add_task_failed_counter = metrics->RegisterAtomicCounter("AddTaskFailed");
}

JobTrackerMetrics::JobTrackerMetrics(JobTracker* job_tracker) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("JobTracker");

  tracking_jobs_gauge =
      metrics->RegisterGauge("TrackingJobs", [job_tracker]() -> int {
        return job_tracker ? job_tracker->job_id_to_progress_.size() : 0;
      });

  tracking_tasks_gauge =
      metrics->RegisterGauge("TrackingTasks", [job_tracker]() -> int {
        return job_tracker ? job_tracker->blk_id_to_tasks_.size() : 0;
      });

  job_tracker_pending_gauge =
      metrics->RegisterGauge("JobTrackerPending", [job_tracker]() -> int {
        return job_tracker && job_tracker->job_tracker_workers_
                   ? job_tracker->job_tracker_workers_->PendingCount()
                   : 0;
      });

  tracker_inspector_counter = metrics->RegisterCounter("TrackerInspector");
  tracker_job_metastorage_recycle_counter =
      metrics->RegisterCounter("TrackerJobMetastorageRecycle");
  task_registed_counter = metrics->RegisterAtomicCounter("TaskRegisted");
  job_registed_counter = metrics->RegisterAtomicCounter("JobRegisted");
  shared_task_counter = metrics->RegisterAtomicCounter("SharedTask");
  job_submitted_counter = metrics->RegisterAtomicCounter("JobSubmitted");
  add_task_tracker_failed_counter =
      metrics->RegisterAtomicCounter("AddTaskTrackerFailed");
  tracked_block_task_counter =
      metrics->RegisterAtomicCounter("TrackedBlockTask");
  task_register_throttled_counter =
      metrics->RegisterAtomicCounter("TaskRegisterThrottled");

  inspect_block_tasks_timer =
      metrics->RegisterHistogram("TrackerInspect#step=block_task");
  inspect_autonomous_tasks_timer =
      metrics->RegisterHistogram("TrackerInspect#step=autonomous_task");
  inspect_job_timer = metrics->RegisterHistogram("TrackerInspect#step=job");
  inspect_ms_timer =
      metrics->RegisterHistogram("TrackerInspect#step=metastore");
  register_job_timer = metrics->RegisterHistogram("RegisterJob");
  register_task_timer = metrics->RegisterHistogram("RegisterTask");
}

ManagedJobMetrics& ManagedJobMetrics::Instance() {
  static std::once_flag flag;
  static std::unique_ptr<ManagedJobMetrics> metrics;
  std::call_once(flag, [] { metrics.reset(new ManagedJobMetrics); });
  ManagedJobMetrics* m = metrics.get();
  CHECK_NOTNULL(m);
  return *m;
}

ManagedJobMetrics::ManagedJobMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("ManagedJob");

  load_data_job_complete_timer =
      metrics->RegisterHistogram("LoadDataJobComplete");
  load_metadata_job_complete_timer =
      metrics->RegisterHistogram("LoadMetadataJobComplete");
  free_job_complete_timer = metrics->RegisterHistogram("FreeJobComplete");
  set_replica_job_complete_timer =
      metrics->RegisterHistogram("SetReplicaComplete");
  resident_data_job_complete_timer = metrics->RegisterHistogram("ResidentData");
  chain_job_complete_timer = metrics->RegisterHistogram("Chain");
  job_complete_timer = {
      {ManagedJobType::LOAD_DATA, load_data_job_complete_timer},
      {ManagedJobType::LOAD_METADATA, load_metadata_job_complete_timer},
      {ManagedJobType::SET_REPLICATION, set_replica_job_complete_timer},
      {ManagedJobType::RESIDENT_DATA, resident_data_job_complete_timer},
      {ManagedJobType::CHAIN, chain_job_complete_timer},
      {ManagedJobType::FREE_DATA, free_job_complete_timer}};

  load_data_consume_job_timer =
      metrics->RegisterHistogram("LoadDataConsumeJob");
  load_metadata_consume_job_timer =
      metrics->RegisterHistogram("LoadMetadataConsumeJob");
  free_task_consume_job_timer = metrics->RegisterHistogram("FreeConsumeJob");
  consume_job_timer = {
      {ManagedJobType::LOAD_DATA, load_data_consume_job_timer},
      {ManagedJobType::LOAD_METADATA, load_metadata_consume_job_timer},
      {ManagedJobType::FREE_DATA, free_task_consume_job_timer}};

  load_data_task_complete_timer =
      metrics->RegisterHistogram("LoadDataTaskComplete");
  load_metadata_task_complete_timer =
      metrics->RegisterHistogram("LoadMetadataTaskComplete");
  free_task_complete_timer = metrics->RegisterHistogram("FreeTaskComplete");
  load_metadata_timer = metrics->RegisterHistogram("LoadMetadata");
  reconcile_inode_attrs_timer = metrics->RegisterHistogram("ReconcileINodeAttrs");
  set_replica_task_complete_timer = metrics->RegisterHistogram("SetReplica");
  copy_replica_task_complete_timer = metrics->RegisterHistogram("CopyReplica");
  mark_resident_data_task_complete_timer =
      metrics->RegisterHistogram("MarkResident");
  chain_task_complete_timer = metrics->RegisterHistogram("Chain");
  task_complete_timer = {
      {ManagedJobType::LOAD_DATA, load_data_task_complete_timer},
      {ManagedJobType::LOAD_METADATA, load_metadata_task_complete_timer},
      {ManagedJobType::SET_REPLICATION, set_replica_task_complete_timer},
      {ManagedJobType::COPY_REPLICA, copy_replica_task_complete_timer},
      {ManagedJobType::RESIDENT_DATA, mark_resident_data_task_complete_timer},
      {ManagedJobType::CHAIN, chain_task_complete_timer},
      {ManagedJobType::FREE_DATA, free_task_complete_timer},
      {ManagedJobType::RECONCILE_INODE_ATTRS, reconcile_inode_attrs_timer}};

  job_complete_success_counter =
      metrics->RegisterAtomicCounter("CompletedSuccessJob");
  job_complete_canceled_counter =
      metrics->RegisterAtomicCounter("CompletedCanceledJob");
  job_complete_failed_counter =
      metrics->RegisterAtomicCounter("CompletedFailedJob");
  job_complete_timeout_counter =
      metrics->RegisterAtomicCounter("CompletedTimeoutJob");
  job_complete_throttled_counter =
      metrics->RegisterAtomicCounter("CompletedThrottledJob");
  job_complete_wrong_state_counter =
      metrics->RegisterAtomicCounter("CompletedWrongStateJob");
  task_complete_success_counter =
      metrics->RegisterAtomicCounter("CompletedSuccessTask");
  task_complete_canceled_counter =
      metrics->RegisterAtomicCounter("CompletedCanceledTask");
  task_complete_failed_counter =
      metrics->RegisterAtomicCounter("CompletedFailedTask");
  task_complete_timeout_counter =
      metrics->RegisterAtomicCounter("CompletedTimeoutTask");
  task_complete_throttled_counter =
      metrics->RegisterAtomicCounter("CompletedThrottledTask");
  task_complete_wrong_state_counter =
      metrics->RegisterAtomicCounter("CompletedWrongStateTask");
  complete_job_state_counters = {
      {WorkflowState::SUCCESS, job_complete_success_counter},
      {WorkflowState::CANCELED, job_complete_canceled_counter},
      {WorkflowState::FAILED, job_complete_failed_counter},
      {WorkflowState::TIMEOUT, job_complete_timeout_counter},
      {WorkflowState::THROTTLED, job_complete_throttled_counter}};
  complete_task_state_counters = {
      {WorkflowState::SUCCESS, task_complete_success_counter},
      {WorkflowState::CANCELED, task_complete_canceled_counter},
      {WorkflowState::FAILED, task_complete_failed_counter},
      {WorkflowState::TIMEOUT, task_complete_timeout_counter},
      {WorkflowState::THROTTLED, task_complete_throttled_counter}};
  free_data_task_complete_counter =
      metrics->RegisterAtomicCounter("FreeDataTaskComplete");
  load_data_task_complete_counter =
      metrics->RegisterAtomicCounter("LoadDataTaskComplete");
  copy_replica_complete_counter =
      metrics->RegisterAtomicCounter("CopyReplicaTaskComplete");

  task_retry_times_counter = metrics->RegisterCounter("TaskRetryTimes");
  task_op_timeout_counter = metrics->RegisterCounter("TaskTimeout");
  metadata_task_list_counter = metrics->RegisterCounter("MetadataJobListCnt");
}

}  // namespace dancenn