//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include <cstdint>
#include <memory>
#include <mutex>
#include <type_traits>

// The header
#include "base/closure.h"
#include "base/logger_metrics.h"
#include "base/constants.h"
#include "base/read_write_lock.h"
#include "base/status.h"
#include "block_manager/block_info.h"
#include "block_manager/datanode_command.h"
#include "edit_log.pb.h"
#include "glog/logging.h"
#include "hdfs.pb.h"
#include "inode.pb.h"
#include "job_manager.h"
#include "job_manager/managed_job_impl.h"
#include "job_manager/workflow.h"
#include "managed_job_impl.h"
#include "namespace/namespace.h"
#include "edit/edit_log_context.h"
#include "edit/sender.h"
#include "edit/sender_base.h"
#include "security/block_token_identifier.h"

DECLARE_uint32(load_cmd_batch_size);
DECLARE_uint32(load_cmd_submit_period_sec);
DECLARE_uint32(load_blk_cmd_cache_max_size);
DECLARE_int32(load_compatible_dn_version);
DECLARE_bool(job_manager_enable);
DECLARE_bool(run_ut);

namespace dancenn {

JobManager::JobManager(std::shared_ptr<DatanodeManager> datanode_manager,
                       std::shared_ptr<BlockManager> block_manager)
    : datanode_manager_(std::move(datanode_manager)),
      block_manager_(std::move(block_manager)),
      metrics_(std::make_unique<JobManagerMetrics>(this)) {
  job_handler_ = std::make_shared<ManagedJobHandler>();
  task_handler_ = std::make_shared<ManagedTaskHandler>();
  job_tracker_ = std::make_shared<JobTracker>(this, task_handler_);
}

void JobManager::SetEditLogContext(
    std::shared_ptr<EditLogContextBase> edit_log_ctx) {
  edit_log_ctx_ = std::move(edit_log_ctx);
}

void JobManager::SetEditLogSender(
    std::shared_ptr<EditLogSenderBase> edit_log_sender) {
  edit_log_sender_ = std::move(edit_log_sender);
}

void JobManager::SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage) {
  meta_storage_ = std::move(meta_storage);
}

Status JobManager::CheckJobManagerStatus() const {
  if (!FLAGS_job_manager_enable) {
    return Status(JavaExceptions::Exception::kUnsupportedOperationException,
                  "JobManager is not enable");
  } else if (!IsRunning()) {
    return Status(Code::kError, "JobManager is not running");
  } else {
    return Status();
  }
}

void JobManager::Start() {
  LOG_IF(INFO, !FLAGS_run_ut) << "JobManager starting";

  if (edit_log_sender_ == nullptr) {
    edit_log_sender_.reset(new EditLogSender(edit_log_ctx_));
  }

  job_handler_->Start();
  task_handler_->Start();
  job_tracker_->Start();

  running_.store(true);
  batch_cmd_manager_ = std::thread([this]() {
    while (IsRunning()) {
      sleep(FLAGS_load_cmd_submit_period_sec);

      auto block_manager_ptr = block_manager_.lock();
      if (!block_manager_ptr) {
        LOG(ERROR) << "Block manager is not available";
        return;
      }
      std::unique_lock<ReadWriteLock> wlock(load_cmd_rwlock_);
      for (auto it = batch_load_cmd_.begin(); it != batch_load_cmd_.end();
           ++it) {
        auto blocks = it->second;
        if (!blocks.empty()) {
          bool success = block_manager_ptr->AddLoadCmd(it->first, it->second);
          if (success) {
            metrics_->add_load_cmd_blk_manager_success_counter->Inc();
          } else {
            metrics_->add_load_cmd_blk_manager_fail_counter->Inc();
          }
        }
      }
    }
  });

  LOG_IF(INFO, !FLAGS_run_ut) << "JobManager started";
}

void JobManager::Stop() {
  StopWatch sw;
  sw.Start();

  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager stopping";
  if (!running_.load()) {
    LOG_IF(INFO, !FLAGS_run_ut)
        << "[HA] " << "JobManager stopped" << " time:" << sw.NextStepTime();
    return;
  }
  running_.store(false);

  {
    std::unique_lock<ReadWriteLock> wlock(load_cmd_rwlock_);
    batch_load_cmd_.clear();
  }
  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager: batch_load_cmd clear"
                              << " time:" << sw.NextStepTime();

  job_tracker_->Stop();
  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager: job_tracker clear"
                              << " time:" << sw.NextStepTime();

  job_handler_->Stop();
  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager: job_handler clear"
                              << " time:" << sw.NextStepTime();

  task_handler_->Stop();
  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager: task_handler clear"
                              << " time:" << sw.NextStepTime();

  batch_cmd_manager_.join();

  edit_log_sender_.reset();
  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager: edit_log_sender clear"
                              << " time:" << sw.NextStepTime();

  LOG_IF(INFO, !FLAGS_run_ut) << "[HA] " << "JobManager stopped";
}

ManagedJobId JobManager::GenerateJobId() {
  return WorkflowIdGenerator::Instance().nextJobId();
}

Status JobManager::SubmitLoadMultiReplicaJob(INode* inode,
                                             const std::string& path,
                                             std::shared_ptr<DataJobOption> opt,
                                             ManagedJobId* job_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  std::vector<std::shared_ptr<ManagedJob>> sub_jobs;
  // NOT change replias for ToBePersisted file or Append file
  if (inode->ufs_file_info().file_state() != kUfsFileStateToBePersisted &&
      inode->ufs_file_info().create_type() != kUfsFileCreateTypeAppend &&
      inode->replication() != opt->replica_num) {
    // Construct set replica job
    ManagedJobId replica_job_id = WorkflowIdGenerator::Instance().nextJobId();
    std::shared_ptr<ManagedTask> set_replica_task =
        std::make_shared<SetReplicationTask>(
            replica_job_id, path, opt->replica_num, this);
    set_replica_task->SetInodeId(inode->id());

    std::shared_ptr<ManagedJob> set_replica_job =
        std::make_shared<SingleTaskJob>(this,
                                        task_handler_,
                                        set_replica_task,
                                        ManagedJobType::SET_REPLICATION,
                                        &replica_job_id,
                                        false);
    sub_jobs.emplace_back(set_replica_job);
  }

  // Construct load data job
  ManagedJobId load_job_id = WorkflowIdGenerator::Instance().nextJobId();
  std::shared_ptr<ManagedJob> load_job = std::make_shared<LoadDataJob>(
      this, task_handler_, inode, opt, &load_job_id, false);

  sub_jobs.emplace_back(load_job);

  // Construct update pin status job
  bool pinned = inode->has_pin_status() && inode->pin_status().pinned();
  bool pinned_resident = inode->has_pin_status() &&
                         inode->pin_status().has_resident_data() &&
                         inode->pin_status().resident_data();
  if (pinned && !pinned_resident) {
    ManagedJobId resident_job_id = WorkflowIdGenerator::Instance().nextJobId();
    std::shared_ptr<ManagedTask> data_resident_task =
        std::make_shared<MarkResidentDataTask>(
            resident_job_id, path, inode, true, this);
    std::shared_ptr<ManagedJob> resident_job =
        std::make_shared<SingleTaskJob>(this,
                                        task_handler_,
                                        data_resident_task,
                                        ManagedJobType::RESIDENT_DATA,
                                        &resident_job_id,
                                        false);
    sub_jobs.emplace_back(resident_job);
  }

  // Construct ordered chain job = set_replica_job + load_job +
  // mark_data_resident_job
  // std::vector<std::shared_ptr<ManagedJob>> sub_jobs{
  //     set_replica_job, load_job, resident_job};
  std::shared_ptr<ManagedJob> chain_job = std::make_shared<OrderedChainJob>(
      this, task_handler_, job_handler_, sub_jobs, job_id);

  return job_handler_->SubmitJob(chain_job);
}

Status JobManager::SubmitLoadDataJob(INode* inode,
                                     std::shared_ptr<DataJobOption> opt,
                                     ManagedJobId* job_id,
                                     const std::vector<DatanodeID>& targets) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }
  std::shared_ptr<ManagedJob> job = std::make_shared<LoadDataJob>(
      this, task_handler_, inode, opt, job_id);
  return job_handler_->SubmitJob(job);
}

Status JobManager::SubmitFreeDataJob(INode* inode,
                                     const std::string& path,
                                     std::shared_ptr<DataJobOption> opt,
                                     ManagedJobId* job_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  std::vector<std::shared_ptr<ManagedJob>> sub_jobs;

  // Construct update pin status job
  bool pinned = inode->has_pin_status() && inode->pin_status().pinned();
  bool pinned_resident = inode->has_pin_status() &&
                         inode->pin_status().has_resident_data() &&
                         inode->pin_status().resident_data();
  if (pinned && pinned_resident) {
    ManagedJobId evict_job_id = WorkflowIdGenerator::Instance().nextJobId();
    std::shared_ptr<ManagedTask> data_evit_task =
        std::make_shared<MarkResidentDataTask>(
            evict_job_id, path, inode, false, this);
    std::shared_ptr<ManagedJob> resident_job =
        std::make_shared<SingleTaskJob>(this,
                                        task_handler_,
                                        data_evit_task,
                                        ManagedJobType::RESIDENT_DATA,
                                        &evict_job_id,
                                        false);
    sub_jobs.emplace_back(resident_job);
  }

  // Construct free data job
  ManagedJobId free_job_id = WorkflowIdGenerator::Instance().nextJobId();
  std::shared_ptr<ManagedJob> free_job = std::make_shared<FreeDataJob>(
      this, task_handler_, inode, opt, &free_job_id, false);
  sub_jobs.emplace_back(free_job);

  // Construct set replica job
  if (inode->replication() != 1) {
    ManagedJobId replica_job_id = WorkflowIdGenerator::Instance().nextJobId();
    std::shared_ptr<ManagedTask> set_replica_task =
        std::make_shared<SetReplicationTask>(replica_job_id, path, 1, this);
    std::shared_ptr<ManagedJob> set_replica_job =
        std::make_shared<SingleTaskJob>(this,
                                        task_handler_,
                                        set_replica_task,
                                        ManagedJobType::SET_REPLICATION,
                                        &replica_job_id,
                                        false);
    sub_jobs.emplace_back(set_replica_job);
  }

  // Construct ordered chain job = set_replica_job + mark_data_resident_job +
  // free_job
  std::shared_ptr<ManagedJob> chain_job = std::make_shared<OrderedChainJob>(
      this, task_handler_, job_handler_, sub_jobs, job_id);

  return job_handler_->SubmitJob(chain_job);
}

Status JobManager::SubmitJob(INode* inode,
                             std::shared_ptr<DataJobOption> opt,
                             std::shared_ptr<TaskCreator>& task_creator,
                             ManagedJobId* job_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  std::shared_ptr<ManagedJob> job = std::make_shared<WrappedJob>(
      this, task_handler_, inode, opt, task_creator, job_id);
  return job_handler_->SubmitJob(job);
}

Status JobManager::SubmitReconcileINodeAttrsJob(
    const INode& inode,
    const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
    std::shared_ptr<TaskCreator> task_creator,
    ManagedJobId* job_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  std::shared_ptr<ManagedJob> job = std::make_shared<ReconcileINodeAttrsJob>(
      this, task_handler_, inode, opt, task_creator, job_id);
  while (!job_handler_->SubmitJob(job).IsOK()) {
    LOG(WARNING) << "Failed to submit ReconcileINodeAttrsJob "
                 << opt->ToString();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
  return Status::OK();
}

void JobManager::ListSubINodes(INode* curr_inode,
                               std::string& start_after,
                               bool& has_more,
                               std::vector<INode>* inodes) {
  StopWatch sw(metrics_->list_inodes_timer);
  sw.Start();

  if (curr_inode->type() == INode_Type_kFile) {
    inodes->emplace_back(*curr_inode);
  } else if (curr_inode->type() == INode_Type_kDirectory) {
    auto ns_ptr = ns_.lock();
    if (!ns_ptr) {
      LOG(ERROR) << "Namespace is not available";
      return;
    }
    MetaStorage* meta_storage = ns_ptr->meta_storage();
    auto iter_holder = meta_storage->GetIterator();
    std::vector<INode> res;
    meta_storage->GetSubINodes(curr_inode->id(),
                               start_after,
                               500,
                               &res,
                               &has_more,
                               iter_holder->iter());
    for (auto& i : res) {
      UpdateINodeAttrs(i, *curr_inode);
      inodes->emplace_back(i);
    }

    // TODO(meiyang) HDFS mode
    if (!curr_inode->ufs_file_info().key().empty()) {
      std::string ufs_prefix =
          curr_inode->ufs_file_info().key() + kSeparatorChar;
      for (auto& inode : *inodes) {
        std::string ufs_key;
        ufs_key.append(ufs_prefix).append(inode.name());
        VLOG(10) << "Set UFS key for " << inode.id() << " : " << ufs_key;
        inode.mutable_ufs_file_info()->set_key(ufs_key);
      }
    }
  }
}

void JobManager::ConstructLocatedBlocks(const std::shared_ptr<INode>& inode_ptr,
                                        cloudfs::LocatedBlocksProto* lbs,
                                        std::vector<bool>& block_task_need_submit,
                                        const ManagedJobType& task_type,
                                        const std::string* ufs_key) {
  StopWatch sw(metrics_->construct_located_blocks);
  sw.Start();

  if (inode_ptr->type() != INode_Type_kFile) {
    return;
  }

  uint64_t file_size = 0;
  for (auto it = inode_ptr->blocks().begin(); it != inode_ptr->blocks().end();
       ++it) {
    file_size += it->numbytes();
  }
  ReadAdvice advice(kDefaultReadPolicy);
  advice.version_checker = [](ProductVersion version) -> bool {
    // Minimum version for Load&Free is 1.5.0
    return version >= FLAGS_load_compatible_dn_version;
  };

  bool pinned = inode_ptr->has_pin_status() && inode_ptr->pin_status().pinned();
  bool pinned_resident = inode_ptr->has_pin_status() &&
                         inode_ptr->pin_status().has_resident_data() &&
                         inode_ptr->pin_status().resident_data();

  auto ns_ptr = ns_.lock();
  if (!ns_ptr) {
    LOG_WITH_LEVEL(ERROR) << "Namespace is not available";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }
  uint16_t expected_replica = inode_ptr->replication();
  INode inode;
  if (ns_ptr->GetINode(inode_ptr->id(), &inode)) {
    expected_replica = inode.replication();
  }

  std::function<void(const std::vector<DetailedBlock>& blks)> block_cb;
  if (ManagedJobType::LOAD_DATA == task_type) {
    block_cb = [&block_task_need_submit,
                pinned,
                pinned_resident,
                expected_replica](const std::vector<DetailedBlock>& blks) {
      for (const DetailedBlock& blk : blks) {
        bool pin_satisfied = !pinned;
        if (!pin_satisfied) {
          for (auto& scr : blk.storage_class_report_) {
            if (scr.pinned()) {
              pin_satisfied = true;
              break;
            }
          }
        }
        VLOG(10) << "Construct block need to be loaded b"
                << blk.GetBlockID() << " live replica: " << blk.storage_.size()
                << ", expected replica: " << expected_replica
                << ", pinned: " << pinned << ", resident: " << pinned_resident;
        bool blk_need_load = blk.storage_.size() < expected_replica ||
                             !pin_satisfied || (pinned && !pinned_resident);
        if (blk.uc_ != BlockUCState::kPersisted || !blk_need_load) {
          // Do not load the block
          block_task_need_submit.emplace_back(false);
        } else {
          block_task_need_submit.emplace_back(true);
          pin_satisfied = true;
        }
      }
    };
  } else if (ManagedJobType::FREE_DATA == task_type) {
    block_cb = [&block_task_need_submit](const std::vector<DetailedBlock>& blks) {
      for (const DetailedBlock& blk : blks) {
        if (blk.uc_ != BlockUCState::kPersisted || blk.storage_.empty()) {
          // Do not free the block
          block_task_need_submit.emplace_back(false);
        } else {
          block_task_need_submit.emplace_back(true);
        }
      } 
    };
  }

  UserGroupInfo default_ugi = UserGroupInfo();
  std::vector<AccessMode> modes{AccessMode::READ};
  ns_ptr->CreateLocatedBlocks(*inode_ptr,
                              0,
                              file_size,
                              cloudfs::DATANODE_BLOCK,
                              advice,
                              false,
                              false,
                              NetworkLocationInfo(),
                              default_ugi,
                              modes,
                              lbs,
                              ufs_key,
                              block_cb);

  VLOG(10) << "JobManager create " << lbs->blocks_size()
           << " located blocks for " << *ufs_key;
}

DatanodeID JobManager::GetDatanodeInterId(const std::string& dn_uuid) {
  return datanode_manager_->GetDatanodeInterId(dn_uuid);
}

bool JobManager::AddLoadCmd(
    DatanodeID dn_id,
    std::shared_ptr<cloudfs::ExtendedBlockProto> block) {
  {
    std::shared_lock<ReadWriteLock> rlock(load_cmd_rwlock_);
    size_t total_size = std::accumulate(
        batch_load_cmd_.begin(),
        batch_load_cmd_.end(),
        0,
        [](size_t acc, const auto& kv) { return acc + kv.second.size(); });

    if (total_size > FLAGS_load_blk_cmd_cache_max_size) {
      LOG(WARNING) << total_size << " load commands be cached out of "
                   << FLAGS_load_blk_cmd_cache_max_size;
      metrics_->add_load_cmd_job_manager_fail_counter->Inc();
      return false;
    }
  }

  std::unique_lock<ReadWriteLock> wlock(load_cmd_rwlock_);
  batch_load_cmd_[dn_id].emplace_back(block);

  return true;
}

bool BlockManager::AddLoadCmd(
    DatanodeID dn_id,
    std::vector<std::shared_ptr<cloudfs::ExtendedBlockProto>>& blocks) {
  LoadCmd cmd;

  if (blocks.empty()) {
    return true;
  }

  size_t batch_max_size = FLAGS_load_cmd_batch_size;
  size_t add_cmd_size = std::min(blocks.size(), batch_max_size);
  VLOG(8) << add_cmd_size << " load commands added to block manager for " << dn_id;
  size_t cmd_added = add_cmd_size;

  for (auto it = blocks.rbegin(); it != blocks.rend() && add_cmd_size-- > 0;
       ++it) {
    auto blk = *it->get();
    VLOG(10) << "Add load cmd for blk " << blk.blockid() << " to " << dn_id;
    cmd.add_blocks()->CopyFrom(blk);
  }

  if (load_cmd_mgr_.Add(dn_id, std::move(cmd))) {
    MFC(metrics_.load_cmd_block_count_)->Inc(cmd_added);
    while (cmd_added-- > 0 && !blocks.empty()) {
      blocks.pop_back();
    }
    return true;
  } else {
    LOG(WARNING) << "Failed to add load command to block manager";
    return false;
  }
}

void JobManager::InvalidBlockCmd(
    const std::shared_ptr<cloudfs::ExtendedBlockProto> block) const {
  BlockProto blk;
  blk.set_blockid(block->blockid());
  blk.set_genstamp(block->generationstamp());
  blk.set_numbytes(block->numbytes());

  std::vector<cloudfs::BlockProto> blockToRemove(1, blk);
  VLOG(10) << "Add free cmd for blk " << block->blockid();
  auto block_manager_ptr = block_manager_.lock();
  if (!block_manager_ptr) {
    LOG(ERROR) << "Block manager is not available";
    return;
  }
  block_manager_ptr->AddBlocksToInvalidate(blockToRemove, "InvalidBlockCmd");
}

void JobManager::TrackBlockTask(uint64_t blk_id, BlockStatus status) const {
  Status checkStatus = CheckJobManagerStatus();
  if (!checkStatus.IsOK()) {
    return;
  }

  job_tracker_->TrackBlockTask(blk_id, status);
}

DetailedBlock JobManager::GetDetailedBlock(uint64_t blk_id) const {
  BlockProto b;
  b.set_blockid(blk_id);
  b.set_numbytes(0);
  auto block_manager_ptr = block_manager_.lock();
  if (!block_manager_ptr) {
    LOG(ERROR) << "Block manager is not available";
    return DetailedBlock(b);
  }
  return block_manager_ptr->GetDetailedBlock(b);
}

void JobManager::CancelTask(uint64_t blk_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return;
  }
  job_tracker_->TrackBlockTask(blk_id, BlockStatus::CANCELED);
}

void JobManager::UpdateJobStatusHA(const std::string& job_id,
                                   const ManagedJobType& job_type,
                                   const JobStatusOpBody& job_status,
                                   bool uncached,
                                   bool notify_standby) const {
  JobInfoOpBody job_info;
  job_info.set_job_id(job_id);
  job_info.set_job_type(ManagedJobTypeInfo::ConvertToOpType(job_type));
  job_info.mutable_job_status()->CopyFrom(job_status);
  job_info.set_uncached(uncached);
  job_info.set_notify_standby(notify_standby);

  auto ns_ptr = ns_.lock();
  if (!ns_ptr) {
    LOG(ERROR) << "Namespace is not available";
    return;
  }
  RpcClosure* done = NewRpcCallback();
  ns_ptr->PersistJobInfo(job_id, job_info, done);
}

std::unique_ptr<rocksdb::Iterator> JobManager::GetJobInfoIterator() const {
  auto ns_ptr = ns_.lock();
  if (!ns_ptr) {
    LOG_IF(ERROR, !FLAGS_run_ut) << "Namespace is not available";
    return nullptr;
  }
  return ns_ptr->meta_storage()->GetJobInfoIterator();
}

Status JobManager::LookupJobState(const ManagedJobId& job_id,
                                  ManagedJobState& job_state) const {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  if (job_tracker_->ContainJob(job_id)) {
    auto status = job_tracker_->LookupJobState(job_id, job_state);
    if (status.IsOK()) {
      return status;
    }
  }

  JobInfoOpBody job_info_op;
  auto ns_ptr = ns_.lock();
  if (!ns_ptr) {
    LOG(ERROR) << "Namespace is not available";
    return Status(Code::kFalse, "Namespace is not available");
  }
  MetaStorage* meta_storage = ns_ptr->meta_storage();
  if (meta_storage->GetJobInfo(job_id, job_info_op) &&
      job_info_op.has_job_status()) {
    job_state.ConvertFrom(job_info_op);
    return Status();
  } else {
    return Status(Code::kJobNotFound,
                  "Job " + job_id + " not found from meta storage");
  }
}

Status JobManager::CancelJob(const ManagedJobId& job_id) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  return job_tracker_->CancelJob(job_id);
}

Status JobManager::CompleteJob(const ManagedJobId& job_id,
                               const WorkflowState state) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  return job_tracker_->CompleteJob(job_id, state);
}

Status JobManager::ListJob(std::vector<ManagedJobId>& jobs,
                           ManagedJobType job_type) {
  Status status = CheckJobManagerStatus();
  if (!status.IsOK()) {
    return status;
  }

  return job_tracker_->ListJob(jobs, job_type);
}

std::shared_ptr<BlockManager> JobManager::GetBlockManager() const {
  std::shared_ptr<BlockManager> block_manager_ptr = block_manager_.lock();
  if (block_manager_ptr == nullptr) {
    LOG(WARNING) << "Failed to get block mananger";
    MFC(LoggerMetrics::Instance().warn_)->Inc();
  }
  return block_manager_ptr;
}

std::shared_ptr<NameSpace> JobManager::GetNameSpace() const {
  std::shared_ptr<NameSpace> ns = ns_.lock();
  if (ns == nullptr) {
    LOG(WARNING) << "Failed to get namespace";
    MFC(LoggerMetrics::Instance().warn_)->Inc();
  }
  return ns;
}

std::shared_ptr<DatanodeManager> JobManager::GetDatanodeManager() const {
  return datanode_manager_;
}

std::shared_ptr<ManagedTaskHandler> JobManager::GetTaskHandler() const {
  return task_handler_;
}

void JobManager::SetNsIdToPufsNameCB(std::function<std::string(uint64_t)> cb) {
  nsid_to_pufs_cb_ = cb;
}

std::function<std::string(uint64_t)>& JobManager::GetNsIdToPufsNameCB() {
  return nsid_to_pufs_cb_;
}

}  // namespace dancenn