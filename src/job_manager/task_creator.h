//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cstddef>

#include "acc/acc_namespace_def.h"
#include "acc/sync_engine.h"
#include "base/status.h"
#include "hdfs.pb.h"
#include "job_manager.h"
#include "job_manager/workflow.h"
#include "managed_task.h"
#include "workflow_id_generator.h"

namespace dancenn {

class JobManager;
class SyncEngine;

class TaskCreator {
 public:
  TaskCreator(ManagedJobType type,
              const ManagedJobId& job_id,
              JobManager* job_manager = nullptr)
      : type_(type), job_id_(job_id), job_manager_(job_manager) {
  }

  virtual std::shared_ptr<ManagedTask> ConstructTask() {
    return nullptr;
  };

  virtual std::shared_ptr<ManagedTask> ConstructTask(
      const cloudfs::LocatedBlockProto& lb) {
    return nullptr;
  }

  ManagedJobType getManagedJobType() {
    return type_;
  }

  void setJobId(const ManagedJobId& job_id) {
    job_id_ = job_id;
  }

 protected:
  ManagedJobId job_id_;
  ManagedJobType type_;
  JobManager* job_manager_{nullptr};
};

class LoadMetadataTaskCreator : public TaskCreator {
 public:
  LoadMetadataTaskCreator(std::shared_ptr<SyncEngine>& sync_engine,
                          const std::shared_ptr<RecursiveListingOption> opt,
                          ManagedJobType type,
                          const ManagedJobId job_id = INVALID_JOB_ID)
      : TaskCreator(type, job_id), sync_engine_(sync_engine), list_opt_(opt) {
  }

  virtual std::shared_ptr<ManagedTask> ConstructTask() override;

 private:
  bool task_constructed_ = false;
  const std::shared_ptr<SyncEngine> sync_engine_;
  const std::shared_ptr<RecursiveListingOption> list_opt_;
  std::shared_ptr<ManagedTask> current_task_;
};

class LoadDataTaskCreator : public TaskCreator {
 public:
  LoadDataTaskCreator(ManagedJobType type,
                      const ManagedJobId& job_id,
                      std::shared_ptr<DataJobOption>& opt,
                      JobManager* job_manager,
                      const std::vector<DatanodeID>& target_dns)
      : TaskCreator(type, job_id, job_manager),
        opt_(opt),
        target_dns_(target_dns) {
  }

  virtual std::shared_ptr<ManagedTask> ConstructTask(
      const cloudfs::LocatedBlockProto& lb) override;

 private:
  std::shared_ptr<DataJobOption> opt_;
  const std::vector<DatanodeID> target_dns_;
};

class FreeTaskCreator : public TaskCreator {
 public:
  FreeTaskCreator(ManagedJobType type,
                  const ManagedJobId& job_id,
                  JobManager* job_manager)
      : TaskCreator(type, job_id, job_manager) {
  }

  virtual std::shared_ptr<ManagedTask> ConstructTask(
      const cloudfs::LocatedBlockProto& lb) override;
};

class ReconcileINodeAttrsTaskCreator : public TaskCreator {
 public:
  ReconcileINodeAttrsTaskCreator(
      ManagedJobType type,
      const INode& inode,
      const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
      const ManagedJobId& job_id,
      JobManager* job_manager,
      NameSpace* ns,
      std::shared_ptr<MetaStorage> ms)
      : TaskCreator(type, job_id, job_manager),
        inode_(inode),
        opt_(opt),
        ns_(ns),
        ms_(ms) {
  }
  virtual std::shared_ptr<ManagedTask> ConstructTask() override;

 private:
  bool task_constructed_ {false};
  INode inode_;
  std::shared_ptr<ReconcileINodeAttrsJobOption> opt_;
  NameSpace* ns_{nullptr};
  std::shared_ptr<MetaStorage> ms_;
};

}  // namespace dancenn
