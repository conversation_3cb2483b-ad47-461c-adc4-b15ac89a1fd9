//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "managed_task_impl.h"

#include "job_manager.h"
#include "job_manager/workflow.h"
#include "namespace/namespace_scrub_pin.h"
#include <cstdint>
#include <iterator>
#include <string>
#include <random>
#include <cmath>

#include "base/logger_metrics.h"
#include "job_manager.h"
#include "job_manager/workflow.h"
#include "managed_task_impl.h"
#include "workflow.h"

DECLARE_uint32(managed_task_load_execute_timeout_ms);
DECLARE_uint32(managed_task_free_execute_timeout_ms);
DECLARE_uint32(copy_replica_task_execute_timeout_ms);
DECLARE_int32(managed_task_load_metadata_execute_timeout_ms);
DECLARE_int32(managed_task_load_max_retry_times);
DECLARE_int32(managed_task_free_max_retry_times);
DECLARE_int32(copy_replica_task_max_retry_times);
DECLARE_int32(managed_task_load_metadata_max_retry_times);
DECLARE_int32(load_cmd_to_job_manager_retry_times);
DECLARE_uint32(load_cmd_to_job_manager_retry_sleep_sec);
DECLARE_bool(enable_load_task_generate_ufs_name);

namespace dancenn {

bool ManagedTask::CountForJobState(ManagedJobState& job_state) {
  bool res = true;
  auto task_state = GetWorkflowState();
  if (WorkflowState::SUCCESS == task_state) {
    job_state.AddSuccessTaskNum();
  } else if (WorkflowState::FAILED == task_state) {
    job_state.AddFailedTaskNum();
  } else if (WorkflowState::CANCELED == task_state) {
    job_state.AddCanceledTaskNum();
  } else if (WorkflowState::TIMEOUT == task_state) {
    job_state.AddCanceledTaskNum();
  } else if (WorkflowState::THROTTLED == task_state) {
    job_state.AddThrottledTaskNum();
  } else {
    res = false;
  }
  return res;
}

void ManagedTask::UpdateState(WorkflowState state, bool track_metrics) {
  if (!WorkflowStateInfo::IsWorkflowDone(state_)) {
    if (state == WorkflowState::FAILED || state == WorkflowState::CANCELED ||
        state == WorkflowState::THROTTLED || state == WorkflowState::TIMEOUT) {
      LOG(INFO) << "Managed task update to "
                << WorkflowStateInfo::GetName(state) << " for " << ToString();
    }

    if (track_metrics && WorkflowStateInfo::IsWorkflowDone(state)) {
      auto counter_iter = metrics_->complete_task_state_counters.find(state);
      if (counter_iter != metrics_->complete_task_state_counters.end()) {
        counter_iter->second->Inc();
      } else {
        LOG(WARNING) << "Complete unknown state for task " << ToString();
        metrics_->task_complete_wrong_state_counter->Inc();
      }

      auto timer_iter = metrics_->task_complete_timer.find(type_);
      if (timer_iter != metrics_->task_complete_timer.end()) {
        MFH(timer_iter->second)
            ->Update(TimeUtil::GetNowEpochMs() - GetLastSubmitTime());
      } else {
        LOG_WITH_LEVEL(WARNING)
            << "Complete unknown job type for task " << ToString();
        MFC(LoggerMetrics::Instance().warn_)->Inc();
      }

      if (GetTaskType() == ManagedJobType::LOAD_DATA) {
        metrics_->load_data_task_complete_counter->Inc();
      } else if (GetTaskType() == ManagedJobType::FREE_DATA) {
        metrics_->free_data_task_complete_counter->Inc();
      } else if (GetTaskType() == ManagedJobType::COPY_REPLICA) {
        metrics_->copy_replica_complete_counter->Inc();
      }
    }
    VLOG(10) << "Update " << ToString() << " to "
             << WorkflowStateInfo::GetName(state);
    state_ = state;
  }
}

bool ManagedTask::operator()(void* arg) {
  UpdateState(WorkflowState::PROCESSING);
  SubmitTaskPlan();
  return true;
}

std::string ManagedBlockTask::ToString() const {
  std::stringstream ss;
  ss << "task_id: " << task_id_;
  ss << " job_id: " << job_id_;
  ss << " type: " << ManagedJobTypeInfo::GetName(type_);
  ss << " inode_id: " << inode_id_;
  ss << " state: " << WorkflowStateInfo::GetName(state_);
  ss << " last_submit_time: " << last_submit_time_;
  ss << " retry_times: " << retry_times_;
  ss << " blk: " << (blk_ ? blk_->blockid() : kInvalidBlockID);
  return ss.str();
}

void ManagedLoadTask::SubmitTaskPlan() {
  if (IsTaskComplete()) {
    UpdateState(WorkflowState::SUCCESS);
  }
  if (datanode_ids_.empty()) {
    LOG(INFO) << "No datanode for load block " << blk_->blockid();
    auto dn_manager = job_manager_->GetDatanodeManager();
    std::vector<DatanodeInfoPtr> dns = dn_manager->GetDatanodeInfo(
        [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsWriteable(); });

    std::stringstream add_dns;
    for (auto& dn_ptr : dns) {
      datanode_ids_.emplace(dn_ptr->id());
      add_dns << dn_ptr->hostname() << "; ";
    }
    LOG(INFO) << "Add datanodes " << add_dns.str() << " to task " << ToString();
  }

  VLOG(10) << "Submit load task " << ToString();
  for (auto dn_id : datanode_ids_) {
    if (datanode_ids_.size() == issued_command_dns_.size()) {
      issued_command_dns_.clear();
    }

    if (issued_command_dns_.find(dn_id) != issued_command_dns_.end()) {
      continue;
    }

    int retry_times = FLAGS_load_cmd_to_job_manager_retry_times;
    while (retry_times-- >= 0) {
      // Generate block pufs name
      std::function<std::string(uint64_t)>& nsid_to_pufs_name_cb = job_manager_->GetNsIdToPufsNameCB();
      if (FLAGS_enable_load_task_generate_ufs_name && nsid_to_pufs_name_cb) {
        std::string pufs_name = nsid_to_pufs_name_cb(inode_id_);
        VLOG(10) << "Generate pufs name for inode_id_ " << inode_id_
                 << " ; blk pufs name: " << blk_->blockpufsname()
                 << " ; gen pufs name: " << pufs_name;
        if (pufs_name.empty()) {
          LOG(WARNING) << "Failed to get pufs name for inode " << inode_id_;
          UpdateState(WorkflowState::CANCELED);
          return;
        }
        blk_->set_blockpufsname(pufs_name);
      } else {
        VLOG(10) << "Disable generate pufs name " << blk_->blockpufsname()
                 << "; FLAGS_enable_load_task_generate_ufs_name "
                 << FLAGS_enable_load_task_generate_ufs_name;
      }

      if (job_manager_->AddLoadCmd(dn_id, blk_)) {
        issued_command_dns_.emplace(dn_id);
        break;
      } else {
        MFC(metrics_->task_retry_times_counter)->Inc();
        LOG(WARNING) << "Failed to add load command to job manager. "
                     << retry_times << " retry times left.";
        sleep(FLAGS_load_cmd_to_job_manager_retry_sleep_sec);
      }
    }
    break;
  }
}

bool ManagedLoadTask::TaskExecTimeout() {
  auto execTime = TimeUtil::GetNowEpochMs() - last_submit_time_;
  if (execTime >=
      ScaledRandomNum(FLAGS_managed_task_load_execute_timeout_ms, 0.1)) {
    MFC(metrics_->task_op_timeout_counter)->Inc();
    return true;
  } else {
    return false;
  }
}

bool ManagedLoadTask::ShouldRetry() {
  return retry_times_ < FLAGS_managed_task_load_max_retry_times;
}

bool ManagedLoadTask::IsTaskComplete() {
  auto blk = job_manager_->GetDetailedBlock(getTaskBlockId());

  if (blk.GetBlockSize() == 0) {
    // Block has been deleted. Cancel this task.
    LOG(INFO) << "Load task be canceled because B" << getTaskBlockId()
              << " has been deleted";
    UpdateState(WorkflowState::CANCELED);
    return true;
  }

  return blk.uc_ == BlockUCState::kPersisted && blk.storage_.size() > 0;
}

void ManagedLoadTask::ProcessBlockReport(BlockStatus blk_status) {
  if (IsDone()) {
    return;
  }
  switch (blk_status) {
    case BlockStatus::RECEIVED:
      UpdateState(WorkflowState::SUCCESS);
      break;
    case BlockStatus::DELETED:
      LOG(INFO) << "Load task be canceled because B" << getTaskBlockId()
                << " replica has been deleted";
      UpdateState(WorkflowState::CANCELED);
      break;
    case BlockStatus::CANCELED:
      UpdateState(WorkflowState::CANCELED);
      break;
    case BlockStatus::FAILED:
      UpdateState(WorkflowState::FAILED);
      break;
    default:
      LOG_WITH_LEVEL(ERROR)
          << "Unexpected block status " << BlockStatusInfo::GetName(blk_status)
          << " for load task " << task_id_;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      break;
  }
}

bool ManagedFreeTask::TaskExecTimeout() {
  auto execTime = TimeUtil::GetNowEpochMs() - last_submit_time_;
  return execTime >= FLAGS_managed_task_free_execute_timeout_ms;
}

bool ManagedFreeTask::ShouldRetry() {
  return retry_times_ < FLAGS_managed_task_free_max_retry_times;
}

void ManagedFreeTask::SubmitTaskPlan() {
  VLOG(10) << "Submit free task " << ToString();
  job_manager_->InvalidBlockCmd(blk_);
}

bool ManagedFreeTask::IsTaskComplete() {
  auto blk = job_manager_->GetDetailedBlock(getTaskBlockId());
  if (blk.GetBlockSize() == 0) {
    LOG(INFO) << "Free task be canceled " << ToString();
    // Block has been deleted. Cancel this task.
    UpdateState(WorkflowState::SUCCESS);
    return true;
  }
  return blk.uc_ == BlockUCState::kPersisted && blk.live_replica_ == 0;
}

void ManagedFreeTask::ProcessBlockReport(BlockStatus blk_status) {
  switch (blk_status) {
    case BlockStatus::RECEIVED:
      break;
    case BlockStatus::DELETED:
      if (IsTaskComplete()) {
        UpdateState(WorkflowState::SUCCESS);
      }
      break;
    case BlockStatus::CANCELED:
      UpdateState(WorkflowState::CANCELED);
      break;
    case BlockStatus::FAILED:
      UpdateState(WorkflowState::FAILED);
      break;
    default:
      LOG_WITH_LEVEL(ERROR)
          << "Unexpected block status " << BlockStatusInfo::GetName(blk_status)
          << " for free task " << task_id_;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      break;
  }
}

CopyReplicaTask::CopyReplicaTask(ManagedJobId job_id,
                                 const cloudfs::ExtendedBlockProto& blk,
                                 int32_t required_replica,
                                 JobManager* job_manager)
    : ManagedBlockTask(ManagedJobType::COPY_REPLICA, job_id, blk, job_manager),
      required_rep_(required_replica) {
  UpdateSubmitTimePoint();
}

bool CopyReplicaTask::TaskExecTimeout() {
  auto execTime = TimeUtil::GetNowEpochMs() - last_submit_time_;
  return execTime >= ScaledRandomNum(FLAGS_copy_replica_task_execute_timeout_ms, 0.1);
}

bool CopyReplicaTask::ShouldRetry() {
  return retry_times_ < FLAGS_copy_replica_task_max_retry_times;
}

void CopyReplicaTask::SubmitTaskPlan() {
  if (IsTaskComplete()) {
    UpdateState(WorkflowState::SUCCESS);
    return;
  }
  VLOG(10) << "Submit copy replica task " << ToString();
  auto block_manager_ptr = job_manager_->GetBlockManager();
  if (!block_manager_ptr) {
    LOG(WARNING) << "Block manager is not available for copy replica task";
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return;
  }
  block_manager_ptr->AddTransferBlocks(
      blk_->blockid(),
      required_rep_,
      UnderReplicatedBlocks::Priority::kUnderReplicated);
}

bool CopyReplicaTask::IsTaskComplete() {
  // check live number of replicas
  auto blk = job_manager_->GetDetailedBlock(getTaskBlockId());

  // Check if the block has been deleted.
  if (blk.GetBlockSize() == 0) {
    // Block has been deleted. Cancel this task.
    LOG(INFO) << "Copy replica task be canceled because B" << getTaskBlockId()
              << " has been deleted";
    UpdateState(WorkflowState::CANCELED);
    return true;
  } else {
    return required_rep_ <= blk.storage_.size();
  }
}

void CopyReplicaTask::ProcessBlockReport(BlockStatus blk_status) {
  DetailedBlock blk;
  switch (blk_status) {
    case BlockStatus::RECEIVED:
      blk = job_manager_->GetDetailedBlock(getTaskBlockId());
      if (blk.storage_.size() >= required_rep_) {
        UpdateState(WorkflowState::SUCCESS); 
      }
      break;
    case BlockStatus::DELETED: {
      blk = job_manager_->GetDetailedBlock(getTaskBlockId());

      // Check if the block has been deleted.
      if (blk.GetBlockSize() == 0) {
        // Block has been deleted. Cancel this task.
        LOG(INFO) << "Copy replica task be canceled because B"
                  << getTaskBlockId() << " has been deleted";
        UpdateState(WorkflowState::CANCELED);
      } 
      break;
    }
    case BlockStatus::CANCELED:
      LOG(INFO) << "Copy replica task be canceled";
      UpdateState(WorkflowState::CANCELED);
      break;
    case BlockStatus::FAILED:
      UpdateState(WorkflowState::FAILED);
      break;
    default:
      LOG(ERROR) << "Unexpected block status "
                 << BlockStatusInfo::GetName(blk_status)
                 << " for copy replica task " << GetTaskId();
      MFC(LoggerMetrics::Instance().error_)->Inc();
      break;
  }
}

bool ChainTask::TaskExecTimeout() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      return task->TaskExecTimeout();
    }
  }
  return false;
}

bool ChainTask::IsTaskComplete() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      return task->IsTaskComplete();
    }
  }
  return true;
}

bool ChainTask::IsDone() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      return false;
    }
  }
  return true;
}

bool ChainTask::ShouldRetry() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      return task->ShouldRetry();
    }
  }
  return false;
}

bool ChainTask::IsTaskPlanIdempotent() {
  for (auto& task : sub_tasks_) {
    if (!task->IsTaskPlanIdempotent()) {
      return false;
    }
  }
  return true;
}

void ChainTask::AddRetryTimes() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      task->AddRetryTimes();
      return;
    }
  }
}

void ChainTask::Cancel() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      task->Cancel();
      return;
    }
  }
}

void ChainTask::UpdateSubmitTimePoint() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      task->UpdateSubmitTimePoint();
      return;
    }
  }
}

void ChainTask::UpdateState(WorkflowState state, bool track_metrics) {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      task->UpdateState(state, track_metrics);
      return;
    }
  }
}

uint64_t ChainTask::GetLastSubmitTime() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      return task->GetLastSubmitTime();
    }
  }
  return TimeUtil::GetNowEpochMs();
}

WorkflowState ChainTask::GetWorkflowState() {
  for (auto& task : sub_tasks_) {
    if (task->IsDone()) {
      if (task->GetWorkflowState() != WorkflowState::SUCCESS) {
        return task->GetWorkflowState();
      }
    } else {
      return task->GetWorkflowState();
    }
  }
  return WorkflowState::SUCCESS;
}

void ChainTask::SubmitTaskPlan() {
  for (auto& task : sub_tasks_) {
    if (!task->IsDone()) {
      job_manager_->GetTaskHandler()->SubmitTask(task);
      return;
    }
  }
}

void ChainTask::ProcessBlockReport(BlockStatus blk_status) {
  bool need_submit_next_task = true;
  for (auto& task : sub_tasks_) { 
    if (!task->IsDone()) {
      task->ProcessBlockReport(blk_status);

      WorkflowState curr_state = task->GetWorkflowState();
      if (curr_state == WorkflowState::PROCESSING) {
        need_submit_next_task = false; 
      } else if (need_submit_next_task && curr_state == WorkflowState::CREATED) {
        job_manager_->GetTaskHandler()->SubmitTask(task);
        need_submit_next_task = false;
      }
    }
  }
}

std::string ChainTask::ToString() const {
  std::stringstream ss;
  for (auto& task : sub_tasks_) {
    ss << task->ToString();
    ss << "; ";
  }
  return ss.str();
}

void ChainTask::SetInodeId(uint64_t inode_id) {
  for (auto& task : sub_tasks_) {
    task->SetInodeId(inode_id);
  }
}

ChainTask* ChainTask::AppendTask(std::shared_ptr<ManagedTask> task) {
  sub_tasks_.emplace_back(task);
  return this;
}

bool ManagedLoadMetadataTask::TaskExecTimeout() {
  auto execTime = TimeUtil::GetNowEpochMs() - last_submit_time_;
  return FLAGS_managed_task_load_metadata_execute_timeout_ms >= 0 &&
         execTime >= FLAGS_managed_task_load_metadata_execute_timeout_ms;
}

bool ManagedLoadMetadataTask::ShouldRetry() {
  return retry_times_ < FLAGS_managed_task_load_metadata_max_retry_times;
}

void ManagedLoadMetadataTask::SubmitTaskPlan() {
  VLOG(10) << "Submit load metadata task " << ToString();
  while (true) {
    Status status = sync_engine_->SyncRecursiveListing(
        list_opt_, TimeUtil::GetNowEpochMs() / 1000);

    VLOG(10) << "MetadataTask execute sync recursive listing done ";
    if (status.IsOK() || status.code() == Code::kBadParameter) {
      UpdateState(WorkflowState::SUCCESS);
      break;
    } else {
      auto && path = list_opt_->listing_opt.inner_path;
      if (*cancel_flag_) {
        LOG(INFO) << "Sync list metadata task be cancelled " << ToString();
        UpdateState(WorkflowState::CANCELED);
        break;
      } else if (ShouldRetry()) {
        LOG(INFO) << "Failed to sync list metadata task " << ToString()
                     << " retry later. Because " << status.ToString();
        AddRetryTimes();
        continue;
      } else {
        LOG(INFO) << "Failed to sync list metadata task " << ToString()
                  << " because " << status.ToString();
        bool track_metrics = true;
        if (status.code() == Code::kFileNotFound ||
            status.code() == Code::kDirNotFound) {
          track_metrics = false;
        }
        UpdateState(WorkflowState::FAILED, track_metrics);
        break;
      }
    }
  }
}

bool ManagedLoadMetadataTask::IsTaskComplete() {
  return false;
}

void ManagedLoadMetadataTask::Cancel() {
  LOG(INFO) << "Canceling load metadata task for " << list_opt_->listing_opt.inner_path;
  *cancel_flag_ = true;
}

bool ManagedLoadMetadataTask::CountForJobState(ManagedJobState& job_state) {
  uint64_t success_count = *list_count_;
  if (IsDone()) {
    LOG(INFO) << "Metadata task sync list count " << success_count
              << " for job " << job_id_;
    MFC(metrics_->metadata_task_list_counter)->Inc(success_count);
    
    job_state.SetSuccessTaskNum(success_count);
    if (state_ == WorkflowState::THROTTLED) {
      job_state.AddThrottledTaskNum();
    } else if (state_ == WorkflowState::TIMEOUT) {
      job_state.AddTimeoutTaskNum();
    } else if (state_ == WorkflowState::FAILED) {
      job_state.AddFailedTaskNum();
    }
    job_state.SetTotalTaskNum(job_state.GetCompletedTaskNum());
    return true;
  } else {
    // If the total number of tasks is equal to the number of successful tasks,
    // the job will be updated to the "completed" state.
    job_state.SetTotalTaskNum(success_count + 1);
    job_state.SetSuccessTaskNum(success_count);
    return false;
  }
}

std::string ManagedLoadMetadataTask::ToString() const {
  std::stringstream ss;
  ss << "task_id: " << task_id_;
  ss << " job_id: " << job_id_;
  ss << " type: " << ManagedJobTypeInfo::GetName(type_);
  ss << " inode_id: " << inode_id_;
  ss << " state: " << WorkflowStateInfo::GetName(state_);
  ss << " last_submit_time: " << last_submit_time_;
  ss << " retry_times: " << retry_times_;
  ss << " path: " << (list_opt_ ? list_opt_->listing_opt.path : "null");
  ss << " inner path: " << (list_opt_ ? list_opt_->listing_opt.inner_path : "null");
  ss << " cancel_flag: " << (cancel_flag_ ? *cancel_flag_ : false);
  ss << " list_count: " << (list_count_? *list_count_ : 0);
  return ss.str();
}

bool ManagedReconcileINodeAttrsTask::TaskExecTimeout() {
  return false;
}

bool ManagedReconcileINodeAttrsTask::ShouldRetry() {
  return false;
}

void ManagedReconcileINodeAttrsTask::SubmitTaskPlan() {
  VLOG(8) << "Start reconcile inode attrs: " << inode_.id()
          << ", opt: " << opt_->ToString();
  NameSpaceScrub scrub(
      inode_, SCRUB_OPTYPE_PIN, SCRUB_ACTION_FORCE_OVERWRITE, ns_, ms_);
  scrub.Start();
  while (!scrub.WaitForDone(1)) {
    if (cancel_flag_) {
      scrub.Stop();
      break;
    }
    VLOG(8) << "Reconcile inode attr task still working " << inode_.id();
  }
  ScrubProgress progress;
  scrub.GetProgress(&progress);
  progress_ = progress.finished_inodes + progress.finished_dirs;
  UpdateState(WorkflowState::SUCCESS);
  VLOG(8) << "Finish reconcile inode attrs: " << inode_.id();
}

bool ManagedReconcileINodeAttrsTask::IsTaskComplete() {
  return false;
}

void ManagedReconcileINodeAttrsTask::Cancel() {
  LOG(INFO) << "Cancel reconcile inode task " << inode_.id()
            << ", opt: " << opt_->ToString();
  cancel_flag_ = true;
}

bool ManagedReconcileINodeAttrsTask::CountForJobState(ManagedJobState& job_state) {
  uint64_t success_count = progress_;
  if (IsDone()) {
    job_state.SetTotalTaskNum(success_count);
    job_state.SetSuccessTaskNum(success_count);
    return true;
  } else {
    job_state.SetTotalTaskNum(success_count + 1);
    job_state.SetSuccessTaskNum(success_count);
    return false;
  }
}

std::string ManagedReconcileINodeAttrsTask::ToString() const {
  std::stringstream ss;
  ss << "task_id: " << task_id_;
  ss << " job_id: " << job_id_;
  ss << " type: " << ManagedJobTypeInfo::GetName(type_);
  ss << " inode_id: " << inode_id_;
  ss << " state: " << WorkflowStateInfo::GetName(state_);
  ss << " last_submit_time: " << last_submit_time_;
  ss << " retry_times: " << retry_times_;
  ss << " inode: " << inode_.ShortDebugString();
  ss << " opt: " << opt_->ToString();
  ss << " cancel_flag: " << cancel_flag_;
  ss << " processed_count: " << progress_;
  return ss.str();
}

void SetReplicationTask::SubmitTaskPlan() {
  VLOG(10) << "Submit set replication task " << ToString();
  auto ns = job_manager_->GetNameSpace();
  if (ns == nullptr) {
    LOG(ERROR) << "Failed to get ns for set replication task "
               << ". Path " << path_ << "; Replication " << replica_;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    UpdateState(WorkflowState::FAILED);
  }
  SynchronizedRpcClosure done;
  auto ugi = UserGroupInfo();
  ns->AsyncSetReplication(path_, replica_, false, ugi, &done);
  done.Await();
  if (done.status().IsOK()) {
    UpdateState(WorkflowState::SUCCESS);
  } else {
    LOG(WARNING) << "Failed to set replication task "
                 << ". Path " << path_ << "; Replication " << replica_
                 << done.status().ToString();
    UpdateState(WorkflowState::FAILED);
  }
}

void MarkResidentDataTask::SubmitTaskPlan() {
  VLOG(10) << "Submit mark resident data task " << ToString();
  auto ns = job_manager_->GetNameSpace();
  if (ns == nullptr) {
    LOG(ERROR) << "Failed to get ns for mark data resident task ";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    UpdateState(WorkflowState::FAILED);
  }
  SynchronizedRpcClosure done;
  ns->UpdatePinDataResident(path_, inode_, is_resident_, &done);
  done.Await();

  auto status = done.status();
  if (status.IsOK() || status.code() == Code::kFileStatusError) {
    UpdateState(WorkflowState::SUCCESS);
  } else {
    LOG(WARNING) << "Failed to mark data resident task "
                 << ". Path " << path_ << "; inode id "
                 << std::to_string(inode_.id()) << status.ToString();
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    UpdateState(WorkflowState::FAILED);
  }
}

uint32_t ManagedTask::ScaledRandomNum(uint32_t value, double coefficient) {
  if (coefficient <= 0.0 || coefficient >= 1.0) {
    return value;
  }
  static std::random_device rd;
  static std::mt19937 gen(rd());
  static std::uniform_real_distribution<> dis(1 - coefficient, 1 + coefficient);
  double rand_coefficient = dis(gen);
  return std::ceil(value * rand_coefficient);
}

}  // namespace dancenn