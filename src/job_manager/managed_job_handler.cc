//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "managed_job_handler.h"

#include <memory>

#include "base/status.h"
#include "gflags/gflags.h"
#include "job_manager/job_tracker.h"
#include "job_manager_metrics.h"

DECLARE_uint32(job_handler_thread_num);
DECLARE_uint32(job_handler_thread_pool_queue_size);

namespace dancenn {

using ThreadPool = cnetpp::concurrency::ThreadPool;
using ThreadPoolTask = cnetpp::concurrency::Task;

ManagedJobHandler::ManagedJobHandler()
    : metrics_(std::make_unique<ManagedJobHandlerMetrics>(this)) {
}

void ManagedJobHandler::Start() {
  LOG(INFO) << "ManagedJobHandler starting.";
  job_workers_ = std::make_unique<ThreadPool>("JobHandler", true);
  job_workers_->set_num_threads(FLAGS_job_handler_thread_num);
  job_workers_->set_max_num_pending_tasks(
      FLAGS_job_handler_thread_pool_queue_size);
  job_workers_->Start();
  LOG(INFO) << "ManagedJobHandler started.";
}

void ManagedJobHandler::Stop() {
  LOG(INFO) << "ManagedJobHandler stopping.";
  job_workers_->Stop();
  LOG(INFO) << "ManagedJobHandler stopped.";
}

Status ManagedJobHandler::SubmitJob(const std::shared_ptr<ManagedJob>& job) {
  if (!job) {
    LOG(ERROR) << "Submit null job";
    return Status(Code::kError, "Submit null job");
  }
  if (!job_workers_->AddTask(std::static_pointer_cast<ThreadPoolTask>(job))) {
    metrics_->add_job_failed_counter->Inc();
    LOG(WARNING) << "Failed to add task to managed job handler. Pendingtask num: "
                 << job_workers_->PendingCount();
    return Status(JavaExceptions::Exception::kThrottlerException,
                  "Pending job out of managed job handler queue size");
  }
  return Status();
}

}  // namespace dancenn
