// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef RPC_CALL_TASK_H_
#define RPC_CALL_TASK_H_

#include <cnetpp/concurrency/task.h>
#include <glog/logging.h>

#include <memory>
#include <utility>
#include <chrono>
#include <string>

#include "service/service_meta.h"
#include "rpc/rpc_controller.h"
#include "rpc/done_closure.h"

namespace dancenn {

class RpcServer;

class CallTask : public cnetpp::concurrency::Task {
 public:
  CallTask(RpcServer* server,
           std::shared_ptr<ServiceMeta> sm,
           const google::protobuf::MethodDescriptor* md,
           MethodMeta mm,
           std::unique_ptr<RpcController>&& controller)
      : server_(server),
        service_meta_(std::move(sm)),
        md_(md),
        mm_(std::move(mm)),
        rpc_controller_(std::move(controller)),
        start_(std::chrono::system_clock::now()) {
  }

  virtual ~CallTask() = default;

  bool operator()(void* arg = nullptr) override;

  std::shared_ptr<ServiceMeta> service_meta() const {
    return service_meta_;
  }
  std::string ToString() const {
    return "[CallTask " + std::to_string(reinterpret_cast<uint64_t>(this)) +
      "] #" + std::to_string(rpc_controller_->rpc_request_header()->callid());
  }

 private:
  RpcServer* server_{nullptr};
  std::shared_ptr<ServiceMeta> service_meta_;
  const google::protobuf::MethodDescriptor* md_{nullptr};
  MethodMeta mm_;
  std::unique_ptr<RpcController> rpc_controller_;

  std::chrono::system_clock::time_point start_;
};

}  // namespace dancenn

#endif  // RPC_CALL_TASK_H_
