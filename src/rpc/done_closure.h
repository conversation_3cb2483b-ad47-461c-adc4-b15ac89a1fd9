// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef RPC_DONE_CLOSURE_H_
#define RPC_DONE_CLOSURE_H_

#include <google/protobuf/service.h>

#include <memory>
#include <utility>

#include "rpc/rpc_controller.h"
#include "service/service_meta.h"

namespace dancenn {

class RpcServer;

class DoneClosure : public google::protobuf::Closure {
 public:
  static std::unique_ptr<google::protobuf::Closure> New(
      RpcServer* server,
      std::unique_ptr<RpcController>&& controller,
      MethodMeta::MethodType method_type) {
    return std::unique_ptr<google::protobuf::Closure>(
        new DoneClosure(server, std::move(controller), method_type));
  }

  ~DoneClosure() override = default;

  void Run() override;

  std::unique_ptr<RpcController>& rpc_controller() {
    return rpc_controller_;
  }

 protected:
  DoneClosure(RpcServer* server,
              std::unique_ptr<RpcController>&& controller,
              MethodMeta::MethodType method_type);

  RpcServer* server_{nullptr};
  std::unique_ptr<RpcController> rpc_controller_;
  std::chrono::system_clock::time_point start_;
  MethodMeta::MethodType method_type_;
};

class WrappedDoneClosure : public google::protobuf::Closure {
 public:
  using Callback = std::function<void(RpcController* c)>;

  WrappedDoneClosure(RpcController* controller,
                     google::protobuf::Closure* done,
                     Callback func)
      : controller_(controller), done_(done), func_(std::move(func)) {
    CHECK_NOTNULL(controller);
    CHECK_NOTNULL(done);
  }
  ~WrappedDoneClosure() override = default;

  void Run() override {
    func_(controller_);
    done_->Run();
    delete this;
  }

 private:
  RpcController* controller_{nullptr};
  google::protobuf::Closure* done_{nullptr};
  Callback func_;
};

}  // namespace dancenn

#endif  // RPC_DONE_CLOSURE_H_

