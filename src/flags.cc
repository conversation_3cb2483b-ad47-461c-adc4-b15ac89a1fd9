// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gflags/gflags.h>
#include <rocksdb/perf_level.h>

#include <climits>
#include <cmath>
#include <string>

#include "base/base64.h"
#include "base/platform.h"
#include "hdfs.pb.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/namespace.h"

static bool ValidateAuthentication(const char* flag, const std::string& v) {
  (void)flag;
  if (v != "simple" && v != "kerberos" && v != "token") {
    return false;
  }
  return true;
}

static bool ValidateBPPolicy(const char* flag, const std::string& v) {
  (void)flag;
  if (v != "default" &&
      v != "multi-dc" &&
      v != "nodezone" &&
      v != "cfs-default" &&
      v != "cfs-multi-dc") {
    return false;
  }
  return true;
}

static bool ValidateBPDistributionType(const char* flag, const std::string& v) {
  (void)flag;
  if (v != "round-robin" && v != "uniform" && v != "geometric" &&
      v != "weight") {
    return false;
  }
  return true;
}

static bool ValidateNamespaceType(const char* flag, int32_t v) {
  (void)flag;
  return v >= 0 && v < static_cast<int32_t>(cloudfs::NamespaceType::BOUNDARY);
}

static bool ValidateDfsReplicationMin(const char* flag, uint32_t v) {
  (void)flag;
  return v > 0;
}

static bool ValidateDfsReplicationMax(const char* flag, uint32_t v) {
  (void)flag;
  // dancenn use uint8_t(255) to store capacity, and use v*2-1 as max value
  return v * 2 - 1 <= 255;
}

static bool ValidatePermissionModel(const char* /*flag*/,
                                    const std::string& v) {
  return v == "posix" || v == "ranger";
}

static bool ValidateAccBatchAddEditLogBatchSize(const char* /*flag*/,
                                                uint32_t v) {
  return v <= 1000;
}

static bool ValidateDefaultStorageClass(const char* flag,
                                        const std::string& v) {
  StorageClassProto cls;
  return dancenn::StorageClassName2ID(v, &cls);
}

static bool ValidateEditLogApplyMode(const char* flag, uint32_t v) {
  return v < static_cast<int>(dancenn::ApplyMode::NUM_MODES);
}


DEFINE_bool(run_ut, false, "run UT, Reduce log");

// RPC related
DEFINE_string(listen_ip_address,
              "0.0.0.0",
              "the listen address for all service");
DEFINE_int32(client_rpc_port, 5060, "the rpc port for ClientNamenodeProtocol");
DEFINE_int32(client_rpc_backlog,
             1024,
             "the backlog of rpc for ClientNamenodeProtocol");
DEFINE_int32(client_normal_rpc_handler_count,
             32,
             "the num of normal rpc workers for ClientNamenodeProtocol");
DEFINE_int32(client_slow_rpc_handler_count,
             32,
             "the num of slow rpc workers for ClientNamenodeProtocol");
DEFINE_int32(client_veryslow_rpc_handler_count,
             8,
             "the num of very slow rpc workers for ClientNamenodeProtocol");
DEFINE_int32(
    client_veryveryslow_rpc_handler_count,
    8,
    "the num of very very slow rpc workers for ClientNamenodeProtocol");
DEFINE_int32(client_recover_lease_rpc_handler_count,
             8,
             "the num of recover lease rpc workers for ClientNamenodeProtocol");
DEFINE_int32(client_rpc_network_thread_count,
             0,
             "the number of rpc threads for ClientNamenodeProtocol");
DEFINE_int32(client_rpc_tcp_send_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(client_rpc_tcp_recv_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(client_rpc_send_buffer_size, 0, "send buffer size");
DEFINE_int32(client_rpc_recv_buffer_size, 0, "send buffer size");

DEFINE_int32(datanode_rpc_port, 5061, "the rpc port for DatanodeProtocol");
DEFINE_int32(datanode_rpc_backlog,
             1024,
             "the backlog of rpc for DatanodeProtocol");
DEFINE_int32(datanode_normal_rpc_handler_count,
             16,
             "the number of normal rpc workers for DatanodeProtocol");
DEFINE_int32(datanode_slow_rpc_handler_count,
             8,
             "the number of slow rpc workers for DatanodeProtocol");
DEFINE_int32(datanode_veryslow_rpc_handler_count,
             2,
             "the number of very slow rpc workers for DatanodeProtocol");
DEFINE_int32(datanode_rpc_network_thread_count,
             0,
             "the number of rpc threads for DatanodeProtocol");
DEFINE_int32(datanode_rpc_tcp_send_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(datanode_rpc_tcp_recv_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(datanode_rpc_send_buffer_size, 0, "send buffer size");
DEFINE_int32(datanode_rpc_recv_buffer_size, 0, "send buffer size");

DEFINE_int32(placement_driver_rpc_port,
             5065,
             "the rpc port for PlacementDriverProtocol");
DEFINE_int32(placement_driver_rpc_backlog,
             1024,
             "the backlog of rpc for PlacementDriverProtocol");
DEFINE_int32(placement_driver_rpc_handler_count,
             32,
             "the num of rpc workers for PlacementDriverProtocol");
DEFINE_int32(placement_driver_rpc_network_thread_count,
             0,
             "the number of rpc threads for PlacementDriverProtocol");
DEFINE_int32(placement_driver_rpc_tcp_send_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(placement_driver_rpc_tcp_recv_buffer_size,
             32 * 1024,
             "tcp send buffer size");
DEFINE_int32(placement_driver_rpc_send_buffer_size, 0, "send buffer size");
DEFINE_int32(placement_driver_rpc_recv_buffer_size, 0, "send buffer size");

DEFINE_string(placement_driver_hosts,
              "127.0.0.1",
              "hosts running placement driver (separate with comma)");

DEFINE_bool(log_rpc_all_rpc, false, "Log all RPC Req/Res");
DEFINE_bool(log_rpc_client_rpc, false, "Log Client RPC Req/Res");
DEFINE_bool(log_rpc_app_err,
            false,
            "Log App Error (like FileNotFoundException)");
DEFINE_bool(log_edit_log_detail, true, "log editlog detail");
DEFINE_bool(log_stale_read, true, "Log when stale read");
DEFINE_bool(log_dn_all_ibr, true, "log dn ibr to upgrade dn");
DEFINE_bool(log_dn_negoed_ibr, true, "log dn negoed ibr to upgrade dn");
DEFINE_bool(log_dn_cmd_overflow, true, "log when dn command overflow");
DEFINE_bool(log_send_ne_cmd, true, "log when send evictable command");
DEFINE_bool(log_block_map_detail, false, "log detail about block map");
DEFINE_bool(log_ufs_sync_detail, true, "log detail about ufs do sync");
DEFINE_bool(log_ufs_persist_detail, true, "log detail about ufs do persist");
DEFINE_bool(log_managed_task_detail, true, "log detail about managed task");
DEFINE_bool(log_block_corrupt, true, "log detail when block corrupt");
DEFINE_bool(log_block_transfer_detail, true, "log detail about block transfer");
DEFINE_bool(log_choose_target, false, "Log when ChooseTarget");
DEFINE_bool(log_ha_lock_detail, false, "Log ha lock detail");
DEFINE_bool(log_evict_delete_file,
            true,
            "log detail about dn evict trigger delete file");
DEFINE_bool(log_write_back_policy_detail,
            false,
            "if true, print detail about write_back_policy");

DEFINE_int32(nodezone_map_refresh_period_ms,
             3 * 60 * 1000,
             "nodezone_map refresh period");
DEFINE_string(nodezone_map_config_file_path,
              "/opt/tiger/dancenn_deploy/conf_common/nodezone_map.txt",
              "the file path for nodezone_map config");

DEFINE_int32(dir_to_nodezone_group_refresh_period_ms,
             1 * 60 * 1000,
             "dir_to_nodezonetag refresh period");
DEFINE_string(dir_to_nodezone_group_config_file_path,
              "/opt/tiger/dancenn_deploy/conf_common/dir_to_nodezone_map.txt",
              "the file path for dir_to_nodezone_group config");

DEFINE_string(nameservice, "", "the nameservice");

DEFINE_int64(request_ttl_since_emit_ms, 25 * 60 * 1000,
             "request ttl since emit in ms");

DEFINE_int32(ha_rpc_port, 5062, "the rpc port for HAServiceProtocol");
DEFINE_int32(ha_normal_rpc_handler_count,
             1,
             "the num of normal rpc workers for HAServiceProtocol");
DEFINE_int32(ha_slow_rpc_handler_count,
             1,
             "the num of slow rpc workers for HAServiceProtocol");
DEFINE_int32(ha_veryslow_rpc_handler_count,
             1,
             "the num of very slow rpc workers for HAServiceProtocol");
DEFINE_int32(ha_rpc_network_thread_count,
             1,
             "the number of rpc threads for HAServiceProtocol");
DEFINE_int32(ha_rpc_tcp_send_buffer_size, 32 * 1024, "tcp send buffer size");
DEFINE_int32(ha_rpc_tcp_recv_buffer_size, 32 * 1024, "tcp send buffer size");
DEFINE_int32(ha_rpc_send_buffer_size, 0, "send buffer size");
DEFINE_int32(ha_rpc_recv_buffer_size, 0, "send buffer size");

DEFINE_int32(rpc_retry_count, 1, "the retry count for a failed rpc");
DEFINE_string(rpc_security_authentication,
              "simple",
              "the authentication method, [simple, kerberos, token]");
DEFINE_validator(rpc_security_authentication, &ValidateAuthentication);

DEFINE_bool(check_untrusted_ip_access,
            false,
            "check the RPC request from untrusted ip");
DEFINE_bool(log_untrusted_ip_access,
            false,
            "log the RPC request from untrusted ip");
DEFINE_bool(forbid_untrusted_ip_access,
            false,
            "forbid the RPC request from untrusted ip");
DEFINE_string(trusted_ip_file,
              "/opt/tiger/dancenn_deploy/conf_common/trusted_ip_list.txt",
              "file to config trusted ip");

// HTTP related
DEFINE_int32(http_port, 5070, "the http port for service");
DEFINE_int32(http_client_worker_count, 5, "the num of workers for http client");
DEFINE_int32(http_handler_count, 5, "the num of workers for http server");
DEFINE_int32(http_network_thread_count,
             5,
             "the number of network threads for http server");
DEFINE_int32(http_tcp_send_buffer_size,
             32 * 1024,
             "tcp send buffer size for http server");
DEFINE_int32(http_tcp_recv_buffer_size,
             32 * 1024,
             "tcp send buffer size for http server");
DEFINE_int32(http_send_buffer_size, 0, "send buffer size for http server");
DEFINE_int32(http_recv_buffer_size, 0, "send buffer size for http server");
DEFINE_int32(view_lease_limit_when_no_condition,
             1000,
             "Reject /view/lease when no condition and too many lease");
DEFINE_bool(allow_list_many_lease, false, "allow list many lease or not");

DEFINE_int32(runtime_check_interval_ms,
             5 * 1000,
             "runtime monitor check interval in millisecond");

// Datanode Manager related
DEFINE_uint32(max_datanodes_num, 1000000, "max number of datanodes");
DEFINE_int32(datanode_check_interval_sec,
             30,
             "Time interval for check datanode status, in seconds");
DEFINE_int32(datanode_keep_alive_timeout_sec,
             30 * 60,
             "Datanode keep alive timeout, in seconds");
DEFINE_int32(datanode_tolerate_interval_misses_sec,
             60,
             "Datanode miss tolerable within the interval, in seconds");
DEFINE_int32(datanode_stale_interval_ms,
             10 * 60 * 1000,
             "The default value of the time interval "
             "for marking datanodes as stale");
DEFINE_int32(datanode_dying_interval_ms,
             10 * 1000,
             "The default value of the time interval "
             "for marking datanodes as dying. Dying DNs are ignored when "
             "choose target unless all DNs are dying.");
DEFINE_bool(avoid_stale_datanode_for_write,
            true,
            "Do not write to stale datanode");
DEFINE_int32(choose_target_context_refresh_interval_ms,
             0,
             "The time interval to refresh per-cpu choose target context");
DEFINE_int32(datanode_manager_thread_num,
             2,
             "DatanodeManager thread pool size");
DEFINE_int32(datanode_stl_block_index_slice_num,
             256,
             "STL block index's slice number");
DEFINE_int32(datanode_ordered_block_index_compact_threshold,
             20000,
             "Threshold to control when to compact block index");
DEFINE_double(datanode_unordered_block_index_load_factor,
              0.66,
              "Datanode unordered block index load factor");
DEFINE_uint32(datanode_unordered_block_index_init_capacity,
              4096,
              "Datanode unordered block index init capacity");
DEFINE_uint32(datanode_max_decommission_per_round,
              1000000,
              "max decommission block per round");
DEFINE_uint32(datanode_max_block_number_in_decommission,
              20000000,
              "max number of block in replication list for decommission");

DEFINE_string(block_index_policy, "stl", "[ordered, unordered, stl]");
DEFINE_string(block_placement_policy,
              "default",
              "[default, multi-dc, nodezone, cfs-default, cfs-multi-dc]");
DEFINE_bool(expand_replica, true, "expand the replica or not in multiple DC");
DEFINE_string(blacklist_dc, "", "config black dc, one of them (LF,HL,LQ)");
DEFINE_string(majority_dc, "", "config majority dc, one of them (LF,HL,LQ)");
DEFINE_string(default_distributed_dc,
              "HL,LF",
              "Replace dc to be HL,LF when dc is empty");
DEFINE_string(dc_topology_file, "", "file to config dc topology");
DEFINE_uint32(dc_max_distance, 5, "max distance between two dc");
DEFINE_validator(block_placement_policy, &ValidateBPPolicy);
DEFINE_bool(block_placement_local_dn_first, true, "local dn first");
DEFINE_bool(datanode_manager_hide_remote_datanode,
            true,
            "Hide datanode in remote data center when getting block locations");
DEFINE_bool(datanode_manager_enable_replica_pipeline,
            true,
            "Construct pipeline if there is no local datanode");
DEFINE_bool(datanode_manager_enable_decommission_transfer_blocks,
            true,
            "Transfer blocks to other DNs when the DN is decommissioned");
DEFINE_bool(block_manager_enable_transfer_blocks,
            true,
            "Enable to transfer block");
DEFINE_bool(block_manager_allow_transfer_persisted_blocks,
            true,
            "Allow transfer persisted blocks to other DNs.");
DEFINE_bool(ibr_deleted_replica_disable_cached_block_info, true, "");

DEFINE_bool(avoid_stale_datanode_for_read,
            true,
            "Avoid reading from stale datanode");
DEFINE_string(network_location_file,
              "/opt/tiger/dancenn_deploy/conf_common/network_location_v2.conf",
              "file to config ip mapped to data center");
DEFINE_bool(rack_aware, true, "using multi-rack placement");
DEFINE_uint32(rack_mask, 6, "ip mask fo rack aware");
DEFINE_bool(read_policy_enable, true, "Enable read policy");
DEFINE_bool(random_node,
            false,
            "The placement strategy ignores local affinity and randomly "
            "selects datanode.");
DEFINE_bool(enable_location_tag,
            true,
            "using location tag instead of nn file config to decide "
            "DN/Client location");
DEFINE_bool(enable_location_tag_by_rack_aware, false, "for back compatibility");
DEFINE_uint32(storage_policy_default_id,
              7,
              "Default storage policy ID (refer to StoragePolicyId)");
DEFINE_uint32(replica_policy_default_id,
              0,
              "Default replica policy ID (0/1/2)");
DEFINE_bool(enable_add_replica_is_cached,
            true,
            "Add isCached for LocatedBlockProto");

// Block Map related
DEFINE_bool(load_local_block_to_blockmap,
            true,
            "Do not disable this flag if enable safemode, e.g., vecompass");
DEFINE_bool(load_persisted_block_to_blockmap,
            false,
            "Do not load persisted blocks into memory to reduce memory usage");
DEFINE_int32(blockmap_num_slice, 4096, "Number of slice for block map");
DEFINE_int32(blockmap_num_bucket_each_slice,
             1024,
             "Number of bucket for each block map slice");
// Deprecated after 20241231
DEFINE_int32(blockmap_invalidate_limit,
             3000,
    "[DEPRECATED] Number of block to invalidate in one heartbeat response");
DEFINE_int32(blockmap_invalidate_limit_per_slice,
             500000,
             "Limit of invalidate block in per blockmap slice");
DEFINE_int32(blockmap_invalidate_limit_per_dn,
             3000,
             "Limit of invalidate block in per DN"
             " = "
             "Number of block to invalidate in one heartbeat response");
DEFINE_int32(blockmap_truncatable_limit_per_slice,
             50000,
             "Limit of truncatable block in per blockmap slice");
DEFINE_int32(blockmap_truncatable_limit_per_dn,
             3000,
             "Limit of truncatable block in per DN"
             " = "
             "Number of block to truncatable in one heartbeat response");
DEFINE_int32(blockmap_sealed_limit_per_slice,
             50000,
             "Limit of sealed block in per blockmap slice");
DEFINE_int32(blockmap_replication_work_multiplier,
             10000,
             "Multiplier for blocks replication work per round");
DEFINE_double(blockmap_invalidate_work_pct,
              0.32,
              "The percentage of blocks invalidate work per round");
DEFINE_int32(blockmap_max_replication_streams,
             10000,
             "The max num of outgoing replication streams per round");
DEFINE_int32(blockmap_max_replication_streams_hard_limit,
             10000,
             "The max hard limit num of outgoing"
             " replication streams per round");
DEFINE_uint64(blockmap_max_replication_replication_bytes,
             256 * 3 * 1024 * 1024L,
             "The max bytes to replication for each dn");
DEFINE_int32(
    blockmap_max_replication_streams_decommission,
    10000,
    "The max num of outgoing replication streams per round for decommission");
DEFINE_double(blockmap_replication_chose_loocal_dc_pct,
              0.95,
              "The percentage of block replication chose local dc");
DEFINE_int64(blockmap_replication_cross_dc_limit_GBps,
             64,
             "Available Cross DC traffic for block repair (GBps)");

DEFINE_uint64(
    replication_grace_time_ms,
    5 * 60 * 1000,
    "ref: https://bytedance.larkoffice.com/docx/NJ6fdMzTioLXYOxZkf1c1ap0nYd");
DEFINE_uint64(
    replication_grace_last_block_num,
    5,
    "ref: https://bytedance.larkoffice.com/docx/NJ6fdMzTioLXYOxZkf1c1ap0nYd");

DEFINE_int64(decommission_cross_dc_limit_GBps,
             40,
             "Available Cross DC traffic for decommission (GBps)");
DEFINE_int32(blockmap_num_postponed_blocks_rescan,
             10000,
             "Number of postponed block to process per round");
DEFINE_bool(blockmap_auto_decommmission,
            false,
            "auto move block from decommission dn");
DEFINE_bool(misinvalidated_block_enable_record,
            true,
            "should record misinvalidated block, default: true");
DEFINE_int32(replication_monitor_interval_ms,
             10 * 1000,
             "Interval of replication monitor execution in millisecond");
DEFINE_int32(invalidate_block_monitor_interval_ms,
             10 * 1000,
             "Interval of invalidate block monitor execution in millisecond");
DEFINE_int32(sealed_block_monitor_interval_ms,
             1 * 1000,
             "Interval of sealed block monitor execution in millisecond");
DEFINE_int32(truncatable_block_monitor_interval_ms,
             1 * 1000,
             "Interval of truncatable block monitor execution in millisecond");
DEFINE_int32(misinvalidated_block_monitor_interval_ms,
             60 * 1000,
             "Interval of misinvalidated blk monitor execution in millisecond");
DEFINE_int32(misinvalidated_block_queue_item_timeout_sec,
             600,
             "Timeout for queue item in second");
DEFINE_bool(blockmap_check_enough_racks,
            true,
            "Check if a block spans multiple racks");
DEFINE_int32(scan_one_block_map_slice_interval_ms,
             1000,
             "Stop x milliseconds after scan one block map slice");
DEFINE_int32(acquire_block_map_slice_lock_slow_threshold_ms,
             3,
             "0 is not enabled");

// Filesystem related
DEFINE_int64(filesystem_id, 0, "filesystem id");

// Namespace related
DEFINE_string(namespace_meta_storage_path,
              "/opt/nndata/meta_storage",
              "namenode namespace meta storage path");
DEFINE_int64(namespace_id, 0, "namespace id");
DEFINE_int32(namespace_type, 0, "local / tos managed / tos raw / no tos");
DEFINE_validator(namespace_type, &ValidateNamespaceType);

DEFINE_bool(readonly_mode_on, false, "Enable write operation or not");

// IAM related.
DEFINE_string(cfs_service_ak, "", "cfs service sk.");
DEFINE_string(cfs_service_sk, "", "cfs service ak.");
DEFINE_string(cfs_service_region, "cn-north-1", "cfs service region.");
DEFINE_string(iam_top_url, "volcengineapi-boe.byted.org", "IAM url");
DEFINE_int32(iam_refresh_interval_secs,
             10 * 60,
             "Duration of ak/sk/token for client is 6h, then we have 5h50min "
             "to handle if fetch ak/sk/token failed");
DEFINE_int32(iam_refresh_interval_secs_on_error,
             3,
             "refresh interval on error case.");
DEFINE_int32(internal_use_token_duration_secs, 43200, "");
DEFINE_int32(external_use_token_duration_secs,
             6 * 60 * 60,
             "The minimum duration of assume role is 6h");
DEFINE_bool(use_fixed_ak, true, "use given fixed ak/sk or fetch from top.");
DEFINE_string(iam_account_id, "**********", "account id for visiting tos.");
DEFINE_string(assume_role_name, "ServiceRoleForCFS", "");

// Deprecated!!!!!!!!!!!!!!!!!! DO NOT USE!!!!!!!!!!
DEFINE_string(extra_iam_resouces, "e.g. [trn:tos:::bucket-1/2046/block/*]", "");

DEFINE_bool(ufs_auth_enabled, false, "Use new ufs auth configs to replace use_fixed_ak, tos_access_key_id, tos_secret_access_key, iam_account_id, assume_role_name");
DEFINE_string(ufs_auth_policy, "fixed", "Options: fixed | role");
static bool Validate_ufs_auth_policy(const char* /*flag*/, const std::string& policy) {
  return policy == "fixed" || policy == "role";
}
DEFINE_validator(ufs_auth_policy, &Validate_ufs_auth_policy);

DEFINE_string(ufs_auth_fixed_ak, "", "UFS AK");
DEFINE_string(ufs_auth_fixed_sk, "", "UFS SK");
DEFINE_string(ufs_auth_fixed_token, "", "UFS STS Token");
DEFINE_string(ufs_auth_role_info, "", "UFS Auth Role config. Base64 decoded.");
static bool Validate_ufs_auth_role_info(const char* /*flag*/, const std::string& info) {
  if (info.empty()) {
    return true;
  }
  std::string res;
  return dancenn::Base64::Decode(info, &res).IsOK();
}

DEFINE_string(hdfs_namespace_node_addr, "", "hdfs nn addr");
DEFINE_uint32(hdfs_namespace_node_port, 0, "hdfs nn port");
DEFINE_string(hdfs_user, "", "hdfs user");
DEFINE_string(hdfs_prefix, "", "hdfs prefix");
DEFINE_string(hdfs_consul, "", "hdfs consul");
DEFINE_string(hdfs_sec_token, "", "hdfs sec token");
DEFINE_int32(ufs_hdfs_client_retry_cnt, 3, "hdfs client api retry count");

DEFINE_bool(force_check_tos_endpoint,
            true,
            "force check tos endpoint matches in rocksdb.");
DEFINE_string(tos_endpoint, "dummy endpoint", "tos endpoint");
DEFINE_string(tos_bucket, "dummy bucket", "tos bucket");
DEFINE_string(tos_prefix, "dummy prefix", "tos prefix path");
static bool ValidateTosPrefix(const char* /*flag*/, const std::string& prefix) {
  if (!prefix.empty() && (prefix.front() == '/' || prefix.back() != '/')) {
    return false;
  }
  return true;
}
DEFINE_bool(
    tos_virtual_host_style,
    true,
    "Amazon S3 supports both virtual-hosted–style and path-style URL access. "
    "virtual host style: http://<bucket>.<endpoint>/<object_name> "
    "path-style: http://<endpoint>/<bucket>/<object_name>");
DEFINE_validator(tos_prefix, &ValidateTosPrefix);
DEFINE_uint64(tos_suffix_salt, 12345678, "tos suffix salt");
DEFINE_string(tos_access_key_id, "dummy access key id", "tos access key");
DEFINE_string(tos_secret_access_key,
              "dummy secret access key",
              "tos secret key");
DEFINE_string(tos_region, "dummy region", "tos region");
DEFINE_bool(tos_sse_enabled, false, "enable tos sse or not");
DEFINE_bool(tos_ufs_mkdir_create_empty_object,
            true,
            "allow mkdir create empty object in TOS as placeholder");

// https://bytedance.larkoffice.com/wiki/CpAIwWEsOixSoUksq26ckZBBnvb
DEFINE_bool(ufs_sync_mkdir_create_in_ufs,
            true,
            "allow mkdir create entry in UFS immediately");

DEFINE_bool(enable_ufs_sync_in_rename, true, "allow rename sync in UFS");
DEFINE_bool(enable_ufs_sync_in_operation, true, "allow sync in UFS operation");
DEFINE_bool(lifecycle_enable_atime_ttl, false, "enable atime ttl");

DEFINE_bool(ufs_sync_delete_local_dir_ufs_not_exist,
            true,
            "allow delete local dir when dir not exist in UFS");
DEFINE_bool(
    disable_dirpolicy_as_workaround_for_kvcache,
    false,
    "Disable directory policy. Effects: Skips loading the policy table from "
    "meta storage into memory during startup, and skips calling the policy "
    "manager when setting directory policies. Ensure the user understands and "
    "accepts these side effects before setting this flag to true!");

DEFINE_uint64(ufs_mkdir_create_scanner_interval_us, 10 * 1000 * 1000, "");
DEFINE_uint64(ufs_mkdir_create_scanner_qps, 1000, "");

DEFINE_bool(ufs_sync_force_with_ufs, true, "force sync with ufs before fs op");
DEFINE_bool(ufs_sync_force_async_sync_in_background,
            true,
            "sync with ufs should async run in background");
DEFINE_bool(ufs_sync_force_when_parent_not_found,
            true,
            "force sync with ufs when parent in path not found");
DEFINE_uint64(dir_lock_retry_sleep_time_ms,
              1,
              "sleep time ms in ufs sync retry");

DEFINE_bool(ufs_event_manager_enabled,
            false,
            "Whether to enable UFS Event Manager");
DEFINE_uint32(ufs_event_consumer_count, 2, "Count of UFS Event Consumers");
DEFINE_bool(ufs_event_kafka_consumer_enabled,
            true,
            "Whether to enable UFS Event Kafka Consumers. Default `true` for "
            "backward compatibility");
DEFINE_string(ufs_event_consumer_kafka_endpoint,
              "",
              "Kafka endpoint for UFS Event Consumers");
DEFINE_string(ufs_event_consumer_kafka_topic,
              "",
              "Kafka topic for UFS Event Consumers to subscribe");
DEFINE_string(ufs_event_consumer_kafka_group_id,
              "",
              "Kafka consumer group ID of UFS Event Consumers");
DEFINE_uint32(ufs_event_consumer_kafka_consume_timeout_ms,
              1000,
              "Kafka consumption timeout (ms) for UFS Event Consumers");
DEFINE_uint32(ufs_event_consumer_max_batch_size,
              2048,
              "Max batch size for UFS Event Consumers");
DEFINE_uint32(ufs_event_consumer_max_batch_interval_ms,
              2000,
              "Max batch interval (ms) for UFS Event Consumers");
DEFINE_bool(ufs_event_rmq_consumer_enabled,
            false,
            "Whether to enable UFS Event RocketMQ Consumers");
DEFINE_string(ufs_event_rmq_consumer_endpoint,
              "",
              "RocketMQ endpoint for UFS Event RocketMQ Consumers");
DEFINE_string(ufs_event_rmq_consumer_topic,
              "",
              "RocketMQ topic for UFS Event RocketMQ Consumers");
DEFINE_string(ufs_event_rmq_consumer_group_id,
              "",
              "RocketMQ group ID for UFS Event RocketMQ Consumers");
DEFINE_string(ufs_event_rmq_consumer_access_key,
              "",
              "RocketMQ access key for UFS Event RocketMQ Consumers");
DEFINE_string(ufs_event_rmq_consumer_secret_key,
              "",
              "RocketMQ secret key for UFS Event RocketMQ Consumers");
DEFINE_uint32(ufs_event_handler_count, 8, "Count of UFS Event Handlers");
DEFINE_uint32(ufs_event_handler_add_task_retry_interval_ms,
              10,
              "Retry interval (ms) of adding a task to UFS Event Handlers");
DEFINE_uint32(ufs_event_handler_sync_retry_time,
              5,
              "Retry time when UFS Event Handler failed to sync");
DEFINE_uint32(ufs_event_handler_sync_retry_interval_ms,
              1000,
              "Retry interval (ms) when UFS Event Handler failed to sync");
DEFINE_bool(tos_event_key_prefix_blacklist_enabled,
            false,
            "Enable TOS Event key prefix blacklist");
// Multiple key prefixes are separated by comma (',')
DEFINE_string(tos_event_key_prefix_blacklist,
              "",
              "TOS Event key prefix blacklist");

// TODO@zhaoliyang.673: Change the default value to true in the next release
// Currently we only turn it on for fortran NN
DEFINE_bool(namespace_read_full_detail_blocks,
            false,
            "If to switch on optimized NameSpace::CreateLocatedBlocks");

// Block Recycler related.
DEFINE_int32(block_pufs_info_monitor_interval_ms,
             500,
             "Scan deprecated block pufs infos interval. "
             "Please be careful when reducing it, "
             "because scanning too frequently will cause repeatedly deleting.");
DEFINE_int32(delete_tos_block_interval_ms, 1000, "Delete tos block interval.");
static bool ValidateGEZero(const char* flag, int32_t t) {
  return t >= 0;
}
DEFINE_int32(ongoing_op_del_depring_blks_max_size,
             10,
             "max size of ongoing OpDelDepringBlks");
DEFINE_validator(ongoing_op_del_depring_blks_max_size, &ValidateGEZero);
DEFINE_int32(del_depring_blks_batch_size,
             4096,
             "max block size of one OpDelDepringBlks");
DEFINE_int32(del_depring_blks_recycle_times,
             10,
             "call Recycle() time when trigger");
DEFINE_validator(del_depring_blks_batch_size, &ValidateGEZero);
DEFINE_int32(
    op_del_depred_blks_batch_size,
    500,
    "max size of one OpDelDepredBlks");  // each block may have its own crc file
                                         // block, so max delete batch size:
                                         // 1000/2 = 500
DEFINE_validator(op_del_depred_blks_batch_size, &ValidateGEZero);
DEFINE_int32(del_depred_blks_recycle_times,
             10,
             "call Recycle() time when trigger");
DEFINE_int32(wait_age_before_seek_depred_blks_iter_to_first,
             20,
             "wait some time before seek iterator of deprecated blks to first");
DEFINE_int32(ongoing_op_del_depred_blks_max_size,
             10,
             "max size of ongoing OpDelDepredBlks");
DEFINE_validator(ongoing_op_del_depred_blks_max_size, &ValidateGEZero);
DEFINE_int32(del_depred_blks_batch_size,
             4096,
             "max block size of one OpDelDepringBlks");
DEFINE_validator(del_depred_blks_batch_size, &ValidateGEZero);
DEFINE_int32(
    not_full_op_del_depred_blks_mature_age,
    10,
    "Wait op become mature if it is not full before sending it to edit logs");
DEFINE_int32(cached_invalidate_pufs_cmd_max_size,
             20,
             "max size of cached InvalidatePufsCommandProto");
DEFINE_validator(cached_invalidate_pufs_cmd_max_size, &ValidateGEZero);
DEFINE_int32(invalidate_pufs_cmd_batch_size,
             500,
             "max block size of one InvalidatePufsCommandProto");
DEFINE_validator(invalidate_pufs_cmd_batch_size, &ValidateGEZero);

// Meta storage checkpoint
DEFINE_string(namespace_meta_storage_ckpt_path,
              "/data01/dancenn_data/rocksdb_ckpt",
              "Namespace meta storage checkpoint path");
DEFINE_int32(meta_storage_checkpoint_period_sec,
             3 * 3600,
             "Namespace meta storage checkpoint period");
DEFINE_int32(meta_storage_checkpoint_extra_retain_num,
             1,
             "Namespace meta storage extra checkpoint to retain num");

DEFINE_uint32(dfs_summary_min_depth,
              0,
              "The minimum depth allowed to use getContentSummary(0='/')");
DEFINE_int32(dfs_ls_limit, 500, "dir list max count every time");
DEFINE_bool(client_block_size_support, false, "support client set block size");
DEFINE_uint64(dfs_block_size,
              128 * 1024 * 1024,
              "block size of file, default 128MB in HDFS");
DEFINE_bool(client_replication_support,
            false,
            "support client set replication num");
DEFINE_int32(dfs_replication, 3, "block replication num");
DEFINE_uint32(dfs_replication_min, 1, "the minimal replication of block");
DEFINE_validator(dfs_replication_min, &ValidateDfsReplicationMin);
DEFINE_uint32(dfs_replication_max, 3, "the max replication of block");
DEFINE_validator(dfs_replication_max, &ValidateDfsReplicationMax);
DEFINE_string(dfs_user_home_dir_prefix, "/user", "user home prefix");
DEFINE_int32(dfs_bytes_per_checksum,
             512,
             "The number of bytes per checksum. "
             "Must not be larger than dfs.stream-buffer-size");
DEFINE_int32(dfs_stream_buffer_size,
             4096,
             "The size of buffer to stream files.");
DEFINE_int32(dfs_client_write_packet_size,
             64 * 1024,
             "Packet size for clients to write");
DEFINE_int32(io_file_buffer_size,
             4096,
             "The size of buffer for use in sequence files.");
DEFINE_bool(dfs_encrypt_data_transfer,
            false,
            "Whether or not actual block data that is read/written "
            "from/to HDFS should be encrypted on the wire.");
DEFINE_uint64(fs_trash_interval,
              0,
              "Number of minutes after which the checkpoint gets deleted. "
              "If zero, the trash feature is disabled.");
DEFINE_string(dfs_checksum_type, "CRC32C", "fs checksum type");
DEFINE_uint32(get_additional_datanode_max_excludes,
              8,
              "GetAdditionalDatanode max excludes, default: 8");
DEFINE_bool(dfs_symlinks_enabled, false, "namenode symlinks enabled");
DEFINE_int32(dfs_get_file_info_with_block_locations_max_block_num,
             1,
             "Return block locations to client only if inode blocks count is "
             "no more than this number");
DEFINE_bool(retry_cache_enabled, true, "whether enable retry cache");
DEFINE_int32(retry_cache_expiration_time_ms,
             60 * 1000 * 8,
             "Expiration time in milliseconds of the retry cache");
DEFINE_int32(retry_cache_slice_count, 4096, "The number of retry cache slices");
DEFINE_int32(retry_cache_size,
             4000,
             "The size of each retry cache slice(number of entries)");

DEFINE_int32(bg_deletion_process_pending_delete_interval_sec,
             60,
             "The interval to run background deletion");
DEFINE_uint32(bg_deletion_batch_remove_inode_threshold,
              10000,
              "Trigger batch-remove when buffered inodes are too many");
DEFINE_uint32(bg_deletion_batch_remove_block_threshold,
              10000,
              "Trigger batch-remove when buffered blocks are too many");
DEFINE_uint32(bg_deletion_batch_remove_sleep_ms,
              500,
              "Sleep N milliseconds after a batch-remove");
DEFINE_bool(enable_snapshot_feature, false, "Whether enable snapshot feature, "
            "before reopening the feature, the old snapshot related data should "
            "be cleaned.");
DEFINE_int32(snapshot_gc_interval_in_sec,
             60,
             "The interval to run background snapshot gc");
DEFINE_int32(snapshot_gc_deletion_num_in_batch,
             10000,
             "The num to delete in one batch for snapshot gc, used to limit "
             "memory usage");
DEFINE_int32(snapshot_full_gc_frequency,
             100,
             "Do snapshot full gc once every x gc times");
DEFINE_int32(
    snapshot_reference_num_threshold,
    10,
    "Normally snapshot_reference_num should be less than the threshold");

DEFINE_int32(bg_auto_compact_interval_in_min,
             60,
             "The interval to compact all to one level, default: 60 minutes");
DEFINE_int32(bg_auto_compact_all_deletion_threshold,
             0,
             "The deletion threshold for compact all to one level");
DEFINE_int32(bg_auto_compact_all_forbid,
             1,
             "Forbid compact all, when set this equal 1, "
             "even in standby, also no compact all, default: 1");
DEFINE_int32(bg_auto_compact_all_enable, 0, "Enable compact all, default: 0");
DEFINE_int32(bg_auto_compact_deletion_enable,
             0,
             "Enable compact deletion, default: 0");

DEFINE_bool(append_reuse_last_block,
            false,
            "Append allow reuse last block, default: true");
DEFINE_bool(complete_rpc_abandon_last_empty_block,
            true,
            "Complete will abandon last block if it is empty, default: false");
DEFINE_bool(complete_rpc_check_replica_in_callback,
            false,
            "Complete will call CheckReplica in callback, default: false");
DEFINE_bool(complete_rpc_trigger_file_upload_in_callback,
            true,
            "trigger file upload after complete");
DEFINE_bool(
    complete_rpc_use_very_slow,
    true,
    "Complete RPC will use kVerySlow worker threadpool, default: false");
DEFINE_uint32(merge_block_max_candidate_size,
              16777216,
              "Do not merge block more than this size");
DEFINE_uint32(merge_block_max_candidate_count,
              4096,
              "Force merge block with more than this count");
DEFINE_uint32(merge_block_reserve_block_count,
              10,
              "Newly added block might change state (persist or evict), "
              "To reduce the risk of bug, do not merge last 10 blocks.");
DEFINE_bool(merge_block_support_acc_persisted_blocks,
            true,
            "Merge persisted blocks in file");
DEFINE_bool(merge_block_support_acc_non_persisted_blocks,
            true,
            "Merge non persisted blocks in file");
// TODO(zhuangsiyu)
DEFINE_bool(merge_block_listener_enable,
            false,
            "Enable auto merging task, default: false; NOT tested, NEVER use");
DEFINE_uint32(merge_block_task_max_ongoing,
              10,
              "Max ongoing task(file) for auto merging scanner listener");

DEFINE_bool(dfs_meta_storage_inode_key_v2,
            true,
            "Whether to append \0 after inode name part in rocksdb inode key");
DEFINE_int32(dfs_meta_storage_max_write_batch_size,
             256,
             "Max write item size per batch, default: 256");
DEFINE_int32(dfs_meta_storage_pending_commit_queue_size,
             65536,
             "The pending commit queue size of meta storage, default: 65536");

DEFINE_bool(dfs_meta_storage_rocksdb_perf_enabled,
            false,
            "Enable rocksdb perf context and io stats context, default: false");
DEFINE_int32(dfs_meta_storage_rocksdb_perf_threshold_ms,
             3,
             "Rocksdb perf context and io stats context threshold, default: 3");
DEFINE_bool(dfs_meta_storage_rocksdb_perf_scr_enabled,
            false,
            "Enable rocksdb perf context and io stats context on SCR CF only");
DEFINE_int32(dfs_meta_storage_rocksdb_perf_scr_level,
            3,
            "rocksdb::PerfLevel 0=kUninitialized, 1=kDisable, 2=EnableCount, "
            "3=EnableTimeExceptForMutex, 4=kEnableTime");
static bool ValidateRocksDBPerfLevel(const char* flag, int32_t v) {
  return ((rocksdb::PerfLevel)v == rocksdb::PerfLevel::kDisable) ||
         ((rocksdb::PerfLevel)v == rocksdb::PerfLevel::kEnableCount) ||
         ((rocksdb::PerfLevel)v == rocksdb::PerfLevel::kEnableTimeExceptForMutex) ||
         ((rocksdb::PerfLevel)v == rocksdb::PerfLevel::kEnableTime);
}
DEFINE_validator(dfs_meta_storage_rocksdb_perf_scr_level, &ValidateRocksDBPerfLevel);
DEFINE_int32(dfs_meta_storage_rocksdb_block_cache_capacity_mb,
             200,
             "Create a new cache with a fixed size capacity, default: 200M");
DEFINE_bool(dfs_meta_storage_rocksdb_use_unified_block_cache,
            false,
            "All CFs want larger block cache will share a unified block cache, "
            "while others will use default 8MB exclusive cache");
DEFINE_bool(dfs_meta_storage_rocksdb_block_cache_for_index_filters,
            false,
            "rocksdb::cache_index_and_filter_blocks, we meet performance "
            "degradation when set to true");
DEFINE_double(dfs_meta_storage_rocksdb_block_cache_lru_priority_ratio,
              0.0,
              "rocksdb::cache_index_and_filter_blocks_with_high_priority, will "
              "be set to true and use separated LRU for index and filter, use "
              "with dfs_meta_storage_rocksdb_block_cache_for_index_filters");
DEFINE_bool(dfs_meta_storage_rocksdb_block_cache_pin_l0,
            false,
            "rocksdb::pin_l0_filter_and_index_blocks_in_cache, use with "
            "dfs_meta_storage_rocksdb_block_cache_for_index_filters");
DEFINE_int32(dfs_meta_storage_rocksdb_block_cache_sim_capacity_mb,
             0,
             "RocksDB Simulated Cache Capacity, default: 0 (disable)");
DEFINE_int32(dfs_meta_storage_rocksdb_block_cache_sim_shard_bits,
             0,
             "RocksDB Simulated Cache Shard Bits, default: 0 (disable)");
DEFINE_bool(dfs_meta_storage_bloom_filter_use_block_based_builder_enabled,
            false,
            "Rocksdb bloom filter use block based builder, default: false");

DEFINE_int32(dfs_meta_storage_max_batch_write_item_num,
             0,
             "Maximum items impose on a journal write to achieve grouping");
DEFINE_int64(dfs_meta_storage_rocksdb_max_bytes_for_level_base,
             256 * 1024 * 1024,
             "Maximum bytes for base level, default: 256MB");
DEFINE_int32(dfs_meta_storage_rocksdb_max_write_buffer_number,
             64,
             "How many memtables at most for rocksdb");
DEFINE_int32(dfs_meta_storage_rocksdb_write_buffer_size_mb,
             64,
             "The maximum size in MB for a memtable");
DEFINE_int32(dfs_meta_storage_rocksdb_level0_stop_writes_trigger,
             128,
             "The maximum number of level 0 files to trigger stopping writes");
DEFINE_int32(dfs_meta_storage_rocksdb_min_write_buffer_number_to_merge,
             1,
             "How many memtables to merge when flushing to disk");
DEFINE_int32(dfs_meta_storage_rocksdb_parallelism,
             32,
             "The maximum of parallelism for rocksdb background workers");
DEFINE_int32(dfs_meta_storage_rocksdb_max_background_flushes,
             16,
             "The maximum flush threads");
DEFINE_int32(dfs_meta_storage_rocksdb_max_background_compactions,
             16,
             "The maximum compaction threads");
DEFINE_int32(dfs_meta_storage_rocksdb_max_background_jobs,
             32,
             "The maximum number of background jobs");
DEFINE_int32(dfs_meta_storage_rocksdb_max_total_wal_size_mb,
             128,
             "The maximum size in MB for wal logs");
DEFINE_int32(dfs_meta_storage_rocksdb_compression_type,
             0,
             "The Compression Type, 0 means kNoCompression");
DEFINE_int32(dfs_meta_storage_rocksdb_level0_file_num_compaction_triger,
             1,
             "The Level0 file num compaction trigger");
DEFINE_int32(dfs_meta_storage_rocksdb_compression_level,
             32767,
             "The Compression Level, default: 32767");
DEFINE_int32(dfs_meta_storage_rocksdb_num_levels,
             7,
             "The Max Rocksdb Levels, default: 7");
DEFINE_bool(dfs_meta_storage_rocksdb_inplace_update,
            false,
            "The Rocksdb use inplace-update-memtable, default: false");

DEFINE_bool(dfs_meta_storage_rocksdb_use_direct_reads,
            false,
            "Enable rocksdb direct I/O mode for read/write");
DEFINE_bool(dfs_meta_storage_rocksdb_use_direct_io_for_flush_and_compaction,
            false,
            "Enable rocksdb direct I/O mode for flush and compactions");
DEFINE_bool(dfs_meta_storage_rocksdb_write_sync,
            false,
            "Enable rocksdb sync for write");

DEFINE_int32(dfs_meta_storage_parent_map_mask,
             10,
             "The mask of number of parent maps");
DEFINE_int32(dfs_meta_storage_parent_map_bucket_count,
             1000000,
             "The number of buckets for each parent map");

DEFINE_int32(dfs_meta_storage_single_writer_spin_wait_times,
             5 * 1024,
             "The times of single writer spin wait after pending");
DEFINE_uint64(dfs_meta_storage_update_scr_batch_size,
              50000,
              "Batch size of update StorageClassReport per WriteTask");
DEFINE_bool(dfs_meta_storage_verify_before_commit,
            true,
            "Verify old key-value before commit db");

DEFINE_int32(dfs_load_block_package_size,
             10000,
             "Number of blocks which will be packed to add to block map");
DEFINE_int32(dfs_load_block_thread_count,
             10,
             "Number of thread to load block when namenode boots");
DEFINE_int32(dfs_load_from_metastorage_threadpool_count,
             10,
             "Number of thread pool to scan meta storage");
DEFINE_int32(dfs_load_from_metastorage_thread_count_per_pool,
             1,
             "Number of thread in the thread pool");
DEFINE_bool(dfs_support_append,
            true,
            "permission of append to an existing file");
DEFINE_int32(dfs_replication_pending_timeout_ms,
             5 * 60000,
             "Replication pending timeout in milliseconds");
DEFINE_int32(dfs_meta_scan_interval_sec,
             60,
             "Interval in seconds to scan meta storage.");
DEFINE_bool(dfs_meta_scan_use_bfs,
            true,
            "Use BFS for meta scanner");
DEFINE_int32(dfs_quota_collect_interval_sec,
             10,
             "Interval in seconds to compute quota by scan meta storage");
DEFINE_uint32(dfs_meta_scanner_v2_scan_worker_num,
              4,
              "Scan worker number for meta scanner v2");
DEFINE_uint32(dfs_meta_scanner_v2_worker_num,
              64,
              "Worker number for meta scanner v2");
DEFINE_uint32(meta_scanner_worker_max_num_pending_tasks,
              1000000,
              "Max ongoing task");
DEFINE_int32(dfs_meta_scan_max_iter_count_per_sec,
             15000,
             "Max iter count per second to scan meta storage.");

DEFINE_bool(dfs_meta_scan_enable,
            true,
            "enable meta scanner");

DEFINE_bool(ms_util_compare_inode_skip_check_mtime,
            false,
            "disable compare mtime of two inodes");
DEFINE_bool(ms_util_compare_inode_skip_check_atime,
            false,
            "disable compare atime of two inodes");
DEFINE_bool(ms_util_compare_inode_skip_check_perm,
            false,
            "disable compare permission of two inodes");
DEFINE_bool(ms_util_compare_inode_skip_check_acl,
            false,
            "disable compare acl of two inodes");
DEFINE_bool(ms_util_compare_inode_skip_check_xattr,
            false,
            "disable compare xattr of two inodes");

// if don't change dfs_rwlocks_static_depth dynamically,
// only if you make better static rwlock table
DEFINE_uint32(dfs_rwlocks_static_depth, 2, "Max rwlocks static depth: 2");

// filesystem limit related
DEFINE_uint64(dfs_namenode_fs_limits_min_block_size,
              1024 * 1024,
              "Minimum block size in bytes, default 1MB.");
DEFINE_uint64(dfs_namenode_fs_limits_max_blocks_per_file,
              1024 * 1024,
              "Maximum number of blocks per file.");

DEFINE_int32(dfs_meta_storage_slows,
             16,
             "Number of slow executors, default: 16");
DEFINE_int32(dfs_meta_storage_slows_mailbox,
             409600,
             "Number of slow executors, default: 4096");

// Edit log & HA & Safemode
DEFINE_string(java_zk_classpath, "", "Java classpath for editlog tailer & HA");
DEFINE_string(java_classpath, "", "Java classpath for editlog tailer & HA");
DEFINE_int32(java_heap_size_mb, 128, "Java heap size for editlog tailer & HA");
DEFINE_string(java_error_file,
              "/var/log/tiger/dancenn-logs/dancenn_vm_err.log.%p",
              "The java error file");
DEFINE_string(java_heap_dump_path,
              "/var/log/tiger/dancenn-logs/dancenn.hprof.%p",
              "The java heap dump path");
DEFINE_string(java_gc_log_file,
              "/var/log/tiger/dancenn-logs/dancenn-gc.log.%p",
              "The java gc log file");
DEFINE_int32(tail_period_ms, 3000, "Sleep period between tailing editlog");
DEFINE_int32(apply_thread_count, 8, "the num of apply thread");
DEFINE_bool(in_progress, true, "whether to tail the in_progress editlog");
DEFINE_bool(safemode_check_block_cnt,
            true,
            "whether to check block cnt in safemode");
DEFINE_bool(safemode_strict_check_block_count,
            true,
            "safemode strict check block count >= 0, if block count < 0 NN "
            "will coredump");
// TODO(ruanjunbin): https://bits.bytedance.net/meego/cfs/issue/detail/2123614
// In CloudFS, most blocks are uploaded to object store.
// safe block percentage = num of unpersisted blocks / available unpersisted
// blocks Origin value is 0.999.
DEFINE_double(safemode_threshold_pct,
              0.999,
              "the percentage of blocks should satisfy "
              "the minimal replication requirement before leave safemode");
DEFINE_int32(checkpoint_period_ms,
             3600 * 1000,
             "Sleep period between checkpoint");

DEFINE_int32(safemode_extension_ms,
             1000,
             "waiting time in safe mode after threshold is reached.");
DEFINE_bool(safemode_check_dn_cnt, true, "whether to check dn cnt in safemode");
DEFINE_int32(safemode_min_datanodes,
             3,
             "minimum number of datanodes alive before leave safe mode");
DEFINE_bool(safemode_check_safe_dn_cnt,
            true,
            "whether to check content stale dn cnt in safemode");
DEFINE_double(safemode_safe_dn_threshold_pct,
              0.99,
              "the percentage of dn should satisfy the minimal "
              "non-content-stale before leave safemode, allow one dn failure. "
              "(total-unsafe)/(total-1)>=threshold");
DEFINE_int64(safemode_safe_check_interval_ms,
             1000,
             "check interval of safe block/dn count");
DEFINE_double(dfs_namenode_replqueue_threshold_pct,
              0.999,
              "percent of blocks needed before populating replication queues");
DEFINE_bool(namenode_ha_enabled, true, "namenode ha enabled");
DEFINE_bool(namenode_ha_use_async_thread,
            true,
            "use async thread to do SetState");
DEFINE_bool(dfs_ha_allow_stale_reads,
            false,
            "allow read operations while NN in standby mode");
DEFINE_bool(ha_fast_return_when_switch_to_standby,
            true,
            "return after bk client stop");
DEFINE_bool(ha_async_run_switch_task,
            true,
            "async do some heavy bg task when has switch(i.e. cleanup)");
DEFINE_double(edit_log_autoroll_txns_threshold,
              2000000,
              "transaction gaps to auto roll edit log");
DEFINE_uint64(
    edit_log_autoroll_txns_min_threshold,
    20000,
    "transaction gaps to refuse auto roll edit log when reach time duration");
DEFINE_uint64(edit_log_autoroll_period_threshold_ms,
              10 * 60 * 1000,
              "time duration of auto roll edit log");
DEFINE_uint64(edit_log_force_autoroll_period_threshold_ms,
              24 * 60 * 60 * 1000,
              "time duration of force auto roll edit log");
DEFINE_uint64(edit_log_autoroll_failover_threshold_ms,
              10 * 1000,
              "time duration of synchronization timeout to auto roll edit log");
DEFINE_uint64(edit_log_autoroll_check_interval_ms,
              5 * 1000,
              "how ofter active nn will check if needs to roll its edit log");
DEFINE_int32(edit_log_sync_min_wait_time_us,
             200,
             "edit log background syncer min wait time, default: 200us");
DEFINE_int32(edit_log_commit_max_wait_time_us,
             100,
             "edit log background commit max wait time, default: 100us");
DEFINE_int32(
    edit_log_commit_max_batch_size,
    0,
    "edit log background committer max batch size, default: 0, mean no limit");
DEFINE_int32(edit_log_last_sync_stale_interval_ms,
             5 * 1000,
             "When the last sync time exceeds this value, the process is "
             "considered no longer active.");
DEFINE_int32(bookkeeper_write_stream_buffer_size,
             1024,
             "bookkeeper write stream buffer size, default: 1024B");
DEFINE_string(namenode_startup_option, "regular", "namenode startup option");
DEFINE_uint32(edit_log_slow_op_us,
              10 * 1000,
              "microseconds for logging slow edit log ops");
DEFINE_uint32(edit_log_assigner_max_num_pending_tasks,
             1000000,
             "max number of pending tasks in editlog assigner thread, "
             "700000 per 1GB memory consumption, based on experience");
DEFINE_uint32(edit_log_assigner_add_task_retry_sleep_ms,
             10,
             "retry sleep time when hit assigner pending task threshold");
DEFINE_uint32(edit_log_assigner_apply_mode,
              0,
              "0: logical-apply, "
              "1: physical-apply sequentially, "
              "2: physical-apply concurrently");
DEFINE_validator(edit_log_assigner_apply_mode, &ValidateEditLogApplyMode);
DEFINE_bool(edit_log_logical_apply_check_physical_log_enable,
            true,
            "Check physical log fields during logical-apply");
DEFINE_bool(edit_log_physical_apply_check_db_enable,
            true,
            "Compare data from rocksdb during physical-apply sequentially");
DEFINE_uint32(edit_log_applyer_wait_no_pending_sleep_us,
              100,
              "Wait period for checking editlog consumed by applyers");

// LeaseManager related
DEFINE_bool(enable_lease_persistence, true, "Enable lease persistence or not");
DEFINE_bool(force_rebuild_lease_db,
            false,
            "rescan inode table and build lease db");
DEFINE_uint64(lease_expired_soft_limit_ms,
              60 * 1000,
              "lease expired soft limit time, in milliseconds");
DEFINE_uint64(lease_expired_hard_limit_ms,
              60 * 60 * 1000,
              "lease expired hard limit time, in milliseconds");
DEFINE_uint32(lease_slice_size,
              100019,
              "The size of lease slice vector(number of slices)");
DEFINE_uint32(index_slice_size,
              10009,
              "The size of lease index vector(number of slices)");
DEFINE_uint32(lease_speed_up_sec,
              10 * 60,
              "The time of lease going to expire from last_update_time used by "
              "decommission");
DEFINE_uint32(lease_monitor_interval_ms,
              10 * 60 * 1000,
              "lease monitor interval");
DEFINE_bool(enable_recover_lease_no_close_file,
            false,
            "Enable recovering leases without closing files.");

// DirPolicy related
DEFINE_bool(force_rebuild_policy_db,
            false,
            "rescan inode table and build dir policy db");
DEFINE_bool(need_build_policy_db_first_time,
            false,
            "need build policy db for first time run."
            "Defaults to false if ReplicaPolicy has never been used before");

// fsimage related
DEFINE_bool(
    load_from_fsimage,
    false,
    "whether to load meta data from fsimage, only used when first start");
DEFINE_string(fsimage_dir,
              "",
              "fsimage path needed when first start from fsimage");
DEFINE_string(fsimage_file_name,
              "",
              "fsimage file name needed when first start from fsimage");

// placement driver related
DEFINE_int32(namenode_stale_timeout_sec,
             60,
             "Namenode stale timeout, in seconds");
DEFINE_int32(namenode_keep_alive_timeout_sec,
             300,
             "Namenode keep alive timeout, in seconds");

// datanode manager related
DEFINE_string(all_datacenters, "", "all data centers separated by ,");

// access counter related
DEFINE_int64(accesscounter_flush_interval_ms,
             30 * 60 * 1000,
             "The flush interval in milliseconds of access counter");
DEFINE_int32(accesscounter_max_depth,
             4,
             "The max path depth the access counter will consider");
DEFINE_double(accesscounter_learn_factor,
              std::pow(0.5, (30 * 60. / 86400.)),
              "Learn factor of access counter");

// danceproxy related
DEFINE_int32(danceproxy_rpc_port, 65212, "the rpc port for danceproxy");
DEFINE_int32(danceproxy_rpc_backlog, 1024, "the backlog for danceproxy rpc");
DEFINE_int32(danceproxy_rpc_handler_count,
             32,
             "the number of rpc workers for danceproxy");
DEFINE_int32(danceproxy_rpc_network_thread_count,
             0,
             "the number of rpc threads for danceproxy");
DEFINE_int32(danceproxy_rpc_send_buffer_size,
             32 * 1024,
             "tcp send buffer size for danceproxy");
DEFINE_int32(danceproxy_rpc_recv_buffer_size,
             32 * 1024,
             "tcp receive buffer size for danceproxy");
DEFINE_string(danceproxy_rpc_default_client_id,
              "DANCEPROXY-0.0.1",
              "the default client id");
DEFINE_string(danceproxy_rpc_default_user, "root", "the default user");
DEFINE_int32(danceproxy_rpc_max_open_connections_per_user_and_fs,
             16,
             "the maximum number of open connections with per user and fs");
DEFINE_int32(danceproxy_rpc_max_pending_calls_per_user_and_fs,
             100000,
             "the maximum number of ongoing calls with per user and fs");
DEFINE_string(danceproxy_mount_table_zk_quorum,
              "",
              "zookeeper server list used to store mount table and read paths");
DEFINE_string(danceproxy_mount_table_zk_path,
              "",
              "the zk path for mount table");
DEFINE_string(danceproxy_read_only_zk_path,
              "",
              "the zk path for read only paths");
DEFINE_int32(danceproxy_zk_recv_timeout_ms,
             60000,
             "the zk receive timeout in milliseconds");
DEFINE_string(danceproxy_namenodes_configuration_file,
              "",
              "the configuration file of all namenodes");
DEFINE_string(danceproxy_rename_dir_whitelist, "", "the rename dir whitelist");
DEFINE_string(danceproxy_quota_redis_backends,
              "",
              "the redis backends for quota");
DEFINE_int32(danceproxy_quota_redis_timeout_ms,
             60000,
             "redis timeout ins milliseconds for quota");
DEFINE_int32(danceproxy_quota_redis_refresh_interval_ms,
             1000,
             "redis refresh interval for quota");
DEFINE_string(danceproxy_quota_redis_path2team_key,
              "path2team",
              "the map shows the path belongs to which team");
DEFINE_string(danceproxy_quota_redis_team2quota_key,
              "team2quota",
              "the map shows the quota of each team");
DEFINE_string(danceproxy_quota_redis_team2usage_key,
              "teamUsage",
              "the map shows the usage of each team");
DEFINE_string(danceproxy_quota_redis_path2usage_key,
              "pathUsage",
              "the map shows usage of each path");
DEFINE_string(danceproxy_quota_redis_ssdpaths_key,
              "storage",
              "set of ssd paths");
DEFINE_string(danceproxy_quota_redis_blacklist_key,
              "blacklist",
              "blacklist team");
DEFINE_string(danceproxy_quota_redis_whitelist_key,
              "whitelist",
              "whitelist team");
DEFINE_string(danceproxy_quota_redis_switch_key,
              "switch",
              "switch to turn on or off the quota checker");
DEFINE_bool(danceproxy_quota_enabled, true, "whether enable quota feature");
DEFINE_int32(danceproxy_quota_on_start_hour_utc,
             0,
             "when to turn on the quota checker in hour of utc time");
DEFINE_int32(danceproxy_quota_on_end_hour_utc,
             14,
             "when to turn off the quota checker in hour of utc time");
DEFINE_string(danceproxy_quota_pattern_team_mapping_file,
              "",
              "pattern team mapping file");
DEFINE_string(danceproxy_frozen_directory_redis_backends,
              "",
              "the redis backends for frozen directory");
DEFINE_int32(danceproxy_frozen_directory_redis_timeout_ms,
             60000,
             "redis timeout in milliseconds for frozen directory");
DEFINE_int32(danceproxy_frozen_directory_redis_refresh_interval_ms,
             1000,
             "redis refresh interval for frozen directory");
DEFINE_string(danceproxy_frozen_directory_redis_frozen_directories_key,
              "frozenDirectories",
              "the redis key for all frozen directories");
DEFINE_string(danceproxy_frozen_directory_redis_frozen_directory_key,
              "frozenDirectory",
              "the redis key prefix for each frozen directory");
DEFINE_string(danceproxy_frozen_directory_redis_switch_key,
              "frozenDirectorySwitch",
              "the redis key for switch to turn on or off "
              "the frozen directory checker");
DEFINE_bool(danceproxy_frozen_directory_enabled,
            true,
            "whether enable frozen directory feature");
DEFINE_string(danceproxy_storage_policy_ttl_redis_backends,
              "",
              "the redis backends for storage policy ttl");
DEFINE_int32(danceproxy_storage_policy_ttl_redis_timeout_ms,
             60000,
             "redis timeout in milliseconds for storage policy ttl");
DEFINE_int32(danceproxy_storage_policy_ttl_redis_refresh_interval_ms,
             1000,
             "redis refresh interval for storage policy ttl");
DEFINE_string(danceproxy_storage_policy_ttl_redis_key,
              "storage_policy_ttls",
              "the redis key for storage policy ttl");
DEFINE_bool(danceproxy_storage_policy_ttl_enabled,
            true,
            "whether enable storage policy ttl feature");

DEFINE_string(danceproxy_throttle_redis_backends,
              "",
              "the redis backends for throttle");
DEFINE_int32(danceproxy_throttle_redis_timeout_ms,
             60000,
             "redis timeout in milliseconds for throttle");
DEFINE_int32(danceproxy_throttle_redis_refresh_interval_ms,
             1000,
             "redis refresh interval for throttle");
DEFINE_string(danceproxy_throttle_redis_key,
              "throttlekey",
              "the redis key of throttle");
DEFINE_string(danceproxy_throttle_redis_switch_key,
              "throttleSwitch",
              "switch to turn on or off the throttle feature");
DEFINE_bool(danceproxy_throttle_enabled,
            true,
            "whether enable throttle feature");

DEFINE_bool(image_diff_load, true, "whether to load fsimage into meta storage");
DEFINE_bool(image_diff_compare,
            true,
            "whether to compare fsimage with meta storage");
DEFINE_bool(image_diff_clear,
            true,
            "whether to clear meta storage before loading or comparison");

DEFINE_uint64(apply_editlog_target_txid,
              0,
              "apply editlog from current txid to given target txid");
DEFINE_bool(apply_editlog_verify_serialization,
            true,
            "verify serialization during apply editlog");

// debug related
DEFINE_bool(mock_edit_log, false, "Use mocking instead of the real edit log");
DEFINE_string(dump_parse_failed_protobuf_path,
              "",
              "/var/log/tiger/namespaces/cfs-xxx/dancenn_logs/protobuf.");

// databus related
DEFINE_int32(databus_protocol_version, 1, "the databus protocol version");
DEFINE_int32(databus_send_retry_count,
             3,
             "retry count when sending payloads to databus channel");
DEFINE_uint64(databus_connect_timeout_ms,
              100,
              "timeout in milliseconds when connect to databus");
DEFINE_string(databus_socket_path,
              "/databus/opt/sock/databus_collector.stream.sock",
              "the unix domain socket path of databus");
DEFINE_string(btrace_databus_channel,
              "btrace_log",
              "the databus channel name used by btrace");

// quota collector
DEFINE_bool(dancenn_observe_mode_on, false, "whether to start as an observer");
DEFINE_int32(dancenn_quota_redis_conn_retry_num,
             3,
             "connect the quota redis retry num");
DEFINE_string(dancenn_quota_redis_backends,
              "",
              "the hosts list for quota redis backends");
DEFINE_int32(dancenn_quota_redis_timeout_ms,
             10 * 1000,
             "connect the quota redis timeout");
DEFINE_int32(dancenn_quota_redis_conn_retry_interval_sec,
             3,
             "connect the quota redis retry interval");
DEFINE_string(dancenn_quota_redis_path2team_key,
              "path2team",
              "the map shows the path belongs to which team");
DEFINE_string(dancenn_quota_redis_default_team_key,
              "#.default",
              "the default team key");
DEFINE_string(dancenn_quota_redis_default_path_key,
              "#.default",
              "the default path key");
DEFINE_string(dancenn_quota_redis_migrating_dir_key,
              "#migratingDirs",
              "the migrating dirs key");
DEFINE_string(dancenn_quota_team_mapping_file,
              "/opt/tiger/dancenn_deploy/conf/quota.mapping",
              "the mapping file of path to team info");
DEFINE_string(dancenn_quota_redis_team_usage_key,
              "#teamUsage",
              "the team usage key");
DEFINE_string(dancenn_quota_redis_path_usage_key,
              "#pathUsage",
              "the path usage key");
DEFINE_string(dancenn_quota_redis_timestamp_key,
              "#timestamp",
              "the timestamp key");
DEFINE_string(dancenn_quota_redis_storage_key, "#storage", "the storage key");
DEFINE_bool(dancenn_observer_submit_editlogger,
            true,
            "submit editlog to EditLogger during apply");

// now only BlockReport has timeout, if other request set timeout,
// please make sure AccessControlException no side effect
DEFINE_int32(rpc_in_queue_timeout_ms_block_report,
             60 * 1000,
             "block report timeout, default 60000ms");

// Add timeout for client requests. Use StandbyException for auto-retry at
// client side
DEFINE_int32(rpc_in_queue_timeout_ms_client_req,
             60 * 1000,
             "client request timeout, default 60000ms");

DEFINE_uint64(block_report_interval_sec,
              24 * 60 * 60,
              "interval for nn to issue blockreport cmd for dn");
DEFINE_uint64(block_report_fast_fbr_interval_sec,
              24 * 60 * 60,
              "interval for nn to issue blockreport cmd for dn");
DEFINE_int32(max_ongoing_block_report_req_count,
             1,
             "max ongoing block report count, excluding fast fbr");
DEFINE_int32(max_ongoing_block_report_req_count_hard_limit,
             2,
             "max ongoing block report count, including fast fbr");
DEFINE_uint32(block_report_delta_set_max_size,
              20000000,
              "Max size for delta set for full block report");
DEFINE_uint64(block_report_window_sec, 600, "time to wait for dn make FBR");
DEFINE_uint64(block_report_scan_interval_sec,
              60,
              "time to scan dn for issue br cmd");
DEFINE_uint64(block_report_batch_interval_ms,
              1000,
              "Interval for DN to send two fbr batches");
DEFINE_uint64(block_report_fast_batch_size,
              100000,
              "Batch size for DN to send one fast fbr rpc");
DEFINE_uint64(block_report_batch_size,
              10000,
              "Batch size for DN to send one fbr rpc");
DEFINE_int32(block_report_count_during_startup,
             10,
             "Limit blockreport count during NN startup, deprecated in 4.6.2");
DEFINE_int32(block_diff_batch_size, 100000, "the batch size for block diff");

DEFINE_int32(blk_report_thread_count, 40, "the num of blk_report workers");
DEFINE_uint64(fbr_protobuf_stream_limit_mb,
              256,
              "set stream size limit of FBR blocks");

DEFINE_int32(block_recovery_thread_count, 1, "For tos mode");
DEFINE_int32(blk_replication_thread_count,
             4,
             "the num of compute block replication workers");
DEFINE_int32(blk_process_all_pending_batch_size,
             1000000,
             "the batch size to process all pending reported blocks");
DEFINE_int32(blk_uploader_thread_count, 2, "the num of upload block workers");
DEFINE_int32(ns_bg_worker_thread_count,
             8,
             "the num of workers to do NS background work");
DEFINE_int32(blk_delete_file_by_dn_evict_thread_count,
             8,
             "the num of workers to delete dn evict file");

DEFINE_int32(status_monitor_interval_ms,
             10 * 60 * 1000,
             "Interval of status monitor execution in millisecond");

DEFINE_uint32(max_component_length, 255, "the max component length");
DEFINE_uint32(max_path_length, 8000, "Max path length");
DEFINE_uint32(max_path_depth, 1000, "Max path depth");

#if defined(OS_DARWIN)
DEFINE_string(default_ethernet_card, "en0", "the default used ethernet card");
#else
DEFINE_string(default_ethernet_card, "eth0", "the default used ethernet card");
#endif

DEFINE_string(metrics_prefix,
              "inf.cfs.dancenn",
              "the metric prefix, All services of cfs nn use the same prefix, "
              "we will use filters to distinguish them.");
DEFINE_string(metric_emitter_plugin_type,
              "",
              "the type of metric emitter plugin, empty means no plugin");
DEFINE_string(metric_emitter_script_type,
              "push",
              "the type of metric emitter script, push or pull");
DEFINE_string(metric_emitter_script_env,
              "inner",
              "the env NN running in, inner or vpc");
DEFINE_string(metric_emitter_script_remote_addr,
              "127.0.0.1",
              "remote metrics server address");
DEFINE_int32(metric_emitter_script_remote_port,
             9123,
             "remote metrics server port");
DEFINE_int32(metric_emitter_script_pull_port,
             5075,
             "the port for pull metric emitter script");
// used when the metric_emitter_plugin_type is `script`
DEFINE_string(metric_emitter_script_path,
              "/opt/tiger/dancenn_deploy/tools/danceproxy_metrics.py",
              "the metric emitter script path");

// used when the metric_emitter_plugin_type is `http`
DEFINE_int32(metrics_emit_interval_ms, 10000, "metric emit interval");
DEFINE_string(metrics_emit_address, "", "the address the metric emit to");

DEFINE_bool(audit_log_enabled, false, "whether the audit log is enabled");
DEFINE_string(audit_log_databus_channel,
              "dancenn_audit_log",
              "the databus channel to send audit log");
DEFINE_string(edit_log_databus_channel,
              "dancenn_editlog",
              "the databus channel to send edit log");

// bytecool related config
DEFINE_bool(bytecool_feature_enabled,
            true,
            "whether bytecool related feature is enabled");

DEFINE_string(bvc_version, "Unknown", "The bvc version");

// recycle bin
DEFINE_bool(recycle_bin_enable,
            true,
            "Enable RecycleBin feature");
DEFINE_bool(recycle_bin_scanner_enable,
            true,
            "Enable RecycleBin background deleter");
DEFINE_uint32(recycle_bin_scanner_interval_sec,
              300,
              "RecycleScanner scan interval time");
DEFINE_uint32(recycle_bin_retention_day,
              1,
              "Retention period of RecycleBin");
DEFINE_bool(recycle_bin_default_policy_enable,
            false,
            "Enable default recycle bin policy for all data");
DEFINE_uint32(recycle_bin_default_policy_time_sec,
              86400,
              "Recycle time of default recycle bin policy, in seconds");

DEFINE_bool(hyper_block_recycle_enable, false, "Enable ");
DEFINE_int32(hyper_block_leak_threshold_sec,
             3600 * 24,
             "Threshold for metascanner to regard hyper block as leak");

// consistent
DEFINE_bool(meta_storage_snapshot_read_enabled,
            true,
            "Enable read from meta storage snapshot to avoid dir-tree lock");
DEFINE_bool(standby_read_enabled, true, "Enable read from standby");
DEFINE_bool(standby_read_set_seen_txid,
            true,
            "Enable set server txid every client rpc");
DEFINE_bool(
    standby_read_enabled_for_main_read,
    true,
    "Enable read from standby for getFileInfo/getBlockLocations/getListing");
DEFINE_bool(standby_read_enabled_for_all_read,
            false,
            "Enable read from standby for all read RPC");
DEFINE_uint32(standby_read_max_txid_gap_for_wait,
              30000,
              "The biggest txid gap worth waiting to catchup");
DEFINE_uint32(standby_read_wait_txid_catchup_time_ms_total,
              3000,
              "Waiting time for txid catchup total");
DEFINE_uint32(standby_read_wait_txid_catchup_time_ms,
              100,
              "Waiting time for txid catchup one round");

// HDFS security block-access-token related config
DEFINE_bool(security_key_enable, true, "enable security key");
DEFINE_string(security_key_manager_model,
              "local",
              "the type of key manager, [local]");
DEFINE_int64(security_key_update_interval_ms,
             600LL * 60 * 1000,
             "key update interval time, in milliseconds");
DEFINE_int64(security_key_token_life_time,
             600LL * 60 * 1000,
             "the time interval of delegation token");
DEFINE_int64(security_key_datanode_update_interval_ms,
             60LL * 1000,
             "the time interval resend block key to datanode");
DEFINE_bool(security_block_access_token_enable,
            false,
            "enable block access token");
// sha1 by default, sync with DN
DEFINE_string(
    security_block_token_encryption_algorithm,
    "sha1",
    "the encryption algorithm to create password for block access token");

// Block Lifecycle
DEFINE_int32(flush_blocklifecycle_interval_ms,
             1000,
             "flush block lifecycle data interval");
DEFINE_int32(block_lifecycle_num_slice,
             65536,
             "Number of slice for block lifecycle");
DEFINE_int32(transfer_timeout_threshold_sec,
             10,
             "Duration consider as timeout for transfer block");
DEFINE_string(block_lifecycle_dn_data_channel,
              "dancenn_block_lifecycle_dn",
              "Databus channel name for dn data");
DEFINE_string(block_lifecycle_blk_data_channel,
              "dancenn_block_lifecycle",
              "Databus channel name for blk data");
DEFINE_string(nn_local_ip, "unknown", "local ipaddr used for blocklifecycle");
DEFINE_bool(block_lifecycle_enable, false, "enable blocklifecycle");

// Op Task
DEFINE_uint32(op_task_max_size, 10, "max number of each type of op task");
DEFINE_uint32(op_task_max_dns,
              1000,
              "max number of datanode list size for each op task");
DEFINE_uint32(op_task_check_interval_sec,
              10,
              "op task check interval, in seconds");

DEFINE_bool(
    force_hyperblock_on_diffrent_dn,
    false,
    "Force hyperblocks of one hyperfile to be replaced on different dn");

DEFINE_uint32(active_write_throttler_limit, 0, "active write throttler limit");
DEFINE_uint32(active_write_throttler_shard, 32, "active write throttler shard");

DEFINE_uint32(ttl_atime_collector_batch_size, 1000, "ttl atime collector batch size");
DEFINE_uint32(ttl_atime_collector_timeout_ms, 100, "ttl atime collector timeout ms");
DEFINE_uint32(ttl_atime_cleanup_interval_sec, 6 * 3600, "ttl atime cleanup interval sec");
DEFINE_uint32(ttl_atime_collector_interval_ms, 100, "ttl atime collector interval ms");
DEFINE_uint32(max_bg_thread_count, 4, "namespace max bg thread count");

DEFINE_bool(clear_storage_when_convert_to_uc,
            true,
            "Call bi->ClearStorages() in ConvertToUnderConstruction");
DEFINE_bool(
    clear_corrupt_block_for_uc_block,
    true,
    "even if block is uc, NN can clear corrupt if exist block is enough");
DEFINE_bool(save_storage_from_uc_complete_in_sdkv2,
            true,
            "Save uc_state dn locations to block info when complete in SDKv2");

DEFINE_uint32(repl_work_max_process_missing_block,
              1000,
              "max process missing block num when compute replication work");
DEFINE_uint32(max_dn_transfer_commands_to_give,
              500,
              "max transfer commands num to give in heartbeat response");
DEFINE_bool(move_back_need_replication_queue,
            true,
            "if to move back to need replication queue if compute replication "
            "work failed");

// CloudFS related configs.
DEFINE_string(cfs_region, "cn-beijing", "CFS region");
DEFINE_string(cfs_env, "product", "CFS env, e.g. product / boe");
DEFINE_string(cfs_cluster, "las", "CFS logical cluster, e.g. normal / las");
DEFINE_string(deploy_dir, "deploy dir", "e.g. /opt/tiger");

DEFINE_bool(use_bk_editlog_os, true, "Use BookKeeper as editlog output stream or not");
DEFINE_string(non_ha_mode_trigger_file,
              "/data00/dancenn_data/run-nn-non-ha",
              "If the file is present, run the namenode as the active node in "
              "non-HA mode.");
DEFINE_int32(ha_mode_transition_manager_interval_sec,
             3,
             "HA Mode Transition Manager Check Interval");

// CloudFS permission related configs.
DEFINE_bool(permission_enabled, false, "enable posix/acl model check.");

// FIXME(wangning.ito): enable acl.
DEFINE_string(permission_model, "posix", "available options are posix/ranger.");
DEFINE_validator(permission_model, &ValidatePermissionModel);

DEFINE_bool(ping_ranger_when_starting,
            false,
            "force ping ranger when bootstrapping.");
DEFINE_string(ranger_sock_path,
              "/bridger/ranger.sock",
              "The path to talk to ranger.");

DEFINE_uint32(ranger_sock_timeout_millis,
              100,
              "Time out for requesting from ranger bridger.");

DEFINE_bool(fill_persist_info_in_located_extended_block,
            true,
            "fill persist info in located extended block");
DEFINE_bool(disable_block_uploader, false, "Disable block uploader");
DEFINE_int32(nn_dn_clock_drift_s,
             1,
             "Max time of nn dn clock drift in seconds.");
DEFINE_int32(randomized_upload_timeout_s,
             60,
             "The interval that randomizes upload timeout in seconds.");
DEFINE_int32(min_upload_timeout_s,
             10 * 60,
             "Min time can use to upload in seconds.");
DEFINE_int32(max_upload_timeout_s,
             6 * 60 * 60,
             "Max time can use to upload in seconds.");
DEFINE_uint64(max_ongoing_upload_inode_num,
              10 * 10000,
              "Max ongoing upload inode number, to ensure upload task can be "
              "completed fast rather than waiting for other tasks");
DEFINE_uint64(max_no_upload_inode_num_cnt,
              10000 * 10000,
              "Max no-upload inode number, to ensure memory cost");
DEFINE_uint64(max_no_upload_inode_grace_time_ms,
              60 * 60 * 1000,
              "grace time to recheck no upload inode");
DEFINE_int32(read_sticky_timeout_s,
             120,
             "Max time can use to download block from pufs in seconds.");
DEFINE_int32(delete_block_on_pufs_timeout_s,
             120,
             "Max time can use to delete block from pufs in seconds.");
DEFINE_int32(block_machine_requirement,
             2,
             "Number of racks that blocks should be placed at. "
             "Namenode will make best effort to meet this requirement.");
DEFINE_int32(
    block_machine_requirement_for_read,
    0,
    "Number of racks that blocks should be placed at for read request. "
    "Namenode will make best effort to meet this requirement. "
    "No such requirement now.");
DEFINE_int32(block_read_cache_refresh_interval_s,
             120,
             "Refresh read cache dns after that.");
DEFINE_int32(block_read_cache_not_expired_interval_s,
             300,
             "Evict read cache dns after that.");
DEFINE_double(block_read_cache_geo_dist_p, 0.1,
              "We use geometric distribution to randomly select dn "
              "as read cache in top k datanodes, this is possibility of "
              "geometric distribution.");
DEFINE_bool(block_read_cache_skip_detail_check, true, "");
DEFINE_string(block_placement_distribution_type,
              "geometric",
              "[round-robin, uniform, geometric, weight]");
DEFINE_validator(block_placement_distribution_type,
                 &ValidateBPDistributionType);
DEFINE_bool(placement_ignore_local_az,
            true,
            "ignore local_az when choose target");
DEFINE_bool(placement_ignore_local_host,
            false,
            "ignore local_host when choose target");
DEFINE_bool(placement_ignore_existed_switch,
            true,
            "ignore existed_switch when choose target");
DEFINE_bool(placement_ignore_existed_host,
            false,
            "ignore existed_host when choose target");
DEFINE_int32(scan_deprecating_block_max_size,
             1000,
             "Max size of deprecating blocks in one scan.");
DEFINE_int32(scan_deprecated_block_max_size,
             100,
             "Max size of deprecated blocks in one scan.");
DEFINE_int32(upload_cmds_max_size,
             20000,
             "Limit upload commands size sent to one dn");
DEFINE_int32(ne_cmds_max_size,
             100000,
             "Limit notify evictable commands size sent to one dn");
DEFINE_int32(br_cmds_max_size,
             1,
             "Limit block report commands size sent to one dn");
DEFINE_int32(load_cmds_max_size,
             200,
             "Limit notify load commands size sent to one dn");
DEFINE_int32(merge_cmds_max_size,
             100,
             "Limit merge commands size sent to one dn");
DEFINE_int32(pin_cmds_max_size,
             1000000,
             "Limit pin commands size sent to one dn");
DEFINE_string(datanode_machine_file,
              "/opt/tiger/dancenn_deploy/conf_common/datanode_machine_info",
              "File that contains datanodes vip to physical machine mapping.");
DEFINE_bool(has_writable_datanode,
            false,
            "[true] means yes, [false] means do not known yet.");
DEFINE_uint64(dn_writable_unit_capacity_bytes_unit,
              10LL * 1024 * 1024 * 1024,
              "The capacity size corresponding to the unit weight, default: "
              "10GiB. correspond max cluster capacity size: 20EB");
DEFINE_bool(dn_writable_use_total_capacity_factor,
            true,
            "Consider dn total capacity when choose target, to maintain the "
            "placement balance of the init state");
DEFINE_bool(choose_target_to_delete_by_weight,
            true,
            "if false, always choose the DN with the smallest remaining space");
DEFINE_bool(choose_target_shuffle_candidate,
            true,
            "shuffle the candidate list when choose target");

DEFINE_bool(dn_reg_ipaddr_has_colon,
            false,
            "dn reg rpc ipaddr has colon to parse");
DEFINE_bool(sort_located_block_method_v2,
            true,
            "use v2 implement to sort located block");
DEFINE_int32(
    sort_located_block_dns_hb_window_ms,
    10 * 1000,
    "If the difference between the last heartbeat time of DNs is "
    "lower than this value, we can ignored this difference. default: 10s");
DEFINE_bool(sort_located_block_dns_by_random_value,
            true,
            "If false, use dn_id instead");
DEFINE_bool(az_monitor_enable,
            false,
            "Enable AZ monitor.");
DEFINE_int32(az_monitor_refresh_interval_ms,
             5000,
             "Refresh interval for AZ monitor.");

// New Edit Log Ops.
DEFINE_bool(validate_active_write_batch_in_standby,
            true,
            "Validate write batch");
DEFINE_int32(validate_active_write_batch_mode,
             1,
             "0 for strictly mode, "
             "1 for skip mtime, atime, "
             "2 for skip mtime, atime, ugi, acls, xattrs.");

// Namespace stat
DEFINE_int32(scrub_list_subdir_batch_size, 100, "Batch size to list subdirs");
DEFINE_bool(scrub_force_refresh_stat,
            false,
            "Force refresh stat on observe mode namespace start.");
DEFINE_uint32(scrub_wait_for_done_sleep_ms,
              1,
              "Sleep time of wait-for-done, in millisecond");
DEFINE_int32(inode_stat_checker_scan_batch_size,
             4096,
             "Batch size to check inode stat");
DEFINE_bool(inode_stat_check_during_startup,
            false,
            "Check integrity of INodeStat CF during startup");
DEFINE_uint32(inode_stat_delta_cache_gc_interval_ms,
              1000,
              "Interval to GC delta cache of INodeStat");

// LifecyclePolicy
DEFINE_bool(lifecycle_enable, true, "Turn on Lifecycle feature.");
DEFINE_bool(
    lifecycle_scanner_force_start_next_group,
    false,
    "Whether start a new group immediately, as long as previous completed.");
DEFINE_bool(lifecycle_scanner_force_skip_next_group,
            false,
            "Whether skip a new group forever");
DEFINE_uint64(lifecycle_scanner_start_next_group_period_ms,
              3 * 60 * 60 * 1000,
              "LifecycleScanner start new group of scrub period.");
DEFINE_uint64(lifecycle_scanner_filter_depred_report_batch_size,
              100000,
              "Hot many deprecated report kv can be cleaned for each group");
DEFINE_uint32(
    lifecycle_scrub_persist_stats_depth,
    2,
    "Depth of child directories that persist scrub result, default: 2");
DEFINE_uint32(
    lifecycle_scrub_remove_depred_xattr_per_scurb,
    50000,
    "Remove how many deprecated StorageClass xattr per one scrub"
    "From experience, commit 15000/min");
DEFINE_uint32(blkid_cmd_max_num_blocks,
              100,
              "Max number of blocks in one BlockIdCommand");
DEFINE_string(default_storage_class,
              "WARM",
              "Default storage class");
DEFINE_validator(default_storage_class, &ValidateDefaultStorageClass);
DEFINE_bool(enable_storage_class, true, "Enable storage class");
DEFINE_bool(drop_storage_class_report,
            true,
            "Drop storage class report during start");

// Usage
DEFINE_bool(usage_report_enable, false, "Usage report enabled.");
DEFINE_string(usage_service_endpoint,
              "",
              "Usage service endpoint used to post usage data to.");
DEFINE_int32(usage_report_interval_seconds, 600, "Usage report interval.");

// Quota
DEFINE_string(observer_endpoint,
              "127.0.0.1",
              "Observer endpoint used to get dir stat");
DEFINE_int32(
    quota_expire_interval_sec_soft_limit,
    300,
    "We will try to refresh quota if time interval is larger than this value");
DEFINE_int32(
    quota_expire_interval_sec_hard_limit,
    600,
    "We believe quota is still valid if time interval is less than this value");
DEFINE_int32(
    quota_expire_txid_gap_soft_limit,
    4096,
    "We will try to refresh quota if txid gap is larger than this value");
DEFINE_int32(
    quota_expire_txid_gap_hard_limit,
    8192,
    "We believe quota is still valid if txid gap is less than this value");
DEFINE_int32(max_eager_update_quota_size,
             1024,
             "How many quotas are eager to update?");
DEFINE_int32(max_quota_batch_size_to_update,
             4096,
             "How many quotas to update in one batch?");
DEFINE_int32(quota_heartbeat_interval_sec,
             1,
             "Active NN sends heartbeat to observer NN every interval");
DEFINE_int32(quotamap_num_slice, 64, "Number of slice for quota map");
DEFINE_int32(quotamap_num_element_each_slice,
             1024,
             "Number of elements for each quota map slice");

// BlockInfoProtoV2
DEFINE_bool(enable_fast_block_id_and_gs_gen,
            true,
            "gen block_id and gs by c++ instead of java");
DEFINE_int32(acquire_dirty_bip_blk_lock_slow_log_ms,
             50,
             "Print slow log if acquire block lock time "
             "is bigger than this value.");
DEFINE_int32(hold_dirty_bip_blk_lock_slow_log_ms,
             50,
             "Print slow log if hold block lock time "
             "is bigger than this value.");
DEFINE_int32(acquire_dirty_bip_bucket_lock_slow_log_ms,
             50,
             "Print slow log if acquire bucket lock time "
             "is bigger than this value.");
DEFINE_int32(hold_dirty_bip_bucket_lock_slow_log_ms,
             50,
             "Print slow log if hold bucket lock time "
             "is bigger than this value.");
DEFINE_int32(dirty_block_info_proto_bucket_num,
             4096,
             "DirtyBlockInfoProtoBucket size.");
DEFINE_int32(process_block_report_batch_size,
             1024,
             "Process <n> blocks in one batch "
             "of incremental/full block report");
DEFINE_int32(block_report_hard_limit_ms,
             24 * 60 * 60 * 1000,
             "Do not believe replica info if it's report time gap"
             " exceed this value");
// ACC
// UFS
DEFINE_bool(ufs_read_only, false, "Enable write operation on ufs or not");
DEFINE_bool(ufs_support_append, true, "Ufs support append object or not");
DEFINE_bool(ufs_delete_for_overwrite,
            true,
            "Delete ufs object when overwrite");
DEFINE_bool(ufs_delete_for_overwrite_appendable_file,
            true,
            "Delete ufs object when overwrite appendable file");
DEFINE_bool(ufs_sync_for_overwrite_conflict,
            true,
            "Sync ufs file when conflict for overwrite");
DEFINE_bool(ufs_sync_for_overwrite_conflict_appendable_file,
            true,
            "Sync ufs file when conflict for overwrite appendable file");
DEFINE_int32(ufs_worker_thread_num,
             64,
             "Number of worker threads for UFS worker pool.");
DEFINE_int32(ufs_worker_pending_tasks_limit,
             4096,
             "Max number of pending tasks for UFS worker pool.");
DEFINE_int32(ufs_s3_client_retry_count,
             300,
             "S3 API retry count on retryable errors such as 429.");
DEFINE_int32(ufs_s3_client_retry_sleep_interval_ms,
             500,
             "S3 API retry sleep interval ms on retryable errors such as 429.");
DEFINE_int32(ufs_s3_client_req_timeout_ms,
             60000,
             "Timeout for s3 client request.");
DEFINE_int32(ufs_s3_client_conn_timeout_ms,
             10000,
             "Timeout for s3 client connect.");
DEFINE_int32(ufs_s3_client_max_http_conns,
             512,
             "Max number of http connection for s3 client, should be greater "
             "than ufs_worker_thread_num.");
DEFINE_int32(ufs_syncengine_worker_num,
             64,
             "Number of syncengine other workers.");
DEFINE_int32(ufs_syncengine_list_worker_num,
             64,
             "Number of syncengine list workers.");
DEFINE_int32(ufs_syncengine_delete_worker_num,
             32,
             "Number of syncengine delete workers.");
DEFINE_int32(ufs_syncengine_rename_worker_num,
             32,
             "Number of syncengine rename workers.");
DEFINE_int32(ufs_syncengine_max_tasks_num,
             4096,
             "Max number of syncengine tasks.");
DEFINE_int32(ufs_sync_min_interval, 30, "Minimum sync interval in seconds.");
DEFINE_uint32(ufs_sync_listing_page_size,
              1000,
              "Number of entry to list from ufs");
DEFINE_bool(ufs_sync_listing_prefetch_enabled,
            false,
            "Enable sync listing prefetch on getFileInfo and getBlockLocation "
            "RPC, it will sync it's parent directory.");
DEFINE_uint32(ufs_sync_listing_prefetch_max_count,
              10000,
              "Max count for getFileInfo triggerred sync listing");
DEFINE_bool(ufs_sync_listing_prefetch_only_once,
            true,
            "Only prefetch once for a dir");
DEFINE_int32(ufs_syncengine_recursive_list_timeout_sec,
             -1,
             "Timeout for recursive list.");
DEFINE_bool(enable_write_back,
            true,
            "Used to temporary disable write back");
DEFINE_bool(write_back_by_default,
            true,
            "if false, all file is no upload by default");
DEFINE_bool(enable_write_back_task_persistence,
            true,
            "Persist WriteBackTask or not");
DEFINE_bool(force_rebuild_write_back_task_db,
            false,
            "rescan inode table and build write back task db");
DEFINE_int32(write_back_task_v2_rocksdb_num_levels,
             3,
             "Number of level for WriteBackTask CF");
DEFINE_uint32(write_back_manager_worker_count, 4, "Count of write back worker");
DEFINE_uint32(write_back_manager_scan_task_interval_ms,
              10000,
              "Interval for write back manager to wake up sleeping worker.");
DEFINE_int64(write_back_task_v2_trigger_interval_us,
             3 * 1000 * 1000,
             "Interval for meta scanner v2 to trigger write back task v2.");
DEFINE_int64(write_back_task_v2_stat_trigger_interval_us,
             10 * 1000 * 1000,
    "Interval for meta scanner v2 to trigger write back task v2 stat.");
DEFINE_uint32(write_back_task_v2_max_scan_num,
              1000000000,
              "Max number of inode to handle in one scan");
DEFINE_uint32(write_back_task_v2_throttle_interval_ms,
              100,
              "Sleep time when too many write back task");
DEFINE_bool(write_back_always_check_ufs, false, "Always check UFS file status");
DEFINE_int32(write_back_num_failure_before_check_ufs,
             100,
             "Check UFS file status after this number of failure");

DEFINE_bool(enable_ufs_evict_write_cache,
            false,
            "Allow to evict DN data when file is write cache");
DEFINE_bool(enable_ufs_evict_write_cache_delete_file,
            true,
            "Allow DN evict cache and delete file.");
DEFINE_bool(enable_ufs_evict_write_cache_delete_file_opened,
            false,
            "Allow DN evict cache and delete UC file.");
DEFINE_uint32(ufs_evict_write_cache_max_num_pending_tasks,
              1000000,
              "Max ongoing task");
DEFINE_uint64(ufs_evict_write_cache_grace_time_ms,
              5 * 60 * 1000,
              "File-level write cache allow to evict grace time");

DEFINE_uint32(acc_mpu_max_part, 10000, "max number of part for a MPU");
DEFINE_uint64(acc_mpu_part_threshold,
              100LL * 1024 * 1024,
              "threshold to create a MPU part in bytes.");
DEFINE_uint64(acc_append_max_size,
              1024LL * 1024 * 1024 * 9 / 2,
              "Max size for append object in bytes.");
DEFINE_uint32(ufs_evict_append_object_max_block_cnt_when_persist,
              10,
              "Issue evict to all blocks when appendable object persisted");
DEFINE_uint32(acc_batch_add_editlog_batch_size,
              100,
              "In a batch add op, the number of files in one batch to write to "
              "editlog and apply to local db.");
DEFINE_validator(acc_batch_add_editlog_batch_size,
                 &ValidateAccBatchAddEditLogBatchSize);
DEFINE_uint64(acc_batch_add_editlog_batch_max_size_bytes,
              4ULL * 1024 * 1024 * 1024 * 1024,
              "In a batch add op, the max size of all files in one batch to "
              "write to editlog and apply to lcoal db. At least one file will "
              "be write if that single file is larger than the threshold.");
DEFINE_bool(nn_drive_upload, true, "nn or dn drive block upload to ufs");
DEFINE_bool(skip_dn_negoed_ibr, false, "skip dn negoed ibr to upgrade dn");
DEFINE_bool(enable_transfer_persist_block, true, "Transfer persist block for complementing pinned replicas");
DEFINE_bool(enable_load_for_complete_replica, true, "Load zero replica block for complementing pinned replicas");
DEFINE_bool(block_expected_replica_determined_by_inode,
            true,
            "block expected replica from inode replication");
DEFINE_int32(upload_monitor_fg_task_thread_count,
             2,
             "the num of file upload trigger fg workers");
DEFINE_int32(upload_monitor_table_slice_num,
             32,
             "the num of table slice of upload monitor and worker class");
DEFINE_uint64(upload_monitor_bg_task_interval_ms,
              10 * 60 * 1000,
              "the interval of bg task(uploading/noupload)");
DEFINE_int32(upload_file_to_ufs_slow_time_ms,
             10 * 60 * 1000,
             "upload file to ufs slow time ms");
DEFINE_int32(sync_file_from_ufs_slow_time_ms,
             5 * 60 * 1000,
             "sync file from ufs slow time ms");
DEFINE_bool(ufs_tos_shallow_copy_enabled,
            false,
            "In TOS' copy implementation, if shallow copy is enabled we use CopyObject API only and DONOT use multi part copy.");
DEFINE_uint64(ufs_tos_shallow_copy_max_count,
              10000000,
              "Max object count for one rename call when shallow copy enabled");
DEFINE_uint64(ufs_tos_shallow_copy_enabled_epoch_sec,
              1717113600, // 2024/5/31/00:00:00
              "The bucket's shallow copy feature enabled timestamp in epoch seconds. For objects before this time, shallow copy is not enabled and we should keep current MultiPartCopy logic.");
DEFINE_bool(ufs_rename_update_local_etag, true, "Update local etag when rename ufs");

// datanode persistence
DEFINE_bool(datanode_info_bg_dump_enable, true, "enable bg datanode info dump");
DEFINE_int32(datanode_info_bg_dump_batch_size,
             1000,
             "batch size of background datanode info dump to DB.");
DEFINE_bool(datanode_info_fg_dump_enable, true, "enable fg datanode info dump");
DEFINE_int32(datanode_info_fg_dump_size_threshold,
             100000,
             "size threshold for foreground datanode info dump to DB.");
DEFINE_int32(datanode_info_fg_dump_time_threshold,
             86400,
             "time threshold for foreground datanode info dump to DB.");

DEFINE_string(banned_user, "", "Banned user(unique)");


// JobManager
DEFINE_bool(job_manager_enable, true, "Enable job manager");

DEFINE_uint32(managed_job_state_cached_ms,
              6 * 60 * 60 * 1000,
              "Managed job info cached time after job completed.");
DEFINE_uint32(managed_job_block_task_exec_timeout_ms,
              12 * 60 * 60 * 1000,
              "Managed job for block task execution timeout.");
DEFINE_int32(managed_metadata_job_exec_timeout_ms,
              -1,
              "Managed metadata job execution timeout.");
DEFINE_int32(single_job_exec_timeout_ms,
              6 * 60 * 60 * 1000,
              "Single job execution timeout.");
DEFINE_uint32(managed_task_load_max_replica,
              3,
              "Max request replica to load.");
DEFINE_uint32(managed_task_load_execute_timeout_ms,
              2 * 60 * 1000,
              "Load managed task execute timeout.");
DEFINE_uint32(managed_task_free_execute_timeout_ms,
              5 * 60 * 1000,
              "Free managed task execute timeout.");
DEFINE_uint32(copy_replica_task_execute_timeout_ms,
              3 * 60 * 1000,
              "Copy replica task execute timeout.");
DEFINE_int32(managed_task_load_metadata_execute_timeout_ms,
              -1,
              "Load metadata managed task execute timeout.");
DEFINE_bool(enable_load_task_generate_ufs_name,
             true,
             "Generate block pufs name for load managed task instead of use "
             "pufs name from inode.");
DEFINE_int32(managed_task_load_max_retry_times,
              100,
              "Load managed task max retry times.");
DEFINE_int32(managed_task_free_max_retry_times,
              10,
              "Free managed task max retry times.");
DEFINE_int32(copy_replica_task_max_retry_times,
              50,
              "Copy replica task max retry times.");
DEFINE_int32(managed_task_load_metadata_max_retry_times,
              1,
              "Load metadata managed task max retry times.");
DEFINE_uint32(load_cmd_batch_size,
              3000,
              "Max block size of one LoadCommandProto");
DEFINE_uint32(load_cmd_submit_period_sec,
              1,
              "Sleep period between add load command to block manager");
DEFINE_uint32(load_blk_cmd_cache_max_size,
              100000,
              "Max size for number of load commands");

DEFINE_int32(load_cmd_to_job_manager_retry_times,
              10,
              "Retry times for add load command");
DEFINE_uint32(load_cmd_to_job_manager_retry_sleep_sec,
              2,
              "Sleep for add block command retry");
DEFINE_uint32(job_tracker_max_pending_block_tasks,
              100000,
              "Max size of pending block tasks for job tracker");
DEFINE_uint32(job_tracker_max_pending_autonomous_tasks,
              4096,
              "Max size of pending autonomous tasks for job tracker");
DEFINE_uint32(job_tracker_max_pending_job,
              100000,
              "Max size of pending job for job tracker");
DEFINE_uint32(job_tracker_max_shared_task,
              5,
              "Max shared tasks for the same block");
DEFINE_uint32(job_handler_thread_num, 50, "Managed job handler thread num");
DEFINE_uint32(job_handler_thread_pool_queue_size,
              10000,
              "Queue size for managed job handler thread pool");
DEFINE_uint32(task_handler_thread_num, 50, "Managed task handler thread num");
DEFINE_uint32(task_handler_thread_pool_queue_size,
              10000,
              "Queue size for Managed task handler thread pool");
DEFINE_uint32(track_block_task_thread_num, 2, "Job block tracker thread num");
DEFINE_uint32(track_block_task_pool_queue_size,
              50000,
              "Queue size for Job block tracker thread pool");
DEFINE_uint32(job_tracker_inspect_period_sec, 3, "Job tracker sleep time");
DEFINE_uint32(job_tracker_metastorage_inspect_period_sec,
              60,
              "Job tracker for meta storage sleep time");
DEFINE_uint32(recursive_sync_listing_sleep_us, 0, "");
DEFINE_uint32(job_tracker_worker_retry_sleep_us, 0, "");
DEFINE_int32(job_tracker_worker_retry_times, 0, "");
DEFINE_uint32(
    dump_memory_prof_interval_ms,
    30000,
    "Dump jemalloc prof interval in ms, only when MALLOC_CONF is set");
DEFINE_int32(load_compatible_dn_version,
             10500,
             "The feature of load and free works with which version of DN");
DEFINE_int64(
    inode_attr_ttl_scan_interval_us,
    10 * 1000 * 1000,
    "Interval for meta scanner v2 to trigger inode attr ttl scan task.");
DEFINE_uint32(inode_attr_ttl_throttle_interval_ms,
             1000,
             "Sleep time when too many write back task");

DEFINE_bool(enable_aws_trace_log, false, "Enable aws sdk trace log");
DEFINE_bool(trace_rpc_log, true, "Log to Trace RPC time");
DEFINE_uint32(trace_rpc_log_every, 50000, "Do Trace every N RPC");

DEFINE_bool(disallow_deprecated_rpc, true, "Disallow deprecated RPC");
DEFINE_bool(enable_fast_shutdown,
            false,
            "Enable fast shutdown, run the user program to release all sockets "
            "before coredump");

DEFINE_bool(ignore_sync_action_update_time,
            false,
            "ignore sync action update time");
DEFINE_bool(acc_always_not_sync, false, "acc always not sync, control by NN");
DEFINE_bool(acc_always_sync, false, "acc always sync, control by NN");
DEFINE_bool(enable_acc_sync_op_lazycheck, false, "lazy check acc sync write operation, control by NN");
DEFINE_int32(submit_task_retry_times, 25, "Retry times for submit error");

DEFINE_bool(enable_lock_many_path,
            true,
            "use by batch/concat API, lock many path instead of parent");
DEFINE_uint64(batch_api_max_files, 1024, "max number of files in batch api");
DEFINE_uint64(batch_create_overwrite_grace_time_ms,
              10 * 60 * 1000,
              "Grace time to allow batch create to overwrite file in kvcache "
              "scenario, unit: ms");
DEFINE_bool(enable_batch_create_precheck,
            true,
            "Enable precheck of batch create api to filter out retry request");
DEFINE_bool(dir_lock_log_detail, false, "show lock detail");
DEFINE_bool(block_ignore_not_exist_fatal, false, "no fatal if block not exist");

DEFINE_bool(
    check_loose_mode,
    false,
    "loose all check to let NN run. "
    "DANGEROUS!!!, never set to true unless you know what you are doing");

DEFINE_int32(ut_loglevel_v, 10, "ut gtest log v");
