//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include "safemode/safemode.h"

#include <glog/logging.h>

#include <shared_mutex>
#include <thread>
#include <tuple>
#include <cstdint>
#include <mutex>
#include <memory>
#include <utility>
#include <limits>
#include <chrono>

#include "base/vlock.h"
#include "ha/ha_state_base.h"
#include "namespace/namespace.h"

DECLARE_bool(safemode_check_block_cnt);
DECLARE_double(safemode_threshold_pct);
DECLARE_bool(safemode_check_dn_cnt);
DECLARE_int32(safemode_min_datanodes);
DECLARE_bool(safemode_check_safe_dn_cnt);
DECLARE_double(safemode_safe_dn_threshold_pct);
DECLARE_int32(safemode_extension_ms);
DECLARE_int64(safemode_safe_check_interval_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_double(dfs_namenode_replqueue_threshold_pct);
DECLARE_bool(safemode_strict_check_block_count);

namespace dancenn {

SafeMode::SafeMode(std::shared_ptr<EditLogContextBase> edit_log_context,
                   std::shared_ptr<VRWLock> barrier)
    : barrier_(std::move(barrier)),
      edit_log_context_(std::move(edit_log_context)),
      state_(SafeModeState::kStartingUp),
      last_enter_time_(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count()),
      last_leave_time_(std::numeric_limits<uint64_t>::max()) {
  LOG(INFO) << "Safemode started, flags: check block cnt = "
            << FLAGS_safemode_check_block_cnt
            << ", safemode_threshold_pct = " << FLAGS_safemode_threshold_pct
            << ", safemode_check_dn_cnt = " << FLAGS_safemode_check_dn_cnt
            << ", safemode_min_datanodes = " << FLAGS_safemode_min_datanodes
            << ", safemode_check_safe_dn_cnt = "
            << FLAGS_safemode_check_safe_dn_cnt
            << ", safemode_safe_dn_threshold_pct = "
            << FLAGS_safemode_safe_dn_threshold_pct
            << ", safemode_extension = " << FLAGS_safemode_extension_ms
            << ", safe_replication = " << FLAGS_dfs_replication_min;

  block_total_ = 0;
  block_safe_ = 0;
  block_safe_target_ = 0;

  monitor_ = std::make_unique<cnetpp::concurrency::ThreadPool>("SafeMode",
                                                               true);
  monitor_->set_num_threads(1);
  monitor_->Start();
}

SafeMode::~SafeMode() {
  Stop();
}

void SafeMode::Start() {
  monitor_->AddDelayTask([this] () { DoCheckModeTask(); return true; },
                         std::chrono::milliseconds(
                           FLAGS_safemode_safe_check_interval_ms));
}

void SafeMode::Stop() {
  if (monitor_) {
    monitor_->Stop(false);
    monitor_.reset();
  }
}

void SafeMode::CheckMode() {
  if (!IsStartingUp() || ha_state_->InTransition()) {
    return;
  }

  if (NeedEnter()) {
    if (IsStartingUpInternal()) {
      DoEnter();
      PrintStatusReport("Safe mode is ON.");
    }
    return;
  }

  // don't need to wait
  if (FLAGS_safemode_extension_ms <= 0) {
    DoLeave();
    return;
  }

  // threshold has already been reached before
  if (state_ == SafeModeState::kInExtension) {
    return;
  }

  state_ = SafeModeState::kInExtension;
  extension_time_point_ = std::chrono::steady_clock::now();

  StartSafeModeMonitor();
  LOG(INFO) << "Safe mode extension entered.";
}

void SafeMode::GetSafeModeTip(std::string *tip) {
  CHECK_NOTNULL(tip);
  cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
  // std::lock_guard<std::mutex> guard(mutex_);
  if (!IsOn()) {
    *tip = "Safe mode is OFF.";
    return;
  }

  if (IsManual()) {
    *tip = " It was turned on manually.\n"
        "Use \"hdfs dfsadmin -safemode leave\" to turn safe mode off.";
    return;
  }

  bool thresholds_met = true;
  if (FLAGS_safemode_check_block_cnt) {
    if (block_safe_ < block_safe_target_) {
      *tip += " The reported blocks " + std::to_string(block_safe_) +
              " needs additional " +
              std::to_string(block_safe_target_ - block_safe_) +
              " blocks to reach the threshold " +
              std::to_string(FLAGS_safemode_threshold_pct) +
              " of total blocks " + std::to_string(block_total_) + ".";
      thresholds_met = false;
    } else {
      *tip += " The reported blocks " + std::to_string(block_safe_) +
              " has reached the threshold " +
              std::to_string(FLAGS_safemode_threshold_pct) +
              " of total blocks " + std::to_string(block_total_) + ".";
    }
  } else {
    *tip += " Check block count is OFF.";
  }

  if (FLAGS_safemode_check_dn_cnt) {
    auto num_live = ns_->datanode_manager()->NumLiveDatanodes();
    if (num_live < FLAGS_safemode_min_datanodes) {
      *tip += " The number of live datanodes " + std::to_string(num_live) +
              " needs an additional " +
              std::to_string(FLAGS_safemode_min_datanodes - num_live) +
              " live datanodes to reach the minimum number " +
              std::to_string(FLAGS_safemode_min_datanodes) + ".";
      thresholds_met = false;
    } else {
      *tip += " The number of live datanodes " + std::to_string(num_live) +
              " has reached the minimum number " +
              std::to_string(FLAGS_safemode_min_datanodes) + ".";
    }
  } else {
    *tip += " Check datanode count is OFF.";
  }

  if (FLAGS_safemode_check_safe_dn_cnt) {
    auto stat = ns_->datanode_manager()->GetBriefStorageStat();
    auto num_expect_safe_dn = stat.num_normal_datanode +
                              stat.num_decommissioning_datanode +
                              stat.num_entering_maintenance_datanode;
    // Allow one dn failure
    auto unsafe_dn = stat.num_content_stale_datanode;
    if ((num_expect_safe_dn > 1) &&
        ((num_expect_safe_dn - unsafe_dn) * 1.0 / (num_expect_safe_dn - 1) <
         FLAGS_safemode_safe_dn_threshold_pct)) {
      *tip += " The number of safe datanodes " +
              std::to_string(num_expect_safe_dn - unsafe_dn) +
              " does not reach the threshold " +
              std::to_string(FLAGS_safemode_safe_dn_threshold_pct) +
              ". Expect safe dn num is " +
              std::to_string(num_expect_safe_dn - 1) + ".";
      thresholds_met = false;
    } else {
      *tip += " The number of safe datanodes " +
              std::to_string(num_expect_safe_dn - unsafe_dn) +
              " has reached the threshold " +
              std::to_string(FLAGS_safemode_safe_dn_threshold_pct) +
              ". Expect safe dn num is " +
              std::to_string(num_expect_safe_dn - 1) + ".";
    }
  } else {
    *tip += " Check safe datanode count is OFF.";
  }

  *tip +=
      state_ == SafeModeState::kInExtension ? " In safe mode extension." : "";
  *tip += " Safe mode will be turned off automatically";

  auto left_time_ms =
      std::chrono::duration_cast<std::chrono::milliseconds>(
          extension_time_point_ - std::chrono::steady_clock::now()).count() +
      FLAGS_safemode_extension_ms;
  if (!thresholds_met) {
    *tip += " once the thresholds have been reached.";
  } else if (left_time_ms > 0) {
    *tip += (" in " + std::to_string(left_time_ms / 1000) + " seconds.");
  } else {
    *tip += " soon.";
  }
}

bool SafeMode::IsStartingUp() {
  // cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
  // std::lock_guard<std::mutex> guard(mutex_);
  return IsStartingUpInternal();
}

bool SafeMode::IsStartingUpInternal() {
  return IsOn() && !IsManual();
}

bool SafeMode::IsManual() {
  return IsOn() && FLAGS_safemode_extension_ms == INT32_MAX;
}

bool SafeMode::IsOn() {
  return state_ != SafeModeState::kOff;
}

Status SafeMode::SetSafeMode(::cloudfs::SafeModeActionProto action,
                             OperationsCategory op) {
  Status res = ha_state_->UnprotectedCheckOperation(op);
  if (res.HasException()) {
    return res;
  }

  if (action != ::cloudfs::SAFEMODE_GET) {
    cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
    // std::lock_guard<std::mutex> guard(mutex_);
    // We must ensure that there is no ongoing write rpcs
    dancenn::vunique_lock bguard(barrier_->lock());

    switch (action) {
      case ::cloudfs::SAFEMODE_LEAVE:
        Leave();
        break;
      case ::cloudfs::SAFEMODE_ENTER:
        Enter();
        break;
      default:
        LOG(ERROR) << "Unexpected safe mode action.";
    }
  }

  return Status(IsOn() ? Code::kOK : Code::kFalse);
}

void SafeMode::Enter() {

  // In observer state, we need to enter safe mode manually after
  // the launch of the server. Therefore, we should remove the check
  // of safe mode in this function, so does the java version.
  //
  // if (IsOn()) {
  //   LOG(INFO) << "Safe Mode is already ON";
  //   return;

  if (edit_log_context_->IsOpenForWrite()) {
    edit_log_context_->LogSyncAll();
  }

  DoEnter();
  SetManual();

  LOG(INFO) << "Safe mode is ON.";
}

void SafeMode::Leave() {
  if (!IsOn()) {
    LOG(INFO) << "Safe Mode is already OFF";
    return;
  }
  DoLeave();
}

void SafeMode::SetBlockTotal(int64_t total) {
  cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
  LOG(INFO) << "Set block total: " << total
            << ", state: " << static_cast<int>(state_.load());
  // std::lock_guard<std::mutex> guard(mutex_);
  if (state_ == SafeModeState::kOff) {
    return;
  }
  SetBlockTotalInternal(total);
}

void SafeMode::SetBlockTotalInternal(int64_t total) {
  block_total_ = total;
  block_safe_target_ = static_cast<uint64_t>(
      block_total_ * FLAGS_safemode_threshold_pct);

  if (block_safe_ < 0) {
    block_safe_ = 0;
  }

  CheckMode();
}

// Print status every 20 seconds.
void SafeMode::PrintStatusReport(const std::string& msg) {
  auto now = std::chrono::duration_cast<std::chrono::seconds>
      (std::chrono::steady_clock::now().time_since_epoch()).count();
  if (now > last_report_time_ + 20) {
    LOG(INFO) << msg;
    last_report_time_ = now;
  }
}

void SafeMode::AdjustSafeModeBlockTotals(int delta_safe, int delta_total) {
  if (!IsStartingUp()) {
    return;
  }

  cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
  // std::lock_guard<std::mutex> guard(mutex_);

  // This is a temporary fix, please fix it again.
  // https://bytedance.feishu.cn/docx/doxcnM0ns8AjyNNohuguTkuvdSf
  // Consider thw following case:
  // 1. Standby NN starts up.
  // 2. DN does block reports, blk_1 has enough replicas,
  //    and IncrementSafeBlockCount is called.
  // 3. Replay persist block or delete file edit logs, blk_1 is removed from
  //    safemode, and AdjustSafeModeBlockTotals is called.
  // block_safe_ + delta_safe can be less than zero.
  if (block_safe_ + delta_safe < 0) {
    int64_t sum = 0;
    {
      std::shared_lock<RWSpinlock> rguard(delta_rwlock_);
      for (auto &delta : block_safe_delta_per_thread_) {
        sum += delta.second.exchange(0);
      }
    }
    block_safe_ += sum;
  }

  // Always(ha enabled and disabled) track blocks, different with java code.
  if (FLAGS_safemode_strict_check_block_count) {
    CHECK_GE(block_safe_ + delta_safe, 0);
    CHECK_GE(block_total_ + delta_total, 0);
  }
  block_safe_ += delta_safe;
  SetBlockTotalInternal(block_total_ + delta_total);
}

void SafeMode::IncrementSafeBlockCount(uint32_t replication) {
  if (!IsStartingUp() || replication != FLAGS_dfs_replication_min) {
    return;
  }

  bool found = false;
  std::map<std::thread::id, std::atomic<int64_t>>::iterator itr;
  {
    std::shared_lock<RWSpinlock> rguard(delta_rwlock_);
    itr = block_safe_delta_per_thread_.find(std::this_thread::get_id());
    found = (itr != block_safe_delta_per_thread_.end());
  }
  if (!found) {
    std::unique_lock<RWSpinlock> wguard(delta_rwlock_);
    itr = block_safe_delta_per_thread_.find(std::this_thread::get_id());
    found = (itr != block_safe_delta_per_thread_.end());
    if (!found) {
      itr = block_safe_delta_per_thread_.emplace(std::piecewise_construct,
          std::forward_as_tuple(std::this_thread::get_id()),
          std::forward_as_tuple(0)).first;
    }
  }
  itr->second++;
}

void SafeMode::DecrementSafeBlockCount(uint32_t replication) {
  if (!IsStartingUp() || replication != FLAGS_dfs_replication_min - 1) {
    return;
  }
  bool found = false;
  std::map<std::thread::id, std::atomic<int64_t>>::iterator itr;
  {
    std::shared_lock<RWSpinlock> rguard(delta_rwlock_);
    itr = block_safe_delta_per_thread_.find(std::this_thread::get_id());
    found = (itr != block_safe_delta_per_thread_.end());
  }
  if (!found) {
    std::unique_lock<RWSpinlock> wguard(delta_rwlock_);
    itr = block_safe_delta_per_thread_.find(std::this_thread::get_id());
    found = (itr != block_safe_delta_per_thread_.end());
    if (!found) {
      itr = block_safe_delta_per_thread_.emplace(std::piecewise_construct,
          std::forward_as_tuple(std::this_thread::get_id()),
          std::forward_as_tuple(0)).first;
    }
  }
  itr->second--;
}

void SafeMode::DoEnter() {
  if (state_ == SafeModeState::kOff) {
    last_enter_time_ = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    last_leave_time_ = std::numeric_limits<uint64_t>::max();
  }
  state_ = SafeModeState::kOn;
}

void SafeMode::DoLeave() {
  if (state_ != SafeModeState::kOff) {
    state_ = SafeModeState::kOff;
    LOG(INFO) << "Safe mode is OFF.";
    last_leave_time_ = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
  }
}

bool SafeMode::NeedEnter() {
  bool need = false;
  if (FLAGS_safemode_check_block_cnt) {
    need |= FLAGS_safemode_threshold_pct > 0 &&
            block_safe_ < block_safe_target_;
  }
  if (FLAGS_safemode_check_dn_cnt) {
    need |= FLAGS_safemode_min_datanodes > 0 &&
            ns_->datanode_manager()->NumLiveDatanodes() <
                FLAGS_safemode_min_datanodes;
  }
  if (FLAGS_safemode_check_safe_dn_cnt) {
    // Allow one dn failure
    BriefStorageStat stat = ns_->datanode_manager()->GetBriefStorageStat();
    auto num_expect_safe_dn = stat.num_normal_datanode +
                              stat.num_decommissioning_datanode +
                              stat.num_entering_maintenance_datanode;
    auto unsafe_dn = stat.num_content_stale_datanode;
    need |=
        FLAGS_safemode_safe_dn_threshold_pct > 0 &&
        ((num_expect_safe_dn > 1) &&
         ((num_expect_safe_dn - unsafe_dn) * 1.0 / (num_expect_safe_dn - 1) <
          FLAGS_safemode_safe_dn_threshold_pct));
  }
  return need;
}

bool SafeMode::CanLeave() {
  CHECK(state_ != SafeModeState::kOff);

  if (state_ == SafeModeState::kOn ||
      state_ == SafeModeState::kStartingUp) {
    return false;
  }

  if (std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::steady_clock::now() - extension_time_point_)
          .count() < FLAGS_safemode_extension_ms) {
    LOG(INFO) << "Safe mode ON, in safe mode extension.";
    return false;
  }

  if (NeedEnter()) {
    LOG(INFO) << "Safe mode ON, thresholds not met.";
    return false;
  }
  return true;
}

void SafeMode::DoCheckModeTask() {
  if (!IsStartingUp()) {
    return;
  }

  int64_t sum = 0;
  {
    std::shared_lock<RWSpinlock> rguard(delta_rwlock_);
    for (auto& delta : block_safe_delta_per_thread_) {
      sum += delta.second.exchange(0);
    }
  }
  {
    cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
    block_safe_ += sum;
    CheckMode();
  }
  monitor_->AddDelayTask([this] () { DoCheckModeTask(); return true; },
                         std::chrono::milliseconds(
                           FLAGS_safemode_safe_check_interval_ms));
}

void SafeMode::DoMonitorTask() {
  if (!IsStartingUp()) {
    safe_mode_monitor_started_ = false;
    return;
  }

  // std::lock_guard<std::mutex> guard(mutex_);
  cnetpp::concurrency::SpinLock::ScopeGuard guard(mutex_);
  if (CanLeave()) {
    DoLeave();
    safe_mode_monitor_started_ = false;
    return;
  }
  monitor_->AddDelayTask([this] () { DoMonitorTask(); return true; },
                         std::chrono::microseconds(1000000));
}

void SafeMode::StartSafeModeMonitor() {
  if (!safe_mode_monitor_started_) {
    monitor_->AddTask([this] () { DoMonitorTask(); return true; });
    safe_mode_monitor_started_ = true;
  }
}

}  // namespace dancenn

