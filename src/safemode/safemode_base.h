// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef SAFEMODE_SAFEMODE_BASE_H_
#define SAFEMODE_SAFEMODE_BASE_H_

#include <cnetpp/concurrency/spin_lock.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread_pool.h>

#include <thread>
#include <cstdint>
#include <mutex>
#include <string>
#include <memory>

#include "HAServiceProtocol.pb.h"  // NOLINT(build/include)
#include "ClientNamenodeProtocol.pb.h"  // NOLINT(build/include)
#include "base/status.h"
#include "base/state_transition_lock.h"
#include "edit/edit_log_context.h"
#include "ha/operations.h"

namespace dancenn {

class SafeModeBase {
 public:
  SafeModeBase() = default;
  virtual ~SafeModeBase() = default;
  SafeModeBase(const SafeModeBase& other) = delete;
  SafeModeBase& operator=(const SafeModeBase& other) = delete;

  virtual void Start() = 0;
  virtual void Stop() = 0;

  virtual bool IsOn() = 0;
  virtual bool IsStartingUp() = 0;
  virtual bool IsManual() = 0;

  virtual void GetSafeModeTip(std::string *tip) = 0;

  // Called from ClientNamenodeProtocol
  // May change the state of safe mode
  virtual Status SetSafeMode(::cloudfs::SafeModeActionProto action,
                             OperationsCategory op) = 0;

  // Called when finished scan meta after start
  virtual void SetBlockTotal(int64_t total) = 0;

  // Called when there is some DN registered
  virtual void AdjustSafeModeBlockTotals(int delta_safe, int delta_total) = 0;
  virtual void IncrementSafeBlockCount(uint32_t replication) = 0;
  virtual void DecrementSafeBlockCount(uint32_t replication) = 0;

  virtual uint64_t last_enter_time() const { return 0; }
  virtual uint64_t last_leave_time() const { return 0; }
};

}  // namespace dancenn

#endif  // SAFEMODE_SAFEMODE_BASE_H_

