//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef SAFEMODE_SAFEMODE_H_
#define SAFEMODE_SAFEMODE_H_

#include <cnetpp/concurrency/spin_lock.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread_pool.h>

#include <thread>
#include <cstdint>
#include <mutex>
#include <string>
#include <memory>

#include "HAServiceProtocol.pb.h"  // NOLINT(build/include)
#include "ClientNamenodeProtocol.pb.h"  // NOLINT(build/include)
#include "base/status.h"
#include "base/rw_spinlock.h"
#include "edit/edit_log_context.h"
#include "ha/operations.h"
#include "safemode/safemode_base.h"

DECLARE_int32(safemode_extension_ms);

namespace dancenn {

class NameSpace;
class HAStateBase;
class VRWLock;

class SafeMode : public SafeModeBase {
 public:
  SafeMode(std::shared_ptr<EditLogContextBase> edit_log_context,
           std::shared_ptr<VRWLock> barrier);
  ~SafeMode();
  SafeMode(const SafeMode &other) = delete;
  SafeMode& operator=(const SafeMode &other) = delete;

  void Start() override;
  void Stop() override;

  void set_ns(NameSpace* ns) {
    ns_ = ns;
  }

  void set_ha_state(HAStateBase* ha_state) {
    ha_state_ = ha_state;
  }

  uint64_t last_enter_time() const override {
    return last_enter_time_;
  }

  uint64_t last_leave_time() const override {
    return last_leave_time_;
  }

  bool IsOn() override;
  bool IsStartingUp() override;
  bool IsManual() override;

  void GetSafeModeTip(std::string *tip) override;

  // Called from ClientNamenodeProtocol
  // May change the state of safe mode
  Status SetSafeMode(::cloudfs::SafeModeActionProto action,
                     OperationsCategory op) override;

  // Called when finished scan meta after start
  void SetBlockTotal(int64_t total) override;

  // Called when there is some DN registered
  void AdjustSafeModeBlockTotals(int delta_safe, int delta_total) override;
  void IncrementSafeBlockCount(uint32_t replication) override;
  void DecrementSafeBlockCount(uint32_t replication) override;

 private:
  NameSpace* ns_{nullptr};
  HAStateBase* ha_state_{nullptr};
  std::shared_ptr<VRWLock> barrier_;
  std::shared_ptr<EditLogContextBase> edit_log_context_;

  enum class SafeModeState : int8_t {
    kOff = -2,
    kStartingUp = -1,
    kOn = 0,
    kInExtension = 1,
  };
  // std::mutex mutex_;
  cnetpp::concurrency::SpinLock mutex_;
  std::atomic<SafeModeState> state_;
  std::chrono::steady_clock::time_point extension_time_point_;

  int64_t last_report_time_ { 0 };

  RWSpinlock delta_rwlock_;
  std::map<std::thread::id, std::atomic<int64_t>> block_safe_delta_per_thread_;

  uint64_t last_enter_time_;
  uint64_t last_leave_time_;

  volatile int64_t block_total_;  // total number of blocks
  volatile int64_t block_safe_;  // Number of safe blocks.
  // Number of blocks needed to satisfy safe mode threshold condition
  volatile int64_t block_safe_target_;

  std::unique_ptr<cnetpp::concurrency::ThreadPool> monitor_;
  bool safe_mode_monitor_started_ { false };
  void StartSafeModeMonitor();
  void DoMonitorTask();
  void DoCheckModeTask();

  bool IsStartingUpInternal();

  void Enter();
  void DoEnter();
  void Leave();
  void DoLeave();
  bool NeedEnter();
  bool CanLeave();
  // Called when the number of blocks has changed
  // to check whether we need to enter safe mode or not
  void CheckMode();

  void SetManual() {
    FLAGS_safemode_extension_ms = INT32_MAX;
  }
  void SetBlockTotalInternal(int64_t total);

  void PrintStatusReport(const std::string& msg);
};

}  // namespace dancenn

#endif  // SAFEMODE_SAFEMODE_H_

