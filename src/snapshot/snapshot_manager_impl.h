#ifndef DANCENN_SNAPSHOT_MANAGER_IMPL_H
#define DANCENN_SNAPSHOT_MANAGER_IMPL_H

#include <unordered_map>

#include "base/read_write_lock.h"
#include "namespace/meta_storage.h"
#include "snapshot/snapshot_manager.h"

namespace dancenn {

using SnapshotRootMap = std::unordered_map<INodeID, std::string>;

class SnapshotManagerImpl : public SnapshotManager {
 public:
  SnapshotManagerImpl(const std::shared_ptr<MetaStorage>& meta_storage);
  ~SnapshotManagerImpl() override = default;

  void Launch() override;

  Status AllowSnapshot(INodeID id, const std::string& src) override;

  Status DisallowSnapshot(INodeID id, const std::string& src) override;

  Status GetSnapshotRoots(
      std::vector<std::pair<INodeID, std::string>>* snapshot_roots) override;

  Status CheckNestedSnapshot(const std::string& path) const override;

  Status CheckSnapshotForDelete(const std::string& path) const override;

  Status CheckSnapshotNumLimit(uint64_t snapshot_num) const override;

  Status AcquireSnapshotID(uint64_t* snapshot_id) override;

  void ReloadSnapshotID() override;

  void SetLimits(uint64_t max_snapshot_id,
                 uint64_t max_snapshot_num_on_each_root) {
    max_snapshot_id_ = max_snapshot_id;
    max_snapshot_num_on_each_root_ = max_snapshot_num_on_each_root;
  }

  const SnapshotRootMap& TestOnlySnapshotRootMap() const {
    return snapshot_root_map_;
  }

 private:
  // `sub` and `ancestor` should have been normalized
  static bool IsAncestor(const std::string& sub, const std::string& ancestor);

  static Status CheckPathValid(const std::string& path,
                               const std::string& info);

  // used to read snapshot related tables
  std::shared_ptr<MetaStorage> meta_storage_;

  mutable ReadWriteLock snapshot_root_map_lock_;
  SnapshotRootMap snapshot_root_map_;
  std::atomic<uint64_t> last_snapshot_id_{0};
  uint64_t max_snapshot_id_;
  uint64_t max_snapshot_num_on_each_root_;
};

}  // namespace dancenn

#endif  // DANCENN_SNAPSHOT_MANAGER_IMPL_H
