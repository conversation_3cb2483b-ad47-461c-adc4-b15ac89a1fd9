#include "snapshot/snapshot_manager_impl.h"

#include "base/path_util.h"

#include <absl/strings/substitute.h>

DECLARE_bool(enable_snapshot_feature);

namespace dancenn {

SnapshotManagerImpl::SnapshotManagerImpl(
    const std::shared_ptr<MetaStorage>& meta_storage)
    : meta_storage_(meta_storage) {
}

void SnapshotManagerImpl::Launch() {
  SetLimits((1 << kSnapshotIdBitWidth) - 1, kSnapshotMaxLimitDefault);

  std::unique_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
  snapshot_root_map_.clear();
  if (FLAGS_enable_snapshot_feature) {
    // if snapshot feature disabled, just work like no snapshot root exists
    meta_storage_->ScanSnapshotRoot([this](INodeID id, const std::string& path) {
      snapshot_root_map_.emplace(id, path);
      return true;
    });
  }
  ReloadSnapshotID();
  meta_storage_->DeleteNameSystemInfo(kNumSnapshotsKey);
}

Status SnapshotManagerImpl::AllowSnapshot(INodeID id, const std::string& src) {
  RETURN_NOT_OK(CheckPathValid(src, "AllowSnapshot"));

  std::unique_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
  snapshot_root_map_.emplace(id, src);
  VLOG(2) << "AllowSnapshot: INodeID=" << id << ", root=" << src
          << ", root_num=" << snapshot_root_map_.size();

  return Status::OK();
}

Status SnapshotManagerImpl::DisallowSnapshot(INodeID id,
                                             const std::string& src) {
  RETURN_NOT_OK(CheckPathValid(src, "DisallowSnapshot"));

  std::unique_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
  auto iter = snapshot_root_map_.find(id);
  if (iter != snapshot_root_map_.end()) {
    if (iter->second != src) {
      LOG(ERROR) << "Snapshot root mismatch, id=" << id
                 << ", user passed path=" << src
                 << ", in-cache path=" << iter->second;
    }
    snapshot_root_map_.erase(iter);
  }
  VLOG(2) << "DisallowSnapshot: INodeID=" << id << ", root=" << src
          << ", root_num=" << snapshot_root_map_.size();

  return Status::OK();
}

Status SnapshotManagerImpl::GetSnapshotRoots(
    std::vector<std::pair<INodeID, std::string>>* snapshot_roots) {
  snapshot_roots->clear();
  {
    std::shared_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
    snapshot_roots->reserve(snapshot_root_map_.size());
    for (const auto& elem : snapshot_root_map_) {
      snapshot_roots->emplace_back(elem.first, elem.second);
    }
  }
  auto comparator = [](const std::pair<INodeID, std::string>& l,
                       const std::pair<INodeID, std::string>& r) {
    return l.second < r.second || (l.second == r.second && l.first < r.first);
  };
  std::sort(snapshot_roots->begin(), snapshot_roots->end(), comparator);

  return Status::OK();
}

Status SnapshotManagerImpl::CheckNestedSnapshot(const std::string& path) const {
  RETURN_NOT_OK(CheckPathValid(path, "CheckNestedSnapshot"));

  std::shared_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
  for (const auto& elem : snapshot_root_map_) {
    if (IsAncestor(path, elem.second)) {
      return Status(JavaExceptions::kSnapshotException,
                    "Nested snapshottable directories not allowed: path=" +
                        path + ", the ancestor " + elem.second +
                        " is already a snapshottable directory.");
    }
    if (IsAncestor(elem.second, path)) {
      return Status(JavaExceptions::kSnapshotException,
                    "Nested snapshottable directories not allowed: path=" +
                        path + ", the subdirectory " + elem.second +
                        " is already a snapshottable directory.");
    }
  }

  return Status::OK();
}

Status SnapshotManagerImpl::CheckSnapshotForDelete(
    const std::string& path) const {
  std::shared_lock<ReadWriteLock> guard(snapshot_root_map_lock_);
  for (const auto& elem : snapshot_root_map_) {
    if (path == elem.second || IsAncestor(elem.second, path)) {
      return Status(JavaExceptions::kSnapshotException,
                    path + " can not be deleted or renamed, " + elem.second +
                        " is a snapshottable directory.");
    }
  }

  return Status::OK();
}

Status SnapshotManagerImpl::CheckSnapshotNumLimit(uint64_t snapshot_num) const {
  if (snapshot_num >= max_snapshot_num_on_each_root_) {
    return Status(
        JavaExceptions::kSnapshotException,
        absl::Substitute("Current root snapshot num $0 reach limit $1",
                         snapshot_num,
                         max_snapshot_num_on_each_root_));
  }
  return Status::OK();
}

Status SnapshotManagerImpl::AcquireSnapshotID(uint64_t* snapshot_id) {
  uint64_t cur_val;
  do {
    cur_val = last_snapshot_id_.load();
    if (cur_val >= max_snapshot_id_) {
      return Status(
          JavaExceptions::kSnapshotException,
          absl::Substitute("Snapshot id run out, last id $0 reach limit $1",
                           cur_val,
                           max_snapshot_id_));
    }
    *snapshot_id = cur_val + 1;
  } while (!std::atomic_compare_exchange_weak(
      &last_snapshot_id_, &cur_val, *snapshot_id));

  return Status::OK();
}

void SnapshotManagerImpl::ReloadSnapshotID() {
  std::string last_snapshot_id;
  if (meta_storage_->GetNameSystemInfo(kLastSnapshotIdKey, &last_snapshot_id)) {
    last_snapshot_id_.store(
        MetaStorage::DecodeInteger<uint64_t>(last_snapshot_id));
  }
}

bool SnapshotManagerImpl::IsAncestor(const std::string& sub,
                                     const std::string& ancestor) {
  if (sub.size() <= ancestor.size()) {
    return false;
  }
  return sub.rfind(ancestor, 0) != std::string::npos &&
         sub[ancestor.size()] == kSeparatorChar;
}

Status SnapshotManagerImpl::CheckPathValid(const std::string& path,
                                           const std::string& info) {
  // ensure path is normalized so that we can judge nested paths by string
  // prefix matching
  std::string normalized_path;
  if (UNLIKELY(!NormalizePath(path, "", &normalized_path) ||
               path != normalized_path)) {
    return Status(JavaExceptions::kInvalidPathException,
                  info + " found invalid path " + path);
  }

  return Status::OK();
}

}  // namespace dancenn
