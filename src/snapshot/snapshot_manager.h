#ifndef DANCENN_SNAPSHOT_MANAGER_H
#define DANCENN_SNAPSHOT_MANAGER_H

#include "base/status.h"
#include "namespace/inode.h"
#include "proto/generated/cloudfs/hdfs.pb.h"

namespace dancenn {

// Manage snapshot directories and their snapshots, only maintain in-memory
// data, disk data is maintained by Namespace.
// Path locks should not be added in this class.
class SnapshotManager {
 public:
  virtual ~SnapshotManager() = default;

  virtual void Launch() = 0;

  // allow creating snapshot on `src` which with inode `id`
  virtual Status AllowSnapshot(INodeID id, const std::string& src) = 0;

  // disallow creating snapshot on `src` which with inode `id`
  virtual Status DisallowSnapshot(INodeID id, const std::string& src) = 0;

  // output all Snapshottable dirs and corresponding inode ids
  virtual Status GetSnapshotRoots(
      std::vector<std::pair<INodeID, std::string>>* snapshot_roots) = 0;

  // return Status::OK iif adding snapshot root `path` won't lead to nested
  // snapshot
  virtual Status CheckNestedSnapshot(const std::string& path) const = 0;

  // return Status::OK iif path and its descendants is not snapshottable
  // TODO if snapshot num is 0, path should pass check, too. We need to cache
  // snapshot num and disallow snapshot automatically if needed and check no
  // nested snapshot path when rename
  virtual Status CheckSnapshotForDelete(const std::string& path) const = 0;

  // return Status::OK iff `snapshot_num` < limit
  // snapshot_num: the number of snapshots at one snapshot_root
  virtual Status CheckSnapshotNumLimit(uint64_t snapshot_num) const = 0;

  // return Status::OK iff there is available snapshot id
  virtual Status AcquireSnapshotID(uint64_t* snapshot_id) = 0;

  // reload in-memory last-snapshot-id
  // last-snapshot-id is beyond protection of dirtree-lock during standby
  // applying editlog (both logical and physical), so we reload it during
  // transit-to-active, instead of update in each editlog apply-callback.
  // see M-4679216655 for detail
  virtual void ReloadSnapshotID() = 0;
};

}

#endif  // DANCENN_SNAPSHOT_MANAGER_H
