//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef HA_HA_STATE_H_
#define HA_HA_STATE_H_

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "HAServiceProtocol.pb.h"  // NOLINT(build/include)
#include "base/closure.h"
#include "base/read_write_lock.h"
#include "base/two_step_vlock.h"
#include "edit/edit_log_context.h"
#include "edit/roller.h"
#include "edit/tailer.h"
#include "ha/ha_state_base.h"
#include "ha/operations.h"

namespace dancenn {

class NameSpace;
class SafeModeBase;
class VRWLock;

class HAState : public HAStateBase {
 public:
  HAState(std::shared_ptr<JavaRuntime> jvm,
          std::shared_ptr<EditLogContextBase> edit_ctx,
          std::shared_ptr<VRWLock> barrier,
          std::shared_ptr<cnetpp::http::HttpClient> http_client);
  ~HAState();
  HAState(const HAState &other) = delete;
  HAState& operator=(const HAState &other) = delete;

  void Stop();

  void set_ns(NameSpace* ns) {
    ns_ = ns;
  }

  void set_safemode(SafeModeBase* safemode) {
    safemode_ = safemode;
  }

  bool IsInitializing() override;
  void CompleteInitialization() override;

  void SetState(cloudfs::HAServiceStateProto next_state,
                Closure* rpc_done) override;
  cloudfs::HAServiceStateProto GetState() override;
  void SetObserverState();

  std::shared_ptr<dancenn::vunique_lock> PreTransition(bool add_lock) override;
  void ExitPreTransition() override;

  std::pair<Status, dancenn::vshared_lock> CheckOperation(
      OperationsCategory op) override;

  Status UnprotectedCheckOperation(OperationsCategory op) override;

  TwoStepVUniqueLock LockBarrierForHASwitcher() override;

  bool InTransition() override;

  bool ShouldPopulateReplicationQueues() override;

  bool IsActive() const override {
    return ha_state_ == cloudfs::ACTIVE;
  }

  // see EditLogContextBase::IsActiveInLease
  bool IsActiveInLease() const override {
    if (edit_log_context_) {
      return edit_log_context_->IsActiveInLease();
    }
    return false;
  }

  uint64_t last_enter_standby_time() const override {
    return last_enter_standby_time_;
  }

  uint64_t last_leave_standby_time() const override {
    return last_leave_standby_time_;
  }

  void EnterDestroyState() override {
    destroy_state_.store(true);
  }
  void ExitDestroyState() override {
    destroy_state_.store(false);
  }
  bool IsDestroyState() override {
    return destroy_state_.load();
  }

  EditLogConf::HAMode GetHAMode() override;
  bool IsTailerStopped() override;
  Status SwitchNonHAActiveToHAActive() override;
  Status SwitchHAActiveToNonHAActive() override;
  Status SwitchHAStandbyToNonHAActive() override;

 private:
  NameSpace* ns_ { nullptr };
  SafeModeBase* safemode_ { nullptr };
  std::shared_ptr<JavaRuntime> jvm_;
  std::shared_ptr<VRWLock> barrier_;
  std::shared_ptr<EditLogContextBase> edit_log_context_;
  std::shared_ptr<cnetpp::http::HttpClient> http_client_;

  std::mutex mutex_;

  uint64_t last_enter_standby_time_;
  uint64_t last_leave_standby_time_;

  std::shared_ptr<dancenn::vunique_lock> vlock_holder_{nullptr};

  bool is_in_transition_{false};

  std::atomic<cloudfs::HAServiceStateProto> ha_state_ {
    cloudfs::HAServiceStateProto::INITIALIZING
  };
  cloudfs::HARequestSource source_;

  std::unique_ptr<EditLogRoller> edit_log_roller_;
  std::shared_ptr<EditLogTailer> edit_log_tailer_;
  std::shared_ptr<Gauge> tailer_apply_assigner_pending_task_;
  std::shared_ptr<Gauge> tailer_apply_assigner_running_task_;

  void StartActiveServicesInternal();
  void StopActiveServicesInternal();
  void StartStandbyServicesInternal();
  void StopStandbyServicesInternal();
  void SetStateInternal(cloudfs::HAServiceStateProto state, Closure* done);

  std::shared_ptr<dancenn::vunique_lock> PreTransitionInternal(bool add_block);
  void ExitPreTransitionInternal();

  std::atomic<bool> destroy_state_{false};

  MetricID metrics_ha_switch_2s_time_;
  MetricID metrics_ha_switch_2a_time_;
  MetricID metrics_ha_catchup_standby_time_;
  MetricID metrics_ha_catchup_active_time_;
  MetricID metrics_ha_catchup_standby_gap_;
  MetricID metrics_ha_catchup_active_gap_;
};

}  // namespace dancenn

#endif  // HA_HA_STATE_H_

