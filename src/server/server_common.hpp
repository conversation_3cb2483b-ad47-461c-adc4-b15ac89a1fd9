// Copyright 2017 He <PERSON> <<EMAIL>>

#ifndef SERVER_SERVER_COMMON_HPP_
#define SERVER_SERVER_COMMON_HPP_

#include <cnetpp/base/log.h>
#include <cnetpp/concurrency/this_thread.h>
#include <fiu.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <pthread.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>

#include <csignal>
#include <iostream>
#include <memory>
#include <string>

#include "base/audit_logger.h"
#include "base/defer.h"
#include "base/edit_logger.h"
#include "base/metric_emitter_plugin.h"
#include "version.h"

DECLARE_string(metric_emitter_plugin_type);
DECLARE_string(audit_log_databus_channel);
DECLARE_string(edit_log_databus_channel);
DECLARE_bool(dancenn_observe_mode_on);

extern char** environ;

static void CnetppLog(cnetpp::base::Log::Level level, const char* msg) {
  switch (level) {
    case cnetpp::base::Log::Level::kDebug:
      DLOG(INFO) << msg;
      break;
    case cnetpp::base::Log::Level::kInfo:
      LOG(INFO) << msg;
      break;
    case cnetpp::base::Log::Level::kWarn:
      LOG(WARNING) << msg;
      break;
    case cnetpp::base::Log::Level::kError:
      LOG(ERROR) << msg;
      break;
    case cnetpp::base::Log::Level::kFatal:
      LOG(FATAL) << msg;
      break;
    default:
      LOG(FATAL) << "Unknown cnetpp log level: " << static_cast<int>(level)
                 << ", message is: " << msg << ". aborting...";
      abort();
  }
}

static void SetupCnetppLogger() {
  cnetpp::base::LOG.set_func(&CnetppLog);
}

struct ServerContext {
  sigset_t set;
  std::unique_ptr<dancenn::MetricEmitterPlugin> metric_emitter_;
};

static void InitEnvAndFlags(int argc,
                            char* argv[],
                            ServerContext* sc,
                            void (*failure_writer)(const char* data,
                                                   int size)) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::SetVersionString(
      dancenn::kReleaseVersion + "@" + dancenn::kVcsBuildPath);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (argc != 1) {
    std::cerr << "No argument is expected" << std::endl;
    exit(EXIT_FAILURE);
  }
  google::InitGoogleLogging(argv[0]);
  google::InstallFailureSignalHandler();
  google::InstallFailureWriter(failure_writer);
  // https://meego.feishu.cn/cfs/issue/detail/7415658?parentUrl=%2Fcfs%2FissueView%2F0lEPDzCGg
  // google::EnableLogCleaner(2);

  std::unique_ptr<dancenn::AuditLogger> audit_logger(
      new dancenn::DatabusAuditLogger(FLAGS_audit_log_databus_channel));
  dancenn::AuditLogger::Init(std::move(audit_logger));
  if(FLAGS_dancenn_observe_mode_on) {
    std::unique_ptr<dancenn::EditLogger> edit_logger(
        new dancenn::DatabusEditLogger(FLAGS_edit_log_databus_channel));
    dancenn::EditLogger::Init(std::move(edit_logger));
  }

  if (signal(SIGPIPE, SIG_IGN) == SIG_ERR) {
    LOG(FATAL) << "Failed to ignore SIGPIPE";
    exit(EXIT_FAILURE);
  }

  sigemptyset(&sc->set);
  sigaddset(&sc->set, SIGINT);
  sigaddset(&sc->set, SIGTERM);
  sigaddset(&sc->set, SIGHUP);
  int error = pthread_sigmask(SIG_BLOCK, &sc->set, NULL);
  if (error) {
    LOG(FATAL) << "pthread_sigmask() failed: " << error;
    exit(EXIT_FAILURE);
  }

  SetupCnetppLogger();

  sc->metric_emitter_ = dancenn::MetricEmitterPlugin::CreateMetricEmitter(
      FLAGS_metric_emitter_plugin_type);
  CHECK_NOTNULL(sc->metric_emitter_.get());
  CHECK(sc->metric_emitter_->Start())
    << "Failed to start metric emitter plugin";

  fiu_init(0);
}

static void InitEnvAndFlags(int argc, char* argv[], ServerContext* sc) {
  auto default_writer = [](const char* data, int size) {
    LOG(ERROR) << std::string(data, size);
    if (write(STDERR_FILENO, data, size) < 0) {
      // Ignore errors.
    }
  };
  InitEnvAndFlags(argc, argv, sc, default_writer);
}

static int AwaitTermination(const ServerContext &sc,
    std::function<void()> cleanup) {
  DEFER([&] {
    google::FlushLogFiles(google::INFO);
    sc.metric_emitter_->Stop();
    google::FlushLogFiles(google::INFO);
    cleanup();
    LOG(INFO) << "Byebye";
    google::FlushLogFiles(google::INFO);
    google::ShutdownGoogleLogging();
  });

  for (;;) {
    int signo;
    int error = sigwait(&sc.set, &signo);
    if (error) {
      if (error == EINTR) {
        continue;
      }
      LOG(FATAL) << "Failed to wait for signals: " << error;
      exit(EXIT_FAILURE);
    }
    switch (signo) {
      case 0:
        continue;
      case SIGCHLD:
        LOG(INFO) << "Received SIGCHLD signal, some child process exited.";
        sc.metric_emitter_->Stop();
        continue;
      case SIGINT:
      case SIGTERM:
      case SIGHUP:
        LOG(INFO) << "Stopping due to signal: " << strsignal(signo);
        break;
      default:
        LOG(INFO) << "Unknown signal: " << strsignal(signo);
        abort();
    }

    return EXIT_SUCCESS;
  }
}

#endif  // SERVER_SERVER_COMMON_HPP_
