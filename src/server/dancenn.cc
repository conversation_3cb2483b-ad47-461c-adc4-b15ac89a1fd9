// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "dancenn.h"

#include <aws/core/Aws.h>
#include <cnetpp/base/end_point.h>
#include <curl/curl.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <jemalloc/jemalloc.h>
#include <pthread.h>
#include <stdlib.h>

#include <iostream>
#include <memory>

#include "base/constants.h"
#include "base/logger_metrics.h"
#include "base/rpc_server_metrics.h"
#include "ha/ha_mode_transition_manager.h"
#include "rpc/rpc_server.h"
#include "server/server_common.hpp"
#include "service/acc_client_namenode_service.h"
#include "service/client_namenode_service.h"
#include "service/datanode_service.h"
#include "service/ha_service.h"
#include "service/namenode_service.h"
#include "service/service_meta.h"
#include "ufs/ufs_auth_conf.h"
#include "ufs/ufs_env.h"

DECLARE_string(listen_ip_address);

DECLARE_int32(client_rpc_port);
DECLARE_int32(client_rpc_backlog);
DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);
DECLARE_int32(client_veryslow_rpc_handler_count);
DECLARE_int32(client_veryveryslow_rpc_handler_count);
DECLARE_int32(client_recover_lease_rpc_handler_count);
DECLARE_int32(client_rpc_network_thread_count);
DECLARE_int32(client_rpc_tcp_send_buffer_size);
DECLARE_int32(client_rpc_tcp_recv_buffer_size);
DECLARE_int32(client_rpc_send_buffer_size);
DECLARE_int32(client_rpc_recv_buffer_size);

DECLARE_int32(datanode_rpc_port);
DECLARE_int32(datanode_rpc_backlog);
DECLARE_int32(datanode_normal_rpc_handler_count);
DECLARE_int32(datanode_slow_rpc_handler_count);
DECLARE_int32(datanode_veryslow_rpc_handler_count);
DECLARE_int32(datanode_rpc_network_thread_count);
DECLARE_int32(datanode_rpc_tcp_send_buffer_size);
DECLARE_int32(datanode_rpc_tcp_recv_buffer_size);
DECLARE_int32(datanode_rpc_send_buffer_size);
DECLARE_int32(datanode_rpc_recv_buffer_size);

DECLARE_int32(ha_rpc_port);
DECLARE_int32(ha_normal_rpc_handler_count);
DECLARE_int32(ha_slow_rpc_handler_count);
DECLARE_int32(ha_veryslow_rpc_handler_count);
DECLARE_int32(ha_rpc_network_thread_count);
DECLARE_int32(ha_rpc_tcp_send_buffer_size);
DECLARE_int32(ha_rpc_tcp_recv_buffer_size);
DECLARE_int32(ha_rpc_send_buffer_size);
DECLARE_int32(ha_rpc_recv_buffer_size);

DECLARE_string(nameservice);
DECLARE_string(namespace_meta_storage_path);

DECLARE_int32(http_port);
DECLARE_int32(http_client_worker_count);

DECLARE_string(namenode_startup_option);

DECLARE_string(java_zk_classpath);
DECLARE_string(java_classpath);
DECLARE_int32(java_heap_size_mb);

DECLARE_bool(retry_cache_enabled);
DECLARE_int32(runtime_check_interval_ms);

DECLARE_bool(security_block_access_token_enable);
DECLARE_string(security_key_manager_model);

DECLARE_int32(namespace_type);

DECLARE_int32(upload_monitor_fg_task_thread_count);
DECLARE_int32(blk_uploader_thread_count);

DECLARE_int32(ufs_syncengine_worker_num);
DECLARE_int32(ufs_syncengine_list_worker_num);
DECLARE_int32(ufs_syncengine_rename_worker_num);
DECLARE_int32(ufs_syncengine_delete_worker_num);
DECLARE_uint32(write_back_manager_worker_count);
DECLARE_uint32(ufs_event_handler_count);

DECLARE_uint32(dfs_meta_scanner_v2_worker_num);
DECLARE_int32(blk_delete_file_by_dn_evict_thread_count);
DECLARE_int32(ns_bg_worker_thread_count);

DECLARE_bool(enable_aws_trace_log);

DECLARE_uint32(dump_memory_prof_interval_ms);
DECLARE_bool(enable_fast_shutdown);

std::shared_ptr<dancenn::RpcServer::ServiceMap> GetCommonServices(
    std::shared_ptr<dancenn::DatanodeManager> datanode_manager,
    std::shared_ptr<dancenn::BlockManager> block_manager,
    std::shared_ptr<dancenn::NameSpace> ns,
    std::shared_ptr<dancenn::AccNamespace> acc_ns,
    dancenn::HAStateBase* ha_state,
    dancenn::SafeModeBase* safemode,
    dancenn::RetryCache* rc) {
  // ClientNameNodeService
  std::shared_ptr<dancenn::ServiceMeta> cli_sm;
  if (dancenn::NameSpace::IsAccMode()) {
    LOG(INFO) << "starting as acc mode.";
    auto cli_service = std::static_pointer_cast<google::protobuf::Service>(
        std::make_shared<dancenn::AccClientNamenodeService>(
            datanode_manager, block_manager, acc_ns, safemode, rc));

    cli_sm = std::make_shared<dancenn::ServiceMeta>(cli_service, 0);
    std::static_pointer_cast<dancenn::AccClientNamenodeService>(cli_service)
        ->SetMethodMetas(cli_sm);
  } else {
    LOG(INFO) << "starting as hdfs mode.";
    auto cli_service = std::static_pointer_cast<google::protobuf::Service>(
        std::make_shared<dancenn::ClientNamenodeService>(
            datanode_manager, block_manager, ns, safemode, rc));
    cli_sm = std::make_shared<dancenn::ServiceMeta>(cli_service, 0);
    std::static_pointer_cast<dancenn::ClientNamenodeService>(cli_service)
        ->SetMethodMetas(cli_sm);
  }

  // NameNodeService
  auto nn_service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<dancenn::NameNodeService>(
          datanode_manager, block_manager, ns, ha_state));
  auto nn_sm = std::make_shared<dancenn::ServiceMeta>(nn_service, 0);
  std::static_pointer_cast<dancenn::NameNodeService>(nn_service)
      ->SetMethodMetas(nn_sm);

  // DataNodeService
  auto dn_service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<dancenn::DatanodeService>(
          datanode_manager, block_manager, ns, ha_state));

  auto dn_sm = std::make_shared<dancenn::ServiceMeta>(dn_service, 0);
  std::static_pointer_cast<dancenn::DatanodeService>(dn_service)
      ->SetMethodMetas(dn_sm);

  // HAService
  auto ha_service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<dancenn::HAService>(ha_state, safemode));

  auto ha_sm = std::make_shared<dancenn::ServiceMeta>(ha_service, 0);

  auto services = std::make_shared<dancenn::RpcServer::ServiceMap>(
      dancenn::RpcServer::ServiceMap{
          {"org.apache.hadoop.hdfs.protocol.ClientProtocol", cli_sm},
          {"org.apache.hadoop.hdfs.server.protocol.NamenodeProtocol", nn_sm},
          {"org.apache.hadoop.hdfs.server.protocol.DatanodeProtocol", dn_sm},

          {"org.apache.hadoop.ha.HAServiceProtocol", ha_sm}});

  return services;
}

std::shared_ptr<dancenn::RpcServer> LaunchNNServices(
    int service_port,
    dancenn::RpcServerOptions const& options,
    std::shared_ptr<dancenn::RpcServer::ServiceMap> services) {
  auto server = std::make_shared<dancenn::RpcServer>();
  cnetpp::base::EndPoint ep(FLAGS_listen_ip_address, service_port);
  server->RegisterService(services);
  server->Launch(ep, options);
  return server;
}

std::shared_ptr<dancenn::RpcServer> LaunchClientNamenodeProtocol(
    std::shared_ptr<dancenn::RpcServer::ServiceMap> services) {
  dancenn::RpcServerOptions options;
  options.set_backlog(FLAGS_client_rpc_backlog);
  options.set_send_buffer_size(FLAGS_client_rpc_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_client_rpc_recv_buffer_size);
  options.set_tcp_receive_buffer_size(FLAGS_client_rpc_tcp_recv_buffer_size);
  options.set_tcp_send_buffer_size(FLAGS_client_rpc_tcp_send_buffer_size);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kNormal,
                            FLAGS_client_normal_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kSlow,
                            FLAGS_client_slow_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kVerySlow,
                            FLAGS_client_veryslow_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kVeryVerySlow,
                            FLAGS_client_veryveryslow_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kRecoverLease,
                            FLAGS_client_recover_lease_rpc_handler_count);
  options.set_network_thread_count(FLAGS_client_rpc_network_thread_count);
  options.set_enable_one_threadpool_per_network_thread_for_normal(true);
  options.set_enable_slow_pool_editlog_committer_optimization(true);
  options.set_enable_veryslow_pool_editlog_committer_optimization(true);
  options.set_rpc_server_name("cli");

  return LaunchNNServices(FLAGS_client_rpc_port, options, services);
}

std::shared_ptr<dancenn::RpcServer> LaunchDatanodeProtocol(
    std::shared_ptr<dancenn::RpcServer::ServiceMap> services) {
  dancenn::RpcServerOptions options;
  options.set_backlog(FLAGS_datanode_rpc_backlog);
  options.set_send_buffer_size(FLAGS_datanode_rpc_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_datanode_rpc_recv_buffer_size);
  options.set_tcp_receive_buffer_size(FLAGS_datanode_rpc_tcp_recv_buffer_size);
  options.set_tcp_send_buffer_size(FLAGS_datanode_rpc_tcp_send_buffer_size);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kNormal,
                            FLAGS_datanode_normal_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kSlow,
                            FLAGS_datanode_slow_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kVerySlow,
                            FLAGS_datanode_veryslow_rpc_handler_count);
  options.set_network_thread_count(FLAGS_datanode_rpc_network_thread_count);
  options.set_enable_normal_pool_editlog_committer_optimization(true);
  options.set_rpc_server_name("dn");

  return LaunchNNServices(FLAGS_datanode_rpc_port, options, services);
}

std::shared_ptr<dancenn::RpcServer> LaunchHAServiceProtocol(
    std::shared_ptr<dancenn::RpcServer::ServiceMap> services) {
  auto server = std::make_shared<dancenn::RpcServer>();
  dancenn::RpcServerOptions options;
  options.set_send_buffer_size(FLAGS_ha_rpc_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_ha_rpc_recv_buffer_size);
  options.set_tcp_receive_buffer_size(FLAGS_ha_rpc_tcp_recv_buffer_size);
  options.set_tcp_send_buffer_size(FLAGS_ha_rpc_tcp_send_buffer_size);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kNormal,
                            FLAGS_ha_normal_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kSlow,
                            FLAGS_ha_slow_rpc_handler_count);
  options.set_handler_count(dancenn::MethodMeta::MethodType::kVerySlow,
                            FLAGS_ha_veryslow_rpc_handler_count);
  options.set_network_thread_count(FLAGS_ha_rpc_network_thread_count);
  options.set_rpc_server_name("ha");

  return LaunchNNServices(FLAGS_ha_rpc_port, options, services);
}

std::shared_ptr<dancenn::JavaRuntime> CreateJVM() {
  // To handle read and write operations for edit logs from IPv6-only Zookeeper
  // and Bookkeeper, use the jars located in /opt/tiger/cfs_zk_deploy. Since
  // there are many jars, avoid adding them directly to FLAGS_java_classpath.
  // Instead, utilize a new flag, FLAGS_java_zk_classpath.
  return std::make_shared<dancenn::JavaRuntime>(FLAGS_java_zk_classpath + ":" +
                                                    FLAGS_java_classpath,
                                                FLAGS_java_heap_size_mb);
}

static std::shared_ptr<dancenn::RpcServer> client_server;
static std::shared_ptr<dancenn::RpcServer> datanode_server;
static std::shared_ptr<dancenn::RpcServer> ha_server;
static std::unique_ptr<dancenn::DancennHttpServer> http_server;

void ShutDownServers() {
  static std::atomic<bool> flag{false};
  bool expected = false;
  if (flag.compare_exchange_strong(expected, true)) {
    LOG(INFO) << "Shut down Rpc server...";
    if (ha_server) {
      LOG(INFO) << "Shut down HA Rpc Server...";
      ha_server->FastShutdown();
    }
    if (client_server) {
      LOG(INFO) << "Shut down ClientNamenode Rpc Server...";
      client_server->FastShutdown();
    }
    if (datanode_server) {
      LOG(INFO) << "Shut down Datanode Rpc Server...";
      datanode_server->FastShutdown();
    }
    if (http_server) {
      LOG(INFO) << "Shut down Http Server...";
      http_server->FastShutdown();
    }
  }
  LOG(INFO) << "Shutdown servers done.";
}

int main(int argc, char* argv[]) {
#if defined(OS_LINUX)
  // Dump memory before startup if MALLOC_CONF is set
  const char* malloc_env = getenv("MALLOC_CONF");
  if (malloc_env == nullptr || strlen(malloc_env) == 0) {
    LOG(INFO) << "MALLOC_CONF is not detected, set MALLOC_CONF="
              << dancenn::kMallocConf
              << " to enable dump memory before startup";
  } else {
    LOG(INFO) << "MALLOC_CONF detected: " << malloc_env
              << ", start dump memory";
    std::thread debug_memory([]() {
      while (true) {
        LOG(INFO) << "Start dump memory";
        int err = mallctl("prof.dump", NULL, NULL, NULL, 0);
        LOG(INFO) << "Finish dump memory, errno=" << err;
        std::this_thread::sleep_for(
            std::chrono::milliseconds(FLAGS_dump_memory_prof_interval_ms));
      }
    });
    debug_memory.detach();
  }
#endif

  // Define customer failure writer
  // Showdown all http/rpc server if first called to speed up zkfc failover
  // detect when dancenn abort for reason that receiving below signals:
  // SIGSEGV, SIGILL, SIGFPE, SIGABRT, SIGBUS, and SIGTERM.
  auto writer = [](const char* data, int size) {
    if (FLAGS_enable_fast_shutdown) {
      ShutDownServers();
    }
    // dump to ERROR file
    LOG(ERROR) << std::string(data, size);
    // dump to std_err
    if (write(STDERR_FILENO, data, size) < 0) {
      // Ignore errors.
    }
  };

  ServerContext sc;
  InitEnvAndFlags(argc, argv, &sc, writer);

  if (FLAGS_enable_fast_shutdown) {
    // called after LOG(FATAL) or CHECK failed
    google::InstallFailureFunction([]() {
      ShutDownServers();
      LOG(INFO) << "shutdown servers done, now abort.";
      abort();
    });
  }

  CHECK(!FLAGS_nameservice.empty())
      << " nameservice is empty, please check the config file";

  dancenn::MetricsCenter::set_global_dynamic_tag_injector([]() -> std::string {
    return "nameservice=" + FLAGS_nameservice + "#active=false";
  });

  dancenn::DanceNNRuntime runtime;

  // Step 1. Construct
  runtime.runtime_monitor = std::make_unique<dancenn::RuntimeMonitor>(
      FLAGS_runtime_check_interval_ms);

  // DN and HA slow handler never used now, but to keep the code consistency, we
  // still create channels for them
  runtime.jvm = CreateJVM();
  int max_channels_num = 0;
  max_channels_num += FLAGS_client_slow_rpc_handler_count;
  max_channels_num += FLAGS_client_veryslow_rpc_handler_count;
  max_channels_num += FLAGS_datanode_normal_rpc_handler_count;
  max_channels_num += FLAGS_upload_monitor_fg_task_thread_count;
  max_channels_num += FLAGS_blk_uploader_thread_count;
  max_channels_num += FLAGS_dfs_meta_scanner_v2_worker_num;
  max_channels_num += FLAGS_blk_delete_file_by_dn_evict_thread_count;
  max_channels_num += FLAGS_ns_bg_worker_thread_count;
  if (dancenn::NameSpace::IsAccMode()) {
    max_channels_num += FLAGS_ufs_syncengine_worker_num;
    max_channels_num += FLAGS_ufs_syncengine_list_worker_num;
    max_channels_num += FLAGS_ufs_syncengine_rename_worker_num;
    max_channels_num += FLAGS_ufs_syncengine_delete_worker_num;
    max_channels_num += FLAGS_write_back_manager_worker_count;
    max_channels_num += FLAGS_ufs_event_handler_count;
  }

  runtime.edit_log_ctx = std::make_shared<dancenn::HAFlexibleEditLogContext>(
      runtime.jvm.get(), max_channels_num);

  // https://curl.se/libcurl/c/curl_easy_init.html
  // If you did not already call curl_global_init,
  // curl_easy_init does it automatically.
  // This may be lethal in multi-threaded cases,
  // since curl_global_init is not thread-safe,
  // and it may result in resource problems
  // because there is no corresponding cleanup.
  CHECK_EQ(curl_global_init(CURL_GLOBAL_ALL), 0)
      << "If this function returns non-zero, something went wrong and you "
         "cannot use the other curl functions.";
  // For delete tos objects.
  Aws::SDKOptions aws_sdk_options;
  aws_sdk_options.httpOptions.installSigPipeHandler = true;
  if (FLAGS_enable_aws_trace_log) {
    aws_sdk_options.loggingOptions.logLevel =
        Aws::Utils::Logging::LogLevel::Trace;
  }
  Aws::InitAPI(aws_sdk_options);

  {
    auto ufs_s = dancenn::UfsAuthConf::Instance().Init();
    if (!ufs_s.IsOK()) {
      LOG(FATAL) << "Failed to init UfsAuthConf, exiting...";
    }
  }

  auto data_centers = std::make_shared<dancenn::DataCenters>();

  runtime.data_centers = std::make_shared<dancenn::DataCenters>();

  runtime.datanode_manager = std::make_shared<dancenn::DatanodeManager>();

  runtime.block_manager =
      std::make_shared<dancenn::BlockManager>(runtime.edit_log_ctx);

  runtime.key_manager = dancenn::KeyManagerFactory::Create(
      FLAGS_security_key_manager_model, runtime.datanode_manager);
  runtime.key_manager->Start();

  runtime.job_manager = std::make_shared<dancenn::JobManager>(
      runtime.datanode_manager, runtime.block_manager);

  runtime.block_manager->set_job_manager(runtime.job_manager);

  // Used when roll edit log, switch ha state or switch safe mode state
  runtime.barrier =
      std::make_shared<dancenn::VRWLock>(FLAGS_client_slow_rpc_handler_count +
                                         FLAGS_client_normal_rpc_handler_count);
  runtime.safemode = std::make_shared<dancenn::SafeMode>(runtime.edit_log_ctx,
                                                         runtime.barrier);

  runtime.http_client = std::make_shared<cnetpp::http::HttpClient>();
  cnetpp::http::HttpClientOptions options;
  options.set_worker_count(FLAGS_http_client_worker_count);
  if (!runtime.http_client->Launch(options)) {
    LOG(FATAL) << "Failed to launch http client, exiting...";
  }

  runtime.ha_state = std::make_shared<dancenn::HAState>(
      runtime.jvm, runtime.edit_log_ctx, runtime.barrier, runtime.http_client);

  dancenn::MetricsCenter::set_global_dynamic_tag_injector(
      [ha_state = runtime.ha_state]() -> std::string {
        if (ha_state->GetState() == cloudfs::ACTIVE) {
          return "nameservice=" + FLAGS_nameservice + "#active=true";
        } else {
          return "nameservice=" + FLAGS_nameservice + "#active=false";
        }
      });

  if (!FLAGS_dancenn_observe_mode_on) {
    runtime.rc = std::make_unique<dancenn::RetryCache>();
  }

  runtime.op_task_manager = std::make_shared<dancenn::OpTaskManager>(
      runtime.datanode_manager, runtime.ha_state);

  runtime.ufs_env = dancenn::UfsEnv::Create();

  if (dancenn::NameSpace::IsAccMode()) {
    // by default only one ufs
    auto ufs_config = runtime.ufs_env->CreateUfsConfig();
    auto ufs = runtime.ufs_env->CreateUfs(ufs_config);

    runtime.ufs_env->AddUfs(ufs);

    runtime.acc_namespace =
        std::make_shared<dancenn::AccNamespace>(runtime.ufs_env, ufs);

    auto nsid_to_pufs_name_cb = runtime.acc_namespace->NsIdToPufsNameCB();
    runtime.job_manager->SetNsIdToPufsNameCB(nsid_to_pufs_name_cb);
  }

  runtime.ns =
      std::make_shared<dancenn::NameSpace>(FLAGS_namespace_meta_storage_path,
                                           runtime.edit_log_ctx,
                                           runtime.block_manager,
                                           runtime.datanode_manager,
                                           runtime.job_manager,
                                           runtime.data_centers,
                                           runtime.ufs_env,
                                           runtime.rc.get());
  auto ha_mode_transition_manager =
      std::make_unique<dancenn::HAModeTransitionManager>(
          runtime.ha_state.get());
  ha_mode_transition_manager->Start();

  // Block Access Token
  if (FLAGS_security_block_access_token_enable) {
    runtime.block_token_secret_manager =
        std::make_shared<dancenn::BlockTokenSecretManager>(
            runtime.ns->blockpool_id(), runtime.key_manager);
  }

  runtime.status_monitor =
      std::make_unique<dancenn::StatusMonitor>(runtime.block_manager.get());

  // Step 2. InjectDependency
  runtime.datanode_manager->RefreshDatanodeMachineInfo();
  runtime.InjectDependency();

  // Step 3. Run Service
  runtime.Start();

  // Step 4. Run Server
  auto services = GetCommonServices(runtime.datanode_manager,
                                    runtime.block_manager,
                                    runtime.ns,
                                    runtime.acc_namespace,
                                    runtime.ha_state.get(),
                                    runtime.safemode.get(),
                                    runtime.rc.get());

  client_server = LaunchClientNamenodeProtocol(services);

  datanode_server = LaunchDatanodeProtocol(services);

  http_server = std::make_unique<dancenn::DancennHttpServer>(&runtime);
  cnetpp::base::EndPoint http_ep(FLAGS_listen_ip_address, FLAGS_http_port);
  http_server->Launch(http_ep);

  // Start Standby after HTTP available
  auto state = runtime.ha_state->GetState();
  CHECK_EQ(state, cloudfs::HAServiceStateProto::INITIALIZING);
  runtime.ha_state->CompleteInitialization();

  auto ha_server = LaunchHAServiceProtocol(services);

  std::unique_ptr<dancenn::RpcServerMetrics> rpc_server_metrics(
      new dancenn::RpcServerMetrics(
          *client_server, *datanode_server, *ha_server));

  LOG(INFO) << "Dancenn started, version: " << DANCENN_VERSION;

  // Step 5. Stop
  return AwaitTermination(sc, [&]() {
    rpc_server_metrics.reset();
    http_server->Shutdown();
    http_server.reset();
    client_server->FastShutdown();
    client_server.reset();
    datanode_server->FastShutdown();
    datanode_server.reset();
    ha_server->FastShutdown();
    ha_server.reset();
    ha_mode_transition_manager->Stop();
    ha_mode_transition_manager.reset();
    services.reset();

    runtime.Stop();

    // Release edit_log_ctx.
    dancenn::MetricsCenter::set_global_dynamic_tag_injector(
        []() -> std::string { return ""; });

    Aws::ShutdownAPI(aws_sdk_options);
    // https://curl.se/libcurl/c/curl_global_cleanup.html
    // You should call curl_global_cleanup once for each call you make to
    // curl_global_init, after you are done using libcurl.
    curl_global_cleanup();
  });
}
