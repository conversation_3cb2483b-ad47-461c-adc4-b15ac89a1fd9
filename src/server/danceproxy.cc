// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <cnetpp/base/end_point.h>
#include <cnetpp/http/http_server.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <memory>

#include "server/server_common.hpp"
#include "rpc/rpc_client_options.h"
#include "rpc/rpc_server.h"
#include "http/danceproxy_http_server.h"
#include "proxy/block_pool_registry.h"
#include "proxy/frozen_directory_manager.h"
#include "proxy/storage_policy_ttl_manager.h"
#include "proxy/mounts_manager.h"
#include "proxy/path_team_space_quota_manager.h"
#include "proxy/proxy_throttler.h"
#include "proxy/upstream_manager.h"
#include "service/danceproxy_service.h"

DECLARE_int32(danceproxy_rpc_port);
DECLARE_int32(danceproxy_rpc_backlog);
DECLARE_int32(danceproxy_rpc_handler_count);
DECLARE_int32(danceproxy_rpc_network_thread_count);
DECLARE_int32(danceproxy_rpc_send_buffer_size);
DECLARE_int32(danceproxy_rpc_recv_buffer_size);
DECLARE_int32(http_port);

DECLARE_string(danceproxy_rpc_default_user);
DECLARE_string(danceproxy_rpc_default_client_id);
DECLARE_int32(danceproxy_rpc_max_open_connections_per_user_and_fs);
DECLARE_int32(danceproxy_rpc_max_pending_calls_per_user_and_fs);

std::shared_ptr<dancenn::RpcServer> LaunchClientNamenodeProtocol(
    std::shared_ptr<dancenn::UpstreamManager> upstream_manager,
    std::shared_ptr<dancenn::MountsManager> mounts_manager,
    std::shared_ptr<dancenn::PathTeamSpaceQuotaManager> quota_manager,
    std::shared_ptr<dancenn::FrozenDirectoryManager> frozen_directory_manager,
    std::shared_ptr<dancenn::StoragePolicyTTLManager> storage_policy_ttl_manager,
    std::shared_ptr<dancenn::ProxyThrottler> throttler,
    std::shared_ptr<dancenn::BlockPoolRegistry> block_pool_registry) {
  auto server = std::make_shared<dancenn::RpcServer>();
  cnetpp::base::EndPoint ep("0.0.0.0", FLAGS_danceproxy_rpc_port);
  dancenn::RpcServerOptions options;
  options.set_send_buffer_size(FLAGS_danceproxy_rpc_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_danceproxy_rpc_recv_buffer_size);
  options.set_handler_count(FLAGS_danceproxy_rpc_handler_count);
  options.set_network_thread_count(FLAGS_danceproxy_rpc_network_thread_count);
  options.set_rpc_server_name("rpcsrv");

  auto service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<dancenn::DanceproxyService>(upstream_manager,
                                                   mounts_manager,
                                                   quota_manager,
                                                   frozen_directory_manager,
                                                   storage_policy_ttl_manager,
                                                   throttler,
                                                   block_pool_registry));
  auto sm = std::make_shared<dancenn::ServiceMeta>(service, 0);
  server->RegisterService("org.apache.hadoop.hdfs.protocol.ClientProtocol", sm);
  server->Launch(ep, options);
  return server;
}

std::shared_ptr<dancenn::HttpServer> LaunchHttpServer(
    std::shared_ptr<dancenn::MountsManager> mm,
    std::shared_ptr<dancenn::PathTeamSpaceQuotaManager> qm,
    std::shared_ptr<dancenn::FrozenDirectoryManager> fdm,
    std::shared_ptr<dancenn::StoragePolicyTTLManager> sptm,
    std::shared_ptr<dancenn::ProxyThrottler> throttler) {
  auto http_server = std::make_shared<dancenn::DanceproxyHttpServer>();
  http_server->set_mounts_manager(mm);
  http_server->set_path_team_space_quota_manager(qm);
  http_server->set_frozen_directory_manager(fdm);
  http_server->set_storage_policy_ttl_manager(sptm);
  http_server->set_throttler(throttler);
  cnetpp::base::EndPoint http_ep("0.0.0.0", FLAGS_http_port);
  http_server->Launch(http_ep);
  return std::static_pointer_cast<dancenn::HttpServer>(http_server);
}

int main(int argc, char* argv[]) {
  ServerContext sc;
  InitEnvAndFlags(argc, argv, &sc);

  auto tcp_client = std::make_shared<cnetpp::tcp::TcpClient>();
  if (!tcp_client->Launch("dpcli")) {
    LOG(FATAL) << "Failed to launch tcp client dpcli: "
               << cnetpp::concurrency::ThisThread::GetLastErrorString();
  }

  dancenn::RpcClientOptions options;
  options.set_user(FLAGS_danceproxy_rpc_default_user);
  options.set_client_id(FLAGS_danceproxy_rpc_default_client_id);
  options.set_protocol_name(
      "org.apache.hadoop.hdfs.protocol.ClientProtocol");
  options.set_protocol_version(1);
  options.set_max_pending_calls_per_user_and_fs(
      FLAGS_danceproxy_rpc_max_pending_calls_per_user_and_fs);
  options.set_max_open_connections_per_user_and_fs(
      FLAGS_danceproxy_rpc_max_open_connections_per_user_and_fs);

  auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("dphdl");
  handlers->Start();

  auto upstream_manager = std::make_shared<dancenn::UpstreamManager>(tcp_client,
                                                                     options,
                                                                     handlers);

  auto quota_manager =
      std::make_shared<dancenn::PathTeamSpaceQuotaManager>(upstream_manager);
  quota_manager->Init();

  auto frozen_directory_manager =
      std::make_shared<dancenn::FrozenDirectoryManager>(upstream_manager);
  frozen_directory_manager->Init();

  auto storage_policy_ttl_manager =
      std::make_shared<dancenn::StoragePolicyTTLManager>(upstream_manager);
  storage_policy_ttl_manager->Init();

  auto throttler =
      std::make_shared<dancenn::ProxyThrottler>(upstream_manager);
  throttler->Init();

  auto block_pool_registry =
      std::make_shared<dancenn::BlockPoolRegistry>(upstream_manager);

  auto mounts_manager = std::make_shared<dancenn::MountsManager>();
  mounts_manager->RegisterAfterInstallCallback([block_pool_registry] () {
    block_pool_registry->RefreshBlockPools();
  });
  mounts_manager->Init(upstream_manager);
  mounts_manager->WaitUntilInstalled();

  auto rpc_server = LaunchClientNamenodeProtocol(upstream_manager,
                                                 mounts_manager,
                                                 quota_manager,
                                                 frozen_directory_manager,
                                                 storage_policy_ttl_manager,
                                                 throttler,
                                                 block_pool_registry);
  auto http_server = LaunchHttpServer(mounts_manager,
                                      quota_manager,
                                      frozen_directory_manager,
                                      storage_policy_ttl_manager,
                                      throttler);

  return AwaitTermination(sc, [&] () {
    http_server->Shutdown();
    rpc_server->Shutdown();

    quota_manager->Stop();

    if (mounts_manager) {
      mounts_manager->Stop();
    }

    if (upstream_manager) {
      upstream_manager->Stop();
    }
  });
}

