// Copyright 2017 He <PERSON>yi <<EMAIL>>

#include <service/placement_driver_service.h>
#include <rpc/rpc_server.h>

#include <stdlib.h>
#include <pthread.h>

#include <cnetpp/base/end_point.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <iostream>
#include <memory>

#include "datanode_manager/datanode_info.h"
#include "server/server_common.hpp"

DECLARE_int32(placement_driver_rpc_port);
DECLARE_int32(placement_driver_rpc_backlog);
DECLARE_int32(placement_driver_rpc_handler_count);
DECLARE_int32(placement_driver_rpc_network_thread_count);
DECLARE_int32(placement_driver_rpc_tcp_send_buffer_size);
DECLARE_int32(placement_driver_rpc_tcp_recv_buffer_size);
DECLARE_int32(placement_driver_rpc_send_buffer_size);
DECLARE_int32(placement_driver_rpc_recv_buffer_size);

std::shared_ptr<dancenn::RpcServer> LaunchPlacementDriverProtocol() {
  auto server = std::make_shared<dancenn::RpcServer>();
  cnetpp::base::EndPoint ep1("0.0.0.0", FLAGS_placement_driver_rpc_port);
  dancenn::RpcServerOptions options;
  options.set_backlog(FLAGS_placement_driver_rpc_backlog);
  options.set_send_buffer_size(FLAGS_placement_driver_rpc_send_buffer_size);
  options.set_receive_buffer_size(FLAGS_placement_driver_rpc_recv_buffer_size);
  options.set_tcp_receive_buffer_size(
      FLAGS_placement_driver_rpc_tcp_recv_buffer_size);
  options.set_tcp_send_buffer_size(
      FLAGS_placement_driver_rpc_tcp_send_buffer_size);
  options.set_handler_count(FLAGS_placement_driver_rpc_handler_count);
  options.set_network_thread_count(
      FLAGS_placement_driver_rpc_network_thread_count);
  options.set_rpc_server_name("pd");

  auto service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<dancenn::PlacementDriverService>());
  auto sm = std::make_shared<dancenn::ServiceMeta>(service, 0);

  server->RegisterService(
      "org.apache.hadoop.hdfs.protocol.PlacementDriverProtocol", sm);
  server->Launch(ep1, options);
  return server;
}

int main(int argc, char* argv[]) {
  ServerContext sc;
  InitEnvAndFlags(argc, argv, &sc);
  auto pd_server = LaunchPlacementDriverProtocol();
  return AwaitTermination(sc, [&] () {
    pd_server->Shutdown();
  });
}
