#pragma once

#include <cnetpp/http/http_client.h>

#include <memory>

#include "base/java.h"
#include "base/logger_metrics.h"
#include "base/retry_cache.h"
#include "base/runtime_monitor.h"
#include "base/status.h"
#include "base/vlock.h"
#include "datanode_manager/data_centers.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/edit_log_context.h"
#include "edit/edit_log_context_base.h"
#include "ha/ha_state.h"
#include "http/dancenn_http_server.h"
#include "job_manager/job_manager.h"
#include "namespace/namespace.h"
#include "op_task/op_task_manager.h"
#include "safemode/safemode.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "ufs/ufs_env.h"

DECLARE_bool(security_block_access_token_enable);
DECLARE_bool(dancenn_observe_mode_on);

namespace dancenn {

class DanceNNRuntime {
 public:
  DanceNNRuntime() = default;
  ~DanceNNRuntime() = default;

  void InjectDependency() {
    CHECK_NOTNULL(runtime_monitor);
    CHECK_NOTNULL(jvm);
    CHECK_NOTNULL(edit_log_ctx);
    CHECK_NOTNULL(data_centers);
    CHECK_NOTNULL(datanode_manager);
    CHECK_NOTNULL(block_manager);
    CHECK_NOTNULL(key_manager);
    CHECK_NOTNULL(job_manager);
    CHECK_NOTNULL(barrier);
    CHECK_NOTNULL(safemode);
    CHECK_NOTNULL(http_client);
    CHECK_NOTNULL(ha_state);
    if (!FLAGS_dancenn_observe_mode_on) {
      CHECK_NOTNULL(rc);
    }
    CHECK_NOTNULL(op_task_manager);
    if (FLAGS_namespace_type != cloudfs::NamespaceType::LOCAL) {
      CHECK_NOTNULL(ufs_env);
    }
    if (dancenn::NameSpace::IsAccMode()) {
      CHECK_NOTNULL(acc_namespace);
    }
    CHECK_NOTNULL(ns);
    if (FLAGS_security_block_access_token_enable) {
      CHECK_NOTNULL(block_token_secret_manager);
    }
    CHECK_NOTNULL(status_monitor);

    safemode->set_ha_state(ha_state.get());
    ha_state->set_safemode(safemode.get());

    if (block_token_secret_manager) {
      ns->set_block_token_secret_manager(block_token_secret_manager);
    }

    ns->set_key_manager(key_manager);
    ns->set_safemode(safemode.get());
    ns->set_ha_state(ha_state.get());
    safemode->set_ns(ns.get());
    ha_state->set_ns(ns.get());

    block_manager->set_ha_state(ha_state.get());
    block_manager->set_safemode(safemode.get());
    block_manager->set_ns(ns.get());

    datanode_manager->set_block_manager(block_manager.get());
    datanode_manager->set_op_task_manager(op_task_manager.get());

    job_manager->set_ns(ns);

    if (ufs_env) {
      ufs_env->SetNS(ns);
    }
    if (dancenn::NameSpace::IsAccMode()) {
      acc_namespace->SetNS(ns);
    }
  }

#define INJECT_DEPENDENCY_TEST(a, b, c)   \
  if ((a != nullptr) && (b != nullptr)) { \
    c;                                    \
  }

  void InjectDependency4Test() {
    // TODO(xiong): implement it
    // ha_state and safemode have set_xx() API not in API XXXBase
    // so MockXXX has no such API
    HAStateBase* ha_state_base = ha_state != nullptr
                                     ? (HAStateBase*)ha_state.get()
                                     : (HAStateBase*)mock_ha_state.get();
    SafeModeBase* safemode_base = safemode != nullptr
                                      ? (SafeModeBase*)safemode.get()
                                      : (SafeModeBase*)mock_safemode.get();

    INJECT_DEPENDENCY_TEST(
        safemode, ha_state_base, safemode->set_ha_state(ha_state_base));
    INJECT_DEPENDENCY_TEST(
        ha_state, safemode_base, ha_state->set_safemode(safemode_base));

    INJECT_DEPENDENCY_TEST(safemode, ns, safemode->set_ns(ns.get()));
    INJECT_DEPENDENCY_TEST(ha_state, ns, ha_state->set_ns(ns.get()));

    INJECT_DEPENDENCY_TEST(
        ns,
        block_token_secret_manager,
        ns->set_block_token_secret_manager(block_token_secret_manager));

    INJECT_DEPENDENCY_TEST(ns, key_manager, ns->set_key_manager(key_manager));
    INJECT_DEPENDENCY_TEST(ns, safemode_base, ns->set_safemode(safemode_base));
    INJECT_DEPENDENCY_TEST(ns, ha_state_base, ns->set_ha_state(ha_state_base));

    INJECT_DEPENDENCY_TEST(block_manager,
                           ha_state_base,
                           block_manager->set_ha_state(ha_state_base));
    INJECT_DEPENDENCY_TEST(block_manager,
                           safemode_base,
                           block_manager->set_safemode(safemode_base));
    INJECT_DEPENDENCY_TEST(block_manager,
                           edit_log_ctx,
                           block_manager->TestOnlySetEditLogCtx(edit_log_ctx));
    INJECT_DEPENDENCY_TEST(block_manager, ns, block_manager->set_ns(ns.get()));

    INJECT_DEPENDENCY_TEST(
        datanode_manager,
        block_manager,
        datanode_manager->set_block_manager(block_manager.get()));
    INJECT_DEPENDENCY_TEST(
        datanode_manager,
        op_task_manager,
        datanode_manager->set_op_task_manager(op_task_manager.get()));

    INJECT_DEPENDENCY_TEST(job_manager, ns, job_manager->set_ns(ns));

    INJECT_DEPENDENCY_TEST(ufs_env, ns, ufs_env->SetNS(ns));
    INJECT_DEPENDENCY_TEST(acc_namespace, ns, acc_namespace->SetNS(ns));
  }

  void Start() {
    if (op_task_manager) {
      op_task_manager->Start();
    }

    if (ns) {
      ns->Start();
    }

    if (ufs_env) {
      // ufs_env::upload_monitor depend ns::meta_scanner_v2
      ufs_env->Start();
    }

    if (dancenn::NameSpace::IsAccMode()) {
      acc_namespace->Start();
    }

    if (status_monitor) {
      status_monitor->Start();
    }

    logger_metrics = &LoggerMetrics::Instance();
  }

  void Stop() {
    // We need to control order of calling destructors
    // rather than let compiler decides. Otherwise coredump happens when exit.
    if (status_monitor) {
      status_monitor->Stop();
      status_monitor.reset();
    }
    if (http_client) {
      http_client->Shutdown();
      http_client.reset();
    }

    if (key_manager) {
      key_manager->Stop();
    }
    if (op_task_manager) {
      op_task_manager->Stop();
    }
    if (datanode_manager) {
      datanode_manager->Stop();
    }
    key_manager.reset();
    op_task_manager.reset();
    datanode_manager.reset();
    block_manager.reset();

    if (ufs_env) {
      ufs_env->Stop();
    }
    if (ns) {
      ns->Stop();
    }
    if (ha_state) {
      ha_state->Stop();
    }
    if (safemode) {
      safemode->Stop();
    }

    ns.reset();
    ha_state.reset();
    safemode.reset();
    ufs_env.reset();
    edit_log_ctx.reset();
  }

 public:
  std::unique_ptr<RuntimeMonitor> runtime_monitor{nullptr};
  std::shared_ptr<JavaRuntime> jvm{nullptr};
  std::shared_ptr<EditLogContextBase> edit_log_ctx{nullptr};
  std::shared_ptr<DataCenters> data_centers{nullptr};
  std::shared_ptr<DatanodeManager> datanode_manager{nullptr};
  std::shared_ptr<BlockManager> block_manager{nullptr};
  std::shared_ptr<KeyManager> key_manager{nullptr};
  std::shared_ptr<JobManager> job_manager{nullptr};
  std::shared_ptr<VRWLock> barrier{nullptr};
  std::shared_ptr<SafeMode> safemode{nullptr};
  std::shared_ptr<cnetpp::http::HttpClient> http_client{nullptr};

  std::shared_ptr<HAState> ha_state{nullptr};
  std::unique_ptr<RetryCache> rc{nullptr};
  std::shared_ptr<OpTaskManager> op_task_manager{nullptr};
  std::shared_ptr<UfsEnv> ufs_env{nullptr};
  std::shared_ptr<AccNamespace> acc_namespace{nullptr};
  std::shared_ptr<NameSpace> ns{nullptr};

  std::shared_ptr<BlockTokenSecretManager> block_token_secret_manager{nullptr};

  std::shared_ptr<StatusMonitor> status_monitor{nullptr};

  LoggerMetrics* logger_metrics{nullptr};

 public:
  // ForTest
  std::shared_ptr<MockHAState> mock_ha_state{nullptr};
  std::shared_ptr<MockSafeMode> mock_safemode{nullptr};
};

}  // namespace dancenn
