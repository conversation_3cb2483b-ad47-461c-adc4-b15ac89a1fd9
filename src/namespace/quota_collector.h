// Copyright 2019 <PERSON><PERSON><PERSON> <zhangsha<PERSON><EMAIL>>

#ifndef NAMESPACE_QUOTA_COLLECTOR_H_
#define NAMESPACE_QUOTA_COLLECTOR_H_

#include <glog/logging.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>

#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <regex>

#include "inode.pb.h"  // NOLINT(build/include)
#include "namespace/file_usage.h"
#include "base/redis_manager.h"
#include "base/metric.h"
#include "base/metrics.h"
#include "base/read_write_lock.h"

namespace dancenn {

class NameSpace;

class QuotaCollector {
 public:
  explicit QuotaCollector(NameSpace* ns) : ns_(ns) {
  }
  ~QuotaCollector() {
    Stop();
  }

  void Start();
  
  void Stop();
  
  void SetupRedisManager(std::shared_ptr<IRedisManager> redis);

 private:
  NameSpace* ns_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::unique_ptr<cnetpp::concurrency::Task> task_;

  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(QuotaCollector* scanner)
        : scanner_(scanner) {
    }

    void Stop() override;

    bool operator()(void* arg = nullptr) override;

   private:
    QuotaCollector* scanner_{nullptr};
    std::mutex mutex_;
    std::condition_variable cond_;
  };

  friend class Task;

  std::shared_ptr<IRedisManager>  redis_;
  mutable ReadWriteLock rw_lock_;
  std::unordered_map<std::string, std::string> path_to_team_;
  std::unordered_map<std::string, FileUsage> path_usage_;
  std::unordered_map<std::string, FileUsage> team_usage_;
  std::unordered_set<std::string> migrating_dirs_;
  std::unordered_set<std::string> ssd_dirs_;
  std::vector<std::pair<std::string, std::string>> pattern_team_mappings_;

  std::string default_team_key_;
  std::string default_path_key_;
  std::string migrating_dirs_key_;
  std::string team_usage_key_;
  std::string path_usage_key_;
  std::string timestamp_key_;
  std::string storage_key_;

  MetricID scan_success_count_;
  MetricID scan_fail_count_;
  
  bool LoadFromRedis();
  bool RefreshMappingFile();
  bool DumpToRedis();
  void Scan(Task* task);

  bool ConnectRedis();
  int64_t ComputeFileSize(const INode& inode);
  int64_t ComputeSizeUsage(const int64_t& size);
  float ComputeReplication(const INode& inode);
  void UpdateUsage(const std::string& quota_path,
      const std::string& quota_team, const int64_t& sata,const int64_t& ssd,
      const int64_t& sata_usage, const int64_t& ssd_usage);
};

}  // namespace dancenn

#endif  // NAMESPACE_QUOTA_COLLECTOR_H_

