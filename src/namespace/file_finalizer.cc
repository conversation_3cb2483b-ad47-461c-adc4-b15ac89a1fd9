// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/05/23
// Description

#include "namespace/file_finalizer.h"

#include <absl/strings/str_format.h>               // For StrFormat.
#include <absl/types/optional.h>                   // For optional.
#include <block_manager/block_info.h>              // For BlockUCState.
#include <gflags/gflags.h>                         // For DECLARE_bool, etc.
#include <glog/logging.h>                          // For CHECK, LOG.
#include <proto/generated/cloudfs/hdfs.pb.h>       // For BlockProto, IoMode.
#include <proto/generated/cloudfs/lifecycle.pb.h>  // For LifecyclePolicyProto.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.

#include <chrono>  // For system_clock, etc.

#include "base/java_exceptions.h"             // For JavaExceptions.
#include "base/logger_metrics.h"              // For LoggerMetrics.
#include "base/stop_watch.h"                  // For StopWatch.
#include "namespace/lifecycle_policy_util.h"  // For Lifecycle.
#include "namespace/namespace.h"              // For IsAccMode
#include "namespace/xattr.h"                  // For XAttr.

DECLARE_int32(namespace_type);
DECLARE_bool(lifecycle_enable);
DECLARE_bool(trace_rpc_log);
DECLARE_bool(complete_rpc_trigger_file_upload_in_callback);
DECLARE_bool(complete_rpc_check_replica_in_callback);

namespace dancenn {

FileFinalizerMetrics::FileFinalizerMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameSpace");
  finalize_file_lm_release_lease_time_ =
      metrics->RegisterHistogram("FinalizeFile#step=LeaseManagerReleaseLease");
  finalize_file_write_editlog_time_ =
      metrics->RegisterHistogram("FinalizeFile#step=WriteEditLog");
  finalize_file_write_meta_storage_time_ =
      metrics->RegisterHistogram("FinalizeFile#step=WriteMetaStorage");
  finalize_file_wait_editlog_and_ms_time_ =
      metrics->RegisterHistogram("FinalizeFile#step=WaitEditLogAndMetaStorage");

  finalize_file_callback_commit_bip_time_ =
      metrics->RegisterHistogram("FinalizeFileCallback#step=CommitBip");
  finalize_file_callback_remove_lease_time_ =
      metrics->RegisterHistogram("FinalizeFileCallback#step=RemoveLease");
  finalize_file_callback_check_replica_time_ =
      metrics->RegisterHistogram("FinalizeFileCallback#step=CheckReplica");
  finalize_file_callback_trigger_upload_time_ =
      metrics->RegisterHistogram("FinalizeFileCallback#step=TriggerUpload");
}

void FileFinalizer::Start(NameSpace* name_space,
                          BlockManager* block_manager,
                          BIPWriteManagerBase* bip_write_manager,
                          LeaseManagerBase* lease_manager,
                          EditLogSenderBase* edit_log_sender,
                          MetaStorage* meta_storage,
                          UfsEnv* ufs_env) {
  // Some test cases may call Start with nullptrs.
  name_space_ = name_space;
  // CHECK_NOTNULL(block_manager);
  block_manager_ = block_manager;
  // CHECK_NOTNULL(bip_write_manager);
  bip_write_manager_ = bip_write_manager;
  // CHECK_NOTNULL(lease_manager);
  lease_manager_ = lease_manager;
  CHECK_NOTNULL(edit_log_sender);
  edit_log_sender_ = edit_log_sender;
  CHECK_NOTNULL(meta_storage);
  meta_storage_ = meta_storage;

  if (NameSpace::IsAccMode()) {
    CHECK_NOTNULL(ufs_env);
  }
  ufs_env_ = ufs_env;
}

void FileFinalizer::Stop() {
  block_manager_ = nullptr;
  bip_write_manager_ = nullptr;
  lease_manager_ = nullptr;
  edit_log_sender_ = nullptr;
  meta_storage_ = nullptr;
}

Status FileFinalizer::FinalizeFile(const std::string& src,
                                   const Block& last_committed_blk,
                                   BlockID abandoned_blk_id,
                                   INodeInPath* iip,
                                   const INode& old_inode,
                                   const std::vector<INode>& ancestors,
                                   RpcClosure* rpc_done,
                                   std::shared_ptr<StopWatchContext> rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  CHECK(iip);
  CHECK(rpc_done);
  ClosureGuard done_guard(rpc_done);

  auto fret = UpdateFileToFinalize(
      src, last_committed_blk, abandoned_blk_id, &iip->MutableInode());
  if (fret.HasException()) {
    rpc_done->set_status(Status(fret));
    return fret;
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after UpdateFileToFinalize");

  StopWatch sw(metrics_.finalize_file_lm_release_lease_time_);
  sw.Start();

  const INode* inode = &iip->Inode();
  BlockInfoProto penultimate_bip, last_bip, deprecated_bip;
  {
    if (inode->blocks_size() > 0) {
      const auto& bp = inode->blocks(inode->blocks_size() - 1);
      BlockID blk_id = bp.blockid();
      BlockInfoProto old_bip;
      CHECK(meta_storage_->GetBlockInfo(blk_id, &old_bip)) << blk_id;
      if (!(old_bip.block_id() == bp.blockid() &&
            // NOTICE: Pipeline recovery and block recovery may update gs.
            old_bip.gen_stamp() <= bp.genstamp() &&
            old_bip.inode_id() == inode->id())) {
        auto msg = LogBipMismatch(old_bip, bp, __FILE__, __LINE__);
        rpc_done->set_status(Status(JavaExceptions::kIOException,
                                    "Internal error, please contact us"));
        return Status(JavaExceptions::kIOException, msg);
      }
      if (old_bip.gen_stamp() < bp.genstamp() &&
          IsBlockIDValid(abandoned_blk_id)) {
        auto msg = LogBipMismatch(old_bip, bp, __FILE__, __LINE__);
        rpc_done->set_status(Status(JavaExceptions::kIOException,
                                    "Internal error, please contact us"));
        return Status(JavaExceptions::kIOException, msg);
      }
      if (old_bip.state() != BlockInfoProto::kUnderConstruction) {
        // Exception case: Block has been committed before,
        // don't commit it again!
        CHECK_EQ(old_bip.gen_stamp(), bp.genstamp()) << blk_id;
        CHECK_EQ(old_bip.num_bytes(), bp.numbytes()) << blk_id;
        CHECK(!last_bip.IsInitialized());

        if (IsAccType(FLAGS_namespace_type) &&
            (!old_bip.has_key_block() || !old_bip.key_block())) {
          LOG(INFO)
              << "Last block is not key block, set acc block info. old_bip: "
              << old_bip.ShortDebugString();
          last_bip.CopyFrom(old_bip);
          name_space_->SetBlockAccInfo(inode, &last_bip, true);
          CHECK(last_bip.IsInitialized())
              << last_bip.InitializationErrorString();
        }
      } else {
        last_bip.CopyFrom(old_bip);
        last_bip.set_gen_stamp(bp.genstamp());
        last_bip.set_num_bytes(bp.numbytes());

        // Copy from NameSpace::IsAccMode().
        if (IsAccType(FLAGS_namespace_type)) {
          name_space_->SetBlockAccInfo(inode, &last_bip, true);
          last_bip.set_state(BlockInfoProto::kComplete);
        } else {
          if (last_bip.has_write_mode() &&
              last_bip.write_mode() == cloudfs::TOS_BLOCK) {
            last_bip.set_state(BlockInfoProto::kPersisted);
          } else {
            last_bip.set_state(BlockInfoProto::kComplete);
          }
        }

        CHECK(last_bip.IsInitialized()) << last_bip.InitializationErrorString();
      }
    }
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after do BIP");

  SnapshotLog inode_snaplog;
  iip->GenerateSnapshotLog(&inode_snaplog);

  sw.NextStep(metrics_.finalize_file_write_editlog_time_);
  // log close will do logSync
  std::vector<INodeID> ancestors_id;
  std::for_each(
      ancestors.begin(), ancestors.end(), [&ancestors_id](INode inode) {
        ancestors_id.push_back(inode.id());
      });
  auto txid = edit_log_sender_->LogCloseFileV2(
      src,
      *inode,
      last_bip.IsInitialized() ? &last_bip : nullptr,
      abandoned_blk_id,
      old_inode,
      ancestors_id,
      inode_snaplog);
  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after EditLog");

  sw.NextStep(metrics_.finalize_file_write_meta_storage_time_);
  CHECK(!rpc_done->callback());
  rpc_done->set_callback(
      [this,
       penultimate_bip,
       last_bip,
       deprecated_bip,
       inode = *inode,
       rpc_sw_ctx](const Status& s) {
        RPC_SW_CTX_LOG(rpc_sw_ctx, "callback");

        if (!s.IsOK()) {
          LOG(ERROR) << "MetaStorage returns false, logical error";
          MFC(LoggerMetrics::Instance().error_)->Inc();
          return;
        }

        StopWatch sw(metrics_.finalize_file_callback_commit_bip_time_);
        sw.Start();
        // NameSpace::RemoveLease
        sw.NextStep(metrics_.finalize_file_callback_remove_lease_time_);
        if (!lease_manager_->RemoveLease(inode.id())) {
          LOG(ERROR) << "RemoveLease failed after FinalizeFile, inode id: "
                     << inode.id();
          MFC(LoggerMetrics::Instance().error_)->Inc();
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "callback after RemoveLease");

        sw.NextStep(metrics_.finalize_file_callback_check_replica_time_);
        // NameSpace::GetFileBlocksInternal
        if (FLAGS_complete_rpc_check_replica_in_callback) {
          std::vector<BlockProto> blocks;
          for (const BlockProto& block : inode.blocks()) {
            blocks.push_back(block);
          }
          block_manager_->CheckReplica(blocks, &inode, rpc_sw_ctx.get());
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "callback after CheckReplica");

        sw.NextStep(metrics_.finalize_file_callback_trigger_upload_time_);
        if (NameSpace::IsAccMode() && ufs_env_ &&
            FLAGS_complete_rpc_trigger_file_upload_in_callback) {
          CHECK_NOTNULL(ufs_env_->upload_monitor())
              ->TriggerFileUpload(inode.id());
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "callback after TriggerFileUpload");
      });
  meta_storage_->OrderedCloseFile(
      &iip->MutableInode(),
      last_bip.IsInitialized() ? &last_bip : nullptr,
      abandoned_blk_id,
      &old_inode,
      inode_snaplog,
      txid,
      done_guard.release());
  return Status();
}

Status FileFinalizer::UpdateFileToFinalize(const std::string& src,
                                           const Block& last_committed_blk,
                                           BlockID abandoned_blk_id,
                                           INode* inode) {
  CHECK(inode);

  if (!inode->has_uc()) {
    std::string msg =
        absl::StrFormat("Cannot finalize file %s with no uc field. inode={%s}",
                        src,
                        inode->ShortDebugString());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  if (IsBlockIDValid(abandoned_blk_id)) {
    if (inode->blocks_size() == 0) {
      std::string msg = absl::StrFormat(
          "Cannot abandon last block of empty file, "
          "abandoned_blk_id=%d, inode={%s}",
          abandoned_blk_id,
          inode->ShortDebugString());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    if (inode->blocks(inode->blocks_size() - 1).blockid() != abandoned_blk_id) {
      std::string msg = absl::StrFormat(
          "Cannot abandon mismatched last block, "
          "abandoned_blk_id=%d, inode={%s}",
          abandoned_blk_id,
          inode->ShortDebugString());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    inode->mutable_blocks()->RemoveLast();
  }

  if (IsBlockIDValid(last_committed_blk.id)) {
    if (inode->blocks_size() == 0) {
      std::string msg = absl::StrFormat(
          "Cannot commit last block of empty file, "
          "last_blk={id:%d,gs:%d,num_bytes:%d}, inode={%s}",
          last_committed_blk.id,
          last_committed_blk.gs,
          last_committed_blk.num_bytes,
          inode->ShortDebugString());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    BlockProto* last_bp = inode->mutable_blocks(inode->blocks_size() - 1);
    if (last_bp->blockid() != last_committed_blk.id) {
      std::string msg = absl::StrFormat(
          "Cannot commit mismatched last block, "
          "last_blk={id:%d,gs:%d,num_bytes:%d}, inode={%s}",
          last_committed_blk.id,
          last_committed_blk.gs,
          last_committed_blk.num_bytes,
          inode->ShortDebugString());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    last_bp->set_genstamp(last_committed_blk.gs);
    last_bp->set_numbytes(last_committed_blk.num_bytes);
  } else {
    // Do not LOG(ERROR) if inode->blocks_size() > 0.
    // CommitBlockSynchronization when request.closefile() &&
    // request.deleteblock().
  }

  int64_t time_now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::system_clock::now().time_since_epoch())
                            .count();
  inode->set_mtime(static_cast<uint64_t>(time_now_ms));
  inode->set_status(INode::kFileComplete);
  inode->clear_uc();

  if (NameSpace::IsAccMode()) {
    if (inode->has_ufs_file_info()) {
      inode->mutable_ufs_file_info()->set_complete_mtime(
          static_cast<uint64_t>(time_now_ms));
    }
  }

  VLOG(10) << "UpdateFileToFinalize Finish. inode="
           << inode->ShortDebugString();
  return {};
}

std::string FileFinalizer::LogBipMismatch(const BlockInfoProto& old_bip,
                                          const BlockProto& bp,
                                          const char* file,
                                          uint32_t line) {
  std::string msg = absl::StrFormat(
      "old_bip:{id:%d,gs:%d,num_bytes:%d} is mismatched with "
      "last bp:{id:%d,gs:%d,num_bytes:%d}. file=%s, line=%d",
      old_bip.block_id(),
      old_bip.gen_stamp(),
      old_bip.num_bytes(),
      bp.blockid(),
      bp.genstamp(),
      bp.numbytes(),
      file,
      line);
  LOG(ERROR) << msg;
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return msg;
}

void FileFinalizer::SetServerTxid(RpcClosure* rpc_done, int64_t txid) {
  if (rpc_done) {
    SetServerTxid(rpc_done->ctl(), txid);
  }
}

void FileFinalizer::SetServerTxid(RpcController* ctx, int64_t txid) {
  if (ctx) {
    ctx->set_seen_txid(txid);
  }
}

}  // namespace dancenn
