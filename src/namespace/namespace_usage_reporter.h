//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

#include <atomic>

// Third
#include "cnetpp/concurrency/spin_lock.h"
#include "cnetpp/concurrency/thread_pool.h"

// Project
#include "base/status.h"

namespace dancenn {

using Thread = cnetpp::concurrency::Thread;

class NameSpace;

class NamespaceUsageReporter {
public:
  NamespaceUsageReporter(NameSpace *ns) : ns_(ns) {}

  Status Start();
  void Stop();

private:
  bool ReporterRunThread();
  Status ReportUsage();
  Status PostToUsageService(cnetpp::base::StringPiece &body);
  static Status CheckPostResult(const std::string &body);

private:
  enum ReporterState : uint32_t {
    REPORTER_STATE_INIT = 0,
    REPORTER_STATE_RUNNING,
    REPORTER_STATE_STOPPING,
    REPORTER_STATE_FINISHED
  };

  std::string url_;
  NameSpace *ns_;
  std::shared_ptr<Thread> thread_;
  std::atomic<ReporterState> state_{REPORTER_STATE_INIT};
};

} // namespace dancenn
