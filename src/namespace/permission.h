//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_PERMISSION_H_
#define NAMESPACE_PERMISSION_H_

#include <string>

#include "inode.h"
#include "acl.pb.h"  // NOLINT(build/include)

namespace dancenn {

using FsAction = cloudfs::AclEntryProto_FsActionProto;

// Assign target & other to target.
inline FsAction And(FsAction target, FsAction other) {
  uint8_t lhs = target;
  uint8_t rhs = other;
  return FsAction(rhs & lhs);
}

inline FsAction Or(FsAction target, FsAction other) {
  uint8_t lhs = target;
  uint8_t rhs = other;
  return FsAction(rhs | lhs);
}

inline FsAction Not(const FsAction& target) {
  uint8_t lhs = target;
  return FsAction(7- lhs);
}

inline bool Implies(FsAction target, FsAction other) {
  uint8_t lhs = target;
  uint8_t rhs = other;
  return (lhs & rhs) == rhs;
}

inline std::string ToReadableString(FsAction action) {
  switch (action) {
    case cloudfs::AclEntryProto_FsActionProto_NONE : return "---";
    case cloudfs::AclEntryProto_FsActionProto_EXECUTE : return "--x";
    case cloudfs::AclEntryProto_FsActionProto_WRITE : return "-w-";
    case cloudfs::AclEntryProto_FsActionProto_WRITE_EXECUTE : return "-wx";
    case cloudfs::AclEntryProto_FsActionProto_READ : return "r--";
    case cloudfs::AclEntryProto_FsActionProto_READ_EXECUTE : return "r-x";
    case cloudfs::AclEntryProto_FsActionProto_READ_WRITE : return "rw-";
    case cloudfs::AclEntryProto_FsActionProto_PERM_ALL : return "rwx";
  }
  return "NOT IMPLEMENTED action";
}

inline std::string ToRangerTypeString(FsAction action) {
  switch (action) {
    case cloudfs::AclEntryProto_FsActionProto_NONE:
      return "none";
    case cloudfs::AclEntryProto_FsActionProto_EXECUTE:
      return "execute";
    case cloudfs::AclEntryProto_FsActionProto_WRITE:
      return "write";
    case cloudfs::AclEntryProto_FsActionProto_WRITE_EXECUTE:
      return "write";
    case cloudfs::AclEntryProto_FsActionProto_READ:
      return "read";
    case cloudfs::AclEntryProto_FsActionProto_READ_EXECUTE:
      return "read";
    case cloudfs::AclEntryProto_FsActionProto_READ_WRITE:
      return "write";
    case cloudfs::AclEntryProto_FsActionProto_PERM_ALL:
      return "write";
  }
  return "NOT IMPLEMENTED action";
}

class FsPermission {
 public:
  FsPermission()
      : user_action_(::cloudfs::AclEntryProto_FsActionProto_PERM_ALL),
        group_action_(::cloudfs::AclEntryProto_FsActionProto_PERM_ALL),
        other_action_(::cloudfs::AclEntryProto_FsActionProto_PERM_ALL),
        sticky_bit_(false) {
  }

  FsPermission(const FsAction& u,
               const FsAction& g,
               const FsAction& o,
               bool sb = false)
      : user_action_(u), group_action_(g), other_action_(o), sticky_bit_(sb) {}

  explicit FsPermission(uint16_t mode) {
    Set(mode);
  }

  void Set(uint16_t mode) {
    user_action_ = FsAction((mode >> 6) & 7);
    group_action_ = FsAction((mode >> 3) & 7);
    other_action_ = FsAction(mode & 7);
    sticky_bit_ = ((mode >> 9) & 1) == 1;
  }

  static FsPermission GetDefault() {
    // TODO(wangning.ito) support customize && store default umask.
    return FsPermission(0777);
  }

  static FsPermission GetDirDefault() {
    // TODO(wangning.ito) support customize && store default umask.
    return FsPermission(0777);
  }

  static FsPermission GetFileDefault() {
    // TODO(wangning.ito) support customize && store default umask.
    return FsPermission(0666);
  }

  static uint16_t AllowUserWriteExecute(uint16_t mask) {
    return mask
        | (::cloudfs::AclEntryProto_FsActionProto_WRITE_EXECUTE << 6);
  }

  uint16_t ToShort() const {
    return static_cast<uint16_t >(0)
        | static_cast<uint16_t >(sticky_bit_ ? 1 << 9 : 0)
        | (uint16_t(user_action_) << 6)
        | (uint16_t(group_action_) << 3)
        | uint16_t(other_action_);
  }

  FsAction user_action() const {
    return user_action_;
  }

  FsAction group_action() const {
    return group_action_;
  }

  FsAction other_action() const {
    return other_action_;
  }

  bool sticky_bit() const {
    return sticky_bit_;
  }

  friend std::ostream& operator<< (std::ostream& os, const FsPermission& permission) {
    os << ToReadableString(permission.user_action_)
       << ToReadableString(permission.group_action_);

    if (permission.sticky_bit_) {
        char t = Implies(permission.other_action_,
                         FsAction::AclEntryProto_FsActionProto_EXECUTE) ? 't' : 'T';
        auto s = ToReadableString(permission.other_action_);
        s[s.length() -1] = t;
        os << s;
    } else {
       os << ToReadableString(permission.other_action_);
    }
    return os;
  };

 private:
  ::cloudfs::AclEntryProto::FsActionProto user_action_;
  ::cloudfs::AclEntryProto::FsActionProto group_action_;
  ::cloudfs::AclEntryProto::FsActionProto other_action_;
  bool sticky_bit_;
};

}  // namespace dancenn

#endif  // NAMESPACE_PERMISSION_H_
