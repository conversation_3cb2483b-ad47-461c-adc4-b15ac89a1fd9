#include "namespace/namespace_scrub_lifecycle_scan.h"

#include <absl/strings/str_format.h>

#include "block_info_proto.pb.h"
#include "proto/generated/cloudfs/lifecycle.pb.h"

#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "base/time_util.h"
#include "edit/sender_base.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"

DECLARE_uint32(lifecycle_scrub_persist_stats_depth);
DECLARE_uint32(lifecycle_scrub_remove_depred_xattr_per_scurb);

namespace dancenn {

using cloudfs::BlockProto;
using cloudfs::StorageClassProto;
using cloudfs::TransitionRuleProto;

const LifecycleScrubStat empty_scrub_stat;

LifecycleScrubStat& LifecycleScrubStat::operator=(
    const LifecycleScrubStat& other) {
  this->num_file.store(other.num_file.load());
  this->num_dir.store(other.num_dir.load());
  this->num_stat_persisted.store(other.num_stat_persisted.load());
  this->num_inode_expired.store(other.num_inode_expired.load());
  this->num_inode_transited.store(other.num_inode_transited.load());

  std::copy(std::begin(other.num_logical_block_expected),
            std::end(other.num_logical_block_expected),
            std::begin(this->num_logical_block_expected));
  std::copy(std::begin(other.num_logical_byte_expected),
            std::end(other.num_logical_byte_expected),
            std::begin(this->num_logical_byte_expected));
  std::copy(std::begin(other.num_logical_block),
            std::end(other.num_logical_block),
            std::begin(this->num_logical_block));
  std::copy(std::begin(other.num_logical_byte),
            std::end(other.num_logical_byte),
            std::begin(this->num_logical_byte));
  std::copy(std::begin(other.num_physical_replica),
            std::end(other.num_physical_replica),
            std::begin(this->num_physical_replica));
  std::copy(std::begin(other.num_physical_byte),
            std::end(other.num_physical_byte),
            std::begin(this->num_physical_byte));
  this->timestamp_sec = other.timestamp_sec;

  return *this;
}

LifecycleScrubStat& LifecycleScrubStat::operator+=(
    const LifecycleScrubStat& other) {
  this->num_file += other.num_file;
  this->num_dir += other.num_dir;
  this->num_stat_persisted += other.num_stat_persisted;
  this->num_inode_expired += other.num_inode_expired;
  this->num_inode_transited += other.num_inode_transited;

  for (int cls = StorageClassProto::NONE;
       cls < cloudfs::StorageClassProto_ARRAYSIZE;
       cls++) {
    this->num_logical_block_expected[cls] += other.num_logical_block_expected[cls];
    this->num_logical_byte_expected[cls] += other.num_logical_byte_expected[cls];
    this->num_logical_block[cls] += other.num_logical_block[cls];
    this->num_logical_byte[cls] += other.num_logical_byte[cls];
    this->num_physical_replica[cls] += other.num_physical_replica[cls];
    this->num_physical_byte[cls] += other.num_physical_byte[cls];
  }

  // update timestamp to now
  this->timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
  return *this;
}

void LifecycleScrubStat::Clear() {
  *this = empty_scrub_stat;
}

void LifecycleScrubStat::SerializeToProto(StorageClassStatProto* proto) {
  CHECK_NOTNULL(proto);
  proto->set_timestampsec(timestamp_sec);
  for (int i = StorageClassProto::NONE;
       i < cloudfs::StorageClassProto_ARRAYSIZE;
       i++) {
    proto->add_numreplica(num_physical_replica[i]);
    proto->add_numbyte(num_physical_byte[i]);
    proto->add_numlogicalblock(num_logical_block[i]);
    proto->add_numlogicalbyte(num_logical_byte[i]);
    proto->add_numlogicalblockexpected(num_logical_block_expected[i]);
    proto->add_numlogicalbyteexpected(num_logical_byte_expected[i]);
  }
}

std::string LifecycleScrubStat::ToString() const {
  std::stringstream ss;
  ss << absl::StrFormat(
      "LifecycleScrubStat: { file %ld, dir %ld, "
      "stat_persisted %ld, inode_expired %ld, inode_transited %ld. ",
      num_file, num_dir,
      num_stat_persisted, num_inode_expired, num_inode_transited);

  for (int i = StorageClassProto::NONE;
       i < cloudfs::StorageClassProto::COLD;
       i++) {
    std::string cls_name;
    bool valid = StorageClassID2Name(static_cast<StorageClassProto>(i), &cls_name);
    CHECK(valid);
    ss << absl::StrFormat(
        "%s logical block %lu(expected) %lu(effective), "
        "logical byte %lu(expected), %lu(effective), "
        "physical replica %lu, physical byte %lu. ",
        cls_name, num_logical_block_expected[i], num_logical_block[i],
        num_logical_byte_expected[i], num_logical_byte[i],
        num_physical_replica[i], num_physical_byte[i]);
  }
  ss << absl::StrFormat("ts_sec %lu }", timestamp_sec);
  return ss.str();
}

LifecycleScanScrubOp::LifecycleScanScrubOp() {}

void LifecycleScanScrubOp::ProcessFile(const INode& file) {
  scrub_stat_.num_file++;

  // 1. get current status
  LifecyclePolicyProto policy;
  INodeID effective_inode_id = kInvalidINodeId;
  distance_to_policy_ = GetEffectiveLifecyclePolicy(
      meta_storage_.get(), file.id(), nullptr/* filter */, &policy, &effective_inode_id);
  StorageClassProto cls;
  uint64_t now = TimeUtil::GetNowEpochMs();
  GetEffectiveStorageClass(policy, now - file.mtime(), &cls);

  // 2. calculate statistics (both active & standby)
  GenerateStat(file, cls);

  // 3. apply lifecycle policy (only active)
  bool is_active = namespace_->ha_state()->IsActive() &&
                   !namespace_->ha_state()->InTransition();
  bool expired = false;
  if (is_active) {
    ApplyLifecyclePolicy(file, effective_inode_id, policy, &expired);
  }

  // 4. diff StorageClassReport and generate command (only active)
  if (is_active && !expired) {
    GenerateCmd(file.id(), cls);
  }
}

void LifecycleScanScrubOp::HandleTransitionDefault(
    BlockID blkid,
    const std::vector<std::string>& dnuuids,
    const std::vector<StorageClassReportProto>& reports,
    StorageClassProto target_class) {
  CHECK_EQ(dnuuids.size(), reports.size());
  for (int i = 0; i < reports.size(); i++) {
    auto dn_uuid = dnuuids.at(i);
    CHECK(reports.at(i).has_stcls());
    auto reported_class = reports.at(i).stcls();
    CHECK_NE(reported_class, StorageClassProto::NONE);

    if (reported_class == target_class) {
      continue;
    }

    // generate cmd as long as reported StorageClass replica mismatch
    dn_cmds_mapping_[dn_uuid].block_ids[target_class].insert(blkid);
  }
}

// XXX(xuex)
// Transit one replica to target class, while the others to COLD.
//
// It is really hack since it conflicts with replication monitor. Ideally,
// Lifecycle should only transit StorageClass of replica, no matter how many
// replicas exist in one block.
//
// Should be removed in future, see link below for more info:
// https://meego.feishu.cn/cfs/issue/detail/13366674
void LifecycleScanScrubOp::HandleTransitionWithOtherCold(
    BlockID blkid,
    const std::vector<std::string>& dnuuids,
    const std::vector<StorageClassReportProto>& reports,
    StorageClassProto target_class) {
  CHECK_EQ(dnuuids.size(), reports.size());
  int tgt_idx = -1;  // points to replica with target class
  for (int i = 0; i < reports.size(); i++) {
    CHECK(reports.at(i).has_stcls());
    if (reports.at(i).stcls() == target_class) {
      tgt_idx = i;
      break;
    }
  }

  for (int i = 0; i < reports.size(); i++) {
    auto dn_uuid = dnuuids.at(i);
    CHECK(reports.at(i).has_stcls());
    auto reported_class = reports.at(i).stcls();
    CHECK_NE(reported_class, StorageClassProto::NONE);

    if (tgt_idx == -1) {
      // If no replica with target StorageClass reported, transit the first
      dn_cmds_mapping_[dn_uuid].block_ids[target_class].insert(blkid);
      tgt_idx = i;
      continue;
    }

    if (i == tgt_idx) {
      // Already has a replica with target StorageClass
      continue;
    }

    // the others COLD
    if (reported_class != StorageClassProto::COLD) {
      dn_cmds_mapping_[dn_uuid].block_ids[StorageClassProto::COLD].insert(blkid);
    }
  }
}

void LifecycleScanScrubOp::ProcessDir(const INode& dir) {
  scrub_stat_.num_dir++;

  // 1. get current status
  bool is_active = namespace_->ha_state()->IsActive() &&
                   !namespace_->ha_state()->InTransition();
  LifecyclePolicyProto policy;
  INodeID effective_inode_id = kInvalidINodeId;
  distance_to_policy_ = GetEffectiveLifecyclePolicy(
      meta_storage_.get(), dir.id(), nullptr/* filter */, &policy, &effective_inode_id);

  // 2. apply lifecycle policy
  bool expired = false;
  if (is_active) {
    ApplyLifecyclePolicy(dir, effective_inode_id, policy, &expired);
  }
}

Status LifecycleScanScrubOp::HandleDirResult(ScrubAction action) {
  Status st;
  switch (action) {
    case SCRUB_ACTION_CHECK: {
      st = CheckDirResult();
      break;
    }
    case SCRUB_ACTION_FORCE_OVERWRITE: {
      st = PersistDirResult();
      break;
    }
    default: {
      std::string msg = absl::StrFormat(
          "HandleDirResult encountered unexpected action %d",
          static_cast<int>(action));
      return Status(Code::kError, msg);
    }
  }
  return st;
}

Status LifecycleScanScrubOp::CheckDirResult() {
  StorageClassStatProto persisted, calculated;
  CHECK_EQ(node_.type(), INode_Type_kDirectory);
  auto st = meta_storage_->GetStorageClassStat(node_.id(), &persisted, nullptr);
  if (UNLIKELY(!st.IsOK())) {
    std::string msg = absl::StrFormat(
        "StorageClassStatProto is corrupted on inode %lu, err: %s",
        node_.id(), st.ToString());
    LOG(ERROR) << msg;
    return Status(st.code(), msg);
  }
  scrub_stat_.SerializeToProto(&calculated);
  // timestamp mismatch is ok
  calculated.set_timestampsec(persisted.timestampsec());

  std::string calc_str, pers_str;
  persisted.SerializeToString(&pers_str);
  calculated.SerializeToString(&calc_str);
  if (UNLIKELY(pers_str != calc_str)) {
    std::string msg = absl::StrFormat(
        "StorageClassStatProto is corrupted on inode %lu, calculated: %s, "
        "persisted: %s",
        node_.id(),
        calculated.ShortDebugString(),
        persisted.ShortDebugString());
    LOG(ERROR) << msg;
    return Status(Code::kError, msg);
  }

  return Status();
}

Status LifecycleScanScrubOp::PersistDirResult() {
  if (distance_to_policy_ < 0) {
    // deprecated
    return Status();
  }
  if (distance_to_policy_ > FLAGS_lifecycle_scrub_persist_stats_depth) {
    // skip directories that far from which attached policy
    return Status();
  }

  scrub_stat_.num_stat_persisted++;

  StorageClassStatProto proto;
  scrub_stat_.SerializeToProto(&proto);
  CHECK_EQ(node_.type(), INode_Type_kDirectory);
  auto st = meta_storage_->PutStorageClassStat(node_.id(), proto);
  CHECK(st.IsOK()) << absl::StrFormat(
      "Failed to write StorageClassStatProto for inode: %lu, %s",
      node_.id(), st.ToString());

  return Status();
}

void LifecycleScanScrubOp::AddSubDirResult(const ScrubOpPtr subdir_op) {
  auto subop = std::dynamic_pointer_cast<LifecycleScanScrubOp>(subdir_op);

  // move cmds to parent
  for (auto& sub_pair : subop->dn_cmds_mapping_) {
    auto& dn_uuid = sub_pair.first;
    auto& sub_dn_cmds = sub_pair.second;
    for (int cls = StorageClassProto::HOT;
         cls < cloudfs::StorageClassProto_ARRAYSIZE;
         cls++) {

      auto& sub_blk_ids = sub_dn_cmds.block_ids[cls];
      auto& blk_ids = dn_cmds_mapping_[dn_uuid].block_ids[cls];

      blk_ids.insert(sub_blk_ids.begin(), sub_blk_ids.end());
      sub_blk_ids.clear();
    }
  }
  subop->dn_cmds_mapping_.clear();

  // accumulate scrub stats to parent
  this->scrub_stat_ += subop->scrub_stat_;
}

int64_t LifecycleScanScrubOp::EstimateRemainingSeconds(int64_t finished_inodes,
                                                       int64_t elapsed_ms) {
  return -1;
}

std::string LifecycleScanScrubOp::ToString() {
  return absl::StrFormat(
      "LifecycleScanScrubOp { inode %lu, scrub_stats: %s }",
      node_.id(), scrub_stat_.ToString());
}

ScrubOpPtr LifecycleScanScrubOp::NewOp() {
  return std::make_shared<LifecycleScanScrubOp>();
}

bool LifecycleScanScrubOp::HaveCommands() {
  return !dn_cmds_mapping_.empty();
}

const StorageClassCmds& LifecycleScanScrubOp::GetCommands() {
  return dn_cmds_mapping_;
}

void LifecycleScanScrubOp::ClearCommands() {
  dn_cmds_mapping_.clear();
}

int64_t LifecycleScanScrubOp::GetExpiredTime(
    const INode& inode,
    const INodeID effective_inode_id,
    const LifecyclePolicyProto& policy) {
  int64_t delta_ms = -1;
  CHECK(policy.has_exprule());
  if (policy.exprule().has_expiration_type() &&
      policy.exprule().expiration_type() ==
      ::cloudfs::ExpirationTypeProto::ATIME_BASED) {
    ATimeToBeUpdate atime;
    auto st = meta_storage_->GetTtlATime(effective_inode_id, &atime);
    if (st.IsOK()) {
      // ttl_atime exist, use ttl_atime
      delta_ms = TimeUtil::GetNowEpochMs() - atime.atime();
    } else if (st.code() == Code::kFileNotFound) {
      // ttl_atime not exist, use inode.atime()
      INode effective_inode;
      auto code = meta_storage_->GetINode(effective_inode_id, &effective_inode);
      if (code == StatusCode::kOK) {
        delta_ms = TimeUtil::GetNowEpochMs() - effective_inode.atime();
      } else {
        LOG(ERROR) << "GetINode failed, error code: " << code;
        return -1;
      }
    }
  } else {
    // mtime_based
    delta_ms = TimeUtil::GetNowEpochMs() - inode.mtime();
  }
  return delta_ms;
}

void LifecycleScanScrubOp::ApplyLifecyclePolicy(
    const INode& inode,
    INodeID effective_inode_id,
    const LifecyclePolicyProto& policy,
    bool* expired) {
  std::string fullpath;
  fullpath = namespace_->BuildFullPath(inode.id(), nullptr);
  if (fullpath.empty()) {
    return;
  }

  // 1. if expired, delete inode
  bool recycle_whole_directory = false;
  if (policy.has_exprule() && policy.exprule().has_recycle_whole_directory() &&
      policy.exprule().recycle_whole_directory()) {
    recycle_whole_directory = true;
  }

  if (recycle_whole_directory && inode.id() != effective_inode_id) {
    // skip subdir
  } else if (!policy.has_exprule()) {
    // skip has no exprule
  } else {
    int64_t delta_ms = GetExpiredTime(inode, effective_inode_id, policy);
    if (delta_ms < 0) {
      // XXX unexpected mtime, should never happen
      LOG(WARNING) << absl::StrFormat(
          "unexpected delta time %ld, inode mtime %lu",
          delta_ms,
          inode.mtime());
    }

    if (LifecycleNeedExpire(policy, delta_ms)) {
      scrub_stat_.num_inode_expired++;

      LOG(INFO) << absl::StrFormat(
          "LifecycleScanScrub found inode %s id %lu expired for policy %s, "
          "now try to delete it.",
          fullpath,
          inode.id(),
          policy.ShortDebugString());
      RpcClosure* done = NewRpcCallback(true);
      namespace_->AsyncDelete(fullpath,
                              recycle_whole_directory,
                              UserGroupInfo("root", "supergroup"),
                              LogRpcInfo(),
                              done);
      *expired = true;
      return;
    }
  }
  *expired = false;

  // 2. if need transit, no need to update inode explicitly, so do nothing
  // XXX
  // Previously, we persisted expected storage class info into INode's xattr.
  // When LifecyclePolicy of big directory updated, however, it may generate
  // too many editlog & rocksdb updates, prolonging the scrub.
  // So currently, we calculate the current effective StorageClass as needed.
  // Clean the deprecated xattr incrementally below.
  uint32_t val;
  if (depred_xattr_removed_ <
          FLAGS_lifecycle_scrub_remove_depred_xattr_per_scurb &&
      XAttrs::GetUInt32XAttr(inode, kStorageClassXAttr, &val)) {
    depred_xattr_removed_++;
    INode dummy_inode;
    XAttrProto xattr_to_remove;
    XAttrs::SetUInt32XAttr(
        kStorageClassXAttr, 0, &dummy_inode, &xattr_to_remove);
    SynchronizedRpcClosure done;
    namespace_->AsyncRemoveXAttr(fullpath,
                                 xattr_to_remove,
                                 LogRpcInfo(),
                                 UserGroupInfo("root", "supergroup"),
                                 &done);
    done.Await();
    if (!done.status().IsOK()) {
      LOG(WARNING) << absl::StrFormat(
          "Failed to remove deprecated xattr %s of inode %s: %s",
          kStorageClassXAttr,
          fullpath,
          done.status().ToString());
    }
  }
}

void LifecycleScanScrubOp::GenerateStat(const INode& file,
                                        StorageClassProto cur_cls) {
  for (const BlockProto& blk : file.blocks()) {
    BlockID blkid = blk.blockid();
    BlockInfoProto bip;
    bool ok = meta_storage_->GetBlockInfo(blkid, &bip);
    if (!ok || (bip.state() != BlockInfoProto::kPersisted)) {
      // only persisted block can start its lifecycle
      continue;
    }

    scrub_stat_.num_logical_block_expected[cur_cls] += 1;
    scrub_stat_.num_logical_byte_expected[cur_cls] += blk.numbytes();

    std::vector<std::string> dnuuids;
    std::vector<StorageClassReportProto> reports;
    meta_storage_->GetStorageClassReports(blkid, &dnuuids, &reports);
    CHECK_EQ(dnuuids.size(), reports.size());
    if (reports.empty()) {
      continue;
    }
    bool found_valid_replica = false;
    for (int i = 0; i < reports.size(); i++) {
      auto rpt = reports.at(i);
      CHECK(rpt.has_stcls());
      int cls_id = static_cast<int>(rpt.stcls());
      CHECK_NE(cls_id, StorageClassProto::NONE);

      if (!found_valid_replica) {
        found_valid_replica = true;
        scrub_stat_.num_logical_block[cur_cls] += 1;
        scrub_stat_.num_logical_byte[cur_cls] += blk.numbytes();
      }
      scrub_stat_.num_physical_replica[cls_id] += 1;
      scrub_stat_.num_physical_byte[cls_id] += blk.numbytes();
    }
  }
  scrub_stat_.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
}

void LifecycleScanScrubOp::GenerateCmd(const INodeID fileid,
                                       StorageClassProto cur_cls) {
  // skip for standby
  if (!namespace_->ha_state()->IsActive() ||
      namespace_->ha_state()->InTransition()) {
    return;
  }

  // XXX do not generate command for TOS_LOCAL
  if (namespace_->namespace_type() == cloudfs::NamespaceType::TOS_LOCAL) {
    return;
  }

  // get current INode, which may been deleted or transited before.
  INode file;
  auto sc = meta_storage_->GetINode(fileid, &file);
  if (sc != kOK) {
    return;
  }

  for (const BlockProto& blk : file.blocks()) {
    BlockID blkid = blk.blockid();
    BlockInfoProto bip;
    bool ok = meta_storage_->GetBlockInfo(blkid, &bip);
    if (!ok || (bip.state() != BlockInfoProto::kPersisted)) {
      // only persisted block can start its lifecycle
      continue;
    }

    std::vector<std::string> dnuuids;
    std::vector<StorageClassReportProto> reports;
    meta_storage_->GetStorageClassReports(blkid, &dnuuids, &reports);
    CHECK_EQ(dnuuids.size(), reports.size());
    if (reports.empty()) {
      continue;
    }

    std::string cls_name;
    ok = StorageClassID2Name(cur_cls, &cls_name);
    CHECK(ok);
    switch (cur_cls) {
      case StorageClassProto::AR:
        // fall through
      case StorageClassProto::IA: {
        // fall through
        LOG(WARNING) << absl::StrFormat(
            "unsupported transition to %s for inode %lu, fallback to COLD",
            cls_name, file.id());
      }
      case StorageClassProto::COLD: {
        // transit all replicas to COLD
        HandleTransitionDefault(blkid, dnuuids, reports, cur_cls);
        break;
      }
      case StorageClassProto::WARM: {
        // transit only one replica to WARM, the other COLD
        HandleTransitionWithOtherCold(blkid, dnuuids, reports, cur_cls);
        break;
      }
      case StorageClassProto::HOT: {
        // transit only one replica to HOT, the other COLD
        HandleTransitionWithOtherCold(blkid, dnuuids, reports, cur_cls);
        break;
      }
      case StorageClassProto::NONE: {
        // stands for deleted report, ignore it
        break;
      }
      default: {
        LOG(FATAL) << absl::StrFormat(
            "unexpected target storage class %s on inode %lu",
            cls_name, file.id());
      }
    }
  }
}

}  // namespace dancenn
