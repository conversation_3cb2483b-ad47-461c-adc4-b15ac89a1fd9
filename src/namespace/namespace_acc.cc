//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include <gflags/gflags.h>
#include <glog/logging.h>

#include "acc/meta_sync_context.h"
#include "acc/task/sync_task.h"
#include "base/closure.h"
#include "base/logger_metrics.h"
#include "base/lru_cache.h"
#include "base/path_util.h"
#include "create_flag.h"
#include "namespace.h"
#include "namespace/locked_path.h"
#include "ufs/ufs.h"
#include "ufs/ufs_dir_sync_actions.h"
#include "ufs/ufs_util.h"

DECLARE_bool(meta_storage_snapshot_read_enabled);
DECLARE_bool(enable_write_back_task_persistence);
DECLARE_uint64(dfs_block_size);
DECLARE_uint32(edit_log_slow_op_us);
DECLARE_uint32(acc_batch_add_editlog_batch_size);
DECLARE_uint64(acc_batch_add_editlog_batch_max_size_bytes);
DECLARE_bool(enable_write_back);
DECLARE_bool(nn_drive_upload);
DECLARE_int32(sync_file_from_ufs_slow_time_ms);
DECLARE_int32(upload_file_to_ufs_slow_time_ms);
DECLARE_bool(trace_rpc_log);
DECLARE_bool(ignore_sync_action_update_time);
DECLARE_bool(ufs_sync_listing_prefetch_only_once);
DECLARE_uint32(ufs_sync_listing_page_size);
DECLARE_int32(ufs_sync_min_interval);
DECLARE_bool(ufs_delete_for_overwrite);
DECLARE_bool(ufs_delete_for_overwrite_appendable_file);
DECLARE_bool(write_back_always_check_ufs);
DECLARE_bool(log_ufs_sync_detail);
DECLARE_bool(log_ufs_persist_detail);
DECLARE_bool(ufs_sync_mkdir_create_in_ufs);
DECLARE_bool(enable_ufs_sync_in_rename);
DECLARE_bool(enable_ufs_sync_in_operation);
DECLARE_bool(ufs_sync_delete_local_dir_ufs_not_exist);
DECLARE_uint64(dir_lock_retry_sleep_time_ms);
DECLARE_bool(ufs_rename_update_local_etag);
DECLARE_bool(ufs_tos_shallow_copy_enabled);
DECLARE_uint64(ufs_tos_shallow_copy_max_count);

// TODO(guojun.john): 处理 parent mtime
// TODO(guojun.john): lcov 覆盖率拉满
// TODO(guojun.john): Sync 锁粒度优化，使用读锁

namespace dancenn {

// Param:
//  node: can be an empty node, which means local file not existed
FileSyncContextPtr CreateFileSyncContextNoLock(const std::string& ufs_path,
                                               const std::string& inner_path,
                                               const PermissionStatus& perm,
                                               const UserGroupInfo& ugi,
                                               const std::shared_ptr<Ufs>& ufs,
                                               const INode& parent,
                                               const INode& node) {
  auto file_ctx =
      std::make_shared<FileSyncContext>(ufs_path, inner_path, perm, ugi, ufs);
  if (CheckValidINode(parent)) {
    file_ctx->parent = parent;
  }
  if (CheckValidINode(node)) {
    file_ctx->iip.MutableInodeUnsafe() = node;
  }
  file_ctx->create_parent = false;
  SplitPath(inner_path, &file_ctx->path_components);
  return file_ctx;
}

#define MFC_EXIST(instance, metrics) \
  if (instance) {                    \
    MFC(instance->metrics)->Inc();   \
  }

Status ComputeFileSyncAction(const std::string& inner_path,
                             const INode* local_file,
                             const UfsFileStatus* file,
                             const std::string& ufs_path,
                             const std::shared_ptr<Ufs>& ufs,
                             FileSyncAction* action,
                             NameSpaceMetrics* metrics = nullptr) {
  if (local_file == nullptr) {
    if (nullptr == file) {
      *action = FILE_ACTION_NONE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_both_not_exist_);
      return Status::OK();
    }

    CHECK(file != nullptr);
    *action = FILE_ACTION_CREATE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_create_exist_in_ufs_);
    return Status::OK();
  }

  // CHECK(local_file != nullptr);
  if (local_file->ufs_file_info().file_state() ==
      UfsFileState::kUfsFileStateToBePersisted) {
    *action = FILE_ACTION_NONE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_file_not_upload_);
    return Status::OK();
  }
  if (nullptr == file) {
    bool should_delete_local_dir =
        FLAGS_ufs_sync_mkdir_create_in_ufs &&
        FLAGS_ufs_sync_delete_local_dir_ufs_not_exist;
    if (local_file->ufs_file_info().file_state() ==
        UfsFileState::kUfsFileStateToBePersisted) {
      *action = FILE_ACTION_NONE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_file_not_upload_);
    } else if (local_file->type() == INode_Type_kDirectory &&
               !should_delete_local_dir) {
      // CFS will not immediately synchronize the directory to UFS.
      // So we cannot determine who is the latest data.
      *action = FILE_ACTION_NONE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_dir_not_exist_);
    } else {
      *action = FILE_ACTION_DELETE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_delete_dir_not_exist_);
    }
    return Status::OK();
  }

  // CHECK(file != nullptr);
  CHECK(local_file->name() == file->FileName());

  // Local is directory
  if (local_file->type() == INode_Type_kDirectory) {
    if (file->FileType() == UFS_DIR) {
      *action = FILE_ACTION_UPDATE_TIME;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_dir_remote_dir_);
      return Status::OK();
    }

    // Check if directory with same name existed, this is valid in Object
    // storage
    UfsDirStatus dir;
    auto ufs_s = ufs->GetDirectoryStatus(ufs_path, &dir);
    if (!ufs_s.IsOK() && ufs_s.code() != Code::kDirNotFound) {
      LOG(INFO) << "Failed to check if dir with same name existed: " << ufs_path
                << ", error: " << ufs_s.ToString();
      return ufs_s;
    }

    // UFS directory existed
    if (ufs_s.IsOK()) {
      LOG(INFO) << "UFS file has both dir and file with same path: "
                << file->FullPath() << ", inner_path: " << inner_path
                << ", will choose ufs dir as local dir existed.";
      *action = FILE_ACTION_UPDATE_TIME;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_dir_remote_both_);
      return Status::OK();
    }

    // Remote is file not directory
    LOG(INFO) << "UFS is file but local is directory, will overwrite local "
                 "dir with file. ufs: "
              << file->FullPath() << ", inner_path: " << inner_path;

    *action = FILE_ACTION_OVERWRITE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_dir_ufs_file_);
    return Status::OK();
  }

  CHECK(local_file->type() == INode_Type_kFile);
  if (file->FileType() == UFS_DIR) {
    // Remote is file not directory
    LOG(INFO) << "UFS is dir but local is file, will overwrite local file "
                 "with dir. ufs: "
              << file->FullPath() << ", inner_path: " << inner_path;

    *action = FILE_ACTION_OVERWRITE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_file_ufs_dir_);
    return Status::OK();
  }

  CHECK(file->FileType() == UFS_FILE);
  // process file
  const std::string& local_etag = local_file->ufs_file_info().etag();
  const std::string& remote_etag = file->Etag();
  if (!remote_etag.empty()) {
    if (local_etag == remote_etag) {
      *action = FILE_ACTION_UPDATE_TIME;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_file_etag_);
    } else {
      *action = FILE_ACTION_OVERWRITE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_file_etag_);
    }
    return Status::OK();
  }

  // in ACC_HDFS etag is empty
  uint64_t local_mtime = local_file->ufs_file_info().last_modified_ts();
  int64_t remote_mtime = file->modified_ts();
  if (local_mtime < remote_mtime) {
    *action = FILE_ACTION_OVERWRITE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_file_mtime_);
  } else {
    *action = FILE_ACTION_UPDATE_TIME;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_file_mtime_);
  }
  return Status::OK();
}

// +----------------+-----------------------+--------------+
// |                | UFS FILE              | UFS NotFound |
// +----------------+-----------------------+--------------+
// | Local FILE     | UPDATE_TIME/OVERWRITE | DELETE/NONE  |
// +----------------+-----------------------+--------------+
// | Local DIR      | NONE                  | NONE         |
// +----------------+-----------------------+--------------+
// | Local NotFound | CREATE                | NONE         |
// +----------------+-----------------------+--------------+
Status ComputeFileSyncActionUfsFileOnly(const INode* local_file,
                                        const UfsFileStatus* ufs_file,
                                        FileSyncAction* action,
                                        NameSpaceMetrics* metrics) {
  // Local not found
  if (local_file == nullptr) {
    // Local not found, UFS not found -> do nothing
    if (ufs_file == nullptr) {
      *action = FILE_ACTION_NONE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_both_not_exist_);
      return Status::OK();
    }

    // Local not found, UFS is file -> create
    CHECK(ufs_file != nullptr);
    CHECK(ufs_file->FileType() == UFS_FILE);
    *action = FILE_ACTION_CREATE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_create_exist_in_ufs_);
    return Status::OK();
  }

  // Local is directory -> do nothing
  if (local_file->type() == INode_Type_kDirectory) {
    *action = FILE_ACTION_NONE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_dir_file_only_);
    return Status::OK();
  }

  // Local is file
  CHECK(local_file->type() == INode_Type_kFile);
  if (local_file->ufs_file_info().file_state() ==
      UfsFileState::kUfsFileStateToBePersisted) {
    // Local is file, Local ToBePersisted -> no nothing
    *action = FILE_ACTION_NONE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_file_not_upload_);
    return Status::OK();
  }
  // Local is file, UFS not found
  if (ufs_file == nullptr) {
    if (local_file->ufs_file_info().file_state() ==
        UfsFileState::kUfsFileStateToBePersisted) {
      // Local is file, UFS not found, Local ToBePersisted -> no nothing
      *action = FILE_ACTION_NONE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_none_file_not_upload_);
    } else {
      // Local is file, UFS not found, Local NOT ToBePersisted -> delete
      *action = FILE_ACTION_DELETE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_delete_file_not_exist_);
    }
    return Status::OK();
  }

  // Local is file, UFS is file
  CHECK(local_file->name() == ufs_file->FileName());
  CHECK(local_file->type() == INode_Type_kFile);
  CHECK(ufs_file->FileType() == UFS_FILE);
  const std::string& local_etag = local_file->ufs_file_info().etag();
  const std::string& remote_etag = ufs_file->Etag();
  if (!remote_etag.empty()) {
    if (local_etag == remote_etag) {
      // Local is file, UFS is file, etag matches -> update time
      *action = FILE_ACTION_UPDATE_TIME;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_file_etag_);
    } else {
      // Local is file, UFS is file, etag mismatches -> overwrite
      *action = FILE_ACTION_OVERWRITE;
      MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_file_etag_);
    }
    return Status::OK();
  }

  // in ACC_HDFS etag is empty
  uint64_t local_mtime = local_file->ufs_file_info().last_modified_ts();
  int64_t remote_mtime = ufs_file->modified_ts();
  if (local_mtime < remote_mtime) {
    *action = FILE_ACTION_OVERWRITE;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_update_file_mtime_);
  } else {
    *action = FILE_ACTION_UPDATE_TIME;
    MFC_EXIST(metrics, acc_sync_ufs_compute_action_overwrite_file_mtime_);
  }
  return Status::OK();
}
#undef MFC_EXIST

std::string NameSpace::InnerPathToUfsPath(const std::string& inner_path) {
  return JoinTwoPath(ufs_conf_->ufs_prefix, inner_path);
}

Status NameSpace::SyncUfsFile(const std::string& ufs_path,
                              const std::string& inner_path,
                              const PermissionStatus& p,
                              const UserGroupInfo& ugi,
                              const std::shared_ptr<Ufs>& ufs,
                              bool sync_on_ancestor_not_dir,
                              uint64_t target_sync_ts,
                              StopWatchContext* rpc_sw_ctx,
                              TriggerSyncReason trigger_sync_reason) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFile started. ufs_path: " << ufs_path
      << ", inner_path: " << inner_path;


  auto s = SyncUfsFileInternal(ufs_path,
                               inner_path,
                               p,
                               ugi,
                               ufs,
                               sync_on_ancestor_not_dir,
                               target_sync_ts,
                               rpc_sw_ctx,
                               trigger_sync_reason);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after SyncUfsFileInternal");

  if (s.IsOK()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncUfsFile finished. ufs_path: " << ufs_path
        << ", inner_path: " << inner_path;
  } else {
    LOG(INFO) << "Failed to sync ufs file. ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }
  return std::move(s);
}

Status NameSpace::SyncUfsFileInternal(const std::string& ufs_path,
                                      const std::string& inner_path,
                                      const PermissionStatus& p,
                                      const UserGroupInfo& ugi,
                                      const std::shared_ptr<Ufs>& ufs,
                                      bool sync_on_ancestor_not_dir,
                                      uint64_t target_sync_ts,
                                      StopWatchContext* rpc_sw_ctx,
                                      TriggerSyncReason trigger_sync_reason) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> ctx;
  vshared_lock ha_barrier;

  auto s = PrepareSyncUfsFile(ufs_path,
                              inner_path,
                              p,
                              ugi,
                              ufs,
                              sync_on_ancestor_not_dir,
                              &ctx,
                              &locked_path,
                              &ha_barrier,
                              rpc_sw_ctx);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after PrepareSyncUfsFile");
  RETURN_NOT_OK(s);

  // avoid duplicate sync
  uint64_t sync_ts = 0;
  if (ctx->iip.Inode().type() == INode_Type_kFile) {
    sync_ts = ctx->iip.Inode().ufs_file_info().sync_ts();
  } else {
    sync_ts = ctx->iip.Inode().ufs_dir_info().sync_ts();
  }
  // unit is second
  if (sync_ts > target_sync_ts) {
    // s = Status(Code::kUfsAlreadySynced, "UFS dir already synced.");
    // just return OK
    return {};
  }

  // do sync
  ctx->trigger_sync_reason = trigger_sync_reason;
  s = SyncUfsFileInnerWithWriteLock(ctx);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after SyncUfsFileInnerWithWriteLock");
  RETURN_NOT_OK(s);

  return s;
}

Status NameSpace::SyncUfsFileOnly(const std::string& ufs_path,
                                  const std::string& inner_path,
                                  const PermissionStatus& p,
                                  const UserGroupInfo& ugi,
                                  const std::shared_ptr<Ufs>& ufs,
                                  bool check_etag,
                                  const std::string& etag,
                                  StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFileOnly started. ufs_path: " << ufs_path
      << ", inner_path: " << inner_path;

  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    s = PrepareSyncUfsFile(ufs_path,
                           inner_path,
                           p,
                           ugi,
                           ufs,
                           /*sync_on_ancestor_not_dir=*/true,
                           &ctx,
                           &locked_path,
                           &ha_barrier,
                           rpc_sw_ctx);
    if (!s.IsOK()) {
      break;
    }

    if (check_etag && ctx->IsNodeValid() &&
        ctx->iip.Inode().ufs_file_info().etag() == etag) {
      break;
    }

    s = SyncUfsFileOnlyWithWriteLock(ctx, rpc_sw_ctx);
    if (!s.IsOK()) {
      break;
    }
  } while (0);
  if (s.IsOK()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncUfsFileOnly finished. ufs_path: " << ufs_path
        << ", inner_path: " << inner_path;

  } else {
    LOG(INFO) << "Failed to sync ufs file only. ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }
  return std::move(s);
}

Status NameSpace::PrepareSyncUfsFile(const std::string& ufs_path,
                                     const std::string& inner_path,
                                     const PermissionStatus& p,
                                     const UserGroupInfo& ugi,
                                     const std::shared_ptr<Ufs>& ufs,
                                     bool sync_on_ancestor_not_dir,
                                     std::shared_ptr<FileSyncContext>* c,
                                     std::unique_ptr<LockedPath>* lp,
                                     vshared_lock* ha_barrier,
                                     StopWatchContext* rpc_sw_ctx,
                                     bool need_log) {
  StopWatch sw(metrics_.acc_prepare_sync_ufs_file_time_);
  sw.Start();
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  auto ctx =
      std::make_shared<FileSyncContext>(ufs_path, inner_path, p, ugi, ufs);
  ctx->set_sync_on_ancestor_not_dir(sync_on_ancestor_not_dir);

  auto s = CheckPathAndSplitComponents(ctx->inner_path,
                                       false /*allow_src_root*/,
                                       &ctx->path_components,
                                       &ctx->lock_components);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after CheckPathAndSplitComponents");
  RETURN_NOT_OK(s);

  s = CheckOperationSafeMode(ha_barrier, OperationsCategory::kWrite);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after CheckOperationSafeMode");
  RETURN_NOT_OK(s);

  std::unique_ptr<LockedPath> lockedpath;
  std::tie(s, lockedpath) = PrepareSyncUfsFileResolvePathAndLock(ctx);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after PrepareSyncUfsFileResolvePathAndLock");

  if (s.IsOK()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail || need_log)
        << "Prepare sync finished for ufs_path: " << ufs_path
        << ", inner_path: " << inner_path << ", parent: " << ctx->parent.id()
        << ", inode id: " << ctx->iip.Inode().id();

    ctx->set_locked_path(lockedpath.get());
    *c = std::move(ctx);
    *lp = std::move(lockedpath);
  } else {
    LOG(INFO) << "Prepare sync failed for ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }

  RPC_SW_CTX_LOG(rpc_sw_ctx, "return PrepareSyncUfsFile");
  return std::move(s);
}

std::pair<Status, std::unique_ptr<LockedPath>> NameSpace::
    PrepareSyncUfsFileResolvePathAndLock(
        const std::shared_ptr<FileSyncContext>& ctx,
        StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  if (ctx->path_components.empty()) {
    // root, only hold read lock for root dir sync as it's a dummy sync,
    auto locked_path = std::make_unique<LockedPath>(
        PathLockType::kPathLockTypeRead, ctx->inner_path, this);
    if (locked_path->ResolveAndLock().IsOK()) {
      ctx->iip.MutableInodeUnsafe() = GetRootINode();
      ctx->parent = ctx->iip.Inode();
      return {Status(), std::move(locked_path)};
    } else {
      return {
          Status(JavaExceptions::kIOException,
                 Code::kError,
                 "Failed to resolve path for root, ufs_path: " + ctx->ufs_path),
          std::move(locked_path)};
    }
  }

  bool is_retry = false;
  while (true) {
    // Hold the write lock during the entire sync period for now
    auto locked_path = std::make_unique<LockedPath>(
        PathLockType::kPathLockTypeWrite, ctx->inner_path, this);
    Status resolve_s = locked_path->ResolveAndLock();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after ResolveAndLock");
    if (!resolve_s.IsOK()) {
      LOG(INFO) << "Failed to resolve path: " << ctx->inner_path
                << ", error: " << resolve_s.ToString();
      return {resolve_s, nullptr};
    }

    auto state = locked_path->state();
    switch (state) {
      case ResolveState::kResolveAncestorNotDir: {
        RPC_SW_CTX_LOG(rpc_sw_ctx, "switch kResolveAncestorNotDir");
        std::string notdir_ancestor_inner_path =
            locked_path->GetLastLockedNodePath();
        std::string notdir_ancestor_ufs_path =
            InnerPathToUfsPath(notdir_ancestor_inner_path);
        if (ctx->sync_on_ancestor_not_dir() && !is_retry) {
          // release lock
          locked_path.reset();

          // target_sync_ts to UINT64_MAX to force sync
          Status sync_s = SyncUfsFile(notdir_ancestor_ufs_path,
                                      notdir_ancestor_inner_path,
                                      ctx->perm,
                                      ctx->ugi,
                                      ctx->ufs,
                                      /*sync_on_ancestor_not_dir=*/false,
                                      /*target_sync_ts=*/UINT64_MAX,
                                      rpc_sw_ctx,
                                      TriggerSyncReason::RESOLVE_ANCESTOR_NOT_DIR);
          RPC_SW_CTX_LOG(rpc_sw_ctx, "after SyncUfsFile");
          if (sync_s.IsOK()) {
            is_retry = true;
            continue;
          }
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "return kFileNotFound");
        LOG(INFO) << "Failed to prepare sync on path: " << ctx->ufs_path
                  << ", parent not dir: " << notdir_ancestor_ufs_path;
        return {Status(JavaExceptions::Exception::kParentNotDirectoryException,
                       Code::kFileNotFound,
                       "Ancestor not dir."),
                nullptr};
      }
      case ResolveState::kResolveOK: {
        RPC_SW_CTX_LOG(rpc_sw_ctx, "switch kResolveOK");

        auto&& ancestors = locked_path->GetLockedAncestors();
        CHECK(!ancestors.empty());

        auto&& parent = ancestors.back();
        auto&& name = ctx->path_components.back().as_string();
        INode node;
        auto code = meta_storage_->GetINode(parent.id(), name, &node);
        RPC_SW_CTX_LOG(rpc_sw_ctx, "after GetINode");
        if (code != kFileNotFound && code != kOK) {
          return {Status(JavaExceptions::kIOException,
                         Code::kError,
                         "Failed to get inode for path: " + ctx->inner_path),
                  nullptr};
        }

        ctx->ancestors = ancestors;
        ctx->parent = parent;
        UpdateINodeAttrs(ctx->parent, locked_path->GetAttrHolder());
        RPC_SW_CTX_LOG(rpc_sw_ctx, "after parent UpdateINodeAttrs");
        if (code == kOK) {
          ctx->iip.MutableInodeUnsafe() = std::move(node);
          ctx->iip.CollectAttrs(locked_path->GetAttrHolder());
          ctx->iip.FinalizeAttrs();
          RPC_SW_CTX_LOG(rpc_sw_ctx, "after node UpdateINodeAttrs");
        } else {
          CHECK(code == kFileNotFound);
          ctx->iip.MutableInodeUnsafe().set_id(kInvalidINodeId);
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "return OK");
        return {Status(), std::move(locked_path)};
      }
      case ResolveState::kResolveAncestorNotFound: {
        RPC_SW_CTX_LOG(rpc_sw_ctx, "switch kResolveAncestorNotFound");
        ctx->create_parent = true;
        ctx->parent.set_id(kInvalidINodeId);
        ctx->iip.MutableInodeUnsafe().set_id(kInvalidINodeId);
        RPC_SW_CTX_LOG(rpc_sw_ctx, "return OK");
        return {Status(), std::move(locked_path)};
      }
      default:
        LOG(INFO) << "Failed to resolve and lock. ufs_path: " << ctx->ufs_path
                  << ", inner_path: " << ctx->inner_path
                  << ", resolve_state: " << static_cast<int>(state);
        return {Status(JavaExceptions::kIOException,
                       Code::kError,
                       "Failed to resolve and lock."),
                nullptr};
    }
  }
}

Status NameSpace::SyncUfsFileInnerWithWriteLock(
    const std::shared_ptr<FileSyncContext>& ctx,
    StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  // Is root
  if (UNLIKELY(ctx->path_components.empty())) {
    // Don't sync on root path of this mount
    return Status::OK();
  }

  StopWatch sw(metrics_.acc_sync_ufs_file_get_ufs_file_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFile will get ufs file. ufs_path: " << ctx->ufs_path
      << ", trigger_sync_reason: " << ctx->trigger_sync_reason;

  UfsFileStatus file_status;
  auto ufs_s = ctx->ufs->GetFileStatus(ctx->ufs_path, &file_status);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after GetFileStatus");
  if (!ufs_s.IsOK() && ufs_s.code() != Code::kFileNotFound) {
    LOG(INFO) << "Failed to get file status for path: " << ctx->ufs_path
              << ", trigger_sync_reason: " << ctx->trigger_sync_reason
              << ", error: " << ufs_s.ToString();
    return ufs_s;
  }

  sw.NextStep(metrics_.acc_sync_ufs_file_compute_action_time_);
  FileSyncAction action;
  RPC_SW_CTX_LOG(rpc_sw_ctx, "before ComputeFileSyncAction");
  Status s =
      ComputeFileSyncAction(ctx->inner_path,
                            ctx->IsNodeValid() ? &ctx->iip.Inode() : nullptr,
                            ufs_s.IsOK() ? &file_status : nullptr,
                            ctx->ufs_path,
                            ctx->ufs,
                            &action,
                            &metrics_);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ComputeFileSyncAction");
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to compute file sync action. ufs_path: "
               << ctx->ufs_path
               << ", trigger_sync_reason: " << ctx->trigger_sync_reason
               << ", error: " << s.ToString();

    return s;
  }

  LOG(INFO) << "SyncUfsFile action: " << ToString(action)
            << ", ufs_path: " << ctx->ufs_path
            << ", inner_path: " << ctx->inner_path
            << ", trigger_sync_reason: " << ctx->trigger_sync_reason;
  CHECK(action >= FILE_ACTION_NONE && action < FILE_ACTION_INVALID)
      << "Invalid action: " << action;
  sw.NextStep();

  RPC_SW_CTX_LOG(rpc_sw_ctx, "before ApplyFileSyncAction");
  s = ApplyFileSyncAction(action, ctx, file_status);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ApplyFileSyncAction");
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to sync ufs file. ufs: " << ctx->ufs_path
              << ", inner: " << ctx->inner_path << ", error: " << s.ToString();
  }
  return s;
}

Status NameSpace::SyncUfsFileOnlyWithWriteLock(
    const std::shared_ptr<FileSyncContext>& ctx,
    StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  // Is root
  if (UNLIKELY(ctx->path_components.empty())) {
    // Don't sync on root path of this mount
    return Status::OK();
  }

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFileOnly will get ufs file and file only. ufs_path: "
      << ctx->ufs_path;

  StopWatch sw(metrics_.acc_sync_ufs_file_only_get_ufs_file_time_);
  sw.Start();
  UfsFileStatus ufs_file_status;
  RPC_SW_CTX_LOG(rpc_sw_ctx, "before GetFileOnlyStatus");
  Status ufs_s = ctx->ufs->GetFileOnlyStatus(ctx->ufs_path, &ufs_file_status);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after GetFileOnlyStatus");
  if (!ufs_s.IsOK() && ufs_s.code() != Code::kFileNotFound) {
    LOG(INFO) << "Failed to get file only status for path: " << ctx->ufs_path
              << ", error: " << ufs_s.ToString();
    return ufs_s;
  }

  sw.NextStep(metrics_.acc_sync_ufs_file_only_compute_action_time_);
  FileSyncAction action;
  RPC_SW_CTX_LOG(rpc_sw_ctx, "before ComputeFileSyncActionUfsFileOnly");
  Status s = ComputeFileSyncActionUfsFileOnly(
      ctx->IsNodeValid() ? &ctx->iip.Inode() : nullptr,
      ufs_s.IsOK() ? &ufs_file_status : nullptr,
      &action,
      &metrics_);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ComputeFileSyncActionUfsFileOnly");
  if (!s.IsOK()) {
    LOG(ERROR)
        << "Failed to compute file sync action for ufs file only. ufs_path: "
        << ctx->ufs_path << ", error: " << s.ToString();
    return s;
  }

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFileOnly action: " << ToString(action)
      << ", ufs_path: " << ctx->ufs_path << ", inner_path: " << ctx->inner_path;

  CHECK(action >= FILE_ACTION_NONE && action < FILE_ACTION_INVALID)
      << "Invalid action: " << action;

  sw.NextStep(metrics_.acc_sync_ufs_file_only_apply_action_time_);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "before ApplyFileSyncAction");
  s = ApplyFileSyncAction(action, ctx, ufs_file_status);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ApplyFileSyncAction");
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to sync ufs file only. ufs: " << ctx->ufs_path
              << ", inner: " << ctx->inner_path << ", error: " << s.ToString();
  }
  return s;
}

Status NameSpace::ApplyFileSyncAction(
    FileSyncAction action,
    const std::shared_ptr<FileSyncContext>& ctx,
    const UfsFileStatus& file_status,
    StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  bool should_wait_async = true;
  SynchronizedRpcClosure done;
  Status s;
  switch (action) {
    case FILE_ACTION_NONE: {
      MFC(metrics_.acc_sync_ufs_apply_action_none_)->Inc();
      RPC_SW_CTX_LOG(rpc_sw_ctx, "action=FILE_ACTION_NONE");
      should_wait_async = false;
      break;
    }
    case FILE_ACTION_CREATE: {
      RPC_SW_CTX_LOG(rpc_sw_ctx, "action=FILE_ACTION_CREATE");
      MFC(metrics_.acc_sync_ufs_apply_action_create_)->Inc();
      s = ProcessFileSyncActionCreate(ctx, file_status, &done);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ProcessFileSyncActionCreate");
      break;
    }
    case FILE_ACTION_DELETE: {
      RPC_SW_CTX_LOG(rpc_sw_ctx, "action=FILE_ACTION_DELETE");
      MFC(metrics_.acc_sync_ufs_apply_action_delete_)->Inc();
      s = ProcessFileSyncActionDelete(ctx, &done);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ProcessFileSyncActionDelete");
      break;
    }
    case FILE_ACTION_OVERWRITE: {
      RPC_SW_CTX_LOG(rpc_sw_ctx, "action=FILE_ACTION_OVERWRITE");
      MFC(metrics_.acc_sync_ufs_apply_action_overwrite_)->Inc();
      s = ProcessFileSyncActionOverwrite(ctx, file_status, &done);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ProcessFileSyncActionOverwrite");
      break;
    }
    case FILE_ACTION_UPDATE_TIME: {
      RPC_SW_CTX_LOG(rpc_sw_ctx, "action=FILE_ACTION_UPDATE_TIME");
      MFC(metrics_.acc_sync_ufs_apply_action_update_time_)->Inc();
      s = ProcessFileSyncActionUpdateTime(ctx, &done);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ProcessFileSyncActionUpdateTime");
      break;
    }
    default: {
      LOG(FATAL) << "Unknown action: " << action;
      break;
    }
  }

  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to start async process of ufs file. ufs: "
               << ctx->ufs_path << ", inner: " << ctx->inner_path
               << ", error: " << s.ToString();
    return s;
  }

  if (should_wait_async) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "before done.Await()");
    done.Await();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after done.Await()");
    s = done.status();
  }

  return s;
}

Status NameSpace::CreateMissingParentInSync(
    const std::shared_ptr<FileSyncContext>& ctx) {
  CHECK(ctx->locked_path() != nullptr);
  INode parent;
  auto s = CreateMissingParentLocal(ctx->ufs,
                                    ctx->ufs_path,
                                    ctx->inner_path,
                                    ctx->path_components,
                                    ctx->lock_components,
                                    ctx->perm,
                                    ctx->locked_path()->path(),
                                    true,
                                    nullptr,
                                    &parent);
  if (!s.IsOK()) {
    return s;
  }
  ctx->parent = std::move(parent);
  return Status::OK();
}

Status NameSpace::CreateMissingParentLocal(
    const std::shared_ptr<Ufs>& ufs,
    const std::string& ufs_path,
    const std::string& inner_path,
    const std::vector<cnetpp::base::StringPiece>& path_comps,
    const std::vector<cnetpp::base::StringPiece>& lock_comps,
    const PermissionStatus& perm,
    const RichPath& rp,
    bool inherit_parent_attrs,
    std::vector<INode>* ancestors,
    INode* p) {
  CHECK(!rp.ancestors.empty());

  INode parent = rp.ancestors.back();
  uint64_t parent_id = parent.id();
  std::vector<INode> ancestors_holder = rp.ancestors;
  std::vector<INodeID> ancestors_id;
  std::for_each(rp.ancestors.begin(),
                rp.ancestors.end(),
                [&ancestors_id](const INode& inode) {
                  ancestors_id.push_back(inode.id());
                });
  int32_t path_comp_index = rp.ancestors.size() - 1;

  std::string parent_group = parent.permission().groupname();
  PermissionStatus new_perm(perm);

  if (path_comp_index == 0) {
    CHECK(parent.id() == kRootINodeId);
  } else {
    CHECK(parent.name() == path_comps[path_comp_index - 1]);
  }

  // Skip already existed
  for (; path_comp_index < path_comps.size() - 1; ++path_comp_index) {
    std::string name = path_comps[path_comp_index].as_string();
    INode inode;
    StatusCode code = meta_storage_->GetINode(parent_id, name, &inode);
    if (code == kFileNotFound) {
      break;
    }
    if (code != kOK) {
      LOG(INFO) << "Failed to get inode in creating missing parent. ufs_path: "
                << ufs_path << ", inner_path: " << inner_path
                << ", error: " << code;
      return Status(JavaExceptions::kIOException,
                    Code::kError,
                    "Failed to get inode in creating missing parent.");
    }

    if (inode.type() != INode_Type_kDirectory) {
      LOG(INFO) << "Failed to create missing parent, one of ancestors not dir: "
                << name << ", path_comp_index: " << path_comp_index;
      return Status(JavaExceptions::kParentNotDirectoryException,
                    Code::kError,
                    "Ancestors not directory: " + name);
    }

    if (inode.permission().has_groupname() &&
        !inode.permission().groupname().empty()) {
      parent_group = inode.permission().groupname();
    } else {
      inode.mutable_permission()->set_groupname(parent_group);
    }

    parent_id = inode.id();
    parent = std::move(inode);
    ancestors_holder.push_back(parent);
    ancestors_id.push_back(parent_id);
  }

  if ((!new_perm.has_groupname()) || new_perm.groupname().empty()) {
    if (parent_group.empty()) {
      new_perm.set_groupname(kDfsPermissionsSuperUserGroupDefault);
    } else {
      new_perm.set_groupname(parent_group);
    }
  }

  if ((!new_perm.has_username()) || new_perm.username().empty()) {
    // HDFS mode use ugi.current_user(), but this is weird since current_user
    // and remote_user always the same?
    LOG(INFO) << "Permission username is empty. ufs_path: " << ufs_path;
  }

  Status s;
  do {
    if (path_comp_index >= path_comps.size() - 1) {
      break;
    }
    SynchronizedRpcClosure done;
    for (; path_comp_index < path_comps.size() - 1; ++path_comp_index) {
      std::string name = path_comps[path_comp_index].as_string();
      INode inode;
      MakeINode(NextINodeId(),
                parent_id,
                path_comps[path_comp_index].as_string(),
                new_perm,
                INode_Type_kDirectory,
                &inode);
      UfsUtil::FillIncompleteUfsDirInode(&inode);
      if (inherit_parent_attrs) {
        UpdateINodeAttrs(inode, rp.ancestors.back(), true);
      }

      INodeInPath parent_iip;
      parent_iip.MutableInodeUnsafe() = parent;
      parent_iip.CollectSnapshotInfo(parent);
      if (inherit_parent_attrs) {
        parent_iip.CollectAttrs(rp.ancestors.back());
      }
      parent_iip.SetModificationTime(inode.mtime());
      SnapshotLog parent_snaplog;
      parent_iip.GenerateSnapshotLog(&parent_snaplog);
      // CHECK(!parent_iip.NeedBackupForDeletion());
      // CHECK(!parent_iip.NeedBackupForModification());
      parent_iip.FinalizeAttrs();

      std::string dir_path = lock_comps[path_comp_index + 1].as_string();
      parent.set_mtime(inode.mtime());
      auto txid = edit_log_sender_->LogMkdirV2(
          dir_path, inode, parent, LogRpcInfo(), ancestors_id, parent_snaplog);
      CHECK_NE(txid, kInvalidTxId);

      Closure* insert_done =
          (path_comp_index == (path_comps.size() - 2)) ? &done : nullptr;
      std::vector<INode*> inodes_add = {&inode};
      std::vector<INodeAndSnapshot> parents;
      parents.emplace_back(&parent_iip.MutableInode(), &parent_snaplog);
      meta_storage_->OrderedCommitINodes(&inodes_add,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         &parents,
                                         nullptr,
                                         {},
                                         {},
                                         txid,
                                         {insert_done});
      parent_id = inode.id();
      parent = std::move(inode);
      ancestors_holder.push_back(parent);
      ancestors_id.push_back(parent_id);
    }

    done.Await();
    if (!done.status().IsOK()) {
      LOG(INFO) << "Failed to create missing parent in sync file. ufs_path: "
                << ufs_path << ", inner_path: " << inner_path
                << ", error: " << done.status().ToString();
      s = done.status();
      break;
    }
  } while (0);
  if (s.IsOK()) {
    if (ancestors) {
      *ancestors = std::move(ancestors_holder);
    }
    *p = parent;
  }
  return Status::OK();
}

// Sync the file or directory with children from UFS to local inode
// We didn't know if ufs_path is a file or dir
Status NameSpace::SyncUfsFileListing(const std::shared_ptr<ListingOption>& opt,
                                     uint64_t target_sync_ts,
                                     const std::shared_ptr<Ufs>& ufs,
                                     ListingSyncTask* task) {
  RPC_SW_CTX_LOG(task->GetRpcStopWatchCtx(), "SyncUfsFileListing");
  StopWatch sw(metrics_.acc_sync_ufs_file_listing_time_);
  sw.Start();
  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileListingSyncContext> ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    s = PrepareUfsFileListing(opt, ufs, &ctx, &locked_path, &ha_barrier);
    RPC_SW_CTX_LOG(task->GetRpcStopWatchCtx(), "after PrepareUfsFileListing");
    if (!s.IsOK()) {
      break;
    }

    // Release read lock on parent inode
    // Will take proper read or write lock on parent inode when write
    // metastorage
    locked_path.reset();

    ctx->task = task;

    if (ctx->node.type() == INode_Type_kFile) {
      s = Status::OK();
      break;
    }

    auto&& dir_info = ctx->node.ufs_dir_info();
    if (dir_info.state() == kUfsDirStateSynced &&
        dir_info.children_sync_ts() > target_sync_ts) {
      // Already synced
      s = Status(Code::kUfsAlreadySynced, "UFS dir already synced.");
      break;
    }

    s = DoSyncUfsDir(ctx);
    RPC_SW_CTX_LOG(task->GetRpcStopWatchCtx(), "after DoSyncUfsDir");
    if (!s.IsOK()) {
      break;
    }

    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncUfsFileListing: finished sync for " << ctx->UfsPath()
        << ", inner_path: " << ctx->InnerPath();

  } while (0);
  if (!s.IsOK() && (s.code() != Code::kFileNotFound)) {
    if (s.code() == Code::kFileNotFound ||
        s.code() == Code::kUfsAlreadySynced || s.code() == Code::kCanceled) {
      VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
          << "SyncUfsFileListing: Failed to sync ufs file listing. ufs_path: "
          << opt->path << ", inner_path: " << opt->inner_path
          << ", error: " << s.ToString();

    } else {
      LOG(INFO)
          << "SyncUfsFileListing: Failed to sync ufs file listing. ufs_path: "
          << opt->path << ", inner_path: " << opt->inner_path
          << ", error: " << s.ToString();
    }
  }
  return s;
}

Status NameSpace::PrepareUfsFileListing(
    const std::shared_ptr<ListingOption>& opt,
    const std::shared_ptr<Ufs>& ufs,
    std::shared_ptr<FileListingSyncContext>* ctx,
    std::unique_ptr<LockedPath>* lp,
    vshared_lock* ha_barrier) {
  StopWatch sw(metrics_.acc_prepare_ufs_file_listing_time_);
  sw.Start();
  std::shared_ptr<FileSyncContext> file_ctx;
  std::unique_ptr<LockedPath> lp_out;
  vshared_lock ha_barrier_out;

  // First do sync the path to be listed
  {
    auto s = PrepareSyncUfsFile(opt->path,
                                opt->inner_path,
                                opt->perm,
                                opt->ugi,
                                ufs,
                                true,
                                &file_ctx,
                                &lp_out,
                                &ha_barrier_out);
    if (!s.IsOK()) {
      return s;
    }

    file_ctx->trigger_sync_reason = TriggerSyncReason::LISTING;
    s = SyncUfsFileInnerWithWriteLock(file_ctx);
    if (!s.IsOK()) {
      return s;
    }
  }

  // Downgrade write lock to read lock, will lock more inode if missing parents
  // are created during previous syncing
  {
    auto s = lp_out->DowngradeToReadLock();
    if (!s.IsOK()) {
      return s;
    }

    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "Locked path after downgrade path: "
        << lp_out->GetLastLockedNodePath() << " target: " << opt->inner_path;

    if (lp_out->GetLastLockedNodePath() != opt->inner_path) {
      LOG(INFO) << "Locked path is not the same as target path, locked path: "
                << lp_out->GetLastLockedNodePath()
                << ", target: " << opt->inner_path;
      auto s = Status(JavaExceptions::kFileNotFoundException,
                      Code::kFileNotFound,
                      opt->inner_path + " not found.");
      return s;
    }
  }

  // Get INode after sync
  INodeInPath parent_iip, node_iip;
  const INode& parent = parent_iip.Inode();
  ;
  const INode& node = node_iip.Inode();
  {
    auto status_code = GetLastINodeInPath(file_ctx->path_components,
                                          &node_iip,
                                          nullptr,
                                          &parent_iip,
                                          nullptr,
                                          false);
    if (status_code == StatusCode::kFileNotFound) {
      auto s = Status(JavaExceptions::kFileNotFoundException,
                      Code::kFileNotFound,
                      file_ctx->ufs_path + " not found.");
      return s;
    }
    if (status_code != StatusCode::kOK) {
      auto s = Status(JavaExceptions::kIOException,
                      Code::kError,
                      "Failed to get inode after sync in SyncUfsFileListing.");
      return s;
    }
  }

  auto listing_ctx = std::make_shared<FileListingSyncContext>(opt, ufs);
  listing_ctx->path_components = std::move(file_ctx->path_components);
  listing_ctx->lock_components = std::move(file_ctx->lock_components);
  listing_ctx->node = std::move(node);
  listing_ctx->parent = std::move(parent);

  ctx->swap(listing_ctx);
  lp->swap(lp_out);
  *ha_barrier = std::move(ha_barrier_out);
  return Status::OK();
}

Status NameSpace::DoSyncUfsDir(
    const std::shared_ptr<FileListingSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_do_sync_ufs_dir_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncUfsFileListing: Start to sync dir: " << ctx->UfsPath()
      << ", inner: " << ctx->InnerPath();

  ctx->children_iter = meta_storage_->GetIterator();

  Status s;
  ListFilesOption opt;
  opt.page_size = FLAGS_ufs_sync_listing_page_size;

  auto sync_task = ctx->task;
  while (true) {
    vshared_lock ha_barrier;
    s = CheckOperation(&ha_barrier, OperationsCategory::kWrite);
    if (!s.IsOK()) {
      return s;
    }
    if (sync_task != nullptr && sync_task->IsCanceled()) {
      if (FLAGS_ufs_sync_listing_prefetch_only_once &&
          sync_task->GetReq()->opt->is_prefetch) {
        ListUfsDirMarkPrefetchFailed(ctx);
      }
      LOG(INFO) << "Sync ufs dir be canceled for " << ctx->InnerPath();
      return Status(Code::kCanceled, "Sync ufs dir be canceled");
    }
    ListFilesResult result;
    s = ListUfsDirWithRetry(ctx->ufs, ctx->UfsPath(), opt, &result);
    if (!s.IsOK()) {
      return s;
    }
    size_t files_len = result.files.size();
    s = ProcessUfsDirChildren(ctx, result.files);
    if (!s.IsOK()) {
      return s;
    }

    ctx->task->ReportProgress(files_len);

    if (result.has_more) {
      opt.continue_token = result.continue_token;
      if (ctx->task != nullptr) {
        ctx->task->CheckProgress();
      }
      continue;
    } else {
      break;
    }
  }

  return FinishSyncUfsDirChildren(ctx);
}

Status NameSpace::ListUfsDirMarkPrefetchFailed(
    const std::shared_ptr<FileListingSyncContext>& ctx) {
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "ListUfsDirMarkPrefetchFailed, path " << ctx->InnerPath();

  MFC(metrics_.acc_list_ufs_dir_mark_prefetch_failed_)->Inc();
  StopWatch sw(metrics_.acc_list_ufs_dir_mark_prefetch_failed_time_);
  sw.Start();

  std::unique_ptr<LockedPath> lockedpath;
  Status lock_s = ListUfsDirLockAndCheckINode(
      &ctx->node, ctx->InnerPath(), true, &lockedpath);

  if (!lock_s.IsOK()) {
    return lock_s;
  }

  INodeInPath iip;
  iip.MutableInodeUnsafe() = ctx->node;
  iip.CollectSnapshotInfo(ctx->node);
  iip.CollectAttrs(ctx->node);
  // CHECK(!iip.NeedBackupForDeletion());
  // CHECK(!iip.NeedBackupForModification());
  iip.FinalizeAttrs();
  INode old_node = iip.OldInode();
  auto&& ufs_dir_info = iip.MutableInode().mutable_ufs_dir_info();
  ufs_dir_info->set_need_prefetch(false);

  SnapshotLog inode_snaplog;
  // ACC does not support snapshot.
  // iip.GenerateSnapshotLog(&inode_snaplog);

  int64_t txid = edit_log_sender_->LogAccSyncUpdateINode(
      ctx->InnerPath(), iip.Inode(), old_node);
  CHECK_NE(txid, kInvalidTxId);

  SynchronizedRpcClosure done;
  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(), &old_node, inode_snaplog, txid, &done);
  done.Await();
  Status s = done.status();
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to update inode after dir sync. path: "
              << ctx->UfsPath() << ", inner_path: " << ctx->InnerPath()
              << ", error: " << s.ToString();
  }

  return s;
}

Status NameSpace::ListUfsDirWithRetry(std::shared_ptr<Ufs>& ufs,
                                      const std::string& path,
                                      const ListFilesOption& option,
                                      ListFilesResult* result) {
  Status s = Status::OK();

  s = ufs->ListFiles(path, option, result);

  if (UNLIKELY(s.code() == Code::kTimeout)) {
    /* curlCode: 28 timeout, change page_size to 100 and try again */
    LOG(INFO) << "Ufs list files timeout, reduce page size to 100 and retry "
              << path;
    if (option.page_size <= 100) {
      return s;
    }
    ListFilesOption tmp_option(option);
    tmp_option.page_size = 100;
    ListFilesResult tmp_result;
    for (int i = 0; i < option.page_size; i += 100) {
      s = ufs->ListFiles(path, tmp_option, &tmp_result);
      if (!s.IsOK()) {
        return s;
      }
      result->files.insert(result->files.end(),
                           tmp_result.files.begin(),
                           tmp_result.files.end());
      if (!tmp_result.has_more) {
        break;
      }
      tmp_option.continue_token = tmp_result.continue_token;
    }
    result->has_more = tmp_result.has_more;
    result->continue_token = tmp_result.continue_token;
  }

  return s;
}

static bool UfsFileAndINodeEqual(const UfsFileStatus& ufs_file,
                                 const INode& local_file) {
  auto ufs_type = ufs_file.FileType();
  auto local_file_type = local_file.type();
  CHECK(ufs_type == UFS_DIR || ufs_type == UFS_FILE);
  CHECK(local_file_type == INode_Type_kFile ||
        local_file_type == INode_Type_kDirectory);
  if ((ufs_type == UFS_FILE) && (local_file_type == INode_Type_kFile)) {
    return ufs_file.Etag() == local_file.ufs_file_info().etag();
  }
  return (ufs_type == UFS_DIR) && (local_file_type == INode_Type_kDirectory);
}

Status NameSpace::GenerateSyncActionsForUfsFiles(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<UfsFileStatus>& files,
    UfsDirSyncActions& actions) {
  StopWatch sw(metrics_.acc_generate_sync_action_for_ufs_files_time_);
  sw.Start();
  size_t files_len = files.size();
  size_t files_index = 0;
  Status s;
  while (true) {
    if (!ctx->child_iter_done) {
      if (ctx->children_nodes.empty()) {
        // Load next batch from meta storage
        bool has_more = true;
        ctx->children_nodes.reserve(1000);
        meta_storage_->GetSubINodes(ctx->node.id(),
                                    ctx->children_iter_last_child,
                                    1000,
                                    &ctx->children_nodes,
                                    &has_more,
                                    ctx->children_iter->iter());
        if (has_more) {
          // Save last child for next batch
          ctx->children_iter_last_child = ctx->children_nodes.back().name();
        } else {
          ctx->child_iter_done = true;
        }
        ctx->child_nodes_index = 0;
      }
    }

    size_t local_files_len = ctx->children_nodes.size();
    size_t local_index = ctx->child_nodes_index;
    while (files_index < files_len && local_index < local_files_len) {
      auto&& file = files[files_index];
      auto&& local_file = ctx->children_nodes[local_index];
      auto&& file_name = file.FileName();
      auto&& local_file_name = local_file.name();
      if (file_name.empty()) {
        s = Status(JavaExceptions::kIOException,
                   Code::kUfsSyncError,
                   "File name invalid (empty): " + file.FullPath());
        break;
      }
      {
        std::vector<StringPiece> path_components;
        std::vector<StringPiece> lock_components;
        auto path_s = CheckPathAndSplitComponents(
            file.FullPath(), false, &path_components, &lock_components);
        if (!path_s.IsOK()) {
          VLOG(2) << "CheckPathAndSplitComponents failed " << file.FullPath()
                  << ", " << path_s.ToString();
          ++files_index;
          continue;
        }
      }

      // Merge remote UFS files and local files
      int res = file_name.compare(local_file_name);
      if (res == 0) {
        // Ufs file and local file matched, update sync time
        if (UfsFileAndINodeEqual(file, local_file)) {
          DLOG(INFO) << "Ufs file and local file exactly match. dir: "
                     << ctx->UfsPath() << ", ufs: " << file_name
                     << ", ufs_type: " << file.FileType()
                     << ", local: " << local_file_name
                     << ", local_type: " << local_file.type();
          actions.files_to_update_time.emplace_back(std::move(local_file));
        } else {
          DLOG(INFO)
              << "Ufs file and local file type not match, check again. dir: "
              << ctx->UfsPath() << ", ufs: " << file_name
              << ", ufs_type: " << file.FileType()
              << ", local: " << local_file_name
              << ", local_type: " << local_file.type();
          actions.files_to_check.emplace_back(std::move(local_file));
        }
        ++files_index;
        ++local_index;
      } else if (res < 0) {
        // Ufs file not existed in local file, add to local
        DLOG(INFO) << "Ufs file not existed in local, add it to local. dir: "
                   << ctx->UfsPath() << ", ufs: " << file_name
                   << ", local: " << local_file_name;
        actions.files_to_add.emplace_back(std::move(file));
        ++files_index;
      } else {
        // res > 0
        // Local file not existed in UFS, remove local
        DLOG(INFO)
            << "Local file not existed in UFS, check again to delete. dir: "
            << ctx->UfsPath() << ", ufs: " << file_name
            << ", local: " << local_file_name;
        actions.files_to_check.emplace_back(std::move(local_file));
        ++local_index;
      }
    }
    if (!s.IsOK()) {
      break;
    }

    // Check if local files left
    // Finish processing current batch, the sync engine will load next batch and
    // call this process func again if there are more UFS files to load;
    // Otherwise sync engine will call the finish func if no more UFS files to
    // process
    if (local_index < local_files_len) {
      ctx->child_nodes_index = local_index;
      // all ufs files in this call are processed, save local index and break
      break;
    }

    // Reset local files
    ctx->child_nodes_index = 0;
    ctx->children_nodes.clear();

    CHECK(files_index <= files_len);
    if (files_index == files_len) {
      // All ufs files are processed
      break;
    }

    // UFS files still left
    // Fetch the next batch of local files, and continue the merge process until
    // either all of the ufs files in this batch are processed or all of the
    // local files in this folder is processed
    if (!ctx->child_iter_done) {
      // It will load next batch of local files at the beginning of the while
      // loop
      continue;
    }

    // No more local files in current folder, add all of the ufs files to add
    for (size_t i = files_index; i < files_len; ++i) {
      auto&& f = files[i];
      {
        std::vector<StringPiece> path_components;
        std::vector<StringPiece> lock_components;
        auto path_s = CheckPathAndSplitComponents(
            f.FullPath(), false, &path_components, &lock_components);
        if (!path_s.IsOK()) {
          VLOG(2) << "CheckPathAndSplitComponents failed " << f.FullPath()
                  << ", " << path_s.ToString();
          continue;
        }
      }
      DLOG(INFO) << "Ufs file not existed in local because all local files are "
                    "traversed, add it to local. dir: "
                 << ctx->UfsPath() << ", ufs: " << f.FileName();
      actions.files_to_add.emplace_back(std::move(f));
    }
    break;
  }
  if (s.IsOK()) {
    // double check to avoid tricky dead lock problems in the apply process
    // afterward
    if (!actions.CheckValid()) {
      s = Status(JavaExceptions::kIOException,
                 Code::kUfsSyncError,
                 "Generated actions are invalid.");
    }
  }
  if (!s.IsOK()) {
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    LOG(WARNING) << "Failed to generate actions for listing sync. actions: "
                 << actions.ToString();
  }
  return std::move(s);
}

Status NameSpace::ListUfsDirLockAndCheckINode(INode* inode,
                                              const std::string& inner_path,
                                              bool write_lock,
                                              std::unique_ptr<LockedPath>* lp) {
  CHECK_NOTNULL(inode);
  StopWatch sw(metrics_.acc_list_ufs_dir_lock_and_check_inode_time_);
  sw.Start();
  std::unique_ptr<LockedPath> lockedpath =
      std::make_unique<LockedPath>(write_lock ? PathLockType::kPathLockTypeWrite
                                              : PathLockType::kPathLockTypeRead,
                                   inner_path,
                                   this);
  Status s = lockedpath->ResolveAndLock();
  if (!s.IsOK()) {
    LOG(WARNING) << "Parent inode changed during sync listing. Previous is "
                 << inode->id() << ". Abort sync listing for path "
                 << inner_path;
    return Status(
        JavaExceptions::Exception::kFileNotFoundException,
        Code::kFileNotFound,
        "Parent inode changed during sync listing, path=" + inner_path);
  }
  switch (lockedpath->state()) {
    case ResolveState::kResolveError:
    case ResolveState::kResolveAncestorNotDir:
    case ResolveState::kResolveAncestorNotFound:
      LOG(WARNING) << "Parent inode changed during sync listing. Previous is "
                   << inode->id() << ". Abort sync listing for path "
                   << inner_path;
      return Status(
          JavaExceptions::Exception::kFileNotFoundException,
          Code::kFileNotFound,
          "Parent inode changed during sync listing, path=" + inner_path);
  }
  if (lockedpath->GetLastLockedNodePath() != inner_path) {
    LOG(WARNING) << "Parent inode changed during sync listing. Previous is "
                 << inode->id() << ". Abort sync listing for path "
                 << inner_path << " locked path "
                 << lockedpath->GetLastLockedNodePath();
    return Status(
        JavaExceptions::Exception::kFileNotFoundException,
        Code::kFileNotFound,
        "Parent inode changed during sync listing, path=" + inner_path);
  }
  if (inode->id() != kRootINodeId) {
    auto&& ancestors = lockedpath->GetLockedAncestors();
    CHECK(!ancestors.empty());
    auto&& parent = ancestors.back();
    INode node;
    auto code = meta_storage_->GetINode(parent.id(), inode->name(), &node);
    if (code != kOK || node.id() != inode->id()) {
      LOG(WARNING) << "Parent inode changed during sync listing. Previous is "
                   << inode->id() << ". Abort sync listing for path "
                   << inner_path;
      return Status(
          JavaExceptions::Exception::kFileNotFoundException,
          Code::kFileNotFound,
          "Parent inode changed during sync listing, path=" + inner_path);
    }
    *inode = node;
  }
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Required lock for path: " << inner_path << " "
      << lockedpath->GetLastLockedNodePath();

  *lp = std::move(lockedpath);
  return Status::OK();
}

Status NameSpace::ProcessUfsDirChildren(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<UfsFileStatus>& files) {
  StopWatch sw(metrics_.acc_process_ufs_dir_children_time_);
  sw.Start();
  std::unique_ptr<LockedPath> lockedpath;
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Reacquire lock for path: " << ctx->UfsPath()
      << ", inner_path: " << ctx->InnerPath();

  Status lock_s = ListUfsDirLockAndCheckINode(
      &ctx->node, ctx->InnerPath(), false, &lockedpath);
  if (!lock_s.IsOK()) {
    return lock_s;
  }
  ctx->set_locked_path(lockedpath.get());
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Start to process children for path: " << ctx->UfsPath()
      << ", inner_path: " << ctx->InnerPath() << ", files: " << files.size();

  UfsDirSyncActions actions;
  GenerateSyncActionsForUfsFiles(ctx, files, actions);
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail) << "actions: " << actions.ToString();

  // Apply actions
  return ApplyUfsDirSyncAction(ctx, actions);
}

Status NameSpace::FinishSyncUfsDirChildren(
    const std::shared_ptr<FileListingSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_finish_sync_ufs_dir_children_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Finish children for path: " << ctx->UfsPath()
      << ", inner_path: " << ctx->InnerPath();

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Reacquire lock for path: " << ctx->UfsPath()
      << ", inner_path: " << ctx->InnerPath();

  std::unique_ptr<LockedPath> lockedpath;
  Status lock_s = ListUfsDirLockAndCheckINode(
      &ctx->node, ctx->InnerPath(), false, &lockedpath);
  if (!lock_s.IsOK()) {
    return lock_s;
  }
  ctx->set_locked_path(lockedpath.get());

  while (true) {
    UfsDirSyncActions actions;
    // Check if all local sub files are iterated, these files are not existed in
    // UFS, should be deleted
    if (!ctx->children_nodes.empty()) {
      for (size_t i = ctx->child_nodes_index; i < ctx->children_nodes.size();
           ++i) {
        auto&& node = ctx->children_nodes[i];
        DLOG(INFO)
            << "Local file not existed in UFS, check again to delete. dir: "
            << ctx->UfsPath() << ", local: " << node.name();
        actions.files_to_check.emplace_back(std::move(node));
      }
      ctx->children_nodes.clear();
      ctx->child_nodes_index = 0;
    }

    ApplyUfsDirSyncAction(ctx, actions);

    if (ctx->child_iter_done) {
      break;
    }

    bool has_more = true;
    ctx->children_nodes.reserve(1000);
    meta_storage_->GetSubINodes(ctx->node.id(),
                                ctx->children_iter_last_child,
                                1000,
                                &ctx->children_nodes,
                                &has_more,
                                ctx->children_iter->iter());
    if (has_more) {
      // Save last child for next batch
      ctx->children_iter_last_child = ctx->children_nodes.back().name();
    } else {
      ctx->child_iter_done = true;
    }
  }

  // Update dir inode status and children sync time
  Status s;
  {
    // Write lock current dir
    s = ctx->locked_path()->UpgradeToWriteLock();
    if (!s.IsOK()) {
      return s;
    }

    INode node;
    if (ctx->path_components.empty()) {
      node = GetRootINode();
    } else {
      std::string dir_name = ctx->path_components.back().as_string();
      auto status = meta_storage_->GetINode(ctx->parent.id(), dir_name, &node);
      // The inode must exist as we just release read lock and acquire write
      // lock
      if (status != kOK) {
        LOG(INFO) << "Failed to get inode for path: " << ctx->UfsPath()
                  << ", inner: " << ctx->InnerPath() << ", error: " << status;
        return Status(JavaExceptions::kFileNotFoundException,
                      Code::kError,
                      "Failed to get inode for path: " + ctx->UfsPath() +
                          ", inner: " + ctx->InnerPath());
      }
    }
    // rare case: the dir is recreated in the gap of read local release and
    // write lock acquire
    if (node.id() != ctx->node.id()) {
      LOG(INFO) << "Local dir updated by others, give up the sync. path: "
                << ctx->UfsPath() << ", inner: " << ctx->InnerPath();
      return Status(JavaExceptions::kIOException,
                    Code::kError,
                    "Local dir updated by others, give up the sync. path: " +
                        ctx->UfsPath() + ", inner: " + ctx->InnerPath());
    }

    INodeInPath iip;
    iip.MutableInodeUnsafe() = node;
    iip.CollectSnapshotInfo(node);
    iip.CollectAttrs(node);
    // CHECK(!iip.NeedBackupForDeletion());
    // CHECK(!iip.NeedBackupForModification());
    iip.FinalizeAttrs();

    INode old_node = iip.OldInode();
    auto&& ufs_dir_info = iip.MutableInode().mutable_ufs_dir_info();
    ufs_dir_info->set_state(kUfsDirStateSynced);

    uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
    ufs_dir_info->set_children_sync_ts(now_sec);

    ufs_dir_info->set_need_prefetch(false);

    UpdateINodeAttrs(iip.MutableInode(), ctx->parent, true);

    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);

    int64_t txid = edit_log_sender_->LogAccSyncUpdateINode(
        ctx->InnerPath(), iip.Inode(), old_node);
    CHECK_NE(txid, kInvalidTxId);

    SynchronizedRpcClosure done;
    meta_storage_->OrderedUpdateINode(
        &iip.MutableInodeUnsafe(), &old_node, inode_snaplog, txid, &done);
    done.Await();
    s = done.status();
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to update inode after dir sync. path: "
                << ctx->UfsPath() << ", inner_path: " << ctx->InnerPath()
                << ", error: " << s.ToString();
    }
  }

  return s;
}

Status NameSpace::ApplyUfsDirSyncAction(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const UfsDirSyncActions& actions) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_time_);
  sw.Start();
  Status s;

  std::unordered_set<std::string> recheck_file_names;
  recheck_file_names.reserve(actions.files_to_add.size() +
                             actions.files_to_update_time.size());

  // files_to_add;
  std::vector<std::unique_ptr<RWLockManager::LockHolder>> locks_for_add;
  std::vector<INodeWithBlocks> inodes_for_add;
  locks_for_add.reserve(actions.files_to_add.size());
  inodes_for_add.reserve(actions.files_to_add.size());
  for (auto&& f : actions.files_to_add) {
    const std::string& name = f.FileName();
    std::string file_inner_path = JoinTwoPath(ctx->InnerPath(), name);
    const uint32_t lock_depth = (uint32_t)ctx->lock_components.size();
    VLOG_OR_IF(12, FLAGS_log_ufs_sync_detail)
        << "Apply dir children sync, files_to_add. "
        << " path: " << ctx->UfsPath() << ", inner_path: " << ctx->InnerPath()
        << ", add file: " << file_inner_path << ", lock_depth: " << lock_depth;

    {
      auto write_lock_holder =
          lock_manager_->WriteLock(file_inner_path, lock_depth);
      INode existed_node;
      StatusCode code =
          meta_storage_->GetINode(ctx->node.id(), name, &existed_node);
      if (code == StatusCode::kOK) {
        // Corner case, someone has synced / created this file while we're
        // syncing the dir
        // Acquire the write lock and check again
        LOG(INFO) << "Local sub already existed for dir, recheck. dir: "
                  << ctx->UfsPath() << ", sub: " << name;
        recheck_file_names.emplace(name);
        continue;
      }
      if (code != StatusCode::kFileNotFound) {
        LOG(INFO) << "Failed to get local sub for dir: " << ctx->UfsPath()
                  << ", sub: " << name << ", code: " << code;
        return Status(JavaExceptions::kIOException,
                      Code::kError,
                      "Failed to get local sub for dir: " + ctx->UfsPath() +
                          ", subdir: " + name);
      }

      {
        INode node;
        std::vector<BlockInfoProto> block_infos;
        MakeInodeAndBlocksFromUfs(
            ctx->node.id(), name, ctx->Perm(), f, &node, &block_infos);
        UpdateINodeAttrs(node, ctx->parent, true);
        INodeWithBlocks ib;
        ib.node = std::move(node);
        ib.blocks = std::move(block_infos);
        locks_for_add.emplace_back(std::move(write_lock_holder));
        inodes_for_add.emplace_back(std::move(ib));
      }
    }
  }
  if (!inodes_for_add.empty()) {
    s = ApplyUfsDirSyncActionCreateFile(ctx, inodes_for_add);
    if (!s.IsOK()) {
      return s;
    }
  }
  locks_for_add.clear();

  // files_to_update_time;
  uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
  std::vector<std::unique_ptr<RWLockManager::LockHolder>> locks_for_update_time;
  std::vector<INode> inodes_for_update_time;
  std::vector<INode> old_inodes_for_update_time;
  locks_for_update_time.reserve(actions.files_to_update_time.size());
  inodes_for_update_time.reserve(actions.files_to_update_time.size());
  old_inodes_for_update_time.reserve(actions.files_to_update_time.size());
  for (auto&& f : actions.files_to_update_time) {
    if (FLAGS_ignore_sync_action_update_time) {
      continue;
    }
    std::string file_inner_path = JoinTwoPath(ctx->InnerPath(), f.name());
    const uint32_t lock_depth = (uint32_t)ctx->lock_components.size();
    VLOG_OR_IF(12, FLAGS_log_ufs_sync_detail)
        << "Apply dir children sync, files_to_update_time. "
        << " path: " << ctx->UfsPath() << ", inner_path: " << ctx->InnerPath()
        << ", update file time: " << file_inner_path
        << ", lock_depth: " << lock_depth;

    {
      auto write_lock_holder =
          lock_manager_->WriteLock(file_inner_path, lock_depth);
      INode local_node;
      StatusCode code =
          meta_storage_->GetINode(ctx->node.id(), f.name(), &local_node);
      if (code != StatusCode::kOK) {
        if (code == StatusCode::kFileNotFound) {
          LOG(INFO) << "Local sub not found  for dir, recheck. dir: "
                    << ctx->UfsPath() << ", sub: " << f.name()
                    << ", code: " << code;
          recheck_file_names.emplace(f.name());
          continue;
        }

        LOG(INFO) << "Failed to get local sub for dir: " << ctx->UfsPath()
                  << ", sub: " << f.name() << ", code: " << code;
        return Status(JavaExceptions::kIOException,
                      Code::kError,
                      "Failed to get local sub for dir: " + ctx->UfsPath() +
                          ", subdir: " + f.name());
      }
      INode old_node = local_node;

      if (local_node.type() != f.type() || local_node.mtime() != f.mtime()) {
        LOG(INFO) << "Local sub updated by others since the start of the "
                     "dir sync, recheck. dir: "
                  << ctx->UfsPath() << ", sub: " << f.name()
                  << ", local type: " << local_node.type()
                  << ", local_mtime: " << local_node.mtime()
                  << ", node type: " << f.type()
                  << ", node mtime: " << f.mtime();
        recheck_file_names.emplace(f.name());
        continue;
      }

      if (local_node.type() == INode_Type_kFile) {
        CHECK(local_node.has_ufs_file_info());
        auto ufs_file_info = local_node.mutable_ufs_file_info();
        ufs_file_info->set_sync_ts(now_sec);
      } else {
        CHECK(local_node.has_ufs_dir_info());
        auto ufs_dir_info = local_node.mutable_ufs_dir_info();
        ufs_dir_info->set_sync_ts(now_sec);
      }

      UpdateINodeAttrs(local_node, ctx->parent);

      locks_for_update_time.emplace_back(std::move(write_lock_holder));
      inodes_for_update_time.emplace_back(std::move(local_node));
      old_inodes_for_update_time.emplace_back(std::move(old_node));
    }
  }
  if (!inodes_for_update_time.empty()) {
    s = ApplyUfsDirSyncActionUpdateFileSyncTime(
        ctx, inodes_for_update_time, old_inodes_for_update_time);
    RETURN_NOT_OK(s);
  }
  locks_for_update_time.clear();

  // files_to_check;
  for (auto&& f : actions.files_to_check) {
    recheck_file_names.emplace(f.name());
  }

  // Process recheck files
  // Do it one by one
  for (auto&& file_name : recheck_file_names) {
    const uint32_t lock_depth = (uint32_t)ctx->lock_components.size();
    std::string file_inner_path = JoinTwoPath(ctx->InnerPath(), file_name);
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "Apply dir children sync. path: " << ctx->UfsPath()
        << ", inner_path: " << ctx->InnerPath()
        << ", overwrite file: " << file_inner_path
        << ", lock_depth: " << lock_depth;

    {
      auto write_lock_holder =
          lock_manager_->WriteLock(file_inner_path, lock_depth);

      INode local_node;
      StatusCode code =
          meta_storage_->GetINode(ctx->node.id(), file_name, &local_node);
      if (code == kOK) {
        UpdateINodeAttrs(local_node, ctx->parent);
      } else {
        if (code != kFileNotFound) {
          LOG(ERROR) << "Failed to get local sub inode for file: "
                     << file_inner_path << ", code: " << code;
          return Status(
              JavaExceptions::kIOException,
              Code::kError,
              "Failed to get local inode for file: " + file_inner_path);
        }
        // Local not found. Fallback to Check File, local_node is invalid
      }
      s = ApplyUfsDirSyncActionCheckFile(ctx, file_name, local_node);
      if (!s.IsOK()) {
        return s;
      }
    }
  }

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "Finish apply dir children sync. path: " << ctx->UfsPath()
      << ", inner_path: " << ctx->InnerPath();

  return Status::OK();
}

static void JoinParentAndSub(const std::string& parent,
                             const std::string& sub,
                             std::string* path) {
  CHECK(!parent.empty() && !sub.empty());

  path->clear();
  path->reserve(parent.size() + 1 + sub.size());
  path->append(parent);
  if (parent.back() != kSeparatorChar) {
    path->append(kSeparator);
  }
  path->append(sub);
}

Status NameSpace::ApplyUfsDirSyncActionCreateFile(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<INodeWithBlocks>& new_ufs_files) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_create_time_);
  sw.Start();

  int start_idx = 0, end_idx = 0;
  while (end_idx < new_ufs_files.size()) {
    uint32_t total_size = 0;
    uint64_t total_bytes = 0;

    while (total_size < FLAGS_acc_batch_add_editlog_batch_size &&
           total_bytes < FLAGS_acc_batch_add_editlog_batch_max_size_bytes &&
           end_idx < new_ufs_files.size()) {
      // total bytes is estimated by first block size
      auto& node = new_ufs_files[end_idx].node;
      if (node.blocks_size() > 0) {
        total_bytes += node.blocks_size() * node.blocks(0).numbytes();
      }
      total_size += 1;
      end_idx += 1;
    }

    VLOG_OR_IF(12, FLAGS_log_ufs_sync_detail)
        << "ApplyUfsDirSyncActionCreateFile batch size " << total_size
        << " total bytes " << total_bytes << " ufs path " << ctx->UfsPath();

    std::vector<INodeWithBlocks> files;
    files.reserve(end_idx - start_idx);
    for (int i = start_idx; i < end_idx; i++) {
      files.push_back(new_ufs_files[i]);
    }
    start_idx = end_idx;

    Status s = ApplyUfsDirSyncActionCreateFileOneBatch(ctx, files);
    if (!s.IsOK()) {
      return std::move(s);
    }
  }

  return Status::OK();
}

Status NameSpace::ApplyUfsDirSyncActionCreateFileOneBatch(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<INodeWithBlocks>& new_ufs_files) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_create_one_time_);
  sw.Start();

  int64_t txid = edit_log_sender_->LogAccSyncListingBatchAdd(
      ctx->InnerPath(), ctx->node, new_ufs_files);
  CHECK_NE(txid, kInvalidTxId);

  SynchronizedRpcClosure done;
  meta_storage_->OrderedCreateUfsFiles(
      txid, new_ufs_files, &ctx->node, {&done});
  done.Await();
  return done.status();
}

// Param:
//  local_file: can be an empty node, which means local file not existed
Status NameSpace::ApplyUfsDirSyncActionCheckFile(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::string& local_file_name,
    const INode& local_file) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_check_time_);
  sw.Start();
  // Get Status from UFS
  // IsDirectory from UFS
  std::string ufs_path;
  std::string inner_path;
  JoinParentAndSub(ctx->UfsPath(), local_file_name, &ufs_path);
  JoinParentAndSub(ctx->InnerPath(), local_file_name, &inner_path);

  // No need to care about ctx->locks as it will only be used to create parent
  // if not existed, and file_ctx->parent is guaranteed to be existed in this
  // case
  auto file_ctx = CreateFileSyncContextNoLock(ufs_path,
                                              inner_path,
                                              ctx->Perm(),
                                              ctx->Ugi(),
                                              ctx->ufs,
                                              ctx->node,
                                              local_file);
  std::unique_ptr<LockedPath> tmp_lp =
      std::make_unique<LockedPath>(PathLockType::kPathLockTypeNone, "/", this);
  tmp_lp->MutablePath().attr_holder = ctx->locked_path()->GetAttrHolder();
  file_ctx->set_locked_path(tmp_lp.get());
  file_ctx->trigger_sync_reason = TriggerSyncReason::DO_SYNC_UFS_DIR;
  return SyncUfsFileInnerWithWriteLock(file_ctx);
}

Status NameSpace::ApplyUfsDirSyncActionUpdateFileSyncTime(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<INode>& update_time_ufs_files,
    const std::vector<INode>& old_ufs_files) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_update_time_);
  sw.Start();

  CHECK_EQ(update_time_ufs_files.size(), old_ufs_files.size());

  int start_idx = 0, end_idx = 0;
  while (end_idx < update_time_ufs_files.size()) {
    uint32_t total_size = 0;
    uint64_t total_bytes = 0;

    while (total_size < FLAGS_acc_batch_add_editlog_batch_size &&
           total_bytes < FLAGS_acc_batch_add_editlog_batch_max_size_bytes &&
           end_idx < update_time_ufs_files.size()) {
      // total bytes is estimated by first block size
      auto& node = update_time_ufs_files[end_idx];
      if (node.blocks_size() > 0) {
        total_bytes += node.blocks_size() * node.blocks(0).numbytes();
      }
      total_size += 1;
      end_idx += 1;
    }

    VLOG_OR_IF(12, FLAGS_log_ufs_sync_detail)
        << "ApplyUfsDirSyncActionUpdateFileSyncTime batch size " << total_size
        << " total bytes " << total_bytes << " ufs path " << ctx->UfsPath();

    std::vector<INode> update_files;
    std::vector<INode> old_files;
    update_files.reserve(end_idx - start_idx);
    old_files.reserve(end_idx - start_idx);
    for (int i = start_idx; i < end_idx; i++) {
      update_files.push_back(update_time_ufs_files[i]);
      old_files.push_back(old_ufs_files[i]);
    }
    start_idx = end_idx;

    Status s = ApplyUfsDirSyncActionUpdateFileSyncTimeOneBatch(
        ctx, update_files, old_files);
    if (!s.IsOK()) {
      return std::move(s);
    }
  }

  return Status::OK();
}

Status NameSpace::ApplyUfsDirSyncActionUpdateFileSyncTimeOneBatch(
    const std::shared_ptr<FileListingSyncContext>& ctx,
    const std::vector<INode>& update_time_ufs_files,
    const std::vector<INode>& old_ufs_files) {
  StopWatch sw(metrics_.acc_apply_ufs_dir_sync_action_update_one_time_);
  sw.Start();

  int64_t txid = edit_log_sender_->LogAccSyncListingBatchUpdate(
      ctx->InnerPath(), ctx->node, update_time_ufs_files, old_ufs_files);
  CHECK_NE(txid, kInvalidTxId);

  SynchronizedRpcClosure done;
  meta_storage_->OrderedUpdateUfsINodes(
      txid, update_time_ufs_files, &old_ufs_files, &done);
  done.Await();
  return done.status();
}

Status NameSpace::CheckPathAndSplitComponents(
    const std::string& src,
    bool allow_src_root,
    std::vector<cnetpp::base::StringPiece>* path_components,
    std::vector<cnetpp::base::StringPiece>* lock_components) {
  std::vector<cnetpp::base::StringPiece> paths;
  if (!SplitPath(src, &paths)) {
    return Status(JavaExceptions::kIllegalArgumentException,
                  Code::kBadParameter,
                  "Invalid path: " + src);
  }

  std::vector<cnetpp::base::StringPiece> locks;
  if (!GetAllAncestorPaths(src, &locks)) {
    return Status(JavaExceptions::kIllegalArgumentException,
                  Code::kBadParameter,
                  "Invalid path: " + src);
  }

  if (locks.size() != paths.size() + 1) {
    return Status(JavaExceptions::kIllegalArgumentException,
                  Code::kBadParameter,
                  "Invalid path: " + src);
  }

  auto s = VerifyPath(src, paths);
  if (!s.IsOK()) {
    LOG(INFO) << "Invalid path: " << s.ToString();
    return s;
  }

  *path_components = std::move(paths);
  *lock_components = std::move(locks);

  return {};
}

Status NameSpace::CheckOperationSafeMode(vshared_lock* ha_barrier,
                                         OperationsCategory op) {
  // Check Operation and take HAState barrier shared lock
  auto s = CheckOperation(ha_barrier, OperationsCategory::kWrite);
  if (!s.IsOK()) {
    LOG(INFO) << "Operation not allowed: " << s.ToString();
    return s;
  }

  // Check SafeMode
  if (safemode_->IsOn()) {
    return Status(JavaExceptions::kSafeModeException,
                  "Namenode is in safe mode.");
  }

  return Status::OK();
}

void NameSpace::MakeInodeAndBlocksFromUfs(
    uint64_t parent_id,
    const std::string& name,
    const PermissionStatus& perm,
    const UfsFileStatus& file,
    INode* node,
    std::vector<BlockInfoProto>* block_infos) {
  StopWatch sw(metrics_.acc_make_inode_and_blocks_from_ufs_time_);
  sw.Start();
  uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;

  if (file.FileType() == UFS_DIR) {
    MakeINode(
        NextINodeId(), parent_id, name, perm, INode_Type_kDirectory, node);
    node->set_atime(file.created_ts());
    node->set_mtime(file.modified_ts());
    node->mutable_permission()->set_permission(0755);
    auto ufs_dir_info = node->mutable_ufs_dir_info();
    ufs_dir_info->set_state(kUfsDirStateIncomplete);
    ufs_dir_info->set_sync_ts(now_sec);
    ufs_dir_info->set_type(kUfsDirTypeUfs);
    ufs_dir_info->set_children_sync_ts(0);
    ufs_dir_info->set_need_prefetch(true);
    return;
  }

  auto&& ufs_path = file.FullPath();
  CHECK(ufs_path[0] == '/');
  MakeINodeFile(NextINodeId(),
                parent_id,
                name,
                perm,
                1,  // Single replica
                file.BlockSize(),
                kBlockStoragePolicyIdUnspecified,
                node);
  node->mutable_permission()->set_permission(0644);
  node->set_status(INode_Status_kFileComplete);
  node->set_atime(file.created_ts());
  node->set_mtime(file.modified_ts());
  auto ufs_info = node->mutable_ufs_file_info();
  ufs_info->set_file_state(kUfsFileStatePersisted);
  ufs_info->set_create_type(kUfsFileCreateTypeInvalid);
  ufs_info->set_write_type(kUfsFileWriteTypeInvalid);
  ufs_info->set_etag(file.Etag());
  ufs_info->set_size(file.FileSize());
  ufs_info->set_last_modified_ts(now_sec);
  ufs_info->set_sync_ts(now_sec);
  std::string key;
  UfsUtil::ConvertUfsPathToObject(ufs_path, &key);
  CHECK(!key.empty());
  ufs_info->set_key(key);

  {
    // metric sync action
    int64_t time_now_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
    int64_t time_gap_ms = time_now_ms - file.modified_ts();
    MFH(metrics_.sync_file_from_ufs_gap_time_)->Update(time_gap_ms * 1000);

    if (time_gap_ms > FLAGS_sync_file_from_ufs_slow_time_ms) {
      MFC(metrics_.sync_file_from_ufs_slow_cnt_)->Inc();
    }
  }

  const uint64_t block_size = file.BlockSize();
  uint64_t blocks_cnt =
      file.FileSize() > 0 ? (file.FileSize() - 1) / block_size + 1 : 1;
  block_infos->reserve(blocks_cnt);

  for (uint64_t i = 0; i < blocks_cnt; ++i) {
    uint64_t block_id = NextBlockId();
    uint64_t gsv2 = NextGenerationStampV2();
    auto blk = node->add_blocks();
    blk->set_blockid(block_id);
    blk->set_genstamp(gsv2);
    blk->set_numbytes((i == (blocks_cnt - 1))
                          ? (file.FileSize() - i * block_size)
                          : block_size);

    BlockInfoProto block_info;
    block_info.set_state(BlockInfoProto::kPersisted);
    block_info.set_block_id(blk->blockid());
    block_info.set_gen_stamp(blk->genstamp());
    block_info.set_num_bytes(blk->numbytes());
    block_info.set_inode_id(node->id());
    block_info.set_expected_rep(1);
    block_info.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);
    block_info.set_pufs_name(
        "");  // No need to save pufsname for ACC, the object key should be
              // computed from inode path
    block_info.set_pufs_offset(i * block_size);
    block_info.set_type(BlockInfoProto_Type_kACCBlock);
    CHECK(block_info.IsInitialized()) << block_info.InitializationErrorString();
    block_infos->emplace_back(std::move(block_info));
  }
}

Status NameSpace::ProcessFileSyncActionCreate(
    const std::shared_ptr<FileSyncContext>& ctx,
    const UfsFileStatus& file,
    RpcClosure* done) {
  StopWatch sw(metrics_.acc_sync_ufs_file_create_time_);
  sw.Start();
  if (ctx->create_parent) {
    auto s = CreateMissingParentInSync(ctx);
    if (!s.IsOK()) {
      LOG(INFO) << "SyncUfsFile failed when creating parent, path: "
                << ctx->inner_path << ", error: " << s.ToString();
      return s;
    }
  } else {
    CHECK(ctx->IsParentValid());
  }

  INodeWithBlocks ib;
  MakeInodeAndBlocksFromUfs(
      ctx->parent.id(), file.FileName(), ctx->perm, file, &ib.node, &ib.blocks);
  UpdateINodeAttrs(ib.node, ctx->parent, true);

  int64_t txid = edit_log_sender_->LogAccSyncAddFile(
      ctx->inner_path, ctx->parent, ib, nullptr);
  CHECK_NE(txid, kInvalidTxId);

  meta_storage_->OrderedCreateUfsFile(txid, ib, done);
  return Status::OK();
}

Status NameSpace::ProcessFileSyncActionDelete(
    const std::shared_ptr<FileSyncContext>& ctx,
    RpcClosure* done) {
  StopWatch sw(metrics_.acc_sync_ufs_file_delete_time_);
  sw.Start();
  // Delete local file means local file existed, hence parent must not be valid
  CHECK(ctx->IsParentValid());

  LogRpcInfo dummy;
  ClosureGuard done_guard(done);

  INodeInPath iip;
  iip.MutableInodeUnsafe() = ctx->iip.Inode();
  iip.CollectSnapshotInfo(ctx->iip.Inode());
  iip.CollectAttrs(ctx->iip.Inode());
  // CHECK(!iip.NeedBackupForDeletion());
  // CHECK(!iip.NeedBackupForModification());
  iip.FinalizeAttrs();
  INode old_node = iip.OldInode();
  iip.RecordDeletion();
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  auto to_remove = std::make_shared<std::vector<BlockProto>>();
  if (iip.Inode().type() == INode_Type_kFile) {
    GetFileBlocksInternal(iip.Inode(), to_remove.get());

    const UfsFileInfoProto& info = iip.Inode().ufs_file_info();
    if (info.file_state() == kUfsFileStateToBePersisted &&
        !info.upload_id().empty()) {
      UfsAbortCreateOption opt(true);
      ctx->ufs->AbortCreate(ctx->inner_path, iip.Inode().ufs_file_info(), opt);
      ufs_env_->upload_monitor()->EraseTask(iip.Inode().id());
    }
  }

  INodeInPath parent_iip;
  parent_iip.MutableInodeUnsafe() = ctx->parent;
  parent_iip.CollectSnapshotInfo(ctx->parent);
  const uint64_t now_ms = TimeUtil::GetNowEpochMs();
  parent_iip.SetModificationTime(now_ms);
  SnapshotLog parent_snaplog;
  parent_iip.GenerateSnapshotLog(&parent_snaplog);

  std::vector<INodeID> ancestors_id;
  const int64_t tx_id = edit_log_sender_->LogDeleteV2(ctx->inner_path,
                                                      ctx->iip.OldInode(),
                                                      ctx->parent,
                                                      now_ms,
                                                      ancestors_id,
                                                      inode_snaplog,
                                                      parent_snaplog,
                                                      LogRpcInfo());
  CHECK_NE(tx_id, kInvalidTxId);

  done->set_callback([this, ctx, to_remove](const Status& s) {
    DeleteINodeCallback(ctx->iip.Inode(), ctx->inner_path);
  });

  meta_storage_->OrderedDeleteINode(
      ctx->iip.Inode(), tx_id, done_guard.release(), &ctx->parent);
  return Status::OK();
}

Status NameSpace::ProcessFileSyncActionOverwrite(
    const std::shared_ptr<FileSyncContext>& ctx,
    const UfsFileStatus& file,
    RpcClosure* done) {
  StopWatch sw(metrics_.acc_sync_ufs_file_overwrite_time_);
  sw.Start();
  // Overwrite local file means local file existed, hence parent must not be
  // valid.
  CHECK(ctx->IsParentValid());

  INodeWithBlocks ib;
  MakeInodeAndBlocksFromUfs(
      ctx->parent.id(), file.FileName(), ctx->perm, file, &ib.node, &ib.blocks);
  UpdateINodeAttrs(ib.node, ctx->parent, true);

  INode old_node = ctx->iip.OldInode();
  int64_t tx_id = edit_log_sender_->LogAccSyncAddFile(
      ctx->inner_path, ctx->parent, ib, &old_node);
  CHECK_NE(tx_id, kInvalidTxId);

  auto to_remove = std::make_shared<std::vector<BlockProto>>();
  if (ctx->iip.Inode().type() == INode_Type_kFile) {
    GetFileBlocksInternal(ctx->iip.Inode(), to_remove.get());
  }

  done->set_callback([this, ctx, to_remove](const Status& s) {
    // if (!to_remove->empty()) {
    //   block_manager_->RemoveBlocksAndUpdateSafeMode(*to_remove);
    // }
  });

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "ProcessFileSyncActionOverwrite "
      << " old_inode=" << ctx->iip.Inode().ShortDebugString()
      << " new_inode=" << ib.node.ShortDebugString();

  meta_storage_->OrderedOverwriteUfsFile(tx_id, ib, &old_node, done);
  return Status::OK();
}

Status NameSpace::ProcessFileSyncActionUpdateTime(
    const std::shared_ptr<FileSyncContext>& ctx,
    RpcClosure* done) {
  StopWatch sw(metrics_.acc_sync_ufs_file_update_time_);
  sw.Start();
  CHECK(ctx->IsParentValid());

  INodeInPath iip = ctx->iip;
  // CHECK(!iip.NeedBackupForDeletion());
  // CHECK(!iip.NeedBackupForModification());

  const uint64_t now_ms = TimeUtil::GetNowEpochMs();
  const uint64_t now_sec = now_ms / 1000;
  if (iip.Inode().type() == INode_Type_kFile) {
    CHECK(iip.Inode().has_ufs_file_info());
    iip.MutableInode().mutable_ufs_file_info()->set_sync_ts(now_sec);
  } else {
    CHECK(iip.Inode().type() == INode_Type_kDirectory &&
          iip.Inode().has_ufs_dir_info());
    iip.MutableInode().mutable_ufs_dir_info()->set_sync_ts(now_sec);
  }

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  INode old_node = iip.OldInode();
  int64_t txid = edit_log_sender_->LogAccSyncUpdateINode(
      ctx->inner_path, iip.Inode(), old_node);
  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(done, txid);

  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(), &old_node, inode_snaplog, txid, done);
  return Status::OK();
}

void NameSpace::UpdateINodeForPersistedFile(const std::string& path,
                                            const INode& parent,
                                            const INode& inode,
                                            const INode& old_inode,
                                            Closure* done) {
  INodeInPath iip;
  iip.MutableInodeUnsafe() = inode;
  iip.CollectSnapshotInfo(inode);
  iip.CollectAttrs(inode);
  // TODO: Please take all inodes of the path and
  //       check !NeedBackupForDeletion().
  // TODO: Please check snapshot flag is disabled.
  // CHECK(!iip.NeedBackupForDeletion());
  // CHECK(!iip.NeedBackupForModification());
  iip.FinalizeAttrs();

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  const int64_t tx_id =
      edit_log_sender_->LogAccPersistFile(path, parent, inode, old_inode);
  CHECK_NE(tx_id, kInvalidTxId);
  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(), &old_inode, inode_snaplog, tx_id, done, nullptr);
}

Status NameSpace::SyncRenameUfs(const std::shared_ptr<RenameToOption>& opt,
                                const std::shared_ptr<Ufs>& ufs) {
  StopWatch sw(metrics_.acc_sync_rename_ufs_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs started. src: " << opt->src
      << " inner_src: " << opt->inner_src << " dst: " << opt->dst
      << " inner_dst: " << opt->inner_dst;

  std::unique_ptr<LockedPathVector> locked_path;
  std::shared_ptr<RenameSyncContext> ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    // Sync src and dst; lock src and dst
    s = PrepareRenameUfs(opt, ufs, &ctx, &locked_path, &ha_barrier);
    if (!s.IsOK()) {
      break;
    }

    // Check src and dst
    s = RenameUfsCheckSrcDst(ctx);
    if (!s.IsOK()) {
      break;
    }

    if (ctx->ufs->SupportAtomicRename()) {
      // Rename remote
      if (!ctx->skip_rename_remote) {
        s = RenameUfsRenameRemote(ctx);
        if (!s.IsOK()) {
          break;
        }
      }

      // Rename local inode
      s = RenameUfsRenameLocal(ctx);
      if (!s.IsOK()) {
        break;
      }
    } else {
      // Check files limit
      s = RenameUfsCheckSrcDstFilesLimit(ctx);
      if (!s.IsOK()) {
        break;
      }

      if (!ctx->skip_rename_remote) {
        // Remove dst if necessary
        s = RenameUfsRemoveDst(ctx);
        if (!s.IsOK()) {
          break;
        }

        // Copy src to dst
        s = RenameUfsCopyUfsFiles(ctx);
        if (!s.IsOK()) {
          break;
        }
      }

      // Rename local inode
      s = RenameUfsRenameLocal(ctx);
      if (!s.IsOK()) {
        break;
      }

      // Delete src
      if (!ctx->skip_rename_remote) {
        s = RenameUfsDeleteUfsFiles(ctx);
        if (!s.IsOK()) {
          break;
        }
      }
    }
  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "SyncRenameUfs failed. src: " << opt->src
              << " inner_src: " << opt->inner_src << " dst: " << opt->dst
              << " inner_dst: " << opt->inner_dst
              << ", error: " << s.ToString();
    return std::move(s);
  }
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs finished. src: " << opt->src
      << " inner_src: " << opt->inner_src << " dst: " << opt->dst
      << " inner_dst: " << opt->inner_dst;

  return Status::OK();
}
Status NameSpace::PrepareRenameUfs(const std::shared_ptr<RenameToOption>& opt,
                                   const std::shared_ptr<Ufs>& ufs,
                                   std::shared_ptr<RenameSyncContext>* c,
                                   std::unique_ptr<LockedPathVector>* lp,
                                   vshared_lock* ha_barrier) {
  StopWatch sw(metrics_.acc_prepare_rename_ufs_time_);
  sw.Start();
  Status s;
  do {
    s = CheckOperationSafeMode(ha_barrier, OperationsCategory::kWrite);
    if (!s.IsOK()) {
      break;
    }
    if (FLAGS_enable_ufs_sync_in_rename && FLAGS_enable_ufs_sync_in_operation) {
      // target_sync_ts to UINT64_MAX to force sync
      s = SyncUfsFile(opt->src,
                      opt->inner_src,
                      opt->perm,
                      opt->ugi,
                      ufs,
                      /*sync_on_ancestor_not_dir=*/true,
                      /*target_sync_ts=*/UINT64_MAX,
                      nullptr,
                      TriggerSyncReason::RENAME);
      if (!s.IsOK()) {
        LOG(INFO) << "Failed to sync src: " << opt->src
                  << ", error: " << s.ToString();
        break;
      }

      // target_sync_ts to UINT64_MAX to force sync
      s = SyncUfsFile(opt->dst,
                      opt->inner_dst,
                      opt->perm,
                      opt->ugi,
                      ufs,
                      /*sync_on_ancestor_not_dir=*/true,
                      /*target_sync_ts=*/UINT64_MAX,
                      nullptr,
                      TriggerSyncReason::RENAME);
      if (!s.IsOK()) {
        LOG(INFO) << "Failed to sync dst: " << opt->dst
                  << ", error: " << s.ToString();
        break;
      }
    }

    {
      std::unique_ptr<LockedPathVector> locked_path =
          std::make_unique<LockedPathVector>(
              this,
              std::vector<std::string>({opt->inner_src, opt->inner_dst}),
              PathLockType::kPathLockTypeWrite);
      s = locked_path->ResolveAndLock();
      if (!s.IsOK()) {
        LOG(INFO) << "Failed to lock src and dst. error: " << s.ToString();
        break;
      }

      auto ctx = std::make_shared<RenameSyncContext>(opt, ufs);
      CHECK(SplitPath(opt->inner_src, &ctx->src_path_components));
      CHECK(GetAllAncestorPaths(opt->inner_src, &ctx->src_lock_components));
      CHECK(SplitPath(opt->inner_dst, &ctx->dst_path_components));
      CHECK(GetAllAncestorPaths(opt->inner_dst, &ctx->dst_lock_components));

      ctx->set_locked_path(locked_path.get());
      *lp = std::move(locked_path);
      *c = std::move(ctx);
    }
  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to prepare rename for src: " << opt->src
              << ", dst: " << opt->dst << ", error: " << s.ToString();
  } else {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "PrepareRenameUfs finished. src: " << opt->src
        << " inner_src: " << opt->inner_src << " dst: " << opt->dst
        << " inner_dst: " << opt->inner_dst;
  }
  return std::move(s);
}

Status NameSpace::RenameUfsCheckSrcDst(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_check_time_);
  sw.Start();
  std::vector<INode> src_ancestors;
  INodeInPath src_parent_iip, src_node_iip;
  const INode& src_parent = src_parent_iip.Inode();
  const INode& src_node = src_node_iip.Inode();
  StatusCode code = GetLastINodeInPath(ctx->src_path_components,
                                       &src_node_iip,
                                       &src_ancestors,
                                       &src_parent_iip,
                                       nullptr,
                                       false);
  if (code != kOK && code != kFileNotFound) {
    return Status(JavaExceptions::kIOException,
                  Code::kError,
                  "Unexpected error on get local inode.");
  }
  if (code == kFileNotFound) {
    return Status(JavaExceptions::kFileNotFoundException,
                  Code::kFileNotFound,
                  "src not existed.");
  }

  // Skip rename ufs under some circumstances
  // not persisted files
  if (src_node.type() == INode_Type_kFile &&
      src_node.ufs_file_info().file_state() == kUfsFileStateToBePersisted &&
      src_node.ufs_file_info().create_type() == kUfsFileCreateTypeNormal) {
    ctx->skip_rename_remote = true;
  }

  // not created dirs
  const bool should_sync_ufs = FLAGS_ufs_sync_mkdir_create_in_ufs;
  if (src_node.type() == INode_Type_kDirectory && !should_sync_ufs) {
    ctx->skip_rename_remote = true;
  }

  std::vector<INode> dst_ancestors;
  INodeInPath dst_parent_iip, dst_node_iip;
  const INode& dst_parent = dst_parent_iip.Inode();
  const INode& dst_node = dst_node_iip.Inode();
  StatusCode dst_code = GetLastINodeInPath(ctx->dst_path_components,
                                           &dst_node_iip,
                                           &dst_ancestors,
                                           &dst_parent_iip,
                                           nullptr,
                                           false);
  if (dst_code != kOK && dst_code != kFileNotFound) {
    return Status(JavaExceptions::kIOException,
                  Code::kError,
                  "Unexpected error on get local inode.");
  }

  if (CheckValidINode(dst_parent) &&
      (dst_parent.type() == INode_Type_kDirectory)) {
    ctx->dst_ancestors = std::move(dst_ancestors);
    ctx->dst_parent = std::move(dst_parent);
  }

  // dst existed
  if (dst_code == kOK) {
    if (false == ctx->opt->overwrite) {
      return Status(JavaExceptions::kFileAlreadyExistsException,
                    Code::kFileExists,
                    "dst already existed, cannot rename.");
    }

    if (src_node.type() != dst_node.type()) {
      return Status(JavaExceptions::kIOException,
                    Code::kError,
                    "Cannot overwrite path with different type, src: " +
                        ctx->SrcUfsPath() + ", dst: " + ctx->DstUfsPath());
    }

    if (dst_node.type() == INode_Type_kDirectory) {
      // Check UFS
      if (!ctx->skip_rename_remote) {
        UfsDirStatus dir;
        auto list_s = ctx->ufs->GetDirectoryStatus(ctx->DstUfsPath(), &dir);
        if (!list_s.IsOK()) {
          LOG(INFO) << "Failed to check dst is empty. path: " << ctx->DstUfsPath()
                    << ", err: " << list_s.ToString();
          return Status(JavaExceptions::kIOException,
                        Code::kError,
                        "Failed to check if dst dir is empty is ufs. err: " +
                            list_s.ToString());
        }
        if (dir.HasChildren()) {
          LOG(INFO) << "Cannot overwrite non-empty dst: " + ctx->DstUfsPath();
          return Status(
              JavaExceptions::kIOException,
              Code::kFileStatusError,
              "Rename destination directory is not empty: " + ctx->DstUfsPath());
        }
      }

      // Check Local FS, file may not have been uploaded to UFS yet
      if (dst_node.type() == INode_Type_kDirectory &&
          meta_storage_->HasSubINodes(dst_node.id())) {
        LOG(INFO) << "Rename destination directory is not empty: "
                  << ctx->DstUfsPath();
        return Status(
            JavaExceptions::kIOException,
            Code::kFileStatusError,
            "Rename destination directory is not empty: " + ctx->DstUfsPath());
      }
    }

    ctx->dst_node = std::move(dst_node);
  }

  ctx->src_ancestors = std::move(src_ancestors);
  ctx->src_parent = std::move(src_parent);
  ctx->src_node = std::move(src_node);
  return Status::OK();
}

Status NameSpace::RenameUfsCheckSrcDstFilesLimit(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  if (FLAGS_ufs_tos_shallow_copy_enabled) {
    ctx->opt->acc_max_count = FLAGS_ufs_tos_shallow_copy_max_count;
    ctx->opt->acc_max_size = 0;
  }
  if (ctx->opt->acc_max_count <= 0 && ctx->opt->acc_max_size <= 0) {
    return Status::OK();
  }
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs check ufs files num to be renamed. ufs_path: "
      << ctx->SrcUfsPath() << ", max_count: " << ctx->opt->acc_max_count
      << ", max_size: " << ctx->opt->acc_max_size;
  uint64_t total_count = 0;
  uint64_t total_size = 0;
  Status s;
  ListFilesOption list_opt;
  list_opt.recursive = true;
  while (true) {
    ListFilesResult res;
    s = ctx->ufs->ListFiles(ctx->SrcUfsPath(), list_opt, &res);
    if (!s.IsOK()) {
      break;
    }

    total_count += res.files.size();
    for (auto&& f : res.files) {
      total_size += f.FileSize();
    }

    if (ctx->opt->acc_max_count > 0) {
      if (total_count >= ctx->opt->acc_max_count) {
        s = Status(
            JavaExceptions::kMaxDirectoryItemsExceededException,
            Code::kUfsTooManyFiles,
            "The number of files to be renamed exceeded the max_count in "
            "request.");
        break;
      }
    }
    if (ctx->opt->acc_max_size > 0) {
      if (total_size >= ctx->opt->acc_max_size) {
        s = Status(JavaExceptions::kMaxDirectoryItemsExceededException,
                   Code::kUfsTooManyFiles,
                   "The size of files to be renamed exceeded the max_size in "
                   "request.");
        break;
      }
    }

    if (!res.has_more) {
      break;
    }
    list_opt.continue_token = res.continue_token;
  }

  if (s.IsOK()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncRenameUfs check ufs files num or size to be renamed "
           "finished. ufs_path: "
        << ctx->SrcUfsPath() << ", total_count: " << total_count;
  } else {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncRenameUfs check ufs files num or size failed. ufs_path: "
        << ctx->SrcUfsPath() << ", max_count: " << ctx->opt->acc_max_count
        << ", max_size: " << ctx->opt->acc_max_size
        << ", error: " << s.ToString();
  }
  return std::move(s);
}

Status NameSpace::RenameUfsRemoveDst(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_remove_dst_time_);
  sw.Start();
  if (!ctx->IsDstNodeValid()) {
    return Status::OK();
  }

  CHECK(ctx->opt->overwrite);

  Status s;
  if (ctx->dst_node.type() == INode_Type_kDirectory) {
    s = ctx->ufs->DeleteDirectory(ctx->DstUfsPath());
  } else {
    s = ctx->ufs->DeleteFile(ctx->DstUfsPath());
  }
  if (!s.IsOK() && s.code() != Code::kFileNotFound) {
    LOG(INFO) << "Failed to delete dst files: " << ctx->DstUfsPath()
              << ", error: " << s.ToString();
    return s;
  }

  return Status::OK();
}

Status NameSpace::RenameUfsCopyUfsFiles(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_copy_ufs_file_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs copy ufs src files. src: " << ctx->SrcUfsPath()
      << " inner_src: " << ctx->SrcInnerPath() << " dst: " << ctx->DstUfsPath()
      << " inner_dst: " << ctx->DstInnerPath();
  Status s;
  s = ctx->ufs->CreateMissingParentIfNecessary(ctx->DstUfsPath());
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to create dst missing parent: " << ctx->DstUfsPath()
              << ", error: " << s.ToString();
    return s;
  }

  UfsCopyResult result;
  const uint64_t now_ms = TimeUtil::GetNowEpochMs();
  const uint64_t now_sec = now_ms / 1000;
  result.SetCopyTimeStamp(now_sec);
  Thread result_handler([this, &ctx, &result]() -> bool {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    while (true) {
      if (!FLAGS_ufs_rename_update_local_etag) {
        result.SetCopyFinished();
        break;
      }
      auto tmp_result = result.ExtractResult();
      RenameUfsHandleCopyResult(ctx,
                                dir_cache,
                                dir_negative_cache,
                                result.GetCopyTimeStamp(),
                                tmp_result);
      if (!tmp_result.empty()) {
        continue;
      }
      if (result.IsCopyFinished()) {
        break;
      }
      // Renaming one file in ufs is at least 1ms
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    return true;
  });
  result_handler.Start();

  auto&& src_node = ctx->src_node;
  if (src_node.type() == INode_Type_kDirectory) {
    s = ctx->ufs->CopyDirectory(ctx->SrcUfsPath(), ctx->DstUfsPath(), &result);
  } else {
    auto&& file_info = src_node.ufs_file_info();
    if (file_info.file_state() == kUfsFileStatePersisted) {
      s = ctx->ufs->CopyFile(
          ctx->SrcUfsPath(), ctx->DstUfsPath(), false, &result);
    } else {
      result.SetCopyFinished();
      result_handler.Join();
      return Status::OK();
    }
  }

  if (!s.IsOK() && s.code() != Code::kFileNotFound &&
      s.code() != Code::kDirNotFound) {
    LOG(INFO) << "Failed to copy files from src: " << ctx->SrcUfsPath()
              << ", dst: " << ctx->DstUfsPath() << ", error: " << s.ToString();
    result.SetCopyFinished();
    result_handler.Join();
    return s;
  }

  if (s.code() == Code::kFileNotFound || s.code() == Code::kDirNotFound) {
    LOG(INFO) << " Failed to copy files from src: " << ctx->SrcUfsPath()
              << " error: " << s.ToString();
    result.SetCopyFinished();
    result_handler.Join();
    return Status::OK();
  }

  result_handler.Join();
  return Status::OK();
}

Status NameSpace::RenameUfsHandleCopyResult(
    const std::shared_ptr<RenameSyncContext>& ctx,
    LRUCache<std::string, INodeInPath, true>& dir_cache,
    LRUCache<std::string, int, true>& dir_negative_cache,
    uint64_t copy_time_stamp,
    const std::unordered_map<std::string, std::string>& result) {
  for (auto&& it : result) {
    auto&& ufs_path = it.first;
    std::string path;
    if (!UfsUtil::ConvertInnerPath(
             ctx->ufs->ufs_config_ptr()->ufs_prefix, ufs_path, &path)
             .IsOK()) {
      LOG(ERROR) << "[RenameUfsHandleCopyResult] Failed to convert ufs path to "
                    "inner path: "
                 << ufs_path;
      continue;
    }
    auto&& etag = it.second;
    DLOG(INFO) << "[RenameUfsHandleCopyResult] Start process result path: "
               << path << " etag: " << etag;
    std::vector<cnetpp::base::StringPiece> path_components;
    std::vector<cnetpp::base::StringPiece> lock_components;
    if (!SplitPath(path, &path_components) ||
        !GetAllAncestorPaths(path, &lock_components)) {
      LOG(ERROR) << "[RenameUfsHandleCopyResult] Failed to split path " << path;
      continue;
    }
    if (lock_components.size() <= 1) {
      LOG(ERROR) << "[RenameUfsHandleCopyResult] Renaming root path " << path;
      continue;
    }
    std::string parent_path =
        lock_components[lock_components.size() - 2].as_string();
    DLOG(INFO) << "[RenameUfsHandleCopyResult] Result parent path: "
               << parent_path;

    INodeInPath parent_iip;
    parent_iip.MutableInodeUnsafe().set_id(kInvalidINodeId);

    // Optimistic try, lookup parent in cache
    // We always try cache first then negative cache
    // Do not do a predict on previous result since it is just a hash lookup
    if (dir_cache.Get(parent_path, &parent_iip)) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Parent hits cache "
               << parent_path;
    } else {
      int ignored;
      if (dir_negative_cache.Get(parent_path, &ignored)) {
        VLOG(10) << "[RenameUfsHandleCopyResult] Parent hits negative cache "
                 << parent_path;
        continue;
      }
    }

    // Cache miss, find parent in meta storage and fill cache
    if (parent_iip.Inode().id() == kInvalidINodeId) {
      DLOG(INFO) << "[RenameUfsHandleCopyResult] Parent cache miss "
                 << parent_path;
      INodeInPath tmp_parent_iip;

      // If one of the ancestor hits negative cache, add all ancestors starting
      // from that one to negative cache; Or find last ancestor hits cache.
      // Note this loop is on lock_components
      int last_missing_ancestor_index = lock_components.size() - 1;
      bool ancestor_negative_cache_hit = false;
      while (last_missing_ancestor_index >= 0) {
        auto&& full_path =
            lock_components[last_missing_ancestor_index].as_string();

        // We failed in previous cache lookup, so this time we prefer the
        // ancestor does not exist in local. We try negative cache first
        int ignored;
        if (dir_negative_cache.Get(full_path, &ignored)) {
          ancestor_negative_cache_hit = true;
          break;
        }
        if (dir_cache.Get(full_path, &tmp_parent_iip)) {
          break;
        }
        last_missing_ancestor_index--;
      }

      if (ancestor_negative_cache_hit) {
        while (last_missing_ancestor_index < lock_components.size() - 1) {
          auto&& full_path =
              lock_components[last_missing_ancestor_index].as_string();
          VLOG(10) << "[RenameUfsHandleCopyResult] Add negative cache "
                   << full_path;
          dir_negative_cache.Set(full_path, 0);
          last_missing_ancestor_index++;
        }
        VLOG(10) << "[RenameUfsHandleCopyResult] Ancestor negative cache hit, "
                    "skip current object "
                 << path;
        continue;
      }

      DLOG(INFO) << "[RenameUfsHandleCopyResult] Start to find ancestor from "
                    "meta storage, last missing ancestor index "
                 << last_missing_ancestor_index;

      // Root does not find
      if (last_missing_ancestor_index == -1) {
        tmp_parent_iip.SwitchToChild(meta_storage_->GetRootINode());
        dir_cache.Set("/", tmp_parent_iip);
        last_missing_ancestor_index++;
      }

      // Parent is root, do not need to check meta storage
      if (path_components.size() == 1) {
        DLOG(INFO) << "[RenameUfsHandleCopyResult] Parent is root " << path;
        parent_iip = tmp_parent_iip;
      }

      // Find missing ancestors in meta storage
      // Note this loop is on path_components
      ancestor_negative_cache_hit = false;
      for (int i = last_missing_ancestor_index; i < path_components.size() - 1;
           ++i) {
        auto&& full_path = lock_components[i + 1].as_string();
        auto&& name = path_components[i].as_string();
        VLOG(10)
            << "[RenameUfsHandleCopyResult] Finding path from meta storage "
            << full_path << ", name " << name;

        if (ancestor_negative_cache_hit) {
          dir_negative_cache.Set(full_path, 0);
          VLOG(10)
              << "[RenameUfsHandleCopyResult] Find path from meta storage, "
                 "ancestor hits negative cache "
              << full_path;
          continue;
        }

        INode& inode = tmp_parent_iip.MutableInodeUnsafe();
        auto code =
            meta_storage_->GetINode(tmp_parent_iip.Inode().id(), name, &inode);
        if (code == kError) {
          LOG(ERROR) << "[RenameUfsHandleCopyResult] Failed to get inode from "
                        "meta storage: "
                     << full_path << ", name: " << name;
          dir_negative_cache.Set(full_path, 0);
          ancestor_negative_cache_hit = true;
          break;
        }
        if (code == kFileNotFound) {
          VLOG(10)
              << "[RenameUfsHandleCopyResult] Find path from meta storage, "
                 "add negative cache "
              << full_path;
          dir_negative_cache.Set(full_path, 0);
          ancestor_negative_cache_hit = true;
          continue;
        }
        tmp_parent_iip.CollectAttrs(inode);
        tmp_parent_iip.FinalizeAttrs();
        dir_cache.Set(full_path, tmp_parent_iip);
        parent_iip = tmp_parent_iip;
        VLOG(10) << "[RenameUfsHandleCopyResult] Find path from meta storage, "
                    "add cache "
                 << full_path;
        DLOG(INFO)
            << "[RenameUfsHandleCopyResult] Find path from meta storage, "
               "parent found "
            << inode.ShortDebugString();
      }

      if (ancestor_negative_cache_hit) {
        VLOG(10) << "[RenameUfsHandleCopyResult] Ancestor negative cache hit "
                 << path;
        continue;
      }
    }

    // Local parent inode not found
    if (parent_iip.Inode().id() == kInvalidINodeId) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Local parent inode not found "
               << path;
      continue;
    }

    // Local parent inode found
    DLOG(INFO) << "[RenameUfsHandleCopyResult] Local parent found: "
               << parent_iip.Inode().ShortDebugString();

    auto&& name = path_components[path_components.size() - 1].as_string();
    INode& inode = parent_iip.MutableInodeUnsafe();
    auto code =
        meta_storage_->GetINode(nullptr, parent_iip.Inode().id(), name, &inode);
    if (code == kError) {
      LOG(ERROR) << "[RenameUfsHandleCopyResult] Failed to get inode from "
                    "meta storage: "
                 << path << ", name: " << name;
      continue;
    }
    if (code == kFileNotFound) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Local inode not found " << path;
      continue;
    }
    if (inode.type() != INode_Type_kFile) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Local inode is not file " << path
               << " inode " << inode.ShortDebugString();
      continue;
    }
    if (inode.ufs_file_info().file_state() == kUfsFileStateToBePersisted) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Local inode is to be persisted "
               << path;
      continue;
    }
    if (inode.ufs_file_info().etag() == etag) {
      VLOG(10) << "[RenameUfsHandleCopyResult] Local inode etag match " << path
               << " etag " << inode.ufs_file_info().etag();
      continue;
    }
    parent_iip.CollectSnapshotInfo(inode);
    parent_iip.CollectAttrs(inode);
    parent_iip.FinalizeAttrs();

    DLOG(INFO) << "[RenameUfsHandleCopyResult] inode to be update "
               << inode.ShortDebugString();

    // batch?
    SnapshotLog inode_snaplog;
    parent_iip.GenerateSnapshotLog(&inode_snaplog);
    INode old_inode = inode;
    inode.mutable_ufs_file_info()->set_etag(etag);
    inode.mutable_ufs_file_info()->set_sync_ts(copy_time_stamp);
    int64_t txid =
        edit_log_sender_->LogAccSyncUpdateINode(path, inode, old_inode);
    CHECK_NE(txid, kInvalidTxId);

    SynchronizedRpcClosure done;
    meta_storage_->OrderedUpdateINode(
        &inode, &old_inode, inode_snaplog, txid, &done);
    done.Await();
    LOG(INFO) << "[RenameUfsHandleCopyResult] Update inode " << path
              << ", inode id " << inode.id() << ", old etag "
              << old_inode.ufs_file_info().etag() << ", new etag "
              << inode.ufs_file_info().etag();
  }
  return Status::OK();
}

Status NameSpace::RenameUfsDeleteUfsFiles(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_delete_ufs_file_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs delete src files. src: " << ctx->SrcUfsPath()
      << " inner_src: " << ctx->SrcInnerPath() << " dst: " << ctx->DstUfsPath()
      << " inner_dst: " << ctx->DstInnerPath();
  Status s;
  if (ctx->src_node.type() == INode_Type_kDirectory) {
    s = ctx->ufs->DeleteDirectory(ctx->SrcUfsPath());
  } else {
    auto&& file_info = ctx->src_node.ufs_file_info();
    if (file_info.file_state() == kUfsFileStatePersisted) {
      s = ctx->ufs->DeleteFile(ctx->SrcUfsPath());
    }
  }
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to delete files src: " << ctx->SrcUfsPath()
              << ", dst: " << ctx->DstUfsPath() << ", error: " << s.ToString();
    return s;
  }
  return Status::OK();
}

Status NameSpace::RenameUfsRenameRemote(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_remote_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs rename remote src files. src: " << ctx->SrcUfsPath()
      << " inner_src: " << ctx->SrcInnerPath() << " dst: " << ctx->DstUfsPath()
      << " inner_dst: " << ctx->DstInnerPath();
  Status s = ctx->ufs->CreateMissingParentIfNecessary(ctx->DstUfsPath());
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to create dst parent dir: " << ctx->DstUfsPath()
              << ", error: " << s.ToString();
    return s;
  }
  s = ctx->ufs->Rename(
      ctx->SrcUfsPath(), ctx->DstUfsPath(), ctx->opt->overwrite);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to rename files src: " << ctx->SrcUfsPath()
              << ", dst: " << ctx->DstUfsPath() << ", error: " << s.ToString();
    return s;
  }
  return Status::OK();
}

Status NameSpace::RenameUfsRenameLocal(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_rename_ufs_local_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncRenameUfs rename local directory. src: " << ctx->SrcUfsPath()
      << " inner_src: " << ctx->SrcInnerPath() << " dst: " << ctx->DstUfsPath()
      << " inner_dst: " << ctx->DstInnerPath();

  std::string src_name = ctx->src_path_components.back().as_string();
  INode old_src_node;
  StatusCode src_code =
      meta_storage_->GetINode(ctx->src_parent.id(), src_name, &old_src_node);
  if (src_code != kOK) {
    LOG(INFO) << "RenameUfsRenameLocal failed to get src node from local. "
                 "inner_src: "
              << ctx->opt->inner_src << ", error: " << src_code;
    return Status(JavaExceptions::kIOException,
                  Code::kError,
                  "Failed to get src node. error: " + std::to_string(src_code));
  }
  if (old_src_node.type() == INode_Type_kFile) {
    if (old_src_node.ufs_file_info().etag() !=
        ctx->src_node.ufs_file_info().etag()) {
      ctx->src_node.CopyFrom(old_src_node);
    }
  }

  if (!ctx->IsDstParentValid()) {
    auto s = CreateMissingDstParentInRename(ctx);
    if (!s.IsOK()) {
      LOG(INFO) << "RenameUfsRenameLocal failed when creating parent, path: "
                << ctx->opt->inner_dst << ", error: " << s.ToString();
      return s;
    }
  }

  std::string dst_name = ctx->dst_path_components.back().as_string();
  INode old_dst_node;
  std::vector<BlockProto> old_dst_blocks;
  StatusCode dst_code =
      meta_storage_->GetINode(ctx->dst_parent.id(), dst_name, &old_dst_node);
  if (dst_code != kOK && dst_code != kFileNotFound) {
    LOG(INFO) << "RenameUfsRenameLocal failed to get dst node from local. "
                 "inner_dst: "
              << ctx->opt->inner_dst << ", error: " << dst_code;
    return Status(JavaExceptions::kIOException,
                  Code::kError,
                  "Failed to get dst node. error: " + std::to_string(dst_code));
  }

  // Dst existed, remove
  if (dst_code == kOK) {
    if (old_dst_node.type() == INode_Type_kFile) {
      GetFileBlocksInternal(old_dst_node, &old_dst_blocks);
    }
  }

  INode dst_node;
  dst_node.CopyFrom(ctx->src_node);
  dst_node.set_id(ctx->src_node.id());
  dst_node.set_parent_id(ctx->dst_parent.id());
  dst_node.set_name(dst_name);

  uint64_t now_ms = TimeUtil::GetNowEpochMs();
  ctx->src_parent.set_mtime(now_ms);
  ctx->dst_parent.set_mtime(now_ms);

  std::vector<INodeID> src_ancestors_id;
  std::for_each(ctx->src_ancestors.begin(),
                ctx->src_ancestors.end(),
                [&src_ancestors_id](const INode& inode) {
                  src_ancestors_id.push_back(inode.id());
                });
  std::vector<INodeID> dst_ancestors_id;
  std::for_each(ctx->dst_ancestors.begin(),
                ctx->dst_ancestors.end(),
                [&dst_ancestors_id](const INode& inode) {
                  dst_ancestors_id.push_back(inode.id());
                });
  SynchronizedRpcClosure done;
  int64_t txid = edit_log_sender_->LogRenameV2(
      ctx->SrcInnerPath(),
      ctx->DstInnerPath(),
      ctx->src_node,
      dst_node,
      ctx->src_parent,
      ctx->dst_parent,
      ctx->opt->overwrite,
      CheckValidINode(old_dst_node) ? &old_dst_node : nullptr,
      false,
      nullptr,
      nullptr,
      nullptr,
      now_ms,
      src_ancestors_id,
      dst_ancestors_id,
      nullptr,
      {},
      {},
      {},
      {},
      {},
      {});
  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(&done, txid);

  std::vector<INodeAndSnapshot> inodes_mov_src;
  INodeInPath src_iip;
  src_iip.MutableInodeUnsafe() = ctx->src_node;
  src_iip.CollectSnapshotInfo(ctx->src_node);
  src_iip.CollectAttrs(ctx->src_node);
  // CHECK(!src_iip.NeedBackupForDeletion());
  // CHECK(!src_iip.NeedBackupForModification());
  src_iip.FinalizeAttrs();
  src_iip.RecordDeletion();
  SnapshotLog src_inode_snaplog;
  src_iip.GenerateSnapshotLog(&src_inode_snaplog);
  inodes_mov_src.emplace_back(&src_iip.MutableInode(), &src_inode_snaplog);
  std::vector<INode*> inodes_mov_dst;
  INodeInPath dst_iip;
  dst_iip.MutableInodeUnsafe() = dst_node;
  dst_iip.CollectSnapshotInfo(dst_node);
  dst_iip.CollectAttrs(dst_node);
  // CHECK(!dst_iip.NeedBackupForDeletion());
  // CHECK(!dst_iip.NeedBackupForModification());
  dst_iip.FinalizeAttrs();
  inodes_mov_dst.emplace_back(&dst_iip.MutableInode());
  //
  INodeInPath old_dst_iip;
  SnapshotLog old_dst_inode_snaplog;
  std::vector<INodeAndSnapshot> inodes_del;
  if (CheckValidINode(old_dst_node)) {
    old_dst_iip.MutableInodeUnsafe() = old_dst_node;
    old_dst_iip.CollectSnapshotInfo(old_dst_node);
    old_dst_iip.CollectAttrs(old_dst_node);
    // CHECK(!old_dst_iip.NeedBackupForDeletion());
    // CHECK(!old_dst_iip.NeedBackupForModification());
    old_dst_iip.FinalizeAttrs();
    old_dst_iip.RecordDeletion();
    old_dst_iip.GenerateSnapshotLog(&old_dst_inode_snaplog);
    inodes_del.emplace_back(&old_dst_iip.MutableInode(),
                            &old_dst_inode_snaplog);
  }
  //
  std::vector<INodeAndSnapshot> parents;
  INodeInPath src_parent_iip;
  src_parent_iip.MutableInodeUnsafe() = ctx->src_parent;
  src_parent_iip.CollectSnapshotInfo(ctx->src_parent);
  src_parent_iip.CollectAttrs(ctx->src_parent);
  // CHECK(!src_parent_iip.NeedBackupForDeletion());
  // CHECK(!src_parent_iip.NeedBackupForModification());
  src_parent_iip.FinalizeAttrs();
  SnapshotLog src_parent_snaplog;
  src_parent_iip.GenerateSnapshotLog(&src_parent_snaplog);
  parents.emplace_back(&src_parent_iip.MutableInode(), &src_parent_snaplog);
  INodeInPath dst_parent_iip;
  dst_parent_iip.MutableInodeUnsafe() = ctx->dst_parent;
  dst_parent_iip.CollectSnapshotInfo(ctx->dst_parent);
  dst_parent_iip.CollectAttrs(ctx->dst_parent);
  // CHECK(!dst_parent_iip.NeedBackupForDeletion());
  // CHECK(!dst_parent_iip.NeedBackupForModification());
  dst_parent_iip.FinalizeAttrs();
  SnapshotLog dst_parent_snaplog;
  dst_parent_iip.GenerateSnapshotLog(&dst_parent_snaplog);
  parents.emplace_back(&dst_parent_iip.MutableInode(), &dst_parent_snaplog);
  //
  std::vector<INode> inodes_old;
  inodes_old.push_back(ctx->src_node);
  if (CheckValidINode(old_dst_node)) {
    inodes_old.push_back(old_dst_node);
  }
  meta_storage_->OrderedCommitINodes(nullptr,
                                     nullptr,
                                     &inodes_mov_src,
                                     &inodes_mov_dst,
                                     &inodes_del,
                                     &parents,
                                     &inodes_old,
                                     {},
                                     {},
                                     txid,
                                     {&done});
  done.Await();

  Status rename_s = done.status();
  if (rename_s.IsOK()) {
    // if (!old_dst_blocks.empty()) {
    //   block_manager_->RemoveBlocksAndUpdateSafeMode(old_dst_blocks);
    // }
  } else {
    LOG(INFO) << "RenameUfsRenameLocal failed to rename local. inner_src: "
              << ctx->opt->inner_src << ", inner_dst: " << ctx->opt->inner_dst
              << ", error: " << rename_s.ToString();
  }
  return std::move(rename_s);
}

Status NameSpace::CreateMissingDstParentInRename(
    const std::shared_ptr<RenameSyncContext>& ctx) {
  CHECK(ctx->locked_path() != nullptr);
  auto&& dst_rp = ctx->locked_path()->GetPath(ctx->DstInnerPath());
  INode parent;
  std::vector<INode> ancestors;
  auto s = CreateMissingParentLocal(ctx->ufs,
                                    ctx->DstUfsPath(),
                                    ctx->DstInnerPath(),
                                    ctx->dst_path_components,
                                    ctx->dst_lock_components,
                                    ctx->opt->perm,
                                    dst_rp,
                                    false,
                                    &ancestors,
                                    &parent);
  if (!s.IsOK()) {
    return s;
  }
  ctx->dst_ancestors = std::move(ancestors);
  ctx->dst_parent = std::move(parent);
  return Status::OK();
}

Status NameSpace::SyncDeleteUfs(const std::shared_ptr<DeleteOption>& opt,
                                const std::shared_ptr<Ufs>& ufs) {
  StopWatch sw(metrics_.acc_sync_delete_ufs_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncDeleteUfs start. path: " << opt->path
      << ", inner_path: " << opt->inner_path;
  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<DeleteSyncContext> ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    Status prepare_s =
        PrepareDeleteFromUfs(opt, ufs, &ctx, &locked_path, &ha_barrier);
    if (!prepare_s.IsOK() && prepare_s.code() != Code::kFileNotFound) {
      s = std::move(prepare_s);
      break;
    }
    if (prepare_s.code() == Code::kFileNotFound) {
      VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
          << "SyncDeleteUfs path not existed, return delete ok. path: "
          << opt->path;
      s = Status::OK();
      break;
    }

    s = PreCheckDeleteUfsFiles(ctx);
    if (!s.IsOK()) {
      break;
    }

    // Skip if file is not persisted
    bool skip_delete_remote =
        ctx->node.type() == INode_Type_kFile &&
        ctx->node.ufs_file_info().file_state() == kUfsFileStateToBePersisted &&
        ctx->node.ufs_file_info().create_type() == kUfsFileCreateTypeNormal;

    if (!skip_delete_remote) {
      s = DeleteUfsDeleteUfsFiles(ctx);
      if (!s.IsOK() && s.code() != Code::kFileNotFound) {
        break;
      }
    }

    s = DeleteUfsDeleteLocal(ctx);
    if (!s.IsOK()) {
      break;
    }
  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "SyncDeleteUfs failed. path: " << opt->path
              << ", inner_path: " << opt->inner_path
              << ", error: " << s.ToString();
    return std::move(s);
  }

  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncDeleteUfs finished. path: " << opt->path
      << ", inner_path: " << opt->inner_path;
  return Status::OK();
}

bool ShouldCheckChildrenOnDelete(const INode& dir,
                                 const std::shared_ptr<DeleteOption>& opt) {
  if (dir.type() != INode_Type_kDirectory || !dir.has_ufs_dir_info()) {
    return true;
  }
  auto&& dir_info = dir.ufs_dir_info();
  if (dir_info.state() != kUfsDirStateSynced) {
    return true;
  }
  int32_t sync_interval = opt->acc_fs_info.syncinterval();

  if (sync_interval != kSyncIntervalNever &&
      sync_interval < FLAGS_ufs_sync_min_interval) {
    sync_interval = FLAGS_ufs_sync_min_interval;
  }

  if (sync_interval == kSyncIntervalNever) {
    return false;
  }
  if (sync_interval == kSyncIntervalAlways) {
    return true;
  }
  const uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
  return (now_sec < dir_info.children_sync_ts()) ||
         (now_sec - dir_info.children_sync_ts()) > sync_interval;
}

Status NameSpace::PrepareDeleteFromUfs(const std::shared_ptr<DeleteOption>& opt,
                                       const std::shared_ptr<Ufs>& ufs,
                                       std::shared_ptr<DeleteSyncContext>* c,
                                       std::unique_ptr<LockedPath>* lp,
                                       vshared_lock* barrier) {
  StopWatch sw(metrics_.acc_prepare_delete_from_ufs_time_);
  sw.Start();
  do {
    std::shared_ptr<FileSyncContext> sync_ctx;
    std::unique_ptr<LockedPath> locked_path;
    vshared_lock ha_barrier;
    Status s = PrepareSyncUfsFile(opt->path,
                                  opt->inner_path,
                                  opt->perm,
                                  opt->ugi,
                                  ufs,
                                  true,
                                  &sync_ctx,
                                  &locked_path,
                                  &ha_barrier);
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to prepare sync src: " << opt->path
                << ", error: " << s.ToString();
      return s;
    }

    if (FLAGS_enable_ufs_sync_in_operation) {
      sync_ctx->trigger_sync_reason = TriggerSyncReason::DELETE;
      s = SyncUfsFileInnerWithWriteLock(sync_ctx);
      if (!s.IsOK()) {
        LOG(INFO) << "Failed to sync src: " << opt->path
                  << ", error: " << s.ToString();
        return s;
      }
    }

    INodeInPath node_iip, parent_iip;
    const INode& node = node_iip.Inode();
    const INode& parent = parent_iip.Inode();
    StatusCode code = GetLastINodeInPath(sync_ctx->path_components,
                                         &node_iip,
                                         nullptr,
                                         &parent_iip,
                                         nullptr,
                                         false);
    if (code != kOK && code != kFileNotFound) {
      return Status(JavaExceptions::kIOException,
                    Code::kError,
                    "SyncDeleteUfs failed to get inode from local.");
    }
    if (code == kFileNotFound) {
      return Status(JavaExceptions::kFileNotFoundException,
                    Code::kFileNotFound,
                    "SyncDeleteUfs failed to get inode from local.");
    }

    if (node.id() == kRootINodeId) {
      return Status(JavaExceptions::kIllegalArgumentException,
                    Code::kBadParameter,
                    "Cannot remove root. path: " + opt->path);
    }

    if (node.type() == INode_Type_kDirectory) {
      if (!opt->recursive) {
        bool has_children = false;
        if (ShouldCheckChildrenOnDelete(node, opt)) {
          // Check if directory is empty
          // Local dir children might not be synced, we should check UFS
          UfsDirStatus dir;
          auto dir_s = ufs->GetDirectoryStatus(opt->path, &dir);
          if (!dir_s.IsOK()) {
            return std::move(dir_s);
          }
          has_children = dir.HasChildren();
        } else {
          has_children = meta_storage_->HasSubINodes(node.id());
        }
        if (has_children) {
          LOG(INFO)
              << "SyncDeleteUfs cannot remove dir as it's not empty. path: "
              << opt->path;
          return Status(
              JavaExceptions::Exception::kPathIsNotEmptyDirectoryException,
              opt->path + " is not empty");
        }
      }
    }

    auto ctx = std::make_shared<DeleteSyncContext>(opt, ufs);
    {
      ctx->path_components = std::move(sync_ctx->path_components);
      ctx->lock_components = std::move(sync_ctx->lock_components);
      ctx->parent = std::move(parent);
      ctx->node = std::move(node);
    }

    ctx->set_locked_path(locked_path.get());
    *lp = std::move(locked_path);
    *barrier = std::move(ha_barrier);
    *c = std::move(ctx);
  } while (0);

  return Status::OK();
}

Status NameSpace::PreCheckDeleteUfsFiles(
    const std::shared_ptr<DeleteSyncContext>& ctx) {
  if (ctx->opt->acc_max_count <= 0) {
    return Status::OK();
  }
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncDeleteUfs check ufs files num to be deleted. ufs_path: "
      << ctx->UfsPath() << ", max: " << ctx->opt->acc_max_count;
  uint64_t total_count = 0;
  Status s;
  ListFilesOption list_opt;
  list_opt.recursive = true;
  while (true) {
    ListFilesResult res;
    s = ListUfsDirWithRetry(ctx->ufs, ctx->UfsPath(), list_opt, &res);
    if (!s.IsOK()) {
      break;
    }

    total_count += res.files.size();
    if (total_count >= ctx->opt->acc_max_count) {
      s = Status(JavaExceptions::kMaxDirectoryItemsExceededException,
                 Code::kUfsTooManyFiles,
                 "The number of files to be deleted exceeded the max_count in "
                 "request.");
      break;
    }

    if (!res.has_more) {
      break;
    }
    list_opt.continue_token = res.continue_token;
  }

  if (s.IsOK()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncDeleteUfs check ufs files num to be deleted finished. "
           "ufs_path: "
        << ctx->UfsPath() << ", total_count: " << total_count;
  } else {
    VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
        << "SyncDeleteUfs check ufs files num failed. ufs_path: "
        << ctx->UfsPath() << ", max: " << ctx->opt->acc_max_count
        << ", error: " << s.ToString();
  }
  return std::move(s);
}

Status NameSpace::DeleteUfsDeleteUfsFiles(
    const std::shared_ptr<DeleteSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_delete_ufs_delete_ufs_file_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncDeleteUfs will delete ufs files. ufs_path: " << ctx->UfsPath();
  Status s;
  if (ctx->node.type() == INode_Type_kDirectory) {
    s = ctx->ufs->DeleteDirectory(ctx->UfsPath());
  } else {
    s = ctx->ufs->DeleteFile(ctx->UfsPath());
  }
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to delete files from ufs. path: " << ctx->UfsPath()
              << ", error: " << s.ToString();
    return std::move(s);
  }

  return Status::OK();
}

Status NameSpace::DeleteUfsDeleteLocal(
    const std::shared_ptr<DeleteSyncContext>& ctx) {
  StopWatch sw(metrics_.acc_delete_ufs_delete_local_file_time_);
  sw.Start();
  VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
      << "SyncDeleteUfs will delete local files. inner_path: "
      << ctx->InnerPath();
  uint64_t now_ms = TimeUtil::GetNowEpochMs();

  ctx->parent.set_mtime(now_ms);

  int64_t txid = edit_log_sender_->LogDeleteV2(ctx->InnerPath(),
                                               ctx->node,
                                               ctx->parent,
                                               now_ms,
                                               {},
                                               {},
                                               {},
                                               LogRpcInfo());
  CHECK_NE(txid, kInvalidTxId);

  SynchronizedRpcClosure done;
  SetServerTxid(&done, txid);

  std::vector<BlockProto> blocks_to_remove;
  if (ctx->node.type() == INode_Type_kFile) {
    GetFileBlocksInternal(ctx->node, &blocks_to_remove);
  }

  meta_storage_->OrderedDeleteINode(ctx->node, txid, &done, &ctx->parent);
  done.Await();

  if (done.status().IsOK()) {
    const INode& inode_del = ctx->node;
    DeleteINodeCallback(inode_del, ctx->InnerPath());
  } else {
    LOG(INFO) << "Failed to delete local inode. path: " << ctx->UfsPath()
              << ", inner_path: " << ctx->InnerPath()
              << ", error: " << done.status().ToString();
  }
  return done.status();
}

Status NameSpace::MkdirsUfsInternal(const std::string& ufs_path,
                                    const std::string& inner_path,
                                    const PermissionStatus& p,
                                    const UserGroupInfo& ugi,
                                    const std::shared_ptr<Ufs>& ufs,
                                    bool create_parent,
                                    RpcController* ctx,
                                    SyncTask* sync_task) {
  RPC_SW_CTX_INIT_BY_CTX(rpc_sw_ctx, ctx);

  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  const bool should_sync_ufs = FLAGS_ufs_sync_mkdir_create_in_ufs;

  StopWatch sw;
  sw.Start();

  struct Holder {
    std::unique_ptr<LockedPath> locked_path{nullptr};
    vshared_lock ha_barrier;
    std::shared_ptr<FileSyncContext> f_ctx{nullptr};
  };

  std::shared_ptr<Holder> holder = std::make_shared<Holder>();

  Status s;

  if (should_sync_ufs) {
    sw.NextStep(metrics_.acc_mkdirs_ufs_time_);
    s = PrepareSyncUfsFile(ufs_path,
                           inner_path,
                           p,
                           ugi,
                           ufs,
                           true,
                           &holder->f_ctx,
                           &holder->locked_path,
                           &holder->ha_barrier);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after cfs->PrepareSyncUfsFile");
    RETURN_NOT_OK(s);

    holder->f_ctx->trigger_sync_reason = TriggerSyncReason::MKDIR;
    s = SyncUfsFileInnerWithWriteLock(holder->f_ctx);
    RETURN_NOT_OK(s);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after cfs->SyncUfsFileInnerWithWriteLock");

    if (create_parent) {
      s = ufs->CreateMissingParentIfNecessary(ufs_path);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ufs->CreateMissingParentIfNecessary");
      RETURN_NOT_OK_LOG_MSG(s, "Ufs create missing parent failed: " + ufs_path);
    }

    s = ufs->CheckParentReady(ufs_path);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after ufs->CheckParentReady");
    RETURN_NOT_OK_LOG_MSG(s, "Check parent existing failed: " + ufs_path);

    s = ufs->Mkdir(ufs_path);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after ufs->Mkdir");
    RETURN_NOT_OK_LOG_MSG(s, "Ufs mkdir failed: " + ufs_path);
  }
  sw.NextStep(metrics_.acc_mkdirs_local_time_);
  // TODO(xiong): why we need this???
  if (should_sync_ufs) {
    s = CreateMissingParentInSync(holder->f_ctx);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after cfs->CreateMissingParentInSync");
    RETURN_NOT_OK(s);
  }

  RpcClosure* done = NewRpcCallback();
  done->add_post_callback(
      [=, holder = std::move(holder), cb = sync_task->ReleaseCb()](
          const Status& s) mutable {
        if (!s.IsOK()) {
          LOG(INFO) << "Inner ns mkdirs failed";
          if (should_sync_ufs) {
            Status s_undo = ufs->AbortMkdir(ufs_path);
            if (!s_undo.IsOK()) {
              LOG(INFO) << "Undo ufs mkdirs failed";
            }
          }
        }

        if (cb) {
          cb(s);
        }

        holder->f_ctx.reset();
        holder->locked_path.reset();
      });

  // should_sync_ufs == no need to lock
  AsyncMkDirs(inner_path,
              p,
              ugi,
              create_parent,
              false,
              ctx,
              LogRpcInfo(),
              done,
              UfsUtil::FillSyncedUfsDirInode,
              /*need_lock=*/!should_sync_ufs);

  return {};
}

Status NameSpace::MkdirsUfs(const std::string& ufs_path,
                            const std::string& inner_path,
                            const PermissionStatus& p,
                            const UserGroupInfo& ugi,
                            const std::shared_ptr<Ufs>& ufs,
                            bool create_parent,
                            RpcController* ctx,
                            SyncTask* sync_task) {
  StopWatch sw(metrics_.acc_mkdirs_time_);
  sw.Start();

  auto s = MkdirsUfsInternal(
      ufs_path, inner_path, p, ugi, ufs, create_parent, ctx, sync_task);

  if (!s.IsOK()) {
    LOG(INFO) << "Failed to mkdir. ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }
  return s;
}

Status NameSpace::CreateUfsFile(const std::string& ufs_path,
                                const std::string& inner_path,
                                const PermissionStatus& p,
                                const NetworkLocationInfo client_location,
                                const UserGroupInfo& ugi,
                                const std::shared_ptr<Ufs>& ufs,
                                const LogRpcInfo& rpc_info,
                                const std::string& client_machine,
                                const cloudfs::CreateRequestProto* request,
                                cloudfs::CreateResponseProto* response,
                                RpcController* ctx) {
  RPC_SW_CTX_INIT_BY_CTX(rpc_sw_ctx, ctx);
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  StopWatch sw(metrics_.acc_create_time_);
  sw.Start();
  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> f_ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    s = PrepareSyncUfsFile(ufs_path,
                           inner_path,
                           p,
                           ugi,
                           ufs,
                           true,
                           &f_ctx,
                           &locked_path,
                           &ha_barrier,
                           rpc_sw_ctx.get());
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after PrepareSyncUfsFile");
    if (!s.IsOK()) {
      break;
    }
    f_ctx->trigger_sync_reason = TriggerSyncReason::CREATE_FILE;
    s = SyncUfsFileInnerWithWriteLock(f_ctx);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after SyncUfsFileInnerWithWriteLock");
    if (!s.IsOK()) {
      break;
    }

    StopWatch sw_inner(metrics_.acc_create_ufs_time_);
    sw_inner.Start();
    std::string key;
    s = ufs->GetUfsIdentifier(UfsIdentifierInfo(ufs_path), &key);
    if (!s.IsOK()) {
      break;
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after GetUfsIdentifier");

    std::function<Status(INode*)> ProcessUfsInfo =
        [key, ufs_path, request, ufs, rpc_sw_ctx](INode* inode) {
          UfsUtil::FillUfsFileInfoForNewFile(
              *(inode->mutable_ufs_file_info()), request->createflag(), key);
          RPC_SW_CTX_LOG(rpc_sw_ctx, "after FillUfsFileInfoForNewFile");

          bool delete_ufs = false;
          delete_ufs |=
              CreateFlag::ContainsOverwrite(request->createflag()) &&
              FLAGS_ufs_delete_for_overwrite;  // overwrite normal file
          delete_ufs |=
              CreateFlag::ContainsOverwrite(request->createflag()) &&
              CreateFlag::ContainsAccAppendable(request->createflag()) &&
              FLAGS_ufs_delete_for_overwrite_appendable_file;  // overwrite
                                                               // appendable
                                                               // file
          if (delete_ufs) {
            Status s = ufs->DeleteFile(ufs_path);
            VLOG_OR_IF(8, FLAGS_log_ufs_sync_detail)
                << "Create overwrite path " << ufs_path << " result "
                << s.ToString();
            return s;
          }
          RPC_SW_CTX_LOG(rpc_sw_ctx, "after DeleteFile");

          return Status::OK();
        };

    if (request->createparent()) {
      s = ufs->CreateMissingParentIfNecessary(ufs_path);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after CreateMissingParentIfNecessary");
      if (!s.IsOK()) {
        LOG(INFO) << "Ufs create missing parent failed: " << ufs_path
                  << ", error: " << s.ToString();
        break;
      }
    }

    s = ufs->CheckParentReady(ufs_path);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after CheckParentReady");

    if (!s.IsOK()) {
      LOG(INFO) << "Check parent existing failed: " << ufs_path;
      break;
    }

    sw_inner.NextStep(metrics_.acc_create_local_time_);
    {
      s = CreateMissingParentInSync(f_ctx);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after CreateMissingParentInSync");
      if (!s.IsOK()) {
        break;
      }
    }
    {
      SynchronizedRpcClosure closure;
      AsyncCreateFile(inner_path,
                      p,
                      client_location,
                      ugi,
                      rpc_info,
                      client_machine,
                      request,
                      response,
                      ctx,
                      &closure,
                      false,
                      ProcessUfsInfo);
      closure.Await();
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after call AsyncCreateFile sync");
      s = closure.status();
      if (!s.IsOK()) {
        LOG(INFO) << "Inner ns create failed";
        break;
      }
    }

    ufs_env_->upload_monitor()->AddTask(response->fs().fileid(), key, "");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "after upload_monitor AddTask");

  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to create file. ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }

  RPC_SW_CTX_LOG(rpc_sw_ctx, "return CreateUfsFile");
  return s;
}

void NameSpace::AppendUfsFile(const std::string& ufs_path,
                              const std::string& inner_path,
                              const std::string& client_machine,
                              const LogRpcInfo& rpc_info,
                              const UserGroupInfo& ugi,
                              const AppendRequestProto* request,
                              AppendResponseProto* response,
                              const std::shared_ptr<Ufs>& ufs,
                              SyncCallback cb) {
  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> f_ctx;
  vshared_lock ha_barrier;
  Status s;
  do {
    PermissionStatus p;
    s = PrepareSyncUfsFile(ufs_path,
                           inner_path,
                           p,
                           ugi,
                           ufs,
                           true,
                           &f_ctx,
                           &locked_path,
                           &ha_barrier);
    if (!s.IsOK()) {
      break;
    }

    const INode& inode = f_ctx->iip.Inode();
    if (!inode.has_ufs_file_info()) {
      LOG(INFO) << "Failed open file for append " << inode.ShortDebugString();
      s = Status(JavaExceptions::Exception::kUnsupportedOperationException,
                 "File is not appendable " + ufs_path);
      break;
    }
    if (inode.ufs_file_info().create_type() !=
        UfsFileCreateType::kUfsFileCreateTypeAppend) {
      LOG(INFO) << "Failed open file for append " << inode.ShortDebugString();
      s = Status(JavaExceptions::Exception::kUnsupportedOperationException,
                 "File is not appendable " + ufs_path);
      break;
    }

    std::string key;
    CHECK(ufs->GetUfsIdentifier(UfsIdentifierInfo(ufs_path), &key).IsOK());
    if (inode.ufs_file_info().key() != key) {
      LOG(INFO) << "Failed open file for append, file has moved. New key "
                << key << " " << inode.ShortDebugString();
      s = Status(JavaExceptions::Exception::kUnsupportedOperationException,
                 "File is not appendable " + ufs_path);
      break;
    }

    {
      SynchronizedRpcClosure closure;
      AsyncAppend(inner_path,
                  client_machine,
                  rpc_info,
                  ugi,
                  request,
                  response,
                  &closure,
                  false);
      closure.Await();
      s = closure.status();
      if (!s.IsOK()) {
        LOG(INFO) << "Inner ns append failed";
        break;
      }
    }

    ufs_env_->upload_monitor()->AddTask(inode.id(), key, "");
  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to create file. ufs_path: " << ufs_path
              << ", inner_path: " << inner_path << ", error: " << s.ToString();
  }
  cb(s);
}

void NameSpace::SetBlockAccInfo(const INode* inode,
                                BlockInfoProto* bip,
                                bool is_last) {
  CHECK(IsAccMode());
  CheckAccINode(*inode);
  CheckAccBlock(*bip);

  uint64_t pufs_offset = 0;
  if (inode->blocks_size() > 0) {
    for (int i = 0; i < inode->blocks_size() - 1; i++) {
      pufs_offset += inode->blocks(i).numbytes();
    }
  }
  bip->set_pufs_offset(pufs_offset);

  // Append object
  if (inode->ufs_file_info().create_type() ==
      UfsFileCreateType::kUfsFileCreateTypeAppend) {
    bip->set_key_block(true);
    bip->set_upload_type(cloudfs::datanode::APPEND);
    bip->set_pufs_name(inode->ufs_file_info().key());
  }

  // Normal object
  if (inode->ufs_file_info().create_type() ==
      UfsFileCreateType::kUfsFileCreateTypeNormal) {
    if (inode->blocks_size() > 1) {
      const auto& bundled_bp = inode->blocks(inode->blocks_size() - 2);
      BlockInfoProto bundled_bip;
      CHECK(meta_storage_->GetBlockInfo(bundled_bp.blockid(), &bundled_bip))
          << "Failed to get bundled bip " << bundled_bp.ShortDebugString();
      UpdateBipInfoForBundleUpload(*inode, bip, &bundled_bip, is_last);
    } else {
      UpdateBipInfoForBundleUpload(*inode, bip, nullptr, is_last);
    }
  }
}

Status NameSpace::PersistUfsFileCheckFileIdAndLock(
    uint64_t inode_id,
    const std::shared_ptr<Ufs>& ufs,
    std::shared_ptr<FileSyncContext>& ctx,
    std::unique_ptr<LockedPath>& locked_path,
    vshared_lock& ha_barrier,
    StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  int retry = 0;
  while (true) {
    INode node;
    std::string inner_path = BuildFullPath(inode_id, &node);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after BuildFullPath");
    if (inner_path.empty()) {
      return Status(JavaExceptions::Exception::kFileNotFoundException,
                    Code::kFileNotFound,
                    "INode not found: " + std::to_string(inode_id) + ". " +
                        "The file may have been deleted, please check if your "
                        "application "
                        "has concurrency issues.");
    }

    std::string ufs_path = InnerPathToUfsPath(inner_path);

    std::unique_ptr<LockedPath> new_lp;
    std::shared_ptr<FileSyncContext> new_ctx;
    vshared_lock new_barrier;
    auto s = PrepareSyncUfsFile(ufs_path,
                                inner_path,
                                PermissionStatus(),
                                UserGroupInfo(),
                                ufs,
                                /*sync_on_ancestor_not_dir=*/true,
                                &new_ctx,
                                &new_lp,
                                &new_barrier,
                                rpc_sw_ctx,
                                /*need_log=*/false);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after PrepareSyncUfsFile");

    if (!s.IsOK()) {
      LOG(INFO) << "Failed to prepare sync on new path: " << inner_path
                << ", new_ufs: " << ufs_path << ", error: " << s.ToString();
      return s;
    }

    // The node existed after all locks acquired
    if (new_ctx->IsNodeValid()) {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "PersistFile ctx ufs: " << new_ctx->ufs_path
          << ", inner: " << new_ctx->inner_path << ", inode id: " << inode_id;

      ctx = new_ctx;
      locked_path = std::move(new_lp);
      ha_barrier = std::move(new_barrier);
      break;
    }

    // release lock
    new_lp.reset();

    // retry on error
    ++retry;
    if (retry >= 10) {
      return Status(JavaExceptions::Exception::kFileNotFoundException,
                    Code::kFileNotFound,
                    "INode not found: " + std::to_string(inode_id) + ". " +
                        "The file may have been deleted, please check if your "
                        "application "
                        "has concurrency issues.");
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry");
    std::this_thread::sleep_for(
        std::chrono::milliseconds(FLAGS_dir_lock_retry_sleep_time_ms));
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after retry sleep");
  }

  RPC_SW_CTX_LOG(rpc_sw_ctx, "return OK");
  CHECK_NOTNULL(ctx);
  return Status::OK();
}

Status NameSpace::PersistUfsFileUpdateUploadInfo(const std::string& inner_path,
                                                 const std::string& ufs_path,
                                                 Ufs* ufs,
                                                 INode* inode) {
  CHECK(ufs);
  CHECK(inode);

  Status s;
  do {
    INode old_inode = *inode;
    auto ufs_file_info = inode->mutable_ufs_file_info();
    // Create new multipart upload
    s = ufs->Create(ufs_path, UfsCreateOption(false, true), ufs_file_info);
    if (!s.IsOK()) {
      LOG(INFO) << "Create MPU failed " << ufs_path << " status "
                << s.ToString();
      break;
    }

    // Update ufs key
    ufs->GetUfsIdentifier(UfsIdentifierInfo(ufs_path),
                          ufs_file_info->mutable_key());

    // Update INode
    int64_t txid =
        edit_log_sender_->LogAccSyncUpdateINode(inner_path, *inode, old_inode);
    CHECK_NE(txid, kInvalidTxId);
    SynchronizedClosure done;
    INodeInPath iip;
    iip.MutableInodeUnsafe() = *inode;
    iip.CollectSnapshotInfo(*inode);
    iip.CollectAttrs(*inode);
    // CHECK(!iip.NeedBackupForDeletion());
    // CHECK(!iip.NeedBackupForModification());
    iip.FinalizeAttrs();
    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);
    meta_storage_->OrderedUpdateINode(
        &iip.MutableInode(), &old_inode, inode_snaplog, txid, &done);
    done.Await();
    s = done.status();
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to update inode for file move. path: " << ufs_path
                << ", error: " << s.ToString();
      break;
    }

    ufs_env_->upload_monitor()->UpdateTask(
        inode->id(), ufs_file_info->key(), ufs_file_info->upload_id());

    for (size_t i = 0; i < inode->blocks_size(); i++) {
      auto& b = inode->blocks(i);
      BlockInfoProto bip;
      block_manager_->GetAccBlockPersistInfoOrRetryUpload(
          b.blockid(),
          ufs,
          *ufs_file_info,
          inode->status() == INode_Status_kFileComplete &&
              i == inode->blocks_size() - 1,
          &bip);
    }

  } while (0);
  return s;
}

Status NameSpace::IsUfsFileAllowConcat(const std::string& inner_path,
                                       const INode& inode) {
  if (!inode.has_ufs_file_info()) {
    // not ufs file
    return {};
  }

  switch (inode.ufs_file_info().create_type()) {
    case UfsFileCreateType::kUfsFileCreateTypeNormal:
      // allow
      break;
    case UfsFileCreateType::kUfsFileCreateTypeAppend:
      return Status(JavaExceptions::Exception::kIOException,
                    "File is append mode, not allow concat. path=" +
                        inner_path + " inode=" + inode.ShortDebugString());
    case UfsFileCreateType::kUfsFileCreateTypeInvalid:
      return Status(JavaExceptions::Exception::kIOException,
                    "File is read cache, not allow concat. path=" + inner_path +
                        " inode=" + inode.ShortDebugString());
    default:
      return Status(JavaExceptions::Exception::kIOException,
                    "File create type UNKNOWN, not allow concat. path=" +
                        inner_path + " inode=" + inode.ShortDebugString());
  }

  switch (inode.ufs_file_info().file_state()) {
    case UfsFileState::kUfsFileStateLocal:
    case UfsFileState::kUfsFileStateToBePersisted: {
      // allow
      break;
    }
    case UfsFileState::kUfsFileStatePersisted:
      return Status(JavaExceptions::Exception::kIOException,
                    "File is persisted to UFS, not allow concat. path=" +
                        inner_path + " inode=" + inode.ShortDebugString());
    default:
      return Status(JavaExceptions::Exception::kIOException,
                    "File upload state UNKNOWN, not allow concat. path=" +
                        inner_path + " inode=" + inode.ShortDebugString());
  }

  return {};
}

Status NameSpace::CheckAllBlockPersisted(const INode& inode,
                                         std::map<int, std::string>* parts,
                                         bool* allBlocksUploaded,
                                         Ufs* ufs) {
  {
    for (size_t i = 0; i < inode.blocks_size(); i++) {
      auto& b = inode.blocks(i);
      BlockInfoProto bip;
      bool success = block_manager_->GetAccBlockPersistInfoOrRetryUpload(
          b.blockid(),
          ufs,
          inode.ufs_file_info(),
          inode.status() == INode_Status_kFileComplete &&
              i == inode.blocks_size() - 1,
          &bip);
      if (!success) {
        VLOG_OR_IF(10, FLAGS_log_ufs_sync_detail)
            << "Block " << b.blockid() << " is not persisted";
        *allBlocksUploaded = false;
        continue;
      }
      if (allBlocksUploaded && bip.key_block()) {
        (*parts)[bip.part_num()] = bip.etag();
      }
    }
  }

  return Status::OK();
}

Status NameSpace::TriggerUploadUfsFile(const std::shared_ptr<Ufs>& ufs,
                                       uint64_t inode_id,
                                       StopWatchContext* rpc_sw_ctx) {
  MFC(metrics_.trigger_file_upload_cnt_)->Inc();

  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "first TriggerUploadUfsFileInternal");
  Status s = TriggerUploadUfsFileInternal(ufs, inode_id, rpc_sw_ctx);
  if (s.code() == Code::kIsRetry) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail) << "TriggerUploadUfsFile"
                                                << " immediately retry";

    // retry once for recreate upload id case
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry TriggerUploadUfsFileInternal");
    s = TriggerUploadUfsFileInternal(ufs, inode_id, rpc_sw_ctx);
  }
  return s;
};

Status NameSpace::TriggerUploadUfsFileInternal(const std::shared_ptr<Ufs>& ufs,
                                               uint64_t inode_id,
                                               StopWatchContext* rpc_sw_ctx) {
  if (!FLAGS_enable_write_back) {
    VLOG(10) << "Write back is disabled";
    LoggerMetrics::Warn();
    return Status(Code::kError, "Write back is disabled");
  }
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> f_ctx;
  vshared_lock ha_barrier;

  RPC_SW_CTX_LOG(rpc_sw_ctx, "before PersistUfsFileCheckFileIdAndLock");
  auto s = PersistUfsFileCheckFileIdAndLock(
      inode_id, ufs, f_ctx, locked_path, ha_barrier, rpc_sw_ctx);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after PersistUfsFileCheckFileIdAndLock");
  if (s.code() == Code::kFileNotFound) {
    // abort in other function
  }

  if (!s.IsOK()) {
    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "PersistUfsFileCheckFileIdAndLock status=" << s.ToString();

    return s;
  }

  CHECK_NOTNULL(f_ctx.get());

  bool force_upload = false;
  {
    for (const auto& inode : f_ctx->ancestors) {
      if (XAttrs::HasXAttr(inode, kForceUploadXAttr)) {
        force_upload = true;
        break;
      }
    }
  }
  UploadPolicy policy;

  auto& inode = f_ctx->iip.Inode();
  if (!force_upload) {
    if (ufs_upload_monitor()->IsNoUpload(f_ctx->inner_path, &inode, &policy)) {
      ufs_uploading_mgr()->remove_upload_ongoing_inode(inode.id());
      // never upload
      return Status(Code::kFileStatusError, "File not allowed to upload");
    }
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after check force_upload");

  // action
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadUfsFile started. inode_id: " << inode_id;

  RPC_SW_CTX_LOG(rpc_sw_ctx, "before TriggerUploadObject");
  s = UfsUploader::Create(this, ufs.get())->TriggerUploadObject(f_ctx.get());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "after TriggerUploadObject");

  if (!s.IsOK() && f_ctx) {
    if (s.HasException()) {
      LOG(INFO) << "Failed to TriggerUploadUfsFile. ufs_path: "
                << f_ctx->ufs_path << ", inner_path: " << f_ctx->inner_path
                << ", error: " << s.ToString();
    } else {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "Failed to TriggerUploadUfsFile. ufs_path: " << f_ctx->ufs_path
          << ", inner_path: " << f_ctx->inner_path
          << ", error: " << s.ToString();
    }
  }
  return s;
}

// ufs_key and upload_id is for abort only
// old_path & new_path are ufs_path
Status NameSpace::PersistUfsFile(const std::shared_ptr<Ufs>& ufs,
                                 uint64_t inode_id,
                                 const std::string& ufs_key,
                                 const std::string& upload_id,
                                 bool check_upload_id,
                                 bool trigger_block_upload,
                                 bool* file_completed,
                                 StopWatchContext* rpc_sw_ctx) {
  if (!FLAGS_enable_write_back) {
    // LOG(WARNING) << "Write back is disabled";
    // LoggerMetrics::Warn();
    return Status(Code::kError, "Write back is disabled");
  }
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  if (FLAGS_nn_drive_upload && trigger_block_upload) {
    VLOG_OR_IF(12, FLAGS_log_ufs_persist_detail)
        << "Try TriggerUploadUfsFile before PersistUfsFile";

    RPC_SW_CTX_LOG(rpc_sw_ctx,
                   "TriggerUploadUfsFile before PersistUfsFileInternal");
    this->TriggerUploadUfsFile(ufs, inode_id, rpc_sw_ctx);
  }

  auto s = PersistUfsFileInternal(ufs,
                                  inode_id,
                                  ufs_key,
                                  upload_id,
                                  check_upload_id,
                                  file_completed,
                                  rpc_sw_ctx);

  if (FLAGS_nn_drive_upload) {
    if (s.code() == Code::kUfsUploadNotReady) {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "Try TriggerUploadUfsFile when PersistUfsFile failed";

      RPC_SW_CTX_LOG(rpc_sw_ctx,
                     "TriggerUploadUfsFile after PersistUfsFileInternal");
      this->TriggerUploadUfsFile(ufs, inode_id, rpc_sw_ctx);
    }
  }

  return s;
}

Status NameSpace::PersistUfsFileInternal(const std::shared_ptr<Ufs>& ufs,
                                         uint64_t inode_id,
                                         const std::string& ufs_key,
                                         const std::string& upload_id,
                                         bool check_upload_id,
                                         bool* file_completed,
                                         StopWatchContext* rpc_sw_ctx) {
  std::unique_ptr<LockedPath> locked_path;
  std::shared_ptr<FileSyncContext> f_ctx;
  vshared_lock ha_barrier;
  RPC_SW_CTX_LOG_FUNCNAME(rpc_sw_ctx);

  auto s = PersistUfsFileCheckFileIdAndLock(
      inode_id, ufs, f_ctx, locked_path, ha_barrier, rpc_sw_ctx);
  if (s.code() == Code::kFileNotFound) {
    // INode has been deleted. Try to abort MPU.
    // Append object does not have upload id.
    UfsUploader::Create(this, ufs.get())->AbortUpload(upload_id, ufs_key);
  }

  // ufs_key and upload_id is for abort only
  // DO NOT use them after this line

  if (!s.IsOK()) {
    return s;
  }

  CHECK_NOTNULL(f_ctx);

  f_ctx->check_upload_id = check_upload_id;

  const auto& inode = f_ctx->iip.Inode();
  if (inode.status() == INode_Status_kFileComplete) {
    *file_completed = true;
  }

  UploadPolicy policy;
  bool force_upload = false;
  {
    for (const auto& inode : f_ctx->ancestors) {
      if (XAttrs::HasXAttr(inode, kForceUploadXAttr)) {
        force_upload = true;
        break;
      }
    }
  }

  if (!force_upload) {
    if (ufs_upload_monitor()->IsNoUpload(f_ctx->inner_path, &inode, &policy)) {
      ufs_uploading_mgr()->remove_upload_ongoing_inode(inode.id());
      // never upload
      return Status(Code::kFileStatusError, "File not allowed to upload");
    }
  }

  if (inode.status() == INode_Status_kFileComplete) {
    int64_t time_now_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
    if (time_now_ms - inode.mtime() > FLAGS_upload_file_to_ufs_slow_time_ms) {
      MFC(metrics_.upload_file_to_ufs_slow_unfinished_cnt_)->Inc();
      VLOG(5) << "upload too slow, "
              << " path=" << f_ctx->inner_path << " inode_id=" << inode_id
              << " time_gap_ms=" << time_now_ms - inode.mtime();
    }
  }

  VLOG_OR_IF(9, FLAGS_log_ufs_persist_detail)
      << "PersistUfsFile started. inode_id: " << inode_id
      << " upload_id: " << upload_id << " ufs_path : " << f_ctx->ufs_path
      << ", inner_path: " << f_ctx->inner_path;

  s = UfsUploader::Create(this, ufs.get())->PersistObject(f_ctx.get());

  if (!s.IsOK() && f_ctx) {
    if (s.HasException()) {
      LOG(INFO) << "Failed to persist file. ufs_path: " << f_ctx->ufs_path
                << ", inner_path: " << f_ctx->inner_path
                << ", error: " << s.ToString();
    } else {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "Failed to persist file. ufs_path: " << f_ctx->ufs_path
          << ", inner_path: " << f_ctx->inner_path
          << ", error: " << s.ToString();
    }
  }

  // trigger evictable
  if (!s.IsOK()) {
    // TODO(xiong): Implement It
    //    TriggerWriteCacheEvict()
  }

  return s;
}

void NameSpace::AccSyncListingBatchAddWriteLock(
    RpcClosure* done,
    const AccSyncListingBatchAddOpBody& op,
    std::vector<cnetpp::base::StringPiece>* path_components,
    std::vector<std::string>* subfile_paths) {
  std::vector<::cnetpp::base::StringPiece> lock_components;
  auto&& path = op.dir_path();
  CHECK(SplitPath(path, path_components));
  CHECK(GetAllAncestorPaths(path, &lock_components));

  StopWatch sw;
  sw.Start();
  {
    auto locks = ReadLockTree(&lock_components);
    subfile_paths->reserve(op.files_to_add_size());
    for (size_t i = 0; i < op.files_to_add_size(); ++i) {
      auto&& f = op.files_to_add(i);
      auto&& name = f.inode().name();
      CHECK(!name.empty());

      subfile_paths->emplace_back(JoinTwoPath(path, name));
      cnetpp::base::StringPiece lock_comp(subfile_paths->back());
      const int lock_depth = lock_components.size();
      locks->at(0)->emplace(std::make_pair(
          lock_comp, lock_manager_->WriteLock(lock_comp, lock_depth)));
    }

    done->set_locks(std::move(locks));
  }
  auto time_cost_us = sw.NextStepTime();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect AccSyncListingBatchAddWriteLock slow"
            << ", path: " << path << ", lock cost us: " << time_cost_us;
  }
}

void NameSpace::AccSyncListingBatchUpdateWriteLock(
    RpcClosure* done,
    const AccSyncListingBatchUpdateOpBody& op,
    std::vector<cnetpp::base::StringPiece>* path_components,
    std::vector<std::string>* subfile_paths) {
  std::vector<::cnetpp::base::StringPiece> lock_components;
  auto&& path = op.dir_path();
  CHECK(SplitPath(path, path_components));
  CHECK(GetAllAncestorPaths(path, &lock_components));

  StopWatch sw;
  sw.Start();
  {
    auto locks = ReadLockTree(&lock_components);
    subfile_paths->reserve(op.files_to_update_size());
    for (size_t i = 0; i < op.files_to_update_size(); ++i) {
      auto&& inode = op.files_to_update(i);
      auto&& name = inode.name();
      CHECK(!name.empty());

      subfile_paths->emplace_back(JoinTwoPath(path, name));
      cnetpp::base::StringPiece lock_comp(subfile_paths->back());
      const int lock_depth = lock_components.size();
      locks->at(0)->emplace(std::make_pair(
          lock_comp, lock_manager_->WriteLock(lock_comp, lock_depth)));
    }

    done->set_locks(std::move(locks));
  }
  auto time_cost_us = sw.NextStepTime();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect AccSyncListingBatchUpdateWriteLock slow"
            << ", path: " << path << ", lock cost us: " << time_cost_us;
  }
}

}  // namespace dancenn
