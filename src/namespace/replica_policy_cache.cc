#include "namespace/replica_policy_cache.h"

#include <mutex>
#include <shared_mutex>

#include "base/constants.h"

DECLARE_uint32(replica_policy_default_id);

namespace dancenn {

class ReplicaPolicyCacheNode {
 public:
   ReplicaPolicyCacheNode(const std::string& path)
     : parent_(nullptr), path_(path), policy_id_(0), policy_dc_("") {}
   ~ReplicaPolicyCacheNode() {}

   bool ParentOf(const std::string& path) {
     return path.find(path_) == 0;
   }
 public:
   ReplicaPolicyCacheNode*               parent_;
   std::string                           path_;
   std::vector<ReplicaPolicyCacheNode*>  children_;
   int                            policy_id_;
   std::string                    policy_dc_;
};

ReplicaPolicyCache::ReplicaPolicyCache() {
  root_ = new ReplicaPolicyCacheNode("");
  root_->policy_id_ = FLAGS_replica_policy_default_id;
}

ReplicaPolicyCache::~ReplicaPolicyCache() {
  ReleaseNode(root_);
}

void ReplicaPolicyCache::GetParentOrSelf(const std::string& path,
    int* policy_id, std::string* policy_dc) {
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  auto node = GetParentOrSelf(root_, NormalizePath(path));
  *policy_id = node->policy_id_;
  *policy_dc = node->policy_dc_;
}

void ReplicaPolicyCache::UpdateChildOrSelf(const std::string& path,
    int policy_id, const std::string& policy_dc) {
  auto n_path = NormalizePath(path);

  if (policy_id == kNonePolicy) {
    DeleteNode(n_path);
    return;
  }

  std::unique_lock<ReadWriteLock> guard(rwlock_);
  auto parent = GetParentOrSelf(root_, n_path);
  if (parent->path_ == n_path) {
    parent->policy_id_ = policy_id;
    parent->policy_dc_ = policy_dc;
    return;
  }

  auto n = new ReplicaPolicyCacheNode(n_path);
  n->parent_ = parent;
  n->policy_id_ = policy_id;
  n->policy_dc_ = policy_dc;

  for (auto itr = parent->children_.begin(); itr != parent->children_.end();) {
    if (n->ParentOf((*itr)->path_)) {
      (*itr)->parent_ = n;
      n->children_.emplace_back(*itr);
      itr = parent->children_.erase(itr);
    } else {
      ++itr;
    }
  }
  parent->children_.emplace_back(n);
}

void ReplicaPolicyCache::Filter(const ReplicaPolicyCacheNode* root, const FilterFn& fn, std::vector<std::string>* v) {
  auto path = NoEndSlashPath(root->path_);
  if (fn(path, root->policy_id_, root->policy_dc_)) {
    v->emplace_back(path);
  }
  for (size_t i = 0; i < root->children_.size(); i++) {
    Filter(root->children_[i], fn, v);
  }
}

std::vector<std::string> ReplicaPolicyCache::Filter(const FilterFn& fn) {
  std::vector<std::string> v;
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  for (size_t i = 0; i < root_->children_.size(); i++) {
    Filter(root_->children_[i], fn, &v);
  }
  return v;
}

std::string ReplicaPolicyCache::NormalizePath(const std::string& path) {
  if (path.size() == 0 || path[path.size() - 1] == '/') return path;
  return path + "/";
}

std::string ReplicaPolicyCache::NoEndSlashPath(const std::string& path) {
  if (path.size() <= 1) return path;
  return path.substr(0, path.size() - 1);
}

void ReplicaPolicyCache::ReleaseNode(ReplicaPolicyCacheNode* root) {
  for (size_t i = 0; i < root->children_.size(); i++) {
    ReleaseNode(root->children_[i]);
  }
  delete root;
}

void ReplicaPolicyCache::DeleteNode(const std::string& path) {
  auto n_path = NormalizePath(path);
  std::unique_lock<ReadWriteLock> guard(rwlock_);
  ReplicaPolicyCacheNode* node = GetParentOrSelf(root_, n_path);
  if (node->path_ == n_path) {
    auto parent = node->parent_;
    for (auto itr = parent->children_.begin();
        itr != parent->children_.end();) {
      if ((*itr)->path_ == n_path) {
        itr = parent->children_.erase(itr);
      } else {
        ++itr;
      }
    }
    for (auto itr = node->children_.begin();
        itr != node->children_.end(); ++itr) {
      (*itr)->parent_ = parent;
      parent->children_.emplace_back(*itr);
    }
    delete node;
  }
}

ReplicaPolicyCacheNode* ReplicaPolicyCache::GetParentOrSelf(ReplicaPolicyCacheNode* root,
    const std::string& path) {
  if (root->path_ == path) return root;
  for (size_t i = 0; i < root->children_.size(); i++) {
    if (root->children_[i]->ParentOf(path)) {
      return GetParentOrSelf(root->children_[i], path);
    }
  }
  return root;
}

}
