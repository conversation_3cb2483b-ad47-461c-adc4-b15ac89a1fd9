#include "namespace/lifecycle_scanner.h"

#include <absl/strings/str_format.h>

#include "namespace/lifecycle_policy_util.h"
#include "namespace/meta_storage.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/namespace.h"
#include "namespace/namespace_scrub.h"
#include "namespace/namespace_scrub_lifecycle_scan.h"

DECLARE_bool(lifecycle_scanner_force_start_next_group);
DECLARE_bool(lifecycle_scanner_force_skip_next_group);
DECLARE_uint64(lifecycle_scanner_start_next_group_period_ms);
DECLARE_uint64(lifecycle_scanner_filter_depred_report_batch_size);
DECLARE_uint32(blkid_cmd_max_num_blocks);
DECLARE_string(default_storage_class);

namespace dancenn {

LifecycleScanner::ScanTask::ScanTask(std::shared_ptr<LifecycleScanner> scanner,
                                     NameSpace* ns,
                                     std::shared_ptr<DatanodeManager> dn_mgr,
                                     std::shared_ptr<MetaStorage> ms,
                                     std::shared_ptr<NameSpaceScrubRunner> sr)
  : lifecycle_scanner_(scanner), ns_(ns), dn_mgr_(dn_mgr), ms_(ms),
    scrub_runner_(sr) {
  InitMetrics();
}

LifecycleScanner::ScanTask::~ScanTask() {
  FiniMetrics();
}

bool LifecycleScanner::ScanTask::operator()(void* arg) {
  step_ = ScanStep::DONE;
  while (!stop_) {
    VLOG(10) << absl::StrFormat("LifecycleScanner wakeup...");

    // LifecycleScanner handle work in rounds. In each round:
    //  1. Filter deprecated data.
    //  2. Calculate subroots that need to be scrubbed one by one later.
    //  3. For each subroot, submit a scrub progress on it, apply transition
    //     and expiration, and generate commands.
    //  4. Refresh all statistics by traversing all reports, once all commands
    //     are consumed by DN heartbeat.
    //
    // Generally speaking, it will loop between each step above, except for
    // following cases:
    //  1. stopped
    //  2. notified when last scrub completed.

    switch (step_) {
      case ScanStep::FILTER_DEPRED_DATA: {
        LOG(INFO) << absl::StrFormat("Step 1: filter deprecated data");
        FilterDepredData();
        step_ = ScanStep::CALC_SCRUB_ROOTS;
        break;
      }
      case ScanStep::CALC_SCRUB_ROOTS: {
        LOG(INFO) << absl::StrFormat("Step 2: calculate scrub subroots");
        CalcScrubRoots();
        step_ = ScanStep::DO_SCRUB_ROOTS;
        break;
      }
      case ScanStep::DO_SCRUB_ROOTS: {
        LOG(INFO) << absl::StrFormat("Step 3: scrub all subroots");
        bool all_done = DoScrubRoots();
        if (!all_done) {
          break;
        }
        step_ = ScanStep::REFRESH_STAT;
        break;
      }
      case ScanStep::REFRESH_STAT: {
        LOG(INFO) << absl::StrFormat("Step 4: refresh statistics");
        RefreshStorageClassStat();
        step_ = ScanStep::DONE;
        break;
      }
      case ScanStep::DONE: {
        bool need_next_group = CheckNextGroup();
        if (!need_next_group) {
          break;
        }
        last_group_run_epoch_ = TimeUtil::GetNowEpochMs();
        LOG(INFO) << absl::StrFormat("Step 0: start next group");
        step_ = ScanStep::FILTER_DEPRED_DATA;
        break;
      }
      default: {
        LOG(FATAL) << "unreachable";
      }
    }

    if (step_ == ScanStep::DONE) {
      int sleep_sec = 5;
      VLOG(10) << absl::StrFormat(
          "LifecycleScanner will sleep %d seconds.", sleep_sec);
      std::chrono::seconds period{sleep_sec};
      std::unique_lock<std::mutex> lock(mtx_);
      cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
    }
  }

  return true;
}

void LifecycleScanner::ScanTask::Stop() {
  std::unique_lock<std::mutex> lock(mtx_);
  stop_ = true;
  cond_.notify_all();
}

void LifecycleScanner::ScanTask::InitMetrics() {
  auto mcenter = MetricsCenter::Instance();
  auto metrics = mcenter->RegisterMetrics("LifecycleScanTask");

  policy_filter_depred_time_ =
      metrics->RegisterHistogram("FilterDepredData#step=filter_policy");
  policy_scanned_cnt_ =
      metrics->RegisterCounter("NumScannedPolicy");
  policy_filtered_cnt_ =
      metrics->RegisterCounter("NumFilteredPolicy");
  stat_filter_depred_time_ =
      metrics->RegisterHistogram("FilterDepredData#step=filter_stat");
  stat_scanned_cnt_ =
      metrics->RegisterCounter("NumScannedStat");
  stat_filtered_cnt_ =
      metrics->RegisterCounter("NumFilteredStat");
  report_filter_depred_time_ =
      metrics->RegisterHistogram("FilterDepredData#step=filter_report");
  report_scanned_cnt_ =
      metrics->RegisterCounter("NumScannedReport");
  report_filtered_cnt_ =
      metrics->RegisterCounter("NumFilteredReport");
  subroot_calc_time_ =
      metrics->RegisterHistogram("CalcScrubRoots");
  subroot_calc_cnt_ =
      metrics->RegisterCounter("NumCalcedRoots");
  scrub_time_ =
      metrics->RegisterHistogram("ScrubTime");
}

void LifecycleScanner::ScanTask::FiniMetrics() {
  // nothing
}

void LifecycleScanner::ScanTask::FilterDepredData() {
  StopWatch sw(policy_filter_depred_time_);
  sw.Start();
  FilterDepredLifecyclePolicy();
  sw.NextStep(stat_filter_depred_time_);
  FilterDepredStorageClassStat();
  sw.NextStep(report_filter_depred_time_);
  FilterDepredStorageClassReport();
}

void LifecycleScanner::ScanTask::FilterDepredLifecyclePolicy() {
  int64_t num_scan = MFC(policy_scanned_cnt_)->GetValue();
  int64_t num_filter = MFC(policy_filtered_cnt_)->GetValue();

  auto lifecycle_iter_holder = ms_->GetIterator(nullptr, kLifecyclePolicyCFIndex);
  auto filter_cb = [&] (const INodeID id,
                        const uint64_t ts,
                        const LifecyclePolicy& policy) -> bool {
    MFC(policy_scanned_cnt_)->Inc();
    SynchronizedRpcClosure done;
    ns_->AsyncUnsetDepredLifecyclePolicy(id, &done);
    done.Await();
    if (done.status().IsOK()) {
      MFC(policy_filtered_cnt_)->Inc();
    }
    return true;
  };
  ms_->ScanLifecyclePolicy(filter_cb, lifecycle_iter_holder->iter());

  num_scan = MFC(policy_scanned_cnt_)->GetValue() - num_scan;
  num_filter = MFC(policy_filtered_cnt_)->GetValue() - num_filter;
  VLOG(10) << absl::StrFormat(
      "LifecycleScanner scanned %ld policies, filtered %ld",
      num_scan, num_filter);
}

void LifecycleScanner::ScanTask::FilterDepredStorageClassStat() {
  int64_t num_scan = MFC(stat_scanned_cnt_)->GetValue();
  int64_t num_filter = MFC(stat_filtered_cnt_)->GetValue();

  auto scs_iter_holder = ms_->GetIterator(nullptr, kStorageClassStatCFIndex);
  auto filter_cb = [&] (const INodeID inode_id,
                        const StorageClassStatProto& proto) {
    MFC(stat_scanned_cnt_)->Inc();
    INode inode;
    auto st = ms_->GetINode(inode_id, &inode);
    if (st != StatusCode::kOK) {
      // 1. inode deleted
      MFC(stat_filtered_cnt_)->Inc();
      ms_->DeleteStorageClassStatAsync(inode_id, nullptr);
      return true;
    }

    LifecyclePolicyProto plcy;
    int dist = GetEffectiveLifecyclePolicy(ms_.get(), inode_id, nullptr/* filter */, &plcy);
    if (dist < 0) {
      // 2. no valid lifecycle policy inherited
      MFC(stat_filtered_cnt_)->Inc();
      ms_->DeleteStorageClassStatAsync(inode_id, nullptr);
      return true;
    }

    return true;
  };
  ms_->ScanStorageClassStat(filter_cb, scs_iter_holder->iter());

  num_scan = MFC(stat_scanned_cnt_)->GetValue() - num_scan;
  num_filter = MFC(stat_filtered_cnt_)->GetValue() - num_filter;
  LOG(INFO) << absl::StrFormat(
      "LifecycleScanner scanned %ld stats, filtered %ld",
      num_scan, num_filter);
}

void LifecycleScanner::ScanTask::FilterDepredStorageClassReport() {
  if (all_depred_report_filtered_) {
    LOG(INFO) << "all deprecated reports are cleanup, skip the filter job";
    return;
  }

  uint64_t num_scan = 0;
  uint64_t num_filter = 0;
  uint64_t batch_size = FLAGS_lifecycle_scanner_filter_depred_report_batch_size;
  auto scr_iter_holder = ms_->GetIterator(nullptr, kStorageClassReportCFIndex);
  auto filter_cb = [&] (const BlockID block_id,
                        const std::string& dn_uuid,
                        const StorageClassReportProto& proto) {
    num_scan++;

    if (dn_uuid.length() > 0) {
      return true;
    }

    ms_->DeleteStorageClassReportAsync(block_id, dn_uuid, nullptr);
    num_filter++;
    if (num_filter >= batch_size) {
      return false;
    }
    return true;
  };
  ms_->ScanStorageClassReport(filter_cb, scr_iter_holder->iter());

  if (num_filter == 0) {
    // all deprecated kv filtered
    all_depred_report_filtered_ = true;
  }

  MFC(report_scanned_cnt_)->Inc(num_scan);
  MFC(report_filtered_cnt_)->Inc(num_filter);
  LOG(INFO) << absl::StrFormat(
      "LifecycleScanner scanned %ld reports, filtered %ld",
      num_scan, num_filter);
}

void LifecycleScanner::ScanTask::CalcScrubRoots() {
  StopWatch sw(subroot_calc_time_);
  sw.Start();

  // reset subroots of last round
  scrub_roots_.clear();
  auto snapshot_holder = ms_->GetSnapshot();

  // iterator holder scope within snapshot holder scope
  {
    auto snap = snapshot_holder->snapshot();
    auto inode_iter_holder = ms_->GetIterator(snap, kINodeDefaultCFIndex);
    auto lifecycle_iter_holder = ms_->GetIterator(snap, kLifecyclePolicyCFIndex);

    auto calc_cb = [&] (const INodeID id,
                        const uint64_t ts,
                        const LifecyclePolicy& policy) -> bool {
      INodeID cur_id = id;
      INodeID par_id = kInvalidINodeId;
      while (true) {
        INodeID par_id = ms_->GetINodeParentId(cur_id);
        if (par_id == kInvalidINodeId) {
          // deprecated policy, leave it cleaned in next round
          break;
        }
        if (par_id == cur_id) {
          // reached root, no parent with LifecyclePolicy attached
          CHECK_EQ(cur_id, kRootINodeId);
          // no need to protect with lock, since I am the only one push/pop it.
          scrub_roots_.push_back(id);
          break;
        }

        // TODO(xuex) how do we unify current-read & snapshot-read
        auto st = ms_->GetLifecyclePolicy(par_id, nullptr, nullptr);
        if (st.IsOK()) {
          // a parent has LifecyclePolicy
          break;
        }

        // go up to check another parent
        cur_id = par_id;
      }

      return true;
    };
    ms_->ScanLifecyclePolicy(calc_cb, lifecycle_iter_holder->iter());
  }

  MFC(subroot_calc_cnt_)->Inc(scrub_roots_.size());
  LOG(INFO) << absl::StrFormat(
      "LifecycleScanner found %lu subroot to scrub",
      scrub_roots_.size());
}

bool LifecycleScanner::ScanTask::DoScrubRoots() {
  // reset statistics of last round
  lifecycle_scanner_->scrub_stat_.Clear();

  std::vector<INodeID>::iterator iter;
  for (iter = scrub_roots_.begin(); iter != scrub_roots_.end(); iter++) {
    if (stop_) {
      // stop between two scrub
      break;
    }

    INode inode;
    auto st = ms_->GetINode(*iter, &inode);
    if (st != StatusCode::kOK) {
      continue;
    }

    // start and wait scrub procedure
    VLOG(10) << absl::StrFormat(
        "LifecycleScanner start a new scrub on inode %lu", *iter);
    StopWatch sw(scrub_time_);
    sw.Start();
    NameSpaceScrub scrub(inode,
                         SCRUB_OPTYPE_LIFECYCLE_SCAN,
                         SCRUB_ACTION_FORCE_OVERWRITE,
                         ns_,
                         ms_);
    scrub.Start();
    scrub.WaitForDone();

    // process result
    auto scrubop =
        std::dynamic_pointer_cast<LifecycleScanScrubOp>(scrub.GetResult());
    CHECK_NOTNULL(scrubop);

    // accumulate commands from scrub result
    if (scrubop->HaveCommands()) {
      LOG(INFO) << absl::StrFormat(
          "LifecycleScanner completed one scrub with non-empty cmds, result: %s",
          scrubop->ToString());

      auto scrubbed_cmds = scrubop->GetCommands();
      lifecycle_scanner_->AddStorageClassCmd(scrubbed_cmds);
      scrubop->ClearCommands();
      CHECK(!scrubop->HaveCommands());
    }

    // accumulate statistics of scrub itself
    lifecycle_scanner_->scrub_stat_ += scrubop->scrub_stat();
    scrubop->scrub_stat().Clear();
  }

  return iter == scrub_roots_.end();
}

void LifecycleScanner::ScanTask::RefreshStorageClassStat() {
  // TODO(xuex)
  // wait a period after all commands consumed by DN heartbeat
  // then start refresh StorageClassStat by traversing all StorageClassReport
  // but is it better?
}

bool LifecycleScanner::ScanTask::CheckNextGroup() {
  if (FLAGS_lifecycle_scanner_force_skip_next_group) {
    return false;
  }
  if (FLAGS_lifecycle_scanner_force_start_next_group) {
    return true;
  }
  auto now = TimeUtil::GetNowEpochMs();
  if (now - last_group_run_epoch_ >
      FLAGS_lifecycle_scanner_start_next_group_period_ms) {
    return true;
  }
  return false;
}


LifecycleScanner::LifecycleScanner(NameSpace* ns,
                                   std::shared_ptr<DatanodeManager> dn_mgr,
                                   std::shared_ptr<MetaStorage> ms,
                                   std::shared_ptr<NameSpaceScrubRunner> sr)
  : ns_(ns), ms_(ms), dn_mgr_(dn_mgr), scrub_runner_(sr) {

  StorageClassProto cls;
  bool ok = StorageClassName2ID(FLAGS_default_storage_class, &cls);
  CHECK(ok);
  default_storage_class = cls;
  default_lifecycle_policy.set_defaultclass(default_storage_class);

  InitMetrics();
}

LifecycleScanner::~LifecycleScanner() {
  FiniMetrics();
}

void LifecycleScanner::Start() {
  CHECK(worker_ == nullptr);
  worker_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::shared_ptr<cnetpp::concurrency::Task>(
          new LifecycleScanner::ScanTask(shared_from_this(),
                                         ns_,
                                         dn_mgr_,
                                         ms_,
                                         scrub_runner_)),
      "LifecycleScanner");
  worker_->Start();
}

void LifecycleScanner::Stop() {
  if (worker_) {
    worker_->Stop();
    worker_.reset();
  }
}

void LifecycleScanner::InitMetrics() {
  metrics_ = MetricsCenter::Instance()->RegisterMetrics("LifecycleScanner");

  // staged commands
  for (int cls = StorageClassProto::HOT;
       cls < cloudfs::StorageClassProto_ARRAYSIZE;
       cls++) {
    std::string cls_name;
    bool valid =
        StorageClassID2Name(static_cast<StorageClassProto>(cls), &cls_name);
    CHECK(valid);
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("NumStagedCmd#cls=%s", cls_name),
        [this, cls] () -> double { return num_cmds_staged_[cls]; }));
  }

  // scrub statistics
  gauge_.push_back(metrics_->RegisterGauge(
      "ScrubStatNumFile",
      [this] () -> double { return scrub_stat_.num_file; }));
  gauge_.push_back(metrics_->RegisterGauge(
      "ScrubStatNumDir",
      [this] () -> double { return scrub_stat_.num_dir; }));
  gauge_.push_back(metrics_->RegisterGauge(
      "ScrubStatNumStatPersisted",
      [this] () -> double { return scrub_stat_.num_stat_persisted; }));
  gauge_.push_back(metrics_->RegisterGauge(
      "ScrubStatNumINodeExpired",
      [this] () -> double { return scrub_stat_.num_inode_expired; }));
  gauge_.push_back(metrics_->RegisterGauge(
      "ScrubStatNumINodeTransited",
      [this] () -> double { return scrub_stat_.num_inode_transited; }));

  for (int cls = StorageClassProto::HOT;
       cls < cloudfs::StorageClassProto_ARRAYSIZE;
       cls++) {
    std::string cls_name;
    bool valid =
        StorageClassID2Name(static_cast<StorageClassProto>(cls), &cls_name);
    CHECK(valid);

    // logical block expected
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumLogicalBlockExpected#cls=%s&", cls_name),
        [this, cls] () -> double
          { return scrub_stat_.num_logical_block_expected[cls]; }));
    // logical byte expected
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumLogicalByteExpected#cls=%s", cls_name),
        [this, cls] () -> double
          { return scrub_stat_.num_logical_byte_expected[cls]; }));
    // logical block effective
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumLogicalBlock#cls=%s&", cls_name),
        [this, cls] () -> double { return scrub_stat_.num_logical_block[cls]; }));
    // logical byte effective
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumLogicalByte#cls=%s", cls_name),
        [this, cls] () -> double { return scrub_stat_.num_logical_byte[cls]; }));
    // physical replica in DN
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumPhysicalReplical#cls=%s",  // typo
                        cls_name),
        [this, cls] () -> double { return scrub_stat_.num_physical_replica[cls]; }));
    // physical byte in DN
    gauge_.push_back(metrics_->RegisterGauge(
        absl::StrFormat("ScrubStatNumPhysicalByte#cls=%s", cls_name),
        [this, cls] () -> double { return scrub_stat_.num_physical_byte[cls]; }));
  }
}

void LifecycleScanner::FiniMetrics() {
  for (const auto& gauge : gauge_) {
    metrics_->DeregisterGauge(gauge);
  }
}

void LifecycleScanner::AddStorageClassCmd(const StorageClassCmds& scrubbed_dn_cmds) {
  std::unique_lock<std::mutex> lock(mtx_);

  // merge scrub result
  for (auto& dn_cmds_pair : scrubbed_dn_cmds) {
    auto& dn_uuid = dn_cmds_pair.first;
    auto& new_dn_cmds = dn_cmds_pair.second;

    for (int cls = StorageClassProto::HOT;
         cls < cloudfs::StorageClassProto_ARRAYSIZE;
         cls++) {
      auto& new_blkids = new_dn_cmds.block_ids[cls];
      if (new_blkids.empty()) {
        continue;
      }
      auto& blkids = dn_cmds_mapping_[dn_uuid].block_ids[cls];
      blkids.insert(new_blkids.begin(), new_blkids.end());

      // debug print
      std::string cls_name;
      bool ok = StorageClassID2Name(
          static_cast<StorageClassProto>(cls), &cls_name);
      CHECK(ok);
      LOG(INFO) << absl::StrFormat(
          "LifecycleScanner prepared %lu commands for DN %s StorageClass %s",
          new_blkids.size(), dn_uuid, cls_name);
    }
  }

  RefreshCmdStat();
}

void LifecycleScanner::GetStorageClassCmd(
    const std::string& dn_uuid,
    const std::string& bpid,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::unique_lock<std::mutex> lock(mtx_);

  // dump to BlockIdCommand
  for (int cls = StorageClassProto::HOT;
       cls < cloudfs::StorageClassProto_ARRAYSIZE;
       cls++) {
    auto& blkids = dn_cmds_mapping_[dn_uuid].block_ids[cls];
    if (blkids.empty()) {
      continue;
    }

    size_t num_cmd =
        std::min(static_cast<size_t>(blkids.size()),
                 static_cast<size_t>(FLAGS_blkid_cmd_max_num_blocks));
    std::set<BlockID>::const_iterator iter = blkids.begin();
    std::advance(iter, num_cmd);
    auto blkid_cmd = ConstructBlockIdCmd(
        static_cast<cloudfs::StorageClassProto>(cls),
        bpid,
        std::set<BlockID>(blkids.begin(), iter));
    auto cmd = response->add_cmds();
    cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::BlockIdCommand);
    cmd->mutable_blkidcmd()->Swap(&blkid_cmd);
    if (bm_metrics) {
      switch (cls) {
        case StorageClassProto::HOT:
          MFC(bm_metrics->dn_cmd_cnt_block_id_lifecycle_hot_)
              ->Inc(blkid_cmd.blockids_size());
          break;
        case StorageClassProto::WARM:
          MFC(bm_metrics->dn_cmd_cnt_block_id_lifecycle_warm_)
              ->Inc(blkid_cmd.blockids_size());
          break;
        case StorageClassProto::COLD:
          MFC(bm_metrics->dn_cmd_cnt_block_id_lifecycle_cold_)
              ->Inc(blkid_cmd.blockids_size());
          break;
      }
    }

    blkids.erase(blkids.begin(), iter);

    // debug print
    std::string cls_name;
    bool ok = StorageClassID2Name(static_cast<StorageClassProto>(cls),
                                  &cls_name);
    CHECK(ok);
    LOG(INFO) << absl::StrFormat(
        "LifecycleScanner generated %lu command for DN %s, StorageClass %s",
        num_cmd, dn_uuid, cls_name);
  }

  RefreshCmdStat();
}

void LifecycleScanner::RefreshCmdStat() {
  // XXX lock held outside
  uint64_t ncmd[cloudfs::StorageClassProto_ARRAYSIZE] = { 0 };
  for (auto& dn_cmds : dn_cmds_mapping_) {
    for (int cls = StorageClassProto::HOT;
         cls < cloudfs::StorageClassProto_ARRAYSIZE;
         cls++) {
      ncmd[cls] += dn_cmds.second.block_ids[cls].size();
    }
  }
  std::copy(std::begin(ncmd), std::end(ncmd), std::begin(num_cmds_staged_));
}

}  // namespace dancenn
