//
// Copyright (c) 2019 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef NAMESPACE_SLOWS_H_
#define NAMESPACE_SLOWS_H_

#include <memory>

#include "base/actor.h"
#include "base/metrics.h"

namespace dancenn {
  namespace meta_storage {
    class WriteTask;
  }

  using WriteTaskActorGroup = ActorGroup<meta_storage::WriteTask*,
                PartitionedPicker<meta_storage::WriteTask*>>;

  class Slows {
    public:
      Slows(int n, int mailbox);
      ~Slows();

      void Start();
      void Stop();

      void Post(meta_storage::WriteTask* task);

      int NumPending() const;
      void WaitNoPending();
    private:
      std::unique_ptr<WriteTaskActorGroup> group_;

      MetricID pending_time_;
      MetricID execute_time_;
      std::shared_ptr<Gauge> pending_num_;
  };
}

#endif
