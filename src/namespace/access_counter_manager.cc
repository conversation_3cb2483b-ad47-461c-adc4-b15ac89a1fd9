// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "namespace/access_counter_manager.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <cnetpp/base/string_piece.h>

#include <shared_mutex>
#include <string>
#include <vector>
#include <chrono>

#include "base/path_util.h"
#include "namespace/namespace.h"
#include "datanode_manager/data_centers.h"

DECLARE_int64(accesscounter_flush_interval_ms);
DECLARE_double(accesscounter_learn_factor);
DECLARE_int32(accesscounter_max_depth);

namespace dancenn {

AccessCounterManager::AccessCounterManager(NameSpace* ns, DataCenters* dcs)
    : ns_(ns), dcs_(dcs) {
  LOG(INFO) << "Access counter manager initialized. number of dc: "
    << dcs_->size() << ", flush interval: "
    << FLAGS_accesscounter_flush_interval_ms << "(ms), learn factor: "
    << FLAGS_accesscounter_learn_factor << ", max depth: "
    << FLAGS_accesscounter_max_depth;
}

std::shared_ptr<AccessCounter> AccessCounterManager::GetOrCreate(
    const std::string& path, std::shared_ptr<AccessCounter> parent) {
  std::shared_lock<ReadWriteLock> read_guard(rwlock_);
  auto itr = counter_map_.find(path);
  if (itr != counter_map_.end()) {
    return itr->second;
  } else {
    read_guard.unlock();
    std::unique_lock<ReadWriteLock> write_guard(rwlock_);
    auto itr1 = counter_map_.find(path);
    if (itr1 != counter_map_.end()) {
      return itr1->second;
    }
    if (parent) {
      return counter_map_.emplace(path,
          std::make_shared<AccessCounter>(path, *parent)).first->second;
    } else {
      return counter_map_.emplace(path,
          std::make_shared<AccessCounter>(path, dcs_->size())).first->second;
    }
  }
}

std::shared_ptr<AccessCounter> AccessCounterManager::Get(
    const std::string& path) {
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  auto itr = counter_map_.find(path);
  if (itr == counter_map_.end()) {
    return nullptr;
  } else {
    return itr->second;
  }
}

void AccessCounterManager::IncreaseAccessCounter(const std::string& path,
    DataCenter dc, uint64_t step) {
  // TODO(yangjinfeng.02)
  // check HA status
  // if (ns_.isInStandbyState()) {
  //   return;
  // }
  std::vector<cnetpp::base::StringPiece> ancestors;
  GetAllAncestorPaths(path, &ancestors);
  if (ancestors.empty()) {
    LOG(ERROR) << "Empty path!";
    return;
  }

  std::shared_ptr<AccessCounter> parent;

  for (int i = 0; i < FLAGS_accesscounter_max_depth; i++) {
    if (static_cast<size_t>(i) >= ancestors.size()) {
      break;
    }

    auto counter = GetOrCreate(ancestors[i].as_string(), parent);
    counter->count_.fetch_add(step);
    counter->increment_values_[dc.id].fetch_add(step);
    uint64_t last_snapshot = counter->last_snapshot_;
    uint64_t now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    if (now - last_snapshot > FLAGS_accesscounter_flush_interval_ms &&
        counter->last_snapshot_.compare_exchange_strong(last_snapshot, now)) {
      EmitSnapshot(counter);
    }
    parent = counter;
  }
}

std::map<DataCenter, double> AccessCounterManager::GetAccessCounterValues(
    const std::string& path) {
  std::map<DataCenter, double> res;
  std::vector<cnetpp::base::StringPiece> ancestors;
  GetAllAncestorPaths(path, &ancestors);
  if (ancestors.empty()) {
    LOG(ERROR) << "Empty path!";
    return res;
  }

  std::shared_ptr<AccessCounter> counter;
  for (int i = static_cast<int>(ancestors.size() - 1); i >= 0; --i) {
    counter = Get(ancestors[i].as_string());
    if (!counter) {
      continue;
    }
    break;
  }
  if (!counter) {
    return res;
  }

  for (int i = 0; i < static_cast<int>(counter->snapshots_.size()); i++) {
    const auto& dc = dcs_->GetDataCenterById(i);
    if (dc == kUnknownDataCenter) {
      LOG(WARNING) << "Unknown DC id: " << i;
      continue;
    }
    res.emplace(dc, counter->snapshots_[i].load());
  }
  return res;
}

void AccessCounterManager::EmitSnapshot(
    std::shared_ptr<AccessCounter> counter) {
  CHECK_NOTNULL(counter.get());

  counter->Flush(FLAGS_accesscounter_learn_factor);

  auto snapshot = counter->TakeSnapshot();
  ns_->SaveAccessCounterSnapshot(snapshot);
}

void AccessCounterManager::ApplySnapshot(
    const cloudfs::fsimage
    ::AccessCounterSection_AccessCounterSnapshotProto&
    snapshot) {  // NOLINT(whitespace/line_length)
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  counter_map_.emplace(snapshot.path(),
      std::make_shared<AccessCounter>(snapshot, dcs_->size()));
}

}  // namespace dancenn

