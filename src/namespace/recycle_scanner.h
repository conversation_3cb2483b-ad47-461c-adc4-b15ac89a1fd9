#ifndef NAMESPACE_RECYCLE_SCANNER_H_
#define NAMESPACE_RECYCLE_SCANNER_H_

#include <chrono>
#include <condition_variable>
#include <memory>
#include <string>

#include "base/actor.h"
#include "base/metrics.h"
#include "namespace/meta_scanner.h"

namespace dancenn {

class NameSpace;

class RecycleScanner : public std::enable_shared_from_this<RecycleScanner> {
 public:
  explicit RecycleScanner(NameSpace* ns);
  ~RecycleScanner();

  void Start();
  void Stop();

 private:
  void InitMetrics();
  void FiniMetrics();

 private:
  class ScanTask : public cnetpp::concurrency::Task {
   public:
    ScanTask(std::shared_ptr<RecycleScanner> scanner);
    ~ScanTask();
    bool operator()(void* arg);
    void Stop();

   private:
    void InitMetrics();
    void FiniMetrics();
    void PreScan();
    void DoScan();
    void PostScan();
    void ProcessRecycleBin(const std::string& rb_path, const INode& rb_inode);
    bool IsUserBin(const INode& inode);
    bool ProcessUserBin(const std::string& ub_path, const INode& ub_inode);
    bool IsDateBin(const INode& inode);
    bool IsDateBinExpired(const INode& inode);
    bool DeleteEntry(const std::string& path, bool recursive);

   private:
    std::shared_ptr<RecycleScanner> recycle_scanner_;
    std::mutex mtx_;
    std::condition_variable cond_;

    std::chrono::steady_clock::time_point scan_start_;
    std::chrono::steady_clock::time_point scan_end_;
    int64_t num_rb_{-1};
    int64_t num_ub_deleted_{-1};
    int64_t num_ub_skipped_{-1};
    int64_t num_ub_invalid_{-1};
    int64_t num_db_deleted_{-1};
    int64_t num_db_skipped_{-1};
    int64_t num_db_invalid_{-1};
    uint64_t retention_sec_thold_{0};

    // metrics
    MetricID process_recycle_bin_time_;
    MetricID process_user_bin_time_;
  };

 private:
  NameSpace* ns_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;

  // stat
  std::atomic<int64_t> num_rb_{-1};
  std::atomic<int64_t> num_ub_deleted_{-1};
  std::atomic<int64_t> num_ub_skipped_{-1};
  std::atomic<int64_t> num_ub_invalid_{-1};
  std::atomic<int64_t> num_db_deleted_{-1};
  std::atomic<int64_t> num_db_skipped_{-1};
  std::atomic<int64_t> num_db_invalid_{-1};

  // metrics
  std::shared_ptr<Metrics> metrics_;
  std::vector<std::shared_ptr<Gauge>> gauges_;
};

}  // namespace dancenn

#endif  // NAMESPACE_RECYCLE_SCANNER_H_
