// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/02/09
// Description

#include "namespace/quota_manager.h"

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <netdb.h>

#include <chrono>
#include <shared_mutex>  // NOLINT(build/include_order)
#include <utility>

#include "base/java_exceptions.h"
#include "base/logger_metrics.h"
#include "cnetpp/base/end_point.h"
#include "namespace/namespace.h"
#include "namespace/xattr.h"
#include "rpc/rpc_client_options.h"
#include "rpc/rpc_controller.h"

DECLARE_string(observer_endpoint);
DECLARE_int32(client_rpc_port);
DECLARE_int32(quota_expire_interval_sec_soft_limit);
DECLARE_int32(quota_expire_interval_sec_hard_limit);
DECLARE_int32(quota_expire_txid_gap_soft_limit);
DECLARE_int32(quota_expire_txid_gap_hard_limit);
DECLARE_int32(max_eager_update_quota_size);
DECLARE_int32(max_quota_batch_size_to_update);
DECLARE_int32(quota_heartbeat_interval_sec);
DECLARE_int32(quotamap_num_slice);
DECLARE_int32(quotamap_num_element_each_slice);
DECLARE_bool(run_ut);

namespace dancenn {

QuotaDelta::QuotaDelta(int64_t dir_num, int64_t file_num, int64_t data_size)
    : inode_num_(dir_num + file_num),
      dir_num_(dir_num),
      file_num_(file_num),
      data_size_(data_size) {
}

void QuotaClient::Start() {
  CHECK(!tcp_client_);
  CHECK(!handlers_);
  CHECK(!channel_);
  CHECK(!stub_);

  tcp_client_ = std::make_shared<cnetpp::tcp::TcpClient>();
  tcp_client_->Launch("quota_client_cli");

  handlers_ = std::make_shared<cnetpp::concurrency::ThreadPool>(
      "quota_client_handlers");
  handlers_->set_num_threads(1);
  handlers_->Start();

  Refresh();
}

void QuotaClient::Stop() {
  CHECK_NOTNULL(tcp_client_);
  CHECK_NOTNULL(handlers_);
  // CHECK_NOTNULL(channel_);
  // CHECK_NOTNULL(stub_);

  if (channel_) {
    channel_->Shutdown();
  }
  handlers_->Stop(true);

  tcp_client_.reset();
  handlers_.reset();
  channel_.reset();
  stub_.reset();
}

bool QuotaClient::Refresh() {
  CHECK(!FLAGS_observer_endpoint.empty());
  struct hostent he;
  struct hostent* hep;
  char temp_work_buffer[4096];
  int err;
  if (gethostbyname2_r(
          FLAGS_observer_endpoint.c_str(),
          AF_INET,
          // This argument will be filled in on success.
          &he,
          temp_work_buffer,
          sizeof(temp_work_buffer),
          // In case of an error or if no entry is found result will be NULL.
          &hep,
          &err) != 0 ||
      hep == nullptr) {
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << "gethostbyname2_r failed for " << err;
    return false;
  }

  char addr_buf[INET_ADDRSTRLEN];
  if (inet_ntop(AF_INET, hep->h_addr, addr_buf, sizeof(addr_buf)) !=
      &addr_buf[0]) {
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << "inet_ntop failed";
    return false;
  }
  VLOG(8) << "observer ip addr: " << addr_buf;

  RpcClientOptions options;
  options.set_max_open_connections_per_user_and_fs(10);
  options.set_max_pending_calls_per_user_and_fs(100);
  options.set_request_timeout_ms(200);
  options.set_send_buffer_size(65536);
  options.set_receive_buffer_size(65536);
  options.set_tcp_receive_buffer_size(65536);
  options.set_tcp_send_buffer_size(65536);
  options.set_network_thread_count(1);
  options.set_client_id("quota_client");
  options.set_user("active namenode");
  options.set_protocol_name("org.apache.hadoop.hdfs.protocol.ClientProtocol");
  options.set_protocol_version(1);

  std::unique_lock<ReadWriteLockLight> _(rwlock_);
  channel_.reset(new PooledRpcChannel(
      tcp_client_,
      options,
      cnetpp::base::EndPoint(addr_buf, FLAGS_client_rpc_port),
      handlers_));
  stub_.reset(new ClientNamenodeService::Stub(channel_.get()));
  return true;
}

bool QuotaClient::GetQuotas(const cloudfs::GetINodeStatRequestProto& request,
                            cloudfs::GetINodeStatResponseProto* response) {
  std::shared_lock<ReadWriteLockLight> _(rwlock_);
  if (!stub_) {
    LOG(INFO) << "No stub in QuotaClient, may cause by refresh failed";
    return false;
  }
  RpcController controller;
  stub_->getINodeStat(&controller, &request, response, nullptr);
  if (controller.status() == RpcStatus::kSuccess) {
    return true;
  } else {
    static const auto* const fmt =
        new absl::ParsedFormat<'s'>("GetQuotas failed, reason: %s");
    LOG_IF(ERROR, !FLAGS_run_ut)
        << absl::StrFormat(*fmt, controller.ErrorText());
    return false;
  }
}

QuotaMapSlice::QuotaMapSlice()
    : quotas_(FLAGS_quotamap_num_element_each_slice) {
}

bool QuotaMapSlice::Get(INodeID inode_id, Quota* quota) {
  CHECK_NOTNULL(quota);
  std::shared_lock<ReadWriteLockLight> _(rwlock_);
  if (!quotas_.Get(inode_id, quota)) {
    return false;
  }
  if (quota->inode_stat.inode_id != inode_id) {
    LOG_IF(WARNING, !FLAGS_run_ut)
        << "Found invalid inode stat for inode " << inode_id;
    return false;
  }
  return true;
}

void QuotaMapSlice::Set(INodeID inode_id, const Quota& quota) {
  std::unique_lock<ReadWriteLockLight> _(rwlock_);
  quotas_.Set(inode_id, quota);
}

void QuotaMapSlice::Del(INodeID inode_id) {
  std::unique_lock<ReadWriteLockLight> _(rwlock_);
  quotas_.Evict(inode_id);
}

bool QuotaMapSlice::GetExpiredQuotas(
    int64_t ts_sec,
    TxID txid,
    int limit,
    cloudfs::GetINodeStatRequestProto* request) {
  CHECK_NOTNULL(request);
  std::shared_lock<ReadWriteLockLight> _(rwlock_);
  int size = 0;
  return quotas_.ForEach([ts_sec, txid, limit, &size, request](
                             const INodeID& inode_id, const Quota& quota) {
    if (quota.last_update_sec <= ts_sec || quota.snapshot_txid <= txid) {
      request->add_inodeids(inode_id);
      size++;
    }
    return size <= limit;
  });
}

QuotaManager::QuotaManager(std::shared_ptr<QuotaClient> quota_client)
    : quota_client_(quota_client),
      num_slices_(FLAGS_quotamap_num_slice),
      slice_mask_(FLAGS_quotamap_num_slice - 1),
      slice_to_update_(0),
      last_heartbeat_sec_(0),
      last_heartbeat_txid_(0) {
  CHECK_EQ(num_slices_ & slice_mask_, 0);
  for (std::size_t i = 0; i < num_slices_; i++) {
    slices_.emplace_back(new QuotaMapSlice);
  }
}

void QuotaManager::RefreshClient() {
  VLOG(8) << "QuotaManager calls RefreshClient";
  CHECK(quota_client_);
  quota_client_->Refresh();
}

Status QuotaManager::CheckCreateOrAdd(const std::vector<INode>& dst_ancestors,
                                      const QuotaDelta& additional_quota,
                                      TxID current_txid) {
  return CheckInternal(
      dst_ancestors, nullptr, nullptr, additional_quota, current_txid);
}

Status QuotaManager::CheckRename(const std::vector<INode>& dst_ancestors,
                                 const INode* src_inode,
                                 const INode* old_dst_inode,
                                 TxID current_txid) {
  return CheckInternal(dst_ancestors,
                       src_inode,
                       old_dst_inode,
                       QuotaDelta(0, 0, 0),
                       current_txid);
}

Status QuotaManager::CheckInternal(const std::vector<INode>& dst_ancestors,
                                   const INode* src_inode,
                                   const INode* old_dst_inode,
                                   const QuotaDelta& additional_quota,
                                   TxID current_txid) {
  int64_t expired_sec =
      GetCurrentTsInSec() - FLAGS_quota_expire_interval_sec_hard_limit;
  TxID expired_txid =
      current_txid < FLAGS_quota_expire_txid_gap_hard_limit
          ? 0
          : (current_txid - FLAGS_quota_expire_txid_gap_hard_limit);
  if (last_heartbeat_sec_.load(std::memory_order_relaxed) < expired_sec) {
    LOG_IF(WARNING, !FLAGS_run_ut)
        << "May lose connection with observer nn, ts is old and "
           "quotas are stale, just pass check";
    return Status(JavaExceptions::Exception::kNoException,
                  "LossConnWithObByTs");
  }
  if (last_heartbeat_txid_.load(std::memory_order_relaxed) < expired_txid) {
    LOG_IF(WARNING, !FLAGS_run_ut)
        << "May lose connection with observer nn, txid is old and "
           "quotas are stale, just pass check.";
    return Status(JavaExceptions::Exception::kNoException,
                  "LossConnWithObByTxId");
  }

  bool foundQuotaPolicy = false;
  // QuotaPolicyProto quota_policy;
  for (auto it = dst_ancestors.rbegin();
       it != dst_ancestors.rend() && !foundQuotaPolicy;
       it++) {
    if (it->type() == INode::kDirectory) {
      for (const auto& xattr : it->xattrs()) {
        if (XAttrs::GetPrefixName(xattr) == kQuotaPolicyXAttr) {
          foundQuotaPolicy = true;
          break;
        }
      }
    }
  }
  if (!foundQuotaPolicy) {
    return Status(JavaExceptions::Exception::kNoException, "NoQuotaPolicy");
  }

  return CheckInternal(dst_ancestors,
                       src_inode,
                       old_dst_inode,
                       additional_quota,
                       expired_sec,
                       current_txid,
                       expired_txid,
                       true);
}

Status QuotaManager::CheckInternal(const std::vector<INode>& dst_ancestors,
                                   const INode* src_inode,
                                   const INode* old_dst_inode,
                                   const QuotaDelta& additional_quota,
                                   int64_t expired_sec,
                                   TxID current_txid,
                                   TxID expired_txid,
                                   bool refresh_if_missing) {
  QuotaDelta required_quota = additional_quota;
  INodeStat src_inode_stat, old_dst_inode_stat;
  if ((src_inode != nullptr &&
       !GetINodeStat(
           src_inode->id(), expired_sec, expired_txid, &src_inode_stat)) ||
      (old_dst_inode != nullptr && !GetINodeStat(old_dst_inode->id(),
                                                 expired_sec,
                                                 expired_txid,
                                                 &old_dst_inode_stat))) {
    if (refresh_if_missing) {
      UpdateQuotasSync(dst_ancestors, src_inode, old_dst_inode, current_txid);
      return CheckInternal(dst_ancestors,
                           src_inode,
                           old_dst_inode,
                           additional_quota,
                           expired_sec,
                           current_txid,
                           expired_txid,
                           false);
    } else if (src_inode != nullptr && old_dst_inode == nullptr) {
      // We can't get inode stat of src inode.
      // Just assume it is a "minimal" inode.
      src_inode_stat.inode_num = 1;
      src_inode_stat.dir_num = src_inode->type() == INode::kDirectory;
      src_inode_stat.file_num = src_inode->type() != INode::kDirectory;
      src_inode_stat.data_size = 0;
      if (src_inode->type() == INode::kFile) {
        for (const auto& block : src_inode->blocks()) {
          src_inode_stat.data_size += block.numbytes();
        }
      }
    } else {
      return Status(JavaExceptions::Exception::kNoException,
                    "SrcOrOldDstQuotaNotFound");
    }
  }
  if (src_inode != nullptr) {
    required_quota.inode_num_ += src_inode_stat.inode_num;
    required_quota.dir_num_ += src_inode_stat.dir_num;
    required_quota.file_num_ += src_inode_stat.file_num;
    required_quota.data_size_ += src_inode_stat.data_size;
  }
  if (old_dst_inode != nullptr) {
    required_quota.inode_num_ -= old_dst_inode_stat.inode_num;
    required_quota.dir_num_ -= old_dst_inode_stat.dir_num;
    required_quota.file_num_ -= old_dst_inode_stat.file_num;
    required_quota.data_size_ -= old_dst_inode_stat.data_size;
  }

  Status s;
  std::vector<INode> expired_ancestors;
  for (const INode& ancestor : dst_ancestors) {
    QuotaPolicyProto quota_policy;
    if (ancestor.type() != INode::kDirectory ||
        !XAttrs::GetProtoBufXAttr(ancestor, kQuotaPolicyXAttr, &quota_policy)) {
      continue;
    }
    INodeStat inode_stat;
    if (!GetINodeStat(ancestor.id(), expired_sec, expired_txid, &inode_stat)) {
      LOG(INFO) << "INodeStat not found: " << ancestor.id();
      expired_ancestors.push_back(ancestor);
      continue;
    }
    Status t =
        SatisfyQuotaPolicy(ancestor.name().empty() ? "[root]" : ancestor.name(),
                           quota_policy,
                           inode_stat,
                           required_quota);
    if (s.IsOK() && !t.IsOK()) {
      s = std::move(t);
    }
  }
  if (s.IsOK() && !expired_ancestors.empty() && refresh_if_missing) {
    UpdateQuotasSync(dst_ancestors, src_inode, old_dst_inode, current_txid);
    return CheckInternal(dst_ancestors,
                         src_inode,
                         old_dst_inode,
                         additional_quota,
                         expired_sec,
                         current_txid,
                         expired_txid,
                         false);
  } else {
    UpdateQuotasAsync(expired_ancestors, current_txid);
    return s;
  }
}

Status QuotaManager::SatisfyQuotaPolicy(const std::string& dir_name,
                                        const QuotaPolicyProto& quota_policy,
                                        const INodeStat& inode_stat,
                                        const QuotaDelta& additional_quota) {
  using ErrMsgFmtT = absl::ParsedFormat<'s', 's', 'd', 'd', 'd'>;
  static std::unique_ptr<ErrMsgFmtT> err_msg_fmt;
  {
    static std::once_flag flag;
    std::call_once(flag, []() {
      const char* s =
          "The NameSpace quota (directories and files) of %s is exceeded: "
          "policy=%s, limit=%d, quota=%d, additional_quota=%d";
      err_msg_fmt = ErrMsgFmtT::New(s);
      CHECK(err_msg_fmt);
    });
  }

  if (quota_policy.has_inode_limit() && additional_quota.inode_num_ > 0 &&
      quota_policy.inode_limit() <=
          inode_stat.inode_num + additional_quota.inode_num_) {
    return Status(JavaExceptions::Exception::kNSQuotaExceededException,
                  absl::StrFormat(*err_msg_fmt,
                                  dir_name,
                                  "inode",
                                  quota_policy.inode_limit(),
                                  inode_stat.inode_num,
                                  additional_quota.inode_num_));
  }
  if (quota_policy.has_dir_limit() && additional_quota.dir_num_ > 0 &&
      quota_policy.dir_limit() <=
          inode_stat.dir_num + additional_quota.dir_num_) {
    return Status(JavaExceptions::Exception::kNSQuotaExceededException,
                  absl::StrFormat(*err_msg_fmt,
                                  dir_name,
                                  "dir",
                                  quota_policy.dir_limit(),
                                  inode_stat.dir_num,
                                  additional_quota.dir_num_));
  }
  if (quota_policy.has_file_limit() && additional_quota.file_num_ > 0 &&
      quota_policy.file_limit() <=
          inode_stat.file_num + additional_quota.file_num_) {
    return Status(JavaExceptions::Exception::kNSQuotaExceededException,
                  absl::StrFormat(*err_msg_fmt,
                                  dir_name,
                                  "file",
                                  quota_policy.file_limit(),
                                  inode_stat.file_num,
                                  additional_quota.file_num_));
  }
  if (quota_policy.has_data_size_limit() && additional_quota.data_size_ > 0 &&
      quota_policy.data_size_limit() <=
          inode_stat.data_size + additional_quota.data_size_) {
    return Status(JavaExceptions::Exception::kDSQuotaExceededException,
                  absl::StrFormat(*err_msg_fmt,
                                  dir_name,
                                  "dataSize",
                                  quota_policy.data_size_limit(),
                                  inode_stat.data_size,
                                  additional_quota.data_size_));
  }
  if (VLOG_IS_ON(8)) {
    // clang-format off
    using DebugMsgFmtT = absl::ParsedFormat<
      's',
      'd', 'd', 'd', 'd',            // quota_policy
      'd', 'd', 'd', 'd', 'd', 'd',  // inode_stat
      'd', 'd', 'd', 'd'             // additional_quota
    >;
    // clang-format on
    static std::unique_ptr<DebugMsgFmtT> debug_msg_fmt;
    static std::once_flag flag;
    std::call_once(flag, []() {
      const char* s =
          "The NameSpace quota (directories and files) of %s is satisfied: "
          "quota_policy={inode=%d,dir=%d,file=%d,data_size=%d}, "
          "inode_stat={inode=%d,dir=%d,file=%d,data_size=%d,txid=%d,ts=%d}, "
          "additional_quota={inode=%d,dir=%d,file=%d,data_size=%d}";
      debug_msg_fmt = DebugMsgFmtT::New(s);
      CHECK(debug_msg_fmt);
    });
    VLOG(8) << absl::StrFormat(
        *debug_msg_fmt,
        dir_name,
        quota_policy.has_inode_limit() ? quota_policy.inode_limit() : -1,
        quota_policy.has_dir_limit() ? quota_policy.dir_limit() : -1,
        quota_policy.has_file_limit() ? quota_policy.file_limit() : -1,
        quota_policy.has_data_size_limit() ? quota_policy.data_size_limit()
                                           : -1,
        inode_stat.inode_num,
        inode_stat.dir_num,
        inode_stat.file_num,
        inode_stat.data_size,
        inode_stat.txid,
        inode_stat.timestamp_sec,
        additional_quota.inode_num_,
        additional_quota.dir_num_,
        additional_quota.file_num_,
        additional_quota.data_size_);
  }
  return Status();
}

bool QuotaManager::SendHeartbeat(TxID current_txid) {
  cloudfs::GetINodeStatRequestProto request;
  const auto limit = FLAGS_max_quota_batch_size_to_update;
  {
    std::lock_guard<std::mutex> _(update_quotas_mtx_);
    for (auto it = quotas_to_update_.begin();
         it != quotas_to_update_.end() && request.inodeids_size() < limit;
         it++) {
      request.add_inodeids(it->first);
    }
  }

  int64_t expired_ts_sec =
      GetCurrentTsInSec() - FLAGS_quota_expire_interval_sec_soft_limit;
  TxID expired_txid = current_txid - FLAGS_quota_expire_txid_gap_soft_limit;
  for (auto i = 0; i < num_slices_ && request.inodeids_size() < limit; i++) {
    if (slices_[slice_to_update_]->GetExpiredQuotas(
            expired_ts_sec,
            expired_txid,
            limit - request.inodeids_size(),
            &request)) {
      slice_to_update_ = (slice_to_update_ + 1) % num_slices_;
    } else if (request.inodeids_size() < limit) {
      LOG(ERROR) << "QuotaManager SendHeartbeat LogicalError";
    }
  }

  return UpdateQuotasSync(request);
}

void QuotaManager::UpdateQuotasSync(const std::vector<INode>& dst_ancestors,
                                    const INode* src_inode,
                                    const INode* old_dst_inode,
                                    TxID txid) {
  cloudfs::GetINodeStatRequestProto request;
  if (src_inode != nullptr) {
    request.add_inodeids(src_inode->id());
  }
  if (old_dst_inode != nullptr) {
    request.add_inodeids(old_dst_inode->id());
  }
  for (const INode& ancestor : dst_ancestors) {
    QuotaPolicyProto quota_policy;
    if (ancestor.type() == INode::kDirectory &&
        XAttrs::GetProtoBufXAttr(ancestor, kQuotaPolicyXAttr, &quota_policy)) {
      request.add_inodeids(ancestor.id());
    }
  }
  UpdateQuotasSync(request);
}

bool QuotaManager::UpdateQuotasSync(
    const cloudfs::GetINodeStatRequestProto& request) {
  cloudfs::GetINodeStatResponseProto response;
  if (!quota_client_->GetQuotas(request, &response)) {
    return false;
  }
  VLOG(8) << "GetQuotas succeed, request count: " << request.inodeids_size()
          << ", succeed response count: " << response.inodestats_size()
          << ", failed response count: " << response.missinginodeids_size();

  int64_t last_update_sec = response.updatetsinsec();
  TxID snapshot_txid = response.snapshottxid();
  for (const auto& stat : response.inodestats()) {
    INodeID inode_id = stat.inodeid();
    Slice(inode_id).Set(inode_id,
                        Quota{.last_update_sec = last_update_sec,
                              .snapshot_txid = snapshot_txid,
                              .inode_stat = INodeStat(stat)});
  }
  for (INodeID inode_id : response.missinginodeids()) {
    Slice(inode_id).Del(inode_id);
  }

  std::lock_guard<std::mutex> _(update_quotas_mtx_);
  for (const auto& stat : response.inodestats()) {
    auto it = quotas_to_update_.find(stat.inodeid());
    if (it == quotas_to_update_.end() || it->second > snapshot_txid) {
      continue;
    }
    quotas_to_update_.erase(it);
  }
  for (INodeID inode_id : response.missinginodeids()) {
    auto it = quotas_to_update_.find(inode_id);
    if (it == quotas_to_update_.end() || it->second > snapshot_txid) {
      continue;
    }
    quotas_to_update_.erase(it);
  }
  if (last_heartbeat_sec_ < last_update_sec) {
    last_heartbeat_sec_ = last_update_sec;
  }
  if (last_heartbeat_txid_ < snapshot_txid) {
    last_heartbeat_txid_ = snapshot_txid;
  }
  return true;
}

void QuotaManager::UpdateQuotasAsync(const std::vector<INode>& inodes,
                                     TxID txid) {
  std::vector<INodeID> inode_ids;
  for (const INode& inode : inodes) {
    INodeID id = inode.id();
    inode_ids.push_back(inode.id());
  }
  UpdateQuotasAsync(inode_ids, txid);
}

void QuotaManager::UpdateQuotasAsync(const std::vector<INodeID>& inode_ids,
                                     TxID txid) {
  std::lock_guard<std::mutex> _(update_quotas_mtx_);
  for (const INodeID& id : inode_ids) {
    if (quotas_to_update_.find(id) == quotas_to_update_.end()) {
      if (quotas_to_update_.size() > FLAGS_max_eager_update_quota_size) {
        continue;
      } else {
        quotas_to_update_[id] = txid;
      }
    } else if (quotas_to_update_[id] < txid) {
      quotas_to_update_[id] = txid;
    }
  }
}

bool QuotaManager::GetINodeStat(INodeID inode_id,
                                int64_t expired_sec,
                                TxID expired_txid,
                                INodeStat* inode_stat) {
  QuotaMapSlice& s = Slice(inode_id);
  Quota quota;
  if (!s.Get(inode_id, &quota)) {
    DLOG(INFO) << "Quota not found for inode_id=" << inode_id;
    return false;
  }
  if (quota.last_update_sec < expired_sec ||
      quota.snapshot_txid < expired_txid) {
    DLOG(INFO) << "Quota expired for inode_id=" << inode_id
               << ", quota.last_update_sec=" << quota.last_update_sec
               << ", expired_sec=" << expired_sec
               << ", quota.snapshot_txid=" << quota.snapshot_txid
               << ", expired_txid=" << expired_txid;
    return false;
  }
  *inode_stat = quota.inode_stat;
  return true;
}

QuotaMapSlice& QuotaManager::Slice(INodeID inode_id) {
  return *slices_[inode_id & slice_mask_];
}

int64_t QuotaManager::GetCurrentTsInSec() {
  return std::chrono::duration_cast<std::chrono::seconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

bool QuotaManager::TestOnlyGetQuota(INodeID inode_id, Quota* quota) {
  return Slice(inode_id).Get(inode_id, quota);
}

void QuotaManager::TestOnlySetQuota(const Quota& quota) {
  Slice(quota.inode_stat.inode_id).Set(quota.inode_stat.inode_id, quota);
}

int64_t QuotaManager::TestOnlyGetLastHeartbeatSec() {
  return last_heartbeat_sec_;
}

TxID QuotaManager::TestOnlyGetLastHeartbeatTxId() {
  return last_heartbeat_txid_;
}

QuotaManagerWrapper::QuotaManagerWrapper() : is_running_(false) {
}

QuotaManagerWrapper::~QuotaManagerWrapper() {
  Stop();
}

void QuotaManagerWrapper::SetNameSpace(NameSpace* ns) {
  CHECK_NOTNULL(ns);
  ns_ = ns;
}

void QuotaManagerWrapper::Start() {
  CHECK(!is_running_.exchange(true));
  CHECK(!cli_);
  CHECK(!mgr_);

  cli_ = std::make_shared<QuotaClient>();
  cli_->Start();
  mgr_ = std::make_unique<QuotaManager>(cli_);
  heartbeat_thread_ = std::thread([this]() {
    std::unique_lock<std::mutex> guard(mu_);
    int cnt = 0;
    while (is_running_) {
      cnt++;
      if (cnt > 60) {
        mgr_->RefreshClient();
        cnt = 0;
      }

      auto txid = ns_->GetLastCkptTxId();
      current_txid_.store(txid);
      mgr_->SendHeartbeat(txid);
      cv_.wait_for(guard,
                   std::chrono::seconds(FLAGS_quota_heartbeat_interval_sec),
                   [this]() -> bool { return !is_running_; });
    }
  });
}

void QuotaManagerWrapper::Stop() {
  if (is_running_.exchange(false)) {
    CHECK(cli_);
    CHECK(mgr_);

    heartbeat_thread_.join();
    mgr_.reset();
    cli_->Stop();
    cli_.reset();
  }
}

Status QuotaManagerWrapper::CheckCreateOrAdd(
    const std::vector<INode>& dst_ancestors,
    const QuotaDelta& additional_quota) {
  return mgr_->CheckCreateOrAdd(
      dst_ancestors, additional_quota, current_txid_.load());
}

Status QuotaManagerWrapper::CheckRename(const std::vector<INode>& dst_ancestors,
                                        const INode* src_inode,
                                        const INode* old_dst_inode) {
  return mgr_->CheckRename(
      dst_ancestors, src_inode, old_dst_inode, current_txid_.load());
}

}  // namespace dancenn
