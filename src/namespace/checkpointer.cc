// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#include <sys/types.h>
#include <dirent.h>
#include <algorithm>

#include "base/file_utils.h"
#include "namespace/checkpointer.h"

DECLARE_int32(meta_storage_checkpoint_period_sec);
DECLARE_int32(meta_storage_checkpoint_extra_retain_num);
DECLARE_string(namespace_meta_storage_ckpt_path);
DECLARE_bool(run_ut);

namespace dancenn {

Checkpointer::Checkpointer(NameSpace* ns) : ns_(ns) {}

void Checkpointer::Start() {
  if (!worker_) {
    LOG(INFO) << "Checkpoint started";
    should_run_ = true;
    worker_ = std::make_unique<cnetpp::concurrency::Thread>([this]() {
      while (should_run_) {
        Run();
        std::unique_lock<std::mutex> lock(cv_mutex_);
        cv_.wait_for(lock,
                     std::chrono::seconds(
                         FLAGS_meta_storage_checkpoint_period_sec),
                     [&]() { return !should_run_; });

      }
      LOG(INFO) << "Checkpointer worker stopped";
      return true;
    }, "Checkpointer");
    worker_->Start();
  }
}

void Checkpointer::Stop() {
  if (worker_) {
    {
      std::unique_lock<std::mutex> lock(cv_mutex_);
      should_run_ = false;
      cv_.notify_all();
    }
    worker_->Stop();
    worker_.reset();
  }
}

void Checkpointer::Run() {
  if (ns_->ha_state()->IsActive() || FLAGS_run_ut) {
    return;
  }
  std::string root = FLAGS_namespace_meta_storage_ckpt_path;
  if (!FileUtils::Exists(root)
      && !FileUtils::CreateDirectoryRecursively(root, S_IRWXU)) {
    LOG(WARNING) << "Create checkpoint root failed, root: " << root;
    return;
  }

  std::string tmp_path = root + "/ckpt.tmp";
  if (FileUtils::Exists(tmp_path)
      && !FileUtils::DeleteDirectoryRecursively(tmp_path)) {
    LOG(WARNING) << "Clear tmp checkpoint failed, path: " << tmp_path;
  }

  auto s = ns_->CreateCheckpoint(tmp_path);
  if (!s.IsOK()) {
    LOG(WARNING) << "Create checkpoint failed, ex: " << s.ToString();
    return;
  }

  auto current_ckpt_id = std::to_string(
      std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now().time_since_epoch()).count());
  std::string current_ckpt_path = root + "/ckpt." + current_ckpt_id;
  CHECK_EQ(rename(tmp_path.c_str(), current_ckpt_path.c_str()), 0);
  LOG(INFO) << "Create checkpoint: " << current_ckpt_path;

  // Clean history checkpoints
  std::set<uint64_t> history;
  FileUtils::ListDirectory(root, true, [&] (struct dirent* child) -> bool {
    if (strcmp(child->d_name, "ckpt.tmp") == 0) {
      return true;
    }
    uint64_t tmp_ckpt_id = 0;
    int ret = sscanf(child->d_name, "ckpt.%lu", &tmp_ckpt_id);
    if (ret != 1) {
      LOG(WARNING) << "Skip unexpected checkpoint path: " << child->d_name;
      return true;
    }
    history.insert(tmp_ckpt_id);
    return true;
  });

  auto extra_retain_num = FLAGS_meta_storage_checkpoint_extra_retain_num;
  LOG(INFO) << "Begin clean history checkpoint, size: " << history.size();
  while (history.size() >= extra_retain_num) {
    auto path = root + "/ckpt." + std::to_string(*(history.begin()));
    auto s = FileUtils::DeleteDirectoryRecursively(path.c_str());
    LOG(INFO) << "Delete expired checkpoint: " << path << ", status: " << s;
    history.erase(history.begin());
  }
}

}  // namespace dancenn
