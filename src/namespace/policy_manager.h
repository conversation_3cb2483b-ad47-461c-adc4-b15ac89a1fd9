// Copyright 2023 Mu <PERSON> <<EMAIL>>

#ifndef NAMESPACE_POLICY_MANAGER_H_
#define NAMESPACE_POLICY_MANAGER_H_

#include <ClientNamenodeProtocol.pb.h>
#include <hdfs.pb.h>
#include <inode.pb.h>

#include <memory>

#include "base/constants.h"
#include "base/policy_cache.h"
#include "base/status.h"

namespace dancenn {

using cloudfs::XAttrProto;
using google::protobuf::RepeatedPtrField;

typedef ::cloudfs::ReplicaPolicyProto ReplicaPolicy;
typedef ::cloudfs::ReadPolicyProto ReadPolicy;
typedef ::cloudfs::UfsUploadPolicyProto UploadPolicy;

extern const ReplicaPolicy kDefaultReplicaPolicy;
extern const ReadPolicy kDefaultReadPolicy;
extern UploadPolicy kDefaultUploadPolicy;

class NameSpace;

template <typename PolicyType>
class PolicyManagerBase;

// PolicyManager
class PolicyManager {
 public:
  // Return true if the input inode has xattrs for (replica | read |
  // write | directory replication) policy
  static bool HasPolicy(const INode& inode);

 public:
  explicit PolicyManager();
  ~PolicyManager() = default;

  PolicyManager(const PolicyManager&) = delete;
  PolicyManager& operator=(const PolicyManager&) = delete;

  void ReloadINodeXAttr(const std::string& path,
                        const INode& inode,
                        const RepeatedPtrField<XAttrProto>& to_upsert,
                        const RepeatedPtrField<XAttrProto>& to_remove);

  // init memory data
  void LoadAllPolicy(const std::string& full_path, const INode& inode);

  void RemoveAllPolicy(const std::string& full_path, const INode& inode);

  // Policy
  PolicyManagerBase<ReplicaPolicy>* ReplicaPolicyInstance() {
    return replica_policy_manager_.get();
  }
  PolicyManagerBase<ReadPolicy>* ReadPolicyInstance() {
    return read_policy_manager_.get();
  }
  PolicyManagerBase<UploadPolicy>* UploadPolicyInstance() {
    return upload_policy_manager_.get();
  }

  // ReplicaPolicy
  static bool GetReplicaPolicyHelper(const INode& inode, ReplicaPolicy* policy);

  // ReadPolicy
  static bool GetReadPolicyHelper(const INode& inode, ReadPolicy* policy);

  // UploadPolicy
  static bool GetUploadPolicyHelper(const INode& inode, UploadPolicy* policy);

 private:
  friend class EditLogTailerTest;

  std::shared_ptr<PolicyManagerBase<ReplicaPolicy>> replica_policy_manager_;
  std::shared_ptr<PolicyManagerBase<ReadPolicy>> read_policy_manager_;
  std::shared_ptr<PolicyManagerBase<UploadPolicy>> upload_policy_manager_;
};

template <typename PolicyType>
struct PolicyConfig {
  PolicyType default_value;

  std::string policy_name;

  std::string xattr_name;

  std::function<bool(const INode& inode, PolicyType* policy)> get_func;

  bool allow_file_level = false;
};

template <typename PolicyType>
class PolicyManagerBase {
 public:
  explicit PolicyManagerBase(PolicyConfig<PolicyType> config)
      : policy_cache_(config.default_value), config_(config) {
  }
  ~PolicyManagerBase() = default;

  bool GetPolicyHelper(const INode& inode, PolicyType* policy) {
    return config_.get_func(inode, policy);
  }

  std::string Name() {
    return config_.policy_name;
  }

  std::string XAttrName() {
    return config_.xattr_name;
  }

  bool AllowFileLevel() {
    return config_.allow_file_level;
  }

  void UpdatePolicy(const std::string& path, const PolicyType& policy) {
    LOG(INFO) << "Update " << Name() << ": " << path
              << " Policy: " << policy.ShortDebugString();
    policy_cache_.UpdatePath(path, policy);
  }

  void RemovePolicy(const std::string& path) {
    LOG(INFO) << "Remove " << Name() << ": " << path;
    policy_cache_.DeletePath(path);
  }

  // Directory-level Version
  void GetPolicy(const std::string& path, PolicyType* policy) {
    if (config_.allow_file_level) {
      VLOG(11) << "GetPolicy should check file level policy";
    }
    policy_cache_.GetAncestorOrSelf(path, policy);
  }

  // File-level Version
  void GetPolicy(const std::string& path,
                 const INode& inode,
                 PolicyType* policy) {
    if (config_.allow_file_level) {
      if (GetPolicyHelper(inode, policy)) {
        return;
      }
    }

    policy_cache_.GetAncestorOrSelf(path, policy);
  }

  std::vector<std::pair<std::string, PolicyType>> ListPolicy() {
    std::vector<std::pair<std::string, PolicyType>> result;

    policy_cache_.Filter(
        [&](const std::string& path, const PolicyType& policy) {
          result.emplace_back(path, policy);
          return false;  // will not add path to temp vector
        });

    return result;
  }

  void LoadPolicy(const std::string& fullpath, const INode& inode) {
    if (inode.type() == INode_Type_kDirectory) {
      PolicyType policy;
      if (GetPolicyHelper(inode, &policy)) {
        UpdatePolicy(fullpath, policy);
        LOG(INFO) << "Load " << Name() << ": " << fullpath
                  << " policy=" << policy.ShortDebugString();
      }
    }
  }

  void ReloadPolicy(const std::string& path,
                    const INode& inode,
                    const RepeatedPtrField<XAttrProto>& to_upsert,
                    const RepeatedPtrField<XAttrProto>& to_remove) {
    static auto ns =
        XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM;
    static cnetpp::base::StringPiece xattr_ns("system");

    auto replica_policy_name =
        cnetpp::base::StringPiece(XAttrName()).substr(xattr_ns.size() + 1);

    for (const auto& xattr : to_upsert) {
      if (xattr.namespace_() != ns) {
        continue;
      }
      cnetpp::base::StringPiece policy_name =
          cnetpp::base::StringPiece(xattr.name());
      if (policy_name == replica_policy_name) {
        PolicyType policy;
        policy.ParseFromString(xattr.value());
        this->UpdatePolicy(path, policy);
      }
    }

    for (const auto& xattr : to_remove) {
      if (xattr.namespace_() != ns) {
        continue;
      }
      cnetpp::base::StringPiece policy_name =
          cnetpp::base::StringPiece(xattr.name());
      if (policy_name == replica_policy_name) {
        this->RemovePolicy(path);
      }
    }
  }

 private:
  PolicyCache<PolicyType> policy_cache_;

  PolicyConfig<PolicyType> config_;
};

}  // namespace dancenn

#endif  // NAMESPACE_POLICY_MANAGER_H_
