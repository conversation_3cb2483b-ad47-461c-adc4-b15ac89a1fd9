//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "namespace/namespace_stat_delta_cache.h"

#include "base/time_util.h"


DECLARE_uint32(inode_stat_delta_cache_gc_interval_ms);

namespace dancenn {

std::shared_ptr<INodeStatDeltaCache> g_delta_cache = nullptr;

INodeStatDeltaCache::INodeStatDeltaCache(MetaStorage* meta_storage)
    : meta_storage_(meta_storage) {
}

INodeStatDeltaCache::~INodeStatDeltaCache() {
}

void INodeStatDeltaCache::GetCurrentINodeStat(INodeStat* stat) {
  auto iter = deltas_map_.find(stat->inode_id);
  if (iter != deltas_map_.end()) {
    for (auto& delta : iter->second) {
      if (delta.txid <= stat->txid) {
        continue;
      }
      *stat += delta;
    }
  }
}

void INodeStatDeltaCache::Add(const INodeStat& new_delta) {
  CHECK_GT(new_delta.inode_id, 0);
  CHECK_GT(new_delta.txid, 0);
  deltas_map_[new_delta.inode_id].push_back(new_delta);;
}

void INodeStatDeltaCache::GC(bool force) {
  if (deltas_map_.empty()) {
    return;
  }
  uint64_t now = TimeUtil::GetNowEpochMs();
  bool wait_enough =
    now - last_gc_epoch_ >= FLAGS_inode_stat_delta_cache_gc_interval_ms;
  if (!wait_enough && !force) {
    return;
  }
  last_gc_epoch_ = now;
  uint64_t ondisk_txid = meta_storage_->GetLastCkptTxId();
  auto iter = deltas_map_.begin();
  while (iter != deltas_map_.end()) {
    INodeID id = iter->first;
    std::deque<INodeStat>& deltas = iter->second;
    while (!deltas.empty()) {
      if (ondisk_txid < deltas.front().txid) {
        break;
      }
      deltas.pop_front();
    }
    if (deltas.empty()) {
      iter = deltas_map_.erase(iter);
    } else {
      iter++;
    }
  }
}


} // namespace dancenn
