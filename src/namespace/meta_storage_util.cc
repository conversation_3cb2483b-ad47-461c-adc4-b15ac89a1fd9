
#include "rocksdb/slice.h"
#include "namespace/meta_storage_util.h"
#include "namespace/meta_storage.h"

namespace dancenn {

const std::vector<uint32_t> verifiable_cf_indexes = {
  kINodeDefaultCFIndex,
  kINodePendingDeleteCFIndex,
  kNameSystemInfoCFIndex,
  kAccessCounterCFIndex,
  kINodeIndexCFIndex,
  kLegacyBlockPufsInfoCFIndex,
  kLegacyDeprecatedBlockPufsInfoCFIndex,
  kLegacyBlockInfoProtoCFIndex,
  kBlockInfoProtoCFIndex,
  kLocalBlockCFIndex,
  kDeprecatingBlockCFIndex,
  kDeprecatedBlockCFIndex,
  // kINodeStatCFIndex,
  kLifecyclePolicyCFIndex,
  // kStorageClassStatCFIndex,
  // kStorageClassReportCFIndex,
  kLeaseCFIndex,
  // FIXME(xuex) order of DatanodeInfoEntryPB::storages may be different
  // kDatanodeInfoCFIndex,
  kSnapshotRootInfoCFIndex,
  kSnapshotInodeCFIndex,
  kSnapshotRenameRecordCFIndex,
  kSnapshotINodeIndexCFIndex,
};

DBStat DBComparator::GetDBStat(bool check_synced) {
  return GetDBStat(verifiable_cf_indexes, check_synced);
}

DBStat DBComparator::GetDBStat(const std::vector<uint32_t> cf_idxs,
                               bool check_synced) {
  DBStat dbstat;

  MetaStorageSnapHolderPtr act_snap_holder = active_->GetSnapshot();
  MetaStorageSnapHolderPtr sby_snap_holder = standby_->GetSnapshot();
  auto act_snapshot = act_snap_holder->snapshot();
  auto sby_snapshot = sby_snap_holder->snapshot();
  for (auto cf_idx : cf_idxs) {
    for (auto cfinfo : meta_storage_cfinfo) {
      if (cfinfo.cf_idx == cf_idx) {
        auto act_iter_holder = active_->GetIterator(act_snapshot, cfinfo.cf_idx);
        auto sby_iter_holder = standby_->GetIterator(sby_snapshot, cfinfo.cf_idx);
        CFStat act_cfstat, sby_cfstat;
        GetCFStat(cfinfo, act_iter_holder->iter(), &act_cfstat);
        GetCFStat(cfinfo, sby_iter_holder->iter(), &sby_cfstat);

        if (!(act_cfstat == sby_cfstat)) {
          std::string msg = absl::StrFormat(
              "DB statistics mismatch on CF %lu, active: %s, standby: %s",
              cf_idx, act_cfstat.ToString(), sby_cfstat.ToString());
          if (check_synced) {
            LOG(FATAL) << msg;
          } else {
            LOG(WARNING) << msg;
          }
        }

        dbstat[cf_idx] = act_cfstat;
      }
    }
  }
  return dbstat;
}

void DBComparator::GetCFStat(const ms_cfinfo_t& cfinfo,
                             MetaStorageIterPtr ms_iter,
                             CFStat* cfstat) {
  CHECK_NOTNULL(cfstat);
  ms_iter->SeekToFirst();
  for (ms_iter->SeekToFirst(); ms_iter->Valid(); ms_iter->Next()) {
    cfstat->num_entry++;
  }
  if (!ms_iter->status().ok()) {
    LOG(WARNING) << absl::StrFormat(
        "Error during traverse rocksdb::Iterator, %s",
        ms_iter->status().ToString());
  }
}

bool DBComparator::CheckDBConsistency() {
  auto cf_idxs = verifiable_cf_indexes;

  MetaStorageSnapHolderPtr act_snap_holder = active_->GetSnapshot();
  MetaStorageSnapHolderPtr sby_snap_holder = standby_->GetSnapshot();

  bool all_match = true;
  auto act_snapshot = act_snap_holder->snapshot();
  auto sby_snapshot = sby_snap_holder->snapshot();
  for (auto cf_idx : cf_idxs) {
    for (auto cfinfo : meta_storage_cfinfo) {
      if (cfinfo.cf_idx == cf_idx) {
        LOG(INFO) << absl::StrFormat(
            "==== Check Data in Column Family [%s] ====",
            cfinfo.cf_name);
        auto act_iter_holder = active_->GetIterator(act_snapshot, cfinfo.cf_idx);
        auto sby_iter_holder = standby_->GetIterator(sby_snapshot, cfinfo.cf_idx);
        bool match = CheckCFConsistency(cfinfo,
                                        act_iter_holder->iter(),
                                        sby_iter_holder->iter());
        if (match) {
          LOG(INFO) << absl::StrFormat(
              "<<<<<< Data in Column Family [%s] Check Pass <<<<<<",
              cfinfo.cf_name);
        } else {
          LOG(WARNING) << absl::StrFormat(
              "<<<<<< Data in Column Family [%s] Check Failed <<<<<<",
              cfinfo.cf_name);
          all_match = false;
        }
      }
    }
  }
  return all_match;
}

bool DBComparator::CheckCFConsistency(const ms_cfinfo_t& cfinfo,
                                      MetaStorageIterPtr act_iter,
                                      MetaStorageIterPtr sby_iter) {
  bool match = true;
  uint64_t line_idx = 0;
  act_iter->SeekToFirst();
  sby_iter->SeekToFirst();
  while (true) {
    if (!act_iter->Valid() && !sby_iter->Valid()) {
      break;
    }
    if (!(act_iter->Valid() && sby_iter->Valid())) {
      LOG(WARNING) << absl::StrFormat(
          "CF %s line %lu data num mismatch, active: %s, standby: %s",
          cfinfo.cf_name, line_idx,
          act_iter->Valid() ? "Valid" : "Invalid",
          sby_iter->Valid() ? "Valid" : "Invalid");
      match = false;
      break;
    }

    bool key_match = CheckKeyConsistency(cfinfo,
                                         act_iter->key(),
                                         sby_iter->key());
    if (!key_match) {
      LOG(WARNING) << absl::StrFormat(
          "CF %s line %lu key mismatch",
          cfinfo.cf_name, line_idx);
      match = false;
      break;
    }

    bool val_match = CheckValueConsistency(cfinfo,
                                           act_iter->value(),
                                           sby_iter->value());
    if (!val_match) {
      LOG(WARNING) << absl::StrFormat(
          "CF %s line %lu value mismatch",
          cfinfo.cf_name, line_idx);
      match = false;
      break;
    }

    line_idx++;
    act_iter->Next();
    sby_iter->Next();
  }
  if (!act_iter->status().ok() || !sby_iter->status().ok()) {
    LOG(WARNING) << absl::StrFormat(
        "Error during traverse rocksdb::Iterator, Active: %s, Stanndby: %s",
        act_iter->status().ToString(),
        sby_iter->status().ToString());
  }
  return match;
}

bool DBComparator::CheckKeyConsistency(const ms_cfinfo_t& cfinfo,
                                       const rocksdb::Slice& act_key_slice,
                                       const rocksdb::Slice& sby_key_slice) {
  std::string act_key = cfinfo.key_decoder(act_key_slice);
  std::string sby_key = cfinfo.key_decoder(sby_key_slice);
  bool match = (act_key == sby_key);
  if (!match) {
    LOG(ERROR) << absl::StrFormat(
        "Found key mismatch on CF %s, active-key: %s, standby-key: %s",
        cfinfo.cf_name, act_key, sby_key);
  }
  return match;
}

bool DBComparator::CheckValueConsistency(const ms_cfinfo_t& cfinfo,
                                         const rocksdb::Slice& act_val_slice,
                                         const rocksdb::Slice& sby_val_slice) {
  bool match = true;
  std::string act_val = cfinfo.val_decoder(act_val_slice);
  std::string sby_val = cfinfo.val_decoder(sby_val_slice);
  match = (act_val == sby_val);
  if (cfinfo.val_comparator) {
    match = cfinfo.val_comparator(act_val_slice, sby_val_slice);
  }
  if (!match) {
    LOG(ERROR) << absl::StrFormat(
        "Found value mismatch on CF %s, active-value: %s, standby-value: %s",
        cfinfo.cf_name, act_val, sby_val);
  }
  return match;
}

} // namespace dancenn
