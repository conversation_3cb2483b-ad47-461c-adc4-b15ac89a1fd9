//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_USER_GROUP_INFO_H_
#define NAMESPACE_USER_GROUP_INFO_H_

#include <ClientNamenodeProtocol.pb.h>

#include <string>
#include <memory>

namespace dancenn {

class UserGroupInfo {
 public:
  UserGroupInfo() {
    // mocked here
    // Todo() Super user flags.
    current_user_ = "root";
    current_group_ = "supergroup";
  }

  UserGroupInfo(const std::string& user, int auth) {
    current_user_ = user;
    current_group_ = user;
    auth_ = auth;
  }

  UserGroupInfo(const std::string& user,
                const std::string& group) {
    current_user_ = user;
    current_group_ = group;
    auth_ = 0;
  }

  UserGroupInfo(const UserGroupInfo& o) {
    current_user_ = o.current_user_;
    current_group_ = o.current_group_;
  }

  void set_user(const std::string& user) { current_user_ = user; }

  const std::string &current_user() const {
    // FSNamesystem:
    // UserGroupInformation fsOwner = UserGroupInformation.getCurrentUser();
    return current_user_;
  }

  const std::string &current_group() const {
    return current_group_;
  }

  const std::string &GetRemoteUser() const {
    // org.apache.hadoop.hdfs.server.namenode.NameNodeRpcServer#getRemoteUser
    return current_user_;
  }

  const std::string &GetShortUserName() const {
    // org.apache.hadoop.security.UserGroupInformation#getShortUserName
    return current_user_;
  }

 private:
  std::string current_user_;
  // TODO only single group
  std::string current_group_;
  int auth_;
};

// TODO see java NameNode.getRemoteUser()
extern std::shared_ptr<UserGroupInfo> GetRemoteUserInfo(
    ::google::protobuf::RpcController* controller);

}  // namespace dancenn

#endif  // NAMESPACE_USER_GROUP_INFO_H_
