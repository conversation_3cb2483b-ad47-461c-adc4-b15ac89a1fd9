//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "rocksdb/merge_operator.h"
#include "rocksdb/db.h"
#include "cnetpp/base/string_piece.h"

#include "base/status.h"
#include "base/constants.h"
#include "namespace/inode.h"
#include "namespace/namespace.h"
#include "namespace/namespace_scrub_inodestat.h"

DECLARE_bool(dancenn_observe_mode_on);

namespace rocksdb {
  class WriteBatch;
  class ColumnFamilyHandle;
}

namespace dancenn {

class MetaStorage;

struct INodeStatUtils {
  static Status Parse(const std::string& str, INodeStat* stat);
  static Status Parse(const rocksdb::Slice& str, INodeStat* stat);
  static Status Parse(const cnetpp::base::StringPiece& str, INodeStat* stat);

  static void GetFileSizeAndBlockNum(const INode &file, int64_t *size,
                                     int64_t *block_num);
};

class INodeStatChangeRecorder {
public:
  INodeStatChangeRecorder(
      const std::shared_ptr<MetaStorage>& meta_storage,
      const std::shared_ptr<INodeStatDeltaCache>& delta_cache);
  ~INodeStatChangeRecorder();

  void RecordINodeDelete(int64_t txid, const INode &to_del,
                         const std::vector<INodeID> &ancestors);
  void RecordINodeAdd(int64_t txid, const INode &to_add,
                      const std::vector<INodeID> &ancestors);
  void RecordINodeUpdate(int64_t txid, const INode &old_inode,
                         const INode &new_inode,
                         const std::vector<INodeID> &ancestors);
  void RecordINodeRename(int64_t txid, const INode &src_inode,
                         const INode &dst_inode,
                         const std::vector<INodeID> &src_ancestors,
                         const std::vector<INodeID> &dst_ancestors);
  void RecordINodeRenameWithOverwriteDst(
      int64_t txid, const INode &src_inode, const INode &dst_inode,
      const INode &old_dst_inode,
      const std::vector<INodeID> &src_ancestors,
      const std::vector<INodeID> &dst_ancestors);

  void UpdateToWriteBatch(rocksdb::WriteBatch* wb, rocksdb::ColumnFamilyHandle* cf_handle);

  void Validate();
  std::string GetDebugInfo();

private:
  void UpdateAncestors(const INodeStat& stat, const std::vector<INodeID>& ancestors);
  void GetCurrentINodeStat(INodeID dir_id, INodeStat* stat);

private:
  std::unordered_map<uint64_t, std::vector<INodeStat>> stat_deltas_;
  std::shared_ptr<MetaStorage> meta_storage_;
  int64_t txid_{-1};
  std::shared_ptr<INodeStatDeltaCache> delta_cache_;
};

class INodeStatMergeOperator : public rocksdb::AssociativeMergeOperator {
public:
  virtual bool Merge(const rocksdb::Slice& key,
                     const rocksdb::Slice* existing_value,
                     const rocksdb::Slice& value,
                     std::string* new_value,
                     rocksdb::Logger* logger) const override;
  static const char* kClassName() { return "INodeStatMergeOperator"; }
  virtual const char* Name() const override { return kClassName(); }
};

static std::vector<INodeID> ConvertAncestorIDs(
    const std::vector<INode> &ancestors) {
  std::vector<INodeID> ids;
  std::for_each(ancestors.begin(), ancestors.end(),
                [&ids] (const INode& inode) { ids.push_back(inode.id()); });
  return ids;
}
static std::vector<INodeID> ConvertAncestorIDs(
    const google::protobuf::RepeatedField<INodeID> &ancestors) {
  return std::vector<INodeID>(ancestors.begin(), ancestors.end());
}

#define DECLARE_STAT_RECORDER(__meta_storage__, __apply_ctx__)                 \
  std::unique_ptr<INodeStatChangeRecorder> __recorder__;                       \
  if (UNLIKELY(FLAGS_dancenn_observe_mode_on)) {                               \
    __recorder__.reset(new INodeStatChangeRecorder(                            \
          __meta_storage__, __apply_ctx__->delta_cache));                      \
  }

#define STAT_RECORDER_PTR __recorder__.get()

#define STAT_RECORDER_INODE_DELETE(__ctx__, __to_del__, __ancestors__)         \
  do {                                                                         \
    if (UNLIKELY(__recorder__ != nullptr)) {                                   \
      __recorder__->RecordINodeDelete((__ctx__)->op->txid(), __to_del__,       \
                                      ConvertAncestorIDs(__ancestors__));      \
    }                                                                          \
  } while (0)

#define STAT_RECORDER_INODE_ADD(__ctx__, __to_add__, __ancestors__)            \
  do {                                                                         \
    if (UNLIKELY(__recorder__ != nullptr)) {                                   \
      __recorder__->RecordINodeAdd((__ctx__)->op->txid(), __to_add__,          \
                                   ConvertAncestorIDs(__ancestors__));         \
    }                                                                          \
  } while (0)

#define STAT_RECORDER_INODE_UPDATE(__ctx__, __old__, __new__, __ancestors__)   \
  do {                                                                         \
    if (UNLIKELY(__recorder__ != nullptr)) {                                   \
      __recorder__->RecordINodeUpdate((__ctx__)->op->txid(), __old__, __new__, \
                                      ConvertAncestorIDs(__ancestors__));      \
    }                                                                          \
  } while (0)

#define STAT_RECORDER_INODE_RENAME(__ctx__, __src__, __dst__,                  \
                                   __src_ancestors__, __dst_ancestors__)       \
  do {                                                                         \
    if (UNLIKELY(__recorder__ != nullptr)) {                                   \
      __recorder__->RecordINodeRename((__ctx__)->op->txid(), __src__, __dst__, \
                                      ConvertAncestorIDs(__src_ancestors__),   \
                                      ConvertAncestorIDs(__dst_ancestors__));  \
    }                                                                          \
  } while (0)

#define STAT_RECORDER_INODE_RENAME_OVERWRITE(__ctx__, __src__, __dst__,        \
                                             __old_dst__, __src_ancestors__,   \
                                             __dst_ancestors__)                \
  do {                                                                         \
    if (UNLIKELY(__recorder__ != nullptr)) {                                   \
      __recorder__->RecordINodeRenameWithOverwriteDst(                         \
          (__ctx__)->op->txid(), __src__, __dst__, __old_dst__,                \
          ConvertAncestorIDs(__src_ancestors__),                               \
          ConvertAncestorIDs(__dst_ancestors__));                              \
    }                                                                          \
  } while (0)

#define RECORD_STAT_TO_WB(__recorder__, __wb_, __cf__) do {                    \
  if (UNLIKELY(__recorder__ != nullptr)) {                                     \
    __recorder__->UpdateToWriteBatch((__wb_), (__cf__));                       \
  }                                                                            \
} while (0)

#define DEBUG_RECORD_STAT(__recorder__) do {                                   \
  if (UNLIKELY(__recorder__ != nullptr && VLOG_IS_ON(20))) {                   \
    VLOG(20) << "Caller: " << __FUNCTION__                                     \
             << ", RecordStat: " << __recorder__->GetDebugInfo();              \
  }                                                                            \
} while (0)

}  // namespace dancenn
