#ifndef NAMESPACE_META_SCANNER_V2_H_
#define NAMESPACE_META_SCANNER_V2_H_

#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <rocksdb/iterator.h>

#include <memory>
#include <mutex>
#include <unordered_map>

#include "namespace/meta_storage.h"

namespace dancenn {

using MetaScannerTaskID = uint64_t;

class MetaStorage;
class MetaScannerBase;
class MetaScannerWorkTask;
class MetaScannerWorkerBase;

class MetaScannerTaskBase : public cnetpp::concurrency::Task {
 public:
  MetaScannerTaskBase() = default;
  virtual ~MetaScannerTaskBase() = default;

  virtual void SetMetaStorage(MetaStorage* ms);
  virtual void SetMetaScanner(MetaScannerBase* ms);

  virtual MetaScannerTaskID GetTaskID();
  virtual void SetTaskID(MetaScannerTaskID id);

  virtual uint32_t GetCfIdx() = 0;

  // Return false will skip current scan
  virtual bool PreScan() = 0;
  // Return false will interrupt current scan and DO post scan
  virtual bool Handle(MetaStorageSnapPtr snapshot,
                      const rocksdb::Slice& key,
                      const rocksdb::Slice& value) = 0;
  // Return value is ignored
  virtual bool PostScan() = 0;

  // Will not schedule next scan if delay is -1
  virtual int64_t GetDelayUs() = 0;

  virtual std::string ToString() = 0;

  virtual bool operator()(void* arg = nullptr) override;
  virtual bool Run(void* arg = nullptr);

 protected:
  virtual void ScheduleNext();

 protected:
  MetaStorage* meta_storage_{nullptr};
  MetaScannerBase* meta_scanner_{nullptr};

  MetaScannerTaskID id_;
};

class MetaScannerBase {
 public:
  MetaScannerBase() = default;
  virtual ~MetaScannerBase() = default;

  virtual void Start() = 0;
  virtual void Stop() = 0;

  virtual MetaScannerTaskID AddScanTask(
      std::shared_ptr<MetaScannerTaskBase> task) = 0;
  virtual bool AddScanTask(MetaScannerTaskID id) = 0;
  // Interrupt current scan task if force is true
  virtual bool EraseScanTask(MetaScannerTaskID id, bool force) = 0;

  virtual bool AddWorkTask(std::shared_ptr<MetaScannerWorkTask> task) = 0;
};

class MetaScannerV2 : public MetaScannerBase {
 public:
  MetaScannerV2(MetaStorage* ms);
  virtual ~MetaScannerV2();

  void Start() override;
  void Stop() override;

  MetaScannerTaskID AddScanTask(
      std::shared_ptr<MetaScannerTaskBase> task) override;
  bool AddScanTask(MetaScannerTaskID id) override;
  bool EraseScanTask(MetaScannerTaskID id, bool force) override;

  bool AddWorkTask(std::shared_ptr<MetaScannerWorkTask> task) override;

  // Test
  std::unordered_map<uint64_t, std::shared_ptr<MetaScannerTaskBase>>
  TestGetAllTasks();

 private:
  MetaStorage* ms_{nullptr};
  std::unique_ptr<cnetpp::concurrency::ThreadPool> scan_worker_;
  std::unique_ptr<MetaScannerWorkerBase> worker_;
  std::mutex mutex_;
  MetaScannerTaskID id_;
  std::unordered_map<uint64_t, std::shared_ptr<MetaScannerTaskBase>> tasks_;
};

class MetaScannerWorkTask : public cnetpp::concurrency::Task {
 public:
  bool operator()(void* arg = nullptr) override;
  virtual bool Run(void* arg = nullptr) = 0;
};

class MetaScannerWorkerBase {
 public:
  MetaScannerWorkerBase() = default;
  virtual ~MetaScannerWorkerBase() = default;
  virtual void Start() = 0;
  virtual void Stop() = 0;

  virtual bool AddTask(std::shared_ptr<MetaScannerWorkTask> task) = 0;
};

class MetaScannerWorkerV1 : public MetaScannerWorkerBase {
 public:
  MetaScannerWorkerV1();
  virtual ~MetaScannerWorkerV1() = default;
  virtual void Start() override;
  virtual void Stop() override;

  virtual bool AddTask(std::shared_ptr<MetaScannerWorkTask> task) override;

 private:
  std::unique_ptr<cnetpp::concurrency::ThreadPool> workers_;
};

}  // namespace dancenn

#endif  // NAMESPACE_META_SCANNER_V2_H_
