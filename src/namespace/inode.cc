#include "namespace/inode.h"

#include <absl/strings/substitute.h>

DECLARE_bool(enable_snapshot_feature);

namespace dancenn {

void SnapshotReadInfo::Clear() {
  is_under_snapshot_ = false;
  is_snapshot_directory_ = false;
  read_txid_ = 0;
}

INodeInPath::INodeInPath(const INodeInPath& other)
    : inode_(other.inode_),
      checked_write_(other.checked_write_),
      snapshot_read_info_(other.snapshot_read_info_),
      latest_snapshot_create_txid_(other.latest_snapshot_create_txid_),
      snapshot_root_id_(other.snapshot_root_id_),
      snapshot_references_(other.snapshot_references_),
      attr_holder_(other.attr_holder_),
      old_inode_(other.old_inode_ ? std::make_unique<INode>(*other.old_inode_)
                                  : std::unique_ptr<INode>()) {
  if (other.snapshot_inode_) {
    snapshot_inode_ = std::make_unique<INode>(*other.snapshot_inode_);
  }
}

INodeInPath& INodeInPath::operator=(const INodeInPath& other) {
  INodeInPath temp(other);
  this->Swap(&temp);
  return *this;
}

void INodeInPath::Swap(INodeInPath* other) {
  if (other != this) {
    inode_.Swap(&other->inode_);
    std::swap(checked_write_, other->checked_write_);
    std::swap(snapshot_read_info_, other->snapshot_read_info_);
    std::swap(latest_snapshot_create_txid_,
              other->latest_snapshot_create_txid_);
    std::swap(snapshot_root_id_, other->snapshot_root_id_);
    std::swap(snapshot_references_, other->snapshot_references_);
    snapshot_inode_.swap(other->snapshot_inode_);
    attr_holder_.Swap(&other->attr_holder_);
    old_inode_.swap(other->old_inode_);
  }
}

void INodeInPath::Clear() {
  inode_.Clear();
  checked_write_ = NOT_CHECKED;
  snapshot_read_info_.Clear();
  latest_snapshot_create_txid_ = 0;
  snapshot_root_id_ = kInvalidINodeId;
  snapshot_references_.clear();
  snapshot_inode_ = nullptr;
  attr_holder_.Clear();
  old_inode_.reset();
}

void INodeInPath::CollectSnapshotInfo(const INode& inode) {
  if (!FLAGS_enable_snapshot_feature) {
    // In case we disabled snapshot without fully gc so that snapshot_references
    // field exists
    return;
  }
  if (inode.snapshot_references_size() > 0) {
    CHECK_EQ(inode.snapshot_references_size(), 1)
        << "non snapshot inode have more than one references"
        << inode.DebugString();
    snapshot_references_.emplace_back(inode.snapshot_references().Get(0));
  }
  if (inode.is_snapshottable()) {
    CHECK_EQ(snapshot_root_id_, kInvalidINodeId)
        << "Nested snapshot root: " << snapshot_root_id_ << " " << inode.id()
        << " " << inode.name();
    snapshot_root_id_ = inode.id();
    if (inode.snapshots_size() > 0) {
      for (int i = inode.snapshots_size() - 1; i >= 0; i--) {
        if (!inode.snapshots(i).deleted()) {
          latest_snapshot_create_txid_ = inode.snapshots(i).create_txid();
          break;
        }
      }
    }
  }
}

void INodeInPath::BuildChildIIP(INode* child_inode, INodeInPath* out) const {
  out->snapshot_read_info_ = snapshot_read_info_;
  out->latest_snapshot_create_txid_ = latest_snapshot_create_txid_;
  out->snapshot_root_id_ = snapshot_root_id_;
  out->snapshot_references_ = snapshot_references_;
  out->CollectSnapshotInfo(*child_inode);
  out->CollectAttrs(*child_inode);
  out->inode_.Swap(child_inode);
  out->FinalizeAttrs();
}

void INodeInPath::SwitchToChild(const INode& child_inode) {
  CollectSnapshotInfo(child_inode);
  CollectAttrs(child_inode);
  inode_ = child_inode;
  FinalizeAttrs();
  checked_write_ = NOT_CHECKED;
  snapshot_inode_ = nullptr;
}

bool INodeInPath::NeedBackupForDeletion() const {
  if (!snapshot_references_.empty()) {
    return true;
  }
  // corner case: if no such latest_snapshot exists, return false;
  // else if create_txid is not set(created early), return true
  return inode_.id() != snapshot_root_id_ &&
         inode_.create_txid() < latest_snapshot_create_txid_;
}

bool INodeInPath::NeedBackupForModification() const {
  if (!snapshot_references_.empty()) {
    return true;
  }
  // corner case: if no such latest_snapshot exists, return false;
  // else if last_update_txid is not set(created early), return true
  return inode_.id() != snapshot_root_id_ &&
         inode_.last_update_txid() < latest_snapshot_create_txid_;
}

void INodeInPath::SetModificationTime(uint64_t mtime) {
  RecordModification();
  inode_.set_mtime(mtime);
}

void INodeInPath::SetAccessTime(uint64_t atime) {
  RecordModification();
  inode_.set_atime(atime);
}

void INodeInPath::RecordModification() {
  if (checked_write_ != CHECKED_MODIFY) {
    if (NeedBackupForModification()) {
      VLOG(8) << "RecordModification init snapshot inode " << DebugINodeStr();
      snapshot_inode_ = std::make_unique<INode>(inode_);
    }
    checked_write_ = CHECKED_MODIFY;
  }
  if (old_inode_ == nullptr) {
    old_inode_ = std::make_unique<INode>(inode_);
  }
}

void INodeInPath::RecordDeletion() {
  if (checked_write_ != CHECKED_DELETE) {
    // NeedBackupForDeletion is stricter than NeedBackupForModification
    CHECK_EQ(checked_write_, NOT_CHECKED)
        << "should RecordDeletion first " << DebugINodeStr();
    if (NeedBackupForDeletion()) {
      VLOG(8) << "RecordDeletion init snapshot inode " << DebugINodeStr();
      snapshot_inode_ = std::make_unique<INode>(inode_);
      snapshot_inode_->set_delete_txid(kInvalidTxId);
    }
    checked_write_ = CHECKED_DELETE;
  }
  if (old_inode_ == nullptr) {
    old_inode_ = std::make_unique<INode>(inode_);
  }
}

bool INodeInPath::HasSnapshotINode() const {
  return snapshot_inode_ != nullptr;
}

INode* INodeInPath::GenerateSnapshotINode() {
  if (!snapshot_inode_) {
    // no snapshot inode recorded
    return nullptr;
  }

  if (!snapshot_references_.empty()) {
    auto* snapshot_references = snapshot_inode_->mutable_snapshot_references();
    snapshot_references->Clear();
    snapshot_references->Reserve(snapshot_references_.size());
    for (const auto& ref : snapshot_references_) {
      snapshot_references->Add()->CopyFrom(ref);
    }
  }

  return snapshot_inode_.get();
}

void INodeInPath::GenerateSnapshotLog(SnapshotLog* snaplog) {
  if (HasSnapshotINode()) {
    snaplog->mutable_inode()->CopyFrom(*GenerateSnapshotINode());
    snaplog->set_snapshot_root_id(SnapshotRootId());
    CHECK(snaplog->IsInitialized());
  } else {
    CHECK(!snaplog->IsInitialized());
  }
}

INode& INodeInPath::MutableInode() {
  RecordModification();
  return inode_;
}

INode& INodeInPath::MutableInodeUnsafe() {
  return inode_;
}

void INodeInPath::SetIsUnderSnapshot(bool val) {
  snapshot_read_info_.is_under_snapshot_ = val;
}

void INodeInPath::SetIsSnapshotDirectory(bool val) {
  snapshot_read_info_.is_snapshot_directory_ = val;
}

void INodeInPath::SetReadTxid(uint64_t txid) {
  snapshot_read_info_.read_txid_ = txid;
}

INode INodeInPath::OldInode() const {
  if (old_inode_) {
    return *old_inode_;
  } else {
    return inode_;
  }
}

const INode& INodeInPath::Inode() const {
  return inode_;
}

INodeID INodeInPath::SnapshotRootId() const {
  return snapshot_root_id_;
}

const SnapshotReadInfo& INodeInPath::GetSnapshotReadInfo() const {
  return snapshot_read_info_;
}

TxID INodeInPath::ReadTxid() const {
  return snapshot_read_info_.read_txid_;
}

bool INodeInPath::IsUnderSnapshot() const {
  return snapshot_read_info_.is_under_snapshot_;
}

std::string INodeInPath::DebugINodeStr() const {
  return absl::Substitute("$0,$1,$2,$3",
                          inode_.parent_id(),
                          inode_.name(),
                          inode_.id(),
                          inode_.mtime());
}

void INodeInPath::CollectAttrs(const INode& inode) {
  if (!attr_holder_.has_pin_status()) {
    PinStatus* init = attr_holder_.mutable_pin_status();
    init->set_pinned(false);
    init->set_ttl(-1);
    init->set_recursive(false);
    init->set_txid(0);
  }

  UpdateINodeAttrs(attr_holder_, inode);
}

void INodeInPath::FinalizeAttrs() {
  old_inode_ = std::make_unique<INode>(inode_);
  UpdateINodeAttrs(inode_, attr_holder_);
}

}  // namespace dancenn
