//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include <rpc/rpc_server_connection.h>
#include "namespace/user_group_info.h"
#include "rpc/rpc_controller.h"

namespace dancenn {

// TODO see java NameNode.getRemoteUser()
std::shared_ptr<UserGroupInfo> GetRemoteUserInfo(
    ::google::protobuf::RpcController* controller) {
  std::shared_ptr<UserGroupInfo> user_info =
      std::make_shared<UserGroupInfo>();
  if (!controller) {
    return user_info;
  }
  RpcController* c = dynamic_cast<RpcController*>(controller);
  RpcServerConnection* connection = dynamic_cast<RpcServerConnection*>(
      c->rpc_connection().get());
  if (!connection) {
    return user_info;
  }
  std::string user_name;
  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "userName" && b.has_value()) {
      user_name = b.value();
      break;
    }
  }
  if (connection->GetUgi()) {
    if (user_name.empty()) {
      return connection->GetUgi();
    } else {
      // we have user in request header
      *user_info = *connection->GetUgi();
      user_info->set_user(user_name);
    }
  }

  // TODO GetCurrentUser: AccessController.getContext
  // TODO GetLoginUser:
  return user_info;
}

}  // namespace dancenn
