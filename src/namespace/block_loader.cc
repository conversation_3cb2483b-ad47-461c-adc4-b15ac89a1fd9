// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "namespace/block_loader.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <string>
#include <utility>

#include "base/constants.h"
#include "base/logger_metrics.h"
#include "namespace/namespace.h"
#include "ufs/ufs_env.h"

DECLARE_int32(namespace_type);

DECLARE_int32(dfs_load_block_package_size);
DECLARE_int32(dfs_load_block_thread_count);
DECLARE_int32(dfs_load_from_metastorage_threadpool_count);
DECLARE_int32(dfs_load_from_metastorage_thread_count_per_pool);

DECLARE_bool(enable_lease_persistence);
DECLARE_bool(enable_write_back_task_persistence);
DECLARE_double(safemode_threshold_pct);
DECLARE_bool(load_local_block_to_blockmap);
DECLARE_bool(dfs_meta_scan_use_bfs);
DECLARE_bool(disable_dirpolicy_as_workaround_for_kvcache);

namespace dancenn {

BlockLoader::BlockLoader(NameSpace* ns, UfsEnv* ufs_env, bool add_lease)
    : ns_(ns), ufs_env_(ufs_env), add_lease_(add_lease) {
  CHECK(ns_);
  block_count_.store(0, std::memory_order_relaxed);
  thread_pool_ = std::make_unique<cnetpp::concurrency::ThreadPool>(
      "BLoader", true);
  thread_pool_->set_num_threads(
      static_cast<size_t>(FLAGS_dfs_load_block_thread_count));
  thread_pool_->Start();
  LogStatus();
}

void BlockLoader::LoadBlockMap() {
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>> workers;
  for (int i = 0; i < FLAGS_dfs_load_from_metastorage_threadpool_count; ++i) {
    auto tp = std::make_shared<cnetpp::concurrency::ThreadPool>(
        "LoadBM-" + std::to_string(i));
    tp->set_num_threads(FLAGS_dfs_load_from_metastorage_thread_count_per_pool);
    tp->Start();
    workers.emplace_back(tp);
  }

  // load all inodes
  LoadAllINodes(workers);
  ns_->meta_storage_->ConstructLeaseTableDuringStartUp();

  if (!FLAGS_disable_dirpolicy_as_workaround_for_kvcache) {
    cnetpp::concurrency::ThreadPool construct_policy_workers(
        "construct-policy");
    construct_policy_workers.set_num_threads(FLAGS_dfs_load_block_thread_count);
    construct_policy_workers.Start();
    ns_->meta_storage_->ConstructPolicyTableDuringStartUp(
        [&](const INode& inode) {
          construct_policy_workers.AddTask([this, inode]() {
            // inode should be checked has policy
            if (PolicyManager::HasPolicy(inode)) {
              auto full_path = ns_->BuildFullPath(inode.id());
              ns_->policy_manager_->LoadAllPolicy(full_path, inode);
            } else {
              // nn version?
              LOG(ERROR) << "inode has no policy, inode="
                         << inode.ShortDebugString();
            }
            return true;
          });
        });
    construct_policy_workers.Stop(/*wait=*/true);
    CHECK_EQ(construct_policy_workers.PendingCount(), 0);
  }

  if (NameSpace::IsAccMode()) {
    ns_->meta_storage_->ConstructWriteBackTasksDuringStartUp();
  }

  // migrate block infos and load local block infos.
  ns_->meta_storage_->MigrateBlockInfoFromV1ToV2DuringStartUp();
  ns_->meta_storage_->MoveBlockInfosToDeprecatedDuringStartUp();
  if (!FLAGS_load_local_block_to_blockmap && FLAGS_safemode_threshold_pct < 0.001) {
    LOG(INFO) << "Skip LoadLocalBlocks";
  } else {
    ns_->block_manager_->LoadLocalBlocks();
  }

  Finalize();

  for (auto& worker : workers) {
    worker->Stop(true);
  }
}

void BlockLoader::LoadAllINodes(const std::vector<
    std::shared_ptr<cnetpp::concurrency::ThreadPool>>& workers) {
  LOG(INFO) << "Loading inodes from meta storage.";
  std::atomic<int64_t> num_loading_inodes(0);
  std::atomic<int64_t> num_inodes(0);
  std::atomic<int64_t> idx(0);
  bool need_init_parent_index = ns_->num_inodes() < 0;

  // The current function has the following responsibilities:
  // 1. Initialize the parent index, if necessary.
  //    In CFS, this is typically not required unless there is a bug.
  // 2. Load WriteBackTasks for the account namespace.
  // 3. Load replica and read policies into
  //    replica_policy_cache_ and read_policy_cache_, respectively.
  //    In CFS, this is generally not needed.
  // 4. Load leases into the LeaseManager.
  // By persisting lease and WriteBackTask data,
  // we can eliminate the need to scan the entire directory tree.
  if (FLAGS_enable_lease_persistence &&
      (!NameSpace::IsAccMode() || FLAGS_enable_write_back_task_persistence)) {
    // The fsimage_transfer writes the num_inodes value to MetaStorage.
    // The call stack for this process is as follows:
    // NameSpace::NameSpace(const std::string& db_path)
    //   FSImageLoader::Load()
    //     FSImageLoader::DoLoad()
    //       NameSpace::LocalSaveNumINodes(0)
    // So in CFS, the need_init_parent_index is always set to false.
    if (!need_init_parent_index) {
      return;
    }
    // For UT.
    LOG_WITH_LEVEL(ERROR) << "LoadAllINodes runs";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  std::function<bool(const std::string&, const INode&)> cb;
  cb = [this,
        &cb,
        &workers,
        &num_loading_inodes,
        &num_inodes,
        &need_init_parent_index,
        &idx](const std::string& fullpath, const INode& inode) -> bool {
    if (need_init_parent_index) {
      this->ns_->meta_storage_->InsertINodeParentIndexAsync(inode);
      ++num_inodes;
    }
    if (inode.type() == INode_Type_kFile) {
      this->QueueBlock(std::make_shared<INode>(inode), fullpath);
      if (NameSpace::IsAccMode()) {
        CheckAccINode(inode);
        const UfsFileInfoProto& info = inode.ufs_file_info();
        if (info.file_state() == kUfsFileStateToBePersisted) {
          ufs_env_->upload_monitor()->AddTask(
              inode.id(), info.key(), info.upload_id());
        }
      }
    } else if (inode.type() == INode_Type_kDirectory) {
      ++num_loading_inodes;
      workers[idx++ % workers.size()]->AddTask(
          [this, &cb, &num_loading_inodes, fullpath, inode]() {
            if (FLAGS_dfs_meta_scan_use_bfs) {
              this->ns_->meta_storage_->ForEachDir(inode.id(), fullpath, cb);
            } else {
              this->ns_->meta_storage_->ForEachINode(inode.id(), fullpath, cb);
            }
            --num_loading_inodes;
            return true;
          });
    }
    return true;
  };

  if (FLAGS_dfs_meta_scan_use_bfs) {
    ns_->meta_storage_->ForEachDir(kRootINodeId, "/", cb);
  } else {
    ns_->meta_storage_->ForEachINode(kRootINodeId, "/", cb);
  }
  int64_t last = num_loading_inodes;
  while (last != 0) {
    std::this_thread::sleep_for(std::chrono::seconds(1));
    std::string debug;
    for (size_t i = 0; i < workers.size(); ++i) {
      debug += std::to_string(workers[i]->PendingCount()) + " ";
    }
    LOG(INFO) << "Pending load tasks: " << debug;
    int64_t now = num_loading_inodes;
    if (now == last) {
      Flush();
    }
    last = now;
  }
  if (need_init_parent_index) {
    ns_->meta_storage_->WaitNoPending(true);
    ns_->LocalSaveNumINodes(num_inodes);
  }
}

void BlockLoader::LoadFileBlock(std::shared_ptr<INode> inode, bool add_lease) {
  if (inode->has_uc() && inode->blocks_size() == 0 && add_lease) {
    // empty uc file
    ns_->AddLease(*inode);
    return;
  }

  for (int i = 0; i < inode->blocks_size(); ++i) {
    Block block{inode->blocks(i).blockid(),
                static_cast<uint32_t>(inode->blocks(i).numbytes()),
                inode->blocks(i).genstamp()};
    // ns_->block_manager_->SetBlockNum(inode->replication());
    if (inode->has_uc() && (i == inode->blocks_size() - 1)) {
      // ns_->block_manager_->SetBlockNum(inode->replication());
      if (add_lease) {
        ns_->AddLease(*inode);
      }
    }
  }
}

void BlockLoader::QueueBlock(std::shared_ptr<INode> inode,
                             const std::string& fullpath) {
  std::lock_guard<std::mutex> lock(mutex_);
  buffer_.emplace_back(std::move(inode), fullpath);
  if (buffer_.size() >= FLAGS_dfs_load_block_package_size) {
    FlushInternal();
  }
}

void BlockLoader::Flush() {
  std::lock_guard<std::mutex> lock(mutex_);
  if (buffer_.empty()) {
    return;
  }
  FlushInternal();
}

void BlockLoader::Finalize() {
  if (!buffer_.empty()) {
    for (const auto& it : buffer_) {
      LoadFileBlock(it.first, add_lease_);
      block_count_.fetch_add(it.first->blocks_size(),
                             std::memory_order_release);
    }
  }
  LOG(INFO) << "Waiting for block loader to finalize.";
  thread_pool_->Stop(true);
}

void BlockLoader::FlushInternal() {
  auto new_buf = std::make_shared<
      std::vector<std::pair<std::shared_ptr<INode>, std::string>>>();
  std::swap(*new_buf, buffer_);
  thread_pool_->AddTask([this, new_buf]() {
    for (const auto& it : *new_buf) {
      LoadFileBlock(it.first, add_lease_);
      block_count_.fetch_add(it.first->blocks_size(),
                             std::memory_order_release);
    }
    return true;
  });
}

void BlockLoader::LogStatus() {
  auto now = TimeUtil::GetNowEpochMs();
  if (last_log_time_ == 0 || now - last_log_time_ >= 1000) {
    last_log_time_ = now;

    LOG(INFO) << "Loaded " << block_count_.load(std::memory_order_acquire)
              << " blocks. thread pool pending "
              << thread_pool_->PendingCount();
  }
  thread_pool_->AddDelayTask(
      [this]() -> bool {
        LogStatus();
        return true;
      },
      std::chrono::milliseconds(1));
}

}  // namespace dancenn
