#ifndef NAMESPACE_MERGE_BLOCK_LISTENER_H_
#define NAMESPACE_MERGE_BLOCK_LISTENER_H_

#include <chrono>
#include <set>
#include <string>

#include "namespace/meta_scanner.h"

namespace dancenn {

class NameSpace;

class MergeBlockListener : public MetaScanner::Listener {
 public:
  explicit MergeBlockListener(NameSpace* ns)
      : MetaScanner::Listener(desc()), ns_(ns) {
  }

  bool PreScan() override;
  std::vector<INodeID> ScanIndexes() override;
  bool Handle(const std::string& full_path, const INode& inode) override;
  void PostScan() override;

  void AddOngoingTask(uint64_t inode_id);

 private:
  static const std::string desc() {
    return "merge_block_listener";
  }

  NameSpace* ns_{nullptr};
  std::chrono::steady_clock::time_point scan_start_;
  std::mutex mut_;
  std::set<uint64_t> merging_inodes_;
  int64_t processed_{0};
};

}  // namespace dancenn

#endif /* NAMESPACE_MERGE_BLOCK_LISTENER_H_ */
