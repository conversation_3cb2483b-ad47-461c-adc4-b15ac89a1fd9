// Copyright 2017 Li<PERSON> Lei <<EMAIL>>

#ifndef NAMESPACE_BLOCK_LOADER_H_
#define NAMESPACE_BLOCK_LOADER_H_

#include <cnetpp/concurrency/thread_pool.h>

#include <functional>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "inode.pb.h"  // NOLINT(build/include)

namespace dancenn {

class NameSpace;
class UfsEnv;
class MetaStorage;

class BlockLoader {
 public:
  BlockLoader(NameSpace* ns, UfsEnv* ufs_env, bool add_lease);
  ~BlockLoader() = default;

  void LoadBlockMap();

  void QueueBlock(std::shared_ptr<INode> inode, const std::string& fullpath);
  void Finalize();

 private:
  void LoadFileBlock(std::shared_ptr<INode> inode, bool add_lease);

  void DeleteDanglingINodes(const std::vector<
      std::shared_ptr<cnetpp::concurrency::ThreadPool>>& workers);

  void LoadAllINodes(const std::vector<
      std::shared_ptr<cnetpp::concurrency::ThreadPool>>& workers);

  void Flush();
  void FlushInternal();

  void LogStatus();

  NameSpace* ns_{nullptr};
  UfsEnv* ufs_env_{nullptr};
  std::unique_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
  std::mutex mutex_;
  std::vector<std::pair<std::shared_ptr<INode>, std::string>> buffer_;
  std::atomic<uint64_t> block_count_;
  bool add_lease_;

  uint64_t last_log_time_{0};
};

}  // namespace dancenn

#endif  // NAMESPACE_BLOCK_LOADER_H_
