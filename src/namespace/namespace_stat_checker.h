// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/17
// Description

#ifndef NAMESPACE_NAMESPACE_STAT_CHECKER_H_
#define NAMESPACE_NAMESPACE_STAT_CHECKER_H_

#include <cnetpp/concurrency/thread.h>  // For Thread.

#include <atomic>              // For atomic.
#include <condition_variable>  // For condition_variable.
#include <memory>              // For shared_ptr, unique_ptr.
#include <mutex>               // For mutex, once_flag.
#include <nlohmann/json.hpp>   // For json.
#include <string>              // For string.

#include "base/status.h"             // For Status.
#include "namespace/inode.h"         // For INode.
#include "namespace/meta_storage.h"  // For MetaStorage.
#include "namespace/namespace_scrub_inodestat.h"  // For INodeStat.

namespace dancenn {

class CheckINodeStatOp {
 public:
  CheckINodeStatOp(std::shared_ptr<MetaStorage> meta_storage,
                   MetaStorageSnapPtr snapshot,
                   MetaStorageIterPtr namespace_info_iter,
                   MetaStorageIterPtr inode_iter,
                   MetaStorageIterPtr inode_stat_iter,
                   const std::string& start_from,
                   // Only for tool namespace_stat_checker.
                   bool enable_debug_msg = false);

  Status Check();
  int ReadINodeCnt();
  std::string GetNext();
  std::string GetDebugMsg();

 private:
  bool AccumulateSubINodeStat(const INode& inode);

 private:
  std::shared_ptr<MetaStorage> meta_storage_;
  MetaStorageSnapPtr snapshot_;
  MetaStorageIterPtr namespace_info_iter_;
  MetaStorageIterPtr inode_iter_;
  MetaStorageIterPtr inode_stat_iter_;
  INodeStat computed_stat_;
  // How many INodes has been read during checking procedure?
  int read_inode_cnt_;
  // Only for tool namespace_stat_checker.
  bool enable_debug_msg_;
  nlohmann::json debug_msg_;
};

class INodeStatChecker {
 public:
  explicit INodeStatChecker(std::shared_ptr<MetaStorage> meta_storage);
  ~INodeStatChecker();

  void Start();
  void Stop();
  bool CheckOnce();

 private:
  std::shared_ptr<MetaStorage> meta_storage_;
  MetaStorageSnapHolderPtr snapshot_holder_;
  std::string start_from_;

  std::mutex mu_;
  std::condition_variable cond_;
  std::atomic<bool> is_running_;
  std::unique_ptr<cnetpp::concurrency::Thread> thread_;
};

}  // namespace dancenn

#endif  // NAMESPACE_NAMESPACE_STAT_CHECKER_H_
