#include <rocksdb/write_batch_base.h>

#include "namespace/write_batch_merge_handler.h"
#include "namespace/meta_storage.h"

namespace dancenn {
  namespace meta_storage {
    WriteBatchMergeHandler::WriteBatchMergeHandler(
        MetaStorage* ms,
        std::shared_ptr<rocksdb::WriteBatchBase> merged_batch)
        : ms_(ms), merged_batch_(merged_batch) {
      CHECK_NOTNULL(ms_);
      CHECK_NOTNULL(merged_batch_);
    }

    rocksdb::Status WriteBatchMergeHandler::PutCF(
        uint32_t cf_id, const rocksdb::Slice& key,
        const rocksdb::Slice& value) {
      merged_batch_->Put(ms_->cf_handle_map_[cf_id], key, value);
      return rocksdb::Status::OK();
    }

    rocksdb::Status WriteBatchMergeHandler::DeleteCF(
        uint32_t cf_id, const rocksdb::Slice& key) {
      merged_batch_->Delete(ms_->cf_handle_map_[cf_id], key);
      return rocksdb::Status::OK();
    }
    rocksdb::Status WriteBatchMergeHandler::MergeCF(
      uint32_t cf_id, const rocksdb::Slice& key, const rocksdb::Slice& value) {
      merged_batch_->Merge(ms_->cf_handle_map_[cf_id], key, value);
      return rocksdb::Status::OK();
    }
  }
}
