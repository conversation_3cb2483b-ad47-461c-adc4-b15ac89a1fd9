#ifndef _LIFECYCLE_POLICY_UTIL_H_
#define _LIFECYCLE_POLICY_UTIL_H_

#include <set>

#include <absl/strings/str_format.h>
#include <rocksdb/merge_operator.h>

#include "block_manager/block.h"
#include "namespace/inode.h"
#include "namespace/meta_storage.h"
#include "namespace/xattr.h"
#include "DatanodeProtocol.pb.h"
#include "lifecycle.pb.h"

namespace dancenn {

using cloudfs::StorageClassProto;
using cloudfs::LifecyclePolicyProto;

extern StorageClassProto default_storage_class;
extern LifecyclePolicyProto default_lifecycle_policy;

static std::map<std::string, StorageClassProto> storage_class_name_id = {
  { "NONE", StorageClassProto::NONE },
  { "HOT",  StorageClassProto::HOT },
  { "WARM", StorageClassProto::WARM },
  { "COLD", StorageClassProto::COLD },
  { "IA",   StorageClassProto::IA },
  { "AR",   StorageClassProto::AR }
};

static std::map<StorageClassProto, std::string> storage_class_id_name= {
  { StorageClassProto::NONE, "NONE" },
  { StorageClassProto::HOT,  "HOT" },
  { StorageClassProto::WARM, "WARM" },
  { StorageClassProto::COLD, "COLD" },
  { StorageClassProto::IA,   "IA" },
  { StorageClassProto::AR,   "AR"}
};

inline bool StorageClassName2ID(std::string name, StorageClassProto *cls) {
  std::transform(name.begin(), name.end(), name.begin(),
      [](unsigned char c){ return std::toupper(c); });
  auto iter = storage_class_name_id.find(name);
  if (iter == storage_class_name_id.end()) {
    return false;
  }
  *cls = iter->second;
  return true;
}

inline bool StorageClassID2Name(StorageClassProto cls, std::string* name) {
  auto iter = storage_class_id_name.find(cls);
  if (iter == storage_class_id_name.end()) {
    return false;
  }
  *name = iter->second;
  return true;
}

void GetEffectiveStorageClass(const LifecyclePolicyProto& policy,
                              int64_t delta_ms,
                              StorageClassProto* cls);

int GetEffectiveLifecyclePolicy(MetaStorage* ms,
                                INodeID id,
                                std::function<bool(LifecyclePolicyProto*)> filter,
                                LifecyclePolicyProto* policy,
                                INodeID* effective_inode_id = nullptr);

bool IsExpRuleValid(const cloudfs::ExpirationRuleProto& policy);

void GetCurrentStorageClass(MetaStorage* ms,
                            INodeID id,
                            StorageClassProto* cls);

bool LifecyclePolicyIsValid(const LifecyclePolicyProto& policy);
bool LifecycleNeedExpire(const LifecyclePolicyProto& policy,
                         int64_t delta_ms);


using cloudfs::datanode::BlockIdCommandProto;
using cloudfs::datanode::BlockIdCommandProto_Action_SET_STORAGE_CLASS_HOT;
using cloudfs::datanode::BlockIdCommandProto_Action_SET_STORAGE_CLASS_WARM;
using cloudfs::datanode::BlockIdCommandProto_Action_SET_STORAGE_CLASS_COLD;

// generate command to DN
inline BlockIdCommandProto ConstructBlockIdCmd(
    const StorageClassProto cls,
    const std::string& bpid,
    const std::set<BlockID>& blk_ids) {
  BlockIdCommandProto cmd;
  switch (cls) {
    case StorageClassProto::HOT: {
      cmd.set_action(BlockIdCommandProto_Action_SET_STORAGE_CLASS_HOT);
      break;
    }
    case StorageClassProto::WARM: {
      cmd.set_action(BlockIdCommandProto_Action_SET_STORAGE_CLASS_WARM);
      break;
    }
    case StorageClassProto::COLD: {
      cmd.set_action(BlockIdCommandProto_Action_SET_STORAGE_CLASS_COLD);
      break;
    }
    default: {
      LOG(FATAL) << absl::StrFormat("unexpected storage class type %d", cls);
    }
  }
  cmd.set_blockpoolid(bpid);
  for (const auto& id : blk_ids) {
    cmd.add_blockids(id);
  }
  return cmd;
}

class StorageClassReportMergeOperator : public rocksdb::AssociativeMergeOperator {
public:
  virtual bool Merge(const rocksdb::Slice& key,
                     const rocksdb::Slice* existing_value,
                     const rocksdb::Slice& value,
                     std::string* new_value,
                     rocksdb::Logger* logger) const override;
  static const char* kClassName() { return "StorageClassReportMergeOperator"; }
  virtual const char* Name() const override { return kClassName(); }
};

} // namespace dancenn

#endif  // _LIFECYCLE_POLICY_UTIL_H_
