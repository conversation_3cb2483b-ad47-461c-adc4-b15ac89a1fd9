// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "namespace/meta_scanner.h"

#include <glog/logging.h>
#include <gflags/gflags.h>

#include <chrono>
#include <memory>
#include <string>

#include "base/path_util.h"
#include "block_manager/block.h"
#include "namespace/namespace.h"

DECLARE_bool(dfs_meta_scan_use_bfs);
DECLARE_int32(dfs_meta_scan_interval_sec);
DECLARE_int32(dfs_meta_scan_max_iter_count_per_sec);
DECLARE_bool(dfs_meta_scan_enable);

namespace dancenn {

void MetaScanner::Start() {
  if (worker_) {
    worker_->Stop();
  }
  worker_.reset(new cnetpp::concurrency::Thread(
      std::make_shared<Task>(this), "MScanner"));
  worker_->Start();
}
void MetaScanner::Stop() {
  if (worker_) {
    worker_->Stop();
    worker_.reset();
  }
  task_.reset();
}

void MetaScanner::RegisterListener(const std::shared_ptr<Listener> &listener) {
  std::lock_guard<std::mutex> guard(mutex_);
  listeners_.emplace(listener->name(), listener);
}

void MetaScanner::DeregisterListener(
    const std::shared_ptr<Listener> &listener) {
  std::lock_guard<std::mutex> guard(mutex_);
  listeners_.erase(listener->name());
}

void MetaScanner::Task::Stop() {
  std::lock_guard<std::mutex> guard(mutex_);
  cnetpp::concurrency::Task::Stop();
  cond_.notify_all();
}

bool MetaScanner::Task::operator()(void* arg) {
  (void) arg;
  std::unique_lock<std::mutex> guard(mutex_);
  while (!IsStopped()) {
    if (FLAGS_dfs_meta_scan_enable) {
      guard.unlock();
      scanner_->Scan(this);
      guard.lock();
    } else {
      LOG(INFO) << "meta scanner disabled";
    }
    cond_.wait_for(guard,
                   std::chrono::seconds(FLAGS_dfs_meta_scan_interval_sec),
                   [&] () {
                     return IsStopped();
                   });
  }
  return true;
}

void MetaScanner::Scan(Task* task) {
  decltype(listeners_) listeners_snapshot;
  {
    std::lock_guard<std::mutex> guard(mutex_);
    listeners_snapshot = listeners_;
  }
  std::map<std::shared_ptr<Listener>, std::vector<INodeID>> scan_snapshot;
  {
    for (auto& itr : listeners_snapshot) {
      if (itr.second->PreScan()) {
        scan_snapshot.insert({itr.second, itr.second->ScanIndexes()});
      }
    }
  }

  if (scan_snapshot.empty()) {
    LOG(INFO) << "No meta scanner listener is currently active, skip scan.";
    return;
  }

  std::chrono::microseconds interval(100);
  std::chrono::minutes info_log_interval(30);
  auto begin_ts = std::chrono::steady_clock::now();
  auto last_ts  = begin_ts;
  auto info_log_last_ts = begin_ts;

  uint64_t scans = 0;
  int32_t count  = 0;
  int32_t max_count_per_interval
      = FLAGS_dfs_meta_scan_max_iter_count_per_sec / 10;

  LOG(INFO) << "Begin MScan max_count_per_interval = " << max_count_per_interval
            << ", meta_scan_interval_sec = " << FLAGS_dfs_meta_scan_interval_sec;

  std::shared_ptr<Listener> cur_listener;
  INodeID cur_inode_id;
  uint64_t cache_inodes = 0;
  std::string max_path;
  int depth = 0, max_depth = 0;

  std::function<bool(const std::string&, const INode&)> cb;
  cb = [&](const std::string& full_path, const INode& inode) {
    if (task->IsStopped()) {
      return false;
    } else {
      std::string normalized_path;
      if (!NormalizePath(full_path, "", &normalized_path)) {
        LOG(ERROR) << "Failed to normalize UserBin path " << full_path;
        return false;
      }

      if (!cur_listener->Handle(
            inode.id() == kRootINodeId ? "/" : normalized_path, inode)) {
        LOG(INFO) << "MetaScanner procedure issued by " << cur_listener->name()
                  << " on inode " << cur_inode_id
                  << " skipped during Handle() inode " << inode.id()
                  << ", path " << normalized_path;
        return false;
      }

      if (inode.type() == INode_Type_kDirectory) {
        if (++count > max_count_per_interval) {
          auto now = std::chrono::steady_clock::now();
          if (now < interval + last_ts) {
            last_ts += interval;
            std::this_thread::sleep_for(last_ts - now);
          } else {
            last_ts = now;
          }

          scans += count;
          if (now >= info_log_interval + info_log_last_ts) {
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - begin_ts);
            LOG(INFO) << "During scan: directory scans = " << scans
                      << ", cache_inodes = " << cache_inodes
                      << ", cast = " << duration.count() << "s";
            info_log_last_ts = now;
          }

          count = 0;
        }
        depth = depth + 1;
        if (depth > max_depth) {
          max_depth = depth;
          max_path  = normalized_path;
        }
        bool r = false;
        if (FLAGS_dfs_meta_scan_use_bfs) {
          r = ns_->meta_storage_->ForEachDir(
              inode.id(), normalized_path, &cache_inodes, cb);
        } else {
          r = ns_->meta_storage_->ForEachINode(inode.id(), normalized_path, cb);
        }
        depth = depth - 1;
        return r;
      }
    }
    return true;
  };

  depth = 1;
  max_depth = 1;
  max_path = "/";

  for (auto& itr : scan_snapshot) {
    // iterate each listener
    cur_listener = itr.first;
    auto& inode_ids = itr.second;

    for (auto id : inode_ids) {
      // iterate each INodeID that current listener indexed
      cur_inode_id = id;

      LOG(INFO) << "Now start listener " << cur_listener->name()
                << " on INodeID " << id;
      do {
        auto fullpath = ns_->BuildFullPath(id, nullptr);
        if (fullpath.empty()) {
          LOG(INFO) << "Skip traversing on INodeID " << id
                    << " since failed to build fullpath.";
          break;
        }

        INode inode;
        auto code = ns_->meta_storage_->GetINode(id, &inode, nullptr);
        if (code != StatusCode::kOK) {
          LOG(INFO) << "Skip traversing on INodeID " << id
                    << " since GetINode() return " << code;
          break;
        }
        if (!cb(fullpath, inode)) {
          break;
        }
      } while (0);

      if (task->IsStopped()) {
        LOG(INFO) << "MetaScanner is interrupted on Listener "
                  << cur_listener->name() << " with INodeID " << id;
        break;
      }
    }

    if (task->IsStopped()) {
      LOG(INFO) << "MetaScanner is interrupted on Listener "
                << cur_listener->name();
      break;
    }
  }

  LOG(INFO) << "Max depth: " << max_depth << ", Max path: " << max_path;
  for (auto& listener : listeners_snapshot) {
    listener.second->PostScan();
  }
}

}  // namespace dancenn
