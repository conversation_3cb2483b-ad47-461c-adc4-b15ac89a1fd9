#ifndef NAMESPACE_INODE_ATTR_TTL_MANAGER_METRICS_H_
#define NAMESPACE_INODE_ATTR_TTL_MANAGER_METRICS_H_

#include "base/metric.h"
#include "base/metrics.h"

#include <memory>

namespace dancenn {

struct INodeAttrTtlMetrics {
  INodeAttrTtlMetrics();
  ~INodeAttrTtlMetrics() = default;

  INodeAttrTtlMetrics(const INodeAttrTtlMetrics&) = delete;
  INodeAttrTtlMetrics& operator=(const INodeAttrTtlMetrics&) = delete;

  std::shared_ptr<Gauge> tasks;
  std::atomic<uint64_t> task_num;
};

} // namespace dancenn

#endif
