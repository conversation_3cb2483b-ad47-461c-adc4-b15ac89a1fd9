#include "namespace/inode_attr_ttl_manager.h"

#include "namespace/namespace.h"

DECLARE_int64(inode_attr_ttl_scan_interval_us);
DECLARE_uint32(inode_attr_ttl_throttle_interval_ms);

DECLARE_bool(run_ut);

namespace dancenn {

INodeAttrTtlScanner::INodeAttrTtlScanner(NameSpace* ns)
    : ns_(ns), start_time_(0), end_time_(0), inode_cnt_(0) {
  CHECK_NOTNULL(ns_);
  VLOG(10) << "INodeAttrTtlScanner::INodeAttrTtlScanner()";
}

uint32_t INodeAttrTtlScanner::GetCfIdx() {
  return kINodeAttrTtlCFIndex;
}

bool INodeAttrTtlScanner::PreScan() {
  start_time_ = TimeUtilV2::GetNowEpochMs();
  end_time_ = 0;
  inode_cnt_ = 0;

  if (ns_->ha_state()->IsActive() == false) {
    LOG(INFO) << "Do not run attr ttl task on standby";
    return false;
  }

  return true;
}

bool INodeAttrTtlScanner::Handle(MetaStorageSnapPtr snapshot,
                                 const rocksdb::Slice& key,
                                 const rocksdb::Slice& value) {
  (void)snapshot;

  uint64_t ttl = -1;
  INodeID id = kInvalidINodeId;
  meta_storage_->DecodeStoreKey(
      cnetpp::base::StringPiece(key.data(), key.size()), &ttl, nullptr, &id);

  auto now = TimeUtilV2::GetNowEpochMs();
  if (now < ttl) {
    VLOG(8) << "Ttl is " << ttl << ", now is " << now << ", abort";
    return false;
  }

  INode inode;
  if (!inode.ParseFromArray(value.data(), value.size())) {
    LOG(FATAL) << "Failed to parse inode from meta storage key: " << key.data();
  }

  int tried = 0;
  while (!meta_scanner_->AddWorkTask(
      std::make_shared<INodeAttrTtlTask>(ns_, inode))) {
    LOG(INFO) << "Failed to add work task " << inode.id();
    std::this_thread::sleep_for(std::chrono::milliseconds(
        FLAGS_inode_attr_ttl_throttle_interval_ms));
    if (tried++ > 60) {
      LOG(WARNING) << "Failed to add work task " << inode.id();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return false;
    }
  }

  return true;
}

bool INodeAttrTtlScanner::PostScan() {
  end_time_ = TimeUtilV2::GetNowEpochMs();
  LOG(INFO) << "INodeAttrTtlScanner finished. Total inode scanned: "
            << inode_cnt_ << ", total time cost: " << (end_time_ - start_time_)
            << "ms";
  return true;
}

int64_t INodeAttrTtlScanner::GetDelayUs() {
  if (start_time_ > 0 && end_time_ > 0) {
    auto cost = static_cast<int64_t>((end_time_ - start_time_) * 1000);
    if (cost < FLAGS_inode_attr_ttl_scan_interval_us) {
      return FLAGS_inode_attr_ttl_scan_interval_us - cost;
    }
    return 0;
  }
  return FLAGS_inode_attr_ttl_scan_interval_us;
}

std::string INodeAttrTtlScanner::ToString() {
  return "INodeAttrTtlScanner";
}

INodeAttrTtlTask::INodeAttrTtlTask(NameSpace* ns,
                                   const INode& inode)
    : ns_(ns), inode_(inode) {
  CHECK_NOTNULL(ns_);
}

bool INodeAttrTtlTask::Run(void* arg) {
  ns_->ReconcileINodeAttrs(inode_);
  return true;
}

INode INodeAttrTtlTask::GetINode() {
  CHECK(FLAGS_run_ut);
  return inode_;
}

uint32_t INodeAttrTtlStat::GetCfIdx() {
  return kINodeAttrTtlCFIndex;
}

bool INodeAttrTtlStat::PreScan() {
  inode_cnt_ = 0;
  return true;
}

bool INodeAttrTtlStat::Handle(MetaStorageSnapPtr snapshot,
                           const rocksdb::Slice& key,
                           const rocksdb::Slice& value) {
  (void)snapshot;
  (void)key;
  (void)value;

  inode_cnt_++;
  return true;
}

bool INodeAttrTtlStat::PostScan() {
  metrics_.task_num = inode_cnt_;
  return true;
}

int64_t INodeAttrTtlStat::GetDelayUs() {
  return FLAGS_inode_attr_ttl_scan_interval_us;
}

std::string INodeAttrTtlStat::ToString() {
  return "INodeAttrStat";
}

uint64_t INodeAttrTtlStat::GetTaskCnt() {
  CHECK(FLAGS_run_ut);
  return metrics_.task_num;
}

}  // namespace dancenn
