// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef NAMESPACE_ROCKSDB_LISTENER_H_
#define NAMESPACE_ROCKSDB_LISTENER_H_

#include <rocksdb/listener.h>

#include <chrono>
#include <mutex>

#include "base/metric.h"
#include "base/metrics.h"

namespace dancenn {

class RocksDBListener : public rocksdb::EventListener {
 public:
  explicit RocksDBListener(std::shared_ptr<Metrics> metrics);
  virtual ~RocksDBListener() = default;

  void OnFlushCompleted(rocksdb::DB* db,
                        const rocksdb::FlushJobInfo& flush_job_info) override;

  void OnFlushBegin(rocksdb::DB* db,
                    const rocksdb::FlushJobInfo& flush_job_info) override;

  void OnTableFileDeleted(const rocksdb::TableFileDeletionInfo& info) override {
    (void) info;
  }

  void OnCompactionCompleted(rocksdb::DB* db,
                             const rocksdb::CompactionJobInfo& ci) override;

  void OnTableFileCreated(
      const rocksdb::TableFileCreationInfo& info) override {
    (void) info;
  }

  void OnTableFileCreationStarted(
      const rocksdb::TableFileCreationBriefInfo& info) override {
    (void) info;
  }

  void OnMemTableSealed(const rocksdb::MemTableInfo& info) override {
    (void) info;
  }

 private:
  std::mutex mu_;
  std::unordered_map<int, std::chrono::steady_clock::time_point> flush_jobs_;

  MetricID write_slowdown_count_;
  MetricID write_stop_count_;
  MetricID flush_count_;
  MetricID flush_time_us_;
  MetricID compaction_count_;
  MetricID compaction_time_us_;
};

}  // namespace dancenn

#endif  // NAMESPACE_ROCKSDB_LISTENER_H_
