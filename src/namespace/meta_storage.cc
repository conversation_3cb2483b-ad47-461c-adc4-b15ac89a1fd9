//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include "namespace/meta_storage.h"

#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>
#include <rocksdb/filter_policy.h>
#include <rocksdb/iostats_context.h>
#include <rocksdb/iterator.h>
#include <rocksdb/perf_context.h>
#include <rocksdb/slice_transform.h>
#include <rocksdb/status.h>
#include <rocksdb/table.h>
#include <rocksdb/utilities/checkpoint.h>
#include <rocksdb/utilities/sim_cache.h>
#include <rocksdb/write_batch.h>
#include <rocksdb/utilities/write_batch_with_index.h>

#include <exception>
#include <iomanip>
#include <memory>
#include <shared_mutex>
#include <unordered_map>
#include <utility>

#include "base/constants.h"
#include "base/defer.h"
#include "base/logger_metrics.h"
#include "base/pb_converter.h"
#include "base/platform.h"
#include "base/stop_watch.h"
#include "base/string_utils.h"
#include "base/to_json_string.h"
#include "meta_storage_constants.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/meta_storage_writer.h"
#include "namespace/namespace_scrub_lifecycle_scan.h"
#include "namespace/namespace_stat.h"
#include "namespace/policy_manager.h"
#include "namespace/rocksdb_listener.h"
#include "namespace/user_group_info.h"
#include "namespace/write_batch_merge_handler.h"
#include "namespace/namespace_stat.h"
#include "namespace/namespace_scrub_lifecycle_scan.h"
#include "snapshot/snapshot_manager.h"

DECLARE_int32(dfs_load_from_metastorage_threadpool_count);
DECLARE_int32(dfs_load_from_metastorage_thread_count_per_pool);
DECLARE_int64(dfs_meta_storage_rocksdb_max_bytes_for_level_base);
DECLARE_int32(dfs_meta_storage_rocksdb_max_write_buffer_number);
DECLARE_int32(dfs_meta_storage_rocksdb_write_buffer_size_mb);
DECLARE_int32(dfs_meta_storage_rocksdb_level0_stop_writes_trigger);
DECLARE_int32(dfs_meta_storage_rocksdb_min_write_buffer_number_to_merge);
DECLARE_int32(dfs_meta_storage_rocksdb_parallelism);
DECLARE_int32(dfs_meta_storage_rocksdb_max_background_flushes);
DECLARE_int32(dfs_meta_storage_rocksdb_max_background_compactions);
DECLARE_int32(dfs_meta_storage_rocksdb_max_background_jobs);
DECLARE_int32(dfs_meta_storage_rocksdb_max_total_wal_size_mb);
DECLARE_bool(dfs_meta_storage_rocksdb_use_direct_reads);
DECLARE_bool(dfs_meta_storage_rocksdb_use_direct_io_for_flush_and_compaction);
DECLARE_bool(dfs_meta_storage_rocksdb_write_sync);
DECLARE_int32(dfs_meta_storage_parent_map_bucket_count);
DECLARE_int32(dfs_meta_storage_max_batch_write_item_num);
DECLARE_int32(dfs_meta_storage_rocksdb_compression_type);
DECLARE_int32(dfs_meta_storage_rocksdb_level0_file_num_compaction_triger);
DECLARE_int32(dfs_meta_storage_rocksdb_compression_level);
DECLARE_int32(dfs_meta_storage_rocksdb_num_levels);
DECLARE_bool(dfs_meta_storage_rocksdb_inplace_update);

DECLARE_int32(dfs_meta_storage_rocksdb_block_cache_capacity_mb);
DECLARE_int32(dfs_meta_storage_rocksdb_block_cache_sim_capacity_mb);
DECLARE_int32(dfs_meta_storage_rocksdb_block_cache_sim_shard_bits);
DECLARE_bool(dfs_meta_storage_bloom_filter_use_block_based_builder_enabled);

DECLARE_int32(dfs_meta_storage_rocksdb_perf_threshold_ms);
DECLARE_bool(dfs_meta_storage_rocksdb_perf_enabled);
DECLARE_bool(dfs_meta_storage_rocksdb_perf_scr_enabled);
DECLARE_int32(dfs_meta_storage_rocksdb_perf_scr_level);

DECLARE_int32(dfs_meta_storage_single_writer_spin_wait_times);
DECLARE_bool(dfs_meta_storage_verify_before_commit);

DECLARE_int32(bg_auto_compact_interval_in_min);
DECLARE_int32(bg_auto_compact_all_deletion_threshold);
DECLARE_int32(bg_auto_compact_all_forbid);
DECLARE_int32(bg_auto_compact_all_enable);
DECLARE_int32(bg_auto_compact_deletion_enable);

DECLARE_int32(dfs_meta_storage_slows);
DECLARE_int32(dfs_meta_storage_slows_mailbox);

DECLARE_int32(delete_block_on_pufs_timeout_s);
DECLARE_int32(scan_deprecating_block_max_size);
DECLARE_int32(scan_deprecated_block_max_size);

DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_int32(dfs_ls_limit);
DECLARE_int32(snapshot_reference_num_threshold);
DECLARE_bool(enable_snapshot_feature);

DECLARE_bool(force_rebuild_lease_db);
DECLARE_bool(force_rebuild_policy_db);
DECLARE_bool(need_build_policy_db_first_time);
DECLARE_bool(force_rebuild_write_back_task_db);
DECLARE_int32(write_back_task_v2_rocksdb_num_levels);
DECLARE_uint64(dfs_meta_storage_update_scr_batch_size);

DECLARE_bool(run_ut);

DECLARE_bool(dfs_meta_storage_rocksdb_use_unified_block_cache);
DECLARE_bool(dfs_meta_storage_rocksdb_block_cache_for_index_filters);
DECLARE_double(dfs_meta_storage_rocksdb_block_cache_lru_priority_ratio);
DECLARE_bool(dfs_meta_storage_rocksdb_block_cache_pin_l0);

DECLARE_bool(drop_storage_class_report);

namespace dancenn {

enum class INodeIndexType : int8_t {
  kParentIndex = 0,
  kUnderConstructionIndex = 1
};

#define ROCKSDB_PERF_START(NAME)                                          \
  auto perf_enabled##NAME = FLAGS_dfs_meta_storage_rocksdb_perf_enabled;  \
  StopWatch sw_##NAME;                                                    \
  if (perf_enabled##NAME) {                                               \
    rocksdb::SetPerfLevel(rocksdb::PerfLevel::kEnableTimeExceptForMutex); \
    sw_##NAME.Start();                                                    \
    rocksdb::get_perf_context()->Reset();                                 \
    rocksdb::get_iostats_context()->Reset();                              \
  }

#define ROCKSDB_PERF_END(NAME, KEY_PREFIX)                                   \
  if (perf_enabled##NAME) {                                                  \
    if (std::chrono::duration_cast<std::chrono::milliseconds>(               \
            sw_##NAME.GetTime())                                             \
            .count() >= FLAGS_dfs_meta_storage_rocksdb_perf_threshold_ms) {  \
      LOG(WARNING) << #NAME << " perf stats, perf_context: "                 \
                   << rocksdb::get_perf_context()->ToString(true) << ";io: " \
                   << rocksdb::get_iostats_context()->ToString(true)         \
                   << "key: " << KEY_PREFIX << " time(ms): "                 \
                   << std::chrono::duration_cast<std::chrono::milliseconds>( \
                          sw_##NAME.GetTime())                               \
                          .count();                                          \
    }                                                                        \
    rocksdb::get_perf_context()->Reset();                                    \
    rocksdb::get_iostats_context()->Reset();                                 \
    rocksdb::SetPerfLevel(rocksdb::PerfLevel::kDisable);                     \
  }

#define ROCKSDB_PERF_PCIOC_START(CFNAME)                                      \
  auto _perf_enabled_##CFNAME =                                               \
      FLAGS_dfs_meta_storage_rocksdb_perf_##CFNAME##_enabled;                 \
  if (_perf_enabled_##CFNAME) {                                               \
    rocksdb::SetPerfLevel((rocksdb::PerfLevel)                                \
        FLAGS_dfs_meta_storage_rocksdb_perf_##CFNAME##_level);                \
    rocksdb::get_perf_context()->Reset();                                     \
    rocksdb::get_iostats_context()->Reset();                                  \
  }

#define ROCKSDB_PERF_PCIOC_END(CFNAME)                                        \
  if (_perf_enabled_##CFNAME) {                                               \
    SubmitPCIOCMetrics();                                                     \
    rocksdb::get_perf_context()->Reset();                                     \
    rocksdb::get_iostats_context()->Reset();                                  \
    rocksdb::SetPerfLevel(rocksdb::PerfLevel::kDisable);                      \
  }


INodeStatItersHolder::INodeStatItersHolder(
    MetaStorageIterPtr namesystem_info_iter,
    MetaStorageIterPtr inode_iter,
    MetaStorageIterPtr inode_stat_iter)
    : namesystem_info_iter_(namesystem_info_iter),
      inode_iter_(inode_iter),
      inode_stat_iter_(inode_stat_iter) {
  CHECK_NOTNULL(namesystem_info_iter);
  CHECK_NOTNULL(inode_iter);
  CHECK_NOTNULL(inode_stat_iter);
}

MetaStorageIterPtr INodeStatItersHolder::NameSystemInfoIter() const {
  return namesystem_info_iter_.get();
}

MetaStorageIterPtr INodeStatItersHolder::INodeIter() const {
  return inode_iter_.get();
}

MetaStorageIterPtr INodeStatItersHolder::INodeStatIter() const {
  return inode_stat_iter_.get();
}

MetaStorageSnapHolder::MetaStorageSnapHolder(
    MetaStorage* ms,
    MetaStorageSnapPtr snap)
    : meta_storage_(ms),
      snapshot_(snap) {
  CHECK_NOTNULL(ms);
  CHECK_NOTNULL(snap);
}

MetaStorageSnapHolder::~MetaStorageSnapHolder() {
  if (meta_storage_ != nullptr) {
    meta_storage_->ReleaseSnapshot(snapshot_);
  }
}

MetaStorageSnapPtr MetaStorageSnapHolder::snapshot() {
  return snapshot_;
}

void SnapshotINodeKey::Swap(SnapshotINodeKey* other) {
  if (other != this) {
    std::swap(parent_id, other->parent_id);
    std::swap(name, other->name);
    std::swap(inode_id, other->inode_id);
    std::swap(last_update_txid, other->last_update_txid);
    std::swap(snapshot_root_id, other->snapshot_root_id);
  }
}

bool SnapshotINodeKey::CoincideAtDirTree(const SnapshotINodeKey& key) const {
  return parent_id == key.parent_id && name == key.name &&
         inode_id == key.inode_id;
}

std::string SnapshotINodeKey::ToString() const {
  return absl::Substitute("$0,$1,$2", inode_id, name, last_update_txid);
}

bool SnapshotMergeOperator::Merge(const rocksdb::Slice& key,
                                  const rocksdb::Slice* value_old,
                                  const rocksdb::Slice& value_new,
                                  std::string* value_res,
                                  rocksdb::Logger* logger) const {
  *value_res = value_old ? value_old->ToString() : value_new.ToString();
  return true;
}

MetaStorage::MetaStorage(const std::string& db_path)
    : db_path_(db_path),
      bottom_compact_(true),
      bg_compact_in_progress_(false),
      metrics_(this) {
  CHECK(!db_path_.empty()) << "Empty db_path!";
}

MetaStorage::~MetaStorage() {
  Shutdown();
}

// multi column family
// cf0: pid/name/id -> inode
// cf1: id -> pending delete
// cf2:
//   PARENT_INDEX/inode_id -> {parent_id, name}
//   UNDER_CONSTRUCTION_INDEX/inode_id -> {""}
// cf3:
//   BLOCK_TYPE/block_id-> {id, gs, len,
//                          inode_id, uc_state, expect_rep, storages}
//   DATANODE_TYPE/datanode_uuid -> {dn_id, ip, storage_types,
//                                     last_update_time, storage_infos}
// cf4:
//   block_id -> BlockPufsInfo / etc.
void MetaStorage::Launch() {
  rocks_write_option_ = rocksdb::WriteOptions();
  rocks_write_option_.sync = FLAGS_dfs_meta_storage_rocksdb_write_sync;

  auto cfopt = rocksdb::ColumnFamilyOptions();
  cfopt.max_bytes_for_level_base =
      FLAGS_dfs_meta_storage_rocksdb_max_bytes_for_level_base;
  cfopt.max_write_buffer_number =
      FLAGS_dfs_meta_storage_rocksdb_max_write_buffer_number;
  cfopt.write_buffer_size =
      FLAGS_dfs_meta_storage_rocksdb_write_buffer_size_mb * 1024 * 1024u;
  cfopt.level0_stop_writes_trigger =
      FLAGS_dfs_meta_storage_rocksdb_level0_stop_writes_trigger;
  cfopt.min_write_buffer_number_to_merge =
      FLAGS_dfs_meta_storage_rocksdb_min_write_buffer_number_to_merge;

  auto compression_type = FLAGS_dfs_meta_storage_rocksdb_compression_type;
  cfopt.compression = static_cast<rocksdb::CompressionType>(compression_type);
  cfopt.compression_opts.level =
      FLAGS_dfs_meta_storage_rocksdb_compression_level;
  cfopt.level0_file_num_compaction_trigger =
      FLAGS_dfs_meta_storage_rocksdb_level0_file_num_compaction_triger;
  cfopt.inplace_update_support = FLAGS_dfs_meta_storage_rocksdb_inplace_update;

  auto namespace_cf_opt = rocksdb::ColumnFamilyOptions(cfopt);
  namespace_cf_opt.num_levels = FLAGS_dfs_meta_storage_rocksdb_num_levels;

  auto write_back_task_cf_opt = rocksdb::ColumnFamilyOptions(cfopt);
  // https://bytedance.feishu.cn/docx/FpBudQdrBo7lv3xQRSqcXfPMnMh
  write_back_task_cf_opt.num_levels = FLAGS_write_back_task_v2_rocksdb_num_levels;

  // Enable prefix bloom for SST files
  rocksdb::BlockBasedTableOptions table_options;
  table_options.filter_policy.reset(rocksdb::NewBloomFilterPolicy(
      10, FLAGS_dfs_meta_storage_bloom_filter_use_block_based_builder_enabled));

  // block cache
  // When FLAGS_dfs_meta_storage_rocksdb_use_unified_block_cache set to true,
  // all CFs that want a larger block cache instead of default 8MB cache will
  // share this cache.
  size_t block_cache_capacity =
      static_cast<size_t>(
          FLAGS_dfs_meta_storage_rocksdb_block_cache_capacity_mb) *
      1024 * 1024;
  auto block_cache = rocksdb::NewLRUCache(
      block_cache_capacity,
      -1,
      false,
      FLAGS_dfs_meta_storage_rocksdb_block_cache_lru_priority_ratio);
  if (FLAGS_dfs_meta_storage_rocksdb_block_cache_sim_capacity_mb &&
      FLAGS_dfs_meta_storage_rocksdb_block_cache_sim_shard_bits) {
    size_t sim_capacity =
        static_cast<size_t>(
            FLAGS_dfs_meta_storage_rocksdb_block_cache_sim_capacity_mb) *
        1024 * 1024;
    auto shard_bits = FLAGS_dfs_meta_storage_rocksdb_block_cache_sim_shard_bits;
    auto sim_cache =
        rocksdb::NewSimCache(block_cache, sim_capacity, shard_bits);
    rocks_cache_ = std::static_pointer_cast<rocksdb::Cache>(sim_cache);
  } else {
    rocks_cache_ = block_cache;
  }
  table_options.block_cache = rocks_cache_;
  table_options.cache_index_and_filter_blocks =
      FLAGS_dfs_meta_storage_rocksdb_block_cache_for_index_filters;
  table_options.pin_l0_filter_and_index_blocks_in_cache =
      FLAGS_dfs_meta_storage_rocksdb_block_cache_pin_l0;
  if (FLAGS_dfs_meta_storage_rocksdb_block_cache_lru_priority_ratio > 0) {
    table_options.cache_index_and_filter_blocks_with_high_priority = true;
  }
  namespace_cf_opt.table_factory.reset(
      NewBlockBasedTableFactory(table_options));
  namespace_cf_opt.prefix_extractor.reset(rocksdb::NewFixedPrefixTransform(8));

  auto bip_cf_opt = rocksdb::ColumnFamilyOptions(cfopt);
  // Default num levels of block info proto column family is 7.
  // Currently, we do not need to set it.
  // # cat /data00/dancenn_data/rocksdb/OPTIONS-651553 |
  //     grep 'CFOptions "block_info_proto"' -A20      |
  //     grep "num_levels"
  // num_levels=7
  // bip_cf_opt.num_levels = FLAGS_dfs_meta_storage_rocksdb_num_levels;
  if (FLAGS_dfs_meta_storage_rocksdb_use_unified_block_cache) {
    bip_cf_opt.table_factory.reset(NewBlockBasedTableFactory(table_options));
  } else {
    rocksdb::BlockBasedTableOptions table_options_for_bip;
    table_options_for_bip.filter_policy.reset(rocksdb::NewBloomFilterPolicy(
        10,
        FLAGS_dfs_meta_storage_bloom_filter_use_block_based_builder_enabled));
    rocks_cache_for_bip_ = rocksdb::NewLRUCache(block_cache_capacity);
    table_options_for_bip.block_cache = rocks_cache_for_bip_;
    bip_cf_opt.table_factory.reset(
        NewBlockBasedTableFactory(table_options_for_bip));
  }

  auto namespace_stat_cf_opt = rocksdb::ColumnFamilyOptions(cfopt);
  namespace_stat_cf_opt.merge_operator =
      std::shared_ptr<rocksdb::MergeOperator>(new INodeStatMergeOperator());

  // StorageClassReport CF
  auto scr_cfopt = rocksdb::ColumnFamilyOptions(cfopt);
  // XXX merge op is deprecated, only for backward compatibility here
  scr_cfopt.merge_operator = std::shared_ptr<rocksdb::MergeOperator>(
      new StorageClassReportMergeOperator());
  if (FLAGS_dfs_meta_storage_rocksdb_use_unified_block_cache) {
    scr_cfopt.table_factory.reset(NewBlockBasedTableFactory(table_options));
  } else {
    rocksdb::BlockBasedTableOptions table_options_for_scr;
    table_options_for_scr.filter_policy.reset(rocksdb::NewBloomFilterPolicy(
        10,
        FLAGS_dfs_meta_storage_bloom_filter_use_block_based_builder_enabled));
    rocks_cache_for_scr_ = rocksdb::NewLRUCache(block_cache_capacity);
    table_options_for_scr.block_cache = rocks_cache_for_scr_;
    scr_cfopt.table_factory.reset(
        NewBlockBasedTableFactory(table_options_for_scr));
  }
  scr_cfopt.prefix_extractor.reset(rocksdb::NewFixedPrefixTransform(8));
  auto snapshot_cf_opt = rocksdb::ColumnFamilyOptions(cfopt);
  snapshot_cf_opt.merge_operator =
    std::shared_ptr<rocksdb::MergeOperator>(new SnapshotMergeOperator());

  column_families_.emplace_back(kINodeDefaultCFName, namespace_cf_opt);
  column_families_.emplace_back(kINodePendingDeleteCFName, cfopt);
  column_families_.emplace_back(kNameSystemInfoCFName, cfopt);
  column_families_.emplace_back(kAccessCounterCFName, cfopt);
  column_families_.emplace_back(kINodeIndexCFName, cfopt);
  column_families_.emplace_back(kLegacyBlockPufsInfoCFName, cfopt);
  column_families_.emplace_back(kLegacyDeprecatedBlockPufsInfoCFName, cfopt);
  column_families_.emplace_back(kLegacyBlockInfoProtoCFName, cfopt);
  column_families_.emplace_back(kBlockInfoProtoCFName, bip_cf_opt);
  column_families_.emplace_back(kLocalBlockCFName, cfopt);
  column_families_.emplace_back(kDeprecatingBlockCFName, cfopt);
  column_families_.emplace_back(kDeprecatedBlockCFName, cfopt);
  column_families_.emplace_back(kINodeStatCFName, namespace_stat_cf_opt);
  column_families_.emplace_back(kLifecyclePolicyCFName, cfopt);
  column_families_.emplace_back(kStorageClassStatCFName, cfopt);
  column_families_.emplace_back(kStorageClassReportCFName, scr_cfopt);
  column_families_.emplace_back(kLeaseCFName, cfopt);
  column_families_.emplace_back(kDatanodeInfoCFName, cfopt);
  column_families_.emplace_back(kSnapshotRootInfoCFName, cfopt);
  column_families_.emplace_back(kSnapshotInodeCFName, snapshot_cf_opt);
  column_families_.emplace_back(kSnapshotRenameRecordCFName, cfopt);
  column_families_.emplace_back(kSnapshotINodeIndexCFName, snapshot_cf_opt);
  column_families_.emplace_back(kJobInfoCFName, cfopt);
  column_families_.emplace_back(kWriteBackTaskCFName, write_back_task_cf_opt);
  column_families_.emplace_back(kDirPolicyCFName, cfopt);
  column_families_.emplace_back(kINodeAttrTtlCFName, cfopt);
  column_families_.emplace_back(kTtlATimeCFName, cfopt);

  rocks_stats_ = rocksdb::CreateDBStatistics();
  rocksdb::DBOptions opt;
  opt.statistics = rocks_stats_;
  opt.listeners.emplace_back(std::static_pointer_cast<rocksdb::EventListener>(
      std::make_shared<RocksDBListener>(metrics_.metrics_)));

  // rocksdb::DBOptions opt;
  opt.create_if_missing = true;
  opt.create_missing_column_families = true;
  if (cfopt.inplace_update_support) {
    opt.allow_concurrent_memtable_write = false;
  } else {
    opt.allow_concurrent_memtable_write = true;
  }
  opt.IncreaseParallelism(FLAGS_dfs_meta_storage_rocksdb_parallelism);
  opt.max_background_flushes =
      FLAGS_dfs_meta_storage_rocksdb_max_background_flushes;
  opt.max_background_compactions =
      FLAGS_dfs_meta_storage_rocksdb_max_background_compactions;
  opt.max_background_jobs = FLAGS_dfs_meta_storage_rocksdb_max_background_jobs;
  opt.max_total_wal_size =
      FLAGS_dfs_meta_storage_rocksdb_max_total_wal_size_mb * 1024 * 1024u;

  opt.use_direct_reads = FLAGS_dfs_meta_storage_rocksdb_use_direct_reads;
  opt.use_direct_io_for_flush_and_compaction =
      FLAGS_dfs_meta_storage_rocksdb_use_direct_io_for_flush_and_compaction;

  std::vector<std::string> all_column_families;
  {
    auto s =
        rocksdb::DB::ListColumnFamilies(opt, db_path_, &all_column_families);
    if (!s.ok()) {
      LOG(WARNING) << "ListColumnFamilies failed, msg: " << s.ToString();
    }
  }
  std::string all_column_families_str;
  // TODO(ruanjunbin): Using var column_families_ to store column families
  // should be removed is confusing.
  auto exclude_cf_start_index = column_families_.size();

  bool has_inode_index_cf = false;

  const std::unordered_set<std::string> valid_cf_name = {
      kINodeDefaultCFName,
      kINodeIndexCFName,
      kINodePendingDeleteCFName,
      kNameSystemInfoCFName,
      kAccessCounterCFName,
      kLegacyBlockPufsInfoCFName,
      kLegacyDeprecatedBlockPufsInfoCFName,
      kLegacyBlockInfoProtoCFName,
      kBlockInfoProtoCFName,
      kLocalBlockCFName,
      kDeprecatingBlockCFName,
      kDeprecatedBlockCFName,
      kINodeStatCFName,
      kLifecyclePolicyCFName,
      kStorageClassStatCFName,
      kStorageClassReportCFName,
      kLeaseCFName,
      kDatanodeInfoCFName,
      kSnapshotRootInfoCFName,
      kSnapshotInodeCFName,
      kSnapshotRenameRecordCFName,
      kSnapshotINodeIndexCFName,
      kJobInfoCFName,
      kWriteBackTaskCFName,
      kDirPolicyCFName,
      kINodeAttrTtlCFName,
      kTtlATimeCFName,
  };
  for (const auto& local : all_column_families) {
    if (valid_cf_name.find(local) == valid_cf_name.end()) {
      column_families_.emplace_back(local, cfopt);
      continue;
    }
    if (local == kINodeIndexCFName) {
      has_inode_index_cf = true;
    }
    all_column_families_str += " " + local;
  }
  LOG(INFO) << "ListColumnFamilies, all:" << all_column_families_str
            << ", exclude_cf_start_index: " << exclude_cf_start_index;

  rocks_db_ = OpenRocksDB(opt);

  // Clean unknown column family
  for (auto i = 0; i < handles_.size(); ++i) {
    if (i < exclude_cf_start_index) {
      cf_handle_map_[handles_[i]->GetID()] = handles_[i];
      continue;
    }
    rocks_db_->DropColumnFamily(handles_[i]);
    LOG(INFO) << "Drop unknown column family: " << column_families_[i].name;
    delete handles_[i];
    handles_[i] = nullptr;
  }

  // init metrics
  for (int idx = 0; idx < handles_.size(); ++idx) {
    if (handles_[idx] == nullptr) {
      continue;
    }

    auto m = metrics_.metrics_->RegisterGauge(
        "ColumnFamilySize#cf_name=" + handles_[idx]->GetName(),
        [this, idx]() -> double {
          int64_t num = 0;

          if (stopped_.load() || rocks_db_ == nullptr ||
              handles_[idx] == nullptr) {
            // handles_[idx] may change, for example,
            // MigrateBlockInfoFromV1ToV2DuringStartUp release handle
            return 0;
          }

          std::string str;
          rocks_db_->GetProperty(
              handles_[idx], rocksdb::DB::Properties::kEstimateNumKeys, &str);

          try {
            num = stoll(str);
          } catch (...) {
            LOG(ERROR)
                << "CF kEstimateNumKeys, return value is not a number. str="
                << str;
          }

          return num;
        });
    metrics_.cf_key_size_metrics_.push_back(m);
  }

  // Reset num inodes for backwards compatibility
  if (!has_inode_index_cf) {
    rocks_db_->Delete(rocks_write_option_,
                      handles_[kNameSystemInfoCFIndex],
                      kNumINodesKey);
  }

  if (FLAGS_drop_storage_class_report) {
    std::string start_key;
    std::string end_key;
    EncodeSCRKey(0, "", &start_key);
    EncodeSCRKey(UINT64_MAX, "", &end_key);
    auto s = rocks_db_->DeleteRange(rocks_write_option_,
                                    handles_[kStorageClassReportCFIndex],
                                    start_key,
                                    end_key);
    if (!s.ok()) {
      LoggerMetrics::Error();
      LOG(ERROR) << "Failed to drop storage class report";
    }
  }

  LOG(INFO) << "DB opened: " << db_path_ << ", with " << handles_.size() << " handles.";

  StartSlows();
  StartWriter();
  StartBGCompactAllWorker();
}

std::unique_ptr<rocksdb::DB> MetaStorage::OpenRocksDB(
    const rocksdb::DBOptions& opt) {
  rocksdb::DB* p = nullptr;
  rocksdb::Status s =
      rocksdb::DB::Open(opt, db_path_, column_families_, &handles_, &p);
  if (!s.ok()) {
    LOG(FATAL) << "Failed to open DB: " << db_path_
               << ", status: " << s.ToString();
  }
  return std::unique_ptr<rocksdb::DB>(p);
}

void MetaStorage::Shutdown() {
  stopped_.store(true);

  StopWriter();
  StopSlows();
  // May stuck on CompactRange
  StopBGCompactAllWorker();

  for (auto h : handles_) {
    // Skip legacy column families about block.
    if (h != nullptr) {
      delete h;
    }
  }
  cf_handle_map_.clear();
  handles_.clear();
  rocks_db_.reset();

  LOG(INFO) << "MetaStorage Shutdown...";
}

void MetaStorage::Clear() {
  Shutdown();

  auto s = rocksdb::DestroyDB(db_path_, rocksdb::Options());
  if (!s.ok()) {
    LOG(FATAL) << "Failed to destroy DB: " << s.ToString();
  }

  Launch();
}

void MetaStorage::DropRpcUntilFinish(
    const std::function<bool()>& finish_checker) {
  writer_->DropAllWriteTaskForActiveUntilFinish(finish_checker);
}

int MetaStorage::NumPending() const {
  return writer_->NumPending();
}

void MetaStorage::WaitNoPending(bool include_bg) {
  writer_->WaitNoPending(include_bg);
  slows_->WaitNoPending();
}

void MetaStorage::ResetLastInodeId(INodeID inode_id) {
  LOG(INFO) << "ResetLastInodeId last_inode_id_=" << inode_id;
  last_inode_id_ = inode_id;
}

void MetaStorage::ResetLastAppliedTxId(int64_t txid) {
  // CHECK_NOTNULL(single_writer_task_.get());
  CHECK_NOTNULL(writer_.get());
  LOG(INFO) << "reset single_writer_task_ last txid " << txid;
  // single_writer_task_->set_last_applied_txid(txid);
  writer_->LastApplyTxid(txid);
}

void MetaStorage::ResetLastBlockId(uint64_t last_block_id) {
  LOG(INFO) << "reset single_writer_task_ last block id " << last_block_id;
  last_block_id_ = last_block_id;
}

void MetaStorage::ResetLastGenerationStampV2(uint64_t last_gs_v2) {
  LOG(INFO) << "reset single_writer_task_ last gs_v2 " << last_gs_v2;
  last_generation_stamp_v2_ = last_gs_v2;
}

void MetaStorage::LoadLastSnapshotID() {
  std::string value;
  bool got = GetNameSystemInfo(kLastSnapshotIdKey, &value);
  if (!got) {
    last_snapshot_id_ = 0;
    LOG(WARNING) << "First time launching, last_snapshot_id is uninitialized";
    return;
  }
  CHECK_EQ(value.length(), sizeof(uint64_t));
  last_snapshot_id_ = MetaStorage::DecodeInteger<uint64_t>(value);
  LOG(INFO) << kLastSnapshotIdKey << " is: " << last_snapshot_id_;
}

void MetaStorage::PutFileSystemInfo(cnetpp::base::StringPiece key,
                                    cnetpp::base::StringPiece value) {
  auto s = rocks_db_->Put(rocks_write_option_,
                          handles_[kNameSystemInfoCFIndex],
                          rocksdb::Slice(key.data(), key.length()),
                          rocksdb::Slice(value.data(), value.length()));
  if (!s.ok()) {
    LOG(FATAL) << "Failed to put filesystem info, key: " << key.as_string()
               << ", value: " << value.as_string() << ", error: " << s.ToString();
  }
}

bool MetaStorage::GetFileSystemInfo(cnetpp::base::StringPiece key,
                                    std::string* value) {
  CHECK_NOTNULL(value);

  auto s = rocks_db_->Get(rocksdb::ReadOptions(),
                          handles_[kNameSystemInfoCFIndex],
                          rocksdb::Slice(key.data(), key.length()),
                          value);
  if (!s.ok()) {
    if (s.IsNotFound()) {
      return false;
    } else {
      LOG(FATAL) << "Failed to get filesystem info, key: " << key.as_string()
                 << ", error: " << s.ToString();
    }
  }
  return false;
}

void MetaStorage::PutNameSystemInfo(cnetpp::base::StringPiece key,
                                    cnetpp::base::StringPiece value) {
  auto s = rocks_db_->Put(rocks_write_option_,
                          handles_[kNameSystemInfoCFIndex],
                          rocksdb::Slice(key.data(), key.length()),
                          rocksdb::Slice(value.data(), value.length()));
  if (!s.ok()) {
    LOG(FATAL) << "Failed to put namesystem info, key: " << key.as_string()
               << ", value: " << value.as_string() << ", error: " << s.ToString();
  }
}

bool MetaStorage::GetNameSystemInfo(cnetpp::base::StringPiece key,
                                    std::string* value,
                                    MetaStorageIterPtr iter) {
  CHECK_NOTNULL(value);

  MetaStorageIterHolder iter_holder;
  if (iter == nullptr) {
    iter = rocks_db_->NewIterator(rocksdb::ReadOptions(),
                                  handles_[kNameSystemInfoCFIndex]);
    iter_holder.iter_.reset(iter);
  }
  CHECK_NOTNULL(iter);

  rocksdb::Slice rocksdb_key(key.data(), key.length());
  iter->Seek(rocksdb_key);
  if (!iter->status().ok()) {
    if (iter->status().IsNotFound()) {
      return false;
    } else {
      LOG(FATAL) << "Failed to get namesystem info, key: " << key
                 << ", error: " << iter->status().ToString();
    }
  }
  if (iter->Valid() && iter->key() == rocksdb_key) {
    *value = iter->value().ToString();
    return true;
  }
  return false;
}

void MetaStorage::DeleteNameSystemInfo(cnetpp::base::StringPiece key) {
  auto s = rocks_db_->Delete(rocksdb::WriteOptions(),
                             handles_[kNameSystemInfoCFIndex],
                             rocksdb::Slice(key.data(), key.length()));
  if (!s.ok()) {
    LOG(FATAL) << "Failed to delete namesystem info, key: " << key.as_string()
               << ", error: " << s.ToString();
  }
}

void MetaStorage::TestOnlyPutLegacyBlockInfoProto(const BlockInfoProto& bip) {
  std::string value;
  bip.SerializeToString(&value);
  CHECK(rocks_db_
            ->Put(rocks_write_option_,
                  handles_[kLegacyBlockInfoProtoCFIndex],
                  std::to_string(bip.block_id()),
                  value)
            .ok());
}

void MetaStorage::TestOnlyPutLegacyBlockPufsInfo(
    const BlockPufsInfo& block_pufs_info) {
  CHECK(rocks_db_
            ->Put(rocks_write_option_,
                  handles_[kLegacyBlockPufsInfoCFIndex],
                  std::to_string(block_pufs_info.block_id_),
                  block_pufs_info.SerializeToJsonString())
            .ok());
}

void MetaStorage::TestOnlyPutLegacyDeprecatedBlockPufsInfo(BlockID blk_id) {
  CHECK(rocks_db_
            ->Put(rocks_write_option_,
                  handles_[kLegacyDeprecatedBlockPufsInfoCFIndex],
                  std::to_string(blk_id),
                  "")
            .ok());
}

bool MetaStorage::TestOnlyIsLocalBlockExisted(BlockID blk_id) {
  std::string value;
  return rocks_db_
      ->Get(rocksdb::ReadOptions(),
            handles_[kLocalBlockCFIndex],
            EncodeBlockID(blk_id),
            &value)
      .ok();
}

bool MetaStorage::TestOnlyIsDeprecatingBlockExisted(BlockID blk_id) {
  std::string value;
  return rocks_db_
      ->Get(rocksdb::ReadOptions(),
            handles_[kDeprecatingBlockCFIndex],
            EncodeBlockID(blk_id),
            &value)
      .ok();
}

bool MetaStorage::TestOnlyIsDeprecatedBlockExisted(BlockID blk_id) {
  std::string value;
  return rocks_db_
      ->Get(rocksdb::ReadOptions(),
            handles_[kDeprecatedBlockCFIndex],
            EncodeBlockID(blk_id),
            &value)
      .ok();
}

void MetaStorage::MigrateBlockInfoFromV1ToV2DuringStartUp() {
  std::string version;
  if (GetNameSystemInfo(kBlockInfoVersion, &version)) {
    if (version == kBlockInfoVersionV2) {
      LOG(INFO) << "Block info version is v2, no need to migrate";
      return;
    } else {
      LOG(FATAL) << "Unknown block info version: " << version;
    }
  }

  LOG(INFO) << "MigrateBlockInfoFromV1ToV2DuringStartUp begin";
  rocksdb::WriteOptions write_options;
  // write_options.sync = true;
  std::string legacy_begin_key, legacy_end_key;
  std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kLegacyBlockInfoProtoCFIndex]));
  for (it->SeekToFirst(); it->Valid(); it->Next()) {
    if (legacy_begin_key.empty()) {
      legacy_begin_key = it->key().ToString();
    }
    legacy_end_key = it->key().ToString();
    BlockID blk_id = std::stoull(it->key().ToString());
    BlockInfoProto bip;
    CHECK(bip.ParseFromArray(it->value().data(), it->value().size()))
        << "NotDeserializable B" << blk_id;
    CHECK_EQ(blk_id, bip.block_id())
        << "Key and value of legacy local block info are unmatched";
    CHECK(bip.state() == BlockInfoProto::kUnderConstruction ||
          bip.state() == BlockInfoProto::kComplete)
        << "Unexpected Block B" << blk_id;
    CHECK(rocks_db_
              ->Put(write_options,
                    handles_[kBlockInfoProtoCFIndex],
                    EncodeBlockID(blk_id),
                    it->value())
              .ok())
        << "Failed to put B" << blk_id << " to block info proto";
    CHECK(rocks_db_
              ->Put(write_options,
                    handles_[kLocalBlockCFIndex],
                    EncodeBlockID(blk_id),
                    "")
              .ok())
        << "Failed to put B" << blk_id << "to local block";
  }
  CHECK(it->status().ok()) << "Meets error when move legacy block info: "
                           << it->status().ToString();
  CHECK(rocks_db_
            ->DeleteRange(write_options,
                          handles_[kLegacyBlockInfoProtoCFIndex],
                          legacy_begin_key,
                          legacy_end_key)
            .ok());

  legacy_begin_key.clear();
  legacy_end_key.clear();
  it.reset(rocks_db_->NewIterator(rocksdb::ReadOptions(),
                                  handles_[kLegacyBlockPufsInfoCFIndex]));
  for (it->SeekToFirst(); it->Valid(); it->Next()) {
    if (legacy_begin_key.empty()) {
      legacy_begin_key = it->key().ToString();
    }
    legacy_end_key = it->key().ToString();
    BlockID blk_id = std::stoull(it->key().ToString());
    CHECK_NE(blk_id, kInvalidBlockID);
    BlockPufsInfo block_pufs_info;
    CHECK(block_pufs_info.DeserializeFromJsonString(
        std::string(it->value().data(), it->value().size())))
        << "NotDeserializable B" << blk_id;
    CHECK_EQ(blk_id, block_pufs_info.block_id_)
        << "Key and value of legacy persisted/deprecated block info are "
           "unmatched";
    CHECK(block_pufs_info.state_ == BlockPufsState::kUploadIssued ||
          block_pufs_info.state_ == BlockPufsState::kPersisted ||
          block_pufs_info.state_ == BlockPufsState::kDeprecated)
        << "UnexpectedBlock B" << blk_id;
    std::string value;
    block_pufs_info.GetBlockInfoProto().SerializeToString(&value);
    CHECK(rocks_db_
              ->Put(write_options,
                    handles_[kBlockInfoProtoCFIndex],
                    EncodeBlockID(blk_id),
                    value)
              .ok())
        << "Failed to put B" << blk_id << " to block info proto";
    if (block_pufs_info.state_ == BlockPufsState::kUploadIssued) {
      CHECK(rocks_db_
                ->Put(write_options,
                      handles_[kLocalBlockCFIndex],
                      EncodeBlockID(blk_id),
                      "")
                .ok())
          << "Failed to put B" << blk_id << " to local block";
    } else if (block_pufs_info.state_ == BlockPufsState::kDeprecated) {
      CHECK(rocks_db_
                ->Put(write_options,
                      handles_[kDeprecatedBlockCFIndex],
                      EncodeBlockID(blk_id),
                      "")
                .ok())
          << "Failed to put B" << blk_id << " to deprecated block";
    }
  }
  CHECK(it->status().ok())
      << "Meets error when move persisted/deprecated block info: "
      << it->status().ToString();
  CHECK(rocks_db_
            ->DeleteRange(write_options,
                          handles_[kLegacyBlockPufsInfoCFIndex],
                          legacy_begin_key,
                          legacy_end_key)
            .ok());

  legacy_begin_key.clear();
  legacy_end_key.clear();
  it.reset(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kLegacyDeprecatedBlockPufsInfoCFIndex]));
  for (it->SeekToFirst(); it->Valid(); it->Next()) {
    if (legacy_begin_key.empty()) {
      legacy_begin_key = it->key().ToString();
    }
    legacy_end_key = it->key().ToString();
    BlockID blk_id = std::stoull(it->key().ToString());
    std::string key;
    if (rocks_db_
            ->Get(rocksdb::ReadOptions(),
                  handles_[kDeprecatedBlockCFIndex],
                  EncodeBlockID(blk_id),
                  &key)
            .ok()) {
    } else {
      CHECK(rocks_db_
                ->Put(write_options,
                      handles_[kDeprecatingBlockCFIndex],
                      EncodeBlockID(blk_id),
                      "")
                .ok())
          << "Failed to put B" << blk_id << " to deprecating block";
    }
  }
  CHECK(it->status().ok())
      << "Meets error when move legacy deprecated block info: "
      << it->status().ToString();
  CHECK(rocks_db_
            ->DeleteRange(write_options,
                          handles_[kLegacyDeprecatedBlockPufsInfoCFIndex],
                          legacy_begin_key,
                          legacy_end_key)
            .ok());

  std::vector<uint32_t> legacy_cf_indices{kLegacyBlockPufsInfoCFIndex,
                                          kLegacyDeprecatedBlockPufsInfoCFIndex,
                                          kLegacyBlockInfoProtoCFIndex};
  for (uint32_t cf_index : legacy_cf_indices) {
    // Don't drop column family, otherwise cf indices will change.
    delete handles_[cf_index];
    handles_[cf_index] = nullptr;
  }
  PutNameSystemInfo(kBlockInfoVersion, kBlockInfoVersionV2);
  LOG(INFO) << "MigrateBlockInfoFromV1ToV2DuringStartUp end";
}

void MetaStorage::ConstructIndexTableCommon(
    const std::function<void(const INode& inode)>& func) {
  auto snapshot_holder = GetSnapshot();

  // Iterator holder scope within snapshot holder scope.
  auto snapshot = snapshot_holder->snapshot();
  auto iter_holder = GetIterator(snapshot, kINodeDefaultCFIndex);
  auto iter = iter_holder->iter();
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    INodeID inode_id = kInvalidINodeId;
    DecodeStoreKey(
        cnetpp::base::StringPiece(iter->key().data(), iter->key().size()),
        nullptr,
        nullptr,
        &inode_id);
    INode inode;
    CHECK(inode.ParseFromArray(iter->value().data(), iter->value().size()))
        << "Failed to parse inode from inode table, id:" << inode_id;
    CHECK_EQ(inode_id, inode.id()) << inode.ShortDebugString();

    VLOG(10) << "ConstructIndexTableCommon inode=" << inode.ShortDebugString();

    func(inode);
  }
  CHECK(iter->status().ok()) << iter->status().ToString();
  WaitNoPending(true);
}

void MetaStorage::ConstructLeaseTableDuringStartUp() {
  if (!FLAGS_force_rebuild_lease_db) {
    std::string version;
    GetNameSystemInfo(kLeaseVersion, &version);
    if (version.empty()) {
    } else if (version == kLeaseVersionV2) {
      LOG(INFO) << "Lease version is v2, no need to migrate";
      return;
    } else {
      LOG(FATAL) << "Unknown lease version: " << version;
    }
  }

  LOG(INFO) << "ConstructLeaseTableDuringStartUp begin";
  DropLeaseTable();
  ConstructIndexTableCommon([&](const INode& inode) {
    // A significant number of inodes exist,
    // but only a small portion of them uc files.
    // Therefore, it is unnecessary to
    // invoke UpdateLeaseWriteBatch for every inode.
    if (inode.has_uc()) {
      auto wb = std::make_unique<rocksdb::WriteBatch>();
      UpdateLeaseWriteBatch(inode, false, wb.get());
      PushINodeBGWriteTask(std::move(wb), 0, nullptr);
    }
  });

  PutNameSystemInfo(kLeaseVersion, kLeaseVersionV2);
  LOG(INFO) << "ConstructLeaseTableDuringStartUp end";
}

void MetaStorage::ConstructPolicyTableDuringStartUp(
    const std::function<void(const INode&)>& func) {
  auto fast_scan_cb = [this, &func](const rocksdb::Slice& key,
                                    const rocksdb::Slice& value) -> bool {
    INode inode;
    auto inode_id = DecodeINodeID(key);
    if (!inode.ParseFromArray(value.data(), value.size())) {
      if (GetINode(inode_id, &inode) != StatusCode::kOK) {
        LOG(ERROR) << "Failed to get inode " << inode_id;
        return true;
      }
    }
    func(inode);
    return true;
  };

  if (!FLAGS_force_rebuild_policy_db) {
    std::string version;
    GetNameSystemInfo(kDirPolicyPersistedKey, &version);
    if (version.empty()) {
    } else if (version == kDirPolicyVersionV2) {
      LOG(INFO) << "dir policy version is v2, no need to rescan all table";
      // fast scan
      ScanColumnFamily(kDirPolicyCFIndex, fast_scan_cb);
      return;
    } else {
      LOG(FATAL) << "Unknown dir policy version: " << version;
    }
  }

  LOG(INFO) << "ConstructPolicyTableDuringStartUp begin";
  if (FLAGS_need_build_policy_db_first_time) {
    DropPolicyTable();
    ConstructIndexTableCommon([&](const INode& inode) {
      if (PolicyManager::HasPolicy(inode)) {
        func(inode);
      }
    });
  }

  PutNameSystemInfo(kDirPolicyPersistedKey, kDirPolicyVersionV2);
  LOG(INFO) << "ConstructPolicyTableDuringStartUp end";
}

void MetaStorage::ConstructWriteBackTasksDuringStartUp() {
  if (!FLAGS_force_rebuild_write_back_task_db) {
    std::string version;
    GetNameSystemInfo(kWriteBackTaskVersion, &version);
    if (version.empty()) {
    } else if (version == kWriteBackTaskVersionV2) {
      LOG(INFO) << "Write back task version is v2, no need to migrate";
      return;
    } else {
      LOG(FATAL) << "Unknown write back task version: " << version;
    }
  }

  LOG(INFO) << "ConstructWriteBackTasksDuringStartUp begin";
  DropWriteBackTasks();

  ConstructIndexTableCommon([&](const INode& inode) {
    if (inode.has_ufs_file_info() &&
        inode.ufs_file_info().file_state() == kUfsFileStateToBePersisted) {
      auto wb = std::make_unique<rocksdb::WriteBatch>();
      UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
      PushINodeBGWriteTask(std::move(wb), 0, nullptr);
    }
  });

  PutNameSystemInfo(kWriteBackTaskVersion, kWriteBackTaskVersionV2);
  LOG(INFO) << "ConstructWriteBackTasksDuringStartUp end";
}

void MetaStorage::DropTableCommon(const std::string& func_name,
                                  uint32_t cf_index) {
  StopWatch sw;
  sw.Start();
  LOG(INFO) << func_name << " Start";

  CHECK_LT(cf_index, handles_.size());
  auto handle = handles_[cf_index];
  CHECK_NOTNULL(handle);
  // Drop.
  {
    std::string start_key = EncodeINodeID(0);
    std::string end_key = EncodeINodeID(UINT64_MAX);
    auto opt = rocksdb::WriteOptions();
    opt.sync = true;
    auto s = rocks_db_->DeleteRange(opt, handle, start_key, end_key);
    CHECK(s.ok());
    LOG(INFO) << func_name << " Dropped";
  }
  // Check.
  {
    auto iter = rocks_db_->NewIterator(rocksdb::ReadOptions(), handle);
    iter->SeekToFirst();
    CHECK(!iter->Valid());
    CHECK(iter->status().ok());
    LOG(INFO) << func_name << " Checked";
  }
  // Compaction.
  {
    rocksdb::CompactRangeOptions options;
    options.bottommost_level_compaction =
        rocksdb::BottommostLevelCompaction::kForce;
    auto s = rocks_db_->CompactRange(options, handle, nullptr, nullptr);
    if (!s.ok()) {
      LOG(FATAL) << "CompactRange " << handle->GetName()
                 << " fail: " << s.ToString();
    }
    LOG(INFO) << func_name << " Compacted";
  }
  LOG(INFO) << func_name << " Finish. " << sw.NextStepTime() << " us";
}

void MetaStorage::DropLeaseTable() {
  DropTableCommon("DropLeaseIndexTable", kLeaseCFIndex);
}

void MetaStorage::DropWriteBackTasks() {
  DropTableCommon("DropWriteBackTasks", kWriteBackTaskCFIndex);
}

void MetaStorage::DropPolicyTable() {
  DropTableCommon("DropPolicyIndexTable", kDirPolicyCFIndex);
}

bool MetaStorage::GetBlockInfo(BlockID block_id, BlockInfoProto* bip) {
  CHECK_NOTNULL(bip);

  MFC(metrics_.get_block_info_num_)->Inc();
  StopWatch sw(metrics_.get_block_info_time_);
  sw.Start();

  std::string value;
  rocksdb::Status s = rocks_db_->Get(rocksdb::ReadOptions(),
                                     handles_[kBlockInfoProtoCFIndex],
                                     EncodeBlockID(block_id),
                                     &value);
  if (!s.ok()) {
    return false;
  }
  if (!bip->ParseFromArray(value.data(), value.size())) {
    LOG(ERROR) << "ParseFromArray failed for: " << block_id;
    return false;
  }
  return true;
}

void MetaStorage::TestOnlyPutBlockInfo(const BlockInfoProto& bip, bool is_deprecating) {
  std::string value;
  CHECK(bip.SerializeToString(&value));
  auto s = rocks_db_->Put(rocks_write_option_,
                          handles_[kBlockInfoProtoCFIndex],
                          EncodeBlockID(bip.block_id()),
                          value);
  CHECK(s.ok());
  if (bip.state() == BlockInfoProto::kPersisted) {
  } else if (bip.state() == BlockInfoProto::kDeprecated) {
    s = rocks_db_->Put(rocks_write_option_,
                       handles_[kDeprecatedBlockCFIndex],
                       EncodeBlockID(bip.block_id()),
                       "");
  } else {
    s = rocks_db_->Put(rocks_write_option_,
                       handles_[kLocalBlockCFIndex],
                       EncodeBlockID(bip.block_id()),
                       "");
  }
  CHECK(s.ok());
  if (is_deprecating) {
    s = rocks_db_->Put(rocks_write_option_,
                       handles_[kDeprecatingBlockCFIndex],
                       EncodeBlockID(bip.block_id()),
                       "");
  }
  CHECK(s.ok());
}

void MetaStorage::PutBlockInfo(const BlockInfoProto& bip,
                               const BlockInfoProto* old_bip,
                               int64_t txid,
                               Closure* done) {
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(bip.block_id());
  std::string value;
  CHECK(bip.SerializeToString(&value)) << bip.block_id();
  wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
  wb->Put(handles_[kLocalBlockCFIndex], key, "");
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_bip) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    verify_kvs->emplace_back(kBlockInfoProtoCFIndex,
                             EncodeBlockID(old_bip->block_id()),
                             old_bip->SerializeAsString());
  }
  PushINodeTXWriteTask(std::move(wb), txid, {}, std::move(verify_kvs), done);
}

void MetaStorage::MoveBlockInfoToPersisted(const BlockInfoProto& bip,
                                           const BlockInfoProto* old_bip,
                                           int64_t txid,
                                           Closure* done) {
  CHECK_EQ(bip.state(), BlockInfoProto::kPersisted) << bip.block_id();
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(bip.block_id());
  std::string value;
  CHECK(bip.SerializeToString(&value)) << bip.block_id();
  wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
  wb->Delete(handles_[kLocalBlockCFIndex], key);
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_bip) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    verify_kvs->emplace_back(kBlockInfoProtoCFIndex,
                             EncodeBlockID(old_bip->block_id()),
                             old_bip->SerializeAsString());
  }
  PushINodeTXWriteTask(std::move(wb), txid, {}, std::move(verify_kvs), done);
}

void MetaStorage::MoveBlockInfosToDeprecatedDuringStartUp() {
  LOG(INFO) << "MoveBlockInfosToDeprecatedDuringStartUp begin";
  std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDeprecatingBlockCFIndex]));
  for (; it->Valid(); it->Next()) {
    BlockID blk_id = DecodeBlockID(it->key());
    CHECK_NE(blk_id, kInvalidBlockID);
    BlockInfoProto bip;
    if (!GetBlockInfo(blk_id, &bip)) {
      LOG(ERROR) << "Missing B" << blk_id;
      continue;
    }
    bip.set_state(BlockInfoProto::kDeprecated);
    MoveBlockInfoToDeprecated(bip);
  }
  LOG(INFO) << "MoveBlockInfosToDeprecatedDuringStartUp end";
}

void MetaStorage::MoveBlockInfoToDeprecated(const BlockInfoProto& bip) {
  CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated) << bip.block_id();
  VLOG(12) << "MoveBlockInfoToDeprecated: " << bip.block_id();
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(bip.block_id());
  std::string value;
  CHECK(bip.SerializeToString(&value)) << bip.block_id();
  wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
  wb->Delete(handles_[kLocalBlockCFIndex], key);
  wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
  wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
  rocks_db_->Write(rocks_write_option_, wb.get());
}

// TODO(ruanjunbin): Delete.
void MetaStorage::DeleteBlockInfo(BlockID blk_id) {
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(blk_id);
  wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
  wb->Delete(handles_[kLocalBlockCFIndex], key);
  wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
  wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
  rocks_db_->Write(rocks_write_option_, wb.get());
}

// TODO(ruanjunbin): Delete.
void MetaStorage::DeleteBlockInfo(BlockID blk_id, int64_t txid, Closure* done) {
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(blk_id);
  wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
  // Standby NN doesn't call MoveBlockInfoToDeprecated before DeleteBlockInfo.
  // So deleting local/deprecating/deprecated block indices is needed.
  wb->Delete(handles_[kLocalBlockCFIndex], key);
  wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
  wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
  auto task = new meta_storage::WriteTask();
  task->set_txid(txid);
  task->set_wb(std::move(wb));
  task->set_done(done);
  writer_->Push(task);
}

void MetaStorage::DelDepringBlks(const DepringBlksToBeDel &blks,
                                 int64_t txid,
                                 const std::vector<Closure*>& dones) {
  auto wb = CreateWriteBatch();
  for (BlockID blk_id : blks.dangling_blk_ids()) {
    std::string key = EncodeBlockID(blk_id);
    wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
    wb->Delete(handles_[kLocalBlockCFIndex], key);
    wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
    wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
  }
  for (const BlockInfoProto& bip : blks.depred_bips()) {
    CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated) << bip.block_id();
    VLOG(12) << "MoveBlockInfoToDeprecated: " << bip.block_id();
    std::string key = EncodeBlockID(bip.block_id());
    std::string value;
    CHECK(bip.SerializeToString(&value)) << bip.block_id();
    wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
    wb->Delete(handles_[kLocalBlockCFIndex], key);
    wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
    wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
  }
  PushINodeTXWriteTasks(std::move(wb), txid, {}, nullptr, dones);
}

void MetaStorage::DelDepredBlks(const DepredBlksToBeDel& blks,
                                int64_t txid,
                                Closure* done) {
  auto wb = CreateWriteBatch();
  for (BlockID blk_id : blks.blk_ids()) {
    std::string key = EncodeBlockID(blk_id);
    wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
    wb->Delete(handles_[kLocalBlockCFIndex], key);
    wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
    wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
  }
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

void MetaStorage::FlushBlockInfoProtos(const BlockInfoProtos& bips,
                                       int64_t txid,
                                       Closure* done) {
  // TODO(xiong): Update GS
  auto wb = CreateWriteBatch();
  for (const BlockInfoProto& bip : bips.content()) {
    std::string key = EncodeBlockID(bip.block_id());
    CHECK(bip.IsInitialized());
    switch (bip.state()) {
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery:
      case BlockInfoProto::kCommitted:
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued: {
        wb->Put(handles_[kLocalBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kPersisted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeprecated: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
        wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeleted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
        wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
      } break;
      default: {
        LOG(FATAL) << ToJsonCompactString(bip);
      } break;
    }
  }
  auto task = new meta_storage::WriteTask();
  task->set_txid(txid);
  task->set_wb(std::move(wb));
  task->set_done(done);
  writer_->Push(task);
}

void MetaStorage::ScanLocalBlocks(
    const std::function<void(const BlockInfoProto&)>& func) {
  std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kLocalBlockCFIndex]));
  it->SeekToFirst();
  for (; it->Valid(); it->Next()) {
    BlockID blk_id = DecodeBlockID(it->key());
    BlockInfoProto bip;
    CHECK(GetBlockInfo(blk_id, &bip)) << "Block info not found, B" << blk_id;
    func(bip);
  }
  CHECK(it->status().ok()) << "ScanLocalBlocks meets error: "
                           << it->status().ToString();
}

std::unique_ptr<rocksdb::Iterator> MetaStorage::GetDeprecatingBlkIterator() {
  return std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDeprecatingBlockCFIndex]));
}

std::unique_ptr<rocksdb::Iterator> MetaStorage::GetDeprecatedBlkIterator() {
  return std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDeprecatedBlockCFIndex]));
}

void MetaStorage::ScanDeprecatingBlocks(std::vector<BlockID>* result) {
  CHECK_NOTNULL(result);
  std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDeprecatingBlockCFIndex]));
  it->SeekToFirst();
  for (; result->size() < FLAGS_scan_deprecating_block_max_size && it->Valid();
       it->Next()) {
    BlockID blk_id = DecodeBlockID(it->key());
    CHECK_NE(blk_id, kInvalidBlockID);
    result->push_back(blk_id);
  }
}

void MetaStorage::ScanDeprecatedBlocks(std::vector<BlockInfoProto>* result) {
  CHECK_NOTNULL(result);
  std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDeprecatedBlockCFIndex]));
  it->SeekToFirst();
  for (; result->size() < FLAGS_scan_deprecated_block_max_size && it->Valid();
       it->Next()) {
    BlockID blk_id = DecodeBlockID(it->key());
    CHECK_NE(blk_id, kInvalidBlockID);
    BlockInfoProto bip;
    if (!GetBlockInfo(blk_id, &bip)) {
      LOG(WARNING) << "B" << blk_id << " found in deprecated_block cf"
                                       " but not found in block_info_proto cf";
      DeleteBlockInfo(blk_id);
      continue;
    }
    // CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated)
    //     << "Try to delete not deprecated block B" << bip.block_id();
    // https://bytedance.feishu.cn/docx/doxcnNmQFAQBgtUhCqJes4HdEVg
    // State of BlockInfoProto which in kDeprecatedBlockCFName can be
    // kCommitted, kUploadIssued or kPersisted. Do not use CHECK here!
    if (bip.state() != BlockInfoProto::kDeprecated) {
      MFC(LoggerMetrics::Instance().try_to_delete_not_deprecated_block_error_)
          ->Inc();
      LOG(ERROR) << "Try to delete not deprecated block B" << bip.block_id();
      continue;
    }
    result->push_back(std::move(bip));
  }
  CHECK(it->status().ok()) << "ScanDeprecatedBlocks meets error: "
                           << it->status().ToString();
}

uint64_t MetaStorage::GetLastCkptTxId(MetaStorageIterPtr iter) {
  std::string txid;
  uint64_t last_txid = kDefaultLastCkptTxId;
  if (GetNameSystemInfo(kLastCkptTxIdKey, &txid, iter)) {
    CHECK_EQ(txid.length(), sizeof(uint64_t));
    last_txid = platform::ReadBigEndian<uint64_t>(&(txid[0]), 0);
    DLOG(INFO) << kLastCkptTxIdKey << " is: " << last_txid;
  } else {
    LOG(INFO) << "First time launching, last_applied_txid_ set to "
              << kDefaultLastCkptTxId << ".";
  }
  return last_txid;
}

INode MetaStorage::CreateRoot() {
  INode root;
  PermissionStatus p;
  // TODO set proper UserGroupInfo
  UserGroupInfo info;
  p.set_username(info.current_user());
  p.set_groupname(kDfsPermissionsSuperUserGroupDefault);
  p.set_permission(0755);
  // root's parent INode is null in HDFS
  // parent id cannot be null here, so we set root's parent to kRootInodeId
  MakeINode(kRootINodeId, kRootINodeId, kRootName, p,
            INode_Type_kDirectory, &root);
  auto ufs_dir_info = root.mutable_ufs_dir_info();
  ufs_dir_info->set_state(kUfsDirStateIncomplete);
  ufs_dir_info->set_type(kUfsDirTypeUfs);
  ufs_dir_info->set_sync_ts(0);
  ufs_dir_info->set_children_sync_ts(0);

  return std::move(root);
}

void MetaStorage::Sync() {
  rocksdb::WriteBatch wb;
  rocksdb::WriteOptions write_options;
  write_options.sync = true;
  auto s = rocks_db_->Write(write_options, &wb);
  if (!s.ok()) {
    LOG(FATAL) << "Failed to sync, error: " + s.ToString();
  }
}

Status MetaStorage::CreateCheckpoint(const std::string& path) {
  rocksdb::Checkpoint* checkpoint_ptr;
  rocksdb::Status status = rocksdb::Checkpoint::Create(rocks_db_.get(),
      &checkpoint_ptr);
  if (!status.ok()) {
    LOG(ERROR) << "Could not create checkpoint, DB: " << rocks_db_->GetName();
    return Status(Code::kError, status.ToString());
  }
  std::unique_ptr<rocksdb::Checkpoint> checkpoint_unique_ptr(checkpoint_ptr);
  status = checkpoint_unique_ptr->CreateCheckpoint(path);
  if (!status.ok()) {
    LOG(ERROR) << "Could not CreateCheckpoint, DB: " << rocks_db_->GetName();
    return Status(Code::kError, status.ToString());
  }
  LOG(INFO) << "Created checkpoint, path: " << path;
  return Status();
}

void MetaStorage::InsertINode(std::shared_ptr<INode> inode) {
  std::string key;
  std::string value;
  EncodeStoreKey(inode->parent_id(), inode->name(), inode->id(), &key);
  inode->SerializeToString(&value);

  auto wb = CreateWriteBatch();
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);
  int64_t num_inodes_delta = UpdateParentIndexWriteBatch(*inode, false,
      wb.get());
  UpdateLeaseWriteBatch(*inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(*inode, false, wb.get());

  SynchronizedClosure done;
  PushINodeBGWriteTask(std::move(wb), num_inodes_delta, &done);
  done.Await();
}

void MetaStorage::InsertINodeParentIndexAsync(const INode& inode,
                                              Closure* done) {
  auto wb = CreateWriteBatch();
  int64_t num_inodes_delta = UpdateParentIndexWriteBatch(inode, false,
      wb.get());
  PushINodeBGWriteTask(std::move(wb), num_inodes_delta, done);
}

void MetaStorage::InsertINodeDirPolicyIndexAsync(const INode& inode,
                                                 Closure* done) {
  auto wb = std::make_unique<rocksdb::WriteBatch>();

  UpdateDirPolicyWriteBatch(inode, false, wb.get());

  PushINodeBGWriteTask(std::move(wb), {}, done);
}

void MetaStorage::MultiInsertINodes(
    const std::deque<std::shared_ptr<INode>>& inodes) {
  auto wb = CreateWriteBatch();
  int64_t num_inodes_delta = 0;
  for (auto& inode : inodes) {
    std::string key;
    std::string value;
    EncodeStoreKey(inode->parent_id(), inode->name(), inode->id(), &key);
    inode->SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    num_inodes_delta += UpdateParentIndexWriteBatch(*inode, false, wb.get());
    UpdateLeaseWriteBatch(*inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(*inode, false, wb.get());
  }
  SynchronizedClosure done;
  PushINodeBGWriteTask(std::move(wb), num_inodes_delta, &done);
  done.Await();
}

void MetaStorage::InsertDatanodeInfoAsync(const DatanodeID internal_id,
                                          const DatanodeInfoEntryPB& pb,
                                          Closure* done) {
  auto wb = CreateWriteBatch();

  CHECK(wb);
  CHECK(internal_id == pb.internal_id());

  std::string key;
  EncodeDatanodeInfoKey(internal_id, &key);

  std::string value;
  CHECK(pb.SerializeToString(&value));

  wb->Put(handles_[kDatanodeInfoCFIndex], key, value);

  PushDatanodeBGWriteTask(std::move(wb), internal_id, done);
}

void MetaStorage::PushDatanodeBGWriteTask(
    std::unique_ptr<rocksdb::WriteBatch> wb,
    DatanodeID max_dn_id,
    Closure* done) {
  auto task = new dancenn::meta_storage::WriteTask();
  if (wb) {
    task->set_wb(std::move(wb));
  }
  task->set_done(done);
  writer_->PushBGTask(task);
}

void MetaStorage::MultiInsertDatanodeInfoAsync(
    const std::vector<DatanodeInfoEntryPB>& pb_list,
    Closure* done) {
  auto wb = CreateWriteBatch();

  CHECK(wb);

  DatanodeID max_dn_id = 0;

  for (const auto& pb : pb_list) {
    CHECK(pb.has_internal_id());
    std::string key;
    EncodeDatanodeInfoKey(pb.internal_id(), &key);
    max_dn_id = std::max(max_dn_id, static_cast<DatanodeID>(pb.internal_id()));

    std::string value;
    CHECK(pb.SerializeToString(&value));

    wb->Put(handles_[kDatanodeInfoCFIndex], key, value);
  }

  PushDatanodeBGWriteTask(std::move(wb), max_dn_id, done);
}

void MetaStorage::PushINodeTXWriteTasks(std::unique_ptr<rocksdb::WriteBatch> wb,
                                        int64_t txid,
                                        const NameSpaceInfoDelta& delta,
                                        std::unique_ptr<KVVerifyVec> verify_kvs,
                                        const std::vector<Closure*>& dones) {
  auto task = new dancenn::meta_storage::WriteTask();
  task->set_txid(txid);
  if (wb) {
    task->set_wb(std::move(wb));
  }
  task->set_max_inode_id(delta.max_inode_id);
  task->set_max_block_id(delta.max_block_id);
  task->set_max_generation_stamp_v2(delta.max_gs_v2);
  task->set_num_inodes_delta(delta.num_inodes_delta);
  task->set_max_snapshot_id(delta.max_snapshot_id);
  if (dones.size() == 1) {
    task->set_done(dones[0]);
  } else if (dones.size() > 1) {
    task->build_sub_tasks(dones);
  }

  if (!task->HasNext() && !task->HasSubTask()) {
    MFC(metrics_.wtask_bind_empty_callback_task_num_)->Inc();
  } else if (task->HasNext()) {
    if (!task->IsSlow()) {
      MFC(metrics_.wtask_bind_fast_single_callback_task_num_)->Inc();
    } else {
      MFC(metrics_.wtask_bind_slow_single_callback_task_num_)->Inc();
    }
  } else {
    CHECK(task->HasSubTask());
    MFC(metrics_.wtask_bind_slow_multiple_callback_task_num_)->Inc();
  }

  task->set_verify_kvs(std::move(verify_kvs));
  writer_->Push(task);
}

void MetaStorage::PushINodeTXWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
  PushINodeTXWriteTasks(std::move(wb), txid, delta, std::move(verify_kvs), {done});
}

void MetaStorage::PushINodeBGWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t num_inodes_delta,
                                       Closure* done) {
  auto task = new dancenn::meta_storage::WriteTask();
  if (wb) {
    task->set_wb(std::move(wb));
  }
  task->set_num_inodes_delta(num_inodes_delta);
  task->set_done(done);
  writer_->PushBGTask(task);
}

void MetaStorage::OrderedCommitINodes(
    std::vector<INode*>* inodes_add,
    std::vector<INodeAndSnapshot>* inodes_mod,
    std::vector<INodeAndSnapshot>* inodes_mov_src,
    std::vector<INode*>* inodes_mov_dst,
    std::vector<INodeAndSnapshot>* inodes_del,
    std::vector<INodeAndSnapshot>* parents,
    std::vector<INode>* inodes_old,
    const std::vector<BlockInfoProto>& bips_add,
    const std::vector<BlockInfoProto>& bips_mod,
    int64_t txid,
    const std::vector<Closure*>& dones,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  std::string key;
  std::string value;

  // added inodes (ID newly allocated)
  if (inodes_add) {
    for (auto& inode_add : *inodes_add) {
      INode& in = *inode_add;
      if (delta.max_inode_id == kInvalidINodeId ||
          delta.max_inode_id < in.id()) {
        delta.max_inode_id = in.id();
      }

      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
      in.set_create_txid(txid);
      in.set_last_update_txid(txid);
      in.SerializeToString(&value);
      wb->Put(handles_[kINodeDefaultCFIndex], key, value);

      UpdateParentIndexWriteBatch(in, false, wb.get());
      UpdateLeaseWriteBatch(in, false, wb.get());
      UpdateWriteBackTaskWriteBatch(in, false, wb.get());
      UpdateDirPolicyWriteBatch(in, false, wb.get());
    }
    delta.num_inodes_delta += inodes_add->size();
  }

  // modified inodes (parent and name unchanged)
  if (inodes_mod) {
    for (auto& inode_mod : *inodes_mod) {
      INode& in = *inode_mod.inode;
      std::string key;
      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);

      in.set_last_update_txid(txid);
      in.SerializeToString(&value);

      wb->Put(handles_[kINodeDefaultCFIndex], key, value);
      UpdateLeaseWriteBatch(in, false, wb.get());
      UpdateWriteBackTaskWriteBatch(in, false, wb.get());
      UpdateDirPolicyWriteBatch(in, false, wb.get());
      FillSnapshotWriteBatch(*inode_mod.snaplog, txid, wb.get());
    }
  }

  // moved inodes (parent or name changed)
  if (inodes_mov_src) {
    CHECK_NOTNULL(inodes_mov_dst);
    CHECK_EQ(inodes_mov_src->size(), inodes_mov_dst->size());
    for (int i = 0; i < inodes_mov_src->size(); i++) {
      INodeAndSnapshot& src = inodes_mov_src->at(i);
      const INode& src_in = *src.inode;
      INode& dst_in = *inodes_mov_dst->at(i);
      CHECK_EQ(src_in.id(), dst_in.id());

      std::string skey, dkey;
      EncodeStoreKey(src_in.parent_id(), src_in.name(), src_in.id(), &skey);
      EncodeStoreKey(dst_in.parent_id(), dst_in.name(), dst_in.id(), &dkey);
      CHECK_NE(skey, dkey);

      if (src.snaplog->IsInitialized()) {
        // when src inode is referred by some snapshot,
        // add a reference from dst inode to src snapshot inode
        dst_in.mutable_snapshot_references()->Clear();
        auto* ref = dst_in.mutable_snapshot_references()->Add();
        ref->set_inode_id(src_in.id());
        ref->set_last_update_txid(src_in.last_update_txid());
      }
      dst_in.set_last_update_txid(txid);
      dst_in.SerializeToString(&value);

      wb->Delete(handles_[kINodeDefaultCFIndex], skey);
      UpdateParentIndexWriteBatch(src_in, true, wb.get());
      UpdateLeaseWriteBatch(src_in, true, wb.get());
      UpdateWriteBackTaskWriteBatch(src_in, true, wb.get());
      UpdateDirPolicyWriteBatch(src_in, false, wb.get());
      wb->Put(handles_[kINodeDefaultCFIndex], dkey, value);
      UpdateParentIndexWriteBatch(dst_in, false, wb.get());
      UpdateLeaseWriteBatch(dst_in, false, wb.get());
      UpdateWriteBackTaskWriteBatch(dst_in, false, wb.get());
      UpdateDirPolicyWriteBatch(dst_in, false, wb.get());
      FillSnapshotWriteBatch(*src.snaplog, txid, wb.get());
    }
  }

  // deleted inodes (ID to be cleaned)
  if (inodes_del) {
    for (auto& inode_del : *inodes_del) {
      const INode& in = *inode_del.inode;
      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
      wb->Delete(handles_[kINodeDefaultCFIndex], key);

      UpdateParentIndexWriteBatch(in, true, wb.get());
      UpdateLeaseWriteBatch(in, true, wb.get());
      UpdateWriteBackTaskWriteBatch(in, true, wb.get());
      UpdateDirPolicyWriteBatch(in, true, wb.get());
      FillDeleteStatWriteBatch(in, wb.get());

      if (inode_del.snaplog->IsInitialized() ||
          in.type() == INode_Type_kDirectory) {
        FillPendingDeleteWriteBatch(in, wb.get(), false);
      } else {
        FillDeprecatingBlockWriteBatch(in, wb.get());
      }

      FillSnapshotWriteBatch(*inode_del.snaplog, txid, wb.get());
    }
    delta.num_inodes_delta -= inodes_del->size();
  }

  // feature: add block
  for (int i = 0; i < bips_add.size(); ++i) {
    const auto& bip_add = bips_add[i];

    CHECK_NOTNULL(inodes_add);
    CHECK_EQ(inodes_add->size(), bips_add.size());
    const auto& inode = (*inodes_add)[i];

    CHECK(bip_add.IsInitialized()) << bip_add.InitializationErrorString();
    CHECK_GT(inode->blocks_size(), 0) << inode->id();
    const auto& bp = inode->blocks(inode->blocks_size() - 1);
    if (!(bip_add.block_id() == bp.blockid() &&
          bip_add.gen_stamp() == bp.genstamp() &&
          bip_add.num_bytes() == bp.numbytes() &&
          bip_add.state() == BlockInfoProto::kUnderConstruction)) {
      LOG(FATAL) << "bip_add_tbuc=" << ToJsonCompactString(bip_add)
                 << " bp=" << ToJsonCompactString(bp);
    }

    BlockID blk_id = bip_add.block_id();
    if (delta.max_block_id == kInvalidBlockID || blk_id > delta.max_block_id) {
      delta.max_block_id = blk_id;
    }
    if (delta.max_gs_v2 == kInvalidGenerationStamp ||
        bip_add.gen_stamp() > delta.max_gs_v2) {
      delta.max_gs_v2 = bip_add.gen_stamp();
    }

    DLOG(INFO) << "write last under construction bip B" << blk_id;
    CHECK(bip_add.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            EncodeBlockID(blk_id),
            bip_add.SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], EncodeBlockID(blk_id), "");
  }

  // Refer to MetaStorage::FlushBlockInfoProtos.
  for (const BlockInfoProto& bip : bips_mod) {
    std::string key = EncodeBlockID(bip.block_id());
    CHECK(bip.IsInitialized());
    switch (bip.state()) {
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery:
      case BlockInfoProto::kCommitted:
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued: {
        wb->Put(handles_[kLocalBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kPersisted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeprecated: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
        wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeleted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
      } break;
      default: {
        LOG(FATAL) << ToJsonCompactString(bip);
      } break;
    }
  }

  // modified parents
  if (parents) {
    for (auto& parent : *parents) {
      INode& in = *parent.inode;
      in.set_last_update_txid(txid);
      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
      in.SerializeToString(&value);
      wb->Put(handles_[kINodeDefaultCFIndex], key, value);
      FillSnapshotWriteBatch(*parent.snaplog, txid, wb.get());
      // Avoid verifying the old and new parents here, as the parent is only
      // protected by a read lock, not a write lock.
      // FillINodeKVVerifyVec(old_parent, new_parent);
    }
  }

  // old inode copies to verify
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (inodes_old) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    for (auto& inode_old : *inodes_old) {
      FillINodeKVVerifyVec(inode_old, verify_kvs.get());
    }
  }

  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTasks(std::move(wb), txid, delta, std::move(verify_kvs), dones);
}

void MetaStorage::OrderedCommitINodes(
    std::vector<INode>* inodes_add,
    std::vector<std::pair<INode, INode>>* inodes_mod,
    std::vector<INode>* inodes_del,
    std::vector<INode>* parents,
    std::vector<INode>* inodes_old,
    const std::vector<BlockInfoProto>& bips_mod,
    const std::vector<BlockInfoProto>& bips_add,
    int64_t txid,
    const std::vector<Closure*>& dones,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;

  auto wb = CreateWriteBatch();
  std::string key;
  std::string value;

  // added inodes (ID newly allocated)
  if (inodes_add) {
    for (auto& in : *inodes_add) {
      if (delta.max_inode_id == kInvalidINodeId ||
          in.id() > delta.max_inode_id) {
        delta.max_inode_id = in.id();
      }

      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
      in.set_create_txid(txid);
      in.set_last_update_txid(txid);
      in.SerializeToString(&value);
      wb->Put(handles_[kINodeDefaultCFIndex], key, value);

      delta.num_inodes_delta += 1;
      UpdateParentIndexWriteBatch(in, false, wb.get());
      UpdateLeaseWriteBatch(in, false, wb.get());
      UpdateWriteBackTaskWriteBatch(in, false, wb.get());
      UpdateDirPolicyWriteBatch(in, false, wb.get());
    }
  }

  // modified inodes (ID unchanged, path changed)
  if (inodes_mod) {
    for (auto& inode_mod : *inodes_mod) {
      const INode& src_in = inode_mod.first;
      INode& dst_in = inode_mod.second;
      std::string skey, dkey;

      CHECK_EQ(src_in.id(), dst_in.id());
      EncodeStoreKey(src_in.parent_id(), src_in.name(), src_in.id(), &skey);
      EncodeStoreKey(dst_in.parent_id(), dst_in.name(), dst_in.id(), &dkey);
      bool delete_src = (skey != dkey);
      dst_in.set_last_update_txid(txid);
      dst_in.SerializeToString(&value);

      if (!delete_src) {
        wb->Put(handles_[kINodeDefaultCFIndex], dkey, value);
        UpdateLeaseWriteBatch(dst_in, false, wb.get());
        UpdateWriteBackTaskWriteBatch(dst_in, false, wb.get());
      } else {
        wb->Delete(handles_[kINodeDefaultCFIndex], skey);
        UpdateParentIndexWriteBatch(src_in, true, wb.get());
        UpdateLeaseWriteBatch(src_in, true, wb.get());
        UpdateWriteBackTaskWriteBatch(src_in, true, wb.get());
        UpdateDirPolicyWriteBatch(src_in, false, wb.get());
        wb->Put(handles_[kINodeDefaultCFIndex], dkey, value);
        UpdateParentIndexWriteBatch(dst_in, false, wb.get());
        UpdateLeaseWriteBatch(dst_in, false, wb.get());
        UpdateWriteBackTaskWriteBatch(dst_in, false, wb.get());
      }
    }
  }

  // deleted inodes (ID to be cleaned)
  if (inodes_del) {
    for (auto& in : *inodes_del) {
      EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
      wb->Delete(handles_[kINodeDefaultCFIndex], key);

      delta.num_inodes_delta -= 1;
      UpdateParentIndexWriteBatch(in, true, wb.get());
      UpdateLeaseWriteBatch(in, true, wb.get());
      UpdateWriteBackTaskWriteBatch(in, true, wb.get());
      UpdateDirPolicyWriteBatch(in, true, wb.get());
      FillDeleteStatWriteBatch(in, wb.get());

      if (in.type() == INode_Type_kDirectory) {
        FillPendingDeleteWriteBatch(in, wb.get(), false);
      } else {
        FillDeprecatingBlockWriteBatch(in, wb.get());
      }
    }
  }

  // Refer to MetaStorage::FlushBlockInfoProtos.
  for (const BlockInfoProto& bip : bips_mod) {
    std::string key = EncodeBlockID(bip.block_id());
    CHECK(bip.IsInitialized());
    switch (bip.state()) {
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery:
      case BlockInfoProto::kCommitted:
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued: {
        wb->Put(handles_[kLocalBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kPersisted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeprecated: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        // According to
        // https://github.com/facebook/rocksdb/blob/v5.7.2/include/rocksdb/write_batch.h#L11,
        // updates are applied in the order they are added to the WriteBatch.
        // Therefore, even though the FillDeprecatingBlockWriteBatch function
        // call adds the block ID to kDeprecatingBlockCFIndex, we delete it
        // later at this point, resulting in the final effect that the block ID
        // is indeed deleted.
        wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
        wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
        wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
      } break;
      case BlockInfoProto::kDeleted: {
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
      } break;
      default: {
        LOG(FATAL) << ToJsonCompactString(bip);
      } break;
    }
  }

  // feature: add block
  for (int i = 0; i < bips_add.size(); ++i) {
    const auto& bip_add = bips_add[i];

    CHECK_NOTNULL(inodes_add);
    CHECK_EQ(inodes_add->size(), bips_add.size());
    const auto& inode = (*inodes_add)[i];

    CHECK(bip_add.IsInitialized()) << bip_add.InitializationErrorString();
    CHECK_GT(inode.blocks_size(), 0) << inode.id();
    const auto& bp = inode.blocks(inode.blocks_size() - 1);
    if (!(bip_add.block_id() == bp.blockid() &&
          bip_add.gen_stamp() == bp.genstamp() &&
          bip_add.num_bytes() == bp.numbytes() &&
          bip_add.state() == BlockInfoProto::kUnderConstruction)) {
      LOG(FATAL) << "bip_add_tbuc=" << ToJsonCompactString(bip_add)
                 << " bp=" << ToJsonCompactString(bp);
    }
    BlockID blk_id = bip_add.block_id();
    if (delta.max_block_id == kInvalidBlockID || blk_id > delta.max_block_id) {
      delta.max_block_id = blk_id;
    }
    if (delta.max_gs_v2 == kInvalidGenerationStamp ||
        bip_add.gen_stamp() > delta.max_gs_v2) {
      delta.max_gs_v2 = bip_add.gen_stamp();
    }

    DLOG(INFO) << "write last under construction bip B" << blk_id;
    CHECK(bip_add.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            EncodeBlockID(blk_id),
            bip_add.SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], EncodeBlockID(blk_id), "");
  }

  // modified parents
  if (parents) {
    for (auto& parent : *parents) {
      parent.set_last_update_txid(txid);
      EncodeStoreKey(parent.parent_id(), parent.name(), parent.id(), &key);
      parent.SerializeToString(&value);
      wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    }
  }

  // old inode copies to verify
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (inodes_old) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    for (auto& inode_old : *inodes_old) {
      FillINodeKVVerifyVec(inode_old, verify_kvs.get());
    }
  }

  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTasks(std::move(wb), txid, delta, std::move(verify_kvs), dones);
}

// customized API for concat
void MetaStorage::OrderedConcatINode(
    INode& target_inode,
    const INode* old_target_inode,
    const std::vector<INode>& src_inodes,
    const std::vector<BlockInfoProto>& target_bips,
    INode& parent,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  std::string key;
  std::string value;

  // target_inode
  if (delta.max_inode_id == kInvalidINodeId ||
      target_inode.id() > delta.max_inode_id) {
    delta.max_inode_id = target_inode.id();
  }

  EncodeStoreKey(
      target_inode.parent_id(), target_inode.name(), target_inode.id(), &key);
  target_inode.set_create_txid(txid);
  target_inode.set_last_update_txid(txid);
  target_inode.SerializeToString(&value);
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);

  // concat not add inode
  delta.num_inodes_delta += 0;
  UpdateParentIndexWriteBatch(target_inode, false, wb.get());
  UpdateLeaseWriteBatch(target_inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(target_inode, false, wb.get());
  UpdateDirPolicyWriteBatch(target_inode, false, wb.get());

  // deleted inodes (ID to be cleaned)
  for (const auto& in : src_inodes) {
    EncodeStoreKey(in.parent_id(), in.name(), in.id(), &key);
    wb->Delete(handles_[kINodeDefaultCFIndex], key);

    delta.num_inodes_delta -= 1;
    UpdateParentIndexWriteBatch(in, true, wb.get());
    UpdateLeaseWriteBatch(in, true, wb.get());
    UpdateWriteBackTaskWriteBatch(in, true, wb.get());
    UpdateDirPolicyWriteBatch(in, true, wb.get());
    FillDeleteStatWriteBatch(in, wb.get());
  }

  // Refer to MetaStorage::FlushBlockInfoProtos.
  for (const auto& bip : target_bips) {
    std::string key = EncodeBlockID(bip.block_id());
    wb->Put(handles_[kLocalBlockCFIndex], key, "");
    CHECK(bip.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex], key, bip.SerializeAsString());
  }

  // modified parents
  parent.set_last_update_txid(txid);
  EncodeStoreKey(parent.parent_id(), parent.name(), parent.id(), &key);
  parent.SerializeToString(&value);
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);

  auto verify_kvs = std::make_unique<KVVerifyVec>();
  if (old_target_inode) {
    FillINodeKVVerifyVec(*old_target_inode, verify_kvs.get());
  }
  for (const auto& src_inode : src_inodes) {
    FillINodeKVVerifyVec(src_inode, verify_kvs.get());
  }

  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::UpdateINode(const INode& inode) {
  auto wb = CreateWriteBatch();
  std::string key;
  std::string value;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  inode.SerializeToString(&value);
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);
  SynchronizedClosure done;
  PushINodeBGWriteTask(std::move(wb), 0 /*num_inodes_delta*/, &done);
  done.Await();
}

void MetaStorage::UpdateSnapshotINodesAsync(
    const std::vector<DeleteSnapshotINodeInfo>& inodes_del,
    Closure* done) {
  auto wb = CreateWriteBatch();
  for (const auto& delete_info : inodes_del) {
    wb->Delete(handles_[kSnapshotInodeCFIndex], delete_info.key);
    std::string snapshot_index_key;
    EncodeSnapshotINodeIndexKey(
        delete_info.id, delete_info.last_update_txid, &snapshot_index_key);
    wb->Delete(handles_[kSnapshotINodeIndexCFIndex], snapshot_index_key);
  }
  PushINodeBGWriteTask(std::move(wb), 0 /*num_inodes_delta*/, done);
}

void MetaStorage::OrderedCommitNop(int64_t txid) {
  PushINodeTXWriteTask(nullptr, txid, {}, nullptr, nullptr);
}

void MetaStorage::UpdateTtlATimes(const ATimeToBeUpdateProtos& atimes,
                                  int64_t txid,
                                  Closure* done) {
  auto wb = CreateWriteBatch();
  for (int i = 0; i < atimes.atime_to_be_updates_size(); i++) {
    const auto& atime = atimes.atime_to_be_updates(i);
    auto key = EncodeInteger((INodeID)atime.inode_id());
    wb->Put(handles_[kTtlATimeCFIndex],
            key,
            atime.SerializeAsString());
  }
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

Status MetaStorage::GetTtlATime(INodeID inode_id, ATimeToBeUpdate* atime) {
  std::string value;
  auto key = EncodeInteger(inode_id);
  auto s = rocks_db_->Get(
    rocksdb::ReadOptions(),
    handles_[kTtlATimeCFIndex],
    key,
    &value);
  if (s.code() == rocksdb::Status::Code::kOk) {
    atime->ParseFromArray(value.data(), value.size());
    return Status::OK();
  }

  if (s.code() == rocksdb::Status::Code::kNotFound) {
    return Status(Code::kFileNotFound);
  }
  LOG(ERROR) << "GetTtlATime from meta_storage failed, status: "
      << s.ToString();
  return Status(Code::kError, s.ToString());
}

void MetaStorage::DeleteTtlATime(INodeID inode_id) {
  LOG(INFO) << "DeleteTtlATime inode_id: " << inode_id;
  std::string key = EncodeInteger(inode_id);
  auto s = rocks_db_->Delete(
    rocksdb::WriteOptions(),
    handles_[kTtlATimeCFIndex],
    key);
  CHECK(s.ok()) << "DeleteTtlATime failed, status: " << s.ToString();
}


void MetaStorage::OrderedUpdateINode(INode* minode,
                                     const INode* old_inode,
                                     SnapshotLog& snaplog,
                                     int64_t txid,
                                     Closure* done,
                                     INodeStatChangeRecorder* recorder) {
  std::vector<INodeAndSnapshot> inodes_mod;
  std::vector<INode> inodes_old;
  if (old_inode) {
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = *old_inode;
  }
  inodes_mod.emplace_back(minode, &snaplog);
  OrderedCommitINodes(nullptr,
                      &inodes_mod,
                      nullptr,
                      nullptr,
                      nullptr,
                      nullptr,
                      &inodes_old,
                      {},
                      {},
                      txid,
                      {done},
                      recorder);
}

void MetaStorage::DeleteLastBlockInWriteBatch(const INode& inode,
                                              BlockID last_blk_to_del,
                                              rocksdb::WriteBatch* wb) {
  if (last_blk_to_del != kInvalidBlockID) {
    // Double check last_blk_to_del is not a valid block, why not CHECK it?
    if (inode.blocks_size() > 0 &&
        inode.blocks(inode.blocks_size() - 1).blockid() == last_blk_to_del) {
      // Unexpected case
      LOG(ERROR) << "UNEXPECTED: Unable to delete last blk B" << last_blk_to_del;
    } else {
      std::string key = EncodeBlockID(last_blk_to_del);
      wb->Put(handles_[kDeprecatingBlockCFIndex], key, "");
    }
  }
}

bool MetaStorage::IsValidToCommitBlockInfoChange(const BlockInfoProto& new_bip) {
  BlockInfoProto old_bip;
  bool found = GetBlockInfo(new_bip.block_id(), &old_bip);
  if (!found) {
    LOG(ERROR) << "BlockInfo not existed in DB to update, B" << new_bip.block_id();
  } else {
    CHECK(old_bip.block_id() == new_bip.block_id() &&
          old_bip.gen_stamp() <= new_bip.gen_stamp() &&
          old_bip.inode_id() == new_bip.inode_id())
        << "UnmatchedBlock, new bip: "
        << PBConverter::ToCompactJsonString(new_bip)
        << ", old bip: " << PBConverter::ToCompactJsonString(old_bip);
  }
  if (found && old_bip.state() != BlockInfoProto::kUnderConstruction) {
    LOG(INFO) << "Skip writing bip due to committed, B" << old_bip.block_id();
    return false;
  } else {
    return true;
  }
}

void MetaStorage::OrderedUpdateINodeAndDeleteLastBlock(
    const INode& inode,
    BlockID last_blk_to_del,
    const INode* old_inode,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  std::string key;
  std::string value;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  inode.SerializeToString(&value);
  auto wb = CreateWriteBatch();
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);
  UpdateLeaseWriteBatch(inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, false, wb.get());

  DeleteLastBlockInWriteBatch(inode, last_blk_to_del, wb.get());
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  DEBUG_RECORD_STAT(recorder);
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedAddBlock(INode* minode,
                                  const BlockInfoProto* penultimate_bip,
                                  const BlockInfoProto& last_bip,
                                  const INode* old_inode,
                                  SnapshotLog& snaplog,
                                  int64_t txid,
                                  Closure* done,
                                  INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  if (penultimate_bip != nullptr) {
    CHECK_GE(inode.blocks_size(), 2) << inode.id();
    const auto& bp = inode.blocks(inode.blocks_size() - 2);
    if (!(penultimate_bip->block_id() == bp.blockid() &&
          penultimate_bip->gen_stamp() == bp.genstamp() &&
          penultimate_bip->num_bytes() == bp.numbytes() &&
          penultimate_bip->state() != BlockInfoProto::kUnderConstruction)) {
      LOG(FATAL) << "penultimate_bip_tbc="
                 << PBConverter::ToCompactJsonString(*penultimate_bip)
                 << " bp=" << PBConverter::ToCompactJsonString(bp);
    }
    BlockID blk_id = penultimate_bip->block_id();
    VLOG(8) << "write committed penultimate bip B" << blk_id;
    CHECK(penultimate_bip->IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            EncodeBlockID(blk_id),
            penultimate_bip->SerializeAsString());
    if (penultimate_bip->state() == BlockInfoProto::kPersisted) {
      wb->Delete(handles_[kLocalBlockCFIndex], EncodeBlockID(blk_id));
    }
  }
  {
    CHECK(last_bip.IsInitialized()) << last_bip.InitializationErrorString();
    CHECK_GT(inode.blocks_size(), 0) << inode.id();
    const auto& bp = inode.blocks(inode.blocks_size() - 1);
    if (!(last_bip.block_id() == bp.blockid() &&
          last_bip.gen_stamp() == bp.genstamp() &&
          last_bip.num_bytes() == bp.numbytes() &&
          last_bip.state() == BlockInfoProto::kUnderConstruction)) {
      LOG(FATAL) << "last_bip_tbuc=" << ToJsonCompactString(last_bip)
                 << " bp=" << ToJsonCompactString(bp);
    }
    BlockID blk_id = last_bip.block_id();
    DLOG(INFO) << "write last under construction bip B" << blk_id;
    CHECK(last_bip.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            EncodeBlockID(blk_id),
            last_bip.SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], EncodeBlockID(blk_id), "");
  }
  {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    FillSnapshotWriteBatch(snaplog, txid, wb.get());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  delta.max_block_id = last_bip.block_id();
  delta.max_gs_v2 = last_bip.gen_stamp();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedAbandonBlock(const INode& inode,
                                      BlockID last_blk_to_be_abandoned,
                                      const INode* old_inode,
                                      int64_t txid,
                                      Closure* done,
                                      INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  CHECK_NE(last_blk_to_be_abandoned, kInvalidBlockID)
      << "txid=" << txid << " inode_id=" << inode.id();
  DeleteLastBlockInWriteBatch(inode, last_blk_to_be_abandoned, wb.get());
  {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedUpdatePipeline(const INode& inode,
                                        const BlockInfoProto& last_bip_tbuc,
                                        const INode* old_inode,
                                        int64_t txid,
                                        Closure* done,
                                        INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  {
    CHECK(inode.IsInitialized()) << inode.InitializationErrorString();
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  {
    CHECK(last_bip_tbuc.IsInitialized())
        << last_bip_tbuc.InitializationErrorString();
    CHECK_GT(inode.blocks_size(), 0) << inode.id();
    const auto& bp = inode.blocks(inode.blocks_size() - 1);
    if (!(last_bip_tbuc.block_id() == bp.blockid() &&
          last_bip_tbuc.gen_stamp() == bp.genstamp() &&
          last_bip_tbuc.num_bytes() == bp.numbytes() &&
          last_bip_tbuc.state() == BlockInfoProto::kUnderConstruction)) {
      LOG(FATAL) << "last_bip_tbuc=" << ToJsonCompactString(last_bip_tbuc)
                 << " bp=" << ToJsonCompactString(bp);
    }
    std::string key = EncodeBlockID(last_bip_tbuc.block_id());
    CHECK(last_bip_tbuc.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            key,
            last_bip_tbuc.SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], key, "");
  }
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedCommitLastBlock(INode* minode,
                                         const BlockInfoProto& last_bip_tbuc,
                                         const INode* old_inode,
                                         SnapshotLog& snaplog,
                                         int64_t txid,
                                         Closure* done,
                                         INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  if (last_bip_tbuc.IsInitialized()) {
    CHECK_EQ(last_bip_tbuc.state(), BlockInfoProto::kCommitted)
        << last_bip_tbuc.block_id();
    std::string key = EncodeBlockID(last_bip_tbuc.block_id());
    CHECK(last_bip_tbuc.IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            key,
            last_bip_tbuc.SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], key, "");
    delta.max_block_id = last_bip_tbuc.block_id();
    delta.max_gs_v2 = last_bip_tbuc.gen_stamp();
  }
  {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    FillSnapshotWriteBatch(snaplog, txid, wb.get());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedFsync(INode* minode,
                               const BlockInfoProto* last_bip_tbuc,
                               const INode* old_inode,
                               SnapshotLog& snaplog,
                               int64_t txid,
                               Closure* done,
                               INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  if (last_bip_tbuc != nullptr) {
    CHECK_EQ(last_bip_tbuc->state(), BlockInfoProto::kUnderConstruction)
        << last_bip_tbuc->block_id();
    std::string key = EncodeBlockID(last_bip_tbuc->block_id());
    CHECK(last_bip_tbuc->IsInitialized());
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            key,
            last_bip_tbuc->SerializeAsString());
    wb->Put(handles_[kLocalBlockCFIndex], key, "");
    delta.max_block_id = last_bip_tbuc->block_id();
    delta.max_gs_v2 = last_bip_tbuc->gen_stamp();
  }
  {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    FillSnapshotWriteBatch(snaplog, txid, wb.get());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedCloseFile(INode* minode,
                                   const BlockInfoProto* last_bip,
                                   BlockID last_blk_to_be_abandoned,
                                   const INode* old_inode,
                                   SnapshotLog& snaplog,
                                   int64_t txid,
                                   Closure* done,
                                   INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  DeleteLastBlockInWriteBatch(inode, last_blk_to_be_abandoned, wb.get());
  if (last_bip) {
    CHECK_GT(inode.blocks_size(), 0) << inode.id();
    const auto& bp = inode.blocks(inode.blocks_size() - 1);
    if (!(last_bip->block_id() == bp.blockid() &&
          last_bip->gen_stamp() == bp.genstamp() &&
          last_bip->num_bytes() == bp.numbytes())) {
      LOG(FATAL) << "last_bip: " << ToJsonCompactString(*last_bip)
                 << ", bp= " << ToJsonCompactString(bp);
    }
    BlockID blk_id = last_bip->block_id();
    CHECK_NE(last_bip->state(), BlockInfoProto::kUnderConstruction) << blk_id;
    VLOG(10) << "write committed last bip B" << blk_id;
    std::string key = EncodeBlockID(blk_id);
    CHECK(last_bip->IsInitialized());
    wb->Put(
        handles_[kBlockInfoProtoCFIndex], key, last_bip->SerializeAsString());
    if (last_bip->state() == BlockInfoProto::kPersisted) {
      wb->Delete(handles_[kLocalBlockCFIndex], key);
    }
  }
  {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
    FillSnapshotWriteBatch(snaplog, txid, wb.get());
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedUpdateINodeAndAddBlock(
    const INode& inode,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  BlockInfoProto penultimate_bip;
  if (MakeCommittedPenultimateBlockInfoProto(inode, &penultimate_bip) &&
      IsValidToCommitBlockInfoChange(penultimate_bip)) {
    VLOG(8) << "write penultimate bip B" << penultimate_bip.block_id();
    std::string value;
    CHECK(penultimate_bip.SerializeToString(&value));
    wb->Put(handles_[kBlockInfoProtoCFIndex],
            EncodeBlockID(penultimate_bip.block_id()),
            value);
    if (delta.max_block_id == kInvalidBlockID ||
        penultimate_bip.block_id() > delta.max_block_id) {
      delta.max_block_id = penultimate_bip.block_id();
    }
    if (delta.max_gs_v2 == kInvalidGenerationStamp ||
        penultimate_bip.gen_stamp() > delta.max_gs_v2) {
      delta.max_gs_v2 = penultimate_bip.gen_stamp();
    }
  }
  BlockInfoProto last_bip;
  // inode have at least one block, so MakeLastBlockInfoProto always succeeded.
  CHECK(MakeLastBlockInfoProto(inode, &last_bip));
  {
    last_bip.set_state(BlockInfoProto::kUnderConstruction);
    VLOG(8) << "write uc last bip B" << last_bip.block_id();
    std::string key = EncodeBlockID(last_bip.block_id());
    std::string value;
    CHECK(last_bip.SerializeToString(&value));
    wb->Put(handles_[kLocalBlockCFIndex], key, "");
    wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
    if (delta.max_block_id == kInvalidBlockID ||
        last_bip.block_id() > delta.max_block_id) {
      delta.max_block_id = last_bip.block_id();
    }
    if (delta.max_gs_v2 == kInvalidGenerationStamp ||
        last_bip.gen_stamp() > delta.max_gs_v2) {
      delta.max_gs_v2 = last_bip.gen_stamp();
    }
  }
  {
    std::string key;
    std::string value;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    inode.SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }

  delta.max_inode_id = inode.id();
  DEBUG_RECORD_STAT(recorder);
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);
}

// Close the file and commit last block
void MetaStorage::OrderedUpdateINodeAndFinalizeBlocks(
    const INode& inode,
    BlockID last_blk_to_del,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  DeleteLastBlockInWriteBatch(inode, last_blk_to_del, wb.get());

  BlockInfoProto last_bip;
  if (MakeLastBlockInfoProto(inode, &last_bip)) {
    if (IsValidToCommitBlockInfoChange(last_bip)) {
      last_bip.set_state(BlockInfoProto::kComplete);
      VLOG(8) << "write committed last bip B" << last_bip.block_id();
      std::string key = EncodeBlockID(last_bip.block_id());
      std::string value;
      CHECK(last_bip.SerializeToString(&value));
      wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
    } else {
      LOG(ERROR) << "Skip write committed last bip B" << last_bip.block_id();
    }
  }
  {
    std::string key;
    std::string value;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    inode.SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  }

  delta.max_inode_id = inode.id();
  DEBUG_RECORD_STAT(recorder);
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);
}

void MetaStorage::ScanSnapshotRoot(
    const std::function<bool(INodeID, const std::string&)>& cb) const {
  auto iter = std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kSnapshotRootInfoCFIndex]));

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    auto id = DecodeInteger<INodeID>(iter->key());
    CHECK_NE(id, kInvalidINodeId)
        << "ScanSnapshotRoot invalid key: " << iter->key().ToString(true);
    if (!cb(id, iter->value().ToString())) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
}

void MetaStorage::OrderedUpdateINodeAndSnapshotData(
    const INode& inode,
    WriteSnapshotDataMode write_mode,
    const std::string* src,
    const uint64_t* new_snapshot_id,
    int64_t txid,
    Closure* done) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();

  switch (write_mode) {
    case ALLOW_SNAPSHOT:
      wb->Put(
          handles_[kSnapshotRootInfoCFIndex], EncodeInteger(inode.id()), *src);
      break;
    case DISALLOW_SNAPSHOT:
      wb->Delete(handles_[kSnapshotRootInfoCFIndex], EncodeInteger(inode.id()));
      break;
    case CREATE_SNAPSHOT:
      delta.max_snapshot_id = *new_snapshot_id;
      break;
    case DELETE_SNAPSHOT:
      break;
    case RENAME_SNAPSHOT:
      break;
    default:
      LOG(FATAL) << "Unsupported WriteSnapshotDataMode: "
                 << static_cast<int>(write_mode);
  }
  std::string key;
  std::string value;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  inode.SerializeToString(&value);
  wb->Put(handles_[kINodeDefaultCFIndex], key, value);

  delta.max_inode_id = inode.id();
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);
}

void MetaStorage::OrderedPutNameSystemInfo(const std::string& key,
                                           cnetpp::base::StringPiece value,
                                           int64_t txid,
                                           Closure* done) {
  auto wb = CreateWriteBatch();
  wb->Put(handles_[kNameSystemInfoCFIndex],
          rocksdb::Slice(key.data(), key.length()),
          rocksdb::Slice(value.data(), value.length()));
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

void MetaStorage::OrderedPutBlockId(const uint64_t block_id,
                                    int64_t txid,
                                    Closure* done) {
  NameSpaceInfoDelta delta;
  delta.max_block_id = block_id;
  auto wb = CreateWriteBatch();
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);
}

void MetaStorage::OrderedPutGenerationStampV2(const uint64_t gs_v2,
                                              int64_t txid,
                                              Closure* done) {
  NameSpaceInfoDelta delta;
  delta.max_gs_v2 = gs_v2;
  auto wb = CreateWriteBatch();
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);
}

void MetaStorage::OrderedPutAccessCounterSnapshot(
    const std::string& key,
    cnetpp::base::StringPiece value,
    int64_t txid,
    Closure* done) {
  auto wb = CreateWriteBatch();
  wb->Put(handles_[kAccessCounterCFIndex],
          rocksdb::Slice(key.data(), key.length()),
          rocksdb::Slice(value.data(), value.length()));
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

void MetaStorage::TestOnlyDeleteINode(const INode& inode) {
  std::string key;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);

  auto wb = CreateWriteBatch();
  int64_t num_inodes_delta = 0;
  wb->Delete(handles_[kINodeDefaultCFIndex], key);
  num_inodes_delta += UpdateParentIndexWriteBatch(inode, true, wb.get());
  FillPendingDeleteWriteBatch(inode, wb.get(), false);
  FillDeprecatingBlockWriteBatch(inode, wb.get());
  FillDeleteStatWriteBatch(inode, wb.get());
  UpdateLeaseWriteBatch(inode, true, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, true, wb.get());
  UpdateDirPolicyWriteBatch(inode, true, wb.get());

  SynchronizedClosure done;
  PushINodeBGWriteTask(std::move(wb), num_inodes_delta, &done);
  done.Await();

  VLOG(10) << "Delete inode successfully: " << inode.ShortDebugString();
}

void MetaStorage::DeletePDINodesAsync(const std::vector<INode>& inodes,
                                      Closure* done) {
  auto wb = CreateWriteBatch();
  for (INode inode : inodes) {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    FillPendingDeleteWriteBatch(inode, wb.get(), true);
    FillDeprecatingBlockWriteBatch(inode, wb.get());
    FillDeleteStatWriteBatch(inode, wb.get());
    UpdateLeaseWriteBatch(inode, true, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, true, wb.get());
    UpdateDirPolicyWriteBatch(inode, true, wb.get());
  }

  PushINodeBGWriteTask(std::move(wb), 0, done);
}

void MetaStorage::DeleteINodesAsync(const std::vector<INode>& inodes,
                                    Closure* done) {
  auto wb = CreateWriteBatch();
  int64_t num_inodes_delta = 0;
  for (auto& inode : inodes) {
    std::string key;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    wb->Delete(handles_[kINodeDefaultCFIndex], key);
    FillDeprecatingBlockWriteBatch(inode, wb.get());
    FillDeleteStatWriteBatch(inode, wb.get());
    num_inodes_delta += UpdateParentIndexWriteBatch(inode, true, wb.get());
    UpdateLeaseWriteBatch(inode, true, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, true, wb.get());
  }
  PushINodeBGWriteTask(std::move(wb), num_inodes_delta, done);
}

void MetaStorage::OrderedDeleteINode(
    const INode& inode,
    int64_t txid,
    Closure* done,
    INode* parent,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();
  std::string key;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  wb->Delete(handles_[kINodeDefaultCFIndex], key);
  FillDeprecatingBlockWriteBatch(inode, wb.get());
  FillDeleteStatWriteBatch(inode, wb.get());

  if (inode.type() == INode_Type_kDirectory) {
    FillPendingDeleteWriteBatch(inode, wb.get(), false);
    UpdateDirPolicyWriteBatch(inode, true, wb.get());
  }

  delta.num_inodes_delta = UpdateParentIndexWriteBatch(inode, true, wb.get());
  UpdateLeaseWriteBatch(inode, true, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, true, wb.get());

  // update parent inode's mtime
  if (parent) {
    parent->set_last_update_txid(txid);
    std::string pkey;
    std::string pvalue;
    EncodeStoreKey(parent->parent_id(), parent->name(), parent->id(), &pkey);
    parent->SerializeToString(&pvalue);
    wb->Put(handles_[kINodeDefaultCFIndex], pkey, pvalue);
  }

  DEBUG_RECORD_STAT(recorder);
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, nullptr, done);

  VLOG(10) << "Delete inode successfully. pid:" << inode.parent_id()
           << " id:" << inode.id() << " name:" << inode.name()
           << ", type: " << inode.type();
}

void MetaStorage::OrderedUpdateINodeMergeBlock(
    INode* minode,
    const INode* old_inode,
    const BlockInfoProto& bip,
    const std::vector<BlockProto>& depred_blks,
    SnapshotLog& snaplog,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;
  auto wb = CreateWriteBatch();
  std::string key;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
  FillSnapshotWriteBatch(snaplog, txid, wb.get());
  UpdateLeaseWriteBatch(inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, false, wb.get());

  CHECK(bip.IsInitialized());
  wb->Put(handles_[kBlockInfoProtoCFIndex],
          EncodeBlockID(bip.block_id()),
          bip.SerializeAsString());
  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction: {
      // Create merging block
      wb->Put(handles_[kLocalBlockCFIndex], EncodeBlockID(bip.block_id()), "");
    } break;
    case BlockInfoProto::kComplete: {
      // TOS_LOCAL, LOCAL
      wb->Put(handles_[kLocalBlockCFIndex], EncodeBlockID(bip.block_id()), "");
    } break;
    case BlockInfoProto::kPersisted: {
      // TOS_MANAGED
      wb->Delete(handles_[kLocalBlockCFIndex], EncodeBlockID(bip.block_id()));
    } break;
    default: {
      LOG_WITH_LEVEL(ERROR) << "Unexpected bip state " << bip.ShortDebugString();
      MFC(LoggerMetrics::Instance().error_)->Inc();
    } break;
  }
  for (auto& b : depred_blks) {
    std::string key = EncodeBlockID(b.blockid());
    wb->Put(handles_[kDeprecatingBlockCFIndex], key, "");
  }
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  // XXX old_inode may not be same with inode_from_ms
  delta.max_inode_id = inode.id();
  delta.max_block_id = bip.block_id();
  delta.max_gs_v2 = bip.gen_stamp();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::PinINode(INode* minode,
                           const INode* old_inode,
                           SnapshotLog& snaplog,
                           const JobInfoOpBody& job,
                           const ManagedJobId& cancel_job_id,
                           int64_t txid,
                           Closure* done,
                           INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  std::string key;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
  if (inode.pin_status().ttl() > 0) {
    std::string key;
    EncodeStoreKey(inode.pin_status().ttl(), "", inode.id(), &key);
    wb->Put(handles_[kINodeAttrTtlCFIndex], key, inode.SerializeAsString());
  }
  if (job.IsInitialized()) {
    wb->Put(handles_[kJobInfoCFIndex], job.job_id(), job.SerializeAsString());
  }
  if (!cancel_job_id.empty()) {
    wb->Delete(handles_[kJobInfoCFIndex], cancel_job_id);
  }
  FillSnapshotWriteBatch(snaplog, txid, wb.get());
  UpdateLeaseWriteBatch(inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::ReconcileINodeAttrs(
    INode* minode,
    const INode* old_inode,
    SnapshotLog& snaplog,
    const std::vector<ManagedJobId>& cancel_job_id,
    const std::set<int64_t>& expired_ttl,
    const std::set<int64_t>& new_ttl,
    int64_t txid,
    Closure* done,
    INodeStatChangeRecorder* recorder) {
  NameSpaceInfoDelta delta;
  minode->set_last_update_txid(txid);
  const INode& inode = *minode;

  auto wb = CreateWriteBatch();
  std::string key;
  EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
  wb->Put(handles_[kINodeDefaultCFIndex], key, inode.SerializeAsString());
  for (auto& job : cancel_job_id) {
    wb->Delete(handles_[kJobInfoCFIndex], job);
  }
  for (auto t : expired_ttl) {
    std::string key;
    EncodeStoreKey(t, "", inode.id(), &key);
    wb->Delete(handles_[kINodeAttrTtlCFIndex], key);
  }
  for (auto t : new_ttl) {
    std::string key;
    EncodeStoreKey(t, "", inode.id(), &key);
    wb->Put(handles_[kINodeAttrTtlCFIndex], key, inode.SerializeAsString());
  }
  FillSnapshotWriteBatch(snaplog, txid, wb.get());
  UpdateLeaseWriteBatch(inode, false, wb.get());
  UpdateWriteBackTaskWriteBatch(inode, false, wb.get());
  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_inode, verify_kvs.get());
  }
  delta.max_inode_id = inode.id();
  RECORD_STAT_TO_WB(recorder, wb.get(), handles_[kINodeStatCFIndex]);
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

uint64_t MetaStorage::GetINodeParentId(uint64_t child) {
  if (child == kRootINodeId) {
    return kRootINodeId;
  }
  INodeParentInfoPB parent_info;
  if (GetINodeParentInfo(child, &parent_info) != kOK) {
    return kInvalidINodeId;
  }
  return parent_info.parent_id();
}

StatusCode MetaStorage::GetINodeParentInfo(const INodeID child,
                                           INodeParentInfoPB* parent_info) {
  std::string key;
  EncodeParentIndexKey(child, &key);
  auto opt = rocksdb::ReadOptions();
  std::string value;

  ROCKSDB_PERF_START(GetINodeParentInfo);
  auto status = rocks_db_->Get(opt, handles_[kINodeIndexCFIndex], key, &value);
  ROCKSDB_PERF_END(GetINodeParentInfo, key);
  if (status.ok()) {
    if (parent_info->ParseFromString(value)) {
      return kOK;
    }
    LOG(FATAL) << "Failed to ParseFromArray, child: " << child;
    return kError;
  }
  CHECK(status.IsNotFound());
  return kFileNotFound;
}

StatusCode MetaStorage::GetDatanodeInfo(const DatanodeID dn_id,
                                        DatanodeInfoEntryPB* pb) {
  std::string key;
  EncodeDatanodeInfoKey(dn_id, &key);
  auto opt = rocksdb::ReadOptions();
  std::string value;

  ROCKSDB_PERF_START(GetDatanodeInfo);
  auto status =
      rocks_db_->Get(opt, handles_[kDatanodeInfoCFIndex], key, &value);
  ROCKSDB_PERF_END(GetDatanodeInfo, key);
  if (status.ok()) {
    if (pb->ParseFromString(value)) {
      return kOK;
    }
    LOG(FATAL) << "Failed to ParseFromString, dn_id: " << dn_id;
    return kError;
  }

  CHECK(status.IsNotFound());
  return kFileNotFound;
}

int64_t MetaStorage::NumINodes() const {
  return num_inodes_;
}

void MetaStorage::ParentMapStats(std::vector<size_t>& v) const {
}

void MetaStorage::FillPendingDeleteWriteBatch(const INode& inode,
                                              rocksdb::WriteBatch* wb,
                                              bool erase) {
  CHECK(wb);

  if (inode.type() != INode_Type_kSymLink) {  // do nothing with symlink
    auto id = platform::HostToBigEndian(inode.id());

    if (!erase) {
      std::string value = inode.SerializeAsString();
      wb->Put(handles_[kINodePendingDeleteCFIndex],
              rocksdb::Slice(reinterpret_cast<const char*>(&id), sizeof(id)),
              value);
    } else {
      wb->Delete(
          handles_[kINodePendingDeleteCFIndex],
          rocksdb::Slice(reinterpret_cast<const char*>(&id), sizeof(id)));
    }
  }
}

void MetaStorage::FillDeprecatingBlockWriteBatch(const INode& inode,
                                                 rocksdb::WriteBatch* wb) {
  CHECK_NOTNULL(wb);
  if (inode.type() != INode_Type_kFile) {
    return;
  }
  for (const cloudfs::BlockProto& bp : inode.blocks()) {
    std::string key = EncodeBlockID(bp.blockid());
    wb->Put(handles_[kDeprecatingBlockCFIndex], key, "");
  }
  for (const MergeBlockContext& ctx : inode.mergingblocks()) {
    std::string key = EncodeBlockID(ctx.block().blockid());
    wb->Put(handles_[kDeprecatingBlockCFIndex], key, "");
  }
  for (const cloudfs::BlockProto& bp : inode.mergedblocks()) {
    std::string key = EncodeBlockID(bp.blockid());
    wb->Put(handles_[kDeprecatingBlockCFIndex], key, "");
  }
}

void MetaStorage::FillDeleteStatWriteBatch(const INode& inode,
                                           rocksdb::WriteBatch* wb) {
  CHECK_NOTNULL(wb);
  if (inode.type() != INode_Type_kDirectory) {
    return;
  }
  uint64_t key = platform::HostToBigEndian(inode.id());
  auto key_slice =
      rocksdb::Slice(reinterpret_cast<const char*>(&key), sizeof(key));
  wb->Delete(handles_[kINodeStatCFIndex], key_slice);
}

void MetaStorage::FillNameSpaceInfoWriteBatch(
    const char* key,
    uint64_t value,
    std::shared_ptr<rocksdb::WriteBatchBase> wb) {
  uint64_t write_value = platform::HostToBigEndian(value);
  wb->Put(handles_[kNameSystemInfoCFIndex],
          rocksdb::Slice(key, strlen(key)),
          rocksdb::Slice(reinterpret_cast<const char*>(&write_value),
                         sizeof(write_value)));
}

void MetaStorage::FillSnapshotWriteBatch(SnapshotLog& snaplog,
                                         int64_t txid,
                                         rocksdb::WriteBatch* wb) {
  if (!snaplog.IsInitialized() || !FLAGS_enable_snapshot_feature) {
    // no snapshot inode to write or snapshot feature disabled
    return;
  }
  CHECK(snaplog.IsInitialized());

  INode& inode = *snaplog.mutable_inode();
  uint64_t snapshot_root_id = snaplog.snapshot_root_id();

  if (VLOG_IS_ON(8)) {
    LOG(INFO) << absl::StrFormat(
        "Write snapshot inode %s snapshot_root_id %lu",
        inode.ShortDebugString(), snapshot_root_id);
  }

  if (inode.delete_txid() == kInvalidTxId) {
    inode.set_delete_txid(txid);
  }
  inode.set_create_txid(txid);

  std::string snapshot_key;
  EncodeSnapshotINodeKey(inode.parent_id(),
                         inode.name(),
                         inode.id(),
                         inode.last_update_txid(),
                         snapshot_root_id,
                         &snapshot_key);
  std::string snapshot_value;
  inode.SerializeToString(&snapshot_value);
  wb->Merge(handles_[kSnapshotInodeCFIndex], snapshot_key, snapshot_value);

  std::string snapshot_index_key;
  EncodeSnapshotINodeIndexKey(
      inode.id(), inode.last_update_txid(), &snapshot_index_key);
  std::string snapshot_index_value = EncodeInteger(inode.parent_id());
  wb->Merge(handles_[kSnapshotINodeIndexCFIndex],
            snapshot_index_key,
            snapshot_index_value);
}

void MetaStorage::FillINodeKVVerifyVec(const INode& inode,
                                       KVVerifyVec* verify_kvs) {
  // The root inode might not exist on disk.
  if (inode.id() != kRootINodeId) {
    std::string key, value;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    verify_kvs->emplace_back(
        kINodeDefaultCFIndex, key, inode.SerializeAsString());
  }
}

MetaStorageIterHolderPtr MetaStorage::GetIterator() {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kINodeDefaultCFIndex]));
  return holder;
}

MetaStorageIterHolderPtr MetaStorage::GetIteratorNull() {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  return holder;
}

MetaStorageIterHolderPtr MetaStorage::GetIteratorWithPrefix(
    uint64_t parent_id,
    const std::string& name,
    uint32_t cf_idx) {
  std::string iterate_key_upper_bound;
  EncodeStoreKey(parent_id, name, UINT64_MAX, &iterate_key_upper_bound);
  return GetIteratorWithPrefix(iterate_key_upper_bound, cf_idx);
}

MetaStorageIterHolderPtr MetaStorage::GetIteratorWithPrefix(
    const std::string& iterate_key_upper_bound,
    uint32_t cf_idx) {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  holder->upper_bound_key_prefix_ = iterate_key_upper_bound;
  holder->slice_ =
      std::make_unique<rocksdb::Slice>(holder->upper_bound_key_prefix_);

  auto opt = rocksdb::ReadOptions();
  opt.prefix_same_as_start = true;
  opt.iterate_upper_bound = holder->slice_.get();
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(opt, handles_[cf_idx]));
  return holder;
}

MetaStorageIterHolderPtr MetaStorage::GetSubINodeIterator(INodeID parent_id) {
  // https://bytedance.larkoffice.com/docx/O2w0dZMb2oVfdXxwqKTcq5MwnKb
  auto holder = std::make_unique<MetaStorageIterHolder>();
  // Refer to MetaStorage::GetIteratorWithPrefix.
  CHECK(EncodeStoreKey(
      parent_id,
      // Using std::string(FLAGS_max_component_length,
      // static_cast<char>(0xFF)) is more reliable. However, since 0xFF is not
      // a valid UTF-8 byte, using a single 0xFF is sufficient.
      // https://en.wikipedia.org/wiki/UTF-8#Invalid_sequences_and_error_handling
      // Bytes that never appear in UTF-8: 0xC0, 0xC1, 0xF5–0xFF.
      // As shown below, the upper bound is greater than any sub-inodes:
      // upperbound=|parent_id(8B)|/|name=0xFF(1B)|0x00(1B)|inode_id=0xFF..FF(8B)|
      //   subinode=|parent_id(8B)|/|name=abcdefgh|0x00(1B)|inode_id(8B)|
      std::string(1, static_cast<char>(0xFF)),
      UINT64_MAX,
      &holder->upper_bound_key_prefix_));
  holder->slice_ =
      std::make_unique<rocksdb::Slice>(holder->upper_bound_key_prefix_);
  auto opt = rocksdb::ReadOptions();
  opt.iterate_upper_bound = holder->slice_.get();
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(opt, handles_[kINodeDefaultCFIndex]));
  return holder;
}

INodeStatItersHolder MetaStorage::GetINodeStatIterators() {
  std::vector<MetaStorageIterPtr> iterators;
  CHECK(rocks_db_
            ->NewIterators(rocksdb::ReadOptions(),
                           std::vector<rocksdb::ColumnFamilyHandle*>{
                               handles_.at(kNameSystemInfoCFIndex),
                               handles_.at(kINodeDefaultCFIndex),
                               handles_.at(kINodeStatCFIndex)},
                           &iterators)
            .ok());
  CHECK_EQ(iterators.size(), 3);
  return INodeStatItersHolder(iterators[0], iterators[1], iterators[2]);
}

MetaStorageSnapHolderPtr MetaStorage::GetSnapshot() {
  return std::make_unique<MetaStorageSnapHolder>(this, AcquireSnapshot());
}

MetaStorageSnapPtr MetaStorage::AcquireSnapshot() {
  return rocks_db_->GetSnapshot();
}

void MetaStorage::ReleaseSnapshot(MetaStorageSnapPtr snapshot) {
  rocks_db_->ReleaseSnapshot(snapshot);
}

// XXX All iterators derived shall be released before snapshot.
MetaStorageIterHolderPtr MetaStorage::GetIterator(
    MetaStorageSnapPtr snapshot, int cf_idx) {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  auto opt = rocksdb::ReadOptions();
  opt.snapshot = snapshot;
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(opt, handles_[cf_idx]));
  return holder;
}

MetaStorageIterHolderPtr MetaStorage::GetIteratorWithPrefix(
    MetaStorageSnapPtr snapshot,
    INodeID parent_id,
    const std::string& name) {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  auto opt = rocksdb::ReadOptions();
  opt.snapshot = snapshot;
  // Refer to the MetaStorage::GetIteratorWithPrefix.
  std::string iterate_key_upper_bound;
  EncodeStoreKey(parent_id, name, UINT64_MAX, &iterate_key_upper_bound);
  holder->upper_bound_key_prefix_ = iterate_key_upper_bound;
  holder->slice_ =
      std::make_unique<rocksdb::Slice>(holder->upper_bound_key_prefix_);
  opt.prefix_same_as_start = true;
  opt.iterate_upper_bound = holder->slice_.get();
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(opt, handles_[kINodeDefaultCFIndex]));
  return holder;
}

StatusCode MetaStorage::GetINode(MetaStorageSnapPtr snapshot,
                                 INodeID parent_id,
                                 const std::string& name,
                                 INode* inode) {
  MetaStorageIterHolderPtr iter_holder;
  MetaStorageIterPtr iter = nullptr;
  if (snapshot != nullptr) {
    iter_holder = this->GetIteratorWithPrefix(snapshot, parent_id, name);
    iter = iter_holder->iter();
  }

  auto sc = this->GetINode(parent_id, name, inode, iter);
  return sc;
}

bool MetaStorage::Get(const std::string& key, std::string* value) {
  rocksdb::Status s = rocks_db_->Get(rocksdb::ReadOptions(),
                                     handles_[kINodeDefaultCFIndex], key, value);
  if (!s.ok()) {
    if (s.IsNotFound()) {
      return false;
    }
    LOG(FATAL) << "Failed to get key: " << key << ", error: " << s.ToString();
  }
  return true;
}

StatusCode MetaStorage::GetINode(uint64_t parent_id,
                                 const std::string& name,
                                 INode* inode,
                                 MetaStorageIterPtr iter) {
  CHECK_NOTNULL(inode);
  inode->Clear();

  MFC(metrics_.get_inode_scan_num_)->Inc();
  StopWatch sw(metrics_.get_inode_scan_time_);
  sw.Start();

  std::string key_prefix;
  EncodeScanKey(parent_id, name, &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetIteratorWithPrefix(parent_id, name);
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(GetINode);
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetINode, key_prefix);
  if (iter->Valid()) {
    if (IsPrefixScanKey(cnetpp::base::StringPiece(iter->key().data(),
                                                  iter->key().size()),
                        key_prefix)) {
      if (inode->ParseFromArray(iter->value().data(), iter->value().size())) {
        return kOK;
      }
      LOG(FATAL) << "Failed to ParseFromArray, parent_id: " << parent_id
                 << ", name: " << name;
      return kError;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return st.ok() ? kFileNotFound : kError;
}

StatusCode MetaStorage::GetINode(uint64_t id,
                                 INode* inode,
                                 MetaStorageIterPtr iter) {
  CHECK_NOTNULL(inode);
  inode->Clear();

  MFC(metrics_.get_inode_num_)->Inc();
  StopWatch sw(metrics_.get_inode_time_);
  sw.Start();

  if (id == kRootINodeId) {
    *inode = GetRootINode();
    return kOK;
  }
  std::string key_prefix;
  INodeParentInfoPB parent_info;

  if (GetINodeParentInfo(id, &parent_info) != kOK) {
    return kFileNotFound;
  }
  auto code =
      GetINode(parent_info.parent_id(), parent_info.name(), inode, iter);
  if (code == kOK && inode->id() != id) {
    LOG(ERROR) << "GetINode error, " << "id: " << id << ", parent: "
               << parent_info.ShortDebugString() << ", inode: "
               << inode->ShortDebugString();
    return kFileNotFound;
  }
  return code;
}

StatusCode MetaStorage::GetINodeFromSnapshot(uint64_t parent_id,
                                             const std::string& name,
                                             uint64_t read_txid,
                                             INode* inode) {
  CHECK_NOTNULL(inode);
  inode->Clear();
  std::string key_prefix;
  EncodeScanKey(parent_id, name, &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  iter_holder = GetIteratorWithPrefix(parent_id, name, kSnapshotInodeCFIndex);
  auto iter = CHECK_NOTNULL(iter_holder->iter());

  ROCKSDB_PERF_START(GetINodeFromSnapshotSeek);
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetINodeFromSnapshotSeek, key_prefix);

  ROCKSDB_PERF_START(GetINodeFromSnapshotIter);
  std::string last_value;
  for (; iter->Valid(); iter->Next()) {
    // filter out inodes whose last_update_txid is no less than read_txid
    auto key =
        cnetpp::base::StringPiece(iter->key().data(), iter->key().size());
    if (!IsSnapshotPrefixScanKey(key, key_prefix)) {
      break;
    }
    uint64_t last_update_txid;
    DecodeSnapshotINodeKey(key, &last_update_txid, nullptr);
    if (last_update_txid >= read_txid) {
      break;
    }
    last_value = iter->value().ToString();
  }

  bool found = false;
  if (!last_value.empty()) {
    INode temp;
    if (!temp.ParseFromArray(last_value.data(), last_value.size())) {
      LOG(FATAL) << "Failed to ParseFromArray, parent_id: " << parent_id
                 << ", name: " << name;
    }
    // discard if it won't be newer than current tree
    found = temp.create_txid() > read_txid;
    if (found) {
      inode->Swap(&temp);
    }
  }

  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  ROCKSDB_PERF_END(GetINodeFromSnapshotIter, key_prefix);

  if (found) {
    return kOK;
  } else {
    return st.ok() ? kFileNotFound : kError;
  }
}

bool MetaStorage::ExistSnapshotINode(INodeID inode_id) {
  if (!FLAGS_enable_snapshot_feature) {
    return false;
  }

  std::string key_prefix = EncodeInteger(inode_id);
  auto iter = std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kSnapshotINodeIndexCFIndex]));

  ROCKSDB_PERF_START(ExistSnapshotINodeSeek)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(ExistSnapshotINodeSeek, key_prefix)
  bool found = false;
  if (iter->Valid()) {
    found = StringPiece(iter->key().data(), iter->key().size())
                .starts_with(key_prefix);
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return found;
}

bool MetaStorage::ExistSnapshotReference(const INode& inode) {
  auto ref_num = inode.snapshot_references_size();
  if (ref_num == 0 || !FLAGS_enable_snapshot_feature) {
    return false;
  }
  if (UNLIKELY(ref_num >= FLAGS_snapshot_reference_num_threshold)) {
    LOG(WARNING) << "inode " << inode.id()
                 << " has too many snapshot_references: " << ref_num;
    MFC(metrics_.get_deep_snapshot_reference_num_)->Inc();
  }

  std::vector<std::string> key_strs;
  key_strs.reserve(ref_num);
  for (const auto& ref : inode.snapshot_references()) {
    std::string key;
    EncodeSnapshotINodeIndexKey(ref.inode_id(), ref.last_update_txid(), &key);
    key_strs.emplace_back(std::move(key));
  }
  auto col_families = std::vector<rocksdb::ColumnFamilyHandle*>(
      key_strs.size(), handles_[kSnapshotINodeIndexCFIndex]);
  auto keys = std::vector<rocksdb::Slice>(key_strs.begin(), key_strs.end());

  ROCKSDB_PERF_START(ExistSnapshotReference);
  auto opt = rocksdb::ReadOptions();
  std::vector<std::string> values;
  auto statuses = rocks_db_->MultiGet(opt, col_families, keys, &values);
  ROCKSDB_PERF_END(ExistSnapshotReference, key_strs[0]);

  bool found_ref = false;
  CHECK_EQ(statuses.size(), key_strs.size());
  for (size_t i = 0; i < key_strs.size(); i++) {
    if (statuses[i].IsNotFound()) {
      continue;
    }
    CHECK(statuses[i].ok()) << "System error: " << statuses[i].ToString()
                            << ", key: " << key_strs[i];
    found_ref = true;
    break;
  }

  return found_ref;
}

uint64_t MetaStorage::GetINodeId(const uint64_t parent_id,
                                 const std::string& name,
                                 MetaStorageIterPtr iter) {
  CHECK(!name.empty());

  MFC(metrics_.get_inode_scan_num_)->Inc();
  StopWatch sw(metrics_.get_inode_scan_time_);
  sw.Start();

  std::string key_prefix;
  EncodeScanKey(parent_id, name, &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetIteratorWithPrefix(parent_id, name);
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(GetINodeId)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetINodeId, key_prefix)
  if (iter->Valid()) {
    if (IsPrefixScanKey(cnetpp::base::StringPiece(iter->key().data(),
                                                  iter->key().size()),
                        key_prefix)) {
      uint64_t id;
      DecodeStoreKey(
          cnetpp::base::StringPiece(iter->key().data(), iter->key().size()),
          nullptr,
          nullptr,
          &id);
      return id;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return 0;
}

std::string MetaStorage::GetLeaseHolder(INodeID inode_id,
                                        MetaStorageSnapPtr snapshot) {
  std::string value;
  rocksdb::ReadOptions read_options;
  read_options.snapshot = snapshot;
  rocksdb::Status s = rocks_db_->Get(
      read_options, handles_[kLeaseCFIndex], EncodeINodeID(inode_id), &value);
  CHECK(s.code() == rocksdb::Status::Code::kOk ||
        s.code() == rocksdb::Status::Code::kNotFound)
      << "code: " << s.code()          //
      << ", subcode: " << s.subcode()  //
      << ", inode_id: " << inode_id;
  if (!s.ok()) {
    return "";
  }
  FileUnderConstructionFeature uc;
  CHECK(uc.ParseFromArray(value.data(), value.size()))
      << "Fail to parse FileUnderConstructionFeature: " << inode_id;
  return uc.client_name();
}

Status MetaStorage::GetWriteBackTask(INodeID inode_id, INode* inode) {
  CHECK(FLAGS_run_ut);
  std::string value;
  rocksdb::ReadOptions read_options;
  rocksdb::Status s = rocks_db_->Get(rocksdb::ReadOptions(),
                                     handles_[kWriteBackTaskCFIndex],
                                     EncodeINodeID(inode_id),
                                     &value);
  if (s.code() == rocksdb::Status::Code::kOk) {
    inode->ParseFromArray(value.data(), value.size());
    return Status::OK();
  }
  if (s.code() == rocksdb::Status::Code::kNotFound) {
    return Status(Code::kFileNotFound);
  }
  CHECK(false) << s.ToString();
  return Status::OK();
}

void MetaStorage::GetSubINodes(uint64_t id,
                               const std::string& start_after,
                               int limit,
                               std::vector<INode>* sub_inodes,
                               bool* has_more,
                               MetaStorageIterPtr iter,
                               const SnapshotReadInfo* snapshot_read_info) {
  CHECK_NOTNULL(sub_inodes);
  CHECK_NOTNULL(has_more);

  sub_inodes->clear();
  *has_more = false;
  auto cb = [&](const INode& inode) -> bool {
    if (limit < 0 || sub_inodes->size() < limit) {
      sub_inodes->emplace_back(inode);
      return true;
    }
    *has_more = true;
    return false;
  };

  if (snapshot_read_info) {
    GetSubINodes(id, *snapshot_read_info, start_after, cb, iter);
  } else {
    GetSubINodes(id, start_after, cb, iter);
  }
}

void MetaStorage::GetSubINodesStartAt(uint64_t id,
                                      const std::string& start_at,
                                      std::function<bool(const INode&)> callback,
                                      MetaStorageIterPtr iter) {
  std::string id_prefix;
  EncodeScanKey(id, "", &id_prefix);

  std::string key_prefix;
  EncodeScanKey(id, start_at, &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetSubINodeIterator(id);
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(GetSubINodesSeek)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetSubINodesSeek, key_prefix)

  if (id == kRootINodeId && iter->Valid()) {
    // maybe root inode exists in rocksdb or not, skip root inode
    if (IsRootINodeKey(iter->key().data(), iter->key().size())) {
      iter->Next();
    }
  }

  ROCKSDB_PERF_START(GetSubINodesIter)
  INode node = INode();
  for (; iter->Valid(); iter->Next()) {
    if (id == kRootINodeId &&
        IsRootINodeKey(iter->key().data(), iter->key().size())) {
          // maybe root inode exists in rocksdb or not, skip root inode
          continue;
    }
    auto key = cnetpp::base::StringPiece(iter->key().data(),
                                         iter->key().size());
    DLOG(ERROR) << "Scanned key: " << key;
    if (!IsPrefixIdKey(key, id_prefix)) {
      break;
    }

    if (!node.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Failed to ParseFromArray, parent_id: "
                 << id << ", start_after: " << start_at;
    }

    if (!callback(node)) {
      break;
    }
  }

  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  ROCKSDB_PERF_END(GetSubINodesIter, key_prefix)
}

void MetaStorage::GetSubINodes(uint64_t id,
                               const std::string& start_after,
                               std::function<bool(const INode&)> callback,
                               MetaStorageIterPtr iter) {
  std::string id_prefix;
  EncodeScanKey(id, "", &id_prefix);

  std::string key_prefix;
  EncodeScanKey(id, start_after, &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetSubINodeIterator(id);
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(GetSubINodesSeek)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetSubINodesSeek, key_prefix)

  if (id == kRootINodeId && iter->Valid()) {
    // maybe root inode exists in rocksdb or not, skip root inode
    if (IsRootINodeKey(iter->key().data(), iter->key().size())) {
      iter->Next();
    }
  }
  ROCKSDB_PERF_START(GetSubINodesIter)
  if (!start_after.empty() && iter->Valid()
      && cnetpp::base::StringPiece(iter->key().data(),
        iter->key().size()).starts_with(key_prefix)) {
    iter->Next();  // jump out this `startAfter`
  }
  INode node = INode();
  for (; iter->Valid(); iter->Next()) {
    if (id == kRootINodeId &&
        IsRootINodeKey(iter->key().data(), iter->key().size())) {
      // maybe root inode exists in rocksdb or not, skip root inode
      continue;
    }
    auto key = cnetpp::base::StringPiece(iter->key().data(),
                                         iter->key().size());
    if (!IsPrefixIdKey(key, id_prefix)) {
      break;
    }
    if (!node.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Failed to ParseFromArray, parent_id: "
                 << id << ", start_after: " << start_after;
    }
    if (!callback(node)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  ROCKSDB_PERF_END(GetSubINodesIter, key_prefix)
}

void MetaStorage::GetSubINodes(uint64_t id,
                               const SnapshotReadInfo& snapshot_read_info,
                               const std::string& start_after,
                               std::function<bool(const INode&)> callback,
                               MetaStorageIterPtr iter) {
  if (!snapshot_read_info.is_under_snapshot_ &&
      !snapshot_read_info.is_snapshot_directory_) {
    return GetSubINodes(id, start_after, callback, iter);
  }

  if (snapshot_read_info.is_snapshot_directory_) {
    return;
  }

  // GetSubINodes from normal and snapshot INode table, we will merge them later
  std::vector<INode> inodes;
  std::vector<INode> snapshot_inodes;
  uint64_t count = 0;
  GetSubINodes(
      id,
      start_after,
      [read_txid = snapshot_read_info.read_txid_, &count, &inodes](
          const INode& inode) {
        if (!inode.has_last_update_txid() ||
            inode.last_update_txid() < read_txid) {
          inodes.emplace_back(inode);
          return count++ <= FLAGS_dfs_ls_limit;
        } else {
          return true;
        }
      },
      iter);
  count = 0;
  GetSubINodesFromSnapshotTable(id,
                                snapshot_read_info,
                                start_after,
                                [&count, &snapshot_inodes](const INode& inode) {
                                  snapshot_inodes.emplace_back(inode);
                                  return count++ <= FLAGS_dfs_ls_limit;
                                });

  // process inodes in lexicographical order of inode name, if there exist same
  // name inodes, select the one with bigger last_update_txid
  INode* last_selected;
  size_t idx1 = 0;
  size_t idx2 = 0;
  bool cont = true;
  while (idx1 < inodes.size() && idx2 < snapshot_inodes.size() && cont) {
    const auto& inode = inodes[idx1];
    const auto& snapshot_inode = snapshot_inodes[idx2];
    int result = inode.name().compare(snapshot_inode.name());
    if (result == 0) {
      if (inode.last_update_txid() < snapshot_inode.last_update_txid()) {
        cont = callback(snapshot_inode);
      } else {
        cont = callback(inode);
      }
      idx1++;
      idx2++;
    } else if (result < 0) {
      cont = callback(inode);
      idx1++;
    } else {
      cont = callback(snapshot_inode);
      idx2++;
    }
  }
  while (idx1 < inodes.size() && cont) {
    cont = callback(inodes[idx1++]);
  }
  while (idx2 < snapshot_inodes.size() && cont) {
    cont = callback(snapshot_inodes[idx2++]);
  }
}

void MetaStorage::GetSubINodesFromSnapshotTable(
    uint64_t id,
    const SnapshotReadInfo& snapshot_read_info,
    const std::string& start_after,
    std::function<bool(const INode&)> callback) {
  CHECK(snapshot_read_info.is_under_snapshot_ &&
        !snapshot_read_info.is_snapshot_directory_);

  auto snapshot_iter_holder = std::make_unique<MetaStorageIterHolder>();
  snapshot_iter_holder->iter_ =
      std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
          rocksdb::ReadOptions(), handles_[kSnapshotInodeCFIndex]));
  auto iter = CHECK_NOTNULL(snapshot_iter_holder->iter());

  auto p_id = id;
  std::string id_prefix;
  std::string key_prefix;
  EncodeScanKey(p_id, "", &id_prefix);
  EncodeScanKey(p_id, start_after, &key_prefix);

  if (p_id == kRootINodeId && iter->Valid()) {
    // maybe root inode exists in rocksdb or not, skip root inode
    if (IsRootINodeKey(iter->key().data(), iter->key().size())) {
      iter->Next();
    }
  }

  ROCKSDB_PERF_START(GetSnapshotSubINodesSeek)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(GetSnapshotSubINodesSeek, key_prefix)

  ROCKSDB_PERF_START(GetSnapshotSubINodesIter)
  if (!start_after.empty() && iter->Valid() &&
      cnetpp::base::StringPiece(iter->key().data(), iter->key().size())
          .starts_with(key_prefix)) {
    iter->Next();  // jump out this `startAfter`
  }

  std::string last_name;
  std::string last_value;
  INode last_inode;

  // return: found valid snapshot inode for last_value
  auto parse_and_filter_last = [&]() -> bool {
    if (last_value.empty()) {
      return false;
    }

    if (!last_inode.ParseFromArray(last_value.data(), last_value.size())) {
      LOG(FATAL) << "Failed to ParseFromArray, parent_id: " << p_id
                 << ", start_after: " << start_after << ", name: " << last_name;
    }
    last_value.clear();
    // discard if it won't be newer than current tree
    return last_inode.create_txid() > snapshot_read_info.read_txid_;
  };

  for (; iter->Valid(); iter->Next()) {
    auto key =
        cnetpp::base::StringPiece(iter->key().data(), iter->key().size());
    if (!IsPrefixIdKey(key, id_prefix)) {
      break;
    }

    // filter out inode which cannot be seen in snapshot
    uint64_t last_update_txid;
    std::string current_inode_name;
    DecodeSnapshotINodeKey(
        key, nullptr, &current_inode_name, nullptr, &last_update_txid, nullptr);
    if (last_update_txid >= snapshot_read_info.read_txid_) {
      // continue here so that we may scan inodes with different names
      continue;
    }

    // handle inode as soon as we finish selecting it
    // here we rely on the existing inode's name is not empty
    bool select_done = !last_name.empty() && last_name != current_inode_name;
    last_name = current_inode_name;
    if (select_done && parse_and_filter_last()) {
      if (!callback(last_inode)) {
        break;
      }
    }
    last_value = iter->value().ToString();
  }

  if (parse_and_filter_last()) {
    callback(last_inode);
  }

  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  ROCKSDB_PERF_END(GetSnapshotSubINodesIter, key_prefix)
}

bool MetaStorage::HasSubINodes(uint64_t id, MetaStorageIterPtr iter) {
  std::string key_prefix;
  EncodeScanKey(id, "", &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetIterator();
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(HasSubINodes)
  iter->Seek(key_prefix);
  ROCKSDB_PERF_END(HasSubINodes, key_prefix)
  if (id == kRootINodeId && iter->Valid()) {
    // maybe root inode exists in rocksdb or not, skip root inode
    if (IsRootINodeKey(iter->key().data(), iter->key().size())) {
      iter->Next();
    }
  }
  if (iter->Valid()) {
    return IsPrefixIdKey(cnetpp::base::StringPiece(iter->key().data(),
                                                   iter->key().size()),
                         key_prefix);
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return false;
}

void MetaStorage::SaveNumINodes(int64_t num_inodes) {
  CHECK_GE(num_inodes, 0);
  auto num = platform::HostToBigEndian(static_cast<uint64_t>(num_inodes));
  auto v = cnetpp::base::StringPiece(reinterpret_cast<const uint8_t*>(&num),
                                     sizeof(uint64_t));
  PutNameSystemInfo(kNumINodesKey, v);
  num_inodes_ = num_inodes;
}

int64_t MetaStorage::LoadNumINodes() {
  std::string num_inodes;
  if (GetNameSystemInfo(kNumINodesKey, &num_inodes)) {
    CHECK_EQ(num_inodes.length(), sizeof(uint64_t));
    num_inodes_ =
        static_cast<int64_t>(platform::ReadBigEndian<uint64_t>(
            &(num_inodes[0]), 0));
    LOG(INFO) << kNumINodesKey << " is: " << num_inodes_.load();
    return num_inodes_;
  }
  num_inodes_ = -1;
  LOG(WARNING) << "First time launching, num_inodes is uninitialized";
  return num_inodes_;
}

int64_t MetaStorage::GetNumINodes() {
  return num_inodes_;
}

uint32_t MetaStorage::NumSubINodes(uint64_t inode_id, MetaStorageIterPtr iter) {
  uint32_t count = 0;
  std::string key_prefix;
  EncodeScanKey(inode_id, "", &key_prefix);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = GetIterator();
    iter = iter_holder->iter();
  }
  CHECK_NOTNULL(iter);

  ROCKSDB_PERF_START(NumSubINodes)
  iter->Seek(rocksdb::Slice(key_prefix));
  if (inode_id == kRootINodeId && iter->Valid()) {
    // maybe root inode exists in rocksdb or not, skip root inode
    if (IsRootINodeKey(iter->key().data(), iter->key().size())) {
      iter->Next();
    }
  }
  for (; iter->Valid(); iter->Next()) {
    if (IsPrefixIdKey(cnetpp::base::StringPiece(iter->key().data(),
                                                iter->key().size()),
                      key_prefix)) {
      count++;
    } else {
      break;
    }
  }
  ROCKSDB_PERF_END(NumSubINodes, key_prefix)
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return count;
}

void MetaStorage::ScanPendingDeleteCF(
    const std::function<bool(const INode&)>& callback) {
  auto iter = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(rocksdb::ReadOptions(),
                             handles_[kINodePendingDeleteCFIndex]));

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    INode inode;
    if (!inode.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Corrupted data in pending delete column family.";
    }
    if (!callback(inode)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
}

void MetaStorage::CleanupOrphanedTtlATimes() {
  LOG(INFO) << "Starting TtlATime cleanup scan";

  auto wb = CreateWriteBatch();
  int cleaned_count = 0;

  ScanColumnFamily(
    kTtlATimeCFIndex,
    [&](const rocksdb::Slice& key, const rocksdb::Slice& value) -> bool {
      ATimeToBeUpdate atime;
      if (!atime.ParseFromString(value.ToString())) {
        LOG(ERROR) << "Failed to parse ATimeToBeUpdate";
        return true;
      }

      INode inode;
      auto code = GetINode(atime.inode_id(), &inode);
      if (code != StatusCode::kOK) {
        // INode not exists, delete the ttlAtime entry
        auto key = EncodeInteger(atime.inode_id());
        wb->Delete(handles_[kTtlATimeCFIndex], key);
        cleaned_count++;
        LOG(INFO) << "Delete orphaned ttlAtime entry: " << atime.inode_id()
            << " cleaned_index: " << cleaned_count;
      }

      // Batch writes to avoid too many small writes
      if (cleaned_count % 1000 == 0) {
        auto status = rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
        CHECK(status.ok()) << "Failed to delete orphaned ttlAtime entries: "
            << status.ToString();
        wb = CreateWriteBatch();
      }
      return true;
    });

  // Write remaining deletions
  if (cleaned_count % 1000 != 0) {
    auto status = rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
    CHECK(status.ok()) << "Failed to delete orphaned ttlAtime entries: "
        << status.ToString();
  }

  LOG(INFO) << "TtlATime cleanup completed, removed " << cleaned_count
      << " orphaned entries";
}

void MetaStorage::ScanSnapshotINodes(
    const std::function<bool(SnapshotINodeKey*,
                             const rocksdb::Slice&,
                             const rocksdb::Slice&)>& callback) {
  auto wrap_cb = [this, &callback](const rocksdb::Slice& key,
                                   const rocksdb::Slice& value) -> bool {
    SnapshotINodeKey temp_inode_key;
    DecodeSnapshotINodeKey(StringPiece(key.data(), key.size()),
                           &temp_inode_key.parent_id,
                           &temp_inode_key.name,
                           &temp_inode_key.inode_id,
                           &temp_inode_key.last_update_txid,
                           &temp_inode_key.snapshot_root_id);
    return callback(&temp_inode_key, key, value);
  };
  ScanColumnFamily(kSnapshotInodeCFIndex, wrap_cb);
}

bool MetaStorage::ScanColumnFamily(
    uint32_t cf_idx,
    const std::function<bool(const rocksdb::Slice&, const rocksdb::Slice&)>&
        callback) {
  auto iter = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(rocksdb::ReadOptions(), handles_[cf_idx]));
  bool scan_completed = true;
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    if (!callback(iter->key(), iter->value())) {
      scan_completed = false;
      break;
    }
  }
  auto st = iter->status();
      LOG_IF(ERROR, !st.ok()) << "Scan " << cf_idx
                              << " System error: " << st.ToString();
  return scan_completed;
}

void MetaStorage::ScanDatanodeInfoCf(
    const std::function<bool(const DatanodeID&, const DatanodeInfoEntryPB&)>&
        callback) {
  auto iter = std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kDatanodeInfoCFIndex]));

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    DatanodeID dn_id = 0;
    DatanodeInfoEntryPB pb;
    if (!pb.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Corrupted data in dir policy column family.";
    }

    DecodeDatanodeInfoKey(
        cnetpp::base::StringPiece(iter->key().data(), iter->key().size()),
        &dn_id);

    CHECK(dn_id == pb.internal_id())
        << " dn_id=" << dn_id << " pb=" << pb.ShortDebugString();

    if (!callback(dn_id, pb)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
}

bool MetaStorage::ForEachINode(
    uint64_t dir_id,
    const std::string& dir_name,
    const std::function<bool(const std::string&, const INode&)>& cb,
    std::set<INodeID>* all_inodes) {
  if (dir_id == kRootINodeId && dir_name == "//") {
    // XXX(xuex) termination of recursion on root, but maybe deprecated now
    return true;
  }

  std::string prefix;
  EncodeScanKey(dir_id, "", &prefix);
  auto iter = std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kINodeDefaultCFIndex]));
  for (iter->Seek(prefix); iter->Valid(); iter->Next()) {
    uint64_t pid;
    uint64_t id;
    std::string name;
    DecodeStoreKey(
        cnetpp::base::StringPiece(iter->key().data(), iter->key().size()),
        &pid,
        &name,
        &id);
    if (pid != dir_id) {
      break;
    }

    auto inode = std::make_unique<INode>();
    if (!inode->ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Failed to parse inode from meta storage key string: "
                 << iter->key().data();
    }

    if (inode->type() != INode_Type_kFile &&
        inode->type() != INode_Type_kDirectory &&
        inode->type() != INode_Type_kSymLink) {
      LOG(FATAL) << "Unknown inode type: " << inode->type();
    }

    // root shall not be traversed as child of itself.
    if (inode->id() == kRootINodeId) {
      CHECK_EQ(dir_id, kRootINodeId);
      continue;
    }

    std::string fullpath = dir_name + inode->name();
    if (dir_id == kRootINodeId) {
      fullpath = '/' + inode->name();
    } else {
      fullpath = dir_name + '/' + inode->name();
    }
    if (!cb(fullpath, *inode)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << iter->status().ToString();
  return st.ok();
}

bool MetaStorage::ForEachDir(
    uint64_t dir_id,
    const std::string& dir_name,
    const std::function<bool(const std::string&, const INode&)>& cb) {
  uint64_t cache_inodes = 0;
  return ForEachDir(dir_id, dir_name, &cache_inodes, cb);
}

bool MetaStorage::ForEachDir(
    uint64_t dir_id,
    const std::string& dir_name,
    uint64_t* cache_inodes,
    const std::function<bool(const std::string&, const INode&)>& cb) {
  if (dir_id == kRootINodeId && dir_name == "//") {
    // XXX(xuex) termination of recursion on root, but maybe deprecated now
    return true;
  }

  INodeQ inodes;
  if (!ForEachCurrentDir(dir_id, dir_name, &inodes)) {
    return false;
  }
  *cache_inodes = *cache_inodes + inodes.size();

  while (!inodes.empty()) {
    auto& p = inodes.front();
    if (!cb(p.first, *p.second.get())) {
      return false;
    }
    inodes.pop();
    *cache_inodes = *cache_inodes - 1;
  }
  return true;
}

bool MetaStorage::ForEachDirComputeQuota(uint64_t dir_id,
                                         const std::string& dir_name,
                                         const std::string& quota_path,
                                         const std::string& quota_team,
                                         bool need_judge_trash,
                                         bool need_judge_mapping,
                                         const std::function<bool(const std::string&,
                                                                  const INode&,
                                                                  const std::string&,
                                                                  const std::string&,
                                                                  bool,
                                                                  bool)>& cb) {
  if (dir_id == kRootINodeId && dir_name == "//") {
    // XXX(xuex) termination of recursion on root, but maybe deprecated now
    return true;
  }

  INodeQ inodes;
  if (!ForEachCurrentDir(dir_id, dir_name, &inodes)) {
    return false;
  }

  while (!inodes.empty()) {
    auto& p = inodes.front();
    if (!cb(p.first, *p.second.get(), quota_path, quota_team,
            need_judge_trash, need_judge_mapping)) {
      return false;
    }
    inodes.pop();
  }
  return true;
}

bool MetaStorage::ForEachCurrentDir(uint64_t dir_id,
                                    const std::string& dir_name,
                                    INodeQ* inodes) {
  std::string prefix;
  EncodeScanKey(dir_id, "", &prefix);
  auto iter = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(rocksdb::ReadOptions(),
                             handles_[kINodeDefaultCFIndex]));
  for (iter->Seek(prefix); iter->Valid(); iter->Next()) {
    uint64_t pid;
    uint64_t id;
    std::string name;
    DecodeStoreKey(cnetpp::base::StringPiece(iter->key().data(),
                                             iter->key().size()),
                   &pid,
                   &name,
                   &id);
    if (pid != dir_id) {
      break;
    }

    auto inode = std::make_unique<INode>();
    if (!inode->ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Failed to parse inode from meta storage, key string: "
                 << iter->key().data();
    }
    if (inode->type() != INode_Type_kFile &&
        inode->type() != INode_Type_kDirectory &&
        inode->type() != INode_Type_kSymLink) {
      LOG(FATAL) << "Unknown inode type " << inode->type();
    }

    // root shall not be traversed as child of itself.
    if (inode->id() == kRootINodeId) {
      CHECK_EQ(dir_id, kRootINodeId);
      continue;
    }

    std::string fullpath;
    if (dir_id == kRootINodeId) {
      fullpath = '/' + inode->name();
    } else {
      fullpath = dir_name + '/' + inode->name();
    }
    inodes->emplace(std::make_pair(fullpath, std::move(inode)));
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
  return st.ok();
}

std::string MetaStorage::EncodeINodeID(INodeID inode_id) {
  std::string str;
  str.resize(sizeof(INodeID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(str.c_str()), 0, inode_id);
  return str;
}

INodeID MetaStorage::DecodeINodeID(rocksdb::Slice slice) {
  if (slice.size() != sizeof(INodeID) / sizeof(uint8_t)) {
    LOG(ERROR) << "INode size is " << slice.size();
    return kInvalidINodeId;
  }
  return platform::ReadBigEndian<BlockID>(slice.data(), 0);
}

std::string MetaStorage::EncodeBlockID(BlockID blk_id) {
  std::string blk_id_str;
  blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  return blk_id_str;
}

BlockID MetaStorage::DecodeBlockID(rocksdb::Slice slice) {
  if (slice.size() != sizeof(BlockID) / sizeof(uint8_t)) {
    LOG(ERROR) << "BlockID size is " << slice.size();
    return kInvalidBlockID;
  }
  return platform::ReadBigEndian<BlockID>(slice.data(), 0);
}

bool MetaStorage::EncodeStoreKey(uint64_t pid,
                                 const std::string& name,
                                 uint64_t id,
                                 std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->reserve(19 + name.size());
  key_str->resize(8);
  platform::WriteBigEndian(&(*key_str)[0], 0, pid);
  key_str->append("/");
  key_str->append(name);
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    key_str->insert(key_str->end(), '\0');
    key_str->insert(key_str->end(), '/');
    key_str->resize(19 + name.size());
    platform::WriteBigEndian(&(*key_str)[0], 11 + name.size(), id);
  } else {
    key_str->append("/", 1);
    key_str->resize(18 + name.size());
    platform::WriteBigEndian(&(*key_str)[0], 10 + name.size(), id);
  }
  return true;
}

void MetaStorage::EncodeSnapshotINodeKey(INodeID p_id,
                                         const std::string& name,
                                         INodeID id,
                                         uint64_t last_update_txid,
                                         INodeID snapshot_root_id,
                                         std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->reserve(37 + name.size());  // store key 19 + suffix 9*2
  CHECK(EncodeStoreKey(p_id, name, id, key_str));
  auto store_key_part_len = key_str->size();

  key_str->append("/", 1);
  key_str->resize(key_str->size() + sizeof last_update_txid);
  platform::WriteBigEndian(
      &(*key_str)[0], store_key_part_len + 1, last_update_txid);
  key_str->append("/", 1);
  key_str->resize(key_str->size() + sizeof snapshot_root_id);
  platform::WriteBigEndian(
      &(*key_str)[0], store_key_part_len + 10, snapshot_root_id);
}

void MetaStorage::EncodeSnapshotINodeIndexKey(INodeID id,
                                              uint64_t last_update_txid,
                                              std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->resize(17);
  platform::WriteBigEndian(&(*key_str)[0], 0, id);
  (*key_str)[8] = '/';
  platform::WriteBigEndian(&(*key_str)[0], 9, last_update_txid);
}

bool MetaStorage::EncodeParentIndexKey(const INodeID inode,
                                       std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->resize(10);
  (*key_str)[0] = static_cast<char>(INodeIndexType::kParentIndex);
  (*key_str)[1] = '/';
  platform::WriteBigEndian(&(*key_str)[0], 2, inode);
  return true;
}

void MetaStorage::EncodeSCRKey(BlockID blk_id,
                               const std::string& dnuuid,
                               std::string* key) {
  CHECK_NOTNULL(key);
  size_t blkid_nbyte = sizeof(BlockID) / sizeof(uint8_t);
  key->reserve(blkid_nbyte + 1 + dnuuid.size());
  key->resize(blkid_nbyte);
  platform::WriteBigEndian(const_cast<char*>(key->c_str()), 0, blk_id);
  if (dnuuid.empty()) {
    // XXX deprecated key format has only blockid
    return;
  }
  key->append("/");
  key->append(dnuuid);
}

void MetaStorage::DecodeSCRKey(rocksdb::Slice slice,
                               BlockID* blk_id,
                               std::string* dn_uuid) {
  CHECK_NOTNULL(blk_id);
  CHECK_NOTNULL(dn_uuid);
  size_t blkid_nbyte = sizeof(BlockID) / sizeof(uint8_t);
  rocksdb::Slice blk_id_slice(slice.data(), blkid_nbyte);
  *blk_id = DecodeBlockID(blk_id_slice);
  if (slice.size() == blkid_nbyte) {
    // XXX deprecated key format has only blockid
    *dn_uuid = "";
    return;
  }
  dn_uuid->assign(slice.data() + blkid_nbyte + 1,
                  slice.size() - blkid_nbyte - 1);
}

int64_t MetaStorage::UpdateParentIndexWriteBatch(const INode& inode,
                                                 bool is_del,
                                                 rocksdb::WriteBatch* wb) {
  std::string key;
  EncodeParentIndexKey(inode.id(), &key);
  if (is_del) {
    wb->Delete(handles_[kINodeIndexCFIndex], key);
    return -1;
  }
  INodeParentInfoPB info;
  info.set_name(inode.name());
  info.set_parent_id(inode.parent_id());
  std::string value;
  info.SerializeToString(&value);
  wb->Put(handles_[kINodeIndexCFIndex], key, value);
  return 1;
}

void MetaStorage::UpdateLeaseWriteBatch(const INode& inode,
                                        bool is_del,
                                        rocksdb::WriteBatch* wb) {
  CHECK(wb);
  if (is_del || !inode.has_uc()) {
    wb->Delete(handles_[kLeaseCFIndex], EncodeINodeID(inode.id()));
  } else {
    wb->Put(handles_[kLeaseCFIndex],
            EncodeINodeID(inode.id()),
            inode.uc().SerializeAsString());
  }
}

void MetaStorage::UpdateDirPolicyWriteBatch(const INode& inode,
                                            bool is_del,
                                            rocksdb::WriteBatch* wb) {
  CHECK(wb);
  if (inode.type() != INode_Type_kDirectory) {
    return;
  }

  if (is_del || !PolicyManager::HasPolicy(inode)) {
    wb->Delete(handles_[kDirPolicyCFIndex], EncodeINodeID(inode.id()));
  } else {
    wb->Put(handles_[kDirPolicyCFIndex],
            EncodeINodeID(inode.id()),
            inode.SerializeAsString());
  }
}

void MetaStorage::UpdateWriteBackTaskWriteBatch(const INode& inode,
                                                bool is_del,
                                                rocksdb::WriteBatch* wb) {
  CHECK(wb);
  if (is_del ||
      (inode.has_ufs_file_info() &&
       inode.ufs_file_info().file_state() != kUfsFileStateToBePersisted)) {
    wb->Delete(handles_[kWriteBackTaskCFIndex], EncodeINodeID(inode.id()));
  } else if (inode.has_ufs_file_info() &&
             inode.ufs_file_info().file_state() == kUfsFileStateToBePersisted) {
    wb->Put(handles_[kWriteBackTaskCFIndex],
            EncodeINodeID(inode.id()),
            inode.SerializeAsString());
  }
}

bool MetaStorage::EncodeDatanodeInfoKey(const DatanodeID dn_id,
                                        std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->resize(sizeof(dn_id));
  platform::WriteBigEndian(&(*key_str)[0], 0, dn_id);
  return true;
}

bool MetaStorage::DecodeDatanodeInfoKey(
    const cnetpp::base::StringPiece& key_str,
    DatanodeID* dn_id) {
  CHECK_NOTNULL(dn_id);
  if (key_str.length() != sizeof(DatanodeID)) {
    return false;
  }
  *dn_id = platform::ReadBigEndian<DatanodeID>(key_str.data(), 0);
  return true;
}

bool MetaStorage::EncodeScanKey(uint64_t pid,
                                const std::string& name,
                                std::string* key_str) {
  CHECK_NOTNULL(key_str);
  key_str->reserve(11 + name.size());
  key_str->resize(8);
  platform::WriteBigEndian(&(*key_str)[0], 0, pid);
  key_str->append("/");
  if (!name.empty()) {
    key_str->append(name);
    if (FLAGS_dfs_meta_storage_inode_key_v2) {
      key_str->insert(key_str->end(), '\0');
      key_str->insert(key_str->end(), '/');
    } else {
      key_str->append("/", 1);
    }
  }
  return true;
}

bool MetaStorage::IsRootINodeKey(const char* key, int len) {
  if (len != GetRootINodeKeyLen()) return false;
  return memcmp(GetRootINodeKey(), key, GetRootINodeKeyLen()) == 0;
}

const char* MetaStorage::GetRootINodeKey() {
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    return kRootINodeKeyV2;
  } else {
    return kRootINodeKey;
  }
}

int MetaStorage::GetRootINodeKeyLen() {
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    return kRootINodeKeyLenV2;
  } else {
    return kRootINodeKeyLen;
  }
}

INode MetaStorage::GetRootINode() {
  std::string key(GetRootINodeKey(), GetRootINodeKeyLen());
  std::string value;
  auto exist = this->Get(key, &value);
  if (VLOG_IS_ON(15)) {
    std::ostringstream oss;
    oss << "MetaStorage::GetRootINode";
    oss << " exist=" << (exist ? "true" : "false");

    oss << " key_len=" << key.size();
    oss << " key=" << StringUtils::ToHexString(key);
    oss << " value_len=" << value.size();
    oss << " value=" << StringUtils::ToHexString(value);
    VLOG(15) << oss.str();
  }
  if (exist) {
    INode root_inode_cache;
    if (!root_inode_cache.ParseFromString(value)) {
      LOG(FATAL) << "LoadRootINode(). Failed to ParseFromArray.";
    }
    return root_inode_cache;
  } else {
    return MetaStorage::CreateRoot();
  }
}

bool MetaStorage::IsPrefixScanKey(const cnetpp::base::StringPiece& key,
                                  const cnetpp::base::StringPiece& prefix) {
  if (key.empty() || prefix.empty()) {
    return false;
  }

  return key.starts_with(prefix) && (prefix.size() + 8 == key.size());
}

bool MetaStorage::IsSnapshotPrefixScanKey(
    const cnetpp::base::StringPiece& key,
    const cnetpp::base::StringPiece& prefix) {
  if (key.empty() || prefix.empty()) {
    return false;
  }

  return key.starts_with(prefix) && (prefix.size() + 26 == key.size());
}

bool MetaStorage::IsPrefixIdKey(const cnetpp::base::StringPiece& key,
                                const cnetpp::base::StringPiece& prefix) {
  if (key.empty() || prefix.empty()) {
    return false;
  }

  return key.starts_with(prefix) && prefix.size() + 9 < key.size();
}

void MetaStorage::DecodeStoreKey(const cnetpp::base::StringPiece& key_str,
                                 uint64_t* p_id,
                                 std::string* name,
                                 uint64_t* id) {
  CHECK(DecodeStoreKeyInternal(key_str, p_id, name, id).IsOK());
}

Status MetaStorage::DecodeStoreKeyInternal(
    const cnetpp::base::StringPiece& key_str,
    uint64_t* pid,
    std::string* name,
    uint64_t* id) {
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    // |--------|-|-...-|-|-|--------|
    // |pid     |/|name |0|/|id      |
    if (key_str.size() < 19UL) {
      return Status(JavaExceptions::kIOException,
                    "The size of key is less than 19");
    }
    if (pid) {
      *pid = platform::ReadBigEndian<uint64_t>(key_str.data(), 0);
    }
    if (key_str[8] != '/') {
      return Status(JavaExceptions::kIOException,
                    "The ninth character of the key is not '/'");
    }
    if (name) {
      name->assign(key_str.data() + 9, key_str.size() - 19);
    }
    if (key_str[key_str.size() - 8 - 1 - 1] != '\0') {
      return Status(JavaExceptions::kIOException,
                    "The tenth character from the end is not \0");
    }
    if (key_str[key_str.size() - 8 - 1] != '/') {
      return Status(JavaExceptions::kIOException,
                    "The ninth character from the end is not /");
    }
    if (id) {
      *id =
          platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 8);
    }
    return Status();
  } else {
    if (key_str.size() < 18) {
      return Status(JavaExceptions::kIOException,
                    "The size of key is less than 18");
    }
    if (pid) {
      *pid = platform::ReadBigEndian<uint64_t>(key_str.data(), 0);
    }
    std::string name_holder;
    if (name == nullptr) {
      name = &name_holder;
    }
    if (name) {
      name->assign(key_str.data() + 9, key_str.size() - 18);
    }
    // We all agree valid path component cannot endswith
    // '\0', whether in HDFS mode or ACC mode. So we can
    // use this feature to validate inode key format.
    if (name->size() > 0 && name->back() == '\0') {
      return Status(JavaExceptions::kIOException, "The last character is \0");
    }
    if (id) {
      *id =
          platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 8);
    }
    return Status();
  }
}

void MetaStorage::DecodeStoreKey(const cnetpp::base::StringPiece& key_str,
                                 uint64_t* pid,
                                 uint64_t* id) {
  DecodeStoreKey(key_str, pid, nullptr, id);
}

void MetaStorage::DecodeSnapshotINodeKey(
    const cnetpp::base::StringPiece& key_str,
    INodeID* p_id,
    std::string* name,
    INodeID* id,
    uint64_t* last_update_txid,
    INodeID* snapshot_root_id) {
  CHECK_GT(key_str.size(), 18);
  DecodeStoreKey(key_str.substr(0, key_str.size() - 18), p_id, name, id);
  DecodeSnapshotINodeKey(key_str, last_update_txid, snapshot_root_id);
}

void MetaStorage::DecodeSnapshotINodeIndexKey(
    const cnetpp::base::StringPiece& key_str,
    INodeID* id,
    uint64_t* last_update_txid) {
  if (id) {
    *id = platform::ReadBigEndian<uint64_t>(key_str.data(), 0);
  }
  if (last_update_txid) {
    *last_update_txid = platform::ReadBigEndian<uint64_t>(key_str.data(), 9);
  }
}

void MetaStorage::DecodeSnapshotINodeKey(
    const cnetpp::base::StringPiece& key_str,
    uint64_t* last_update_txid,
    INodeID* snapshot_root_id) {
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    CHECK_GE(key_str.size(), 37UL) << "The size of Key is less than 37!";
  } else {
    CHECK_GE(key_str.size(), 36UL) << "The size of Key is less than 36!";
  }
  if (last_update_txid) {
    *last_update_txid =
        platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 17);
  }
  if (snapshot_root_id) {
    *snapshot_root_id =
        platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 8);
  }
}

void MetaStorage::StartSlows() {
  slows_ = std::make_unique<Slows>(
        FLAGS_dfs_meta_storage_slows,
        FLAGS_dfs_meta_storage_slows_mailbox
      );
  slows_->Start();
}

void MetaStorage::StopSlows() {
  if (slows_.get()) {
    slows_->Stop();
  }
}

void MetaStorage::StartWriter() {
  CHECK(!writer_thread_.get());
  writer_.reset(new meta_storage::Writer(this));
  auto task = std::static_pointer_cast<cnetpp::concurrency::Task>(writer_);
  writer_thread_.reset(new cnetpp::concurrency::Thread(task, "RDBWriter"));
  writer_thread_->Start();
}

void MetaStorage::StopWriter() {
  if (writer_thread_.get()) {
    writer_thread_->Stop();
    writer_thread_.reset();
  }
}

void MetaStorage::StartBGCompactAllWorker() {
  CHECK(!bg_compact_all_worker_.get());
  bg_compact_all_task_.reset(new BGCompactAllTask(this));
  auto task = std::static_pointer_cast<cnetpp::concurrency::Task>(
      bg_compact_all_task_);
  bg_compact_all_worker_.reset(
      new cnetpp::concurrency::Thread(task, "BGCompactAll"));
  bg_compact_all_worker_->Start();
}

void MetaStorage::StopBGCompactAllWorker() {
  if (bg_compact_all_worker_.get()) {
    bg_compact_all_worker_->Stop();
    bg_compact_all_worker_.reset();
  }
}

bool MetaStorage::ReachCompactAllThreshold() {
  int64_t x = num_deleted_for_compact_all_.load();
  return x < 0 || (x >= FLAGS_bg_auto_compact_all_deletion_threshold && x >= 10000);
}

bool MetaStorage::MaybeForceCompactDeletion() {
  if (bg_compact_in_progress_) return false;
  force_compact_deletion_.store(true);
  bg_compact_all_task_->cond_var_.notify_one();
  return true;
}

bool MetaStorage::MaybeForceCompactAll(int64_t num_deleted, bool force) {
  if (force) {
    num_deleted_for_compact_all_.store(INT64_MAX);
  } else if (FLAGS_bg_auto_compact_all_enable) {
    num_deleted_for_compact_all_.fetch_add(num_deleted);
  }

  if (bg_compact_in_progress_) return false;

  if (ReachCompactAllThreshold()) {
    // before wait, call notify_one never happen and even if
    // in this situation is not serious, it will notify later
    bg_compact_all_task_->cond_var_.notify_one();
    return true;
  }

  // for beautiful graph in grafana, we not use INT64_MAX
  auto x = num_deleted_for_compact_all_.load();
  if (x >= FLAGS_bg_auto_compact_all_deletion_threshold) {
    x = FLAGS_bg_auto_compact_all_deletion_threshold;
  }
  MFC(metrics_.num_will_retrieve_deletion_)->Set(x);

  return false;
}

void MetaStorage::ForceCompactDeletion() {
  rocksdb::CompactRangeOptions options;
  options.bottommost_level_compaction = rocksdb::BottommostLevelCompaction::kForce;

  auto handle = handles_[kINodePendingDeleteCFIndex];
  auto s = rocks_db_->CompactRange(options, handle, nullptr, nullptr);
  if (!s.ok()) {
    LOG(WARNING) << "CompactRange " << handle->GetName() << " fail: " << s.ToString();
  }
}

void MetaStorage::ForceCompactStorageClassReport() {
  rocksdb::CompactRangeOptions options;
  options.bottommost_level_compaction = rocksdb::BottommostLevelCompaction::kForce;

  auto handle = handles_[kStorageClassReportCFIndex];
  auto s = rocks_db_->CompactRange(options, handle, nullptr, nullptr);
  if (!s.ok()) {
    LOG(WARNING) << "CompactRange " << handle->GetName() << " fail: " << s.ToString();
  }
}

void MetaStorage::ForceCompactAll() {
  rocksdb::CompactRangeOptions options;
  if (bottom_compact_) {
    options.bottommost_level_compaction = rocksdb::BottommostLevelCompaction::kForce;
  } else {
    options.bottommost_level_compaction = rocksdb::BottommostLevelCompaction::kSkip;
  }

  for (auto handle : handles_) {
    if (handle == nullptr) {
      // Skip legacy column families about block.
      continue;
    }
    auto s = rocks_db_->CompactRange(options, handle, nullptr, nullptr);
    if (!s.ok()) {
      LOG(WARNING) << "CompactRange " << handle->GetName() << " fail: " << s.ToString();
    }
  }
}

void MetaStorage::SetCompactAllStyle(bool bottom_compact) {
  bottom_compact_ = bottom_compact;
}

bool MetaStorage::IsCompactAllInProgress() const {
  return bg_compact_in_progress_;
}

bool MetaStorage::BGCompactAllTask::operator()(void* arg) {
  LOG(INFO) << "Background compact all worker starts...";
  // Give the unit test a chance to stop the bg compact all worker before it works.
  std::this_thread::sleep_for(std::chrono::milliseconds(50));

  std::unique_lock<std::mutex> lock(mutex_);
  while (!IsStopped()) {
    auto interval = FLAGS_bg_auto_compact_interval_in_min;
    if (interval <= 0) {
      interval = 60;
    }

    cond_var_.wait_for(lock, std::chrono::minutes(interval), [&]() {
      return IsStopped() || storage_->force_compact_deletion_ ||
        storage_->ReachCompactAllThreshold();
    });

    if (IsStopped()) break;

    // force_compact_deletion && reach_compact_all -> compact all
    // !force_compact_deletion && reach_compact_all -> compact all
    // force_compact_deletion && !reach_compact_all -> compact deletion
    // !force_compact_deletion && !reach_compact_all -> compact none
    // compact_all_enable && !compact_deletion_enable -> compact all
    // compact_all_enable && compact_deletion_enable -> compact all
    // !compact_all_enable && compact_deletion_enable -> compact deletion
    // !compact_all_enable && !compact_deletion_enable -> none

    bool force_compact_deletion = storage_->force_compact_deletion_.load();
    if ((!FLAGS_bg_auto_compact_all_forbid && FLAGS_bg_auto_compact_all_enable
          && !force_compact_deletion) || storage_->ReachCompactAllThreshold()) {
      LOG(INFO) << "compact all for deletion("
                << storage_->num_deleted_for_compact_all_.load()
                << ") > threshold("
                << FLAGS_bg_auto_compact_all_deletion_threshold << ") start...";

      storage_->num_deleted_for_compact_all_.store(0);
      storage_->force_compact_deletion_.store(false);
      MFC(storage_->metrics_.num_will_retrieve_deletion_)->Set(0);

      MFC(storage_->metrics_.force_compact_all_time_)->Set(1);
      auto start = std::chrono::steady_clock::now();
      storage_->bg_compact_in_progress_ = true;
      storage_->ForceCompactAll();
      storage_->bg_compact_in_progress_ = false;
      auto end = std::chrono::steady_clock::now();
      auto used = std::chrono::duration_cast<std::chrono::seconds>(end - start).count();
      LOG(INFO) << "compact all cost: " << used << "s";
      MFC(storage_->metrics_.force_compact_all_time_)->Set(0);
    } else if (FLAGS_bg_auto_compact_deletion_enable || force_compact_deletion) {
      LOG(INFO) << (force_compact_deletion ? "force " : "") << "compact deletion start...";

      storage_->force_compact_deletion_.store(false);
      MFC(storage_->metrics_.force_compact_deletion_time_)->Set(1);
      auto start = std::chrono::steady_clock::now();
      storage_->bg_compact_in_progress_ = true;
      storage_->ForceCompactDeletion();
      storage_->bg_compact_in_progress_ = false;
      auto end = std::chrono::steady_clock::now();
      auto used = std::chrono::duration_cast<std::chrono::seconds>(end - start).count();
      LOG(INFO) << "compact deletion cost: " << used << "s";
      MFC(storage_->metrics_.force_compact_deletion_time_)->Set(0);
    }
  }

  return true;
}

void MetaStorage::BatchWrite(const std::vector<meta_storage::WriteTask*>& tasks) {
  CHECK(tasks.size() > 0);

  StopWatch sw(metrics_.single_writer_merged_batch_time_);
  sw.Start();

  bool inode_id_changed = false;
  bool block_id_changed = false;
  bool generation_stamp_v2_changed = false;
  bool snapshot_id_changed = false;
  int64_t num_inodes_delta = 0;
  int64_t max_txid = kInvalidTxId;
  // merge all write items into one write batch
  std::shared_ptr<rocksdb::WriteBatchBase> merged_batch;
  rocksdb::WriteBatch* merged_batch_to_commit;
  bool verify_before_commit = FLAGS_dfs_meta_storage_verify_before_commit;
  if (verify_before_commit) {
    auto mb = std::make_shared<rocksdb::WriteBatchWithIndex>();
    merged_batch_to_commit = mb->GetWriteBatch();
    merged_batch = mb;
  } else {
    auto mb = std::make_shared<rocksdb::WriteBatch>();
    merged_batch_to_commit = mb.get();
    merged_batch = mb;
  }
  meta_storage::WriteBatchMergeHandler handler(this, merged_batch);

  for (auto task : tasks) {
    if (task->max_inode_id() != kInvalidINodeId &&
        task->max_inode_id() > last_inode_id_) {
      last_inode_id_ = task->max_inode_id();
      inode_id_changed = true;
    }
    if (task->max_block_id() != kInvalidBlockID &&
        task->max_block_id() > last_block_id_) {
      last_block_id_ = task->max_block_id();
      block_id_changed = true;
    }
    if (task->max_generation_stamp_v2() != kInvalidGenerationStamp &&
        task->max_generation_stamp_v2() > last_generation_stamp_v2_) {
      last_generation_stamp_v2_ = task->max_generation_stamp_v2();
      generation_stamp_v2_changed = true;
    }
    if (task->max_snapshot_id() > last_snapshot_id_) {
      last_snapshot_id_ = task->max_snapshot_id();
      snapshot_id_changed = true;
    }
    if (task->txid() != kInvalidTxId) {
      if (max_txid != kInvalidTxId) {
        CHECK_EQ(max_txid + 1, task->txid());
      }
      max_txid = task->txid();
    }
    if (verify_before_commit && task->verify_kvs()) {
      auto wbwi = std::dynamic_pointer_cast<rocksdb::WriteBatchWithIndex>(
                      merged_batch);
      for (auto& ent : *task->verify_kvs()) {
        std::string value;
        rocksdb::Status s = wbwi->GetFromBatchAndDB(rocks_db_.get(),
                                                    rocksdb::ReadOptions(),
                                                    handles_[ent.cfid],
                                                    ent.key,
                                                    &value);
        if (!s.ok()) {
          LOG(FATAL) << absl::StrFormat(
              "failed to get txid %lu { %d, %s, %s } from batch-and-db: %s",
              task->txid(), ent.cfid, ent.key, ent.value, s.ToString());
        }
        if (value != ent.value) {
          // XXX currently we only verify INode, so transform to INode is ok
          INode inode_ondisk, inode_expected;
          if (inode_ondisk.ParseFromString(value) &&
              inode_expected.ParseFromString(ent.value)) {
            // Two protobuf objects with identical fields can be serialized into
            // bytes with different field orders, making byte comparison
            // unreliable. In the future, we should find a way to consistently
            // serialize protobuf objects in a single order to solve this issue.
            // The following are just workarounds.
            // https://bytedance.larkoffice.com/docx/T4godpKgeoEpHlx7pkPc0PQTnwe
            //
            // `MessageDifferencer` would be a better solution for this problem.
            // However, to support it in the current protobuf version (v2.5.0),
            // we would need to add many files, as it's only supported from
            // version v3.0.0-beta-1 onwards. Therefore, I've decided against
            // this solution.
            // https://protobuf.dev/reference/cpp/api-docs/google.protobuf.util.message_differencer/
            //
            // `ShortDebugString` appears to iterate over all fields of the
            // message in a depth-first search manner. At the same level, fields
            // are processed in order based on their field numbers. Therefore,
            // two protobuf objects with the same content should produce
            // identical `ShortDebugString` outputs.
            // https://github.com/protocolbuffers/protobuf/blob/v2.5.0/src/google/protobuf/text_format.cc#L1181
            // https://github.com/protocolbuffers/protobuf/blob/v2.5.0/src/google/protobuf/message.h#L414
            //   "Fields (both normal fields and extension fields) will be
            //   listed ordered by field number."
            if (inode_ondisk.ShortDebugString() !=
                inode_expected.ShortDebugString()) {
              LOG(FATAL) << absl::StrFormat(
                  "failed to verify kv of txid %lu before commit: "
                  "ondisk INode: %s, expected INode: %s",
                  task->txid(),
                  inode_ondisk.ShortDebugString(),
                  inode_expected.ShortDebugString());
            }
          } else {
            LOG(FATAL) << absl::StrFormat(
                "failed to verify kv of txid %lu before commit: "
                "ondisk value string: %s, expected value string: %s",
                task->txid(),
                value,
                ent.value);
          }
        }
      }
    }
    if (task->wb() != nullptr) {
      auto s = task->wb()->Iterate(&handler);
      if (!s.ok()) {
        LOG(FATAL) << "Failed to iterate write batch: " << s.ToString();
      }
    }
    num_inodes_delta += task->num_inodes_delta();
  }

  // update last transaction id
  if (max_txid != kInvalidTxId) {
    FillNameSpaceInfoWriteBatch(
        kLastCkptTxIdKey, static_cast<uint64_t>(max_txid), merged_batch);
  }

  // update max inode id, max block id, and max gsv2
  if (inode_id_changed) {
    FillNameSpaceInfoWriteBatch(
        kLastINodeIdKey, last_inode_id_, merged_batch);
  }
  if (block_id_changed) {
    FillNameSpaceInfoWriteBatch(
        kLastAllocatedBlockIdKey, last_block_id_, merged_batch);
  }
  if (generation_stamp_v2_changed) {
    FillNameSpaceInfoWriteBatch(
        kGenerationStampV2Key, last_generation_stamp_v2_, merged_batch);
  }
  if (snapshot_id_changed) {
    FillNameSpaceInfoWriteBatch(
        kLastSnapshotIdKey, last_snapshot_id_, merged_batch);
  }

  // update inode total number
  // num_inodes_ < 0 indicate num_inodes_ is not initialized
  if (num_inodes_delta != 0 && num_inodes_ >= 0) {
    num_inodes_ += num_inodes_delta;
    if (num_inodes_ >= 0) {
      FillNameSpaceInfoWriteBatch(
          kNumINodesKey, static_cast<uint64_t>(num_inodes_), merged_batch);
    } else {
      LOG(ERROR) << "Must num_inodes_ >= 0, actual: " << num_inodes_;
    }
  }

  sw.NextStep(metrics_.single_writer_write_rocksdb_time_);

  // write into rocksdb
  auto s = rocks_db_->Write(rocks_write_option_, merged_batch_to_commit);
  if (!s.ok()) {
    LOG(FATAL) << "Failed to write batch, error: " + s.ToString();
  }

  // wake up all writers
  sw.NextStep();
  MFH(metrics_.write_items_batch_num_)->Update(tasks.size());
}

void MetaStorage::PostSlow(meta_storage::WriteTask* task) {
  slows_->Post(task);
}

void MetaStorage::TxFinish(int64_t start_txid, int n) {
  writer_->TxFinish(start_txid, n);
}

void MetaStorage::StartActive() {
  writer_->StartActive();
}

void MetaStorage::StartStandby() {
  writer_->StartStandby();
}

void MetaStorage::NotifyForActive(int64_t txid) {
  writer_->NotifyForActive(txid);
}

std::unique_ptr<rocksdb::WriteBatch> MetaStorage::CreateWriteBatch() {
  return std::make_unique<rocksdb::WriteBatch>();
}

Status MetaStorage::GetDirectoryINodeStat(const INodeID dir_id,
                                          INodeStat* stat,
                                          MetaStorageIterPtr iter) {
  CHECK(stat != nullptr);
  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = std::make_unique<MetaStorageIterHolder>();
    iter_holder->iter_ =
        std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
            rocksdb::ReadOptions(), handles_[kINodeStatCFIndex]));
    iter = iter_holder->iter();
  }

  uint64_t key = platform::HostToBigEndian(dir_id);
  auto key_slice = rocksdb::Slice(reinterpret_cast<const char*>(&key), sizeof(key));

  std::string value;
  auto s = rocks_db_->Get(rocksdb::ReadOptions(),
                          handles_[kINodeStatCFIndex],
                          key_slice,
                          &value);
  if (!s.ok()) {
    LOG(ERROR) << "Failed to get INodeStat for dir: " << dir_id << ", error: " << s.ToString();
    return Status(s.IsNotFound() ? Code::kINodeStatNotFound : Code::kError,
                  "Failed to get director stat: " + s.ToString());
  }
  return INodeStatUtils::Parse(value, stat);
}

void MetaStorage::PutDirectoryINodeStatAsync(const INode &dir,
                                             const INodeStat &stat,
                                             Closure *done) {
  CHECK(dir.type() == INode_Type_kDirectory);

  uint64_t key = platform::HostToBigEndian(dir.id());
  auto key_slice =
      rocksdb::Slice(reinterpret_cast<const char *>(&key), sizeof(key));

  auto wb = CreateWriteBatch();
  std::string value = stat.Serialize();
  wb->Put(handles_[kINodeStatCFIndex], key_slice, value);

  PushINodeBGWriteTask(std::move(wb), 0, done);
}

Status MetaStorage::PutDirectoryINodeStat(const INode &dir,
                                          const INodeStat &stat) {
  SynchronizedClosure done;
  PutDirectoryINodeStatAsync(dir, stat, &done);
  done.Await();
  return done.status();
}

Status MetaStorage::GetLifecyclePolicy(const INodeID id,
                                       uint64_t* ts,
                                       LifecyclePolicyProto* policy) {
  std::string key;
  std::string value;
  key = EncodeINodeID(id);
  auto st = rocks_db_->Get(rocksdb::ReadOptions(),
                           handles_[kLifecyclePolicyCFIndex],
                           rocksdb::Slice(key.data(), key.length()),
                           &value);
  if (!st.ok()) {
    CHECK(st.IsNotFound());
    return Status(Code::kNoEntry,
                  absl::StrFormat("No LifecyclePolicyInfoProto entry for %lu", id));
  }

  cloudfs::LifecyclePolicyInfoProto proto;
  if (!proto.ParseFromString(value)) {
    LOG(FATAL) << "Failed to parse LifecyclePolicyInfoProto of " << std::to_string(id);
    return Status(Code::kError,
                  absl::StrFormat("Failed to parse LifecyclePolicyInfoProto of %lu", id));
  }
  if (ts) {
    *ts = proto.timestampms();
  }
  if (policy) {
    policy->CopyFrom(proto.policy());
  }
  return Status();
}

void MetaStorage::OrderedPutLifecyclePolicy(const INodeID id,
                                            const uint64_t ts,
                                            const LifecyclePolicyProto& policy,
                                            int64_t txid,
                                            Closure* done) {
  MFC(metrics_.lifecycle_update_policy_num_)->Inc();

  std::string key;
  std::string value;
  key = EncodeINodeID(id);
  cloudfs::LifecyclePolicyInfoProto proto;
  proto.set_timestampms(ts);
  proto.mutable_policy()->CopyFrom(policy);
  proto.SerializeToString(&value);

  auto wb = CreateWriteBatch();
  wb->Put(handles_[kLifecyclePolicyCFIndex],
          rocksdb::Slice(key.data(), key.length()),
          rocksdb::Slice(value.data(), value.length()));
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

void MetaStorage::OrderedDeleteLifecyclePolicy(const INodeID id,
                                               int64_t txid,
                                               Closure* done) {
  MFC(metrics_.lifecycle_delete_policy_num_)->Inc();

  std::string key;
  key = EncodeINodeID(id);

  auto wb = CreateWriteBatch();
  wb->Delete(handles_[kLifecyclePolicyCFIndex],
          rocksdb::Slice(key.data(), key.length()));
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

MetaStorageIterHolderPtr MetaStorage::GetLifecyclePolicyIterator() {
  auto holder = std::make_unique<MetaStorageIterHolder>();
  holder->iter_ = std::unique_ptr<rocksdb::Iterator>(
                      rocks_db_->NewIterator(rocksdb::ReadOptions(),
                                             handles_[kLifecyclePolicyCFIndex]));
  return holder;
}

void MetaStorage::ScanLifecyclePolicy(
    std::function<bool(const INodeID,
                       const uint64_t,
                       const LifecyclePolicyProto&)> callback,
    MetaStorageIterPtr iter) {
  CHECK_NOTNULL(iter);

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    INodeID inode_id = DecodeINodeID(iter->key());
    CHECK_NE(inode_id, kInvalidINodeId);

    cloudfs::LifecyclePolicyInfoProto proto;
    if (!proto.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Corrupted value in LifecyclePolicy column family.";
    }
    if (!callback(inode_id, proto.timestampms(), proto.policy())) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "RocksDB error: " << st.ToString();
}

Status MetaStorage::GetStorageClassStat(const INodeID inode_id,
                                        StorageClassStatProto* stat,
                                        MetaStorageIterPtr iter) {
  CHECK_NE(inode_id, kInvalidINodeId);
  CHECK_NOTNULL(stat);

  MetaStorageIterHolderPtr iter_holder;
  if (iter == nullptr) {
    iter_holder = std::make_unique<MetaStorageIterHolder>();
    iter_holder->iter_ =
        std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
            rocksdb::ReadOptions(), handles_[kStorageClassStatCFIndex]));
    iter = iter_holder->iter();
  }

  std::string key = EncodeINodeID(inode_id);
  std::string value;
  auto st = rocks_db_->Get(rocksdb::ReadOptions(),
                           handles_[kStorageClassStatCFIndex],
                           rocksdb::Slice(key.data(), key.length()),
                           &value);

  if (!st.ok()) {
    CHECK(st.IsNotFound());
    return Status(Code::kNoEntry,
                  absl::StrFormat("No StorageClassStatProto entry for %lu", inode_id));
  }

  if (!stat->ParseFromString(value)) {
    LOG(FATAL) << absl::StrFormat(
        "Failed to parse StorageClassStatProto of %lu", inode_id);
  }
  return Status();
}

void MetaStorage::PutStorageClassStatAsync(const INodeID inode_id,
                                           const StorageClassStatProto& stat,
                                           Closure* done) {
  CHECK_NE(inode_id, kInvalidINodeId);

  std::string key = EncodeINodeID(inode_id);
  std::string value;
  stat.SerializeToString(&value);
  auto wb = CreateWriteBatch();
  wb->Put(handles_[kStorageClassStatCFIndex],
          rocksdb::Slice(key.data(), key.length()),
          rocksdb::Slice(value.data(), value.length()));
  PushINodeBGWriteTask(std::move(wb), 0, done);
}

Status MetaStorage::PutStorageClassStat(const INodeID inode_id,
                                        const StorageClassStatProto& stat) {
  SynchronizedClosure done;
  PutStorageClassStatAsync(inode_id, stat, &done);
  done.Await();
  return done.status();
}

void MetaStorage::DeleteStorageClassStatAsync(const INodeID inode_id,
                                              Closure* done) {
  CHECK_NE(inode_id, kInvalidINodeId);
  std::string key = EncodeINodeID(inode_id);
  auto wb = CreateWriteBatch();
  wb->Delete(handles_[kStorageClassStatCFIndex],
             rocksdb::Slice(key.data(), key.length()));
  PushINodeBGWriteTask(std::move(wb), 0, done);
}

void MetaStorage::ScanStorageClassStat(
    std::function<bool(const INodeID,
                       const StorageClassStatProto&)> callback,
    MetaStorageIterPtr iter) {
  CHECK_NOTNULL(iter);

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    INodeID inode_id = DecodeINodeID(iter->key());
    CHECK_NE(inode_id, kInvalidINodeId);

    StorageClassStatProto proto;
    if (!proto.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Corrupted value in StorageClassStat column family.";
    }
    if (!callback(inode_id, proto)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "RocksDB error: " << st.ToString();
}

void MetaStorage::PutAZBlacklistAsync(const std::string& azs,
                                      int64_t txid,
                                      Closure* done) {
  auto wb = CreateWriteBatch();
  wb->Put(handles_[kNameSystemInfoCFIndex],
          kAZBlacklistKey,
          azs);
  PushINodeTXWriteTask(std::move(wb), txid, {}, nullptr, done);
}

void MetaStorage::GetAZBlacklist(std::string* azs) {
  std::string azs_str;
  auto st = rocks_db_->Get(rocksdb::ReadOptions(),
                           handles_[kNameSystemInfoCFIndex],
                           kAZBlacklistKey,
                           &azs_str);
  if (!st.ok()) {
    if (st.IsNotFound()) {
      azs->clear();
      return;
    } else {
      LOG(FATAL) << absl::StrFormat(
          "Failed to get AZ blacklist, error: %s", st.ToString());
    }
  }
  *azs = azs_str;
}

void MetaStorage::GetStorageClassReports(
    const BlockID block_id,
    std::vector<std::string>* dn_uuids,
    std::vector<StorageClassReportProto>* reports) {
  MFC(metrics_.get_scrs_num_)->Inc();
  StopWatch sw_total(metrics_.get_scrs_time_);
  sw_total.Start();

  rocksdb::ReadOptions read_options;
  read_options.prefix_same_as_start = true;
  std::string prefix = EncodeBlockID(block_id);
  auto iter = std::unique_ptr<rocksdb::Iterator>(
      rocks_db_->NewIterator(read_options,
                             handles_[kStorageClassReportCFIndex]));
  bool seeked = false;
  int nvalid = 0;
  while (true) {
    BlockID blkid;
    std::string dnuuid;
    bool got_one = false;

    StopWatch sw_one;
    sw_one.Start();
    ROCKSDB_PERF_PCIOC_START(scr);
    {
      // Seek() or Next() one key
      bool skip_this = false;
      do {
        if (!seeked) {
          iter->Seek(prefix);
          seeked = true;
        } else {
          iter->Next();
        }
        if (!iter->Valid()) {
          got_one = false;
          break;
        }
        DecodeSCRKey(iter->key(), &blkid, &dnuuid);
        if (blkid != block_id) {
          got_one = false;
          break;
        }
        got_one = true;
        if (dnuuid.empty()) {
          // ignore deprecated key-value format, it will be cleanup in
          // LifecycleScanner::FilterDepredStorageClassReport()
          skip_this = true;
        }
      } while (0);
      if (skip_this) {
        continue;
      }
    }
    ROCKSDB_PERF_PCIOC_END(scr);
    auto duration =
        std::chrono::duration_cast<std::chrono::microseconds>(
            sw_one.GetTime()).count();
    if (got_one) {
      nvalid++;
      MFH(metrics_.get_scrs_seek_valid_next_time_)->Update(duration);
    } else {
      MFH(metrics_.get_scrs_seek_invalid_next_time_)->Update(duration);
      break;
    }

    if (dn_uuids) {
      dn_uuids->push_back(dnuuid);
    }
    if (reports) {
      StorageClassReportProto rpt;
      if (!rpt.ParseFromArray(iter->value().data(), iter->value().size())) {
        LOG(FATAL) << absl::StrFormat(
            "Failed to parse StorageClassReport of key { %lu, %s }",
            blkid, dnuuid);
      }
      reports->push_back(rpt);
    }
  }
  MFH(metrics_.get_scrs_count_valid_)->Update(nvalid);

  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "System error: " << st.ToString();
}

Status MetaStorage::GetStorageClassReport(const BlockID block_id,
                                          const std::string& dn_uuid,
                                          StorageClassReportProto* report) {
  std::string key;
  EncodeSCRKey(block_id, dn_uuid, &key);
  std::string value;
  auto s = rocks_db_->Get(rocksdb::ReadOptions(),
                          handles_[kStorageClassReportCFIndex],
                          rocksdb::Slice(key.data(), key.length()),
                          &value);
  if (!s.ok()) {
    if (s.IsNotFound()) {
      return Status(Code::kNoEntry, s.ToString());
    }
    std::string msg = absl::StrFormat(
        "Failed to get StorageClassReport data, key: { %lu, %s }, error: %s",
        block_id, dn_uuid, s.ToString());
    LOG(ERROR) << msg;
    return Status(Code::kError, msg);
  }
  if (!report->ParseFromString(value)) {
    std::string msg = absl::StrFormat(
        "Failed to parse StorageClassReport of key { %lu, %s }",
        block_id, dn_uuid);
    LOG(ERROR) << msg;
    return Status(Code::kError, msg);
  }
  return Status::OK();
}

void MetaStorage::UpdateStorageClassReportsAsync(
    const std::string& dn_uuid,
    std::vector<std::pair<BlockID, StorageClassReportProto>>& replica_report,
    Closure* done) {
  MFC(metrics_.lifecycle_report_storage_class_num_)->Inc(replica_report.size());
  MFC(metrics_.update_scrs_num_)->Inc();
  StopWatch sw_total(metrics_.update_scrs_time_);
  sw_total.Start();

  int ndel = 0;
  int nput = 0;
  int ndel_filtered = 0;
  int nput_filtered = 0;
  auto wb = CreateWriteBatch();
  int wb_nop = 0;
  for (auto&& p : replica_report) {
    BlockID blkid = p.first;
    std::string key;
    EncodeSCRKey(blkid, dn_uuid, &key);

    // check on-disk data
    StorageClassProto stcls = StorageClassProto::NONE;
    bool exist = false;
    bool pinned = false;
    StorageClassReportProto ondisk_rpt;
    Status st = GetStorageClassReport(blkid, dn_uuid, &ondisk_rpt);
    if (st.IsOK()) {
      exist = true;
      stcls = ondisk_rpt.stcls();
      if (ondisk_rpt.has_pinned()) {
        pinned = ondisk_rpt.pinned();
      }
    }

    // filter redundant update/delete
    if (p.second.stcls() == StorageClassProto::NONE) {
      if (!exist) {
        ndel_filtered++;
      } else {
        ndel++;
        // XXX cls==NONE indicates remove corresponding entry,
        // it shall NEVER be persisted into MetaStorage
        wb->Delete(handles_[kStorageClassReportCFIndex],
                   rocksdb::Slice(key.data(), key.length()));
        wb_nop++;
      }
    } else {
      if (exist &&
          p.second.stcls() == stcls &&
          p.second.pinned() == pinned) {
        nput_filtered++;
      } else {
        nput++;
        std::string value;
        p.second.SerializeToString(&value);
        wb->Put(handles_[kStorageClassReportCFIndex],
                rocksdb::Slice(key.data(), key.length()),
                rocksdb::Slice(value.data(), value.length()));
        wb_nop++;
      }
    }

    if (wb_nop >= FLAGS_dfs_meta_storage_update_scr_batch_size) {
      SynchronizedClosure done;
      PushINodeBGWriteTask(std::move(wb), 0, &done);
      done.Await();
      wb = CreateWriteBatch();
      wb_nop = 0;
    }
  }
  MFH(metrics_.update_scrs_count_del_)->Update(ndel);
  MFH(metrics_.update_scrs_count_put_)->Update(nput);
  MFH(metrics_.update_scrs_count_del_filtered_)->Update(ndel_filtered);
  MFH(metrics_.update_scrs_count_put_filtered_)->Update(nput_filtered);
  if (ndel + nput > 0) {
    PushINodeBGWriteTask(std::move(wb), 0, done);
  } else {
    if (done) {
      done->Run();
    }
  }
}

void MetaStorage::DeleteStorageClassReportAsync(const BlockID block_id,
                                                const std::string& dn_uuid,
                                                Closure* done) {
  CHECK_NE(block_id, kInvalidBlockID);
  std::string key;
  EncodeSCRKey(block_id, dn_uuid, &key);
  auto wb = CreateWriteBatch();
  wb->Delete(handles_[kStorageClassReportCFIndex],
             rocksdb::Slice(key.data(), key.length()));
  PushINodeBGWriteTask(std::move(wb), 0, done);
}

void MetaStorage::ScanStorageClassReport(
    std::function<bool(const BlockID,
                       const std::string&,
                       const StorageClassReportProto&)> callback,
    MetaStorageIterPtr iter) {
  CHECK_NOTNULL(iter);

  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    BlockID block_id;
    std::string dn_uuid;
    DecodeSCRKey(iter->key(), &block_id, &dn_uuid);
    CHECK_NE(block_id, kInvalidBlockID);

    StorageClassReportProto proto;
    if (!proto.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL) << "Corrupted value in StorageClassReport column family.";
    }
    if (!callback(block_id, dn_uuid, proto)) {
      break;
    }
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "RocksDB error: " << st.ToString();
}

void MetaStorage::ScanLease(
    const std::function<void(INodeID inode_id, const std::string& client_name)>&
        callback,
    MetaStorageIterPtr iter) {
  CHECK_NOTNULL(iter);
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    INodeID inode_id = DecodeINodeID(iter->key());
    CHECK_NE(inode_id, kInvalidINodeId);
    FileUnderConstructionFeature uc;
    if (!uc.ParseFromArray(iter->value().data(), iter->value().size())) {
      LOG(FATAL)
          << "Corrupted value in FileUnderConstructionFeature column family.";
    }
    callback(inode_id, uc.client_name());
  }
  auto st = iter->status();
  LOG_IF(ERROR, !st.ok()) << "RocksDB error: " << st.ToString();
}

ReadOnlyMetaStorage::ReadOnlyMetaStorage(const std::string& db_path)
    : MetaStorage(db_path) {
}

std::unique_ptr<rocksdb::DB> ReadOnlyMetaStorage::OpenRocksDB(
    const rocksdb::DBOptions& opt) {
  rocksdb::DB* p = nullptr;
  rocksdb::Status s = rocksdb::DB::OpenForReadOnly(
      opt, db_path_, column_families_, &handles_, &p);
  if (!s.ok()) {
    LOG(FATAL) << "Failed to open DB: " << db_path_
               << ", status: " << s.ToString();
  }
  return std::unique_ptr<rocksdb::DB>(p);
}

inline void EnsureValidINode(const INode& n) {
  if (n.id() != kRootINodeId) {
    CHECK(n.id() != n.parent_id()) << "Invalid Inode: " << n.id();
  }
}

void MetaStorage::OrderedCreateUfsFiles(
    int64_t txid,
    const std::vector<INodeWithBlocks>& files,
    const INode* dir_inode,
    const std::vector<Closure*>& dones) {
  NameSpaceInfoDelta delta;
  CHECK(!files.empty());
  auto wb = CreateWriteBatch();

  for (auto&& f : files) {
    EnsureValidINode(f.node);

    std::string key;
    std::string value;
    EncodeStoreKey(f.node.parent_id(), f.node.name(), f.node.id(), &key);
    f.node.SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    UpdateParentIndexWriteBatch(f.node, false, wb.get());
    UpdateLeaseWriteBatch(f.node, false, wb.get());
    UpdateWriteBackTaskWriteBatch(f.node, false, wb.get());

    for (auto&& info : f.blocks) {
      std::string block_key = EncodeBlockID(info.block_id());
      std::string block_value;
      CHECK(info.SerializeToString(&block_value));
      wb->Put(handles_[kBlockInfoProtoCFIndex], block_key, block_value);
      if (delta.max_block_id == kInvalidBlockID ||
          info.block_id() > delta.max_block_id) {
        delta.max_block_id = info.block_id();
      }
      if (delta.max_gs_v2 == kInvalidGenerationStamp ||
          info.gen_stamp() > delta.max_gs_v2) {
        delta.max_gs_v2 = info.gen_stamp();
      }
    }
    if (delta.max_inode_id == kInvalidINodeId ||
        f.node.id() > delta.max_inode_id) {
      delta.max_inode_id = f.node.id();
    }
  }

  delta.num_inodes_delta = files.size();
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (dir_inode) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*dir_inode, verify_kvs.get());
  }
  PushINodeTXWriteTasks(
      std::move(wb), txid, delta, std::move(verify_kvs), dones);
}

void MetaStorage::OrderedUpdateUfsINodes(int64_t txid,
                                         const std::vector<INode>& files,
                                         const std::vector<INode>* old_files,
                                         Closure* done) {
  CHECK(!files.empty());
  auto wb = CreateWriteBatch();

  NameSpaceInfoDelta delta;
  for (auto&& inode : files) {
    EnsureValidINode(inode);

    std::string key;
    std::string value;
    EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
    inode.SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    UpdateLeaseWriteBatch(inode, false, wb.get());
    UpdateWriteBackTaskWriteBatch(inode, false, wb.get());

    if (delta.max_inode_id == kInvalidINodeId ||
        inode.id() > delta.max_inode_id) {
      delta.max_inode_id = inode.id();
    }
  }

  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_files && !old_files->empty()) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    for (const auto& old_file : *old_files) {
      FillINodeKVVerifyVec(old_file, verify_kvs.get());
    }
  }

  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedCreateUfsFile(int64_t txid,
                                       const INodeWithBlocks& new_file,
                                       Closure* done) {
  OrderedOverwriteUfsFile(txid, new_file, nullptr, done);
}

void MetaStorage::OrderedOverwriteUfsFile(int64_t txid,
                                          const INodeWithBlocks& new_file,
                                          const INode* old_file,
                                          Closure* done) {
  NameSpaceInfoDelta delta;
  auto wb = CreateWriteBatch();

  std::string value;
  if (old_file != nullptr) {
    EnsureValidINode(*old_file);

    std::string key;
    EncodeStoreKey(
        old_file->parent_id(), old_file->name(), old_file->id(), &key);
    old_file->SerializeToString(&value);
    wb->Delete(handles_[kINodeDefaultCFIndex], key);
    FillDeprecatingBlockWriteBatch(*old_file, wb.get());
    UpdateParentIndexWriteBatch(*old_file, true, wb.get());
    UpdateLeaseWriteBatch(*old_file, true, wb.get());
    UpdateWriteBackTaskWriteBatch(*old_file, true, wb.get());

    if (old_file->type() == INode_Type_kFile) {
      for (auto&& b : old_file->blocks()) {
        std::string block_key = EncodeBlockID(b.blockid());
        wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
        wb->Delete(handles_[kLocalBlockCFIndex], key);
        wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
        wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
      }
    }
  }

  {
    EnsureValidINode(new_file.node);
    std::string key;
    std::string value;
    EncodeStoreKey(new_file.node.parent_id(),
                   new_file.node.name(),
                   new_file.node.id(),
                   &key);
    new_file.node.SerializeToString(&value);
    wb->Put(handles_[kINodeDefaultCFIndex], key, value);
    UpdateParentIndexWriteBatch(new_file.node, false, wb.get());
    UpdateLeaseWriteBatch(new_file.node, false, wb.get());
    UpdateWriteBackTaskWriteBatch(new_file.node, false, wb.get());

    for (auto&& info : new_file.blocks) {
      std::string block_key = EncodeBlockID(info.block_id());
      std::string block_value;
      CHECK(info.SerializeToString(&block_value));
      wb->Put(handles_[kBlockInfoProtoCFIndex], block_key, block_value);

      if (delta.max_block_id == kInvalidBlockID ||
          info.block_id() > delta.max_block_id) {
        delta.max_block_id = info.block_id();
      }
      if (delta.max_gs_v2 == kInvalidGenerationStamp ||
          info.gen_stamp() > delta.max_gs_v2) {
        delta.max_gs_v2 = info.gen_stamp();
      }
    }
  }

  std::unique_ptr<KVVerifyVec> verify_kvs = nullptr;
  if (old_file != nullptr) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    FillINodeKVVerifyVec(*old_file, verify_kvs.get());
  }
  delta.max_inode_id = new_file.node.id();
  delta.num_inodes_delta = (old_file == nullptr) ? 1 : 0;
  PushINodeTXWriteTask(std::move(wb), txid, delta, std::move(verify_kvs), done);
}

void MetaStorage::OrderedUpdateUfsBlockInfo(int64_t txid,
                                            const BlockInfoProto& bip,
                                            const BlockInfoProto* old_bip,
                                            Closure* done) {
  auto wb = CreateWriteBatch();
  std::string key = EncodeBlockID(bip.block_id());
  std::string value;
  CHECK(bip.SerializeToString(&value)) << bip.block_id();
  wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
  std::unique_ptr<KVVerifyVec> verify_kvs;
  if (old_bip) {
    verify_kvs = std::make_unique<KVVerifyVec>();
    verify_kvs->emplace_back(kBlockInfoProtoCFIndex,
                             EncodeBlockID(old_bip->block_id()),
                             old_bip->SerializeAsString());
  }
  auto task = new meta_storage::WriteTask();
  task->set_txid(txid);
  task->set_wb(std::move(wb));
  task->set_done(done);
  task->set_verify_kvs(std::move(verify_kvs));
  writer_->Push(task);
}

void MetaStorage::PutJobInfo(const JobInfoOpBody& job_info,
                             int64_t txid,
                             Closure* done) {
  auto wb = CreateWriteBatch();
  auto job_id = job_info.job_id();
  std::string job_info_string;
  job_info.SerializeToString(&job_info_string);
  wb->Put(handles_[kJobInfoCFIndex], job_id, job_info_string);
  auto task = new meta_storage::WriteTask();
  task->set_wb(std::move(wb));
  task->set_done(done);
  if (txid == kInvalidTxId) {
    writer_->PushBGTask(task);
  } else {
    task->set_txid(txid);
    writer_->Push(task);
  }
}

void MetaStorage::DeleteJobInfo(const std::string& job_id,
                                int64_t txid,
                                Closure* done) {
  (void)txid;
  auto wb = CreateWriteBatch();
  wb->Delete(handles_[kJobInfoCFIndex], job_id);
  auto task = new meta_storage::WriteTask();
  task->set_wb(std::move(wb));
  task->set_done(done);
  writer_->PushBGTask(task);
}

std::unique_ptr<rocksdb::Iterator> MetaStorage::GetJobInfoIterator() {
  return std::unique_ptr<rocksdb::Iterator>(rocks_db_->NewIterator(
      rocksdb::ReadOptions(), handles_[kJobInfoCFIndex]));
}

bool MetaStorage::GetJobInfo(const std::string& job_id,
                             JobInfoOpBody& job_info) {
  std::string value;
  rocksdb::Status s = rocks_db_->Get(
      rocksdb::ReadOptions(), handles_[kJobInfoCFIndex], job_id, &value);
  if (!s.ok()) {
    if (s.IsNotFound()) {
      return false;
    }
    LOG(FATAL) << "Failed to get job id: " << job_id
               << ", error: " << s.ToString();
  }

  job_info.ParseFromString(value);
  return true;
}

#define SUBMIT_PC(name)                             \
    MFH(metrics_.name##_)->Update(pc->name);
#define SUBMIT_IOC(name)                            \
    MFH(metrics_.name##_)->Update(ioc->name);

void MetaStorage::SubmitPCIOCMetrics() {
  auto pc = rocksdb::get_perf_context();
  SUBMIT_PC(user_key_comparison_count);
  SUBMIT_PC(block_cache_hit_count);
  SUBMIT_PC(block_read_count);
  SUBMIT_PC(block_read_byte);
  SUBMIT_PC(block_read_time);
  SUBMIT_PC(block_checksum_time);
  SUBMIT_PC(block_decompress_time);
  SUBMIT_PC(internal_key_skipped_count);
  SUBMIT_PC(internal_delete_skipped_count);
  SUBMIT_PC(internal_recent_skipped_count);
  SUBMIT_PC(internal_merge_count);
  SUBMIT_PC(get_snapshot_time);
  SUBMIT_PC(get_from_memtable_time);
  SUBMIT_PC(get_from_memtable_count);
  SUBMIT_PC(get_post_process_time);
  SUBMIT_PC(get_from_output_files_time);
  SUBMIT_PC(seek_on_memtable_time);
  SUBMIT_PC(seek_on_memtable_count);
  SUBMIT_PC(next_on_memtable_count);
  SUBMIT_PC(prev_on_memtable_count);
  SUBMIT_PC(seek_child_seek_time);
  SUBMIT_PC(seek_child_seek_count);
  SUBMIT_PC(seek_min_heap_time);
  SUBMIT_PC(seek_max_heap_time);
  SUBMIT_PC(seek_internal_seek_time);
  SUBMIT_PC(find_next_user_entry_time);
  SUBMIT_PC(write_wal_time);
  SUBMIT_PC(write_memtable_time);
  SUBMIT_PC(write_delay_time);
  SUBMIT_PC(write_pre_and_post_process_time);
  SUBMIT_PC(db_mutex_lock_nanos);
  SUBMIT_PC(db_condition_wait_nanos);
  SUBMIT_PC(merge_operator_time_nanos);
  SUBMIT_PC(read_index_block_nanos);
  SUBMIT_PC(read_filter_block_nanos);
  SUBMIT_PC(new_table_block_iter_nanos);
  SUBMIT_PC(new_table_iterator_nanos);
  SUBMIT_PC(block_seek_nanos);
  SUBMIT_PC(find_table_nanos);
  SUBMIT_PC(bloom_memtable_hit_count);
  SUBMIT_PC(bloom_memtable_miss_count);
  SUBMIT_PC(bloom_sst_hit_count);
  SUBMIT_PC(bloom_sst_miss_count);
  SUBMIT_PC(env_new_sequential_file_nanos);
  SUBMIT_PC(env_new_random_access_file_nanos);
  SUBMIT_PC(env_new_writable_file_nanos);
  SUBMIT_PC(env_reuse_writable_file_nanos);
  SUBMIT_PC(env_new_random_rw_file_nanos);
  SUBMIT_PC(env_new_directory_nanos);
  SUBMIT_PC(env_file_exists_nanos);
  SUBMIT_PC(env_get_children_nanos);
  SUBMIT_PC(env_get_children_file_attributes_nanos);
  SUBMIT_PC(env_delete_file_nanos);
  SUBMIT_PC(env_create_dir_nanos);
  SUBMIT_PC(env_create_dir_if_missing_nanos);
  SUBMIT_PC(env_delete_dir_nanos);
  SUBMIT_PC(env_get_file_size_nanos);
  SUBMIT_PC(env_get_file_modification_time_nanos);
  SUBMIT_PC(env_rename_file_nanos);
  SUBMIT_PC(env_link_file_nanos);
  SUBMIT_PC(env_lock_file_nanos);
  SUBMIT_PC(env_unlock_file_nanos);
  SUBMIT_PC(env_new_logger_nanos);

  auto ioc = rocksdb::get_iostats_context();
  SUBMIT_IOC(thread_pool_id);
  SUBMIT_IOC(bytes_written);
  SUBMIT_IOC(bytes_read);
  SUBMIT_IOC(open_nanos);
  SUBMIT_IOC(allocate_nanos);
  SUBMIT_IOC(write_nanos);
  SUBMIT_IOC(read_nanos);
  SUBMIT_IOC(range_sync_nanos);
  SUBMIT_IOC(fsync_nanos);
  SUBMIT_IOC(prepare_write_nanos);
  SUBMIT_IOC(logger_nanos);
}

}  // namespace dancenn
