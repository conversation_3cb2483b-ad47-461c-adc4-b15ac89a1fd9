#ifndef NAMESPACE_INODE_ATTR_TTL_MANAGER_H_
#define NAMESPACE_INODE_ATTR_TTL_MANAGER_H_

#include "namespace/meta_scanner_v2.h"
#include "namespace/inode_attr_ttl_manager_metrics.h"

namespace dancenn {

class NameSpace;

class INodeAttrTtlScanner : public MetaScannerTaskBase {
 public:
  INodeAttrTtlScanner(NameSpace* ns);
  uint32_t GetCfIdx() override;
  bool PreScan() override;
  bool Handle(MetaStorageSnapPtr snapshot,
              const rocksdb::Slice& key,
              const rocksdb::Slice& value) override;
  bool PostScan() override;
  int64_t GetDelayUs() override;
  std::string ToString() override;

 private:
  NameSpace* ns_{nullptr};

  uint64_t start_time_;
  uint64_t end_time_;
  uint64_t inode_cnt_;
};

class INodeAttrTtlTask : public MetaScannerWorkTask {
 public:
  INodeAttrTtlTask(NameSpace* ns, const INode& inode);

  bool Run(void* arg = nullptr) override;

  // Test
  virtual INode GetINode();

 private:
  NameSpace* ns_{nullptr};
  INode inode_;
};

class INodeAttrTtlStat : public MetaScannerTaskBase {
 public:
  uint32_t GetCfIdx() override;
  bool PreScan() override;
  bool Handle(MetaStorageSnapPtr snapshot,
              const rocksdb::Slice& key,
              const rocksdb::Slice& value) override;
  bool PostScan() override;
  int64_t GetDelayUs() override;
  std::string ToString() override;

  // Test
  virtual uint64_t GetTaskCnt();

 private:
  uint64_t inode_cnt_;

  INodeAttrTtlMetrics metrics_;
};

}

#endif