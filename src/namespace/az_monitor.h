#ifndef _NAMESPACE_AZ_MONITOR_H_
#define _NAMESPACE_AZ_MONITOR_H_

#include <memory>
#include <mutex>
#include <set>

#include "base/status.h"

namespace dancenn {

std::set<std::string> AZStr2Set(const std::string azs);
std::string AZSet2Str(const std::set<std::string> azs);

class NameSpace;
class HAStateBase;
class EditLogContextBase;
class Refresher;

class AZMonitor {
 public:
  explicit AZMonitor(NameSpace* ns,
                     EditLogContextBase* edit_log_ctx);
  ~AZMonitor();

  void Start();
  void Stop();
  std::set<std::string> GetAZBlacklistExpected();
  std::set<std::string> GetAZBlacklistEffective();

 private:
  void DoRefresh();
  void UpdateAZBlacklistForBK(const std::set<std::string>& blacklist);
  void UpdateAZBlacklistForDN(const std::set<std::string>& blacklist);

  NameSpace* ns_;
  EditLogContextBase* edit_log_ctx_;
  std::unique_ptr<Refresher> refresher_;

  std::mutex mtx_;
  std::set<std::string> blacklist_expected_;
  std::set<std::string> blacklist_effective_;
};

}  // namespace dancenn

#endif
