//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include <atomic>
#include <memory>
#include <queue>
#include <random>

#include <absl/strings/str_format.h>

#include "base/constants.h"
#include "base/edit_logger.h"
#include "base/logger_metrics.h"
#include "base/metrics.h"
#include "base/path_util.h"
#include "base/pb_converter.h"
#include "base/to_json_string.h"
#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_pufs_info.h"
#include "datanode_manager/datanode_manager.h"
#include "datanode_manager/storage_policy.h"
#include "edit/sender.h"
#include "lease/lease_manager.h"
#include "namespace/inode.h"
#include "namespace/namespace.h"
#include "namespace/namespace_stat.h"
#include "security/block_token_identifier.h"

DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_bool(retry_cache_enabled);
DECLARE_uint32(edit_log_slow_op_us);
DECLARE_int32(validate_active_write_batch_mode);
DECLARE_int32(namespace_type);
DECLARE_bool(enable_write_back_task_persistence);
DECLARE_bool(enable_fast_block_id_and_gs_gen);
DECLARE_bool(complete_rpc_trigger_file_upload_in_callback);
DECLARE_bool(log_edit_log_detail);
DECLARE_uint32(edit_log_applyer_wait_no_pending_sleep_us);
DECLARE_bool(edit_log_physical_apply_compare_db_enable);
DECLARE_bool(write_back_by_default);

//
// ============= IMPORTANT ================
//
// 1. ApplyOpXXXX must called by one thread
// 2. Txid(n) = Txid(n-1) + 1
//
// ========================================
//

// edit logs related
namespace dancenn {
namespace {

void CompareINodeFromMetaStorageAndEditLog(const INode& inode_from_ms,
                                           const INode& inode_from_editlog) {
  bool mismatch = false;
  do {
    // Basic info.
    if (!(inode_from_ms.id() == inode_from_editlog.id() &&
          inode_from_ms.parent_id() == inode_from_editlog.parent_id() &&
          inode_from_ms.name() == inode_from_editlog.name() &&
          inode_from_ms.type() == inode_from_editlog.type())) {
      DLOG(ERROR) << "Basic info is mismatched";
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      mismatch = true;
      break;
    }
    // Block info.
    if (inode_from_ms.blocks_size() != inode_from_editlog.blocks_size()) {
      DLOG(ERROR) << "Block size is mismatched";
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      mismatch = true;
      break;
    }
    for (auto i = 0; i < inode_from_ms.blocks_size(); i++) {
      const auto& lhs = inode_from_ms.blocks(i);
      const auto& rhs = inode_from_ms.blocks(i);
      if (!(lhs.blockid() == rhs.blockid() && lhs.genstamp() == rhs.genstamp() &&
            lhs.numbytes() == rhs.numbytes())) {
        DLOG(ERROR) << "Block is mismatched";
        MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
        mismatch = true;
        break;
      }
    }
    if (mismatch) {
      break;
    }
    // Permission and acl.
    {
      const auto& lhs = inode_from_ms.permission();
      const auto& rhs = inode_from_editlog.permission();
      if (!(lhs.username() == rhs.username() &&
            lhs.groupname() == rhs.groupname() &&
            lhs.permission() == rhs.permission())) {
        DLOG(ERROR) << "Permission is mismatched";
        MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
        mismatch = true;
        break;
      }
    }
    if (inode_from_ms.acls_size() != inode_from_editlog.acls_size()) {
      LOG(ERROR) << "Acl size is mismatched, inode: " << inode_from_ms.id();
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      if (FLAGS_validate_active_write_batch_mode < 2) {
        mismatch = true;
        break;
      }
    }
    for (auto i = 0;
         i < inode_from_ms.acls_size() && i < inode_from_editlog.acls_size();
         i++) {
      const cloudfs::AclEntryProto& lhs = inode_from_ms.acls(i);
      const cloudfs::AclEntryProto& rhs = inode_from_editlog.acls(i);
      if (!(lhs.type() == rhs.type() && lhs.scope() == rhs.scope() &&
            lhs.permissions() == rhs.permissions())) {
        LOG(ERROR) << "Acl is mismatched, inode: " << inode_from_ms.id();
        MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
        if (FLAGS_validate_active_write_batch_mode < 2) {
          mismatch = true;
          break;
        }
      }
    }
    if (mismatch) {
      break;
    }
    // XAttr.
    if (inode_from_ms.xattrs_size() != inode_from_editlog.xattrs_size()) {
      LOG(ERROR) << "XAttr size is mismatched, inode: " << inode_from_ms.id();
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      if (FLAGS_validate_active_write_batch_mode < 2) {
        mismatch = true;
        break;
      }
    }
    for (auto i = 0;
         i < inode_from_ms.xattrs_size() && i < inode_from_editlog.xattrs_size();
         i++) {
      const auto& lhs = inode_from_ms.xattrs(i);
      const auto& rhs = inode_from_editlog.xattrs(i);
      if (!(lhs.namespace_() == rhs.namespace_() && lhs.name() == rhs.name() &&
            // Protobuf: For bytes, the default value is empty bytes.
            lhs.value() == rhs.value())) {
        LOG(ERROR) << "XAttr is mismatched, inode: " << inode_from_ms.id();
        MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
        if (FLAGS_validate_active_write_batch_mode < 2) {
          mismatch = true;
          break;
        }
      }
    }
    if (mismatch) {
      break;
    }
    // Ignore mtime and atime. They have bug in older version:
    // EditLogSender::LogDelete generates mtime itself.
    if (inode_from_ms.mtime() != inode_from_editlog.mtime()) {
      LOG(ERROR) << "mtime is mismatched, inode: " << inode_from_ms.id();
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      if (FLAGS_validate_active_write_batch_mode < 1) {
        mismatch = true;
        break;
      }
    }
    if (inode_from_ms.atime() != inode_from_editlog.atime()) {
      LOG(ERROR) << "atime is mismatched, inode: " << inode_from_ms.id();
      MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
      if (FLAGS_validate_active_write_batch_mode < 1) {
        mismatch = true;
        break;
      }
    }
  } while (0);

  if (mismatch) {
    LOG(FATAL) << absl::StrFormat(
        "inode mismatched! local: { %s }, editlog: { %s }",
        inode_from_ms.ShortDebugString(),
        inode_from_editlog.ShortDebugString());
  }
}

bool CompareBIPFromMetaStorageAndEditLog(
    const BlockInfoProto& bip_from_ms,
    const BlockInfoProto& bip_from_editlog) {
  // state
  if (bip_from_ms.state() != bip_from_editlog.state()) {
    LOG(ERROR) << "state is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // block_id
  if (bip_from_ms.block_id() != bip_from_editlog.block_id()) {
    LOG(ERROR) << "block_id is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // gen_stamp
  if (bip_from_ms.gen_stamp() != bip_from_editlog.gen_stamp()) {
    LOG(ERROR) << "gen_stamp is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // num_bytes
  if (bip_from_ms.num_bytes() != bip_from_editlog.num_bytes()) {
    LOG(ERROR) << "num_bytes is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // inode_id
  if (bip_from_ms.inode_id() != bip_from_editlog.inode_id()) {
    LOG(ERROR) << "inode_id is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // expected_rep
  if (bip_from_ms.expected_rep() != bip_from_editlog.expected_rep()) {
    LOG(ERROR) << "expected_rep is mismatched, inode: "
               << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // pufs_name
  if (bip_from_ms.pufs_name() != bip_from_editlog.pufs_name()) {
    LOG(ERROR) << "pufs_name is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // pufs_offset
  if (bip_from_ms.pufs_offset() != bip_from_editlog.pufs_offset()) {
    LOG(ERROR) << "pufs_offset is mismatched, inode: "
               << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // part_num
  if (bip_from_ms.part_num() != bip_from_editlog.part_num()) {
    LOG(ERROR) << "part_num is mismatched, inode: " << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // bundle_offset
  if (bip_from_ms.bundle_offset() != bip_from_editlog.bundle_offset()) {
    LOG(ERROR) << "bundle_offset is mismatched, inode: "
               << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  // bundle_length
  if (bip_from_ms.bundle_length() != bip_from_editlog.bundle_length()) {
    LOG(ERROR) << "bundle_length is mismatched, inode: "
               << bip_from_ms.block_id();
    MFC(LoggerMetrics::Instance().compare_inode_failed_)->Inc();
    if (FLAGS_validate_active_write_batch_mode < 0) {
      return false;
    }
  }

  return true;
}

void CompareAncestors(
    const std::vector<INode>& ancestors_from_ms,
    const google::protobuf::RepeatedField<INodeID>& ancestors_from_editlog) {
  CHECK_EQ(ancestors_from_ms.size(), ancestors_from_editlog.size())
      << absl::StrFormat(
          "ancestors_from_ms size is %lu, ancestors_from_editlog size is %lu",
          ancestors_from_ms.size(), ancestors_from_editlog.size());
  for (size_t i = 0; i < ancestors_from_ms.size(); i++) {
    CHECK_EQ(ancestors_from_ms[i].id(), ancestors_from_editlog.Get(i));
  }
}

void CompareSnapshotLog(const SnapshotLog& snaplog_from_ms,
                        const SnapshotLog& snaplog_from_editlog) {
  CHECK_EQ(snaplog_from_ms.IsInitialized(),
           snaplog_from_editlog.IsInitialized())
      << absl::StrFormat(
          "snaplog_from_ms: %s, snaplog_from_editlog: %s",
          snaplog_from_ms.ShortDebugString(),
          snaplog_from_editlog.ShortDebugString());
  if (!snaplog_from_ms.IsInitialized()) {
    return;
  }
  CompareINodeFromMetaStorageAndEditLog(snaplog_from_ms.inode(),
                                        snaplog_from_editlog.inode());
  CHECK_EQ(snaplog_from_ms.snapshot_root_id(),
           snaplog_from_editlog.snapshot_root_id())
      << absl::StrFormat(
          "snapshot_root_id from ms: %lu, snapshot_root_id from editlog: %lu",
          snaplog_from_ms.snapshot_root_id(),
          snaplog_from_editlog.snapshot_root_id());
}

}  // namespace

#define DECODE_CFSOP_PROTO(ctx, op_type, proto_type)                           \
  auto op = std::static_pointer_cast<op_type>(ctx->op);                        \
  proto_type& proto = op->GetProto();                                          \
  if (VLOG_IS_ON(10)) {                                                        \
    LOG(INFO) << absl::StrFormat(                                              \
        "now apply txid %lu op %s editlog PB %s",                              \
        op->txid(), #op_type, proto.ShortDebugString());                       \
  }

std::vector<std::string> NameSpace::GetSortedLockPathsFromPB(
    ApplyContext* apply_ctx) {
  std::vector<std::string> sorted_paths;

  CfsOpCode code =
      std::dynamic_pointer_cast<AbstractEditLogCfsOp>(apply_ctx->op)
          ->GetCfsOpCode();
  switch (code) {
    case CfsOpCode::kBatchCreateFile: {
      const auto op =
          std::static_pointer_cast<OpBatchCreateFile>(apply_ctx->op);
      const BatchInodeToCreate& proto = op->GetProto();
      for (const auto& file : proto.files()) {
        sorted_paths.push_back(file.path());
      }
    } break;
    case CfsOpCode::kBatchCompleteFile: {
      const auto op =
          std::static_pointer_cast<OpBatchCompleteFile>(apply_ctx->op);
      const BatchInodeToComplete& proto = op->GetProto();
      for (const auto& path : proto.paths()) {
        sorted_paths.push_back(path);
      }
    } break;
    case CfsOpCode::kBatchDeleteFile: {
      const auto op =
          std::static_pointer_cast<OpBatchDeleteFile>(apply_ctx->op);
      const BatchInodeToDelete& proto = op->GetProto();
      for (const auto& file : proto.files()) {
        sorted_paths.push_back(file.path());
      }
    } break;
    default:
      LOG(FATAL) << "GetSortedLockPathsFromPB, unrecognized edit log op_code: "
                 << static_cast<int>(code);
  }

  sort(sorted_paths.begin(), sorted_paths.end());
  for (int i = 0; i < sorted_paths.size(); ++i) {
    if (i != 0) {
      CHECK(sorted_paths[i] != sorted_paths[i - 1]) << absl::StrFormat(
          "The path appears multiple times, path=%s", sorted_paths[i]);
    }
  }
  return sorted_paths;
}
void NameSpace::LogCfsOp(std::shared_ptr<ApplyContext> apply_ctx) {
  if (!FLAGS_log_edit_log_detail) {
    return;
  }

  auto op = apply_ctx->op;
  auto txid = op->txid();

  auto cfs_op = std::dynamic_pointer_cast<AbstractEditLogCfsOp>(apply_ctx->op);

  LOG(INFO) << cfs_op->ToString();
}

void NameSpace::ApplyOpAdd(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAdd>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path()
          << ", inode id: " << op->inodeId()
           << ", blocks num: " << op->blocks().size()
           << ", clientHolder: " << op->clientName()
           << ", clientMachine:" << op->clientMachine();

  INode old_file, parent_inode;
  std::vector<INode> ancestors;

  METRIC_WATCH_START(op_add_get_last_inode_time_)
  StatusCode sc = GetLastINodeInPath(ctx->src_path_components, &old_file,
      &ancestors, &parent_inode);
  METRIC_WATCH_STOP(op_add_get_last_inode_time_)

  // OpAdd may be produced by 2 kinds of ClientNamenode RPC:
  // 1. CreateFile
  // 2. Append
  // below we handle them separately for better readability.
  if (sc == StatusCode::kOK && !op->overwrite()) {
    // OpAdd produced by Append
    ApplyOpAddFromAppendInternal(ctx);
  } else {
    // OpAdd produced by Create
    ApplyOpAddFromCreateInternal(ctx);
  }
}

void NameSpace::ApplyOpAddFromAppendInternal(
    std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAdd>(ctx->op);

  INodeInPath iip;
  INode old_file;
  const INode& new_file = iip.Inode();
  LocatedBlockProto located_block;
  std::vector<INode> ancestors;

  DECLARE_STAT_RECORDER(meta_storage_, ctx);

  // 1. get inodes and check
  {
    METRIC_WATCH_START(op_add_get_last_inode_time_)
    StatusCode sc =
        GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors);
    METRIC_WATCH_STOP(op_add_get_last_inode_time_)
    CHECK_EQ(sc, StatusCode::kOK)
        << "Append should specify existing file " << op->path();
    CHECK(!op->overwrite());

    old_file.CopyFrom(iip.Inode());
    if (new_file.status() != INode_Status_kFileUnderConstruction) {
      // append on an already-closed file.
      VLOG(9) << "Reopening an already-closed file " << op->path()
              << " for append";
      UserGroupInfo default_ugi = UserGroupInfo();
      PrepareFileForWriteInternal(op->path(),
                                  op->clientName(),
                                  op->clientMachine(),
                                  false,
                                  iip.MutableInode(),
                                  default_ugi,
                                  &located_block);
    }
    // update the block list for a new file or an updated file
    iip.SetAccessTime(op->atime());
    iip.SetModificationTime(op->mtime());
    UpdateBlocks(op->path(),
                 &iip.MutableInode(),
                 op->blocks(),
                 /*should_complete_last_block=*/false,
                 ctx,
                 nullptr,
                 true);
  }

  // 2. update related cache
  {
    auto resp = std::make_shared<cloudfs::AppendResponseProto>();
    resp->mutable_block()->Swap(&located_block);
    AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  }

  // 3. commit txn
  {
    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);
    STAT_RECORDER_INODE_UPDATE(ctx, old_file, new_file, ancestors);
    meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                      &old_file,
                                      inode_snaplog,
                                      op->txid(),
                                      ctx->done,
                                      STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->path()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpAddFromCreateInternal(
    std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAdd>(ctx->op);

  uint64_t pid;
  INodeInPath old_file_iip, parent_iip;
  const INode& old_file = old_file_iip.Inode();
  INode new_file;
  std::vector<INode> ancestors;
  std::vector<INode*> inodes_add;
  std::vector<INodeAndSnapshot> inodes_del, parents;
  std::vector<INode> inodes_old;
  SnapshotLog old_inode_snaplog, parent_snaplog;
  bool old_deleted = false;

  DECLARE_STAT_RECORDER(meta_storage_, ctx);

  // 1. get inodes and check
  {
    METRIC_WATCH_START(op_add_get_last_inode_time_)
    StatusCode sc = GetLastINodeInPath(
        ctx->src_path_components, &old_file_iip, &ancestors, &parent_iip);
    METRIC_WATCH_STOP(op_add_get_last_inode_time_)

    if (sc == StatusCode::kOK) {
      // target exist
      inodes_old.resize(inodes_old.size() + 1);
      inodes_old.back() = old_file_iip.OldInode();
      CHECK_EQ(old_file.type(), INode_Type_kFile);
      CHECK_NE(old_file.id(), kRootINodeId);
      if (op->overwrite()) {
        // cleanup old inode before construct new inode
        old_deleted = true;

        // no need to consider move-to-recycle-bin case here,
        // which will generate two editlogs before:
        // 1. OpSetXAttrV1
        // 2. OpRenameOldV1
        // that must applied before the current one.

        pid = old_file.parent_id();
        old_file_iip.GenerateSnapshotLog(&old_inode_snaplog);
        inodes_del.emplace_back(&old_file_iip.MutableInode(), &old_inode_snaplog);
        STAT_RECORDER_INODE_DELETE(ctx, old_file, ancestors);
        // OUT 1: overwrite old file
      } else {
        // this is Append case, not handled here.
        LOG(FATAL) << "OpAdd produced by Append request is handled elsewhere.";
        return;
      }
    } else if (sc == kFileNotFound) {
      // target not exist

      pid = parent_iip.Inode().id();
      // OUT 2: new file
    } else {
      // unexpected failure
      LOG(FATAL) << "unexpected error during applying OpAdd: " << sc;
      return;
    }

    CHECK_NE(pid, kInvalidINodeId);
    MakeINodeFile(op->inodeId(),
                  pid,
                  ctx->src_path_components.back().as_string(),
                  op->permissions(),
                  op->replication(),
                  op->blockSize(),
                  op->storagePolicyId(),
                  &new_file);
    new_file.clear_acls();
    for (const auto &acl : op->aclEntries()) {
      new_file.mutable_acls()->Add()->CopyFrom(acl);
    }
    new_file.clear_xattrs();
    if (op->xAttrs().xattrs_size() > 0) {
      auto create_attrs = op->xAttrs().xattrs();
      for (auto& create_attr: create_attrs) {
        new_file.mutable_xattrs()->Add()->CopyFrom(create_attr);
      }
    }
    new_file.set_status(INode_Status_kFileUnderConstruction);
    new_file.mutable_uc()->set_client_name(op->clientName());
    new_file.mutable_uc()->set_client_machine(op->clientMachine());
    new_file.set_atime(op->atime());
    new_file.set_mtime(op->mtime());
    parent_iip.SetModificationTime(op->mtime());

    UpdateLastINodeId(op->inodeId());
    AddLease(new_file);

    inodes_add.emplace_back(&new_file);
    parent_iip.GenerateSnapshotLog(&parent_snaplog);
    parents.emplace_back(&parent_iip.MutableInode(), &parent_snaplog);
  }

  // 2. update related cache
  {
    auto resp = std::make_shared<cloudfs::CreateResponseProto>();
    cloudfs::HdfsFileStatusProto* file_status = resp->mutable_fs();
    UserGroupInfo default_ugi = UserGroupInfo();
    std::vector<AccessMode> modes{AccessMode::READ};
    ConstructFileStatus(op->path(),
                        new_file,
                        false,
                        false,
                        NetworkLocationInfo(),
                        default_ugi,
                        modes,
                        file_status);
    AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  }

  // 3. commit txn
  {
    STAT_RECORDER_INODE_ADD(ctx, new_file, ancestors);

    ctx->done->set_callback([this, old_deleted, old_file](const Status& s) {
      CHECK(s.IsOK());
      if (old_deleted && old_file.has_uc()) {
          // ApplyOpAddFromCreateInternal replays the edit log generated by
          // LogOpenFile rather than LogOpenFileV2, causing it to always
          // overwrite the old inode instead of moving it to the recycle bin.
          // Consequently, we should also clean the lease of the old file.
        DeleteINodeCallback(old_file);
      }
    });
    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(&inodes_add,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->path()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpOpenFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpOpenFile, FileToBeOpen);

  INode old_file, parent_inode;
  std::vector<INode> ancestors;

  METRIC_WATCH_START(op_add_get_last_inode_time_)
  StatusCode sc = GetLastINodeInPath(ctx->src_path_components, &old_file,
      &ancestors, &parent_inode);
  METRIC_WATCH_STOP(op_add_get_last_inode_time_)

  // OpAdd may be produced by 2 kinds of ClientNamenode RPC:
  // 1. CreateFile
  // 2. Append
  // below we handle them separately for better readability.
  if (sc == StatusCode::kOK && !proto.overwrite()) {
    // OpAdd produced by Append
    ApplyOpOpenFileFromAppendInternal(ctx);
  } else {
    // OpAdd produced by Create
    ApplyOpOpenFileFromCreateInternal(ctx);
  }
}

void NameSpace::ApplyOpOpenFileFromAppendInternal(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpOpenFile, FileToBeOpen);

  // convert OpOpenFile to OpAppend
  CHECK(!proto.overwrite());
  CHECK(!proto.move_to_recycle_bin());
  CHECK(!proto.has_rb_path());
  CHECK(!proto.has_rb_inode());
  CHECK(!proto.has_rb_parent());
  FileToBeAppend proto_append;
  proto.set_physical_applyable(false);
  proto_append.set_path(proto.path());
  proto_append.mutable_inode()->CopyFrom(proto.inode());
  proto_append.mutable_parent()->CopyFrom(proto.parent());
  // XXX
  // It's a required field but old version OpenFile doesn't contain.
  // Fortunately, during logical-apply procedure below, it is only accessed
  // when double-checking physical log. After all, these compatible logic is
  // only used one-time during this version upgrade.
  proto_append.clear_old_inode();
  CHECK(!ctx->logical_apply_check_physical_log);
  if (proto.ancestors_id_size() > 0) {
    proto_append.mutable_ancestors_id()->CopyFrom(proto.ancestors_id());
  }
  if (proto.has_old_inode_snaplog()) {
    proto_append.mutable_inode_snaplog()->CopyFrom(proto.old_inode_snaplog());
  }
  if (proto.has_log_rpc_info()) {
    proto_append.mutable_log_rpc_info()->CopyFrom(proto.log_rpc_info());
  }
  std::shared_ptr<OpAppend> op_append = std::make_shared<OpAppend>();
  op_append->SetTxid(ctx->op->txid());
  op_append->SetProto(proto_append);
  ApplyOpAppendImpl(op_append, ctx);
}

void NameSpace::ApplyOpAppendImpl(std::shared_ptr<OpAppend> op,
                                  std::shared_ptr<ApplyContext> ctx) {
  FileToBeAppend& proto = op->GetProto();

  std::string rpc_clientid;
  uint32_t rpc_callid;
  INodeInPath iip;
  INode old_file;
  const INode& new_file = iip.Inode();
  LocatedBlockProto located_block;
  std::vector<INode> ancestors;

  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  // 1. get inodes and check
  {
    METRIC_WATCH_START(op_add_get_last_inode_time_)
    StatusCode sc =
        GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors);
    METRIC_WATCH_STOP(op_add_get_last_inode_time_)
    CHECK_EQ(sc, StatusCode::kOK)
        << "Append should specify existing file " << proto.path();

    old_file.CopyFrom(iip.Inode());
    if (new_file.status() != INode_Status_kFileUnderConstruction) {
      // append on an already-closed file.
      VLOG(9) << "Reopening an already-closed file " << proto.path()
              << " for append";
      UserGroupInfo default_ugi = UserGroupInfo();
      PrepareFileForWriteInternal(proto.path(),
                                  proto.inode().uc().client_name(),
                                  proto.inode().uc().client_machine(),
                                  false,
                                  iip.MutableInode(),
                                  default_ugi,
                                  &located_block);
    }
    // update the block list for a new file or an updated file
    iip.SetAccessTime(proto.inode().atime());
    iip.SetModificationTime(proto.inode().mtime());
    std::vector<cloudfs::BlockProto> blks;
    for (const auto& blk : new_file.blocks()) {
      blks.push_back(blk);
    }
    UpdateBlocks(proto.path(),
                 &iip.MutableInode(),
                 blks,
                 /*should_complete_last_block=*/false,
                 ctx,
                 nullptr,
                 true);

    if (NameSpace::IsAccMode() && ctx->acc_open_or_mkdir_inode) {
      CheckAccINode(*ctx->acc_open_or_mkdir_inode);
      CHECK(new_file.id() == ctx->acc_open_or_mkdir_inode->id());
      iip.MutableInode().mutable_ufs_file_info()->CopyFrom(
          ctx->acc_open_or_mkdir_inode->ufs_file_info());
    }
  }

  // 2. update related cache
  {
    auto resp = std::make_shared<cloudfs::AppendResponseProto>();
    resp->mutable_block()->Swap(&located_block);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
  }

  // 3. commit txn
  {
    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);

    if (ctx->logical_apply_check_physical_log) {
      if (IsHdfsMode()) {
        CHECK_GT(proto.ancestors_id_size(), 0);
        CompareAncestors(ancestors, proto.ancestors_id());
      }
      CompareINodeFromMetaStorageAndEditLog(new_file, proto.inode());
      CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
      CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
    }

    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_UPDATE(ctx, old_file, new_file, ancestors);

    // Cannot use ctx->done->add_post_callback because we want this logic to be
    // executed before any callbacks are triggered
    Closure* done_cb = NewCallback([this, ctx, new_file, op](const Status& s) {
      // Add task to writeback manager for ACC mode
      if (s.IsOK()) {
        if (NameSpace::IsAccMode() && !FLAGS_enable_write_back_task_persistence) {
          CheckAccINode(new_file);
          ufs_env_->upload_monitor()->AddTask(
              new_file.id(),
              new_file.ufs_file_info().key(),
              new_file.ufs_file_info().upload_id());
        }
      }
      auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                              ctx->sw->GetTime())
                              .count();
      if (time_cost_us > FLAGS_edit_log_slow_op_us) {
        VLOG(6) << "Detect edit log apply slow "
                << ", op_name: " << op->op_name() << ", path: " << op->GetProto().path()
                << ", apply op cost us: " << time_cost_us;
      }

      // Trigger original callback
      if (s.code() != ctx->done->status().code()) {
        ctx->done->set_status(Status(s));
      }
      ctx->done->Run();
    });

    meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                      proto.has_old_inode()
                                          ? &proto.old_inode()
                                          : nullptr,
                                      inode_snaplog,
                                      op->txid(),
                                      done_cb,
                                      STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyOpOpenFileFromCreateInternal(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpOpenFile, FileToBeOpen);

  std::string rpc_clientid;
  uint32_t rpc_callid;
  INodeInPath old_file_iip, parent_iip;
  const INode& old_file = old_file_iip.Inode();
  INode new_file;
  std::vector<INode> ancestors, rb_ancestors;
  INodeInPath rb_iip, rb_parent_iip;
  const INode& rb_inode = rb_iip.Inode();
  const INode& rb_parent = rb_parent_iip.Inode();
  std::vector<BlockInfoProto> add_block_bips;
  std::vector<std::vector<DatanodeID>> bips_expected_locs;
  std::vector<Block> future_blks;
  std::vector<INode*> inodes_add, inodes_mov_dst;
  std::vector<INode> inodes_old;
  std::vector<INodeAndSnapshot> inodes_mov_src, inodes_del, parents;
  SnapshotLog old_inode_snaplog, parent_snaplog, rb_parent_snaplog;
  bool old_deleted = false;

  DECLARE_STAT_RECORDER(meta_storage_, ctx);

  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  // 1. get inodes and check
  {
    METRIC_WATCH_START(op_add_get_last_inode_time_)
    StatusCode sc = GetLastINodeInPath(
        ctx->src_path_components, &old_file_iip, &ancestors, &parent_iip);
    METRIC_WATCH_STOP(op_add_get_last_inode_time_)

    if (sc == StatusCode::kOK) {
      // target exist
      CHECK_EQ(old_file.type(), INode_Type_kFile);
      CHECK_NE(old_file.id(), kRootINodeId);
      inodes_old.resize(inodes_old.size() + 1);
      inodes_old.back() = old_file_iip.OldInode();

      if (proto.overwrite()) {
        // cleanup old inode before construct new inode
        if (proto.has_move_to_recycle_bin() && proto.move_to_recycle_bin()) {
          // move old inode into recycle bin
          CHECK(proto.has_rb_path());
          CHECK(proto.has_rb_inode());
          CHECK(proto.has_rb_parent());

          auto s = MoveToRecycleBinInternal(proto.path(),
                                            proto.rb_path(),
                                            ctx->src_path_components,
                                            ctx->rb_path_components,
                                            &rb_iip,
                                            &rb_parent_iip,
                                            &rb_ancestors);
          CHECK(s.code() == Code::kOK);
          CHECK(!s.HasException());

          // OUT 1: target inode exists, move it to recycle bin
          CHECK_EQ(old_file.id(), rb_iip.Inode().id());
          CHECK_EQ(old_file.id(), proto.rb_inode().id());
          CHECK_EQ(rb_parent.id(), proto.rb_parent().id());
          old_file_iip.RecordDeletion();
          rb_iip.MutableInodeUnsafe().CopyFrom(proto.rb_inode());
          rb_parent_iip.MutableInodeUnsafe().CopyFrom(proto.rb_parent());
          old_file_iip.GenerateSnapshotLog(&old_inode_snaplog);
          rb_parent_iip.GenerateSnapshotLog(&rb_parent_snaplog);
          inodes_mov_src.emplace_back(&old_file_iip.MutableInode(),
                                      &old_inode_snaplog);
          inodes_mov_dst.emplace_back(&rb_iip.MutableInode());
          parents.emplace_back(&rb_parent_iip.MutableInode(),
                                               &rb_parent_snaplog);
          STAT_RECORDER_INODE_RENAME(
              ctx, old_file, rb_iip.Inode(), ancestors, rb_ancestors);
        } else {
          // delete old inode directly
          old_deleted = true;
          CHECK(!proto.has_rb_path());
          CHECK(!proto.has_rb_inode());
          CHECK(!proto.has_rb_parent());

          // OUT 2: target inode exists, remove it.
          old_file_iip.RecordDeletion();
          old_file_iip.GenerateSnapshotLog(&old_inode_snaplog);
          inodes_del.emplace_back(&old_file_iip.MutableInode(),
                                  &old_inode_snaplog);
          STAT_RECORDER_INODE_DELETE(ctx, old_file, ancestors);
        }
      } else {
        // this is Append case, not handled here.
        LOG(FATAL) << "OpAdd produced by Append request is handled elsewhere.";
        return;
      }
    } else if (sc == kFileNotFound) {
      // target not exist

      // OUT 3: target inode not exists.
    } else {
      // unexpected failure
      LOG(FATAL) << "unexpected error during applying OpAdd: " << sc;
      return;
    }

    if (proto.has_parent()) {
      CHECK_EQ(parent_iip.Inode().id(), proto.parent().id());
      parent_iip.MutableInode().CopyFrom(proto.parent());
    }
    new_file.CopyFrom(proto.inode());

    UpdateLastINodeId(new_file.id());
    AddLease(new_file);

    // feature: add block
    if (proto.add_block_bips_with_locs_size() == 0) {
      for (const auto& add_bip : proto.add_block_bips()) {
        add_block_bips.emplace_back(add_bip);
        bips_expected_locs.emplace_back(std::vector<DatanodeID>());
      }
    } else {
      for (const auto& add_bip_with_locs : proto.add_block_bips_with_locs()) {
        add_block_bips.emplace_back(add_bip_with_locs.bip());
        std::vector<DatanodeID> dn_ids;
        for (const auto& dn_uuid : add_bip_with_locs.dns()) {
          auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
          if (dn == nullptr) {
            LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
            continue;
          }
          dn_ids.emplace_back(dn->id());
        }
        bips_expected_locs.emplace_back(dn_ids);
      }
    }

    CHECK_EQ(add_block_bips.size(), bips_expected_locs.size());
    for (int i = 0; i < add_block_bips.size(); i++) {
      const auto& bip = add_block_bips[i];
      const auto& locs = bips_expected_locs[i];

      // Do not update block id and gs in callback
      // refer to inode id above
      UpdateLastAllocatedBlockId(bip.block_id());
      UpdateGenerationStampV2(bip.gen_stamp());

      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};
      block_manager_->LoadBlock(new_file.id(),
                                new_file.parent_id(),
                                static_cast<uint8_t>(new_file.replication()),
                                bip.write_mode(),
                                blk,
                                locs,
                                BlockUCState::kUnderConstruction);

      future_blks.emplace_back(blk);
    }

#if 0
    std::vector<cloudfs::BlockProto> blks;
    for (const auto& blk : new_file.blocks()) {
      blks.push_back(blk);
    }
    UpdateBlocks(proto.path(),
                 &new_file,
                 blks,
                 /*should_complete_last_block=*/false,
                 ctx,
                 nullptr,
                 true);
#endif
    UpdateINodeAttrs(new_file, parent_iip.Inode(), true);

    if (NameSpace::IsAccMode() && ctx->acc_open_or_mkdir_inode) {
      CheckAccINode(*ctx->acc_open_or_mkdir_inode);
      CHECK(new_file.id() == ctx->acc_open_or_mkdir_inode->id());
      new_file.mutable_ufs_file_info()->CopyFrom(
          ctx->acc_open_or_mkdir_inode->ufs_file_info());
    }

    parent_iip.GenerateSnapshotLog(&parent_snaplog);
    inodes_add.emplace_back(&new_file);
    parents.emplace_back(&parent_iip.MutableInode(), &parent_snaplog);
  }

  // 2. update related cache
  {
    auto resp = std::make_shared<cloudfs::CreateResponseProto>();
    cloudfs::HdfsFileStatusProto* file_status = resp->mutable_fs();
    UserGroupInfo default_ugi = UserGroupInfo();
    std::vector<AccessMode> modes{AccessMode::READ};
    ConstructFileStatus(proto.path(),
                        new_file,
                        false,
                        false,
                        NetworkLocationInfo(),
                        default_ugi,
                        modes,
                        file_status);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
  }

  // 3. commit txn
  {
    STAT_RECORDER_INODE_ADD(ctx, new_file, ancestors);

    if (ctx->logical_apply_check_physical_log) {
      // XXX skip check parent-snaplog: M-4724035558
      CHECK_EQ(proto.inode().parent_id(), proto.parent().id());
      if (proto.has_overwrite_inode()) {
        CHECK(proto.overwrite());
        if (proto.move_to_recycle_bin()) {
          CHECK(proto.has_rb_path());
          CHECK(proto.has_rb_inode());
          CHECK(proto.has_rb_parent());
          CHECK_EQ(proto.rb_inode().id(), proto.overwrite_inode().id());
          CHECK_EQ(proto.rb_inode().parent_id(), proto.rb_parent().id());
          CHECK_GT(proto.rb_ancestors_id_size(), 0);
          CompareINodeFromMetaStorageAndEditLog(rb_inode, proto.rb_inode());
          //CompareINodeFromMetaStorageAndEditLog(rb_parent, proto.rb_parent());
          CompareAncestors(rb_ancestors, proto.rb_ancestors_id());
          //CompareSnapshotLog(rb_parent_snaplog, proto.rb_parent_snaplog());
        } else {
          CHECK(!proto.has_rb_path());
          CHECK(!proto.has_rb_inode());
          CHECK(!proto.has_rb_parent());
          CHECK_EQ(proto.rb_ancestors_id_size(), 0);
          CHECK(!proto.has_rb_parent_snaplog());
          CompareINodeFromMetaStorageAndEditLog(old_file, proto.overwrite_inode());
        }
        CompareSnapshotLog(old_inode_snaplog, proto.old_inode_snaplog());
        //CompareSnapshotLog(parent_snaplog, proto.parent_snaplog());
      } else {
        CHECK(!proto.move_to_recycle_bin());
        CHECK(!proto.has_rb_path());
        CHECK(!proto.has_rb_inode());
        CHECK(!proto.has_rb_parent());
        CHECK_EQ(proto.rb_ancestors_id_size(), 0);
      }
      if (IsHdfsMode()) {
        CHECK_GT(proto.ancestors_id_size(), 0);
        CompareAncestors(ancestors, proto.ancestors_id());
      }
    }

    ctx->done->set_callback([=](const Status& s) {
      CHECK(s.IsOK());
      {
        // if (!old_blocks.empty()) {
        //   block_manager_->RemoveBlocksAndUpdateSafeMode(old_blocks);
        // }
        // https://code.byted.org/inf/dancenn/blob/127a49166a3e4f718623ba1f2582002be9acdb52/src/namespace/editlog_apply.cc#L74
        // https://code.byted.org/inf/dancenn/blob/127a49166a3e4f718623ba1f2582002be9acdb52/src/namespace/namespace.cc#L2535
        DeleteINodeCallback(old_file);
      }

      StopWatch sw;
      sw.Start();
      auto current_block_id = last_allocated_block_id();
      auto current_gsv2 = generation_stamp_v2();
      for (auto& b : future_blks) {
        block_manager_->ProcessPendingFutureBlks(
            b.id, current_block_id, current_gsv2);
        block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
      }
      auto time_cost_us = sw.NextStepTime();
      if (time_cost_us > FLAGS_edit_log_slow_op_us) {
        VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
                << " slow, path: " << proto.path()
                << ", process cost us: " << time_cost_us;
      }
    });

    // Cannot use ctx->done->add_post_callback because we want this logic to be
    // executed before any callbacks are triggered
    Closure* done_cb = NewCallback([this, ctx, new_file, op](const Status& s) {
      // Add task to writeback manager for ACC mode
      if (s.IsOK()) {
        if (NameSpace::IsAccMode() && !FLAGS_enable_write_back_task_persistence) {
          CheckAccINode(new_file);
          ufs_env_->upload_monitor()->AddTask(
              new_file.id(),
              new_file.ufs_file_info().key(),
              new_file.ufs_file_info().upload_id());
        }
      }
      auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                              ctx->sw->GetTime())
                              .count();
      if (time_cost_us > FLAGS_edit_log_slow_op_us) {
        VLOG(6) << "Detect edit log apply slow "
                << ", op_name: " << op->op_name() << ", path: " << op->GetProto().path()
                << ", apply op cost us: " << time_cost_us;
      }

      // Trigger original callback
      if (s.code() != ctx->done->status().code()) {
        ctx->done->set_status(Status(s));
      }
      ctx->done->Run();
    });

    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(&inodes_add,
                                       nullptr,
                                       &inodes_mov_src,
                                       &inodes_mov_dst,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       add_block_bips,
                                       {},
                                       op->txid(),
                                       {done_cb},
                                       STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyOpAppend(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAppend>(ctx->op);
  FileToBeAppend& proto = op->GetProto();

  // Previously, RPC 'append' generate editlog in the form of 'FileToBeOpen'
  // for convenience. However, it becomes more and more complicated nowadays.
  // So we decouple it as 'FileToBeAppend'.
  // For backward compatiblity, we differentiate apply-functions for all cases:
  // | PB format      | NN version | apply-function        | logical/physical |
  // | -------------- | ---------- | --------------------- | ---------------- |
  // | FileToBeOpen   | previous   | ApplyOpOpenFile()     | logical          |
  // |                |            |   -> ApplyOpAppendImpl() |               |
  // | FileToBeAppend | current    | ApplyOpAppend()       | logical          |
  // |                |            |   -> ApplyOpAppendImpl() |               |
  // | FileToBeAppend | current    | ApplyCfsOpAppend()    | physical         |

  ApplyOpAppendImpl(op, ctx);
}


void NameSpace::ApplyOpClose(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpClose>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path()
          << ", blocks num: " << op->blocks().size();
  METRIC_WATCH_START(op_close_get_last_inode_time_)
  std::vector<INode> ancestors;
  INode file;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &file, &ancestors),
      StatusCode::kOK) << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_close_get_last_inode_time_)

  INode old_file(file);
  file.set_atime(op->atime());
  file.set_mtime(op->mtime());
  BlockID origin_last_blk_to_del = kInvalidBlockID;
  UpdateBlocks(op->path(),
               &file,
               op->blocks(),
               /*should_complete_last_block=*/true,
               ctx,
               &origin_last_blk_to_del,
               true);

  if (file.status() == INode_Status_kFileUnderConstruction) {
    CHECK(!ctx->done->callback());
    ctx->done->set_callback([this, file, path = op->path()](const Status& s) {
      RemoveLease(file);
    });
    file.set_status(INode_Status_kFileComplete);
    file.clear_uc();
  } else {
    LOG(FATAL) << "File " << op->path() << " is not under construction.";
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file, ancestors);
  meta_storage_->OrderedUpdateINodeAndFinalizeBlocks(
      file, origin_last_blk_to_del, op->txid(), ctx->done, STAT_RECORDER_PTR);
  auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime()).count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
      << ", op_name: " << op->op_name()
      << ", path: " << op->path()
      << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::ApplyOpAddBlock(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAddBlock>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path()
          << ", new block id: " << op->blocks().back().blockid();
  METRIC_WATCH_START(op_add_block_get_last_inode_time_)

  std::vector<INode> ancestors;

  INode file;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &file, &ancestors),
      StatusCode::kOK) << " op=" << op->op_name() << " path=" << op->path();
  CHECK_EQ(file.type(), INode_Type_kFile);
  METRIC_WATCH_STOP(op_add_block_get_last_inode_time_)

  INode old_file(file);

  std::vector<Block> future_blks;
  BlockProto new_block;
  if (op->blocks().size() == 2) {
    CHECK_GE(file.blocks_size(), 1);
    BlockProto p_block = op->blocks().at(0);
    new_block = op->blocks().at(1);
    BlockProto *old_last_block =
        file.mutable_blocks(file.blocks_size() - 1);
    VLOG(9) << op->op_name() << ":" << op->path()
            << ", p_block=" << p_block.ShortDebugString()
               << ", new_block=" << new_block.ShortDebugString()
               << ", old_last_block=" << old_last_block->ShortDebugString();
    if (old_last_block->blockid() != p_block.blockid()
        || old_last_block->genstamp() != p_block.genstamp()) {
      LOG(FATAL)
          << "Mismatched block IDs or generation stamps for "
          << "the old last block of file" << op->path()
          << ", the old last block is " << old_last_block->ShortDebugString()
          << ", and the block from edit log is " << p_block.ShortDebugString();
    }
    old_last_block->set_numbytes(p_block.numbytes());
    block_manager_->UpdateLength(p_block.blockid(), p_block.numbytes());
    if (!block_manager_->BlockHasBeenComplete(old_last_block->blockid())) {
      Block blk{p_block.blockid(),
                static_cast<uint32_t>(p_block.numbytes()),
                p_block.genstamp()};
      block_manager_->CommitOrCompleteOrPersistLastBlock(blk, true);
      future_blks.emplace_back(blk);
    } else {
      VLOG(9) << "Block is complete: blk=" << p_block.ShortDebugString();
    }
  } else if (op->blocks().size() == 1) {
    new_block = op->blocks().at(0);
    CHECK_EQ(file.blocks_size(), 0);
  }

  BlockInfoProto existed_bip;
  bool existed = meta_storage_->GetBlockInfo(new_block.blockid(), &existed_bip);
  // Double check to make sure new block not existed.
  if (existed) {
    LOG(FATAL)
        << "FATAL error in replay OP_ADDBLOCK, new block already existed. B"
        << new_block.blockid();
    return;
  }

  UpdateLastAllocatedBlockId(new_block.blockid());
  UpdateGenerationStampV2(new_block.genstamp());

  Block blk{new_block.blockid(), static_cast<uint32_t>(new_block.numbytes()),
            new_block.genstamp()};
  block_manager_->LoadBlock(file.id(),
                            file.parent_id(),
                            static_cast<uint8_t>(file.replication()),
                            // TODO(ruanjunbin)
                            cloudfs::DATANODE_BLOCK,
                            blk,
                            /*dn_ids*/std::vector<DatanodeID>(),
                            BlockUCState::kUnderConstruction);
  auto blk_proto = file.mutable_blocks()->Add();
  blk_proto->set_blockid(new_block.blockid());
  blk_proto->set_genstamp(new_block.genstamp());
  blk_proto->set_numbytes(new_block.numbytes());
  future_blks.emplace_back(blk);
  auto path = op->path();
  ctx->done->add_post_callback([future_blks, path, this](const Status& s) {
    StopWatch sw;
    sw.Start();
    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto& b : future_blks) {
      block_manager_->ProcessPendingFutureBlks(
          b.id, current_block_id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
    }
    auto time_cost_us = sw.NextStepTime();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
        << " slow, path: " << path
        << ", process cost us: " << time_cost_us;
    }
  });

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file, ancestors);
  meta_storage_->OrderedUpdateINodeAndAddBlock(
      file, op->txid(), ctx->done, STAT_RECORDER_PTR);
  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
      << ", op_name: " << op->op_name()
      << ", path: " << op->path()
      << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::ApplyOpAddBlockV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAddBlockV2, BlockToBeAdd);
  const INode& file_from_editlog = proto.inode();

  // Active NN must promise: msgs from edit log op always match each other.
  if (proto.has_penultimate_bip() || proto.has_penultimate_bip_with_locs()) {
    const BlockInfoProto* penultimate_bip_tbc = nullptr;
    if (proto.has_penultimate_bip_with_locs()) {
      penultimate_bip_tbc = &proto.penultimate_bip_with_locs().bip();
    } else {
      penultimate_bip_tbc = &proto.penultimate_bip();
    }
    CHECK_NOTNULL(penultimate_bip_tbc);
    CHECK_GE(file_from_editlog.blocks_size(), 2);
    const BlockProto& penultimate_bp =
        file_from_editlog.blocks(file_from_editlog.blocks_size() - 2);
    if (penultimate_bip_tbc->block_id() != penultimate_bp.blockid() ||
        penultimate_bip_tbc->gen_stamp() != penultimate_bp.genstamp() ||
        penultimate_bip_tbc->num_bytes() != penultimate_bp.numbytes()) {
      LOG(FATAL) << "editlog msg mismatched, txid= " << op->txid()
                 << " proto=" << ToJsonCompactString(op->GetProto());
    }
  }
  {
    CHECK(proto.has_last_bip() || proto.has_last_bip_with_locs());
    const BlockInfoProto* last_bip_tbuc = nullptr;
    if (proto.has_last_bip_with_locs()) {
      last_bip_tbuc = &proto.last_bip_with_locs().bip();
    } else {
      last_bip_tbuc = &proto.last_bip();
    }
    CHECK_NOTNULL(last_bip_tbuc);
    CHECK_GE(file_from_editlog.blocks_size(), 1);
    const BlockProto& last_bp =
        file_from_editlog.blocks(file_from_editlog.blocks_size() - 1);
    if (last_bip_tbuc->block_id() != last_bp.blockid() ||
        last_bip_tbuc->gen_stamp() != last_bp.genstamp() ||
        last_bip_tbuc->num_bytes() != last_bp.numbytes()) {
      LOG(FATAL) << "edit log msg mismatched, txid= " << op->txid()
                 << " proto=" << ToJsonCompactString(op->GetProto());
    }
  }

  METRIC_WATCH_START(op_add_block_get_last_inode_time_);
  INodeInPath iip;
  const INode& file_from_ms = iip.Inode();
  std::vector<INode> ancestors;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->GetProto().path();
  CHECK_EQ(file_from_ms.type(), INode::kFile);
  METRIC_WATCH_STOP(op_add_block_get_last_inode_time_);
  INode old_file = file_from_ms;

  std::vector<Block> future_blks;
  if (file_from_editlog.blocks_size() > 1) {
    BlockProto p_block =
        file_from_editlog.blocks(file_from_editlog.blocks_size() - 2);
    BlockProto* old_last_block =
        iip.MutableInode().mutable_blocks(file_from_ms.blocks_size() - 1);
    if (old_last_block->blockid() != p_block.blockid() ||
        old_last_block->genstamp() != p_block.genstamp()) {
      LOG(FATAL) << "Mismatched block IDs or generation stamps for "
                 << "the old last block of file " << op->GetProto().path()
                 << ", the old last block is "
                 << old_last_block->ShortDebugString()
                 << ", and the block from edit log is "
                 << p_block.ShortDebugString();
    }
    old_last_block->set_numbytes(p_block.numbytes());
    block_manager_->UpdateLength(p_block.blockid(), p_block.numbytes());
    if (!block_manager_->BlockHasBeenComplete(old_last_block->blockid())) {
      Block blk{p_block.blockid(),
                static_cast<uint32_t>(p_block.numbytes()),
                p_block.genstamp()};
      block_manager_->CommitOrCompleteOrPersistLastBlock(blk, true);
      // TODO(ruanjunbin)
      // auto state = block_manager_->GetBlockUCState(blk.id);
      // if (state == BlockUCState::kPersisted && )
      future_blks.emplace_back(blk);
    } else {
      VLOG(9) << "Block is complete: blk=" << p_block.ShortDebugString();
    }
  }

  const BlockInfoProto* new_bip = nullptr;
  std::vector<DatanodeID> dns;
  if (proto.has_last_bip_with_locs()) {
    new_bip = &proto.last_bip_with_locs().bip();
    for (const auto& dn_uuid : proto.last_bip_with_locs().dns()) {
      auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
      if (!dn) {
        LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
        continue;
      }
      dns.emplace_back(dn->id());
    }
  } else {
    new_bip = &proto.last_bip();
  }
  CHECK_NOTNULL(new_bip);

  UpdateLastAllocatedBlockId(new_bip->block_id());
  UpdateGenerationStampV2(new_bip->gen_stamp());

  Block blk{new_bip->block_id(),
            static_cast<uint32_t>(new_bip->num_bytes()),
            new_bip->gen_stamp()};
  block_manager_->LoadBlock(file_from_ms.id(),
                            file_from_ms.parent_id(),
                            static_cast<uint8_t>(file_from_ms.replication()),
                            new_bip->write_mode(),
                            blk,
                            dns,
                            BlockUCState::kUnderConstruction);
  auto blk_proto = iip.MutableInode().mutable_blocks()->Add();
  blk_proto->set_blockid(new_bip->block_id());
  blk_proto->set_genstamp(new_bip->gen_stamp());
  blk_proto->set_numbytes(new_bip->num_bytes());

  // update mtime
  iip.MutableInode().set_mtime(file_from_editlog.mtime());

  future_blks.emplace_back(blk);
  auto path = op->GetProto().path();
  ctx->done->add_post_callback([future_blks, path, this](const Status& s) {
    StopWatch sw;
    sw.Start();
    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto& b : future_blks) {
      block_manager_->ProcessPendingFutureBlks(
          b.id, current_block_id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
    }
    auto time_cost_us = sw.NextStepTime();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
              << " slow, path: " << path
              << ", process cost us: " << time_cost_us;
    }
  });

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file_from_ms, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  meta_storage_->OrderedAddBlock(&iip.MutableInode(),
                                 op->GetProto().has_penultimate_bip()
                                     ? &op->GetProto().penultimate_bip()
                                     : nullptr,
                                 *new_bip,
                                 &old_file,
                                 inode_snaplog,
                                 op->txid(),
                                 ctx->done,
                                 STAT_RECORDER_PTR);

  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
            << ", op_name: " << op->op_name()
            << ", path: " << op->GetProto().path()
            << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::ApplyOpAbandonBlock(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAbandonBlock, BlockToBeAbandon);
  const INode& file_from_editlog = proto.inode();
  VLOG(9) << op->op_name() << ": block size "
          << file_from_editlog.blocks_size();

  // Active NN must promise: msgs from edit log op always match each other.
  CHECK(proto.has_abandoned_blk_id()) << op->txid();
  for (const auto& block : file_from_editlog.blocks()) {
    CHECK_NE(block.blockid(), proto.abandoned_blk_id()) << op->txid();
  }

  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_);
  INode file_from_ms;
  std::vector<INode> ancestors;
  CHECK_EQ(
      GetLastINodeInPath(ctx->src_path_components, &file_from_ms, &ancestors),
      StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  CHECK_EQ(file_from_ms.type(), INode::kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_);
  INode old_file = file_from_ms;

  BlockID last_blk_to_be_abandoned = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &file_from_ms,
               std::vector<BlockProto>{file_from_editlog.blocks().begin(),
                                       file_from_editlog.blocks().end()},
               /*should_complete_last_block=*/false,
               ctx,
               &last_blk_to_be_abandoned,
               true);
  CHECK_EQ(last_blk_to_be_abandoned, proto.abandoned_blk_id()) << op->txid();

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  auto resp = std::make_shared<AbandonBlockResponseProto>();
  AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file_from_ms, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  meta_storage_->OrderedAbandonBlock(file_from_ms,
                                     last_blk_to_be_abandoned,
                                     &old_file,
                                     op->txid(),
                                     ctx->done,
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpUpdatePipeline(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUpdatePipeline, PipelineToBeUpdate);
  const INode& file_from_editlog = proto.inode();
  VLOG(9) << op->op_name() << ": block size "
          << file_from_editlog.blocks_size();

  // Active NN must promise: msgs from edit log op always match each other.
  CHECK(proto.has_last_bip_tbuc()) << op->txid();
  CHECK_GT(file_from_editlog.blocks_size(), 0) << op->txid();
  CHECK_EQ(
      proto.last_bip_tbuc().block_id(),
      file_from_editlog.blocks(file_from_editlog.blocks_size() - 1).blockid())
      << op->txid();

  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_);
  INode file_from_ms;
  std::vector<INode> ancestors;
  CHECK_EQ(
      GetLastINodeInPath(ctx->src_path_components, &file_from_ms, &ancestors),
      StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  CHECK_EQ(file_from_ms.type(), INode::kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_);

  INode old_file(file_from_ms);
  BlockID last_blk_to_be_abandoned = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &file_from_ms,
               std::vector<BlockProto>{file_from_editlog.blocks().begin(),
                                       file_from_editlog.blocks().end()},
               /*should_complete_last_block=*/false,
               ctx,
               &last_blk_to_be_abandoned,
               true);
  CHECK_EQ(last_blk_to_be_abandoned, kInvalidBlockID);

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  auto resp = std::make_shared<UpdatePipelineResponseProto>();
  AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file_from_ms, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  meta_storage_->OrderedUpdatePipeline(file_from_ms,
                                       proto.last_bip_tbuc(),
                                       &old_file,
                                       op->txid(),
                                       ctx->done,
                                       STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpFsync(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpFsync, FileToBeSync);
  const INode& file_from_editlog = proto.inode();
  VLOG(9) << op->op_name() << ": block size "
          << file_from_editlog.blocks_size();

  // Active NN must promise: msgs from edit log op always match each other.
  if (proto.has_last_bip_tbuc()) {
    CHECK_GT(file_from_editlog.blocks_size(), 0) << op->txid();
    CHECK_EQ(
        proto.last_bip_tbuc().block_id(),
        file_from_editlog.blocks(file_from_editlog.blocks_size() - 1).blockid())
        << op->txid();
  }

  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_);
  INodeInPath iip;
  const INode& file_from_ms = iip.Inode();
  std::vector<INode> ancestors;
  CHECK_EQ(
      GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors),
      StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  CHECK_EQ(file_from_ms.type(), INode::kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_);
  INode old_file(file_from_ms);

  BlockID last_blk_to_be_abandoned = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &iip.MutableInode(),
               std::vector<BlockProto>{proto.inode().blocks().begin(),
                                       proto.inode().blocks().end()},
               /*should_complete_last_block=*/false,
               ctx,
               &last_blk_to_be_abandoned,
               true);
  // for commitLastBlock
  bool commit_last_block = false;
  if (proto.has_last_bip_tbuc() &&
      proto.last_bip_tbuc().state() == BlockInfoProto::kCommitted) {
    commit_last_block = true;
    Block b(file_from_editlog.blocks(file_from_editlog.blocks_size() - 1));
    UpdateLastAllocatedBlockId(b.id);
    UpdateGenerationStampV2(b.gs);
    if (!block_manager_->BlockHasBeenCommitted(
            proto.last_bip_tbuc().block_id())) {
      block_manager_->CommitOrCompleteOrPersistLastBlock(b,
                                                         /*force=*/false);
    }
  }
  CHECK_EQ(last_blk_to_be_abandoned, kInvalidBlockID) << op->txid();

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  auto resp = std::make_shared<FsyncResponseProto>();
  AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file_from_ms, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  if (commit_last_block) {
    meta_storage_->OrderedCommitLastBlock(&iip.MutableInode(),
                                          proto.last_bip_tbuc(),
                                          &old_file,
                                          inode_snaplog,
                                          op->txid(),
                                          ctx->done,
                                          STAT_RECORDER_PTR);
  } else {
    meta_storage_->OrderedFsync(
        &iip.MutableInode(),
        proto.has_last_bip_tbuc() ? &proto.last_bip_tbuc() : nullptr,
        &old_file,
        inode_snaplog,
        op->txid(),
        ctx->done,
        STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyOpCommitBlockSynchronization(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(
      ctx, OpCommitBlockSynchronization, BlockToBeCommitSynchronization);

  CHECK(!proto.delete_block());
  CHECK(!proto.close_file());
  CHECK(proto.has_last_bip());
  CHECK_GT(proto.inode().blocks_size(), 0);
  CHECK_EQ(proto.last_bip().block_id(),
           proto.inode().blocks(proto.inode().blocks_size() - 1).blockid());

  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_);
  INodeInPath iip;
  const INode& file_from_ms = iip.Inode();
  std::vector<INode> ancestors;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  CHECK_EQ(file_from_ms.type(), INode::kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_);
  INode old_file(file_from_ms);

  BlockID last_blk_to_del = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &iip.MutableInode(),
               std::vector<BlockProto>{proto.inode().blocks().begin(),
                                       proto.inode().blocks().end()},
               /*should_complete_last_block=*/true,
               ctx,
               &last_blk_to_del,
               true);
  CHECK_EQ(last_blk_to_del, kInvalidBlockID);
  iip.MutableInode().set_mtime(proto.inode().mtime());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file_from_ms, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  std::vector<INodeAndSnapshot> inodes_mod{
      INodeAndSnapshot(proto.mutable_inode(), proto.mutable_inode_snaplog())};
  std::vector<INode> inodes_old{proto.old_inode()};
  meta_storage_->OrderedCommitINodes(nullptr,
                                     &inodes_mod,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &inodes_old,
                                     {},
                                     {proto.last_bip()},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpCloseFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpCloseFile, FileToBeClose);
  const INode& file_from_editlog = proto.inode();
  VLOG(9) << op->op_name() << ": " << proto.path()
          << ", blocks num: " << proto.inode().blocks_size();

  METRIC_WATCH_START(op_close_get_last_inode_time_);
  std::vector<INode> ancestors;
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  METRIC_WATCH_STOP(op_close_get_last_inode_time_);
  INode& file_from_ms = iip.MutableInode();
  INode old_file(file_from_ms);
  file_from_ms.set_atime(file_from_editlog.atime());
  file_from_ms.set_mtime(file_from_editlog.mtime());
  if (IsAccMode()) {
    if (file_from_ms.has_ufs_file_info()) {
      file_from_ms.mutable_ufs_file_info()->set_complete_mtime(
          file_from_editlog.mtime());
    }
  }
  BlockID last_blk_to_be_abandoned = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &file_from_ms,
               std::vector<BlockProto>{file_from_editlog.blocks().begin(),
                                       file_from_editlog.blocks().end()},
               /*should_complete_last_block=*/true,
               ctx,
               &last_blk_to_be_abandoned,
               true);
  CHECK_EQ(IsBlockIDValid(proto.abandoned_blk_id()),
           IsBlockIDValid(last_blk_to_be_abandoned));
  if (IsBlockIDValid(proto.abandoned_blk_id())) {
    CHECK_EQ(proto.abandoned_blk_id(), last_blk_to_be_abandoned);
  }

  CHECK(file_from_ms.status() == INode::kFileUnderConstruction) << proto.path();
  CHECK(!ctx->done->callback());
  ctx->done->set_callback([this, proto, file_from_ms](const Status& s) {
    CHECK(s.IsOK()) << s.ToString();
    RemoveLease(file_from_ms);
  });
  file_from_ms.set_status(INode::kFileComplete);
  file_from_ms.clear_uc();
  file_from_ms.clear_xattrs();
  file_from_ms.mutable_xattrs()->CopyFrom(file_from_editlog.xattrs());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file_from_ms, ancestors);
  meta_storage_->OrderedCloseFile(
      &iip.MutableInode(),
      proto.has_last_bip() ? &proto.last_bip() : nullptr,
      last_blk_to_be_abandoned,
      &old_file,
      inode_snaplog,
      op->txid(),
      ctx->done,
      STAT_RECORDER_PTR);
  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
            << ", op_name: " << op->op_name() << ", path: " << proto.path()
            << ", apply op cost us: " << time_cost_us;
  }
}

// TODO(ruanjunbin): Please update local block info proto and test it.
void NameSpace::UpdateBlocks(const std::string& path,
                             INode* file,
                             const std::vector<BlockProto> &new_blocks,
                             bool should_complete_last_block,
                             std::shared_ptr<ApplyContext> ctx,
                             BlockID* last_blk_to_delete,
                             bool bind_callback) {
  auto old_blocks = file->mutable_blocks();
  bool is_gs_update = old_blocks->size() == new_blocks.size();

  if (new_blocks.size() == 1) {
    auto& orphan_blk = new_blocks[0];
    VLOG(9) << "UpdateBlocks: path=" << path
            << ", orphan_blk=" << orphan_blk.ShortDebugString();
  } else if (new_blocks.empty()) {
    VLOG(9) << "UpdateBlocks: path=" << path << ", no blocks";
  } else {
    auto& penult_blk = new_blocks[new_blocks.size() - 2];
    auto& last_blk = new_blocks[new_blocks.size() - 1];
    VLOG(9) << "UpdateBlocks: path=" << path
            << ", penult_blk=" << penult_blk.ShortDebugString()
             << ", last_blk=" << last_blk.ShortDebugString();
  }

  std::vector<Block> future_blks;
  std::vector<Block> to_update_blks;

  for (size_t i = 0; i < old_blocks->size() && i < new_blocks.size(); i++) {
    auto old_blk = file->mutable_blocks(i);
    auto new_blk = new_blocks[i];
    bool is_last = (i == new_blocks.size() - 1);

    if (old_blk->blockid() != new_blk.blockid()
        || (old_blk->genstamp() != new_blk.genstamp()
            && !(is_gs_update && is_last))) {
      LOG(ERROR) << "Mismatched block IDs or generation stamps, " <<
                 "attempting to replace block " << old_blk->ShortDebugString()
                 << " with " << new_blk.ShortDebugString() <<
                 " as block # " << i << "/" << new_blocks.size() << " of "
                 << path;
    }
    old_blk->set_numbytes(new_blk.numbytes());
    bool changed = old_blk->genstamp() != new_blk.genstamp();
    old_blk->set_genstamp(new_blk.genstamp());
    to_update_blks.emplace_back(Block{old_blk->blockid(),
                                      static_cast<uint32_t>(new_blk.numbytes()),
                                      new_blk.genstamp()});
    // In Block Protocol V2, the block that is not the last one does not need to
    // be in the complete state either.
    // But it looks like there is no need to change here
    if (!block_manager_->BlockHasBeenComplete(old_blk->blockid())
        && (!is_last || should_complete_last_block)) {
      changed = true;
      block_manager_->CommitOrCompleteOrPersistLastBlock(
          Block{old_blk->blockid(),
                static_cast<uint32_t>(old_blk->numbytes()),
                old_blk->genstamp()},
          true);
    }
    if (changed) {
      // The state or gen-stamp of the block has changed. So, we may be
      // able to process some messages from datanodes that we previously
      // were unable to process.
      future_blks.emplace_back(Block{new_blk.blockid(),
                                     static_cast<uint32_t>(new_blk.numbytes()),
                                     new_blk.genstamp()});
    }
  }

  if (new_blocks.size() < old_blocks->size()) {
    // we're removing a block from file, e.g. abandonBlock()
    CHECK_EQ(file->status(), INode_Status_kFileUnderConstruction)
        << "try to remove a block from not under construction file " << path;
    CHECK_EQ(new_blocks.size(), old_blocks->size() - 1)
        << "try to remove more than one block from file " << path;
    auto old_block = old_blocks->Get(old_blocks->size() - 1);
    file->mutable_blocks()->RemoveLast();
    // https://bytedance.feishu.cn/docx/HCkndtWbkomsOUxB7uRcvglOn0g#PjjgdY9bToHgwgxtP44cocJMnos
    if (last_blk_to_delete != nullptr) {
      *last_blk_to_delete = old_block.blockid();
    }
  } else if (new_blocks.size() > old_blocks->size()) {
    // we're adding blocks
    for (size_t i = old_blocks->size(); i < new_blocks.size(); i++) {
      BlockProto new_blk = new_blocks[i];
      // TODO(ruanjunbin): Please make sure state can't be kPersisted.
      BlockUCState state = BlockUCState::kComplete;
      if (!should_complete_last_block) {
        state = BlockUCState::kUnderConstruction;
      }
      auto b = Block(new_blk.blockid(), new_blk.numbytes(), new_blk.genstamp());
      block_manager_->AddBlock(b,
                               file->id(),
                               file->parent_id(),
                               static_cast<uint8_t>(file->replication()),
                               // TODO(ruanjunbin)
                               cloudfs::DATANODE_BLOCK,
                               std::vector<uint32_t>(),
                               state);
      BlockProto *bp = file->mutable_blocks()->Add();
      bp->set_blockid(new_blk.blockid());
      bp->set_genstamp(new_blk.genstamp());
      bp->set_numbytes(new_blk.numbytes());
      future_blks.emplace_back(Block{new_blk.blockid(),
                                     static_cast<uint32_t>(new_blk.numbytes()),
                                     new_blk.genstamp()});
    }
  }

  if (to_update_blks.empty()) {
    return;
  }

  std::function<void()> handle_to_update_blks = [this, to_update_blks]() {
    for (auto& b : to_update_blks) {
      block_manager_->UpdateGenStamp(b.id, b.gs);
      block_manager_->UpdateLength(b.id, b.num_bytes);
    }
  };

  if (bind_callback) {
    ctx->done->add_post_callback(
        [handle_to_update_blks, this](const Status& s) {
          handle_to_update_blks();
        });
  } else {
    handle_to_update_blks();
  }

  if (future_blks.empty()) {
    return;
  }

  std::function<void()> handle_future_blks = [future_blks, path, this]() {
    StopWatch sw;
    sw.Start();
    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto& b : future_blks) {
      block_manager_->ProcessPendingFutureBlks(
          b.id, current_block_id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
    }
    auto time_cost_us = sw.NextStepTime();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
              << " slow, path: " << path
              << ", process cost us: " << time_cost_us;
    }
  };

  if (bind_callback) {
    ctx->done->add_post_callback(
        [handle_future_blks, this](const Status& s) { handle_future_blks(); });
  } else {
    handle_future_blks();
  }
}

void NameSpace::ApplyOpDelete(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpDelete>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path();

  INodeInPath src_iip, src_parent_iip;
  const INode& src_inode = src_iip.Inode();
  std::vector<INode> src_ancestors;
  bool move_to_recycle_bin = false;
  // the dummy_XXX variables won't be used in DeleteInternal()
  std::string dummy_rb;
  std::vector<cnetpp::base::StringPiece> dummy_rb_path_components;
  INodeInPath dummy_rb_inode, dummy_rb_parent;
  std::vector<INodeAndSnapshot> inodes_del, parents;
  std::vector<INode> inodes_old;
  SnapshotLog inode_snaplog, parent_snaplog;

  // 1. get inodes and check
  METRIC_WATCH_START(op_delete_get_last_inode_time_)
  {
    auto s = DeleteInternal(op->path(),
                            dummy_rb,
                            ctx->src_path_components,
                            dummy_rb_path_components,
                            true,
                            &src_iip,
                            &src_parent_iip,
                            &move_to_recycle_bin,
                            &dummy_rb_inode,
                            &dummy_rb_parent,
                            UserGroupInfo("root", "supergroup"),
                            &src_ancestors,
                            nullptr,
                            nullptr);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());

    if (move_to_recycle_bin) {
      // out case 1: target inode can be moved to recycle bin.
      LOG(FATAL) << "OpDelete will only be produced when target is "
                 << "removed directly, instead of been renamed to "
                 << "recycle bin, which will generates OpRenameOld.";
    } else {
      // out case 2: target inode should be deleted directly.
      src_parent_iip.SetModificationTime(op->timestamp());
      src_iip.GenerateSnapshotLog(&inode_snaplog);
      src_parent_iip.GenerateSnapshotLog(&parent_snaplog);
      inodes_del.emplace_back(&src_iip.MutableInode(), &inode_snaplog);
      parents.emplace_back(&src_parent_iip.MutableInode(), &parent_snaplog);
    }
  }
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = src_inode;
  METRIC_WATCH_STOP(op_delete_get_last_inode_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_delete_del_cache_time_)
  {
    auto resp = std::make_shared<DeleteResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  }
  METRIC_WATCH_STOP(op_delete_del_cache_time_)

  // 3. commit txn
  {
    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);

    ctx->done->set_callback(
        [this, path = op->path(), src_inode](const Status& s) {
          CHECK(s.IsOK());

          DeleteINodeCallback(src_inode, path);
        });
    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyOpDeleteV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDeleteV2, INodeToBeDelete);

  std::string rpc_clientid;
  uint32_t rpc_callid;
  INodeInPath src_iip, src_parent_iip;
  const INode& src_inode = src_iip.Inode();
  const INode& src_parent = src_parent_iip.Inode();
  std::vector<INode> src_ancestors;
  bool move_to_recycle_bin = false;
  // the dummy_XXX variables won't be used in DeleteInternal()
  std::string dummy_rb;
  std::vector<cnetpp::base::StringPiece> dummy_rb_path_components;
  INodeInPath dummy_rb_inode, dummy_rb_parent;
  std::vector<BlockInfoProto> overwritten_bips;
  std::vector<INodeAndSnapshot> inodes_del, parents;
  std::vector<INode> inodes_old;
  SnapshotLog inode_snaplog, parent_snaplog;

  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  // 1. get inodes and check
  METRIC_WATCH_START(op_delete_get_last_inode_time_)
  {
    auto s = DeleteInternal(proto.path(),
                            dummy_rb,
                            ctx->src_path_components,
                            dummy_rb_path_components,
                            true,
                            &src_iip,
                            &src_parent_iip,
                            &move_to_recycle_bin,
                            &dummy_rb_inode,
                            &dummy_rb_parent,
                            UserGroupInfo("root", "supergroup"),
                            &src_ancestors,
                            nullptr,
                            nullptr);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());

    if (move_to_recycle_bin) {
      // out case 1: target inode can be moved to recycle bin.
      LOG(FATAL) << "OpDeleteV2 will only be produced when target is "
                 << "removed directly, instead of been renamed to "
                 << "recycle bin, which will generates OpRenameOld.";
    } else {
      // out case 2: target inode should be deleted directly.
      CompareINodeFromMetaStorageAndEditLog(src_inode, proto.inode());
      CHECK_EQ(src_parent.id(), proto.parent().id());
      src_iip.MutableInodeUnsafe().CopyFrom(proto.inode());
      src_parent_iip.MutableInodeUnsafe().CopyFrom(proto.parent());
      src_iip.GenerateSnapshotLog(&inode_snaplog);
      src_parent_iip.GenerateSnapshotLog(&parent_snaplog);
      inodes_del.emplace_back(&src_iip.MutableInode(), &inode_snaplog);
      parents.emplace_back(&src_parent_iip.MutableInode(), &parent_snaplog);
    }
  }
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = src_inode;
  METRIC_WATCH_STOP(op_delete_get_last_inode_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_delete_del_cache_time_)
  {
    auto resp = std::make_shared<DeleteResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
  }
  METRIC_WATCH_STOP(op_delete_del_cache_time_)

  // 3. commit txn
  {
    if (ctx->logical_apply_check_physical_log) {
      // XXX skip check parent-snaplog: M-4724035558
      if (IsHdfsMode()) {
        CHECK_GT(proto.ancestors_id_size(), 0);
        CompareAncestors(src_ancestors, proto.ancestors_id());
      }
      CompareINodeFromMetaStorageAndEditLog(src_inode, proto.inode());
      //CompareINodeFromMetaStorageAndEditLog(src_parent, proto.parent());
      CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
      //CompareSnapshotLog(parent_snaplog, proto.parent_snaplog());
    }

    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);

    ctx->done->set_callback(
        [this, path = proto.path(), src_inode](const Status& s) {
          CHECK(s.IsOK());

          DeleteINodeCallback(src_inode, path);
        });
    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyOpSetReplication(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetReplication>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path()
          << " replica:" << op->replication();

  METRIC_WATCH_START(op_set_replication_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->path();
  CHECK_EQ(iip.Inode().type(), INode_Type_kFile);
  METRIC_WATCH_STOP(op_set_replication_get_last_inode_time_)
  auto replica = AdjustReplication(op->replication());
  iip.MutableInode().set_replication(replica);
  // no need to modify blockmap in standby.
  // namenode will check blocks' replica after failover.
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetReplicationV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetReplicationV2, INodeToSetReplication);

  METRIC_WATCH_START(op_set_replication_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  CHECK_EQ(iip.Inode().type(), INode_Type_kFile);
  METRIC_WATCH_STOP(op_set_replication_get_last_inode_time_)
  auto replica = AdjustReplication(proto.replication());
  iip.MutableInode().set_replication(replica);
  // no need to modify blockmap in standby.
  // namenode will check blocks' replica after failover.
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(), nullptr, inode_snaplog, op->txid(), ctx->done);
}

uint32_t NameSpace::AdjustReplication(uint32_t replica) {
  uint32_t dfs_replication_min =
      FLAGS_bytecool_feature_enabled ? 0 : FLAGS_dfs_replication_min;
  if (replica < dfs_replication_min) {
    return dfs_replication_min;
  } else if (replica > FLAGS_dfs_replication_max) {
    return FLAGS_dfs_replication_max;
  }
  return replica;
}

void NameSpace::ApplyOpMkDir(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpMkdir>(ctx->op);
  VLOG(9) << "ApplyOpMkDir " << op->op_name() << " path=" << op->path()
          << " inode id=" << op->inodeId() << " txid=" << op->txid();

  UpdateLastINodeId(op->inodeId());
  METRIC_WATCH_START(op_mkdir_get_parent_time_)
  INode d;
  INodeInPath last_parent_iip;
  const INode& last_parent = last_parent_iip.Inode();
  std::vector<INode> ancestors;
  CHECK(GetParent(ctx->src_path_components, &last_parent_iip, &ancestors))
      << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_mkdir_get_parent_time_)

  MakeINode(op->inodeId(), last_parent.id(),
      ctx->src_path_components.back().as_string(),
      op->permissions(), INode_Type_kDirectory, &d);
  d.set_mtime(op->mtime());
  d.set_atime(op->atime());
  if (!op->aclEntries().empty()) {
    google::protobuf::RepeatedPtrField<::cloudfs::AclEntryProto> acls(
        op->aclEntries().begin(), op->aclEntries().end());
    d.mutable_acls()->Swap(&acls);
  }
  if (op->xAttrs().xattrs_size() > 0) {
    google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs(
        op->xAttrs().xattrs().begin(), op->xAttrs().xattrs().end());
    d.mutable_xattrs()->Swap(&xattrs);
  }

  last_parent_iip.SetModificationTime(op->mtime());
  SnapshotLog parent_snaplog;
  last_parent_iip.GenerateSnapshotLog(&parent_snaplog);

  UpdateINodeAttrs(d, last_parent_iip.Inode(), true);

  if (NameSpace::IsAccMode() && ctx->acc_open_or_mkdir_inode) {
    CheckAccINode(*ctx->acc_open_or_mkdir_inode);
    CHECK(d.id() == ctx->acc_open_or_mkdir_inode->id());
    d.mutable_ufs_dir_info()->CopyFrom(
        ctx->acc_open_or_mkdir_inode->ufs_dir_info());
  }

  VLOG(9) << "ApplyOpMkDir"
          << " parent=" << last_parent.ShortDebugString();
  VLOG(9) << "ApplyOpMkDir"
          << " inode=" << d.ShortDebugString();

  ctx->done->add_post_callback([=](const Status& s) {
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name() << ", path: " << op->path()
              << ", apply op cost us: " << time_cost_us;
    }
  });

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_ADD(ctx, d, ancestors);

  std::vector<INode*> inodes_add = {&d};
  std::vector<INodeAndSnapshot> parents;
  parents.emplace_back(&last_parent_iip.MutableInode(), &parent_snaplog);
  meta_storage_->OrderedCommitINodes(&inodes_add,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &parents,
                                     nullptr,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);

  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
            << ", op_name: " << op->op_name() << ", path: " << op->path()
            << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::ApplyOpMkDirV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpMkdirV2, DirToBeMake);

  UpdateLastINodeId(proto.inode().id());
  METRIC_WATCH_START(op_mkdir_get_parent_time_)
  INode d;
  INodeInPath last_parent_iip;
  const INode& last_parent = last_parent_iip.Inode();
  std::vector<INode> ancestors;
  CHECK(GetParent(ctx->src_path_components, &last_parent_iip, &ancestors));
  METRIC_WATCH_STOP(op_mkdir_get_parent_time_)

  MakeINode(proto.inode().id(), last_parent.id(),
      ctx->src_path_components.back().as_string(),
      proto.inode().permission(), INode_Type_kDirectory, &d);
  d.set_mtime(proto.inode().mtime());
  d.set_atime(proto.inode().atime());
  if (proto.inode().acls_size() > 0) {
    google::protobuf::RepeatedPtrField<::cloudfs::AclEntryProto> acls(
        proto.inode().acls().begin(), proto.inode().acls().end());
    d.mutable_acls()->Swap(&acls);
  }
  if (proto.inode().xattrs_size() > 0) {
    google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs(
        proto.inode().xattrs().begin(), proto.inode().xattrs().end());
    d.mutable_xattrs()->Swap(&xattrs);
  }

  last_parent_iip.SetModificationTime(proto.inode().mtime());
  SnapshotLog parent_snaplog;
  last_parent_iip.GenerateSnapshotLog(&parent_snaplog);

  UpdateINodeAttrs(d, last_parent_iip.Inode(), true);

  if (NameSpace::IsAccMode() && ctx->acc_open_or_mkdir_inode) {
    CheckAccINode(*ctx->acc_open_or_mkdir_inode);
    CHECK(d.id() == ctx->acc_open_or_mkdir_inode->id());
    d.mutable_ufs_dir_info()->CopyFrom(
        ctx->acc_open_or_mkdir_inode->ufs_dir_info());
  }

  if (ctx->logical_apply_check_physical_log) {
    // XXX skip check parent-snaplog: M-4724035558
    CHECK(proto.has_parent());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(d, proto.inode());
    auto last_parent_for_compare = last_parent_iip.OldInode();
    last_parent_for_compare.set_mtime(proto.inode().mtime());
    //CompareINodeFromMetaStorageAndEditLog(last_parent_for_compare,
    //                                      proto.parent());
    //CompareSnapshotLog(parent_snaplog, proto.parent_snaplog());
  }

  if (proto.has_log_rpc_info()) {
    auto rpc_info = proto.log_rpc_info();
    auto resp = std::make_shared<MkdirsResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_info.rpc_client_id(),
                       rpc_info.rpc_call_id(),
                       std::move(resp));
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_ADD(ctx, d, ancestors);

  std::vector<INode*> inodes_add = {&d};
  std::vector<INodeAndSnapshot> parents;
  parents.emplace_back(&last_parent_iip.MutableInode(), &parent_snaplog);
  meta_storage_->OrderedCommitINodes(&inodes_add,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &parents,
                                     nullptr,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);

  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
            << ", op_name: " << op->op_name() << ", path: " << proto.path()
            << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::UpdateLastINodeId(uint64_t inode_id_from_op) {
  // inode_id_from_op may be kGrandfatherINodeId in hdfs.
  while (true) {
    auto last_id = last_inode_id();
    if (inode_id_from_op > last_id) {
      if (!last_inode_id_.compare_exchange_strong(
              last_id, inode_id_from_op, std::memory_order_acq_rel)) {
        continue;
      }
    }
    break;
  }
}

void NameSpace::UpdateLastAllocatedBlockId(uint64_t block_id_from_op) {
  while (true) {
    auto last_id = last_allocated_block_id();
    if (block_id_from_op > last_id) {
      if (!last_block_id_.compare_exchange_strong(
              last_id, block_id_from_op, std::memory_order_acq_rel)) {
        continue;
      }
    }
    break;
  }
}

void NameSpace::UpdateGenerationStampV2(uint64_t gs_v2_from_op) {
  while (true) {
    auto last_id = generation_stamp_v2();
    if (gs_v2_from_op > last_id) {
      if (!last_generation_stamp_v2_.compare_exchange_strong(
              last_id, gs_v2_from_op, std::memory_order_acq_rel)) {
        continue;
      }
    }
    break;
  }
}

void NameSpace::AddRetryCacheEntry(const std::string& client_id,
                                   uint32_t call_id,
                                   PayloadTypePtr payload) {
  if (!retry_cache_ || !FLAGS_retry_cache_enabled ||
      client_id == INVALID_CLIENT_ID || call_id == INVALID_CALL_ID) {
    return;
  }

  std::string debug_info = "Entry added by Standby applying editlog.";
  retry_cache_->AddCacheEntry(client_id, call_id, payload, debug_info);
  MFC(metrics_.standby_retry_cache_add_entry_count_)->Inc();
}

void NameSpace::ApplyOpRenameOld(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpRenameOld>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->src() << " to " << op->dst();

  INodeInPath src_iip, src_parent_iip, dst_iip, dst_parent_iip;
  const INode& src_inode = src_iip.Inode();
  const INode& src_parent = src_parent_iip.Inode();
  const INode& dst_inode = dst_iip.Inode();
  const INode& dst_parent = dst_parent_iip.Inode();
  std::vector<INode> src_ancestors;
  std::vector<INode> dst_ancestors;
  std::vector<INodeAndSnapshot> inodes_mov_src, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  SnapshotLog src_inode_snaplog, src_parent_snaplog, dst_parent_snaplog;

  // 1. get inodes and check
  METRIC_WATCH_START(op_rename_old_time_)
  {
    auto s = RenameInternal(op->src(),
                            op->dst(),
                            ctx->src_path_components,
                            ctx->dst_path_components,
                            &src_iip,
                            &dst_iip,
                            &src_parent_iip,
                            &dst_parent_iip,
                            UserGroupInfo("root", "supergroup"),
                            /*check_quota=*/false,
                            &src_ancestors,
                            &dst_ancestors);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());
    CHECK(!IsHyperFile(src_inode));

    src_iip.GenerateSnapshotLog(&src_inode_snaplog);
    src_parent_iip.GenerateSnapshotLog(&src_parent_snaplog);
    dst_parent_iip.GenerateSnapshotLog(&dst_parent_snaplog);
    inodes_mov_src.emplace_back(&src_iip.MutableInode(), &src_inode_snaplog);
    inodes_mov_dst.emplace_back(&dst_iip.MutableInode());
    parents.emplace_back(&src_parent_iip.MutableInode(), &src_parent_snaplog);
    parents.emplace_back(&dst_parent_iip.MutableInode(), &dst_parent_snaplog);
  }
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = src_inode;
  METRIC_WATCH_STOP(op_rename_old_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_rename_old_del_cache_time_)
  {
    auto resp = std::make_shared<RenameResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  }
  METRIC_WATCH_STOP(op_rename_old_del_cache_time_)

  // 3. commit txn
  {
    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_RENAME(ctx, src_inode, dst_inode, src_ancestors,
                               dst_ancestors);

    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       &inodes_mov_src,
                                       &inodes_mov_dst,
                                       nullptr,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", src_path: " << op->src()
              << ", dst_path: " << op->dst()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpRenameOldV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameOldV2, INodeToBeRenameOld);

  std::string rpc_clientid;
  uint32_t rpc_callid;
  INodeInPath src_iip, src_parent_iip, dst_iip, dst_parent_iip;
  const INode& src_inode = src_iip.Inode();
  const INode& src_parent = src_parent_iip.Inode();
  const INode& dst_inode = dst_iip.Inode();
  const INode& dst_parent = dst_parent_iip.Inode();
  std::vector<INode> src_ancestors;
  std::vector<INode> dst_ancestors;
  std::vector<INodeAndSnapshot> inodes_mov_src, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  SnapshotLog src_inode_snaplog, src_parent_snaplog, dst_parent_snaplog;

  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  // 1. get inodes and check
  METRIC_WATCH_START(op_rename_old_time_)
  {
    // FIXME: now action is recorded as super user.
    auto s = RenameInternal(proto.src_path(),
                            proto.dst_path(),
                            ctx->src_path_components,
                            ctx->dst_path_components,
                            &src_iip,
                            &dst_iip,
                            &src_parent_iip,
                            &dst_parent_iip,
                            UserGroupInfo("root", "supergroup"),
                            /*check_quota=*/false,
                            &src_ancestors,
                            &dst_ancestors);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());
    CHECK(!IsHyperFile(src_inode));
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = src_iip.OldInode();

    CompareINodeFromMetaStorageAndEditLog(src_inode, proto.src_inode());
    CHECK_EQ(dst_inode.id(), proto.dst_inode().id());
    CHECK_EQ(src_parent.id(), proto.src_parent().id());
    CHECK_EQ(dst_parent.id(), proto.dst_parent().id());
    src_iip.MutableInodeUnsafe().CopyFrom(proto.src_inode());
    dst_iip.MutableInodeUnsafe().CopyFrom(proto.dst_inode());
    src_parent_iip.MutableInodeUnsafe().CopyFrom(proto.src_parent());
    dst_parent_iip.MutableInodeUnsafe().CopyFrom(proto.dst_parent());

    src_iip.GenerateSnapshotLog(&src_inode_snaplog);
    src_parent_iip.GenerateSnapshotLog(&src_parent_snaplog);
    dst_parent_iip.GenerateSnapshotLog(&dst_parent_snaplog);
    inodes_mov_src.emplace_back(&src_iip.MutableInode(), &src_inode_snaplog);
    inodes_mov_dst.emplace_back(&dst_iip.MutableInode());
    parents.emplace_back(&src_parent_iip.MutableInode(), &src_parent_snaplog);
    parents.emplace_back(&dst_parent_iip.MutableInode(), &dst_parent_snaplog);
  }
  METRIC_WATCH_STOP(op_rename_old_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_rename_old_del_cache_time_)
  {
    auto resp = std::make_shared<RenameResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
  }
  METRIC_WATCH_STOP(op_rename_old_del_cache_time_)

  // 3. commit txn
  {
    if (ctx->logical_apply_check_physical_log) {
      // XXX skip check parent-snaplog: M-4724035558
      CHECK_EQ(proto.src_inode().id(), proto.dst_inode().id());
      CHECK_EQ(proto.src_inode().parent_id(), proto.src_parent().id());
      CHECK_EQ(proto.dst_inode().parent_id(), proto.dst_parent().id());
      CHECK_GT(proto.src_ancestors_id_size(), 0);
      CHECK_GT(proto.dst_ancestors_id_size(), 0);
      CompareINodeFromMetaStorageAndEditLog(src_inode, proto.src_inode());
      //CompareINodeFromMetaStorageAndEditLog(src_parent, proto.src_parent());
      CompareINodeFromMetaStorageAndEditLog(dst_inode, proto.dst_inode());
      //CompareINodeFromMetaStorageAndEditLog(dst_parent, proto.dst_parent());
      CompareAncestors(src_ancestors, proto.src_ancestors_id());
      CompareAncestors(dst_ancestors, proto.dst_ancestors_id());
      CompareSnapshotLog(src_inode_snaplog, proto.src_inode_snaplog());
      //CompareSnapshotLog(src_parent_snaplog, proto.src_parent_snaplog());
      //CompareSnapshotLog(dst_parent_snaplog, proto.dst_parent_snaplog());
    }

    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_RENAME(ctx, src_inode, dst_inode, src_ancestors,
                               dst_ancestors);

    CHECK_NE(op->txid(), kInvalidTxId);
    // INodeToBeRenameOld will not delete file.
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       &inodes_mov_src,
                                       &inodes_mov_dst,
                                       nullptr,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", src_path: " << proto.src_path()
              << ", dst_path: " << proto.dst_path()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpRename(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpRename>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->src() << " to " << op->dst()
          << ", overwrite:" << op->options().overwrite();

  INodeInPath src_iip, src_parent_iip, old_dst_iip, new_dst_iip, dst_parent_iip;
  const INode& src_inode = src_iip.Inode();
  const INode& src_parent = src_parent_iip.Inode();
  const INode& old_dst_inode = old_dst_iip.Inode();
  const INode& new_dst_inode = new_dst_iip.Inode();
  const INode& dst_parent = dst_parent_iip.Inode();
  std::vector<INode> src_ancestors;
  std::vector<INode> dst_ancestors;
  bool move_to_recycle_bin = false;
  // the dummy_XXX variables won't be used in Rename2Internal()
  std::string dummy_rb;
  INode dummy_rb_inode;
  INode dummy_rb_parent;
  std::vector<cnetpp::base::StringPiece> dummy_rb_path_components;
  std::vector<INodeAndSnapshot> inodes_mov_src, inodes_del, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  SnapshotLog src_inode_snaplog, old_dst_inode_snaplog;
  SnapshotLog src_parent_snaplog, dst_parent_snaplog;
  bool old_deleted = false;

  // 1. get inodes and check
  METRIC_WATCH_START(op_rename_time_)
  {
    // FIXME: now action is recorded as super user.
    auto s = Rename2Internal(op->src(),
                             op->dst(),
                             dummy_rb,
                             ctx->src_path_components,
                             ctx->dst_path_components,
                             dummy_rb_path_components,
                             op->options().overwrite(),
                             &move_to_recycle_bin,
                             &src_iip,
                             &old_dst_iip,
                             &new_dst_iip,
                             nullptr,
                             &src_parent_iip,
                             &dst_parent_iip,
                             nullptr,
                             UserGroupInfo("root", "supergroup"),
                             /*check_quota=*/false,
                             &src_ancestors,
                             &dst_ancestors,
                             nullptr);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());
    CHECK(!IsHyperFile(src_inode));
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = src_inode;

    if (old_dst_inode.has_id()) {
      CHECK(op->options().overwrite());
      if (move_to_recycle_bin) {
        // out case 1: old dst inode exists, move it to recycle bin.
        LOG(FATAL) << "OpRename will move old dst inode to recycle bin in "
                   << "in previous editlog, instead of triggering here.";
      } else {
        // out case 2: old dst inode exists, remove it.
        old_deleted = true;
        old_dst_iip.GenerateSnapshotLog(&old_dst_inode_snaplog);
        inodes_del.emplace_back(&old_dst_iip.MutableInode(),
                                &old_dst_inode_snaplog);
      }
    } else {
      // out case 3: old dst inode not exists.
    }
    CHECK_EQ(src_inode.id(), new_dst_inode.id());

    src_iip.GenerateSnapshotLog(&src_inode_snaplog);
    src_parent_iip.GenerateSnapshotLog(&src_parent_snaplog);
    dst_parent_iip.GenerateSnapshotLog(&dst_parent_snaplog);
    inodes_mov_src.emplace_back(&src_iip.MutableInode(),
                                &src_inode_snaplog);
    inodes_mov_dst.emplace_back(&new_dst_iip.MutableInode());
    parents.emplace_back(&src_parent_iip.MutableInode(),
                         &src_parent_snaplog);
    parents.emplace_back(&dst_parent_iip.MutableInode(),
                         &dst_parent_snaplog);
  }
  METRIC_WATCH_STOP(op_rename_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_rename_old_del_cache_time_)
  {
    auto resp = std::make_shared<Rename2ResponseProto>();
    AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  }
  METRIC_WATCH_STOP(op_rename_old_del_cache_time_)

  // 3. commit txn
  {
    DECLARE_STAT_RECORDER(meta_storage_, ctx);
    STAT_RECORDER_INODE_RENAME_OVERWRITE(ctx,
                                         src_inode,
                                         new_dst_inode,
                                         old_dst_inode,
                                         src_ancestors,
                                         dst_ancestors);

    ctx->done->set_callback([this, old_dst_inode](const Status& s) {
      CHECK(s.IsOK());

      DeleteINodeCallback(old_dst_inode);
    });
    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       &inodes_mov_src,
                                       &inodes_mov_dst,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->src()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpRenameV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameV2, INodeToBeRename);
  bool overwrite = RenameOptions(proto.rename_options().value()).overwrite();
  VLOG(9) << op->op_name() << ": src: " << proto.src_path()
           << ", dst: " << proto.dst_path()
           << ", overwrite: " << overwrite;

  std::string rpc_clientid;
  uint32_t rpc_callid;
  INodeInPath src_iip, src_parent_iip, old_dst_iip, new_dst_iip, dst_parent_iip;
  const INode& src_inode = src_iip.Inode();
  const INode& src_parent = src_parent_iip.Inode();
  const INode& old_dst_inode = old_dst_iip.Inode();
  const INode& new_dst_inode = new_dst_iip.Inode();
  const INode& dst_parent = dst_parent_iip.Inode();
  std::vector<INode> src_ancestors;
  std::vector<INode> dst_ancestors;
  std::vector<INode> rb_ancestors;
  bool move_to_recycle_bin = false;
  INodeInPath rb_iip, rb_parent_iip;
  const INode& rb_inode = rb_iip.Inode();
  const INode& rb_parent = rb_parent_iip.Inode();
  std::vector<BlockInfoProto> overwritten_bips;
  std::vector<INodeAndSnapshot> inodes_mov_src, inodes_del, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  SnapshotLog src_inode_snaplog, old_dst_inode_snaplog;
  SnapshotLog src_parent_snaplog, dst_parent_snaplog, rb_parent_snaplog;
  bool old_deleted = false;

  DECLARE_STAT_RECORDER(meta_storage_, ctx);

  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  // 1. get inodes and check
  METRIC_WATCH_START(op_rename_time_)
  {
    // FIXME: now action is recorded as super user.
    auto s = Rename2Internal(proto.src_path(),
                             proto.dst_path(),
                             proto.has_rb_path()
                                 ? proto.rb_path()
                                 : std::string(),
                             ctx->src_path_components,
                             ctx->dst_path_components,
                             ctx->rb_path_components,
                             overwrite,
                             &move_to_recycle_bin,
                             &src_iip,
                             &old_dst_iip,
                             &new_dst_iip,
                             &rb_iip,
                             &src_parent_iip,
                             &dst_parent_iip,
                             &rb_parent_iip,
                             UserGroupInfo("root", "supergroup"),
                             /*check_quota=*/false,
                             &src_ancestors,
                             &dst_ancestors,
                             &rb_ancestors);
    CHECK(s.code() == Code::kOK);
    CHECK(!s.HasException());
    CHECK(!IsHyperFile(src_inode));
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = src_iip.OldInode();

    if (old_dst_inode.has_id()) {
      CHECK(overwrite);
      CompareINodeFromMetaStorageAndEditLog(old_dst_inode,
                                            proto.overwrite_inode());
      old_dst_iip.MutableInodeUnsafe().CopyFrom(proto.overwrite_inode());

      if (proto.has_move_to_recycle_bin() && proto.move_to_recycle_bin()) {
        // out case 1: old dst inode exists, move it to recycle bin.
        CHECK(proto.has_rb_path());
        CHECK(proto.has_rb_inode());
        CHECK(proto.has_rb_parent());
        // the local result (@move_to_recycle_bin, @rb_inode, @rb_parent)
        // may differ from editlog, due to clock skew.
        rb_iip.MutableInodeUnsafe().CopyFrom(proto.rb_inode());
        rb_parent_iip.MutableInodeUnsafe().CopyFrom(proto.rb_parent());
        old_dst_iip.GenerateSnapshotLog(&old_dst_inode_snaplog);
        rb_parent_iip.GenerateSnapshotLog(&rb_parent_snaplog);
        inodes_mov_src.emplace_back(&old_dst_iip.MutableInode(),
                                    &old_dst_inode_snaplog);
        inodes_mov_dst.emplace_back(&rb_iip.MutableInode());
        parents.emplace_back(&rb_parent_iip.MutableInode(),
                             &rb_parent_snaplog);
        STAT_RECORDER_INODE_RENAME(ctx,
                                   old_dst_inode,
                                   rb_inode,
                                   dst_ancestors,
                                   rb_ancestors);
      } else {
        // out case 2: old dst inode exists, remove it.
        old_deleted = true;
        old_dst_iip.GenerateSnapshotLog(&old_dst_inode_snaplog);
        inodes_del.emplace_back(&old_dst_iip.MutableInode(),
                                &old_dst_inode_snaplog);
        STAT_RECORDER_INODE_DELETE(ctx, old_dst_inode, dst_ancestors);
      }
    } else {
      // out case 3: old dst inode not exists.
    }
    STAT_RECORDER_INODE_RENAME(ctx, src_inode, new_dst_inode,
                               src_ancestors, dst_ancestors);
    CompareINodeFromMetaStorageAndEditLog(src_inode, proto.src_inode());
    CHECK_EQ(new_dst_inode.id(), proto.dst_inode().id());
    CHECK_EQ(src_parent.id(), proto.src_parent().id());
    CHECK_EQ(dst_parent.id(), proto.dst_parent().id());
    new_dst_iip.MutableInodeUnsafe().CopyFrom(proto.dst_inode());
    src_parent_iip.MutableInodeUnsafe().CopyFrom(proto.src_parent());
    dst_parent_iip.MutableInodeUnsafe().CopyFrom(proto.dst_parent());

    CHECK_EQ(src_inode.id(), new_dst_inode.id());

    src_iip.GenerateSnapshotLog(&src_inode_snaplog);
    src_parent_iip.GenerateSnapshotLog(&src_parent_snaplog);
    dst_parent_iip.GenerateSnapshotLog(&dst_parent_snaplog);
    inodes_mov_src.emplace_back(&src_iip.MutableInode(),
        &src_inode_snaplog);
    inodes_mov_dst.emplace_back(&new_dst_iip.MutableInode());
    parents.emplace_back(&src_parent_iip.MutableInode(),
        &src_parent_snaplog);
    parents.emplace_back(&dst_parent_iip.MutableInode(),
        &dst_parent_snaplog);
  }
  METRIC_WATCH_STOP(op_rename_time_)

  // 2. update related cache
  METRIC_WATCH_START(op_rename_old_del_cache_time_)
  {
    auto resp = std::make_shared<Rename2ResponseProto>();
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
  }
  METRIC_WATCH_STOP(op_rename_old_del_cache_time_)

  // 3. commit txn
  {
    if (ctx->logical_apply_check_physical_log) {
      // XXX skip check parent-snaplog: M-4724035558
      CHECK_EQ(proto.src_inode().id(), proto.dst_inode().id());
      CHECK_EQ(proto.src_inode().parent_id(), proto.src_parent().id());
      CHECK_EQ(proto.dst_inode().parent_id(), proto.dst_parent().id());
      CHECK_GT(proto.src_ancestors_id_size(), 0);
      CHECK_GT(proto.dst_ancestors_id_size(), 0);
      if (proto.has_overwrite_inode()) {
        if (proto.move_to_recycle_bin()) {
          CHECK(proto.has_rb_path());
          CHECK(proto.has_rb_inode());
          CHECK(proto.has_rb_parent());
          CHECK_EQ(proto.rb_inode().id(), proto.overwrite_inode().id());
          CHECK_EQ(proto.rb_inode().parent_id(), proto.rb_parent().id());
          CHECK_GT(proto.rb_ancestors_id_size(), 0);
          CompareINodeFromMetaStorageAndEditLog(rb_inode, proto.rb_inode());
          //CompareINodeFromMetaStorageAndEditLog(rb_parent, proto.rb_parent());
          CompareAncestors(rb_ancestors, proto.rb_ancestors_id());
          //CompareSnapshotLog(rb_parent_snaplog, proto.rb_parent_snaplog());
        } else {
          CHECK(!proto.has_rb_path());
          CHECK(!proto.has_rb_inode());
          CHECK(!proto.has_rb_parent());
          CHECK_EQ(proto.rb_ancestors_id_size(), 0);
        }
        CompareINodeFromMetaStorageAndEditLog(old_dst_inode, proto.overwrite_inode());
        //CompareINodeFromMetaStorageAndEditLog(dst_parent, proto.dst_parent());
        CompareSnapshotLog(old_dst_inode_snaplog, proto.old_dst_inode_snaplog());
        //CompareSnapshotLog(dst_parent_snaplog, proto.dst_parent_snaplog());
      } else {
        CHECK(!proto.move_to_recycle_bin());
        CHECK(!proto.has_rb_path());
        CHECK(!proto.has_rb_inode());
        CHECK(!proto.has_rb_parent());
        CHECK_EQ(proto.rb_ancestors_id_size(), 0);
      }
      CompareINodeFromMetaStorageAndEditLog(src_inode, proto.src_inode());
      //CompareINodeFromMetaStorageAndEditLog(src_parent, proto.src_parent());
      CompareINodeFromMetaStorageAndEditLog(new_dst_inode, proto.dst_inode());
      //CompareINodeFromMetaStorageAndEditLog(dst_parent, proto.dst_parent());
      CompareAncestors(src_ancestors, proto.src_ancestors_id());
      CompareAncestors(dst_ancestors, proto.dst_ancestors_id());
      CompareSnapshotLog(src_inode_snaplog, proto.src_inode_snaplog());
      //CompareSnapshotLog(src_parent_snaplog, proto.src_parent_snaplog());
    }

    ctx->done->set_callback([this, old_dst_inode](const Status& s) {
      CHECK(s.IsOK());
      // https://bytedance.feishu.cn/docx/HCkndtWbkomsOUxB7uRcvglOn0g#DbXOdjz8Uoiv1CxeddZcmMv7nwc
      DeleteINodeCallback(old_dst_inode);
    });
    CHECK_NE(op->txid(), kInvalidTxId);
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       &inodes_mov_src,
                                       &inodes_mov_dst,
                                       &inodes_del,
                                       &parents,
                                       &inodes_old,
                                       {},
                                       {},
                                       op->txid(),
                                       {ctx->done},
                                       STAT_RECORDER_PTR);

    auto time_cost_us =
        std::chrono::duration_cast<std::chrono::microseconds>
            (ctx->sw->GetTime()).count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << proto.src_path()
              << ", apply op cost us: " << time_cost_us;
    }
  }
}

void NameSpace::ApplyOpSetPermissions(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetPermissions>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->src();
  METRIC_WATCH_START(op_set_permissions_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->src();
  METRIC_WATCH_STOP(op_set_permissions_get_last_inode_time_)
  iip.MutableInode().mutable_permission()->set_permission(op->permissions());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetPermissionsV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetPermissionsV2, INodeToSetPermissions);

  METRIC_WATCH_START(op_set_permissions_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  METRIC_WATCH_STOP(op_set_permissions_get_last_inode_time_)
  iip.MutableInode().mutable_permission()->set_permission(proto.permissions());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetOwner(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetOwner>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->src() << " user: " << op->username()
          << " group:" << op->groupname();
  METRIC_WATCH_START(op_set_owner_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->src();
  METRIC_WATCH_STOP(op_set_owner_get_last_inode_time_)
  if (!op->username().empty()) {
    iip.MutableInode().mutable_permission()->set_username(op->username());
  }
  if (!op->groupname().empty()) {
    iip.MutableInode().mutable_permission()->set_groupname(op->groupname());
  }

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetOwnerV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetOwnerV2, INodeToSetOwner);

  METRIC_WATCH_START(op_set_owner_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  METRIC_WATCH_STOP(op_set_owner_get_last_inode_time_)
  iip.MutableInode().mutable_permission()->set_username(proto.username());
  if (!proto.groupname().empty()) {
    iip.MutableInode().mutable_permission()->set_groupname(proto.groupname());
  }
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetGenstampV1(std::shared_ptr<ApplyContext> ctx) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(WARNING)
        << "FLAGS_enable_fast_block_id_and_gs_gen=true, should not call "
           "ApplyOpAllocateBlockId";
  }
  auto op = std::static_pointer_cast<OpSetGenstampV1>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->genStampV1();
  {
    std::lock_guard<std::mutex> guard(genstamp_v1_mutex_);
    generation_stamp_v1_.store(op->genStampV1());
  }
  char gs1[sizeof(uint64_t)];
  platform::WriteBigEndian(gs1, 0, op->genStampV1());
  meta_storage_->OrderedPutNameSystemInfo(kGenerationStampV1Key,
                                          cnetpp::base::StringPiece(gs1, 8),
                                          op->txid(),
                                          ctx->done);
}

void NameSpace::ApplyOpSetGenerationStampV1(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetGenStampV1, GenStampToBeSet);
  {
    std::lock_guard<std::mutex> guard(genstamp_v1_mutex_);
    generation_stamp_v1_.store(proto.new_gen_stamp());
  }
  char gs1[sizeof(uint64_t)];
  platform::WriteBigEndian(gs1, 0, proto.new_gen_stamp());
  meta_storage_->OrderedPutNameSystemInfo(kGenerationStampV1Key,
                                          cnetpp::base::StringPiece(gs1, 8),
                                          op->txid(),
                                          ctx->done);
}

void NameSpace::ApplyOpSetGenstampV2(std::shared_ptr<ApplyContext> ctx) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(WARNING)
        << "FLAGS_enable_fast_block_id_and_gs_gen=true, should not call "
           "ApplyOpAllocateBlockId";
  }
  auto op = std::static_pointer_cast<OpSetGenstampV2>(ctx->op);
  VLOG(10) << op->op_name() << ": " << op->genStampV2();
  UpdateGenerationStampV2(op->genStampV2());
  meta_storage_->OrderedPutGenerationStampV2(op->genStampV2(),
                                             op->txid(),
                                             ctx->done);
}

void NameSpace::ApplyOpSetGenerationStampV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetGenStampV2, GenStampToBeSet);
  UpdateGenerationStampV2(proto.new_gen_stamp());
  meta_storage_->OrderedPutGenerationStampV2(proto.new_gen_stamp(),
                                             op->txid(),
                                             ctx->done);
}

void NameSpace::ApplyOpTimes(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpTimes>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path();
  METRIC_WATCH_START(op_times_get_last_inode_time_)
  INodeInPath iip;
  auto s = GetLastINodeInPath(ctx->src_path_components, &iip);
  if (s != StatusCode::kOK) {
    LOG(FATAL) << "applyOpTimes GetLastINodeInPath " << op->path()
               << ", txid = " << op->txid() << ", atime = " << op->atime()
               << ", mtime = " << op->mtime() << ", code = " << s;
  }
  METRIC_WATCH_STOP(op_times_get_last_inode_time_)

  iip.SetModificationTime(op->mtime());
  iip.SetAccessTime(op->atime());
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpTimesV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetTimesV2, INodeToSetTimes);

  METRIC_WATCH_START(op_times_get_last_inode_time_)
  INodeInPath iip;
  auto s = GetLastINodeInPath(ctx->src_path_components, &iip);
  if (s != StatusCode::kOK) {
    LOG(FATAL) << "applyOpTimes GetLastINodeInPath " << proto.path()
               << ", txid = " << op->txid() << ", atime = " << proto.atime()
               << ", mtime = " << proto.mtime() << ", code = " << s;
  }
  METRIC_WATCH_STOP(op_times_get_last_inode_time_)

  iip.SetModificationTime(proto.mtime());
  iip.SetAccessTime(proto.atime());
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpConcatDelete(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "ApplyOpConcatDelete not supported!";
}

void NameSpace::ApplyOpSymlink(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSymlink>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path() << " to " << op->value();
  //CHECK(FLAGS_dfs_symlinks_enabled) << "symlinks is disabled";
  METRIC_WATCH_START(op_symlink_get_parent_time_)
  INode parent;
  CHECK(GetParent(ctx->src_path_components, &parent))
      << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_symlink_get_parent_time_)

  INode symlink;
  UpdateLastINodeId(op->inodeId());
  MakeINodeSymLink(op->inodeId(), parent.id(),
                   ctx->src_path_components.back().as_string(),
                   op->permissions(), op->value(), &symlink);
  symlink.set_atime(op->atime());
  symlink.set_mtime(op->mtime());

  auto resp = std::make_shared<CreateSymlinkResponseProto>();
  AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));
  std::vector<INode*> inodes_add = { &symlink };
  meta_storage_->OrderedCommitINodes(&inodes_add,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done});
}

void NameSpace::ApplyOpSymlinkV2(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "unsupported symlink editlog";
}

void NameSpace::ApplyOpReassignLease(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpReassignLease>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path() << ", from "
          << op->leaseHolder() << " to " << op->newHolder();
  METRIC_WATCH_START(op_reassign_lease_get_last_inode_time_)
  INodeInPath iip;
  const INode& file = iip.Inode();
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->path();
  CHECK_EQ(file.type(), INode_Type_kFile);
  CHECK(file.has_uc());
  METRIC_WATCH_STOP(op_reassign_lease_get_last_inode_time_)
  CHECK(lease_manager_->ReassignLease(
      op->leaseHolder(), file.id(), op->newHolder()))
      << "file=" << file.ShortDebugString() << " holder=" << op->leaseHolder()
      << " new_holder=" << op->newHolder();
  iip.MutableInode().mutable_uc()->set_client_name(op->newHolder());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpReassignLeaseV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpReassignLeaseV2, LeaseToBeReassign);
  METRIC_WATCH_START(op_reassign_lease_get_last_inode_time_)
  INodeInPath iip;
  const INode& file = iip.Inode();
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  CHECK_EQ(file.type(), INode_Type_kFile);
  CHECK(file.has_uc());
  METRIC_WATCH_STOP(op_reassign_lease_get_last_inode_time_)
  CHECK(lease_manager_->ReassignLease(
      proto.lease_holder(), file.id(), proto.new_holder()))
      << "file=" << file.ShortDebugString() << " holder=" << proto.lease_holder()
      << " new_holder=" << proto.new_holder();
  iip.MutableInode().mutable_uc()->set_client_name(proto.new_holder());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(file, proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpUpdateBlocks(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpUpdateBlocks>(ctx->op);
  VLOG(9) << op->op_name() << ": block size " << op->blocks().size();
  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_)
  INode file;
  std::vector<INode> ancestors;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &file, &ancestors),
      StatusCode::kOK) << " op=" << op->op_name() << " path=" << op->path();
  CHECK_EQ(file.type(), INode_Type_kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_)

  INode old_file(file);
  BlockID last_blk_to_del = kInvalidBlockID;
  UpdateBlocks(op->path(),
               &file,
               op->blocks(),
               /*should_complete_last_block=*/false,
               ctx,
               &last_blk_to_del,
               true);

  // UpdateBlocks RPC only has inode_id

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file, ancestors);
  meta_storage_->OrderedUpdateINodeAndDeleteLastBlock(file,
                                                      last_blk_to_del,
                                                      &old_file,
                                                      op->txid(),
                                                      ctx->done,
                                                      STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpUpdateBlocksV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUpdateBlocksV2, BlocksToBeUpdate);

  METRIC_WATCH_START(op_update_blocks_get_last_inode_time_)
  INode file;
  std::vector<INode> ancestors;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &file, &ancestors),
      StatusCode::kOK);
  CHECK_EQ(file.type(), INode_Type_kFile);
  METRIC_WATCH_STOP(op_update_blocks_get_last_inode_time_)

  INode old_file(file);
  BlockID last_blk_to_del = kInvalidBlockID;
  UpdateBlocks(proto.path(),
               &file,
               { proto.inode().blocks().begin(),
                 proto.inode().blocks().end() },
               false,
               ctx,
               &last_blk_to_del,
               true);

  // UpdateBlocks RPC only has inode_id

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(file, proto.inode());
    CompareINodeFromMetaStorageAndEditLog(old_file, proto.old_inode());
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, file, ancestors);
  meta_storage_->OrderedUpdateINodeAndDeleteLastBlock(file,
                                                      last_blk_to_del,
                                                      &old_file,
                                                      op->txid(),
                                                      ctx->done,
                                                      STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpAllocateBlockId(std::shared_ptr<ApplyContext> ctx) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(WARNING)
        << "FLAGS_enable_fast_block_id_and_gs_gen=true, should not call "
           "ApplyOpAllocateBlockId";
  }

  auto op = std::static_pointer_cast<OpAllocateBlockId>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->blockId();
  char block_id[sizeof(uint64_t)];
  platform::WriteBigEndian(block_id, 0, op->blockId());
  meta_storage_->OrderedPutNameSystemInfo(
      kLastAllocatedBlockIdKey,
      cnetpp::base::StringPiece(block_id, sizeof(uint64_t)),
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyOpAllocateBlockIdV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAllocateBlockIdV2, BlockIdToBeAllocate);
  UpdateLastAllocatedBlockId(proto.new_block_id());
  meta_storage_->OrderedPutBlockId(proto.new_block_id(),
                                   op->txid(),
                                   ctx->done);
}

void NameSpace::ApplyOpSetXattr(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetXattr>(ctx->op);
  VLOG(1) << "op=" << op->op_name() << " path=" << op->xAttrs().src()
          << " xattr=" << op->xAttrs().ShortDebugString();

  METRIC_WATCH_START(op_set_xattr_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->xAttrs().src();
  CHECK_GT(op->xAttrs().xattrs_size(), 0);
  METRIC_WATCH_STOP(op_set_xattr_get_last_inode_time_)
  RepeatedPtrField<XAttrProto> xattrs = op->xAttrs().xattrs();
  for (const cloudfs::XAttrProto& x : xattrs) {
    CHECK(
        XAttrs::UpdateINodeXAttrs(x, true, true, &iip.MutableInode()).code() ==
        Code::kOK);
  }

  auto resp = std::make_shared<SetXAttrResponseProto>();
  AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));

  policy_manager_->ReloadINodeXAttr(
      op->xAttrs().src(), iip.Inode(), xattrs, {});
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetXattrV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetXAttrsV2, INodeToSetXAttrs);

  METRIC_WATCH_START(op_set_xattr_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  CHECK_GT(proto.xattrs_size(), 0);
  METRIC_WATCH_STOP(op_set_xattr_get_last_inode_time_)
  RepeatedPtrField<XAttrProto> xattrs = proto.xattrs();
  for (const cloudfs::XAttrProto& x : xattrs) {
    CHECK(
        XAttrs::UpdateINodeXAttrs(x, true, true, &iip.MutableInode()).code() ==
        Code::kOK);
  }

  if (proto.has_log_rpc_info()) {
    auto rpc_info = proto.log_rpc_info();
    auto resp = std::make_shared<SetXAttrResponseProto>();
    AddRetryCacheEntry(rpc_info.rpc_client_id(),
                       rpc_info.rpc_call_id(),
                       std::move(resp));
  }

  policy_manager_->ReloadINodeXAttr(proto.path(), iip.Inode(), xattrs, {});
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpRemoveXattr(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpRemoveXattr>(ctx->op);
  VLOG(9) << op->op_name() << ": xattr " << op->xAttrs().ShortDebugString();
  METRIC_WATCH_START(op_remove_xattr_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->xAttrs().src();
  CHECK(XAttrs::FilterXAttrs(op->xAttrs().xattrs(),
                             iip.MutableInode().mutable_xattrs()));
  METRIC_WATCH_STOP(op_remove_xattr_get_last_inode_time_)

  auto resp = std::make_shared<RemoveXAttrResponseProto>();
  AddRetryCacheEntry(op->clientId(), op->callId(), std::move(resp));

  policy_manager_->ReloadINodeXAttr(
      op->xAttrs().src(), iip.Inode(), {}, op->xAttrs().xattrs());
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpRemoveXattrV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRemoveXAttrsV2, INodeToRemoveXAttrs);

  METRIC_WATCH_START(op_remove_xattr_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  CHECK(XAttrs::FilterXAttrs(proto.xattrs(),
                             iip.MutableInode().mutable_xattrs()));
  METRIC_WATCH_STOP(op_remove_xattr_get_last_inode_time_)

  if (proto.has_log_rpc_info()) {
    auto rpc_info = proto.log_rpc_info();
    auto resp = std::make_shared<RemoveXAttrResponseProto>();
    AddRetryCacheEntry(rpc_info.rpc_client_id(),
                       rpc_info.rpc_call_id(),
                       std::move(resp));
  }

  policy_manager_->ReloadINodeXAttr(
      proto.path(), iip.Inode(), {}, proto.xattrs());
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetStoragePolicy(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetStoragePolicy>(ctx->op);
  VLOG(9) << op->op_name() << ": " << op->path()
          << " storage policy: " << std::to_string(op->policyId());

  METRIC_WATCH_START(op_set_storage_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_set_storage_policy_get_last_inode_time_)
  auto s =
      SetStoragePolicyInternal(op->path(), op->policyId(), &iip.MutableInode());
  CHECK(s.code() == Code::kOK);

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpSetStoragePolicyV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetStoragePolicyV2, INodeToSetStoragePolicy);

  METRIC_WATCH_START(op_set_storage_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  METRIC_WATCH_STOP(op_set_storage_policy_get_last_inode_time_)
  auto s = SetStoragePolicyInternal(
      proto.path(), proto.policy_id(), &iip.MutableInode());
  CHECK(s.code() == Code::kOK);

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

// Deprecate
void NameSpace::ApplyOpSetReplicaPolicy(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetReplicaPolicy>(ctx->op);
  VLOG(1) << "op=" << op->op_name() << " path=" << op->path()
          << " replica policy: " << std::to_string(op->id());

  METRIC_WATCH_START(op_set_replica_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_set_replica_policy_get_last_inode_time_)

  ReplicaPolicy policy;
  if (op->id() == kCentralizePolicy) {
    policy.set_distributed(false);
  } else if (op->id() == kCentralizePolicy) {
    policy.set_distributed(true);
  } else {
    LOG(FATAL) << "Invalid op->id()=" << op->id();
  }

  XAttrProto policy_xattr;
  XAttrs::BuildXAttr(
      kReplicaPolicyXAttr, policy.SerializeAsString(), &policy_xattr);

  auto s =
      XAttrs::UpdateINodeXAttrs(policy_xattr, true, true, &iip.MutableInode());
  CHECK(s.IsOK()) << s.ToString();

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
  policy_manager_->ReplicaPolicyInstance()->UpdatePolicy(op->path(), policy);
  LOG(ERROR) << "Should Deprecated";
}

void NameSpace::ApplyOpSetReplicaPolicyV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetReplicaPolicyV2, INodeToSetReplicaPolicy);

  METRIC_WATCH_START(op_set_replica_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  METRIC_WATCH_STOP(op_set_replica_policy_get_last_inode_time_)

  ReplicaPolicy policy;
  if (proto.policy_id() == kCentralizePolicy) {
    policy.set_distributed(false);
  } else if (proto.policy_id() == kCentralizePolicy) {
    policy.set_distributed(true);
  } else {
    LOG(FATAL) << "Invalid proto.policy_id()=" << proto.policy_id();
  }

  XAttrProto policy_xattr;
  XAttrs::BuildXAttr(
      kReplicaPolicyXAttr, policy.SerializeAsString(), &policy_xattr);

  auto s =
      XAttrs::UpdateINodeXAttrs(policy_xattr, true, true, &iip.MutableInode());
  CHECK(s.IsOK());

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
  policy_manager_->ReplicaPolicyInstance()->UpdatePolicy(proto.path(), policy);
}

void NameSpace::ApplyOpSetDirReplicaPolicy(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetDirReplicaPolicy>(ctx->op);
  VLOG(1) << "op=" << op->op_name() << " path=" << op->path()
          << " dir replica policy: id=" << std::to_string(op->id())
          << " dc=" << op->dc();

  METRIC_WATCH_START(op_set_dir_replica_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << op->path();
  METRIC_WATCH_STOP(op_set_dir_replica_policy_get_last_inode_time_)

  if (op->id() == kNonePolicy) {
    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(kReplicaPolicyXAttr, "", &policy_xattr);
    // backwards compatible
    XAttrs::BuildXAttr(kReplicaPolicyTypeXAttr, "", &policy_xattr);
    XAttrs::BuildXAttr(kReplicaPolicyDCXAttr, "", &policy_xattr);

    RepeatedPtrField<XAttrProto> to_filter;
    to_filter.Add()->CopyFrom(policy_xattr);

    XAttrs::FilterXAttrs(to_filter, iip.MutableInode().mutable_xattrs());

    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);
    meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                      nullptr,
                                      inode_snaplog,
                                      op->txid(),
                                      ctx->done);

    policy_manager_->ReplicaPolicyInstance()->RemovePolicy(op->path());
  } else {
    ReplicaPolicy policy;
    policy.set_distributed(op->id() == kDistributePolicy);
    auto dcs = cnetpp::base::StringUtils::SplitByChars(op->dc(), ",");
    for (const auto& d : dcs) {
      policy.add_dc(d);
    }

    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(
        kReplicaPolicyXAttr, policy.SerializeAsString(), &policy_xattr);

    auto s = XAttrs::UpdateINodeXAttrs(
        policy_xattr, true, true, &iip.MutableInode());
    CHECK(s.IsOK()) << s.ToString();

    SnapshotLog inode_snaplog;
    iip.GenerateSnapshotLog(&inode_snaplog);
    meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                      nullptr,
                                      inode_snaplog,
                                      op->txid(),
                                      ctx->done);
    policy_manager_->ReplicaPolicyInstance()->UpdatePolicy(op->path(), policy);
  }
}

void NameSpace::ApplyOpSetDirReplicaPolicyV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetDirReplicaPolicyV2, DirToSetReplicaPolicy);

  METRIC_WATCH_START(op_set_dir_replica_policy_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK);
  METRIC_WATCH_STOP(op_set_dir_replica_policy_get_last_inode_time_)

  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  if (proto.policy_id() == kNonePolicy) {
    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(kReplicaPolicyXAttr, "", &policy_xattr);
    // backwards compatible
    XAttrs::BuildXAttr(kReplicaPolicyTypeXAttr, "", &policy_xattr);
    XAttrs::BuildXAttr(kReplicaPolicyDCXAttr, "", &policy_xattr);

    RepeatedPtrField<XAttrProto> to_filter;
    to_filter.Add()->CopyFrom(policy_xattr);

    XAttrs::FilterXAttrs(to_filter, iip.MutableInode().mutable_xattrs());

    meta_storage_->OrderedUpdateINode(
        &iip.MutableInode(), nullptr, inode_snaplog, op->txid(), ctx->done);

    policy_manager_->ReplicaPolicyInstance()->RemovePolicy(proto.path());
  } else {
    ReplicaPolicy policy;
    policy.set_distributed(proto.policy_id() == kDistributePolicy);
    auto dcs = cnetpp::base::StringUtils::SplitByChars(proto.dc(), ",");
    for (const auto& d : dcs) {
      policy.add_dc(d);
    }

    XAttrProto policy_xattr;
    XAttrs::BuildXAttr(
        kReplicaPolicyXAttr, policy.SerializeAsString(), &policy_xattr);

    auto s = XAttrs::UpdateINodeXAttrs(
        policy_xattr, true, true, &iip.MutableInode());
    CHECK(s.IsOK());

    meta_storage_->OrderedUpdateINode(
        &iip.MutableInode(), nullptr, inode_snaplog, op->txid(), ctx->done);
    policy_manager_->ReplicaPolicyInstance()->UpdatePolicy(proto.path(),
                                                           policy);
  }
}

void NameSpace::ApplyOpAccessCounterSnapshot(
    std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAccessCounterSnapshot>(ctx->op);
  VLOG(9) << op->op_name() << " " << op->snapshot().path() << " "
             << op->snapshot().DebugString() << " txid=" << op->txid();
  // access_counter_manager_->ApplySnapshot(op->snapshot());
  meta_storage_->PushINodeTXWriteTask(nullptr, op->txid(), {}, nullptr, ctx->done);
}

void NameSpace::ApplyOpSetBlockInfo(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpSetBlockPufsInfo>(ctx->op);
  DLOG(INFO) << op->op_name() << " " << op->s() << " txid=" << op->txid();
  BlockPufsInfo block_pufs_info;
  if (!block_pufs_info.DeserializeFromJsonString(op->s())) {
    LOG(FATAL) << "NotDeserializable " << op->s();
  }
  BlockInfoProto bip = block_pufs_info.GetBlockInfoProto();
  CHECK(bip.state() == BlockInfoProto::kUploadIssued ||
        bip.state() == BlockInfoProto::kPersisted)
      << PBConverter::ToCompactJsonString(bip);
  if (bip.state() == BlockInfoProto::kUploadIssued) {
    meta_storage_->PutBlockInfo(bip, nullptr, op->txid(), ctx->done);
  } else {
    ctx->done->add_post_callback([this, bip](const Status& ignored) {
      Block blk{/*id*/ bip.block_id(),
                /*num_bytes*/ static_cast<uint32_t>(bip.num_bytes()),
                /*gs*/ bip.gen_stamp()};
      if (!block_manager_->PersistBlock(blk)) {
        block_manager_->EnqueuePendingPersistedBlks(blk);
      }
    });
    meta_storage_->MoveBlockInfoToPersisted(
        bip, nullptr, op->txid(), ctx->done);
  }
}

void NameSpace::ApplyOpMergeBlock(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpMergeBlock, FileAndBlockToBeMerge);

  INodeInPath iip;
  std::vector<INode> ancestors;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip, &ancestors), StatusCode::kOK);
  auto& old_inode = iip.MutableInode();
  VLOG(8) << old_inode.ShortDebugString();
  const INode& inode = op->GetProto().inode();
  CHECK_EQ(old_inode.id(), inode.id());
  old_inode.CopyFrom(inode);
  const BlockInfoProto& bip = op->GetProto().merged_bip();
  std::vector<BlockProto> depre_blks;
  depre_blks.resize(op->GetProto().depred_blks_size());
  for (size_t i = 0; i < op->GetProto().depred_blks_size(); i ++) {
    depre_blks[i].CopyFrom(op->GetProto().depred_blks(i));
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  //
  // When applying edit logs to create new blocks (e.g., add or merge block edit
  // logs), make the in-memory modifications before updating on-disk. This
  // prevents IBR/FBR from loading the block from disk, which could result in
  // the block being loaded twice — once for IBR/FBR and once for applying edit
  // logs - leading to a potential core dump at BlockMapSlice::LoadBlock. For
  // modifying existing blocks, we can perform the modifications in
  // apply_threads_[0] or callback threads. Using callback threads is more
  // efficient.
  {
    {
      VLOG(8) << "UpdateMergedBlock " << inode.id() << " B" << bip.block_id()
              << " state " << bip.state();

      UpdateLastAllocatedBlockId(bip.block_id());
      UpdateGenerationStampV2(bip.gen_stamp());

      // Hdfs mode and Acc mode have different logic for block maanger
      if (NameSpace::IsHdfsMode()) {
        BlockUCState uc_state;
        switch (bip.state()) {
          case BlockInfoProto::kUnderConstruction: {
            uc_state = BlockUCState::kUnderConstruction;
            block_manager_->LoadBlock(
                inode.id(),
                inode.parent_id(),
                static_cast<uint8_t>(inode.replication()),
                bip.write_mode(),
                Block(bip.block_id(),
                      static_cast<uint32_t>(bip.num_bytes()),
                      bip.gen_stamp()),
                /*dn_id*/std::vector<DatanodeID>(),
                uc_state);
          } break;
          case BlockInfoProto::kComplete: {
            uc_state = BlockUCState::kComplete;
            block_manager_->UpdateMergeBlockUCState(
                Block{bip.block_id(),
                      static_cast<uint32_t>(bip.num_bytes()),
                      bip.gen_stamp()},
                uc_state);
          } break;
          case BlockInfoProto::kPersisted: {
            uc_state = BlockUCState::kPersisted;
            block_manager_->UpdateMergeBlockUCState(
                Block{bip.block_id(),
                      static_cast<uint32_t>(bip.num_bytes()),
                      bip.gen_stamp()},
                uc_state);
          } break;
          default:
            LOG_WITH_LEVEL(ERROR)
                << "Unexpected bip state " << bip.ShortDebugString();
            MFC(LoggerMetrics::Instance().error_)->Inc();
            break;
        }
      }

      // ACC only support merge persisted blocks
      if (NameSpace::IsAccMode()) {
        switch(bip.state()) {
          case BlockInfoProto::kPersisted: {
            // Do nothing
            break;
          }
          default:
            LOG_WITH_LEVEL(ERROR)
                << "Unexpected bip state " << bip.ShortDebugString();
            MFC(LoggerMetrics::Instance().error_)->Inc();
            break;
        }
      }
    }
  }

  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_inode, op->GetProto().inode(), ancestors);
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);
  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_old_inode());
    if (IsHdfsMode()) {
      CHECK_GT(proto.ancestors_id_size(), 0);
      CompareAncestors(ancestors, proto.ancestors_id());
    }
    CompareINodeFromMetaStorageAndEditLog(old_inode, proto.old_inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }
  meta_storage_->OrderedUpdateINodeMergeBlock(&iip.MutableInode(),
                                              nullptr,
                                              bip,
                                              depre_blks,
                                              inode_snaplog,
                                              op->txid(),
                                              ctx->done,
                                              STAT_RECORDER_PTR);
}

void NameSpace::ApplyOpApproveUploadBlk(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpApproveUploadBlk, BlkToBeApproveUpload);
  meta_storage_->PutBlockInfo(proto.bip(),
                              proto.has_old_bip() ? &proto.old_bip() : nullptr,
                              op->txid(),
                              ctx->done);
}

void NameSpace::ApplyOpPersistBlk(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpPersistBlk, BlkToBePersist);
  const auto& bip = proto.bip();
  ctx->done->add_post_callback([this, bip](const Status& ignored) {
    Block blk(bip.block_id(),
              static_cast<uint32_t>(bip.num_bytes()),
              bip.gen_stamp());
    if (!block_manager_->PersistBlock(blk)) {
      block_manager_->EnqueuePendingPersistedBlks(blk);
    }
  });
  meta_storage_->MoveBlockInfoToPersisted(
      bip,
      proto.has_old_bip() ? &proto.old_bip() : nullptr,
      op->txid(),
      ctx->done);
}

// TODO(ruanjunbin): test.
void NameSpace::ApplyOpDeleteDeprecatedBlockPufsInfo(
    std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpDeleteDeprecatedBlockPufsInfo>(ctx->op);
  DLOG(INFO) << op->op_name() << " " << op->s() << " txid=" << op->txid();
  BlockID blk_id = kInvalidBlockID;
  try {
    blk_id = std::stoull(op->s());
  } catch (...) {
    LOG(FATAL) << "Can't deserialize " << op->s();
  }
  ctx->done->add_post_callback([this, blk_id](const Status& ignored) {
    block_manager_->RemoveBlock(blk_id, false);
  });
  meta_storage_->DeleteBlockInfo(blk_id, op->txid(), ctx->done);
}

void NameSpace::ApplyOpDelDepringBlks(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDelDepringBlks, DepringBlksToBeDel);
  // TODO(ruanjunbin)
  // MetaStorage doesn't promise post callback will be called in order of txid.
  // 1. So do we need to take locks of BlockMapSlice in apply thread?
  //    And release them in post callback?
  // 2. Don't let Standby NN maintain BlockInfo in memory?
  ctx->done->add_post_callback([this, op](const Status& ignored) {
    for (BlockID blk_id : op->GetProto().dangling_blk_ids()) {
      block_manager_->RemoveBlock(blk_id, false);
    }
    block_manager_->RemoveBlocksAndUpdateSafeMode(
        std::vector<BlockInfoProto>(op->GetProto().depred_bips().begin(),
                                    op->GetProto().depred_bips().end()));
  });
  meta_storage_->DelDepringBlks(op->GetProto(), op->txid(), { ctx->done });
}

void NameSpace::ApplyOpDelDepredBlks(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDelDepredBlks, DepredBlksToBeDel);
  meta_storage_->DelDepredBlks(op->GetProto(), op->txid(), ctx->done);
}

void NameSpace::ApplyOpSetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetLifecyclePolicy, LifecyclePolicyToBeSet);

  INode inode;
  auto st = PrepareSetLifecyclePolicy(proto.path(),
                                      ctx->src_path_components,
                                      proto.policy(),
                                      &inode);
  CHECK(st.code() == Code::kOK);
  CHECK(!st.HasException());
  CHECK_EQ(inode.id(), proto.inode_id());

  if (proto.has_log_rpc_info()) {
    auto rpc_info = proto.log_rpc_info();
    auto resp = std::make_shared<SetLifecyclePolicyResponseProto>();
    AddRetryCacheEntry(rpc_info.rpc_client_id(),
                       rpc_info.rpc_call_id(),
                       std::move(resp));
  }

  meta_storage_->OrderedPutLifecyclePolicy(proto.inode_id(),
                                           proto.timestamp_ms(),
                                           proto.policy(),
                                           op->txid(),
                                           ctx->done);
}

void NameSpace::ApplyOpUnsetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUnsetLifecyclePolicy, LifecyclePolicyToBeUnset);

  if (proto.has_log_rpc_info()) {
    auto rpc_info = proto.log_rpc_info();
    auto resp = std::make_shared<UnsetLifecyclePolicyResponseProto>();
    AddRetryCacheEntry(rpc_info.rpc_client_id(),
                       rpc_info.rpc_call_id(),
                       std::move(resp));
  }

  meta_storage_->OrderedDeleteLifecyclePolicy(proto.inode_id(),
                                              op->txid(),
                                              ctx->done);
}

void NameSpace::ApplyOpSetAZBlacklist(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetAZBlacklist, AZBlacklist);

  meta_storage_->PutAZBlacklistAsync(proto.azs(), op->txid(), ctx->done);
}

void NameSpace::ApplyOpFlushBlockInfoProtos(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpFlushBlockInfoProtos>(ctx->op);
  DLOG(INFO) << op->op_name() << " txid=" << op->txid();
  meta_storage_->FlushBlockInfoProtos(op->GetProto(), op->txid(), ctx->done);
}

void NameSpace::ApplyOpUpdateATimeProtos(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpUpdateATimeProtos>(ctx->op);
  DLOG(INFO) << op->op_name() << " txid=" << op->txid();
  meta_storage_->UpdateTtlATimes(op->GetProto(), op->txid(), ctx->done);
}

void NameSpace::ApplyOpNop(std::shared_ptr<ApplyContext> ctx) {
  auto op = ctx->op;
  VLOG(9) << op->op_name() << ": txid=" << op->txid();
  CHECK(
    op->op_code() == OP_START_LOG_SEGMENT ||
    op->op_code() == OP_END_LOG_SEGMENT);
  meta_storage_->PushINodeTXWriteTask(nullptr, op->txid(), {}, nullptr, ctx->done);

  // If the writer thead is sleeping, while it use to be Active,
  // so we need to wakeup it first.
  meta_storage_->NotifyForActive(op->txid());
}

void NameSpace::ApplyOpDummy(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAccSyncDummy>(ctx->op);
  DLOG(INFO) << op->op_name() << ": txid=" << op->txid();
  CHECK(op->GetCfsOpCode() == CfsOpCode::kAccSyncDummy) << op->txid();
  meta_storage_->PushINodeTXWriteTask(
      nullptr, op->txid(), NameSpaceInfoDelta{}, nullptr, ctx->done);
}

void NameSpace::ApplyOpAllowSnapshotV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAllowSnapshotV2, SnapshotToAllow);
  const auto& path = proto.path();

  INode inode;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &inode),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << path;
  inode.set_is_snapshottable(true);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_inode());
    CompareINodeFromMetaStorageAndEditLog(inode, proto.inode());
  }

  // copy path and id because callback exceeds their lifecycle
  ctx->done->set_callback([this, id = inode.id(), path](const Status& status) {
    CHECK(status.IsOK());
    auto s = snapshot_manager_->AllowSnapshot(id, path);
    CHECK(s.IsOK()) << "snapshot_manager_ AllowSnapshot failed: "
                    << s.ToString();
  });
  meta_storage_->OrderedUpdateINodeAndSnapshotData(inode,
                                                   MetaStorage::ALLOW_SNAPSHOT,
                                                   &path,
                                                   nullptr /*snapshot_id*/,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyOpDisallowSnapshotV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDisallowSnapshotV2, SnapshotToDisallow);
  const auto& path = proto.path();

  INode inode;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &inode),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << path;
  inode.set_is_snapshottable(false);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_inode());
    CompareINodeFromMetaStorageAndEditLog(inode, proto.inode());
  }

  ctx->done->set_callback([this, id = inode.id(), path](const Status& status) {
    CHECK(status.IsOK());
    auto s = snapshot_manager_->DisallowSnapshot(id, path);
    CHECK(s.IsOK()) << "snapshot_manager_ DisallowSnapshot failed: "
                    << s.ToString();
  });
  meta_storage_->OrderedUpdateINodeAndSnapshotData(
      inode,
      MetaStorage::DISALLOW_SNAPSHOT,
      nullptr /*src*/,
      nullptr /*snapshot_id*/,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyOpCreateSnapshotV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpCreateSnapshotV2, SnapshotToCreate);
  const auto& path = proto.path();
  const auto& name = proto.name();
  uint64_t mtime = proto.timestamp_in_ms();

  INode inode;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &inode),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << path << ", name=" << name;

  uint64_t new_snapshot_id = 0;
  if (proto.has_new_snapshot_id()) {
    // XXX try best to rectify bug of M-4679216655
    new_snapshot_id = proto.new_snapshot_id();
  } else {
    RETURN_SET_CLOSURE_IF_ERROR(
        snapshot_manager_->AcquireSnapshotID(&new_snapshot_id), ctx->done);
  }

  inode.set_mtime(mtime);
  auto* new_snapshot = inode.mutable_snapshots()->Add();
  new_snapshot->set_snapshot_id(new_snapshot_id);
  new_snapshot->set_name(name);
  new_snapshot->set_create_txid(op->txid());
  new_snapshot->set_deleted(false);
  new_snapshot->set_mtime(mtime);
  VLOG(10) << "Create snapshot " << name << ", id=" << new_snapshot_id;

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_inode());
    CHECK(proto.has_new_snapshot_id());
    CompareINodeFromMetaStorageAndEditLog(inode, proto.inode());
    CHECK_EQ(new_snapshot_id, proto.new_snapshot_id())
        << absl::StrFormat(
            "new-snapshot-id acquired is inconsistent between Active(%lu) "
            "and Standby(%lu), which means rocksdb of Standby shall be "
            "reconstructed from Active! see M-4679216655 for more info.",
            proto.new_snapshot_id(), new_snapshot_id);
  }

  meta_storage_->OrderedUpdateINodeAndSnapshotData(inode,
                                                   MetaStorage::CREATE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   &new_snapshot_id,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyOpDeleteSnapshotV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDeleteSnapshotV2, SnapshotToDelete);
  const auto& path = proto.path();
  const auto& name = proto.name();
  uint64_t mtime = proto.timestamp_in_ms();

  INode inode;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &inode),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << path << ", name=" << name;

  for (auto& snapshot : *inode.mutable_snapshots()) {
    if (snapshot.name() == name) {
      snapshot.set_deleted(true);
      break;
    }
  }
  inode.set_mtime(mtime);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_inode());
    CompareINodeFromMetaStorageAndEditLog(inode, proto.inode());
  }

  meta_storage_->OrderedUpdateINodeAndSnapshotData(inode,
                                                   MetaStorage::DELETE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   nullptr /*snapshot_id*/,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyOpRenameSnapshotV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameSnapshotV2, SnapshotToRename);
  const auto& path = proto.path();
  const auto& old_name = proto.old_name();
  const auto& new_name = proto.new_name();
  uint64_t mtime = proto.timestamp_in_ms();

  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << path
      << ", old_name=" << old_name << ", new_name=" << new_name;

  bool target_snapshot_found = false;
  for (auto& snapshot : *iip.MutableInode().mutable_snapshots()) {
    if (snapshot.has_deleted() && snapshot.deleted()) {
      continue;
    }
    CHECK_NE(snapshot.name(), new_name) << iip.DebugINodeStr();
    if (snapshot.name() == old_name) {
      CHECK(!target_snapshot_found) << iip.DebugINodeStr();
      target_snapshot_found = true;
      snapshot.set_name(new_name);
    }
  }
  iip.SetModificationTime(mtime);
  CHECK(target_snapshot_found) << iip.DebugINodeStr();
  CHECK(!iip.HasSnapshotINode())
      << "found nested snapshot root " << iip.DebugINodeStr();
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  if (ctx->logical_apply_check_physical_log) {
    CHECK(proto.has_inode());
    CompareINodeFromMetaStorageAndEditLog(iip.Inode(), proto.inode());
    CompareSnapshotLog(inode_snaplog, proto.inode_snaplog());
  }

  meta_storage_->OrderedUpdateINode(&iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyOpPin(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpPin>(ctx->op);
  const auto& proto = op->GetProto();
  VLOG(9) << "op=" << op->op_name() << " path=" << proto.path()
          << " inode=" << proto.inode().ShortDebugString();
  METRIC_WATCH_START(op_pin_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  METRIC_WATCH_STOP(op_pin_get_last_inode_time_)

  INode& inode = iip.MutableInodeUnsafe();
  inode.CopyFrom(proto.inode());
  if (!proto.has_update_txid() || proto.update_txid()) {
    inode.mutable_pin_status()->set_txid(op->txid());
  }
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  JobInfoOpBody job;
  if (proto.has_job()) {
    job.CopyFrom(proto.job());
  }

  ManagedJobId cancel_job_id;
  if (proto.has_cancel_job_id()) {
    cancel_job_id = proto.cancel_job_id();
  }

  meta_storage_->PinINode(&iip.MutableInode(),
                          proto.has_old_inode() ? &proto.old_inode() : nullptr,
                          inode_snaplog,
                          job,
                          cancel_job_id,
                          op->txid(),
                          ctx->done);

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);
  auto resp = std::make_shared<cloudfs::PinResponseProto>();
  resp->set_result(true);
  AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
}

void NameSpace::ApplyOpReconcileINodeAttrs(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpReconcileINodeAttrs>(ctx->op);
  const auto& proto = op->GetProto();
  VLOG(9) << "op=" << op->op_name() << " path=" << proto.path()
          << " inode=" << proto.inode().ShortDebugString();
  METRIC_WATCH_START(op_reconcile_inode_attrs_get_last_inode_time_)
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(ctx->src_path_components, &iip), StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.path();
  METRIC_WATCH_STOP(op_reconcile_inode_attrs_get_last_inode_time_)

  SnapshotLog inode_snaplog;

  std::vector<ManagedJobId> cancel_job_id;
  for(auto& job : proto.cancel_job_id()) {
    cancel_job_id.emplace_back(job);
  }

  std::set<int64_t> expired_ttl;
  for (auto t : proto.expired_ttl()) {
    expired_ttl.insert(t);
  }
  std::set<int64_t> new_ttl;
  for (auto t : proto.new_ttl()) {
    new_ttl.insert(t);
  }

  meta_storage_->ReconcileINodeAttrs(
      &iip.MutableInode(),
      proto.has_old_inode() ? &proto.old_inode() : nullptr,
      inode_snaplog,
      cancel_job_id,
      expired_ttl,
      new_ttl,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyOpPersistJobInfo(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpPersistJobInfo>(ctx->op);
  const auto& proto = op->GetProto();
  meta_storage_->PutJobInfo(proto, op->txid(), ctx->done);
}

void NameSpace::CheckDBINodeForPhysicalApply(
    const std::string& path,
    const INode& old_inode_from_editlog) {
  std::vector<cnetpp::base::StringPiece> path_comps;
  CHECK(SplitPath(path, &path_comps));
  CHECK(VerifyPath(path, path_comps).IsOK());
  INodeInPath iip;
  CHECK_EQ(GetLastINodeInPath(path_comps, &iip), StatusCode::kOK);
  CompareINodeFromMetaStorageAndEditLog(iip.Inode(), old_inode_from_editlog);
}

#define REGISTER_CB_EXEC_TIME_METRICS(op)                                     \
  auto _mitr = metrics_.edit_callback_exec_time_.find((op)->op_name());       \
  CHECK(_mitr != metrics_.edit_callback_exec_time_.end());                    \
  StopWatch _sw(_mitr->second);                                               \
  _sw.Start()

void NameSpace::ApplyCfsOpMkdir(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpMkdirV2, DirToBeMake);

  // 1. check editlog
  CHECK_EQ(proto.inode().parent_id(), proto.parent().id());

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    UpdateLastINodeId(proto.inode().id());

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<MkdirsResponseProto>();
      resp->set_result(true);
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_ADD(ctx, proto.inode(), proto.ancestors_id());
  std::vector<INode*> inodes_add = { proto.mutable_inode() };
  std::vector<INodeAndSnapshot> parents;
  parents.emplace_back(proto.mutable_parent(),
                       proto.mutable_parent_snaplog());
  meta_storage_->OrderedCommitINodes(&inodes_add,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &parents,
                                     nullptr,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpDelete(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDeleteV2, INodeToBeDelete);

  // 1. check editlog
  CHECK_EQ(proto.inode().parent_id(), proto.parent().id());
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    const INode& inode = proto.inode();
    if (inode.type() == INode_Type_kFile && inode.has_uc()) {
      RemoveLease(inode);
    }

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<DeleteResponseProto>();
      resp->set_result(true);
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_DELETE(ctx, proto.inode(), proto.ancestors_id());
  std::vector<INodeAndSnapshot> inodes_del, parents;
  std::vector<INode> inodes_old;
  inodes_del.emplace_back(proto.mutable_inode(),
                          proto.mutable_inode_snaplog());
  parents.emplace_back(proto.mutable_parent(),
                       proto.mutable_parent_snaplog());
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = proto.inode();
  meta_storage_->OrderedCommitINodes(nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &inodes_del,
                                     &parents,
                                     &inodes_old,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpRenameOld(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameOldV2, INodeToBeRenameOld);

  // 1. check editlog
  CHECK_EQ(proto.src_inode().id(), proto.dst_inode().id());
  CHECK_EQ(proto.src_inode().parent_id(), proto.src_parent().id());
  CHECK_EQ(proto.dst_inode().parent_id(), proto.dst_parent().id());
  CHECK_GT(proto.src_ancestors_id_size(), 0);
  CHECK_GT(proto.dst_ancestors_id_size(), 0);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.src_path(), proto.src_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<DeleteResponseProto>();
      resp->set_result(true);
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.src_inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_RENAME(ctx,
                             proto.src_inode(),
                             proto.dst_inode(),
                             proto.src_ancestors_id(),
                             proto.dst_ancestors_id());
  std::vector<INodeAndSnapshot> inodes_mov_src, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  inodes_mov_src.emplace_back(proto.mutable_src_inode(),
                              proto.mutable_src_inode_snaplog());
  inodes_mov_dst.emplace_back(proto.mutable_dst_inode());
  parents.emplace_back(proto.mutable_src_parent(),
                       proto.mutable_src_parent_snaplog());
  parents.emplace_back(proto.mutable_dst_parent(),
                       proto.mutable_dst_parent_snaplog());
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = proto.src_inode();
  meta_storage_->OrderedCommitINodes(nullptr,
                                     nullptr,
                                     &inodes_mov_src,
                                     &inodes_mov_dst,
                                     nullptr,
                                     &parents,
                                     &inodes_old,
                                     {},
                                     {},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpRename(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameV2, INodeToBeRename);

  // 1. check editlog
  CHECK_EQ(proto.src_inode().id(), proto.dst_inode().id());
  CHECK_EQ(proto.src_inode().parent_id(), proto.src_parent().id());
  CHECK_EQ(proto.dst_inode().parent_id(), proto.dst_parent().id());
  CHECK_GT(proto.src_ancestors_id_size(), 0);
  CHECK_GT(proto.dst_ancestors_id_size(), 0);
  if (proto.has_overwrite_inode()) {
    if (proto.move_to_recycle_bin()) {
      CHECK(proto.has_rb_path());
      CHECK(proto.has_rb_inode());
      CHECK(proto.has_rb_parent());
      CHECK_EQ(proto.rb_inode().id(), proto.overwrite_inode().id());
      CHECK_EQ(proto.rb_inode().parent_id(), proto.rb_parent().id());
      CHECK_GT(proto.rb_ancestors_id_size(), 0);
    } else {
      CHECK(!proto.has_rb_path());
      CHECK(!proto.has_rb_inode());
      CHECK(!proto.has_rb_parent());
      CHECK_EQ(proto.rb_ancestors_id_size(), 0);
    }
  } else {
    CHECK(!proto.move_to_recycle_bin());
    CHECK(!proto.has_rb_path());
    CHECK(!proto.has_rb_inode());
    CHECK(!proto.has_rb_parent());
    CHECK_EQ(proto.rb_ancestors_id_size(), 0);
  }
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.src_path(), proto.src_inode());
    if (proto.has_overwrite_inode()) {
      CheckDBINodeForPhysicalApply(proto.dst_path(), proto.overwrite_inode());
    }
  }

  // 2. set callback
  std::vector<Closure*> dones;
  if (proto.has_overwrite_inode() && !proto.move_to_recycle_bin()) {
    auto cb = [this, op, &proto] (const Status& s) {
      StopWatch sw(metrics_.edit_rename_overwrite_callback_exec_time_);
      sw.Start();
      CHECK(s.IsOK());
      const INode& inode_del =  proto.overwrite_inode();
      if (inode_del.type() == INode_Type_kFile && inode_del.has_uc()) {
        RemoveLease(inode_del);
      }
    };
    ApplyClosure* inode_del_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx,
                           inode_del_closure,
                           proto.overwrite_inode().id(),
                           cb);
    dones.emplace_back(inode_del_closure);
  }
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<DeleteResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyClosure* inode_mov_closure = new ApplyClosure();
  ApplyCfsOpSetupClosure(ctx,
                         inode_mov_closure,
                         proto.src_inode().id(),
                         cb);
  dones.emplace_back(inode_mov_closure);
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [=] (const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  std::vector<INodeAndSnapshot> inodes_mov_src, inodes_del, parents;
  std::vector<INode*> inodes_mov_dst;
  std::vector<INode> inodes_old;
  if (proto.has_overwrite_inode()) {
    if (proto.move_to_recycle_bin()) {
      STAT_RECORDER_INODE_RENAME(ctx,
                                 proto.overwrite_inode(),
                                 proto.rb_inode(),
                                 proto.dst_ancestors_id(),
                                 proto.rb_ancestors_id());
      inodes_mov_src.emplace_back(proto.mutable_overwrite_inode(),
                                  proto.mutable_old_dst_inode_snaplog());
      inodes_mov_dst.emplace_back(proto.mutable_rb_inode());
      parents.emplace_back(proto.mutable_rb_parent(),
                           proto.mutable_rb_parent_snaplog());
    } else {
      STAT_RECORDER_INODE_DELETE(ctx,
                                 proto.overwrite_inode(),
                                 proto.dst_ancestors_id());
      inodes_del.emplace_back(proto.mutable_overwrite_inode(),
                              proto.mutable_old_dst_inode_snaplog());
    }
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = proto.overwrite_inode();
  }
  inodes_old.resize(inodes_old.size() + 1);
  inodes_old.back() = proto.src_inode();
  STAT_RECORDER_INODE_RENAME(ctx,
                             proto.src_inode(),
                             proto.dst_inode(),
                             proto.src_ancestors_id(),
                             proto.dst_ancestors_id());
  inodes_mov_src.emplace_back(proto.mutable_src_inode(),
                              proto.mutable_src_inode_snaplog());
  inodes_mov_dst.emplace_back(proto.mutable_dst_inode());
  parents.emplace_back(proto.mutable_src_parent(),
                       proto.mutable_src_parent_snaplog());
  parents.emplace_back(proto.mutable_dst_parent(),
                       proto.mutable_dst_parent_snaplog());
  meta_storage_->OrderedCommitINodes(nullptr,
                                     nullptr,
                                     &inodes_mov_src,
                                     &inodes_mov_dst,
                                     &inodes_del,
                                     &parents,
                                     &inodes_old,
                                     {},
                                     {},
                                     op->txid(),
                                     dones,
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpSymlink(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "unsupported symlink editlog";
}

void NameSpace::ApplyCfsOpOpenFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpOpenFile, FileToBeOpen);

  // 1. check editlog
  CHECK_EQ(proto.inode().parent_id(), proto.parent().id());
  if (IsHdfsMode()) {
    CHECK_GT(proto.ancestors_id_size(), 0);
  }
  if (proto.has_overwrite_inode()) {
    CHECK(proto.overwrite());
    if (proto.move_to_recycle_bin()) {
      CHECK(proto.has_rb_path());
      CHECK(proto.has_rb_inode());
      CHECK(proto.has_rb_parent());
      CHECK_EQ(proto.rb_inode().id(), proto.overwrite_inode().id());
      CHECK_EQ(proto.rb_inode().parent_id(), proto.rb_parent().id());
      CHECK_GT(proto.rb_ancestors_id_size(), 0);
    } else {
      CHECK(!proto.has_rb_path());
      CHECK(!proto.has_rb_inode());
      CHECK(!proto.has_rb_parent());
      CHECK_EQ(proto.rb_ancestors_id_size(), 0);
    }
  } else {
    CHECK(!proto.move_to_recycle_bin());
    CHECK(!proto.has_rb_path());
    CHECK(!proto.has_rb_inode());
    CHECK(!proto.has_rb_parent());
    CHECK_EQ(proto.rb_ancestors_id_size(), 0);
  }
  if (ctx->physical_apply_check_db) {
    if (proto.has_overwrite_inode()) {
      CheckDBINodeForPhysicalApply(proto.path(), proto.overwrite_inode());
    }
  }

  // 2. set callback
  std::vector<Closure*> dones;
  if (proto.has_overwrite_inode() && !proto.move_to_recycle_bin()) {
    auto cb = [this, op, &proto] (const Status& s) {
      StopWatch sw(metrics_.edit_openfile_overwrite_callback_exec_time_);
      sw.Start();
      CHECK(s.IsOK());
      auto& inode_del = proto.overwrite_inode();
      if (inode_del.type() == INode_Type_kFile && inode_del.has_uc()) {
        RemoveLease(inode_del);
      }
    };
    ApplyClosure* inode_del_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx,
                           inode_del_closure,
                           proto.overwrite_inode().id(),
                           cb);
    dones.emplace_back(inode_del_closure);
  }
  auto cb = [this, op, &proto](const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    auto& inode_add = proto.inode();

    // Cannot use ctx->done->add_post_callback because we want this logic to be
    // executed before any callbacks are triggered
    // Add task to writeback manager for ACC mode
    if (NameSpace::IsAccMode() && !FLAGS_enable_write_back_task_persistence) {
      CheckAccINode(inode_add);
      ufs_env_->upload_monitor()->AddTask(
          inode_add.id(),
          inode_add.ufs_file_info().key(),
          inode_add.ufs_file_info().upload_id());
    }

    UpdateLastINodeId(inode_add.id());
    AddLease(inode_add);

    std::vector<BlockInfoProto> add_block_bips;
    std::vector<std::vector<DatanodeID>> bips_expected_locs;
    std::vector<Block> future_blks;
    if (proto.add_block_bips_with_locs_size() == 0) {
      for (const auto& add_bip : proto.add_block_bips()) {
        add_block_bips.emplace_back(add_bip);
        bips_expected_locs.emplace_back(std::vector<DatanodeID>());
      }
    } else {
      for (const auto& add_bip_with_locs : proto.add_block_bips_with_locs()) {
        add_block_bips.emplace_back(add_bip_with_locs.bip());
        std::vector<DatanodeID> dn_ids;
        for (const auto& dn_uuid : add_bip_with_locs.dns()) {
          auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
          if (dn == nullptr) {
            LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
            continue;
          }
          dn_ids.emplace_back(dn->id());
        }
        bips_expected_locs.emplace_back(dn_ids);
      }
    }

    CHECK_EQ(add_block_bips.size(), bips_expected_locs.size());
    for (int i = 0; i < add_block_bips.size(); i++) {
      const auto& bip = add_block_bips[i];
      const auto& locs = bips_expected_locs[i];

      UpdateLastAllocatedBlockId(bip.block_id());
      UpdateGenerationStampV2(bip.gen_stamp());

      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};
      block_manager_->LoadBlock(inode_add.id(),
                                inode_add.parent_id(),
                                static_cast<uint8_t>(inode_add.replication()),
                                bip.write_mode(),
                                blk,
                                locs,
                                BlockUCState::kUnderConstruction);

      future_blks.emplace_back(blk);
    }

    StopWatch sw;
    sw.Start();
    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto& b : future_blks) {
      block_manager_->ProcessPendingFutureBlks(
          b.id, current_block_id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
    }
    auto time_cost_us = sw.NextStepTime();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
              << " slow, path: " << proto.path()
              << ", process cost us: " << time_cost_us;
    }

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<cloudfs::CreateResponseProto>();
      cloudfs::HdfsFileStatusProto* fst = resp->mutable_fs();
      UserGroupInfo default_ugi = UserGroupInfo();
      std::vector<AccessMode> modes{AccessMode::READ};
      ConstructFileStatus(proto.path(),
                          proto.inode(),
                          false,
                          false,
                          NetworkLocationInfo(),
                          default_ugi,
                          modes,
                          fst);
      AddRetryCacheEntry(
          rpc_info.rpc_client_id(), rpc_info.rpc_call_id(), std::move(resp));
    }
  };
  ApplyClosure* inode_add_closure = new ApplyClosure();
  ApplyCfsOpSetupClosure(ctx,
                         inode_add_closure,
                         proto.inode().id(),
                         cb);
  dones.emplace_back(inode_add_closure);
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [=] (const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  std::vector<INodeAndSnapshot> inodes_mov_src, inodes_del, parents;
  std::vector<INode*> inodes_add, inodes_mov_dst;
  std::vector<INode> inodes_old;
  std::vector<BlockInfoProto> add_block_bips;
  if (proto.has_overwrite_inode()) {
    if (proto.move_to_recycle_bin()) {
      STAT_RECORDER_INODE_RENAME(ctx,
                                 proto.overwrite_inode(),
                                 proto.rb_inode(),
                                 proto.ancestors_id(),
                                 proto.rb_ancestors_id());
      inodes_mov_src.emplace_back(proto.mutable_overwrite_inode(),
                                  proto.mutable_old_inode_snaplog());
      inodes_mov_dst.emplace_back(proto.mutable_rb_inode());
      parents.emplace_back(proto.mutable_rb_parent(),
                           proto.mutable_rb_parent_snaplog());
    } else {
      STAT_RECORDER_INODE_DELETE(ctx,
                                 proto.overwrite_inode(),
                                 proto.ancestors_id());
      inodes_del.emplace_back(proto.mutable_overwrite_inode(),
                              proto.mutable_old_inode_snaplog());
    }
    inodes_old.resize(inodes_old.size() + 1);
    inodes_old.back() = proto.overwrite_inode();
  }
  STAT_RECORDER_INODE_ADD(ctx, proto.inode(), proto.ancestors_id());
  inodes_add.emplace_back(proto.mutable_inode());
  parents.emplace_back(proto.mutable_parent(),
                       proto.mutable_parent_snaplog());
  if (proto.add_block_bips_with_locs_size() == 0) {
    for (const auto& add_bip : proto.add_block_bips()) {
      add_block_bips.emplace_back(add_bip);
    }
  } else {
    for (const auto& add_bip_with_locs : proto.add_block_bips_with_locs()) {
      add_block_bips.emplace_back(add_bip_with_locs.bip());
    }
  }
  meta_storage_->OrderedCommitINodes(&inodes_add,
                                     nullptr,
                                     &inodes_mov_src,
                                     &inodes_mov_dst,
                                     &inodes_del,
                                     &parents,
                                     &inodes_old,
                                     add_block_bips,
                                     {},
                                     op->txid(),
                                     dones,
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpConcat(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpConcatV2, FileToBeConcat);

  // 1. check editlog
  CHECK(proto.old_target_inode().type() == INode_Type_kFile);
  CHECK(!proto.old_target_inode().has_uc());
  auto s = IsUfsFileAllowConcat(proto.target_path(), proto.old_target_inode());
  CHECK(s.IsOK()) << "IsUfsFileAllowConcat target_path=" << proto.target_path()
                  << " target_inode="
                  << proto.old_target_inode().ShortDebugString()
                  << " status=" << s.ToString();

  CHECK_EQ(proto.src_paths_size(), proto.src_inodes_size());
  for (auto i = 0U; i < proto.src_paths_size(); i++) {
    CHECK(proto.src_inodes(i).type() == INode_Type_kFile);
    CHECK(proto.src_inodes(i).blocks_size() > 0);
    CHECK(!proto.src_inodes(i).has_uc());
    s = IsUfsFileAllowConcat(proto.src_paths(i), proto.src_inodes(i));
    CHECK(s.IsOK()) << "IsUfsFileAllowConcat src_path="
                    << proto.src_paths().Get(i)
                    << " src_inode=" << proto.src_inodes(i).ShortDebugString()
                    << " status=" << s.ToString();
  }

  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.target_path(), proto.old_target_inode());
    for (auto i = 0U; i < proto.src_paths_size(); i++) {
      CheckDBINodeForPhysicalApply(proto.src_paths(i), proto.src_inodes(i));
    }
  }

  // XXX
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written.
  for (const auto& bip : proto.target_bips()) {
    block_manager_->UpdateINode(bip.block_id(), proto.target_inode().id());
  }

  // 2. set callback
  auto cb = [this, ctx, op, proto](const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (IsAccMode()) {
      UploadPolicy policy;
      policy_manager_->UploadPolicyInstance()->GetPolicy(proto.parent_path(),
                                                         &policy);
      if (policy.has_upload_interval_ms() && policy.upload_interval_ms() >= 0) {
        LOG(FATAL) << "no upload policy";
      }
    }
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << proto.target_path()
              << ", apply op cost us: " << time_cost_us;
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.target_inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  // STAT_RECORDER_INODE_UPDATE(ctx,
  //                            proto.old_target_inode(),
  //                            proto.target_inode(),
  //                            proto.ancestors_id());
  meta_storage_->OrderedConcatINode(
      *proto.mutable_target_inode(),
      &proto.old_target_inode(),
      std::vector<INode>{proto.src_inodes().begin(), proto.src_inodes().end()},
      std::vector<BlockInfoProto>{proto.target_bips().begin(),
                                  proto.target_bips().end()},
      *proto.mutable_parent(),
      op->txid(),
      ctx->done,
      STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpAppend(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAppend, FileToBeAppend);

  // 1. check editlog
  CHECK_EQ(proto.inode().parent_id(), proto.parent().id());
  CHECK(proto.inode().has_uc());
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    // Cannot use ctx->done->add_post_callback because we want this logic to be
    // executed before any callbacks are triggered
    // Add task to writeback manager for ACC mode
    if (NameSpace::IsAccMode() && !FLAGS_enable_write_back_task_persistence) {
      CheckAccINode(proto.inode());
      ufs_env_->upload_monitor()->AddTask(
          proto.inode().id(),
          proto.inode().ufs_file_info().key(),
          proto.inode().ufs_file_info().upload_id());
    }

    INode old_inode = proto.old_inode();
    LocatedBlockProto located_block;
    if (old_inode.status() != INode_Status_kFileUnderConstruction) {
      // append on an already-closed file.
      VLOG(10) << "Reopening an already-closed file " << proto.path()
               << " for append";
      UserGroupInfo default_ugi = UserGroupInfo();
      PrepareFileForWriteInternal(proto.path(),
                                  proto.inode().uc().client_name(),
                                  proto.inode().uc().client_machine(),
                                  false,
                                  *proto.mutable_old_inode(),
                                  default_ugi,
                                  &located_block);
    }
    UpdateBlocks(proto.path(),
                 &old_inode,
                 { proto.inode().blocks().begin(),
                   proto.inode().blocks().end() },
                 false,
                 nullptr,
                 nullptr,
                 false);
    AddLease(proto.inode());

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<AppendResponseProto>();
      resp->mutable_block()->Swap(&located_block);
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpAddBlock(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAddBlockV2, BlockToBeAdd);

  // 1. check editlog
  {
    // check last block
    CHECK(proto.has_last_bip());
    CHECK_GE(proto.inode().blocks_size(), 1);
    auto last_bp = proto.inode().blocks(proto.inode().blocks_size() - 1);
    auto last_bip = proto.last_bip();
    CHECK_EQ(last_bip.block_id(), last_bp.blockid());
    CHECK_EQ(last_bip.gen_stamp(), last_bp.genstamp());
    CHECK_EQ(last_bip.num_bytes(), last_bp.numbytes());
  }
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  std::vector<Block> future_blks;
  // process penultimate block
  if (proto.inode().blocks_size() > 1) {
    BlockProto new_penult_bp =
        proto.inode().blocks(proto.inode().blocks_size() - 2);
    BlockProto old_last_bp =
        proto.old_inode().blocks(proto.old_inode().blocks_size() - 1);
    CHECK_EQ(old_last_bp.blockid(), new_penult_bp.blockid());
    CHECK_EQ(old_last_bp.genstamp(), new_penult_bp.genstamp());
    block_manager_->UpdateLength(new_penult_bp.blockid(),
                                 new_penult_bp.numbytes());
    if (!block_manager_->BlockHasBeenComplete(old_last_bp.blockid())) {
      Block blk{ new_penult_bp.blockid(),
                 static_cast<uint32_t>(new_penult_bp.numbytes()),
                 new_penult_bp.genstamp() };
      block_manager_->CommitOrCompleteOrPersistLastBlock(blk, true);
      future_blks.emplace_back(blk);
    }
  }

  const BlockInfoProto* new_last_bip = nullptr;
  std::vector<DatanodeID> dns;
  if (proto.has_last_bip_with_locs()) {
    new_last_bip = &proto.last_bip_with_locs().bip();
    for (const auto& dn_uuid : proto.last_bip_with_locs().dns()) {
      auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
      if (!dn) {
        LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
        continue;
      }
      dns.emplace_back(dn->id());
    }
  } else {
    new_last_bip = &proto.last_bip();
  }
  CHECK_NOTNULL(new_last_bip);

  UpdateLastAllocatedBlockId(new_last_bip->block_id());
  UpdateGenerationStampV2(new_last_bip->gen_stamp());

  // process last block
  Block blk{new_last_bip->block_id(),
            static_cast<uint32_t>(new_last_bip->num_bytes()),
            new_last_bip->gen_stamp()};
  block_manager_->LoadBlock(
      proto.old_inode().id(),
      proto.old_inode().parent_id(),
      static_cast<uint8_t>(proto.old_inode().replication()),
      new_last_bip->write_mode(),
      blk,
      dns,
      BlockUCState::kUnderConstruction);
  future_blks.emplace_back(blk);

  // 2. set callback
  auto cb = [this, op, &proto, future_blks] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    auto current_gsv2 = generation_stamp_v2();
    for (auto& future_blk : future_blks) {
      block_manager_->ProcessPendingFutureBlks(future_blk.id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(future_blk, current_gsv2);
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedAddBlock(proto.mutable_inode(),
                                 op->GetProto().has_penultimate_bip()
                                     ? &op->GetProto().penultimate_bip()
                                     : nullptr,
                                 *new_last_bip,
                                 proto.mutable_old_inode(),
                                 *proto.mutable_inode_snaplog(),
                                 op->txid(),
                                 ctx->done,
                                 STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpAbandonBlock(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAbandonBlock, BlockToBeAbandon);

  // 1. check editlog
  CHECK(proto.has_abandoned_blk_id());
  for (const auto& block : proto.inode().blocks()) {
    CHECK_NE(block.blockid(), proto.abandoned_blk_id());
  }
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               { proto.inode().blocks().begin(),
                 proto.inode().blocks().end() },
               false,
               ctx,
               &last_blk_to_del,
               true);
  CHECK_EQ(last_blk_to_del, proto.abandoned_blk_id());

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<AbandonBlockResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedAbandonBlock(proto.inode(),
                                     proto.abandoned_blk_id(),
                                     proto.mutable_old_inode(),
                                     op->txid(),
                                     ctx->done,
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpUpdatePipeline(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUpdatePipeline, PipelineToBeUpdate);

  // 1. check editlog
  CHECK(proto.has_last_bip_tbuc());
  CHECK_GT(proto.inode().blocks_size(), 0);
  CHECK_EQ(proto.last_bip_tbuc().block_id(),
           proto.inode().blocks(proto.inode().blocks_size() - 1).blockid());
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               { proto.inode().blocks().begin(),
                 proto.inode().blocks().end() },
               false,
               ctx,
               &last_blk_to_del,
               true);
  CHECK_EQ(last_blk_to_del, kInvalidBlockID);

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<UpdatePipelineResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedUpdatePipeline(proto.inode(),
                                       proto.last_bip_tbuc(),
                                       &proto.old_inode(),
                                       op->txid(),
                                       ctx->done,
                                       STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpFsync(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpFsync, FileToBeSync);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 1. check editlog
  if (proto.has_last_bip_tbuc()) {
    CHECK_GT(proto.inode().blocks_size(), 0);
    CHECK_EQ(proto.last_bip_tbuc().block_id(),
             proto.inode().blocks(proto.inode().blocks_size() - 1).blockid());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               { proto.inode().blocks().begin(),
                 proto.inode().blocks().end() },
               false,
               ctx,
               &last_blk_to_del,
               true);
  CHECK_EQ(last_blk_to_del, kInvalidBlockID);
  // for commitLastBlock
  bool commit_last_block =
      proto.has_last_bip_tbuc() &&
      proto.last_bip_tbuc().state() == BlockInfoProto::kCommitted;

  // 2. set callback
  auto cb = [this, op, &proto, commit_last_block](const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (commit_last_block) {
      Block b(proto.inode().blocks(proto.inode().blocks_size() - 1));
      UpdateLastAllocatedBlockId(b.id);
      UpdateGenerationStampV2(b.gs);
      if (!block_manager_->BlockHasBeenCommitted(
              proto.last_bip_tbuc().block_id())) {
        block_manager_->CommitOrCompleteOrPersistLastBlock(b,
                                                           /*force=*/false);
      }
    }
    {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<FsyncResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  if (commit_last_block) {
    meta_storage_->OrderedCommitLastBlock(proto.mutable_inode(),
                                          proto.last_bip_tbuc(),
                                          &proto.old_inode(),
                                          *proto.mutable_inode_snaplog(),
                                          op->txid(),
                                          ctx->done,
                                          STAT_RECORDER_PTR);
  } else {
    meta_storage_->OrderedFsync(
        proto.mutable_inode(),
        proto.has_last_bip_tbuc() ? &proto.last_bip_tbuc() : nullptr,
        &proto.old_inode(),
        *proto.mutable_inode_snaplog(),
        op->txid(),
        ctx->done,
        STAT_RECORDER_PTR);
  }
}

void NameSpace::ApplyCfsOpCommitBlockSynchronization(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(
      ctx, OpCommitBlockSynchronization, BlockToBeCommitSynchronization);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 1. check editlog
  CHECK(!proto.delete_block());
  CHECK(!proto.close_file());
  CHECK(proto.has_last_bip());
  CHECK_GT(proto.inode().blocks_size(), 0);
  CHECK_EQ(proto.last_bip().block_id(),
           proto.inode().blocks(proto.inode().blocks_size() - 1).blockid());

  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               {proto.inode().blocks().begin(), proto.inode().blocks().end()},
               /*should_complete_last_block=*/true,
               ctx,
               &last_blk_to_del,
               true);
  CHECK_EQ(last_blk_to_del, kInvalidBlockID);

  // 2. set callback
  auto cb = [this, op, &proto](const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
  };
  ApplyCfsOpSetupClosure(
      ctx, dynamic_cast<ApplyClosure*>(ctx->done), proto.inode().id(), cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(
      ctx, proto.old_inode(), proto.inode(), proto.ancestors_id());
  std::vector<INodeAndSnapshot> inodes_mod{
      INodeAndSnapshot(proto.mutable_inode(), proto.mutable_inode_snaplog())};
  std::vector<INode> inodes_old{proto.old_inode()};
  meta_storage_->OrderedCommitINodes(nullptr,
                                     &inodes_mod,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     nullptr,
                                     &inodes_old,
                                     {},
                                     {proto.last_bip()},
                                     op->txid(),
                                     {ctx->done},
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpUpdateBlocks(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUpdateBlocksV2, BlocksToBeUpdate);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               {proto.inode().blocks().begin(), proto.inode().blocks().end()},
               /*should_complete_last_block=*/false,
               ctx,
               &last_blk_to_del,
               true);

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedUpdateINodeAndDeleteLastBlock(proto.inode(),
                                                      last_blk_to_del,
                                                      &proto.old_inode(),
                                                      op->txid(),
                                                      ctx->done,
                                                      STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpCloseFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpCloseFile, FileToBeClose);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  BlockID last_blk_to_del = kInvalidBlockID;
  INode old_inode = proto.old_inode();
  UpdateBlocks(proto.path(),
               &old_inode,
               { proto.inode().blocks().begin(),
                 proto.inode().blocks().end() },
               true,
               ctx,
               &last_blk_to_del,
               true);

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    RemoveLease(proto.old_inode());
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedCloseFile(
      proto.mutable_inode(),
      proto.has_last_bip() ? &proto.last_bip() : nullptr,
      last_blk_to_del,
      proto.mutable_old_inode(),
      *proto.mutable_inode_snaplog(),
      op->txid(),
      ctx->done,
      STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpReassignLease(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpReassignLeaseV2, LeaseToBeReassign);

  // 1. check editlog
  // nothing
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    bool ok = lease_manager_->ReassignLease(proto.lease_holder(),
                                            proto.inode().id(),
                                            proto.new_holder());
    CHECK(ok);
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpAllocateBlockId(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAllocateBlockIdV2, BlockIdToBeAllocate);

  // 1. check editlog
  // nothing

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    UpdateLastAllocatedBlockId(proto.new_block_id());
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         -1,
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedPutBlockId(proto.new_block_id(),
                                   op->txid(),
                                   ctx->done);
}

void NameSpace::ApplyCfsOpSetGenStampV1(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetGenStampV2, GenStampToBeSet);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  // XXX refer to NameSpace::ApplyOpSetGenstampV1()
  {
    std::lock_guard<std::mutex> guard(genstamp_v1_mutex_);
    generation_stamp_v1_.store(proto.new_gen_stamp());
  }
  char genstamp[sizeof(uint64_t)];
  platform::WriteBigEndian(genstamp, 0, proto.new_gen_stamp());
  meta_storage_->OrderedPutNameSystemInfo(kGenerationStampV1Key,
                                          cnetpp::base::StringPiece(genstamp, 8),
                                          op->txid(),
                                          ctx->done);
}

void NameSpace::ApplyCfsOpSetGenStampV2(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetGenStampV2, GenStampToBeSet);

  // 1. check editlog
  // nothing

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    UpdateGenerationStampV2(proto.new_gen_stamp());
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         -1,
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedPutGenerationStampV2(proto.new_gen_stamp(),
                                             op->txid(),
                                             ctx->done);
}

// Refer to ApplyOpBatchCreateFile.
void NameSpace::ApplyCfsOpBatchCreateFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpBatchCreateFile, BatchInodeToCreate);

  // 1. check editlog
  CHECK_EQ(proto.parent().mtime(), proto.timestamp_in_ms());
  for (const auto& file : proto.files()) {
    CHECK_EQ(file.inode().parent_id(), proto.parent().id());
  }
  for (const auto& overwritten_inode : proto.overwritten_inodes()) {
    CHECK_EQ(overwritten_inode.id(), proto.parent().id());
  }
  CHECK_EQ(proto.overwritten_bips_size(), 0);

  // check paths
  METRIC_WATCH_START(op_batch_create_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (int i = 0; i < proto.files_size(); ++i) {
      paths.push_back(proto.files().Get(i).path());
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  if (ctx->physical_apply_check_db) {
    // get inodes and check inode
    INode parent_inode;
    std::vector<INode> ancestors;
    std::vector<INode> src_inodes;
    std::vector<INode> overwritten_inodes;
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/false,
                                    /*push_even_if_src_notfound=*/true,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();
    for (const auto& inode : src_inodes) {
      if (inode.id() != kInvalidINodeId) {
        overwritten_inodes.push_back(inode);
      }
    }
    src_inodes.clear();
    CHECK_EQ(src_inodes.size(), 0);
    CHECK_EQ(proto.overwritten_inodes().size(), overwritten_inodes.size());
  }
  METRIC_WATCH_STOP(op_batch_create_file_get_inodes_time_);

  // 2. set callback
  std::vector<Closure*> dones;
  for (const auto& file : proto.files()) {
    auto cb = [this, file](const Status& s) {
      CHECK(s.IsOK());
      auto& new_file = file.inode();

      // update last inode id
      UpdateLastINodeId(new_file.id());

      // lease
      lease_manager_->AddLease(new_file.uc().client_name(), new_file.id());

      // block_manager
      std::vector<cloudfs::BlockProto> blks_inode;
      for (const auto& blk : new_file.blocks()) {
        blks_inode.push_back(blk);
      }

      // feature: add block
      std::vector<BlockInfoProto> add_block_bips_per_file;
      std::vector<std::vector<DatanodeID>> bip_expected_locs_per_file;
      std::vector<Block> future_blks_per_file;
      std::vector<Block> blks_bip_per_file;
      if (file.add_block_bips_with_locs_size() > 0) {
        for (const auto& add_block_bip_with_locs :
             file.add_block_bips_with_locs()) {
          add_block_bips_per_file.emplace_back(add_block_bip_with_locs.bip());
          std::vector<DatanodeID> dns;
          for (const auto& dn_uuid : add_block_bip_with_locs.dns()) {
            auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
            if (dn == nullptr) {
              LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
              continue;
            }
            dns.emplace_back(dn->id());
          }
          bip_expected_locs_per_file.emplace_back(dns);
        }
      } else if (file.has_add_block_bips()) {
        add_block_bips_per_file.emplace_back(file.add_block_bips());
        bip_expected_locs_per_file.emplace_back(std::vector<DatanodeID>());
      }
      CHECK_EQ(add_block_bips_per_file.size(),
               bip_expected_locs_per_file.size());
      for (int i = 0; i < add_block_bips_per_file.size(); i++) {
        const auto& add_bip = add_block_bips_per_file[i];
        const auto& dns = bip_expected_locs_per_file[i];

        UpdateLastAllocatedBlockId(add_bip.block_id());
        UpdateGenerationStampV2(add_bip.gen_stamp());

        Block blk{add_bip.block_id(),
                  static_cast<uint32_t>(add_bip.num_bytes()),
                  add_bip.gen_stamp()};
        blks_bip_per_file.push_back(blk);

        block_manager_->LoadBlock(new_file.id(),
                                  new_file.parent_id(),
                                  static_cast<uint8_t>(new_file.replication()),
                                  add_bip.write_mode(),
                                  blk,
                                  dns,
                                  BlockUCState::kUnderConstruction);

        future_blks_per_file.emplace_back(blk);

        CHECK_EQ(blks_inode.size(), blks_bip_per_file.size());
        for (int j = 0; j < blks_inode.size(); ++j) {
          CHECK_EQ(blks_inode[j].blockid(), blks_bip_per_file[j].id);
          CHECK_EQ(blks_inode[j].numbytes(), blks_bip_per_file[j].num_bytes);
          CHECK_EQ(blks_inode[j].genstamp(), blks_bip_per_file[j].gs);
        }
#if 0
      UpdateBlocks(path,
                   &new_file,
                   blks,
                   /*should_complete_last_block=*/false,
                   ctx,
                   nullptr);
#endif

        StopWatch sw;
        sw.Start();
        auto current_block_id = last_allocated_block_id();
        auto current_gsv2 = generation_stamp_v2();
        for (auto& b : future_blks_per_file) {
          block_manager_->ProcessPendingFutureBlks(
              b.id, current_block_id, current_gsv2);
          block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
        }
        auto time_cost_us = sw.NextStepTime();
        if (time_cost_us > FLAGS_edit_log_slow_op_us) {
          VLOG(6)
              << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
              << " slow, path: " << file.path()
              << ", process cost us: " << time_cost_us;
        }
      }
    };
    ApplyClosure* inode_add_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, inode_add_closure, file.inode().id(), cb);
    dones.emplace_back(inode_add_closure);
  }

  for (const auto& overwritten_inode : proto.overwritten_inodes()) {
    auto cb = [this, overwritten_inode](const Status& s) {
      CHECK(s.IsOK());
      lease_manager_->RemoveLease(overwritten_inode.id());
    };
    ApplyClosure* inode_del_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, inode_del_closure, overwritten_inode.id(), cb);
    dones.emplace_back(inode_del_closure);
  }

  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));
  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [this, ctx_latch, ctx](const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();

#if 0  // TODO(xiong): Implement
      // cache
      METRIC_WATCH_START(op_batch_create_file_del_cache_time_);
      if (proto.has_log_rpc_info()) {
        auto resp = std::make_shared<BatchCreateResponseProto>();
        resp->set_result(true);
        AddRetryCacheEntry(rpc_info.rpc_client_id(), rpc_info.rpc_call_id(), std::move(resp));
      }
      METRIC_WATCH_STOP(op_batch_create_file_del_cache_time_);
#endif
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
#if 0  // TODO(xiong): Implement
  for (const auto& file : proto.files()) {
    STAT_RECORDER_INODE_ADD(ctx, file.inode(), proto.ancestors_id());
  }
  for (const auto& overwritten_inode : proto.overwritten_inodes()) {
    STAT_RECORDER_INODE_DELETE(ctx, overwritten_inode, proto.ancestors_id());
  }
#endif
  std::vector<INode> src_inodes;
  std::vector<BlockInfoProto> add_block_bips;
  for (const auto& file : proto.files()) {
    src_inodes.emplace_back(file.inode());
    if (file.add_block_bips_with_locs_size() > 0) {
      for (const auto& add_block_bip_with_locs :
           file.add_block_bips_with_locs()) {
        add_block_bips.emplace_back(add_block_bip_with_locs.bip());
      }
    } else if (file.has_add_block_bips()) {
      add_block_bips.emplace_back(file.add_block_bips());
    }
  }
  std::vector<INode> overwritten_inodes{proto.overwritten_inodes().begin(),
                                        proto.overwritten_inodes().end()};
  std::vector<INode> parent_inodes{proto.parent()};
  meta_storage_->OrderedCommitINodes(&src_inodes,
                                     nullptr,
                                     &overwritten_inodes,
                                     &parent_inodes,
                                     {},
                                     {},
                                     add_block_bips,
                                     op->txid(),
                                     dones,
                                     STAT_RECORDER_PTR);
}

// Refer to ApplyOpBatchCompleteFile.
void NameSpace::ApplyCfsOpBatchCompleteFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpBatchCompleteFile, BatchInodeToComplete);

  // 1. check editlog
  CHECK_GT(proto.paths_size(), 0);
  CHECK_EQ(proto.original_inodes_size(),
           proto.complete_inodes_size() + proto.deleted_inodes_size());
  for (const auto& inode : proto.deleted_inodes()) {
    CHECK_EQ(inode.blocks().size(), 0);
  }
  for (const auto& bip : proto.deleted_bips()) {
    CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated);
  }

  // check paths
  METRIC_WATCH_START(op_batch_complete_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (const auto& path : proto.paths()) {
      paths.push_back(path);
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  if (ctx->physical_apply_check_db) {
    // get inodes and check inode
    INode parent_inode;
    std::vector<INode> ancestors;
    std::vector<INode> src_inodes;
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/true,
                                    /*push_even_if_src_notfound=*/false,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();
    CHECK_EQ(parent_inode.id(), proto.parent().id());
    CHECK_EQ(src_inodes.size(), sorted_paths.size());
    CHECK_EQ(src_inodes.size(), proto.original_inodes().size());

    std::unordered_map<INodeID, INode> complete_inodes_table;
    for (const auto& inode : proto.complete_inodes()) {
      complete_inodes_table.insert({inode.id(), inode});
    }
    std::vector<std::pair<INode, INode>> complete_inodes;
    for (int i = 0; i < src_inodes.size(); ++i) {
      const auto& inode_from_db = src_inodes[i];
      const auto& inode_from_pb = proto.original_inodes().Get(i);

      auto it = complete_inodes_table.find(inode_from_db.id());
      if (it != complete_inodes_table.end()) {
        complete_inodes.emplace_back(inode_from_db, it->second);
      }

      // very easy check
      if (!(inode_from_db.id() == inode_from_pb.id() &&
            inode_from_db.parent_id() == inode_from_pb.parent_id() &&
            inode_from_db.name() == inode_from_pb.name() &&
            inode_from_db.type() == inode_from_pb.type())) {
        LOG(FATAL) << "INode mismatched,"
                   << " inode_from_db=" << ToJsonCompactString(inode_from_db)
                   << " inode_from_pb=" << ToJsonCompactString(inode_from_pb)
                   << " txid=" << op->txid();
      }
      // CompareINodeFromMetaStorageAndEditLog(inode_from_db, inode_from_pb);
    }
    CHECK_EQ(complete_inodes_table.size(), complete_inodes.size());
  }
  METRIC_WATCH_STOP(op_batch_complete_file_get_inodes_time_);

  // 2. set callback
  std::vector<Closure*> dones;
  for (const auto& inode : proto.complete_inodes()) {
    auto cb = [this, inode](const Status& s) {
      CHECK(s.IsOK());
      // remove lease
      lease_manager_->RemoveLease(inode.id());

      // trigger upload
      if (NameSpace::IsAccMode() && ufs_env_ &&
          FLAGS_complete_rpc_trigger_file_upload_in_callback) {
        CHECK_NOTNULL(ufs_env_->upload_monitor())
            ->TriggerFileUpload(inode.id());
      }
    };
    ApplyClosure* inode_complete_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, inode_complete_closure, inode.id(), cb);
    dones.emplace_back(inode_complete_closure);
  }

  for (const auto& inode : proto.deleted_inodes()) {
    auto cb = [this, inode](const Status& s) {
      CHECK(s.IsOK());
      // remove lease
      lease_manager_->RemoveLease(inode.id());
    };
    ApplyClosure* inode_delete_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, inode_delete_closure, inode.id(), cb);
    dones.emplace_back(inode_delete_closure);
  }

  for (const auto& bip : proto.bips()) {
    auto cb = [this, bip](const Status& s) {
      CHECK(s.IsOK());
      // update block length
      block_manager_->UpdateINode(bip.block_id(), bip.inode_id());

      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};
      block_manager_->CommitOrCompleteOrPersistLastBlock(blk, true);

      // future block
      auto current_gsv2 = generation_stamp_v2();
      block_manager_->ProcessPendingFutureBlks(blk.id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(blk, current_gsv2);
    };
    ApplyClosure* complete_block_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, complete_block_closure, bip.inode_id(), cb);
    dones.emplace_back(complete_block_closure);
  }

  for (const auto& bip : proto.deleted_bips()) {
    auto cb = [this, bip](const Status& s) {
      CHECK(s.IsOK());
      // future block
      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};
      auto current_gsv2 = generation_stamp_v2();
      block_manager_->ProcessPendingFutureBlks(blk.id, current_gsv2);
      block_manager_->ProcessPendingPersistedBlks(blk, current_gsv2);
    };
    ApplyClosure* remove_block_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, remove_block_closure, bip.inode_id(), cb);
    dones.emplace_back(remove_block_closure);
  }

  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));
  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [=](const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();

#if 0  // TODO(xiong): Implement
      // cache
      METRIC_WATCH_START(op_batch_create_file_del_cache_time_);
      if (proto.has_log_rpc_info()) {
        auto resp = std::make_shared<BatchCreateResponseProto>();
        resp->set_result(true);
        AddRetryCacheEntry(
            rpc_info.rpc_client_id(), rpc_info.rpc_call_id(), std::move(resp));
      }
      METRIC_WATCH_STOP(op_batch_create_file_del_cache_time_);
#endif
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
#if 0  // TODO(xiong): Implement
  for (const auto& inode : proto.complete_inodes()) {
    STAT_RECORDER_INODE_ADD(ctx, inode, proto.ancestors_id());
  }
  for (const auto& inode : proto.deleted_inodes()) {
    STAT_RECORDER_INODE_DELETE(ctx, inode, proto.ancestors_id());
  }
#endif
  std::vector<INode> parent_inodes{proto.parent()};
  std::vector<std::pair<INode, INode>> complete_inodes;
  {
    std::map<INodeID, INode> original_inodes_table;
    for (const auto& inode : proto.original_inodes()) {
      original_inodes_table[inode.id()] = inode;
    }
    for (const auto& inode : proto.complete_inodes()) {
      complete_inodes.emplace_back(original_inodes_table.at(inode.id()), inode);
    }
  }
  std::vector<INode> deleted_inodes{proto.deleted_inodes().begin(),
                                    proto.deleted_inodes().end()};
  std::vector<BlockInfoProto> bips_modify;
  for (const auto& bip : proto.bips()) {
    bips_modify.push_back(bip);
  }
  for (const auto& bip : proto.deleted_bips()) {
    CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated);
    bips_modify.push_back(bip);
  }
  std::vector<INode> old_inodes{proto.original_inodes().begin(),
                                proto.original_inodes().end()};
  meta_storage_->OrderedCommitINodes(nullptr,
                                     &complete_inodes,
                                     &deleted_inodes,
                                     &parent_inodes,
                                     &old_inodes,
                                     bips_modify,
                                     {},
                                     op->txid(),
                                     dones,
                                     /*STAT_RECORDER_PTR*/ nullptr);
}

void NameSpace::ApplyCfsOpBatchDeleteFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpBatchDeleteFile, BatchInodeToDelete);

  if (proto.files().size() == 0) {
    VLOG(9) << op->op_name();
    VLOG(9) << "proto=" << proto.ShortDebugString();
    meta_storage_->OrderedCommitNop(op->txid());
    ctx->done->Run();
    return;
  }

  // 1. check editlog
  CHECK_GT(proto.files_size(), 0);
  for (const auto& file : proto.files()) {
    CHECK_EQ(file.bips_size(), 0);
  }

  // check paths
  METRIC_WATCH_START(op_batch_delete_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (int i = 0; i < proto.files_size(); ++i) {
      paths.push_back(proto.files().Get(i).path());
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  if (ctx->physical_apply_check_db) {
    // get inodes and check inode
    INode parent_inode;
    std::vector<INode> ancestors;
    std::vector<INode> src_inodes;
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/true,
                                    /*push_even_if_src_notfound=*/false,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();
    CHECK_EQ(parent_inode.id(), proto.parent().id());
    CHECK_EQ(paths.size(), src_inodes.size());

    for (int i = 0; i < paths.size(); ++i) {
      const auto& inode_from_db = src_inodes[i];
      const auto& inode_from_pb = proto.files().Get(i).inode();

      CompareINodeFromMetaStorageAndEditLog(inode_from_db, inode_from_pb);

      // not use now
      CHECK_EQ(proto.files().Get(i).bips().size(), 0);
    }
  }
  METRIC_WATCH_STOP(op_batch_delete_file_get_inodes_time_);

  // 2. set callback
  std::vector<Closure*> dones;
  for (const auto& file : proto.files()) {
    auto cb = [this, file](const Status& s) {
      CHECK(s.IsOK());
      if (file.inode().type() == INode::kFile && file.inode().has_uc()) {
        RemoveLease(file.inode());
      }
    };
    ApplyClosure* inode_del_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, inode_del_closure, file.inode().id(), cb);
    dones.emplace_back(inode_del_closure);
  }

  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));
  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [=](const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();

#if 0  // TODO(xiong): Implement
      // cache
      METRIC_WATCH_START(op_batch_create_file_del_cache_time_);
      if (proto.has_log_rpc_info()) {
        auto resp = std::make_shared<DeleteResponseProto>();
        resp->set_result(true);
        AddRetryCacheEntry(
            rpc_info.rpc_client_id(), rpc_info.rpc_call_id(), std::move(resp));
      }
      METRIC_WATCH_STOP(op_batch_create_file_del_cache_time_);
#endif
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
#if 0  // TODO(xiong): Implement
  STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);
#endif
  std::vector<INode> parent_inodes{proto.parent()};
  std::vector<INode> src_inodes;
  for (const auto& file : proto.files()) {
    src_inodes.emplace_back(file.inode());
  }
  std::vector<INode> inodes_old = src_inodes;
  meta_storage_->OrderedCommitINodes(nullptr,
                                     nullptr,
                                     &src_inodes,
                                     &parent_inodes,
                                     &src_inodes,
                                     {},
                                     {},
                                     op->txid(),
                                     dones,
                                     STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpUpdateATimeProtos(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUpdateATimeProtos, ATimeToBeUpdateProtos);
  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  meta_storage_->UpdateTtlATimes(op->GetProto(), op->txid(), ctx->done);
}

void NameSpace::ApplyCfsOpApproveUploadBlk(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpApproveUploadBlk, BlkToBeApproveUpload);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.bip().inode_id());

  // 3. submit WriteTask async
  meta_storage_->PutBlockInfo(proto.bip(),
                              proto.has_old_bip() ? &proto.old_bip() : nullptr,
                              op->txid(),
                              ctx->done);
}

void NameSpace::ApplyCfsOpPersistBlk(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpPersistBlk, BlkToBePersist);

  // 1. check editlog
  // nothing

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    Block blk(proto.bip().block_id(),
              static_cast<uint32_t>(proto.bip().num_bytes()),
              proto.bip().gen_stamp());
    if (!block_manager_->PersistBlock(blk)) {
      block_manager_->EnqueuePendingPersistedBlks(blk);
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.bip().inode_id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->MoveBlockInfoToPersisted(
      proto.bip(),
      proto.has_old_bip() ? &proto.old_bip() : nullptr,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyCfsOpDelDepringBlks(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDelDepringBlks, DepringBlksToBeDel);

  // 1. check editlog
  // nothing

  // 2. set callback
  std::vector<Closure*> dones;
  // group bips by inode-id to reduce number of closure
  std::unordered_map<INodeID, std::vector<BlockInfoProto>> in_blks_map;
  for (BlockInfoProto bip : proto.depred_bips()) {
    in_blks_map[bip.inode_id()].push_back(bip);
  }
  for (auto& idx_pair : in_blks_map) {
    auto cb = [this, op, &proto, idx_pair] (const Status& s) {
      CHECK(s.IsOK());
      block_manager_->RemoveBlocksAndUpdateSafeMode(idx_pair.second);
    };
    ApplyClosure* blk_del_closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, blk_del_closure, idx_pair.first, cb);
    dones.emplace_back(blk_del_closure);
  }
  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [=] (const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  meta_storage_->DelDepringBlks(op->GetProto(), op->txid(), dones);
}

void NameSpace::ApplyCfsOpDelDepredBlks(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDelDepredBlks, DepredBlksToBeDel);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  meta_storage_->DelDepredBlks(op->GetProto(), op->txid(), ctx->done);
}

void NameSpace::ApplyCfsOpAccUpdateBlockInfo(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAccUpdateBlockInfo, AccUpdateBlockInfoOpBody);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateUfsBlockInfo(
      op->txid(),
      op->GetProto().bip(),
      op->GetProto().has_old_bip() ? &op->GetProto().old_bip() : nullptr,
      ctx->done);
}

void NameSpace::ApplyCfsOpPin(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpPin, INodeToPin);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto](const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<cloudfs::PinResponseProto>();
      AddRetryCacheEntry(
          rpc_info.rpc_client_id(), rpc_info.rpc_call_id(), std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(
      ctx, dynamic_cast<ApplyClosure*>(ctx->done), proto.inode().id(), cb);

  // 3. submit WriteTask async
  INode inode = proto.inode();
  if (!proto.has_update_txid() || proto.update_txid()) {
    inode.mutable_pin_status()->set_txid(op->txid());
  }
  SnapshotLog inode_snaplog;
  JobInfoOpBody job;
  if (proto.has_job()) {
    job.CopyFrom(proto.job());
  }
  ManagedJobId cancel_job_id;
  if (proto.has_cancel_job_id()) {
    cancel_job_id = proto.cancel_job_id();
  }
  meta_storage_->PinINode(&inode,
                          proto.has_old_inode() ? &proto.old_inode() : nullptr,
                          inode_snaplog,
                          job,
                          cancel_job_id,
                          op->txid(),
                          ctx->done);
}

void NameSpace::ApplyCfsOpReconcileINodeAttrs(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpReconcileINodeAttrs, INodeToReconcile);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    if (proto.has_old_inode()) {
      CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
    }
  }

  // 2. set callback
  ApplyCfsOpSetupClosure(
      ctx, dynamic_cast<ApplyClosure*>(ctx->done), proto.inode().id());

  // 3. submit WriteTask async
  SnapshotLog inode_snaplog;
  std::vector<ManagedJobId> cancel_job_id;
  for (auto& job : proto.cancel_job_id()) {
    cancel_job_id.emplace_back(job);
  }
  std::set<int64_t> expired_ttl;
  for (auto t : proto.expired_ttl()) {
    expired_ttl.insert(t);
  }
  std::set<int64_t> new_ttl;
  for (auto t : proto.new_ttl()) {
    new_ttl.insert(t);
  }
  meta_storage_->ReconcileINodeAttrs(
      proto.mutable_inode(),
      proto.has_old_inode() ? &proto.old_inode() : nullptr,
      inode_snaplog,
      cancel_job_id,
      expired_ttl,
      new_ttl,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyCfsOpPersistJobInfo(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpPersistJobInfo, JobInfoOpBody);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  meta_storage_->PutJobInfo(proto, op->txid(), ctx->done);
}

void NameSpace::ApplyCfsOpAccSyncListingBatchAdd(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(
      ctx, OpAccSyncListingBatchAdd, AccSyncListingBatchAddOpBody);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.dir_path(), proto.dir_inode());
  }

  // 2. set callback
  // refer to NameSpace::ApplyCfsOpOpenFile
  std::vector<Closure*> dones;
  for (const auto& file_to_add : proto.files_to_add()) {
    auto cb = [this, file_to_add](const Status& s) {
      CHECK(s.IsOK());
      UpdateLastINodeId(file_to_add.inode().id());
      for (auto&& b : file_to_add.block_info()) {
        UpdateLastAllocatedBlockId(b.block_id());
        UpdateGenerationStampV2(b.gen_stamp());
      }

      auto current_block_id = last_allocated_block_id();
      auto current_gsv2 = generation_stamp_v2();
      for (auto&& b : file_to_add.block_info()) {
        block_manager_->ProcessPendingFutureBlks(
            b.block_id(), current_block_id, current_gsv2);
      }
    };
    ApplyClosure* closure = new ApplyClosure();
    ApplyCfsOpSetupClosure(ctx, closure, file_to_add.inode().id(), cb);
    dones.emplace_back(closure);
  }
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));
  // XXX callbacks may be submitted to different slows-threads and executed.
  // utill all sub-closures called, will ctx->done completed.
  auto ctx_latch = std::make_shared<std::atomic<size_t>>(dones.size());
  auto ctx_cb = [this, ctx, op, ctx_latch](const Status& s) {
    CHECK(s.IsOK());
    if (ctx_latch->fetch_sub(1) == 1) {
      ctx->done->Run();
      auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                              ctx->sw->GetTime())
                              .count();
      if (time_cost_us > FLAGS_edit_log_slow_op_us) {
        VLOG(6) << "Detect edit log apply slow "
                << ", op_name: " << op->op_name()
                << ", path: " << op->GetProto().dir_path()
                << ", inode id: " << op->GetProto().dir_inode().id()
                << ", subfiles: " << op->GetProto().files_to_add_size()
                << ", apply op cost us: " << time_cost_us;
      }
      MFH(metrics_.op_acc_sync_batch_add_time_)->Update(time_cost_us);
    }
  };
  for (Closure* done : dones) {
    dynamic_cast<ApplyClosure*>(done)->add_post_callback(ctx_cb);
  }

  // 3. submit WriteTask async
  std::vector<INodeWithBlocks> new_ufs_files;
  new_ufs_files.reserve(proto.files_to_add_size());
  for (const auto& file_to_add : proto.files_to_add()) {
    INodeWithBlocks ib;
    ib.node = file_to_add.inode();
    for (const auto& block_info : file_to_add.block_info()) {
      ib.blocks.push_back(block_info);
    }
    new_ufs_files.emplace_back(std::move(ib));
  }
  meta_storage_->OrderedCreateUfsFiles(
      op->txid(), new_ufs_files, &proto.dir_inode(), dones);
}

void NameSpace::ApplyCfsOpAccSyncListingBatchUpdate(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(
      ctx, OpAccSyncListingBatchUpdate, AccSyncListingBatchUpdateOpBody);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.dir_path(), proto.dir_inode());
  }

  // 2. set callback
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().dir_path()
              << ", inode id: " << op->GetProto().dir_inode().id()
              << ", subfiles: " << op->GetProto().files_to_update_size()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_batch_update_time_)->Update(time_cost_us);
  });
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  std::vector<INode> files_to_update;
  files_to_update.reserve(proto.files_to_update_size());
  for (const auto& inode : proto.files_to_update()) {
    files_to_update.emplace_back(inode);
  }
  std::vector<INode> old_files;
  if (proto.old_files_size() > 0) {
    CHECK_EQ(proto.old_files_size(), files_to_update.size());
    old_files.reserve(proto.old_files_size());
    for (const auto& old_file : proto.old_files()) {
      old_files.emplace_back(old_file);
    }
  }
  meta_storage_->OrderedUpdateUfsINodes(
      op->txid(), files_to_update, &old_files, ctx->done);
}

void NameSpace::ApplyCfsOpAccSyncUpdateINode(
    std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAccSyncUpdateINode, AccSyncUpdateINodeOpBody);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    if (proto.has_old_inode()) {
      CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
    }
  }

  // 2. set callback
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", inode id: " << op->GetProto().inode().id()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_update_inode_time_)->Update(time_cost_us);
  });
  ApplyCfsOpSetupClosure(
      ctx, dynamic_cast<ApplyClosure*>(ctx->done), proto.inode().id());

  // 3. submit WriteTask async
  INode inode = proto.inode();
  SnapshotLog inode_snaplog;
  meta_storage_->OrderedUpdateINode(
      &inode,
      proto.has_old_inode() ? &proto.old_inode() : nullptr,
      inode_snaplog,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyCfsOpAccSyncAddFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAccSyncAddFile, AccSyncAddFileOpBody);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    std::string normalized_parent_path;
    CHECK(NormalizePath(proto.path() + "/..", "", &normalized_parent_path));
    CheckDBINodeForPhysicalApply(normalized_parent_path, proto.parent());
    if (proto.has_old_file_to_del()) {
      CheckDBINodeForPhysicalApply(proto.path(), proto.old_file_to_del());
    }
  }

  // 2. set callback
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto&& proto = op->GetProto();
    UpdateLastINodeId(proto.file_blocks().inode().id());
    for (auto&& b : proto.file_blocks().inode().blocks()) {
      UpdateLastAllocatedBlockId(b.blockid());
      UpdateGenerationStampV2(b.genstamp());
    }

    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto&& b : proto.file_blocks().inode().blocks()) {
      block_manager_->ProcessPendingFutureBlks(
          b.blockid(), current_block_id, current_gsv2);
    }
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_add_file_time_)->Update(time_cost_us);
  });
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.file_blocks().inode().id());

  // 3. submit WriteTask async
  INodeWithBlocks ib;
  {
    ib.node = proto.file_blocks().inode();
    ib.blocks.reserve(proto.file_blocks().block_info_size());
    for (const auto& b : proto.file_blocks().block_info()) {
      ib.blocks.emplace_back(b);
    }
  }
  meta_storage_->OrderedOverwriteUfsFile(
      op->txid(),
      ib,
      proto.has_old_file_to_del() ? &proto.old_file_to_del() : nullptr,
      ctx->done);
}

void NameSpace::ApplyCfsOpAccPersistFile(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAccPersistFile, AccPersistFileOpBody);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    if (proto.has_old_inode()) {
      CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
    }
  }

  // 2. set callback
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    if (s.IsOK() && NameSpace::IsAccMode() &&
        !FLAGS_enable_write_back_task_persistence) {
      CheckAccINode(op->GetProto().inode());
      ufs_env_->upload_monitor()->EraseTask(op->GetProto().inode().id());
    }
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", inode id: " << op->GetProto().inode().id()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_persist_file_time_)->Update(time_cost_us);
  });
  ApplyCfsOpSetupClosure(
      ctx, dynamic_cast<ApplyClosure*>(ctx->done), proto.inode().id());

  // 3. submit WriteTask async
  INode inode = proto.inode();
  SnapshotLog inode_snaplog;
  meta_storage_->OrderedUpdateINode(
      &inode,
      proto.has_old_inode() ? &proto.old_inode() : nullptr,
      inode_snaplog,
      op->txid(),
      ctx->done);
}

void NameSpace::ApplyCfsOpSetReplication(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetReplicationV2, INodeToSetReplication);

  // 1. check editlog
  CHECK_EQ(proto.inode().type(), INode_Type_kFile);
  CHECK_NE(proto.inode().replication(), 0);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  // no need to modify blockmap in standby.
  // namenode will check blocks' replica after failover.
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetStoragePolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetStoragePolicyV2, INodeToSetStoragePolicy);

  // 1. check editlog
  CHECK_EQ(proto.inode().storage_policy_id(), proto.policy_id());
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetReplicaPolicy(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "unsupported setacl editlog";
}

void NameSpace::ApplyCfsOpSetDirReplicaPolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetDirReplicaPolicyV2, DirToSetReplicaPolicy);

  // 1. check editlog
  CHECK_EQ(proto.inode().type(), INode_Type_kDirectory);
  if (proto.policy_id() == kCentralizePolicy) {
    CHECK(data_centers_->IsDataCenterValidForCentralizePolicy(proto.dc()));
  } else if (proto.policy_id() == kDistributePolicy) {
    CHECK(data_centers_->IsDataCenterValidForDistributePolicy(proto.dc()));
  } else {
    CHECK_EQ(proto.policy_id(), kNonePolicy);
  }
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (proto.policy_id() == kNonePolicy) {
      policy_manager_->ReplicaPolicyInstance()->RemovePolicy(proto.path());
    } else {
      ReplicaPolicy policy;
      policy.set_distributed(proto.policy_id() == kDistributePolicy);
      auto dcs = cnetpp::base::StringUtils::SplitByChars(proto.dc(), ",");
      for (const auto& d : dcs) {
        policy.add_dc(d);
      }
      policy_manager_->ReplicaPolicyInstance()->UpdatePolicy(proto.path(),
                                                             policy);
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetPermissions(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetPermissionsV2, INodeToSetPermissions);

  // 1. check editlog
  // nothing
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetOwner(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetOwnerV2, INodeToSetOwner);

  // 1. check editlog
  CHECK_EQ(proto.inode().permission().username(), proto.username());
  CHECK_EQ(proto.inode().permission().groupname(), proto.groupname());
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetAcl(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "unsupported setacl editlog";
}

void NameSpace::ApplyCfsOpSetXAttrs(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetXAttrsV2, INodeToSetXAttrs);

  // 1. check editlog
  CHECK_GT(proto.xattrs_size(), 0);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    policy_manager_->ReloadINodeXAttr(
        proto.path(), proto.inode(), proto.xattrs(), {});

    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<SetXAttrResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpRemoveXAttrs(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRemoveXAttrsV2, INodeToRemoveXAttrs);

  // 1. check editlog
  CHECK_GT(proto.xattrs_size(), 0);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    policy_manager_->ReloadINodeXAttr(
        proto.path(), proto.inode(), {}, proto.xattrs());
    {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<RemoveXAttrResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetQuota(std::shared_ptr<ApplyContext> ctx) {
  LOG(FATAL) << "unsupported setquota editlog";
}

void NameSpace::ApplyCfsOpSetTimes(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetTimesV2, INodeToSetTimes);

  // 1. check editlog
  CHECK_GT(proto.inode().atime(), 0);
  CHECK_GT(proto.inode().mtime(), 0);
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    &proto.old_inode(),
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done);
}

void NameSpace::ApplyCfsOpSetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetLifecyclePolicy, LifecyclePolicyToBeSet);

  // 1. check editlog
  // nothing

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<SetLifecyclePolicyResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode_id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedPutLifecyclePolicy(proto.inode_id(),
                                           proto.timestamp_ms(),
                                           proto.policy(),
                                           op->txid(),
                                           ctx->done);
}

void NameSpace::ApplyCfsOpUnsetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpUnsetLifecyclePolicy, LifecyclePolicyToBeUnset);

  // 1. check editlog
  // nothing

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    if (proto.has_log_rpc_info()) {
      auto rpc_info = proto.log_rpc_info();
      auto resp = std::make_shared<UnsetLifecyclePolicyResponseProto>();
      AddRetryCacheEntry(rpc_info.rpc_client_id(),
                         rpc_info.rpc_call_id(),
                         std::move(resp));
    }
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode_id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedDeleteLifecyclePolicy(proto.inode_id(),
                                              op->txid(),
                                              ctx->done);
}

void NameSpace::ApplyCfsOpSetAZBlacklist(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpSetAZBlacklist, AZBlacklist);

  // 1. check editlog
  // nothing

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx, dynamic_cast<ApplyClosure*>(ctx->done));

  // 3. submit WriteTask async
  meta_storage_->PutAZBlacklistAsync(proto.azs(), op->txid(), ctx->done);
}

void NameSpace::ApplyCfsOpMergeBlock(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpMergeBlock, FileAndBlockToBeMerge);

  // 1. check editlog
  if (ctx->physical_apply_check_db) {
    CheckDBINodeForPhysicalApply(proto.path(), proto.old_inode());
  }

  // XXX(xuex)
  // Here we break the rule that physical-apply should update in-memory data
  // until WriteTask written. It's because some codes, like FBR/IBR, has very
  // implicit assertion that any UC BIP on-disk must be indexed by 'uc_state_'.
  // Considering that physical-ApplyTasks are all assigned to apply_threads_[0]
  // and executed in order, those updates on block is safe.
  //
  // When applying edit logs to create new blocks (e.g., add or merge block edit
  // logs), make the in-memory modifications before updating on-disk. This
  // prevents IBR/FBR from loading the block from disk, which could result in
  // the block being loaded twice — once for IBR/FBR and once for applying edit
  // logs - leading to a potential core dump at BlockMapSlice::LoadBlock. For
  // modifying existing blocks, we can perform the modifications in
  // apply_threads_[0] or callback threads. Using callback threads is more
  // efficient.
  {
    const INode& inode = proto.inode();
    const BlockInfoProto& bip = proto.merged_bip();

    UpdateLastAllocatedBlockId(bip.block_id());
    UpdateGenerationStampV2(bip.gen_stamp());

    // Hdfs mode and Acc mode have different logic for block maanger
    if (NameSpace::IsHdfsMode()) {
      switch (bip.state()) {
        case BlockInfoProto::kUnderConstruction: {
          block_manager_->LoadBlock(
              inode.id(),
              inode.parent_id(),
              static_cast<uint8_t>(inode.replication()),
              bip.write_mode(),
              Block(bip.block_id(),
                    static_cast<uint32_t>(bip.num_bytes()),
                    bip.gen_stamp()),
              /*dn_id*/std::vector<DatanodeID>(),
              BlockUCState::kUnderConstruction);
        } break;
        case BlockInfoProto::kCommitted:
        case BlockInfoProto::kComplete: {
          block_manager_->UpdateMergeBlockUCState(
              Block{bip.block_id(),
                    static_cast<uint32_t>(bip.num_bytes()),
                    bip.gen_stamp()},
              BlockUCState::kComplete);
        } break;
        case BlockInfoProto::kPersisted: {
          block_manager_->UpdateMergeBlockUCState(
              Block{bip.block_id(),
                    static_cast<uint32_t>(bip.num_bytes()),
                    bip.gen_stamp()},
              BlockUCState::kPersisted);
        } break;
        default:
          LOG(FATAL) << "Unexpected bip state " << bip.ShortDebugString();
          MFC(LoggerMetrics::Instance().error_)->Inc();
          break;
      }
    }

    // ACC only support merge persisted blocks
    if (NameSpace::IsAccMode()) {
      switch (bip.state()) {
        case BlockInfoProto::kPersisted: {
          // Do nothing
          break;
        }
        default:
          LOG_WITH_LEVEL(ERROR)
              << "Unexpected bip state " << bip.ShortDebugString();
          MFC(LoggerMetrics::Instance().error_)->Inc();
          break;
      }
    }
  }
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx,
                             proto.old_inode(),
                             proto.inode(),
                             proto.ancestors_id());
  meta_storage_->OrderedUpdateINodeMergeBlock(
      proto.mutable_inode(),
      &proto.old_inode(),
      proto.merged_bip(),
      std::vector<BlockProto>{proto.depred_blks().begin(),
                              proto.depred_blks().end()},
      *proto.mutable_inode_snaplog(),
      op->txid(),
      ctx->done,
      STAT_RECORDER_PTR);
}

void NameSpace::ApplyCfsOpAllowSnapshot(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpAllowSnapshotV2, SnapshotToAllow);

  // 1. check editlog
  CHECK(proto.inode().is_snapshottable());

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    auto st = snapshot_manager_->AllowSnapshot(
        proto.inode().id(), proto.path());
    CHECK(st.IsOK());
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINodeAndSnapshotData(proto.inode(),
                                                   MetaStorage::ALLOW_SNAPSHOT,
                                                   &proto.path(),
                                                   nullptr /*snapshot_id*/,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyCfsOpDisallowSnapshot(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDisallowSnapshotV2, SnapshotToDisallow);

  // 1. check editlog
  CHECK_EQ(proto.inode().snapshots_size(), 0);
  CHECK(!proto.inode().is_snapshottable());

  // 2. set callback
  auto cb = [this, op, &proto] (const Status& s) {
    REGISTER_CB_EXEC_TIME_METRICS(op);
    CHECK(s.IsOK());
    auto st = snapshot_manager_->DisallowSnapshot(
        proto.inode().id(), proto.path());
    CHECK(st.IsOK());
  };
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id(),
                         cb);

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINodeAndSnapshotData(proto.inode(),
                                                   MetaStorage::DISALLOW_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   nullptr /*snapshot_id*/,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyCfsOpCreateSnapshot(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpCreateSnapshotV2, SnapshotToCreate);

  // 1. check editlog
  CHECK_EQ(proto.inode().mtime(), proto.timestamp_in_ms());
  CHECK_GT(proto.inode().snapshots_size(), 0);
  auto new_snapshot = proto.mutable_inode()->mutable_snapshots()->rbegin();
  CHECK_EQ(new_snapshot->snapshot_id(), proto.new_snapshot_id());
  CHECK_EQ(new_snapshot->name(), proto.name());
  CHECK(!new_snapshot->deleted());
  CHECK_EQ(new_snapshot->mtime(), proto.timestamp_in_ms());
  // XXX 'create_txid' field is not recorded in editlog
  new_snapshot->set_create_txid(op->txid());

  // 2. set callback
  // XXX Unlike Active logic, here we do not call AcquireSnapshotID().
  // At Active side, new_snapshot_id is maintained as an atomic variable and
  // updated without any lock, before acquiring txid. When two CreateSnapshot
  // happen concurrently, reversed order between 'new_snapshot_id' and 'txid'
  // is possible.
  // In this case, Standby will find new_snapshot_id-2 in editlog with txid-1,
  // but SnapshotManager::AcquireSnapshotID() return new_snapshot_id-1.
  // To solve this, we encode the new_snapshot_id into NameSpaceInfoDelta.
  // and ReloadSnapshotID() during transit to Active.
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  uint64_t snapshot_id = proto.new_snapshot_id();
  meta_storage_->OrderedUpdateINodeAndSnapshotData(proto.inode(),
                                                   MetaStorage::CREATE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   &snapshot_id,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyCfsOpDeleteSnapshot(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpDeleteSnapshotV2, SnapshotToDelete);

  // 1. check editlog
  for (auto snapshot : proto.inode().snapshots()) {
    if (snapshot.name() == proto.name()) {
      CHECK(snapshot.deleted());
    }
  }
  CHECK_EQ(proto.inode().mtime(), proto.timestamp_in_ms());

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTask async
  meta_storage_->OrderedUpdateINodeAndSnapshotData(proto.inode(),
                                                   MetaStorage::DELETE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   nullptr /*snapshot_id*/,
                                                   op->txid(),
                                                   ctx->done);
}

void NameSpace::ApplyCfsOpRenameSnapshot(std::shared_ptr<ApplyContext> ctx) {
  DECODE_CFSOP_PROTO(ctx, OpRenameSnapshotV2, SnapshotToRename);

  // 1. check editlog
  CHECK_NE(proto.old_name(), proto.new_name());
  bool found_renamed = false;
  for (auto snapshot : proto.inode().snapshots()) {
    if (snapshot.has_deleted() && snapshot.deleted()) {
      continue;
    }
    if (snapshot.name() == proto.new_name()) {
      found_renamed = true;
    }
  }
  CHECK(found_renamed);
  CHECK_EQ(proto.inode().mtime(), proto.timestamp_in_ms());

  // 2. set callback
  ApplyCfsOpSetupClosure(ctx,
                         dynamic_cast<ApplyClosure*>(ctx->done),
                         proto.inode().id());

  // 3. submit WriteTash async
  meta_storage_->OrderedUpdateINode(proto.mutable_inode(),
                                    nullptr,
                                    *proto.mutable_inode_snaplog(),
                                    op->txid(),
                                    ctx->done,
                                    nullptr);
}

void NameSpace::WaitNoPending() {
  while (true) {
    // 1. wait pending tasks done, make sure all write tasks submitted
    while (true) {
      uint32_t us = FLAGS_edit_log_applyer_wait_no_pending_sleep_us;
      if (NumPendingLogToApply() == 0) {
        break;
      }
      usleep(us);
    }

    // 2. wait write tasks and callbacks done
    meta_storage_->WaitNoPending(true);
    break;
  }
}

uint64_t NameSpace::NumPendingLogToApply() {
  uint64_t result = 0;
  for (size_t i = 0; i < apply_threads_.size(); i++) {
    const auto& thread_pool = apply_threads_[i];
    if (thread_pool && (thread_pool->PendingCount() > 0 ||
                        thread_pool->NumRunningTasks() > 0)) {
      result += thread_pool->PendingCount() + thread_pool->NumRunningTasks();
      VLOG(2) << absl::StrFormat(
          "apply-thread %lu pending %lu running %lu",
          i, thread_pool->PendingCount(), thread_pool->NumRunningTasks());
    }
  }

  return result;
}

int64_t NameSpace::GetLastAppliedOrWrittenTxId() {
  return meta_storage_->GetLastCkptTxId();
}

void NameSpace::TailerCatchupTxid() {
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  last_catchup_txid_ms_ = now;
}

int64_t NameSpace::SinceLastCatchupTxid() {
  if (ha_state_ == nullptr) {
    // not start
    return -1;
  }
  if (ha_state_->IsActive() && !ha_state_->InTransition()) {
    // active
    return -1;
  }
  auto last = last_catchup_txid_ms_.load();
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  return now > last ? now - last : 0;
}

void NameSpace::ApplyOpAccSyncListingBatchAdd(
    std::shared_ptr<ApplyContext> ctx) {
  auto&& op = std::static_pointer_cast<OpAccSyncListingBatchAdd>(ctx->op);
  auto&& op_proto = op->GetProto();
  int64_t txid = op->txid();

  VLOG(8) << "Apply op: " << op->op_name() << ", dir: " << op_proto.dir_path()
          << ", inode id: " << op_proto.dir_inode().id()
          << ", subfiles: " << op->GetProto().files_to_add_size();

  {
    METRIC_WATCH_START(op_acc_sync_batch_add_lock_acquire_time_)
    INode dir;
    StatusCode sc =
        GetLastINodeInPath(ctx->acc_path_components, &dir, nullptr, nullptr);
    CHECK(sc == kOK);
    // dir-inode is not under protection by wlock
    //CompareINodeFromMetaStorageAndEditLog(dir, op_proto.dir_inode());
    METRIC_WATCH_STOP(op_acc_sync_batch_add_lock_acquire_time_)
  }

  uint64_t max_inode_id = 0;
  BlockID max_block_id = 0;
  uint64_t max_gsv2 = 0;
  std::vector<INodeWithBlocks> new_ufs_files;
  new_ufs_files.reserve(op_proto.files_to_add_size());
  for (size_t i = 0; i < op_proto.files_to_add_size(); ++i) {
    auto&& f = op_proto.files_to_add(i);
    auto&& name = f.inode().name();

    if (f.inode().id() > max_inode_id) {
      max_inode_id = f.inode().id();
    }

    INodeWithBlocks ib;
    ib.node = f.inode();
    for (size_t k = 0; k < f.block_info_size(); ++k) {
      ib.blocks.push_back(f.block_info(k));

      if (f.block_info(k).block_id() > max_block_id) {
        max_block_id = f.block_info(k).block_id();
        max_gsv2 = f.block_info(k).gen_stamp();
      }
    }
    new_ufs_files.emplace_back(std::move(ib));
  }
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto&& op_proto = op->GetProto();
    for (size_t i = 0; i < op_proto.files_to_add_size(); ++i) {
      auto&& f = op_proto.files_to_add(i);
      auto current_block_id = last_allocated_block_id();
      auto current_gsv2 = generation_stamp_v2();
      for (auto&& b : f.inode().blocks()) {
        block_manager_->ProcessPendingFutureBlks(
            b.blockid(), current_block_id, current_gsv2);
      }
    }

    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().dir_path()
              << ", inode id: " << op->GetProto().dir_inode().id()
              << ", subfiles: " << op->GetProto().files_to_add_size()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_batch_add_time_)->Update(time_cost_us);
  });

  if (max_inode_id > 0) {
    UpdateLastINodeId(max_inode_id);
  }
  if (max_block_id > 0) {
    UpdateLastAllocatedBlockId(max_block_id);
    UpdateGenerationStampV2(max_gsv2);
  }
  meta_storage_->OrderedCreateUfsFiles(
      txid, new_ufs_files, &op_proto.dir_inode(), {ctx->done});
}

void NameSpace::ApplyOpAccSyncListingBatchUpdate(
    std::shared_ptr<ApplyContext> ctx) {
  auto&& op = std::static_pointer_cast<OpAccSyncListingBatchUpdate>(ctx->op);
  auto&& op_proto = op->GetProto();
  int64_t txid = op->txid();

  VLOG(8) << "Apply op: " << op->op_name() << ", dir: " << op_proto.dir_path()
          << ", inode id: " << op_proto.dir_inode().id()
          << ", subfiles: " << op->GetProto().files_to_update_size();

  {
    METRIC_WATCH_START(op_acc_sync_batch_add_lock_acquire_time_)
    INode dir;
    StatusCode sc =
        GetLastINodeInPath(ctx->acc_path_components, &dir, nullptr, nullptr);
    CHECK(sc == kOK);
    // dir-inode is not under protection by wlock
    //CompareINodeFromMetaStorageAndEditLog(dir, op_proto.dir_inode());
    METRIC_WATCH_STOP(op_acc_sync_batch_add_lock_acquire_time_)
  }

  std::vector<INode> files_to_update;
  files_to_update.reserve(op_proto.files_to_update_size());
  for (size_t i = 0; i < op_proto.files_to_update_size(); ++i) {
    auto&& inode = op_proto.files_to_update(i);
    auto&& name = inode.name();
    files_to_update.emplace_back(inode);
  }
  std::vector<INode> old_files;
  if (op_proto.old_files_size() > 0) {
    CHECK_EQ(op_proto.old_files_size(), files_to_update.size());
    old_files.reserve(op_proto.old_files_size());
    for (const auto& old_file : op_proto.old_files()) {
      old_files.emplace_back(old_file);
    }
  }
  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().dir_path()
              << ", inode id: " << op->GetProto().dir_inode().id()
              << ", subfiles: " << op->GetProto().files_to_update_size()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_batch_update_time_)->Update(time_cost_us);
  });
  meta_storage_->OrderedUpdateUfsINodes(
      txid, files_to_update, &old_files, ctx->done);
}

void NameSpace::ApplyOpAccSyncUpdateINode(std::shared_ptr<ApplyContext> ctx) {
  auto&& op = std::static_pointer_cast<OpAccSyncUpdateINode>(ctx->op);
  auto&& op_proto = op->GetProto();
  int64_t txid = op->txid();

  VLOG(8) << "Apply op: " << op->op_name() << ", dir: " << op_proto.path()
          << ", inode id: " << op_proto.inode().id();

  INodeInPath iip;
  {
    METRIC_WATCH_START(op_acc_sync_update_inode_lock_acquire_time_)
    INodeInPath iip_from_ms;
    StatusCode sc = GetLastINodeInPath(
        ctx->acc_path_components, &iip_from_ms, nullptr, nullptr);
    CHECK(sc == kOK);
    CHECK_EQ(iip_from_ms.Inode().id(), op_proto.inode().id());
    CHECK(!iip_from_ms.NeedBackupForDeletion());
    CHECK(!iip_from_ms.NeedBackupForModification());
    iip.MutableInodeUnsafe().CopyFrom(op_proto.inode());
    iip.CollectSnapshotInfo(op_proto.inode());
    iip.CollectAttrs(op_proto.inode());
    iip.FinalizeAttrs();
    CHECK(!iip.NeedBackupForDeletion());
    CHECK(!iip.NeedBackupForModification());
    METRIC_WATCH_STOP(op_acc_sync_update_inode_lock_acquire_time_)
  }
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", inode id: " << op->GetProto().inode().id()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_update_inode_time_)->Update(time_cost_us);
  });
  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(),
      op_proto.has_old_inode() ? &op_proto.old_inode() : nullptr,
      inode_snaplog,
      txid,
      ctx->done);
}

void NameSpace::ApplyOpAccSyncAddFile(std::shared_ptr<ApplyContext> ctx) {
  auto&& op = std::static_pointer_cast<OpAccSyncAddFile>(ctx->op);
  auto&& op_proto = op->GetProto();
  int64_t txid = op->txid();

  VLOG(8) << "Apply op: " << op->op_name() << ", dir: " << op_proto.path()
          << ", parent id: " << op_proto.parent().id()
          << ", file id: " << op_proto.file_blocks().inode().id()
          << ", file blocks: " << op_proto.file_blocks().block_info_size()
          << ", del: "
          << (op_proto.has_old_file_to_del() ? op_proto.old_file_to_del().id()
                                             : 0);

  {
    METRIC_WATCH_START(op_acc_sync_add_file_lock_acquire_time_)
    INode node;
    StatusCode sc =
        GetLastINodeInPath(ctx->acc_path_components, &node, nullptr, nullptr);
    if (op_proto.has_old_file_to_del()) {
      CHECK(sc == kOK);
      CompareINodeFromMetaStorageAndEditLog(node, op_proto.old_file_to_del());
    } else {
      CHECK(sc == kFileNotFound);
    }
    METRIC_WATCH_STOP(op_acc_sync_add_file_lock_acquire_time_)
  }

  ctx->done->add_post_callback([this, ctx, op](const Status& s) {
    auto&& op_proto = op->GetProto();
    auto current_block_id = last_allocated_block_id();
    auto current_gsv2 = generation_stamp_v2();
    for (auto&& b : op_proto.file_blocks().inode().blocks()) {
      block_manager_->ProcessPendingFutureBlks(
          b.blockid(), current_block_id, current_gsv2);
    }

    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_sync_add_file_time_)->Update(time_cost_us);
  });

  INodeWithBlocks ib;
  {
    ib.node = op_proto.file_blocks().inode();
    ib.blocks.reserve(op_proto.file_blocks().block_info_size());
    for (int i = 0; i < op_proto.file_blocks().block_info_size(); ++i) {
      auto&& b = op_proto.file_blocks().block_info(i);
      ib.blocks.emplace_back(b);
    }
  }

  UpdateLastINodeId(ib.node.id());
  BlockID max_block_id = 0;
  uint64_t max_gsv2 = 0;
  for (auto&& b : ib.node.blocks()) {
    if (b.blockid() > max_block_id) {
      max_block_id = b.blockid();
      max_gsv2 = b.genstamp();
    }
  }
  if (max_block_id > 0) {
    UpdateLastAllocatedBlockId(max_block_id);
    UpdateGenerationStampV2(max_gsv2);
  }
  const INode* del =
      op_proto.has_old_file_to_del() ? &op_proto.old_file_to_del() : nullptr;
  meta_storage_->OrderedOverwriteUfsFile(txid, ib, del, ctx->done);
}

void NameSpace::ApplyOpAccPersistFile(std::shared_ptr<ApplyContext> ctx) {
  auto&& op = std::static_pointer_cast<OpAccPersistFile>(ctx->op);
  auto&& op_proto = op->GetProto();
  int64_t txid = op->txid();

  VLOG(8) << "Apply op: " << op->op_name() << ", dir: " << op_proto.path()
          << ", inode id: " << op_proto.inode().id();

  INodeInPath iip;
  {
    METRIC_WATCH_START(op_acc_persist_file_lock_acquire_time_)
    INodeInPath iip_from_ms;
    StatusCode sc = GetLastINodeInPath(
        ctx->acc_path_components, &iip_from_ms, nullptr, nullptr);
    CHECK(sc == kOK);
    CHECK_EQ(iip_from_ms.Inode().id(), op_proto.inode().id());
    CHECK(!iip_from_ms.NeedBackupForDeletion());
    CHECK(!iip_from_ms.NeedBackupForModification());
    iip.MutableInodeUnsafe().CopyFrom(op_proto.inode());
    iip.CollectSnapshotInfo(op_proto.inode());
    iip.CollectAttrs(op_proto.inode());
    iip.FinalizeAttrs();
    CHECK(!iip.NeedBackupForDeletion());
    CHECK(!iip.NeedBackupForModification());
    METRIC_WATCH_STOP(op_acc_persist_file_lock_acquire_time_)
  }
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  ctx->done->add_post_callback([this, ctx, op, op_proto](const Status& s) {
    if (s.IsOK() && NameSpace::IsAccMode() &&
        !FLAGS_enable_write_back_task_persistence) {
      CheckAccINode(op->GetProto().inode());
      ufs_env_->upload_monitor()->EraseTask(op->GetProto().inode().id());
    }
    auto time_cost_us = std::chrono::duration_cast<std::chrono::microseconds>(
                            ctx->sw->GetTime())
                            .count();
    if (time_cost_us > FLAGS_edit_log_slow_op_us) {
      VLOG(6) << "Detect edit log apply slow "
              << ", op_name: " << op->op_name()
              << ", path: " << op->GetProto().path()
              << ", inode id: " << op->GetProto().inode().id()
              << ", apply op cost us: " << time_cost_us;
    }
    MFH(metrics_.op_acc_persist_file_time_)->Update(time_cost_us);
  });
  meta_storage_->OrderedUpdateINode(
      &iip.MutableInode(),
      op_proto.has_old_inode() ? &op_proto.old_inode() : nullptr,
      inode_snaplog,
      txid,
      ctx->done);
}

void NameSpace::ApplyOpAccUpdateBlockInfo(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpAccUpdateBlockInfo>(ctx->op);
  meta_storage_->OrderedUpdateUfsBlockInfo(
      op->txid(),
      op->GetProto().bip(),
      op->GetProto().has_old_bip() ? &op->GetProto().old_bip() : nullptr,
      ctx->done);
}

void NameSpace::ApplyOpConcatV2(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpConcatV2>(ctx->op);
  const auto& proto = op->GetProto();

  VLOG(9) << op->op_name() << ": " << proto.target_path()
          << " srcs:" << absl::StrJoin(proto.src_paths(), ",")
           << ", blocks num: " << proto.target_bips().size();

  // pre check
  // Active NN must promise: msgs from edit log op always match each other.
  CHECK_EQ(proto.src_inodes().size(), proto.src_paths().size());

  // get inode
  METRIC_WATCH_START(op_concat_get_last_inode_time_);
  std::vector<INode> ancestors;
  INodeInPath iip;
  INodeInPath parent_iip;
  std::vector<cnetpp::base::StringPiece> path_components;
  CHECK(SplitPath(proto.target_path(), &path_components));
  CHECK_EQ(GetLastINodeInPath(path_components, &iip, &ancestors, &parent_iip),
           StatusCode::kOK)
      << " op=" << op->op_name() << " path=" << proto.target_path();

  CHECK(!iip.IsUnderSnapshot());
  auto& target_inode = iip.MutableInode();
  auto& parent = parent_iip.MutableInode();
  CHECK(target_inode.type() == INode_Type_kFile && !target_inode.has_uc());
  INode old_file(target_inode);

  std::set<std::string> exist_paths{proto.target_path()};
  std::vector<INode> src_inodes;

  for (auto& src : proto.src_paths()) {
    exist_paths.insert(src);

    INodeInPath src_iip;
    INodeInPath src_parent_iip;
    std::vector<INode> ancestors;
    std::vector<cnetpp::base::StringPiece> src_path_components;
    CHECK(SplitPath(src, &src_path_components)) << src;
    CHECK_EQ(GetLastINodeInPath(
                 src_path_components, &src_iip, &ancestors, &src_parent_iip),
             StatusCode::kOK);
    CHECK(src_iip.Inode().type() == INode_Type_kFile);
    CHECK(src_iip.Inode().blocks_size() > 0);
    CHECK(!src_iip.Inode().has_uc());

    src_inodes.emplace_back(src_iip.OldInode());
  }
  CHECK_EQ(exist_paths.size(), proto.src_paths().size() + 1);
  if (IsAccMode() && ufs_env_) {
    UploadPolicy policy;

    bool inode_allow_upload = !ufs_upload_monitor()->IsNoUploadWithoutCache(
        proto.parent_path(), nullptr, &policy);

    if (inode_allow_upload) {
      LOG(FATAL) << "concat not allowed upload";
    }
  }

  // get bips
  auto s = IsUfsFileAllowConcat(proto.target_path(), target_inode);
  CHECK(s.IsOK()) << "IsUfsFileAllowConcat target_path=" << proto.target_path()
                  << " target_inode=" << target_inode.ShortDebugString()
                  << " status=" << s.ToString();

  for (int i = 0; i < src_inodes.size(); ++i) {
    s = IsUfsFileAllowConcat(proto.src_paths().Get(i), src_inodes[i]);
    CHECK(s.IsOK()) << "IsUfsFileAllowConcat src_path="
                    << proto.src_paths().Get(i)
                    << " src_inode=" << src_inodes[i].ShortDebugString()
                    << " status=" << s.ToString();
  }

  std::vector<BlockInfoProto> target_bips;

  s = CheckAndGetBIPForConcat(proto.target_path(), target_inode, &target_bips);
  CHECK(s.IsOK()) << "CheckAndGetBIPForConcat target_path="
                  << proto.target_path()
                  << " target_inode=" << target_inode.ShortDebugString()
                  << " status=" << s.ToString();
  for (int i = 0; i < src_inodes.size(); ++i) {
    s = CheckAndGetBIPForConcat(
        proto.src_paths().Get(i), src_inodes[i], &target_bips);
    CHECK(s.IsOK()) << "CheckAndGetBIPForConcat src_path="
                    << proto.src_paths().Get(i)
                    << " src_inode=" << src_inodes[i].ShortDebugString()
                    << " status=" << s.ToString();
  }
  CHECK_EQ(target_bips.size(), proto.target_bips().size());

  std::vector<BlockID> target_blk_ids;
  for (int i = 0; i < target_bips.size(); ++i) {
    target_blk_ids.push_back(target_bips[i].block_id());
    CHECK_EQ(target_bips[i].block_id(), proto.target_bips().Get(i).block_id());
  }
  METRIC_WATCH_STOP(op_concat_get_last_inode_time_);

  // do concat
  DoConcatFileToTarget(src_inodes, &target_bips, &target_inode);

  target_inode.set_mtime(static_cast<uint64_t>(proto.timestamp_in_ms()));
  parent.set_mtime(static_cast<uint64_t>(proto.timestamp_in_ms()));

  // post check
  CHECK(!ctx->done->callback());
  ctx->done->set_callback(
      [this, target_blk_ids, target_inode](const Status& s) {
        CHECK(s.IsOK());

        for (auto b : target_blk_ids) {
          block_manager_->UpdateINode(b, target_inode.id());
        }
      });

  CompareINodeFromMetaStorageAndEditLog(
      /*inode_from_ms=*/target_inode,
      /*inode_from_editlog=*/proto.target_inode());
  for (int i = 0; i < target_bips.size(); ++i) {
    if (!CompareBIPFromMetaStorageAndEditLog(
            /*bip_from_ms=*/target_bips[i],
            /*bip_from_editlog=*/proto.target_bips().Get(i))) {
      LOG(FATAL) << "INode mismatched, block_from_ms="
                 << ToJsonCompactString(target_bips[i])
                 << " block_from_editlog="
                 << ToJsonCompactString(proto.target_bips().Get(i))
                 << " txid=" << op->txid();
    }
  }

  // write back
  DECLARE_STAT_RECORDER(meta_storage_, ctx);
  STAT_RECORDER_INODE_UPDATE(ctx, old_file, target_inode, ancestors);
  meta_storage_->OrderedConcatINode(target_inode,
                                    nullptr,
                                    src_inodes,
                                    target_bips,
                                    parent,
                                    op->txid(),
                                    ctx->done,
                                    STAT_RECORDER_PTR);
  auto time_cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(ctx->sw->GetTime())
          .count();
  if (time_cost_us > FLAGS_edit_log_slow_op_us) {
    VLOG(6) << "Detect edit log apply slow "
            << ", op_name: " << op->op_name()
            << ", path: " << proto.target_path()
            << ", apply op cost us: " << time_cost_us;
  }
}

void NameSpace::ApplyOpBatchCreateFile(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpBatchCreateFile>(ctx->op);
  BatchInodeToCreate& proto = op->GetProto();
  CHECK_GT(proto.files_size(), 0);
  std::string first_path = proto.files().Get(0).path();
  VLOG(9) << op->op_name() << ": " << first_path;
  VLOG(9) << "proto=" << proto.ShortDebugString();

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  // check paths
  METRIC_WATCH_START(op_batch_create_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (int i = 0; i < proto.files_size(); ++i) {
      paths.push_back(proto.files().Get(i).path());
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  // get inodes and check inode
  INode parent_inode;
  std::vector<INode> ancestors;
  std::vector<INode> src_inodes;
  std::vector<INode> overwritten_inodes;
  std::vector<BlockInfoProto> add_block_bips;
  std::vector<std::vector<DatanodeID>> bip_expected_locs;
  std::vector<Block> future_blks;
  {
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/false,
                                    /*push_even_if_src_notfound=*/true,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();

    for (const auto& inode : src_inodes) {
      if (inode.type() == INode_Type_kDirectory) {
        auto s = Status(
            JavaExceptions::Exception::kFileAlreadyExistsException,
            absl::StrFormat(
                "[AsyncBatchCreate] %s/%s already exists as a directory.",
                parent_path,
                inode.name()));
        CHECK(false) << s.ToString();
      }
      if (inode.id() != kInvalidINodeId) {
        overwritten_inodes.push_back(inode);
      }
    }
    src_inodes.clear();

    // no overwrite, all inode is new inode
    CHECK_EQ(src_inodes.size(), 0);
    CHECK_EQ(proto.overwritten_inodes().size(), overwritten_inodes.size());
    CHECK_EQ(proto.overwritten_bips().size(), 0);

    for (int i = 0; i < overwritten_inodes.size(); ++i) {
      CHECK_EQ(overwritten_inodes[i].id(),
               proto.overwritten_inodes().Get(i).id());
    }

    for (int i = 0; i < paths.size(); ++i) {
      const auto& path = paths[i];

      const auto& file_proto = proto.files().Get(i);
      src_inodes.push_back(file_proto.inode());

      auto& new_file = src_inodes[i];

      // update last inode id
      UpdateLastINodeId(new_file.id());

      // lease
      lease_manager_->AddLease(new_file.uc().client_name(), new_file.id());

      // block_manager
      std::vector<cloudfs::BlockProto> blks_inode;
      for (const auto& blk : new_file.blocks()) {
        blks_inode.push_back(blk);
      }

      // feature: add block
      std::vector<BlockInfoProto> add_block_bips_per_file;
      std::vector<std::vector<DatanodeID>> bip_expected_locs_per_file;
      std::vector<Block> future_blks_per_file;
      std::vector<Block> blks_bip_per_file;
      if (file_proto.add_block_bips_with_locs_size() > 0) {
        for (const auto& add_block_bip_with_locs :
             file_proto.add_block_bips_with_locs()) {
          add_block_bips_per_file.emplace_back(add_block_bip_with_locs.bip());
          std::vector<DatanodeID> dns;
          for (const auto& dn_uuid : add_block_bip_with_locs.dns()) {
            auto dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
            if (dn == nullptr) {
              LOG(ERROR) << "Unknown dn uuid " << dn_uuid;
              continue;
            }
            dns.emplace_back(dn->id());
          }
          bip_expected_locs_per_file.emplace_back(dns);
        }
      } else if (file_proto.has_add_block_bips()) {
        add_block_bips_per_file.emplace_back(file_proto.add_block_bips());
        bip_expected_locs_per_file.emplace_back(std::vector<DatanodeID>());
      }
      CHECK_EQ(add_block_bips_per_file.size(),
               bip_expected_locs_per_file.size());
      for (int i = 0; i < add_block_bips_per_file.size(); i++) {
        const auto& add_bip = add_block_bips_per_file[i];
        const auto& dns = bip_expected_locs_per_file[i];

        // Do not update block id and gs in callback
        // refer to inode id above
        UpdateLastAllocatedBlockId(add_bip.block_id());
        UpdateGenerationStampV2(add_bip.gen_stamp());

        Block blk{add_bip.block_id(),
                  static_cast<uint32_t>(add_bip.num_bytes()),
                  add_bip.gen_stamp()};
        blks_bip_per_file.push_back(blk);

        block_manager_->LoadBlock(new_file.id(),
                                  new_file.parent_id(),
                                  static_cast<uint8_t>(new_file.replication()),
                                  add_bip.write_mode(),
                                  blk,
                                  dns,
                                  BlockUCState::kUnderConstruction);

        future_blks_per_file.emplace_back(blk);
      }

      CHECK_EQ(blks_inode.size(), blks_bip_per_file.size());
      for (int j = 0; j < blks_inode.size(); ++j) {
        CHECK_EQ(blks_inode[j].blockid(), blks_bip_per_file[j].id);
        CHECK_EQ(blks_inode[j].numbytes(), blks_bip_per_file[j].num_bytes);
        CHECK_EQ(blks_inode[j].genstamp(), blks_bip_per_file[j].gs);
      }

      add_block_bips.insert(add_block_bips.end(),
                            add_block_bips_per_file.begin(),
                            add_block_bips_per_file.end());
      bip_expected_locs.insert(bip_expected_locs.end(),
                               bip_expected_locs_per_file.begin(),
                               bip_expected_locs_per_file.end());
      future_blks.insert(future_blks.end(),
                         future_blks_per_file.begin(),
                         future_blks_per_file.end());
#if 0
      UpdateBlocks(path,
                   &new_file,
                   blks,
                   /*should_complete_last_block=*/false,
                   ctx,
                   nullptr);
#endif
      UpdateINodeAttrs(new_file, parent_inode, true);
    }

    parent_inode.set_mtime(proto.parent().mtime());
    CHECK_EQ(parent_inode.id(), proto.parent().id());
  }
  METRIC_WATCH_STOP(op_batch_create_file_get_inodes_time_);

  {
#if 0  // TODO(xiong): Implement
    // cache
    METRIC_WATCH_START(op_batch_create_file_del_cache_time_);
    auto resp = std::make_shared<BatchCreateResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
    METRIC_WATCH_STOP(op_batch_create_file_del_cache_time_);
#endif
  }

  // commit txn
  {
#if 0
    DECLARE_STAT_RECORDER(meta_storage_);
    STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);
#endif

    CHECK_NE(op->txid(), kInvalidTxId);

    ctx->done->set_callback([=](const Status& s) {
      for (int i = 0; i < overwritten_inodes.size(); ++i) {
        DeleteINodeCallback(overwritten_inodes[i]);
      }

      StopWatch sw;
      sw.Start();
      auto current_block_id = last_allocated_block_id();
      auto current_gsv2 = generation_stamp_v2();
      for (auto& b : future_blks) {
        block_manager_->ProcessPendingFutureBlks(
            b.id, current_block_id, current_gsv2);
        block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
      }
      auto time_cost_us = sw.NextStepTime();
      if (time_cost_us > FLAGS_edit_log_slow_op_us) {
        VLOG(6) << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
                << " slow, path: " << first_path
                << ", process cost us: " << time_cost_us;
      }
    });

    // write db
    std::vector<INode> parent_inodes{proto.parent()};
    meta_storage_->OrderedCommitINodes(&src_inodes,
                                       nullptr,
                                       &overwritten_inodes,
                                       &parent_inodes,
                                       {},
                                       {},
                                       add_block_bips,
                                       op->txid(),
                                       {ctx->done},
                                       /*STAT_RECORDER_PTR*/ nullptr);
  }
}

void NameSpace::ApplyOpBatchCompleteFile(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpBatchCompleteFile>(ctx->op);
  BatchInodeToComplete& proto = op->GetProto();
  CHECK_GT(proto.paths_size(), 0);
  std::string first_path = proto.paths().Get(0);
  VLOG(9) << op->op_name() << ": " << first_path;
  VLOG(9) << "proto=" << proto.ShortDebugString();

  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  std::unordered_map<INodeID, INode> complete_inodes_table;
  std::vector<std::pair<INode, INode>> complete_inodes;
  std::vector<INode> deleted_inodes;
  std::vector<BlockInfoProto> bips_modify;
  for (const auto& inode : proto.complete_inodes()) {
    complete_inodes_table.insert({inode.id(), inode});
  }
  for (const auto& inode : proto.deleted_inodes()) {
    deleted_inodes.push_back(inode);
    CHECK_EQ(inode.blocks().size(), 0);
  }
  for (const auto& bip : proto.bips()) {
    bips_modify.push_back(bip);
  }
  for (const auto& bip : proto.deleted_bips()) {
    CHECK_EQ(bip.state(), BlockInfoProto::kDeprecated);
    bips_modify.push_back(bip);
  }

  // check paths
  METRIC_WATCH_START(op_batch_complete_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (const auto& path : proto.paths()) {
      paths.push_back(path);
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  // get inodes and check inode
  INode parent_inode;
  std::vector<INode> ancestors;
  std::vector<INode> src_inodes;
  std::vector<BlockInfoProto> add_block_bips;
  std::vector<Block> future_blks;
  {
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/true,
                                    /*push_even_if_src_notfound=*/false,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();

    CHECK_EQ(src_inodes.size(), sorted_paths.size());
    CHECK_EQ(src_inodes.size(), proto.original_inodes().size());
    for (int i = 0; i < src_inodes.size(); ++i) {
      const auto& inode_from_db = src_inodes[i];
      const auto& inode_from_pb = proto.original_inodes().Get(i);

      auto it = complete_inodes_table.find(inode_from_db.id());
      if (it != complete_inodes_table.end()) {
        complete_inodes.emplace_back(inode_from_db, it->second);
      }

      // very easy check
      if (!(inode_from_db.id() == inode_from_pb.id() &&
            inode_from_db.parent_id() == inode_from_pb.parent_id() &&
            inode_from_db.name() == inode_from_pb.name() &&
            inode_from_db.type() == inode_from_pb.type())) {
        LOG(FATAL) << "INode mismatched,"
                   << " inode_from_db=" << ToJsonCompactString(inode_from_db)
                   << " inode_from_pb=" << ToJsonCompactString(inode_from_pb)
                   << " txid=" << op->txid();
      }

      //      if (!CompareINodeFromMetaStorageAndEditLog(inode_from_db,
      //                                                 inode_from_pb)) {
      //        LOG(FATAL) << "INode mismatched,"
      //                   << " file_from_ms=" <<
      //                   ToJsonCompactString(inode_from_db)
      //                   << " file_from_editlog="
      //                   << ToJsonCompactString(inode_from_pb)
      //                   << " txid=" << op->txid();
      //      }
    }
    CHECK_EQ(parent_inode.id(), proto.parent().id());
  }
  CHECK_EQ(complete_inodes_table.size(), complete_inodes.size());
  CHECK_EQ(proto.original_inodes().size(),
           complete_inodes_table.size() + deleted_inodes.size());
  METRIC_WATCH_STOP(op_batch_complete_file_get_inodes_time_);

  // memory data
  {
    // update block length
    for (const auto& bip : proto.bips()) {
      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};

      future_blks.emplace_back(blk);
    }

    // remove block
    for (const auto& bip : proto.deleted_bips()) {
      Block blk{bip.block_id(),
                static_cast<uint32_t>(bip.num_bytes()),
                bip.gen_stamp()};

      future_blks.emplace_back(blk);
    }
  }

  {
#if 0  // TODO(xiong): Implement
    // cache
    METRIC_WATCH_START(op_batch_create_file_del_cache_time_);
    auto resp = std::make_shared<BatchCreateResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
    METRIC_WATCH_STOP(op_batch_create_file_del_cache_time_);
#endif
  }

  // commit txn
  {
#if 0
    DECLARE_STAT_RECORDER(meta_storage_);
    STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);
#endif

    CHECK_NE(op->txid(), kInvalidTxId);

    ctx->done->set_callback([=](const Status& s) {
      // remove lease
      for (const auto& pair : complete_inodes) {
        const auto& inode = pair.second;

        lease_manager_->RemoveLease(inode.id());
      }
      for (const auto& inode : deleted_inodes) {
        DeleteINodeCallback(inode);
      }

      // update block length
      for (const auto& bip : proto.bips()) {
        block_manager_->UpdateINode(bip.block_id(), bip.inode_id());

        Block blk{bip.block_id(),
                  static_cast<uint32_t>(bip.num_bytes()),
                  bip.gen_stamp()};
        block_manager_->CommitOrCompleteOrPersistLastBlock(blk, true);
      }

      // future block
      {
        StopWatch sw;
        sw.Start();
        auto current_block_id = last_allocated_block_id();
        auto current_gsv2 = generation_stamp_v2();
        for (auto& b : future_blks) {
          block_manager_->ProcessPendingFutureBlks(
              b.id, current_block_id, current_gsv2);
          block_manager_->ProcessPendingPersistedBlks(b, current_gsv2);
        }
        auto time_cost_us = sw.NextStepTime();
        if (time_cost_us > FLAGS_edit_log_slow_op_us) {
          VLOG(6)
              << "Detect ProcessPendingFutureBlks/ProcessPendingPersistedBlks"
              << " slow, path: " << first_path
              << ", process cost us: " << time_cost_us;
        }
      }

      // trigger upload
      if (NameSpace::IsAccMode() && ufs_env_ &&
          FLAGS_complete_rpc_trigger_file_upload_in_callback) {
        for (const auto& pair : complete_inodes) {
          const auto& inode = pair.second;
          CHECK_NOTNULL(ufs_env_->upload_monitor())
              ->TriggerFileUpload(inode.id());
        }
      }
    });

    // write db
    std::vector<INode> parent_inodes{proto.parent()};
    meta_storage_->OrderedCommitINodes(nullptr,
                                       &complete_inodes,
                                       &deleted_inodes,
                                       &parent_inodes,
                                       nullptr,
                                       bips_modify,
                                       {},
                                       op->txid(),
                                       { ctx->done },
                                       /*STAT_RECORDER_PTR*/ nullptr);
  }
}

void NameSpace::ApplyOpBatchDeleteFile(std::shared_ptr<ApplyContext> ctx) {
  auto op = std::static_pointer_cast<OpBatchDeleteFile>(ctx->op);
  BatchInodeToDelete& proto = op->GetProto();

  if (proto.files().size() == 0) {
    VLOG(9) << op->op_name();
    VLOG(9) << "proto=" << proto.ShortDebugString();
    meta_storage_->OrderedCommitNop(op->txid());

    ctx->done->Run();
    return;
  }

  std::string first_path = proto.files().Get(0).path();
  VLOG(9) << op->op_name() << ": " << first_path;
  VLOG(9) << "proto=" << proto.ShortDebugString();

  CHECK_GT(proto.files_size(), 0);
  std::string rpc_clientid;
  uint32_t rpc_callid;
  EditLogOpFactory::ConvertLogRpcInfo(proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      &rpc_clientid,
                                      &rpc_callid);

  // check paths
  METRIC_WATCH_START(op_batch_delete_file_get_inodes_time_);
  std::vector<std::string> paths;
  std::string parent_path;
  std::vector<std::string> sorted_paths;
  {
    for (int i = 0; i < proto.files_size(); ++i) {
      paths.push_back(proto.files().Get(i).path());
    }

    std::unordered_map<std::string, int> path_index_table;
    auto s = BatchApiPathsPrecheck(
        paths, &parent_path, &sorted_paths, &path_index_table);
    CHECK(s.IsOK()) << s.ToString();
    for (int i = 0; i < paths.size(); ++i) {
      CHECK_EQ(paths[i], sorted_paths[i])
          << " sort error. pb=" << proto.ShortDebugString();
    }
  }

  // get inodes and check inode
  INode parent_inode;
  std::vector<INode> ancestors;
  std::vector<INode> src_inodes;
  {
    auto s = BatchApiPathsGetINodes(parent_path,
                                    sorted_paths,
                                    ctx->parent_path_components,
                                    ctx->children_path_components,
                                    /*break_if_src_found=*/false,
                                    /*break_if_src_notfound=*/true,
                                    /*push_even_if_src_notfound=*/false,
                                    nullptr,
                                    /*is_for_read=*/false,
                                    &ancestors,
                                    &parent_inode,
                                    &src_inodes);
    CHECK(s.IsOK()) << s.ToString();
    CHECK_EQ(paths.size(), src_inodes.size());

    for (int i = 0; i < paths.size(); ++i) {
      const auto& inode_from_db = src_inodes[i];
      const auto& inode_from_pb = proto.files().Get(i).inode();

      CompareINodeFromMetaStorageAndEditLog(inode_from_db, inode_from_pb);

      // not use now
      CHECK_EQ(proto.files().Get(i).bips().size(), 0);
    }

    CHECK_EQ(parent_inode.id(), proto.parent().id());
  }
  METRIC_WATCH_STOP(op_batch_delete_file_get_inodes_time_);

  {
#if 0  // TODO(xiong): Implement
    // cache
    METRIC_WATCH_START(op_batch_delete_file_del_cache_time_);
    auto resp = std::make_shared<DeleteResponseProto>();
    resp->set_result(true);
    AddRetryCacheEntry(rpc_clientid, rpc_callid, std::move(resp));
    METRIC_WATCH_STOP(op_batch_delete_file_del_cache_time_);
#endif
  }

  // commit txn
  {
#if 0
    DECLARE_STAT_RECORDER(meta_storage_);
    STAT_RECORDER_INODE_DELETE(ctx, src_inode, src_ancestors);
#endif

    CHECK_NE(op->txid(), kInvalidTxId);

    ctx->done->set_callback([=](const Status& s) {
      for (const auto& inode : src_inodes) {
        DeleteINodeCallback(inode);
      }
    });

    // write db
    std::vector<INode> parent_inodes{proto.parent()};
    meta_storage_->OrderedCommitINodes(nullptr,
                                       nullptr,
                                       &src_inodes,
                                       &parent_inodes,
                                       nullptr,
                                       {},
                                       {},
                                       op->txid(),
                                       { ctx->done },
                                       /*STAT_RECORDER_PTR*/ nullptr);
  }
}

}  // namespace dancenn
