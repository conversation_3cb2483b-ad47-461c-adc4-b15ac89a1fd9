//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_ACL_H_
#define NAMESPACE_ACL_H_

#include <glog/logging.h>

#include <string>

#include "inode.pb.h"  // NOLINT(build/include)
#include "acl.pb.h"  // NOLINT(build/include)
#include "permission.h"  // NOLINT(build/include)
#include "base/constants.h"
#include "base/status.h"

using ::cloudfs::AclEntryProto;
using ::google::protobuf::RepeatedPtrField;

namespace dancenn {

class FsAction {
 public:
  explicit FsAction(uint8_t value) : value_(value) {
    CHECK(cloudfs::AclEntryProto_FsActionProto_IsValid(value_));
  }

  FsAction &operator &=(const FsAction &that) {
    this->value_ = this->value_ & that.value_;
    return *this;
  }

  FsAction &operator |=(const FsAction &that) {
    this->value_ = this->value_ | that.value_;
    return *this;
  }

  void Not() {
    this->value_ = static_cast<uint8_t>(7) - this->value_;
  }

  bool implies(const FsAction &that) const {
    return (this->value_ & that.value_) == that.value_;
  }

  const char* &name() {
    switch (static_cast<::cloudfs::AclEntryProto_FsActionProto>(value_)) {
      case cloudfs::AclEntryProto_FsActionProto_NONE:
        return kFsActionNone;
      case cloudfs::AclEntryProto_FsActionProto_EXECUTE:
        return kFsActionExecute;
      case cloudfs::AclEntryProto_FsActionProto_WRITE:
        return kFsActionWrite;
      case cloudfs::AclEntryProto_FsActionProto_WRITE_EXECUTE:
        return kFsActionWriteExecute;
      case cloudfs::AclEntryProto_FsActionProto_READ:
        return kFsActionRead;
      case cloudfs::AclEntryProto_FsActionProto_READ_EXECUTE:
        return kFsActionReadExecute;
      case cloudfs::AclEntryProto_FsActionProto_READ_WRITE:
        return kFsActionReadWrite;
      case cloudfs::AclEntryProto_FsActionProto_PERM_ALL:
        return kFsActionPermAll;
    }
  }

 private:
  uint8_t value_;
};

class Acl {
 public:
  Acl() {}

  void GetUserAcl(const FsPermission &p, AclEntryProto *user_acl) {
    CHECK(user_acl);
    user_acl->set_scope(cloudfs::AclEntryProto_AclEntryScopeProto_ACCESS);
    user_acl->set_type(cloudfs::AclEntryProto_AclEntryTypeProto_USER);
    user_acl->set_permissions(p.user_action());
  }

  void GetGroupAcl(const FsPermission &p, AclEntryProto *group_acl) {
    CHECK(group_acl);
    group_acl->set_scope(cloudfs::AclEntryProto_AclEntryScopeProto_ACCESS);
    group_acl->set_type(cloudfs::AclEntryProto_AclEntryTypeProto_GROUP);
    group_acl->set_permissions(p.group_action());
  }

  void GetOtherAcl(const FsPermission &p, AclEntryProto *other_acl) {
    CHECK(other_acl);
    other_acl->set_scope(cloudfs::AclEntryProto_AclEntryScopeProto_ACCESS);
    other_acl->set_type(cloudfs::AclEntryProto_AclEntryTypeProto_OTHER);
    other_acl->set_permissions(p.other_action());
  }

  static bool IsMinimalAcl(const RepeatedPtrField<AclEntryProto> acls) {
    return acls.size() == 3;
  }
};

}  // namespace dancenn

#endif  // NAMESPACE_ACL_H_
