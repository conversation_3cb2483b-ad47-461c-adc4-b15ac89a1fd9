#include "unique_txid_ring_window.h"

#include <stdlib.h>
#include <string.h>
#include <sched.h>
#include <assert.h>

#include "meta_storage_write_task.h"

namespace dancenn {
  namespace meta_storage {
    UniqueTxidRingWindow::UniqueTxidRingWindow(int n) {
      data_             = new meta_storage::WriteTask*[n];
      next_apply_txid_  = 0;
      capacity_         = n;
      memset(data_, 0, sizeof(meta_storage::WriteTask*) * n);
    }

    UniqueTxidRingWindow::~UniqueTxidRingWindow() {
      for (uint32_t i = 0; i < capacity_; i++) {
        if (data_[i] != nullptr) {
          delete data_[i];
        }
      }
      delete []data_;
    }

    void UniqueTxidRingWindow::NextApplyTxid(int64_t txid) {
      next_apply_txid_ = txid;
    }

    bool UniqueTxidRingWindow::TryPush(int64_t txid, meta_storage::WriteTask* task) {
      CHECK(txid >= next_apply_txid_) << txid << " " << next_apply_txid_;

      if (txid - next_apply_txid_ >= capacity_) {
        // too fast
        return false;
      }

      int index = txid % capacity_;
      CHECK(data_[index] == nullptr);
      data_[index] = task;
      return true;
    }

    void UniqueTxidRingWindow::Push(int64_t txid, meta_storage::WriteTask* task) {
      if (TryPush(txid, task)) return;

      // wait and slow down
      {
        LOG(INFO) << "unique txid: slow down";
        std::unique_lock<std::mutex> guard(m_);
        cond_.wait(guard, [txid, this](){
            return txid - next_apply_txid_ < capacity_;
            });
      }

      // must success
      bool ok = TryPush(txid, task);
      CHECK(ok);
    }

    void UniqueTxidRingWindow::Pop(int n, std::vector<meta_storage::WriteTask*>* tasks) {
      tasks->reserve(n);

      TryPop(n, tasks);
      n = n - tasks->size();

      if (n > 0) {
        for (int i = 0; i < 200; i++) {
#ifdef __aarch64__
          asm volatile("yield" ::: "memory");
#else
          asm volatile("pause" ::: "memory");
#endif
        }

        TryPop(n, tasks);
      }

      if (tasks->size() > 0) {
        std::unique_lock<std::mutex> guard(m_);
        cond_.notify_all();
      }
    }

    void UniqueTxidRingWindow::TryPop(int n,
        std::vector<meta_storage::WriteTask*>* tasks) {
      while (n > 0) {
        auto index = next_apply_txid_ % capacity_;
        if (data_[index] == nullptr) break;
        tasks->emplace_back(data_[index]);
        data_[index] = nullptr;
#ifdef __aarch64__
        asm volatile("dsb sy" ::: "memory");
#else
        asm volatile("" ::: "memory");
#endif
        next_apply_txid_ = next_apply_txid_ + 1;
        n = n - 1;
      }
    }
  }
}
