// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/17
// Description

#include "namespace/namespace_stat_checker.h"

#include <absl/strings/str_cat.h>      // For StrAppend.
#include <absl/strings/str_format.h>   // For StrFormat.
#include <cnetpp/base/string_piece.h>  // For StringPiece.
#include <gflags/gflags.h>             // For DECLARE_int32.
#include <glog/logging.h>              // For CHECK.

#include <chrono>   // For chrono_literals.
#include <cstdint>  // For uint64_t.
#include <numeric>  // For accumulate.
#include <regex>    // For regex.
#include <thread>   // For sleep_for, this_thread.
#include <utility>  // For move.

#include "base/defer.h"                       // For DEFER.
#include "namespace/meta_storage_constants.h" // For MetaStorage CFIndex.
#include "proto/generated/cloudfs/hdfs.pb.h"  // For BlockProto.

DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);

DECLARE_int32(inode_stat_checker_scan_batch_size);

namespace dancenn {
namespace {

using INodeErrMsgFmtT = absl::ParsedFormat<'d', 'd', 's', 'd', 'd', 's', 'd'>;
std::unique_ptr<INodeErrMsgFmtT> kINodeErrMsgFmt;

using ParseINodeFailedMsgFmtT = absl::ParsedFormat<'d', 'd', 's', 'd'>;
std::unique_ptr<ParseINodeFailedMsgFmtT> kParseINodeFailedMsgFmt;

// clang-format off
using CheckStatResultMsgFmtT = absl::ParsedFormat<
  // environment
  's', 's', 's', 'd', 'd',
  's', 'd', 'd',
  // computed_stat
  'd', 'd', 'd', 'd', 'd',
  // stat
  'd', 'd', 'd', 'd', 'd', 'd', 'd'
>;
// clang-format on
std::unique_ptr<CheckStatResultMsgFmtT> kCheckStatResultMsgFmt;

}  // namespace

CheckINodeStatOp::CheckINodeStatOp(std::shared_ptr<MetaStorage> meta_storage,
                                   MetaStorageSnapPtr snapshot,
                                   MetaStorageIterPtr namespace_info_iter,
                                   MetaStorageIterPtr inode_iter,
                                   MetaStorageIterPtr inode_stat_iter,
                                   const std::string& start_from,
                                   bool enable_debug_msg)
    : meta_storage_(meta_storage),
      snapshot_(snapshot),
      namespace_info_iter_(namespace_info_iter),
      inode_iter_(inode_iter),
      inode_stat_iter_(inode_stat_iter),
      read_inode_cnt_(0),
      enable_debug_msg_(enable_debug_msg) {
  inode_iter_->Seek(start_from);

  static std::once_flag flag;
  std::call_once(flag, []() {
    kParseINodeFailedMsgFmt = ParseINodeFailedMsgFmtT::New(
        "Found invalid inode, pid=%d, id=%d, name=%s, txid=%d");
    CHECK(kParseINodeFailedMsgFmt);
    kINodeErrMsgFmt = INodeErrMsgFmtT::New(
        "Found invalid inode, key(pid=%d, id=%d, name=%s), "
        "value(pid=%d, id=%d, name=%s), txid=%d");
    CHECK(kINodeErrMsgFmt);
    const char* s = R"(
    |{
    |"cfs_region":"%s",
    |"cfs_env":"%s",
    |"cfs_cluster":"%s",
    |"filesystem_id":%d,
    |"namespace_id":%d,
    |"is_equal":%s,
    |"txid":%d,
    |"inode_id":%d,
    |"computed_stat":{
    |"inode_num":%d,
    |"file_num":%d,
    |"dir_num":%d,
    |"block_num":%d,
    |"data_size":%d
    |},
    |"stat":{
    |"inode_num":%d,
    |"file_num":%d,
    |"dir_num":%d,
    |"block_num":%d,
    |"data_size":%d,
    |"txid":%d,
    |"timestamp_sec":%d
    |}
    |})";
    kCheckStatResultMsgFmt = CheckStatResultMsgFmtT::New(
        std::regex_replace(s, std::regex(" +\\||\n"), "").c_str());
  });
}

Status CheckINodeStatOp::Check() {
  auto txid = meta_storage_->GetLastCkptTxId(namespace_info_iter_);
  // https://github.com/facebook/rocksdb/wiki/Iterator#error-handling
  // If Iterator::Valid() is true, status() is guaranteed to be OK().
  // If Iterator::Valid() is false, there are two possibilities:
  // (1) We reached the end of the data. In this case, status() is OK();
  // (2) there is an error. In this case status() is not OK().
  if (!inode_iter_->Valid()) {
    if (inode_iter_->status().ok() || inode_iter_->status().IsNotFound()) {
      // Scan to end.
      return Status(Code::kOK, "end");
    }
    LOG(FATAL) << "Failed to scan inode, status: "
               << inode_iter_->status().ToString();
  }

  DEFER([&]() {
    read_inode_cnt_++;
    inode_iter_->Next();
  });

  // Parse key.
  std::string key(inode_iter_->key().data(), inode_iter_->key().size());
  uint64_t pid = 0;
  uint64_t id = 0;
  std::string name;
  meta_storage_->DecodeStoreKey(key, &pid, &name, &id);
  // Parse value.
  INode inode;
  CHECK(inode.ParseFromArray(inode_iter_->value().data(),
                             inode_iter_->value().size()))
      << absl::StrFormat(*kParseINodeFailedMsgFmt, pid, id, name, txid);
  CHECK(inode.parent_id() == pid && inode.id() == id && inode.name() == name)
      << absl::StrFormat(*kINodeErrMsgFmt,
                         pid,
                         id,
                         name,
                         inode.parent_id(),
                         inode.id(),
                         inode.name(),
                         txid);
  if (enable_debug_msg_) {
    debug_msg_ = {{"parent_inode", {{"name", name}, {"id", id}, {"pid", pid}}}};
  }

  switch (inode.type()) {
    case INode::kFile: {
      VLOG(15) << "CheckINodeStatOp returns true for file: " << id;
      return Status(Code::kOK, absl::StrFormat(R"({"inode_id":%d})", id));
    }
    case INode::kDirectory: {
      INodeStat stat;
      auto s = meta_storage_->GetDirectoryINodeStat(
          inode.id(), &stat, inode_stat_iter_);
      CHECK(s.IsOK()) << absl::StrFormat(
          "Get dir inode stat failed, id=%d, name=%s, status=%s",
          inode.id(),
          inode.name(),
          s.ToString());
      computed_stat_ = INodeStat();

      // XXX
      // Use another iterator that created by the same snapshot, leaving
      // @inode_iter_ only for sequential traversal. Otherwise, we may see
      // old version of data.
      // https://meego.feishu.cn/cfs/issue/detail/8530263
      auto iter2_holder = meta_storage_->GetIterator(snapshot_, kINodeDefaultCFIndex);
      meta_storage_->GetSubINodes(
          id,
          "",
          [this](const INode& inode) { return AccumulateSubINodeStat(inode); },
          iter2_holder->iter());

      bool equal = stat.inode_num == computed_stat_.inode_num &&
                   stat.file_num == computed_stat_.file_num &&
                   stat.dir_num == computed_stat_.dir_num &&
                   stat.block_num == computed_stat_.block_num &&
                   stat.data_size == computed_stat_.data_size;
      std::string msg = absl::StrFormat(*kCheckStatResultMsgFmt,
                                        FLAGS_cfs_region,
                                        FLAGS_cfs_env,
                                        FLAGS_cfs_cluster,
                                        FLAGS_filesystem_id,
                                        FLAGS_namespace_id,
                                        equal ? "true" : "false",
                                        txid,
                                        stat.inode_id,
                                        computed_stat_.inode_num,
                                        computed_stat_.file_num,
                                        computed_stat_.dir_num,
                                        computed_stat_.block_num,
                                        computed_stat_.data_size,
                                        stat.inode_num,
                                        stat.file_num,
                                        stat.dir_num,
                                        stat.block_num,
                                        stat.data_size,
                                        stat.txid,
                                        stat.timestamp_sec);
      if (!equal) {
        LOG(INFO) << "CheckINodeStatOp returns false, " << msg;
      } else {
        VLOG(15) << "CheckINodeStatOp returns true, " << msg;
      }
      if (enable_debug_msg_) {
        debug_msg_["dir_stat"] = {{"inode_num", stat.inode_num},
                                  {"file_num", stat.file_num},
                                  {"dir_num", stat.dir_num},
                                  {"data_size", stat.data_size},
                                  {"block_num", stat.block_num}};
        debug_msg_["computed_stat"] = {{"inode_num", computed_stat_.inode_num},
                                       {"file_num", computed_stat_.file_num},
                                       {"dir_num", computed_stat_.dir_num},
                                       {"data_size", computed_stat_.data_size},
                                       {"block_num", computed_stat_.block_num}};
        debug_msg_["equal"] = equal;
      }
      // TODO(ruanjunbin)
      // if (FLAGS_dirstat_log_enabled) {
      // }
      return Status(equal ? Code::kOK : Code::kFalse, msg);
    }
    case INode::kSymLink:
    default: {
      LOG(FATAL) << absl::StrFormat(
          "Not supported INode type, id=%d, name=%s", inode.id(), inode.name());
      return Status(Code::kFalse);
    }
  }
}

bool CheckINodeStatOp::AccumulateSubINodeStat(const INode& inode) {
  read_inode_cnt_++;
  switch (inode.type()) {
    case INode::kFile: {
      computed_stat_.inode_num++;
      computed_stat_.file_num++;
      const auto& blks = inode.blocks();
      auto data_size = std::accumulate(
          blks.begin(), blks.end(), 0L, [](int64_t len, const BlockProto& bp) {
            return len + bp.numbytes();
          });
      computed_stat_.data_size += data_size;
      computed_stat_.block_num += blks.size();
      if (enable_debug_msg_) {
        debug_msg_["sub_inodes"][inode.name()] = {{"name", inode.name()},
                                                  {"id", inode.id()},
                                                  {"pid", inode.parent_id()},
                                                  {"is_file", true},
                                                  {"data_size", data_size},
                                                  {"block_num", blks.size()}};
      }
      return true;
    }

    case INode::kDirectory: {
      INodeStat stat;
      auto s = meta_storage_->GetDirectoryINodeStat(
          inode.id(), &stat, inode_stat_iter_);
      CHECK(s.IsOK()) << absl::StrFormat(
          "Get dir inode stat failed, id=%d, name=%s, status=%s",
          inode.id(),
          inode.name(),
          s.ToString());
      computed_stat_.inode_num += 1 + stat.inode_num;
      computed_stat_.file_num += stat.file_num;
      computed_stat_.dir_num += 1 + stat.dir_num;
      computed_stat_.data_size += stat.data_size;
      computed_stat_.block_num += stat.block_num;
      if (enable_debug_msg_) {
        debug_msg_["sub_inodes"][inode.name()] = {
            {"name", inode.name()},
            {"id", inode.id()},
            {"pid", inode.parent_id()},
            {"is_file", false},
            {"inode_num", stat.inode_num},
            {"file_num", stat.file_num},
            {"dir_num", stat.dir_num},
            {"data_size", stat.data_size},
            {"block_num", stat.block_num}};
      }
      return true;
    }

    case INode::kSymLink:
    default: {
      LOG(FATAL) << absl::StrFormat(
          "Not supported INode type, id=%d, name=%s", inode.id(), inode.name());
      return false;
    }
  }
}

int CheckINodeStatOp::ReadINodeCnt() {
  return read_inode_cnt_;
}

std::string CheckINodeStatOp::GetNext() {
  return inode_iter_->Valid() ? inode_iter_->key().ToString() : "";
}

std::string CheckINodeStatOp::GetDebugMsg() {
  return debug_msg_.dump();
}

INodeStatChecker::INodeStatChecker(std::shared_ptr<MetaStorage> meta_storage)
    : meta_storage_(meta_storage), is_running_(false) {
}

INodeStatChecker::~INodeStatChecker() {
  if (is_running_.exchange(false)) {
    thread_.reset();
  }
}

void INodeStatChecker::Start() {
  if (!is_running_.exchange(true)) {
    LOG(INFO) << "Start INodeStatChecker";
    CHECK(!thread_);
    thread_.reset(new cnetpp::concurrency::Thread([this]() {
      while (is_running_) {
        using namespace std::chrono_literals;  // NOLINT(build/namespaces)
        if (CheckOnce()) {
          std::unique_lock<std::mutex> lock(mu_);
          cond_.wait_for(
              lock, 10min, [this]() -> bool { return !is_running_; });
        } else {
          std::unique_lock<std::mutex> lock(mu_);
          cond_.wait_for(
              lock, 500ms, [this]() -> bool { return !is_running_; });
        }
      }
      LOG(INFO) << "INodeStatChecker is stopped";
      return true;
    }));
    thread_->Start();
  }
}

void INodeStatChecker::Stop() {
  if (is_running_.exchange(false)) {
    cond_.notify_all();
    thread_->Stop();
    thread_.reset();
  }
}

bool INodeStatChecker::CheckOnce() {
  if (!snapshot_holder_) {
    // acquire snapshot
    snapshot_holder_ = meta_storage_->GetSnapshot();
  }

  // iterator holder scope
  {
    auto snap = snapshot_holder_->snapshot();
    auto nsinfo_iter_holder = meta_storage_->GetIterator(snap, kNameSystemInfoCFIndex);
    auto inode_iter_holder = meta_storage_->GetIterator(snap, kINodeDefaultCFIndex);
    auto instat_iter_holder = meta_storage_->GetIterator(snap, kINodeStatCFIndex);

    CheckINodeStatOp op(meta_storage_,
                        snap,
                        nsinfo_iter_holder->iter(),
                        inode_iter_holder->iter(),
                        instat_iter_holder->iter(),
                        start_from_);
    while (op.ReadINodeCnt() < FLAGS_inode_stat_checker_scan_batch_size &&
           op.GetNext() != "") {
      CHECK(op.Check().IsOK());
    }
    start_from_ = op.GetNext();
  }

  if (start_from_.empty()) {
    // release snapshot
    snapshot_holder_.reset(nullptr);
    return true;
  }
  return false;
}

}  // namespace dancenn
