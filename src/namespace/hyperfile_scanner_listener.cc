// Copyright 2020 Mu jinrenjie.0823 <<EMAIL>>

#include "hyperfile_scanner_listener.h"

#include <glog/logging.h>
#include <gflags/gflags.h>
#include "namespace/namespace.h"
#include "namespace/meta_scanner.h"

DECLARE_int32(hyper_block_leak_threshold_sec);

namespace dancenn {

const std::regex HyperfileScannerListener::kHyperBlockSrcFormat("(.+)_part(\\d{3})_([0-9a-f]{8})");
const std::string HyperfileScannerListener::kInvalidHyperBlockSrc("");
// Format: "hyper_file_name" + "_part000_12345678"

HyperfileScannerListener::HyperfileScannerListener(NameSpace* ns)
     : MetaScanner::Listener(desc()), ns_(ns) {
  LOG(INFO) << "Start HyperfileScannerListener";
}

std::vector<INodeID> HyperfileScannerListener::ScanIndexes() {
  return {kRootINodeId};
}

bool HyperfileScannerListener::Handle(const std::string &full_path, const dancenn::INode &inode) {
  if (ns_->IsHyperFile(inode)) {
    hyper_file_count_++;

    // Get Meta of hyperfile
    HyperCacheMeta meta;
    if (!XAttrs::GetProtoBufXAttr(inode, kHyperFileKey.c_str(), &meta, false)) {
      LOG(ERROR) << "Unable to parse meta of hyper file. Inode: " << inode.ShortDebugString();
      return true;
    }

    //
    auto& blocks = meta.hyperblock();
    for (const std::string& block_suffix: blocks) {
      std::string full_block_path = full_path + block_suffix;
      bool broken = false;
      if (!ns_->FileExists(full_block_path)) {
        broken = true;
        LOG(ERROR) << "Find broken hyperFile: " << full_path
                   << "; hyperBlock: " << full_block_path;
      }

      if (broken) {
        broken_hyper_file_count_++;
      }
    }
  }

  if (ns_->IsHyperBlock(inode)) {
    hyper_block_count_++;

    auto expected_hyper_file_path = GetHyperFilePath(full_path, inode);
    if (expected_hyper_file_path == kInvalidHyperBlockSrc) {
      LOG(INFO) << "Invalid hyper block format: " << full_path;
      return true;
    }

    int64_t current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();

    if (!ns_->FileExists(expected_hyper_file_path)) {
      if (inode.mtime() / 1000 - current_time_sec > FLAGS_hyper_block_leak_threshold_sec) {
        leak_hyper_block_count_++;

        SynchronizedRpcClosure done;
        // FIXME now recorded as super user.
        ns_->AsyncDelete(full_path, true, UserGroupInfo("root", "supergroup"), LogRpcInfo(), &done);
        done.Await();
        if (!done.status().IsOK()) {
          LOG(WARNING) << "Delete leak hyper_block failed: " << full_path
              << " status=" << done.status().ToString()
              << ". Expected file path: " << expected_hyper_file_path
              << ". current_time_sec: " << current_time_sec
              << ". inode_mtime_sec: " << inode.mtime() / 1000
              << ". Threshold: " << FLAGS_hyper_block_leak_threshold_sec;
        } else {
          LOG(WARNING) << "Delete leak hyper_block success: " << full_path
              << " status=" << done.status().ToString()
              << ". Expected file path: " << expected_hyper_file_path
              << ". current_time_sec: " << current_time_sec
              << ". inode_mtime_sec: " << inode.mtime() / 1000
              << ". Threshold: " << FLAGS_hyper_block_leak_threshold_sec;
        }
      } else {
        LOG(WARNING) << "One hyperblock maybe leak but has not expired: " << full_path
                     << ". Expected hyper file path: " << expected_hyper_file_path
                     << ". current_time_sec: " << current_time_sec
                     << ". inode_mtime_sec: " << inode.mtime() / 1000
                     << ". Threshold: " << FLAGS_hyper_block_leak_threshold_sec;
        suspect_leak_hyper_block_count_++;
      }
    }
  }

  return true;
}

bool HyperfileScannerListener::PreScan() {
  hyper_file_count_ = 0;
  hyper_block_count_ = 0;
  broken_hyper_file_count_ = 0;
  leak_hyper_block_count_ = 0;
  suspect_leak_hyper_block_count_ = 0;
  return true;
}

void HyperfileScannerListener::PostScan() {
  LOG(INFO) << "HyperCache Scan Result: ";
  LOG(INFO) << "Total hyper file: " << hyper_file_count_;
  LOG(INFO) << "Total hyper cache: " << hyper_block_count_;
  LOG(INFO) << "Broken hyper file count: " << broken_hyper_file_count_;
  LOG(INFO) << "Leak hyper block count: " << leak_hyper_block_count_;
  LOG(INFO) << "Suspect leak hyper block count: " << suspect_leak_hyper_block_count_;
}

std::string HyperfileScannerListener::GetHyperFilePath(const std::string& full_path,
                                                       const dancenn::INode &inode) {
  // Consider the performance of regex. Just use subdir to extract filepath.
  // std::smatch match;
  // if (!std::regex_match(full_path, match, kHyperBlockSrcFormat)) {
  //  return kInvalidHyperBlockSrc;
  // }
  // return std::string(match[1].first, match[1].second);

  if (inode.name().length() <= kHyperBlockSuffixLength) {
    return kInvalidHyperBlockSrc;
  } else {
    return full_path.substr(0, full_path.size() - kHyperBlockSuffixLength);
  }
}

}  // namespace dancenn
