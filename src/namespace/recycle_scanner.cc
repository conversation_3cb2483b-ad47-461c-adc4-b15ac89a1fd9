#include "recycle_scanner.h"

#include <absl/strings/str_format.h>
#include <glog/logging.h>

#include <chrono>
#include <string>

#include "base/logger_metrics.h"
#include "base/stop_watch.h"
#include "block_manager/block_manager.h"
#include "namespace/meta_scanner.h"
#include "namespace/meta_storage.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/namespace.h"

DECLARE_bool(recycle_bin_enable);
DECLARE_uint32(recycle_bin_retention_day);
DECLARE_bool(recycle_bin_scanner_enable);
DECLARE_uint32(recycle_bin_scanner_interval_sec);

namespace dancenn {

RecycleScanner::RecycleScanner(NameSpace* ns)
    : ns_(ns) {
  InitMetrics();
}

RecycleScanner::~RecycleScanner() {
  FiniMetrics();
}

void RecycleScanner::Start() {
  CHECK(worker_ == nullptr);
  worker_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::shared_ptr<cnetpp::concurrency::Task>(
          new RecycleScanner::ScanTask(shared_from_this())),
      "RecycleScanner");
  worker_->Start();
}

void RecycleScanner::Stop() {
  if (!worker_) {
    return;
  }
  worker_->Stop();
  worker_.reset();
}

void RecycleScanner::InitMetrics() {
  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("NameSpace");

  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumRecycleBin",
      [this] () -> double { return num_rb_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumUserBin#type=deleted",
      [this] () -> double { return num_ub_deleted_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumUserBin#type=skipped",
      [this] () -> double { return num_ub_skipped_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumUserBin#type=invalid",
      [this] () -> double { return num_ub_invalid_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumDateBin#type=deleted",
      [this] () -> double { return num_db_deleted_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumDateBin#type=skipped",
      [this] () -> double { return num_db_skipped_; }));
  gauges_.push_back(metrics_->RegisterGauge(
      "RecycleBin.Scanner.NumDateBin#type=invalid",
      [this] () -> double { return num_db_invalid_; }));
}

void RecycleScanner::FiniMetrics() {
  for (const auto& gauge : gauges_) {
    metrics_->DeregisterGauge(gauge);
  }
}

RecycleScanner::ScanTask::ScanTask(std::shared_ptr<RecycleScanner> scanner)
    : recycle_scanner_(scanner) {
  InitMetrics();
}

RecycleScanner::ScanTask::~ScanTask() {
  FiniMetrics();
}

bool RecycleScanner::ScanTask::operator()(void* arg) {
  while (!stop_) {
    PreScan();
    if (FLAGS_recycle_bin_scanner_enable) {
      DoScan();
    }
    PostScan();

    int sleep_sec = FLAGS_recycle_bin_scanner_interval_sec;
    VLOG(10) << absl::StrFormat(
        "RecycleScanner will sleep %d seconds.", sleep_sec);
    std::chrono::seconds period{sleep_sec};
    std::unique_lock<std::mutex> lock(mtx_);
    cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
  }
  return true;
}

void RecycleScanner::ScanTask::Stop() {
  std::unique_lock<std::mutex> lock(mtx_);
  stop_ = true;
  cond_.notify_all();
}

void RecycleScanner::ScanTask::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameSpace");

  process_recycle_bin_time_ =
      metrics->RegisterHistogram("RecycleBin.Scanner.ProcessRecycleBin");
  process_user_bin_time_ =
      metrics->RegisterHistogram("RecycleBin.Scanner.ProcessUserBin");
}

void RecycleScanner::ScanTask::FiniMetrics() {
  // nothing
}

void RecycleScanner::ScanTask::PreScan() {
  scan_start_ = std::chrono::steady_clock::now();
  num_rb_ = 0;
  num_ub_deleted_ = 0;
  num_ub_skipped_ = 0;
  num_ub_invalid_ = 0;
  num_db_deleted_ = 0;
  num_db_skipped_ = 0;
  num_db_invalid_ = 0;
  const int sec_per_day = 24 * 60 * 60;
  uint64_t retention_sec = FLAGS_recycle_bin_retention_day * sec_per_day;
  uint64_t now_sec = TimeUtil::YMDToSec(TimeUtil::GetNowYMD());
  retention_sec_thold_ = now_sec - (now_sec % sec_per_day) - retention_sec;
}

void RecycleScanner::ScanTask::DoScan() {
  if (stop_) {
    return;
  }
  NameSpace* ns = recycle_scanner_->ns_;
  if (!ns->IsRunning() || !ns->ha_state()->IsActive() ||
      ns->ha_state()->InTransition()) {
    return;
  }

  MetaStorage* ms = ns->meta_storage();
  INode rb_inode;
  StatusCode sc = ms->GetINode(kRootINodeId,
                               kRecycleBinDirNameString,
                               &rb_inode);
  if (sc != StatusCode::kOK) {
    return;
  }
  if (rb_inode.type() != INode_Type_kDirectory) {
    LOG(WARNING) << absl::StrFormat(
        "Invalid inode type %d on RecycleBin path", rb_inode.type());
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return;
  }

  num_rb_++;
  ProcessRecycleBin(kSeparator + kRecycleBinDirNameString, rb_inode);
}

void RecycleScanner::ScanTask::PostScan() {
  scan_end_ = std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      scan_end_ - scan_start_)
                      .count();

  LOG(INFO) << absl::StrFormat(
      "Completed one round in %ld ms"
      "  #RecycleBin: %ld"
      "  #UserBin: %ld deleted, %ld skipped, %ld invalid"
      "  #DateBin: %ld deleted, %ld skipped, %ld invalid",
      duration, num_rb_,
      num_ub_deleted_, num_ub_skipped_, num_ub_invalid_,
      num_db_deleted_, num_db_skipped_, num_db_invalid_);

  recycle_scanner_->num_rb_ = num_rb_;
  recycle_scanner_->num_ub_deleted_ = num_ub_deleted_;
  recycle_scanner_->num_ub_skipped_ = num_ub_skipped_;
  recycle_scanner_->num_ub_invalid_ = num_ub_invalid_;
  recycle_scanner_->num_db_deleted_ = num_db_deleted_;
  recycle_scanner_->num_db_skipped_ = num_db_skipped_;
  recycle_scanner_->num_db_invalid_ = num_db_invalid_;
}

void RecycleScanner::ScanTask::ProcessRecycleBin(
    const std::string& rb_path,
    const INode& rb_inode) {
  StopWatch sw_total(process_recycle_bin_time_);
  sw_total.Start();

  // 1. try to delete each UserBin
  NameSpace* ns = recycle_scanner_->ns_;
  int num_child = 0;
  std::function<bool(const std::string&, const INode&)> cb;
  cb = [&](const std::string& child_path, const INode& child_inode) {
    if (stop_) {
      return false;
    }
    if (!ns->IsRunning() || !ns->ha_state()->IsActive() ||
        ns->ha_state()->InTransition()) {
      return false;
    }

    VLOG(10) << absl::StrFormat(
        "now process child of RecycleBin %s, INode %s",
        child_path, child_inode.ShortDebugString());

    num_child++;
    if (!IsUserBin(child_inode)) {
      num_ub_invalid_++;
      LOG(WARNING) << absl::StrFormat(
          "Invalid entry in RecycleBin, path: %s, inode: %s",
          child_path, child_inode.ShortDebugString());
      return true;
    }

    if (ProcessUserBin(child_path, child_inode)) {
      num_ub_deleted_++;
      return true;
    }
    num_ub_skipped_++;
    return true;
  };
  MetaStorage* ms = ns->meta_storage();
  (void)ms->ForEachINode(rb_inode.id(), rb_path, cb);

  // 2. delete RecycleBin when all children deleted
  if (num_ub_deleted_ < num_child) {
    return;
  }

  VLOG(8) << absl::StrFormat(
      "All UserBin deleted, now try to delete RecycleBin %s", rb_path);
  bool deleted = DeleteEntry(rb_path, false);
  VLOG(8) << absl::StrFormat(
      "RecycleBin deleted %s", deleted ? "successfully" : "failed");
}

bool RecycleScanner::ScanTask::IsUserBin(const INode& inode) {
  // UserBin is sub directory of RecycleBin without RecyclePolicy
  if (inode.type() != INode_Type_kDirectory) {
    return false;
  }
  RecyclePolicy policy;
  if (XAttrs::GetProtoBufXAttr(inode, kRecyclePolicyXAttr, &policy)) {
    return false;
  }
  return true;
}

bool RecycleScanner::ScanTask::ProcessUserBin(
    const std::string& ub_path,
    const INode& ub_inode) {
  StopWatch sw_total(process_user_bin_time_);
  sw_total.Start();

  // 1. try to delete each DateBin
  NameSpace* ns = recycle_scanner_->ns_;
  int num_child = 0;
  std::function<bool(const std::string&, const INode&)> cb;
  cb = [&](const std::string& child_path, const INode& child_inode) {
    if (stop_) {
      return false;
    }
    NameSpace* ns = recycle_scanner_->ns_;
    if (!ns->IsRunning() || !ns->ha_state()->IsActive() ||
        ns->ha_state()->InTransition()) {
      return false;
    }

    VLOG(10) << absl::StrFormat(
        "now process child of UserBin %s, INode %s",
        child_path, child_inode.ShortDebugString());

    num_child++;
    if (!IsDateBin(child_inode)) {
      num_db_invalid_++;
      LOG(WARNING) << absl::StrFormat(
          "Invalid entry in UserBin, path: %s, inode: %s",
          child_path, child_inode.ShortDebugString());
      return true;
    }

    if (IsDateBinExpired(child_inode) && DeleteEntry(child_path, true)) {
      num_db_deleted_++;
      return true;
    }
    num_db_skipped_++;
    return true;
  };
  MetaStorage* ms = ns->meta_storage();
  (void)ms->ForEachINode(ub_inode.id(), ub_path, cb);

  // 2. delete UserBin when all children deleted.
  if (num_db_deleted_ < num_child) {
    return false;
  }

  VLOG(8) << absl::StrFormat(
      "All DateBin deleted, now try to delete UserBin %s", ub_path);
  bool deleted = DeleteEntry(ub_path, false);
  VLOG(8) << absl::StrFormat(
      "UserBin %s deleted %s", ub_path, deleted ? "successfully" : "failed");
  return deleted;
}

bool RecycleScanner::ScanTask::IsDateBin(const INode& inode) {
  // DateBin is sub directory of UserBin without RecyclePolicy
  if (inode.type() != INode_Type_kDirectory) {
    return false;
  }
  RecyclePolicy policy;
  if (XAttrs::GetProtoBufXAttr(inode, kRecyclePolicyXAttr, &policy)) {
    return false;
  }
  uint32_t year, month, day;
  int num = sscanf(inode.name().c_str(), "%u-%u-%u", &year, &month, &day);
  if (num != 3) {
    return false;
  }

  // align to GetRecycleBinPath() in path_util.h
  return true;
}

bool RecycleScanner::ScanTask::IsDateBinExpired(const INode& inode) {
  uint64_t ts_sec = TimeUtil::YMDToSec(inode.name());
  CHECK_GT(ts_sec, 0);
  if (ts_sec < retention_sec_thold_) {
    return true;
  }
  return false;
}

bool RecycleScanner::ScanTask::DeleteEntry(const std::string& path,
                                           bool recursive) {
  VLOG(8) << absl::StrFormat(
      "Delete entry path %s", path);
  NameSpace* ns = recycle_scanner_->ns_;
  SynchronizedRpcClosure done;
  // XXX act as supergroup.
  ns->AsyncDelete(path,
                  recursive,
                  UserGroupInfo("root", "supergroup"),
                  LogRpcInfo(),
                  &done);
  done.Await();
  const Status& st = done.status();
  VLOG(8) << absl::StrFormat(
      "Delete entry status %s", st.ToString());

  if (!st.IsOK()) {
    LOG(WARNING) << absl::StrFormat(
        "Delete entry failed, path %s, status %s",
        path, st.ToString());
    return false;
  }
  return true;
}

}  // namespace dancenn
