
#include "meta_storage_constants.h"

#include <cstdint>

namespace dancenn {

const char* kINodeDefaultCFName = "default";
const uint32_t kINodeDefaultCFIndex = 0;
const char* kINodePendingDeleteCFName = "pending_delete";
const uint32_t kINodePendingDeleteCFIndex = 1;
const char* kNameSystemInfoCFName = "namesystem_info";
const uint32_t kNameSystemInfoCFIndex = 2;
const char* kAccessCounterCFName = "access_counter";
const uint32_t kAccessCounterCFIndex = 3;
const char* kINodeIndexCFName = "inode_index";
const uint32_t kINodeIndexCFIndex = 4;
const char* kLegacyBlockPufsInfoCFName = "block_pufs_info";
const uint32_t kLegacyBlockPufsInfoCFIndex = 5;
const char* kLegacyDeprecatedBlockPufsInfoCFName = "deprecated_block_pufs_info";
const uint32_t kLegacyDeprecatedBlockPufsInfoCFIndex = 6;
const char* kLegacyBlockInfoProtoCFName = "block_info";
const uint32_t kLegacyBlockInfoProtoCFIndex = 7;
const char* kBlockInfoProtoCFName = "block_info_proto";
const uint32_t kBlockInfoProtoCFIndex = 8;
const char* kLocalBlockCFName = "local_block";
const uint32_t kLocalBlockCFIndex = 9;
// Why does cfs need deprecating block column family?
// Avoid conflicts between uploading blocks and deleting blocks.
// Detail: https://bytedance.feishu.cn/docs/doccnMKaH5G2i2E9dvJePW3wchf
const char* kDeprecatingBlockCFName = "deprecating_block";
const uint32_t kDeprecatingBlockCFIndex = 10;
const char* kDeprecatedBlockCFName = "deprecated_block";
const uint32_t kDeprecatedBlockCFIndex = 11;
const char* kINodeStatCFName = "inode_stat";
const uint32_t kINodeStatCFIndex = 12;
const char* kLifecyclePolicyCFName = "lifecycle_policy";
const uint32_t kLifecyclePolicyCFIndex = 13;
const char* kStorageClassStatCFName = "storage_class_stat";
const uint32_t kStorageClassStatCFIndex = 14;
const char* kStorageClassReportCFName = "storage_class_report";
const uint32_t kStorageClassReportCFIndex = 15;
const char* kLeaseCFName = "lease";
const uint32_t kLeaseCFIndex = 16;
const char* kDatanodeInfoCFName = "datanode_info";
const uint32_t kDatanodeInfoCFIndex = 17;
const char* kSnapshotRootInfoCFName = "snapshot_root_info";
const uint32_t kSnapshotRootInfoCFIndex = 18;
const char* kSnapshotInodeCFName = "snapshot_inode";
const uint32_t kSnapshotInodeCFIndex = 19;
const char* kSnapshotRenameRecordCFName = "snapshot_rename_record";
const uint32_t kSnapshotRenameRecordCFIndex = 20;
const char* kSnapshotINodeIndexCFName = "snapshot_inode_index";
const uint32_t kSnapshotINodeIndexCFIndex = 21;
const char* kJobInfoCFName = "job_info";
const uint32_t kJobInfoCFIndex = 22;
const char* kWriteBackTaskCFName = "write_back_task";
const uint32_t kWriteBackTaskCFIndex = 23;
const char* kDirPolicyCFName = "dir_policy";
const uint32_t kDirPolicyCFIndex = 24;
const char* kINodeAttrTtlCFName = "inode_attr_ttl";
const uint32_t kINodeAttrTtlCFIndex = 25;
const char* kTtlATimeCFName = "ttl_atime";
const uint32_t kTtlATimeCFIndex = 26;

}
