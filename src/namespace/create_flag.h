//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_CREATE_FLAG_H_
#define NAMESPACE_CREATE_FLAG_H_

#include <ClientNamenodeProtocol.pb.h>

namespace dancenn {

// reference to CreateFlagProto and org/apache/hadoop/fs/CreateFlag.java
//
// there are 4 members in CreateFlagProto
// and 5 members in org/apache/hadoop/fs/CreateFlag.java
// SYNC_BLOCK(0x08) only used in DFSOutputStream,
// when finish block and close output stream
class CreateFlag {
 public:
  static inline bool ContainsCreate(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::CREATE)
        == ::cloudfs::CreateFlagProto::CREATE;
  }

  static inline bool ContainsOverwrite(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::OVERWRITE)
        == ::cloudfs::CreateFlagProto::OVERWRITE;
  }

  static inline bool ContainsAppend(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::APPEND)
        == ::cloudfs::CreateFlagProto::APPEND;
  }

  static inline bool ContainsLazyPersist(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::LAZY_PERSIST)
        == ::cloudfs::CreateFlagProto::LAZY_PERSIST;
  }

  static inline bool ContainsAccAsync(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::ACC_ASYNC) ==
           ::cloudfs::CreateFlagProto::ACC_ASYNC;
  }

  static inline bool ContainsAccAppendable(uint32_t flag) {
    return (flag & ::cloudfs::CreateFlagProto::ACC_APPENDABLE) ==
           ::cloudfs::CreateFlagProto::ACC_APPENDABLE;
  }

  static inline uint32_t SetCreate(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::CREATE;
  }

  static inline uint32_t SetOverwrite(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::OVERWRITE;
  }

  static inline uint32_t SetAppend(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::APPEND;
  }

  static inline uint32_t SetLazyPersist(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::LAZY_PERSIST;
  }

  static inline uint32_t SetAccAsync(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::ACC_ASYNC;
  }

  static inline uint32_t SetAccAppendable(uint32_t flag) {
    return flag | ::cloudfs::CreateFlagProto::ACC_APPENDABLE;
  }
};

}  // namespace dancenn

#endif  // NAMESPACE_CREATE_FLAG_H_
