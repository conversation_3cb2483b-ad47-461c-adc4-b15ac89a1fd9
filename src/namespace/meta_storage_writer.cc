#include "namespace/meta_storage_writer.h"
#include "namespace/meta_storage_write_task.h"
#include "namespace/meta_storage.h"
#include "base/stop_watch.h"

DECLARE_int32(dfs_meta_storage_pending_commit_queue_size);
DECLARE_int32(dfs_meta_storage_max_write_batch_size);

namespace dancenn {
namespace meta_storage {

Writer::Writer(MetaStorage* storage)
    : notified_txid_start_(1),
      notified_txid_end_(1),
      empty_notified_num_(0),
      bg_task_queue_(FLAGS_dfs_meta_storage_pending_commit_queue_size),
      storage_(storage) {
  pending_commit_task_queue_ = std::make_unique<UniqueTxidRingWindow>(
      FLAGS_dfs_meta_storage_pending_commit_queue_size);

  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("MetaStorageWriter");

  wtask_push_fg_task_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=push#priority=fg#type=task");
  wtask_push_fg_callback_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=push#priority=fg#type=callback");
  wtask_pop_fg_task_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=pop#priority=fg#type=task");
  wtask_push_bg_task_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=push#priority=bg#type=task");
  wtask_push_bg_callback_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=push#priority=bg#type=callback");
  wtask_pop_bg_task_num_ = metrics->RegisterCounter(
      "WriteTaskQueue#action=pop#priority=bg#type=task");

  writer_thread_fetch_fg_time_ = metrics->RegisterHistogram(
      "WriterThread#step=FetchFg");
  writer_thread_fetch_bg_time_ = metrics->RegisterHistogram(
      "WriterThread#step=FetchBg");
  writer_thread_write_time_ = metrics->RegisterHistogram(
      "WriterThread#step=Write");
  writer_thread_fast_time_ = metrics->RegisterHistogram(
      "WriterThread#step=Fast");
  writer_thread_slow_time_ = metrics->RegisterHistogram(
      "WriterThread#step=Slow");

  wtask_wait_time_ = metrics->RegisterHistogram(
      "WriteTask#step=Wait");
  wtask_exec_fast_callback_time_ = metrics->RegisterHistogram(
      "WriteTask#step=FastCallback");
  wtask_exec_slow_callback_time_ = metrics->RegisterHistogram(
      "WriteTask#step=SlowCallback");

  wtask_exec_empty_callback_task_num_ = metrics->RegisterCounter(
      "WriteTaskExecCallback#type=fast#callback_num=empty");
  wtask_exec_fast_single_callback_task_num_ = metrics->RegisterCounter(
      "WriteTaskExecCallback#type=fast#callback_num=single");
  wtask_exec_slow_single_callback_task_num_ = metrics->RegisterCounter(
      "WriteTaskExecCallback#type=slow#callback_num=single");
  wtask_exec_slow_multiple_callback_task_num_ = metrics->RegisterCounter(
      "WriteTaskExecCallback#type=slow#callback_num=multiple");

  pending_task_include_bg_ = metrics->RegisterGauge("PendingTaskIncludeBg",
      [this]() -> int {
        return NumPending(true);
      });
  pending_task_exclude_bg_ = metrics->RegisterGauge("PendingTaskExcludeBg",
      [this]() -> int {
        return NumPending(false);
      });
}

void Writer::LastApplyTxid(int64_t txid) {
  notified_txid_start_ = txid + 1;
#ifdef __aarch64__
  asm volatile("dsb sy" ::: "memory");
#else
  asm volatile("":: : "memory");
#endif
  notified_txid_end_ = txid + 1;
  empty_notified_num_ = 0;
  pending_commit_task_queue_->NextApplyTxid(txid + 1);
}

void Writer::TxFinish(int64_t start_txid, int n) {
  if (is_active_ && notified_txid_end_ != start_txid) {
    LOG(FATAL) << "notified_txid_end: " << notified_txid_end_
               << ", start_txid: " << start_txid;
  }
  std::unique_lock<std::mutex> lock(mutex_);
  // after start, update notified_txid_end_ only here
  notified_txid_end_ = start_txid + n;
  cond_var_.notify_all();
}

void Writer::Push(WriteTask* task) {
  MFC(wtask_push_fg_task_num_)->Inc();
  if (task->HasNext()) {
    MFC(wtask_push_fg_callback_num_)->Inc();
  } else if (task->HasSubTask()) {
    MFC(wtask_push_fg_callback_num_)->Inc(task->sub_tasks_size());
  }

  task->Tick();
  pending_.fetch_add(1);
  pending_commit_task_queue_->Push(task->txid(), task);
  NotifyForStandby();
}

void Writer::PushBGTask(WriteTask* task) {
  MFC(wtask_push_bg_task_num_)->Inc();
  if (task->HasNext()) {
    MFC(wtask_push_bg_callback_num_)->Inc();
  } else if (task->HasSubTask()) {
    MFC(wtask_push_bg_callback_num_)->Inc(task->sub_tasks_size());
  }

  task->Tick();
  while (!bg_task_queue_.LockedTryPush(task)) {
    std::this_thread::sleep_for(std::chrono::microseconds(500));
  }
  if (is_active_) {
    std::unique_lock<std::mutex> lock(mutex_);
    cond_var_.notify_all();
  } else {
    std::unique_lock<std::mutex> lock(empty_notified_mutex_);
    empty_notified_cond_var_.notify_all();
  }
}

void Writer::WaitNoPending(bool include_bg) {
  while (true) {
    int n = NumPending(include_bg);
    if (n == 0) break;
    LOG(INFO) << "MetaStorageWriter wait no pending, remain:  " << n;
    usleep(1000);
  }
}

void Writer::StartActive() {
  // when the task queue is empty, writer thread will sleep
  // while meeting the 'EmptyForStandby()' conditions.
  // for example, in startup, etc.
  NotifyForStandby();

  is_active_ = true;
}

void Writer::StartStandby() {
  is_active_ = false;
}

void Writer::NotifyForActive(int64_t txid) {
  // when the task queue is empty, writer thread will sleep
  // while meeting the 'EmptyForActive()' conditions.
  // for example, there aren't any 'EditLogOp', etc.
  std::unique_lock<std::mutex> lock(mutex_);
  notified_txid_end_ = txid + 1;
  cond_var_.notify_all();
}

void Writer::NotifyForStandby() {
  if (!is_active_) {
    std::unique_lock<std::mutex> lock(empty_notified_mutex_);
    empty_notified_num_++;
    empty_notified_cond_var_.notify_all();
  }
}

bool Writer::operator()(void* arg __attribute__((unused))) {
  std::vector<meta_storage::WriteTask*> tasks;
  while (!IsStopped()) {
    {
      std::unique_lock<std::mutex> lock(mutex_);
      while (!IsStopped() && is_active_ && EmptyForActive()) {
        cond_var_.wait_for(lock, std::chrono::milliseconds(1));
      }
      if (IsStopped()) {
        break;
      }
    }

    {
      std::unique_lock<std::mutex> lock(empty_notified_mutex_);
      while (!IsStopped() && !is_active_ && EmptyForStandby()) {
        empty_notified_cond_var_.wait_for(lock, std::chrono::milliseconds(1));
      }
      if (IsStopped()) {
        break;
      }
    }

    std::unique_lock<std::mutex> guard(pending_task_mutex_);

    // Get foreground task
    StopWatch sw(writer_thread_fetch_fg_time_);
    sw.Start();

    int64_t n = is_active_ ? (notified_txid_end_ - notified_txid_start_)
      : FLAGS_dfs_meta_storage_pending_commit_queue_size;
    if (n > FLAGS_dfs_meta_storage_max_write_batch_size) {
      n = FLAGS_dfs_meta_storage_max_write_batch_size;
    }
    if (n > 0) {
      pending_commit_task_queue_->Pop(n, &tasks);
    }
    auto num_ordered_tasks = tasks.size();
    MFC(wtask_pop_fg_task_num_)->Inc(num_ordered_tasks);

    // Get background task
    sw.NextStep(writer_thread_fetch_bg_time_);
    WriteTask* task;
    while (bg_task_queue_.TryPop(&task)) {
      tasks.emplace_back(task);
    }
    MFC(wtask_pop_bg_task_num_)->Inc(tasks.size() - num_ordered_tasks);

    if (tasks.size() > 0) {

      // Update metrics about waiting time in queue before writing to RocksDB
      for (auto* t : tasks) {
        MFH(wtask_wait_time_)->Update(t->NextTick());
      }

      // after start, update notified_txid_start_ only here,
      // so need no any lock or atomic
      sw.NextStep(writer_thread_write_time_);
      notified_txid_start_ += num_ordered_tasks;
      storage_->BatchWrite(tasks);

      // finish all fast tasks first to avoid potential pending or even dead
      // lock caused by lease lock
      sw.NextStep(writer_thread_fast_time_);
      for (auto& task : tasks) {
        CHECK_NOTNULL(task);
        // 1. no callback need to process, just release here
        if (!task->HasNext() && !task->HasSubTask()) {
          delete task;
          task = nullptr;
          MFC(wtask_exec_empty_callback_task_num_)->Inc();
          continue;
        }
        // 2. single-closure with fast callback to be done ASAP
        if (task->HasNext() && !task->IsSlow()) {
          // Call Tick() to set the time to current time.
          task->Tick();
          task->Next();
          MFH(wtask_exec_fast_callback_time_)->Update(task->NextTick());
          MFC(wtask_exec_fast_single_callback_task_num_)->Inc();
          delete task;
          task = nullptr;
          continue;
        }
        // 3. single-closure with slow callback to be dispatched later
        if (task->HasNext()) {
          CHECK(task->IsSlow());
          continue;
        }
        // 4. multiple-closure with slow callback to be dispatched later
        CHECK(task->HasSubTask());
      }

      sw.NextStep(writer_thread_slow_time_);
      for (auto& task : tasks) {
        if (task == nullptr) {
          continue;
        }
        CHECK_EQ(task->HasNext(), !task->HasSubTask());
        // do not use task->Tick() here, since it may be dtor-ed
        StopWatch sw(wtask_exec_slow_callback_time_);
        sw.Start();
        if (task->HasNext()) {
          MFC(wtask_exec_slow_single_callback_task_num_)->Inc();
          CHECK(task->IsSlow());
          storage_->PostSlow(task);
          task = nullptr;
        } else if (task->HasSubTask()) {
          MFC(wtask_exec_slow_multiple_callback_task_num_)->Inc();
          for (size_t subi = 0; subi < task->sub_tasks_size(); subi++) {
            meta_storage::WriteTask* sub_task = task->release_sub_task(subi);
            storage_->PostSlow(sub_task);
          }
          delete task;
          task = nullptr;
        }
      }
      if (!is_active_) {
        std::unique_lock<std::mutex> lock(empty_notified_mutex_);
        empty_notified_num_ -= num_ordered_tasks;
      }
      pending_.fetch_sub(num_ordered_tasks);
      tasks.clear();
    }
  }
  return true;
}

void Writer::DropAllWriteTaskForActiveUntilFinish(
    const std::function<bool()>& finish_checker) {
  std::vector<meta_storage::WriteTask*> tasks;

  std::unique_lock<std::mutex> guard(pending_task_mutex_);
  CHECK(is_active_);

  LOG(INFO) << "DropAllWriteTaskForActiveUntilFinish Start";
  while (true) {
    // Get online task

    int64_t n = FLAGS_dfs_meta_storage_max_write_batch_size;
    pending_commit_task_queue_->Pop(n, &tasks);

    auto num_ordered_tasks = tasks.size();
    if (tasks.size()) {
      notified_txid_start_ += num_ordered_tasks;

      // Mark all failed
      for (size_t i = 0; i < tasks.size(); ++i) {
        Status st = Status(JavaExceptions::kStandbyException,
                           "Write EditLog Failed (committed but write failed)");
        tasks[i]->SetFailed(std::move(st));

        LOG(INFO) << "task " << tasks[i]->DebugString();

        if (tasks[i]->HasNext()) {
          // Call Tick() to set the time to current time.
          tasks[i]->Tick();
          tasks[i]->Next();

          delete tasks[i];
          tasks[i] = nullptr;
        }
      }

      LOG(INFO) << "Apply: " << notified_txid_start_
                << ", total: " << tasks.size()
                << ", num_ordered_tasks: " << num_ordered_tasks;
      pending_.fetch_sub(num_ordered_tasks);
      tasks.clear();
    }

    // Check.
    // This may result in a infinity loop. Be careful.
    if (finish_checker()) {
      return;
    }
  }
}

}  // namespace meta_storage
}  // namespace dancenn
