#ifndef _LEASE_SCANNER_LISTENER_H_
#define _LEASE_SCANNER_LISTENER_H_

#include <string>

#include "namespace/meta_scanner.h"

namespace dancenn {

class LeaseScannerListener : public MetaScanner::Listener {
 public:
  explicit LeaseScannerListener(NameSpace* ns)
      : MetaScanner::Listener(desc()),
        ns_(ns) {
  }

  std::vector<INodeID> ScanIndexes() override;
  bool Handle(const std::string& full_path, const INode& inode) override;

 private:
  void CheckConsistencyBetweenLeaseAndINode(const INode& inode);

  static const std::string desc() {
    return "lease_scanner_listener";
  }

  NameSpace* ns_{nullptr};
  std::chrono::steady_clock::time_point scan_start_;
  int64_t num_invalid_ {0};
  int64_t num_under_replicated_ {0};
  int64_t num_over_replicated_ {0};
  int64_t num_postponed_ {0};
  int64_t num_corrupt_ {0};
  int64_t num_under_construction_ {0};
  int64_t processed_ {0};
};

}  // namespace dancenn

#endif  // _LEASE_SCANNER_LISTENER_H_
