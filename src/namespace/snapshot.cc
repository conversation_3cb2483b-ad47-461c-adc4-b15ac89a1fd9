#include "namespace/namespace.h"
#include "base/time_util.h"
#include "base/constants.h"
#include "base/path_util.h"
#include "namespace/meta_storage_constants.h"
#include "security/block_token_identifier.h"

#include <absl/strings/substitute.h>

// Snapshot relate api implementation of NameSpace class

DECLARE_int32(snapshot_gc_deletion_num_in_batch);
DECLARE_bool(enable_snapshot_feature);

namespace dancenn {

namespace {

bool CheckSnapshotFeatureEnabled(RpcClosure* rpc_done) {
  if (!FLAGS_enable_snapshot_feature) {
    rpc_done->set_status(Status(JavaExceptions::kUnsupportedOperationException,
                                "Snapshot feature is disabled."));
    return false;
  }
  return true;
}

// Check whether `inode` is possible to be SnapshotRoot or not
// check_snapshottable: if true, check `inode` is already snapshottable or not
// return false if not pass check
bool CheckINodeAsSnapshotRoot(const StatusCode& get_inode_status,
                              const INode& inode,
                              const std::string& snapshot_root,
                              bool check_snapshottable,
                              RpcClosure* rpc_done) {
  if (get_inode_status != StatusCode::kOK) {
    rpc_done->set_status(Status(JavaExceptions::kFileNotFoundException,
                                "Snapshot root not found " + snapshot_root));
    return false;
  }
  if (inode.type() != INode_Type_kDirectory) {
    rpc_done->set_status(
        Status(JavaExceptions::kSnapshotException,
               "Snapshot root is not directory " + snapshot_root));
    return false;
  }
  if (check_snapshottable) {
    if (!inode.has_is_snapshottable() || !inode.is_snapshottable()) {
      rpc_done->set_status(Status(
          JavaExceptions::kSnapshotException,
          "Directory is not a snapshottable directory: " + snapshot_root));
      return false;
    }
  }
  return true;
}
}  // namespace

void NameSpace::AsyncAllowSnapshot(const std::string& src,
                                   RpcClosure* rpc_done) {
  ClosureGuard done_guard(rpc_done);
  if (!CheckSnapshotFeatureEnabled(rpc_done)) {
    return;
  }

  StringPieces path_components;
  StringPieces lock_components;
  if (!CheckValidPathAndCanWrite(
          src, &path_components, &lock_components, rpc_done)) {
    return;
  }

  // The lock also avoid allowing nested path concurrently
  rpc_done->set_locks(WriteLockTree(&lock_components));

  INode last_inode;
  auto res = GetLastINodeInPath(path_components, &last_inode);

  // 1. check snapshot root inode is directory and no nested snapshot
  if (!CheckINodeAsSnapshotRoot(
          res, last_inode, src, false /*check_snapshottable*/, rpc_done)) {
    return;
  }
  RETURN_SET_CLOSURE_IF_ERROR(snapshot_manager_->CheckNestedSnapshot(src),
                              rpc_done);

  last_inode.set_is_snapshottable(true);

  // 2. send edit log, persist storage, update memory struct
  auto txid = edit_log_sender_->LogAllowSnapshotV2(src, last_inode);

  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);
  rpc_done->set_callback(
      // copy src and id because callback exceeds their lifecycle
      [this, id = last_inode.id(), src](const Status& status) {
        if (LIKELY(status.IsOK())) {
          auto s = snapshot_manager_->AllowSnapshot(id, src);
          CHECK(s.IsOK()) << "snapshot_manager_ AllowSnapshot failed: "
                          << s.ToString();
        }
      });
  meta_storage_->OrderedUpdateINodeAndSnapshotData(last_inode,
                                                   MetaStorage::ALLOW_SNAPSHOT,
                                                   &src,
                                                   nullptr /*new_snapshot_id*/,
                                                   txid,
                                                   done_guard.release());
}

void NameSpace::AsyncDisAllowSnapshot(const std::string& src,
                                      RpcClosure* rpc_done) {
  ClosureGuard done_guard(rpc_done);
  if (!CheckSnapshotFeatureEnabled(rpc_done)) {
    return;
  }

  StringPieces path_components;
  StringPieces lock_components;
  if (!CheckValidPathAndCanWrite(
          src, &path_components, &lock_components, rpc_done)) {
    return;
  }

  rpc_done->set_locks(WriteLockTree(&lock_components));

  INode last_inode;
  auto res = GetLastINodeInPath(path_components, &last_inode);

  // 1. check snapshot root inode is directory and no snapshot on it
  if (!CheckINodeAsSnapshotRoot(
          res, last_inode, src, false /*check_snapshottable*/, rpc_done)) {
    return;
  }
  if (last_inode.snapshots_size() > 0) {
    int deleted_num = 0;
    for (const auto& snapshot : last_inode.snapshots()) {
      if (snapshot.has_deleted() && snapshot.deleted()) {
        deleted_num++;
      }
    }
    if (last_inode.snapshots_size() != deleted_num) {
      rpc_done->set_status(
          Status(JavaExceptions::kSnapshotException,
                 absl::Substitute(
                     "The directory $0 has $1 snapshot(s). "
                     "Please redo the operation after deleting all snapshots.",
                     src,
                     last_inode.snapshots_size() - deleted_num)));
      return;
    }
  }
  last_inode.clear_snapshots();
  last_inode.set_is_snapshottable(false);

  // 2. send edit log, persist storage, update memory struct
  auto txid = edit_log_sender_->LogDisallowSnapshotV2(src, last_inode);

  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);
  rpc_done->set_callback(
      [this, id = last_inode.id(), src](const Status& status) {
        if (LIKELY(status.IsOK())) {
          auto s = snapshot_manager_->DisallowSnapshot(id, src);
          CHECK(s.IsOK()) << "snapshot_manager_ DisallowSnapshot failed: "
                          << s.ToString();
        }
      });
  meta_storage_->OrderedUpdateINodeAndSnapshotData(
      last_inode,
      MetaStorage::DISALLOW_SNAPSHOT,
      nullptr /*src*/,
      nullptr /*new_snapshot_id*/,
      txid,
      done_guard.release());
}

void NameSpace::AsyncCreateSnapshot(
    const std::string& src,
    const std::string& input_snapshot_name,
    ::cloudfs::CreateSnapshotResponseProto* result,
    RpcClosure* rpc_done) {
  ClosureGuard done_guard(rpc_done);
  result->Clear();
  if (!CheckSnapshotFeatureEnabled(rpc_done)) {
    return;
  }

  StringPieces path_components;
  StringPieces lock_components;
  if (!CheckValidPathAndCanWrite(
          src, &path_components, &lock_components, rpc_done)) {
    return;
  }

  // 1. check snapshot_name valid, generate a default name if empty
  auto snapshot_name = input_snapshot_name;
  if (snapshot_name.empty()) {
    // for example: s20230306-181648.116
    auto epoch_ms = TimeUtil::GetNowEpochMs();
    snapshot_name = TimeUtil::GetTimeFormatted(epoch_ms / 1000,
                                               kDefaultSnapshotNamePattern) +
                    "." + std::to_string(epoch_ms % 1000);
  }
  RETURN_SET_CLOSURE_IF_ERROR(IsValidNameForComponent(snapshot_name), rpc_done);

  // 2. check snapshot root inode is snapshottable
  // check snapshot id limit, snapshot num limit
  rpc_done->set_locks(WriteLockTree(&lock_components));

  INode last_inode;
  auto res = GetLastINodeInPath(path_components, &last_inode);
  if (!CheckINodeAsSnapshotRoot(
          res, last_inode, src, true /*check_snapshottable*/, rpc_done)) {
    return;
  }
  RETURN_SET_CLOSURE_IF_ERROR(
      snapshot_manager_->CheckSnapshotNumLimit(last_inode.snapshots_size()),
      rpc_done);

  for (const auto& snapshot : last_inode.snapshots()) {
    if (snapshot.has_deleted() && snapshot.deleted()) {
      continue;
    }
    if (snapshot.name() == snapshot_name) {
      rpc_done->set_status(
          Status(JavaExceptions::kSnapshotException,
                 absl::Substitute("There is already snapshot with the same "
                                  "name: $0, snapshot_id=$1",
                                  snapshot_name,
                                  snapshot.snapshot_id())));
      return;
    }
  }

  uint64_t new_snapshot_id = 0;
  RETURN_SET_CLOSURE_IF_ERROR(
      snapshot_manager_->AcquireSnapshotID(&new_snapshot_id), rpc_done);
  auto now_ms = TimeUtil::GetNowEpochMs();

  last_inode.set_mtime(now_ms);
  auto mutable_snapshots = last_inode.mutable_snapshots();
  mutable_snapshots->Add();
  auto new_snapshot = mutable_snapshots->rbegin();
  new_snapshot->set_snapshot_id(new_snapshot_id);
  new_snapshot->set_name(snapshot_name);
  new_snapshot->set_deleted(false);
  new_snapshot->set_mtime(now_ms);

  // 3. send edit log, persist storage, update memory struct
  auto txid = edit_log_sender_->LogCreateSnapshotV2(
      src, snapshot_name, now_ms, last_inode, new_snapshot_id);
  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);

  // XXX 'create_txid' field is not recorded in editlog
  new_snapshot->set_create_txid(txid);

  meta_storage_->OrderedUpdateINodeAndSnapshotData(last_inode,
                                                   MetaStorage::CREATE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   &new_snapshot_id,
                                                   txid,
                                                   done_guard.release());
  VLOG(2) << absl::Substitute(
      "CreateSnapshot: name=$0, root=$1 $2, create_txid=$3, id=$4",
      new_snapshot->name(),
      src,
      last_inode.id(),
      new_snapshot->create_txid(),
      new_snapshot_id);

  // already checked NormalizePath
  result->set_snapshotpath(absl::Substitute(
      "$0$1$2$1$3", src, kSeparator, kDotSnapshotDir, snapshot_name));
}

void NameSpace::AsyncDeleteSnapshot(const std::string& src,
                                    const std::string& snapshot_name,
                                    RpcClosure* rpc_done) {
  ClosureGuard done_guard(rpc_done);
  if (!CheckSnapshotFeatureEnabled(rpc_done)) {
    return;
  }

  StringPieces path_components;
  StringPieces lock_components;
  if (!CheckValidPathAndCanWrite(
          src, &path_components, &lock_components, rpc_done)) {
    return;
  }

  // 1. check snapshot root inode is snapshottable
  // check snapshot exists, mark snapshot deleted

  rpc_done->set_locks(WriteLockTree(&lock_components));

  INode last_inode;
  auto res = GetLastINodeInPath(path_components, &last_inode);
  if (!CheckINodeAsSnapshotRoot(
          res, last_inode, src, true /*check_snapshottable*/, rpc_done)) {
    return;
  }

  int64_t deleted_snapshot_create_txid = 0;
  bool need_update = false;
  for (auto& snapshot : *last_inode.mutable_snapshots()) {
    if (snapshot.name() == snapshot_name) {
      need_update = !(snapshot.has_deleted() && snapshot.deleted());
      snapshot.set_deleted(true);
      deleted_snapshot_create_txid = snapshot.create_txid();
      break;
    }
  }
  if (!need_update) {
    VLOG(2) << "DeleteSnapshot: snapshot " << snapshot_name << " under " << src
            << " does not exist or is deleted";
    return;
  }
  auto now_ms = TimeUtil::GetNowEpochMs();
  last_inode.set_mtime(now_ms);

  // 2. send edit log, persist storage, update memory struct
  auto txid = edit_log_sender_->LogDeleteSnapshotV2(
      src, snapshot_name, now_ms, last_inode);
  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);

  meta_storage_->OrderedUpdateINodeAndSnapshotData(last_inode,
                                                   MetaStorage::DELETE_SNAPSHOT,
                                                   nullptr /*src*/,
                                                   nullptr /*new_snapshot_id*/,
                                                   txid,
                                                   done_guard.release());
  VLOG(2) << absl::Substitute(
      "DeleteSnapshot: name=$0, root=$1 $2, create_txid=$3",
      snapshot_name,
      src,
      last_inode.id(),
      deleted_snapshot_create_txid);
}

void NameSpace::AsyncRenameSnapshot(const std::string& src,
                                    const std::string& snapshot_old_name,
                                    const std::string& snapshot_new_name,
                                    RpcClosure* rpc_done) {
  ClosureGuard done_guard(rpc_done);
  if (!CheckSnapshotFeatureEnabled(rpc_done)) {
    return;
  }

  StringPieces path_components;
  StringPieces lock_components;
  if (!CheckValidPathAndCanWrite(
          src, &path_components, &lock_components, rpc_done)) {
    return;
  }

  // 1. check snapshot_new_name is valid
  RETURN_SET_CLOSURE_IF_ERROR(IsValidNameForComponent(snapshot_new_name),
                              rpc_done);

  // 2. check snapshot root inode is snapshottable
  // check snapshot with old name exists and snapshot with new name doesn't
  // exist, modify snapshot's name
  rpc_done->set_locks(WriteLockTree(&lock_components));

  INodeInPath last_iip;
  const INode& last_inode = last_iip.Inode();
  auto res = GetLastINodeInPath(path_components, &last_iip);
  if (!CheckINodeAsSnapshotRoot(
          res, last_inode, src, true /*check_snapshottable*/, rpc_done)) {
    return;
  }
  if (snapshot_old_name == snapshot_new_name) {
    return;
  }

  bool target_snapshot_found = false;
  for (auto& snapshot : *last_iip.MutableInode().mutable_snapshots()) {
    if (snapshot.has_deleted() && snapshot.deleted()) {
      continue;
    }
    if (snapshot.name() == snapshot_old_name) {
      target_snapshot_found = true;
      snapshot.set_name(snapshot_new_name);
    } else if (snapshot.name() == snapshot_new_name) {
      rpc_done->set_status(Status(JavaExceptions::kSnapshotException,
                                  "The snapshot with name " +
                                      snapshot_new_name + " under " + src +
                                      " already exists"));
      return;
    }
  }
  if (!target_snapshot_found) {
    rpc_done->set_status(Status(JavaExceptions::kSnapshotException,
                                "The snapshot with name " + snapshot_old_name +
                                    " under " + src + " does not exist"));
    return;
  }
  auto now_ms = TimeUtil::GetNowEpochMs();
  last_iip.SetModificationTime(now_ms);
  CHECK(!last_iip.HasSnapshotINode())
      << "found nested snapshot root " << last_iip.DebugINodeStr();

  SnapshotLog inode_snaplog;
  last_iip.GenerateSnapshotLog(&inode_snaplog);

  // 3. send edit log, persist storage, update memory struct
  auto txid = edit_log_sender_->LogRenameSnapshotV2(src,
                                                    snapshot_old_name,
                                                    snapshot_new_name,
                                                    now_ms,
                                                    last_inode,
                                                    inode_snaplog);

  CHECK_NE(txid, kInvalidTxId);
  SetServerTxid(rpc_done, txid);

  meta_storage_->OrderedUpdateINode(&last_iip.MutableInode(),
                                    nullptr,
                                    inode_snaplog,
                                    txid,
                                    done_guard.release());
  VLOG(2) << absl::Substitute(
      "RenameSnapshot: old_name=$0, new_name=$1, root=$2 $3",
      snapshot_old_name,
      snapshot_new_name,
      src,
      last_inode.id());
}

Status NameSpace::GetSnapshottableDirListing(
    ::cloudfs::SnapshottableDirectoryListingProto* result,
    RpcController* ctx) {
  CHECK_NOTNULL(result);

  auto cres = ha_state_->CheckOperation(OperationsCategory::kRead);
  if (cres.first.HasException()) {
    return cres.first;
  }
  auto tres = CheckClientTxid(ctx);
  if (tres.HasException()) {
    return tres;
  }

  std::vector<std::pair<INode, std::string>> snapshot_root_inodes;
  RETURN_NOT_OK(GetSnapshotRoots(&snapshot_root_inodes));

  auto& snapshot_dirs = *result->mutable_snapshottabledirlisting();
  snapshot_dirs.Clear();
  snapshot_dirs.Reserve(snapshot_root_inodes.size());
  UserGroupInfo default_ugi = UserGroupInfo();
  std::vector<AccessMode> modes{AccessMode::READ};
  for (size_t i = 0; i < snapshot_root_inodes.size(); i++) {
    const auto& inode = snapshot_root_inodes[i].first;
    const auto& path = snapshot_root_inodes[i].second;
    CHECK(inode.is_snapshottable()) << inode.DebugString();
    auto* snapshot_dir = snapshot_dirs.Add();
    snapshot_dir->set_snapshot_number(inode.snapshots_size());
    // snapshot quota is not supported
    snapshot_dir->set_snapshot_quota(0);
    snapshot_dir->set_parent_fullpath(path);
    // TODO(zhuangsiyu): pin in snapshot?
    ConstructFileStatus(path,
                        inode,
                        false,
                        false,
                        NetworkLocationInfo(),
                        default_ugi,
                        modes,
                        snapshot_dir->mutable_dirstatus());
  }

  return Status::OK();
}

Status NameSpace::GetSnapshotDiff(const std::string& src,
                                  const std::string& from_snapshot,
                                  const std::string& to_snapshot,
                                  ::cloudfs::SnapshotDiffReportProto* result,
                                  RpcController* ctx) {
  result->set_snapshotroot(src);
  result->set_fromsnapshot(from_snapshot);
  result->set_tosnapshot(to_snapshot);
  result->clear_diffreportentries();
  auto& report_entries= *result->mutable_diffreportentries();


  return Status::OK();
}

Status NameSpace::GetSnapshotRoots(
    std::vector<std::pair<INode, std::string>>* snapshot_root_inodes) {
  std::vector<std::pair<INodeID, std::string>> snapshot_roots;
  RETURN_NOT_OK(snapshot_manager_->GetSnapshotRoots(&snapshot_roots));

  snapshot_root_inodes->resize(snapshot_roots.size());
  for (size_t i = 0; i < snapshot_roots.size(); i++) {
    (*snapshot_root_inodes)[i].second = std::move(snapshot_roots[i].second);
    auto& inode_result = (*snapshot_root_inodes)[i].first;

    const auto& path = (*snapshot_root_inodes)[i].second;
    StringPieces lock_components;
    if (!GetAllAncestorPaths(path, &lock_components)) {
      return Status(JavaExceptions::kInvalidPathException,
                    "Found corrupted cache data, invalid path: " + path);
    }

    auto locks = ReadLockTree(&lock_components);
    StatusCode sc =
        meta_storage_->GetINode(snapshot_roots[i].first, &inode_result);
    if (UNLIKELY(sc != StatusCode::kOK)) {
      return Status(JavaExceptions::kFileNotFoundException,
                    absl::Substitute("Failed to get snapshot root inode: "
                                     "inode_id=$0, path=$1, code=$2",
                                     snapshot_roots[i].first,
                                     path,
                                     StatusCode_Name(sc)));
    }
  }

  return Status::OK();
}

int64_t NameSpace::SnapshotGc(const std::function<bool(int64_t)>& should_stop,
                              bool incremental_gc) {
  auto start = std::chrono::steady_clock::now();

  std::vector<std::pair<INode, std::string>> snapshot_root_inodes;
  auto status = GetSnapshotRoots(&snapshot_root_inodes);
  CHECK(status.IsOK()) << status.ToString();

  SnapshotRootIdToGcInfo gc_info;
  GatherMarkDeletedSnapshots(snapshot_root_inodes, &gc_info, incremental_gc);
  int64_t num_deleted1 =
      DeleteDanglingSnapshottedINodes(should_stop, gc_info, incremental_gc);
  int64_t num_deleted2 = RemoveAlreadyGcSnapshots(gc_info);

  LOG(INFO) << "[Snapshot GC] Deleted snapshot inodes, num: " << num_deleted1
            << ", deleted snapshots, num: " << num_deleted2
            << ", time: " << TimeUtil::GetSecondsSince(start) << "s";

  return num_deleted1;
}

void NameSpace::GatherMarkDeletedSnapshots(
    const std::vector<std::pair<INode, std::string>>& snapshot_root_inodes,
    SnapshotRootIdToGcInfo* snapshots_gc_info,
    bool incremental_gc) {
  auto last_ckpt_txid = GetLastCkptTxId();
  snapshots_gc_info->reserve(snapshot_root_inodes.size());

  for (size_t root_idx = 0; root_idx < snapshot_root_inodes.size();
       root_idx++) {
    const auto& inode = snapshot_root_inodes[root_idx].first;
    CHECK(inode.is_snapshottable()) << inode.DebugString();

    auto& snapshot_gc_info =
        snapshots_gc_info->emplace(inode.id(), SnapshotRootGcInfo())
            .first->second;
    std::set<int64_t> valid_snapshot_txids;
    std::set<int64_t> deleted_snapshot_txids;
    TxID current_state = last_ckpt_txid;
    for (const auto& snapshot : inode.snapshots()) {
      current_state = std::max(current_state, snapshot.create_txid());
      if (snapshot.has_deleted() && snapshot.deleted()) {
        snapshot_gc_info.to_delete_snapshot_ids.insert(snapshot.snapshot_id());
        deleted_snapshot_txids.insert(snapshot.create_txid());
      } else {
        valid_snapshot_txids.insert(snapshot.create_txid());
      }
    }
    snapshot_gc_info.path = snapshot_root_inodes[root_idx].second;
    snapshot_gc_info.merge_ranges.clear();
    if (valid_snapshot_txids.empty()) {
      snapshot_gc_info.snapshot_txid_lower_bound = current_state;
      continue;
    }

    // For example, watch snapshot inodes and snapshots in txid order, result
    // is S0|A=[A1...Am]|S1|B=[B1...Bn]|S2|C=[...]|current, A/B/C are all inodes
    // set, S0 S1 and S2 are created time points of snapshots. If S1 is to be
    // deleted, then we merge A and B and merge ranges is (S0, S2). If S2 is to
    // be deleted, then we merge B and C and merge ranges is (S1, current).
    auto& merge_ranges = snapshot_gc_info.merge_ranges;
    if (!incremental_gc) {
      // select all ranges
      merge_ranges.reserve(valid_snapshot_txids.size() + 1);
      int64_t prev_txid = 0;
      for (int64_t valid_snapshot_txid : valid_snapshot_txids) {
        merge_ranges.emplace_back(prev_txid, valid_snapshot_txid);
        prev_txid = valid_snapshot_txid;
      }
      if (prev_txid != current_state) {
        merge_ranges.emplace_back(prev_txid, current_state);
      }
      continue;
    }

    // select the ranges where deleted snapshots appeared
    if (deleted_snapshot_txids.empty()) {
      continue;
    }
    std::unordered_set<int64_t> selected;
    for (int64_t delete_snapshot_txid : deleted_snapshot_txids) {
      auto iter = valid_snapshot_txids.lower_bound(delete_snapshot_txid);
      // range is (prev_iter, iter), handle corner case and deduplicate
      int64_t start_txid;
      int64_t end_txid;
      if (iter == valid_snapshot_txids.end()) {
        start_txid = *valid_snapshot_txids.rbegin();
        end_txid = current_state;
      } else {
        end_txid = *iter;
        start_txid = (iter == valid_snapshot_txids.begin() ? 0 : *(--iter));
      }

      if (!selected.count(start_txid)) {
        merge_ranges.emplace_back(start_txid, end_txid);
        selected.insert(start_txid);
      }
    }
  }  // iterate snapshot root

  if (FLAGS_v >= 2) {
    for (const auto& elem : *snapshots_gc_info) {
      std::stringstream ss;
      ss << "[Snapshot GC] root " << elem.first << " " << elem.second.path
         << ", snapshot_txid_lower_bound: "
         << elem.second.snapshot_txid_lower_bound << ", merge ranges:";
      for (const auto& range : elem.second.merge_ranges) {
        ss << absl::Substitute(" ($0, $1)", range.first, range.second);
      }
      VLOG(2) << ss.str();
    }
  }
}

int64_t NameSpace::DeleteDanglingSnapshottedINodes(
    const std::function<bool(int64_t)>& should_stop,
    SnapshotRootIdToGcInfo& snapshots_gc_info,
    bool incremental_gc) {
  struct LastEntryInMergeRange {
    std::string key;
    std::string value;
    bool prev_reserved = false;
  };

  using LastEntryInMergeRanges = std::vector<LastEntryInMergeRange>;
  using MergedInodesBySnapshot =
      std::unordered_map<INodeID, LastEntryInMergeRanges>;
  // key: snapshot root inode id
  // value: value[i] represents the key-value to be reserved for merge_ranges[i]
  // used to merge snapshot inodes with the same parent_id/name/id
  MergedInodesBySnapshot merged_inodes_by_snapshot;
  std::vector<DeleteSnapshotINodeInfo> delete_infos;
  SnapshotINodeKey prev_inode_key, cur_inode_key;

  // return -1 for not found, else return x if txid in ranges[x]
  auto find_txid_in_merge_range = [](const SnapshotMergeRanges& ranges,
                                     int64_t txid) -> int64_t {
    auto compare = [](const SnapshotMergeRange& range, int64_t txid) {
      return range.second < txid;
    };
    auto iter = std::lower_bound(ranges.begin(), ranges.end(), txid, compare);
    if (iter != ranges.end() && iter->first < txid) {
      return iter - ranges.begin();
    }
    return -1;
  };

  // decided whether to delete the last entry in each merge range.
  // For example, they are r1,r2,r3, we may delete r3 when:
  // 1. r1,r2 will be deleted.
  // 2. snapshot inode r3 is created in the merge range.
  auto process_fully_merged_inodes =
      [this, &delete_infos](const MergedInodesBySnapshot& merged_inodes,
                            const SnapshotRootIdToGcInfo& gc_info) {
        for (const auto& elem : merged_inodes) {
          const auto& merge_ranges = gc_info.at(elem.first).merge_ranges;
          const LastEntryInMergeRanges& last_entries = elem.second;

          CHECK_EQ(merge_ranges.size(), last_entries.size());
          for (size_t i = 0; i < merge_ranges.size(); i++) {
            if (last_entries[i].prev_reserved) {
              break;
            }
            if (last_entries[i].key.empty()) {
              continue;
            }
            INode inode;
            CHECK(inode.ParseFromArray(last_entries[i].value.data(),
                                       last_entries[i].value.size()));
            CHECK_GT(inode.create_txid(), inode.last_update_txid())
                << inode.DebugString();
            bool can_delete = merge_ranges[i].first < inode.create_txid() &&
                              inode.create_txid() < merge_ranges[i].second &&
                              !meta_storage_->ExistSnapshotReference(inode);
            if (!can_delete) {
              break;
            }

            delete_infos.emplace_back(
                last_entries[i].key, inode.id(), inode.last_update_txid());
            VLOG(6) << absl::Substitute(
                "[Snapshot GC] delete the last entry $0,$1,$2",
                inode.id(),
                inode.name(),
                inode.last_update_txid());
          }
        }
      };

  auto try_add_delete_info = [this, &delete_infos](
                                 std::string table_key,
                                 const SnapshotINodeKey& inode_key,
                                 const rocksdb::Slice& value,
                                 bool* can_delete_snapshot) -> bool {
    INode snapshot_inode;
    CHECK(snapshot_inode.ParseFromArray(value.data(), value.size()));
    bool to_delete = !meta_storage_->ExistSnapshotReference(snapshot_inode);
    if (to_delete) {
      delete_infos.emplace_back(
          std::move(table_key), inode_key.inode_id, inode_key.last_update_txid);
    } else if (can_delete_snapshot != nullptr) {
      *can_delete_snapshot = false;
    }
    return to_delete;
  };

  auto iterate_step = [&](SnapshotINodeKey* inode_key,
                          const rocksdb::Slice& key,
                          const rocksdb::Slice& value) -> bool {
    int64_t num_deleted = delete_infos.size();
    bool stop = num_deleted > 0 && should_stop(num_deleted);
    if (stop || num_deleted >= FLAGS_snapshot_gc_deletion_num_in_batch) {
      VLOG(2) << "[Snapshot GC] stop handling dangling-delete " << num_deleted;
      for (auto& item : snapshots_gc_info) {
        item.second.can_delete_snapshot = false;
      }
      return false;
    }

    prev_inode_key.Swap(&cur_inode_key);
    cur_inode_key.Swap(inode_key);
    if (!cur_inode_key.CoincideAtDirTree(prev_inode_key)) {
      process_fully_merged_inodes(merged_inodes_by_snapshot, snapshots_gc_info);
      merged_inodes_by_snapshot.clear();
    }

    // snapshot inode is reference(snapshot_root_id == kInvalidINodeId)
    // or corresponding snapshot root not exist for some reason.
    auto gc_info_iter = snapshots_gc_info.find(cur_inode_key.snapshot_root_id);
    if (gc_info_iter == snapshots_gc_info.end()) {
      bool to_delete =
          try_add_delete_info(key.ToString(), cur_inode_key, value, nullptr);
      VLOG(6) << "[Snapshot GC] " << cur_inode_key.ToString()
              << " under no snapshot root, to_delete: " << to_delete;
      return true;
    }

    bool* can_delete_snapshot = &gc_info_iter->second.can_delete_snapshot;
    const auto& snapshot_gc_info = gc_info_iter->second;
    const auto& merge_ranges = snapshot_gc_info.merge_ranges;

    // accelerate for the condition when all snapshots are deleted
    if (cur_inode_key.last_update_txid <
        snapshot_gc_info.snapshot_txid_lower_bound) {
      bool to_delete = try_add_delete_info(
          key.ToString(), cur_inode_key, value, can_delete_snapshot);
      VLOG(6) << "[Snapshot GC] " << cur_inode_key.ToString()
              << " earlier than " << snapshot_gc_info.snapshot_txid_lower_bound
              << ", to_delete: " << to_delete;
      return true;
    }

    if (merge_ranges.empty()) {
      return true;
    }

    // for each merge_range and inode id, reserve the last inode. The last
    // inode is not reserved if it's deleted at last.
    auto pos = find_txid_in_merge_range(
        merge_ranges, static_cast<int64_t>(cur_inode_key.last_update_txid));
    if (pos == -1) {
      return true;
    }

    auto iter = merged_inodes_by_snapshot.find(cur_inode_key.snapshot_root_id);
    if (iter == merged_inodes_by_snapshot.end()) {
      iter = merged_inodes_by_snapshot
                 .emplace(cur_inode_key.snapshot_root_id,
                          LastEntryInMergeRanges(merge_ranges.size()))
                 .first;
    }
    auto& last_entry_at_range = iter->second[pos];
    if (!last_entry_at_range.key.empty()) {
      bool to_delete = try_add_delete_info(std::move(last_entry_at_range.key),
                                           prev_inode_key,
                                           value,
                                           can_delete_snapshot);
      if (!to_delete) {
        last_entry_at_range.prev_reserved = true;
      }
      VLOG(6) << "[Snapshot GC] inode " << prev_inode_key.ToString()
              << " is not last in merge range, to_delete: " << to_delete;
    }
    last_entry_at_range.key = key.ToString();
    last_entry_at_range.value = value.ToString();

    return true;
  };

  meta_storage_->ScanSnapshotINodes(iterate_step);
  process_fully_merged_inodes(merged_inodes_by_snapshot, snapshots_gc_info);
  meta_storage_->UpdateSnapshotINodesAsync(delete_infos);

  if (FLAGS_v >= 2) {
    std::ostringstream ss;
    ss << "[Snapshot GC] delete snapshot inodes:";
    for (const auto& delete_info : delete_infos) {
      std::string name;
      auto key = StringPiece(delete_info.key);
      meta_storage_->DecodeSnapshotINodeKey(
          key, nullptr, &name, nullptr, nullptr, nullptr);
      ss << absl::Substitute(
          " $0,$1,$2", delete_info.id, name, delete_info.last_update_txid);
    }
    VLOG(2) << ss.str();
  }
  return delete_infos.size();
}

int64_t NameSpace::RemoveAlreadyGcSnapshots(
    const SnapshotRootIdToGcInfo& snapshots_gc_info) {
  int64_t num_removed_sum = 0;
  for (const auto& elem : snapshots_gc_info) {
    INodeID id = elem.first;
    const auto& gc_info = elem.second;
    const auto& path = gc_info.path;
    if (!gc_info.can_delete_snapshot) {
      continue;
    }

    StringPieces lock_components;
    if (!GetAllAncestorPaths(path, &lock_components)) {
      LOG(ERROR) << "[Snapshot GC] found corrupted cache data, invalid path: "
                 << path;
    }

    auto locks = WriteLockTree(&lock_components);
    INode inode;
    StatusCode sc = meta_storage_->GetINode(id, &inode);
    if (UNLIKELY(sc != StatusCode::kOK)) {
      LOG(INFO) << absl::Substitute(
          "[Snapshot GC] failed to get snapshot root inode, maybe user deleted "
          "it: inode_id=$0, path=$1, code=$2",
          id,
          path,
          StatusCode_Name(sc));
      continue;
    }

    RepeatedPtrField<Snapshot> new_snapshots;
    int num_removed = 0;
    for (auto& snapshot : *inode.mutable_snapshots()) {
      if (gc_info.to_delete_snapshot_ids.count(snapshot.snapshot_id())) {
        CHECK(snapshot.deleted()) << "maybe a bug " << snapshot.DebugString();
        num_removed++;
      } else {
        new_snapshots.Add()->Swap(&snapshot);
      }
    }
    inode.mutable_snapshots()->Swap(&new_snapshots);

    if (num_removed != gc_info.to_delete_snapshot_ids.size()) {
      // maybe user called disallowSnapshot during gc process.
      LOG(INFO) << "[Snapshot GC] to remove " << num_removed
                << " snapshots under " << path << ", expect to remove "
                << gc_info.to_delete_snapshot_ids.size();
    } else if (num_removed > 0) {
      LOG(INFO) << "[Snapshot GC] to remove " << num_removed
                << " snapshots under " << path;
    }
    num_removed_sum += num_removed;
    meta_storage_->UpdateINode(inode);
  }

  return num_removed_sum;
}

}  // namespace dancenn
