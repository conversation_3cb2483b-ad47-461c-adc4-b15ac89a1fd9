#include "namespace/lease_scanner_listener.h"

#include <glog/logging.h>

#include <string>
#include <chrono>

#include "base/logger_metrics.h"
#include "namespace/meta_scanner.h"
#include "namespace/meta_storage.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/namespace.h"

namespace dancenn {

std::vector<INodeID> LeaseScannerListener::ScanIndexes() {
  return {kRootINodeId};
}

bool LeaseScannerListener::Handle(const std::string& full_path,
                                  const INode& inode) {
  CheckConsistencyBetweenLeaseAndINode(inode);
  return true;
}

void LeaseScannerListener::CheckConsistencyBetweenLeaseAndINode(
    const INode& inode) {
  if (!inode.has_uc()) {
    return;
  }
  std::string lease_holder = ns_->meta_storage()->GetLeaseHolder(inode.id());
  if (lease_holder == inode.uc().client_name()) {
    return;
  }
  LOG(INFO) << "Lease holder is not the same as inode, lease holder: "
            << lease_holder << "inode: " << inode.uc().ShortDebugString();
  auto snapshot_holder = ns_->meta_storage()->GetSnapshot();
  {
    // Iterator holder scope within snapshot holder scope.
    auto snapshot = snapshot_holder->snapshot();
    auto iter_holder =
        ns_->meta_storage()->GetIterator(snapshot, kINodeDefaultCFIndex);
    INode snapshot_inode;
    {
      StatusCode code = ns_->meta_storage()->GetINode(
          inode.id(), &snapshot_inode, iter_holder->iter());
      if (code != StatusCode::kOK && code != StatusCode::kFileNotFound) {
        LOG(ERROR) << "Get INode failed, inode: " << inode.ShortDebugString();
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return;
      }
    }
    std::string snapshot_lease_holder =
        ns_->meta_storage()->GetLeaseHolder(inode.id(), snapshot);
    if (snapshot_inode.uc().client_name() != snapshot_lease_holder) {
      LOG(ERROR) << "snapshot_inode.uc().client_name() != snapshot_lease_holder"
                 << ", snapshot_inode.uc().client_name(): "
                 << snapshot_inode.uc().client_name()
                 << ", snapshot_lease_holder: " << snapshot_lease_holder
                 << ", id: " << inode.id();
      MFC(LoggerMetrics::Instance().error_)->Inc();
    } else {
      LOG(INFO) << inode.id() << " passes snapshot check";
    }
  }
}

}  // namespace dancenn

