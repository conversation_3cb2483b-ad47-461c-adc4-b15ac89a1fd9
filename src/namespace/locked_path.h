//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include <cnetpp/base/string_piece.h>

#include <map>
#include <string>
#include <vector>

#include "base/rwlock_manager.h"
#include "base/status.h"
#include "namespace/inode.h"

namespace dancenn {

using StringPiece = cnetpp::base::StringPiece;

class NameSpace;

enum class PathLockType {
  kPathLockTypeNone = 0,
  kPathLockTypeRead = 1,
  kPathLockTypeWrite = 2,
};

enum class ResolveState {
  kResolveOK = 0,
  kResolveInit = 1,
  kResolveAncestorNotFound = 2,
  kResolveAncestorNotDir = 3,
  kResolveError = 4,
};
struct RichPath {
  std::string path;
  std::vector<cnetpp::base::StringPiece> path_comps;
  std::vector<cnetpp::base::StringPiece> lock_comps;
  std::vector<INode> ancestors;
  INode attr_holder;

  RichPath(const std::string& p);
  void Clear() { ancestors.clear(); }
};

class LockedPathBase {
 public:
  LockedPathBase(NameSpace* ns, PathLockType type) : ns_(ns) {
    switch(type) {
      case PathLockType::kPathLockTypeNone:
        lock_type_ = PathLockTypeInner::kPathLockTypeNone;
        break;
      case PathLockType::kPathLockTypeRead:
        lock_type_ = PathLockTypeInner::kPathLockTypeRead;
        break;
      case PathLockType::kPathLockTypeWrite:
        lock_type_ = PathLockTypeInner::kPathLockTypeWrite;
        break;
      default:
        LOG(FATAL) << "Invalid lock type: " << static_cast<int>(type);
        break;
    }
  }

  virtual ~LockedPathBase() {
  }

 protected:
  enum class PathLockTypeInner {
    kPathLockTypeNone = 0,
    kPathLockTypeRead = 1,
    kPathLockTypeWrite = 2,
  };
  ResolveState ResolveAndLockInner(
      RichPath& p,
      const std::unique_ptr<RWLockManager::PathLockMap>& locks,
      std::function<PathLockTypeInner(const RichPath&, uint32_t lock_node_index)>
          lock_type_func,
      size_t start_index);
  // Get the inode, or upgrade to write lock if the inode is missing
  Status GetINodeOrUpgradeWriteLockOnMissing(
      RichPath& p,
      uint64_t parent_id,
      const std::string& name,
      uint32_t lock_node_index,
      std::unique_ptr<RWLockManager::LockHolder>* lock,
      INode* out_node);
  Status UpgradeToWriteLockOnNonExistDir(
      RichPath& p,
      int64_t parent_id,
      const std::string& name,
      uint32_t lock_node_index,
      std::unique_ptr<RWLockManager::LockHolder>* lock,
      INode* out_node);

  Status GetINode(uint64_t parent_id,
                  const std::string& name,
                  INode* inode,
                  INode* attr_holder);
  INode GetRootINode();

  std::unique_ptr<RWLockManager::LockHolder> Lock(PathLockTypeInner type,
                                                  const StringPiece& comp,
                                                  uint32_t depth);
  std::unique_ptr<RWLockManager::LockHolder> ReadLock(
      const StringPiece& lock_comp,
      uint32_t lock_depth);
  std::unique_ptr<RWLockManager::LockHolder> WriteLock(
      const StringPiece& lock_comp,
      uint32_t lock_depth);
    
  void set_lock_type(PathLockTypeInner type) {
    lock_type_ = type;
  }
  PathLockTypeInner lock_type() const {
    return lock_type_;
  }

 private:
  NameSpace* ns_{nullptr};
  PathLockTypeInner lock_type_;
};

class LockedPath : public LockedPathBase {
 public:
  LockedPath(PathLockType type, const std::string& path, NameSpace* ns);
  virtual ~LockedPath() {
  }

  LockedPath(const LockedPath&) = delete;
  LockedPath(LockedPath&&) = delete;
  LockedPath& operator=(const LockedPath&) = delete;
  LockedPath& operator=(LockedPath&&) = delete;

  // Resolve path and acquire lock, go from root to the last inode
  // The locks state depends on the return value, the resolve state.
  // kResolveOK:
  //   - Resolve succeeded, all inodes in the path existed.
  //   - Lock Status:
  //     - Read Lock: Read lock on all lock components
  //     - Write Lock: Read lock on all lock components except last one, Write
  //     lock on last lock component
  //  - Eg: Path: /a/b/c, Existed: /a/b/c,
  //    - ReadLock: ReadLock on [/, /a, /a/b, /a/b/c]
  //    - WriteLock: ReadLock on [/, /a, /a/b], WriteLock on [/a/b/c]
  // kResolveAncestorNotFound:
  //   - Resolve failed because one of the ancestors directory not existed.
  //   Caller should decide if it's a valid case
  //   - Lock Status:
  //     - Read Lock: Read lock on all lock components, including the first
  //     nonexisted lock component
  //     - Write Lock: Read lock on all lock components existed, Write lock on
  //     first nonexisted lock component
  //  - Eg: Path: /a/b/c, Existed: /a,
  //    - ReadLock: ReadLock on [/, /a, /a/b]
  //    - WriteLock: ReadLock on [/, /a], WriteLock on [/a/b]
  // kResolveAncestorNotDir:
  //   - Resolve failed because one of the ancestors is not directory. Caller
  //   should decide if it's a valid case
  //   - Lock Status:
  //     - Read Lock: Read lock on lock components until first invalid inode
  //     (included)
  //     - Write Lock: Read lock on lock components until first invalid inode
  //     (included)
  //  - Eg: Path: /a/b/c, Existed: /a/b is a file
  //    - ReadLock: ReadLock on [/, /a, /a/b]
  //    - WriteLock: ReadLock on [/, /a, /a/b]
  // kResolveError:
  //   - Unexpected error
  //   - Lock Status: All locks are cleared
  Status ResolveAndLock();
  ResolveState state() const {
    return state_;
  }

  // Might trigger ResolveAndLock to acquire more locks on inodes just created.
  // However resolve result cannot be determined from return value. Caller
  // should check inode and lock state before further operations.
  Status UpgradeToWriteLock();
  Status DowngradeToReadLock();

  std::string GetLastLockedNodePath();

  const RichPath& path() const {
    return path_;
  }
  RichPath& MutablePath() {
    return path_;
  }
  const std::string& GetPathStr() const {
    return path_.path;
  }
  const std::vector<StringPiece>& GetPathComps() const {
    return path_.path_comps;
  }
  const std::vector<StringPiece>& GetLockComps() const {
    return path_.lock_comps;
  }
  const std::vector<INode>& GetLockedAncestors() const {
    return path_.ancestors;
  }
  const INode& GetAttrHolder() const {
    return path_.attr_holder;
  }
  const std::unique_ptr<RWLockManager::PathLockMap>& locks() const {
    return locks_;
  }

 private:
  PathLockTypeInner CalLockType(const RichPath& p, uint32_t lock_node_index);

 private:
  RichPath path_;
  std::unique_ptr<RWLockManager::PathLockMap> locks_;

  ResolveState state_{ResolveState::kResolveInit};
};

class LockedPathVector : public LockedPathBase {
 public:
  LockedPathVector(NameSpace* ns,
                   const std::vector<std::string>& paths,
                   PathLockType type);

  Status ResolveAndLock();

  const RichPath& GetRichPath(int idx) const {
    return rich_paths_[idx];
  }

  ResolveState GetResolveState(int idx) const {
    return resolve_states_[idx];
  }

  const std::unique_ptr<RWLockManager::PathLockMap>& GetLocks(int idx) const {
    return locks_[idx];
  }

  const RichPath& GetPath(const std::string& p) {
    for (auto&& rp : rich_paths_) {
      if (rp.path == p) {
        return rp;
      }
    }
    LOG(FATAL) << "Cannot find p in LockedPath " << p;
    // Never get here.
    return rich_paths_[0];
  }

  const std::vector<INode>& GetAncestors(int idx) const {
    return rich_paths_[idx].ancestors;
  }

 private:
  // Lock result might not be align with plan!
  void InitLockPlan();

 private:
  std::vector<RichPath> rich_paths_;
  std::vector<ResolveState> resolve_states_;
  std::vector<std::unique_ptr<RWLockManager::PathLockMap>> locks_;

  std::map<StringPiece, PathLockTypeInner> lock_plan_;
};

}  // namespace dancenn
