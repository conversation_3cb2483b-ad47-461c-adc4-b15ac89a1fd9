// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "namespace/rocksdb_listener.h"

#include <glog/logging.h>

namespace dancenn {

RocksDBListener::RocksDBListener(std::shared_ptr<Metrics> metrics) {
  write_slowdown_count_ = metrics->RegisterCounter("WriteSlowDownCount");
  write_stop_count_ = metrics->RegisterCounter("WriteStopCount");
  flush_count_ = metrics->RegisterCounter("FlushCount");
  flush_time_us_ = metrics->RegisterHistogram("FlushTimeUs");
  compaction_count_ = metrics->RegisterCounter("CompactionCount");
  compaction_time_us_ = metrics->RegisterHistogram("CompactionTimeUs");
}

void RocksDBListener::OnFlushCompleted(
    rocksdb::DB* db, const rocksdb::FlushJobInfo& flush_job_info) {
  (void) db;
  std::chrono::steady_clock::time_point start;
  {
    std::unique_lock<std::mutex> guard(mu_);
    auto itr = flush_jobs_.find(flush_job_info.job_id);
    CHECK(itr != flush_jobs_.end());
    start = itr->second;
    flush_jobs_.erase(itr);
  }

  if (flush_job_info.triggered_writes_slowdown) {
    MFC(write_slowdown_count_)->Inc();
  }
  if (flush_job_info.triggered_writes_stop) {
    MFC(write_stop_count_)->Inc();
  }

  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
      (std::chrono::steady_clock::now() - start)).count();
  MFC(flush_count_)->Inc();
  MFH(flush_time_us_)->Update(duration);
}

void RocksDBListener::OnFlushBegin(
    rocksdb::DB* db, const rocksdb::FlushJobInfo& flush_job_info) {
  (void) db;
  {
    std::unique_lock<std::mutex> guard(mu_);
    flush_jobs_[flush_job_info.job_id] = std::chrono::steady_clock::now();
  }
}

void RocksDBListener::OnCompactionCompleted(
    rocksdb::DB* db, const rocksdb::CompactionJobInfo& ci) {
  (void) db;
  MFC(compaction_count_)->Inc();
  MFH(compaction_time_us_)->Update(ci.stats.elapsed_micros);
}

}  // namespace dancenn
