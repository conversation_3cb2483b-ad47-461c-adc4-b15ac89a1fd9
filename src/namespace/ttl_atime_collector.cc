#include "ttl_atime_collector.h"

#include "meta_storage.h"
#include "namespace.h"


DECLARE_uint32(ttl_atime_collector_interval_ms);

namespace dancenn {

TtlATimeCollector::TtlATimeCollector(NameSpace* ns,
                                     MetaStorage* meta_storage,
                                     const TtlATimeCollectorConfig& config)
    : running_(true),
      ns_(ns),
      meta_storage_(meta_storage),
      config_(config),
      batch_start_time_(std::chrono::steady_clock::now()) {
}

void TtlATimeCollector::Start() {
  running_ = true;
  flush_thread_ =
      std::make_unique<std::thread>(&TtlATimeCollector::FlushLoop, this);
  LOG(INFO) << "TtlATimeCollector start";
}

void TtlATimeCollector::Stop() {
  if (!running_) {
    return;
  }
  LOG(INFO) << "TtlATimeCollector stop";
  running_ = false;
  if (flush_thread_ && flush_thread_->joinable()) {
    flush_thread_->join();
    flush_thread_.reset();
  }
}

TtlATimeCollector::~TtlATimeCollector() {
  Stop();
}

void TtlATimeCollector::AddAccessRecord(INodeID inode_id, int64_t access_time) {
  std::lock_guard<std::mutex> lock(mutex_);

  if (access_time_map_.empty()) {
    batch_start_time_ = std::chrono::steady_clock::now();
  }

  auto it = access_time_map_.find(inode_id);
  if (it == access_time_map_.end()) {
    // First time seeing this inode_id
    access_time_map_[inode_id] = access_time;
  } else {
    // Update access_time if newer
    if (access_time > it->second) {
      it->second = access_time;
    }
  }

  if (ShouldFlush()) {
    Flush();
  }
}

bool TtlATimeCollector::GetAccessRecords(std::vector<TtlATimeRecord>* records) {
  std::lock_guard<std::mutex> lock(mutex_);
  if (access_time_map_.empty()) {
    return false;
  }

  for (auto it = access_time_map_.begin(); it != access_time_map_.end(); ++it) {
    records->emplace_back(it->first, it->second);
  }
  return true;
}

bool TtlATimeCollector::ShouldFlush() const {
  if (access_time_map_.size() >= config_.batch_size) {
    return true;
  }

  if (access_time_map_.empty()) {
    return false;
  }

  auto now = std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now - batch_start_time_)
                      .count();
  return duration >= config_.timeout_ms;
}

void TtlATimeCollector::Flush() {
  if (access_time_map_.empty()) {
    return;
  }

  std::vector<TtlATimeRecord> records;
  for (auto it = access_time_map_.begin(); it != access_time_map_.end(); ++it) {
    records.emplace_back(it->first, it->second);
  }

  RpcClosure* done = NewRpcCallback();
  ns_->BatchUpdateTtlATimes(records, done);

  access_time_map_.clear();
}

void TtlATimeCollector::FlushLoop() {
  while (running_) {
    {
      std::lock_guard<std::mutex> lock(mutex_);
      if (ShouldFlush()) {
        Flush();
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_ttl_atime_collector_interval_ms));
  }
}

}  // namespace dancenn