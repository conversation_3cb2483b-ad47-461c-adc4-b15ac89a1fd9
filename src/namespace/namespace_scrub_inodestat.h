#ifndef _NAMESPACE_SCRUB_INODESTAT_
#define _NAMESPACE_SCRUB_INODESTAT_

#include "base/status.h"
#include "namespace/namespace_scrub.h"

namespace dancenn {

struct INodeStat {
  uint64_t inode_id{0};
  int64_t inode_num{0};
  int64_t file_num{0};
  int64_t dir_num{0};
  int64_t block_num{0};
  int64_t data_size{0};
  int64_t txid{-1};
  int64_t timestamp_sec{0};

  INodeStat() {}
  explicit INodeStat(uint64_t id) : inode_id(id) {}
  explicit INodeStat(const cloudfs::GetINodeStatResponseProto::INodeStat& other)
      : inode_id(other.inodeid()),
        inode_num(other.inodenum()),
        file_num(other.filenum()),
        dir_num(other.dirnum()),
        block_num(other.blocknum()),
        data_size(other.datasize()),
        txid(kInvalidTxId),
        timestamp_sec(0) {
  }

  std::string ToString() const;
  std::string Serialize() const;
};

bool operator==(const INodeStat &a, const INodeStat &b);
bool operator!=(const INodeStat &a, const INodeStat &b);
void operator+=(INodeStat &a, const INodeStat &b);

class INodeStatScrubOp : public ScrubOp {
public:
  INodeStatScrubOp() {}
  ~INodeStatScrubOp() {}
  void BindINode(const INode& node) override {
    ScrubOp::BindINode(node);
    computed_stat_.inode_id = node.id();
  }

  void PreScrub(ScrubAction action) override;
  void PostScrub(ScrubAction action) override {};

  void ProcessFile(const INode& file) override;
  void ProcessDir(const INode& dir) override {};
  Status HandleDirResult(ScrubAction action) override;
  void AddSubDirResult(const ScrubOpPtr subdir_op) override;

  int64_t EstimateRemainingSeconds(int64_t finished_inodes,
                                   int64_t elapsed_ms) override;
  std::string ToString() override;
  ScrubOpPtr NewOp() override;

  INodeStat GetComputedStat();

private:
  Status CheckStat();
  Status ForceOverwriteStat();

private:
  INodeStat stat_before_scrub_;
  INodeStat computed_stat_;
};

}

#endif
