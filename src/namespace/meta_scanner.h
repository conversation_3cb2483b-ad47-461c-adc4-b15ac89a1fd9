// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef NAMESPACE_META_SCANNER_H_
#define NAMESPACE_META_SCANNER_H_

#include <glog/logging.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>

#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <string>

#include "inode.pb.h"  // NOLINT(build/include)
#include "inode.h"

namespace dancenn {

class NameSpace;

class MetaScanner {
 public:
  explicit MetaScanner(NameSpace* ns) : ns_(ns) {
  }
  ~MetaScanner() {
    Stop();
  }

  void Start();
  void Stop();

  class Listener {
   public:
    explicit Listener(const std::string& name) : name_(name) {}
    virtual ~Listener() = default;

    const std::string& name() const {
      return name_;
    }

    // return false means skip this iteration
    virtual bool PreScan() { return true; }
    // return all sub-root's inode id for traversal
    virtual std::vector<INodeID> ScanIndexes() = 0;
    // return false means iteration should be interrupted.
    virtual bool Handle(const std::string& full_path, const INode& inode) = 0;
    virtual void PostScan() {}

   protected:
    const std::string name_;
  };

  void RegisterListener(const std::shared_ptr<Listener>& listener);
  void DeregisterListener(const std::shared_ptr<Listener>& listener);

 private:
  NameSpace* ns_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::unique_ptr<cnetpp::concurrency::Task> task_;
  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(MetaScanner* scanner)
        : scanner_(scanner) {
    }

    void Stop() override;

    bool operator()(void* arg = nullptr) override;

   private:
    MetaScanner* scanner_{nullptr};
    std::mutex mutex_;
    std::condition_variable cond_;
  };

  friend class Task;

  std::mutex mutex_;
  std::map<std::string, std::shared_ptr<Listener>> listeners_;

  void Scan(Task* task);
};

}  // namespace dancenn

#endif  // NAMESPACE_META_SCANNER_H_

