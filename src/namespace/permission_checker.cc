//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
//
#include "namespace/permission_checker.h"

#include <deque>
#include <memory>
#include <set>
#include <sstream>
#include <stack>
#include <string>
#include <utility>
#include <vector>

#include "base/path_util.h"
#include "base/time_util.h"
#include "base/status.h"
#include "inode.h"
#include "namespace/user_group_info.h"
#include "namespace/meta_storage.h"

using std::make_shared;
using std::string;
using std::vector;
using cnetpp::base::StringPiece;
using cloudfs::RangerRequestListPB;
using cloudfs::RangerResponseListPB;

DECLARE_string(permission_model);
DECLARE_string(ranger_sock_path);

namespace dancenn {

namespace {

string GetClientAddress(RpcController* c) {
  if (c == nullptr) {
    return "";
  }

  // Get client ip from rpc request header
  cnetpp::base::IPAddress writer_address;
  if (!c->rpc_request_header()->has_clientaddress() ||
      !cnetpp::base::IPAddress::LiteralToNumber(
          c->rpc_request_header()->clientaddress(), &writer_address)) {
    writer_address =
        c->rpc_connection()->tcp_connection()->remote_end_point().address();
  }
  // handle IPv6
  if (writer_address.IsIPv4Mapped()) {
    // ad-hoc parse ipv4 mapped ipv6
    cnetpp::base::IPAddress real_ip;
    writer_address.GetIPv4FromIPv6(&real_ip);
    return real_ip.ToString();
  } else {
    return writer_address.ToString();
  }
}

}  // namespace

std::string FsPermissionChecker::GenStickyException(const string& inode_path,
                                                    const string& parent_path,
                                                    const INode& inode,
                                                    const INode& parent) const {
  std::ostringstream ss;
  auto inode_perm = FsPermission(inode.permission().permission());
  auto parent_perm = FsPermission(parent.permission().permission());
  ss << "Permission denied by sticky bit: user=" << user_
     << ", path=\"" << inode_path << "\""
     << ", parent=\"" << parent_path << "\"";
  return ss.str();
}

// No group of resource.
FsPermissionChecker::FsPermissionChecker(std::string original_path,
                                         const UserGroupInfo& fs_info,
                                         const UserGroupInfo& caller_ugi,
                                         MetaStorage& meta_storage,
                                         RpcController* c):  // NOLINT
  path_to_be_verified_(std::move(original_path)),
  fs_owner_(fs_info.current_user()),
  super_group_(fs_info.current_group()),
  caller_ugi_(caller_ugi),
  user_(caller_ugi.current_user()),
  group_(caller_ugi.current_group()),
  meta_storage_(meta_storage),
  event_time_(TimeUtil::GetNowEpochMs()),
  client_ip_(GetClientAddress(c)) {
  // So far, we don't maintaining a groups relations.
  // Actually, we should call caller_ugi.groups.contains(super_group).
  is_super_ = (user_ == fs_owner_) || (group_ == fs_info.current_group());

  if (FLAGS_permission_model == "ranger") {
    model_ = RANGER;

    Status s;
    auto pos = FLAGS_ranger_sock_path.find(':');
    if (pos != std::string::npos) {

      // Case tcp.
      auto host = FLAGS_ranger_sock_path.substr(0, pos);

      std::stringstream port_ss(FLAGS_ranger_sock_path.substr(pos+1));
      int port = 0;
      bool port_legal = static_cast<bool>(port_ss >> port);
      if (!port_legal) {
        s = Status(JavaExceptions::kIOException, "Bad ranger socket format. use unix/ip socket.");
      } else {
        s = addr_.ParseFromHostPort(host, port);
      }
    } else {
      s = addr_.ParseFromUnixPath(FLAGS_ranger_sock_path);
    }

    if (!s.IsOK() || !addr_.initialized()) {
      LOG(FATAL) << "Bad ranger socket format or unable to init sock."
                 << FLAGS_ranger_sock_path << s.ToString();
    }
  } else if (FLAGS_permission_model == "posix") {
    model_ = POSIX;
  } else {
    // This not supposed to happen.
    LOG(FATAL) << "NOT supported model, use posix or ranger." << FLAGS_permission_model;
  }
}

// Ref:
// HDFS permission: https://hadoop.apache.org/docs/stable/hadoop-project-dist/hadoop-hdfs/HdfsPermissionsGuide.html
// Ranger AuditLog: https://github.com/apache/ranger/blob/master/hdfs-agent/src/main/java/org/apache/ranger/authorization/hadoop/RangerHdfsAuthorizer.java#L1019
//                  processResult method.
Status FsPermissionChecker::CheckPermission(const UserGroupInfo& fs_ugi,
                                            const UserGroupInfo& caller_ugi,
                                            const std::vector<INode>& ancestors,
                                            const INode& inode,
                                            const std::vector<cnetpp::base::StringPiece>& path_components,
                                            const std::string& path,
                                            bool do_check_owner,
                                            const FsAction* const ancestor_access,
                                            const FsAction* const parent_access,
                                            const FsAction* const access,
                                            INodeHint inode_hint,
                                            const FsAction* const sub_access,
                                            bool ignore_empty_dir,
                                            bool flush_audit_at_last) const {
  if (inode_owner_.empty()) {
    inode_owner_ = inode.permission().username();
  }

  ACCESS_CHECK access_check = NONE;

  if (flush_audit_at_last) {
    if (ancestor_access) {
      access_check = ANCESTOR;
    }
    if (parent_access) {
      access_check = PARENT;
    }
    if (access) {
      access_check = FINAL;
    }
    if (sub_access) {
      access_check = SUBTREE;
    }
  }


  return CheckPermission(fs_ugi,
                         caller_ugi,
                         ancestors,
                         inode,
                         path_components,
                         path,
                         do_check_owner,
                         ancestor_access,
                         parent_access,
                         access,
                         inode_hint,
                         sub_access,
                         ignore_empty_dir,
                         model_ == RANGER,
                         access_check);
}

Status FsPermissionChecker::CheckPermission(const UserGroupInfo& fs_ugi,
                                            const UserGroupInfo& caller_ugi,
                                            const vector<INode>& ancestors,
                                            const INode& inode,
                                            const vector<StringPiece>& path_components,
                                            const string& path,
                                            bool do_check_owner,
                                            const FsAction* const ancestor_access,
                                            const FsAction* const parent_access,
                                            const FsAction* const access,
                                            INodeHint inode_hint,
                                            const FsAction* const sub_access,
                                            bool ignore_empty_dir,
                                            bool with_ranger,
                                            ACCESS_CHECK audit_check) const {
  AuthStatus auth_status;
  Status ret_status;
  do {
    {
      // 1. check parents has X permission.
      if (with_ranger) {
        // CheckRangerAccess may return not determined.
        if ((!inode.IsInitialized() && inode_hint != INodeHint::DIRECTORY)
            || (inode.IsInitialized() && inode.type() != INode_Type_kDirectory)) {
          auth_status = CheckRangerAccess(user_,
                                          JoinPathFromRoot(path_components,
                                                           path_components.size() -1),
                                          group_,
                                          FsAction::AclEntryProto_FsActionProto_EXECUTE,
                                          false);
        } else {
          auth_status = CheckRangerAccess(user_,
                                          JoinPathFromRoot(path_components),
                                          group_,
                                          FsAction::AclEntryProto_FsActionProto_EXECUTE,
                                          false);
        }
      } else {
        auth_status = NOT_DETERMINED;
      }
      if (auth_status == NOT_DETERMINED) {
        auto s = CheckTraverse(ancestors, path_components);
        auth_status = !s.HasException() ? ALLOW : DENY;
        if (s.HasException()) {
          ret_status = s;
          auth_status = DENY;
        } else {
          auth_status = ALLOW;
        }
      }
    }

    // 2. check sticky bit.
    if (auth_status == ALLOW
        && parent_access != nullptr
        && Implies(*parent_access, FsAction::AclEntryProto_FsActionProto_WRITE)
        && !ancestors.empty()
        && inode.IsInitialized()) {
      // If check sticky_bit, then ancestors last element is parent.
      auto s = CheckStickyBit(ancestors, inode, path_components);
      if (s.HasException()) {
        ret_status = s;
        auth_status = DENY;
      } else {
        auth_status = ALLOW;
      }
    }

    // 3. check ancestor access.
    if (auth_status == ALLOW
        && ancestor_access != nullptr
        && !ancestors.empty()) {
      if (with_ranger) {
        auth_status = CheckRangerAccess(user_,
                                        JoinPathFromRoot(path_components,
                                                         path_components.size() - 1),
                                        group_,
                                        *ancestor_access,
                                        audit_check == ANCESTOR);
      } else {
        auth_status = NOT_DETERMINED;
      }

      if (auth_status == NOT_DETERMINED) {
        auto s = Check(ancestors, path_components, *ancestor_access);
        if (s.HasException()) {
          ret_status = s;
          auth_status = DENY;
        } else {
          auth_status = ALLOW;
        }
      }
    }
    // 4. check parent access if needed.
    if (auth_status == ALLOW
        && parent_access != nullptr
        && !ancestors.empty()
        && ancestors.size() == path_components.size()) {
      if (with_ranger) {
        auth_status = CheckRangerAccess(user_,
                                        JoinPathFromRoot(path_components,
                                                         path_components.size() - 1),
                                        group_,
                                        *parent_access,
                                        audit_check == PARENT);
      } else {
        auth_status = NOT_DETERMINED;
      }

      if (auth_status == NOT_DETERMINED) {
        auto s = Check(ancestors, path_components, *parent_access);
        if (s.HasException()) {
          ret_status = s;
          auth_status = DENY;
        } else {
          auth_status = ALLOW;
        }
      }
    }

    // 4. check access if needed.
    if (auth_status == ALLOW
        && access != nullptr
        && inode.IsInitialized()) {
      if (with_ranger) {
        auth_status = CheckRangerAccess(user_,
                                        JoinPathFromRoot(path_components),
                                        group_,
                                        *access,
                                        audit_check == FINAL);
      } else {
        auth_status = NOT_DETERMINED;
      }

      if (auth_status == NOT_DETERMINED) {
        auto s = Check(inode, path_components, path_components.size(), *access);
        if (s.HasException()) {
          ret_status = s;
          auth_status = DENY;
        } else {
          auth_status = ALLOW;
        }
      }
    }

    // 4. check access if needed.
    if (auth_status == ALLOW
        && sub_access != nullptr) {
      if (model_ == RANGER) {
        auth_status = CheckRangerSubAccess(JoinPathFromRoot(path_components),
                                           inode,
                                           ignore_empty_dir,
                                           *sub_access,
                                           audit_check == SUBTREE,
                                           with_ranger);
      } else {
        auto s = CheckSubAccess(path_components, inode, *sub_access, ignore_empty_dir, meta_storage_);
        if (s.HasException()) {
          ret_status = s;
          auth_status = DENY;
        } else {
          auth_status = ALLOW;
        }
      }
    }
    if (auth_status == ALLOW
        && do_check_owner) {
      auto s = CheckOwner(inode, path_components);
      auth_status = !s.HasException() ? ALLOW : DENY;
    }
  } while (false);
  if (auth_status == DENY) {
    // FIXME: DENY
    std::string full_path;
    full_path = JoinPathFromRoot(path_components);

    FsAction exec = cloudfs::AclEntryProto_FsActionProto_EXECUTE;
    const FsAction* action;
    action = access;
    if (access == nullptr) {
      if (parent_access != nullptr) {
        action = parent_access;
      } else if (ancestor_access != nullptr) {
        action = ancestor_access;
      } else {
        action = &exec;
      }
    }

    if (model_ == RANGER) {
      return Status(JavaExceptions::kRangerAccessControlException,
                    ToAccessControlString(full_path,
                                          *action));
    } else {
      if (ret_status.HasException()) {
        return ret_status;
      }
      return Status(JavaExceptions::kAccessControlException,
                    ToAccessControlString(full_path,
                                          *action));
    }
  } else if (auth_status == ALLOW) {
    return Status();
  } else {
    // Copy from ranger official hdfs plugin, it run every thing again to check.
    DCHECK(with_ranger);
    return CheckPermission(fs_ugi,
                           caller_ugi,
                           ancestors,
                           inode,
                           path_components,
                           path,
                           do_check_owner,
                           ancestor_access,
                           parent_access,
                           access,
                           inode_hint,
                           sub_access,
                           ignore_empty_dir,
                           /*with_ranger*/false);
  }
  return Status();
}

Status FsPermissionChecker::CheckOwner(const INode& inode,
                                       const vector<StringPiece>& path_components) const {
  bool is_owner = inode.permission().username() == user_;
  if (!is_owner) {
    std::ostringstream ss;
    ss << "Permission denied. user=" << user_
        <<  " is not the owner of inode="
        << JoinPathFromRoot(path_components, path_components.size());
    return Status(JavaExceptions::kAccessControlException, ss.str());
  }
  return Status();
}

/*
 * Check if ancestors has X permission.
 * */
Status FsPermissionChecker::CheckTraverse(const vector<INode>& inodes,
                                          const vector<StringPiece>& path_components) const {
  // Padding mocked root node.
  RETURN_NOT_OK(CheckIsDirectory(inodes[0], "/"));
  // May not right.
  RETURN_NOT_OK(Check(inodes[0], path_components, 0, FsAction::AclEntryProto_FsActionProto_EXECUTE));

  // The reset nodes.
  for (size_t i = 1; i < inodes.size(); ++i) {
    const auto& inode = inodes[i];
    const auto& path_component = path_components[i];
    RETURN_NOT_OK(CheckIsDirectory(inode, path_component));
    RETURN_NOT_OK(Check(inodes[i], path_components, i + 1, FsAction::AclEntryProto_FsActionProto_EXECUTE));
  }
  return Status();
};

Status FsPermissionChecker::CheckIsDirectory(const INode& inode,
                                             const StringPiece& path_component) const {
  if (inode.type() != INode_Type_kDirectory) {
    return Status(JavaExceptions::kParentNotDirectoryException,
                  path_component.as_string() + " (is not a directory)");
  }
  return Status();
}

// For parent and ancestors check, the last one is always the parent/ancestors,
// whether parent exists is determined before.
Status FsPermissionChecker::Check(const vector<INode>& inodes,
                                  const vector<StringPiece>& path_components,
                                  FsAction fs_action) const {
  const auto& inode = inodes.back();
  if (!HasPermission(inode, fs_action)) {
    return Status(JavaExceptions::kAccessControlException,
                  ToAccessControlString(JoinPathFromRoot(path_components),
                                        fs_action));
  }
  return Status();
}

FsPermissionChecker::AuthStatus FsPermissionChecker::CheckRangerAccess(const string& user,
                                                                       const string& path,
                                                                       const string& group,
                                                                       const FsAction& inode_hint,
                                                                       bool flush_audit_if_allow) const {
  CHECK(model_ == RANGER);
  // 1. Takes ancestors + inode.
  // 2. Takes inodes(ancestors and self included).
  RangerRequestListPB request_list;
  auto* rangerReqPB = request_list.mutable_access_request();
  rangerReqPB->set_user(user);
  rangerReqPB->set_path(path);
  rangerReqPB->add_user_groups(group);
  rangerReqPB->set_fs_action(inode_hint);
  rangerReqPB->set_access_time(TimeUtil::GetNowEpochMs());
  rangerReqPB->set_path_to_be_verified(path_to_be_verified_);
  // FIXME(wangning.ito): we should remove these fields. BTW, the audit info may be flush by another proto..
  if (!client_ip_.empty()) {
    rangerReqPB->set_remote_ip(client_ip_);
  }
  if (!inode_owner_.empty()) {
    rangerReqPB->set_path_owner(inode_owner_);
  }

  request_list.set_flush_audit_if_allow(flush_audit_if_allow);

  bridger::RangerBridger ranger_client(addr_);
  auto resp_list = std::make_unique<RangerResponseListPB>();
  auto s = ranger_client.SendRequest(request_list, resp_list.get());
  if (!s.IsOK() || !resp_list->has_access_response()) {
    LOG(INFO) << "Unable to get result from ranger." << request_list.DebugString()
              << " status: " << s.ToString() << " resp: " << resp_list->DebugString();
    return DENY;
  }
  auto resp = resp_list->access_response();
  VLOG(4) << "Received response: " << resp_list->DebugString();
  {
    // Record audit log. Flush at last.
    const auto& access_req = request_list.access_request();

  }
  if (resp.is_access_determined()) {
    return resp.is_allowed() ? ALLOW : DENY;
  } else {
    return DENY;
  }
}

Status FsPermissionChecker::Check(const INode& inode,
                                  const vector<StringPiece>& path_components,
                                  int idx,
                                  FsAction fs_action) const {
  if (!HasPermission(inode, fs_action)) {
    return Status(JavaExceptions::kAccessControlException,
                  ToAccessControlString(JoinPathFromRoot(path_components, idx),
                                        fs_action));
  }
  return Status();
}

bool FsPermissionChecker::HasPermission(const INode& inode, FsAction access) const {
  // Use acl or posix.
  const auto& permission_status = inode.permission();
  auto fs_permission = FsPermission(permission_status.permission());
  // Posix here.
  // TODO(wangning.ito) TBD, how to switch from posix or acl or ranger.
  if (user_ == permission_status.username()) {
    return Implies(fs_permission.user_action(), access);
  }
  // TODO(wangning.ito) we have to see if group belonging.
  if (group_ == permission_status.groupname()) {
    return Implies(fs_permission.group_action(), access);
  }
  return Implies(fs_permission.other_action(), access);
}

string FsPermissionChecker::ToAccessControlString(const string& path,
                                                  FsAction access) const {
  std::ostringstream oss;
  oss << "Permission denied: "
    << "user = " << user_ << ", "
    << "access= " << ToReadableString(access) << ", "
    << "inode=\"" << path << "\"";

  return oss.str();
}

Status FsPermissionChecker::CheckStickyBit(const vector<INode>& ancestors,
                                           const INode& inode,
                                           const vector<StringPiece>& path_components) const {
  const auto& parent = ancestors.back();
  const auto& parent_perm = FsPermission(parent.permission().permission());
  if (!parent_perm.sticky_bit()) {
    return Status();
  }

  const auto& inode_perm = FsPermission(inode.permission().permission());

  if (!IsStickyBitViolated(parent, inode)) {
    return Status();
  }

  string parent_path = JoinPathFromRoot(path_components, path_components.size() - 1);
  string inode_path = JoinPathFromRoot(path_components, path_components.size());
  return Status(JavaExceptions::kAccessControlException,
                GenStickyException(inode_path, parent_path, inode, parent));
}

FsPermissionChecker::AuthStatus
  FsPermissionChecker::CheckRangerSubAccess(const std::string& pre_path,
                                            const INode& inode,
                                            bool ignore_empty_dir,
                                            const FsAction& sub_access,
                                            bool flush_audit_if_pass,
                                            bool with_ranger) const {
  AuthStatus ret_status = NOT_DETERMINED;
  std::vector<std::pair<INode, int>> directories;
  std::vector<std::string> level;

  if (inode.type() == INode_Type_kDirectory) {
    directories.emplace_back(inode, 1);
    level.emplace_back(inode.name());
  }

  while (!directories.empty()) {
    // 1. Get children.
    auto pair = std::move(directories.back());
    directories.pop_back();
    auto curr_inode = pair.first;
    auto curr_level = pair.second;

    if (curr_level > level.size()) {
      level.emplace_back(curr_inode.name());
    } else if (curr_level == level.size()) {
      level.pop_back();
      level.emplace_back(curr_inode.name());
    } else {
      while (curr_level != level.size() ) {
        level.pop_back();
      }
    }

    auto children = std::make_shared<std::vector<INode>>();
    auto has_more = false;
    meta_storage_.GetSubINodes(curr_inode.id(),
                              /*start after*/"",
                              /*limit*/-1,
                              children.get(),
                              &has_more,
                              /*iter*/nullptr);


    if (!(children->empty() && ignore_empty_dir)) {
      if (with_ranger) {
        ret_status = CheckRangerAccess(user_,
                                       pre_path + JoinPath(level),
                                       group_,
                                       sub_access,
                                       flush_audit_if_pass && children->empty());
      } else {
        ret_status = HasPermission(curr_inode, sub_access) ? ALLOW : DENY;
      }

      if (ret_status != ALLOW) {
        break;
      }
    }

    for (auto it = children->rbegin(); it != children->rend(); ++it) {
      if (it->type() == INode_Type_kDirectory) {
        directories.emplace_back(*it, curr_level + 1);
      }
    }
  }

  if (with_ranger && ret_status == NOT_DETERMINED) {
    ret_status = CheckRangerSubAccess(pre_path, inode, ignore_empty_dir, sub_access,
                                      /*ranger audit*/false,
                                      /*with ranger*/false);
  }

  return ret_status;
}

Status FsPermissionChecker::CheckSubAccess(const vector<StringPiece>& path_components,
                                           const INode& inode,
                                           FsAction sub_access,
                                           bool ignore_empty_dir,
                                           MetaStorage& meta_storage) const {

  std::vector<std::pair<INode, int>> directories;
  std::vector<std::string> level;

  if (inode.type() == INode_Type_kDirectory) {
    directories.emplace_back(inode, 1);
    level.emplace_back(inode.name());
  } else {
    LOG(FATAL) << "Passed a file to check subaccess" << inode.DebugString();
  }

  int32_t last_level = -1;
  // iterate by level. With a stack to record the fullpath.
  while (!directories.empty()) {
    // 1. Get children.
    auto pair = std::move(directories.back());
    directories.pop_back();
    auto curr_inode = pair.first;
    auto curr_level = pair.second;

    if (curr_level > level.size()) {
      level.emplace_back(curr_inode.name());
    } else if (curr_level == level.size()) {
      level.pop_back();
      level.emplace_back(curr_inode.name());
    } else {
      while ( curr_level != level.size() ) {
        level.pop_back();
      }
    }

    // 1. get children list.
    auto children = make_shared<vector<INode>>();
    bool has_more = false;
    meta_storage.GetSubINodes(curr_inode.id(),
                              /*start_after*/"",
                              /*pagination off*/ -1,
                              children.get(),
                              &has_more,
                              /*no iter*/nullptr);

    if (!(children->empty() && ignore_empty_dir)) {
      // 2. check if node has permission.
      if (!HasPermission(curr_inode, sub_access)) {
        return Status(JavaExceptions::kAccessControlException,
                      ToAccessControlString(JoinPathFromRoot(path_components) + JoinPath(level),
                                            sub_access));
      }

      // 3. check sticky bit if exists.
      uint16_t perm = curr_inode.permission().permission();
      auto perm_status = FsPermission(perm);
      if (perm_status.sticky_bit()) {
        for (const auto& child : *children) {
          if (IsStickyBitViolated(curr_inode, child) ) {
            auto path = JoinPathFromRoot(path_components) + JoinPath(level);
            auto child_path = path + "/" + child.name();
            return Status(JavaExceptions::kAccessControlException,
                          GenStickyException(child_path, path, child, curr_inode));
          }
        }
      }

      // 4. put child into stack if it's dir.
      for (auto it = children->rbegin(); it != children->rend(); ++it) {
        if (it->type() == INode_Type_kDirectory) {
          directories.emplace_back(inode, curr_level + 1);
        }
      }
    }
  }

  return Status();
}

bool FsPermissionChecker::IsStickyBitViolated(const INode& parent, const INode& inode) const {
  if (user_ == parent.permission().username()) {
    return false;
  }
  if (user_ == inode.permission().username()) {
    return false;
  }
  return true;
}

}  // namespace dancenn

