#ifndef _NAMESPACE_SCRUB_PIN_
#define _NAMESPACE_SCRUB_PIN_

#include "base/status.h"
#include "namespace/namespace_scrub.h"

namespace dancenn {

struct PinCmd {
  std::set<uint64_t> block_ids;
};

struct PinScrubStat {
  // statistics of scrub procedure
  std::atomic<int64_t> num_file{ 0 };
  std::atomic<int64_t> num_dir{ 0 };

  uint64_t timestamp_sec{ 0 };

  PinScrubStat& operator=(const PinScrubStat& other);
  PinScrubStat& operator+=(const PinScrubStat& other);
  std::string ToString() const;

  void Clear();
};

class PinScrubOp : public ScrubOp {
 public:
  PinScrubOp();
  ~PinScrubOp() {}

  void PreScrub(ScrubAction action) override {}
  void PostScrub(ScrubAction action) override {}

  void ProcessFile(const INode& file) override;
  void ProcessDir(const INode& dir) override;
  Status HandleDirResult(ScrubAction action) override;
  void AddSubDirResult(const ScrubOpPtr subdir_op) override;

  int64_t EstimateRemainingSeconds(int64_t finished_inodes,
                                   int64_t elapsed_ms) override;
  ScrubOpPtr NewOp() override;
  std::string ToString() override;

  PinScrubStat& scrub_stat() { return scrub_stat_; }

 private:
  void GenerateStat(const INode& file);

 private:
  PinScrubStat scrub_stat_;
};

}

#endif
