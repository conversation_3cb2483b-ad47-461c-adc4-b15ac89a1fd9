//
// Copyright (c) 2019 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef NAMESPACE_WRITE_BATCH_MERGE_HANDLER_H_
#define NAMESPACE_WRITE_BATCH_MERGE_HANDLER_H_

#include <memory>
#include <rocksdb/write_batch.h>
#include <rocksdb/write_batch_base.h>

namespace dancenn {
  class MetaStorage;
  namespace meta_storage {
    class WriteBatchMergeHandler : public rocksdb::WriteBatch::Handler {
     public:
      WriteBatchMergeHandler(
          MetaStorage* ms,
          std::shared_ptr<rocksdb::WriteBatchBase> merged_batch);
      virtual ~WriteBatchMergeHandler() {}

      rocksdb::Status PutCF(uint32_t cf_id, const rocksdb::Slice& key,
          const rocksdb::Slice& value) override;
      rocksdb::Status DeleteCF(uint32_t cf_id,
          const rocksdb::Slice& key) override;
      rocksdb::Status MergeCF(uint32_t column_family_id, const rocksdb::Slice& key,
                           const rocksdb::Slice& value) override;

     private:
      MetaStorage* ms_{nullptr};
      std::shared_ptr<rocksdb::WriteBatchBase> merged_batch_;
    };
  }
}

#endif
