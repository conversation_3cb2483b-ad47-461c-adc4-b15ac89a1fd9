//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <atomic>
#include <condition_variable>
#include <memory>
#include <mutex>

// Third
#include "cnetpp/concurrency/queue_base.h"
#include "cnetpp/concurrency/spin_lock.h"
#include "cnetpp/concurrency/thread_pool.h"

// Project
#include "namespace/inode.h"
#include "namespace/meta_storage.h"

namespace dancenn {

using Thread = cnetpp::concurrency::Thread;
using ThreadPool = cnetpp::concurrency::ThreadPool;
using SpinLock = cnetpp::concurrency::SpinLock;
using SpinLockGuard = cnetpp::concurrency::SpinLock::ScopeGuard;
using QueueBase = cnetpp::concurrency::QueueBase;

class MetaStorage;
class ScrubOp;
using ScrubOpPtr = std::shared_ptr<ScrubOp>;
class ScrubTask;
using ScrubTaskPtr = std::shared_ptr<ScrubTask>;
class DirScrubInfo;
using DirScrubInfoPtr = std::shared_ptr<DirScrubInfo>;
class NameSpaceScrub;

enum ScrubOpType {
  SCRUB_OPTYPE_INODE_STAT = 0,
  SCRUB_OPTYPE_LIFECYCLE_SCAN = 1,
  SCRUB_OPTYPE_PIN = 2,

  SCRUB_OPTYPE_NUM
};

enum ScrubAction {
  SCRUB_ACTION_CHECK = 0,
  SCRUB_ACTION_FORCE_OVERWRITE
};

class ScrubOp {
public:
  ScrubOp() {}
  ~ScrubOp() {}

  void Init(NameSpace* ns, std::shared_ptr<MetaStorage> ms) {
    namespace_ = ns;
    meta_storage_ = ms;
  }
  virtual void BindINode(const INode& node) {
    node_ = node;
  }

  virtual void PreScrub(ScrubAction action) = 0;
  virtual void PostScrub(ScrubAction action) = 0;

  // post-order traversal
  virtual void ProcessFile(const INode& file) = 0;
  virtual void ProcessDir(const INode& dir) = 0;
  virtual Status HandleDirResult(ScrubAction action) = 0;
  virtual void AddSubDirResult(const ScrubOpPtr subdir_op) = 0;

  virtual int64_t EstimateRemainingSeconds(int64_t finished_inodes,
                                           int64_t elapsed_ms) = 0;
  virtual std::string ToString() = 0;

  // alloc new derived op bound with DisScrubInfo
  virtual ScrubOpPtr NewOp() = 0;

 protected:
  NameSpace* namespace_{nullptr};
  std::shared_ptr<MetaStorage> meta_storage_;
  INode node_;
};

class DirScrubInfo : public std::enable_shared_from_this<DirScrubInfo> {
public:
  DirScrubInfo(const INode& node, const DirScrubInfoPtr& parent,
               ScrubTask* task, ScrubOpPtr op,
               const std::shared_ptr<MetaStorage>& storage);
  ~DirScrubInfo();

  // Return false: no more subdirs
  bool GetNextSubDir(INode* dir, std::function<bool()>interrupted);
  void PushFrontSubDir(const INode& subdir) { subdirs_.push_front(subdir); }
  void RemoveConcurrentSubDirTask(const ScrubTaskPtr& subdir_task);
  void AddConcurrentSubDirTask(const ScrubTaskPtr& subdir_task);
  bool IsFinished();
  void FinishScrub();
  void NotifySubDirTaskFinished(uint64_t subdir_id);
  void FinishSubDirTask(uint64_t subdir_id);
  void AddSubDirOp(const ScrubOpPtr subdir_op);
  size_t SubDirTasksCount();
  const INode& GetINode() { return node_; }
  ScrubOpPtr GetResult();

  static int32_t GetObjectCount();

private:
  void LoadChildren(std::function<bool()>interrupted);
  Status HandleResult(ScrubAction action);
  void CheckStat();
  void ForceOverwriteStat();

private:
  INode node_;
  DirScrubInfoPtr parent_; // Parent
  ScrubTask* task_{nullptr};  // Owner task
  std::shared_ptr<MetaStorage> meta_storage_;

  MetaStorageIterHolderPtr iter_holder_;

  SpinLock lock_;
  // Subdir scrub tasks running concurrently
  std::unordered_map<uint64_t, ScrubTaskPtr> concurrent_subdir_tasks_;

  // Load subdir in batches
  uint64_t subfile_count_{0};
  std::deque<INode> subdirs_;
  bool has_more_subdir_{true};
  std::string last_child_;

  // Used to check memory leak
  static std::atomic<int32_t> object_count_;

  // Operations and statistics
  ScrubOpPtr op_;
};

class ScrubTask : public cnetpp::concurrency::Task,
                  public std::enable_shared_from_this<ScrubTask> {
public:
  enum State : uint32_t {
    SCRUB_TASK_STATE_INIT = 0,
    SCRUB_TASK_STATE_RUNNING,
    SCRUB_TASK_STATE_FINISHED,
    SCRUB_TASK_STATE_ABORTED
  };

  enum TraverseResult { TRAVERSE_DONE = 0, TRAVERSE_ABORTED };

  ScrubTaskPtr GetPtr() { return shared_from_this(); }

  ScrubTask(const INode& node, const DirScrubInfoPtr& parent,
            NameSpaceScrub* scrub, ScrubOpPtr op);
  ~ScrubTask();

  const INode& GetINode() { return node_; }
  NameSpaceScrub* GetScrub() { return scrub_; }

  bool operator()(void* arg = nullptr) override;

  bool ShouldStop();
  void DoScrub();
  TraverseResult Traverse();
  void FinishSubDirTaskInDirScrubInfo(const DirScrubInfoPtr& dir_info,
                                      uint64_t subdir_id);

  bool IsFinished() {
    auto state = GetState();
    return SCRUB_TASK_STATE_FINISHED == state ||
           SCRUB_TASK_STATE_ABORTED == state;
  }

  State GetState() { return static_cast<State>(state_.load()); }
  ScrubOpPtr GetResult();
  int64_t GetStartTimestampMs() { return start_timestamp_ms_; }
  int64_t GetFinishTimestampMs() { return finished_timestamp_ms_; }

  static int32_t GetObjectCount();
  MetaStorageSnapPtr GetMetaSnapshot();

private:
  void SetState(State s) { state_.store(s); }

private:
  INode node_;
  DirScrubInfoPtr parent_scrub_info_;
  NameSpaceScrub* scrub_{nullptr};
  ScrubOpPtr op_;

  SpinLock state_lock_;
  std::atomic<uint32_t> state_{SCRUB_TASK_STATE_INIT};

  NameSpace* namespace_{nullptr};
  std::shared_ptr<MetaStorage> meta_storage_;

  DirScrubInfoPtr info_;

  std::stack<DirScrubInfoPtr> stack_;

  std::atomic<uint32_t> pending_child_tasks_count_{0};

  // Used to check memory leak
  static std::atomic<int32_t> object_count_;

  int64_t start_timestamp_ms_{0};
  int64_t finished_timestamp_ms_{0};
};

struct ScrubProgress {
  int64_t start_ms;
  int64_t finished_inodes;
  int64_t finished_dirs;
  int64_t estimated_time_remain_seconds;

  ScrubProgress()
      : start_ms(0), finished_inodes(0), finished_dirs(0),
        estimated_time_remain_seconds(-1) {}
  std::string ToString() const;
};

struct ScrubResult {
  int64_t start_ms;
  int64_t finished_ms;
  Status status;
  ScrubOpPtr result;

  ScrubResult() : start_ms(0), finished_ms(0), result(nullptr) {}
  std::string ToString() const;
};

class NameSpaceScrub {
public:
  enum State : uint32_t {
    SCRUB_STATE_INIT = 0,
    SCRUB_STATE_RUNNING,
    SCRUB_STATE_FINISHED,
    SCRUB_STATE_STOP,
    SCRUB_STATE_ERROR,
  };

  NameSpaceScrub(const INode& root, ScrubOpType op, ScrubAction action,
                 NameSpace* ns, const std::shared_ptr<MetaStorage>& meta_storage);

  void Start();
  // Stop by caller
  void Stop();
  // Stop on error
  void StopOnError(const std::string& msg);
  bool WaitForDone(int timeout_sec = -1);
  State GetState();
  bool ShouldStop();

  bool TrySubmitTask(const ScrubTaskPtr& task);
  void ForceSubmitTask(const ScrubTaskPtr& task);
  void FinishTask();

  ScrubAction Action() const { return action_; }

  NameSpace* GetNameSpace() const {
    return namespace_;
  }
  const std::shared_ptr<MetaStorage> GetMetaStorage() const {
    return meta_storage_;
  }
  const std::shared_ptr<ThreadPool>& GetWorkerPool() const {
    return worker_pool_;
  }

  ScrubOpPtr GetResult();
  Status GetProgress(ScrubProgress* p);
  Status GetResult(ScrubResult* r);

  void IncreaseFinishedDirCount(uint64_t subfile_count) {
    finished_inodes_num_.fetch_add(subfile_count + 1);
    finished_dirs_num_.fetch_add(1);
  }

private:
  void StartThreads(uint32_t concur);

private:
  // Scrub root INode
  INode root_;
  ScrubAction action_;
  NameSpace* namespace_{nullptr};
  std::shared_ptr<MetaStorage> meta_storage_;
  ScrubOpPtr op_;

  std::shared_ptr<ThreadPool> worker_pool_;

  SpinLock submit_lock_;
  int32_t running_tasks_count_{0};
  int32_t scrub_concurrency_{0};

  int64_t start_timestamp_ms_{0};
  ScrubTaskPtr root_task_;

  SpinLock state_lock_;
  State state_{SCRUB_STATE_INIT};
  std::string error_msg_;

  std::atomic<int64_t> finished_inodes_num_{0};
  std::atomic<int64_t> finished_dirs_num_{0};
};

class NameSpaceScrubRunner {
public:
  enum RunnerState : uint32_t {
    RUNNER_STATE_INIT = 0,
    RUNNER_STATE_RUNNING,
    RUNNER_STATE_STOPPING,
    RUNNER_STATE_STOPPED
  };

  void Start();
  void Stop();
  Status StartScrub(ScrubOpType optype, ScrubAction action, const INode& root,
                    NameSpace* ns, const std::shared_ptr<MetaStorage>& store);
  Status StopScrub(ScrubOpType optype);
  Status GetScrubProgress(ScrubOpType optype, ScrubProgress* progress);
  Status GetScrubResult(ScrubOpType optype, ScrubResult* result);

  bool IsRunning(ScrubOpType optype);

private:
  bool ScrubRunThread();

private:
  struct ScrubReq {
    ScrubOpType optype;
    ScrubAction action;
    INode root;
    NameSpace* ns{nullptr};
    std::shared_ptr<MetaStorage> store;

    ScrubReq(ScrubOpType o, ScrubAction a, const INode& r,
             NameSpace* n, const std::shared_ptr<MetaStorage>& s)
        : optype(o), action(a), root(r), ns(n), store(s) {}
  };

  struct NameSpaceScrubContext {
    std::shared_ptr<ScrubReq> scrub_req_;
    std::shared_ptr<NameSpaceScrub> scrub_;
    ScrubResult last_result_;
  };

  std::mutex lock_;
  std::condition_variable scrub_cond_;
  std::atomic<RunnerState> state_{RUNNER_STATE_INIT};
  NameSpaceScrubContext scrub_ctx_[SCRUB_OPTYPE_NUM];

  std::mutex thread_lock_;
  std::shared_ptr<Thread> thread_;
};

} // namespace dancenn
