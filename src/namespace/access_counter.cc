// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "namespace/access_counter.h"

namespace dancenn {

AccessCounter::AccessCounter(const std::string& path, size_t num_dc)
    : path_(path),
      count_(0),
      snapshots_(num_dc),
      increment_values_(num_dc) {
  for (size_t i = 0; i < num_dc; ++i) {
    snapshots_[i] = 1.0 / num_dc;
    increment_values_[i] = 0;
  }

  last_snapshot_ = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now().time_since_epoch()).count();
}

AccessCounter::AccessCounter(const std::string& path,
                             const AccessCounter& parent)
    : path_(path),
      count_(0),  // count is not inherited
      snapshots_(parent.snapshots_.size()),
      increment_values_(parent.increment_values_.size()) {
  for (size_t i = 0; i < parent.snapshots_.size(); ++i) {
    snapshots_[i] = parent.snapshots_[i].load();
  }

  for (size_t i = 0; i < parent.increment_values_.size(); ++i) {
    increment_values_[i] = 0;
  }

  last_snapshot_ = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now().time_since_epoch()).count();
}

AccessCounter::AccessCounter(
    const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot,  // NOLINT(whitespace/line_length)
    size_t num_dc)
    : path_(snapshot.path()),
      count_(snapshot.count()),
      snapshots_(num_dc),
      increment_values_(num_dc) {
  for (int i = 0; i < snapshot.dcvalue_size(); i++) {
    snapshots_[i] = snapshot.dcvalue(i);
  }
  for (size_t i = 0; i < num_dc; i++) {
    increment_values_[i] = 0;
  }

  last_snapshot_ = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now().time_since_epoch()).count();
}

void AccessCounter::Flush(double alpha) {
  size_t num_dc = snapshots_.size();
  std::vector<uint64_t> values(num_dc);
  uint64_t total = 0;
  for (size_t i = 0; i < num_dc; i++) {
    values[i] = increment_values_[i].exchange(0);
    total += values[i];
  }
  bool has_prob = total > 0;
  if (has_prob) {
    std::vector<double> prob(num_dc);
    for (size_t i = 0; i < num_dc; i++) {
      prob[i] = static_cast<double>(values[i]) / total;
    }

    // merge with snapshots
    cnetpp::concurrency::SpinLock::ScopeGuard guard(spinlock_);
    for (size_t i = 0; i < num_dc; i++) {
      snapshots_[i] = alpha * snapshots_[i] + (1.0 - alpha) * prob[i];
    }
  }
}

cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto
AccessCounter::TakeSnapshot() {
  cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto snap;
  snap.set_path(path_);
  snap.set_count(count_);
  for (size_t i = 0; i < snapshots_.size(); ++i) {
    snap.add_dcvalue(snapshots_[i]);
  }
  return snap;
}

}  // namespace dancenn

