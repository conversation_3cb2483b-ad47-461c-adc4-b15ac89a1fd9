// Copyright 2020 Mu Xiong <<EMAIL>>

#include <rocksdb/perf_context.h>
#include <rocksdb/iostats_context.h>

#include "meta_storage_metrics.h"
#include "meta_storage.h"

namespace dancenn {

// Register RocksDB Metrics
#define RRM(name, metric_ticker)                              \
  metrics->RegisterGauge(name, [ms]() -> double {             \
    if (ms && ms->rocks_stats_) {                             \
      return ms->rocks_stats_->getTickerCount(metric_ticker); \
    }                                                         \
    return 0.;                                                \
  });

#define RRM_PC(name)                                          \
  name##_ = metrics_pc_->RegisterHistogram(#name)

#define RRM_IOC(name)                                         \
  name##_ = metrics_ioc_->RegisterHistogram(#name)

MetaStorageMetrics::MetaStorageMetrics(MetaStorage* ms) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("MetaStorage");
  metrics_ = metrics;

  single_writer_merged_batch_time_ =
      metrics->RegisterHistogram("SingleWriter#step=MergedBatch");
  single_writer_write_rocksdb_time_ =
      metrics->RegisterHistogram("SingleWriter#step=WriteRocksdb");

  write_items_batch_num_ = metrics->RegisterHistogram("NumWriteItems");

  force_compact_all_time_ = metrics->RegisterCounter("ForceCompactAll");
  force_compact_deletion_time_ =
      metrics->RegisterCounter("ForceCompactDeletion");
  num_will_retrieve_deletion_ =
      metrics->RegisterCounter("NumWillRetrieveDeletion");

  get_inode_num_ = metrics->RegisterCounter("GetINodeNum");
  get_inode_time_ = metrics->RegisterHistogram("GetINodeTime");
  get_inode_scan_num_ = metrics->RegisterCounter("GetINodeScanNum");
  get_inode_scan_time_ = metrics->RegisterHistogram("GetINodeScanTime");
  get_block_info_num_ = metrics->RegisterCounter("GetBlockInfoNum");
  get_block_info_time_ = metrics->RegisterHistogram("GetBlockInfoTime");

  get_scrs_num_ = metrics->RegisterCounter("GetSCRsNum");
  get_scrs_time_ = metrics->RegisterHistogram("GetSCRsTime");
  get_scrs_seek_valid_next_time_ = metrics->RegisterHistogram("GetSCRsSeekValidNextTime");
  get_scrs_seek_invalid_next_time_ = metrics->RegisterHistogram("GetSCRsSeekInvalidNextTime");
  get_scrs_count_valid_ = metrics->RegisterHistogram("GetSCRsCountValid");
  update_scrs_num_ = metrics->RegisterCounter("UpdateSCRsNum");
  update_scrs_time_ = metrics->RegisterHistogram("UpdateSCRsTime");
  update_scrs_count_put_ = metrics->RegisterHistogram("UpdateSCRsCountPut#type=valid");
  update_scrs_count_del_ = metrics->RegisterHistogram("UpdateSCRsCountDel#type=valid");
  update_scrs_count_put_filtered_ =
      metrics->RegisterHistogram("UpdateSCRsCountPut#type=filtered");
  update_scrs_count_del_filtered_ =
      metrics->RegisterHistogram("UpdateSCRsCountDel#type=filtered");

  get_deep_snapshot_reference_num_ =
      metrics->RegisterHistogram("GetDeepSnapshotReferenceNum");

  wtask_bind_empty_callback_task_num_ =
      metrics->RegisterCounter("WriteTaskBindCallback#type=fast#callback_num=empty");
  wtask_bind_fast_single_callback_task_num_ =
      metrics->RegisterCounter("WriteTaskBindCallback#type=fast#callback_num=single");
  wtask_bind_slow_single_callback_task_num_ =
      metrics->RegisterCounter("WriteTaskBindCallback#type=slow#callback_num=single");
  wtask_bind_slow_multiple_callback_task_num_ =
      metrics->RegisterCounter("WriteTaskBindCallback#type=slow#callback_num=multiple");

  lifecycle_update_policy_num_ =
      metrics->RegisterCounter("LifecycleUpdatePolicyNum");
  lifecycle_delete_policy_num_ =
      metrics->RegisterCounter("LifecycleDeletePolicyNum");
  lifecycle_report_storage_class_num_ =
      metrics->RegisterCounter("LifecycleReportStorageClassNum");

  metrics->RegisterGauge("rocksdb.BlockCacheUsage", [ms]() -> double {
    if (ms && ms->rocks_cache_) {
      return ms->rocks_cache_->GetUsage();
    }
    return 0.;
  });
  metrics->RegisterGauge("rocksdb.BlockCacheUsageForBIP", [ms]() -> double {
    if (ms && ms->rocks_cache_for_bip_) {
      return ms->rocks_cache_for_bip_->GetUsage();
    }
    return 0.;
  });
  metrics->RegisterGauge("rocksdb.BlockCacheUsageForSCR", [ms]() -> double {
    if (ms && ms->rocks_cache_for_scr_) {
      return ms->rocks_cache_for_scr_->GetUsage();
    }
    return 0.;
  });

  RRM("rocksdb.BLOCK_CACHE_MISS", rocksdb::BLOCK_CACHE_MISS);
  RRM("rocksdb.BLOCK_CACHE_HIT", rocksdb::BLOCK_CACHE_HIT);
  RRM("rocksdb.BLOCK_CACHE_ADD", rocksdb::BLOCK_CACHE_ADD);
  RRM("rocksdb.BLOCK_CACHE_ADD_FAILURES", rocksdb::BLOCK_CACHE_ADD_FAILURES);
  RRM("rocksdb.BLOCK_CACHE_INDEX_MISS", rocksdb::BLOCK_CACHE_INDEX_MISS);
  RRM("rocksdb.BLOCK_CACHE_INDEX_HIT", rocksdb::BLOCK_CACHE_INDEX_HIT);
  RRM("rocksdb.BLOCK_CACHE_INDEX_ADD", rocksdb::BLOCK_CACHE_INDEX_ADD);
  RRM("rocksdb.BLOCK_CACHE_INDEX_BYTES_INSERT", rocksdb::BLOCK_CACHE_INDEX_BYTES_INSERT);
  RRM("rocksdb.BLOCK_CACHE_INDEX_BYTES_EVICT", rocksdb::BLOCK_CACHE_INDEX_BYTES_EVICT);
  RRM("rocksdb.BLOCK_CACHE_FILTER_MISS", rocksdb::BLOCK_CACHE_FILTER_MISS);
  RRM("rocksdb.BLOCK_CACHE_FILTER_HIT", rocksdb::BLOCK_CACHE_FILTER_HIT);
  RRM("rocksdb.BLOCK_CACHE_FILTER_ADD", rocksdb::BLOCK_CACHE_FILTER_ADD);
  RRM("rocksdb.BLOCK_CACHE_FILTER_BYTES_INSERT", rocksdb::BLOCK_CACHE_FILTER_BYTES_INSERT);
  RRM("rocksdb.BLOCK_CACHE_FILTER_BYTES_EVICT", rocksdb::BLOCK_CACHE_FILTER_BYTES_EVICT);
  RRM("rocksdb.BLOCK_CACHE_DATA_MISS", rocksdb::BLOCK_CACHE_DATA_MISS);
  RRM("rocksdb.BLOCK_CACHE_DATA_HIT", rocksdb::BLOCK_CACHE_DATA_HIT);
  RRM("rocksdb.BLOCK_CACHE_DATA_ADD", rocksdb::BLOCK_CACHE_DATA_ADD);
  RRM("rocksdb.BLOCK_CACHE_DATA_BYTES_INSERT", rocksdb::BLOCK_CACHE_DATA_BYTES_INSERT);
  RRM("rocksdb.BLOCK_CACHE_BYTES_READ", rocksdb::BLOCK_CACHE_BYTES_READ);
  RRM("rocksdb.BLOCK_CACHE_BYTES_WRITE", rocksdb::BLOCK_CACHE_BYTES_WRITE);

  RRM("rocksdb.BLOOM_FILTER_USEFUL", rocksdb::BLOOM_FILTER_USEFUL);

  RRM("rocksdb.PERSISTENT_CACHE_HIT", rocksdb::PERSISTENT_CACHE_HIT);
  RRM("rocksdb.PERSISTENT_CACHE_MISS", rocksdb::PERSISTENT_CACHE_MISS);

  RRM("rocksdb.SIM_BLOCK_CACHE_HIT", rocksdb::SIM_BLOCK_CACHE_HIT);
  RRM("rocksdb.SIM_BLOCK_CACHE_MISS", rocksdb::SIM_BLOCK_CACHE_MISS);

  RRM("rocksdb.MEMTABLE_HIT", rocksdb::MEMTABLE_HIT);
  RRM("rocksdb.MEMTABLE_MISS", rocksdb::MEMTABLE_MISS);

  RRM("rocksdb.GET_HIT_L0", rocksdb::GET_HIT_L0);
  RRM("rocksdb.GET_HIT_L1", rocksdb::GET_HIT_L1);
  RRM("rocksdb.GET_HIT_L2_AND_UP", rocksdb::GET_HIT_L2_AND_UP);

  RRM("rocksdb.COMPACTION_KEY_DROP_NEWER_ENTRY", rocksdb::COMPACTION_KEY_DROP_NEWER_ENTRY);
  RRM("rocksdb.COMPACTION_KEY_DROP_OBSOLETE", rocksdb::COMPACTION_KEY_DROP_OBSOLETE);
  RRM("rocksdb.COMPACTION_KEY_DROP_RANGE_DEL", rocksdb::COMPACTION_KEY_DROP_RANGE_DEL);
  RRM("rocksdb.COMPACTION_KEY_DROP_USER", rocksdb::COMPACTION_KEY_DROP_USER);

  RRM("rocksdb.COMPACTION_RANGE_DEL_DROP_OBSOLETE", rocksdb::COMPACTION_RANGE_DEL_DROP_OBSOLETE);

  RRM("rocksdb.NUMBER_KEYS_WRITTEN", rocksdb::NUMBER_KEYS_WRITTEN);
  RRM("rocksdb.NUMBER_KEYS_READ", rocksdb::NUMBER_KEYS_READ);
  RRM("rocksdb.NUMBER_KEYS_UPDATED", rocksdb::NUMBER_KEYS_UPDATED);
  RRM("rocksdb.BYTES_WRITTEN", rocksdb::BYTES_WRITTEN);
  RRM("rocksdb.BYTES_READ", rocksdb::BYTES_READ);
  RRM("rocksdb.NUMBER_DB_SEEK", rocksdb::NUMBER_DB_SEEK);
  RRM("rocksdb.NUMBER_DB_NEXT", rocksdb::NUMBER_DB_NEXT);
  RRM("rocksdb.NUMBER_DB_PREV", rocksdb::NUMBER_DB_PREV);
  RRM("rocksdb.NUMBER_DB_SEEK_FOUND", rocksdb::NUMBER_DB_SEEK_FOUND);
  RRM("rocksdb.NUMBER_DB_NEXT_FOUND", rocksdb::NUMBER_DB_NEXT_FOUND);
  RRM("rocksdb.NUMBER_DB_PREV_FOUND", rocksdb::NUMBER_DB_PREV_FOUND);
  RRM("rocksdb.ITER_BYTES_READ", rocksdb::ITER_BYTES_READ);
  RRM("rocksdb.NO_FILE_CLOSES", rocksdb::NO_FILE_CLOSES);
  RRM("rocksdb.NO_FILE_OPENS", rocksdb::NO_FILE_OPENS);
  RRM("rocksdb.NO_FILE_ERRORS", rocksdb::NO_FILE_ERRORS);
  RRM("rocksdb.STALL_L0_SLOWDOWN_MICROS", rocksdb::STALL_L0_SLOWDOWN_MICROS);
  RRM("rocksdb.STALL_MEMTABLE_COMPACTION_MICROS",
      rocksdb::STALL_MEMTABLE_COMPACTION_MICROS);
  RRM("rocksdb.STALL_L0_NUM_FILES_MICROS", rocksdb::STALL_L0_NUM_FILES_MICROS);
  RRM("rocksdb.STALL_MICROS", rocksdb::STALL_MICROS);
  RRM("rocksdb.DB_MUTEX_WAIT_MICROS", rocksdb::DB_MUTEX_WAIT_MICROS);
  RRM("rocksdb.RATE_LIMIT_DELAY_MILLIS", rocksdb::RATE_LIMIT_DELAY_MILLIS);
  RRM("rocksdb.NO_ITERATORS", rocksdb::NO_ITERATORS);

  RRM("rocksdb.NUMBER_MULTIGET_CALLS", rocksdb::NUMBER_MULTIGET_CALLS);
  RRM("rocksdb.NUMBER_MULTIGET_KEYS_READ", rocksdb::NUMBER_MULTIGET_KEYS_READ);
  RRM("rocksdb.NUMBER_MULTIGET_BYTES_READ", rocksdb::NUMBER_MULTIGET_BYTES_READ);

  RRM("rocksdb.NUMBER_FILTERED_DELETES", rocksdb::NUMBER_FILTERED_DELETES);
  RRM("rocksdb.NUMBER_MERGE_FAILURES", rocksdb::NUMBER_MERGE_FAILURES);

  RRM("rocksdb.BLOOM_FILTER_PREFIX_CHECKED", rocksdb::BLOOM_FILTER_PREFIX_CHECKED);
  RRM("rocksdb.BLOOM_FILTER_PREFIX_USEFUL", rocksdb::BLOOM_FILTER_PREFIX_USEFUL);

  RRM("rocksdb.NUMBER_OF_RESEEKS_IN_ITERATION", rocksdb::NUMBER_OF_RESEEKS_IN_ITERATION);

  RRM("rocksdb.GET_UPDATES_SINCE_CALLS", rocksdb::GET_UPDATES_SINCE_CALLS);
  RRM("rocksdb.BLOCK_CACHE_COMPRESSED_MISS", rocksdb::BLOCK_CACHE_COMPRESSED_MISS);
  RRM("rocksdb.BLOCK_CACHE_COMPRESSED_HIT", rocksdb::BLOCK_CACHE_COMPRESSED_HIT);
  RRM("rocksdb.BLOCK_CACHE_COMPRESSED_ADD", rocksdb::BLOCK_CACHE_COMPRESSED_ADD);
  RRM("rocksdb.BLOCK_CACHE_COMPRESSED_ADD_FAILURES", rocksdb::BLOCK_CACHE_COMPRESSED_ADD_FAILURES);
  RRM("rocksdb.WAL_FILE_SYNCED", rocksdb::WAL_FILE_SYNCED);
  RRM("rocksdb.WAL_FILE_BYTES", rocksdb::WAL_FILE_BYTES);

  RRM("rocksdb.WRITE_DONE_BY_SELF", rocksdb::WRITE_DONE_BY_SELF);
  RRM("rocksdb.WRITE_DONE_BY_OTHER", rocksdb::WRITE_DONE_BY_OTHER);
  RRM("rocksdb.WRITE_TIMEDOUT", rocksdb::WRITE_TIMEDOUT);
  RRM("rocksdb.WRITE_WITH_WAL", rocksdb::WRITE_WITH_WAL);
  RRM("rocksdb.COMPACT_READ_BYTES", rocksdb::COMPACT_READ_BYTES);
  RRM("rocksdb.COMPACT_WRITE_BYTES", rocksdb::COMPACT_WRITE_BYTES);
  RRM("rocksdb.FLUSH_WRITE_BYTES", rocksdb::FLUSH_WRITE_BYTES);

  RRM("rocksdb.NUMBER_DIRECT_LOAD_TABLE_PROPERTIES", rocksdb::NUMBER_DIRECT_LOAD_TABLE_PROPERTIES);
  RRM("rocksdb.NUMBER_SUPERVERSION_ACQUIRES", rocksdb::NUMBER_SUPERVERSION_ACQUIRES);
  RRM("rocksdb.NUMBER_SUPERVERSION_RELEASES", rocksdb::NUMBER_SUPERVERSION_RELEASES);
  RRM("rocksdb.NUMBER_SUPERVERSION_CLEANUPS", rocksdb::NUMBER_SUPERVERSION_CLEANUPS);

  RRM("rocksdb.NUMBER_BLOCK_COMPRESSED", rocksdb::NUMBER_BLOCK_COMPRESSED);
  RRM("rocksdb.NUMBER_BLOCK_DECOMPRESSED", rocksdb::NUMBER_BLOCK_DECOMPRESSED);

  RRM("rocksdb.NUMBER_BLOCK_NOT_COMPRESSED", rocksdb::NUMBER_BLOCK_NOT_COMPRESSED);
  RRM("rocksdb.MERGE_OPERATION_TOTAL_TIME", rocksdb::MERGE_OPERATION_TOTAL_TIME);
  RRM("rocksdb.FILTER_OPERATION_TOTAL_TIME", rocksdb::FILTER_OPERATION_TOTAL_TIME);

  RRM("rocksdb.ROW_CACHE_HIT", rocksdb::ROW_CACHE_HIT);
  RRM("rocksdb.ROW_CACHE_MISS", rocksdb::ROW_CACHE_MISS);

  RRM("rocksdb.READ_AMP_ESTIMATE_USEFUL_BYTES", rocksdb::READ_AMP_ESTIMATE_USEFUL_BYTES);
  RRM("rocksdb.READ_AMP_TOTAL_READ_BYTES", rocksdb::READ_AMP_TOTAL_READ_BYTES);

  RRM("rocksdb.NUMBER_RATE_LIMITER_DRAINS", rocksdb::NUMBER_RATE_LIMITER_DRAINS);

  std::function<std::string()> tag_injector = [] () -> std::string {
    std::string tags_prefix;
    if (MetricsCenter::global_dynamic_tag_injector()) {
      tags_prefix = MetricsCenter::global_dynamic_tag_injector()();
      if (!tags_prefix.empty()) {
        tags_prefix = tags_prefix + "#";
      }
    }
    int tid = cnetpp::concurrency::ThisThread::GetId();
    return tags_prefix + "tid=" + std::to_string(tid);
  };
  metrics_pc_ = center->RegisterMetrics("MetaStoragePerfContext");
  metrics_pc_->set_dynamic_tag_injector(tag_injector);

  RRM_PC(user_key_comparison_count);
  RRM_PC(block_cache_hit_count);
  RRM_PC(block_read_count);
  RRM_PC(block_read_byte);
  RRM_PC(block_read_time);
  RRM_PC(block_checksum_time);
  RRM_PC(block_decompress_time);
  RRM_PC(internal_key_skipped_count);
  RRM_PC(internal_delete_skipped_count);
  RRM_PC(internal_recent_skipped_count);
  RRM_PC(internal_merge_count);
  RRM_PC(get_snapshot_time);
  RRM_PC(get_from_memtable_time);
  RRM_PC(get_from_memtable_count);
  RRM_PC(get_post_process_time);
  RRM_PC(get_from_output_files_time);
  RRM_PC(seek_on_memtable_time);
  RRM_PC(seek_on_memtable_count);
  RRM_PC(next_on_memtable_count);
  RRM_PC(prev_on_memtable_count);
  RRM_PC(seek_child_seek_time);
  RRM_PC(seek_child_seek_count);
  RRM_PC(seek_min_heap_time);
  RRM_PC(seek_max_heap_time);
  RRM_PC(seek_internal_seek_time);
  RRM_PC(find_next_user_entry_time);
  RRM_PC(write_wal_time);
  RRM_PC(write_memtable_time);
  RRM_PC(write_delay_time);
  RRM_PC(write_pre_and_post_process_time);
  RRM_PC(db_mutex_lock_nanos);
  RRM_PC(db_condition_wait_nanos);
  RRM_PC(merge_operator_time_nanos);
  RRM_PC(read_index_block_nanos);
  RRM_PC(read_filter_block_nanos);
  RRM_PC(new_table_block_iter_nanos);
  RRM_PC(new_table_iterator_nanos);
  RRM_PC(block_seek_nanos);
  RRM_PC(find_table_nanos);
  RRM_PC(bloom_memtable_hit_count);
  RRM_PC(bloom_memtable_miss_count);
  RRM_PC(bloom_sst_hit_count);
  RRM_PC(bloom_sst_miss_count);
  RRM_PC(env_new_sequential_file_nanos);
  RRM_PC(env_new_random_access_file_nanos);
  RRM_PC(env_new_writable_file_nanos);
  RRM_PC(env_reuse_writable_file_nanos);
  RRM_PC(env_new_random_rw_file_nanos);
  RRM_PC(env_new_directory_nanos);
  RRM_PC(env_file_exists_nanos);
  RRM_PC(env_get_children_nanos);
  RRM_PC(env_get_children_file_attributes_nanos);
  RRM_PC(env_delete_file_nanos);
  RRM_PC(env_create_dir_nanos);
  RRM_PC(env_create_dir_if_missing_nanos);
  RRM_PC(env_delete_dir_nanos);
  RRM_PC(env_get_file_size_nanos);
  RRM_PC(env_get_file_modification_time_nanos);
  RRM_PC(env_rename_file_nanos);
  RRM_PC(env_link_file_nanos);
  RRM_PC(env_lock_file_nanos);
  RRM_PC(env_unlock_file_nanos);
  RRM_PC(env_new_logger_nanos);

  metrics_ioc_ = center->RegisterMetrics("MetaStorageIOStatsContext");
  metrics_ioc_->set_dynamic_tag_injector(tag_injector);

  RRM_IOC(thread_pool_id);
  RRM_IOC(bytes_written);
  RRM_IOC(bytes_read);
  RRM_IOC(open_nanos);
  RRM_IOC(allocate_nanos);
  RRM_IOC(write_nanos);
  RRM_IOC(read_nanos);
  RRM_IOC(range_sync_nanos);
  RRM_IOC(fsync_nanos);
  RRM_IOC(prepare_write_nanos);
  RRM_IOC(logger_nanos);
}

}  // namespace dancenn
