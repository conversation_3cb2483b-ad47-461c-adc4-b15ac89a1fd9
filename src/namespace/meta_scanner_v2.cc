#include "namespace/meta_scanner_v2.h"

#include <glog/logging.h>

#include "base/committer_channel_context.h"
#include "base/logger_metrics.h"

DECLARE_uint32(dfs_meta_scanner_v2_scan_worker_num);
DECLARE_uint32(dfs_meta_scanner_v2_worker_num);
DECLARE_uint32(meta_scanner_worker_max_num_pending_tasks);

DECLARE_bool(run_ut);

namespace dancenn {

void MetaScannerTaskBase::SetMetaStorage(MetaStorage* ms) {
  meta_storage_ = ms;
}

void MetaScannerTaskBase::SetMetaScanner(MetaScannerBase* ms) {
  meta_scanner_ = ms;
}

MetaScannerTaskID MetaScannerTaskBase::GetTaskID() {
  return id_;
}

void MetaScannerTaskBase::SetTaskID(MetaScannerTaskID id) {
  id_ = id;
}

bool MetaScannerTaskBase::operator()(void* arg) {
  return Run(arg);
}

bool MetaScannerTaskBase::Run(void* arg) {
  (void)arg;

  auto start = std::chrono::steady_clock::now();
  MetaStorageSnapHolderPtr snapshot_holder = meta_storage_->GetSnapshot();
  MetaStorageSnapPtr snapshot = snapshot_holder->snapshot();
  MetaStorageIterHolderPtr iter_holder =
      meta_storage_->GetIterator(snapshot, GetCfIdx());
  MetaStorageIterPtr iter = iter_holder->iter();
  if (IsStopped()) {
    return true;
  }
  if (!PreScan()) {
    ScheduleNext();
    LOG(INFO) << "MetaScannerTask PreScan failed: " << ToString();
    return true;
  }
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    if (IsStopped() || !Handle(snapshot, iter->key(), iter->value())) {
      LOG(INFO) << "MetaScannerTask Handle failed: " << ToString();
      break;
    }
  }
  PostScan();
  ScheduleNext();
  auto end = std::chrono::steady_clock::now();
  auto cost = std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
                  .count();
  LOG(INFO) << "MetaScannerTask " << ToString() << " cost: " << cost << "ms";
  return true;
}

void MetaScannerTaskBase::ScheduleNext() {
  if (GetDelayUs() > -1 && !IsStopped()) {
    if (!meta_scanner_->AddScanTask(GetTaskID())) {
      LOG(ERROR) << "MetaScannerTask ScheduleNext failed: " << ToString();
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return;
    }
    LOG(INFO) << "MetaScannerTask " << ToString()
              << " schedule next, delay: " << GetDelayUs() << "us";
  } else {
    if (!meta_scanner_->EraseScanTask(GetTaskID(), true)) {
      LOG(WARNING) << "MetaScannerTask EraseScanTask failed: " << ToString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return;
    }
    LOG(INFO) << "MetaScannerTask finished: " << ToString();
  }
}

MetaScannerV2::MetaScannerV2(MetaStorage* ms) : id_(1) {
  ms_ = ms;
  scan_worker_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("msv2", true);
  scan_worker_->set_num_threads(FLAGS_dfs_meta_scanner_v2_scan_worker_num);
  worker_ = std::make_unique<MetaScannerWorkerV1>();
}

MetaScannerV2::~MetaScannerV2() {
}

void MetaScannerV2::Start() {
  scan_worker_->Start();
  worker_->Start();
}

// TODO(zhuangsiyu): gracefully stop
void MetaScannerV2::Stop() {
  scan_worker_->Stop();
  worker_->Stop();
}

MetaScannerTaskID MetaScannerV2::AddScanTask(
    std::shared_ptr<MetaScannerTaskBase> task) {
  std::lock_guard<std::mutex> lock(mutex_);
  MetaScannerTaskID id = id_++;
  task->SetTaskID(id);
  task->SetMetaScanner(this);
  task->SetMetaStorage(ms_);
  tasks_.insert({id, task});
  if (!scan_worker_->AddDelayTask(
          std::dynamic_pointer_cast<cnetpp::concurrency::Task>(task),
          std::chrono::microseconds(task->GetDelayUs()))) {
    LOG(WARNING) << "MetaScannerV2 AddScanTask failed: " << task->ToString();
    return -1;
  }
  LOG(INFO) << "MetaScanner add task: " << task->ToString();
  return id;
}

bool MetaScannerV2::AddScanTask(MetaScannerTaskID id) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = tasks_.find(id);
  if (it == tasks_.end()) {
    LOG(INFO) << "Failed to add task, it might be erased, id " << id;
    return false;
  }
  LOG(INFO) << "MetaScanner add task: " << id;
  return scan_worker_->AddDelayTask(
      std::dynamic_pointer_cast<cnetpp::concurrency::Task>(it->second),
      std::chrono::microseconds(it->second->GetDelayUs()));
}

bool MetaScannerV2::EraseScanTask(MetaScannerTaskID id, bool force) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = tasks_.find(id);
  if (it == tasks_.end()) {
    LOG(INFO) << "Erase non exist task: " << id;
    return true;
  }
  if (!it->second->IsStopped()) {
    if (force) {
      it->second->Stop();
    } else {
      return false;
    }
  }
  LOG(INFO) << "MetaScanner erase task: " << it->second->ToString();
  tasks_.erase(it);
  return true;
}

bool MetaScannerV2::AddWorkTask(std::shared_ptr<MetaScannerWorkTask> task) {
  return worker_->AddTask(task);
}

bool MetaScannerWorkTask::operator()(void* arg) {
  return Run(arg);
}

std::unordered_map<uint64_t, std::shared_ptr<MetaScannerTaskBase>>
MetaScannerV2::TestGetAllTasks() {
  CHECK(FLAGS_run_ut);
  return tasks_;
}

MetaScannerWorkerV1::MetaScannerWorkerV1() {
  workers_ = std::make_unique<cnetpp::concurrency::ThreadPool>("mswv1", false);
  workers_->set_num_threads(FLAGS_dfs_meta_scanner_v2_worker_num);
  workers_->set_max_num_pending_tasks(
      FLAGS_meta_scanner_worker_max_num_pending_tasks);
  workers_->set_user_context(
      reinterpret_cast<void*>(new CommitterChannelContext(
          "MetaScannerWorkerV1", FLAGS_dfs_meta_scanner_v2_worker_num)));
}

void MetaScannerWorkerV1::Start() {
  workers_->Start();
}

void MetaScannerWorkerV1::Stop() {
  workers_->Stop();
}

bool MetaScannerWorkerV1::AddTask(std::shared_ptr<MetaScannerWorkTask> task) {
  return workers_->AddTask(
      std::dynamic_pointer_cast<cnetpp::concurrency::Task>(task));
}

}  // namespace dancenn
