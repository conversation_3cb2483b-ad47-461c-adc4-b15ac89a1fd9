//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef NAMESPACE_META_STORAGE_WRITE_TASK_H_
#define NAMESPACE_META_STORAGE_WRITE_TASK_H_

#include <stdint.h>
#include <functional>
#include <vector>
#include <utility>

#include <rocksdb/write_batch.h>

#include "base/closure.h"
#include "namespace/inode.h"


namespace dancenn {

struct KVVerifyEntry {
  KVVerifyEntry(int id, std::string k, std::string v)
      : cfid(id), key(k), value(v) {}
  int cfid;
  std::string key;
  std::string value;
};
using KVVerifyVec = std::vector<KVVerifyEntry>;

namespace meta_storage {

class WriteTask {
 public:
  int64_t txid() const { return txid_; }
  void set_txid(int64_t txid) { txid_ = txid; }

  void set_wb(std::unique_ptr<rocksdb::WriteBatch> batch) {
    wb_ = std::move(batch);
  }
  const std::unique_ptr<rocksdb::WriteBatch>& wb() const { return wb_; }

  void set_done(Closure* done) {
    CHECK(done_ == nullptr);
    CHECK(sub_tasks_ == nullptr);
    done_ = done;
  }

  void set_max_inode_id(INodeID inode_id) {
    max_inode_id_ = inode_id;
  }
  INodeID max_inode_id() const {
    return max_inode_id_;
  }

  void set_max_block_id(uint64_t block_id) { max_block_id_ = block_id; }
  const uint64_t max_block_id() const { return max_block_id_; }

  void set_max_generation_stamp_v2(uint64_t max_generation_stamp_v2) {
    max_generation_stamp_v2_ = max_generation_stamp_v2;
  }
  const uint64_t max_generation_stamp_v2() const {
    return max_generation_stamp_v2_;
  }

  void set_max_snapshot_id(uint64_t max_snapshot_id) {
    max_snapshot_id_ = max_snapshot_id;
  }
  const uint64_t max_snapshot_id() const {
    return max_snapshot_id_;
  }

  void set_num_inodes_delta(int64_t num_inodes_delta) {
    num_inodes_delta_ = num_inodes_delta;
  }
  const int64_t num_inodes_delta() const { return num_inodes_delta_; }

  // return -1 means no need to partition callbacks of the task
  int64_t id_for_partition_cb() const {
    return done_ ? done_->partition_id() : -1;
  }

  void build_sub_tasks(const std::vector<Closure*>& dones) {
    CHECK(done_ == nullptr);
    CHECK(sub_tasks_ == nullptr);
    if (UNLIKELY(dones.empty())) {
      return;
    }
    sub_tasks_ = std::make_unique<std::vector<WriteTask*>>();
    sub_tasks_->reserve(dones.size());
    for (Closure* done : dones) {
      auto sub_task = new WriteTask();
      sub_task->set_done(done);
      sub_tasks_->emplace_back(sub_task);
    }
  }
  size_t sub_tasks_size() const { return sub_tasks_ ? sub_tasks_->size() : 0; }
  WriteTask* release_sub_task(size_t index) {
    DCHECK(sub_tasks_ != nullptr && index < sub_tasks_->size());
    auto result = (*sub_tasks_)[index];
    (*sub_tasks_)[index] = nullptr;
    return result;
  }

  void set_verify_kvs(std::unique_ptr<KVVerifyVec> v) {
    verify_kvs_ = std::move(v);
  }
  const std::unique_ptr<KVVerifyVec>& verify_kvs() const {
    return verify_kvs_;
  }

  bool HasNext() const { return done_ != nullptr; }
  bool HasSubTask() const { return sub_tasks_size() > 0; }
  bool IsSlow() const { return done_->IsSlow(); }
  void Next() {
    CHECK(done_ != nullptr);
    done_->Run();
  }

  void SetFailed(Status st) {
    CHECK(!st.IsOK());
    if (done_) {
      done_->set_status(std::move(st));
    }
  }

  void Tick() {
    tick_ = std::chrono::steady_clock::now()
        .time_since_epoch().count();
  }

  int64_t NextTick() {
    int64_t x = std::chrono::steady_clock::now()
        .time_since_epoch().count();
    int64_t duration = x - tick_;
    tick_ = x;
    return duration / 1000;
  }

  std::string DebugString() {
    std::ostringstream oss;
    oss << "[WriteTask]";
    oss << " done=" << (done_ ? done_->DebugString() : "null");
    return oss.str();
  }

 private:
  int64_t txid_ { kInvalidTxId };
  std::unique_ptr<rocksdb::WriteBatch> wb_ { nullptr };
  Closure* done_{ nullptr };
  INodeID max_inode_id_{ kInvalidINodeId };
  uint64_t max_block_id_{ 0 };
  uint64_t max_generation_stamp_v2_{ 0 };
  uint64_t max_snapshot_id_{ 0 };
  int64_t num_inodes_delta_ { 0 };
  // The used members from sub_tasks_: done_ and tick_.
  // Like WriteTask, sub tasks are destructed by user.
  std::unique_ptr<std::vector<WriteTask*>> sub_tasks_;
  std::unique_ptr<KVVerifyVec> verify_kvs_;
  int64_t tick_;
};
}  // namespace meta_storage
}  // namespace dancenn

#endif  // NAMESPACE_META_STORAGE_WRITE_TASK_H_
