//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#pragma once

#include <memory>
#include <cstdlib>

#include "namespace/namespace_scrub_inodestat.h"

namespace dancenn {

class INodeStatDeltaCache {
public:
  INodeStatDeltaCache(MetaStorage* meta_storage);
  ~INodeStatDeltaCache();

  void GetCurrentINodeStat(INodeStat* stat);
  void Add(const INodeStat& new_delta);
  void GC(bool force);

private:
  MetaStorage* meta_storage_;
  std::unordered_map<INodeID, std::deque<INodeStat>> deltas_map_;
  uint64_t last_gc_epoch_;
};

extern std::shared_ptr<INodeStatDeltaCache> g_delta_cache;

}  // namespace dancenn
