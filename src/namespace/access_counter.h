// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef NAMESPACE_ACCESS_COUNTER_H_
#define NAMESPACE_ACCESS_COUNTER_H_

#include <cnetpp/concurrency/spin_lock.h>

#include <string>
#include <atomic>
#include <vector>
#include <chrono>

#include "fsimage.pb.h"  // NOLINT(build/include)

namespace dancenn {

class AccessCounter {
 public:
  AccessCounter(const std::string& path, size_t num_dc);
  AccessCounter(const std::string& path, const AccessCounter& parent);
  AccessCounter(
      const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot,  // NOLINT(whitespace/line_length)
      size_t num_dc);

  void Flush(double alpha);

  cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto TakeSnapshot();  // NOLINT(whitespace/line_length)

  // for unit test
  void set(int i, double v) {
    snapshots_[i] = v;
  }

  // for unit test
  void ClearLastSnapshot() {
    last_snapshot_ = 0;
  }

  std::string path_;
  std::atomic<uint64_t> count_;

  cnetpp::concurrency::SpinLock spinlock_;
  std::vector<std::atomic<double>> snapshots_;  // one per dc

  std::vector<std::atomic<uint64_t>> increment_values_;  // one per dc

  std::atomic<uint64_t> last_snapshot_;
};

}  // namespace dancenn

#endif  // NAMESPACE_ACCESS_COUNTER_H_

