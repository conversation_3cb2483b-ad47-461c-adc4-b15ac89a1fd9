// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef NAMESPACE_NAMESPACE_METRICS_H_
#define NAMESPACE_NAMESPACE_METRICS_H_

#include <memory>
#include <unordered_set>

#include "base/metric.h"
#include "base/metrics.h"
#include "base/refresher.h"
#include "inode.pb.h"

namespace dancenn {

class NameSpace;

struct NameSpaceMetrics {
  NameSpaceMetrics();
  explicit NameSpaceMetrics(NameSpace* ns);

  ~NameSpaceMetrics() = default;
  NameSpaceMetrics(const NameSpaceMetrics&) = delete;
  NameSpaceMetrics& operator=(const NameSpaceMetrics&) = delete;

  std::shared_ptr<Gauge> startup_ts_in_ms_;

  // Replica
  MetricID add_block_replication_0_cnt_;
  MetricID add_block_replication_1_cnt_;
  MetricID add_block_replication_2_cnt_;
  MetricID add_block_replication_3_cnt_;
  MetricID add_block_replication_more_cnt_;
  inline const MetricID& MetricAddBlockReplication(const uint32_t replication) {
    switch (replication) {
      case 0:
        return add_block_replication_0_cnt_;
      case 1:
        return add_block_replication_1_cnt_;
      case 2:
        return add_block_replication_2_cnt_;
      case 3:
        return add_block_replication_3_cnt_;
      default:
        return add_block_replication_more_cnt_;
    }
    return add_block_replication_more_cnt_;
  }

  void RecordLiveTime(const INode& inode);

  // StandbyRead
  MetricID standby_read_stale_read_;
  MetricID standby_read_one_click_;
  MetricID standby_read_need_wait_;
  MetricID standby_read_txid_gap_;
  MetricID standby_read_catchup_cnt_;
  MetricID standby_read_catchup_wait_time_;
  MetricID standby_read_catchup_applied_txid_;
  MetricID standby_read_catchup_applied_rate_;
  MetricID standby_read_failed_cnt_;
  MetricID standby_read_failed_wait_time_;
  MetricID standby_read_failed_applied_txid_;
  MetricID standby_read_failed_applied_rate_;
  MetricID standby_read_failed_remain_txid_gap_;
  MetricID standby_read_failed_remain_progress_;

  // Standby generate retry cache
  MetricID standby_retry_cache_add_entry_count_;

  // Refuse Read
  MetricID refuse_read_blacklist_dc_cnt_;
  MetricID refuse_read_cross_dc_cnt_;

  // Recycle Bin
  MetricID recycle_bin_move_to_cnt_;
  MetricID recycle_bin_move_success_cnt_;
  MetricID recycle_bin_move_failed_cnt_;
  MetricID recycle_bin_not_dir_;
  MetricID recycle_bin_create_dir_;
  MetricID recycle_bin_cleanup_;

  MetricID recycle_bin_add_lock_time_;
  MetricID recycle_bin_check_dir_time_;
  MetricID recycle_bin_create_dir_time_;
  MetricID recycle_bin_setxattr_time_;
  MetricID recycle_bin_move_to_time_;
  MetricID recycle_bin_retention_sec_;
  MetricID recycle_bin_over_retention_sec_;

  // MountPoint
  MetricID mount_point_path_ancestor_mismatched_cnt_;
  MetricID mount_point_perm_check_failed_cnt_;

  // GetListing latency metrics per step
  MetricID get_listing_ha_check_time_;
  MetricID get_listing_lock_acquire_time_;
  MetricID get_listing_get_last_inode_time_;
  MetricID get_listing_get_sub_inodes_time_;
  MetricID get_listing_fill_in_response_time_;

  // GetFileInfo
  MetricID construct_file_status_;

  // GetBlockLocations
  MetricID get_block_locations_ha_and_safemode_check_time_;
  MetricID get_block_locations_lock_acquire_time_;
  MetricID get_block_locations_get_last_inode_time_;
  MetricID get_block_locations_get_policy_time_;
  MetricID get_block_infer_read_mode_time_;
  MetricID get_block_locations_create_located_blocks_time_;
  MetricID create_located_blocks_get_detail_blocks;
  MetricID create_located_blocks_get_blocks_in_range_;
  MetricID create_located_blocks_fillin_time_;
  MetricID create_located_blocks_compute_file_size_;
  MetricID create_located_blocks_sort_blocks_;
  MetricID compute_file_size_;
  MetricID num_blocks_accessed_for_locs_;

  // GetBlockLocations
  MetricID get_hyper_block_locations_ha_and_safemode_check_time_;
  MetricID get_hyper_block_locations_lock_acquire_time_;
  MetricID get_hyper_block_locations_get_last_inode_time_;
  MetricID scan_hyper_block_time_;
  MetricID create_hyper_block_located_time_;

  // Release lease
  MetricID release_lease_time_need_lock_;
  MetricID release_lease_time_need_not_lock_;
  MetricID release_lease_check_lease_time_;
  MetricID release_lease_get_blocks_time_;
  MetricID release_lease_bm_need_release_time_;
  MetricID release_lease_reassign_lease_time_;
  MetricID release_lease_get_gs_time_;
  MetricID release_lease_bm_init_recover_;

  // Reassign lease
  MetricID reassign_lease_lm_reassign_lease_time_;
  MetricID reassign_lease_write_editlog_time_;
  MetricID reassign_lease_write_meta_storage_time_;
  MetricID reassign_lease_wait_editlog_and_ms_time_;

  // CreateFile latency metrics per step
  MetricID create_ha_and_safemode_check_time_;
  MetricID create_lock_acquire_time_;
  MetricID create_get_parent_time_;
  MetricID create_get_inode_time_;
  MetricID create_remove_or_recover_lease_time_;
  MetricID create_add_lease_time_;
  MetricID create_storage_policy_time_;
  MetricID create_replica_policy_time_;
  MetricID create_make_inode_time_;
  MetricID create_add_block_time_;
  MetricID create_log_edit_time_;
  MetricID create_construct_file_status_time_;
  MetricID create_callback_remove_blocks_in_bm_time_;
  MetricID create_callback_add_remove_lease_time_;
  MetricID create_callback_commit_bip_time_;
  MetricID create_with_add_block_time_;
  MetricID create_callback_construct_file_status_time_;

  MetricID add_block_ha_and_safemode_check_time_;
  MetricID add_block_lock_acquire_time_;
  MetricID add_block_get_last_inode_time_;
  MetricID add_block_check_lease_time_;
  MetricID add_block_analyze_file_blocks_time_;
  MetricID acc_block_acc_check_time_;
  MetricID add_block_ccp_last_block_time_;
  MetricID add_block_get_replica_policy_time_;
  MetricID add_block_infer_write_mode_time_;
  MetricID add_block_choose_target_4_new_time_;
  MetricID add_block_gen_block_id_and_genstamp_time_;
  MetricID add_block_add_block_to_bm_time_;
  MetricID add_block_fill_in_located_block_time_;
  MetricID add_block_log_edit_time_;
  MetricID add_block_write_meta_storage_time_;

  MetricID get_content_summary_lock_acquire_time_;
  MetricID get_content_summary_compute_time_;
  MetricID get_content_summary_get_children_time_;

  MetricID complete_ha_and_safemode_check_time_;
  MetricID complete_get_last_inode_time_;
  MetricID complete_check_lease_time_;
  MetricID complete_check_file_progress_v1_time_;
  MetricID complete_commit_or_complete_last_block_time_;
  MetricID complete_check_file_progress_v2_time_;
  MetricID complete_finalize_file_time_;
  MetricID complete_callback_time_;

  MetricID concat_input_src_count_;
  MetricID concat_ha_and_safemode_check_time_;
  MetricID concat_add_lock_time_;
  MetricID concat_get_target_inode_time_;
  MetricID concat_get_src_inodes_time_;
  MetricID concat_check_upload_policy_time_;
  MetricID concat_check_file_state_time_;
  MetricID concat_check_ufs_state_time_;
  MetricID concat_merge_blocks_time_;
  MetricID concat_commit_block_time_;
  MetricID concat_edit_log_time_;
  MetricID concat_write_db_time_;
  MetricID concat_callback_time_;

  MetricID get_listing_inode_count_;

  std::shared_ptr<Gauge> since_last_catchup_txid_;
  std::shared_ptr<Gauge> last_ckpt_txid_;
  std::shared_ptr<Gauge> last_inode_id_;
  std::shared_ptr<Gauge> last_block_id_;
  std::shared_ptr<Gauge> genstampv2_;
  std::shared_ptr<Gauge> num_inodes_;
  std::shared_ptr<Gauge> editlog_apply_mode_;
  MetricID edit_assigner_switch_apply_mode_time_;

  MetricID edit_tailer_num_ops_;
  MetricID edit_tailer_duration_;
  MetricID edit_tailer_deserialize_time_;
  MetricID edit_tailer_submit_time_;
  std::unordered_map<std::string, MetricID> edit_apply_num_ops_;
  std::unordered_map<std::string, MetricID> edit_apply_duration_;
  std::unordered_map<std::string, MetricID> edit_assigner_assign_time_;
  std::unordered_map<std::string, MetricID> edit_applyer_exec_time_;
  std::unordered_map<std::string, MetricID> edit_callback_exec_time_;
  MetricID edit_rename_overwrite_callback_exec_time_;
  MetricID edit_openfile_overwrite_callback_exec_time_;

  MetricID op_add_lock_acquire_time_;
  MetricID op_add_get_last_inode_time_;

  MetricID op_rename_old_lock_acquire_time_;
  MetricID op_rename_old_time_;
  MetricID op_rename_old_del_cache_time_;

  // Rename HyperFile
  MetricID op_rename_hyper_lock_acquire_time_;
  MetricID op_rename_hyper_time_;
  MetricID op_rename_hyper_file_del_cache_time_;

  MetricID op_delete_lock_acquire_time_;
  MetricID op_delete_get_last_inode_time_;
  MetricID op_delete_del_cache_time_;

  MetricID op_mkdir_lock_acquire_time_;
  MetricID op_mkdir_get_parent_time_;

  MetricID op_set_replication_lock_acquire_time_;
  MetricID op_set_replication_get_last_inode_time_;

  MetricID op_set_permissions_lock_acquire_time_;
  MetricID op_set_permissions_get_last_inode_time_;

  MetricID op_set_owner_lock_acquire_time_;
  MetricID op_set_owner_get_last_inode_time_;

  MetricID op_times_lock_acquire_time_;
  MetricID op_times_get_last_inode_time_;

  MetricID op_close_lock_acquire_time_;
  MetricID op_close_get_last_inode_time_;

  MetricID op_rename_lock_acquire_time_;
  MetricID op_rename_time_;
  MetricID op_rename_del_cache_time_;

  MetricID op_symlink_lock_acquire_time_;
  MetricID op_symlink_get_parent_time_;

  MetricID op_reassign_lease_lock_acquire_time_;
  MetricID op_reassign_lease_get_last_inode_time_;

  MetricID op_merge_block_lock_acquire_time_;
  MetricID op_merge_block_get_last_inode_time_;

  MetricID op_concat_lock_acquire_time_;
  MetricID op_concat_get_last_inode_time_;

  MetricID op_update_blocks_lock_acquire_time_;
  MetricID op_update_blocks_get_last_inode_time_;

  MetricID op_add_block_lock_acquire_time_;
  MetricID op_add_block_get_last_inode_time_;

  MetricID op_set_xattr_lock_acquire_time_;
  MetricID op_set_xattr_get_last_inode_time_;

  MetricID op_remove_xattr_lock_acquire_time_;
  MetricID op_remove_xattr_get_last_inode_time_;

  MetricID op_set_storage_policy_lock_acquire_time_;
  MetricID op_set_storage_policy_get_last_inode_time_;

  MetricID op_set_dir_replica_policy_lock_acquire_time_;
  MetricID op_set_dir_replica_policy_get_last_inode_time_;

  MetricID op_set_replica_policy_lock_acquire_time_;
  MetricID op_set_replica_policy_get_last_inode_time_;

  MetricID op_set_lifecycle_policy_lock_acquire_time_;
  MetricID op_set_lifecycle_policy_get_last_inode_time_;

  MetricID op_unset_lifecycle_policy_lock_acquire_time_;
  MetricID op_unset_lifecycle_policy_get_last_inode_time_;

  // Acc namespace
  MetricID acc_sync_ufs_compute_action_none_both_not_exist_;
  MetricID acc_sync_ufs_compute_action_none_file_not_upload_;
  MetricID acc_sync_ufs_compute_action_none_dir_not_exist_;
  MetricID acc_sync_ufs_compute_action_none_dir_file_only_;
  MetricID acc_sync_ufs_compute_action_create_exist_in_ufs_;
  MetricID acc_sync_ufs_compute_action_delete_file_not_exist_;
  MetricID acc_sync_ufs_compute_action_delete_dir_not_exist_;
  MetricID acc_sync_ufs_compute_action_overwrite_dir_ufs_file_;
  MetricID acc_sync_ufs_compute_action_overwrite_file_ufs_dir_;
  MetricID acc_sync_ufs_compute_action_overwrite_file_etag_;
  MetricID acc_sync_ufs_compute_action_overwrite_file_mtime_;
  MetricID acc_sync_ufs_compute_action_update_dir_remote_dir_;
  MetricID acc_sync_ufs_compute_action_update_dir_remote_both_;
  MetricID acc_sync_ufs_compute_action_update_file_etag_;
  MetricID acc_sync_ufs_compute_action_update_file_mtime_;

  MetricID acc_sync_ufs_apply_action_none_;
  MetricID acc_sync_ufs_apply_action_create_;
  MetricID acc_sync_ufs_apply_action_delete_;
  MetricID acc_sync_ufs_apply_action_overwrite_;

  MetricID acc_sync_ufs_apply_action_update_time_;
  MetricID acc_prepare_sync_ufs_file_time_;
  MetricID acc_sync_ufs_file_get_ufs_file_time_;
  MetricID acc_sync_ufs_file_compute_action_time_;
  MetricID acc_sync_ufs_file_create_time_;
  MetricID acc_sync_ufs_file_delete_time_;
  MetricID acc_sync_ufs_file_overwrite_time_;
  MetricID acc_sync_ufs_file_update_time_;
  MetricID acc_sync_ufs_file_listing_time_;
  MetricID acc_sync_ufs_file_only_get_ufs_file_time_;
  MetricID acc_sync_ufs_file_only_compute_action_time_;
  MetricID acc_sync_ufs_file_only_apply_action_time_;

  MetricID acc_trigger_parent_prefetch_;
  MetricID acc_list_ufs_dir_mark_prefetch_failed_;
  MetricID acc_list_ufs_dir_mark_prefetch_failed_time_;
  MetricID acc_list_ufs_dir_lock_and_check_inode_time_;
  MetricID acc_prepare_ufs_file_listing_time_;
  MetricID acc_do_sync_ufs_dir_time_;
  MetricID acc_generate_sync_action_for_ufs_files_time_;
  MetricID acc_process_ufs_dir_children_time_;
  MetricID acc_finish_sync_ufs_dir_children_time_;
  MetricID acc_apply_ufs_dir_sync_action_time_;
  MetricID acc_apply_ufs_dir_sync_action_create_time_;
  MetricID acc_apply_ufs_dir_sync_action_create_one_time_;
  MetricID acc_apply_ufs_dir_sync_action_check_time_;
  MetricID acc_apply_ufs_dir_sync_action_update_time_;
  MetricID acc_apply_ufs_dir_sync_action_update_one_time_;
  MetricID acc_make_inode_and_blocks_from_ufs_time_;
  MetricID acc_sync_rename_ufs_time_;
  MetricID acc_prepare_rename_ufs_time_;
  MetricID acc_rename_ufs_check_time_;
  MetricID acc_rename_ufs_remove_dst_time_;
  MetricID acc_rename_ufs_copy_ufs_file_time_;
  MetricID acc_rename_ufs_check_local_result_time_;
  MetricID acc_rename_ufs_delete_ufs_file_time_;
  MetricID acc_rename_ufs_remote_time_;
  MetricID acc_rename_ufs_local_time_;
  MetricID acc_sync_delete_ufs_time_;
  MetricID acc_prepare_delete_from_ufs_time_;
  MetricID acc_delete_ufs_delete_ufs_file_time_;
  MetricID acc_delete_ufs_delete_local_file_time_;
  MetricID acc_mkdirs_time_;
  MetricID acc_mkdirs_local_time_;
  MetricID acc_mkdirs_ufs_time_;
  MetricID acc_mkdirs_update_local_time_;
  MetricID acc_create_time_;
  MetricID acc_create_local_time_;
  MetricID acc_create_ufs_time_;

  MetricID op_acc_sync_batch_add_time_;
  MetricID op_acc_sync_batch_add_lock_acquire_time_;
  MetricID op_acc_sync_batch_add_get_last_inode_time_;

  MetricID op_acc_sync_batch_update_time_;
  MetricID op_acc_sync_batch_update_lock_acquire_time_;
  MetricID op_acc_sync_batch_update_get_last_inode_time_;

  MetricID op_acc_sync_update_inode_time_;
  MetricID op_acc_sync_update_inode_lock_acquire_time_;
  MetricID op_acc_sync_update_inode_get_last_inode_time_;

  MetricID op_acc_sync_add_file_time_;
  MetricID op_acc_sync_add_file_lock_acquire_time_;
  MetricID op_acc_sync_add_file_get_last_inode_time_;

  MetricID op_acc_persist_file_time_;
  MetricID op_acc_persist_file_lock_acquire_time_;
  MetricID op_acc_persist_file_get_last_inode_time_;

  MetricID op_acc_update_block_info_time_;
  MetricID op_acc_update_block_info_lock_acquire_time_;
  MetricID op_acc_update_block_info_get_last_inode_time_;

  MetricID op_pin_lock_acquire_time_;
  MetricID op_pin_get_last_inode_time_;

  MetricID op_reconcile_inode_attrs_lock_acquire_time_;
  MetricID op_reconcile_inode_attrs_get_last_inode_time_;

  MetricID op_batch_create_file_lock_acquire_time_;
  MetricID op_batch_create_file_get_inodes_time_;
  MetricID op_batch_create_file_del_cache_time_;

  MetricID op_batch_complete_file_lock_acquire_time_;
  MetricID op_batch_complete_file_get_inodes_time_;
  MetricID op_batch_complete_file_del_cache_time_;

  MetricID op_batch_delete_file_lock_acquire_time_;
  MetricID op_batch_delete_file_get_inodes_time_;
  MetricID op_batch_delete_file_del_cache_time_;

  // FillInLocatedBlock
  MetricID fill_in_located_block_set_block_info_time_;
  MetricID fill_in_located_block_construct_block_token_time_;
  MetricID fill_in_located_block_set_block_pufs_name_time_;

  // acc
  MetricID file_not_found_after_sync_cnt_;

  MetricID sync_file_from_ufs_gap_time_;
  MetricID sync_file_from_ufs_slow_cnt_;

  MetricID upload_file_to_ufs_gap_time_;
  MetricID upload_file_to_ufs_cnt_;
  MetricID upload_file_to_ufs_slow_cnt_;
  MetricID upload_file_to_ufs_slow_unfinished_cnt_;

  MetricID trigger_file_upload_cnt_;
  MetricID trigger_block_upload_cnt_;

  // CreateFile latency metrics per step
  MetricID batch_create_precheck_time_;
  MetricID batch_create_ha_and_safemode_check_time_;
  MetricID batch_create_lock_acquire_time_;
  MetricID batch_create_get_parent_time_;
  MetricID batch_create_storage_policy_time_;
  MetricID batch_create_make_all_inode_time_;
  MetricID batch_create_log_edit_time_;
  MetricID batch_create_write_db_time_;
  MetricID batch_create_construct_file_status_time_;
  MetricID batch_create_callback_time_;

  MetricID batch_create_per_delete_inode_time_;
  MetricID batch_create_per_add_lease_time_;
  MetricID batch_create_per_replica_policy_time_;
  MetricID batch_create_per_add_block_time_;
  MetricID batch_create_per_choose_target_4_new_time_;
  MetricID batch_create_per_make_inode_time_;

  // Batch Complete latency metrics per step
  MetricID batch_complete_precheck_;
  MetricID batch_complete_ha_and_safemode_check_time_;
  MetricID batch_complete_lock_acquire_time_;
  MetricID batch_complete_get_inodes_time_;
  MetricID batch_complete_check_inode_id_time_;
  MetricID batch_complete_check_lease_time_;
  MetricID batch_complete_finalize_single_file_time_;
  MetricID batch_complete_concat_file_time_;
  MetricID batch_complete_log_edit_time_;
  MetricID batch_complete_write_db_time_;
  MetricID batch_complete_callback_time_;

  // Batch Delete latency metrics per step
  MetricID batch_delete_precheck_;
  MetricID batch_delete_ha_and_safemode_check_time_;
  MetricID batch_delete_lock_acquire_time_;
  MetricID batch_delete_get_inodes_time_;
  MetricID batch_delete_log_edit_time_;
  MetricID batch_delete_write_db_time_;
  MetricID batch_delete_callback_time_;

  // Batch Get latency metrics per step
  MetricID batch_get_precheck_;
  MetricID batch_get_ha_and_safemode_check_time_;
  MetricID batch_get_lock_acquire_time_;
  MetricID batch_get_get_inodes_time_;
  MetricID batch_get_construct_files_time_;

  MetricID delete_file_by_dn_evict_cnt_;
  MetricID delete_file_by_dn_get_block_info_time_;
  MetricID delete_file_by_dn_ha_safemode_time_;
  MetricID delete_file_by_dn_get_inode_path_time_;
  MetricID delete_file_by_dn_edit_log_time_;
  MetricID delete_file_by_dn_write_db_time_;
  MetricID delete_file_by_dn_callback_time_;

  MetricID file_live_time_;

  // BGDeletion
  MetricID bg_deletion_remove_inode_num_;
  MetricID bg_deletion_remove_block_num_;
};

}  // namespace dancenn

#endif  // NAMESPACE_NAMESPACE_METRICS_H_
