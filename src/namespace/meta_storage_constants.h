#ifndef _META_STORAGE_CONSTANTS_H_
#define _META_STORAGE_CONSTANTS_H_

#include <cstdint>

namespace dancenn {

extern const char* kINodeDefaultCFName;
extern const uint32_t kINodeDefaultCFIndex;
extern const char* kINodePendingDeleteCFName;
extern const uint32_t kINodePendingDeleteCFIndex;
extern const char* kNameSystemInfoCFName;
extern const uint32_t kNameSystemInfoCFIndex;
extern const char* kAccessCounterCFName;
extern const uint32_t kAccessCounterCFIndex;
extern const char* kINodeIndexCFName;
extern const uint32_t kINodeIndexCFIndex;
extern const char* kLegacyBlockPufsInfoCFName;
extern const uint32_t kLegacyBlockPufsInfoCFIndex;
extern const char* kLegacyDeprecatedBlockPufsInfoCFName;
extern const uint32_t kLegacyDeprecatedBlockPufsInfoCFIndex;
extern const char* kLegacyBlockInfoProtoCFName;
extern const uint32_t kLegacyBlockInfoProtoCFIndex;
extern const char* kBlockInfoProtoCFName;
extern const uint32_t kBlockInfoProtoCFIndex;
extern const char* kLocalBlockCFName;
extern const uint32_t kLocalBlockCFIndex;
// Why does cfs need deprecating block column family?
// Avoid conflicts between uploading blocks and deleting blocks.
// Detail: https://bytedance.feishu.cn/docs/doccnMKaH5G2i2E9dvJePW3wchf
extern const char* kDeprecatingBlockCFName;
extern const uint32_t kDeprecatingBlockCFIndex;
extern const char* kDeprecatedBlockCFName;
extern const uint32_t kDeprecatedBlockCFIndex;
extern const char* kINodeStatCFName;
extern const uint32_t kINodeStatCFIndex;
extern const char* kLifecyclePolicyCFName;
extern const uint32_t kLifecyclePolicyCFIndex;
extern const char* kStorageClassStatCFName;
extern const uint32_t kStorageClassStatCFIndex;
extern const char* kStorageClassReportCFName;
extern const uint32_t kStorageClassReportCFIndex;
extern const char* kLeaseCFName;
extern const uint32_t kLeaseCFIndex;
extern const char* kDatanodeInfoCFName;
extern const uint32_t kDatanodeInfoCFIndex;
extern const char* kSnapshotRootInfoCFName;
extern const uint32_t kSnapshotRootInfoCFIndex;
extern const char* kSnapshotInodeCFName;
extern const uint32_t kSnapshotInodeCFIndex;
extern const char* kSnapshotRenameRecordCFName;
extern const uint32_t kSnapshotRenameRecordCFIndex;
extern const char* kSnapshotINodeIndexCFName;
extern const uint32_t kSnapshotINodeIndexCFIndex;
extern const char* kJobInfoCFName;
extern const uint32_t kJobInfoCFIndex;
extern const char* kWriteBackTaskCFName;
extern const uint32_t kWriteBackTaskCFIndex;
extern const char* kDirPolicyCFName;
extern const uint32_t kDirPolicyCFIndex;
extern const char* kINodeAttrTtlCFName;
extern const uint32_t kINodeAttrTtlCFIndex;
extern const char* kTtlATimeCFName;
extern const uint32_t kTtlATimeCFIndex;

}  // namespace dancenn

#endif  // _META_STORAGE_CONSTANTS_H_
