// Copyright 2019 S<PERSON><PERSON> <zhang<PERSON><PERSON><EMAIL>>

#ifndef NAMESPACE_FILE_USAGE_H_
#define NAMESPACE_FILE_USAGE_H_

#include <cnetpp/base/csonpp.h>

#include <string>
#include <utility>

namespace dancenn {

class FileUsage {
 public:
  FileUsage() = default;
  FileUsage(int64_t sata, int64_t ssd, int64_t sata_usage,
      int64_t ssd_usage, int64_t ns = 0) 
      : sata_(sata), ssd_(ssd), sata_usage_(sata_usage),
      ssd_usage_(ssd_usage), ns_(ns) {}

  int64_t sata() const {
    return sata_;
  }

  int64_t ssd() const {
    return ssd_;
  }

  int64_t sataUsage() const {
    return sata_usage_;
  }

  int64_t ssdUsage() const {
    return ssd_usage_;
  }

  int64_t ns() const {
    return ns_;
  }

  void AddUsage(const FileUsage& other) {
    sata_ += other.sata_;
    ssd_ += other.ssd_;
    sata_usage_ += other.sata_usage_;
    ssd_usage_ += other.ssd_usage_;
    ns_ += other.ns_;
  }

  cnetpp::base::Value SerializeToJson() const {
    cnetpp::base::Object obj;
    obj["sata"] = sata_;
    obj["ssd"] = ssd_;
    obj["sataUsage"] = sata_usage_;
    obj["ssdUsage"] = ssd_usage_;
    obj["ns"] = ns_;
    return cnetpp::base::Value(std::move(obj));
  }

  std::string SerializeToJsonString() const {
    return cnetpp::base::Parser::Serialize(SerializeToJson());
  }

 private:
  int64_t sata_ { 0 };              // actual file size(byte) of sata disk
  int64_t ssd_ { 0 };               // actual file size(byte) of ssd disk
  int64_t sata_usage_ { 0 };        // aligned file size(byte) of sata disk
  int64_t ssd_usage_ { 0 };         // aligned file size(byte) of sata disk
  int64_t ns_ { 0 };                // number of children
};

}  // namespace dancenn

#endif  // NAMESPACE_FILE_USAGE_H_
