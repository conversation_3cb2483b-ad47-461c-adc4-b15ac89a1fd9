#include "namespace/az_monitor.h"

#include <absl/strings/str_format.h>
#include <absl/strings/str_join.h>
#include <absl/strings/str_split.h>
#include <gflags/gflags.h>

#include "base/refresher.h"
#include "edit/ha_edit_log_context.h"
#include "namespace/namespace.h"

DECLARE_bool(az_monitor_enable);
DECLARE_int32(az_monitor_refresh_interval_ms);

namespace dancenn {

std::set<std::string> AZStr2Set(const std::string azs) {
  if (azs.empty()) {
    return std::set<std::string>();
  }
  return absl::StrSplit(azs, ',');
}

std::string AZSet2Str(const std::set<std::string> azs) {
  return absl::StrJoin(azs, ",");
}

AZMonitor::AZMonitor(NameSpace* ns,
                     EditLogContextBase* edit_log_ctx)
    : ns_(ns),
      edit_log_ctx_(edit_log_ctx) {
}

AZMonitor::~AZMonitor() {
  Stop();
}

void AZMonitor::Start() {
  refresher_ = std::make_unique<Refresher>("az-monitor-refresher",
                                           [&]() { DoRefresh(); });
  CHECK_NOTNULL(refresher_);
  refresher_->set_period(
      std::chrono::milliseconds(FLAGS_az_monitor_refresh_interval_ms));
  refresher_->Start();

  LOG(INFO) << "AZMonitor started.";
}

void AZMonitor::Stop() {
  if (!refresher_) {
    return;
  }

  refresher_->Stop();
  refresher_.reset();
  LOG(INFO) << "AZMonitor stopped.";
}

std::set<std::string> AZMonitor::GetAZBlacklistExpected() {
  // get expected AZ blacklist from MetaStorage
  std::string azs_expected;
  ns_->GetAZBlacklist(&azs_expected);
  return AZStr2Set(azs_expected);
}

std::set<std::string> AZMonitor::GetAZBlacklistEffective() {
  std::lock_guard<std::mutex> guard(mtx_);
  return blacklist_effective_;
}

void AZMonitor::DoRefresh() {
  if (!FLAGS_az_monitor_enable) {
    return;
  }

  std::set<std::string> blacklist_expected = GetAZBlacklistExpected();

  std::lock_guard<std::mutex> guard(mtx_);

  // check if need to update effective blacklist
  if (blacklist_expected == blacklist_effective_) {
    return;
  }
  LOG(INFO) << absl::StrFormat(
      "trigger effective AZ blacklist update: { %s } -> { %s }",
      AZSet2Str(blacklist_effective_), AZSet2Str(blacklist_expected));

  UpdateAZBlacklistForBK(blacklist_expected_);
  UpdateAZBlacklistForDN(blacklist_expected_);

  // will take effect immediately in current implementation, no need to check
  blacklist_effective_ = blacklist_expected;
}

void AZMonitor::UpdateAZBlacklistForBK(const std::set<std::string>& blacklist) {
  std::string azs = AZSet2Str(blacklist);
  bool ok = edit_log_ctx_->UpdateConfProperty(
      kEditLogConfDisallowBookieRegion, azs);
  if (!ok) {
    LOG(ERROR) << absl::StrFormat(
        "failed to update BK conf property '%s': '%s'",
        kEditLogConfDisallowBookieRegion, azs);
  }
}

void AZMonitor::UpdateAZBlacklistForDN(const std::set<std::string>& blacklist) {
  std::string azs = AZSet2Str(blacklist);
  // refer to BlockPlacementMultiDC::RefreshConfig()
  gflags::SetCommandLineOption("blacklist_dc", azs.c_str());
}

}  // namespace dancenn
