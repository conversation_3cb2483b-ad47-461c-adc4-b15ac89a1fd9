//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_META_STORAGE_H_
#define NAMESPACE_META_STORAGE_H_

#include <cnetpp/base/string_piece.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>
#include <google/protobuf/repeated_field.h>
#include <rocksdb/db.h>
#include <rocksdb/write_batch.h>
#include <rocksdb/merge_operator.h>
#include <absl/strings/substitute.h>

#include <condition_variable>
#include <deque>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/closure.h"
#include "base/constants.h"
#include "base/count_down_latch.h"
#include "base/read_write_lock.h"
#include "base/rw_spinlock.h"
#include "block_manager/block_info_proto.h"
#include "block_manager/block_pufs_info.h"
#include "datanode_info.pb.h"  // NOLINT (build/include_subdir)
#include "datanode_manager/datanode_info.h"
#include "edit/edit_log_sync_listener.h"
#include "fsimage.pb.h"  // NOLINT (build/include_subdir)
#include "job_manager/managed_job_state.h"
#include "namespace/block_loader.h"
#include "namespace/inode.h"
#include "namespace/inode_with_blocks.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/meta_storage_metrics.h"
#include "namespace/meta_storage_write_task.h"
#include "namespace/slows.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/cloudfs/lifecycle.pb.h"
#include "proto/generated/dancenn/edit_log.pb.h"
#include "status_code.pb.h"  // NOLINT (build/include_subdir)

using cloudfs::LifecyclePolicyProto;
using cloudfs::StorageClassProto;
using cloudfs::StorageClassReportProto;
using cloudfs::StorageClassStatProto;

namespace dancenn {

namespace meta_storage {
class Writer;
class WriteBatchMergeHandler;
}  // namespace meta_storage

// MetaStorage Iterator
using MetaStorageIter = rocksdb::Iterator;
using MetaStorageIterPtr = MetaStorageIter*;
struct MetaStorageIterHolder {
 public:
  MetaStorageIterPtr iter() {
    return iter_.get();
  }

 private:
  std::string upper_bound_key_prefix_;
  std::unique_ptr<rocksdb::Slice> slice_;
  std::unique_ptr<MetaStorageIter> iter_;

  friend class MetaStorage;
};
using MetaStorageIterHolderPtr = std::unique_ptr<MetaStorageIterHolder>;

class INodeStatItersHolder {
 public:
  INodeStatItersHolder(MetaStorageIterPtr namesystem_info_iter,
                       MetaStorageIterPtr inode_iter,
                       MetaStorageIterPtr inode_stat_iter);
  INodeStatItersHolder(const INodeStatItersHolder& other) = delete;
  INodeStatItersHolder(INodeStatItersHolder&& other) = default;
  INodeStatItersHolder& operator=(const INodeStatItersHolder& other) = delete;
  INodeStatItersHolder& operator=(INodeStatItersHolder&& other) = default;

  MetaStorageIterPtr NameSystemInfoIter() const;
  MetaStorageIterPtr INodeIter() const;
  MetaStorageIterPtr INodeStatIter() const;

 private:
  std::unique_ptr<MetaStorageIter> namesystem_info_iter_;
  std::unique_ptr<MetaStorageIter> inode_iter_;
  std::unique_ptr<MetaStorageIter> inode_stat_iter_;
};

// MetaStorage Snapshot
using MetaStorageSnap = rocksdb::Snapshot;
using MetaStorageSnapPtr = const MetaStorageSnap*;
class MetaStorageSnapHolder {
 public:
  MetaStorageSnapHolder() = default;
  MetaStorageSnapHolder(MetaStorage* ms, MetaStorageSnapPtr snap);
  ~MetaStorageSnapHolder();
  MetaStorageSnapHolder(MetaStorageSnapHolder&) = delete;
  MetaStorageSnapHolder(const MetaStorageSnapHolder&) = delete;
  MetaStorageSnapHolder& operator=(MetaStorageSnapHolder&) = delete;
  MetaStorageSnapHolder& operator=(const MetaStorageSnapHolder&) = delete;
  MetaStorageSnapPtr snapshot();

 private:
  MetaStorage* meta_storage_{};
  MetaStorageSnapPtr snapshot_{};
  friend class MetaStorage;
};
using MetaStorageSnapHolderPtr = std::unique_ptr<MetaStorageSnapHolder>;

struct SnapshotINodeKey {
  // set default value to judge initialized SnapshotINodeKey
  INodeID parent_id = kInvalidINodeId;
  std::string name;
  INodeID inode_id;
  uint64_t last_update_txid;
  INodeID snapshot_root_id;
  void Swap(SnapshotINodeKey* other);
  bool CoincideAtDirTree(const SnapshotINodeKey& key) const;
  std::string ToString() const;
};

struct DeleteSnapshotINodeInfo {
  DeleteSnapshotINodeInfo(std::string key_,
                          INodeID id_,
                          int64_t last_update_txid_)
      : key(std::move(key_)), id(id_), last_update_txid(last_update_txid_) {
  }
  std::string key;
  INodeID id;
  int64_t last_update_txid;
};

struct INodeAndSnapshot {
  INodeAndSnapshot(INode* i, SnapshotLog* s) :
      inode(i), snaplog(s) {}
  INode* inode;
  SnapshotLog* snaplog;
};

class SnapshotMergeOperator : public rocksdb::AssociativeMergeOperator {
 public:
  virtual bool Merge(const rocksdb::Slice& key,
                     const rocksdb::Slice* existing_value,
                     const rocksdb::Slice& value,
                     std::string* new_value,
                     rocksdb::Logger* logger) const override;
  static const char* kClassName() {
    return "SnapshotMergeOperator";
  }
  virtual const char* Name() const override {
    return kClassName();
  }
};

struct NameSpaceInfoDelta {
  uint64_t max_inode_id{kInvalidINodeId};
  uint64_t max_block_id{0};
  uint64_t max_gs_v2{0};
  uint64_t max_snapshot_id{0};
  int64_t num_inodes_delta{0};

  bool operator==(const NameSpaceInfoDelta& other) const {
    return max_inode_id == other.max_inode_id &&
           max_block_id == other.max_block_id && max_gs_v2 == other.max_gs_v2 &&
           max_snapshot_id == other.max_snapshot_id &&
           num_inodes_delta == other.num_inodes_delta;
  }
};

class INodeStat;
class GMockMetaStorage;
class INodeStatChangeRecorder;
class MetaStorage : public IEditLogSyncListener {
 public:
  explicit MetaStorage(const std::string& db_path);
  virtual ~MetaStorage();
  void Launch();
  void Shutdown();

  // Only used by ut.
  void SetWriter(std::shared_ptr<meta_storage::Writer> writer) {
    writer_ = writer;
  }
  void DropRpcUntilFinish(const std::function<bool()>& finish_checker);

  int NumPending() const;
  virtual void WaitNoPending(bool include_bg = false);

  // NOTICE:
  // Only used when the first launch from fsimage.
  void Clear();

  // asynchronous call
  bool MaybeForceCompactDeletion();
  bool MaybeForceCompactAll(int64_t num_deleted, bool force);
  void SetCompactAllStyle(bool bottom_compact);
  bool IsCompactAllInProgress() const;
  // synchronous call
  void ForceCompactDeletion();
  void ForceCompactStorageClassReport();
  void ForceCompactAll();

  // Only used when NameSpace launches
  // TODO(xiong): NameSpace Load them from MetaStorage and reset it into
  // MetaStorage is too stupid. This process should be closed in MetaStorage.
  void ResetLastInodeId(INodeID inode_id);
  void ResetLastAppliedTxId(int64_t txid);
  void ResetLastBlockId(uint64_t last_block_id);
  void ResetLastGenerationStampV2(uint64_t last_gs_v2);

  void PutFileSystemInfo(cnetpp::base::StringPiece key,
                         cnetpp::base::StringPiece value);
  bool GetFileSystemInfo(cnetpp::base::StringPiece key, std::string* value);

  virtual void PutNameSystemInfo(cnetpp::base::StringPiece key,
                                 cnetpp::base::StringPiece value);
  virtual bool GetNameSystemInfo(cnetpp::base::StringPiece key,
                                 std::string* value,
                                 MetaStorageIterPtr iter = nullptr);
  virtual void DeleteNameSystemInfo(cnetpp::base::StringPiece key);

  void TestOnlyPutLegacyBlockInfoProto(const BlockInfoProto& bip);
  void TestOnlyPutLegacyBlockPufsInfo(const BlockPufsInfo& block_pufs_info);
  void TestOnlyPutLegacyDeprecatedBlockPufsInfo(BlockID blk_id);
  bool TestOnlyIsLocalBlockExisted(BlockID blk_id);
  bool TestOnlyIsDeprecatingBlockExisted(BlockID blk_id);
  bool TestOnlyIsDeprecatedBlockExisted(BlockID blk_id);

  // Please make sure nobody writes RocksDB when calling me.
  void MigrateBlockInfoFromV1ToV2DuringStartUp();
  void ConstructIndexTableCommon(const std::function<void(const INode&)>& func);
  void ConstructLeaseTableDuringStartUp();
  void ConstructPolicyTableDuringStartUp(
      const std::function<void(const INode&)>& func);
  void ConstructWriteBackTasksDuringStartUp();

  // BlockInfo
  virtual bool GetBlockInfo(BlockID block_id, BlockInfoProto* bip);
  void TestOnlyPutBlockInfo(const BlockInfoProto& bip,
                            bool is_deprecating = false);
  void PutBlockInfo(const BlockInfoProto& bip,
                    const BlockInfoProto* old_bip,
                    int64_t txid,
                    Closure* done);
  void MoveBlockInfoToPersisted(const BlockInfoProto& bip,
                                const BlockInfoProto* old_bip,
                                int64_t txid,
                                Closure* done);
  void MoveBlockInfosToDeprecatedDuringStartUp();
  void MoveBlockInfoToDeprecated(const BlockInfoProto& bip);
  void DelDepringBlks(const DepringBlksToBeDel& blks,
                      int64_t txid,
                      const std::vector<Closure*>& dones);
  void DelDepredBlks(const DepredBlksToBeDel& blks,
                     int64_t txid,
                     Closure* done);
  virtual void FlushBlockInfoProtos(const BlockInfoProtos& bips,
                                    int64_t txid,
                                    Closure* done);

  void UpdateTtlATimes(const ATimeToBeUpdateProtos& atimes, int64_t txid, Closure* done);
  Status GetTtlATime(INodeID inode_id, ATimeToBeUpdate* atime);
  void DeleteTtlATime(INodeID inode_id);
  void CleanupOrphanedTtlATimes();

  // TODO(ruanjunbin): Delete
  void DeleteBlockInfo(BlockID blk_id);
  void DeleteBlockInfo(BlockID blk_id, int64_t txid, Closure* done);
  void ScanLocalBlocks(const std::function<void(const BlockInfoProto&)>& func);
  std::unique_ptr<rocksdb::Iterator> GetDeprecatingBlkIterator();
  // TODO(ruanjunbin): Delete.
  void ScanDeprecatingBlocks(std::vector<BlockID>* result);
  std::unique_ptr<rocksdb::Iterator> GetDeprecatedBlkIterator();
  // TODO(ruanjunbin): Delete.
  void ScanDeprecatedBlocks(std::vector<BlockInfoProto>* result);

  virtual uint64_t GetLastCkptTxId(MetaStorageIterPtr iter = nullptr);

  static INode CreateRoot();

  void SaveNumINodes(int64_t num_inodes);
  int64_t LoadNumINodes();
  int64_t GetNumINodes();
  void LoadLastSnapshotID();

  // Read INode
  virtual MetaStorageIterHolderPtr GetIterator();
  MetaStorageIterHolderPtr GetIteratorNull();
  MetaStorageIterHolderPtr GetIteratorWithPrefix(
      uint64_t parent_id,
      const std::string& name,
      uint32_t cf_idx = kINodeDefaultCFIndex);
  MetaStorageIterHolderPtr GetIteratorWithPrefix(
      const std::string& iterate_key_upper_bound,
      uint32_t cf_idx);
  MetaStorageIterHolderPtr GetSubINodeIterator(INodeID parent_id);
  INodeStatItersHolder GetINodeStatIterators();

  // MetaStorage Snapshot & Iterator
  virtual MetaStorageSnapHolderPtr GetSnapshot();
  MetaStorageIterHolderPtr GetIterator(MetaStorageSnapPtr snapshot, int cf_idx);
  virtual MetaStorageIterHolderPtr GetIteratorWithPrefix(
      MetaStorageSnapPtr snapshot,
      INodeID parent_id,
      const std::string& name);
  virtual StatusCode GetINode(MetaStorageSnapPtr snapshot,
                              INodeID parent_id,
                              const std::string& name,
                              INode* inode);

  bool Get(const std::string& key, std::string* value);
  virtual StatusCode GetINode(uint64_t id,
                              INode* inode,
                              MetaStorageIterPtr iter = nullptr);
  virtual StatusCode GetINode(uint64_t parent_id,
                              const std::string& name,
                              INode* inode,
                              MetaStorageIterPtr iter = nullptr);
  // get inode retained by the specified snapshot from the snapshot tables
  // read_txid: create_txid of the specified snapshot
  StatusCode GetINodeFromSnapshot(uint64_t parent_id,
                                  const std::string& name,
                                  uint64_t read_txid,
                                  INode* inode);
  INodeID GetSnapshotINodeParentId(INodeID inode_id, TxID txid);
  virtual bool ExistSnapshotINode(INodeID inode_id);
  bool ExistSnapshotReference(const INode& inode);

  // snapshot_read_info: used when we are likely to call GetSubINodes on
  // snapshot path
  void GetSubINodes(uint64_t id,
                    const std::string& start_after,
                    int limit,
                    std::vector<INode>* sub_inodes,
                    bool* has_more,
                    MetaStorageIterPtr iter = nullptr,
                    const SnapshotReadInfo* snapshot_read_info = nullptr);

  void GetSubINodes(uint64_t id,
                    const std::string& start_after,
                    std::function<bool(const INode&)> callback,
                    MetaStorageIterPtr iter = nullptr);

  // used when we are likely to call GetSubINodes on snapshot path
  void GetSubINodes(uint64_t id,
                    const SnapshotReadInfo& snapshot_read_info,
                    const std::string& start_after,
                    std::function<bool(const INode&)> callback,
                    MetaStorageIterPtr iter = nullptr);

  void GetSubINodesFromSnapshotTable(
      uint64_t id,
      const SnapshotReadInfo& snapshot_read_info,
      const std::string& start_after,
      std::function<bool(const INode&)> callback);

  void GetSubINodesStartAt(uint64_t id,
                           const std::string& start_at,
                           std::function<bool(const INode&)> callback,
                           MetaStorageIterPtr iter);

  bool HasSubINodes(uint64_t id, MetaStorageIterPtr iter = nullptr);

  uint32_t NumSubINodes(uint64_t inode_id, MetaStorageIterPtr iter = nullptr);
  uint64_t GetINodeId(uint64_t parent_id,
                      const std::string& name,
                      MetaStorageIterPtr iter = nullptr);

  virtual std::string GetLeaseHolder(INodeID inode_id,
                                     MetaStorageSnapPtr snapshot = nullptr);
  virtual Status GetWriteBackTask(INodeID inode_id, INode* inode);

  virtual void Sync();
  virtual Status CreateCheckpoint(const std::string& path);

  // InsertINode & InsertINodeParentIndexAsync Only used in
  // FSImageLoader::LoadINodeFile to init rocksdb
  void InsertINode(std::shared_ptr<INode> inode);
  void InsertINodeParentIndexAsync(const INode& inode, Closure* done = nullptr);

  void InsertINodeDirPolicyIndexAsync(const INode& inode, Closure* done);

  void MultiInsertINodes(const std::deque<std::shared_ptr<INode>>& inodes);

  // unified commit procedure of local txn on inodes
  //
  // argument:
  //   inodes_add = added inodes (ID newly allocated)
  //   inodes_mod = modified inodes (ID unchanged)
  //   inodes_del = deleted inodes (ID to be cleaned)
  //   bips_mod   = modified bips (include deprecated bips)
  //   bips_add   = add bips (by add block)
  //   parents    = modified parents
  virtual void OrderedCommitINodes(
      std::vector<INode*>* inodes_add,  // added inodes (ID newly allocated)
      std::vector<INodeAndSnapshot>*
          inodes_mod,  // modified inodes (parent and name unchanged)
      std::vector<INodeAndSnapshot>* inodes_mov_src,
      std::vector<INode*>*
          inodes_mov_dst,  // moved inodes (parent or name changed)
      std::vector<INodeAndSnapshot>*
          inodes_del,  // deleted inodes (ID to be cleaned)
      std::vector<INodeAndSnapshot>* parents,  // modified parents
      std::vector<INode>* inodes_old,          // old copies to verify
      const std::vector<BlockInfoProto>& bips_add,
      const std::vector<BlockInfoProto>& bips_mod,
      int64_t txid,
      const std::vector<Closure*>& dones,
      INodeStatChangeRecorder* recorder = nullptr);

  // use INode version, remove snapshot support
  virtual void OrderedCommitINodes(
      std::vector<INode>* inodes_add,
      std::vector<std::pair<INode, INode>>* inodes_mod,
      std::vector<INode>* inodes_del,
      std::vector<INode>* parents,
      std::vector<INode>* inodes_old,
      const std::vector<BlockInfoProto>& bips_mod,
      const std::vector<BlockInfoProto>& bips_add,
      int64_t txid,
      const std::vector<Closure*>& dones,
      INodeStatChangeRecorder* recorder = nullptr);

  // customized API for concat
  void OrderedConcatINode(INode& target_inode,
                          const INode* old_target_inode,
                          const std::vector<INode>& src_inodes,
                          const std::vector<BlockInfoProto>& src_bips,
                          INode& parent,
                          int64_t txid,
                          Closure* done = nullptr,
                          INodeStatChangeRecorder* recorder = nullptr);

  void UpdateINode(const INode& inode);
  void UpdateSnapshotINodesAsync(
      const std::vector<DeleteSnapshotINodeInfo>& inodes_del,
      Closure* done = nullptr);

  // commit empty writebatch for backward compatibility
  void OrderedCommitNop(int64_t txid);

  void InsertDatanodeInfoAsync(const DatanodeID internal_id,
                               const DatanodeInfoEntryPB& pb,
                               Closure* done = nullptr);
  void MultiInsertDatanodeInfoAsync(
      const std::vector<DatanodeInfoEntryPB>& pb_list,
      Closure* done = nullptr);

  virtual void OrderedUpdateINode(INode* minode,
                                  const INode* old_inode,
                                  SnapshotLog& snaplog,
                                  int64_t txid,
                                  Closure* done = nullptr,
                                  INodeStatChangeRecorder* recorder = nullptr);

  // TODO(ruanjunbin): These functions are only used when applying
  // old edit log ops. Delete them if we migrate to new edit log ops.
  virtual void OrderedUpdateINodeAndDeleteLastBlock(
      const INode& inode,
      BlockID last_blk_to_del,
      const INode* old_inode,
      int64_t txid,
      Closure* done = nullptr,
      INodeStatChangeRecorder* recorder = nullptr);
  void OrderedUpdateINodeAndAddBlock(
      const INode& inode,
      int64_t txid,
      Closure* done = nullptr,
      INodeStatChangeRecorder* recorder = nullptr);
  // Close the file and commit last block
  void OrderedUpdateINodeAndFinalizeBlocks(
      const INode& inode,
      BlockID origin_last_blk_to_del,
      int64_t txid,
      Closure* done = nullptr,
      INodeStatChangeRecorder* recorder = nullptr);

  // Namespace Snapshot related api
  void ScanSnapshotRoot(
      const std::function<bool(INodeID id, const std::string& path)>& cb) const;
  enum WriteSnapshotDataMode {
    ALLOW_SNAPSHOT = 0,
    DISALLOW_SNAPSHOT = 1,
    CREATE_SNAPSHOT = 2,
    DELETE_SNAPSHOT = 3,
    RENAME_SNAPSHOT = 4,
  };
  // `src` is used in specific modes, pass null if it's not used
  // The same to `snapshot_id`
  void OrderedUpdateINodeAndSnapshotData(const INode& inode,
                                         WriteSnapshotDataMode write_mode,
                                         const std::string* src,
                                         const uint64_t* new_snapshot_id,
                                         int64_t txid,
                                         Closure* done = nullptr);

  virtual void OrderedAddBlock(INode* minode,
                               const BlockInfoProto* penultimate_bip,
                               const BlockInfoProto& last_bip_tbuc,
                               const INode* old_inode,
                               SnapshotLog& snaplog,
                               int64_t txid,
                               Closure* done,
                               INodeStatChangeRecorder* recorder = nullptr);

  void OrderedAbandonBlock(const INode& inode,
                           BlockID last_blk_to_be_abandoned,
                           const INode* old_inode,
                           int64_t txid,
                           Closure* done,
                           INodeStatChangeRecorder* recorder = nullptr);
  void OrderedUpdatePipeline(const INode& inode,
                             const BlockInfoProto& last_bip_tbuc,
                             const INode* old_inode,
                             int64_t txid,
                             Closure* done,
                             INodeStatChangeRecorder* recorder = nullptr);
  // Use for Fsync and CommitLastBlock
  void OrderedFsync(INode* minode,
                    const BlockInfoProto* last_bip_tbuc,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder = nullptr);
  // virtual for mock
  virtual void OrderedCommitLastBlock(
      INode* minode,
      const BlockInfoProto& last_bip_tbuc,
      const INode* old_inode,
      SnapshotLog& snaplog,
      int64_t txid,
      Closure* done,
      INodeStatChangeRecorder* recorder = nullptr);
  virtual void OrderedCloseFile(
      INode* minode,
      // In block recovery, dn may drop the last block of origin inode.
      // | ... | last_bip | dropped_blk_id |
      // See NameSpace::CommitBlockSynchronization for more infos.
      const BlockInfoProto* last_bip,
      BlockID last_blk_to_be_abandoned,
      const INode* old_inode,
      SnapshotLog& snaplog,
      int64_t txid,
      Closure* done,
      INodeStatChangeRecorder* recorder = nullptr);

  void OrderedInsertINode(INode* new_inode,
                          int64_t txid,
                          Closure* done = nullptr,
                          INodeInPath* parent_iip = nullptr,
                          INodeStatChangeRecorder* recorder = nullptr);

  // old version API for test
  void PushINodeTXWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                            int64_t txid,
                            INodeID max_inode_id,
                            int64_t num_inodes_delta,
                            Closure* done = nullptr);

  // Push inode transaction task to write queue
  virtual void PushINodeTXWriteTasks(std::unique_ptr<rocksdb::WriteBatch> wb,
                                     int64_t txid,
                                     const NameSpaceInfoDelta& delta,
                                     std::unique_ptr<KVVerifyVec> verify_kvs,
                                     const std::vector<Closure*>& dones);
  virtual void PushINodeTXWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                                    int64_t txid,
                                    const NameSpaceInfoDelta& delta,
                                    std::unique_ptr<KVVerifyVec> verify_kvs,
                                    Closure* done);

  // Push inode non-transaction task to write queue
  void PushINodeBGWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                            int64_t num_inodes_delta,
                            Closure* done = nullptr);

  void PushDatanodeBGWriteTask(std::unique_ptr<rocksdb::WriteBatch> wb,
                               DatanodeID max_dn_id,
                               Closure* done = nullptr);

  virtual void OrderedPutNameSystemInfo(const std::string& key,
                                        cnetpp::base::StringPiece value,
                                        int64_t txid,
                                        Closure* done = nullptr);

  virtual void OrderedPutBlockId(const uint64_t block_id,
                                 int64_t txid,
                                 Closure* done = nullptr);

  virtual void OrderedPutGenerationStampV2(const uint64_t gs_v2,
                                           int64_t txid,
                                           Closure* done = nullptr);

  void OrderedPutAccessCounterSnapshot(const std::string& key,
                                       cnetpp::base::StringPiece value,
                                       int64_t txid,
                                       Closure* done = nullptr);

  void TestOnlyDeleteINode(const INode& inode);

  // Only used in NameSpace::DeleteDanglingINodes(background deleter)
  // to delete inodes in pending delete cf but not default cf.
  void DeletePDINodesAsync(const std::vector<INode>& inodes,
                           Closure* done);

  // Only used in NameSpace::DeleteDanglingINodes(background deleter)
  // to delete inodes in default cf but not pending delete cf.
  void DeleteINodesAsync(const std::vector<INode>& inodes,
                         Closure* done);

  void OrderedDeleteINode(const INode& inode,
                          int64_t txid,
                          Closure* done = nullptr,
                          INode* parent = nullptr,
                          INodeStatChangeRecorder* recorder = nullptr);

  void OrderedUpdateINodeMergeBlock(
      INode* minode,
      const INode* old_inode,
      const BlockInfoProto& bip,
      const std::vector<BlockProto>& depred_blks,
      SnapshotLog& snaplog,
      int64_t txid,
      Closure* done = nullptr,
      INodeStatChangeRecorder* recorder = nullptr);

  virtual void PinINode(INode* minode,
                        const INode* old_inode,
                        SnapshotLog& snaplog,
                        const JobInfoOpBody& job,
                        const ManagedJobId& cancel_job_id,
                        int64_t txid,
                        Closure* done,
                        INodeStatChangeRecorder* recorder = nullptr);
  virtual void ReconcileINodeAttrs(
      INode* minode,
      const INode* old_inode,
      SnapshotLog& snaplog,
      const std::vector<ManagedJobId>& cancel_job_id,
      const std::set<int64_t>& expired_ttl,
      const std::set<int64_t>& new_ttl,
      int64_t txid,
      Closure* done,
      INodeStatChangeRecorder* recorder = nullptr);

  uint64_t GetINodeParentId(uint64_t child);

  StatusCode GetINodeParentInfo(const INodeID child,
                                INodeParentInfoPB* parent_info);

  StatusCode GetDatanodeInfo(const DatanodeID dn_id, DatanodeInfoEntryPB* pb);

  int64_t NumINodes() const;
  void ParentMapStats(std::vector<size_t>& v) const;

  virtual void ScanPendingDeleteCF(
      const std::function<bool(const INode&)>& callback);
  void ScanSnapshotINodes(
      const std::function<bool(SnapshotINodeKey*,
                               const rocksdb::Slice&,
                               const rocksdb::Slice&)>& callback);
  // return: true if scan complete without break
  bool ScanColumnFamily(
      uint32_t cf_idx,
      const std::function<bool(const rocksdb::Slice&, const rocksdb::Slice&)>&
          callback);

  void ScanDatanodeInfoCf(
      const std::function<bool(const DatanodeID&, const DatanodeInfoEntryPB&)>&
          callback);
  bool ForEachINode(
      uint64_t dir_id,
      const std::string& dir_name,
      const std::function<bool(const std::string&, const INode&)>& cb,
      std::set<INodeID>* all_inodes = nullptr);

  // fullpath may equal "//", that mean root
  bool ForEachDir(
      uint64_t dir_id,
      const std::string& dir_name,
      const std::function<bool(const std::string&, const INode&)>& cb);
  // fullpath may equal "//", that mean root
  virtual bool ForEachDir(
      uint64_t dir_id,
      const std::string& dir_name,
      uint64_t* cache_inodes,
      const std::function<bool(const std::string&, const INode&)>& cb);

  bool ForEachDirComputeQuota(uint64_t dir_id,
                              const std::string& dir_name,
                              const std::string& quota_path,
                              const std::string& quota_team,
                              bool need_judge_trash,
                              bool need_judge_mapping,
                              const std::function<bool(const std::string&,
                                                       const INode&,
                                                       const std::string&,
                                                       const std::string&,
                                                       bool,
                                                       bool)>& cb);

  void BatchWrite(const std::vector<meta_storage::WriteTask*>& tasks);
  void PostSlow(meta_storage::WriteTask* task);
  void TxFinish(int64_t start_txid, int n) override;

  void StartActive();
  void StartStandby();
  void NotifyForActive(int64_t txid);

  static const char* GetRootINodeKey();
  static int GetRootINodeKeyLen();
  virtual INode GetRootINode();

  Status GetDirectoryINodeStat(const INodeID dir_id,
                               INodeStat* stat,
                               MetaStorageIterPtr iter = nullptr);
  void PutDirectoryINodeStatAsync(const INode& dir,
                                  const INodeStat& stat,
                                  Closure* done);
  Status PutDirectoryINodeStat(const INode& dir, const INodeStat& stat);

  virtual Status GetLifecyclePolicy(const INodeID id,
                                    uint64_t* ts,
                                    LifecyclePolicyProto* policy);
  void OrderedPutLifecyclePolicy(const INodeID id,
                                 const uint64_t ts,
                                 const LifecyclePolicyProto& policy,
                                 int64_t txid,
                                 Closure* done);
  void OrderedDeleteLifecyclePolicy(const INodeID id,
                                    int64_t txid,
                                    Closure* done);
  MetaStorageIterHolderPtr GetLifecyclePolicyIterator();
  void ScanLifecyclePolicy(
      std::function<bool(const INodeID,
                         const uint64_t,
                         const LifecyclePolicyProto&)> callback,
      MetaStorageIterPtr iter);

  Status GetStorageClassStat(const INodeID inode_id,
                             StorageClassStatProto* stat,
                             MetaStorageIterPtr iter);
  void PutStorageClassStatAsync(const INodeID inode_id,
                                const StorageClassStatProto& stat,
                                Closure* done);
  Status PutStorageClassStat(const INodeID inode_id,
                             const StorageClassStatProto& stat);
  void DeleteStorageClassStatAsync(const INodeID inode_id, Closure* done);
  void ScanStorageClassStat(
      std::function<bool(const INodeID, const StorageClassStatProto&)> callback,
      MetaStorageIterPtr iter);

  void SubmitPCIOCMetrics();

  void GetStorageClassReports(const BlockID block_id,
                              std::vector<std::string>* dn_uuids,
                              std::vector<StorageClassReportProto>* reports);
  Status GetStorageClassReport(const BlockID block_id,
                               const std::string& dn_uuid,
                               StorageClassReportProto* report);
  virtual void UpdateStorageClassReportsAsync(
      const std::string& dn_uuid,
      std::vector<std::pair<BlockID, StorageClassReportProto>>& replica_report,
      Closure* done);
  void DeleteStorageClassReportAsync(const BlockID block_id,
                                     const std::string& dn_uuid,
                                     Closure* done);
  void ScanStorageClassReport(
      std::function<bool(const BlockID,
                         const std::string& dn_uuid,
                         const StorageClassReportProto&)> callback,
      MetaStorageIterPtr iter);

  void PutAZBlacklistAsync(const std::string& azs, int64_t txid, Closure* done);
  void GetAZBlacklist(std::string* azs);

  void ScanLease(
      const std::function<void(INodeID inode_id,
                               const std::string& client_name)>& callback,
      MetaStorageIterPtr iter);

  // Acc related
  void OrderedCreateUfsFiles(int64_t txid,
                             const std::vector<INodeWithBlocks>& files,
                             const INode* dir_inode,
                             const std::vector<Closure*>& dones);
  void OrderedUpdateUfsINodes(int64_t txid,
                              const std::vector<INode>& files,
                              const std::vector<INode>* old_files,
                              Closure* done);
  void OrderedCreateUfsFile(int64_t txid,
                            const INodeWithBlocks& new_file,
                            Closure* done);
  void OrderedOverwriteUfsFile(int64_t txid,
                               const INodeWithBlocks& new_file,
                               const INode* old_file,
                               Closure* done);
  void OrderedUpdateUfsBlockInfo(int64_t txid,
                                 const BlockInfoProto& bip,
                                 const BlockInfoProto* old_bip,
                                 Closure* done);

  template <typename T>
  static std::string EncodeInteger(T number);
  template <typename T>
  static T DecodeInteger(const rocksdb::Slice& slice);

  void PutJobInfo(const JobInfoOpBody& job_info, int64_t txid, Closure* done);
  void DeleteJobInfo(const std::string& job_id, int64_t txid, Closure* done);
  std::unique_ptr<rocksdb::Iterator> GetJobInfoIterator();
  bool GetJobInfo(const std::string& job_id, JobInfoOpBody& job_info);

 protected:
  void StartSlows();
  void StopSlows();
  void StartWriter();
  void StopWriter();

  void DropTableCommon(const std::string& func_name, uint32_t cf_index);
  void DropLeaseTable();
  void DropWriteBackTasks();
  void DropPolicyTable();

  void DoOrderedUpdate(meta_storage::WriteTask* task);

  void DeleteLastBlockInWriteBatch(const INode& inode,
                                   BlockID last_blk_to_del,
                                   rocksdb::WriteBatch* wb);
  bool IsValidToCommitBlockInfoChange(const BlockInfoProto& new_bip);

  int64_t UpdateParentIndexWriteBatch(const INode& inode,
                                      bool is_del,
                                      rocksdb::WriteBatch* wb);
  void UpdateLeaseWriteBatch(const INode& inode,
                             bool is_del,
                             rocksdb::WriteBatch* wb);
  void UpdateDirPolicyWriteBatch(const INode& inode,
                                 bool is_del,
                                 rocksdb::WriteBatch* wb);
  void UpdateWriteBackTaskWriteBatch(const INode& inode,
                                     bool is_del,
                                     rocksdb::WriteBatch* wb);

  bool EncodeParentIndexKey(const INodeID inode, std::string* key_str);

  bool EncodeScanKey(uint64_t p_id,
                     const std::string& name,
                     std::string* key_str);

 public:
  void EncodeSnapshotINodeKey(INodeID p_id,
                              const std::string& name,
                              INodeID id,
                              uint64_t last_update_txid,
                              INodeID snapshot_root_id,
                              std::string* key_str);
  void EncodeSnapshotINodeIndexKey(INodeID id,
                                   uint64_t last_update_txid,
                                   std::string* key_str);

  bool EncodeStoreKey(uint64_t p_id,
                      const std::string& name,
                      uint64_t id,
                      std::string* key_str);
  void DecodeStoreKey(const cnetpp::base::StringPiece& key_str,
                      uint64_t* p_id,
                      std::string* name,
                      uint64_t* id);
  Status DecodeStoreKeyInternal(const cnetpp::base::StringPiece& key_str,
                                uint64_t* p_id,
                                std::string* name,
                                uint64_t* id);

  void DecodeSnapshotINodeKey(const cnetpp::base::StringPiece& key_str,
                              INodeID* p_id,
                              std::string* name,
                              INodeID* id,
                              uint64_t* last_update_txid,
                              INodeID* snapshot_root_id);
  void DecodeSnapshotINodeIndexKey(const cnetpp::base::StringPiece& key_str,
                                   INodeID* id,
                                   uint64_t* last_update_txid);
  static std::string EncodeINodeID(INodeID inode_id);
  static INodeID DecodeINodeID(rocksdb::Slice slice);
  static std::string EncodeBlockID(BlockID blk_id);
  static BlockID DecodeBlockID(rocksdb::Slice slice);
  static void EncodeSCRKey(BlockID blk_id,
                           const std::string& dn_uuid,
                           std::string* key);
  static void DecodeSCRKey(rocksdb::Slice slice,
                           BlockID* blk_id,
                           std::string* dn_uuid);

 protected:
  virtual std::unique_ptr<rocksdb::DB> OpenRocksDB(
      const rocksdb::DBOptions& opt);

  void DecodeStoreKey(const cnetpp::base::StringPiece& key_str,
                      uint64_t* pid,
                      uint64_t* id);

  void DecodeSnapshotINodeKey(const cnetpp::base::StringPiece& key_str,
                              uint64_t* last_update_txid,
                              INodeID* snapshot_root_id);

  bool IsRootINodeKey(const char* key, int len);

  bool IsPrefixScanKey(const cnetpp::base::StringPiece& key,
                       const cnetpp::base::StringPiece& prefix);

  bool IsSnapshotPrefixScanKey(const cnetpp::base::StringPiece& key,
                               const cnetpp::base::StringPiece& prefix);

  bool IsPrefixIdKey(const cnetpp::base::StringPiece& key,
                     const cnetpp::base::StringPiece& prefix);

  void FillPendingDeleteWriteBatch(const INode& inode,
                                   rocksdb::WriteBatch* wb,
                                   bool erase);
  void FillDeprecatingBlockWriteBatch(const INode& inode,
                                      rocksdb::WriteBatch* wb);
  void FillDeleteStatWriteBatch(const INode& inode,
                                rocksdb::WriteBatch* wb);
  void FillNameSpaceInfoWriteBatch(const char* key,
                                   uint64_t value,
                                   std::shared_ptr<rocksdb::WriteBatchBase> wb);
  void FillSnapshotWriteBatch(SnapshotLog& snaplog,
                              TxID txid,
                              rocksdb::WriteBatch* wb);
  void FillINodeKVVerifyVec(const INode& inode,
                            KVVerifyVec* verify_kvs);

  // key format about datanode info in datanode info table
  static bool EncodeDatanodeInfoKey(const DatanodeID dn_id,
                                    std::string* key_str);

  static bool DecodeDatanodeInfoKey(const cnetpp::base::StringPiece& key_str,
                                    DatanodeID* dn_id);

  using INodeQ = std::queue<std::pair<std::string, std::unique_ptr<INode>>>;

  bool ForEachCurrentDir(uint64_t dir_id,
                         const std::string& dir_name,
                         INodeQ* inodes);

  virtual std::unique_ptr<rocksdb::WriteBatch> CreateWriteBatch();

  MetaStorageSnapPtr AcquireSnapshot();
  void ReleaseSnapshot(MetaStorageSnapPtr snapshot);

 protected:
  std::atomic<bool> stopped_{false};

  INodeID last_inode_id_{0};
  uint64_t last_block_id_{0};
  uint64_t last_generation_stamp_v2_{0};
  uint64_t last_snapshot_id_{0};
  std::string db_path_;
  rocksdb::WriteOptions rocks_write_option_;
  std::unique_ptr<rocksdb::DB> rocks_db_;
  std::shared_ptr<rocksdb::Statistics> rocks_stats_;
  std::shared_ptr<rocksdb::Cache> rocks_cache_;
  std::shared_ptr<rocksdb::Cache> rocks_cache_for_bip_;
  std::shared_ptr<rocksdb::Cache> rocks_cache_for_scr_;
  std::vector<rocksdb::ColumnFamilyDescriptor> column_families_;
  std::vector<rocksdb::ColumnFamilyHandle*> handles_;
  std::unordered_map<uint32_t, rocksdb::ColumnFamilyHandle*> cf_handle_map_;

  std::atomic<int64_t> num_inodes_{-1};

  friend class meta_storage::WriteBatchMergeHandler;
  friend class GMockMetaStorage;
  friend class MetaStorageSnapHolder;

  // The thread which is responsible for committing all edits into rocksdb
  // in order.
  std::unique_ptr<cnetpp::concurrency::Thread> writer_thread_;
  std::shared_ptr<meta_storage::Writer> writer_;
  std::unique_ptr<Slows> slows_;

  // Task running background to compacting to one level
  class BGCompactAllTask : public cnetpp::concurrency::Task {
   public:
    explicit BGCompactAllTask(MetaStorage* storage) : storage_(storage) {
    }

    void Stop() override {
      std::lock_guard<std::mutex> guard(mutex_);
      cnetpp::concurrency::Task::Stop();
      cond_var_.notify_all();
    }

    bool operator()(void* arg = nullptr) override;

   private:
    friend class MetaStorage;
    MetaStorage* storage_{nullptr};

    std::mutex mutex_;
    std::condition_variable cond_var_;
  };

  std::unique_ptr<cnetpp::concurrency::Thread> bg_compact_all_worker_;
  std::shared_ptr<BGCompactAllTask> bg_compact_all_task_;
  std::atomic<int64_t> num_deleted_for_compact_all_{0};
  std::atomic<bool> force_compact_deletion_{false};
  bool bottom_compact_;
  bool bg_compact_in_progress_;

  void StartBGCompactAllWorker();
  void StopBGCompactAllWorker();

  bool ReachCompactAllThreshold();

  friend struct MetaStorageMetrics;
  MetaStorageMetrics metrics_;
};

class ReadOnlyMetaStorage : public MetaStorage {
 public:
  explicit ReadOnlyMetaStorage(const std::string& db_path);

 protected:
  std::unique_ptr<rocksdb::DB> OpenRocksDB(
      const rocksdb::DBOptions& opt) override;
};

template <typename T>
std::string MetaStorage::EncodeInteger(T number) {
  std::string result;
  result.resize(sizeof(T) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(result.c_str()), 0, number);
  return result;
}

template <typename T>
T MetaStorage::DecodeInteger(const rocksdb::Slice& slice) {
  if (slice.size() != sizeof(T) / sizeof(uint8_t)) {
    LOG(FATAL) << "Expected slice size " << sizeof(T) / sizeof(uint8_t)
               << ", actual " << slice.size();
  }
  return platform::ReadBigEndian<T>(slice.data(), 0);
}

}  // namespace dancenn

#endif  // NAMESPACE_META_STORAGE_H_
