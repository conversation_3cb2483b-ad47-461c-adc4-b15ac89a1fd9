// Copyright 2020 Mu <PERSON> <<EMAIL>>

#ifndef NAMESPACE_META_STORAGE_METRICS_H_
#define NAMESPACE_META_STORAGE_METRICS_H_

#include <memory>

#include "base/metrics.h"

namespace dancenn {

class MetaStorage;

struct MetaStorageMetrics {
  explicit MetaStorageMetrics(MetaStorage* ns);

  ~MetaStorageMetrics() = default;
  MetaStorageMetrics(const MetaStorageMetrics&) = delete;
  MetaStorageMetrics& operator=(const MetaStorageMetrics&) = delete;

  void SubmitPCIOCMetrics();

  std::shared_ptr<Metrics> metrics_;
  std::shared_ptr<Metrics> metrics_pc_;
  std::shared_ptr<Metrics> metrics_ioc_;

  MetricID single_writer_merged_batch_time_;
  MetricID single_writer_write_rocksdb_time_;

  MetricID write_items_batch_num_;

  MetricID force_compact_all_time_;
  MetricID force_compact_deletion_time_;
  MetricID num_will_retrieve_deletion_;

  MetricID get_inode_num_;
  MetricID get_inode_time_;
  MetricID get_inode_scan_num_;
  MetricID get_inode_scan_time_;
  MetricID get_block_info_num_;
  MetricID get_block_info_time_;

  MetricID get_scrs_num_;
  MetricID get_scrs_time_;
  MetricID get_scrs_seek_valid_next_time_;
  MetricID get_scrs_seek_invalid_next_time_;
  MetricID get_scrs_count_valid_;
  MetricID update_scrs_num_;
  MetricID update_scrs_time_;
  MetricID update_scrs_count_put_;
  MetricID update_scrs_count_del_;
  MetricID update_scrs_count_put_filtered_;
  MetricID update_scrs_count_del_filtered_;

  MetricID wtask_bind_empty_callback_task_num_;
  MetricID wtask_bind_fast_single_callback_task_num_;
  MetricID wtask_bind_slow_single_callback_task_num_;
  MetricID wtask_bind_slow_multiple_callback_task_num_;

  // XXX rocksdb PerfContext
  MetricID user_key_comparison_count_;
  MetricID block_cache_hit_count_;
  MetricID block_read_count_;
  MetricID block_read_byte_;
  MetricID block_read_time_;
  MetricID block_checksum_time_;
  MetricID block_decompress_time_;
  MetricID internal_key_skipped_count_;
  MetricID internal_delete_skipped_count_;
  MetricID internal_recent_skipped_count_;
  MetricID internal_merge_count_;
  MetricID get_snapshot_time_;
  MetricID get_from_memtable_time_;
  MetricID get_from_memtable_count_;
  MetricID get_post_process_time_;
  MetricID get_from_output_files_time_;
  MetricID seek_on_memtable_time_;
  MetricID seek_on_memtable_count_;
  MetricID next_on_memtable_count_;
  MetricID prev_on_memtable_count_;
  MetricID seek_child_seek_time_;
  MetricID seek_child_seek_count_;
  MetricID seek_min_heap_time_;
  MetricID seek_max_heap_time_;
  MetricID seek_internal_seek_time_;
  MetricID find_next_user_entry_time_;
  MetricID write_wal_time_;
  MetricID write_memtable_time_;
  MetricID write_delay_time_;
  MetricID write_pre_and_post_process_time_;
  MetricID db_mutex_lock_nanos_;
  MetricID db_condition_wait_nanos_;
  MetricID merge_operator_time_nanos_;
  MetricID read_index_block_nanos_;
  MetricID read_filter_block_nanos_;
  MetricID new_table_block_iter_nanos_;
  MetricID new_table_iterator_nanos_;
  MetricID block_seek_nanos_;
  MetricID find_table_nanos_;
  MetricID bloom_memtable_hit_count_;
  MetricID bloom_memtable_miss_count_;
  MetricID bloom_sst_hit_count_;
  MetricID bloom_sst_miss_count_;
  MetricID env_new_sequential_file_nanos_;
  MetricID env_new_random_access_file_nanos_;
  MetricID env_new_writable_file_nanos_;
  MetricID env_reuse_writable_file_nanos_;
  MetricID env_new_random_rw_file_nanos_;
  MetricID env_new_directory_nanos_;
  MetricID env_file_exists_nanos_;
  MetricID env_get_children_nanos_;
  MetricID env_get_children_file_attributes_nanos_;
  MetricID env_delete_file_nanos_;
  MetricID env_create_dir_nanos_;
  MetricID env_create_dir_if_missing_nanos_;
  MetricID env_delete_dir_nanos_;
  MetricID env_get_file_size_nanos_;
  MetricID env_get_file_modification_time_nanos_;
  MetricID env_rename_file_nanos_;
  MetricID env_link_file_nanos_;
  MetricID env_lock_file_nanos_;
  MetricID env_unlock_file_nanos_;
  MetricID env_new_logger_nanos_;

  // XXX rocksdb IOStat Context
  MetricID thread_pool_id_;
  MetricID bytes_written_;
  MetricID bytes_read_;
  MetricID open_nanos_;
  MetricID allocate_nanos_;
  MetricID write_nanos_;
  MetricID read_nanos_;
  MetricID range_sync_nanos_;
  MetricID fsync_nanos_;
  MetricID prepare_write_nanos_;
  MetricID logger_nanos_;

  MetricID get_deep_snapshot_reference_num_;

  // Lifecycle-related
  MetricID lifecycle_update_policy_num_;
  MetricID lifecycle_delete_policy_num_;
  MetricID lifecycle_report_storage_class_num_;

  // key size
  std::vector<std::shared_ptr<Gauge>> cf_key_size_metrics_;
};

}  // namespace dancenn

#endif  // NAMESPACE_META_STORAGE_METRICS_H_
