//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: sunguo<PERSON> <<EMAIL>>
//

#ifndef NAMESPACE_XATTR_H_
#define NAMESPACE_XATTR_H_

#include <cnetpp/base/string_utils.h>
#include <glog/logging.h>
#include <string>

#include "inode.pb.h"  // NOLINT(build/include)
#include "xattr.pb.h"  // NOLINT(build/include)

#include "base/constants.h"
#include "base/status.h"
#include "base/java_exceptions.h"
#include "base/platform.h"

using cloudfs::XAttrProto;
using google::protobuf::RepeatedPtrField;

namespace dancenn {

class XAttrs {
 public:
  static Status BuildXAttr(const std::string &name,
                           const std::string &value,
                           XAttrProto *xattr) {
    CHECK_NOTNULL(xattr);
    CHECK(!name.empty());
    auto prefix_index = name.find('.');
    if (prefix_index == std::string::npos || prefix_index < 3) {
      return Status{
          JavaExceptions::Exception::kIllegalArgumentException,
          Code::kError,
          "XAttr name must be prefixed with user/trusted/security/system/raw, followed by a '.'"};  // NOLINT(whitespace/line_length)
    } else if (prefix_index == name.length() - 1) {
      return Status{
          JavaExceptions::Exception::kIllegalArgumentException,
          Code::kError,
          "XAttr name cannot be empty."};
    }
    std::string prefix = name.substr(0, prefix_index);
    cnetpp::base::StringUtils::ToLower(&prefix);
    XAttrProto::XAttrNamespaceProto ns;
    if (prefix == "user") {
      ns = XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_USER;
    } else if (prefix == "trusted") {
      ns = XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_TRUSTED;  // NOLINT(whitespace/line_length)
    } else if (prefix == "system") {
      ns = XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM;  // NOLINT(whitespace/line_length)
    } else if (prefix == "security") {
      ns = XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SECURITY;  // NOLINT(whitespace/line_length)
    } else if (prefix == "raw") {
      ns = XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_RAW;
    } else {
      return Status{
          JavaExceptions::Exception::kIllegalArgumentException,
          Code::kError,
          "XAttr name must be prefixed with user/trusted/security/system/raw, followed by a '.'"};  // NOLINT(whitespace/line_length)
    }
    xattr->set_namespace_(ns);
    xattr->set_name(name.substr(prefix_index + 1));
    xattr->set_value(value);
    return Status();
  }

  static bool CheckXAttrs(const RepeatedPtrField<XAttrProto> &xattrs) {
    // check duplicate xattrs, if duplicate return false
    for (int i = 0; i < xattrs.size(); i++) {
      for (int j = i + 1; j < xattrs.size(); j++) {
        if (xattrs.Get(i).namespace_() == xattrs.Get(j).namespace_()
            && xattrs.Get(i).name() == xattrs.Get(j).name()) {
          return false;
        }
      }
    }
    return true;
  }

  static bool FilterXAttrs(const RepeatedPtrField<XAttrProto> &to_filter,
                            RepeatedPtrField<XAttrProto> *existing_xattrs) {
    if (existing_xattrs == nullptr || existing_xattrs->size() == 0
        || to_filter.size() == 0) {
      return true;
    }
    // raw.hdfs.crypto.encryption.zone
    // security.hdfs.unreadable.by.superuser
    // these xattrs can not be deleted.
    for (int i = 0; i < existing_xattrs->size(); i++) {
      for (const auto &f : to_filter) {
        if (f.namespace_() == existing_xattrs->Get(i).namespace_()
            && f.name() == existing_xattrs->Get(i).name()) {
          existing_xattrs->DeleteSubrange(i, 1);
          i--;
          break;
        }
      }
    }
    return true;
  }

  static std::string GetPrefixName(const XAttrProto &xattr) {
    if (xattr.namespace_()
        == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_USER) {  // NOLINT(whitespace/line_length)
      return "user." + xattr.name();
    } else if (xattr.namespace_()
        == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_TRUSTED) {  // NOLINT(whitespace/line_length)
      return "trusted." + xattr.name();
    } else if (xattr.namespace_()
        == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM) {  // NOLINT(whitespace/line_length)
      return "system." + xattr.name();
    } else if (xattr.namespace_()
        == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SECURITY) {  // NOLINT(whitespace/line_length)
      return "security." + xattr.name();
    } else if (xattr.namespace_()
        == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_RAW) {  // NOLINT(whitespace/line_length)
      return "raw." + xattr.name();
    }
    LOG(FATAL) << "unrecognized xattr namespace";
    return "";
  }

  static void FilterXAttrsForApi(const RepeatedPtrField<XAttrProto> &xattrs,
                                 bool is_raw_path, bool is_super_user,
                                 RepeatedPtrField<XAttrProto> *res) {
    CHECK(res);
    if (xattrs.size() == 0) {
      return;
    }
    for (const XAttrProto &xattr : xattrs) {
      if (xattr.namespace_()
          == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_USER) {  // NOLINT(whitespace/line_length)
        res->Add()->CopyFrom(xattr);
      } else if (xattr.namespace_()
          == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_TRUSTED  // NOLINT(whitespace/line_length)
          && is_super_user) {
        res->Add()->CopyFrom(xattr);
      } else if (xattr.namespace_()
          == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_RAW  // NOLINT(whitespace/line_length)
          && is_super_user && is_raw_path) {
        res->Add()->CopyFrom(xattr);
      } else if (GetPrefixName(xattr)
          == std::string(kSecurityXAttrUnreadableBySuperUser)) {
        res->Add()->CopyFrom(xattr);
      } else if (GetPrefixName(xattr) == std::string(kReplicaPolicyXAttr) ||
                 GetPrefixName(xattr) == std::string(kReplicaPolicyTypeXAttr) ||
                 GetPrefixName(xattr) == std::string(kReplicaPolicyDCXAttr) ||
                 GetPrefixName(xattr) == std::string(kReadPolicyXAttr)) {
        res->Add()->CopyFrom(xattr);
      }
    }
  }

  static Status UpdateINodeXAttrs(
      const XAttrProto &to_add, bool create_flag, bool replace_flag,
      INode* file) {
    // pre_condition: no duplicate xattr in file existed xattrs
    // equal means: namespace, name
    auto existing_xattrs = file->mutable_xattrs();
    auto iter = existing_xattrs->begin();
    while (iter != existing_xattrs->end()) {
      if (iter->namespace_() == to_add.namespace_()
          && iter->name() == to_add.name()) {
        if (replace_flag) {
          iter->set_value(to_add.value());
          return Status();
        } else {
          return Status{
              JavaExceptions::Exception::kIOException,
              Code::kError,
              "XAttr: " + iter->name()
                  + " already exists. The REPLACE flag must be specified."};
        }
      }
      iter++;
    }
    if (!create_flag) {
      return Status{
          JavaExceptions::Exception::kIOException,
          Code::kError,
          "XAttr: " + to_add.name()
              + " does not exist. The CREATE flag must be specified."};
    }
    file->add_xattrs()->CopyFrom(to_add);
    return Status();
  }

  static Status DeleteINodeXAttrs(
      const RepeatedPtrField<XAttrProto>& to_delete, INode* file) {
    CHECK_NOTNULL(file);
    if (to_delete.size() == 0) {
      return Status();
    }
    auto existing_xattrs = file->mutable_xattrs();
    for (uint32_t i = 0; i < existing_xattrs->size(); i++) {
      for (const auto& d : to_delete) {
        if (existing_xattrs->Get(i).namespace_() == d.namespace_() &&
            existing_xattrs->Get(i).name() == d.name()) {
          existing_xattrs->DeleteSubrange(i, 1);
          i--;
          break;
        }
      }
    }
    return Status();
  }

  static bool GetUInt32XAttr(const INode &inode,
                             const char* xattr_name,
                             uint32_t* value) {
    auto name = std::string(xattr_name);
    for (const auto& xattr : inode.xattrs()) {
      if (GetPrefixName(xattr) != name) {
        continue;
      }

      if (xattr.value().size() != sizeof(uint32_t)) {
        return false;
      }

      *value = platform::ReadBigEndian<uint32_t>(&(xattr.value()[0]), 0);
      return true;
    }
    return false;
  }

  static void SetUInt32XAttr(const char* xattr_name,
                             const uint32_t value,
                             INode* inode,
                             XAttrProto* xattr) {
    std::string data;
    data.resize(sizeof(uint32_t));
    platform::WriteBigEndian(&(data[0]), 0, value);

    BuildXAttr(xattr_name, data, xattr);

    UpdateINodeXAttrs(*xattr, true, true, inode);
  }

  static bool GetProtoBufXAttr(const INode &inode,
                               const char* xattr_name,
                               google::protobuf::Message* value,
                               bool requirePrefixMatch = true) {
    auto name = std::string(xattr_name);
    for (const auto& xattr : inode.xattrs()) {
      if (requirePrefixMatch && GetPrefixName(xattr) != name) {
        continue;
      } else if (!requirePrefixMatch && xattr.name() != name) {
        continue;
      }

      return value->ParseFromString(xattr.value());
    }
    return false;
  }

  static void SetProtoBufXAttr(const char* xattr_name,
                               const google::protobuf::Message* value,
                               INode* inode) {
    std::string data = value->SerializeAsString();

    XAttrProto xattr;
    BuildXAttr(xattr_name, data, &xattr);

    UpdateINodeXAttrs(xattr, true, true, inode);
  }

  static bool HasXAttr(const INode& inode,
                       const char* xattr_name,
                       bool requirePrefixMatch = true) {
    auto name = std::string(xattr_name);
    for (const auto& xattr : inode.xattrs()) {
      if (requirePrefixMatch && GetPrefixName(xattr) != name) {
        continue;
      } else if (!requirePrefixMatch && xattr.name() != name) {
        continue;
      }

      return true;
    }
    return false;
  }

  static void DeleteXAttr(const char* xattr_name,
                                INode* inode) {
    XAttrProto xattr;
    XAttrs::BuildXAttr(xattr_name, std::string(), &xattr);

    RepeatedPtrField<XAttrProto> to_delete;
    to_delete.Add()->CopyFrom(xattr);

    DeleteINodeXAttrs(to_delete, inode);
  }
};

}  // namespace dancenn

#endif  // NAMESPACE_XATTR_H_
