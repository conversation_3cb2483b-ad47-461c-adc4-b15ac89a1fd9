// Copyright 2019 <PERSON><PERSON><PERSON> <<EMAIL>>

#include "namespace/quota_collector.h"

#include <glog/logging.h>
#include <gflags/gflags.h>

#include <chrono>
#include <memory>
#include <string>
#include <ctime>

#include "inode.pb.h"  // NOLINT(build/include)
#include "xattr.pb.h"  // NOLINT(build/include)

#include "block_manager/block.h"
#include "namespace/namespace.h"
#include "base/redis_manager.h"
#include "base/path_util.h"
#include "base/file_utils.h"
#include "base/string_utils.h"

DECLARE_string(nameservice);
DECLARE_int32(dfs_quota_collect_interval_sec);
DECLARE_int32(dancenn_quota_redis_conn_retry_num);
DECLARE_string(dancenn_quota_redis_backends);
DECLARE_int32(dancenn_quota_redis_timeout_ms);
DECLARE_int32(dancenn_quota_redis_conn_retry_interval_sec);
DECLARE_string(dancenn_quota_redis_path2team_key);
DECLARE_string(dancenn_quota_redis_default_team_key);
DECLARE_string(dancenn_quota_redis_default_path_key);
DECLARE_string(dancenn_quota_redis_migrating_dir_key);
DECLARE_string(dancenn_quota_team_mapping_file);
DECLARE_string(dancenn_quota_redis_team_usage_key);
DECLARE_string(dancenn_quota_redis_path_usage_key);
DECLARE_string(dancenn_quota_redis_timestamp_key);
DECLARE_string(dancenn_quota_redis_storage_key);

using cloudfs::XAttrProto;

namespace dancenn {
static const char* kRedisStartFlag = ".REDIS_START_FLAG";
static const char* kRedisDoneFlag = ".REDIS_DONE_FLAG";

void QuotaCollector::SetupRedisManager(std::shared_ptr<IRedisManager> redis) {
  redis_ = redis;
}

void QuotaCollector::Start() {
  if (worker_) {
    worker_->Stop();
  }

  //init default value
  default_team_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_default_team_key;
  default_path_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_default_path_key;
  migrating_dirs_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_migrating_dir_key;
  team_usage_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_team_usage_key;
  path_usage_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_path_usage_key;
  timestamp_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_timestamp_key;
  storage_key_ = 
      FLAGS_nameservice + FLAGS_dancenn_quota_redis_storage_key;

  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("QuotaCollector");
  scan_success_count_ = metrics->RegisterCounter("ScanSuccessCount");
  scan_fail_count_ = metrics->RegisterCounter("ScanFailCount");

  worker_.reset(new cnetpp::concurrency::Thread(
      std::make_shared<Task>(this), "QuotaCollector"));
  worker_->Start();
  LOG(INFO) << "quota collector started";
}
void QuotaCollector::Stop() {
  if (worker_) {
    worker_->Stop();
    worker_.reset();
  }
  task_.reset();
  redis_->Stop();
  LOG(INFO) << "quota collector stopped";
}

void QuotaCollector::Task::Stop() {
  std::lock_guard<std::mutex> guard(mutex_);
  cnetpp::concurrency::Task::Stop();
  cond_.notify_all();
}

bool QuotaCollector::Task::operator()(void* arg) {
  (void) arg;
  std::unique_lock<std::mutex> guard(mutex_);
  while (!IsStopped()) {
    guard.unlock();
    scanner_->Scan(this);
    guard.lock();
    cond_.wait_for(guard,
                   std::chrono::seconds(FLAGS_dfs_quota_collect_interval_sec),
                   [&] () {
                     return IsStopped();
                   });
  }
  return true;
}

void QuotaCollector::Scan(Task* task) {
  LOG(INFO) << "start a new round of quota collector";
  // load path to team info from redis
  if(!LoadFromRedis()) {
    LOG(ERROR) << "load quota info from redis failed";
    MFC(scan_fail_count_)->Inc();
    return;
  }
  LOG(INFO) << "load quota info from redis success";

  // refresh mapping file
  if(!RefreshMappingFile()) {
    LOG(WARNING) << "refresh mapping file error";
  } else {
    LOG(INFO) << "refresh mapping file success";
  }

  // start to scan rocksdb
  LOG(INFO) << "Begin MScan for compute quota, interval_sec = " 
      << FLAGS_dfs_quota_collect_interval_sec;
  std::unordered_set<std::string> ssd_dirs;

  std::function<bool(const std::string&, const INode&, 
      const std::string&, const std::string&, bool, bool)> cb;
  cb = [&] (const std::string& full_path, const INode& inode,
      const std::string& quota_path, const std::string& quota_team,
      bool need_judge_trash, bool need_judge_mapping) {
    if (task->IsStopped()) {
      return false;
    } else {
      // skip trash path
      if (need_judge_trash) {
        std::vector<cnetpp::base::StringPiece> path_components;
        SplitPath(full_path, &path_components);
        if (path_components.size() > 0) {
          if (path_components[0] != "user") {
            need_judge_trash = false;
          } else if (path_components.size() > 2) {
            if (path_components[2] == ".Trash") {
               LOG(INFO) << "Skip trash path: " << full_path;
              return true;
            } else {
              need_judge_trash = false;
            }
          }
        }
      }
      if (inode.type() == INode_Type_kDirectory) {
        // skip migrating_dirs
        auto itr_migrating_dirs = migrating_dirs_.find(full_path);
        if (itr_migrating_dirs != migrating_dirs_.end()) {
          LOG(INFO) << "Skip migrating dirs: " << full_path;
          return true;
        }
        // find quota team and quota path
        std::string full_path_without_slash(full_path);
        if (full_path.length() > 1) {
          full_path_without_slash = full_path.substr(0, full_path.length() - 1);
        }
        std::string cur_quota_team("");
        std::string cur_quota_path(full_path_without_slash);
        auto itr_path_to_team = path_to_team_.find(full_path_without_slash);
        if (itr_path_to_team == path_to_team_.end()) {
          //get quota team according to mapping file
          if (need_judge_mapping) {
            std::vector<cnetpp::base::StringPiece> path_components;
            SplitPath(full_path, &path_components);
            if (path_components.size() > 0) {
              if (path_components[0] != "tmp" && path_components[0] != "user") {
                need_judge_mapping = false;
              } else if (path_components.size() > 2) {
                if (path_components[2] != "warehouse") {
                  need_judge_mapping = false;
                }
              }
            }
            if (need_judge_mapping) {
              for (auto& itr_pattern_team : pattern_team_mappings_) {
                if (full_path.find(itr_pattern_team.first)
                    != std::string::npos) {
                  cur_quota_team = itr_pattern_team.second;
                  LOG(INFO) << "Mapping " << full_path
                    << " to " << cur_quota_team;
                  break;
                }
              }
            }
          }
        } else {
          cur_quota_team = itr_path_to_team->second;
          if (inode.storage_policy_id() == kAllSSDStoragePolicy ||
              inode.storage_policy_id() == kOnlySSDStoragePolicy) {
            LOG(INFO) << "ssd path " << full_path_without_slash;
            ssd_dirs.emplace(full_path_without_slash);
          }
        }
        if (cur_quota_team.empty()) {
          cur_quota_team = quota_team;
          cur_quota_path = quota_path;
        }
        UpdateUsage(cur_quota_path, cur_quota_team, 0, 0, 0, 0);
        auto r = ns_->meta_storage_->ForEachDirComputeQuota(inode.id(),
            full_path, cur_quota_path, cur_quota_team, need_judge_trash,
            need_judge_mapping, cb);
        if (!r) {
          MFC(scan_fail_count_)->Inc();
        }
        return r;
      } else if (inode.type() == INode_Type_kFile) {
        int64_t sata = 0;
        int64_t sataUsage = 0;
        int64_t ssd = 0;
        int64_t ssdUsage = 0;
        int64_t file_size = ComputeFileSize(inode);
        float replication = ComputeReplication(inode);
        int64_t file_size_total = file_size * replication;
        int64_t size_usage = ComputeSizeUsage(file_size) * replication;
        if (inode.storage_policy_id() == kAllSSDStoragePolicy ||
            inode.storage_policy_id() == kOnlySSDStoragePolicy) { 
          ssd = file_size_total;
          ssdUsage = size_usage;
        } else {
          sata = file_size_total;
          sataUsage = size_usage;
        }
        UpdateUsage(quota_path, quota_team, sata, ssd, sataUsage, ssdUsage);
        VLOG(8) << "update usage quota_path[" << quota_path
            << "] quota_team[" << quota_team << "] sata[" << sata << "] ssd["
            << ssd << "] sataUsage[" << sataUsage << "] ssdUsage[" << ssdUsage
            << "] fullPath[" << full_path << "] node_id[" << inode.id() << "]";
      }
    }
    return true;
  };

  if (!ns_->meta_storage_->ForEachDirComputeQuota(kRootINodeId, "/",
      default_path_key_, default_team_key_, true, true, cb)) {
    MFC(scan_fail_count_)->Inc();
  }

  // ForEachDirComputeQuota could abort because task is stopped(process is
  // killed for example). In this case, current statistics in memory is not
  // complete and should not be dumped into redis.
  if (task->IsStopped()) {
    LOG(INFO) << "Quota collection is interrupted.";
    return;
  }

  ssd_dirs_.swap(ssd_dirs);
  LOG(INFO) << "MScan done for quota computing";

  // dump quota info to redis
  if (!DumpToRedis()) {
    LOG(ERROR) << "dump quota compute result to Redis failed";
    MFC(scan_fail_count_)->Inc();
    return;
  }
  LOG(INFO) << "dump quota compute result to Redis success";
  MFC(scan_success_count_)->Inc();

  return;
}

bool QuotaCollector::LoadFromRedis() {
  if (!ConnectRedis()) {
    LOG(WARNING) << "Failed to connect to redis";
    return false;
  }

  // get path2team from redis
  std::unordered_map<std::string, std::string> path_to_team;
  while (true) {
    std::unordered_map<std::string, std::string> r;
    if (!redis_->RedisHGetAll(FLAGS_dancenn_quota_redis_path2team_key, &r)) {
      LOG(ERROR) << "Failed to get path2team data from redis.";
      return false;
    }
    if (r.find(kRedisDoneFlag) == r.end() || r.find(kRedisStartFlag) == r.end()
        || r[kRedisDoneFlag] != r[kRedisStartFlag]) {
      LOG(INFO) << "Got incomplete redis content, sleep 1000ms and retry.";
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    } else {
      for (auto& rr : r) {
        if (rr.first == kRedisStartFlag || rr.first == kRedisDoneFlag) {
          continue;
        }
        std::string t = rr.first;
        RemoveRedundantSlash(&t);
        path_to_team.emplace(t, rr.second);
        DLOG(INFO) << "Load from Redis path2Team, path: " << t
            << " team:" << rr.second;
      }
      break;
    }
  }

  // Init path to usage mapping and team to usage mapping.
  std::unordered_map<std::string, FileUsage> path_usage;
  std::unordered_map<std::string, FileUsage> team_usage;
  for (auto& pt : path_to_team) {
    std::string path = pt.first;
    std::string team = pt.second;
    path_usage.emplace(path, FileUsage());
    team_usage.emplace(team, FileUsage());
  }
  path_usage.emplace(default_path_key_, FileUsage());
  team_usage.emplace(default_team_key_, FileUsage());

  // get migrating directories from redis
  std::unordered_set<std::string> migrating_dirs;
  if (!redis_->RedisSMembers(migrating_dirs_key_,
      &migrating_dirs)) {
    LOG(ERROR) << "Failed to get migrating_dirs from redis.";
    return false;
  }
  std::unordered_set<std::string> new_mds;
  std::string schema = "hdfs://";
  std::string prefix = schema + FLAGS_nameservice;
  for (auto& md : migrating_dirs) {
    if (md.empty()) {
      LOG(ERROR) << "Got empty migrating directory element";
      continue;
    }
    // Remove schema header and filter out path in other backend
    cnetpp::base::StringPiece md_piece(md);
    if (md_piece.starts_with(prefix)) {
      md_piece = md_piece.substr(prefix.length());
    } else {
      if (md_piece.starts_with(schema)) {
        LOG(WARNING) << "Invalid migrating dir: " << md;
        continue;
      }
    }
    // make sure every migrating path is ended with slash
    if (!md_piece.ends_with("/")) {
      new_mds.emplace(md_piece.as_string() + "/");
    } else {
      new_mds.emplace(md_piece.as_string());
    }
    LOG(INFO) << "migrate path: " << md_piece;
  }
  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    this->path_to_team_.swap(path_to_team);
    this->path_usage_.swap(path_usage);
    this->team_usage_.swap(team_usage);
    this->migrating_dirs_.swap(new_mds);
  }
  return true;
}

bool QuotaCollector::RefreshMappingFile() {
  if (!FileUtils::IsFile(FLAGS_dancenn_quota_team_mapping_file)) {
    LOG(ERROR) << "Mapping file: "
        << FLAGS_dancenn_quota_team_mapping_file
        << " doesn't exist or is invalid.";
    return false;
  }
  RandomAccessFile raf(FLAGS_dancenn_quota_team_mapping_file);
  auto size = raf.Size();
  if (size <= 0) {
    return false;
  }
  std::string buf(size, '\0');
  cnetpp::base::StringPiece result;
  if (!raf.Read(0, &(buf[0]), size, &result)) {
    LOG(ERROR) << "Failed to read enough data from mapping file: "
        << FLAGS_dancenn_quota_team_mapping_file << ".";
    return false;
  }
  cnetpp::base::Value mapping_config;
  if (!cnetpp::base::Parser::Deserialize(result.as_string(), &mapping_config)) {
    LOG(ERROR) << "Failed to parse json file: "
        << FLAGS_dancenn_quota_team_mapping_file;
    return false;
  }
  if (!mapping_config.IsArray()) {
    LOG(ERROR) << "json file is not expected format: "
        << FLAGS_dancenn_quota_team_mapping_file;
    return false;
  }
  auto entries = mapping_config.AsArray();
  std::vector<std::pair<std::string, std::string>> pattern_team_mappings;
  for (auto itr = entries.Begin(); itr != entries.End(); ++itr) {
    if (itr->IsObject()) {
      auto entry = itr->AsObject();
      auto team_param = entry.Find("team");
      auto pattern_str_param = entry.Find("patternString");
      if (team_param == entry.End() || pattern_str_param == entry.End()
          || !team_param->second.IsString()
          || !pattern_str_param->second.IsString()) {
        LOG(ERROR) << "Invalid parameter";
        return false;
      }
      auto team = team_param->second.AsString();
      auto pattern_str = pattern_str_param->second.AsString();
      LOG(INFO) << "Load mapping: " << team <<  " -> " << pattern_str;
      pattern_team_mappings.emplace_back(pattern_str, team);
    }
  }

  std::unique_lock<ReadWriteLock> guard(rw_lock_);
  pattern_team_mappings_.swap(pattern_team_mappings);
  return true;
}

bool QuotaCollector::DumpToRedis() {
  if (!ConnectRedis()) {
    LOG(WARNING) << "Failed to connect to redis";
    return false;
  }

  // dump path quota and team quota
  LOG(INFO) << "Start redis dump for path quota and team quota";
  for (auto& tu : team_usage_) {
    if (!redis_->RedisHSet(team_usage_key_, tu.first,
        tu.second.SerializeToJsonString())) {
      LOG(ERROR) << "dump team usage error, field: " << tu.first;
    }
  }
  for (auto& pu : path_usage_) {
    if (!redis_->RedisHSet(path_usage_key_, pu.first,
        pu.second.SerializeToJsonString())) {
      LOG(ERROR) << "dump path usage error, field: " << pu.first;
    }
  }

  // dump ssd path
  for (auto& sd : ssd_dirs_) {
    if (!redis_->RedisSAdd(storage_key_, sd)) {
      LOG(ERROR) << "dump ssd dir error, dir: " << sd;
    }
  }

  // dump timestamp
  time_t rawtime;
  struct tm timeinfo;
  char buffer[80];
  time(&rawtime);
  gmtime_r(&rawtime, &timeinfo);
  strftime(buffer, sizeof(buffer), "%a %b %d %H:%M:%S CST %Y", &timeinfo);
  std::string timestamp(buffer);
  if (!redis_->RedisSet(timestamp_key_, timestamp)) {
    LOG(ERROR) << "dump timestamp error";
  }

  return true;
}

bool QuotaCollector::ConnectRedis() {
  bool success = false;
  if (FLAGS_dancenn_quota_redis_backends.empty()) {
    LOG(ERROR) << "get dancenn quota redis backends empty";
    return success;
  }
  int retry = 0;
  while (retry < FLAGS_dancenn_quota_redis_conn_retry_num) {
    redis_->Stop();
    if (!redis_->Init(FLAGS_dancenn_quota_redis_backends,
                     FLAGS_dancenn_quota_redis_timeout_ms)) {
      retry++;
      LOG(WARNING) << "Failed to init redis, retry: " << retry;
      std::this_thread::sleep_for(std::chrono::seconds(
            FLAGS_dancenn_quota_redis_conn_retry_interval_sec));
      continue;
    } else {
      success = true;
      break;
    }
  }
  return success;
}

int64_t QuotaCollector::ComputeFileSize(const INode& inode) {
  if (inode.type() == INode_Type_kDirectory
      || inode.type() == INode_Type_kSymLink) {
    return 0;
  }
  if (inode.blocks_size() == 0) {
    return 0;
  }

  int64_t size = 0;
  for (uint32_t i = 0; i < inode.blocks_size(); ++i) {
    size += inode.blocks(i).numbytes();
  }
  return size;
}

int64_t QuotaCollector::ComputeSizeUsage(const int64_t& size) {
  const int64_t MEGA = 1024 * 1024;
  if (size < 128 * MEGA) {
    return 128 * MEGA;
  } else if (size < 256 * MEGA) {
    return 256 * MEGA;
  } else {
    return size;
  }
}

float QuotaCollector::ComputeReplication(const INode& inode) {
  if (inode.replication() != 0) {
    return inode.replication();
  }
  for (int i = 0; i < inode.xattrs_size(); ++i) {
    if (inode.xattrs(i).namespace_()
      == XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_TRUSTED // NOLINT(whitespace/line_length)
      && inode.xattrs(i).name() == "ec" ) {
      if (inode.xattrs(i).has_value()) {
        std::string v = inode.xattrs(i).value();
        if (v.size() == 1) {
          uint8_t value = *reinterpret_cast<const uint8_t*>(v.data());
          uint8_t n = (value & 0xf8) >> 3;
          uint8_t m = value & 0x7;
          if (n != 0) {
            return float(n + m) / float(n);
          } else {
            LOG(ERROR) << "EC value invalid, n:" << unsigned(n) << " m:"
                << unsigned(m);
          }
        } else {
            LOG(ERROR) << "value size invalid, size:" << v.size() << " value:"
                << v;
        }
      } else {
        LOG(ERROR) << "xattr ec has no value";
      }
      break;
    }
  }
  return inode.replication();
}

void QuotaCollector::UpdateUsage(const std::string& quota_path,
    const std::string& quota_team, const int64_t& sata, const int64_t& ssd,
    const int64_t& sata_usage, const int64_t& ssd_usage) {
  FileUsage file_usage(sata, ssd, sata_usage, ssd_usage, 1);
  auto tu = team_usage_.find(quota_team);
  if (tu == team_usage_.end()) {
    team_usage_.emplace(quota_team, file_usage);
  } else {
    tu->second.AddUsage(file_usage);
  }
  auto pu = path_usage_.find(quota_path);
  if (pu == path_usage_.end()) {
    path_usage_.emplace(quota_path, file_usage);
  } else {
    pu->second.AddUsage(file_usage);
  }
}

}  // namespace dancenn

