//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

// System
#include <chrono>
#include <thread>
#include <unordered_map>
#include <unordered_set>

// Third
#include <absl/strings/str_format.h>

// Project
#include "base/path_util.h"
#include "base/time_util.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"
#include "namespace/namespace_scrub.h"
#include "namespace/namespace_scrub_inodestat.h"
#include "namespace/namespace_scrub_lifecycle_scan.h"
#include "namespace/namespace_scrub_pin.h"
#include "namespace/meta_storage_constants.h"

DECLARE_bool(meta_storage_snapshot_read_enabled);
DECLARE_int32(scrub_list_subdir_batch_size);
DECLARE_uint32(scrub_wait_for_done_sleep_ms);

namespace dancenn {

std::atomic<int32_t> ScrubTask::object_count_{0};
std::atomic<int32_t> DirScrubInfo::object_count_{0};

int32_t GetConcurrency() {
  int32_t cpu_limit = std::thread::hardware_concurrency();
  do {
    int64_t quota_us;
    FILE *quota_fp = fopen("/sys/fs/cgroup/cpu/cpu.cfs_quota_us", "r");
    if (!quota_fp) {
      break;
    }
    DEFER([quota_fp]() { fclose(quota_fp); });
    int64_t period_us;
    FILE *period_fp = fopen("/sys/fs/cgroup/cpu/cpu.cfs_period_us", "r");
    if (!period_fp) {
      break;
    }
    DEFER([period_fp]() { fclose(period_fp); });
    if (!(fscanf(quota_fp, "%ld", &quota_us) == 1 &&
          fscanf(period_fp, "%ld", &period_us) == 1 &&
          quota_us > 0 && period_us > 0)) {
      break;
    }
    if (quota_us / period_us != 0) {
      cpu_limit = quota_us / period_us;
    }
  } while (0);
  auto concur = cpu_limit / 4;
  return static_cast<int32_t>(concur ? concur : 1);
}

int64_t NowMs() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

DirScrubInfo::DirScrubInfo(const INode& node, const DirScrubInfoPtr& parent,
                           ScrubTask* task, ScrubOpPtr op,
                           const std::shared_ptr<MetaStorage>& storage)
    : node_(node), parent_(parent), task_(task), op_(op), meta_storage_(storage) {
  iter_holder_ = meta_storage_->GetIterator();
  op_->BindINode(node);

  auto count = DirScrubInfo::object_count_.fetch_add(1);
  VLOG(12) << "DirScrubInfo +1. count: " << count + 1;
}

DirScrubInfo::~DirScrubInfo() {
  auto count = DirScrubInfo::object_count_.fetch_sub(1);
  VLOG(12) << "DirScrubInfo -1. count: " << count - 1;
}

// Return false: no more subdirs
bool DirScrubInfo::GetNextSubDir(INode* dir,
                                 std::function<bool()> interrupted) {
  if (!subdirs_.empty()) {
    *dir = subdirs_.front();
    subdirs_.pop_front();
    return true;
  }

  if (has_more_subdir_) {
    LoadChildren(interrupted);
    if (!subdirs_.empty()) {
      *dir = subdirs_.front();
      subdirs_.pop_front();
      return true;
    }
  }

  return false;
}

void DirScrubInfo::RemoveConcurrentSubDirTask(
    const ScrubTaskPtr& subdir_task) {
  SpinLockGuard lg(lock_);
  uint64_t id = subdir_task->GetINode().id();
  concurrent_subdir_tasks_.erase(id);
  if (VLOG_IS_ON(12)) {
    VLOG(12) << "SubDir task removed. dir: " << node_.id()
             << ", subdir_id: " << subdir_task->GetINode().id()
             << ", task inode: " << task_->GetINode().id();
  }
}

void DirScrubInfo::AddConcurrentSubDirTask(
    const ScrubTaskPtr& subdir_task) {
  SpinLockGuard lg(lock_);
  uint64_t id = subdir_task->GetINode().id();
  CHECK(concurrent_subdir_tasks_.find(id) == concurrent_subdir_tasks_.end());
  concurrent_subdir_tasks_[id] = subdir_task;
  if (VLOG_IS_ON(12)) {
    VLOG(12) << "SubDir task added. dir: " << node_.id()
             << ", subdir_id: " << subdir_task->GetINode().id()
             << ", task inode: " << task_->GetINode().id();
  }
}

bool DirScrubInfo::IsFinished() {
  SpinLockGuard lg(lock_);
  return (!has_more_subdir_) && (concurrent_subdir_tasks_.size() == 0);
}

void DirScrubInfo::FinishScrub() {
  // 1. process current directory, after all children finished
  op_->ProcessDir(node_);

  // 2. handle scrub action
  auto action = task_->GetScrub()->Action();
  CHECK(action == SCRUB_ACTION_CHECK ||
        action == SCRUB_ACTION_FORCE_OVERWRITE);
  auto st = op_->HandleDirResult(action);
  if (UNLIKELY(!st.IsOK())) {
    task_->GetScrub()->StopOnError("Failed to scrub inode " +
        std::to_string(node_.id()) + ", " + st.message());
  } else {
    VLOG(12) << "Scrub finished for inode " + std::to_string(node_.id());
  }

  // 3. accumulate result to parent
  if (nullptr != parent_) {
    parent_->AddSubDirOp(GetResult());
  }

  task_->GetScrub()->IncreaseFinishedDirCount(subfile_count_);
}

void DirScrubInfo::NotifySubDirTaskFinished(uint64_t subdir_id) {
  {
    SpinLockGuard lg(lock_);
    auto iter = concurrent_subdir_tasks_.find(subdir_id);
    CHECK(iter != concurrent_subdir_tasks_.end());
  }

  if (VLOG_IS_ON(12)) {
    VLOG(12) << "SubDir task finished. dir: " << node_.id()
             << ", subdir_id: " << subdir_id
             << ", task inode: " << task_->GetINode().id();
  }
  task_->FinishSubDirTaskInDirScrubInfo(shared_from_this(), subdir_id);
}

void DirScrubInfo::FinishSubDirTask(uint64_t subdir_id) {
  SpinLockGuard lg(lock_);
  concurrent_subdir_tasks_.erase(subdir_id);
  if (VLOG_IS_ON(12)) {
    VLOG(12) << "SubDir task removed. dir: " << node_.id()
             << ", subdir_id: " << subdir_id
             << ", task inode: " << task_->GetINode().id();
  }
}

void DirScrubInfo::AddSubDirOp(const ScrubOpPtr subdir_op) {
  SpinLockGuard lg(lock_);
  op_->AddSubDirResult(subdir_op);
}

size_t DirScrubInfo::SubDirTasksCount() {
  SpinLockGuard lg(lock_);
  return concurrent_subdir_tasks_.size();
}

ScrubOpPtr DirScrubInfo::GetResult() {
  SpinLockGuard lg(lock_);
  return op_;
}

void DirScrubInfo::LoadChildren(std::function<bool()> interrupted) {
  CHECK(node_.type() == INode_Type_kDirectory);
  if (!has_more_subdir_) {
    return;
  }

  // Get next batch of inodes, and accumulate stat
  bool has_more = false;
  meta_storage_->GetSubINodes(
      node_.id(), last_child_,
      [this, &interrupted, &has_more](const INode& child) -> bool {
        if (interrupted()) {
          return false;
        }
        if (child.type() == INode_Type_kFile) {
          SpinLockGuard lg(lock_);
          op_->ProcessFile(child);
          ++subfile_count_;
        } else {
          CHECK(child.type() == INode_Type_kDirectory);
          subdirs_.emplace_back(child);
          if (subdirs_.size() >= FLAGS_scrub_list_subdir_batch_size) {
            has_more = true;
            return false;
          }
        }
        return true;
      },
      iter_holder_->iter());
  if (subdirs_.empty()) {
    last_child_.clear();
  } else {
    last_child_ = subdirs_.back().name();
  }
  if (!has_more) {
    iter_holder_.reset(nullptr);
  }
  has_more_subdir_ = has_more;
}

ScrubTask::ScrubTask(const INode& node, const DirScrubInfoPtr& parent,
                     NameSpaceScrub* scrub, ScrubOpPtr op)
    : node_(node), parent_scrub_info_(parent), scrub_(scrub), op_(op) {
  auto new_op = op_->NewOp();
  namespace_ = scrub_->GetNameSpace();
  meta_storage_ = scrub_->GetMetaStorage();
  new_op->Init(namespace_, meta_storage_);
  info_ = std::make_shared<DirScrubInfo>(node, parent, this, new_op,
                                         meta_storage_);
  stack_.push(info_);

  auto count = ScrubTask::object_count_.fetch_add(1);
  VLOG(12) << "ScrubTask +1. count: " << count + 1;
}

ScrubTask::~ScrubTask() {
  auto count = ScrubTask::object_count_.fetch_sub(1);
  VLOG(12) << "ScrubTask -1. count: " << count + 1;
}

bool ScrubTask::operator()(void* arg) {
  DoScrub();
  scrub_->FinishTask();
  return true;
}

bool ScrubTask::ShouldStop() {
  // Stop from Task flag
  if (IsStopped()) {
    LOG(INFO) << "Task is stopped from Task flag. inode: " << node_.id();
    return true;
  }
  // Stop from Scrub flag
  if (scrub_->ShouldStop()) {
    LOG(INFO) << "Task is stopped from Scrub flag. inode: " << node_.id();
    return true;
  }
  return false;
}

void ScrubTask::DoScrub() {
  if (ShouldStop()) {
    SetState(SCRUB_TASK_STATE_ABORTED);
    LOG(INFO) << "Scrub task is stopped, exit. inode: " << node_.id();
    return;
  }

  if (GetState() == SCRUB_TASK_STATE_FINISHED) {
    LOG(ERROR) << "Task already finished. id: " << node_.id();
    return;
  }

  if (GetState() == SCRUB_TASK_STATE_INIT) {
    CHECK(FLAGS_meta_storage_snapshot_read_enabled);
    SetState(SCRUB_TASK_STATE_RUNNING);
    start_timestamp_ms_ = NowMs();
    VLOG(10) << "Traverse starting. task inode: " << node_.id();
  }

  CHECK_EQ(SCRUB_TASK_STATE_RUNNING, GetState());
  auto result = Traverse();
  bool task_finished = false;
  switch (result) {
  case TRAVERSE_DONE:
    task_finished = true;
    break;
  case TRAVERSE_ABORTED: {
    task_finished = true;
    while (pending_child_tasks_count_.load() != 0) {
      LOG(INFO) << "Waiting for child tasks to abort. task inode: "
                << node_.id();
      std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    break;
  }
  default:
    LOG(FATAL) << "Invalid traverse result: " << result;
  }
  VLOG(10)  << "Traverse finished: " << task_finished
            << ". task inode: " << node_.id() << ", state: " << GetState()
            << ", result: " << result;
  if (task_finished) {
    if (nullptr != parent_scrub_info_) {
      // Notify parent
      parent_scrub_info_->NotifySubDirTaskFinished(node_.id());
    }
    finished_timestamp_ms_ = NowMs();
  }
}

ScrubTask::TraverseResult ScrubTask::Traverse() {
  uint64_t task_dir_id = node_.id();
  while (!stack_.empty()) {
    if (ShouldStop()) {
      LOG(INFO) << "Scrub task is stopped, exit. inode: " << node_.id();
      SetState(SCRUB_TASK_STATE_ABORTED);
      return TRAVERSE_ABORTED;
    }

    auto&& cur_dir_info = stack_.top();
    uint64_t cur_dir_id = cur_dir_info->GetINode().id();

    VLOG(12) << "Traverse. task inode: " << task_dir_id
             << ", process inode info: " << cur_dir_id;

    INode first_next_subdir;
    bool finish_current_dir = false;
    // Get first next child; 获取接下来的第一个 Child
    {
      bool succ = cur_dir_info->GetNextSubDir(
          &first_next_subdir, std::bind(&ScrubTask::ShouldStop, this));
      if (succ) {
        if (VLOG_IS_ON(12)) {
          VLOG(12) << "Traverse. task inode: " << task_dir_id
                   << ", process inode info: " << cur_dir_id
                   << ", first next subdir: " << first_next_subdir.id();
        }
      } else {
        // No more children to process
        finish_current_dir = true;
        if (VLOG_IS_ON(12)) {
          VLOG(12) << "Traverse. task inode: " << task_dir_id
                   << ", process inode info: " << cur_dir_id
                   << ", no more child to process.";
        }
      }
    }

    if (!finish_current_dir) {
      // Try submit next subdirs to pool
      // Iterate through subdirs
      while (true) {
        if (ShouldStop()) {
          LOG(INFO) << "Scrub task is stopped, exit. inode: " << task_dir_id;
          SetState(SCRUB_TASK_STATE_ABORTED);
          return TRAVERSE_ABORTED;
        }

        INode subdir;
        bool succ = cur_dir_info->GetNextSubDir(
            &subdir, std::bind(&ScrubTask::ShouldStop, this));
        if (succ) {
          if (VLOG_IS_ON(12)) {
            VLOG(12) << "Traverse. task inode: " << task_dir_id
                     << ", process inode info: " << cur_dir_id
                     << ", next subdir: " << subdir.id();
          }
          // Try submit to pool first
          // If failed, traverse in current thread
          auto subdir_task =
              std::make_shared<ScrubTask>(subdir, cur_dir_info, scrub_, op_);
          cur_dir_info->AddConcurrentSubDirTask(subdir_task);
          uint32_t pending_count = pending_child_tasks_count_.fetch_add(1);
          bool submitted = scrub_->TrySubmitTask(subdir_task);
          if (submitted) {
            // Current subdir is submitted to running concurrently
            // Get next subdir
            if (VLOG_IS_ON(12)) {
              VLOG(12) << "Traverse. task inode: " << task_dir_id
                       << ", process inode info: " << cur_dir_id
                       << ", next subdir: " << subdir.id()
                       << ". submit to pool successfully. "
                       << " pending: " << (pending_count + 1);
            }
            continue;
          } else {
            // Push the subdir back to info's subdir list
            cur_dir_info->RemoveConcurrentSubDirTask(subdir_task);
            cur_dir_info->PushFrontSubDir(subdir);
            pending_child_tasks_count_.fetch_sub(1);
            if (VLOG_IS_ON(12)) {
              VLOG(12) << "Traverse. task inode: " << task_dir_id
                       << ", process inode info: " << cur_dir_id
                       << ", next subdir: " << subdir.id()
                       << ". Failed to submit to pool and push it back. ";
            }
            break;
          }
        } else {
          if (VLOG_IS_ON(12)) {
            VLOG(12) << "Traverse. task inode: " << task_dir_id
                     << ", process inode info: " << cur_dir_id
                     << ", no more child subdir to process.";
          }
          break;
        }
      }
    }

    if (finish_current_dir) {
      // No more subdirs to process
      // Check and wait for concurrent subdir tasks to finish
      int64_t sleep_count = 0;
      while (true) {
        bool finished = false;
        {
          SpinLockGuard lg(state_lock_);
          finished = cur_dir_info->IsFinished();
        }
        if (finished) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        ++sleep_count;
        if (sleep_count % 500 == 0) {
          LOG(INFO) << "Traverse. task inode: " << task_dir_id
                    << ", process inode info: " << cur_dir_id
                    << ", waiting for subdir tasks to finish.";
        }
      }

      SpinLockGuard lg(state_lock_);
      if (VLOG_IS_ON(12)) {
        VLOG(12) << "Traverse. task inode: " << task_dir_id
                 << ", process inode info: " << cur_dir_id
                 << ", finished and pop from stack.";
      }
      // Finish scrub for current subdir, pop it out
      cur_dir_info->FinishScrub();
      stack_.pop();

    } else {
      // Push first_subdir into stack, process it in next loop
      auto subdir_op = op_->NewOp();
      subdir_op->Init(namespace_, meta_storage_);
      auto first_next_subdir_info = std::make_shared<DirScrubInfo>(
          first_next_subdir, cur_dir_info, this, subdir_op, meta_storage_);
      stack_.push(first_next_subdir_info);
      if (VLOG_IS_ON(12)) {
        VLOG(12) << "Traverse. task inode: " << task_dir_id
                 << ", process inode info: " << cur_dir_id
                 << ", push inode to stack: " << first_next_subdir.id();
      }
    }
  }

  // All done
  SetState(SCRUB_TASK_STATE_FINISHED);
  return TRAVERSE_DONE;
}

void ScrubTask::FinishSubDirTaskInDirScrubInfo(
    const DirScrubInfoPtr& dir_info, uint64_t subdir_id) {
  do {
    SpinLockGuard lg(state_lock_);
    dir_info->FinishSubDirTask(subdir_id);
  } while (0);
  uint32_t pending_count = pending_child_tasks_count_.fetch_sub(1);
  if (VLOG_IS_ON(12)) {
    VLOG(12) << "Subdir Task removed from task. task inode: " << node_.id()
             << ", dir_info: " << dir_info->GetINode().id()
             << ", subdir: " << subdir_id
             << " pending: " << (pending_count - 1);
  }
}

ScrubOpPtr ScrubTask::GetResult() { return info_->GetResult(); }

int32_t ScrubTask::GetObjectCount() {
  return ScrubTask::object_count_.load();
}

int32_t DirScrubInfo::GetObjectCount() {
  return DirScrubInfo::object_count_.load();
}

NameSpaceScrub::NameSpaceScrub(const INode& root,
                               ScrubOpType optype,
                               ScrubAction action,
                               NameSpace* ns,
                               const std::shared_ptr<MetaStorage>& meta_storage)
    : root_(root), action_(action), namespace_(ns), meta_storage_(meta_storage) {
  scrub_concurrency_ = GetConcurrency();
  switch (optype) {
    case SCRUB_OPTYPE_INODE_STAT: {
      op_ = std::make_shared<INodeStatScrubOp>();
      break;
    }
    case SCRUB_OPTYPE_LIFECYCLE_SCAN : {
      op_ = std::make_shared<LifecycleScanScrubOp>();
      break;
    }
    case SCRUB_OPTYPE_PIN: {
      op_ = std::make_shared<PinScrubOp>();
      break;
    }
    default: {
      LOG(FATAL) << "Invalid scrub operation type " + std::to_string(optype);
    }
  }

  op_->Init(ns, meta_storage_);
}

void NameSpaceScrub::Start() {
  // TODO(xuex)
  // maybe we should avoid multiple scrubs running simultaneously
  // using global mutex

  start_timestamp_ms_ = NowMs();
  LOG(INFO) << "NameSpaceScrub started. id: " << root_.id()
            << ", concur: " << scrub_concurrency_
            << ", timestamp: " << start_timestamp_ms_;

  StartThreads(scrub_concurrency_);

  op_->PreScrub(Action());

  {
    SpinLockGuard lg(state_lock_);
    state_ = SCRUB_STATE_RUNNING;
  }

  root_task_ = std::make_shared<ScrubTask>(root_, nullptr, this, op_);
  ForceSubmitTask(root_task_);
}

void NameSpaceScrub::Stop() {
  {
    SpinLockGuard lg(state_lock_);
    if (SCRUB_STATE_RUNNING != state_) {
      LOG(ERROR) << "Scrub not in running state, ignore Stop call.";
      return;
    }
    state_ = SCRUB_STATE_STOP;
    error_msg_ = "Scrub is stopped because of caller request.";
  }

  LOG(INFO) << "Stopping worker pool.";
  // MUST wait all task in pool to be finished; otherwise the sub task counter
  // might not be decreased correctly
  worker_pool_->Stop(true);

  LOG(INFO) << "Scrub stopped.";
}

void NameSpaceScrub::StopOnError(const std::string& msg) {
  SpinLockGuard lg(state_lock_);
  if (SCRUB_STATE_RUNNING != state_) {
    LOG(INFO) << "Scrub not in running state, ignore StopOnError call. msg: "
              << msg;
    return;
  }
  state_ = SCRUB_STATE_ERROR;
  error_msg_ = msg;
  LOG(ERROR) << "Scrub stop on error: " << msg;
  // TODO(xuex) leave worker-threads alone? may be bug
}

bool NameSpaceScrub::WaitForDone(int timeout_sec) {
  uint32_t sleep_ms = FLAGS_scrub_wait_for_done_sleep_ms;
  bool finished = false;
  int sleep_cnt = 0;
  while (timeout_sec < 0 || sleep_cnt < timeout_sec) {
    if (root_task_->IsFinished()) {
      finished = true;
      break;
    }

    sleep_cnt++;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_ms));
    if (sleep_cnt % 10 == 0) {
      VLOG(12) << "NameSpaceScrub finished dir count: "
               << finished_dirs_num_.load();
    }
  }
  if (!finished) {
    return false;
  }

  op_->PostScrub(Action());
  LOG(INFO) << "NameSpaceScrub finished. id: " << root_.id()
            << ", timestamp: " << NowMs();
  state_ = SCRUB_STATE_FINISHED;
  return true;
}

NameSpaceScrub::State NameSpaceScrub::GetState() {
  SpinLockGuard lg(state_lock_);
  return state_;
}
bool NameSpaceScrub::ShouldStop() {
  SpinLockGuard lg(state_lock_);
  return SCRUB_STATE_RUNNING != state_;
}

bool NameSpaceScrub::TrySubmitTask(const ScrubTaskPtr& task) {
  SpinLockGuard lg(submit_lock_);
  if (running_tasks_count_ >= scrub_concurrency_) {
    return false;
  }
  ++running_tasks_count_;

  bool succ = worker_pool_->AddTask(
      std::static_pointer_cast<cnetpp::concurrency::Task>(task));
  if (!succ) {
    LOG(ERROR) << "Failed to submit scrub task. This won't happen because "
                  "we've controlled the running tasks count.";
    return false;
  }
  return true;
}

void NameSpaceScrub::ForceSubmitTask(const ScrubTaskPtr& task) {
  SpinLockGuard lg(submit_lock_);
  ++running_tasks_count_;

  bool succ = worker_pool_->AddTask(
      std::static_pointer_cast<cnetpp::concurrency::Task>(task));
  if (!succ) {
    LOG(FATAL) << "Failed to submit scrub task. This won't happen because "
                  "we've controlled the pending queue should hold the element.";
    return;
  }
}

void NameSpaceScrub::FinishTask() {
  SpinLockGuard lg(submit_lock_);
  --running_tasks_count_;
}

ScrubOpPtr NameSpaceScrub::GetResult() {
  return root_task_->GetResult();
}

Status NameSpaceScrub::GetProgress(ScrubProgress* p) {
  ScrubProgress progress;
  progress.start_ms = start_timestamp_ms_;
  progress.finished_dirs = finished_dirs_num_.load();
  progress.finished_inodes = finished_inodes_num_.load();

  int64_t elapsed_ms = NowMs() - start_timestamp_ms_;
  progress.estimated_time_remain_seconds =
      op_->EstimateRemainingSeconds(progress.finished_inodes, elapsed_ms);
  *p = progress;
  return Status::OK();
}

Status NameSpaceScrub::GetResult(ScrubResult* r) {
  if (nullptr == root_task_) {
    return Status(Code::kScrubNotRunning, "Scrub task not started.");
  }
  if (!root_task_->IsFinished()) {
    return Status(Code::kScrubRunning,
                  "Cannot get result while scrub task is running.");
  }

  ScrubResult result;
  result.start_ms = start_timestamp_ms_;
  result.finished_ms = root_task_->GetFinishTimestampMs();
  result.result = GetResult();
  if (root_task_->GetState() ==
      ScrubTask::State::SCRUB_TASK_STATE_FINISHED) {
    result.status = Status::OK();
  } else {
    result.status = Status(Code::kScrubFailed,
                           absl::StrFormat("Stat scrub failed. error: %s", error_msg_));
  }

  *r = result;
  return Status::OK();
}

void NameSpaceScrub::StartThreads(uint32_t concur) {
  // driver_thread_ = std::make_shared<Thread>(
  //     std::bind(&NameSpaceScrub::DriverThread, this),
  //     "NameSpaceScrub-Driver");
  // driver_thread_->Start();

  worker_pool_ = std::make_shared<ThreadPool>("NameSpaceScrub-Worker");
  worker_pool_->set_max_num_pending_tasks(4 * concur);
  worker_pool_->set_num_threads(2 * concur);
  worker_pool_->Start();

  LOG(INFO) << "NameSpaceScrub driver and worker threads started.";
}

void NameSpaceScrubRunner::Start() {
  std::lock_guard<std::mutex> lg(thread_lock_);
  CHECK(nullptr == thread_);
  CHECK(RUNNER_STATE_INIT == state_.load());

  auto thread = std::make_shared<Thread>(
      std::bind(&NameSpaceScrubRunner::ScrubRunThread, this));
  thread->Start();
  LOG(INFO) << "Wait for scrub runner thread start.";

  auto start_time = TimeUtil::GetNowEpochMs();
  auto last_log_time = start_time;
  while (RUNNER_STATE_RUNNING != state_.load()) {
    sched_yield();
    auto now = TimeUtil::GetNowEpochMs();
    if (now - start_time >= 10 * 1000) {
      LOG(FATAL) << "Scrub runner thread failed to start in 10 seconds...";
      return;
    } else if (now - last_log_time >= 1 * 1000) {
      last_log_time = now;
      LOG(INFO) << "Still waiting for scrub runner thread start.";
    }
  }

  thread_ = thread;
  LOG(INFO) << "Scrub runner thread started.";
}

void NameSpaceScrubRunner::Stop() {
  std::lock_guard<std::mutex> lg(thread_lock_);
  CHECK(nullptr != thread_);
  CHECK(RUNNER_STATE_RUNNING == state_.load());

  state_.store(RUNNER_STATE_STOPPING);

  for (int i = 0; i < SCRUB_OPTYPE_NUM; i++) {
    auto& ctx = scrub_ctx_[i];
    std::lock_guard<std::mutex> lg(lock_);
    if (nullptr != ctx.scrub_) {
      ctx.scrub_->Stop();
    }
  }

  auto start_time = TimeUtil::GetNowEpochMs();
  auto last_log_time = start_time;
  while (RUNNER_STATE_STOPPED != state_.load()) {
    sched_yield();
    auto now = TimeUtil::GetNowEpochMs();
    if (now - start_time >= 60 * 1000) {
      LOG(FATAL) << "Scrub runner thread failed to stop in 60 seconds...";
      return;
    } else if (now - last_log_time >= 10 * 1000) {
      last_log_time = now;
      LOG(INFO) << "Still waiting for scrub runner thread to stop.";
    }
  }

  thread_->Stop();
  thread_.reset();
}

Status
NameSpaceScrubRunner::StartScrub(ScrubOpType optype,
                                 ScrubAction action,
                                 const INode& root,
                                 NameSpace* ns,
                                 const std::shared_ptr<MetaStorage>& store) {
  {
    std::lock_guard<std::mutex> lg(lock_);
    auto& ctx = scrub_ctx_[optype];
    if (nullptr != ctx.scrub_req_) {
      return Status(Code::kScrubRunning,
                    "Scrub already running. Please stop it before start "
                    "another scrub.");
    }

    ctx.scrub_req_ = std::make_shared<ScrubReq>(optype, action, root, ns, store);
    scrub_cond_.notify_all();
  }
  LOG(INFO) << "Scrub started. optype: " << optype << ", action: " << action
            << ", root: " << root.id();
  return Status::OK();
}

Status NameSpaceScrubRunner::StopScrub(ScrubOpType optype) {
  std::lock_guard<std::mutex> lg(lock_);
  auto& ctx = scrub_ctx_[optype];
  if (nullptr == ctx.scrub_req_) {
    return Status(Code::kScrubNotRunning, "Scrub not running.");
  }
  if (nullptr != ctx.scrub_) {
    ctx.scrub_->Stop();
    ctx.scrub_.reset();
  }
  ctx.scrub_req_.reset();
  LOG(INFO) << "Scrub " << optype << "stopped by caller.";
  return Status::OK();
}

Status NameSpaceScrubRunner::GetScrubProgress(ScrubOpType optype,
                                              ScrubProgress* progress) {
  std::lock_guard<std::mutex> lg(lock_);
  auto& ctx = scrub_ctx_[optype];
  if (nullptr == ctx.scrub_req_) {
    return Status(Code::kScrubNotRunning, "Scrub not running.");
  }
  if (nullptr != ctx.scrub_) {
    return ctx.scrub_->GetProgress(progress);
  }
  *progress = ScrubProgress();
  return Status::OK();
}

Status NameSpaceScrubRunner::GetScrubResult(ScrubOpType optype, ScrubResult* result) {
  std::lock_guard<std::mutex> lg(lock_);
  auto& ctx = scrub_ctx_[optype];
  if (nullptr != ctx.scrub_req_) {
    return Status(Code::kScrubRunning,
                  "Scrub is running, cannot get result.");
  }
  *result = ctx.last_result_;
  return Status::OK();
}

bool NameSpaceScrubRunner::IsRunning(ScrubOpType optype) {
  std::lock_guard<std::mutex> lg(lock_);
  auto& ctx = scrub_ctx_[optype];
  return nullptr != ctx.scrub_;
}

bool NameSpaceScrubRunner::ScrubRunThread() {
  state_.store(RUNNER_STATE_RUNNING);

  bool all_scrub_completed = true;
  while (RUNNER_STATE_RUNNING == state_.load() || !all_scrub_completed) {

    all_scrub_completed = true;
    for (int idx = 0; idx < SCRUB_OPTYPE_NUM; idx++) {
      auto& ctx = scrub_ctx_[idx];

      std::unique_lock<std::mutex> lg(lock_);
      if (nullptr == ctx.scrub_req_) {
        // case 1: no scrub staged
        scrub_cond_.wait_for(lg, std::chrono::milliseconds(10));
        continue;
      }

      auto& req = ctx.scrub_req_;
      CHECK_EQ(idx, req->optype);
      if (nullptr == ctx.scrub_) {
        // case 2: new scrub request, submit a new scrub procedure
        auto scrub = std::make_shared<NameSpaceScrub>(
            req->root, req->optype, req->action, req->ns, req->store);
        LOG(INFO) << "Start to run scrub " << req->optype
                  << ". timestamp: " << NowMs();
        scrub->Start();
        ctx.scrub_ = scrub;
      } else {
        // case 3: scurb procedure inprogress
      }
      CHECK_NOTNULL(ctx.scrub_req_);
      CHECK_NOTNULL(ctx.scrub_);

      // try to get result of scrub
      auto s = ctx.scrub_->GetResult(&ctx.last_result_);
      if (!s.IsOK()) {
        if (s.code() != Code::kScrubRunning) {
          LOG(FATAL) << "Unexpected scrub status" << s.ToString();
        }
        all_scrub_completed = false;
      } else {
        ctx.scrub_req_.reset();
        ctx.scrub_.reset();
        LOG(INFO) << "Finished scrub " << idx << ". timestamp: " << NowMs();
      }
    }
  }

  state_.store(RUNNER_STATE_STOPPED);
  LOG(INFO) << "Runner thread stopped.";
  return true;
}

Status NameSpace::StartScrub(ScrubOpType optype, ScrubAction action) {
  INode root = GetRootINode();
  return scrub_runner_->StartScrub(optype, action, root, this, meta_storage_);
}

Status NameSpace::StopScrub(ScrubOpType optype) {
  return scrub_runner_->StopScrub(optype);
}

Status NameSpace::GetScrubProgress(ScrubOpType optype, ScrubProgress* progress) {
  return scrub_runner_->GetScrubProgress(optype, progress);
}

Status NameSpace::GetScrubResult(ScrubOpType optype, ScrubResult* result) {
  return scrub_runner_->GetScrubResult(optype, result);
}

} // namespace dancenn
