// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef NAMESPACE_ACCESS_COUNTER_MANAGER_H_
#define NAMESPACE_ACCESS_COUNTER_MANAGER_H_

#include <string>
#include <unordered_map>
#include <map>
#include <memory>

#include "base/read_write_lock.h"
#include "namespace/access_counter.h"
#include "datanode_manager/data_centers.h"

namespace dancenn {

class NameSpace;
class DataCenters;

class AccessCounterManager {
 public:
  AccessCounterManager(NameSpace* ns, DataCenters* dcs);

  std::shared_ptr<AccessCounter> Get(const std::string& path);

  void IncreaseAccessCounter(const std::string& path, DataCenter dc,
      uint64_t step);

  std::map<DataCenter, double> GetAccessCounterValues(const std::string& path);

  void EmitSnapshot(std::shared_ptr<AccessCounter> counter);

  void ApplySnapshot(
      const cloudfs::fsimage
      ::AccessCounterSection_AccessCounterSnapshotProto& snapshot);  //
      // NOLINT(whitespace/line_length)

  // for unit test
  const std::unordered_map<std::string, std::shared_ptr<AccessCounter>>& counter_map() const {  // NOLINT(whitespace/line_length)
    return counter_map_;
  }

 private:
  std::shared_ptr<AccessCounter> GetOrCreate(const std::string& path,
      std::shared_ptr<AccessCounter> parent);

  NameSpace* ns_;
  DataCenters* dcs_;

  ReadWriteLock rwlock_;
  std::unordered_map<std::string, std::shared_ptr<AccessCounter>> counter_map_;
};

}  // namespace dancenn

#endif  // NAMESPACE_ACCESS_COUNTER_MANAGER_H_

