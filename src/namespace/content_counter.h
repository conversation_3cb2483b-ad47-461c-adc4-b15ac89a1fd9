// Copyright 2018 Panfeng Ran <<EMAIL>>

#ifndef NAMESPACE_CONTENT_COUNTER_H_
#define NAMESPACE_CONTENT_COUNTER_H_

#include <cstdint>
#include <cstring>

namespace dancenn {
class ContentCounter {
 public:
  enum class Type : int32_t {
    // The number of files.
    FILE,
    // The number of directories.
    DIRECTORY,
    // The number of symlinks.
    SYMLINK,
    // The total of file length in bytes.
    LENGTH,
    // The total of disk space usage in bytes including replication.
    DISKSPACE,
    // The number of snapshots.
    SNAPSHOT,
    // The number of snapshottable directories.
    SNAPSHOTTABLE_DIRECTORY,
    // dummy value for get #entries of ContentType.
    CONTENT_TYPE_SIZE
  };

  typedef uint64_t ValueType;

  ContentCounter() {
    bzero(counters_, sizeof counters_);
  }

  ValueType& operator[](Type t) {
    return counters_[static_cast<int>(t)];
  }

 private:
  ValueType counters_[static_cast<int>(Type::CONTENT_TYPE_SIZE)];
};
}  // namespace dancenn
#endif  // NAMESPACE_CONTENT_COUNTER_H_
