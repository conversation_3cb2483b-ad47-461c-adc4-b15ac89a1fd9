//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include "namespace_usage_reporter.h"

// Third
#include <cnetpp/http/http_callbacks.h>
#include <cnetpp/http/http_client.h>
#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>
#include <curl/curl.h>

#include "base/count_down_latch.h"
#include "base/logger_metrics.h"
#include "base/time_util.h"
#include "namespace/namespace.h"

DECLARE_int64(namespace_id);
DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(usage_service_endpoint);
DECLARE_int32(usage_report_interval_seconds);

static const char *REPORT_USAGE_DETAIL_URI = "/namespaceusage/detail";

namespace dancenn {

using HttpConnectionPtr = std::shared_ptr<cnetpp::http::HttpConnection>;

Status NamespaceUsageReporter::Start() {

  if (FLAGS_usage_service_endpoint.empty()) {
    return Status(Code::kBadParameter,
                  "Invalid usage_service_endpoint config.");
  }

  auto thread = std::make_shared<Thread>(
      std::bind(&NamespaceUsageReporter::ReporterRunThread, this),
      "USAGE_REPORTER_THREAD");
  thread->Start();
  LOG(INFO) << "Wait for reporter thread to start.";

  int i = 0;
  while (state_.load() != REPORTER_STATE_RUNNING) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ++i;
    if (i > 100) {
      LOG(ERROR) << "Failed to start report thread in 10 seconds.";
      return Status(Code::kUsageError,
                    "Failed to start report thread in 10 seconds.");
    }
  }
  LOG(INFO) << "Reporter thread started successfully.";
  thread_ = thread;
  return Status::OK();
}

void NamespaceUsageReporter::Stop() {
  if (state_.load() != REPORTER_STATE_RUNNING) {
    LOG(INFO) << "Reporter thread not running.";
    return;
  }

  state_.store(REPORTER_STATE_STOPPING);
  LOG(INFO) << "Wait for reporter thread to stop. usage_service_endpoint: "
            << FLAGS_usage_service_endpoint
            << ", report interval: " << FLAGS_usage_report_interval_seconds;
  int i = 0;
  while (state_.load() != REPORTER_STATE_FINISHED) {
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ++i;
    if (i > 10) {
      LOG(ERROR) << "Failed to stop report thread in 10 seconds. Ignore it.";
      return;
    }
  }
  thread_->Stop();
  thread_.reset();
  LOG(INFO) << "Reporter thread stopped successfully.";
}

bool NamespaceUsageReporter::ReporterRunThread() {
  state_.store(REPORTER_STATE_RUNNING);

  if (FLAGS_usage_service_endpoint.back() == '/') {
    url_ = FLAGS_usage_service_endpoint + "/" + REPORT_USAGE_DETAIL_URI;
  } else {
    url_ = FLAGS_usage_service_endpoint + REPORT_USAGE_DETAIL_URI;
  }
  LOG(INFO) << "Usage report url: " << url_;

  int32_t interval_sec = FLAGS_usage_report_interval_seconds;

  uint64_t last_report = 0;
  while (state_ == REPORTER_STATE_RUNNING) {
    uint64_t now = TimeUtil::GetNowEpochMs() / 1000;
    if (now - last_report > interval_sec) {
      // Do report
      auto s = ReportUsage();
      if (!s.IsOK()) {
        LOG_WITH_LEVEL(ERROR) << "Failed to report usage. retry in 30 seconds. error : "
                   << s.ToString();
        // Emit warning
        MFC(LoggerMetrics::Instance().error_)->Inc();
        interval_sec = 30;
      } else {
        interval_sec = FLAGS_usage_report_interval_seconds;
      }
      last_report = TimeUtil::GetNowEpochMs() / 1000;
    }

    // Only sleep 1 seconds for fast stop
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }

  state_.store(REPORTER_STATE_FINISHED);
  return true;
}

Status NamespaceUsageReporter::ReportUsage() {
  INodeStat stat;
  auto s = ns_->GetDirectoryStat("/", &stat);
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to get directory stat: " << s.ToString();
    return s;
  }

  LOG(INFO) << "Will post stat to usage service: " << stat.ToString();

  uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
  static const char *BODY_TMPL =
      R"({"ns_id": %ld, "ts": %lu, "size_bytes": %ld, "inode_count": %ld, "block_count": %ld, "file_count": %ld, "dir_count": %ld, "env": "%s", "region": "%s"})";
  char buf[1024];
  int len =
      snprintf(buf, sizeof(buf), BODY_TMPL, FLAGS_namespace_id, now_sec,
               stat.data_size, stat.inode_num, stat.block_num, stat.file_num,
               stat.dir_num, FLAGS_cfs_env.c_str(), FLAGS_cfs_region.c_str());
  if (len <= 0 || len >= sizeof(buf)) {
    LOG(ERROR) << "Failed to generate body content. len: " << len;
    return Status(Code::kUsageError, "Failed to generate body content.");
  }

  cnetpp::base::StringPiece body(buf, len);
  LOG(INFO) << "Will post stat to usage service. body: [" << buf << "]";
  return PostToUsageService(body);
}

static size_t CurlUsageServiceResponseBodyCallback(void *contents, size_t size,
                                                   size_t nmemb,
                                                   std::string *s) {
  size_t len = size * nmemb;
  try {
    s->append((char *)contents, len);
  } catch (const std::bad_alloc &e) {
    LOG(FATAL) << "Failed to alloc memory for usage report.";
    return 0;
  }
  return len;
}

Status
NamespaceUsageReporter::PostToUsageService(cnetpp::base::StringPiece &body) {
  auto handle = curl_easy_init();
  if (!handle) {
    LOG(ERROR) << "Failed to init curl.";
    return Status(Code::kError, "Failed to init curl");
  }

  curl_easy_setopt(handle, CURLOPT_URL, url_.c_str());

  curl_easy_setopt(handle, CURLOPT_POSTFIELDS, body.data());

  struct curl_slist *headers = NULL;
  headers = curl_slist_append(headers, "Accept: application/json");
  headers = curl_slist_append(headers, "Content-Type: application/json");
  curl_easy_setopt(handle, CURLOPT_HTTPHEADER, headers);
  curl_easy_setopt(handle, CURLOPT_VERBOSE, 1L);

  std::string rsp_body;
  curl_easy_setopt(handle, CURLOPT_WRITEFUNCTION,
                   CurlUsageServiceResponseBodyCallback);
  curl_easy_setopt(handle, CURLOPT_WRITEDATA, &rsp_body);

  Status s;
  do {
    CURLcode res = curl_easy_perform(handle);
    if (res != CURLE_OK) {
      LOG(ERROR) << "curl_easy_perform failed. error: "
                 << curl_easy_strerror(res);
      s = Status(Code::kUsageError, "curl_easy_perform failed.");
      break;
    }

    int64_t http_code = 0;
    res = curl_easy_getinfo(handle, CURLINFO_RESPONSE_CODE, &http_code);
    if (res != CURLE_OK || http_code != 200) {
      LOG(ERROR)
          << "Failed to get success http code from usage service. error: "
          << curl_easy_strerror(res) << ", http_code: " << http_code;
      s = Status(Code::kUsageError,
                 "Failed to get success http code from usage service.");
      break;
    }

    if (rsp_body.empty()) {
      LOG(ERROR) << "Failed to receive response body from usage service.";
      s = Status(Code::kUsageError,
                 "Failed to receive response body from usage service.");
      break;
    }

    s = CheckPostResult(rsp_body);
  } while (0);

  curl_easy_cleanup(handle);
  curl_slist_free_all(headers);
  return s;
}

Status NamespaceUsageReporter::CheckPostResult(const std::string &body) {

  cnetpp::base::Value v;
  if (!cnetpp::base::Parser::Deserialize(body, &v)) {
    LOG(INFO) << "Unable to Deserialize post result: " << body;
    return Status(Code::kUsageError, "Failed to Deserialize post result.");
  }

  // Has responseMeta && has error.
  auto vobj = v.AsObject();
  auto it = vobj.Find("status");
  if (it == vobj.End()) {
    LOG(INFO) << "Invalid post result, cannot find status: " << body;
    return Status(Code::kUsageError, "Invalid post result, cannot find status");
  }
  std::string status = it->second.AsString();
  if (status == "OK") {
    return Status::OK();
  }

  it = vobj.Find("message");
  if (it == vobj.End()) {
    LOG(INFO) << "Invalid post result, cannot find message on error: " << body;
    return Status(
        Code::kUsageError,
        "Invalid post result, cannot find message on error. status: " + status);
  }
  std::string message = it->second.AsString();
  return Status(Code::kUsageError, "Failed to post usage. service rsp: " +
                                       status + ", message: " + message);
}

} // namespace dancenn
