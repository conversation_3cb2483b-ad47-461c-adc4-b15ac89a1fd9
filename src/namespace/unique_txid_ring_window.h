//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef NAMESPACE_UNIQUE_TXID_RING_WINDOW_H_
#define NAMESPACE_UNIQUE_TXID_RING_WINDOW_H_

#include <stdint.h>

#include <condition_variable>
#include <mutex>
#include <shared_mutex>
#include <vector>

namespace dancenn {
namespace meta_storage {

class WriteTask;
class UniqueTxidRingWindow {
 public:
  UniqueTxidRingWindow(int n);
  ~UniqueTxidRingWindow();

  bool TryPush(int64_t txid, meta_storage::WriteTask* task);
  void Push(int64_t txid, meta_storage::WriteTask* task);
  void Pop(int n, std::vector<meta_storage::WriteTask*>* tasks);

  void NextApplyTxid(int64_t txid);
  int64_t NextApplyTxid() const {
    return next_apply_txid_;
  }

 private:
  void TryPop(int n, std::vector<meta_storage::WriteTask*>* tasks);

 private:
  volatile int64_t next_apply_txid_;
  char padding_[64 - sizeof(next_apply_txid_)];
  uint32_t capacity_;
  meta_storage::WriteTask** data_{nullptr};
  std::mutex m_;
  std::condition_variable cond_;
};

}  // namespace meta_storage
}  // namespace dancenn

#endif
