// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/05/23
// Description

#ifndef NAMESPACE_FILE_FINALIZER_H_
#define NAMESPACE_FILE_FINALIZER_H_

#include <proto/generated/dancenn/inode.pb.h>  // For INode, FileUnderConstructionFeature.

#include <string>  // For string.

#include "base/closure.h"  // For RpcClosure, ClosureGuard.
#include "base/metrics.h"  // For MetricID, MetricsCenter, MFC.
#include "base/status.h"   // For Status, Code.
#include "base/stop_watch.h"
#include "block_manager/bip_write_manager.h"  // For BIPWriteManagerBase, etc.
#include "block_manager/block.h"          // For Block, BlockID, IsBlockIDValid.
#include "block_manager/block_manager.h"  // For BlockManager.
#include "edit/sender_base.h"             // For EditLogSenderBase.
#include "lease/lease_manager_base.h"     // For LeaseManagerBase.
#include "namespace/meta_storage.h"       // For MetaStorage.
#include "ufs/ufs_env.h"

namespace dancenn {

class FileFinalizerBase {
 public:
  // TODO
  // // We utilize specific types with
  // // disabled implicit type conversion constructors
  // // to provide callers with a clearer understanding
  // // of the meaning of the parameters.
  // struct LastCommittedBlock {
  //   explicit LastCommittedBlock(const Block& block) : val(block) {
  //   }
  //   Block val;
  // };
  // struct AbandonedBlkID {
  //   explicit AbandonedBlkID(BlockID id) : val(id) {
  //   }
  //   BlockID val;
  // };

 public:
  virtual ~FileFinalizerBase() = default;

  virtual void Start(NameSpace* name_space,
                     BlockManager* block_manager,
                     BIPWriteManagerBase* bip_write_manager,
                     LeaseManagerBase* lease_manager,
                     EditLogSenderBase* edit_log_sender,
                     MetaStorage* meta_storage,
                     UfsEnv* ufs_env) = 0;
  virtual void Stop() = 0;

  // The caller of FinalizeFile() should ensure that:
  // 1. Start() has already been called.
  // 2. Stop() has not yet been called.
  //
  // @abandoned_blk_id
  // The last block id of inode must be
  // abandoned_blk_id if abandoned_blk_id is valid.
  virtual Status FinalizeFile(
      const std::string& src,
      const Block& last_committed_blk,
      BlockID abandoned_blk_id,
      INodeInPath* iip,
      const INode& old_inode,
      const std::vector<INode>& ancestors,
      RpcClosure* rpc_done,
      std::shared_ptr<StopWatchContext> rpc_sw_ctx = nullptr) = 0;
};

class FileFinalizerMetrics {
 public:
  FileFinalizerMetrics();

 public:
  MetricID finalize_file_lm_release_lease_time_;
  MetricID finalize_file_write_editlog_time_;
  MetricID finalize_file_write_meta_storage_time_;
  MetricID finalize_file_wait_editlog_and_ms_time_;
  MetricID finalize_file_callback_commit_bip_time_;
  MetricID finalize_file_callback_remove_lease_time_;
  MetricID finalize_file_callback_check_replica_time_;
  MetricID finalize_file_callback_trigger_upload_time_;
};

class FileFinalizer : public FileFinalizerBase {
 public:
  FileFinalizer() = default;

  void Start(NameSpace* name_space,
             BlockManager* block_manager,
             BIPWriteManagerBase* bip_write_manager,
             LeaseManagerBase* lease_manager,
             EditLogSenderBase* edit_log_sender,
             MetaStorage* meta_storage,
             UfsEnv* ufs_env) override;
  void Stop() override;

  Status FinalizeFile(
      const std::string& src,
      const Block& last_committed_blk,
      // The last block id of inode must be
      // abandoned_blk_id if abandoned_blk_id is valid.
      BlockID abandoned_blk_id,
      INodeInPath* iip,
      const INode& old_inode,
      const std::vector<INode>& ancestors,
      RpcClosure* rpc_done,
      std::shared_ptr<StopWatchContext> rpc_sw_ctx = nullptr) override;

  // Update inode only.
  static Status UpdateFileToFinalize(const std::string& src,
                                     const Block& last_committed_blk,
                                     BlockID abandoned_blk_id,
                                     INode* inode);

  static std::string LogBipMismatch(const BlockInfoProto& old_bip,
                                    const BlockProto& bp,
                                    const char* file,
                                    uint32_t line);

 private:
  // Copy from NameSpace.
  inline void SetServerTxid(RpcClosure* rpc_done, int64_t txid);
  inline void SetServerTxid(RpcController* ctx, int64_t txid);

 private:
  FileFinalizerMetrics metrics_;
  NameSpace* name_space_{nullptr};
  BlockManager* block_manager_{nullptr};
  BIPWriteManagerBase* bip_write_manager_{nullptr};
  LeaseManagerBase* lease_manager_{nullptr};
  EditLogSenderBase* edit_log_sender_{nullptr};
  MetaStorage* meta_storage_{nullptr};
  UfsEnv* ufs_env_{nullptr};
};

}  // namespace dancenn

#endif  // NAMESPACE_FILE_FINALIZER_H_
