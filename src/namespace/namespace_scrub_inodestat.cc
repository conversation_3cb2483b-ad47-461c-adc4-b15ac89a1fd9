#include "namespace/namespace_scrub_inodestat.h"

#include <absl/strings/str_format.h>

#include "base/time_util.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace_stat.h"

namespace dancenn {

void INodeStatScrubOp::PreScrub(ScrubAction action) {
  auto s =
      meta_storage_->GetDirectoryINodeStat(node_.id(), &stat_before_scrub_);
  if (s.IsOK()) {
    LOG(INFO) << absl::StrFormat(
        "Root stat before scrub: %s",
        stat_before_scrub_.ToString());
  } else {
    LOG(WARNING) << absl::StrFormat(
        "Failed to load root stat before scrub. error: %s",
        s.To<PERSON>tring());
  }
}

void INodeStatScrubOp::ProcessFile(const INode& file) {
  int64_t size = 0;
  int64_t block_num = 0;
  INodeStatUtils::GetFileSizeAndBlockNum(file, &size, &block_num);

  computed_stat_.inode_num += 1;
  computed_stat_.file_num += 1;
  computed_stat_.data_size += size;
  computed_stat_.block_num += block_num;
  computed_stat_.txid = -1;
  computed_stat_.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
}

Status INodeStatScrubOp::CheckStat() {
  INodeStat persisted_stat;
  auto s = meta_storage_->GetDirectoryINodeStat(node_.id(), &persisted_stat);
  if (UNLIKELY(!s.IsOK())) {
    std::string msg = absl::StrFormat(
        "INodeStat is corrupted on inode %lu, err: %s",
        node_.id(), s.ToString());
    LOG(ERROR) << msg;
    return Status(s.code(), msg);
  }
  if (UNLIKELY(persisted_stat != computed_stat_)) {
    std::string msg = absl::StrFormat(
        "INodeStat is corrupted on inode %lu, computed_stat: %s, "
        "persisted_stat: %s",
        node_.id(), computed_stat_.ToString(), persisted_stat.ToString());
    LOG(ERROR) << msg;
    return Status(Code::kINodeStatCorrupt, msg);
  }

  return Status();
}

Status INodeStatScrubOp::ForceOverwriteStat() {
  computed_stat_.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
  auto s = meta_storage_->PutDirectoryINodeStat(node_, computed_stat_);
  CHECK(s.IsOK()) << absl::StrFormat(
      "Failed to write INodeStat for inode: %lu", node_.id());
  return Status();
}

Status INodeStatScrubOp::HandleDirResult(ScrubAction action) {
  Status st;
  switch (action) {
    case SCRUB_ACTION_CHECK: {
      st = CheckStat();
      CHECK(st.IsOK());
      break;
    }
    case SCRUB_ACTION_FORCE_OVERWRITE: {
      st = ForceOverwriteStat();
      break;
    }
    default: {
      std::string msg = absl::StrFormat(
          "HandleDirResult encountered unexpected action %d",
          static_cast<int>(action));
      return Status(Code::kError, msg);
    }
  }
  return st;
}

void INodeStatScrubOp::AddSubDirResult(const ScrubOpPtr subdir_op) {
  auto& subdir_stat =
    std::dynamic_pointer_cast<INodeStatScrubOp>(subdir_op)->computed_stat_;

  computed_stat_.inode_num += 1 + subdir_stat.inode_num;
  computed_stat_.file_num += subdir_stat.file_num;
  computed_stat_.dir_num += 1 + subdir_stat.dir_num;
  computed_stat_.data_size += subdir_stat.data_size;
  computed_stat_.block_num += subdir_stat.block_num;
  computed_stat_.txid = -1;
  computed_stat_.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
}

int64_t INodeStatScrubOp::EstimateRemainingSeconds(int64_t finished_inodes,
                                                   int64_t elapsed_ms) {
  int64_t est = -1;
  if (stat_before_scrub_.inode_id != 0 && elapsed_ms > 1000 &&
      stat_before_scrub_.inode_num >= finished_inodes) {
    double speed =
        finished_inodes / (1.0 * elapsed_ms / 1000); // inodes/second
    est = static_cast<int64_t>(
        (stat_before_scrub_.inode_num - finished_inodes) / speed);
  }
  return est;
}

std::string INodeStatScrubOp::ToString() {
  return computed_stat_.ToString();
};

ScrubOpPtr INodeStatScrubOp::NewOp() {
  return std::make_shared<INodeStatScrubOp>();
}

INodeStat INodeStatScrubOp::GetComputedStat() {
  return computed_stat_;
}

}
