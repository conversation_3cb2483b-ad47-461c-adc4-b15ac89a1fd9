// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef NAMESPACE_NAMESPACE_H_
#define NAMESPACE_NAMESPACE_H_

#include <aws/s3/S3Client.h>
#include <cnetpp/base/ip_address.h>
#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <status_code.pb.h>

#include <atomic>
#include <condition_variable>
#include <cstddef>
#include <cstdint>
#include <cstdlib>
#include <deque>
#include <list>
#include <map>
#include <memory>
#include <mutex>
#include <set>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "ClientNamenodeProtocol.pb.h"
#include "HAServiceProtocol.pb.h" // NOLINT(build/include)

// Project
#include "acc/acc.h"
#include "base/constants.h"
#include "base/hyper_file_utils.h"
#include "base/lru_cache.h"
#include "base/network_location_info.h"
#include "base/platform.h"
#include "base/rwlock_manager.h"
#include "base/status.h"
#include "base/stop_watch.h"
#include "base/throttler.h"
#include "base/timer_task_manager.h"
#include "block_manager/bip_write_manager.h"
#include "block_manager/block_info.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_report_handler.h"
#include "bridger/socket_wrapper.h"
#include "datanode_manager/data_centers.h"
#include "datanode_manager/datanode_manager.h"
#include "datanode_manager/storage_policy.h"
#include "edit/edit_log_cfs_op.h"
#include "edit/edit_log_context.h"
#include "edit/op/op_access_counter_snapshot.h"
#include "edit/op/op_add.h"
#include "edit/op/op_add_block.h"
#include "edit/op/op_allocate_block_id.h"
#include "edit/op/op_close.h"
#include "edit/op/op_concat_delete.h"
#include "edit/op/op_delete.h"
#include "edit/op/op_delete_deprecated_block_pufs_info.h"
#include "edit/op/op_mkdir.h"
#include "edit/op/op_reassign_lease.h"
#include "edit/op/op_remove_xattr.h"
#include "edit/op/op_rename.h"
#include "edit/op/op_rename_old.h"
#include "edit/op/op_set_acl.h"
#include "edit/op/op_set_block_pufs_info.h"
#include "edit/op/op_set_cfs_universal_info.h"
#include "edit/op/op_set_dir_replica_policy.h"
#include "edit/op/op_set_genstamp_v1.h"
#include "edit/op/op_set_genstamp_v2.h"
#include "edit/op/op_set_owner.h"
#include "edit/op/op_set_permissions.h"
#include "edit/op/op_set_replica_policy.h"
#include "edit/op/op_set_replication.h"
#include "edit/op/op_set_storage_policy.h"
#include "edit/op/op_set_xattr.h"
#include "edit/op/op_symlink.h"
#include "edit/op/op_times.h"
#include "edit/op/op_update_blocks.h"
#include "edit/sender_base.h"
#include "hdfs.pb.h"  // NOLINT(build/include)
#include "job_manager/job_manager_def.h"
#include "job_manager/task_creator.h"
#include "lease/lease_manager.h"
#include "lease/lease_manager_base.h"
#include "lease/lease_monitor.h"
#include "lifecycle.pb.h"
#include "namespace/access_counter_manager.h"
#include "namespace/az_monitor.h"
#include "namespace/checkpointer.h"
#include "namespace/content_counter.h"
#include "namespace/file_finalizer.h"
#include "namespace/hyperfile_scanner_listener.h"
#include "namespace/inode.h"
#include "namespace/inode_with_blocks.h"
#include "namespace/lifecycle_scanner.h"
#include "namespace/merge_block_listener.h"
#include "namespace/meta_scanner_v2.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace_http.h"
#include "namespace/namespace_metrics.h"
#include "namespace/namespace_scrub_inodestat.h"
#include "namespace/namespace_stat_checker.h"
#include "namespace/namespace_stat_delta_cache.h"
#include "namespace/permission.h"
#include "namespace/permission_checker.h"
#include "namespace/policy_manager.h"
#include "namespace/quota_collector.h"
#include "namespace/ttl_atime_collector.h"
#include "namespace/user_group_info.h"
#include "namespace/xattr.h"
#include "ranger.pb.h"
#include "security/block_token_secret_manager.h"
#include "security/key_manager.h"
#include "snapshot/snapshot_manager.h"
#include "ufs/ufs.h"
#include "ufs/ufs_env.h"
#include "ufs/upload/ufs_noupload_manager.h"

using cloudfs::AbandonBlockRequestProto;
using cloudfs::AbandonBlockResponseProto;
using cloudfs::AddBlockRequestProto;
using cloudfs::AddBlockResponseProto;
using cloudfs::AppendRequestProto;
using cloudfs::AppendResponseProto;
using cloudfs::BatchCompleteFileRequestProto;
using cloudfs::BatchCompleteFileResponseProto;
using cloudfs::BatchCreateFileRequestProto;
using cloudfs::BatchCreateFileResponseProto;
using cloudfs::BatchDeleteFileRequestProto;
using cloudfs::BatchDeleteFileResponseProto;
using cloudfs::BatchGetFileRequestProto;
using cloudfs::BatchGetFileResponseProto;
using cloudfs::BlockProto;
using cloudfs::CheckAccessRequestProto;
using cloudfs::CheckAccessResponseProto;
using cloudfs::CommitLastBlockRequestProto;
using cloudfs::CommitLastBlockResponseProto;
using cloudfs::CompleteRequestProto;
using cloudfs::CompleteResponseProto;
using cloudfs::ContentSummaryProto;
using cloudfs::CreateRequestProto;
using cloudfs::CreateResponseProto;
using cloudfs::CreateSymlinkRequestProto;
using cloudfs::CreateSymlinkResponseProto;
using cloudfs::DeleteRequestProto;
using cloudfs::DeleteResponseProto;
using cloudfs::DirectoryListingProto;
using cloudfs::ExtendedBlockProto;
using cloudfs::FsyncRequestProto;
using cloudfs::FsyncResponseProto;
using cloudfs::GetAdditionalDatanodeRequestProto;
using cloudfs::GetAdditionalDatanodeResponseProto;
using cloudfs::GetBlockKeysResponseProto;
using cloudfs::GetBlockLocationsRequestProto;
using cloudfs::GetBlockLocationsResponseProto;
using cloudfs::GetContentSummaryRequestProto;
using cloudfs::GetContentSummaryResponseProto;
using cloudfs::GetDistributedRequestProto;
using cloudfs::GetDistributedResponseProto;
using cloudfs::GetFileInfoResponseProto;
using cloudfs::GetFsStatsResponseProto;
using cloudfs::GetHyperBlockLocationsRequestProto;
using cloudfs::GetHyperBlockLocationsResponseProto;
using cloudfs::GetLifecyclePolicyRequestProto;
using cloudfs::GetLifecyclePolicyResponseProto;
using cloudfs::GetListingRequestProto;
using cloudfs::GetListingResponseProto;
using cloudfs::GetPreferredBlockSizeResponseProto;
using cloudfs::GetReadPolicyResponseProto;
using cloudfs::GetReplicaPolicyResponseProto;
using cloudfs::GetStoragePoliciesResponseProto;
using cloudfs::HAServiceStateResponseProto;
using cloudfs::HdfsFileStatusProto;
using cloudfs::IsFileClosedResponseProto;
using cloudfs::ListReadPoliciesResponseProto;
using cloudfs::LocatedBlockProto;
using cloudfs::LocatedBlocksProto;
using cloudfs::LocatedHyperBlockProto;
using cloudfs::MsyncResponseProto;
using cloudfs::MkdirsRequestProto;
using cloudfs::MkdirsResponseProto;
using cloudfs::MsyncResponseProto;
using cloudfs::NamespaceInfoProto;
using cloudfs::RecoverLeaseRequestProto;
using cloudfs::RecoverLeaseResponseProto;
using cloudfs::RemoveXAttrRequestProto;
using cloudfs::RemoveXAttrResponseProto;
using cloudfs::Rename2RequestProto;
using cloudfs::Rename2ResponseProto;
using cloudfs::RenameRequestProto;
using cloudfs::RenameResponseProto;
using cloudfs::SetLifecyclePolicyRequestProto;
using cloudfs::SetLifecyclePolicyResponseProto;
using cloudfs::SetTimesRequestProto;
using cloudfs::SetXAttrRequestProto;
using cloudfs::SetXAttrResponseProto;
using cloudfs::StorageInfoProto;
using cloudfs::TokenProto;
using cloudfs::UnsetLifecyclePolicyRequestProto;
using cloudfs::UnsetLifecyclePolicyResponseProto;
using cloudfs::UpdateBlockForPipelineRequestProto;
using cloudfs::UpdateBlockForPipelineResponseProto;
using cloudfs::UpdatePipelineRequestProto;
using cloudfs::UpdatePipelineResponseProto;
using cloudfs::XAttrProto;
using RecyclePolicy = cloudfs::RecyclePolicyProto;
using StorageClass = cloudfs::StorageClassProto;
using ExpirationRule = cloudfs::ExpirationRuleProto;
using TransitionRule = cloudfs::TransitionRuleProto;
using LifecyclePolicy = cloudfs::LifecyclePolicyProto;
using StorageClassStatProto = cloudfs::StorageClassStatProto;
using HyperCacheMeta = cloudfs::HyperCacheMetaProto;
using DirectoryListingPtr = cloudfs::DirectoryListingProto*;
using FileStatusPtr = ::cloudfs::HdfsFileStatusProto*;
using StringPiece = cnetpp::base::StringPiece;
using StringPieces = std::vector<StringPiece>;
using cloudfs::CancelJobRequestProto;
using cloudfs::CancelJobResponseProto;
using cloudfs::FreeRequestProto;
using cloudfs::FreeResponseProto;
using cloudfs::LoadRequestProto;
using cloudfs::LoadResponseProto;
using cloudfs::LookupJobRequestProto;
using cloudfs::LookupJobResponseProto;
using cloudfs::PinRequestProto;
using cloudfs::PinResponseProto;

#define METRIC_WATCH_START(name) \
  StopWatch name(metrics_.name); \
  name.Start();
#define METRIC_WATCH_STOP(name)                                       \
  name.Stop();                                                        \
  MFH(metrics_.name)                                                  \
      ->Update(std::chrono::duration_cast<std::chrono::microseconds>( \
                   name.GetTime())                                    \
                   .count());
#define RETURN_SET_CLOSURE_IF_ERROR(_status_expr, __rpc_done) \
  do {                                                        \
    auto&& _status = (_status_expr);                          \
    if (UNLIKELY(!_status.IsOK())) {                          \
      (__rpc_done)->set_status(Status(std::move(_status)));   \
      return;                                                 \
    }                                                         \
  } while (0)

namespace dancenn {

class HAStateBase;
class SafeModeBase;
class NameSpace;
class FsPermissionChecker;
class LeaseScannerListener;
class RecycleScanner;
class PolicyManager;
class SlowExecutorPool;
class Checkpointer;
class MockFSImageTransfer;
class MockNameSpace;
class UploadProcessTest;
class INodeStatChangeRecorder;
class NameSpaceScrubRunner;
class NamespaceUsageReporter;
class QuotaManagerWrapper;
class ActiveStandbySyncEditlogTest;
class FileSyncContext;
class FileListingSyncContext;
class RenameSyncContext;
class DeleteSyncContext;
class FileFinalizerBase;

class SyncEngine;
class Ufs;
class UfsEnv;
class UfsUploadMonitor;
class UfsUploadingManager;
class UfsNouploadManager;
class UfsConfig;
class UfsCopyResult;
class PersistentUfsInfo;
class UfsFileStatus;
class WriteBackManager;
struct ListFilesOption;
struct ListFilesResult;
struct UfsDirSyncActions;
class LockedPathBase;
class RichPath;
class LockedPath;
class LockedPathVector;
class ListingSyncTask;
class INodeSyncChecker;
class JobManager;
class TaskCreator;

class BGDeletionTask;
class SnapshotGcTask;

typedef std::function<void (const Status &)> SyncCallback;

Status ComputeFileSyncActionUfsFileOnly(const INode* local_file,
                                        const UfsFileStatus* ufs_file,
                                        FileSyncAction* action,
                                        NameSpaceMetrics* metrics = nullptr);

enum class ApplyMode {
  LOGICAL = 0,
  PHYSICAL_SEQ = 1,
  PHYSICAL_CONCUR = 2,
  NUM_MODES = 3,
};

struct ApplyContext {
  ApplyContext() {
  }
  ApplyContext(const MetricID& timer) {
    sw = std::make_unique<StopWatch>(timer);
    sw->Start();
  }
  std::unique_ptr<StopWatch> sw;
  std::shared_ptr<EditLogOp> op;
  RpcClosure* done{nullptr};
  std::vector<cnetpp::base::StringPiece> src_path_components;
  std::vector<cnetpp::base::StringPiece> dst_path_components;
  std::vector<cnetpp::base::StringPiece> rb_path_components;
  std::vector<std::string> sorted_paths;

  std::vector<cnetpp::base::StringPiece> parent_path_components;
  std::vector<cnetpp::base::StringPiece> children_path_components;

  std::vector<cnetpp::base::StringPiece> acc_path_components;
  std::vector<std::string> acc_subfile_paths;
  std::shared_ptr<INode> acc_open_or_mkdir_inode;

  ApplyMode apply_mode;
  int thread_idx;
  std::shared_ptr<INodeStatDeltaCache> delta_cache;
  bool logical_apply_check_physical_log;
  bool physical_apply_check_db;
};

std::shared_ptr<EditLogOp> ForgeDummyStartSegmentOp(int64_t txid);
std::shared_ptr<EditLogOp> ForgeDummyEndSegmentOp(int64_t txid);
// Represent ExtendedBlock as string like Java
std::string FormatExtendedBlock(const ExtendedBlockProto& block);

class ClientTxIDChecker {
 public:
  ClientTxIDChecker(HAStateBase* ha_state,
                    const std::function<int64_t()>& get_txid_func,
                    NameSpaceMetrics* metrics);

  Status Check(RpcController* ctx);

 private:
  HAStateBase* ha_state_{nullptr};
  std::function<int64_t()> get_txid_func_;
  NameSpaceMetrics& metrics_;
};

class NameSpace {
 public:
  NameSpace();
  // only use for fsimage_transfer
  explicit NameSpace(const std::string& db_path);
  NameSpace(const std::string& db_path,
            std::shared_ptr<EditLogContextBase> context,
            std::shared_ptr<BlockManager> block_manager,
            std::shared_ptr<DatanodeManager> datanode_manager,
            std::shared_ptr<JobManager> job_manager,
            std::shared_ptr<DataCenters> data_centers,
            std::shared_ptr<UfsEnv> ufs_env,
            RetryCache* rc);
  // TestOnly.
  NameSpace(HAStateBase* ha_state,
            SafeModeBase* safemode,
            std::shared_ptr<BlockManager> block_manager,
            std::unique_ptr<BlockReportManager>&& block_report_manager,
            std::shared_ptr<DatanodeManager> datanode_manager,
            std::unique_ptr<LeaseManagerBase>&& lease_manager,
            std::unique_ptr<LeaseMonitorBase>&& lease_monitor,
            std::shared_ptr<EditLogContextBase> edit_log_ctx,
            std::shared_ptr<EditLogSenderBase> edit_log_sender,
            std::shared_ptr<MetaStorage> meta_storage,
            std::unique_ptr<RWLockManager> lock_manager,
            std::shared_ptr<KeyManager> key_manager,
            std::shared_ptr<DataCenters> data_centers,
            std::shared_ptr<AccessCounterManager> access_counter_manager,
            std::unique_ptr<FileFinalizerBase>&& file_finalizer,
            std::shared_ptr<JobManager> job_manager);

  virtual ~NameSpace();

  static bool IsAccMode();
  static bool IsHdfsMode();

  void ShowPendingCountForApplyThread();
  void StopApplyThread();
  void ResetLastAppliedTxId(int64_t txid);

  void LoadGenerationStampV2();
  void LoadLastAllocatedBlockId();

  void set_ha_state(HAStateBase* ha_state) {
    ha_state_ = ha_state;
    // The use of HAFlexibleEditLogContext::SetMetaStorage may not be
    // aesthetically pleasing in code design. However, given that MetaStorage is
    // constructed by NameSpace, this step is necessary and unavoidable in our
    // implementation.
    if (edit_log_ctx_) {
      auto ctx = std::dynamic_pointer_cast<dancenn::HAFlexibleEditLogContext>(
          edit_log_ctx_);
      if (ctx) {
        ctx->SetHAState(ha_state);
      }
    }
  }

  HAStateBase* ha_state() const {
    return ha_state_;
  }

  void set_safemode(SafeModeBase* safemode) {
    safemode_ = safemode;
  }

  SafeModeBase* safemode() const {
    return safemode_;
  }

  // Only used by uts.
  void set_rwlock_manager(std::unique_ptr<RWLockManager>&& rwlock_manager) {
    lock_manager_ = std::move(rwlock_manager);
  }

  void set_meta_storage(std::unique_ptr<MetaStorage>&& meta_storage) {
    meta_storage_ = std::move(meta_storage);
  }

  // TODO: This is a workaround. Pls refactor it.
  BlockReportManager* GetBlockReportManager() {
    return block_report_manager_.get();
  }

  void Start();
  void Stop();
  bool IsRunning() { return is_running_.load(); }
  void StartActive();
  void StopActive();
  void StartStandby();
  void StopStandby();
  void AwaitHaSwitch();

  cnetpp::concurrency::ThreadPool* GetBgThreadPool();

  Status CreateCheckpoint(const std::string& path);
  Status PurgeLogsOlderThan(int64_t txid);

  void StartMetaScanner();
  void StopMetaScanner();

  void StartRecycleScanner();
  void StopRecycleScanner();

  void StartLifecycleScanner();
  void StopLifecycleScanner();

  void StartAZMonitor();
  void StopAZMonitor();

  // public for test
  void StartLeaseMonitor() {
    lease_manager_->RenewAllLeases();
    lease_monitor_->Start();
  }
  void StopLeaseMonitor() {
    if (lease_monitor_) {
      lease_monitor_->Stop();
    }
  }

  void TestOnlySetEditLogSender(std::shared_ptr<EditLogSenderBase> sender);

  FileFinalizerBase* TestOnlyGetFileFinalizer() {
    return file_finalizer_.get();
  }

  void set_block_manager(std::shared_ptr<BlockManager> block_manager) {
    block_manager_ = block_manager;
  }
  std::shared_ptr<BlockManager> block_manager() const {
    return block_manager_;
  }

  void set_datanode_manager(std::shared_ptr<DatanodeManager> datanode_manager) {
    datanode_manager_ = datanode_manager;
  }
  std::shared_ptr<DatanodeManager> datanode_manager() const {
    return datanode_manager_;
  }

  void set_block_token_secret_manager(
      std::shared_ptr<BlockTokenSecretManager> block_token_secret_manager) {
    block_token_secret_manager_ = block_token_secret_manager;
  }
  std::shared_ptr<BlockTokenSecretManager> block_token_secret_manager() const {
    return block_token_secret_manager_;
  }

  void set_key_manager(std::shared_ptr<KeyManager> key_manager) {
    key_manager_ = std::move(key_manager);
  }
  std::shared_ptr<KeyManager> key_manager() const {
    return key_manager_;
  }

  std::shared_ptr<LifecycleScanner> lifecycle_scanner() const {
    return lifecycle_scanner_;
  }

  std::shared_ptr<AZMonitor> az_monitor() const {
    return az_monitor_;
  }

  // for_internal_use: true=DN, false=Client
  bool GetUfsInfo(cloudfs::RemoteBlockInfoProto* info, bool for_internal_use) const;
  void GetNamespaceInfo(NamespaceInfoProto* info);
  bool VerifyRequest(const StorageInfoProto& info);

  void SyncMeta();

  void WriteStartNop(int64_t txid, bool transition_to_active = false);
  void WriteEndNop(int64_t txid);

  Status NextBlockIdAndGSv2Loop(int delta, uint64_t* block_id, uint64_t* gsv2);

  // [SECTION] Core Write RPC
  void AsyncCreateFile(const std::string& src,
                       const PermissionStatus& permission,
                       const NetworkLocationInfo& client_location,
                       const UserGroupInfo& ugi,
                       const LogRpcInfo& rpc_info,
                       const std::string& client_machine,
                       const CreateRequestProto* request,
                       CreateResponseProto* response,
                       RpcController* ctx,
                       RpcClosure* rpc_done,
                       bool need_lock = true,
                       std::function<Status(INode*)> process_ufs_info = nullptr,
                       INodeSyncChecker* sync_checker = nullptr) noexcept;

  void AsyncAddBlock(const std::string& src,
                     const NetworkLocationInfo& client_location,
                     const LogRpcInfo& rpc_info,
                     const UserGroupInfo& ugi,
                     const AddBlockRequestProto* request,
                     AddBlockResponseProto* response,
                     RpcController* ctx,
                     RpcClosure* rpc_done) noexcept;

  void AsyncMkDirs(const std::string& path,
                   const PermissionStatus& permission,
                   const UserGroupInfo& ugi,
                   bool create_parent,
                   bool inherit_perm,
                   RpcController* ctx,
                   const LogRpcInfo& rpc_info,
                   RpcClosure* rpc_done,
                   std::function<void(INode*)> fill_inode_func = nullptr,
                   bool need_lock = true) noexcept;

  void AsyncRenameTo(const std::string& src,
                     const std::string& dst,
                     const LogRpcInfo& rpc_info,
                     const UserGroupInfo& ugi,
                     RpcClosure* rpc_done) noexcept;

  void AsyncRenameTo2(const std::string& src,
                      const std::string& dst,
                      bool overwrite,
                      const LogRpcInfo& rpc_info,
                      const UserGroupInfo& ugi,
                      RpcClosure* rpc_done) noexcept;

  void AsyncConcat(const std::string& target,
                   const std::vector<std::string>& srcs,
                   const LogRpcInfo& rpc_info,
                   RpcClosure* rpc_done);

  void AsyncDelete(const std::string& src,
                   bool recursive,
                   const UserGroupInfo& ugi,
                   const LogRpcInfo& rpc_info,
                   RpcClosure* rpc_done) noexcept;

  void DeleteFileByDnEvict(
      const DatanodeID dn_id,
      const std::string& dn_ip,
      const cloudfs::datanode::ReceivedDeletedBlockInfoProto& block);

  void AsyncUpdateBlockForPipeline(
      const UpdateBlockForPipelineRequestProto* request,
      const LogRpcInfo& rpc_info,
      UpdateBlockForPipelineResponseProto* response,
      const UserGroupInfo& ugi,
      RpcClosure* rpc_done);

  void AsyncUpdatePipeline(const UpdatePipelineRequestProto* request,
                           const LogRpcInfo& rpc_info,
                           RpcClosure* rpc_done);

  void AsyncAbandonBlock(const std::string& src,
                         const AbandonBlockRequestProto* request,
                         const LogRpcInfo& rpc_info,
                         RpcClosure* rpc_done);

  void AsyncCompleteFile(const std::string& src,
                         const CompleteRequestProto* request,
                         RpcController* ctx,
                         RpcClosure* rpc_done);

  void AsyncAppend(const std::string& path,
                   const std::string& client_machine,
                   const LogRpcInfo& rpc_info,
                   const UserGroupInfo& ugi,
                   const AppendRequestProto* request,
                   AppendResponseProto* response,
                   RpcClosure* rpc_done,
                   bool need_lock = true);

  void AsyncFsync(const std::string& src,
                  const FsyncRequestProto* request,
                  const LogRpcInfo& rpc_info,
                  RpcClosure* rpc_done);

  void AsyncSetTimes(const std::string& path,
                     const SetTimesRequestProto* request,
                     const UserGroupInfo& ugi,
                     RpcClosure* rpc_done);

  void AsyncSetStoragePolicy(const std::string& src,
                             const std::string& policy_name,
                             const UserGroupInfo& ugi,
                             RpcClosure* rpc_done);

  Status RenewLease(const std::string& client, const std::string& client_ip);
  Status RecoverLease(const std::string& client,
                      const std::string& src,
                      const RecoverLeaseRequestProto* request);

  virtual void SpeedUpRelease(INodeID inode_id);

  void AsyncSetReplication(const std::string& path,
                           uint32_t replica,
                           bool allow_zero_replica,
                           const UserGroupInfo& ugi,
                           RpcClosure* rpc_done);

  Status GetBlockKeys(GetBlockKeysResponseProto* response);

  Status TestSetReplicationAttrOnly(const std::string& path, uint32_t replica);

  void AsyncCreateSymlink(const std::string& target,
                          const std::string& link,
                          const PermissionStatus& perm,
                          const UserGroupInfo& ugi,
                          bool create_parent,
                          const LogRpcInfo& rpc_info,
                          RpcClosure* rpc_done);

  void AsyncSetOwner(const std::string& path,
                     const std::string& username,
                     const std::string& groupname,
                     const UserGroupInfo& ugi,
                     RpcClosure* rpc_done);

  void AsyncSetPermission(const std::string& path,
                          uint32_t perm,
                          const UserGroupInfo& ugi,
                          RpcClosure* rpc_done);

  Status CommitBlockSynchronization(
      const CommitBlockSynchronizationRequestProto& request);

  void AsyncCommitLastBlock(const std::string& src,
                            const cnetpp::base::IPAddress& writer,
                            const CommitLastBlockRequestProto* request,
                            CommitLastBlockResponseProto* response,
                            const LogRpcInfo& rpc_info,
                            const UserGroupInfo& ugi,
                            RpcClosure* rpc_done);

  // [SECTION] Core Read RPC
  Status GetFileInfo(const std::string& path,
                     const NetworkLocationInfo& client_location,
                     bool resolve_link,
                     bool need_location,
                     GetFileInfoResponseProto* response,
                     const UserGroupInfo& ugi,
                     RpcController* ctx = nullptr,
                     INodeSyncChecker* sync_checker = nullptr,
                     const std::string* pufs_name = nullptr);

  Status GetBlockLocation(const std::string& src,
                          const NetworkLocationInfo& client_location,
                          const GetBlockLocationsRequestProto& request,
                          GetBlockLocationsResponseProto* response,
                          const UserGroupInfo& ugi,
                          RpcController* ctx = nullptr,
                          INodeSyncChecker* sync_checker = nullptr,
                          std::function<std::string(const std::string&)>
                              GenBlockPufsName = nullptr);

  Status GetListing(const std::string& path,
                    const NetworkLocationInfo& client_location,
                    const GetListingRequestProto& request,
                    GetListingResponseProto* response,
                    const UserGroupInfo& ugi,
                    RpcController* ctx = nullptr,
                    INodeSyncChecker* sync_checker = nullptr);

  Status GetSnapshotsListing(
      const INodeInPath& parent_iip,
      const std::string &start_after,
      uint32_t limit,
      const std::function<HdfsFileStatusProto*()>& add_result,
      bool* has_more = nullptr);

  Status IsFileClosed(const std::string& path,
                      IsFileClosedResponseProto* response,
                      RpcController* ctx = nullptr);

  Status GetServerDefaults(::cloudfs::FsServerDefaultsProto* server_default,
                           RpcController* ctx = nullptr);

  Status GetFsStats(GetFsStatsResponseProto* response,
                    RpcController* ctx = nullptr);

  Status CheckAccess(const std::shared_ptr<UserGroupInfo>& user_info,
                     const CheckAccessRequestProto* request,
                     CheckAccessResponseProto* response,
                     RpcController* ctx = nullptr);

  Status GetPreferredBlockSize(const std::string& path,
                               GetPreferredBlockSizeResponseProto* response,
                               RpcController* ctx = nullptr);

  Status GetRecyclePolicy(
      const std::string& path,
      std::vector<std::pair<std::string, std::shared_ptr<RecyclePolicy>>>*
          result,
      RpcController* ctx = nullptr);

  void GetAdditionalDatanode(const std::string& src,
                             const NetworkLocationInfo& client_location,
                             const UserGroupInfo& ugi,
                             const GetAdditionalDatanodeRequestProto* request,
                             GetAdditionalDatanodeResponseProto* response,
                             RpcClosure* rpc_done,
                             RpcController* ctx = nullptr);

  Status GetContentSummary(const std::string& path,
                           const std::shared_ptr<UserGroupInfo>& user_info,
                           GetContentSummaryResponseProto* response,
                           RpcController* ctx = nullptr);

  Status GetStoragePolicies(GetStoragePoliciesResponseProto* response,
                            RpcController* ctx = nullptr);

  Status DumpFileInfo(const std::string& path,
                      bool detail,
                      bool standby_read,
                      std::ostream& os,
                      std::vector<uint64_t>* block_ids = nullptr,
                      RpcController* ctx = nullptr);

  // [SECTION] HA Related RPC
  Status Msync(MsyncResponseProto* response);

  Status GetHAServiceState(HAServiceStateResponseProto* response);

  // [SECTION] Snapshot Related RPC
  void AsyncAllowSnapshot(const std::string& src, RpcClosure* rpc_done);
  void AsyncDisAllowSnapshot(const std::string& src, RpcClosure* rpc_done);
  void AsyncCreateSnapshot(const std::string& src,
                           const std::string& snapshot_name,
                           ::cloudfs::CreateSnapshotResponseProto* result,
                           RpcClosure* rpc_done);
  void AsyncDeleteSnapshot(const std::string& src,
                           const std::string& snapshot_name,
                           RpcClosure* rpc_done);
  void AsyncRenameSnapshot(const std::string& src,
                           const std::string& snapshot_old_name,
                           const std::string& snapshot_new_name,
                           RpcClosure* rpc_done);
  Status GetSnapshottableDirListing(
      ::cloudfs::SnapshottableDirectoryListingProto* result,
      RpcController* ctx = nullptr);
  Status GetSnapshotDiff(const std::string& src,
                         const std::string& from_snapshot,
                         const std::string& to_snapshot,
                         ::cloudfs::SnapshotDiffReportProto* result,
                         RpcController* ctx = nullptr);

  // [Section] Additional RPC
  // XAttr
  Status GetXAttrs(const std::string& path,
                   const RepeatedPtrField<XAttrProto>& xattrs,
                   RepeatedPtrField<XAttrProto>* response,
                   const UserGroupInfo& ugi,
                   RpcController* ctx = nullptr);

  void AsyncSetXAttr(const std::string& path,
                     const XAttrProto& xattr,
                     bool create,
                     bool replace,
                     const LogRpcInfo& rpc_info,
                     RpcClosure* rpc_done);

  void AsyncRemoveXAttr(const std::string& path,
                        const XAttrProto& xattr,
                        const LogRpcInfo& rpc_info,
                        const UserGroupInfo& ugi,
                        RpcClosure* rpc_done);

  Status ListXAttrs(const std::string& path,
                    RepeatedPtrField<XAttrProto>* xattrs,
                    const UserGroupInfo& ugi,
                    RpcController* ctx = nullptr);

  void AsyncSetMountPointAttr(const std::string& src,
                              const std::set<std::string>& allow_entries,
                              const std::set<std::string>& deny_entries,
                              bool require_path_empty_dir,
                              const PermissionStatus& permission,
                              RpcClosure* rpc_done);

  // Dir Policy
  void AsyncSetDirPolicy(const std::string& path,
                         const ::cloudfs::SetDirPolicyRequestProto* request,
                         RpcClosure* rpc_done);
  void AsyncRemoveDirPolicy(
      const std::string& path,
      const ::cloudfs::RemoveDirPolicyRequestProto* request,
      RpcClosure* rpc_done);
  Status GetDirPolicy(const std::string& path,
                      const ::cloudfs::GetDirPolicyRequestProto* request,
                      ::cloudfs::GetDirPolicyResponseProto* response);
  Status ListDirPolicy(const ::cloudfs::ListDirPolicyRequestProto* request,
                       ::cloudfs::ListDirPolicyResponseProto* response);

  // ReplicaPolicy
  // Deprecated: remove API
  Status GetReplicaPolicy(const std::string& path,
                          GetReplicaPolicyResponseProto* response,
                          RpcController* ctx = nullptr);

  // Deprecated: remove API
  void AsyncSetReplicaPolicy(const std::string& path,
                             const ReplicaPolicy& policy,
                             RpcClosure* rpc_done);
  // Deprecated: remove API
  void AsyncRemoveReplicaPolicy(const std::string& path, RpcClosure* rpc_done);

  // listReplicaPolicies
  // Deprecated: remove API
  Status GetDistributed(GetDistributedResponseProto* response,
                        RpcController* ctx = nullptr);

  // ReadPolicy
  // Deprecated: remove API
  Status GetReadPolicy(const std::string& path,
                       GetReadPolicyResponseProto* response,
                       RpcController* ctx = nullptr);

  // Deprecated: remove API
  void AsyncSetReadPolicy(const std::string& path,
                          const ReadPolicy& policy,
                          RpcClosure* rpc_done);

  // Deprecated: remove API
  void AsyncRemoveReadPolicy(const std::string& path, RpcClosure* rpc_done);

  // Deprecated: remove API
  Status ListReadPolicies(ListReadPoliciesResponseProto* response,
                          RpcController* ctx = nullptr);

  // LifecyclePolicy
  Status GetStorageClass(const std::string& path, StorageClass* cls);
  void AsyncSetLifecyclePolicy(const std::string& path,
                               const SetLifecyclePolicyRequestProto& request,
                               SetLifecyclePolicyResponseProto* response,
                               const UserGroupInfo& ugi,
                               const LogRpcInfo& rpc_info,
                               RpcClosure* rpc_done);
  void AsyncUnsetLifecyclePolicy(
      const std::string& path,
      const UnsetLifecyclePolicyRequestProto& request,
      UnsetLifecyclePolicyResponseProto* response,
      const UserGroupInfo& ugi,
      const LogRpcInfo& rpc_info,
      RpcClosure* rpc_done);
  void AsyncUnsetDepredLifecyclePolicy(const INodeID id, RpcClosure* rpc_done);
  Status GetLifecyclePolicy(const std::string& path,
                            LifecyclePolicy* policy,
                            const UserGroupInfo& ugi,
                            RpcController* ctx);
  Status GetStorageClassStat(const std::string& path,
                             StorageClassStatProto* stat);

  Status PrepareSetAZBlacklist();
  void CommitSetAZBlacklist(const std::string& azs, RpcClosure* rpc_done);
  void AsyncSetAZBlacklist(const std::string& azs, RpcClosure* rpc_done);
  void GetAZBlacklist(std::string* azs);

  // for lease monitor
  // Status.code == kOK: file closed, vise versa.
  Status ReleaseLease(uint64_t inode_id,
                      const std::string& orig_holder,
                      const std::string& new_holder,
                      bool close_file = true);

  void AsyncLoadData(const std::string& src,
                 const LoadRequestProto* request,
                 LoadResponseProto* response,
                 RpcClosure* rpc_done,
                 const std::string* ufs_key = nullptr);

  void AsyncLoadMetadata(const std::string& src,
                         const LoadRequestProto* request,
                         LoadResponseProto* response,
                         RpcClosure* rpc_done,
                         std::shared_ptr<TaskCreator>& task_creator);

  void AsyncFree(const std::string& src,
                 const FreeRequestProto* request,
                 FreeResponseProto* response,
                 const UserGroupInfo& ugi,
                 RpcClosure* rpc_done);

  // UGI is not checked
  void AsyncPin(const std::string& src,
                const PinRequestProto* request,
                PinResponseProto* response,
                const LogRpcInfo& rpc_info,
                RpcClosure* rpc_done,
                INodeSyncChecker* sync_checker);
  void UpdatePinDataResident(const std::string& src,
                             const INode& target_inode,
                             bool resident_data,
                             RpcClosure* rpc_done);
  // Called by scrub, update inode attrs by current inode-tree status
  // Supported attr:
  // - pin status
  Status ReconcileINodeAttrs(const INode& inode);

  // UGI is not checked
  Status LookupJob(const LookupJobRequestProto* request,
                   LookupJobResponseProto* response,
                   RpcController* ctx);

  Status CancelJob(const CancelJobRequestProto* request,
                   CancelJobResponseProto* response,
                   RpcController* ctx);

  Status InnerSubmitJob(const std::string& src,
                        std::shared_ptr<DataJobOption> opt,
                        const ManagedJobType job_type,
                        ManagedJobId* job_id,
                        bool file_only,
                        std::shared_ptr<TaskCreator> task_creator = nullptr);

  void PersistJobInfo(const std::string& job_id,
                      const JobInfoOpBody& job_info,
                      RpcClosure* rpc_done);

  // Batch API
  void AsyncBatchCreate(const std::vector<std::string>& paths,
                        const std::vector<std::string>& ufs_keys,
                        const NetworkLocationInfo& client_location,
                        const std::string& client_machine,
                        const UserGroupInfo& ugi,
                        const BatchCreateFileRequestProto* request,
                        BatchCreateFileResponseProto* response,
                        const LogRpcInfo& rpc_info,
                        RpcClosure* rpc_done,
                        RpcController* ctrl);

  void AsyncBatchComplete(
      const std::vector<std::string>& single_file_paths,
      const std::vector<std::vector<std::string>>& concat_file_srcs_paths,
      const std::vector<std::string>& concat_file_target_paths,
      const BatchCompleteFileRequestProto* request,
      BatchCompleteFileResponseProto* response,
      const LogRpcInfo& rpc_info,
      RpcClosure* rpc_done,
      RpcController* ctrl);

  void AsyncBatchDelete(const std::vector<std::string>& paths,
                        const BatchDeleteFileRequestProto* request,
                        BatchDeleteFileResponseProto* response,
                        const LogRpcInfo& rpc_info,
                        RpcClosure* rpc_done,
                        RpcController* ctrl);

  void AsyncBatchGet(const std::vector<std::string>& paths,
                     const NetworkLocationInfo& client_location,
                     bool need_location,
                     const PermissionStatus& permission,
                     const UserGroupInfo& ugi,
                     const BatchGetFileRequestProto* request,
                     BatchGetFileResponseProto* response,
                     RpcClosure* rpc_done,
                     RpcController* ctrl);

  // Other
  Status RefreshDataNodes();

  virtual void BatchUpdateTtlATimes(const std::vector<TtlATimeRecord>& access_records, Closure* done);

 public:
  // Acc Mode related
  void SetUfsConfig(const UfsConfig* conf) {
    ufs_conf_ = conf;
  }
  std::string InnerPathToUfsPath(const std::string& inner_path);
  Status CreateAccPrefixIfNotExist(const std::string& prefix);

  Status MkdirsUfs(const std::string& ufs_path,
                   const std::string& inner_path,
                   const PermissionStatus& p,
                   const UserGroupInfo& ugi,
                   const std::shared_ptr<Ufs>& ufs,
                   bool create_parent,
                   RpcController* ctx,
                   SyncTask* sync_task);

  Status CreateUfsFile(const std::string& ufs_path,
                       const std::string& inner_path,
                       const PermissionStatus& p,
                       const NetworkLocationInfo client_location,
                       const UserGroupInfo& ugi,
                       const std::shared_ptr<Ufs>& ufs,
                       const LogRpcInfo& rpc_info,
                       const std::string& client_machine,
                       const cloudfs::CreateRequestProto* request,
                       cloudfs::CreateResponseProto* response,
                       RpcController* ctx);

  void AppendUfsFile(const std::string& ufs_path,
                     const std::string& inner_path,
                     const std::string& client_machine,
                     const LogRpcInfo& rpc_info,
                     const UserGroupInfo& ugi,
                     const AppendRequestProto* request,
                     AppendResponseProto* response,
                     const std::shared_ptr<Ufs>& ufs,
                     SyncCallback cb);

  void SetBlockAccInfo(const INode* inode, BlockInfoProto* bip, bool is_last);

  Status PersistUfsFileCheckFileIdAndLock(
      uint64_t inode_id,
      const std::shared_ptr<Ufs>& ufs,
      std::shared_ptr<FileSyncContext>& f_ctx,
      std::unique_ptr<LockedPath>& locked_path,
      vshared_lock& ha_barrier,
      StopWatchContext* rpc_sw_ctx = nullptr);
  Status IsUfsFileAllowConcat(const std::string& inner_path,
                              const INode& inode);
  // ignore_last_block: for batch API, use when the last block is abandon
  // last_bip: for batch API, use when last block is updated
  Status CheckAndGetBIPForConcat(const std::string& inner_path,
                                 const INode& inode,
                                 std::vector<BlockInfoProto>* bips,
                                 BlockInfoProto* last_bip = nullptr);
  static void UpdateBipInfoForBundleUpload(const INode& inode,
                                           BlockInfoProto* bip,
                                           BlockInfoProto* last_bip,
                                           bool is_last);
  // Create new MPU, re-issue upload commands for block
  Status PersistUfsFileUpdateUploadInfo(const std::string& inner_path,
                                        const std::string& ufs_path,
                                        Ufs* ufs,
                                        INode* inode);
  Status CheckAllBlockPersisted(const INode& inode,
                                std::map<int, std::string>* parts,
                                bool* allBlocksUploaded,
                                Ufs* ufs);
  virtual Status TriggerUploadUfsFile(const std::shared_ptr<Ufs>& ufs,
                                      uint64_t inode_id,
                                      StopWatchContext* rpc_sw_ctx = nullptr);
  // most likely need to retry
  Status TriggerUploadUfsFileInternal(const std::shared_ptr<Ufs>& ufs,
                                      uint64_t inode_id,
                                      StopWatchContext* rpc_sw_ctx = nullptr);

  // TriggerWriteCacheEvict
  Status TriggerWriteCacheEvict(FileSyncContext* ctx);
  // set file_completed = true if inode already complete
  virtual Status PersistUfsFile(const std::shared_ptr<Ufs>& ufs,
                                uint64_t inode_id,
                                const std::string& ufs_key,
                                const std::string& upload_id,
                                bool check_upload_id,
                                bool trigger_upload_block,
                                bool* file_completed,
                                StopWatchContext* rpc_sw_ctx = nullptr);
  Status PersistUfsFileInternal(const std::shared_ptr<Ufs>& ufs,
                                uint64_t inode_id,
                                const std::string& ufs_key,
                                const std::string& upload_id,
                                bool check_upload_id,
                                bool* file_completed,
                                StopWatchContext* rpc_sw_ctx = nullptr);
  // Sync the UFS file (include directory) to local inode
  // For directory, only create the directory inode, not include its children
  // We didn't know if ufs_path is a file or dir
  virtual Status SyncUfsFile(const std::string& ufs_path,
                             const std::string& inner_path,
                             const PermissionStatus& p,
                             const UserGroupInfo& ugi,
                             const std::shared_ptr<Ufs>& ufs,
                             bool sync_on_ancestor_not_dir,
                             uint64_t target_sync_ts,
                             StopWatchContext* rpc_sw_ctx = nullptr,
                             TriggerSyncReason trigger_sync_reason = UNKNOWN);
  // SyncUfsFileOnly treats UFS path only as FILE, and apply sync
  // action only on local FILE. Also, it checks etag if `check_etag` is true,
  // and the file will not be synced if the etag in local INode is equal to the
  // input one.
  virtual Status SyncUfsFileOnly(const std::string& ufs_path,
                                 const std::string& inner_path,
                                 const PermissionStatus& p,
                                 const UserGroupInfo& ugi,
                                 const std::shared_ptr<Ufs>& ufs,
                                 bool check_etag,
                                 const std::string& etag,
                                 StopWatchContext* rpc_sw_ctx = nullptr);
  // Sync the file or directory with children from UFS to local inode
  // We didn't know if ufs_path is a file or dir
  Status SyncUfsFileListing(const std::shared_ptr<ListingOption>& opt,
                            uint64_t target_sync_ts,
                            const std::shared_ptr<Ufs>& ufs,
                            ListingSyncTask* task);

  Status SyncRenameUfs(const std::shared_ptr<RenameToOption>& opt,
                       const std::shared_ptr<Ufs>& ufs);

  Status SyncDeleteUfs(const std::shared_ptr<DeleteOption>& opt,
                       const std::shared_ptr<Ufs>& ufs);
  // for BlockManager
  // used by block manager
  bool GetStoragePolicyByINodeId(uint64_t inode_id,
                                 uint64_t parent_id,
                                 StoragePolicyId* id);

  // used by block manager
  bool GetReplicaPolicyByINodeId(uint64_t inode_id, ReplicaPolicy* policy);

  uint64_t GetLastCkptTxId();

  cloudfs::HAServiceStateProto GetHAServiceStateInternal();

  bool GetStackInfo(std::string* stack_info);

  bool ForceCompactDeletion();
  void ForceCompactStorageClassReport();
  bool ForceCompactAll();
  void SetCompactAllStyle(bool bottom_compact);
  bool IsCompactAllInProgress() const;

  Status SaveAccessCounterSnapshot(
      const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto&
          snapshot);  // NOLINT(whitespace/line_length)

  std::shared_ptr<AccessCounterManager> access_counter_manager() {
    return access_counter_manager_;
  }

  std::shared_ptr<DataCenters> data_centers() {
    return data_centers_;
  }

  std::string BuildFullPath(uint64_t id, INode* output_inode = nullptr);
  std::string BuildFullPath2(uint64_t id, INodeInPath* output_iip);

  int32_t layout_version() const;

  int64_t ctime() const;
  int64_t filesystem_id() const;
  int64_t namespace_id() const;
  cloudfs::NamespaceType namespace_type() const;
  const PersistentUfsInfo& persistent_ufs_info() const;
  const std::string& cluster_id() const;
  const std::string& blockpool_id() const;
  const std::string& software_version() const;
  uint64_t generation_stamp_v1() const;
  uint64_t generation_stamp_v2() const;
  uint64_t generation_stamp_v1_limit() const;
  uint64_t last_inode_id() const;
  uint64_t last_allocated_block_id() const;
  ApplyMode editlog_apply_mode() const;
  void set_editlog_apply_mode(ApplyMode mode);
  bool IsGenStampInFuture(uint64_t block_generation_stamp,
                          uint64_t current_gsv2 = 0) const;

  UfsEnv* ufs_env() const;
  UfsUploadMonitor* ufs_upload_monitor() const;
  UfsNouploadManager* ufs_noupload_mgr() const;
  UfsUploadingManager* ufs_uploading_mgr() const;

  int64_t num_inodes() const;

  bool GetINode(uint64_t id, INode* inode);
  Status GetINodeByPath(const std::string& path, INode* inode);

  uint64_t LastInodeId() const {
    return last_inode_id_.load(std::memory_order_seq_cst);
  }
  uint64_t NextINodeId();

  void NextBlockIdAndGSv2(uint64_t* block_id, uint64_t* gsv2);

  uint64_t NextBlockId();

  StatusCode GetChildINodeInPath(const INodeInPath& parent_iip,
                                 const std::string& child_name,
                                 INodeInPath* child_iip,
                                 const MetaStorageIterPtr iter = nullptr);
  // is_for_read: if true, we can see snapshot path
  StatusCode GetLastINodeInPath(
      const std::vector<cnetpp::base::StringPiece>& path_components,
      INode* last_inode,
      std::vector<INode>* ancestors = nullptr,
      INode* parent = nullptr,
      MetaStorageSnapPtr snapshot = nullptr,
      bool is_for_read = false);
  // output INodeInPath instead of INode, used when path snapshot information is
  // needed by continuous process
  StatusCode GetLastINodeInPath(
      const std::vector<cnetpp::base::StringPiece>& path_components,
      INodeInPath* last_iip,
      std::vector<INode>* ancestors = nullptr,
      INodeInPath* parent_iip = nullptr,
      MetaStorageSnapPtr snapshot = nullptr,
      bool is_for_read = false);
  // used to implement GetLastINodeInPath, read path like xxx/.snapshot/...
  // snapshot_component_index: the position of ".snapshot" in `path_components`
  StatusCode GetLastINodeInSnapshotPath(
      const StringPieces& path_components,
      size_t snapshot_component_index,
      INodeInPath* last_iip,
      std::vector<INode>* ancestors = nullptr,
      const MetaStorageIterPtr iter = nullptr);
  StatusCode GetInodeOfSnapshotPath(uint64_t parent_id,
                                    const std::string& name,
                                    uint64_t read_txid,
                                    INode* inode);

  StatusCode GetLastINodeInPathByFileId(const std::string& src,
                                        uint64_t file_id,
                                        std::string* new_path,
                                        INode* inode,
                                        std::vector<INode>* ancestors,
                                        RpcClosure* rpc_done,
                                        bool write_lock = true);
  StatusCode GetLastINodeInPathByFileId(const std::string& src,
                                        uint64_t file_id,
                                        std::string* new_path,
                                        INodeInPath* iip,
                                        std::vector<INode>* ancestors,
                                        RpcClosure* rpc_done,
                                        bool write_lock = true);

  Status GetINodeAndLockPathByFileId(uint64_t file_id,
                                     std::string* path,
                                     RWLockManager::DirTreeLockPtr* locks,
                                     INode* inode,
                                     std::vector<INode>* ancestors,
                                     bool write_lock = true);
  Status GetINodeAndLockPathByFileId(uint64_t file_id,
                                     std::string* path,
                                     RWLockManager::DirTreeLockPtr* locks,
                                     INodeInPath* iip,
                                     std::vector<INode>* ancestors,
                                     bool write_lock = true);

  int64_t GetCompleteBlocksTotal();

  DetailLeaseStats GetLeaseDetailStats(const std::string& path = "",
                                       const std::string& holder = "",
                                       const std::string& ip = "");

  void NextGenerationStampUpdateBlockForPipeline(uint64_t previous_id,
                                                 uint64_t* txid,
                                                 uint64_t* new_gs);

  uint64_t NextGenerationStamp(uint64_t previous_gs, Closure* done = nullptr);
  uint64_t NextGenerationStampV1(Closure* done = nullptr);
  uint64_t NextGenerationStampV2(Closure* done = nullptr);

  // only for unit test
  MetaStorage* meta_storage() {
    return meta_storage_.get();
  }
  // only for unit test
  void set_lease_manager(std::unique_ptr<LeaseManagerBase>&& lease_manager) {
    lease_manager_ = std::move(lease_manager);
  }
  LeaseManagerBase* lease_manager() {
    return lease_manager_.get();
  }
  std::shared_ptr<MetaStorage> GetMetaStorage() {
    return meta_storage_;
  }

  RetryCache* retry_cache() {
    return retry_cache_;
  }

  NameSpaceMetrics& metrics() {
    return metrics_;
  }

  cloudfs::HAServiceStateProto GetHAState() {
    return ha_state_->GetState();
  }

  PolicyManager* policy_manager() {
    return policy_manager_.get();
  }

  void StartBGDeletionWorker();
  void StopBGDeletionWorker();

  // if interval_in_ms == 0, use FLAGS_snapshot_gc_interval_in_sec
  void StartSnapshotGcWorker(uint64_t interval_in_ms = 0);
  void StopSnapshotGcWorker();

  void StartTimerTaskManager();
  void StopTimerTaskManager();

  MetaScannerTaskID AddMetaScannerTask(std::shared_ptr<MetaScannerTaskBase> task);
  bool EraseMetaScannerTask(MetaScannerTaskID id, bool force);

  // apply edit log ops
  int64_t Apply(std::shared_ptr<EditLogOp> op);
  ApplyMode ChooseApplyMode(std::shared_ptr<EditLogOp> op,
                            bool* physical_checkable);
  void ApplyOpLogical(std::shared_ptr<ApplyContext> apply_ctx);
  void ApplyOpPhysical(std::shared_ptr<ApplyContext> apply_ctx);
  void CheckDBINodeForPhysicalApply(const std::string& path,
                                    const INode& old_inode_from_editlog);

  void ApplyNonCfsOpLogical(std::shared_ptr<ApplyContext> apply_ctx);
  void ApplyCfsOpLogical(std::shared_ptr<ApplyContext> apply_ctx);
  void ApplyOpAdd(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAddFromCreateInternal(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAddFromAppendInternal(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpOpenFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpOpenFileFromCreateInternal(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpOpenFileFromAppendInternal(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAppend(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAppendImpl(std::shared_ptr<OpAppend> op,
                           std::shared_ptr<ApplyContext> ctx);
  void ApplyOpClose(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAddBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAddBlockV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAbandonBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpUpdatePipeline(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpFsync(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpCommitBlockSynchronization(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpCloseFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDelete(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDeleteV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetReplication(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetReplicationV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpMkDir(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpMkDirV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRenameOld(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRenameOldV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRename(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRenameV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetPermissions(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetPermissionsV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetOwner(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetOwnerV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetGenstampV1(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetGenerationStampV1(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetGenstampV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetGenerationStampV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpTimes(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpTimesV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpConcatDelete(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSymlink(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSymlinkV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpReassignLease(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpReassignLeaseV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpMergeBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpUpdateBlocks(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpUpdateBlocksV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAllocateBlockId(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAllocateBlockIdV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetXattr(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetXattrV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRemoveXattr(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRemoveXattrV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetStoragePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetStoragePolicyV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetReplicaPolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetReplicaPolicyV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetDirReplicaPolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetDirReplicaPolicyV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAccessCounterSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetBlockInfo(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpApproveUploadBlk(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpPersistBlk(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDeleteDeprecatedBlockPufsInfo(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDelDepringBlks(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDelDepredBlks(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpUnsetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpSetAZBlacklist(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpFlushBlockInfoProtos(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpUpdateATimeProtos(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpNop(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDummy(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAllowSnapshotV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDisallowSnapshotV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpCreateSnapshotV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpDeleteSnapshotV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpRenameSnapshotV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpPin(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpReconcileINodeAttrs(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpPersistJobInfo(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpConcatV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpBatchCreateFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpBatchCompleteFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpBatchDeleteFile(std::shared_ptr<ApplyContext> ctx);

  void LogCfsOp(std::shared_ptr<ApplyContext> ctx);
  std::vector<std::string> GetSortedLockPathsFromPB(ApplyContext* apply_ctx);

  void ApplyCfsOpPhysical(std::shared_ptr<ApplyContext> apply_ctx);
  void ApplyCfsOpSetupClosure(
      std::shared_ptr<ApplyContext> apply_ctx,
      ApplyClosure* done,
      int64_t partition_id = -1,
      const std::function<void(const Status&)>& cb = nullptr);
  // directory tree related
  void ApplyCfsOpMkdir(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpDelete(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpRenameOld(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpRename(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSymlink(std::shared_ptr<ApplyContext> ctx);
  // file related
  void ApplyCfsOpOpenFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpConcat(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAppend(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAddBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAbandonBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpUpdatePipeline(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpFsync(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpCommitBlockSynchronization(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpUpdateBlocks(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpCloseFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpReassignLease(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAllocateBlockId(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetGenStampV1(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetGenStampV2(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpBatchCreateFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpBatchCompleteFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpBatchDeleteFile(std::shared_ptr<ApplyContext> ctx);
  // block upload related
  void ApplyCfsOpApproveUploadBlk(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpPersistBlk(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpDelDepringBlks(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpDelDepredBlks(std::shared_ptr<ApplyContext> ctx);
  // acc upload related
  void ApplyCfsOpAccUpdateBlockInfo(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpPin(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpReconcileINodeAttrs(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpPersistJobInfo(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAccSyncListingBatchAdd(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAccSyncListingBatchUpdate(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAccSyncUpdateINode(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAccSyncAddFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAccPersistFile(std::shared_ptr<ApplyContext> ctx);
  // inode attribute related
  void ApplyCfsOpSetReplication(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetStoragePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetReplicaPolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetDirReplicaPolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetPermissions(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetOwner(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetAcl(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetXAttrs(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpRemoveXAttrs(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetQuota(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetTimes(std::shared_ptr<ApplyContext> ctx);
  // feature related
  void ApplyCfsOpSetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpUnsetLifecyclePolicy(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpSetAZBlacklist(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpMergeBlock(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpAllowSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpDisallowSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpCreateSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpDeleteSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpRenameSnapshot(std::shared_ptr<ApplyContext> ctx);
  void ApplyCfsOpUpdateATimeProtos(std::shared_ptr<ApplyContext> ctx);

  void WaitNoPending();
  uint64_t NumPendingLogToApply();

  int64_t GetLastAppliedOrWrittenTxId();
  void TailerCatchupTxid();
  int64_t SinceLastCatchupTxid();

  size_t NumDynamicRWLocks() const;
  size_t NumStaticRWLocks() const;
  void BlockIndexStats(std::deque<size_t>& d) const;
  void BlockIndexDetail(DatanodeID id, std::deque<NameBlockIndex*>& d) const;
  void ParentMapStats(std::vector<size_t>& v) const;

  void GetByteCoolInfo(const INode& inode,
                       cnetpp::base::StringPiece& object_id,
                       cnetpp::base::StringPiece& temperature,
                       cnetpp::base::StringPiece& temperature_mtime);

  void ScanLifecyclePolicy(
      std::function<
          bool(const INodeID, const uint64_t, const LifecyclePolicyProto&)> cb);

  void InterruptGetContentSummary();
  void ResumeGetContentSummary();

  // Please refer document "HyperMFS Design Proposal".
  // https://bytedance.feishu.cn/wiki/wikcnMKAXl8mluncpjE4V0pPSpb#
  static bool IsHyperFile(const INode& inode);
  static bool IsHyperBlock(const INode& inode);
  bool FileExists(const std::string& path);
  Status ScanHyperFileAndHyperBlock(uint64_t parent_inode_id,
                                    const std::string& hyper_file_name,
                                    INode* hyper_file_inode,
                                    std::vector<INode>* hyper_block_inodes,
                                    uint64_t* stripe_width = nullptr);

  void CompleteHyperFileInfo(
      const INode& hyper_block_inode,
      std::unordered_map<FileStatusPtr, HyperCacheMeta>& hyperfile_meta_map,
      std::unordered_map<FileStatusPtr, int32_t>& hyperfile_scan_index);

  Status GetDirectoryStat(const std::string& path, INodeStat* stat);
  Status GetDirectoryStats(
      const google::protobuf::RepeatedField<INodeID>& inode_ids,
      cloudfs::GetINodeStatResponseProto* response);

  std::shared_ptr<MetaStorage> TestOnlyGetMetaStorage() {
    return meta_storage_;
  }
  SnapshotManager& TestOnlyGetSnapshotManager() {
    return *snapshot_manager_;
  }

  void GetDirSubINodes(const std::string& path,
                       const std::string& start_after,
                       int limit,
                       std::vector<INode>* sub_inodes,
                       bool* has_more);

  // For scrub
  Status StartScrub(ScrubOpType optype, ScrubAction action);
  Status StopScrub(ScrubOpType optype);
  Status GetScrubProgress(ScrubOpType optype, ScrubProgress* progress);
  Status GetScrubResult(ScrubOpType optype, ScrubResult* result);

  // For HTTP API
  Status GetFileInfoForHttp(const std::string& path,
                            bool need_location,
                            HdfsFileStatusProto* response);
  Status GetListingForHttp(const std::string& path,
                           const std::string& start_after,
                           int32_t count,
                           bool need_location,
                           bool allow_list_file,
                           GetListingForHttpResponse* response);
  std::vector<std::string> GetActiveClients() const;

  // For merge blocks
  class MergeTaskInfo {
   public:
    MergeTaskInfo(uint64_t offset,
                  uint32_t length,
                  uint32_t count,
                  size_t start_idx)
        : offset(offset), length(length), count(count), start_idx(start_idx) {
    }

    uint64_t offset;   // Start numByte in inode
    uint32_t length;   // Length of all candidate blocks
    uint32_t count;    // Count of candidate blocks
    size_t start_idx;  // Index of first candidate block in inode

    std::string ToString() const {
      std::stringstream ss;
      ss << "MergeTaskInfo: offset=" << offset << ", length=" << length
         << ", count=" << count << ", start_idx=" << start_idx;
      return ss.str();
    }
  };

  Status MergeBlocks(INodeID inode_id);
  Status MergeBlockGenPlan(
      const google::protobuf::RepeatedPtrField<cloudfs::BlockProto>& blocks,
      int reserved_block_count,
      std::vector<MergeTaskInfo>* merge_task_info);
  Status MergeBlockAcc(const std::string& path,
                       const std::vector<INode>& ancestors,
                       INodeInPath& iip);
  Status MergeBlockAccCheckBlocks(
      const INodeInPath& iip,
      google::protobuf::RepeatedPtrField<cloudfs::BlockProto>* persisted_blocks,
      google::protobuf::RepeatedPtrField<cloudfs::BlockProto>*
          to_be_persisted_blocks);
  Status MergeBlockAccMergePersistedBlocks(
      const std::string& path,
      const std::vector<INode>& ancestors,
      INodeInPath& iip,
      const google::protobuf::RepeatedPtrField<cloudfs::BlockProto>&
          persisted_blocks,
      const std::vector<MergeTaskInfo>& persisted_block_merge_task_info);
  Status MergeBlockHdfs(const std::string& path,
                        const std::vector<INode>& ancestors,
                        INodeInPath& iip);
  Status IsINodeMergingBlocks(INodeID inode_id, bool* merging);

  void TEST_StartUsageReporterInObserveMode() {
    StartUsageReporterInObserveMode();
  }

 private:
  void ProcessINodeStat();
  void RefreshINodeStatIfNecessaryInObserveMode();
  void CheckINodeStat();
  void StartUsageReporterInObserveMode();

  // Choose the appropriate read/write mode according to the client request
  // and file lifecycle policy.
  cloudfs::IoMode DecideIoMode(const INode& inode,
                               cloudfs::ExpectedIoMode request_io_mode,
                               const bool is_new_block);
  cloudfs::IoMode DecideIoModeForBlock(const DetailedBlock& blk,
                                       cloudfs::IoMode io_mode);

 private:
  friend struct NameSpaceMetrics;
  NameSpaceMetrics metrics_;

  RetryCache* retry_cache_{nullptr};

  HAStateBase* ha_state_{nullptr};
  SafeModeBase* safemode_{nullptr};
  std::atomic<bool> is_running_{false};
  std::unique_ptr<BIPWriteManager> bip_write_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  std::unique_ptr<BlockReportManager> block_report_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<JobManager> job_manager_;
  std::unique_ptr<LeaseManagerBase> lease_manager_;
  std::unique_ptr<LeaseMonitorBase> lease_monitor_;
  std::shared_ptr<MetaStorage> meta_storage_;
  std::unique_ptr<RWLockManager> lock_manager_;
  std::shared_ptr<BlockTokenSecretManager> block_token_secret_manager_{nullptr};
  std::shared_ptr<KeyManager> key_manager_{nullptr};
  std::unique_ptr<SnapshotManager> snapshot_manager_;

  std::shared_ptr<DataCenters> data_centers_;

  std::shared_ptr<AccessCounterManager> access_counter_manager_;

  // TODO set these variables in constructor
  UserGroupInfo ns_ugi_;
  bool is_perm_enabled_;

  int32_t layout_version_;
  int64_t ctime_;
  int64_t filesystem_id_;
  int64_t namespace_id_;
  cloudfs::NamespaceType namespace_type_;
  PersistentUfsInfo persistent_ufs_info_;
  std::string cluster_id_;
  std::string blockpool_id_;
  std::string software_version_;

  std::shared_ptr<EditLogContextBase> edit_log_ctx_{nullptr};
  // In product environment, NameSpace is the only owner of edit_log_sender_.
  // But in ut, due to the implement of MockEditLogSender,
  // NameSpace, BlockManager, DepringBlockRecycler and DepredBlockRecycler need
  // to share it.
  // TODO(ruanjunbin): Change the behavior of MockEditLogSender and
  // make edit_log_sender_ unique_ptr again.
  std::shared_ptr<EditLogSenderBase> edit_log_sender_{nullptr};

  std::atomic<int64_t> last_catchup_txid_ms_{0};

  std::atomic<uint64_t> last_inode_id_{kLastReservedINodeId};
  std::atomic<uint64_t> last_block_id_{kLastReservedBlockId};
  std::atomic<uint64_t> last_generation_stamp_v2_{kLastReservedGenerationStamp};

  // Block id generation and edit log send must be serial.
  std::atomic<uint64_t> last_ckpt_txid_;

  std::atomic<ApplyMode> editlog_apply_mode_{ApplyMode::LOGICAL};

  // Generation stamp generation and edit log send must be serial.
  std::mutex genstamp_v1_mutex_;
  std::atomic<uint64_t> generation_stamp_v1_;
  uint64_t generation_stamp_v1_limit_{0};

  std::atomic<bool> interrupt_get_content_summary_{false};

  std::unique_ptr<FileFinalizerBase> file_finalizer_{nullptr};

  friend class BGDeletionTask;
  friend class SnapshotGcTask;
  friend class BGCompactAllTask;
  friend class MetaScanner;
  friend class QuotaCollector;
  friend class BlockMap;
  friend class UploadProcessTest;
  friend class BlockManager;

  std::unique_ptr<cnetpp::concurrency::Thread> bg_deletion_worker_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> snapshot_gc_worker_{nullptr};
  std::shared_ptr<BGDeletionTask> bg_deletion_task_{nullptr};
  std::shared_ptr<SnapshotGcTask> snapshot_gc_task_{nullptr};
  std::unique_ptr<Checkpointer> checkpointer_{nullptr};

  std::shared_ptr<MetaScanner> meta_scanner_{nullptr};
  std::shared_ptr<MetaScannerBase> meta_scanner_v2_{
      nullptr};  // TODO(zhuangsiyu): Fix naming
  std::shared_ptr<LeaseScannerListener> lease_scanner_listener_{nullptr};
  std::shared_ptr<RecycleScanner> recycle_scanner_{nullptr};
  std::shared_ptr<LifecycleScanner> lifecycle_scanner_{nullptr};
  std::shared_ptr<AZMonitor> az_monitor_{nullptr};

  std::unique_ptr<PolicyManager> policy_manager_{nullptr};

  std::shared_ptr<HyperfileScannerListener> hyperfile_scanner_listener_{
      nullptr};

  std::shared_ptr<MergeBlockListener> merge_block_listener_{nullptr};

  // [SECTION] Meta Loader
  friend class FSImageLoader;
  friend class MockFSImageTransfer;
  void LocalSaveLayoutVersion(int32_t layout_version);
  void LocalSaveCTime(int64_t ctime);
  void LocalSaveFileSystemId(int64_t fsid);
  void LocalSaveNameSpaceId(int64_t nsid);
  void LocalSaveNameSpaceType(cloudfs::NamespaceType type);
  void LocalSavePersistentUfsInfo(const PersistentUfsInfo& info);
  void LocalSavePersistentFlags();
  void LocalSaveClusterId(const std::string& cluster_id);
  void LocalSaveBlockPoolId(const std::string& bpid);
  void LocalSaveGenerationStampV1(uint64_t gs1);
  void LocalSaveGenerationStampV2(uint64_t gs2);
  void LocalSaveGenerationStampV1Limit(uint64_t gs1_limit);
  void LocalSaveLastINodeId(INodeID inode_id);
  void LocalSaveLastAllocatedBlockId(uint64_t last_allocated_block_id);
  void LocalSaveLastCkptTxId(uint64_t last_transaction_id);
  void LocalSaveNumINodes(int64_t num_inodes);

  // Called when dancenn is starting up.
  // Load various information from meta storage, i.e. the generation stamp v1,
  // the generation stamp v2, the generation stamp v1 limit, the blockpool id,
  // the last inode id, the last block id, etc.
  void LoadFileSystemId();
  void LoadNameSpaceInfoFromVersionFile(std::string const& version_file);
  void LoadNameSpaceInfo();
  void LoadNameSpaceType();
  void LoadPersistentUfsInfo();
  void LoadLayoutVersion();
  void LoadCTime();
  void LoadNameSpaceId();
  void LoadClusterId();
  void LoadBlockPoolId();
  void LoadGenerationStampV1();
  void LoadGenerationStampV1Limit();
  void LoadLastINodeId();
  void LoadLastCkptTxId();

  uint64_t last_ckpt_txid() const;

  // [SECTION] Write Rpc Internal
  Status MoveToRecycleBinInternal(
      const std::string& src,
      const std::string& rb,
      const std::vector<cnetpp::base::StringPiece>& src_path_components,
      const std::vector<cnetpp::base::StringPiece>& rb_path_components,
      INodeInPath* rb_iip,
      INodeInPath* rb_parent_iip,
      std::vector<INode>* rb_ancestors);

  bool AsyncCreateFileTxn(const std::string& src,
                          const PermissionStatus& permission,
                          const NetworkLocationInfo& client_location,
                          const UserGroupInfo& ugi,
                          const LogRpcInfo& rpc_info,
                          const std::string& client_machine,
                          const CreateRequestProto* request,
                          CreateResponseProto* response,
                          RpcController* ctx,
                          RpcClosure* rpc_done,
                          bool need_lock,
                          std::function<Status(INode*)> process_ufs_info,
                          INodeSyncChecker* sync_checker);
  Status AsyncCreateFileWithAddBlock(
      const std::string& src,
      const NetworkLocationInfo& client_location,
      const UserGroupInfo& ugi,
      const cloudfs::RpcProtocolType& rpc_type,
      const cloudfs::ExpectedIoMode& expected_io_mode,
      bool strict_rep_num,
      StopWatchContext* rpc_sw_ctx,
      INode* inode,
      LocatedBlockProto* located_block,
      BlockInfoProto* bip,
      cloudfs::IoMode* write_mode_output,
      std::vector<DatanodeID>* targets_output);

  bool AsyncCreateSymlinkTxn(const std::string& target,
                             const std::string& link,
                             const PermissionStatus& perm,
                             const UserGroupInfo& ugi,
                             bool create_parent,
                             const LogRpcInfo& rpc_info,
                             RpcClosure* rpc_done);

  bool AsyncDeleteTxn(const std::string& src,
                      bool recursive,
                      const UserGroupInfo& ugi,
                      const LogRpcInfo& rpc_info,
                      RpcClosure* rpc_done);

  bool AsyncRenameTo2Txn(const std::string& src,
                         const std::string& dst,
                         bool overwrite,
                         const LogRpcInfo& rpc_info,
                         const UserGroupInfo& ugi,
                         RpcClosure* rpc_done);

  bool AsyncMkDirsTxn(const std::string& path,
                      const PermissionStatus& permission,
                      const UserGroupInfo& ugi,
                      bool create_parent,
                      bool inherit_perm,
                      RpcController* ctx,
                      const LogRpcInfo& rpc_info,
                      RpcClosure* rpc_done,
                      std::function<void(INode*)> fill_inode_func,
                      bool need_lock);

  Status RenameInternal(
      const std::string& src,
      const std::string& dst,
      const std::vector<::cnetpp::base::StringPiece>& src_path_components,
      const std::vector<::cnetpp::base::StringPiece>& dst_path_components,
      INodeInPath* src_iip,
      INodeInPath* dst_iip,
      INodeInPath* src_parent_iip,
      INodeInPath* dst_parent_iip,
      const UserGroupInfo& ugi,
      bool check_quota,
      std::vector<INode>* src_ancestors,
      std::vector<INode>* dst_ancestors);

  Status Rename2Internal(
      const std::string& src,
      const std::string& dst,
      const std::string& rb,
      const std::vector<cnetpp::base::StringPiece>& src_path_components,
      const std::vector<cnetpp::base::StringPiece>& dst_path_components,
      const std::vector<cnetpp::base::StringPiece>& rb_path_components,
      bool overwrite,
      bool* move_to_recycle_bin,
      INodeInPath* src_iip,
      INodeInPath* old_dst_iip,
      INodeInPath* new_dst_iip,
      INodeInPath* rb_iip,
      INodeInPath* src_parent_iip,
      INodeInPath* dst_parent_iip,
      INodeInPath* rb_parent_iip,
      const UserGroupInfo& ugi,
      bool check_quota,
      std::vector<INode>* src_ancestors,
      std::vector<INode>* dst_ancestors,
      std::vector<INode>* rb_ancestors);

  Status CommitLastBlockSyncNoCloseFile(
      const CommitBlockSynchronizationRequestProto& request,
      const std::string& path,
      const std::vector<INode>& ancestors,
      INodeInPath iip);

  // Rename hyperfile from src_path to dst_path.
  void RenameHyperFile(const std::string& src_path,
                       const std::string& dst_path,
                       const INode& src_hyper_file,
                       const INode& dst_hyper_file,
                       INode& src_parent,
                       uint64_t now_ms,
                       const LogRpcInfo& rpc_info,
                       RpcClosure* rpc_done,
                       INode* old_dst_inode,
                       std::vector<BlockProto>* block_to_del);

  // Scan the src hyperblocks according to src_path and add scanned inodes to
  // src_hyper_blocks. Construct dst hyperblocks according to dst_path and add
  // dst inodes to dst_hyper_blocks.
  Status RenameHyperFileInternal(const std::string& src_path,
                                 const std::string& dst_path,
                                 const INode& src_hyper_file,
                                 const INode& dst_hyper_file,
                                 std::vector<INode>& src_hyper_blocks,
                                 std::vector<INode>& dst_hyper_blocks);

  // caller should already acquired the dir lock
  // return value:
  // - exception means failed
  // - true means file closed
  // - false means file not closed
  Status ReleaseLeaseInternal(const std::string& path,
                              INodeInPath iip,
                              const std::vector<INode>& ancestors,
                              const std::string& orig_holder,
                              const std::string& new_holder,
                              bool close_file);

  bool ReassignLease(const std::string& holder,
                     const std::string& path,
                     const std::string& new_holder,
                     INodeInPath* iip);

  Status RecoverLeaseInternal(const std::string& src,
                              const INodeInPath& iip,
                              const std::vector<INode>& ancestors,
                              const std::string& client,
                              bool force,
                              bool close_file);

  // [SECTION] Read Rpc Internal
  Status ComputeAndConvertContentSummary(
      const std::string& path,
      const uint32_t depth,
      const INodeInPath& iip,
      ContentCounter* counts,
      ContentSummaryProto* summary,
      const MetaStorageIterPtr iter = nullptr);

  // is_snapshot_read: set to false if used for write rpcs which only see
  // current directory tree.
  uint64_t ComputeFileSize(const INode& inode,
                           bool is_snapshot_read,
                           bool include_last_uc,
                           bool use_preferred_size_4_uc,
                           bool* last_blk_complete = nullptr);

  uint64_t ComputeFileSizeNoLock(
      const INode& inode,
      const std::vector<DetailedBlock>& detail_blocks,
      bool include_last_uc,
      bool use_preferred_size_4_uc,
      bool* last_blk_complete = nullptr);

  uint64_t ComputeFileSizeByInodeNoLock(const INode& inode,
                                        const DetailedBlock& last_block,
                                        bool include_last_uc,
                                        bool use_preferred_size_4_uc,
                                        bool* last_blk_complete = nullptr);

  // [SECTION] Apply Internal
  Status DeleteInternal(
      const std::string& src,
      const std::string& rb,
      const std::vector<cnetpp::base::StringPiece>& src_path_components,
      const std::vector<cnetpp::base::StringPiece>& rb_path_components,
      bool recursive,
      INodeInPath* src_iip,
      INodeInPath* src_parent_iip,
      bool* move_to_recycle_bin,
      INodeInPath* rb_iip,
      INodeInPath* rb_parent_iip,
      const UserGroupInfo& ugi,
      std::vector<INode> *src_ancestors,
      std::vector<INode> *rb_ancestors,
      RpcClosure* rpc_done);

  // [SECTION] BlockLoader
  // ACC namespace load write back task also.
  friend class BlockLoader;
  void LoadBlockMap();

  // [SECTION] Checker
  Status CheckSafeMode();
  bool CheckSafeMode(Closure* done);

  Status CheckClientTxid(RpcController* ctx);
  void SetServerTxid(RpcClosure* rpc_done, int64_t txid);
  void SetServerTxid(RpcController* ctx, int64_t txid);

  bool CheckValidPath(
      Closure* done,
      const std::string& path,
      std::vector<cnetpp::base::StringPiece>* path_components,
      std::vector<::cnetpp::base::StringPiece>* lock_components);

  void CheckAndWriteLockPath(
      RpcClosure* done,
      const std::string& path,
      std::vector<cnetpp::base::StringPiece>* path_components);

  void CheckAndWriteLockTree(
      RpcClosure* done,
      const std::string& src,
      const std::string* dst,
      const std::string* rb,
      std::vector<cnetpp::base::StringPiece>* src_path_components,
      std::vector<cnetpp::base::StringPiece>* dst_path_components,
      std::vector<cnetpp::base::StringPiece>* rb_path_components);

  void CheckAndWriteLockTree(
      RpcClosure* done,
      const std::vector<std::string>& sorted_paths,
      std::vector<cnetpp::base::StringPiece>* parent_path_components,
      std::vector<cnetpp::base::StringPiece>* children_path_components);

  Status CheckOwner(const FsPermissionChecker& pc,
                    const INode& inode,
                    const std::vector<cnetpp::base::StringPiece>& path_components,
                    INodeHint inode_hint,
                    const std::string& path);

  Status CheckPathAccess(const FsPermissionChecker& pc,
                         const std::string& path,
                         const std::vector<INode>& ancestors,
                         const INode& inode,
                         const std::vector<cnetpp::base::StringPiece>& path_components,
                         FsAction access,
                         INodeHint inode_hint,
                         bool flush_audit_at_last);

  Status CheckParentAccess(const FsPermissionChecker& pc,
                           const std::string& path,
                           const std::vector<INode>& ancestors,
                           const INode& inode,
                           const std::vector<cnetpp::base::StringPiece>& path_components,
                           FsAction access,
                           INodeHint inode_hint,
                           bool flush_audit_at_last);

  Status CheckAncestorAccess(const FsPermissionChecker& pc,
                             const std::string& path,
                             const std::vector<INode>& ancestors,
                             const INode& inode,
                             const std::vector<cnetpp::base::StringPiece>& path_components,
                             FsAction access,
                             INodeHint inode_hint,
                             bool flush_audit_at_last);

  Status CheckSubAccess(const FsPermissionChecker& pc,
                        const std::string& path,
                        const std::vector<INode>& ancestors,
                        const INode& inode,
                        const std::vector<cnetpp::base::StringPiece>& path_components,
                        FsAction access,
                        INodeHint inode_hint,
                        bool flush_audit_at_last);

  Status CheckTraverse(const FsPermissionChecker& pc, const std::string& path);

  void GetFileBlocksInternal(const INode& inode, std::vector<BlockProto>* blks);

  void GetDetailedBlocksInternal(const INode& inode,
                                 bool is_snapshot_read,
                                 std::vector<DetailedBlock>* blks,
                                 bool need_storage_class_report = false);

  std::pair<uint64_t, DetailedBlock> GetDetailedBlocksByRangeInternal(
      const INode& inode,
      bool is_snapshot_read,
      std::vector<DetailedBlock>* blks,
      uint64_t offset,
      uint64_t len);

  bool IsNonemptyDir(const INode& inode);

  bool CheckFileProgress(const std::vector<BlockProto>& blocks, bool check_all);

  Status CheckLease(const std::string& holder,
                    const std::string& path,
                    const INode& inode);
  void AddLease(const INode& inode);
  void RemoveLease(const INode& inode);

  Status CheckRenameSrc(
      const std::vector<::cnetpp::base::StringPiece>& path_components,
      const std::string& src,
      INodeInPath* iip,
      INodeInPath* parent_iip,
      const UserGroupInfo& ugi,
      std::vector<INode>* p_ancestors = nullptr);

  Status CheckRenameDst(
      std::vector<::cnetpp::base::StringPiece> path_components,
      const std::string& src,
      const std::string& dst,
      const INode& sinode,
      INodeInPath* dparent_iip,
      std::string* new_dst_full,
      std::string* new_dst_local,
      const UserGroupInfo& ugi,
      std::vector<INode>* p_ancestors = nullptr);

  Status CheckRenameDst2(
      const std::vector<::cnetpp::base::StringPiece>& path_components,
      const std::string& src,
      const std::string& dst,
      const INode& sinode,
      INodeInPath* dst_parent_iip,
      INodeInPath* dst_iip,
      const UserGroupInfo& ugi,
      std::vector<INode>* p_old_dst_ancestors = nullptr);

  Status CheckMountPoint(
      const std::string& src,
      const std::vector<cnetpp::base::StringPiece>& path_components,
      const std::vector<INode>& ancestors);
  bool CheckMountPoint(const INode& parent, const std::string& sub_name);

  Status CheckPipelineUpdate(const std::string& client,
                             const std::string& path,
                             const INode& inode,
                             uint64_t inode_id,
                             uint64_t block_id);

  bool CheckOperation(Closure* done, OperationsCategory op);
  Status CheckOperation(vshared_lock* ha_barrier, OperationsCategory op);

  // For writing methods, check it's allowed to write on current node
  bool CheckValidPathAndCanWrite(const std::string& path,
                                 StringPieces* path_components,
                                 StringPieces* lock_components,
                                 RpcClosure* rpc_done);

  // [SECTION] MetaStorage Related
  bool GetParent(const std::vector<cnetpp::base::StringPiece>& path_components,
                 INode* parent,
                 std::vector<INode>* ancestors = nullptr,
                 MetaStorageSnapPtr snapshot = nullptr);
  bool GetParent(const std::vector<cnetpp::base::StringPiece>& path_components,
                 INodeInPath* parent_iip,
                 std::vector<INode>* ancestors = nullptr,
                 MetaStorageSnapPtr snapshot = nullptr);

  /*
   * MkdirOp requires on NO inheritance.
   * WriteFileOP requires on inheritance.
   *
   * Implicitly created dir would have u+wx implicitly.
   * */
  Status GetParentCreateIfMissing(
      const std::string& path,
      const std::vector<cnetpp::base::StringPiece>& path_components,
      const std::vector<::cnetpp::base::StringPiece>& lock_components,
      const PermissionStatus& permission,
      const UserGroupInfo& ugi,
      INodeInPath* parent_iip,
      RWLockManager::PathLockMap* locks,
      bool inherit_perm,
      bool* perm_checked,
      std::vector<INode>* ancestors,
      RpcController* c,
      INodeHint perm_inode_hint,
      std::function<void(INode*)> fill_inode_func = nullptr,
      bool* created_missing = nullptr,
      Closure* done = nullptr);

  // open a closed file for append
  bool PrepareFileForWriteInternal(const std::string& path,
                                   const std::string& client_name,
                                   const std::string& client_machine,
                                   bool is_active,
                                   INode& file,
                                   const UserGroupInfo& ugi,
                                   LocatedBlockProto* block);

  static void DoConcatFileToTarget(const std::vector<INode>& src_inodes,
                                   std::vector<BlockInfoProto>* target_bips,
                                   INode* target_inode);

  // [SECTION] Helper
  void ConstructFileStatus(const std::string& path,
                           const INode& inode,
                           bool is_snapshot_read,
                           bool need_location,
                           const NetworkLocationInfo& client_location,
                           const UserGroupInfo& ugi,
                           const std::vector<AccessMode>& modes,
                           ::cloudfs::HdfsFileStatusProto* file_status,
                           const std::string* pufs_name = nullptr);

  // [SECTION] Helper
  void ConstructHyperFileStatus(const std::string& path,
                                const INode& hyper_file_inode,
                                const std::vector<INode>& hyper_block_inodes,
                                ::cloudfs::HdfsFileStatusProto* file_status);

  // Only used for write
  void CreateLocatedBlock(const INode& inode,
                          const Block& block,
                          uint64_t offset,
                          cloudfs::IoMode write_mode,
                          const std::string& pufs_name,
                          const std::vector<DatanodeID>& targets,
                          const UserGroupInfo& ugi,
                          const std::vector<AccessMode>& modes,
                          LocatedBlockProto* lb);

  // is_snapshot_read: if true, we are reading snapshot path, and we should make
  // the last block not uc and use block length from `inode`
  // BlockManager::FillUploadCmdAccMode call this method without taking path lock!
  void CreateLocatedBlocks(
      const INode& inode,
      uint64_t offset,
      uint64_t len,
      cloudfs::IoMode write_mode,
      ReadAdvice& advice,
      bool is_snapshot_read,
      bool need_sort,
      const NetworkLocationInfo& client_location,
      const UserGroupInfo& ugi,
      const std::vector<AccessMode>& modes,
      LocatedBlocksProto* lb,
      const std::string* pufs_name = nullptr,
      const std::function<void(const std::vector<DetailedBlock>& blk)>&
          block_callback = nullptr);

  // is_snapshot_read: see comments of `CreateLocatedBlocks`
  void CreateLocatedBlocksRangeBased(const INode& inode,
                                     uint64_t offset,
                                     uint64_t len,
                                     cloudfs::IoMode write_mode,
                                     ReadAdvice& advice,
                                     bool is_snapshot_read,
                                     bool need_sort,
                                     const NetworkLocationInfo& client_location,
                                     const UserGroupInfo& ugi,
                                     const std::vector<AccessMode>& modes,
                                     LocatedBlocksProto* lb,
                                     const std::string* pufs_name = nullptr);
  bool IsBlockPersisted(const INode& inode, const DetailedBlock& block);
  std::string GetBlockEtag(const INode& inode, BlockID block_id);

  void FillInLocatedBlock(const Block& block,
                          uint64_t offset,
                          cloudfs::IoMode write_mode,
                          const std::string& pufs_name,
                          const std::string& user,
                          const std::vector<AccessMode>& modes,
                          bool pinned,
                          bool persisted,
                          const std::string& etag,
                          LocatedBlockProto* lb);

  std::pair<uint64_t, std::vector<DetailedBlock>> GetBlocksInRange(
      const INode& inode,
      uint64_t offset,
      uint64_t len,
      std::vector<DetailedBlock>* blocks);

  void ConstructDefaultBlockToken(TokenProto* token_proto);

  void ConstructRealBlockToken(const std::string& user,
                               uint64_t block_id,
                               const std::vector<AccessMode>& modes,
                               TokenProto* token_proto);

  Status SetStoragePolicyInternal(const std::string& path,
                                  uint32_t policy_id,
                                  INode* inode);

  bool CheckINodeInRecycleBin(const INode& inode,
                              const std::vector<INode>& ancestors);

  bool GetRecyclePolicyRecursive(const INode& inode,
                                 const std::vector<INode>& ancestors,
                                 RecyclePolicy* ans);

  bool CanMoveToRecycleBin(const INode& inode,
                           const std::vector<INode>& ancestors);

  bool GetCreateRpcInfo(const INode& inode, LogRpcInfo* rpc_info);

  void SetReplicationInternal(
      const std::string& path,
      const std::vector<cnetpp::base::StringPiece>& path_components,
      uint32_t replica,
      bool need_recover,
      const UserGroupInfo& ugi,
      RpcClosure* rpc_done);

  Status PrepareSetLifecyclePolicy(
      const std::string& path,
      const std::vector<cnetpp::base::StringPiece> path_components,
      const LifecyclePolicy& policy,
      INode* inode);

  void CommitSetLifecyclePolicy(const std::string& path,
                                const INode& inode,
                                const uint64_t ts,
                                const LifecyclePolicy& policy,
                                const LogRpcInfo& rpc_info,
                                RpcClosure* done);

  Status PrepareUnsetLifecyclePolicy(
      const std::string& path,
      const std::vector<cnetpp::base::StringPiece> path_components,
      INode* inode);

  Status PrepareUnsetDepredLifecyclePolicy(const INodeID id);

  void CommitUnsetLifecyclePolicy(const INodeID id,
                                  const std::string* path,
                                  const LogRpcInfo& rpc_info,
                                  RpcClosure* done);

  Status PrepareGetLifecyclePolicy(
      const std::string& path,
      const std::vector<cnetpp::base::StringPiece> path_components,
      LifecyclePolicy* policy);

  uint32_t AdjustReplication(uint32_t replica);

  Status VerifyReplication(const std::string& path,
                           uint32_t replica,
                           bool allow_zero_replica = false);

  /*
   * Validate extended attribute update for HyperFile/HyperBlock.
   * For HyperFile, only HyperBlock info can be updated.
   * For HyperBlock, no extended attributes can be updated
   */
  Status VerifyHyperCacheMeta(const INode& inode, const XAttrProto& xattr);

  RWLockManager::PathLockMapPtr ReadLockPath(
      const std::vector<::cnetpp::base::StringPiece>& comp);

  RWLockManager::PathLockMapPtr WriteLockPath(
      const std::vector<::cnetpp::base::StringPiece>& comp);

  void WriteLockThreePaths(const std::vector<cnetpp::base::StringPiece>* first,
                           const std::vector<cnetpp::base::StringPiece>* second,
                           const std::vector<cnetpp::base::StringPiece>* third,
                           RWLockManager::PathLockMap* first_lock,
                           RWLockManager::PathLockMap* second_lock,
                           RWLockManager::PathLockMap* third_lock);

  RWLockManager::DirTreeLockPtr ReadLockTree(
      const std::vector<cnetpp::base::StringPiece>* comp);

  RWLockManager::DirTreeLockPtr WriteLockTree(
      const std::vector<cnetpp::base::StringPiece>* src_lock_comp,
      const std::vector<cnetpp::base::StringPiece>* dst_lock_comp = nullptr,
      const std::vector<cnetpp::base::StringPiece>* rb_lock_comp = nullptr);

  RWLockManager::DirTreeLockPtr ReadLockManyPaths(
      const std::vector<std::vector<::cnetpp::base::StringPiece>>& comps);
  RWLockManager::DirTreeLockPtr WriteLockManyPaths(
      const std::vector<std::vector<::cnetpp::base::StringPiece>>& comps);

  RWLockManager::DirTreeLockPtr LockManyPathsDFS(
      const std::vector<std::vector<::cnetpp::base::StringPiece>>& comps,
      bool write_lock);

  RWLockManager::DirTreeLockPtr LockManyPathsDictionary(
      const std::vector<std::vector<::cnetpp::base::StringPiece>>& comps,
      bool write_lock);

  uint32_t DetermineStoragePolicy(const std::vector<INode>& ancestors);

  uint16_t GetPermissionMask(uint32_t parent_mask,
                             uint32_t mask,
                             bool inherit_permission,
                             bool is_last_inode);

  void LoadReservedINodes();
  INode GetRootINode();

  // Only called by FSImageLoader.
  // This method does not update last inode id in rocksdb.
  void InsertINode(std::shared_ptr<INode> inode);
  void InsertINodes(const std::deque<std::shared_ptr<INode>>& inodes);

  struct BGDContext {
    std::vector<INode> pd_inodes;
    std::vector<INode> dangling_dirs;
    std::vector<INode> dangling_files;
    uint64_t todel_blocks{0};
  };
  struct BGDStat {
    uint64_t num_pd_processed{0};
    uint64_t num_pd_removed{0};
    uint64_t num_dangling_processed{0};
    uint64_t num_dangling_removed{0};
    uint64_t num_blk_removed{0};
  };
  int64_t ProcessPendingDeleteCF(std::function<bool(int64_t)> should_stop);
  void ProcessPendingDeleteINode(const INode& pd_inode,
                                 BGDContext* bgd_ctx,
                                 BGDStat* bgd_stat);
  bool ProcessDanglingINodes(const INode& parent_inode,
                             BGDContext* bgd_ctx,
                             BGDStat* bgd_stat);
  void RemovePDAndDanglingINodes(BGDContext* bgd_ctx,
                                 BGDStat* bgd_stat,
                                 bool force);
  void RemovePendingDeleteINodes(const std::vector<INode>& pd_inodes);
  void RemoveDanglingINodes(const std::vector<INode>& dangling_files,
                            const std::vector<INode>& dangling_dirs);

  // incremental_gc: if true, process based on delta of snapshot and inode data,
  // else process based on all the data, see implementation for detail.
  int64_t SnapshotGc(const std::function<bool(int64_t)>& should_stop,
                     bool incremental_gc);

  // consists of two snapshots' create_txid, they are adjacent to each other.
  using SnapshotMergeRange = std::pair<int64_t, int64_t>;
  using SnapshotMergeRanges = std::vector<SnapshotMergeRange>;
  // What snapshot gc do:
  // For inode table, actually remove mark deleted snapshots at last.
  // For snapshot inode table, remove unnecessary inodes.
  struct SnapshotRootGcInfo {
    SnapshotMergeRanges merge_ranges;
    std::unordered_set<uint64_t> to_delete_snapshot_ids;
    // valid when all snapshots deleted. Note user may add snapshot during gc.
    uint64_t snapshot_txid_lower_bound = 0;
    std::string path;
    // if there exist snapshot inode that should be deleted but not deleted
    // due to referred by other snapshot inode, we'd better keep the snapshot.
    bool can_delete_snapshot = true;
  };
  using SnapshotRootIdToGcInfo =
      std::unordered_map<INodeID, SnapshotRootGcInfo>;

  // snapshot_txid_lower_bound: valid when no snapshot root found, we can delete
  // snapshot inode earlier than snapshot_txid_lower_bound
  void GatherMarkDeletedSnapshots(
      const std::vector<std::pair<INode, std::string>>& snapshot_root_inodes,
      SnapshotRootIdToGcInfo* snapshots_gc_info,
      bool incremental_gc);

  // delete snapshotted inodes which are not needed any more.
  // return: deleted inodes num
  int64_t DeleteDanglingSnapshottedINodes(
      const std::function<bool(int64_t)>& should_stop,
      SnapshotRootIdToGcInfo& snapshots_gc_info,
      bool incremental_gc);

  // delete snapshots which finished gc, we cannot simply remove mark deleted
  // snapshots, because new snapshots maybe added and marked deleted during gc.
  int64_t RemoveAlreadyGcSnapshots(
      const SnapshotRootIdToGcInfo& snapshots_gc_info);

  Status GetSnapshotRoots(
      std::vector<std::pair<INode, std::string>>* snapshot_root_inodes);

  // used by FSImageLoader only
  void LoadLease(const INode& inode);

  // edit logs related
  void UpdateBlocks(const std::string& path,
                    INode* file,
                    const std::vector<BlockProto>& new_blocks,
                    bool should_complete_last_block,
                    std::shared_ptr<ApplyContext> ctx,
                    BlockID* last_blk_to_delete,
                    bool bind_callback);

  // called in standby only
  void UpdateLastINodeId(uint64_t inode_id_from_op);
  void UpdateLastAllocatedBlockId(uint64_t block_id_from_op);
  void UpdateGenerationStampV2(uint64_t gs_v2_from_op);

  void AddRetryCacheEntry(const std::string& client_id,
                          uint32_t call_id,
                          PayloadTypePtr payload);

  Status GetDNBelongToHyperFile(
      const INode& hyper_block,
      const std::string& hyper_file_name,
      std::unordered_set<DatanodeInfoPtr>& existing_datanodes);

  // Acc Mode related
  Status CheckPathAndSplitComponents(
      const std::string& src,
      bool allow_src_root,
      std::vector<cnetpp::base::StringPiece>* path_components,
      std::vector<cnetpp::base::StringPiece>* lock_components);
  Status CheckOperationSafeMode(vshared_lock* ha_barrier,
                                OperationsCategory op);
  Status ProcessFileSyncActionCreate(
      const std::shared_ptr<FileSyncContext>& ctx,
      const UfsFileStatus& file,
      RpcClosure* done);
  Status ProcessFileSyncActionDelete(
      const std::shared_ptr<FileSyncContext>& ctx,
      RpcClosure* done);
  Status ProcessFileSyncActionOverwrite(
      const std::shared_ptr<FileSyncContext>& ctx,
      const UfsFileStatus& file,
      RpcClosure* done);
  Status ProcessFileSyncActionUpdateTime(
      const std::shared_ptr<FileSyncContext>& ctx,
      RpcClosure* done);
  void MakeInodeAndBlocksFromUfs(uint64_t parent_id,
                                 const std::string& name,
                                 const PermissionStatus& perm,
                                 const UfsFileStatus& file,
                                 INode* node,
                                 std::vector<BlockInfoProto>* block_infos);

  Status SyncUfsFileInternal(const std::string& ufs_path,
                             const std::string& inner_path,
                             const PermissionStatus& p,
                             const UserGroupInfo& ugi,
                             const std::shared_ptr<Ufs>& ufs,
                             bool sync_on_ancestor_not_dir,
                             uint64_t target_sync_ts,
                             StopWatchContext* rpc_sw_ctx = nullptr,
                             TriggerSyncReason trigger_sync_reason = UNKNOWN);
  Status MkdirsUfsInternal(const std::string& ufs_path,
                           const std::string& inner_path,
                           const PermissionStatus& p,
                           const UserGroupInfo& ugi,
                           const std::shared_ptr<Ufs>& ufs,
                           bool create_parent,
                           RpcController* ctx,
                           SyncTask* sync_task);

  Status PrepareSyncUfsFile(const std::string& ufs_path,
                            const std::string& inner_path,
                            const PermissionStatus& p,
                            const UserGroupInfo& ugi,
                            const std::shared_ptr<Ufs>& ufs,
                            bool sync_on_ancestor_not_dir,
                            std::shared_ptr<FileSyncContext>* ctx,
                            std::unique_ptr<LockedPath>* lp,
                            vshared_lock* ha_barrier,
                            StopWatchContext* rpc_sw_ctx = nullptr,
                            bool need_log = true);
  std::pair<Status, std::unique_ptr<LockedPath>>
  PrepareSyncUfsFileResolvePathAndLock(
      const std::shared_ptr<FileSyncContext>& ctx,
      StopWatchContext* rpc_sw_ctx = nullptr);
  Status SyncUfsFileInnerWithWriteLock(
      const std::shared_ptr<FileSyncContext>& ctx,
      StopWatchContext* rpc_sw_ctx = nullptr);
  Status SyncUfsFileOnlyWithWriteLock(
      const std::shared_ptr<FileSyncContext>& ctx,
      StopWatchContext* rpc_sw_ctx = nullptr);
  Status ApplyFileSyncAction(FileSyncAction action,
                             const std::shared_ptr<FileSyncContext>& ctx,
                             const UfsFileStatus& file_status,
                             StopWatchContext* rpc_sw_ctx = nullptr);

  Status CreateMissingParentInSync(const std::shared_ptr<FileSyncContext>& ctx);
  Status CreateMissingParentLocal(
      const std::shared_ptr<Ufs>& ufs,
      const std::string& ufs_path,
      const std::string& inner_path,
      const std::vector<cnetpp::base::StringPiece>& path_comps,
      const std::vector<cnetpp::base::StringPiece>& lock_comps,
      const PermissionStatus& perm,
      const RichPath& rp,
      bool inherit_parent_attrs,
      std::vector<INode>* ancestors,
      INode* parent);

  Status PrepareUfsFileListing(const std::shared_ptr<ListingOption>& opt,
                               const std::shared_ptr<Ufs>& ufs,
                               std::shared_ptr<FileListingSyncContext>* ctx,
                               std::unique_ptr<LockedPath>* lp,
                               vshared_lock* ha_barrier);
  Status DoSyncUfsDir(const std::shared_ptr<FileListingSyncContext>& ctx);
  Status ListUfsDirWithRetry(std::shared_ptr<Ufs>& ufs,
                             const std::string& path,
                             const ListFilesOption& option,
                             ListFilesResult* result);

  Status ListUfsDirLockAndCheckINode(INode* inode,
                                     const std::string& inner_path,
                                     bool write_lock,
                                     std::unique_ptr<LockedPath>* lp);
  Status ListUfsDirMarkPrefetchFailed(
      const std::shared_ptr<FileListingSyncContext>& ctx);
  Status ProcessUfsDirChildren(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<UfsFileStatus>& files);
  Status GenerateSyncActionsForUfsFiles(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<UfsFileStatus>& files,
      UfsDirSyncActions& actions);
  Status FinishSyncUfsDirChildren(
      const std::shared_ptr<FileListingSyncContext>& ctx);
  Status ApplyUfsDirSyncAction(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const UfsDirSyncActions& actions);
  Status ApplyUfsDirSyncActionCreateFile(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<INodeWithBlocks>& new_ufs_files);
  Status ApplyUfsDirSyncActionCreateFileOneBatch(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<INodeWithBlocks>& new_ufs_files);
  Status ApplyUfsDirSyncActionCheckFile(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::string& local_file_name,
      const INode& local_file);
  Status ApplyUfsDirSyncActionUpdateFileSyncTime(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<INode>& update_time_ufs_files,
      const std::vector<INode>& old_ufs_files);
  Status ApplyUfsDirSyncActionUpdateFileSyncTimeOneBatch(
      const std::shared_ptr<FileListingSyncContext>& ctx,
      const std::vector<INode>& update_time_ufs_files,
      const std::vector<INode>& old_ufs_files);

  Status PrepareRenameUfs(const std::shared_ptr<RenameToOption>& opt,
                          const std::shared_ptr<Ufs>& ufs,
                          std::shared_ptr<RenameSyncContext>* c,
                          std::unique_ptr<LockedPathVector>* lp,
                          vshared_lock* ha_barrier);
  Status RenameUfsCheckSrcDst(const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsCheckSrcDstFilesLimit(
      const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsRemoveDst(const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsCopyUfsFiles(const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsHandleCopyResult(
      const std::shared_ptr<RenameSyncContext>& ctx,
      LRUCache<std::string, INodeInPath, true>& dir_cache,
      LRUCache<std::string, int, true>& dir_negative_cache,
      uint64_t copy_time_stamp,
      const std::unordered_map<std::string, std::string>& result);
  Status RenameUfsDeleteUfsFiles(const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsRenameRemote(const std::shared_ptr<RenameSyncContext>& ctx);
  Status RenameUfsRenameLocal(const std::shared_ptr<RenameSyncContext>& ctx);
  Status FinishRenameUfs(const std::shared_ptr<RenameSyncContext>& ctx);
  Status CreateMissingDstParentInRename(
      const std::shared_ptr<RenameSyncContext>& ctx);

  Status PrepareDeleteFromUfs(const std::shared_ptr<DeleteOption>& opt,
                              const std::shared_ptr<Ufs>& ufs,
                              std::shared_ptr<DeleteSyncContext>* c,
                              std::unique_ptr<LockedPath>* lp,
                              vshared_lock* ha_barrier);
  Status PreCheckDeleteUfsFiles(const std::shared_ptr<DeleteSyncContext>& ctx);
  Status DeleteUfsDeleteUfsFiles(const std::shared_ptr<DeleteSyncContext>& ctx);
  Status DeleteUfsDeleteLocal(const std::shared_ptr<DeleteSyncContext>& ctx);

  void UpdateINodeForPersistedFile(const std::string& path,
                                   const INode& parent,
                                   const INode& inode,
                                   const INode& old_inode,
                                   Closure* done);

  // For standby apply
  void AccSyncListingBatchAddWriteLock(
      RpcClosure* done,
      const AccSyncListingBatchAddOpBody& op,
      std::vector<cnetpp::base::StringPiece>* path_components,
      std::vector<std::string>* subfile_paths);
  void ApplyOpAccSyncListingBatchAdd(std::shared_ptr<ApplyContext> ctx);

  void AccSyncListingBatchUpdateWriteLock(
      RpcClosure* done,
      const AccSyncListingBatchUpdateOpBody& op,
      std::vector<cnetpp::base::StringPiece>* path_components,
      std::vector<std::string>* subfile_paths);
  void ApplyOpAccSyncListingBatchUpdate(std::shared_ptr<ApplyContext> ctx);

  void ApplyOpAccSyncUpdateINode(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAccSyncAddFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAccPersistFile(std::shared_ptr<ApplyContext> ctx);
  void ApplyOpAccUpdateBlockInfo(std::shared_ptr<ApplyContext> ctx);

  void PersistCreatedJobInfo(const std::string& job_id,
                             const JobInfoOpBody_Type job_type,
                             RpcClosure* rpc_done);
  Status UpdateTtlATime(const std::string& path, INodeID inode_id);

  static Status BatchApiPathsPrecheck(
      const std::vector<std::string>& paths,
      std::string* parent_path,
      std::vector<std::string>* sorted_paths,
      std::unordered_map<std::string, int>* path_index_table);
  static Status BatchApiPathsParse4Lock(
      const std::vector<std::string>& sorted_paths,
      std::vector<std::vector<cnetpp::base::StringPiece>>* path_components_all,
      std::vector<std::vector<cnetpp::base::StringPiece>>* lock_components_all,
      std::vector<cnetpp::base::StringPiece>* parent_path_components,
      std::vector<cnetpp::base::StringPiece>* children_path_components);
  Status BatchApiPathsGetINodes(
      const std::string& parent_path,
      const std::vector<std::string>& sorted_paths,
      const std::vector<cnetpp::base::StringPiece>& parent_path_components,
      const std::vector<cnetpp::base::StringPiece>& children_path_components,
      bool break_if_src_found,
      bool break_if_src_notfound,
      bool push_even_if_src_notfound,
      MetaStorageSnapPtr snapshot,
      bool is_for_read,
      std::vector<INode>* ancestors,
      INode* parent_inode,
      std::vector<INode>* src_inodes);

  Status BatchCreatePrecheck(
      const std::string& parent_path,
      const std::vector<std::string>& sorted_paths,
      const std::unordered_map<std::string, int>& path_index_table,
      const std::vector<cnetpp::base::StringPiece>& parent_path_components,
      const std::vector<cnetpp::base::StringPiece>& children_path_components,
      const BatchCreateFileRequestProto* request,
      BatchCreateFileResponseProto* response,
      RpcClosure* rpc_done);
  Status BatchCreateFileOneEntry(
      // file argument
      const std::string& path,
      const std::string& ufs_key,
      const std::string& inode_name,
      const cloudfs::BatchCreateFileEntry& request_entry,
      cloudfs::BatchCreateFileResponseEntry* response_entry,
      // common argument
      const BatchCreateFileRequestProto* request,
      const NetworkLocationInfo& client_location,
      const std::string& client_machine,
      const UserGroupInfo& ugi,
      const PlacementAdvice& advice,
      const int64_t time_now_ms,
      const INode& parent_inode,
      const uint32_t storage_policy_id,
      const LogRpcInfo& rpc_info,
      // output
      INode* src_inode,
      BlockInfoProto* add_bip,
      cloudfs::IoMode* write_mode,
      std::vector<DatanodeID>* targets);
  // bips only return last bip
  Status BatchCompleteFileSingleFileEntry(
      const std::string& path,
      const INode& inode,
      const cloudfs::BatchCompleteFileEntry& request_entry,
      INode* output_inode,
      std::vector<BlockInfoProto>* bips,
      std::vector<BlockInfoProto>* bips_deleted);
  Status BatchCompleteFileConcatFileEntry(
      const std::string& target_path,
      const std::vector<std::string>& src_paths,
      const INode& target_inode,
      const std::vector<INode>& src_inodes,
      const cloudfs::BatchCompleteFileConcatEntry& request_entry,
      const std::string& parent_path,
      INode* output_inode,
      std::vector<BlockInfoProto>* bips,
      std::vector<BlockInfoProto>* bips_deleted);

  void DeleteINodeCallback(const INode& inode,
                           const std::string& path = "",
                           StopWatchContext* rpc_sw_ctx = nullptr);

 private:
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>> apply_threads_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> bg_thread_pool_;
  std::unique_ptr<TimerTaskManager> timer_task_manager_;
  int64_t cleanup_ttl_atime_task_id_{0};
  std::unique_ptr<TtlATimeCollector> ttl_access_collector_;

  std::unique_ptr<Throttler> throttler_{nullptr};

  std::shared_ptr<NameSpaceScrubRunner> scrub_runner_;
  std::unique_ptr<INodeStatChecker> stat_checker_;
  std::shared_ptr<NamespaceUsageReporter> usage_reporter_;
  std::unique_ptr<QuotaManagerWrapper> quota_mgr_;

  friend class CkptCatcher;
  friend class MockNameSpace;
  friend class ActiveStandbySyncEditlogTest;
  FRIEND_TEST(NameSpaceTest, GetBlocksInRange);
  FRIEND_TEST(NameSpaceTest, WriteLockPath);
  FRIEND_TEST(NameSpaceTest, WriteLockPaths);
  FRIEND_TEST(NameSpaceTest, DecideIoMode);
  FRIEND_TEST(NameSpaceTest, AsyncCreateFileTxnCases);
  FRIEND_TEST(NameSpaceTest, AsyncDeleteTxnCases);
  FRIEND_TEST(NameSpaceTest, AsyncRenameTo2TxnCases);
  FRIEND_TEST(NameSpaceTest, AsyncMkDirsTxnCases);
  FRIEND_TEST(ReadWriteWithSnapshotsTest, SnapshotGcFunction);
  FRIEND_TEST(ReadWriteWithSnapshotsTest, SnapshotGc);
  FRIEND_TEST(ActiveStandbySyncEditlogTest, ProcessPendingDelete);
  FRIEND_TEST(ApplyCfsOpTest, ConcatV2TosLocal);
  FRIEND_TEST(ApplyCfsOpTest, ConcatV2Acc);
  FRIEND_TEST(NameSpaceTestV2, RenameUfsHandleCopyResult);

  const UfsConfig* ufs_conf_{nullptr};

  friend class AccNamespace;
  friend class UfsUploader;

  std::shared_ptr<UfsEnv> ufs_env_;

  MetaScannerTaskID reconcile_inode_attr_task_;

  // Friend class forward declaration
  friend class LockedPathBase;
  friend class JobManager;
};

// Task running background to delete unreached directories and files
class BGDeletionTask : public cnetpp::concurrency::Task {
 public:
  explicit BGDeletionTask(NameSpace* ns);

  void Stop() override;
  virtual bool IsStopped();
  bool operator()(void* arg = nullptr) override;

 protected:
  std::mutex mutex_;
  std::condition_variable cond_var_;
  NameSpace* ns_{nullptr};
};

class SnapshotGcTask : public BGDeletionTask {
 public:
  SnapshotGcTask(NameSpace* ns, uint64_t interval_in_ms);

  bool operator()(void* arg) override;
  int gc_times = 0;

 private:
  uint64_t interval_in_ms_;
};

}  // namespace dancenn

#endif  // NAMESPACE_NAMESPACE_H_
