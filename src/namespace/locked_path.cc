//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "locked_path.h"

#include "base/path_util.h"
#include "namespace/namespace.h"

DECLARE_uint64(dir_lock_retry_sleep_time_ms);

namespace dancenn {

RichPath::RichPath(const std::string& p) : path(p) {
  CHECK(SplitPath(path, &path_comps));
  CHECK(GetAllAncestorPaths(path, &lock_comps));
  CHECK(lock_comps.size() == path_comps.size() + 1);
  ancestors.reserve(lock_comps.size());
}

ResolveState LockedPathBase::ResolveAndLockInner(
    RichPath& p,
    const std::unique_ptr<RWLockManager::PathLockMap>& locks,
    std::function<PathLockTypeInner(const RichPath&, uint32_t lock_node_index)>
        lock_type_func,
    size_t start_index) {
  auto&& path = p.path;
  auto&& path_comps = p.path_comps;
  auto&& lock_comps = p.lock_comps;
  ResolveState state = ResolveState::kResolveInit;

  do {
    if (start_index == 0) {
      // Root must be handled specially, because
      //   path_comps.size() + 1 == lock_comps.size()
      {
        PathLockTypeInner type = lock_type_func(p, 0);
        locks->emplace(
            std::make_pair(lock_comps[0], Lock(type, lock_comps[0], 0)));
      }

      if (path_comps.empty()) {
        state = ResolveState::kResolveOK;
        break;
      }

      // Must not be root
      CHECK(!path_comps.empty());

      p.ancestors.emplace_back(GetRootINode());

      start_index++;
    }

    std::string parent_group = p.ancestors.back().permission().groupname();
    uint64_t parent_id = p.ancestors.back().id();
    size_t node_index = start_index;
    for (; node_index < lock_comps.size() - 1; ++node_index) {
      const size_t path_index = node_index - 1;
      // First lock before GetINode
      PathLockTypeInner type = lock_type_func(p, node_index);
      std::unique_ptr<RWLockManager::LockHolder> lock;

      const std::string next_path_comp(path_comps[path_index].as_string());
      INode node;
      Status get_s;

      {
        std::unique_ptr<RWLockManager::LockHolder> lock;
        // Get INode
        lock = Lock(type, lock_comps[node_index], node_index);
        get_s = GetINodeOrUpgradeWriteLockOnMissing(
            p, parent_id, next_path_comp, node_index, &lock, &node);
        if (!get_s.IsOK() && (get_s.code() != Code::kFileNotFound)) {
          // Unrecoverable error
          LOG(ERROR) << "Unrecoverable error on resolve. error: " +
                            get_s.ToString();
          state = ResolveState::kResolveError;
          break;
        }

        locks->emplace(std::make_pair(lock_comps[node_index], std::move(lock)));
      }

      if (get_s.code() == Code::kFileNotFound) {
        state = ResolveState::kResolveAncestorNotFound;
        break;
      }

      CHECK(CheckValidINode(node));
      if (node.type() != INode_Type_kDirectory) {
        state = ResolveState::kResolveAncestorNotDir;
        break;
      }

      if (node.permission().has_groupname() &&
          !node.permission().groupname().empty()) {
        parent_group = node.permission().groupname();
      } else {
        node.mutable_permission()->set_groupname(parent_group);
      }

      parent_id = node.id();
      p.ancestors.emplace_back(std::move(node));
    }

    if (ResolveState::kResolveInit != state) {
      break;
    }

    // Lock last node
    {
      CHECK(node_index == (lock_comps.size() - 1));
      PathLockTypeInner type = lock_type_func(p, node_index);
      locks->emplace(
          std::make_pair(lock_comps[node_index],
                         Lock(type, lock_comps[node_index], node_index)));
    }

    state = ResolveState::kResolveOK;
  } while (0);
  return state;
}

// Get the inode, or upgrade to write lock if the inode does not exist
Status LockedPathBase::GetINodeOrUpgradeWriteLockOnMissing(
    RichPath& p,
    uint64_t parent_id,
    const std::string& name,
    uint32_t lock_node_index,
    std::unique_ptr<RWLockManager::LockHolder>* lock,
    INode* out_node) {
  CHECK(lock != nullptr);

  INode curr_node;
  Status s;
  do {
    s = GetINode(parent_id, name, &curr_node, &p.attr_holder);
    if (LIKELY(s.IsOK())) {
      break;
    }

    if (s.code() != Code::kFileNotFound) {
      // Error
      LOG(ERROR) << "Resolve failed to GetINode on parent: " << parent_id
                 << ", name: " << name << ", error: " << s.ToString();
      break;
    }

    CHECK(s.code() == Code::kFileNotFound);
    // For no lock and read lock, just keep it, no need to upgrade to write lock
    if (PathLockTypeInner::kPathLockTypeWrite != lock_type_) {
      break;
    }

    // Lock has been held by previous path, skip
    if (*lock == nullptr) {
      break;
    }

    // Write lock already held on current non-existed inode
    if (!(*lock)->IsReadLock()) {
      break;
    }

    // For write lock, will hold write lock on first missing inode
    // Upgrade to write lock on missing inode for write lock
    Status upgrade_s = UpgradeToWriteLockOnNonExistDir(
        p, parent_id, name, lock_node_index, lock, &curr_node);
    if (!upgrade_s.IsOK()) {
      s = upgrade_s;
      break;
    }
    bool last_inode_valid = CheckValidINode(curr_node);
    if (last_inode_valid) {
      CHECK((*lock)->IsReadLock());
      s = Status::OK();
    } else {
      // Check write lock is held on last missing inode
      CHECK(!((*lock)->IsReadLock()));
      s = Status(Code::kFileNotFound,
                 "File not found for parent: " + std::to_string(parent_id) +
                     ", name: " + name);
    }
  } while (0);

  if (s.IsOK()) {
    *out_node = std::move(curr_node);
  }
  return s;
}
Status LockedPathBase::UpgradeToWriteLockOnNonExistDir(
    RichPath& p,
    int64_t parent_id,
    const std::string& name,
    uint32_t lock_node_index,
    std::unique_ptr<RWLockManager::LockHolder>* lock,
    INode* out_node) {
  auto&& path = p.path;
  auto&& path_comps = p.path_comps;
  auto&& lock_comps = p.lock_comps;

  // Release
  CHECK((*lock)->IsReadLock());
  lock->reset();
  int retry = 0;
  while (retry < 10) {
    auto newlock = WriteLock(lock_comps[lock_node_index], lock_node_index);
    // Double check the INode again in case something
    INode node;
    Status s = GetINode(parent_id, name, &node, &p.attr_holder);
    switch (s.code()) {
      case Code::kOK: {
        // The node has been created by someone else during lock gap
        // Downgrade to read lock
        newlock.reset();
        newlock = ReadLock(lock_comps[lock_node_index], lock_node_index);

        Status retry_s = GetINode(parent_id, name, &node, &p.attr_holder);
        switch (retry_s.code()) {
          case Code::kOK: {
            *out_node = std::move(node);
            *lock = std::move(newlock);
            return Status::OK();
          }
          case Code::kFileNotFound: {
            // The node is deleted in the gap between writelock release and
            // readlock acquire retry
            LOG(WARNING) << "Corner case: inode is deleted after downgrade to "
                            "read lock, will retry. parent: "
                         << parent_id << ", comp: " << name;
            ++retry;
            std::this_thread::sleep_for(
                std::chrono::milliseconds(FLAGS_dir_lock_retry_sleep_time_ms));
            continue;
          }
          case Code::kError:
          default: {
            LOG(ERROR) << "Failed to get inode for parent: " << parent_id
                       << ", name: " << name
                       << ", error: " << retry_s.ToString();
            return Status(
                Code::kError,
                "Failed to get inode for parent " + std::to_string(parent_id));
          }
        }
      }
      case Code::kFileNotFound: {
        // The inode still not existed after acquire write lock, just return
        *lock = std::move(newlock);
        return Status::OK();
      }
      case Code::kError:
      default: {
        LOG(ERROR) << "Failed to get inode for parent: " << parent_id
                   << ", name: " << name << ", error: " << s.ToString();
        return Status(
            Code::kError,
            "Failed to get inode for parent " + std::to_string(parent_id));
      }
    }
  }

  LOG(ERROR) << "Failed to acquire write lock after retries for parent: "
             << parent_id << ", comp: " << name;
  return Status(Code::kError, "Failed to acquire write lock after retries");
}
Status LockedPathBase::GetINode(uint64_t parent_id,
                                const std::string& name,
                                INode* inode,
                                INode* attr_holder) {
  StatusCode code = ns_->meta_storage_->GetINode(parent_id, name, inode);
  if (LIKELY(code == kOK)) {
    UpdateINodeAttrs(*attr_holder, *inode);
    return Status::OK();
  }
  if (code == kFileNotFound) {
    std::stringstream ss;
    ss << "INode not found for parent: " << parent_id << ", name: " << name;
    return Status(Code::kFileNotFound, ss.str());
  }
  LOG(ERROR) << "Failed to get INode for parent: " << parent_id
             << ", name: " << name << ", code: " << code;
  return Status(Code::kError,
                "Unexpected error to get INode: " + std::to_string(code));
}
INode LockedPathBase::GetRootINode() {
  return ns_->GetRootINode();
}

std::unique_ptr<RWLockManager::LockHolder> LockedPathBase::Lock(
    PathLockTypeInner type,
    const StringPiece& comp,
    uint32_t depth) {
  if (type == PathLockTypeInner::kPathLockTypeNone) {
    return nullptr;
  }

  if (PathLockTypeInner::kPathLockTypeRead == type) {
    VLOG(15) << "rlock " << comp.as_string();
    auto ret = ReadLock(comp, depth);
    VLOG(15) << "rlocked " << comp.as_string();
    return ret;
  }

  CHECK(type == PathLockTypeInner::kPathLockTypeWrite);
  VLOG(15) << "wlock " << comp.as_string();
  auto ret = WriteLock(comp, depth);
  VLOG(15) << "wlocked " << comp.as_string();
  return ret;
}
std::unique_ptr<RWLockManager::LockHolder> LockedPathBase::ReadLock(
    const StringPiece& lock_comp,
    uint32_t lock_depth) {
  return ns_->lock_manager_->ReadLock(lock_comp, lock_depth);
}
std::unique_ptr<RWLockManager::LockHolder> LockedPathBase::WriteLock(
    const StringPiece& lock_comp,
    uint32_t lock_depth) {
  return ns_->lock_manager_->WriteLock(lock_comp, lock_depth);
}

LockedPath::PathLockTypeInner LockedPath::CalLockType(
    const RichPath& p,
    uint32_t lock_node_index) {
  if (PathLockTypeInner::kPathLockTypeNone == lock_type()) {
    return PathLockTypeInner::kPathLockTypeNone;
  }

  if (lock_node_index == (p.lock_comps.size() - 1)) {
    switch (lock_type()) {
      case PathLockTypeInner::kPathLockTypeRead:
        return PathLockTypeInner::kPathLockTypeRead;
      case PathLockTypeInner::kPathLockTypeWrite:
      case PathLockTypeInner::kPathLockTypeNone:
      default:
        return PathLockTypeInner::kPathLockTypeWrite;
    }
  } else {
    return PathLockTypeInner::kPathLockTypeRead;
  }
}

LockedPath::LockedPath(PathLockType type,
                       const std::string& path,
                       NameSpace* ns)
    : LockedPathBase(ns, type), path_(path) {
  locks_ = std::make_unique<RWLockManager::PathLockMap>();
}

Status LockedPath::ResolveAndLock() {
  if (state_ != ResolveState::kResolveInit) {
    LOG(ERROR) << "Cannot resolve on current state: "
               << static_cast<int>(state_) << ", path: " << path_.path;
    return Status(
        Code::kError,
        "Cannot resolve on current state: " + static_cast<int>(state_));
  }

  CHECK(state_ == ResolveState::kResolveInit);

  state_ = ResolveAndLockInner(path_,
                               locks_,
                               std::bind(&LockedPath::CalLockType,
                                         this,
                                         std::placeholders::_1,
                                         std::placeholders::_2),
                               0);

  CHECK(state_ != ResolveState::kResolveInit);
  if (state_ == ResolveState::kResolveError) {
    locks_->clear();
    return Status(Code::kError, "Unexpected error on resolve.");
  }
  return Status::OK();
}

Status LockedPath::UpgradeToWriteLock() {
  set_lock_type(PathLockTypeInner::kPathLockTypeWrite);

  const auto& lock_comps = path_.lock_comps;
  size_t node_index = locks_->size() - 1;
  auto it = locks_->find(lock_comps[node_index]);
  CHECK(it != locks_->end());

  auto&& lock = it->second;
  if (node_index >= lock_comps.size() - 1 && !lock->IsReadLock()) {
    return Status::OK();
  }
  locks_->erase(lock_comps[node_index]);

  state_ = ResolveAndLockInner(path_,
                               locks_,
                               std::bind(&LockedPath::CalLockType,
                                         this,
                                         std::placeholders::_1,
                                         std::placeholders::_2),
                               node_index);

  if (state_ == ResolveState::kResolveError) {
    locks_->clear();
    return Status(Code::kError, "Unexpected error on resolve.");
  }

  return Status::OK();
}

Status LockedPath::DowngradeToReadLock() {
  set_lock_type(PathLockTypeInner::kPathLockTypeRead);

  const auto& lock_comps = path_.lock_comps;
  size_t node_index = locks_->size() - 1;
  auto it = locks_->find(lock_comps[node_index]);
  CHECK(it != locks_->end());

  auto&& lock = it->second;
  if (node_index >= lock_comps.size() - 1 && lock->IsReadLock()) {
    return Status::OK();
  }
  locks_->erase(lock_comps[node_index]);

  state_ = ResolveAndLockInner(path_,
                               locks_,
                               std::bind(&LockedPath::CalLockType,
                                         this,
                                         std::placeholders::_1,
                                         std::placeholders::_2),
                               node_index);

  if (state_ == ResolveState::kResolveError) {
    locks_->clear();
    return Status(Code::kError, "Unexpected error on resolve.");
  }

  return Status::OK();
}

std::string LockedPath::GetLastLockedNodePath() {
  CHECK(!locks_->empty());
  const size_t lock_node_index = locks_->size() - 1;
  return path_.lock_comps[lock_node_index].as_string();
}

LockedPathVector::LockedPathVector(NameSpace* ns,
                                   const std::vector<std::string>& paths,
                                   PathLockType type)
    : LockedPathBase(ns, type) {
  std::vector<std::string> tmp_paths = paths;
  std::sort(tmp_paths.begin(), tmp_paths.end());
  rich_paths_.reserve(tmp_paths.size());
  locks_.reserve(tmp_paths.size());
  for (auto&& p : tmp_paths) {
    rich_paths_.emplace_back(p);
    locks_.emplace_back(std::make_unique<RWLockManager::PathLockMap>());
    resolve_states_.emplace_back(ResolveState::kResolveInit);
  }
  InitLockPlan();
}

void LockedPathVector::InitLockPlan() {
  for (auto& rp : rich_paths_) {
    for (size_t i = 0; i < rp.lock_comps.size(); ++i) {
      auto&& comp = rp.lock_comps[i];
      bool is_last = i == (rp.lock_comps.size() - 1);
      PathLockTypeInner type =
          (is_last && PathLockTypeInner::kPathLockTypeWrite == lock_type())
              ? PathLockTypeInner::kPathLockTypeWrite
              : PathLockTypeInner::kPathLockTypeRead;
      auto it = lock_plan_.find(comp);
      if (it == lock_plan_.end()) {
        lock_plan_.emplace(std::make_pair(comp, type));
      } else {
        if (type > it->second) {
          lock_plan_[comp] = type;
        }
      }
    }
  }
}

Status LockedPathVector::ResolveAndLock() {
  for (auto& state : resolve_states_) {
    if (state != ResolveState::kResolveInit) {
      LOG(ERROR) << "Cannot resolve on state: " << static_cast<int>(state);
      return Status(Code::kError, "Cannot resolve on state");
    }
  }

  std::vector<ResolveState> states(resolve_states_.size(),
                                   ResolveState::kResolveInit);

  bool success = true;
  for (size_t i = 0; i < resolve_states_.size(); i++) {
    states[i] = ResolveAndLockInner(
        rich_paths_[i],
        locks_[i],
        [&, this](const RichPath& p, uint32_t lock_node_index) {
          auto&& comp = p.lock_comps[lock_node_index];
          for (auto& locks : locks_) {
            // If locked by previous path, just skip
            if (locks->find(comp) != locks->end()) {
              return PathLockTypeInner::kPathLockTypeNone;
            }
          }
          return lock_plan_[comp];
        },
        0);
    if (states[i] == ResolveState::kResolveError) {
      LOG(ERROR) << "Failed to resolve on path: " << rich_paths_[i].path;
      success = false;
      break;
    }
  }

  for (size_t i = 0; i < resolve_states_.size(); i++) {
    if (success) {
      resolve_states_[i] = states[i];
    } else {
      states[i] = ResolveState::kResolveError;
      locks_[i]->clear();
      rich_paths_[i].Clear();
    }
  }

  if (!success) {
    std::stringstream ss;
    ss.imbue(std::locale::classic());
    for (auto& p : rich_paths_) {
      if (ss.str().empty()) {
        ss << "Failed to resolve paths. Paths are: " << p.path;
      } else {
        ss << ", " << p.path;
      }
    }
    return Status(Code::kError, ss.str());
  }

  return Status::OK();
}

}  // namespace dancenn
