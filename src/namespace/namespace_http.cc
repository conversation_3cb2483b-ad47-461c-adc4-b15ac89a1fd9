//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include "base/path_util.h"
#include "namespace/namespace.h"
#include "security/block_token_identifier.h"

DECLARE_int32(dfs_ls_limit);
DECLARE_bool(meta_storage_snapshot_read_enabled);

namespace dancenn {

static const int32_t kDefaultCountForHttpListing = 100;

Status NameSpace::GetFileInfoForHttp(const std::string& path,
                                     bool need_location,
                                     HdfsFileStatusProto* rsp) {
  std::vector<cnetpp::base::StringPiece> path_components;
  std::vector<::cnetpp::base::StringPiece> lock_components;
  if (!SplitPath(path, &path_components) ||
      !GetAllAncestorPaths(path, &lock_components)) {
    return Status(Code::kBadParameter, "Invalid path: " + path);
  }

  CHECK(FLAGS_meta_storage_snapshot_read_enabled);
  MetaStorageSnapHolderPtr snapshot_holder = meta_storage_->GetSnapshot();
  MetaStorageSnapPtr snapshot = snapshot_holder->snapshot();

  INodeInPath parent_iip;
  const INode& parent_inode = parent_iip.Inode();
  auto code = GetLastINodeInPath(
      path_components, &parent_iip, nullptr, nullptr, snapshot);
  if (code == StatusCode::kFileNotFound) {
    return Status(Code::kFileNotFound, "Invalid path: " + path);
  }
  UserGroupInfo default_ugi = UserGroupInfo();
  std::vector<AccessMode> modes{AccessMode::READ};
  ConstructFileStatus(parent_inode.name(),
                      parent_inode,
                      parent_iip.IsUnderSnapshot(),
                      need_location,
                      NetworkLocationInfo(),
                      default_ugi,
                      modes,
                      rsp);
  return Status::OK();
}

Status NameSpace::GetListingForHttp(const std::string& path,
                                    const std::string& start_after,
                                    int32_t count,
                                    bool need_location,
                                    bool allow_list_file,
                                    GetListingForHttpResponse* rsp) {
  std::vector<cnetpp::base::StringPiece> path_components;
  std::vector<::cnetpp::base::StringPiece> lock_components;
  if (!SplitPath(path, &path_components) ||
      !GetAllAncestorPaths(path, &lock_components)) {
    return Status(Code::kBadParameter, "Invalid path: " + path);
  }

  CHECK(FLAGS_meta_storage_snapshot_read_enabled);
  MetaStorageSnapHolderPtr snapshot_holder = meta_storage_->GetSnapshot();
  MetaStorageSnapPtr snapshot = snapshot_holder->snapshot();

  INodeInPath parent_iip;
  const INode& parent_inode = parent_iip.Inode();
  std::vector<INode> ancestors;
  auto code = GetLastINodeInPath(
      path_components, &parent_iip, &ancestors, nullptr, snapshot);
  if (code == StatusCode::kFileNotFound) {
    return Status(Code::kFileNotFound, "Invalid path: " + path);
  }

  if (parent_inode.type() != INode_Type_kDirectory) {
    if (allow_list_file) {
      rsp->files.emplace_back(HdfsFileStatusProto());
      UserGroupInfo default_ugi = UserGroupInfo();
      std::vector<AccessMode> modes{AccessMode::READ};
      ConstructFileStatus(path,
                          parent_inode,
                          parent_iip.IsUnderSnapshot(),
                          need_location,
                          NetworkLocationInfo(),
                          default_ugi,
                          modes,
                          &(rsp->files.back()),
                          nullptr);
      rsp->has_more = false;
      return Status::OK();
    } else {
      return Status(Code::kBadParameter, "Not a directory. path: " + path);
    }
  }

  if (count > FLAGS_dfs_ls_limit && count <= 0) {
    count = kDefaultCountForHttpListing;
  }

  // handling ls /xxx/.snapshot
  if (parent_iip.GetSnapshotReadInfo().is_snapshot_directory_) {
    return GetSnapshotsListing(
        parent_iip,
        start_after,
        count,
        [&]() {
          rsp->files.emplace_back(HdfsFileStatusProto());
          return &(rsp->files.back());
        },
        &rsp->has_more);
  }

  int file_count = 0;
  bool has_more = false;
  bool is_snapshot_read = parent_iip.IsUnderSnapshot();
  meta_storage_->GetSubINodes(
      parent_inode.id(),
      start_after,
      [this,
       count,
       need_location,
       rsp,
       is_snapshot_read,
       &file_count,
       &has_more,
       parent_iip,
       ancestors](const INode& child) -> bool {
        if (child.name() == kRecycleBinDirNameString) {
          // ignore RecycleBinDir
          return true;
        }

        ++file_count;
        if (file_count <= count) {
          rsp->files.emplace_back(HdfsFileStatusProto());
          UserGroupInfo default_ugi = UserGroupInfo();
          std::vector<AccessMode> modes{AccessMode::READ};
          ConstructFileStatus(child.name(),
                              child,
                              is_snapshot_read,
                              need_location,
                              NetworkLocationInfo(),
                              default_ugi,
                              modes,
                              &(rsp->files.back()),
                              nullptr);
          return true;
        }

        has_more = true;
        return false;
      });
  rsp->has_more = has_more;
  return Status();
}

std::vector<std::string> NameSpace::GetActiveClients() const {
  // Rules about RVO is too complicated to understand. Just move it.
  return std::move(lease_manager_->GetActiveClients());
}

}  // namespace dancenn
