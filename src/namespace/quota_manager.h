// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/02/09
// Description

#ifndef NAMESPACE_QUOTA_MANAGER_H_
#define NAMESPACE_QUOTA_MANAGER_H_

#include <cnetpp/tcp/tcp_client.h>

#include <atomic>
#include <condition_variable>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include "base/constants.h"
#include "base/lru_cache.h"
#include "base/read_write_lock.h"
#include "base/status.h"
#include "cnetpp/concurrency/thread_pool.h"
#include "namespace/inode.h"
#include "namespace/namespace_scrub_inodestat.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/dancenn/inode.pb.h"
#include "rpc/pooled_rpc_channel.h"
#include "service/client_namenode_service.h"

namespace dancenn {

struct Quota {
  int64_t last_update_sec;
  // snapshot_txid is different from dancenn::INodeStat::txid.
  // INodeStat::txid: TxId of edit log which cause INodeStat changes.
  // snapshot_txid: TxId when reading INodeStat.
  TxID snapshot_txid;
  // inode_stat.inode_id == kInvalidINodeId if inode_stat is invalid.
  INodeStat inode_stat;
};

class QuotaDelta {
 public:
  QuotaDelta(int64_t dir_num, int64_t file_num, int64_t data_size);

 public:
  int64_t inode_num_;
  int64_t dir_num_;
  int64_t file_num_;
  int64_t data_size_;
};

class QuotaClient {
 public:
  QuotaClient() = default;
  virtual ~QuotaClient() = default;

  void Start();
  void Stop();

  bool Refresh();
  virtual bool GetQuotas(const cloudfs::GetINodeStatRequestProto& request,
                         cloudfs::GetINodeStatResponseProto* response);

 private:
  std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_;

  ReadWriteLockLight rwlock_;
  std::unique_ptr<PooledRpcChannel> channel_;
  std::unique_ptr<ClientNamenodeService::Stub> stub_;
};

class QuotaMapSlice {
 public:
  QuotaMapSlice();
  virtual ~QuotaMapSlice() = default;
  QuotaMapSlice(const QuotaMapSlice& other) = delete;
  QuotaMapSlice(QuotaMapSlice&& other) = delete;
  QuotaMapSlice& operator=(const QuotaMapSlice& other) = delete;
  QuotaMapSlice& operator=(QuotaMapSlice&& other) = delete;

  bool Get(INodeID inode_id, Quota* quota);
  void Set(INodeID inode_id, const Quota& quota);
  void Del(INodeID inode_id);
  /*traverse_to_end=*/bool GetExpiredQuotas(
      int64_t ts_sec,
      TxID txid,
      int limit,
      cloudfs::GetINodeStatRequestProto* request);

 private:
  ReadWriteLockLight rwlock_;
  // We believe inode id instead of path as key because inode stat
  // is still valid for that key after move operation.
  // INodeStat of /a/b/c is {"inode_num":100,"data_size":1024}.
  // Move /a/b/c to /a/b/d.
  // Then INodeStat of /a/b/d is {"inode_num":100,"data_size":1024}.
  LRUCache<INodeID, Quota, false> quotas_;
};

class QuotaManager {
 public:
  explicit QuotaManager(std::shared_ptr<QuotaClient> quota_client);

  void RefreshClient();
  Status CheckCreateOrAdd(const std::vector<INode>& dst_ancestors,
                          const QuotaDelta& additional_quota,
                          TxID current_txid);
  Status CheckRename(const std::vector<INode>& dst_ancestors,
                     const INode* src_inode,
                     const INode* old_dst_inode,
                     TxID current_txid);

 private:
  Status CheckInternal(const std::vector<INode>& dst_ancestors,
                       const INode* src_inode,
                       const INode* old_dst_inode,
                       const QuotaDelta& additional_quota,
                       TxID current_txid);
  Status CheckInternal(const std::vector<INode>& dst_ancestors,
                       const INode* src_inode,
                       const INode* old_dst_inode,
                       const QuotaDelta& additional_quota,
                       int64_t expired_sec,
                       TxID current_txid,
                       TxID expired_txid,
                       bool refresh_if_missing);
  Status SatisfyQuotaPolicy(const std::string& dir_name,
                            const QuotaPolicyProto& quota_policy,
                            const INodeStat& inode_stat,
                            const QuotaDelta& additional_quota);

  void UpdateQuotasSync(const std::vector<INode>& dst_ancestors,
                        const INode* src_inode,
                        const INode* old_dst_inode,
                        TxID txid);
  bool UpdateQuotasSync(const cloudfs::GetINodeStatRequestProto& request);
  void UpdateQuotasAsync(const std::vector<INode>& inodes, TxID txid);

  bool GetINodeStat(INodeID inode_id,
                    int64_t expired_sec,
                    TxID expired_txid,
                    INodeStat* inode_stat);
  QuotaMapSlice& Slice(INodeID inode_id);

  virtual int64_t GetCurrentTsInSec();

 public:
  void UpdateQuotasAsync(const std::vector<INodeID>& inode_ids, TxID txid);
  bool SendHeartbeat(TxID current_txid);

  bool TestOnlyGetQuota(INodeID inode_id, Quota* quota);
  void TestOnlySetQuota(const Quota& quota);
  int64_t TestOnlyGetLastHeartbeatSec();
  TxID TestOnlyGetLastHeartbeatTxId();

 private:
  std::shared_ptr<QuotaClient> quota_client_;

  const std::size_t num_slices_;
  const std::size_t slice_mask_;
  std::vector<std::unique_ptr<QuotaMapSlice>> slices_;

  std::size_t slice_to_update_;
  // Lock order: First update_quotas_mtx_, then slices_.
  std::mutex update_quotas_mtx_;
  std::unordered_map<INodeID, TxID> quotas_to_update_;

  // We use heartbeats (empty dirstats rpc requests) to update
  // last_heartbeat_sec_ and last_heartbeat_txid_.
  // So them indicate whether observer nn is healthy.
  std::atomic<int64_t> last_heartbeat_sec_;
  std::atomic<TxID> last_heartbeat_txid_;
};

class NameSpace;
class QuotaManagerWrapper {
 public:
  QuotaManagerWrapper();
  ~QuotaManagerWrapper();

  void SetNameSpace(NameSpace* ns);
  void Start();
  void Stop();

  Status CheckCreateOrAdd(const std::vector<INode>& dst_ancestors,
                          const QuotaDelta& additional_quota);
  Status CheckRename(const std::vector<INode>& dst_ancestors,
                     const INode* src_inode,
                     const INode* old_dst_inode);

 private:
  NameSpace* ns_{nullptr};
  std::atomic<TxID> current_txid_;

  std::atomic<bool> is_running_;
  std::shared_ptr<QuotaClient> cli_;
  std::unique_ptr<QuotaManager> mgr_;

  std::mutex mu_;
  std::condition_variable cv_;
  std::thread heartbeat_thread_;
};

}  // namespace dancenn

#endif  // NAMESPACE_QUOTA_MANAGER_H_
