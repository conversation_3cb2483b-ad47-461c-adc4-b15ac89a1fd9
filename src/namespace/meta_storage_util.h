#pragma once

#include <absl/strings/str_format.h>
#include <rocksdb/slice.h>

#include "namespace/meta_storage.h"
#include "namespace/namespace_stat.h"

DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_bool(ms_util_compare_inode_skip_check_mtime);
DECLARE_bool(ms_util_compare_inode_skip_check_atime);
DECLARE_bool(ms_util_compare_inode_skip_check_perm);
DECLARE_bool(ms_util_compare_inode_skip_check_acl);
DECLARE_bool(ms_util_compare_inode_skip_check_xattr);

namespace dancenn {

static std::string DecINodeStoreKey(const rocksdb::Slice& slice) {
  // refer to MetaStorage::DecodeStoreKey()
  uint64_t par_id, id;
  std::string name;
  cnetpp::base::StringPiece key_str(slice.ToString());
  if (FLAGS_dfs_meta_storage_inode_key_v2) {
    if (key_str.size() < 19UL ||
        key_str[8] != '/' ||
        key_str[key_str.size() - 8 - 1 - 1] != '\0' ||
        key_str[key_str.size() - 8 - 1] != '/') {
      return "Failed to decode inode store key";
    }
    par_id = platform::ReadBigEndian<uint64_t>(key_str.data(), 0);
    name.assign(key_str.data() + 9, key_str.size() - 19);
    id = platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 8);
  } else {
    if (key_str.size() < 18) {
      return "Failed to decode inode store key";
    }
    par_id = platform::ReadBigEndian<uint64_t>(key_str.data(), 0);
    name.assign(key_str.data() + 9, key_str.size() - 18);
    if (name.size() > 0 && name.back() == '\0') {
      return "Failed to decode inode store key";
    }
    id = platform::ReadBigEndian<uint64_t>(key_str.data(), key_str.size() - 8);
  }
  return absl::Substitute("$0/$1/$2", par_id, name, id);
}

static std::string DecINodeID(const rocksdb::Slice& slice) {
  INodeID id = platform::BigEndianToHost(*(reinterpret_cast<const uint64_t*>(slice.data())));
  return absl::Substitute("$0", id);
}

static std::string DecINode(const rocksdb::Slice& slice) {
  INode inode;
  if (!inode.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid INode";
  }
  // XXX
  inode.set_atime(0);
  return inode.ShortDebugString();
}

static std::string DecString(const rocksdb::Slice& slice) {
  return slice.ToString();
}

static std::string DecINodeIndexKey(const rocksdb::Slice& slice) {
  return absl::Substitute("$0", platform::ReadBigEndian<INodeID>(slice.data(), 2));
}

static std::string DecParentInfoProto(const rocksdb::Slice& slice) {
  INodeParentInfoPB proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid INodeParentInfoPB";
  }
  return proto.ShortDebugString();
}

static std::string DecLegacyBlockID(const rocksdb::Slice& slice) {
  return absl::Substitute("$0", std::stoull(slice.ToString()));
}

static std::string DecLegacyBlockPufsInfo(const rocksdb::Slice& slice) {
  BlockPufsInfo bpi;
  if (!bpi.DeserializeFromJsonString(slice.ToString())) {
    return "Failed to deserialize a valid BlockPufsInfo";
  }
  return bpi.SerializeToJsonString();
}

static std::string DecBlockID(const rocksdb::Slice& slice) {
  if (slice.size() != sizeof(BlockID) / sizeof(uint8_t)) {
    return "Failed to deserialize a valid BlockID";
  }
  BlockID blkid = platform::ReadBigEndian<BlockID>(slice.data(), 0);
  return absl::Substitute("$0", blkid);
}

static std::string DecBlockInfoProto(const rocksdb::Slice& slice) {
  BlockInfoProto bip;
  if (!bip.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid BlockInfoProto";
  }
  return bip.ShortDebugString();
}

static std::string DecINodeStat(const rocksdb::Slice& slice) {
  INodeStat inode_stat;
  Status st = INodeStatUtils::Parse(slice.ToString(), &inode_stat);
  if (!st.IsOK()) {
    return absl::Substitute("Failed to deserialize a valid BlockInfoProto: $0", st.ToString());
  }
  return inode_stat.ToString();
}

static std::string DecLifecyclePolicyProto(const rocksdb::Slice& slice) {
  LifecyclePolicyProto proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid LifecyclePolicyProto";
  }
  return proto.ShortDebugString();
}

static std::string DecSCSProto(const rocksdb::Slice& slice) {
  StorageClassStatProto proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid StorageClassStatProto";
  }
  return proto.ShortDebugString();
}

static std::string DecSCRKey(const rocksdb::Slice& slice) {
  // refer to MetaStorage::DecodeSCRKey
  size_t blkid_nbyte = sizeof(BlockID) / sizeof(uint8_t);
  std::string blkid_str;
  std::string dnuuid;
  if (slice.size() == blkid_nbyte) {
    blkid_str = DecBlockID(slice);
    dnuuid = "(null)";
  } else {
    blkid_str = DecBlockID(rocksdb::Slice(slice.data(), blkid_nbyte));
    dnuuid.assign(slice.data() + blkid_nbyte + 1,
                  slice.size() - blkid_nbyte - 1);
  }
  return absl::Substitute("$0-$1", blkid_str, dnuuid);
}

static std::string DecSCRProto(const rocksdb::Slice& slice) {
  StorageClassReportProto proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid StorageClassReportProto";
  }
  return proto.ShortDebugString();
}

static std::string DecUCProto(const rocksdb::Slice& slice) {
  FileUnderConstructionFeature proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid FileUnderConstructionFeature";
  }
  return proto.ShortDebugString();
}

static std::string DecDNInfoKey(const rocksdb::Slice& slice) {
  if (slice.size() != sizeof(DatanodeID)) {
    return "Failed to deserialize a valid DNInfoKey";
  }
  DatanodeID dnid = platform::ReadBigEndian<DatanodeID>(slice.data(), 0);
  return absl::Substitute("$0", dnid);
}

static std::string DecDNInfoEntryPB(const rocksdb::Slice& slice) {
  DatanodeInfoEntryPB proto;
  if (!proto.ParseFromArray(slice.data(), slice.size())) {
    return "Failed to deserialize a valid DatanodeInfoEntryPB";
  }
  return proto.ShortDebugString();
}

static std::string DecSnapshotINodeKey(const rocksdb::Slice& slice) {
  // refer to MetaStorage::DecodeSnapshotINodeKey()
  if (slice.size() <= 18) {
    return "Failed to deserialize a valid SnapshotINodeKey";
  }
  std::string inode_store_key = DecINodeStoreKey(slice);
  uint64_t last_update_txid
      = platform::ReadBigEndian<uint64_t>(slice.data(), slice.size() - 17);
  INodeID snapshot_root_id
      = platform::ReadBigEndian<uint64_t>(slice.data(), slice.size() - 8);
  return absl::Substitute("$0-$1-$2",
                          inode_store_key,
                          last_update_txid,
                          snapshot_root_id);
}

static std::string DecSnapshotINodeIndexKey(const rocksdb::Slice& slice) {
  // refer to MetaStorage::DecodeSnapshotINodeIndexKey()
  INodeID id = platform::ReadBigEndian<uint64_t>(slice.data(), 0);
  uint64_t last_update_txid = platform::ReadBigEndian<uint64_t>(slice.data(), 9);
  return absl::Substitute("$0-$1", id, last_update_txid);
}

static bool CompareINode(const rocksdb::Slice& slice1,
                         const rocksdb::Slice& slice2) {
  bool match = true;
  INode in1, in2;
  if (!in1.ParseFromArray(slice1.data(), slice1.size()) ||
      !in2.ParseFromArray(slice2.data(), slice2.size())) {
    LOG(ERROR) << "Failed to deserialize valid INode";
    return false;
  }

  if (FLAGS_ms_util_compare_inode_skip_check_mtime) {
    in1.clear_mtime();
    in2.clear_mtime();
  }
  if (FLAGS_ms_util_compare_inode_skip_check_atime) {
    in1.clear_atime();
    in2.clear_atime();
  }
  if (FLAGS_ms_util_compare_inode_skip_check_perm) {
    in1.clear_permission();
    in2.clear_permission();
  }
  if (FLAGS_ms_util_compare_inode_skip_check_acl) {
    in1.clear_acls();
    in2.clear_acls();
  }
  if (FLAGS_ms_util_compare_inode_skip_check_xattr) {
    in1.clear_xattrs();
    in2.clear_xattrs();
  }

  return in1.ShortDebugString() == in2.ShortDebugString();
}

static bool CompareDNInfo(const rocksdb::Slice& slice1,
                          const rocksdb::Slice& slice2) {
  bool match = true;
  DatanodeInfoEntryPB dninfo1, dninfo2;
  if (!dninfo1.ParseFromArray(slice1.data(), slice1.size()) ||
      !dninfo2.ParseFromArray(slice2.data(), slice2.size())) {
    LOG(ERROR) << "Failed to deserialize valid DatanodeInfoEntryPB";
    return false;
  }
  if (dninfo1.storages_uuid_size() != dninfo2.storages_uuid_size() ||
      dninfo1.storages_size() != dninfo2.storages_size()) {
    return false;
  }
  // XXX(xuex)
  // The protobuf version we depends on does not support MessageDifferencer.
  // Fortunately, number of storages is usually limited, so the inefficient
  // algorithm below is acceptable.
  int idx1 = 0;
  int idx2 = 0;
  for (; idx1 < dninfo1.storages_uuid_size(); idx1++) {
    auto uuid1 = dninfo1.storages_uuid(idx1);
    for (; idx2 < dninfo2.storages_uuid_size(); idx2++) {
      auto uuid2 = dninfo2.storages_uuid(idx2);
      if (uuid1 == uuid2) {
        auto storage1 = dninfo1.storages(idx1);
        auto storage2 = dninfo1.storages(idx2);
        match = storage1.ShortDebugString() == storage2.ShortDebugString();
        break;
      }
    }
    if (idx2 == dninfo2.storages_uuid_size()) {
      match = false;
    }
    if (!match) {
      break;
    }
  }
  return match;
}

static const struct ms_cfinfo_t {
  uint32_t cf_idx;
  const char* cf_name;
  std::function<std::string(const rocksdb::Slice&)> key_decoder;
  std::function<std::string(const rocksdb::Slice&)> val_decoder;
  std::function<bool(const rocksdb::Slice&, const rocksdb::Slice&)> val_comparator;
} meta_storage_cfinfo[] {
  {
    kINodeDefaultCFIndex,                   kINodeDefaultCFName,
    DecINodeStoreKey,                       DecINode,
    CompareINode,
  }, {
    kINodePendingDeleteCFIndex,             kINodePendingDeleteCFName,
    DecINodeID,                             DecINode,
    CompareINode,
  }, {
    kNameSystemInfoCFIndex,                 kNameSystemInfoCFName,
    DecString,                              DecString,
    nullptr,
  }, {
    kAccessCounterCFIndex,                  kAccessCounterCFName,
    DecString,                              DecString,
    nullptr,
  }, {
    kINodeIndexCFIndex,                     kINodeIndexCFName,
    DecINodeIndexKey,                       DecParentInfoProto,
    nullptr,
  }, {
    kLegacyBlockPufsInfoCFIndex,            kLegacyBlockPufsInfoCFName,
    DecLegacyBlockID,                       DecLegacyBlockPufsInfo,
    nullptr,
  }, {
    kLegacyDeprecatedBlockPufsInfoCFIndex,  kLegacyDeprecatedBlockPufsInfoCFName,
    DecLegacyBlockID,                       DecString,
    nullptr,
  }, {
    kLegacyBlockInfoProtoCFIndex,           kLegacyBlockInfoProtoCFName,
    DecLegacyBlockID,                       DecBlockInfoProto,
    nullptr,
  }, {
    kBlockInfoProtoCFIndex,                 kBlockInfoProtoCFName,
    DecBlockID,                             DecBlockInfoProto,
    nullptr,
  }, {
    kLocalBlockCFIndex,                     kLocalBlockCFName,
    DecBlockID,                             DecString,
    nullptr,
  }, {
    kDeprecatingBlockCFIndex,               kDeprecatingBlockCFName,
    DecBlockID,                             DecString,
    nullptr,
  }, {
    kDeprecatedBlockCFIndex,                kDeprecatedBlockCFName,
    DecBlockID,                             DecString,
    nullptr,
  }, {
    kINodeStatCFIndex,                      kINodeStatCFName,
    DecINodeID,                             DecINodeStat,
    nullptr,
  }, {
    kLifecyclePolicyCFIndex,                kLifecyclePolicyCFName,
    DecINodeID,                             DecLifecyclePolicyProto,
    nullptr,
  }, {
    kStorageClassStatCFIndex,               kStorageClassStatCFName,
    DecINodeID,                             DecSCSProto,
    nullptr,
  }, {
    kStorageClassReportCFIndex,             kStorageClassReportCFName,
    DecSCRKey,                              DecSCRProto,
    nullptr,
  }, {
    kLeaseCFIndex,                          kLeaseCFName,
    DecINodeID,                             DecUCProto,
    nullptr,
  }, {
    kDatanodeInfoCFIndex,                   kDatanodeInfoCFName,
    DecDNInfoKey,                           DecDNInfoEntryPB,
    CompareDNInfo,
  }, {
    kSnapshotRootInfoCFIndex,               kSnapshotRootInfoCFName,
    DecINodeID,                             DecString,
    nullptr,
  }, {
    kSnapshotInodeCFIndex,                  kSnapshotInodeCFName,
    DecSnapshotINodeKey,                    DecINode,
    CompareINode,
  }, {
    kSnapshotRenameRecordCFIndex,           kSnapshotRenameRecordCFName,
    DecString,                              DecString,
    nullptr,
  }, {
    kSnapshotINodeIndexCFIndex,             kSnapshotINodeIndexCFName,
    DecSnapshotINodeIndexKey,               DecINodeID,
    nullptr,
  },
};

struct CFStat {
  CFStat() : num_entry(0) {}
  bool operator==(const CFStat& other) const {
    return (num_entry == other.num_entry);
  }
  std::string ToString() {
    return absl::StrFormat(
        "{ num_entry: %lu }",
        num_entry);
  }
  uint64_t num_entry { 0 };
};
using DBStat = std::map<int, CFStat>;

class DBComparator {
 public:
  DBComparator(const std::string& active_db_path,
               const std::string& standby_db_path)
      : active_path_(active_db_path),
        standby_path_(standby_db_path) {
    Init();
  }
  ~DBComparator() {
    Fini();
  }
  void Init() {
    active_ = std::make_unique<ReadOnlyMetaStorage>(active_path_);
    active_->Launch();
    standby_ = std::make_unique<ReadOnlyMetaStorage>(standby_path_);
    standby_->Launch();
  }
  void Fini() {
    active_->Shutdown();
    standby_->Shutdown();
  }

  DBStat GetDBStat(bool check_synced);
  DBStat GetDBStat(const std::vector<uint32_t> cf_idxs, bool check_synced);
  bool CheckDBConsistency();
  void DumpDB();

 protected:
  void GetCFStat(const ms_cfinfo_t& cfinfo,
                 MetaStorageIterPtr ms_iter,
                 CFStat* cfstat);
  bool CheckCFConsistency(const ms_cfinfo_t& cfinfo,
                          MetaStorageIterPtr active_iter,
                          MetaStorageIterPtr standby_iter);
  bool CheckKeyConsistency(const ms_cfinfo_t& cfinfo,
                           const rocksdb::Slice& active_key_slice,
                           const rocksdb::Slice& standby_key_slice);
  bool CheckValueConsistency(const ms_cfinfo_t& cfinfo,
                             const rocksdb::Slice& active_value_slice,
                             const rocksdb::Slice& standby_value_slice);
  void DumpCF(const ms_cfinfo_t& cfinfo,
              MetaStorageIterPtr iter);
  void DumpKey(const ms_cfinfo_t& cfinfo,
               const rocksdb::Slice& key_slice);
  void DumpValue(const ms_cfinfo_t& cfinfo,
                 const rocksdb::Slice& value_slice);

 private:
  std::string active_path_;
  std::string standby_path_;
  std::unique_ptr<ReadOnlyMetaStorage> active_;
  std::unique_ptr<ReadOnlyMetaStorage> standby_;
};

} // namespace dancenn
