#ifndef NAMESPACE_HYPERFILE_SCANNER_LISTENER_H
#define NAMESPACE_HYPERFILE_SCANNER_LISTENER_H

#include <regex>
#include "base/metrics.h"
#include "base/constants.h"
#include "namespace/meta_scanner.h"

namespace dancenn {

class NameSpace;

class HyperfileScannerListener : public MetaScanner::Listener {
 public:
  explicit HyperfileScannerListener(NameSpace* ns);
  ~HyperfileScannerListener() = default;

  bool PreScan() override;
  std::vector<INodeID> ScanIndexes() override;
  bool Handle(const std::string& full_path, const INode& inode) override;
  void PostScan() override;

 private:
  static std::string desc() { return "hyper_scanner_listener"; }
  std::string GetHyperFilePath(const std::string& full_path,
                               const dancenn::INode &inode);

  NameSpace* ns_;
  int64_t hyper_file_count_{0};
  int64_t hyper_block_count_{0};
  int64_t broken_hyper_file_count_{0};
  int64_t leak_hyper_block_count_{0};
  int64_t suspect_leak_hyper_block_count_{0};
  static const std::regex kHyperBlockSrcFormat;
  static const std::string kInvalidHyperBlockSrc;
};

}



#endif // NAMESPACE_HYPERFILE_SCANNER_LISTENER_H
