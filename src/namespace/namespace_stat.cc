//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include "namespace_stat.h"

#include <absl/strings/str_format.h>
#include <cnetpp/base/csonpp.h>
#include <cnetpp/concurrency/spin_lock.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <glog/logging.h>
#include <rocksdb/db.h>

#include <sstream>
#include <unordered_map>
#include <unordered_set>

#include "base/pb_converter.h"
#include "base/platform.h"
#include "base/string_utils.h"
#include "base/time_util.h"
#include "base/to_json_string.h"
#include "namespace/meta_storage.h"

using StringPiece = cnetpp::base::StringPiece;
using ThreadPool = cnetpp::concurrency::ThreadPool;
using ThreadPoolPtr = std::shared_ptr<cnetpp::concurrency::ThreadPool>;
using ThisThread = cnetpp::concurrency::ThisThread;
using SpinLock = cnetpp::concurrency::SpinLock;
using SpinLockGuard = cnetpp::concurrency::SpinLock::ScopeGuard;

namespace dancenn {

inline void ToJson(const INodeStat& t, cnetpp::base::Value* value) {
  CHECK_NOTNULL(value);
  cnetpp::base::Object obj;
  obj["inode_id"] = t.inode_id;
  obj["inode_num"] = t.inode_num;
  obj["file_num"] = t.file_num;
  obj["dir_num"] = t.dir_num;
  obj["block_num"] = t.block_num;
  obj["data_size"] = t.data_size;
  obj["txid"] = t.txid;
  obj["timestamp_sec"] = t.timestamp_sec;
  *value = std::move(obj);
}

static const uint32_t kINodeStatMagic = 0X12345678;
static const uint32_t kINodeStatVersion = 1;
static const uint32_t kINodeStatItemsNumForVersion1 = 8;

std::string INodeStat::ToString() const {
  std::stringstream ss;
  ss << "INodeStat: ("
     << "inode: " << inode_id << ", inode_num: " << inode_num
     << ", file_num: " << file_num << ", dir_num: " << dir_num
     << ", block_num: " << block_num << ", data_size: " << data_size
     << ", txid: " << txid << ", timestamp_sec: " << timestamp_sec << ")";
  return ss.str();
}

std::string INodeStat::Serialize() const {
  std::string result;
  result.reserve(128);
  StringUtils::PutFixed32(&result, kINodeStatMagic);
  StringUtils::PutFixed32(&result, kINodeStatVersion);
  StringUtils::PutFixed64(&result, inode_id);
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(inode_num));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(file_num));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(dir_num));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(block_num));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(data_size));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(txid));
  StringUtils::PutFixed64(&result, static_cast<uint64_t>(timestamp_sec));

  CHECK(timestamp_sec != 0);
  return std::move(result);
}

bool operator==(const INodeStat& a, const INodeStat& b) {
  return a.inode_id == b.inode_id && a.inode_num == b.inode_num &&
         a.file_num == b.file_num && a.dir_num == b.dir_num &&
         a.data_size == b.data_size && a.block_num == b.block_num;
}

bool operator!=(const INodeStat& a, const INodeStat& b) {
  return a.inode_id != b.inode_id || a.inode_num != b.inode_num ||
         a.file_num != b.file_num || a.dir_num != b.dir_num ||
         a.data_size != b.data_size || a.block_num != b.block_num;
}

void operator+=(INodeStat& base, const INodeStat& delta) {
  CHECK_EQ(base.inode_id, delta.inode_id);
  CHECK_LE(base.txid, delta.txid);
  base.inode_num += delta.inode_num;
  base.file_num += delta.file_num;
  base.dir_num += delta.dir_num;
  base.block_num += delta.block_num;
  base.data_size += delta.data_size;
  base.txid = delta.txid;
  base.timestamp_sec = delta.timestamp_sec;
}

std::string ScrubProgress::ToString() const {
  std::stringstream ss;
  ss << "ScrubProgress: ("
     << "start_ms: " << start_ms << ", finished_inodes: " << finished_inodes
     << ", finished_dirs: " << finished_dirs
     << ", estimated_time_remain_seconds: " << estimated_time_remain_seconds
     << ")";
  return ss.str();
}

std::string ScrubResult::ToString() const {
  std::stringstream ss;
  ss << "ScrubResult: ("
     << "start_ms: " << start_ms << ", finished_ms: " << finished_ms
     << ", status: " << status.ToString() << ", result: " << result->ToString()
     << ")";
  return ss.str();
}

Status INodeStatUtils::Parse(const std::string& str, INodeStat* stat) {
  return Parse(StringPiece(str), stat);
}

Status INodeStatUtils::Parse(const rocksdb::Slice& str, INodeStat* stat) {
  return Parse(StringPiece(str.data(), str.size()), stat);
}

Status INodeStatUtils::Parse(const StringPiece& str, INodeStat* stat) {
  CHECK(stat != nullptr);
  if (str.empty()) {
    return Status(Code::kBadParameter, "INodeStat::Parse str is empty.");
  }

  Status s;
  do {
    StringPiece sp(str);
    uint32_t magic;
    if (!StringUtils::GetFixed32(&sp, &magic)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse magic: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    if (magic != kINodeStatMagic) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, invalid magic: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }

    uint32_t version;
    if (!StringUtils::GetFixed32(&sp, &version)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse version: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    CHECK(version == kINodeStatVersion);

    uint64_t inode_id = 0;
    if (!StringUtils::GetFixed64(&sp, &inode_id)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse inode_id: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }

    uint64_t inode_num = 0;
    if (!StringUtils::GetFixed64(&sp, &inode_num)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse inode_num: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t file_num = 0;
    if (!StringUtils::GetFixed64(&sp, &file_num)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse file_num: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t dir_num = 0;
    if (!StringUtils::GetFixed64(&sp, &dir_num)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse dir_num: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t block_num = 0;
    if (!StringUtils::GetFixed64(&sp, &block_num)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse block_num: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t data_size = 0;
    if (!StringUtils::GetFixed64(&sp, &data_size)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse data_size: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t txid = 0;
    if (!StringUtils::GetFixed64(&sp, &txid)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot get txid: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }
    uint64_t ts = 0;
    if (!StringUtils::GetFixed64(&sp, &ts)) {
      s = Status(Code::kINodeStatCorrupt,
                 "Corrupted stat value, cannot parse timestamp_sec: " +
                     StringUtils::ToHexString(str.as_string()));
      break;
    }

    stat->inode_id = inode_id;
    stat->inode_num = (int64_t)inode_num;
    stat->file_num = (int64_t)file_num;
    stat->dir_num = (int64_t)dir_num;
    stat->block_num = (int64_t)block_num;
    stat->data_size = (int64_t)data_size;
    stat->txid = (int64_t)txid;
    stat->timestamp_sec = (int64_t)ts;

    CHECK(stat->timestamp_sec != 0);
  } while (0);

  if (!s.IsOK()) {
    LOG(ERROR) << s.ToString();
    return s;
  }

  return Status::OK();
}

void INodeStatUtils::GetFileSizeAndBlockNum(const INode& file, int64_t* size,
                                            int64_t* block_num) {
  int64_t total_size = 0;
  for (auto&& blk : file.blocks()) {
    total_size += blk.numbytes();
  }
  *size = total_size;
  *block_num = file.blocks_size();
}

INodeStatChangeRecorder::INodeStatChangeRecorder(
    const std::shared_ptr<MetaStorage>& meta_storage,
    const std::shared_ptr<INodeStatDeltaCache>& delta_cache)
    : meta_storage_(meta_storage),
      delta_cache_(delta_cache) {
}

INodeStatChangeRecorder::~INodeStatChangeRecorder() {
  if (delta_cache_) {
    delta_cache_->GC(false);
  }
}

void INodeStatChangeRecorder::UpdateAncestors(
    const INodeStat& delta, const std::vector<INodeID>& ancestors) {
  for (auto&& anc : ancestors) {
    stat_deltas_[anc].emplace_back(delta);
    stat_deltas_[anc].back().inode_id = anc;
  }
}

void INodeStatChangeRecorder::GetCurrentINodeStat(
    INodeID dir_id, INodeStat* stat) {
  stat->inode_id = dir_id;

  // get ondisk INodeStat
  (void)meta_storage_->GetDirectoryINodeStat(dir_id, stat);
  // add cached INodeStat delta that may be inflight in previous WriteTask
  // https://bytedance.larkoffice.com/docx/IeKAd7Q3Ho55gcxznOLcJC3Ansg
  if (delta_cache_) {
    delta_cache_->GetCurrentINodeStat(stat);
  }
}

void INodeStatChangeRecorder::RecordINodeDelete(
    int64_t txid, const INode &to_del, const std::vector<INodeID> &ancestors) {
  uint64_t id = to_del.id();
  CHECK(id != kRootINodeId && id != 0);

  txid_ = txid;
  INodeStat delta(id);
  if (to_del.type() == INode_Type_kFile) {
    int64_t size = 0;
    int64_t block_num = 0;
    INodeStatUtils::GetFileSizeAndBlockNum(to_del, &size, &block_num);

    delta.inode_num = -1;
    delta.file_num = -1;
    delta.block_num = -block_num;
    delta.data_size = -size;
  } else {
    CHECK(to_del.type() == INode_Type_kDirectory);
    INodeStat stat(to_del.id());
    GetCurrentINodeStat(to_del.id(), &stat);

    delta.inode_num = -(1 + stat.inode_num);
    delta.dir_num = -(1 + stat.dir_num);
    delta.file_num = -stat.file_num;
    delta.block_num = -stat.block_num;
    delta.data_size = -stat.data_size;
  }

  UpdateAncestors(delta, ancestors);
}

void INodeStatChangeRecorder::RecordINodeAdd(
    int64_t txid, const INode &to_add, const std::vector<INodeID> &ancestors) {
  uint64_t id = to_add.id();
  CHECK(id != kRootINodeId);

  txid_ = txid;
  INodeStat delta(id);
  if (to_add.type() == INode_Type_kFile) {
    int64_t size = 0;
    int64_t block_num = 0;
    INodeStatUtils::GetFileSizeAndBlockNum(to_add, &size, &block_num);

    delta.inode_num += 1;
    delta.file_num += 1;
    delta.block_num += block_num;
    delta.data_size += size;
  } else {
    CHECK(to_add.type() == INode_Type_kDirectory);
    INodeStat stat(id);

    // Add stat for this new directory
    stat_deltas_[id].emplace_back(stat);
    delta.inode_num += 1;
    delta.dir_num += 1;
  }
  UpdateAncestors(delta, ancestors);
}

void INodeStatChangeRecorder::RecordINodeUpdate(
    int64_t txid, const INode &old_inode, const INode &new_inode,
    const std::vector<INodeID> &ancestors) {
  CHECK(old_inode.type() == INode_Type_kFile &&
        new_inode.type() == INode_Type_kFile &&
        old_inode.id() == new_inode.id());

  txid_ = txid;
  int64_t old_size = 0;
  int64_t old_block_num = 0;
  INodeStatUtils::GetFileSizeAndBlockNum(old_inode, &old_size, &old_block_num);

  int64_t new_size = 0;
  int64_t new_block_num = 0;
  INodeStatUtils::GetFileSizeAndBlockNum(new_inode, &new_size, &new_block_num);

  INodeStat delta(new_inode.id());
  delta.block_num = new_block_num - old_block_num;
  delta.data_size = new_size - old_size;

  UpdateAncestors(delta, ancestors);
}

void INodeStatChangeRecorder::RecordINodeRename(
    int64_t txid, const INode &src_inode, const INode &dst_inode,
    const std::vector<INodeID> &src_ancestors,
    const std::vector<INodeID> &dst_ancestors) {
  CHECK(src_inode.id() == dst_inode.id() &&
        src_inode.type() == dst_inode.type());

  txid_ = txid;
  INodeStat src_delta(src_inode.id());
  INodeStat dst_delta(dst_inode.id());
  if (src_inode.type() == INode_Type_kFile) {
    int64_t size = 0;
    int64_t block_num = 0;
    INodeStatUtils::GetFileSizeAndBlockNum(src_inode, &size, &block_num);

    src_delta.inode_num = -1;
    src_delta.file_num = -1;
    src_delta.block_num = -block_num;
    src_delta.data_size = -size;

    dst_delta.inode_num = 1;
    dst_delta.file_num = 1;
    dst_delta.block_num = block_num;
    dst_delta.data_size = size;
  } else {
    CHECK(src_inode.type() == INode_Type_kDirectory);
    INodeStat stat(src_inode.id());
    GetCurrentINodeStat(src_inode.id(), &stat);

    src_delta.inode_num = -(1 + stat.inode_num);
    src_delta.dir_num = -(1 + stat.dir_num);
    src_delta.file_num = -stat.file_num;
    src_delta.block_num = -stat.block_num;
    src_delta.data_size = -stat.data_size;

    dst_delta.inode_num = (1 + stat.inode_num);
    dst_delta.dir_num = (1 + stat.dir_num);
    dst_delta.file_num = stat.file_num;
    dst_delta.block_num = stat.block_num;
    dst_delta.data_size = stat.data_size;
  }
  UpdateAncestors(src_delta, src_ancestors);
  UpdateAncestors(dst_delta, dst_ancestors);
}

void INodeStatChangeRecorder::RecordINodeRenameWithOverwriteDst(
    int64_t txid, const INode &src_inode, const INode &dst_inode,
    const INode &old_dst_inode, const std::vector<INodeID> &src_ancestors,
    const std::vector<INodeID> &dst_ancestors) {
  txid_ = txid;
  if (old_dst_inode.id() != 0) {
    RecordINodeDelete(txid, old_dst_inode, dst_ancestors);
  }
  RecordINodeRename(txid, src_inode, dst_inode, src_ancestors, dst_ancestors);
}

void INodeStatChangeRecorder::UpdateToWriteBatch(
    rocksdb::WriteBatch* wb, rocksdb::ColumnFamilyHandle* cf_handle) {
  for (auto&& it : stat_deltas_) {
    auto&& id = it.first;
    auto&& deltas = it.second;
    CHECK(deltas.size() > 0);

    INodeStat delta_sum(id);
    for (auto&& d : deltas) {
      delta_sum += d;
    }
    delta_sum.txid = txid_;
    delta_sum.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;

    uint64_t key = platform::HostToBigEndian(id);
    auto key_slice =
        rocksdb::Slice(reinterpret_cast<const char*>(&key), sizeof(key));
    wb->Merge(cf_handle, key_slice, delta_sum.Serialize());

    if (delta_cache_) {
      delta_cache_->Add(delta_sum);
    }
  }
}

std::string INodeStatChangeRecorder::GetDebugInfo() {
  cnetpp::base::Object obj;
  obj["txid"] = txid_;
  cnetpp::base::Object stat_deltas;
  for (const auto &kv : stat_deltas_) {
    cnetpp::base::Value value;
    ToJson(kv.second, &value);
    stat_deltas[std::to_string(kv.first)] = std::move(value);
  }
  obj["stat_deltas"] = stat_deltas;
  return cnetpp::base::Parser::Serialize(cnetpp::base::Value(obj));
}

bool INodeStatMergeOperator::Merge(const rocksdb::Slice& key,
                                   const rocksdb::Slice* existing_value,
                                   const rocksdb::Slice& value,
                                   std::string* new_value,
                                   rocksdb::Logger* logger) const {
  uint64_t id = platform::BigEndianToHost(
      *(reinterpret_cast<const uint64_t*>(key.data())));

  dancenn::Status s;
  do {
    INodeStat existing_stat(id);
    if (nullptr != existing_value) {
      s = INodeStatUtils::Parse(*existing_value, &existing_stat);
      if (!s.IsOK()) {
        break;
      }
      if (id != existing_stat.inode_id) {
        LOG(ERROR) << "inode id in existing_value is inconsistent with key: "
                   << id << ", value: " << existing_stat.inode_id;
        s = Status(Code::kINodeStatCorrupt,
                   "inode id in existing_value is inconsistent with key.");
        break;
      }
    }

    INodeStat stat2;
    s = INodeStatUtils::Parse(value, &stat2);
    if (!s.IsOK()) {
      break;
    }
    if (id != stat2.inode_id) {
      LOG(ERROR) << "inode id in value is inconsistent with key: " << id
                 << ", value: " << stat2.inode_id;
      s = Status(Code::kINodeStatCorrupt,
                 "inode id in value is inconsistent with key.");
      break;
    }

    existing_stat += stat2;

    *new_value = existing_stat.Serialize();
  } while (0);

  if (!s.IsOK()) {
    // TODO: Trigger warning, set invalid flag for manual repair
    LOG(ERROR) << "Failed to do INodeStat merge. Clear the stat in DB, need "
                  "manual repair. error: "
               << s.ToString();
    *new_value = INodeStat(kInvalidINodeId).Serialize();
  }

  // Never return false in Merge
  return true;
}

} // namespace dancenn
