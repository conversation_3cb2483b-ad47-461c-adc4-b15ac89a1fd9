#include "namespace/namespace_scrub_pin.h"

#include <absl/strings/str_format.h>

#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "base/time_util.h"
#include "edit/sender_base.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"

namespace dancenn {

const PinScrubStat empty_scrub_stat;

PinScrubStat& PinScrubStat::operator=(const PinScrubStat& other) {
  this->num_file.store(other.num_file.load());
  this->num_dir.store(other.num_dir.load());

  this->timestamp_sec = other.timestamp_sec;

  return *this;
}

PinScrubStat& PinScrubStat::operator+=(const PinScrubStat& other) {
  this->num_file += other.num_file;
  this->num_dir += other.num_dir;

  // update timestamp to now
  this->timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
  return *this;
}

void PinScrubStat::Clear() {
  *this = empty_scrub_stat;
}

std::string PinScrubStat::ToString() const {
  std::stringstream ss;
  ss << absl::StrFormat(
      "PinScrubStat: { file %ld, dir %ld, ", num_file, num_dir);

  ss << absl::StrFormat("ts_sec %lu }", timestamp_sec);
  return ss.str();
}

PinScrubOp::PinScrubOp() {
}

void PinScrubOp::ProcessFile(const INode& file) {
  scrub_stat_.num_file++;

  GenerateStat(file);

  (void)namespace_->ReconcileINodeAttrs(file);
}

void PinScrubOp::ProcessDir(const INode& dir) {
  scrub_stat_.num_dir++;

  GenerateStat(dir);

  (void)namespace_->ReconcileINodeAttrs(dir);
}

Status PinScrubOp::HandleDirResult(ScrubAction action) {
  (void)action;
  return Status::OK();
}

int64_t PinScrubOp::EstimateRemainingSeconds(int64_t finished_inodes,
                                             int64_t elapsed_ms) {
  (void)finished_inodes;
  (void)elapsed_ms;
  return -1;
}

std::string PinScrubOp::ToString() {
  return absl::StrFormat("PinScrubOp { inode %lu, scrub_stats: %s }",
                         node_.id(),
                         scrub_stat_.ToString());
}

ScrubOpPtr PinScrubOp::NewOp() {
  return std::make_shared<PinScrubOp>();
}

void PinScrubOp::GenerateStat(const INode& file) {
  (void)file;
  scrub_stat_.timestamp_sec = TimeUtil::GetNowEpochMs() / 1000;
}

void PinScrubOp::AddSubDirResult(const ScrubOpPtr subdir_op) {
  (void)subdir_op;
  return;
}

}  // namespace dancenn
