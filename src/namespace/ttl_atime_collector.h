#pragma once
#include <atomic>
#include <chrono>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include "namespace/inode.h"
namespace dancenn {

class NameSpace;
class MetaStorage;

struct TtlATimeRecord {
  INodeID inode_id;
  int64_t access_time;

  TtlATimeRecord(INodeID id, int64_t time) : inode_id(id), access_time(time) {
  }
};

struct TtlATimeCollectorConfig {
  size_t batch_size = 1000;  // 默认批量大小
  int timeout_ms = 100;      // 等待超时时间
};

class TtlATimeCollector {
 public:
  TtlATimeCollector(NameSpace* ns,
                    MetaStorage* meta_storage,
                    const TtlATimeCollectorConfig& config);
  ~TtlATimeCollector();

  void Start();
  void Stop();

  void AddAccessRecord(INodeID inode_id, int64_t access_time);
  bool GetAccessRecords(std::vector<TtlATimeRecord>* records);
  void Flush();

 private:
  bool ShouldFlush() const;
  void FlushLoop();

 private:
  TtlATimeCollectorConfig config_;

  std::map<INodeID, int64_t> access_time_map_;
  std::mutex mutex_;
  std::atomic<bool> running_;
  std::unique_ptr<std::thread> flush_thread_;
  NameSpace* ns_{nullptr};
  MetaStorage* meta_storage_{nullptr};
  std::chrono::steady_clock::time_point batch_start_time_;
};

}  // namespace dancenn