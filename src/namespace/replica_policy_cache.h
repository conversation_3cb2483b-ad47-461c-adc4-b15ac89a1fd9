// Copyright 2018 livexmm <<EMAIL>>

#ifndef SRC_NAMESPACE_REPLICA_POLICY_CACHE_H_
#define SRC_NAMESPACE_REPLICA_POLICY_CACHE_H_

#include <string>
#include <vector>
#include <functional>

#include "base/read_write_lock.h"

namespace dancenn {
class ReplicaPolicyCacheNode;

// Deprecated
class ReplicaPolicyCache {
 public:
   using FilterFn = std::function<bool(const std::string, int, const std::string&)>;
 public:
   ReplicaPolicyCache();
   ~ReplicaPolicyCache();

   void GetParentOrSelf(const std::string& path, int* policy_id, std::string* policy_dc);
   void UpdateChildOrSelf(const std::string& path, int policy_id, const std::string& policy_dc);
   // all path end with "/"
   std::vector<std::string> Filter(const FilterFn& fn);
   void DeleteNode(const std::string& path);
 private:
   std::string NormalizePath(const std::string& path);
   std::string NoEndSlashPath(const std::string& path);
   void ReleaseNode(ReplicaPolicyCacheNode* root);
   ReplicaPolicyCacheNode* GetParentOrSelf(ReplicaPolicyCacheNode* root, const std::string& path);
   void Filter(const ReplicaPolicyCacheNode* root, const FilterFn& fn, std::vector<std::string>* v);
 private:
   ReadWriteLock    rwlock_;
   ReplicaPolicyCacheNode* root_{nullptr};
};
}

#endif  // SRC_NAMESPACE_REPLICA_POLICY_CACHE_H_
