#ifndef _NAMESPACE_LIFECYCLE_SCANNER_H_
#define _NAMESPACE_LIFECYCLE_SCANNER_H_

#include <condition_variable>
#include <mutex>
#include <string>
#include <vector>
#include <gtest/gtest.h>

#include <cnetpp/concurrency/thread.h>

#include "DatanodeProtocol.pb.h"

#include "base/metrics.h"
#include "datanode_manager/datanode_manager.h"
#include "namespace/inode.h"
#include "namespace/namespace_scrub_lifecycle_scan.h"

namespace dancenn {

class NameSpace;
class NameSpaceScrubRunner;
class MetaStorage;

class LifecycleScanner : public std::enable_shared_from_this<LifecycleScanner> {
 public:
  explicit LifecycleScanner(NameSpace* ns,
                            std::shared_ptr<DatanodeManager> dn_mgr,
                            std::shared_ptr<MetaStorage> ms,
                            std::shared_ptr<NameSpaceScrubRunner> sr);
  ~LifecycleScanner();

  void Start();
  void Stop();
  void GetStorageClassCmd(const std::string& dn_uuid,
                          const std::string& bpid,
                          cloudfs::datanode::HeartbeatResponseProto* response,
                          BlockManagerMetrics* bm_metrics = nullptr);

 private:
  void InitMetrics();
  void FiniMetrics();
  void AddStorageClassCmd(const StorageClassCmds& scrubbed_dn_cmds);
  void RefreshCmdStat();

 private:
  class ScanTask : public cnetpp::concurrency::Task {
   public:
    ScanTask(std::shared_ptr<LifecycleScanner> scanner,
             NameSpace* ns,
             std::shared_ptr<DatanodeManager> dn_mgr,
             std::shared_ptr<MetaStorage> ms,
             std::shared_ptr<NameSpaceScrubRunner> sr);
    ~ScanTask();
    bool operator()(void* arg) override;
    void Stop();

   private:
    void InitMetrics();
    void FiniMetrics();
    void FilterDepredData();          // Step 1
    void FilterDepredLifecyclePolicy();
    void FilterDepredStorageClassStat();
    void FilterDepredStorageClassReport();
    void CalcScrubRoots();            // Step 2
    bool DoScrubRoots();              // Step 3
    void RefreshStorageClassStat();   // Step 4
    bool CheckNextGroup();            // Step 0

    enum class ScanStep {
      FILTER_DEPRED_DATA,
      CALC_SCRUB_ROOTS,
      DO_SCRUB_ROOTS,
      REFRESH_STAT,
      DONE,
    };

   private:
    ScanStep step_;
    std::shared_ptr<LifecycleScanner> lifecycle_scanner_;
    NameSpace* ns_{nullptr};
    std::shared_ptr<DatanodeManager> dn_mgr_;
    std::shared_ptr<MetaStorage> ms_;
    std::shared_ptr<NameSpaceScrubRunner> scrub_runner_;
    std::mutex mtx_;
    std::condition_variable cond_;

    uint64_t last_group_run_epoch_{ 0 };
    std::vector<INodeID> scrub_roots_;
    StopWatch scrub_sw_;
    bool scrub_sw_started_{ false };
    bool all_depred_report_filtered_{ false };

    // metrics
    MetricID policy_filter_depred_time_;
    MetricID policy_scanned_cnt_;
    MetricID policy_filtered_cnt_;
    MetricID stat_filter_depred_time_;
    MetricID stat_scanned_cnt_;
    MetricID stat_filtered_cnt_;
    MetricID report_filter_depred_time_;
    MetricID report_scanned_cnt_;
    MetricID report_filtered_cnt_;
    MetricID subroot_calc_time_;
    MetricID subroot_calc_cnt_;
    MetricID scrub_time_;
  };

  NameSpace* ns_{nullptr};
  std::shared_ptr<DatanodeManager> dn_mgr_;
  std::shared_ptr<MetaStorage> ms_;
  std::shared_ptr<NameSpaceScrubRunner> scrub_runner_;
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;

  std::mutex mtx_;
  // dnuuid -> { cls -> blkids }
  StorageClassCmds dn_cmds_mapping_;
  uint64_t num_cmds_staged_[cloudfs::StorageClassProto_ARRAYSIZE]{};
  LifecycleScrubStat scrub_stat_;

  // metrics
  std::shared_ptr<Metrics> metrics_;
  std::vector<std::shared_ptr<Gauge>> gauge_;

  FRIEND_TEST(LifecycleTest, LifecycleScannerScrub);
};

}  // namespace dancenn

#endif
