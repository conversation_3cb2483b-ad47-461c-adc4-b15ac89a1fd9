#include "namespace/lifecycle_policy_util.h"

#include "base/logger_metrics.h"
#include "base/platform.h"
#include "base/status.h"
#include "base/time_util.h"

DECLARE_bool(lifecycle_enable);
DECLARE_string(default_storage_class);

using cloudfs::StorageClassReportProto;

namespace dancenn {

StorageClassProto default_storage_class = StorageClassProto::WARM;
LifecyclePolicyProto default_lifecycle_policy;

void GetEffectiveStorageClass(const LifecyclePolicyProto& policy,
                              int64_t delta_ms,
                              StorageClassProto* cls) {
  CHECK(LifecyclePolicyIsValid(policy));

  // target class is determined based on the following priorities:
  // 1. transition_rule matched
  // 2. default_class matched
  StorageClassProto tgt_cls = default_storage_class;
  if (policy.has_defaultclass()) {
    tgt_cls = policy.defaultclass();
  }

  int64_t latest_ttl_ms = -1;
  for (auto&& trule : policy.transrules()) {
    if (trule.days() == 0 && policy.has_defaultclass()) {
      // backward compatibility, TTL=0 only works when defaultClass not set
      continue;
    }

    int64_t ttl_ms = trule.days() * 24 * 60 * 60 * 1000;
    if (delta_ms < ttl_ms) {
      continue;
    }

    if ((latest_ttl_ms == -1) ||
        (latest_ttl_ms < ttl_ms) ||
        (latest_ttl_ms == ttl_ms && tgt_cls < trule.targetclass())) {
      // get the colder one of latest transition rules in effect
      latest_ttl_ms = ttl_ms;
      tgt_cls = trule.targetclass();
    }
  }
  CHECK_NE(tgt_cls, StorageClassProto::NONE);
  *cls = tgt_cls;
}

bool IsExpRuleValid(const cloudfs::ExpirationRuleProto& exp_rule) {
  if (!(exp_rule.has_days() || exp_rule.has_seconds())) {
    return false;
  }

  if (exp_rule.has_days() && exp_rule.has_seconds()) {
    return false;
  }

  // If 'days' specified, 'days' shall be non-negative
  if (exp_rule.has_days() && exp_rule.days() < 0) {
    return false;
  }

  // If 'seconds' specified, 'seconds' shall be non-negative
  if (exp_rule.has_seconds() && exp_rule.seconds() < 0) {
    return false;
  }
  return true;
}

int GetEffectiveLifecyclePolicy(MetaStorage* ms,
                                INodeID id,
                                std::function<bool(LifecyclePolicyProto*)> filter,
                                LifecyclePolicyProto* policy,
                                INodeID* effective_inode_id) {
  int distance = 0;
  INode inode;
  LifecyclePolicyProto plcy;
  while (true) {
    // try to get effective policy from nearest parent
    auto st = ms->GetLifecyclePolicy(id, nullptr, &plcy);
    if (st.IsOK()) {
      policy->CopyFrom(plcy);
      CHECK(LifecyclePolicyIsValid(*policy));
      if (!filter || filter(policy)) {
        if (effective_inode_id != nullptr) {
          *effective_inode_id = id;
        }
        return distance;
      }
    }
    distance++;

    if (id == kRootINodeId) {
      break;
    }
    auto sc = ms->GetINode(id, &inode, nullptr);
    if (sc != kOK) {
      break;
    }
    id = inode.parent_id();
  }

  policy->CopyFrom(default_lifecycle_policy);
  CHECK(LifecyclePolicyIsValid(*policy));
  return -1;
}

void GetCurrentStorageClass(MetaStorage* ms,
                            INodeID id,
                            StorageClassProto* cls) {
  if (!FLAGS_lifecycle_enable) {
    bool ok = StorageClassName2ID(FLAGS_default_storage_class, cls);
    CHECK(ok);
    return;
  }

  INode inode;
  int delta_ms;
  LifecyclePolicyProto policy;
  (void)GetEffectiveLifecyclePolicy(ms, id, nullptr/* filter */, &policy);
  StatusCode sc = ms->GetINode(id, &inode, nullptr);
  if (sc != kOK) {
    // in case caller just query default cls of inode that just about to create
    delta_ms = 0;
  } else {
    delta_ms = TimeUtil::GetNowEpochMs() - inode.mtime();
  }
  GetEffectiveStorageClass(policy, delta_ms, cls);
}

bool LifecyclePolicyIsValid(const LifecyclePolicyProto& policy) {
  // A valid policy is composed by at least one type of following rules:
  //  1. default storage class
  //  2. expiration rule
  //  3. transition rule(s)
  // https://bytedance.feishu.cn/docx/KuKjdLSgOoJdSJxbRR2cFhBknlc
  if (!policy.has_defaultclass() &&
      !policy.has_exprule() &&
      policy.transrules_size() == 0) {
    return false;
  }

  // 1. If 'defaultclass' specified, it shall be valid value
  if (policy.has_defaultclass()) {
    if (policy.defaultclass() == StorageClassProto::NONE) {
      return false;
    }
  }

  // 2. If 'exprule' specified, either 'days' or 'seconds' shall be valid value
  if (policy.has_exprule()) {
    if (!IsExpRuleValid(policy.exprule())) {
      return false;
    }
  }

  // 3. If 'transrules' specified, 'days' shall be non-negative,
  // 'targetclass' shall be valid value
  if (policy.transrules_size() > 0) {
    for (auto&& trule : policy.transrules()) {
      if ((!trule.has_days() || trule.days() < 0) ||
          (!trule.has_targetclass() ||
           trule.targetclass() == StorageClassProto::NONE)) {
        return false;
      }
    }
  }

  return true;
}

bool LifecycleNeedExpire(const LifecyclePolicyProto& policy,
                         int64_t delta_ms) {
  CHECK(LifecyclePolicyIsValid(policy));
  if (!policy.has_exprule()) {
    return false;
  }

  if (policy.exprule().has_seconds()) {
    uint64_t ttl_ms = policy.exprule().seconds() * 1000;
    if (delta_ms < ttl_ms) {
      return false;
    }
    return true;
  }

  if (policy.exprule().has_days()) {
    uint64_t ttl_ms = policy.exprule().days() * 24 * 60 * 60 * 1000;
    if (delta_ms < ttl_ms) {
      return false;
    }
    return true;
  }

  return false;
}

// XXX
// Previously, reports are updated incrementally and asynchronously in the form
// of merge operator.
// To provide real-time visibility after update, we refactor the implementation
// by using Get/Put/Delete of RocksDB, except for Merge.
// Due to backward compatibility, however, the implementation of merge operator
// is preserved to exhaust all merge-ops generated in previous revision. But it
// should not be used at runtime anymore.
//
// https://meego.feishu.cn/cfs/issue/detail/16358405
bool StorageClassReportMergeOperator::Merge(const rocksdb::Slice& key,
                                            const rocksdb::Slice* value_old,
                                            const rocksdb::Slice& value_new,
                                            std::string* value_res,
                                            rocksdb::Logger* logger) const {
  // XXX MetaStorage::DecodeBlockID()
  CHECK_EQ(key.size(), sizeof(BlockID) / sizeof(uint8_t));
  BlockID blkid = platform::ReadBigEndian<BlockID>(key.data(), 0);

  // Merge(proto_old, proto_new)
  dancenn::Status st;
  do {
    // decode 'proto_old'
    StorageClassReportProto proto_old;
    if (value_old != nullptr) {
      bool ok = proto_old.ParseFromArray(value_old->data(),
                                         value_old->size());
      if (!ok) {
        std::string msg = absl::StrFormat(
            "Failed to parse StorageClassReportProto of BlockID %lu",
            blkid);
        LOG(ERROR) << msg;
        st = Status(Code::kError, msg);
        break;
      }
    } else {
      proto_old.Clear();
    }
    CHECK_EQ(proto_old.dnuuid_size(), proto_old.reportedclass_size());

    // decode 'proto_new'
    StorageClassReportProto proto_new;
    auto ok = proto_new.ParseFromArray(value_new.data(),
                                       value_new.size());
    if (!ok) {
      std::string msg = absl::StrFormat(
          "Failed to parse StorageClassReportProto of BlockID %lu",
          blkid);
      LOG(ERROR) << msg;
      st = Status(Code::kError, msg);
      break;
    }
    CHECK_EQ(proto_new.dnuuid_size(), proto_new.reportedclass_size());

    // use 'proto_old' as base, apply 'proto_new' as delta
    StorageClassReportProto proto_res;
    proto_res.CopyFrom(proto_old);
    for (int idx_new = 0; idx_new < proto_new.dnuuid_size(); idx_new++) {
      // iterate each record in delta, apply it into base
      auto& dnuuid_new = proto_new.dnuuid(idx_new);

      // locate correspond record in base
      int idx_res;
      for (idx_res = 0; idx_res < proto_res.dnuuid_size(); idx_res++) {
        auto& dnuuid_res = proto_res.dnuuid(idx_res);
        if (dnuuid_new == dnuuid_res) {
          break;
        }
      }

      bool old_found = idx_res < proto_res.dnuuid_size();
      auto new_cls = proto_new.reportedclass(idx_new);
      if (old_found) {
        // DN reported before, update in-place
        proto_res.set_reportedclass(idx_res, new_cls);
      } else {
        // DN not reported before, insert new record
        proto_res.add_dnuuid(dnuuid_new);
        proto_res.add_reportedclass(new_cls);
      }
    }

    proto_res.SerializeToString(value_res);
  } while (0);

  if (!st.IsOK()) {
      LOG(ERROR) << absl::StrFormat(
          "Failed to merge StorageClassReportProto of BlockID %lu, %s",
          blkid, st.code());
  }

  LOG_EVERY_N(WARNING, 10000) << absl::StrFormat(
      "StorageClassReportMergeOperator is deprecated and should only be used "
      "to exhaust all merge-ops generated in previous revision. "
      "Do not use it anymore!");
  return true;
}

} // namespace dancenn
