#include "policy_manager.h"

#include <cnetpp/base/string_piece.h>
#include <cnetpp/base/string_utils.h>

#include "base/constants.h"
#include "base/string_utils.h"
#include "namespace/xattr.h"

DECLARE_bool(write_back_by_default);
DECLARE_bool(disable_dirpolicy_as_workaround_for_kvcache);

namespace dancenn {

const ReplicaPolicy kDefaultReplicaPolicy;

const ReadPolicy kDefaultReadPolicy;

UploadPolicy kDefaultUploadPolicy;

PolicyManager::PolicyManager() {
  PolicyConfig<ReplicaPolicy> replica_policy_config;
  replica_policy_config.default_value = kDefaultReplicaPolicy;
  replica_policy_config.policy_name = "ReplicaPolicy";
  replica_policy_config.xattr_name = kReplicaPolicyXAttr;
  replica_policy_config.get_func = &PolicyManager::GetReplicaPolicyHelper;
  replica_policy_config.allow_file_level = false;
  replica_policy_manager_ =
      std::make_shared<PolicyManagerBase<ReplicaPolicy>>(replica_policy_config);

  PolicyConfig<ReadPolicy> read_policy_config;
  read_policy_config.default_value = kDefaultReadPolicy;
  read_policy_config.policy_name = "ReadPolicy";
  read_policy_config.xattr_name = kReadPolicyXAttr;
  read_policy_config.get_func = &PolicyManager::GetReadPolicyHelper;
  read_policy_config.allow_file_level = false;
  read_policy_manager_ =
      std::make_shared<PolicyManagerBase<ReadPolicy>>(read_policy_config);

  PolicyConfig<UploadPolicy> upload_policy_config;
  kDefaultUploadPolicy.set_upload_interval_ms(FLAGS_write_back_by_default ? 0
                                                                          : -1);
  upload_policy_config.default_value = kDefaultUploadPolicy;
  upload_policy_config.policy_name = "UploadPolicy";
  upload_policy_config.xattr_name = kUploadPolicyXAttr;
  upload_policy_config.get_func = &PolicyManager::GetUploadPolicyHelper;
  upload_policy_config.allow_file_level = true;
  upload_policy_manager_ =
      std::make_shared<PolicyManagerBase<UploadPolicy>>(upload_policy_config);
}

bool PolicyManager::HasPolicy(const INode& inode) {
  ReplicaPolicy replica_policy;
  if (PolicyManager::GetReplicaPolicyHelper(inode, &replica_policy)) {
    return true;
  }

  ReadPolicy read_policy;
  if (PolicyManager::GetReadPolicyHelper(inode, &read_policy)) {
    return true;
  }

  UploadPolicy upload_policy;
  if (PolicyManager::GetUploadPolicyHelper(inode, &upload_policy)) {
    return true;
  }

  return false;
}

void PolicyManager::ReloadINodeXAttr(
    const std::string& path,
    const INode& inode,
    const RepeatedPtrField<XAttrProto>& to_upsert,
    const RepeatedPtrField<XAttrProto>& to_remove) {
  if (FLAGS_disable_dirpolicy_as_workaround_for_kvcache) {
    return;
  }
  if (inode.type() != INode::kDirectory) {
    // ignore file-level
    return;
  }
  VLOG(10) << "ReloadINodeXAttr"
           << " path=" << path << " inode=" << inode.ShortDebugString()
           << " to_upsert=" << to_upsert.size()
           << " to_remove=" << to_remove.size();
  replica_policy_manager_->ReloadPolicy(path, inode, to_upsert, to_remove);
  read_policy_manager_->ReloadPolicy(path, inode, to_upsert, to_remove);
  upload_policy_manager_->ReloadPolicy(path, inode, to_upsert, to_remove);
}

void PolicyManager::LoadAllPolicy(const std::string& full_path,
                                  const INode& inode) {
  if (FLAGS_disable_dirpolicy_as_workaround_for_kvcache) {
    return;
  }
  if (inode.type() != INode::kDirectory) {
    // ignore file-level
    return;
  }
  if (full_path.empty()) {
    LOG(WARNING) << "LoadAllPolicy full_path.empty(), maybe inode is deleted."
                 << " inode=" << inode.ShortDebugString();
    return;
  }
  std::string fullpath = full_path;
  if (full_path == "//") {
    fullpath = "/";
  }
  LOG(INFO) << "LoadAllPolicy fullpath=" << fullpath
            << " inode=" << inode.ShortDebugString();
  replica_policy_manager_->LoadPolicy(fullpath, inode);
  read_policy_manager_->LoadPolicy(fullpath, inode);
  upload_policy_manager_->LoadPolicy(fullpath, inode);
}

void PolicyManager::RemoveAllPolicy(const std::string& full_path,
                                    const INode& inode) {
  if (FLAGS_disable_dirpolicy_as_workaround_for_kvcache) {
    return;
  }
  if (inode.type() != INode::kDirectory) {
    // ignore file-level
    return;
  }
  if (full_path.empty()) {
    LOG(WARNING) << "RemoveAllPolicy full_path.empty(), maybe inode is deleted."
                 << " inode=" << inode.ShortDebugString();
    return;
  }
  std::string fullpath = full_path;
  if (full_path == "//") {
    fullpath = "/";
  }
  LOG(INFO) << "RemoveAllPolicy fullpath=" << fullpath
            << " inode=" << inode.ShortDebugString();
  replica_policy_manager_->RemovePolicy(fullpath);
  read_policy_manager_->RemovePolicy(fullpath);
  upload_policy_manager_->RemovePolicy(fullpath);
}

// ReplicaPolicy
bool PolicyManager::GetReplicaPolicyHelper(const INode& inode,
                                           ReplicaPolicy* policy) {
  XAttrProto::XAttrNamespaceProto ns =
      XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM;
  cnetpp::base::StringPiece xattr_ns("system");
  auto pb_name = cnetpp::base::StringPiece(kReplicaPolicyXAttr)
                     .substr(xattr_ns.size() + 1);
  auto id_name = cnetpp::base::StringPiece(kReplicaPolicyTypeXAttr)
                     .substr(xattr_ns.size() + 1);
  auto dc_name = cnetpp::base::StringPiece(kReplicaPolicyDCXAttr)
                     .substr(xattr_ns.size() + 1);

  // back compatibility
  bool xattr_found = false;
  int id;
  std::string dc;
  for (const auto& xattr : inode.xattrs()) {
    if (xattr.namespace_() != ns) {
      continue;
    }
    if (cnetpp::base::StringPiece(xattr.name()) == pb_name) {
      if (!policy->ParseFromString(xattr.value())) {
        LOG(ERROR) << "ParseFromString failed, inode="
                   << inode.ShortDebugString();
      }
      return true;
    }
    if (cnetpp::base::StringPiece(xattr.name()) == id_name) {
      id = static_cast<int32_t>(xattr.value()[0]);
      xattr_found = true;
    }
    if (cnetpp::base::StringPiece(xattr.name()) == dc_name) {
      dc = xattr.value();
    }
  }

  if (xattr_found) {
    policy->set_distributed(id == kDistributePolicy);
    auto dcs = StringUtils::SplitByChars(dc, ",");
    for (auto d : dcs) {
      policy->add_dc(d);
    }
  }

  return xattr_found;
}

// ReadPolicy
bool PolicyManager::GetReadPolicyHelper(const INode& inode,
                                        ReadPolicy* policy) {
  static auto ns =
      XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM;
  static cnetpp::base::StringPiece xattr_ns("system");

  static auto policy_name =
      cnetpp::base::StringPiece(kReadPolicyXAttr).substr(xattr_ns.size() + 1);
  for (const auto& xattr : inode.xattrs()) {
    if (xattr.namespace_() != ns) {
      continue;
    }
    if (cnetpp::base::StringPiece(xattr.name()) == policy_name) {
      return policy->ParseFromString(xattr.value());
    }
  }

  return false;
}

// UploadPolicy
bool PolicyManager::GetUploadPolicyHelper(const INode& inode,
                                          UploadPolicy* policy) {
  static auto ns =
      XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM;
  static cnetpp::base::StringPiece xattr_ns("system");

  static auto policy_name =
      cnetpp::base::StringPiece(kUploadPolicyXAttr).substr(xattr_ns.size() + 1);
  for (const auto& xattr : inode.xattrs()) {
    if (xattr.namespace_() != ns) {
      continue;
    }
    if (cnetpp::base::StringPiece(xattr.name()) == policy_name) {
      return policy->ParseFromString(xattr.value());
    }
  }

  return false;
}

}  // namespace dancenn