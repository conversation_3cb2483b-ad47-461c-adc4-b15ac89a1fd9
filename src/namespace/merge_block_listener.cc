#include "namespace/merge_block_listener.h"

#include <glog/logging.h>

#include "namespace/meta_scanner.h"
#include "namespace/namespace.h"

DECLARE_uint32(merge_block_task_max_ongoing);

namespace dancenn {

bool MergeBlockListener::PreScan() {
  scan_start_ = std::chrono::steady_clock::now();
  std::lock_guard<std::mutex> guard(mut_);
  std::vector<uint64_t> to_remove;
  for (uint64_t inode_id : merging_inodes_) {
    bool is_merging;
    auto s = ns_->IsINodeMergingBlocks(inode_id, &is_merging);
    if (s.<PERSON>()) {
      if (!is_merging) {
        to_remove.emplace_back(inode_id);
      }
    } else {
      LOG(WARNING) << "Failed to check merging for inode " << inode_id;
      to_remove.emplace_back(inode_id);
    }
  }
  for (uint64_t inode_id : to_remove) {
    merging_inodes_.erase(inode_id);
  }
  processed_ = 0;
  return true;
}

std::vector<INodeID> MergeBlockListener::ScanIndexes() {
  return {kRootINodeId};
}

// TODO(zhuangsiyu): clean up failed merge tasks?
bool MergeBlockListener::Handle(const std::string& full_path,
                                const INode& inode) {
  (void)full_path;
  if (inode.type() == INode_Type_kFile) {
    {
      std::lock_guard<std::mutex> guard(mut_);
      if (merging_inodes_.size() >= FLAGS_merge_block_task_max_ongoing) {
        std::stringstream ss;
        ss << "Too many merging tasks, max is "
          << FLAGS_merge_block_task_max_ongoing << ". inodes=[";
        for (uint64_t inode_id : merging_inodes_) {
          ss << inode_id << ",";
        }
        ss << "]";
        return false;
      }
    }
    ns_->MergeBlocks(inode.id());
    processed_++;
  }
  return true;
}

void MergeBlockListener::PostScan() {
  auto scan_end = std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      scan_end - scan_start_)
                      .count();

  std::stringstream ss;
  ss << "Total number of inodes = " << processed_
     << ", number of merging tasks = " << merging_inodes_.size()
     << ", timecost = " << duration << "msec";
  LOG(INFO) << ss.str();
}

void MergeBlockListener::AddOngoingTask(uint64_t inode_id) {
  std::lock_guard<std::mutex> guard(mut_);
  merging_inodes_.insert(inode_id);
}

};  // namespace dancenn
