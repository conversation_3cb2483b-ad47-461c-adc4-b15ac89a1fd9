//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
//

#ifndef NAMESPACE_PERMISSION_CHECKER_H_
#define NAMESPACE_PERMISSION_CHECKER_H_

#include <string>
#include <set>
#include <vector>

#include "acl.pb.h"
#include "ranger.pb.h"
#include "bridger/socket_wrapper.h"
#include "base/status.h"
#include "namespace/inode.h"
#include "namespace/user_group_info.h"
#include "namespace/meta_storage.h"

// Authentication/Authorization related things.
//
// Ref:
// HDFS permission: https://hadoop.apache.org/docs/stable/hadoop-project-dist/hadoop-hdfs/HdfsPermissionsGuide.html
// Ranger AuditLog: https://github.com/apache/ranger/blob/master/hdfs-agent/src/main/java/org/apache/ranger/authorization/hadoop/RangerHdfsAuthorizer.java#L1019
//                  processResult method.
namespace dancenn {


// For every action we check 'X' permission implicitly.
// When inode is a file, we have to check X permission of its' parent,
// and X permission of itself in case of inode is a directory.
// Consider 'create' scenario, inode is not filled, thus we have a hint to help check permission.
// For scenario of CREATING INODE, mkdir/createSymLink/createFile, we have to pass a hint cause the inode is not filled.
// We also have a NOT_SET to protect from the abnormal cases: inode mustn't be not filled.
enum class INodeHint {
  FILE, /*symlink counts.*/
  DIRECTORY,
  NOT_SET
};

/*
 * Has ranger && enable ranger -> ranger.
 * TBD: where to restore acl entries.
 * Now short circuited: (No ranger || not enable ranger) && enable acl -> acl.
 * (No ranger || not enable ranger) && no enable acl -> posix.
 */
class FsPermissionChecker {
 public:
  enum AuthStatus {
    ALLOW,
    DENY,
    NOT_DETERMINED,
  };

  enum ACCESS_CHECK {
    PARENT,
    ANCESTOR,
    FINAL,
    SUBTREE,

    NONE,
  };

  FsPermissionChecker(std::string original_path,
                      const UserGroupInfo& fs_info,
                      const UserGroupInfo& user_info,
                      MetaStorage& meta_storage,
                      RpcController* c);

  ~FsPermissionChecker() = default;

  bool IsSuperUser() const {
    return is_super_;
  }

  const std::string& GetUser() const {
    return user_;
  }

  const std::string& GetGroup() const {
    return group_;
  }

  const UserGroupInfo& GetCallerUgi() const {
    return caller_ugi_;
  }

  /*
   * This func could check permission for specified action(s).
   *
   * Set FLAGS_permission_enabled to true to enable permission check.
   * Set FLAGS_permission_model to choose ranger/posix.
   *
   * When using ranger model, we also have ranger audit log. By specify
   * flush_audit_at_last to true, the last call to ranger would generate
   * auditLog. Deny action would be record in any case.
   *
   * */
  Status CheckPermission(const UserGroupInfo& fs_ugi,
                         const UserGroupInfo& caller_ugi,
                         const std::vector<INode>& ancestors,
                         const INode& inode,
                         const std::vector<cnetpp::base::StringPiece>& path_components,
                         const std::string& path,
                         bool do_check_owner,
                         const FsAction* const ancestor_access,
                         const FsAction* const parent_access,
                         const FsAction* const access,
                         INodeHint inode_hint,
                         const FsAction* const sub_access,
                         bool ignore_empty_dir,
                         bool flush_audit_at_last) const;


  Status CheckTraverse(const std::vector<INode>& inodes,
                       const std::vector<cnetpp::base::StringPiece>& path_components) const;

  Status CheckWithRanger(const std::vector<INode>& inodes,
                         const std::vector<cnetpp::base::StringPiece>& path_components,
                         FsAction fs_action,
                         AuthStatus* auth_status);

  Status Check(const std::vector<INode>& inodes,
               const std::vector<cnetpp::base::StringPiece>& path_components,
               FsAction fs_action) const;

  AuthStatus CheckRangerAccess(const std::string& user,
                               const std::string& path,
                               const std::string& group,
                               const FsAction& inode_hint,
                               bool flush_audit_if_allow) const;

  AuthStatus CheckRangerSubAccess(const std::string& pre_path,
                                  const INode& inode,
                                  bool ignore_empty_dir,
                                  const FsAction& sub_access,
                                  bool flush_audit_if_pass,
                                  bool with_ranger) const;

  Status Check(const INode& inode,
               const std::vector<cnetpp::base::StringPiece>& path_components,
               int idx,
               FsAction fs_action) const;

  Status CheckIsDirectory(const INode& inode,
                          const cnetpp::base::StringPiece& path_component) const;

  Status CheckStickyBit(const std::vector<INode>& ancestors,
                        const INode& inode,
                        const std::vector<cnetpp::base::StringPiece>& path_components) const;

  std::string ToAccessControlString(const std::string& path,
                                    FsAction access) const;

  bool IsStickyBitViolated(const INode& parent, const INode& inode) const;

  Status CheckSubAccess(const std::vector<cnetpp::base::StringPiece>& path_components,
                        const INode& inode,
                        FsAction access,
                        bool ignore_empty_dir,
                        MetaStorage& meta_storage) const;

  std::string GenStickyException(const std::string& inode_path,
                                 const std::string& parent_path,
                                 const INode& inode,
                                 const INode& parent) const;

  void FlushAuditLogIfNeeded();

 private:
  enum PermissionModel {
    POSIX,
    RANGER,
  };


  bool HasPermission(const INode& inode, FsAction access) const;

  /*
   * Actually fs_owner_ field. Follow java style.
   * Given a path /a/b/c, has inodes [root, inode_a, inode_b, inode_c]
   * with corresponding path_components ["", "a", "b", "c"].
   * Some times we mkdir -p /a/b/c/d in case of only /a/b but no /a/b/c exists,
   * the inodes would filled with null,
   * here we use proto's IsInitialized to represent the nullness.
   *
   * For action which inode may not exists, the inode may be not filled, pass inode_hint to help.
   * If inode_hint is set to NOT_SET, there is a check on inode fill status.
   * */
  Status CheckPermission(const UserGroupInfo& fs_ugi,
                         const UserGroupInfo& caller_ugi,
                         const std::vector<INode>& ancestors,
                         const INode& inode,
                         const std::vector<cnetpp::base::StringPiece>& path_components,
                         const std::string& path,
                         bool do_check_owner,
                         const FsAction* const ancestor_access,
                         const FsAction* const parent_access,
                         const FsAction* const access,
                         INodeHint inode_hint,
                         const FsAction* const sub_access,
                         bool ignore_empty_dir,
                         bool with_ranger,
                         ACCESS_CHECK audit_check) const;

  Status CheckOwner(const INode& inode,
                    const std::vector<cnetpp::base::StringPiece>& path_components) const;

  // Original path, record for audit.
  std::string path_to_be_verified_;

  UserGroupInfo caller_ugi_;

  // Info of super owner.
  std::string fs_owner_;
  std::string super_group_;

  // Info of caller.
  std::string user_;
  std::string group_;

  bool is_super_;

  std::string operation_type_;

  // TODO(wangning.ito) Attribute Provider.
  //
  // Check acl permission.
  MetaStorage& meta_storage_;

  PermissionModel model_;

  std::string fail_reason_;

  uint64_t event_time_;

  std::string client_ip_;

  bridger::SockAddr addr_;

  // TODO(wangning.ito) Consider use inode ref instead.
  mutable std::string inode_owner_;
};


class AccessControlEnforcer {
public:

  void checkPermissionWithContext();
};


struct AuthorizationContext {
  // This determines whether user is superuser or not.
  std::string fs_owner;
  std::string super_group;

  UserGroupInfo caller_ugi;
  // NB: We don't have inode attrs, all were stored in inode object.
  std::vector<INode> inodes;

  int32_t snapshot_id;

  std::string path;
  int ancestor_index;
  bool check_owner;
};

}  // namespace dancenn
#endif  // NAMESPACE_PERMISSION_CHECKER_H_
