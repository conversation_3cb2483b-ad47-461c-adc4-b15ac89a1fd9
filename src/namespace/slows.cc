#include "namespace/slows.h"
#include "namespace/meta_storage_write_task.h"

namespace dancenn {

  Slows::Slows(int n, int mailbox) {
    auto center = MetricsCenter::Instance();
    auto metrics = center->RegisterMetrics("Slows");
    pending_time_ = metrics->RegisterHistogram("Pending");
    execute_time_ = metrics->RegisterHistogram("Execute");
    pending_num_ = metrics->RegisterGauge(
        "NumPendingTask",
        [this]() -> double { return NumPending(); });

    group_ = std::make_unique<WriteTaskActorGroup>(
        [this](meta_storage::WriteTask* task){
      MFH(pending_time_)->Update(task->NextTick());
      task->Next();
      MFH(execute_time_)->Update(task->NextTick());
      delete task;
    }, n, mailbox, "SlowEP");
    metrics->RegisterGauge("PendingTask",
                           [this]() -> int { return NumPending(); });
  }

  Slows::~Slows() {
    Stop();
  }

  void Slows::Start() {
    group_->Start();
  }

  void Slows::Stop() {
    group_->Stop();
  }

  void Slows::Post(meta_storage::WriteTask* task) {
    group_->Tell(task, task->id_for_partition_cb());
  }

  int Slows::NumPending() const {
    return group_->NumPending();
  }

  void Slows::WaitNoPending() {
    while (true) {
      int n = group_->NumPending();
      if (n == 0) break;
      LOG(INFO) << "Slows wait no pending, remain:  " << n;
      usleep(50 * 1000);
    }
  }
}
