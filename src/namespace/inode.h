//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguo<PERSON> <<EMAIL>>
//
#ifndef NAMESPACE_INODE_H_
#define NAMESPACE_INODE_H_

#include <cnetpp/base/string_utils.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <chrono>
#include <string>
#include <vector>

#include "inode.pb.h"  // NOLINT
#include "edit_log.pb.h"
#include "base/constants.h"
#include "base/time_util.h"
#include "block_manager/block.h"
#include "datanode_manager/storage_policy.h"
#include "inode.pb.h"  // NOLINT
#include "namespace/permission.h"

DECLARE_uint64(dfs_block_size);
DECLARE_int32(dfs_replication);
DECLARE_bool(expand_replica);

namespace dancenn {

using INodeID = uint64_t;

// information extracted when we resolve a path for read.
struct SnapshotReadInfo {
  void Clear();

  // If true, the inode is under .snapshot/<snapshot> directory(including
  // .snapshot/<snapshot> itself)
  bool is_under_snapshot_ = false;
  // If true, the inode is .snapshot directory
  bool is_snapshot_directory_ = false;
  // create_txid of corresponding snapshot which is being accessed. Used when
  // is_under_snapshot_
  uint64_t read_txid_ = 0;
};

// Contains INode information resolved from a given path.
struct INodeInPath {
 public:
  INodeInPath() = default;
  INodeInPath(const INodeInPath& other);
  INodeInPath& operator=(const INodeInPath& other);

  void Swap(INodeInPath* other);
  void Clear();

  INodeInPath(INodeInPath&& x) noexcept : INodeInPath() {
    Swap(&x);
  }
  INodeInPath& operator=(INodeInPath&& x) noexcept {
    if (&x != this) {
      INodeInPath(std::move(x)).Swap(this);  // will clear x
    }
    return *this;
  }

  enum CheckedWrite {
    NOT_CHECKED,
    CHECKED_MODIFY,
    CHECKED_DELETE
  };

  // functions used for path resolving.
  void CollectSnapshotInfo(const INode& inode);
  void BuildChildIIP(INode* child_inode, INodeInPath* out) const;
  void SwitchToChild(const INode& child_inode);

  // There are 3 ways to mutate inode:
  // 1. call MutableInode() to get a mutable inode, used for most cases;
  // 2. call MutableInodeUnsafe(), used when we don't want to snapshot incoming
  // inode modification, for example: resolve path, block recovery, etc;
  // 3. call RecordDeletion() then MutableInodeUnsafe(), used when we want to
  // delete an inode.
  INode& MutableInode();
  INode& MutableInodeUnsafe();
  void RecordModification();
  void RecordDeletion();
  void SetModificationTime(uint64_t mtime);
  void SetAccessTime(uint64_t atime);
  // generate snapshot inode without related *_txid fields set, which shall be
  // updated after txid acquired from editlog-sender.
  // return nullptr if no snapshot inode generated
  INode* GenerateSnapshotINode();
  // generate all snapshot-related data for both editlog and rocksdb, with
  // *_txid fields to-be-set once txid acquired form editlog-sender.
  void GenerateSnapshotLog(SnapshotLog* snaplog);

  // SnapshotReadInfo setters
  void SetIsUnderSnapshot(bool);
  void SetIsSnapshotDirectory(bool);
  void SetReadTxid(uint64_t txid);

  INode OldInode() const;
  const INode& Inode() const;
  bool HasSnapshotINode() const;
  INodeID SnapshotRootId() const;
  const SnapshotReadInfo& GetSnapshotReadInfo() const;
  TxID ReadTxid() const;
  bool IsUnderSnapshot() const;
  std::string DebugINodeStr() const;

  // If return true, this inode may have valid snapshot inode, we can not
  // delete its blocks like normal inodes, and should write snapshot inode to
  // record deletion in snapshot table
  bool NeedBackupForDeletion() const;

  // If return true, we need to save its backup to snapshot data when modifying
  bool NeedBackupForModification() const;

  void CollectAttrs(const INode& inode);
  void FinalizeAttrs();

 private:
  INode inode_;
  CheckedWrite checked_write_ = NOT_CHECKED;

  // used by read-rpc
  SnapshotReadInfo snapshot_read_info_;

  // used by write-rpc
  // if the inode is descendant of a snapshot root dir and snapshot num > 0, the
  // variable is create_txid of corresponding latest snapshot.
  // else use default value 0
  int64_t latest_snapshot_create_txid_ = 0;
  INodeID snapshot_root_id_ = kInvalidINodeId;
  std::vector<SnapshotReference> snapshot_references_;
  std::unique_ptr<INode> snapshot_inode_;

  // Some attrs are updated during path resolve
  INode attr_holder_;
  // The `inode_` may be modified by methods like `FinalizeAttrs`, but we need
  // an unmodified inode for verification at the KV level.
  std::unique_ptr<INode> old_inode_;
};

// TODO(yangjinfeng.02)
// What about acl and xattr feature?
inline void MakeINode(uint64_t id, uint64_t pid, const std::string &name,
                      const PermissionStatus &permission,
                      INode::Type type, INode *inode) {
  CHECK_NOTNULL(inode);
  inode->set_id(id);
  inode->set_parent_id(pid);
  inode->set_name(name);
  inode->mutable_permission()->set_username(permission.username());
  inode->mutable_permission()->set_groupname(permission.groupname());
  inode->mutable_permission()->set_permission(permission.permission());
  int64_t time_now_ms =
      std::chrono::duration_cast<std::chrono::milliseconds>
          (std::chrono::system_clock::now().time_since_epoch()).count();
  inode->set_mtime(time_now_ms);
  inode->set_atime(time_now_ms);
  // access_pattern
  inode->set_type(type);
  inode->set_storage_policy_id(kBlockStoragePolicyIdUnspecified);
}

inline int32_t DetermineReplication(int32_t orig, int32_t geograph,
                                    const std::string& dns_str) {
  int32_t new_rep = orig;
  if (geograph == kDistributePolicy) {
    if (orig <= 2) {
      new_rep = 2;
    } else if (orig <= 4 && FLAGS_expand_replica) {
      new_rep = 4;
    } else if (orig >= 256) {
      new_rep = 1;
    }
    auto dns = cnetpp::base::StringUtils::SplitByChars(dns_str, ",");
    if (new_rep < dns.size()) {
      new_rep = dns.size();
    }
  }
  return new_rep;
}

inline void MakeINodeFile(uint64_t id, uint64_t pid, const std::string &name,
                          const PermissionStatus &permission, INode *file) {
  CHECK_NOTNULL(file);
  MakeINode(id, pid, name, permission, INode::kFile, file);

  file->set_preferred_blk_size(FLAGS_dfs_block_size);
  file->set_replication(FLAGS_dfs_replication);
}

inline void MakeINodeFile(uint64_t id,
                          uint64_t pid,
                          const std::string& name,
                          const PermissionStatus& permission,
                          uint32_t replication,
                          uint64_t preferred_block_size,
                          uint32_t storage_policy_id,
                          INode* file) {
  CHECK_NOTNULL(file);
  MakeINode(id, pid, name, permission, INode::kFile, file);

  file->set_preferred_blk_size(preferred_block_size);
  file->set_storage_policy_id(storage_policy_id);
  file->set_replication(replication);
}

inline void MakeINodeSymLink(uint64_t id, uint64_t pid, const std::string &name,
                             const PermissionStatus &permission,
                             const std::string& target,
                             INode *sym_link) {
  CHECK_NOTNULL(sym_link);
  MakeINode(id, pid, name, permission, INode::kSymLink, sym_link);
  sym_link->set_symlink(target);
}

inline void AddBlockToInode(INode* inode, const Block block) {
  auto b = inode->add_blocks();
  b->set_blockid(block.id);
  b->set_genstamp(block.gs);
  b->set_numbytes(block.num_bytes);
}

inline void CheckAccINode(const INode& inode) {
  CHECK(inode.has_ufs_file_info() || inode.has_ufs_dir_info());
}

inline void CheckHdfsINode(const INode& inode) {
  CHECK(!inode.has_ufs_file_info() && !inode.has_ufs_dir_info());
}

inline void UpdateINodeAttrs(INode& inode,
                             const INode& other,
                             bool force_inherit = false) {
  // PIN START
  if ((!inode.has_pin_status()) ||
      (inode.pin_status().ttl() > 0 &&
       inode.pin_status().ttl() < TimeUtilV2::GetNowEpochMs())) {
    inode.mutable_pin_status()->set_pinned(false);
    inode.mutable_pin_status()->set_ttl(-1);
    inode.mutable_pin_status()->set_recursive(false);
    inode.mutable_pin_status()->set_txid(0);
  }
  if (other.has_pin_status()) {
    PinStatus other_pin = other.pin_status();
    if (((other_pin.recursive() || force_inherit)) &&
        (!inode.has_pin_status() ||
         other_pin.txid() > inode.pin_status().txid())) {
      inode.mutable_pin_status()->CopyFrom(other_pin);
      inode.mutable_pin_status()->clear_job_id();
    }
  }
  // PIN END
}

}  // namespace dancenn

#endif  // NAMESPACE_INODE_H_
