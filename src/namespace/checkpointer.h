// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef NAMESPACE_CHECKPOINTER_H_
#define NAMESPACE_CHECKPOINTER_H_

#include <glog/logging.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <cnetpp/http/http_client.h>

#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <string>

#include "namespace/namespace.h"

namespace dancenn {

class Checkpointer {
 public:
  Checkpointer(NameSpace* ns);

  ~Checkpointer() {
    Stop();
  }

  void Start();
  void Stop();
  void Run();

 private:
  volatile bool should_run_;
  std::mutex cv_mutex_;
  std::condition_variable cv_;
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  NameSpace* ns_{nullptr};
};

}  // namespace dancenn

#endif // NAMESPACE_CHECKPOINTER_H_
