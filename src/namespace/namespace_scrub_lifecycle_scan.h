#ifndef _NAMESPACE_SCRUB_LIFECYCLE_SCAN_
#define _NAMESPACE_SCRUB_LIFECYCLE_SCAN_

#include "base/status.h"
#include "lifecycle.pb.h"
#include "namespace/namespace_scrub.h"

namespace dancenn {

using cloudfs::LifecyclePolicyProto;
using cloudfs::StorageClassProto;
using cloudfs::StorageClassStatProto;

struct StorageClassCmd {
  std::set<uint64_t> block_ids[cloudfs::StorageClassProto_ARRAYSIZE];
};

// dn -> HOT block_id * N, COLD block_id * N, ...
typedef std::map<std::string, StorageClassCmd> StorageClassCmds;

struct LifecycleScrubStat {
  // statistics of scrub procedure
  std::atomic<int64_t> num_file{0};
  std::atomic<int64_t> num_dir{0};
  std::atomic<int64_t> num_stat_persisted{0};
  std::atomic<int64_t> num_inode_expired{0};
  std::atomic<int64_t> num_inode_transited{0};

  // statistics of lifecycle feature
  //  1. expected
  uint64_t num_logical_block_expected[cloudfs::StorageClassProto_ARRAYSIZE]{};
  uint64_t num_logical_byte_expected[cloudfs::StorageClassProto_ARRAYSIZE]{};
  //  2. effective
  uint64_t num_logical_block[cloudfs::StorageClassProto_ARRAYSIZE]{};
  uint64_t num_logical_byte[cloudfs::StorageClassProto_ARRAYSIZE]{};
  uint64_t num_physical_replica[cloudfs::StorageClassProto_ARRAYSIZE]{};
  uint64_t num_physical_byte[cloudfs::StorageClassProto_ARRAYSIZE]{};

  uint64_t timestamp_sec{0};

  LifecycleScrubStat& operator=(const LifecycleScrubStat& other);
  LifecycleScrubStat& operator+=(const LifecycleScrubStat& other);
  std::string ToString() const;

  void Clear();
  void SerializeToProto(StorageClassStatProto* proto);
};

class LifecycleScanScrubOp : public ScrubOp {
 public:
  LifecycleScanScrubOp();
  ~LifecycleScanScrubOp() {
  }

  void PreScrub(ScrubAction action) override {
  }
  void PostScrub(ScrubAction action) override {
  }

  void ProcessFile(const INode& file) override;
  void ProcessDir(const INode& dir) override;
  Status HandleDirResult(ScrubAction action) override;
  void AddSubDirResult(const ScrubOpPtr subdir_op) override;

  int64_t EstimateRemainingSeconds(int64_t finished_inodes,
                                   int64_t elapsed_ms) override;
  ScrubOpPtr NewOp() override;
  std::string ToString() override;

  bool HaveCommands();
  const StorageClassCmds& GetCommands();
  void ClearCommands();
  LifecycleScrubStat& scrub_stat() {
    return scrub_stat_;
  }

 private:
  void ApplyLifecyclePolicy(const INode& inode,
                            INodeID effective_inode_id,
                            const LifecyclePolicyProto& policy,
                            bool* expired);
  void ApplyLifecyclePolicy(
    const INode& inode,
    const LifecyclePolicyProto& policy,
    const INodeID effective_inode_id,
    bool* expired);
  void GenerateStat(const INode& file, StorageClassProto cur_cls);
  void GenerateCmd(const INodeID fileid, StorageClassProto cur_cls);
  Status CheckDirResult();
  Status PersistDirResult();
  void HandleTransitionDefault(
      BlockID blkid,
      const std::vector<std::string>& dnuuids,
      const std::vector<StorageClassReportProto>& reports,
      StorageClassProto target_class);
  // XXX(xuex) should not be used in future
  // https://meego.feishu.cn/cfs/issue/detail/13366674
  void HandleTransitionWithOtherCold(
      BlockID blkid,
      const std::vector<std::string>& dnuuids,
      const std::vector<StorageClassReportProto>& reports,
      StorageClassProto target_class);

  int64_t GetExpiredTime(const INode& inode,
                         const INodeID effective_inode_id,
                         const LifecyclePolicyProto& policy);

 private:
  StorageClassCmds dn_cmds_mapping_;
  int distance_to_policy_{-1};
  LifecycleScrubStat scrub_stat_;
  uint32_t depred_xattr_removed_{0};
};

}  // namespace dancenn

#endif
