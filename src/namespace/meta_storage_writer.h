//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef NAMESPACE_META_STORAGE_WRITER_H_
#define NAMESPACE_META_STORAGE_WRITER_H_

#include <memory>
#include <mutex>
#include <condition_variable>
#include <vector>

#include <cnetpp/concurrency/task.h>
#include "base/metrics.h"
#include "base/ring_buffer.h"
#include "namespace/unique_txid_ring_window.h"

namespace dancenn {

class MetaStorage;

namespace meta_storage {

class WriteTask;
class Writer : public cnetpp::concurrency::Task {
 public:
  Writer(MetaStorage* storage);
  virtual ~Writer() = default;

  void LastApplyTxid(int64_t txid);
  void LastINodeId(uint64_t inode_id);
  void TxFinish(int64_t start_txid, int n);
  virtual void Push(WriteTask* task);
  virtual void PushBGTask(WriteTask* task);
  int NumPending(bool include_bg = false) const {
    return include_bg ? pending_.load() + bg_task_queue_.Length()
                      : pending_.load();
  }
  void WaitNoPending(bool include_bg = false);
  void StartActive();
  void StartStandby();
  void NotifyForActive(int64_t txid);

  void Stop() override {
    cnetpp::concurrency::Task::Stop();
    {
      std::lock_guard<std::mutex> guard(mutex_);
      cond_var_.notify_all();
    }
    {
      std::lock_guard<std::mutex> guard(empty_notified_mutex_);
      empty_notified_cond_var_.notify_all();
    }
  }

  bool operator()(void* arg = nullptr) override;

  void DropAllWriteTaskForActiveUntilFinish(
      const std::function<bool()>& finish_checker);

 private:
  void NotifyForStandby();

  inline bool EmptyForActive() {
    return notified_txid_end_ <= notified_txid_start_
        && bg_task_queue_.Empty();
  }

  inline bool EmptyForStandby() {
    return empty_notified_num_ == 0 && bg_task_queue_.Empty();
  }

  // status control for active
  volatile int64_t notified_txid_start_;
  char padding[64 - sizeof(notified_txid_start_)];
  volatile int64_t notified_txid_end_;
  std::unique_ptr<UniqueTxidRingWindow> pending_commit_task_queue_;
  dancenn::RingBuffer<WriteTask*> bg_task_queue_;
  std::mutex mutex_;
  std::condition_variable cond_var_;
  MetaStorage* storage_{nullptr};
  std::atomic<int> pending_{0};

  // this mutex control the rocksdb writer and drop editlog transaction
  std::mutex pending_task_mutex_;

  // status control for standby
  volatile uint64_t empty_notified_num_;
  std::mutex empty_notified_mutex_;
  std::condition_variable empty_notified_cond_var_;

  std::atomic<bool> is_active_{ false };

  MetricID wtask_push_fg_task_num_;
  MetricID wtask_push_fg_callback_num_;
  MetricID wtask_pop_fg_task_num_;
  MetricID wtask_push_bg_task_num_;
  MetricID wtask_push_bg_callback_num_;
  MetricID wtask_pop_bg_task_num_;

  MetricID writer_thread_fetch_fg_time_;
  MetricID writer_thread_fetch_bg_time_;
  MetricID writer_thread_write_time_;
  MetricID writer_thread_fast_time_;
  MetricID writer_thread_slow_time_;

  MetricID wtask_wait_time_;
  MetricID wtask_exec_fast_callback_time_;
  MetricID wtask_exec_slow_callback_time_;
  MetricID wtask_exec_empty_callback_task_num_;
  MetricID wtask_exec_fast_single_callback_task_num_;
  MetricID wtask_exec_slow_single_callback_task_num_;
  MetricID wtask_exec_slow_multiple_callback_task_num_;

  std::shared_ptr<Gauge> pending_task_include_bg_;
  std::shared_ptr<Gauge> pending_task_exclude_bg_;
};

} // namespace meta_storage
} // namespace dancenn

#endif // NAMESPACE_META_STORAGE_WRITER_H_
