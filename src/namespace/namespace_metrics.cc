// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "namespace/namespace_metrics.h"

#include <chrono>

#include "edit/op/all.h"
#include "namespace/namespace.h"

DECLARE_bool(run_ut);

namespace dancenn {


NameSpaceMetrics::NameSpaceMetrics() {
  CHECK(FLAGS_run_ut);
}

void NameSpaceMetrics::RecordLiveTime(const INode& inode) {
  if (inode.type() != INode::kFile) {
    return;
  }

  if (inode.has_uc()) {
    return;
  }

  uint64_t now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::system_clock::now().time_since_epoch())
                        .count();

  MFH(file_live_time_)->Update(now_ms - inode.mtime());
}

NameSpaceMetrics::NameSpaceMetrics(NameSpace* ns) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameSpace");

  auto current_ts_in_ms =
      std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now().time_since_epoch())
          .count();
  startup_ts_in_ms_ =
      metrics->RegisterGauge("StartUpTsInMs", [current_ts_in_ms]() -> double {
        return current_ts_in_ms;
      });

  add_block_replication_0_cnt_ =
      metrics->RegisterCounter("AddBlockReplication#replication=0");
  add_block_replication_1_cnt_ =
      metrics->RegisterCounter("AddBlockReplication#replication=1");
  add_block_replication_2_cnt_ =
      metrics->RegisterCounter("AddBlockReplication#replication=2");
  add_block_replication_3_cnt_ =
      metrics->RegisterCounter("AddBlockReplication#replication=3");
  add_block_replication_more_cnt_ =
      metrics->RegisterCounter("AddBlockReplication#replication=more");

  standby_read_stale_read_ = metrics->RegisterCounter("StandbyRead.StaleRead");
  standby_read_one_click_ = metrics->RegisterCounter("StandbyRead.OneClick");
  standby_read_need_wait_ = metrics->RegisterCounter("StandbyRead.NeedWait");
  standby_read_txid_gap_ = metrics->RegisterHistogram("StandbyRead.TxidGap");

  standby_read_catchup_cnt_ = metrics->RegisterCounter("StandbyRead.Catchup");
  standby_read_catchup_wait_time_ =
      metrics->RegisterHistogram("StandbyRead.Catchup.WaitTime");
  standby_read_catchup_applied_txid_ =
      metrics->RegisterHistogram("StandbyRead.Catchup.AppliedTxid");
  standby_read_catchup_applied_rate_ =
      metrics->RegisterHistogram("StandbyRead.Catchup.AppliedRate");

  standby_read_failed_cnt_ = metrics->RegisterCounter("StandbyRead.Failed");
  standby_read_failed_wait_time_ =
      metrics->RegisterHistogram("StandbyRead.Failed.WaitTime");
  standby_read_failed_applied_txid_ =
      metrics->RegisterHistogram("StandbyRead.Failed.AppliedTxid");
  standby_read_failed_applied_rate_ =
      metrics->RegisterHistogram("StandbyRead.Failed.AppliedRate");
  standby_read_failed_remain_txid_gap_ =
      metrics->RegisterHistogram("StandbyRead.Failed.RemainTxidGap");
  standby_read_failed_remain_progress_ =
      metrics->RegisterHistogram("StandbyRead.Failed.RemainProgress");

  standby_retry_cache_add_entry_count_ =
      metrics->RegisterCounter("StandbyRetryCache.AddEntry");

  refuse_read_blacklist_dc_cnt_ =
      metrics->RegisterCounter("RefuseRead#type=blacklist_dc");
  refuse_read_cross_dc_cnt_ =
      metrics->RegisterCounter("RefuseRead#type=cross_dc");

  recycle_bin_move_to_cnt_ =
      metrics->RegisterCounter("RecycleBin.MoveTo#type=total");
  recycle_bin_move_success_cnt_ =
      metrics->RegisterCounter("RecycleBin.MoveTo#type=success");
  recycle_bin_move_failed_cnt_ =
      metrics->RegisterCounter("RecycleBin.MoveTo#type=failed");
  recycle_bin_not_dir_ = metrics->RegisterCounter("RecycleBin.NotDir");
  recycle_bin_create_dir_ = metrics->RegisterCounter("RecycleBin.CreateDir");
  recycle_bin_cleanup_ = metrics->RegisterCounter("RecycleBin.Cleanup");

  recycle_bin_add_lock_time_ =
      metrics->RegisterHistogram("RecycleBin.Rpc#step=AddLock");
  recycle_bin_check_dir_time_ =
      metrics->RegisterHistogram("RecycleBin.Rpc#step=CheckDir");
  recycle_bin_create_dir_time_ =
      metrics->RegisterHistogram("RecycleBin.Rpc#step=CreateDir");
  recycle_bin_setxattr_time_ =
      metrics->RegisterHistogram("RecycleBin.Rpc#step=SetXAttr");
  recycle_bin_move_to_time_ =
      metrics->RegisterHistogram("RecycleBin.Rpc#step=MoveTo");
  recycle_bin_retention_sec_ =
      metrics->RegisterHistogram("RecycleBin.RetentionSecond");
  recycle_bin_over_retention_sec_ =
      metrics->RegisterHistogram("RecycleBin.OverRetentionSecond");

  mount_point_path_ancestor_mismatched_cnt_ =
      metrics->RegisterCounter("MountPoint#type=PathAncestorMismatch");
  mount_point_perm_check_failed_cnt_ =
      metrics->RegisterCounter("MountPoint#type=PermCheckFailed");

  get_listing_ha_check_time_ =
      metrics->RegisterHistogram("GetListingTime#step=CheckHA");
  get_listing_lock_acquire_time_ =
      metrics->RegisterHistogram("GetListingTime#step=LockAcquire");
  get_listing_get_last_inode_time_ =
      metrics->RegisterHistogram("GetListingTime#step=GetLastINode");
  get_listing_get_sub_inodes_time_ =
      metrics->RegisterHistogram("GetListingTime#step=GetSubINodes");
  get_listing_fill_in_response_time_ =
      metrics->RegisterHistogram("GetListingTime#step=FillInResponse");

  construct_file_status_ = metrics->RegisterHistogram("ConstructFileStatus");

  get_block_locations_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=CheckHA");
  get_block_locations_lock_acquire_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=LockAcquire");
  get_block_locations_get_last_inode_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=GetLastINode");
  get_block_locations_get_policy_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=GetPolicy");
  get_block_infer_read_mode_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=InferReadMode");
  get_block_locations_create_located_blocks_time_ =
      metrics->RegisterHistogram("GetBlockLocations#step=CreateLocatedBlocks");
  create_located_blocks_get_detail_blocks = metrics->RegisterHistogram(
      "CreateLocatedBlocks#step=GetDetailedBlocksInternal");
  create_located_blocks_get_blocks_in_range_ =
      metrics->RegisterHistogram("CreateLocatedBlocks#step=GetBlockInRange");
  create_located_blocks_fillin_time_ =
      metrics->RegisterHistogram("CreateLocatedBlocks#step=FillInPb");
  create_located_blocks_compute_file_size_ =
      metrics->RegisterHistogram("CreateLocatedBlocks#step=ComputeFileSize");
  create_located_blocks_sort_blocks_ =
      metrics->RegisterHistogram("CreateLocatedBlocks#step=SortBlocks");
  compute_file_size_ = metrics->RegisterHistogram("ComputeFileSize");
  num_blocks_accessed_for_locs_ = metrics->RegisterHistogram(
      "AccessBlockNumForLocations");

  get_hyper_block_locations_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("GetHyperlockLocations#step=CheckHA");
  get_hyper_block_locations_lock_acquire_time_ =
      metrics->RegisterHistogram("GetHyperlockLocations#step=LockAcquire");
  get_hyper_block_locations_get_last_inode_time_ =
      metrics->RegisterHistogram("GetHyperlockLocations#step=GetLastINode");
  scan_hyper_block_time_ =
      metrics->RegisterHistogram("GetHyperlockLocations#step=Scan");
  create_hyper_block_located_time_ = metrics->RegisterHistogram(
      "GetHyperlockLocations#step=CreateLocatedBlocks");

  create_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=CheckHAAndSafeMode");
  create_lock_acquire_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=LockAcquire");
  create_get_parent_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=GetParent");
  create_get_inode_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=GetINode");
  create_remove_or_recover_lease_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=RemoveOrRecoverLease");
  create_add_lease_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=AddLease");
  create_storage_policy_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=StoragePolicy");
  create_replica_policy_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=ReplicaPolicy");
  create_make_inode_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=MakeINode");
  create_add_block_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=AddBlock");
  create_log_edit_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=LogEdit");
  create_construct_file_status_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=ConstructFileStatus");

  create_callback_remove_blocks_in_bm_time_ = metrics->RegisterHistogram(
      "CreateFileCallbackTime#step=RemoveBlocksInBM");
  create_callback_add_remove_lease_time_ =
      metrics->RegisterHistogram("CreateFileCallbackTime#step=AddRemoveLease");
  create_callback_commit_bip_time_ =
      metrics->RegisterHistogram("CreateFileCallbackTime#step=CommitBip");
  create_with_add_block_time_ =
      metrics->RegisterHistogram("CreateFileTime#step=WithAddBlock");
  create_callback_construct_file_status_time_ = metrics->RegisterHistogram(
      "CreateFileCallbackTime#step=ConstructFileStatus");

  add_block_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("AddBlock#step=CheckHAAndSafeMode");

  release_lease_time_need_lock_ =
      metrics->RegisterHistogram("ReleaseLease#need_lock=yes");
  release_lease_time_need_not_lock_ =
      metrics->RegisterHistogram("ReleaseLease#need_lock=no");
  release_lease_check_lease_time_ =
      metrics->RegisterHistogram("ReleaseLease#step=CheckLease");
  release_lease_get_blocks_time_ =
      metrics->RegisterHistogram("ReleaseLease#step=GetFileBlocks");
  release_lease_bm_need_release_time_ =
      metrics->RegisterHistogram("ReleaseLease#step=BlockManagerNeedRelease");
  release_lease_reassign_lease_time_ =
      metrics->RegisterHistogram("ReleaseLease#step=ReassignLease");
  release_lease_get_gs_time_ =
      metrics->RegisterHistogram("ReleaseLease#step=GetGs");
  release_lease_bm_init_recover_ =
      metrics->RegisterHistogram("ReleaseLease#step=BlockManagerInitRecover");

  reassign_lease_lm_reassign_lease_time_ = metrics->RegisterHistogram(
      "ReassignLease#step=LeaseManagerReassignLease");
  reassign_lease_write_editlog_time_ =
      metrics->RegisterHistogram("ReassignLease#step=WriteEditLog");
  reassign_lease_write_meta_storage_time_ =
      metrics->RegisterHistogram("ReassignLease#step=WriteMetaStorage");
  reassign_lease_wait_editlog_and_ms_time_ = metrics->RegisterHistogram(
      "ReassignLease#step=WaitEditLogAndMetaStorage");

  add_block_lock_acquire_time_ =
      metrics->RegisterHistogram("AddBlock#step=LockAcquire");
  add_block_get_last_inode_time_ =
      metrics->RegisterHistogram("AddBlock#step=GetLastINode");
  add_block_check_lease_time_ =
      metrics->RegisterHistogram("AddBlock#step=CheckLease");
  add_block_analyze_file_blocks_time_ =
      metrics->RegisterHistogram("AddBlock#step=AnalyzeFileBlocks");
  acc_block_acc_check_time_ =
      metrics->RegisterHistogram("AddBlock#step=AccCheck");
  add_block_ccp_last_block_time_ =
      metrics->RegisterHistogram("AddBlock#step=CommitOrCompleteOrPersistLastBlock");
  add_block_get_replica_policy_time_ =
      metrics->RegisterHistogram("AddBlock#step=GetReplicaPolicy");
  add_block_infer_write_mode_time_ =
      metrics->RegisterHistogram("AddBlock#step=InferWriteMode");
  add_block_choose_target_4_new_time_ =
      metrics->RegisterHistogram("AddBlock#step=ChooseTarget4New");
  add_block_gen_block_id_and_genstamp_time_ =
      metrics->RegisterHistogram("AddBlock#step=GenBlockIDAndGenStamp");
  add_block_add_block_to_bm_time_ =
      metrics->RegisterHistogram("AddBlock#step=AddBlockToBM");
  add_block_fill_in_located_block_time_ =
      metrics->RegisterHistogram("AddBlock#step=FillInLocatedBlock");
  add_block_log_edit_time_ =
      metrics->RegisterHistogram("AddBlock#step=LogEdit");
  add_block_write_meta_storage_time_ =
      metrics->RegisterHistogram("AddBlock#step=WriteMetaStorage");

  get_content_summary_lock_acquire_time_ =
      metrics->RegisterHistogram("GetContentSummary#step=LockAcquire");
  get_content_summary_compute_time_ =
      metrics->RegisterHistogram("GetContentSummary#step=Compute");
  get_content_summary_get_children_time_ =
      metrics->RegisterHistogram("GetContentSummary#step=GetChildren");

  complete_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("Complete#step=CheckHAAndSafeMode");
  complete_get_last_inode_time_ =
      metrics->RegisterHistogram("Complete#step=GetLastINode");
  complete_check_lease_time_ =
      metrics->RegisterHistogram("Complete#step=CheckLease");
  complete_check_file_progress_v1_time_ =
      metrics->RegisterHistogram("Complete#step=CheckFileProgressV1");
  complete_commit_or_complete_last_block_time_ =
      metrics->RegisterHistogram("Complete#step=CommitOrCompleteLastBlock");
  complete_check_file_progress_v2_time_ =
      metrics->RegisterHistogram("Complete#step=CheckFileProgressV2");
  complete_finalize_file_time_ =
      metrics->RegisterHistogram("Complete#step=FinalizeFile");
  complete_callback_time_ =
      metrics->RegisterHistogram("Complete#step=Callback");

  concat_input_src_count_ = metrics->RegisterHistogram("ConcatSrcsSize");
  concat_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("Concat#step=CheckHaAndSafeMode");
  concat_add_lock_time_ = metrics->RegisterHistogram("Concat#step=AddLock");
  concat_get_target_inode_time_ =
      metrics->RegisterHistogram("Concat#step=GetTargetINode");
  concat_get_src_inodes_time_ =
      metrics->RegisterHistogram("Concat#step=GetSrcINodes");
  concat_check_upload_policy_time_ =
      metrics->RegisterHistogram("Concat#step=CheckUploadPolicy");
  concat_check_file_state_time_ =
      metrics->RegisterHistogram("Concat#step=CheckFileState");
  concat_check_ufs_state_time_ =
      metrics->RegisterHistogram("Concat#step=CheckUfsState");
  concat_merge_blocks_time_ =
      metrics->RegisterHistogram("Concat#step=MergeBlocks");
  concat_edit_log_time_ = metrics->RegisterHistogram("Concat#step=EditLog");
  concat_write_db_time_ = metrics->RegisterHistogram("Concat#step=WriteDB");
  concat_callback_time_ = metrics->RegisterHistogram("Concat#step=Callback");

  get_listing_inode_count_ =
      metrics->RegisterHistogram("GetListing.inode.count");

  batch_create_precheck_time_ =
      metrics->RegisterHistogram("BatchCreate#step=PreCheck");
  batch_create_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("BatchCreate#step=CheckHaAndSafeMode");
  batch_create_lock_acquire_time_ =
      metrics->RegisterHistogram("BatchCreate#step=DirLock");
  batch_create_get_parent_time_ =
      metrics->RegisterHistogram("BatchCreate#step=GetParent");
  batch_create_storage_policy_time_ =
      metrics->RegisterHistogram("BatchCreate#step=GetStorageType");
  batch_create_make_all_inode_time_ =
      metrics->RegisterHistogram("BatchCreate#step=MakeAllINode");
  batch_create_log_edit_time_ =
      metrics->RegisterHistogram("BatchCreate#step=EditLog");
  batch_create_write_db_time_ =
      metrics->RegisterHistogram("BatchCreate#step=WriteDB");
  batch_create_construct_file_status_time_ =
      metrics->RegisterHistogram("BatchCreate#step=ConstructFileStatus");
  batch_create_callback_time_ =
      metrics->RegisterHistogram("BatchCreate#step=Callback");

  batch_create_per_delete_inode_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=DeleteINode");
  batch_create_per_add_lease_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=AddLease");
  batch_create_per_replica_policy_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=ReplicaPolicy");
  batch_create_per_add_block_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=AddBlock");
  batch_create_per_choose_target_4_new_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=ChooseTarget4New");
  batch_create_per_make_inode_time_ =
      metrics->RegisterHistogram("BatchCreatePerINode#step=MakeINode");

  batch_complete_precheck_ =
      metrics->RegisterHistogram("BatchComplete#step=PreCheck");
  batch_complete_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("BatchComplete#step=CheckHaAndSafeMode");
  batch_complete_lock_acquire_time_ =
      metrics->RegisterHistogram("BatchComplete#step=DirLock");
  batch_complete_get_inodes_time_ =
      metrics->RegisterHistogram("BatchComplete#step=GetINodes");
  batch_complete_check_inode_id_time_ =
      metrics->RegisterHistogram("BatchComplete#step=CheckINodeID");
  batch_complete_check_lease_time_ =
      metrics->RegisterHistogram("BatchComplete#step=CheckLease");
  batch_complete_finalize_single_file_time_ =
      metrics->RegisterHistogram("BatchComplete#step=SingleFile");
  batch_complete_concat_file_time_ =
      metrics->RegisterHistogram("BatchComplete#step=ConcatFile");
  batch_complete_log_edit_time_ =
      metrics->RegisterHistogram("BatchComplete#step=EditLog");
  batch_complete_write_db_time_ =
      metrics->RegisterHistogram("BatchComplete#step=WriteDB");
  batch_complete_callback_time_ =
      metrics->RegisterHistogram("BatchComplete#step=Callback");

  batch_delete_precheck_ =
      metrics->RegisterHistogram("BatchDelete#step=PreCheck");
  batch_delete_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("BatchDelete#step=CheckHaAndSafeMode");
  batch_delete_lock_acquire_time_ =
      metrics->RegisterHistogram("BatchDelete#step=DirLock");
  batch_delete_get_inodes_time_ =
      metrics->RegisterHistogram("BatchDelete#step=GetINodes");
  batch_delete_log_edit_time_ =
      metrics->RegisterHistogram("BatchDelete#step=EditLog");
  batch_delete_write_db_time_ =
      metrics->RegisterHistogram("BatchDelete#step=WriteDB");
  batch_delete_callback_time_ =
      metrics->RegisterHistogram("BatchDelete#step=Callback");

  batch_get_precheck_ = metrics->RegisterHistogram("BatchGet#step=Precheck");
  batch_get_ha_and_safemode_check_time_ =
      metrics->RegisterHistogram("BatchGet#step=CheckHAAndSafeMode");
  batch_get_lock_acquire_time_ =
      metrics->RegisterHistogram("BatchGet#step=DirLock");
  batch_get_get_inodes_time_ =
      metrics->RegisterHistogram("BatchGet#step=GetINode");
  batch_get_construct_files_time_ =
      metrics->RegisterHistogram("BatchGet#step=ConstructFile");

  delete_file_by_dn_get_block_info_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=GetBlockInfo");
  delete_file_by_dn_ha_safemode_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=CheckHAAndSafeMode");
  delete_file_by_dn_get_inode_path_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=GetINodeAndPath");
  delete_file_by_dn_edit_log_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=EditLog");
  delete_file_by_dn_write_db_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=WriteDB");
  delete_file_by_dn_callback_time_ =
      metrics->RegisterHistogram("EvictFileTime#step=Callback");

  since_last_catchup_txid_ = metrics->RegisterGauge(
      "SinceLastCatchupTxId",
      [ns]() -> double { return ns->SinceLastCatchupTxid(); });
  last_ckpt_txid_ = metrics->RegisterGauge(
      "LastCkptTxId", [ns]() -> double { return ns->last_ckpt_txid(); });
  last_inode_id_ = metrics->RegisterGauge(
      "LastINodeId", [ns]() -> double { return ns->last_inode_id(); });
  last_block_id_ = metrics->RegisterGauge("LastBlockId", [ns]() -> double {
    return ns->last_allocated_block_id();
  });
  genstampv2_ = metrics->RegisterGauge(
      "GenStampV2", [ns]() -> double { return ns->generation_stamp_v2(); });
  num_inodes_ = metrics->RegisterGauge(
      "NumINodes", [ns]() -> double { return ns->num_inodes(); });
  editlog_apply_mode_ = metrics->RegisterGauge(
      "EditApplyMode",
      [ns]() -> double { return static_cast<int>(ns->editlog_apply_mode()); });
  edit_assigner_switch_apply_mode_time_ =
      metrics->RegisterHistogram("EditAssignerSwitchApplyModeTime");

  edit_tailer_num_ops_ = metrics->RegisterCounter("EditTailerNumOps");
  edit_tailer_duration_ = metrics->RegisterHistogram("EditTailerTime#step=total");
  edit_tailer_deserialize_time_ =
      metrics->RegisterHistogram("EditTailerTime#step=deserialize_op");
  edit_tailer_submit_time_ =
      metrics->RegisterHistogram("EditTailerTime#step=submit_assign_task");

  // 'edit_apply_duration_': from tailer submitting AssignTask to callback done
  // 'edit_assigner_assign_time_': assigner submit async task to applyer
  // 'edit_applyer_exec_time_': applyer execute ApplyTask and submit WriteTask
  // 'edit_callback_exec_time_': callback execute time
  for (auto& i : kOpCodeToCtor) {
    auto op = i.second();
    edit_apply_num_ops_[op->op_name()] =
        metrics->RegisterCounter("EditApplyNumOps#type=" + op->op_name());
    edit_apply_duration_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyTime#type=" + op->op_name());
    edit_assigner_assign_time_[op->op_name()] =
        metrics->RegisterHistogram("EditAssignerAssignTime#type=" + op->op_name());
    edit_applyer_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyerExecTime#type=" + op->op_name());
    edit_callback_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditCallbackExecTime#type=" + op->op_name());
    delete op;
  }
  for (auto& i : kCfsOpCodeToCtor) {
    auto op = i.second();
    edit_apply_num_ops_[op->op_name()] =
        metrics->RegisterCounter("EditApplyNumOps#type=" + op->op_name());
    edit_apply_duration_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyTime#type=" + op->op_name());
    edit_assigner_assign_time_[op->op_name()] =
        metrics->RegisterHistogram("EditAssignerAssignTime#type=" + op->op_name());
    edit_applyer_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyerExecTime#type=" + op->op_name());
    edit_callback_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditCallbackExecTime#type=" + op->op_name());
    delete op;
  }
  {
    auto op = OpWaitNoPending::New();
    edit_apply_num_ops_[op->op_name()] =
        metrics->RegisterCounter("EditApplyNumOps#type=" + op->op_name());
    edit_apply_duration_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyTime#type=" + op->op_name());
    edit_assigner_assign_time_[op->op_name()] =
        metrics->RegisterHistogram("EditAssignerAssignTime#type=" + op->op_name());
    edit_applyer_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditApplyerExecTime#type=" + op->op_name());
    edit_callback_exec_time_[op->op_name()] =
        metrics->RegisterHistogram("EditCallbackExecTime#type=" + op->op_name());
  }
  edit_rename_overwrite_callback_exec_time_ =
      metrics->RegisterHistogram("EditCallbackExecTime#type=RenameOldV2 (overwrite-cb)");
  edit_openfile_overwrite_callback_exec_time_ =
      metrics->RegisterHistogram("EditCallbackExecTime#type=OpenFile (overwrite-cb)");

  op_add_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=AddLockAcquire");
  op_add_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AddGetLastInode");

  op_rename_old_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameOldLockAcquire");
  op_rename_old_time_ = metrics->RegisterHistogram("TailerTime#step=RenameOld");
  op_rename_old_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameOldDelCache");

  // Rename HyperFile
  op_rename_hyper_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameHyperLockAcquire");
  op_rename_hyper_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameHyper");
  op_rename_hyper_file_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameHyperDelCache");

  op_delete_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=DeleteLockAcquire");
  op_delete_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=DeleteGetLastInode");
  op_delete_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=DeleteDelCache");

  op_mkdir_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=MkdirLockAcquire");
  op_mkdir_get_parent_time_ =
      metrics->RegisterHistogram("TailerTime#step=MkdirGetParent");

  op_set_replication_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetReplicationLockAcquire");
  op_set_replication_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetReplicationGetLastInode");

  op_set_permissions_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetPermissionsLockAcquire");
  op_set_permissions_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetPermissionsGetLastInode");

  op_set_owner_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetOwnerLockAcquire");
  op_set_owner_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetOwnerGetLastInode");

  op_times_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=TimesLockAcquire");
  op_times_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=TimesGetLastInode");

  op_close_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=CloseLockAcquire");
  op_close_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=CloseGetLastInode");

  op_rename_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameLockAcquire");
  op_rename_time_ = metrics->RegisterHistogram("TailerTime#step=Rename");
  op_rename_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=RenameDelCache");

  op_symlink_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SymlinkLockAcquire");
  op_symlink_get_parent_time_ =
      metrics->RegisterHistogram("TailerTime#step=SymlinkGetParent");

  op_reassign_lease_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=ReassignLeaseLockAcquire");
  op_reassign_lease_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=ReassignLeaseGetLastInode");

  op_merge_block_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=MergeBlockLockAcquire");
  op_merge_block_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=MergeBlockGetLastINode");

  op_concat_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=ConcatLockAcquire");
  op_concat_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=ConcatGetLastInode");

  op_update_blocks_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=UpdateBlocksLockAcquire");
  op_update_blocks_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=UpdateBlocksGetLastInode");

  op_add_block_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=AddBlockLockAcquire");
  op_add_block_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AddBlockGetLastInode");

  op_set_xattr_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetXattrLockAcquire");
  op_set_xattr_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetXattrGetLastInode");

  op_remove_xattr_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=RemoveXattrLockAcquire");
  op_remove_xattr_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=RemoveXattrGetLastInode");

  op_set_storage_policy_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetStoragePolicyLockAcquire");
  op_set_storage_policy_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=SetStoragePolicyGetLastInode");

  op_set_dir_replica_policy_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=SetDirReplicaPolicyLockAcquire");
  op_set_dir_replica_policy_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=SetDirReplicaPolicyGetLastInode");

  op_set_replica_policy_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetReplicaPolicyLockAcquire");
  op_set_replica_policy_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=SetReplicaPolicyGetLastInode");

  op_set_lifecycle_policy_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetLifecyclePolicyLockAcquire");
  op_set_lifecycle_policy_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=SetLifecyclePolicyGetLastInode");

  op_unset_lifecycle_policy_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=UnsetLifecyclePolicyLockAcquire");
  op_unset_lifecycle_policy_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=UnsetLifecyclePolicyGetLastInode");

  op_pin_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=PinLockAcquire");
  op_pin_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=PinGetLastINode");

  op_reconcile_inode_attrs_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=ReconcileInodeAttrsLockAcquire");
  op_reconcile_inode_attrs_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=ReconcileInodeAttrsGetLastINode");

  op_batch_create_file_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchCreateFileLockAcquire");
  op_batch_create_file_get_inodes_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchCreateFileGetINodes");
  op_batch_create_file_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchCreateFileDelCache");

  op_batch_complete_file_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=BatchCompleteFileLockAcquire");
  op_batch_complete_file_get_inodes_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchCompleteFileGetINodes");
  op_batch_complete_file_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchCompleteFileDelCache");

  op_batch_delete_file_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchDeleteFileLockAcquire");
  op_batch_delete_file_get_inodes_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchDeleteFileGetINodes");
  op_batch_delete_file_del_cache_time_ =
      metrics->RegisterHistogram("TailerTime#step=BatchDeleteFileDelCache");

  delete_file_by_dn_evict_cnt_ = metrics->RegisterCounter("EvictFile");

  // Acc
  acc_sync_ufs_compute_action_none_both_not_exist_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=none_both_not_exist");
  acc_sync_ufs_compute_action_none_file_not_upload_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=none_file_not_upload");
  acc_sync_ufs_compute_action_none_dir_not_exist_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=none_dir_not_exist");
  acc_sync_ufs_compute_action_none_dir_file_only_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=none_dir_file_only");
  acc_sync_ufs_compute_action_create_exist_in_ufs_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=create_exist_in_ufs");
  acc_sync_ufs_compute_action_delete_file_not_exist_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=delete_file_not_exist");
  acc_sync_ufs_compute_action_delete_dir_not_exist_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=delete_dir_not_exist");
  acc_sync_ufs_compute_action_overwrite_dir_ufs_file_ =
      metrics->RegisterCounter(
          "Acc.Sync.ComputeAction#type=overwrite_dir_ufs_file");
  acc_sync_ufs_compute_action_overwrite_file_ufs_dir_ =
      metrics->RegisterCounter(
          "Acc.Sync.ComputeAction#type=overwrite_file_ufs_dir");
  acc_sync_ufs_compute_action_overwrite_file_etag_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=overwrite_file_etag");
  acc_sync_ufs_compute_action_overwrite_file_mtime_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=overwrite_file_mtime");
  acc_sync_ufs_compute_action_update_dir_remote_dir_ = metrics->RegisterCounter(
      "Acc.Sync.ComputeAction#type=update_dir_remote_dir");
  acc_sync_ufs_compute_action_update_dir_remote_both_ =
      metrics->RegisterCounter(
          "Acc.Sync.ComputeAction#type=update_dir_remote_both");
  acc_sync_ufs_compute_action_update_file_etag_ =
      metrics->RegisterCounter("Acc.Sync.ComputeAction#type=update_file_etag");
  acc_sync_ufs_compute_action_update_file_mtime_ =
      metrics->RegisterCounter("Acc.Sync.ComputeAction#type=update_file_mtime");

  acc_sync_ufs_apply_action_none_ =
      metrics->RegisterCounter("Acc.Sync.ApplyAction#type=None");
  acc_sync_ufs_apply_action_create_ =
      metrics->RegisterCounter("Acc.Sync.ApplyAction#type=Create");
  acc_sync_ufs_apply_action_delete_ =
      metrics->RegisterCounter("Acc.Sync.ApplyAction#type=Delete");
  acc_sync_ufs_apply_action_overwrite_ =
      metrics->RegisterCounter("Acc.Sync.ApplyAction#type=Overwrite");
  acc_sync_ufs_apply_action_update_time_ =
      metrics->RegisterCounter("Acc.Sync.ApplyAction#type=UpdateTime");

  acc_prepare_sync_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=PrepareSyncUfsFile");
  acc_sync_ufs_file_get_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileGetUfsFile");
  acc_sync_ufs_file_compute_action_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileComputeAction");
  acc_sync_ufs_file_create_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileCreate");
  acc_sync_ufs_file_delete_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileDelete");
  acc_sync_ufs_file_overwrite_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileOverwrite");
  acc_sync_ufs_file_update_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileUpdate");
  acc_sync_ufs_file_listing_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileListing");
  acc_prepare_ufs_file_listing_time_ =
      metrics->RegisterHistogram("Acc#step=PrepareUfsFileListing");

  acc_sync_ufs_file_only_get_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileOnlyGetUfsFile");
  acc_sync_ufs_file_only_compute_action_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileOnlyComputeAction");
  acc_sync_ufs_file_only_apply_action_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsFileOnlyApplyAction");
  acc_trigger_parent_prefetch_ =
      metrics->RegisterCounter("Acc.Sync.TriggerParentPrefetch");
  acc_list_ufs_dir_mark_prefetch_failed_ =
      metrics->RegisterCounter("Acc.Sync.ListUfsDirPrefetchFailed");
  acc_list_ufs_dir_mark_prefetch_failed_time_ =
      metrics->RegisterHistogram("Acc#step=ListUfsDirPrefetchFailedTime");
  acc_list_ufs_dir_lock_and_check_inode_time_ =
      metrics->RegisterHistogram("Acc#step=ListUfsDirLockAndCheckInode");
  acc_do_sync_ufs_dir_time_ =
      metrics->RegisterHistogram("Acc#step=DoSyncUfsDirTime");
  acc_generate_sync_action_for_ufs_files_time_ =
      metrics->RegisterHistogram("Acc#step=GenerateSyncActionForUfsFiles");
  acc_process_ufs_dir_children_time_ =
      metrics->RegisterHistogram("Acc#step=ProcessUfsDirChildren");
  acc_finish_sync_ufs_dir_children_time_ =
      metrics->RegisterHistogram("Acc#step=FinishSyncUfsDirChildren");
  acc_apply_ufs_dir_sync_action_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDir");
  acc_apply_ufs_dir_sync_action_create_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDirCreate");
  acc_apply_ufs_dir_sync_action_create_one_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDirCreateOne");
  acc_apply_ufs_dir_sync_action_check_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDirCheck");
  acc_apply_ufs_dir_sync_action_update_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDirUpdate");
  acc_apply_ufs_dir_sync_action_update_one_time_ =
      metrics->RegisterHistogram("Acc#step=SyncUfsDirUpdateOne");
  acc_make_inode_and_blocks_from_ufs_time_ =
      metrics->RegisterHistogram("Acc#step=MakeInodeAndBlocksFromUfs");
  acc_sync_rename_ufs_time_ = metrics->RegisterHistogram("Acc#step=Rename");
  acc_prepare_rename_ufs_time_ =
      metrics->RegisterHistogram("Acc#step=RenamePrepare");
  acc_rename_ufs_check_time_ =
      metrics->RegisterHistogram("Acc#step=RenameUfsCheck");
  acc_rename_ufs_remove_dst_time_ =
      metrics->RegisterHistogram("Acc#step=RenameRemoveDst");
  acc_rename_ufs_copy_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=RenameCopyUfs");
  acc_rename_ufs_delete_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=RenameUfs");
  acc_rename_ufs_remote_time_ =
      metrics->RegisterHistogram("Acc#step=RenameRemote");
  acc_rename_ufs_local_time_ =
      metrics->RegisterHistogram("Acc#step=RenameLocal");
  acc_sync_delete_ufs_time_ = metrics->RegisterHistogram("Acc#step=Delete");
  acc_prepare_delete_from_ufs_time_ =
      metrics->RegisterHistogram("Acc#step=DeletePrepare");
  acc_delete_ufs_delete_ufs_file_time_ =
      metrics->RegisterHistogram("Acc#step=DeleteUfs");
  acc_delete_ufs_delete_local_file_time_ =
      metrics->RegisterHistogram("Acc#step=DeleteLocal");
  acc_mkdirs_time_ = metrics->RegisterHistogram("Acc#step=Mkdirs");
  acc_mkdirs_local_time_ = metrics->RegisterHistogram("Acc#step=MkdirsLocal");
  acc_mkdirs_ufs_time_ = metrics->RegisterHistogram("Acc#step=MkdirsUfs");
  acc_mkdirs_update_local_time_ =
      metrics->RegisterHistogram("Acc#step=MkdirsUpdateLocal");
  acc_create_time_ = metrics->RegisterHistogram("Acc#step=Create");
  acc_create_local_time_ = metrics->RegisterHistogram("Acc#step=CreateLocal");
  acc_create_ufs_time_ = metrics->RegisterHistogram("Acc#step=CreateUfs");

  op_acc_sync_batch_add_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncListingBatchAdd");
  op_acc_sync_batch_add_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncListingBatchAddLockAcquire");
  op_acc_sync_batch_add_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncListingBatchAddGetLastINode");

  op_acc_sync_batch_update_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncListingBatchUpdate");
  op_acc_sync_batch_update_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncListingBatchUpdateLockAcquire");
  op_acc_sync_batch_update_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncListingBatchUpdateGetLastINode");

  op_acc_sync_update_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncUpdateINode");
  op_acc_sync_update_inode_lock_acquire_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncUpdateINodeLockAcquire");
  op_acc_sync_update_inode_get_last_inode_time_ = metrics->RegisterHistogram(
      "TailerTime#step=AccSyncUpdateINodeGetLastINode");

  op_acc_sync_add_file_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncAddFile");
  op_acc_sync_add_file_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncAddFileLockAcquire");
  op_acc_sync_add_file_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccSyncAddFileGetLastINode");

  op_acc_persist_file_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccPersistFile");
  op_acc_persist_file_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccPersistFileLockAcquire");
  op_acc_persist_file_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccPersistFileGetLastINode");

  op_acc_update_block_info_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccUpdateBlockInfo");
  op_acc_update_block_info_lock_acquire_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccUpdateBlockInfoLockAcquire");
  op_acc_update_block_info_get_last_inode_time_ =
      metrics->RegisterHistogram("TailerTime#step=AccUpdateBlockInfoGetLastINode");

  fill_in_located_block_set_block_info_time_ =
      metrics->RegisterHistogram("FillInLocatedBlock#step=SetBlockInfo");
  fill_in_located_block_construct_block_token_time_ =
      metrics->RegisterHistogram("FillInLocatedBlock#step=ConstructBlockToken");
  fill_in_located_block_set_block_pufs_name_time_ =
      metrics->RegisterHistogram("FillInLocatedBlock#step=SetBlockPufsName");

  file_not_found_after_sync_cnt_ =
      metrics->RegisterCounter("Acc.Sync.RpcFileNotFound");
  sync_file_from_ufs_gap_time_ =
      metrics->RegisterHistogram("Acc.Sync.GapBetweenSyncedAndUploaded");
  sync_file_from_ufs_slow_cnt_ = metrics->RegisterCounter("Acc.Sync.SyncSlow");
  upload_file_to_ufs_gap_time_ =
      metrics->RegisterHistogram("Acc.Upload.UploadFileToUfsGapTime");
  upload_file_to_ufs_cnt_ =
      metrics->RegisterCounter("Acc.Upload.UploadFileToUfs");
  upload_file_to_ufs_slow_cnt_ =
      metrics->RegisterCounter("Acc.Upload.UploadFileToUfsSlow");
  upload_file_to_ufs_slow_unfinished_cnt_ =
      metrics->RegisterCounter("Acc.Upload.UploadFileToUfsSlowAndUnfinished");

  trigger_file_upload_cnt_ =
      metrics->RegisterCounter("Acc.Upload.TriggerFileUpload");
  trigger_block_upload_cnt_ =
      metrics->RegisterCounter("Acc.Upload.TriggerBlockUpload");

  file_live_time_ = metrics->RegisterHistogram("FileLiveTime");

  bg_deletion_remove_inode_num_ =
      metrics->RegisterCounter("BGDeletion.Removed#type=inode");
  bg_deletion_remove_block_num_ =
      metrics->RegisterCounter("BGDeletion.Removed#type=block");
}

}  // namespace dancenn
