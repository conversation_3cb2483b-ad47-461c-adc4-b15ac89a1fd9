#!/bin/sh

set -e
touch $1
out=`realpath $1`
cd `dirname $0`

version="0-1-0"

build_version=$(git rev-parse --short HEAD)
build_path="https://code.byted.org/inf/dancenn/commit/$build_version"

commit_msg=$(git log -n 1 --abbrev-commit --oneline | awk '{ $1=""; print $0}')

sed -e "s/@@version@@/${version}/g" version.h.in |\
sed -e "s/@@build_version@@/${build_version}/g" |\
sed -e "s#@@commit_msg@@#${commit_msg}#g" |\
sed -e "s#@@build_path@@#${build_path}#g" > $out
