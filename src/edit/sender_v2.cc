// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/11
// Description

#include <string>

#include "base/stop_watch.h"
#include "block_manager/block.h"
#include "edit/edit_log_cfs_op.h"
#include "edit/edit_types.h"
#include "edit/sender.h"
#include "edit/sender_base.h"
#include "proto/generated/dancenn/inode.pb.h"

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

#define ENCODE_RPCINFO(rpcinfo)                                                \
  if (rpcinfo.to_log_rpc_ids_) {                                               \
    proto.mutable_log_rpc_info()                                               \
        ->set_rpc_client_id(rpcinfo.rpc_client_id_);                           \
    proto.mutable_log_rpc_info()                                               \
        ->set_rpc_call_id(rpcinfo.rpc_call_id_);                               \
  }

#define ENCODE_SNAPLOG(snaplog)                                                \
  if (snaplog.IsInitialized()) {                                               \
    proto.mutable_##snaplog()->CopyFrom(snaplog);                              \
  }

int64_t EditLogSender::LogOpenFileV2(
    const std::string& path,
    const INode& inode,
    const INode& parent,
    bool overwrite,
    const INode* overwrite_inode,
    bool move_to_recycle_bin,
    const std::string* rb_path,
    const INode* rb_inode,
    const INode* rb_parent,
    const std::vector<BlockInfoProto>& add_block_bips,
    const std::vector<std::vector<std::string>>& bips_expected_locs,
    const std::vector<INodeID>& ancestors_id,
    const std::vector<INodeID>* rb_ancestors_id,
    const SnapshotLog& old_inode_snaplog,
    const SnapshotLog& parent_snaplog,
    const SnapshotLog& rb_parent_snaplog,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  FileToBeOpen proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.set_overwrite(overwrite);
  proto.mutable_parent()->CopyFrom(parent);
  if (overwrite_inode) {
    CHECK(overwrite);
    proto.mutable_overwrite_inode()->CopyFrom(*overwrite_inode);
    if (move_to_recycle_bin) {
      proto.set_move_to_recycle_bin(true);
      proto.set_rb_path(*rb_path);
      proto.mutable_rb_inode()->CopyFrom(*rb_inode);
      proto.mutable_rb_parent()->CopyFrom(*rb_parent);
      std::for_each(rb_ancestors_id->begin(),
                    rb_ancestors_id->end(),
                    [&proto] (INodeID id) { proto.add_rb_ancestors_id(id); });
    } else {
      proto.set_move_to_recycle_bin(false);
      CHECK(rb_path == nullptr);
      CHECK(rb_inode == nullptr);
      CHECK(rb_parent == nullptr);
      CHECK(rb_ancestors_id == nullptr);
    }
  } else {
    CHECK(!move_to_recycle_bin);
    proto.set_move_to_recycle_bin(false);
    CHECK(rb_path == nullptr);
    CHECK(rb_inode == nullptr);
    CHECK(rb_parent == nullptr);
    CHECK(rb_ancestors_id == nullptr);
  }
  for (const BlockInfoProto& add_block_bip : add_block_bips) {
    *proto.add_add_block_bips() = add_block_bip;
  }
  CHECK_EQ(add_block_bips.size(), bips_expected_locs.size());
  for (int i = 0; i < add_block_bips.size(); i++) {
    auto bip_with_locs = proto.add_add_block_bips_with_locs();
    bip_with_locs->mutable_bip()->CopyFrom(add_block_bips[i]);
    for (auto& uuid : bips_expected_locs[i]) {
      bip_with_locs->add_dns(uuid);
    }
  }
  ENCODE_SNAPLOG(old_inode_snaplog);
  ENCODE_SNAPLOG(parent_snaplog);
  ENCODE_SNAPLOG(rb_parent_snaplog);

  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });

  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpOpenFile op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAppend(const std::string path,
                                 const INode& inode,
                                 const INode& parent,
                                 const INode& old_inode,
                                 const std::vector<INodeID>& ancestors_id,
                                 const SnapshotLog& inode_snaplog,
                                 const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  FileToBeAppend proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_parent()->CopyFrom(parent);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpAppend op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAddBlockV2(const std::string& path,
                                     const INode& inode,
                                     const BlockInfoProto* penultimate_bip,
                                     const BlockInfoProto& last_bip,
                                     const INode& old_inode,
                                     const std::vector<INodeID>& ancestors_id,
                                     const std::vector<std::string>& dn_uuids,
                                     const SnapshotLog& inode_snaplog,
                                     const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  CHECK_GT(inode.blocks_size(), 0);
  BlockToBeAdd proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  if (penultimate_bip != nullptr) {
    *proto.mutable_penultimate_bip() = *penultimate_bip;
  }
  CHECK(last_bip.IsInitialized()) << last_bip.InitializationErrorString();
  proto.mutable_last_bip()->CopyFrom(last_bip);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  // EditLogSender::LogAddBlock ignores log_rpc_info.
  proto.set_physical_applyable(true);
  proto.mutable_last_bip_with_locs()->mutable_bip()->CopyFrom(last_bip);
  for (auto& dn : dn_uuids) {
    proto.mutable_last_bip_with_locs()->add_dns(dn);
  }
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpAddBlockV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAbandonBlock(const std::string& path,
                                       const INode& inode,
                                       BlockID abandoned_blk_id,
                                       const INode& old_inode,
                                       const std::vector<INodeID>& ancestors_id,
                                       const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  BlockToBeAbandon proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.set_abandoned_blk_id(abandoned_blk_id);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  proto.mutable_old_inode()->CopyFrom(old_inode);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpAbandonBlock op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogUpdatePipeline(const std::string& path,
                                         const INode& inode,
                                         const BlockInfoProto& last_bip_tbuc,
                                         const INode& old_inode,
                                         const std::vector<INodeID>& ancestors_id,
                                         const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  PipelineToBeUpdate proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_last_bip_tbuc()->CopyFrom(last_bip_tbuc);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  proto.mutable_old_inode()->CopyFrom(old_inode);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpUpdatePipeline op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogFsync(const std::string& path,
                                const INode& inode,
                                const BlockInfoProto* last_bip_tbuc,
                                const INode& old_inode,
                                const std::vector<INodeID>& ancestors_id,
                                const SnapshotLog& inode_snaplog,
                                const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  FileToBeSync proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  if (last_bip_tbuc != nullptr) {
    proto.mutable_last_bip_tbuc()->CopyFrom(*last_bip_tbuc);
  }
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpFsync op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogCommitBlockSynchronization(
    const std::string& path,
    bool delete_block,
    bool close_file,
    const INode& inode,
    const BlockInfoProto& last_bip,
    const INode& old_inode,
    std::vector<INodeID>& ancestors_id,
    const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  BlockToBeCommitSynchronization proto;
  proto.set_path(path);
  proto.set_delete_block(delete_block);
  proto.set_close_file(close_file);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_last_bip()->CopyFrom(last_bip);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(), ancestors_id.end(), [&proto](INodeID id) {
    proto.add_ancestors_id(id);
  });
  ENCODE_SNAPLOG(inode_snaplog);
  CHECK(proto.IsInitialized());
  OpCommitBlockSynchronization op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogUpdateBlocksV2(const std::string& path,
                                         const INode& inode,
                                         const INode& old_inode,
                                         const std::vector<INodeID>& ancestors_id,
                                         const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(inode.status() == INode::kFileUnderConstruction);
  BlocksToBeUpdate proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  proto.mutable_old_inode()->CopyFrom(old_inode);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpUpdateBlocksV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogCloseFileV2(const std::string& path,
                                      const INode& inode,
                                      const BlockInfoProto* last_bip,
                                      BlockID dropped_blk_id,
                                      const INode& old_inode,
                                      const std::vector<INodeID>& ancestors_id,
                                      const SnapshotLog& inode_snaplog) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  FileToBeClose proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  if (last_bip != nullptr) {
    proto.mutable_last_bip()->CopyFrom(*last_bip);
  }
  if (dropped_blk_id != kInvalidBlockID) {
    proto.set_abandoned_blk_id(dropped_blk_id);
  }
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpCloseFile op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogReassignLeaseV2(const std::string& path,
                                          const INode& inode,
                                          const INode& old_inode,
                                          const std::string& lease_holder,
                                          const std::string& new_holder,
                                          const SnapshotLog& inode_snaplog) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  LeaseToBeReassign proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.set_lease_holder(lease_holder);
  proto.set_new_holder(new_holder);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpReassignLeaseV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogConcatV2(
    const std::string& parent_path,
    const std::string& target,
    const std::vector<std::string>& srcs,
    const INode& target_inode,
    const INode& old_target_inode,
    const std::vector<INode>& src_inodes,
    const std::vector<BlockInfoProto>& target_bips,
    const INode& parent,
    uint64_t timestamp_in_ms,
    const LogRpcInfo& log_rpc_info) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  FileToBeConcat proto;
  proto.set_parent_path(parent_path);
  proto.set_target_path(target);
  for (const auto& src : srcs) {
    proto.add_src_paths(src);
  }
  proto.mutable_target_inode()->CopyFrom(target_inode);
  proto.mutable_old_target_inode()->CopyFrom(old_target_inode);
  for (const auto& src_inode : src_inodes) {
    proto.add_src_inodes()->CopyFrom(src_inode);
  }
  for (const auto& bip : target_bips) {
    proto.add_target_bips()->CopyFrom(bip);
  }
  proto.mutable_parent()->CopyFrom(parent);
  proto.set_timestamp_in_ms(timestamp_in_ms);
  if (log_rpc_info.to_log_rpc_ids_) {
    proto.mutable_log_rpc_info()->set_rpc_client_id(
        log_rpc_info.rpc_client_id_);
    proto.mutable_log_rpc_info()->set_rpc_call_id(log_rpc_info.rpc_call_id_);
  }
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();

  OpConcatV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAllocateBlockIdV2(uint64_t blockid) {
  CHECK(context_->IsOpenForWrite());

  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    return kInvalidTxId;
  }

  BlockIdToBeAllocate proto;
  proto.set_new_block_id(blockid);
  CHECK(proto.IsInitialized());

  OpAllocateBlockIdV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetGenerationStampV1(uint64_t genstamp) {
  CHECK(context_->IsOpenForWrite());

  GenStampToBeSet proto;
  proto.set_new_gen_stamp(genstamp);
  CHECK(proto.IsInitialized());

  OpSetGenStampV1 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetGenerationStampV2(uint64_t genstamp,
                                               bool force_send) {
  CHECK(context_->IsOpenForWrite());

  if (!force_send && FLAGS_enable_fast_block_id_and_gs_gen) {
    return kInvalidTxId;
  }

  GenStampToBeSet proto;
  proto.set_new_gen_stamp(genstamp);
  CHECK(proto.IsInitialized());

  OpSetGenStampV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogMergeBlocks(
    const std::string& path,
    const INode& inode,
    const BlockInfoProto& bip,
    const std::vector<BlockProto>& depred_blks,
    const INode& old_inode,
    const std::vector<INodeID>& ancestors_id,
    const SnapshotLog& inode_snaplog) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  FileAndBlockToBeMerge proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_merged_bip()->CopyFrom(bip);
  for (const auto& b : depred_blks) {
    proto.add_depred_blks()->CopyFrom(b);
  }
  proto.mutable_old_inode()->CopyFrom(old_inode);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());

  OpMergeBlock op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogPin(const std::string& path,
                              const INode& inode,
                              const INode& old_inode,
                              const JobInfoOpBody& job,
                              const ManagedJobId& cancel_job_id,
                              const LogRpcInfo& log_rpc_info,
                              bool update_txid) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  INodeToPin proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  if (job.IsInitialized()) {
    proto.mutable_job()->CopyFrom(job);
  }
  if (!cancel_job_id.empty()) {
    proto.set_cancel_job_id(cancel_job_id);
  }
  if (log_rpc_info.to_log_rpc_ids_) {
    proto.mutable_log_rpc_info()->set_rpc_client_id(
        log_rpc_info.rpc_client_id_);
    proto.mutable_log_rpc_info()->set_rpc_call_id(log_rpc_info.rpc_call_id_);
  }
  proto.set_update_txid(update_txid);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();

  OpPin op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogReconcileINodeAttrs(
    const std::string& path,
    const INode& inode,
    const INode& old_inode,
    const std::set<int64_t>& expired_ttl,
    const std::set<int64_t>& new_ttl,
    const std::vector<ManagedJobId>& cancel_job_id) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  INodeToReconcile proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  for (auto& job : cancel_job_id) {
    *(proto.add_cancel_job_id()) = job;
  }
  for (auto t : expired_ttl) {
    proto.add_expired_ttl(t);
  }
  for (auto t : new_ttl) {
    proto.add_new_ttl(t);
  }
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();

  OpReconcileINodeAttrs op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogPersistJobInfo(const JobInfoOpBody& job) {
  StopWatch sw(check_is_open_for_write_time_);
  sw.Start();
  CHECK(context_->IsOpenForWrite());

  sw.NextStep(serialize_time_);
  std::stringstream ss;
  OpPersistJobInfo op;
  auto t = job;
  t.set_physical_applyable(true);
  op.SetProto(t);
  op.WriteFields(&ss);

  sw.NextStep(call_context_time_);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogApproveUploadBlk(const BlockInfoProto& bip,
                                           const BlockInfoProto& old_bip) {
  CHECK(context_->IsOpenForWrite());
  BlkToBeApproveUpload proto;
  proto.mutable_bip()->CopyFrom(bip);
  proto.mutable_old_bip()->CopyFrom(old_bip);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpApproveUploadBlk op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogPersistBlk(const BlockInfoProto& bip,
                                     const BlockInfoProto& old_bip) {
  CHECK(context_->IsOpenForWrite());
  BlkToBePersist proto;
  proto.mutable_bip()->CopyFrom(bip);
  proto.mutable_old_bip()->CopyFrom(old_bip);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpPersistBlk op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogDelDepringBlks(const DepringBlksToBeDel& blks) {
  CHECK(context_->IsOpenForWrite());
  OpDelDepringBlks op;
  op.SetProto(blks);
  std::stringstream ss;
  op.WriteFields(&ss);
  auto txid = context_->LogCfsOp(&ss);
  ss.seekg(0, std::ios_base::end);
  LOG(INFO) << "txid=" << txid << " len=" << ss.tellg();
  return txid;
}

int64_t EditLogSender::LogDelDepredBlks(const DepredBlksToBeDel& blks) {
  CHECK(context_->IsOpenForWrite());
  OpDelDepredBlks op;
  op.SetProto(blks);
  std::stringstream ss;
  op.WriteFields(&ss);
  auto txid = context_->LogCfsOp(&ss);
  ss.seekg(0, std::ios_base::end);
  LOG(INFO) << "txid=" << txid << " len=" << ss.tellg();
  return txid;
}

int64_t EditLogSender::LogUpdateATimeProtos(const ATimeToBeUpdateProtos& atimes) {
  CHECK(context_->IsOpenForWrite());
  OpUpdateATimeProtos op;
  op.SetProto(atimes);
  std::stringstream ss;
  op.WriteFields(&ss);
  auto txid = context_->LogCfsOp(&ss);  
  
  ss.seekg(0, std::ios_base::end);
  LOG(INFO) << "txid=" << txid << " len=" << ss.tellg();
  return txid;
}

int64_t EditLogSender::LogFlushBlockInfoProtos(const BlockInfoProtos& bips) {
  CHECK(context_->IsOpenForWrite());
  OpFlushBlockInfoProtos op;
  op.SetProto(bips);
  std::stringstream ss;
  op.WriteFields(&ss);
  auto txid = context_->LogCfsOp(&ss);
  ss.seekg(0, std::ios_base::end);
  LOG(INFO) << "txid=" << txid << " len=" << ss.tellg();
  return txid;
}

int64_t EditLogSender::LogMkdirV2(const std::string& path,
                                  const INode& inode,
                                  const INode& parent,
                                  const LogRpcInfo& log_rpc_info,
                                  const std::vector<INodeID>& ancestors_id,
                                  const SnapshotLog& parent_snaplog) {
  CHECK(context_->IsOpenForWrite());
  DirToBeMake proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_parent()->CopyFrom(parent);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(parent_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpMkdirV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogDeleteV2(const std::string& path,
                                   const INode& inode,
                                   const INode& parent,
                                   uint64_t timestamp_in_ms,
                                   const std::vector<INodeID>& ancestors_id,
                                   const SnapshotLog& inode_snaplog,
                                   const SnapshotLog& parent_snaplog,
                                   const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToBeDelete proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_parent()->CopyFrom(parent);
  // NOTICE: For historical reasons,
  // when you compare inode from meta storage and inode from edit log,
  // please ignore last modified timestamp and other not important fields.
  proto.set_timestamp_in_ms(timestamp_in_ms);
  std::for_each(ancestors_id.begin(),
                ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_ancestors_id(id); });
  ENCODE_SNAPLOG(inode_snaplog);
  ENCODE_SNAPLOG(parent_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpDeleteV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogRenameOldV2(
    const std::string& src_path,
    const std::string& dst_path,
    const INode& src_inode,
    const INode& dst_inode,
    const INode& src_parent,
    const INode& dst_parent,
    uint64_t timestamp_in_ms,
    const std::vector<INodeID>& src_ancestors_id,
    const std::vector<INodeID>& dst_ancestors_id,
    const SnapshotLog& src_inode_snaplog,
    const SnapshotLog& src_parent_snaplog,
    const SnapshotLog& dst_parent_snaplog,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToBeRenameOld proto;
  proto.set_src_path(src_path);
  proto.set_dst_path(dst_path);
  proto.mutable_src_inode()->CopyFrom(src_inode);
  proto.mutable_dst_inode()->CopyFrom(dst_inode);
  proto.mutable_src_parent()->CopyFrom(src_parent);
  proto.mutable_dst_parent()->CopyFrom(dst_parent);
  proto.set_timestamp_in_ms(timestamp_in_ms);
  std::for_each(src_ancestors_id.begin(),
                src_ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_src_ancestors_id(id); });
  std::for_each(dst_ancestors_id.begin(),
                dst_ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_dst_ancestors_id(id); });
  ENCODE_SNAPLOG(src_inode_snaplog);
  ENCODE_SNAPLOG(src_parent_snaplog);
  ENCODE_SNAPLOG(dst_parent_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpRenameOldV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogRenameV2(
    const std::string& src_path,
    const std::string& dst_path,
    const INode& src_inode,
    const INode& new_dst_inode,
    const INode& src_parent,
    const INode& dst_parent,
    bool overwrite,
    const INode* old_dst_inode,
    bool move_to_recycle_bin,
    const std::string* rb_path,
    const INode* rb_inode,
    const INode* rb_parent,
    uint64_t timestamp_in_ms,
    const std::vector<INodeID>& src_ancestors_id,
    const std::vector<INodeID>& dst_ancestors_id,
    const std::vector<INodeID>* rb_ancestors_id,
    const SnapshotLog& src_inode_snaplog,
    const SnapshotLog& old_dst_inode_snaplog,
    const SnapshotLog& src_parent_snaplog,
    const SnapshotLog& dst_parent_snaplog,
    const SnapshotLog& rb_parent_snaplog,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToBeRename proto;
  proto.set_src_path(src_path);
  proto.set_dst_path(dst_path);
  proto.mutable_src_inode()->CopyFrom(src_inode);
  proto.mutable_dst_inode()->CopyFrom(new_dst_inode);
  proto.mutable_src_parent()->CopyFrom(src_parent);
  proto.mutable_dst_parent()->CopyFrom(dst_parent);
  if (old_dst_inode != nullptr) {
    CHECK(overwrite);
    proto.mutable_overwrite_inode()->CopyFrom(*old_dst_inode);
  }
  proto.set_timestamp_in_ms(timestamp_in_ms);
  if (overwrite) {
    proto.mutable_rename_options()->set_value(
        RenameOptions(RenameOption::kOverwrite).value());
  } else {
    proto.mutable_rename_options()->set_value(
        RenameOptions(RenameOption::kNone).value());
  }
  if (move_to_recycle_bin) {
    CHECK(overwrite);
    proto.set_move_to_recycle_bin(true);
    proto.set_rb_path(*rb_path);
    proto.mutable_rb_inode()->CopyFrom(*rb_inode);
    proto.mutable_rb_parent()->CopyFrom(*rb_parent);
    std::for_each(rb_ancestors_id->begin(),
                  rb_ancestors_id->end(),
                  [&proto] (INodeID id) { proto.add_rb_ancestors_id(id); });
  } else {
    proto.set_move_to_recycle_bin(false);
    CHECK(rb_path == nullptr);
    CHECK(rb_inode == nullptr);
    CHECK(rb_parent == nullptr);
    CHECK(rb_ancestors_id == nullptr);
  }
  std::for_each(src_ancestors_id.begin(),
                src_ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_src_ancestors_id(id); });
  std::for_each(dst_ancestors_id.begin(),
                dst_ancestors_id.end(),
                [&proto] (INodeID id) { proto.add_dst_ancestors_id(id); });
  ENCODE_SNAPLOG(src_inode_snaplog);
  ENCODE_SNAPLOG(old_dst_inode_snaplog);
  ENCODE_SNAPLOG(src_parent_snaplog);
  ENCODE_SNAPLOG(dst_parent_snaplog);
  ENCODE_SNAPLOG(rb_parent_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpRenameV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetReplicationV2(const std::string& path,
                                           const INode& inode,
                                           const INode& old_inode,
                                           uint32_t replication,
                                           const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetReplication proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.set_replication(replication);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetReplicationV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetStoragePolicyV2(const std::string& path,
                                             const INode& inode,
                                             const INode& old_inode,
                                             uint32_t policy_id,
                                             const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetStoragePolicy proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.set_policy_id(policy_id);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetStoragePolicyV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetReplicaPolicyV2(const std::string& path,
                                             const INode& inode,
                                             const INode& old_inode,
                                             int32_t policy_id,
                                             const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetReplicaPolicy proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.set_policy_id(policy_id);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetReplicaPolicyV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetDirReplicaPolicyV2(
    const std::string& path,
    const INode& inode,
    const INode& old_inode,
    int32_t policy_id,
    const std::string& dc,
    const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  DirToSetReplicaPolicy proto;
  proto.set_path(path);
  *proto.mutable_inode() = inode;
  *proto.mutable_old_inode() = old_inode;
  proto.set_policy_id(policy_id);
  proto.set_dc(dc);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetDirReplicaPolicyV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetQuotaV2(const std::string& src,
                                     uint64_t ns_quota,
                                     uint64_t ds_quota,
                                     const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetQuota proto;
  proto.set_src(src);
  proto.set_ns_quota(ns_quota);
  proto.set_ds_quota(ds_quota);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpSetQuotaV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetPermissionsV2(const std::string& path,
                                           const INode& inode,
                                           const INode& old_inode,
                                           uint16_t permission,
                                           const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetPermissions proto;
  proto.set_path(path);
  *proto.mutable_inode() = inode;
  *proto.mutable_old_inode() = old_inode;
  proto.set_permissions(permission);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetPermissionsV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetOwnerV2(const std::string& path,
                                     const INode& inode,
                                     const INode& old_inode,
                                     const std::string& username,
                                     const std::string& groupname,
                                     const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetOwner proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.set_username(username);
  proto.set_groupname(groupname);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetOwnerV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetTimesV2(const std::string& src,
                                     uint64_t mtime,
                                     uint64_t atime,
                                     const INode& inode,
                                     const INode& old_inode,
                                     const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetTimes proto;
  proto.set_path(src);
  proto.set_mtime(mtime);
  proto.set_atime(atime);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  ENCODE_SNAPLOG(inode_snaplog);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpSetTimesV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSymlinkV2(const std::string& path,
                                    const INode& node,
                                    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToSymlink proto;
  proto.set_path(path);
  proto.mutable_node()->CopyFrom(node);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpSymlinkV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetAclV2(
    const std::string& src,
    const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>& entries) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetAcl proto;
  proto.set_path(src);
  for (auto& e : entries) {
    proto.add_acl_entries()->CopyFrom(e);
  }
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpSetAclV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetXAttrsV2(
    const std::string& path,
    const INode& inode,
    const INode& old_inode,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
    const SnapshotLog& inode_snaplog,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToSetXAttrs proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.mutable_xattrs()->CopyFrom(xattrs);
  ENCODE_SNAPLOG(inode_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetXAttrsV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogRemoveXAttrsV2(
    const std::string& path,
    const INode& inode,
    const INode& old_inode,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
    const SnapshotLog& inode_snaplog,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  INodeToRemoveXAttrs proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);
  proto.mutable_old_inode()->CopyFrom(old_inode);
  proto.mutable_xattrs()->CopyFrom(xattrs);
  ENCODE_SNAPLOG(inode_snaplog);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpRemoveXAttrsV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetLifecyclePolicy(
    const INode& inode,
    const std::string& path,
    uint64_t ts,
    const cloudfs::LifecyclePolicyProto& policy,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  LifecyclePolicyToBeSet proto;
  proto.set_inode_id(inode.id());
  proto.set_path(path);
  proto.set_timestamp_ms(ts);
  proto.mutable_policy()->CopyFrom(policy);
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpSetLifecyclePolicy op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogUnsetLifecyclePolicy(
    const INodeID& id,
    const std::string* path,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  LifecyclePolicyToBeUnset proto;
  proto.set_inode_id(id);
  if (path) {
    proto.set_path(*path);
  }
  ENCODE_RPCINFO(log_rpc_info);
  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpUnsetLifecyclePolicy op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccSyncDummy() {
  CHECK(context_->IsOpenForWrite());
  OpAccSyncDummy op;
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccSyncListingBatchAdd(
    const std::string& dir_path,
    const INode& dir_inode,
    const std::vector<INodeWithBlocks>& file_blocks) {
  CHECK(context_->IsOpenForWrite());
  OpAccSyncListingBatchAdd op;
  {
    AccSyncListingBatchAddOpBody body;
    body.set_dir_path(dir_path);
    body.set_allocated_dir_inode(new INode(dir_inode));
    for (auto&& f : file_blocks) {
      auto subfile = body.add_files_to_add();
      subfile->set_allocated_inode(new INode(f.node));
      for (auto&& b : f.blocks) {
        auto block_info = subfile->add_block_info();
        *block_info = b;
      }
    }
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAllowSnapshotV2(const std::string& path,
                                          const INode& inode) {
  CHECK(context_->IsOpenForWrite());

  SnapshotToAllow proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);

  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpAllowSnapshotV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccSyncListingBatchUpdate(
    const std::string& dir_path,
    const INode& dir_inode,
    const std::vector<INode>& inodes_to_update,
    const std::vector<INode>& old_inodes) {
  CHECK(context_->IsOpenForWrite());
  OpAccSyncListingBatchUpdate op;
  {
    AccSyncListingBatchUpdateOpBody body;
    body.set_dir_path(dir_path);
    body.set_allocated_dir_inode(new INode(dir_inode));
    for (auto&& f : inodes_to_update) {
      auto subfile = body.add_files_to_update();
      *subfile = f;
    }
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogDisallowSnapshotV2(const std::string& path,
                                             const INode& inode) {
  CHECK(context_->IsOpenForWrite());

  SnapshotToDisallow proto;
  proto.set_path(path);
  proto.mutable_inode()->CopyFrom(inode);

  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpDisallowSnapshotV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccSyncUpdateINode(const std::string& path,
                                             const INode& inode,
                                             const INode& old_inode) {
  CHECK(context_->IsOpenForWrite());
  OpAccSyncUpdateINode op;
  {
    AccSyncUpdateINodeOpBody body;
    body.set_path(path);
    body.mutable_inode()->CopyFrom(inode);
    body.mutable_old_inode()->CopyFrom(old_inode);
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogCreateSnapshotV2(const std::string& path,
                                           const std::string& name,
                                           uint64_t timestamp_in_ms,
                                           const INode& inode,
                                           uint64_t new_snapshot_id) {
  CHECK(context_->IsOpenForWrite());

  SnapshotToCreate proto;
  proto.set_path(path);
  proto.set_name(name);
  proto.set_timestamp_in_ms(timestamp_in_ms);
  proto.mutable_inode()->CopyFrom(inode);
  proto.set_new_snapshot_id(new_snapshot_id);

  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpCreateSnapshotV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccSyncAddFile(const std::string& path,
                                         const INode& parent,
                                         const INodeWithBlocks& file,
                                         const INode* file_to_del) {
  CHECK(context_->IsOpenForWrite());
  OpAccSyncAddFile op;
  {
    FileWithBlocks fb;
    fb.set_allocated_inode(new INode(file.node));
    for (auto&& b : file.blocks) {
      *fb.add_block_info() = b;
    }
    AccSyncAddFileOpBody body;
    body.set_path(path);
    body.set_allocated_parent(new INode(parent));
    body.set_allocated_file_blocks(new FileWithBlocks(std::move(fb)));
    if (file_to_del != nullptr) {
      body.set_allocated_old_file_to_del(new INode(*file_to_del));
    }
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccPersistFile(const std::string& path,
                                         const INode& parent,
                                         const INode& inode,
                                         const INode& old_inode) {
  CHECK(context_->IsOpenForWrite());
  OpAccPersistFile op;
  {
    AccPersistFileOpBody body;
    body.set_path(path);
    body.mutable_parent()->CopyFrom(parent);
    body.mutable_inode()->CopyFrom(inode);
    body.mutable_old_inode()->CopyFrom(old_inode);
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogDeleteSnapshotV2(const std::string& path,
                                           const std::string& name,
                                           uint64_t timestamp_in_ms,
                                           const INode& inode) {
  CHECK(context_->IsOpenForWrite());

  SnapshotToDelete proto;
  proto.set_path(path);
  proto.set_name(name);
  proto.set_timestamp_in_ms(timestamp_in_ms);
  proto.mutable_inode()->CopyFrom(inode);

  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpDeleteSnapshotV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogAccUpdateBlockInfo(const BlockInfoProto& bip,
                                             const BlockInfoProto& old_bip) {
  CHECK(context_->IsOpenForWrite());
  OpAccUpdateBlockInfo op;
  {
    AccUpdateBlockInfoOpBody body;
    body.mutable_bip()->CopyFrom(bip);
    body.set_physical_applyable(true);
    op.SetProto(std::move(body));
  }
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogRenameSnapshotV2(const std::string& path,
                                           const std::string& old_name,
                                           const std::string& new_name,
                                           uint64_t timestamp_in_ms,
                                           const INode& inode,
                                           const SnapshotLog& inode_snaplog) {
  CHECK(context_->IsOpenForWrite());

  SnapshotToRename proto;
  proto.set_path(path);
  proto.set_old_name(old_name);
  proto.set_new_name(new_name);
  proto.set_timestamp_in_ms(timestamp_in_ms);
  proto.mutable_inode()->CopyFrom(inode);
  ENCODE_SNAPLOG(inode_snaplog);

  proto.set_physical_applyable(true);
  CHECK(proto.IsInitialized());
  OpRenameSnapshotV2 op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogSetAZBlacklist(
    const std::string& azs) {
  CHECK(context_->IsOpenForWrite());
  AZBlacklist proto;
  proto.set_azs(azs);
  CHECK(proto.IsInitialized());
  OpSetAZBlacklist op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogBatchCreateFile(
    const std::vector<std::string>& paths,
    const std::vector<INode>& inodes,
    const std::vector<INode>& overwritten_inodes,
    const std::vector<BlockInfoProto>& overwritten_bips,
    const std::vector<BlockInfoProto>& add_block_bips,
    const std::vector<std::vector<std::string>>& bips_expected_locs,
    // other
    const INode& parent,
    uint64_t timestamp_in_ms,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  CHECK_EQ(paths.size(), inodes.size());
  CHECK(paths.size() == add_block_bips.size() || add_block_bips.empty());
  CHECK(paths.size() == bips_expected_locs.size() ||
        bips_expected_locs.empty());
  CHECK_EQ(add_block_bips.size(), bips_expected_locs.size());

  BatchInodeToCreate proto;

  for (int i = 0; i < paths.size(); ++i) {
    auto file = proto.add_files();
    file->set_path(paths[i]);
    file->mutable_inode()->CopyFrom(inodes[i]);
    if (!add_block_bips.empty()) {
      file->mutable_add_block_bips()->CopyFrom(add_block_bips[i]);
    }
    if (!bips_expected_locs.empty()) {
      auto bip_with_locs = file->add_add_block_bips_with_locs();
      bip_with_locs->mutable_bip()->CopyFrom(add_block_bips[i]);
      for (auto& dn : bips_expected_locs[i]) {
        bip_with_locs->add_dns(dn);
      }
    }
    // bips not used in block proto v1
  }

  // overwrite
  for (const auto& inode : overwritten_inodes) {
    proto.add_overwritten_inodes()->CopyFrom(inode);
  }
  for (const auto& bip : overwritten_bips) {
    proto.add_overwritten_bips()->CopyFrom(bip);
  }

  if (log_rpc_info.to_log_rpc_ids_) {
    proto.mutable_log_rpc_info()->set_rpc_client_id(
        log_rpc_info.rpc_client_id_);
    proto.mutable_log_rpc_info()->set_rpc_call_id(log_rpc_info.rpc_call_id_);
  }
  proto.mutable_parent()->CopyFrom(parent);
  proto.set_timestamp_in_ms(timestamp_in_ms);

  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpBatchCreateFile op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogBatchCompleteFile(
    const std::vector<std::string>& paths,
    const std::vector<INode>& complete_inodes,
    const std::vector<INode>& deleted_inodes,
    const std::vector<BlockInfoProto>& bips,
    const std::vector<BlockInfoProto>& deleted_bips,
    const std::vector<INode>& original_inodes,
    // other
    const INode& parent,
    uint64_t timestamp_in_ms,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  BatchInodeToComplete proto;

  // single file
  CHECK_EQ(paths.size(), original_inodes.size());
  for (int i = 0; i < paths.size(); ++i) {
    proto.add_paths(paths[i]);
    // bips not used in block proto v1
    proto.add_original_inodes()->CopyFrom(original_inodes[i]);
  }

  for (const auto& inode : complete_inodes) {
    proto.add_complete_inodes()->CopyFrom(inode);
  }
  for (const auto& inode : deleted_inodes) {
    proto.add_deleted_inodes()->CopyFrom(inode);
  }
  for (const auto& bip : bips) {
    proto.add_bips()->CopyFrom(bip);
  }
  for (const auto& bip : deleted_bips) {
    proto.add_deleted_bips()->CopyFrom(bip);
  }

  // other
  if (log_rpc_info.to_log_rpc_ids_) {
    proto.mutable_log_rpc_info()->set_rpc_client_id(
        log_rpc_info.rpc_client_id_);
    proto.mutable_log_rpc_info()->set_rpc_call_id(log_rpc_info.rpc_call_id_);
  }
  proto.mutable_parent()->CopyFrom(parent);
  proto.set_timestamp_in_ms(timestamp_in_ms);

  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpBatchCompleteFile op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

int64_t EditLogSender::LogBatchDeleteFile(
    const std::vector<std::string>& paths,
    const std::vector<INode>& inodes,
    const std::vector<BlockInfoProto>& bips,
    // other
    const INode& parent,
    uint64_t timestamp_in_ms,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  CHECK_EQ(paths.size(), inodes.size());

  BatchInodeToDelete proto;
  for (int i = 0; i < paths.size(); ++i) {
    auto file = proto.add_files();
    file->set_path(paths[i]);
    file->mutable_inode()->CopyFrom(inodes[i]);
    // bips not used in block proto v1
  }

  if (log_rpc_info.to_log_rpc_ids_) {
    proto.mutable_log_rpc_info()->set_rpc_client_id(
        log_rpc_info.rpc_client_id_);
    proto.mutable_log_rpc_info()->set_rpc_call_id(log_rpc_info.rpc_call_id_);
  }
  proto.mutable_parent()->CopyFrom(parent);
  proto.set_timestamp_in_ms(timestamp_in_ms);

  CHECK(proto.IsInitialized()) << proto.InitializationErrorString();
  OpBatchDeleteFile op;
  op.SetProto(std::move(proto));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCfsOp(&ss);
}

}  // namespace dancenn
