//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef EDIT_COMMITTER_H_
#define EDIT_COMMITTER_H_

#include <cnetpp/concurrency/task.h>  // For Task.

#include <memory>  // For shared_ptr, unique_ptr, make_unique.
#include <mutex>   // For mutex, lock_guard.
#include <vector>  // For vector.

#include "base/read_write_lock.h"  // For ReadWriteLock.
#include "base/ring_buffer.h"      // For RingBuffer.

namespace dancenn {

class HAEditLogContext;
class BGEditLogSyncer;
class EditLogTask;

// Task running background to commit edit log.
class BGEditLogCommitter : public cnetpp::concurrency::Task {
 public:
  BGEditLogCommitter(int max_producers,
                     HAEditLogContext* context,
                     std::shared_ptr<BGEditLogSyncer> syncer);

  void SetNextTxId(int64_t txid);
  void SetLastAllocatedBlockId(uint64_t block_id);
  void SetLastGenerationStampV2(uint64_t gsv2);

  void Commit(EditLogTask* task, bool worse_channel = false);
  bool operator()(void* arg = nullptr) override;
  bool TestOnlyIsChannelsFull();

  // Before invoking this function, you must first call
  // `BGEditLogSyncer::HASwitchFence`. This action ensures that further calls to
  // `BGEditLogSyncer::Sync` will fail.
  //
  // Once this function completes, the system will no longer allow committing
  // any `EditLogTask` (which would result in a coredump).
  //
  // Return value is [pending_begin_txid, pending_end_txid).
  void HASwitchFence(int64_t* pending_begin_txid,
                     int64_t* pending_end_txid,
                     uint64_t* last_allocated_block_id,
                     uint64_t* last_generation_stamp_v2);

 private:
  std::unique_ptr<EditLogTask> TxStart(int channel_id);
  void TxFinish(EditLogTask* task);

 private:
  ReadWriteLock producer_lock_;
  std::mutex consumer_mtx_;
  bool is_fenced_;
  std::vector<std::unique_ptr<RingBuffer<EditLogTask*>>> channels_;
  int64_t already_ok_task_txid_;
  // Refer to `FSEditLog::lastAllocatedBlockID`.
  uint64_t last_allocated_block_id_;
  // Refer to `FSEditLog::lastGenerationStampV2`.
  uint64_t last_generation_stamp_v2_;
  EditLogTask* processing_task_{nullptr};
  HAEditLogContext* context_{nullptr};
  std::shared_ptr<BGEditLogSyncer> syncer_;
};

}  // namespace dancenn

#endif  // EDIT_COMMITTER_H_
