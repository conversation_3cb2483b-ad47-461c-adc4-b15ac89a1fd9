// Copyright 2017 He <PERSON> <<EMAIL>>

#include "edit/ha_edit_log_context.h"

#include <absl/strings/str_format.h>
#include <cnetpp/concurrency/thread.h>
#include <cxxabi.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <jni.h>

#define UNW_LOCAL_ONLY
#include <libunwind.h>

#include <cstdint>
#include <shared_mutex>

#include "base/constants.h"
#include "base/defer.h"
#include "base/java.h"
#include "base/logger_metrics.h"
#include "base/platform.h"
#include "base/stop_watch.h"
#include "edit/committer.h"
#include "edit/edit_log_task.h"
#include "edit/serializer.h"
#include "edit/syncer.h"

DECLARE_bool(run_ut);
DECLARE_string(deploy_dir);
DECLARE_bool(use_bk_editlog_os);
DECLARE_int32(edit_log_last_sync_stale_interval_ms);
DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

bool LogEditLogTooLargeError(const std::string& editlog) {
  if (editlog.size() < /*2MiB=*/2 * 1024 * 1024) {
    return false;
  }

  std::stringstream stack_msg;
  do {
    // Initialize cursor to current frame for local unwinding.
    unw_context_t context;
    if (unw_getcontext(&context) < 0) {
      LOG(INFO) << "Cannot get local machine state";
      break;
    }
    unw_cursor_t cursor;
    if (unw_init_local(&cursor, &context) < 0) {
      LOG(INFO) << "Cannot initialize cursor for local unwinding";
      break;
    }
    for (auto l = 32; l > 0 && unw_step(&cursor) > 0; l--) {
      unw_word_t pc = 0;
      unw_get_reg(&cursor, UNW_REG_IP, &pc);
      if (pc == 0) {
        LOG(INFO) << "Get stacktrace stop with l=" << l;
        break;
      }
      char symbol_name[2048];
      unw_word_t offset = 0;
      stack_msg << absl::StrFormat(
          "0x%x/%s,",
          pc,
          unw_get_proc_name(
              &cursor, symbol_name, sizeof(symbol_name), &offset) == 0
              ? symbol_name
              : "<unknown>");
    }
  } while (false);

  // Refer to EditLogCfsOp::WriteFields.
  std::stringstream editlog_msg;
  const char* p_editlog = editlog.c_str();
  OpCode op_code = *reinterpret_cast<const OpCode*>(p_editlog);
  p_editlog += sizeof(op_code);
  p_editlog += sizeof(/*txid=*/int64_t);
  editlog_msg << absl::StrFormat("op_code:%d,", op_code);
  if (op_code == OP_CFS) {
    uint32_t cfs_op_code = platform::BigEndianToHost<uint32_t>(
        *reinterpret_cast<const uint32_t*>(p_editlog));
    p_editlog += sizeof(cfs_op_code);
    uint32_t prefix = platform::BigEndianToHost<uint32_t>(
        *reinterpret_cast<const uint32_t*>(p_editlog));
    p_editlog += sizeof(prefix);
    uint32_t suffix =
        platform::BigEndianToHost<uint32_t>(*reinterpret_cast<const uint32_t*>(
            // https://en.cppreference.com/w/cpp/string/basic_string/size
            // std::string s("Exemplar");
            // assert(8 == s.size());
            editlog.c_str() + editlog.size() - sizeof(suffix)));
    editlog_msg << absl::StrFormat(
        "cfs_op_code:%d,prefix:%x,suffix:%x", cfs_op_code, prefix, suffix);
  }

  LOG(ERROR) << "EditLogTooLarge " << stack_msg.str() << editlog_msg.str();
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return true;
}

const char* kEditLogConfDisallowBookieRegion =
    "dfs.namenode.bookkeeperjournal.disallowBookieRegion";

HAEditLogInputContext::HAEditLogInputContext(JavaRuntime* jvm,
                                             jclass clazz,
                                             jobject obj)
    : jvm_(jvm), java_obj_(new JavaObject(jvm, clazz, obj)) {
  auto env = jvm_->env();
  method_read_op_to_byte_buffer_ = env->GetMethodID(
      clazz, "readOpToByteBufferV2", "(Ljava/nio/ByteBuffer;)I");
  CHECK(method_read_op_to_byte_buffer_);
  method_close_ = env->GetMethodID(clazz, "close", "()V");
  CHECK(method_close_);
  jclass clazz_bb = env->FindClass("java/nio/ByteBuffer");
  CHECK(clazz_bb);
  method_bb_position_ = env->GetMethodID(clazz_bb, "position", "()I");
  CHECK(method_bb_position_);
  buffer_.reserve(32 * 1024 * 1024);  // 32MiB
  bb_.reset(new JavaObject(
      jvm_,
      clazz_bb,
      env->NewDirectByteBuffer(&buffer_.front(), buffer_.capacity())));
}

HAEditLogInputContext::~HAEditLogInputContext() {
  // User should call close before destructing HAEditLogInputContext.
  // Otherwise, ReadOnlyLedgerHandle objects leak and oom happens.
  // https://bytedance.feishu.cn/docx/doxcn4Y8jQLSX8fILElY0IxGxvf
  jvm_->env()->CallVoidMethod(java_obj_->ref(), method_close_);
  CHECK_JVM_EXCEPTION(jvm_);
}

bool HAEditLogInputContext::ReadOp(std::string* serialized_op) {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    static std::once_flag once_flag;
    static MetricID read_op_time;
    static MetricID read_op_num;
    std::call_once(once_flag, []() {
      auto center = MetricsCenter::Instance();
      auto metrics = center->RegisterMetrics("EditLogInputContext");
      read_op_time = metrics->RegisterHistogram("ReadOpTime");
      read_op_num = metrics->RegisterCounter("ReadOpNum");
    });

    sw.NextStep();
    MFH(read_op_time)
        ->Update(
            std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
                .count());
    MFC(read_op_num)->Inc();
  });

  jint rtn = jvm_->env()->CallIntMethod(
      java_obj_->ref(), method_read_op_to_byte_buffer_, bb_->ref());
  CHECK_JVM_EXCEPTION(jvm_);
  if (rtn == -1) {
    return false;
  } else if (rtn == 0) {
    LOG(INFO) << "HAEditLogInputContext::ReadOp() error, maybe reach EOF";
    *serialized_op = "";
    return true;
  } else {
    jint pos = jvm_->env()->CallIntMethod(bb_->ref(), method_bb_position_);
    *serialized_op = std::string(&buffer_.front(), pos);
    return true;
  }
}

HAEditLogContext::HAEditLogContext(JavaRuntime* jvm, int max_producers)
    : jvm_(jvm),
      is_open_for_write_(false),
      is_bg_worker_running_(false),
      last_allocated_block_id_(kInvalidBlockID),
      last_generation_stamp_v2_(kInvalidGenerationStamp) {
  if (jvm_ == nullptr) {
    CHECK(FLAGS_run_ut);
    CHECK(!is_bg_worker_running_.exchange(true));
    CHECK(!bg_edit_log_sync_worker_.get());
    bg_edit_log_syncer_.reset(
        new BGEditLogSyncer(this, (max_producers + 1) * 20));
    CHECK(!bg_edit_log_commit_worker_.get());
    bg_edit_log_committer_.reset(
        new BGEditLogCommitter(max_producers, this, bg_edit_log_syncer_));
    return;
  }

  auto env = jvm_->env();
  jclass klass = env->FindClass(
      "org/apache/hadoop/hdfs/server/namenode/DanceNNEditLogContext");
  CHECK(klass);
  jmethodID method_new_configuration = env->GetStaticMethodID(
      klass, "newConfiguration", "()Lorg/apache/hadoop/conf/Configuration;");
  CHECK(method_new_configuration);
  jmethodID constructor = env->GetMethodID(
      klass, "<init>", "(Lorg/apache/hadoop/conf/Configuration;)V");
  CHECK(constructor);
  jobject hadoop_conf =
      env->CallStaticObjectMethod(klass, method_new_configuration);
  CHECK_JVM_EXCEPTION(jvm_);
  LOG(INFO) << "Successfully created hadoop configuration";
  jobject context = env->NewObject(klass, constructor, hadoop_conf);
  CHECK_JVM_EXCEPTION(jvm_);
  java_obj_.reset(new JavaObject(jvm_, klass, context));

  // Make sure hadoop-dev loads current hdfs-site.xml and core-site.xml.
  jmethodID method_get_hdfs_site_location =
      env->GetMethodID(klass, "getHdfsSiteLocation", "()Ljava/lang/String;");
  CHECK(method_get_hdfs_site_location);
  jstring hsl =
      (jstring)env->CallObjectMethod(context, method_get_hdfs_site_location);
  const char* hdfs_site_location = env->GetStringUTFChars(hsl, 0);
  CHECK_JVM_EXCEPTION(jvm_);
  if (!FLAGS_run_ut) {
    CHECK_EQ(hdfs_site_location,
             FLAGS_deploy_dir + "/cfs_hdfs_deploy/hadoop/conf/hdfs-site.xml");
    LOG(INFO) << "hdfs-site.xml: " << hdfs_site_location;
  }
  env->ReleaseStringUTFChars(hsl, hdfs_site_location);
  jmethodID method_get_core_site_location =
      env->GetMethodID(klass, "getCoreSiteLocation", "()Ljava/lang/String;");
  jstring csl =
      (jstring)env->CallObjectMethod(context, method_get_core_site_location);
  const char* core_site_location = env->GetStringUTFChars(csl, 0);
  CHECK_JVM_EXCEPTION(jvm_);
  if (!FLAGS_run_ut) {
    CHECK_EQ(core_site_location,
             FLAGS_deploy_dir + "/cfs_hdfs_deploy/hadoop/conf/core-site.xml");
    LOG(INFO) << "core-site.xml: " << core_site_location;
  }
  env->ReleaseStringUTFChars(csl, core_site_location);

  class_input_context_ = (jclass)env->NewGlobalRef(env->FindClass(
      "org/apache/hadoop/hdfs/server/namenode/"
      "DanceNNEditLogContext$EditLogInputContext"));  // NOLINT(whitespace/line_length)
  CHECK(class_input_context_);
  method_create_input_context_ = env->GetMethodID(
      klass,
      "createInputContext",
      "(JJZ)Lorg/apache/hadoop/hdfs/server/namenode/"
      "DanceNNEditLogContext$EditLogInputContext;");  // NOLINT(whitespace/line_length)
  CHECK(method_create_input_context_);

  method_open_for_read_ = env->GetMethodID(klass, "openForRead", "()V");
  CHECK(method_open_for_read_);
  method_init_journals_for_write_ =
      env->GetMethodID(klass, "initJournalsForWrite", "()V");
  CHECK(method_init_journals_for_write_);

  method_open_for_write_ = env->GetMethodID(klass, "openForWriteNew", "()J");
  CHECK(method_open_for_write_);
  OpMethodName(method_open_for_write_, "openForWriteNew");

  method_is_open_for_read_ = env->GetMethodID(klass, "isOpenForRead", "()Z");
  CHECK(method_is_open_for_read_);
  method_is_open_for_write_ = env->GetMethodID(klass, "isOpenForWrite", "()Z");
  CHECK(method_is_open_for_write_);
  method_use_bk_editlog_os_ =
      env->GetMethodID(klass, "useBookKeeperEditLogOutputStream", "()Z");
  CHECK(method_use_bk_editlog_os_);

  method_update_conf_property_ =
      env->GetMethodID(klass, "updateConfProperty", "([B)Z");
  CHECK(method_update_conf_property_);

  method_log_sync_ = env->GetMethodID(klass, "logSync", "()V");
  CHECK(method_log_sync_);
  method_log_sync_all_ = env->GetMethodID(klass, "logSyncAll", "()V");
  CHECK(method_log_sync_all_);
  method_close_ = env->GetMethodID(klass, "close", "()V");
  CHECK(method_close_);
  OpMethodName(method_close_, "close");
  method_roll_edit_log_ = env->GetMethodID(klass, "rollEditLog", "()J");
  CHECK(method_roll_edit_log_);
  OpMethodName(method_roll_edit_log_, "rollEditLog");
  method_set_next_tx_id_ = env->GetMethodID(klass, "setNextTxId", "(J)V");
  CHECK(method_set_next_tx_id_);
  method_get_last_allocated_block_id_ =
      env->GetMethodID(klass, "getLastAllocatedBlockId", "()J");
  CHECK(method_get_last_allocated_block_id_);
  method_get_last_generation_stamp_v2_ =
      env->GetMethodID(klass, "getLastGenerationStampV2", "()J");
  CHECK(method_get_last_allocated_block_id_);
  method_get_cur_segment_tx_id_ =
      env->GetMethodID(klass, "getCurSegmentTxId", "()J");
  CHECK(method_get_cur_segment_tx_id_);
  method_get_last_written_tx_id_ =
      env->GetMethodID(klass, "getLastWrittenTxId", "()J");
  CHECK(method_get_last_written_tx_id_);
  method_purge_logs_older_than_ =
      env->GetMethodID(klass, "purgeLogsOlderThan", "(J)V");
  CHECK(method_purge_logs_older_than_);
  method_get_peer_nn_addr_ = env->GetMethodID(klass, "getPeerNNAddr", "()[B");
  CHECK(method_get_peer_nn_addr_);
  method_get_all_stack_traces_ =
      env->GetMethodID(klass, "getAllStackTraces", "()[B");
  CHECK(method_get_all_stack_traces_);

  InitOpMethods(klass);

  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("EditLogContext");
  create_input_context_time_ =
      metrics->RegisterHistogram("CreateInputContextTime");
  create_input_context_num_ =
      metrics->RegisterCounter("CreateInputContextNum");
  edit_log_time_ = metrics->RegisterHistogram("EditLogTime");
  edit_sync_time_ = metrics->RegisterHistogram("EditSyncTime");

  StartBGEditLogWorker(max_producers);
}

// TestOnly.
HAEditLogContext::HAEditLogContext()
    : jvm_(nullptr), is_open_for_write_(false) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("EditLogContext");
  edit_log_time_ = metrics->RegisterHistogram("EditLogTime");
  edit_sync_time_ = metrics->RegisterHistogram("EditSyncTime");
}

void HAEditLogContext::SetupCommitter(
    std::shared_ptr<BGEditLogCommitter> bg_edit_log_committer) {
  bg_edit_log_committer_ = bg_edit_log_committer;
}

void HAEditLogContext::SetUpSyncer(
    std::shared_ptr<BGEditLogSyncer> bg_edit_log_syncer) {
  bg_edit_log_syncer_ = bg_edit_log_syncer;
}

void HAEditLogContext::SetupSyncListener(
    std::shared_ptr<IEditLogSyncListener> listener) {
  bg_edit_log_syncer_->SetupListener(listener);
}

std::shared_ptr<IEditLogSyncListener> HAEditLogContext::
    TestOnlyGetSyncListener() {
  return nullptr;
}

void HAEditLogContext::InitOpMethods(jclass klass) {
  auto env = jvm_->env();

  method_log_open_file_ = env->GetMethodID(klass, "logOpenFile", "([BZ)J");
  CHECK(method_log_open_file_);
  OpMethodName(method_log_open_file_, "logOpenFile");

  method_log_close_file_ = env->GetMethodID(klass, "logCloseFile", "([B)J");
  CHECK(method_log_close_file_);
  OpMethodName(method_log_close_file_, "logCloseFile");

  method_log_add_block_ = env->GetMethodID(klass, "logAddBlock", "([B)J");
  CHECK(method_log_add_block_);
  OpMethodName(method_log_add_block_, "logAddBlock");

  method_log_update_blocks_ =
      env->GetMethodID(klass, "logUpdateBlocks", "([BZ)J");
  CHECK(method_log_update_blocks_);
  OpMethodName(method_log_update_blocks_, "LogUpdateBlocks");

  method_log_mk_dir_ = env->GetMethodID(klass, "logMkDir", "([B)J");
  CHECK(method_log_mk_dir_);
  OpMethodName(method_log_mk_dir_, "logMkDir");

  method_log_rename_old_ = env->GetMethodID(klass, "logRenameOld", "([BZ)J");
  CHECK(method_log_rename_old_);
  OpMethodName(method_log_rename_old_, "logRenameOld");

  method_log_rename_ = env->GetMethodID(klass, "logRename", "([BZ)J");
  CHECK(method_log_rename_);
  OpMethodName(method_log_rename_, "logRename");

  method_log_set_replication_ =
      env->GetMethodID(klass, "LogSetReplication", "([B)J");
  CHECK(method_log_set_replication_);
  OpMethodName(method_log_set_replication_, "LogSetReplication");

  method_log_set_storage_policy_ =
      env->GetMethodID(klass, "LogSetStoragePolicy", "([B)J");
  CHECK(method_log_set_storage_policy_);
  OpMethodName(method_log_set_storage_policy_, "LogSetStoragePolicy");

  method_log_set_dir_replica_policy_ =
      env->GetMethodID(klass, "logSetDirReplicaPolicy", "([B)J");
  CHECK(method_log_set_dir_replica_policy_);
  OpMethodName(method_log_set_dir_replica_policy_, "logSetDirReplicaPolicy");

  method_log_set_replica_policy_ =
      env->GetMethodID(klass, "logSetReplicaPolicy", "([B)J");
  CHECK(method_log_set_replica_policy_);
  OpMethodName(method_log_set_replica_policy_, "logSetReplicaPolicy");

  method_log_set_quota_ = env->GetMethodID(klass, "logSetQuota", "([B)J");
  CHECK(method_log_set_quota_);
  OpMethodName(method_log_set_quota_, "logSetQuota");

  method_log_set_permission_ =
      env->GetMethodID(klass, "logSetPermission", "([B)J");
  CHECK(method_log_set_permission_);
  OpMethodName(method_log_set_permission_, "logSetPermission");

  method_log_set_owner_ = env->GetMethodID(klass, "logSetOwner", "([B)J");
  CHECK(method_log_set_owner_);
  OpMethodName(method_log_set_owner_, "LogSetOwner");

  method_log_concat_ = env->GetMethodID(klass, "logConcat", "([BZ)J");
  CHECK(method_log_concat_);
  OpMethodName(method_log_concat_, "logConcat");

  method_log_delete_ = env->GetMethodID(klass, "logDelete", "([BZ)J");
  CHECK(method_log_delete_);
  OpMethodName(method_log_delete_, "logDelete");

  method_log_generation_stamp_v1_ =
      env->GetMethodID(klass, "logGenerationStampV1", "([B)J");
  CHECK(method_log_generation_stamp_v1_);
  OpMethodName(method_log_generation_stamp_v1_, "logGenerationStampV1");

  method_log_generation_stamp_v2_ =
      env->GetMethodID(klass, "logNewGenerationStampV2", "([B)[B");
  CHECK(method_log_generation_stamp_v2_);
  OpMethodName(method_log_generation_stamp_v2_, "logNewGenerationStampV2");

  method_log_allocate_block_id_ =
      env->GetMethodID(klass, "logNewAllocateBlockId", "([B)[B");
  CHECK(method_log_allocate_block_id_);
  OpMethodName(method_log_allocate_block_id_, "logNewAllocateBlockId");

  method_log_times_ = env->GetMethodID(klass, "logTimes", "([B)J");
  CHECK(method_log_times_);
  OpMethodName(method_log_times_, "logTimes");

  method_log_symlink_ = env->GetMethodID(klass, "logSymlink", "([BZ)J");
  CHECK(method_log_symlink_);
  OpMethodName(method_log_symlink_, "logSymlink");

  method_log_reassign_lease_ =
      env->GetMethodID(klass, "logReassignLease", "([B)J");
  CHECK(method_log_reassign_lease_);
  OpMethodName(method_log_reassign_lease_, "logReassignLease");

  method_log_set_acl_ = env->GetMethodID(klass, "logSetAcl", "([B)J");
  CHECK(method_log_set_acl_);
  OpMethodName(method_log_set_acl_, "logSetAcl");

  method_log_set_xttrs_ = env->GetMethodID(klass, "logSetXttrs", "([BZ)J");
  CHECK(method_log_set_xttrs_);
  OpMethodName(method_log_set_xttrs_, "logSetXttrs");

  method_log_remove_xattrs_ =
      env->GetMethodID(klass, "LogRemoveXattrs", "([BZ)J");
  CHECK(method_log_remove_xattrs_);
  OpMethodName(method_log_remove_xattrs_, "logRemoveXattrs");

  method_log_access_counters_snapshot_ =
      env->GetMethodID(klass, "logAccessCounterSnapshot", "([B)J");
  CHECK(method_log_access_counters_snapshot_);
  OpMethodName(method_log_access_counters_snapshot_,
               "logAccessCounterSnapshot");

  method_log_set_cfs_universal_info_ =
      env->GetMethodID(klass, "logSetCfsUniversalInfo", "([B)J");
  CHECK(method_log_set_cfs_universal_info_);
  OpMethodName(method_log_set_cfs_universal_info_, "logSetCfsUniversalInfo");

  /* TODO(xuex)
   * need implement int DanceNNEditLogContext.java
  method_log_allow_snapshot_op_ =
      env->GetMethodID(klass, "logAllowSnapshot", "(Ljava/lang/String;)J");
  CHECK(method_log_allow_snapshot_op_);
  OpMethodName(method_log_allow_snapshot_op_, "logAllowSnapshot");

  method_log_disallow_snapshot_op_ = env->GetMethodID(klass,
  "logDisallowSnapshot", "([B)J"); CHECK(method_log_disallow_snapshot_op_);
  OpMethodName(method_log_disallow_snapshot_op_, "logDisallowSnapshot");

  method_log_create_snapshot_op_ = env->GetMethodID(klass, "logCreateSnapshot",
  "([B)J"); CHECK(method_log_create_snapshot_op_);
  OpMethodName(method_log_create_snapshot_op_, "logCreateSnapshot");

  method_log_delete_snapshot_op_ = env->GetMethodID(klass, "logDeleteSnapshot",
  "([B)J"); CHECK(method_log_delete_snapshot_op_);
  OpMethodName(method_log_delete_snapshot_op_, "logDeleteSnapshot");

  method_log_rename_snapshot_op_ = env->GetMethodID(klass, "logRenameSnapshot",
  "([B)J"); CHECK(method_log_rename_snapshot_op_);
  OpMethodName(method_log_rename_snapshot_op_, "logRenameSnapshot");
  */

  method_log_cfs_op_ = env->GetMethodID(klass, "logCfsOp", "([B)J");
  CHECK(method_log_cfs_op_);
  OpMethodName(method_log_cfs_op_, "logCfsOp");

  method_set_last_allocated_block_id_ =
      env->GetMethodID(klass, "setLastAllocatedBlockID", "(J)V");
  CHECK(method_set_last_allocated_block_id_);
  OpMethodName(method_set_last_allocated_block_id_, "setLastAllocatedBlockID");

  method_set_last_generation_stamp_v2_ =
      env->GetMethodID(klass, "setLastGenerationStampV2", "(J)V");
  CHECK(method_set_last_generation_stamp_v2_);
  OpMethodName(method_set_last_generation_stamp_v2_,
               "setLastGenerationStampV2");
}

HAEditLogContext::~HAEditLogContext() {
  StopBGEditLogWorker();
  CloseInternal();
  if (jvm_ == nullptr) {
    CHECK(FLAGS_run_ut);
  } else {
    jvm_->env()->DeleteGlobalRef(class_input_context_);
  }
}

void HAEditLogContext::OpenForRead() {  // no edit log write
  jvm_->env()->CallVoidMethod(java_obj_->ref(), method_open_for_read_);
  is_open_for_write_ = false;
}

int64_t HAEditLogContext::OpenForWrite() {  // write OP_START and flush stream
  auto task = new EditLogTask(method_open_for_write_, "", false, false);
  bg_edit_log_committer_->Commit(task);
  task->Await();
  auto txid = task->Txid();
  delete task;
  CHECK(jvm_->env()->CallBooleanMethod(java_obj_->ref(),
                                       method_is_open_for_write_));
  is_open_for_write_ = true;

  if (!FLAGS_run_ut) {
    CHECK_EQ(jvm_->env()->CallBooleanMethod(java_obj_->ref(),
                                            method_use_bk_editlog_os_),
             FLAGS_use_bk_editlog_os);
    LOG(INFO) << "Use bookkeeper editlog output stream: "
              << FLAGS_use_bk_editlog_os;
  }

  return txid;
}

void HAEditLogContext::Close() {
  if (IsOpenForWrite()) {
    auto task = std::make_unique<EditLogTask>(method_close_,
                                              /*msg=*/"",
                                              /*with_rpc_id=*/false,
                                              /*to_log_rpc_ids=*/false);
    bg_edit_log_committer_->Commit(task.get());
    task->Await();
    CHECK(!jvm_->env()->CallBooleanMethod(java_obj_->ref(),
                                          method_is_open_for_write_));
    is_open_for_write_ = false;
  } else if (IsOpenForRead()) {
    CloseInternal();
  } else {
    LOG(INFO) << "Ignore HAEditLogContext::Close";
  }
}

int64_t HAEditLogContext::CloseInternal() {
  if (jvm_ == nullptr) {
    CHECK(FLAGS_run_ut);
    return 0;
  }
  // write OP_END and flush stream
  // method_close is very very special and ugly
  // 1. method_close mean close current edit_log_context, it
  // will write OP_END and close current BK segment
  // 2. only call method_close at dancenn exit, or standby to
  // active, or active to standby
  //   a. standby to active or active to standby use
  //   exclusive lock, ensure no other writer
  //   b. exit will drop record no write to BK
  // 3. for above reason and simple, we don't sync txid
  // 4. txid will reload from BK at next time
  jvm_->env()->CallVoidMethod(java_obj_->ref(), method_close_);
  THROW_JVM_EXCEPTION(jvm_);
  auto txid = GetLastWrittenTxId();
  THROW_JVM_EXCEPTION(jvm_);
  return txid;
}

bool HAEditLogContext::IsActiveInLease() const {
  auto last_sync_time = finish_sync_time_.load();

  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
                 std::chrono::steady_clock::now().time_since_epoch())
                 .count();
  return now < last_sync_time + FLAGS_edit_log_last_sync_stale_interval_ms;
}

bool HAEditLogContext::IsOpenForRead() {  // just memory check
  return jvm_->env()->CallBooleanMethod(java_obj_->ref(),
                                        method_is_open_for_read_);
}

// CLI threads should avoid directly calling
// `DanceNNEditLogContext::isOpenForWrite`. Doing so can lead to a deadlock if
// the zk/bk clusters are unavailable and the decision is made to switch the
// namenode to non-HA mode. Consider the following scenario:
//
// - Thread `BGEditLogBCT`: Executes `BGEditLogCommitter::operator() ->
//   HAEditLogContext::CallJavaOpMethod -> DanceNNEditLogContext::logCfsOp ->
//   FSEditLog::logEdit -> FSEditLog::logEditInternal`. `logEditInternal` is
//   synchronized, and will block if the zk/bk cluster is down.
// - Thread `cli-1-wk-1`: Executes `EditLogSender::LogOpenFileV2` (while holding
//   the read lock of `HAState::barrier_`) -> `HAEditLogContext::IsOpenForWrite
//   -> DanceNNEditLogContext::isOpenForWrite -> FSEditLog::isOpenForWrite`.
//   `isOpenForWrite` is synchronized and will block due to the blocking of
//   thread `BGEditLogBCT`.
// - Thread `http-worker-0`: Executes `DancennAdminHandler::Handle ->
//   DancennAdminHandler::SwitchHAActiveToNonHAActive ->
//   HAState::SwitchHAActiveToNonHAActive ->
//   HAFlexibleEditLogContext::SwitchHAActiveToNonHAActive ->
//   SwitchOp::operator()
//   -> TwoStepVUniqueLock::~TwoStepVUniqueLock -> TwoStepVLock::Lock ->
//   sched_yield`. This thread attempts to acquire the write lock of
//   `HAState::barrier_`, but is blocked by thread `cli-1-wk-1`.
bool HAEditLogContext::IsOpenForWrite() {  // just memory check
  return is_open_for_write_;
}

EditLogConf::HAMode HAEditLogContext::GetHAMode() {
  return EditLogConf::HA;
}

bool HAEditLogContext::UpdateConfProperty(const std::string& name,
                                          const std::string& value) {
  LOG(INFO) << "UpdateConfProperty"
            << " name=" << name << " value=" << value;
  std::stringstream ss;
  WriteField(&name, &ss);
  WriteField(&value, &ss);

  std::string msg = ss.str();
  jbyteArray java_msg = jvm_->env()->NewByteArray(msg.size());
  jvm_->env()->SetByteArrayRegion(
      java_msg,
      0,
      msg.size(),
      reinterpret_cast<const signed char*>(msg.c_str()));

  auto ret = jvm_->env()->CallBooleanMethod(
      java_obj_->ref(), method_update_conf_property_, java_msg);
  return ret;
}

#define WATCH_SYNC(op)                                                  \
  {                                                                     \
    StopWatch sw;                                                       \
    sw.Start();                                                         \
    op;                                                                 \
    sw.NextStep();                                                      \
    MFH(edit_sync_time_)                                                \
        ->Update(std::chrono::duration_cast<std::chrono::microseconds>( \
                     sw.GetTime())                                      \
                     .count());                                         \
  }

void HAEditLogContext::StartSync() {
  std::lock_guard<std::mutex> guard(sync_mutex_);

  if (sync_onfly_.fetch_add(1) == 0) {
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
                   std::chrono::steady_clock::now().time_since_epoch())
                   .count();
    start_sync_time_ = now;
  }
}

void HAEditLogContext::FinishSync() {
  std::lock_guard<std::mutex> guard(sync_mutex_);

  if (sync_onfly_.fetch_sub(1) == 1) {
    start_sync_time_ = 0;
  }
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
                 std::chrono::steady_clock::now().time_since_epoch())
                 .count();
  finish_sync_time_ = now;
}

int64_t HAEditLogContext::GetWaitSyncTime() {
  auto start_time = start_sync_time_.load();
  if (sync_onfly_.load() && start_time) {
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
                   std::chrono::steady_clock::now().time_since_epoch())
                   .count();
    return now - start_time;
  } else {
    return 0;
  }
}

void HAEditLogContext::LogSyncInternal() {
  // no write OP_XX just flush stream
  StartSync();
  WATCH_SYNC((jvm_->env()->CallVoidMethod(java_obj_->ref(), method_log_sync_)));
  FinishSync();
  THROW_JVM_EXCEPTION(jvm_);
}

void HAEditLogContext::LogSync(bool force) {
  if (!force) return;

  auto task = new EditLogTask(method_log_sync_, "", false, false);
  bg_edit_log_committer_->Commit(task);

  task->Await();
  delete task;
}

void HAEditLogContext::LogSyncAllInternal() {
  // no write OP_XX just flush stream
  StartSync();
  WATCH_SYNC(
      (jvm_->env()->CallVoidMethod(java_obj_->ref(), method_log_sync_all_)));
  FinishSync();
  THROW_JVM_EXCEPTION(jvm_);
}

void HAEditLogContext::LogSyncAll() {
  auto task = new EditLogTask(method_log_sync_all_, "", false, false);
  bg_edit_log_committer_->Commit(task);

  task->Await();
  delete task;
}

void HAEditLogContext::InitJournalsForWrite() {
  // memory op?
  LOG(INFO) << "Enter InitJournalsForWrite";
  DEFER([&] { LOG(INFO) << "Leave InitJournalsForWrite"; });
  jvm_->env()->CallVoidMethod(java_obj_->ref(),
                              method_init_journals_for_write_);
  auto next_txid = GetLastWrittenTxId() + 1;
  CHECK_JVM_EXCEPTION(jvm_);
  LOG(INFO) << "SetNextTxId: " << next_txid;
  CHECK(bg_edit_log_committer_);
  bg_edit_log_committer_->SetNextTxId(next_txid);
  CHECK(bg_edit_log_syncer_);
  bg_edit_log_syncer_->SetNextTxId(next_txid);
}

void HAEditLogContext::SetLastAllocatedBlockId(int64_t id) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    last_allocated_block_id_.store(id);
    return;
  }

  // memory op
  jvm_->env()->CallVoidMethod(
      java_obj_->ref(), method_set_last_allocated_block_id_, id);
  CHECK(bg_edit_log_committer_);
  bg_edit_log_committer_->SetLastAllocatedBlockId(id);
}

void HAEditLogContext::SetLastGenerationStampV2(int64_t gsv2) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    last_generation_stamp_v2_.store(gsv2);
    return;
  }

  // memory op
  jvm_->env()->CallVoidMethod(
      java_obj_->ref(), method_set_last_generation_stamp_v2_, gsv2);
  CHECK(bg_edit_log_committer_);
  bg_edit_log_committer_->SetLastGenerationStampV2(gsv2);
}

void HAEditLogContext::SetNextTxId(int64_t tx_id) {
  // memory op
  LOG(INFO) << "SetNextTxId: " << tx_id;
  jvm_->env()->CallVoidMethod(java_obj_->ref(), method_set_next_tx_id_, tx_id);
  CHECK_JVM_EXCEPTION(jvm_);
  CHECK(bg_edit_log_committer_);
  bg_edit_log_committer_->SetNextTxId(tx_id);
  CHECK(bg_edit_log_syncer_);
  bg_edit_log_syncer_->SetNextTxId(tx_id);
}

uint64_t HAEditLogContext::GetLastAllocatedBlockId() {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    CHECK_NE(last_allocated_block_id_, kInvalidBlockID);
    return last_allocated_block_id_;
  }
  // memory op
  return jvm_->env()->CallLongMethod(java_obj_->ref(),
                                     method_get_last_allocated_block_id_);
}

uint64_t HAEditLogContext::GetLastGenerationStampV2() {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    CHECK_NE(last_generation_stamp_v2_, kInvalidGenerationStamp);
    return last_generation_stamp_v2_;
  }
  // memory op
  return jvm_->env()->CallLongMethod(java_obj_->ref(),
                                     method_get_last_generation_stamp_v2_);
}

int64_t HAEditLogContext::GetCurSegmentTxId() {
  // memory op
  return jvm_->env()->CallLongMethod(java_obj_->ref(),
                                     method_get_cur_segment_tx_id_);
}

int64_t HAEditLogContext::GetLastWrittenTxId() {
  // memory op
  return jvm_->env()->CallLongMethod(java_obj_->ref(),
                                     method_get_last_written_tx_id_);
}

int64_t HAEditLogContext::RollEditLog() {
  // at future we no need any lock here
  // OP_END -> flush stream -> close ledger
  //        -> create ledger -> OP_START -> flush stream
  // here we must use exclusive lock for protecting
  // OP_END + OP_START and no OPs write to closed stream
  // IMPORTANT: if want remove this ReadWriteLock,
  // please give RollEditLog specify channel with LockedTryPush
  auto task = new EditLogTask(method_roll_edit_log_, "", false, false);
  bg_edit_log_committer_->Commit(task, true);

  task->Await();
  auto txid = task->Txid();
  delete task;

  return txid;
}

void HAEditLogContext::PurgeLogsOlderThan(int64_t min_tx_id_to_keep) {
  // memory op?
  jvm_->env()->CallVoidMethod(
      java_obj_->ref(), method_purge_logs_older_than_, min_tx_id_to_keep);
}

bool HAEditLogContext::GetAllStackTraces(std::string* stack_info) {
  jbyteArray input_array =
      static_cast<jbyteArray>(jvm_->env()->CallObjectMethod(
          java_obj_->ref(), method_get_all_stack_traces_));
  CHECK_JVM_EXCEPTION(jvm_);
  auto len = static_cast<size_t>(jvm_->env()->GetArrayLength(input_array));
  stack_info->reserve(len);
  jvm_->env()->GetByteArrayRegion(
      input_array, 0, len, reinterpret_cast<jbyte*>(&(*stack_info)[0]));
  stack_info->resize(len);
  jvm_->env()->DeleteLocalRef(input_array);
  return true;
}

bool HAEditLogContext::GetPeerNNAddr(std::string* addr) {
  jbyteArray input_array =
      static_cast<jbyteArray>(jvm_->env()->CallObjectMethod(
          java_obj_->ref(), method_get_peer_nn_addr_));
  CHECK_JVM_EXCEPTION(jvm_);
  auto len = static_cast<size_t>(jvm_->env()->GetArrayLength(input_array));
  char tmp[len + 1];
  jvm_->env()->GetByteArrayRegion(
      input_array, 0, len, reinterpret_cast<jbyte*>(tmp));
  addr->assign(tmp, len);
  jvm_->env()->DeleteLocalRef(input_array);
  return true;
}

std::unique_ptr<EditLogInputContextBase> HAEditLogContext::CreateInputContext(
    int64_t from_txid,
    int64_t to_at_least_txid,
    bool is_progress_ok) {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    MFH(create_input_context_time_)
        ->Update(
            std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
                .count());
    MFC(create_input_context_num_)->Inc();
  });

  jobject input_context =
      jvm_->env()->CallObjectMethod(java_obj_->ref(),
                                    method_create_input_context_,
                                    from_txid,
                                    to_at_least_txid,
                                    is_progress_ok);
  CHECK_JVM_EXCEPTION(jvm_);
  auto ctx = std::unique_ptr<EditLogInputContextBase>(
      new HAEditLogInputContext(jvm_, class_input_context_, input_context));
  jvm_->env()->DeleteLocalRef(input_context);
  return ctx;
}

int64_t HAEditLogContext::CallJavaOpVoidMethodL(jmethodID method) {
  auto ret = jvm_->env()->CallLongMethod(java_obj_->ref(), method);
  THROW_JVM_EXCEPTION(jvm_);
  return ret;
}

// TODO(ruanjunbin): Why java method need with_rpc_id and to_log_rpc_ids?
int64_t HAEditLogContext::CallJavaOpMethod(jmethodID method,
                                           const std::string& msg,
                                           bool with_rpc_id,
                                           bool to_log_rpc_ids) {
  jbyteArray op_msg = jvm_->env()->NewByteArray(msg.size());
  jvm_->env()->SetByteArrayRegion(
      op_msg, 0, msg.size(), reinterpret_cast<const signed char*>(msg.c_str()));

  if (with_rpc_id) {
    auto ret = jvm_->env()->CallLongMethod(
        java_obj_->ref(), method, op_msg, to_log_rpc_ids);
    THROW_JVM_EXCEPTION(jvm_);
    jvm_->env()->DeleteLocalRef(op_msg);
    return ret;
  }

  auto ret = jvm_->env()->CallLongMethod(java_obj_->ref(), method, op_msg);
  THROW_JVM_EXCEPTION(jvm_);
  jvm_->env()->DeleteLocalRef(op_msg);
  return ret;
}

int64_t HAEditLogContext::CallJavaOpMethod(jmethodID method,
                                           const std::stringstream* ss,
                                           bool with_rpc_id,
                                           bool to_log_rpc_ids) {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    MFH(edit_log_time_)
        ->Update(
            std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
                .count());
  });

  std::string msg = ss->str();
  LogEditLogTooLargeError(msg);
  auto task = new EditLogTask(method, msg, with_rpc_id, to_log_rpc_ids);
  bg_edit_log_committer_->Commit(task);
  task->Await();
  int64_t txid = task->Txid();
  delete task;
  return txid;
}

int64_t HAEditLogContext::CallJavaOpMethodLLLL(
    jmethodID method,
    const std::stringstream* blkid_ss,
    const std::stringstream* gsv2_ss,
    uint64_t* blkid,
    uint64_t* gsv2) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(FATAL) << "unreachable code";
  }

  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    MFH(edit_log_time_)
        ->Update(
            std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
                .count());
  });

  auto task = new EditLogTask(method, blkid_ss->str(), false, false);
  task->ExtraMsg(gsv2_ss->str());
  bg_edit_log_committer_->Commit(task);
  task->Await();

  int64_t txid = task->Txid();
  *blkid = task->Value();
  *gsv2 = task->ExtraValue();
  delete task;
  return txid;
}

int64_t HAEditLogContext::CallJavaOpMethodLL(jmethodID method,
                                             const std::stringstream* ss,
                                             uint64_t* value) {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    MFH(edit_log_time_)
        ->Update(
            std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
                .count());
  });

  std::string msg = ss->str();
  LogEditLogTooLargeError(msg);
  auto task = new EditLogTask(method, msg, false, false);
  bg_edit_log_committer_->Commit(task);
  task->Await();

  int64_t txid = task->Txid();
  *value = task->Value();
  delete task;
  return txid;
}

int64_t HAEditLogContext::CallJavaOpMethodLL(jmethodID method,
                                             const std::string& msg,
                                             uint64_t* value) {
  jbyteArray op_msg = jvm_->env()->NewByteArray(msg.size());
  jvm_->env()->SetByteArrayRegion(
      op_msg, 0, msg.size(), reinterpret_cast<const signed char*>(msg.c_str()));

  auto r = static_cast<jbyteArray>(
      jvm_->env()->CallObjectMethod(java_obj_->ref(), method, op_msg));
  THROW_JVM_EXCEPTION(jvm_);
  jvm_->env()->DeleteLocalRef(op_msg);

  auto len = jvm_->env()->GetArrayLength(r);
  CHECK_EQ(len, 16);

  char buf[17];
  jvm_->env()->GetByteArrayRegion(r, 0, 16, reinterpret_cast<jbyte*>(buf));
  jvm_->env()->DeleteLocalRef(r);

  int64_t txid = platform::ReadBigEndian<uint64_t>(buf, 0);
  *value = platform::ReadBigEndian<uint64_t>(buf, 8);
  return txid;
}

int64_t HAEditLogContext::LogOpenFile(const std::stringstream* ss,
                                      bool to_log_rpc_ids) {
  // write OP_ADD
  return CallJavaOpMethod(method_log_open_file_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogCloseFile(const std::stringstream* ss) {
  // write OP_CLOSE
  return CallJavaOpMethod(method_log_close_file_, ss);
}

int64_t HAEditLogContext::LogAddBlock(const std::stringstream* ss) {
  // write OP_ADD_BLOCK
  return CallJavaOpMethod(method_log_add_block_, ss);
}

int64_t HAEditLogContext::LogUpdateBlocks(const std::stringstream* ss,
                                          bool to_log_rpc_ids) {
  // write OP_UPDATE_BLOCKS
  return CallJavaOpMethod(method_log_update_blocks_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogMkDir(const std::stringstream* ss) {
  // write OP_MKDIR
  return CallJavaOpMethod(method_log_mk_dir_, ss);
}

int64_t HAEditLogContext::LogRenameOld(const std::stringstream* ss,
                                       bool to_log_rpc_ids) {
  // write OP_RENAME_OLD
  return CallJavaOpMethod(method_log_rename_old_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogRename(const std::stringstream* ss,
                                    bool to_log_rpc_ids) {
  // write OP_RENAME
  return CallJavaOpMethod(method_log_rename_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogSetReplication(const std::stringstream* ss) {
  // write OP_SET_REPLICATION
  return CallJavaOpMethod(method_log_set_replication_, ss);
}

int64_t HAEditLogContext::LogSetStoragePolicy(const std::stringstream* ss) {
  // write OP_SET_STORAGE_POLICY
  return CallJavaOpMethod(method_log_set_storage_policy_, ss);
}

int64_t HAEditLogContext::LogSetReplicaPolicy(const std::stringstream* ss) {
  // write OP_SET_REPLICA_POLICY
  return CallJavaOpMethod(method_log_set_replica_policy_, ss);
}

int64_t HAEditLogContext::LogSetDirReplicaPolicy(const std::stringstream* ss) {
  // write OP_SET_DIR_REPLICA_POLICY
  return CallJavaOpMethod(method_log_set_dir_replica_policy_, ss);
}

int64_t HAEditLogContext::LogSetQuota(const std::stringstream* ss) {
  // write OP_SET_NS_QUOTA
  return CallJavaOpMethod(method_log_set_quota_, ss);
}

int64_t HAEditLogContext::LogSetPermissions(const std::stringstream* ss) {
  // write OP_SET_PERMISSIONS
  return CallJavaOpMethod(method_log_set_permission_, ss);
}

int64_t HAEditLogContext::LogSetOwner(const std::stringstream* ss) {
  // write OP_SET_OWNER
  return CallJavaOpMethod(method_log_set_owner_, ss);
}

int64_t HAEditLogContext::LogConcat(const std::stringstream* ss,
                                    bool to_log_rpc_ids) {
  // write OP_CONCAT_DELETE
  return CallJavaOpMethod(method_log_concat_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogDelete(const std::stringstream* ss,
                                    bool to_log_rpc_ids) {
  // write OP_DELETE
  return CallJavaOpMethod(method_log_delete_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogGenerationStampV1(const std::stringstream* ss) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(FATAL) << "FLAGS_enable_fast_block_id_and_gs_gen=true, should not call "
                  "LogGenerationStampV1";
  }
  // write OP_SET_GENSTAMP_V1
  return CallJavaOpMethod(method_log_generation_stamp_v1_, ss);
}

int64_t HAEditLogContext::LogGenerationStampV2(const std::stringstream* ss,
                                               uint64_t* gsv2) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    *gsv2 = last_generation_stamp_v2_.fetch_add(1) + 1;

    return kInvalidTxId;
  }

  // write OP_SET_GENSTAMP_V2
  return CallJavaOpMethodLL(method_log_generation_stamp_v2_, ss, gsv2);
}

int64_t HAEditLogContext::LogAllocateBlockId(const std::stringstream* ss,
                                             uint64_t* id) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    *id = last_allocated_block_id_.fetch_add(1) + 1;

    return kInvalidTxId;
  }

  // write OP_ALLOCATE_BLOCK_ID
  return CallJavaOpMethodLL(method_log_allocate_block_id_, ss, id);
}

int64_t HAEditLogContext::LogAllocateBlockIdAndGSv2(
    const std::stringstream* blkid_ss,
    const std::stringstream* gsv2_ss,
    uint64_t* blkid,
    uint64_t* gsv2) {
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    *blkid = last_allocated_block_id_.fetch_add(1) + 1;
    *gsv2 = last_generation_stamp_v2_.fetch_add(1) + 1;

    return kInvalidTxId;
  }

  // write OP_ALLOCATE_BLOCK_ID then OP_SET_GENSTAMP_V2
  return CallJavaOpMethodLLLL(
      method_log_allocate_block_id_, blkid_ss, gsv2_ss, blkid, gsv2);
}

int64_t HAEditLogContext::LogTimes(const std::stringstream* ss) {
  // write OP_TIMES
  return CallJavaOpMethod(method_log_times_, ss);
}

int64_t HAEditLogContext::LogSymlink(const std::stringstream* ss,
                                     bool to_log_rpc_ids) {
  // write OP_SYMLINK
  return CallJavaOpMethod(method_log_symlink_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogReassignLease(const std::stringstream* ss) {
  // write OP_REASSIGN_LEASE
  return CallJavaOpMethod(method_log_reassign_lease_, ss);
}

int64_t HAEditLogContext::LogSetAcl(const std::stringstream* ss) {
  // write OP_SET_ACL
  return CallJavaOpMethod(method_log_set_acl_, ss);
}

int64_t HAEditLogContext::LogSetXAttrs(const std::stringstream* ss,
                                       bool to_log_rpc_ids) {
  // write OP_SET_XATTR
  return CallJavaOpMethod(method_log_set_xttrs_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogRemoveXAttrs(const std::stringstream* ss,
                                          bool to_log_rpc_ids) {
  // write OP_REMOVE_XATTR
  return CallJavaOpMethod(method_log_remove_xattrs_, ss, true, to_log_rpc_ids);
}

int64_t HAEditLogContext::LogAccessCounterSnapshot(
    const std::stringstream* ss) {
  // write OP_ACCESS_COUNTER_SNAPSHOT
  return CallJavaOpMethod(method_log_access_counters_snapshot_, ss);
}

int64_t HAEditLogContext::LogSetBlockPufsInfo(const std::stringstream* ss) {
  return CallJavaOpMethod(method_log_set_cfs_universal_info_, ss);
}

int64_t HAEditLogContext::LogDeleteDeprecatedBlockPufsInfo(
    const std::stringstream* ss) {
  return CallJavaOpMethod(method_log_set_cfs_universal_info_, ss);
}

int64_t HAEditLogContext::LogAllowSnapshot(const std::stringstream* ss) {
  // TODO(xuex)
  return kInvalidTxId;
  // return CallJavaOpMethod();
}

int64_t HAEditLogContext::LogDisallowSnapshot(const std::stringstream* ss) {
  // TODO(xuex)
  return kInvalidTxId;
  // return CallJavaOpMethod(method_log_disallow_snapshot_, ss);
}

int64_t HAEditLogContext::LogCreateSnapshot(const std::stringstream* ss) {
  // TODO(xuex)
  return kInvalidTxId;
  // return CallJavaOpMethod(method_log_create_snapshot_, ss);
}

int64_t HAEditLogContext::LogDeleteSnapshot(const std::stringstream* ss) {
  // TODO(xuex)
  return kInvalidTxId;
  // return CallJavaOpMethod(method_log_delete_snapshot_, ss);
}

int64_t HAEditLogContext::LogRenameSnapshot(const std::stringstream* ss) {
  // TODO(xuex)
  return kInvalidTxId;
  // return CallJavaOpMethod(method_log_rename_snapshot_, ss);
}

int64_t HAEditLogContext::LogCfsOp(const std::stringstream* ss) {
  return CallJavaOpMethod(method_log_cfs_op_, ss);
}

void HAEditLogContext::StartBGEditLogWorker(int max_producers) {
  if (is_bg_worker_running_.exchange(true)) {
    LOG(ERROR)
        << "HAEditLogContext::StartBGEditLogWorker is called more than once";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }
  {
    CHECK(!bg_edit_log_sync_worker_.get());
    bg_edit_log_syncer_.reset(
        new BGEditLogSyncer(this, (max_producers + 1) * 20));
    auto task = std::static_pointer_cast<cnetpp::concurrency::Task>(
        bg_edit_log_syncer_);
    bg_edit_log_sync_worker_.reset(
        new cnetpp::concurrency::Thread(task, "BGEditLogSync"));
    bg_edit_log_sync_worker_->Start();
  }
  {
    CHECK(!bg_edit_log_commit_worker_.get());
    bg_edit_log_committer_.reset(
        new BGEditLogCommitter(max_producers, this, bg_edit_log_syncer_));
    auto task = std::static_pointer_cast<cnetpp::concurrency::Task>(
        bg_edit_log_committer_);
    bg_edit_log_commit_worker_.reset(
        new cnetpp::concurrency::Thread(task, "BGEditLogBCT"));
    bg_edit_log_commit_worker_->Start();
  }
}

void HAEditLogContext::StopBGEditLogWorker() {
  // Under normal conditions, StartBGEditLogWorker and StopBGEditLogWorker are
  // each invoked exactly once by the constructor and destructor respectively.
  // However, during a transition from HA to nonHA, StopBGEditLogWorker may be
  // called multiple times.
  // if (!is_bg_worker_running_.exchange(false)) {
  //   LOG(ERROR)
  //       << "HAEditLogContext::StopBGEditLogWorker is called more than once";
  //   MFC(LoggerMetrics::Instance().error_)->Inc();
  //   return;
  // }
  if (bg_edit_log_commit_worker_.get()) {
    bg_edit_log_commit_worker_->Stop();
    bg_edit_log_commit_worker_.reset();
  }
  if (bg_edit_log_sync_worker_.get()) {
    bg_edit_log_sync_worker_->Stop();
    bg_edit_log_sync_worker_.reset();
  }
}

EditLogConf::PreviousEditLogConf HAEditLogContext::HASwitchFence() {
  StopWatch sw;
  sw.Start();

  CHECK(bg_edit_log_committer_);
  bg_edit_log_committer_->Stop();
  int64_t committer_pending_begin_txid = kInvalidTxId;
  int64_t committer_pending_end_txid = kInvalidTxId;
  uint64_t last_allocated_block_id = kInvalidBlockID;
  uint64_t last_generation_stamp_v2 = kGrandfatherGenerationStamp;
  bg_edit_log_committer_->HASwitchFence(&committer_pending_begin_txid,
                                        &committer_pending_end_txid,
                                        &last_allocated_block_id,
                                        &last_generation_stamp_v2);
  CHECK_LE(committer_pending_begin_txid, committer_pending_end_txid);
  LOG(INFO) << "BGEditLogCommitter::HASwitchFence time(us): "
            << sw.NextStepTime();

  CHECK(bg_edit_log_syncer_);
  bg_edit_log_syncer_->Stop();
  int64_t syncer_pending_begin_txid = kInvalidTxId;
  int64_t syncer_pending_end_txid = kInvalidTxId;
  bg_edit_log_syncer_->HASwitchFence(&syncer_pending_begin_txid,
                                     &syncer_pending_end_txid);
  CHECK_LE(syncer_pending_begin_txid, syncer_pending_end_txid);
  LOG(INFO) << "BGEditLogSyncer::HASwitchFence time(us): " << sw.NextStepTime();

  // `syncer_pending_begin_txid` cannot be larger than
  // `committer_pending_begin_txid + 1` as the syncer is unable to perform
  // synchronization of a `txid` until the committer invokes
  // `BGEditLogSyncer::Sync` on it.
  //
  // We state `syncer_pending_begin_txid <= committer_pending_begin_txid + 1`
  // instead of `syncer_pending_begin_txid <= committer_pending_begin_txid`.
  // This is because `BGEditLogCommitter::operator()` invokes
  // `BGEditLogSyncer::Sync` before calling `BGEditLogCommitter::TxFinish`. This
  // sequence may lead to a situation where at most one task is synchronized to
  // the syncer, but is not recorded by the committer.
  CHECK_LE(syncer_pending_begin_txid, committer_pending_begin_txid + 1);
  CHECK_LE(syncer_pending_end_txid, committer_pending_end_txid);
  // The fence mechanism is required to ensure that `syncer_pending_end_txid` is
  // greater than or equal to `committer_pending_begin_txid`. If this condition
  // is not met, tasks with a `txid` in the non-empty range of
  // [`syncer_pending_end_txid`, `committer_pending_begin_txid`) will not be
  // present in either the syncer buffer or the committer buffer.
  //
  // --- syncer_pending_begin_txid
  // ... (tasks present in the syncer buffer)
  // --- syncer_pending_end_txid
  // ... [tasks absent from both buffers]
  // --- committer_pending_begin_txid
  // ... (tasks present in the committer buffer)
  // --- committer_pending_end_txid
  CHECK_GE(syncer_pending_end_txid, committer_pending_begin_txid);
  int64_t sc_pending_begin_txid = syncer_pending_begin_txid;
  int64_t sc_pending_end_txid = committer_pending_end_txid;
  CHECK_LE(sc_pending_begin_txid, sc_pending_end_txid);

  // When the BookKeeper cluster is down,
  // BookKeeperEditLogOutputStream::flushAndSync may become indefinitely
  // blocked. This can lead to a endless halt of the FSEditLog::logSync
  // function, subsequently causing the BGEditLogSyncer::operator() function to
  // remain blocked without end.
  //
  // When the BookKeeper cluster is down, the BGEditLogSyncer::operator()
  // function may come to a halt. This can cause the BGEditLogSyncer::channel_
  // to become full, which in turn can lead to a halt in
  // BGEditLogCommitter::operator() when calling BGEditLogSyncer::Sync.
  std::thread stop_worker_thd([this]() { StopBGEditLogWorker(); });
  LOG(INFO) << "stop_worker_thd: "
            << std::hash<std::thread::id>()(stop_worker_thd.get_id());
  stop_worker_thd.detach();

  EditLogConf::PreviousEditLogConf conf;
  conf.set_pending_begin_txid(sc_pending_begin_txid);
  conf.set_pending_end_txid(sc_pending_end_txid);
  // The editlogs may be in transit over the network; hence, their
  // reuse should be avoided to prevent potential conflicts or errors.
  // These empty WriteBatches serve as guards,
  // a concept analogous to the "guard pages" in Linux.
  const int64_t reserved_txid_gap =
      100 /*(q/ms)*/ * 1000 /*(ms/s)*/ * 5 /*(s)*/;
  conf.set_reserved_begin_txid(sc_pending_end_txid);
  conf.set_reserved_end_txid(sc_pending_end_txid + reserved_txid_gap);
  conf.set_last_allocated_block_id(last_allocated_block_id);
  conf.set_last_generation_stamp_v2(last_generation_stamp_v2);
  conf.set_is_ha_edit_log_conf(true);
  EditLogConf::PreviousEditLogConf::HAEditLogConf* ha_conf =
      conf.mutable_ha_edit_log_conf();
  ha_conf->set_syncer_pending_begin_txid(syncer_pending_begin_txid);
  ha_conf->set_syncer_pending_end_txid(syncer_pending_end_txid);
  ha_conf->set_committer_pending_begin_txid(committer_pending_begin_txid);
  ha_conf->set_committer_pending_end_txid(committer_pending_end_txid);
  return conf;
}

Status HAEditLogContext::SwitchNonHAActiveToHAActive() {
  LOG(ERROR) << "HAEditLogContext::SwitchNonHAActiveToHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

Status HAEditLogContext::SwitchHAActiveToNonHAActive() {
  LOG(ERROR) << "HAEditLogContext::SwitchHAActiveToNonHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

Status HAEditLogContext::SwitchHAStandbyToNonHAActive() {
  LOG(ERROR) << "HAEditLogContext::SwitchHAStandbyToNonHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

std::string HAEditLogContext::OpMethodName(jmethodID method) const {
  auto itr = op_method_names_.find(reinterpret_cast<uint64_t>(method));
  if (itr == op_method_names_.end()) {
    return "Unknown";
  }
  return itr->second;
}

void HAEditLogContext::OpMethodName(jmethodID method, const std::string& name) {
  op_method_names_.emplace(reinterpret_cast<uint64_t>(method), name);
}

}  // namespace dancenn
