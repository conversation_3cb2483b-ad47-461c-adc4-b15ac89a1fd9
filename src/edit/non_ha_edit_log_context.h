// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/10/30
// Description

#ifndef EDIT_NON_HA_EDIT_LOG_CONTEXT_H_
#define EDIT_NON_HA_EDIT_LOG_CONTEXT_H_

#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <cstdint>  // For int64_t, etc.
#include <memory>   // For unique_ptr, shared_ptr.
#include <mutex>    // For mutex, lock_guard.
#include <sstream>  // For stringstream.
#include <string>   // For string.

#include "base/status.h"                  // For Status, Code.
#include "block_manager/block.h"          // For BlockID.
#include "edit/edit_log_context_base.h"   // For EditLogContextBase.
#include "edit/edit_log_sync_listener.h"  // For IEditLogSyncListener.

namespace dancenn {

class NonHAEditLogContext : public EditLogContextBase {
 public:
  NonHAEditLogContext();
  virtual ~NonHAEditLogContext() = default;

  // Common methods.
  EditLogConf::HAMode GetHAMode() override;
  bool UpdateConfProperty(const std::string& name,
                          const std::string& value) override;
  void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) override;
  std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() override;
  bool GetPeerNNAddr(std::string* addr) override;
  bool GetAllStackTraces(std::string* stack_info) override;

  // see EditLogContextBase::IsActiveInLease
  // useless in NonHAEditLogContext
  bool IsActiveInLease() const override {
    return true;
  }

  // Read related.
  bool IsOpenForRead() override;
  void OpenForRead() override;
  std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid,
      int64_t to_at_least_txid,
      bool is_progress_ok) override;
  // Write related.
  bool IsOpenForWrite() override;
  void InitJournalsForWrite() override;
  int64_t OpenForWrite() override;
  // Close.
  void Close() override;

  // TxID, BlockID and GS related.
  int64_t GetLastWrittenTxId() override;
  void SetNextTxId(int64_t txid) override;
  uint64_t GetLastAllocatedBlockId() override;
  void SetLastAllocatedBlockId(int64_t id) override;
  uint64_t GetLastGenerationStampV2() override;
  void SetLastGenerationStampV2(int64_t gsv2) override;
  int64_t GetCurSegmentTxId() override;

  // Sync related.
  int64_t GetWaitSyncTime() override;
  void LogSync(bool force = false) override;
  void LogSyncAll() override;

  int64_t RollEditLog() override;
  void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) override;

  // Not deprecated.
  int64_t LogCfsOp(const std::stringstream* ss) override;
  // Block related.
  int64_t LogAllocateBlockId(const std::stringstream* ss,
                             uint64_t* id) override;
  int64_t LogGenerationStampV1(const std::stringstream* ss) override;
  int64_t LogGenerationStampV2(const std::stringstream* ss,
                               uint64_t* gsv2) override;
  int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                    const std::stringstream* gsv2_ss,
                                    uint64_t* blkid,
                                    uint64_t* gsv2) override;
  // Dir tree related.
  int64_t LogTimes(const std::stringstream* ss) override;

  // Deprecated.
  // File related.
  int64_t LogOpenFile(const std::stringstream* ss,
                      bool to_log_rpc_ids = false) override;
  int64_t LogAddBlock(const std::stringstream* ss) override;
  int64_t LogUpdateBlocks(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  int64_t LogCloseFile(const std::stringstream* ss) override;
  int64_t LogReassignLease(const std::stringstream* ss) override;
  int64_t LogConcat(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  // Block related.
  int64_t LogSetBlockPufsInfo(const std::stringstream* ss) override;
  int64_t LogDeleteDeprecatedBlockPufsInfo(
      const std::stringstream* ss) override;
  // Dir tree related.
  int64_t LogMkDir(const std::stringstream* ss) override;
  int64_t LogDelete(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogRenameOld(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRename(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogSymlink(const std::stringstream* ss,
                     bool to_log_rpc_ids = false) override;
  // Set* related.
  int64_t LogSetReplication(const std::stringstream* ss) override;
  int64_t LogSetStoragePolicy(const std::stringstream* ss) override;
  int64_t LogSetReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetDirReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetQuota(const std::stringstream* ss) override;
  int64_t LogSetPermissions(const std::stringstream* ss) override;
  int64_t LogSetOwner(const std::stringstream* ss) override;
  int64_t LogSetAcl(const std::stringstream* ss) override;
  int64_t LogSetXAttrs(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRemoveXAttrs(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  // Snapshot related.
  int64_t LogAllowSnapshot(const std::stringstream* ss) override;
  int64_t LogDisallowSnapshot(const std::stringstream* ss) override;
  int64_t LogCreateSnapshot(const std::stringstream* ss) override;
  int64_t LogDeleteSnapshot(const std::stringstream* ss) override;
  int64_t LogRenameSnapshot(const std::stringstream* ss) override;
  int64_t LogAccessCounterSnapshot(const std::stringstream* ss) override;

  // Switch related.
  EditLogConf::PreviousEditLogConf HASwitchFence() override;
  Status SwitchNonHAActiveToHAActive() override;
  Status SwitchHAActiveToNonHAActive() override;
  Status SwitchHAStandbyToNonHAActive() override;

 private:
  // Refer to FSEditLog::logEditInternal.
  int64_t LogEdit();
  int64_t LogEditInternal();

 private:
  // Avoid using ReadWriteLock in this context. It is crucial to ensure that all
  // executions of LogEditInternal are serialized. Failure to do so may lead to
  // issues with meta_storage::Writer::TxFinish.
  std::mutex mtx_;
  bool is_open_for_write_;
  bool is_fenced_;
  std::shared_ptr<IEditLogSyncListener> listener_;
  // Refer to FSEditLog::txid.
  int64_t txid_;
  // Refer to FSEditLog::curSegmentTxId.
  int64_t cur_segment_txid_;
  // Refer to FSEditLog::lastAllocatedBlockID.
  BlockID last_allocated_block_id_;
  // Refer to FSEditLog::lastGenerationStampV2.
  uint64_t last_generation_stamp_v2_;
};

}  // namespace dancenn

#endif  // EDIT_NON_HA_EDIT_LOG_CONTEXT_H_
