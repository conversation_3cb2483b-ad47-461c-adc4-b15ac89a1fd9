// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/26
// Description

#ifndef EDIT_EDIT_LOG_CFS_OP_H_
#define EDIT_EDIT_LOG_CFS_OP_H_

#include <cnetpp/base/csonpp.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <google/protobuf/message.h>
#include <proto/generated/dancenn/block_info_proto.pb.h>
#include <proto/generated/dancenn/edit_log.pb.h>

#include <atomic>
#include <cstdint>
#include <fstream>  // NOLINT(readability/streams)
#include <functional>
#include <ios>
#include <limits>
#include <map>
#include <memory>
#include <sstream>
#include <string>
#include <type_traits>

#include "base/to_json_string.h"
#include "edit/edit_log_op.h"
#include "edit/serializer.h"

DECLARE_string(dump_parse_failed_protobuf_path);

namespace dancenn {

enum class CfsOpCode : uint32_t {
  // File related.
  kOpenFile = 1001,
  kAddBlockV2 = 1002,
  kAbandonBlock = 1003,
  kUpdatePipeline = 1004,
  kFsync = 1005,
  kCommitBlockSynchronization = 1022,
  // kUpdateBlocks is only used when
  // CommitBlockSynchronizationRequestProto::closefile() == false.
  kUpdateBlocksV2 = 1006,
  kCloseFile = 1007,
  kReassignLeaseV2 = 1008,
  kMergeBlock = 1009,
  kConcatV2 = 1010,
  kAppend = 1020,
  kAllocateBlockIdV2 = 1011,
  kSetGenStampV1 = 1012,
  kSetGenStampV2 = 1013,

  // Block related.
  kApproveUploadBlk = 2001,
  kPersistBlk = 2002,
  kDelDepringBlks = 1,
  kDelDepredBlks = 2,
  kFlushBlockInfoProtos = 2003,

  // Dir tree related.
  kMkdirV2 = 3001,
  kDeleteV2 = 3002,
  kRenameOldV2 = 3003,
  kRenameV2 = 3004,

  // Set* related.
  kSetReplicationV2 = 4001,
  kSetStoragePolicyV2 = 4002,
  kSetReplicaPolicyV2 = 4003,
  kSetDirReplicaPolicyV2 = 4004,
  kSetPermissionsV2 = 4005,
  kSetOwnerV2 = 4006,
  kSetAclV2 = 4007,
  kSetXAttrsV2 = 4008,
  kRemoveXAttrsV2 = 4009,

  // Lifecycle related.
  kSetLifecyclePolicy = 4010,
  kUnsetLifecyclePolicy = 4011,
  kSetAZBlacklist = 4020,

  kSetQuotaV2 = 4012,
  kSetTimesV2 = 4013,
  kSymlinkV2 = 4014,
  kUpdateATimeProtos = 4015,

  // Acc related
  kAccSyncDummy = 5000,
  kAccSyncListingBatchAdd = 5001,
  kAccSyncListingBatchUpdate = 5002,
  kAccSyncUpdateINode = 5003,
  kAccSyncAddFile = 5004,
  kAccPersistFile = 5005,
  kAccUpdateBlockInfo = 5006,

  // Snapshot related.
  kAllowSnapshotV2 = 6001,
  kDisallowSnapshotV2 = 6002,
  kCreateSnapshotV2 = 6003,
  kDeleteSnapshotV2 = 6004,
  kRenameSnapshotV2 = 6005,

  // Replica policy
  kPin = 6007,
  kReconcileINodeAttrs = 6008,

  // Job
  kPersistJobInfo = 7000,

  // Batch
  kBatchCreateFile = 8000,
  kBatchCompleteFile = 8001,
  kBatchDeleteFile = 8002,
};

const uint32_t kCfsOpPrefix = 0x12345678;
const uint32_t kCfsOpSuffix = 0x21436587;

class AbstractEditLogCfsOp : public EditLogOp {
 public:
  virtual ~AbstractEditLogCfsOp() = default;
  virtual CfsOpCode GetCfsOpCode() const = 0;

  virtual std::string ToString() const = 0;
};

extern std::map<CfsOpCode, std::function<AbstractEditLogCfsOp*()>>
    kCfsOpCodeToCtor;
std::shared_ptr<EditLogOp> DeserializeCfsOp(std::stringstream* ss);

// Use tools/get_cfs_op.sh to get cfs op from bk.
template <CfsOpCode Code,
          const char* Name,
          typename P,
          typename = typename std::enable_if<
              std::is_base_of<google::protobuf::Message, P>::value>::type>
class EditLogCfsOp : public AbstractEditLogCfsOp {
 public:
  EditLogCfsOp() : cfs_op_code_(Code) {
    SetOpCode(OP_CFS);
  }

  std::string op_name() const override {
    return Name;
  }

  CfsOpCode GetCfsOpCode() const override {
    return Code;
  }

  const P& GetProto() const {
    return proto_;
  }

  P& GetProto() {
    return proto_;
  }

  void SetProto(const P& proto) {
    proto_ = proto;
  }

  void SetProto(P&& proto) {
    proto_.Swap(&proto);
  }

  void ReadFields(std::stringstream* ss) override {
    uint32_t prefix = 0;
    ReadField(&prefix, ss);
    CHECK_EQ(prefix, kCfsOpPrefix);

    uint32_t len = 0;
    ReadField(&len, ss);
    std::string s;
    s.resize(len);
    ss->read(const_cast<char*>(s.c_str()), len);
    if (!proto_.ParseFromString(s)) {
      std::string filename = FLAGS_dump_parse_failed_protobuf_path;
      if (!filename.empty()) {
        static std::atomic<uint64_t> id(0);
        filename = filename + __PRETTY_FUNCTION__ + "." +
                   std::to_string(id.fetch_add(1));
        std::ofstream outfile(filename, std::ios::binary);
        outfile.write(s.c_str(), len);
        outfile.close();
      }
      CHECK(false);
    }

    uint32_t suffix = 0;
    ReadField(&suffix, ss);
    CHECK_EQ(suffix, kCfsOpSuffix);
  }

  void WriteFields(std::stringstream* ss,
                   bool should_write_base_param = true) const override {
    CHECK_NOTNULL(ss);
    if (should_write_base_param) {
      WriteField(&op_code_, ss);
      WriteField(&txid_, ss);
    }
    uint32_t cfs_op_code = static_cast<uint32_t>(cfs_op_code_);
    WriteField(&cfs_op_code, ss);
    uint32_t prefix = kCfsOpPrefix;
    WriteField(&prefix, ss);

    std::string s = proto_.SerializeAsString();
    uint32_t len = s.size();
    uint32_t cfs_op_max_len = 32 * 1024 * 1024;
    CHECK_LE(len, cfs_op_max_len);
    WriteField(&len, ss);
    ss->write(s.c_str(), s.size());

    uint32_t suffix = kCfsOpSuffix;
    WriteField(&suffix, ss);
  }

  cnetpp::base::Value SerializeToJson() const override {
    cnetpp::base::Object obj;
    cnetpp::base::Value value;
    ToJson(txid_, &value);
    obj["txid"] = std::move(value);
    ToJson(op_code_, &value);
    obj["opCode"] = std::move(value);
    ToJson(op_name(), &value);
    obj["opName"] = std::move(value);
    ToJson(static_cast<uint32_t>(cfs_op_code_), &value);
    obj["cfsOpCode"] = std::move(value);
    ToJson(proto_, &value);
    obj["proto"] = std::move(value);
    return cnetpp::base::Value(std::move(obj));
  }

  std::string ToString() const override {
    std::ostringstream oss;
    oss << "EditLogCfsOp";
    oss << " txid=" << txid_;
    oss << " opCode=" << op_code_;
    oss << " opName=" << op_name();
    oss << " cfsOpCode=" << static_cast<int>(cfs_op_code_);
    oss << " proto=" << proto_.ShortDebugString();
    return oss.str();
  }

 private:
  CfsOpCode cfs_op_code_;
  P proto_;
};

#define DEF_CFS_OP(OP_NAME, INNER_PROTO)                       \
  static constexpr const char kOp##OP_NAME##Name[] = #OP_NAME; \
  using Op##OP_NAME =                                          \
      EditLogCfsOp<CfsOpCode::k##OP_NAME, kOp##OP_NAME##Name, INNER_PROTO>;

// File related.
DEF_CFS_OP(OpenFile, FileToBeOpen);
DEF_CFS_OP(Append, FileToBeAppend);
DEF_CFS_OP(AddBlockV2, BlockToBeAdd);
DEF_CFS_OP(AbandonBlock, BlockToBeAbandon);
DEF_CFS_OP(UpdatePipeline, PipelineToBeUpdate);
DEF_CFS_OP(Fsync, FileToBeSync);
DEF_CFS_OP(CommitBlockSynchronization, BlockToBeCommitSynchronization);
DEF_CFS_OP(UpdateBlocksV2, BlocksToBeUpdate);
DEF_CFS_OP(CloseFile, FileToBeClose);
DEF_CFS_OP(ReassignLeaseV2, LeaseToBeReassign);
DEF_CFS_OP(AllocateBlockIdV2, BlockIdToBeAllocate);
DEF_CFS_OP(SetGenStampV1, GenStampToBeSet);
DEF_CFS_OP(SetGenStampV2, GenStampToBeSet);
DEF_CFS_OP(MergeBlock, FileAndBlockToBeMerge);
DEF_CFS_OP(ConcatV2, FileToBeConcat);

// Block related.
DEF_CFS_OP(ApproveUploadBlk, BlkToBeApproveUpload);
DEF_CFS_OP(PersistBlk, BlkToBePersist);
DEF_CFS_OP(DelDepringBlks, DepringBlksToBeDel);
DEF_CFS_OP(DelDepredBlks, DepredBlksToBeDel);
DEF_CFS_OP(FlushBlockInfoProtos, BlockInfoProtos);
// Dir tree related.
DEF_CFS_OP(MkdirV2, DirToBeMake);
DEF_CFS_OP(DeleteV2, INodeToBeDelete);
DEF_CFS_OP(RenameOldV2, INodeToBeRenameOld);
DEF_CFS_OP(RenameV2, INodeToBeRename);
DEF_CFS_OP(SymlinkV2, INodeToSymlink);

// Set* related.
DEF_CFS_OP(SetReplicationV2, INodeToSetReplication);
DEF_CFS_OP(SetStoragePolicyV2, INodeToSetStoragePolicy);
DEF_CFS_OP(SetReplicaPolicyV2, INodeToSetReplicaPolicy);
DEF_CFS_OP(SetDirReplicaPolicyV2, DirToSetReplicaPolicy);
DEF_CFS_OP(SetQuotaV2, INodeToSetQuota);
DEF_CFS_OP(SetPermissionsV2, INodeToSetPermissions);
DEF_CFS_OP(SetOwnerV2, INodeToSetOwner);
DEF_CFS_OP(SetTimesV2, INodeToSetTimes);
DEF_CFS_OP(SetAclV2, INodeToSetAcl);
DEF_CFS_OP(SetXAttrsV2, INodeToSetXAttrs);
DEF_CFS_OP(RemoveXAttrsV2, INodeToRemoveXAttrs);
DEF_CFS_OP(SetLifecyclePolicy, LifecyclePolicyToBeSet);
DEF_CFS_OP(UnsetLifecyclePolicy, LifecyclePolicyToBeUnset);
DEF_CFS_OP(SetAZBlacklist, AZBlacklist);

// Acc NS Related
DEF_CFS_OP(AccSyncDummy, AccSyncDummyOpBody);
DEF_CFS_OP(AccSyncListingBatchAdd, AccSyncListingBatchAddOpBody);
DEF_CFS_OP(AccSyncListingBatchUpdate, AccSyncListingBatchUpdateOpBody);
DEF_CFS_OP(AccSyncUpdateINode, AccSyncUpdateINodeOpBody);
DEF_CFS_OP(AccSyncAddFile, AccSyncAddFileOpBody);
DEF_CFS_OP(AccPersistFile, AccPersistFileOpBody);
DEF_CFS_OP(AccUpdateBlockInfo, AccUpdateBlockInfoOpBody);

// Snapshot related.
DEF_CFS_OP(AllowSnapshotV2, SnapshotToAllow);
DEF_CFS_OP(DisallowSnapshotV2, SnapshotToDisallow);
DEF_CFS_OP(CreateSnapshotV2, SnapshotToCreate);
DEF_CFS_OP(DeleteSnapshotV2, SnapshotToDelete);
DEF_CFS_OP(RenameSnapshotV2, SnapshotToRename);

// Replica Policy
DEF_CFS_OP(Pin, INodeToPin);
DEF_CFS_OP(ReconcileINodeAttrs, INodeToReconcile);

DEF_CFS_OP(PersistJobInfo, JobInfoOpBody);

// Batch related
DEF_CFS_OP(BatchCreateFile, BatchInodeToCreate);
DEF_CFS_OP(BatchCompleteFile, BatchInodeToComplete);
DEF_CFS_OP(BatchDeleteFile, BatchInodeToDelete);
DEF_CFS_OP(UpdateATimeProtos, ATimeToBeUpdateProtos);

#undef DEF_CFS_OP

}  // namespace dancenn

#endif  // EDIT_EDIT_LOG_CFS_OP_H_
