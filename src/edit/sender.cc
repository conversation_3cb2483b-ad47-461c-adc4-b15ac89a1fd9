// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include "edit/sender.h"

#include <google/protobuf/message.h>

#include <utility>

#include "edit/edit_types.h"
#include "edit/edit_log_cfs_op.h"
#include "edit/edit_log_op.h"
#include "edit/op/op_access_counter_snapshot.h"
#include "namespace/namespace.h"

DECLARE_string(java_classpath);
DECLARE_int32(java_heap_size_mb);
DECLARE_int32(tail_period_ms);
DECLARE_int32(namespace_type);

namespace dancenn {

void ConvertNewEditLogOpToOld::Op(std::shared_ptr<ApplyContext> apply_ctx) {
  CfsOpCode code =
      std::dynamic_pointer_cast<AbstractEditLogCfsOp>(apply_ctx->op)
          ->GetCfsOpCode();
  switch (code) {
    // File related.
    case CfsOpCode::kOpenFile: {
      ConvertNewEditLogOpToOld::OpAdd(apply_ctx);
    } break;
    case CfsOpCode::kAddBlockV2: {
      ConvertNewEditLogOpToOld::OpAddBlock(apply_ctx);
    } break;
    case CfsOpCode::kAbandonBlock: {
      ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpAbandonBlock(apply_ctx);
    } break;
    case CfsOpCode::kUpdatePipeline: {
      ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpUpdatePipeline(apply_ctx);
    } break;
    case CfsOpCode::kFsync: {
      ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpFsync(apply_ctx);
    } break;
    case CfsOpCode::kUpdateBlocksV2: {
      ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpUpdateBlocksV2(apply_ctx);
    } break;
    case CfsOpCode::kCloseFile: {
      ConvertNewEditLogOpToOld::OpClose(apply_ctx);
    } break;
    case CfsOpCode::kReassignLeaseV2: {
      ConvertNewEditLogOpToOld::OpReassignLease(apply_ctx);
    } break;

    // Dir tree related.
    case CfsOpCode::kMkdirV2: {
      ConvertNewEditLogOpToOld::OpMkdir(apply_ctx);
    } break;
    case CfsOpCode::kDeleteV2: {
      ConvertNewEditLogOpToOld::OpDelete(apply_ctx);
    } break;
    case CfsOpCode::kRenameOldV2: {
      ConvertNewEditLogOpToOld::OpRenameOld(apply_ctx);
    } break;
    case CfsOpCode::kRenameV2: {
      ConvertNewEditLogOpToOld::OpRename(apply_ctx);
    } break;

    // Set* related.
    case CfsOpCode::kSetReplicationV2: {
      ConvertNewEditLogOpToOld::OpSetReplication(apply_ctx);
    } break;
    case CfsOpCode::kSetStoragePolicyV2: {
      ConvertNewEditLogOpToOld::OpSetStoragePolicy(apply_ctx);
    } break;
    case CfsOpCode::kSetReplicaPolicyV2: {
      ConvertNewEditLogOpToOld::OpSetReplicaPolicy(apply_ctx);
    } break;
    case CfsOpCode::kSetDirReplicaPolicyV2: {
      ConvertNewEditLogOpToOld::OpSetDirReplicaPolicy(apply_ctx);
    } break;
    case CfsOpCode::kSetQuotaV2: {
      ConvertNewEditLogOpToOld::OpSetQuota(apply_ctx);
    } break;
    case CfsOpCode::kSetPermissionsV2: {
      ConvertNewEditLogOpToOld::OpSetPermissions(apply_ctx);
    } break;
    case CfsOpCode::kSetOwnerV2: {
      ConvertNewEditLogOpToOld::OpSetOwner(apply_ctx);
    } break;
    case CfsOpCode::kSetTimesV2: {
      ConvertNewEditLogOpToOld::OpTimes(apply_ctx);
    } break;
    case CfsOpCode::kSymlinkV2: {
      ConvertNewEditLogOpToOld::OpSymlink(apply_ctx);
    } break;
    case CfsOpCode::kSetAclV2: {
      ConvertNewEditLogOpToOld::OpSetAcl(apply_ctx);
    } break;
    case CfsOpCode::kSetXAttrsV2: {
      ConvertNewEditLogOpToOld::OpSetXattr(apply_ctx);
    } break;
    case CfsOpCode::kRemoveXAttrsV2: {
      ConvertNewEditLogOpToOld::OpRemoveXattr(apply_ctx);
    } break;
    default:
      LOG(FATAL) << "Unknown cfs op code: " << static_cast<int>(code);
  }
}

void EditLogOpFactory::CreateOpAdd(const std::string& path,
                                   const INode& file,
                                   bool overwrite,
                                   const LogRpcInfo& log_rpc_info,
                                   OpAdd& op) {
  op.SetOpCode(OP_ADD);
  op.set_inodeId(file.id());
  op.set_path(path);
  op.set_replication(static_cast<int16_t>(file.replication()));
  op.set_mtime(file.mtime());
  op.set_atime(file.atime());
  op.set_blockSize(file.preferred_blk_size());
  std::vector<::cloudfs::BlockProto> blks;
  for (const auto& x : file.blocks()) {
    blks.push_back(x);
  }
  op.set_blocks(blks);
  op.set_permissions(file.permission());
  op.set_clientName(file.uc().client_name());
  op.set_clientMachine(file.uc().client_machine());
  op.set_overwrite(overwrite);
  op.set_storagePolicyId(static_cast<uint8_t>(file.storage_policy_id()));

  if (file.acls_size() > 0) {
    auto acls = std::vector<cloudfs::AclEntryProto>(file.acls().begin(),
                                                         file.acls().end());
    op.set_aclEntries(acls);
  }
  if (file.xattrs_size() > 0) {
    cloudfs::XAttrEditLogProto edit_xattr;
    edit_xattr.mutable_xattrs()->CopyFrom(file.xattrs());
    op.set_xAttrs(edit_xattr);
  }

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpAdd(std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpOpenFile>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpAdd>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpAdd(proto.path(),
                                proto.inode(),
                                proto.overwrite(),
                                proto.has_log_rpc_info()
                                    ? LogRpcInfo(proto.log_rpc_info())
                                    : LogRpcInfo(),
                                *old_op);
  apply_ctx->op = old_op;
  if (NameSpace::IsAccMode()) {
    CheckAccINode(new_op->GetProto().inode());
    apply_ctx->acc_open_or_mkdir_inode =
        std::make_shared<INode>(new_op->GetProto().inode());
  }
}

int64_t EditLogSender::LogOpenFile(const std::string &path, const INode &file,
    bool overwrite, const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());
  CHECK(file.status() == INode::kFileUnderConstruction);

  OpAdd op = OpAdd();
  EditLogOpFactory::CreateOpAdd(path, file, overwrite, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogOpenFile(&ss);
}

void EditLogOpFactory::CreateOpClose(const std::string& path,
                                     const INode& inode,
                                     OpClose& op) {
  op.SetOpCode(OP_CLOSE);
  op.set_path(path);
  op.set_replication(static_cast<int16_t>(inode.replication()));
  op.set_mtime(inode.mtime());
  op.set_atime(inode.atime());
  op.set_blockSize(inode.preferred_blk_size());
  std::vector<::cloudfs::BlockProto> blks;
  for (const auto& x : inode.blocks()) {
    blks.push_back(x);
  }
  op.set_blocks(blks);
  op.set_permissions(inode.permission());
}

void ConvertNewEditLogOpToOld::OpClose(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpCloseFile>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpClose>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpClose(proto.path(), proto.inode(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogCloseFile(const std::string &path,
    const INode &inode) {
  CHECK(context_->IsOpenForWrite());

  OpClose op = OpClose();
  EditLogOpFactory::CreateOpClose(path, inode, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCloseFile(&ss);
}

void EditLogOpFactory::CreateOpAddBlock(const std::string& path,
                                        const INode& file,
                                        const LogRpcInfo& log_rpc_info,
                                        OpAddBlock& op) {
  op.SetOpCode(OP_ADD_BLOCK);
  op.set_path(path);
  CompactBlockArray blocks;
  if (file.blocks_size() > 1) {
    // the penultimate block
    blocks.push_back(file.blocks(file.blocks_size() - 2));
  }
  // the new block
  blocks.push_back(file.blocks(file.blocks_size() - 1));
  op.set_blocks(blocks);

  // XXX drop it, referring to hadoop-dev FSEditLog.java
  op.set_clientId(INVALID_CLIENT_ID);
  op.set_callId(INVALID_CALL_ID);
}

void ConvertNewEditLogOpToOld::OpAddBlock(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpAddBlockV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpAddBlock>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpAddBlock(proto.path(),
                                     proto.inode(),
                                     proto.has_log_rpc_info()
                                         ? LogRpcInfo(proto.log_rpc_info())
                                         : LogRpcInfo(),
                                     *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogAddBlock(const std::string &path,
    const INode &file, const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  CHECK(file.status() == INode::kFileUnderConstruction);
  CHECK_GT(file.blocks_size(), 0);

  OpAddBlock op = OpAddBlock();
  EditLogOpFactory::CreateOpAddBlock(path, file, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogAddBlock(&ss);
}

void EditLogOpFactory::CreateOpUpdateBlocks(const std::string& path,
                                            const INode& file,
                                            const LogRpcInfo& log_rpc_info,
                                            OpUpdateBlocks& op) {
  op.SetOpCode(OP_UPDATE_BLOCKS);
  op.set_path(path);
  std::vector<::cloudfs::BlockProto> blks;
  for (const auto& x : file.blocks()) {
    blks.push_back(x);
  }
  op.set_blocks(blks);

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpAbandonBlock(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpAbandonBlock>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpUpdateBlocks>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpUpdateBlocks(proto.path(),
                                         proto.inode(),
                                         proto.has_log_rpc_info()
                                             ? LogRpcInfo(proto.log_rpc_info())
                                             : LogRpcInfo(),
                                         *old_op);
  apply_ctx->op = old_op;
}

void ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpUpdatePipeline(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpUpdatePipeline>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpUpdateBlocks>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpUpdateBlocks(proto.path(),
                                         proto.inode(),
                                         proto.has_log_rpc_info()
                                             ? LogRpcInfo(proto.log_rpc_info())
                                             : LogRpcInfo(),
                                         *old_op);
  apply_ctx->op = old_op;
}

void ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpFsync(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpFsync>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpUpdateBlocks>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpUpdateBlocks(proto.path(),
                                         proto.inode(),
                                         proto.has_log_rpc_info()
                                             ? LogRpcInfo(proto.log_rpc_info())
                                             : LogRpcInfo(),
                                         *old_op);
  apply_ctx->op = old_op;
}

void ConvertNewEditLogOpToOld::OpUpdateBlocksFromOpUpdateBlocksV2(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpUpdateBlocksV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpUpdateBlocks>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpUpdateBlocks(proto.path(),
                                         proto.inode(),
                                         proto.has_log_rpc_info()
                                             ? LogRpcInfo(proto.log_rpc_info())
                                             : LogRpcInfo(),
                                         *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogUpdateBlocks(const std::string &path,
    const INode &file, const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  CHECK(file.status() == INode::kFileUnderConstruction);

  OpUpdateBlocks op = OpUpdateBlocks();
  EditLogOpFactory::CreateOpUpdateBlocks(path, file, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogUpdateBlocks(&ss);
}

void EditLogOpFactory::CreateOpMkdir(const std::string& path,
                                     const INode& inode,
                                     OpMkdir& op) {
  op.SetOpCode(OP_MKDIR);
  op.set_inodeId(inode.id());
  op.set_path(path);
  op.set_mtime(inode.mtime());
  op.set_atime(inode.atime());
  op.set_permissions(inode.permission());
  if (inode.acls_size() > 0) {
    auto acls = std::vector<cloudfs::AclEntryProto>(inode.acls().begin(),
                                                         inode.acls().end());
    op.set_aclEntries(acls);
  }
  if (inode.xattrs_size() > 0) {
    cloudfs::XAttrEditLogProto edit_xattr;
    edit_xattr.mutable_xattrs()->CopyFrom(inode.xattrs());
    op.set_xAttrs(edit_xattr);
  }
}

void ConvertNewEditLogOpToOld::OpMkdir(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpMkdirV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpMkdir>();
  old_op->SetTxid(new_op->txid());
  auto& proto = new_op->GetProto();
  // NOTICE: Actually, we don't support atime, it is just an alias of mtime.
  // MkdirOp::writeFields hadoop/hdfs/server/namenode/FSEditLogOp.java
  proto.mutable_inode()->set_atime(proto.inode().mtime());
  EditLogOpFactory::CreateOpMkdir(proto.path(), proto.inode(), *old_op);
  apply_ctx->op = old_op;
  if (NameSpace::IsAccMode()) {
    CheckAccINode(new_op->GetProto().inode());
    apply_ctx->acc_open_or_mkdir_inode =
        std::make_shared<INode>(new_op->GetProto().inode());
  }
}

int64_t EditLogSender::LogMkDir(const std::string &path, const INode &inode) {
  CHECK(context_->IsOpenForWrite());

  OpMkdir op = OpMkdir();
  EditLogOpFactory::CreateOpMkdir(path, inode, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogMkDir(&ss);
}

void EditLogOpFactory::CreateOpRenameOld(const std::string& src,
                                         const std::string& dst,
                                         uint64_t timestamp,
                                         const LogRpcInfo& log_rpc_info,
                                         OpRenameOld& op) {
  op.SetOpCode(OP_RENAME_OLD);
  op.set_src(src);
  op.set_dst(dst);
  op.set_timestamp(timestamp);

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpRenameOld(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpRenameOldV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpRenameOld>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpRenameOld(proto.src_path(),
                                      proto.dst_path(),
                                      proto.timestamp_in_ms(),
                                      proto.has_log_rpc_info()
                                          ? LogRpcInfo(proto.log_rpc_info())
                                          : LogRpcInfo(),
                                      *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogRename(const std::string &src,
    const std::string &dst, uint64_t timestamp,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpRenameOld op = OpRenameOld();
  EditLogOpFactory::CreateOpRenameOld(src, dst, timestamp, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogRenameOld(&ss);
}

void EditLogOpFactory::CreateOpRename(const std::string& src,
                                      const std::string& dst,
                                      uint64_t timestamp,
                                      const LogRpcInfo& log_rpc_info,
                                      bool overwrite,
                                      OpRename& op) {
  op.SetOpCode(OP_RENAME);
  op.set_src(src);
  op.set_dst(dst);
  op.set_timestamp(timestamp);
  RenameOptions opts;
  if (overwrite) {
    opts = RenameOptions(RenameOption::kOverwrite);
  } else {
    opts = RenameOptions(RenameOption::kNone);
  }
  op.set_options(opts);

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpRename(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpRenameV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpRename>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpRename(
      proto.src_path(),
      proto.dst_path(),
      proto.timestamp_in_ms(),
      proto.has_log_rpc_info() ? LogRpcInfo(proto.log_rpc_info())
                               : LogRpcInfo(),
      RenameOptions(proto.rename_options().value()).overwrite(),
      *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogRename(const std::string &src,
    const std::string &dst, uint64_t timestamp, const LogRpcInfo& log_rpc_info,
    bool overwrite) {
  CHECK(context_->IsOpenForWrite());

  OpRename op = OpRename();
  EditLogOpFactory::CreateOpRename(
      src, dst, timestamp, log_rpc_info, overwrite, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogRename(&ss);
}

void EditLogOpFactory::CreateOpSetReplication(const std::string& src,
                                              uint32_t replication,
                                              OpSetReplication& op) {
  op.SetOpCode(OP_SET_REPLICATION);
  op.set_path(src);
  op.set_replication(static_cast<uint16_t>(replication));
}

void ConvertNewEditLogOpToOld::OpSetReplication(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetReplicationV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetReplication>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetReplication(proto.path(), proto.replication(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetReplication(const std::string &src,
    uint32_t replication) {
  CHECK(context_->IsOpenForWrite());

  OpSetReplication op = OpSetReplication();
  EditLogOpFactory::CreateOpSetReplication(src, replication, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetReplication(&ss);
}

void EditLogOpFactory::CreateOpSetStoragePolicy(const std::string& src,
                                                uint32_t policy_id,
                                                OpSetStoragePolicy& op) {
  op.SetOpCode(OP_SET_STORAGE_POLICY);
  op.set_path(src);
  op.set_policyId(static_cast<uint8_t>(policy_id));
}

void ConvertNewEditLogOpToOld::OpSetStoragePolicy(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetStoragePolicyV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetStoragePolicy>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetStoragePolicy(proto.path(), proto.policy_id(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetStoragePolicy(const std::string &src,
    uint32_t policy_id) {
  CHECK(context_->IsOpenForWrite());

  OpSetStoragePolicy op = OpSetStoragePolicy();
  EditLogOpFactory::CreateOpSetStoragePolicy(src, policy_id, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetStoragePolicy(&ss);
}

void EditLogOpFactory::CreateOpSetReplicaPolicy(const std::string& src,
                                                int32_t id,
                                                OpSetReplicaPolicy& op) {
  op.SetOpCode(OP_SET_REPLICA_POLICY);
  op.set_path(src);
  op.set_id(id);
}

void ConvertNewEditLogOpToOld::OpSetReplicaPolicy(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetReplicaPolicyV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetReplicaPolicy>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetReplicaPolicy(
      proto.path(), proto.policy_id(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetReplicaPolicy(const std::string &src, int32_t id) {
  CHECK(context_->IsOpenForWrite());

  OpSetReplicaPolicy op = OpSetReplicaPolicy();
  EditLogOpFactory::CreateOpSetReplicaPolicy(src, id, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetReplicaPolicy(&ss);
}

void EditLogOpFactory::CreateOpSetDirReplicaPolicy(const std::string& src,
                                                   int32_t id,
                                                   const std::string& dc,
                                                   OpSetDirReplicaPolicy& op) {
  op.SetOpCode(OP_SET_DIR_REPLICA_POLICY);
  op.set_path(src);
  op.set_id(id);
  op.set_dc(dc);
}

void ConvertNewEditLogOpToOld::OpSetDirReplicaPolicy(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op =
      std::static_pointer_cast<OpSetDirReplicaPolicyV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetDirReplicaPolicy>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetDirReplicaPolicy(
      proto.path(), proto.policy_id(), proto.dc(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetDirReplicaPolicy(
    const std::string &src, int32_t id, const std::string &dc) {
  CHECK(context_->IsOpenForWrite());

  auto op = OpSetDirReplicaPolicy();
  EditLogOpFactory::CreateOpSetDirReplicaPolicy(src, id, dc, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetDirReplicaPolicy(&ss);
}

void EditLogOpFactory::CreateOpSetQuota(const std::string& src,
                                        uint64_t ns_quota,
                                        uint64_t ds_quota,
                                        OpSetQuota& op) {
  op.SetOpCode(OP_SET_QUOTA);
  op.set_src(src);
  op.set_nsQuota(ns_quota);
  op.set_dsQuota(ds_quota);
}

void ConvertNewEditLogOpToOld::OpSetQuota(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetQuotaV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetQuota>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetQuota(
      proto.src(), proto.ns_quota(), proto.ds_quota(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetQuota(const std::string &src, uint64_t ns_quota,
    uint64_t ds_quota) {
  CHECK(context_->IsOpenForWrite());

  OpSetQuota op = OpSetQuota();
  op.SetOpCode(OP_SET_QUOTA);
  op.set_src(src);
  op.set_nsQuota(ns_quota);
  op.set_dsQuota(ds_quota);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetQuota(&ss);
}

void EditLogOpFactory::CreateOpSetPermissions(const std::string& src,
                                              uint16_t permission,
                                              OpSetPermissions& op) {
  op.SetOpCode(OP_SET_PERMISSIONS);
  op.set_src(src);
  op.set_permissions(permission);
}

void ConvertNewEditLogOpToOld::OpSetPermissions(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetPermissionsV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetPermissions>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetPermissions(
      proto.path(), static_cast<uint16_t>(proto.permissions()), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetPermissions(const std::string &src,
    uint16_t permission) {
  CHECK(context_->IsOpenForWrite());

  OpSetPermissions op = OpSetPermissions();
  EditLogOpFactory::CreateOpSetPermissions(src, permission, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetPermissions(&ss);
}

void EditLogOpFactory::CreateOpSetOwner(const std::string& src,
                                        const std::string& username,
                                        const std::string& groupname,
                                        OpSetOwner& op) {
  op.SetOpCode(OP_SET_OWNER);
  op.set_src(src);
  op.set_username(username);
  op.set_groupname(groupname);
}

void ConvertNewEditLogOpToOld::OpSetOwner(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetOwnerV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetOwner>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetOwner(
      proto.path(), proto.username(), proto.groupname(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetOwner(const std::string &src,
    const std::string &username, const std::string &groupname) {
  CHECK(context_->IsOpenForWrite());

  OpSetOwner op = OpSetOwner();
  EditLogOpFactory::CreateOpSetOwner(src, username, groupname, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetOwner(&ss);
}

int64_t EditLogSender::LogConcat(const std::string &trg,
    const std::vector<std::string> &srcs, uint64_t timestamp,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpConcatDelete op = OpConcatDelete();
  op.SetOpCode(OP_CONCAT_DELETE);
  op.set_trg(trg);
  op.set_srcs(srcs);
  op.set_timestamp(timestamp);

  std::string clientId;
  uint32_t callId;
  EditLogOpFactory::ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogConcat(&ss);
}

void EditLogOpFactory::CreateOpDelete(const std::string& src,
                                      uint64_t time_now_ms,
                                      const LogRpcInfo& log_rpc_info,
                                      OpDelete& op) {
  op.SetOpCode(OP_DELETE);
  op.set_path(src);
  op.set_timestamp(static_cast<uint64_t>(time_now_ms));

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpDelete(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpDeleteV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpDelete>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpDelete(proto.path(),
                                   proto.timestamp_in_ms(),
                                   proto.has_log_rpc_info()
                                       ? LogRpcInfo(proto.log_rpc_info())
                                       : LogRpcInfo(),
                                   *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogDelete(const std::string &src,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpDelete op = OpDelete();
  uint64_t time_now_ms =
      std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now().time_since_epoch())
          .count();
  EditLogOpFactory::CreateOpDelete(src, time_now_ms, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogDelete(&ss);
}

int64_t EditLogSender::LogGenerationStampV1(uint64_t genstamp) {
  CHECK(context_->IsOpenForWrite());

  OpSetGenstampV1 op = OpSetGenstampV1();
  op.SetOpCode(OP_SET_GENSTAMP_V1);
  op.set_genStampV1(genstamp);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogGenerationStampV1(&ss);
}

int64_t EditLogSender::LogGenerationStampV2(uint64_t genstamp) {
  CHECK(context_->IsOpenForWrite());

  OpSetGenstampV2 op = OpSetGenstampV2();
  op.SetOpCode(OP_SET_GENSTAMP_V2);
  op.set_genStampV2(kLastReservedGenerationStamp);

  std::stringstream ss;
  op.WriteFields(&ss);
  uint64_t genstamp_out = genstamp;
  int64_t txid = context_->LogGenerationStampV2(&ss, &genstamp_out);
  CHECK_EQ(genstamp_out, genstamp)
      << "expect " << genstamp << " but got " << genstamp_out;
  return txid;
}

int64_t EditLogSender::LogAllocateBlockId(uint64_t blockId) {
  CHECK(context_->IsOpenForWrite());

  OpAllocateBlockId op = OpAllocateBlockId();
  op.SetOpCode(OP_ALLOCATE_BLOCK_ID);
  op.set_blockId(blockId);

  std::stringstream ss;
  op.WriteFields(&ss);
  uint64_t block_id_out = blockId;
  int64_t txid = context_->LogAllocateBlockId(&ss, &block_id_out);
  CHECK_EQ(block_id_out, blockId)
      << "expect " << blockId << " but got " << block_id_out;
  return txid;
}

int64_t EditLogSender::LogBlockIdAndGSv2(uint64_t* blockId,
                                         uint64_t* genstamp) {
  CHECK(context_->IsOpenForWrite());

  std::stringstream blkid_ss;
  std::stringstream gsv2_ss;

  {
    OpAllocateBlockId op = OpAllocateBlockId();
    op.SetOpCode(OP_ALLOCATE_BLOCK_ID);
    op.set_blockId(kLastReservedBlockId);
    op.WriteFields(&blkid_ss);
  }
  {
    OpSetGenstampV2 op = OpSetGenstampV2();
    op.SetOpCode(OP_SET_GENSTAMP_V2);
    op.set_genStampV2(kLastReservedGenerationStamp);
    op.WriteFields(&gsv2_ss);
  }

  return context_->LogAllocateBlockIdAndGSv2(
      &blkid_ss, &gsv2_ss, blockId, genstamp);
}

void EditLogOpFactory::CreateOpTimes(const std::string& src,
                                     uint64_t mtime,
                                     uint64_t atime,
                                     OpTimes& op) {
  op.SetOpCode(OP_TIMES);
  op.set_path(src);
  op.set_mtime(mtime);
  op.set_atime(atime);
}

void ConvertNewEditLogOpToOld::OpTimes(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetTimesV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpTimes>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpTimes(
      proto.path(), proto.mtime(), proto.atime(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogTimes(const std::string &src, uint64_t mtime,
    uint64_t atime) {
  CHECK(context_->IsOpenForWrite());

  OpTimes op = OpTimes();
  op.SetOpCode(OP_TIMES);
  op.set_path(src);
  op.set_mtime(mtime);
  op.set_atime(atime);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogTimes(&ss);
}

void EditLogOpFactory::CreateOpSymlink(const std::string& src,
                                       const INode& node,
                                       const LogRpcInfo& log_rpc_info,
                                       OpSymlink& op) {
  op.SetOpCode(OP_SYMLINK);
  op.set_inodeId(node.id());
  op.set_path(src);
  op.set_value(node.symlink());
  op.set_mtime(node.mtime());
  op.set_atime(node.atime());
  op.set_permissions(node.permission());

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpSymlink(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSymlinkV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSymlink>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSymlink(proto.path(),
                                    proto.node(),
                                    proto.has_log_rpc_info()
                                        ? LogRpcInfo(proto.log_rpc_info())
                                        : LogRpcInfo(),
                                    *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSymlink(const std::string &path, const INode &node,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpSymlink op = OpSymlink();
  op.SetOpCode(OP_SYMLINK);
  op.set_inodeId(node.id());
  op.set_path(path);
  op.set_value(node.symlink());
  op.set_mtime(node.mtime());
  op.set_atime(node.atime());
  op.set_permissions(node.permission());

  std::string clientId;
  uint32_t callId;
  EditLogOpFactory::ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSymlink(&ss);
}

void EditLogOpFactory::CreateOpReassignLease(const std::string& lease_holder,
                                             const std::string& src,
                                             const std::string& new_holder,
                                             OpReassignLease& op) {
  op.SetOpCode(OP_REASSIGN_LEASE);
  op.set_leaseHolder(lease_holder);
  op.set_path(src);
  op.set_newHolder(new_holder);
}

void ConvertNewEditLogOpToOld::OpReassignLease(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpReassignLeaseV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpReassignLease>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpReassignLease(
      proto.lease_holder(), proto.path(), proto.new_holder(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogReassignLease(const std::string &lease_holder,
    const std::string &src, const std::string &new_holder) {
  CHECK(context_->IsOpenForWrite());

  OpReassignLease op = OpReassignLease();
  EditLogOpFactory::CreateOpReassignLease(lease_holder, src, new_holder, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogReassignLease(&ss);
}

void EditLogOpFactory::CreateOpSetAcl(
    const std::string& src,
    const ::google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
        entries,
    OpSetAcl& op) {
  op.SetOpCode(OP_SET_ACL);
  cloudfs::AclEditLogProto acls;
  acls.set_src(src);
  acls.mutable_entries()->CopyFrom(entries);
  op.set_aclEditLog(acls);
}

void ConvertNewEditLogOpToOld::OpSetAcl(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetAclV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetAcl>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetAcl(proto.path(), proto.acl_entries(), *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetAcl(const std::string &src,
    const ::google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>& entries) {
  CHECK(context_->IsOpenForWrite());

  OpSetAcl op = OpSetAcl();
  EditLogOpFactory::CreateOpSetAcl(src, entries, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetAcl(&ss);
}

void EditLogOpFactory::CreateOpSetXattr(
    const std::string& src,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>&
        xattrs,
    const LogRpcInfo& log_rpc_info,
    OpSetXattr& op) {
  op.SetOpCode(OP_SET_XATTR);
  cloudfs::XAttrEditLogProto edit_xattr;
  edit_xattr.set_src(src);
  edit_xattr.mutable_xattrs()->CopyFrom(xattrs);
  op.set_xAttrs(edit_xattr);

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void ConvertNewEditLogOpToOld::OpSetXattr(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpSetXAttrsV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpSetXattr>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpSetXattr(proto.path(),
                                     proto.xattrs(),
                                     proto.has_log_rpc_info()
                                         ? LogRpcInfo(proto.log_rpc_info())
                                         : LogRpcInfo(),
                                     *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogSetXAttrs(const std::string &src,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpSetXattr op = OpSetXattr();
  EditLogOpFactory::CreateOpSetXattr(src, xattrs, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetXAttrs(&ss);
}

void EditLogOpFactory::CreateOpRemoveXattr(
    const std::string& src,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>&
        xattrs,
    const LogRpcInfo& log_rpc_info,
    OpRemoveXattr& op) {
  op.SetOpCode(OP_REMOVE_XATTR);
  cloudfs::XAttrEditLogProto edit_xattr;
  edit_xattr.set_src(src);
  edit_xattr.mutable_xattrs()->CopyFrom(xattrs);
  op.set_xAttrs(edit_xattr);

  std::string clientId;
  uint32_t callId;
  ConvertLogRpcInfo(log_rpc_info, &clientId, &callId);
  op.set_clientId(clientId);
  op.set_callId(callId);
}

void EditLogOpFactory::ConvertLogRpcInfo(const LogRpcInfo& log_rpc_info,
                                         std::string* clientId,
                                         uint32_t* callId) {
  CHECK_NOTNULL(clientId);
  CHECK_NOTNULL(callId);
  if (log_rpc_info.to_log_rpc_ids_) {
    *clientId = log_rpc_info.rpc_client_id_;
    *callId = log_rpc_info.rpc_call_id_;
  } else {
    *clientId = INVALID_CLIENT_ID;
    *callId = INVALID_CALL_ID;
  }
}

void ConvertNewEditLogOpToOld::OpRemoveXattr(
    std::shared_ptr<ApplyContext> apply_ctx) {
  auto new_op = std::static_pointer_cast<OpRemoveXAttrsV2>(apply_ctx->op);
  CHECK(new_op != nullptr);
  auto old_op = std::make_shared<::dancenn::OpRemoveXattr>();
  old_op->SetTxid(new_op->txid());
  const auto& proto = new_op->GetProto();
  EditLogOpFactory::CreateOpRemoveXattr(proto.path(),
                                        proto.xattrs(),
                                        proto.has_log_rpc_info()
                                            ? LogRpcInfo(proto.log_rpc_info())
                                            : LogRpcInfo(),
                                        *old_op);
  apply_ctx->op = old_op;
}

int64_t EditLogSender::LogRemoveXAttrs(const std::string &src,
    const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
    const LogRpcInfo& log_rpc_info) {
  CHECK(context_->IsOpenForWrite());

  OpRemoveXattr op = OpRemoveXattr();
  EditLogOpFactory::CreateOpRemoveXattr(src, xattrs, log_rpc_info, op);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogRemoveXAttrs(&ss);
}

int64_t EditLogSender::LogAccessCounterSnapshot(
    const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot) {  // NOLINT(whitespace/line_length)
  CHECK(context_->IsOpenForWrite());

  OpAccessCounterSnapshot op;
  op.SetOpCode(OP_ACCESS_COUNTER_SNAPSHOT);
  op.set_snapshot(snapshot);

  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogAccessCounterSnapshot(&ss);
}

int64_t EditLogSender::LogSetBlockPufsInfo(const std::string& s) {
  CHECK(context_->IsOpenForWrite());
  OpSetBlockPufsInfo op;
  op.SetOpCode(OP_SET_BLOCK_PUFS_INFO);
  op.set_s(s);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogSetBlockPufsInfo(&ss);
}

int64_t EditLogSender::LogDeleteDeprecatedBlockPufsInfo(uint64_t blk_id) {
  CHECK(context_->IsOpenForWrite());
  OpDeleteDeprecatedBlockPufsInfo op;
  op.SetOpCode(OP_DELETE_DEPRECATED_BLOCK_PUFS_INFO);
  op.set_s(std::to_string(blk_id));
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogDeleteDeprecatedBlockPufsInfo(&ss);
}

int64_t EditLogSender::LogAllowSnapshot(const std::string& path) {
  CHECK(context_->IsOpenForWrite());
  OpAllowSnapshot op;
  op.SetOpCode(OP_ALLOW_SNAPSHOT);
  op.set_snapshotRoot(path);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogAllowSnapshot(&ss);
}

int64_t EditLogSender::LogDisallowSnapshot(const std::string& path) {
  CHECK(context_->IsOpenForWrite());
  OpDisallowSnapshot op;
  op.SetOpCode(OP_DISALLOW_SNAPSHOT);
  op.set_snapshotRoot(path);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogDisallowSnapshot(&ss);
}

int64_t EditLogSender::LogCreateSnapshot(const std::string& path,
                                         const std::string& name) {
  CHECK(context_->IsOpenForWrite());
  OpCreateSnapshot op;
  op.SetOpCode(OP_CREATE_SNAPSHOT);
  op.set_snapshotRoot(path);
  op.set_snapshotName(name);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogCreateSnapshot(&ss);
}

int64_t EditLogSender::LogDeleteSnapshot(const std::string& path,
                                         const std::string& name) {
  CHECK(context_->IsOpenForWrite());
  OpDeleteSnapshot op;
  op.SetOpCode(OP_DELETE_SNAPSHOT);
  op.set_snapshotRoot(path);
  op.set_snapshotName(name);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogDeleteSnapshot(&ss);
}

int64_t EditLogSender::LogRenameSnapshot(const std::string& path,
                                         const std::string& old_name,
                                         const std::string& new_name) {
  CHECK(context_->IsOpenForWrite());
  OpRenameSnapshot op;
  op.SetOpCode(OP_RENAME_SNAPSHOT);
  op.set_snapshotRoot(path);
  op.set_snapshotOldName(old_name);
  op.set_snapshotNewName(new_name);
  std::stringstream ss;
  op.WriteFields(&ss);
  return context_->LogRenameSnapshot(&ss);
}

void EditLogSender::Sync(bool force) {
  // EditLogRecoveryTest.SyncAfterClose depends on below
  if (!context_->IsOpenForWrite()) return;

  context_->LogSync(force);
}

}  // namespace dancenn

