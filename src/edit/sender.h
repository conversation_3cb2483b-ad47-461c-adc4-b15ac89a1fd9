//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef EDIT_SENDER_H_
#define EDIT_SENDER_H_

#include <cnetpp/concurrency/spin_lock.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <memory>
#include <string>
#include <vector>

#include "base/metric.h"
#include "base/metrics.h"
#include "edit/edit_log_context.h"
#include "edit/edit_log_op.h"
#include "edit/op/all.h"
#include "edit/sender_base.h"
#include "inode.pb.h"  // NOLINT(build/include)
#include "lifecycle.pb.h"
#include "namespace/inode.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

// This is a temporary piece of code to safely refactor old edit logs.
// TODO(ruanjunbin): Delete it after refactor done.
struct EditLogOpFactory {
  // File related.
  static void CreateOpAdd(const std::string& path,
                          const INode& file,
                          bool overwrite,
                          const LogRpcInfo& log_rpc_info,
                          OpAdd& op);
  static void CreateOpAddBlock(const std::string& path,
                               const INode& file,
                               const LogRpcInfo& log_rpc_info,
                               OpAddBlock& op);
  static void CreateOpUpdateBlocks(const std::string& path,
                                   const INode& file,
                                   const LogRpcInfo& log_rpc_info,
                                   OpUpdateBlocks& op);
  static void CreateOpClose(const std::string& path,
                            const INode& inode,
                            OpClose& op);
  static void CreateOpReassignLease(const std::string& lease_holder,
                                    const std::string& src,
                                    const std::string& new_holder,
                                    OpReassignLease& op);

  // Dir tree related.
  static void CreateOpMkdir(const std::string& path,
                            const INode& inode,
                            OpMkdir& op);
  static void CreateOpDelete(const std::string& src,
                             uint64_t timestamp_in_ms,
                             const LogRpcInfo& log_rpc_info,
                             OpDelete& op);
  static void CreateOpRenameOld(const std::string& src,
                                const std::string& dst,
                                uint64_t timestamp,
                                const LogRpcInfo& log_rpc_info,
                                OpRenameOld& op);
  static void CreateOpRename(const std::string& src,
                             const std::string& dst,
                             uint64_t timestamp,
                             const LogRpcInfo& log_rpc_info,
                             bool overwrite,
                             OpRename& op);

  // Set* related.
  static void CreateOpSetReplication(const std::string& src,
                                     uint32_t replication,
                                     OpSetReplication& op);
  static void CreateOpSetStoragePolicy(const std::string& src,
                                       uint32_t policy_id,
                                       OpSetStoragePolicy& op);
  static void CreateOpSetReplicaPolicy(const std::string& src,
                                       int32_t id,
                                       OpSetReplicaPolicy& op);
  static void CreateOpSetDirReplicaPolicy(const std::string& src,
                                          int32_t id,
                                          const std::string& dc,
                                          OpSetDirReplicaPolicy& op);
  static void CreateOpSetQuota(const std::string& src,
                               uint64_t ns_quota,
                               uint64_t ds_quota,
                               OpSetQuota& op);
  static void CreateOpSetPermissions(const std::string& src,
                                     uint16_t permission,
                                     OpSetPermissions& op);
  static void CreateOpSetOwner(const std::string& src,
                               const std::string& username,
                               const std::string& groupname,
                               OpSetOwner& op);
  static void CreateOpTimes(const std::string& src,
                               uint64_t mtime,
                               uint64_t atime,
                               OpTimes& op);
  static void CreateOpSymlink(const std::string& path,
                              const INode& inode,
                              const LogRpcInfo& log_rpc_info,
                              OpSymlink& op);
  static void CreateOpSetAcl(
      const std::string& src,
      const ::google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
          entries,
      OpSetAcl& op);
  static void CreateOpSetXattr(
      const std::string& src,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>&
          xattrs,
      const LogRpcInfo& log_rpc_info,
      OpSetXattr& op);
  static void CreateOpRemoveXattr(
      const std::string& src,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>&
          xattrs,
      const LogRpcInfo& log_rpc_info,
      OpRemoveXattr& op);

  // util
  static void ConvertLogRpcInfo(const LogRpcInfo& log_rpc_info,
                                std::string* clientId,
                                uint32_t* callId);
};

class ApplyContext;
class ConvertNewEditLogOpToOld {
 public:
  static void Op(std::shared_ptr<ApplyContext> apply_ctx);

 private:
  // File related.
  static void OpAdd(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpAddBlock(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpUpdateBlocksFromOpAbandonBlock(
      std::shared_ptr<ApplyContext> apply_ctx);
  static void OpUpdateBlocksFromOpUpdatePipeline(
      std::shared_ptr<ApplyContext> apply_ctx);
  static void OpUpdateBlocksFromOpFsync(
      std::shared_ptr<ApplyContext> apply_ctx);
  static void OpUpdateBlocksFromOpUpdateBlocksV2(
      std::shared_ptr<ApplyContext> apply_ctx);
  static void OpClose(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpReassignLease(std::shared_ptr<ApplyContext> apply_ctx);

  // Dir tree related.
  static void OpMkdir(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpDelete(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpRenameOld(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpRename(std::shared_ptr<ApplyContext> apply_ctx);

  // Set* related.
  static void OpSetReplication(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetStoragePolicy(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetReplicaPolicy(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetDirReplicaPolicy(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetQuota(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetPermissions(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetOwner(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpTimes(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSymlink(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetAcl(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpSetXattr(std::shared_ptr<ApplyContext> apply_ctx);
  static void OpRemoveXattr(std::shared_ptr<ApplyContext> apply_ctx);
};

class EditLogSender : public EditLogSenderBase {
 public:
  EditLogSender(int64_t last_transaction_id,
      std::shared_ptr<EditLogContextBase> context) : context_(context) {
    LOG(INFO) << "SetNextTxId: " << last_transaction_id + 1;
    context_->SetNextTxId(last_transaction_id + 1);

    auto center = MetricsCenter::Instance();
    auto metrics = center->RegisterMetrics("EditLogSender");
    check_is_open_for_write_time_ =
        metrics->RegisterHistogram("Log#step=CheckIsOpenForWrite");
    serialize_time_ = metrics->RegisterHistogram("Log#step=Serialize");
    call_context_time_ = metrics->RegisterHistogram("Log#step=CallContext");
  }
  // Notice: This constructor doesn't set next transaction id.
  // Please make sure EditLogSender(int64_t last_transaction_id,
  // std::shared_ptr<EditLogContextBase> context) has been called before.
  // TODO(ruanjunbin): Find a place to init EditLogSender, don't let user
  // choose between these two constructors.
  explicit EditLogSender(std::shared_ptr<EditLogContextBase> context)
      : context_(context) {
  }
  virtual ~EditLogSender() {}

  EditLogSender(const EditLogSender &other) = delete;

  // -------- New Edit Log Begins.
  // File related.
  int64_t LogOpenFileV2(
      const std::string& path,
      const INode& inode,
      const INode& parent,
      bool overwrite,
      const INode* overwrite_inode,
      bool move_to_recycle_bin,
      const std::string* rb_path,
      const INode* rb_inode,
      const INode* rb_parent,
      const std::vector<BlockInfoProto>& add_block_bips,
      const std::vector<std::vector<std::string>>& bips_expected_locs,
      const std::vector<INodeID>& ancestors_id,
      const std::vector<INodeID>* rb_ancestors_id,
      const SnapshotLog& old_inode_snaplog,
      const SnapshotLog& parent_snaplog,
      const SnapshotLog& rb_parent_snaplog,
      const LogRpcInfo& log_rpc_info) override;
  int64_t LogAppend(const std::string path,
                    const INode& inode,
                    const INode& parent,
                    const INode& old_inode,
                    const std::vector<INodeID>& ancestors_id,
                    const SnapshotLog& inode_snaplog,
                    const LogRpcInfo& log_rpc_info) override;
  int64_t LogAddBlockV2(const std::string& path,
                        const INode& inode,
                        const BlockInfoProto* penultimate_bip,
                        const BlockInfoProto& last_bip,
                        const INode& old_inode,
                        const std::vector<INodeID>& ancestors_id,
                        const std::vector<std::string>& dn_uuids,
                        const SnapshotLog& inode_snaplog,
                        const LogRpcInfo& log_rpc_info) override;
  int64_t LogAbandonBlock(const std::string& path,
                          const INode& inode,
                          BlockID abandoned_blk_id,
                          const INode& old_inode,
                          const std::vector<INodeID>& ancestors_id,
                          const LogRpcInfo& log_rpc_info) override;
  int64_t LogUpdatePipeline(const std::string& path,
                            const INode& inode,
                            const BlockInfoProto& last_bip_tbuc,
                            const INode& old_inode,
                            const std::vector<INodeID>& ancestors_id,
                            const LogRpcInfo& log_rpc_info) override;
  int64_t LogFsync(const std::string& path,
                   const INode& inode,
                   const BlockInfoProto* last_bip_tbuc,
                   const INode& old_inode,
                   const std::vector<INodeID>& ancestors_id,
                   const SnapshotLog& inode_snaplog,
                   const LogRpcInfo& log_rpc_info) override;
  int64_t LogCommitBlockSynchronization(
      const std::string& path,
      bool delete_block,
      bool close_file,
      const INode& inode,
      const BlockInfoProto& last_bip,
      const INode& old_inode,
      std::vector<INodeID>& ancestors_id,
      const SnapshotLog& inode_snaplog) override;
  int64_t LogUpdateBlocksV2(const std::string& path,
                            const INode& inode,
                            const INode& old_inode,
                            const std::vector<INodeID>& ancestors_id,
                            const LogRpcInfo& log_rpc_info) override;
  int64_t LogCloseFileV2(const std::string& path,
                         const INode& inode,
                         const BlockInfoProto* last_bip,
                         BlockID dropped_blk_id,
                         const INode& old_inode,
                         const std::vector<INodeID>& ancestors_id,
                         const SnapshotLog& inode_snaplog) override;
  int64_t LogReassignLeaseV2(const std::string& path,
                             const INode& inode,
                             const INode& old_inode,
                             const std::string& lease_holder,
                             const std::string& new_holder,
                             const SnapshotLog& inode_snaplog) override;
  int64_t LogConcatV2(const std::string& parent_path,
                      const std::string& target,
                      const std::vector<std::string>& srcs,
                      const INode& target_inode,
                      const INode& old_target_inode,
                      const std::vector<INode>& src_inodes,
                      const std::vector<BlockInfoProto>& target_bips,
                      const INode& parent,
                      uint64_t timestamp_in_ms,
                      const LogRpcInfo& log_rpc_info) override;

  int64_t LogAllocateBlockIdV2(uint64_t blockid) override;
  int64_t LogSetGenerationStampV1(uint64_t genstamp) override;
  int64_t LogSetGenerationStampV2(uint64_t genstamp, bool force_send) override;
  int64_t LogMergeBlocks(
      const std::string& path,
      const INode& inode,
      const BlockInfoProto& bip,
      const std::vector<BlockProto>& depred_blks,
      const INode& old_inode,
      const std::vector<INodeID>& ancestors_id,
      const SnapshotLog& inode_snaplog) override;

  int64_t LogPin(const std::string& path,
                 const INode& inode,
                 const INode& old_inode,
                 const JobInfoOpBody& job,
                 const ManagedJobId& cancel_job_id,
                 const LogRpcInfo& log_rpc_info,
                 bool update_txid) override;
  int64_t LogReconcileINodeAttrs(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const std::set<int64_t>& expired_ttl,
      const std::set<int64_t>& new_ttl,
      const std::vector<ManagedJobId>& cancel_job_id) override;

  int64_t LogPersistJobInfo(const JobInfoOpBody& job) override;

  // Block related.
  int64_t LogApproveUploadBlk(const BlockInfoProto& bip,
                              const BlockInfoProto& old_bip) override;
  int64_t LogPersistBlk(const BlockInfoProto& bip,
                        const BlockInfoProto& old_bip) override;
  int64_t LogDelDepringBlks(const DepringBlksToBeDel& blks) override;
  int64_t LogDelDepredBlks(const DepredBlksToBeDel& blks) override;
  int64_t LogFlushBlockInfoProtos(const BlockInfoProtos& bips) override;
  int64_t LogUpdateATimeProtos(const ATimeToBeUpdateProtos& atimes) override;

  // Dir tree related.
  int64_t LogMkdirV2(const std::string& path,
                     const INode& inode,
                     const INode& parent,
                     const LogRpcInfo& log_rpc_info,
                     const std::vector<INodeID>& ancestors_id,
                     const SnapshotLog& parent_snaplog) override;
  int64_t LogDeleteV2(const std::string& path,
                      const INode& inode,
                      const INode& parent,
                      uint64_t timestamp_in_ms,
                      const std::vector<INodeID>& ancestors_id,
                      const SnapshotLog& inode_snaplog,
                      const SnapshotLog& parent_snaplog,
                      const LogRpcInfo& log_rpc_info) override;
  int64_t LogRenameOldV2(const std::string& src_path,
                         const std::string& dst_path,
                         const INode& src_inode,
                         const INode& dst_inode,
                         const INode& src_parent,
                         const INode& dst_parent,
                         uint64_t timestamp_in_ms,
                         const std::vector<INodeID>& src_ancestors_id,
                         const std::vector<INodeID>& dst_ancestors_id,
                         const SnapshotLog& src_inode_snaplog,
                         const SnapshotLog& src_parent_snaplog,
                         const SnapshotLog& dst_parent_snaplog,
                         const LogRpcInfo& log_rpc_info) override;
  int64_t LogRenameV2(const std::string& src_path,
                      const std::string& dst_path,
                      const INode& src_inode,
                      const INode& new_dst_inode,
                      const INode& src_parent,
                      const INode& dst_parent,
                      bool overwrite,
                      const INode* old_dst_inode,
                      bool move_to_recycle_bin,
                      const std::string* rb_path,
                      const INode* rb_inode,
                      const INode* rb_parent,
                      uint64_t timestamp_in_ms,
                      const std::vector<INodeID>& src_ancestors_id,
                      const std::vector<INodeID>& dst_ancestors_id,
                      const std::vector<INodeID>* rb_ancestors_id,
                      const SnapshotLog& src_inode_snaplog,
                      const SnapshotLog& old_dst_inode_snaplog,
                      const SnapshotLog& src_parent_snaplog,
                      const SnapshotLog& dst_parent_snaplog,
                      const SnapshotLog& rb_parent_snaplog,
                      const LogRpcInfo& log_rpc_info) override;

  // Set* related.
  int64_t LogSetReplicationV2(const std::string& path,
                              const INode& inode,
                              const INode& old_inode,
                              uint32_t replication,
                              const SnapshotLog& inode_snaplog) override;
  int64_t LogSetStoragePolicyV2(const std::string& path,
                                const INode& inode,
                                const INode& old_inode,
                                uint32_t policy_id,
                                const SnapshotLog& inode_snaplog) override;
  int64_t LogSetReplicaPolicyV2(const std::string& path,
                                const INode& inode,
                                const INode& old_inode,
                                int32_t policy_id,
                                const SnapshotLog& inode_snaplog) override;
  int64_t LogSetDirReplicaPolicyV2(const std::string& path,
                                   const INode& inode,
                                   const INode& old_inode,
                                   int32_t policy_id,
                                   const std::string& dc,
                                   const SnapshotLog& inode_snaplog) override;
  int64_t LogSetQuotaV2(const std::string& src,
                        uint64_t ns_quota,
                        uint64_t ds_quota,
                        const SnapshotLog& inode_snaplog) override;
  int64_t LogSetPermissionsV2(const std::string& path,
                              const INode& inode,
                              const INode& old_inode,
                              uint16_t permission,
                              const SnapshotLog& inode_snaplog) override;
  int64_t LogSetOwnerV2(const std::string& path,
                        const INode& inode,
                        const INode& old_inode,
                        const std::string& username,
                        const std::string& groupname,
                        const SnapshotLog& inode_snaplog) override;
  int64_t LogSetTimesV2(const std::string& src,
                        uint64_t mtime,
                        uint64_t atime,
                        const INode& inode,
                        const INode& old_inode,
                        const SnapshotLog& inode_snaplog) override;
  int64_t LogSymlinkV2(const std::string& path,
                       const INode& node,
                       const LogRpcInfo& log_rpc_info) override;
  int64_t LogSetAclV2(
      const std::string& src,
      const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>& entries)
      override;
  int64_t LogSetXAttrsV2(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const SnapshotLog& inode_snaplog,
      const LogRpcInfo& log_rpc_info) override;
  int64_t LogRemoveXAttrsV2(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const SnapshotLog& inode_snaplog,
      const LogRpcInfo& log_rpc_info) override;
  int64_t LogSetLifecyclePolicy(
      const INode& inode,
      const std::string& path,
      uint64_t ts,
      const cloudfs::LifecyclePolicyProto& policy,
      const LogRpcInfo& rpc_info) override;
  int64_t LogUnsetLifecyclePolicy(
      const INodeID& id,
      const std::string* path,
      const LogRpcInfo& rpc_info) override;
  int64_t LogSetAZBlacklist(
      const std::string& azs) override;

  // Acc related
  int64_t LogAccSyncDummy() override;
  int64_t LogAccSyncListingBatchAdd(
      const std::string& dir_path,
      const INode& dir_inode,
      const std::vector<INodeWithBlocks>& file_blocks) override;
  int64_t LogAccSyncListingBatchUpdate(
      const std::string& dir_path,
      const INode& dir_inode,
      const std::vector<INode>& inodes_to_update,
      const std::vector<INode>& old_inodes) override;
  int64_t LogAccSyncUpdateINode(const std::string& path,
                                const INode& inode,
                                const INode& old_inode) override;
  int64_t LogAccSyncAddFile(const std::string& path,
                            const INode& parent,
                            const INodeWithBlocks& file,
                            const INode* file_to_del) override;
  int64_t LogAccPersistFile(const std::string& path,
                            const INode& parent,
                            const INode& inode,
                            const INode& old_inode) override;
  int64_t LogAccUpdateBlockInfo(const BlockInfoProto& bip,
                                const BlockInfoProto& old_bip) override;

  // -------- New Edit Log Ends.

  // log ops:
  int64_t LogOpenFile(const std::string &path, const INode &file,
      bool overwrite, const LogRpcInfo& log_rpc_info) override;

  int64_t LogCloseFile(const std::string &path, const INode &inode) override;

  int64_t LogAddBlock(const std::string &path, const INode &file,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogUpdateBlocks(const std::string &path, const INode &file,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogMkDir(const std::string &path, const INode &inode) override;

  int64_t LogRename(const std::string &src, const std::string &dst,
      uint64_t timestamp, const LogRpcInfo& log_rpc_info) override;

  int64_t LogRename(const std::string &src, const std::string &dst,
      uint64_t timestamp, const LogRpcInfo& log_rpc_info,
      bool overwrite) override;

  int64_t LogSetReplication(const std::string &src,
      uint32_t replication) override;

  int64_t LogSetStoragePolicy(const std::string &src,
      uint32_t policy_id) override;

  int64_t LogSetReplicaPolicy(const std::string &src, int32_t id) override;

  int64_t LogSetDirReplicaPolicy(const std::string &src,
                                 int32_t id, const std::string &dc) override;

  int64_t LogSetQuota(const std::string &src, uint64_t ns_quota,
      uint64_t ds_quota) override;

  int64_t LogSetPermissions(const std::string &src,
      uint16_t permission) override;

  int64_t LogSetOwner(const std::string &src, const std::string &username,
      const std::string &groupname) override;

  int64_t LogConcat(const std::string &trg,
      const std::vector<std::string> &srcs, uint64_t timestamp,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogDelete(const std::string &src,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogGenerationStampV1(uint64_t genstamp) override;

  int64_t LogGenerationStampV2(uint64_t genstamp) override;

  int64_t LogAllocateBlockId(uint64_t blockId) override;

  int64_t LogBlockIdAndGSv2(uint64_t* blockId, uint64_t* genstamp) override;

  int64_t LogTimes(const std::string &src, uint64_t mtime,
      uint64_t atime) override;

  int64_t LogSymlink(const std::string &path,
      const INode &node, const LogRpcInfo& log_rpc_info) override;

  int64_t LogReassignLease(const std::string &lease_holder,
      const std::string &src, const std::string &new_holder) override;

  int64_t LogSetAcl(const std::string &src,
      const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
        entries) override;

  int64_t LogSetXAttrs(const std::string &src,
      const google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogRemoveXAttrs(const std::string &src,
      const google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const LogRpcInfo& log_rpc_info) override;

  int64_t LogAccessCounterSnapshot(
      const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot) override;  // NOLINT(whitespace/line_length)

  int64_t LogSetBlockPufsInfo(const std::string& s) override;
  int64_t LogDeleteDeprecatedBlockPufsInfo(uint64_t blk_id) override;

  // Snapshot
  int64_t LogAllowSnapshot(const std::string& path) override;
  int64_t LogDisallowSnapshot(const std::string& path) override;
  int64_t LogCreateSnapshot(const std::string& path,
                            const std::string& name) override;
  int64_t LogDeleteSnapshot(const std::string& path,
                            const std::string& name) override;
  int64_t LogRenameSnapshot(const std::string& path,
                            const std::string& old_name,
                            const std::string& new_name) override;
  int64_t LogAllowSnapshotV2(const std::string& path,
                             const INode& inode) override;
  int64_t LogDisallowSnapshotV2(const std::string& path,
                                const INode& inode) override;
  int64_t LogCreateSnapshotV2(const std::string& path,
                              const std::string& name,
                              uint64_t timestamp_in_ms,
                              const INode& inode,
                              uint64_t new_snapshot_id) override;
  int64_t LogDeleteSnapshotV2(const std::string& path,
                              const std::string& name,
                              uint64_t timestamp_in_ms,
                              const INode& inode) override;
  int64_t LogRenameSnapshotV2(const std::string& path,
                              const std::string& old_name,
                              const std::string& new_name,
                              uint64_t timestamp_in_ms,
                              const INode& inode,
                              const SnapshotLog& inode_snaplog) override;

  // Batch API
  int64_t LogBatchCreateFile(
      const std::vector<std::string>& paths,
      const std::vector<INode>& inodes,
      const std::vector<INode>& overwritten_inodes,
      const std::vector<BlockInfoProto>& overwritten_bips,
      const std::vector<BlockInfoProto>& add_block_bips,
      const std::vector<std::vector<std::string>>& bips_expected_locs,
      // other
      const INode& parent,
      uint64_t timestamp_in_ms,
      const LogRpcInfo& log_rpc_info) override;
  int64_t LogBatchCompleteFile(const std::vector<std::string>& paths,
                               const std::vector<INode>& complete_inodes,
                               const std::vector<INode>& deleted_inodes,
                               const std::vector<BlockInfoProto>& bips,
                               const std::vector<BlockInfoProto>& deleted_bips,
                               const std::vector<INode>& original_inodes,
                               // other
                               const INode& parent,
                               uint64_t timestamp_in_ms,
                               const LogRpcInfo& log_rpc_info) override;
  int64_t LogBatchDeleteFile(const std::vector<std::string>& path,
                             const std::vector<INode>& inode,
                             const std::vector<BlockInfoProto>& bips,
                             // other
                             const INode& parent,
                             uint64_t timestamp_in_ms,
                             const LogRpcInfo& log_rpc_info) override;

  void Sync(bool force = false) override;

 private:
  std::shared_ptr<EditLogContextBase> context_;

  MetricID check_is_open_for_write_time_;
  MetricID serialize_time_;
  MetricID call_context_time_;
};

}  // namespace dancenn

#endif  // EDIT_SENDER_H_

