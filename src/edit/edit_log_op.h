// Copyright 2017 He <PERSON>ianyi <<EMAIL>>

#ifndef EDIT_EDIT_LOG_OP_H_
#define EDIT_EDIT_LOG_OP_H_

#include <cstdint>
#include <string>
#include <memory>
#include <utility>

#include "base/constants.h"
#include "base/pb_converter.h"
#include "base/to_json_string.h"
#include "cnetpp/base/csonpp.h"

namespace dancenn {

enum OpCode : int8_t {
  OP_ADD = 0,
  OP_RENAME_OLD = 1,
  OP_DELETE = 2,
  OP_MKDIR = 3,
  OP_SET_REPLICATION = 4,
  OP_DATANODE_ADD = 5,
  OP_DATANODE_REMOVE = 6,
  OP_SET_PERMISSIONS = 7,
  OP_SET_OWNER = 8,
  OP_CLOSE = 9,
  OP_SET_GENSTAMP_V1 = 10,
  OP_SET_NS_QUOTA = 11,
  OP_CLEAR_NS_QUOTA = 12,
  OP_TIMES = 13,
  OP_SET_QUOTA = 14,
  OP_RENAME = 15,
  OP_CONCAT_DELETE = 16,
  OP_SYMLINK = 17,
  OP_GET_DELEGATION_TOKEN = 18,
  OP_RENEW_DELEGATION_TOKEN = 19,
  OP_CANCEL_DELEGATION_TOKEN = 20,
  OP_UPDATE_MASTER_KEY = 21,
  OP_REASSIGN_LEASE = 22,
  OP_END_LOG_SEGMENT = 23,
  OP_START_LOG_SEGMENT = 24,
  OP_UPDATE_BLOCKS = 25,
  OP_CREATE_SNAPSHOT = 26,
  OP_DELETE_SNAPSHOT = 27,
  OP_RENAME_SNAPSHOT = 28,
  OP_ALLOW_SNAPSHOT = 29,
  OP_DISALLOW_SNAPSHOT = 30,
  OP_SET_GENSTAMP_V2 = 31,
  OP_ALLOCATE_BLOCK_ID = 32,
  OP_ADD_BLOCK = 33,
  OP_ADD_CACHE_DIRECTIVE = 34,
  OP_REMOVE_CACHE_DIRECTIVE = 35,
  OP_ADD_CACHE_POOL = 36,
  OP_MODIFY_CACHE_POOL = 37,
  OP_REMOVE_CACHE_POOL = 38,
  OP_MODIFY_CACHE_DIRECTIVE = 39,
  OP_SET_ACL = 40,
  OP_ROLLING_UPGRADE_START = 41,
  OP_ROLLING_UPGRADE_FINALIZE = 42,
  OP_SET_XATTR = 43,
  OP_REMOVE_XATTR = 44,
  OP_SET_STORAGE_POLICY = 45,
  OP_SET_DIR_REPLICA_POLICY = 125,
  OP_SET_REPLICA_POLICY = 126,
  OP_ACCESS_COUNTER_SNAPSHOT = 127,
  // [100, 102] ops are deprecated.
  OP_SET_CFS_UNIVERSAL_INFO = 100,
  OP_SET_BLOCK_PUFS_INFO = 101,
  OP_DELETE_DEPRECATED_BLOCK_PUFS_INFO = 102,

  // Ops related to CloudFS.
  // According to https://bytedance.feishu.cn/docs/doccnkJRWgnda6dPuop1JyVjKQg,
  // ops are serialized and deserialized in both Java and C++ side.
  // We need to modify both Java and C++ codes when adding a new op,
  // which is annoying.
  // To avoid this, I add cfs op containing a protobuf field which can hold
  // any content, serialization and deserialization happen only in C++ side
  // for this field.
  // Please follow this rule when developing CloudFS.
  OP_CFS = 111,
  // Please don't add new op when developing CloudFS.

  OP_INVALID = -1,
  OP_WAIT_NO_PENDING = -2,
};

inline void ToJson(OpCode op_code, cnetpp::base::Value* value) {
  CHECK_NOTNULL(value);
  *value = static_cast<int>(op_code);
}

class EditLogOp {
 public:
  virtual ~EditLogOp() { }
  OpCode op_code() const {
    return op_code_;
  }
  int64_t txid() const {
    return txid_;
  }
  void SetTxid(int64_t txid) {
    txid_ = txid;
  }
  bool valid() {
    return this->txid_ != kInvalidTxId;
  }
  void SetOpCode(int8_t op_code) {
    op_code_ = static_cast<OpCode>(op_code);
  }
  void SetOpCode(OpCode op_code) {
    op_code_ = op_code;
  }
  virtual void ReadFields(std::stringstream* ss) = 0;
  virtual void WriteFields(std::stringstream *ss,
                           bool should_write_base_param = true) const = 0;
  virtual std::string op_name() const = 0;

  std::string SerializeToJsonString() const {
    return cnetpp::base::Parser::Serialize(SerializeToJson());
  }
  
  virtual cnetpp::base::Value SerializeToJson() const = 0;

 protected:
  OpCode op_code_;
  int64_t txid_ = kInvalidTxId;
};

class OpWaitNoPending: public EditLogOp {
 public:
  static std::shared_ptr<EditLogOp> New() {
    return std::shared_ptr<EditLogOp>(new OpWaitNoPending());
  }

  cnetpp::base::Value SerializeToJson() const override {
    cnetpp::base::Object obj;
    obj["opCode"] = static_cast<int>(op_code_);
    obj["opName"] = op_name();
    obj["txid"] = txid_;
    return cnetpp::base::Value(std::move(obj));
  }

 public:
  OpWaitNoPending() {
    op_code_ = OP_WAIT_NO_PENDING;
  }
  void ReadFields(std::stringstream*) override {}
  void WriteFields(std::stringstream*, bool) const override {}
  std::string op_name() const override {
    return "OP_WAIT_NO_PENDING";
  }
};

}  // namespace dancenn

#endif  // EDIT_EDIT_LOG_OP_H_
