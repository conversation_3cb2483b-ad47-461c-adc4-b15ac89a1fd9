#!/usr/bin/python
#coding: utf-8
# Generate headers for every editlog op. Not using protobuf.
import re, sys, os

def get_opcode(name):
  return 'OP_%s' % re.sub(r'([A-Z])', r'_\1', name).upper()[1:]

def from_opcode(code):
  sb = ''
  for mch in re.finditer(r'([^_]+)', code):
    s = mch.group(1)
    sb += s.lower().title()
  return sb

def handle_op(output_path, op_class_name, op_code, fields):
  fn = '%s.h' % op_code.lower()
  sys.stderr.write('Generated op/%s\n' % (fn))
  with open('%s/%s' % (output_path, fn), 'w') as f:
    header_name = 'EDIT_OP_%s_H_' % op_code
    public = ''
    private = ''
    reads = ''
    writes = '    if (should_write_base_param) {\n      WriteField(&op_code_,  ss);\n      WriteField(&txid_, ss);\n    }\n'
    jsons = '  cnetpp::base::Value SerializeToJson() const override {\n'
    jsons += '    cnetpp::base::Object obj;\n'
    jsons += '    cnetpp::base::Value value;\n'
    jsons += '    ToJson(op_code_, &value);\n'
    jsons += '    obj["opCode"] = std::move(value);\n'
    jsons += '    ToJson(op_name(), &value);\n'
    jsons += '    obj["opName"] = std::move(value);\n'
    jsons += '    ToJson(txid_, &value);\n'
    jsons += '    obj["txid"] = std::move(value);\n'
    for const, field, ref, name in fields:
      return_type = field + ref
      if len(const) > 0:
        return_type = const + " " + return_type
      public += '''  %s %s() const {
    return %s_;
  }
  void set_%s(const %s &v) {
    %s_ = v;
  }\n''' % (return_type, name, name, name, field, name)
      private += '  %s %s_;\n' % (field, name)
      field_type = None
      if field.startswith('std::vector<'):
        field_type = 'Vector'
      elif field.startswith('Compact'):
        field_type = 'Compact'
      else:
        field_type = ''
      func_read_field = 'Read%sField' % field_type
      func_write_field = 'Write%sField' % field_type
      reads += '    %s(&%s_, ss);\n' % (func_read_field, name)
      writes += '    %s(&%s_, ss);\n' % (func_write_field, name)
      jsons += '    ToJson(%s_, &value);\n' % name
      jsons += '    obj["%s"] = std::move(value);\n' % name
    jsons += '    return cnetpp::base::Value(std::move(obj));\n'
    jsons += '  }'
    f.write(
'''// Generated by gen.py. See edit_log_op.def. DO NOT MODIFY MANUALLY!
#ifndef %s
#define %s

#include <hdfs.pb.h>
#include <inode.pb.h>
#include <cstdint>

#include <vector>
#include <string>

#include "edit/serializer.h"
#include "edit/edit_log_op.h"
#include "edit/edit_types.h"
#include "proto/generated/cloudfs/fsimage.pb.h"
#include "proto/generated/cloudfs/xattr.pb.h"
#include "proto/generated/cloudfs/acl.pb.h"

namespace dancenn {

class %s: public EditLogOp {
 public:
%s
  void ReadFields(std::stringstream* ss) override {
%s
  }
  void WriteFields(std::stringstream* ss, bool should_write_base_param = true) const override {
%s
  }
  std::string op_name() const override {
    return "%s";
  }

%s
 private:
%s

};
}  // namespace dancenn
#endif  // %s
''' % (header_name, header_name, op_class_name, public, reads, writes, op_code, jsons, private, header_name))
  return fn

def gen_const_type_name(line):
  # std::string xxx;
  # const|constexpr std::string xxx;
  # const|constexpr std::string&|&& xxx;
  # const|constexpr std::string &| && xxx;
  chunks = re.split(r'\s+', line.strip().replace(';', ''))

  const = ""
  if chunks[0].strip() == "const":
    const = "const"
    chunks = chunks[1:]
  elif chunks[0].strip() == "constexpr":
    const = "constexpr"
    chunks = chunks[1:]

  type_ = chunks[0].strip()
  chunks = chunks[1:]

  ref = ""
  if type_.endswith("&&"):
    ref = "&&"
    type_ = type_[:-2]
  elif type_.endswith("&"):
    ref = "&"
    type_ = type_[:-1]
  elif chunks[0].strip() == "&" or chunks[0].strip() == "&&":
    ref = chunks[0].strip()
    chunks = chunks[1:]

  name = chunks[0].strip()
  return (const, type_, ref, name)

def gen(input, output_path):
  with open(input, 'r') as f:
    lines = f.read().strip().split('\n')
  i = 0
  op_class_name = None
  op_code = None
  fields = []
  headers = []
  ctors = []
  while i < len(lines):
    line = lines[i]
    i += 1
    if len(line) == 0:
      continue
    if line.startswith(' '):
      fields.append(gen_const_type_name(line))
    else:
      if op_code is not None:
        headers.append(handle_op(output_path, op_class_name, op_code, fields))
        ctors.append((op_code, op_class_name, ))
        fields = []
      op_class_name = from_opcode(line)
      op_code = line
  with open('%s/all.h' % output_path, 'w') as f:
      includes = '\n'.join(map(lambda x: '#include <edit/op/%s>' % x, headers))
      ctors = '\n'.join(map(lambda x: '  {%s, []() -> EditLogOp* {return new %s();}}, ' % x, ctors));
      f.write(
'''// Generated by gen.py. See edit_log_op.def. DO NOT MODIFY MANUALLY!
#ifndef EDIT_OP_ALL_H_
#define EDIT_OP_ALL_H_

#include <glog/logging.h>

%s

#include <map>
#include <functional>

namespace dancenn {

static const std::map<uint8_t, std::function<EditLogOp*()>> kOpCodeToCtor = {
%s
};

class OpInstantizer {
 public:
  EditLogOp* Instantiate(uint8_t op_code) {
    const auto &it = kOpCodeToCtor.find(op_code);
    CHECK_EQ(it != kOpCodeToCtor.end(), true);
    return it->second();
  }
};

}  // namespace dancenn
#endif  // EDIT_OP_ALL_H_
''' % (includes, ctors))

if __name__ == '__main__':
  home = os.path.dirname(os.path.realpath(__file__))
  try:
    os.mkdir('%s/op' % home)
  except:
    pass
  gen('%s/edit_log_op.def' % home, '%s/op' % home)
