// Copyright 2017 He <PERSON> <<EMAIL>>

#ifndef EDIT_EDIT_TYPES_H_
#define EDIT_EDIT_TYPES_H_

#include <hdfs.pb.h>
#include <cstdint>

#include <string>
#include <vector>

#include "base/to_json_string.h"

namespace dancenn {

using CompactBlockArray = std::vector<::cloudfs::BlockProto>;

enum class RenameOption : int8_t {
  kNone = 0,  // No options
  kOverwrite = 1,  // Overwrite the rename destination
};

class RenameOptions {
 public:
  RenameOptions(): value_("") {}
  explicit RenameOptions(const std::string &value): value_(value) {}
  explicit RenameOptions(RenameOption option) {
    value_.resize(1);
    value_[0] = static_cast<char>(option);
  }
  void Assign(const std::string &value) {
    value_ = value;
  }
  std::vector<RenameOption> options() const {
    std::vector<RenameOption> result;
    result.reserve(value_.size());
    for (const char &c : value_) {
      result.push_back(static_cast<RenameOption>(c));
    }
    return result;
  }
  bool overwrite() const {
    for (const char &c : value_) {
      if (static_cast<RenameOption>(c) == RenameOption::kOverwrite) {
        return true;
      }
    }
    return false;
  }
  std::string value() const {
    return value_;
  }

 private:
  std::string value_;
};

inline void ToJson(const RenameOptions &t, cnetpp::base::Value *value) {
  CHECK_NOTNULL(value);
  *value = t.value();
}

}  // namespace dancenn

#endif  // EDIT_EDIT_TYPES_H_
