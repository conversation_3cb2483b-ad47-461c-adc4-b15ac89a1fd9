// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/10/30
// Description

#ifndef EDIT_EDIT_LOG_CONTEXT_BASE_H_
#define EDIT_EDIT_LOG_CONTEXT_BASE_H_

#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <atomic>  
#include <cstdint>  // For int64_t, etc.
#include <memory>   // For unique_ptr, shared_ptr.
#include <sstream>  // For stringstream.
#include <string>   // For string.

#include "base/status.h"                  // For Status.
#include "edit/edit_log_sync_listener.h"  // For IEditLogSyncListener.

namespace dancenn {

class EditLogInputContextBase {
 public:
  EditLogInputContextBase() = default;
  virtual ~EditLogInputContextBase() = default;

  // Return value:
  // true: read Op success. `serialized_op` will be set.
  // false: IO error has occurred. The caller can try again.
  virtual bool ReadOp(std::string* serialized_op) = 0;
};

class EditLogContextBase {
 public:
  EditLogContextBase() = default;
  virtual ~EditLogContextBase() = default;

  // Common methods.
  virtual EditLogConf::HAMode GetHAMode() = 0;
  virtual bool UpdateConfProperty(const std::string& name,
                                  const std::string& value) = 0;
  virtual void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) = 0;
  virtual std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() = 0;
  virtual bool GetPeerNNAddr(std::string* addr) = 0;
  virtual bool GetAllStackTraces(std::string* stack_info) = 0;

  // Consider the validity of the active lease: If the process has not
  // successfully written data for a period of time, it cannot be considered
  // that the process is valid and active.
  virtual bool IsActiveInLease() const = 0;

  // Read related.
  virtual bool IsOpenForRead() = 0;
  virtual void OpenForRead() = 0;
  virtual std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid,
      int64_t to_at_least_txid,
      bool is_progress_ok) = 0;
  // Write related.
  virtual bool IsOpenForWrite() = 0;
  virtual void InitJournalsForWrite() = 0;
  virtual int64_t OpenForWrite() = 0;
  // Close.
  virtual void Close() = 0;

  // TxID, BlockID and GS related.
  virtual int64_t GetLastWrittenTxId() = 0;
  virtual void SetNextTxId(int64_t) = 0;
  virtual uint64_t GetLastAllocatedBlockId() = 0;
  virtual void SetLastAllocatedBlockId(int64_t id) = 0;
  virtual uint64_t GetLastGenerationStampV2() = 0;
  virtual void SetLastGenerationStampV2(int64_t gsv2) = 0;
  virtual int64_t GetCurSegmentTxId() = 0;

  // Sync related.
  virtual int64_t GetWaitSyncTime() = 0;
  virtual void LogSync(bool force = false) = 0;
  virtual void LogSyncAll() = 0;

  virtual int64_t RollEditLog() = 0;
  virtual void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) = 0;

  // Not deprecated.
  virtual int64_t LogCfsOp(const std::stringstream* ss) = 0;
  // Block related.
  virtual int64_t LogAllocateBlockId(const std::stringstream* ss,
                                     uint64_t* id) = 0;
  virtual int64_t LogGenerationStampV1(const std::stringstream* ss) = 0;
  virtual int64_t LogGenerationStampV2(const std::stringstream* ss,
                                       uint64_t* gsv2) = 0;
  virtual int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                            const std::stringstream* gsv2_ss,
                                            uint64_t* blkid,
                                            uint64_t* gsv2) = 0;
  // Dir tree related.
  virtual int64_t LogTimes(const std::stringstream* ss) = 0;

  // Deprecated.
  // File related.
  virtual int64_t LogOpenFile(const std::stringstream* ss,
                              bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogAddBlock(const std::stringstream* ss) = 0;
  virtual int64_t LogUpdateBlocks(const std::stringstream* ss,
                                  bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogCloseFile(const std::stringstream* ss) = 0;
  virtual int64_t LogReassignLease(const std::stringstream* ss) = 0;
  virtual int64_t LogConcat(const std::stringstream* ss,
                            bool to_log_rpc_ids = false) = 0;
  // Block related.
  virtual int64_t LogSetBlockPufsInfo(const std::stringstream* ss) = 0;
  virtual int64_t LogDeleteDeprecatedBlockPufsInfo(
      const std::stringstream* ss) = 0;
  // Dir tree related.
  virtual int64_t LogMkDir(const std::stringstream* ss) = 0;
  virtual int64_t LogDelete(const std::stringstream* ss,
                            bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogRenameOld(const std::stringstream* ss,
                               bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogRename(const std::stringstream* ss,
                            bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogSymlink(const std::stringstream* ss,
                             bool to_log_rpc_ids = false) = 0;
  // Set* related.
  virtual int64_t LogSetReplication(const std::stringstream* ss) = 0;
  virtual int64_t LogSetStoragePolicy(const std::stringstream* ss) = 0;
  virtual int64_t LogSetReplicaPolicy(const std::stringstream* ss) = 0;
  virtual int64_t LogSetDirReplicaPolicy(const std::stringstream* ss) = 0;
  virtual int64_t LogSetQuota(const std::stringstream* ss) = 0;
  virtual int64_t LogSetPermissions(const std::stringstream* ss) = 0;
  virtual int64_t LogSetOwner(const std::stringstream* ss) = 0;
  virtual int64_t LogSetAcl(const std::stringstream* ss) = 0;
  virtual int64_t LogSetXAttrs(const std::stringstream* ss,
                               bool to_log_rpc_ids = false) = 0;
  virtual int64_t LogRemoveXAttrs(const std::stringstream* ss,
                                  bool to_log_rpc_ids = false) = 0;
  // Snapshot related.
  virtual int64_t LogAllowSnapshot(const std::stringstream* ss) = 0;
  virtual int64_t LogDisallowSnapshot(const std::stringstream* ss) = 0;
  virtual int64_t LogCreateSnapshot(const std::stringstream* ss) = 0;
  virtual int64_t LogDeleteSnapshot(const std::stringstream* ss) = 0;
  virtual int64_t LogRenameSnapshot(const std::stringstream* ss) = 0;
  virtual int64_t LogAccessCounterSnapshot(const std::stringstream* ss) = 0;

  // Switch related.
  virtual EditLogConf::PreviousEditLogConf HASwitchFence() = 0;
  virtual Status SwitchNonHAActiveToHAActive() = 0;
  virtual Status SwitchHAActiveToNonHAActive() = 0;
  virtual Status SwitchHAStandbyToNonHAActive() = 0;

  void SetFailed() {
    failed_.store(true);
  }
  void ResetFailed() {
    failed_.store(false);
  }
  bool IsFailed() {
    return failed_.load();
  }

 protected:
  std::atomic<bool> failed_{false};
};

}  // namespace dancenn

#endif  // EDIT_EDIT_LOG_CONTEXT_BASE_H_
