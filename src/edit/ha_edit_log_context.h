// Copyright 2017 He <PERSON>yi <<EMAIL>>

#ifndef HA_EDIT_EDIT_LOG_CONTEXT_H_
#define HA_EDIT_EDIT_LOG_CONTEXT_H_

#include <jni.h>  // For jclass, jobject, jmethodID, etc.
#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <atomic>   // For atomic.
#include <cstdint>  // For int64_t, etc.
#include <memory>   // For unique_ptr, shared_ptr.
#include <mutex>    // For mutex.
#include <sstream>  // For stringstream.
#include <string>   // For string.

#include "base/java.h"                  // For JavaRuntime.
#include "base/metrics.h"               // For MetricID.
#include "base/read_write_lock.h"       // For ReadWriteLock.
#include "cnetpp/concurrency/thread.h"  // For Thread.
#include "edit/edit_log_context_base.h"  // For EditLogInputContextBase, EditLogContextBase.

namespace dancenn {

extern const char* kEditLogConfDisallowBookieRegion;

class HAEditLogInputContext : public EditLogInputContextBase {
 public:
  HAEditLogInputContext(JavaRuntime* jvm, jclass clazz, jobject obj);
  virtual ~HAEditLogInputContext();

  bool ReadOp(std::string* serialized_op) override;

 private:
  JavaRuntime* jvm_{nullptr};
  std::unique_ptr<JavaObject> java_obj_;
  jmethodID method_read_op_to_byte_buffer_;
  jmethodID method_close_;
  jmethodID method_bb_position_;
  std::string buffer_;
  std::unique_ptr<JavaObject> bb_;
};

class BGEditLogSyncer;
class BGEditLogCommitter;
class IEditLogSyncListener;

class HAEditLogContext : public EditLogContextBase {
 public:
  HAEditLogContext(JavaRuntime* jvm, int max_producers);
  // TestOnly.
  HAEditLogContext();
  virtual ~HAEditLogContext();

  // Common methods.
  EditLogConf::HAMode GetHAMode() override;
  bool UpdateConfProperty(const std::string& name,
                          const std::string& value) override;
  void SetupCommitter(
      std::shared_ptr<BGEditLogCommitter> bg_edit_log_committer);
  void SetUpSyncer(std::shared_ptr<BGEditLogSyncer> bg_edit_log_syncer);
  void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) override;
  std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() override;
  bool GetPeerNNAddr(std::string* addr) override;
  bool GetAllStackTraces(std::string* stack_info) override;

  // see EditLogContextBase::IsActiveInLease
  bool IsActiveInLease() const override;

  // Read related.
  bool IsOpenForRead() override;
  void OpenForRead() override;
  std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid,
      int64_t to_at_least_txid,
      bool is_progress_ok) override;
  // Write related.
  bool IsOpenForWrite() override;
  void InitJournalsForWrite() override;
  int64_t OpenForWrite() override;
  // Close.
  void Close() override;

  // TxID, BlockID and GS related.
  int64_t GetLastWrittenTxId() override;
  void SetNextTxId(int64_t tx_id) override;
  uint64_t GetLastAllocatedBlockId() override;
  void SetLastAllocatedBlockId(int64_t id) override;
  uint64_t GetLastGenerationStampV2() override;
  void SetLastGenerationStampV2(int64_t gsv2) override;
  int64_t GetCurSegmentTxId() override;

  // Sync related.
  int64_t GetWaitSyncTime() override;
  void LogSync(bool force = false) override;
  void LogSyncAll() override;

  int64_t RollEditLog() override;
  void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) override;

  // Not deprecated.
  int64_t LogCfsOp(const std::stringstream* ss) override;
  // Block related.
  int64_t LogAllocateBlockId(const std::stringstream* ss,
                             uint64_t* id) override;
  int64_t LogGenerationStampV1(const std::stringstream* ss) override;
  int64_t LogGenerationStampV2(const std::stringstream* ss,
                               uint64_t* gsv2) override;
  int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                    const std::stringstream* gsv2_ss,
                                    uint64_t* blkid,
                                    uint64_t* gsv2) override;
  // Dir tree related.
  int64_t LogTimes(const std::stringstream* ss) override;

  // Deprecated.
  // File related.
  int64_t LogOpenFile(const std::stringstream* ss,
                      bool to_log_rpc_ids = false) override;
  int64_t LogAddBlock(const std::stringstream* ss) override;
  int64_t LogUpdateBlocks(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  int64_t LogCloseFile(const std::stringstream* ss) override;
  int64_t LogReassignLease(const std::stringstream* ss) override;
  int64_t LogConcat(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  // Block related.
  int64_t LogSetBlockPufsInfo(const std::stringstream* ss) override;
  int64_t LogDeleteDeprecatedBlockPufsInfo(
      const std::stringstream* ss) override;
  // Dir tree related.
  int64_t LogMkDir(const std::stringstream* ss) override;
  int64_t LogDelete(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogRenameOld(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRename(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogSymlink(const std::stringstream* ss,
                     bool to_log_rpc_ids = false) override;
  // Set* related.
  int64_t LogSetReplication(const std::stringstream* ss) override;
  int64_t LogSetStoragePolicy(const std::stringstream* ss) override;
  int64_t LogSetReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetDirReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetQuota(const std::stringstream* ss) override;
  int64_t LogSetPermissions(const std::stringstream* ss) override;
  int64_t LogSetOwner(const std::stringstream* ss) override;
  int64_t LogSetAcl(const std::stringstream* ss) override;
  int64_t LogSetXAttrs(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRemoveXAttrs(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  // Snapshot related.
  int64_t LogAllowSnapshot(const std::stringstream* ss) override;
  int64_t LogDisallowSnapshot(const std::stringstream* ss) override;
  int64_t LogCreateSnapshot(const std::stringstream* ss) override;
  int64_t LogDeleteSnapshot(const std::stringstream* ss) override;
  int64_t LogRenameSnapshot(const std::stringstream* ss) override;
  int64_t LogAccessCounterSnapshot(const std::stringstream* ss) override;

  std::string OpMethodName(jmethodID method) const;
  void OpMethodName(jmethodID method, const std::string& name);

  // Switch related.
  EditLogConf::PreviousEditLogConf HASwitchFence() override;
  Status SwitchNonHAActiveToHAActive() override;
  Status SwitchHAActiveToNonHAActive() override;
  Status SwitchHAStandbyToNonHAActive() override;
  void StopBGEditLogWorker();

 protected:
  void InitOpMethods(jclass klass);

  // These methods interact directly with the JVM.
  virtual int64_t CallJavaOpVoidMethodL(jmethodID method);
  virtual int64_t CallJavaOpMethod(jmethodID method,
                                   const std::string& msg,
                                   bool with_rpc_id,
                                   bool to_log_rpc_ids = false);
  virtual int64_t CallJavaOpMethodLL(jmethodID method,
                                     const std::string& msg,
                                     uint64_t* value);

  // These methods generate EditLogTasks and subsequently commit them.
  int64_t CallJavaOpMethod(jmethodID method,
                           const std::stringstream* ss,
                           bool with_rpc_id = false,
                           bool to_log_rpc_ids = false);
  int64_t CallJavaOpMethodLL(jmethodID method,
                             const std::stringstream* ss,
                             uint64_t* value);
  int64_t CallJavaOpMethodLLLL(jmethodID method,
                               const std::stringstream* blkid_ss,
                               const std::stringstream* gsv2_ss,
                               uint64_t* blkid,
                               uint64_t* gsv2);

  int64_t CloseInternal();

  void StartSync();
  void FinishSync();

  void LogSyncInternal();
  virtual void LogSyncAllInternal();

  void StartBGEditLogWorker(int max_producers);

 protected:
  ReadWriteLock rwlock_;
  JavaRuntime* jvm_{nullptr};
  std::unique_ptr<JavaObject> java_obj_;
  std::shared_ptr<JavaObject> java_conf_obj_;
  bool is_open_for_write_;
  jclass class_input_context_;
  std::unordered_map<uint64_t, std::string> op_method_names_;

  // Common methods.
  jmethodID method_update_conf_property_;
  jmethodID method_get_peer_nn_addr_;
  jmethodID method_get_all_stack_traces_;

  // Read related.
  jmethodID method_is_open_for_read_;
  jmethodID method_open_for_read_;
  jmethodID method_create_input_context_;
  // Write related.
  jmethodID method_is_open_for_write_;
  jmethodID method_init_journals_for_write_;
  jmethodID method_open_for_write_;
  jmethodID method_use_bk_editlog_os_;
  // Close.
  jmethodID method_close_;

  // TxID, BlockID and GS related.
  jmethodID method_get_last_written_tx_id_;
  jmethodID method_set_next_tx_id_;
  jmethodID method_get_last_allocated_block_id_;
  jmethodID method_set_last_allocated_block_id_;
  jmethodID method_get_last_generation_stamp_v2_;
  jmethodID method_set_last_generation_stamp_v2_;
  jmethodID method_get_cur_segment_tx_id_;

  // Sync related.
  std::atomic<int64_t> sync_onfly_{0};
  std::atomic<int64_t> start_sync_time_{0};
  std::atomic<int64_t> finish_sync_time_{0};
  std::mutex sync_mutex_;

  jmethodID method_log_sync_;
  jmethodID method_log_sync_all_;

  jmethodID method_roll_edit_log_;
  jmethodID method_purge_logs_older_than_;

  // Not deprecated.
  jmethodID method_log_cfs_op_;
  // Block related.
  jmethodID method_log_allocate_block_id_;
  jmethodID method_log_generation_stamp_v1_;
  jmethodID method_log_generation_stamp_v2_;
  // Dir tree related.
  jmethodID method_log_times_;

  // Deprecated.
  // File related.
  jmethodID method_log_open_file_;
  jmethodID method_log_add_block_;
  jmethodID method_log_update_blocks_;
  jmethodID method_log_close_file_;
  jmethodID method_log_reassign_lease_;
  jmethodID method_log_concat_;
  // Block related.
  jmethodID method_log_set_cfs_universal_info_;
  // Dir tree related.
  jmethodID method_log_mk_dir_;
  jmethodID method_log_delete_;
  jmethodID method_log_rename_old_;
  jmethodID method_log_rename_;
  jmethodID method_log_symlink_;
  // Set* related.
  jmethodID method_log_set_replication_;
  jmethodID method_log_set_storage_policy_;
  jmethodID method_log_set_replica_policy_;
  jmethodID method_log_set_dir_replica_policy_;
  jmethodID method_log_set_quota_;
  jmethodID method_log_set_permission_;
  jmethodID method_log_set_owner_;
  jmethodID method_log_set_acl_;
  jmethodID method_log_set_xttrs_;
  jmethodID method_log_remove_xattrs_;
  // Snapshot related.
  jmethodID method_log_access_counters_snapshot_;

  std::atomic<bool> is_bg_worker_running_;
  std::unique_ptr<cnetpp::concurrency::Thread> bg_edit_log_commit_worker_;
  std::shared_ptr<BGEditLogCommitter> bg_edit_log_committer_;
  std::unique_ptr<cnetpp::concurrency::Thread> bg_edit_log_sync_worker_;
  std::shared_ptr<BGEditLogSyncer> bg_edit_log_syncer_;

  MetricID create_input_context_time_;
  MetricID create_input_context_num_;
  MetricID edit_log_time_;
  MetricID edit_sync_time_;

  // Refer to FSEditLog::lastAllocatedBlockID.
  std::atomic<uint64_t> last_allocated_block_id_;
  // Refer to FSEditLog::lastGenerationStampV2.
  std::atomic<uint64_t> last_generation_stamp_v2_;

 private:
  friend class BGEditLogSyncer;
  friend class BGEditLogCommitter;
};

}  // namespace dancenn

#endif  // HA_EDIT_EDIT_LOG_CONTEXT_H_
