// Copyright 2018 livexmm <<EMAIL>>

#ifndef EDIT_EDIT_LOG_TASK_H_
#define EDIT_EDIT_LOG_TASK_H_

#include <stdint.h>
#include <jni.h>
#include "base/java.h"
#include "base/count_down_latch.h"
#include <sstream>

namespace dancenn {
  class EditLogTask {
    public:
      explicit EditLogTask(jmethodID method, const std::string& msg,
          bool with_rpc_id = false, bool to_log_rpc_ids = false)
        : method_(method),
          msg_(msg),
          with_rpc_id_(with_rpc_id),
          to_log_rpc_ids_(to_log_rpc_ids),
          has_extra_(false),
          latch_(1),
          txid_(-1),
          value_(0),
          extra_value_(0) {}

      ~EditLogTask() {};

      void OK() { latch_.CountDown(); }
      void Await() { latch_.Await(); }

      jmethodID Method() const { return method_; }

      const std::string& Msg() const { return msg_; };
      const std::string& ExtraMsg() const { return extra_msg_; }

      void ExtraMsg(const std::string& msg) {
        extra_msg_ = msg;
        has_extra_ = true;
      }


      bool WithRpcID() const { return with_rpc_id_; }
      bool ToLogRpcIds() const { return to_log_rpc_ids_; }
      bool HasExtra() const { return has_extra_; }

      int64_t Txid() const { return txid_; }
      void Txid(int64_t txid) { txid_ = txid; }

      uint64_t Value() const { return value_; }
      void Value(uint64_t value) { value_ = value; }

      uint64_t ExtraValue() const { return extra_value_; }
      void ExtraValue(uint64_t value) { extra_value_ = value; }
    private:
      jmethodID    method_;
      std::string  msg_;
      std::string  extra_msg_;
      bool         with_rpc_id_;
      bool         to_log_rpc_ids_;
      bool         has_extra_;

      CountDownLatch latch_;
      int64_t        txid_;
      uint64_t       value_;
      uint64_t       extra_value_;
  };
}

#endif
