#include "syncer.h"

#include <absl/strings/str_format.h>  // For StrFormat.
#include <gflags/gflags.h>            // For DECLARE_int32, etc.
#include <glog/logging.h>             // For LOG, etc.
#include <sched.h>                    // For sched_yield.
#include <unistd.h>                   // For usleep.

#include <shared_mutex>  // For shared_lock.

#include "base/constants.h"               // For kInvalidTxId.
#include "base/defer.h"                   // For DEFER.
#include "base/logger_metrics.h"          // For LoggerMetrics.
#include "edit/edit_log_sync_listener.h"  // For IEditLogSyncListener.
#include "edit/ha_edit_log_context.h"     // For HAEditLogContext.

DECLARE_int32(edit_log_sync_min_wait_time_us);

namespace dancenn {

BGEditLogSyncer::BGEditLogSyncer(HAEditLogContext* context, int max_buffers)
    : is_fenced_(false),
      context_(context),
      channel_(max_buffers),
      listener_(nullptr),
      already_finished_task_txid_(kInvalidTxId) {
}

void BGEditLogSyncer::SetupListener(
    std::shared_ptr<IEditLogSyncListener>& listener) {
  listener_ = listener;
}

void BGEditLogSyncer::SetNextTxId(int64_t txid) {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  LOG(INFO) << "BGEditLogSyncer::SetNextTxId txid=" << txid;
  // Refer to `FSEditLog::setNextTxId`.
  if (already_finished_task_txid_ != kInvalidTxId) {
    CHECK_GE(txid, already_finished_task_txid_ + 1);
  }
  already_finished_task_txid_ = txid - 1;
}

bool BGEditLogSyncer::Close(int64_t close_task_txid) {
  // `HAEditLogContext::method_close_` is very ugly.
  // See `HAEditLogContext::CloseInternal` for more infos.
  DLOG(INFO) << "BGEditLogSyncer::Close txid=" << close_task_txid;
  while (true) {
    {
      std::lock_guard<std::mutex> _(consumer_mtx_);
      if (UNLIKELY(is_fenced_)) {
        return false;
      }
      CHECK_LE(already_finished_task_txid_, close_task_txid);
      if (already_finished_task_txid_ < close_task_txid - 1) {
        LOG(INFO) << "Still waiting in BGEditLogSyncer::Close"
                  << ", already_finished_task_txid_="
                  << already_finished_task_txid_
                  << ", close_task_txid=" << close_task_txid;
      } else if (already_finished_task_txid_ == close_task_txid - 1) {
        CHECK(channel_.Empty());
        already_finished_task_txid_ = close_task_txid;
        return true;
      } else if (already_finished_task_txid_ == close_task_txid) {
        CHECK(channel_.Empty());
        // This is the situation:
        // The `HAEditLogContext::InitJournalsForWrite` is invoked followed by
        // `Close`, without calling `OpenForWrite` in between. See
        // EditLogRecoveryTest.InitJournalsForWriteThenRead for more infos.
        LOG(INFO) << "BGEditLogSyncer::Close exits";
        return true;
      }
    }
    usleep(10);
  }
}

bool BGEditLogSyncer::Sync(int64_t txid) {
  std::shared_lock<ReadWriteLock> _(producer_lock_);
  DLOG(INFO) << "BGEditLogSyncer::Sync txid=" << txid;
  // In this context, we rely on the lock acquisition ordering property of
  // `ReadWriteLock`. Specifically, if thread-A attempts to acquire the read
  // lock first, followed by thread-B's attempt to acquire the read lock, and
  // finally thread-C's attempt to acquire the write lock, the read locks for
  // thread-A and thread-B must be granted before the write lock for thread-C.
  // Maintaining this A-B-C sequence is crucial; deviations, such as in an A-C-B
  // sequence, could potentially lead to bugs.
  if (UNLIKELY(is_fenced_)) {
    return false;
  }
  CHECK_GT(txid, already_finished_task_txid_);
  while (!channel_.TryPush(txid)) {
    // Now never reach here, TryPush always return true
    // at future dancenn implement better pipeline may
    // reach here.
    sched_yield();
  }
  return true;
}

bool BGEditLogSyncer::operator()(void* arg) {
  (void)arg;
  LOG(INFO) << "Background edit log syncer starts...";

  while (!IsStopped()) {
    while (true) {
      // [begin_txid, end_txid)
      int64_t begin_txid = kInvalidTxId;
      int64_t end_txid = kInvalidTxId;
      Status s = TxStart(&begin_txid, &end_txid);
      if (UNLIKELY(s.code() == Code::kError)) {
        LOG(INFO) << "Background edit log syncer ends by ha switcher...";
        return true;
      }
      int64_t length = end_txid - begin_txid;
      if (length > 0) {
        // We need to discuss the code referenced in the following link:
        // https://code.byted.org/inf/dancenn/blob/41c6e9d11d4997ab6d97d2540978476d2e9ddf84/src/edit/syncer.cc#L41
        // The original source code executes an additional NewPopWindow
        // function. While this may potentially enhance efficiency, it might
        // also compromise the code's structure. We must consider whether the
        // trade-off is worthwhile.
        // if (length < channel_.Capacity() / 2) {
        //   channel_.NewPopWindow();
        // }
        context_->LogSyncAllInternal();
        if (context_->IsFailed()) {
          break;
        }
        s = TxFinish(begin_txid, end_txid);
        if (UNLIKELY(s.code() == Code::kError)) {
          LOG(INFO) << "Background edit log syncer ends by ha switcher...";
          return true;
        }
      }
      if (length < channel_.Capacity() / 2) {
        break;
      }
    }
    usleep(FLAGS_edit_log_sync_min_wait_time_us);
  }
  if (context_->IsFailed()) {
    LOG(WARNING) << "Syncer exist because context failed";
  }

  context_->jvm_->DetachCurrentThread();
  LOG(INFO) << "Background edit log syncer ends...";
  return true;
}

bool BGEditLogSyncer::TestOnlyIsChannelFull() {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  return channel_.Full();
}

void BGEditLogSyncer::HASwitchFence(int64_t* pending_begin_txid,
                                    int64_t* pending_end_txid) {
  // The `BGEditLogSyncer::operator()` must ensure it does not invoke
  // `HAEditLogContext::LogSyncAllInternal` while maintaining a lock on
  // `consumer_mtx_`. Failure to adhere to this could result in `HASwitchFence`
  // being blocked.
  std::lock_guard<std::mutex> _(consumer_mtx_);

  // [pending_begin_txid, pending_end_txid)
  CHECK(pending_begin_txid);
  *pending_begin_txid = already_finished_task_txid_ + 1;
  CHECK(pending_end_txid);
  *pending_end_txid = *pending_begin_txid;

  while (!producer_lock_.try_lock()) {
    auto length = channel_.NewPopWindow();
    CHECK_GE(length, 0);
    if (LIKELY(length != 0)) {
      auto txid = kInvalidTxId;
      CHECK(channel_.Peek(&txid));
      CHECK_EQ(txid, *pending_end_txid);
      *pending_end_txid += length;
      CHECK_GE(*pending_end_txid, *pending_begin_txid);
    }
    channel_.WindowSkip();
    sched_yield();
  }
  // Once we have acquired `producer_lock_`, no further calls to
  // `BGEditLogSyncer::Sync` can be made.
  DEFER([&]() { producer_lock_.unlock(); });
  auto length = channel_.NewPopWindow();
  CHECK_GE(length, 0);
  if (length != 0) {
    auto txid = kInvalidTxId;
    CHECK(channel_.Peek(&txid));
    CHECK_EQ(txid, *pending_end_txid);
    *pending_end_txid += length;
    CHECK_GE(*pending_end_txid, *pending_begin_txid);
  }
  channel_.WindowSkip();
  CHECK(!is_fenced_);
  is_fenced_ = true;
}

// [begin_txid, end_txid)
Status BGEditLogSyncer::TxStart(int64_t* begin_txid, int64_t* end_txid) {
  CHECK(begin_txid);
  CHECK(end_txid);

  std::lock_guard<std::mutex> _(consumer_mtx_);
  if (UNLIKELY(is_fenced_)) {
    std::string msg = absl::StrFormat(
        "Interrupted by ha switcher, already_finished_task_txid_: %d",
        already_finished_task_txid_);
    LOG_WITH_LEVEL(WARNING) << msg;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return Status(Code::kError, msg);
  }
  auto length = channel_.NewPopWindow();
  if (length == 0) {
    return Status(Code::kFalse);
  }
  CHECK(channel_.Peek(begin_txid));
  CHECK_EQ(*begin_txid, already_finished_task_txid_ + 1);
  *end_txid = *begin_txid + length;
  return Status();
}

// [begin_txid, end_txid)
Status BGEditLogSyncer::TxFinish(int64_t begin_txid, int64_t end_txid) {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  if (UNLIKELY(is_fenced_)) {
    std::string msg = absl::StrFormat(
        "Interrupted by ha switcher, already_finished_task_txid_: %d",
        already_finished_task_txid_);
    LOG_WITH_LEVEL(WARNING) << msg;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return Status(Code::kError, msg);
  }
  CHECK_EQ(begin_txid, already_finished_task_txid_ + 1);
  auto length = end_txid - begin_txid;
  CHECK_EQ(channel_.WindowLength(), length);
  channel_.WindowSkip();
  already_finished_task_txid_ = end_txid - 1;
  listener_->TxFinish(begin_txid, length);
  return Status();
}

}  // namespace dancenn
