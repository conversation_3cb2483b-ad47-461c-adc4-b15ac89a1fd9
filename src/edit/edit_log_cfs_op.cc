// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/27
// Description

#include "edit/edit_log_cfs_op.h"

namespace dancenn {

#define DEF_CFS_OP_CTOR(OP_NAME)                            \
  {                                                         \
    CfsOpCode::k##OP_NAME, []() { return new Op##OP_NAME; } \
  }

std::map<CfsOpCode, std::function<AbstractEditLogCfsOp*()>> kCfsOpCodeToCtor = {
    // File related.
    DEF_CFS_OP_CTOR(OpenFile),
    DEF_CFS_OP_CTOR(AddBlockV2),
    DEF_CFS_OP_CTOR(AbandonBlock),
    DEF_CFS_OP_CTOR(UpdatePipeline),
    DEF_CFS_OP_CTOR(Fsync),
    DEF_CFS_OP_CTOR(CommitBlockSynchronization),
    DEF_CFS_OP_CTOR(UpdateBlocksV2),
    DEF_CFS_OP_CTOR(CloseFile),
    DEF_CFS_OP_CTOR(ReassignLeaseV2),
    DEF_CFS_OP_CTOR(AllocateBlockIdV2),
    DEF_CFS_OP_CTOR(SetGenStampV1),
    DEF_CFS_OP_CTOR(SetGenStampV2),
    DEF_CFS_OP_CTOR(MergeBlock),
    DEF_CFS_OP_CTOR(ConcatV2),
    DEF_CFS_OP_CTOR(Append),

    // Block related.
    DEF_CFS_OP_CTOR(ApproveUploadBlk),
    DEF_CFS_OP_CTOR(PersistBlk),
    DEF_CFS_OP_CTOR(DelDepringBlks),
    DEF_CFS_OP_CTOR(DelDepredBlks),
    DEF_CFS_OP_CTOR(FlushBlockInfoProtos),

    // Dir tree related.
    DEF_CFS_OP_CTOR(MkdirV2),
    DEF_CFS_OP_CTOR(DeleteV2),
    DEF_CFS_OP_CTOR(RenameOldV2),
    DEF_CFS_OP_CTOR(RenameV2),

    // Set* related.
    DEF_CFS_OP_CTOR(SetReplicationV2),
    DEF_CFS_OP_CTOR(SetStoragePolicyV2),
    DEF_CFS_OP_CTOR(SetReplicaPolicyV2),
    DEF_CFS_OP_CTOR(SetDirReplicaPolicyV2),
    DEF_CFS_OP_CTOR(SetQuotaV2),
    DEF_CFS_OP_CTOR(SetPermissionsV2),
    DEF_CFS_OP_CTOR(SetOwnerV2),
    DEF_CFS_OP_CTOR(SetTimesV2),
    DEF_CFS_OP_CTOR(SymlinkV2),
    DEF_CFS_OP_CTOR(SetAclV2),
    DEF_CFS_OP_CTOR(SetXAttrsV2),
    DEF_CFS_OP_CTOR(RemoveXAttrsV2),

    // Snapshot related.
    DEF_CFS_OP_CTOR(AllowSnapshotV2),
    DEF_CFS_OP_CTOR(DisallowSnapshotV2),
    DEF_CFS_OP_CTOR(CreateSnapshotV2),
    DEF_CFS_OP_CTOR(DeleteSnapshotV2),
    DEF_CFS_OP_CTOR(RenameSnapshotV2),

    DEF_CFS_OP_CTOR(SetLifecyclePolicy),
    DEF_CFS_OP_CTOR(UnsetLifecyclePolicy),
    DEF_CFS_OP_CTOR(SetAZBlacklist),

    // Acc Related
    DEF_CFS_OP_CTOR(AccSyncDummy),
    DEF_CFS_OP_CTOR(AccSyncListingBatchAdd),
    DEF_CFS_OP_CTOR(AccSyncListingBatchUpdate),
    DEF_CFS_OP_CTOR(AccSyncUpdateINode),
    DEF_CFS_OP_CTOR(AccSyncAddFile),
    DEF_CFS_OP_CTOR(AccPersistFile),
    DEF_CFS_OP_CTOR(AccUpdateBlockInfo),

    DEF_CFS_OP_CTOR(Pin),
    DEF_CFS_OP_CTOR(ReconcileINodeAttrs),
    DEF_CFS_OP_CTOR(PersistJobInfo),

    // Batch API
    DEF_CFS_OP_CTOR(BatchCreateFile),
    DEF_CFS_OP_CTOR(BatchCompleteFile),
    DEF_CFS_OP_CTOR(BatchDeleteFile),
    DEF_CFS_OP_CTOR(UpdateATimeProtos),
};

#undef DEF_CFS_OP_CTOR

std::shared_ptr<EditLogOp> DeserializeCfsOp(std::stringstream* ss) {
  CHECK_NOTNULL(ss);
  CfsOpCode cfs_op_code;
  ReadField(reinterpret_cast<uint32_t*>(&cfs_op_code), ss);
  auto it = kCfsOpCodeToCtor.find(cfs_op_code);
  CHECK(it != kCfsOpCodeToCtor.end()) << static_cast<uint32_t>(cfs_op_code);
  std::shared_ptr<AbstractEditLogCfsOp> op((it->second)());
  CHECK_EQ(static_cast<uint32_t>(op->GetCfsOpCode()),
           static_cast<uint32_t>(cfs_op_code));
  return op;
}

}  // namespace dancenn
