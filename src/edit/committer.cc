#include "committer.h"

#include <cnetpp/concurrency/thread.h>  // For Thread.
#include <gflags/gflags.h>              // For DECLARE_int32, etc.
#include <glog/logging.h>               // For LOG, etc.
#include <sched.h>                      // For sched_yield.
#include <unistd.h>                     // For usleep.

#include <shared_mutex>  // For shared_lock.
#include <stdexcept>     // For runtime_error.
#include <string>        // For string.

#include "base/committer_channel_context.h"  // For CommitterChannelContext.
#include "base/constants.h"  // For kInvalidTxId, kLastReservedGenerationStamp.
#include "base/defer.h"      // For DEFER.
#include "base/logger_metrics.h"       // For LoggerMetrics.
#include "base/metrics.h"              // For MetricsCenter, etc.
#include "block_manager/block.h"       // For kInvalidBlockID.
#include "edit/edit_log_task.h"        // For EditLogTask.
#include "edit/ha_edit_log_context.h"  // For HAEditLogContext.
#include "edit/syncer.h"               // For BGEditLogSyncer.

DECLARE_int32(edit_log_commit_max_wait_time_us);
DECLARE_int32(client_slow_rpc_handler_count);

namespace dancenn {

BGEditLogCommitter::BGEditLogCommitter(int max_producers,
                                       HAEditLogContext* context,
                                       std::shared_ptr<BGEditLogSyncer> syncer)
    : is_fenced_(false),
      already_ok_task_txid_(kInvalidTxId),
      last_allocated_block_id_(kInvalidBlockID),
      last_generation_stamp_v2_(kLastReservedGenerationStamp),
      processing_task_(nullptr),
      context_(context),
      syncer_(syncer) {
  // channels_[max_producers] use for producer not in cli-workers
  // thread pool, it will hold lock, its performance is worse than
  // other channels, just in test will use it.
  LOG(INFO) << "Init channels count: " << max_producers;
  for (int i = 0; i < max_producers + 1; i++) {
    channels_.emplace_back(std::make_unique<RingBuffer<EditLogTask*>>(128));
  }
}

void BGEditLogCommitter::SetNextTxId(int64_t txid) {
  LOG(INFO) << "SetNextTxId: " << txid;
  std::lock_guard<std::mutex> _(consumer_mtx_);
  // Refer to `FSEditLog::setNextTxId`.
  if (already_ok_task_txid_ != kInvalidTxId) {
    CHECK_GE(txid, already_ok_task_txid_ + 1);
  }
  already_ok_task_txid_ = txid - 1;
}

void BGEditLogCommitter::SetLastAllocatedBlockId(uint64_t block_id) {
  last_allocated_block_id_ = block_id;
}

void BGEditLogCommitter::SetLastGenerationStampV2(uint64_t gsv2) {
  last_generation_stamp_v2_ = gsv2;
}

void BGEditLogCommitter::Commit(EditLogTask* task, bool worse_channel) {
  std::shared_lock<ReadWriteLock> _(producer_lock_);
  // We rely on the lock acquisition order property of `ReadWriteLock` in this
  // context. Specifically, when thread-A attempts to acquire the read lock
  // prior to thread-B's attempt to acquire the write lock, the `ReadWriteLock`
  // mechanism ensures thread-A is granted the read lock before thread-B
  // receives the write lock.
  // TODO(ruanjunbin): `is_fenced_` may be set to true in rare cases.
  CHECK(!is_fenced_);
  if (context_->IsFailed()) {
    task->Txid(kInvalidTxId);
    task->OK();
    return;
  }

  auto this_thread = cnetpp::concurrency::Thread::ThisThread();
  const std::string* thread_name = nullptr;
  int channel_index = -1;

  if (this_thread == nullptr) {
    worse_channel = true;
    LOG(WARNING) << "CallJavaOpMethod from native-thread, WORSE performance";
  } else {
    auto ctx =
        reinterpret_cast<CommitterChannelContext*>(this_thread->UserContext());
    thread_name = &(this_thread->name());
    if (ctx == nullptr) {
      worse_channel = true;
      LOG(INFO) << context_->OpMethodName(task->Method())
                << " CallJavaOpMethod from " << this_thread->name()
                << ", WORSE performance";
    } else {
      bool ok = ctx->GetChannelIndex(&channel_index);

      if (!ok) {
        worse_channel = true;
        LOG(INFO) << context_->OpMethodName(task->Method())
                  << " CallJavaOpMethod from " << this_thread->name()
                  << ", cannot get correct channel index"
                  << ", WORSE performance";
      }
    }
  }
  CHECK(channel_index == -1 || channel_index < channels_.size())
      << "channel overflow, see max_channels_num in dancenn.cc."
      << " channel_index=" << channel_index
      << " channels_.size()=" << channels_.size();

  if (LIKELY(!worse_channel)) {
    DLOG(INFO) << "Commit task. channel_index: " << channel_index
               << ", name: " << *thread_name;
    while (!channels_[channel_index]->TryPush(task)) {
      // Now never reach here, TryPush always return true
      // at future dancenn implement better pipeline may
      // reach here.
      sched_yield();
    }
  } else {
    while (!channels_.back()->LockedTryPush(task)) {
      // Now never reach here, TryPush always return true
      // at future dancenn implement better pipeline may
      // reach here.
      sched_yield();
    }
  }
}

bool BGEditLogCommitter::operator()(void* arg) {
  (void)arg;
  LOG(INFO) << "Background edit log committer starts...";

  Histogram* each_round_process_tasks_ = nullptr;
  // Histogram* each_java_log_call_ = nullptr;
  {
    auto metrics =
        MetricsCenter::Instance()->RegisterMetrics("BGEditLogCommitter");
    each_round_process_tasks_ =
        MFH(metrics->RegisterHistogram("EachRoundProcessTask"));
    // each_java_log_call_ = MFH(metrics->RegisterHistogram("EachJavaLogCall"));
  }

  std::vector<EditLogTask*> sync_tasks;
  sync_tasks.reserve(channels_.size());

  while (!IsStopped()) {
    bool async_sync = true;
    int num_task = 0;
    for (int i = 0; i < channels_.size(); i++) {
      std::unique_ptr<EditLogTask> task = TxStart(i);
      if (!task) {
        continue;
      }

      try {
        if (task->Method() == context_->method_log_allocate_block_id_) {
          {
            uint64_t value = 0;
            auto txid = context_->CallJavaOpMethodLL(
                task->Method(), task->Msg(), &value);
            DLOG(INFO) << "log_allocate_block_id txid = " << txid;
            CHECK(txid > 0) << "CallJavaOpMethodLL txid = " << txid;
            task->Txid(txid);
            task->Value(value);
          }
          if (task->HasExtra()) {
            uint64_t value = 0;
            auto txid = context_->CallJavaOpMethodLL(
                context_->method_log_generation_stamp_v2_,
                task->ExtraMsg(),
                &value);
            DLOG(INFO) << "log_generation_stamp_v2 txid = " << txid;
            CHECK(txid > 0 && txid == task->Txid() + 1)
                << "CallJavaOpMethodLLLL last_txid = " << task->Txid()
                << ", txid = " << txid;
            task->Txid(txid);
            task->ExtraValue(value);
          }
        } else if (task->Method() ==
                   context_->method_log_generation_stamp_v2_) {
          uint64_t value = 0;
          auto txid =
              context_->CallJavaOpMethodLL(task->Method(), task->Msg(), &value);
          DLOG(INFO) << "log_generation_stamp_v2 txid = " << txid;
          CHECK(txid > 0) << "CallJavaOpMethodLL txid = " << txid;
          task->Txid(txid);
          task->Value(value);
        } else if (task->Method() == context_->method_open_for_write_ ||
                   task->Method() == context_->method_roll_edit_log_) {
          auto txid = context_->CallJavaOpVoidMethodL(task->Method());
          DLOG(INFO) << "open_for_write/roll_edit_log txid = " << txid;
          CHECK(txid > 0) << "CallJavaOpVoidMethodL txid = " << txid;
          task->Txid(txid);
        } else if (task->Method() == context_->method_close_) {
          auto txid = context_->CloseInternal();
          DLOG(INFO) << "close txid = " << txid;
          // This is the situation:
          // The `HAEditLogContext::InitJournalsForWrite` is invoked followed
          // by `Close`, without calling `OpenForWrite` in between. See
          // EditLogRecoveryTest.SyncAfterClose for more infos.
          // CHECK(txid > 0) << "CloseInternal txid = " << txid;
          CHECK_GE(txid, 0);
          task->Txid(txid);
          async_sync = false;
        } else if (task->Method() != context_->method_log_sync_all_ &&
                   task->Method() != context_->method_log_sync_) {
          auto txid = context_->CallJavaOpMethod(task->Method(),
                                                 task->Msg(),
                                                 task->WithRpcID(),
                                                 task->ToLogRpcIds());
          DLOG(INFO) << "others txid = " << txid;
          CHECK(txid > 0) << "CallJavaOpMethod txid = " << txid;
          task->Txid(txid);
        } else if (task->Method() == context_->method_log_sync_) {
          // TODO (livexmm) at future, we will remove this
          context_->LogSyncInternal();
          async_sync = false;
        } else {
          // TODO (livexmm) at future, we will remove this
          context_->LogSyncAllInternal();
          async_sync = false;
        }
      } catch (const std::runtime_error& e) {
        LOG_WITH_LEVEL(ERROR) << "Call context methods failed, " << e.what();
        MFC(LoggerMetrics::Instance().error_)->Inc();
        std::lock_guard<std::mutex> _(consumer_mtx_);
        CHECK(is_fenced_);
      }

      num_task = num_task + 1;

      if (task->Txid() == kInvalidTxId) {
        context_->SetFailed();
      }
      if (context_->IsFailed()) {
        task->Txid(kInvalidTxId);
        task->OK();
        continue;
      }

      int remaining_txid_to_be_synced = 0;
      do {
        if (LIKELY(async_sync)) {
          // TODO (livexmm) opt maybe we don't need every txid call TrySync
          if (task->HasExtra() ||
              task->Method() == context_->method_roll_edit_log_) {
            if (!syncer_->Sync(task->Txid() - 1)) {
              remaining_txid_to_be_synced = 2;
              break;
            }
          }
          if (!syncer_->Sync(task->Txid())) {
            remaining_txid_to_be_synced = 1;
            break;
          }
        } else if (task->Method() == context_->method_close_) {
          if (!syncer_->Close(task->Txid())) {
            remaining_txid_to_be_synced = 1;
            break;
          }
        }
      } while (false);

      if (remaining_txid_to_be_synced > 0) {
        std::lock_guard<std::mutex> _(consumer_mtx_);
        // The function `BGEditLogSyncer::HASwitchFence` is invoked prior to
        // calling `BGEditLogCommitter::HASwitchFence`.
        CHECK(is_fenced_);
      } else {
        TxFinish(task.get());
      }
    }

    each_round_process_tasks_->Update(num_task);
    if (num_task == 0) {
      usleep(FLAGS_edit_log_commit_max_wait_time_us);
    }
  }

  context_->jvm_->DetachCurrentThread();
  LOG(INFO) << "Background edit log committer ends...";
  return true;
}

bool BGEditLogCommitter::TestOnlyIsChannelsFull() {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  for (auto& channel : channels_) {
    if (!channel->Full()) {
      return false;
    }
  }
  return true;
}

// While implementing this function, bear in mind that calls to
// `HAEditLogContext::CallJavaXXX` or `BGEditLogSyncer::Sync` could either
// return or block execution. It's crucial to carefully handle both outcomes.
void BGEditLogCommitter::HASwitchFence(int64_t* pending_begin_txid,
                                       int64_t* pending_end_txid,
                                       uint64_t* last_allocated_block_id,
                                       uint64_t* last_generation_stamp_v2) {
  // The `BGEditLogCommitter::operator()` must ensure it does not invoke
  // `HAEditLogContext::CallJavaXXX` or `BGEditLogSyncer::Sync` while
  // maintaining a lock on `consumer_mtx_`. Failure to adhere to this could
  // result in `HASwitchFence` being blocked.
  std::lock_guard<std::mutex> _(consumer_mtx_);

  std::vector<EditLogTask*> pending_tasks;
  if (processing_task_ != nullptr) {
    pending_tasks.emplace_back(processing_task_);
    processing_task_ = nullptr;
  };
  while (!producer_lock_.try_lock()) {
    for (auto& channel : channels_) {
      EditLogTask* pending_task = nullptr;
      while (channel->TryPop(&pending_task)) {
        pending_tasks.emplace_back(pending_task);
      }
    }
    sched_yield();
  }
  // Once we have acquired `producer_lock_`, no further calls to
  // `BGEditLogCommitter::Commit` can be made.
  DEFER([&]() { producer_lock_.unlock(); });
  for (auto& channel : channels_) {
    EditLogTask* pending_task = nullptr;
    while (channel->TryPop(&pending_task)) {
      pending_tasks.emplace_back(pending_task);
    }
  }
  CHECK(!is_fenced_);
  is_fenced_ = true;

  // [pending_begin_txid, pending_end_txid)
  *pending_begin_txid = already_ok_task_txid_ + 1;

  // Introduce a gap between block ids to prevent potential bugs that could lead
  // to overlapping block ids.
  const uint64_t reserved_block_id_gap = 65536;
  const uint64_t reserved_gs_gap = reserved_block_id_gap;
  last_allocated_block_id_ += reserved_block_id_gap;
  last_generation_stamp_v2_ += reserved_gs_gap;
  bool edit_log_ctx_closed = false;
  for (EditLogTask* task : pending_tasks) {
    // Refer to `BGEditLogCommitter::operator()`.
    if (task->Method() == context_->method_close_) {
      if (!edit_log_ctx_closed) {
        edit_log_ctx_closed = true;
      }
    } else {
      CHECK(!edit_log_ctx_closed);
      if (task->Method() == context_->method_log_sync_all_ ||
          task->Method() == context_->method_log_sync_) {
      } else if (task->Method() == context_->method_log_allocate_block_id_) {
        {
          already_ok_task_txid_++;
          task->Txid(already_ok_task_txid_);
          last_allocated_block_id_++;
          task->Value(last_allocated_block_id_);
        }
        if (task->HasExtra()) {
          already_ok_task_txid_++;
          task->Txid(already_ok_task_txid_);
          last_generation_stamp_v2_++;
          task->ExtraValue(last_generation_stamp_v2_);
        }
      } else if (task->Method() == context_->method_log_generation_stamp_v2_) {
        already_ok_task_txid_++;
        task->Txid(already_ok_task_txid_);
        last_generation_stamp_v2_++;
        task->Value(last_generation_stamp_v2_);
      } else if (task->Method() == context_->method_roll_edit_log_) {
        already_ok_task_txid_++;
        already_ok_task_txid_++;
        task->Txid(already_ok_task_txid_);
      } else {
        already_ok_task_txid_++;
        task->Txid(already_ok_task_txid_);
      }
    }
    task->OK();
  }
  last_allocated_block_id_ += reserved_block_id_gap;
  last_generation_stamp_v2_ += reserved_gs_gap;

  if (edit_log_ctx_closed) {
    *pending_begin_txid -= 1;
  }
  *pending_end_txid = already_ok_task_txid_ + 1;
  CHECK_GE(*pending_end_txid, *pending_begin_txid);
  *last_allocated_block_id = last_allocated_block_id_;
  *last_generation_stamp_v2 = last_generation_stamp_v2_;
}

std::unique_ptr<EditLogTask> BGEditLogCommitter::TxStart(int channel_id) {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  if (is_fenced_) {
    return false;
  }
  EditLogTask* t = nullptr;
  if (!channels_[channel_id]->TryPop(&t)) {
    return false;
  }
  CHECK(!processing_task_);
  processing_task_ = t;
  auto task =
      std::make_unique<EditLogTask>(t->Method(),
                                    t->Msg(),
                                    /*with_rpc_id=*/t->WithRpcID(),
                                    /*to_log_rpc_ids=*/t->ToLogRpcIds());
  if (t->HasExtra()) {
    task->ExtraMsg(t->ExtraMsg());
  }
  CHECK_EQ(task->Method(), t->Method());
  CHECK_EQ(task->Msg(), t->Msg());
  CHECK_EQ(task->ExtraMsg(), t->ExtraMsg());
  CHECK_EQ(task->WithRpcID(), t->WithRpcID());
  CHECK_EQ(task->ToLogRpcIds(), t->ToLogRpcIds());
  CHECK_EQ(task->HasExtra(), t->HasExtra());
  return task;
}

void BGEditLogCommitter::TxFinish(EditLogTask* task) {
  std::lock_guard<std::mutex> _(consumer_mtx_);
  if (is_fenced_) {
    return;
  }

  // Refer to `BGEditLogCommitter::operator()`.
  if (task->Method() == context_->method_close_) {
    // In the first scenario, `HAEditLogContext::InitJournalsForWrite` is
    // called, then immediately followed by `Close`, without an intermediate
    // call to `OpenForWrite`. For more information, refer to
    // `EditLogRecoveryTest.InitJournalsForWriteThenRead`.
    //
    // In the second scenario, the `HAEditLogContext::Close` method is invoked
    // multiple times.
    if (task->Txid() == already_ok_task_txid_) {
    } else if (task->Txid() == already_ok_task_txid_ + 1) {
      already_ok_task_txid_ = task->Txid();
    } else {
      LOG(FATAL) << "task->Txid()=" << task->Txid()
                 << ", already_ok_task_txid_=" << already_ok_task_txid_;
    }
  } else if (task->Method() == context_->method_log_sync_all_ ||
             task->Method() == context_->method_log_sync_) {
    // CHECK_EQ(task->Txid(), already_ok_task_txid_);
  } else if ((task->Method() == context_->method_log_allocate_block_id_ &&
              task->HasExtra()) ||
             task->Method() == context_->method_roll_edit_log_) {
    CHECK_EQ(task->Txid(), already_ok_task_txid_ + 2);
    already_ok_task_txid_ = task->Txid();
  } else {
    CHECK_EQ(task->Txid(), already_ok_task_txid_ + 1);
    already_ok_task_txid_ = task->Txid();
  }

  if (task->Method() == context_->method_log_allocate_block_id_) {
    // Refer to `FSEditLog::logEditInternal`:
    // value = ++lastAllocatedBlockID;
    // return new long[]{retTxId, value};
    //
    // https://stackoverflow.com/questions/6373976/precedence-of-and-operators-in-java
    // `++x` changes the value of `x`, and returns the new value.
    //
    // Therefore, we can derive the following equation:
    //    `BGEditLogCommitter::last_allocated_block_id_`
    // == `FSEditLog::lastAllocatedBlockID`.
    CHECK_EQ(task->Value(), last_allocated_block_id_ + 1);
    last_allocated_block_id_ = task->Value();
    if (task->HasExtra()) {
      CHECK_EQ(task->ExtraValue(), last_generation_stamp_v2_ + 1);
      last_generation_stamp_v2_ = task->ExtraValue();
    }
  } else if (task->Method() == context_->method_log_generation_stamp_v2_) {
    // Refer to `FSEditLog::logEditInternal`:
    // value = ++lastGenerationStampV2;
    //
    // https://stackoverflow.com/questions/6373976/precedence-of-and-operators-in-java
    // `++x` changes the value of `x`, and returns the new value.
    //
    // Therefore, we can derive the following equation:
    //    `BGEditLogCommitter::last_generation_stamp_v2_`
    // == `FSEditLog::lastGenerationStampV2`.
    CHECK_EQ(task->Value(), last_generation_stamp_v2_ + 1);
    last_generation_stamp_v2_ = task->Value();
  } else if (task->Method() == context_->method_log_generation_stamp_v1_) {
    LOG_WITH_LEVEL(ERROR) << "Should not reach here due to "
                             "kGrandfatherGenerationStamp is 0";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  CHECK(processing_task_);
  processing_task_->Txid(task->Txid());
  processing_task_->Value(task->Value());
  processing_task_->ExtraValue(task->ExtraValue());
  processing_task_->OK();
  processing_task_ = nullptr;
}

}  // namespace dancenn
