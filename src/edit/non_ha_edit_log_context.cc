// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/10/30
// Description

#include "edit/non_ha_edit_log_context.h"

#include <glog/logging.h>  // For LOG, etc.

#include "base/constants.h"       // For kInvalidTxId.
#include "base/logger_metrics.h"  // For LoggerMetrics.
#include "base/metrics.h"         // For MFC.
#include "block_manager/block.h"  // For kInvalidBlockID.

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

NonHAEditLogContext::NonHAEditLogContext()
    : is_open_for_write_(false),
      is_fenced_(false),
      txid_(kInvalidTxId),
      cur_segment_txid_(kInvalidTxId),
      last_allocated_block_id_(kInvalidBlockID),
      last_generation_stamp_v2_(kLastReservedGenerationStamp) {
}

EditLogConf::HAMode NonHAEditLogContext::GetHAMode() {
  return EditLogConf::NonHA;
}

bool NonHAEditLogContext::UpdateConfProperty(const std::string& name,
                                             const std::string& value) {
  LOG(ERROR) << "NonHAEditLogContext::UpdateConfProperty is called"
             << ", name: " << name << ", value: " << value;
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return true;
}

void NonHAEditLogContext::SetupSyncListener(
    std::shared_ptr<IEditLogSyncListener> listener) {
  listener_ = listener;
}

std::shared_ptr<IEditLogSyncListener> NonHAEditLogContext::
    TestOnlyGetSyncListener() {
  LOG(ERROR) << "NonHAEditLogContext::TestOnlyGetSyncListener is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return listener_;
}

bool NonHAEditLogContext::GetPeerNNAddr(std::string* addr) {
  LOG(ERROR) << "NonHAEditLogContext::GetPeerNNAddr is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return false;
}

bool NonHAEditLogContext::GetAllStackTraces(std::string* stack_info) {
  LOG(ERROR) << "NonHAEditLogContext::GetAllStackTraces is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return false;
}

bool NonHAEditLogContext::IsOpenForRead() {
  return false;
}

void NonHAEditLogContext::OpenForRead() {
  LOG(ERROR) << "NonHAEditLogContext::OpenForRead is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
}

std::unique_ptr<EditLogInputContextBase> NonHAEditLogContext::
    CreateInputContext(int64_t from_txid,
                       int64_t to_at_least_txid,
                       bool is_progress_ok) {
  LOG(ERROR) << "NonHAEditLogContext::CreateInputContext is called"
             << ", from_txid: " << from_txid
             << ", to_at_least_txid: " << to_at_least_txid
             << ", is_progress_ok: " << is_progress_ok;
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return nullptr;
}

bool NonHAEditLogContext::IsOpenForWrite() {
  std::lock_guard<std::mutex> _(mtx_);
  return is_open_for_write_;
}

void NonHAEditLogContext::InitJournalsForWrite() {
  LOG(INFO) << "NonHAEditLogContext::InitJournalsForWrite is called.";
}

int64_t NonHAEditLogContext::OpenForWrite() {
  std::lock_guard<std::mutex> _(mtx_);
  is_open_for_write_ = true;
  LogEditInternal();
  // Users of the EditLogContextBase class should comply with the following
  // procedure: invoke the SetNextTxId method prior to calling the OpenForWrite
  // method. For instance, the HAState::StartActiveServicesInternal function
  // exemplifies this process by sequentially calling
  // EditLogContextBase::InitJournalsForWrite, SetNextTxId, and OpenForWrite.
  // Refer to FSEditLog::openForWriteInternal.
  return GetLastWrittenTxId();
}

void NonHAEditLogContext::Close() {
  std::lock_guard<std::mutex> _(mtx_);
  is_open_for_write_ = false;
  LOG(INFO) << "NonHAEditLogContext::Close is called.";
}

int64_t NonHAEditLogContext::GetLastWrittenTxId() {
  CHECK_NE(txid_, kInvalidTxId);
  return txid_;
}

void NonHAEditLogContext::SetNextTxId(int64_t txid) {
  // Refer to FSEditLog::setNextTxId.
  LOG(INFO) << "SetNextTxId: " << txid;
  txid_ = txid - 1;
}

uint64_t NonHAEditLogContext::GetLastAllocatedBlockId() {
  CHECK_NE(last_allocated_block_id_, kInvalidBlockID);
  return last_allocated_block_id_;
}

void NonHAEditLogContext::SetLastAllocatedBlockId(int64_t id) {
  last_allocated_block_id_ = id;
}

uint64_t NonHAEditLogContext::GetLastGenerationStampV2() {
  CHECK_NE(last_generation_stamp_v2_, kLastReservedGenerationStamp);
  return last_generation_stamp_v2_;
}

void NonHAEditLogContext::SetLastGenerationStampV2(int64_t gsv2) {
  last_generation_stamp_v2_ = gsv2;
}

int64_t NonHAEditLogContext::GetCurSegmentTxId() {
  // At present, the sole invoker of this function is
  // EditLogRoller::RollTask::operator(),
  // which is primarily utilized for debugging purposes.
  // CHECK_NE(cur_segment_txid_, kInvalidTxId);
  return cur_segment_txid_;
}

int64_t NonHAEditLogContext::GetWaitSyncTime() {
  return 0;
}

void NonHAEditLogContext::LogSync(bool force) {
}

void NonHAEditLogContext::LogSyncAll() {
}

int64_t NonHAEditLogContext::RollEditLog() {
  std::lock_guard<std::mutex> _(mtx_);
  // Refer to FSEditLog::rollEditLog.
  LOG(INFO) << "Rolling edit logs";

  // Refer to endCurrentLogSegment.
  LOG(INFO) << "Ending log segment";
  LogEditInternal();

  // Refer to FSEditLog::startLogSegment.
  LOG(INFO) << "Starting log segment";
  auto next_txid = txid_ + 1;
  cur_segment_txid_ = next_txid;
  CHECK_EQ(LogEditInternal(), next_txid);
  return next_txid;
}

void NonHAEditLogContext::PurgeLogsOlderThan(int64_t min_tx_id_to_keep) {
}

int64_t NonHAEditLogContext::LogCfsOp(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogAllocateBlockId(const std::stringstream* ss,
                                                uint64_t* id) {
  std::lock_guard<std::mutex> _(mtx_);
  CHECK_NOTNULL(id);
  // Refer to FSEditLog::logEditInternal.
  // value = ++lastAllocatedBlockID;
  // allocateBlockIdOp.setBlockId(value);
  //
  // https://stackoverflow.com/questions/6373976/precedence-of-and-operators-in-java
  // ++x changes the value of x, and returns the new value.
  //
  // https://en.cppreference.com/w/cpp/atomic/atomic/fetch_add
  // Return value is the value immediately preceding the effects of this
  // function in the modification order of *this.
  last_allocated_block_id_++;
  *id = last_allocated_block_id_;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    return kInvalidTxId;
  }
  return LogEditInternal();
}

int64_t NonHAEditLogContext::LogGenerationStampV1(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogGenerationStampV2(const std::stringstream* ss,
                                                  uint64_t* gsv2) {
  std::lock_guard<std::mutex> _(mtx_);
  CHECK_NOTNULL(gsv2);
  // Refer to FSEditLog::logEditInternal.
  // value = ++lastGenerationStampV2;
  // setGenstampV2Op.setGenerationStamp(value);
  //
  // https://stackoverflow.com/questions/6373976/precedence-of-and-operators-in-java
  // ++x changes the value of x, and returns the new value.
  //
  // https://en.cppreference.com/w/cpp/atomic/atomic/fetch_add
  // Return value is the value immediately preceding the effects of this
  // function in the modification order of *this.
  last_generation_stamp_v2_++;
  *gsv2 = last_generation_stamp_v2_;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    return kInvalidTxId;
  }
  return LogEditInternal();
}

int64_t NonHAEditLogContext::LogAllocateBlockIdAndGSv2(
    const std::stringstream* blkid_ss,
    const std::stringstream* gsv2_ss,
    uint64_t* blkid,
    uint64_t* gsv2) {
  std::lock_guard<std::mutex> _(mtx_);
  CHECK_NOTNULL(blkid);
  CHECK_NOTNULL(gsv2);
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    last_allocated_block_id_++;
    *blkid = last_allocated_block_id_;

    last_generation_stamp_v2_++;
    *gsv2 = last_generation_stamp_v2_;

    return kInvalidTxId;
  }

  last_allocated_block_id_++;
  *blkid = last_allocated_block_id_;
  auto allocate_block_id_txid = LogEditInternal();

  last_generation_stamp_v2_++;
  *gsv2 = last_generation_stamp_v2_;
  auto gen_stamp_txid = LogEditInternal();

  // Refer to BGEditLogCommitter::operator().
  // The txid increment between allocating a block ID and allocating a genstamp
  // is 1. This is ensured by the BGEditLogCommitter::operator() function,
  // which is designed to be executed by only a single thread at any given time.
  CHECK_EQ(allocate_block_id_txid + 1, gen_stamp_txid);
  return gen_stamp_txid;
}

int64_t NonHAEditLogContext::LogTimes(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogOpenFile(const std::stringstream* ss,
                                         bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogAddBlock(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogUpdateBlocks(const std::stringstream* ss,
                                             bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogCloseFile(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogReassignLease(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogConcat(const std::stringstream* ss,
                                       bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetBlockPufsInfo(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogDeleteDeprecatedBlockPufsInfo(
    const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogMkDir(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogDelete(const std::stringstream* ss,
                                       bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogRenameOld(const std::stringstream* ss,
                                          bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogRename(const std::stringstream* ss,
                                       bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSymlink(const std::stringstream* ss,
                                        bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetReplication(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetStoragePolicy(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetReplicaPolicy(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetDirReplicaPolicy(
    const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetQuota(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetPermissions(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetOwner(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetAcl(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogSetXAttrs(const std::stringstream* ss,
                                          bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogRemoveXAttrs(const std::stringstream* ss,
                                             bool to_log_rpc_ids) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogAllowSnapshot(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogDisallowSnapshot(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogCreateSnapshot(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogDeleteSnapshot(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogRenameSnapshot(const std::stringstream* ss) {
  return LogEdit();
}

int64_t NonHAEditLogContext::LogAccessCounterSnapshot(
    const std::stringstream* ss) {
  return LogEdit();
}

EditLogConf::PreviousEditLogConf NonHAEditLogContext::HASwitchFence() {
  std::lock_guard<std::mutex> _(mtx_);
  is_fenced_ = true;

  EditLogConf::PreviousEditLogConf conf;
  conf.set_reserved_begin_txid(txid_ + 1);

  const int64_t reserved_txid_gap =
      100 /*request(q/ms)*/ * 1000 /*(ms/s)*/ * 5 /*(min/s)*/;
  txid_ += reserved_txid_gap;
  const uint64_t reserved_block_id_gap = 65536;
  const uint64_t reserved_gs_gap = reserved_block_id_gap;
  last_allocated_block_id_ += reserved_block_id_gap;
  last_generation_stamp_v2_ += reserved_gs_gap;

  // [reserved_begin_txid, reserved_end_txid)
  conf.set_reserved_end_txid(txid_ + 1);
  conf.set_pending_begin_txid(conf.reserved_begin_txid());
  conf.set_pending_end_txid(conf.reserved_begin_txid());
  conf.set_last_allocated_block_id(last_allocated_block_id_);
  conf.set_last_generation_stamp_v2(last_generation_stamp_v2_);
  conf.set_is_ha_edit_log_conf(false);
  conf.mutable_non_ha_edit_log_conf();
  return conf;
}

Status NonHAEditLogContext::SwitchNonHAActiveToHAActive() {
  LOG(ERROR) << "NonHAEditLogContext::SwitchNonHAActiveToHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

Status NonHAEditLogContext::SwitchHAActiveToNonHAActive() {
  LOG(ERROR) << "NonHAEditLogContext::SwitchHAActiveToNonHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

Status NonHAEditLogContext::SwitchHAStandbyToNonHAActive() {
  LOG(ERROR) << "NonHAEditLogContext::SwitchHAStandbyToNonHAActive is called";
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(Code::kError);
}

int64_t NonHAEditLogContext::LogEdit() {
  std::lock_guard<std::mutex> _(mtx_);
  return LogEditInternal();
}

int64_t NonHAEditLogContext::LogEditInternal() {
  // CHECK(is_open_for_write_);
  CHECK(!is_fenced_);
  // Refer to FSEditLog::logEditInternal.
  // beginTransaction();
  //   txid++;
  // op.setTransactionId(txid);
  // retTxId = txid;
  // return new long[]{retTxId, value};
  //
  // https://en.cppreference.com/w/cpp/atomic/atomic/fetch_add
  // Return value is the value immediately preceding the effects of this
  // function in the modification order of *this.
  txid_++;
  listener_->TxFinish(txid_, 1);
  return txid_;
}

}  // namespace dancenn