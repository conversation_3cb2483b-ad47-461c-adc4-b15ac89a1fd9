// Copyright 2017 He <PERSON> <<EMAIL>>

#ifndef EDIT_TAILER_H_
#define EDIT_TAILER_H_

#include <cstdint>
#include <thread>
#include <memory>
#include <condition_variable>
#include <string>

#include "base/metric.h"
#include "base/metrics.h"
#include "edit/tailer_base.h"
#include "edit/edit_log_context.h"
#include "edit/op/all.h"
#include "namespace/namespace.h"
#include "edit/deserializer.h"

namespace dancenn {

class EditLogTailer : public EditLogTailerBase {
 public:
  EditLogTailer(int64_t last_applied_txid,
                std::shared_ptr<JavaRuntime> jvm,
                std::shared_ptr<EditLogContextBase> context,
                NameSpace* ns);
  ~EditLogTailer();

  EditLogTailer(EditLogTailer const&) = delete;
  EditLogTailer& operator=(EditLogTailer const&) = delete;

  int64_t last_applied_txid() {
    return last_applied_txid_;
  }

  void Start();
  void Stop(bool to_last_txid = false);
  virtual void CatchupDuringFailover();
  void Apply(std::shared_ptr<EditLogOp> op) override;
  // For testing
  void StopApplyAssignerThread();
  void WaitNoPending(bool wait_db);

  size_t GetApplyAssignerThreadPendingCount() const;
  size_t GetApplyAssignerThreadNumRunningTasks() const;

 protected:
  virtual void Run();
  void Apply(const std::string &serialized_op);

  volatile bool should_run_;
  NameSpace* ns_{nullptr};
  std::mutex cv_mutex_;
  std::condition_variable cv_;
  std::atomic<int64_t> last_applied_txid_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> apply_assigner_thread_;

 private:
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::shared_ptr<JavaRuntime> java_runtime_;
  std::shared_ptr<EditLogContextBase> context_;
  OpDeSerializer op_deserializer;
  void CreateEditLogContext();

  size_t DoTail(bool is_inprogress_ok, bool retryable);
};

}  // namespace dancenn

#endif  // EDIT_TAILER_H_

