//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef EDIT_ROLLER_H_
#define EDIT_ROLLER_H_

#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>

#include <memory>
#include <condition_variable>

#include "edit/edit_log_context.h"
#include "namespace/namespace.h"

namespace dancenn {

class HAStateBase;
class SafeModeBase;
class NameSpace;

class EditLogRoller {
 public:
  EditLogRoller(std::shared_ptr<EditLogContextBase> context,
                NameSpace* ns);
  EditLogRoller(const EditLogRoller &other) = delete;
  ~EditLogRoller();

  void set_ha_state(HAStateBase* ha_state) {
    ha_state_ = ha_state;
  }

  void set_safemode(SafeModeBase* safemode) {
    safemode_ = safemode;
  }

  void Stop();
  void Start();

 private:
  class RollTask : public cnetpp::concurrency::Task {
   public:
    explicit RollTask(EditLogRoller* roller);
    bool operator()(void *arg) override;
    void Stop() override;

   private:
    const EditLogRoller* roller_{nullptr};

    std::chrono::steady_clock::time_point last_roll_time_;

    std::mutex cv_mutex_;
    std::condition_variable cv_;

    void RollEditLog();
  };  // RollTask

  friend class RollTask;
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::shared_ptr<EditLogContextBase> edit_log_context_;
  NameSpace* ns_{nullptr};
  HAStateBase*   ha_state_;
  SafeModeBase*  safemode_;
};

}  // namespace dancenn

#endif  // EDIT_ROLLER_H_

