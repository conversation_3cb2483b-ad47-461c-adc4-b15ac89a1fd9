OP_ADD
    uint64_t inodeId;
    const std::string& path;
    uint16_t replication;
    uint64_t mtime;
    uint64_t atime;
    uint64_t blockSize;
    const std::vector<cloudfs::BlockProto>& blocks;
    const PermissionStatus& permissions;
    const std::vector<cloudfs::AclEntryProto>& aclEntries;
    const cloudfs::XAttrEditLogProto& xAttrs;
    const std::string& clientName;
    const std::string& clientMachine;
    bool overwrite;
    uint8_t storagePolicyId;
    const std::string& clientId;
    uint32_t callId;
OP_RENAME_OLD
    const std::string& src;
    const std::string& dst;
    uint64_t timestamp;
    const std::string& clientId;
    uint32_t callId;
OP_DELETE
    const std::string& path;
    uint64_t timestamp;
    const std::string& clientId;
    uint32_t callId;
OP_MKDIR
    uint64_t inodeId;
    const std::string& path;
    uint64_t mtime;
    uint64_t atime;
    const PermissionStatus& permissions;
    const std::vector<cloudfs::AclEntryProto>& aclEntries;
    const cloudfs::XAttrEditLogProto& xAttrs;
OP_SET_REPLICATION
    const std::string& path;
    uint16_t replication;
OP_SET_PERMISSIONS
    const std::string& src;
    uint16_t permissions;
OP_SET_OWNER
    const std::string& src;
    const std::string& username;
    const std::string& groupname;
OP_CLOSE
    uint64_t inodeId;
    const std::string& path;
    uint16_t replication;
    uint64_t mtime;
    uint64_t atime;
    uint64_t blockSize;
    const std::vector<cloudfs::BlockProto>& blocks;
    const PermissionStatus& permissions;
OP_SET_GENSTAMP_V1
    uint64_t genStampV1;
OP_SET_NS_QUOTA
    const std::string& src;
    uint64_t nsQuota;
OP_CLEAR_NS_QUOTA
    const std::string& src;
OP_TIMES
    const std::string& path;
    uint64_t mtime;
    uint64_t atime;
OP_SET_QUOTA
    const std::string& src;
    uint64_t nsQuota;
    uint64_t dsQuota;
OP_RENAME
    const std::string& src;
    const std::string& dst;
    uint64_t timestamp;
    const RenameOptions& options;
    const std::string& clientId;
    uint32_t callId;
OP_CONCAT_DELETE
    const std::string& trg;
    const std::vector<std::string>& srcs;
    uint64_t timestamp;
    const std::string& clientId;
    uint32_t callId;
OP_SYMLINK
    uint64_t inodeId;
    const std::string& path;
    const std::string& value;
    uint64_t mtime;
    uint64_t atime;
    const PermissionStatus& permissions;
    const std::string& clientId;
    uint32_t callId;
OP_REASSIGN_LEASE
    const std::string& leaseHolder;
    const std::string& path;
    const std::string& newHolder;
OP_UPDATE_BLOCKS
    const std::string& path;
    const CompactBlockArray& blocks;
    const std::string& clientId;
    uint32_t callId;
OP_CREATE_SNAPSHOT
    const std::string& snapshotRoot;
    const std::string& snapshotName;
OP_DELETE_SNAPSHOT
    const std::string& snapshotRoot;
    const std::string& snapshotName;
OP_RENAME_SNAPSHOT
    const std::string& snapshotRoot;
    const std::string& snapshotOldName;
    const std::string& snapshotNewName;
OP_ALLOW_SNAPSHOT
    const std::string& snapshotRoot;
OP_DISALLOW_SNAPSHOT
    const std::string& snapshotRoot;
OP_SET_GENSTAMP_V2
    uint64_t genStampV2;
OP_ALLOCATE_BLOCK_ID
    uint64_t blockId;
OP_ADD_BLOCK
    const std::string& path;
    const CompactBlockArray& blocks;
    const std::string& clientId;
    uint32_t callId;
OP_SET_ACL
    const cloudfs::AclEditLogProto& aclEditLog;
OP_ROLLING_UPGRADE_START
    const std::string& name;
    uint64_t time;
OP_ROLLING_UPGRADE_FINALIZE
    const std::string& name;
    uint64_t time;
OP_SET_XATTR
    const cloudfs::XAttrEditLogProto& xAttrs;
    const std::string& clientId;
    uint32_t callId;
OP_REMOVE_XATTR
    const cloudfs::XAttrEditLogProto& xAttrs;
    std::string clientId;
    uint32_t callId;
OP_SET_STORAGE_POLICY
    const std::string& path;
    uint8_t policyId;
OP_SET_REPLICA_POLICY
    const std::string& path;
    int32_t id;
OP_SET_DIR_REPLICA_POLICY
    const std::string& path;
    int8_t id;
    const std::string& dc;
OP_ACCESS_COUNTER_SNAPSHOT
    const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot;
OP_SET_CFS_UNIVERSAL_INFO
    const std::string& s;
OP_SET_BLOCK_PUFS_INFO
    const std::string& s;
OP_DELETE_DEPRECATED_BLOCK_PUFS_INFO
    const std::string& s;
OP_START_LOG_SEGMENT
OP_END_LOG_SEGMENT
OP_INVALID
