//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//
#include <glog/logging.h>

#include <thread>

#include "edit/roller.h"
#include "ha/operations.h"
#include "ha/ha_state.h"
#include "safemode/safemode.h"

DECLARE_double(edit_log_autoroll_txns_threshold);
DECLARE_uint64(edit_log_autoroll_txns_min_threshold);
DECLARE_uint64(edit_log_autoroll_period_threshold_ms);
DECLARE_uint64(edit_log_force_autoroll_period_threshold_ms);
DECLARE_uint64(edit_log_autoroll_failover_threshold_ms);
DECLARE_uint64(edit_log_autoroll_check_interval_ms);

namespace dancenn {

EditLogRoller::EditLogRoller(std::shared_ptr<EditLogContextBase> context,
                             NameSpace* ns)
    : edit_log_context_(context),
      ns_(ns) {
}

EditLogRoller::~EditLogRoller() {
  Stop();
}

void EditLogRoller::Start() {
  CHECK_NOTNULL(ha_state_);
  CHECK_NOTNULL(safemode_);

  if (!worker_.get()) {
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::static_pointer_cast<cnetpp::concurrency::Task>(
            std::make_shared<RollTask>(this)), "EditRoller");
  }
  worker_->Start();
}

void EditLogRoller::Stop() {
  if (worker_.get()) {
    worker_->Stop();
    worker_.reset();
  }
}

EditLogRoller::RollTask::RollTask(EditLogRoller* roller) : roller_(roller) {
  CHECK_NOTNULL(roller_);
  last_roll_time_ = std::chrono::steady_clock::now();
}

void EditLogRoller::RollTask::Stop() {
  std::unique_lock<std::mutex> lock(cv_mutex_);
  cnetpp::concurrency::Task::Stop();
  cv_.notify_all();
}

bool EditLogRoller::RollTask::operator()(void *arg) {
  (void) arg;
  while (!IsStopped()) {
    auto last_written_txid = roller_->edit_log_context_->GetLastWrittenTxId();
    auto cur_segment_txid = roller_->edit_log_context_->GetCurSegmentTxId();
    auto wait_time_ms = roller_->edit_log_context_->GetWaitSyncTime();
    VLOG(10) << "last_written_txid=" << last_written_txid
             << " cur_segment_txid=" << cur_segment_txid
             << " wait_time_ms=" << wait_time_ms;

    uint64_t edit_gap = last_written_txid - cur_segment_txid;
    int64_t time_gap = std::chrono::duration_cast<std::chrono::milliseconds>
        (std::chrono::steady_clock::now() - last_roll_time_).count();
    if (edit_gap > FLAGS_edit_log_autoroll_txns_threshold) {
      LOG(INFO) << "NameNode start to roll edit log(edits gap > threshold) "
          << cur_segment_txid << " " << last_written_txid;
      RollEditLog();
    } else if (time_gap > 0 &&
               time_gap > FLAGS_edit_log_autoroll_period_threshold_ms) {
      // when edit logs are produced very slow, roll edit log frequently will
      // produce lots of ledgers with small size, which as a result will create
      // more znode on zookeeper.
      // so if edit logs are produced very slow, we will roll edit log for once
      // each day.
      if (edit_gap > FLAGS_edit_log_autoroll_txns_min_threshold) {
        LOG(INFO) << "NameNode start to roll edit log(edit gap: " << edit_gap
                  << " > roll transactions min threshold: "
                  << FLAGS_edit_log_autoroll_txns_min_threshold << ")"
                  << cur_segment_txid << " " << last_written_txid;
        RollEditLog();
      } else if (time_gap > FLAGS_edit_log_force_autoroll_period_threshold_ms) {
        LOG(INFO) << "NameNode start to roll edit log(time gap: " << time_gap
                  << " > force roll threshold: "
                  << FLAGS_edit_log_force_autoroll_period_threshold_ms
                  << cur_segment_txid << " " << last_written_txid;
        RollEditLog();
      } else {
        // log every 600 seconds
        LOG_EVERY_N(INFO, 120)
            << "NameNode will not roll edit log(time gap: " << time_gap
            << " <= force roll threshold: "
            << FLAGS_edit_log_force_autoroll_period_threshold_ms
            << "&& edit gap: " << edit_gap
            << " <= roll transactions min threshold: "
            << FLAGS_edit_log_autoroll_txns_min_threshold << ")"
            << cur_segment_txid << " " << last_written_txid;
      }
    } else if (wait_time_ms > 0 &&
               wait_time_ms > FLAGS_edit_log_autoroll_failover_threshold_ms) {
      LOG(INFO) << "NameNode start to roll edit log(wait time > threshold), need failover "
          << cur_segment_txid << " " << last_written_txid << " " << wait_time_ms;
      RollEditLog();
    }
    {
      std::unique_lock<std::mutex> lock(cv_mutex_);
      cv_.wait_for(lock,
                   std::chrono::milliseconds(
                       FLAGS_edit_log_autoroll_check_interval_ms),
                   [&]() { return this->IsStopped(); });
    }
  }
  return true;
}

void EditLogRoller::RollTask::RollEditLog() {
  auto res = roller_->ha_state_->CheckOperation(OperationsCategory::kJournal);
  if (res.first.HasException()) {
    LOG(WARNING) << "Abort Roll Edit log due to check operation failed: "
                    << res.first.ToString();
    return;
  }

  if (roller_->safemode_->IsOn()) {
    LOG(WARNING) << "Abort Roll Edit log due to safe mode is on.";
    return;
  }

//  res.second.unlock();  // release the barrier's read lock
//
//  std::unique_lock<ReadWriteLock> guard(*(roller_->barrier_));
// for safety we also keep read_lock with barrier_
  {
    auto txid = roller_->edit_log_context_->RollEditLog();
    if (txid == kInvalidTxId) {
      LOG(WARNING) << "RollEditLog Failed";
      return;
    }
    last_roll_time_ = std::chrono::steady_clock::now();
    roller_->ns_->WriteEndNop(txid - 1);
    roller_->ns_->WriteStartNop(txid);
    LOG(INFO) << "Roll Edit log success.";
  }
}

}  // namespace dancenn

