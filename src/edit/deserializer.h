//
// Copyright 2018 Panfeng Ran <<EMAIL>>
//

#ifndef EDIT_DESERIALIZER_H_
#define EDIT_DESERIALIZER_H_

#include <glog/logging.h>

#include <memory>
#include <string>

#include "edit/edit_log_cfs_op.h"
#include "edit/op/all.h"
#include "edit/serializer.h"

namespace dancenn {
class OpDeSerializer {
 public:
  std::shared_ptr<EditLogOp> Deserialize(std::string const &serialized_op) {
    CHECK_GT(serialized_op.length(), 0);
    CHECK_LT(serialized_op.length(), 128 * 1024 * 1024);
    std::stringstream ss(serialized_op);
    uint8_t op_code;
    ReadField(&op_code, &ss);
    CHECK_NE(op_code, kInvalidTxId);
    int64_t tx_id;
    ReadField(&tx_id, &ss);
    std::shared_ptr<EditLogOp> op;
    if (op_code != OP_CFS) {
      op.reset(op_instantizer_.Instantiate(op_code));
    } else {
      op = DeserializeCfsOp(&ss);
    }
    op->SetOpCode(op_code);
    op->SetTxid(tx_id);
    op->ReadFields(&ss);
    return op;
  }
 private:
  OpInstantizer op_instantizer_;
};
}  // namespace dancenn
#endif  // EDIT_DESERIALIZER_H_
