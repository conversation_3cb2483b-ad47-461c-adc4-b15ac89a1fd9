// Copyright 2017 He <PERSON>yi <<EMAIL>>

#ifndef EDIT_SERIALIZER_H_
#define EDIT_SERIALIZER_H_

#include <google/protobuf/message.h>
#include <inode.pb.h>
#include <fsimage.pb.h>

#include <cstdint>
#include <vector>
#include <string>
#include <sstream>
#include <algorithm>

#include "edit/edit_types.h"
#include "block_manager/block.h"
#include "base/platform.h"
#include "base/writable_util.h"
#include "edit_log_op.h"  // NOLINT(build/include)

namespace dancenn {

// implements (de)serialization with same behavior as in
// org.apache.hadoop.hdfs.server.namenode.FSEditLogOp

// write fixed bool
inline void WriteField(const bool *value, std::ostream *ss) {
  bool vle = *value;
  ss->write(reinterpret_cast<const char *>(&vle), 1);
}

// write fixed int8
inline void WriteField(const int8_t *value, std::ostream *ss) {
  uint8_t vle = static_cast<uint8_t>(*value);
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint8_t));
}

// write fixed uint8
inline void WriteField(const uint8_t *value, std::ostream *ss) {
  uint8_t vle = *value;
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint8_t));
}

// write fixed uint16 big endian
inline void WriteField(const uint16_t *value, std::ostream *ss) {
  uint16_t vle = platform::HostToBigEndian(*value);
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint16_t));
}

// write fixed int32 big endian
inline void WriteField(const int32_t *value, std::ostream *ss) {
  uint32_t vle = platform::HostToBigEndian(static_cast<uint32_t>(*value));
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint32_t));
}

// write fixed uint32 big endian
inline void WriteField(const uint32_t *value, std::ostream *ss) {
  uint32_t vle = platform::HostToBigEndian(*value);
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint32_t));
}

// write fixed int64 big endian
inline void WriteField(const int64_t *value, std::ostream *ss) {
  uint64_t vle = platform::HostToBigEndian(static_cast<uint64_t>(*value));
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint64_t));
}

// write fixed uint64 big endian
inline void WriteField(const uint64_t *value, std::ostream *ss) {
  uint64_t vle = platform::HostToBigEndian(*value);
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint64_t));
}

// write fixed uint16 + raw big endian
inline void WriteField(const std::string *value, std::ostream *ss) {
  uint16_t len = value->size();
  WriteField(&len, ss);
  ss->write(&value->front(), len);
}

// read fixed bool
inline void ReadField(bool *value, std::istream *ss) {
  bool vle;
  ss->read(reinterpret_cast<char *>(&vle), 1);
  *value = vle;
}

// read fixed int8
inline void ReadField(int8_t *value, std::istream *ss) {
  uint8_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint8_t));
  *value = static_cast<int8_t>(vle);
}

// read fixed uint8
inline void ReadField(uint8_t *value, std::istream *ss) {
  uint8_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint8_t));
  *value = vle;
}

// read fixed uint16 big endian
inline void ReadField(uint16_t *value, std::istream *ss) {
  uint16_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint16_t));
  *value = platform::BigEndianToHost(vle);
}

// read fixed int32 big endian
inline void ReadField(int32_t *value, std::istream *ss) {
  uint32_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint32_t));
  *value = platform::BigEndianToHost(vle);
}

// read fixed uint32 big endian
inline void ReadField(uint32_t *value, std::istream *ss) {
  uint32_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint32_t));
  *value = platform::BigEndianToHost(vle);
}

// read fixed int64 big endian
inline void ReadField(int64_t *value, std::istream *ss) {
  uint64_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint64_t));
  *value = static_cast<int64_t>(platform::BigEndianToHost(vle));
}

// read fixed uint64 big endian
inline void ReadField(uint64_t *value, std::istream *ss) {
  uint64_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint64_t));
  *value = platform::BigEndianToHost(vle);
}

// read fixed uint16 + raw big endian
inline void ReadField(std::string *value, std::istream *ss) {
  uint16_t len;
  ReadField(&len, ss);
  value->resize(len);
  ss->read(reinterpret_cast<char *>(&value->front()), len);
}

// write fixed OpCode(uint8)
inline void WriteField(const OpCode *value, std::ostream *ss) {
  uint8_t vle = static_cast<uint8_t>(*value);
  ss->write(reinterpret_cast<const char *>(&vle), sizeof(uint8_t));
}

// read fixed OpCode(uint8)
inline void ReadField(OpCode *value, std::istream *ss) {
  uint8_t vle;
  ss->read(reinterpret_cast<char *>(&vle), sizeof(uint8_t));
  *value = static_cast<OpCode>(vle);
}

// OP_SYMLINK | OP_CLOSE | OP_ADD | OP_MKDIR
// write hdfs varint + raw +
//       hdfs varint + raw +
//       uint16 big endian
// org.apache.hadoop.fs.permission.PermissionStatus.write
inline void WriteField(const PermissionStatus *value, std::ostream *ss) {
  WriteStringWithVIntPrefix(ss, value->username());
  WriteStringWithVIntPrefix(ss, value->groupname());
  uint16_t perm = static_cast<uint16_t>(value->permission());
  WriteField(&perm, ss);
}

// OP_SYMLINK | OP_CLOSE | OP_ADD | OP_MKDIR
// read hdfs varint + raw +
//      hdfs varint + raw +
//      uint16 big endian
// org.apache.hadoop.fs.permission.PermissionStatus.read
inline void ReadField(PermissionStatus *value, std::istream *ss) {
  ReadStringWithVIntPrefix(ss, value->mutable_username());
  ReadStringWithVIntPrefix(ss, value->mutable_groupname());
  uint16_t perm;
  ReadField(&perm, ss);
  value->set_permission(perm);
}

// OP_RENAME
// write fixed uint32 big endian + raw
// org.apache.hadoop.io.write
inline void WriteField(const RenameOptions *options, std::ostream *ss) {
  std::string value = options->value();
  uint32_t len = value.size();
  WriteField(&len, ss);
  ss->write(&value.front(), len);
}

// OP_RENAME
// read fixed uint32 big endian + raw
// org.apache.hadoop.io.read
inline void ReadField(RenameOptions *options, std::istream *ss) {
  std::string value;
  uint32_t len;
  ReadField(&len, ss);
  value.resize(len);
  ss->read(reinterpret_cast<char *>(&value[0]), len);
  options->Assign(value);
}

// OP_ADD | OP_MKDIR
// | 1bits  | 1bits    | 1bits | 2bits | 3bits       |
// | unused | has_name | scope | type  | permissions |
// if has_no_name
// v = scope << 5 | type << 3 | permissions
// if has_name
// v = 1 << 6 | scope << 5 | type << 3 | permissions
// write fixed uint8 + fixed uint16 big endian + raw NOTES raw in JAVA utf8
// org.apache.hadoop.hdfs.server.namenode.write(List<AclEntry> ...)
inline void WriteField(const ::cloudfs::AclEntryProto *acl,
                       std::ostream *ss) {
  uint8_t v = (static_cast<uint8_t>(acl->scope()) << 5)
      | (static_cast<uint8_t>(acl->type()) << 3)
      | static_cast<uint8_t>(acl->permissions());
  if (acl->has_name()) {
    v |= (1 << 6);
  }
  WriteField(&v, ss);
  if (acl->has_name()) {
    std::string name = acl->name();
    WriteField(&name, ss);
  }
}

using AclEntryScopeProto = ::cloudfs::AclEntryProto_AclEntryScopeProto;
using AclEntryTypeProto = ::cloudfs::AclEntryProto_AclEntryTypeProto;
using FsActionProto = ::cloudfs::AclEntryProto_FsActionProto;

// OP_ADD | OP_MKDIR
// | 1bits  | 1bits    | 1bits | 2bits | 3bits       |
// | unused | has_name | scope | type  | permissions |
// if has_no_name
// v = scope << 5 | type << 3 | permissions
// if has_name
// v = 1 << 6 | scope << 5 | type << 3 | permissions
// read fixed uint8 + fixed uint16 big endian + raw NOTES raw in JAVA utf8
// org.apache.hadoop.hdfs.server.namenode.write(List<AclEntry> ...)
inline void ReadField(::cloudfs::AclEntryProto *acl,
                      std::istream *ss) {
  uint8_t v;
  ReadField(&v, ss);
  acl->set_scope(static_cast<AclEntryScopeProto>((v >> 5) & 1));
  acl->set_type(static_cast<AclEntryTypeProto>((v >> 3) & 3));
  acl->set_permissions(static_cast<FsActionProto>(v & 7));
  if ((v & (1 << 6)) > 0) {
    std::string name;
    ReadField(&name, ss);
    acl->set_name(name);
  }
}

// protobuf varint32
// only used by WriteField(const ::google::protobuf::Message *message, ...)
inline void WriteProtoVarint32(std::ostream *ss, int32_t value) {
  while (true) {
    if ((value & ~0x7F) == 0) {
      WriteByte(ss, static_cast<int8_t>(value));
      return;
    } else {
      WriteByte(ss, static_cast<int8_t>((value & 0x7F) | 0x80));
      value = static_cast<int32_t>(static_cast<uint32_t>(value) >> 7);
    }
  }
}

// protobuf varint32
// only used by  ReadField(::google::protobuf::Message *message, ...)
inline int32_t ReadProtoVarint32(std::istream *ss) {
  uint8_t first_byte = ReadByte(ss);
  if ((first_byte & 0x80) == 0) {
    return first_byte;
  }
  int32_t result = first_byte & 0x7f;
  int32_t offset = 7;
  for (; offset < 32; offset += 7) {
    int32_t b = ReadByte(ss);
    result |= (b & 0x7f) << offset;
    if ((b & 0x80) == 0) {
      return result;
    }
  }
  // Keep reading up to 64 bits.
  for (; offset < 64; offset += 7) {
    int32_t b = ReadByte(ss);
    if ((b & 0x80) == 0) {
      return result;
    }
  }
  return -1;
}

// OP_ADD_BLOCK | OP_UPDATE_BLOCKS
// write hdfs varint : fixed uint64, hdfs varint, hdfs varint | ...
// org.apache.hadoop.hdfs.server.namenode.FSImageSerialization.writeCompactBlockArray
inline void WriteCompactField(const CompactBlockArray *blkarr,
                              std::ostream *ss) {
  WriteVInt64(ss, blkarr->size());
  int64_t prev_numbytes = 0;
  int64_t prev_genstamp = 0;
  for (auto &blk : *blkarr) {
    uint64_t blkid = blk.blockid();
    int64_t delta_numbytes = blk.numbytes() - prev_numbytes;
    int64_t delta_genstamp = blk.genstamp() - prev_genstamp;
    prev_numbytes = blk.numbytes();
    prev_genstamp = blk.genstamp();

    WriteField(&blkid, ss);
    WriteVInt64(ss, delta_numbytes);
    WriteVInt64(ss, delta_genstamp);
  }
}

// OP_ADD_BLOCK | OP_UPDATE_BLOCKS
// read hdfs varint : fixed uint64, hdfs varint, hdfs varint | ...
// org.apache.hadoop.hdfs.server.namenode.FSImageSerialization.readCompactBlockArray
inline void ReadCompactField(CompactBlockArray *blkarr,
                             std::istream *ss) {
  uint64_t size = ReadVInt64(ss);
  blkarr->reserve(size);
  int64_t prev_numbytes = 0;
  int64_t prev_genstamp = 0;
  for (size_t i = 0; i < size; i++) {
    uint64_t blkid;
    ReadField(&blkid, ss);
    int64_t numbytes = prev_numbytes + ReadVInt64(ss);
    int64_t genstamp = prev_genstamp + ReadVInt64(ss);
    prev_numbytes = numbytes;
    prev_genstamp = genstamp;

    ::cloudfs::BlockProto blk;
    blk.set_blockid(blkid);
    blk.set_numbytes(numbytes);
    blk.set_genstamp(genstamp);
    blkarr->push_back(blk);
  }
}

// OP_ADD | OP_CLOSE
// write fixed uint64 + fixed uint64 + fixed uint64
// this.blocks = readBlocks(in, logVersion);
inline void ReadField(::cloudfs::BlockProto *blk,
                      std::istream *ss) {
  uint64_t blkid;
  uint64_t numbytes;
  uint64_t genstamp;
  ReadField(&blkid, ss);
  ReadField(&numbytes, ss);
  ReadField(&genstamp, ss);
  blk->set_blockid(blkid);
  blk->set_numbytes(numbytes);
  blk->set_genstamp(genstamp);
}

// OP_ADD | OP_CLOSE
// write fixed uint64 + fixed uint64 + fixed uint64
// new ArrayWritable(Block.class, blocks).write(out)
inline void WriteField(const ::cloudfs::BlockProto *blk,
                       std::ostream *ss) {
  uint64_t blkid = blk->blockid();
  uint64_t numbytes = blk->numbytes();
  uint64_t genstamp = blk->genstamp();
  WriteField(&blkid, ss);
  WriteField(&numbytes, ss);
  WriteField(&genstamp, ss);
}

// OP_ADD | OP_MKDIR
// write fixed uint32 + array of AclEntry
// org.apache.hadoop.hdfs.server.namenode.read(List<AclEntry> ...)
// OP_CONCAT_DELETE
// write fixed uint32 + array of src :: NOTES in java string use utf8
// org.apache.hadoop.hdfs.server.namenode.read
template<class T>
inline void ReadVectorField(std::vector<T> *arr, std::istream *ss) {
  uint32_t size;
  ReadField(&size, ss);
  arr->reserve(size);
  for (size_t i = 0; i < size; i++) {
    T item;
    ReadField(&item, ss);
    arr->push_back(item);
  }
}

// OP_ADD | OP_MKDIR
// write fixed uint32 + array of AclEntry
// org.apache.hadoop.hdfs.server.namenode.write(List<AclEntry> ...)
// OP_CONCAT_DELETE
// write fixed uint32 + array of src :: NOTES in java string use utf8
// org.apache.hadoop.hdfs.server.namenode.write
template<class T>
inline void WriteVectorField(const std::vector<T> *arr, std::ostream *ss) {
  uint32_t size = arr->size();
  WriteField(&size, ss);
  for (auto &item : *arr) {
    WriteField(&item, ss);
  }
}

inline void WriteField(const ::google::protobuf::Message *message,
                       std::ostream *ss) {
  std::string s;
  message->SerializeToString(&s);
  WriteProtoVarint32(ss, s.size());
  ss->write(&s.front(), s.size());
}

inline void ReadField(::google::protobuf::Message *message, std::istream *ss) {
  std::string s;
  int32_t len = ReadProtoVarint32(ss);
  s.resize(len);
  ss->read(&s.front(), static_cast<size_t>(len));
  message->ParseFromString(s);
}

// OP_SET_ACL
// use pb varint not hdfs varint in Java
// b.addAllEntries(PBHelper.convertAclEntryProto(aclEntries));
// b.build().writeDelimitedTo(out);
inline void WriteField(const cloudfs::AclEditLogProto *value,
                       std::ostream *ss) {
  WriteField(static_cast<const ::google::protobuf::Message *>(value), ss);
}

// OP_SET_ACL
// use pb varint not hdfs varint in Java
// AclEditLogProto.parseDelimitedFrom in Java
inline void ReadField(cloudfs::AclEditLogProto *value,
                      std::istream *ss) {
  ReadField(static_cast<::google::protobuf::Message *>(value), ss);
}

// OP_SET_XATTR | OP_ADD | OP_REMOVE_XATTR | OP_MKDIR
// use pb varint not hdfs varint in Java
// b.addAllXAttrs(PBHelper.convertXAttrProto(xAttrs));
// b.build().writeDelimitedTo(out);
inline void WriteField(const cloudfs::XAttrEditLogProto *value,
                       std::ostream *ss) {
  WriteField(static_cast<const ::google::protobuf::Message *>(value), ss);
}

// OP_SET_XATTR | OP_ADD | OP_REMOVE_XATTR | OP_MKDIR
// use pb varint not hdfs varint in Java
// XAttrEditLogProto.parseDelimitedFrom(in);
inline void ReadField(cloudfs::XAttrEditLogProto *value,
                      std::istream *ss) {
  ReadField(static_cast<::google::protobuf::Message *>(value), ss);
}

// OP_ACCESS_COUNTER_SNAPSHOT
// use pb varint not hdfs varint in Java
// snapshot.writeDelimitedTo(out);
inline void WriteField(
    const ::cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto *value,
    std::ostream *ss) {
  WriteField(static_cast<const ::google::protobuf::Message *>(value), ss);
}

// OP_ACCESS_COUNTER_SNAPSHOT
// use pb varint not hdfs varint in Java
// AccessCounterSnapshotProto.parseDelimitedFrom(in);
inline void ReadField(
    ::cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto *value,
    std::istream *ss) {
  ReadField(static_cast<::google::protobuf::Message *>(value), ss);
}

}  // namespace dancenn

#endif  // EDIT_SERIALIZER_H_

