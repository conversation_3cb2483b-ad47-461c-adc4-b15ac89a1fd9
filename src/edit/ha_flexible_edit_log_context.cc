// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/10/31
// Description

#include "edit/ha_flexible_edit_log_context.h"

#include <gflags/gflags.h>  // For DECLARE_string.
#include <glog/logging.h>   // For LOG, etc.

#include <chrono>        // For system_clock, etc.
#include <mutex>         // For unique_lock.
#include <shared_mutex>  // For shared_lock.
#include <thread>        // For this_thread.
#include <utility>       // For move.

#include "base/closure.h"         // For SynchronizedClosure.
#include "base/constants.h"       // For kLastAllocatedBlockIdKey, etc.
#include "base/file_utils.h"      // For FileUtils.
#include "base/stop_watch.h"      // For StopWatch.
#include "base/two_step_vlock.h"  // For TwoStepVUniqueLock.

DECLARE_string(namespace_meta_storage_ckpt_path);

namespace dancenn {

HAFlexibleEditLogContext::HAFlexibleEditLogContext(JavaRuntime* jvm,
                                                   int max_producers)
    : ha_state_(nullptr),
      meta_storage_(nullptr),
      mode_(EditLogConf::HA),
      jvm_(jvm),
      max_producers_(max_producers) {
}

HAFlexibleEditLogContext::HAFlexibleEditLogContext(HAStateBase* ha_state,
                                                   MetaStorage* meta_storage,
                                                   EditLogConf::HAMode mode,
                                                   JavaRuntime* jvm,
                                                   int max_producers)
    : ha_state_(ha_state),
      meta_storage_(meta_storage),
      mode_(mode),
      jvm_(jvm),
      max_producers_(max_producers) {
}

void HAFlexibleEditLogContext::SetMetaStorage(MetaStorage* meta_storage) {
  CHECK_NOTNULL(meta_storage);
  meta_storage_ = meta_storage;

  mode_ = EditLogConf::HA;
  std::string edit_log_conf_str;
  if (meta_storage_->GetNameSystemInfo(kEditLogConfKey, &edit_log_conf_str)) {
    EditLogConf edit_log_conf;
    CHECK(edit_log_conf.ParseFromString(edit_log_conf_str));
    mode_ = edit_log_conf.mode();
  }
  LOG(INFO) << "HAFlexibleEditLogContext::mode_=" << static_cast<int>(mode_);
  if (mode_ == EditLogConf::HA) {
    inner_edit_log_ctxs_.emplace_back(
        std::make_unique<HAEditLogContext>(jvm_, max_producers_));
  } else {
    inner_edit_log_ctxs_.emplace_back(std::make_unique<NonHAEditLogContext>());
  }
}

void HAFlexibleEditLogContext::SetHAState(HAStateBase* ha_state) {
  CHECK_NOTNULL(ha_state);
  ha_state_ = ha_state;
}

EditLogConf::HAMode HAFlexibleEditLogContext::GetHAMode() {
  CHECK_EQ(mode_ == EditLogConf::HA,
           GetCurrentInnerEditLogCtx()->GetHAMode() == EditLogConf::HA);
  return mode_;
}

bool HAFlexibleEditLogContext::UpdateConfProperty(const std::string& name,
                                                  const std::string& value) {
  return GetCurrentInnerEditLogCtx()->UpdateConfProperty(name, value);
}

void HAFlexibleEditLogContext::SetupSyncListener(
    std::shared_ptr<IEditLogSyncListener> listener) {
  listener_ = listener;
  GetCurrentInnerEditLogCtx()->SetupSyncListener(listener);
}

std::shared_ptr<IEditLogSyncListener> HAFlexibleEditLogContext::
    TestOnlyGetSyncListener() {
  return nullptr;
}

bool HAFlexibleEditLogContext::GetPeerNNAddr(std::string* addr) {
  return GetCurrentInnerEditLogCtx()->GetPeerNNAddr(addr);
}

bool HAFlexibleEditLogContext::GetAllStackTraces(std::string* stack_info) {
  return GetCurrentInnerEditLogCtx()->GetAllStackTraces(stack_info);
}

bool HAFlexibleEditLogContext::IsActiveInLease() const {
  return GetCurrentInnerEditLogCtx()->IsActiveInLease();
}

bool HAFlexibleEditLogContext::IsOpenForRead() {
  return GetCurrentInnerEditLogCtx()->IsOpenForRead();
}

void HAFlexibleEditLogContext::OpenForRead() {
  GetCurrentInnerEditLogCtx()->OpenForRead();
}

std::unique_ptr<EditLogInputContextBase> HAFlexibleEditLogContext::
    CreateInputContext(int64_t from_txid,
                       int64_t to_at_least_txid,
                       bool is_progress_ok) {
  return GetCurrentInnerEditLogCtx()->CreateInputContext(
      from_txid, to_at_least_txid, is_progress_ok);
}

bool HAFlexibleEditLogContext::IsOpenForWrite() {
  return GetCurrentInnerEditLogCtx()->IsOpenForWrite();
}

void HAFlexibleEditLogContext::InitJournalsForWrite() {
  {
    // The method HAState::StartActiveServicesInternal invokes
    // InitJournalsForWrite prior to calling OpenForWrite. If the current
    // NameNode is operating in standby mode within a non-HA (High Availability)
    // setting, it is crucial to trigger a core dump at this point. This measure
    // is taken to prevent the current NameNode from transitioning to active
    // mode and potentially causing a split-brain scenario.
    CHECK_NE(mode_, EditLogConf::StandbyNonHA) << "Please copy rocksdb ckpt";
  }
  GetCurrentInnerEditLogCtx()->InitJournalsForWrite();
}

int64_t HAFlexibleEditLogContext::OpenForWrite() {
  return GetCurrentInnerEditLogCtx()->OpenForWrite();
}

void HAFlexibleEditLogContext::Close() {
  GetCurrentInnerEditLogCtx()->Close();
}

int64_t HAFlexibleEditLogContext::GetLastWrittenTxId() {
  return GetCurrentInnerEditLogCtx()->GetLastWrittenTxId();
}

void HAFlexibleEditLogContext::SetNextTxId(int64_t txid) {
  LOG(INFO) << "SetNextTxId: " << txid;
  GetCurrentInnerEditLogCtx()->SetNextTxId(txid);
}

uint64_t HAFlexibleEditLogContext::GetLastAllocatedBlockId() {
  return GetCurrentInnerEditLogCtx()->GetLastAllocatedBlockId();
}

void HAFlexibleEditLogContext::SetLastAllocatedBlockId(int64_t id) {
  GetCurrentInnerEditLogCtx()->SetLastAllocatedBlockId(id);
}

uint64_t HAFlexibleEditLogContext::GetLastGenerationStampV2() {
  return GetCurrentInnerEditLogCtx()->GetLastGenerationStampV2();
}

void HAFlexibleEditLogContext::SetLastGenerationStampV2(int64_t gsv2) {
  GetCurrentInnerEditLogCtx()->SetLastGenerationStampV2(gsv2);
}

int64_t HAFlexibleEditLogContext::GetCurSegmentTxId() {
  return GetCurrentInnerEditLogCtx()->GetCurSegmentTxId();
}

int64_t HAFlexibleEditLogContext::GetWaitSyncTime() {
  return GetCurrentInnerEditLogCtx()->GetWaitSyncTime();
}

void HAFlexibleEditLogContext::LogSync(bool force) {
  return GetCurrentInnerEditLogCtx()->LogSync(force);
}

void HAFlexibleEditLogContext::LogSyncAll() {
  return GetCurrentInnerEditLogCtx()->LogSyncAll();
}

int64_t HAFlexibleEditLogContext::RollEditLog() {
  return GetCurrentInnerEditLogCtx()->RollEditLog();
}

void HAFlexibleEditLogContext::PurgeLogsOlderThan(int64_t min_tx_id_to_keep) {
  GetCurrentInnerEditLogCtx()->PurgeLogsOlderThan(min_tx_id_to_keep);
}

int64_t HAFlexibleEditLogContext::LogCfsOp(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogCfsOp(ss);
}

int64_t HAFlexibleEditLogContext::LogAllocateBlockId(
    const std::stringstream* ss,
    uint64_t* id) {
  return GetCurrentInnerEditLogCtx()->LogAllocateBlockId(ss, id);
}

int64_t HAFlexibleEditLogContext::LogGenerationStampV1(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogGenerationStampV1(ss);
}

int64_t HAFlexibleEditLogContext::LogGenerationStampV2(
    const std::stringstream* ss,
    uint64_t* gsv2) {
  return GetCurrentInnerEditLogCtx()->LogGenerationStampV2(ss, gsv2);
}

int64_t HAFlexibleEditLogContext::LogAllocateBlockIdAndGSv2(
    const std::stringstream* blkid_ss,
    const std::stringstream* gsv2_ss,
    uint64_t* blkid,
    uint64_t* gsv2) {
  return GetCurrentInnerEditLogCtx()->LogAllocateBlockIdAndGSv2(
      blkid_ss, gsv2_ss, blkid, gsv2);
}

int64_t HAFlexibleEditLogContext::LogTimes(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogTimes(ss);
}

int64_t HAFlexibleEditLogContext::LogOpenFile(const std::stringstream* ss,
                                              bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogOpenFile(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogAddBlock(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogAddBlock(ss);
}

int64_t HAFlexibleEditLogContext::LogUpdateBlocks(const std::stringstream* ss,
                                                  bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogUpdateBlocks(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogCloseFile(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogCloseFile(ss);
}

int64_t HAFlexibleEditLogContext::LogReassignLease(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogReassignLease(ss);
}

int64_t HAFlexibleEditLogContext::LogConcat(const std::stringstream* ss,
                                            bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogConcat(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogSetBlockPufsInfo(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetBlockPufsInfo(ss);
}

int64_t HAFlexibleEditLogContext::LogDeleteDeprecatedBlockPufsInfo(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogDeleteDeprecatedBlockPufsInfo(ss);
}

int64_t HAFlexibleEditLogContext::LogMkDir(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogMkDir(ss);
}

int64_t HAFlexibleEditLogContext::LogDelete(const std::stringstream* ss,
                                            bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogDelete(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogRenameOld(const std::stringstream* ss,
                                               bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogRenameOld(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogRename(const std::stringstream* ss,
                                            bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogRename(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogSymlink(const std::stringstream* ss,
                                             bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogSymlink(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogSetReplication(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetReplication(ss);
}

int64_t HAFlexibleEditLogContext::LogSetStoragePolicy(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetStoragePolicy(ss);
}

int64_t HAFlexibleEditLogContext::LogSetReplicaPolicy(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetReplicaPolicy(ss);
}

int64_t HAFlexibleEditLogContext::LogSetDirReplicaPolicy(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetDirReplicaPolicy(ss);
}

int64_t HAFlexibleEditLogContext::LogSetQuota(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetQuota(ss);
}

int64_t HAFlexibleEditLogContext::LogSetPermissions(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetPermissions(ss);
}

int64_t HAFlexibleEditLogContext::LogSetOwner(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetOwner(ss);
}

int64_t HAFlexibleEditLogContext::LogSetAcl(const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogSetAcl(ss);
}

int64_t HAFlexibleEditLogContext::LogSetXAttrs(const std::stringstream* ss,
                                               bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogSetXAttrs(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogRemoveXAttrs(const std::stringstream* ss,
                                                  bool to_log_rpc_ids) {
  return GetCurrentInnerEditLogCtx()->LogRemoveXAttrs(ss, to_log_rpc_ids);
}

int64_t HAFlexibleEditLogContext::LogAllowSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogAllowSnapshot(ss);
}

int64_t HAFlexibleEditLogContext::LogDisallowSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogDisallowSnapshot(ss);
}

int64_t HAFlexibleEditLogContext::LogCreateSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogCreateSnapshot(ss);
}

int64_t HAFlexibleEditLogContext::LogDeleteSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogDeleteSnapshot(ss);
}

int64_t HAFlexibleEditLogContext::LogRenameSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogRenameSnapshot(ss);
}

int64_t HAFlexibleEditLogContext::LogAccessCounterSnapshot(
    const std::stringstream* ss) {
  return GetCurrentInnerEditLogCtx()->LogAccessCounterSnapshot(ss);
}

EditLogConf::PreviousEditLogConf HAFlexibleEditLogContext::HASwitchFence() {
  LOG(FATAL) << "HAFlexibleEditLogContext::HASwitchFence is called";
  return EditLogConf::PreviousEditLogConf();
}

Status HAFlexibleEditLogContext::SwitchNonHAActiveToHAActive() {
  return SwitchNonHAActiveToHAActiveOp(this)();
}

Status HAFlexibleEditLogContext::SwitchHAActiveToNonHAActive() {
  return SwitchHAActiveToNonHAActiveOp(this)();
}

Status HAFlexibleEditLogContext::SwitchHAStandbyToNonHAActive() {
  return SwitchHAStandbyToNonHAActiveOp(this)();
}

EditLogContextBase* HAFlexibleEditLogContext::
    TestOnlyGetCurrentInnerEditLogCtx() {
  return GetCurrentInnerEditLogCtx();
}

void HAFlexibleEditLogContext::TestOnlySetCurrentInnerEditLogCtx(
    std::unique_ptr<EditLogContextBase>&& ctx) {
  std::unique_lock<ReadWriteLock> _(get_ctx_rwlock_);
  inner_edit_log_ctxs_.emplace_back(std::move(ctx));
}

EditLogContextBase* HAFlexibleEditLogContext::GetCurrentInnerEditLogCtx()
    const {
  std::shared_lock<ReadWriteLock> _(get_ctx_rwlock_);
  return GetCurrentInnerEditLogCtxUnsafe();
}

EditLogContextBase* HAFlexibleEditLogContext::GetCurrentInnerEditLogCtxUnsafe()
    const {
  CHECK(!inner_edit_log_ctxs_.empty());
  return inner_edit_log_ctxs_.back().get();
}

SwitchOp::SwitchOp(HAFlexibleEditLogContext* flexible_ctx)
    : flexible_ctx_(flexible_ctx) {
}

Status SwitchOp::operator()() {
  StopWatch sw;
  sw.Start();

  // The lock order of ha_state_::barrier_ and
  // HAFlexibleEditLogContext::get_ctx_rwlock_ must not be switched. This is
  // because, in the standard write path, these two locks are acquired in this
  // specific order.

  // [Step 1]
  // Further client or datanode write requests intending to write editlogs will
  // be refused. These requests will be met with a StandbyException.
  // See HAState::CheckOperation for more infos.
  // Refer to HAState::SetState:
  TwoStepVUniqueLock ha_barrier_vlock = LockHABarrier();
  LOG(INFO) << "LockBarrierForHASwitcher time(us): " << sw.NextStepTime();
  {
    Status check_ha_s = CheckHAState();
    if (!check_ha_s.IsOK()) {
      return check_ha_s;
    }
  }

  // [Step 2]
  // Ensure to prepare a checkpoint before initiating the switch, as a
  // precautionary measure against potential bugs.
  // Refer to Checkpointer::Run().
  {
    std::string root = FLAGS_namespace_meta_storage_ckpt_path;
    if (!FileUtils::Exists(root) &&
        !FileUtils::CreateDirectoryRecursively(root, S_IRWXU)) {
      return Status(Code::kError,
                    "Create checkpoint root failed, root: " + root);
    }
    auto current_ckpt_id =
        std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
                           std::chrono::system_clock::now().time_since_epoch())
                           .count());
    std::string current_ckpt_path =
        root + "/" + GetCkptPrefix() + "." + current_ckpt_id;
    Status s =
        flexible_ctx_->meta_storage_->CreateCheckpoint(current_ckpt_path);
    if (!s.IsOK()) {
      return Status(Code::kError,
                    "Create checkpoint failed, path: " + current_ckpt_path +
                        ", reason:" + s.message());
    }
    LOG(INFO) << "CreateCheckpoint time(us): " << sw.NextStepTime();
  }

  // [Step 3]
  // Subsequent LogXXX methods will remain blocked
  // until the current function has completed execution.
  std::unique_lock<ReadWriteLock> _(flexible_ctx_->get_ctx_rwlock_);
  // Wait for `BGEditLogCommitter::Commit` acquires the read lock.
  std::this_thread::sleep_for(std::chrono::seconds(1));
  LOG(INFO) << "Take rwlock_ time(us): " << sw.NextStepTime();

  // [Step 4]
  EditLogConf::PreviousEditLogConf previous_conf;
  {
    Status fence_s = Fence(&previous_conf);
    if (!fence_s.IsOK()) {
      return fence_s;
    }
    // Ensure TxFinish is invoked prior to calling PushINodeTXWriteTask. Failure
    // to do this may result in MetaStorage::writer_ reaching its capacity.
    flexible_ctx_->listener_->TxFinish(
        previous_conf.pending_begin_txid(),
        previous_conf.reserved_end_txid() - previous_conf.pending_begin_txid());
    for (int64_t txid = previous_conf.reserved_begin_txid();
         txid < previous_conf.reserved_end_txid();
         txid++) {
      // Refer to NameSpace::ApplyOpNop.
      flexible_ctx_->meta_storage_->PushINodeTXWriteTask(
          nullptr, txid, {}, nullptr, nullptr);
    }
    flexible_ctx_->meta_storage_->WaitNoPending();
    LOG(INFO) << "Fence time(us): " << sw.NextStepTime();
  }

  // [Step 5]
  // Write last_allocated_block_id, generation_stamp_v2 and edit_log_conf to
  // MetaStorage. The edit_log_conf is used to prevent the automatic use of
  // HAEditLogContext by the NameNode after a restart.
  //
  // There's no need to write last_ckpt_txid again, as it has already been
  // handled by previous invocations of MetaStorage::PushINodeTXWriteTask.
  //
  // There's no need to write last_inode_id, given that it's managed by
  // NameSpace::last_inode_id_. This is unlike generation_stamp_v2, which is
  // maintained within the JVM by FSEditLog::lastGenerationStampV2. For
  // additional details, please refer to NameSpace::NextINodeId.
  auto conf_end_txid = kInvalidTxId;
  {
    auto conf_begin_txid = previous_conf.reserved_end_txid();
    conf_end_txid = conf_begin_txid + 3;
    {
      // Refer to NameSpace::ApplyOpAllocateBlockId.
      char value[sizeof(uint64_t)];
      platform::WriteBigEndian(
          value, 0, previous_conf.last_allocated_block_id());
      flexible_ctx_->meta_storage_->TxFinish(conf_begin_txid, 1);
      flexible_ctx_->meta_storage_->OrderedPutNameSystemInfo(
          kLastAllocatedBlockIdKey,
          cnetpp::base::StringPiece(value, sizeof(uint64_t)),
          conf_begin_txid);
    }
    {
      // Refer to NameSpace::ApplyOpSetGenstampV2.
      flexible_ctx_->meta_storage_->TxFinish(conf_begin_txid + 1, 1);
      flexible_ctx_->meta_storage_->OrderedPutGenerationStampV2(
          previous_conf.last_generation_stamp_v2(),
          conf_begin_txid + 1,
          nullptr);
    }
    {
      EditLogConf conf;
      conf.set_mode(previous_conf.is_ha_edit_log_conf()
                        ? EditLogConf::ActiveNonHA
                        : EditLogConf::HA);
      *conf.mutable_previous_edit_log_conf() = previous_conf;
      conf.set_conf_begin_txid(conf_begin_txid);
      conf.set_conf_end_txid(conf_end_txid);
      CHECK(conf.IsInitialized());
      SynchronizedClosure done;
      flexible_ctx_->meta_storage_->TxFinish(conf_end_txid - 1, 1);
      flexible_ctx_->meta_storage_->OrderedPutNameSystemInfo(
          kEditLogConfKey, conf.SerializeAsString(), conf_end_txid - 1, &done);
      done.Await();
      flexible_ctx_->meta_storage_->WaitNoPending();
      CHECK_EQ(flexible_ctx_->meta_storage_->GetLastCkptTxId(),
               conf_end_txid - 1);
      flexible_ctx_->mode_ = conf.mode();
    }
    LOG(INFO)
        << "Write last_allocated_block_id/generation_stamp_v2/edit_log_conf "
           "time(us): "
        << sw.NextStepTime();
  }

  // [Step 6]
  // Prepare the new inner edit log context.
  InitNewEditLogContext(conf_end_txid,
                        previous_conf.last_allocated_block_id(),
                        previous_conf.last_generation_stamp_v2());
  LOG(INFO) << "Modify the inner edit log context time(us): "
            << sw.NextStepTime();
  return Status();
}

void SwitchOp::InitNewEditLogContext(int64_t next_txid,
                                     uint64_t last_allocated_block_id,
                                     uint64_t last_generation_stamp_v2) {
  StopWatch sw;
  sw.Start();

  std::unique_ptr<EditLogContextBase> inner_ctx = NewEditLogContext();
  CHECK_EQ(inner_ctx->GetHAMode() == EditLogConf::HA ? EditLogConf::HA
                                                     : EditLogConf::ActiveNonHA,
           flexible_ctx_->mode_);
  // Refer to the constructor of NameSpace.
  inner_ctx->SetupSyncListener(flexible_ctx_->listener_);
  LOG(INFO) << "SetupSyncListener time(us): " << sw.NextStepTime();

  // Refer to HAState::StartActiveServicesInternal.
  inner_ctx->InitJournalsForWrite();
  LOG(INFO) << "InitJournalsForWrite time(us): " << sw.NextStepTime();

  inner_ctx->SetNextTxId(next_txid);
  LOG(INFO) << "SetNextTxId time(us): " << sw.NextStepTime();
  // Refer to NameSpace::StartActive and the constructor of EditLogSender.
  // inner_ctx->SetNextTxId(meta_storage_->GetLastCkptTxId() + 1);
  // Refer to NameSpace::LoadLastAllocatedBlockId.
  inner_ctx->SetLastAllocatedBlockId(last_allocated_block_id);
  LOG(INFO) << "SetLastAllocatedBlockId time(us): " << sw.NextStepTime();
  // Refer to NameSpace::LoadGenerationStampV2.
  inner_ctx->SetLastGenerationStampV2(last_generation_stamp_v2);
  LOG(INFO) << "SetLastGenerationStampV2 time(us): " << sw.NextStepTime();

  CHECK_EQ(inner_ctx->OpenForWrite(), next_txid);
  LOG(INFO) << "OpenForWrite time(us): " << sw.NextStepTime();
  CHECK(inner_ctx->IsOpenForWrite());
  LOG(INFO) << "IsOpenForWrite time(us): " << sw.NextStepTime();

  SynchronizedClosure done;
  flexible_ctx_->meta_storage_->PushINodeTXWriteTask(
      nullptr, next_txid, {}, nullptr, &done);
  done.Await();
  flexible_ctx_->meta_storage_->WaitNoPending();
  flexible_ctx_->inner_edit_log_ctxs_.emplace_back(std::move(inner_ctx));
  LOG(INFO) << "WaitNoPending time(us): " << sw.NextStepTime();
}

SwitchNonHAActiveToHAActiveOp::SwitchNonHAActiveToHAActiveOp(
    HAFlexibleEditLogContext* flexible_ctx)
    : SwitchOp(flexible_ctx) {
}

TwoStepVUniqueLock SwitchNonHAActiveToHAActiveOp::LockHABarrier() {
  return flexible_ctx_->ha_state_->LockBarrierForHASwitcher();
}

Status SwitchNonHAActiveToHAActiveOp::CheckHAState() {
  if (!flexible_ctx_->ha_state_->IsActive()) {
    return Status(Code::kError,
                  "It is not recommended to switch a standby from non-HA mode "
                  "to HA mode. It is more advisable to copy RocksDB from an "
                  "active instance in HA mode.");
  }
  return Status();
}

std::string SwitchNonHAActiveToHAActiveOp::GetCkptPrefix() {
  return "ckpt_before_switch_to_ha";
}

Status SwitchNonHAActiveToHAActiveOp::Fence(
    EditLogConf::PreviousEditLogConf* previous_conf) {
  auto inner_ctx = flexible_ctx_->GetCurrentInnerEditLogCtxUnsafe();
  if (inner_ctx->GetHAMode() == EditLogConf::HA) {
    return Status(Code::kIsRetry);
  }
  *previous_conf = inner_ctx->HASwitchFence();
  return Status();
}

std::unique_ptr<EditLogContextBase> SwitchNonHAActiveToHAActiveOp::
    NewEditLogContext() {
  return std::make_unique<HAEditLogContext>(flexible_ctx_->jvm_,
                                            flexible_ctx_->max_producers_);
}

SwitchHAActiveToNonHAActiveOp::SwitchHAActiveToNonHAActiveOp(
    HAFlexibleEditLogContext* flexible_ctx)
    : SwitchOp(flexible_ctx) {
}

TwoStepVUniqueLock SwitchHAActiveToNonHAActiveOp::LockHABarrier() {
  return flexible_ctx_->ha_state_->LockBarrierForHASwitcher();
}

Status SwitchHAActiveToNonHAActiveOp::CheckHAState() {
  if (!flexible_ctx_->ha_state_->IsActive()) {
    return Status(
        Code::kError,
        "Converting a standby node in High Availability (HA) mode to active "
        "status in non-HA mode can be risky due to the possibility of some "
        "BookKeeper edit logs not being fully processed before the node "
        "becomes active. If you are certain about proceeding with this "
        "operation, please use the following command: curl "
        "/admin?cmd=turn_ha_standby_to_non_ha_active.");
  }
  return Status();
}

std::string SwitchHAActiveToNonHAActiveOp::GetCkptPrefix() {
  return "ckpt_before_switch_to_non_ha";
}

Status SwitchHAActiveToNonHAActiveOp::Fence(
    EditLogConf::PreviousEditLogConf* previous_conf) {
  auto inner_ctx = flexible_ctx_->GetCurrentInnerEditLogCtxUnsafe();
  if (inner_ctx->GetHAMode() != EditLogConf::HA) {
    return Status(Code::kIsRetry);
  }
  *previous_conf = inner_ctx->HASwitchFence();
  return Status();
}

std::unique_ptr<EditLogContextBase> SwitchHAActiveToNonHAActiveOp::
    NewEditLogContext() {
  return std::make_unique<NonHAEditLogContext>();
}

SwitchHAStandbyToNonHAActiveOp::SwitchHAStandbyToNonHAActiveOp(
    HAFlexibleEditLogContext* flexible_ctx)
    : SwitchOp(flexible_ctx) {
}

TwoStepVUniqueLock SwitchHAStandbyToNonHAActiveOp::LockHABarrier() {
  TwoStepVUniqueLock ha_barrier_vlock =
      flexible_ctx_->ha_state_->LockBarrierForHASwitcher();
  // TwoStepVLock::Prepare + TwoStepVLock::Lock = VLock::lock.
  ha_barrier_vlock.Lock();
  return std::move(ha_barrier_vlock);
}

Status SwitchHAStandbyToNonHAActiveOp::CheckHAState() {
  if (flexible_ctx_->ha_state_->GetState() !=
      cloudfs::HAServiceStateProto::STANDBY) {
    return Status(Code::kError, "Current ha state is not standby.");
  }
  return Status();
}

std::string SwitchHAStandbyToNonHAActiveOp::GetCkptPrefix() {
  return "ckpt_before_switch_to_non_ha";
}

Status SwitchHAStandbyToNonHAActiveOp::Fence(
    EditLogConf::PreviousEditLogConf* previous_conf) {
  auto inner_ctx = flexible_ctx_->GetCurrentInnerEditLogCtxUnsafe();
  if (inner_ctx->GetHAMode() != EditLogConf::HA) {
    return Status(Code::kIsRetry);
  }
  CHECK(flexible_ctx_->ha_state_->IsTailerStopped());
  *previous_conf = EditLogConf::PreviousEditLogConf();
  // The last_txid is already used. For more details, see
  // NameSpace::Apply/MetaStorage::BatchWrite. Therefore, the next available
  // txid should be incremented by 1.
  const int64_t next_txid = flexible_ctx_->meta_storage_->GetLastCkptTxId() + 1;
  previous_conf->set_pending_begin_txid(next_txid);
  previous_conf->set_pending_end_txid(next_txid);
  // Since the exact number of edit logs in transit over the network is unknown,
  // we should increase the txid by a large gap.
  const int64_t reserved_txid_gap =
      100 /*(q/ms)*/ * 1000 /*(ms/s)*/ * 60 /*(s/min)*/ * 3 /*(min)*/;
  previous_conf->set_reserved_begin_txid(next_txid);
  previous_conf->set_reserved_end_txid(next_txid + reserved_txid_gap);
  {
    // NameSpace::LoadLastAllocatedBlockId.
    BlockID last_allocated_block_id = 0;
    std::string block_id_str;
    if (flexible_ctx_->meta_storage_->GetNameSystemInfo(
            kLastAllocatedBlockIdKey, &block_id_str)) {
      CHECK_EQ(block_id_str.length(), sizeof(uint64_t));
      last_allocated_block_id =
          platform::ReadBigEndian<uint64_t>(&(block_id_str[0]), 0);
    } else {
      LOG(INFO) << "kLastAllocatedBlockIdKey is not found";
      last_allocated_block_id = kLastReservedBlockId;
    }
    CHECK_GT(last_allocated_block_id, 0);
    // BGEditLogCommitter::HASwitchFence.
    const uint64_t reserverd_block_id_gap = 65536;
    previous_conf->set_last_allocated_block_id(last_allocated_block_id +
                                               reserverd_block_id_gap);
  }
  {
    // NameSpace::LoadGenerationStampV2
    uint64_t last_gsv2 = 0;
    std::string gsv2_str;
    if (flexible_ctx_->meta_storage_->GetNameSystemInfo(kGenerationStampV2Key,
                                                        &gsv2_str)) {
      CHECK_EQ(gsv2_str.length(), sizeof(uint64_t));
      last_gsv2 = platform::ReadBigEndian<uint64_t>(&(gsv2_str[0]), 0);
    } else {
      LOG(INFO) << "kGenerationStampV2Key is not found";
      last_gsv2 = kLastReservedGenerationStamp;
    }
    CHECK_GT(last_gsv2, 0);
    // BGEditLogCommitter::HASwitchFence.
    const uint64_t reserverd_gs_gap = 65536;
    previous_conf->set_last_generation_stamp_v2(last_gsv2 + reserverd_gs_gap);
  }
  previous_conf->set_is_ha_edit_log_conf(true);
  // previous_conf->mutable_ha_edit_log_conf();
  return Status();
}

std::unique_ptr<EditLogContextBase> SwitchHAStandbyToNonHAActiveOp::
    NewEditLogContext() {
  return std::make_unique<NonHAEditLogContext>();
}

}  // namespace dancenn
