// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef EDIT_TAILER_BASE_H_
#define EDIT_TAILER_BASE_H_

#include <cstdint>

#include <memory>

#include "edit_log_op.h"  // NOLINT(build/include)
#include "namespace/namespace.h"

namespace dancenn {

class EditLogTailerBase {
 public:
  EditLogTailerBase() = default;
  EditLogTailerBase(std::shared_ptr<NameSpace> name_space,
                    std::shared_ptr<JavaRuntime> jvm) {}

 private:
  virtual void Apply(std::shared_ptr<EditLogOp> op) = 0;
};

}  // namespace dancenn

#endif  // EDIT_TAILER_BASE_H_
