//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cstdint>
#include <string>

#include "inode.pb.h"  // NOLINT(build/include)

namespace dancenn {

static const uint32_t INVALID_CALL_ID(-2);
static const std::string INVALID_CLIENT_ID("");

struct LogRpcInfo {
  bool to_log_rpc_ids_;
  std::string rpc_client_id_;
  uint32_t rpc_call_id_;

  LogRpcInfo()
      : to_log_rpc_ids_(false),
        rpc_client_id_(INVALID_CLIENT_ID),
        rpc_call_id_(INVALID_CALL_ID) {
  }
  LogRpcInfo(std::string client_id,
             uint32_t call_id,
             bool to_log_rpc_ids = true)
      : to_log_rpc_ids_(to_log_rpc_ids),
        rpc_client_id_(std::move(client_id)),
        rpc_call_id_(call_id) {
  }
  explicit LogRpcInfo(const RpcInfoPB& pb)
      : to_log_rpc_ids_(true),
        rpc_client_id_(pb.rpc_client_id()),
        rpc_call_id_(pb.rpc_call_id()) {
  }

  bool operator==(const LogRpcInfo& other) const {
    return rpc_client_id_ == other.rpc_client_id_ &&
           rpc_call_id_ == other.rpc_call_id_;
  }

  std::string ToString() const {
    if (to_log_rpc_ids_) {
      return ToPB().ShortDebugString();
    } else {
      return "";
    }
  }

  RpcInfoPB ToPB() const {
    RpcInfoPB pb;
    pb.set_rpc_client_id(rpc_client_id_);
    pb.set_rpc_call_id(rpc_call_id_);
    return pb;
  }

  bool IsValid() const {
    if (!to_log_rpc_ids_) {
      return false;
    }
    if (rpc_client_id_.empty()) {
      return false;
    }
    if (static_cast<int32_t>(rpc_call_id_) < 0) {
      return false;
    }
    return true;
  }
};
}  // namespace dancenn