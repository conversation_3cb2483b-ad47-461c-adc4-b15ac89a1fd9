// Copyright 2017 He <PERSON> <<EMAIL>>

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <chrono>
#include <thread>
#include <sstream>

#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "edit/tailer.h"
#include "edit/edit_log_context.h"
#include "edit/edit_log_op.h"
#include "edit/op/all.h"
#include "edit/serializer.h"
#include "namespace/namespace.h"

DECLARE_string(java_classpath);
DECLARE_int32(java_heap_size_mb);
DECLARE_int32(tail_period_ms);
DECLARE_bool(in_progress);
DECLARE_uint32(edit_log_assigner_max_num_pending_tasks);
DECLARE_uint32(edit_log_assigner_add_task_retry_sleep_ms);
DECLARE_uint32(edit_log_applyer_wait_no_pending_sleep_us);

namespace dancenn {

EditLogTailer::EditLogTailer(
  int64_t last_applied_txid,
  std::shared_ptr<JavaRuntime> jvm,
  std::shared_ptr<EditLogContextBase> context,
  NameSpace* ns)
    :last_applied_txid_(last_applied_txid),
     java_runtime_(jvm),
     context_(context),
     ns_(ns) {
  should_run_ = false;
}

EditLogTailer::~EditLogTailer() {
  Stop();
  StopApplyAssignerThread();
}

void EditLogTailer::Start() {
  // The initialization order of apply_assigner_thread_ and worker_ is crucial.
  // If worker_ is started prior to apply_assigner_thread_, it could potentially
  // execute the sequence
  // Run -> DoTail -> Apply -> apply_assigner_thread_->PendingCount
  // before apply_assigner_thread_ has been initialized.
  // This scenario could lead to a program crash
  if (!apply_assigner_thread_) {
    apply_assigner_thread_ =
        std::make_shared<cnetpp::concurrency::ThreadPool>("ApplyAssigner");
    apply_assigner_thread_->set_num_threads(1);
    uint32_t num_pending = FLAGS_edit_log_assigner_max_num_pending_tasks;
    if (num_pending > 0) {
      apply_assigner_thread_->set_max_num_pending_tasks(num_pending);
    }
    apply_assigner_thread_->Start();
  }

  if (!worker_) {
    LOG(INFO) << "EditLogTailer started, start txid:" << last_applied_txid_;
    should_run_ = true;
    worker_ = std::make_unique<cnetpp::concurrency::Thread>([this] () {
      Run();
      return true;
    }, "EditLogTailer");
    worker_->Start();
  }
}

void EditLogTailer::Stop(bool to_last_txid) {
  StopWatch sw;
  sw.Start();

  DEFER([&]{
    LOG(INFO) << "EditLogTailer stopped, last txid:" << last_applied_txid_;
  });
  if (worker_) {
    {
      std::unique_lock<std::mutex> lock(cv_mutex_);
      should_run_ = false;
      cv_.notify_all();
    }
    LOG(INFO) << "[HA] "
              << "EditLogTailer: cv_mutex_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    worker_->Stop();
    worker_.reset();
    LOG(INFO) << "[HA] "
              << "EditLogTailer: stop worker time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  if (!to_last_txid) {
    return;
  }

  if (!context_->IsOpenForRead()) {
    context_->OpenForRead();
    LOG(INFO) << "[HA] "
              << "EditLogTailer: OpenForRead time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  CatchupDuringFailover();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: CatchupDuringFailover time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  context_->Close();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: Close time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void EditLogTailer::CatchupDuringFailover() {
  LOG(INFO) << "Catching up during failover.";
  ns_->TailerCatchupTxid();
  DoTail(/*is_inprogress_ok=*/true, /*retryable=*/false);
}

void EditLogTailer::Apply(const std::string &serialized_op) {
  StopWatch sw(ns_->metrics().edit_tailer_deserialize_time_);
  sw.Start();

  auto op = op_deserializer.Deserialize(serialized_op);
  VLOG(8) << "last_applied_txid: " << last_applied_txid_
          << ", txid: " << op->txid()
          << ", op_name: " << op->op_name()
          << ", op_code: " << op->op_code()
          << ", apply_assigner_thread_pending: "
          << apply_assigner_thread_->PendingCount();
  CHECK_EQ(op->txid(), last_applied_txid_ + 1);

  sw.NextStep(ns_->metrics().edit_tailer_submit_time_);

  std::function<bool()> task_closure = [op, this] () -> bool {
    ns_->Apply(op);
    return true;
  };

  int retry = 0;
  while (true) {
    bool queued = apply_assigner_thread_->AddTask(task_closure);
    if (queued) {
      break;
    }
    uint32_t sleep_ms = FLAGS_edit_log_assigner_add_task_retry_sleep_ms;
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_ms));
    retry++;
    if (retry % 1000 == 0) {
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      LOG_WITH_LEVEL(WARNING) << "assign editlog op retry too many times: "
                              << std::to_string(retry);
    }
  }

  last_applied_txid_ = op->txid();
}

void EditLogTailer::Apply(std::shared_ptr<EditLogOp> op) {
  last_applied_txid_ = ns_->Apply(op);
}

void EditLogTailer::WaitNoPending(bool wait_db) {
  while (true) {
    if (!apply_assigner_thread_) {
      break;
    }
    auto n = apply_assigner_thread_->PendingCount() +
             apply_assigner_thread_->NumRunningTasks();
    if (n == 0) break;
    sched_yield();
  }

  if (wait_db) {
    while (true) {
      uint32_t us = FLAGS_edit_log_applyer_wait_no_pending_sleep_us;
      if (ns_->NumPendingLogToApply() == 0) {
        break;
      }
      usleep(us);
    }
    ns_->WaitNoPending();
  }
}

size_t EditLogTailer::GetApplyAssignerThreadPendingCount() const {
  auto apply_assigner_thread = apply_assigner_thread_;
  if (apply_assigner_thread == nullptr) {
    return 0;
  }
  return apply_assigner_thread->PendingCount();
}

size_t EditLogTailer::GetApplyAssignerThreadNumRunningTasks() const {
  auto apply_assigner_thread = apply_assigner_thread_;
  if (apply_assigner_thread == nullptr) {
    return 0;
  }
  return apply_assigner_thread->NumRunningTasks();
}

// is_progress_ok indicates whether in-progress edit logs
// shall be replayed or not.
//
// true: replay both finalized and in-progress logs, used in
// the situation that standby recover to the latest state of its
// predecessor on failover
//
// false: replay only finalized logs, used in the situation that
// standby catches up with active.
//
// retryable indicated whether retry is allowed if ReadOp fails
//
// true: ReadOp fails is considered as a trivial tailer EOF.
// scenarios: when the standby cannot catchup edit log,
// retry to avoid crash & restart.
//
// false: ReadOp fails is considered as a panic, and the program will exit.
// scenarios: when the pre-active cannot catchup edit log,
// fast fail to release active.
size_t EditLogTailer::DoTail(bool is_inprogress_ok, bool retryable) {
  // tail to the end
  LOG(INFO) << "Create editlog input context from next txid: "
            << last_applied_txid_ + 1;
  auto input = context_->CreateInputContext(
    last_applied_txid_ + 1, 0, is_inprogress_ok);
  CHECK(input);
  size_t txns = 0;
  auto last_log_time = std::chrono::system_clock::now();
  std::chrono::seconds replay_log_interval(1);
  int64_t last_done_txid = ns_->GetLastCkptTxId();
  int64_t last_tail_txid = last_applied_txid_;
  // replaying in-progress logs is non-interruptable
  while (is_inprogress_ok || should_run_) {
    auto start_time = std::chrono::steady_clock::now();
    // read op
    std::string serialized_op;
    if (!input->ReadOp(&serialized_op)) {
      if (retryable) {
        LOG(WARNING) << "Failed to read edit log, need retry";
        break;
      } else {
        LOG(FATAL) << "Failed to read edit log";
      }
    }
    if (serialized_op.length() == 0) {
      // eof
      ns_->TailerCatchupTxid();
      break;
    }
    // apply
    Apply(serialized_op);
    // metrics
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                        std::chrono::steady_clock::now() - start_time)
                        .count();
    MFC(ns_->metrics().edit_tailer_num_ops_)->Inc();
    MFH(ns_->metrics().edit_tailer_duration_)->Update(duration);
    // log
    auto now = std::chrono::system_clock::now();
    if (last_log_time + replay_log_interval < now)  {
      int64_t cur_txid = ns_->GetLastCkptTxId();
      LOG(INFO) << "Replaying edit log, last_txid: " << cur_txid
        << ", qps: " << last_applied_txid_ - last_tail_txid
        << ", tps: " << cur_txid - last_done_txid;
      ns_->ShowPendingCountForApplyThread();
      last_tail_txid = last_applied_txid_;
      last_done_txid = cur_txid;
      last_log_time = now;
    }
    txns++;
  }
  return txns;
}

void EditLogTailer::Run() {
  StopWatch sw;
  sw.Start();

  context_->OpenForRead();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: OpenForRead time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  ns_->TailerCatchupTxid();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: TailerCatchupTxid time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  while (should_run_) {
    size_t txns = DoTail(FLAGS_in_progress, /*retryable=*/true);
    LOG(INFO) << "EditLogTailer has loaded " << txns << " edit(s). "
              << "Current txid is " << last_applied_txid_ << ".";
    {
      std::unique_lock<std::mutex> lock(cv_mutex_);
      cv_.wait_for(lock, std::chrono::milliseconds(FLAGS_tail_period_ms),
                   [&]() {
                     return !should_run_;
                   });
    }
  }
  LOG(INFO) << "[HA] "
            << "EditLogTailer: after while time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  context_->Close();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: Close time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  java_runtime_->DetachCurrentThread();
  LOG(INFO) << "[HA] "
            << "EditLogTailer: DetachCurrentThread time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  WaitNoPending(true);
  LOG(INFO) << "[HA] "
            << "EditLogTailer: WaitNoPending time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  uint64_t txid_applied = last_applied_txid_;
  uint64_t txid_ondisk = ns_->GetLastCkptTxId();
  CHECK_EQ(txid_applied, txid_ondisk);

  LOG(INFO) << "[HA] " << "EditLogTailer worker stopped";
}

void EditLogTailer::StopApplyAssignerThread() {
  if (apply_assigner_thread_) {
    apply_assigner_thread_->Stop(true);
    apply_assigner_thread_.reset();
  }
}

}  // namespace dancenn

