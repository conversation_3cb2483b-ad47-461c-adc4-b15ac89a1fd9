// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/10/31
// Description

#ifndef EDIT_HA_FLEXIBLE_EDIT_LOG_CONTEXT_H_
#define EDIT_HA_FLEXIBLE_EDIT_LOG_CONTEXT_H_

#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <cstdint>  // For int64_t, etc.
#include <memory>   // For unique_ptr, shared_ptr.
#include <sstream>  // For stringstream.
#include <string>   // For string.
#include <vector>   // For vector.

#include "base/java.h"             // For JavaRuntime.
#include "base/read_write_lock.h"  // For ReadWriteLock.
#include "base/status.h"           // For Status, Code.
#include "edit/edit_log_context_base.h"  // For EditLogInputContextBase, EditLogContextBase.
#include "edit/edit_log_sync_listener.h"  // For IEditLogSyncListener.
#include "edit/ha_edit_log_context.h"     // For HAEditLogContext.
#include "edit/non_ha_edit_log_context.h"  // For NonHAEditLogContext.
#include "ha/ha_state_base.h"              // For HAStateBase.
#include "namespace/meta_storage.h"        // For MetaStorage.

namespace dancenn {

class SwitchOp;
class SwitchNonHAActiveToHAActiveOp;
class SwitchHAActiveToNonHAActiveOp;
class SwitchHAStandbyToNonHAActiveOp;

class HAFlexibleEditLogContext : public EditLogContextBase {
 public:
  HAFlexibleEditLogContext(JavaRuntime* jvm, int max_producers);
  // TestOnly.
  HAFlexibleEditLogContext(HAStateBase* ha_state,
                           MetaStorage* meta_storage,
                           EditLogConf::HAMode mode,
                           JavaRuntime* jvm,
                           int max_producers);
  virtual ~HAFlexibleEditLogContext() = default;

  void SetMetaStorage(MetaStorage* meta_storage);
  void SetHAState(HAStateBase* ha_state);

  // Common methods.
  EditLogConf::HAMode GetHAMode() override;
  bool UpdateConfProperty(const std::string& name,
                          const std::string& value) override;
  void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) override;
  std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() override;
  bool GetPeerNNAddr(std::string* addr) override;
  bool GetAllStackTraces(std::string* stack_info) override;

  // see EditLogContextBase::IsActiveInLease
  bool IsActiveInLease() const override;

  // Read related.
  bool IsOpenForRead() override;
  void OpenForRead() override;
  std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid,
      int64_t to_at_least_txid,
      bool is_progress_ok) override;
  // Write related.
  bool IsOpenForWrite() override;
  void InitJournalsForWrite() override;
  int64_t OpenForWrite() override;
  // Close.
  void Close() override;

  // TxID, BlockID and GS related.
  int64_t GetLastWrittenTxId() override;
  void SetNextTxId(int64_t txid) override;
  uint64_t GetLastAllocatedBlockId() override;
  void SetLastAllocatedBlockId(int64_t id) override;
  uint64_t GetLastGenerationStampV2() override;
  void SetLastGenerationStampV2(int64_t gsv2) override;
  int64_t GetCurSegmentTxId() override;

  // Sync related.
  int64_t GetWaitSyncTime() override;
  void LogSync(bool force = false) override;
  void LogSyncAll() override;

  int64_t RollEditLog() override;
  void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) override;

  // Not deprecated.
  int64_t LogCfsOp(const std::stringstream* ss) override;
  // Block related.
  int64_t LogAllocateBlockId(const std::stringstream* ss,
                             uint64_t* id) override;
  int64_t LogGenerationStampV1(const std::stringstream* ss) override;
  int64_t LogGenerationStampV2(const std::stringstream* ss,
                               uint64_t* gsv2) override;
  int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                    const std::stringstream* gsv2_ss,
                                    uint64_t* blkid,
                                    uint64_t* gsv2) override;
  // Dir tree related.
  int64_t LogTimes(const std::stringstream* ss) override;

  // Deprecated.
  // File related.
  int64_t LogOpenFile(const std::stringstream* ss,
                      bool to_log_rpc_ids = false) override;
  int64_t LogAddBlock(const std::stringstream* ss) override;
  int64_t LogUpdateBlocks(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  int64_t LogCloseFile(const std::stringstream* ss) override;
  int64_t LogReassignLease(const std::stringstream* ss) override;
  int64_t LogConcat(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  // Block related.
  int64_t LogSetBlockPufsInfo(const std::stringstream* ss) override;
  int64_t LogDeleteDeprecatedBlockPufsInfo(
      const std::stringstream* ss) override;
  // Dir tree related.
  int64_t LogMkDir(const std::stringstream* ss) override;
  int64_t LogDelete(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogRenameOld(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRename(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override;
  int64_t LogSymlink(const std::stringstream* ss,
                     bool to_log_rpc_ids = false) override;
  // Set* related.
  int64_t LogSetReplication(const std::stringstream* ss) override;
  int64_t LogSetStoragePolicy(const std::stringstream* ss) override;
  int64_t LogSetReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetDirReplicaPolicy(const std::stringstream* ss) override;
  int64_t LogSetQuota(const std::stringstream* ss) override;
  int64_t LogSetPermissions(const std::stringstream* ss) override;
  int64_t LogSetOwner(const std::stringstream* ss) override;
  int64_t LogSetAcl(const std::stringstream* ss) override;
  int64_t LogSetXAttrs(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override;
  int64_t LogRemoveXAttrs(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override;
  // Snapshot related.
  int64_t LogAllowSnapshot(const std::stringstream* ss) override;
  int64_t LogDisallowSnapshot(const std::stringstream* ss) override;
  int64_t LogCreateSnapshot(const std::stringstream* ss) override;
  int64_t LogDeleteSnapshot(const std::stringstream* ss) override;
  int64_t LogRenameSnapshot(const std::stringstream* ss) override;
  int64_t LogAccessCounterSnapshot(const std::stringstream* ss) override;

  // Switch related.
  EditLogConf::PreviousEditLogConf HASwitchFence() override;
  Status SwitchNonHAActiveToHAActive() override;
  Status SwitchHAActiveToNonHAActive() override;
  Status SwitchHAStandbyToNonHAActive() override;

  EditLogContextBase* TestOnlyGetCurrentInnerEditLogCtx();
  void TestOnlySetCurrentInnerEditLogCtx(
      std::unique_ptr<EditLogContextBase>&& ctx);

 private:
  EditLogContextBase* GetCurrentInnerEditLogCtx() const;
  EditLogContextBase* GetCurrentInnerEditLogCtxUnsafe() const;

 private:
  HAStateBase* ha_state_{nullptr};
  MetaStorage* meta_storage_{nullptr};
  EditLogConf::HAMode mode_;
  JavaRuntime* jvm_{nullptr};
  int max_producers_;
  std::shared_ptr<IEditLogSyncListener> listener_;
  mutable ReadWriteLock get_ctx_rwlock_;
  std::vector<std::unique_ptr<EditLogContextBase>> inner_edit_log_ctxs_;

 private:
  friend class SwitchOp;
  friend class SwitchNonHAActiveToHAActiveOp;
  friend class SwitchHAActiveToNonHAActiveOp;
  friend class SwitchHAStandbyToNonHAActiveOp;
};

class SwitchOp {
 public:
  explicit SwitchOp(HAFlexibleEditLogContext* flexible_ctx);

  Status operator()();

 protected:
  void InitNewEditLogContext(int64_t next_txid,
                             uint64_t last_allocated_block_id,
                             uint64_t last_generation_stamp_v2);

  virtual TwoStepVUniqueLock LockHABarrier() = 0;
  virtual Status CheckHAState() = 0;
  virtual std::string GetCkptPrefix() = 0;
  virtual Status Fence(EditLogConf::PreviousEditLogConf* previous_conf) = 0;
  virtual std::unique_ptr<EditLogContextBase> NewEditLogContext() = 0;

 protected:
  HAFlexibleEditLogContext* flexible_ctx_{nullptr};
};

class SwitchNonHAActiveToHAActiveOp : public SwitchOp {
 public:
  explicit SwitchNonHAActiveToHAActiveOp(
      HAFlexibleEditLogContext* flexible_ctx);

 protected:
  TwoStepVUniqueLock LockHABarrier() override;
  Status CheckHAState() override;
  std::string GetCkptPrefix() override;
  Status Fence(EditLogConf::PreviousEditLogConf* previous_conf) override;
  std::unique_ptr<EditLogContextBase> NewEditLogContext() override;
};

class SwitchHAActiveToNonHAActiveOp : public SwitchOp {
 public:
  explicit SwitchHAActiveToNonHAActiveOp(
      HAFlexibleEditLogContext* flexible_ctx);

 protected:
  TwoStepVUniqueLock LockHABarrier() override;
  Status CheckHAState() override;
  std::string GetCkptPrefix() override;
  Status Fence(EditLogConf::PreviousEditLogConf* previous_conf) override;
  std::unique_ptr<EditLogContextBase> NewEditLogContext() override;
};

class SwitchHAStandbyToNonHAActiveOp : public SwitchOp {
 public:
  explicit SwitchHAStandbyToNonHAActiveOp(
      HAFlexibleEditLogContext* flexible_ctx);

 protected:
  TwoStepVUniqueLock LockHABarrier() override;
  Status CheckHAState() override;
  std::string GetCkptPrefix() override;
  Status Fence(EditLogConf::PreviousEditLogConf* previous_conf) override;
  std::unique_ptr<EditLogContextBase> NewEditLogContext() override;
};

}  // namespace dancenn

#endif  // EDIT_HA_FLEXIBLE_EDIT_LOG_CONTEXT_H_
