//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#ifndef EDIT_SYNCER_H_
#define EDIT_SYNCER_H_

#include <cstdint>  // For int64_t.
#include <memory>   // For shared_ptr.
#include <mutex>    // For mutex, lock_guard.

#include "base/read_write_lock.h"     // For ReadWriteLock.
#include "base/ring_buffer.h"         // For RingBuffer.
#include "base/status.h"              // For Status.
#include "cnetpp/concurrency/task.h"  // For Task.

namespace dancenn {

class HAEditLogContext;
class IEditLogSyncListener;

// Task running background to sync edit log.
class BGEditLogSyncer : public cnetpp::concurrency::Task {
 public:
  BGEditLogSyncer(HAEditLogContext* context, int max_buffers);

  void SetupListener(std::shared_ptr<IEditLogSyncListener>& listener);
  void SetNextTxId(int64_t txid);
  bool Close(int64_t close_task_txid);

  // Only called by BGEditLogCommitter.
  bool Sync(int64_t txid);
  bool operator()(void* arg = nullptr) override;
  bool TestOnlyIsChannelFull();

  // Return value is [pending_begin_txid, pending_end_txid).
  void HASwitchFence(int64_t* pending_begin_txid, int64_t* pending_end_txid);

 private:
  // [begin_txid, end_txid)
  Status TxStart(int64_t* begin_txid, int64_t* end_txid);
  Status TxFinish(int64_t begin_txid, int64_t end_txid);

 private:
  ReadWriteLock producer_lock_;
  std::mutex consumer_mtx_;
  bool is_fenced_;
  HAEditLogContext* context_{nullptr};
  RingBuffer<int64_t> channel_;
  std::shared_ptr<IEditLogSyncListener> listener_;
  int64_t already_finished_task_txid_;
};

}  // namespace dancenn

#endif  // EDIT_SYNCER_H_
