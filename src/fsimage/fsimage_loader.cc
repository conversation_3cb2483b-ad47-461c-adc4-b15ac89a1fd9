// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "fsimage/fsimage_loader.h"

#include <gflags/gflags.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>

#include <regex>
#include <algorithm>
#include <memory>
#include <utility>

#include "fsimage.pb.h"  // NOLINT(build/include)
#include "namespace/namespace.h"
#include "namespace/inode.h"
#include "base/file_utils.h"
#include "base/properties.h"
#include "base/platform.h"
#include "base/path_util.h"
#include "base/defer.h"

DECLARE_string(fsimage_dir);
DECLARE_string(fsimage_file_name);
DECLARE_int32(dfs_meta_storage_parent_map_mask);
DECLARE_int32(namespace_type);

namespace dancenn {

const char* FSImageLoader::kVersionFileName = "VERSION";

const char* FSImageLoader::kLayoutVersionKey = "layoutVersion";
const char* FSImageLoader::kNamespaceIdKey = "namespaceID";
const char* FSImageLoader::kClusterIdKey = "clusterID";
const char* FSImageLoader::kStorageTypeKey = "storageType";
const char* FSImageLoader::kBlockPoolIdKey = "blockpoolID";
const char* FSImageLoader::kCTimeKey = "cTime";

const std::regex FSImageLoader::kMD5Regex("([0-9a-f]{32}) \\*(.+)");

const char* FSImageLoader::kMD5FileSuffix = ".md5";
const char* FSImageLoader::kSupportedStorageType = "NAME_NODE";
const char* FSImageLoader::kMagicHeader = "HDFSIMG1";

const char* FSImageLoader::kDefaultCodec =
  "org.apache.hadoop.io.compress.DefaultCodec";
const char* FSImageLoader::kGzipCodec =
  "org.apache.hadoop.io.compress.GzipCodec";

const std::map<std::string, FSImageLoader::SectionName>
FSImageLoader::kSectionNameToOrdinal = {
  {"NS_INFO", FSImageLoader::SectionName::kNSInfo},
  {"STRING_TABLE", FSImageLoader::SectionName::kStringTable},
  {"EXTENDED_ACL", FSImageLoader::SectionName::kExtendedAcl},
  {"INODE", FSImageLoader::SectionName::kINode},
  {"INODE_REFERENCE", FSImageLoader::SectionName::kINodeReference},
  {"SNAPSHOT", FSImageLoader::SectionName::kSnapshot},
  {"INODE_DIR", FSImageLoader::SectionName::kINodeDir},
        {"FILES_UNDERCONSTRUCTION",
         FSImageLoader::SectionName::
             kFilesUnderconstruction},  // NOLINT(whitespace/line_length)
        {"SNAPSHOT_DIFF", FSImageLoader::SectionName::kSnapshotDiff},
  {"SECRET_MANAGER", FSImageLoader::SectionName::kSecretManager},
  {"CACHE_MANAGER", FSImageLoader::SectionName::kCacheManager},
  {"ACCESS_COUNTER", FSImageLoader::SectionName::kAccessCounter},
};

FSImageLoader::FSImageLoader(NameSpace* ns)
    : fsimage_file_(FLAGS_fsimage_dir + "/" + FLAGS_fsimage_file_name),
      ns_(ns),
      parent_map_(1) {
  // CHECK_NOTNULL(ns_);
}

bool FSImageLoader::Load() {
  CHECK(FileUtils::IsFile(fsimage_file_)) << "FSImage: "
    << FLAGS_fsimage_dir << "/" << FLAGS_fsimage_file_name
    << " doesn't exist or is not a valid file.";

  LoadVersionFile();
  LOG(INFO) << "Loaded version file";

  LoadStoredMD5FromFile();
  LOG(INFO) << "Loaded md5 file";

  VerifySavedMD5();
  LOG(INFO) << "Verified md5 file";

  DoLoad();

  return true;
}

void FSImageLoader::RefreshNameSpaceInfo() {
  if (ns_) {
    ns_->LoadNameSpaceInfo();
  }
}

void FSImageLoader::LoadVersionFile() {
  std::string version_file = FLAGS_fsimage_dir + "/" + kVersionFileName;
  CHECK(FileUtils::IsFile(version_file)) << "VERSION file: " << version_file
      << " doesn't exist or is not a valid file.";

  Properties prop;
  CHECK(prop.Load(version_file)) << "Failed to parse VERSION file: "
    << version_file << ", it may not be a valid java Properties file.";

  int32_t layout_version = -1;
  CHECK(prop.GetInt<int32_t>(kLayoutVersionKey, &layout_version))
    << "No " << kLayoutVersionKey << " field in "
    << version_file << ", it's not a valid VERSION file.";
  CHECK_EQ(layout_version, kSupportedLayoutVersion)
    << "Unsupported layout version: " << layout_version
    << ", expect: " << kSupportedLayoutVersion;

  int64_t namespace_id = -1;
  CHECK(prop.GetInt<int64_t>(kNamespaceIdKey, &namespace_id))
    << "No " << kNamespaceIdKey << " field in "
    << version_file << ", it's not a valid VERSION file.";
  CHECK(namespace_id >= 0 && namespace_id <= INT64_MAX)
    << "Invalid namespace id " << namespace_id;

  std::string cluster_id;
  CHECK(prop.GetString(kClusterIdKey, &cluster_id))
    << "No " << kClusterIdKey << " field in "
    << version_file << ", it's not a valid VERSION file.";
  CHECK(!cluster_id.empty()) << "Empty " << kClusterIdKey
    << " field in VERSION file: " << version_file;

  int64_t ctime = -1;
  CHECK(prop.GetInt<int64_t>(kCTimeKey, &ctime))
    << "No " << kCTimeKey << " field in "
    << version_file << ", it's not a valid VERSION file.";

  std::string blockpool_id;
  CHECK(prop.GetString(kBlockPoolIdKey, &blockpool_id))
    << "No " << kBlockPoolIdKey << " field in "
    << version_file << ", it's not a valid VERSION file.";
  CHECK(!blockpool_id.empty()) << "Empty " << kBlockPoolIdKey
    << " field in VERSION file: " << version_file;

  std::string storage_type;
  CHECK(prop.GetString(kStorageTypeKey, &storage_type))
    << "No " << kStorageTypeKey << " field in "
    << version_file << ", it's not a valid VERSION file.";
  CHECK_EQ(storage_type, kSupportedStorageType) << "Unsupported storage type: "
    << storage_type << ", expected: " << kSupportedStorageType << ".";

  layout_version_ = static_cast<uint32_t>(layout_version);
  namespace_id_ = static_cast<uint64_t>(namespace_id);
  ctime_ = ctime;
  cluster_id_ = cluster_id;
  blockpool_id_ = blockpool_id;
}

void FSImageLoader::LoadStoredMD5FromFile() {
  std::string md5_file = fsimage_file_ + kMD5FileSuffix;
  CHECK(FileUtils::IsFile(md5_file)) << "MD5 file: " << md5_file
      << " doesn't exist or is not a valid file.";

  RandomAccessFile raf(md5_file);
  auto size = raf.Size();
  CHECK_GT(size, 0) << "Empty MD5 file: " << md5_file << ".";

  std::string line(size, '\0');
  cnetpp::base::StringPiece result;
  CHECK(raf.Read(0, &(line[0]), size, &result))
    << "Failed to read enough data from MD5 file: " << md5_file << ".";

  if (line.back() == '\n') {
    line.pop_back();
  }
  std::smatch match;
  CHECK(std::regex_match(line, match, kMD5Regex))
    << "Invalid content of MD5 file: " << md5_file;
  CHECK_EQ(match.size(), 3) << "Invalid content of MD5 file: " << md5_file;

  auto md5 = std::string(match[1].first, match[1].second);
  auto referenced = std::string(match[2].first, match[2].second);
  CHECK_EQ(referenced, FLAGS_fsimage_file_name) << "MD5 file at "
    << md5_file << " references file named " << referenced
    << " but we expected it to reference " << FLAGS_fsimage_dir << "/"
    << FLAGS_fsimage_file_name;
  CHECK(md5_.FromString(md5)) << "Invalid MD5 value: " << md5;
}

void FSImageLoader::VerifySavedMD5() {
  MD5Calculator c;
  CHECK(c.Update(fsimage_file_, 128 * 1024))
    << "Failed to calculate MD5 value of fsimage: " << fsimage_file_;

  auto md5 = c.Digest();
  CHECK(md5 == md5_) << "MD5 mismatch";
}

void FSImageLoader::DoLoad() {
  if (ns_) {
    ns_->LocalSaveLayoutVersion(layout_version_);
    ns_->LocalSaveCTime(ctime_);
    // CloudFS will generate a new namespace id,
    // just discard namespace id generated by Java name node.
    // ns_->LocalSaveNameSpaceId(namespace_id_);
    ns_->LocalSaveClusterId(cluster_id_);
    ns_->LocalSaveBlockPoolId(blockpool_id_);
    ns_->LocalSaveNumINodes(0);
  }

  LOG(INFO) << "Loaded layout version: " << layout_version_;
  LOG(INFO) << "Loaded ctime: " << ctime_;
  LOG(INFO) << "Discarded namespace id: " << namespace_id_;
  LOG(INFO) << "Loaded cluster id: " << cluster_id_;
  LOG(INFO) << "Loaded blockpool id: " << blockpool_id_;

  RandomAccessFile raf(fsimage_file_);
  auto size = raf.Size();
  CHECK_GE(size, 0) << "Failed to get size of fsimage file: " << fsimage_file_;
  CHECK_GE(size, kMinimumFileLength)
    << "Unrecognized format of fsimage file: " << fsimage_file_;

  const size_t kHeaderLength = strlen(kMagicHeader);
  char header_buffer[kHeaderLength];
  cnetpp::base::StringPiece h;
  if (!raf.Read(0, header_buffer, kHeaderLength, &h) ||
      h.size() != kHeaderLength) {
    LOG(FATAL) << "Failed to read magic header.";
  }
  CHECK_EQ(h, cnetpp::base::StringPiece(kMagicHeader, kHeaderLength))
    << "Magic header must be " << kMagicHeader << ".";

  char summary_length_buffer[kSummaryLengthFieldSize];
  cnetpp::base::StringPiece slb;
  if (!raf.Read(size - kSummaryLengthFieldSize, summary_length_buffer,
                kSummaryLengthFieldSize, &slb) ||
      slb.size() != kSummaryLengthFieldSize) {
    LOG(FATAL) << "Failed to read summary length from fsimage: "
      << fsimage_file_;
  }
  int32_t summary_length = static_cast<int32_t>(
      platform::ReadBigEndian<uint32_t>(summary_length_buffer, 0));
  if (summary_length < 0 || summary_length > size - kSummaryLengthFieldSize) {
    LOG(FATAL) << "Invalid length(" << summary_length
      << ") of summary for fsimage file: " << fsimage_file_;
  }

  auto summary_buffer = std::unique_ptr<char[]>(new char[summary_length]);
  cnetpp::base::StringPiece sb;
  if (!raf.Read(size - kSummaryLengthFieldSize - summary_length,
        summary_buffer.get(), summary_length, &sb) ||
      sb.size() != summary_length) {
    LOG(FATAL) << "Failed to read summary from fsimage: " << fsimage_file_;
  }

  cloudfs::fsimage::FileSummary fs;
  {
    google::protobuf::io::ArrayInputStream ais(
        reinterpret_cast<const uint8_t*>(sb.data()),
        static_cast<int>(sb.size()));
    ParseDelimitedFrom(&ais, &fs);
  }

  // release summary memory
  summary_buffer.reset();

  CHECK_EQ(fs.ondiskversion(), kFileVersion)
    << "Unsupported fsimage file version";

  if (fs.has_codec() &&
      fs.codec() != kDefaultCodec &&
      fs.codec() != kGzipCodec) {
    LOG(FATAL) << "Unsupported codec: " << fs.codec();
  }

  using Section = cloudfs::fsimage::FileSummary_Section;
  std::vector<Section> sections(fs.sections_size());
  for (int i = 0; i < fs.sections_size(); ++i) {
    sections[i] = fs.sections(i);
  }
  std::sort(sections.begin(), sections.end(),
      [](const Section& a, const Section& b) {
    auto aitr = kSectionNameToOrdinal.find(a.name());
    auto bitr = kSectionNameToOrdinal.find(b.name());
    if (aitr == kSectionNameToOrdinal.end()) {
      return true;
    } else if (bitr == kSectionNameToOrdinal.end()) {
      return false;
    } else {
      return aitr->second < bitr->second;
    }
  });

  // NOTICE:
  // The process sequence has to be:
  // kINodeReference -> INodeDirectorySection -> INodeSection
  // since we need the first two to build the namespace tree。
  auto inode_dir_section_itr = std::find_if(sections.begin(),
      sections.end(), [](const Section& s) -> bool {
    auto itr = kSectionNameToOrdinal.find(s.name());
    return itr != kSectionNameToOrdinal.end() &&
      itr->second == SectionName::kINodeDir;
  });

  auto inode_section_itr = std::find_if(sections.begin(),
      sections.end(), [](const Section& s) -> bool {
    auto itr = kSectionNameToOrdinal.find(s.name());
    return itr != kSectionNameToOrdinal.end() &&
      itr->second == SectionName::kINode;
  });

  auto ref_section_itr = std::find_if(sections.begin(),
      sections.end(), [](const Section& s) -> bool {
    auto itr = kSectionNameToOrdinal.find(s.name());
    return itr != kSectionNameToOrdinal.end() &&
        itr->second == SectionName::kINodeReference;
  });

  CHECK(inode_dir_section_itr != sections.end())
    << "No INodeDirectorySection in fsimage: " << fsimage_file_;
  CHECK(inode_section_itr != sections.end())
    << "No INodeSection in fsimage: " << fsimage_file_;

  // Make sure that we process kINodeReference first;
  if (ref_section_itr > inode_dir_section_itr ||
      ref_section_itr > inode_section_itr) {
    if (inode_dir_section_itr < inode_section_itr) {
      std::swap(*(inode_dir_section_itr), *(ref_section_itr));
      auto tmp = inode_dir_section_itr;
      inode_dir_section_itr = ref_section_itr;
      ref_section_itr = tmp;
    } else {
      std::swap(*(inode_section_itr), *(ref_section_itr));
      auto tmp = inode_section_itr;
      inode_section_itr = ref_section_itr;
      ref_section_itr = tmp;
    }
  }

  // Now make sure that we process INodeDirectorySection before INodeSection
  if (inode_section_itr < inode_dir_section_itr) {
    std::swap(*(inode_section_itr), *(inode_dir_section_itr));
  }

  for (const auto& section : sections) {
    LOG(INFO) << "section: " << section.name();
  }

  // Process each section one by one
  for (auto& section : sections) {
    auto itr = kSectionNameToOrdinal.find(section.name());
    if (itr == kSectionNameToOrdinal.end()) {
      LOG(WARNING) << "Unrecognized section: " << section.name();
      continue;
    }

    if (!section.has_length() || section.length() == 0) {
      LOG(INFO) << "Empty section: " << section.name() << ", skip";
      continue;
    }

    auto section_data = std::unique_ptr<char[]>(new char[section.length()]);
    cnetpp::base::StringPiece sp;
    if (!raf.Read(section.offset(), section_data.get(), section.length(), &sp)
        || sp.length() != section.length()) {
      LOG(FATAL) << "Failed to read section(" << section.name()
      << ") from fsimage: " << fsimage_file_;
    }

    auto ais = std::unique_ptr<google::protobuf::io::ZeroCopyInputStream>(
        new google::protobuf::io::ArrayInputStream(sp.data(),
          static_cast<int64_t>(sp.size())));
    std::unique_ptr<google::protobuf::io::ZeroCopyInputStream> gis;
    if (!fs.has_codec()) {
      gis.reset(ais.release());
    } else {
      gis = std::unique_ptr<google::protobuf::io::ZeroCopyInputStream>(
          new google::protobuf::io::GzipInputStream(ais.get()));
    }

    switch (itr->second) {
      case SectionName::kNSInfo:
        LOG(INFO) << "Loading kNSInfo";
        LoadNameSystemSection(gis.get());
        LOG(INFO) << "Done loading kNSInfo";
        break;
      case SectionName::kStringTable:
        LOG(INFO) << "Loading kStringTable";
        LoadStringTableSection(gis.get());
        LOG(INFO) << "Done loading kStringTable";
        break;
      case SectionName::kINode:
        LOG(INFO) << "Loading kINode";
        LoadINodeSection(gis.get());
        LOG(INFO) << "Done loading kINode";
        break;
      case SectionName::kINodeReference:
        LOG(INFO) << "Loading kINodeReference";
        LoadINodeReferenceSection(gis.get(), sp.size());
        LOG(INFO) << "Done loading kINodeReference";
        break;
      case SectionName::kINodeDir:
        LOG(INFO) << "Loading kINodeDir";
        LoadINodeDirectorySection(gis.get(), sp.size());
        LOG(INFO) << "Done loading kINodeDir";
        break;
      case SectionName::kFilesUnderconstruction:
        LOG(INFO) << "Loading kFilesUnderconstruction";
        LoadFilesUnderConstructionSection(gis.get(), sp.size());
        LOG(INFO) << "Done loading kFilesUnderconstruction";
        break;
      case SectionName::kSnapshot:
        LOG(INFO) << "Loading kSnapshot";
        LoadSnapshotSection(gis.get(), sp.size());
        LOG(INFO) << "Done loading kSnapshot";
        break;
      case SectionName::kSnapshotDiff:
        LOG(INFO) << "Loading kSnapshotDiff";
        LoadSnapshotDiffSection(gis.get(), sp.size());
        LOG(INFO) << "Done loading kSnapshotDiff";
        break;
      case SectionName::kSecretManager:
        LOG(INFO) << "Loading kSecretManager";
        LoadSecretManagerSection(gis.get());
        LOG(INFO) << "Done loading kSecretManager";
        break;
      case SectionName::kCacheManager:
        LOG(INFO) << "Loading kCacheManager";
        LoadCacheManagerSection(gis.get());
        LOG(INFO) << "Done loading kCacheManager";
        break;
      case SectionName::kAccessCounter:
        LOG(INFO) << "Loading kAccessCounter";
        LoadAccessCounterSection(gis.get());
        LOG(INFO) << "Done loading kAccessCounter";
        break;
      default:
        CHECK(false) << "Never happen!";
    }
  }

  // We must save the last transaction id at the end, because the value
  // in meta storage might be changed while saving inode directory or inode file
  if (ns_) {
    ns_->LocalSaveLastCkptTxId(last_transaction_id_);
  }
  LOG(INFO) << "num inodes " << ns_->num_inodes() << " " << parent_map_.size();
}

void FSImageLoader::ParseDelimitedFrom(
    google::protobuf::io::ZeroCopyInputStream* is,
    google::protobuf::Message* s) {
  google::protobuf::io::CodedInputStream cis(is);

  uint64_t size;
  CHECK(cis.ReadVarint64(&size))
    << "Failed to read varint32 from " << s->GetDescriptor()->name() << ".";

  auto limit = cis.PushLimit(size);
  DEFER(([limit, &cis] () { cis.PopLimit(limit); }));

  CHECK(s->MergePartialFromCodedStream(&cis))
    << "Failed to merge from coded stream for: "
    << s->GetDescriptor()->name() << ". ## " << s->ShortDebugString();
  CHECK(cis.ConsumedEntireMessage())
    << "Failed to consumed entire message for: "
    << s->GetDescriptor()->name() << ". ## " << s->ShortDebugString();
}

void FSImageLoader::LoadNameSystemSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  cloudfs::fsimage::NameSystemSection nssection;

  ParseDelimitedFrom(gis, &nssection);

  if (ns_) {
    ns_->LocalSaveGenerationStampV1(nssection.genstampv1());
    ns_->LocalSaveGenerationStampV2(nssection.genstampv2());
    ns_->LocalSaveGenerationStampV1Limit(nssection.genstampv1limit());
    ns_->LocalSaveLastAllocatedBlockId(nssection.lastallocatedblockid());
  }

  // Save transaction id at the end
  last_transaction_id_ = nssection.transactionid();

  LOG(INFO) << "Loaded generation stamp v1: " << nssection.genstampv1();
  LOG(INFO) << "Loaded generation stamp v2: " << nssection.genstampv2();
  LOG(INFO) << "Loaded generation stamp v1 limit: "
    << nssection.genstampv1limit();
  LOG(INFO) << "Loaded last allocated block id: "
    << nssection.lastallocatedblockid();
  LOG(INFO) << "Loaded transaction id: " << nssection.transactionid();
}

void FSImageLoader::LoadStringTableSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  cloudfs::fsimage::StringTableSection stsection;

  ParseDelimitedFrom(gis, &stsection);

  string_table_.resize(stsection.numentry() + 1);
  for (int i = 0; i < stsection.numentry(); ++i) {
    cloudfs::fsimage::StringTableSection_Entry e;
    ParseDelimitedFrom(gis, &e);
    if (e.has_id() && e.has_str()) {
      string_table_[e.id()] = e.str();
    }
  }
}

void FSImageLoader::LoadINodeSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  using namespace cloudfs::fsimage;  // NOLINT(build/namespaces)
  INodeSection inode_section;
  ParseDelimitedFrom(gis, &inode_section);

  if (ns_) {
    ns_->LocalSaveLastINodeId(inode_section.lastinodeid());
  }
  LOG(INFO) << "Loaded last inode id: " << inode_section.lastinodeid();

  std::unique_ptr<BlockLoader> block_loader;
  std::unique_ptr<NSFlusher> ns_flusher;
  if (ns_) {
    LOG(INFO) << "have no block loader";
    if (!load_inode_cb_) {
      ns_flusher = std::make_unique<NSFlusher>(ns_);
      ns_flusher->Start();
    }
  }

  LOG(INFO) << "Will load " << inode_section.numinodes() << " inodes ...";
  for (int i = 0; i < inode_section.numinodes(); ++i) {
    INodeSection_INode inode;
    ParseDelimitedFrom(gis, &inode);
    if (inode.type() == INodeSection_INode_Type_DIRECTORY) {
      CHECK(inode.has_directory())
        << "Type is directory, but directory field is empty.";
      auto ps = ParsePermission(inode.directory().permission());
      LoadINodeDirectory(static_cast<uint64_t>(inode.id()),
          inode.name(), inode.mutable_directory(), ns_flusher.get());
    } else if (inode.type() == INodeSection_INode_Type_FILE) {
      LoadINodeFile(static_cast<uint64_t>(inode.id()),
                    inode.name(),
                    inode.mutable_file(),
                    block_loader.get(),
                    ns_flusher.get());
    } else if (inode.type() == INodeSection_INode_Type_SYMLINK) {
      LoadINodeSymlink(static_cast<uint64_t>(inode.id()),
                       inode.name(),
                       inode.mutable_symlink(),
                       ns_flusher.get());
    } else {
      LOG(FATAL) << "Unknown inode type: " << inode.type() << ".";
    }
  }
  if (block_loader) {
    block_loader->Finalize();
  }
  if (ns_flusher) {
    ns_flusher->Stop();
  }

  LOG(INFO) << "After load inodes, compact rocksdb start...";
  ns_->SetCompactAllStyle(false);
  sleep(1);
  ns_->ForceCompactAll();
  do {
    sleep(2);
    LOG(INFO) << "compact all is in progress...";
  } while (ns_->IsCompactAllInProgress());
  LOG(INFO) << "compact rocksdb end...";
}

void FSImageLoader::LoadINodeReferenceSection(
    google::protobuf::io::ZeroCopyInputStream* gis, size_t length) {
  LOG(WARNING) << "Unsupported inode reference section, continue...";
  while (gis->ByteCount() < length) {
    cloudfs::fsimage::INodeReferenceSection_INodeReference e;
    ParseDelimitedFrom(gis, &e);
    refs_.emplace_back(e);
  }
}

void FSImageLoader::LoadINodeDirectorySection(
    google::protobuf::io::ZeroCopyInputStream* gis, size_t length) {
  while (gis->ByteCount() < length) {
    cloudfs::fsimage::INodeDirectorySection_DirEntry e;
    ParseDelimitedFrom(gis, &e);
    for (int i = 0; i < e.children_size(); ++i) {
      bool ret = parent_map_.insert(
          std::make_pair(e.children(i), e.parent())).second;
      if (!ret) {
        LOG(FATAL) << "LoadINodeDirectorySection found duplicated pair. child: "
                   << e.children(i) << " parent: " << e.parent();
      }
    }

    for (int i = 0; i < e.refchildren_size(); ++i) {
      if (e.refchildren(i) >= refs_.size()) {
        LOG(FATAL) << "ref out of range. ref length = " << refs_.size()
                   << " try to access " << e.refchildren(i);
      }
      uint64_t ref_id = refs_[e.refchildren(i)].referredid();
      bool ret = parent_map_.insert(std::make_pair(ref_id, e.parent())).second;
      if (!ret) {
        LOG(FATAL) << "Found legacy inode when building parent map";
      }
    }
  }
}

void FSImageLoader::LoadFilesUnderConstructionSection(
    google::protobuf::io::ZeroCopyInputStream* gis, size_t length) {
  using namespace cloudfs::fsimage;  // NOLINT(build/namespaces)
  while (gis->ByteCount() < length) {
    FilesUnderConstructionSection_FileUnderConstructionEntry e;
    ParseDelimitedFrom(gis, &e);

    auto fullpath = e.fullpath();
    std::vector<cnetpp::base::StringPiece> path_segs;
    std::vector<::cnetpp::base::StringPiece> lock_segs;
    CHECK(SplitPath(fullpath, &path_segs))
      << "Failed to split path: " << fullpath;
    CHECK(GetAllAncestorPaths(fullpath, &lock_segs))
        << "Failed to get all ancestor paths: " << fullpath;

    if (ns_) {
      INode inode;
      CHECK_EQ(ns_->GetLastINodeInPath(path_segs, &inode),
        StatusCode::kOK) << "Failed to get inode: " << fullpath;
      CHECK_EQ(inode.type(), INode_Type_kFile)
        << "Incorrect inode type(" << inode.type()
        << ") for path: " << fullpath;
      CHECK(inode.has_uc())
          << "File: " << fullpath << " is not under construction.";
      ns_->LoadLease(inode);
    }
  }
}

void FSImageLoader::LoadSnapshotSection(
    google::protobuf::io::ZeroCopyInputStream* gis, size_t length) {
  LOG(WARNING) << "Unsupported snapshot section, continue...";
//  It seems we don't need this part yet. But keep the code just in case...
//  cloudfs::fsimage::SnapshotSection s;
//  ParseDelimitedFrom(gis, &s);
//  LOG(INFO) << "Snapshot section: " << s.ShortDebugString();
//  for (size_t i = 0; i < s.numsnapshots(); ++i) {
//    cloudfs::fsimage::SnapshotSection_Snapshot e;
//    ParseDelimitedFrom(gis, &e);
//    LOG(INFO) << "Snapshot: " << e.ShortDebugString();
//  }
}

void FSImageLoader::LoadSnapshotDiffSection(
    google::protobuf::io::ZeroCopyInputStream* gis, size_t length) {
  LOG(WARNING) << "Unsupported snapshot diff section, continue...";
//  It seems we don't need this part yet. But keep the code just in case...
//  using namespace cloudfs::fsimage;  // NOLINT(build/namespaces)
//  while (gis->ByteCount() < length) {
//    SnapshotDiffSection_DiffEntry d;
//    ParseDelimitedFrom(gis, &d);
//    LOG(INFO) << "Diff: " << d.ShortDebugString();
//    if (d.type() == SnapshotDiffSection_DiffEntry_Type_DIRECTORYDIFF) {
//      for (uint32_t i = 0; i < d.numofdiff(); ++i) {
//        SnapshotDiffSection_DirectoryDiff dir_diff;
//        ParseDelimitedFrom(gis, &dir_diff);
//        LOG(INFO) << "Dir diff: " << dir_diff.ShortDebugString();
//      }
//    } else if (d.type() == SnapshotDiffSection_DiffEntry_Type_FILEDIFF) {
//      for (uint32_t i = 0; i < d.numofdiff(); ++i) {
//        SnapshotDiffSection_FileDiff file_diff;
//        ParseDelimitedFrom(gis, &file_diff);
//        LOG(INFO) << "file diff: " << file_diff.ShortDebugString();
//      }
//    } else {
//      LOG(WARNING) << "Unknown type " << d.type();
//    }
//  }
}

void FSImageLoader::LoadSecretManagerSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  LOG(WARNING) << "Unsupported secret manager section, continue...";
  (void)gis;
}

void FSImageLoader::LoadCacheManagerSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  LOG(WARNING) << "Unsupported cache manager section, continue...";
  (void)gis;
}

void FSImageLoader::LoadAccessCounterSection(
    google::protobuf::io::ZeroCopyInputStream* gis) {
  // TODO(yangjinfeng.02)
  // Load access counter section
  (void)gis;
}

void FSImageLoader::LoadINodeFile(
    uint64_t id,
    const std::string& name,
    cloudfs::fsimage::INodeSection_INodeFile* file,
    BlockLoader* loader,
    NSFlusher* flusher) {
  auto pitr = parent_map_.find(id);
  CHECK(pitr != parent_map_.end())
    << "Could not find parent id when loading file: " << name << ". "
    << file->ShortDebugString();

  std::shared_ptr<INode> inode = std::make_shared<INode>();
  auto ps = ParsePermission(file->permission());
  MakeINodeFile(id, pitr->second, name, ps, file->replication(),
      file->preferredblocksize(), file->storagepolicyid(), inode.get());
  inode->set_mtime(file->modificationtime());
  inode->set_atime(file->accesstime());
  inode->set_status(INode_Status_kFileComplete);
  if (file->has_accesspattern()) {
    inode->set_access_pattern(file->accesspattern());
  }
  for (int i = 0; i < file->blocks_size(); ++i) {
    inode->add_blocks()->CopyFrom(file->blocks(i));
  }

  if (file->has_acl()) {
    ParseAcls(file->acl(), inode.get());
  }
  if (file->has_xattrs()) {
    ParseXAttrs(file->xattrs(), inode.get());
  }
  if (file->has_fileuc()) {
    inode->set_status(INode_Status_kFileUnderConstruction);
    auto uc = inode->mutable_uc();
    if (file->fileuc().has_clientname()) {
      uc->set_client_name(file->fileuc().clientname());
    }
    if (file->fileuc().has_clientmachine()) {
      uc->set_client_machine(file->fileuc().clientmachine());
    }
  }

  if (flusher) {
    flusher->Push(inode);
  } else if (ns_) {
    ns_->InsertINode(inode);
  }
  if (load_inode_cb_) {
    load_inode_cb_(*inode);
  }
  if (loader) {
    loader->QueueBlock(inode, "");
  }
}

void FSImageLoader::LoadINodeDirectory(
    uint64_t id,
    const std::string& name,
    cloudfs::fsimage::INodeSection_INodeDirectory* dir,
    NSFlusher* flusher) {
  uint64_t pid = kRootINodeId;
  auto pitr = parent_map_.find(id);
  if (pitr != parent_map_.end()) {
    pid = pitr->second;
  }

  std::shared_ptr<INode> inode = std::make_shared<INode>();
  auto ps = ParsePermission(dir->permission());
  MakeINode(id, pid, name, ps, INode::kDirectory, inode.get());
  inode->set_mtime(dir->modificationtime());
  if (dir->has_acl()) {
    ParseAcls(dir->acl(), inode.get());
  }
  if (dir->has_xattrs()) {
    ParseXAttrs(dir->xattrs(), inode.get());
  }

  if (NameSpace::IsAccMode()) {
    auto info = inode->mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(0);
    info->set_children_sync_ts(0);
  }

  if (flusher) {
    flusher->Push(inode);
  } else if (ns_) {
    ns_->InsertINode(inode);
  }
}

void FSImageLoader::LoadINodeSymlink(
    uint64_t id,
    const std::string& name,
    cloudfs::fsimage::INodeSection_INodeSymlink* symlink,
    NSFlusher* flusher) {
  auto pitr = parent_map_.find(id);
  CHECK(pitr != parent_map_.end())
    << "Could not find parent id when loading symlink: " << name << ". "
    << symlink->ShortDebugString();

  std::shared_ptr<INode> inode = std::make_shared<INode>();
  auto ps = ParsePermission(symlink->permission());
  MakeINodeSymLink(id, pitr->second, name, ps, symlink->target(), inode.get());
  inode->set_mtime(symlink->modificationtime());
  inode->set_atime(symlink->accesstime());

  if (flusher) {
    flusher->Push(inode);
  } else if (ns_) {
    ns_->InsertINode(inode);
  }
}

PermissionStatus FSImageLoader::ParsePermission(uint64_t value) {
  PermissionStatus ps;
  uint16_t perm = static_cast<uint16_t>(value & ((1 << kGroupStrIdOffset) - 1));
  int gsid =
    static_cast<int>((value >> kGroupStrIdOffset) & kUserGroupStrIdMask);
  int usid =
    static_cast<int>((value >> kUserStrIdOffset) & kUserGroupStrIdMask);
  ps.set_username(string_table_[usid]);
  ps.set_groupname(string_table_[gsid]);
  ps.set_permission(static_cast<uint32_t>(perm));
  return ps;
}

void FSImageLoader::ParseAcls(
    const cloudfs::fsimage::INodeSection_AclFeatureProto& acls,
    INode* inode) {
  CHECK_NOTNULL(inode);

  for (int i = 0; i < acls.entries_size(); ++i) {
    int p = acls.entries(i) & kAclEntryPermMask;
    int t = (acls.entries(i) >> kAclEntryTypeOffset) & kAclEntryTypeMask;
    int s = (acls.entries(i) >> kAclEntryScopeOffset) & kAclEntryScopeMask;
    int nid = (acls.entries(i) >> kAclEntryNameOffset) & kAclEntryNameMask;
    auto a = inode->add_acls();
    a->set_name(string_table_[nid]);
    a->set_permissions(
        static_cast<cloudfs::AclEntryProto_FsActionProto>(p));
    a->set_type(static_cast<cloudfs::AclEntryProto_AclEntryTypeProto>(t));
    a->set_scope(
        static_cast<cloudfs::AclEntryProto_AclEntryScopeProto>(s));
  }
}

void FSImageLoader::ParseXAttrs(
    const cloudfs::fsimage::INodeSection_XAttrFeatureProto& xattrs,
    INode* inode) {
  CHECK_NOTNULL(inode);

  for (int i = 0; i < xattrs.xattrs_size(); ++i) {
    auto x = inode->add_xattrs();
    int v = xattrs.xattrs(i).name();
    int nid = (v >> kXAttrNameOffset) & kXAttrNameMask;
    int ns = (v >> kXAttrNamespaceOffset) & kXAttrNamespaceMask;
    ns |= ((v >> kXAttrNamespaceExtOffset) & kXAttrNamespaceExtMask) << 2;
    if (xattrs.xattrs(i).has_value()) {
      x->set_value(xattrs.xattrs(i).value());
    }
    x->set_namespace_(
        static_cast<cloudfs::XAttrProto_XAttrNamespaceProto>(ns));
    x->set_name(string_table_[nid]);
  }
}

void FSImageLoader::set_load_inode_cb(const LoadINodeCallback& cb) {
  load_inode_cb_ = cb;
}

void FSImageLoader::NSFlusher::Start() {
  workers_ = std::make_unique<cnetpp::concurrency::ThreadPool>("NSFlusher");
  workers_->set_num_threads(32);
  workers_->Start();
}

void FSImageLoader::NSFlusher::Stop() {
  if (workers_) {
    if (pending_inodes_ && pending_inodes_->size() > 0) {
      Flush();
    }
    while (workers_->PendingCount() > 0) {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      LOG(INFO) << "Waiting for NSFlusher to finished, pending task count: "
                << workers_->PendingCount();
    }
    workers_->Stop(true);
    workers_.reset();
  }
}

void FSImageLoader::NSFlusher::Push(std::shared_ptr<INode> inode) {
  if (!pending_inodes_) {
    pending_inodes_ = std::make_shared<std::deque<std::shared_ptr<INode>>>();
  }
  pending_inodes_->emplace_back(std::move(inode));
  if (pending_inodes_->size() >= 10000) {
    Flush();
  }
}

void FSImageLoader::NSFlusher::Flush() {
  auto pending_inodes = pending_inodes_;
  pending_inodes_.reset();
  workers_->AddTask([this, pending_inodes] () {
    this->ns_->InsertINodes(*pending_inodes);
    return true;
  });
}

}  // namespace dancenn
