// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef FSIMAGE_FSIMAGE_LOADER_H_
#define FSIMAGE_FSIMAGE_LOADER_H_

#include <cnetpp/concurrency/task.h>
#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/message.h>

#include <condition_variable>
#include <deque>
#include <string>
#include <vector>
#include <regex>
#include <map>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <memory>

#include "base/md5_calculator.h"
#include "namespace/block_loader.h"
#include "inode.pb.h"  // NOLINT(build/include)
#include "fsimage.pb.h"  // NOLINT(build/include)

namespace dancenn {

class NameSpace;

class FSImageLoader {
 public:
  // FIXME(livexmm) no depends on NameSpace, use MetaStorage directly
  explicit FSImageLoader(NameSpace* ns);
  ~FSImageLoader() {}

  FSImageLoader(const FSImageLoader&) = delete;
  FSImageLoader& operator=(const FSImageLoader&) = delete;

  // for image_diff tool
  using LoadINodeCallback = std::function<void(const INode&)>;
  void set_load_inode_cb(const LoadINodeCallback& cb);

  bool Load();

  // for unit testing
  void RefreshNameSpaceInfo();

  uint64_t last_transaction_id() {
    return last_transaction_id_;
  }

 private:
  static const char* kVersionFileName;

  static const char* kLayoutVersionKey;
  static const char* kNamespaceIdKey;
  static const char* kClusterIdKey;
  static const char* kStorageTypeKey;
  static const char* kBlockPoolIdKey;
  static const char* kCTimeKey;

  static const std::regex kMD5Regex;

  static const char* kMD5FileSuffix;
  static const int64_t kSupportedLayoutVersion = -60;
  static const int64_t kFileVersion = 1;
  static const int64_t kMinimumFileLength = 8;
  static const int64_t kSummaryLengthFieldSize = 4;
  static const char* kSupportedStorageType;
  static const char* kMagicHeader;
  static const char* kDefaultCodec;
  static const char* kGzipCodec;

  static const uint64_t kUserGroupStrIdMask = (1 << 24) - 1;
  static const int kUserStrIdOffset = 40;
  static const int kGroupStrIdOffset = 16;

  static const int kAclEntryNameMask = (1 << 24) - 1;
  static const int kAclEntryNameOffset = 6;
  static const int kAclEntryTypeOffset = 3;
  static const int kAclEntryScopeOffset = 5;
  static const int kAclEntryPermMask = 7;
  static const int kAclEntryTypeMask = 3;
  static const int kAclEntryScopeMask = 1;

  static const int kXAttrNamespaceMask = 3;
  static const int kXAttrNamespaceOffset = 30;
  static const int kXAttrNameMask = (1 << 24) - 1;
  static const int kXAttrNameOffset = 6;
  // See the comments in fsimage.proto for an explanation of the following.
  static const int kXAttrNamespaceExtOffset = 5;
  static const int kXAttrNamespaceExtMask = 1;

  enum class SectionName : uint8_t {
    kNSInfo = 0,
    kStringTable,
    kExtendedAcl,
    kINode,
    kINodeReference,
    kSnapshot,
    kINodeDir,
    kFilesUnderconstruction,
    kSnapshotDiff,
    kSecretManager,
    kCacheManager,
    kAccessCounter,
  };

  static const std::map<std::string, SectionName> kSectionNameToOrdinal;

  class NSFlusher {
   public:
    explicit NSFlusher(NameSpace* ns) : ns_(ns) {}
    virtual ~NSFlusher() { Stop(); }

    void Start();
    void Stop();

    void Push(std::shared_ptr<INode> inode);

   private:
    NameSpace* ns_{nullptr};
    // batch buffer
    std::shared_ptr<std::deque<std::shared_ptr<INode>>> pending_inodes_;

    std::unique_ptr<cnetpp::concurrency::ThreadPool> workers_;

    void Flush();
  };

  void LoadVersionFile();
  void LoadStoredMD5FromFile();
  void VerifySavedMD5();

  void DoLoad();

  // Load each type of section into meta storage
  // Some types of section are not supported yet
  void LoadNameSystemSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadStringTableSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadINodeSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadINodeReferenceSection(
      google::protobuf::io::ZeroCopyInputStream* gis, size_t length);
  void LoadINodeDirectorySection(
      google::protobuf::io::ZeroCopyInputStream* gis, size_t length);
  void LoadFilesUnderConstructionSection(
      google::protobuf::io::ZeroCopyInputStream* gis, size_t length);
  void LoadSnapshotSection(google::protobuf::io::ZeroCopyInputStream* gis,
      size_t length);
  void LoadSnapshotDiffSection(google::protobuf::io::ZeroCopyInputStream* gis,
      size_t length);
  void LoadSecretManagerSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadCacheManagerSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadAccessCounterSection(google::protobuf::io::ZeroCopyInputStream* gis);
  void LoadINodeFile(uint64_t id, const std::string& name,
      cloudfs::fsimage::INodeSection_INodeFile* file, BlockLoader* loader,
      NSFlusher* flusher);
  void LoadINodeDirectory(uint64_t id, const std::string& name,
      cloudfs::fsimage::INodeSection_INodeDirectory* dir,
      NSFlusher* flusher);
  void LoadINodeSymlink(uint64_t id, const std::string& name,
      cloudfs::fsimage::INodeSection_INodeSymlink* symlink,
      NSFlusher* flusher);
  PermissionStatus ParsePermission(uint64_t value);
  void ParseAcls(
      const cloudfs::fsimage::INodeSection_AclFeatureProto& acls,
      INode* inode);
  void ParseXAttrs(
      const cloudfs::fsimage::INodeSection_XAttrFeatureProto& xattrs,
      INode* inode);

  void ParseDelimitedFrom(google::protobuf::io::ZeroCopyInputStream* is,
      google::protobuf::Message* s);

  LoadINodeCallback load_inode_cb_;

  const std::string fsimage_file_;

  NameSpace* ns_{nullptr};

  uint64_t last_transaction_id_;

  uint32_t layout_version_;
  uint32_t namespace_id_;
  int64_t ctime_;
  std::string cluster_id_;
  std::string blockpool_id_;
  MD5 md5_;

  std::vector<std::string> string_table_;
  std::vector<cloudfs::fsimage::INodeReferenceSection_INodeReference> refs_;  // NOLINT(whitespace/line_length)

  // inode id to parent inode id
  std::unordered_map<uint64_t, uint64_t> parent_map_;
};

}  // namespace dancenn

#endif  // FSIMAGE_FSIMAGE_LOADER_H_

