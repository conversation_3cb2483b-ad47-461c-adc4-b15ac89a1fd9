// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "proxy/proxy_throttler.h"

#include <gflags/gflags.h>

#include <utility>
#include <vector>
#include <chrono>

#include "base/redis_manager.h"
#include "base/path_util.h"

DECLARE_string(danceproxy_throttle_redis_backends);
DECLARE_int32(danceproxy_throttle_redis_timeout_ms);
DECLARE_int32(danceproxy_throttle_redis_refresh_interval_ms);
DECLARE_string(danceproxy_throttle_redis_key);
DECLARE_string(danceproxy_throttle_redis_switch_key);
DECLARE_bool(danceproxy_throttle_enabled);

namespace dancenn {

std::string ProxyThrottler::Entry::ToString() const {
  return "path: " + path_ + " rpc: " + rpc_ +
         ", permits: " + std::to_string(permits_);
}

bool ProxyThrottler::Entry::DeserializeFromJson(const cnetpp::base::Object& json) {
  auto itr = json.Find("path");
  if (itr == json.End() || !itr->second.IsString()) {
    return false;
  }
  path_ = itr->second.AsString();
  itr = json.Find("rpc");
  if (itr == json.End() || !itr->second.IsString()) {
    return false;
  }
  rpc_ = itr->second.AsString();
  itr = json.Find("permits");
  if (itr == json.End() || !itr->second.IsNumeric()) {
    return false;
  }
  permits_ = itr->second.AsDouble();
  return true;
}

void ProxyThrottler::Init() {
  DoInit(FLAGS_danceproxy_throttle_enabled,
         std::chrono::milliseconds(
             FLAGS_danceproxy_throttle_redis_refresh_interval_ms));
}

bool ProxyThrottler::Refresh() {
  LOG(INFO) << "Start to refresh throttle from Redis.";
  RedisManager redis;
  if (!redis.Init(FLAGS_danceproxy_throttle_redis_backends,
                  FLAGS_danceproxy_throttle_redis_timeout_ms)) {
    LOG(ERROR) << "Failed to initialize the throttle redis backends.";
    return false;
  }

  // get switch key
  std::string swch;
  if (!redis.RedisGet(FLAGS_danceproxy_throttle_redis_switch_key, &swch)) {
    LOG(ERROR) << "Failed to get throttle switch from redis.";
    return false;
  }

  std::string result;
  if (!redis.RedisGet(FLAGS_danceproxy_throttle_redis_key, &result)) {
    LOG(ERROR) << "Failed to get throttle from redis.";
    return false;
  }
  cnetpp::base::Value json_result;
  if (!cnetpp::base::Parser::Deserialize(result, &json_result)) {
    LOG(ERROR) << "Failed to deserialize throttle string.";
    return false;
  }
  if (!json_result.IsArray()) {
    LOG(ERROR) << "throttle string is not a json array.";
    return false;
  }
  auto json_array = json_result.AsArray();
  std::unordered_map<std::string, RpcToTokenBucketMapType> tmp_token_buckets;
  for (auto itr = json_array.Begin(); itr != json_array.End(); itr++) {
    if (!itr->IsObject()) {
      LOG(ERROR) << "throttle field is not a json object.";
      return false;
    }
    Entry e;
    if (!e.DeserializeFromJson(itr->AsObject())) {
      LOG(ERROR) << "Invalid throttle entry.";
      return false;
    }
    auto itr1 = tmp_token_buckets.find(e.path_);
    if (itr1 == tmp_token_buckets.end()) {
      itr1 =
          tmp_token_buckets.emplace(e.path_, RpcToTokenBucketMapType()).first;
    }
    itr1->second.emplace(e.rpc_,
                         std::move(TokenBucket(e.permits_, e.permits_)));
  }
  LOG(INFO) << "Load " << tmp_token_buckets.size() << " throttle entries.";

  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    this->token_buckets_.swap(tmp_token_buckets);
  }
  return true;
}

Status ProxyThrottler::CheckThrottle(const std::string& path,
                                const std::string& rpc) {
  if (!enabled_ || swch_ == Switch::kOff) {
    return Status();
  }

  std::string normalized_path = path;
  RemoveRedundantSlash(&normalized_path);
  std::vector<cnetpp::base::StringPiece> ancestors;
  if (!GetAllAncestorPaths(normalized_path, &ancestors)) {
    return Status(JavaExceptions::kInvalidPathException,
                  "Invalid path: " + path);
  }
  CHECK_GT(normalized_path.size(), 0);

  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  for (size_t i = ancestors.size() - 1; i != 0; i--) {
    auto ancestor = ancestors[i].as_string();
    auto itr = token_buckets_.find(ancestor);
    if (itr != token_buckets_.end()) {
      auto itr1 = itr->second.find(rpc);
      if (itr1 == itr->second.end()) {
        // rpc does not match
        return Status();
      }
      if (!itr1->second.Consume(1.)) {
        GetMetrics(std::string("ThrottledCount#path=") + ancestor +
                   "#rpc=" + rpc + "#switch=" + ReadableSwitchType())->Inc();
        std::string err_msg = "Path: ";
        err_msg += path + " is throttled with ";
        err_msg += std::to_string(itr1->second.token_generate_rate());
        err_msg += " permits per second. Please control access QPS";
        if (swch_ == Switch::kOn) {
          return Status(JavaExceptions::kStandbyException, std::move(err_msg));
        } else if (swch_ == Switch::kDebug) {
          LOG(INFO) << err_msg;
          return Status();
        }
      } else {
        GetMetrics(std::string("ThrottlePassCount#path=") + ancestor +
                   "#rpc=" + rpc + "#switch=" + ReadableSwitchType())->Inc();
        return Status();
      }
    }
  }
  return Status();
}

cnetpp::base::Value ProxyThrottler::DumpToJson() const {
  cnetpp::base::Object res;
  cnetpp::base::Object token_buckets;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  for (auto& tb : token_buckets_) {
    cnetpp::base::Object rpcs;
    for (auto& rpc : tb.second) {
      cnetpp::base::Object rpc_tb;
      rpc_tb["burst"] = rpc.second.burst();
      rpc_tb["rate"] = rpc.second.token_generate_rate();
      rpc_tb["available"] = rpc.second.Available();
      rpcs[rpc.first] = rpc_tb;
    }
    token_buckets[tb.first] = rpcs;
  }
  res["token_buckets"] = token_buckets;
  return cnetpp::base::Value(std::move(res));
}

}  // namespace dancenn

