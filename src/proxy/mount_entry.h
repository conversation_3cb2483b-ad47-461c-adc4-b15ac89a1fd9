// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_MOUNT_ENTRY_H_
#define PROXY_MOUNT_ENTRY_H_

#include <cnetpp/base/csonpp.h>

#include <memory>
#include <regex>
#include <string>
#include <vector>
#include <utility>

namespace dancenn {

class MountEntry {
 public:
  static std::shared_ptr<MountEntry> DeserializeFromJson(
      const cnetpp::base::Object& object);

  void set_fs_uri(const std::string& fs_uri) {
    fs_uri_ = fs_uri;
  }
  const std::string& fs_uri() const {
    return fs_uri_;
  }

  void set_mount_point(const std::string& mount_point) {
    mount_point_ = mount_point;
  }
  const std::string& mount_point() const {
    return mount_point_;
  }

  void set_attributes(const std::vector<std::string>& attributes) {
    attributes_ = attributes;
  }

  void set_start(std::unique_ptr<std::string>&& start) {
    start_ = std::move(start);
  }

  void set_end(std::unique_ptr<std::string>&& end) {
    end_ = std::move(end);
  }

  void set_includes(const std::vector<std::string>& includes) {
    for (auto &include : includes) {
      include_regexes_.emplace_back(include);
    }
    includes_ = includes;
  }

  void set_excludes(const std::vector<std::string>& excludes) {
    for (auto &exclude : excludes) {
      exclude_regexes_.emplace_back(exclude);
    }
    excludes_ = excludes;
  }

  bool HasFeature() const {
    return !include_regexes_.empty() || !exclude_regexes_.empty() ||
           start_.get() || end_.get();
  }

  bool HasIncludeFeature() const {
    return !include_regexes_.empty();
  }

  bool HasRangeFeature() const {
    return start_.get() || end_.get();
  }

  bool IsPathExcluded(const std::string& path) const;
  bool IsPathIncluded(const std::string& path) const;
  bool IsPathInRange(const std::string& path) const;

  cnetpp::base::Value SerializeToJson() const {
    cnetpp::base::Object res;
    res["fs_uri"] = fs_uri_;
    res["mount_point"] = mount_point_;
    cnetpp::base::Array attributes;
    for (auto& attr : attributes_) {
      attributes.Append(cnetpp::base::Value(attr));
    }
    res["attributes"] = attributes;
    if (start_) {
      res["start"] = *start_;
    } else {
      res["start"] = cnetpp::base::Value(nullptr);
    }
    if (end_) {
      res["end"] = *end_;
    } else {
      res["end"] = cnetpp::base::Value(nullptr);
    }
    cnetpp::base::Array includes;
    for (auto& include : includes_) {
      includes.Append(cnetpp::base::Value(include));
    }
    res["includes"] = includes;
    cnetpp::base::Array excludes;
    for (auto& exclude : excludes_) {
      excludes.Append(cnetpp::base::Value(exclude));
    }
    res["excludes"] = excludes;
    return cnetpp::base::Value(res);
  }

 private:
  std::string fs_uri_;
  std::string mount_point_;
  std::vector<std::string> attributes_;  // for compatibility
  std::unique_ptr<std::string> start_;  // nullptr means not set
  std::unique_ptr<std::string> end_;  // nullptr means not set
  std::vector<std::regex> include_regexes_;
  std::vector<std::string> includes_;
  std::vector<std::regex> exclude_regexes_;
  std::vector<std::string> excludes_;
};

}  // namespace dancenn

#endif  // PROXY_MOUNT_ENTRY_H_

