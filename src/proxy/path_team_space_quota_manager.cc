// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/path_team_space_quota_manager.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <hiredis/hiredis.h>

#include <algorithm>
#include <memory>
#include <random>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/read_write_lock.h"
#include "base/redis_manager.h"
#include "base/path_util.h"
#include "proxy/quota_or_usage.h"
#include "proxy/upstream_manager.h"
#include "base/java_exceptions.h"

DECLARE_string(danceproxy_quota_redis_backends);
DECLARE_int32(danceproxy_quota_redis_timeout_ms);
DECLARE_int32(danceproxy_quota_redis_refresh_interval_ms);
DECLARE_string(danceproxy_quota_redis_path2team_key);
DECLARE_string(danceproxy_quota_redis_team2quota_key);
DECLARE_string(danceproxy_quota_redis_team2usage_key);
DECLARE_string(danceproxy_quota_redis_path2usage_key);
DECLARE_string(danceproxy_quota_redis_ssdpaths_key);
DECLARE_string(danceproxy_quota_redis_blacklist_key);
DECLARE_string(danceproxy_quota_redis_whitelist_key);
DECLARE_string(danceproxy_quota_redis_switch_key);
DECLARE_bool(danceproxy_quota_enabled);
DECLARE_int32(danceproxy_quota_on_start_hour_utc);
DECLARE_int32(danceproxy_quota_on_end_hour_utc);
DECLARE_string(danceproxy_quota_pattern_team_mapping_file);

namespace dancenn {

const char* PathTeamSpaceQuotaManager::kDefault = ".default";
static const char* kRedisDoneFlag = ".REDIS_DONE_FLAG";

void PathTeamSpaceQuotaManager::Init() {
  DoInit(FLAGS_danceproxy_quota_enabled,
         std::chrono::milliseconds(
             FLAGS_danceproxy_quota_redis_refresh_interval_ms));
}

bool PathTeamSpaceQuotaManager::Refresh() {
  bool ret = true;
  if (!RefreshMappingFile()) {
    ret = false;
  }
  if (!RefreshRedis()) {
    ret = false;
  }
  return ret;
}

bool PathTeamSpaceQuotaManager::RefreshMappingFile() {
  if (!FileUtils::IsFile(FLAGS_danceproxy_quota_pattern_team_mapping_file)) {
    LOG(ERROR) << "Mapping file: "
        << FLAGS_danceproxy_quota_pattern_team_mapping_file
        << " doesn't exist or is invalid.";
    return false;
  }
  RandomAccessFile raf(FLAGS_danceproxy_quota_pattern_team_mapping_file);
  auto size = raf.Size();
  if (size <= 0) {
    return false;
  }
  std::string buf(size, '\0');
  cnetpp::base::StringPiece result;
  if (!raf.Read(0, &(buf[0]), size, &result)) {
    LOG(ERROR) << "Failed to read enough data from mapping file: "
        << FLAGS_danceproxy_quota_pattern_team_mapping_file << ".";
    return false;
  }
  cnetpp::base::Value mapping_config;
  if (!cnetpp::base::Parser::Deserialize(result.as_string(), &mapping_config)) {
    LOG(ERROR) << "Failed to parse json file: "
        << FLAGS_danceproxy_quota_pattern_team_mapping_file;
    return false;
  }
  if (!mapping_config.IsArray()) {
    LOG(ERROR) << "json file is not expected format: "
        << FLAGS_danceproxy_quota_pattern_team_mapping_file;
    return false;
  }
  auto entries = mapping_config.AsArray();
  std::vector<std::pair<std::regex, std::string>> pattern_team_mappings;
  for (auto itr = entries.Begin(); itr != entries.End(); ++itr) {
    if (itr->IsObject()) {
      auto entry = itr->AsObject();
      auto team_param = entry.Find("team");
      auto pattern_str_param = entry.Find("patternString");
      if (team_param == entry.End() || pattern_str_param == entry.End()
          || !team_param->second.IsString()
          || !pattern_str_param->second.IsString()) {
        LOG(ERROR) << "Invalid parameter";
        return false;
      }
      auto team = team_param->second.AsString();
      auto pattern_str = pattern_str_param->second.AsString();
      LOG(INFO) << "Load mapping: " << team << " -> " << pattern_str;
      pattern_team_mappings.emplace_back(std::regex(pattern_str,
          std::regex::optimize), team);
    }
  }

  std::unique_lock<ReadWriteLock> guard(rw_lock_);
  pattern_team_mappings_.swap(pattern_team_mappings);
  return true;
}

bool PathTeamSpaceQuotaManager::RefreshRedis() {
  LOG(INFO) << "Start to refresh path team quota data from redis.";
  auto namenodes = upstream_manager_->namenodes();
  std::vector<std::string> nsids;
  for (auto& nn : namenodes) {
    if (cnetpp::base::StringPiece(nn.first).starts_with("hdfs://")) {
      nsids.emplace_back(nn.first.substr(7) + "#");
    }
  }
  RedisManager redis;
  if (!redis.Init(FLAGS_danceproxy_quota_redis_backends,
                  FLAGS_danceproxy_quota_redis_timeout_ms)) {
    LOG(ERROR) << "Failed to initialize the quota redis backends.";
    return false;
  }

  std::unordered_map<std::string, std::string> raw_team_to_quota;
  if (!redis.RedisHGetAll(FLAGS_danceproxy_quota_redis_team2quota_key,
                          &raw_team_to_quota)) {
    LOG(ERROR) << "Failed to get team2quota data from redis.";
    return false;
  }
  // get team2quota
  std::unordered_map<std::string, QuotaOrUsage> team_to_quota;
  bool has_default = false;
  for (auto& ttq : raw_team_to_quota) {
    QuotaOrUsage qu;
    if (!qu.DeserializeFromJson(ttq.second)) {
      LOG(ERROR) << "Invalid team2quota data, must be json type.";
      return false;
    }
    if (!has_default &&
        cnetpp::base::StringPiece(ttq.first).ends_with(kDefault)) {
      has_default = true;
    }
    team_to_quota.emplace(ttq.first, qu);
  }
  if (!has_default) {
    LOG(ERROR) << "No .default team2quota field";
    return false;
  }

  // get path2team
  std::unordered_map<std::string, std::string> path_to_team;
  while (true) {
    std::unordered_map<std::string, std::string> r;
    if (!redis.RedisHGetAll(FLAGS_danceproxy_quota_redis_path2team_key, &r)) {
      LOG(ERROR) << "Failed to get path2team data from redis.";
      return false;
    }
    if (r.find(kRedisDoneFlag) == r.end()) {
      LOG(INFO) << "Got incomplete redis content, sleep 100ms and retry.";
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
    } else {
      for (auto& rr : r) {
        if (rr.first == kRedisDoneFlag) {
          continue;
        }
        std::string t = rr.first;
        RemoveRedundantSlash(&t);
        path_to_team.emplace(t, rr.second);
      }
      break;
    }
  }

  // get blacklist
  std::unordered_set<std::string> blacklist_teams;
  if (!redis.RedisSMembers(FLAGS_danceproxy_quota_redis_blacklist_key,
                           &blacklist_teams)) {
    LOG(ERROR) << "Failed to get blacklist from redis.";
    return false;
  }

  // get whitelist
  std::unordered_set<std::string> whitelist_teams;
  if (!redis.RedisSMembers(FLAGS_danceproxy_quota_redis_whitelist_key,
                           &whitelist_teams)) {
    LOG(ERROR) << "Failed to get whitelist from redis.";
    return false;
  }

  // get switch key
  std::string swch;
  if (!redis.RedisGet(FLAGS_danceproxy_quota_redis_switch_key, &swch)) {
    LOG(ERROR) << "Failed to get quota switch from redis.";
    return false;
  }

  std::unordered_map<std::string, QuotaOrUsage> team_to_usage;
  std::unordered_set<std::string> ssd_paths;
  for (auto& nsid : nsids) {
    // get team2usage
    has_default = false;
    std::unordered_map<std::string, std::string> u;
    if (!redis.RedisHGetAll(nsid + FLAGS_danceproxy_quota_redis_team2usage_key,
                            &u)) {
      LOG(ERROR) << "Failed to get team2usage data from redis.";
      return false;
    }
    for (auto& uu : u) {
      std::string key = uu.first;
      if (!has_default &&
          cnetpp::base::StringPiece(uu.first).ends_with(kDefault) &&
          cnetpp::base::StringPiece(uu.first).starts_with(nsid)) {
        has_default = true;
        key = uu.first.substr(nsid.size());
      }
      QuotaOrUsage qu;
      if (!qu.DeserializeFromJson(uu.second)) {
        LOG(ERROR) << "Invalid team2usage data, must be json type.";
        return false;
      }
      auto itr = team_to_usage.find(key);
      if (itr == team_to_usage.end()) {
        team_to_usage.emplace(key, qu);
      } else {
        itr->second.Merge(qu);
      }
    }
    if (!has_default) {
      LOG(ERROR) << "No .default team2usage fieled.";
      return false;
    }
    std::unordered_set<std::string> ssds;
    // get ssd paths
    if (!redis.RedisSMembers(nsid + FLAGS_danceproxy_quota_redis_ssdpaths_key,
                             &ssds)) {
      LOG(ERROR) << "Failed to get ssd paths data from redis.";
      return false;
    }
    ssd_paths.insert(ssds.begin(), ssds.end());
  }
  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    this->path_to_team_.swap(path_to_team);
    this->team_to_quota_.swap(team_to_quota);
    this->team_to_usage_.swap(team_to_usage);
    this->ssd_paths_.swap(ssd_paths);
    this->blacklist_teams_.swap(blacklist_teams);
    this->whitelist_teams_.swap(whitelist_teams);
    if (cnetpp::base::StringPiece(swch).ignore_case_equal("on")) {
      this->swch_ = Switch::kOn;
    } else if (cnetpp::base::StringPiece(swch).ignore_case_equal("off")) {
      this->swch_ = Switch::kOff;
    } else {
      this->swch_ = Switch::kDebug;
    }
  }
  return true;
}

std::tuple<bool, const char*, std::string>
PathTeamSpaceQuotaManager::CheckSpace(const std::string& path) {
  auto res = std::make_tuple<bool, const char*, std::string>(
      true, nullptr, "");
  if (!FLAGS_danceproxy_quota_enabled || swch_ == Switch::kOff) {
    return res;
  }

  auto now = static_cast<time_t>(
      std::chrono::duration_cast<std::chrono::seconds>(
          std::chrono::system_clock::now().time_since_epoch()).count());
  struct tm tm_now;
  auto hour = gmtime_r(&now, &tm_now)->tm_hour;
  if (FLAGS_danceproxy_quota_on_start_hour_utc < 0 ||
      FLAGS_danceproxy_quota_on_end_hour_utc < 0 ||
      hour < FLAGS_danceproxy_quota_on_start_hour_utc ||
      hour >= FLAGS_danceproxy_quota_on_end_hour_utc) {
    return res;
  }

  std::string normalized_path = path;
  RemoveRedundantSlash(&normalized_path);
  std::vector<cnetpp::base::StringPiece> pancestors;
  if (!GetAllAncestorPaths(normalized_path, &pancestors)) {
    LOG(ERROR) << "Invalid path: " << path;
    return std::make_tuple<bool, const char *, std::string>(
        false,
        JavaExceptions::IllegalArgumentException(),
        "Invalid path: " + path);
  }

  std::vector<std::string> ancestors;
  ancestors.reserve(pancestors.size());
  for (int i = static_cast<int>(pancestors.size()) - 1; i >= 0; --i) {
    ancestors.emplace_back(pancestors[i].as_string());
  }

  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  bool ssd = false;
  for (auto &p : ancestors) {
    if (!ssd && ssd_paths_.find(p) != ssd_paths_.end()) {
      ssd = true;
      break;
    }
  }

  std::string t;
  bool found = false;
  for (auto &a : ancestors) {
    auto itr = path_to_team_.find(a);
    if (itr != path_to_team_.end()) {
      t = itr->second;
      found = true;
      break;
    }
  }
  if (!found) {
    for (const auto &pt : pattern_team_mappings_) {
      if (std::regex_match(path, pt.first)) {
        found = true;
        t = pt.second;
        LOG(INFO) << "Mapping: " << path << "->" << t;
        break;
      }
    }
  }
  if (!found) {
    t = kDefault;
  }
  if (blacklist_teams_.find(t) != blacklist_teams_.end()) {
    auto m = GetMetrics(std::string("BlackListHitCount#team=") +
                        t + "#switch=" + ReadableSwitchType());
    m->Inc();
    std::string err_msg = "Your team: " + t;
    err_msg += " is in blacklist, for more information, ";
    err_msg += "please refer to https://wiki.bytedance.net/pages/viewpage.";
    err_msg += "action?pageId=92103943 or contact with administrator";
    if (swch_.load() == Switch::kOn) {
      return std::make_tuple<bool, const char *, std::string>(
          false, JavaExceptions::IOException(), std::move(err_msg));
    } else {
      LOG(INFO) << err_msg;
      return res;
    }
  }
  if (whitelist_teams_.find(t) != whitelist_teams_.end()) {
    auto m = GetMetrics(std::string("WhiteListHitCount#team=") +
                        t + "#switch=" + ReadableSwitchType());
    m->Inc();
    return res;
  }

  auto quota = team_to_quota_.find(t);
  if (quota == team_to_quota_.end()) {
    LOG(ERROR) << "No quota for team: " << t;
    return res;
  }

  auto usage = team_to_usage_.find(t);
  if (usage == team_to_usage_.end()) {
    LOG(ERROR) << "No usage for team: " << t;
    return res;
  }

  if ((ssd && usage->second.ssd() >= quota->second.ssd()) ||
      (!ssd && usage->second.sata() >= quota->second.sata())) {
    auto m = GetMetrics(std::string("SpaceQuotaExceededCount#team=") + t +
                        "#switch=" + ReadableSwitchType() +
                        "#storage=" + ReadableStorageType(ssd));
    m->Inc();
    std::string err_msg;
    if (t == kDefault) {
      err_msg = "path: " + path + " is not claimed, ";
      err_msg += "please refer to https://wiki.bytedance.net/pages/";
      err_msg += "viewpage.action?pageId=92103943";
    } else {
      err_msg = ReadableStorageType(ssd) + " space quota exceeded occurred,";
      err_msg += " please remove some unused data, ";
      err_msg += "team: " + t + ", path: " + path + ", quota: ";
      err_msg += quota->second.SerializeToJsonString();
      err_msg += ", usage: " + usage->second.SerializeToJsonString();
    }
    if (swch_.load() == Switch::kOn) {
      return std::make_tuple<bool, const char *, std::string>(
          false,
          JavaExceptions::DSQuotaExceededException(),
          std::move(err_msg));
    } else {
      LOG(ERROR) << err_msg;
    }
  }
  return res;
}

cnetpp::base::Value PathTeamSpaceQuotaManager::DumpToJson() const {
  cnetpp::base::Object res;
  cnetpp::base::Object path_to_team;
  cnetpp::base::Object team_to_quota;
  cnetpp::base::Object team_to_usage;
  cnetpp::base::Array ssd_paths;
  cnetpp::base::Array blacklist_teams;
  cnetpp::base::Array whitelist_teams;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  res["switch"] = ReadableSwitchType();
  for (auto& ptt : path_to_team_) {
    path_to_team[ptt.first] = ptt.second;
  }
  res["path_to_team"] = path_to_team;
  for (auto& ttq : team_to_quota_) {
    team_to_quota[ttq.first] = ttq.second.SerializeToJson();
  }
  res["team_to_quota"] = team_to_quota;
  for (auto& ttu : team_to_usage_) {
    team_to_usage[ttu.first] = ttu.second.SerializeToJson();
  }
  res["team_to_usage"] = team_to_usage;
  for (auto& ssd_path : ssd_paths_) {
    ssd_paths.Append(cnetpp::base::Value(ssd_path));
  }
  res["ssd_paths"] = ssd_paths;
  for (auto& blacklist : blacklist_teams_) {
    blacklist_teams.Append(cnetpp::base::Value(blacklist));
  }
  res["blacklist_teams"] = blacklist_teams;
  for (auto& whitelist : whitelist_teams_) {
    whitelist_teams.Append(cnetpp::base::Value(whitelist));
  }
  res["whitelist_teams"] = whitelist_teams;
  return cnetpp::base::Value(std::move(res));
}

}  // namespace dancenn

