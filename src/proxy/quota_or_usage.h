// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_QUOTA_OR_USAGE_H_
#define PROXY_QUOTA_OR_USAGE_H_

#include <cnetpp/base/csonpp.h>

#include <string>
#include <utility>

namespace dancenn {

class QuotaOrUsage {
 public:
  QuotaOrUsage() = default;
  QuotaOrUsage(int64_t sata, int64_t ssd) {
    sata_ = sata;
    ssd_ = ssd;
  }

  int64_t sata() const {
    return sata_;
  }

  int64_t ssd() const {
    return ssd_;
  }

  void Merge(const QuotaOrUsage& other) {
    sata_ += other.sata_;
    ssd_ += other.ssd_;
  }

  cnetpp::base::Value SerializeToJson() const {
    cnetpp::base::Object obj;
    obj["sata"] = sata_;
    obj["ssd"] = ssd_;
    return cnetpp::base::Value(std::move(obj));
  }

  std::string SerializeToJsonString() const {
    return cnetpp::base::Parser::Serialize(SerializeToJson());
  }

  bool DeserializeFromJson(const std::string& str) {
    cnetpp::base::Value v;
    if (!cnetpp::base::Parser::Deserialize(str, &v)) {
      return false;
    }
    if (!v.IsObject()) {
      return false;
    }
    auto obj = v.AsObject();
    auto itr = obj.Find("sata");
    if (itr == obj.End() || !itr->second.IsIntegral()) {
      return false;
    }
    sata_ = itr->second.AsInteger();
    itr = obj.Find("ssd");
    if (itr == obj.End() || !itr->second.IsIntegral()) {
      return false;
    }
    ssd_ = itr->second.AsInteger();
    return true;
  }

 private:
  int64_t sata_ { 0 };
  int64_t ssd_ { 0 };
};

}  // namespace dancenn

#endif  // PROXY_QUOTA_OR_USAGE_H_

