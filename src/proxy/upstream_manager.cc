// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/upstream_manager.h"

#include <cnetpp/tcp/tcp_client.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/end_point.h>
#include <cnetpp/base/ip_address.h>
#include <cnetpp/base/string_piece.h>
#include <cnetpp/base/string_utils.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <shared_mutex>
#include <memory>
#include <map>
#include <utility>
#include <vector>
#include <string>
#include <unordered_map>

#include "ClientNamenodeProtocol.pb.h"
#include "base/file_utils.h"
#include "rpc/failovered_rpc_channel.h"

DECLARE_string(danceproxy_namenodes_configuration_file);

namespace dancenn {

UpstreamManager::UpstreamManager(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    RpcClientOptions options,
    std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers)
    : tcp_client_(tcp_client), options_(options), handlers_(handlers) {
  if (!ReloadNamenodesConfigurationFile()) {
    LOG(FATAL) << "Failed to load namenodes configuration file: "
               << FLAGS_danceproxy_namenodes_configuration_file;
  }
  reloader_.reset(new cnetpp::concurrency::Thread(
      [&] () -> bool {
        int64_t cur_hour = 0;
        while (!stopped_) {
          int64_t tmp = std::chrono::duration_cast<std::chrono::seconds>(
              std::chrono::system_clock::now().time_since_epoch()).count();
          if (tmp != cur_hour) {
            LOG(INFO) << "Reloading namenodes.json and "
                       << "cleaning up idle upstreams.";
            cur_hour = tmp;
          }
          ReloadNamenodesConfigurationFile();
          CleanupIdleUpstreams();
          CleanupIdleDNUpstreams();
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        return true;
      }, "conf-reloader"));
  reloader_->Start();
}

void UpstreamManager::Stop() {
  std::unique_lock<ReadWriteLock> guard(rw_lock_);
  stopped_ = true;
  for (auto& uu : dn_upstreams_) {
    for (auto& fu : uu.second) {
      while (fu.second->stub_.use_count() > 1) {
        guard.unlock();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        guard.lock();
      }
    }
  }
  for (auto& uu : upstreams_) {
    for (auto& fu : uu.second) {
      while (fu.second->stub_.use_count() > 1) {
        guard.unlock();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        guard.lock();
      }
    }
  }
  reloader_->Stop();
}

std::shared_ptr<Upstream> UpstreamManager::GetUpstream(const std::string& user,
                                                       const std::string& fs) {
  {
    // fast path
    std::shared_lock<ReadWriteLock> read_guard(rw_lock_);
    if (stopped_) {
      return nullptr;
    }
    auto uitr = upstreams_.find(user);
    if (uitr != upstreams_.end()) {
      auto fitr = uitr->second.find(fs);
      if (fitr != uitr->second.end()) {
        fitr->second->access_time_ =
            std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
        return fitr->second;
      }
    }
  }
  {
    // slow path
    std::unique_lock<ReadWriteLock> write_guard(rw_lock_);
    if (stopped_) {
      return nullptr;
    }
    auto uitr = upstreams_.find(user);
    if (uitr == upstreams_.end()) {
      uitr = upstreams_.emplace(user,
          std::unordered_map<std::string, std::shared_ptr<Upstream>>()).first;
    }
    auto fitr = uitr->second.find(fs);
    if (fitr == uitr->second.end()) {
      auto eitr = cp_namenodes_.find(fs);
      if (eitr == cp_namenodes_.end()) {
        // unknown fs
        return nullptr;
      }
      RpcClientOptions options = options_;
      options.set_user(user);
      auto us = std::make_shared<Upstream>();
      auto channel = new FailoveredRpcChannel(tcp_client_, options,
                                              eitr->second, handlers_);
      us->access_time_ = std::chrono::duration_cast<std::chrono::seconds>(
          std::chrono::system_clock::now().time_since_epoch()).count();
      us->stub_ = std::make_shared<cloudfs::ClientNamenodeProtocol::Stub>(
          channel, google::protobuf::Service::STUB_OWNS_CHANNEL);
      fitr = uitr->second.emplace(fs, std::move(us)).first;
      LOG(INFO) << "Create upstream to " << fs << ", user: " << user;
    }
    return fitr->second;
  }
}

std::vector<std::shared_ptr<Upstream>> UpstreamManager::GetAllUpstreams(
    const std::string& user) {
  std::vector<std::shared_ptr<Upstream>> res;
  std::vector<std::string> all_fs;
  {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    for (auto &namenode : cp_namenodes_) {
      all_fs.emplace_back(namenode.first);
    }
  }
  for (auto& fs : all_fs) {
    auto upstream = GetUpstream(user, fs);
    if (upstream) {
      res.emplace_back(upstream);
    }
  }
  return res;
}

std::shared_ptr<DNUpstream> UpstreamManager::GetDNUpstream(
    const std::string& user, const std::string& fs) {
  std::unique_lock<ReadWriteLock> guard(rw_lock_);
  if (stopped_) {
    return nullptr;
  }
  auto uitr = dn_upstreams_.find(user);
  if (uitr == dn_upstreams_.end()) {
    uitr = dn_upstreams_.emplace(user,
        std::unordered_map<std::string, std::shared_ptr<DNUpstream>>()).first;
  }
  auto fitr = uitr->second.find(fs);
  if (fitr == uitr->second.end()) {
    auto eitr = dp_namenodes_.find(fs);
    if (eitr == dp_namenodes_.end()) {
      return nullptr;
    }
    auto us = std::make_shared<DNUpstream>();
    RpcClientOptions options = options_;
    options.set_user(user);
    options.set_protocol_name(
        "org.apache.hadoop.hdfs.server.protocol.DatanodeProtocol");
    options.set_protocol_version(1);
    auto channel = new FailoveredRpcChannel(tcp_client_, options,
                                            eitr->second, handlers_);
    us->stub_ =
        std::make_shared<cloudfs::datanode::DatanodeProtocolService::Stub>(
            channel, google::protobuf::Service::STUB_OWNS_CHANNEL);
    us->access_time_ = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    fitr = uitr->second.emplace(fs, std::move(us)).first;
    LOG(INFO) << "Create dn upstream to " << fs << ", user: " << user;
  }
  return fitr->second;
}

bool UpstreamManager::ReloadNamenodesConfigurationFile() {
  RandomAccessFile raf(FLAGS_danceproxy_namenodes_configuration_file);
  auto size = raf.Size();
  if (size < 0) {
    LOG(ERROR) << "Failed to get size of file: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }

  auto buffer = std::unique_ptr<char[]>(new char[size]);
  cnetpp::base::StringPiece result;
  if (!raf.Read(0, buffer.get(), size, &result)) {
    LOG(ERROR) << "Failed to read contents from file: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }

  cnetpp::base::Value json_config;
  if (!cnetpp::base::Parser::Deserialize(result.as_string(), &json_config)) {
    LOG(ERROR) << "Failed to parse json file: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }

  if (!json_config.IsObject()) {
    LOG(ERROR) << "json file is not expected format: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }

  auto obj_config = json_config.AsObject();
  auto itr_nameservices = obj_config.Find("nameservices");
  if (itr_nameservices == obj_config.End()) {
    LOG(ERROR) << "No nameservices fields in config file: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }
  if (!itr_nameservices->second.IsString()) {
    LOG(ERROR) << "nameservices fields is not string format: "
               << FLAGS_danceproxy_namenodes_configuration_file;
    return false;
  }

  std::map<std::string, std::vector<cnetpp::base::EndPoint>> fs_to_cp_endpoints;
  std::map<std::string, std::vector<cnetpp::base::EndPoint>> fs_to_dp_endpoints;

  auto str_nameservices = itr_nameservices->second.AsString();
  auto nameservices =
      cnetpp::base::StringUtils::SplitByChars(str_nameservices, ",");
  for (auto& nameservice : nameservices) {
    auto itr_nameservice = obj_config.Find(nameservice);
    if (itr_nameservice == obj_config.End()) {
      LOG(ERROR) << "No " << nameservice << " field in config file: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      return false;
    }
    if (!itr_nameservice->second.IsObject()) {
      LOG(ERROR) << nameservice << " field is not object format: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      return false;
    }
    auto protocols = itr_nameservice->second.AsObject();
    auto cp = protocols.Find("ClientProtocol");
    if (cp == protocols.End() || !cp->second.IsArray()) {
      LOG(ERROR) << nameservice
                 << " no ClientProtocol field or invalid format: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      return false;
    }
    auto dp = protocols.Find("DatanodeProtocol");
    if (dp == protocols.End() || !dp->second.IsArray()) {
      LOG(ERROR) << nameservice
                 << " no DatanodeProtocol field or invalid format: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      return false;
    }
    auto cp_namenodes = cp->second.AsArray();
    auto dp_namenodes = dp->second.AsArray();
    auto cp_endpoints = ParseEndPoints("ClientProtocol", cp_namenodes);
    auto dp_endpoints = ParseEndPoints("DatanodeProtocol", dp_namenodes);
    if (cp_endpoints.empty() || dp_endpoints.empty()) {
      return false;
    }
    fs_to_cp_endpoints.emplace("hdfs://" + nameservice,
                               std::move(cp_endpoints));
    fs_to_dp_endpoints.emplace("hdfs://" + nameservice,
                               std::move(dp_endpoints));
  }
  std::unique_lock<ReadWriteLock> guard(rw_lock_);
  cp_namenodes_.swap(fs_to_cp_endpoints);
  dp_namenodes_.swap(fs_to_dp_endpoints);
  return true;
}

std::vector<cnetpp::base::EndPoint> UpstreamManager::ParseEndPoints(
    const std::string& protocol,
    const cnetpp::base::Array& endpoints) {
  std::vector<cnetpp::base::EndPoint> res;
  for (auto itr = endpoints.Begin(); itr != endpoints.End(); ++itr) {
    if (!itr->IsString()) {
      LOG(ERROR) << "Fields in " << protocol << " is not string format: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      res.clear();
      return res;
    }
    auto endpoint = itr->AsString();
    auto host_and_port =
        cnetpp::base::StringUtils::SplitByChars(endpoint, ":");
    if (host_and_port.size() != 2) {
      LOG(ERROR) << "Invalid " << protocol << " field in config file: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      res.clear();
      return res;
    }
    cnetpp::base::IPAddress ip;
    if (!cnetpp::base::IPAddress::LiteralToNumber(host_and_port[0], &ip)) {
      LOG(ERROR) << host_and_port[0]
                 << " is not a valid ipv4 address in config file: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      res.clear();
      return res;
    }
    auto port = std::strtol(host_and_port[1].data(), nullptr, 10);
    if (port <= 0 || port > 65535) {
      LOG(ERROR) << host_and_port[1]
                 << " is not a valid port number in config file: "
                 << FLAGS_danceproxy_namenodes_configuration_file;
      res.clear();
      return res;
    }
    res.emplace_back(ip, port);
  }
  return res;
}

void UpstreamManager::CleanupIdleUpstreams() {
  auto now = std::chrono::duration_cast<std::chrono::seconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  std::vector<std::pair<std::string, std::string>> idle_ups;
  {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    for (auto &u : upstreams_) {
      for (auto &f : u.second) {
        // clean up the upstreams idled for 10 mins
        if (now - f.second->access_time_ >= 10 * 60) {
          idle_ups.emplace_back(std::make_pair(u.first, f.first));
        }
      }
    }
  }
  if (idle_ups.empty()) {
    return;
  }
  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    for (auto& uf : idle_ups) {
      auto uitr = upstreams_.find(uf.first);
      if (uitr == upstreams_.end()) {
        continue;
      }
      auto fitr = uitr->second.find(uf.second);
      CHECK(fitr != uitr->second.end());
      if (now - fitr->second->access_time_ >= 10 * 60) {
        LOG(INFO) << "Idle upstream, user: "
                  << uf.first << ", fs: " << uf.second << ", Cleaning up...";
        FailoveredRpcChannel* channel = static_cast<FailoveredRpcChannel*>(
            fitr->second->stub_->channel());
        CHECK_NOTNULL(channel);
        channel->Shutdown();

        uitr->second.erase(fitr);
        if (uitr->second.empty()) {
          upstreams_.erase(uitr);
        }
      }
    }
  }
}

void UpstreamManager::CleanupIdleDNUpstreams() {
  auto now = std::chrono::duration_cast<std::chrono::seconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  std::vector<std::pair<std::string, std::string>> idle_ups;
  {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    for (auto &u : dn_upstreams_) {
      for (auto &f : u.second) {
        // clean up the dn upstreams idled for 10 mins
        if (now - f.second->access_time_ >= 10 * 60) {
          idle_ups.emplace_back(std::make_pair(u.first, f.first));
        }
      }
    }
  }
  if (idle_ups.empty()) {
    return;
  }
  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    for (auto& uf : idle_ups) {
      auto uitr = dn_upstreams_.find(uf.first);
      if (uitr == dn_upstreams_.end()) {
        continue;
      }
      auto fitr = uitr->second.find(uf.second);
      CHECK(fitr != uitr->second.end());
      if (now - fitr->second->access_time_ >= 10 * 60) {
        LOG(INFO) << "Idle dn upstream, user: "
                  << uf.first << ", fs: " << uf.second << ", Cleaning up...";
        FailoveredRpcChannel* channel = static_cast<FailoveredRpcChannel*>(
            fitr->second->stub_->channel());
        CHECK_NOTNULL(channel);
        channel->Shutdown();

        uitr->second.erase(fitr);
        if (uitr->second.empty()) {
          dn_upstreams_.erase(uitr);
        }
      }
    }
  }
}

}  // namespace dancenn

