// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/fanout_closure.h"

#include <google/protobuf/service.h>

#include <algorithm>

#include "rpc/rpc_controller.h"
#include "rpc/done_closure.h"

namespace dancenn {

void FanoutClosure::Run() {
  if (remaining_.fetch_sub(1) == 1) {
    bool failed = false;
    for (auto& c : controllers_) {
      if (c->status() != RpcStatus::kSuccess) {
        auto done_closure = dynamic_cast<DoneClosure*>(done_->orig());
        CHECK_NOTNULL(done_closure);
        done_closure->rpc_controller()->MarkAsFailed(c->Exception(),
                                                     c->ErrorText());
        done_closure->rpc_controller()->set_status(c->status());
        // skip response merge step if some call is failed
        failed = true;
        break;
      }
    }
    if (!failed && merge_) {
      auto done_closure = dynamic_cast<DoneClosure*>(done_->orig());
      CHECK_NOTNULL(done_closure);
      auto merged_resp = done_closure->rpc_controller()->response();
      for (int i = 0; i < controllers_.size(); ++i) {
        merge_(merged_resp, controllers_[i]->response());
      }
    }
    done_->Run();
    delete this;
  }
}

}  // namespace dancenn
