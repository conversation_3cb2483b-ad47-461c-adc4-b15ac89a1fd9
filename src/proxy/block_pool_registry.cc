// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/block_pool_registry.h"

#include <shared_mutex>
#include <string>
#include <unordered_map>

#include "base/read_write_lock.h"
#include "proxy/upstream_manager.h"

DECLARE_string(danceproxy_rpc_default_user);

namespace dancenn {

void BlockPoolRegistry::RefreshBlockPools() {
  for (auto& namenode : upstream_manager_->namenodes()) {
    {
      std::shared_lock<ReadWriteLock> guard(rw_lock_);
      // blockpool is immutable, so no need to refresh again and again
      if (fs_to_bp_.find(namenode.first) != fs_to_bp_.end()) {
        continue;
      }
    }
    auto us = upstream_manager_->GetDNUpstream(
        FLAGS_danceproxy_rpc_default_user, namenode.first);
    if (!us) {
      LOG(ERROR) << "Could not get DNUpstream for " << namenode.first;
      continue;
    }

    RpcController controller;
    controller.set_retry_count(2);
    cloudfs::VersionRequestProto request;
    cloudfs::VersionResponseProto response;
    // sync call
    us->stub_->versionRequest(&controller, &request, &response, nullptr);
    if (controller.status() != RpcStatus::kSuccess) {
      LOG(ERROR) << "Failed to send versionRequest to " + namenode.first
                 << ", status: " << static_cast<int>(controller.status());
      continue;
    } else {
      LOG(INFO) << "block pool: " << response.info().blockpoolid()
                << " -> fs: " << namenode.first;
    }
    {
      std::unique_lock<ReadWriteLock> guard(rw_lock_);
      bp_to_fs_[response.info().blockpoolid()] = namenode.first;
      fs_to_bp_[namenode.first] = response.info().blockpoolid();
    }
  }
}

std::string BlockPoolRegistry::GetFs(const std::string& bp_id) {
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  auto itr = bp_to_fs_.find(bp_id);
  if (itr == bp_to_fs_.end()) {
    return "";
  }
  return itr->second;
}

}  // namespace dancenn

