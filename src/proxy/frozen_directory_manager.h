// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_FROZEN_DIRECTORY_MANAGER_H_
#define PROXY_FROZEN_DIRECTORY_MANAGER_H_

#include <cnetpp/base/csonpp.h>

#include <algorithm>
#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>

#include "proxy/abstract_condition_checker.h"
#include "proxy/upstream_manager.h"

namespace dancenn {

class FrozenDirectoryManager : public AbstractConditionChecker {
 public:
  explicit FrozenDirectoryManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : AbstractConditionChecker(upstream_manager, "FrozenDirectoryManager") {
  }

  void Init() override;

  std::tuple<bool, const char*, std::string> IsFrozen(
      const std::string& ns, const std::string& path, bool is_trash);

  cnetpp::base::Value DumpToJson() const override;

  mutable ReadWriteLock rw_lock_;
  std::unordered_map<std::string,
      std::unordered_map<std::string,
          std::unordered_set<std::string>>> frozen_directories_;

  bool Refresh() override;
};

}  // namespace dancenn

#endif  // PROXY_FROZEN_DIRECTORY_MANAGER_H_

