// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_MOUNTS_MANAGER_H_
#define PROXY_MOUNTS_MANAGER_H_

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/string_utils.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <zookeeper/zookeeper.h>

#include <shared_mutex>
#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/read_write_lock.h"
#include "base/count_down_latch.h"
#include "proxy/mount_entry.h"
#include "proxy/upstream_manager.h"

namespace dancenn {

// Manages mount table and keep up-to-date to ZooKeeper.
class MountsManager {
 public:
  MountsManager() = default;
  virtual ~MountsManager() = default;

  void Init(std::shared_ptr<UpstreamManager> upstream_manager);
  void Stop();

  void WaitUntilInstalled() const;

  void RegisterAfterInstallCallback(std::function<void()> cb) {
    after_install_callbacks_.emplace_back(std::move(cb));
  }

  std::string Resolve(const std::string& path, bool* is_trash = nullptr) const;

  std::vector<std::string> ResolveAll(const std::string& path) const;

  bool IsReadOnly(const std::string& path) const;

  bool InRenameWhiteList(const std::string& path) const;

  std::unordered_set<std::string> all_fs() const {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    return all_fs_;  // return a copy
  }

  // Determine whether given path is exactly a valid mount point.
  bool IsMountPoint(const std::string& path) const {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    if (mount_lookup_map_->find(path) != mount_lookup_map_->end()) {
      return true;
    }
    return false;
  }

  // Determine whether given path contains a mount point.
  // Directory is considered unified even if itself is a mount point,
  // unless it contains another mount point.
  bool IsUnified(const std::string& path) const;

  cnetpp::base::Value DumpToJson() const;

 protected:
  using MountLookupMapType =
      std::unordered_map<std::string, std::vector<std::shared_ptr<MountEntry>>>;

  struct ZkRefreshContext {
    ZkRefreshContext()
        : latch(1),
          data(std::unique_ptr<char[]>(new char[kZkBufferLength])),
          data_length(kZkBufferLength),
          success(false) {
    }

    CountDownLatch latch;
    std::unique_ptr<char[]> data;
    int data_length;
    struct Stat stat;
    bool success;
  };

  std::shared_ptr<MountEntry> ResolveSegmentedMountInternal(
      const std::string& parent, const std::string& path) const;

  void ScheduleZkRefreshTask();
  void RefreshZkData();
  bool GetRefreshResult(
      std::shared_ptr<std::vector<std::shared_ptr<ZkRefreshContext>>> all_ctxs,
      cnetpp::base::StringPiece* data, int* version);

  void HandleMountTableChange(cnetpp::base::StringPiece data, int version);
  void HandleReadOnlyChange(cnetpp::base::StringPiece data, int version);

  bool ParseMountTable(cnetpp::base::StringPiece data,
                       int version,
                       std::vector<std::shared_ptr<MountEntry>>* table);
  std::shared_ptr<MountEntry> ParseMountEntryString(
      cnetpp::base::StringPiece mount);
  bool ParseMountEntryJson(const std::string &mount,
                           std::vector<std::shared_ptr<MountEntry>>* entries);
  std::unique_ptr<MountLookupMapType> BuildLookupMap(
      const std::vector<std::shared_ptr<MountEntry>>& entries);

  void InstallMountTable(
      const std::vector<std::shared_ptr<MountEntry>>& entries,
      cnetpp::base::StringPiece data, int version);
  void AfterInstall();

  volatile bool after_install_running_;

  std::vector<std::string> rename_dir_whitelist_;

 private:
  static const int kZkBufferLength = 10 * 1024 * 1024;  // 10MB

  std::vector<std::tuple<std::string,
                         zhandle_t*,
                         std::unique_ptr<ReadWriteLock>>> handles_;

  std::unique_ptr<cnetpp::concurrency::ThreadPool> workers_;

  std::shared_ptr<UpstreamManager> upstream_manager_;

  std::vector<std::function<void()>> after_install_callbacks_;

  mutable ReadWriteLock rw_lock_;
  // For http or log purpose when mount table is changed
  std::string raw_mount_table_;
  int raw_mount_table_zk_version_ { -1 };
  std::string raw_read_only_paths_;
  int raw_read_only_paths_zk_version_ { -1 };
  std::unordered_set<std::string> all_fs_;
  std::unique_ptr<std::unordered_set<std::string>> read_only_paths_;
  std::unique_ptr<MountLookupMapType> mount_lookup_map_;

  std::shared_ptr<MountEntry> root_;

  volatile bool installed_;
};

}  // namespace dancenn

#endif  // PROXY_MOUNTS_MANAGER_H_

