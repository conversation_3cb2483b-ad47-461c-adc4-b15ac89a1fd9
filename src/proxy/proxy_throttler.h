// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_THROTTLER_H_
#define PROXY_THROTTLER_H_

#include <cnetpp/base/csonpp.h>

#include <memory>
#include <string>
#include <unordered_map>

#include "base/token_bucket.h"
#include "base/status.h"
#include "proxy/abstract_condition_checker.h"

namespace dancenn {

class ProxyThrottler : public AbstractConditionChecker {
 public:
  struct Entry {
   public:
    std::string path_;
    std::string rpc_;
    int permits_;

    std::string ToString() const;
    bool DeserializeFromJson(const cnetpp::base::Object& json);
  };

  explicit ProxyThrottler(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : AbstractConditionChecker(upstream_manager, "Throttler") {
  }

  void Init() override;

  Status CheckThrottle(const std::string& path, const std::string& rpc);

  cnetpp::base::Value DumpToJson() const override;

  mutable ReadWriteLock rw_lock_;
  using RpcToTokenBucketMapType = std::unordered_map<std::string, TokenBucket>;
  std::unordered_map<std::string, RpcToTokenBucketMapType> token_buckets_;

  bool Refresh() override;
};

}  // namespace dancenn

#endif  // PROXY_THROTTLER_H_

