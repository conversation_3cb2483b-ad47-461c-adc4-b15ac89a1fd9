// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_PATH_TEAM_SPACE_QUOTA_MANAGER_H_
#define PROXY_PATH_TEAM_SPACE_QUOTA_MANAGER_H_

#include <gflags/gflags.h>
#include <hiredis/hiredis.h>

#include <algorithm>
#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <utility>
#include <regex>
#include "base/read_write_lock.h"
#include "proxy/abstract_condition_checker.h"
#include "proxy/quota_or_usage.h"
#include "proxy/upstream_manager.h"

namespace dancenn {

class PathTeamSpaceQuotaManager : public AbstractConditionChecker {
 public:
  explicit PathTeamSpaceQuotaManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : AbstractConditionChecker(upstream_manager, "PathTeamSpaceQuota") {
  }

  void Init() override;

  std::tuple<bool, const char*, std::string> CheckSpace(
      const std::string& path);

  cnetpp::base::Value DumpToJson() const override;

 protected:  // for test
  static const char* kDefault;

  mutable ReadWriteLock rw_lock_;
  std::unordered_map<std::string, std::string> path_to_team_;
  std::unordered_map<std::string, QuotaOrUsage> team_to_quota_;
  std::unordered_map<std::string, QuotaOrUsage> team_to_usage_;
  std::unordered_set<std::string> ssd_paths_;
  std::unordered_set<std::string> blacklist_teams_;
  std::unordered_set<std::string> whitelist_teams_;
  std::vector<std::pair<std::regex, std::string>> pattern_team_mappings_;

  bool Refresh() override;

  bool RefreshRedis();
  bool RefreshMappingFile();
  std::string ReadableStorageType(bool ssd) {
    if (ssd) {
      return "SSD";
    }
    return "SATA";
  }
};

}  // namespace dancenn

#endif  // PROXY_PATH_TEAM_SPACE_QUOTA_MANAGER_H_

