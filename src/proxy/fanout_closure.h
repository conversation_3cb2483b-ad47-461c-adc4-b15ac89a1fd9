// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_FANOUT_CLOSURE_H_
#define PROXY_FANOUT_CLOSURE_H_

#include <google/protobuf/service.h>

#include <functional>
#include <atomic>
#include <vector>
#include <utility>
#include <memory>

#include "rpc/rpc_controller.h"
#include "service/method_tracer_closure.h"

namespace dancenn {

class FanoutClosure : public google::protobuf::Closure {
 public:
  FanoutClosure(MethodTracerClosure* done,
                std::vector<std::unique_ptr<RpcController>>&& controllers,
                std::function<void(google::protobuf::Message*,
                                   google::protobuf::Message*)> merge = nullptr)
      : done_(done),
        remaining_(controllers.size()),
        controllers_(std::move(controllers)), merge_(std::move(merge)) {
  }
  ~FanoutClosure() override = default;

  void Run() override;

  RpcController* GetController(size_t i) {
    return controllers_[i].get();
  }

 private:
  MethodTracerClosure* done_;
  std::atomic<size_t> remaining_;
  std::vector<std::unique_ptr<RpcController>> controllers_;
  std::function<void(google::protobuf::Message*,
                     google::protobuf::Message*)> merge_;
};

}  // namespace dancenn

#endif  // PROXY_FANOUT_CLOSURE_H_

