// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/mounts_manager.h"

#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/base/string_utils.h>
#include <cnetpp/base/string_piece.h>
#include <cnetpp/base/csonpp.h>
#include <gflags/gflags.h>
#include <zookeeper/zookeeper.h>

#include <shared_mutex>
#include <algorithm>
#include <memory>
#include <regex>
#include <string>
#include <thread>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/read_write_lock.h"
#include "base/count_down_latch.h"
#include "proxy/mount_entry.h"

DECLARE_string(danceproxy_mount_table_zk_quorum);
DECLARE_string(danceproxy_mount_table_zk_path);
DECLARE_string(danceproxy_read_only_zk_path);
DECLARE_int32(danceproxy_zk_recv_timeout_ms);
DECLARE_string(danceproxy_rename_dir_whitelist);

namespace dancenn {

static const std::regex kTrashRegex("/user/[^/]+/.Trash/[^/]+/(.+)");

static void ZkLogger(const char* message) {
  LOG(ERROR) << "ZOOKEEPER: " << message;
}

static void GlobalZkWatcher(zhandle_t* handle, int type, int state,
                            const char* path, void* context);

static zhandle_t* ConnectToZk(
    std::tuple<std::string,
               zhandle_t*,
               std::unique_ptr<ReadWriteLock>>* context) {
  CHECK_NOTNULL(context);
  auto handle = zookeeper_init2(std::get<0>(*context).c_str(),
                                GlobalZkWatcher,
                                FLAGS_danceproxy_zk_recv_timeout_ms,
                                nullptr,
                                static_cast<void*>(context),
                                0,
                                &ZkLogger);
  return handle;
}

static void GlobalZkWatcher(zhandle_t* handle, int type, int state,
                            const char* path, void* context) {
  if (type == ZOO_SESSION_EVENT) {
    if (state == ZOO_AUTH_FAILED_STATE) {
      LOG(FATAL) << "Failed to connect to zookeeper server "
          "due to authentication error.";
    } else if (state == ZOO_EXPIRED_SESSION_STATE) {
      LOG(ERROR) << "Zookeeper session expired, try to reconnect.";
      auto ctx = static_cast<std::tuple<std::string, zhandle_t*,
          std::unique_ptr<ReadWriteLock>>*>(context);
      auto new_handle = ConnectToZk(ctx);
      auto old_handle = std::get<1>(*ctx);
      if (new_handle) {
        {
          std::unique_lock<ReadWriteLock> guard(*(std::get<2>(*ctx)));
          std::get<1>(*ctx) = new_handle;
        }
        if (old_handle) {
          zookeeper_close(old_handle);
        }
      }
    }
  }
  // ignore all of other types of events
}

void MountsManager::Init(std::shared_ptr<UpstreamManager> upstream_manager) {
  auto zk_servers = cnetpp::base::StringUtils::SplitByChars(
      FLAGS_danceproxy_mount_table_zk_quorum, ",");

  rename_dir_whitelist_ = cnetpp::base::StringUtils::SplitByChars(
      FLAGS_danceproxy_rename_dir_whitelist, ",");
  rename_dir_whitelist_.erase(
      std::remove(rename_dir_whitelist_.begin(),
                  rename_dir_whitelist_.end(), ""),
      rename_dir_whitelist_.end());
  for (auto& d : rename_dir_whitelist_) {
    if (*(d.end()) != '/') {
      d = d + "/";
    }
  }

  handles_.reserve(zk_servers.size());  // make sure reallocation not happen
  for (auto& server : zk_servers) {
    handles_.emplace_back(std::make_tuple(server,
                                          static_cast<zhandle_t*>(nullptr),
                                          std::make_unique<ReadWriteLock>()));
    auto handle = ConnectToZk(&(handles_.back()));
    if (!handle) {
      LOG(FATAL) << "Failed to create zookeeper handle when connecting to "
                 << server;
    }
    std::unique_lock<ReadWriteLock> guard(*(std::get<2>(handles_.back())));
    std::get<1>(handles_.back()) = handle;
  }
  installed_ = false;
  after_install_running_ = false;

  workers_ = std::make_unique<cnetpp::concurrency::ThreadPool>("zkrfsh", true);
  // An extra thread is used to invoke after install callbacks asynchronously.
  // And another extra thread is used for RefreshZkData().
  workers_->set_num_threads(2 * handles_.size() + 2);
  workers_->Start();

  upstream_manager_ = std::move(upstream_manager);

  RefreshZkData();
}

void MountsManager::Stop() {
  workers_->Stop(false);
  for (auto& handle : handles_) {
    std::unique_lock<ReadWriteLock> guard(*(std::get<2>(handle)));
    zookeeper_close(std::get<1>(handle));
  }
  handles_.clear();
}

void MountsManager::WaitUntilInstalled() const {
  while (!installed_) {
    LOG(INFO) << "Waiting until mount table installed...";
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
}

std::string MountsManager::Resolve(const std::string& path,
                                   bool* is_trash) const {
  bool tmp_is_trash = false;
  const std::string* logical_path = &path;
  std::smatch match;
  std::string tmp_lpath = "/";
  if (std::regex_match(path.begin(), path.end(), match, kTrashRegex)) {
    auto &sub_match = match[1];
    tmp_lpath += std::string(sub_match.first, sub_match.second);
    logical_path = &tmp_lpath;
    tmp_is_trash = true;
  }
  if (is_trash) {
    *is_trash = tmp_is_trash;
  }

  std::shared_ptr<MountEntry> chosen;

  std::shared_lock<ReadWriteLock> guard(rw_lock_);

  if (logical_path->empty()) {
    return root_->fs_uri();
  } else {
    chosen = ResolveSegmentedMountInternal(*logical_path, *logical_path);
    if (!chosen) {
      cnetpp::base::StringPiece p(*logical_path);
      for (int i = static_cast<int>(p.length()) - 1; i >= 0; --i) {
        if (p[i] == '/') {
          std::string parent = p.substr(0, i).as_string();
          auto ee = ResolveSegmentedMountInternal(parent, *logical_path);
          if (ee) {
            chosen = ee;
            break;
          }
        }
      }
    }
  }
  if (!chosen) {
    chosen = root_;
  }
  return chosen->fs_uri();
}

std::vector<std::string> MountsManager::ResolveAll(
    const std::string& path) const {
  std::vector<std::shared_ptr<MountEntry>> mount_result;
  auto add_mounts = [&mount_result, this] (const std::string& p) {
    auto itr = mount_lookup_map_->find(p);
    if (itr != mount_lookup_map_->end()) {
      mount_result.insert(mount_result.begin(),
                          itr->second.begin(),
                          itr->second.end());
    }
  };

  std::shared_lock<ReadWriteLock> guard(rw_lock_);

  if (path.empty()) {
    mount_result.push_back(root_);
  } else {
    add_mounts(path);
    if (mount_result.empty()) {
      cnetpp::base::StringPiece p(path);
      for (int i = static_cast<int>(p.length()) - 1; i >= 0; --i) {
        if (p[i] == '/') {
          std::string parent =
              p.substr(0, static_cast<size_t>(i)).as_string();
          add_mounts(parent);
          if (!mount_result.empty()) {
            break;
          }
        }
      }
    }
  }
  std::vector<std::string> result;
  for (auto& e : mount_result) {
    result.emplace_back(e->fs_uri());
  }
  if (result.empty()) {
    result.emplace_back(root_->fs_uri());
  }
  return result;
}

bool MountsManager::IsReadOnly(const std::string& path) const {
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  if (read_only_paths_->empty()) {
    return false;
  }

  cnetpp::base::StringPiece p(path);
  for (int i = 0; i < static_cast<int>(p.length()); ++i) {
    if (p[i] == '/') {
      std::string parent = p.substr(0, i == 0 ? i + 1 : i).as_string();
      if (read_only_paths_->find(parent) != read_only_paths_->end()) {
        return true;
      }
    }
  }
  return read_only_paths_->find(path) != read_only_paths_->end();
}

bool MountsManager::InRenameWhiteList(const std::string& path) const {
  // Like a mount entry,
  // it's not allowed to rename a path to the dir in whitelist
  auto p = cnetpp::base::StringPiece(path);
  while (!p.empty() && *(p.end() - 1) == '/') {
    p.remove_suffix(1);
  }
  for (auto& wl : rename_dir_whitelist_) {
    if (p.starts_with(wl)) {
      return true;
    }
  }
  return false;
}

bool MountsManager::IsUnified(const std::string& path) const {
  std::string prefix = path;
  if (path.back() != '/') {
    prefix.append(1, '/');
  }

  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  for (auto& e : *mount_lookup_map_) {
    if (cnetpp::base::StringPiece(e.first).starts_with(prefix)) {
      return false;
    }
  }
  return true;
}

void MountsManager::ScheduleZkRefreshTask() {
  workers_->AddDelayTask([&]() {
                           RefreshZkData();
                           return true;
                         },
                         std::chrono::microseconds(1000000));
}

void MountsManager::RefreshZkData() {
  auto start = std::chrono::steady_clock::now();
  auto read_only_paths_contexts =
      std::make_shared<std::vector<std::shared_ptr<ZkRefreshContext>>>();
  auto mount_table_contexts =
      std::make_shared<std::vector<std::shared_ptr<ZkRefreshContext>>>();
  for (size_t i = 0; i < handles_.size(); ++i) {
    read_only_paths_contexts->emplace_back(
        std::make_shared<ZkRefreshContext>());
    mount_table_contexts->emplace_back(
        std::make_shared<ZkRefreshContext>());
  }
  for (int i = 0; i < static_cast<int>(handles_.size()); ++i) {
    workers_->AddTask([read_only_paths_contexts, i, this] () {
      auto res = zoo_get(std::get<1>(handles_[i]),
                         FLAGS_danceproxy_read_only_zk_path.c_str(),
                         0,
                         (*read_only_paths_contexts)[i]->data.get(),
                         &((*read_only_paths_contexts)[i]->data_length),
                         &((*read_only_paths_contexts)[i]->stat));
      if (res == ZOK) {
        (*read_only_paths_contexts)[i]->success = true;
      } else {
        (*read_only_paths_contexts)[i]->success = false;
        LOG(ERROR) << "Failed to get mount table from zk: " << res;
      }
      (*read_only_paths_contexts)[i]->latch.CountDown();
      return true;
    });
    workers_->AddTask([mount_table_contexts, i, this] () {
      auto res = zoo_get(std::get<1>(handles_[i]),
                         FLAGS_danceproxy_mount_table_zk_path.c_str(),
                         0,
                         (*mount_table_contexts)[i]->data.get(),
                         &((*mount_table_contexts)[i]->data_length),
                         &((*mount_table_contexts)[i]->stat));
      if (res == ZOK) {
        (*mount_table_contexts)[i]->success = true;
      } else {
        (*mount_table_contexts)[i]->success = false;
        LOG(ERROR) << "Failed to get mount table from zk: " << res;
      }
      (*mount_table_contexts)[i]->latch.CountDown();
      return true;
    });
  }

  cnetpp::base::StringPiece read_only_path_data;
  int read_only_path_version;
  bool read_only_path_ok = GetRefreshResult(read_only_paths_contexts,
                                            &read_only_path_data,
                                            &read_only_path_version);
  cnetpp::base::StringPiece mount_table_data;
  int mount_table_version;
  auto mount_table_ok = GetRefreshResult(mount_table_contexts,
                                         &mount_table_data,
                                         &mount_table_version);
  if (read_only_path_ok) {
    HandleReadOnlyChange(read_only_path_data, read_only_path_version);
  } else {
    // TODO(yangjinfeng.02) record an error
  }
  if (mount_table_ok) {
    HandleMountTableChange(mount_table_data, mount_table_version);
  } else {
    // TODO(yangjinfeng.02) record an error
  }

  ScheduleZkRefreshTask();
}

bool MountsManager::GetRefreshResult(
    std::shared_ptr<std::vector<std::shared_ptr<ZkRefreshContext>>> all_ctxs,
    cnetpp::base::StringPiece* data,
    int* version) {
  std::vector<std::shared_ptr<ZkRefreshContext>> contexts = *all_ctxs;
  auto quorum = all_ctxs->size() / 2 + 1;
  *version = -1;
  size_t num_success = 0;
  while (true) {
    std::vector<std::shared_ptr<ZkRefreshContext>> pending_contexts;
    for (auto context : contexts) {
      if (context->latch.Await(std::chrono::milliseconds(0))) {
        if (context->success) {
          if (context->stat.version > *version) {
            data->set(context->data.get(), context->data_length);
            *version = context->stat.version;
          }
          num_success++;
          if (num_success >= quorum) {
            break;
          }
        } else {
          LOG(ERROR) << "Failed to refresh data from zk.";
        }
      } else {
        pending_contexts.emplace_back(context);
      }
    }
    contexts = pending_contexts;
    if (num_success >= quorum || contexts.empty()) {
      break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }
  return num_success >= quorum;
}

void MountsManager::HandleMountTableChange(cnetpp::base::StringPiece data,
                                           int version) {
  if (version <= raw_mount_table_zk_version_) {
    DLOG(INFO) << "Old or unchanged mount table, skipped.";
    return;
  }

  std::vector<std::shared_ptr<MountEntry>> table;
  auto ok = ParseMountTable(data, version, &table);
  if (ok) {
    InstallMountTable(table, data, version);
  }
}

void MountsManager::HandleReadOnlyChange(cnetpp::base::StringPiece data,
                                         int version) {
  if (version <= raw_read_only_paths_zk_version_) {
    DLOG(INFO) << "Old or unchanged read only path, skipped.";
    return;
  }

  if (data.empty()) {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    if (!read_only_paths_) {
      read_only_paths_ = std::make_unique<std::unordered_set<std::string>>();
    } else {
      read_only_paths_->clear();
    }
    raw_read_only_paths_zk_version_ = version;
    raw_read_only_paths_.clear();
  } else {
    auto paths = cnetpp::base::StringUtils::SplitByChars(data, "\n");
    auto new_read_only_paths =
        std::make_unique<std::unordered_set<std::string>>();
    for (auto& p : paths) {
      cnetpp::base::StringUtils::Trim(&p);
      if (!p.empty()) {
        new_read_only_paths->emplace(std::move(p));
      }
    }

    {
      std::unique_lock<ReadWriteLock> guard(rw_lock_);
      read_only_paths_.swap(new_read_only_paths);
      raw_read_only_paths_zk_version_ = version;
      raw_read_only_paths_ = data.as_string();
    }
  }
  LOG(INFO) << "Read only paths are: " << raw_read_only_paths_
            << ", version: " << raw_read_only_paths_zk_version_;
}

bool MountsManager::ParseMountTable(cnetpp::base::StringPiece data,
    int version, std::vector<std::shared_ptr<MountEntry>>* table) {
  CHECK_NOTNULL(table);
  bool has_root = false;
  auto parts = cnetpp::base::StringUtils::SplitByChars(data, "\n");
  for (int i = 0; i < parts.size(); ++i) {
    auto& part = parts[i];
    cnetpp::base::StringUtils::Trim(&part);
    if (part.empty()) {
      continue;
    }
    cnetpp::base::StringPiece s(part);
    if (s.starts_with("hdfs://")) {
      std::shared_ptr<MountEntry> entry = ParseMountEntryString(s);
      if (!entry) {
        LOG(ERROR) << "Invalid mount entry string: " << s.as_string()
                   << ", skipped parsing mount table.";
        table->clear();
        return false;
      }
      table->emplace_back(entry);
      // TODO(yangjinfeng.02)
      if (!has_root && entry->mount_point() == "/") {
        has_root = true;
      }
    } else {
      // Treat the rest parts as a whole json string
      std::string rest;
      std::for_each(parts.begin() + i,
                    parts.end(),
                    [&rest] (const std::string& p) {
                      rest.append(p);
                      rest.append("\n");
                    });
      rest.resize(rest.length() - 1);  // trimmed last \n
      std::vector<std::shared_ptr<MountEntry>> entries;
      bool ok = ParseMountEntryJson(rest, &entries);
      if (!ok) {
        LOG(ERROR) << "Invalid mount entry string: " << rest
                   << ", skipped parsing mount table.";
        table->clear();
        return false;
      }
      std::for_each(entries.begin(),
                    entries.end(),
                    [&] (const std::shared_ptr<MountEntry>& e) {
                      table->emplace_back(e);
                      if (e && !has_root && e->mount_point() == "/") {
                        has_root = true;
                      }
                    });
      break;
    }
  }
  if (!has_root) {
    LOG(ERROR) << "root mount point is not found, "
               << "ignored invalid mount table: " << data.as_string();
    table->clear();
    return false;
  }
  return true;
}

std::shared_ptr<MountEntry> MountsManager::ParseMountEntryString(
    cnetpp::base::StringPiece mount) {
  auto cols = cnetpp::base::StringUtils::SplitByChars(mount, " ");
  if (cols.size() < 2) {
    return nullptr;
  }

  std::string& fs_uri = cols[0];
  std::string& mount_point = cols[1];
  auto entry = std::make_shared<MountEntry>();
  entry->set_fs_uri(fs_uri);
  entry->set_mount_point(mount_point);
  if (cols.size() > 2) {
    auto attrs = cnetpp::base::StringUtils::SplitByChars(cols[2], ",");
    entry->set_attributes(attrs);
  }
  return entry;
}

bool MountsManager::ParseMountEntryJson(const std::string& mount,
    std::vector<std::shared_ptr<MountEntry>>* entries) {
  cnetpp::base::Parser p;
  cnetpp::base::Value v;
  auto ok = p.Deserialize(mount, &v);
  if (!ok || !v.IsArray()) {
    return false;
  }
  auto a = v.AsArray();
  for (auto itr = a.Begin(); itr != a.End(); ++itr) {
    if (!itr->IsObject()) {
      return false;
    }

    auto entry = MountEntry::DeserializeFromJson(itr->AsObject());
    if (entry) {
      entries->emplace_back(entry);
    } else {
      return false;
    }
  }
  return true;
}

std::unique_ptr<MountsManager::MountLookupMapType>
MountsManager::BuildLookupMap(
    const std::vector<std::shared_ptr<MountEntry>>& entries) {
  auto lookup_map = std::make_unique<MountLookupMapType>();
  for (auto& entry : entries) {
    auto itr = lookup_map->find(entry->mount_point());
    if (itr == lookup_map->end()) {
      itr = lookup_map->emplace(entry->mount_point(),
          std::vector<std::shared_ptr<MountEntry>>()).first;
    }
    itr->second.emplace_back(entry);
  }
  lookup_map->emplace("", (*lookup_map)["/"]);
  return lookup_map;
}

void MountsManager::InstallMountTable(
    const std::vector<std::shared_ptr<MountEntry>>& entries,
    cnetpp::base::StringPiece data, int version) {
  std::unordered_set<std::string> all_fs;
  std::shared_ptr<MountEntry> root;
  for (auto& entry : entries) {
    if (entry->mount_point() == "/") {
      root = entry;
    }
    all_fs.emplace(entry->fs_uri());
  }
  CHECK_NOTNULL(root.get());
  auto mount_lookup_map = BuildLookupMap(entries);
  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    all_fs_ = std::move(all_fs);
    root_ = std::move(root);
    mount_lookup_map_.swap(mount_lookup_map);
    raw_mount_table_zk_version_ = version;
    raw_mount_table_ = data.as_string();
    LOG(INFO) << "Installed mount table: " << raw_mount_table_
              << ", version: " << raw_mount_table_zk_version_;
  }
  AfterInstall();
}

void MountsManager::AfterInstall() {
  installed_ = true;
  if (after_install_running_) {
    return;
  }

  after_install_running_ = true;
  workers_->AddTask([&] () {
    for (auto& cb : after_install_callbacks_) {
      cb();
    }
    after_install_running_ = false;
    return true;
  });
}

std::shared_ptr<MountEntry> MountsManager::ResolveSegmentedMountInternal(
    const std::string& parent, const std::string& path) const {
  auto itr = mount_lookup_map_->find(parent);
  if (itr == mount_lookup_map_->end()) {
    return nullptr;
  }

  std::vector<int> candidates;
  candidates.reserve(itr->second.size());
  for (int i = 0; i < itr->second.size(); ++i) {
    if (!itr->second[i]->HasFeature() ||
        !itr->second[i]->IsPathExcluded(path)) {
      candidates.push_back(i);
    }
  }

  for (auto i : candidates) {
    if (itr->second[i]->IsPathIncluded(path)) {
      return itr->second[i];
    }
  }

  for (auto i : candidates) {
    if (itr->second[i]->IsPathInRange(path)) {
      return itr->second[i];
    }
  }

  for (auto i : candidates) {
    if (itr->second[i]->HasIncludeFeature() ||
        itr->second[i]->HasRangeFeature()) {
      continue;
    }
    // Rules before should have filtered out the fit target.
    // If there are multiple candidates here, rules must be overlapped.
    // Just return the first one.
    return itr->second[i];
  }
  return nullptr;
}

cnetpp::base::Value MountsManager::DumpToJson() const {
  cnetpp::base::Object res;
  cnetpp::base::Array read_only_paths;
  cnetpp::base::Object mount_table;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  res["raw_read_only_paths"] = raw_read_only_paths_;
  res["raw_read_only_paths_zk_version"] = raw_read_only_paths_zk_version_;
  res["raw_mount_table"] = raw_mount_table_;
  res["raw_mount_table_zk_version"] = raw_mount_table_zk_version_;
  for (auto& p : *read_only_paths_) {
    read_only_paths.Append(cnetpp::base::Value(p));
  }
  res["read_only_paths"] = read_only_paths;
  for (auto& m : *mount_lookup_map_) {
    cnetpp::base::Array entries;
    for (auto& e : m.second) {
      entries.Append(e->SerializeToJson());
    }
    mount_table[m.first] = std::move(entries);
  }
  res["mount_table"] = std::move(mount_table);
  return cnetpp::base::Value(std::move(res));
}

}  // namespace dancenn

