// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_UPSTREAM_MANAGER_H_
#define PROXY_UPSTREAM_MANAGER_H_

#include <cnetpp/tcp/tcp_client.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/end_point.h>
#include <cnetpp/base/ip_address.h>
#include <cnetpp/base/string_piece.h>
#include <cnetpp/base/string_utils.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <shared_mutex>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#include "DatanodeProtocol.pb.h"  // NOLINT(build/include)
#include "ClientNamenodeProtocol.pb.h"  // NOLINT(build/include)
#include "base/file_utils.h"
#include "base/read_write_lock.h"
#include "rpc/failovered_rpc_channel.h"

DECLARE_string(danceproxy_namenodes_configuration_file);

namespace dancenn {

struct Upstream {
  std::atomic<int64_t> access_time_;
  std::shared_ptr<cloudfs::ClientNamenodeProtocol::Stub> stub_;
};

struct DNUpstream {
  std::atomic<int64_t> access_time_;
  std::shared_ptr<cloudfs::datanode::DatanodeProtocolService::Stub> stub_;
};

class UpstreamManager {
 public:
  UpstreamManager(std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
                  RpcClientOptions options,
                  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers);
  virtual ~UpstreamManager() = default;

  void Stop();

  std::shared_ptr<Upstream> GetUpstream(const std::string& user,
                                        const std::string& fs);

  std::vector<std::shared_ptr<Upstream>> GetAllUpstreams(
      const std::string& user);

  std::map<std::string, std::vector<cnetpp::base::EndPoint>> namenodes() const {
    std::shared_lock<ReadWriteLock> guard(rw_lock_);
    return cp_namenodes_;
  }

  std::shared_ptr<DNUpstream> GetDNUpstream(const std::string& user,
                                            const std::string& fs);

 private:
  std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client_;
  RpcClientOptions options_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_;

  mutable ReadWriteLock rw_lock_;
  bool stopped_ { false };
  // user -> {fs -> upstream}
  std::unordered_map<std::string,
      std::unordered_map<std::string, std::shared_ptr<Upstream>>> upstreams_;
  // user -> {fs -> dn_upstream}
  std::unordered_map<std::string,
      std::unordered_map<std::string,
                         std::shared_ptr<DNUpstream>>> dn_upstreams_;
  // fs -> endpoints for ClientProtocol
  std::map<std::string, std::vector<cnetpp::base::EndPoint>> cp_namenodes_;
  // fs -> endpoints for DatanodeProtocol
  std::map<std::string, std::vector<cnetpp::base::EndPoint>> dp_namenodes_;

  std::unique_ptr<cnetpp::concurrency::Thread> reloader_;
  bool ReloadNamenodesConfigurationFile();
  std::vector<cnetpp::base::EndPoint> ParseEndPoints(
      const std::string& protocol,
      const cnetpp::base::Array& endpoints);

  void CleanupIdleUpstreams();
  void CleanupIdleDNUpstreams();
};

}  // namespace dancenn

#endif  // PROXY_UPSTREAM_MANAGER_H_

