// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_STORAGE_POLICY_TTL_MANAGER_H_
#define PROXY_STORAGE_POLICY_TTL_MANAGER_H_

#include <cnetpp/base/csonpp.h>

#include <memory>
#include <string>
#include <unordered_map>

#include "proxy/abstract_condition_checker.h"

namespace dancenn {

// storagePolicyTTL:x -1: unlimited, 0: unknown, n(>0): n days
class StoragePolicyTTLManager : public AbstractConditionChecker {
 public:
  explicit StoragePolicyTTLManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : AbstractConditionChecker(upstream_manager, "StoragePolicyTTLManager") {
  }

  void Init() override;

  std::string GetStoragePolicyTTL(const std::string& path);

  cnetpp::base::Value DumpToJson() const override;

  mutable ReadWriteLock rw_lock_;
  std::unordered_map<std::string, int> ttls_;

  bool Refresh() override;
};

}  // namespace dancenn

#endif  // PROXY_STORAGE_POLICY_TTL_MANAGER_H_

