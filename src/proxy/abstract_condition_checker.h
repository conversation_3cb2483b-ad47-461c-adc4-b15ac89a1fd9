// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_ABSTRACT_CONDITION_CHECKER_H_
#define PROXY_ABSTRACT_CONDITION_CHECKER_H_

#include <cnetpp/base/csonpp.h>
#include <cnetpp/concurrency/thread.h>
#include <cnetpp/concurrency/task.h>
#include <glog/logging.h>

#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>

#include "base/metric.h"
#include "base/metrics.h"
#include "base/read_write_lock.h"
#include "proxy/upstream_manager.h"

namespace dancenn {

class AbstractConditionChecker {
 public:
  AbstractConditionChecker(std::shared_ptr<UpstreamManager> upstream_manager,
                           const std::string& metric_name)
      : upstream_manager_(upstream_manager) {
    metrics_ = MetricsCenter::Instance()->RegisterMetrics(metric_name);
  }

  virtual ~AbstractConditionChecker() {
    Stop();
  }

  virtual void Init() = 0;

  virtual void Stop() {
    DoStop();
  }

  virtual cnetpp::base::Value DumpToJson() const = 0;

 protected:
  std::shared_ptr<UpstreamManager> upstream_manager_;

  enum class Switch : int32_t {
    kOn,
    kOff,
    kDebug,
  };
  std::atomic<Switch> swch_ { Switch::kOff };
  bool enabled_ { true };

  std::shared_ptr<Metrics> metrics_;
  mutable ReadWriteLock metrics_rw_lock_;
  std::unordered_map<std::string, std::shared_ptr<AtomicCounter>> metric_set_;

  std::unique_ptr<cnetpp::concurrency::Thread> redis_refresher_;
  class RefreshTask : public cnetpp::concurrency::Task {
   public:
    explicit RefreshTask(AbstractConditionChecker *checker,
                         std::chrono::milliseconds refresh_interval)
        : refresh_interval_(refresh_interval),
          checker_(checker) {
      CHECK_NOTNULL(checker_);
    }

    bool operator()(void *args) override;

    void Stop() override {
      std::lock_guard<std::mutex> guard(mu_);
      stop_.store(true);
      cond_.notify_all();
    }

   private:
    std::mutex mu_;
    std::condition_variable cond_;
    std::chrono::milliseconds refresh_interval_;
    AbstractConditionChecker* checker_;
  };

  void DoInit(bool enabled, std::chrono::milliseconds refresh_interval);

  void DoStop();

  virtual bool Refresh() = 0;

  std::shared_ptr<AtomicCounter> GetMetrics(const std::string& metric_name);

  std::string ReadableSwitchType() const;
};

}  // namespace dancenn

#endif  // PROXY_ABSTRACT_CONDITION_CHECKER_H_

