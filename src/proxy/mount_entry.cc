// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/mount_entry.h"

namespace dancenn {

std::shared_ptr<MountEntry> MountEntry::DeserializeFromJson(
    const cnetpp::base::Object& object) {
  auto entry = std::make_shared<MountEntry>();

  auto fs_uri = object.Find("fsUri");
  if (fs_uri == object.End() || !fs_uri->second.IsString()) {
    return nullptr;
  }
  entry->set_fs_uri(fs_uri->second.AsString());

  auto mount_point = object.Find("mountPoint");
  if (mount_point == object.End() || !fs_uri->second.IsString()) {
    return nullptr;
  }
  entry->set_mount_point(mount_point->second.AsString());

  auto includes = object.Find("includes");
  if (includes != object.End()) {
    if (!includes->second.IsArray()) {
      return nullptr;
    }
    std::vector<std::string> include_fields;
    auto incs = includes->second.AsArray();
    for (auto i = incs.Begin(); i != incs.End(); ++i) {
      if (!i->IsString()) {
        return nullptr;
      }
      include_fields.emplace_back(i->AsString());
    }
    entry->set_includes(include_fields);
  }

  auto excludes = object.Find("excludes");
  if (excludes != object.End()) {
    if (!excludes->second.IsArray()) {
      return nullptr;
    }
    std::vector<std::string> exclude_fields;
    auto excs = excludes->second.AsArray();
    for (auto i = excs.Begin(); i != excs.End(); ++i) {
      if (!i->IsString()) {
        return nullptr;
      }
      exclude_fields.emplace_back(i->AsString());
    }
    entry->set_excludes(exclude_fields);
  }

  auto start = object.Find("start");
  if (start != object.End()) {
    if (!start->second.IsString()) {
      return nullptr;
    }
    entry->set_start(std::make_unique<std::string>(start->second.AsString()));
  }

  auto end = object.Find("end");
  if (end != object.End()) {
    if (!end->second.IsString()) {
      return nullptr;
    }
    entry->set_end(std::make_unique<std::string>(end->second.AsString()));
  }

  return entry;
}

bool MountEntry::IsPathExcluded(const std::string& path) const {
  if (exclude_regexes_.empty()) {
    return false;
  }

  for (auto& exclude : exclude_regexes_) {
    if (std::regex_match(path, exclude)) {
      return true;
    }
  }
  return false;
}

bool MountEntry::IsPathIncluded(const std::string& path) const {
  if (include_regexes_.empty()) {
    return false;
  }

  for (auto& include : include_regexes_) {
    if (std::regex_match(path, include)) {
      return true;
    }
  }
  return false;
}

bool MountEntry::IsPathInRange(const std::string& path) const {
  if (!start_.get() && !end_.get()) {
    return false;
  }

  if ((!start_.get() || start_->empty() || path.compare(*(start_)) >= 0) &&
      (!end_.get() || end_->empty() || path.compare((*end_)) < 0)) {
    return true;
  }
  return false;
}

}  // namespace dancenn

