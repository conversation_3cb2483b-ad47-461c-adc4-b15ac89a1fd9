// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "proxy/abstract_condition_checker.h"

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <shared_mutex>
#include <memory>
#include <string>
#include <vector>

#include "base/read_write_lock.h"
#include "base/metric.h"

namespace dancenn {

void AbstractConditionChecker::DoInit(
    bool enabled, std::chrono::milliseconds refresh_interval) {
  enabled_ = enabled;
  if (!enabled_) {
    return;
  }

  if (!Refresh()) {
    LOG(FATAL) << "Failed to refresh data.";
  }

  redis_refresher_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::static_pointer_cast<cnetpp::concurrency::Task>(
          std::make_shared<RefreshTask>(this, refresh_interval)));
  redis_refresher_->Start();
}

void AbstractConditionChecker::DoStop() {
  if (redis_refresher_) {
    redis_refresher_->Stop();
    redis_refresher_.reset();
  }
}

std::shared_ptr<AtomicCounter> AbstractConditionChecker::GetMetrics(
    const std::string& metric_name) {
  {
    std::shared_lock<ReadWriteLock> guard(metrics_rw_lock_);
    auto itr = metric_set_.find(metric_name);
    if (itr != metric_set_.end()) {
      return itr->second;
    }
  }
  {
    std::unique_lock<ReadWriteLock> guard(metrics_rw_lock_);
    auto itr = metric_set_.find(metric_name);
    if (itr != metric_set_.end()) {
      return itr->second;
    }
    return metric_set_.emplace(
        metric_name,
        metrics_->RegisterAtomicCounter(metric_name)).first->second;
  }
}

std::string AbstractConditionChecker::ReadableSwitchType() const {
  switch (swch_.load()) {
    case Switch::kDebug:
      return "debug";
    case Switch::kOn:
      return "on";
    case Switch::kOff:
      return "off";
    default:
      LOG(FATAL) << "never happen.";
  }
}

bool AbstractConditionChecker::RefreshTask::operator()(void* args) {
  (void) args;
  while (!stop_.load()) {
    checker_->Refresh();
    std::unique_lock<std::mutex> guard(mu_);
    cond_.wait_for(guard, refresh_interval_, [this] () -> bool {
      return stop_.load();
    });
  }
  return true;
}

}  // namespace dancenn

