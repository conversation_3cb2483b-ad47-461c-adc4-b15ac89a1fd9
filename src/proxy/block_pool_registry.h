// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef PROXY_BLOCK_POOL_REGISTRY_H_
#define PROXY_BLOCK_POOL_REGISTRY_H_

#include <memory>
#include <utility>
#include <string>
#include <unordered_map>

#include "base/read_write_lock.h"
#include "proxy/upstream_manager.h"

namespace dancenn {

// Provides blockPoolId to NameNode mapping.
// This is based on the assumption that blockPoolId assigned for one
// particular FS never changes.
class BlockPoolRegistry {
 public:
  explicit BlockPoolRegistry(
      std::shared_ptr<UpstreamManager> upstream_manager) {
    upstream_manager_ = std::move(upstream_manager);
  }

  void RefreshBlockPools();

  std::string GetFs(const std::string& bp_id);

 private:
  ReadWriteLock rw_lock_;
  std::unordered_map<std::string, std::string> bp_to_fs_;
  std::unordered_map<std::string, std::string> fs_to_bp_;

  std::shared_ptr<UpstreamManager> upstream_manager_;
};

}  // namespace dancenn

#endif  // PROXY_BLOCK_POOL_REGISTRY_H_

