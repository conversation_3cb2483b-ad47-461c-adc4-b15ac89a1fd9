// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "proxy/storage_policy_ttl_manager.h"

#include <gflags/gflags.h>

#include <utility>
#include <vector>
#include <chrono>

#include "base/redis_manager.h"
#include "base/path_util.h"

DECLARE_string(danceproxy_storage_policy_ttl_redis_backends);
DECLARE_int32(danceproxy_storage_policy_ttl_redis_timeout_ms);
DECLARE_int32(danceproxy_storage_policy_ttl_redis_refresh_interval_ms);
DECLARE_string(danceproxy_storage_policy_ttl_redis_key);
DECLARE_bool(danceproxy_storage_policy_ttl_enabled);

namespace dancenn {

void StoragePolicyTTLManager::Init() {
  DoInit(FLAGS_danceproxy_storage_policy_ttl_enabled,
         std::chrono::milliseconds(
             FLAGS_danceproxy_storage_policy_ttl_redis_refresh_interval_ms));
}

bool StoragePolicyTTLManager::Refresh() {
  LOG(INFO) << "Start to refresh storage policy ttl from Redis.";
  RedisManager redis;
  if (!redis.Init(FLAGS_danceproxy_storage_policy_ttl_redis_backends,
                  FLAGS_danceproxy_storage_policy_ttl_redis_timeout_ms)) {
    LOG(ERROR) << "Failed to initialize the storage policy ttl redis backends.";
    return false;
  }

  std::unordered_map<std::string, std::string> redis_result;
  std::unordered_map<std::string, int> ttls;
  if (!redis.RedisHGetAll(FLAGS_danceproxy_storage_policy_ttl_redis_key,
                          &redis_result)) {
    LOG(ERROR) << "Failed to get storage policy ttl from redis";
    return false;
  }
  for (auto& rr : redis_result) {
    std::string p = rr.first;
    RemoveRedundantSlash(&p);
    ttls.emplace(p, std::atoi(rr.second.c_str()));
  }

  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    this->ttls_.swap(ttls);
  }
  return true;
}

std::string StoragePolicyTTLManager::GetStoragePolicyTTL(
    const std::string &path) {
  if (!enabled_) {
    return "";
  }

  std::string normalized_path = path;
  RemoveRedundantSlash(&normalized_path);
  std::vector<cnetpp::base::StringPiece> ancestors;
  if (!GetAllAncestorPaths(normalized_path, &ancestors)) {
    return "";
  }
  CHECK_GT(normalized_path.size(), 0);

  auto ttl = 0;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  for (size_t i = ancestors.size() - 1; i != 0; i--) {
    auto itr = ttls_.find(ancestors[i].as_string());
    if (itr != ttls_.end()) {
      ttl = itr->second;
      break;
    }
  }
  if (ttl != 0) {
    return "storagePolicyTTL:" + std::to_string(ttl);
  }
  return "";
}

cnetpp::base::Value StoragePolicyTTLManager::DumpToJson() const {
  cnetpp::base::Object res;
  cnetpp::base::Object storage_policy_ttls;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  for (auto& ttl : ttls_) {
    storage_policy_ttls[ttl.first] = ttl.second;
  }
  res["storage_policy_ttls"] = storage_policy_ttls;
  return cnetpp::base::Value(std::move(res));
}

}  // namespace dancenn

