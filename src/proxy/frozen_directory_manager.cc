// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/frozen_directory_manager.h"

#include <gflags/gflags.h>

#include <utility>
#include <vector>

#include "base/redis_manager.h"
#include "base/path_util.h"
#include "proxy/quota_or_usage.h"
#include "proxy/upstream_manager.h"
#include "base/java_exceptions.h"

DECLARE_string(danceproxy_frozen_directory_redis_backends);
DECLARE_int32(danceproxy_frozen_directory_redis_timeout_ms);
DECLARE_int32(danceproxy_frozen_directory_redis_refresh_interval_ms);
DECLARE_string(danceproxy_frozen_directory_redis_frozen_directories_key);
DECLARE_string(danceproxy_frozen_directory_redis_frozen_directory_key);
DECLARE_string(danceproxy_frozen_directory_redis_switch_key);
DECLARE_bool(danceproxy_frozen_directory_enabled);

namespace dancenn {

void FrozenDirectoryManager::Init() {
  DoInit(FLAGS_danceproxy_frozen_directory_enabled,
         std::chrono::milliseconds(
             FLAGS_danceproxy_frozen_directory_redis_refresh_interval_ms));
}

bool FrozenDirectoryManager::Refresh() {
  LOG(INFO) << "Start to refresh frozen directories from Redis.";
  auto namenodes = upstream_manager_->namenodes();
  std::vector<std::string> nsids;
  for (auto& nn : namenodes) {
    if (cnetpp::base::StringPiece(nn.first).starts_with("hdfs://")) {
      nsids.emplace_back(nn.first.substr(7));
    }
  }
  RedisManager redis;
  if (!redis.Init(FLAGS_danceproxy_frozen_directory_redis_backends,
                  FLAGS_danceproxy_frozen_directory_redis_timeout_ms)) {
    LOG(ERROR) << "Failed to initialize the quota redis backends.";
    return false;
  }

  std::unordered_map<std::string,
      std::unordered_map<std::string,
          std::unordered_set<std::string>>> new_fds;
  for (auto& ns : nsids) {
    std::unordered_set<std::string> fds_each_ns;
    if (!redis.RedisSMembers(
        ns + "#" + FLAGS_danceproxy_frozen_directory_redis_frozen_directories_key,  // NOLINT(whitespace/line_length)
        &fds_each_ns)) {
      LOG(ERROR) << "Failed to get frozen directories of "
                 << ns << " from redis";
      return false;
    }
    for (auto& dir : fds_each_ns) {
      std::unordered_set<std::string> sub_dirs;
      if (!redis.RedisSMembers(
          ns + "#" + FLAGS_danceproxy_frozen_directory_redis_frozen_directory_key + "://" + dir,  // NOLINT(whitespace/line_length)
          &sub_dirs)) {
        LOG(ERROR) << "Failed to get sub directories for path " << dir
                   << " of " << ns  << " from redis";
        return false;
      }
      auto itr = new_fds.find("hdfs://" + ns);
      if (itr == new_fds.end()) {
        itr = new_fds.emplace(
            "hdfs://" + ns, std::unordered_map<std::string, std::unordered_set<std::string>>()).first;  // NOLINT(whitespace/line_length)
      }
      itr->second.emplace(dir, sub_dirs);
    }
  }

  // get switch key
  std::string swch;
  if (!redis.RedisGet(FLAGS_danceproxy_frozen_directory_redis_switch_key,
                      &swch)) {
    LOG(ERROR) << "Failed to get frozen directory switch from redis.";
    return false;
  }

  {
    std::unique_lock<ReadWriteLock> guard(rw_lock_);
    this->frozen_directories_.swap(new_fds);
    if (cnetpp::base::StringPiece(swch).ignore_case_equal("on")) {
      this->swch_ = Switch::kOn;
    } else if (cnetpp::base::StringPiece(swch).ignore_case_equal("off")) {
      this->swch_ = Switch::kOff;
    } else {
      this->swch_ = Switch::kDebug;
    }
  }
  return true;
}

std::tuple<bool, const char*, std::string>
FrozenDirectoryManager::IsFrozen(const std::string &ns,
                                 const std::string &path,
                                 bool is_trash) {
  auto res = std::make_tuple<bool, const char*, std::string>(
      false, nullptr, "");
  if (!enabled_ || swch_ == Switch::kOff || is_trash) {
    return res;
  }

  std::shared_lock<ReadWriteLock> guard(rw_lock_);

  auto itr = frozen_directories_.find(ns);
  if (itr == frozen_directories_.end() || itr->second.empty()) {
    return res;
  }

  std::string formalized_path = path;
  RemoveRedundantSlash(&formalized_path);

  if (formalized_path == "/") {
    if (swch_ == Switch::kOn) {
      return std::make_tuple<bool, const char *, std::string>(
          true,
          JavaExceptions::NSQuotaExceededException(),
          "The directory / is frozen, please change to another directory. "
              "For more information, please refer to "
              "https://wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
              "or contact administrators.");
    } else {
      LOG(INFO) << "Frozen directory: /, path: " << path;
      return res;
    }
  }

  if (formalized_path.empty() || formalized_path[0] != '/') {
    return std::make_tuple<bool, const char*, std::string>(
        true,
        JavaExceptions::IllegalArgumentException(),
        "Relative path found: " + path);
  }

  std::vector<cnetpp::base::StringPiece> ancestors;
  if (!GetAllAncestorPaths(formalized_path, &ancestors)) {
    return std::make_tuple<bool, const char *, std::string>(
        true,
        JavaExceptions::IllegalArgumentException(),
        "Invalid path: " + path);
  }

  CHECK_GE(ancestors.size(), 2);

  for (auto i = static_cast<int>(ancestors.size() - 2); i >= 0; --i) {
    auto ancestor = ancestors[i].as_string();
    auto itr1 = itr->second.find(ancestor);
    if (itr1 == itr->second.end()) {
      continue;
    }
    size_t pos = ancestors[i].size() + (i == 0 ? 0 : 1);
    size_t len =
        ancestors[i + 1].size() - ancestors[i].size() - (i == 0 ? 0 : 1);
    std::string sub_dir = formalized_path.substr(pos, len);
    auto itr2 = itr1->second.find(sub_dir);
    if (itr2 == itr1->second.end()) {
      if (swch_ == Switch::kOn) {
        return std::make_tuple<bool, const char *, std::string>(
            true,
            JavaExceptions::NSQuotaExceededException(),
            "The directory " + ancestor + " is frozen, please change to "
                "another directory. For more information, please refer to "
                "https://wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                "or contact administrators.");
      } else {
        LOG(INFO) << "Frozen directory " << ancestor << ", path: " << path;
        return res;
      }
    } else {
      return res;
    }
  }
  return res;
}

cnetpp::base::Value FrozenDirectoryManager::DumpToJson() const {
  cnetpp::base::Object res;
  cnetpp::base::Object frozen_directories;
  std::shared_lock<ReadWriteLock> guard(rw_lock_);
  res["switch"] = ReadableSwitchType();
  for (auto& fd : frozen_directories_) {
    cnetpp::base::Object rels;
    for (auto dir : fd.second) {
      cnetpp::base::Array sub_dirs;
      for (auto sub_dir : dir.second) {
        sub_dirs.Append(cnetpp::base::Value(sub_dir));
      }
      rels[dir.first] = sub_dirs;
    }
    frozen_directories[fd.first] = rels;
  }
  res["frozen_directories"] = frozen_directories;
  return cnetpp::base::Value(std::move(res));
}

}  // namespace dancenn

