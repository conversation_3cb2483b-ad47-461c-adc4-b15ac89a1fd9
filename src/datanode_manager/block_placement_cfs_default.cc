// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/06
// Description

#include "datanode_manager/block_placement_cfs_default.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <algorithm>
#include <cstring>
#include <shared_mutex>  // NOLINT
#include <utility>

#include "base/block_event_logger.h"
#include "base/rand_number.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"

DECLARE_int32(datanode_tolerate_interval_misses_sec);
DECLARE_bool(avoid_stale_datanode_for_write);
DECLARE_int32(blockmap_num_slice);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(block_read_cache_refresh_interval_s);
DECLARE_int32(block_read_cache_not_expired_interval_s);
DECLARE_double(block_read_cache_geo_dist_p);
DECLARE_string(block_placement_distribution_type);
DECLARE_bool(placement_ignore_local_az);
DECLARE_bool(placement_ignore_local_host);
DECLARE_bool(placement_ignore_existed_switch);
DECLARE_bool(placement_ignore_existed_host);
DECLARE_bool(block_placement_local_dn_first);
DECLARE_bool(choose_target_to_delete_by_weight);
DECLARE_bool(block_read_cache_skip_detail_check);
DECLARE_bool(log_choose_target);
DECLARE_bool(choose_target_shuffle_candidate);

namespace dancenn {
namespace {

const std::size_t BEST_BUCKETS[] = {49921, 32771, 5519, 1447, 233, 23, 7, 3, 0};
inline std::size_t ChooseBestBuckets(std::size_t n) {
  std::size_t x = BEST_BUCKETS[0];
  for (int i = 0; BEST_BUCKETS[i] != 0 && n <= BEST_BUCKETS[i]; i++) {
    x = BEST_BUCKETS[i];
  }
  return x;
}

inline enum BPDistributionType GetDistributionType(const std::string& type) {
  if (type == "round-robin") {
    return BPDistributionType::RoundRobin;
  }
  if (type == "uniform") {
    return BPDistributionType::Uniform;
  }
  if (type == "geometric") {
    return BPDistributionType::Geometric;
  }
  if (type == "weight") {
    return BPDistributionType::Weight;
  }
  LOG(WARNING) << "Unknown BP distribution type, use geometric distribution";
  return BPDistributionType::Geometric;
}

}  // namespace

void BlockReadCache::Clear() {
  size = 0;
  std::memset(dns, 0, sizeof(DatanodeInfoPtr) * kCapacity);
}

bool BlockReadCache::Push(DatanodeInfoPtr dn) {
  CHECK_NOTNULL(dn);
  if (size >= kCapacity) {
    return false;
  }
  for (std::size_t i = 0; i < size; i++) {
    if (dn->id() == dns[i]->id()) {
      return false;
    }
  }
  dns[size] = dn;
  size++;
  return true;
}

void BlockReadCache::Sort(const std::vector<DatanodeID>& dns_with_replica) {
  struct FixedDatanodeInfo {
    DatanodeInfoPtr dn;
    bool has_replica;
    uint64_t temp_based_remaining;
    uint64_t score;
  };

  std::vector<FixedDatanodeInfo> fixed_dns;
  for (std::size_t i = 0; i < size; i++) {
    FixedDatanodeInfo fixed_dn;
    CHECK_NOTNULL(dns[i]);
    fixed_dn.dn = dns[i];
    fixed_dn.has_replica = std::find(dns_with_replica.begin(),
                                     dns_with_replica.end(),
                                     dns[i]->id()) != dns_with_replica.end();
    fixed_dn.temp_based_remaining = dns[i]->stat().temp_based_remaining;
    // https://bytedance.feishu.cn/docs/doccntYD0QAfVV02yvSoX9b1Zth
    // Reading one large block (e.g. 128M) frequently cause dn busy.
    // So we need to let client access different dns with cache.
    fixed_dn.score = std::hash<uint64_t>()(
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::time_point_cast<std::chrono::milliseconds>(
                dns[i]->last_heartbeat())
                .time_since_epoch())
            .count() -
        fixed_dn.temp_based_remaining);
    fixed_dns.push_back(fixed_dn);
  }
  // TODO(zhuangsiyu/ruanjunbin): Update sort algorithm
  std::stable_sort(
      fixed_dns.begin(),
      fixed_dns.end(),
      [](const FixedDatanodeInfo& lhs, const FixedDatanodeInfo& rhs) {
        if (lhs.has_replica && rhs.has_replica) {
          return lhs.score > rhs.score;
        }
        if (!lhs.has_replica && !rhs.has_replica) {
          return lhs.temp_based_remaining > rhs.temp_based_remaining;
        }
        return lhs.has_replica > rhs.has_replica;
      });
  Clear();
  for (const FixedDatanodeInfo& fixed_dn : fixed_dns) {
    if (!Push(fixed_dn.dn)) {
      // TODO(ruanjunbin): Log.
    }
  }
}

BlockReadCacheSlice::BlockReadCacheSlice()
    : num_buckets_(ChooseBestBuckets(FLAGS_blockmap_num_bucket_each_slice)) {
  buckets_.resize(num_buckets_);
}

BlockReadCacheSlice::~BlockReadCacheSlice() {
  RemoveBlocksWithCond([](const BlockReadCache& ignored) { return true; });
}

void BlockReadCacheSlice::lock() {
  lock_.lock();
}

void BlockReadCacheSlice::unlock() {
  lock_.unlock();
}

BlockReadCache* BlockReadCacheSlice::GetOrCreateBlock(BlockID blk_id) {
  std::size_t bucket_id = BlockIdBucket(blk_id);
  BlockReadCache* cur = buckets_[bucket_id];
  while (cur) {
    if (cur->blk_id == blk_id) {
      return cur;
    }
    cur = cur->next;
  }

  BlockReadCache* cache = new BlockReadCache;
  cache->blk_id = blk_id;
  cache->prev = nullptr;
  cache->next = buckets_[bucket_id];
  if (buckets_[bucket_id] != nullptr) {
    buckets_[bucket_id]->prev = cache;
  }
  buckets_[bucket_id] = cache;
  return cache;
}

void BlockReadCacheSlice::RemoveExpiredBlocks(int64_t currentTsInSec) {
  int64_t expireTsInSec =
      currentTsInSec - FLAGS_block_read_cache_not_expired_interval_s;
  RemoveBlocksWithCond([expireTsInSec](const BlockReadCache& cache) {
    return cache.refresh_ts <= expireTsInSec;
  });
}

std::vector<BlockID> BlockReadCacheSlice::GetDebugInfo(std::size_t bucket_id) {
  CHECK_LT(bucket_id, num_buckets_);
  BlockReadCache* cur = buckets_[bucket_id];
  std::vector<BlockID> block_ids;
  while (cur != nullptr) {
    if (cur->prev != nullptr) {
      CHECK_EQ(cur->prev->next, cur);
    }
    if (cur->next != nullptr) {
      CHECK_EQ(cur->next->prev, cur);
    }
    block_ids.push_back(cur->blk_id);
    cur = cur->next;
  }
  return block_ids;
}

std::size_t BlockReadCacheSlice::BlockIdBucket(BlockID blk_id) {
  return blk_id % num_buckets_;
}

void BlockReadCacheSlice::RemoveBlocksWithCond(
    const std::function<bool(const BlockReadCache& cache)>& cond) {
  for (std::size_t i = 0; i < buckets_.size(); i++) {
    BlockReadCache* cache = buckets_[i];
    while (cache != nullptr) {
      if (!cond(*cache)) {
        cache = cache->next;
        continue;
      }

      if (cache->prev != nullptr) {
        cache->prev->next = cache->next;
      } else {
        buckets_[i] = cache->next;
      }
      if (cache->next != nullptr) {
        cache->next->prev = cache->prev;
      }
      BlockReadCache* t = cache;
      cache = cache->next;
      delete t;
    }
  }
}

ChooseTargetPerCpuContextCfsDefault::ChooseTargetPerCpuContextCfsDefault()
    : geometric_dist_(FLAGS_block_read_cache_geo_dist_p) {
}

void ChooseTargetPerCpuContextCfsDefault::Init() {
  round_robin_id_ = 0;
  rand_.seed(std::chrono::system_clock::now().time_since_epoch().count());
}

void ChooseTargetPerCpuContextCfsDefault::Refresh(
    const std::vector<DatanodeInfoPtr>& dns) {
  if (GetDistributionType(FLAGS_block_placement_distribution_type) !=
      BPDistributionType::Weight) {
    return;
  }
  total_nodes_ = 0;
  total_weight_ = 0;
  prefix_weight_sum_.clear();

  for (auto dn : dns) {
    auto w = dn->GetWriteableWeight();

    uint32_t last = prefix_weight_sum_.empty() ? 0 : prefix_weight_sum_.back();

    total_nodes_++;
    total_weight_ += w;
    prefix_weight_sum_.push_back(last + w);
  }

  CHECK(dns.size() == total_nodes_);
  CHECK(prefix_weight_sum_.size() == total_nodes_);
  CHECK(prefix_weight_sum_.back() == total_weight_);

  if (VLOG_IS_ON(20)) {
    LOG(INFO) << "total_nodes_=" << total_nodes_;
    LOG(INFO) << "total_weight_=" << total_weight_;
    for (auto ws : prefix_weight_sum_) {
      LOG(INFO) << ws;
    }
  }
}

std::size_t ChooseTargetPerCpuContextCfsDefault::GetGeometircDistIndex(
    std::size_t size) {
  CHECK_GT(size, 0);
  std::size_t i;
  do {
    i = geometric_dist_(rand_);
  } while (i >= size);
  return i;
}

std::size_t ChooseTargetPerCpuContextCfsDefault::GetUniformDistIndex(
    std::size_t size) {
  CHECK_GT(size, 0);
  return uniform_dist_(rand_) % size;
}

std::size_t ChooseTargetPerCpuContextCfsDefault::GetRoundRobinIndex(
    std::size_t size) {
  CHECK_GT(size, 0);
  return round_robin_id_.fetch_add(1) % size;
}

std::size_t ChooseTargetPerCpuContextCfsDefault::GetWeightedSelectIndex() {
  if (total_weight_ == 0) {
    return 0;
  }
  size_t weight = (uniform_dist_(rand_) % total_weight_) + 1;
  return std::lower_bound(
             prefix_weight_sum_.begin(), prefix_weight_sum_.end(), weight) -
         prefix_weight_sum_.begin();
}

BlockPlacementCfsDefault::BlockPlacementCfsDefault(
    DatanodeManagerMetrics* metrics,
    bool with_refresher)
    : BlockPlacement(false),
      metrics_(metrics),
      // NOTICE: Please call constructor of tbr_topk_dns_lock_
      // before starting refresher_.
      tbr_topk_dns_lock_(),
      num_slices_(static_cast<std::size_t>(FLAGS_blockmap_num_slice)) {
  if (with_refresher) {
    refresher_ = std::make_unique<ConfigRefresher>(this);
  }
  slice_mask_ = num_slices_ - 1;
  CHECK_EQ(slice_mask_ & num_slices_, 0)
      << "FLAGS_blockmap_num_slice must be power of 2";
  for (std::size_t i = 0; i < num_slices_; i++) {
    slices_.emplace_back(new BlockReadCacheSlice());
  }
  choose_target_ctx_.Each(
      [](ChooseTargetPerCpuContextCfsDefault* ctx) { ctx->Init(); });
}

BlockPlacementCfsDefault::~BlockPlacementCfsDefault() {}

void BlockPlacementCfsDefault::AddDatanode(DatanodeInfoPtr dn) {
  CHECK_NOTNULL(dn);
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  datanodes_.insert(dn);
  id_to_dn_map_[dn->id()] = dn;
}

void BlockPlacementCfsDefault::RemoveDatanode(DatanodeInfoPtr dn) {
  CHECK_NOTNULL(dn);
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  datanodes_.erase(dn);
  id_to_dn_map_.erase(dn->id());
}

bool BlockPlacementCfsDefault::ChooseTarget4New(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GE(rep_num, 0);
  std::unordered_set<DatanodeInfoPtr> included;
  return ChooseTargetInternal(rep_num,
                              advice,
                              client_location,
                              favored_nodes,
                              included,
                              excluded,
                              result);
}

bool BlockPlacementCfsDefault::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  return ChooseTargetInternal(rep_num,
                              advice,
                              client_location,
                              favored_nodes,
                              included,
                              excluded,
                              result);
}

bool BlockPlacementCfsDefault::ChooseTarget4Read(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GE(rep_num, 0);
  CHECK_NOTNULL(excluded);
  CHECK_NOTNULL(result);

  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "ChooseTarget4Read"
      << " block_id=" << detailed_block.blk_.id
      << " advice=" << advice.ToString()
      << " location=" << client_location.ToString();

  StopWatch sw(metrics_->block_placement_choose_target_for_read_recheck_);
  sw.Start();

  // detail block
  for (DatanodeID dn_id : detailed_block.storage_) {
    auto dn = GetDatanodeInfo(dn_id);

    // skip outdated DN
    if (dn == nullptr || dn->IsStale() || dn->IsDecommissionInProgress()) {
      continue;
    }

    // skip DN does not meet minimum version
    if (advice.version_checker != nullptr) {
      if (!advice.version_checker(dn->version())) {
        VLOG_OR_IF(8, FLAGS_log_choose_target)
            << "Skip DN " << dn->id() << " for version " << dn->version();
        continue;
      }
    }

    result->push_back(dn);
    excluded->insert(dn);
  }

  // prefer
  if (advice.policy.has_read_switch_policy()) {
    switch (advice.policy.read_switch_policy()) {
      case ReadPolicy::PREFER_LOCAL: {
        ChooseTarget4ReadPreferLocal(detailed_block,
                                     rep_num,
                                     advice,
                                     client_location,
                                     favored_nodes,
                                     excluded,
                                     result);
        break;
      }
      case ReadPolicy::PREFER_CACHED: {
        ChooseTarget4ReadPreferCached(detailed_block,
                                      rep_num,
                                      advice,
                                      client_location,
                                      favored_nodes,
                                      excluded,
                                      result);
        break;
      }
      default:
        LOG(FATAL) << "unreached code";
    }
  } else {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "policy has no read switch policy";
  }

  // cache
  if (result->size() < rep_num) {
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "ChooseTarget4ReadByCache";

    std::unordered_set<DatanodeInfoPtr> excluded_2(result->begin(),
                                                   result->end());

    ChooseTarget4ReadByCache(detailed_block,
                             rep_num - result->size(),
                             advice,
                             client_location,
                             favored_nodes,
                             &excluded_2,
                             result);
  }

  return result->size() >= rep_num;
}

bool BlockPlacementCfsDefault::ChooseTarget4ReadPreferLocal(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  if (rep_num == 0) {
    return true;
  }
  VLOG_OR_IF(10, FLAGS_log_choose_target) << "policy is PREFER_LOCAL";

  // no rdma_tag and location_tag should skip this function
  do {
    if (!client_location.rdma_tag.empty()) {
      break;
    } else {
      VLOG_OR_IF(10, FLAGS_log_choose_target) << "rdmaTag is empty";
    }
    if (client_location.location_tag.has_az() &&
        !client_location.location_tag.az().empty()) {
      break;
    } else {
      VLOG_OR_IF(10, FLAGS_log_choose_target) << "az is empty";
    }

    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "skip ChooseTarget4ReadPreferLocal";
    return false;
  } while (0);

  int not_applicable_cnt = 0;
  std::vector<DatanodeInfoPtr> local_az_dns;
  std::vector<DatanodeInfoPtr> extra_dns;

  std::vector<DatanodeInfoPtr> local_dns;
  std::vector<DatanodeInfoPtr> remote_dns;
  for (auto dn : *result) {
    bool is_local_az = dn->IsLocalAz(client_location.location_tag);

    bool applicable = false;
    bool is_same_switch = false;
    std::string dn_switch;
    std::tie(applicable, is_same_switch, dn_switch) = dn->IsInSameSwitch(
        client_location.location_tag, client_location.rdma_tag);

    if (!applicable) {
      not_applicable_cnt++;
      remote_dns.push_back(dn);
      continue;
    }
    if (is_same_switch) {
      local_dns.push_back(dn);
    } else {
      remote_dns.push_back(dn);
    }
    if (is_local_az) {
      local_az_dns.push_back(dn);
    }
  }

  if (not_applicable_cnt == result->size()) {
    // no dn applicable, maybe cluster not set rdma_tag
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "rdmaTag: no dn applicable";
  }

  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "local_az_dns=" << local_az_dns.size()
      << "local_dns=" << local_dns.size()
      << " remote_dns=" << remote_dns.size();
  if (FLAGS_placement_ignore_local_az == false) {
    if (local_az_dns.size() < rep_num) {
      PlacementAdvice write_advice;
      write_advice.geograph = kCentralizePolicy;
      write_advice.local_switch_target = rep_num;
      write_advice.other_switch_target = 1;

      ChooseTarget4New(/*ignored*/ "",
                       rep_num - local_az_dns.size(),
                       /*ignored*/ 0,
                       write_advice,
                       client_location,
                       favored_nodes,
                       excluded,
                       &extra_dns);
      if (extra_dns.empty()) {
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "ChooseTarget4Read, choose local az failed";
      } else {
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "ChooseTarget4Read, choose local az success";
      }
    }
  }
  if (FLAGS_placement_ignore_existed_switch == false) {
    if (local_dns.size() < rep_num) {
      PlacementAdvice write_advice;
      write_advice.geograph = kCentralizePolicy;
      write_advice.local_switch_target = rep_num;
      write_advice.other_switch_target = 1;

      ChooseTarget4New(/*ignored*/ "",
                       rep_num - local_dns.size(),
                       /*ignored*/ 0,
                       write_advice,
                       client_location,
                       favored_nodes,
                       excluded,
                       &local_dns);
      if (local_dns.empty()) {
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "ChooseTarget4Read, choose local dns failed";
      } else {
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "ChooseTarget4Read, choose local dns success";
      }
    }
  }

  result->clear();
  for (auto dn : extra_dns) {
    result->push_back(dn);
  }
  for (auto dn : local_dns) {
    result->push_back(dn);
  }
  for (auto dn : remote_dns) {
    result->push_back(dn);
  }

  return true;
}

bool BlockPlacementCfsDefault::ChooseTarget4ReadPreferCached(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  if (rep_num == 0) {
    return true;
  }
  VLOG_OR_IF(10, FLAGS_log_choose_target) << "policy is PREFER_CACHED";
  if (result->size() >= rep_num) {
    return true;
  }

  PlacementAdvice write_advice;
  write_advice.local_switch_target = 1;
  write_advice.other_switch_target = 0;

  ChooseTarget4New(/*ignored*/ "",
                   /*rep_num=*/1,
                   /*ignored*/ 0,
                   write_advice,
                   client_location,
                   favored_nodes,
                   excluded,
                   result);
  return true;
}

bool BlockPlacementCfsDefault::ChooseTarget4ReadByCache(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GE(rep_num, 0);
  CHECK_NOTNULL(excluded);
  CHECK_NOTNULL(result);

  StopWatch sw;
  sw.Start();
  sw.NextStep(metrics_->block_placement_choose_target_for_read_get_slice_time_);
  BlockID blk_id = detailed_block.blk_.id;
  BlockReadCacheSlice* s = Slice(blk_id);
  CHECK_NOTNULL(s);

  sw.NextStep(
      metrics_
          ->block_placement_choose_target_for_read_get_block_read_cache_time_);
  std::unique_lock<BlockReadCacheSlice> guard(*s);
  BlockReadCache* cache = s->GetOrCreateBlock(blk_id);
  CHECK_NOTNULL(cache);

  sw.NextStep(
      metrics_
          ->block_placement_choose_target_for_read_refresh_block_read_cache_time_);
  RefreshBlockReadCache(detailed_block, advice, cache);
  sw.NextStep();

  switch (GetDistributionType(FLAGS_block_placement_distribution_type)) {
    case BPDistributionType::Geometric:
    case BPDistributionType::Uniform:
    case BPDistributionType::RoundRobin:
      cache->Sort(detailed_block.storage_);
      break;
    default:
      break;
  }

  std::unique_ptr<std::vector<DatanodeInfoPtr>,
                  std::function<void(std::vector<DatanodeInfoPtr>*)>>
      log_guard(result, [blk_id](std::vector<DatanodeInfoPtr>* result) {
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "ChooseTarget4ReadByCache of B" << blk_id << " returns "
            << result->size() << " elements";
      });
  for (std::size_t i = 0; i < cache->size; i++) {
    if (excluded->count(cache->dns[i]) != 0) {
      continue;
    }

    result->push_back(cache->dns[i]);
    rep_num--;
    if (rep_num == 0) {
      return true;
    }
  }
  return false;
}

void BlockPlacementCfsDefault::ChooseFunctionalTarget4Read(
    const DetailedBlock& detailed_block,
    const ReadAdvice& advice,
    std::vector<DatanodeInfoPtr>* result) {
  for (DatanodeID dn_id : detailed_block.storage_) {
    auto dn = GetDatanodeInfo(dn_id);

    // skip outdated DN
    if (dn == nullptr || dn->IsStale2()) {
      continue;
    }

    // skip DN does not meet minimum version
    if (advice.version_checker != nullptr) {
      if (!advice.version_checker(dn->version())) {
        VLOG_OR_IF(8, FLAGS_log_choose_target)
            << "Skip DN " << dn->id() << " for version " << dn->version();
        continue;
      }
    }

    result->push_back(dn);
  }
}

DatanodeInfoPtr BlockPlacementCfsDefault::ChooseReplicaToDelete(
    const Block& blk,
    std::size_t replication,
    const std::unordered_set<DatanodeInfoPtr>& more_than_one,
    const std::unordered_set<DatanodeInfoPtr>& exactly_one,
    const PlacementAdvice& advice) {
  DatanodeInfoPtr dn = ChooseReplicaToDelete(more_than_one);
  if (dn) {
    return dn;
  }
  return ChooseReplicaToDelete(exactly_one);
}

DatanodeInfoPtr BlockPlacementCfsDefault::ChooseReplicaToDelete(
    const std::unordered_set<DatanodeInfoPtr>& replicas) {
  // TODO(ruanjunbin): Replace now.
  // 1. last heartbeat older than threshold
  std::chrono::time_point<std::chrono::system_clock> oldest_heartbeat =
      std::chrono::system_clock::now() -
      std::chrono::milliseconds(FLAGS_datanode_tolerate_interval_misses_sec *
                                1000);
  DatanodeInfoPtr oldest_dn = nullptr;
  for (auto replica_dn : replicas) {
    if (replica_dn->last_heartbeat() < oldest_heartbeat) {
      oldest_dn = replica_dn;
      oldest_heartbeat = replica_dn->last_heartbeat();
    }
  }
  if (oldest_dn != nullptr) {
    return oldest_dn;
  }

  // 2. sample by remain_space
  if (FLAGS_choose_target_to_delete_by_weight) {
    // see <<Weighted random sampling with a reservoir>>
    DatanodeInfoPtr chosen_dn = nullptr;
    double k = 0;
    for (auto replica_dn : replicas) {
      // The smaller remain, the higher the weight
      // w = 1/remain
      // k = r^(1/w) = r^remain
      auto remain = replica_dn->GetWriteableWeightByTmpRemain();
      auto r = RandNumber::Instance()->NextDouble();
      auto my_k = std::pow(r, remain);
      k = my_k;

      if (chosen_dn == nullptr || my_k > k) {
        chosen_dn = replica_dn;
        k = my_k;
        continue;
      }
    }
    return chosen_dn;
  } else {
    DatanodeInfoPtr min_space_dn = nullptr;
    for (auto replica_dn : replicas) {
      if (min_space_dn == nullptr ||
          replica_dn->stat().temp_based_remaining <
              min_space_dn->stat().temp_based_remaining) {
        min_space_dn = replica_dn;
      }
    }
    return min_space_dn;
  }
}

std::unordered_map<std::string, int> BlockPlacementCfsDefault::
    GetExpectedPlacement(const PlacementAdvice& advice, int32_t rep_num) {
  return {{"", rep_num}};
}

bool BlockPlacementCfsDefault::IsLocal(DatanodeInfoPtr dn,
                                       const NetworkLocation& loc,
                                       bool* same_rack,
                                       bool* same_dc,
                                       uint32_t* dc_distance) {
  CHECK_NOTNULL(same_rack);
  CHECK_NOTNULL(same_dc);
  CHECK_NOTNULL(dc_distance);
  *same_dc = true;
  *dc_distance = 0;
  *same_rack = (loc.rack == dn->rack());
  return true;
}

bool BlockPlacementCfsDefault::DatanodeExist(DatanodeInfoPtr dn) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  return datanodes_.find(dn) != datanodes_.end();
}

bool BlockPlacementCfsDefault::HasBeenMultiRack() {
  return false;
}

std::size_t BlockPlacementCfsDefault::NumRacks() {
  return 1;
}

// TODO(ruanjunbin): Is dummy implement okay?
void BlockPlacementCfsDefault::ListAll(std::string* output) {
  CHECK_NOTNULL(output);
  output->clear();
}

void BlockPlacementCfsDefault::GetAllDatanodesInRack(
    const std::string& dc,
    const std::string& rack,
    std::unordered_set<DatanodeInfoPtr>* dns) {
  CHECK_NOTNULL(dns);
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  for (DatanodeInfoPtr dn : datanodes_) {
    if (dn->rack() == rack) {
      dns->emplace(dn);
    }
  }
}

void BlockPlacementCfsDefault::GetAllDatanodeInfo(
    std::vector<DatanodeInfoPtr>* dns) {
  CHECK_NOTNULL(dns);
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  dns->insert(dns->end(), datanodes_.begin(), datanodes_.end());
}

bool BlockPlacementCfsDefault::RefreshConfig() {
  // ConfigRefresher calls me every 10 seconds.
  int64_t currentTsInSec = GetCurrentTsInMs() / 1000;
  for (const auto& slice : slices_) {
    CHECK_NOTNULL(slice.get());
    std::unique_lock<BlockReadCacheSlice> lock(*slice);
    slice->RemoveExpiredBlocks(currentTsInSec);
  }
  RecalcAllDatanode();
  return true;
}

void BlockPlacementCfsDefault::RecalcAllDatanode() {
  // TODO(ruanjunbin): Segmentation fault happens randomly when release
  // tbr_lock. __GI___pthread_rwlock_unlock (rwlock=0x7ffff4f61d50) at
  // pthread_rwlock_unlock.c:38
  // tbr_topk_dns_lock_ is a zero-filled object.
  // std::shared_timed_mutex (ReadWriteLock) inherit __shared_mutex_pthread.
  // Destructor of __shared_mutex_pthread will call pthread_rwlock_destroy.
  std::unique_lock<ReadWriteLock> tbr_lock(tbr_topk_dns_lock_);
  std::shared_lock<PlacementRWLock> dn_lock(rwlock_);

  struct FixedDatanodeInfo {
    DatanodeInfoPtr dn;
    uint64_t temp_based_remaining;
  };
  std::vector<FixedDatanodeInfo> fixed_dns;
  for (DatanodeInfoPtr dn : datanodes_) {
    if (!dn->IsWriteable()) {
      continue;
    }

    fixed_dns.push_back(FixedDatanodeInfo{dn, dn->stat().temp_based_remaining});
  }

  switch (GetDistributionType(FLAGS_block_placement_distribution_type)) {
    case BPDistributionType::Geometric:
      std::sort(fixed_dns.begin(),
                fixed_dns.end(),
                [](const FixedDatanodeInfo& lhs, const FixedDatanodeInfo& rhs) {
                  return lhs.temp_based_remaining > rhs.temp_based_remaining;
                });
      break;
    case BPDistributionType::Uniform:
    case BPDistributionType::RoundRobin:
    default:
      break;
  }

  tbr_topk_dns_.clear();
  for (FixedDatanodeInfo fixed_dn : fixed_dns) {
    tbr_topk_dns_.push_back(fixed_dn.dn);
  }

  if (FLAGS_choose_target_shuffle_candidate) {
    std::random_device rd;
    std::mt19937 re(rd());
    std::shuffle(tbr_topk_dns_.begin(), tbr_topk_dns_.end(), re);
  }

  VLOG(11) << "tbr_topk_dns_.size()=" << tbr_topk_dns_.size();

  choose_target_ctx_.Each([&](ChooseTargetPerCpuContextCfsDefault* ctx) {
    ctx->Refresh(tbr_topk_dns_);
  });
}

// NOTICE: DatanodeManager::datanodes_ owns DatanodeInfoPtrs.
// We assume DatanodeManager never delete them.
// Based on that assumption, we don't need to hold BlockPlacement::rwlock_
// except using datanodes_.
// See commit 96c15bfcfe4b61fe1ecd140a3b032e29e57333cc for more infos.
void BlockPlacementCfsDefault::RefreshBlockReadCache(
    const DetailedBlock& detailed_block,
    const ReadAdvice& advice,
    BlockReadCache* cache) {
  CHECK_NOTNULL(cache);
  int64_t now = GetCurrentTsInMs() / 1000;
  BlockID blk_id = detailed_block.blk_.id;
  if (now >= cache->refresh_ts + FLAGS_block_read_cache_refresh_interval_s) {
    cache->refresh_ts = now;
    cache->Clear();
    for (DatanodeID dn_id : detailed_block.storage_) {
      auto dn = GetDatanodeInfo(dn_id);

      // skip outdated DN
      if (dn == nullptr ||
          dn->IsStale() ||
          dn->IsDecommissionInProgress()) {
        continue;
      }

      // skip DN does not meet minimum version
      if (advice.version_checker != nullptr) {
        if (!advice.version_checker(dn->version())) {
          VLOG_OR_IF(8, FLAGS_log_choose_target)
              << "Skip DN " << dn->id() << " for version " << dn->version();
          continue;
        }
      }

      cache->Push(dn);
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Push D" << dn_id << " with replica to read cache dns of B"
          << blk_id;
    }
    if (cache->size < BlockReadCache::kCapacity &&
        (FLAGS_block_read_cache_skip_detail_check ||
         detailed_block.uc_ == BlockUCState::kPersisted)) {
      std::vector<DatanodeInfoPtr> favored_nodes;
      std::unordered_set<DatanodeInfoPtr> included(cache->dns,
                                                   cache->dns + cache->size);
      std::unordered_set<DatanodeInfoPtr> excluded;
      std::vector<DatanodeInfoPtr> dns_without_replica;
      PlacementAdvice padvice;
      padvice.version_checker = advice.version_checker;
      padvice.machine_requirement = advice.machine_requirement;
      ChooseTargetInternal(BlockReadCache::kCapacity - cache->size,
                           padvice,
                           NetworkLocationInfo(),
                           favored_nodes,
                           included,
                           &excluded,
                           &dns_without_replica);
      for (DatanodeInfoPtr dn : dns_without_replica) {
        cache->Push(dn);
        VLOG_OR_IF(10, FLAGS_log_choose_target)
            << "Push D" << dn->id() << " without replica to read cache dns of B"
            << blk_id;
      }
    }
  }
}

bool BlockPlacementCfsDefault::ChooseTargetInternal(
    int32_t rep_num,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GT(rep_num, 0);
  CHECK_NOTNULL(excluded);
  // existed_host being null indicates don't consider nodes.
  CHECK_NOTNULL(result);

  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "ChooseTargetInternal"
      << " advice=" << advice.ToString()
      << " client_location=" << client_location.ToString()
      << " favored/include/exclude=" << favored_nodes.size() << "/"
           << included.size() << "/" << excluded->size();

  // for host level anti-affinity
  std::unordered_set<std::string> existed_host;
  // for switch level affinity and anti-affinity
  std::unordered_map<std::string, int> existed_switch;
  for (auto dn : included) {
    if (dn->location_tag().has_host() && !dn->location_tag().host().empty()) {
      existed_host.insert(dn->location_tag().host());
    }
    if (dn->location_tag().has_switch_() &&
        !dn->location_tag().switch_().empty()) {
      existed_switch[dn->location_tag().switch_()]++;
    }
  }
  // Add all favored_nodes with the highest priority.
  for (DatanodeInfoPtr dn : favored_nodes) {
    if (AddTarget(dn,
                  advice,
                  client_location.location_tag,
                  client_location.rdma_tag,
                  included,
                  excluded,
                  &existed_host,
                  &existed_switch,
                  /*avoid_dying_dn=*/false,
                  /*enable_local_az=*/false,
                  /*enable_local_host=*/false,
                  result)) {
      rep_num--;
      if (rep_num <= 0) {
        return true;
      }
    } else {
      excluded->insert(dn);
    }
  }
  for (auto dn : *result) {
    if (dn->location_tag().has_host() && !dn->location_tag().host().empty()) {
      existed_host.insert(dn->location_tag().host());
    }
  }

  // choose 1 local first
  if (FLAGS_block_placement_local_dn_first) {
    if (rep_num > 0) {
      int32_t cur_rep_num = result->size();
      ChooseTargetImp(1,
                      advice,
                      client_location,
                      included,
                      excluded,
                      &existed_host,
                      &existed_switch,
                      /*avoid_dying_dn=*/true,
                      /*enable_local_az=*/true,
                      /*enable_local_host=*/true,
                      result);
      rep_num -= result->size() - cur_rep_num;
    }
  }

  // origin
  if (rep_num > 0) {
    int32_t cur_rep_num = result->size();
    ChooseTargetImp(rep_num,
                    advice,
                    client_location,
                    included,
                    excluded,
                    &existed_host,
                    &existed_switch,
                    /*avoid_dying_dn=*/true,
                    /*enable_local_az=*/true,
                    /*enable_local_host=*/false,
                    result);
    rep_num -= result->size() - cur_rep_num;
  }

  // Do not consider dn dying state.
  if (rep_num > 0) {
    MFC(metrics_->block_placement_choose_target_fallback_dying_dn_)->Inc();
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "fallback to allow dying dn";
    int32_t cur_rep_num = result->size();
    ChooseTargetImp(rep_num,
                    advice,
                    client_location,
                    included,
                    excluded,
                    &existed_host,
                    &existed_switch,
                    /*avoid_dying_dn=*/false,
                    /*enable_local_az=*/true,
                    /*enable_local_host=*/false,
                    result);
    rep_num -= result->size() - cur_rep_num;
  }

  // Do not consider switch.
  if (rep_num > 0) {
    MFC(metrics_->block_placement_choose_target_fallback_existed_switch_)
        ->Inc();
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "fallback to ignore existed_switch";
    int32_t cur_rep_num = result->size();
    ChooseTargetImp(rep_num,
                    advice,
                    client_location,
                    included,
                    excluded,
                    &existed_host,
                    nullptr,
                    /*avoid_dying_dn=*/false,
                    /*enable_local_az=*/true,
                    /*enable_local_host=*/false,
                    result);
    rep_num -= result->size() - cur_rep_num;
  }

  // Do not consider machine and switch.
  if (rep_num > 0) {
    MFC(metrics_->block_placement_choose_target_fallback_existed_host_)->Inc();
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "fallback to ignore existed_host";
    int32_t cur_rep_num = result->size();
    ChooseTargetImp(rep_num,
                    advice,
                    client_location,
                    included,
                    excluded,
                    nullptr,
                    nullptr,
                    /*avoid_dying_dn=*/false,
                    /*enable_local_az=*/true,
                    /*enable_local_host=*/false,
                    result);
    rep_num -= result->size() - cur_rep_num;
  }

  // Do not consider machine and switch and local_az
  if (rep_num > 0) {
    MFC(metrics_->block_placement_choose_target_fallback_local_az_)->Inc();
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "fallback to ignore local_az";
    MFC(metrics_->block_placement_choose_target_fallback_all_)->Inc();
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "fallback all rule";
    int32_t cur_rep_num = result->size();
    ChooseTargetImp(rep_num,
                    advice,
                    client_location,
                    included,
                    excluded,
                    nullptr,
                    nullptr,
                    /*avoid_dying_dn=*/false,
                    /*enable_local_az=*/false,
                    /*enable_local_host=*/false,
                    result);
    rep_num -= result->size() - cur_rep_num;
  }
  return rep_num == 0;
}

bool BlockPlacementCfsDefault::ChooseTargetImp(
    int32_t rep_num,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::unordered_set<std::string>* existed_host,
    std::unordered_map<std::string, int>* existed_switch,
    bool avoid_dying_dn,
    bool enable_local_az,
    bool enable_local_host,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GT(rep_num, 0);
  CHECK_NOTNULL(excluded);
  // existed_host being null indicates don't consider nodes.
  CHECK_NOTNULL(result);

  // Randomly choose dn from all dns.
  std::shared_lock<ReadWriteLock> lock(tbr_topk_dns_lock_);
  std::size_t size = tbr_topk_dns_.size();
  while (rep_num > 0 && size > 0) {
    std::size_t start = GetIndex(size);
    std::size_t i = 0;
    for (; i < size; i++) {
      std::size_t cur = (start + i) % size;
      bool found = false;
      found = AddTarget(tbr_topk_dns_[cur],
                        advice,
                        client_location.location_tag,
                        client_location.rdma_tag,
                        included,
                        excluded,
                        existed_host,
                        existed_switch,
                        avoid_dying_dn,
                        enable_local_az,
                        enable_local_host,
                        result);
      if (found) {
        rep_num--;
        break;
      }
    }
    if (i == size) {
      break;
    }
  }

  return rep_num == 0;
}

bool BlockPlacementCfsDefault::AddTarget(
    DatanodeInfoPtr dn,
    const PlacementAdvice& advice,
    const cloudfs::LocationTag& client_location_tag,
    const std::string& client_rdma_tag,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::unordered_set<std::string>* existed_host,
    std::unordered_map<std::string, int>* existed_switch,
    bool avoid_dying_dn,
    bool enable_local_az,
    bool enable_local_host,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_NOTNULL(excluded);
  CHECK_NOTNULL(result);

  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "AddTarget, Try."
      << " client_location_tag=" << client_location_tag.ShortDebugString()
      << " client_rdma_tag=" << client_rdma_tag;

  if (dn == nullptr) {
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "Exclude dn==nullptr";
    return false;
  }
  if (GetDatanodeInfo(dn->id()) != dn) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to GetDatanodeInfo mismatch";
    return false;
  }
  if (included.find(dn) != included.end()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to included";
    return false;
  }
  if (excluded->find(dn) != excluded->end()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to excluded";
    return false;
  }
  auto storage_policy =
      GetGlobalStoragePolicySuite().GetPolicyFromId(advice.storage_policy_id);
  if (storage_policy != nullptr) {
    auto storage_types_mask = storage_policy->storage_types_mask();
    if ((dn->storage_type() & storage_types_mask) == 0) {
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Exclude D" << dn->id() << " due to storage type mismatch"
          << ": dn_type=" << static_cast<uint32_t>(dn->storage_type())
          << ", storage_types_mask="
          << static_cast<uint32_t>(storage_types_mask);
      return false;
    }
  }

  const auto& location_tag = dn->location_tag();
  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "dn_location_tag=" << location_tag.ShortDebugString();
  // check host
  do {
    if (FLAGS_placement_ignore_existed_host) {
      break;
    }
    if (!location_tag.has_host() || location_tag.host().empty()) {
      break;
    }
    if (!existed_host ||
        existed_host->find(location_tag.host()) == existed_host->end()) {
      break;
    }

    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to existed nodes";
    return false;
  } while (false);

  // check host
  do {
    if (FLAGS_placement_ignore_local_host) {
      break;
    }
    if (!enable_local_host) {
      break;
    }
    if (location_tag.host().empty()) {
      break;
    }
    if (client_location_tag.host().empty()) {
      break;
    }
    if (location_tag.host() == client_location_tag.host()) {
      break;
    }

    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to not local_host";
    return false;
  } while (false);

  // check az
  do {
    if (FLAGS_placement_ignore_local_az) {
      break;
    }
    if (!enable_local_az) {
      break;
    }
    if (advice.geograph != kCentralizePolicy) {
      break;
    }
    if (location_tag.az().empty()) {
      break;
    }
    if (location_tag.az() == advice.enforce_dc) {
      break;
    }
    if (client_location_tag.az().empty()) {
      break;
    }
    if (location_tag.az() == client_location_tag.az()) {
      break;
    }

    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to not local_az";
    return false;
  } while (false);

  // check switch
  std::string dn_switch;
  do {
    if (FLAGS_placement_ignore_existed_switch) {
      break;
    }
    if (existed_switch == nullptr) {
      break;
    }
    bool applicable = false;
    bool is_same_switch = false;
    std::tie(applicable, is_same_switch, dn_switch) =
        dn->IsInSameSwitch(client_location_tag, client_rdma_tag);
    if (!applicable) {
      break;
    }

    auto it = existed_switch->find(dn_switch);
    int exist = it == existed_switch->end() ? 0 : it->second;
    auto local_it = existed_switch->find(client_rdma_tag);
    int local_exist = local_it == existed_switch->end() ? 0 : local_it->second;

    // local first
    if (!is_same_switch && advice.local_switch_target > 0 &&
        local_exist < advice.local_switch_target) {
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Exclude D" << dn->id()
          << " due to not local and not reach the limit of local_switch."
          << " dn_switch=" << dn_switch << " local_exist=" << local_exist;
      return false;
    }
    // check local
    if (is_same_switch && advice.local_switch_target > 0 &&
        exist >= advice.local_switch_target) {
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Exclude D" << dn->id()
          << " due to reach the limit of local_switch."
          << " dn_switch=" << dn_switch << " exist=" << exist;
      return false;
    }
    // check other
    if (!is_same_switch && advice.other_switch_target > 0 &&
        exist >= advice.other_switch_target) {
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Exclude D" << dn->id()
          << " due to reach the limit of other_switch."
          << " dn_switch=" << dn_switch << " exist=" << exist;
      return false;
    }
  } while (false);

  if (avoid_dying_dn && dn->IsDying()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to dying";
    return false;
  }
  if (FLAGS_avoid_stale_datanode_for_write && dn->IsStale()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to stale";
    return false;
  }
  if (dn->IsDecommissionInProgress()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Exclude D" << dn->id() << " due to decommission in progress";
    return false;
  }
  if (advice.version_checker != nullptr) {
    if (!advice.version_checker(dn->version())) {
      VLOG_OR_IF(10, FLAGS_log_choose_target)
          << "Exclude D" << dn->id() << " due to version requirement";
      return false;
    }
  }

  VLOG_OR_IF(10, FLAGS_log_choose_target) << "AddTarget D" << dn->id();

  if (dn->location_tag().has_host() && existed_host) {
    existed_host->insert(dn->location_tag().host());
  }
  excluded->insert(dn);
  result->push_back(dn);

  if (existed_switch && !dn_switch.empty()) {
    (*existed_switch)[dn_switch]++;
  }
  return true;
}

DatanodeInfoPtr BlockPlacementCfsDefault::GetDatanodeInfo(DatanodeID dn_id,
                                                          bool check_liveness) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  auto it = id_to_dn_map_.find(dn_id);
  if (it == id_to_dn_map_.end()) {
    return nullptr;
  }
  if (check_liveness && !it->second->IsAlive()) {
    return nullptr;
  }
  return it->second;
}

BlockReadCacheSlice* BlockPlacementCfsDefault::Slice(BlockID blk_id) {
  return slices_[blk_id & slice_mask_].get();
}

int64_t BlockPlacementCfsDefault::GetCurrentTsInMs() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

// already hold tbr_topk_dns_lock_
std::size_t BlockPlacementCfsDefault::GetIndex(std::size_t size) {
  std::size_t start;
  switch (GetDistributionType(FLAGS_block_placement_distribution_type)) {
    case BPDistributionType::Geometric:
      start = choose_target_ctx_.At()->GetGeometircDistIndex(size);
      break;
    case BPDistributionType::Uniform:
      start = choose_target_ctx_.At()->GetUniformDistIndex(size);
      break;
    case BPDistributionType::RoundRobin:
      start = choose_target_ctx_.At()->GetRoundRobinIndex(size);
      break;
    case BPDistributionType::Weight:
      start = choose_target_ctx_.At()->GetWeightedSelectIndex();
      break;
    default:
      LOG(ERROR) << "Unknown BP distribution type";
      break;
  }

  VLOG(11) << "GetIndex, " << start;
  return start;
}

}  // namespace dancenn
