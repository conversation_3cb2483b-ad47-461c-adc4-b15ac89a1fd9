// Copyright 2019 jiangxunyang <<EMAIL>>

#include "datanode_manager/block_placement.h"
#include "datanode_manager/block_placement_nodezone.h"
#include "datanode_manager/block_placement_nodezone_metrics.h"

namespace dancenn {

BlockPlacementNodeZoneMetrics::BlockPlacementNodeZoneMetrics(BlockPlacementNodeZone* bp) {
  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("BlockPlacementNodeZone");

  {
    ZoneGroupId group_id = "default";
    auto metrics_key = "ChooseTarget4NewFailover#TagId=" + group_id;
    choose_target4new_failover_[group_id] = metrics_->RegisterCounter(metrics_key);
  }

  {
    ZoneGroupId group_id = "default";
    auto metrics_key = "ChooseTarget4RecoverFailover#TagId=" + group_id;
    choose_target4recover_failover_[group_id] = metrics_->RegisterCounter(metrics_key);
  }
}

void BlockPlacementNodeZoneMetrics::PrepareNodeZoneMetrics(const NodeZonePtr& nodezone) {
  auto dc_name_set = dc_info_.GetAllDCName();
  for (const auto& dcitr : dc_name_set) {
    auto metrics_key = "NodeZoneAllocBlocks#NodeZoneId=" + nodezone->id
        + "#DC=" + dcitr;
    nodezone_alloc_block_[GET_METRICS_NODEZONE_ALLOC_BLOCK_KEY(nodezone->id, dcitr)] =
        metrics_->RegisterCounter(metrics_key);
  }

  {
    auto metrics_key = "ChooseTarget4NewFailover#TagId=" + nodezone->group_id;
    choose_target4new_failover_[nodezone->group_id] = metrics_->RegisterCounter(metrics_key);
  }

  {
    auto metrics_key = "ChooseTarget4RecoverFailover#TagId=" + nodezone->group_id;
    choose_target4recover_failover_[nodezone->group_id] = metrics_->RegisterCounter(metrics_key);
  }
}

void BlockPlacementNodeZoneMetrics::IncNodeZoneAllocBlocks(const ZoneId& nodezone_id,
                                                           std::vector<DatanodeInfoPtr>* result) {
  if (result->empty()) {
    return;
  }
  NetworkLocation loc;
  for (auto itr : *result) {
    loc = ResolveNetworkLocation(itr->ip());
    auto key = GET_METRICS_NODEZONE_ALLOC_BLOCK_KEY(nodezone_id, loc.dc);

    auto metric_itr = nodezone_alloc_block_.find(key);
    if (metric_itr == nodezone_alloc_block_.end()) {
      LOG(WARNING) << "BlockPlacementNodeZoneMetrics::IncNodeZoneAllocBlocks metrics not found: "
          << " dc" << loc.dc
          << " nodezone_id" << nodezone_id;
      return;
    }
    MFC(metric_itr->second)->Inc(1);
  }
}

void BlockPlacementNodeZoneMetrics::IncChooseTarget4NewFailover(const ZoneGroupId& group_id) {
  auto itr = choose_target4new_failover_.find(group_id);
  if (itr == choose_target4new_failover_.end()) {
      LOG(WARNING) << "BlockPlacementNodeZoneMetrics::IncChooseTarget4NewFailover metrics not found: "
          << " group_id" << group_id;
      return;
  }
  MFC(itr->second)->Inc(1);
}

void BlockPlacementNodeZoneMetrics::IncChooseTarget4RecoverFailover(const ZoneGroupId& group_id){
  auto itr = choose_target4recover_failover_.find(group_id);
  if (itr == choose_target4recover_failover_.end()) {
      LOG(WARNING) << "BlockPlacementNodeZoneMetrics::IncChooseTarget4RecoverFailover metrics not found: "
          << " group_id" << group_id;
      return;
  }
  MFC(itr->second)->Inc(1);
}

}  // namespace dancenn

