// Copyright 2024 Mu <PERSON>ong <<EMAIL>>

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <algorithm>

#include "datanode_manager/block_index.h"

DECLARE_int32(datanode_stl_block_index_slice_num);

namespace dancenn {

BlockIndexImplStl::BlockIndexImplStl() {
  slice_number_ = FLAGS_datanode_stl_block_index_slice_num;
  table_.resize(slice_number_);

  size_ = 0;
}

BlockIndexImplStl::~BlockIndexImplStl() {
}

bool BlockIndexImplStl::Contains(uint64_t block_id) {
  CHECK_GT(block_id, 0);
  auto bucket = hash_uint64(block_id) % slice_number_;
  auto& slice = table_[bucket];

  return slice.find(block_id) != slice.end();
}

void BlockIndexImplStl::AddBlock(uint64_t block_id) {
  CHECK_GT(block_id, 0);

  auto bucket = hash_uint64(block_id) % slice_number_;
  auto& slice = table_[bucket];

  slice.insert(block_id);
}

void BlockIndexImplStl::AddBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    AddBlock(b);
  }
}

void BlockIndexImplStl::RemoveBlock(const uint64_t block_id) {
  CHECK_GT(block_id, 0);

  auto bucket = hash_uint64(block_id) % slice_number_;
  auto& slice = table_[bucket];

  slice.erase(block_id);
}

void BlockIndexImplStl::RemoveBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    RemoveBlock(b);
  }
}

void BlockIndexImplStl::Reset(const std::vector<uint64_t>& blocks) {
  Clear();
  for (auto b : blocks) {
    AddBlock(b);
  }
}

void BlockIndexImplStl::GetBlocks(std::vector<uint64_t>* blocks) {
  ForEach([&](uint64_t b) { blocks->emplace_back(b); });
}

void BlockIndexImplStl::Clear() {
  for (auto& slice : table_) {
    slice.clear();
  }
  size_ = 0;
}

void BlockIndexImplStl::ExtractBlocks(std::vector<uint64_t>* blocks) {
  GetBlocks(blocks);
  Clear();
}

void BlockIndexImplStl::ForEach(std::function<void(uint64_t)> cb) const {
  for (const auto& slice : table_) {
    for (const auto& block_id : slice) {
      cb(block_id);
    }
  }
}

}  // namespace dancenn
