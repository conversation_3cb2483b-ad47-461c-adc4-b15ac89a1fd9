//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: liji<PERSON><PERSON> <<EMAIL>>
//
#ifndef DATANODE_MANAGER_STORAGE_POLICY_H_
#define DATANODE_MANAGER_STORAGE_POLICY_H_

#include <hdfs.pb.h>

#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <vector>

#include "base/constants.h"

using cloudfs::BlockStoragePolicyProto;
using cloudfs::StorageTypeProto;
using cloudfs::StorageTypesProto;

namespace dancenn {

enum class StorageType : uint8_t {
  DISK = 1,
  SSD = 1 << 1,
  ARCHIVE = 1 << 2,
  RAM_DISK = 1 << 3,
  DISK2 = 1 << 4,
  DISK3 = 1 << 5
};

enum StoragePolicyId : uint32_t {
  kBlockStoragePolicyIdUnspecified = 0,
  kAllRAMStoragePolicy = 21,
  kMemoryStoragePolicy = 15,
  kOnlyDisk3StoragePolicy = 14,
  kOnlySSDStoragePolicy = 13,
  kAllSSDStoragePolicy = 12,
  kOnlyDisk2StoragePolicy = 11,
  kOneSSDStoragePolicy = 10,
  kOneSSD2StoragePolicy = 9,
  kHotStoragePolicy = 7,
  kWarmStoragePolicy = 5,
  kColdStoragePolicy = 2,
};

class StoragePolicy {
 public:
  StoragePolicy() = default;
  ~StoragePolicy() = default;

  StoragePolicyId id() const { return id_; }
  const std::string& name() const { return name_; }
  const std::vector<StorageType>& storage_types() const {
    return storage_types_;
  }
  const std::vector<StorageType>& creation_fallbacks() const {
    return creation_fallbacks_;
  }
  const std::vector<StorageType>& replication_fallbacks() const {
    return replication_fallbacks_;
  }
  uint8_t storage_types_mask() const { return storage_types_mask_; }
  uint8_t creation_fallbacks_mask() const { return creation_fallbacks_mask_; }
  uint8_t replication_fallbacks_mask() const {
    return replication_fallbacks_mask_;
  }
  bool copy_on_create_file() const { return copy_on_create_file_; }

  BlockStoragePolicyProto ToProto();

  static StorageType ToType(const StoragePolicyId& storage_policy);
  static StorageType ToType(const StorageTypeProto& type);
  static StorageTypeProto ToProto(const StoragePolicyId& storage_policy);
  static StorageTypeProto ToProto(const StorageType& type);
  StorageType DetermineStorageType(uint8_t storage_type, bool is_create_block);

 private:
  friend class StoragePolicySuite;
  // never modify after StoragePolicySuite Init
  StoragePolicyId id_{kBlockStoragePolicyIdUnspecified};
  std::string name_;
  std::vector<StorageType> storage_types_;
  std::vector<StorageType> creation_fallbacks_;
  std::vector<StorageType> replication_fallbacks_;
  uint8_t storage_types_mask_{0};
  uint8_t creation_fallbacks_mask_{0};
  uint8_t replication_fallbacks_mask_{0};
  bool copy_on_create_file_{false};
};

typedef std::shared_ptr<StoragePolicy> StoragePolicyPtr;

typedef std::tuple<StoragePolicyId,
                   std::string,
                   std::vector<StorageTypeProto>,
                   std::vector<StorageTypeProto>,
                   std::vector<StorageTypeProto>,
                   bool>
    StoragePolicyTuple;

class StoragePolicySuite {
 public:
  StoragePolicySuite();
  ~StoragePolicySuite() = default;

  StoragePolicyPtr GetPolicyFromName(const std::string& name) const;
  StoragePolicyPtr GetPolicyFromId(StoragePolicyId id) const;
  std::vector<StoragePolicyPtr> GetAllPolicies() const;

  StorageType DetermineStorageType(StoragePolicyId storage_policy_id,
                                   uint32_t storage_type,
                                   bool is_create_block) const;

 private:
  void InitMaps(const std::vector<StoragePolicyTuple>& tuples);

  //  id -> policy
  std::unordered_map<uint32_t, StoragePolicyPtr> id_policy_;
  //  name -> policy
  std::unordered_map<std::string, StoragePolicyPtr> name_policy_;
  StoragePolicyId defaultPolicyId_ = kHotStoragePolicy;
};

const StoragePolicySuite& GetGlobalStoragePolicySuite();

}  // namespace dancenn

#endif  // DATANODE_MANAGER_STORAGE_POLICY_H_
