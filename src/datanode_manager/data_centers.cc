// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "datanode_manager/data_centers.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <cnetpp/base/string_utils.h>

DECLARE_string(all_datacenters);

namespace dancenn {

const DataCenter kUnknownDataCenter { "", -1 };
const DataCenter kDefaultDataCenter { "", 0 };

DataCenters::DataCenters() {
  auto dcs =
    cnetpp::base::StringUtils::SplitByChars(FLAGS_all_datacenters, ",");
  if (dcs.size() == 0 || (dcs.size() == 1 && dcs[0].empty())) {
    dc_by_id_.push_back(kDefaultDataCenter);
    dc_by_name_.emplace(kDefaultDataCenter.name, kDefaultDataCenter);
    LOG(WARNING) << "No all_datacenters option, use default: "
      << kDefaultDataCenter.name;
  } else {
    for (int i = 0; i < static_cast<int>(dcs.size()); ++i) {
      dc_by_id_.emplace_back(dcs[i], i);
      dc_by_name_.emplace(dcs[i], DataCenter(dcs[i], i));
      LOG(INFO) << "Found DC: " << dcs[i] << ", id: " << i;
    }
  }
}

const DataCenter& DataCenters::GetDataCenterById(int id) const {
  if (id < 0 || static_cast<size_t>(id) >= dc_by_id_.size()) {
    return kUnknownDataCenter;
  }
  return dc_by_id_[id];
}

const DataCenter& DataCenters::GetDataCenterByName(
    const std::string& name) const {
  auto itr = dc_by_name_.find(name);
  if (itr == dc_by_name_.end()) {
    return kUnknownDataCenter;
  }
  return itr->second;
}

bool DataCenters::IsDataCenterValidForCentralizePolicy(const std::string& dc) {
  bool valid = true;
  if (dc.empty()) {
    return valid;
  }
  auto dcs = cnetpp::base::StringUtils::SplitByChars(dc, ",");
  if (dcs.size() > 1) {
    valid = false;
  } else {
    auto data_center = GetDataCenterByName(dc);
    if (data_center == kUnknownDataCenter) {
      valid = false;
    }
  }
  return valid;
}

bool DataCenters::IsDataCenterValidForDistributePolicy(const std::string& dc) {
  bool valid = true;
  auto dcs = cnetpp::base::StringUtils::SplitByChars(dc, ",");
  if (dcs.size() < 2 || dcs.size() > 3) {
    valid = false;
  } else {
    for (auto& d : dcs) {
      auto data_center = GetDataCenterByName(d);
      if (data_center == kUnknownDataCenter) {
        valid = false;
        break;
      }
    }
  }
  return valid;
}

}  // namespace dancenn

