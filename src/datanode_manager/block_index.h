// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef DATANODE_MANAGER_BLOCK_INDEX_H_
#define DATANODE_MANAGER_BLOCK_INDEX_H_

#include <string.h>

#include <functional>
#include <memory>
#include <set>
#include <unordered_set>
#include <vector>

#include "base/hash.h"

namespace dancenn {

class BlockIndex {
 public:
  BlockIndex() {}
  virtual ~BlockIndex() {}

  virtual void ForEach(std::function<void(uint64_t)> cb) const = 0;
  virtual bool Contains(uint64_t block_id) = 0;

  virtual void AddBlock(uint64_t block_id) = 0;
  virtual void AddBlocks(const std::vector<uint64_t>& blocks) = 0;
  virtual void RemoveBlock(uint64_t block_id) = 0;
  virtual void RemoveBlocks(const std::vector<uint64_t>& blocks) = 0;

  virtual void Reset(const std::vector<uint64_t> &blocks) = 0;
  virtual void GetBlocks(std::vector<uint64_t>* blocks) = 0;
  virtual void ExtractBlocks(std::vector<uint64_t>* blocks) = 0;

  virtual uint32_t size() const = 0;
  virtual void Stats(size_t* v) const = 0;
};

class OrderedBlockIndexImpl : public BlockIndex {
 public:
  OrderedBlockIndexImpl();
  ~OrderedBlockIndexImpl();

  void ForEach(std::function<void(uint64_t)> cb) const override;
  bool Contains(uint64_t block_id) override;

  void AddBlock(uint64_t block_id) override;
  void AddBlocks(const std::vector<uint64_t>& blocks) override;
  void RemoveBlock(uint64_t block_id) override;
  void RemoveBlocks(const std::vector<uint64_t>& blocks) override;

  void Reset(const std::vector<uint64_t> &blocks) override;
  void GetBlocks(std::vector<uint64_t>* blocks) override;
  void ExtractBlocks(std::vector<uint64_t>* blocks) override;

  uint32_t size() const override;
  void Stats(size_t* v) const override {
    v[0] = base_block_ids_->size();
    v[1] = delta_added_block_ids_.size();
    v[2] = delta_removed_block_ids_.size();
  }

 private:
  void Compact();
  void CompactIfNecessary();

  std::unique_ptr<std::vector<uint64_t>> base_block_ids_;
  std::set<uint64_t> delta_added_block_ids_;
  std::set<uint64_t> delta_removed_block_ids_;
};

class UnorderedBlockIndexImpl : public BlockIndex {
 public:
  UnorderedBlockIndexImpl();
  ~UnorderedBlockIndexImpl();

  void ForEach(std::function<void(uint64_t)> cb) const override;
  bool Contains(uint64_t block_id) override;

  void AddBlock(uint64_t block_id) override;
  void AddBlocks(const std::vector<uint64_t>& blocks) override;
  void RemoveBlock(uint64_t block_id) override;
  void RemoveBlocks(const std::vector<uint64_t>& blocks) override;

  void Reset(const std::vector<uint64_t> &blocks) override;
  void GetBlocks(std::vector<uint64_t>* blocks) override;
  void ExtractBlocks(std::vector<uint64_t>* blocks) override;

  uint32_t size() const override {
    return size_;
  }

  void Stats(size_t* v) const override {
    v[0] = size_;
    v[1] = capacity_;
    v[2] = used_buckets_;
  }

 private:
  void Clear();
  void Resize();
  inline void CleanBucketInternal(uint32_t bucket);

  uint64_t* table_{nullptr};

  uint32_t size_;
  uint32_t capacity_;
  uint32_t used_buckets_;
  uint32_t resize_threshold_;

  static constexpr uint64_t kEmptyBlockId = 0LL;
  static constexpr uint64_t kDeletedBlockId = UINT64_MAX;
};

// use stl to implement block index
// Support multi times add same block_id
class BlockIndexImplStl : public BlockIndex {
 public:
  BlockIndexImplStl();
  ~BlockIndexImplStl();

  void ForEach(std::function<void(uint64_t)> cb) const override;
  bool Contains(uint64_t block_id) override;

  void AddBlock(uint64_t block_id) override;
  void AddBlocks(const std::vector<uint64_t>& blocks) override;
  void RemoveBlock(uint64_t block_id) override;
  void RemoveBlocks(const std::vector<uint64_t>& blocks) override;

  void Reset(const std::vector<uint64_t>& blocks) override;
  void GetBlocks(std::vector<uint64_t>* blocks) override;
  void ExtractBlocks(std::vector<uint64_t>* blocks) override;

  uint32_t size() const override {
    return size_;
  }

  // NOTICE: update this function, use by http view
  void Stats(size_t* v) const override {
    v[0] = size_;
    v[1] = 0;
    v[2] = 0;
  }

 private:
  void Clear();

  std::vector<std::unordered_set<uint64_t>> table_;

  uint32_t size_;
  uint32_t slice_number_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_INDEX_H_
