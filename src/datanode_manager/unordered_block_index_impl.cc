// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#include "datanode_manager/block_index.h"

#include <algorithm>

#include <gflags/gflags.h>
#include <glog/logging.h>

DECLARE_double(datanode_unordered_block_index_load_factor);
DECLARE_uint32(datanode_unordered_block_index_init_capacity);

namespace dancenn {

UnorderedBlockIndexImpl::UnorderedBlockIndexImpl() {
  size_ = 0;
  used_buckets_ = 0;
  capacity_ = FLAGS_datanode_unordered_block_index_init_capacity;
  LOG_IF(FATAL, capacity_ & (capacity_ - 1) != 0) << "Must pow two, but: "
                                                  << capacity_;
  resize_threshold_ = static_cast<uint32_t>(
      capacity_ * FLAGS_datanode_unordered_block_index_load_factor);
  table_ = new uint64_t[capacity_];
  memset(table_, kEmptyBlockId, sizeof(uint64_t) * capacity_);
}

UnorderedBlockIndexImpl::~UnorderedBlockIndexImpl() {
  delete[] table_;
}

bool UnorderedBlockIndexImpl::Contains(uint64_t block_id) {
  CHECK_GT(block_id, 0);
  auto bucket = hash_uint64(block_id) & (capacity_ - 1);
  while (true) {
    auto stored_block_id = table_[bucket];
    if (stored_block_id == block_id) {
      return true;
    }
    if (stored_block_id == kEmptyBlockId) {
      return false;
    }
    //LOG(INFO) << "Contains stored_block_id" << stored_block_id;
    bucket = (bucket + 1) & (capacity_ - 1);
  }
}

void UnorderedBlockIndexImpl::AddBlock(uint64_t block_id) {
  CHECK_GT(block_id, 0);
  if (used_buckets_ > resize_threshold_) {
    Resize();
  }
  auto bucket = hash_uint64(block_id) & (capacity_ - 1);
  uint64_t* first_deleted_loc = nullptr;
  while (true) {
    auto stored_block_id = table_[bucket];
    if (stored_block_id == block_id) {
      return;
    }
    if (stored_block_id == kEmptyBlockId) {
      if (first_deleted_loc != nullptr) {
        *first_deleted_loc = block_id;
      } else {
        table_[bucket] = block_id;
        ++used_buckets_;
      }
      ++size_;
      return;
    }
    if (stored_block_id == kDeletedBlockId
        && first_deleted_loc == nullptr) {
      first_deleted_loc = table_ + bucket;
    }
    //LOG(INFO) << "AddBlock " << block_id << " " << stored_block_id << " "
    //  << used_buckets_ << " " << size_ << "  " << capacity_;
    bucket = (bucket + 1) & (capacity_ - 1);
  }
}

void UnorderedBlockIndexImpl::AddBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    AddBlock(b);
  }
}

void UnorderedBlockIndexImpl::RemoveBlock(const uint64_t block_id) {
  CHECK_GT(block_id, 0);
  auto bucket = hash_uint64(block_id) & (capacity_ - 1);
  while (true) {
    auto stored_block_id = table_[bucket];
    if (stored_block_id == block_id) {
      --size_;
      CleanBucketInternal(bucket);
      return;
    }
    if (stored_block_id == kEmptyBlockId) {
      return;
    }
    bucket = (bucket + 1) & (capacity_ - 1);
  }
}

void UnorderedBlockIndexImpl::RemoveBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    RemoveBlock(b);
  }
}

void UnorderedBlockIndexImpl::Reset(const std::vector<uint64_t>& blocks) {
  Clear();
  for (auto b : blocks) {
    AddBlock(b);
  }
}

void UnorderedBlockIndexImpl::GetBlocks(std::vector<uint64_t>* blocks) {
  ForEach([&](uint64_t b) {
    blocks->emplace_back(b);
  });
}

void UnorderedBlockIndexImpl::Clear() {
  memset(table_, kEmptyBlockId, sizeof(uint64_t) * capacity_);
  size_ = 0;
  used_buckets_ = 0;
}

void UnorderedBlockIndexImpl::ExtractBlocks(std::vector<uint64_t>* blocks) {
  GetBlocks(blocks);
  Clear();
}

void UnorderedBlockIndexImpl::ForEach(std::function<void(uint64_t)> cb) const {
  for (uint32_t bucket = 0; bucket < capacity_; ++bucket) {
    if (table_[bucket] != kEmptyBlockId
        && table_[bucket] != kDeletedBlockId) {
      cb(table_[bucket]);
    }
  }
}

void UnorderedBlockIndexImpl::Resize() {
  uint32_t new_capacity = capacity_ << 1;
  auto new_table = new uint64_t[new_capacity];
  memset(new_table, kEmptyBlockId, sizeof(uint64_t) * new_capacity);
  VLOG(8) << "Resize " << capacity_ << " -> " << new_capacity;
  for (uint32_t bucket = 0; bucket < capacity_; ++bucket) {
    auto stored_block_id = table_[bucket];
    if (stored_block_id != kEmptyBlockId
        && stored_block_id != kDeletedBlockId) {
      auto new_bucket = hash_uint64(stored_block_id) & (new_capacity - 1);
      while (true) {
        if (new_table[new_bucket] == kEmptyBlockId) {
          new_table[new_bucket] = stored_block_id;
          break;
        }
        new_bucket = (new_bucket + 1) & (new_capacity - 1);
      }
    }
  }
  delete[] table_;
  table_ = new_table;
  used_buckets_ = size_;

  capacity_ = new_capacity;
  resize_threshold_ = static_cast<uint32_t>(
      capacity_ * FLAGS_datanode_unordered_block_index_load_factor);
}

void UnorderedBlockIndexImpl::CleanBucketInternal(uint32_t bucket) {
  auto next_bucket = (bucket + 1) & (capacity_ - 1);
  if (table_[next_bucket] == kEmptyBlockId) {
    table_[bucket] = kEmptyBlockId;
    --used_buckets_;
  } else {
    table_[bucket] = kDeletedBlockId;
  }
}

}  // namespace dancenn
