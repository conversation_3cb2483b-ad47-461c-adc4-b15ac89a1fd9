// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/16
// Description

#ifndef DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_DEFAULT_H_
#define DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_DEFAULT_H_

#include <cstddef>
#include <cstdint>
#include <functional>
#include <queue>
#include <random>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/cpu_local.h"
#include "base/rack_aware.h"
#include "base/read_write_lock.h"
#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/advice.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"

// TODO(ruanjunbin): Sort functions.
namespace dancenn {

enum class BPDistributionType {
  RoundRobin,
  Uniform,
  Geometric,
  Weight,
};

// https://bytedance.feishu.cn/docs/doccnrdi8PmQtTWsuL4oP5Z8sAf?from=from_copylink
// ids_ records which data nodes we select for read request of
// persisted blocks. We use it to make sure almost all read requests of one
// block is send to the same data node.
struct BlockReadCache {
  static constexpr std::size_t kCapacity = 3;
  BlockID blk_id = kInvalidBlockID;
  int64_t refresh_ts = 0;
  std::size_t size = 0;
  DatanodeInfoPtr dns[kCapacity] = {nullptr, nullptr, nullptr};
  BlockReadCache* next = nullptr;
  BlockReadCache* prev = nullptr;

  void Clear();
  bool Push(DatanodeInfoPtr dn);
  void Sort(const std::vector<DatanodeID>& dns_with_replica);
};

class BlockReadCacheSlice {
 public:
  BlockReadCacheSlice();
  ~BlockReadCacheSlice();

  BlockReadCacheSlice(const BlockReadCacheSlice& other) = delete;
  BlockReadCacheSlice(BlockReadCacheSlice&& other) = delete;
  BlockReadCacheSlice& operator=(const BlockReadCacheSlice& other) = delete;
  BlockReadCacheSlice&& operator=(BlockReadCacheSlice&& other) = delete;

  void lock();
  void unlock();

  BlockReadCache* GetOrCreateBlock(BlockID blk_id);
  // Caller already holds lock.
  void RemoveExpiredBlocks(int64_t currentTsInSec);

  // For unit tests.
  std::vector<BlockID> GetDebugInfo(std::size_t bucket_id);

 private:
  std::size_t BlockIdBucket(BlockID blk_id);
  void RemoveBlocksWithCond(
      const std::function<bool(const BlockReadCache& cache)>& cond);

 private:
  std::mutex lock_;
  std::size_t num_buckets_;
  std::vector<BlockReadCache*> buckets_;
};

class ChooseTargetPerCpuContextCfsDefault {
 public:
  ChooseTargetPerCpuContextCfsDefault();
  void Init();
  std::size_t GetGeometircDistIndex(std::size_t size);
  std::size_t GetUniformDistIndex(std::size_t size);
  std::size_t GetRoundRobinIndex(std::size_t size);
  std::size_t GetWeightedSelectIndex();

  // refresh dn state for placement
  void Refresh(const std::vector<DatanodeInfoPtr>& dns);

 private:
  std::atomic_uint round_robin_id_;
  // TODO(ruanjunbin): need a lock?
  std::mt19937 rand_;
  std::geometric_distribution<std::size_t> geometric_dist_;
  std::uniform_int_distribution<std::size_t> uniform_dist_;

  std::vector<uint32_t> prefix_weight_sum_;
  uint32_t total_weight_;
  uint32_t total_nodes_;
};

class BlockPlacementCfsDefault : public BlockPlacement {
 public:
  explicit BlockPlacementCfsDefault(DatanodeManagerMetrics* metrics,
                                    bool with_refresher = false);
  ~BlockPlacementCfsDefault() override;

  void AddDatanode(DatanodeInfoPtr dn) override;
  void RemoveDatanode(DatanodeInfoPtr dn) override;

  bool ChooseTarget4New(const std::string& src_path,
                        int32_t rep_num,
                        uint32_t blocksize,
                        const PlacementAdvice& advice,
                        const NetworkLocationInfo& client_location,
                        const std::vector<DatanodeInfoPtr>& favored_nodes,
                        std::unordered_set<DatanodeInfoPtr>* excluded,
                        std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Recover(const std::string& src_path,
                            int32_t rep_num,
                            uint32_t blocksize,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored_nodes,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Read(const DetailedBlock& detailed_block,
                         int32_t rep_num,
                         const ReadAdvice& advice,
                         const NetworkLocationInfo& client_location,
                         const std::vector<DatanodeInfoPtr>& favored_nodes,
                         std::unordered_set<DatanodeInfoPtr>* excluded,
                         std::vector<DatanodeInfoPtr>* result) override;
  void ChooseFunctionalTarget4Read(
      const DetailedBlock& detailed_block,
      const ReadAdvice& advice,
      std::vector<DatanodeInfoPtr>* result) override;
  DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk,
      std::size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice& advice = kDefaultAdvice) override;

  std::unordered_map<std::string, int> GetExpectedPlacement(
      const PlacementAdvice& advice,
      int32_t rep_num) override;

  bool IsLocal(DatanodeInfoPtr dn,
               const NetworkLocation& loc,
               bool* same_rack,
               bool* same_dc,
               uint32_t* dc_distance) override;
  bool DatanodeExist(DatanodeInfoPtr dn) override;

  bool HasBeenMultiRack() override;
  std::size_t NumRacks() override;

  void ListAll(std::string* output) override;

  void GetAllDatanodesInRack(const std::string& dc,
                             const std::string& rack,
                             std::unordered_set<DatanodeInfoPtr>* dns) override;
  void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) override;
  bool RefreshConfig() override;
  void RecalcAllDatanode() override;

 private:
  void RefreshBlockReadCache(const DetailedBlock& detailed_block,
                             const ReadAdvice& advice,
                             BlockReadCache* cache);
  bool ChooseTargetInternal(int32_t rep_num,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored_nodes,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeInfoPtr>* result);
  bool ChooseTargetImp(int32_t rep_num,
                       const PlacementAdvice& advice,
                       const NetworkLocationInfo& client_location,
                       const std::unordered_set<DatanodeInfoPtr>& included,
                       std::unordered_set<DatanodeInfoPtr>* excluded,
                       std::unordered_set<std::string>* existed_host,
                       std::unordered_map<std::string, int>* existed_switch,
                       bool avoid_dying_dn,
                       bool enable_local_az,
                       bool enable_local_host,
                       std::vector<DatanodeInfoPtr>* result);
  bool AddTarget(DatanodeInfoPtr dn,
                 const PlacementAdvice& advice,
                 const cloudfs::LocationTag& client_location_tag,
                 const std::string& client_rdma_tag,
                 const std::unordered_set<DatanodeInfoPtr>& included,
                 std::unordered_set<DatanodeInfoPtr>* excluded,
                 std::unordered_set<std::string>* existed_host,
                 std::unordered_map<std::string, int>* existed_switch,
                 bool avoid_dying_dn,
                 bool enable_local_az,
                 bool enable_local_host,
                 std::vector<DatanodeInfoPtr>* result);

  bool ChooseTarget4ReadPreferLocal(
      const DetailedBlock& detailed_block,
      int32_t rep_num,
      const ReadAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result);
  bool ChooseTarget4ReadPreferCached(
      const DetailedBlock& detailed_block,
      int32_t rep_num,
      const ReadAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result);
  bool ChooseTarget4ReadByCache(
      const DetailedBlock& detailed_block,
      int32_t rep_num,
      const ReadAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result);
  DatanodeInfoPtr ChooseReplicaToDelete(
      const std::unordered_set<DatanodeInfoPtr>& replicas);

  DatanodeInfoPtr GetDatanodeInfo(DatanodeID dn_id, bool check_liveness = true);
  BlockReadCacheSlice* Slice(BlockID blk_id);
  virtual int64_t GetCurrentTsInMs();
  virtual std::size_t GetIndex(std::size_t size);

 private:
  // Use BlockPlacement::rwlock_ to protect them.
  std::unordered_set<DatanodeInfoPtr> datanodes_;
  std::unordered_map<DatanodeID, DatanodeInfoPtr> id_to_dn_map_;

  DatanodeManagerMetrics* metrics_{nullptr};

  // Please take locks in order to avoid dead lock:
  // 1. tbr_topk_dns_lock_
  // 2. BlockPlacement::rwlock_
  ReadWriteLock tbr_topk_dns_lock_;
  std::vector<DatanodeInfoPtr> tbr_topk_dns_;
  CpuLocal<ChooseTargetPerCpuContextCfsDefault> choose_target_ctx_;

  std::size_t num_slices_;
  std::size_t slice_mask_;
  std::vector<std::unique_ptr<BlockReadCacheSlice>> slices_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_DEFAULT_H_
