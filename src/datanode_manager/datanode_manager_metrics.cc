// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "datanode_manager/datanode_manager_metrics.h"

#include "datanode_manager/datanode_manager.h"

namespace dancenn {

DatanodeManagerMetrics::DatanodeManagerMetrics(DatanodeManager* dm) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("DatanodeManager");

  num_total_datanode_ =
      metrics->RegisterGauge("NumDatanodes", [dm]() -> double {
        if (dm) {
          return dm->stat().num_total_datanode;
        }
        return 0.;
      });
  
  num_functional_datanode_ = metrics->RegisterGauge("NumFunctional", [dm]() -> double {
    if (dm) {
      return dm->stat().num_functional_datanode;
    }
    return 0.;
  });

  num_alive_datanode_ = metrics->RegisterGauge("NumAlive", [dm]() -> double {
    if (dm) {
      return dm->stat().num_alive_datanode;
    }
    return 0.;
  });

  num_content_stale_datanode_ =
      metrics->RegisterGauge("NumContentStale", [dm]() -> double {
        if (dm) {
          return dm->stat().num_content_stale_datanode;
        }
        return 0.;
      });

  num_stale_datanode_ = metrics->RegisterGauge("NumStale", [dm]() -> double {
    if (dm) {
      return dm->stat().num_stale_datanode;
    }
    return 0.;
  });

  num_decommission_datanode_ =
      metrics->RegisterGauge("NumDecommission", [dm]() -> double {
        if (dm) {
          return dm->stat().num_decommission_datanode;
        }
        return 0.;
      });

  num_decommissioned_datanode_ =
      metrics->RegisterGauge("NumDecommissioned", [dm]() -> double {
        if (dm) {
          return dm->stat().num_decommissioned_datanode;
        }
        return 0.;
      });

  num_dead_datanode_ = metrics->RegisterGauge("NumDead", [dm]() -> double {
    if (dm) {
      return dm->stat().num_dead_datanode;
    }
    return 0.;
  });

  capacity_bytes_ = metrics->RegisterGauge("SpaceUsage", [dm]() -> double {
    if (dm) {
      auto dms = dm->stat();
      return dms.capacity_kb ? static_cast<double>(dms.dfs_used_kb) /
                                   static_cast<double>(dms.capacity_kb)
                             : 0;
    }
    return 0.;
  });

  capacity_bytes_ = metrics->RegisterGauge("CapacityBytes", [dm]() -> double {
    if (dm) {
      return dm->stat().capacity;
    }
    return 0.;
  });

  remaining_bytes_ = metrics->RegisterGauge("RemainingBytes", [dm]() -> double {
    if (dm) {
      return dm->stat().remaining;
    }
    return 0.;
  });

  nondfs_used_bytes_ =
      metrics->RegisterGauge("NondfsUsedBytes", [dm]() -> double {
        if (dm) {
          return dm->stat().non_dfs_used;
        }
        return 0.;
      });

  dfs_used_bytes_ = metrics->RegisterGauge("DfsUsedBytes", [dm]() -> double {
    if (dm) {
      return dm->stat().dfs_used;
    }
    return 0.;
  });

  blockpool_used_bytes_ =
      metrics->RegisterGauge("BlockPoolUsedBytes", [dm]() -> double {
        if (dm) {
          return dm->stat().blockpool_used;
        }
        return 0.;
      });

  capacity_kb_ = metrics->RegisterGauge("CapacityKB", [dm]() -> double {
    if (dm) {
      return dm->stat().capacity_kb;
    }
    return 0.;
  });

  remaining_kb_ = metrics->RegisterGauge("RemainingKB", [dm]() -> double {
    if (dm) {
      return dm->stat().remaining_kb;
    }
    return 0.;
  });

  nondfs_used_kb_ = metrics->RegisterGauge("NondfsUsedKB", [dm]() -> double {
    if (dm) {
      return dm->stat().non_dfs_used_kb;
    }
    return 0.;
  });

  dfs_used_kb_ = metrics->RegisterGauge("DfsUsedKB", [dm]() -> double {
    if (dm) {
      return dm->stat().dfs_used_kb;
    }
    return 0.;
  });

  blockpool_used_kb_ =
      metrics->RegisterGauge("BlockPoolUsedKB", [dm]() -> double {
        if (dm) {
          return dm->stat().blockpool_used_kb;
        }
        return 0.;
      });

  block_placement_replication_distribution_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyReplicaCountDistribution");
  block_placement_choose_target_succeed_cnt_ = metrics->RegisterCounter(
      "BlockPlacementPolicyChooseTargetCnt#status=Succeed");
  block_placement_choose_target_failed_cnt_ = metrics->RegisterCounter(
      "BlockPlacementPolicyChooseTargetCnt#status=Failed");
  block_placement_writer_to_dn_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step=WriterToDn");
  block_placement_resolve_writer_location_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step=ResolveWriteLocation");
  block_placement_process_excluded_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step=ProcessExcluded");
  block_placement_process_favored_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#"
      "step=ProcessFavoredNodesBeforeChooseTarget");
  block_placement_choose_target_for_new_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#"
      "step=ChooseTarget4New");
  block_placement_choose_target_for_read_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step=ChooseTarget4Read");
  block_placement_choose_target_compute_dc_list_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ComputeDcList");
  block_placement_choose_target_process_favored_nodes_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ProcessFavoredNodes");
  block_placement_choose_target_shuffle_racks_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ShuffleRacks");
  block_placement_choose_target_choose_random_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ChooseRandom");
  block_placement_choose_target_choose_remote_rack_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ChooseRemoteRack");
  block_placement_choose_target_choose_from_rack_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step=ChooseFromRack");
  block_placement_fill_response_time_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step=FillResponse");

  block_placement_choose_target_for_read_get_slice_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step="
          "ChooseTarget4ReadGetSlice");
  block_placement_choose_target_for_read_get_block_read_cache_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step="
          "ChooseTarget4ReadGetReadCache");
  block_placement_choose_target_for_read_refresh_block_read_cache_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step="
          "ChooseTarget4ReadRefreshBlockReadCache");
  block_placement_choose_target_for_read_recheck_ = metrics->RegisterHistogram(
      "BlockPlacementPolicyChooseTargetTimeUs#step="
      "Recheck");

  block_placement_choose_target_fallback_dying_dn_ = metrics->RegisterCounter(
      "BlockPlacementPolicyChooseTarget.Fallback#state=FallbackDyingDN");
  block_placement_choose_target_fallback_existed_switch_ =
      metrics->RegisterCounter(
          "BlockPlacementPolicyChooseTarget.Fallback#state="
          "FallbackExistedSwitch");
  block_placement_choose_target_fallback_existed_host_ =
      metrics->RegisterCounter(
          "BlockPlacementPolicyChooseTarget.Fallback#state="
          "FallbackExistedHost");
  block_placement_choose_target_fallback_local_az_ = metrics->RegisterCounter(
      "BlockPlacementPolicyChooseTarget.Fallback#state="
      "FallbackLocalAz");
  block_placement_choose_target_fallback_all_ = metrics->RegisterCounter(
      "BlockPlacementPolicyChooseTarget.Fallback#state=FallbackAll");

  block_placement_choose_target_for_read_block_event_logger_time_ =
      metrics->RegisterHistogram(
          "BlockPlacementPolicyChooseTargetTimeUs#step="
          "ChooseTarget4ReadBlockEventLogger");

  construct_sorted_located_block_num_dn_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlockNumDN");
  construct_sorted_located_block_num_block_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlockNumBlock");

  construct_sorted_located_block_per_dn_get_dn_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlockPerDN#step=GetDN");
  construct_sorted_located_block_per_dn_is_local_ = metrics->RegisterHistogram(
      "ConstructSortedLocatedBlockPerDN#step=IsLocal");
  construct_sorted_located_block_per_dn_other_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlockPerDN#step=Other");

  construct_sorted_located_block_get_dns_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlock#step=GetDNS");
  construct_sorted_located_block_add_local_dn_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlock#step=AddLocalDN");
  construct_sorted_located_block_add_stale_dn_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlock#step=AddStaleDN");
  construct_sorted_located_block_add_remote_dn_ = metrics->RegisterHistogram(
      "ConstructSortedLocatedBlock#step=AddRemoteDN");
  construct_sorted_located_block_advice_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlock#step=Advice");
  construct_sorted_located_block_other_ =
      metrics->RegisterHistogram("ConstructSortedLocatedBlock#step=Other");

  dump_datanode_info_bg_batch_size_ =
      metrics->RegisterHistogram("DumpDatanodeInfoBg.BatchSize");
  dump_datanode_info_bg_batch_time_ =
      metrics->RegisterHistogram("DumpDatanodeInfoBg.BatchTime");
  dump_datanode_info_bg_total_time_ =
      metrics->RegisterHistogram("DumpDatanodeInfoBg.TotalTime");
  dump_datanode_info_fg_time_ =
      metrics->RegisterHistogram("DumpDatanodeInfoFg.Time");
}

}  // namespace dancenn
