// Copyright 2017 Liyuan Lei <<EMAIL>>

#ifndef DATANODE_MANAGER_BLOCK_PLACEMENT_H_
#define DATANODE_MANAGER_BLOCK_PLACEMENT_H_

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <fstream>
#include <memory>
#include <mutex>
#include <random>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/cpu_local.h"
#include "base/network_location_info.h"
#include "base/read_write_lock.h"
#include "base/rw_spinlock.h"
#include "base/vlock.h"
#include "block_manager/block.h"
#include "datanode_manager/advice.h"
#include "datanode_manager/data_centers.h"
#include "datanode_manager/data_centers_topology.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/datanode_manager_metrics.h"
#include "datanode_manager/storage_policy.h"

namespace dancenn {

struct DatanodeIDWithPlacementInfo {
  DatanodeID dn_id{kInvalidDatanodeID};
  DatanodeID random_id{kInvalidDatanodeID};
  DatanodeInfoPtr dn_ptr{nullptr};
  bool exist{false};
  bool is_local_dc{false};
  bool is_local_az{false};
  bool is_local_switch{false};
  bool is_local_host{false};
  bool is_stale{false};
  uint32_t dc_distance{0};
  uint64_t last_heartbeat{0};

  std::string ToString() const {
    std::ostringstream oss;
    oss << "["
        << "dn_id=" << dn_id << " random_id=" << random_id
        << " info=" << (dn_ptr == nullptr ? "nullptr" : dn_ptr->ToShortString())
        << " exist=" << exist << " is_local_dc=" << is_local_dc
        << " is_local_az=" << is_local_az
        << " is_local_switch=" << is_local_switch
        << " is_local_host=" << is_local_host << " is_stale=" << is_stale
        << " dc_distance=" << dc_distance
        << " last_heartbeat=" << last_heartbeat << "]";
    return oss.str();
  }
};

class RackNode {
 public:
  RackNode(const std::string& name, const std::string& dc)
      : name_(name), dc_(dc) {}

  void AddDatanode(const DatanodeInfoPtr& dn) {
    auto dn_it = datanodes_.find(dn);
    if (dn_it != datanodes_.end()) {
      VLOG(10) << "data node exists: " << dn->id();
      return;
    }

    datanodes_.insert(dn);
    datanodes_vec_.emplace_back(dn);
  }

  void RemoveDatanode(const DatanodeInfoPtr& dn) {
    if (dn == nullptr) {
      return;
    }

    datanodes_.erase(dn);
    for(auto it = datanodes_vec_.begin();
        it != datanodes_vec_.end();
        it++) {
      if ((*it)->id() == dn->id()) {
        datanodes_vec_.erase(it);
        break;
      }
    }
  }

  std::string GetName() { return name_; }

  bool Has(const DatanodeInfoPtr& dn) {
    return datanodes_.find(dn) != datanodes_.end();
  }

  const std::vector<DatanodeInfoPtr>& GetDatanodes() { return datanodes_vec_; }

  void ListAll(std::string* output) {
    for (const auto& it : datanodes_) {
      output->append(std::to_string(it->id()) + " ");
    }
    output->append("\n");
  }

  void GetAllDatanodes(std::unordered_set<DatanodeInfoPtr>* dns) {
    for (const auto& it : datanodes_) {
        dns->emplace(it);
    }
  }

  void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) {
    dns->reserve(dns->size() + datanodes_.size());
    dns->insert(dns->end(), datanodes_.begin(), datanodes_.end());
  }

 private:
  std::string name_;
  std::string dc_;
  std::unordered_set<DatanodeInfoPtr> datanodes_;
  std::vector<DatanodeInfoPtr> datanodes_vec_;
};

typedef std::shared_ptr<RackNode> RackNodePtr;

extern const std::vector<DatanodeInfoPtr> kDefaultFavored;

class DetailedBlock;
class BlockPlacement;
class BlockPlacementDefault;
class BlockPlacementMultiDC;
class BlockPlacementNodeZone;
class NodeZonePolicy;

class ConfigRefresher {
 public:
  explicit ConfigRefresher(BlockPlacement *policy) : policy_(policy) {}

  ~ConfigRefresher() {
    Stop();
  }

  void Start() {
    CHECK(!worker_.get());
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(
            new Task(this, period_)), "config-refresher");
    worker_->Start();
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

 private:
  BlockPlacement *policy_;
  std::chrono::milliseconds period_{10 * 1000};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;


  friend class Task;

  class Task : public cnetpp::concurrency::Task {
   public:
    Task(ConfigRefresher *refresher, std::chrono::milliseconds period)
        : refresher_(refresher), period_(period) {
    }

    bool operator()(void *arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

   private:
    ConfigRefresher *refresher_;
    std::chrono::milliseconds period_;
    std::mutex mu_;
    std::condition_variable cond_;
  };

  void Do();
};

class BlockPlacement {
 public:
  explicit BlockPlacement(bool with_refresher=false);
  virtual ~BlockPlacement();
  virtual void AddDatanode(DatanodeInfoPtr dn) = 0;
  virtual void RemoveDatanode(DatanodeInfoPtr dn) = 0;

  // ctor-Start-Stop-dtor symmetrically
  void Start();
  void Stop();

  // choose `rep_num` datanodes for `writer` to add
  // @param src_path The file to which this chooseTargets is being invoked.
  // @param rep_num  Additional number of replicas wanted.
  // @param advice The placement advice according to directory replica policy
  // @param blocksize The size of the data to be written.
  // @param writer_dn The writer's machine, null if not in the cluster.
  // @param writer_from The DataCenters where the writer's machine is located.
  // @param favored_nodes Datanodes that should be favored as targets.
  //                      This is only a hint and namenode may not be able to
  //                      place the blocks on these datanodes.
  // @param included Datanodes that have been chosen as targets.
  // @ref excluded Datanodes that should not be considered as targets.
  //               If a Datanodes is selected, it will be added to this list
  //               (whether or not it is added to the results).
  // @out result Array of datanode instances chosen as target
  virtual bool ChooseTarget4New(
      const std::string& src_path,
      int32_t rep_num,
      uint32_t blocksize,
      const PlacementAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result) = 0;

  // Currently, ChooseTarget4Read is only used in CloudFS.
  // HDFS doesn't need to implement it.
  virtual bool ChooseTarget4Read(
      const DetailedBlock& detailed_block,
      int32_t rep_num,
      const ReadAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result);
  virtual void ChooseFunctionalTarget4Read(
      const DetailedBlock& detailed_block,
      const ReadAdvice& advice,
      std::vector<DatanodeInfoPtr>* result);

  // choose `rep_num` datanodes for `writer` to re-replicate
  // @param src_path The file to which this chooseTargets is being invoked.
  // @param rep_num  Additional number of replicas wanted.
  // @param advice The placement advice according to directory replica policy
  // @param blocksize The size of the data to be written.
  // @param writer_dn The writer's machine, null if not in the cluster.
  // @param favored_nodes Datanodes that should be favored as targets.
  //                      This is only a hint and namenode may not be able to
  //                      place the blocks on these datanodes.
  // @param included Datanodes that have been chosen as targets.
  // @ref excluded Datanodes that should not be considered as targets.
  //               If a Datanodes is selected, it will be added to this list
  //               (whether or not it is added to the results).
  // @out result Array of datanode instances chosen as target
  virtual bool ChooseTarget4Recover(
      const std::string& src_path,
      int32_t rep_num,
      uint32_t blocksize,
      const PlacementAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      const std::unordered_set<DatanodeInfoPtr>& included,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result) = 0;
  virtual DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk,
      size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice& advice = kDefaultAdvice) = 0;

  virtual std::unordered_map<std::string, int>
  GetExpectedPlacement(const PlacementAdvice& advice, int32_t rep_num) = 0;

  virtual bool IsLocal(DatanodeInfoPtr dn,
                       const NetworkLocation& loc,
                       bool *same_rack,
                       bool *same_dc,
                       uint32_t *dc_distance) = 0;

  virtual bool DatanodeExist(DatanodeInfoPtr dn) = 0;

  virtual bool HasBeenMultiRack() = 0;
  virtual size_t NumRacks() = 0;
  virtual void ListAll(std::string* output) = 0;

  virtual void GetAllDatanodesInRack(const std::string& dc,
                                     const std::string& rack,
                                     std::unordered_set<DatanodeInfoPtr>* dns) = 0;

  virtual void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) = 0;

  virtual std::string GetBlacklistDc() const { return ""; }
  virtual std::string GetMajorityDc() const { return ""; }

  virtual bool RefreshConfig();

  virtual void RecalcAllDatanode() = 0;

  constexpr static int INVALID_PREFER_DC_ID = -2;

 protected:
  using PlacementRWLock = RWSpinlock;
  PlacementRWLock rwlock_;
  std::unique_ptr<ConfigRefresher> refresher_{nullptr};

 private:
  uint32_t network_config_version_{0};
};

class BlockPlacementDefault : public BlockPlacement {
 public:
  explicit BlockPlacementDefault(DatanodeManagerMetrics* metrics,
                                 bool with_refresher=false);
  ~BlockPlacementDefault() override = default;
  void AddDatanode(DatanodeInfoPtr dn) override;
  void RemoveDatanode(DatanodeInfoPtr dn) override;

  bool ChooseTarget4New(const std::string& src_path,
                        int32_t rep_num,
                        uint32_t blocksize,
                        const PlacementAdvice& advice,
                        const NetworkLocationInfo& client_location,
                        const std::vector<DatanodeInfoPtr>& favored_nodes,
                        std::unordered_set<DatanodeInfoPtr>* excluded,
                        std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Recover(const std::string& src_path,
                            int32_t rep_num,
                            uint32_t blocksize,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored_nodes,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeInfoPtr>* result) override;

  bool IsLocal(DatanodeInfoPtr dn,
               const NetworkLocation& loc,
               bool* same_rack,
               bool* same_dc,
               uint32_t* dc_distance) override;

  bool DatanodeExist(DatanodeInfoPtr dn) override;

  bool HasBeenMultiRack() override;
  size_t NumRacks() override;
  void ListAll(std::string* output) override;

  DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk, size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice &advice) override; //  NOLINT

  std::unordered_map<std::string, int>
  GetExpectedPlacement(const PlacementAdvice& advice, int32_t rep_num) override;

  void GetAllDatanodesInRack(const std::string& dc, const std::string& rack,
          std::unordered_set<DatanodeInfoPtr>* dns) override;

  void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) override;

  bool RefreshConfig() override;

  void RecalcAllDatanode() override;

 protected:
  struct WeightDatanodes {
    std::vector<DatanodeInfoPtr> dns;
    std::vector<uint32_t> prefix_weight_sum;
    uint32_t total_weight;
    uint32_t total_nodes;

    void CleanAndReserve(size_t size) {
      dns.clear();
      dns.reserve(size);
      prefix_weight_sum.clear();
      prefix_weight_sum.reserve(size);
      total_weight = 0;
      total_nodes = 0;
    }

    void Finish() {
      if (dns.empty()) {
        return;
      }
      for (auto dn : dns) {
        auto weight = dn->GetWriteableWeight();
        int last = prefix_weight_sum.empty() ? 0 : prefix_weight_sum.back();

        prefix_weight_sum.push_back(last + weight);
        total_weight += weight;
        total_nodes++;
      }
      CHECK(dns.size() == total_nodes);
      CHECK(prefix_weight_sum.size() == total_nodes);
      CHECK(prefix_weight_sum.back() == total_weight);
    }

    size_t Index4Weight(int32_t weight) const {
      CHECK(weight < total_weight && weight >= 0);
      return std::lower_bound(prefix_weight_sum.begin(),
                              prefix_weight_sum.end(), weight) -
             prefix_weight_sum.begin();
    }
  };
  struct ChooseTargetPerCpuContext {
    ReadWriteLock rwlock;
    std::unordered_map<std::string, WeightDatanodes> rack_wdns;
    WeightDatanodes all_wdns;

    std::mt19937 rand;
    std::uniform_int_distribution<uint32_t> dist;
    int64_t last_update_time_ms;
  };

  void RefreshCpuContext(ChooseTargetPerCpuContext* ctx, int64_t now_time_ms);

  virtual DatanodeInfoPtr ChooseReplicaToDelete(
      const std::unordered_set<DatanodeInfoPtr>& replicas);

  std::pair<int32_t, int32_t> GetMaxNodesPerRack(
      int32_t chosen_num, int32_t rep_num);

  virtual void AddDatanodeWithoutLock(DatanodeInfoPtr dn);
  void RemoveDatanodeWithoutLock(DatanodeInfoPtr dn);

  void GetAllDatanodesInRackUnsafe(const std::string& rack,
                                   std::unordered_set<DatanodeInfoPtr>* dns);
  void GetAllDatanodeInfoUnsafe(std::vector<DatanodeInfoPtr>* dns);

  bool ChooseTargetInternal(
      int32_t rep_num,
      uint8_t type_mask,
      uint32_t blocksize,
      DatanodeInfoPtr writer_dn,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      const std::unordered_set<DatanodeInfoPtr>& included,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::vector<DatanodeInfoPtr>* result);

  bool ChooseRandom(int32_t rep_num,
                    uint8_t type_mask,
                    uint32_t blocksize,
                    ChooseTargetPerCpuContext* ctx,
                    const std::unordered_set<DatanodeInfoPtr>& included,
                    std::unordered_set<DatanodeInfoPtr>* excluded,
                    int32_t max_nodes_per_rack,
                    std::vector<DatanodeInfoPtr>* result);

  bool ChooseLocalStorage(DatanodeInfoPtr writer_dn,
                          uint8_t type_mask,
                          uint32_t blocksize,
                          ChooseTargetPerCpuContext* ctx,
                          const std::unordered_set<DatanodeInfoPtr>& included,
                          std::unordered_set<DatanodeInfoPtr>* excluded,
                          int32_t max_nodes_per_rack,
                          std::vector<DatanodeInfoPtr>* result);

  bool ChooseRemoteRack(DatanodeInfoPtr dn,
                        uint8_t type_mask,
                        uint32_t blocksize,
                        ChooseTargetPerCpuContext* ctx,
                        const std::unordered_set<DatanodeInfoPtr>& included,
                        std::unordered_set<DatanodeInfoPtr>* excluded,
                        int32_t max_nodes_per_rack,
                        std::vector<DatanodeInfoPtr>* result);

  bool ChooseLocalRack(DatanodeInfoPtr dn,
                       uint8_t type_mask,
                       uint32_t blocksize,
                       ChooseTargetPerCpuContext* ctx,
                       const std::unordered_set<DatanodeInfoPtr>& included,
                       std::unordered_set<DatanodeInfoPtr>* excluded,
                       int32_t max_nodes_per_rack,
                       std::vector<DatanodeInfoPtr>* result);

  bool AddTarget(DatanodeInfoPtr dn,
                 uint8_t type_mask,
                 uint32_t blocksize,
                 const std::unordered_set<DatanodeInfoPtr>& included,
                 std::unordered_set<DatanodeInfoPtr>* excluded,
                 int32_t max_nodes_per_rack,
                 bool avoid_dying_dn,
                 std::vector<DatanodeInfoPtr>* result);

  // Return true if successfully chose a datanode as target.
  virtual bool ChooseFromRack(ChooseTargetPerCpuContext* ctx,
                              const RackNodePtr& rack,
                              uint8_t type_mask,
                              uint32_t blocksize,
                              const std::unordered_set<DatanodeInfoPtr>& included,
                              std::unordered_set<DatanodeInfoPtr>* excluded,
                              int32_t max_nodes_per_rack,
                              std::vector<DatanodeInfoPtr>* result);

  bool IsOnSameRack(DatanodeInfoPtr dn0, DatanodeInfoPtr dn1);

  std::unordered_set<DatanodeInfoPtr> datanodes_;
  // rackname ->rack
  std::unordered_map<std::string, RackNodePtr> racks_map_;

  // TODO(lijiehui): rethink if this flag is needed
  bool has_been_multi_rack_;

  DatanodeManagerMetrics* metrics_{nullptr};

  CpuLocal<ChooseTargetPerCpuContext> choose_target_ctx_;
};

typedef std::shared_ptr<BlockPlacementDefault> BlockPlacementDefaultPtr;
class BlockPlacementMultiDC;

class BlockPlacementMultiDC : public BlockPlacement {
 public:
  explicit BlockPlacementMultiDC(DatanodeManagerMetrics* metrics,
                                 bool with_refresher=false);
  virtual ~BlockPlacementMultiDC() override;

  void PrepareDC(const std::string& dc);

  void AddDatanode(DatanodeInfoPtr dn) override;
  void RemoveDatanode(DatanodeInfoPtr dn) override;

  bool ChooseTarget4New(const std::string& src_path,
                        int32_t rep_num,
                        uint32_t blocksize,
                        const PlacementAdvice& advice,
                        const NetworkLocationInfo& client_location,
                        const std::vector<DatanodeInfoPtr>& favored_nodes,
                        std::unordered_set<DatanodeInfoPtr>* excluded,
                        std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Recover(const std::string& src_path,
                            int32_t rep_num,
                            uint32_t blocksize,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored_nodes,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeInfoPtr>* result) override;

  std::unordered_map<std::string, int>
  GetExpectedPlacement(const PlacementAdvice& advice, int32_t rep_num) override;

  bool HasBeenMultiRack() override;
  size_t NumRacks() override;
  bool IsLocal(DatanodeInfoPtr dn,
               const NetworkLocation &loc,
               bool* same_rack,
               bool* same_dc,
               uint32_t* dc_distance) override;

  bool DatanodeExist(DatanodeInfoPtr dn) override;

  void ListAll(std::string* output) override;
  DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk, size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice &advice) override; // NOLINT

  void GetAllDatanodesInRack(const std::string& dc,
                             const std::string& rack,
                             std::unordered_set<DatanodeInfoPtr>* dns) override;

  void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) override;

  bool SetDCList(const std::string& type, const std::string& dc_str);

  bool RefreshConfig() override;

  void RecalcAllDatanode() override;

  std::string GetBlacklistDc() const override;
  std::string GetMajorityDc() const override;

 private:
  void AddDatanodeWithoutLock(DatanodeInfoPtr dn);
  void RemoveDatanodeWithoutLock(DatanodeInfoPtr dn);

  void GetAllDatanodeInfoUnsafe(std::vector<DatanodeInfoPtr>* dns);

  DatanodeInfoPtr ChooseReplicaToDelete(
      const std::unordered_set<DatanodeInfoPtr>& replicas,
      const std::unordered_set<std::string>& excluded_dcs);

  void ComputeDCList(const std::string &writer_dc,
                     std::vector<std::string>* result,
                     const PlacementAdvice& advice,
                     bool ignore_blacklist);

  std::string GetExpectedDC(DatanodeInfoPtr dn, const std::string& from);

  std::shared_ptr<BlockPlacementDefault> GetDC(const std::string& dc_name);

  std::unordered_set<DatanodeInfoPtr> datanodes_;
  std::unordered_map<std::string, std::shared_ptr<BlockPlacementDefault>> datacenters_;  // NOLINT(whitespace/line_length)
  DataCenters dc_info_;
  DataCentersTopology dc_topology_;
  std::vector<std::string> black_dc_list_;
  std::vector<std::string> majority_dc_list_;
  std::shared_ptr<VRWLock> rwlock_global_conf_;
  DatanodeManagerMetrics* metrics_{nullptr};

  std::string last_blacklist_dc_;
  std::string last_majority_dc_;
};

class BlockPlacementPolicyFactory {
 public:
  BlockPlacementPolicyFactory() = default;
  ~BlockPlacementPolicyFactory() = default;

  static std::unique_ptr<BlockPlacement> Create(
      const std::string& policy, bool with_start, DatanodeManagerMetrics* metrics);
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_PLACEMENT_H_
