#include "datanode_manager/block_placement.h"

#include "block_manager/block_manager.h"
#include "datanode_manager/block_placement_cfs_default.h"
#include "datanode_manager/block_placement_cfs_multidc.h"
#include "datanode_manager/block_placement_nodezone.h"

namespace dancenn {

// NOTICE: Start refresher_ in constructor is very dangerous!
// Consider the following steps:
// 1. Call constructor of BlockPlacement, and refresher_ is running.
// 2. refresher_ is running, so it calls virtual function RefreshConfig.
//    But BlockPlacementCfsDefault (it is a subclass of BlockPlacement)
//    isn't ready.
// 3. Call constructor of BlockPlacementCfsDefault.
// Is starting refresher_ in constructor of subclass a good idea?
BlockPlacement::BlockPlacement(bool with_refresher) {
  if (!refresher_ && with_refresher) {
    refresher_ = std::make_unique<ConfigRefresher>(this);
  }
}

// NOTICE: Stop refresher_ in destructor is very dangerous!
// Consider the following steps:
// 1. Call destructor of BlockPlacementCfsDefault
//    (it is a subclass of BlockPlacement).
// 2. refresher_ is still running, so it calls virtual function RefreshConfig
//    (expect to be BlockPlacementCfsDefault::RefreshConfig).
//    But some fields and virtual table pointer (?) are destroyed.
// 3. Call destructor of BlockPlacement and stop refresher_.
// https://stackoverflow.com/questions/10707286/how-to-resolve-pure-virtual-method-called
// UT may print:
// pure virtual method called
// terminate called without an active exception
// Is stopping refresher_ in destructor a good idea?
BlockPlacement::~BlockPlacement() {
  CHECK(refresher_ == nullptr) << "refresher_ must be Stop() by the caller.";
}

void BlockPlacement::Start() {
  if (refresher_) {
    refresher_->Start();
  }
}

void BlockPlacement::Stop() {
  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

bool BlockPlacement::ChooseTarget4Read(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  return false;
}

void BlockPlacement::ChooseFunctionalTarget4Read(
    const DetailedBlock& detailed_block,
    const ReadAdvice& advice,
    std::vector<DatanodeInfoPtr>* targets) {
}

bool BlockPlacement::RefreshConfig() {
  auto latest = ConfigBasedRackAware::GetSingleton().GetConfigVersion();
  if (network_config_version_ == 0 || latest > network_config_version_) {
    LOG(WARNING) << "Reload NetworkLocation Success!!!";
    network_config_version_ = latest;
    this->RecalcAllDatanode();
  }

  return true;
}

void ConfigRefresher::Do() { policy_->RefreshConfig(); }

bool ConfigRefresher::Task::operator()(void *arg) {
  (void)arg;
  while (!stop_) {
    refresher_->Do();
    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period_, [this]() -> bool { return stop_; });
  }
  return true;
}

std::unique_ptr<BlockPlacement> BlockPlacementPolicyFactory::Create(
    const std::string &policy,
    bool with_start,
    DatanodeManagerMetrics *metrics) {
  std::unique_ptr<BlockPlacement> placement;
  if (policy == "multi-dc") {
    placement = std::unique_ptr<BlockPlacement>(
        new BlockPlacementMultiDC(metrics, with_start));
  } else if (policy == "nodezone") {
    placement = std::unique_ptr<BlockPlacement>(
        new BlockPlacementNodeZone(metrics, with_start));
  } else if (policy == "default") {
    placement = std::unique_ptr<BlockPlacement>(
        new BlockPlacementDefault(metrics, with_start));
  } else if (policy == "cfs-default") {
    placement = std::unique_ptr<BlockPlacement>(
        new BlockPlacementCfsDefault(metrics, with_start));
  } else if (policy == "cfs-multi-dc") {
    placement = std::unique_ptr<BlockPlacement>(
        new BlockPlacementCfsMultiDC(metrics, with_start));
  } else {
    LOG(ERROR) << "Unknown placement policy: " << policy;
    return nullptr;
  }
  return std::move(placement);
}

} // namespace dancenn
