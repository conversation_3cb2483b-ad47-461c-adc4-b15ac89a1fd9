// Copyright 2017 Liyuan Lei <<EMAIL>>

#ifndef DATANODE_MANAGER_DATANODE_MANAGER_H_
#define DATANODE_MANAGER_DATANODE_MANAGER_H_

#include <DatanodeProtocol.pb.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <datanode_info.pb.h>

#include <atomic>
#include <chrono>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/dictionary.h"
#include "base/network_location_info.h"
#include "base/read_write_lock.h"
#include "base/status.h"
#include "block_manager/block_lifecycle_logger.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/datanode_manager_metrics.h"
#include "datanode_manager/storage_policy.h"
#include "op_task/op_task_manager.h"

namespace dancenn {

class BlockPlacement;
class MetaStorage;

class DatanodeManager {
  friend class NameSpaceBlockPlacementTest;

 public:
  explicit DatanodeManager(bool with_start = true);
  virtual ~DatanodeManager();
  void Start();
  void Stop();

  void LoadFromDB();
  // Volatile properties like IP/HOSTNAME will not be loaded
  bool LoadDatanodeInfo(const DatanodeID& dn_id, const DatanodeInfoEntryPB& pb);

  // Realtime brief stat
  virtual BriefStorageStat GetBriefStorageStat();
  // Non-realtime detailed stat
  StorageStat stat() {
    return stat_;
  }

  typedef google::protobuf::RepeatedPtrField<cloudfs::DatanodeInfoProto>
      RepeatedDatanodeInfo;
  typedef google::protobuf::RepeatedPtrField<
      cloudfs::DatanodeStorageReportProto>
      RepeatedDatanodeStorageInfo;

  typedef google::protobuf::RepeatedPtrField<
      cloudfs::datanode::DatanodeCommandProto>
      RepeatedCmds;

  // need_log for UT
  Status Register(const DatanodeIDProto& addr,
                  cloudfs::datanode::DatanodeRegistrationProto* registration,
                  const cnetpp::base::IPAddress& ip);

  Status Register(const DatanodeIDProto& addr,
                  cloudfs::datanode::DatanodeRegistrationProto* registration,
                  const cnetpp::base::IPAddress& ip,
                  ProductVersion version);

  void Heartbeat(const HeartbeatRequestProto& request, RepeatedCmds* cmds);

  void AddBlocks(DatanodeID dn_id,
                 const DatanodeStorageProto& storage_proto,
                 const std::vector<uint64_t>& to_add);
  std::vector<DatanodeID> AddBlockStorages(
      const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
          request,  // NOLINT(whitespace/line_length)
      uint64_t block_id);
  void RemoveBlocks(DatanodeID dn_id,
                    const DatanodeStorageProto& storage_proto,
                    const std::vector<uint64_t>& to_remove);

  void UpdateStaleNodes(const std::unordered_set<std::string>& all_nodes,
                        std::unordered_set<DatanodeID>* stale_dns);
  void UpdateStale2Nodes(const std::unordered_set<std::string>& all_nodes,
                         std::unordered_set<DatanodeID>* stale2_dns);

  std::vector<uint64_t> UpdateBlockReportStatus(
      const std::string& storage_uuid,
      DatanodeID dn_id,
      const BlockReportContextProto& context,
      bool is_last);
  void GetDatanodeListForReport(const DatanodeReportTypeProto& type,
                                const NetworkLocationInfo& client_location,
                                std::vector<DatanodeInfoPtr>* datanodes);

  Status GetDatanodeReport(const DatanodeReportTypeProto& type,
                           const NetworkLocationInfo& client_location,
                           RepeatedDatanodeInfo* datanode_info);

  Status GetDatanodeStorageReport(
      const DatanodeReportTypeProto& type,
      const NetworkLocationInfo& client_location,
      RepeatedDatanodeStorageInfo* repeated_storage_info);

  virtual size_t NumLiveDatanodes() const;

  bool IsDatanodeStale(DatanodeID dn_id);
  void SetDatanodeStale(DatanodeID dn_id, bool stale);
  // just for unittest
  void SetDecommissioning(DatanodeID dn_id);
  bool IsDecommissionInProgress(const cnetpp::base::IPAddress& ip);

  virtual Status SetDecommissioned(DatanodeID dn_id, bool force);

  std::vector<DatanodeInfoPtr> GetFavored4Decommission(
      const DnSet& decommission_dns);
  std::vector<DatanodeInfoPtr> GetAllDatanodeInfo();
  std::vector<DatanodeInfoPtr> GetDatanodeInfo(
      std::function<bool(DatanodeInfoPtr)> dn_state_filter);
  bool HasWriteableDatanode();
  virtual bool HasEnoughWriteableDatanode(int expected_dn_num);

  void GetRecoverLocations(const std::vector<DatanodeID>& dn_ids,
                           RepeatedDatanodeInfo* datanodes);

  virtual DatanodeID GetDatanodeInterId(const std::string& uuid) const {
    auto internal_id = internal_ids_.forward_lookup(uuid);
    return internal_id == kInvalidId ? kInvalidDatanodeID : internal_id;
  }
  DatanodeIDProto GetDatanodeId(DatanodeID dn_id);

  // For UT.
  bool RefreshConfig();
  void RefreshDatanodeMachineInfo();

  bool ChooseTarget4Recover(const std::string& src_path,
                            int32_t rep_num,
                            uint32_t blocksize,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeID>* targets);

  virtual bool ChooseTarget4New(const std::string& src_path,
                                int32_t exp_rep_num,
                                int32_t min_rep_num,
                                uint32_t blocksize,
                                const PlacementAdvice& advice,
                                const NetworkLocationInfo& client_location,
                                const std::vector<std::string>& favorednodes,
                                const RepeatedDatanodeInfo& excludes,
                                cloudfs::LocatedBlockProto* located_block,
                                std::vector<DatanodeID>* targets);

  bool ChooseTarget4Read(const DetailedBlock& detailed_block,
                         int32_t rep_num,
                         const ReadAdvice& advice,
                         const NetworkLocationInfo& client_location,
                         const RepeatedDatanodeInfo& excludes,
                         std::vector<DatanodeID>* targets);

  void ChooseFunctionalTarget4Read(const DetailedBlock& detailed_block,
                                   const ReadAdvice& advice,
                                   std::vector<DatanodeID>* targets);

  virtual DatanodeID ChooseBlockRecoverPrimary(
      std::unordered_map<DatanodeID, bool>* replicas);

  std::unordered_map<std::string, int> GetExpectedPlacement(
      const PlacementAdvice& advice,
      int32_t rep_num);

  void ConstructBlockCommand(const std::vector<DatanodeID>& storages,
                             StoragePolicyId storage_policy_id,
                             cloudfs::datanode::BlockCommandProto* blk_cmd);

  void ConstructLocatedBlock(const std::vector<DatanodeID>& storages,
                             StoragePolicyId storage_policy_id,
                             cloudfs::LocatedBlockProto* block);
  void ConstructLocatedBlocks(
      const std::vector<std::vector<DatanodeID>>& storages,
      StoragePolicyId storage_policy_id,
      cloudfs::LocatedBlocksProto* blocks);

  static bool LocatedBlockCmpNormal(const DatanodeIDWithPlacementInfo& a,
                                    const DatanodeIDWithPlacementInfo& b);
  static bool LocatedBlockCmpPreferLocal(const DatanodeIDWithPlacementInfo& a,
                                         const DatanodeIDWithPlacementInfo& b);
  static bool LocatedBlockCmpPreferCached(const DatanodeIDWithPlacementInfo& a,
                                          const DatanodeIDWithPlacementInfo& b);
  // Deprecated
  size_t ConstructSortedLocatedBlockV1(
      const std::vector<DatanodeID>& storages,
      const std::vector<DatanodeID>& storages_exist,
      ReadAdvice& advice,
      StoragePolicyId storage_policy_id,
      const NetworkLocationInfo& client_location,
      cloudfs::LocatedBlockProto* block);
  size_t ConstructSortedLocatedBlockV2(
      const std::vector<DatanodeID>& storages,
      const std::vector<DatanodeID>& storages_exist,
      ReadAdvice& advice,
      StoragePolicyId storage_policy_id,
      const NetworkLocationInfo& client_location,
      cloudfs::LocatedBlockProto* block);
  size_t ConstructSortedLocatedBlock(
      const std::vector<DatanodeID>& storages,
      const std::vector<DatanodeID>& storages_exist,
      ReadAdvice& advice,
      StoragePolicyId storage_policy_id,
      const NetworkLocationInfo& client_location,
      cloudfs::LocatedBlockProto* block);
  size_t ConstructSortedLocatedBlock(const std::vector<DatanodeID>& storages,
                                     ReadAdvice& advice,
                                     StoragePolicyId storage_policy_id,
                                     const NetworkLocationInfo& client_location,
                                     cloudfs::LocatedBlockProto* block);
  void ConstructLocatedSortedBlocks(
      const std::vector<std::vector<DatanodeID>>& block_dns,
      const std::vector<std::vector<DatanodeID>>& block_dns_exist,
      ReadAdvice& advice,
      StoragePolicyId storage_policy_id,
      const NetworkLocationInfo& client_location,
      cloudfs::LocatedBlocksProto* blocks);
  void ConstructLocatedSortedBlocks(
      const std::vector<std::vector<DatanodeID>>& block_dns,
      ReadAdvice& advice,
      StoragePolicyId storage_policy_id,
      const NetworkLocationInfo& client_location,
      cloudfs::LocatedBlocksProto* blocks);

  std::string GetDatanodeIpFromId(DatanodeID dn_id) const;

  virtual DatanodeInfoPtr GetDatanodeFromId(DatanodeID dn_id) const;

  DatanodeInfoPtr GetDatanodeFromIp(const cnetpp::base::IPAddress& ip) const;
  DatanodeInfoPtr GetDatanodeFromIp(const std::string& ip) const;
  DatanodeInfoPtr GetDatanodeFromSocket(const std::string& socket) const;
  DnSet IpSet2DnSet(const std::unordered_set<std::string>& ip_set) const;
  DnSet SocketSet2DnSet(
      const std::unordered_set<std::string>& socket_set) const;
  DnSet IpOrSocketSet2DnSet(
      const std::unordered_set<std::string>& string_set) const;

  virtual DatanodeInfoPtr GetDatanodeFromUuid(const std::string& uuid) const;

  NetworkLocation GetLocation(DatanodeID dn_id);
  bool EverBeenMultiRack();
  size_t NumRacks();
  DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk,
      size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice& advice);

  void MarkAllStaleAfterFailover();
  void MarkKeyUpdate();

  StoragePolicyPtr GetStoragePolicyByName(const std::string& name);
  StoragePolicyPtr GetStoragePolicyById(StoragePolicyId id);
  std::vector<StoragePolicyPtr> GetAllPolicies();

  void set_meta_storage(MetaStorage* ms) {
    CHECK(ms);
    meta_storage_ = ms;
  }

  void set_op_task_manager(OpTaskManager* op_task_manager) {
    CHECK(op_task_manager);
    op_task_manager_ = op_task_manager;
  }

  void set_block_manager(BlockManager* block_manager) {
    CHECK(block_manager);
    block_manager_ = block_manager;
  }

  void BlockIndexStats(std::deque<size_t>& d) const;
  void BlockIndexDetail(DatanodeID id, std::deque<NameBlockIndex*>& d) const;

  void ClearInvalidateBlock();
  void ClearTruncatableBlock();

  std::string GetBlacklistDc() const {
    return block_placement_->GetBlacklistDc();
  }

  std::string GetMajorityDc() const {
    return block_placement_->GetMajorityDc();
  }

  BlockPlacement* placement() {
    return block_placement_.get();
  }

  void StartActive() {
    is_active_ = true;
  }
  void StopActive() {
    is_active_ = false;
  }

  // public for unittest only
  void DumpDatanodeInfo2DB();
  void DumpDatanodeInfo2DB(DatanodeInfo* dn_info);

  void AddReplicaIsCached(cloudfs::LocatedBlockProto* block);

 private:
  DatanodeInfoPtr GetDatanodeFromHost(const std::string& host) const;
  virtual DatanodeInfoPtr GetDatanodeFromIdInternal(DatanodeID dn_id) const;
  DatanodeInfoPtr GetDatanodeFromIpInternal(const std::string& ip) const;
  DatanodeInfoPtr GetDatanodeFromSocketInternal(
      const std::string& socket) const;

  void AddStorageStat(const DatanodeInfoPtr& dn);
  void SubStorageStat(const DatanodeInfoPtr& dn);

  void DatanodeCheck();
  void ProcessDeadDatanode(DatanodeInfoPtr datanode);
  void ProcessDecommissionDatanode(DatanodeInfoPtr datanode);
  void UpdateReplicatedBlock(DatanodeInfoPtr datanode);

  std::vector<uint64_t> ProcessFailedStorages(
      const DatanodeInfoPtr dn_info,
      const std::unordered_set<std::string>& storages);

  std::chrono::milliseconds GetDatanodeKeepAliveTimeoutMs() const;

 private:
  MetaStorage* meta_storage_{nullptr};

  int64_t loaded_{0};

 private:
  using DNManagerRWLock = std::shared_timed_mutex;
  mutable DNManagerRWLock rwlock_;
  std::unique_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
  std::unique_ptr<BlockPlacement> block_placement_;
  // std::unordered_map<DatanodeID, DatanodeInfoPtr> datanodes_;
  // start from 1
  std::vector<DatanodeInfoPtr> datanodes_;
  std::atomic<DatanodeID> datanodes_size_{0};
  Dictionary<std::string, DatanodeID> internal_ids_;  // uuid <-> internal_id
  std::unordered_map<std::string, DatanodeID> ip_to_ids_;  // addr <-> inter_id
  std::unordered_map<std::string, DatanodeID>
      socket_to_ids_;  // addr+port <-> inter_id
  // std::chrono::milliseconds datanode_keep_alive_timeout_ms_;
  BlockManager* block_manager_{nullptr};
  OpTaskManager* op_task_manager_{nullptr};
  // Storage policies should be managed by datanode manager because it is
  // intended for choosing targets.
  const StoragePolicySuite& storage_policy_suite_ =
      GetGlobalStoragePolicySuite();

  StorageStat stat_;

  std::unordered_map<std::string, std::string> dn_vip_to_machine_;

  friend struct DatanodeManagerMetrics;
  DatanodeManagerMetrics metrics_;

  std::atomic<bool> is_active_{false};

  uint64_t start_time_{0};
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_DATANODE_MANAGER_H_
