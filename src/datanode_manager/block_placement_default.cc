// Copyright 2017 Li<PERSON> Lei <<EMAIL>>

#include "datanode_manager/block_placement.h"

#include <algorithm>
#include <chrono>
#include <cstddef>
#include <memory>
#include <random>
#include <shared_mutex>

#include "base/block_event_logger.h"
#include "base/data_center_table.h"
#include "base/stop_watch.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_tolerate_interval_misses_sec);
DECLARE_bool(avoid_stale_datanode_for_write);
DECLARE_int32(choose_target_context_refresh_interval_ms);

namespace dancenn {

BlockPlacementDefault::BlockPlacementDefault(DatanodeManagerMetrics* metrics,
                                             bool with_refresher)
    : BlockPlacement(with_refresher),
      has_been_multi_rack_(false),
      metrics_(metrics) {
  choose_target_ctx_.Each([] (ChooseTargetPerCpuContext* ctx) {
    ctx->rand.seed(std::chrono::system_clock::now().time_since_epoch().count());
    ctx->last_update_time_ms = 0;
  });
}

void BlockPlacementDefault::AddDatanode(DatanodeInfoPtr dn) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  if (datanodes_.count(dn)) {
    return;
  }

  AddDatanodeWithoutLock(dn);
  bool need_log = !FLAGS_run_ut;
  LOG_IF(INFO, need_log) << "add datanode " << dn->hostname() << ":"
                         << dn->ip().ToString() << " to topology "
                         << dn->GetLocationString();
}

void BlockPlacementDefault::AddDatanodeWithoutLock(DatanodeInfoPtr dn) {
  const auto& dc = GetGlobalDataCenterTable().Name(dn->dc());
  std::string rack = dn->rack();

  auto it = racks_map_.find(rack);

  if (racks_map_.find(rack) == racks_map_.end()) {
    if (!racks_map_.empty()) {
      has_been_multi_rack_ = true;
    }

    auto rack_ptr = std::make_shared<RackNode>(rack, dc);
    racks_map_.insert(std::make_pair(rack, rack_ptr));
  }
  racks_map_[rack]->AddDatanode(dn);
  datanodes_.insert(dn);
}

void BlockPlacementDefault::RemoveDatanode(DatanodeInfoPtr dn) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);

  if (datanodes_.find(dn) == datanodes_.end()) {
    return;
  }

  RemoveDatanodeWithoutLock(dn);
}

void BlockPlacementDefault::RemoveDatanodeWithoutLock(DatanodeInfoPtr dn) {
  const auto& dc = dn->dc_name();
  std::string rack = dn->rack();

  if (racks_map_.count(rack)) {
    racks_map_[rack]->RemoveDatanode(dn);
  }
  datanodes_.erase(dn);
  LOG(INFO) << " remove datanode " << dn->hostname() << ":"
            << dn->ip().ToString() << " location " << dn->GetLocationString();
}

bool BlockPlacementDefault::ChooseTarget4New(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr> &favored_nodes,
    std::unordered_set<DatanodeInfoPtr> *excluded,
    std::vector<DatanodeInfoPtr> *result) {
  CHECK_GE(rep_num, 0);
  auto storage_policy =
      GetGlobalStoragePolicySuite().GetPolicyFromId(advice.storage_policy_id);
  CHECK_NOTNULL(storage_policy);
  auto origin_rep_num = result->size();
  auto target_rep_num = origin_rep_num + rep_num;
  auto storage_types_mask = storage_policy->storage_types_mask();
  auto fallback_mask = storage_policy->creation_fallbacks_mask();
  std::unordered_set<DatanodeInfoPtr> included;

  std::shared_lock<PlacementRWLock> lock(rwlock_);
  auto ret = ChooseTargetInternal(rep_num,
                                  storage_types_mask,
                                  blocksize,
                                  client_location.dn,
                                  favored_nodes,
                                  included,
                                  excluded,
                                  result);

  if (result->size() != target_rep_num && fallback_mask != 0 &&
      storage_types_mask != fallback_mask) {
    // fallback
    ret = ChooseTargetInternal(target_rep_num - result->size(),
                               fallback_mask,
                               blocksize,
                               client_location.dn,
                               favored_nodes,
                               included,
                               excluded,
                               result);
  }
  return ret;
}

bool BlockPlacementDefault::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GE(rep_num, 0);
  auto storage_policy =
      GetGlobalStoragePolicySuite().GetPolicyFromId(advice.storage_policy_id);
  CHECK_NOTNULL(storage_policy);
  auto origin_rep_num = result->size();
  auto target_rep_num = origin_rep_num + rep_num;
  auto storage_types_mask = storage_policy->storage_types_mask();
  auto fallback_mask = storage_policy->replication_fallbacks_mask();

  std::shared_lock<PlacementRWLock> lock(rwlock_);

  auto ret = ChooseTargetInternal(rep_num,
                                  storage_types_mask,
                                  blocksize,
                                  client_location.dn,
                                  favored_nodes,
                                  included,
                                  excluded,
                                  result);

  if (result->size() != target_rep_num && fallback_mask != 0 &&
      fallback_mask != storage_types_mask) {
    // fallback
    ret = ChooseTargetInternal(target_rep_num - result->size(),
                               fallback_mask,
                               blocksize,
                               client_location.dn,
                               favored_nodes,
                               included,
                               excluded,
                               result);
  }
  return ret;
}

std::unordered_map<std::string, int>
BlockPlacementDefault::GetExpectedPlacement(const PlacementAdvice &advice,
                                            int32_t rep_num) {
  return {{"", rep_num}};
}

bool BlockPlacementDefault::IsLocal(DatanodeInfoPtr dn,
                                    const NetworkLocation& loc,
                                    bool* same_rack,
                                    bool* same_dc,
                                    uint32_t* dc_distance) {
  *same_dc = true;
  *dc_distance = 0;
  *same_rack = (loc.rack == dn->rack());
  return true;
}

bool BlockPlacementDefault::DatanodeExist(DatanodeInfoPtr dn) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);

  return datanodes_.find(dn) != datanodes_.end();
}

DatanodeInfoPtr BlockPlacementDefault::ChooseReplicaToDelete(
    const Block& blk, size_t replication,
    const std::unordered_set<DatanodeInfoPtr>& more_than_one,
    const std::unordered_set<DatanodeInfoPtr>& exactly_one,
    const PlacementAdvice &advice) {
  DatanodeInfoPtr replica = nullptr;
  replica = ChooseReplicaToDelete(more_than_one);
  if (replica == nullptr) {
    replica = ChooseReplicaToDelete(exactly_one);
  }
  BlockEventLogger::DeleteReplica(blk.id, replica->id());
  return replica;
}

DatanodeInfoPtr BlockPlacementDefault::ChooseReplicaToDelete(
    const std::unordered_set<DatanodeInfoPtr>& replicas) {
  std::chrono::time_point<std::chrono::system_clock> oldest_heartbeat
      = std::chrono::system_clock::now()
          - std::chrono::milliseconds(
          FLAGS_datanode_tolerate_interval_misses_sec * 1000);
  DatanodeInfoPtr oldest_dn = nullptr;
  DatanodeInfoPtr min_space_dn = nullptr;
  // Pick the node with the oldest heartbeat or with the least free space,
  // if all heartbeats are within the tolerable heartbeat interval
  for (auto replica_dn : replicas) {
    if (replica_dn->last_heartbeat() < oldest_heartbeat) {
      oldest_dn = replica_dn;
      oldest_heartbeat = replica_dn->last_heartbeat();
    }
    if (min_space_dn == nullptr ||
        replica_dn->stat().remaining < min_space_dn->stat().remaining) {
      min_space_dn = replica_dn;
    }
  }
  return oldest_dn != nullptr ? oldest_dn : min_space_dn;
}

bool BlockPlacementDefault::HasBeenMultiRack() {
  return has_been_multi_rack_;
}

size_t BlockPlacementDefault::NumRacks() {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  return racks_map_.size();
}

void BlockPlacementDefault::ListAll(std::string* output) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  for (auto it = racks_map_.begin(); it != racks_map_.end(); ++it) {
    output->append(it->first + ": ");
    it->second->ListAll(output);
  }
  output->append("\n");
}


std::pair<int32_t, int32_t> BlockPlacementDefault::GetMaxNodesPerRack(
    int32_t chosen_num, int32_t rep_num) {
  auto total_replica_num = chosen_num + rep_num;

  auto cluster_size = datanodes_.size();
  if (total_replica_num > cluster_size) {
    total_replica_num = cluster_size;
    rep_num = total_replica_num - chosen_num;
  }

  auto total_rack_num = racks_map_.size();
  if (total_replica_num <= 1 || total_rack_num == 1) {
    return std::make_pair(rep_num, total_replica_num);
  }
  auto max_nodes_per_rack = (total_replica_num - 1) / total_rack_num + 2;
  if (max_nodes_per_rack == total_replica_num) {
    --max_nodes_per_rack;
  }

  return std::make_pair(rep_num, max_nodes_per_rack);
}

// release ctx->lock before
void BlockPlacementDefault::RefreshCpuContext(
    ChooseTargetPerCpuContext* ctx,
    int64_t now_time_ms) {
  CHECK_NOTNULL(ctx);
  std::unique_lock<ReadWriteLock> wlock(ctx->rwlock);

  if (now_time_ms - ctx->last_update_time_ms <
      FLAGS_choose_target_context_refresh_interval_ms) {
    return;
  }

  auto& all_wdns = ctx->all_wdns;
  all_wdns.CleanAndReserve(datanodes_.size());

  ctx->rack_wdns.clear();
  ctx->rack_wdns.reserve(racks_map_.size());

  for (auto& pair : racks_map_) {
    auto& r = pair.second;
    auto& rack_wdns = ctx->rack_wdns[r->GetName()];
    rack_wdns.CleanAndReserve(r->GetDatanodes().size());

    for (auto& dn : r->GetDatanodes()) {
      if (!dn->IsWriteable()) {
        continue;
      }
      rack_wdns.dns.push_back(dn);
      all_wdns.dns.push_back(dn);
    }

    std::shuffle(rack_wdns.dns.begin(), rack_wdns.dns.end(), ctx->rand);
    rack_wdns.Finish();

    if (VLOG_IS_ON(10)) {
      LOG(WARNING) << "rack=" << r->GetName()
                   << " rack_dns.size()=" << r->GetDatanodes().size()
                   << " rack_wdns.dns.size()=" << rack_wdns.dns.size()
                   << " rack_wdns.total_nodes=" << rack_wdns.total_nodes
                   << " rack_wdns.total_weight=" << rack_wdns.total_weight;
      std::ostringstream oss;
      for (auto& dn : rack_wdns.dns) {
        oss << dn->ip().ToString() << " ";
      }
      LOG(WARNING) << oss.str();
    }
  }

  // all
  std::shuffle(all_wdns.dns.begin(), all_wdns.dns.end(), ctx->rand);
  all_wdns.Finish();

  if (VLOG_IS_ON(10)) {
    LOG(WARNING) << "all_wdns.dns.size() = " << all_wdns.dns.size()
                 << " all_wdns.total_nodes=" << all_wdns.total_nodes
                 << " all_wdns.total_weight=" << all_wdns.total_weight;
    std::ostringstream oss;
    for (auto& dn : ctx->all_wdns.dns) {
      oss << dn->ip().ToString() << " ";
    }
    LOG(WARNING) << oss.str();
  }

  ctx->last_update_time_ms = now_time_ms;
}

bool BlockPlacementDefault::ChooseTargetInternal(
    int32_t rep_num,
    uint8_t type_mask,
    uint32_t blocksize,
    DatanodeInfoPtr writer_dn,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  CHECK_GE(rep_num, 0);
  if (rep_num == 0) {
    VLOG(10) << "no need to find nodes for 0 replica";
    return true;
  }

  StopWatch sw(
      metrics_->block_placement_choose_target_process_favored_nodes_time_);
  sw.Start();

  // It could be possible that writer dn is not null but it does not
  // belong to current dc or nodezone. For example, multiple dc. In that case, we
  // set the writer dn to be null.
  if (writer_dn != nullptr && datanodes_.find(writer_dn) == datanodes_.end()) {
    writer_dn = nullptr;
  }

  // 'included' may contain the DNs of other DC
  int chosen_num = std::accumulate(included.begin(), included.end(), 0,
                                   [&](const int a, DatanodeInfoPtr b) {
                                     return a + datanodes_.count(b);
                                   });
  auto max_rack_info = GetMaxNodesPerRack(chosen_num, rep_num);
  bool is_enough_dn = rep_num == max_rack_info.first;
  VLOG(10) << "chosen_num: " << chosen_num << " rep_num: " << rep_num
           << " is_enough_dn: " << is_enough_dn
           << " real_rep_num: " << max_rack_info.first
           << " max_nodes_per_rack: " << max_rack_info.second;
  rep_num = max_rack_info.first;
  auto max_nodes_per_rack = max_rack_info.second;

  if (rep_num <= 0) {
    return is_enough_dn;
  }

  for (auto dn : favored_nodes) {
    if (AddTarget(dn,
                  type_mask,
                  blocksize,
                  included,
                  excluded,
                  max_nodes_per_rack,
                  /*avoid_dying_dn=*/true,
                  result)) {
      VLOG(10) << "favored node is chosen: " << writer_dn;
      if (--rep_num == 0) {
        VLOG(10) << "favored nodes is satisfying";
        return is_enough_dn;
      }
    } else {
      excluded->insert(dn);
    }
  }

  sw.NextStep(metrics_->block_placement_choose_target_shuffle_racks_time_);

  auto ctx = choose_target_ctx_.At();
  std::shared_lock<ReadWriteLock> rlock(ctx->rwlock);

  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now().time_since_epoch()).count();
  if (now - ctx->last_update_time_ms >=
      FLAGS_choose_target_context_refresh_interval_ms) {
    rlock.unlock();
    RefreshCpuContext(ctx, now);
    rlock.lock();
  }

  sw.NextStep();

  const size_t num_of_results = result->size();
  if (num_of_results == 0) {
    bool found = ChooseLocalStorage(writer_dn,
                                    type_mask,
                                    blocksize,
                                    ctx,
                                    included,
                                    excluded,
                                    max_nodes_per_rack,
                                    result);
    if (!found) {
      LOG(INFO) << "can not find first node";
      return false;
    }
    if (--rep_num == 0) {
      return is_enough_dn;
    }
  }

  const DatanodeInfoPtr dn0 = (*result)[0];
  if (num_of_results <= 1) {
    bool found = ChooseRemoteRack(dn0,
                                  type_mask,
                                  blocksize,
                                  ctx,
                                  included,
                                  excluded,
                                  max_nodes_per_rack,
                                  result);
    if (!found) {
      LOG(INFO) << "can not find second node";
      return false;
    }

    if (--rep_num == 0) {
      return is_enough_dn;
    }
  }

  if (num_of_results <= 2) {
    const DatanodeInfoPtr dn1 = (*result)[1];
    bool found;
    if (IsOnSameRack(dn0, dn1)) {
      found = ChooseRemoteRack(dn0,
                               type_mask,
                               blocksize,
                               ctx,
                               included,
                               excluded,
                               max_nodes_per_rack,
                               result);
    } else {
      found = ChooseLocalRack(dn1,
                              type_mask,
                              blocksize,
                              ctx,
                              included,
                              excluded,
                              max_nodes_per_rack,
                              result);
    }

    if (!found) {
      LOG(INFO) << "can not find 3rd node";
      return false;
    }

    if (--rep_num == 0) {
      return is_enough_dn;
    }
  }

  return ChooseRandom(rep_num,
                      type_mask,
                      blocksize,
                      ctx,
                      included,
                      excluded,
                      max_nodes_per_rack,
                      result) &&
         is_enough_dn;
}

bool BlockPlacementDefault::ChooseLocalStorage(
    DatanodeInfoPtr writer_dn,
    uint8_t type_mask,
    uint32_t blocksize,
    ChooseTargetPerCpuContext* ctx,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    std::vector<DatanodeInfoPtr>* result) {
  if (AddTarget(writer_dn,
                type_mask,
                blocksize,
                included,
                excluded,
                max_nodes_per_rack,
                /*avoid_dying_dn=*/false,
                result)) {
    VLOG(10) << "Local node is chosen: " << writer_dn;
    return true;
  }

  return ChooseLocalRack(writer_dn,
                         type_mask,
                         blocksize,
                         ctx,
                         included,
                         excluded,
                         max_nodes_per_rack,
                         result);
}

bool BlockPlacementDefault::ChooseLocalRack(
    DatanodeInfoPtr writer_dn,
    uint8_t type_mask,
    uint32_t blocksize,
    ChooseTargetPerCpuContext* ctx,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    std::vector<DatanodeInfoPtr>* result) {
  if (writer_dn != nullptr && racks_map_.count(writer_dn->rack())) {
    RackNodePtr rack = racks_map_[writer_dn->rack()];
    CHECK_NOTNULL(rack);

    if (ChooseFromRack(ctx, rack, type_mask, blocksize, included, excluded,
                       max_nodes_per_rack, result)) {
      VLOG(10) << "found a node in rack of node: " << writer_dn;
      return true;
    }
  }

  return ChooseRandom(1, type_mask, blocksize, ctx, included, excluded,
                      max_nodes_per_rack, result);
}

bool BlockPlacementDefault::ChooseRemoteRack(
    DatanodeInfoPtr dn,
    uint8_t type_mask,
    uint32_t blocksize,
    ChooseTargetPerCpuContext* ctx,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    std::vector<DatanodeInfoPtr>* result) {
  StopWatch sw(
      metrics_->block_placement_choose_target_choose_remote_rack_time_);
  sw.Start();

  RackNodePtr local_rack = nullptr;

  auto dn_it = datanodes_.find(dn);
  if (dn_it != datanodes_.end()) {
    local_rack = racks_map_[dn->rack()];
  } else {
    // dn may deleted by copyset
    local_rack = nullptr;
  }

  const auto& wdns = ctx->all_wdns;
  if (wdns.total_weight == 0 || wdns.total_nodes == 0) {
    if (VLOG_IS_ON(10)) {
      VLOG(10) << "wdns.total_weight == 0 || wdns.total_nodes == 0";
    }
    return false;
  }

  // NOTICE: Variable start_weight is just a random number, it specifies a rack.
  size_t start_weight = ctx->dist(ctx->rand) % wdns.total_weight;
  size_t start = wdns.Index4Weight(start_weight);
  for (int i = 0; i < wdns.total_nodes; ++i) {
    size_t now = (start + i) % wdns.total_nodes;
    auto target_dn = wdns.dns[now];

    if (local_rack && local_rack->Has(target_dn)) {
      continue;
    }

    if (AddTarget(target_dn,
                  type_mask,
                  blocksize,
                  included,
                  excluded,
                  max_nodes_per_rack,
                  /*avoid_dying_dn=*/true,
                  result)) {
      return true;
    }
  }

  if (VLOG_IS_ON(10)) {
    VLOG(10) << "Fall back to local rack.";
  }
  return ChooseFromRack(ctx, local_rack, type_mask, blocksize, included, excluded,
                        max_nodes_per_rack, result);
}

bool BlockPlacementDefault::ChooseRandom(
    int32_t rep_num,
    uint8_t type_mask,
    uint32_t blocksize,
    ChooseTargetPerCpuContext* ctx,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    std::vector<DatanodeInfoPtr>* result) {
  StopWatch sw(metrics_->block_placement_choose_target_choose_random_time_);
  sw.Start();

  const auto& wdns = ctx->all_wdns;
  if (wdns.total_weight == 0 || wdns.total_nodes == 0) {
    if (VLOG_IS_ON(10)) {
      VLOG(10) << "wdns.total_weight == 0 || wdns.total_nodes == 0";
    }
    return false;
  }

  VLOG(15) << "BlockPlacementDefault::ChooseRandom rep_num: " << rep_num
           << ", total: " << wdns.total_nodes
           << ", total_weight: " << wdns.total_weight;

  while (rep_num > 0) {
    size_t start_weight = ctx->dist(ctx->rand) % wdns.total_weight;
    size_t start = wdns.Index4Weight(start_weight);
    int i = 0;
    for (; i < wdns.total_nodes; ++i) {
      size_t now = (start + i) % wdns.total_nodes;
      auto target_dn = wdns.dns[now];

      if (AddTarget(target_dn,
                    type_mask,
                    blocksize,
                    included,
                    excluded,
                    max_nodes_per_rack,
                    /*avoid_dying_dn,=*/true,
                    result)) {
        VLOG(15) << "BlockPlacementDefault::ChooseRandom add target";
        rep_num--;
        break;
      } else {
        VLOG(15) << "BlockPlacementDefault::ChooseRandom add target failed";
      }
    }
    if (i == wdns.total_nodes) {
      break;
    }
  }

  // allow dying dn
  while (rep_num > 0) {
    size_t start_weight = ctx->dist(ctx->rand) % wdns.total_weight;
    size_t start = wdns.Index4Weight(start_weight);
    int i = 0;
    for (; i < wdns.total_nodes; ++i) {
      size_t now = (start + i) % wdns.total_nodes;
      auto target_dn = wdns.dns[now];

      if (AddTarget(target_dn,
                    type_mask,
                    blocksize,
                    included,
                    excluded,
                    max_nodes_per_rack,
                    /*avoid_dying_dn=*/false,
                    result)) {
        VLOG(15) << "BlockPlacementDefault::ChooseRandom add target";
        rep_num--;
        break;
      } else {
        VLOG(15) << "BlockPlacementDefault::ChooseRandom add target failed";
      }
    }
    if (i == wdns.total_nodes) {
      break;
    }
  }

  VLOG(15) << "BlockPlacementDefault::ChooseRandom finished: rep_num: "
           << rep_num;

  return rep_num == 0;
}

bool BlockPlacementDefault::IsOnSameRack(DatanodeInfoPtr dn0,
                                         DatanodeInfoPtr dn1) {
  return dn0->rack() == dn1->rack();
}

void BlockPlacementDefault::GetAllDatanodesInRack(const std::string& /* dc */,
    const std::string& rack, std::unordered_set<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  GetAllDatanodesInRackUnsafe(rack, dns);
}

void BlockPlacementDefault::GetAllDatanodesInRackUnsafe(const std::string& rack,
    std::unordered_set<DatanodeInfoPtr>* dns) {
  auto itr = racks_map_.find(rack);
  if (itr != racks_map_.end()) {
      itr->second->GetAllDatanodes(dns);
  }
}

void BlockPlacementDefault::GetAllDatanodeInfo(
    std::vector<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  GetAllDatanodeInfoUnsafe(dns);
}

void BlockPlacementDefault::GetAllDatanodeInfoUnsafe(
    std::vector<DatanodeInfoPtr>* dns) {
  std::for_each(racks_map_.begin(), racks_map_.end(),
                [=](const auto& pair) { pair.second->GetAllDatanodeInfo(dns); });
}

bool BlockPlacementDefault::RefreshConfig() {
  return BlockPlacement::RefreshConfig();
}

void BlockPlacementDefault::RecalcAllDatanode() {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    auto duration_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
            .count();

    LOG(INFO) << "RecalcAllDatanode cost: " << duration_ms << "ms";
  });

  std::unique_lock<PlacementRWLock> lock(rwlock_);
  LOG(INFO) << "RecalcAllDatanode Start";

  std::vector<DatanodeInfoPtr> dns;
  this->GetAllDatanodeInfoUnsafe(&dns);
  has_been_multi_rack_ = false;
  datanodes_.clear();
  racks_map_.clear();

  LOG(INFO) << "RecalcAllDatanode dn.size(): " << dns.size();

  for (const auto& dn : dns) {
    dn->RefreshLocation();
    this->AddDatanodeWithoutLock(dn);
    LOG(INFO) << "Recalc datanode " << dn->hostname() << ":"
              << dn->ip().ToString() << " to topology "
              << dn->GetLocationString();
  }
}

bool BlockPlacementDefault::AddTarget(
    DatanodeInfoPtr dn,
    uint8_t type_mask,
    uint32_t blocksize,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    bool avoid_dying_dn,
    std::vector<DatanodeInfoPtr>* result) {
  if (dn == nullptr) {
    return false;
  }

  if (datanodes_.find(dn) == datanodes_.end()) {
    // copyset may remove this dn, recheck it
    return false;
  }

  if (excluded->find(dn) != excluded->end() ||
      included.find(dn) != included.end()) {
    VLOG(10) << "excluded dn D#" << dn->id();
    return false;
  }

  if ((dn->storage_type() & type_mask) == 0) {
    VLOG(10) << "type mismatch dn D#" << dn->id()
             << ": dn_type=" << static_cast<uint32_t>(dn->storage_type())
             << ", type_mask=" << static_cast<uint32_t>(type_mask);
    return false;
  }

  if (!dn->IsAlive()) {
    VLOG(10) << "dn not alive dn " << dn->id();
    return false;
  }

  if (FLAGS_avoid_stale_datanode_for_write && dn->IsStale()) {
    VLOG(10) << "dn is stale " << dn->id();
    return false;
  }

  if (!dn->CheckLoad(blocksize)) {
    VLOG(10) << "dn overloaded dn D#" << dn->id();
    return false;
  }

  int count = 1;
  for (auto included_dn : included) {
    if (dn->dc() == included_dn->dc() && dn->rack() == included_dn->rack()) {
      ++count;
    }
  }
  if (count > max_nodes_per_rack) {
    VLOG(10) << "the rack has too many chosen nodes D#" << dn->id();
    return false;
  }

  result->push_back(dn);
  excluded->insert(dn);

  VLOG(10) << "chose dn D#" << dn->id();
  return true;
}

bool BlockPlacementDefault::ChooseFromRack(
    ChooseTargetPerCpuContext* ctx,
    const RackNodePtr& rack,
    uint8_t type_mask,
    uint32_t blocksize,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    int32_t max_nodes_per_rack,
    std::vector<DatanodeInfoPtr>* result) {
  StopWatch sw(metrics_->block_placement_choose_target_choose_from_rack_time_);
  sw.Start();

  VLOG(10) << "choosing from rack " << rack->GetName();

  if (ctx->rack_wdns.find(rack->GetName()) == ctx->rack_wdns.end()) {
    VLOG(10) << "unable to find rack: " << rack->GetName();
    return false;
  }
  const auto& wdns = ctx->rack_wdns[rack->GetName()];
  if (wdns.total_nodes == 0 || wdns.total_weight == 0) {
    VLOG(10) << "unable to find any node in rack: " << rack->GetName();
    return false;
  }

  size_t start_weight = ctx->dist(ctx->rand) % wdns.total_weight;
  size_t start = wdns.Index4Weight(start_weight);
  for (int i = 0; i < wdns.total_nodes; ++i) {
    size_t now = (start + i) % wdns.total_nodes;
    auto target_dn = wdns.dns[now];

    if (AddTarget(target_dn,
                  type_mask,
                  blocksize,
                  included,
                  excluded,
                  max_nodes_per_rack,
                  /*avoid_dying_dn=*/true,
                  result)) {
      return true;
    }
  }

  VLOG(10) << "unable to find any node in rack: " << rack->GetName();
  return false;
}

}  // namespace dancenn
