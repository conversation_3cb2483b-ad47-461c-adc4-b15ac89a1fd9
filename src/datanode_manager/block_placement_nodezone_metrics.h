// Copyright 2019 jiangxunyang <<EMAIL>>

#ifndef DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_METRICS_H_
#define DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_METRICS_H_

#include <memory>

#include "base/rack_aware.h"
#include "base/metrics.h"
#include "datanode_manager/block_placement_nodezone.h"
#include "datanode_manager/data_centers.h"

#define GET_METRICS_NODEZONE_ALLOC_BLOCK_KEY(nodezone_id, dc) nodezone_id + "." + dc

namespace dancenn {

class BlockPlacementNodeZoneMetrics {
 public:
  explicit BlockPlacementNodeZoneMetrics(BlockPlacementNodeZone* bp);
  ~BlockPlacementNodeZoneMetrics() = default;

  BlockPlacementNodeZoneMetrics(const BlockPlacementNodeZoneMetrics&) = delete;
  BlockPlacementNodeZoneMetrics& operator=(const BlockPlacementNodeZoneMetrics&) = delete;

  void PrepareNodeZoneMetrics(const NodeZonePtr& nodezone);
  void IncNodeZoneAllocBlocks(const ZoneId& zone_id,
      std::vector<DatanodeInfoPtr>* result);
  void IncChooseTarget4NewFailover(const ZoneGroupId& zone_group_id);
  void IncChooseTarget4RecoverFailover(const ZoneGroupId& zone_group_id);

 private:
  std::unordered_map<std::string, MetricID> nodezone_alloc_block_;
  std::unordered_map<std::string, MetricID> choose_target4new_failover_;
  std::unordered_map<std::string, MetricID> choose_target4recover_failover_;
  DataCenters dc_info_;
  std::shared_ptr<Metrics> metrics_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_METRICS_H_

