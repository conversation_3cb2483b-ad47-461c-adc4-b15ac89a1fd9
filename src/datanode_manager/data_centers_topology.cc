// Copyright 2019 <PERSON>ong Mu <<EMAIL>>

#include "datanode_manager/data_centers_topology.h"

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/string_utils.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include "base/data_center_table.h"
#include "base/file_utils.h"

DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);

namespace dancenn {

DataCentersTopology::DataCentersTopology()
    : rwlock_(FLAGS_client_slow_rpc_handler_count +
              FLAGS_client_normal_rpc_handler_count) {}

bool DataCentersTopology::ReloadFromFile(const std::string& config_filepath) {
  RandomAccessFile raf(config_filepath);

  auto size = raf.Size();
  if (size < 0) {
    LOG(ERROR) << "Failed to get size of file: " << config_filepath;
    return false;
  }

  auto buffer = std::unique_ptr<char[]>(new char[size]);
  cnetpp::base::StringPiece result;
  if (!raf.Read(0, buffer.get(), size, &result)) {
    LOG(ERROR) << "Failed to read contents from file: " << config_filepath;
    return false;
  }

  return ParseJsonAndUpdateConfig(result.as_string());
}

bool DataCentersTopology::ParseJsonAndUpdateConfig(
    const std::string& config_string) {
  if (config_string == config_string_) {
    return true;
  }
  config_string_ = config_string;

  LOG(INFO) << "New DataCentersTopology config_string: " << config_string;

  // Deserialize
  cnetpp::base::Value received_json;
  if (!cnetpp::base::Parser::Deserialize(config_string, &received_json) ||
      !received_json.IsArray()) {
    LOG(ERROR) << "config_string is not valid json array.";
    return false;
  }

  std::vector<Edge> edges;

  auto edge_arr = received_json.AsArray();
  for (auto itr = edge_arr.Begin(); itr != edge_arr.End(); ++itr) {
    if (!itr->IsArray()) {
      LOG(ERROR) << "item not json array.";
      return false;
    }

    auto item = itr->AsArray();

    if (item.Size() != 2 || !item[0].IsString() || !item[0].IsString()) {
      LOG(ERROR) << "item not valid.";
      return false;
    }

    std::string u = item[0].AsString();
    std::string v = item[1].AsString();

    if (u.empty() || v.empty()) {
      LOG(ERROR) << "dc name is empty.";
      return false;
    }

    edges.emplace_back(Edge(GetGlobalDataCenterTable().ID(u),
                            GetGlobalDataCenterTable().ID(v)));
  }

  return UpdateConfig(edges);
}

uint32_t GetDistanceFromMap(const DataCentersTopology::DistanceMap& d,
                            const size_t& dc1,
                            const size_t& dc2) {
  if (dc1 == dc2) {
    return 0;
  }
  auto iter = d.find(DataCentersTopology::Edge(dc1, dc2));
  if (iter == d.end()) {
    return UINT_MAX;
  } else {
    return iter->second;
  }
}

bool DataCentersTopology::UpdateConfig(const std::vector<Edge>& edges) {
  DistanceMap d;

  std::unordered_set<size_t> nodes;
  for (auto& edge : edges) {
    LOG(INFO) << "The connection between dc: "
              << GetGlobalDataCenterTable().ToString(edge.first) << ","
              << GetGlobalDataCenterTable().ToString(edge.second);
    nodes.insert(edge.first);
    nodes.insert(edge.second);
    d[edge] = 1;
    d[Edge(edge.second, edge.first)] = 1;
  }

  // Floyd
  for (auto& by : nodes)
    for (auto& from : nodes) {
      if (GetDistanceFromMap(d, from, by) == UINT_MAX) {
        continue;
      }

      for (auto& to : nodes) {
        if (GetDistanceFromMap(d, by, to) == UINT_MAX) {
          continue;
        }

        auto dis =
            GetDistanceFromMap(d, from, by) + GetDistanceFromMap(d, by, to);
        if (GetDistanceFromMap(d, from, to) > dis) {
          d[Edge(from, to)] = dis;
        }
      }
    }

  vunique_lock guard(rwlock_.lock());
  distance_.swap(d);

  return true;
}

uint32_t DataCentersTopology::GetDistance(const std::string& dc1,
                                          const std::string& dc2) {
  return GetDistance(GetGlobalDataCenterTable().ID(dc1),
                     GetGlobalDataCenterTable().ID(dc2));
}

uint32_t DataCentersTopology::GetDistance(size_t dc1, size_t dc2) {
  vshared_lock guard(rwlock_.lock());

  return GetDistanceFromMap(distance_, dc1, dc2);
}

}  // namespace dancenn
