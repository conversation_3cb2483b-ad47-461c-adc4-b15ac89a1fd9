// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "datanode_manager/datanode_info.h"

#include <sys/socket.h>

#include <bitset>
#include <random>
#include <shared_mutex>

#include "base/data_center_table.h"
#include "base/pb_converter.h"
#include "base/rack_aware.h"
#include "base/read_write_lock.h"
#include "base/stop_watch.h"
#include "block_manager/block_manager_metrics.h"
#include "hdfs.pb.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_int32(datanode_dying_interval_ms);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_bool(avoid_stale_datanode_for_read);
DECLARE_bool(avoid_stale_datanode_for_write);
DECLARE_int32(blockmap_invalidate_limit_per_dn);
DECLARE_int32(blockmap_truncatable_limit_per_dn);
DECLARE_uint64(dfs_block_size);
DECLARE_bool(enable_location_tag);
DECLARE_uint64(dn_writable_unit_capacity_bytes_unit);
DECLARE_bool(dn_writable_use_total_capacity_factor);

namespace dancenn {

void NameBlockIndex::ToStream(std::ostringstream& os) const {
  os << "{";
  os << "\"name\": \"" << name << "\",";
  {
    auto& v = blocks[0];
    os << "\"base\": [";
    if (v.size() > 0) os << v[0];
    for (size_t i = 0; i < v.size(); i++) {
      os << "," << v[i];
    }
    os << "],";
  }
  {
    auto& v = blocks[1];
    os << "\"add\": [";
    if (v.size() > 0) os << v[0];
    for (size_t i = 0; i < v.size(); i++) {
      os << "," << v[i];
    }
    os << "],";
  }
  {
    auto& v = blocks[2];
    os << "\"del\": [";
    if (v.size() > 0) os << v[0];
    for (size_t i = 0; i < v.size(); i++) {
      os << "," << v[i];
    }
    os << "]";
  }
  os << "}";
}

DatanodeInfo::DatanodeInfo(uint32_t internal_id,
                           const DatanodeIDProto& addr,
                           const cnetpp::base::IPAddress& ip)
    : current_address_(nullptr),
      current_ip_(nullptr),
      current_socket_address_(nullptr),
      current_physical_machine_(nullptr),
      current_rack_(nullptr),
      current_nodezone_id_(nullptr),
      uuid_(addr.datanodeuuid()),
      internal_id_(internal_id),
      storage_types_(0),
      status_(DatanodeReportTypeProto::LIVE),
      admin_state_(DatanodeInfoProto_AdminState_NORMAL) {
  current_socket_address_.store(&*socket_addresses_.insert("").first);
  current_physical_machine_.store(&*physical_machines_.insert("").first);
  current_rack_.store(&*racks_.insert("").first);
  current_nodezone_id_.store(&*nodezone_ids_.insert("").first);

  set_address(addr, ip);
  last_heartbeat_ = std::chrono::system_clock::time_point::min();

  ResetLastNormalFbrTimepoint();
  ResetLastFastFbrTimepoint();
}

const DatanodeIDProto& DatanodeInfo::address() const {
  return *CHECK_NOTNULL(current_address_.load(std::memory_order_relaxed));
}

const std::string& DatanodeInfo::hostname() const {
  return CHECK_NOTNULL(current_address_.load(std::memory_order_relaxed))
      ->hostname();
}

const cnetpp::base::IPAddress& DatanodeInfo::ip() const {
  return *CHECK_NOTNULL(current_ip_.load(std::memory_order_relaxed));
}

const std::string& DatanodeInfo::socket_address() const {
  return *CHECK_NOTNULL(
      current_socket_address_.load(std::memory_order_relaxed));
}

void DatanodeInfo::set_address(DatanodeIDProto address,
                               const cnetpp::base::IPAddress& ip) {
  LOG(INFO) << "DatanodeInfo::set_address address="
            << address.ShortDebugString() << " ip=" << ip.ToString();
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  address.set_ipaddr(ip.ToString());
  current_address_.store(&*addresses_.insert(address).first);
  CHECK_NOTNULL(current_address_);
  current_ip_.store(&*ips_.insert(ip).first);
  CHECK_NOTNULL(current_ip_);

  // Set socket address.
  {
    auto ip_str = ip.ToString();
    if (current_ip_.load()->Family() == AF_INET6) {
      ip_str = "[" + ip_str + "]";
    }
    auto result =
        ip_str + ":" + std::to_string(current_address_.load()->xferport());
    current_socket_address_.store(&*socket_addresses_.insert(result).first);
  }

  // Set rack and dc.
  NetworkLocation loc;
  if (FLAGS_enable_location_tag) {
    auto location_tag = address.location_tag();
    if (location_tag.az().empty() || location_tag.switch_().empty() ||
        location_tag.host().empty()) {
      VLOG(10) << "fallback, location_tag has empty field, use config file. "
                  "location_tag="
               << location_tag.ShortDebugString();
      loc = ResolveNetworkLocation(this->ip());
      if (location_tag.az().empty()) {
        location_tag.set_az(loc.dc);
      }
      if (location_tag.switch_().empty()) {
        location_tag.set_switch_(loc.rack);
      }
      if (location_tag.host().empty()) {
        location_tag.set_host(loc.host);
      }
      VLOG(10) << "location_tag=" << location_tag.ShortDebugString();
    }

    set_location_tag(location_tag);
    loc = NetworkLocation(location_tag);
  } else {
    loc = ResolveNetworkLocation(this->ip());
  }
  dc_ = GetGlobalDataCenterTable().ID(loc.dc);
  current_rack_.store(&*racks_.insert(loc.rack).first);
}

const std::string& DatanodeInfo::physical_machine() const {
  return *current_physical_machine_.load(std::memory_order_relaxed);
}

void DatanodeInfo::set_physical_machine(const std::string& machine) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  current_physical_machine_.store(&*physical_machines_.insert(machine).first);
}

const std::string& DatanodeInfo::rack() const {
  return *current_rack_.load(std::memory_order_relaxed);
}

const std::string& DatanodeInfo::nodezone_id() const {
  return *current_nodezone_id_.load(std::memory_order_relaxed);
}

void DatanodeInfo::set_nodezone_id(const std::string& nodezone_id) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  current_nodezone_id_.store(&*nodezone_ids_.insert(nodezone_id).first);
}

LocationTag DatanodeInfo::location_tag() {
  return location_tag_;
}

void DatanodeInfo::set_location_tag(const LocationTag& location_tag) {
  location_tag_ = location_tag;
}

bool DatanodeInfo::IsDecommissionInProgress() const {
  return admin_state_ == DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS;
}

void DatanodeInfo::SetDecommissioning() {
  admin_state_ = DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS;
}

void DatanodeInfo::SetNonDecommissioning() {
  admin_state_ = DatanodeInfoProto_AdminState_NORMAL;
}

bool DatanodeInfo::IsDecommissioned() const {
  return admin_state_ == DatanodeInfoProto_AdminState_DECOMMISSIONED;
}

void DatanodeInfo::SetDecommissioned() {
  admin_state_ = DatanodeInfoProto_AdminState_DECOMMISSIONED;
}

StorageStat& DatanodeInfo::stat() {
  return stat_;
}

bool DatanodeInfo::CheckAndUpdateHeartbeat() {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (!IsAlive()) {
    return false;
  }
  last_heartbeat_ = std::chrono::system_clock::now();
  return true;
}

void DatanodeInfo::UpdateHeartbeat4Test(
    const std::chrono::system_clock::time_point last_heartbeat) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);

  last_heartbeat_ = last_heartbeat;
}

void DatanodeInfo::UpdateHeartbeat(bool heartbeated_since_registration) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  last_heartbeat_ = std::chrono::system_clock::now();
  status_ = DatanodeReportTypeProto::LIVE;
  heartbeated_since_failover_ = true;
  heartbeated_since_registration_ = heartbeated_since_registration;
}

bool DatanodeInfo::GetBlocks(std::vector<uint64_t>* blocks, size_t max_size) {
  CHECK_NOTNULL(blocks);

  std::vector<uint64_t> tmp;
  {
    std::unique_lock<ReadWriteLock> lock(rwlock_);

    for (auto& it : storages_) {
      auto storage = it.second;
      if (storage->failed()) {
        continue;
      }
      storage->GetBlocks(&tmp);
    }
  }

  std::random_device rd;
  std::mt19937 re(rd());
  if (max_size != INT_MAX && tmp.size() > max_size) {
    std::shuffle(tmp.begin(), tmp.end(), re);
    tmp.resize(max_size);
  }
  blocks->insert(blocks->end(), tmp.begin(), tmp.end());

  return true;
}

void DatanodeInfo::GetBlocksGroupByStorage(
    std::unordered_map<std::string, std::vector<BlockID>>* blks) {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  for (auto& it : storages_) {
    std::vector<BlockID> tmp;
    auto storage = it.second;
    if (storage->failed()) {
      continue;
    }
    storage->GetBlocks(&tmp);
    blks->insert({storage->uuid(), tmp});
  }
}

bool DatanodeInfo::MarkDead(std::vector<uint64_t>* blocks,
                            std::chrono::milliseconds keep_alive) {
  auto now = std::chrono::system_clock::now();
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (last_heartbeat_ + keep_alive > now) {
    // Datanode may report a heartbeat after we last check it
    return false;
  }
  dead_processing_ = true;
  status_ = DatanodeReportTypeProto::DEAD;
  for (auto it = storages_.begin(); it != storages_.end();) {
    auto storage = it->second;
    if (storage->failed()) {
      ++it;
      continue;
    }
    storage->ExtractBlocks(blocks);
    it = storages_.erase(it);
  }
  return true;
}

bool DatanodeInfo::GetBlocksWithLocations(
    const std::function<bool(
        uint64_t,
        const std::string&,
        const std::string&,
        const StorageTypeProto&)>& consume_fn) {
  std::unordered_set<std::string> used_storages;
  while (true) {
    if (status_ == DatanodeReportTypeProto::DEAD) {
      return false;
    }
    std::vector<uint64_t> block_ids;
    std::string dn_uuid;
    std::string storage_uuid;
    StorageTypeProto storage_type_proto;

    {
      std::unique_lock<ReadWriteLock> lock(rwlock_);
      dn_uuid = uuid_;
      for (auto& storage : storages_) {
        if (used_storages.find(storage.first) == used_storages.end()) {
          storage.second->GetBlocks(&block_ids);
          used_storages.insert(storage.first);
          if (!block_ids.empty()) {
            storage_uuid = storage.second->uuid();
            storage_type_proto =
                StoragePolicy::ToProto(storage.second->storage_type());
            break;
          }
        }
      }
    }
    if (block_ids.empty()) {
      return true;
    }
    for (auto block_id : block_ids) {
      if (!consume_fn(block_id, dn_uuid, storage_uuid, storage_type_proto)) {
        return true;
      }
    }
  }
  return true;
}

void DatanodeInfo::AddBlocks(const DatanodeStorageProto& storage_proto,
                             const std::vector<uint64_t>& blocks) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  auto storage = GetStorageInternal(storage_proto.storageuuid());
  if (!storage) {
    storage = AddStorageInternal(storage_proto);
  }
  storage->AddBlocks(blocks);
}

uint32_t DatanodeInfo::NumBlocks() {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  uint32_t total = 0;
  for (const auto &storage : storages_) {
    total += storage.second->NumBlocks();
  }
  return total;
}

void DatanodeInfo::RemoveBlocks(const DatanodeStorageProto& storage_proto,
                                const std::vector<uint64_t>& to_remove) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  auto storage = GetStorageInternal(storage_proto.storageuuid());
  if (!storage) {
    storage = AddStorageInternal(storage_proto);
    LOG(WARNING) << "Remove blocks on non exist storage: "
                 << storage_proto.storageuuid()
                 << ", dn: " << current_ip_.load()->ToString();
    return;
  }
  storage->RemoveBlocks(to_remove);
}

bool DatanodeInfo::AddBlockStorages(const std::string& storage_id,
                                    uint64_t block_id) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  auto storage = GetStorageInternal(storage_id);
  if (storage == nullptr) {
    return false;
  }
  storage->AddBlock(block_id);
  return true;
}

std::unordered_set<std::string> DatanodeInfo::UpdateStorage(
    const HeartbeatRequestProto &request) {
  bool need_log = !FLAGS_run_ut;
  auto last_volume_failure_date =
      request.volumefailuresummary().lastvolumefailuredate();
  auto volume_failures = request.failedvolumes();
  std::unordered_set<std::string> failed_storages;

  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (CheckFailedStorages(last_volume_failure_date, volume_failures)) {
    LOG_IF(INFO, need_log) << "[DN" << internal_id_ << " " << socket_address()
                           << "]"
                           << " Volume failed date "
                           << last_volume_failure_date_ << " to "
                           << last_volume_failure_date
                           << " Number of failed storage " << volume_failures_
                           << " to " << volume_failures;
    for (const auto& storage : storages_) {
      failed_storages.insert(storage.first);
    }
  }
  last_volume_failure_date_ = last_volume_failure_date;
  volume_failures_ = volume_failures;
  storage_types_ = 0;
  for (const auto &report : request.reports()) {
    auto storage = GetStorageInternal(report.storage().storageuuid());
    if (!storage) {
      storage = AddStorageInternal(report);
    }
    storage->Update(report);
    failed_storages.erase(report.storage().storageuuid());
    storage_types_ |= static_cast<uint8_t>(storage->storage_type());
  }
  stat_.Clear();
  for (const auto &storage : storages_) {
    stat_.Add(storage.second->stat());
  }
  stat_.xceiver_count = request.xceivercount();
  stat_.temp_based_remaining = request.tempbasedremaining();
  return failed_storages;
}

std::unordered_set<std::string> DatanodeInfo::UpdateBlockReportStatus(
    const std::string& storage_uuid,
    const BlockReportContextProto& context,
    bool is_last) {
  auto storage = GetStorage(storage_uuid);
  storage->UpdateReportId(context.id());
  std::unordered_set<std::string> failed_storages;
  if (is_last) {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    if (context.id() != report_status_.first) {
      report_status_.first = context.id();
      report_status_.second.reset();
    }
    report_status_.second.set(context.currpc());
    if (report_status_.second.count() >= context.totalrpcs() - 1) {
      for (const auto& pair : storages_) {
        auto& storage_uuid_now = pair.first;
        auto& storage_now = pair.second;
        if (storage_now->report_id() != context.id()) {
          LOG(WARNING) << "Zombie storage S" << storage_uuid_now << " on DN "
                       << current_ip_.load()->ToString()
                       << " current report id " << context.id()
                       << " storage report id " << storage_now->report_id();
          failed_storages.insert(storage_uuid_now);
        }
      }
    }
  }
  return failed_storages;
}

std::vector<uint64_t> DatanodeInfo::MarkStorageAsFailed(
    const std::string& storage_id) {
  auto storage = GetStorage(storage_id);
  std::vector<uint64_t> blocks;
  if (!storage) {
    return blocks;
  }

  std::unique_lock<ReadWriteLock> lock(rwlock_);
  storage->ExtractBlocks(&blocks);
  storage->SetFailed();

  storage_types_ = 0;
  for (const auto& it : storages_) {
    auto s = it.second;
    if (s->state() == cloudfs::DatanodeStorageProto_StorageState_NORMAL) {
      storage_types_ |= static_cast<uint8_t>(s->storage_type());
    }
  }

  stat_.Subtract(storage->stat());
  return blocks;
}

bool DatanodeInfo::CheckLoad(uint32_t /*blocksize*/) {
  // TODO(liyuan) implementation
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  return true;
}

void DatanodeInfo::MarkStaleAfterFailover() {
  heartbeated_since_failover_ = false;
  content_stale_ = true;
}

bool DatanodeInfo::IsFunctional() {
  bool ret =
      admin_state_ == DatanodeInfoProto_AdminState_NORMAL ||
      admin_state_ == DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS;
  VLOG_IF(8, !ret) << "Non-functional dn accessed " << internal_id_;
  return ret;
}

bool DatanodeInfo::IsDying() {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  return ((std::chrono::system_clock::now() - last_heartbeat_) >=
          std::chrono::milliseconds(GetDyingInterval()));
}

bool DatanodeInfo::IsStale() {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  return force_stale_ || force_stale2_ ||
         ((std::chrono::system_clock::now() - last_heartbeat_) >=
          std::chrono::milliseconds(GetStaleInterval()));
}

bool DatanodeInfo::IsStale2() {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  return force_stale2_;
}

bool DatanodeInfo::IsInactive() {
  if (FLAGS_avoid_stale_datanode_for_read) {
    return IsStale();
  }
  return false;
}

bool DatanodeInfo::IsAlive() {
  return IsFunctional() && status_ != DatanodeReportTypeProto::DEAD;
}

bool DatanodeInfo::IsCapableWrite() {
  return admin_state_ == DatanodeInfoProto_AdminState_NORMAL;
}

bool DatanodeInfo::IsWriteable() {
  return writeable_;
}

void DatanodeInfo::CheckWriteable() {
  CheckWriteableStorageCnt();
  if (!IsAlive()) {
    writeable_ = false;
    return;
  }
  if (FLAGS_avoid_stale_datanode_for_write && IsStale()) {
    writeable_ = false;
    return;
  }
  if (IsDecommissionInProgress()) {
    writeable_ = false;
    return;
  }
  if (!CheckLoad(FLAGS_dfs_block_size)) {
    writeable_ = false;
    return;
  }

  writeable_ = true;
}

uint32_t DatanodeInfo::GetWriteableStorageCnt() {
  return writeable_storage_cnt_.load();
}

void DatanodeInfo::CheckWriteableStorageCnt() {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  uint32_t cnt = 0;
  for (auto& pair : storages_) {
    auto& storage = pair.second;
    if (!storage->failed()) {
      cnt++;
    }
  }
  writeable_storage_cnt_.store(cnt);
  VLOG(10) << "dn=" << current_ip_.load()->ToString()
           << " storages.size()=" << storages_.size()
           << " writeable_storage_cnt_=" << writeable_storage_cnt_;
}

void DatanodeInfo::SetWriteableStorageCnt4Test(uint32_t cnt) {
  writeable_storage_cnt_ = cnt;
  LOG(INFO) << "SetWriteableStorageCnt4Test writeable_=" << writeable_
            << " writeable_storage_cnt_" << writeable_storage_cnt_
            << " IsAlive=" << IsAlive() << " IsStale=" << IsStale();
}

uint32_t DatanodeInfo::GetWriteableWeight() {
  if (FLAGS_dn_writable_use_total_capacity_factor &&
      FLAGS_dn_writable_unit_capacity_bytes_unit > 0) {
    uint64_t capacity = stat().capacity.load();
    if (capacity < FLAGS_dn_writable_unit_capacity_bytes_unit) {
      return 1;
    } else {
      return capacity / FLAGS_dn_writable_unit_capacity_bytes_unit;
    }
  } else {
    auto cnt = GetWriteableStorageCnt();
    return cnt ? cnt : 1;
  }
}

uint32_t DatanodeInfo::GetWriteableWeightByTmpRemain() {
  if (FLAGS_dn_writable_unit_capacity_bytes_unit > 0) {
    uint64_t remaining = stat().temp_based_remaining.load();
    if (remaining < FLAGS_dn_writable_unit_capacity_bytes_unit) {
      return 1;
    } else {
      return remaining / FLAGS_dn_writable_unit_capacity_bytes_unit;
    }
  } else {
    return 1;
  }
}

std::shared_ptr<StorageInfo> DatanodeInfo::GetStorage(const std::string& id) {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  return GetStorageInternal(id);
}

std::shared_ptr<StorageInfo> DatanodeInfo::GetStorageInternal(
    const std::string& id) {
  auto it = storages_.find(id);
  if (it != storages_.end()) {
    return it->second;
  }
  return nullptr;
}

std::shared_ptr<StorageInfo> DatanodeInfo::AddStorageInternal(
    const StorageReportProto& report) {
  auto storage = std::make_shared<StorageInfo>(report);
  if (report.storage().state() ==
      cloudfs::DatanodeStorageProto_StorageState_NORMAL) {
    storage_types_ |= static_cast<uint8_t>(storage->storage_type());
  }
  storages_.insert(std::make_pair(report.storage().storageuuid(), storage));
  // AddLoadInternal(report);
  return storage;
}

std::shared_ptr<StorageInfo> DatanodeInfo::AddStorageInternal(
    const DatanodeStorageProto& report) {
  auto storage = std::make_shared<StorageInfo>(report);
  if (report.state() == cloudfs::DatanodeStorageProto_StorageState_NORMAL) {
    storage_types_ |= static_cast<uint8_t>(report.storagetype());
  }
  storages_.insert(std::make_pair(report.storageuuid(), storage));
  return storage;
}

int32_t DatanodeInfo::GetDyingInterval() {
  return FLAGS_datanode_dying_interval_ms;
}

int32_t DatanodeInfo::GetStaleInterval() {
  return FLAGS_datanode_stale_interval_ms;

#if 0
  CHECK(FLAGS_datanode_stale_interval_ms > 0)
  << "datanode_stale_interval_ms " << FLAGS_datanode_stale_interval_ms
  << " should be positive";

  // TODO(liyuan) stale interval should not be smaller than
  // 3 times of heartbeat interval

  if (FLAGS_datanode_stale_interval_ms
      >= FLAGS_datanode_keep_alive_timeout_sec * 1000) {
    LOG(WARNING) << "The given interval for marking stale datanode = "
                 << ", which is larger than heartbeat expire interval "
                 << FLAGS_datanode_keep_alive_timeout_sec * 1000;
  }
  return FLAGS_datanode_stale_interval_ms;
#endif
}

bool DatanodeInfo::CheckFailedStorages(int64_t last_volume_failure_date,
                                       int32_t volume_failures) {
  if (last_volume_failure_date_ > 0 && last_volume_failure_date > 0) {
    return last_volume_failure_date > last_volume_failure_date_;
  }
  return volume_failures > volume_failures_ ||
      !heartbeated_since_registration_;
}

void DatanodeInfo::BlockIndexStats(std::deque<size_t>& d) {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  for (auto& item : storages_) {
    size_t v[3];
    item.second->BlockIndexStats(v);
    d.emplace_back(internal_id_);
    d.emplace_back(current_ip_.load()->ToIPv4ID());
    d.emplace_back(v[0]);
    d.emplace_back(v[1]);
    d.emplace_back(v[2]);
  }
}

void DatanodeInfo::BlockIndexDetail(std::deque<NameBlockIndex*>& d) {
  std::shared_lock<ReadWriteLock> lock(rwlock_);
  for (auto& item : storages_) {
    auto nbi = new NameBlockIndex();
    nbi->name = item.first;
    item.second->GetBlocks(nbi->blocks);
    d.emplace_back(nbi);
  }
}

void DatanodeInfo::PushInvalidateBlock(
    std::unordered_set<Block, BlockHash>& blks) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  invalidate_blks_.insert(blks.begin(), blks.end());
}

uint32_t DatanodeInfo::PopInvalidateBlock(
    cloudfs::datanode::BlockCommandProto* cmd) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  uint32_t num = 0;
  for (auto blk_it = invalidate_blks_.begin();
      blk_it != invalidate_blks_.end();) {
    auto blk = cmd->add_blocks();
    blk->set_blockid(blk_it->id);
    blk->set_numbytes(blk_it->num_bytes);
    blk->set_genstamp(blk_it->gs);
    blk_it = invalidate_blks_.erase(blk_it);
    if (++num >= FLAGS_blockmap_invalidate_limit_per_dn) {
      break;
    }
  }
  return num;
}

std::vector<Block> DatanodeInfo::PeekInvalidateBlock(size_t offset,
                                                     size_t limit) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (offset >= invalidate_blks_.size()) {
    return {};
  }

  std::vector<Block> result;
  result.reserve(limit);
  int idx = 0;
  for (auto it = invalidate_blks_.begin(); it != invalidate_blks_.end(); ++it) {
    if (idx >= offset) {
      result.emplace_back(*it);
    }

    if (result.size() >= limit) {
      break;
    }

    idx++;
  }
  return result;
}

void DatanodeInfo::ClearInvalidateBlock() {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  invalidate_blks_.clear();
}

void DatanodeInfo::PushTruncatableBlock(
    std::unordered_set<Block, BlockHash>& blks) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);

  for (const auto b : blks) {
    if (truncatable_blks_.size() >= FLAGS_blockmap_truncatable_limit_per_dn) {
      return;
    }

    VLOG(12) << "PushTruncatableBlock"
             << " id=" << id() << " ip=" << ip().ToString()
             << " blk=" << b.ToString();
    truncatable_blks_.insert(b);
  }
}

uint32_t DatanodeInfo::PopTruncatableBlock(
    cloudfs::datanode::BlockCommandProto* cmd) {
  decltype(truncatable_blks_) v;
  {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    v.swap(truncatable_blks_);
  }

  for (auto b : v) {
    auto blk = cmd->add_blocks();
    blk->set_blockid(b.id);
    blk->set_numbytes(b.num_bytes);
    blk->set_genstamp(b.gs);
  }
  return v.size();
}

std::vector<Block> DatanodeInfo::PeekTruncatableBlock(size_t offset,
                                                      size_t limit) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (offset >= truncatable_blks_.size()) {
    return {};
  }

  std::vector<Block> result;
  result.reserve(limit);
  int idx = 0;
  for (const auto& blk : truncatable_blks_) {
    if (idx >= offset) {
      result.emplace_back(blk);
    }

    if (result.size() >= limit) {
      break;
    }

    idx++;
  }
  return result;
}

void DatanodeInfo::ClearTruncatableBlock() {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  truncatable_blks_.clear();
}

void DatanodeInfo::AddUploadCmds(std::vector<UploadCmd>&& cmds) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  for (auto it = cmds.begin(); it != cmds.end();) {
    upload_cmds_.emplace_back();
    upload_cmds_.back().Swap(&(*it));
    it = cmds.erase(it);
  }
}

void DatanodeInfo::GetUploadCmds(
    cloudfs::datanode::HeartbeatResponseProto* response) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  for (auto it = upload_cmds_.begin(); it != upload_cmds_.end();) {
    auto upload_cmd = response->add_cmds();
    upload_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::UploadCommand);
    upload_cmd->mutable_uploadcmd()->Swap(&(*it));
    it = upload_cmds_.erase(it);
  }
}

void DatanodeInfo::AddNotifyEvictableCmds(
    std::vector<NotifyEvictableCmd>&& cmds) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  for (auto it = cmds.begin(); it != cmds.end();) {
    notify_evictable_cmds_.emplace_back();
    notify_evictable_cmds_.back().Swap(&(*it));
    it = cmds.erase(it);
  }
}

void DatanodeInfo::GetNotifyEvictableCmds(
    cloudfs::datanode::HeartbeatResponseProto* response) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  for (auto it = notify_evictable_cmds_.begin();
       it != notify_evictable_cmds_.end();) {
    auto ne_cmd = response->add_cmds();
    ne_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::NotifyEvictableCommand);
    ne_cmd->mutable_necmd()->Swap(&(*it));
    it = notify_evictable_cmds_.erase(it);
  }
}

void DatanodeInfo::AddInvalidatePufsCmd(InvalidatePufsCmd&& cmd) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  // Too many invalidate pufs cmds will cause other threads stucked by
  // DatanodeInfo::xxx operations because rwlock_ is holding by
  // DatanodeInfo::GetInvalidatePufsCmds.
  // Detail: https://bytedance.feishu.cn/docs/doccnaz4ugl1LnA45iEhXM8Sd8C
  // So we limit the size of invalidate pufs cmd to one,
  // throw old invalidate pufs cmd away if a new one coming.
  // Notice: One cmd can contain many blocks.
  invalidate_pufs_cmd_.Swap(&cmd);
}

void DatanodeInfo::GetInvalidatePufsCmds(
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  if (invalidate_pufs_cmd_.IsInitialized() &&
      invalidate_pufs_cmd_.blocks_size() > 0) {
    auto invalidate_cmd = response->add_cmds();
    invalidate_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::InvalidatePufsCommand);
    invalidate_cmd->mutable_invalidatepufscmd()->Swap(&invalidate_pufs_cmd_);
    invalidate_pufs_cmd_.Clear();
    if (bm_metrics) {
      MFC(bm_metrics->dn_cmd_cnt_invalidate_pufs_)
          ->Inc(invalidate_cmd->invalidatepufscmd().blocks_size());
    }
  }
}

void DatanodeInfo::ResetBlockReportCount() {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  for (const auto& it : storages_) {
    auto s = it.second;
    s->ResetBlockReportCount();
  }
}

void DatanodeInfo::LockForBlockReport() {
  block_report_mut_.lock();
}

bool DatanodeInfo::TryLockForBlockReport() {
  return block_report_mut_.try_lock();
}

void DatanodeInfo::UnlockForBlockReport() {
  block_report_mut_.unlock();
}

void DatanodeInfo::IncBlockReportCount() {
  for (auto& s : storages_) {
    s.second->IncBlockReportCount();
  }
}

std::chrono::system_clock::time_point DatanodeInfo::GetLastNormalFbrTimepoint()
    const {
  return last_normal_fbr_;
}
std::chrono::system_clock::time_point DatanodeInfo::GetLastFastFbrTimepoint()
    const {
  return last_fast_fbr_;
}
std::chrono::system_clock::time_point DatanodeInfo::
    ResetLastNormalFbrTimepoint() {
  return last_normal_fbr_ = std::chrono::system_clock::time_point::min();
}
std::chrono::system_clock::time_point DatanodeInfo::
    ResetLastFastFbrTimepoint() {
  return last_fast_fbr_ = std::chrono::system_clock::time_point::min();
}
std::chrono::system_clock::time_point DatanodeInfo::
    UpdateLastNormalFbrTimepoint() {
  auto now = std::chrono::system_clock::now();
  return last_normal_fbr_ = now;
}
std::chrono::system_clock::time_point DatanodeInfo::
    UpdateLastFastFbrTimepoint() {
  auto now = std::chrono::system_clock::now();
  return last_fast_fbr_ = now;
}

std::shared_ptr<StorageInfo> DatanodeInfo::GetOrCreateStorage(
    const DatanodeStorageProto& storage_proto) {
  std::shared_ptr<StorageInfo> storage = nullptr;
  {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    storage = GetStorageInternal(storage_proto.storageuuid());
  }
  if (storage == nullptr) {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    storage = GetStorageInternal(storage_proto.storageuuid());
    if (storage == nullptr) {
      storage = AddStorageInternal(storage_proto);
    }
  }
  return storage;
}

const std::string& DatanodeInfo::dc_name() const {
  return GetGlobalDataCenterTable().Name(this->dc_);
}

void DatanodeInfo::RefreshLocation() {
  if (FLAGS_enable_location_tag) {
    // pass
    return;
  }

  auto loc = ResolveNetworkLocation(ip());

  // update dc
  auto new_dc = GetGlobalDataCenterTable().ID(loc.dc);
  if (dc_ != new_dc) {
    LOG(INFO) << "DN id=" << id() << " ip=" << ip().ToString()
              << " change DC, old=" << GetGlobalDataCenterTable().Name(dc_)
              << " new=" << GetGlobalDataCenterTable().Name(new_dc);
    dc_ = new_dc;
  }
}

std::string DatanodeInfo::GetLocationString() {
  if (FLAGS_enable_location_tag) {
    return NetworkLocation::ToString(location_tag());
  } else {
    return "/" + dc_name() + "/" + rack();
  }
}

std::string DatanodeInfo::GetFullLocationString() {
  if (FLAGS_enable_location_tag) {
    return NetworkLocation::ToString(location_tag()) + "/" + ip().ToString();
  } else {
    return NetworkLocation::ToString(dc_name(), rack(), ip().ToString());
  }
}

bool DatanodeInfo::HasRdmaTag(const std::string& rdma_tag) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);

  return HasRdmaTagInternal(rdma_tag);
}

bool DatanodeInfo::HasRdmaTagInternal(const std::string& rdma_tag) {
  for (const auto& tag : current_address_.load()->optiprdmatags()) {
    if (tag == rdma_tag) {
      return true;
    }
  }
  return false;
}

bool DatanodeInfo::IsLocalAz(const cloudfs::LocationTag& client_location_tag) {
  // use location tag
  if (this->location_tag().has_az() && !this->location_tag().az().empty() &&
      client_location_tag.has_az() && !client_location_tag.az().empty()) {
    return client_location_tag.az() == this->location_tag().az();
  }

  // default true
  return true;
}

std::tuple<bool, bool, std::string> DatanodeInfo::IsInSameSwitch(
    const cloudfs::LocationTag& client_location_tag,
    const std::string& client_rdma_tag) {
  // rdma tag > location tag
  // use rdma tag
  {
    std::unique_lock<ReadWriteLock> lock(rwlock_);

    VLOG(15) << "IsInSameSwitch "
             << " DatanodeIDProto="
             << current_address_.load()->ShortDebugString()
             << " dn_location_tag=" << location_tag().ShortDebugString()
             << " client_location_tag="
             << client_location_tag.ShortDebugString()
             << " client_rdma_tag=" << client_rdma_tag;

    if (!client_rdma_tag.empty() &&
        current_address_.load()->optiprdmatags().size() > 0) {
      bool has_same_tag = HasRdmaTagInternal(client_rdma_tag);
      if (has_same_tag) {
        return std::tuple<bool, bool, std::string>{
            true, has_same_tag, client_rdma_tag};
      } else {
        auto tag = current_address_.load()->optiprdmatags().Get(0);
        return std::tuple<bool, bool, std::string>{true, has_same_tag, tag};
      }
    }
  }

  // use location tag
  if (this->location_tag().has_switch_() &&
      !this->location_tag().switch_().empty() &&
      client_location_tag.has_switch_() &&
      !client_location_tag.switch_().empty()) {
    return std::tuple<bool, bool, std::string>{
        true,
        this->location_tag().switch_() == client_location_tag.switch_(),
        this->location_tag().switch_()};
  }

  return std::tuple<bool, bool, std::string>{false, false, ""};
}

void DatanodeInfo::SetLocation4Test(const std::string& dc,
                                    const std::string& rack) {
  dc_ = GetGlobalDataCenterTable().ID(dc);
  current_rack_.store(&*racks_.insert(rack).first);
}

void DatanodeInfo::DumpDatanodeStorageReportProto(
    cloudfs::DatanodeStorageReportProto* storage_report) {
  CHECK_NOTNULL(storage_report);

  std::shared_lock<ReadWriteLock> lock(rwlock_);
  DumpDatanodeInfoProto(storage_report->mutable_datanodeinfo());
  for (auto& storage : storages_) {
    storage.second->GetStorageReportProto(storage_report->add_storagereports());
  }
}

void DatanodeInfo::DumpDatanodeInfoProto(
    cloudfs::DatanodeInfoProto* dn_info) {
  auto dn = this;
  CHECK_NOTNULL(dn_info);

  auto now = std::chrono::system_clock::now();
  dn_info->mutable_id()->CopyFrom(dn->address());
  const auto& stat = dn->stat();
  dn_info->set_capacity(stat.capacity);
  dn_info->set_dfsused(stat.dfs_used);
  dn_info->set_nondfsused(stat.non_dfs_used);
  dn_info->set_remaining(stat.remaining);
  dn_info->set_tempbasedremaining(stat.temp_based_remaining);
  dn_info->set_blockpoolused(stat.blockpool_used);
  dn_info->set_lastupdate(std::chrono::duration_cast<std::chrono::seconds>(
                              now - dn->last_heartbeat())
                              .count());
  if (dn->IsDecommissionInProgress()) {
    dn_info->set_adminstate(
        cloudfs::DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS);
  }
  dn_info->set_location(dn->GetLocationString());
}

cnetpp::base::Object DatanodeInfo::ToJson(bool show_storage) {
  auto dn = this;
  cnetpp::base::Object detail;

  cloudfs::DatanodeStorageReportProto storage_report_info;
  DumpDatanodeStorageReportProto(&storage_report_info);

  detail["id"] = dn->id();
  detail["is_registered"] = dn->IsRegistered();
  detail["alive"] = dn->IsAlive();
  detail["stale"] = dn->IsStale();
  detail["stale2"] = dn->IsStale2();
  detail["dying"] = dn->IsDying();
  bool decommission = dn->IsDecommissionInProgress();
  detail["decommission"] = decommission;
  if (decommission) {
    detail["replicated_blocks"] = static_cast<int>(dn->replicated_blocks());
  }
  detail["content_stale"] = dn->content_stale();
  detail["num_blocks"] = dn->NumBlocks();
  detail["nodezone_id"] = dn->nodezone_id();
  detail["writeable"] = dn->IsWriteable();
  detail["writeable_storage"] = dn->GetWriteableStorageCnt();
  detail["writeable_weight"] = dn->GetWriteableWeight();
  detail["ip_address"] = dn->ip().ToString();
  detail["socket_address"] = dn->socket_address();
  detail["storage_type"] = dn->storage_type();
  detail["report"] = cnetpp::base::Value(
      PBConverter::ToJson(storage_report_info.datanodeinfo()));
  detail["storage"] = cnetpp::base::Array();
  if (show_storage) {
    cnetpp::base::Array storages;
    for (const auto& storage : storage_report_info.storagereports()) {
      auto obj = PBConverter::ToJson(storage);
      obj["type"] =
          cloudfs::StorageTypeProto_Name(storage.storage().storagetype());
      storages.Append(cnetpp::base::Value(obj));
    }
    detail["storage"] = storages;
  }
  detail["version"] = dn->version();
  detail["admin_state"] = static_cast<int>(dn->admin_state());

  auto now = std::chrono::system_clock::now();
  detail["during_normal_fbr"] = TimeUtil::ToSeconds(last_normal_fbr_, now);
  detail["during_fast_fbr"] = TimeUtil::ToSeconds(last_fast_fbr_, now);
  return detail;
}

void DatanodeInfo::ToStorePB(DatanodeInfoEntryPB* pb) {
  CHECK(pb);

  pb->set_internal_id(internal_id_);

  pb->set_uuid(uuid_);
  pb->mutable_address()->CopyFrom(address());
  pb->set_ip_address(ip().ToString());

  pb->set_nodezone_id(nodezone_id());
  {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    for (auto& storage : storages_) {
      auto add_storage = pb->add_storages();
      add_storage->set_storageuuid(storage.first);
      add_storage->set_storagetype(
          StoragePolicy::ToProto(storage.second->storage_type()));
      add_storage->set_state(storage.second->state());
    }
  }

  // ignore the stale caused by time.
  pb->set_is_stale(force_stale_);

  pb->set_version(version_);

  pb->set_admin_state(admin_state_);
}

std::string DatanodeInfo::ToShortString() const {
  std::ostringstream oss;
  oss << "[DatanodeInfo](";
  oss << "dn_id=" << id();
  oss << ", uuid=" << uuid();
  oss << ", host=" << hostname();
  oss << ")";
  return oss.str();
}

void DatanodeInfo::set_version(ProductVersion version) {
  version_ = version;
}

ProductVersion DatanodeInfo::version() {
  return version_;
}

}  // namespace dancenn
