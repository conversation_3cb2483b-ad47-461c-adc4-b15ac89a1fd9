// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef DATANODE_MANAGER_DATA_CENTERS_H_
#define DATANODE_MANAGER_DATA_CENTERS_H_

#include <string>
#include <map>
#include <unordered_set>
#include <vector>
#include <shared_mutex>

#include "base/read_write_lock.h"

namespace dancenn {

struct DataCenter {
  std::string name;
  int id;

  DataCenter(const std::string& dcname, int dcid) : name(dcname), id(dcid) {}

  int compare(const DataCenter& that) const {
    if (this->id == that.id) {
      return this->name.compare(that.name);
    } else {
      return this->id - that.id;
    }
  }
};

inline bool operator==(const DataCenter& a, const DataCenter& b) {
  return a.compare(b) == 0;
}

inline bool operator!=(const DataCenter& a, const DataCenter& b) {
  return a.compare(b) != 0;
}

inline bool operator>(const DataCenter& a, const DataCenter& b) {
  return a.compare(b) > 0;
}

inline bool operator<(const DataCenter& a, const DataCenter& b) {
  return a.compare(b) < 0;
}

inline bool operator>=(const DataCenter& a, const DataCenter& b) {
  return !(a < b);
}

inline bool operator<=(const DataCenter& a, const DataCenter& b) {
  return !(a > b);
}

extern const DataCenter kUnknownDataCenter;
extern const DataCenter kDefaultDataCenter;

class DataCenters {
 public:
  DataCenters();
  ~DataCenters() = default;

  size_t size() const {
    return dc_by_id_.size();
  }

  std::unordered_set<std::string> GetAllDCName() const {
    std::unordered_set<std::string> ret;
    for (auto itr : dc_by_name_) {
      ret.insert(itr.first);
    }
    return ret;
  }

  const DataCenter& GetDataCenterById(int id) const;

  const DataCenter& GetDataCenterByName(const std::string& name) const;

  bool IsDataCenterValidForCentralizePolicy(const std::string& dc);
  bool IsDataCenterValidForDistributePolicy(const std::string& dc);

 private:
  std::map<std::string, DataCenter> dc_by_name_;
  std::vector<DataCenter> dc_by_id_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_DATA_CENTERS_H_

