// Copyright 2017 Liyuan Lei <<EMAIL>>

#ifndef DATANODE_MANAGER_DATANODE_INFO_H_
#define DATANODE_MANAGER_DATANODE_INFO_H_

#include <ClientNamenodeProtocol.pb.h>
#include <DatanodeProtocol.pb.h>
#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/ip_address.h>
#include <datanode_info.pb.h>
#include <hdfs.pb.h>

#include <atomic>
#include <bitset>
#include <chrono>
#include <deque>
#include <functional>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/read_write_lock.h"
#include "block_manager/block.h"
#include "block_manager/block_pufs_info.h"
#include "block_manager/datanode_command.h"
#include "datanode_manager/storage_info.h"

using cloudfs::DatanodeIDProto;
using cloudfs::DatanodeInfoProto_AdminState;
using cloudfs::DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS;
using cloudfs::DatanodeInfoProto_AdminState_DECOMMISSIONED;
using cloudfs::DatanodeInfoProto_AdminState_NORMAL;
using cloudfs::DatanodeReportTypeProto;
using cloudfs::LocationTag;
using cloudfs::StorageReportProto;
using cloudfs::datanode::BlockReportContextProto;
using cloudfs::datanode::HeartbeatRequestProto;
using cloudfs::datanode::StorageBlockReportProto;
using cloudfs::datanode::BlockReportContextProto;

namespace dancenn {
namespace datanode_info {

struct IPAddressCompare {
  bool operator()(const cnetpp::base::IPAddress& lhs,
                  const cnetpp::base::IPAddress& rhs) const {
    return lhs.ToString() < rhs.ToString();
  }
};

struct DatanodeIDProtoCompare {
  bool operator()(const DatanodeIDProto& lhs, const DatanodeIDProto& rhs) {
    // CHECK(lhs.IsInitialized());
    // CHECK(rhs.IsInitialized());
    return lhs.SerializeAsString() < rhs.SerializeAsString();
  }
};

}  // namespace datanode_info

class BlockManagerMetrics;
class StorageInfo;
typedef ::google::protobuf::RepeatedPtrField<StorageReportProto>
    RepeatedStorageReport;

using DatanodeID = uint32_t;
const DatanodeID kInvalidDatanodeID = UINT32_MAX;

class NameBlockIndex {
 public:
  NameBlockIndex() {
  }
  ~NameBlockIndex() {
  }
  void ToStream(std::ostringstream& os) const;

 public:
  std::string name;
  std::vector<uint64_t> blocks[3];
};

class DatanodeInfo {
 public:
  DatanodeInfo(uint32_t internal_id, const DatanodeIDProto& addr,
               const cnetpp::base::IPAddress& ip);
  ~DatanodeInfo() = default;

  const DatanodeIDProto& address() const;
  const std::string& hostname() const;
  const cnetpp::base::IPAddress& ip() const;
  const std::string& socket_address() const;
  void set_address(DatanodeIDProto address, const cnetpp::base::IPAddress& ip);
  const std::string& physical_machine() const;
  void set_physical_machine(const std::string& machine);
  const std::string& rack() const;
  const std::string& nodezone_id() const;
  void set_nodezone_id(const std::string& nodezone_id);
  LocationTag location_tag();
  void set_location_tag(const LocationTag& location_tag);

  DatanodeID id() const { return internal_id_; }
  std::string uuid() const {
    return uuid_;
  }
  const size_t dc() const { return dc_; }
  const std::string& dc_name() const;

  bool IsRegistered() { return is_registered_; }
  void SetRegistered() { is_registered_ = true; }

  void set_status(DatanodeReportTypeProto s) { status_ = s; }
  DatanodeReportTypeProto status() const { return status_; }
  void set_admin_state(DatanodeInfoProto_AdminState s) { admin_state_ = s; }
  DatanodeInfoProto_AdminState admin_state() const { return admin_state_; };

  void SetDecommissioning();
  void SetNonDecommissioning();
  bool IsDecommissionInProgress() const;
  bool SetDecommissionOnScheduleOn() {
    bool expected = false;
    return decommission_on_schedule_.compare_exchange_strong(expected, true);
  }
  bool SetDecommissionOnScheduleOff() {
    bool expected = true;
    return decommission_on_schedule_.compare_exchange_strong(expected, false);
  }
  void SetDecommissioned();
  bool IsDecommissioned() const ;

  StorageStat& stat();
  uint8_t storage_type() { return storage_types_; }
  void add_storage_type(uint8_t storage_type) {
    storage_types_ |= storage_type;
  }
  bool heartbeated_since_failover() { return heartbeated_since_failover_; }
  void unset_content_stale() { content_stale_ = false; }
  bool content_stale() { return content_stale_; }
  size_t replicated_blocks() { return replicated_blocks_; }
  void set_replicated_blocks(size_t replicated_blocks) {
    replicated_blocks_ = replicated_blocks;
  }

  void set_force_stale(bool force_stale) { force_stale_ = force_stale; }
  bool get_force_stale() { return force_stale_; }
  void set_force_stale2(bool force_stale2) { force_stale2_ = force_stale2; }
  bool get_force_stale2() { return force_stale2_; }

  void set_dead_processing(bool dead_processing) {
    dead_processing_ = dead_processing;
  }
  bool dead_processing() const {
    return dead_processing_;
  }

  const std::chrono::time_point<std::chrono::system_clock>& last_heartbeat()
      const {  // NOLINT(whitespace/line_length)
    return last_heartbeat_;
  }

  bool need_key_update() const { return need_key_update_; }
  void set_need_key_update(bool need_key_update) {
    need_key_update_ = need_key_update;
  }

  bool CheckAndUpdateHeartbeat();
  void UpdateHeartbeat4Test(
      const std::chrono::system_clock::time_point last_heartbeat);
  void UpdateHeartbeat(bool heartbeated_since_registration);

  bool GetBlocks(std::vector<uint64_t>* blocks, size_t max_size = INT_MAX);
  bool MarkDead(std::vector<uint64_t>* blocks,
                std::chrono::milliseconds keep_alive);
  bool GetBlocksWithLocations(
      const std::function<bool(
          uint64_t,
          const std::string&,
          const std::string&,
          const StorageTypeProto&)>& consume_fn);
  void GetBlocksGroupByStorage(
      std::unordered_map<std::string, std::vector<BlockID>>* blks);

  void AddBlocks(const DatanodeStorageProto& storage_proto,
                 const std::vector<uint64_t>& blocks);

  uint32_t NumBlocks();

  void RemoveBlocks(const DatanodeStorageProto& storage_proto,
                    const std::vector<uint64_t>& to_remove);
  bool AddBlockStorages(const std::string& storage_id, uint64_t block_id);
  std::unordered_set<std::string> UpdateStorage(
      const HeartbeatRequestProto &request);
  std::unordered_set<std::string> UpdateBlockReportStatus(
      const std::string& storage_uuid,
      const BlockReportContextProto& context,
      bool is_last);
  std::vector<uint64_t> MarkStorageAsFailed(const std::string& storage_id);

  bool CheckLoad(uint32_t blocksize);

  void MarkStaleAfterFailover();
  bool IsFunctional();
  bool IsStale();
  bool IsStale2();
  bool IsDying();
  bool IsInactive();
  bool IsAlive();
  bool IsCapableWrite();
  bool IsWriteable();
  void CheckWriteable();
  uint32_t GetWriteableStorageCnt();
  void CheckWriteableStorageCnt();
  uint32_t GetWriteableWeight();
  uint32_t GetWriteableWeightByTmpRemain();
  void SetWriteableStorageCnt4Test(uint32_t cnt);

  void BlockIndexStats(std::deque<size_t>& d);
  void BlockIndexDetail(std::deque<NameBlockIndex*>& d);

  void PushInvalidateBlock(std::unordered_set<Block, BlockHash>& blks);
  uint32_t PopInvalidateBlock(cloudfs::datanode::BlockCommandProto* cmd);
  std::vector<Block> PeekInvalidateBlock(size_t offset, size_t limit);
  void ClearInvalidateBlock();

  void PushTruncatableBlock(std::unordered_set<Block, BlockHash>& blks);
  uint32_t PopTruncatableBlock(cloudfs::datanode::BlockCommandProto* cmd);
  std::vector<Block> PeekTruncatableBlock(size_t offset, size_t limit);
  void ClearTruncatableBlock();

  // Deprecated Start
  void AddUploadCmds(std::vector<UploadCmd>&& cmds);
  void GetUploadCmds(cloudfs::datanode::HeartbeatResponseProto* response);
  void AddNotifyEvictableCmds(std::vector<NotifyEvictableCmd>&& cmds);
  void GetNotifyEvictableCmds(
      cloudfs::datanode::HeartbeatResponseProto* response);
  void AddInvalidatePufsCmd(InvalidatePufsCmd&& cmd);
  void GetInvalidatePufsCmds(
      cloudfs::datanode::HeartbeatResponseProto* response,
      BlockManagerMetrics* bm_metrics = nullptr);
  // Deprecated End

  std::shared_ptr<StorageInfo> GetOrCreateStorage(
      const DatanodeStorageProto& storage_proto);

  // TODO(zhuangsiyu): deprecated in 4.6.2
  void ResetBlockReportCount();

  void RefreshLocation();
  std::string GetLocationString();
  std::string GetFullLocationString();
  bool HasRdmaTag(const std::string& rdma_tag);
  bool HasRdmaTagInternal(const std::string& rdma_tag);

  bool IsLocalAz(const cloudfs::LocationTag& client_location_tag);
  // <applicable, is_same, dn_switch>
  std::tuple<bool, bool, std::string> IsInSameSwitch(
      const cloudfs::LocationTag& client_location_tag,
      const std::string& client_rdma_tag);

  void SetLocation4Test(const std::string& dc, const std::string& rack);

  void DumpDatanodeStorageReportProto(
      cloudfs::DatanodeStorageReportProto* storage_report);
  void DumpDatanodeInfoProto(cloudfs::DatanodeInfoProto* dn_info);
  cnetpp::base::Object ToJson(bool show_storage = false);
  std::string ToShortString() const;

  void LockForBlockReport();
  bool TryLockForBlockReport();
  void UnlockForBlockReport();
  void StartFBR() {
    is_doing_fbr_ = true;
  }
  void FinishFBR() {
    is_doing_fbr_ = false;
  }
  bool IsDoingFBR() const {
    return is_doing_fbr_;
  }
  void IncBlockReportCount();
  void ToStorePB(DatanodeInfoEntryPB* pb);

  std::chrono::system_clock::time_point GetLastNormalFbrTimepoint() const;
  std::chrono::system_clock::time_point GetLastFastFbrTimepoint() const;
  std::chrono::system_clock::time_point ResetLastNormalFbrTimepoint();
  std::chrono::system_clock::time_point ResetLastFastFbrTimepoint();
  std::chrono::system_clock::time_point UpdateLastNormalFbrTimepoint();
  std::chrono::system_clock::time_point UpdateLastFastFbrTimepoint();

  void set_version(ProductVersion version);
  ProductVersion version();

 private:
  std::shared_ptr<StorageInfo> GetStorage(const std::string& id);
  std::shared_ptr<StorageInfo> GetStorageInternal(const std::string& id);
  std::shared_ptr<StorageInfo> AddStorageInternal(
      const StorageReportProto& report);
  std::shared_ptr<StorageInfo> AddStorageInternal(
      const DatanodeStorageProto& reports);

  int32_t GetDyingInterval();
  int32_t GetStaleInterval();
  bool CheckFailedStorages(int64_t last_volume_failure_date,
                           int32_t volume_failures);
 private:
  ReadWriteLock rwlock_;

  // In veCompass(K8S), address/ip of datanode can change.
  //
  // We used rwlock_ to protect the following vars before,
  // but taking lock and copying DatanodeIDProto/IPAddress are too expensive.
  // DatanodeIDProto DatanodeInfo::address() const {
  //   std::shared_lock<ReadWriteLock> lock(rwlock_);
  //   return address_;
  // }
  // The above code was executed many time in ChooseTarget4New,
  // cause billion-small-files namespace to slow down.
  //
  // So we use lists to stores them now.
  // NOTICE: No iterators of set are invalidated after calling insert.
  std::atomic<const DatanodeIDProto*> current_address_;
  std::set<DatanodeIDProto, datanode_info::DatanodeIDProtoCompare> addresses_;
  std::atomic<const cnetpp::base::IPAddress*> current_ip_;
  std::set<cnetpp::base::IPAddress, datanode_info::IPAddressCompare> ips_;
  std::atomic<const std::string*> current_socket_address_;
  std::set<std::string> socket_addresses_;
  std::atomic<const std::string*> current_physical_machine_;
  std::set<std::string> physical_machines_;
  // dninfo has const ip, so rack_ never change
  std::atomic<const std::string*> current_rack_;
  std::set<std::string> racks_;
  std::atomic<const std::string*> current_nodezone_id_;
  std::set<std::string> nodezone_ids_;
  LocationTag location_tag_;

  std::string uuid_;
  // dc_ may be accessed by multiple threads, but only need eventual consistency
  size_t dc_;

  const DatanodeID internal_id_;
  std::unordered_map<std::string, std::shared_ptr<StorageInfo>> storages_;
  uint8_t storage_types_;
  std::pair<uint64_t, std::bitset<INT16_MAX>> report_status_;
  StorageStat stat_;
  std::atomic<DatanodeReportTypeProto> status_; // LIVE or DEAD
  std::atomic<DatanodeInfoProto_AdminState> admin_state_; // NORMAL / DECOMMISSIONING
  // for datanode check
  std::atomic<bool> decommission_on_schedule_{false};
  std::chrono::time_point<std::chrono::system_clock> last_heartbeat_;

  std::atomic<bool> need_key_update_{true};

  std::atomic<bool> force_stale_ { false };
  std::atomic<bool> force_stale2_{ false };
  // NOTICE: Storages are marked as content stale after NameNode
  // restart/failover before first block report is received.
  // DatanodeInfo::UpdateBlockReportStatus will set content_stale_ to false if
  // name node receives HeartbeatRequestProto and BlockReportRequestProto msgs.
  // content_stale_:
  //   1. true (constructor)
  //   2. false (receive msgs in standby mode)
  //   3. true (DatanodeManager::MarkAllStaleAfterFailover)
  //   4. false (receive msgs in active mode)
  // In rare cases DN FBR might lost and it will take up to 5 mins to trigger
  // FBR again. To accelerator this process, run the following cmd:
  // curl 'localhost:5070/admin?cmd=fbr&dn=<dn_id>'
  std::atomic<bool> content_stale_ { true };
  std::atomic<bool> heartbeated_since_failover_ { false };

  std::atomic<bool> dead_processing_  { false };

  int64_t last_volume_failure_date_ { -1 };
  int32_t volume_failures_ { 0 };
  bool heartbeated_since_registration_ { false };

  // Deprecated Start
  std::unordered_set<Block, BlockHash> invalidate_blks_;
  std::unordered_set<Block, BlockHash> truncatable_blks_;
  std::vector<UploadCmd> upload_cmds_;
  std::vector<NotifyEvictableCmd> notify_evictable_cmds_;
  InvalidatePufsCmd invalidate_pufs_cmd_;
  // Deprecated End

  // for ChooseTarget
  std::atomic<bool> writeable_{true};
  std::atomic<uint32_t> writeable_storage_cnt_{0};

  // for Decommission
  std::atomic<size_t> replicated_blocks_{0};

  std::mutex block_report_mut_;
  bool is_doing_fbr_{false};
  std::chrono::system_clock::time_point last_fast_fbr_;
  std::chrono::system_clock::time_point last_normal_fbr_;
  std::atomic<bool> is_registered_{false};

  ProductVersion version_{0};
};

typedef DatanodeInfo* DatanodeInfoPtr;
using DnSet = std::unordered_set<DatanodeInfoPtr>;
using DnVec = std::vector<DatanodeInfoPtr>;

}  // namespace dancenn

#endif  // DATANODE_MANAGER_DATANODE_INFO_H_
