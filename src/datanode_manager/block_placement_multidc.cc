// Copyright 2017 Liyuan Lei <<EMAIL>>

#include <cnetpp/base/string_utils.h>

#include <algorithm>
#include <cassert>
#include <cmath>
#include <random>
#include <shared_mutex>
#include <vector>

#include "base/data_center_table.h"
#include "base/defer.h"
#include "base/rack_aware.h"
#include "base/stop_watch.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_manager_metrics.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_tolerate_interval_misses_sec);
DECLARE_string(blacklist_dc);
DECLARE_string(majority_dc);
DECLARE_string(dc_topology_file);
DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);
DECLARE_string(default_distributed_dc);

namespace dancenn {

BlockPlacementMultiDC::BlockPlacementMultiDC(DatanodeManagerMetrics* metrics,
                                             bool with_refresher)
    : BlockPlacement(with_refresher),
      metrics_(metrics) {
  rwlock_global_conf_ = std::make_shared<dancenn::VRWLock>(
      FLAGS_client_slow_rpc_handler_count
      + FLAGS_client_normal_rpc_handler_count + 1);
}

BlockPlacementMultiDC::~BlockPlacementMultiDC() {
}

void BlockPlacementMultiDC::PrepareDC(const std::string& dc) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  auto dc_it = datacenters_.find(dc);
  if (dc_it == datacenters_.end()) {
    BlockPlacementDefaultPtr bp_default
      = std::make_shared<BlockPlacementDefault>(metrics_);
    datacenters_.insert(std::make_pair(dc, bp_default));
  }
}

void BlockPlacementMultiDC::AddDatanode(DatanodeInfoPtr dn) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  if (datanodes_.count(dn)) {
    return;
  }

  AddDatanodeWithoutLock(dn);
  bool need_log = !FLAGS_run_ut;
  LOG_IF(INFO, need_log) << "add datanode " << dn->hostname() << ":"
                         << dn->ip().ToString() << " to topology "
                         << dn->GetLocationString();
}

void BlockPlacementMultiDC::AddDatanodeWithoutLock(DatanodeInfoPtr dn) {
  const auto& dc = dn->dc_name();

  if (datacenters_.find(dc) == datacenters_.end()) {
    BlockPlacementDefaultPtr bp_default =
        std::make_shared<BlockPlacementDefault>(metrics_);
    datacenters_.insert(std::make_pair(dc, bp_default));
  }
  datacenters_[dc]->AddDatanode(dn);
  datanodes_.insert(dn);

  bool need_log = !FLAGS_run_ut;
  LOG_IF(INFO, need_log) << "add to dns_, dn: " << dn->ip().ToString()
                         << ", dc: " << dc;
}

void BlockPlacementMultiDC::RemoveDatanode(DatanodeInfoPtr dn) {
  if (dn == nullptr) {
    return;
  }

  std::unique_lock<PlacementRWLock> lock(rwlock_);
  RemoveDatanodeWithoutLock(dn);
}

void BlockPlacementMultiDC::RemoveDatanodeWithoutLock(DatanodeInfoPtr dn) {
  if (dn == nullptr) {
    return;
  }

  const auto& dc = dn->dc_name();

  datanodes_.erase(dn);
  if (datacenters_.count(dc)) {
    datacenters_[dc]->RemoveDatanode(dn);
  } else {
    LOG(INFO) << "datacenters_ dc not found dn " << dn->id();
  }
}

std::string BlockPlacementMultiDC::GetExpectedDC(const DatanodeInfoPtr dn,
                                                 const std::string& from) {
  if (dn != nullptr) {
    return GetGlobalDataCenterTable().Name(dn->dc());
  }

  auto dc = dc_info_.GetDataCenterByName(from);
  return dc != kUnknownDataCenter ? from : "";
}

std::shared_ptr<BlockPlacementDefault> BlockPlacementMultiDC::GetDC(
    const std::string& dc_name) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);

  auto it = datacenters_.find(dc_name);
  if (it != datacenters_.end()) {
    return it->second;
  }
  return nullptr;
}

void BlockPlacementMultiDC::ComputeDCList(const std::string &writer_dc,
                                          std::vector<std::string>* result,
                                          const PlacementAdvice& advice,
                                          bool ignore_blacklist) {
  DEFER([&] {
    // Global blacklist switch
    if (!ignore_blacklist) {
      std::vector<std::string> black_dc_list;
      {
        vshared_lock guard(rwlock_global_conf_->lock());
        black_dc_list = black_dc_list_;
      }
      for (auto& d : black_dc_list) {
        auto it = std::find(result->begin(), result->end(), d);
        if (it != result->end()) {
          VLOG(8) << "Hit blacklist, dc: " << d
                  << ", writer_dc: " << writer_dc;
          result->erase(it);
        }
      }
    }
  });

  std::vector<std::string> dcs;
  // Modify enforce_dc from "" to "HL,LF",
  // so that we need not to change policy when update namenode to triple dc version.
  if (advice.geograph == kDistributePolicy && advice.enforce_dc.empty()) {
    dcs = cnetpp::base::StringUtils::SplitByChars(FLAGS_default_distributed_dc, ",");
  } else {
    dcs = cnetpp::base::StringUtils::SplitByChars(advice.enforce_dc, ",");
  }

  if (dcs.empty() || (dcs.size() == 1 && dcs[0].empty())) {
    dcs.clear();
  }

  if (advice.geograph == kDistributePolicy) {
    // Distribute
    if (dcs.empty()) {
      std::shared_lock<PlacementRWLock> lock(rwlock_);
      for (const auto& pair : datacenters_) {
        dcs.emplace_back(pair.first);
      }
    }
    std::vector<std::string> majority_dc_list;
    {
      vshared_lock guard(rwlock_global_conf_->lock());
      majority_dc_list = majority_dc_list_;
    }
    std::vector<std::string> ranked_dc;
    for (auto& d : majority_dc_list) {
      auto it = std::find(dcs.begin(), dcs.end(), d);
      if (it != dcs.end()) {
        ranked_dc.emplace_back(d);
      }
    }
    for (auto& d : dcs) {
      auto it = std::find(ranked_dc.begin(), ranked_dc.end(), d);
      if (it == ranked_dc.end()) {
        ranked_dc.emplace_back(d);
      }
    }
    result->swap(ranked_dc);
  } else {
    // Centralize
    if (dcs.empty()) {
      if (!writer_dc.empty()) {
        result->emplace_back(writer_dc);
      } else {
        result->emplace_back(dc_info_.GetDataCenterById(0).name);
      }
    } else {
      std::shared_lock<PlacementRWLock> lock(rwlock_);
      for (auto& d : dcs) {
        if (datacenters_.count(d) == 0) {
          continue;
        }
        result->emplace_back(d);
      }
    }
    return;
  }
}

bool BlockPlacementMultiDC::ChooseTarget4New(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* results) {
  auto writer_dn = client_location.dn;
  auto writer_from = client_location.location.dc;

  MFH(metrics_->block_placement_replication_distribution_)->Update(rep_num);
  StopWatch sw(metrics_->block_placement_choose_target_compute_dc_list_time_);
  sw.Start();
  std::vector<std::string> dc_list;
  auto writer_dc = GetExpectedDC(writer_dn, writer_from);
  ComputeDCList(writer_dc, &dc_list, advice, false);

  if (dc_list.empty()) {
    LOG(WARNING) << "No dc to write, rep_num: " << rep_num
                 << ", writer_dc: " << writer_dc << ", advice: " << advice.ToString();
    return false;
  }

  double remain_rep_num = rep_num;
  auto satisfying = true;
  auto dc_size = dc_list.size();

  std::unordered_map<std::string, std::vector<DatanodeInfoPtr>> dc_favored;
  for (auto dn : favored_nodes) {
    auto dc_name = GetExpectedDC(dn, "");
    if (!dc_name.empty()) {
      dc_favored[dc_name].push_back(dn);
    }
  }

  sw.NextStep();

  for (auto i = 0; i < dc_size; ++i) {
    const auto dc_name = dc_list[i];
    auto current_dc = GetDC(dc_name);
    if (!current_dc) {
      LOG(ERROR) << "Policy for datacenter not found: " << dc_name;
      continue;
    }
    auto rep = static_cast<int32_t>(std::ceil(remain_rep_num / (dc_size - i)));
    std::vector<DatanodeInfoPtr> nodes;
    auto s = current_dc->ChooseTarget4New(src_path,
                                          rep,
                                          blocksize,
                                          advice,
                                          client_location,
                                          dc_favored[dc_name],
                                          excluded,
                                          &nodes);
    VLOG(8) << "ChooseTarget4New, rep_num: " << rep_num << ", " << i << " "
            << dc_name << " " << rep << " advice: " << advice.ToString() << " "
            << ", writer_dc: " << writer_dc << ", "
            << dc_favored[dc_name].size()
            << ", excluded:" << (excluded != nullptr ? excluded->size() : 0)
            << ", nodes: " << nodes.size() << ", status: " << s;

    satisfying = satisfying && s;
    if (dc_list[i] == writer_dc) {
      // Write to local dc first
      results->insert(results->begin(), nodes.begin(), nodes.end());
    } else {
      results->insert(results->end(), nodes.begin(), nodes.end());
    }
    remain_rep_num -= nodes.size();
  }
  return satisfying;
}

bool BlockPlacementMultiDC::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr> *excluded,
    std::vector<DatanodeInfoPtr> *results) {

  std::unordered_map<std::string, int32_t> replica_dist;
  for (auto dn : included) {
    CHECK_NOTNULL(dn);
    const auto& dc_name = GetGlobalDataCenterTable().Name(dn->dc());
    replica_dist[dc_name]++;
  }

  std::vector<std::string> dc_list;
  ComputeDCList("", &dc_list, advice, true);

  std::unordered_map<std::string,std::vector<DatanodeInfoPtr>> dc_favored;
  for (auto dn : favored_nodes) {
    auto dc_name = GetExpectedDC(dn, "");
    if (!dc_name.empty()) {
      dc_favored[dc_name].push_back(dn);
    }
  }

  double full_rep = included.size() + rep_num;
  double target_rep_num = rep_num;
  size_t dc_size = dc_list.size();
  for (size_t i = 0; i < dc_list.size(); ++i) {
    auto dc_name = dc_list[i];
    auto current_dc = GetDC(dc_name);
    if (!current_dc) {
      LOG(ERROR) << "Policy for datacenter not found: " << dc_name;
      continue;
    }
    auto dc_rep = static_cast<int32_t>(std::ceil(full_rep / (dc_size - i)));
    full_rep -= dc_rep;
    auto delta = dc_rep - replica_dist[dc_list[i]];
    if (delta > static_cast<int32_t>(target_rep_num)) {
      delta = static_cast<int32_t>(target_rep_num);
    }
    if (delta <= 0) {
      // skip the dc who has the majority replica
      continue;
    }
    std::vector<DatanodeInfoPtr> nodes;
    current_dc->ChooseTarget4Recover(src_path,
                                     delta,
                                     blocksize,
                                     advice,
                                     client_location,
                                     dc_favored[dc_name],
                                     included,
                                     excluded,
                                     &nodes);
    results->insert(results->end(), nodes.begin(), nodes.end());
    target_rep_num -= nodes.size();

    VLOG(8) << "ChooseTarget4Recover, rep_num: " << rep_num << ", " << i << " "
            << dc_name << ", " << delta << " advice: " << advice.ToString()
            << " " << ", writer_dn: "
            << (client_location.dn != nullptr
                    ? client_location.dn->ip().ToString()
                    : "")
            << ", " << dc_favored[dc_name].size()
            << ", excluded:" << (excluded != nullptr ? excluded->size() : 0)
            << ", nodes: " << nodes.size();
  }

  if (target_rep_num > 0) {
    excluded->insert(included.begin(), included.end());
    std::vector<DatanodeInfoPtr> nodes;
    ChooseTarget4New(src_path,
                     static_cast<int32_t>(target_rep_num),
                     blocksize,
                     advice,
                     client_location,
                     favored_nodes,
                     excluded,
                     &nodes);

    results->insert(results->end(), nodes.begin(), nodes.end());
  }

  // The bottom line is find *rep_num* additional nodes to serve the replicas,
  // no matter if they are distributed among multiple data centers as expected.
  return results->size() == rep_num;
}

DatanodeInfoPtr BlockPlacementMultiDC::ChooseReplicaToDelete(
    const Block& blk, size_t replication,
    const std::unordered_set<DatanodeInfoPtr>& more_than_one,
    const std::unordered_set<DatanodeInfoPtr>& exactly_one,
    const PlacementAdvice &advice) {
  std::unordered_map<std::string, size_t> included_dcs;

  for (auto dn : more_than_one) {
    included_dcs[dn->dc_name()]++;
  }
  for (auto dn : exactly_one) {
    included_dcs[dn->dc_name()]++;
  }

  std::vector<std::string> all_dcs;
  ComputeDCList("", &all_dcs, advice, true);
  size_t dc_size = all_dcs.size();

  double remain_rep_num = replication;
  std::unordered_set<std::string> excluded_dcs;
  for (size_t i = 0; i < dc_size; ++i) {
    auto& dc_name = all_dcs[i];
    int32_t rep = std::ceil(remain_rep_num / (dc_size - i));
    auto iter = included_dcs.find(all_dcs[i]);
    if (iter != included_dcs.end() && iter->second <= rep) {
      // exclude the dc who has the minority replica
      excluded_dcs.insert(all_dcs[i]);
    }
    remain_rep_num -= rep;
  }
  DatanodeInfoPtr replica = nullptr;
  replica = ChooseReplicaToDelete(more_than_one, excluded_dcs);
  if (replica == nullptr) {
    replica = ChooseReplicaToDelete(exactly_one, excluded_dcs);
  }
  if (replica == nullptr) {
    excluded_dcs.clear();
    replica = ChooseReplicaToDelete(more_than_one, excluded_dcs);
    if (replica == nullptr) {
      replica = ChooseReplicaToDelete(exactly_one, excluded_dcs);
    }
  }
  VLOG(8) << "Choosing " << replica->ip().ToString()
          << " to delete for blk: " << blk.ToString();

  return replica;
}

DatanodeInfoPtr BlockPlacementMultiDC::ChooseReplicaToDelete(
    const std::unordered_set<DatanodeInfoPtr>& replicas,
    const std::unordered_set<std::string>& excluded_dcs) {
  std::chrono::time_point<std::chrono::system_clock> oldest_heartbeat
      = std::chrono::system_clock::now()
          - std::chrono::milliseconds(
          FLAGS_datanode_tolerate_interval_misses_sec * 1000);
  DatanodeInfoPtr oldest_dn = nullptr;
  DatanodeInfoPtr min_space_dn = nullptr;
  // Pick the node with the oldest heartbeat or with the least free space
  // if all heartbeats are within the tolerable heartbeat interval and
  // prefer to choose redundant dc replicas in dc
  for (auto& replica_dn : replicas) {
    auto dc = GetGlobalDataCenterTable().Name(replica_dn->dc());
    if (excluded_dcs.count(dc)) {
      continue;
    }
    if (replica_dn->last_heartbeat() < oldest_heartbeat) {
      oldest_dn = replica_dn;
      oldest_heartbeat = replica_dn->last_heartbeat();
    }
    if (min_space_dn == nullptr ||
        replica_dn->stat().remaining < min_space_dn->stat().remaining) {
      min_space_dn = replica_dn;
    }
  }
  return oldest_dn != nullptr ? oldest_dn : min_space_dn;
}

std::unordered_map<std::string, int>
BlockPlacementMultiDC::GetExpectedPlacement(const PlacementAdvice &advice,
                                            int32_t rep_num) {
  std::unordered_map<std::string, int> result;

  std::vector<std::string> dc_list;
  ComputeDCList("", &dc_list, advice, true);

  double full_rep = rep_num;
  size_t dc_size = dc_list.size();
  for (size_t i = 0; i < dc_list.size(); ++i) {
    auto dc_name = dc_list[i];
    auto current_dc = GetDC(dc_name);
    if (!current_dc) {
      LOG(ERROR) << "Policy for datacenter not found: " << dc_name;
      continue;
    }
    auto dc_rep = static_cast<int32_t>(std::ceil(full_rep / (dc_size - i)));
    full_rep -= dc_rep;
    result[dc_name] = dc_rep;

    VLOG(8) << "GetExpectedPlacement, rep_num: " << rep_num << ", " << i << " "
          << dc_name << ", " << dc_rep << " advice: " << advice.ToString();
  }

  return result;
}

bool BlockPlacementMultiDC::HasBeenMultiRack() {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  if (datacenters_.empty()) {
    return false;
  }
  if (datacenters_.size() > 1) {
    return true;
  }
  return datacenters_.begin()->second->HasBeenMultiRack();
}

size_t BlockPlacementMultiDC::NumRacks() {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  size_t num_racks = 0;
  for (auto it : datacenters_) {
    num_racks += it.second->NumRacks();
  }
  return num_racks;
}

bool BlockPlacementMultiDC::IsLocal(DatanodeInfoPtr dn,
                                    const NetworkLocation& loc,
                                    bool* same_rack,
                                    bool* same_dc,
                                    uint32_t* dc_distance) {
  const auto& dc_id = dn->dc();
  std::string rack = dn->rack();

  *same_rack = (dc_id == loc.dc_id && loc.rack == rack);
  *same_dc = (dc_id == loc.dc_id);
  *dc_distance = dc_topology_.GetDistance(dc_id, loc.dc_id);

  return true;
}

bool BlockPlacementMultiDC::DatanodeExist(DatanodeInfoPtr dn) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);

  return datanodes_.find(dn) != datanodes_.end();
}

void BlockPlacementMultiDC::GetAllDatanodesInRack(
    const std::string& dc,
    const std::string& rack,
    std::unordered_set<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  auto dc_it = datacenters_.find(dc);
  if (dc_it == datacenters_.end()) {
    LOG(ERROR) << "Cannot find dc info for " << dc << ":" << rack;
    return;
  }
  dc_it->second->GetAllDatanodesInRack(dc, rack, dns);
}

void BlockPlacementMultiDC::GetAllDatanodeInfo(
    std::vector<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  GetAllDatanodeInfoUnsafe(dns);
}

void BlockPlacementMultiDC::GetAllDatanodeInfoUnsafe(
    std::vector<DatanodeInfoPtr>* dns) {
  std::for_each(
      datacenters_.begin(), datacenters_.end(),
      [=](const auto& pair) { pair.second->GetAllDatanodeInfo(dns); });
}

void BlockPlacementMultiDC::ListAll(std::string* output) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  for (auto & datacenter : datacenters_) {
    output->append(datacenter.first + ": ");
    datacenter.second->ListAll(output);
  }
}

bool BlockPlacementMultiDC::RefreshConfig() {
  auto result = BlockPlacement::RefreshConfig();

  std::string blacklist_dc;
  if (gflags::GetCommandLineOption("blacklist_dc", &blacklist_dc) &&
      blacklist_dc != last_blacklist_dc_) {
    if (!this->SetDCList("blacklist", blacklist_dc)) {
      LOG(WARNING) << "Refresh blacklist dc failed, str: " << blacklist_dc;
    } else {
      last_blacklist_dc_ = blacklist_dc;
    }
  }
  std::string majority_dc;
  if (gflags::GetCommandLineOption("majority_dc", &majority_dc) &&
      majority_dc != last_majority_dc_) {
    if (!this->SetDCList("majority", majority_dc)) {
      LOG(WARNING) << "Refresh majority dc failed, str: " << majority_dc;
    } else {
      last_majority_dc_ = majority_dc;
    }
  }

  if (!dc_topology_.ReloadFromFile(FLAGS_dc_topology_file)) {
    LOG(WARNING) << "Failed to reload datacenters topology.";
    result = false;
  }

  return result;
}

void BlockPlacementMultiDC::RecalcAllDatanode() {
  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    auto duration_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
            .count();

    LOG(INFO) << "RecalcAllDatanode cost: " << duration_ms << "ms";
  });

  std::unique_lock<PlacementRWLock> lock(rwlock_);
  LOG(INFO) << "RecalcAllDatanode Start. this: " << (uint64_t)this;

  std::vector<DatanodeInfoPtr> dns;
  this->GetAllDatanodeInfoUnsafe(&dns);
  datacenters_.clear();
  datanodes_.clear();

  LOG(INFO) << "RecalcAllDatanode dn.size(): " << dns.size();

  for (const auto& dn : dns) {
    dn->RefreshLocation();

    this->AddDatanodeWithoutLock(dn);
    LOG(INFO) << "Recalc datanode " << dn->hostname() << ":"
              << dn->ip().ToString() << " to topology "
              << dn->GetLocationString();
  }
}

bool BlockPlacementMultiDC::SetDCList(const std::string& type,
                                      const std::string& dc_str) {
  bool res = false;
  std::vector<std::string> dc_list;
  std::vector<std::string> dcs =
    cnetpp::base::StringUtils::SplitByChars(dc_str, ",");
  LOG(WARNING) << "SetDCList type=" << type <<" dc_str=" << dc_str;
  if (dcs.empty() || (dcs.size() == 1 && dcs[0].empty())) {
    res = true;
  } else {
    for (auto& d : dcs) {
      auto dc = dc_info_.GetDataCenterByName(d);
      if (dc == kUnknownDataCenter) {
        LOG(WARNING) << "Unknown datacenter, d=" << d;
        res = false;
        break;
      } else {
        auto it = std::find(dc_list.begin(), dc_list.end(), dc.name);
        if (it == dc_list.end()) {
          dc_list.emplace_back(dc.name);
        }
        res = true;
      }
    }
  }

  if (res) {
    std::stringstream ss;
    for (auto& d : dc_list) {
      ss << d << " ";
    }
    LOG(WARNING) << "new dc_list=" << ss.str();

    dancenn::vunique_lock vlock(rwlock_global_conf_->lock());
    if (type == "blacklist") {
      black_dc_list_.swap(dc_list);
    } else if (type == "majority") {
      majority_dc_list_.swap(dc_list);
    }
  }
  return res;
}

std::string BlockPlacementMultiDC::GetBlacklistDc() const {
  std::vector<std::string> black_dc_list;
  {
    dancenn::vshared_lock _(rwlock_global_conf_->lock());
    black_dc_list = black_dc_list_;
  }
  std::string dcs;
  for (size_t i = 0; i < black_dc_list.size(); i++) {
    dcs.append(black_dc_list[i]);
    if (i < black_dc_list.size() - 1) {
      dcs.append(",");
    }
  }
  return dcs;
}

std::string BlockPlacementMultiDC::GetMajorityDc() const {
  std::vector<std::string> majority_dc_list;
  {
    dancenn::vshared_lock _(rwlock_global_conf_->lock());
    majority_dc_list = majority_dc_list_;
  }
  std::string dcs;
  for (size_t i = 0; i < majority_dc_list.size(); i++) {
    dcs.append(majority_dc_list[i]);
    if (i < majority_dc_list.size() - 1) {
      dcs.append(",");
    }
  }
  return dcs;
}

}  // namespace dancenn
