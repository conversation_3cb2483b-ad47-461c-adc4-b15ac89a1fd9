// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#include "datanode_manager/block_index.h"

#include <algorithm>

#include <gflags/gflags.h>

DECLARE_int32(datanode_ordered_block_index_compact_threshold);

namespace dancenn {

OrderedBlockIndexImpl::OrderedBlockIndexImpl() {
  base_block_ids_.reset(new std::vector<uint64_t>());
}

OrderedBlockIndexImpl::~OrderedBlockIndexImpl() {
}

void OrderedBlockIndexImpl::Compact() {
  auto new_base = std::make_unique<std::vector<uint64_t>>();
  if (base_block_ids_->size() + delta_added_block_ids_.size()
      > delta_removed_block_ids_.size()) {
    new_base->reserve(base_block_ids_->size() + delta_added_block_ids_.size()
                          - delta_removed_block_ids_.size());
  }

  ForEach([&] (uint64_t val) {
    new_base->push_back(val);
  });
  base_block_ids_.reset(new_base.release());
  delta_added_block_ids_.clear();
  delta_removed_block_ids_.clear();
}

// caller must have acquired unique_lock
void OrderedBlockIndexImpl::CompactIfNecessary() {
  if (delta_added_block_ids_.size() + delta_removed_block_ids_.size()
      >= FLAGS_datanode_ordered_block_index_compact_threshold) {
    Compact();
  }
}

bool OrderedBlockIndexImpl::Contains(uint64_t block_id) {
  if (delta_added_block_ids_.find(block_id) != delta_added_block_ids_.end()) {
    return true;
  }
  if (delta_removed_block_ids_.find(block_id)
      != delta_removed_block_ids_.end()) {
    return false;
  }
  return std::binary_search(base_block_ids_->begin(), base_block_ids_->end(),
      block_id);
}

void OrderedBlockIndexImpl::AddBlock(uint64_t block_id) {
  delta_added_block_ids_.insert(block_id);
  delta_removed_block_ids_.erase(block_id);
  CompactIfNecessary();
}

void OrderedBlockIndexImpl::AddBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    delta_added_block_ids_.insert(b);
    delta_removed_block_ids_.erase(b);
  }
  CompactIfNecessary();
}

void OrderedBlockIndexImpl::RemoveBlock(uint64_t block_id) {
  delta_removed_block_ids_.insert(block_id);
  delta_added_block_ids_.erase(block_id);
  CompactIfNecessary();
}

void OrderedBlockIndexImpl::RemoveBlocks(const std::vector<uint64_t>& blocks) {
  for (const auto& b : blocks) {
    delta_removed_block_ids_.insert(b);
    delta_added_block_ids_.erase(b);
  }
  CompactIfNecessary();
}

void OrderedBlockIndexImpl::Reset(const std::vector<uint64_t> &blocks) {
  base_block_ids_.reset(new std::vector<uint64_t>(blocks));
  delta_removed_block_ids_.clear();
  delta_added_block_ids_.clear();
}

void OrderedBlockIndexImpl::GetBlocks(std::vector<uint64_t>* blocks) {
  Compact();
  blocks->insert(blocks->end(), base_block_ids_->begin(),
      base_block_ids_->end());
}

void OrderedBlockIndexImpl::ExtractBlocks(std::vector<uint64_t>* blocks) {
  std::unique_ptr<std::vector<uint64_t>> tmp(new std::vector<uint64_t>());
  Compact();
  base_block_ids_.swap(tmp);
  blocks->insert(blocks->end(), tmp->begin(), tmp->end());
}

uint32_t OrderedBlockIndexImpl::size() const {
  uint32_t num = 0;
  size_t add_num = base_block_ids_->size() + delta_added_block_ids_.size();
  if (add_num > delta_removed_block_ids_.size()) {
    num = static_cast<uint32_t>(add_num - delta_removed_block_ids_.size());
  }
  return num;
}

void OrderedBlockIndexImpl::ForEach(std::function<void(uint64_t)> cb) const {
  size_t size = base_block_ids_->size();
  size_t i = 0;
  auto added_it = delta_added_block_ids_.begin();
  auto removed_it = delta_removed_block_ids_.begin();
  while (i < size) {
    bool forward = true;
    uint64_t val = (*base_block_ids_)[i];
    // extract min(base, added), unless removed
    if (added_it != delta_added_block_ids_.end()) {
      if (*added_it < val) {
        forward = false;
        val = *added_it++;
      } else if (*added_it == val) {
        added_it++; // skip same blk in added
      }
    }
    while (removed_it != delta_removed_block_ids_.end() && *removed_it < val) {
      removed_it++;
    }
    if (removed_it == delta_removed_block_ids_.end() || *removed_it != val) {
      cb(val);
    }
    if (forward) {
      i++;
    }
  }
  // extra elements in 'added'
  while (added_it != delta_added_block_ids_.end()) {
    cb(*added_it++);
  }
}

}  // namespace dancenn
