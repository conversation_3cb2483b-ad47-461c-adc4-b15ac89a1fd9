// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "datanode_manager/datanode_manager.h"

#include <algorithm>
#include <memory>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/block_event_logger.h"
#include "base/constants.h"
#include "base/stop_watch.h"
#include "base/string_utils.h"
#include "datanode_manager/advice.h"
#include "hdfs.pb.h"
#include "namespace/meta_storage.h"

// TODO(liyuan) need datanode configuration to compute heartbeat expire limit
DECLARE_bool(run_ut);
DECLARE_uint32(max_datanodes_num);
DECLARE_int32(datanode_check_interval_sec);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_manager_thread_num);
DECLARE_string(block_placement_policy);
DECLARE_bool(datanode_manager_hide_remote_datanode);
DECLARE_bool(datanode_manager_enable_replica_pipeline);
DECLARE_uint32(dfs_replication_min);
DECLARE_bool(read_policy_enable);
DECLARE_uint32(dc_max_distance);
DECLARE_bool(rack_aware);
DECLARE_uint32(datanode_max_decommission_per_round);
DECLARE_bool(random_node);
DECLARE_string(datanode_machine_file);
DECLARE_bool(has_writable_datanode);
DECLARE_bool(datanode_info_bg_dump_enable);
DECLARE_int32(datanode_info_bg_dump_batch_size);
DECLARE_bool(datanode_info_fg_dump_enable);
DECLARE_int32(datanode_info_fg_dump_size_threshold);
DECLARE_int32(datanode_info_fg_dump_time_threshold);
DECLARE_bool(datanode_manager_enable_decommission_transfer_blocks);
DECLARE_bool(enable_location_tag);
DECLARE_bool(sort_located_block_method_v2);
DECLARE_int32(sort_located_block_dns_hb_window_ms);
DECLARE_bool(sort_located_block_dns_by_random_value);
DECLARE_bool(enable_add_replica_is_cached);
DECLARE_bool(log_choose_target);

namespace dancenn {

DatanodeManager::DatanodeManager(bool with_start) : metrics_(this) {
  thread_pool_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("dnmgr", true);
  thread_pool_->set_num_threads(
      static_cast<size_t>(FLAGS_datanode_manager_thread_num));
  block_placement_ = BlockPlacementPolicyFactory::Create(
      FLAGS_block_placement_policy, with_start, &metrics_);
  block_placement_->Start();

  if (with_start) {
    Start();
  }

  datanodes_.resize(FLAGS_max_datanodes_num);
  datanodes_size_ = 0;
  start_time_ = TimeUtil::GetNowEpochMs();
}

DatanodeManager::~DatanodeManager() {
  Stop();
}

void DatanodeManager::LoadFromDB() {
  if (meta_storage_ == nullptr) {
    LOG(ERROR) << "meta_storage_ not Init, at LoadFromDB()";
    return;
  }

  LOG(INFO) << "LoadFromDB Start";
  StopWatch sw;
  sw.Start();

  // Load
  meta_storage_->ScanDatanodeInfoCf(
      [this](const DatanodeID& dn_id, const DatanodeInfoEntryPB& pb) {
        auto res = LoadDatanodeInfo(dn_id, pb);
        if (res) {
          loaded_++;
        }
        return res;
      });

  LOG(INFO) << "LoadFromDB Finish. take " << sw.NextStepTime() << " us"
            << " loaded_=" << loaded_;
}

bool DatanodeManager::LoadDatanodeInfo(const DatanodeID& dn_id,
                                       const DatanodeInfoEntryPB& pb) {
  bool need_log = !FLAGS_run_ut;
  LOG_IF(INFO, need_log) << "LoadDatanodeInfo dn_id=" << dn_id
                         << " pb=" << pb.ShortDebugString();

  if (dn_id != datanodes_size_ + 1) {
    LOG(INFO) << "Skip the DN import because there are id gaps."
              << " dn_id=" << dn_id
              << " datanodes_size_=" << datanodes_size_.load();
    // TODO(xiong): Process invalid block id in block persistence.
    return false;
  }

  DatanodeID id = internal_ids_.AssignTo(pb.uuid(), dn_id);
  CHECK(id == dn_id) << "Assign internal id failed";

  cnetpp::base::IPAddress ip;
  CHECK(cnetpp::base::IPAddress::LiteralToNumber(pb.ip_address(), &ip));

  auto info = new DatanodeInfo(dn_id, pb.address(), ip);
  block_placement_->AddDatanode(info);
  // set info
  datanodes_[dn_id] = info;
  if (dn_id > datanodes_size_) {
    datanodes_size_ = dn_id;
  }

  if (datanodes_size_ + 1 >= FLAGS_max_datanodes_num) {
    LOG(FATAL) << "LoadDatanodeInfo datanodes_size overflow"
               << " datanodes_size=" << datanodes_size_.load();
  }
  {
    // TODO(xiong): @zhuangsiyu init dn ip is not allowed before Register()
    //    std::unique_lock<DNManagerRWLock> lock(rwlock_);
    //
    //    ip_to_ids_.insert(std::make_pair(info->ip().ToString(), info->id()));
    //    auto ip_xfer = info->socket_address();
    //    socket_to_ids_.emplace(ip_xfer, id);
  }

  info->set_status(DatanodeReportTypeProto::DEAD);
  info->set_admin_state(pb.admin_state());

  switch (pb.admin_state()) {
    case DatanodeInfoProto_AdminState_NORMAL:
      break;
    case DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS:
      info->SetDecommissioning();
      break;
    case DatanodeInfoProto_AdminState_DECOMMISSIONED:
      info->SetDecommissioned();
      break;
    default:
      LOG(ERROR) << "Unexpected State="
                 << static_cast<int>(info->admin_state());
      break;
  }

  if (pb.has_is_stale() && pb.is_stale()) {
    info->set_force_stale(true);
  }

  if (pb.has_nodezone_id()) {
    info->set_nodezone_id(pb.nodezone_id());
  }

  if (pb.has_version()) {
    info->set_version(pb.version());
  }

  for (int i = 0; i < pb.storages_size(); i++) {
    info->GetOrCreateStorage(pb.storages(i));
  }

  if (need_log) {
    auto info_json = info->ToJson();
    cnetpp::base::Parser parser;
    std::string info_json_str;
    parser.Serialize(cnetpp::base::Value(std::move(info_json)), &info_json_str);

    LOG_IF(INFO, need_log) << "Datanode Loaded, uuid: " << pb.uuid()
                           << " internal_id: " << id
                           << " addr: " << info->address().ShortDebugString()
                           << " pb=" << pb.ShortDebugString()
                           << " info=" << info_json_str;
  }
  return true;
}

BriefStorageStat DatanodeManager::GetBriefStorageStat() {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);
  BriefStorageStat stat;
  for (int dn_id = datanodes_size_; dn_id > 0; dn_id--) {
    const auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);

    stat.num_total_datanode++;

    switch (dn->admin_state()) {
      case DatanodeInfoProto_AdminState_NORMAL:
        stat.num_normal_datanode++;
        stat.num_content_stale_datanode += dn->content_stale();
        stat.num_stale_datanode += dn->IsStale();
        stat.num_live_datanode += dn->IsAlive();
        stat.num_dead_datanode += !dn->IsAlive();
        break;
      case DatanodeInfoProto_AdminState_DECOMMISSION_INPROGRESS:
        stat.num_decommissioning_datanode++;
        stat.num_content_stale_datanode += dn->content_stale();
        stat.num_stale_datanode += dn->IsStale();
        stat.num_live_datanode += dn->IsAlive();
        stat.num_dead_datanode += !dn->IsAlive();
        break;
      case DatanodeInfoProto_AdminState_DECOMMISSIONED:
        stat.num_decommissioned_datanode++;
        break;
      default:
        LOG(ERROR) << "Unexpected Datanode AdminState "
                   << static_cast<int>(dn->admin_state());
        break;
    }
  }
  return stat;
}

void DatanodeManager::Start() {
  thread_pool_->Start();
  thread_pool_->AddTask([this]() -> bool {
    DatanodeCheck();
    return true;
  });
}

void DatanodeManager::Stop() {
  block_placement_->Stop();
  thread_pool_->Stop(false);
}

Status DatanodeManager::Register(
    const DatanodeIDProto& addr,
    cloudfs::datanode::DatanodeRegistrationProto* registration,
    const cnetpp::base::IPAddress& ip) {
  return Register(addr, registration, ip, 0);
}

Status DatanodeManager::Register(
    const DatanodeIDProto& addr,
    cloudfs::datanode::DatanodeRegistrationProto* registration,
    const cnetpp::base::IPAddress& ip,
    ProductVersion version) {
  bool need_log = !FLAGS_run_ut;

  const std::string& uuid = addr.datanodeuuid();
  DatanodeInfoPtr info = GetDatanodeFromUuid(uuid);
  std::unique_lock<DNManagerRWLock> lock(rwlock_);
  // TODO(liyuan) exclude is not allowed to register
  if (info == nullptr) {
    if (datanodes_size_ >= FLAGS_max_datanodes_num - 1) {
      // off-by-one warning: max(datanodes_size_) == datanodes_size_.size()-1 ==
      // FLAGS_max_datanodes_num
      return Status(JavaExceptions::Exception::kIOException,
                    "number of DataNode overflow");
    }
    DatanodeID id = internal_ids_.Assign(uuid);
    info = new DatanodeInfo(id, addr, ip);
    info->CheckAndUpdateHeartbeat();
    info->set_version(version);
    std::string vip;
    cnetpp::base::IPAddress::NumberToLiteral(ip, &vip);
    if (dn_vip_to_machine_.find(vip) != dn_vip_to_machine_.end()) {
      info->set_physical_machine(dn_vip_to_machine_[vip]);
    } else {
      info->set_physical_machine(vip);
    }
    block_placement_->AddDatanode(info);
    // set info
    datanodes_[id] = info;
    // make id visible
    DatanodeID old_id = id - 1;
    while (!datanodes_size_.compare_exchange_strong(old_id, id)) {
    }

    auto ip_xfer = info->socket_address();
    socket_to_ids_.emplace(ip_xfer, id);

    auto itr_dn_id = ip_to_ids_.find(ip.ToString());
    if (itr_dn_id != ip_to_ids_.end()) {
      auto dn_id = itr_dn_id->second;
      auto old_info = datanodes_[dn_id];
      if (old_info) {
        // In CFS, DN control its own decommission status, NN just follow it
        info->set_force_stale(old_info->get_force_stale());
        // TODO(xiongmu) how to decommission datanode?
        // to avoid internal_id invalid, which cause crash when reverse lookup
        // from block
        // internal_ids_.Unassign(internal_ids_.reverse_lookup(itr_dn_id->second));
        // datanodes_.erase(itr_dn_info);
        ip_to_ids_.erase(itr_dn_id);
      }
    }
    ip_to_ids_.insert(std::make_pair(ip.ToString(), id));
    registration->set_blockreportpreserved(true);
    block_manager_->AddBlockReportCandidate(id);
    info->SetRegistered();

    auto now = TimeUtil::GetNowEpochMs();
    auto time_gap = now - start_time_;
    if (FLAGS_datanode_info_fg_dump_enable &&
        id >= FLAGS_datanode_info_fg_dump_size_threshold &&
        time_gap >= FLAGS_datanode_info_fg_dump_time_threshold * 1000) {
      if (meta_storage_ == nullptr) {
        LOG(ERROR) << "meta_storage_ not Init, at Register()";
      } else {
        VLOG(5) << "Dump DatanodeInfo at Register()";
        DumpDatanodeInfo2DB(info);
      }
    }

    LOG_IF(INFO, need_log) << "Datanode registered, uuid: " << uuid
                           << ", internal_id: " << id
                           << ", addr: " << info->address().ShortDebugString();
  } else if (info->IsFunctional()) {
    // check update
    DatanodeIDProto real_addr;
    real_addr.CopyFrom(addr);
    real_addr.set_ipaddr(ip.ToString());
    if (real_addr.SerializeAsString() != info->address().SerializeAsString()) {
      auto msg =
          "Datanode addr is changed"
          " old_addr=" +
          info->address().ShortDebugString() +
          " new_addr=" + real_addr.ShortDebugString() +
          " old_ip=" + info->ip().ToString() + " new_ip=" + ip.ToString();
      LOG(WARNING) << msg;

      {
        ip_to_ids_.erase(info->ip().ToString());
        socket_to_ids_.erase(info->socket_address());
      }

      // IP change is allowed in CloudFS.
      info->set_address(real_addr, ip);
      // TODO(ruanjunbin): socket_to_ids_ and ip_to_ids_ should change.

      std::string vip;
      cnetpp::base::IPAddress::NumberToLiteral(ip, &vip);
      if (dn_vip_to_machine_.find(vip) != dn_vip_to_machine_.end()) {
        info->set_physical_machine(dn_vip_to_machine_[vip]);
      } else {
        info->set_physical_machine(vip);
      }
    }

    {
      ip_to_ids_.emplace(info->ip().ToString(), info->id());
      socket_to_ids_.emplace(info->socket_address(), info->id());
    }

    if (info->status() == DatanodeReportTypeProto::DEAD) {
      AddStorageStat(info);
    }
    // load from db but never do block report
    if (!info->IsRegistered()) {
      AddStorageStat(info);
      info->SetRegistered();
    }
    info->set_version(version);

    registration->set_blockreportpreserved(true);
    block_manager_->AddBlockReportCandidate(info->id());
    info->UpdateHeartbeat(false);
    info->ResetBlockReportCount();
    info->MarkStaleAfterFailover();
    LOG_IF(INFO, need_log) << "Datanode " << uuid << " reconnected";
  } else {
    return Status(JavaExceptions::kIOException,
                  Code::kBadParameter,
                  "Datanode is not functional");
  }

  registration->mutable_datanodeid()->CopyFrom(addr);
  return Status();
}

Status DatanodeManager::SetDecommissioned(DatanodeID dn_id, bool force) {
  auto dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    LOG(ERROR) << "Datanode not found " << dn_id;
    return Status(Code::kBadParameter, "Datanode not found");
  }
  std::unique_lock<DNManagerRWLock> lock(rwlock_);
  if (dn->dead_processing()) {
    LOG(ERROR) << "Datanode is still dead processing " << dn_id;
    return Status(Code::kBadParameter, "Datanode is still dead processing");
  }
  if (!force && !dn->IsDecommissionInProgress()) {
    LOG(ERROR) << "Datanode is not decommissioning " << dn_id;
    return Status(Code::kBadParameter, "Datanode is not decommissioning");
  }
  if (!force && dn->IsAlive()) {
    LOG(ERROR) << "Datanode is still alive " << dn_id;
    return Status(Code::kBadParameter, "Datanode is still alive");
  }

  dn->SetDecommissioned();
  DumpDatanodeInfo2DB(dn);
  return Status::OK();
}

void DatanodeManager::Heartbeat(const HeartbeatRequestProto& request,
                                RepeatedCmds* cmds) {
  const auto& dn = request.registration();
  // TODO(liyuan) Black/White list
  const std::string& uuid = dn.datanodeid().datanodeuuid();
  DatanodeInfoPtr info = GetDatanodeFromUuid(uuid);

  // not exist or load from db but never register
  if (info == nullptr || !info->IsFunctional() || !info->IsRegistered()) {
    auto cmd = cmds->Add();
    cmd->set_cmdtype(cloudfs::datanode::DatanodeCommandProto::RegisterCommand);
    LOG(INFO) << "Unregistered dn " << dn.datanodeid().ShortDebugString();
    MFC(block_manager_->metrics()->dn_cmd_cnt_register_)->Inc();
    return;
  }
  if (info->status() == DatanodeReportTypeProto::DEAD ||
      info->dead_processing()) {
    auto cmd = cmds->Add();
    cmd->set_cmdtype(cloudfs::datanode::DatanodeCommandProto::RegisterCommand);
    LOG(INFO) << "dn " << dn.datanodeid().ShortDebugString()
              << " is dead. Register required";
    MFC(block_manager_->metrics()->dn_cmd_cnt_register_)->Inc();
    return;
  }
  SubStorageStat(info);
  auto failed_storages = info->UpdateStorage(request);
  if (!failed_storages.empty()) {
    ProcessFailedStorages(info, failed_storages);
  }
  info->UpdateHeartbeat(true);
  if (request.has_decommission()) {
    if (request.decommission()) {
      info->SetDecommissioning();
    } else {
      info->SetNonDecommissioning();
    }
  }
  if (request.has_stale()) {
    info->set_force_stale(request.stale());
  }
  AddStorageStat(info);
  DLOG(INFO) << "Heartbeat from " << uuid << "-" << info->ip().ToString();
}

void DatanodeManager::AddBlocks(DatanodeID dn_id,
                                const DatanodeStorageProto& storage_proto,
                                const std::vector<uint64_t>& to_add) {
  DatanodeInfoPtr dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    LOG(FATAL) << "BlockReport from an unregistered datanode DN" << dn_id;
  }
  dn->AddBlocks(storage_proto, to_add);
}

std::vector<DatanodeID> DatanodeManager::AddBlockStorages(
    const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
        request,  // NOLINT(whitespace/line_length)
    uint64_t block_id) {
  std::vector<DatanodeID> dn_ids;
  for (int i = 0; i < request.newtaragets_size(); ++i) {
    auto dn = GetDatanodeFromUuid(request.newtaragets(i).datanodeuuid());
    if (dn == nullptr) {
      continue;
    }
    auto ret = dn->AddBlockStorages(request.newtargetstorages(i), block_id);
    if (ret) {
      dn_ids.emplace_back(dn->id());
    }
  }
  return dn_ids;
}

void DatanodeManager::RemoveBlocks(DatanodeID dn_id,
                                   const DatanodeStorageProto& storage_proto,
                                   const std::vector<uint64_t>& to_remove) {
  DatanodeInfoPtr dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    LOG(FATAL) << "BlockReport from an unregistered datanode DN" << dn_id;
  }
  dn->RemoveBlocks(storage_proto, to_remove);
}

void DatanodeManager::UpdateStaleNodes(
    const std::unordered_set<std::string>& all_nodes,
    std::unordered_set<DatanodeID>* stale_dns) {
  CHECK_NOTNULL(stale_dns);
  size_t old_count = stale_dns->size();
  size_t add = 0;
  size_t remove = 0;

  auto start = std::chrono::steady_clock::now();
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);

    auto ip_address = dn->ip().ToString();
    auto socket_address = dn->socket_address();

    bool to_stale = false;
    if (all_nodes.count(ip_address) || all_nodes.count(socket_address)) {
      to_stale = true;
    }

    if (to_stale) {
      if (dn->get_force_stale()) {
        continue;
      }
      add++;
      dn->set_force_stale(true);
      stale_dns->insert(dn_id);
    } else {
      if (!dn->get_force_stale()) {
        continue;
      }
      remove++;
      dn->set_force_stale(false);
      stale_dns->erase(dn_id);
    }
  }

  size_t new_count = stale_dns->size();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::steady_clock::now() - start)
                      .count();
  LOG(INFO) << "Stale nodes updated: " << old_count << " -> " << new_count
            << ", add: " << add << ", remove: " << remove
            << ", cost: " << duration << "(ms)";
}

void DatanodeManager::UpdateStale2Nodes(
    const std::unordered_set<std::string>& all_nodes,
    std::unordered_set<DatanodeID>* stale2_dns) {
  CHECK_NOTNULL(stale2_dns);
  size_t old_count = stale2_dns->size();
  size_t add = 0;
  size_t remove = 0;

  auto start = std::chrono::steady_clock::now();
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);

    auto ip_address = dn->ip().ToString();
    auto socket_address = dn->socket_address();

    bool to_stale2 = false;
    if (all_nodes.count(ip_address) || all_nodes.count(socket_address)) {
      to_stale2 = true;
    }

    if (to_stale2) {
      if (dn->get_force_stale2()) {
        continue;
      }
      add++;
      dn->set_force_stale2(true);
      stale2_dns->insert(dn_id);
    } else {
      if (!dn->get_force_stale2()) {
        continue;
      }
      remove++;
      dn->set_force_stale2(false);
      stale2_dns->erase(dn_id);
    }
  }

  size_t new_count = stale2_dns->size();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::steady_clock::now() - start)
                      .count();
  LOG(INFO) << "Stale2 nodes updated: " << old_count << " -> " << new_count
            << ", add: " << add << ", remove: " << remove
            << ", cost: " << duration << "(ms)";
}

std::vector<uint64_t> DatanodeManager::UpdateBlockReportStatus(
    const std::string& storage_uuid,
    DatanodeID dn_id,
    const BlockReportContextProto& context,
    bool is_last) {
  auto dn = GetDatanodeFromId(dn_id);
  auto failed_storages =
      dn->UpdateBlockReportStatus(storage_uuid, context, is_last);
  return ProcessFailedStorages(dn, failed_storages);
}

void DatanodeManager::GetDatanodeListForReport(
    const DatanodeReportTypeProto& type,
    const NetworkLocationInfo& client_location,
    std::vector<DatanodeInfoPtr>* datanodes) {
  bool list_live = (type == DatanodeReportTypeProto::LIVE) ||
                   (type == DatanodeReportTypeProto::ALL);
  bool list_dead = type == (DatanodeReportTypeProto::DEAD) ||
                   (type == DatanodeReportTypeProto::ALL);
  bool list_decommission = (type == DatanodeReportTypeProto::DECOMMISSIONING) ||
                           (type == DatanodeReportTypeProto::ALL);
  bool list_cur_dc_list = (type == DatanodeReportTypeProto::CUR_DC_LIVE) ||
                          (type == DatanodeReportTypeProto::ALL);

  auto now = std::chrono::system_clock::now();
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    if (!dn->IsFunctional()) {
      continue;
    }

    bool is_dead = dn->last_heartbeat() + GetDatanodeKeepAliveTimeoutMs() < now;
    bool in_decommission = dn->IsDecommissionInProgress();
    if ((list_live && !is_dead) || (list_dead && is_dead) ||
        (list_decommission && in_decommission) ||
        (list_cur_dc_list && !is_dead && !in_decommission &&
         client_location.location.dc == dn->dc_name())) {
      datanodes->push_back(dn);
    }
  }
  // TODO include / exclude datanodes
}

std::vector<DatanodeInfoPtr> DatanodeManager::GetAllDatanodeInfo() {
  std::vector<DatanodeInfoPtr> datanodes;

  auto dn_size = datanodes_size_.load();
  datanodes.reserve(dn_size);
  for (int dn_id = dn_size; dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    datanodes.push_back(dn);
  }

  return datanodes;
}

std::vector<DatanodeInfoPtr> DatanodeManager::GetDatanodeInfo(
    std::function<bool(DatanodeInfoPtr)> dn_state_filter) {
  std::vector<DatanodeInfoPtr> datanodes;

  auto dn_size = datanodes_size_.load();
  datanodes.reserve(dn_size);
  for (int dn_id = dn_size; dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    if (dn_state_filter(dn)) {
      datanodes.push_back(dn);
    }
  }
  return datanodes;
}


bool DatanodeManager::HasWriteableDatanode() {
  if (FLAGS_has_writable_datanode) {
    return true;
  }
  for (int dn_id = datanodes_size_.load(); dn_id > 0; dn_id--) {
    const auto& dn = datanodes_[dn_id];
    if (dn->IsWriteable()) {
      return true;
    }
  }
  return false;
}

bool DatanodeManager::HasEnoughWriteableDatanode(int expected_dn_num) {
  for (int dn_id = datanodes_size_.load(); dn_id > 0; dn_id--) {
    const auto& dn = datanodes_[dn_id];
    if (dn->IsWriteable()) {
      expected_dn_num--;
      if (expected_dn_num <= 0) {
        return true;
      }
    }
  }
  return false;
}

Status DatanodeManager::GetDatanodeReport(
    const DatanodeReportTypeProto& type,
    const NetworkLocationInfo& client_location,
    RepeatedDatanodeInfo* datanode_info) {
  std::vector<DatanodeInfoPtr> datanodes;
  GetDatanodeListForReport(type, client_location, &datanodes);
  for (auto dn : datanodes) {
    dn->DumpDatanodeInfoProto(datanode_info->Add());
  }
  if (datanode_info->size() == 0) {
    return Status(JavaExceptions::Exception::kIOException,
                  "Failed to get datanode report for " +
                      DatanodeReportTypeProto_Name(type) + " datanodes.");
  }
  return Status();
}

Status DatanodeManager::GetDatanodeStorageReport(
    const DatanodeReportTypeProto& type,
    const NetworkLocationInfo& client_location,
    RepeatedDatanodeStorageInfo* repeated_storage_info) {
  std::vector<DatanodeInfoPtr> datanodes;
  GetDatanodeListForReport(type, client_location, &datanodes);
  for (auto dn : datanodes) {
    dn->DumpDatanodeStorageReportProto(repeated_storage_info->Add());
  }
  if (repeated_storage_info->size() == 0) {
    return Status(JavaExceptions::Exception::kIOException,
                  "Failed to get datanode storage report for " +
                      DatanodeReportTypeProto_Name(type) + " datanodes.");
  }
  return Status();
}

size_t DatanodeManager::NumLiveDatanodes() const {
  size_t res = 0;

  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    if (dn->IsAlive()) {
      res++;
    }
  }
  return res;
}

bool DatanodeManager::IsDatanodeStale(DatanodeID dn_id) {
  auto dn = GetDatanodeFromId(dn_id);
  return dn->IsStale();
}

void DatanodeManager::SetDatanodeStale(DatanodeID dn_id, bool stale) {
  auto dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    return;
  }
  dn->set_force_stale(stale);
}

void DatanodeManager::SetDecommissioning(DatanodeID dn_id) {
  auto dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    return;
  }
  dn->SetDecommissioning();
}

bool DatanodeManager::IsDecommissionInProgress(
    const cnetpp::base::IPAddress& ip) {
  auto dn = GetDatanodeFromIp(ip);
  if (dn == nullptr) {
    return false;
  }
  return dn->IsDecommissionInProgress();
}

std::vector<DatanodeInfoPtr> DatanodeManager::GetFavored4Decommission(
    const DnSet& decommission_dns) {
  if (op_task_manager_ == nullptr) {
    return kDefaultFavored;
  }
  for (auto& dn : decommission_dns) {
    auto dn_socket = dn->socket_address();
    auto op_task = op_task_manager_->GetDnReplacementTaskByDn(dn_socket);
    if (op_task == nullptr) {
      continue;
    }
    auto res = SocketSet2DnSet(op_task->dst_dns());

    std::vector<DatanodeInfoPtr> ret;
    ret.reserve(res.size());
    for (auto& dn_info : res) {
      ret.push_back(dn_info);
    }
    return ret;
  }

  return kDefaultFavored;
}

DatanodeIDProto DatanodeManager::GetDatanodeId(DatanodeID dn_id) {
  auto dn = GetDatanodeFromId(dn_id);
  if (dn == nullptr) {
    LOG(FATAL) << "Failed to get datanode from id " << dn_id;
  }
  return dn->address();
}

void DatanodeManager::GetRecoverLocations(const std::vector<DatanodeID>& dn_ids,
                                          RepeatedDatanodeInfo* datanodes) {
  std::vector<DatanodeID> tmp;
  for (const auto& dn : dn_ids) {
    if (true /* TODO(liyuan) stale datanodes */) {
      tmp.emplace_back(dn);
    }

    const std::vector<DatanodeID>& targets = tmp.size() <= 1 ? dn_ids : tmp;
    for (const auto& dn_id : targets) {
      auto datanode = GetDatanodeFromId(dn_id);
      if (datanode == nullptr) {
        continue;
      }
      datanodes->Add()->CopyFrom(datanode->address());
    }
  }
}

bool DatanodeManager::RefreshConfig() {
  return block_placement_->RefreshConfig();
}

void DatanodeManager::RefreshDatanodeMachineInfo() {
  std::ifstream machine_info_file(FLAGS_datanode_machine_file);
  std::stringstream buffer;
  buffer << machine_info_file.rdbuf();
  cnetpp::base::Value dn_machine_info_json;
  dn_vip_to_machine_.clear();
  if (cnetpp::base::Parser::Deserialize(buffer.str(), &dn_machine_info_json)) {
    auto dn_machine_info = dn_machine_info_json.AsObject();
    for (auto itr = dn_machine_info.Begin(); itr != dn_machine_info.End();
         ++itr) {
      dn_vip_to_machine_.insert({itr->first, itr->second.AsString()});
    }
  } else {
    LOG(WARNING) << "Failed to deserialize dn machine info. Clear current dn "
                    "machine info.";
  }

  for (DatanodeInfoPtr dn : datanodes_) {
    if (dn == nullptr) {
      continue;
    }
    std::string dn_vip;
    cnetpp::base::IPAddress::NumberToLiteral(dn->ip(), &dn_vip);
    if (dn_vip_to_machine_.find(dn_vip) != dn_vip_to_machine_.end()) {
      dn->set_physical_machine(dn_vip_to_machine_[dn_vip]);
    } else {
      dn->set_physical_machine(dn_vip);
    }
  }
  LOG(INFO) << "Refreshed DN physical machine info.";
}

bool DatanodeManager::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeID>* targets) {
  std::vector<DatanodeInfoPtr> result;
  bool ret = block_placement_->ChooseTarget4Recover(src_path,
                                                    rep_num,
                                                    blocksize,
                                                    advice,
                                                    client_location,
                                                    favored,
                                                    included,
                                                    excluded,
                                                    &result);
  if (ret) {
    for (const auto& dn : result) {
      targets->emplace_back(dn->id());
    }
    MFC(metrics_.block_placement_choose_target_succeed_cnt_)->Inc();
  } else {
    LOG(ERROR) << "ChooseTarget4Recover failed for " << src_path;
    MFC(metrics_.block_placement_choose_target_failed_cnt_)->Inc();
  }
  BlockEventLogger::ChooseTarget4Recover(src_path, *targets);
  return ret;
}

bool DatanodeManager::ChooseTarget4New(
    const std::string& src_path,
    int32_t exp_rep_num,
    int32_t min_rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<std::string>& favorednodes,
    const RepeatedDatanodeInfo& excludes,
    cloudfs::LocatedBlockProto* located_block,
    std::vector<DatanodeID>* targets) {
  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "DatanodeManager::ChooseTarget4New, "
      << " src_path=" << src_path << " exp_rep_num=" << exp_rep_num
      << " min_rep_num=" << min_rep_num << " blocksize=" << blocksize
      << " advice=" << advice.ToString()
      << " client_location=" << client_location.ToString()
      << " favorednodes=" << absl::StrJoin(favorednodes, ",")
      << " excludes.size()=" << excludes.size();

  StopWatch sw(metrics_.block_placement_writer_to_dn_time_);
  sw.Start();

  auto writer_dn = GetDatanodeFromIp(client_location.ip);

  NetworkLocationInfo client_location_with_dn(client_location);
  client_location_with_dn.dn = writer_dn;

  if (writer_dn != nullptr) {
    auto dn_location = writer_dn->location_tag();
    if (dn_location.ShortDebugString() !=
        client_location_with_dn.location_tag.ShortDebugString()) {
      VLOG_OR_IF(9, FLAGS_log_choose_target)
          << "client_ip == dn_ip, but has different location_tag, "
             "overwrite client_location_tag";
      client_location_with_dn.location_tag.CopyFrom(dn_location);
    }
  }
  VLOG_OR_IF(10, FLAGS_log_choose_target)
      << "ChooseTarget4New path=" << src_path << " write_dn="
      << (writer_dn == nullptr ? "nullptr" : std::to_string(writer_dn->id()))
      << " location_tag="
      << client_location_with_dn.location_tag.ShortDebugString();

  auto tc = std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
                .count();
  if (tc > 10) {
    LOG(WARNING) << "Slow GetDatanodeFromIp("
                 << client_location_with_dn.ip.ToString()
                 << "), time cost: " << tc << "ms";
  }

  sw.NextStep(metrics_.block_placement_resolve_writer_location_time_);

  const auto& location = client_location.location;
  if (location.dc.empty()) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "Can not resolve writer_from, writer="
        << client_location_with_dn.ip.ToString();
  }

  tc = std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
           .count();
  if (tc > 10) {
    LOG(WARNING) << "Slow ResolveNetworkLocation("
                 << client_location_with_dn.ip.ToString()
                 << "), time cost: " << tc << "ms";
  }

  sw.NextStep(metrics_.block_placement_process_excluded_time_);

  std::unordered_set<DatanodeInfoPtr> excluded_nodes;
  for (const auto& dn : excludes) {
    auto dn_ptr = GetDatanodeFromUuid(dn.id().datanodeuuid());
    if (dn_ptr != nullptr) {
      excluded_nodes.emplace(dn_ptr);
    }
  }

  sw.NextStep(metrics_.block_placement_process_favored_time_);

  std::vector<DatanodeInfoPtr> favored_nodes;

  // int storage_policy_ttl = 0;
  // int preferred_data_center = BlockPlacement::INVALID_PREFER_DC_ID;

  // writer_dn is BlockManager.java:1608's client ?
  if (FLAGS_random_node) {
    writer_dn = nullptr;
  }
  if (writer_dn != nullptr) {
    for (auto& host : favorednodes) {
      if (host == "ignoreLocalNode:1") {
        excluded_nodes.emplace(writer_dn);
      } else if (host == "ignoreLocalRack:1") {
        if (!FLAGS_rack_aware) {
          // if not rack-aware, ignore this flag
          continue;
        }
        if (!location.rack.empty()) {
          block_placement_->GetAllDatanodesInRack(
              location.dc, location.rack, &excluded_nodes);
        }
      } else if (host.find("randomNode:1") == 0) {
        writer_dn = nullptr;
      } else if (host.find("storagePolicyTTL:") == 0) {
        // TODO(livexmm) at future
      } else if (host.find("preferDataCenter:") == 0) {
        // TODO(livexmm) at future
      } else {
        // node format hostaddress:port or just hostaddress
        DatanodeInfoPtr dn_info = GetDatanodeFromHost(host);
        if (dn_info != nullptr) {
          favored_nodes.push_back(dn_info);
        } else {
          LOG(WARNING) << host << " can't find in dancenn";
        }
      }
    }
  }

  sw.NextStep(metrics_.block_placement_choose_target_for_new_time_);

  std::vector<DatanodeInfoPtr> result;
  block_placement_->ChooseTarget4New(src_path,
                                     exp_rep_num,
                                     blocksize,
                                     advice,
                                     client_location_with_dn,
                                     favored_nodes,
                                     &excluded_nodes,
                                     &result);

  sw.NextStep();

  bool satisfied = result.size() >= min_rep_num;
  if (satisfied) {
    StopWatch sw1(metrics_.block_placement_fill_response_time_);
    sw1.Start();

    for (const auto& dn : result) {
      // add block may fallback
      auto storage_type = GetGlobalStoragePolicySuite().DetermineStorageType(
          advice.storage_policy_id, dn->storage_type(), true);

      // for response
      dn->DumpDatanodeInfoProto(located_block->add_locs());
      located_block->add_iscached(false);
      located_block->add_storagetypes(StoragePolicy::ToProto(storage_type));
      located_block->add_storageids("");  // ignore storage id

      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<uint32_t> dis;
      if (dn->address().storageports_size() > 0) {
        uint32_t idx = dis(gen) % dn->address().storageports_size();
        located_block->add_ports(dn->address().storageports().Get(idx).port());
      } else {
        located_block->add_ports(0);
      }

      // for LOG
      targets->emplace_back(dn->id());
    }
    MFC(metrics_.block_placement_choose_target_succeed_cnt_)->Inc();
  } else {
    if (static_cast<int>(/*uint32_t=*/datanodes_size_.load()) >
        /*int=*/excludes.size()) {
      LOG(ERROR) << "ChooseTarget4New failed for " << src_path;
      MFC(metrics_.block_placement_choose_target_failed_cnt_)->Inc();
    }
  }
  BlockEventLogger::ChooseTarget4New(src_path, *targets);
  return satisfied;
}

bool DatanodeManager::ChooseTarget4Read(
    const DetailedBlock& detailed_block,
    int32_t rep_num,
    const ReadAdvice& advice,
    const NetworkLocationInfo& client_location,
    const RepeatedDatanodeInfo& excludes,
    std::vector<DatanodeID>* targets) {
  StopWatch sw(metrics_.block_placement_process_excluded_time_);
  sw.Start();

  std::unordered_set<DatanodeInfoPtr> excluded_nodes;
  for (const auto& dn : excludes) {
    auto dn_ptr = GetDatanodeFromUuid(dn.id().datanodeuuid());
    if (dn_ptr != nullptr) {
      excluded_nodes.emplace(dn_ptr);
    }
  }

  sw.NextStep(metrics_.block_placement_choose_target_for_read_time_);

  std::vector<DatanodeInfoPtr> result;
  block_placement_->ChooseTarget4Read(detailed_block,
                                      rep_num,
                                      advice,
                                      client_location,
                                      std::vector<DatanodeInfoPtr>{},
                                      &excluded_nodes,
                                      &result);
  for (const auto& dn : result) {
    targets->emplace_back(dn->id());
  }
  StopWatch sw1(
      metrics_.block_placement_choose_target_for_read_block_event_logger_time_);
  sw1.Start();
  // BlockEventLogger::ChooseTarget4Read(detailed_block.blk_.id, *targets);
  bool satisfied = targets->size() >= FLAGS_dfs_replication_min;
  if (satisfied) {
    MFC(metrics_.block_placement_choose_target_succeed_cnt_)->Inc();
  } else {
    if (static_cast<int>(/*uint32_t=*/datanodes_size_.load()) >
        /*int=*/excludes.size()) {
      LOG(ERROR) << "ChooseTarget4Read failed for "
                 << detailed_block.GetBlockID();
      MFC(metrics_.block_placement_choose_target_failed_cnt_)->Inc();
    }
  }
  return satisfied;
}

void DatanodeManager::ChooseFunctionalTarget4Read(
    const DetailedBlock& detailed_block,
    const ReadAdvice& advice,
    std::vector<DatanodeID>* targets) {
  std::vector<DatanodeInfoPtr> result;
  block_placement_->ChooseFunctionalTarget4Read(
      detailed_block, advice, &result);
  for (const auto& dn : result) {
    targets->emplace_back(dn->id());
  }
}

DatanodeID DatanodeManager::ChooseBlockRecoverPrimary(
    std::unordered_map<DatanodeID, bool>* replicas) {
  bool all_tried_as_primary = true;
  for (const auto& it : (*replicas)) {
    all_tried_as_primary &= it.second;
  }

  if (all_tried_as_primary) {
    for (auto& it : (*replicas)) {
      it.second = false;
    }
  }

  DatanodeInfoPtr primary_dn = nullptr;
  std::chrono::time_point<std::chrono::system_clock> most_recent_update_time(
      std::chrono::system_clock::now() - GetDatanodeKeepAliveTimeoutMs() * 2);
  auto it = replicas->begin();
  for (; it != replicas->end(); ++it) {
    if (it->second) {
      continue;
    }
    auto dn = GetDatanodeFromId(it->first);
    if (!dn->IsAlive()) {
      continue;
    }
    if (dn->last_heartbeat() > most_recent_update_time) {
      primary_dn = dn;
      most_recent_update_time = dn->last_heartbeat();
    }
  }

  if (primary_dn != nullptr) {
    (*replicas)[primary_dn->id()] = true;
    return primary_dn->id();
  }
  return kInvalidDatanodeID;
}

std::unordered_map<std::string, int> DatanodeManager::GetExpectedPlacement(
    const PlacementAdvice& advice,
    int32_t rep_num) {
  return block_placement_->GetExpectedPlacement(advice, rep_num);
}

size_t DatanodeManager::ConstructSortedLocatedBlock(
    const std::vector<DatanodeID>& storages,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlockProto* block) {
  return ConstructSortedLocatedBlock(
      storages, storages, advice, storage_policy_id, client_location, block);
}

size_t DatanodeManager::ConstructSortedLocatedBlock(
    const std::vector<DatanodeID>& storages,
    const std::vector<DatanodeID>& storages_exist,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlockProto* block) {
  size_t ret = 0;
  if (FLAGS_sort_located_block_method_v2) {
    ret = ConstructSortedLocatedBlockV2(storages,
                                        storages_exist,
                                        advice,
                                        storage_policy_id,
                                        client_location,
                                        block);
  } else {
    ret = ConstructSortedLocatedBlockV1(storages,
                                        storages_exist,
                                        advice,
                                        storage_policy_id,
                                        client_location,
                                        block);
  }
  AddReplicaIsCached(block);
  return ret;
}

size_t DatanodeManager::ConstructSortedLocatedBlockV1(
    const std::vector<DatanodeID>& storages,
    const std::vector<DatanodeID>& exist_dns,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlockProto* block) {
  StopWatch sw;
  sw.Start();

  sw.NextStep(metrics_.construct_sorted_located_block_get_dns_);
  std::vector<std::vector<DatanodeInfoPtr>> weighted_dn(3);
  std::vector<std::vector<DatanodeInfoPtr>> weighted_remote_dn(
      FLAGS_dc_max_distance);
  std::vector<DatanodeInfoPtr> stale;
  stale.reserve(3);

  // 0: local
  // 1: same rack
  // 2: same dc
  // INT_MAX: other
  for (auto dn_id : storages) {
    StopWatch sw_dn;
    sw_dn.Start();

    sw_dn.NextStep(metrics_.construct_sorted_located_block_per_dn_get_dn_);
    auto dn_ptr = GetDatanodeFromId(dn_id);
    int weight = INT_MAX;
    if (dn_ptr == nullptr) {
      LOG(FATAL) << "ConstructLocatedBlocks get datanode returns nullptr D"
                 << dn_id;
    }
    if (!dn_ptr->IsFunctional()) {
      LOG(INFO) << "dn is not functional " << dn_id;
      continue;
    }

    sw_dn.NextStep(metrics_.construct_sorted_located_block_per_dn_is_local_);
    bool same_rack = false;
    bool same_dc = false;
    uint32_t dc_distance = 0;
    if (!block_placement_->IsLocal(dn_ptr,
                                   client_location.location,
                                   &same_rack,
                                   &same_dc,
                                   &dc_distance)) {
      LOG(FATAL) << "Cannot get dn dc/rack info "
                 << dn_ptr->address().ShortDebugString()
                 << " ip: " << client_location.ip.ToString();
    }

    sw_dn.NextStep(metrics_.construct_sorted_located_block_per_dn_other_);
    if (!same_dc) {
      dc_distance = std::min(dc_distance, FLAGS_dc_max_distance - 1);
      weighted_remote_dn[dc_distance].emplace_back(dn_ptr);
      continue;
    }
    if (dn_ptr->IsInactive()) {
      stale.emplace_back(dn_ptr);
      continue;
    }
    if (client_location.ip.address() == dn_ptr->ip().address()) {
      weight = 0;
    } else {
      if (same_rack) {
        weight = 1;
      } else {
        weight = 2;
      }
    }
    weighted_dn[weight].emplace_back(dn_ptr);
  }

  // sort datanode from local DC by load & status
  sw.NextStep(metrics_.construct_sorted_located_block_add_local_dn_);
  size_t remote_start_idx = 0;
  for (const auto& dns : weighted_dn) {
    if (dns.size() > 1) {
      // TODO(liyuan) sort datanode by load here
    }
    for (const auto& dn : dns) {
      dn->DumpDatanodeInfoProto(block->add_locs());
      block->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
      // Notice: storage uuid is not stored in block info,
      // and the client does not need it, just filling in datanode uuid
      block->add_storageids(dn->address().datanodeuuid());
      ++remote_start_idx;
    }
  }

  // put stale datanode at the end
  sw.NextStep(metrics_.construct_sorted_located_block_add_stale_dn_);
  for (const auto& dn : stale) {
    dn->DumpDatanodeInfoProto(block->add_locs());
    block->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
    block->add_storageids(dn->address().datanodeuuid());
    ++remote_start_idx;
  }

  // sort datanode from remote DC by dc distance
  sw.NextStep(metrics_.construct_sorted_located_block_add_remote_dn_);
  for (const auto& remote_dns : weighted_remote_dn) {
    // put datanode from remote DC
    for (const auto& dn : remote_dns) {
      dn->DumpDatanodeInfoProto(block->add_locs());
      block->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
      block->add_storageids(dn->address().datanodeuuid());
    }
  }

  // advice
  sw.NextStep(metrics_.construct_sorted_located_block_advice_);
  if (FLAGS_read_policy_enable && advice.localdconly()) {
    if (remote_start_idx == 0 && !storages.empty()) {
      advice.impact.local_dn_not_found = true;
    }
    cloudfs::LocatedBlockProto tmp(*block);
    block->mutable_originallocatedblock()->CopyFrom(tmp);
    for (size_t i = remote_start_idx; i < storages.size(); ++i) {
      block->mutable_locs()->RemoveLast();
      block->mutable_storagetypes()->RemoveLast();
      block->mutable_storageids()->RemoveLast();
    }
    return remote_start_idx;
  }

  sw.NextStep(metrics_.construct_sorted_located_block_other_);
#if 0
  // Have any one local dc dn, just pass
  // if (remote_start_idx > 0 ||
  //    (false /* isAllowMultiDC */ &&
  //        FLAGS_datanode_manager_hide_remote_datanode)) {
  if (true) {
    // return untouched
  } else {
    // hide remote datanodes if needed
    cloudfs::LocatedBlockProto tmp(*block);
    block->mutable_originallocatedblock()->CopyFrom(tmp);
    for (size_t i = remote_start_idx; i < storages.size(); ++i) {
      block->mutable_locs()->RemoveLast();
      block->mutable_storagetypes()->RemoveLast();
      block->mutable_storageids()->RemoveLast();
    }

    // At last, construct a pipeliner if needed.
    if (block->locs_size() == 0 &&
        FLAGS_datanode_manager_enable_replica_pipeline) {
      std::vector<DatanodeInfoPtr> results;
      std::unordered_set<uint32_t> exclude;
      // TODO(liyuan) choose from local
      if (block_placement_->ChooseTarget4New(
          1, PlacementAdvice(storage_policy), block->b().numbytes(),
          GetDatanodeFromIp(ip), "", kDefaultFavored, &exclude, &results)) {
        block->mutable_replicapipeliner()
            ->mutable_info()->mutable_id()->CopyFrom(results[0]->address());
        block->mutable_replicapipeliner()
            ->set_storageid(results[0]->address().datanodeuuid());
        block->mutable_replicapipeliner()
            ->set_storagetype(ToProto(storage_policy));
      } else {
        LOG(WARNING) << "Failed to choose replica pipeliner, client: "
            << ResolveNetworkLocation(ip).ToString() + "/" + ip.ToString();
        block->CopyFrom(tmp);
      }
    }
  }
#endif
  return remote_start_idx;
}

// dc first:
// - local_dc < remote_dc
// - dc_distance: a < b
//
// active < stale
// local host < other
//
// PREFER_LOCAL
//   local switch < other switch
//   exist < non exist
// PREFER_CACHED
//   exist < non exist
//   local switch < other switch
//
// heartbeat: a < b
bool DatanodeManager::LocatedBlockCmpNormal(
    const DatanodeIDWithPlacementInfo& a,
    const DatanodeIDWithPlacementInfo& b) {
  // local dc
  if (a.is_local_dc != b.is_local_dc) {
    return a.is_local_dc > b.is_local_dc;
  }
  // remote dc, check de distance
  if (a.is_local_dc == false) {
    if (a.dc_distance != b.dc_distance) {
      return a.dc_distance < b.dc_distance;
    }
  }
  // exist
  if (a.exist != b.exist) {
    return a.exist > b.exist;
  }
  // local az
  if (a.is_local_az != b.is_local_az) {
    return a.is_local_az > b.is_local_az;
  }
  // stale
  if (a.is_stale != b.is_stale) {
    return a.is_stale < b.is_stale;
  }
  // local host
  if (a.is_local_host != b.is_local_host) {
    return a.is_local_host > b.is_local_host;
  }
  // local switch
  if (a.is_local_switch != b.is_local_switch) {
    return a.is_local_switch > b.is_local_switch;
  }

  if (a.last_heartbeat != b.last_heartbeat) {
    return a.last_heartbeat < b.last_heartbeat;
  }

  if (FLAGS_sort_located_block_dns_by_random_value) {
    return a.random_id < b.random_id;
  } else {
    return a.dn_id < b.dn_id;
  }
}

bool DatanodeManager::LocatedBlockCmpPreferLocal(
    const DatanodeIDWithPlacementInfo& a,
    const DatanodeIDWithPlacementInfo& b) {
  // local dc
  if (a.is_local_dc != b.is_local_dc) {
    return a.is_local_dc > b.is_local_dc;
  }
  // remote dc, check de distance
  if (a.is_local_dc == false) {
    if (a.dc_distance != b.dc_distance) {
      return a.dc_distance < b.dc_distance;
    }
  }
  // local az
  if (a.is_local_az != b.is_local_az) {
    return a.is_local_az > b.is_local_az;
  }
  // stale
  if (a.is_stale != b.is_stale) {
    return a.is_stale < b.is_stale;
  }
  // local host
  if (a.is_local_host != b.is_local_host) {
    return a.is_local_host > b.is_local_host;
  }
  // local switch
  if (a.is_local_switch != b.is_local_switch) {
    return a.is_local_switch > b.is_local_switch;
  }
  // exist
  if (a.exist != b.exist) {
    return a.exist > b.exist;
  }

  if (a.last_heartbeat != b.last_heartbeat) {
    return a.last_heartbeat < b.last_heartbeat;
  }

  if (FLAGS_sort_located_block_dns_by_random_value) {
    return a.random_id < b.random_id;
  } else {
    return a.dn_id < b.dn_id;
  }
}

bool DatanodeManager::LocatedBlockCmpPreferCached(
    const DatanodeIDWithPlacementInfo& a,
    const DatanodeIDWithPlacementInfo& b) {
  // local dc
  if (a.is_local_dc != b.is_local_dc) {
    return a.is_local_dc > b.is_local_dc;
  }
  // remote dc, check de distance
  if (a.is_local_dc == false) {
    if (a.dc_distance != b.dc_distance) {
      return a.dc_distance < b.dc_distance;
    }
  }
  // local az
  if (a.is_local_az != b.is_local_az) {
    return a.is_local_az > b.is_local_az;
  }
  // stale
  if (a.is_stale != b.is_stale) {
    return a.is_stale < b.is_stale;
  }
  // local host
  if (a.is_local_host != b.is_local_host) {
    return a.is_local_host > a.is_local_host;
  }
  // exist
  if (a.exist != b.exist) {
    return a.exist > b.exist;
  }
  // local switch
  if (a.is_local_switch != b.is_local_switch) {
    return a.is_local_switch > b.is_local_switch;
  }

  if (a.last_heartbeat != b.last_heartbeat) {
    return a.last_heartbeat < b.last_heartbeat;
  }

  if (FLAGS_sort_located_block_dns_by_random_value) {
    return a.random_id < b.random_id;
  } else {
    return a.dn_id < b.dn_id;
  }
}

size_t DatanodeManager::ConstructSortedLocatedBlockV2(
    const std::vector<DatanodeID>& storages,
    const std::vector<DatanodeID>& storages_exist,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlockProto* block) {
  StopWatch sw;
  sw.Start();

  sw.NextStep(metrics_.construct_sorted_located_block_get_dns_);
  std::vector<DatanodeIDWithPlacementInfo> dn_infos;

  VLOG_OR_IF(10, FLAGS_log_choose_target) << "<<<===";
  if (block) {
    VLOG_OR_IF(10, FLAGS_log_choose_target)
        << "block_id=" << block->b().blockid();
  }
  std::unordered_set<DatanodeID> exist_dns(storages_exist.begin(),
                                           storages_exist.end());
  for (const auto& dn_id : storages) {
    DatanodeIDWithPlacementInfo dn_info;

    StopWatch sw_dn;
    sw_dn.Start();
    sw_dn.NextStep(metrics_.construct_sorted_located_block_per_dn_get_dn_);

    dn_info.dn_ptr = GetDatanodeFromId(dn_id);
    if (dn_info.dn_ptr == nullptr) {
      LOG(FATAL) << "ConstructLocatedBlocks get datanode returns nullptr D"
                 << dn_id;
    }
    if (!dn_info.dn_ptr->IsFunctional()) {
      LOG(INFO) << "dn is not functional " << dn_id;
      continue;
    }

    if (dn_info.dn_ptr->IsInactive()) {
      dn_info.is_stale = true;
    }

    dn_info.is_local_az = true;
    if (!dn_info.dn_ptr->IsLocalAz(client_location.location_tag)) {
      dn_info.is_local_az = false;
    }

    sw_dn.NextStep(metrics_.construct_sorted_located_block_per_dn_is_local_);
    bool same_rack = false;
    bool same_dc = false;
    uint32_t dc_distance = 0;
    if (!block_placement_->IsLocal(dn_info.dn_ptr,
                                   client_location.location,
                                   &same_rack,
                                   &same_dc,
                                   &dc_distance)) {
      LOG(FATAL) << "Cannot get dn dc/rack info "
                 << dn_info.dn_ptr->address().ShortDebugString()
                 << " ip: " << client_location.ip.ToString();
    }

    // dn_info
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis;

    dn_info.dn_id = dn_id;
    dn_info.random_id = dis(gen);
    dn_info.exist = exist_dns.count(dn_id) > 0;
    if (client_location.ip.address() == dn_info.dn_ptr->ip().address()) {
      dn_info.is_local_host = true;
    }
    dn_info.is_local_switch = same_rack;
    bool applicable = false;
    bool is_same_switch = false;
    std::string dn_switch;
    std::tie(applicable, is_same_switch, dn_switch) =
        dn_info.dn_ptr->IsInSameSwitch(client_location.location_tag,
                                       client_location.rdma_tag);
    if (applicable) {
      dn_info.is_local_switch = is_same_switch;
    }
    dn_info.is_local_dc = same_dc;
    dn_info.dc_distance = std::min(dc_distance, FLAGS_dc_max_distance - 1);
    dn_info.last_heartbeat =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            dn_info.dn_ptr->last_heartbeat().time_since_epoch())
            .count();
    if (FLAGS_sort_located_block_dns_hb_window_ms > 0) {
      dn_info.last_heartbeat /= FLAGS_sort_located_block_dns_hb_window_ms;
    }

    dn_infos.push_back(dn_info);
  }

  for (const auto& dn_info : dn_infos) {
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "dn_info=" << dn_info.ToString();
  }

  // advice: sort
  sw.NextStep(metrics_.construct_sorted_located_block_other_);
  if (FLAGS_read_policy_enable && advice.policy.has_read_switch_policy()) {
    if (advice.policy.read_switch_policy() ==
        cloudfs::ReadPolicyProto::PREFER_LOCAL) {
      VLOG_OR_IF(10, FLAGS_log_choose_target) << "LocatedBlockCmpPreferLocal";
      std::sort(dn_infos.begin(), dn_infos.end(), &LocatedBlockCmpPreferLocal);
    } else if (advice.policy.read_switch_policy() ==
               cloudfs::ReadPolicyProto::PREFER_CACHED) {
      VLOG_OR_IF(10, FLAGS_log_choose_target) << "LocatedBlockCmpPreferCached";
      std::sort(dn_infos.begin(), dn_infos.end(), &LocatedBlockCmpPreferCached);
    } else {
      LOG(FATAL) << "unsupported policy";
    }
  } else {
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "LocatedBlockCmpNormal";
    std::sort(dn_infos.begin(), dn_infos.end(), &LocatedBlockCmpNormal);
  }

  VLOG_OR_IF(10, FLAGS_log_choose_target) << "------";
  for (const auto& dn_info : dn_infos) {
    VLOG_OR_IF(10, FLAGS_log_choose_target) << "dn_info=" << dn_info.ToString();
  }
  VLOG_OR_IF(10, FLAGS_log_choose_target) << "===>>>";

  size_t remote_start_idx = 0;

  // output
  for (const auto& dn_info : dn_infos) {
    auto dn = dn_info.dn_ptr;

    if (dn_info.is_local_dc) {
      remote_start_idx++;
    }

    dn->DumpDatanodeInfoProto(block->add_locs());
    block->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
    block->add_storageids(dn->address().datanodeuuid());
  }

  // advice: localdconly
  sw.NextStep(metrics_.construct_sorted_located_block_advice_);
  if (FLAGS_read_policy_enable && advice.localdconly()) {
    if (remote_start_idx == 0 && !storages.empty()) {
      advice.impact.local_dn_not_found = true;
    }
    cloudfs::LocatedBlockProto tmp(*block);
    block->mutable_originallocatedblock()->CopyFrom(tmp);
    for (size_t i = remote_start_idx; i < storages.size(); ++i) {
      block->mutable_locs()->RemoveLast();
      block->mutable_storagetypes()->RemoveLast();
      block->mutable_storageids()->RemoveLast();
    }
    return remote_start_idx;
  }

  return remote_start_idx;
}

void DatanodeManager::ConstructLocatedBlock(
    const std::vector<DatanodeID>& storages,
    StoragePolicyId storage_policy_id,
    cloudfs::LocatedBlockProto* block) {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);
  for (auto dn_id : storages) {
    auto dn = GetDatanodeFromIdInternal(dn_id);
    CHECK(dn != nullptr)
        << "ConstructLocatedBlock get datanode returns nullptr D" << dn;
    dn->DumpDatanodeInfoProto(block->add_locs());
    block->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
    block->add_storageids(dn->address().datanodeuuid());
  }
  AddReplicaIsCached(block);
}

void DatanodeManager::AddReplicaIsCached(cloudfs::LocatedBlockProto* block) {
  if (!FLAGS_enable_add_replica_is_cached || block == nullptr) {
    return;
  }
  if (!block->has_b() || !block->b().has_blockid()) {
    LOG(INFO) << "block has no block Id " << block->ShortDebugString();
    return;
  }

  auto blk_id = block->b().blockid();

  BlockProto b;
  b.set_blockid(blk_id);
  DetailedBlock detailed_blk = block_manager_->GetDetailedBlock(b);
  int live_replicas = (int)detailed_blk.live_replica_;

  for (int i = 0; i < block->locs_size(); ++i) {
    if (live_replicas > 0) {
      block->add_iscached(true);
      --live_replicas;
    } else {
      block->add_iscached(false);
    }
  }
}

void DatanodeManager::ConstructBlockCommand(
    const std::vector<DatanodeID>& storages,
    StoragePolicyId storage_policy_id,
    cloudfs::datanode::BlockCommandProto* blk_cmd) {
  auto targets = blk_cmd->add_targets();
  auto storage_types = blk_cmd->add_targetstoragetypes();
  auto storage_uuids = blk_cmd->add_targetstorageuuids();
  std::shared_lock<DNManagerRWLock> lock(rwlock_);
  for (auto dn_id : storages) {
    auto dn = GetDatanodeFromIdInternal(dn_id);
    CHECK_NOTNULL(dn);
    dn->DumpDatanodeInfoProto(targets->add_datanodes());
    storage_types->add_storagetypes(StoragePolicy::ToProto(storage_policy_id));
    storage_uuids->add_storageuuids(dn->address().datanodeuuid());
  }
}

void DatanodeManager::ConstructLocatedSortedBlocks(
    const std::vector<std::vector<DatanodeID>>& block_dns,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlocksProto* blocks) {
  ConstructLocatedSortedBlocks(
      block_dns, block_dns, advice, storage_policy_id, client_location, blocks);
}

void DatanodeManager::ConstructLocatedSortedBlocks(
    const std::vector<std::vector<DatanodeID>>& block_dns,
    const std::vector<std::vector<DatanodeID>>& block_dns_existed,
    ReadAdvice& advice,
    StoragePolicyId storage_policy_id,
    const NetworkLocationInfo& client_location,
    cloudfs::LocatedBlocksProto* blocks) {
  CHECK_EQ(block_dns.size(), blocks->blocks_size())
      << "ConstructLocatedBlocks in datanode manager but size mismatch "
      << blocks->ShortDebugString();
  // sort datanode as distance
  MFH(metrics_.construct_sorted_located_block_num_block_)
      ->Update(block_dns.size());
  for (uint32_t block_idx = 0; block_idx < block_dns.size(); ++block_idx) {
    auto block = blocks->mutable_blocks(block_idx);
    ConstructSortedLocatedBlock(block_dns[block_idx],
                                block_dns_existed[block_idx],
                                advice,
                                storage_policy_id,
                                client_location,
                                block);
  }
}

void DatanodeManager::ConstructLocatedBlocks(
    const std::vector<std::vector<DatanodeID>>& storages,
    StoragePolicyId storage_policy_id,
    cloudfs::LocatedBlocksProto* blocks) {
  CHECK_EQ(storages.size(), blocks->blocks_size())
      << "ConstructLocatedBlocks in datanode manager but size mismatch "
      << blocks->ShortDebugString();
  for (uint32_t block_idx = 0; block_idx < storages.size(); ++block_idx) {
    auto block = blocks->mutable_blocks(block_idx);
    ConstructLocatedBlock(storages[block_idx], storage_policy_id, block);
  }
}

std::string DatanodeManager::GetDatanodeIpFromId(DatanodeID dn_id) const {
  auto inode_dn = GetDatanodeFromId(dn_id);
  if (inode_dn == nullptr) {
    return "";
  }
  return inode_dn->ip().ToString();
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromId(DatanodeID dn_id) const {
  return GetDatanodeFromIdInternal(dn_id);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromIp(
    const cnetpp::base::IPAddress& ip) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  return GetDatanodeFromIpInternal(ip.ToString());
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromIp(
    const std::string& ip) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  return GetDatanodeFromIpInternal(ip);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromIpInternal(
    const std::string& ip) const {
  DatanodeID dn_id = kInvalidDatanodeID;
  auto it = ip_to_ids_.find(ip);
  if (it != ip_to_ids_.end()) {
    dn_id = it->second;
  }

  return GetDatanodeFromIdInternal(dn_id);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromSocketInternal(
    const std::string& socket) const {
  DatanodeID dn_id = kInvalidDatanodeID;
  auto it = socket_to_ids_.find(socket);
  if (it != socket_to_ids_.end()) {
    dn_id = it->second;
  }

  return GetDatanodeFromIdInternal(dn_id);
}

DnSet DatanodeManager::IpSet2DnSet(
    const std::unordered_set<std::string>& ip_set) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  DnSet dns;
  for (auto& ip : ip_set) {
    auto dn = GetDatanodeFromIpInternal(ip);
    if (dn) {
      dns.insert(dn);
    }
  }
  return dns;
}

DnSet DatanodeManager::SocketSet2DnSet(
    const std::unordered_set<std::string>& socket_set) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  DnSet dns;
  for (auto& socket : socket_set) {
    auto dn = GetDatanodeFromSocketInternal(socket);
    if (dn) {
      dns.insert(dn);
    }
  }
  return dns;
}

DnSet DatanodeManager::IpOrSocketSet2DnSet(
    const std::unordered_set<std::string>& string_set) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  DnSet dns;
  for (auto& str : string_set) {
    auto dn = GetDatanodeFromIpInternal(str);
    if (dn == nullptr) {
      dn = GetDatanodeFromSocketInternal(str);
    }
    if (dn) {
      dns.insert(dn);
    }
  }
  return dns;
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromUuid(
    const std::string& uuid) const {
  DatanodeID id = internal_ids_.forward_lookup(uuid);
  return GetDatanodeFromId(id);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromIdInternal(
    DatanodeID dn_id) const {
  // (0, datanodes_size_]
  if (dn_id != 0 && dn_id != kInvalidDatanodeID &&
      dn_id <= datanodes_size_.load()) {
    return datanodes_[dn_id];
  } else {
    return nullptr;
  }
}

NetworkLocation DatanodeManager::GetLocation(DatanodeID dn_id) {
  auto dn_info = GetDatanodeFromId(dn_id);
  if (dn_info == nullptr) {
    return NetworkLocation();
  }
  return ResolveNetworkLocation(dn_info->ip());
}

bool DatanodeManager::EverBeenMultiRack() {
  return block_placement_->HasBeenMultiRack();
}

size_t DatanodeManager::NumRacks() {
  return block_placement_->NumRacks();
}

DatanodeInfoPtr DatanodeManager::ChooseReplicaToDelete(
    const Block& blk,
    size_t replication,
    const std::unordered_set<DatanodeInfoPtr>& more_than_one,
    const std::unordered_set<DatanodeInfoPtr>& exactly_one,
    const PlacementAdvice& advice) {
  // decommission dn first
  for (auto dn : more_than_one) {
    if (dn->IsDecommissionInProgress()) {
      return dn;
    }
  }
  for (auto dn : exactly_one) {
    if (dn->IsDecommissionInProgress()) {
      return dn;
    }
  }

  // stale dn second
  for (auto dn : more_than_one) {
    if (dn->IsStale()) {
      return dn;
    }
  }
  for (auto dn : exactly_one) {
    if (dn->IsStale()) {
      return dn;
    }
  }

  return block_placement_->ChooseReplicaToDelete(
      blk, replication, more_than_one, exactly_one, advice);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromHost(
    const std::string& host) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);
  DatanodeID dn_id = kInvalidDatanodeID;
  auto it = ip_to_ids_.find(host);
  if (it != ip_to_ids_.end()) {
    dn_id = it->second;
  }

  return GetDatanodeFromIdInternal(dn_id);
}

DatanodeInfoPtr DatanodeManager::GetDatanodeFromSocket(
    const std::string& socket) const {
  std::shared_lock<DNManagerRWLock> lock(rwlock_);

  return GetDatanodeFromSocketInternal(socket);
}

void DatanodeManager::AddStorageStat(const DatanodeInfoPtr& dn) {
  // std::unique_lock<DNManagerRWLock> lock(rwlock_);
  stat_.Add(dn->stat());
  if (true /* TODO(liyuan) not decommission */) {
    // stat_.nodes_in_service++;
    // stat_.nodes_in_service_xceiver_count += dn->stat().xceiver_count;
  }
}

void DatanodeManager::SubStorageStat(const DatanodeInfoPtr& dn) {
  // std::unique_lock<DNManagerRWLock> lock(rwlock_);
  stat_.Subtract(dn->stat());
  if (true /* TODO(liyuan) not decommission */) {
    // stat_.nodes_in_service--;
    // stat_.nodes_in_service_xceiver_count -= dn->stat().xceiver_count;
  }
}

void DatanodeManager::MarkAllStaleAfterFailover() {
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);
    dn->MarkStaleAfterFailover();
  }
}

void DatanodeManager::DumpDatanodeInfo2DB() {
  if (!FLAGS_datanode_info_bg_dump_enable) {
    return;
  }
  if (meta_storage_ == nullptr) {
    LOG(ERROR) << "meta_storage_ not Init, at DumpDatanodeInfo2DB()";
    return;
  }

  StopWatch sw;
  sw.Start();

  int32_t batch_size = 10;
  if (FLAGS_datanode_info_bg_dump_batch_size > 0) {
    batch_size = FLAGS_datanode_info_bg_dump_batch_size;
  }
  MFH(metrics_.dump_datanode_info_bg_batch_size_)->Update(batch_size);

  // datanodes_size_ = datanodes_.size() + 1
  // See DatanodeManager::Register for more infos.
  uint32_t dn_size = datanodes_size_.load();
  uint32_t batch_cnt = (dn_size + (batch_size - 1)) / batch_size;

  SynchronizedClosure done(batch_cnt);
  for (auto i = 0; i < batch_cnt; ++i) {
    std::vector<DatanodeInfoEntryPB> pb_list;
    // [i * batch_size + 1, (i + 1) * batch_size + 1)
    for (uint32_t j = i * batch_size + 1;
         j < std::min<uint32_t>((i + 1) * batch_size + 1, dn_size + 1);
         ++j) {
      const auto& dn = datanodes_[j];

      DatanodeInfoEntryPB pb;
      dn->ToStorePB(&pb);
      pb_list.push_back(pb);
    }
    meta_storage_->MultiInsertDatanodeInfoAsync(pb_list, &done);
  }
  done.Await();

  auto time = sw.NextStepTime();
  MFH(metrics_.dump_datanode_info_bg_total_time_)->Update(time);
  LOG(INFO) << "DumpDatanodeInfo2DB Finished, cost=" << time << "us";
}

void DatanodeManager::DumpDatanodeInfo2DB(DatanodeInfo* dn_info) {
  if (dn_info == nullptr) {
    LOG(ERROR) << "DatanodeManager::DumpDatanodeInfo2DB dn_info=nullptr";
    return;
  }
  if (!FLAGS_datanode_info_fg_dump_enable) {
    return;
  }

  StopWatch sw(metrics_.dump_datanode_info_fg_time_);
  sw.Start();
  DatanodeInfoEntryPB pb;
  dn_info->ToStorePB(&pb);

  SynchronizedClosure done;
  meta_storage_->InsertDatanodeInfoAsync(pb.internal_id(), pb, &done);
  done.Await();
}

void DatanodeManager::DatanodeCheck() {
  auto now = std::chrono::system_clock::now();
  LOG(INFO) << "DatanodeCheck Start."
            << " datanode_size=" << datanodes_size_.load();
  auto earliest = now;
  StorageStat stat1;
  uint32_t total_num = 0;
  uint32_t functional_num = 0;
  uint32_t alive_num = 0;
  uint32_t content_stale_num = 0;
  uint32_t stale_num = 0;
  uint32_t dead_num = 0;
  uint32_t decommission_num = 0;
  uint32_t decommissioned_num = 0;
  {
    for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
      const auto& dn = datanodes_[dn_id];
      CHECK_NOTNULL(dn);

      ++total_num;

      if (!dn->IsFunctional()) {
        continue;
      }

      if (dn->IsDecommissioned()) {
        ++decommissioned_num;
      }

      ++functional_num;

      if (!dn->IsAlive()) {
        ++dead_num;
        continue;
      }

      ++alive_num;
      stat1.Add(dn->stat());

      dn->CheckWriteable();

      if (dn->IsStale()) {
        ++stale_num;
      }

      // avoid being executed concurrently in multiple thread
      if (dn->IsDecommissionInProgress() && dn->SetDecommissionOnScheduleOn()) {
        ++decommission_num;
        thread_pool_->AddTask([dn, this]() -> bool {
          ProcessDecommissionDatanode(dn);
          dn->SetDecommissionOnScheduleOff();
          return true;
        });
      }

      if (dn->content_stale()) {
        ++content_stale_num;
      }

      auto last_heartbeat = dn->last_heartbeat();
      if (last_heartbeat + GetDatanodeKeepAliveTimeoutMs() < now) {
        LOG(INFO) << "Check dead node, D" << dn->id()
                  << ", ip: " << dn->ip().ToString();
        thread_pool_->AddTask([dn, this]() -> bool {
          ProcessDeadDatanode(dn);
          return true;
        });
      } else {
        earliest = std::min(earliest, last_heartbeat);
      }
    }
  }

  // update capacity state
  stat_.SetCapacity(stat1);

  // update dn state
  stat_.num_total_datanode = total_num;
  stat_.num_functional_datanode = functional_num;
  stat_.num_alive_datanode = alive_num;
  stat_.num_content_stale_datanode = content_stale_num;
  stat_.num_stale_datanode = stale_num;
  stat_.num_decommission_datanode = decommission_num;
  stat_.num_decommissioned_datanode = decommissioned_num;
  stat_.num_dead_datanode = dead_num;

  // dump
  DumpDatanodeInfo2DB();

  auto sleep_for_earliest =
      std::chrono::duration_cast<std::chrono::microseconds>(
          earliest + GetDatanodeKeepAliveTimeoutMs() - now);
  auto sleep_for_interval = std::chrono::microseconds(
      FLAGS_datanode_check_interval_sec * 1000 * 1000);
  auto to_sleep = std::min(sleep_for_interval, sleep_for_earliest);

  auto end = std::chrono::system_clock::now();
  auto cost_us =
      std::chrono::duration_cast<std::chrono::microseconds>(end - now).count();
  auto to_sleep_sec =
      std::chrono::duration_cast<std::chrono::seconds>(to_sleep).count();
  LOG(INFO) << "DatanodeCheck done. "
            << " Cost " << cost_us << "us."
            << " The next round of datanode check is after " << to_sleep_sec
            << "s";

  thread_pool_->AddDelayTask(
      [this]() {
        DatanodeCheck();
        return true;
      },
      to_sleep);
}

void DatanodeManager::MarkKeyUpdate() {
  LOG(INFO) << "MarkKeyUpdate";

  for (auto dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);
    if (!dn->IsFunctional()) {
      continue;
    }

    dn->set_need_key_update(true);
  }
}

void DatanodeManager::ProcessDeadDatanode(DatanodeInfoPtr datanode) {
  LOG(INFO) << "Process dead node, D" << datanode->id()
            << ", ip: " << datanode->ip().ToString();
  std::vector<BlockID> blocks;
  if (datanode->MarkDead(&blocks, GetDatanodeKeepAliveTimeoutMs())) {
    SubStorageStat(datanode);
    ++stat_.expired_heartbeats;
    block_manager_->RemoveBlocksAssociatedTo(datanode->id(), blocks);
    block_manager_->CleanupDatanode(datanode->id());
    datanode->set_dead_processing(false);
  }
  LOG(INFO) << "Processed dead node " << datanode->hostname() << " DN"
            << datanode->id() << ", blocks: " << blocks.size();
  DANCENN_BLOCKLIFECYCLE_DN_LOG(
      "\"dn\":\"%s\","
      "\"op\":\"dead\"}",
      datanode->ip().ToString().c_str());
}

void DatanodeManager::ProcessDecommissionDatanode(DatanodeInfoPtr datanode) {
  if (!datanode->IsDecommissionInProgress()) {
    return;
  }
  if (!datanode->IsAlive()) {
    // avoid dead dn
    return;
  }

  std::vector<BlockID> blocks;
  datanode->GetBlocks(&blocks, FLAGS_datanode_max_decommission_per_round);
  VLOG(8) << "Decommission node " << datanode->ip().ToString()
          << " ,total blocks " << blocks.size();

  // In CFS, do NOT transfer block for decommission by default
  auto dn_ip = datanode->ip().ToString();
  if (FLAGS_datanode_manager_enable_decommission_transfer_blocks) {
    block_manager_->AddDecommissionBlocks(datanode->id(), dn_ip, blocks);

    UpdateReplicatedBlock(datanode);
  }

  auto cnt = block_manager_->SpeedUpUCBlockRelease(blocks);
  LOG(INFO) << "Processed decommission node " << dn_ip << " DN"
            << datanode->id() << " blocks_size=" << blocks.size()
            << " Speed up inodes count " << cnt;
}

void DatanodeManager::UpdateReplicatedBlock(DatanodeInfoPtr datanode) {
  auto dn_ip = datanode->ip().ToString();

  std::vector<BlockID> blocks;
  datanode->GetBlocks(&blocks);

  auto replicated_blocks = block_manager_->CountReplicatedBlocks(blocks);
  datanode->set_replicated_blocks(replicated_blocks);

  LOG(INFO) << "UpdateReplicatedBlock " << dn_ip << " DN" << datanode->id()
            << " replicated_blocks=" << datanode->replicated_blocks();
}

std::vector<uint64_t> DatanodeManager::ProcessFailedStorages(
    const DatanodeInfoPtr dn_info,
    const std::unordered_set<std::string>& storages) {
  std::vector<uint64_t> blocks;
  for (auto& storage_uuid : storages) {
    std::vector<uint64_t> blks = dn_info->MarkStorageAsFailed(storage_uuid);
    LOG(INFO) << "Remove failed storage: " << storage_uuid << " : "
              << dn_info->ip().ToString();
    blocks.insert(blocks.end(), blks.begin(), blks.end());
  }
  block_manager_->RemoveBlocksAssociatedTo(dn_info->id(), blocks);
  return blocks;
}

StoragePolicyPtr DatanodeManager::GetStoragePolicyByName(
    const std::string& name) {
  return storage_policy_suite_.GetPolicyFromName(name);
}

StoragePolicyPtr DatanodeManager::GetStoragePolicyById(StoragePolicyId id) {
  return storage_policy_suite_.GetPolicyFromId(id);
}

std::vector<StoragePolicyPtr> DatanodeManager::GetAllPolicies() {
  return storage_policy_suite_.GetAllPolicies();
}

std::chrono::milliseconds DatanodeManager::GetDatanodeKeepAliveTimeoutMs()
    const {
  return std::chrono::milliseconds(FLAGS_datanode_keep_alive_timeout_sec *
                                   1000);
}

void DatanodeManager::BlockIndexStats(std::deque<size_t>& d) const {
  auto n = datanodes_size_.load();

  std::vector<DatanodeInfoPtr> v;
  v.reserve(n);

  for (int dn_id = n; dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    CHECK_NOTNULL(dn);
    v.emplace_back(dn);
  }

  for (auto& x : v) {
    x->BlockIndexStats(d);
  }
}

void DatanodeManager::BlockIndexDetail(DatanodeID id,
                                       std::deque<NameBlockIndex*>& d) const {
  DatanodeInfoPtr dn = GetDatanodeFromId(id);
  if (dn == nullptr) {
    return;
  }
  dn->BlockIndexDetail(d);
}

void DatanodeManager::ClearInvalidateBlock() {
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    dn->ClearInvalidateBlock();
  }
}

void DatanodeManager::ClearTruncatableBlock() {
  for (int dn_id = datanodes_size_.load(); dn_id > 0; --dn_id) {
    const auto& dn = datanodes_[dn_id];
    dn->ClearTruncatableBlock();
  }
}

}  // namespace dancenn
