// Copyright 2019 jiangxunyang <<EMAIL>>

#include "block_placement_nodezone.h"
#include "block_placement_nodezone_metrics.h"

#include <cnetpp/base/csonpp.h>

#include <cstdio>
#include <memory>
#include <shared_mutex>
#include <random>
#include <utility>
#include <vector>
#include <cmath>
#include <cassert>
#include <algorithm>

#include "base/file_utils.h"
#include "base/rack_aware.h"

DECLARE_int32(nodezone_map_refresh_period_ms);
DECLARE_string(nodezone_map_config_file_path);
DECLARE_int32(dir_to_nodezone_group_refresh_period_ms);
DECLARE_string(dir_to_nodezone_group_config_file_path);

namespace dancenn {

// NodeZonePolicy
NodeZonePolicy::NodeZonePolicy(NodeZonePtr nodezone, NodeZonePolicyMgr* mgr,
                               bool with_conf_refresher)
    : mgr_(mgr),
      nodezone_(std::move(nodezone)),
      bp_(new BlockPlacementMultiDC(mgr->dn_mgr_metrics(),
                                    with_conf_refresher)) {
  LOG(INFO) << "Init NodeZonePolicy " << nodezone_->ToString();
  mgr_->metrics()->PrepareNodeZoneMetrics(nodezone_);
  bp_->Start();
  auto dc_name_set = mgr_->dc_info().GetAllDCName();
  for (auto& itr : dc_name_set) {
    bp_->PrepareDC(itr);
  }
}

NodeZonePolicy::~NodeZonePolicy() {
  bp_->Stop();
  set_nodezone(nullptr);
}

bool NodeZonePolicy::IsDatanodeInDatacenters(DatanodeInfoPtr dn) {
  if (dn == nullptr) {
    return false;
  }

  return bp_->DatanodeExist(dn);
}

void NodeZonePolicy::ResetNodeZone(NodeZonePtr nodezone) {
  NodeZonePtr old_nodezone = get_nodezone();
  CHECK_EQ(old_nodezone->id, nodezone->id);

  if (old_nodezone->group_id != nodezone->group_id) {
    mgr_->metrics()->PrepareNodeZoneMetrics(nodezone);
  }

  std::unordered_set<std::string> to_remove;
  std::unordered_set<std::string> to_add;
  int to_remove_ignore = 0;
  int to_add_ignore = 0;
  std::stringstream to_add_ss;
  std::stringstream to_remove_ss;

  LOG(INFO) << "ResetNodeZone"
            << " zone_id=" << nodezone->id
            << " group_id=" << nodezone->group_id
            << " dnips.size()=" << nodezone->dnips.size();
  for (const auto& dn_ip : old_nodezone->dnips) {
    if (nodezone->dnips.find(dn_ip) == nodezone->dnips.end()) {
      to_remove.emplace(dn_ip);
      to_remove_ss << dn_ip << " ";
    }
  }
  LOG(INFO) << "to_remove: " << to_remove_ss.str();

  for (const auto& dn_ip : nodezone->dnips) {
    if (old_nodezone->dnips.find(dn_ip) == old_nodezone->dnips.end()) {
      to_add.emplace(dn_ip);
      to_add_ss << dn_ip << " ";
    }
  }
  LOG(INFO) << "to_add: " << to_add_ss.str();

  for (const auto& dn_ip : to_add) {
    auto dn = mgr_->GetDatanodeFromIp(dn_ip);
    if (dn == nullptr) {
      to_add_ignore++;
      continue;
    }
    dn->set_nodezone_id(nodezone->id);
    bp_->AddDatanode(dn);
  }

  for (const auto &dn_ip : to_remove) {
    auto dn = mgr_->GetDatanodeFromIp(dn_ip);
    if (dn == nullptr) {
      to_remove_ignore++;
      continue;
    }
    if (dn->nodezone_id() == nodezone->id) {
      dn->set_nodezone_id("");
    }
    bp_->RemoveDatanode(dn);
  }
  LOG(INFO) << "ResetNodeZone Finish"
            << " zone_id=" << nodezone->id
            << " group_id=" << nodezone->group_id
            << " dnips.size()=" << nodezone->dnips.size()
            << " to_add.size()=" << to_add.size()
            << " to_add_ignore=" << to_add_ignore
            << " to_remove.size()=" << to_remove.size()
            << " to_remove_ignore=" << to_remove_ignore;

  set_nodezone(std::move(nodezone));
}

void NodeZonePolicy::AddDatanode(DatanodeInfoPtr dn) {
  dn->set_nodezone_id(get_nodezone()->id);
  bp_->AddDatanode(dn);
}

void NodeZonePolicy::RemoveDatanode(DatanodeInfoPtr dn) {
  bp_->RemoveDatanode(dn);
}

bool NodeZonePolicy::ChooseTarget4New(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* results) {
  return bp_->ChooseTarget4New(src_path,
                               rep_num,
                               blocksize,
                               advice,
                               client_location,
                               favored_nodes,
                               excluded,
                               results);
}

bool NodeZonePolicy::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* results) {
  return bp_->ChooseTarget4Recover(src_path,
                                   rep_num,
                                   blocksize,
                                   advice,
                                   client_location,
                                   favored_nodes,
                                   included,
                                   excluded,
                                   results);
}

size_t NodeZonePolicy::NumRacks() {
  return bp_->NumRacks();
}

// NodeZoneMapRefresher
bool NodeZoneMapRefresher::DecodeNodeZoneJson(cnetpp::base::Object& json_object,
                                              std::vector<NodeZonePtr>* nodezone_list) {
  auto zone_id_itr = json_object.Find("id");
  if (zone_id_itr == json_object.End() ||
      !zone_id_itr->second.IsString()) {
    LOG(ERROR) << "json string is not expected format: nodezone->id";
    return false;
  }
  auto zone_id = zone_id_itr->second.AsString();

  {
    // check if zone_id repeated
    auto test_nodezone_itr =
        std::find_if(nodezone_list->begin(), nodezone_list->end(),
                     [zone_id](auto& test_nodezone) -> bool {
                       return test_nodezone->id == zone_id;
                     });
    if (test_nodezone_itr != nodezone_list->end()) {
      LOG(WARNING) << "json string is not expected format:"
                   << " zone_id repeated "
                   << "zone_id:" << zone_id;
      return true;
    }
  }

  NodeZonePtr nodezone(new NodeZone());
  nodezone->id = zone_id;

  auto nodezone_remaining_ratio = json_object.Find("remainingRatio");
  if (nodezone_remaining_ratio == json_object.End() ||
      !nodezone_remaining_ratio->second.IsDouble()) {
    LOG(ERROR) << "json string is not expected format:"
               << " nodezone->remaining_ratio";
    return false;
  }
  nodezone->remaining_ratio = nodezone_remaining_ratio->second.AsDouble();

  auto json_tag = json_object.Find("tag");
  if (json_tag == json_object.End() || !json_tag->second.IsObject()) {
    LOG(ERROR) << "json string is not expected format: nodezone->tag";
    return false;
  }

  auto json_tag_obj = json_tag->second.AsObject();
  auto json_group_id = json_tag_obj.Find("id");
  if (json_group_id == json_tag_obj.End() || !json_group_id->second.IsString()) {
    LOG(ERROR) << "json string is not expected format: nodezone->tag.id";
    return false;
  }
  nodezone->group_id = json_group_id->second.AsString();

  auto json_node_paths = json_object.Find("nodePaths");
  if (json_node_paths == json_object.End() ||
      !json_node_paths->second.IsArray()) {
    LOG(ERROR) << "json string is not expected format: nodezone->dnips";
    return false;
  }

  std::string node_path;
  std::string dnip;
  auto node_paths = json_node_paths->second.AsArray();
  size_t index0, index1;
  for (auto itr = node_paths.Begin(); itr != node_paths.End(); ++itr) {
    if (!itr->IsString()) {
      LOG(ERROR) << "json string is not expected format: nodezone node_path";
      return false;
    }
    node_path = itr->AsString();

    // extract ip
    index0 = node_path.find_last_of('/');
    if (index0 < 0) {
      LOG(ERROR) << "json string is not expected format: nodezone node_path";
      return false;
    }

    index1 = node_path.find_last_of(':');
    if (index1 < 0) {
      LOG(ERROR) << "json string is not expected format: nodezone node_path";
      return false;
    }
    dnip = node_path.substr(index0 + 1, index1 - (index0 + 1));
    int length = dnip.length();
    if (dnip[0] == '[' && dnip[length-1] == ']') {
      // format v6 address before insert
      // remove brackets
      dnip = dnip.substr(1, length - 2);
    }
    cnetpp::base::IPAddress ip;
    if (cnetpp::base::IPAddress::LiteralToNumber(dnip, &ip)) {
      nodezone->dnips.insert(ip.ToString());
    } else {
      LOG(ERROR) << "Found invalid DNip: " << dnip
                 << " from entry " << node_path;
    }
  }

  nodezone_list->push_back(nodezone);
  return true;
}

bool NodeZoneMapRefresher::DecodeDataJson(const std::string& data_json_str,
                                          std::vector<NodeZonePtr>* nodezone_list) {
  cnetpp::base::Value json_config;
  if (!cnetpp::base::Parser::Deserialize(data_json_str, &json_config)) {
    LOG(ERROR) << "Failed to parse nodezone_list json string";
    return false;
  }

  if (!json_config.IsArray()) {
    LOG(ERROR) << "json string is not expected format: nodezone_list";
    return false;
  }

  auto json_arr = json_config.AsArray();
  for (auto itr = json_arr.Begin(); itr != json_arr.End(); ++itr) {
    if (!itr->IsObject()) {
      LOG(ERROR) << "json string is not expected format: nodezone";
      return false;
    }

    auto json_object = itr->AsObject();
    if (!DecodeNodeZoneJson(json_object, nodezone_list)) {
      return false;
    }
  }

  return true;
}

bool NodeZoneMapRefresher::LoadDataFromLocal(std::string* data_json_str) {
  CHECK_NOTNULL(data_json_str);
  if (FLAGS_nodezone_map_config_file_path.empty()) {
    LOG(WARNING) << "nodezone_map_config_file_path is not setted";
    return true;
  }

  if (!FileUtils::ReadFile(FLAGS_nodezone_map_config_file_path,
                           *data_json_str)) {
    LOG(ERROR) << "NodeZoneMapRefresher readfile error";
    return false;
  }

  return true;
}

bool NodeZoneMapRefresher::Refresh() {
  std::unique_lock<ReadWriteLock> guard(refresh_rwlock_);
  std::vector<NodeZonePtr> nodezone_list;
  std::string data_json_str;

  bool is_loaddata_success = false;

  // load data
  if (data_json_for_test_ != nullptr) {
    // for test
    data_json_str.assign(*data_json_for_test_);
    LOG(INFO) << "Load nodezone_map config for test";
    is_loaddata_success = true;
  } else {
    // from local file
    is_loaddata_success = LoadDataFromLocal(&data_json_str);
    if (!is_loaddata_success) {
      LOG(ERROR) << "Failed to load nodezone_map json from localfile.";
      return false;
    }
  }

  if (data_json_str == old_data_json_str) {
    return true;
  } else {
    old_data_json_str = data_json_str;
  }

  LOG(INFO) << "new NodeZoneMap Config, json string: " << data_json_str;
  if (!DecodeDataJson(data_json_str, &nodezone_list)) {
    LOG(ERROR) << "NodeZoneMapRefresher DecodeDataJson failed";
    return false;
  }

  // consume nodezone_list
  if (!mgr_->ConsumeNodeZoneList(nodezone_list)) {
    return false;
  }

  LOG(INFO) << "load node_zones_map success";
  return true;
}

// Dir2ZoneGroupRefresher
bool Dir2ZoneGroupRefresher::DecodeDataJson(const std::string& data_json_str,
                                            DirToZoneGroup* dir2zone_group) {
  cnetpp::base::Value json_config;
  if (!cnetpp::base::Parser::Deserialize(data_json_str, &json_config)) {
    LOG(ERROR) << "Failed to parse dir2zone_group json string";
    return false;
  }

  if (!json_config.IsObject()) {
    LOG(ERROR) << "json string is not expected format: dir2zone_group";
    return false;
  }

  auto json_arr = json_config.AsObject();
  for (auto itr = json_arr.Begin(); itr != json_arr.End(); ++itr) {
    auto dir = itr->first;
    if (!itr->second.IsString()) {
      LOG(ERROR) << "json string is not expected format:"
          << " dir2zone_group zone_group_id";
      return false;
    }
    auto zone_group_id = itr->second.AsString();

    if (dir.back() != '/') {
      dir.push_back('/');
    }
    dir2zone_group->emplace(dir, zone_group_id);
  }

  return true;
}

bool Dir2ZoneGroupRefresher::LoadDataFromLocal(std::string* data_json_str) {
  CHECK_NOTNULL(data_json_str);
  if (FLAGS_dir_to_nodezone_group_config_file_path.empty()) {
    LOG(WARNING) << "dir_to_nodezone_group_config_file_path is not setted";
    return true;
  }

  if (!FileUtils::ReadFile(FLAGS_dir_to_nodezone_group_config_file_path,
                           *data_json_str)) {
    LOG(ERROR) << "Dir2ZoneGroupRefresher readfile error";
    return false;
  }

  return true;
}

bool Dir2ZoneGroupRefresher::Refresh() {
  std::unique_lock<ReadWriteLock> guard(refresh_rwlock_);
  auto dir2zone_group = std::make_shared<DirToZoneGroup>();
  std::string data_json_str;

  bool is_loaddata_success = false;
  bool is_loaddata_from_redis = false;

  if (data_json_for_test_ != nullptr) {
    // load data for test
    data_json_str.assign(*data_json_for_test_);
    is_loaddata_success = true;
  } else {
    // load from localfile
    is_loaddata_success = LoadDataFromLocal(&data_json_str);
    if (!is_loaddata_success) {
      LOG(ERROR) << "Failed to load dir2zone_group json from localfile.";
      return false;
    }
  }

  if (data_json_str == old_data_json_str_) {
    return true;
  } else {
    old_data_json_str_ = data_json_str;
  }

  LOG(INFO) << "new Dir2ZoneGroup Config, json string: " << data_json_str;
  if (!DecodeDataJson(data_json_str, dir2zone_group.get())) {
    LOG(ERROR) << "Dir2ZoneGroupRefresher DecodeDataJson failed";
    return false;
  }

  // consume dir2zone_group
  if (!mgr_->ConsumeDirToZoneGroup(std::move(dir2zone_group))) {
    return false;
  }

  LOG(INFO) << "load dir2zone_group success";
  return true;
}

NodeZonePolicyMgr::NodeZonePolicyMgr(BlockPlacementNodeZoneMetrics* metrics,
                                     DatanodeManagerMetrics* dn_mgr_metrics,
                                     bool with_conf_refresher)
    : metrics_(metrics),
      dn_mgr_metrics_(dn_mgr_metrics),
      with_conf_refresher_(with_conf_refresher),
      bigcluster_bp_(new BlockPlacementMultiDC(dn_mgr_metrics, with_conf_refresher)),
      dnip_to_nodezone_(new std::unordered_map<std::string, ZoneId>()),
      dir2zone_group_(new DirToZoneGroup()) {
  CHECK_NOTNULL(dnip_to_nodezone_);
  CHECK_NOTNULL(dir2zone_group_);
  nodezone_map_refresher_ = std::make_unique<NodeZoneMapRefresher>(
      this, std::chrono::milliseconds(FLAGS_nodezone_map_refresh_period_ms));
  dir2zone_group_refresher_ = std::make_unique<Dir2ZoneGroupRefresher>(
      this,
      std::chrono::milliseconds(FLAGS_dir_to_nodezone_group_refresh_period_ms));
  nodezone_map_refresher_->Start();
  dir2zone_group_refresher_->Start();
  bigcluster_bp_->Start();

  auto dc_name_set = dc_info_.GetAllDCName();
  for (auto& itr : dc_name_set) {
    bigcluster_bp_->PrepareDC(itr);
  }
}

NodeZonePolicyMgr::~NodeZonePolicyMgr() {
  bigcluster_bp_->Stop();
  nodezone_map_refresher_->Stop();
  dir2zone_group_refresher_->Stop();
  ip_to_dn_.clear();
  policy_map_.clear();
}

NodeZonePolicyPtr NodeZonePolicyMgr::InitPolicyWithoutLock(ZoneId zone_id,
                                                           double remaining_ratio,
                                                           ZoneGroupId zone_group_id) {
  NodeZonePtr new_nodezone(new NodeZone());
  new_nodezone->id = std::move(zone_id);
  new_nodezone->remaining_ratio = remaining_ratio;
  new_nodezone->group_id = std::move(zone_group_id);

  auto policy = std::make_shared<NodeZonePolicy>(new_nodezone, this,
                                                 with_conf_refresher_);
  policy_map_.emplace(new_nodezone->id, policy);

  return policy;
}

void NodeZonePolicyMgr::ConsumeNodeZone(NodeZonePtr nodezone) {
  NodeZonePolicyPtr policy = nullptr;
  std::unique_lock<ReadWriteLock> guard(policy_map_rwlock_);
  auto itr = policy_map_.find(nodezone->id);
  if (itr != policy_map_.end()) {
    policy = itr->second;
  }

  if (policy == nullptr) {
    policy = InitPolicyWithoutLock(nodezone->id,
                                   nodezone->remaining_ratio,
                                   nodezone->group_id);
  }
  policy->ResetNodeZone(nodezone);
}

bool NodeZonePolicyMgr::ConsumeNodeZoneList(const std::vector<NodeZonePtr>& nodezone_list) {
  auto dnip_to_nodezone = std::make_shared<std::unordered_map<std::string, ZoneId>>();
  auto nodezone_group_map = std::make_shared<std::unordered_map<ZoneGroupId, NodeZoneGroupPtr>>();
  auto old_nodezone_group_map = get_nodezone_group_map();

  for (const auto& nodezone : nodezone_list) {
    ConsumeNodeZone(nodezone);

    for (auto dn_itr = nodezone->dnips.begin();
         dn_itr != nodezone->dnips.end();
         dn_itr++) {
      dnip_to_nodezone->emplace(*dn_itr, nodezone->id);
    }

    NodeZoneGroupPtr nodezone_group;
    auto group_itr = nodezone_group_map->find(nodezone->group_id);
    if (group_itr == nodezone_group_map->end()) {
      // reuse last_nodezone_selected
      uint32_t last_nodezone_selected = 0;
      if (old_nodezone_group_map != nullptr) {
        auto group_itr1 = old_nodezone_group_map->find(nodezone->group_id);
        if(group_itr1 != old_nodezone_group_map->end()) {
          last_nodezone_selected = 
              group_itr1->second->last_nodezone_selected.load();
        }
      }

      nodezone_group = std::make_shared<NodeZoneGroup>();
      nodezone_group->id = nodezone->group_id;
      nodezone_group->last_nodezone_selected.store(last_nodezone_selected);
      nodezone_group_map->emplace(nodezone_group->id,
                                  nodezone_group);
    } else {
      nodezone_group = group_itr->second;
    }
    nodezone_group->nodezones.push_back(nodezone->id);
  }

  if (nodezone_group_map->empty()) {
    LOG(ERROR) << "ConsumeNodeZoneList error: nodezone_group_map is empty";
    return false;
  }

  set_dnip_to_nodezone(dnip_to_nodezone);
  set_nodezone_group_map(nodezone_group_map);

  return true;
}

bool NodeZonePolicyMgr::ConsumeDirToZoneGroup(std::shared_ptr<DirToZoneGroup> dir2zone_group) {
  set_dir2zone_group(std::move(dir2zone_group));
  return true;
}

NodeZonePolicyPtr NodeZonePolicyMgr::GetPolicyPtr(ZoneId zone_id) {
  std::shared_lock<ReadWriteLock> guard(policy_map_rwlock_);
  auto itr = policy_map_.find(zone_id);
  if (itr == policy_map_.end()) {
    return nullptr;
  }
  return itr->second;
}

NodeZonePolicyPtr NodeZonePolicyMgr::GetPolicyByDnip(const std::string& dnip) {
  auto dnip_to_nodezone = get_dnip_to_nodezone();
  auto zone_id_itr = dnip_to_nodezone->find(dnip);
  if (zone_id_itr == dnip_to_nodezone->end()) {
    VLOG(10) << "ChoosePolicy failed: nodezone not found, dnip:" << dnip;
    return nullptr;
  }
  auto zone_id = zone_id_itr->second;
  return GetPolicyPtr(zone_id);
}

NodeZonePolicyPtr NodeZonePolicyMgr::ChoosePolicy(const std::string& src_path) {
  std::string zone_group_id;
  int maxlen = 0;
  auto src_path_piece = cnetpp::base::StringPiece(src_path);
  auto d2z = get_dir2zone_group();
  if (!d2z) {
    return nullptr;
  }
  for (auto & itr : *d2z) {
    auto test_path = itr.first;
    if (test_path.size() > maxlen && src_path_piece.starts_with(test_path)) {
      zone_group_id = itr.second;
      maxlen = test_path.size();
    }
  }

  auto nodezone_group_map = get_nodezone_group_map();
  auto nodezone_group_itr = nodezone_group_map->find(zone_group_id);
  if (nodezone_group_itr == nodezone_group_map->end()) {
    VLOG(10) << "ChoosePolicy failed: nodezone tag not found,"
                 << " nodezone_group:" << zone_group_id
                 << ", src_path:" << src_path;
    return nullptr;
  }

  auto nodezone_group = nodezone_group_itr->second;

  if (nodezone_group->nodezones.empty()) {
    LOG(WARNING) << "ChoosePolicy failed: nodezones is empty,"
                 << " nodezone_group:" << zone_group_id
                 << ", src_path:" << src_path;
    return nullptr;
  }

  NodeZonePolicyPtr policy = nullptr;
  int retrytime = 0;
  int retrylimit = nodezone_group->nodezones.size();
  double remaining_ratio_throttle = 0.1;
  for (auto index = nodezone_group->last_nodezone_selected.fetch_add(1);
       retrytime < retrylimit;
       index = nodezone_group->last_nodezone_selected.fetch_add(1),
            retrytime++) {
    int select_index = index % nodezone_group->nodezones.size();
    auto zone_id = nodezone_group->nodezones[select_index];
    policy = GetPolicyPtr(zone_id);

    // skip empty nodes policy
    if (policy->NumRacks() == 0) {
      LOG(WARNING) << "ChoosePolicy failed: zone NumRacks is empty,"
                   << " nodezone_group:" << zone_group_id
                   << ", src_path:" << src_path
                   << ", zone_id:" << policy->zone_id();
      policy = nullptr;
      continue;
    }

    // skip nodezone remaining_ratio < remaining_ratio_throttle
    if (policy->get_nodezone()->remaining_ratio < remaining_ratio_throttle) {
      LOG(WARNING) << "ChoosePolicy failed: zone capacity is too low"
                   << " remaining_ratio: "
                   << policy->get_nodezone()->remaining_ratio
                   << " nodezone_grouptag:" << zone_group_id
                   << ", src_path:" << src_path
                   << ", zone_id:" << policy->zone_id();
      policy = nullptr;
      continue;
    }

    // done
    break;
  }

  return policy;
}

DatanodeInfoPtr NodeZonePolicyMgr::GetDatanodeFromIp(const std::string& ip) {
  std::shared_lock<ReadWriteLock> guard(ip_to_dn_rwlock_);
  auto itr = ip_to_dn_.find(ip);
  if (itr == ip_to_dn_.end()) {
    return nullptr;
  }
  return itr->second;
}

void NodeZonePolicyMgr::AddDatanode(DatanodeInfoPtr dn) {
  auto ip = dn->ip().ToString();
  {
    std::unique_lock<ReadWriteLock> guard(ip_to_dn_rwlock_);
    ip_to_dn_.emplace(ip, dn);
  }
  auto policy = GetPolicyByDnip(ip);
  if (policy == nullptr) {
    LOG(INFO) << "[AddDatanode to NodeZone] dn=" << ip
              << " not belong to any NodeZone";
  } else{
    LOG(INFO) << "[AddDatanode to NodeZone] dn=" << ip << " to "
              << policy->get_nodezone()->ToString();
    policy->AddDatanode(dn);
  }
}

BlockPlacementNodeZone::BlockPlacementNodeZone(
    DatanodeManagerMetrics* dn_mgr_metrics,
    bool with_conf_refresher) : BlockPlacement(false) {
  metrics_ = std::make_shared<BlockPlacementNodeZoneMetrics>(this);
  mgr_ = std::make_shared<NodeZonePolicyMgr>(metrics_.get(), dn_mgr_metrics,
                                             with_conf_refresher);
}

void BlockPlacementNodeZone::SetDir2ZoneGroupRefresherTestData(
    const std::string& data_json) {
  mgr_->dir2zone_group_refresher()
      ->set_data_json_for_test(data_json);
}

void BlockPlacementNodeZone::SetNodeZoneMapRefresherTestData(
    const std::string& data_json) {
  mgr_->nodezone_map_refresher()
      ->set_data_json_for_test(data_json);
}

bool BlockPlacementNodeZone::ForceRefreshNodeZoneMapForTest() {
  return mgr_->nodezone_map_refresher()->Refresh();
}

Dir2ZoneGroupRefresher *BlockPlacementNodeZone::GetDir2ZoneGroupRefresher() {
  return mgr_->dir2zone_group_refresher();
}

bool BlockPlacementNodeZone::ForceRefreshDir2ZoneGroupForTest() {
  return mgr_->dir2zone_group_refresher()->Refresh();
}

NodeZoneMapRefresher *BlockPlacementNodeZone::GetNodeZoneMapRefresher() {
  return mgr_->nodezone_map_refresher();
}

bool BlockPlacementNodeZone::IsSameNodeZone(DatanodeInfoPtr dn0,
                                            DatanodeInfoPtr dn1) {
  auto nodezone0 = mgr_->GetPolicyByDnip(dn0->ip().ToString());
  auto nodezone1 = mgr_->GetPolicyByDnip(dn1->ip().ToString());
  if (nodezone0.get() == nodezone1.get()) {
    return true;
  }
  if (nodezone0 == nullptr || nodezone1 == nullptr) {
    return false;
  }
  return nodezone0->zone_id() == nodezone1->zone_id();
}

std::string BlockPlacementNodeZone::GetZoneIdByDnip(const std::string& dnip) {
  auto nodezone = mgr_->GetPolicyByDnip(dnip);
  if (nodezone == nullptr) {
    return "";
  }
  return nodezone->zone_id();
}

std::string BlockPlacementNodeZone::GetZoneGroupIdByDnip(const std::string& dnip) {
  auto nodezone = mgr_->GetPolicyByDnip(dnip);
  if (nodezone == nullptr) {
    return "";
  }
  return nodezone->zone_group_id();
}

void BlockPlacementNodeZone::AddDatanode(DatanodeInfoPtr dn) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  LOG(INFO) << "[BlockPlacementNodeZone::AddDatanode] to bigcluster";
  mgr_->bigcluster_bp_->AddDatanode(dn);
  LOG(INFO) << "[BlockPlacementNodeZone::AddDatanode] to nodezone";
  mgr_->AddDatanode(dn);
}

void BlockPlacementNodeZone::RemoveDatanode(DatanodeInfoPtr dn) {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  auto policy = mgr_->GetPolicyByDnip(dn->ip().ToString());
  if (policy != nullptr) {
    policy->RemoveDatanode(dn);
  }
  mgr_->bigcluster_bp_->RemoveDatanode(dn);
}

bool BlockPlacementNodeZone::ChooseTarget4New(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  auto writer_dn = client_location.dn;
  auto writer_from = client_location.location.dc;

  std::shared_lock<PlacementRWLock> lock(rwlock_);
  std::string writer_dn_ip;
  if (writer_dn != nullptr) {
    writer_dn_ip = writer_dn->ip().ToString();
  }

  NodeZonePolicyPtr policy = mgr_->ChoosePolicy(src_path);

  bool ret = false;

  ZoneGroupId zone_group_id = "default";
  ZoneId zone_id;
  if (policy != nullptr) {
    VLOG(10) << "nodezone_id: " << policy->get_nodezone()->id;
    zone_group_id = policy->zone_group_id();
    zone_id = policy->zone_id();

    ret = policy->ChooseTarget4New(src_path,
                                   rep_num,
                                   blocksize,
                                   advice,
                                   client_location,
                                   favored_nodes,
                                   excluded,
                                   result);
    metrics_->IncNodeZoneAllocBlocks(policy->zone_id(), result);

    if (result->empty()) {
      // failover
      LOG(WARNING) << "ChooseTarget4New result is empty, "
        << "failover to multidc blockplacement "
        << "writer_from:" << writer_from << ", "
        << "writer_dn:" << writer_dn_ip << ", "
        << "src_path:" << src_path << ", "
        << "tagid:" << zone_group_id << ", "
        << "zoneid:" << zone_id;
      metrics_->IncChooseTarget4NewFailover(zone_group_id);
    }
  }

  if (result->empty()) {
    ret = mgr_->bigcluster_bp_->ChooseTarget4New(src_path,
                                                 rep_num,
                                                 blocksize,
                                                 advice,
                                                 client_location,
                                                 favored_nodes,
                                                 excluded,
                                                 result);
  }
  if (result->empty()) {
    LOG(WARNING) << "ChooseTarget4New final result is empty, "
        << "writer_from:" << writer_from << ", "
        << "writer_dn:" << writer_dn_ip << ", "
        << "src_path:" << src_path << ", "
        << "tagid:" << zone_group_id << ", "
        << "zoneid:" << zone_id;
  }

  return ret;
}

bool BlockPlacementNodeZone::ChooseTarget4Recover(
    const std::string& src_path,
    int32_t rep_num,
    uint32_t blocksize,
    const PlacementAdvice& advice,
    const NetworkLocationInfo& client_location,
    const std::vector<DatanodeInfoPtr>& favored_nodes,
    const std::unordered_set<DatanodeInfoPtr>& included,
    std::unordered_set<DatanodeInfoPtr>* excluded,
    std::vector<DatanodeInfoPtr>* result) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  std::string writer_dn_ip;
  if (client_location.dn != nullptr) {
    writer_dn_ip = client_location.dn->ip().ToString();
  }

  NodeZonePolicyPtr policy = mgr_->ChoosePolicy(src_path);

  bool ret = false;

  ZoneGroupId zone_group_id = "default";
  ZoneId zone_id;
  if (policy != nullptr) {
    zone_group_id = policy->zone_group_id();
    zone_id = policy->zone_id();

    ret = policy->ChooseTarget4Recover(src_path,
                                       rep_num,
                                       blocksize,
                                       advice,
                                       client_location,
                                       favored_nodes,
                                       included,
                                       excluded,
                                       result);

    if (result->empty()) {
      LOG(WARNING) << "ChooseTarget4Recover result is empty,"
                   << "failover to multidc blockplacement "
                   << "writer_dn:" << writer_dn_ip << ", "
                   << "src_path:" << src_path << ", "
                   << "tagid:" << zone_group_id << ", "
                   << "zoneid:" << zone_id;
      metrics_->IncChooseTarget4RecoverFailover(zone_group_id);
    }
  }

  if (result->empty()) {
    ret = mgr_->bigcluster_bp_->ChooseTarget4Recover(src_path,
                                                     rep_num,
                                                     blocksize,
                                                     advice,
                                                     client_location,
                                                     favored_nodes,
                                                     included,
                                                     excluded,
                                                     result);
  }

  if (result->empty()) {
    LOG(WARNING) << "ChooseTarget4Recover final result is empty, "
                 << "writer_dn:" << writer_dn_ip << ", "
                 << "src_path:" << src_path << ", "
                 << "tagid:" << zone_group_id << ", "
                 << "zoneid:" << zone_id;
  }

  return ret;
}

DatanodeInfoPtr BlockPlacementNodeZone::ChooseReplicaToDelete(
    const Block& blk, size_t replication,
    const std::unordered_set<DatanodeInfoPtr>& more_than_one,
    const std::unordered_set<DatanodeInfoPtr>& exactly_one,
    const PlacementAdvice& advice) {
  return mgr_->bigcluster_bp_->ChooseReplicaToDelete(blk, replication,
                                                    more_than_one,
                                                    exactly_one,
                                                    advice);
}

std::unordered_map<std::string, int>
BlockPlacementNodeZone::GetExpectedPlacement(const PlacementAdvice &advice,
                                             int32_t rep_num) {
  return mgr_->bigcluster_bp_->GetExpectedPlacement(advice, rep_num);
}

bool BlockPlacementNodeZone::IsLocal(DatanodeInfoPtr dn,
                                     const NetworkLocation& loc,
                                     bool* same_rack,
                                     bool* same_dc,
                                     uint32_t* dc_distance) {
  return mgr_->bigcluster_bp_->IsLocal(dn,
                                       loc,
                                       same_rack,
                                       same_dc,
                                       dc_distance);
}

bool BlockPlacementNodeZone::DatanodeExist(DatanodeInfoPtr dn) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  return mgr_->bigcluster_bp_->DatanodeExist(dn);
}

bool BlockPlacementNodeZone::HasBeenMultiRack() {
  return mgr_->bigcluster_bp_->HasBeenMultiRack();
}

size_t BlockPlacementNodeZone::NumRacks() {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  return mgr_->bigcluster_bp_->NumRacks();
}

void BlockPlacementNodeZone::ListAll(std::string* output) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  mgr_->bigcluster_bp_->ListAll(output);
}


void BlockPlacementNodeZone::GetAllDatanodesInRack(
    const std::string& dc,
    const std::string& rack,
    std::unordered_set<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  mgr_->bigcluster_bp_->GetAllDatanodesInRack(dc, rack, dns);
}

void BlockPlacementNodeZone::GetAllDatanodeInfo(
    std::vector<DatanodeInfoPtr>* dns) {
  std::shared_lock<PlacementRWLock> lock(rwlock_);
  mgr_->bigcluster_bp_->GetAllDatanodeInfo(dns);
}

void BlockPlacementNodeZone::RecalcAllDatanode() {
  std::unique_lock<PlacementRWLock> lock(rwlock_);
  mgr_->bigcluster_bp_->RecalcAllDatanode();

  std::shared_lock<ReadWriteLock> guard(mgr_->policy_map_rwlock_);
  for (auto& pair : mgr_->policy_map_) {
    auto& r = pair.second;
    r->RecalcAllDatanode();
  }
}

} // namespace dancenn
