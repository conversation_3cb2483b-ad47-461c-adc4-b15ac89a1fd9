// Copyright 2017 Liyuan Lei <<EMAIL>>

#ifndef DATANODE_MANAGER_STORAGE_INFO_H_
#define DATANODE_MANAGER_STORAGE_INFO_H_

#include <hdfs.pb.h>
#include <string>
#include <memory>
#include <mutex>
#include <functional>
#include <vector>
#include <atomic>

#include "datanode_manager/block_index.h"
#include "datanode_manager/storage_policy.h"

using cloudfs::StorageReportProto;
using cloudfs::DatanodeStorageProto;
using cloudfs::StorageTypeProto;

namespace dancenn {

struct BriefStorageStat {
  uint32_t num_total_datanode{0};
  uint32_t num_normal_datanode{0};
  uint32_t num_decommissioning_datanode{0};
  uint32_t num_decommissioned_datanode{0};
  uint32_t num_entering_maintenance_datanode{0};
  uint32_t num_in_maintenance_datanode{0};
  uint32_t num_live_datanode{0};
  uint32_t num_dead_datanode{0};
  uint32_t num_content_stale_datanode{0};
  uint32_t num_stale_datanode{0};
};

struct StorageStat {
  StorageStat() = default;

  StorageStat(const StorageStat& other);
  StorageStat& operator=(const StorageStat& other);

  void Init(const StorageStat& other);
  void Init(uint64_t capacity, uint64_t used, uint64_t remaining,
            uint64_t blockpool_used, uint64_t non_dfs_used);
  void Init (uint64_t capacity, uint64_t used, uint64_t remaining,
             uint64_t blockpool_used, uint64_t non_dfs_used,
             uint32_t xceiver_count, uint64_t cache_capacity,
             uint64_t cache_used);

  void SetCapacity(const StorageStat& other);

  void Add(const StorageStat& stat);
  void Subtract(const StorageStat& stat);
  void Clear();

  // [SECTION] calc in DatanodeCheck()
  // store
  std::atomic<uint32_t> num_total_datanode{0};
  std::atomic<uint32_t> num_functional_datanode{0};
  std::atomic<uint32_t> num_alive_datanode{0};
  std::atomic<uint32_t> num_content_stale_datanode{0};
  std::atomic<uint32_t> num_stale_datanode{0};
  std::atomic<uint32_t> num_decommission_datanode{0};
  std::atomic<uint32_t> num_decommissioned_datanode{0};
  std::atomic<uint32_t> num_dead_datanode{0};
  // counter
  std::atomic<uint32_t> expired_heartbeats{0};

  // [SECTION] sum(datanode info)
  // stackable
  std::atomic<uint32_t> xceiver_count{0};
  std::atomic<uint32_t> nodes_in_service{0};
  std::atomic<uint32_t> nodes_in_service_xceiver_count{0};
  std::atomic<uint64_t> capacity{0};
  std::atomic<uint64_t> dfs_used{0};
  std::atomic<uint64_t> non_dfs_used{0};
  std::atomic<uint64_t> remaining{0};
  // https://bytedance.feishu.cn/docs/doccnoCZdRZL4ZljVlfSS5Xgxep
  // remaining capacity weighted by temperature =
  //   total capacity - sum(capacity of block * temperature)
  // temperature >= 0 && temperature <= 1
  std::atomic<uint64_t> temp_based_remaining{0};
  std::atomic<uint64_t> blockpool_used{0};
  std::atomic<uint64_t> cache_capacity{0};
  std::atomic<uint64_t> cache_used{0};
  std::atomic<uint64_t> capacity_kb{0};
  std::atomic<uint64_t> dfs_used_kb{0};
  std::atomic<uint64_t> non_dfs_used_kb{0};
  std::atomic<uint64_t> remaining_kb{0};
  std::atomic<uint64_t> blockpool_used_kb{0};
  std::atomic<uint64_t> cache_capacity_kb{0};
  std::atomic<uint64_t> cache_used_kb{0};
};

class StorageInfo {
 public:
  explicit StorageInfo(const DatanodeStorageProto& proto);
  explicit StorageInfo(const StorageReportProto& storage_report);
  StorageInfo(const StorageInfo& a) = delete;
  ~StorageInfo() = default;

  const std::string& uuid() const;
  DatanodeStorageProto::StorageState state() const;
  const StorageType& storage_type() const;
  StorageStat stat();
  int64_t report_id() const;
  bool failed();

  void SetFailed();
  void Update(const StorageReportProto& report);
  void GetStorageReportProto(StorageReportProto* report);
  void UpdateReportId(int64_t id);

  void AddBlock(uint64_t block_id);
  void AddBlocks(const std::vector<uint64_t>& blocks);
  void RemoveBlocks(const std::vector<uint64_t>& blocks);
  void ExtractBlocks(std::vector<uint64_t>* blocks);

  uint32_t NumBlocks();
  void GetBlocks(std::vector<uint64_t>* blocks);

  void BlockIndexStats(size_t* v) const { blocks_->Stats(v); }

  // TODO(zhuangsiyu): deprecated in 4.6.2
  bool ConditionalIncBlockReportCount();
  const uint64_t GetBlockReportCount() const;
  void ResetBlockReportCount();
  void IncBlockReportCount();
  void DecBlockReportCount();

 private:
  DatanodeStorageProto::StorageState state_;  // if is readonly
  std::string uuid_;
  StorageType type_;
  bool failed_;
  StorageStat stat_;
  std::unique_ptr<BlockIndex> blocks_;
  int64_t report_id_;

  // TODO(zhuangsiyu): deprecated in 4.6.2
  // The number of block reports received
  std::mutex block_report_count_mutex_;
  std::atomic<uint64_t> block_report_count_ { 0 };
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_STORAGE_INFO_H_
