// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "datanode_manager/storage_info.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <mutex>

DECLARE_string(block_index_policy);
DECLARE_int32(block_report_count_during_startup);

namespace dancenn {

StorageStat::StorageStat(const StorageStat& other) {
  Init(other);
}

StorageStat& StorageStat::operator=(const StorageStat& other) {
  Init(other);
  return *this;
}

void StorageStat::Init(const StorageStat& other) {
  num_total_datanode = other.num_total_datanode.load();
  num_functional_datanode = other.num_functional_datanode.load();
  num_alive_datanode = other.num_alive_datanode.load();
  num_content_stale_datanode = other.num_content_stale_datanode.load();
  num_stale_datanode = other.num_stale_datanode.load();
  num_decommission_datanode = other.num_decommission_datanode.load();
  num_decommissioned_datanode = other.num_decommissioned_datanode.load();
  num_dead_datanode = other.num_dead_datanode.load();
  expired_heartbeats = other.expired_heartbeats.load();

  xceiver_count = other.xceiver_count.load();
  nodes_in_service = other.nodes_in_service.load();
  nodes_in_service_xceiver_count = other.nodes_in_service_xceiver_count.load();
  capacity = other.capacity.load();
  dfs_used = other.dfs_used.load();
  non_dfs_used = other.non_dfs_used.load();
  remaining = other.remaining.load();
  temp_based_remaining = other.temp_based_remaining.load();
  blockpool_used = other.blockpool_used.load();
  cache_capacity = other.cache_capacity.load();
  cache_used = other.cache_used.load();
  // kb
  capacity_kb = other.capacity_kb.load();
  dfs_used_kb = other.dfs_used_kb.load();
  non_dfs_used_kb = other.non_dfs_used_kb.load();
  remaining_kb = other.remaining_kb.load();
  blockpool_used_kb = other.blockpool_used_kb.load();
  cache_capacity_kb = other.cache_capacity_kb.load();
  cache_used_kb = other.cache_used_kb.load();
}

void StorageStat::Init(
    uint64_t capacity, uint64_t dfs_used, uint64_t remaining,
    uint64_t blockpool_used, uint64_t non_dfs_used) {
  this->capacity = capacity;
  this->dfs_used = dfs_used;
  this->remaining = remaining;
  this->temp_based_remaining = 0;
  this->blockpool_used = blockpool_used;
  this->non_dfs_used = non_dfs_used;
  this->xceiver_count = 0;
  this->cache_capacity = 0;
  this->cache_used = 0;
  this->nodes_in_service = 0;
  this->nodes_in_service_xceiver_count = 0;
  this->expired_heartbeats = 0;
  // kb
  this->capacity_kb = capacity >> 10;
  this->dfs_used_kb = dfs_used >> 10;
  this->non_dfs_used_kb = non_dfs_used >> 10;
  this->remaining_kb = remaining >> 10;
  this->blockpool_used_kb = blockpool_used >> 10;
  this->cache_capacity_kb = cache_capacity >> 10;
  this->cache_used_kb = cache_used >> 10;
}

void StorageStat::Init(
    uint64_t capacity, uint64_t dfs_used, uint64_t remaining,
    uint64_t blockpool_used, uint64_t non_dfs_used,
    uint32_t xceiver_count, uint64_t cache_capacity,
    uint64_t cache_used) {
  this->capacity = capacity;
  this->dfs_used = dfs_used;
  this->remaining = remaining;
  this->temp_based_remaining = 0;
  this->blockpool_used = blockpool_used;
  this->non_dfs_used = non_dfs_used;
  this->xceiver_count = xceiver_count;
  this->cache_capacity = cache_capacity;
  this->cache_used = cache_used;
  this->nodes_in_service = 0;
  this->nodes_in_service_xceiver_count = 0;
  this->expired_heartbeats = 0;
  // kb
  this->capacity_kb = capacity >> 10;
  this->dfs_used_kb = dfs_used >> 10;
  this->non_dfs_used_kb = non_dfs_used >> 10;
  this->remaining_kb = remaining >> 10;
  this->blockpool_used_kb = blockpool_used >> 10;
  this->cache_capacity_kb = cache_capacity >> 10;
  this->cache_used_kb = cache_used >> 10;
}

void StorageStat::SetCapacity(const StorageStat& other) {
  xceiver_count = other.xceiver_count.load();
  nodes_in_service = other.nodes_in_service.load();
  nodes_in_service_xceiver_count = other.nodes_in_service_xceiver_count.load();
  capacity = other.capacity.load();
  dfs_used = other.dfs_used.load();
  non_dfs_used = other.non_dfs_used.load();
  remaining = other.remaining.load();
  temp_based_remaining = other.temp_based_remaining.load();
  blockpool_used = other.blockpool_used.load();
  cache_capacity = other.cache_capacity.load();
  cache_used = other.cache_used.load();
  // kb
  capacity_kb = other.capacity_kb.load();
  dfs_used_kb = other.dfs_used_kb.load();
  non_dfs_used_kb = other.non_dfs_used_kb.load();
  remaining_kb = other.remaining_kb.load();
  blockpool_used_kb = other.blockpool_used_kb.load();
  cache_capacity_kb = other.cache_capacity_kb.load();
  cache_used_kb = other.cache_used_kb.load();
}

void StorageStat::Add(const StorageStat& stat) {
  capacity += stat.capacity;
  dfs_used += stat.dfs_used;
  remaining += stat.remaining;
  temp_based_remaining += stat.temp_based_remaining;
  blockpool_used += stat.blockpool_used;
  non_dfs_used += stat.non_dfs_used;
  xceiver_count += stat.xceiver_count;
  cache_capacity += stat.cache_capacity;
  cache_used += stat.cache_used;
  // kb
  capacity_kb += stat.capacity_kb;
  dfs_used_kb += stat.dfs_used_kb;
  non_dfs_used_kb += stat.non_dfs_used_kb;
  remaining_kb += stat.remaining_kb;
  blockpool_used_kb += stat.blockpool_used_kb;
  cache_capacity_kb += stat.cache_capacity_kb;
  cache_used_kb += stat.cache_used_kb;
}

void StorageStat::Subtract(const StorageStat& stat) {
  capacity -= stat.capacity;
  dfs_used -= stat.dfs_used;
  remaining -= stat.remaining;
  temp_based_remaining -= stat.temp_based_remaining;
  blockpool_used -= stat.blockpool_used;
  non_dfs_used -= stat.non_dfs_used;
  xceiver_count -= stat.xceiver_count;
  cache_capacity -= stat.cache_capacity;
  cache_used -= stat.cache_used;
  // kb
  capacity_kb -= stat.capacity_kb;
  dfs_used_kb -= stat.dfs_used_kb;
  non_dfs_used_kb -= stat.non_dfs_used_kb;
  remaining_kb -= stat.remaining_kb;
  blockpool_used_kb -= stat.blockpool_used_kb;
  cache_capacity_kb -= stat.cache_capacity_kb;
  cache_used_kb -= stat.cache_used_kb;
}

void StorageStat::Clear() {
  capacity = 0;
  dfs_used = 0;
  remaining = 0;
  temp_based_remaining = 0;
  blockpool_used = 0;
  non_dfs_used = 0;
  xceiver_count = 0;
  cache_capacity = 0;
  cache_used = 0;
  nodes_in_service = 0;
  nodes_in_service_xceiver_count = 0;
  expired_heartbeats = 0;
  // kb
  capacity_kb = 0;
  dfs_used_kb = 0;
  non_dfs_used_kb = 0;
  remaining_kb = 0;
  blockpool_used_kb = 0;
  cache_capacity_kb = 0;
  cache_used_kb = 0;
}

StorageInfo::StorageInfo(const DatanodeStorageProto& proto) :
    failed_(false), report_id_(0) {
  state_ = proto.state();
  uuid_ = proto.storageuuid();
  type_ = StoragePolicy::ToType(proto.storagetype());
  if (FLAGS_block_index_policy == "ordered") {
    blocks_ = std::make_unique<OrderedBlockIndexImpl>();
  } else if (FLAGS_block_index_policy == "unordered") {
    blocks_ = std::make_unique<UnorderedBlockIndexImpl>();
  } else if (FLAGS_block_index_policy == "stl") {
    blocks_ = std::make_unique<BlockIndexImplStl>();
  } else {
    LOG(FATAL) << "unreachable code";
  }
}

StorageInfo::StorageInfo(const StorageReportProto& storage_report) :
    failed_(false) {
  state_ = storage_report.storage().state();
  uuid_ = storage_report.storage().storageuuid();
  type_ = StoragePolicy::ToType(storage_report.storage().storagetype());
  if (FLAGS_block_index_policy == "ordered") {
    blocks_ = std::make_unique<OrderedBlockIndexImpl>();
  } else if (FLAGS_block_index_policy == "unordered") {
    blocks_ = std::make_unique<UnorderedBlockIndexImpl>();
  } else if (FLAGS_block_index_policy == "stl") {
    blocks_ = std::make_unique<BlockIndexImplStl>();
  } else {
    LOG(FATAL) << "unreachable code";
  }
  stat_.Init(storage_report.capacity(), storage_report.dfsused(),
             storage_report.remaining(), storage_report.blockpoolused(),
             storage_report.nondfsused());
}

const std::string& StorageInfo::uuid() const {
  return uuid_;
}

DatanodeStorageProto::StorageState StorageInfo::state() const {
  return state_;
}

const StorageType& StorageInfo::storage_type() const {
  return type_;
}

StorageStat StorageInfo::stat() {
  return stat_;
}

int64_t StorageInfo::report_id() const {
  return report_id_;
}

bool StorageInfo::failed() {
  return failed_;
}

void StorageInfo::SetFailed() {
  failed_ = true;
}

void StorageInfo::Update(const StorageReportProto& report) {
  stat_.Init(report.capacity(), report.dfsused(), report.remaining(),
             report.blockpoolused(), report.nondfsused());
  if (report.has_storage() && report.storage().has_storagetype()) {
    type_ = StoragePolicy::ToType(report.storage().storagetype());
  }
  failed_ = false;
}

void StorageInfo::GetStorageReportProto(StorageReportProto* report) {
  report->set_storageuuid(uuid_);
  report->mutable_storage()->set_storageuuid(uuid_);
  report->mutable_storage()->set_state(state_);
  report->mutable_storage()->set_storagetype(StoragePolicy::ToProto(type_));

  report->set_capacity(stat_.capacity);
  report->set_dfsused(stat_.dfs_used);
  report->set_remaining(stat_.remaining);
  report->set_blockpoolused(stat_.blockpool_used);
  report->set_nondfsused(stat_.non_dfs_used);
}

void StorageInfo::UpdateReportId(int64_t id) {
  report_id_ = id;
}

void StorageInfo::AddBlock(uint64_t block_id) {
  blocks_->AddBlock(block_id);
}

void StorageInfo::AddBlocks(const std::vector<uint64_t>& blocks) {
  blocks_->AddBlocks(blocks);
}

void StorageInfo::RemoveBlocks(const std::vector<uint64_t>& blocks) {
  blocks_->RemoveBlocks(blocks);
}

void StorageInfo::ExtractBlocks(std::vector<uint64_t>* blocks) {
  blocks_->ExtractBlocks(blocks);
}

uint32_t StorageInfo::NumBlocks() {
  return blocks_->size();
}

void StorageInfo::GetBlocks(std::vector<uint64_t>* blocks) {
  blocks_->GetBlocks(blocks);
}

bool StorageInfo::ConditionalIncBlockReportCount() {
  std::lock_guard<std::mutex> lock(block_report_count_mutex_);
  if (block_report_count_ > FLAGS_block_report_count_during_startup) {
    return false;
  }
  block_report_count_++;
  return true;
}

const uint64_t StorageInfo::GetBlockReportCount() const {
  return block_report_count_.load();
}

void StorageInfo::ResetBlockReportCount() {
  block_report_count_.store(0);
}

void StorageInfo::IncBlockReportCount() {
  block_report_count_.fetch_add(1);
}

void StorageInfo::DecBlockReportCount() {
  block_report_count_.fetch_sub(1);
}

}  // namespace dancenn
