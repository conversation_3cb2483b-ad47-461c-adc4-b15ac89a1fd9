//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: l<PERSON><PERSON><PERSON> <<EMAIL>>
//

#include <gflags/gflags.h>

#include "datanode_manager/storage_policy.h"

DECLARE_uint32(storage_policy_default_id);

namespace dancenn {

StorageTypeProto StoragePolicy::ToProto(const StorageType& type) {
  switch (type) {
    case StorageType::DISK:
      return StorageTypeProto::DISK;
    case StorageType::SSD:
      return StorageTypeProto::SSD;
    case StorageType::ARCHIVE:
      return StorageTypeProto::ARCHIVE;
    case StorageType::RAM_DISK:
      return StorageTypeProto::RAM_DISK;
    case StorageType::DISK2:
      return StorageTypeProto::TIERED_NVME;
    case StorageType ::DISK3:
      return StorageTypeProto::DISK3;
    default:
      return StorageTypeProto::DISK;
  }
}

StorageType StoragePolicy::ToType(const StorageTypeProto& type) {
  switch (type) {
    case StorageTypeProto::DISK:
      return StorageType::DISK;
    case StorageTypeProto::SSD:
      return StorageType::SSD;
    case StorageTypeProto::ARCHIVE:
      return StorageType::ARCHIVE;
    case StorageTypeProto::RAM_DISK:
      return StorageType::RAM_DISK;
    case StorageTypeProto::TIERED_NVME:
      return StorageType::DISK2;
    case StorageTypeProto::DISK3:
      return StorageType::DISK3;
    default:
      return StorageType::DISK;
  }
}

StorageType StoragePolicy::ToType(const StoragePolicyId& storage_policy) {
  if (storage_policy == kOnlyDisk3StoragePolicy ||
      storage_policy == kOnlyDisk2StoragePolicy ||
      storage_policy == kHotStoragePolicy ||
      storage_policy == kWarmStoragePolicy ||
      storage_policy == kColdStoragePolicy) {
    return StorageType::DISK;
  }
  if (storage_policy == kMemoryStoragePolicy ||
      storage_policy == kOnlySSDStoragePolicy ||
      storage_policy == kAllSSDStoragePolicy ||
      storage_policy == kOneSSD2StoragePolicy ||
      storage_policy == kOneSSDStoragePolicy) {
    return StorageType::SSD;
  }
  if (storage_policy == kAllRAMStoragePolicy) {
    return StorageType::RAM_DISK;
  }
  return StorageType::DISK;
}

StorageTypeProto StoragePolicy::ToProto(const StoragePolicyId& storage_policy) {
  return ToProto(ToType(storage_policy));
}

BlockStoragePolicyProto StoragePolicy::ToProto() {
  BlockStoragePolicyProto proto;

  proto.set_policyid(id_);
  proto.set_name(name_);

  // Set storage type
  for (const auto& type : storage_types_) {
    proto.mutable_creationpolicy()->add_storagetypes(ToProto(type));
  }

  // Set creation fallback storage types
  for (const auto& type : creation_fallbacks_) {
    proto.mutable_creationfallbackpolicy()->add_storagetypes(ToProto(type));
  }

  // Set replication fallback storage types
  for (const auto& type : replication_fallbacks_) {
    proto.mutable_replicationfallbackpolicy()->add_storagetypes(ToProto(type));
  }

  proto.set_copyoncreatefile(copy_on_create_file_);

  return proto;
}

StorageType StoragePolicy::DetermineStorageType(uint8_t storage_type,
                                                bool is_create_block) {
  for (auto type : storage_types_) {
    if (storage_type & static_cast<uint8_t>(type)) {
      return type;
    }
  }

  if (is_create_block) {
    for (auto type : creation_fallbacks_) {
      if (storage_type & static_cast<uint8_t>(type)) {
        return type;
      }
    }
  } else {
    for (auto type : replication_fallbacks_) {
      if (storage_type & static_cast<uint8_t>(type)) {
        return type;
      }
    }
  }

  // fallback
  return StorageType::DISK;
}

// HDFS does not support customizing storage policy so far
StoragePolicySuite::StoragePolicySuite() {
  std::vector<StoragePolicyTuple> desc = {
      StoragePolicyTuple(kAllRAMStoragePolicy,
                         kAllRAMStoragePolicyName,
                         {StorageTypeProto::RAM_DISK},
                         {},
                         {},
                         true),
      StoragePolicyTuple(kMemoryStoragePolicy,
                         kMemoryStoragePolicyName,
                         {StorageTypeProto::RAM_DISK, StorageTypeProto::DISK},
                         {StorageTypeProto::DISK},
                         {StorageTypeProto::DISK},
                         true),
      StoragePolicyTuple(kAllSSDStoragePolicy,
                         kAllSSDStoragePolicyName,
                         {StorageTypeProto::SSD},
                         {StorageTypeProto::DISK},
                         {StorageTypeProto::DISK},
                         true),
      StoragePolicyTuple(kOnlyDisk2StoragePolicy,
                         kOnlyDisk2StoragePolicyName,
                         {StorageTypeProto::TIERED_NVME},
                         {},
                         {},
                         false),
      StoragePolicyTuple(kOneSSDStoragePolicy,
                         kOneSSDStoragePolicyName,
                         {StorageTypeProto::SSD, StorageTypeProto::DISK},
                         {StorageTypeProto::SSD, StorageTypeProto::DISK},
                         {StorageTypeProto::SSD, StorageTypeProto::DISK},
                         false),
      StoragePolicyTuple(kHotStoragePolicy,
                         kHotStoragePolicyName,
                         {StorageTypeProto::DISK},
                         {},
                         {StorageTypeProto::ARCHIVE},
                         false),
      StoragePolicyTuple(kWarmStoragePolicy,
                         kWarmStoragePolicyName,
                         {StorageTypeProto::DISK, StorageTypeProto::ARCHIVE},
                         {StorageTypeProto::DISK, StorageTypeProto::ARCHIVE},
                         {StorageTypeProto::DISK, StorageTypeProto::ARCHIVE},
                         false),
      StoragePolicyTuple(kColdStoragePolicy,
                         kColdStoragePolicyName,
                         {StorageTypeProto::ARCHIVE},
                         {},
                         {},
                         false),
      StoragePolicyTuple(kOnlySSDStoragePolicy,
                         kOnlySSDStoragePolicyName,
                         {StorageTypeProto::SSD},
                         {},
                         {},
                         false),
      StoragePolicyTuple(kOnlyDisk3StoragePolicy,
                         kOnlyDisk3StoragePolicyName,
                         {StorageTypeProto::DISK3},
                         {},
                         {},
                         false),
      StoragePolicyTuple(kOneSSD2StoragePolicy,
                         kOneSSD2StoragePolicyName,
                         {StorageTypeProto::SSD, StorageTypeProto::TIERED_NVME},
                         {StorageTypeProto::SSD, StorageTypeProto::TIERED_NVME},
                         {StorageTypeProto::SSD, StorageTypeProto::TIERED_NVME},
                         false),
  };
  InitMaps(desc);
}

void StoragePolicySuite::InitMaps(
    const std::vector<StoragePolicyTuple>& tuples) {
  for (auto& it : tuples) {
    StoragePolicyPtr policy = std::make_shared<StoragePolicy>();
    policy->id_ = std::get<0>(it);
    policy->name_ = std::get<1>(it);

    // Set storage type
    for (auto& proto : std::get<2>(it)) {
      auto type = StoragePolicy::ToType(proto);
      policy->storage_types_.push_back(type);
      policy->storage_types_mask_ |= static_cast<uint8_t>(type);
    }

    // Set creation fallback storage types
    for (auto& proto : std::get<3>(it)) {
      auto type = StoragePolicy::ToType(proto);
      policy->creation_fallbacks_.push_back(type);
      policy->creation_fallbacks_mask_ |= static_cast<uint8_t>(type);
    }

    // Set replication fallback storage types
    for (auto& proto : std::get<4>(it)) {
      auto type = StoragePolicy::ToType(proto);
      policy->replication_fallbacks_.push_back(type);
      policy->replication_fallbacks_mask_ |= static_cast<uint8_t>(type);
    }

    policy->copy_on_create_file_ = std::get<5>(it);

    id_policy_.insert(std::make_pair(policy->id(), policy));
    name_policy_.insert(std::make_pair(policy->name(), policy));
  }
}

StoragePolicyPtr StoragePolicySuite::GetPolicyFromName(
    const std::string& name) const {
  auto it = name_policy_.find(name);
  if (it == name_policy_.end()) {
    return nullptr;
  }

  return it->second;
}

StoragePolicyPtr StoragePolicySuite::GetPolicyFromId(StoragePolicyId id) const {
  if (id == kBlockStoragePolicyIdUnspecified) {
    id = static_cast<StoragePolicyId>(FLAGS_storage_policy_default_id);
  }
  auto it = id_policy_.find(id);
  if (it == id_policy_.end()) {
    return nullptr;
  }
  return it->second;
}

std::vector<StoragePolicyPtr> StoragePolicySuite::GetAllPolicies() const {
  std::vector<StoragePolicyPtr> policies;
  for (auto& it : id_policy_) {
    policies.push_back(it.second);
  }
  return policies;
}

StorageType StoragePolicySuite::DetermineStorageType(
    StoragePolicyId storage_policy_id,
    uint32_t storage_type,
    bool is_create_block) const {
  auto policy = GetPolicyFromId(storage_policy_id);
  if (policy == nullptr) {
    // fallback
    return StorageType::DISK;
  }
  return policy->DetermineStorageType(storage_type, is_create_block);
}

const StoragePolicySuite& GetGlobalStoragePolicySuite() {
  static StoragePolicySuite suite;
  return suite;
}

}  // namespace dancenn
