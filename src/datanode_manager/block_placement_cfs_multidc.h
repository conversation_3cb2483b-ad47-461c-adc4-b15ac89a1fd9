// Copyright (c) @ 2023.
// All right reserved.
//
// Author: xuexiang <<EMAIL>>
// Created: 2023/07/31
// Description

#ifndef DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_MULTIDC_H_
#define DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_MULTIDC_H_

#include <cstddef>
#include <cstdint>
#include <functional>
#include <queue>
#include <random>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/cpu_local.h"
#include "base/rack_aware.h"
#include "base/read_write_lock.h"
#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/advice.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/block_placement_cfs_default.h"
#include "datanode_manager/datanode_info.h"

namespace dancenn {

class BlockPlacementCfsMultiDC : public BlockPlacement {
 public:
  explicit BlockPlacementCfsMultiDC(DatanodeManagerMetrics* metrics,
                                    bool with_refresher=false);
  virtual ~BlockPlacementCfsMultiDC() override;

  void PrepareDC(const std::string& dc);

  void AddDatanode(DatanodeInfoPtr dn) override;
  void RemoveDatanode(DatanodeInfoPtr dn) override;

  bool ChooseTarget4New(const std::string& src_path,
                        int32_t rep_num,
                        uint32_t blocksize,
                        const PlacementAdvice& advice,
                        const NetworkLocationInfo& client_location,
                        const std::vector<DatanodeInfoPtr>& favored_nodes,
                        std::unordered_set<DatanodeInfoPtr>* excluded,
                        std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Recover(const std::string& src_path,
                            int32_t rep_num,
                            uint32_t blocksize,
                            const PlacementAdvice& advice,
                            const NetworkLocationInfo& client_location,
                            const std::vector<DatanodeInfoPtr>& favored_nodes,
                            const std::unordered_set<DatanodeInfoPtr>& included,
                            std::unordered_set<DatanodeInfoPtr>* excluded,
                            std::vector<DatanodeInfoPtr>* result) override;
  bool ChooseTarget4Read(const DetailedBlock& detailed_block,
                         int32_t rep_num,
                         const ReadAdvice& advice,
                         const NetworkLocationInfo& client_location,
                         const std::vector<DatanodeInfoPtr>& favored_nodes,
                         std::unordered_set<DatanodeInfoPtr>* excluded,
                         std::vector<DatanodeInfoPtr>* result) override;
  void ChooseFunctionalTarget4Read(
      const DetailedBlock& detailed_block,
      const ReadAdvice& advice,
      std::vector<DatanodeInfoPtr>* targets) override;

  std::unordered_map<std::string, int> GetExpectedPlacement(
      const PlacementAdvice& advice,
      int32_t rep_num) override;

  bool HasBeenMultiRack() override;
  size_t NumRacks() override;
  bool IsLocal(DatanodeInfoPtr dn,
               const NetworkLocation& loc,
               bool* same_rack,
               bool* same_dc,
               uint32_t* dc_distance) override;

  bool DatanodeExist(DatanodeInfoPtr dn) override;

  void ListAll(std::string* output) override;
  DatanodeInfoPtr ChooseReplicaToDelete(
      const Block& blk, size_t replication,
      const std::unordered_set<DatanodeInfoPtr>& more_than_one,
      const std::unordered_set<DatanodeInfoPtr>& exactly_one,
      const PlacementAdvice &advice) override; // NOLINT

  void GetAllDatanodesInRack(const std::string& dc,
                             const std::string& rack,
                             std::unordered_set<DatanodeInfoPtr>* dns) override;

  void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) override;

  bool SetDCList(const std::string& type, const std::string& dc_str);

  bool RefreshConfig() override;

  void RecalcAllDatanode() override;

  std::string GetBlacklistDc() const override;
  std::string GetMajorityDc() const override;

 private:
  bool ChooseTarget4NewInternal(
      const std::string& src_path,
      int32_t rep_num,
      uint32_t blocksize,
      const PlacementAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::set<std::string>* local_dc_blacklist,
      std::vector<DatanodeInfoPtr>* result);
  bool ChooseTarget4ReadInternal(
      const DetailedBlock& detailed_block,
      int32_t rep_num,
      const ReadAdvice& advice,
      const NetworkLocationInfo& client_location,
      const std::vector<DatanodeInfoPtr>& favored_nodes,
      std::unordered_set<DatanodeInfoPtr>* excluded,
      std::set<std::string>* local_dc_blacklist,
      std::vector<DatanodeInfoPtr>* result);

  void AddDatanodeWithoutLock(DatanodeInfoPtr dn);
  void RemoveDatanodeWithoutLock(DatanodeInfoPtr dn);
  DatanodeInfoPtr GetDatanodeInfo(DatanodeID dn_id, bool check_liveness = true);

  void GetAllDatanodeInfoUnsafe(std::vector<DatanodeInfoPtr>* dns);

  DatanodeInfoPtr ChooseReplicaToDelete(
      const std::unordered_set<DatanodeInfoPtr>& replicas,
      const std::unordered_set<std::string>& excluded_dcs);

  void ComputeDCList(
      const std::string &writer_dc,
      std::vector<std::string>* result,
      const PlacementAdvice& advice,
      bool ignore_blacklist,
      const std::set<std::string>* local_dc_blacklist);
  void ComputeDCList4Read(
      const std::set<std::string>* local_dc_blacklist,
      std::vector<std::string>* result);

  std::string GetExpectedDC(DatanodeInfoPtr dn, const std::string& from);

  std::shared_ptr<BlockPlacementCfsDefault> GetDC(const std::string& dc_name);

  std::unordered_set<DatanodeInfoPtr> datanodes_;
  std::unordered_map<DatanodeID, DatanodeInfoPtr> id_to_dn_map_;
  std::unordered_map<std::string, std::shared_ptr<BlockPlacementCfsDefault>> datacenters_;  // NOLINT(whitespace/line_length)
  DataCenters dc_info_;
  DataCentersTopology dc_topology_;
  std::vector<std::string> black_dc_list_;
  std::vector<std::string> majority_dc_list_;
  std::shared_ptr<VRWLock> rwlock_global_conf_;
  DatanodeManagerMetrics* metrics_{nullptr};

  std::string last_blacklist_dc_;
  std::string last_majority_dc_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_PLACEMENT_CFS_MULTIDC_H_
