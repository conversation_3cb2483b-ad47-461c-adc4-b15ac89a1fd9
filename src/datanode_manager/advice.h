// Copyright 2020 XiongMu <<EMAIL>>

#ifndef DATANODE_MANAGER_ADVICE_H_
#define DATANODE_MANAGER_ADVICE_H_

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>
#include <hdfs.pb.h>

#include <functional>
#include <sstream>
#include <utility>

#include "base/constants.h"
#include "datanode_manager/storage_policy.h"
#include "namespace/policy_manager.h"

DECLARE_int32(block_machine_requirement);
DECLARE_int32(block_machine_requirement_for_read);

namespace dancenn {

struct PlacementAdvice {
  // Storage policy
  StoragePolicyId storage_policy_id = kBlockStoragePolicyIdUnspecified;
  // centralize or distribute. Default is centralize
  int32_t geograph = kCentralizePolicy;
  // enforce dc
  std::string enforce_dc = kEnforceDCUnspecified;
  // DN version requirement
  // version_checker, not implemented
  std::function<bool(ProductVersion)> version_checker = nullptr;
  int32_t machine_requirement = FLAGS_block_machine_requirement;

  // example:
  //   for minipod local affinity
  //   local_switch_target=2 other_switch_target=2 result 2,2,2,2,0...
  //   local_switch_target=1 other_switch_target=2 result 1,2,2,2,0...
  //   local_switch_target=3 other_switch_target=1 result 3,1,1,1,0...
  uint32_t local_switch_target{0};
  uint32_t other_switch_target{0};

  explicit PlacementAdvice(StoragePolicyId id) : storage_policy_id(id) {
  }

  explicit PlacementAdvice(int32_t id, const std::string& dc) {
    geograph = id;
    enforce_dc = dc;
  }

  explicit PlacementAdvice(const ReplicaPolicy& policy) {
    Init(policy);
  }

  PlacementAdvice() = default;

  void Init(const ReplicaPolicy& policy) {
    if (policy.has_distributed() && policy.distributed()) {
      geograph = kDistributePolicy;
    } else {
      geograph = kCentralizePolicy;
    }
    for (auto i = 0; i < policy.dc_size(); ++i) {
      if (i != 0) {
        enforce_dc += ",";
      }
      enforce_dc += policy.dc().Get(i);
    }
    local_switch_target = policy.local_switch_target();
    other_switch_target = policy.other_switch_target();
  }

  std::string ToString() const {
    std::ostringstream oss;
    oss << "PlacementAdvice{";
    oss << "storage_policy_id=" << static_cast<uint32_t>(storage_policy_id);
    oss << "[";
    if (geograph == kCentralizePolicy) {
      oss << "CENTRALIZE";
    } else if (geograph == kDistributePolicy) {
      oss << "DISTRIBUTE";
    } else {
      oss << "NonePolicy";
    }
    oss << "] ";
    oss << " dc=" << enforce_dc;
    oss << " machine_requirement=" << machine_requirement;
    oss << " local_switch_target=" << local_switch_target;
    oss << " other_switch_target=" << other_switch_target;
    oss << "}";

    return oss.str();
  }
};

extern const PlacementAdvice kDefaultAdvice;

extern bool operator==(const ReadPolicy& a, const ReadPolicy& b);
extern bool operator!=(const ReadPolicy& a, const ReadPolicy& b);

struct ReadAdvice {
  struct ReadAdviceImpact {
    bool local_dn_not_found = false;
  };
  ReadPolicy policy;
  ReadAdviceImpact impact;
  // DN version requirement
  std::function<bool(ProductVersion)> version_checker = nullptr;
  int32_t machine_requirement = FLAGS_block_machine_requirement_for_read;

  explicit ReadAdvice(const ReadPolicy& p = kDefaultReadPolicy) {
    policy.CopyFrom(p);
  }

  bool ParseFromString(const std::string& data) {
    return policy.ParseFromString(data);
  }

  bool InBlacklistDC(const std::string& dc) {
    // we assume size < 10
    for (int i = 0; i < policy.blacklistdc_size(); ++i) {
      if (dc == policy.blacklistdc(i)) {
        return true;
      }
    }
    return false;
  }

  bool localdconly() const {
    return policy.has_localdconly() ? policy.localdconly() : false;
  }
  void set_localdconly(bool b) { policy.set_localdconly(b); }

  std::string ToString() const {
    std::ostringstream oss;
    oss << "ReadAdvice{";
    oss << " policy=" << policy.ShortDebugString();
    oss << " machine_requirement=" << machine_requirement;
    oss << "}";

    return oss.str();
  }
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_ADVICE_H_