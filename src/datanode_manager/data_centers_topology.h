// Copyright 2019 Xiong Mu <<EMAIL>>

#ifndef DATANODE_MANAGER_DATA_CENTERS_TOPOLOGY_H_
#define DATANODE_MANAGER_DATA_CENTERS_TOPOLOGY_H_

#include <cnetpp/base/ip_address.h>

#include <map>
#include <shared_mutex>
#include <string>
#include <vector>

#include "base/vlock.h"
#include "datanode_manager/datanode_info.h"

namespace dancenn {

class DataCentersTopology {
 public:
  DataCentersTopology();

  ~DataCentersTopology() = default;

  // Reload config from config file
  bool ReloadFromFile(const std::string& config_filepath);

  // example:
  // [
  //   ["HL", "LQ"],
  //   ["HL", "LF"],
  //   ["LQ", "LF"],
  //   ["WJ", "HL"],
  //   ["WJ", "BJSY"],
  //   ["HL", "HLSY"],
  //   ["LF", "alinc2"]
  // ]
  bool ParseJsonAndUpdateConfig(const std::string& config_string);

  // Get the distance between dc1 and dc2
  // return the distance. If dc1 and dc2 are disconnected, return UINT_MAX
  uint32_t GetDistance(const std::string& dc1, const std::string& dc2);

  uint32_t GetDistance(size_t dc1, size_t dc2);

 public:
  using Edge = std::pair<size_t, size_t>;
  using DistanceMap = std::map<Edge, uint32_t>;

 private:
  bool UpdateConfig(const std::vector<Edge>& edges);

 private:
  std::string config_string_;

  DistanceMap distance_;

  VRWLock rwlock_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_DATA_CENTERS_TOPOLOGY_H_
