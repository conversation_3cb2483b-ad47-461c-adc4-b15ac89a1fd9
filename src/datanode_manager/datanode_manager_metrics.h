// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef DATANODE_MANAGER_DATANODE_MANAGER_METRICS_H_
#define DATANODE_MANAGER_DATANODE_MANAGER_METRICS_H_

#include <memory>

#include "base/metrics.h"

namespace dancenn {

class DatanodeManager;

struct DatanodeManagerMetrics {
  explicit DatanodeManagerMetrics(DatanodeManager* ns);

  ~DatanodeManagerMetrics() = default;
  DatanodeManagerMetrics(const DatanodeManagerMetrics&) = delete;
  DatanodeManagerMetrics& operator=(const DatanodeManagerMetrics&) = delete;

  std::shared_ptr<Gauge> num_total_datanode_;
  std::shared_ptr<Gauge> num_functional_datanode_;
  std::shared_ptr<Gauge> num_alive_datanode_;
  std::shared_ptr<Gauge> num_content_stale_datanode_;
  std::shared_ptr<Gauge> num_stale_datanode_;
  std::shared_ptr<Gauge> num_decommission_datanode_;
  std::shared_ptr<Gauge> num_decommissioned_datanode_;
  std::shared_ptr<Gauge> num_dead_datanode_;

  std::shared_ptr<Gauge> capacity_bytes_;
  std::shared_ptr<Gauge> remaining_bytes_;
  std::shared_ptr<Gauge> nondfs_used_bytes_;
  std::shared_ptr<Gauge> dfs_used_bytes_;
  std::shared_ptr<Gauge> blockpool_used_bytes_;

  std::shared_ptr<Gauge> capacity_kb_;
  std::shared_ptr<Gauge> remaining_kb_;
  std::shared_ptr<Gauge> nondfs_used_kb_;
  std::shared_ptr<Gauge> dfs_used_kb_;
  std::shared_ptr<Gauge> blockpool_used_kb_;

  MetricID block_placement_replication_distribution_;

  MetricID block_placement_choose_target_succeed_cnt_;
  MetricID block_placement_choose_target_failed_cnt_;

  MetricID block_placement_writer_to_dn_time_;
  MetricID block_placement_resolve_writer_location_time_;
  MetricID block_placement_process_excluded_time_;
  MetricID block_placement_process_favored_time_;
  MetricID block_placement_choose_target_for_new_time_;
  MetricID block_placement_choose_target_for_read_time_;
  MetricID block_placement_choose_target_compute_dc_list_time_;
  MetricID block_placement_choose_target_process_favored_nodes_time_;
  MetricID block_placement_choose_target_shuffle_racks_time_;
  MetricID block_placement_choose_target_choose_random_time_;
  MetricID block_placement_choose_target_choose_remote_rack_time_;
  MetricID block_placement_choose_target_choose_from_rack_time_;
  MetricID block_placement_fill_response_time_;
  MetricID block_placement_choose_target_for_read_get_slice_time_;
  MetricID block_placement_choose_target_for_read_get_block_read_cache_time_;
  MetricID block_placement_choose_target_for_read_refresh_block_read_cache_time_;
  MetricID block_placement_choose_target_for_read_recheck_;

  MetricID block_placement_choose_target_fallback_dying_dn_;
  MetricID block_placement_choose_target_fallback_existed_switch_;
  MetricID block_placement_choose_target_fallback_existed_host_;
  MetricID block_placement_choose_target_fallback_local_az_;
  MetricID block_placement_choose_target_fallback_all_;

  MetricID block_placement_choose_target_for_read_block_event_logger_time_;

  MetricID construct_sorted_located_block_num_dn_;
  MetricID construct_sorted_located_block_num_block_;

  MetricID construct_sorted_located_block_per_dn_get_dn_;
  MetricID construct_sorted_located_block_per_dn_is_local_;
  MetricID construct_sorted_located_block_per_dn_other_;

  MetricID construct_sorted_located_block_get_dns_;
  MetricID construct_sorted_located_block_add_local_dn_;
  MetricID construct_sorted_located_block_add_stale_dn_;
  MetricID construct_sorted_located_block_add_remote_dn_;
  MetricID construct_sorted_located_block_advice_;
  MetricID construct_sorted_located_block_other_;

  MetricID dump_datanode_info_bg_batch_size_;
  MetricID dump_datanode_info_bg_batch_time_;
  MetricID dump_datanode_info_bg_total_time_;
  MetricID dump_datanode_info_fg_time_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_DATANODE_MANAGER_METRICS_H_
