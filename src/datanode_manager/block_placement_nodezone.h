// Copyright 2019 jiangxunyang <<EMAIL>>

#ifndef DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_H_
#define DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_H_

#include <atomic>
#include <memory>
#include <utility>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <shared_mutex>
#include <sstream>

#include <cnetpp/base/csonpp.h>

#include "base/read_write_lock.h"
#include "base/refresher.h"
#include "datanode_manager/advice.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/block_placement.h"

namespace dancenn {

using ZoneId = std::string;
using ZoneGroupId = std::string;

// NodeZone
struct NodeZone {
  ZoneId id;
  double remaining_ratio;
  ZoneGroupId group_id;
  std::unordered_set<std::string> dnips;

  std::string ToString() const {
    std::stringstream oss;
    oss << "NodeZone(id=" << id << " group_id=" << group_id << ")";
    return oss.str();
  }
};
using NodeZonePtr = std::shared_ptr<NodeZone>;

// alias Tag
struct NodeZoneGroup {
  ZoneGroupId id;
  std::vector<ZoneId> nodezones;
  std::atomic<uint32_t> last_nodezone_selected;
};
using NodeZoneGroupPtr = std::shared_ptr<NodeZoneGroup>;
using NodeZoneGroupMap = std::unordered_map<ZoneGroupId, NodeZoneGroupPtr>;

using DirToZoneGroup = std::unordered_map<std::string, ZoneGroupId>;

class NodeZonePolicyMgr;
class BlockPlacementNodeZoneMetrics;

class NodeZonePolicy {
  public:
    NodeZonePolicy(NodeZonePtr nodezone, NodeZonePolicyMgr* mgr, bool with_conf_refresher);
    ~NodeZonePolicy();

    NodeZonePtr get_nodezone() {
      std::shared_lock<ReadWriteLock> guard(nodezone_rwlock_);
      return nodezone_;
    }
    void set_nodezone(NodeZonePtr params) {
      std::unique_lock<ReadWriteLock> guard(nodezone_rwlock_);
      nodezone_ = std::move(params);
    }
    std::string zone_id() { return get_nodezone()->id; }
    std::string zone_group_id() { return get_nodezone()->group_id; }

    bool IsDatanodeInDatacenters(DatanodeInfoPtr writer_dn);

    void ResetNodeZone(NodeZonePtr nodezone);

    void AddDatanode(DatanodeInfoPtr dn);
    void RemoveDatanode(DatanodeInfoPtr dn);

    bool ChooseTarget4New(const std::string& src_path,
                          int32_t rep_num,
                          uint32_t blocksize,
                          const PlacementAdvice& advice,
                          const NetworkLocationInfo& client_location,
                          const std::vector<DatanodeInfoPtr>& favored_nodes,
                          std::unordered_set<DatanodeInfoPtr>* excluded,
                          std::vector<DatanodeInfoPtr>* result);
    bool ChooseTarget4Recover(
        const std::string& src_path,
        int32_t rep_num,
        uint32_t blocksize,
        const PlacementAdvice& advice,
        const NetworkLocationInfo& client_location,
        const std::vector<DatanodeInfoPtr>& favored_nodes,
        const std::unordered_set<DatanodeInfoPtr>& included,
        std::unordered_set<DatanodeInfoPtr>* excluded,
        std::vector<DatanodeInfoPtr>* result);

    size_t NumRacks();

    void RecalcAllDatanode() { bp_->RecalcAllDatanode(); }

  private:
    NodeZonePolicyMgr* mgr_{nullptr};
    ReadWriteLock nodezone_rwlock_;
    NodeZonePtr nodezone_;
    std::unique_ptr<BlockPlacementMultiDC> bp_;
};
using NodeZonePolicyPtr = std::shared_ptr<NodeZonePolicy>;

class NodeZoneMapRefresher {
  public:
    NodeZoneMapRefresher(NodeZonePolicyMgr* mgr,
        std::chrono::milliseconds period)
      : mgr_(mgr) {
    refresher_ = std::make_unique<Refresher>("nodezone-refresher",
                                             [&]() { this->Refresh(); });
    refresher_->set_period(period);
  }

    ~NodeZoneMapRefresher() {
      Stop();
    }

    static bool DecodeNodeZoneJson(cnetpp::base::Object& json_object,
        std::vector<NodeZonePtr>* nodezone_list);
    static bool DecodeDataJson(const std::string& data_json_str,
        std::vector<NodeZonePtr>* nodezone_list);
    static bool LoadDataFromLocal(std::string* data_json_str);

    bool Refresh();

    void set_data_json_for_test(const std::string& data_json) {
      data_json_for_test_ = std::make_shared<std::string>(data_json);
    }

    void Start() {
      CHECK_NOTNULL(refresher_);
      if (refresher_) {
        refresher_->Do();
        refresher_->Start();
      }
    }

    void Stop() {
      if (refresher_) {
        refresher_->Stop();
        refresher_.reset();
      }
    }

  private:
    std::shared_ptr<std::string> data_json_for_test_;
    std::string old_data_json_str;


    ReadWriteLock refresh_rwlock_;
    NodeZonePolicyMgr* mgr_{nullptr};
    std::unique_ptr<Refresher> refresher_{nullptr};
};

class Dir2ZoneGroupRefresher {
  public:
    Dir2ZoneGroupRefresher(NodeZonePolicyMgr* mgr,
        std::chrono::milliseconds period)
      : mgr_(mgr) {
    refresher_ = std::make_unique<Refresher>("dirnodezone-refresher",
                                             [&]() { this->Refresh(); });
    refresher_->set_period(period);
    }

    ~Dir2ZoneGroupRefresher() {
      Stop();
    }

    bool DecodeDataJson(const std::string& data_json_str,
                        DirToZoneGroup* dir2zone_group);
    bool LoadDataFromLocal(std::string* data_json_str);

    bool Refresh();

    void set_data_json_for_test(const std::string& data_json) {
      data_json_for_test_ = std::make_shared<std::string>(data_json);
    }

    void Start() {
      CHECK_NOTNULL(refresher_);
      if (refresher_) {
        refresher_->Do();
        refresher_->Start();
      }
    }

    void Stop() {
      if (refresher_) {
        refresher_->Stop();
        refresher_.reset();
      }
    }

  private:
    std::shared_ptr<std::string> data_json_for_test_;
    std::string old_data_json_str_;

    ReadWriteLock refresh_rwlock_;
    NodeZonePolicyMgr* mgr_{nullptr};
    std::unique_ptr<Refresher> refresher_{nullptr};
};

class NodeZonePolicyMgr {
 public:
  NodeZonePolicyMgr(BlockPlacementNodeZoneMetrics* metrics,
                    DatanodeManagerMetrics* dn_mgr_metrics,
                    bool with_conf_refresher);
  ~NodeZonePolicyMgr();

  NodeZonePolicyPtr GetPolicyByDnip(const std::string& dnip);
  NodeZonePolicyPtr ChoosePolicy(const std::string& src_path);

  DatanodeInfoPtr GetDatanodeFromIp(const std::string& ip);
  void AddDatanode(DatanodeInfoPtr dn);

  NodeZoneMapRefresher* nodezone_map_refresher() {
    return nodezone_map_refresher_.get();
  }

  Dir2ZoneGroupRefresher* dir2zone_group_refresher() {
    return dir2zone_group_refresher_.get();
  }

  DataCenters& dc_info() { return dc_info_; }

  BlockPlacementNodeZoneMetrics* metrics() { return metrics_; }

  DatanodeManagerMetrics* dn_mgr_metrics() { return dn_mgr_metrics_; }

  void ConsumeNodeZone(NodeZonePtr nodezone);
  bool ConsumeNodeZoneList(const std::vector<NodeZonePtr>& nodezone_list);
  bool ConsumeDirToZoneGroup(std::shared_ptr<DirToZoneGroup> dir2zone_group);

 private:
  friend class BlockPlacementNodeZone;

  NodeZonePolicyPtr InitPolicyWithoutLock(ZoneId zone_id,
                                          double remaining_ratio,
                                          ZoneGroupId nodezone_group_id);
  NodeZonePolicyPtr GetPolicyPtr(ZoneId zone_id);

  BlockPlacementNodeZoneMetrics* metrics_{nullptr};
  DatanodeManagerMetrics* dn_mgr_metrics_{nullptr};
  DataCenters dc_info_;
  bool with_conf_refresher_;
  std::unique_ptr<BlockPlacementMultiDC> bigcluster_bp_;

  ReadWriteLock ip_to_dn_rwlock_;
  std::unordered_map<std::string, DatanodeInfoPtr> ip_to_dn_;

  std::unique_ptr<NodeZoneMapRefresher> nodezone_map_refresher_;

  ReadWriteLock dnip_to_nodezone_rwlock_;
  std::shared_ptr<std::unordered_map<std::string, ZoneId>> dnip_to_nodezone_;
  std::shared_ptr<std::unordered_map<std::string, ZoneId>> get_dnip_to_nodezone() {
    std::shared_lock<ReadWriteLock> guard(dnip_to_nodezone_rwlock_);
    return dnip_to_nodezone_;
  }
  void set_dnip_to_nodezone(std::shared_ptr<std::unordered_map<std::string, ZoneId>> params) {
    std::unique_lock<ReadWriteLock> guard(dnip_to_nodezone_rwlock_);
    dnip_to_nodezone_ = params;
  }

  ReadWriteLock policy_map_rwlock_;
  std::unordered_map<ZoneId, NodeZonePolicyPtr> policy_map_;

  ReadWriteLock nodezone_group_map_rwlock_;
  std::shared_ptr<NodeZoneGroupMap> nodezone_group_map_;
  std::shared_ptr<NodeZoneGroupMap> get_nodezone_group_map() {
    std::shared_lock<ReadWriteLock> guard(nodezone_group_map_rwlock_);
    return nodezone_group_map_;
  }
  void set_nodezone_group_map(std::shared_ptr<NodeZoneGroupMap> params) {
    std::unique_lock<ReadWriteLock> guard(nodezone_group_map_rwlock_);
    nodezone_group_map_ = std::move(params);
  }

  std::unique_ptr<Dir2ZoneGroupRefresher> dir2zone_group_refresher_;

  ReadWriteLock dir2zone_group_rwlock_;
  std::shared_ptr<DirToZoneGroup> dir2zone_group_;
  std::shared_ptr<DirToZoneGroup> get_dir2zone_group() {
    std::shared_lock<ReadWriteLock> guard(dir2zone_group_rwlock_);
    return dir2zone_group_;
  }
  void set_dir2zone_group(std::shared_ptr<DirToZoneGroup> params) {
    std::unique_lock<ReadWriteLock> guard(dir2zone_group_rwlock_);
    dir2zone_group_ = params;
  }
};

class BlockPlacementNodeZone : public BlockPlacement {
  public:
    explicit BlockPlacementNodeZone(DatanodeManagerMetrics* dn_mgr_metrics,
                                    bool with_conf_refresher=false);
    ~BlockPlacementNodeZone() override = default;

    // for test
    void SetDir2ZoneGroupRefresherTestData(const std::string& data_json);
    void SetNodeZoneMapRefresherTestData(const std::string& data_json);
    bool ForceRefreshNodeZoneMapForTest();
    bool ForceRefreshDir2ZoneGroupForTest();

    Dir2ZoneGroupRefresher *GetDir2ZoneGroupRefresher();
    NodeZoneMapRefresher *GetNodeZoneMapRefresher();

    bool IsSameNodeZone(DatanodeInfoPtr dn0, DatanodeInfoPtr dn1);
    std::string GetZoneIdByDnip(const std::string& dnip);
    std::string GetZoneGroupIdByDnip(const std::string& dnip);

    void AddDatanode(DatanodeInfoPtr dn) override;
    void RemoveDatanode(DatanodeInfoPtr dn) override;

    bool ChooseTarget4New(const std::string& src_path,
                          int32_t rep_num,
                          uint32_t blocksize,
                          const PlacementAdvice& advice,
                          const NetworkLocationInfo& client_location,
                          const std::vector<DatanodeInfoPtr>& favored_nodes,
                          std::unordered_set<DatanodeInfoPtr>* excluded,
                          std::vector<DatanodeInfoPtr>* result) override;
    bool ChooseTarget4Recover(
        const std::string& src_path,
        int32_t rep_num,
        uint32_t blocksize,
        const PlacementAdvice& advice,
        const NetworkLocationInfo& client_location,
        const std::vector<DatanodeInfoPtr>& favored_nodes,
        const std::unordered_set<DatanodeInfoPtr>& included,
        std::unordered_set<DatanodeInfoPtr>* excluded,
        std::vector<DatanodeInfoPtr>* result) override;
    DatanodeInfoPtr ChooseReplicaToDelete(
        const Block& blk, size_t replication,
        const std::unordered_set<DatanodeInfoPtr>& more_than_one,
        const std::unordered_set<DatanodeInfoPtr>& exactly_one,
        const PlacementAdvice& advice = kDefaultAdvice) override;

    std::unordered_map<std::string, int>
    GetExpectedPlacement(const PlacementAdvice &advice, int32_t rep_num) override;

    bool IsLocal(DatanodeInfoPtr dn,
                 const NetworkLocation& loc,
                 bool* same_rack,
                 bool* same_dc,
                 uint32_t* dc_distance) override;

    bool DatanodeExist(DatanodeInfoPtr dn) override;

    bool HasBeenMultiRack() override;
    size_t NumRacks() override;
    void ListAll(std::string* output) override;

    void GetAllDatanodesInRack(
        const std::string& dc,
        const std::string& rack,
        std::unordered_set<DatanodeInfoPtr>* dns) override;

    void GetAllDatanodeInfo(std::vector<DatanodeInfoPtr>* dns) override;

    void GetAllDatanodeInfoUnsafe(std::vector<DatanodeInfoPtr>* dns);

    std::string GetBlacklistDc() const { return mgr_->bigcluster_bp_->GetBlacklistDc(); }

    std::string GetMajorityDc() const { return mgr_->bigcluster_bp_->GetMajorityDc(); }

    void RecalcAllDatanode();

  private:
    std::shared_ptr<BlockPlacementNodeZoneMetrics> metrics_;
    std::shared_ptr<NodeZonePolicyMgr> mgr_;
};

}  // namespace dancenn

#endif  // DATANODE_MANAGER_BLOCK_PLACEMENT_NODEZONE_H_
