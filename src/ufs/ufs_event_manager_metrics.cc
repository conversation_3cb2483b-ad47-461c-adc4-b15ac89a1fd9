//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_manager_metrics.h"

// Project
#include "ufs/ufs_event_manager.h"

namespace dancenn {

UfsEventManagerMetrics::UfsEventManagerMetrics(UfsEventManager* mgr) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("UfsEventManager");

  kafka_consumer_running =
      metrics->RegisterGauge("KafkaConsumerRunning", [mgr]() -> int {
        return mgr && mgr->kafka_consumer_pool_
                   ? mgr->kafka_consumer_pool_->NumRunningTasks()
                   : 0;
      });
  kafka_consumer_pending =
      metrics->RegisterGauge("KafkaConsumerPending", [mgr]() -> int {
        return mgr && mgr->kafka_consumer_pool_
                   ? mgr->kafka_consumer_pool_->PendingCount()
                   : 0;
      });
  rmq_consumer_running =
      metrics->RegisterGauge("RmqConsumerRunning", [mgr]() -> int {
        return mgr && mgr->rmq_consumer_pool_
                   ? mgr->rmq_consumer_pool_->NumRunningTasks()
                   : 0;
      });
  rmq_consumer_pending =
      metrics->RegisterGauge("RmqConsumerPending", [mgr]() -> int {
        return mgr && mgr->rmq_consumer_pool_
                   ? mgr->rmq_consumer_pool_->PendingCount()
                   : 0;
      });
  handler_task_running =
      metrics->RegisterGauge("HandlerTaskRunning", [mgr]() -> int {
        return mgr && mgr->handler_pool_ ? mgr->handler_pool_->NumRunningTasks()
                                         : 0;
      });
  handler_task_pending =
      metrics->RegisterGauge("HandlerTaskPending", [mgr]() -> int {
        return mgr && mgr->handler_pool_ ? mgr->handler_pool_->PendingCount()
                                         : 0;
      });

  msg_consumed = metrics->RegisterAtomicCounter("MsgConsumed");

  task_pending_time_for_handler =
      metrics->RegisterHistogram("TaskPendingTimeForHandler");

  event_handling = metrics->RegisterAtomicCounter("EventHandling");
  event_handled = metrics->RegisterAtomicCounter("EventHandled");
  event_invalid = metrics->RegisterAtomicCounter("EventInvalid");
  op_sync = metrics->RegisterAtomicCounter("OpSync");
  event_handle_time = metrics->RegisterHistogram("EventHandleTime");

  consume_error = metrics->RegisterAtomicCounter("ConsumeError");
  handle_error = metrics->RegisterAtomicCounter("HandleError");

  // TOS-related metrics
  tos_event_handled_map = {
      {TosEvent::EventType::OBJECT_CREATED,
       metrics->RegisterAtomicCounter("TosEventHandled#type=ObjectCreated")},
      {TosEvent::EventType::OBJECT_REMOVED,
       metrics->RegisterAtomicCounter("TosEventHandled#type=ObjectRemoved")},
      {TosEvent::EventType::LIFECYCLE_EXPIRATION,
       metrics->RegisterAtomicCounter(
           "TosEventHandled#type=LifecycleExpiration")},
      {TosEvent::EventType::OBJECT_REPLICATION,
       metrics->RegisterAtomicCounter(
           "TosEventHandled#type=ObjectReplication")}};
}

}  // namespace dancenn
