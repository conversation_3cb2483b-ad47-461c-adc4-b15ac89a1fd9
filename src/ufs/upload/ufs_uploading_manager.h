#pragma once

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <memory>
#include <unordered_set>

#include "base/constants.h"
#include "base/metric.h"
#include "base/metrics.h"
#include "base/refresher.h"

namespace dancenn {

class NameSpace;

class UfsUploadingSlice {
 public:
  UfsUploadingSlice() = default;
  ~UfsUploadingSlice() = default;

  // For UfsUploadingManager::lease_slices_.
  UfsUploadingSlice(UfsUploadingSlice&& other);
  UfsUploadingSlice& operator=(UfsUploadingSlice&& other) = delete;
  UfsUploadingSlice(const UfsUploadingSlice& other) = delete;
  UfsUploadingSlice& operator=(const UfsUploadingSlice& other) = delete;

  bool add(uint64_t inode_id);
  bool remove(uint64_t inode_id);
  size_t size() const;
  bool has(uint64_t inode_id) const;
  std::unordered_set<uint64_t> copy() const;

  void erase(const std::vector<uint64_t>& id_list);

 private:
  mutable std::mutex upload_ongoing_inode_mutex_;
  std::unordered_set<uint64_t> upload_ongoing_inode_;
};

// Belong to UfsUploadMonitor
class UfsUploadingManager {
 public:
  UfsUploadingManager();
  ~UfsUploadingManager();

  UfsUploadingManager(const UfsUploadingManager&) = delete;
  UfsUploadingManager& operator=(const UfsUploadingManager&) = delete;

  void SetNS(NameSpace* ns) {
    ns_ = ns;
  }

  void Stop();

  void add_upload_ongoing_inode(uint64_t inode_id);
  void remove_upload_ongoing_inode(uint64_t inode_id);
  size_t size_upload_ongoing_inode() const;
  bool has_inode(uint64_t inode_id) const;
  std::unordered_set<uint64_t> copy_upload_ongoing_inode();

 public:
  // metrics
  void SetMetrics();

  MetricID metric_fg_tasks_;
  MetricID metric_bg_tasks_;
  std::shared_ptr<Gauge> metric_upload_ongoing_inode_cnt_;

 private:
  NameSpace* ns_{nullptr};

 private:
  void CheckInode();

  std::atomic<bool> stopped_{false};

  std::unique_ptr<Refresher> refresher_{nullptr};

  std::vector<UfsUploadingSlice> slices_;
};

}  // namespace dancenn