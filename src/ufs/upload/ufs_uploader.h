//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
// Author: xiongmu <<EMAIL>>
//

#pragma once

#include <memory>

#include "base/status.h"
#include "namespace/namespace.h"
#include "ufs/ufs.h"

namespace dancenn {

class NameSpace;
class Ufs;
class FileSyncContext;

// This class wraps the upload process used by NameSpace.
class UfsUploader {
 public:
  static std::shared_ptr<UfsUploader> Create(NameSpace* ns,
                                             Ufs* ufs);

 public:
  UfsUploader(NameSpace* ns, Ufs* ufs) : ns_(ns), ufs_(ufs) {
    CHECK(ns_);
    CHECK(ufs_);
  }
  virtual ~UfsUploader() = default;

  virtual Status TriggerUploadObject(FileSyncContext* f_ctx) = 0;
  virtual Status PersistObject(FileSyncContext* f_ctx) = 0;
  virtual Status AbortUpload(const std::string& upload_id,
                             const std::string& ufs_key) = 0;

  // update block state
  void PersistNonKeyBlock(const INode& inode, BlockID block_id);

  // update cfs file
  Status PersistCfsFile(const std::string& path,
                        const INode& parent,
                        const INode& inode,
                        const INode& old_inode);

 protected:
  NameSpace* ns_{nullptr};
  Ufs* ufs_{nullptr};
};

}  // namespace dancenn