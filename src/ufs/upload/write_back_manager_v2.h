#ifndef UFS_UPLOAD_WRITE_BACK_MANAGER_V2_H_
#define UFS_UPLOAD_WRITE_BACK_MANAGER_V2_H_

#include <atomic>

#include "namespace/meta_scanner_v2.h"
#include "ufs/upload/write_back_manager_metrics.h"

namespace dancenn {

class NameSpace;
class Ufs;

// TODO(xiong): should deprecated, function move to UfsUploadMonitor
class WriteBackScanner : public MetaScannerTaskBase {
 public:
  WriteBackScanner(std::shared_ptr<NameSpace> ns, std::shared_ptr<Ufs> ufs);
  uint32_t GetCfIdx() override;
  bool PreScan() override;
  bool Handle(MetaStorageSnapPtr snapshot,
              const rocksdb::Slice& key,
              const rocksdb::Slice& value) override;
  bool PostScan() override;
  int64_t GetDelayUs() override;
  std::string ToString() override;

  static bool AddTask(uint64_t inode_id);
  static bool EraseTask(uint64_t inode_id, bool finished);
  static bool TestAndResetTaskTriedTimes(uint64_t inode_id,
                                         std::function<bool(int)> test);

  uint64_t LastFinishScanTxid() {
    return last_finish_scan_txid_.load();
  }

 private:
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<Ufs> ufs_;

  uint64_t start_time_;
  uint64_t end_time_;
  uint64_t inode_cnt_;

  std::atomic<uint64_t> last_finish_scan_txid_{0};
  uint64_t start_txid_{0};

  static std::mutex task_mutex_;
  static std::unordered_set<uint64_t> task_set_;
  static std::unordered_map<uint64_t, int> task_tried_times_;
};

class WriteBackTaskV2 : public MetaScannerWorkTask {
 public:
  WriteBackTaskV2(std::shared_ptr<NameSpace> ns,
                  std::shared_ptr<Ufs> ufs,
                  const INode& inode);

  bool Run(void* arg = nullptr) override;

  // Test
  virtual INode GetINode();

 private:
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<Ufs> ufs_;
  INode inode_;
};

class WriteBackStat : public MetaScannerTaskBase {
 public:
  uint32_t GetCfIdx() override;
  bool PreScan() override;
  bool Handle(MetaStorageSnapPtr snapshot,
              const rocksdb::Slice& key,
              const rocksdb::Slice& value) override;
  bool PostScan() override;
  int64_t GetDelayUs() override;
  std::string ToString() override;

  virtual uint64_t GetTaskCnt();

 private:
  uint64_t inode_cnt_;

  WriteBackManagerV2Metrics metrics_;
};

}  // namespace dancenn

#endif  // UFS_UPLOAD_WRITE_BACK_MANAGER_V2_H_
