#pragma once

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <memory>
#include <unordered_map>

#include "base/constants.h"
#include "base/metric.h"
#include "base/metrics.h"
#include "base/refresher.h"

DECLARE_int32(upload_monitor_table_slice_num);

namespace dancenn {

class NameSpace;

// 1. timeout
// 2. dir policy modify
// 3. inode modify
struct UfsNouploadState {
  uint64_t ts_ms{0};
  uint64_t dir_txid{0};
  uint64_t inode_txid{0};
};

class UfsNouploadSlice {
 public:
  UfsNouploadSlice() = default;
  ~UfsNouploadSlice() = default;

  // For UfsNouploadManager::lease_slices_.
  UfsNouploadSlice(UfsNouploadSlice&& other);
  UfsNouploadSlice& operator=(UfsNouploadSlice&& other) = delete;
  UfsNouploadSlice(const UfsNouploadSlice& other) = delete;
  UfsNouploadSlice& operator=(const UfsNouploadSlice& other) = delete;

  void add(uint64_t inode_id, UfsNouploadState value);
  void remove(uint64_t inode_id);
  size_t size() const;
  bool has(uint64_t inode_id) const;
  UfsNouploadState get(uint64_t inode_id) const;
  std::unordered_map<uint64_t, UfsNouploadState> copy() const;

  void erase(const std::vector<uint64_t>& id_list);

 private:
  mutable std::mutex noupload_inode_mutex_;
  // inode_id -> info
  std::unordered_map<uint64_t, UfsNouploadState> noupload_inode_;
};

// Belong to UfsUploadMonitor
class UfsNouploadManager {
 public:
  UfsNouploadManager(std::function<uint64_t(void)> get_txid_func);
  ~UfsNouploadManager();

  UfsNouploadManager(const UfsNouploadManager&) = delete;
  UfsNouploadManager& operator=(const UfsNouploadManager&) = delete;

  void SetNS(NameSpace* ns) {
    ns_ = ns;
  }

  void Stop();

  void add_noupload_inode(uint64_t inode_id, UfsNouploadState value);
  void remove_noupload_inode(uint64_t inode_id);
  size_t size_noupload_inode() const;
  bool has_inode(uint64_t inode_id) const;
  UfsNouploadState get_value(uint64_t inode_id) const;
  std::unordered_map<uint64_t, UfsNouploadState> copy_noupload_inode();

 private:
  // metrics
  void SetMetrics();

  std::shared_ptr<Gauge> metric_noupload_inode_cnt_{nullptr};

 private:
  NameSpace* ns_{nullptr};

  std::function<uint64_t(void)> get_txid_func_;

 private:
  void CheckInode();

  std::atomic<bool> stopped_{false};

  std::unique_ptr<Refresher> refresher_{nullptr};

  std::vector<UfsNouploadSlice> slices_;
};

}  // namespace dancenn
