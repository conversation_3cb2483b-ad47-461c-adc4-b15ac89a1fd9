#include "ufs/upload/write_back_manager_metrics.h"

#include "ufs/upload/write_back_manager.h"
#include "ufs/upload/write_back_manager_v2.h"

DECLARE_bool(enable_write_back_task_persistence);

namespace dancenn {

WriteBackManagerMetrics::WriteBackManagerMetrics(WriteBackManager* manager) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("WriteBackManager");

  // V1 and V2 use the same metric prefix and key, disable V1 metrics if V2 is
  // enabled.
  if (!FLAGS_enable_write_back_task_persistence) {
    tasks = metrics->RegisterGauge("WriteBackTasks",
      [manager] () -> int {
        if (manager) {
          return manager->task_count_;
        }
        return  0;
      }
    );
  }
  waiting_time = metrics->RegisterHistogram("WriteBackTaskWaitingTime");
  finish_time = metrics->RegisterHistogram("WriteBackTaskFinishTime");
  slow_task = metrics->RegisterCounter("WriteBackTaskSlow");
}

WriteBackManagerV2Metrics::WriteBackManagerV2Metrics() : task_num(0) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("WriteBackManager");

  tasks = metrics->RegisterGauge("WriteBackTasks",
                                 [this]() -> int { return task_num; });
}

}  // namespace dancenn
