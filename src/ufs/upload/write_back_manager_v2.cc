#include "ufs/upload/write_back_manager_v2.h"

#include <atomic>
#include <utility>

#include "base/logger_metrics.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/namespace.h"

DECLARE_bool(enable_write_back_task_persistence);
DECLARE_int64(write_back_task_v2_trigger_interval_us);
DECLARE_uint32(write_back_task_v2_throttle_interval_ms);
DECLARE_uint32(write_back_task_v2_max_scan_num);
DECLARE_int64(write_back_task_v2_stat_trigger_interval_us);
DECLARE_uint64(max_ongoing_upload_inode_num);
DECLARE_int32(write_back_num_failure_before_check_ufs);

DECLARE_bool(run_ut);

namespace dancenn {

std::mutex WriteBackScanner::task_mutex_;
std::unordered_set<uint64_t> WriteBackScanner::task_set_;
std::unordered_map<uint64_t, int> WriteBackScanner::task_tried_times_;

WriteBackScanner::WriteBackScanner(std::shared_ptr<NameSpace> ns,
                                   std::shared_ptr<Ufs> ufs)
    : ns_(std::move(ns)),
      ufs_(std::move(ufs)),
      start_time_(0),
      end_time_(0),
      inode_cnt_(0) {
  CHECK_NOTNULL(ns_);
  CHECK_NOTNULL(ufs_);
  VLOG(10) << "WriteBackScanner::WriteBackScanner()";
}

uint32_t WriteBackScanner::GetCfIdx() {
  return kWriteBackTaskCFIndex;
}

bool WriteBackScanner::PreScan() {
  start_time_ = TimeUtilV2::GetNowEpochMs();
  end_time_ = 0;
  inode_cnt_ = 0;

  start_txid_ = meta_storage_->GetLastCkptTxId();

  if (!FLAGS_enable_write_back_task_persistence) {
    LOG(WARNING) << "Write back task persistence is disabled";
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return false;
  }

  if (!ns_->ha_state()->IsActive() || ns_->ha_state()->InTransition()) {
    LOG(INFO) << "Do not run write back scanner on standby";
    return false;
  }

  return true;
}

bool WriteBackScanner::Handle(MetaStorageSnapPtr snapshot,
                              const rocksdb::Slice& key,
                              const rocksdb::Slice& value) {
  (void)snapshot;

  if (!ns_->ha_state()->IsActive() || ns_->ha_state()->InTransition()) {
    return false;
  }

  INodeID id = MetaStorage::DecodeINodeID(key);
  INode inode;
  if (!inode.ParseFromArray(value.data(), value.size())) {
    LOG(FATAL) << "Failed to parse inode from meta storage key: " << key.data();
  }
  if (inode.type() != INode_Type_kFile || !inode.has_ufs_file_info()) {
    LOG(ERROR) << "Unexpected inode in write back CF: "
               << inode.ShortDebugString();
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  }

  do {
    // check uploading and noupload state
    if (FLAGS_run_ut) {
      break;
    }

    auto uploading_mgr = ns_->ufs_uploading_mgr();

    // IsCached NoUpload
    if (ns_->ufs_upload_monitor()->IsCachedNoUpload(inode)) {
      uploading_mgr->remove_upload_ongoing_inode(inode.id());

      VLOG(11) << "Already no upload inode, skip inode=" << inode.id();
      return true;
    }

    if (uploading_mgr->size_upload_ongoing_inode() >=
            FLAGS_max_ongoing_upload_inode_num &&
        !uploading_mgr->has_inode(inode.id())) {
      VLOG(10) << "Too many ongoing upload task, skip inode=" << inode.id();
      // do not break scan
      return true;
    }

  } while (false);

  if (inode_cnt_++ > FLAGS_write_back_task_v2_max_scan_num) {
    LOG(INFO) << "Too many write back task in one scan";
    return false;
  }

  if (!AddTask(inode.id())) {
    VLOG(10) << "Duplicated task inode=" << inode.id();
    return true;
  }

  int tried = 0;
  while (!meta_scanner_->AddWorkTask(
      std::make_shared<WriteBackTaskV2>(ns_, ufs_, inode))) {
    LOG(INFO) << "Failed to add work task " << inode.id();
    std::this_thread::sleep_for(std::chrono::milliseconds(
        FLAGS_write_back_task_v2_throttle_interval_ms));
    if (tried++ > 60) {
      EraseTask(inode.id(), false);
      LOG(WARNING) << "Failed to add work task " << inode.id();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return false;
    }
  }

  return true;
}

bool WriteBackScanner::PostScan() {
  end_time_ = TimeUtilV2::GetNowEpochMs();
  last_finish_scan_txid_ = start_txid_;
  LOG(INFO) << "WriteBackScanner finished. Total inode scanned: " << inode_cnt_
            << ", total time cost: " << (end_time_ - start_time_) << "ms";
  return true;
}

int64_t WriteBackScanner::GetDelayUs() {
  if (start_time_ > 0 && end_time_ > 0) {
    auto cost = static_cast<int64_t>((end_time_ - start_time_) * 1000);
    if (cost < FLAGS_write_back_task_v2_trigger_interval_us) {
      return FLAGS_write_back_task_v2_trigger_interval_us - cost;
    }
    return 0;
  }
  return FLAGS_write_back_task_v2_trigger_interval_us;
}

std::string WriteBackScanner::ToString() {
  return "WriteBackScanner";
}

bool WriteBackScanner::AddTask(uint64_t inode_id) {
  std::lock_guard<std::mutex> guard(task_mutex_);
  if (task_set_.find(inode_id) != task_set_.end()) {
    return false;
  } else {
    task_set_.insert(inode_id);
    if (task_tried_times_.find(inode_id) != task_tried_times_.end()) {
      task_tried_times_[inode_id]++;
    } else {
      task_tried_times_[inode_id] = 1;
    }
    return true;
  }
}

bool WriteBackScanner::EraseTask(uint64_t inode_id, bool finished) {
  std::lock_guard<std::mutex> guard(task_mutex_);
  task_set_.erase(inode_id);
  if (finished) {
    task_tried_times_.erase(inode_id);
  }
  return true;
}

bool WriteBackScanner::TestAndResetTaskTriedTimes(
    uint64_t inode_id,
    std::function<bool(int)> test) {
  std::lock_guard<std::mutex> guard(task_mutex_);
  if (task_tried_times_.find(inode_id) != task_tried_times_.end()) {
    if (test(task_tried_times_[inode_id])) {
      task_tried_times_[inode_id] = 0;
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}

WriteBackTaskV2::WriteBackTaskV2(std::shared_ptr<NameSpace> ns,
                                 std::shared_ptr<Ufs> ufs,
                                 const INode& inode)
    : ns_(std::move(ns)), ufs_(std::move(ufs)), inode_(inode) {
  CHECK_NOTNULL(ns_);
  CHECK_NOTNULL(ufs_);
}

bool WriteBackTaskV2::Run(void* arg) {
  if (!FLAGS_enable_write_back_task_persistence) {
    // LOG(WARNING) << "Write back task persistence is disabled";
    // MFC(LoggerMetrics::Instance().warn_)->Inc();
    return true;
  }

  bool ignored;

  if (!ns_->ha_state()->IsActive() || ns_->ha_state()->InTransition()) {
    return false;
  }
  RPC_SW_CTX_INIT(rpc_sw_ctx,
                  "[PersistFile][TaskV2::Run]",
                  "inode_id=" + std::to_string(inode_.id()));
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");
  Status s = ns_->PersistUfsFile(
      ufs_,
      inode_.id(),
      inode_.ufs_file_info().key(),
      inode_.ufs_file_info().upload_id(),
      WriteBackScanner::TestAndResetTaskTriedTimes(
          inode_.id(),
          [](int tried_times) {
            return tried_times > FLAGS_write_back_num_failure_before_check_ufs;
          }),
      true,
      &ignored,
      rpc_sw_ctx.get());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "end");
  WriteBackScanner::EraseTask(inode_.id(), s.IsOK());
  return true;
}

INode WriteBackTaskV2::GetINode() {
  CHECK(FLAGS_run_ut);
  return inode_;
}

uint32_t WriteBackStat::GetCfIdx() {
  return kWriteBackTaskCFIndex;
}

bool WriteBackStat::PreScan() {
  inode_cnt_ = 0;
  return true;
}

bool WriteBackStat::Handle(MetaStorageSnapPtr snapshot,
                           const rocksdb::Slice& key,
                           const rocksdb::Slice& value) {
  (void)snapshot;
  (void)key;
  (void)value;

  inode_cnt_++;
  return true;
}

bool WriteBackStat::PostScan() {
  metrics_.task_num = inode_cnt_;
  return true;
}

int64_t WriteBackStat::GetDelayUs() {
  return FLAGS_write_back_task_v2_stat_trigger_interval_us;
}

std::string WriteBackStat::ToString() {
  return "WriteBackStat";
}

uint64_t WriteBackStat::GetTaskCnt() {
  return metrics_.task_num;
}

}  // namespace dancenn
