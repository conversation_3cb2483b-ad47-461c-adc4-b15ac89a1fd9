#include "ufs_noupload_manager.h"

#include <inode.pb.h>

#include "base/refresher.h"
#include "namespace/namespace.h"

DECLARE_int32(upload_monitor_table_slice_num);
DECLARE_uint64(upload_monitor_bg_task_interval_ms);
DECLARE_uint64(max_no_upload_inode_grace_time_ms);
DECLARE_bool(write_back_by_default);

// define
namespace dancenn {

// UfsNouploadSlice
UfsNouploadSlice::UfsNouploadSlice(UfsNouploadSlice&& other) {
  LOG(FATAL) << "UfsNouploadSlice::UfsNouploadSlice("
                "UfsNouploadSlice&&) is not implemented";
}

void UfsNouploadSlice::add(uint64_t inode_id, UfsNouploadState value) {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  noupload_inode_.insert({inode_id, value});
}

void UfsNouploadSlice::remove(uint64_t inode_id) {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  noupload_inode_.erase(inode_id);
}

size_t UfsNouploadSlice::size() const {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  return noupload_inode_.size();
}

std::unordered_map<uint64_t, UfsNouploadState> UfsNouploadSlice::copy() const {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  return noupload_inode_;
}

bool UfsNouploadSlice::has(uint64_t inode_id) const {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  return noupload_inode_.count(inode_id) > 0;
}

UfsNouploadState UfsNouploadSlice::get(uint64_t inode_id) const {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);

  auto it = noupload_inode_.find(inode_id);
  if (it != noupload_inode_.end()) {
    return it->second;
  } else {
    return {};
  };
}

void UfsNouploadSlice::erase(const std::vector<uint64_t>& id_list) {
  std::lock_guard<std::mutex> guard(noupload_inode_mutex_);
  for (const auto& id : id_list) {
    noupload_inode_.erase(id);
  }
}

// UfsNouploadManager
UfsNouploadManager::UfsNouploadManager(
    std::function<uint64_t(void)> get_txid_func)
    : get_txid_func_(get_txid_func) {
  CHECK_NOTNULL(get_txid_func_);

  slices_.resize(FLAGS_upload_monitor_table_slice_num);

  SetMetrics();

  refresher_ = std::make_unique<Refresher>("noupload-upload-refresher",
                                           [&]() { CheckInode(); });
  refresher_->set_period(
      std::chrono::milliseconds(FLAGS_upload_monitor_bg_task_interval_ms));
  refresher_->Start();
}

UfsNouploadManager::~UfsNouploadManager() {
  Stop();

  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

void UfsNouploadManager::SetMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameSpace");

  metric_noupload_inode_cnt_ = metrics->RegisterGauge(
      "Acc.Upload.NoUploadTask",
      [this]() -> double { return this->size_noupload_inode(); });
}

void UfsNouploadManager::Stop() {
  stopped_ = true;
}

void UfsNouploadManager::CheckInode() {
  if (!ns_) {
    return;
  }
  if (!ns_->IsAccMode()) {
    return;
  }

  for (auto& slice : slices_) {
    if (stopped_) {
      return;
    }

    auto inodes = slice.copy();

    // check
    std::vector<uint64_t> invalid_inodes;
    auto now_dir_txid = get_txid_func_();
    for (auto pair : inodes) {
      if (stopped_) {
        return;
      }

      const auto& inode_id = pair.first;
      const auto& v = pair.second;

      auto now = TimeUtil::GetNowEpochMs();
      // 1. timeout
      // 2. dir policy modify
      if (v.ts_ms + FLAGS_max_no_upload_inode_grace_time_ms < now ||
          v.dir_txid < now_dir_txid) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      INode inode;
      bool res = ns_->GetINode(inode_id, &inode);
      if (!res) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      // 3. inode modify
      if (v.inode_txid != inode.last_update_txid()) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      if (!inode.has_ufs_file_info() ||
          inode.ufs_file_info().file_state() !=
              UfsFileState::kUfsFileStateToBePersisted) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      if (stopped_) {
        return;
      }
    }

    // remove
    slice.erase(invalid_inodes);
  }
}

void UfsNouploadManager::add_noupload_inode(uint64_t inode_id,
                                            UfsNouploadState value) {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];
  VLOG(10) << "add_noupload_inode " << inode_id;

  slice.add(inode_id, value);
}

void UfsNouploadManager::remove_noupload_inode(uint64_t inode_id) {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];
  VLOG(10) << "remove_noupload_inode " << inode_id;

  slice.remove(inode_id);
}

size_t UfsNouploadManager::size_noupload_inode() const {
  size_t res = 0;
  for (const auto& slice : slices_) {
    res += slice.size();
  }
  return res;
}

std::unordered_map<uint64_t, UfsNouploadState> UfsNouploadManager::
    copy_noupload_inode() {
  std::unordered_map<uint64_t, UfsNouploadState> res;

  for (const auto& slice : slices_) {
    std::unordered_map<uint64_t, UfsNouploadState> local;
    local = slice.copy();

    res.insert(local.begin(), local.end());
  }
  return res;
}

bool UfsNouploadManager::has_inode(uint64_t inode_id) const {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];

  return slice.has(inode_id);
}

UfsNouploadState UfsNouploadManager::get_value(uint64_t inode_id) const {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];

  return slice.get(inode_id);
}

}  // namespace dancenn