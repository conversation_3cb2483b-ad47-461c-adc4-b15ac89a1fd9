#include "ufs_uploading_manager.h"

#include <inode.pb.h>

#include "namespace/namespace.h"

DECLARE_int32(upload_monitor_table_slice_num);
DECLARE_uint64(upload_monitor_bg_task_interval_ms);

namespace dancenn {

// UfsUploadingSlice
UfsUploadingSlice::UfsUploadingSlice(UfsUploadingSlice&& other) {
  LOG(FATAL) << "UfsUploadingSlice::UfsUploadingSlice("
                "UfsUploadingSlice&&) is not implemented";
}

bool UfsUploadingSlice::add(uint64_t inode_id) {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);

  auto res = upload_ongoing_inode_.insert(inode_id);

  return res.second;
}

bool UfsUploadingSlice::remove(uint64_t inode_id) {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);

  auto res = upload_ongoing_inode_.erase(inode_id);

  return res > 0;
}

size_t UfsUploadingSlice::size() const {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);
  return upload_ongoing_inode_.size();
}

std::unordered_set<uint64_t> UfsUploadingSlice::copy() const {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);
  return upload_ongoing_inode_;
}

bool UfsUploadingSlice::has(uint64_t inode_id) const {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);
  return upload_ongoing_inode_.count(inode_id) > 0;
}

void UfsUploadingSlice::erase(const std::vector<uint64_t>& id_list) {
  std::lock_guard<std::mutex> guard(upload_ongoing_inode_mutex_);
  for (const auto& id : id_list) {
    upload_ongoing_inode_.erase(id);
  }
}

// UfsUploadingManager
UfsUploadingManager::UfsUploadingManager() {
  slices_.resize(FLAGS_upload_monitor_table_slice_num);

  SetMetrics();

  refresher_ = std::make_unique<Refresher>("ongoing-upload-refresher",
                                           [&]() { CheckInode(); });
  refresher_->set_period(
      std::chrono::milliseconds(FLAGS_upload_monitor_bg_task_interval_ms));
  refresher_->Start();
}

UfsUploadingManager::~UfsUploadingManager() {
  Stop();

  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

void UfsUploadingManager::SetMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameSpace");

  metric_fg_tasks_ = metrics->RegisterCounter("Acc.Upload.FgTasks");
  metric_bg_tasks_ = metrics->RegisterCounter("Acc.Upload.BgTasks");

  metric_upload_ongoing_inode_cnt_ = metrics->RegisterGauge(
      "Acc.Upload.OngoingUploadTask",
      [this]() -> double { return this->size_upload_ongoing_inode(); });
}

void UfsUploadingManager::Stop() {
  stopped_ = true;
}

void UfsUploadingManager::CheckInode() {
  if (!ns_) {
    return;
  }
  if (!ns_->IsAccMode()) {
    return;
  }

  for (auto& slice : slices_) {
    if (stopped_) {
      return;
    }

    auto inodes = slice.copy();

    // check
    std::vector<uint64_t> invalid_inodes;
    for (auto inode_id : inodes) {
      if (stopped_) {
        return;
      }

      INode inode;
      bool res = ns_->GetINode(inode_id, &inode);
      if (!res) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      if (!inode.has_ufs_file_info() ||
          inode.ufs_file_info().file_state() !=
              UfsFileState::kUfsFileStateToBePersisted) {
        invalid_inodes.emplace_back(inode_id);
        continue;
      }

      if (stopped_) {
        return;
      }
    }

    // remove
    slice.erase(invalid_inodes);
  }
}

void UfsUploadingManager::add_upload_ongoing_inode(uint64_t inode_id) {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];

  if (slice.add(inode_id)) {
    VLOG(10) << "add_upload_ongoing_inode " << inode_id;
  }
}

void UfsUploadingManager::remove_upload_ongoing_inode(uint64_t inode_id) {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];

  if (slice.remove(inode_id)) {
    VLOG(10) << "remove_upload_ongoing_inode " << inode_id;
  }
}

size_t UfsUploadingManager::size_upload_ongoing_inode() const {
  size_t res = 0;
  for (const auto& slice : slices_) {
    res += slice.size();
  }
  return res;
}

std::unordered_set<uint64_t> UfsUploadingManager::copy_upload_ongoing_inode() {
  std::unordered_set<uint64_t> res;

  for (const auto& slice : slices_) {
    std::unordered_set<uint64_t> local;
    local = slice.copy();

    res.insert(local.begin(), local.end());
  }
  return res;
}

bool UfsUploadingManager::has_inode(uint64_t inode_id) const {
  auto& slice = slices_[inode_id % FLAGS_upload_monitor_table_slice_num];

  return slice.has(inode_id);
}

}  // namespace dancenn