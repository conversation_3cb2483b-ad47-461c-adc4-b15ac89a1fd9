#ifndef UFS_UPLOAD_WRITE_BACK_MANAGER_METRICS_H_
#define UFS_UPLOAD_WRITE_BACK_MANAGER_METRICS_H_

#include <memory>
#include <unordered_set>

#include "base/metric.h"
#include "base/metrics.h"

namespace dancenn {

class WriteBackManager;

struct WriteBackManagerMetrics {
  explicit WriteBackManagerMetrics(WriteBackManager* manager);
  ~WriteBackManagerMetrics() = default;

  WriteBackManagerMetrics(const WriteBackManagerMetrics&) = delete;
  WriteBackManagerMetrics& operator=(const WriteBackManagerMetrics&) = delete;

  std::shared_ptr<Gauge> tasks;
  MetricID waiting_time;
  MetricID finish_time;
  MetricID slow_task;
};

struct WriteBackManagerV2Metrics {
  WriteBackManagerV2Metrics();
  ~WriteBackManagerV2Metrics() = default;

  WriteBackManagerV2Metrics(const WriteBackManagerV2Metrics&) = delete;
  WriteBackManagerV2Metrics& operator=(const WriteBackManagerV2Metrics&) =
      delete;

  std::shared_ptr<Gauge> tasks;

  std::atomic<uint64_t> task_num;
};

}  // namespace dancenn

#endif  // UFS_UPLOAD_WRITE_BACK_MANAGER_METRICS_H_
