//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_manager.h"

// Third
#include <gflags/gflags.h>

// Project
#include "acc/task/sync_task.h"
#include "base/committer_channel_context.h"
#include "ufs/ufs_event_kafka_consumer.h"
#include "ufs/ufs_event_rmq_consumer.h"
#include "ufs/ufs_event_sync_task_factory.h"

DECLARE_bool(ufs_event_manager_enabled);
DECLARE_uint32(ufs_event_consumer_count);
DECLARE_bool(ufs_event_kafka_consumer_enabled);
DECLARE_string(ufs_event_consumer_kafka_endpoint);
DECLARE_string(ufs_event_consumer_kafka_topic);
DECLARE_string(ufs_event_consumer_kafka_group_id);
DECLARE_bool(ufs_event_rmq_consumer_enabled);
DECLARE_string(ufs_event_rmq_consumer_endpoint);
DECLARE_string(ufs_event_rmq_consumer_topic);
DECLARE_string(ufs_event_rmq_consumer_group_id);
DECLARE_string(ufs_event_rmq_consumer_access_key);
DECLARE_string(ufs_event_rmq_consumer_secret_key);
DECLARE_uint32(ufs_event_handler_count);
DECLARE_uint32(ufs_event_handler_add_task_retry_interval_ms);
DECLARE_bool(tos_event_key_prefix_blacklist_enabled);
DECLARE_string(tos_event_key_prefix_blacklist);

namespace dancenn {

UfsEventManager::UfsEventManager(const std::shared_ptr<Ufs>& ufs)
    : ufs_(ufs), metrics_(this) {
  ufs_event_manager_enabled_ = FLAGS_ufs_event_manager_enabled;
  if (!ufs_event_manager_enabled_) {
    LOG(INFO) << "UfsEventManager not enabled.";
    return;
  }

  if (FLAGS_ufs_event_kafka_consumer_enabled) {
    InitKafkaConsumers();
  }
  if (FLAGS_ufs_event_rmq_consumer_enabled) {
    InitRmqConsumers();
  }

  handler_pool_ = std::make_unique<ThreadPool>("UEH");
  handler_pool_->set_num_threads(
      static_cast<size_t>(FLAGS_ufs_event_handler_count));
  handler_pool_->set_max_num_pending_tasks(
      static_cast<size_t>(FLAGS_ufs_event_handler_count));
  handler_pool_->set_user_context(reinterpret_cast<void*>(
      new CommitterChannelContext("UEH", FLAGS_ufs_event_handler_count)));

  if (FLAGS_tos_event_key_prefix_blacklist_enabled &&
      !FLAGS_tos_event_key_prefix_blacklist.empty()) {
    tos_event_key_prefix_blacklist_ =
        StringUtils::SplitByChars(FLAGS_tos_event_key_prefix_blacklist, ",");
  }
}

void UfsEventManager::Start() {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  LOG(INFO) << "Will start UfsEventManager";
  LOG(INFO) << "Will start UfsEventManager handler pool";
  handler_pool_->Start();
  if (FLAGS_ufs_event_kafka_consumer_enabled) {
    LOG(INFO) << "Will start UfsEventManager Kafka consumer pool";
    kafka_consumer_pool_->Start();
  }
  if (FLAGS_ufs_event_rmq_consumer_enabled) {
    LOG(INFO) << "Will start UfsEventManager RocketMQ consumer pool";
    rmq_consumer_pool_->Start();
  }
  LOG(INFO) << "UfsEventManager started";
}

void UfsEventManager::Stop() {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  SetHaState(/*is_active=*/false);

  LOG(INFO) << "Will stop UfsEventManager";
  if (FLAGS_ufs_event_rmq_consumer_enabled) {
    LOG(INFO) << "Will stop UfsEventManager RocketMQ consumer pool";
    rmq_consumer_pool_->Stop();
  }
  if (FLAGS_ufs_event_kafka_consumer_enabled) {
    LOG(INFO) << "Will stop UfsEventManager Kafka consumer pool";
    kafka_consumer_pool_->Stop();
  }
  LOG(INFO) << "Will stop UfsEventManager handler pool";
  handler_pool_->Stop();
  LOG(INFO) << "UfsEventManager stopped";
}

void UfsEventManager::SetNS(const std::shared_ptr<NameSpace>& ns) {
  ns_ = ns;
}

void UfsEventManager::SetHaState(bool is_active) {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  if (is_active) {
    StartConsumers();
  } else {
    StopConsumers();
  }
}

void UfsEventManager::StartConsumers() {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  if (FLAGS_ufs_event_kafka_consumer_enabled) {
    StartKafkaConsumers();
  }
  if (FLAGS_ufs_event_rmq_consumer_enabled) {
    StartRmqConsumers();
  }
}

void UfsEventManager::StopConsumers() {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  LOG(INFO) << "Will stop all UFS event consumers";
  if (FLAGS_ufs_event_kafka_consumer_enabled) {
    StopKafkaConsumers();
  }
  if (FLAGS_ufs_event_rmq_consumer_enabled) {
    StopRmqConsumers();
  }
}

bool UfsEventManager::IsKafkaConsumerRunning() const {
  return kafka_consumer_running_;
}

bool UfsEventManager::IsRmqConsumerRunning() const {
  return rmq_consumer_running_;
}

std::mutex& UfsEventManager::GetRmqConsumerMtx() {
  return rmq_consumer_mtx_;
}

std::condition_variable& UfsEventManager::GetRmqConsumerCv() {
  return rmq_consumer_cv_;
}

void UfsEventManager::AddSyncTask(const std::string& msg,
                                  const SyncCallback& cb) {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  std::shared_ptr<SyncTask> task = UfsEventSyncTaskFactory::CreateTask(
      ufs_->ufs_config_ptr(), ufs_, ns_, cb, this, msg);
  while (!handler_pool_->AddTask(task)) {
    VLOG(10) << "Failed to submit task to handler pool due to overload"
             << ", running tasks: " << handler_pool_->NumRunningTasks()
             << ", pending cnt: " << handler_pool_->PendingCount();
    std::this_thread::sleep_for(std::chrono::milliseconds(
        FLAGS_ufs_event_handler_add_task_retry_interval_ms));
  }
}

const std::vector<std::string>& UfsEventManager::GetTosEventKeyPrefixBlacklist()
    const {
  return tos_event_key_prefix_blacklist_;
}

UfsEventManagerStatus UfsEventManager::GetStatus() const {
  bool ufs_event_manager_enabled = ufs_event_manager_enabled_;
  bool kafka_consumer_running = IsKafkaConsumerRunning();
  bool rmq_consumer_running = IsRmqConsumerRunning();
  return UfsEventManagerStatus{
      ufs_event_manager_enabled /* enabled */,
      kafka_consumer_running /* kafka_consumer_running */,
      kafka_consumer_running ? kafka_consumer_pool_->NumRunningTasks()
                             : 0 /* kafka_consumer_num */,
      rmq_consumer_running /* rmq_consumer_running */,
      rmq_consumer_running ? rmq_consumer_pool_->NumRunningTasks()
                           : 0 /* rmq_consumer_num */,
      ufs_event_manager_enabled ? handler_pool_->NumRunningTasks()
                                : 0 /* handler_num */
  };
}

const UfsEventManagerMetrics& UfsEventManager::metrics() const {
  return metrics_;
}

void UfsEventManager::ClearError() {
  if (!ufs_event_manager_enabled_) {
    return;
  }

  metrics_.consume_error->Set(0);
  metrics_.handle_error->Set(0);
}

void UfsEventManager::InitKafkaConsumers() {
  CHECK(!FLAGS_ufs_event_consumer_kafka_endpoint.empty());
  CHECK(!FLAGS_ufs_event_consumer_kafka_topic.empty());
  CHECK(!FLAGS_ufs_event_consumer_kafka_group_id.empty());

  InitKafkaConf();

  kafka_topics_.emplace_back(FLAGS_ufs_event_consumer_kafka_topic);

  kafka_consumer_pool_ = std::make_unique<ThreadPool>("UEKC");
  kafka_consumer_pool_->set_num_threads(FLAGS_ufs_event_consumer_count);
}

void UfsEventManager::InitRmqConsumers() {
  CHECK(!FLAGS_ufs_event_rmq_consumer_endpoint.empty());
  CHECK(!FLAGS_ufs_event_rmq_consumer_topic.empty());
  CHECK(!FLAGS_ufs_event_rmq_consumer_group_id.empty());
  CHECK(!FLAGS_ufs_event_rmq_consumer_access_key.empty());
  CHECK(!FLAGS_ufs_event_rmq_consumer_secret_key.empty());

  rmq_endpoint_ = FLAGS_ufs_event_rmq_consumer_endpoint;
  rmq_topic_ = FLAGS_ufs_event_rmq_consumer_topic;
  rmq_group_id_ = FLAGS_ufs_event_rmq_consumer_group_id;
  rmq_access_key_ = FLAGS_ufs_event_rmq_consumer_access_key;
  rmq_secret_key_ = FLAGS_ufs_event_rmq_consumer_secret_key;

  rmq_consumer_pool_ = std::make_unique<ThreadPool>("UERC");
  rmq_consumer_pool_->set_num_threads(FLAGS_ufs_event_consumer_count);
}

/*
 * Initialize Kafka configurations
 * Doc: https://github.com/confluentinc/librdkafka/blob/master/CONFIGURATION.md
 */
void UfsEventManager::InitKafkaConf() {
  kafka_conf_.reset(
      RdKafka::Conf::create(RdKafka::Conf::ConfType::CONF_GLOBAL));
  std::string err_msg;
  /* Kafka server endpoint */
  SET_KAFKA_CONF(kafka_conf_,
                 "bootstrap.servers",
                 FLAGS_ufs_event_consumer_kafka_endpoint,
                 err_msg);
  /*
   * Consumer group ID. Messages are load-balanced over the consumers in one
   * group, and broadcasted to all groups.
   * For CFS: Each TOS Namespace is one consumer group; ACTIVE and STANDBY NNs
   * are consumers in a consumer group.
   */
  SET_KAFKA_CONF(kafka_conf_,
                 "group.id",
                 FLAGS_ufs_event_consumer_kafka_group_id,
                 err_msg);
  /*
   * - earliest: all events from the beginning of the topic partitions will be
   *             consumed.
   * - latest:   only new messages written to the topic partitions will be
   *             consumed.
   * For CFS: Once a consumer group is created (a TOS Namespace is created), we
   * only need to consume new messages.
   */
  SET_KAFKA_CONF(kafka_conf_, "auto.offset.reset", "latest", err_msg);
  /*
   * For CFS: Disable auto commit; manually commit after handling the messages
   */
  SET_KAFKA_CONF(kafka_conf_, "enable.auto.commit", "false", err_msg);
}

void UfsEventManager::StartKafkaConsumers() {
  kafka_consumer_running_ = true;
  int cnt =
      kafka_consumer_pool_->size() - kafka_consumer_pool_->NumRunningTasks();
  LOG(INFO) << "Will try to start " << cnt << " UfsEventKafkaConsumer(s)";
  int success = 0;
  for (int i = 0; i < cnt; ++i) {
    if (kafka_consumer_pool_->AddTask(std::make_shared<UfsEventKafkaConsumer>(
            this, kafka_conf_, kafka_topics_))) {
      ++success;
    }
  }
  LOG(INFO) << "Successfully started " << success
            << " UfsEventKafkaConsumer(s)";
}

void UfsEventManager::StartRmqConsumers() {
  rmq_consumer_running_ = true;
  int cnt = rmq_consumer_pool_->size() - rmq_consumer_pool_->NumRunningTasks();
  LOG(INFO) << "Will try to start " << cnt << " UfsEventRmqConsumer(s)";
  int success = 0;
  for (int i = 0; i < cnt; ++i) {
    if (rmq_consumer_pool_->AddTask(
            std::make_shared<UfsEventRmqConsumer>(this,
                                                  rmq_endpoint_,
                                                  rmq_topic_,
                                                  rmq_group_id_,
                                                  rmq_access_key_,
                                                  rmq_secret_key_))) {
      ++success;
    }
  }
  LOG(INFO) << "Successfully started " << success << " UfsEventRmqConsumer(s)";
}

void UfsEventManager::StopKafkaConsumers() {
  kafka_consumer_running_ = false;
  LOG(INFO) << "Will stop UFS event Kafka consumers";
}

void UfsEventManager::StopRmqConsumers() {
  rmq_consumer_running_ = false;
  rmq_consumer_cv_.notify_all();
  LOG(INFO) << "Will stop UFS event RocketMQ consumers";
}

}  // namespace dancenn
