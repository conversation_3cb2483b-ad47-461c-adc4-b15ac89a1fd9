#pragma once

#include <DatanodeProtocol.pb.h>
#include <glog/logging.h>
#include <proto/generated/dancenn/persistent_flags.pb.h>

#include <memory>

#include "ufs/persistent_ufs_info.h"
#include "ufs/ufs.h"
#include "ufs/ufs_config.h"
#include "ufs/ufs_event_manager.h"
#include "ufs/upload/ufs_upload_monitor.h"

namespace dancenn {

using cloudfs::datanode::HeartbeatResponseProto;

class PersistentUfsInfo;
class NameSpace;
class Ufs;
class UfsEventManager;
class UfsUploadMonitor;

// This class is responsible for managing common progress related to UFS
// The difference with UFS is that UFS is only associated with ONE backend,
// while UfsEnv will manage MULTI-tenant backends (in the future).
class UfsEnv {
 public:
  static std::shared_ptr<UfsEnv> Create();

  static bool NeedInitPersistentUfsInfo();

 public:
  UfsEnv();
  virtual ~UfsEnv();

  virtual Status Start();
  virtual void Stop() = 0;

  // TODO(xiong): Add Multi Ufs
  void AddUfs(std::shared_ptr<Ufs> ufs);
  // no ownership
  void SetNS(std::shared_ptr<NameSpace> ns);
  void SetHaState(bool is_active);

  virtual UfsConfig CreateUfsConfig() {
    CHECK(false) << "Not Implement";
    return {};
  }

  std::shared_ptr<Ufs> get_only_ufs() {
    return ufs_;
  }
  UfsUploadMonitor* upload_monitor() {
    return upload_monitor_.get();
  }
  std::shared_ptr<UfsEventManager> event_manager() {
    return ufs_event_manager_;
  }

  virtual std::shared_ptr<Ufs> CreateUfs(const UfsConfig& c) = 0;

  // coredump if error
  virtual void CheckPersistentUfsInfo(
      PersistentUfsInfo* stored_ufs_info) const = 0;

  virtual bool GetUfsInfo(cloudfs::RemoteBlockInfoProto* info,
                          bool for_internal_use) const = 0;

  virtual void DumpUfsInfoToFlags(PersistentFlags* persistent_flags) const = 0;

  // Function
  virtual Status GetBlockLengthFromUfs(const std::string& ufs_filename,
                                       int64_t* new_length) const = 0;

  virtual Status DeleteBlocks(
      const cloudfs::datanode::HeartbeatResponseProto& heartbeat_resp) = 0;

 protected:
  std::shared_ptr<NameSpace> ns_{nullptr};

  std::unique_ptr<UfsUploadMonitor> upload_monitor_{nullptr};

  std::shared_ptr<UfsEventManager> ufs_event_manager_{nullptr};

  // TODO: multi ufs support
  std::shared_ptr<Ufs> ufs_{nullptr};
};

}  // namespace dancenn
