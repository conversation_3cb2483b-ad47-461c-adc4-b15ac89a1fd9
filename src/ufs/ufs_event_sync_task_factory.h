//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include "acc/task/sync_task.h"

namespace dancenn {

class UfsEventManager;

class UfsEventSyncTaskFactory {
 public:
  static std::shared_ptr<SyncTask> CreateTask(
      const UfsConfig* config,
      const std::shared_ptr<Ufs>& ufs,
      const std::shared_ptr<NameSpace>& ns,
      const SyncCallback& cb,
      UfsEventManager* mgr,
      const std::string& msg);
};

}  // namespace dancenn
