//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cstdint>
#include <memory>
#include <string>

#include "base/status.h"

namespace dancenn {

class UfsFileStatusBuilder;

enum UfsFileType { UFS_FILE = 0, UFS_DIR };

class UfsFileStatus {
 public:
  UfsFileStatus() = default;
  UfsFileStatus(const UfsFileStatus&) = default;
  UfsFileStatus(UfsFileStatus&&) = default;
  UfsFileStatus& operator=(const UfsFileStatus&) = default;
  UfsFileStatus& operator=(UfsFileStatus&&) = default;

  const UfsFileType FileType() const {
    return filetype_;
  }
  const std::string& FullPath() const {
    return full_path_;
  }
  const std::string& FileName() const {
    return file_name_;
  };
  uint64_t BlockSize() const {
    return block_size_;
  }
  uint64_t FileSize() const {
    return filesize_;
  }
  const std::string& Etag() const {
    return etag_;
  }
  int64_t created_ts() const {
    return created_ts_;
  }
  int64_t modified_ts() const {
    return modified_ts_;
  }

  static UfsFileStatusBuilder Builder();

  bool IsDir() const {
    return filetype_ == UFS_DIR;
  }

 private:
  UfsFileType filetype_ = UFS_FILE;
  std::string full_path_;
  std::string file_name_;
  uint64_t block_size_ = 0;
  uint64_t filesize_ = 0;
  std::string etag_;
  int64_t created_ts_ = 0; // epoch ms
  int64_t modified_ts_ = 0; // epoch ms

  friend class UfsFileStatusBuilder;
};

class UfsFileStatusBuilder {
 public:
  UfsFileStatusBuilder() = default;
  UfsFileStatusBuilder(const UfsFileStatusBuilder&) = default;
  UfsFileStatusBuilder(UfsFileStatusBuilder&&) = default;

  UfsFileStatusBuilder& SetFileType(UfsFileType type) {
    info_.filetype_ = type;
    return *this;
  }
  UfsFileStatusBuilder& SetFullPath(std::string full_path) {
    info_.full_path_ = std::move(full_path);
    return *this;
  }
  UfsFileStatusBuilder& SetBlockSize(uint64_t block_size) {
    info_.block_size_ = block_size;
    return *this;
  }
  UfsFileStatusBuilder& SetFileSize(uint64_t file_size) {
    info_.filesize_ = file_size;
    return *this;
  }
  UfsFileStatusBuilder& SetEtag(std::string etag) {
    info_.etag_ = std::move(etag);
    return *this;
  }
  UfsFileStatusBuilder& SetCreatedTs(int64_t ts) {
    info_.created_ts_ = ts;
    return *this;
  }
  UfsFileStatusBuilder& SetModifiedTs(int64_t ts) {
    info_.modified_ts_ = ts;
    return *this;
  }

  Status Build(UfsFileStatus* out_s);

 private:
  std::string ParseFileName(const std::string& path);

 private:
  UfsFileStatus info_;
};

enum UfsOpResult {
  UFS_OK = 0,
  UFS_NOT_FOUND,
};

}  // namespace dancenn
