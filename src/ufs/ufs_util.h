//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cstdint>
#include <string>

#include "ClientNamenodeProtocol.pb.h"
#include "base/status.h"
#include "base/time_util.h"
#include "inode.pb.h"  // NOLINT
#include "namespace/create_flag.h"

namespace dancenn {

class UfsUtil {
 public:
  static bool HasInvalidSeg(const std::string& path);

  static void FillUfsFileInfoForNewFile(UfsFileInfoProto& info,
                                        const uint32_t createflag,
                                        const std::string& ufs_key);

  static void FillIncompleteUfsDirInode(INode* inode);

  static void FillSyncedUfsDirInode(INode* inode);

  static std::string UfsKeyToPath(const std::string& key);

  static bool IsValidUfsPath(const std::string& path);

  static bool IsListingChildrenValidUfsPath(const std::string& prefix,
                                            const std::string& key);

  static Status ConvertInnerPath(const std::string& prefix,
                                 const std::string& path,
                                 std::string* inner_path);

  static Status ConvertUfsPathToObject(const std::string& ufs_path,
                                       std::string* object_name);
};

}  // namespace dancenn