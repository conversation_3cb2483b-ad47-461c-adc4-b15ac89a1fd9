//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_rmq_consumer.h"

// Third
#include <glog/logging.h>

// Project
#include "ufs/ufs_event_manager.h"

DECLARE_int64(namespace_id);

namespace dancenn {

UfsEventRmqMessageListener::UfsEventRmqMessageListener(UfsEventManager* mgr)
    : mgr_(mgr) {
}

rocketmq::ConsumeStatus UfsEventRmqMessageListener::consumeMessage(
    const std::vector<rocketmq::MQMessageExt>& msgs) {
  size_t msg_cnt = msgs.size();
  VLOG(8) << "Consumed " << msg_cnt << " messages, start to handle them";
  mgr_->metrics().msg_consumed->Inc(msg_cnt);

  // Prepare CountDownLatch and SyncCallback
  CountDownLatch latch(msg_cnt);
  std::atomic_bool success{true};
  SyncCallback cb([&success, &latch](const Status& s) {
    if (!s.IsOK()) {
      success = false;
    }
    latch.CountDown();
  });

  // Add UfsEventSyncTask
  for (const rocketmq::MQMessageExt& msg : msgs) {
    const std::string& msg_body = msg.getBody();
    mgr_->AddSyncTask(msg_body, cb);
  }

  latch.Await();
  if (success) {
    return rocketmq::CONSUME_SUCCESS;
  } else {
    mgr_->StopConsumers();
    return rocketmq::RECONSUME_LATER;
  }
}

UfsEventRmqConsumer::UfsEventRmqConsumer(UfsEventManager* mgr,
                                         const std::string& endpoint,
                                         const std::string& topic,
                                         const std::string& group_id,
                                         const std::string& access_key,
                                         const std::string& secret_key)
    : mgr_(mgr) {
  CHECK_NOTNULL(mgr_);

  rmq_consumer_ = std::make_unique<rocketmq::DefaultMQPushConsumer>(group_id);
  rmq_consumer_->setNamesrvAddr(endpoint);
  rmq_consumer_->setSessionCredentials(access_key, secret_key, "VOLC");
  rmq_consumer_->setConsumeFromWhere(rocketmq::CONSUME_FROM_FIRST_OFFSET);
  rmq_consumer_->subscribe(topic, "*");
  rmq_listener_ = std::make_unique<UfsEventRmqMessageListener>(mgr_);
  rmq_consumer_->registerMessageListener(rmq_listener_.get());
}

bool UfsEventRmqConsumer::operator()(void* arg) {
  std::ostringstream oss;
  oss << FLAGS_namespace_id << "-" << std::this_thread::get_id();
  std::string instance_name = oss.str();
  LOG(INFO) << "UfsEventRmqConsumer " << instance_name << " starts to run";
  rmq_consumer_->setInstanceName(instance_name);
  rmq_consumer_->start();

  while (mgr_->IsRmqConsumerRunning()) {
    std::unique_lock<std::mutex> lock(mgr_->GetRmqConsumerMtx());
    mgr_->GetRmqConsumerCv().wait(lock);
  }

  LOG(INFO) << "UfsEventRmqConsumer is stopping";
  rmq_consumer_->shutdown();
  LOG(INFO) << "UfsEventRmqConsumer stopped";
  return true;
}

}  // namespace dancenn
