//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include "base/kafka_util.h"
#include "base/threading.h"

namespace dancenn {

class UfsEventManager;

class UfsEventKafkaConsumer : public ThreadPoolTask {
 public:
  UfsEventKafkaConsumer(UfsEventManager* mgr,
                        const std::shared_ptr<RdKafka::Conf>& kafka_conf,
                        const std::vector<std::string>& topics);
  ~UfsEventKafkaConsumer() = default;

  bool operator()(void* /*arg*/ = nullptr) override;

 private:
  UfsEventManager* mgr_{nullptr};

  std::unique_ptr<RdKafka::KafkaConsumer> kafka_consumer_{nullptr};
};

}  // namespace dancenn
