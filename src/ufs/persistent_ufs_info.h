// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/04/26
// Description

#ifndef SRC_NAMESPACE_PERSISTENT_UFS_INFO_H_
#define SRC_NAMESPACE_PERSISTENT_UFS_INFO_H_

#include <cnetpp/base/csonpp.h>

#include <atomic>
#include <cstdint>
#include <iostream>
#include <memory>
#include <mutex>
#include <string>
#include <tuple>

#include "base/status.h"
#include "hdfs.pb.h"  // NOLINT(build/include)
#include "iam/assume_role_client.h"
#include "iam/assume_role_metrics.h"
#include "iam/model/policy_document.h"
#include "ufs/hdfs/hdfs_client.h"
#include "ufs/tos_ufs/tos_info.h"

namespace dancenn {

class TosInfo;

enum class PersistentUfsProtocol {
  kTos = 1,
  kHdfs = 2,
};

std::ostream& operator<<(std::ostream& os,
                         const PersistentUfsProtocol protocol);

// db stored class
struct PersistentUfsInfo {
 public:
  cnetpp::base::Value SerializeToJson() const;
  bool DeserializeFromJson(const cnetpp::base::Value& jsonValue);

  void Init4FirstTimeStart(cloudfs::NamespaceType type);

 public:
  PersistentUfsProtocol protocol;
  TosInfo tos_info;
  HdfsConfig hdfs_info;
};

}  // namespace dancenn

#endif  // SRC_NAMESPACE_PERSISTENT_UFS_INFO_H_
