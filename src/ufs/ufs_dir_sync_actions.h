//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <string>
#include <vector>

#include "cnetpp/base/string_piece.h"
#include "ufs/ufs_file_status.h"

namespace dancenn {

using StringPiece = cnetpp::base::StringPiece;

struct UfsDirSyncActions {
  std::vector<UfsFileStatus> files_to_add{};
  std::vector<INode> files_to_update_time{};
  std::vector<INode> files_to_check{};

  std::string ToString() const {
    std::stringstream ss;
    ss << "file_to_add: ";
    for (const auto& f : files_to_add) {
      ss << "[path: " << f.FullPath() << ", name: " << f.FileName()
         << ", dir: " << f.IsDir() << "], ";
    }
    ss << "files_to_update_time: ";
    for (const auto& f : files_to_update_time) {
      ss << "[name: " << f.name()
         << ", dir: " << (f.type() == INode_Type_kDirectory) << "], ";
    }
    ss << "files_to_check: ";
    for (const auto& f : files_to_check) {
      ss << "[name: " << f.name()
         << ", dir: " << (f.type() == INode_Type_kDirectory) << "], ";
    }
    return ss.str();
  }

  bool CheckValid() {
    // Check if file name is unique
    std::set<StringPiece> s;
    for (auto&& f : files_to_add) {
      StringPiece file_name(f.FileName());
      if (s.find(file_name) != s.end()) {
        return false;
      }
      s.emplace(file_name);
    }
    for (auto&& f : files_to_update_time) {
      StringPiece file_name(f.name());
      if (s.find(file_name) != s.end()) {
        return false;
      }
      s.emplace(file_name);
    }
    for (auto&& f : files_to_check) {
      StringPiece file_name(f.name());
      if (s.find(file_name) != s.end()) {
        return false;
      }
      s.emplace(file_name);
    }
    return true;
  }
};

}  // namespace dancenn