//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_kafka_consumer.h"

// Third
#include <gflags/gflags.h>

// Project
#include "base/count_down_latch.h"
#include "ufs/ufs_event_manager.h"

DECLARE_uint32(ufs_event_consumer_kafka_consume_timeout_ms);
DECLARE_uint32(ufs_event_consumer_max_batch_size);
DECLARE_uint32(ufs_event_consumer_max_batch_interval_ms);

namespace dancenn {

UfsEventKafkaConsumer::UfsEventKafkaConsumer(
    UfsEventManager* mgr,
    const std::shared_ptr<RdKafka::Conf>& kafka_conf,
    const std::vector<std::string>& topics)
    : mgr_(mgr) {
  CHECK_NOTNULL(mgr_);
  CHECK_NOTNULL(kafka_conf);
  CHECK(!topics.empty());

  std::string err_msg;
  kafka_consumer_.reset(
      RdKafka::KafkaConsumer::create(kafka_conf.get(), err_msg));
  CHECK(kafka_consumer_ != nullptr)
      << "Failed to construct kafka consumer. error: " << err_msg;

  RdKafka::ErrorCode err = kafka_consumer_->subscribe(topics);
  CHECK(err == RdKafka::ERR_NO_ERROR)
      << "Failed to subscribe topics. error: " << RdKafka::err2str(err);
}

bool UfsEventKafkaConsumer::operator()(void* arg) {
  LOG(INFO) << "UfsEventKafkaConsumer starts to run";

  std::vector<std::string> msgs;
  KafkaOffsetMap offset_map;
  uint64_t batch_start_ts = TimeUtil::GetNowEpochMs();
  while (mgr_->IsKafkaConsumerRunning()) {
    std::shared_ptr<RdKafka::Message> msg(kafka_consumer_->consume(
        FLAGS_ufs_event_consumer_kafka_consume_timeout_ms));

    switch (msg->err()) {
      case RdKafka::ERR_NO_ERROR: {  // Consume success
        char* payload = (char*)msg->payload();
        if (payload == nullptr) {
          VLOG(8) << "Empty message payload, " << LOG_KMSG_DETAIL(msg);
          break;
        }

        msgs.emplace_back(payload);
        offset_map[msg->topic_name()][msg->partition()] = msg->offset();
        break;
      }
      case RdKafka::ERR__TIMED_OUT:  // No message, just skip
        break;
      default:  // Consume error
        VLOG(8) << "Failed to consume message, " << LOG_KMSG_DETAIL(msg);
        mgr_->metrics().consume_error->Inc();
    }

    if (!msgs.empty()) {
      bool batch_exceed_size =
          msgs.size() >= FLAGS_ufs_event_consumer_max_batch_size;

      // The interval of a batch should not exceed the max batch interval even
      // if the next consumption timeout.
      bool batch_exceed_interval =
          TimeUtil::GetNowEpochMs() - batch_start_ts +
              FLAGS_ufs_event_consumer_kafka_consume_timeout_ms >
          FLAGS_ufs_event_consumer_max_batch_interval_ms;

      if (batch_exceed_size || batch_exceed_interval) {
        // Consumed a batch of msgs, start to handle them
        size_t msg_cnt = msgs.size();
        VLOG(8) << "Consumed " << msg_cnt << " messages, start to handle them";
        mgr_->metrics().msg_consumed->Inc(msg_cnt);

        // Prepare CountDownLatch and SyncCallback
        CountDownLatch latch(msg_cnt);
        std::atomic_bool success{true};
        SyncCallback cb([&success, &latch](const Status& s) {
          if (!s.IsOK()) {
            success = false;
          }
          latch.CountDown();
        });

        // Add UfsEventSyncTask
        for (const std::string& msg : msgs) {
          mgr_->AddSyncTask(msg, cb);
        }

        latch.Await();
        if (success) {
          // Commit offsets to Kafka
          std::vector<RdKafka::TopicPartition*> offsets =
              KafkaUtil::OffsetMapToOffsets(offset_map);
          std::ostringstream os_log_offsets;
          for (auto&& offset : offsets) {
            os_log_offsets << ", (" << LOG_KOFFSET(offset) << ")";
          }
          RdKafka::ErrorCode errcode = kafka_consumer_->commitSync(offsets);
          switch (errcode) {
            case RdKafka::ERR_NO_ERROR:  // Commit successfully
              VLOG(8) << "Offsets committed, " << os_log_offsets.str();
              break;
            default:
              VLOG(8) << "Failed to commit offsets, " << os_log_offsets.str()
                      << ", errcode: " << RdKafka::err2str(errcode);
          }
          RdKafka::TopicPartition::destroy(offsets);
        } else {
          mgr_->StopConsumers();
        }

        // Clear this batch
        msgs.clear();
        offset_map.clear();
        batch_start_ts = TimeUtil::GetNowEpochMs();
      }
    } else {
      // No event to handle
      batch_start_ts = TimeUtil::GetNowEpochMs();
    }
  }

  LOG(INFO) << "UfsEventKafkaConsumer will stop";
  kafka_consumer_->close();
  LOG(INFO) << "UfsEventKafkaConsumer stopped";
  return true;
}

}  // namespace dancenn
