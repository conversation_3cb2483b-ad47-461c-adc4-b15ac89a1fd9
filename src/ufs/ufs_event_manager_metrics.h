//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <unordered_map>

// Project
#include "base/metric.h"
#include "base/metrics.h"
#include "ufs/tos_ufs/tos_event.h"

namespace dancenn {

class UfsEventManager;

struct UfsEventManagerMetrics {
  UfsEventManagerMetrics(UfsEventManager* mgr);
  ~UfsEventManagerMetrics() = default;
  UfsEventManagerMetrics(const UfsEventManagerMetrics&) = delete;
  UfsEventManagerMetrics& operator=(const UfsEventManagerMetrics&) = delete;

  std::shared_ptr<Gauge> kafka_consumer_running;  // Gauge
  std::shared_ptr<Gauge> kafka_consumer_pending;  // Gauge
  std::shared_ptr<Gauge> rmq_consumer_running;    // Gauge
  std::shared_ptr<Gauge> rmq_consumer_pending;    // Gauge
  std::shared_ptr<Gauge> handler_task_running;    // Gauge
  std::shared_ptr<Gauge> handler_task_pending;    // Gauge

  std::shared_ptr<AtomicCounter> msg_consumed;    // AtomicCounter

  MetricID task_pending_time_for_handler;                   // Histogram

  std::shared_ptr<AtomicCounter> event_handling;  // AtomicCounter
  std::shared_ptr<AtomicCounter> event_handled;   // AtomicCounter
  std::shared_ptr<AtomicCounter> event_invalid;   // AtomicCounter
  std::shared_ptr<AtomicCounter> op_sync;         // AtomicCounter
  MetricID event_handle_time;                     // Histogram

  std::shared_ptr<AtomicCounter> consume_error;  // AtomicCounter
  std::shared_ptr<AtomicCounter> handle_error;  // AtomicCounter

  // TOS-related metrics
  std::unordered_map<TosEvent::EventType, std::shared_ptr<AtomicCounter>>
      tos_event_handled_map;
};

}  // namespace dancenn
