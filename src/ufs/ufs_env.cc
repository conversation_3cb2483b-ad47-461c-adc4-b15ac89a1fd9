#include "ufs_env.h"

#include <hdfs.pb.h>

#include "namespace/namespace.h"  // for IsAccMode
#include "ufs/hdfs_ufs/hdfs_ufs_env.h"
#include "ufs/tos_ufs/tos_ufs_env.h"

DECLARE_int32(namespace_type);

namespace dancenn {

std::shared_ptr<UfsEnv> UfsEnv::Create() {
  switch (FLAGS_namespace_type) {
    case cloudfs::NamespaceType::TOS_LOCAL:
      // 文件切分成 Block，Block 数据优先保存在本地，超出容量溢出到 TOS
    case cloudfs::NamespaceType::TOS_MANAGED:
      // 文件切分成 Block，Block 数据持久化到 TOS，写入成功后异步刷到 TOS
    case cloudfs::NamespaceType::ACC_TOS:
      // 对应 TOS 缓存加速模式
      return std::shared_ptr<UfsEnv>(new TosUfsEnv());
    case cloudfs::NamespaceType::ACC_HDFS:
      return std::make_shared<HdfsUfsEnv>();
    case cloudfs::NamespaceType::LOCAL:
      // 文件保存在本地磁盘
      return nullptr;
    default:
      LOG(FATAL) << "Unknown namespace_type" << FLAGS_namespace_type;
  }
  return nullptr;
}

bool UfsEnv::NeedInitPersistentUfsInfo() {
  switch (FLAGS_namespace_type) {
    case cloudfs::NamespaceType::TOS_LOCAL:
    case cloudfs::NamespaceType::TOS_MANAGED:
    case cloudfs::NamespaceType::ACC_TOS:
    case cloudfs::NamespaceType::ACC_HDFS:
      return true;
    case cloudfs::NamespaceType::LOCAL:
      return false;
    default:
      LOG(FATAL) << "Unknown namespace_type" << FLAGS_namespace_type;
  }
  return false;
}

UfsEnv::UfsEnv() {
  if (NameSpace::IsAccMode()) {
    upload_monitor_ = std::make_unique<UfsUploadMonitor>();
  }
}

UfsEnv::~UfsEnv() {
}

Status UfsEnv::Start() {
  LOG(INFO) << "UfsEnv::Start()";

  if (NameSpace::IsAccMode()) {
    // no except
    if (ufs_event_manager_) {
      ufs_event_manager_->Start();
    }

    return upload_monitor_->Start();
  } else {
    return Status();
  }
}

void UfsEnv::Stop() {
  LOG(INFO) << "UfsEnv::Stop()";

  if (NameSpace::IsAccMode()) {
    if (ufs_event_manager_) {
      ufs_event_manager_->Stop();
    }

    upload_monitor_->Stop();
  }
}

// TODO(xiong): Add Multi Ufs
void UfsEnv::AddUfs(std::shared_ptr<Ufs> ufs) {
  LOG(INFO) << "AddUfs " << ufs->Name();

  ufs_ = std::move(ufs);

  if (upload_monitor_) {
    upload_monitor_->AddUfs(ufs_);
  }

  // TODO(xiongmu): multi ufs support
  if (NameSpace::IsAccMode()) {
    ufs_event_manager_ = std::make_unique<UfsEventManager>(ufs_);
  }
}

void UfsEnv::SetNS(std::shared_ptr<NameSpace> ns) {
  ns_ = std::move(ns);

  if (ufs_event_manager_) {
    ufs_event_manager_->SetNS(ns_);
  }

  if (upload_monitor_) {
    upload_monitor_->SetNS(ns_);
  }
}

void UfsEnv::SetHaState(bool is_active) {
  if (ufs_event_manager_) {
    ufs_event_manager_->SetHaState(is_active);
  }

  if (upload_monitor_) {
    upload_monitor_->SetHaState(is_active);
  }
}

}  // namespace dancenn