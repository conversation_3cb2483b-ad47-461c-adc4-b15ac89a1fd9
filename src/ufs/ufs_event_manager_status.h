//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Third
#include <absl/strings/str_format.h>

// Project
#include <base/string_utils.h>

namespace dancenn {

struct UfsEventManagerStatus {
  bool enabled;
  bool kafka_consumer_running;
  size_t kafka_consumer_num;
  bool rmq_consumer_running;
  size_t rmq_consumer_num;
  size_t handler_num;

  inline std::string ToJson() const {
    return absl::StrFormat(
        "{\"enabled\": %s, "
        "\"kafka_consumer_running\": %s, "
        "\"kafka_consumer_num\": %d, "
        "\"rmq_consumer_running\": %s, "
        "\"rmq_consumer_num\": %d, "
        "\"handler_num\": %d}",
        StringUtils::BoolToString(enabled),
        StringUtils::BoolToString(kafka_consumer_running),
        kafka_consumer_num,
        StringUtils::BoolToString(rmq_consumer_running),
        rmq_consumer_num,
        handler_num);
  }
};

}  // namespace dancenn
