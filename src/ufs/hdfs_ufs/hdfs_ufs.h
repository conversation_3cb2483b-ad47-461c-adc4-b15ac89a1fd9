#pragma once

// Project
#include <memory>

#include "ufs/hdfs/hdfs_client.h"
#include "ufs/ufs.h"

namespace dancenn {
class HdfsUfs final : public Ufs {
 public:
  HdfsUfs(UfsConfig ufs_config,
          HdfsConfig config,
          const std::shared_ptr<HdfsClient>& hdfs_client);
  virtual ~HdfsUfs() = default;

  virtual Status Init() override;

  virtual std::string Name() override;

  virtual Status GetFileStatus(const std::string& path,
                               UfsFileStatus* info) override;
  virtual Status GetFileOnlyStatus(const std::string& path,
                                   UfsFileStatus* info) override;
  virtual Status GetDirectoryStatus(const std::string& path,
                                    UfsDirStatus* dir) override;

  virtual Status ListFiles(const std::string& path,
                           const ListFilesOption& option,
                           ListFilesResult* result) override;
  virtual Status Mkdir(const std::string& path) override;
  virtual Status AbortMkdir(const std::string& path) override;
  virtual Status CreateMissingParentIfNecessary(
      const std::string& path) override;
  virtual Status CheckParentReady(const std::string& path) override;

  virtual Status Create(const std::string& path,
                        const UfsCreateOption& opt,
                        UfsFileInfoProto* info) override;
  virtual Status AbortCreate(const std::string& path,
                             const UfsFileInfoProto& info,
                             const UfsAbortCreateOption& opt) override;

  virtual Status CopyDirectory(const std::string& src,
                               const std::string& dst,
                               UfsCopyResult* result) override {
    return Status(Code::kNotImplemented);
  }

  virtual Status CopyFile(const std::string& src,
                          const std::string& dst,
                          bool overwrite,
                          UfsCopyResult* result) override {
    return Status(Code::kNotImplemented);
  }

  Status DeleteFile(const std::string& path) override;

  Status DeleteDirectory(const std::string& path) override;

  virtual Status CheckUfsFileState(const std::string& path,
                                   const UfsFileInfoProto& info) override;
  virtual Status CompleteFile(const std::string& path,
                              const std::map<int, std::string>& parts,
                              UfsFileInfoProto* info,
                              uint64_t size) override;

  virtual Status GetUfsIdentifier(const UfsIdentifierInfo& info,
                                  std::string* key) override;
  virtual Status GetUfsInfo(const std::string& key,
                            UfsIdentifierInfo* info) override;

  virtual Status ReadFile(const std::string& path,
                          uint64_t offset,
                          uint64_t length,
                          std::string* result) override {
    return Status(Code::kNotImplemented);
  }

  virtual bool SupportAtomicRename() override {
    return true;
  }

  virtual Status Rename(const std::string& src,
                        const std::string& dst,
                        bool overwrite) override;

  virtual std::string GetBlockPufsName(const UfsFileInfoProto& ufs_info,
                                       const BlockInfoProto& bip) override;

 private:
  Status CheckHdfsPathValid(const std::string& ufs_path, std::string* path);

  std::string GetUploadDir(const std::string& upload_id);

  HdfsConfig config_;
  std::shared_ptr<HdfsClient> client_;
  std::string upload_dir_prefix_;
};
}  // namespace dancenn