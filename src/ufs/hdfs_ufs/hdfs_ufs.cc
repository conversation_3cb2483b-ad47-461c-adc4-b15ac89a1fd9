#include "ufs/hdfs_ufs/hdfs_ufs.h"

#ifndef DANCENN_DISABLE_UFS_HDFS
#include <hdfs/hdfs.h>
#endif

// System
#include <absl/strings/str_format.h>
#include <algorithm>
#include <cstdint>
#include <utility>
#include <vector>

#include "absl/strings/str_join.h"
#include "base/java_exceptions.h"
#include "base/path_util.h"
#include "base/status.h"
#include "base/uuid.h"
#include "glog/logging.h"
#include "ufs/hdfs/hdfs_client.h"
#include "ufs/hdfs/hdfs_constant.h"
#include "ufs/ufs.h"
#include "ufs/ufs_config.h"
#include "ufs/ufs_dir_status.h"
#include "ufs/ufs_file_status.h"

DECLARE_int64(namespace_id);
DECLARE_bool(log_ufs_persist_detail);
DECLARE_bool(ufs_sync_mkdir_create_in_ufs);

namespace dancenn {
HdfsUfs::HdfsUfs(UfsConfig ufs_config,
                 HdfsConfig config,
                 const std::shared_ptr<HdfsClient>& client)
    : Ufs(UfsType::UFS_TYPE_HDFS, ufs_config),
      config_(std::move(config)),
      client_{client},
      upload_dir_prefix_{absl::StrFormat(
          "/%s/%d",
          JoinTwoPath(config_.prefix, HdfsConstants::kHdfsUfsUploadDirPrefix),
          FLAGS_namespace_id)} {
  RemoveRedundantSlash(&upload_dir_prefix_);
}

Status HdfsUfs::Init() {
  // TODO(@max.chenxi): for security
  return Status::OK();
}

std::string HdfsUfs::Name() {
  if (config_.nn.empty()) {
    return "HDFS-" + config_.consul;
  } else {
    return "HDFS-" + absl::StrFormat("%s:%d", config_.nn, config_.port);
  }
}

Status HdfsUfs::GetFileStatus(const std::string& ufs_path,
                              UfsFileStatus* info) {
  Status s;
#ifndef DANCENN_DISABLE_UFS_HDFS
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  FileInfo file_info;
  s = client_->GetPathInfo(path, &file_info);
  if (!s.IsOK()) {
    return s;
  }

  UfsFileStatus file;
  s = std::move(UfsFileStatusBuilder()
                    .SetFileType(file_info.info->mKind == kObjectKindFile
                                     ? UFS_FILE
                                     : UFS_DIR)
                    .SetFullPath(path)
                    .SetModifiedTs(file_info.info->mLastMod)
                    .SetFileSize(file_info.info->mSize)
                    .SetBlockSize(file_info.info->mBlockSize)
                    .Build(&file));
  if (!s.IsOK()) {
    return s;
  }

  *info = std::move(file);
#else
  LOG(FATAL) << "Not support HDFS UFS";
#endif
  return s;
}

Status HdfsUfs::GetFileOnlyStatus(const std::string& ufs_path,
                                  UfsFileStatus* info) {
  Status s;
#ifndef DANCENN_DISABLE_UFS_HDFS
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  FileInfo file_info;
  s = client_->GetPathInfo(path, &file_info);
  if (!s.IsOK()) {
    return s;
  }

  if (file_info.info->mKind == kObjectKindDirectory) {
    s = std::move(Status(Code::kFileTypeError));
    return s;
  }

  UfsFileStatus file;
  s = std::move(UfsFileStatusBuilder()
                    .SetFileType(file_info.info->mKind == kObjectKindFile
                                     ? UFS_FILE
                                     : UFS_DIR)
                    .SetFullPath(path)
                    .SetModifiedTs(file_info.info->mLastMod)
                    .SetFileSize(file_info.info->mSize)
                    .SetBlockSize(file_info.info->mBlockSize)
                    .Build(&file));
  if (!s.IsOK()) {
    return s;
  }

  *info = std::move(file);
#else
  LOG(FATAL) << "Not support HDFS UFS";
#endif
  return s;
}

Status HdfsUfs::GetDirectoryStatus(const std::string& ufs_path,
                                   UfsDirStatus* dir) {
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  UfsFileStatus status;
  Status s = GetFileStatus(path, &status);
  if (!s.IsOK() || !status.IsDir()) {
    return Status(JavaExceptions::kIOException,
                  Code::kDirNotFound,
                  "dir: " + path + " not found");
  }

  FileInfo file_info;
  s = client_->ListDirectory(path, &file_info);
  if (!s.IsOK()) {
    return s;
  }

  if (file_info.EntryCount() == 0) {
    LOG(INFO) << "HdfsUfs::GetIsDirectoryEmpty path: " << path;
    *dir = UfsDirStatus(path, false);
  } else {
    *dir = UfsDirStatus(path, true);
  }
  return s;
}

Status HdfsUfs::ListFiles(const std::string& ufs_path,
                          const ListFilesOption& option,
                          ListFilesResult* result) {
  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
      << "ListFiles for path: " << ufs_path
      << ", page_size: " << option.page_size
          << ", recursive: " << option.recursive
          << ", continue_token: " << option.continue_token;
  Status s;
#ifndef DANCENN_DISABLE_UFS_HDFS
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  FileInfo file_info;
  int has_remaining;
  s = client_->ListDirectory(
      path, &file_info, option.continue_token.c_str(), &has_remaining);
  if (!s.IsOK()) {
    return s;
  }

  if (file_info.EntryCount() == 0) {
    LOG(INFO) << "HdfsUfs::GetIsDirectoryEmpty path: " << path;
    result->files.clear();
    result->has_more = false;
    result->continue_token.clear();
  } else {
    std::vector<UfsFileStatus> files;
    files.reserve(file_info.EntryCount());
    int batch_size = std::min<int>(file_info.EntryCount(), option.page_size);
    result->has_more =
        has_remaining == 1 || option.page_size < file_info.EntryCount();

    for (int i = 0; i < batch_size; i++) {
      // build status
      UfsFileStatus status;
      auto info = file_info.info[i];
      auto file_name = GetFileNameFromPath(info.mName);
      auto build_s =
          UfsFileStatusBuilder()
              .SetFileType(info.mKind == kObjectKindFile ? UFS_FILE : UFS_DIR)
              .SetFullPath(JoinTwoPath(path, file_name))
              .SetFileSize(info.mSize)
              .SetModifiedTs(info.mLastMod)
              .SetBlockSize(info.mBlockSize)
              .Build(&status);

      if (i == batch_size - 1 && result->has_more) {
        // maintain continue token
        result->continue_token.assign(file_name);
      }

      if (!build_s.IsOK()) {
        LOG(WARNING) << "Failed to construct UfsFileStatus. fullpath: "
                     << info.mName << ", error: " << build_s.ToString();
        MFC(LoggerMetrics::Instance().warn_)->Inc();
        continue;
      }

      files.emplace_back(std::move(status));
    }
    result->files = std::move(files);
  }
#else
  LOG(FATAL) << "Not support HDFS UFS";
#endif
  return s;
}

Status HdfsUfs::Mkdir(const std::string& ufs_path) {
  CHECK_READ_ONLY
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));
  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail) << "Create dir: " << path;

  return client_->CreateDirectory(path);
}

Status HdfsUfs::AbortMkdir(const std::string& ufs_path) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail) << "AbortCreate dir: " << path;

  return client_->Delete(path, true);
}

Status HdfsUfs::CreateMissingParentIfNecessary(const std::string& path) {
  std::vector<cnetpp::base::StringPiece> ancestors;
  CHECK(GetAllAncestorPaths(path, &ancestors));
  ancestors.pop_back();
  if (ancestors.size() <= 1) {
    // Self or parent is root
    return Status::OK();
  }
  std::string parent = ancestors.back().as_string();
  return Mkdir(parent);
}

Status HdfsUfs::CheckParentReady(const std::string& path) {
  std::vector<cnetpp::base::StringPiece> ancestors;
  CHECK(GetAllAncestorPaths(path, &ancestors));
  ancestors.pop_back();
  if (ancestors.size() <= 1) {
    // Self or parent is root
    return Status::OK();
  }
  std::string parent = ancestors.back().as_string();
  Status s;
  UfsFileStatus status;
  s = GetFileStatus(parent, &status);
  if (!s.IsOK()) {
    if (s.code() == Code::kFileNotFound) {
      return Status::OK();
    }
    return s;
  }
  if (!status.IsDir()) {
    return Status(JavaExceptions::Exception::kFileNotFoundException,
                  Code::kFileNotFound,
                  "Parent directory doesn't exist.");
  }
  return Status::OK();
}

Status HdfsUfs::Create(const std::string& ufs_path,
                       const UfsCreateOption& opt,
                       UfsFileInfoProto* info) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  if (opt.emptyObject) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Create empty file: " << path;
    auto s = client_->CreateFile(path);
    if (!s.IsOK()) {
      return s;
    }
    info->clear_upload_id();
  }

  if (opt.multipartUpload) {
    const auto& upload_id = UUID().ToString();
    const auto& mpu_file =
        JoinTwoPath(GetUploadDir(upload_id), GetFileNameFromPath(path));
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Create MPU file: " << mpu_file;

    auto s = client_->CreateFile(mpu_file);
    if (!s.IsOK()) {
      return s;
    }
    *(info->mutable_upload_id()) = std::move(upload_id);
  }
  return Status::OK();
}

Status HdfsUfs::AbortCreate(const std::string& ufs_path,
                            const UfsFileInfoProto& info,
                            const UfsAbortCreateOption& opt) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));
  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
      << "AbortCreate file path: " << path;

  if (opt.abortMultipartUpload) {
    // TODO(@max.chenxi):only delete upload dir, but dn MPU can not be cancelled
    const auto& upload_dir = GetUploadDir(info.upload_id());
    auto s = client_->Delete(upload_dir, true);
    if (!s.IsOK()) {
      return s;
    }
  }

  return client_->Delete(path, false);
}

Status HdfsUfs::DeleteFile(const std::string& ufs_path) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  return client_->Delete(path, false);
}

Status HdfsUfs::DeleteDirectory(const std::string& ufs_path) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  return client_->Delete(path, true);
}

Status HdfsUfs::CheckUfsFileState(const std::string& ufs_path,
                                  const UfsFileInfoProto& info) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));
  return Status::OK();
}

Status HdfsUfs::CompleteFile(const std::string& ufs_path,
                             const std::map<int, std::string>& parts,
                             UfsFileInfoProto* info,
                             uint64_t size) {
  CHECK_READ_ONLY;
  std::string path;
  RETURN_NOT_OK(CheckHdfsPathValid(ufs_path, &path));

  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail) << "Complete file path: " << path;

  if (!FLAGS_ufs_sync_mkdir_create_in_ufs) {
    // mkdir not create parent immediately, so we need to create parent dir
    // before rename.
    auto s = CreateMissingParentIfNecessary(path);
    if (s.IsOK()) {
      LOG(INFO) << "[CompleteFile] Ufs create missing parent success: " << path;
    } else {
      LOG(INFO) << "[CompleteFile] Ufs create missing parent failed: " << path
                << ", error: " << s.ToString();
    }
  }

  const auto& upload_dir = GetUploadDir(info->upload_id());
  std::vector<std::string> paths;
  for (auto it = parts.begin(); it != parts.end(); it++) {
    const auto& src_path = absl::StrFormat("%s/%d", upload_dir, it->first);
    VLOG(12) << "Construct src path: " << src_path;
    paths.push_back(src_path);
  }

  CHECK(!paths.empty());

  Status s;
  // s = client_->CreateFile(path);
  // if (!s.IsOK()) {
  //   return s;
  // }
  if (paths.size() == 1) {
    // rename
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Rename from " << paths[0] << " to " << path;
    s = client_->Rename(paths[0], path, true);
    if (!s.IsOK()) {
      return s;
    }
  } else {
    // concat
    const auto& concat_path =
        JoinTwoPath(upload_dir, GetFileNameFromPath(path));
    UfsFileStatus file;
    s = GetFileStatus(concat_path, &file);
    bool skip_concat = false;
    if (s.IsOK()) {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "Concat file path: " << concat_path << " size: " << file.FileSize()
          << " inode size: " << size;
      if (file.FileSize() == size) {
        // skip concat
        skip_concat = true;
      }
    }

    if (!skip_concat) {
      VLOG(8) << "Concat file path: " << path
              << ", src_paths: " << absl::StrJoin(paths, ",");
      auto s = client_->Concat(concat_path, paths);
      if (!s.IsOK()) {
        return s;
      }
    }
    s = client_->Rename(concat_path, path, true);
    if (!s.IsOK()) {
      return s;
    }
  }
  return client_->Delete(upload_dir, true);
}

Status HdfsUfs::GetUfsIdentifier(const UfsIdentifierInfo& info,
                                 std::string* path) {
  return CheckHdfsPathValid(info.path, path);
}

Status HdfsUfs::GetUfsInfo(const std::string& key, UfsIdentifierInfo* info) {
  CHECK_NOTNULL(info);
  std::string path = key;
  if (path.back() == '/') {
    path.pop_back();
  }
  if (path.front() != '/') {
    path.insert(0, "/");
  }
  info->path = path;
  return Status::OK();
}

Status HdfsUfs::Rename(const std::string& src,
                       const std::string& dst,
                       bool overwrite) {
  std::string src_path, dst_path;
  RETURN_NOT_OK(CheckHdfsPathValid(src, &src_path));
  RETURN_NOT_OK(CheckHdfsPathValid(dst, &dst_path));

  // check src file
  // FileInfo src_file;
  // auto s = client_->GetPathInfo(src_path, &src_file);
  // if (!s.IsOK()) {
  //   return s;
  // }

  if (!overwrite) {
    FileInfo dst_file;
    auto s = client_->GetPathInfo(dst_path, &dst_file);
    if (!s.IsOK() && s.code() != Code::kFileNotFound) {
      return s;
    }
    if (s.IsOK()) {
      return Status(JavaExceptions::Exception::kFileAlreadyExistsException,
                    Code::kFileExists,
                    "dst already existed, cannot copy direcotry. dst: " + dst);
    }
  }

  return client_->Rename(src_path, dst_path, overwrite);
}

Status HdfsUfs::CheckHdfsPathValid(const std::string& ufs_path,
                                   std::string* valid_path) {
  std::string path;
  path.reserve(ufs_path.size() + 8);
  path.assign(ufs_path);

  RemoveRedundantSlash(&path);

  if (path.empty()) {
    LOG(INFO) << "Invalid parameter, ufs_path is empty";
    return Status(JavaExceptions::Exception::kIllegalArgumentException,
                  Code::kBadParameter,
                  "ufs_path is empty.");
  }
  if (path[0] != kSeparatorChar) {
    LOG(INFO) << "Invalid parameter, ufs_path not start with / :" << ufs_path;
    return Status(JavaExceptions::Exception::kIllegalArgumentException,
                  Code::kBadParameter,
                  "ufs_path not start with / : " + ufs_path);
  }

  *valid_path = std::move(path);
  return Status::OK();
}

std::string HdfsUfs::GetUploadDir(const std::string& upload_id) {
  return JoinTwoPath(upload_dir_prefix_, upload_id);
}

std::string HdfsUfs::GetBlockPufsName(const UfsFileInfoProto& ufs_info,
                                      const BlockInfoProto& bip) {
  return absl::StrFormat(
      "%s/%d", GetUploadDir(ufs_info.upload_id()), bip.part_num());
}

}  // namespace dancenn