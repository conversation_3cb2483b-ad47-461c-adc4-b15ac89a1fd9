#pragma once

#include <memory>

#include "hdfs.pb.h"
#include "namespace/namespace.h"
#include "ufs/hdfs/hdfs_client.h"
#include "ufs/persistent_ufs_info.h"
#include "ufs/ufs_config.h"
#include "ufs/ufs_env.h"

namespace dancenn {
class HdfsUfsEnv final : public UfsEnv {
 public:
  HdfsUfsEnv();
  ~HdfsUfsEnv() = default;

  Status Start() override;
  void Stop() override;

  UfsConfig CreateUfsConfig() override;

  std::shared_ptr<Ufs> CreateUfs(const UfsConfig& c) override;

  void CheckPersistentUfsInfo(
      PersistentUfsInfo* stored_ufs_info) const override;

  bool GetUfsInfo(cloudfs::RemoteBlockInfoProto* info,
                  bool for_internal_use) const override;

  void DumpUfsInfoToFlags(PersistentFlags* persistent_flags) const override;

  Status GetBlockLengthFromUfs(const std::string& ufs_filename,
                               int64_t* new_length) const override;

  Status DeleteBlocks(
      const cloudfs::datanode::HeartbeatResponseProto& heartbeat_resp) override;

 private:
  HdfsConfig hdfs_config_;
  std::shared_ptr<HdfsClient> hdfs_client_{nullptr};
};
}  // namespace dancenn