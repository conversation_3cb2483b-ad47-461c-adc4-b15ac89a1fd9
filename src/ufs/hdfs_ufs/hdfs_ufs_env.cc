#include "ufs/hdfs_ufs/hdfs_ufs_env.h"

#ifndef DANCENN_DISABLE_UFS_HDFS
#include <hdfs/hdfs.h>
#endif

#include <cstdint>
#include <memory>
#include <string>

#include "base/status.h"
#include "base/string_utils.h"
#include "gflags/gflags_declare.h"
#include "glog/logging.h"
#include "hdfs.pb.h"
#include "ufs/hdfs/hdfs_client.h"
#include "ufs/hdfs/hdfs_constant.h"
#include "ufs/hdfs_ufs/hdfs_ufs.h"
#include "ufs/ufs_config.h"

DECLARE_string(hdfs_namespace_node_addr);
DECLARE_uint32(hdfs_namespace_node_port);
DECLARE_string(hdfs_user);
DECLARE_string(hdfs_prefix);
DECLARE_string(hdfs_consul);
DECLARE_string(hdfs_sec_token);

namespace dancenn {

static bool ParseHdfsConfig(const UfsConfig& ufs_conf, HdfsConfig* config) {
  auto&& params = ufs_conf.params;
  auto itor = params.find(HdfsConstants::kConfigNN);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << HdfsConstants::kConfigNN;
    return false;
  }
  auto&& nn = itor->second;

  itor = params.find(HdfsConstants::kConfigPort);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << HdfsConstants::kConfigPort;
    return false;
  }
  uint16_t port;
  StringPiece port_str(itor->second);
  if (!StringUtils::GetFixed16(&port_str, &port)) {
    LOG(ERROR) << "parse port failed";
    return false;
  }

  itor = params.find(HdfsConstants::kConfigUser);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << HdfsConstants::kConfigUser;
    return false;
  }
  auto&& user = itor->second;

  itor = params.find(HdfsConstants::kConfigPrefix);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << HdfsConstants::kConfigPrefix;
    return false;
  }
  auto&& prefix = itor->second;

  itor = params.find(HdfsConstants::kConfigConsul);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << HdfsConstants::kConfigConsul;
    return false;
  }
  auto&& consul = itor->second;

  *config = HdfsConfig(nn, port, user, prefix, consul);
  return true;
}

HdfsUfsEnv::HdfsUfsEnv() {
  hdfs_config_.nn = FLAGS_hdfs_namespace_node_addr;
  hdfs_config_.port = FLAGS_hdfs_namespace_node_port;
  hdfs_config_.user = FLAGS_hdfs_user;
  hdfs_config_.prefix = FLAGS_hdfs_prefix;
  hdfs_config_.consul = FLAGS_hdfs_consul;

  if (!(FLAGS_hdfs_prefix.empty() ||
        FLAGS_hdfs_prefix[FLAGS_hdfs_prefix.size() - 1] == '/')) {
    LOG(FATAL) << "Invalid gflags, prefix should be \"\" or end with '/'. "
                  "FLAGS_hdfs_prefix="
               << FLAGS_hdfs_prefix;
  }

  hdfs_client_ = std::make_shared<HdfsClient>(hdfs_config_);
}

Status HdfsUfsEnv::Start() {
  return UfsEnv::Start();
}

void HdfsUfsEnv::Stop() {
  UfsEnv::Stop();
}

UfsConfig HdfsUfsEnv::CreateUfsConfig() {
  UfsConfig ufs_config;
  ufs_config.type = UfsType::UFS_TYPE_HDFS;
  ufs_config.ufs_prefix = "/" + FLAGS_hdfs_prefix;
  ufs_config.params[HdfsConstants::kConfigNN] = FLAGS_hdfs_namespace_node_addr;
  ufs_config.params[HdfsConstants::kConfigUser] = FLAGS_hdfs_user;
  ufs_config.params[HdfsConstants::kConfigPrefix] = FLAGS_hdfs_prefix;
  ufs_config.params[HdfsConstants::kConfigConsul] = FLAGS_hdfs_consul;
  std::string port_str;
  StringUtils::PutFixed16(&port_str, FLAGS_hdfs_namespace_node_port);
  ufs_config.params[HdfsConstants::kConfigPort] = port_str;
  return ufs_config;
}

std::shared_ptr<Ufs> HdfsUfsEnv::CreateUfs(const UfsConfig& c) {
  CHECK(c.type == UFS_TYPE_HDFS) << "Only HDFS is supported";

  HdfsConfig config;
  if (!ParseHdfsConfig(c, &config)) {
    LOG(ERROR) << "CreateUfs Failed, ParseHdfsConfig Failed";
    return nullptr;
  }

  return std::make_shared<HdfsUfs>(c, std::move(config), hdfs_client_);
}

void HdfsUfsEnv::CheckPersistentUfsInfo(
    PersistentUfsInfo* stored_ufs_info) const {
  CHECK_NOTNULL(stored_ufs_info);

  CHECK_EQ(stored_ufs_info->protocol, PersistentUfsProtocol::kHdfs);

  if (stored_ufs_info->hdfs_info.nn != hdfs_config_.nn) {
    LOG(WARNING) << "Update hdfs_nn_addr."
                 << " from=" << stored_ufs_info->hdfs_info.nn
                 << " to=" << hdfs_config_.nn;
    stored_ufs_info->hdfs_info.nn = hdfs_config_.nn;
  }

  if (stored_ufs_info->hdfs_info.port != hdfs_config_.port) {
    LOG(WARNING) << "Update port."
                 << " from=" << stored_ufs_info->hdfs_info.port
                 << " to=" << hdfs_config_.port;
    stored_ufs_info->hdfs_info.port = hdfs_config_.port;
  }
  if (stored_ufs_info->hdfs_info.user != hdfs_config_.user) {
    LOG(WARNING) << "Update user."
                 << " from=" << stored_ufs_info->hdfs_info.user
                 << " to=" << hdfs_config_.user;
    stored_ufs_info->hdfs_info.user = hdfs_config_.user;
  }
  if (stored_ufs_info->hdfs_info.prefix != hdfs_config_.prefix) {
    LOG(WARNING) << "Update prefix."
                 << " from=" << stored_ufs_info->hdfs_info.prefix
                 << " to=" << hdfs_config_.prefix;
    stored_ufs_info->hdfs_info.prefix = hdfs_config_.prefix;
  }
}

bool HdfsUfsEnv::GetUfsInfo(cloudfs::RemoteBlockInfoProto* info,
                            bool for_internal_use) const {
  info->set_type(cloudfs::RemoteBlockInfoProto::HDFS);
  info->mutable_hdfs_info()->set_nn(hdfs_config_.nn);
  info->mutable_hdfs_info()->set_port(hdfs_config_.port);
  info->mutable_hdfs_info()->set_user(hdfs_config_.user);
  info->mutable_hdfs_info()->set_prefix(hdfs_config_.prefix);
  info->mutable_hdfs_info()->set_consul(hdfs_config_.consul);

  info->mutable_hdfs_info()->set_sectoken(FLAGS_hdfs_sec_token);
  return true;
}

void HdfsUfsEnv::DumpUfsInfoToFlags(PersistentFlags* persistent_flags) const {
  CHECK_NOTNULL(persistent_flags);
}

Status HdfsUfsEnv::GetBlockLengthFromUfs(const std::string& ufs_filename,
                                         int64_t* new_length) const {
#ifndef DANCENN_DISABLE_UFS_HDFS
  CHECK(new_length);

  *new_length = 0;
  auto session = hdfs_client_->Acquire();
  auto* file_info = hdfsGetPathInfo(session->fs, ufs_filename.c_str());
  if (file_info != nullptr) {
    *new_length = file_info->mSize;
    hdfsFreeFileInfo(file_info, 1);
  }
#else
  LOG(FATAL) << "Not support HDFS UFS";
#endif

  return Status::OK();
}

Status HdfsUfsEnv::DeleteBlocks(
    const cloudfs::datanode::HeartbeatResponseProto& heartbeat_resp) {
  // only support ACC_HDFS at now!!!
  // delete blocks only in TOS_LOCAL & TOS_MANAGED
  return Status::OK();
}

}  // namespace dancenn