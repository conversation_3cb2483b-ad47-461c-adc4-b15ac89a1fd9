#include "ufs/hdfs_ufs/hdfs_ufs_uploader.h"

#include "inode.pb.h"
#include "ufs/ufs_file_status.h"

DECLARE_bool(log_ufs_persist_detail);

namespace dancenn {

HdfsUfsUploader::HdfsUfsUploader(NameSpace* ns, Ufs* ufs)
    : UfsUploader(ns, ufs) {
}

HdfsUfsUploader::~HdfsUfsUploader() {
}

Status HdfsUfsUploader::PersistObject(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "PersistUfsFile inode " << inode.id() << " state " << inode.status();

  switch (inode.ufs_file_info().file_state()) {
    case UfsFileState::kUfsFileStateToBePersisted: {
      // Not persisted yet
      ns_->ufs_uploading_mgr()->add_upload_ongoing_inode(inode.id());

      return PersistHdfsFile(f_ctx);
    }
    case UfsFileState::kUfsFileStatePersisted: {
      // When WriteBackManager under high pressure, it is possible to get here
      // Consider add MetaScannerV2 worker count (need restart)
      // dfs_meta_scanner_v2_worker_num
      LOG(INFO) << "File already persisted " << inode.ShortDebugString();
      return Status::OK();
    }
    case UfsFileState::kUfsFileStateLocal: {
      LOG(WARNING) << "Local file do not need persist, should not get here "
                   << inode.ShortDebugString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return Status::OK();
    }
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }
}

Status HdfsUfsUploader::PersistHdfsFile(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  INode old_inode = inode;

  uint64_t size = 0;
  for (auto& b : inode.blocks()) {
    size += b.numbytes();
  }

  // Step 1: check block is ready to persisted
  Status s;
  switch (inode.ufs_file_info().create_type()) {
    case kUfsFileCreateTypeAppend:
      s = PersistHdfsFileAppend(&inode, size);
      break;
    case kUfsFileCreateTypeNormal:
      s = PersistHdfsFileNormal(
          f_ctx->ufs_path, f_ctx->inner_path, &inode, size);
      break;
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }

  if (!s.IsOK()) {
    return s;
  }

  // Step 2: write inode
  UfsFileInfoProto* ufs_file_info = inode.mutable_ufs_file_info();
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  ufs_file_info->set_file_state(UfsFileState::kUfsFileStatePersisted);
  ufs_file_info->set_sync_ts(now_ts);
  ufs_file_info->set_size(size);

  // ACC_HDFS use UfsFileInfo mtime and use mtime to compute sync action
  UfsFileStatus info;
  ufs_->GetFileOnlyStatus(f_ctx->ufs_path, &info);
  ufs_file_info->set_last_modified_ts(info.modified_ts());

  // TODO(zhuangsiyu): clear pufsname and upload id in all bips
  // Step 3: make all block persisted
  for (auto& b : inode.blocks()) {
    // non-key block may have no chance to persist, do it before persist file.
    PersistNonKeyBlock(inode, b.blockid());
  }

  // Step 4: write inode
  return PersistCfsFile(f_ctx->inner_path, f_ctx->parent, inode, old_inode);
}

Status HdfsUfsUploader::PersistHdfsFileAppend(INode* inode, uint64_t size) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeAppend);

  if (inode->status() != INode_Status_kFileComplete) {
    return Status(Code::kFileStatusError, "File not completed");
  }

  if (size == 0) {
    // Empty append object, do nothing
    return Status::OK();
  }

  BlockInfoProto bip;
  if (!ns_->meta_storage()->GetBlockInfo(
          inode->blocks(inode->blocks_size() - 1).blockid(), &bip)) {
    LOG(INFO) << "Failed to get AccInfo for block "
              << inode->ShortDebugString();
    return Status(Code::kError, "Block not found");
  }

  if (bip.state() != BlockInfoProto_Status::BlockInfoProto_Status_kPersisted) {
    return Status(Code::kError, "Block not persisted");
  }

  // in ACC_HDFS etag is empty, use mtime to compute sync action.
  // inode->mutable_ufs_file_info()->set_etag(bip.etag());

  return Status::OK();
}

Status HdfsUfsUploader::PersistHdfsFileNormal(const std::string& ufs_path,
                                              const std::string& inner_path,
                                              INode* inode,
                                              uint64_t size) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeNormal);

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;

  if (size == 0) {
    // empty file
    if (inode->status() != INode_Status_kFileComplete) {
      return Status(Code::kFileStatusError, "File not completed");
    }

    auto s = ufs_->Create(
        new_path, UfsCreateOption(true, false), inode->mutable_ufs_file_info());
    if (!s.IsOK()) {
      LOG(INFO) << "Create empty object in ufs failed " << s.ToString();
    }
    return s;
  }

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // MPU not created yet
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "UploadId is empty, inode " << inode->id() << " old path "
        << old_path << " new path " << new_path;
    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // check path
  // if (old_path != new_path) {
  // Path has changed during persist
  // Will re-upload all blocks
  // LOG(INFO) << "Path changed during persist, inode " << inode->id()
  //           << " old path " << old_path << " new path " << new_path;

  // Abort old multipart upload
  // This may be done multiple time during failover
  // Do not check success
  // Status ufs_status;
  // ufs_status = ufs_->AbortCreate(
  //     old_path, inode->ufs_file_info(), UfsAbortCreateOption(true));
  // if (!ufs_status.IsOK()) {
  //   LOG(INFO) << "Abort MPU failed path " << old_path << " status "
  //             << ufs_status.ToString();
  // }

  // auto s =
  //     ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
  // if (s.IsOK()) {
  //   return Status(Code::kIsRetry, "Recreate upload id, need retry.");
  // } else {
  //   return s;
  // }
  // }

  // action
  auto state = ufs_->CheckUfsFileState(old_path, inode->ufs_file_info());
  if (!state.IsOK()) {
    LOG(WARNING) << "Check UploadId failed, inode " << inode->id() << " path "
                 << old_path << ", " << state.ToString();
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, old_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  std::map<int, std::string> parts;
  bool allBlocksUploaded = true;
  auto s =
      ns_->CheckAllBlockPersisted(*inode, &parts, &allBlocksUploaded, ufs_);
  if (!s.IsOK()) {
    return s;
  }

  if (inode->status() != INode_Status_kFileComplete) {
    return Status(Code::kFileStatusError, "File not completed");
  }

  if (allBlocksUploaded) {
    auto s =
        ufs_->CompleteFile(old_path, parts, inode->mutable_ufs_file_info(), size);
    if (!s.IsOK()) {
      return s;
    }
    if (old_path != new_path) {
      // rename dst path
      LOG(INFO) << "Path changed during persist, inode " << inode->id()
                << " old path " << old_path << " new path " << new_path;
      s = ufs_->Rename(old_path, new_path, true);
      if (!s.IsOK()) {
        return s;
      }
    }
    inode->mutable_ufs_file_info()->clear_upload_id();
    inode->mutable_ufs_file_info()->clear_key();

    return s;
  } else {
    return Status(Code::kUfsUploadNotReady, "Not all blocks persisted");
  }
}

Status HdfsUfsUploader::TriggerUploadHdfsFileAppend(
    const std::string& ufs_path,
    const std::string& inner_path,
    INode* inode) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeAppend);

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileAppend"
      << " ufs_path=" << ufs_path << " inner_path=" << inner_path
      << " inode=" << inode->ShortDebugString();

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileAppend old_path=" << old_info.path;

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // pass, append mode not need upload_id
  }

  // check path
  if (old_path != new_path) {
    // pass, append mode not need (?)
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileAppend trigger block upload";
  for (auto& b : inode->blocks()) {
    BlockInfoProto bip;
    if (!ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip)) {
      LOG(INFO) << "Failed to get AccInfo for block " << b.blockid();
      return Status(Code::kError, "Block not found");
    }

    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadHdfsFileAppend check block one by one"
        << " block_id=" << b.blockid() << " bip=" << bip.ShortDebugString();

    if (bip.state() ==
        BlockInfoProto_Status::BlockInfoProto_Status_kPersisted) {
      continue;
    } else {
      VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
          << "TriggerUploadHdfsFileAppend, Trigger";

      ns_->block_manager()->TriggerUploadChooseDN(
          b, inode->ufs_file_info().upload_id(), inode->ufs_file_info().key());
      break;
    }
  }
  return Status();
}

Status HdfsUfsUploader::TriggerUploadHdfsFileNormal(
    const std::string& ufs_path,
    const std::string& inner_path,
    INode* inode) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeNormal);

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileNormal"
      << " ufs_path=" << ufs_path << " inner_path=" << inner_path
      << " inode=" << inode->ShortDebugString();

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileNormal old_path=" << old_info.path;

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // MPU not created yet
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "UploadId is empty, inode " << inode->id() << " old path "
        << old_path << " new path " << new_path;
    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // check path
  if (old_path != new_path) {
    // Path has changed during persist
    // Will re-upload all blocks
    LOG(INFO) << "Path changed during persist, inode " << inode->id()
              << " old path " << old_path << " new path " << new_path;

    // Abort old multipart upload
    // This may be done multiple time during failover
    // Do not check success
    Status ufs_status;
    ufs_status = ufs_->AbortCreate(
        old_path, inode->ufs_file_info(), UfsAbortCreateOption(true));
    if (!ufs_status.IsOK()) {
      LOG(INFO) << "Abort MPU failed path " << old_path << " status "
                << ufs_status.ToString();
    }

    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFileNormal trigger block upload";
  for (auto& b : inode->blocks()) {
    BlockInfoProto bip;
    if (!ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip)) {
      LOG(INFO) << "Failed to get AccInfo for block " << b.blockid();
      return Status(Code::kError, "Block not found");
    }

    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadHdfsFileNormal check block one by one"
        << " block_id=" << b.blockid() << " bip=" << bip.ShortDebugString();

    const std::string& pufs_name =
        ufs_->GetBlockPufsName(inode->ufs_file_info(), bip);
    if (bip.state() ==
            BlockInfoProto_Status::BlockInfoProto_Status_kPersisted &&
        (bip.has_curr_upload_id() &&
         bip.curr_upload_id() == inode->ufs_file_info().upload_id()) &&
        (bip.has_pufs_name() && bip.pufs_name() == pufs_name)) {
      // skip persisted and consist bip
      continue;
    }

    // need to trigger upload
    ns_->block_manager()->TriggerUploadChooseDN(
        b, inode->ufs_file_info().upload_id(), pufs_name);
  }
  return Status();
}

Status HdfsUfsUploader::TriggerUploadHdfsFile(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadHdfsFile, inode=" << inode.ShortDebugString();

  uint64_t size = 0;
  for (auto& b : inode.blocks()) {
    size += b.numbytes();
  }

  if (size == 0) {
    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadHdfsFile, size == 0";
    return Status();
  }

  ns_->ufs_uploading_mgr()->add_upload_ongoing_inode(inode.id());

  Status s;
  switch (inode.ufs_file_info().create_type()) {
    case kUfsFileCreateTypeAppend:
      s = TriggerUploadHdfsFileAppend(
          f_ctx->ufs_path, f_ctx->inner_path, &inode);
      break;
    case kUfsFileCreateTypeNormal:
      s = TriggerUploadHdfsFileNormal(
          f_ctx->ufs_path, f_ctx->inner_path, &inode);
      break;
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }

  return s;
}

Status HdfsUfsUploader::TriggerUploadObject(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadObject inode " << inode.id() << " state "
      << inode.status();

  switch (inode.ufs_file_info().file_state()) {
    case UfsFileState::kUfsFileStateToBePersisted: {
      return TriggerUploadHdfsFile(f_ctx);
    }
    case UfsFileState::kUfsFileStatePersisted: {
      LOG(INFO) << "File already persisted " << inode.ShortDebugString();
      return Status::OK();
    }
    case UfsFileState::kUfsFileStateLocal: {
      LOG(WARNING) << "Local file do not need persist, should not get here "
                   << inode.ShortDebugString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return Status::OK();
    }
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }
}

Status HdfsUfsUploader::AbortUpload(const std::string& upload_id,
                                    const std::string& ufs_key) {
  if (!upload_id.empty()) {
    UfsIdentifierInfo id;
    CHECK(ufs_->GetUfsInfo(ufs_key, &id).IsOK());
    UfsFileInfoProto info;
    info.set_upload_id(upload_id);
    Status s = ufs_->AbortCreate(id.path, info, UfsAbortCreateOption(true));
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to abort create " << s.ToString();
      return s;
    }
  }
  return {};
}

}  // namespace dancenn
