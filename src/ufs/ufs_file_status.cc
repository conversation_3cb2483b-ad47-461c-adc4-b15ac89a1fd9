//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "ufs_file_status.h"

#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>

#include "base/constants.h"
#include "base/java_exceptions.h"

namespace dancenn {

UfsFileStatusBuilder UfsFileStatus::Builder() {
  return UfsFileStatusBuilder();
}

std::string UfsFileStatusBuilder::ParseFileName(const std::string& path) {
  if (path == kSeparator) {
    return "";
  }
  const size_t path_len = path.size();
  const bool end_with_slash = path.back() == kSeparatorChar;
  cnetpp::base::StringPiece substr(path.data(),
                                   end_with_slash ? path_len - 1 : path_len);
  auto pos = substr.find_last_of(kSeparatorChar);
  CHECK(pos != cnetpp::base::StringPiece::npos)
      << "Invalid full_path_, not started with / : " << path;
  return substr.substr(pos + 1).as_string();
}

Status UfsFileStatusBuilder::Build(UfsFileStatus* out_s) {
  // Root
  if (info_.full_path_ == kSeparator) {
    info_.file_name_ = "";
    *out_s = std::move(info_);
    return Status::OK();
  }

  {
    std::string file_name = ParseFileName(info_.full_path_);
    if (file_name.empty()) {
      return Status(JavaExceptions::kIOException,
                    Code::kUfsSyncError,
                    "Cannot parse file name for path: " + info_.full_path_);
    }

    info_.file_name_ = std::move(file_name);
  }
  if (info_.full_path_.back() == kSeparatorChar) {
    info_.full_path_ = info_.full_path_.substr(0, info_.full_path_.size() - 1);
  }

  *out_s = std::move(info_);
  return Status::OK();
}

}  // namespace dancenn
