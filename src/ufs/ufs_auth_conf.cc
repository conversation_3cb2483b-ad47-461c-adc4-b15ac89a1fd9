//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "ufs_auth_conf.h"

#include <nlohmann/json.hpp>  // For json.
#include "base/base64.h"

DECLARE_bool(use_fixed_ak);
DECLARE_bool(ufs_auth_enabled);
DECLARE_string(ufs_auth_policy);
DECLARE_string(ufs_auth_fixed_ak);
DECLARE_string(ufs_auth_fixed_sk);
DECLARE_string(ufs_auth_fixed_token);
DECLARE_string(ufs_auth_role_info);

static const std::string kUfsAuthPolicyFixed = "fixed";
static const std::string kUfsAuthPolicyRole = "role";

namespace dancenn
{

UfsAuthConf& UfsAuthConf::Instance() {
  static UfsAuthConf s_conf;
  return s_conf;
}

bool UfsAuthConf::IsFixedAK() {
  if (!FLAGS_ufs_auth_enabled) {
    return FLAGS_use_fixed_ak;
  }
  return FLAGS_ufs_auth_policy == kUfsAuthPolicyFixed;
}

Status UfsAuthConf::Init() {
  if (!FLAGS_ufs_auth_enabled) {
    return Status::OK();
  }

  if (FLAGS_ufs_auth_policy == kUfsAuthPolicyFixed) {
    policy_ = UfsAuthPolicy::FIXED;
    fixed_ak_ = FLAGS_ufs_auth_fixed_ak;
    fixed_sk_ = FLAGS_ufs_auth_fixed_sk;
    fixed_token_ = FLAGS_ufs_auth_fixed_token;
    return Status::OK();
  }

  if (FLAGS_ufs_auth_policy == kUfsAuthPolicyRole) {
    policy_ = UfsAuthPolicy::ROLE;
    return InitRoleInfos();
  }

  LOG(ERROR) << "Invalid ufs_auth_policy: " << FLAGS_ufs_auth_policy;
  return Status(Code::kBadParameter, "Invalid ufs_auth_policy: " + FLAGS_ufs_auth_policy);
}

Status UfsAuthConf::InitRoleInfos() {
  if (FLAGS_ufs_auth_role_info.empty()) {
    LOG(ERROR) << "Invalid ufs_auth_role_info: " << FLAGS_ufs_auth_role_info;
    return Status(Code::kBadParameter, "ufs_auth_role_info is empty.");
  }

  std::string decoded_info_str;
  auto s = Base64::Decode(FLAGS_ufs_auth_role_info, &decoded_info_str);
  if (!s.IsOK()) {
    LOG(ERROR) << "Failed to base64 decode ufs_auth_role_info: " << FLAGS_ufs_auth_role_info << ", error: " << s.ToString();
    return s;
  }

  nlohmann::json obj;
  try {
    obj = nlohmann::json::parse(decoded_info_str);
  } catch (const nlohmann::json::parse_error& e) {
    LOG(ERROR) << "Failed to parse json. e: " << e.what() << ", id: " << e.id
               << ", byte position: " << e.byte;
    return Status(Code::kBadParameter, "Invalid json: " + decoded_info_str);
  }

  if (!obj.is_array()) {
    LOG(ERROR) << "Invalid json, top node is not list: " << decoded_info_str;
    return Status(Code::kBadParameter, "Invalid json, top node is not list: " + decoded_info_str);
  }

  for (const auto& item : obj) {
    UfsAuthRoleInfo r;
    if (item.contains("AccountId")) {
      r.account_id = item["AccountId"];
    }
    r.role_name = item["Role"];
    role_infos_.emplace_back(std::move(r));
  }

  return Status::OK();
}

void UfsAuthConf::TEST_Reset() {
  policy_ = UfsAuthPolicy::FIXED;
  fixed_ak_.clear();
  fixed_sk_.clear();
  fixed_token_.clear();
  role_infos_.clear();
}

} // namespace dancenn