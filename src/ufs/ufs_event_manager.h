//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include "acc/task/sync_task.h"
#include "base/kafka_util.h"
#include "base/threading.h"
#include "ufs/ufs.h"
#include "ufs/ufs_event_manager_metrics.h"
#include "ufs/ufs_event_manager_status.h"

namespace dancenn {

class Ufs;
class NameSpace;

class UfsEventManager {
 public:
  UfsEventManager(const std::shared_ptr<Ufs>& ufs);

  void Start();
  void Stop();

  void SetNS(const std::shared_ptr<NameSpace>& ns);
  void SetHaState(bool is_active);

  void StartConsumers();

  void StopConsumers();

  bool IsKafkaConsumerRunning() const;
  bool IsRmqConsumerRunning() const;

  std::mutex& GetRmqConsumerMtx();
  std::condition_variable& GetRmqConsumerCv();

  void AddSyncTask(const std::string& msg, const SyncCallback& cb);

  const std::vector<std::string>& GetTosEventKeyPrefixBlacklist() const;

  UfsEventManagerStatus GetStatus() const;
  const UfsEventManagerMetrics& metrics() const;
  void ClearError();

 private:
  void InitKafkaConsumers();
  void InitKafkaConf();
  void InitRmqConsumers();

  void StartKafkaConsumers();
  void StartRmqConsumers();

  void StopKafkaConsumers();
  void StopRmqConsumers();

  bool ufs_event_manager_enabled_{false};
  std::atomic_bool consumer_running_{false};

  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<Ufs> ufs_;

  // Kafka consumers
  std::shared_ptr<RdKafka::Conf> kafka_conf_;
  std::vector<std::string> kafka_topics_;
  std::atomic_bool kafka_consumer_running_{false};
  std::unique_ptr<ThreadPool> kafka_consumer_pool_;

  // RMQ consumers
  std::string rmq_endpoint_;
  std::string rmq_topic_;
  std::string rmq_group_id_;
  std::string rmq_access_key_;
  std::string rmq_secret_key_;
  std::atomic_bool rmq_consumer_running_{false};
  std::unique_ptr<ThreadPool> rmq_consumer_pool_;
  std::mutex rmq_consumer_mtx_;
  std::condition_variable rmq_consumer_cv_;

  // Handlers
  std::unique_ptr<ThreadPool> handler_pool_;

  std::vector<std::string> tos_event_key_prefix_blacklist_;

  UfsEventManagerMetrics metrics_;

  friend class UfsEventManagerMetrics;
};

}  // namespace dancenn
