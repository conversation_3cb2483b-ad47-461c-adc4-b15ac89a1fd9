//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Project
#include "base/threading.h"
#include "rocketmq/DefaultMQPushConsumer.h"

namespace dancenn {

class UfsEventManager;

class UfsEventRmqMessageListener
    : public rocketmq::MessageListenerConcurrently {
 public:
  UfsEventRmqMessageListener(UfsEventManager* mgr);

  rocketmq::ConsumeStatus consumeMessage(
      const std::vector<rocketmq::MQMessageExt>& msgs);

 private:
  UfsEventManager* mgr_{nullptr};
};

class UfsEventRmqConsumer : public ThreadPoolTask {
 public:
  UfsEventRmqConsumer(UfsEventManager* mgr,
                      const std::string& endpoint,
                      const std::string& topic,
                      const std::string& group_id,
                      const std::string& access_key,
                      const std::string& secret_key);
  ~UfsEventRmqConsumer() = default;

  bool operator()(void* /*arg*/ = nullptr) override;

 private:
  UfsEventManager* mgr_{nullptr};

  std::unique_ptr<rocketmq::DefaultMQPushConsumer> rmq_consumer_;
  std::unique_ptr<UfsEventRmqMessageListener> rmq_listener_;
};

}  // namespace dancenn
