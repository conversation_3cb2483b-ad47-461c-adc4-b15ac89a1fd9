//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/tos_ufs/tos_event.h"

// System
#include <sstream>

// Third
#include "nlohmann/json.hpp"

// Project
#include "base/time_util.h"

using json = nlohmann::json;

#define JSON_STR(obj, key) obj.contains(key) ? std::string(obj[key]) : ""
#define JSON_UINT64(obj, key) obj.contains(key) ? uint64_t(obj[key]) : 0

const char* kTosObjectCreated = "tos:ObjectCreated";
const char* kTosObjectRemoved = "tos:ObjectRemoved";
const char* kTosLifecycleExpiration = "tos:LifecycleExpiration";
const char* kTosObjectReplication = "tos:ObjectReplication";
const char* kEventTimeFormat = "%Y-%m-%dT%H:%M:%SZ";

namespace dancenn {

TosBucket::TosBucket(const std::string& trn,
                     const std::string& name,
                     const std::string& owner_identify)
    : trn_(trn), name_(name), owner_identify_(owner_identify) {
}

const std::string& TosBucket::trn() const {
  return trn_;
}

const std::string& TosBucket::name() const {
  return name_;
}

void TosBucket::set_name(const std::string& name) {
  name_ = name;
}

const std::string& TosBucket::owner_identify() const {
  return owner_identify_;
}

std::string TosBucket::ToJson() const {
  std::ostringstream oss;
  oss << "{\"trn\":\"" << trn_ << "\",\"name\":\"" << name_
      << "\",\"ownerIdentify\":\"" << owner_identify_ << "\"}";
  return oss.str();
}

TosObject::TosObject(const std::string& etag,
                     const std::string& key,
                     uint64_t size)
    : etag_(etag), key_(key), size_(size) {
}

const std::string& TosObject::etag() const {
  return etag_;
}

const std::string& TosObject::key() const {
  return key_;
}

void TosObject::set_key(const std::string& key) {
  key_ = key;
}

const uint64_t TosObject::size() const {
  return size_;
}

std::string TosObject::ToJson() const {
  std::ostringstream oss;
  oss << "{\"eTag\":\"\\\""
      << (etag_.size() >= 2 ? etag_.substr(1, etag_.size() - 2) : "")
      << "\\\"\",\"key\":\"" << key_ << "\",\"size\":" << size_ << "}";
  return oss.str();
}

RequestParameters::RequestParameters(const std::string& source_ip_address)
    : source_ip_address_(source_ip_address) {
}

const std::string& RequestParameters::source_ip_address() const {
  return source_ip_address_;
}

std::string RequestParameters::ToJson() const {
  std::ostringstream oss;
  oss << "{\"sourceIPAddress\":\"" << source_ip_address_ << "\"}";
  return oss.str();
}

ResponseElements::ResponseElements(const std::string& request_id)
    : request_id_(request_id) {
}

const std::string& ResponseElements::request_id() const {
  return request_id_;
}

std::string ResponseElements::ToJson() const {
  std::ostringstream oss;
  oss << "{\"requestId\":\"" << request_id_ << "\"}";
  return oss.str();
}

UserIdentity::UserIdentity(const std::string& principal_id)
    : principal_id_(principal_id) {
}

const std::string& UserIdentity::principal_id() const {
  return principal_id_;
}

std::string UserIdentity::ToJson() const {
  std::ostringstream oss;
  oss << "{\"principalId\":\"" << principal_id_ << "\"}";
  return oss.str();
}

Tos::Tos(const std::shared_ptr<TosBucket>& bucket,
         const std::shared_ptr<TosObject>& object,
         const std::string& tos_schema_version,
         const std::string& rule_id,
         const std::string& region,
         const std::shared_ptr<RequestParameters>& request_parameters,
         const std::shared_ptr<ResponseElements>& response_elements,
         const std::shared_ptr<UserIdentity>& user_identity)
    : bucket_(bucket),
      object_(object),
      tos_schema_version_(tos_schema_version),
      rule_id_(rule_id),
      region_(region),
      request_parameters_(request_parameters),
      response_elements_(response_elements),
      user_identity_(user_identity) {
}

const std::shared_ptr<TosBucket>& Tos::bucket() const {
  return bucket_;
}

const std::shared_ptr<TosObject>& Tos::object() const {
  return object_;
}

const std::string& Tos::tos_schema_version() const {
  return tos_schema_version_;
}

const std::string& Tos::rule_id() const {
  return rule_id_;
}

const std::string& Tos::region() const {
  return region_;
}

void Tos::set_region(const std::string& region) {
  region_ = region;
}

const std::shared_ptr<RequestParameters>& Tos::request_parameters() const {
  return request_parameters_;
}

const std::shared_ptr<ResponseElements>& Tos::response_elements() const {
  return response_elements_;
}

const std::shared_ptr<UserIdentity>& Tos::user_identity() const {
  return user_identity_;
}

std::string Tos::ToJson() const {
  std::ostringstream oss;
  oss << "{\"bucket\":" << (bucket_ != nullptr ? bucket_->ToJson() : "null")
      << ",\"object\":" << (object_ != nullptr ? object_->ToJson() : "null")
      << ",\"tosSchemaVersion\":\"" << tos_schema_version_ << "\",\"ruleId\":\""
      << rule_id_ << "\",\"region\":\"" << region_
      << "\",\"requestParameters\":"
      << (request_parameters_ != nullptr ? request_parameters_->ToJson()
                                         : "null")
      << ",\"responseElements\":"
      << (response_elements_ != nullptr ? response_elements_->ToJson() : "null")
      << ",\"userIdentity\":"
      << (user_identity_ != nullptr ? user_identity_->ToJson() : "null") << "}";
  return oss.str();
}

TosEvent::TosEvent(const std::string& event_name,
                   const std::string& event_source,
                   const std::string& event_time,
                   const std::string& event_version,
                   const std::shared_ptr<Tos>& tos)
    : event_name_(event_name),
      event_source_(event_source),
      event_time_(event_time),
      event_version_(event_version),
      tos_(tos) {
  event_type_ = TosEvent::EventTypeFromName(event_name_);
  event_ts_ = TimeUtil::GetEpochMsByFormat(event_time_, kEventTimeFormat);
}

const std::string& TosEvent::event_name() const {
  return event_name_;
}

void TosEvent::set_event_name(const std::string& event_name) {
  event_name_ = event_name;
  event_type_ = TosEvent::EventTypeFromName(event_name_);
}

const std::string& TosEvent::event_source() const {
  return event_source_;
}

const std::string& TosEvent::event_time() const {
  return event_time_;
}

const std::string& TosEvent::event_version() const {
  return event_version_;
}

const std::shared_ptr<Tos>& TosEvent::tos() const {
  return tos_;
}

const TosEvent::EventType& TosEvent::event_type() const {
  return event_type_;
}

const uint64_t& TosEvent::event_ts() const {
  return event_ts_;
}

std::string TosEvent::ToJson() const {
  std::ostringstream oss;
  oss << "{\"eventName\":\"" << event_name_ << "\",\"eventSource\":\""
      << event_source_ << "\",\"eventTime\":\"" << event_time_
      << "\",\"eventVersion\":\"" << event_version_
      << "\",\"tos\":" << (tos_ != nullptr ? tos_->ToJson() : "null") << "}";
  return oss.str();
}

bool TosEvent::ParseFromMessage(
    const char* msg,
    std::vector<std::shared_ptr<TosEvent>>* events) {
  auto j = json::parse(msg, nullptr, false);
  if (j.is_discarded()) {
    return false;  // Invalid json format
  }

  auto j_events = j["events"];
  for (auto j_event : j_events) {
    auto j_tos = j_event["tos"];
    auto j_bucket = j_tos["bucket"];
    auto j_object = j_tos["object"];

    auto bucket =
        std::make_shared<TosBucket>(JSON_STR(j_bucket, "trn"),
                                    JSON_STR(j_bucket, "name"),
                                    JSON_STR(j_bucket, "ownerIdentify"));
    auto object = std::make_shared<TosObject>(JSON_STR(j_object, "eTag"),
                                              JSON_STR(j_object, "key"),
                                              JSON_UINT64(j_object, "size"));
    auto req_params = std::make_shared<RequestParameters>(
        j_tos["requestParameters"]["sourceIPAddress"]);
    auto rsp_elems = std::make_shared<ResponseElements>(
        j_tos["responseElements"]["requestId"]);
    auto user_id =
        std::make_shared<UserIdentity>(j_tos["userIdentity"]["principalId"]);

    auto tos = std::make_shared<Tos>(bucket,
                                     object,
                                     JSON_STR(j_tos, "tosSchemaVersion"),
                                     JSON_STR(j_tos, "ruleId"),
                                     JSON_STR(j_tos, "region"),
                                     req_params,
                                     rsp_elems,
                                     user_id);

    auto tos_event =
        std::make_shared<TosEvent>(JSON_STR(j_event, "eventName"),
                                   JSON_STR(j_event, "eventSource"),
                                   JSON_STR(j_event, "eventTime"),
                                   JSON_STR(j_event, "eventVersion"),
                                   tos);

    events->emplace_back(tos_event);
  }

  return true;
}

TosEvent::EventType TosEvent::EventTypeFromName(const std::string& name) {
  if (name.compare(0,
                   strlen(kTosObjectCreated),
                   kTosObjectCreated,
                   0,
                   strlen(kTosObjectCreated)) == 0) {
    return TosEvent::EventType::OBJECT_CREATED;
  } else if (name.compare(0,
                          strlen(kTosObjectRemoved),
                          kTosObjectRemoved,
                          0,
                          strlen(kTosObjectRemoved)) == 0) {
    return TosEvent::EventType::OBJECT_REMOVED;
  } else if (name.compare(0,
                          strlen(kTosLifecycleExpiration),
                          kTosLifecycleExpiration,
                          0,
                          strlen(kTosLifecycleExpiration)) == 0) {
    return TosEvent::EventType::LIFECYCLE_EXPIRATION;
  } else if (name.compare(0,
                          strlen(kTosObjectReplication),
                          kTosObjectReplication,
                          0,
                          strlen(kTosObjectReplication)) == 0) {
    return TosEvent::EventType::OBJECT_REPLICATION;
  } else {
    return TosEvent::EventType::INVALID;
  }
}

}  // namespace dancenn
