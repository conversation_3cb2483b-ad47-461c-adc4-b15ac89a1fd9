#include "ufs/tos_ufs/tos_ufs_env.h"

#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/Delete.h>
#include <aws/s3/model/DeleteObjectsRequest.h>
#include <aws/s3/model/ObjectIdentifier.h>

#include "hdfs.pb.h"
#include "ufs/tos/tos_checksum_utils.h"
#include "ufs/tos/tos_client.h"
#include "ufs/tos/tos_constant.h"
#include "ufs/tos/tos_cred.h"
#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/tos_ufs/tos_ufs.h"
#include "ufs/ufs_auth_conf.h"

DECLARE_string(tos_endpoint);
DECLARE_string(tos_region);
DECLARE_string(tos_bucket);
DECLARE_string(tos_prefix);
DECLARE_bool(force_check_tos_endpoint);

DECLARE_bool(tos_sse_enabled);
DECLARE_uint64(tos_suffix_salt);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_bool(use_fixed_ak);

namespace dancenn {

namespace {
const char* kBlockSuffix = ".block";
const char* kCrcSuffix = ".crc";
}  // namespace

static bool ParseTosConfig(const UfsConfig& ufs_conf, TosConfig* config) {
  auto&& params = ufs_conf.params;
  auto itor = params.find(TosConstants::kConfigEndpoint);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << TosConstants::kConfigEndpoint;
    return false;
  }
  auto&& endpoint = itor->second;

  itor = params.find(TosConstants::kConfigRegion);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << TosConstants::kConfigRegion;
    return false;
  }
  auto&& region = itor->second;

  itor = params.find(TosConstants::kConfigBucket);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << TosConstants::kConfigBucket;
    return false;
  }
  auto&& bucket = itor->second;

  itor = params.find(TosConstants::kConfigPrefix);
  if (itor == params.end()) {
    LOG(ERROR) << "Key not found in params: " << TosConstants::kConfigPrefix;
    return false;
  }
  auto&& prefix = itor->second;

  *config = TosConfig(endpoint, region, bucket, prefix);
  return true;
}

TosUfsEnv::TosUfsEnv() {
  tos_info_.endpoint = FLAGS_tos_endpoint;
  tos_info_.region = FLAGS_tos_region;
  tos_info_.bucket = FLAGS_tos_bucket;
  tos_info_.prefix = FLAGS_tos_prefix;

  cred_keeper_ = std::make_shared<dancenn::TosCredKeeper>();

  // tos client construct depend it
  auto cred_s = cred_keeper_->Start();
  if (!cred_s.IsOK()) {
    LOG(FATAL) << "Failed to start TosCredKeeper. error: " << cred_s.ToString();
  }
}

Status TosUfsEnv::Start() {
  return UfsEnv::Start();
}

void TosUfsEnv::Stop() {
  UfsEnv::Stop();

  if (cred_keeper_) {
    cred_keeper_->Stop();
    cred_keeper_.reset();
  }
}

UfsConfig TosUfsEnv::CreateUfsConfig() {
  UfsConfig ufs_config;
  ufs_config.type = UfsType::UFS_TYPE_TOS;
  ufs_config.ufs_prefix = "/" + FLAGS_tos_prefix;
  ufs_config.params[TosConstants::kConfigBucket] = FLAGS_tos_bucket;
  ufs_config.params[TosConstants::kConfigPrefix] = FLAGS_tos_prefix;
  ufs_config.params[TosConstants::kConfigEndpoint] = FLAGS_tos_endpoint;
  ufs_config.params[TosConstants::kConfigRegion] = FLAGS_tos_region;
  return ufs_config;
}

std::shared_ptr<Ufs> TosUfsEnv::CreateUfs(const UfsConfig& c) {
  CHECK(c.type == UFS_TYPE_TOS || c.type == UFS_TYPE_TEST)
      << "Only TOS is supported for now";

  TosConfig config;
  if (!ParseTosConfig(c, &config)) {
    LOG(ERROR) << "CreateUfs Failed, ParseTosConfig Failed";
    return nullptr;
  }

  std::shared_ptr<TosCredentialProvider> cred_provider;
  if (cred_keeper_->UseFixedAK()) {
    cred_provider = std::shared_ptr<TosCredentialProvider>(
        new StaticTosCredentialProvider(cred_keeper_));
  } else {
    cred_provider = std::shared_ptr<TosCredentialProvider>(
        new AssumeRoleTosCredentialProvider(cred_keeper_));
  }

  return std::shared_ptr<Ufs>(
      new TosUfs(c, std::move(config), std::move(cred_provider)));
}

void TosUfsEnv::CheckPersistentUfsInfo(
    PersistentUfsInfo* stored_ufs_info) const {
  CHECK_NOTNULL(stored_ufs_info);

  CHECK_EQ(stored_ufs_info->protocol, PersistentUfsProtocol::kTos);

  if (FLAGS_force_check_tos_endpoint) {
    CHECK_EQ(stored_ufs_info->tos_info.endpoint, this->tos_info_.endpoint);
  } else {
    if (stored_ufs_info->tos_info.endpoint != this->tos_info_.endpoint) {
      LOG(WARNING) << "Update tos_endpoint."
                   << " from=" << stored_ufs_info->tos_info.endpoint
                   << " to=" << FLAGS_tos_endpoint;
      stored_ufs_info->tos_info.endpoint = FLAGS_tos_endpoint;
    }
  }
  CHECK_EQ(stored_ufs_info->tos_info.region, this->tos_info_.region);
  CHECK_EQ(stored_ufs_info->tos_info.bucket, this->tos_info_.bucket);
  CHECK_EQ(stored_ufs_info->tos_info.prefix, this->tos_info_.prefix);
}

bool TosUfsEnv::GetUfsInfo(cloudfs::RemoteBlockInfoProto* info,
                           bool for_internal_use) const {
  info->set_type(cloudfs::RemoteBlockInfoProto::TOS);
  if (false) {
    // Under LOCAL mode, this->tos_info_ is kept uninitialized,
    // message: optional TOSInfoProto tosInfo = 1000;
    // in this case, protobuf would generate an empty key 1000 with an empty
    // structure, the client would treat the response as unparseable.
    info->mutable_tos_info()->set_endpoint("tos://dummy");
    info->mutable_tos_info()->set_bucket("dummy");
    info->mutable_tos_info()->set_region("dummy");
    info->mutable_tos_info()->set_prefix("");
    LOG(WARNING) << "Protocol of persistent ufs isn't tos: "
                 << static_cast<int>(-1);
    return false;
  }

  return DumpToProtoInternal(
      info->mutable_tos_info(), UfsAuthConf::IsFixedAK(), for_internal_use);
}

void TosUfsEnv::DumpUfsInfoToFlags(PersistentFlags* persistent_flags) const {
  CHECK_NOTNULL(persistent_flags);
  if (persistent_flags->has_tos_suffix_salt()) {
    CHECK_EQ(persistent_flags->tos_suffix_salt(), FLAGS_tos_suffix_salt)
        << "tos_suffix_salt";
  } else {
    persistent_flags->set_tos_suffix_salt(FLAGS_tos_suffix_salt);
  }

  if (persistent_flags->has_tos_sse_enabled()) {
    CHECK_EQ(persistent_flags->tos_sse_enabled(), FLAGS_tos_sse_enabled)
        << "tos_sse_enabled";
  } else {
    persistent_flags->set_tos_sse_enabled(FLAGS_tos_sse_enabled);
  }
}

bool TosUfsEnv::DumpToProtoInternal(cloudfs::TOSInfoProto* info,
                                    bool use_fixed_ak,
                                    bool for_internal_use) const {
  info->set_endpoint(tos_info_.endpoint);
  info->set_bucket(tos_info_.bucket);
  info->set_region(tos_info_.region);
  info->set_prefix(tos_info_.prefix);
  if (for_internal_use) {
    // for DN
    if (use_fixed_ak) {
      this->WriteInAkSk(info);
    } else {
      this->WriteInInternalUseAkSkToken(info);
    }
  } else {
    // for Client
    if (use_fixed_ak) {
      LOG(ERROR) << "[HDFS Mode Without DN] needs FLAGS_use_fixed_ak be false";
      return false;
    } else {
      this->WriteInExternalUseAkSkToken(info);
    }
  }
  info->set_enableserversideencryption(FLAGS_tos_sse_enabled);
  info->set_suffixsalt(FLAGS_tos_suffix_salt);
  return true;
}

void TosUfsEnv::WriteInInternalUseAkSkToken(cloudfs::TOSInfoProto* info) const {
  auto inner = cred_keeper_->InnerCredential();
  info->set_accesskey(inner->ak);
  info->set_secretkey(inner->sk);
  info->set_ststoken(inner->token);
};

void TosUfsEnv::WriteInExternalUseAkSkToken(cloudfs::TOSInfoProto* info) const {
  auto external = cred_keeper_->InnerCredential();
  if (external == nullptr || external->ak.empty() || external->sk.empty() ||
      external->token.empty()) {
    LOG(ERROR) << "Empty external-use ak/sk";
  }
  info->set_accesskey(external->ak);
  info->set_secretkey(external->sk);
  info->set_ststoken(external->token);
};

void TosUfsEnv::WriteInAkSk(cloudfs::TOSInfoProto* info) const {
  auto inner = cred_keeper_->InnerCredential();
  info->set_accesskey(inner->ak);
  info->set_secretkey(inner->sk);
};

std::shared_ptr<Aws::S3::S3Client> TosUfsEnv::CreateAwsS3Client() const {
  // NOTICE: Please call Aws::InitAPI before!
  DLOG(INFO) << "tos endpoint: " << FLAGS_tos_endpoint;
  auto inner = cred_keeper_->InnerCredential();
  Aws::Auth::AWSCredentials credentials(inner->ak, inner->sk, inner->token);
  Aws::Client::ClientConfiguration clientCfg;
  clientCfg.endpointOverride = FLAGS_tos_endpoint;
  clientCfg.region = FLAGS_tos_region;
  return std::make_shared<Aws::S3::S3Client>(credentials, clientCfg);
}

Status TosUfsEnv::GetBlockLengthFromUfs(const std::string& ufs_filename,
                                        int64_t* new_length) const {
  CHECK(new_length);

  auto client = this->CreateAwsS3Client();
  return ObjectStorageChecksumUtils::GetBlockLengthFromTos(
      client.get(), FLAGS_tos_bucket, ufs_filename, new_length);
}

Status TosUfsEnv::DeleteBlocks(
    const cloudfs::datanode::HeartbeatResponseProto& heartbeat_resp) {
  // HDFS Mode Only

  const bool should_delete_tos =
      (FLAGS_namespace_type == cloudfs::NamespaceType::TOS_LOCAL ||
       FLAGS_namespace_type == cloudfs::NamespaceType::TOS_MANAGED);

  if (!should_delete_tos) {
    return Status();
  }

  Aws::S3::Model::Delete objs_to_delete;
  for (const auto& cmd : heartbeat_resp.cmds()) {
    if (!(cmd.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::InvalidatePufsCommand &&
          cmd.has_invalidatepufscmd())) {
      continue;
    }
    const auto& delete_cmd = cmd.invalidatepufscmd();

    for (const auto& block : delete_cmd.blocks()) {
      Aws::S3::Model::ObjectIdentifier id;
      std::string block_key = block.blockpufsname();
      std::string crc_key(block_key);
      id.SetKey(block_key);
      objs_to_delete.AddObjects(std::move(id));

      // If this block doesn't have crc file, delete would still successful,
      // because aws delete is mark delete.
      // You can use awscli to test: delete a object that no exist.
      // aws-cli delete ref:
      // https://awscli.amazonaws.com/v2/documentation/api/2.0.34/reference/s3api/delete-object.html
      Aws::S3::Model::ObjectIdentifier crc_id;
      crc_key.replace(
          crc_key.rfind(kBlockSuffix), sizeof(kBlockSuffix), kCrcSuffix);
      crc_id.SetKey(crc_key);
      objs_to_delete.AddObjects(std::move(crc_id));
    }
  }

  LOG(INFO) << "Send delete objects request, size="
            << objs_to_delete.GetObjects().size();
  // https://bytedance.feishu.cn/docs/doccnnXSEyReUfCJPOeSoKFRXUb
  // The request contains a list of up to 1000 keys
  // that you want to delete.
  Aws::S3::Model::DeleteObjectsRequest request;
  request.SetBucket(FLAGS_tos_bucket);
  request.SetDelete(std::move(objs_to_delete));
  // TODO(ruanjunbin): Why we create aws s3 client every time?
  // Because we need to change sts token. But it is not efficient.
  auto client = CreateAwsS3Client();
  if (!client) {
    auto msg = "Cannot create aws s3 client";
    LOG(ERROR) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }

  Aws::S3::Model::DeleteObjectsOutcome response =
      client->DeleteObjects(request);
  if (response.IsSuccess()) {
    DLOG(INFO) << "Delete objects succeed";
    return Status();
  } else {
    const auto& error = response.GetError();
    LOG(ERROR) << "Delete objects failed, msg: " << error.GetMessage()
               << ", exception: "
               << error.GetExceptionName()
               // aws/core/http/HttpResponse.h
               << ", code: " << static_cast<int>(error.GetResponseCode());
    return Status(JavaExceptions::kIOException, "Delete objects failed");
  }
}

}  // namespace dancenn