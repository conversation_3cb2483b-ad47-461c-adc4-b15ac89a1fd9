#pragma once

#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>

#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/ufs_env.h"

namespace dancenn {

class UfsEnv;
class UfsConfig;
class TosCredKeeper;

class TosUfsEnv : public UfsEnv {
 public:
  TosUfsEnv();
  ~TosUfsEnv() = default;

  Status Start() override;
  void Stop() override;

  UfsConfig CreateUfsConfig() override;

  std::shared_ptr<Ufs> CreateUfs(const UfsConfig& c) override;

  void CheckPersistentUfsInfo(
      PersistentUfsInfo* stored_ufs_info) const override;

  bool GetUfsInfo(cloudfs::RemoteBlockInfoProto* info,
                  bool for_internal_use) const override;

  void DumpUfsInfoToFlags(PersistentFlags* persistent_flags) const override;

  Status GetBlockLengthFromUfs(const std::string& ufs_filename,
                               int64_t* new_length) const override;

  Status DeleteBlocks(
      const cloudfs::datanode::HeartbeatResponseProto& heartbeat_resp) override;

 public:
  std::shared_ptr<TosCredKeeper> cred_keeper() {
    return cred_keeper_;
  }

  std::shared_ptr<Aws::S3::S3Client> CreateAwsS3Client() const;

 private:
  // https://docs.aws.amazon.com/general/latest/gr/glos-chap.html#accesskeyID
  // Access key is the combination of an access key ID (like
  // AKIAIOSFODNN7EXAMPLE) and a secret access key (like
  // wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY).
  bool DumpToProtoInternal(cloudfs::TOSInfoProto* info,
                           bool use_fixed_ak,
                           bool for_internal_use) const;
  void WriteInInternalUseAkSkToken(cloudfs::TOSInfoProto* info) const;
  void WriteInExternalUseAkSkToken(cloudfs::TOSInfoProto* info) const;
  void WriteInAkSk(cloudfs::TOSInfoProto* info) const;

  std::shared_ptr<TosCredKeeper> cred_keeper_;

  TosInfo tos_info_;
};

}  // namespace dancenn
