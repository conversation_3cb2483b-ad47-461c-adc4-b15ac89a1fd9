//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include "base/threading.h"

namespace dancenn {

// Deprecated
class UploadPartCopyTask : public ThreadPoolTask {
 public:
  UploadPartCopyTask();
  virtual ~UploadPartCopyTask() = default;

  bool operator()(void* arg = nullptr) override;

 private:
  std::string dst_key;
  std::string src_key;
  uint64_t src_copy_offset;
  uint64_t src_copy_len;
  std::string upload_id;
  int part_num;
};

}  // namespace dancenn
