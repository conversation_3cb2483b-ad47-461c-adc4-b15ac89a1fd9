//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <memory>
#include <string>
#include <vector>

namespace dancenn {

class TosBucket {
 public:
  TosBucket(const std::string& trn,
            const std::string& name,
            const std::string& owner_identify);

  const std::string& trn() const;
  const std::string& name() const;
  void set_name(const std::string& name);
  const std::string& owner_identify() const;

  std::string ToJson() const;

 private:
  std::string trn_;
  std::string name_;
  std::string owner_identify_;
};

class TosObject {
 public:
  TosObject(const std::string& etag, const std::string& key, uint64_t size);

  const std::string& etag() const;
  const std::string& key() const;
  void set_key(const std::string& key);
  const uint64_t size() const;

  std::string ToJson() const;

 private:
  std::string etag_;
  std::string key_;
  uint64_t size_;
};

class RequestParameters {
 public:
  RequestParameters(const std::string& source_ip_address);

  const std::string& source_ip_address() const;

  std::string ToJson() const;

 private:
  std::string source_ip_address_;
};

class ResponseElements {
 public:
  ResponseElements(const std::string& request_id);

  const std::string& request_id() const;

  std::string ToJson() const;

 private:
  std::string request_id_;
};

class UserIdentity {
 public:
  UserIdentity(const std::string& principal_id);

  const std::string& principal_id() const;

  std::string ToJson() const;

 private:
  std::string principal_id_;
};

class Tos {
 public:
  Tos(const std::shared_ptr<TosBucket>& bucket,
      const std::shared_ptr<TosObject>& object,
      const std::string& tos_schema_version,
      const std::string& rule_id,
      const std::string& region,
      const std::shared_ptr<RequestParameters>& request_parameters,
      const std::shared_ptr<ResponseElements>& response_elements,
      const std::shared_ptr<UserIdentity>& user_identity);

  const std::shared_ptr<TosBucket>& bucket() const;
  const std::shared_ptr<TosObject>& object() const;
  const std::string& tos_schema_version() const;
  const std::string& rule_id() const;
  const std::string& region() const;
  void set_region(const std::string& region);
  const std::shared_ptr<RequestParameters>& request_parameters() const;
  const std::shared_ptr<ResponseElements>& response_elements() const;
  const std::shared_ptr<UserIdentity>& user_identity() const;

  std::string ToJson() const;

 private:
  std::shared_ptr<TosBucket> bucket_;
  std::shared_ptr<TosObject> object_;
  std::string tos_schema_version_;
  std::string rule_id_;
  std::string region_;
  std::shared_ptr<RequestParameters> request_parameters_;
  std::shared_ptr<ResponseElements> response_elements_;
  std::shared_ptr<UserIdentity> user_identity_;
};

class TosEvent {
 public:
  enum class EventType {
    OBJECT_CREATED = 1,
    OBJECT_REMOVED,
    LIFECYCLE_EXPIRATION,
    OBJECT_REPLICATION,

    INVALID
  };

  TosEvent(const std::string& event_name,
           const std::string& event_source,
           const std::string& event_time,
           const std::string& event_version,
           const std::shared_ptr<Tos>& tos);

  const std::string& event_name() const;
  void set_event_name(const std::string& event_name);
  const std::string& event_source() const;
  const std::string& event_time() const;
  const std::string& event_version() const;
  const std::shared_ptr<Tos>& tos() const;

  const EventType& event_type() const;
  const uint64_t& event_ts() const;

  std::string ToJson() const;

  static bool ParseFromMessage(const char* msg,
                               std::vector<std::shared_ptr<TosEvent>>* events);

 private:
  static EventType EventTypeFromName(const std::string& name);

  std::string event_name_;
  std::string event_source_;
  std::string event_time_;
  std::string event_version_;
  std::shared_ptr<Tos> tos_;

  EventType event_type_;
  uint64_t event_ts_;

  friend class TosEventTest;
};

}  // namespace dancenn

namespace std {

template <>
struct hash<dancenn::TosEvent::EventType> {
  size_t operator()(const dancenn::TosEvent::EventType& e) const {
    return static_cast<size_t>(e);
  }
};

}  // namespace std
