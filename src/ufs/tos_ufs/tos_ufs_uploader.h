#pragma once

#include "ufs/upload/ufs_uploader.h"

namespace dancenn {

// TOS Uploader
class TosUfsUploader : public UfsUploader {
 public:
  TosUfsUploader(NameSpace* ns, Ufs* ufs);
  ~TosUfsUploader() override;

  Status TriggerUploadObject(FileSyncContext* f_ctx) override;

  Status PersistObject(FileSyncContext* f_ctx) override;

  Status AbortUpload(const std::string& upload_id,
                     const std::string& ufs_key) override;

 private:
  Status PersistS3Object(FileSyncContext* f_ctx);
  Status PersistS3ObjectAppend(INode* inode, uint64_t size);
  // will update inode.update_info
  Status PersistS3ObjectNormal(const std::string& ufs_path,
                               const std::string& inner_path,
                               INode* inode,
                               uint64_t size,
                               bool check_upload_id);

  Status TriggerUploadS3Object(FileSyncContext* f_ctx);
  // will update inode.update_info
  Status TriggerUploadS3ObjectNormal(const std::string& ufs_path,
                                     const std::string& inner_path,
                                     INode* inode);
  Status TriggerUploadS3ObjectAppend(const std::string& ufs_path,
                                     const std::string& inner_path,
                                     INode* inode);
};

}  // namespace dancenn