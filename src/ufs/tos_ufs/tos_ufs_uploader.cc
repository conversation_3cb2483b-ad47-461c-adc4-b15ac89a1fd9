#include "ufs/tos_ufs/tos_ufs_uploader.h"

DECLARE_bool(tos_ufs_mkdir_create_empty_object);
DECLARE_bool(write_back_always_check_ufs);
DECLARE_bool(log_ufs_persist_detail);

namespace dancenn {

TosUfsUploader::TosUfsUploader(NameSpace* ns, Ufs* ufs) : UfsUploader(ns, ufs) {
}

TosUfsUploader::~TosUfsUploader() {
}

Status TosUfsUploader::TriggerUploadS3ObjectAppend(
    const std::string& ufs_path,
    const std::string& inner_path,
    INode* inode) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeAppend);

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectAppend"
      << " ufs_path=" << ufs_path << " inner_path=" << inner_path
      << " inode=" << inode->ShortDebugString();

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectAppend old_path=" << old_info.path;

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // pass, append mode not need upload_id
  }

  // check path
  if (old_path != new_path) {
    // pass, append mode not need (?)
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectAppend trigger block upload";

  const auto& upload_id = inode->ufs_file_info().upload_id();
  const auto& pufs_name = inode->ufs_file_info().key();
  for (auto& b : inode->blocks()) {
    BlockInfoProto bip;
    if (!ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip)) {
      LOG(INFO) << "Failed to get AccInfo for block " << b.blockid();
      return Status(Code::kError, "Block not found");
    }

    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadS3ObjectAppend check block one by one"
        << " block_id=" << b.blockid() << " bip=" << bip.ShortDebugString();

    if (bip.state() == BlockInfoProto::kPersisted &&
        BlockManager::IsBlockReadyToPersisted(bip, pufs_name, upload_id)) {
      continue;
    } else {
      VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
          << "TriggerUploadS3ObjectAppend, Trigger";

      ns_->block_manager()->TriggerUploadChooseDN(b, upload_id, pufs_name);
      break;
    }
  }

  return Status();
}

Status TosUfsUploader::TriggerUploadS3ObjectNormal(
    const std::string& ufs_path,
    const std::string& inner_path,
    INode* inode) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeNormal);

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectNormal"
      << " ufs_path=" << ufs_path << " inner_path=" << inner_path
      << " inode=" << inode->ShortDebugString();

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectNormal old_path=" << old_info.path;

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // MPU not created yet
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "UploadId is empty, inode " << inode->id() << " old path "
        << old_path << " new path " << new_path;

    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // check path
  if (old_path != new_path) {
    // Path has changed during persist
    // Will re-upload all blocks
    LOG(INFO) << "Path changed during persist, inode " << inode->id()
              << " old path " << old_path << " new path " << new_path;

    // Abort old multipart upload
    // This may be done multiple time during failover
    // Do not check success
    Status ufs_status;
    ufs_status = ufs_->AbortCreate(
        old_path, inode->ufs_file_info(), UfsAbortCreateOption(true));
    if (!ufs_status.IsOK()) {
      LOG(INFO) << "Abort MPU failed path " << old_path << " status "
                << ufs_status.ToString();
    }

    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // action
  // throttle
  //  if (!ufs_->CheckUfsFileState(new_path, inode->ufs_file_info()).IsOK()) {
  //    LOG(WARNING) << "Check UploadId failed, inode " << inode->id() << " path
  //    "
  //                 << new_path;
  //    MFC(LoggerMetrics::Instance().warn_)->Inc();
  //    auto s =
  //        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_,
  //        inode);
  //    if (!s.IsOK()) {
  //      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
  //    } else {
  //      return s;
  //    }
  //  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3ObjectNormal trigger block upload";

  const auto& upload_id = inode->ufs_file_info().upload_id();
  const auto& pufs_name = inode->ufs_file_info().key();
  for (auto& b : inode->blocks()) {
    BlockInfoProto bip;
    if (!ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip)) {
      LOG(INFO) << "Failed to get AccInfo for block " << b.blockid();
      return Status(Code::kError, "Block not found");
    }

    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadS3ObjectNormal check block one by one"
        << " block_id=" << b.blockid() << " bip=" << bip.ShortDebugString();

    if (bip.state() == BlockInfoProto::kPersisted &&
        BlockManager::IsBlockReadyToPersisted(bip, pufs_name, upload_id)) {
      // skip persisted and consist bip
      continue;
    }

    // need to trigger upload
    ns_->block_manager()->TriggerUploadChooseDN(b, upload_id, pufs_name);
  }
  return Status();
}

Status TosUfsUploader::TriggerUploadS3Object(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadS3Object, inode=" << inode.ShortDebugString();

  uint64_t size = 0;
  for (auto& b : inode.blocks()) {
    size += b.numbytes();
  }

  if (size == 0) {
    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadS3Object, size == 0";

    return Status();
  }

  ns_->ufs_uploading_mgr()->add_upload_ongoing_inode(inode.id());

  Status s;
  switch (inode.ufs_file_info().create_type()) {
    case kUfsFileCreateTypeAppend:
      s = TriggerUploadS3ObjectAppend(
          f_ctx->ufs_path, f_ctx->inner_path, &inode);
      break;
    case kUfsFileCreateTypeNormal:
      s = TriggerUploadS3ObjectNormal(
          f_ctx->ufs_path, f_ctx->inner_path, &inode);
      break;
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }

  return s;
}

Status TosUfsUploader::TriggerUploadObject(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadObject inode " << inode.id() << " state "
      << inode.status();

  switch (inode.ufs_file_info().file_state()) {
    case UfsFileState::kUfsFileStateToBePersisted: {
      // Not persisted yet
      return TriggerUploadS3Object(f_ctx);
    }
    case UfsFileState::kUfsFileStatePersisted: {
      // When WriteBackManager under high pressure, it is possible to get here
      // Consider add MetaScannerV2 worker count (need restart)
      // dfs_meta_scanner_v2_worker_num
      LOG(INFO) << "File already persisted " << inode.ShortDebugString();
      return Status::OK();
    }
    case UfsFileState::kUfsFileStateLocal: {
      LOG(WARNING) << "Local file do not need persist, should not get here "
                   << inode.ShortDebugString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return Status::OK();
    }
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }
}

Status TosUfsUploader::PersistS3ObjectAppend(INode* inode,
                                             uint64_t size) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeAppend);

  if (inode->status() != INode_Status_kFileComplete) {
    return Status(Code::kFileStatusError, "File not completed");
  }

  if (size == 0) {
    // Empty append object, do nothing
    return Status::OK();
  }

  BlockInfoProto bip;
  if (!ns_->meta_storage()->GetBlockInfo(
          inode->blocks(inode->blocks_size() - 1).blockid(), &bip)) {
    LOG(INFO) << "Failed to get AccInfo for block "
              << inode->ShortDebugString();
    return Status(Code::kError, "Block not found");
  }

  if (bip.state() != BlockInfoProto_Status::BlockInfoProto_Status_kPersisted) {
    return Status(Code::kError, "Block not persisted");
  }

  inode->mutable_ufs_file_info()->set_etag(bip.etag());

  return Status::OK();
}

Status TosUfsUploader::PersistS3ObjectNormal(const std::string& ufs_path,
                                             const std::string& inner_path,
                                             INode* inode,
                                             uint64_t size,
                                             bool check_upload_id) {
  CHECK_NOTNULL(inode);
  CHECK(inode->ufs_file_info().create_type() == kUfsFileCreateTypeNormal);

  UfsIdentifierInfo old_info;
  ufs_->GetUfsInfo(inode->ufs_file_info().key(), &old_info);
  // old_path, new_path are ufs_path; use f_ctx->inner_path for inner_path
  const std::string& old_path = old_info.path;
  const std::string& new_path = ufs_path;

  if (size == 0) {
    // empty file
    if (inode->status() != INode_Status_kFileComplete) {
      return Status(Code::kFileStatusError, "File not completed");
    }

    if (!FLAGS_tos_ufs_mkdir_create_empty_object) {
      return Status(JavaExceptions::kIOException,
                    "not allow upload empty object");
    }

    auto s = ufs_->Create(
        new_path, UfsCreateOption(true, false), inode->mutable_ufs_file_info());
    if (!s.IsOK()) {
      LOG(INFO) << "Create empty object in ufs failed " << s.ToString();
    }
    return s;
  }

  // MultiPart Upload
  // check upload id
  if (inode->ufs_file_info().upload_id().empty()) {
    // MPU not created yet
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "UploadId is empty, inode " << inode->id() << " old path "
        << old_path << " new path " << new_path;

    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // check path
  if (old_path != new_path) {
    // Path has changed during persist
    // Will re-upload all blocks
    LOG(INFO) << "Path changed during persist, inode " << inode->id()
              << " old path " << old_path << " new path " << new_path;

    // Abort old multipart upload
    // This may be done multiple time during failover
    // Do not check success
    Status ufs_status;
    ufs_status = ufs_->AbortCreate(
        old_path, inode->ufs_file_info(), UfsAbortCreateOption(true));
    if (!ufs_status.IsOK()) {
      LOG(INFO) << "Abort MPU failed path " << old_path << " status "
                << ufs_status.ToString();
    }

    auto s =
        ns_->PersistUfsFileUpdateUploadInfo(inner_path, new_path, ufs_, inode);
    if (s.IsOK()) {
      return Status(Code::kIsRetry, "Recreate upload id, need retry.");
    } else {
      return s;
    }
  }

  // action
  if (FLAGS_write_back_always_check_ufs || check_upload_id) {
    if (!ufs_->CheckUfsFileState(new_path, inode->ufs_file_info()).IsOK()) {
      LOG(WARNING) << "Check UploadId failed, inode " << inode->id() << " path "
                   << new_path;
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      auto s = ns_->PersistUfsFileUpdateUploadInfo(
          inner_path, new_path, ufs_, inode);
      if (s.IsOK()) {
        return Status(Code::kIsRetry, "Recreate upload id, need retry.");
      } else {
        return s;
      }
    }
  }

  std::map<int, std::string> parts;
  bool allBlocksUploaded = true;
  auto s =
      ns_->CheckAllBlockPersisted(*inode, &parts, &allBlocksUploaded, ufs_);
  if (!s.IsOK()) {
    return s;
  }

  if (inode->status() != INode_Status_kFileComplete) {
    return Status(Code::kFileStatusError, "File not completed");
  }

  if (allBlocksUploaded) {
    auto s =
        ufs_->CompleteFile(new_path, parts, inode->mutable_ufs_file_info(), size);
    if (s.IsOK()) {
      inode->mutable_ufs_file_info()->clear_upload_id();
      inode->mutable_ufs_file_info()->clear_key();
    }
    return s;
  } else {
    return Status(Code::kUfsUploadNotReady, "Not all blocks persisted");
  }
}

Status TosUfsUploader::PersistS3Object(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  INode old_inode = inode;

  uint64_t size = 0;
  for (auto& b : inode.blocks()) {
    size += b.numbytes();
  }

  // Step 1: check block is ready to persisted
  Status s;
  switch (inode.ufs_file_info().create_type()) {
    case kUfsFileCreateTypeAppend:
      s = PersistS3ObjectAppend(&inode, size);
      break;
    case kUfsFileCreateTypeNormal:
      s = PersistS3ObjectNormal(f_ctx->ufs_path,
                                f_ctx->inner_path,
                                &inode,
                                size,
                                f_ctx->check_upload_id);
      break;
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }

  if (!s.IsOK()) {
    return s;
  }

  // Step 2: write inode
  UfsFileInfoProto* ufs_file_info = inode.mutable_ufs_file_info();
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  ufs_file_info->set_file_state(UfsFileState::kUfsFileStatePersisted);
  ufs_file_info->set_sync_ts(now_ts);
  ufs_file_info->set_last_modified_ts(now_ts);
  ufs_file_info->set_size(size);

  // TODO(zhuangsiyu): clear pufsname and upload id in all bips
  // Step 3: make all block persisted
  for (auto& b : inode.blocks()) {
    // non-key block may have no chance to persist, do it before persist file.
    PersistNonKeyBlock(inode, b.blockid());
  }

  // Step 4: write inode
  return PersistCfsFile(f_ctx->inner_path, f_ctx->parent, inode, old_inode);
}

Status TosUfsUploader::PersistObject(FileSyncContext* f_ctx) {
  INode& inode = f_ctx->iip.MutableInodeUnsafe();
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "PersistUfsFile inode " << inode.id() << " state " << inode.status();

  switch (inode.ufs_file_info().file_state()) {
    case UfsFileState::kUfsFileStateToBePersisted: {
      // Not persisted yet
      ns_->ufs_uploading_mgr()->add_upload_ongoing_inode(inode.id());

      return PersistS3Object(f_ctx);
    }
    case UfsFileState::kUfsFileStatePersisted: {
      // When WriteBackManager under high pressure, it is possible to get here
      // Consider add MetaScannerV2 worker count (need restart)
      // dfs_meta_scanner_v2_worker_num
      LOG(INFO) << "File already persisted " << inode.ShortDebugString();
      return Status::OK();
    }
    case UfsFileState::kUfsFileStateLocal: {
      LOG(WARNING) << "Local file do not need persist, should not get here "
                   << inode.ShortDebugString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      return Status::OK();
    }
    default:
      LOG(FATAL) << "Unreachable code" << inode.ShortDebugString();
  }
}

Status TosUfsUploader::AbortUpload(const std::string& upload_id,
                                   const std::string& ufs_key) {
  if (!upload_id.empty()) {
    UfsIdentifierInfo id;
    CHECK(ufs_->GetUfsInfo(ufs_key, &id).IsOK());
    UfsFileInfoProto info;
    info.set_upload_id(upload_id);
    Status s = ufs_->AbortCreate(id.path, info, UfsAbortCreateOption(true));
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to abort create " << s.ToString();
      return s;
    }
  }
  return {};
}

}  // namespace dancenn