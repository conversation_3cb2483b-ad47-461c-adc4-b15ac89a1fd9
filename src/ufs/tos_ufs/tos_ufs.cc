//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/tos_ufs/tos_ufs.h"

#include "absl/strings/string_view.h"
#include "base/constants.h"
#include "base/java_exceptions.h"
#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "base/stop_watch.h"
#include "base/string_utils.h"
#include "base/time_util.h"
#include "ufs/tos/tos_constant.h"
#include "ufs/tos/tos_s3_def.h"
#include "ufs/ufs.h"
#include "ufs/ufs_file_status.h"
#include "ufs/ufs_util.h"

// System
#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <glog/logging.h>

using std::string;
using std::vector;

DECLARE_int32(ufs_worker_thread_num);
DECLARE_int32(ufs_worker_pending_tasks_limit);
DECLARE_bool(tos_ufs_mkdir_create_empty_object);
DECLARE_bool(ufs_tos_shallow_copy_enabled);
DECLARE_uint64(ufs_tos_shallow_copy_enabled_epoch_sec);
DECLARE_bool(ufs_sync_mkdir_create_in_ufs);

std::string kTosListDelimiterDir{"/"};
std::string kTosListDelimiterEmpty;
uint32_t kTosListMaxKeys = 1000;

namespace dancenn {

static const int32_t kSubmitPoolRetryCount = 120;
static const std::string kEmptyString;
static const uint64_t kTosCopyObjectSizeLimit = 512UL * kBytesPerMB;

TosUfs::TosUfs(UfsConfig ufs_config,
               TosConfig config,
               std::shared_ptr<TosCredentialProvider> cred_provider)
    : Ufs(UfsType::UFS_TYPE_TOS, ufs_config),
      config_(std::move(config)),
      cred_provider_(std::move(cred_provider)),
      pool_("TOSUFS") {
}

TosUfs::TosUfs(UfsConfig ufs_config,
               TosConfig config,
               std::shared_ptr<TosCredentialProvider> cred_provider,
               std::shared_ptr<TosClient> tos_client)
    : Ufs(UfsType::UFS_TYPE_TOS, ufs_config),
      config_(std::move(config)),
      cred_provider_(std::move(cred_provider)),
      tos_client_(std::move(tos_client)),
      pool_("TOSUFS") {
}

TosUfs::~TosUfs() {
  pool_.Stop();
}

std::string TosUfs::Name() {
  return "TOS-" + config_.bucket;
}

Status TosUfs::Init() {
  LOG(INFO) << "TosUfs::Init Init tos_client";
  if (tos_client_ == nullptr) {
    tos_client_ = std::make_shared<TosClient>(config_, cred_provider_);
  }

  LOG(INFO) << "TosUfs::Init worker: " << FLAGS_ufs_worker_thread_num
            << ", pending: " << FLAGS_ufs_worker_pending_tasks_limit;
  pool_.set_num_threads((size_t)FLAGS_ufs_worker_thread_num);
  pool_.set_max_num_pending_tasks((size_t)FLAGS_ufs_worker_pending_tasks_limit);
  pool_.Start();
  return Status::OK();
}

Status TosUfs::GetFileStatus(const std::string& path, UfsFileStatus* info) {
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));

  uint64_t now_ms = TimeUtil::GetNowEpochMs();
  if (key.empty()) {  // Root dir
    Status build_s = UfsFileStatusBuilder()
                         .SetFileType(UFS_DIR)
                         .SetFullPath(path)
                         .SetCreatedTs(now_ms)
                         .SetModifiedTs(now_ms)
                         .Build(info);
    return std::move(build_s);
  }

  Status s;
  UfsFileStatus file;
  do {
    // Case 1: Get the object matches key
    TosHeadObjectResult res;
    Status head_s = tos_client_->HeadObject(key, &res);
    // Found the object
    if (head_s.IsOK()) {
      Status build_s =
          UfsFileStatusBuilder()
              .SetFullPath("/" + key)
              .SetFileType(UFS_FILE)
              .SetBlockSize(TosConstants::kTosBlockSize)
              .SetFileSize(res.GetContentLength())
              .SetEtag(res.GetETag())
              .SetCreatedTs(res.GetLastModified().Millis())
              .SetModifiedTs(res.GetLastModified().Millis())
              .Build(&file);
      if (!build_s.IsOK()) {
        s = std::move(build_s);
      }
      break;
    }
    // Unexpected service error
    if (head_s.code() != Code::kFileNotFound) {
      s = std::move(head_s);
      break;
    }

    // No object matches the key
    // Case 2: Check for directory by listing prefix
    AddDirSuffix(&key);
    TosListObjectsResult list_res;
    Status list_s = tos_client_->ListObjects(
        key, kEmptyString, 1, &list_res, kTosListDelimiterEmpty);
    // Unexpected service error
    if (!list_s.IsOK()) {
      s = std::move(list_s);
      break;
    }
    if (list_res.GetContents().size() > 0) {
      Status build_s = UfsFileStatusBuilder()
                           .SetFileType(UFS_DIR)
                           .SetFullPath("/" + key)
                           .SetCreatedTs(now_ms)
                           .SetModifiedTs(now_ms)
                           .Build(&file);
      if (!build_s.IsOK()) {
        s = std::move(build_s);
      }
      break;
    }

    // Case 3: Not found
    s = Status(Code::kFileNotFound,
               "TosUfs cannot find object / directory / directory-object in "
               "TOS for key: " +
                   key.substr(0, key.size() - 1));
  } while (0);
  if (!s.IsOK()) {
    if (s.code() == Code::kFileNotFound) {
      VLOG(8) << "TosUfs::GetFileStatus failed. path: " << path
              << ", error: " << s.ToString();

    } else {
      LOG(INFO) << "TosUfs::GetFileStatus failed. path: " << path
                << ", error: " << s.ToString();
    }
    return std::move(s);
  }

  VLOG(8) << "TosUfs::GetFileStatus finished for path: " << path
          << ", type: " << file.FileType() << ", size: " << file.FileSize();
  *info = std::move(file);
  return Status::OK();
}

Status TosUfs::GetFileOnlyStatus(const std::string& path, UfsFileStatus* info) {
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));

  uint64_t now_ms = TimeUtil::GetNowEpochMs();
  if (key.empty()) {  // Root dir
    return Status(JavaExceptions::Exception::kFileNotFoundException, Code::kFileNotFound,
                  path + "not existed as a file (not dir).");
  }

  VLOG(12) << "TosUfs::GetFileOnlyStatus for path: " << path
           << ", key: " << key;

  Status s;
  UfsFileStatus file;
  do {
    // Case 1: Get the object matches key
    TosHeadObjectResult res;
    Status head_s = tos_client_->HeadObject(key, &res);
    // Found the object
    if (head_s.IsOK()) {
      Status build_s =
          UfsFileStatusBuilder()
              .SetFullPath("/" + key)
              .SetFileType(UFS_FILE)
              .SetBlockSize(TosConstants::kTosBlockSize)
              .SetFileSize(res.GetContentLength())
              .SetEtag(res.GetETag())
              .SetCreatedTs(res.GetLastModified().Millis())
              .SetModifiedTs(res.GetLastModified().Millis())
              .Build(&file);
      if (!build_s.IsOK()) {
        s = std::move(build_s);
      }
      break;
    }
    // Unexpected service error
    if (head_s.code() != Code::kFileNotFound) {
      s = std::move(head_s);
      break;
    }

    s = Status(Code::kFileNotFound, path + "not existed as a file (not dir).");
  } while (0);
  if (s.IsOK()) {
    *info = std::move(file);
  }
  return s;
}

Status TosUfs::GetDirectoryStatus(const std::string& path, UfsDirStatus* dir) {
  std::string prefix;
  RETURN_NOT_OK(GetTosObjectKey(path, &prefix));

  AddDirSuffix(&prefix);

  TosListObjectsResult list_res;
  Status list_s = tos_client_->ListObjects(
      prefix, kEmptyString, 2, &list_res, kTosListDelimiterEmpty);
  if (!list_s.IsOK()) {
    LOG(INFO) << "TosUfs::GetIsDirectoryEmpty failed to list objects from "
                 "TOS. prefix: "
              << prefix << ", error: " << list_s.ToString();
    return std::move(list_s);
  }

  auto&& contents = list_res.GetContents();
  if (contents.size() == 0) {
    // Dir not found
    return Status(JavaExceptions::Exception::kFileNotFoundException, Code::kDirNotFound, "dir not found for path: " + path);
  }

  if (contents.size() == 1 && contents[0].GetKey() == prefix) {
    // Only dir-object existed
    *dir = UfsDirStatus(path, false);
    return Status::OK();
  }

  *dir = UfsDirStatus(path, true);
  return Status::OK();
}

Status TosUfs::ListFiles(const std::string& path,
                         const ListFilesOption& option,
                         ListFilesResult* result) {
  VLOG(8) << "ListFiles for path: " << path
          << ", page_size: " << option.page_size
          << ", recursive: " << option.recursive
          << ", continue_token: " << option.continue_token;

  uint64_t now_ms = TimeUtil::GetNowEpochMs();
  std::string prefix;
  RETURN_NOT_OK(GetTosObjectKey(path, &prefix));

  // For root dir, the prefix is "", don't append / as a prefix
  // In other case, directory subfile listing must use a prefix ends with /
  if (!prefix.empty()) {
    prefix.append(kSeparator);
  }

  TosListObjectsResult list_res;
  auto s = tos_client_->ListObjects(
      prefix,
      option.continue_token,
      option.page_size,
      &list_res,
      option.recursive ? kTosListDelimiterEmpty : kTosListDelimiterDir);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to list objects. path: " << path
              << ", error: " << s.ToString();
    return s;
  }

  // COS may not return KeyCount in result
  if (list_res.GetContents().empty() && list_res.GetCommonPrefixes().empty()) {
    result->files.clear();
    result->has_more = false;
    result->continue_token.clear();
    return Status::OK();
  }

  std::vector<UfsFileStatus> files;
  files.reserve(list_res.GetContents().size() +
                list_res.GetCommonPrefixes().size());
  for (auto&& o : list_res.GetContents()) {
    if (IsBreadcrumbObject(prefix, o.GetKey())) {
      continue;
    }
    if (!UfsUtil::IsListingChildrenValidUfsPath(prefix, o.GetKey())) {
      LOG(WARNING) << "TosUfs::ListFiles ignore invalid key in tos ufs: "
                   << o.GetKey();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      continue;
    }

    std::string full_path = "/" + o.GetKey();
    UfsFileType type = full_path.back() == '/' ? UFS_DIR : UFS_FILE;
    UfsFileStatus obj_file;
    auto build_s = UfsFileStatusBuilder()
                       .SetFileType(type)
                       .SetFullPath(std::move(full_path))
                       .SetBlockSize(TosConstants::kTosBlockSize)
                       .SetFileSize(o.GetSize())
                       .SetEtag(o.GetETag())
                       .SetCreatedTs(o.GetLastModified().Millis())
                       .SetModifiedTs(o.GetLastModified().Millis())
                       .Build(&obj_file);
    if (!build_s.IsOK()) {
      LOG(WARNING) << "Failed to construct UfsFileStatus. full_path: "
                   << full_path << ", error: " << build_s.ToString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      continue;
    }

    files.emplace_back(std::move(obj_file));
  }
  for (auto&& p : list_res.GetCommonPrefixes()) {
    auto&& prefix_str = p.GetPrefix();
    if (prefix_str.empty()) {
      return Status(JavaExceptions::Exception::kIOException, Code::kUfsSyncError, "TOS prefix is empty");
    }
    if (prefix_str.back() != kSeparatorChar) {
      return Status(JavaExceptions::Exception::kIOException, Code::kUfsSyncError,
                    "TOS prefix invalid, not end with /: " + prefix_str);
    }

    if (!UfsUtil::IsListingChildrenValidUfsPath(prefix, prefix_str)) {
      LOG(INFO) << "TosUfs::ListFiles ignore invalid key in tos ufs: "
                << prefix_str;
      continue;
    }

    UfsFileStatus subdir;
    auto build_s = UfsFileStatusBuilder()
                       .SetFileType(UFS_DIR)
                       .SetFullPath(kSeparator + prefix_str)
                       .SetCreatedTs(now_ms)
                       .SetModifiedTs(now_ms)
                       .Build(&subdir);
    if (!build_s.IsOK()) {
      LOG(INFO) << "Failed to construct UfsFileStatus. full_path: "
                << (kSeparator + prefix_str)
                << ", error: " << build_s.ToString();
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      continue;
    }
    files.emplace_back(std::move(subdir));
  }

  // Sort the result in ascending order as elements in Contents and
  // CommonPrefixes might be disordered.
  // The Common prefix's order returned from  S3 is not same as local ns,
  // because trailing slash '/' is counted as part of sort element. Use
  // stable_sort to keep file element in front of dir element for duplicated
  // object and prefix
  if (list_res.GetCommonPrefixes().size() > 0) {
    std::stable_sort(
        files.begin(),
        files.end(),
        [](const UfsFileStatus& a, const UfsFileStatus& b) -> bool {
          return a.FullPath().compare(b.FullPath()) < 0;
        });
  }

  {
    // Remove duplicated dir element in current batch
    auto it = files.begin();
    const std::string* last = nullptr;
    while (it != files.end()) {
      if (last != nullptr) {
        if (UNLIKELY(*last == it->FileName())) {
          it = files.erase(it);
          continue;
        }
      }

      last = &(it->FileName());
      ++it;
    }
  }

  result->continue_token = list_res.GetNextContinuationToken();
  result->has_more = list_res.GetIsTruncated();
  result->files = std::move(files);
  return Status::OK();
}

Status TosUfs::Mkdir(const std::string& path) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  AddDirSuffix(&key);

  VLOG(8) << "Create dir: " << key;
  if (FLAGS_tos_ufs_mkdir_create_empty_object) {
    static const std::string dirTagging =
        "Status=Created&CreateType=Put&Creator=CloudFS";

    return tos_client_->CreateEmptyObject(key, dirTagging, nullptr);
  }

  return {};
}

Status TosUfs::AbortMkdir(const std::string& path) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  AddDirSuffix(&key);

  VLOG(8) << "AbortCreate dir: " << key;

  return tos_client_->DeleteObject(key);
}

Status TosUfs::CreateMissingParentIfNecessary(const std::string& path) {
  std::vector<cnetpp::base::StringPiece> ancestors;
  CHECK(GetAllAncestorPaths(path, &ancestors));
  ancestors.pop_back();
  if (ancestors.size() <= 1) {
    // Self or parent is root
    return Status::OK();
  }
  std::string parent = ancestors.back().as_string();
  return Mkdir(parent);
}

Status TosUfs::CheckParentReady(const std::string& path) {
  std::vector<cnetpp::base::StringPiece> ancestors;
  CHECK(GetAllAncestorPaths(path, &ancestors));
  ancestors.pop_back();
  if (ancestors.size() <= 1) {
    // Self or parent is root
    return Status::OK();
  }
  std::string parent = ancestors.back().as_string();
  Status s;
  UfsFileStatus status;
  s = GetFileStatus(parent, &status);
  if (!s.IsOK()) {
    if (s.code() == Code::kFileNotFound) {
      return Status::OK();
    }
    return s;
  }
  if (!status.IsDir()) {
    return Status(JavaExceptions::Exception::kFileNotFoundException,
                  Code::kFileNotFound,
                  "Parent directory doesn't exist.");
  }
  return Status::OK();
}

Status TosUfs::Create(const std::string& path,
                      const UfsCreateOption& opt,
                      UfsFileInfoProto* info) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  VLOG(8) << "Create path: " << key;

  Status s;
  if (opt.emptyObject) {
    if (!FLAGS_tos_ufs_mkdir_create_empty_object) {
      LOG(ERROR) << "not allow create empty object " << path << " "
                 << info->ShortDebugString();
      return Status(JavaExceptions::kIOException,
                    "not allow create empty object");
    }

    static const std::string emptyObjectTagging =
        "Status=Created&CreateType=Put&Creator=CloudFS";
    std::string etag;
    s = tos_client_->CreateEmptyObject(key, emptyObjectTagging, &etag);
    if (!s.IsOK()) {
      LOG(INFO) << "Create empty object failed " << path << " " << s.ToString();
      return s;
    }
    info->clear_upload_id();
    info->set_tagging(emptyObjectTagging);
    info->set_etag(etag);
  }
  if (opt.multipartUpload) {
    static const std::string tagging =
        "Status=Created&CreateType=Upload&Creator=CloudFS";
    std::string upload_id;
    s = tos_client_->CreateMpuObject(key, tagging, &upload_id);
    if (!s.IsOK()) {
      LOG(INFO) << "Create multipartupload failed " << path << " "
                << s.ToString();
      return s;
    }
    *(info->mutable_upload_id()) = std::move(upload_id);
  }
  return s;
}

Status TosUfs::AbortCreate(const std::string& path,
                           const UfsFileInfoProto& info,
                           const UfsAbortCreateOption& opt) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  VLOG(8) << "AbortCreate file path: " << key;
  Status s;
  if (opt.abortMultipartUpload) {
    s = tos_client_->AbortCreateMultipartUpload(key, info.upload_id());
    if (!s.IsOK()) {
      LOG(INFO) << "AbortCreate abort multipartupload failed " << path << " "
                << s.ToString();
      return s;
    }
  }
  return s;
}

Status TosUfs::CheckUfsFileState(const std::string& path,
                                 const UfsFileInfoProto& info) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  VLOG(10) << "CheckUfsFileState file path: " << key;
  auto s = tos_client_->CheckMpuStatus(key, info.upload_id());
  if (!s.IsOK()) {
    LOG(INFO) << "CheckUfsFileState failed to check Mpu, path: " << path
              << ", error: " << s.ToString();
  }
  return s;
}

Status TosUfs::CompleteFile(const std::string& path,
                            const std::map<int, std::string>& parts,
                            UfsFileInfoProto* info,
                            uint64_t size) {
  CHECK_READ_ONLY;
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  VLOG(8) << "CompleteFile file path: " << key;

  if (!FLAGS_ufs_sync_mkdir_create_in_ufs) {
    // mkdir not create parent immediately, so we need to create parent dir
    // before rename.
    auto s = CreateMissingParentIfNecessary(path);
    if (s.IsOK()) {
      LOG(INFO) << "[CompleteFile] Ufs create missing parent success: " << path;
    } else {
      LOG(INFO) << "[CompleteFile] Ufs create missing parent failed: " << path
                << ", error: " << s.ToString();
    }
  }

  auto s = tos_client_->CompleteObject(
      key, info->upload_id(), parts, info->mutable_etag());
  if (s.IsOK()) {
    static const std::string tagging =
        "Status=Created&CreateType=Upload&Creator=CloudFS";
    info->set_tagging(tagging);
  }
  return s;
}

Status TosUfs::DeleteDirectory(const string& path) {
  CHECK_READ_ONLY;
  string prefix;
  RETURN_NOT_OK(GetTosObjectKey(path, &prefix));

  prefix.append(kSeparator);
  VLOG(8) << "TosUfs::DeleteDirectory started. path: " << path
          << ", prefix: " << prefix;

  std::string continue_token;
  TosListObjectsResult res;
  Status s;
  while (true) {
    s = tos_client_->ListObjects(
        prefix, continue_token, kTosListMaxKeys, &res, kTosListDelimiterEmpty);
    if (!s.IsOK()) {
      break;
    }

    s = tos_client_->DeleteObjects(res.GetContents());
    if (!s.IsOK()) {
      break;
    }

    continue_token = res.GetNextContinuationToken();
    if (!res.GetIsTruncated()) {
      break;
    }
  };

  if (!s.IsOK()) {
    LOG(INFO) << "TosUfs::DeleteDirectory failed to delete directory. path: "
              << path << ", error: " << s.ToString();
    return s;
  }

  // Corner case
  // Try delete the directory-object matches key
  do {
    auto delete_s = tos_client_->DeleteObject(prefix);
    if (!delete_s.IsOK() && delete_s.code() != Code::kFileNotFound) {
      s = std::move(delete_s);
      break;
    }
  } while (0);

  if (!s.IsOK()) {
    LOG(INFO)
        << "TosUfs::DeleteDirectory failed to delete object with the same key: "
        << prefix << ", error: " << s.ToString();
    return s;
  }

  VLOG(8) << "TosUfs::DeleteDirectory done, path: " << path;
  return std::move(s);
}

Status TosUfs::GetUfsIdentifier(const UfsIdentifierInfo& info,
                                std::string* key) {
  return GetTosObjectKey(info.path, key);
}

Status TosUfs::GetTosObjectKey(const std::string& ufs_path, std::string* key) {
  std::string path;
  // Reserve for possible append later to avoid memory allocation
  path.reserve(ufs_path.size() + 8);
  path.assign(ufs_path);

  RemoveRedundantSlash(&path);

  if (path.empty()) {
    LOG(INFO) << "Invalid parameter, ufs_path is empty";
    return Status(JavaExceptions::Exception::kIllegalArgumentException,
                  Code::kBadParameter,
                  "ufs_path is empty.");
  }
  if (path[0] != kSeparatorChar) {
    LOG(INFO) << "Invalid parameter, ufs_path not start with / :" << ufs_path;
    return Status(JavaExceptions::Exception::kIllegalArgumentException,
                  Code::kBadParameter,
                  "ufs_path not start with / : " + ufs_path);
  }

  path.erase(0, 1);
  *key = std::move(path);
  return Status::OK();
}

Status TosUfs::GetUfsInfo(const std::string& key, UfsIdentifierInfo* info) {
  CHECK_NOTNULL(info);
  std::string path = key;
  if (path.back() == '/') {
    path.pop_back();
  }
  path.insert(0, "/");
  info->path = path;
  return Status::OK();
}

Status TosUfs::DeleteFile(const std::string& path) {
  CHECK_READ_ONLY;
  string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  auto s = tos_client_->DeleteObject(key);
  if (!s.IsOK()) {
    LOG(INFO) << "TosUfs::DeleteFile failed to delete file with key: " << key
              << ", error: " << s.ToString();
    return s;
  }

  VLOG(8) << "TosUfs::DeleteFile deleted file key: " << key;
  return std::move(s);
}

struct CopyDirectoryContext {
  CopyDirectoryContext(std::string src, std::string dst)
      : src_key(std::move(src)), dst_key(std::move(dst)) {
  }

  void StartBatch(uint32_t batch_size) {
    std::lock_guard<std::mutex> lg(lock);
    CHECK(current_batch_finished == current_batch_total);
    current_batch_finished = 0;
    current_batch_total = batch_size;
  }

  void WaitForBatchDone() {
    std::unique_lock<std::mutex> lg(lock);
    while (current_batch_finished < current_batch_total) {
      cv.wait_for(lg, std::chrono::seconds(1));
    }
  }

  void AbortCurrentBatch(const Status& s) {
    std::unique_lock<std::mutex> lg(lock);
    aborted = true;
    status = s;
  }

  bool CurrentBatchDone() {
    std::lock_guard<std::mutex> lg(lock);
    return current_batch_finished == current_batch_total;
  }

  void FinishCopy(const Status& s) {
    {
      std::lock_guard<std::mutex> lg(lock);
      if (aborted) {
        return;
      }
      ++current_batch_finished;
      ++total_copied_file_count;
      if (!s.IsOK()) {
        status = s;
      }
      if (current_batch_finished == current_batch_total) {
        cv.notify_all();
      }
    }
  }

  std::string src_key;
  std::string dst_key;

  std::mutex lock;
  std::condition_variable cv;
  bool aborted{false};
  Status status;
  uint64_t total_copied_file_count{0};
  uint32_t current_batch_total{0};
  uint32_t current_batch_finished{0};
};

Status TosUfs::CopyDirectory(const std::string& src,
                             const std::string& dst,
                             UfsCopyResult* result) {
  CHECK_READ_ONLY;
  std::shared_ptr<CopyDirectoryContext> ctx;
  {
    string src_key, dst_key;
    RETURN_NOT_OK(GetTosObjectKey(src, &src_key));
    RETURN_NOT_OK(GetTosObjectKey(dst, &dst_key));
    AddDirSuffix(&src_key);
    AddDirSuffix(&dst_key);

    // Check if src existed
    UfsDirStatus dir;
    Status s = GetDirectoryStatus(src, &dir);
    if (!s.IsOK() && s.code() != Code::kDirNotFound) {
      LOG(INFO) << "TosUfs::CopyDirectory failed. Unable to check if src is "
                   "directory. src: "
                << src << ", error: " << s.ToString();
      return s;
    }
    if (s.code() == Code::kDirNotFound) {
      LOG(INFO) << "TosUfs::CopyDirectory failed. src not existed or not a "
                   "directory. src: "
                << src;
      return Status(JavaExceptions::Exception::kIllegalArgumentException,
                    Code::kDirNotFound,
                    "src not existed or not a directory.");
    }

    UfsFileStatus dst_file;
    s = GetFileStatus(dst, &dst_file);
    if (!s.IsOK() && s.code() != Code::kFileNotFound) {
      LOG(INFO)
          << "TosUfs::CopyDirectory failed. Unable to get dst status. dst: "
          << dst << ", error: " << s.ToString();
      return s;
    }
    // Dst existed
    if (s.IsOK()) {
      return Status(JavaExceptions::Exception::kFileAlreadyExistsException,
                    Code::kFileExists,
                    "dst already existed, cannot copy directory. dst: " + dst);
    }

    ctx = std::make_shared<CopyDirectoryContext>(std::move(src_key),
                                                 std::move(dst_key));
  }

  VLOG(8) << "TosUfs::CopyDirectory copy started. src: " << src
          << ", dst: " << dst;

  std::string continue_token;
  Status s;
  while (true) {
    DLOG(INFO) << "TosUfs::CopyDirectory start to list src_key: "
               << ctx->src_key << ", continue: " << continue_token;

    TosListObjectsResult res;
    auto list_s = tos_client_->ListObjects(ctx->src_key,
                                           continue_token,
                                           kTosListMaxKeys,
                                           &res,
                                           kTosListDelimiterEmpty);
    if (!list_s.IsOK()) {
      s = list_s;
      break;
    }

    CHECK(ctx->CurrentBatchDone());

    auto&& objects = res.GetContents();
    DLOG(INFO) << "TosUfs::CopyDirectory finish list src_key: " << ctx->src_key
               << ", continue: " << continue_token
               << ", count: " << objects.size();

    ctx->StartBatch(objects.size());

    Status copy_s;
    for (auto&& o : objects) {
      auto&& copy_src_key = o.GetKey();
      std::string name = copy_src_key.substr(ctx->src_key.size());
      std::string copy_src_path = JoinTwoPath(src, name);
      std::string copy_dst_key = ctx->dst_key + name;
      std::string copy_dst_path = JoinTwoPath(dst, name);
      uint64_t len = (uint64_t)o.GetSize();

      const uint64_t last_modified =
          static_cast<uint64_t>(o.GetLastModified().SecondsWithMSPrecision());
      VLOG(10) << "CopyFile shallow_copy: "
               << FLAGS_ufs_tos_shallow_copy_enabled << ", epoch_sec: "
               << FLAGS_ufs_tos_shallow_copy_enabled_epoch_sec
               << ", last_modified: " << last_modified
               << ", src: " << copy_src_key << ", dst: " << copy_dst_key;
      // If TOS's shallow copy is enabled, and the object last modified time is
      // larger than shallow copy effect time, we can always use shallow copy
      if (FLAGS_ufs_tos_shallow_copy_enabled &&
          last_modified > FLAGS_ufs_tos_shallow_copy_enabled_epoch_sec) {
        copy_s = CopyFileByCopyObjectAsync(
            copy_src_path,
            copy_src_key,
            copy_dst_path,
            copy_dst_key,
            len,
            [copy_src_path, ctx, result](const Status& s,
                                         const std::string& etag) {
              if (s.IsOK()) {
                result->AddResult(copy_src_path, etag);
              }
              ctx->FinishCopy(s);
            });
      } else {
        if (len <= kTosCopyObjectSizeLimit) {
          copy_s = CopyFileByCopyObjectAsync(
              copy_src_path,
              copy_src_key,
              copy_dst_path,
              copy_dst_key,
              len,
              [copy_src_path, ctx, result](const Status& s,
                                           const std::string& etag) {
                if (s.IsOK()) {
                  result->AddResult(copy_src_path, etag);
                }
                ctx->FinishCopy(s);
              });
        } else {
          copy_s =
              CopyFileByMpuAsync(copy_src_path,
                                 copy_src_key,
                                 copy_dst_path,
                                 copy_dst_key,
                                 len,
                                 [copy_src_path, ctx, result](
                                     const Status& s, const std::string& etag) {
                                   if (s.IsOK()) {
                                     result->AddResult(copy_src_path, etag);
                                   }
                                   ctx->FinishCopy(s);
                                 });
        }
      }
      if (!copy_s.IsOK()) {
        LOG(INFO) << "Failed to start async copy for key: " << copy_src_key
                  << ", dst: " << copy_dst_key << ", len: " << len
                  << ", error: " << copy_s.ToString();
        break;
      }
    }
    if (!copy_s.IsOK()) {
      ctx->AbortCurrentBatch(copy_s);
      s = copy_s;
      break;
    }

    ctx->WaitForBatchDone();
    if (!ctx->status.IsOK()) {
      s = ctx->status;
      break;
    }

    continue_token = res.GetNextContinuationToken();

    VLOG(8) << "TosUfs::CopyDirectory copy one batch finished. src: " << src
            << ", dst: " << dst
            << ", total_copied_file_count: " << ctx->total_copied_file_count;

    if (!res.GetIsTruncated()) {
      break;
    }
  }

  if (!s.IsOK()) {
    LOG(INFO) << "TosUfs::CopyDirectory failed. src: " << src
              << ", dst: " << dst << ", error: " << s.ToString();
  } else {
    result->SetCopyFinished();
    VLOG(8) << "TosUfs::CopyDirectory copy finished. src: " << src
            << ", dst: " << dst
            << ", total_copied_file_count: " << ctx->total_copied_file_count;
  }
  return std::move(s);
}

Status TosUfs::CopyFile(const std::string& src,
                        const std::string& dst,
                        bool overwrite,
                        UfsCopyResult* result) {
  CHECK_READ_ONLY;
  string src_key, dst_key;
  RETURN_NOT_OK(GetTosObjectKey(src, &src_key));
  RETURN_NOT_OK(GetTosObjectKey(dst, &dst_key));

  Status s;
  do {
    TosHeadObjectResult src_res;
    s = tos_client_->HeadObject(src_key, &src_res);
    if (!s.IsOK()) {
      break;
    }

    // Return error if dst existed
    if (!overwrite) {
      TosHeadObjectResult dst_res;
      s = tos_client_->HeadObject(dst_key, &dst_res);
      if (!s.IsOK() && s.code() != Code::kFileNotFound) {
        break;
      }
      if (s.IsOK()) {
        s = Status(Code::kFileExists, "Rename dst existed: " + dst);
        break;
      }
    }

    const uint64_t last_modified = static_cast<uint64_t>(src_res.GetLastModified().SecondsWithMSPrecision());
    VLOG(10) << "CopyFile shallow_copy: " << FLAGS_ufs_tos_shallow_copy_enabled 
             << ", epoch_sec: " << FLAGS_ufs_tos_shallow_copy_enabled_epoch_sec
             << ", last_modified: " << last_modified 
             << ", src: " << src_key
             << ", dst: " << dst_key;
    // If TOS's shallow copy is enabled, and the object last modified time is larger than shallow copy effect time, we can always use shallow copy
    if (FLAGS_ufs_tos_shallow_copy_enabled && last_modified > FLAGS_ufs_tos_shallow_copy_enabled_epoch_sec) {
      std::string etag;
      s = tos_client_->CopyObjectWithInBucket(src_key, dst_key, &etag);
      if (s.IsOK()) {
        result->AddResult(src, etag);
      }
    } else {
      const uint64_t src_len = src_res.GetContentLength();
      if (src_len <= kTosCopyObjectSizeLimit) {
        // Copy using CopyObject
        std::string etag;
        s = tos_client_->CopyObjectWithInBucket(src_key, dst_key, &etag);
        if (s.IsOK()) {
          result->AddResult(src, etag);
        }
      } else {
        // Copy using multipart
        s = CopyFileByMpu(src, src_key, dst, dst_key, src_len, result);
      }
    }
    if (!s.IsOK()) {
      break;
    }
    result->SetCopyFinished();
  } while (0);

  if (!s.IsOK()) {
    LOG(INFO) << "TosUfs: Failed to copy file. src: " << src << ", dst: " << dst
              << ", error: " << s.ToString();
  }
  return s;
}

struct CopyFileContext {
  CopyFileContext(const std::shared_ptr<TosClient>& t,
                  const std::string& src,
                  const std::string& dst,
                  uint64_t len,
                  int32_t part_n)
      : tos(t), src_key(src), dst_key(dst), src_len(len), part_num(part_n) {
  }

  void Finish(int32_t part_seq, const std::string& part_etag, const Status& s) {
    bool trigger_cb = false;
    {
      SpinLockGuard lg(lock);
      ++finished_num;
      if (s.IsOK()) {
        VLOG(8) << "Copy file part finished. seq: " << part_seq
                << ", src: " << src_key << ", dst: " << dst_key
                << ", len: " << src_len << ", part_num: " << part_num
                << ", etag: " << part_etag;
        part_etags[part_seq] = part_etag;
      } else {
        LOG(INFO) << "Copy file part failed. seq: " << part_seq
                  << ". src: " << src_key << ", dst: " << dst_key
                  << ", len: " << src_len << ", part_seq: " << part_seq
                  << ", error: " << s.ToString();
        bool expected = false;
        if (aborted.compare_exchange_strong(expected, true)) {
          last_error = s;
        }
      }

      if (finished_num == part_num) {
        trigger_cb = true;
      }
    }

    if (trigger_cb) {
      if (last_error.IsOK()) {
        Status complete_s = CompleteMpu();
        if (!complete_s.IsOK()) {
          last_error = complete_s;
        }
      } else {
        AbortMpu();
      }
      cb(last_error, etag);
    }
  }

 private:
  Status CompleteMpu() {
    auto s = tos->CompleteObject(dst_key, upload_id, part_etags, &etag);
    if (s.IsOK()) {
      VLOG(8) << "CopyFileContext complete file successfully. key: " << dst_key
              << ", upload_id: " << upload_id << ", etag: " << etag;
    } else {
      LOG(INFO) << "CopyFileContext failed to complete file, will abort. key: "
                << dst_key << ", upload_id: " << upload_id
                << ", error: " << s.ToString();
      AbortMpu();
    }
    return std::move(s);
  }
  void AbortMpu() {
    auto s = tos->AbortCreateMultipartUpload(dst_key, upload_id);
    if (!s.IsOK()) {
      LOG(INFO) << "Failed to abort mpu. key: " << dst_key
                << ", upload_id: " << upload_id;
    }
  }

 public:
  std::shared_ptr<TosClient> tos;
  std::string src_key;
  std::string dst_key;
  uint64_t src_len;
  std::string upload_id;
  std::string etag;
  AsyncCopyFileCallback cb;

  std::atomic<bool> aborted{false};

  SpinLock lock;
  int32_t part_num;
  int32_t finished_num{0};
  Status last_error;
  std::map<int, std::string> part_etags;
};

Status TosUfs::CopyFileByMpu(const std::string& src,
                             const std::string& src_key,
                             const std::string& dst,
                             const std::string& dst_key,
                             uint64_t src_len,
                             UfsCopyResult* result) {
  CHECK_READ_ONLY;
  CountDownLatch lat(1);
  Status copy_status;
  auto s = CopyFileByMpuAsync(src,
                              src_key,
                              dst,
                              dst_key,
                              src_len,
                              [&src, &dst, &result, &lat, &copy_status](
                                  const Status& s, const std::string& etag) {
                                if (s.IsOK()) {
                                  result->AddResult(src, etag);
                                } else {
                                  copy_status = s;
                                }
                                lat.CountDown();
                              });
  if (!s.IsOK()) {
    return s;
  }

  lat.Await();
  if (!copy_status.IsOK()) {
    LOG(INFO) << "Failed to copy file using mpu. src: " << src_key
              << ", dst: " << dst_key << ", src_len: " << src_len
              << ", error: " << copy_status.ToString();
  }
  return std::move(copy_status);
}

Status TosUfs::CopyFileByMpuAsync(const std::string& src,
                                  const std::string& src_key,
                                  const std::string& dst,
                                  const std::string& dst_key,
                                  uint64_t src_len,
                                  AsyncCopyFileCallback cb) {
  CHECK_READ_ONLY;
  VLOG(8) << "TosUfs::CopyFileByMpuAsync Start copy file using MPU. src_key: "
          << src_key << ", dst_key: " << dst_key << ", src_len: " << src_len;
  Status s;
  int32_t part_num = (src_len - 1) / kTosCopyObjectSizeLimit + 1;
  do {
    auto ctx = std::make_shared<CopyFileContext>(
        tos_client_, src_key, dst_key, src_len, part_num);
    s = tos_client_->CreateMpuObject(dst_key, "", &ctx->upload_id);
    if (!s.IsOK()) {
      break;
    }

    ctx->cb = std::move(cb);

    int32_t part_seq = 1;
    uint64_t offset = 0;
    while (offset < src_len) {
      uint64_t copy_start = offset;
      uint64_t copy_len = std::min(kTosCopyObjectSizeLimit, src_len - offset);

      DLOG(INFO) << "TosUfs::CopyFileByMpuAsync submit task to pool. src: "
                 << ctx->src_key << ", dst: " << ctx->dst_key
                 << ", part_seq: " << part_seq << ", start: " << copy_start
                 << ", copy_len: " << copy_len;
      auto submit_s =
          SubmitToPool([this, ctx, part_seq, copy_start, copy_len]() -> bool {
            if (ctx->aborted.load()) {
              ctx->Finish(part_seq,
                          kEmptyString,
                          Status(Code::kError, "Upload aborted"));
              return true;
            }
            TosUploadPartCopyResult part_res;
            auto s = tos_client_->UploadMpuPartCopy(ctx->dst_key,
                                                    ctx->src_key,
                                                    copy_start,
                                                    copy_len,
                                                    ctx->upload_id,
                                                    part_seq,
                                                    &part_res);
            ctx->Finish(part_seq,
                        s.IsOK() ? part_res.GetCopyPartResult().GetETag()
                                 : kEmptyString,
                        s);
            return true;
          });
      if (!submit_s.IsOK()) {
        s = submit_s;
        break;
      }

      offset += copy_len;
      ++part_seq;
    }
    if (!s.IsOK()) {
      break;
    }
  } while (0);
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to start copy file using MPU. src_key: " << src_key
              << ", dst_key: " << dst_key << ", src_len: " << src_len
              << ", error: " << s.ToString();
  }
  return s;
}

Status TosUfs::CopyFileByCopyObjectAsync(const std::string& src,
                                         const std::string& src_key,
                                         const std::string& dst,
                                         const std::string& dst_key,
                                         uint64_t src_len,
                                         AsyncCopyFileCallback cb) {
  CHECK_READ_ONLY;
  VLOG(8) << "TosUfs::CopyFileByCopyObjectAsync start. src: " << src_key
          << ", dst: " << dst_key << ", len: " << src_len;

  // Copy using CopyObject
  Status s =
      SubmitToPool([this, src_key, dst_key, cb = std::move(cb)]() -> bool {
        std::string etag;
        Status copy_s =
            tos_client_->CopyObjectWithInBucket(src_key, dst_key, &etag);
        cb(copy_s, etag);
        return true;
      });
  if (!s.IsOK()) {
    LOG(INFO) << "Failed to submit copy object task to pool. src_key: "
              << src_key << ", dst_key: " << dst_key
              << ", error: " << s.ToString();
  }
  return std::move(s);
}

Status TosUfs::SubmitToPool(std::function<bool()> fn) {
  int count = 0;
  while (count < kSubmitPoolRetryCount) {
    if (pool_.AddTask(fn)) {
      return Status::OK();
    }
    LOG(INFO) << "Failed to submit task to pool. try again. count: " << count;
    std::this_thread::sleep_for(std::chrono::seconds(1));
    ++count;
  }
  return Status(JavaExceptions::Exception::kIOException, Code::kTimeout, "Failed to submit task to pool. Timedout");
}

Status TosUfs::ReadFile(const std::string& path,
                        uint64_t offset,
                        uint64_t length,
                        std::string* result) {
  std::string key;
  RETURN_NOT_OK(GetTosObjectKey(path, &key));
  return tos_client_->ReadObject(key, offset, length, result);
}

void TosUfs::AddDirSuffix(std::string* prefix) {
  // For root dir, the prefix is "", don't append / as a prefix
  // In other case, directory subfile listing must use a prefix ends with /

  if (prefix->empty()) {  // For root dir, no need to add / prefix to list
    return;
  }
  if (prefix->back() == kSeparatorChar) {
    return;
  }
  prefix->append(kSeparator);
}

}  // namespace dancenn
