//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_sync_task_factory.h"

// Project
#include "acc/task/tos_event_sync_task.h"
#include "ufs/ufs_config.h"

namespace dancenn {

std::shared_ptr<SyncTask> UfsEventSyncTaskFactory::CreateTask(
    const UfsConfig* config,
    const std::shared_ptr<Ufs>& ufs,
    const std::shared_ptr<NameSpace>& ns,
    const SyncCallback& cb,
    UfsEventManager* mgr,
    const std::string& msg) {
  switch (config->type) {
    case UfsType::UFS_TYPE_TOS: {
      return std::make_shared<TosEventSyncTask>(config, ufs, ns, cb, mgr, msg);
    }
    default:
      LOG(FATAL) << "Unexpected UfsType: " << config->type;
  }
}

}  // namespace dancenn
