// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "service/ha_service.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <memory>
#include <string>

#include "base/file_utils.h"
#include "rpc/rpc_controller.h"

DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_string(non_ha_mode_trigger_file);
DECLARE_bool(namenode_ha_use_async_thread);
DECLARE_bool(ha_fast_return_when_switch_to_standby);

namespace dancenn {

using MR = MethodRecorder<HAService>;

HAService::HAService(HAStateBase* ha_state, SafeModeBase* safemode)
    : ha_state_(ha_state), safemode_(safemode) {
  InitMetrics();

  thread_pool_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("HA-worker", true);
  thread_pool_->set_num_threads(1);
  thread_pool_->Start();
}

HAService::~HAService() {
  if (thread_pool_) {
    thread_pool_->Stop();
    thread_pool_.reset();
  }
}

void HAService::monitorHealth(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::MonitorHealthRequestProto* request,
    ::cloudfs::MonitorHealthResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "monitorHealth", c, done);
}

void HAService::transitionToActive(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::TransitionToActiveRequestProto* request,
    ::cloudfs::TransitionToActiveResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "transitionToActive", c, request, response, done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FileUtils::IsFile(FLAGS_non_ha_mode_trigger_file)) {
    c->MarkAsFailed(JavaExceptions::kUnsupportedOperationException,
                    FLAGS_non_ha_mode_trigger_file + " exists");
  }
  if (ha_state_->GetHAMode() == EditLogConf::StandbyNonHA) {
    c->MarkAsFailed(JavaExceptions::kUnsupportedOperationException,
                    "Current HA mode is StandbyNonHA");
    return;
  }

  if (FLAGS_namenode_ha_use_async_thread) {
    thread_pool_->AddTask([rpc_done = done_guard.release(), this]() -> bool {
      ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE);

      rpc_done->Run();

      return true;
    });
  } else {
    ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE);
  }
}

void HAService::transitionToStandby(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::TransitionToStandbyRequestProto* request,
    ::cloudfs::TransitionToStandbyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "transitionToStandby", c, request, response, done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FileUtils::IsFile(FLAGS_non_ha_mode_trigger_file)) {
    c->MarkAsFailed(JavaExceptions::kUnsupportedOperationException,
                    FLAGS_non_ha_mode_trigger_file + " exists");
  }
  if (ha_state_->GetHAMode() == EditLogConf::ActiveNonHA) {
    c->MarkAsFailed(JavaExceptions::kUnsupportedOperationException,
                    "Current HA mode is ActiveNonHA");
    return;
  }

  if (FLAGS_namenode_ha_use_async_thread) {
    thread_pool_->AddTask([rpc_done = done_guard.release(), this]() -> bool {
      if (FLAGS_ha_fast_return_when_switch_to_standby) {
        ha_state_->SetState(cloudfs::HAServiceStateProto::STANDBY, rpc_done);
      } else {
        ha_state_->SetState(cloudfs::HAServiceStateProto::STANDBY);

        rpc_done->Run();
      }

      return true;
    });
  } else {
    ha_state_->SetState(cloudfs::HAServiceStateProto::STANDBY);
  }
}

void HAService::getServiceStatus(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetServiceStatusRequestProto* request,
    ::cloudfs::GetServiceStatusResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "getServiceStatus", c, done);
  auto current_state = ha_state_->GetState();
  if (current_state == cloudfs::HAServiceStateProto::INITIALIZING) {
    response->set_state(current_state);
    return;
  }
  response->set_state(current_state);
  if (current_state == cloudfs::HAServiceStateProto::STANDBY) {
    if (safemode_->IsOn()) {
      std::string tip;
      safemode_->GetSafeModeTip(&tip);
      response->set_notreadyreason(tip);
    } else if (ha_state_->GetHAMode() != EditLogConf::HA) {
      response->set_notreadyreason("NonHA");
    } else {
      response->set_readytobecomeactive(true);
    }
  } else if (current_state == cloudfs::HAServiceStateProto::ACTIVE) {
    response->set_readytobecomeactive(true);
  } else {
    response->set_notreadyreason(
        "state is " + HAServiceStateProto_Name(current_state));
  }
  response->set_filesystemid(FLAGS_filesystem_id);
  response->set_namespaceid(FLAGS_namespace_id);
  c->set_status(RpcStatus::kSuccess);
}

static void InitMethodMetrics(
    std::shared_ptr<Metrics> metrics,
    const std::string& name,
    std::unordered_map<std::string, MethodMetric>* method_metrics) {

  MethodMetric metric;
  metric.num_ops_ = metrics->RegisterCounter("NumOps#method=" + name);
  metric.num_success_ops_ = metrics->RegisterCounter(
      "Success.NumOps#method=" + name);
  metric.duration_ = metrics->RegisterHistogram("Time#method=" + name);
  metric.success_duration_ = metrics->RegisterHistogram(
      "Success.Time#method=" + name);

  (*method_metrics)[name] = metric;
}

void HAService::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("HAService");
  InitMethodMetrics(metrics,
                    "monitorHealth",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "transitionToActive",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "transitionToStandby",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "getServiceStatus",
                    &method_metrics_);
}  // NOLINT(readability/fn_size)

}  // namespace dancenn

