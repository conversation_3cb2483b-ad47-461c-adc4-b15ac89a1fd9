// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_DATANODE_SERVICE_H_
#define SERVICE_DATANODE_SERVICE_H_

#include <DatanodeProtocol.pb.h>
#include <memory>

#include "service/method_recorder.h"
#include "service/service_meta.h"

using cloudfs::StorageInfoProto;

namespace dancenn {

class BlockManager;
class BlockReportManager;
class DatanodeManager;
class NameSpace;
class HAStateBase;

class DatanodeService : public cloudfs::datanode::DatanodeProtocolService {
 public:
  explicit DatanodeService(
    std::shared_ptr<DatanodeManager> datanode_manager,
    std::shared_ptr<BlockManager> block_map,
    std::shared_ptr<NameSpace> name_space,
    HAStateBase* ha_state);
  virtual ~DatanodeService() {
  }

  DatanodeService(const DatanodeService&) = delete;
  DatanodeService& operator=(const DatanodeService&) = delete;

  void registerDatanode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::RegisterDatanodeRequestProto* request,
      ::cloudfs::datanode::RegisterDatanodeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void sendHeartbeat(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::HeartbeatRequestProto* request,
      ::cloudfs::datanode::HeartbeatResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void blockReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::BlockReportRequestProto* request,
      ::cloudfs::datanode::BlockReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void cacheReport(  // TODO(liyuan)
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::CacheReportRequestProto* request,
      ::cloudfs::datanode::CacheReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void blockReceivedAndDeleted(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::BlockReceivedAndDeletedRequestProto* request,  // NOLINT(whitespace/line_length)
      ::cloudfs::datanode::BlockReceivedAndDeletedResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void errorReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::ErrorReportRequestProto* request,
      ::cloudfs::datanode::ErrorReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void versionRequest(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::VersionRequestProto* request,
      ::cloudfs::VersionResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void reportBadBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::ReportBadBlocksRequestProto* request,
      ::cloudfs::datanode::ReportBadBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void commitBlockSynchronization(  // TODO(liyuan)
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::datanode::CommitBlockSynchronizationRequestProto* request,  // NOLINT(whitespace/line_length)
      ::cloudfs::datanode::CommitBlockSynchronizationResponseProto* response,  // NOLINT(whitespace/line_length)
      ::google::protobuf::Closure* done) override;

  void SetMethodMetas(std::shared_ptr<ServiceMeta> sm);

 private:
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  BlockReportManager* block_report_manager_{nullptr};
  std::shared_ptr<NameSpace> namespace_;
  HAStateBase* ha_state_{nullptr};

  friend class MethodRecorder<DatanodeService>;
  std::unordered_map<std::string, MethodMetric> method_metrics_;
  MetricID mismatch_backend_count_metric_;
  MetricID empty_backend_count_metric_;

  void InitMetrics();

  bool RequestPreCheck(RpcController* c);
};

}  // namespace dancenn

#endif  // SERVICE_DATANODE_SERVICE_H_

