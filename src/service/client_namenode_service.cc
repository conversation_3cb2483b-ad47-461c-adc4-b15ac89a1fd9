// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <absl/strings/str_format.h>
#include <cnetpp/base/ip_address.h>
#include <glog/logging.h>
#include <service/client_namenode_service.h>

#include <chrono>
#include <iomanip>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "ClientNamenodeProtocol.pb.h"
#include "base/audit_logger.h"
#include "base/java_exceptions.h"
#include "base/metrics.h"
#include "base/path_util.h"
#include "base/string_utils.h"
#include "namespace/create_flag.h"
#include "rpc/rpc_controller.h"
#include "rpc/rpc_server_connection.h"
#include "xattr.pb.h"
#include "service/service_util.h"

DECLARE_int32(dfs_bytes_per_checksum);
DECLARE_int32(dfs_client_write_packet_size);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_int32(io_file_buffer_size);
DECLARE_bool(dfs_encrypt_data_transfer);
DECLARE_uint64(fs_trash_interval);
DECLARE_string(dfs_checksum_type);
DECLARE_uint64(dfs_namenode_fs_limits_min_block_size);
DECLARE_bool(dfs_support_append);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_int32(rpc_in_queue_timeout_ms_client_req);
DECLARE_bool(log_stale_read);
DECLARE_bool(standby_read_enabled_for_main_read);
DECLARE_bool(standby_read_enabled_for_all_read);
DECLARE_bool(standby_read_set_seen_txid);
DECLARE_bool(retry_cache_enabled);
DECLARE_bool(client_replication_support);
DECLARE_string(banned_user);
DECLARE_string(nameservice);
DECLARE_int64(request_ttl_since_emit_ms);
DECLARE_bool(disallow_deprecated_rpc);
DECLARE_bool(trace_rpc_log);
DECLARE_bool(complete_rpc_use_very_slow);
DECLARE_bool(dfs_ha_allow_stale_reads);

using cloudfs::HdfsFileStatusProto;
using cloudfs::StorageTypeProto;
using cloudfs::TokenProto;
using cloudfs::XAttrSetFlagProto;

#define DANCENN_AUDIT_LOG2_SIMPLE(method_)           \
  DANCENN_AUDIT_LOG2(method_,                        \
                     ugi->GetRemoteUser(),           \
                     ugi->current_group(),           \
                     GetClientAddress(c).ToString(), \
                     s,                              \
                     request,                        \
                     response);

namespace dancenn {

using MR = MethodRecorder<ClientNamenodeService>;

ClientNamenodeService::ClientNamenodeService(
    std::shared_ptr<DatanodeManager> datanode_manager,
    std::shared_ptr<BlockManager> block_manager,
    std::shared_ptr<NameSpace> name_space,
    SafeModeBase* safemode,
    RetryCache* rc)
    : datanode_manager_(datanode_manager),
      block_manager_(block_manager),
      name_space_(name_space),
      safemode_(safemode),
      retry_cache_(rc) {
  InitMetrics();
}

::google::protobuf::Closure* ClientNamenodeService::WrapClosure4Txid(
    RpcController* controller,
    ::google::protobuf::Closure* done) {
  return new WrappedDoneClosure(
      controller, done, [this](RpcController* controller) {
        if (FLAGS_standby_read_set_seen_txid) {
          auto txid = this->name_space_->GetLastAppliedOrWrittenTxId();
          controller->set_seen_txid(txid);
        }
      });
}

void ClientNamenodeService::getBlockLocations(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetBlockLocationsRequestProto* request,
    ::cloudfs::GetBlockLocationsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getBlockLocations", c, wrapped_done);
  c->AllowStandbyRead();

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  auto s = name_space_->GetBlockLocation(
      normalized_path, client_location, *request, response, *ugi, c);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException &&
        s.exception() != JavaExceptions::kReadOnlyCoolFileException &&
        s.exception() != JavaExceptions::kHyperFileException) {
      DLOG(INFO) << "get BlockLocations failed request: "
                 << request->ShortDebugString() << " returns " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetBlockLocations,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_address.ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DLOG(INFO) << "getBlockLocations response " << response->ShortDebugString();
    DANCENN_AUDIT_LOG2(AuditLog::kGetBlockLocations,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       client_address.ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::getHyperBlockLocations(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetHyperBlockLocationsRequestProto* request,
    ::cloudfs::GetHyperBlockLocationsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }


  MR recorder(this, "getHyperBlockLocations", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getServerDefaults(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetServerDefaultsRequestProto* request,
    ::cloudfs::GetServerDefaultsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getServerDefaults", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto server_default = response->mutable_serverdefaults();
  auto status = name_space_->GetServerDefaults(server_default);

  if (status.HasException()) {
    DLOG(INFO) << "getServerDefaults failed request: "
               << request->ShortDebugString() << " returns "
               << status.ToString();
    c->MarkAsFailed(status);
  } else {
    DLOG(INFO) << "getServerDefaults response " << response->ShortDebugString();
  }
}

void ClientNamenodeService::create(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CreateRequestProto* request,
    ::cloudfs::CreateResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "create", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx = c->InitRpcSwCtx("[RPC-create]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  if (request->replication() < FLAGS_dfs_replication_min ||
      request->replication() > FLAGS_dfs_replication_max) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid replication number");
    return;
  }
  if (request->blocksize() < FLAGS_dfs_namenode_fs_limits_min_block_size) {
    c->MarkAsFailed(JavaExceptions::IOException(), "Invalid block size");
    return;
  }

  PermissionStatus permission = PermissionStatus();
  permission.set_username(ugi->GetRemoteUser());
  // Follow parents' permission.
  permission.set_groupname("");
  permission.set_permission(request->masked().perm());

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();
  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Create file error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kCreate,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->clear_fs();
    } else {
      DLOG(INFO) << "Create " << request->src()
                 << " success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kCreate,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ClientNamenodeService");
  name_space_->AsyncCreateFile(normalized_path,
                               permission,
                               client_location,
                               *ugi,
                               rpc_info,
                               client_machine,
                               request,
                               response,
                               c,
                               dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::append(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AppendRequestProto* request,
    ::cloudfs::AppendResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "append", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!FLAGS_dfs_support_append) {
    c->MarkAsFailed(JavaExceptions::UnsupportedOperationException(),
                    "Append is not enabled on this NameNode, "
                    "check dfs_support_append configuration.");
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Append file error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAppend,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kAppend,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncAppend(normalized_path,
                           client_machine,
                           rpc_info,
                           *ugi,
                           request,
                           response,
                           dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::setReplication(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetReplicationRequestProto* request,
    ::cloudfs::SetReplicationResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setReplication", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!FLAGS_client_replication_support) {
    rpc_done->add_post_callback([=](const Status& s) { c->MarkAsFailed(s); });
    rpc_done->set_status(
        Status(::dancenn::JavaExceptions::kRpcNoSuchMethodException,
               Code::kFalse,
               "Set replication not support"));
    LOG(WARNING) << "Client is trying to set replication";
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "setReplication for " << normalized_path
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetReplication,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      return;
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetReplication,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }
    response->set_result(s.IsOK());
  });

  name_space_->AsyncSetReplication(
      normalized_path,
      request->replication(),
      request->allowzeroreplica(),
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::setReplicationAttrOnly(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetReplicationRequestProto* request,
    ::cloudfs::SetReplicationResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "setReplicationAttrOnly",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!FLAGS_client_replication_support) {
    rpc_done->add_post_callback([=](const Status& s) { c->MarkAsFailed(s); });
    rpc_done->set_status(
        Status(::dancenn::JavaExceptions::kRpcNoSuchMethodException,
               Code::kFalse,
               "Set replication not support"));
    LOG(WARNING) << "Client is trying to set replication";
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "setReplicationAttrOnly for " << normalized_path
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetReplicationAttrOnly,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      return;
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetReplicationAttrOnly,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    response->set_result(s.IsOK());
  });

  name_space_->AsyncSetReplication(
      normalized_path,
      request->replication(),
      request->allowzeroreplica(),
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::setStoragePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetStoragePolicyRequestProto* request,
    ::cloudfs::SetStoragePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setStoragePolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  if (request->policyname().empty()) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid policy: " + request->policyname());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Set storage policy error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetStoragePolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      return;
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetStoragePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });
  name_space_->AsyncSetStoragePolicy(
      normalized_path,
      request->policyname(),
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getStoragePolicies(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetStoragePoliciesRequestProto* request,
    ::cloudfs::GetStoragePoliciesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getStoragePolicies", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto s = name_space_->GetStoragePolicies(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "Get storage policies error: " << s.message();
      c->MarkAsFailed(s);

      auto ugi = GetRemoteUserInfo(c);
      DANCENN_AUDIT_LOG2(AuditLog::kGetStoragePolicies,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    auto ugi = GetRemoteUserInfo(c);
    DANCENN_AUDIT_LOG2(AuditLog::kGetStoragePolicies,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::setPermission(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetPermissionRequestProto* request,
    ::cloudfs::SetPermissionResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setPermission", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  // src and permission (required)
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Set Permission error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetPermission,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      return;
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetPermission,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  name_space_->AsyncSetPermission(
      normalized_path,
      request->permission().perm(),
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::setOwner(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetOwnerRequestProto* request,
    ::cloudfs::SetOwnerResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setOwner", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Set Owner error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetOwner,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetOwner,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  name_space_->AsyncSetOwner(normalized_path,
                             request->username(),
                             request->groupname(),
                             *ugi,
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::abandonBlock(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AbandonBlockRequestProto* request,
    ::cloudfs::AbandonBlockResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "abandonBlock", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  // TODO(sunguoli): holder for lease
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "abandonBlock Failed: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAbandonBlock,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kAbandonBlock,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncAbandonBlock(
      normalized_path,
      request,
      rpc_info,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::addBlock(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AddBlockRequestProto* request,
    ::cloudfs::AddBlockResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "addBlock", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx = c->InitRpcSwCtx("[RPC-addBlock]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "addBlock Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(c->rpc_sw_ctx(), "post callback");
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Add block failed," << request->ShortDebugString() << " "
                << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAddBlock,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_address.ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Add block success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kAddBlock,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_address.ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(c->rpc_sw_ctx(), "post callback finish");
    RETRY_CACHE_EXIT(s, response);
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ClientNamenodeService");
  name_space_->AsyncAddBlock(normalized_path,
                             client_location,
                             rpc_info,
                             *ugi,
                             request,
                             response,
                             c,
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::commitLastBlock(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CommitLastBlockRequestProto* request,
    ::cloudfs::CommitLastBlockResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "commitLastBlock", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "commitLastBlock Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  // Writer address is address of cfs client.
  auto writer_address = ServiceUtil::GetClientAddress(c);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "commitLastBlock failed," << request->ShortDebugString()
                << " " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kCommitLastBlock,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Add block success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kCommitLastBlock,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  name_space_->AsyncCommitLastBlock(
      normalized_path,
      writer_address,
      request,
      response,
      rpc_info,
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getAdditionalDatanode(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetAdditionalDatanodeRequestProto* request,
    ::cloudfs::GetAdditionalDatanodeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "getAdditionalDatanode",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "calling additional datanode " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Failed to get " << request->numadditionalnodes()
                << " nodes for file " << request->src();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "additional datanode " << response->ShortDebugString();
    }
  });

  name_space_->GetAdditionalDatanode(
      normalized_path,
      client_location,
      *ugi,
      request,
      response,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::complete(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CompleteRequestProto* request,
    ::cloudfs::CompleteResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "complete", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx = c->InitRpcSwCtx("[RPC-complete]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "Complete: " << request->ShortDebugString();
  response->set_result(false);
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Complete file " << normalized_path
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kComplete,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(s.IsOK());
      DLOG(INFO) << "complete response " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kComplete,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ClientNamenodeService");
  name_space_->AsyncCompleteFile(
      normalized_path,
      request,
      c,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::reportBadBlocks(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ReportBadBlocksRequestProto* request,
    ::cloudfs::ReportBadBlocksResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "reportBadBlocks", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto status = block_manager_->ReportBadBlocks(request->blocks());
  if (status.HasException()) {
    if (status.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "Report bad blocks failed," << request->ShortDebugString()
              << " " << status.ToString();
      c->MarkAsFailed(status);
      DANCENN_AUDIT_LOG2(AuditLog::kReportBadBlocks,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         status,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(status.exception(), status.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kReportBadBlocks,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       status,
                       request,
                       response);
  }
}

void ClientNamenodeService::concat(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ConcatRequestProto* request,
    ::cloudfs::ConcatResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "concat", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (request->trg().empty()) {
    c->MarkAsFailed(JavaExceptions::Exception::kIllegalArgumentException,
                    "Target file name is empty");
    return;
  }
  if (request->srcs_size() == 0) {
    c->MarkAsFailed(JavaExceptions::Exception::kIllegalArgumentException,
                    "No sources given");
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();
  const std::string& user_name = ugi->GetRemoteUser();

  std::string normalized_trg;
  std::vector<std::string> normalized_srcs;

  if (!NormalizePath(request->trg(), user_name, &normalized_trg)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid target path:" + request->trg());
    return;
  }

  for (int i = 0; i < request->srcs().size(); ++i) {
    const auto& src = request->srcs(i);
    std::string normalized_path;

    if (!NormalizePath(src, user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(), "Invalid src path:" + src);
      return;
    }
    normalized_srcs.push_back(normalized_path);
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Concat file error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kConcat,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Concat to " << request->trg()
                 << " success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kConcat,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncConcat(normalized_trg,
                           normalized_srcs,
                           rpc_info,
                           dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::rename(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RenameRequestProto* request,
    ::cloudfs::RenameResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "rename", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  response->set_result(false);
  auto ugi = GetRemoteUserInfo(c);
  auto user_name = ugi->GetRemoteUser();
  std::string src;
  std::string dst;
  if (!PathCheckBinary(
          c, user_name, request->src(), request->dst(), &src, &dst)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      response->set_result(false);
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "rename error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRename,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(s.IsOK());
      DLOG(INFO) << "Rename success src: " << src << ", dst: " << dst;
      DANCENN_AUDIT_LOG2(AuditLog::kRename,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncRenameTo(src,
                             dst,
                             rpc_info,
                             *ugi,
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::rename2(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::Rename2RequestProto* request,
    ::cloudfs::Rename2ResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "rename2", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string src;
  std::string dst;
  if (!PathCheckBinary(
          c, user_name, request->src(), request->dst(), &src, &dst)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "rename2 error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRename2,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kRename2,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncRenameTo2(src,
                              dst,
                              request->overwritedest(),
                              rpc_info,
                              *ugi,
                              dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::Delete(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::DeleteRequestProto* request,
    ::cloudfs::DeleteResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "Delete", controller, request, response, wrapped_done);

  VLOG(8) << "Delete rpc, src: " << request->src();
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  response->set_result(false);

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      response->set_result(false);
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Delete " << request->src() << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kDelete,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(!s.IsFalse());
      DANCENN_AUDIT_LOG2(AuditLog::kDelete,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncDelete(normalized_path,
                           request->recursive(),
                           *ugi,
                           rpc_info,
                           dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::mkdirs(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::MkdirsRequestProto* request,
    ::cloudfs::MkdirsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "mkdirs", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx = c->InitRpcSwCtx("[RPC-mkdirs]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");
  auto rpc_info = GetLogRpcInfo(c);

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "mkdirs called, " << request->src();  // for debug
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  PermissionStatus permission = PermissionStatus();
  permission.set_username(user_name);
  permission.set_groupname("");
  permission.set_permission(request->masked().perm());

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to mkdirs " << request->src()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kMkdirs,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->set_result(false);
    } else {
      response->set_result(true);
      DANCENN_AUDIT_LOG2(AuditLog::kMkdirs,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
    RETRY_CACHE_EXIT(s, response);
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after ClientNamenodeService");
  name_space_->AsyncMkDirs(normalized_path,
                           permission,
                           *ugi,
                           request->createparent(),
                           false,
                           c,
                           rpc_info,
                           dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getListing(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetListingRequestProto* request,
    ::cloudfs::GetListingResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto name = request->has_needlocation() && request->needlocation()
                  ? "getListingWithBlockLocations"
                  : "getListing";
  MR recorder(this, name, c, wrapped_done);
  c->AllowStandbyRead();

  if (!RequestPreCheck(c)) {
    return;
  }

  VLOG(8) << name << " rpc, src: " << request->src();

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  std::vector<uint64_t> collect_blocks;
  Status s = name_space_->GetListing(
      normalized_path, client_location, *request, response, *ugi, c);
  if (s.HasException()) {
    response->clear_dirlist();
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getListing " << request->src() << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetListing,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetListing,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::renewLease(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RenewLeaseRequestProto* request,
    ::cloudfs::RenewLeaseResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "renewLease", c, wrapped_done);

  auto client_ip = ServiceUtil::GetClientAddress(c).ToString();
  if (!RequestPreCheck(c)) {
    return;
  }

  auto s = name_space_->RenewLease(request->clientname(), client_ip);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      auto ugi = GetRemoteUserInfo(c);
      DANCENN_AUDIT_LOG2(AuditLog::kRenewLease,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    auto ugi = GetRemoteUserInfo(c);
    DANCENN_AUDIT_LOG2(AuditLog::kRenewLease,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::recoverLease(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RecoverLeaseRequestProto* request,
    ::cloudfs::RecoverLeaseResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "recoverLease", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }
  auto s = name_space_->RecoverLease(request->clientname(), normalized_path, request);

  // force set txid
  auto txid = this->name_space_->GetLastAppliedOrWrittenTxId();
  c->set_seen_txid(txid);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      auto ugi = GetRemoteUserInfo(c);
      DANCENN_AUDIT_LOG2(AuditLog::kRecoverLease,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    response->set_result(s.IsOK());
    auto ugi = GetRemoteUserInfo(c);
    DANCENN_AUDIT_LOG2(AuditLog::kRecoverLease,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::getFsStats(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetFsStatusRequestProto* request,
    ::cloudfs::GetFsStatsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getFsStats", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto s = name_space_->GetFsStats(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::getDatanodeReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDatanodeReportRequestProto* request,
    ::cloudfs::GetDatanodeReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getDatanodeReport", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  auto s = datanode_manager_->GetDatanodeReport(
      request->type(), client_location, response->mutable_di());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::getDatanodeStorageReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDatanodeStorageReportRequestProto* request,
    ::cloudfs::GetDatanodeStorageReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getDatanodeStorageReport", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  auto s = datanode_manager_->GetDatanodeStorageReport(
      request->type(),
      client_location,
      response->mutable_datanodestoragereports());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::getPreferredBlockSize(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetPreferredBlockSizeRequestProto* request,
    ::cloudfs::GetPreferredBlockSizeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getPreferredBlockSize", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->filename(), &normalized_path)) {
    return;
  }

  auto s = name_space_->GetPreferredBlockSize(normalized_path, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::setSafeMode(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetSafeModeRequestProto* request,
    ::cloudfs::SetSafeModeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "setSafeMode", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  ::cloudfs::SafeModeActionProto action = request->action();
  OperationsCategory op = OperationsCategory::kUnchecked;
  if (request->checked()) {
    if (action == ::cloudfs::SafeModeActionProto::SAFEMODE_GET) {
      op = OperationsCategory::kRead;
    } else {
      op = OperationsCategory::kWrite;
    }
  }
  auto s = safemode_->SetSafeMode(action, op);
  LOG(INFO) << "setSafeMode: result=" << s.ToString();
  if (s.HasException()) {
    c->MarkAsFailed(s);
  } else {
    response->set_result(s.IsOK() /* true means safe mode is ON */);
  }
}

void ClientNamenodeService::saveNamespace(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SaveNamespaceRequestProto* request,
    ::cloudfs::SaveNamespaceResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "saveNamespace", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::rollEdits(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RollEditsRequestProto* request,
    ::cloudfs::RollEditsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "rollEdits", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::restoreFailedStorage(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RestoreFailedStorageRequestProto* request,
    ::cloudfs::RestoreFailedStorageResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "restoreFailedStorage", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::refreshNodes(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RefreshNodesRequestProto* request,
    ::cloudfs::RefreshNodesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "refreshNodes", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  Status s = name_space_->RefreshDataNodes();
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "RefreshDataNodes error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
    return;
  }
}

void ClientNamenodeService::finalizeUpgrade(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::FinalizeUpgradeRequestProto* request,
    ::cloudfs::FinalizeUpgradeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "finalizeUpgrade", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::rollingUpgrade(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RollingUpgradeRequestProto* request,
    ::cloudfs::RollingUpgradeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "rollingUpgrade", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::listCorruptFileBlocks(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListCorruptFileBlocksRequestProto* request,
    ::cloudfs::ListCorruptFileBlocksResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCorruptFileBlocks", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::metaSave(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::MetaSaveRequestProto* request,
    ::cloudfs::MetaSaveResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "metaSave", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getFileInfo(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetFileInfoRequestProto* request,
    ::cloudfs::GetFileInfoResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getFileInfo", c, wrapped_done);
  c->AllowStandbyRead();

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "getFileInfo: " << request->src();

  auto ugi = GetRemoteUserInfo(c);
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  Status s = name_space_->GetFileInfo(
      request->src(), client_location, true, false, response, *ugi, c);

  if (s.HasException()) {
    response->clear_fs();
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getFileInfo " << request->src() << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetFileInfo,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetFileInfo,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::addCacheDirective(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AddCacheDirectiveRequestProto* request,
    ::cloudfs::AddCacheDirectiveResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "addCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::modifyCacheDirective(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ModifyCacheDirectiveRequestProto* request,
    ::cloudfs::ModifyCacheDirectiveResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::removeCacheDirective(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveCacheDirectiveRequestProto* request,
    ::cloudfs::RemoveCacheDirectiveResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::listCacheDirectives(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListCacheDirectivesRequestProto* request,
    ::cloudfs::ListCacheDirectivesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCacheDirectives", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::addCachePool(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AddCachePoolRequestProto* request,
    ::cloudfs::AddCachePoolResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "addCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::modifyCachePool(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ModifyCachePoolRequestProto* request,
    ::cloudfs::ModifyCachePoolResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::removeCachePool(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveCachePoolRequestProto* request,
    ::cloudfs::RemoveCachePoolResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::listCachePools(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListCachePoolsRequestProto* request,
    ::cloudfs::ListCachePoolsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCachePools", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getFileLinkInfo(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetFileLinkInfoRequestProto* request,
    ::cloudfs::GetFileLinkInfoResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getFileLinkInfo", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getContentSummary(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetContentSummaryRequestProto* request,
    ::cloudfs::GetContentSummaryResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getContentSummary", c, wrapped_done);
  c->AllowStandbyRead();

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  Status s = name_space_->GetContentSummary(normalized_path, ugi, response, c);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getContentSummary" << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetContentSummary,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
    response->Clear();
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetContentSummary,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::setQuota(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetQuotaRequestProto* request,
    ::cloudfs::SetQuotaResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setQuota", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::fsync(::google::protobuf::RpcController* controller,
                                  const ::cloudfs::FsyncRequestProto* request,
                                  ::cloudfs::FsyncResponseProto* response,
                                  ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "fsync", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = GetLogRpcInfo(c);
  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Failed to fsync " << request->src()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kFsync,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kFsync,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncFsync(normalized_path,
                          request,
                          rpc_info,
                          dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::setTimes(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetTimesRequestProto* request,
    ::cloudfs::SetTimesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setTimes", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "setTimes " << request->src() << " failed: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    }
  });

  name_space_->AsyncSetTimes(normalized_path,
                             request,
                             *ugi,
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::createSymlink(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CreateSymlinkRequestProto* request,
    ::cloudfs::CreateSymlinkResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "createSymlink", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!FLAGS_dfs_symlinks_enabled) {
    c->MarkAsFailed(JavaExceptions::UnsupportedOperationException(),
                    "Symlinks not supported");
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_link;
  std::string normalized_target;
  if (!PathCheckBinary(c,
                       user_name,
                       request->link(),
                       request->target(),
                       &normalized_link,
                       &normalized_target)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  PermissionStatus perm;
  // in Java NameNode set username, but no groupname
  perm.set_username(user_name);
  perm.set_groupname("");
  perm.set_permission(request->dirperm().perm());

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncCreateSymlink(
      normalized_target,
      normalized_link,
      perm,
      *ugi,
      request->createparent(),
      rpc_info,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getLinkTarget(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetLinkTargetRequestProto* request,
    ::cloudfs::GetLinkTargetResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getLinkTarget", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::updateBlockForPipeline(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::UpdateBlockForPipelineRequestProto* request,
    ::cloudfs::UpdateBlockForPipelineResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "updateBlockForPipeline",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  LogRpcInfo rpc_info(c->rpc_request_header()->clientid(),
                      static_cast<uint32_t>(c->rpc_request_header()->callid()),
                      false);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "UpdateBlockForPipeline error: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "UpdateBlockForPipeline successfully"
                 << response->ShortDebugString();
    }
  });

  auto ugi = GetRemoteUserInfo(c);

  name_space_->AsyncUpdateBlockForPipeline(
      request,
      rpc_info,
      response,
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::updatePipeline(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::UpdatePipelineRequestProto* request,
    ::cloudfs::UpdatePipelineResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "updatePipeline", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Update pipeline error: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Update pipeline successfully"
                 << response->ShortDebugString();
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncUpdatePipeline(
      request, rpc_info, dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getDelegationToken(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDelegationTokenRequestProto* request,
    ::cloudfs::GetDelegationTokenResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::renewDelegationToken(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RenewDelegationTokenRequestProto* request,
    ::cloudfs::RenewDelegationTokenResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "renewDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::cancelDelegationToken(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CancelDelegationTokenRequestProto* request,
    ::cloudfs::CancelDelegationTokenResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "cancelDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::setBalancerBandwidth(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetBalancerBandwidthRequestProto* request,
    ::cloudfs::SetBalancerBandwidthResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setBalancerBandwidth", c, wrapped_done);
  // Not Implemented
}

void ClientNamenodeService::getDataEncryptionKey(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDataEncryptionKeyRequestProto* request,
    ::cloudfs::GetDataEncryptionKeyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getDataEncryptionKey", c, wrapped_done);
  response->Clear();
}

void ClientNamenodeService::createSnapshot(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CreateSnapshotRequestProto* request,
    ::cloudfs::CreateSnapshotResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "createSnapshot", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  const std::string& user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->snapshotroot(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->snapshotroot());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to createSnapshot " << request->snapshotroot()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kCreateSnapshot);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kCreateSnapshot);
    }
  });

  name_space_->AsyncCreateSnapshot(
      normalized_path,
      request->snapshotname(),
      response,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::renameSnapshot(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RenameSnapshotRequestProto* request,
    ::cloudfs::RenameSnapshotResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "renameSnapshot", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  const std::string& user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->snapshotroot(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->snapshotroot());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to renameSnapshot " << request->snapshotroot()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kRenameSnapshot);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kRenameSnapshot);
    }
  });

  name_space_->AsyncRenameSnapshot(
      normalized_path,
      request->snapshotoldname(),
      request->snapshotnewname(),
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::allowSnapshot(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AllowSnapshotRequestProto* request,
    ::cloudfs::AllowSnapshotResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "allowSnapshot", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  const std::string& user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->snapshotroot(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->snapshotroot());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to allowSnapshot " << request->snapshotroot()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kAllowSnapshot);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kAllowSnapshot);
    }
  });

  name_space_->AsyncAllowSnapshot(
      normalized_path, dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::disallowSnapshot(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::DisallowSnapshotRequestProto* request,
    ::cloudfs::DisallowSnapshotResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "disallowSnapshot", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  const std::string& user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->snapshotroot(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->snapshotroot());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to disallowSnapshot " << request->snapshotroot()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kDisallowSnapshot);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kDisallowSnapshot);
    }
  });

  name_space_->AsyncDisAllowSnapshot(
      normalized_path, dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getSnapshottableDirListing(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetSnapshottableDirListingRequestProto* request,
    ::cloudfs::GetSnapshottableDirListingResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getSnapshottableDirListing", c, wrapped_done);
  auto ugi = GetRemoteUserInfo(c);
  Status s = name_space_->GetSnapshottableDirListing(
      response->mutable_snapshottabledirlist(), c);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getSnapshottableDirListing error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kGetSnapshottableDirListing);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kGetSnapshottableDirListing);
  }
}

void ClientNamenodeService::deleteSnapshot(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::DeleteSnapshotRequestProto* request,
    ::cloudfs::DeleteSnapshotResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "deleteSnapshot", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  const std::string& user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->snapshotroot(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->snapshotroot());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Failed to deleteSnapshot " << request->snapshotroot()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kDeleteSnapshot);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kDeleteSnapshot);
    }
  });

  name_space_->AsyncDeleteSnapshot(
      normalized_path,
      request->snapshotname(),
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getSnapshotDiffReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetSnapshotDiffReportRequestProto* request,
    ::cloudfs::GetSnapshotDiffReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getSnapshotDiffReport", c, wrapped_done);
  auto ugi = GetRemoteUserInfo(c);
  Status s = name_space_->GetSnapshotDiff(request->snapshotroot(),
                                          request->fromsnapshot(),
                                          request->tosnapshot(),
                                          response->mutable_diffreport(),
                                          c);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getSnapshotDiffReport " << request->snapshotroot()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kGetSnapshotDiffReport);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2_SIMPLE(AuditLog::kGetSnapshotDiffReport);
  }
}

void ClientNamenodeService::isFileClosed(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::IsFileClosedRequestProto* request,
    ::cloudfs::IsFileClosedResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "isFileClosed", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto s = name_space_->IsFileClosed(normalized_path, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kIsFileClosed,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kIsFileClosed,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::modifyAclEntries(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ModifyAclEntriesRequestProto* request,
    ::cloudfs::ModifyAclEntriesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyAclEntries", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::removeAclEntries(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveAclEntriesRequestProto* request,
    ::cloudfs::RemoveAclEntriesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeAclEntries", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::removeDefaultAcl(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveDefaultAclRequestProto* request,
    ::cloudfs::RemoveDefaultAclResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeDefaultAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::removeAcl(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveAclRequestProto* request,
    ::cloudfs::RemoveAclResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::setAcl(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetAclRequestProto* request,
    ::cloudfs::SetAclResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getAclStatus(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetAclStatusRequestProto* request,
    ::cloudfs::GetAclStatusResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getAclStatus", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::setXAttr(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetXAttrRequestProto* request,
    ::cloudfs::SetXAttrResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setXAttr", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    }

    RETRY_CACHE_EXIT(s, response);
  });

  bool create = (request->flag() & XAttrSetFlagProto::XATTR_CREATE) ==
                XAttrSetFlagProto::XATTR_CREATE;
  bool replace = (request->flag() & XAttrSetFlagProto::XATTR_REPLACE) ==
                 XAttrSetFlagProto::XATTR_REPLACE;

  name_space_->AsyncSetXAttr(normalized_path,
                             request->xattr(),
                             create,
                             replace,
                             rpc_info,
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getXAttrs(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetXAttrsRequestProto* request,
    ::cloudfs::GetXAttrsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getXAttrs", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto s = name_space_->GetXAttrs(
      normalized_path, request->xattrs(), response->mutable_xattrs(), *ugi);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::listXAttrs(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListXAttrsRequestProto* request,
    ::cloudfs::ListXAttrsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listXAttrs", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto s = name_space_->ListXAttrs(
      normalized_path, response->mutable_xattrs(), *ugi);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::removeXAttr(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveXAttrRequestProto* request,
    ::cloudfs::RemoveXAttrResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "removeXAttr", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->src(), &normalized_path)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncRemoveXAttr(
      normalized_path,
      request->xattr(),
      rpc_info,
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::checkAccess(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CheckAccessRequestProto* request,
    ::cloudfs::CheckAccessResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "checkAccess", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  auto s = name_space_->CheckAccess(ugi, request, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::createEncryptionZone(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CreateEncryptionZoneRequestProto* request,
    ::cloudfs::CreateEncryptionZoneResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "createEncryptionZone", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::listEncryptionZones(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListEncryptionZonesRequestProto* request,
    ::cloudfs::ListEncryptionZonesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listEncryptionZones", c, wrapped_done);
  response->Clear();
}

void ClientNamenodeService::getEZForPath(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetEZForPathRequestProto* request,
    ::cloudfs::GetEZForPathResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getEZForPath", c, wrapped_done);
  response->Clear();
}

void ClientNamenodeService::getCurrentEditLogTxid(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetCurrentEditLogTxidRequestProto* request,
    ::cloudfs::GetCurrentEditLogTxidResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getCurrentEditLogTxid", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getEditsFromTxid(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetEditsFromTxidRequestProto* request,
    ::cloudfs::GetEditsFromTxidResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getEditsFromTxid", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::increaseAccessCounter(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::IncreaseAccessCounterRequestProto* request,
    ::cloudfs::IncreaseAccessCounterResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "increaseAccessCounter", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::getAccessCounterValues(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetAccessCounterValuesRequestProto* request,
    ::cloudfs::GetAccessCounterValuesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getAccessCounterValues", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::setDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetDirPolicyRequestProto* request,
    ::cloudfs::SetDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "set dir policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetDirPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  name_space_->AsyncSetDirPolicy(
      normalized_path,
      request,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::removeDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveDirPolicyRequestProto* request,
    ::cloudfs::RemoveDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "removeDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "set replica policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRemoveDirPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kRemoveDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  name_space_->AsyncRemoveDirPolicy(
      normalized_path,
      request,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDirPolicyRequestProto* request,
    ::cloudfs::GetDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "getDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  Status s = name_space_->GetDirPolicy(normalized_path, request, response);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException &&
        s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
      VLOG(8) << "get dir policy " << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetDirPolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::listDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListDirPolicyRequestProto* request,
    ::cloudfs::ListDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listDirPolicy", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  Status s = name_space_->ListDirPolicy(request, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "listDirPolicy "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

// Deprecate: remove API
void ClientNamenodeService::setReplicaPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetReplicaPolicyRequestProto* request,
    ::cloudfs::SetReplicaPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setReplicaPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "set replica policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetReplicaPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetReplicaPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  // backwards compatible
  ReplicaPolicy policy;
  if (request->has_policy()) {
    name_space_->AsyncSetReplicaPolicy(
        normalized_path,
        policy,
        dynamic_cast<RpcClosure*>(done_guard.release()));
  } else {
    if (request->id() == kNonePolicy) {
      name_space_->AsyncRemoveReplicaPolicy(
          normalized_path, dynamic_cast<RpcClosure*>(done_guard.release()));
    } else {
      policy.set_distributed(request->id() == kDistributePolicy);
      auto dcs = StringUtils::SplitByChars(request->dc(), ",");
      for (auto d : dcs) {
        policy.add_dc(d);
      }
      name_space_->AsyncSetReplicaPolicy(
          normalized_path,
          policy,
          dynamic_cast<RpcClosure*>(done_guard.release()));
    }
  }
}

// Deprecate: remove API
void ClientNamenodeService::getReplicaPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetReplicaPolicyRequestProto* request,
    ::cloudfs::GetReplicaPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getReplicaPolicy", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  Status s = name_space_->GetReplicaPolicy(normalized_path, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "get replica policy " << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetReplicaPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetReplicaPolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

// Deprecate: remove API
void ClientNamenodeService::getDistributed(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDistributedRequestProto* request,
    ::cloudfs::GetDistributedResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getDistributed", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  Status s = name_space_->GetDistributed(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "get distributed "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

// Deprecate: remove API
void ClientNamenodeService::setReadPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetReadPolicyRequestProto* request,
    ::cloudfs::SetReadPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setReadPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  auto request_debug_string = request->ShortDebugString();
  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "set read policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetReadPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetReadPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  if (request->has_policy()) {
    name_space_->AsyncSetReadPolicy(
        normalized_path,
        request->policy(),
        dynamic_cast<RpcClosure*>(done_guard.release()));
  } else {
    name_space_->AsyncRemoveReadPolicy(
        normalized_path, dynamic_cast<RpcClosure*>(done_guard.release()));
  }
}

// Deprecate: remove API
void ClientNamenodeService::getReadPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetReadPolicyRequestProto* request,
    ::cloudfs::GetReadPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getReadPolicy", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!PathCheckUnary(c, user_name, request->path(), &normalized_path)) {
    return;
  }

  Status s = name_space_->GetReadPolicy(normalized_path, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "get read policy " << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetReadPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    auto policy_debug_string = response->policy().ShortDebugString();
    DANCENN_AUDIT_LOG2(AuditLog::kGetReadPolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

// Deprecate: remove API
void ClientNamenodeService::listReadPolicies(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListReadPoliciesRequestProto* request,
    ::cloudfs::ListReadPoliciesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listReadPolicies", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  Status s = name_space_->ListReadPolicies(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "list read policies "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::addCompleteBlocksAndCloseFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AddCompleteBlocksAndCloseFileRequestProto* request,
    ::cloudfs::AddCompleteBlocksAndCloseFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  // TODO(yangjinfeng.02)
  MR recorder(this, "addCompleteBlocksAndCloseFile", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::msync(::google::protobuf::RpcController* controller,
                                  const ::cloudfs::MsyncRequestProto* request,
                                  ::cloudfs::MsyncResponseProto* response,
                                  ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "msync", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  Status s = name_space_->Msync(response);

  // force set txid
  auto txid = this->name_space_->GetLastAppliedOrWrittenTxId();
  c->set_seen_txid(txid);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "msync error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kMsync,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kMsync,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::getHAServiceState(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::HAServiceStateRequestProto* request,
    ::cloudfs::HAServiceStateResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getHAServiceState", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  Status s = name_space_->GetHAServiceState(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getHAServiceState error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetHAServiceState,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetHAServiceState,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void ClientNamenodeService::getBlockKeys(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetBlockKeysRequestProto* request,
    ::cloudfs::GetBlockKeysResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "getBlockKeys", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  Status s = name_space_->GetBlockKeys(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getBlockKeys "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

void ClientNamenodeService::getINodeStat(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetINodeStatRequestProto* request,
    ::cloudfs::GetINodeStatResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "getINodeStat", c, done);

  Status s = name_space_->GetDirectoryStats(request->inodeids(), response);
  if (s.HasException()) {
    c->MarkAsFailed(s);
  }
}

void ClientNamenodeService::setLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetLifecyclePolicyRequestProto* request,
    ::cloudfs::SetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setLifecyclePolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("setLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto rpc_info = GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetLifecyclePolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      VLOG(8) << absl::StrFormat(
          "setLifecyclePolicy failed, response %s, st %s",
          response->ShortDebugString(), s.ToString());
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
      VLOG(8) << absl::StrFormat(
          "setLifecyclePolicy succeed, response %s, st %s",
          response->ShortDebugString(), s.ToString());
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncSetLifecyclePolicy(normalized_path,
                                       *request,
                                       response,
                                       *ugi,
                                       rpc_info,
                                       dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::unsetLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::UnsetLifecyclePolicyRequestProto* request,
    ::cloudfs::UnsetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "unsetLifecyclePolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("unsetLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto rpc_info = GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kUnsetLifecyclePolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      VLOG(8) << absl::StrFormat(
          "unsetLifecyclePolicy failed, response %s, st %s",
          response->ShortDebugString(), s.ToString());
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kUnsetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
      VLOG(8) << absl::StrFormat(
          "unsetLifecyclePolicy succeed, response %s, st %s",
          response->ShortDebugString(), s.ToString());
    }

    RETRY_CACHE_EXIT(s, response);
  });

  name_space_->AsyncUnsetLifecyclePolicy(normalized_path,
                                         *request,
                                         response,
                                         *ugi,
                                         rpc_info,
                                         dynamic_cast<RpcClosure*>(done_guard.release()));
}

void ClientNamenodeService::getLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetLifecyclePolicyRequestProto* request,
    ::cloudfs::GetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getLifecyclePolicy", c, wrapped_done);
  c->AllowStandbyRead();

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("getLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto s = name_space_->GetLifecyclePolicy(normalized_path,
                                           response->mutable_lifecyclepolicy(),
                                           *ugi,
                                           c);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
    VLOG(8) << absl::StrFormat(
        "getLifecyclePolicy failed, response %s, st %s",
        response->ShortDebugString(), s.ToString());
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetLifecyclePolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
    VLOG(8) << absl::StrFormat(
        "getLifecyclePolicy failed, response %s, st %s",
        response->ShortDebugString(), s.ToString());
  }
}

void ClientNamenodeService::load(::google::protobuf::RpcController* controller,
                                 const ::cloudfs::LoadRequestProto* request,
                                 ::cloudfs::LoadResponseProto* response,
                                 ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "load", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::free(::google::protobuf::RpcController* controller,
                                 const ::cloudfs::FreeRequestProto* request,
                                 ::cloudfs::FreeResponseProto* response,
                                 ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "free", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::lookupJob(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::LookupJobRequestProto* request,
    ::cloudfs::LookupJobResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "lookupJob", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void ClientNamenodeService::cancelJob(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CancelJobRequestProto* request,
    ::cloudfs::CancelJobResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "cancelJob", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

static void InitMethodMetrics(
    std::shared_ptr<Metrics> metrics,
    const std::string& name,
    std::unordered_map<std::string, MethodMetric>* method_metrics) {
  MethodMetric metric;
  metric.num_ops_ = metrics->RegisterCounter("NumOps#method=" + name);
  metric.num_success_ops_ =
      metrics->RegisterCounter("Success.NumOps#method=" + name);
  metric.duration_ = metrics->RegisterHistogram("Time#method=" + name);
  metric.success_duration_ =
      metrics->RegisterHistogram("Success.Time#method=" + name);

  (*method_metrics)[name] = metric;
}

void ClientNamenodeService::SetMethodMetas(std::shared_ptr<ServiceMeta> sm) {
  sm->AddMethodMeta("getListing",
                    MethodMetaBuilder()
                        .SetName("getListing")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDatanodeReport",
                    MethodMetaBuilder()
                        .SetName("getDatanodeReport")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDatanodeStorageReport",
                    MethodMetaBuilder()
                        .SetName("getDatanodeStorageReport")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getContentSummary",
                    MethodMetaBuilder()
                        .SetName("getContentSummary")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());

  sm->AddMethodMeta("recoverLease",
                    MethodMetaBuilder()
                        .SetName("recoverLease")
                        .SetType(MethodMeta::MethodType::kRecoverLease)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("create",
                    MethodMetaBuilder()
                        .SetName("create")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("append",
                    MethodMetaBuilder()
                        .SetName("append")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplication",
                    MethodMetaBuilder()
                        .SetName("setReplication")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplicationAttrOnly",
                    MethodMetaBuilder()
                        .SetName("setReplicationAttrOnly")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setStoragePolicy",
                    MethodMetaBuilder()
                        .SetName("setStoragePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setPermission",
                    MethodMetaBuilder()
                        .SetName("setPermission")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setOwner",
                    MethodMetaBuilder()
                        .SetName("setOwner")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("abandonBlock",
                    MethodMetaBuilder()
                        .SetName("abandonBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("addBlock",
                    MethodMetaBuilder()
                        .SetName("addBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("commitLastBlock",
                    MethodMetaBuilder()
                        .SetName("addBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  auto complete_type = MethodMeta::MethodType::kSlow;
  if (FLAGS_complete_rpc_use_very_slow) {
    complete_type = MethodMeta::MethodType::kVerySlow;
  }
  sm->AddMethodMeta("complete",
                    MethodMetaBuilder()
                        .SetName("complete")
                        .SetType(complete_type)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("concat",
                    MethodMetaBuilder()
                        .SetName("concat")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("rename",
                    MethodMetaBuilder()
                        .SetName("rename")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("rename2",
                    MethodMetaBuilder()
                        .SetName("rename2")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("Delete",
                    MethodMetaBuilder()
                        .SetName("Delete")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("mkdirs",
                    MethodMetaBuilder()
                        .SetName("mkdirs")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("fsync",
                    MethodMetaBuilder()
                        .SetName("fsync")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setTimes",
                    MethodMetaBuilder()
                        .SetName("setTimes")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("updateBlockForPipeline",
                    MethodMetaBuilder()
                        .SetName("updateBlockForPipeline")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("updatePipeline",
                    MethodMetaBuilder()
                        .SetName("updatePipeline")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setXAttr",
                    MethodMetaBuilder()
                        .SetName("setXAttr")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("removeXAttr",
                    MethodMetaBuilder()
                        .SetName("removeXAttr")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("increaseAccessCounter",
                    MethodMetaBuilder()
                        .SetName("increaseAccessCounter")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplicaPolicy",
                    MethodMetaBuilder()
                        .SetName("setReplicaPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReadPolicy",
                    MethodMetaBuilder()
                        .SetName("setReadPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setDirPolicy",
                    MethodMetaBuilder()
                        .SetName("setDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("removeDirPolicy",
                    MethodMetaBuilder()
                        .SetName("removeDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDirPolicy",
                    MethodMetaBuilder()
                        .SetName("getDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("listDirPolicy",
                    MethodMetaBuilder()
                        .SetName("listDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("addCompleteBlocksAndCloseFile",
                    MethodMetaBuilder()
                        .SetName("addCompleteBlocksAndCloseFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("createSnapshot",
                    MethodMetaBuilder()
                        .SetName("createSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("renameSnapshot",
                    MethodMetaBuilder()
                        .SetName("renameSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("allowSnapshot",
                    MethodMetaBuilder()
                        .SetName("allowSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("disallowSnapshot",
                    MethodMetaBuilder()
                        .SetName("disallowSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("deleteSnapshot",
                    MethodMetaBuilder()
                        .SetName("deleteSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("setLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("unsetLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("unsetLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("getLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("load",
                    MethodMetaBuilder()
                        .SetName("load")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("free",
                    MethodMetaBuilder()
                        .SetName("free")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("lookupJob",
                    MethodMetaBuilder()
                        .SetName("lookupJob")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("cancelJob",
                    MethodMetaBuilder()
                        .SetName("cancelJob")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchCreateFile",
                    MethodMetaBuilder()
                        .SetName("batchCreateFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchCompleteFile",
                    MethodMetaBuilder()
                        .SetName("batchCompleteFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchDeleteFile",
                    MethodMetaBuilder()
                        .SetName("batchDeleteFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchGetFile",
                    MethodMetaBuilder()
                        .SetName("batchGetFile")
                        .SetType(MethodMeta::MethodType::kNormal)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
}

void ClientNamenodeService::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("ClientNameNodeService");
  InitMethodMetrics(metrics, "msync", &method_metrics_);
  InitMethodMetrics(metrics, "getHAServiceState", &method_metrics_);
  InitMethodMetrics(metrics, "getBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "getHyperBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "getServerDefaults", &method_metrics_);
  InitMethodMetrics(metrics, "create", &method_metrics_);
  InitMethodMetrics(metrics, "append", &method_metrics_);
  InitMethodMetrics(metrics, "setReplication", &method_metrics_);
  InitMethodMetrics(metrics, "setReplicationAttrOnly", &method_metrics_);
  InitMethodMetrics(metrics, "setStoragePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getStoragePolicies", &method_metrics_);
  InitMethodMetrics(metrics, "setPermission", &method_metrics_);
  InitMethodMetrics(metrics, "setOwner", &method_metrics_);
  InitMethodMetrics(metrics, "abandonBlock", &method_metrics_);
  InitMethodMetrics(metrics, "addBlock", &method_metrics_);
  InitMethodMetrics(metrics, "commitLastBlock", &method_metrics_);
  InitMethodMetrics(metrics, "getAdditionalDatanode", &method_metrics_);
  InitMethodMetrics(metrics, "complete", &method_metrics_);
  InitMethodMetrics(metrics, "reportBadBlocks", &method_metrics_);
  InitMethodMetrics(metrics, "concat", &method_metrics_);
  InitMethodMetrics(metrics, "rename", &method_metrics_);
  InitMethodMetrics(metrics, "rename2", &method_metrics_);
  InitMethodMetrics(metrics, "Delete", &method_metrics_);
  InitMethodMetrics(metrics, "mkdirs", &method_metrics_);
  InitMethodMetrics(metrics, "getListing", &method_metrics_);
  InitMethodMetrics(metrics, "getListingWithBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "renewLease", &method_metrics_);
  InitMethodMetrics(metrics, "recoverLease", &method_metrics_);
  InitMethodMetrics(metrics, "getFsStats", &method_metrics_);
  InitMethodMetrics(metrics, "getDatanodeReport", &method_metrics_);
  InitMethodMetrics(metrics, "getDatanodeStorageReport", &method_metrics_);
  InitMethodMetrics(metrics, "getPreferredBlockSize", &method_metrics_);
  InitMethodMetrics(metrics, "setSafeMode", &method_metrics_);
  InitMethodMetrics(metrics, "saveNamespace", &method_metrics_);
  InitMethodMetrics(metrics, "rollEdits", &method_metrics_);
  InitMethodMetrics(metrics, "restoreFailedStorage", &method_metrics_);
  InitMethodMetrics(metrics, "refreshNodes", &method_metrics_);
  InitMethodMetrics(metrics, "finalizeUpgrade", &method_metrics_);
  InitMethodMetrics(metrics, "rollingUpgrade", &method_metrics_);
  InitMethodMetrics(metrics, "listCorruptFileBlocks", &method_metrics_);
  InitMethodMetrics(metrics, "metaSave", &method_metrics_);
  InitMethodMetrics(metrics, "getFileInfo", &method_metrics_);
  InitMethodMetrics(metrics, "addCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "modifyCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "removeCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "listCacheDirectives", &method_metrics_);
  InitMethodMetrics(metrics, "addCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "modifyCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "removeCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "listCachePools", &method_metrics_);
  InitMethodMetrics(metrics, "getFileLinkInfo", &method_metrics_);
  InitMethodMetrics(metrics, "getContentSummary", &method_metrics_);
  InitMethodMetrics(metrics, "setQuota", &method_metrics_);
  InitMethodMetrics(metrics, "fsync", &method_metrics_);
  InitMethodMetrics(metrics, "setTimes", &method_metrics_);
  InitMethodMetrics(metrics, "createSymlink", &method_metrics_);
  InitMethodMetrics(metrics, "getLinkTarget", &method_metrics_);
  InitMethodMetrics(metrics, "updateBlockForPipeline", &method_metrics_);
  InitMethodMetrics(metrics, "updatePipeline", &method_metrics_);
  InitMethodMetrics(metrics, "getDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "renewDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "cancelDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "setBalancerBandwidth", &method_metrics_);
  InitMethodMetrics(metrics, "getDataEncryptionKey", &method_metrics_);
  InitMethodMetrics(metrics, "createSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "renameSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "allowSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "disallowSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "getSnapshottableDirListing", &method_metrics_);
  InitMethodMetrics(metrics, "deleteSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "getSnapshotDiffReport", &method_metrics_);
  InitMethodMetrics(metrics, "isFileClosed", &method_metrics_);
  InitMethodMetrics(metrics, "modifyAclEntries", &method_metrics_);
  InitMethodMetrics(metrics, "removeAclEntries", &method_metrics_);
  InitMethodMetrics(metrics, "removeDefaultAcl", &method_metrics_);
  InitMethodMetrics(metrics, "removeAcl", &method_metrics_);
  InitMethodMetrics(metrics, "setAcl", &method_metrics_);
  InitMethodMetrics(metrics, "getAclStatus", &method_metrics_);
  InitMethodMetrics(metrics, "setXAttr", &method_metrics_);
  InitMethodMetrics(metrics, "getXAttrs", &method_metrics_);
  InitMethodMetrics(metrics, "listXAttrs", &method_metrics_);
  InitMethodMetrics(metrics, "removeXAttr", &method_metrics_);
  InitMethodMetrics(metrics, "checkAccess", &method_metrics_);
  InitMethodMetrics(metrics, "createEncryptionZone", &method_metrics_);
  InitMethodMetrics(metrics, "listEncryptionZones", &method_metrics_);
  InitMethodMetrics(metrics, "getEZForPath", &method_metrics_);
  InitMethodMetrics(metrics, "getCurrentEditLogTxid", &method_metrics_);
  InitMethodMetrics(metrics, "getEditsFromTxid", &method_metrics_);
  InitMethodMetrics(metrics, "increaseAccessCounter", &method_metrics_);
  InitMethodMetrics(metrics, "getAccessCounterValues", &method_metrics_);
  InitMethodMetrics(metrics, "setDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "removeDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "listDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "setReplicaPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getReplicaPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getDistributed", &method_metrics_);
  InitMethodMetrics(metrics, "setReadPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getReadPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getBlockKeys", &method_metrics_);
  InitMethodMetrics(metrics, "listReadPolicies", &method_metrics_);
  InitMethodMetrics(metrics, "addCompleteBlocksAndCloseFile", &method_metrics_);
  InitMethodMetrics(metrics, "getINodeStat", &method_metrics_);
  InitMethodMetrics(metrics, "setLifecyclePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "unsetLifecyclePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getLifecyclePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "load", &method_metrics_);
  InitMethodMetrics(metrics, "free", &method_metrics_);
  InitMethodMetrics(metrics, "lookupJob", &method_metrics_);
  InitMethodMetrics(metrics, "cancelJob", &method_metrics_);
  InitMethodMetrics(metrics, "batchCreateFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchCompleteFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchDeleteFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchGetFile", &method_metrics_);

  mismatch_backend_count_metric_ = metrics->RegisterCounter("mismatchBackend");
  empty_backend_count_metric_ = metrics->RegisterCounter("emptyBackend");
  request_exceeds_ttl_count_metric_ =
      metrics->RegisterCounter("requestExceedsTTL");
}  // NOLINT(readability/fn_size)

cnetpp::base::IPAddress ClientNamenodeService::GetClientAddress(
    RpcController* c) {
  // Get client ip from rpc request header
  cnetpp::base::IPAddress client_address;
  if (!c->rpc_request_header()->has_clientaddress() ||
      !cnetpp::base::IPAddress::LiteralToNumber(
          c->rpc_request_header()->clientaddress(), &client_address)) {
    client_address =
        c->rpc_connection()->tcp_connection()->remote_end_point().address();
  }
  // handle IPv6
  if (client_address.IsIPv4Mapped()) {
    // ad-hoc parse ipv4 mapped ipv6
    cnetpp::base::IPAddress real_ip;
    client_address.GetIPv4FromIPv6(&real_ip);
    return real_ip;
  } else {
    return client_address;
  }
}

LogRpcInfo ClientNamenodeService::GetLogRpcInfo(RpcController* c) {
  auto original_client_id = c->rpc_request_header()->clientid();
  auto original_call_id =
      static_cast<uint32_t>(c->rpc_request_header()->callid());
  std::string baggage_client_id_raw;
  std::string baggage_client_id;
  uint32_t baggage_call_id = 0;
  auto client_id = original_client_id;
  auto call_id = original_call_id;
  CHECK_EQ(original_client_id.size(), 16);

  bool flag_log = false;

  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "clientId" && b.has_value()) {
      baggage_client_id = b.value();
      if (!StringUtils::IsHexString(baggage_client_id)) {
        LOG(ERROR) << "baggage have clientId, but not HexString";
        flag_log = true;
        continue;
      }
      baggage_client_id_raw = StringUtils::HexStringTo(baggage_client_id);
      if (baggage_client_id_raw.size() != 16) {
        LOG(ERROR) << "baggage have HexString clientId, but length error";
        flag_log = true;
        continue;
      }
      client_id = baggage_client_id_raw;
    }
    if (b.has_name() && b.name() == "callId" && b.has_value()) {
      try {
        baggage_call_id = stoi(b.value());
        call_id = baggage_call_id;
      } catch (...) {
        LOG(ERROR) << "callId in baggages is not a number. callId="
                   << b.value();
        flag_log = true;
      }
    }
  }

  if (VLOG_IS_ON(12) || flag_log) {
    auto proxy_address = c->rpc_connection()
                             ->tcp_connection()
                             ->remote_end_point()
                             .address()
                             .ToString();
    LOG(INFO) << "GetLogRpcInfo "
              << " | "
              << " proxy_address=" << proxy_address
              << " client_address=" << GetClientAddress(c).ToString() << " | "
              << " original_client_id(" << original_client_id.size()
              << ")=" << StringUtils::ToHexString(original_client_id)
              << " original_call_id=" << original_call_id << " | "
              << " baggage_client_id(" << baggage_client_id.size()
              << ")=" << baggage_client_id << " baggage_client_id_raw("
              << baggage_client_id_raw.size()
              << ")=" << StringUtils::ToHexString(baggage_client_id_raw)
              << " baggage_call_id=" << baggage_call_id << " | "
              << " client_id(" << client_id.size()
              << ")=" << StringUtils::ToHexString(client_id)
              << " call_id=" << call_id << " | "
              << "\nheader=" << c->rpc_request_header()->ShortDebugString();
  }
  CHECK_EQ(client_id.size(), 16);
  return LogRpcInfo(std::move(client_id), call_id);
}

bool ClientNamenodeService::PathCheckUnary(RpcController* c,
                                           const std::string& user_name,
                                           const std::string& path,
                                           std::string* normalized_path) {
  CHECK_NOTNULL(normalized_path);

  if (!StringUtils::IsValidUTF8(user_name)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid UTF8 char in username: " + user_name);
    return false;
  }

  if (!FLAGS_banned_user.empty() && user_name == FLAGS_banned_user) {
    std::string msg = "User is banned, user_name: " + user_name;
    LOG(WARNING) << msg;
    c->MarkAsFailed(JavaExceptions::IOException(), msg);
    return false;
  }

  if (!StringUtils::IsValidUTF8(path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid UTF8 char in path: " + path);
    return false;
  }

  if (!NormalizePath(path, user_name, normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path: " + path);
    return false;
  }

  return true;
}

bool ClientNamenodeService::TestOnlyRequestPreCheck(RpcController* c) {
  return RequestPreCheck(c);
}

/**
 * Do some precheck on requests. mark the request as failed if it doesn't make
 * it and then return false.
 * 1. check if the `backend` in baggage matches our nameservice
 * 2. check if the request exceeds TTL.
 * 3. check request header
 */
bool ClientNamenodeService::RequestPreCheck(RpcController* c) {
  std::string backend_name;
  int64_t emit_time = -1;
  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "backend" && b.has_value()) {
      backend_name = b.value();
    }
    if (b.has_name() && b.name() == "emitTime" && b.has_value()) {
      try {
        emit_time = std::stol(b.value());
      } catch (std::exception e) {
        LOG(ERROR) << "emitTime value in baggage could not be parsed to long "
                      "type. value: "
                   << b.value() << ", exception: " << e.what()
                   << ", will not use it.";
      }
    }
  }
  // check backend name
  if (!backend_name.empty() && backend_name != FLAGS_nameservice) {
    MFC(mismatch_backend_count_metric_)->Inc();
    LOG(ERROR) << "mismatch target backend. nameservice: " << FLAGS_nameservice
               << ", target backend: " << backend_name << "; request header: "
               << c->request_header()->ShortDebugString()
               << "; body: " << c->request()->ShortDebugString();
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(),
                    "backend name not match. request: " + backend_name +
                        ", expected: " + FLAGS_nameservice);
    return false;
  }
  if (backend_name.empty()) {
    MFC(empty_backend_count_metric_)->Inc();
    VLOG(8) << "backend field is not set in baggage, this request may not "
               "comes from nnproxy.";
  }

  // check request TTL
  if (emit_time >= 0) {
    int64_t receive_time =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
    int64_t time_gap = receive_time - emit_time;
    if (time_gap > FLAGS_request_ttl_since_emit_ms) {
      MFC(request_exceeds_ttl_count_metric_)->Inc();
      LOG(ERROR) << "request exceeds ttl; time_gap:" << time_gap
                 << "; emit time: " << emit_time
                 << ", receive time: " << receive_time << "; request header: "
                 << c->request_header()->ShortDebugString()
                 << "; body: " << c->request()->ShortDebugString();
      c->MarkAsFailed(
          JavaExceptions::InvalidRequestException(),
          "request exceeds ttl, emit time: " + std::to_string(emit_time) +
              ", receive time: " + std::to_string(receive_time) +
              ", ttl: " + std::to_string(FLAGS_request_ttl_since_emit_ms));
      return false;
    }
  }

  // check client_id
  auto client_id = c->rpc_request_header()->clientid();
  if (client_id.size() != 16) {
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(),
                    "client id size invalid. client_id=" + client_id);
    return false;
  }

  // check txid
  auto client_txid = c->client_txid();
  auto server_txid = name_space_->GetLastAppliedOrWrittenTxId();
  if (c->client_txid() > server_txid && !FLAGS_dfs_ha_allow_stale_reads) {
    std::ostringstream os;
    os << "client_txid(" << client_txid << ") > server_txid(" << server_txid
       << "), stale read.";
    auto msg = os.str();
    LOG(ERROR) << msg;

    c->MarkAsWeakFailed(JavaExceptions::Exception::kStandbyException,
                        "StandbyException" + msg);
    return false;
  }

  return true;
}

bool ClientNamenodeService::PathCheckBinary(RpcController* c,
                                            const std::string& user_name,
                                            const std::string& path1,
                                            const std::string& path2,
                                            std::string* normalized_path1,
                                            std::string* normalized_path2) {
  CHECK_NOTNULL(normalized_path1);
  CHECK_NOTNULL(normalized_path2);

  if (!StringUtils::IsValidUTF8(user_name)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid UTF8 in username: " + user_name);
    return false;
  }

  if (!FLAGS_banned_user.empty() && user_name == FLAGS_banned_user) {
    std::string msg = "User is banned, user_name: " + user_name;
    LOG(WARNING) << msg;
    c->MarkAsFailed(JavaExceptions::IOException(), msg);
    return false;
  }

  if (!StringUtils::IsValidUTF8(path1)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid UTF8 in path1: " + path1);
    return false;
  }
  if (!StringUtils::IsValidUTF8(path2)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid UTF8 in path2: " + path2);
    return false;
  }

  if (!NormalizePath(path1, user_name, normalized_path1)) {
    c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path1: " + path1);
    return false;
  }
  if (!NormalizePath(path2, user_name, normalized_path2)) {
    c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path2: " + path2);
    return false;
  }

  return true;
}

std::string ClientNamenodeService::LocatedBlockProtoToJsonObject(
    const ::cloudfs::LocatedBlockProto& blk) {
  std::string res;
  res += "{\"blockId\":" + std::to_string(blk.b().blockid()) +
         ",\"generationStamp\":" + std::to_string(blk.b().generationstamp());
  if (blk.b().has_numbytes()) {
    res += ",\"numBytes\":" + std::to_string(blk.b().numbytes());
  }
  res += ",\"locs\":[";
  for (int i = 0; i < blk.locs_size(); ++i) {
    res += "\"" + blk.locs(i).id().ipaddr() + "\",";
    if (i == blk.locs_size() - 1) {
      res.pop_back();
    }
  }
  res += "],\"offset\":" + std::to_string(blk.offset()) + ",\"corrupt\":";
  res += std::to_string(blk.corrupt()) + "}";
  return res;
}

std::string ClientNamenodeService::ExcludedNodesToJsonArray(
    const ::cloudfs::AddBlockRequestProto* request) {
  std::string res = "[";
  for (int i = 0; i < request->excludenodes_size(); ++i) {
    res += "\"" + request->excludenodes(i).id().ipaddr() + "\"";
    if (i != request->excludenodes_size() - 1) {
      res.append(1, ',');
    }
  }
  res += "]";
  return res;
}

std::string ClientNamenodeService::FavoredNodesToJsonArray(
    const ::cloudfs::AddBlockRequestProto* request) {
  std::string res = "[";
  for (int i = 0; i < request->favorednodes_size(); ++i) {
    res += "\"" + request->favorednodes(i) + "\"";
    if (i != request->favorednodes_size() - 1) {
      res.append(1, ',');
    }
  }
  res += "]";
  return res;
}

std::string ClientNamenodeService::HdfsFileStatusProtoToJsonObject(
    const ::cloudfs::HdfsFileStatusProto& fs) {
  std::string res = "{\"fileType\":\"";
  switch (fs.filetype()) {
    case ::cloudfs::HdfsFileStatusProto_FileType_IS_DIR:
      res += "DIR\",\"path\":\"";
      break;
    case ::cloudfs::HdfsFileStatusProto_FileType_IS_FILE:
      res += "FILE\",\"path\":\"";
      break;
    case ::cloudfs::HdfsFileStatusProto_FileType_IS_SYMLINK:
      res += "SYMLINK\",\"path\":\"";
      break;
    default:
      LOG(FATAL) << "Never happen";
  }
  res += fs.path() + "\",\"length\":" + std::to_string(fs.length()) +
         ",\"permission\":" + std::to_string(fs.permission().perm()) +
         ",\"owner\":\"" + fs.owner() + "\",\"group\":\"" + fs.group() +
         "\",\"modification_time\":" + std::to_string(fs.modification_time()) +
         ",\"access_time\":" + std::to_string(fs.access_time()) + "}";
  return res;
}

}  // namespace dancenn
