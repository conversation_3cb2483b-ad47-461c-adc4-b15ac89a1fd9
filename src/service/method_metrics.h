// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_METHOD_METRICS_H_
#define SERVICE_METHOD_METRICS_H_

#include <memory>
#include <utility>
#include "base/metrics.h"

namespace dancenn {

struct MethodMetrics {
  MethodMetrics() = delete;

  MethodMetrics(const MetricID& num_ops,
                const MetricID& num_success_ops,
                const MetricID& time,
                const MetricID& success_time,
                const MetricID& overall_time,
                const MetricID& overall_success_time)
      : num_ops_(num_ops),
        num_success_ops_(num_success_ops),
        time_(time),
        success_time_(success_time),
        overall_time_(overall_time),
        overall_success_time_(overall_success_time) {}

  MetricID num_ops_;
  MetricID num_success_ops_;
  MetricID time_;
  MetricID success_time_;
  MetricID overall_time_;
  MetricID overall_success_time_;
};

}  // namespace dancenn

#endif  // SERVICE_METHOD_METRICS_H_

