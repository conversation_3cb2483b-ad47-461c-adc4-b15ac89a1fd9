// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <service/datanode_service.h>

#include <glog/logging.h>

#include "hdfs.pb.h"
#include "rpc/rpc_controller.h"
#include "rpc/rpc_server_connection.h"
#include "base/java_exceptions.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "namespace/namespace.h"
#include "ha/ha_state_base.h"
#include "ha/ha_state.h"

DECLARE_int32(rpc_in_queue_timeout_ms_block_report);
DECLARE_bool(dancenn_observe_mode_on);
DECLARE_string(nameservice);
DECLARE_bool(dn_reg_ipaddr_has_colon);
DECLARE_bool(enable_ufs_evict_write_cache);

using cloudfs::datanode::NNHAStatusHeartbeatProto;

namespace dancenn {

using MR = MethodRecorder<DatanodeService>;

DatanodeService::DatanodeService(
    std::shared_ptr<DatanodeManager> datanode_manager,
    std::shared_ptr<BlockManager> block_manager,
    std::shared_ptr<NameSpace> name_space,
    HAStateBase* ha_state)
    : datanode_manager_(datanode_manager),
      block_manager_(block_manager),
      block_report_manager_(name_space->GetBlockReportManager()),
      namespace_(name_space),
      ha_state_(ha_state) {
  InitMetrics();
  if (!FLAGS_dancenn_observe_mode_on) {
    CHECK_NOTNULL(block_report_manager_);
  }
}

std::string GetIPString(const std::string& input) {
  if (!FLAGS_dn_reg_ipaddr_has_colon) {
    return input;
  }
  std::string output;

  std::vector<std::string> elems;

  StringUtils::SplitStringForAddr(input, ":", &elems);
  if (elems.size() != 2) {
    return input;
  }
  StringUtils::TrimIPString(&elems[0]);

  return elems[0];
}

void DatanodeService::registerDatanode(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::RegisterDatanodeRequestProto* request,
    ::cloudfs::datanode::RegisterDatanodeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "registerDatanode", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  // In CloudFS, data nodes are deployed in k8s.
  // physical_addr is address of host.
  // virtual_addr is address of container.
  cnetpp::base::IPAddress physical_addr =
      c->rpc_connection()->tcp_connection()->remote_end_point().address();
  std::string virtual_host_str =
      GetIPString(request->registration().datanodeid().ipaddr());

  cnetpp::base::IPAddress virtual_addr(virtual_host_str);
  if (virtual_addr.IsEmpty()) {
    LOG(WARNING) << "ip_address is invalid. pb=" << request->ShortDebugString();
    c->MarkAsFailed(JavaExceptions::kIOException,
                    "ip_address is invalid. pb=" + request->ShortDebugString());
    return;
  }

  // Data node doesn't know itself physical address.
  // So we can't let data node set it.
  DatanodeIDProto datanode_id = request->registration().datanodeid();
  datanode_id.set_physicalipaddr(physical_addr.ToString());
  ProductVersion registration_version =
      request->registration().dnmajorversion() * 10000 +
      request->registration().dnminorversion() * 100 +
      request->registration().dnpatchversion();
  auto s = datanode_manager_->Register(
      datanode_id,
      response->mutable_registration(),
      virtual_addr,
      registration_version == 0 ? c->client_version() : registration_version);

  if (s.HasException()) {
    LOG(WARNING) << "registerDatanode error, ex: " << s.ToString()
                 << " " << request->registration().ShortDebugString();
    c->MarkAsFailed(s.exception(), s.message());
  } else {
    DLOG(INFO) << "registerDatanode success: " << response->ShortDebugString();
  }

  auto reg = response->mutable_registration();
  reg->mutable_storageinfo()->CopyFrom(request->registration().storageinfo());
  reg->set_softwareversion(namespace_->software_version());
  reg->mutable_keys()->set_isblocktokenenabled(false);
  reg->mutable_keys()->set_keyupdateinterval(0);
  reg->mutable_keys()->set_tokenlifetime(0);
  reg->mutable_keys()->mutable_currentkey()->set_keyid(0);
  reg->mutable_keys()->mutable_currentkey()->set_expirydate(0);
}

void DatanodeService::sendHeartbeat(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::HeartbeatRequestProto* request,
    ::cloudfs::datanode::HeartbeatResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "sendHeartbeat", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!namespace_->VerifyRequest(request->registration().storageinfo())) {
    c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
        "Datanode version mismatch");
  } else {
    auto res = ha_state_->CheckOperation(OperationsCategory::kWrite);
    uint32_t dn_id = datanode_manager_->GetDatanodeInterId(
        request->registration().datanodeid().datanodeuuid());
    do {
      datanode_manager_->Heartbeat(*request, response->mutable_cmds());
      if (response->cmds_size() != 0) {
        // DatanodeManager added a register command since the datanode
        // has not registered yet. So we do not fetch commands from BlockManager
        break;
      }

      block_manager_->GetBlockReportCommand(dn_id, response);

      if (res.first.HasException()) {
        // standby mode
        break;
      }

      block_manager_->GetCommands(dn_id,
                                  namespace_->blockpool_id(),
                                  request->xmitsinprogress(),
                                  response,
                                  request->registration().has_dnmajorversion());
    } while (0);

    auto txid = namespace_->GetLastCkptTxId();
    response->mutable_hastatus()->set_txid(txid);
    if (namespace_->GetHAState() == cloudfs::ACTIVE) {
      response->mutable_hastatus()->set_state(NNHAStatusHeartbeatProto::ACTIVE);
    } else {
      response->mutable_hastatus()->set_state(
          NNHAStatusHeartbeatProto::STANDBY);
    }
    namespace_->GetUfsInfo(response->mutable_remoteblockinfo(), true);
    // ! deprecated, only for forward compatible
    if (response->remoteblockinfo().has_tos_info()) {
      response->mutable_tosinfo()->CopyFrom(response->remoteblockinfo().tos_info());
    }
    if (FLAGS_enable_ufs_evict_write_cache) {
      response->set_allowevictwritecache(true);
    } else {
      response->set_allowevictwritecache(false);
    }
  }
  DLOG(INFO) << "heartbeat response " << response->ShortDebugString();
}

void DatanodeService::blockReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::BlockReportRequestProto* request,
    ::cloudfs::datanode::BlockReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto rpc_done = dancenn::NewRpcCallback(
      this, "blockReport", controller, request, response, done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto c = rpc_done->ctl();

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!namespace_->VerifyRequest(request->registration().storageinfo())) {
    c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
                    "Datanode version mismatch");
  } else if (request->reports_size() == 0) {
    c->set_status(RpcStatus::kSuccess);
  } else {
    rpc_done->add_post_callback([=](const Status& s) {
      if (s.HasException()) {
        LOG(WARNING) << "BlockReport error, ex: " << s.ToString()
                     << " " << request->registration().ShortDebugString();
        c->MarkAsFailed(s.exception(), s.message());
      } else {
        DLOG(INFO) << "Block report success: "
            << response->ShortDebugString();
      }
    });
    block_manager_->AsyncBlockReport(
        request->registration().datanodeid().datanodeuuid(), request,
        dynamic_cast<RpcClosure*>(done_guard.release()));
    if (block_report_manager_) {
      block_report_manager_->FullBlockReport(
          request->registration().datanodeid().datanodeuuid(), *request);
    }
  }
}

void DatanodeService::cacheReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::CacheReportRequestProto* request,
    ::cloudfs::datanode::CacheReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "cacheReport", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!namespace_->VerifyRequest(request->registration().storageinfo())) {
    c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
                    "Datanode version mismatch");
  }
}

void DatanodeService::blockReceivedAndDeleted(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::BlockReceivedAndDeletedRequestProto* request,  // NOLINT(whitespace/line_length)
    ::cloudfs::datanode::BlockReceivedAndDeletedResponseProto* response,
    ::google::protobuf::Closure* done) {

  DLOG(INFO) << "blockReceivedAndDeleted " << request->ShortDebugString();
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "blockReceivedAndDeleted", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  if (!namespace_->VerifyRequest(request->registration().storageinfo())) {
    c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
                    "Datanode version mismatch");
  } else if (!request->blocks_size()) {
    // do nothing
  } else {
    auto status = block_manager_->IncrementalBlockReport(
        request->registration().datanodeid().datanodeuuid(),
        request->blocks());
    if (status.HasException()) {
      LOG(WARNING) << "blockReceivedAndDeleted error: " << status.ToString()
                   << " " << request->registration().ShortDebugString();
      c->MarkAsFailed(status.exception(), status.message());
    }
    if (block_report_manager_) {
      block_report_manager_->IncrementalBlockReport(
          datanode_manager_->GetDatanodeInterId(
              request->registration().datanodeid().datanodeuuid()),
          request->registration().datanodeid().datanodeuuid(),
          request->blocks());
    }
  }
}

void DatanodeService::errorReport(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::ErrorReportRequestProto* request,
    ::cloudfs::datanode::ErrorReportResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "errorReport", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto remote_ip = c->GetRemoteAddress().ToString();

  auto error_code = request->errorcode();
  auto msg = request->msg();
  if (error_code ==
      ::cloudfs::datanode::ErrorReportRequestProto_ErrorCode_NOTIFY) {
    LOG(INFO) << "Error report from dn: " << remote_ip << ", msg:" << msg;
  } else {
    if (!namespace_->VerifyRequest(request->registartion().storageinfo())) {
      c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
                      "Datanode version mismatch");
    }
    if (error_code ==
        ::cloudfs::datanode
        ::ErrorReportRequestProto_ErrorCode_DISK_ERROR) {
      LOG(WARNING) << "Disk error on dn: " << remote_ip << ", msg: " << msg;
    } else if (error_code ==
        ::cloudfs::datanode
        ::ErrorReportRequestProto_ErrorCode_FATAL_DISK_ERROR) {
      // TODO remove datanode
      LOG(WARNING) << "Fatal disk error on: " << remote_ip << ", msg: " << msg;
    } else {
      LOG(INFO) << "Error report from dn: " << remote_ip << ", msg: " << msg;
    }
  }
}

void DatanodeService::versionRequest(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::VersionRequestProto* request,
    ::cloudfs::VersionResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "versionRequest", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  namespace_->GetNamespaceInfo(response->mutable_info());
  namespace_->GetUfsInfo(response->mutable_remoteblockinfo(), true);
  // ! deprecated, only for forward compatible
  if (response->remoteblockinfo().has_tos_info()) {
    response->mutable_tosinfo()->CopyFrom(response->remoteblockinfo().tos_info());
  }
  DLOG(INFO) << "version request response " << response->ShortDebugString();
}

void DatanodeService::reportBadBlocks(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::ReportBadBlocksRequestProto* request,
    ::cloudfs::datanode::ReportBadBlocksResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "reportBadBlocks", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto status = block_manager_->ReportBadBlocks(request->blocks());
  if (status.HasException()) {
    if (status.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(status.exception(), status.message());
    } else {
      c->MarkAsWeakFailed(status.exception(), status.message());
    }
  }
}

void DatanodeService::commitBlockSynchronization(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::datanode::CommitBlockSynchronizationRequestProto* request,  // NOLINT(whitespace/line_length)
    ::cloudfs::datanode::CommitBlockSynchronizationResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "commitBlockSynchronization", c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto status = namespace_->CommitBlockSynchronization(*request);
  if (status.HasException()) {
    c->MarkAsFailed(
        JavaExceptions::ExceptionStr(status.exception()), "");
  }
}

static void InitMethodMetrics(
    std::shared_ptr<Metrics> metrics,
    const std::string& name,
    std::unordered_map<std::string, MethodMetric>* method_metrics) {

  MethodMetric metric;
  metric.num_ops_ = metrics->RegisterCounter("NumOps#method=" + name);
  metric.num_success_ops_ = metrics->RegisterCounter(
      "Success.NumOps#method=" + name);
  metric.duration_ = metrics->RegisterHistogram("Time#method=" + name);
  metric.success_duration_ = metrics->RegisterHistogram(
      "Success.Time#method=" + name);

  (*method_metrics)[name] = metric;
}

void DatanodeService::SetMethodMetas(std::shared_ptr<ServiceMeta> sm) {
  sm->AddMethodMeta(
      "blockReport",
      MethodMetaBuilder()
          .SetName("blockReport")
          .SetType(MethodMeta::MethodType::kSlow)
          .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_block_report)
          .SetExceptionWhenTimedout(
              JavaExceptions::Exception::kAccessControlException)
          .Build());

  sm->AddMethodMeta(
      "blockReceivedAndDeleted",
      MethodMetaBuilder()
          .SetName("blockReceivedAndDeleted")
          .SetType(MethodMeta::MethodType::kSlow)
          .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_block_report)
          .SetExceptionWhenTimedout(
              JavaExceptions::Exception::kAccessControlException)
          .Build());
}

void DatanodeService::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("DatanodeService");
  InitMethodMetrics(metrics,
                    "registerDatanode",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "sendHeartbeat",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "blockReport",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "cacheReport",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "blockReceivedAndDeleted",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "errorReport",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "versionRequest",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "reportBadBlocks",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "commitBlockSynchronization",
                    &method_metrics_);

  mismatch_backend_count_metric_ =
      metrics->RegisterCounter("mismatchBackendDnSvc");
  empty_backend_count_metric_ = metrics->RegisterCounter("emptyBackendDnSvc");
}  // NOLINT(readability/fn_size)

bool DatanodeService::RequestPreCheck(RpcController* c) {
  std::string backend_name;
  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "backend" && b.has_value()) {
      backend_name = b.value();
    }
  }
  // check backend name
  if (!backend_name.empty() && backend_name != FLAGS_nameservice) {
    MFC(mismatch_backend_count_metric_)->Inc();
    LOG(ERROR) << "mismatch target backend. nameservice: " << FLAGS_nameservice
               << ", target backend: " << backend_name << "; request header: "
               << c->request_header()->ShortDebugString()
               << "; body: " << c->request()->ShortDebugString();
    c->MarkAsFailed(JavaExceptions::IncorrectVersionException(),
                    "backend name not match. request: " + backend_name +
                        ", expected: " + FLAGS_nameservice);
    return false;
  }
  if (backend_name.empty()) {
    MFC(empty_backend_count_metric_)->Inc();
    VLOG(8) << "backend field is not set in baggage in DatanodeService, this "
               "request may not come from nnproxy.";
  }

  return true;
}

}  // namespace dancenn
