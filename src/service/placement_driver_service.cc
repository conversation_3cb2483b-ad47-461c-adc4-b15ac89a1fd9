// Copyright 2017 He <PERSON>yi <<EMAIL>>

#include <service/placement_driver_service.h>

#include <cnetpp/base/ip_address.h>
#include <glog/logging.h>

#include <vector>
#include <string>
#include <unordered_set>

#include "rpc/rpc_controller.h"
#include "datanode_manager/datanode_manager.h"
#include "datanode_manager/block_placement.h"

DECLARE_int32(namenode_stale_timeout_sec);
DECLARE_int32(namenode_keep_alive_timeout_sec);
DECLARE_string(block_placement_policy);

namespace dancenn {

PlacementDriverService::PlacementDriverService() {
}

PlacementDriverService::~PlacementDriverService() {
}

void PlacementDriverService::reportDatanodes(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::namenode::ReportDatanodesRequestProto* request,
    ::cloudfs::namenode::ReportDatanodesResponseProto* response,
    ::google::protobuf::Closure* done) {
  std::unique_lock<ReadWriteLock> lock(rwlock_);
  uint32_t namespace_id = request->namespaceid();
  if (placements_.find(namespace_id) == placements_.end()) {
    placements_[namespace_id] = BlockPlacementPolicyFactory::Create(
        FLAGS_block_placement_policy, false, nullptr);
    placements_[namespace_id]->Start();
  }
  auto &placement = placements_[namespace_id];
  for (auto &dn : request->reports()) {
    auto &di = dn.datanodeinfo();
    auto ip = di.id().ipaddr();
    uint32_t id = dn_ids_.forward_lookup(ip);
    if (id == kInvalidId) {
      id = dn_ids_.Assign(ip);
      datanodes_[id]= new DatanodeInfo(id, di.id(),
          ::cnetpp::base::IPAddress(::cnetpp::base::StringPiece(ip)));
    }
    placement->AddDatanode(datanodes_[id]);
  }
  done->Run();
}

void PlacementDriverService::chooseTarget(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::namenode::ChooseTargetRequestProto* request,
    ::cloudfs::namenode::ChooseTargetResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  std::shared_lock<ReadWriteLock> lock(rwlock_);

  uint32_t namespace_id = request->namespaceid();
  const auto &it = placements_.find(namespace_id);
  if (it == placements_.end()) {
    response->set_status(
        ::cloudfs::namenode::ChooseTargetResponseProto_Status_MissingDatanodeReport);  // NOLINT(whitespace/line_length)
    done->Run();
    return;
  }
  std::unordered_set<DatanodeInfoPtr> excluded_nodes;
  for (const auto& dn : request->excluded()) {
    uint32_t id = dn_ids_.forward_lookup(dn);
    if (id != kInvalidId) {
      excluded_nodes.emplace(datanodes_[id]);
    }
  }
  uint32_t writer_id = dn_ids_.forward_lookup(request->writer());
  DatanodeInfoPtr writer_dn = nullptr;
  if (writer_id != kInvalidDatanodeID) {
    auto it = datanodes_.find(writer_id);
    if (it != datanodes_.end()) {
      writer_dn = it->second;
    }
  }

  cnetpp::base::IPAddress ip(request->writer());

  const auto &placement = it->second;
  std::vector<DatanodeInfoPtr> result;
  auto storage_policy = static_cast<StoragePolicyId>(request->storagetype());
  PlacementAdvice advice(storage_policy);
  // todo get correct inode_dnip
  auto inode_dnip = "";
  NetworkLocationInfo client_location(writer_dn);
  if (writer_dn == nullptr) {
    client_location = NetworkLocationInfo(ip);
  }
  bool ret = placement->ChooseTarget4New(request->srcpath(),
                                         request->numreplica(),
                                         request->blocksize(),
                                         advice,
                                         client_location,
                                         kDefaultFavored,
                                         &excluded_nodes,
                                         &result);
  response->set_status(
      ::cloudfs::namenode::ChooseTargetResponseProto_Status_OK);
  for (DatanodeInfoPtr ptr : result) {
    std::string ip;
    ::cnetpp::base::IPAddress::NumberToLiteral(ptr->ip(), &ip);
    response->add_chosennodes(ip);
  }
  done->Run();
}

}  // namespace dancenn

