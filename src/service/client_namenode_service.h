// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_CLIENT_NAMENODE_SERVICE_H_
#define SERVICE_CLIENT_NAMENODE_SERVICE_H_

#include <ClientNamenodeProtocol.pb.h>

#include <memory>
#include <map>
#include <set>
#include <string>
#include <unordered_map>

#include "datanode_manager/datanode_manager.h"
#include "namespace/namespace.h"
#include "service/method_recorder.h"
#include "service/service_meta.h"
#include "base/metric.h"
#include "base/retry_cache.h"
#include "block_manager/block_manager.h"
#include "safemode/safemode.h"
#include "ha/ha_state.h"
#include "rpc/rpc_controller.h"
#include "security/key_manager.h"

namespace dancenn {

class DatanodeManager;
class BlockManager;
class KeyManager;

class ClientNamenodeService : public cloudfs::ClientNamenodeProtocol {
 public:
  explicit ClientNamenodeService(
      std::shared_ptr<DatanodeManager> datanode_manager,
      std::shared_ptr<BlockManager> block_manager,
      std::shared_ptr<NameSpace> name_space,
      SafeModeBase* safemode,
      RetryCache* rc);
  virtual ~ClientNamenodeService() {}

  ClientNamenodeService(const ClientNamenodeService&) = delete;
  ClientNamenodeService& operator=(const ClientNamenodeService&) = delete;

  void getBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockLocationsRequestProto* request,
      ::cloudfs::GetBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getServerDefaults(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetServerDefaultsRequestProto* request,
      ::cloudfs::GetServerDefaultsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void create(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateRequestProto* request,
      ::cloudfs::CreateResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void append(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AppendRequestProto* request,
      ::cloudfs::AppendResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setReplication(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setReplicationAttrOnly(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setStoragePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetStoragePolicyRequestProto* request,
      ::cloudfs::SetStoragePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getStoragePolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetStoragePoliciesRequestProto* request,
      ::cloudfs::GetStoragePoliciesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setPermission(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetPermissionRequestProto* request,
      ::cloudfs::SetPermissionResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setOwner(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetOwnerRequestProto* request,
      ::cloudfs::SetOwnerResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void abandonBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AbandonBlockRequestProto* request,
      ::cloudfs::AbandonBlockResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void addBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddBlockRequestProto* request,
      ::cloudfs::AddBlockResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void commitLastBlock(::google::protobuf::RpcController* controller,
                       const ::cloudfs::CommitLastBlockRequestProto* request,
                       ::cloudfs::CommitLastBlockResponseProto* response,
                       ::google::protobuf::Closure* done);

  void getAdditionalDatanode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAdditionalDatanodeRequestProto* request,
      ::cloudfs::GetAdditionalDatanodeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void complete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CompleteRequestProto* request,
      ::cloudfs::CompleteResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void reportBadBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ReportBadBlocksRequestProto* request,
      ::cloudfs::ReportBadBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void concat(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ConcatRequestProto* request,
      ::cloudfs::ConcatResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void rename(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameRequestProto* request,
      ::cloudfs::RenameResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void rename2(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::Rename2RequestProto* request,
      ::cloudfs::Rename2ResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void Delete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteRequestProto* request,
      ::cloudfs::DeleteResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void mkdirs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MkdirsRequestProto* request,
      ::cloudfs::MkdirsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetListingRequestProto* request,
      ::cloudfs::GetListingResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void renewLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewLeaseRequestProto* request,
      ::cloudfs::RenewLeaseResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void recoverLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RecoverLeaseRequestProto* request,
      ::cloudfs::RecoverLeaseResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getFsStats(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFsStatusRequestProto* request,
      ::cloudfs::GetFsStatsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getDatanodeReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeReportRequestProto* request,
      ::cloudfs::GetDatanodeReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getDatanodeStorageReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeStorageReportRequestProto* request,
      ::cloudfs::GetDatanodeStorageReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getPreferredBlockSize(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetPreferredBlockSizeRequestProto* request,
      ::cloudfs::GetPreferredBlockSizeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setSafeMode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetSafeModeRequestProto* request,
      ::cloudfs::SetSafeModeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void saveNamespace(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SaveNamespaceRequestProto* request,
      ::cloudfs::SaveNamespaceResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void rollEdits(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollEditsRequestProto* request,
      ::cloudfs::RollEditsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void restoreFailedStorage(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RestoreFailedStorageRequestProto* request,
      ::cloudfs::RestoreFailedStorageResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void refreshNodes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RefreshNodesRequestProto* request,
      ::cloudfs::RefreshNodesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void finalizeUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FinalizeUpgradeRequestProto* request,
      ::cloudfs::FinalizeUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void rollingUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollingUpgradeRequestProto* request,
      ::cloudfs::RollingUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void listCorruptFileBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCorruptFileBlocksRequestProto* request,
      ::cloudfs::ListCorruptFileBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void metaSave(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MetaSaveRequestProto* request,
      ::cloudfs::MetaSaveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getFileInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileInfoRequestProto* request,
      ::cloudfs::GetFileInfoResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void addCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCacheDirectiveRequestProto* request,
      ::cloudfs::AddCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void modifyCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCacheDirectiveRequestProto* request,
      ::cloudfs::ModifyCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCacheDirectiveRequestProto* request,
      ::cloudfs::RemoveCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void listCacheDirectives(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCacheDirectivesRequestProto* request,
      ::cloudfs::ListCacheDirectivesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void addCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCachePoolRequestProto* request,
      ::cloudfs::AddCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void modifyCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCachePoolRequestProto* request,
      ::cloudfs::ModifyCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCachePoolRequestProto* request,
      ::cloudfs::RemoveCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void listCachePools(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCachePoolsRequestProto* request,
      ::cloudfs::ListCachePoolsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getFileLinkInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileLinkInfoRequestProto* request,
      ::cloudfs::GetFileLinkInfoResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getContentSummary(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetContentSummaryRequestProto* request,
      ::cloudfs::GetContentSummaryResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setQuota(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetQuotaRequestProto* request,
      ::cloudfs::SetQuotaResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void fsync(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FsyncRequestProto* request,
      ::cloudfs::FsyncResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setTimes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetTimesRequestProto* request,
      ::cloudfs::SetTimesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void createSymlink(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSymlinkRequestProto* request,
      ::cloudfs::CreateSymlinkResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getLinkTarget(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLinkTargetRequestProto* request,
      ::cloudfs::GetLinkTargetResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void updateBlockForPipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdateBlockForPipelineRequestProto* request,
      ::cloudfs::UpdateBlockForPipelineResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void updatePipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdatePipelineRequestProto* request,
      ::cloudfs::UpdatePipelineResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDelegationTokenRequestProto* request,
      ::cloudfs::GetDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void renewDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewDelegationTokenRequestProto* request,
      ::cloudfs::RenewDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void cancelDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CancelDelegationTokenRequestProto* request,
      ::cloudfs::CancelDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setBalancerBandwidth(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetBalancerBandwidthRequestProto* request,
      ::cloudfs::SetBalancerBandwidthResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getDataEncryptionKey(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDataEncryptionKeyRequestProto* request,
      ::cloudfs::GetDataEncryptionKeyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void createSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSnapshotRequestProto* request,
      ::cloudfs::CreateSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void renameSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameSnapshotRequestProto* request,
      ::cloudfs::RenameSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void allowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AllowSnapshotRequestProto* request,
      ::cloudfs::AllowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void disallowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DisallowSnapshotRequestProto* request,
      ::cloudfs::DisallowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getSnapshottableDirListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshottableDirListingRequestProto* request,
      ::cloudfs::GetSnapshottableDirListingResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void deleteSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteSnapshotRequestProto* request,
      ::cloudfs::DeleteSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getSnapshotDiffReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshotDiffReportRequestProto* request,
      ::cloudfs::GetSnapshotDiffReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void isFileClosed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IsFileClosedRequestProto* request,
      ::cloudfs::IsFileClosedResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void modifyAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyAclEntriesRequestProto* request,
      ::cloudfs::ModifyAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclEntriesRequestProto* request,
      ::cloudfs::RemoveAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeDefaultAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveDefaultAclRequestProto* request,
      ::cloudfs::RemoveDefaultAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclRequestProto* request,
      ::cloudfs::RemoveAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetAclRequestProto* request,
      ::cloudfs::SetAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getAclStatus(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAclStatusRequestProto* request,
      ::cloudfs::GetAclStatusResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetXAttrRequestProto* request,
      ::cloudfs::SetXAttrResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetXAttrsRequestProto* request,
      ::cloudfs::GetXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void listXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListXAttrsRequestProto* request,
      ::cloudfs::ListXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void removeXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveXAttrRequestProto* request,
      ::cloudfs::RemoveXAttrResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void checkAccess(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CheckAccessRequestProto* request,
      ::cloudfs::CheckAccessResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void createEncryptionZone(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateEncryptionZoneRequestProto* request,
      ::cloudfs::CreateEncryptionZoneResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void listEncryptionZones(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListEncryptionZonesRequestProto* request,
      ::cloudfs::ListEncryptionZonesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getEZForPath(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEZForPathRequestProto* request,
      ::cloudfs::GetEZForPathResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getCurrentEditLogTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetCurrentEditLogTxidRequestProto* request,
      ::cloudfs::GetCurrentEditLogTxidResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getEditsFromTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEditsFromTxidRequestProto* request,
      ::cloudfs::GetEditsFromTxidResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void increaseAccessCounter(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IncreaseAccessCounterRequestProto* request,
      ::cloudfs::IncreaseAccessCounterResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getAccessCounterValues(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAccessCounterValuesRequestProto* request,
      ::cloudfs::GetAccessCounterValuesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setDirPolicy(::google::protobuf::RpcController* controller,
                    const ::cloudfs::SetDirPolicyRequestProto* request,
                    ::cloudfs::SetDirPolicyResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void removeDirPolicy(::google::protobuf::RpcController* controller,
                       const ::cloudfs::RemoveDirPolicyRequestProto* request,
                       ::cloudfs::RemoveDirPolicyResponseProto* response,
                       ::google::protobuf::Closure* done) override;

  void getDirPolicy(::google::protobuf::RpcController* controller,
                    const ::cloudfs::GetDirPolicyRequestProto* request,
                    ::cloudfs::GetDirPolicyResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void listDirPolicy(::google::protobuf::RpcController* controller,
                     const ::cloudfs::ListDirPolicyRequestProto* request,
                     ::cloudfs::ListDirPolicyResponseProto* response,
                     ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void setReplicaPolicy(::google::protobuf::RpcController* controller,
                        const ::cloudfs::SetReplicaPolicyRequestProto* request,
                        ::cloudfs::SetReplicaPolicyResponseProto* response,
                        ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void getReplicaPolicy(::google::protobuf::RpcController* controller,
                        const ::cloudfs::GetReplicaPolicyRequestProto* request,
                        ::cloudfs::GetReplicaPolicyResponseProto* response,
                        ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void getDistributed(::google::protobuf::RpcController* controller,
                      const ::cloudfs::GetDistributedRequestProto* request,
                      ::cloudfs::GetDistributedResponseProto* response,
                      ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void setReadPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReadPolicyRequestProto* request,
      ::cloudfs::SetReadPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void getReadPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetReadPolicyRequestProto* request,
      ::cloudfs::GetReadPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // Deprecate: remove API
  void listReadPolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListReadPoliciesRequestProto* request,
      ::cloudfs::ListReadPoliciesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void addCompleteBlocksAndCloseFile(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCompleteBlocksAndCloseFileRequestProto* request,
      ::cloudfs::AddCompleteBlocksAndCloseFileResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getBlockKeys(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockKeysRequestProto* request,
      ::cloudfs::GetBlockKeysResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void msync(::google::protobuf::RpcController* controller,
             const ::cloudfs::MsyncRequestProto* request,
             ::cloudfs::MsyncResponseProto* response,
             ::google::protobuf::Closure* done) override;

  void getHyperBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetHyperBlockLocationsRequestProto* request,
      ::cloudfs::GetHyperBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getHAServiceState(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::HAServiceStateRequestProto* request,
      ::cloudfs::HAServiceStateResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getINodeStat(::google::protobuf::RpcController* controller,
                    const ::cloudfs::GetINodeStatRequestProto* request,
                    ::cloudfs::GetINodeStatResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void setLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetLifecyclePolicyRequestProto* request,
      ::cloudfs::SetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void unsetLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UnsetLifecyclePolicyRequestProto* request,
      ::cloudfs::UnsetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLifecyclePolicyRequestProto* request,
      ::cloudfs::GetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void load(::google::protobuf::RpcController* controller,
            const ::cloudfs::LoadRequestProto* request,
            ::cloudfs::LoadResponseProto* response,
            ::google::protobuf::Closure* done) override;

  void free(::google::protobuf::RpcController* controller,
            const ::cloudfs::FreeRequestProto* request,
            ::cloudfs::FreeResponseProto* response,
            ::google::protobuf::Closure* done) override;

  void lookupJob(::google::protobuf::RpcController* controller,
                 const ::cloudfs::LookupJobRequestProto* request,
                 ::cloudfs::LookupJobResponseProto* response,
                 ::google::protobuf::Closure* done) override;

  void cancelJob(::google::protobuf::RpcController* controller,
                 const ::cloudfs::CancelJobRequestProto* request,
                 ::cloudfs::CancelJobResponseProto* response,
                 ::google::protobuf::Closure* done) override;

  void SetMethodMetas(std::shared_ptr<ServiceMeta> sm);

  // only used by audit log
  static std::string LocatedBlockProtoToJsonObject(
      const ::cloudfs::LocatedBlockProto& blk);

  static std::string FavoredNodesToJsonArray(
      const ::cloudfs::AddBlockRequestProto* request);

  static std::string ExcludedNodesToJsonArray(
      const ::cloudfs::AddBlockRequestProto* request);

  static std::string HdfsFileStatusProtoToJsonObject(
      const ::cloudfs::HdfsFileStatusProto& fs);

  static cnetpp::base::IPAddress GetClientAddress(RpcController* c);
  static LogRpcInfo GetLogRpcInfo(RpcController* c);

  bool TestOnlyRequestPreCheck(RpcController* c);

 private:
  ::google::protobuf::Closure* WrapClosure4Txid(
      RpcController* controller, ::google::protobuf::Closure* done);

  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<NameSpace> name_space_;
  SafeModeBase* safemode_{nullptr};

  RetryCache* retry_cache_{nullptr};

  friend class MethodRecorder<ClientNamenodeService>;
  std::unordered_map<std::string, MethodMetric> method_metrics_;
  MetricID mismatch_backend_count_metric_;
  MetricID empty_backend_count_metric_;
  MetricID request_exceeds_ttl_count_metric_;

  MetricID miss_retry_cache_count_metric_;
  MetricID mismatch_retry_cache_count_metric_;
  MetricID hit_retry_cache_count_metric_;

  void InitMetrics();

  bool PathCheckUnary(RpcController* c,
                      const std::string& user_name,
                      const std::string& path,
                      std::string* normalized_path);
  bool PathCheckBinary(RpcController* c,
                       const std::string& user_name,
                       const std::string& path1,
                       const std::string& path2,
                       std::string* normalized_path1,
                       std::string* normalized_path2);
  bool RequestPreCheck(RpcController* c);
};

}  // namespace dancenn

#endif  // SERVICE_CLIENT_NAMENODE_SERVICE_H_

