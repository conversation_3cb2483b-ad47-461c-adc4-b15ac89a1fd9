// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_METHOD_TRACER_H_
#define SERVICE_METHOD_TRACER_H_

#include <chrono>
#include <memory>
#include <utility>

#include "base/metrics.h"
#include "service/method_metrics.h"

namespace dancenn {

class MethodTracer {
 public:
  explicit MethodTracer(std::shared_ptr<MethodMetrics> metrics)
      : start_time_(std::chrono::steady_clock::now()),
        method_metrics_(std::move(metrics)) {
  }
  MethodTracer(MethodTracer&& other) noexcept
      : start_time_(std::move(other.start_time_)),
        method_metrics_(std::move(other.method_metrics_)) {
  }

  const MethodTracer& operator=(MethodTracer&& other) noexcept {
    start_time_ = std::move(other.start_time_);
    method_metrics_ = std::move(other.method_metrics_);
    return *this;
  }

  ~MethodTracer() {
    if (method_metrics_) {
      auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
          std::chrono::steady_clock::now() - start_time_).count();
      MFC(method_metrics_->num_ops_)->Inc();
      MFH(method_metrics_->time_)->Update(duration);
      MFH(method_metrics_->overall_time_)->Update(duration);
    }
  }

  const std::chrono::steady_clock::time_point& start_time() const {
    return start_time_;
  }

  std::shared_ptr<MethodMetrics> method_metrics() const {
    return method_metrics_;
  }

 private:
  std::chrono::steady_clock::time_point start_time_;
  std::shared_ptr<MethodMetrics> method_metrics_;
};

}  // namespace dancenn

#endif  // SERVICE_METHOD_TRACER_H_

