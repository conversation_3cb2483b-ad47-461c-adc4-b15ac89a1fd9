// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "service/danceproxy_service.h"

#include <cnetpp/concurrency/this_thread.h>
#include <glog/logging.h>

#include <chrono>
#include <memory>
#include <unordered_map>
#include <utility>
#include <vector>
#include <string>
#include <set>

#include "base/net_utils.h"
#include "btrace.pb.h"  // NOLINT(build/include)
#include "IpcConnectionContext.pb.h"  // NOLINT(build/include)
#include "base/metrics.h"
#include "base/java_exceptions.h"
#include "proxy/block_pool_registry.h"
#include "proxy/fanout_closure.h"
#include "rpc/rpc_controller.h"
#include "rpc/rpc_client_options.h"
#include "service/method_tracer_closure.h"

DECLARE_uint64(dfs_block_size);
DECLARE_int32(dfs_bytes_per_checksum);
DECLARE_int32(dfs_client_write_packet_size);
DECLARE_int32(dfs_replication);
DECLARE_int32(io_file_buffer_size);
DECLARE_uint64(fs_trash_interval);
DECLARE_bool(dfs_encrypt_data_transfer);
DECLARE_string(dfs_checksum_type);
DECLARE_string(danceproxy_namenodes_configuration_file);
DECLARE_string(danceproxy_rpc_default_user);
DECLARE_string(danceproxy_rpc_default_client_id);
DECLARE_int32(danceproxy_rpc_max_open_connections_per_user_and_fs);
DECLARE_int32(danceproxy_rpc_max_pending_calls_per_user_and_fs);
DECLARE_string(default_ethernet_card);
DECLARE_string(btrace_databus_channel);

namespace dancenn {

DanceproxyService::DanceproxyService(
    std::shared_ptr<UpstreamManager> upstream_manager,
    std::shared_ptr<MountsManager> mounts_manager,
    std::shared_ptr<PathTeamSpaceQuotaManager> quota_manager,
    std::shared_ptr<FrozenDirectoryManager> frozen_directory_manager,
    std::shared_ptr<StoragePolicyTTLManager> storage_policy_ttl_manager,
    std::shared_ptr<ProxyThrottler> throttler,
    std::shared_ptr<BlockPoolRegistry> block_pool_registry) {
  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("DanceproxyService");

  byte_system_id_.set_cluster_name("CN-DanceProxy");
  auto ips = net::GetHostAddresses(false);
  auto ip_itr = ips.find(FLAGS_default_ethernet_card);
  if (ip_itr == ips.end()) {
    LOG(FATAL) << "Unknown ethernet card: " << FLAGS_default_ethernet_card;
  }

  byte_system_id_.set_server_ip_address(ip_itr->second.ToString());
  byte_system_id_.set_type(btrace::ByteSystemType::HDFS);

  btrace_databus_channel_ = Databus::GetChannel(FLAGS_btrace_databus_channel);

  upstream_manager_ = upstream_manager;
  mounts_manager_ = mounts_manager;
  quota_manager_ = quota_manager;
  frozen_directory_manager_ = frozen_directory_manager;
  storage_policy_ttl_manager_ = storage_policy_ttl_manager;
  throttler_ = throttler;
  block_pool_registry_ = block_pool_registry;

  server_defaults_.set_blocksize(FLAGS_dfs_block_size);
  server_defaults_.set_bytesperchecksum(FLAGS_dfs_bytes_per_checksum);
  server_defaults_.set_writepacketsize(FLAGS_dfs_client_write_packet_size);
  server_defaults_.set_replication(FLAGS_dfs_replication);
  server_defaults_.set_filebuffersize(FLAGS_io_file_buffer_size);
  server_defaults_.set_trashinterval(FLAGS_fs_trash_interval);
  server_defaults_.set_encryptdatatransfer(FLAGS_dfs_encrypt_data_transfer);
  if (FLAGS_dfs_checksum_type == "CRC32C") {
    server_defaults_.set_checksumtype(
        ::cloudfs::ChecksumTypeProto::CHECKSUM_CRC32C);
    LOG(INFO) << FLAGS_dfs_checksum_type << ", using CHECKSUM_CRC32C.";
  } else if (FLAGS_dfs_checksum_type == "CRC32") {
    server_defaults_.set_checksumtype(
        ::cloudfs::ChecksumTypeProto::CHECKSUM_CRC32);
    LOG(INFO) << FLAGS_dfs_checksum_type << ", using CHECKSUM_CRC32.";
  } else {
    server_defaults_.set_checksumtype(
        ::cloudfs::ChecksumTypeProto::CHECKSUM_NULL);
    LOG(INFO) << FLAGS_dfs_checksum_type << ", using CHECKSUM_NULL.";
  }
}

std::shared_ptr<MethodMetrics> DanceproxyService::InitMetrics(
    const std::string& fs, const std::string& method) {
  auto num_ops = metrics_->RegisterCounter(
      "NumOps#fs=" + fs + "#method=" + method);
  auto num_success_ops = metrics_->RegisterCounter(
      "Success.NumOps#fs=" + fs + "#method=" + method);
  auto time = metrics_->RegisterHistogram(
      "Time#fs=" + fs + "#method=" + method);
  auto success_time = metrics_->RegisterHistogram(
      "Success.Time#fs=" + fs + "#method=" + method);
  auto overall_time = metrics_->RegisterHistogram("Overall.Time#fs=" + fs);
  auto overall_success_time = metrics_->RegisterHistogram(
      "Overall.Success.Time#fs=" + fs);
  return std::make_shared<MethodMetrics>(num_ops,
                                         num_success_ops,
                                         time,
                                         success_time,
                                         overall_time,
                                         overall_success_time);
}

const std::string* DanceproxyService::GetLoginUser(RpcController *c) {
  const std::string* user = &FLAGS_danceproxy_rpc_default_user;
  if (c->ipc_connection_context().get() &&
      c->ipc_connection_context()->has_userinfo() &&
      c->ipc_connection_context()->userinfo().has_effectiveuser()) {
    user = &(c->ipc_connection_context()->userinfo().effectiveuser());
  }
  return user;
}

std::shared_ptr<MethodMetrics> DanceproxyService::GetMethodMetricsFast(
    const std::string& fs, const std::string& method) {
  std::shared_lock<ReadWriteLock> guard(metrics_rw_lock_);
  auto fitr = all_method_metrics_.find(fs);
  if (fitr != all_method_metrics_.end()) {
    auto mitr = fitr->second.find(method);
    if (mitr != fitr->second.end()) {
      return mitr->second;
    }
  }
  return nullptr;
}

std::shared_ptr<MethodMetrics> DanceproxyService::GetMethodMetricsSlow(
    const std::string& fs, const std::string& method) {
  std::unique_lock<ReadWriteLock> guard(metrics_rw_lock_);
  auto fitr = all_method_metrics_.find(fs);
  if (fitr == all_method_metrics_.end()) {
    fitr = all_method_metrics_.emplace(fs,
        std::unordered_map<std::string,
            std::shared_ptr<MethodMetrics>>()).first;
  }
  auto mitr = fitr->second.find(method);
  if (mitr == fitr->second.end()) {
    mitr = fitr->second.emplace(method, InitMetrics(fs, method)).first;
  }
  return mitr->second;
}

DanceproxyService::StubMetaInfo DanceproxyService::GetStub(
    const std::string& method, RpcController* c, const std::string& path) {
  StubMetaInfo res;
  res.fs = mounts_manager_->Resolve(path, &(res.is_trash));
  auto upstream = upstream_manager_->GetUpstream(*GetLoginUser(c), res.fs);
  if (!upstream) {
    return res;
  }
  auto metrics = GetMethodMetricsFast(res.fs, method);
  if (!metrics) {
    metrics = GetMethodMetricsSlow(res.fs, method);
  }
  res.stub = upstream->stub_;
  res.metrics = metrics;
  return res;
}

std::pair<std::vector<DanceproxyService::StubType>,
          std::shared_ptr<MethodMetrics>>
DanceproxyService::GetAllStubs(const std::string& method, RpcController* c) {
  std::pair<std::vector<StubType>, std::shared_ptr<MethodMetrics>> res;
  auto ups = upstream_manager_->GetAllUpstreams(*GetLoginUser(c));
  for (auto& up : ups) {
    if (up) {
      res.first.emplace_back(up->stub_);
    }
  }
  auto fs = mounts_manager_->Resolve("/");
  res.second = GetMethodMetricsFast(fs, method);
  if (!res.second) {
    res.second = GetMethodMetricsSlow(fs, method);
  }
  return res;
}

DanceproxyService::StubMetaInfo DanceproxyService::GetRootStub(
    const std::string& method, RpcController* c) {
  return GetStub(method, c, "/");
}

bool DanceproxyService::CheckReadOnly(RpcController* c,
                                      google::protobuf::Closure* done,
                                      const std::string& path) {
  if (mounts_manager_->IsReadOnly(path)) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Trying to modify a read only path.");
    done->Run();
    return false;
  }
  return true;
}

bool DanceproxyService::CheckRename(RpcController* c,
                                    google::protobuf::Closure* done,
                                    const std::string& path) {
  if (mounts_manager_->IsMountPoint(path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Cannot rename a mount point (" + path + ")");
    done->Run();
    return false;
  }
  if (!mounts_manager_->IsUnified(path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Cannot rename a non-unified directory "
                    + path + " (contains mount point)");
    done->Run();
    return false;
  }
  return true;
}

void DanceproxyService::getBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockLocationsRequestProto* request,
      ::cloudfs::GetBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController *>(controller);
  auto sm = GetStub("getBlockLocations", c, request->src());
  MethodTracer tracer(sm.metrics);
  auto tres = throttler_->CheckThrottle(request->src(), "getBlockLocations");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::READ,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "getBlockLocations",
        c,
        done,
        std::move(tracer));
    sm.stub->getBlockLocations(c, request, response, closure);
  }
}

void DanceproxyService::getServerDefaults(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetServerDefaultsRequestProto* request,
      ::cloudfs::GetServerDefaultsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);

  response->mutable_serverdefaults()->CopyFrom(server_defaults_);

  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getServerDefaults");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getServerDefaults");
  }

  MethodTracer tracer(metrics);

  auto closure = new MethodTracerClosure(c, done, std::move(tracer));
  closure->Run();
}

void DanceproxyService::create(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateRequestProto* request,
      ::cloudfs::CreateResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("create", c, request->src());
  MethodTracer tracer(sm.metrics);

  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto res = quota_manager_->CheckSpace(request->src());
  if (!std::get<0>(res)) {
    c->MarkAsFailed(std::get<1>(res), std::get<2>(res));
    done->Run();
    return;
  }

  auto fres = frozen_directory_manager_->IsFrozen(
      sm.fs, request->src(), sm.is_trash);
  if (std::get<0>(fres)) {
    c->MarkAsFailed(std::get<1>(fres), std::get<2>(fres));
    done->Run();
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(), "create");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(), "Unknown nameservice");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::CREATE,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "create",
        c,
        done,
        std::move(tracer));
    sm.stub->create(c, request, response, closure);
  }
}

void DanceproxyService::append(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AppendRequestProto* request,
      ::cloudfs::AppendResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("append", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(), "append");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::APPEND,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "append",
        c,
        done,
        std::move(tracer));
    sm.stub->append(c, request, response, closure);
  }
}

void DanceproxyService::setReplication(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setReplication", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(), "setReplication");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setReplication(c, request, response, closure);
  }
}

void DanceproxyService::setReplicationAttrOnly(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setReplicationAttrOnly", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(),
                                        "setReplicationAttrOnly");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setReplicationAttrOnly(c, request, response, closure);
  }
}

void DanceproxyService::setStoragePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetStoragePolicyRequestProto* request,
      ::cloudfs::SetStoragePolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setStoragePolicy", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(), "setStoragePolicy");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setStoragePolicy(c, request, response, closure);
  }
}

void DanceproxyService::getStoragePolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetStoragePoliciesRequestProto* request,
      ::cloudfs::GetStoragePoliciesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetRootStub("getStoragePolicies", c);
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getStoragePolicies(c, request, response, closure);
  }
}

void DanceproxyService::setPermission(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetPermissionRequestProto* request,
      ::cloudfs::SetPermissionResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setPermission", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setPermission(c, request, response, closure);
  }
}

void DanceproxyService::setOwner(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetOwnerRequestProto* request,
      ::cloudfs::SetOwnerResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setOwner", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setOwner(c, request, response, closure);
  }
}

void DanceproxyService::abandonBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AbandonBlockRequestProto* request,
      ::cloudfs::AbandonBlockResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("abandonBlock", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->abandonBlock(c, request, response, closure);
  }
}

void DanceproxyService::addBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddBlockRequestProto* request,
      ::cloudfs::AddBlockResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController *>(controller);
  auto sm = GetStub("addBlock", c, request->src());
  MethodTracer tracer(sm.metrics);

  auto tres = throttler_->CheckThrottle(request->src(), "addBlock");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    auto ttl = storage_policy_ttl_manager_->GetStoragePolicyTTL(request->src());
    if (!ttl.empty()) {
      auto new_req = new cloudfs::AddBlockRequestProto(*request);
      new_req->add_favorednodes(ttl);
      delete request;
      c->set_request(new_req);
      sm.stub->addBlock(c, new_req, response, closure);
    } else {
      sm.stub->addBlock(c, request, response, closure);
    }
  }
}

void DanceproxyService::getAdditionalDatanode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAdditionalDatanodeRequestProto* request,
      ::cloudfs::GetAdditionalDatanodeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getAdditionalDatanode", c, request->src());
  MethodTracer tracer(sm.metrics);

  auto tres = throttler_->CheckThrottle(request->src(),
                                        "getAdditionalDatanode");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getAdditionalDatanode(c, request, response, closure);
  }
}

void DanceproxyService::complete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CompleteRequestProto* request,
      ::cloudfs::CompleteResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("complete", c, request->src());
  MethodTracer tracer(sm.metrics);

  auto tres = throttler_->CheckThrottle(request->src(), "complete");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->complete(c, request, response, closure);
  }
}

void DanceproxyService::reportBadBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ReportBadBlocksRequestProto* request,
      ::cloudfs::ReportBadBlocksResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);

  std::string random_fs;
  std::unordered_map<std::string, std::unique_ptr<RpcController>> fanout_calls;
  for (int i = 0; i < request->blocks_size(); ++i) {
    const auto& bp_id = request->blocks(i).b().poolid();
    auto fs = block_pool_registry_->GetFs(bp_id);
    if (fs.empty()) {
      c->MarkAsFailed(JavaExceptions::StandbyException(),
                      "Unknown block pool: " + bp_id);
      done->Run();
      return;
    }
    random_fs = fs;
    auto itr = fanout_calls.find(fs);
    if (itr == fanout_calls.end()) {
      itr = fanout_calls.emplace(fs, std::make_unique<RpcController>()).first;
      itr->second->set_ipc_connection_context(c->ipc_connection_context());
      itr->second->set_request_header(c->request_header());
      itr->second->set_rpc_request_header(c->rpc_request_header());
      itr->second->set_retry_count(c->retry_count());
      // The created request and response are managed by the
      // created controller
      itr->second->set_request(new cloudfs::ReportBadBlocksRequestProto);
      itr->second->set_response(new cloudfs::ReportBadBlocksResponseProto);
    }
    auto req = const_cast<cloudfs::ReportBadBlocksRequestProto*>(
        static_cast<const cloudfs::ReportBadBlocksRequestProto*>(
            itr->second->request()));
    req->add_blocks()->CopyFrom(request->blocks(i));
  }

  if (random_fs.empty()) {
    random_fs = "/";
  }
  auto metrics = GetMethodMetricsFast(random_fs, "reportBadBlocks");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(random_fs, "reportBadBlocks");
  }

  MethodTracer tracer(metrics);

  std::vector<std::unique_ptr<RpcController>> controllers;
  controllers.reserve(fanout_calls.size());
  std::vector<std::shared_ptr<Upstream>> upstreams;
  upstreams.reserve(fanout_calls.size());
  for (auto& call : fanout_calls) {
    auto u = upstream_manager_->GetUpstream(*GetLoginUser(c), call.first);
    if (!u) {
      c->MarkAsFailed(JavaExceptions::StandbyException(),
                      "Unknown fs: " + call.first);
      done->Run();
      return;
    }
    upstreams.emplace_back(u);
    controllers.emplace_back(std::move(call.second));
  }
  // The tracer_closure will be destroyed automatically after the method Run()
  // is called. And the method Run() will be called in method Run() of
  // fanout_closure when all fanout calls are finished.
  auto tracer_closure = new MethodTracerClosure(c, done, std::move(tracer));
  // The fanout_closure will be destroyed automatically
  // after the method Run() is called N(= controllers.size()) times.
  // Then the controllers will be destroyed,
  // and will release all allocated requests and responses
  auto fanout_closure = new FanoutClosure(tracer_closure,
                                          std::move(controllers));
  for (size_t i = 0; i < upstreams.size(); ++i) {
    upstreams[i]->stub_->reportBadBlocks(
        fanout_closure->GetController(i),
        static_cast<const cloudfs::ReportBadBlocksRequestProto*>(
            fanout_closure->GetController(i)->request()),
        static_cast<cloudfs::ReportBadBlocksResponseProto*>(
            fanout_closure->GetController(i)->response()),
        fanout_closure);
  }
}

void DanceproxyService::concat(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ConcatRequestProto* request,
      ::cloudfs::ConcatResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);

  if (request->trg().empty()) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "Target file name is empty");
    done->Run();
    return;
  } else if (request->srcs_size() == 0) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "No sources given");
    done->Run();
    return;
  }

  auto target_fs = mounts_manager_->Resolve(request->trg());
  for (int i = 0; i < request->srcs_size(); ++i) {
    if (mounts_manager_->Resolve(request->srcs(i)) != target_fs) {
      c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                      "Cannot concat across namespaces");
      done->Run();
      return;
    }
  }

  auto sm = GetStub("concat", c, request->trg());
  if (!CheckReadOnly(c, done, request->trg())) {
    return;
  }

  auto tres = throttler_->CheckThrottle(request->trg(), "concat");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->concat(c, request, response, closure);
  }
}

void DanceproxyService::rename(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameRequestProto* request,
      ::cloudfs::RenameResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src()) ||
      !CheckReadOnly(c, done, request->dst()) ||
      !CheckRename(c, done, request->src()) ||
      !CheckRename(c, done, request->dst())) {
    return;
  }

  bool is_trash = false;
  auto src_fs = mounts_manager_->Resolve(request->src());
  auto dst_fs = mounts_manager_->Resolve(request->dst(), &is_trash);
  if (!mounts_manager_->InRenameWhiteList(request->dst()) && src_fs != dst_fs) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "Cannot rename across namespaces");
    done->Run();
    return;
  }

  if (!is_trash) {
    auto res = quota_manager_->CheckSpace(request->dst());
    if (!std::get<0>(res)) {
      c->MarkAsFailed(std::get<1>(res), std::get<2>(res));
      done->Run();
      return;
    }
  }

  auto fres =
      frozen_directory_manager_->IsFrozen(dst_fs, request->dst(), is_trash);
  if (std::get<0>(fres)) {
    c->MarkAsFailed(std::get<1>(fres), std::get<2>(fres));
    done->Run();
    return;
  }

  auto sm = GetStub("rename", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::RENAME,
        c->rpc_request_header()->traceinfo().btid(),
        "",
        "rename",
        c,
        done,
        std::move(tracer));
    closure->get_extra()["source_path"] = cnetpp::base::Value(request->src());
    closure->get_extra()["dest_path"] = cnetpp::base::Value(request->dst());
    sm.stub->rename(c, request, response, closure);
  }
}

void DanceproxyService::rename2(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::Rename2RequestProto* request,
      ::cloudfs::Rename2ResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src()) ||
      !CheckReadOnly(c, done, request->dst()) ||
      !CheckRename(c, done, request->src()) ||
      !CheckRename(c, done, request->dst())) {
    return;
  }

  bool is_trash = false;
  auto src_fs = mounts_manager_->Resolve(request->src());
  auto dst_fs = mounts_manager_->Resolve(request->dst(), &is_trash);
  if (!mounts_manager_->InRenameWhiteList(request->dst()) && src_fs != dst_fs) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "Cannot rename2 across namespaces");
    done->Run();
    return;
  }

  if (!is_trash) {
    auto res = quota_manager_->CheckSpace(request->dst());
    if (!std::get<0>(res)) {
      c->MarkAsFailed(std::get<1>(res), std::get<2>(res));
      done->Run();
      return;
    }
  }

  auto fres =
      frozen_directory_manager_->IsFrozen(dst_fs, request->dst(), is_trash);
  if (std::get<0>(fres)) {
    c->MarkAsFailed(std::get<1>(fres), std::get<2>(fres));
    done->Run();
    return;
  }

  auto sm = GetStub("rename2", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::RENAME,
        c->rpc_request_header()->traceinfo().btid(),
        "",
        "rename2",
        c,
        done,
        std::move(tracer));
    closure->get_extra()["source_path"] = cnetpp::base::Value(request->src());
    closure->get_extra()["dest_path"] = cnetpp::base::Value(request->dst());
    sm.stub->rename2(c, request, response, closure);
  }
}

void DanceproxyService::Delete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteRequestProto* request,
      ::cloudfs::DeleteResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("Delete", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::DELETE,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "delete",
        c,
        done,
        std::move(tracer));
    sm.stub->Delete(c, request, response, closure);
  }
}

void DanceproxyService::mkdirs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MkdirsRequestProto* request,
      ::cloudfs::MkdirsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("mkdirs", c, request->src());
  MethodTracer tracer(sm.metrics);
  auto fres =
      frozen_directory_manager_->IsFrozen(sm.fs, request->src(), sm.is_trash);
  if (std::get<0>(fres)) {
    c->MarkAsFailed(std::get<1>(fres), std::get<2>(fres));
    done->Run();
    return;
  }

  auto tres = throttler_->CheckThrottle(request->src(), "mkdirs");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::CREATE,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "mkdirs",
        c,
        done,
        std::move(tracer));
    closure->get_extra()["create_parent"] =
        cnetpp::base::Value(request->createparent() ? "true" : "false");
    sm.stub->mkdirs(c, request, response, closure);
  }
}

void DanceproxyService::getListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetListingRequestProto* request,
      ::cloudfs::GetListingResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);

  auto all_fs = mounts_manager_->ResolveAll(request->src());
  if (all_fs.size() <= 1) {  // fast path
    auto sm = GetStub("getListing", c, request->src());
    MethodTracer tracer(sm.metrics);

    auto tres = throttler_->CheckThrottle(request->src(), "getListing");
    if (tres.HasException()) {
      c->MarkAsFailed(tres.exception(), tres.message());
      done->Run();
      return;
    }

    if (!sm.stub) {
      c->MarkAsFailed(JavaExceptions::StandbyException(),
                      "Could not resolve path");
      done->Run();
    } else {
      auto closure = new MethodTracerClosure(
          btrace_databus_channel_,
          byte_system_id_,
          btrace::OperationType::READ,
          c->rpc_request_header()->traceinfo().btid(),
          request->src(),
          "getListing",
          c,
          done,
          std::move(tracer));
      sm.stub->getListing(c, request, response, closure);
    }
    return;
  }

  auto metrics = GetMethodMetricsFast(all_fs[0], "getListing");
  if (!metrics) {
    metrics = GetMethodMetricsFast(all_fs[0], "getListing");
  }
  MethodTracer tracer(metrics);

  auto tres = throttler_->CheckThrottle(request->src(), "getListing");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  std::vector<std::shared_ptr<Upstream>> ups;
  std::vector<std::unique_ptr<RpcController>> controllers;
  for (auto& fs : all_fs) {
    auto up = upstream_manager_->GetUpstream(*GetLoginUser(c), fs);
    if (!up) {
      c->MarkAsFailed(JavaExceptions::StandbyException(),
                      "Could not resolve path");
      done->Run();
      return;
    }
    ups.emplace_back(up);
    controllers.emplace_back(std::make_unique<RpcController>());
    auto& con = controllers.back();
    con->set_ipc_connection_context(c->ipc_connection_context());
    con->set_request_header(c->request_header());
    con->set_rpc_request_header(c->rpc_request_header());
    con->set_retry_count(c->retry_count());
    auto req = new cloudfs::GetListingRequestProto;
    req->CopyFrom(*request);
    con->set_request(req);
    con->set_response(new cloudfs::GetListingResponseProto);
  }

  // The tracer_closure will be destroyed automatically after the method Run()
  // is called. And the method Run() will be called in method Run() of
  // fanout_closure when all fanout calls are finished.
  auto tracer_closure = new MethodTracerClosure(
      btrace_databus_channel_,
      byte_system_id_,
      btrace::OperationType::READ,
      request->src(),
      c->rpc_request_header()->traceinfo().btid(),
      "getListing",
      c,
      done,
      std::move(tracer));
  // The fanout_closure will be destroyed automatically
  // after the method Run() is called N(= controllers.size()) times.
  // Then the controllers will be destroyed,
  // and will release all allocated requests and responses
  auto fanout_closure = new FanoutClosure(
      tracer_closure,
      std::move(controllers),
      [] (google::protobuf::Message* r1, google::protobuf::Message* r2) {
        auto ls1 = static_cast<cloudfs::GetListingResponseProto*>(r1);
        auto ls2 = static_cast<cloudfs::GetListingResponseProto*>(r2);
        for (int i = 0; i < ls2->dirlist().partiallisting_size(); ++i) {
          ls1->mutable_dirlist()->add_partiallisting()->CopyFrom(
              ls2->dirlist().partiallisting(i));
          ls1->mutable_dirlist()->set_remainingentries(
              ls1->mutable_dirlist()->remainingentries() +
                  ls2->dirlist().remainingentries());
        }
      });
  for (size_t i = 0; i < ups.size(); ++i) {
    ups[i]->stub_->getListing(
        fanout_closure->GetController(i),
        static_cast<const cloudfs::GetListingRequestProto*>(
            fanout_closure->GetController(i)->request()),
        static_cast<cloudfs::GetListingResponseProto*>(
            fanout_closure->GetController(i)->response()),
        fanout_closure);
  }
}

void DanceproxyService::renewLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewLeaseRequestProto* request,
      ::cloudfs::RenewLeaseResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetAllStubs("renewLease", c);
  MethodTracer tracer(sm.second);
  std::vector<std::unique_ptr<RpcController>> controllers;
  for (auto& stub : sm.first) {
    controllers.emplace_back(std::make_unique<RpcController>());
    auto& con = controllers.back();
    con->set_ipc_connection_context(c->ipc_connection_context());
    con->set_request_header(c->request_header());
    con->set_rpc_request_header(c->rpc_request_header());
    con->set_retry_count(c->retry_count());
    // The created request and response are managed by the
    // created controller
    auto req = new cloudfs::RenewLeaseRequestProto();
    req->CopyFrom(*request);
    con->set_request(req);
    con->set_response(new cloudfs::RenewLeaseResponseProto);
  }
  // The tracer_closure will be destroyed automatically after the method Run()
  // is called. And the method Run() will be called in method Run() of
  // fanout_closure when all fanout calls are finished.
  auto tracer_closure = new MethodTracerClosure(c, done, std::move(tracer));
  // The fanout_closure will be destroyed automatically
  // after the method Run() is called N(= controllers.size()) times.
  // Then the controllers will be destroyed,
  // and will release all allocated requests and responses
  auto fanout_closure = new FanoutClosure(tracer_closure,
                                          std::move(controllers));
  for (size_t i = 0; i < sm.first.size(); ++i) {
    sm.first[i]->renewLease(
        fanout_closure->GetController(i),
        static_cast<const cloudfs::RenewLeaseRequestProto*>(
            fanout_closure->GetController(i)->request()),
        static_cast<cloudfs::RenewLeaseResponseProto*>(
            fanout_closure->GetController(i)->response()),
        fanout_closure);
  }
}

void DanceproxyService::recoverLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RecoverLeaseRequestProto* request,
      ::cloudfs::RecoverLeaseResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("recoverLease", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->recoverLease(c, request, response, closure);
  }
}

void DanceproxyService::getFsStats(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFsStatusRequestProto* request,
      ::cloudfs::GetFsStatsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetRootStub("getFsStats", c);
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getFsStats(c, request, response, closure);
  }
}

void DanceproxyService::getDatanodeReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeReportRequestProto* request,
      ::cloudfs::GetDatanodeReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetRootStub("getDatanodeReport", c);
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getDatanodeReport(c, request, response, closure);
  }
}

void DanceproxyService::getDatanodeStorageReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeStorageReportRequestProto* request,
      ::cloudfs::GetDatanodeStorageReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetRootStub("getDatanodeStorageReport", c);
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getDatanodeStorageReport(c, request, response, closure);
  }
}

void DanceproxyService::getPreferredBlockSize(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetPreferredBlockSizeRequestProto* request,
      ::cloudfs::GetPreferredBlockSizeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getPreferredBlockSize", c, request->filename());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getPreferredBlockSize(c, request, response, closure);
  }
}

void DanceproxyService::setSafeMode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetSafeModeRequestProto* request,
      ::cloudfs::SetSafeModeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "setSafeMode");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "setSafeMode");
  }
  MethodTracer tracer(metrics);
  if (request->action() == cloudfs::SafeModeActionProto::SAFEMODE_GET) {
    response->set_result(false);
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    closure->Run();
  } else {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid operation, do not use proxy.");
    done->Run();
  }
}

void DanceproxyService::saveNamespace(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SaveNamespaceRequestProto* request,
      ::cloudfs::SaveNamespaceResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "saveNamespace");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "saveNamespace");
  }
  MethodTracer tracer(metrics);
  auto c = static_cast<RpcController*>(controller);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::rollEdits(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollEditsRequestProto* request,
      ::cloudfs::RollEditsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "rollEdits");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "rollEdits");
  }
  MethodTracer tracer(metrics);
  auto c = static_cast<RpcController*>(controller);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::restoreFailedStorage(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RestoreFailedStorageRequestProto* request,
      ::cloudfs::RestoreFailedStorageResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "restoreFailedStorage");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "restoreFailedStorage");
  }
  MethodTracer tracer(metrics);
  auto c = static_cast<RpcController*>(controller);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::refreshNodes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RefreshNodesRequestProto* request,
      ::cloudfs::RefreshNodesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetAllStubs("refreshNodes", c);
  MethodTracer tracer(sm.second);
  std::vector<std::unique_ptr<RpcController>> controllers;
  for (auto& stub : sm.first) {
    controllers.emplace_back(std::make_unique<RpcController>());
    auto& con = controllers.back();
    con->set_ipc_connection_context(c->ipc_connection_context());
    con->set_request_header(c->request_header());
    con->set_rpc_request_header(c->rpc_request_header());
    con->set_retry_count(c->retry_count());
    // The created request and response are managed by the
    // created controller
    auto req = new cloudfs::RefreshNodesRequestProto();
    req->CopyFrom(*request);
    con->set_request(req);
    con->set_response(new cloudfs::RefreshNodesResponseProto);
  }
  // The tracer_closure will be destroyed automatically after the method Run()
  // is called. And the method Run() will be called in method Run() of
  // fanout_closure when all fanout calls are finished.
  auto tracer_closure = new MethodTracerClosure(c, done, std::move(tracer));
  // The fanout_closure will be destroyed automatically
  // after the method Run() is called N(= controllers.size()) times.
  // Then the controllers will be destroyed,
  // and will release all allocated requests and responses
  auto fanout_closure = new FanoutClosure(tracer_closure,
                                          std::move(controllers));
  for (size_t i = 0; i < sm.first.size(); ++i) {
    sm.first[i]->refreshNodes(
        fanout_closure->GetController(i),
        static_cast<const cloudfs::RefreshNodesRequestProto*>(
            fanout_closure->GetController(i)->request()),
        static_cast<cloudfs::RefreshNodesResponseProto*>(
            fanout_closure->GetController(i)->response()),
        fanout_closure);
  }
}

void DanceproxyService::finalizeUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FinalizeUpgradeRequestProto* request,
      ::cloudfs::FinalizeUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "finalizeUpgrade");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "finalizeUpgrade");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::rollingUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollingUpgradeRequestProto* request,
      ::cloudfs::RollingUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "rollingUpgrade");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "rollingUpgrade");
  }
  MethodTracer tracer(metrics);
  auto c = static_cast<RpcController*>(controller);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::listCorruptFileBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCorruptFileBlocksRequestProto* request,
      ::cloudfs::ListCorruptFileBlocksResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("listCorruptFileBlocks", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->listCorruptFileBlocks(c, request, response, closure);
  }
}

void DanceproxyService::metaSave(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MetaSaveRequestProto* request,
      ::cloudfs::MetaSaveResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "metaSave");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "metaSave");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getFileInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileInfoRequestProto* request,
      ::cloudfs::GetFileInfoResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController *>(controller);
  auto sm = GetStub("getFileInfo", c, request->src());
  MethodTracer tracer(sm.metrics);

  auto tres = throttler_->CheckThrottle(request->src(), "getFileInfo");
  if (tres.HasException()) {
    c->MarkAsFailed(tres.exception(), tres.message());
    done->Run();
    return;
  }

  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(
        btrace_databus_channel_,
        byte_system_id_,
        btrace::OperationType::READ,
        c->rpc_request_header()->traceinfo().btid(),
        request->src(),
        "getFileInfo",
        c,
        done,
        std::move(tracer));
    sm.stub->getFileInfo(c, request, response, closure);
  }
}

void DanceproxyService::addCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCacheDirectiveRequestProto* request,
      ::cloudfs::AddCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  // TODO(yangjinfeng.02)
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "addCacheDirective");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "addCacheDirective");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::modifyCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCacheDirectiveRequestProto* request,
      ::cloudfs::ModifyCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  // TODO(yangjinfeng.02)
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "modifyCacheDirective");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "modifyCacheDirective");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::removeCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCacheDirectiveRequestProto* request,
      ::cloudfs::RemoveCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  // TODO(yangjinfeng.02)
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "removeCacheDirective");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "removeCacheDirective");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::listCacheDirectives(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCacheDirectivesRequestProto* request,
      ::cloudfs::ListCacheDirectivesResponseProto* response,
      ::google::protobuf::Closure* done) {
  // TODO(yangjinfeng.02)
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "listCacheDirectives");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "listCacheDirectives");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::addCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCachePoolRequestProto* request,
      ::cloudfs::AddCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "addCachePool");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "addCachePool");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::modifyCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCachePoolRequestProto* request,
      ::cloudfs::ModifyCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "modifyCachePool");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "modifyCachePool");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::removeCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCachePoolRequestProto* request,
      ::cloudfs::RemoveCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "removeCachePool");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "removeCachePool");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::listCachePools(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCachePoolsRequestProto* request,
      ::cloudfs::ListCachePoolsResponseProto* response,
      ::google::protobuf::Closure* done) {
  // TODO(yangjinfeng.02)
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "listCachePools");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "listCachePools");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getFileLinkInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileLinkInfoRequestProto* request,
      ::cloudfs::GetFileLinkInfoResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getFileLinkInfo", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getFileLinkInfo(c, request, response, closure);
  }
}

void DanceproxyService::getContentSummary(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetContentSummaryRequestProto* request,
      ::cloudfs::GetContentSummaryResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getContentSummary", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getContentSummary(c, request, response, closure);
  }
}

void DanceproxyService::setQuota(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetQuotaRequestProto* request,
      ::cloudfs::SetQuotaResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("setQuota", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setQuota(c, request, response, closure);
  }
}

void DanceproxyService::fsync(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FsyncRequestProto* request,
      ::cloudfs::FsyncResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("fsync", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->fsync(c, request, response, closure);
  }
}

void DanceproxyService::setTimes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetTimesRequestProto* request,
      ::cloudfs::SetTimesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("setTimes", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setTimes(c, request, response, closure);
  }
}

void DanceproxyService::createSymlink(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSymlinkRequestProto* request,
      ::cloudfs::CreateSymlinkResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->link())) {
    return;
  }

  auto link_fs = mounts_manager_->Resolve(request->link());
  auto target_fs = mounts_manager_->Resolve(request->target());
  if (link_fs != target_fs) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "createSymlink across namespaces");
    done->Run();
    return;
  }

  auto sm = GetStub("createSymlink", c, request->target());

  auto fres =
      frozen_directory_manager_->IsFrozen(sm.fs, request->link(), sm.is_trash);
  if (std::get<0>(fres)) {
    c->MarkAsFailed(std::get<1>(fres), std::get<2>(fres));
    done->Run();
    return;
  }

  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->createSymlink(c, request, response, closure);
  }
}

void DanceproxyService::getLinkTarget(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLinkTargetRequestProto* request,
      ::cloudfs::GetLinkTargetResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getLinkTarget", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getLinkTarget(c, request, response, closure);
  }
}

void DanceproxyService::updateBlockForPipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdateBlockForPipelineRequestProto* request,
      ::cloudfs::UpdateBlockForPipelineResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = block_pool_registry_->GetFs(request->block().poolid());
  if (fs.empty()) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Unknown block pool: " + request->block().poolid());
    done->Run();
    return;
  }

  auto metrics = GetMethodMetricsFast(fs, "updateBlockForPipeline");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "updateBlockForPipeline");
  }
  MethodTracer tracer(metrics);

  auto up = upstream_manager_->GetUpstream(*GetLoginUser(c), fs);
  if (!up) {
    c->MarkAsFailed(JavaExceptions::StandbyException(), "Unknown fs: " + fs);
    done->Run();
    return;
  }
  auto closure = new MethodTracerClosure(c, done, std::move(tracer));
  up->stub_->updateBlockForPipeline(c, request, response, closure);
}

void DanceproxyService::updatePipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdatePipelineRequestProto* request,
      ::cloudfs::UpdatePipelineResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (request->oldblock().poolid() != request->newblock().poolid()) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Cannot update pipeline across block pools");

    done->Run();
    return;
  }

  auto fs = block_pool_registry_->GetFs(request->newblock().poolid());
  if (fs.empty()) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Unknown block pool: " + request->newblock().poolid());
    done->Run();
    return;
  }

  auto metrics = GetMethodMetricsFast(fs, "updatePipeline");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "updatePipeline");
  }
  MethodTracer tracer(metrics);

  auto up = upstream_manager_->GetUpstream(*GetLoginUser(c), fs);
  if (!up) {
    c->MarkAsFailed(JavaExceptions::StandbyException(), "Unknown fs: " + fs);
    done->Run();
    return;
  }

  auto closure = new MethodTracerClosure(c, done, std::move(tracer));
  up->stub_->updatePipeline(c, request, response, closure);
}

void DanceproxyService::getDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDelegationTokenRequestProto* request,
      ::cloudfs::GetDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getDelegationToken");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getDelegationToken");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::renewDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewDelegationTokenRequestProto* request,
      ::cloudfs::RenewDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "renewDelegationToken");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "renewDelegationToken");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::cancelDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CancelDelegationTokenRequestProto* request,
      ::cloudfs::CancelDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "cancelDelegationToken");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "cancelDelegationToken");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::setBalancerBandwidth(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetBalancerBandwidthRequestProto* request,
      ::cloudfs::SetBalancerBandwidthResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);

  auto sm = GetAllStubs("setBalancerBandwidth", c);
  MethodTracer tracer(sm.second);
  std::vector<std::unique_ptr<RpcController>> controllers;
  for (auto& stub : sm.first) {
    controllers.emplace_back(std::make_unique<RpcController>());
    auto& con = controllers.back();
    con->set_ipc_connection_context(c->ipc_connection_context());
    con->set_request_header(c->request_header());
    con->set_rpc_request_header(c->rpc_request_header());
    con->set_retry_count(c->retry_count());
    // The created request and response are managed by the
    // created controller
    auto req = new cloudfs::SetBalancerBandwidthRequestProto();
    req->CopyFrom(*request);
    con->set_request(req);
    con->set_response(new cloudfs::SetBalancerBandwidthResponseProto);
  }
  // The tracer_closure will be destroyed automatically after the method Run()
  // is called. And the method Run() will be called in method Run() of
  // fanout_closure when all fanout calls are finished.
  auto tracer_closure = new MethodTracerClosure(c, done, std::move(tracer));
  // The fanout_closure will be destroyed automatically
  // after the method Run() is called N(= controllers.size()) times.
  // Then the controllers will be destroyed,
  // and will release all allocated requests and responses
  auto fanout_closure = new FanoutClosure(tracer_closure,
                                          std::move(controllers));
  for (size_t i = 0; i < sm.first.size(); ++i) {
    sm.first[i]->setBalancerBandwidth(
        fanout_closure->GetController(i),
        static_cast<const cloudfs::SetBalancerBandwidthRequestProto*>(
            fanout_closure->GetController(i)->request()),
        static_cast<cloudfs::SetBalancerBandwidthResponseProto*>(
            fanout_closure->GetController(i)->response()),
        fanout_closure);
  }
}

void DanceproxyService::getDataEncryptionKey(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDataEncryptionKeyRequestProto* request,
      ::cloudfs::GetDataEncryptionKeyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetRootStub("getDataEncryptionKey", c);
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getDataEncryptionKey(c, request, response, closure);
  }
}

void DanceproxyService::createSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSnapshotRequestProto* request,
      ::cloudfs::CreateSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "createSnapshot");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "createSnapshot");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::renameSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameSnapshotRequestProto* request,
      ::cloudfs::RenameSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "renameSnapshot");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "renameSnapshot");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::allowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AllowSnapshotRequestProto* request,
      ::cloudfs::AllowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "allowSnapshot");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "allowSnapshot");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::disallowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DisallowSnapshotRequestProto* request,
      ::cloudfs::DisallowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "disallowSnapshot");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "disallowSnapshot");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getSnapshottableDirListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshottableDirListingRequestProto* request,
      ::cloudfs::GetSnapshottableDirListingResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getSnapshottableDirListing");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getSnapshottableDirListing");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::deleteSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteSnapshotRequestProto* request,
      ::cloudfs::DeleteSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "deleteSnapshot");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "deleteSnapshot");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getSnapshotDiffReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshotDiffReportRequestProto* request,
      ::cloudfs::GetSnapshotDiffReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getSnapshotDiffReport");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getSnapshotDiffReport");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::isFileClosed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IsFileClosedRequestProto* request,
      ::cloudfs::IsFileClosedResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("isFileClosed", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->isFileClosed(c, request, response, closure);
  }
}

void DanceproxyService::modifyAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyAclEntriesRequestProto* request,
      ::cloudfs::ModifyAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("modifyAclEntries", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->modifyAclEntries(c, request, response, closure);
  }
}

void DanceproxyService::removeAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclEntriesRequestProto* request,
      ::cloudfs::RemoveAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("removeAclEntries", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->removeAclEntries(c, request, response, closure);
  }
}

void DanceproxyService::removeDefaultAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveDefaultAclRequestProto* request,
      ::cloudfs::RemoveDefaultAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("removeDefaultAcl", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->removeDefaultAcl(c, request, response, closure);
  }
}

void DanceproxyService::removeAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclRequestProto* request,
      ::cloudfs::RemoveAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("removeAcl", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->removeAcl(c, request, response, closure);
  }
}

void DanceproxyService::setAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetAclRequestProto* request,
      ::cloudfs::SetAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("setAcl", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setAcl(c, request, response, closure);
  }
}

void DanceproxyService::getAclStatus(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAclStatusRequestProto* request,
      ::cloudfs::GetAclStatusResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getAclStatus", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getAclStatus(c, request, response, closure);
  }
}

void DanceproxyService::setXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetXAttrRequestProto* request,
      ::cloudfs::SetXAttrResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("setXAttr", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setXAttr(c, request, response, closure);
  }
}

void DanceproxyService::getXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetXAttrsRequestProto* request,
      ::cloudfs::GetXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getXAttrs", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getXAttrs(c, request, response, closure);
  }
}

void DanceproxyService::listXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListXAttrsRequestProto* request,
      ::cloudfs::ListXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("listXAttrs", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->listXAttrs(c, request, response, closure);
  }
}

void DanceproxyService::removeXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveXAttrRequestProto* request,
      ::cloudfs::RemoveXAttrResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->src())) {
    return;
  }

  auto sm = GetStub("removeXAttr", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->removeXAttr(c, request, response, closure);
  }
}

void DanceproxyService::checkAccess(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CheckAccessRequestProto* request,
      ::cloudfs::CheckAccessResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("checkAccess", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->checkAccess(c, request, response, closure);
  }
}

void DanceproxyService::createEncryptionZone(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateEncryptionZoneRequestProto* request,
      ::cloudfs::CreateEncryptionZoneResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "createEncryptionZone");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "createEncryptionZone");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::listEncryptionZones(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListEncryptionZonesRequestProto* request,
      ::cloudfs::ListEncryptionZonesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "listEncryptionZones");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "listEncryptionZones");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getEZForPath(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEZForPathRequestProto* request,
      ::cloudfs::GetEZForPathResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getEZForPath", c, request->src());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getEZForPath(c, request, response, closure);
  }
}

void DanceproxyService::getCurrentEditLogTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetCurrentEditLogTxidRequestProto* request,
      ::cloudfs::GetCurrentEditLogTxidResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getCurrentEditLogTxid");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getCurrentEditLogTxid");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::getEditsFromTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEditsFromTxidRequestProto* request,
      ::cloudfs::GetEditsFromTxidResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getEditsFromTxid");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getEditsFromTxid");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::increaseAccessCounter(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IncreaseAccessCounterRequestProto* request,
      ::cloudfs::IncreaseAccessCounterResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("increaseAccessCounter", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->increaseAccessCounter(c, request, response, closure);
  }
}

void DanceproxyService::getAccessCounterValues(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAccessCounterValuesRequestProto* request,
      ::cloudfs::GetAccessCounterValuesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getAccessCounterValues", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getAccessCounterValues(c, request, response, closure);
  }
}

void DanceproxyService::setReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicaPolicyRequestProto* request,
      ::cloudfs::SetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  if (!CheckReadOnly(c, done, request->path())) {
    return;
  }

  auto sm = GetStub("setReplicaPolicy", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->setReplicaPolicy(c, request, response, closure);
  }
}

void DanceproxyService::getReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetReplicaPolicyRequestProto* request,
      ::cloudfs::GetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto sm = GetStub("getReplicaPolicy", c, request->path());
  MethodTracer tracer(sm.metrics);
  if (!sm.stub) {
    c->MarkAsFailed(JavaExceptions::StandbyException(),
                    "Could not resolve path");
    done->Run();
  } else {
    auto closure = new MethodTracerClosure(c, done, std::move(tracer));
    sm.stub->getReplicaPolicy(c, request, response, closure);
  }
}

void DanceproxyService::getDistributed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDistributedRequestProto* request,
      ::cloudfs::GetDistributedResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "getDistributed");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "getDistributed");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

void DanceproxyService::addCompleteBlocksAndCloseFile(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCompleteBlocksAndCloseFileRequestProto* request,
      ::cloudfs::AddCompleteBlocksAndCloseFileResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  auto fs = mounts_manager_->Resolve("/");
  auto metrics = GetMethodMetricsFast(fs, "addCompleteBlocksAndCloseFile");
  if (!metrics) {
    metrics = GetMethodMetricsSlow(fs, "addCompleteBlocksAndCloseFile");
  }
  MethodTracer tracer(metrics);
  c->MarkAsFailed(JavaExceptions::IOException(),
                  "Invalid operation, do not use proxy.");
  done->Run();
}

}  // namespace dancenn

