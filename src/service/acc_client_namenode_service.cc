//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//
// ningw <EMAIL>>
//
#include "service/acc_client_namenode_service.h"

#include <glog/logging.h>

#include <iomanip>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/audit_logger.h"
#include "base/constants.h"
#include "base/java_exceptions.h"
#include "base/metrics.h"
#include "base/path_util.h"
#include "base/string_utils.h"
#include "namespace/create_flag.h"
#include "rpc/rpc_controller.h"
#include "rpc/rpc_server_connection.h"
#include "service/service_util.h"

DECLARE_bool(ufs_read_only);
DECLARE_bool(standby_read_set_seen_txid);
DECLARE_bool(dfs_support_append);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_uint64(dfs_namenode_fs_limits_min_block_size);
DECLARE_bool(retry_cache_enabled);
DECLARE_bool(disallow_deprecated_rpc);
DECLARE_bool(trace_rpc_log);
DECLARE_string(nameservice);
DECLARE_int64(request_ttl_since_emit_ms);
DECLARE_int32(rpc_in_queue_timeout_ms_client_req);
DECLARE_bool(dfs_ha_allow_stale_reads);

namespace dancenn {

using MR = MethodRecorder<AccClientNamenodeService>;

void CreateDefaultPermForAcc(const std::shared_ptr<UserGroupInfo>& ugi,
                             PermissionStatus* p) {
  p->set_username(ugi->current_user());
  p->set_groupname(ugi->current_group());

  /**
   * Will be set by sync task corresponding to inode type.
   * 0644 for file, 0755 for dir.
   */
  p->set_permission(0);
}

AccClientNamenodeService::AccClientNamenodeService(
    std::shared_ptr<DatanodeManager> datanode_manager,
    std::shared_ptr<BlockManager> block_manager,
    std::shared_ptr<AccNamespace> tos_ns,
    SafeModeBase* safemode,
    RetryCache* rc)
    : datanode_manager_(std::move(datanode_manager)),
      block_manager_(std::move(block_manager)),
      tos_ns_(std::move(tos_ns)),
      safemode_(safemode),
      retry_cache_(rc) {
  InitMetrics();
}

void AccClientNamenodeService::getBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockLocationsRequestProto* request,
      ::cloudfs::GetBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "getBlockLocations", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-getBlockLocations]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  PermissionStatus permission = PermissionStatus();
  CreateDefaultPermForAcc(ugi, &permission);

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException &&
          s.exception() != JavaExceptions::kHyperFileException) {
        DLOG(INFO) << "get BlockLocations failed request: "
                   << request->ShortDebugString() << " returns "
                   << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kGetBlockLocations,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_address.ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "getBlockLocations response "
                 << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kGetBlockLocations,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_address.ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncGetBlockLocations(
      normalized_path,
      permission,
      *ugi,
      rpc_info,
      client_location,
      c->rpc_request_header()->accfsinfo(),
      request,
      response,
      c,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

  // 透传
void AccClientNamenodeService::getServerDefaults(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetServerDefaultsRequestProto* request,
      ::cloudfs::GetServerDefaultsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getServerDefaults", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto server_default = response->mutable_serverdefaults();
  auto s = tos_ns_->GetServerDefaults(server_default);

  if (s.HasException()) {
    DLOG(INFO) << "getServerDefaults failed request: "
               << request->ShortDebugString() << " returns " << s.ToString();
    c->MarkAsFailed(s);
  } else {
    DLOG(INFO) << "getServerDefaults response " << response->ShortDebugString();
  }
}

  // 同步 File 信息，创建 TOS Object 占位，创建 Upload ID，设置 Tag
void AccClientNamenodeService::create(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateRequestProto* request,
      ::cloudfs::CreateResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "create", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-create]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }
  if (request->replication() < FLAGS_dfs_replication_min ||
      request->replication() > FLAGS_dfs_replication_max) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid replication number");
    return;
  }
  if (request->blocksize() < FLAGS_dfs_namenode_fs_limits_min_block_size) {
    c->MarkAsFailed(JavaExceptions::IOException(), "Invalid block size");
    return;
  }

  PermissionStatus permission = PermissionStatus();
  permission.set_username(ugi->GetRemoteUser());
  // Follow parents' permission.
  permission.set_groupname("");
  permission.set_permission(request->masked().perm());

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();
  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache enter");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Create file error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kCreate,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->clear_fs();
    } else {
      DLOG(INFO) << "Create " << request->src()
                 << " success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kCreate,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncCreateFile(normalized_path,
                           permission,
                           client_location,
                           *ugi,
                           rpc_info,
                           client_machine,
                           request,
                           response,
                           c,
                           dynamic_cast<RpcClosure*>(done_guard.release()),
                           c->rpc_request_header()->accfsinfo());
}

// 同步 File 信息，检查 TOS Object Tag
void AccClientNamenodeService::append(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AppendRequestProto* request,
      ::cloudfs::AppendResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "append", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!FLAGS_dfs_support_append) {
    c->MarkAsFailed(JavaExceptions::UnsupportedOperationException(),
                    "Append is not enabled on this NameNode, "
                    "check dfs_support_append configuration.");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "Append file: " << request->ShortDebugString();
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Append file error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAppend,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kAppend,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncAppend(normalized_path,
                       client_machine,
                       rpc_info,
                       *ugi,
                       request,
                       response,
                       dynamic_cast<RpcClosure*>(done_guard.release()),
                       c->rpc_request_header()->accfsinfo());
}

// 暂不支持，写入默认 2副本，读取缓存单副本
void AccClientNamenodeService::setReplication(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  MR recorder(this, "setReplication", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 暂不支持，写入默认 2副本，读取缓存单副本
void AccClientNamenodeService::setReplicationAttrOnly(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  MR recorder(this, "setReplicationAttrOnly", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 暂不支持
void AccClientNamenodeService::setStoragePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetStoragePolicyRequestProto* request,
      ::cloudfs::SetStoragePolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  MR recorder(this, "setStoragePolicy", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 暂不支持
void AccClientNamenodeService::getStoragePolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetStoragePoliciesRequestProto* request,
      ::cloudfs::GetStoragePoliciesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  MR recorder(this, "getStoragePolicies", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 暂不支持
void AccClientNamenodeService::setPermission(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetPermissionRequestProto* request,
      ::cloudfs::SetPermissionResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setPermission", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  // src and permission (required)
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Set Permission error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetPermission,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetPermission,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncSetPermission(normalized_path,
                              request->permission().perm(),
                              *ugi,
                              dynamic_cast<RpcClosure*>(done_guard.release()),
                              c->rpc_request_header()->accfsinfo());
}

// 暂不支持
void AccClientNamenodeService::setOwner(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetOwnerRequestProto* request,
      ::cloudfs::SetOwnerResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setOwner", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Set Owner error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetOwner,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetOwner,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncSetOwner(normalized_path,
                         request->username(),
                         request->groupname(),
                         *ugi,
                         dynamic_cast<RpcClosure*>(done_guard.release()),
                         c->rpc_request_header()->accfsinfo());
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
void AccClientNamenodeService::abandonBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AbandonBlockRequestProto* request,
      ::cloudfs::AbandonBlockResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "abandonBlock", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  // TODO(sunguoli): holder for lease
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "abandonBlock Failed: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAbandonBlock,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kAbandonBlock,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncAbandonBlock(normalized_path,
                             request,
                             dynamic_cast<RpcClosure*>(done_guard.release()),
                             c->rpc_request_header()->accfsinfo());
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
void AccClientNamenodeService::addBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddBlockRequestProto* request,
      ::cloudfs::AddBlockResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "addBlock", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-addBlock]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "addBlock Request " << request->ShortDebugString();
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache enter");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Add block failed," << request->ShortDebugString() << " "
                << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kAddBlock,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Add block success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kAddBlock,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncAddBlock(normalized_path,
                         client_location,
                         rpc_info,
                         *ugi,
                         request,
                         response,
                         c,
                         dynamic_cast<RpcClosure*>(done_guard.release()),
                         c->rpc_request_header()->accfsinfo());
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
void AccClientNamenodeService::complete(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CompleteRequestProto* request,
    ::cloudfs::CompleteResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "complete", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-complete]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "Complete: " << request->ShortDebugString();
  response->set_result(false);
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }

  PermissionStatus permission = PermissionStatus();
  CreateDefaultPermForAcc(ugi, &permission);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache enter");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Complete file " << normalized_path
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kComplete,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(s.IsOK());
      DLOG(INFO) << "complete response " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kComplete,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncCompleteFile(normalized_path,
                             request,
                             response,
                             c,
                             dynamic_cast<RpcClosure*>(done_guard.release()),
                             c->rpc_request_header()->accfsinfo());
}

  // 保持原有实现
void AccClientNamenodeService::getAdditionalDatanode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAdditionalDatanodeRequestProto* request,
      ::cloudfs::GetAdditionalDatanodeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "getAdditionalDatanode",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "calling additional datanode " << request->ShortDebugString();
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Failed to get " << request->numadditionalnodes()
                << " nodes for file " << request->src();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "additional datanode " << response->ShortDebugString();
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->GetAdditionalDatanode(
      normalized_path,
      client_location,
      *ugi,
      request,
      response,
      dynamic_cast<RpcClosure*>(done_guard.release()),
      c->rpc_request_header()->accfsinfo());
}

// 对于 KPersisted 文件，不需要
void AccClientNamenodeService::reportBadBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ReportBadBlocksRequestProto* request,
      ::cloudfs::ReportBadBlocksResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (!RequestPreCheck(c)) {
    return;
  }

  MR recorder(this, "reportBadBlocks", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 目前只有未上传文件支持 concat
void AccClientNamenodeService::concat(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ConcatRequestProto* request,
      ::cloudfs::ConcatResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "concat", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "Concat: " << request->ShortDebugString();

  if (request->trg().empty()) {
    c->MarkAsFailed(JavaExceptions::Exception::kIllegalArgumentException,
                    "Target file name is empty");
    return;
  }
  if (request->srcs_size() == 0) {
    c->MarkAsFailed(JavaExceptions::Exception::kIllegalArgumentException,
                    "No sources given");
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  std::string client_machine = ServiceUtil::GetClientAddress(c).ToString();
  const std::string& user_name = ugi->GetRemoteUser();

  std::string normalized_trg;
  std::vector<std::string> normalized_srcs;

  if (!NormalizePath(request->trg(), user_name, &normalized_trg)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid target path:" + request->trg());
    return;
  }

  for (int i = 0; i < request->srcs().size(); ++i) {
    const auto& src = request->srcs(i);
    std::string normalized_path;

    if (!NormalizePath(src, user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(), "Invalid src path:" + src);
      return;
    }
    normalized_srcs.push_back(normalized_path);
  }

  PermissionStatus permission = PermissionStatus();
  CreateDefaultPermForAcc(ugi, &permission);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Concat file " << normalized_trg
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kConcat,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "concat response " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kConcat,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncConcat(normalized_trg,
                       normalized_srcs,
                       rpc_info,
                       dynamic_cast<RpcClosure*>(done_guard.release()),
                       c->rpc_request_header()->accfsinfo());
}

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
void AccClientNamenodeService::rename(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameRequestProto* request,
      ::cloudfs::RenameResponseProto* response,
      ::google::protobuf::Closure* done) {
  // Not allowed overwrite.
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "rename", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  response->set_result(false);
  DLOG(INFO) << "rename called, " << request->src();  // for debug

  if (!c->rpc_request_header()->has_accfsinfo()) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Illegal request, accFsInfo is missing.");
    return;
  }

  auto opt = std::make_shared<RenameToOption>();
  {
    std::string src;
    std::string dst;
    auto ugi = GetRemoteUserInfo(c);
    auto user_name = ugi->GetRemoteUser();
    if (!NormalizePath(request->src(), user_name, &src)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path:" + request->src());
      return;
    }
    if (!NormalizePath(request->dst(), user_name, &dst)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path:" + request->dst());
      return;
    }

    opt->src = std::move(src);
    opt->dst = std::move(dst);
    opt->overwrite = false;

    CreateDefaultPermForAcc(ugi, &opt->perm);
    opt->ugi = std::move(*ugi);

    opt->rpc_info = ServiceUtil::GetLogRpcInfo(c);
    opt->acc_fs_info = c->rpc_request_header()->accfsinfo();
    opt->acc_max_count = request->maxcount();
    opt->acc_max_size = static_cast<int64_t>(request->maxsize()) * 1024 * 1024 *
                        1024;  // GiB to Byte
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "rename error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRename,
                           opt->ugi.GetRemoteUser(),
                           opt->ugi.current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(s.IsOK());
      DLOG(INFO) << "Rename success src: " << opt->src << ", dst: " << opt->dst;
      DANCENN_AUDIT_LOG2(AuditLog::kRename,
                         opt->ugi.GetRemoteUser(),
                         opt->ugi.current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncRenameTo(
      opt, dynamic_cast<RpcClosure*>(done_guard.release()), response);
}

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
void AccClientNamenodeService::rename2(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::Rename2RequestProto* request,
      ::cloudfs::Rename2ResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "rename2", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  auto opt = std::make_shared<RenameToOption>();
  {
    std::string src;
    std::string dst;
    auto ugi = GetRemoteUserInfo(c);
    std::string user_name = ugi->GetRemoteUser();
    if (!NormalizePath(request->src(), user_name, &src)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path:" + request->src());
      return;
    }
    if (!NormalizePath(request->dst(), user_name, &dst)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path:" + request->dst());
      return;
    }

    opt->src = std::move(src);
    opt->dst = std::move(dst);
    opt->overwrite = request->overwritedest();

    CreateDefaultPermForAcc(ugi, &opt->perm);
    opt->ugi = std::move(*ugi);

    opt->rpc_info = ServiceUtil::GetLogRpcInfo(c);
    opt->acc_fs_info = c->rpc_request_header()->accfsinfo();
    opt->acc_max_count = request->maxcount();
    opt->acc_max_size = static_cast<int64_t>(request->maxsize()) * 1024 * 1024 *
                        1024;  // GiB to Byte
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "rename2 error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRename2,
                           opt->ugi.GetRemoteUser(),
                           opt->ugi.current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kRename2,
                         opt->ugi.GetRemoteUser(),
                         opt->ugi.current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncRenameTo2(
      opt, dynamic_cast<RpcClosure*>(done_guard.release()), response);
}

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
void AccClientNamenodeService::Delete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteRequestProto* request,
      ::cloudfs::DeleteResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "Delete", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  VLOG(8) << "Delete rpc, src: " << request->src();
  if (!c->rpc_request_header()->has_accfsinfo()) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Illegal request, accFsInfo is missing.");
    return;
  }

  response->set_result(false);

  auto opt = std::make_shared<DeleteOption>();
  {
    std::string normalized_path;
    auto ugi = GetRemoteUserInfo(c);
    std::string user_name = ugi->GetRemoteUser();
    if (!NormalizePath(request->src(), user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path: " + request->src());
      return;
    }

    opt->path = std::move(normalized_path);
    opt->recursive = request->recursive();

    CreateDefaultPermForAcc(ugi, &opt->perm);
    opt->ugi = std::move(*ugi);

    opt->rpc_info = ServiceUtil::GetLogRpcInfo(c);
    opt->acc_fs_info = c->rpc_request_header()->accfsinfo();
    opt->acc_max_count = request->maxcount();
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Delete " << request->src() << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kDelete,
                           opt->ugi.GetRemoteUser(),
                           opt->ugi.current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      response->set_result(s.IsOK());
      DANCENN_AUDIT_LOG2(AuditLog::kDelete,
                         opt->ugi.GetRemoteUser(),
                         opt->ugi.current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncDelete(
      opt, response, dynamic_cast<RpcClosure*>(done_guard.release()));
}

// Local 操作，不做改动. 会在 tos 创建一个 d/ 的目录(同 tos 网页).
void AccClientNamenodeService::mkdirs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MkdirsRequestProto* request,
      ::cloudfs::MkdirsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "mkdirs", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-mkdirs]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "mkdirs called, " << request->src();  // for debug
  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  PermissionStatus permission = PermissionStatus();
  permission.set_username(user_name);
  permission.set_groupname("");
  permission.set_permission(request->masked().perm());

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Mkdir error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kMkdirs,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->set_result(false);
    } else {
      DLOG(INFO) << "Mkdir " << normalized_path << " success";
      response->set_result(true);
      DANCENN_AUDIT_LOG2(AuditLog::kMkdirs,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncMkDirs(normalized_path,
                       permission,
                       *ugi,
                       request->createparent(),
                       request,
                       response,
                       c,
                       dynamic_cast<RpcClosure*>(done_guard.release()),
                       c->rpc_request_header()->accfsinfo());

}

// 同步File或者目录元数据；支持 Paging 和异步化 Sync
void AccClientNamenodeService::getListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetListingRequestProto* request,
      ::cloudfs::GetListingResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "getListing", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-getListing]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  auto opt = std::make_shared<ListingOption>();
  {
    std::string normalized_path;
    auto ugi = GetRemoteUserInfo(c);
    std::string user_name = ugi->GetRemoteUser();
    if (!NormalizePath(request->src(), user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path: " + request->src());
      return;
    }

    PermissionStatus permission = PermissionStatus();
    CreateDefaultPermForAcc(ugi, &permission);

    auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

    opt->path = std::move(normalized_path);
    opt->start_after = request->startafter();
    opt->need_location = request->needlocation();
    opt->acc_max_count = request->maxcount();
    opt->perm = std::move(permission);
    opt->ugi = std::move(*ugi);
    opt->acc_fs_info = c->rpc_request_header()->accfsinfo();
    opt->rpc_info = std::move(rpc_info);
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      response->clear_dirlist();
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "getListing " << request->src()
                << ", normalized: " << opt->path << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kGetListing,
                           opt->ugi.GetRemoteUser(),
                           opt->ugi.current_group(),
                           client_address.ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kGetListing,
                         opt->ugi.GetRemoteUser(),
                         opt->ugi.current_group(),
                         client_address.ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncGetListing(opt,
                           client_location,
                           *request,
                           response,
                           c,
                           dynamic_cast<RpcClosure*>(done_guard.release()));
}

// - 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::renewLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewLeaseRequestProto* request,
      ::cloudfs::RenewLeaseResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "renewLease", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  auto client_ip = ServiceUtil::GetClientAddress(c).ToString();
  auto s = tos_ns_->RenewLease(request->clientname(), client_ip);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      auto ugi = GetRemoteUserInfo(c);
      DANCENN_AUDIT_LOG2(AuditLog::kRenewLease,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    auto ugi = GetRemoteUserInfo(c);
    DANCENN_AUDIT_LOG2(AuditLog::kRenewLease,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }

  RETRY_CACHE_EXIT(s, response);
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::recoverLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RecoverLeaseRequestProto* request,
      ::cloudfs::RecoverLeaseResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "recoverLease", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }
  auto s = tos_ns_->RecoverLease(request->clientname(),
                                 normalized_path,
                                 request,
                                 c->rpc_request_header()->accfsinfo());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      auto ugi = GetRemoteUserInfo(c);
      DANCENN_AUDIT_LOG2(AuditLog::kRecoverLease,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    response->set_result(s.IsOK());
    auto ugi = GetRemoteUserInfo(c);
    DANCENN_AUDIT_LOG2(AuditLog::kRecoverLease,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }

  RETRY_CACHE_EXIT(s, response);
}

  // 维持现有实现
void AccClientNamenodeService::getFsStats(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFsStatusRequestProto* request,
      ::cloudfs::GetFsStatsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getFsStats", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto s = tos_ns_->GetFsStats(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // 维持现有实现
void AccClientNamenodeService::getDatanodeReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeReportRequestProto* request,
      ::cloudfs::GetDatanodeReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getDatanodeReport", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  auto s = datanode_manager_->GetDatanodeReport(
      request->type(), client_location, response->mutable_di());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // 维持现有实现
void AccClientNamenodeService::getDatanodeStorageReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeStorageReportRequestProto* request,
      ::cloudfs::GetDatanodeStorageReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getDatanodeStorageReport", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);

  auto s = datanode_manager_->GetDatanodeStorageReport(
      request->type(),
      client_location,
      response->mutable_datanodestoragereports());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // 维持现有实现
void AccClientNamenodeService::getPreferredBlockSize(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetPreferredBlockSizeRequestProto* request,
      ::cloudfs::GetPreferredBlockSizeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getPreferredBlockSize", c, wrapped_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->filename(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->filename());
    return;
  }

  auto s = tos_ns_->GetPreferredBlockSize(normalized_path, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // 维持现有实现
void AccClientNamenodeService::setSafeMode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetSafeModeRequestProto* request,
      ::cloudfs::SetSafeModeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "setSafeMode", c, wrapped_done);

  ::cloudfs::SafeModeActionProto action = request->action();
  OperationsCategory op = OperationsCategory::kUnchecked;
  if (request->checked()) {
    if (action == ::cloudfs::SafeModeActionProto::SAFEMODE_GET) {
      op = OperationsCategory::kRead;
    } else {
      op = OperationsCategory::kWrite;
    }
  }
  auto s = safemode_->SetSafeMode(action, op);
  LOG(INFO) << "setSafeMode: result=" << s.ToString();
  if (s.HasException()) {
    c->MarkAsFailed(s);
  } else {
    response->set_result(s.IsOK() /* true means safe mode is ON */);
  }
}

  // 现在未实现
void AccClientNamenodeService::saveNamespace(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SaveNamespaceRequestProto* request,
      ::cloudfs::SaveNamespaceResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "saveNamespace", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::rollEdits(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollEditsRequestProto* request,
      ::cloudfs::RollEditsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "rollEdits", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::restoreFailedStorage(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RestoreFailedStorageRequestProto* request,
      ::cloudfs::RestoreFailedStorageResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "restoreFailedStorage", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 维持现有实现
void AccClientNamenodeService::refreshNodes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RefreshNodesRequestProto* request,
      ::cloudfs::RefreshNodesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "refreshNodes", c, wrapped_done);

  Status s = tos_ns_->RefreshDataNodes();
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "RefreshDataNodes error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // 现在未实现
void AccClientNamenodeService::finalizeUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FinalizeUpgradeRequestProto* request,
      ::cloudfs::FinalizeUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "finalizeUpgrade", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::rollingUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollingUpgradeRequestProto* request,
      ::cloudfs::RollingUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "finalizeUpgrade", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}


  // 现在未实现
void AccClientNamenodeService::listCorruptFileBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCorruptFileBlocksRequestProto* request,
      ::cloudfs::ListCorruptFileBlocksResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCorruptFileBlocks", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
};

  // 现在未实现
void AccClientNamenodeService::metaSave(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MetaSaveRequestProto* request,
      ::cloudfs::MetaSaveResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "metaSave", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 同 getBlockLocations
void AccClientNamenodeService::getFileInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileInfoRequestProto* request,
      ::cloudfs::GetFileInfoResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "getFileInfo", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-getFileInfo]", "path=" + request->src());
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  PermissionStatus permission = PermissionStatus();
  CreateDefaultPermForAcc(ugi, &permission);

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      response->clear_fs();
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "getFileInfo " << request->src()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kGetFileInfo,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_address.ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kGetFileInfo,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_address.ToString(),
                         s,
                         request,
                         response);
    }

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncGetFileInfo(
      normalized_path,
      client_location,
      true,
      request->has_needlocation() ? request->needlocation() : false,
      permission,
      *ugi,
      c->rpc_request_header()->accfsinfo(),
      response,
      c,
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

// 现在未实现
void AccClientNamenodeService::addCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCacheDirectiveRequestProto* request,
      ::cloudfs::AddCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "addCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::modifyCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCacheDirectiveRequestProto* request,
      ::cloudfs::ModifyCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::removeCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCacheDirectiveRequestProto* request,
      ::cloudfs::RemoveCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeCacheDirective", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::listCacheDirectives(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCacheDirectivesRequestProto* request,
      ::cloudfs::ListCacheDirectivesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCacheDirectives", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::addCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCachePoolRequestProto* request,
      ::cloudfs::AddCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "addCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::modifyCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCachePoolRequestProto* request,
      ::cloudfs::ModifyCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::removeCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCachePoolRequestProto* request,
      ::cloudfs::RemoveCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeCachePool", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::listCachePools(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCachePoolsRequestProto* request,
      ::cloudfs::ListCachePoolsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listCachePools", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getFileLinkInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileLinkInfoRequestProto* request,
      ::cloudfs::GetFileLinkInfoResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getFileLinkInfo", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 递归同步元数据，超过限制报错不支持，主要是 du 命令在用
void AccClientNamenodeService::getContentSummary(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetContentSummaryRequestProto* request,
      ::cloudfs::GetContentSummaryResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getContentSummary", c, wrapped_done);
  c->AllowStandbyRead();

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  if (!NormalizePath(request->path(), ugi->GetRemoteUser(), &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->path());
    return;
  }

  Status s = tos_ns_->GetContentSummary(normalized_path, ugi, response, c, c->rpc_request_header()->accfsinfo());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getContentSummary" << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetContentSummary,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
    response->Clear();
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetContentSummary,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

  // 现在未实现
void AccClientNamenodeService::setQuota(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetQuotaRequestProto* request,
      ::cloudfs::SetQuotaResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setQuota", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::fsync(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FsyncRequestProto* request,
      ::cloudfs::FsyncResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "fsync", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  auto user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Failed to fsync " << request->src()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kFsync,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kFsync,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncFsync(normalized_path,
                      request,
                      dynamic_cast<RpcClosure*>(done_guard.release()),
                      c->rpc_request_header()->accfsinfo());
}

  // - 先改成不支持，没有意义；可以设到 Object 的 Meta 里
void AccClientNamenodeService::setTimes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetTimesRequestProto* request,
      ::cloudfs::SetTimesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "setTimes", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  auto user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "setTimes " << request->src() << " failed: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncSetTimes(normalized_path,
                         request,
                         *ugi,
                         dynamic_cast<RpcClosure*>(done_guard.release()),
                         c->rpc_request_header()->accfsinfo());
}

  // 现在未实现
void AccClientNamenodeService::createSymlink(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSymlinkRequestProto* request,
      ::cloudfs::CreateSymlinkResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "createSymlink", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getLinkTarget(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLinkTargetRequestProto* request,
      ::cloudfs::GetLinkTargetResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getLinkTarget", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::updateBlockForPipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdateBlockForPipelineRequestProto* request,
      ::cloudfs::UpdateBlockForPipelineResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "updateBlockForPipeline",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  LogRpcInfo rpc_info(c->rpc_request_header()->clientid(),
                      static_cast<uint32_t>(c->rpc_request_header()->callid()),
                      false);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "UpdateBlockForPipeline error: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "UpdateBlockForPipeline successfully"
                 << response->ShortDebugString();
    }

    RETRY_CACHE_EXIT(s, response);
  });

  auto ugi = GetRemoteUserInfo(c);

  tos_ns_->AsyncUpdateBlockForPipeline(
      request,
      rpc_info,
      response,
      *ugi,
      dynamic_cast<RpcClosure*>(done_guard.release()),
      c->rpc_request_header()->accfsinfo());
}

  // - 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::updatePipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdatePipelineRequestProto* request,
      ::cloudfs::UpdatePipelineResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "updatePipeline", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        VLOG(8) << "Update pipeline error: " << s.ToString();
        c->MarkAsFailed(s);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Update pipeline successfully"
                 << response->ShortDebugString();
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncUpdatePipeline(request,
                               rpc_info,
                               dynamic_cast<RpcClosure*>(done_guard.release()),
                               c->rpc_request_header()->accfsinfo());
}

  // 现在未实现
void AccClientNamenodeService::getDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDelegationTokenRequestProto* request,
      ::cloudfs::GetDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::renewDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewDelegationTokenRequestProto* request,
      ::cloudfs::RenewDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "renewDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::cancelDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CancelDelegationTokenRequestProto* request,
      ::cloudfs::CancelDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "cancelDelegationToken", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::setBalancerBandwidth(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetBalancerBandwidthRequestProto* request,
      ::cloudfs::SetBalancerBandwidthResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setBalancerBandwidth", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getDataEncryptionKey(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDataEncryptionKeyRequestProto* request,
      ::cloudfs::GetDataEncryptionKeyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getDataEncryptionKey", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::createSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSnapshotRequestProto* request,
      ::cloudfs::CreateSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "createSnapshot", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::renameSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameSnapshotRequestProto* request,
      ::cloudfs::RenameSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "renameSnapshot", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::allowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AllowSnapshotRequestProto* request,
      ::cloudfs::AllowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "allowSnapshot", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::disallowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DisallowSnapshotRequestProto* request,
      ::cloudfs::DisallowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "disallowSnapshot", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getSnapshottableDirListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshottableDirListingRequestProto* request,
      ::cloudfs::GetSnapshottableDirListingResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getSnapshottableDirListing", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::deleteSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteSnapshotRequestProto* request,
      ::cloudfs::DeleteSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "deleteSnapshot", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getSnapshotDiffReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshotDiffReportRequestProto* request,
      ::cloudfs::GetSnapshotDiffReportResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getSnapshotDiffReport", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::isFileClosed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IsFileClosedRequestProto* request,
      ::cloudfs::IsFileClosedResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "isFileClosed", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->src());
    return;
  }

  auto s = tos_ns_->IsFileClosed(
      normalized_path, request, response, c, c->rpc_request_header()->accfsinfo());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kIsFileClosed,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kIsFileClosed,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

  // 现在未实现
void AccClientNamenodeService::modifyAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyAclEntriesRequestProto* request,
      ::cloudfs::ModifyAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "modifyAclEntries", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::removeAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclEntriesRequestProto* request,
      ::cloudfs::RemoveAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeAclEntries", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::removeDefaultAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveDefaultAclRequestProto* request,
      ::cloudfs::RemoveDefaultAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeDefaultAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::removeAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclRequestProto* request,
      ::cloudfs::RemoveAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::setAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetAclRequestProto* request,
      ::cloudfs::SetAclResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "setAcl", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // 现在未实现
void AccClientNamenodeService::getAclStatus(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAclStatusRequestProto* request,
      ::cloudfs::GetAclStatusResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getAclStatus", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
void AccClientNamenodeService::setXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetXAttrRequestProto* request,
      ::cloudfs::SetXAttrResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "finalizeUpgrade", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
void AccClientNamenodeService::getXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetXAttrsRequestProto* request,
      ::cloudfs::GetXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getXAttrs", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
void AccClientNamenodeService::listXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListXAttrsRequestProto* request,
      ::cloudfs::ListXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listXAttrs", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
void AccClientNamenodeService::removeXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveXAttrRequestProto* request,
      ::cloudfs::RemoveXAttrResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "removeXAttr", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void AccClientNamenodeService::checkAccess(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CheckAccessRequestProto* request,
      ::cloudfs::CheckAccessResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "checkAccess", c, wrapped_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->path());
    return;
  }

  auto s = tos_ns_->CheckAccess(ugi, request, response, c, c->rpc_request_header()->accfsinfo());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

  // NN 未实现
void AccClientNamenodeService::createEncryptionZone(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateEncryptionZoneRequestProto* request,
      ::cloudfs::CreateEncryptionZoneResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "createEncryptionZone", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::listEncryptionZones(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListEncryptionZonesRequestProto* request,
      ::cloudfs::ListEncryptionZonesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listEncryptionZones", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::getEZForPath(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEZForPathRequestProto* request,
      ::cloudfs::GetEZForPathResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getEZForPath", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::getCurrentEditLogTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetCurrentEditLogTxidRequestProto* request,
      ::cloudfs::GetCurrentEditLogTxidResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getCurrentEditLogTxid", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::getEditsFromTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEditsFromTxidRequestProto* request,
      ::cloudfs::GetEditsFromTxidResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getEditsFromTxid", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::increaseAccessCounter(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IncreaseAccessCounterRequestProto* request,
      ::cloudfs::IncreaseAccessCounterResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "increaseAccessCounter", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // NN 未实现
void AccClientNamenodeService::getAccessCounterValues(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAccessCounterValuesRequestProto* request,
      ::cloudfs::GetAccessCounterValuesResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getAccessCounterValues", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

void AccClientNamenodeService::setDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetDirPolicyRequestProto* request,
    ::cloudfs::SetDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "set dir policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetDirPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  tos_ns_->AsyncSetDirPolicy(normalized_path,
                             request,
                             c->rpc_request_header()->accfsinfo(),
                             dynamic_cast<RpcClosure*>(done_guard.release()));
}

void AccClientNamenodeService::removeDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::RemoveDirPolicyRequestProto* request,
    ::cloudfs::RemoveDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "removeDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "set replica policy " << request->path()
                << " error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kRemoveDirPolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kRemoveDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  tos_ns_->AsyncRemoveDirPolicy(
      normalized_path,
      request,
      c->rpc_request_header()->accfsinfo(),
      dynamic_cast<RpcClosure*>(done_guard.release()));
}

void AccClientNamenodeService::getDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetDirPolicyRequestProto* request,
    ::cloudfs::GetDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "getDirPolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  std::string normalized_path;
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  Status s = tos_ns_->GetDirPolicy(
      normalized_path, request, response, c->rpc_request_header()->accfsinfo());

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException &&
        s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
      VLOG(8) << "get dir policy " << request->path()
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetDirPolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetDirPolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void AccClientNamenodeService::listDirPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListDirPolicyRequestProto* request,
    ::cloudfs::ListDirPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "listDirPolicy", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  Status s = tos_ns_->ListDirPolicy(request, response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "listDirPolicy "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

// - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::setReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicaPolicyRequestProto* request,
      ::cloudfs::SetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "setReplicaPolicy", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::getReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetReplicaPolicyRequestProto* request,
      ::cloudfs::GetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "getReplicaPolicy", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

  // - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::getDistributed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDistributedRequestProto* request,
      ::cloudfs::GetDistributedResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "getDistributed", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::setReadPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetReadPolicyRequestProto* request,
    ::cloudfs::SetReadPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "setReadPolicy", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::getReadPolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetReadPolicyRequestProto* request,
    ::cloudfs::GetReadPolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "getReadPolicy", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::listReadPolicies(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::ListReadPoliciesRequestProto* request,
    ::cloudfs::ListReadPoliciesResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  if (FLAGS_disallow_deprecated_rpc) {
    auto msg = "call deprecated rpc. rpc=setReplicaPolicy";
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG(ERROR) << msg;
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(), msg);
    return;
  }

  MR recorder(this, "listReadPolicies", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 先改成不支持，透明加速场景没有意义
void AccClientNamenodeService::addCompleteBlocksAndCloseFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::AddCompleteBlocksAndCloseFileRequestProto* request,
    ::cloudfs::AddCompleteBlocksAndCloseFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "addCompleteBlocksAndCloseFile", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 维持现有实现
// - 这个接口是 NNproxy 在用，不应该加到 ClientNamenodeProtocol 里面，Client
// 端并不需要
void AccClientNamenodeService::getBlockKeys(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetBlockKeysRequestProto* request,
    ::cloudfs::GetBlockKeysResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "getBlockKeys", c, done);

  Status s = tos_ns_->GetBlockKeys(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getBlockKeys "
              << " error: " << s.ToString();
      c->MarkAsFailed(s);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  }
}

// - 检查文件是否是正在写入的文件，kToBePersisted
void AccClientNamenodeService::msync(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::MsyncRequestProto* request,
    ::cloudfs::MsyncResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "msync", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// - 不需要，禁用
void AccClientNamenodeService::getHyperBlockLocations(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetHyperBlockLocationsRequestProto* request,
    ::cloudfs::GetHyperBlockLocationsResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  MR recorder(this, "getHyperBlockLocations", c, wrapped_done);
  c->MarkAsFailed(JavaExceptions::IOException(), "Not Implemented");
}

// 维持现有实现
void AccClientNamenodeService::getHAServiceState(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::HAServiceStateRequestProto* request,
      ::cloudfs::HAServiceStateResponseProto* response,
      ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getHAServiceState", c, wrapped_done);

  if (!RequestPreCheck(c, false)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  Status s = tos_ns_->GetHAServiceState(response);
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "getHAServiceState error: " << s.ToString();
      c->MarkAsFailed(s);

      DANCENN_AUDIT_LOG(
          "{"
          "\"method\":\"getHAServiceState\","
          "\"status\":\"%s\","
          "\"user\":\"%s\","
          "\"group\":\"%s\","
          "\"ip\":\"%s\"}",
          s.ExceptionStr().c_str(),
          ugi->GetRemoteUser().c_str(),
          ugi->current_group().c_str(),
          ServiceUtil::GetClientAddress(c).ToString().c_str());
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG(
        "{"
        "\"method\":\"getHAServiceState\","
        "\"status\":\"%s\","
        "\"user\":\"%s\","
        "\"group\":\"%s\","
        "\"ip\":\"%s\""
        "\"state\":\"%s\"}",
        s.ExceptionStr().c_str(),
        ugi->GetRemoteUser().c_str(),
        ugi->current_group().c_str(),
        ServiceUtil::GetClientAddress(c).ToString().c_str(),
        cloudfs::HAServiceStateProto_Name(response->state()).c_str());
  }
}

void AccClientNamenodeService::setLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::SetLifecyclePolicyRequestProto* request,
    ::cloudfs::SetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(
      this, "setLifecyclePolicy", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("setLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kSetLifecyclePolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      VLOG(8) << absl::StrFormat(
          "setLifecyclePolicy failed, response %s, st %s",
          response->ShortDebugString(),
          s.ToString());
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kSetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
      VLOG(8) << absl::StrFormat(
          "setLifecyclePolicy succeed, response %s, st %s",
          response->ShortDebugString(),
          s.ToString());
    }
  });

  tos_ns_->AsyncSetLifecyclePolicy(
      normalized_path,
      *request,
      response,
      *ugi,
      rpc_info,
      dynamic_cast<RpcClosure*>(done_guard.release()),
      c->rpc_request_header()->accfsinfo());
}

void AccClientNamenodeService::unsetLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::UnsetLifecyclePolicyRequestProto* request,
    ::cloudfs::UnsetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);

  auto rpc_done = dancenn::NewRpcCallback(this,
                                          "unsetLifecyclePolicy",
                                          controller,
                                          request,
                                          response,
                                          wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("unsetLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kUnsetLifecyclePolicy,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      VLOG(8) << absl::StrFormat(
          "unsetLifecyclePolicy failed, response %s, st %s",
          response->ShortDebugString(),
          s.ToString());
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kUnsetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
      VLOG(8) << absl::StrFormat(
          "unsetLifecyclePolicy succeed, response %s, st %s",
          response->ShortDebugString(),
          s.ToString());
    }
  });

  tos_ns_->AsyncUnsetLifecyclePolicy(
      normalized_path,
      *request,
      response,
      *ugi,
      rpc_info,
      dynamic_cast<RpcClosure*>(done_guard.release()),
      c->rpc_request_header()->accfsinfo());
}

void AccClientNamenodeService::getLifecyclePolicy(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::GetLifecyclePolicyRequestProto* request,
    ::cloudfs::GetLifecyclePolicyResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "getLifecyclePolicy", c, wrapped_done);
  c->AllowStandbyRead();

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->path(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path: " + request->path());
    return;
  }

  VLOG(8) << absl::StrFormat("getLifecyclePolicy start, request: %s",
                             request->ShortDebugString());

  auto s = tos_ns_->GetLifecyclePolicy(normalized_path,
                                       response->mutable_lifecyclepolicy(),
                                       *ugi,
                                       c,
                                       c->rpc_request_header()->accfsinfo());
  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kGetLifecyclePolicy,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
    VLOG(8) << absl::StrFormat("getLifecyclePolicy failed, response %s, st %s",
                               response->ShortDebugString(),
                               s.ToString());
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kGetLifecyclePolicy,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
    VLOG(8) << absl::StrFormat("getLifecyclePolicy failed, response %s, st %s",
                               response->ShortDebugString(),
                               s.ToString());
  }
}

void AccClientNamenodeService::load(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::LoadRequestProto* request,
    ::cloudfs::LoadResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "load", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  bool load_data = request->data();
  bool load_metadata = request->metadata();
  if (!(load_data ^ load_metadata)) {
    c->MarkAsFailed(JavaExceptions::IllegalArgumentException(),
                    "Cannot load both data and metadata at the same time.");
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }

  auto audit_log_method = AuditLog::kLoadData;
  if (load_metadata) {
    audit_log_method = AuditLog::kLoadMetadata;
  }

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        LOG(INFO) << "Load meta/data error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(audit_log_method,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DANCENN_AUDIT_LOG2(audit_log_method,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  // @rpc_done will be submitted to async procedure eventually
  CHECK(load_data ^ load_metadata);
  if (load_data) {
    tos_ns_->AsyncLoadData(normalized_path,
                           request,
                           response,
                           c->rpc_request_header()->accfsinfo(),
                           dynamic_cast<RpcClosure*>(done_guard.release()));
  } else {
    auto recursive_opt = std::make_shared<RecursiveListingOption>();
    {
      ListingOption opt;
      PermissionStatus permission = PermissionStatus();
      CreateDefaultPermForAcc(ugi, &permission);

      auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

      opt.path = normalized_path;
      opt.start_after = kInitStartAfter;
      opt.need_location = false;
      opt.acc_max_count = -1;
      opt.perm = std::move(permission);
      opt.ugi = std::move(*ugi);
      opt.acc_fs_info = c->rpc_request_header()->accfsinfo();
      opt.rpc_info = std::move(rpc_info);

      recursive_opt->listing_opt = std::move(opt);
      recursive_opt->recursive = request->recursive();
    }

    tos_ns_->AsyncLoadMetadata(normalized_path,
                               request,
                               response,
                               recursive_opt,
                               dynamic_cast<RpcClosure*>(done_guard.release()));
  }
}

void AccClientNamenodeService::free(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::FreeRequestProto* request,
    ::cloudfs::FreeResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "free", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }
  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        DLOG(INFO) << "Free error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kFree,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->set_jobid(INVALID_JOB_ID);
      response->set_done(false);
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kFree,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
  });

  tos_ns_->AsyncFree(normalized_path,
                     request,
                     response,
                     c->rpc_request_header()->accfsinfo(),
                     *ugi,
                     rpc_info,
                     dynamic_cast<RpcClosure*>(done_guard.release()));
}

void AccClientNamenodeService::pin(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::PinRequestProto* request,
    ::cloudfs::PinResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "pin", controller, request, response, wrapped_done);
  ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  std::string normalized_path;
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();
  if (!NormalizePath(request->src(), user_name, &normalized_path)) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Invalid path:" + request->src());
    return;
  }
  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException) {
        DLOG(INFO) << "Pin error: " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kPin,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
      response->set_result(false);
    } else {
      DANCENN_AUDIT_LOG2(AuditLog::kPin,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
      response->set_result(true);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncPin(normalized_path,
                    request,
                    response,
                    c->rpc_request_header()->accfsinfo(),
                    rpc_info,
                    dynamic_cast<RpcClosure*>(done_guard.release()));
}

void AccClientNamenodeService::lookupJob(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::LookupJobRequestProto* request,
    ::cloudfs::LookupJobResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  MR recorder(this, "lookupJob", c, wrapped_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  auto ugi = GetRemoteUserInfo(c);
  Status s = tos_ns_->LookupJob(request, response, c);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "lookupJob " << request->jobid() << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kLookupJob,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kLookupJob,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }
}

void AccClientNamenodeService::cancelJob(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::CancelJobRequestProto* request,
    ::cloudfs::CancelJobResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "cancelJob", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  if (!RequestPreCheck(c)) {
    return;
  }

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  auto ugi = GetRemoteUserInfo(c);
  Status s = tos_ns_->CancelJob(request, response, c);

  if (s.HasException()) {
    if (s.exception() != JavaExceptions::kSafeModeException &&
        s.exception() != JavaExceptions::kStandbyException) {
      VLOG(8) << "cancelJob " << request->jobid() << " error: " << s.ToString();
      c->MarkAsFailed(s);
      DANCENN_AUDIT_LOG2(AuditLog::kCancelJob,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    } else {
      c->MarkAsWeakFailed(s.exception(), s.message());
    }
  } else {
    DANCENN_AUDIT_LOG2(AuditLog::kCancelJob,
                       ugi->GetRemoteUser(),
                       ugi->current_group(),
                       ServiceUtil::GetClientAddress(c).ToString(),
                       s,
                       request,
                       response);
  }

  RETRY_CACHE_EXIT(s, response);
}

void AccClientNamenodeService::batchCreateFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::BatchCreateFileRequestProto* request,
    ::cloudfs::BatchCreateFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "batchCreateFile", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string first_path;
  if (request->files().size()) {
    first_path = request->files().Get(0).src();
  }
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-batchCreateFile]", "path[0]=" + first_path);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "batchCreateFile Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  std::vector<std::string> paths;
  for (int i = 0; i < request->files_size(); ++i) {
    auto path = request->files().Get(i).src();

    std::string normalized_path;
    if (!NormalizePath(path, user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path: " + path);
      return;
    }

    paths.push_back(normalized_path);
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_machine = client_address.ToString();
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache enter");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "Batch Create failed," << request->ShortDebugString() << " "
                << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kBatchCreateFile,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           client_machine,
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "Batch Create success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kBatchCreateFile,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         client_machine,
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncBatchCreate(paths,
                            client_location,
                            client_machine,
                            *ugi,
                            request,
                            response,
                            rpc_info,
                            dynamic_cast<RpcClosure*>(done_guard.release()),
                            c,
                            c->rpc_request_header()->accfsinfo());
}

void AccClientNamenodeService::batchCompleteFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::BatchCompleteFileRequestProto* request,
    ::cloudfs::BatchCompleteFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "batchCompleteFile", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string first_path;
  if (request->singlefile().size()) {
    first_path = request->singlefile().Get(0).src();
  } else {
    if (request->concatfile().size()) {
      if (request->concatfile().Get(0).srcs().size()) {
        first_path = request->concatfile().Get(0).srcs().Get(0).src();
      } else if (request->concatfile().Get(0).has_target()) {
        first_path = request->concatfile().Get(0).target().src();
      }
    }
  }
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-batchCompleteFile]", "path[0]=" + first_path);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "batchCompleteFile Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  std::vector<std::string> single_file_paths;
  std::vector<std::vector<std::string>> concat_file_srcs_paths;
  std::vector<std::string> concat_file_target_paths;
  // single
  for (const auto& entry : request->singlefile()) {
    std::string normalized_path;
    if (!NormalizePath(entry.src(), user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path: " + entry.src());
      return;
    }
    single_file_paths.push_back(normalized_path);
  }
  // concat
  for (const auto& entry : request->concatfile()) {
    // src
    concat_file_srcs_paths.push_back({});
    for (const auto& src_entry : entry.srcs()) {
      std::string normalized_path;
      if (!NormalizePath(src_entry.src(), user_name, &normalized_path)) {
        c->MarkAsFailed(JavaExceptions::IOException(),
                        "Invalid path: " + src_entry.src());
        return;
      }
      concat_file_srcs_paths.back().push_back(normalized_path);
    }

    // target
    std::string normalized_path;
    if (!NormalizePath(entry.target().src(), user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(),
                      "Invalid path: " + entry.target().src());
      return;
    }
    concat_file_target_paths.push_back(normalized_path);
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);

  rpc_done->add_post_callback([=](const Status& s) {
    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "BatchCompleteFile failed," << request->ShortDebugString()
                << " " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kBatchCompleteFile,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "BatchCompleteFile success: "
                 << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kBatchCompleteFile,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }

    RETRY_CACHE_EXIT(s, response);
  });

  tos_ns_->AsyncBatchComplete(single_file_paths,
                              concat_file_srcs_paths,
                              concat_file_target_paths,
                              request,
                              response,
                              rpc_info,
                              dynamic_cast<RpcClosure*>(done_guard.release()),
                              c,
                              c->rpc_request_header()->accfsinfo());
}

void AccClientNamenodeService::batchDeleteFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::BatchDeleteFileRequestProto* request,
    ::cloudfs::BatchDeleteFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "batchDeleteFile", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string first_path;
  if (request->srcs_size()) {
    first_path = request->srcs().Get(0);
  }
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-batchDeleteFile]", "path[0]=" + first_path);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "batchDeleteFile Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  std::vector<std::string> paths;
  for (const auto& src : request->srcs()) {
    std::string normalized_path;
    if (!NormalizePath(src, user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path: " + src);
      return;
    }

    paths.push_back(normalized_path);
  }

  auto rpc_info = ServiceUtil::GetLogRpcInfo(c);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  RETRY_CACHE_ENTER(retry_cache_, c, response, done_guard);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache enter");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "BatchDeleteFile failed," << request->ShortDebugString()
                << " " << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kBatchDeleteFile,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "BatchDeleteFile success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kBatchDeleteFile,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RETRY_CACHE_EXIT(s, response);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "retry cache exit");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncBatchDelete(paths,
                            request,
                            response,
                            rpc_info,
                            dynamic_cast<RpcClosure*>(done_guard.release()),
                            c,
                            c->rpc_request_header()->accfsinfo());
}

void AccClientNamenodeService::batchGetFile(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::BatchGetFileRequestProto* request,
    ::cloudfs::BatchGetFileResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = dynamic_cast<RpcController*>(controller);
  auto wrapped_done = WrapClosure4Txid(c, done);
  auto rpc_done = dancenn::NewRpcCallback(
      this, "batchGetFile", controller, request, response, wrapped_done);
  dancenn::ClosureGuard done_guard(rpc_done);

  std::string first_path;
  if (request->srcs().size()) {
    first_path = request->srcs().Get(0);
  }
  auto rpc_sw_ctx =
      c->InitRpcSwCtx("[RPC-Acc-batchGetFile]", "path=" + first_path);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  if (FLAGS_ufs_read_only) {
    c->MarkAsFailed(JavaExceptions::AccessControlException(),
                    "Operation not allowed in read-only mode");
    return;
  }

  if (!RequestPreCheck(c)) {
    return;
  }

  DLOG(INFO) << "batchGetFile Request " << request->ShortDebugString();
  auto ugi = GetRemoteUserInfo(c);
  std::string user_name = ugi->GetRemoteUser();

  PermissionStatus permission = PermissionStatus();
  CreateDefaultPermForAcc(ugi, &permission);

  std::vector<std::string> paths;
  for (const auto& src : request->srcs()) {
    std::string normalized_path;
    if (!NormalizePath(src, user_name, &normalized_path)) {
      c->MarkAsFailed(JavaExceptions::IOException(), "Invalid path: " + src);
      return;
    }

    paths.push_back(normalized_path);
  }

  // Writer address is address of cfs client.
  auto client_address = ServiceUtil::GetClientAddress(c);
  auto client_location_tag = ServiceUtil::GetClientLocationTag(c);
  NetworkLocationInfo client_location(client_address, client_location_tag);
  client_location.rdma_tag = request->rdmatag();
  RPC_SW_CTX_LOG(rpc_sw_ctx, "precheck");

  rpc_done->add_post_callback([=](const Status& s) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback");

    if (s.HasException()) {
      if (s.exception() != JavaExceptions::kSafeModeException &&
          s.exception() != JavaExceptions::kStandbyException &&
          s.exception() != JavaExceptions::kReadOnlyCoolFileException) {
        VLOG(8) << "BatchGetFile failed," << request->ShortDebugString() << " "
                << s.ToString();
        c->MarkAsFailed(s);
        DANCENN_AUDIT_LOG2(AuditLog::kBatchGetFile,
                           ugi->GetRemoteUser(),
                           ugi->current_group(),
                           ServiceUtil::GetClientAddress(c).ToString(),
                           s,
                           request,
                           response);
      } else {
        c->MarkAsWeakFailed(s.exception(), s.message());
      }
    } else {
      DLOG(INFO) << "BatchGetFile success: " << response->ShortDebugString();
      DANCENN_AUDIT_LOG2(AuditLog::kBatchGetFile,
                         ugi->GetRemoteUser(),
                         ugi->current_group(),
                         ServiceUtil::GetClientAddress(c).ToString(),
                         s,
                         request,
                         response);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "postcheck");

    RPC_SW_CTX_LOG(rpc_sw_ctx, "post callback finish");
  });

  RPC_SW_CTX_LOG(rpc_sw_ctx, "after AccClientNamenodeService");
  tos_ns_->AsyncBatchGet(paths,
                         client_location,
                         request->needlocation(),
                         permission,
                         *ugi,
                         request,
                         response,
                         dynamic_cast<RpcClosure*>(done_guard.release()),
                         c,
                         c->rpc_request_header()->accfsinfo());
}

::google::protobuf::Closure* AccClientNamenodeService::WrapClosure4Txid(
    RpcController* controller, ::google::protobuf::Closure* done) {
  return new WrappedDoneClosure(
      controller, done, [this](RpcController* controller) {
        if (FLAGS_standby_read_set_seen_txid) {
          auto txid = this->tos_ns_->GetLastAppliedOrWrittenTxId();
          controller->set_seen_txid(txid);
        }
      });
}

static void InitMethodMetrics(
    std::shared_ptr<Metrics> metrics,
    const std::string& name,
    std::unordered_map<std::string, MethodMetric>* method_metrics) {
  MethodMetric metric;
  metric.num_ops_ = metrics->RegisterCounter("NumOps#method=" + name);
  metric.num_success_ops_ =
      metrics->RegisterCounter("Success.NumOps#method=" + name);
  metric.duration_ = metrics->RegisterHistogram("Time#method=" + name);
  metric.success_duration_ =
      metrics->RegisterHistogram("Success.Time#method=" + name);

  (*method_metrics)[name] = metric;
}

void AccClientNamenodeService::SetMethodMetas(std::shared_ptr<ServiceMeta> sm) {
  sm->AddMethodMeta("getListing",
                    MethodMetaBuilder()
                        .SetName("getListing")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDatanodeReport",
                    MethodMetaBuilder()
                        .SetName("getDatanodeReport")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDatanodeStorageReport",
                    MethodMetaBuilder()
                        .SetName("getDatanodeStorageReport")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getContentSummary",
                    MethodMetaBuilder()
                        .SetName("getContentSummary")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());

  sm->AddMethodMeta("recoverLease",
                    MethodMetaBuilder()
                        .SetName("recoverLease")
                        .SetType(MethodMeta::MethodType::kRecoverLease)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("create",
                    MethodMetaBuilder()
                        .SetName("create")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("append",
                    MethodMetaBuilder()
                        .SetName("append")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplication",
                    MethodMetaBuilder()
                        .SetName("setReplication")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplicationAttrOnly",
                    MethodMetaBuilder()
                        .SetName("setReplicationAttrOnly")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setStoragePolicy",
                    MethodMetaBuilder()
                        .SetName("setStoragePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setPermission",
                    MethodMetaBuilder()
                        .SetName("setPermission")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setOwner",
                    MethodMetaBuilder()
                        .SetName("setOwner")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("abandonBlock",
                    MethodMetaBuilder()
                        .SetName("abandonBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("addBlock",
                    MethodMetaBuilder()
                        .SetName("addBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("commitLastBlock",
                    MethodMetaBuilder()
                        .SetName("addBlock")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("complete",
                    MethodMetaBuilder()
                        .SetName("complete")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("concat",
                    MethodMetaBuilder()
                        .SetName("concat")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("rename",
                    MethodMetaBuilder()
                        .SetName("rename")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("rename2",
                    MethodMetaBuilder()
                        .SetName("rename2")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("Delete",
                    MethodMetaBuilder()
                        .SetName("Delete")
                        .SetType(MethodMeta::MethodType::kVeryVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("mkdirs",
                    MethodMetaBuilder()
                        .SetName("mkdirs")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("fsync",
                    MethodMetaBuilder()
                        .SetName("fsync")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setTimes",
                    MethodMetaBuilder()
                        .SetName("setTimes")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("updateBlockForPipeline",
                    MethodMetaBuilder()
                        .SetName("updateBlockForPipeline")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("updatePipeline",
                    MethodMetaBuilder()
                        .SetName("updatePipeline")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setXAttr",
                    MethodMetaBuilder()
                        .SetName("setXAttr")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("removeXAttr",
                    MethodMetaBuilder()
                        .SetName("removeXAttr")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("increaseAccessCounter",
                    MethodMetaBuilder()
                        .SetName("increaseAccessCounter")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReplicaPolicy",
                    MethodMetaBuilder()
                        .SetName("setReplicaPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setReadPolicy",
                    MethodMetaBuilder()
                        .SetName("setReadPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setDirPolicy",
                    MethodMetaBuilder()
                        .SetName("setDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("removeDirPolicy",
                    MethodMetaBuilder()
                        .SetName("removeDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getDirPolicy",
                    MethodMetaBuilder()
                        .SetName("getDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("listDirPolicy",
                    MethodMetaBuilder()
                        .SetName("listDirPolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("addCompleteBlocksAndCloseFile",
                    MethodMetaBuilder()
                        .SetName("addCompleteBlocksAndCloseFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("createSnapshot",
                    MethodMetaBuilder()
                        .SetName("createSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("renameSnapshot",
                    MethodMetaBuilder()
                        .SetName("renameSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("allowSnapshot",
                    MethodMetaBuilder()
                        .SetName("allowSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("disallowSnapshot",
                    MethodMetaBuilder()
                        .SetName("disallowSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("deleteSnapshot",
                    MethodMetaBuilder()
                        .SetName("deleteSnapshot")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("setLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("setLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("unsetLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("unsetLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("getLifecyclePolicy",
                    MethodMetaBuilder()
                        .SetName("getLifecyclePolicy")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("load",
                    MethodMetaBuilder()
                        .SetName("load")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("free",
                    MethodMetaBuilder()
                        .SetName("free")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("lookupJob",
                    MethodMetaBuilder()
                        .SetName("lookupJob")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("cancelJob",
                    MethodMetaBuilder()
                        .SetName("cancelJob")
                        .SetType(MethodMeta::MethodType::kVerySlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchCreateFile",
                    MethodMetaBuilder()
                        .SetName("batchCreateFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchCompleteFile",
                    MethodMetaBuilder()
                        .SetName("batchCompleteFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchDeleteFile",
                    MethodMetaBuilder()
                        .SetName("batchDeleteFile")
                        .SetType(MethodMeta::MethodType::kSlow)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
  sm->AddMethodMeta("batchGetFile",
                    MethodMetaBuilder()
                        .SetName("batchGetFile")
                        .SetType(MethodMeta::MethodType::kNormal)
                        .SetTimeoutMs(FLAGS_rpc_in_queue_timeout_ms_client_req)
                        .Build());
}

void AccClientNamenodeService::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("AccClientNameNodeService");
  InitMethodMetrics(metrics, "msync", &method_metrics_);
  InitMethodMetrics(metrics, "getHAServiceState", &method_metrics_);
  InitMethodMetrics(metrics, "getBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "getHyperBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "getServerDefaults", &method_metrics_);
  InitMethodMetrics(metrics, "create", &method_metrics_);
  InitMethodMetrics(metrics, "append", &method_metrics_);
  InitMethodMetrics(metrics, "setReplication", &method_metrics_);
  InitMethodMetrics(metrics, "setReplicationAttrOnly", &method_metrics_);
  InitMethodMetrics(metrics, "setStoragePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getStoragePolicies", &method_metrics_);
  InitMethodMetrics(metrics, "setPermission", &method_metrics_);
  InitMethodMetrics(metrics, "setOwner", &method_metrics_);
  InitMethodMetrics(metrics, "abandonBlock", &method_metrics_);
  InitMethodMetrics(metrics, "addBlock", &method_metrics_);
  InitMethodMetrics(metrics, "getAdditionalDatanode", &method_metrics_);
  InitMethodMetrics(metrics, "complete", &method_metrics_);
  InitMethodMetrics(metrics, "reportBadBlocks", &method_metrics_);
  InitMethodMetrics(metrics, "concat", &method_metrics_);
  InitMethodMetrics(metrics, "rename", &method_metrics_);
  InitMethodMetrics(metrics, "rename2", &method_metrics_);
  InitMethodMetrics(metrics, "Delete", &method_metrics_);
  InitMethodMetrics(metrics, "mkdirs", &method_metrics_);
  InitMethodMetrics(metrics, "getListing", &method_metrics_);
  InitMethodMetrics(metrics, "getListingWithBlockLocations", &method_metrics_);
  InitMethodMetrics(metrics, "renewLease", &method_metrics_);
  InitMethodMetrics(metrics, "recoverLease", &method_metrics_);
  InitMethodMetrics(metrics, "getFsStats", &method_metrics_);
  InitMethodMetrics(metrics, "getDatanodeReport", &method_metrics_);
  InitMethodMetrics(metrics, "getDatanodeStorageReport", &method_metrics_);
  InitMethodMetrics(metrics, "getPreferredBlockSize", &method_metrics_);
  InitMethodMetrics(metrics, "setSafeMode", &method_metrics_);
  InitMethodMetrics(metrics, "saveNamespace", &method_metrics_);
  InitMethodMetrics(metrics, "rollEdits", &method_metrics_);
  InitMethodMetrics(metrics, "restoreFailedStorage", &method_metrics_);
  InitMethodMetrics(metrics, "refreshNodes", &method_metrics_);
  InitMethodMetrics(metrics, "finalizeUpgrade", &method_metrics_);
  InitMethodMetrics(metrics, "rollingUpgrade", &method_metrics_);
  InitMethodMetrics(metrics, "listCorruptFileBlocks", &method_metrics_);
  InitMethodMetrics(metrics, "metaSave", &method_metrics_);
  InitMethodMetrics(metrics, "getFileInfo", &method_metrics_);
  InitMethodMetrics(metrics, "addCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "modifyCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "removeCacheDirective", &method_metrics_);
  InitMethodMetrics(metrics, "listCacheDirectives", &method_metrics_);
  InitMethodMetrics(metrics, "addCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "modifyCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "removeCachePool", &method_metrics_);
  InitMethodMetrics(metrics, "listCachePools", &method_metrics_);
  InitMethodMetrics(metrics, "getFileLinkInfo", &method_metrics_);
  InitMethodMetrics(metrics, "getContentSummary", &method_metrics_);
  InitMethodMetrics(metrics, "setQuota", &method_metrics_);
  InitMethodMetrics(metrics, "fsync", &method_metrics_);
  InitMethodMetrics(metrics, "setTimes", &method_metrics_);
  InitMethodMetrics(metrics, "createSymlink", &method_metrics_);
  InitMethodMetrics(metrics, "getLinkTarget", &method_metrics_);
  InitMethodMetrics(metrics, "updateBlockForPipeline", &method_metrics_);
  InitMethodMetrics(metrics, "updatePipeline", &method_metrics_);
  InitMethodMetrics(metrics, "getDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "renewDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "cancelDelegationToken", &method_metrics_);
  InitMethodMetrics(metrics, "setBalancerBandwidth", &method_metrics_);
  InitMethodMetrics(metrics, "getDataEncryptionKey", &method_metrics_);
  InitMethodMetrics(metrics, "createSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "renameSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "allowSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "disallowSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "getSnapshottableDirListing", &method_metrics_);
  InitMethodMetrics(metrics, "deleteSnapshot", &method_metrics_);
  InitMethodMetrics(metrics, "getSnapshotDiffReport", &method_metrics_);
  InitMethodMetrics(metrics, "isFileClosed", &method_metrics_);
  InitMethodMetrics(metrics, "modifyAclEntries", &method_metrics_);
  InitMethodMetrics(metrics, "removeAclEntries", &method_metrics_);
  InitMethodMetrics(metrics, "removeDefaultAcl", &method_metrics_);
  InitMethodMetrics(metrics, "removeAcl", &method_metrics_);
  InitMethodMetrics(metrics, "setAcl", &method_metrics_);
  InitMethodMetrics(metrics, "getAclStatus", &method_metrics_);
  InitMethodMetrics(metrics, "setXAttr", &method_metrics_);
  InitMethodMetrics(metrics, "getXAttrs", &method_metrics_);
  InitMethodMetrics(metrics, "listXAttrs", &method_metrics_);
  InitMethodMetrics(metrics, "removeXAttr", &method_metrics_);
  InitMethodMetrics(metrics, "checkAccess", &method_metrics_);
  InitMethodMetrics(metrics, "createEncryptionZone", &method_metrics_);
  InitMethodMetrics(metrics, "listEncryptionZones", &method_metrics_);
  InitMethodMetrics(metrics, "getEZForPath", &method_metrics_);
  InitMethodMetrics(metrics, "getCurrentEditLogTxid", &method_metrics_);
  InitMethodMetrics(metrics, "getEditsFromTxid", &method_metrics_);
  InitMethodMetrics(metrics, "increaseAccessCounter", &method_metrics_);
  InitMethodMetrics(metrics, "getAccessCounterValues", &method_metrics_);
  InitMethodMetrics(metrics, "setDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "removeDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "listDirPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "setReplicaPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getReplicaPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getDistributed", &method_metrics_);
  InitMethodMetrics(metrics, "setReadPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getReadPolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getBlockKeys", &method_metrics_);
  InitMethodMetrics(metrics, "listReadPolicies", &method_metrics_);
  InitMethodMetrics(metrics, "addCompleteBlocksAndCloseFile", &method_metrics_);
  InitMethodMetrics(metrics, "load", &method_metrics_);
  InitMethodMetrics(metrics, "free", &method_metrics_);
  InitMethodMetrics(metrics, "lookupJob", &method_metrics_);
  InitMethodMetrics(metrics, "cancelJob", &method_metrics_);
  InitMethodMetrics(metrics, "pin", &method_metrics_);
  InitMethodMetrics(metrics, "batchCreateFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchCompleteFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchDeleteFile", &method_metrics_);
  InitMethodMetrics(metrics, "batchGetFile", &method_metrics_);
  InitMethodMetrics(metrics, "setLifecyclePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "unsetLifecyclePolicy", &method_metrics_);
  InitMethodMetrics(metrics, "getLifecyclePolicy", &method_metrics_);

  mismatch_backend_count_metric_ = metrics->RegisterCounter("mismatchBackend");
  empty_backend_count_metric_ = metrics->RegisterCounter("emptyBackend");
  request_exceeds_ttl_count_metric_ =
      metrics->RegisterCounter("requestExceedsTTL");
}  // NOLINT(readability/fn_size)

bool AccClientNamenodeService::RequestPreCheck(RpcController* c,
                                               bool check_acc_fs_info) {
  // has acc fs info
  if (check_acc_fs_info && !c->rpc_request_header()->has_accfsinfo()) {
    c->MarkAsFailed(JavaExceptions::IOException(),
                    "Illegal request, accFsInfo is missing.");
    return false;
  }

  std::string backend_name;
  int64_t emit_time = -1;
  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "backend" && b.has_value()) {
      backend_name = b.value();
    }
    if (b.has_name() && b.name() == "emitTime" && b.has_value()) {
      try {
        emit_time = std::stol(b.value());
      } catch (std::exception e) {
        LOG(ERROR) << "emitTime value in baggage could not be parsed to long "
                      "type. value: "
                   << b.value() << ", exception: " << e.what()
                   << ", will not use it.";
      }
    }
  }
  // check backend name
  if (!backend_name.empty() && backend_name != FLAGS_nameservice) {
    MFC(mismatch_backend_count_metric_)->Inc();
    LOG(ERROR) << "mismatch target backend. nameservice: " << FLAGS_nameservice
               << ", target backend: " << backend_name << "; request header: "
               << c->request_header()->ShortDebugString()
               << "; body: " << c->request()->ShortDebugString();
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(),
                    "backend name not match. request: " + backend_name +
                        ", expected: " + FLAGS_nameservice);
    return false;
  }
  if (backend_name.empty()) {
    MFC(empty_backend_count_metric_)->Inc();
    VLOG(8) << "backend field is not set in baggage, this request may not "
               "comes from nnproxy.";
  }

  // check request TTL
  if (emit_time >= 0) {
    int64_t receive_time =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
    int64_t time_gap = receive_time - emit_time;
    if (time_gap > FLAGS_request_ttl_since_emit_ms) {
      MFC(request_exceeds_ttl_count_metric_)->Inc();
      LOG(ERROR) << "request exceeds ttl; time_gap:" << time_gap
                 << "; emit time: " << emit_time
                 << ", receive time: " << receive_time << "; request header: "
                 << c->request_header()->ShortDebugString()
                 << "; body: " << c->request()->ShortDebugString();
      c->MarkAsFailed(
          JavaExceptions::InvalidRequestException(),
          "request exceeds ttl, emit time: " + std::to_string(emit_time) +
              ", receive time: " + std::to_string(receive_time) +
              ", ttl: " + std::to_string(FLAGS_request_ttl_since_emit_ms));
      return false;
    }
  }

  // check client_id
  auto client_id = c->rpc_request_header()->clientid();
  if (client_id.size() != 16) {
    c->MarkAsFailed(JavaExceptions::InvalidRequestException(),
                    "client id size invalid. client_id=" + client_id);
    return false;
  }

  // check txid
  auto client_txid = c->client_txid();
  auto server_txid = tos_ns_->GetLastAppliedOrWrittenTxId();
  if (c->client_txid() > server_txid && !FLAGS_dfs_ha_allow_stale_reads) {
    std::ostringstream os;
    os << "client_txid(" << client_txid << ") > server_txid(" << server_txid
       << "), stale read.";
    auto msg = os.str();
    LOG(ERROR) << msg;

    c->MarkAsWeakFailed(JavaExceptions::Exception::kStandbyException,
                        "StandbyException" + msg);
    return false;
  }

  return true;
}

}  // namespace dancenn
