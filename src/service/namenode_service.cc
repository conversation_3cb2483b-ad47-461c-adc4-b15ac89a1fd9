// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#include <service/namenode_service.h>

#include <glog/logging.h>

#include "rpc/rpc_controller.h"
#include "rpc/rpc_server_connection.h"
#include "base/java_exceptions.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "namespace/namespace.h"
#include "ha/ha_state_base.h"
#include "ha/ha_state.h"

namespace dancenn {

using MR = MethodRecorder<NameNodeService>;

NameNodeService::NameNodeService(
    std::shared_ptr<DatanodeManager> datanode_manager,
    std::shared_ptr<BlockManager> block_manager,
    std::shared_ptr<NameSpace> name_space,
    HAStateBase* ha_state)
    : datanode_manager_(datanode_manager),
      block_manager_(block_manager),
      namespace_(name_space),
      ha_state_(ha_state) {
  InitMetrics();
}

void NameNodeService::getBlocks(::google::protobuf::RpcController* controller,
               const ::cloudfs::namenode::GetBlocksRequestProto* request,
               ::cloudfs::namenode::GetBlocksResponseProto* response,
               ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "getBlocks", c, done);
  auto dn = datanode_manager_->GetDatanodeFromUuid(request->datanode()
      .datanodeuuid());
  if (dn == nullptr) {
    c->MarkAsFailed(JavaExceptions::HadoopIllegalArgumentException(),
                    "Datanode " + request->datanode().datanodeuuid()
                        + " not found.");
    LOG(WARNING) << "GetBlocks, Datanode "
                 << request->datanode().datanodeuuid() << " not found.";
    return;
  }
  auto s = block_manager_->GetBlocksWithLocations(
      dn, request->size(), response->mutable_blocks());
  if (s.HasException()) {
    c->MarkAsFailed(s);
  }
}

void NameNodeService::versionRequest(
    ::google::protobuf::RpcController* controller,
    const ::cloudfs::VersionRequestProto* request,
    ::cloudfs::VersionResponseProto* response,
    ::google::protobuf::Closure* done) {
  auto c = static_cast<RpcController*>(controller);
  MR recorder(this, "versionRequest", c, done);
  namespace_->GetNamespaceInfo(response->mutable_info());
  DLOG(INFO) << "version request response " << response->ShortDebugString();
}

void NameNodeService::isUpgradeFinalized(
    ::google::protobuf::RpcController *controller,
    const ::cloudfs::namenode::IsUpgradeFinalizedRequestProto *request,
    ::cloudfs::namenode::IsUpgradeFinalizedResponseProto *response,
    ::google::protobuf::Closure *done) {
  auto c = static_cast<RpcController *>(controller);
  MR recorder(this, "isUpgradeFinalized", c, done);
  response->set_isupgradefinalized(true);
  DLOG(INFO) << "isUpgradeFinalized response: true";
}

void NameNodeService::SetMethodMetas(std::shared_ptr<ServiceMeta> sm) {
  sm->AddMethodMeta(
      "getBlocks",
      MethodMetaBuilder()
          .SetName("getBlocks")
          .SetType(MethodMeta::MethodType::kVerySlow)
          .Build());
}

static void InitMethodMetrics(
    std::shared_ptr<Metrics> metrics,
    const std::string& name,
    std::unordered_map<std::string, MethodMetric>* method_metrics) {

  MethodMetric metric;
  metric.num_ops_ = metrics->RegisterCounter("NumOps#method=" + name);
  metric.num_success_ops_ = metrics->RegisterCounter(
      "Success.NumOps#method=" + name);
  metric.duration_ = metrics->RegisterHistogram("Time#method=" + name);
  metric.success_duration_ = metrics->RegisterHistogram(
      "Success.Time#method=" + name);

  (*method_metrics)[name] = metric;
}

void NameNodeService::InitMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("NameNodeService");
  InitMethodMetrics(metrics,
                    "getBlocks",
                    &method_metrics_);
  InitMethodMetrics(metrics,
                    "versionRequest",
                    &method_metrics_);
  InitMethodMetrics(metrics, "isUpgradeFinalized", &method_metrics_);
}

} // namespace dancenn
