//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//
// ningw <EMAIL>>
//

#pragma once

#include <ClientNamenodeProtocol.pb.h>

#include <memory>
#include <map>
#include <set>
#include <string>
#include <unordered_map>

#include "datanode_manager/datanode_manager.h"
#include "namespace/namespace.h"
#include "acc/acc_namespace.h"
#include "service/method_recorder.h"
#include "service/service_meta.h"
#include "base/metric.h"
#include "base/retry_cache.h"
#include "block_manager/block_manager.h"
#include "safemode/safemode.h"
#include "ha/ha_state.h"
#include "rpc/rpc_controller.h"
#include "security/key_manager.h"

namespace dancenn {

class DatanodeManager;
class BlockManager;
class KeyManager;

class AccClientNamenodeService : public cloudfs::ClientNamenodeProtocol {
 public:
  explicit AccClientNamenodeService(
      std::shared_ptr<DatanodeManager> datanode_manager,
      std::shared_ptr<BlockManager> block_manager,
      std::shared_ptr<AccNamespace> tos_ns,
      SafeModeBase* safemode,
      RetryCache* rc);

  ~AccClientNamenodeService() override = default;

  AccClientNamenodeService(const AccClientNamenodeService&) = delete;
  AccClientNamenodeService& operator=(const AccClientNamenodeService&) = delete;

   // Support
  void getBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockLocationsRequestProto* request,
      ::cloudfs::GetBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 透传
  void getServerDefaults(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetServerDefaultsRequestProto* request,
      ::cloudfs::GetServerDefaultsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 同步 File 信息，创建 TOS Object 占位，创建 Upload ID，设置 Tag
  void create(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateRequestProto* request,
      ::cloudfs::CreateResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 同步 File 信息，检查 TOS Object Tag
  void append(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AppendRequestProto* request,
      ::cloudfs::AppendResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void setReplication(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void setReplicationAttrOnly(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicationRequestProto* request,
      ::cloudfs::SetReplicationResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void setStoragePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetStoragePolicyRequestProto* request,
      ::cloudfs::SetStoragePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void getStoragePolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetStoragePoliciesRequestProto* request,
      ::cloudfs::GetStoragePoliciesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void setPermission(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetPermissionRequestProto* request,
      ::cloudfs::SetPermissionResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NO
  void setOwner(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetOwnerRequestProto* request,
      ::cloudfs::SetOwnerResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
  void abandonBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AbandonBlockRequestProto* request,
      ::cloudfs::AbandonBlockResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
  void addBlock(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddBlockRequestProto* request,
      ::cloudfs::AddBlockResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  // - Complete 处理文件 Close 场景
  void complete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CompleteRequestProto* request,
      ::cloudfs::CompleteResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 保持原有实现
  void getAdditionalDatanode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAdditionalDatanodeRequestProto* request,
      ::cloudfs::GetAdditionalDatanodeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 对于 KPersisted 文件，不需要
  void reportBadBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ReportBadBlocksRequestProto* request,
      ::cloudfs::ReportBadBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 目前未实现 NameSpace::Concat
  void concat(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ConcatRequestProto* request,
      ::cloudfs::ConcatResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
  void rename(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameRequestProto* request,
      ::cloudfs::RenameResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
  void rename2(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::Rename2RequestProto* request,
      ::cloudfs::Rename2ResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 同步File或者目录元数据
  // - 操作透传到 TOS，同步等待
  // - 检查需要 copy + delete 的数据量大小，超过限制报错不支持
  void Delete(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteRequestProto* request,
      ::cloudfs::DeleteResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // Local 操作，不做改动. 会在 tos 创建一个 d/ 的目录(同 tos 网页).
  void mkdirs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MkdirsRequestProto* request,
      ::cloudfs::MkdirsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 同步File或者目录元数据；支持 Paging 和异步化 Sync
  void getListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetListingRequestProto* request,
      ::cloudfs::GetListingResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  void renewLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewLeaseRequestProto* request,
      ::cloudfs::RenewLeaseResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  void recoverLease(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RecoverLeaseRequestProto* request,
      ::cloudfs::RecoverLeaseResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void getFsStats(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFsStatusRequestProto* request,
      ::cloudfs::GetFsStatsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void getDatanodeReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeReportRequestProto* request,
      ::cloudfs::GetDatanodeReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void getDatanodeStorageReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDatanodeStorageReportRequestProto* request,
      ::cloudfs::GetDatanodeStorageReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void getPreferredBlockSize(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetPreferredBlockSizeRequestProto* request,
      ::cloudfs::GetPreferredBlockSizeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void setSafeMode(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetSafeModeRequestProto* request,
      ::cloudfs::SetSafeModeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void saveNamespace(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SaveNamespaceRequestProto* request,
      ::cloudfs::SaveNamespaceResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void rollEdits(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollEditsRequestProto* request,
      ::cloudfs::RollEditsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void restoreFailedStorage(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RestoreFailedStorageRequestProto* request,
      ::cloudfs::RestoreFailedStorageResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void refreshNodes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RefreshNodesRequestProto* request,
      ::cloudfs::RefreshNodesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void finalizeUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FinalizeUpgradeRequestProto* request,
      ::cloudfs::FinalizeUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void rollingUpgrade(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RollingUpgradeRequestProto* request,
      ::cloudfs::RollingUpgradeResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void listCorruptFileBlocks(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCorruptFileBlocksRequestProto* request,
      ::cloudfs::ListCorruptFileBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void metaSave(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MetaSaveRequestProto* request,
      ::cloudfs::MetaSaveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 同 getBlockLocations
  void getFileInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileInfoRequestProto* request,
      ::cloudfs::GetFileInfoResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void addCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCacheDirectiveRequestProto* request,
      ::cloudfs::AddCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void modifyCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCacheDirectiveRequestProto* request,
      ::cloudfs::ModifyCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void removeCacheDirective(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCacheDirectiveRequestProto* request,
      ::cloudfs::RemoveCacheDirectiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void listCacheDirectives(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCacheDirectivesRequestProto* request,
      ::cloudfs::ListCacheDirectivesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void addCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCachePoolRequestProto* request,
      ::cloudfs::AddCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void modifyCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyCachePoolRequestProto* request,
      ::cloudfs::ModifyCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void removeCachePool(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveCachePoolRequestProto* request,
      ::cloudfs::RemoveCachePoolResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void listCachePools(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListCachePoolsRequestProto* request,
      ::cloudfs::ListCachePoolsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getFileLinkInfo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetFileLinkInfoRequestProto* request,
      ::cloudfs::GetFileLinkInfoResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 递归同步元数据，超过限制报错不支持，主要是 du 命令在用
  void getContentSummary(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetContentSummaryRequestProto* request,
      ::cloudfs::GetContentSummaryResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void setQuota(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetQuotaRequestProto* request,
      ::cloudfs::SetQuotaResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 检查文件是否是正在写入的文件，kToBePersisted
  void fsync(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::FsyncRequestProto* request,
      ::cloudfs::FsyncResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，没有意义；可以设到 Object 的 Meta 里
  void setTimes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetTimesRequestProto* request,
      ::cloudfs::SetTimesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void createSymlink(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSymlinkRequestProto* request,
      ::cloudfs::CreateSymlinkResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getLinkTarget(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLinkTargetRequestProto* request,
      ::cloudfs::GetLinkTargetResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  void updateBlockForPipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdateBlockForPipelineRequestProto* request,
      ::cloudfs::UpdateBlockForPipelineResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  void updatePipeline(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UpdatePipelineRequestProto* request,
      ::cloudfs::UpdatePipelineResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDelegationTokenRequestProto* request,
      ::cloudfs::GetDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void renewDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenewDelegationTokenRequestProto* request,
      ::cloudfs::RenewDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void cancelDelegationToken(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CancelDelegationTokenRequestProto* request,
      ::cloudfs::CancelDelegationTokenResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void setBalancerBandwidth(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetBalancerBandwidthRequestProto* request,
      ::cloudfs::SetBalancerBandwidthResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getDataEncryptionKey(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDataEncryptionKeyRequestProto* request,
      ::cloudfs::GetDataEncryptionKeyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void createSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateSnapshotRequestProto* request,
      ::cloudfs::CreateSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void renameSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RenameSnapshotRequestProto* request,
      ::cloudfs::RenameSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void allowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AllowSnapshotRequestProto* request,
      ::cloudfs::AllowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void disallowSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DisallowSnapshotRequestProto* request,
      ::cloudfs::DisallowSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getSnapshottableDirListing(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshottableDirListingRequestProto* request,
      ::cloudfs::GetSnapshottableDirListingResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void deleteSnapshot(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::DeleteSnapshotRequestProto* request,
      ::cloudfs::DeleteSnapshotResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getSnapshotDiffReport(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetSnapshotDiffReportRequestProto* request,
      ::cloudfs::GetSnapshotDiffReportResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 检查文件是否是正在写入的文件，kToBePersisted
  void isFileClosed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IsFileClosedRequestProto* request,
      ::cloudfs::IsFileClosedResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void modifyAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ModifyAclEntriesRequestProto* request,
      ::cloudfs::ModifyAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void removeAclEntries(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclEntriesRequestProto* request,
      ::cloudfs::RemoveAclEntriesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void removeDefaultAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveDefaultAclRequestProto* request,
      ::cloudfs::RemoveDefaultAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void removeAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveAclRequestProto* request,
      ::cloudfs::RemoveAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void setAcl(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetAclRequestProto* request,
      ::cloudfs::SetAclResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 现在未实现
  void getAclStatus(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAclStatusRequestProto* request,
      ::cloudfs::GetAclStatusResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
  void setXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetXAttrRequestProto* request,
      ::cloudfs::SetXAttrResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
  void getXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetXAttrsRequestProto* request,
      ::cloudfs::GetXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
  void listXAttrs(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListXAttrsRequestProto* request,
      ::cloudfs::ListXAttrsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义；可以设到 Object 的 Meta 里
  void removeXAttr(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::RemoveXAttrRequestProto* request,
      ::cloudfs::RemoveXAttrResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void checkAccess(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CheckAccessRequestProto* request,
      ::cloudfs::CheckAccessResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void createEncryptionZone(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CreateEncryptionZoneRequestProto* request,
      ::cloudfs::CreateEncryptionZoneResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void listEncryptionZones(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListEncryptionZonesRequestProto* request,
      ::cloudfs::ListEncryptionZonesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void getEZForPath(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEZForPathRequestProto* request,
      ::cloudfs::GetEZForPathResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void getCurrentEditLogTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetCurrentEditLogTxidRequestProto* request,
      ::cloudfs::GetCurrentEditLogTxidResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void getEditsFromTxid(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetEditsFromTxidRequestProto* request,
      ::cloudfs::GetEditsFromTxidResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void increaseAccessCounter(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::IncreaseAccessCounterRequestProto* request,
      ::cloudfs::IncreaseAccessCounterResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // NN 未实现
  void getAccessCounterValues(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetAccessCounterValuesRequestProto* request,
      ::cloudfs::GetAccessCounterValuesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setDirPolicy(::google::protobuf::RpcController* controller,
                    const ::cloudfs::SetDirPolicyRequestProto* request,
                    ::cloudfs::SetDirPolicyResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void removeDirPolicy(::google::protobuf::RpcController* controller,
                       const ::cloudfs::RemoveDirPolicyRequestProto* request,
                       ::cloudfs::RemoveDirPolicyResponseProto* response,
                       ::google::protobuf::Closure* done) override;

  void getDirPolicy(::google::protobuf::RpcController* controller,
                    const ::cloudfs::GetDirPolicyRequestProto* request,
                    ::cloudfs::GetDirPolicyResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void listDirPolicy(::google::protobuf::RpcController* controller,
                     const ::cloudfs::ListDirPolicyRequestProto* request,
                     ::cloudfs::ListDirPolicyResponseProto* response,
                     ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void setReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReplicaPolicyRequestProto* request,
      ::cloudfs::SetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void getReplicaPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetReplicaPolicyRequestProto* request,
      ::cloudfs::GetReplicaPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void getDistributed(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetDistributedRequestProto* request,
      ::cloudfs::GetDistributedResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void setReadPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetReadPolicyRequestProto* request,
      ::cloudfs::SetReadPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void getReadPolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetReadPolicyRequestProto* request,
      ::cloudfs::GetReadPolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void listReadPolicies(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::ListReadPoliciesRequestProto* request,
      ::cloudfs::ListReadPoliciesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 先改成不支持，透明加速场景没有意义
  void addCompleteBlocksAndCloseFile(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::AddCompleteBlocksAndCloseFileRequestProto* request,
      ::cloudfs::AddCompleteBlocksAndCloseFileResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 维持现有实现
  // - 这个接口是 proxy 在用，不应该加到 ClientNamenodeProtocol 里面，Client
  // - 端并不需要
  void getBlockKeys(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetBlockKeysRequestProto* request,
      ::cloudfs::GetBlockKeysResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // - 检查文件是否是正在写入的文件，kToBePersisted
  void msync(::google::protobuf::RpcController* controller,
             const ::cloudfs::MsyncRequestProto* request,
             ::cloudfs::MsyncResponseProto* response,
             ::google::protobuf::Closure* done) override;

  // - 不需要，禁用
  void getHyperBlockLocations(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetHyperBlockLocationsRequestProto* request,
      ::cloudfs::GetHyperBlockLocationsResponseProto* response,
      ::google::protobuf::Closure* done) override;

  // 维持现有实现
  void getHAServiceState(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::HAServiceStateRequestProto* request,
      ::cloudfs::HAServiceStateResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void setLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::SetLifecyclePolicyRequestProto* request,
      ::cloudfs::SetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void unsetLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::UnsetLifecyclePolicyRequestProto* request,
      ::cloudfs::UnsetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getLifecyclePolicy(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetLifecyclePolicyRequestProto* request,
      ::cloudfs::GetLifecyclePolicyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void load(::google::protobuf::RpcController* controller,
                const ::cloudfs::LoadRequestProto* request,
                ::cloudfs::LoadResponseProto* response,
                ::google::protobuf::Closure* done) override;

  void free(::google::protobuf::RpcController* controller,
            const ::cloudfs::FreeRequestProto* request,
            ::cloudfs::FreeResponseProto* response,
            ::google::protobuf::Closure* done) override;

  void pin(::google::protobuf::RpcController* controller,
           const ::cloudfs::PinRequestProto* request,
           ::cloudfs::PinResponseProto* response,
           ::google::protobuf::Closure* done) override;

  void lookupJob(::google::protobuf::RpcController* controller,
                 const ::cloudfs::LookupJobRequestProto* request,
                 ::cloudfs::LookupJobResponseProto* response,
                 ::google::protobuf::Closure* done) override;

  void cancelJob(::google::protobuf::RpcController* controller,
                 const ::cloudfs::CancelJobRequestProto* request,
                 ::cloudfs::CancelJobResponseProto* response,
                 ::google::protobuf::Closure* done) override;

  void batchCreateFile(::google::protobuf::RpcController* controller,
                       const ::cloudfs::BatchCreateFileRequestProto* request,
                       ::cloudfs::BatchCreateFileResponseProto* response,
                       ::google::protobuf::Closure* done) override;
  void batchCompleteFile(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::BatchCompleteFileRequestProto* request,
      ::cloudfs::BatchCompleteFileResponseProto* response,
      ::google::protobuf::Closure* done) override;
  void batchDeleteFile(::google::protobuf::RpcController* controller,
                       const ::cloudfs::BatchDeleteFileRequestProto* request,
                       ::cloudfs::BatchDeleteFileResponseProto* response,
                       ::google::protobuf::Closure* done) override;
  void batchGetFile(::google::protobuf::RpcController* controller,
                    const ::cloudfs::BatchGetFileRequestProto* request,
                    ::cloudfs::BatchGetFileResponseProto* response,
                    ::google::protobuf::Closure* done) override;

  void SetMethodMetas(std::shared_ptr<ServiceMeta> sm);

 private:
  ::google::protobuf::Closure* WrapClosure4Txid(
      RpcController* controller, ::google::protobuf::Closure* done);

  void InitMetrics();

  friend class MethodRecorder<AccClientNamenodeService>;
  std::unordered_map<std::string, MethodMetric> method_metrics_;
  MetricID mismatch_backend_count_metric_;
  MetricID empty_backend_count_metric_;
  MetricID request_exceeds_ttl_count_metric_;

  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<AccNamespace> tos_ns_;
  SafeModeBase* safemode_{nullptr};

  RetryCache* retry_cache_{nullptr};

  bool RequestPreCheck(RpcController* c, bool check_acc_fs_info = true);
};

}  // namespace dancenn
