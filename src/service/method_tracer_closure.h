// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_METHOD_TRACER_CLOSURE_H_
#define SERVICE_METHOD_TRACER_CLOSURE_H_

#include <glog/logging.h>
#include <google/protobuf/service.h>
#include <cnetpp/base/csonpp.h>

#include <chrono>
#include <memory>
#include <utility>
#include <string>

#include "btrace.pb.h"  // NOLINT(build/include)
#include "base/databus.h"
#include "service/method_tracer.h"

namespace dancenn {

class MethodTracerClosure : public google::protobuf::Closure {
 public:
  MethodTracerClosure(google::protobuf::RpcController* controller,
                      google::protobuf::Closure* orig,
                      MethodTracer&& tracer)
      : controller_(controller),
        orig_(orig),
        tracer_(std::move(tracer)) {
    CHECK_NOTNULL(controller_);
    CHECK_NOTNULL(orig_);
  }

  MethodTracerClosure(std::shared_ptr<DatabusChannel> btrace_databus_channel,
                      const btrace::ByteSystemId& byte_system_id,
                      btrace::OperationType operation_type,
                      const std::string& btid,
                      const std::string& path,
                      const char* method_name,  // must be static const c string
                      google::protobuf::RpcController* controller,
                      google::protobuf::Closure* orig,
                      MethodTracer&& tracer)
      : controller_(controller),
        orig_(orig),
        tracer_(std::move(tracer)),
        btrace_databus_channel_(btrace_databus_channel),
        byte_system_id_(&byte_system_id),
        operation_type_(operation_type),
        path_(path),
        btid_(btid),
        method_name_(method_name) {
    CHECK_NOTNULL(controller_);
    CHECK_NOTNULL(orig_);
  }

  void Run() override {
    bool failed = controller_->Failed();
    orig_->Run();  // the controller_ will be destructed
    MFC(tracer_.method_metrics()->num_success_ops_)->Inc();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - tracer_.start_time()).count();
    MFH(tracer_.method_metrics()->success_time_)->Update(duration);
    MFH(tracer_.method_metrics()->overall_success_time_)->Update(duration);
    EmitByteTraceLog(duration, failed);
    delete this;
  }

  google::protobuf::Closure* orig() {
    return orig_;
  }

  cnetpp::base::Object& get_extra() {
    return extra_;
  }

 private:
  void EmitByteTraceLog(int64_t duration, bool failed) {
    if (!byte_system_id_) {
      return;
    }

    btrace::ByteTraceLog log;
    log.set_local_timestamp_ms(
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
    log.set_operation_cost_ms(duration);
    log.set_operation_type(operation_type_);
    log.mutable_sysid()->CopyFrom(*byte_system_id_);
    log.mutable_btid()->ParseFromString(btid_);
    log.set_resource_path(path_);
    extra_["method"] = cnetpp::base::Value(method_name_);
    extra_["result"] = cnetpp::base::Value(failed ? "fail" : "success");
    // We are sure the extra_ is not used any more,
    // so move it to optimize performance
    log.set_extra(cnetpp::base::Parser::Serialize(
        cnetpp::base::Value(std::move(extra_))));

    if (!btrace_databus_channel_->Emit(log.SerializeAsString())) {
      DLOG(ERROR) << "Failed to emit bytetrace log: "
                  << cnetpp::concurrency::ThisThread::GetLastErrorString();
    }
  }

  google::protobuf::RpcController* controller_{nullptr};
  google::protobuf::Closure* orig_{nullptr};
  MethodTracer                     tracer_;
  std::shared_ptr<DatabusChannel>  btrace_databus_channel_;
  const btrace::ByteSystemId*      byte_system_id_ { nullptr };
  btrace::OperationType operation_type_;
  std::string           path_;
  std::string           btid_;
  std::string           method_name_;
  cnetpp::base::Object  extra_;
};

}  // namespace dancenn

#endif  // SERVICE_METHOD_TRACER_CLOSURE_H_

