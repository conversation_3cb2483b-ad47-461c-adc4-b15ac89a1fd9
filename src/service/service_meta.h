// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_SERVICE_META_H_
#define SERVICE_SERVICE_META_H_

#include <google/protobuf/service.h>

#include <memory>
#include <unordered_map>

#include "base/java_exceptions.h"

namespace dancenn {

class MethodMeta {
 public:
  enum class MethodType {
    kNormal = 0,
    kSlow = 1,
    kVerySlow = 2,
    kVeryVerySlow = 3,
    kRecoverLease = 4,
    kLast = 5,
  };

  MethodMeta() = default;
  ~MethodMeta() = default;

  MethodMeta(MethodMeta&& other) = default;
  MethodMeta(const MethodMeta& other) = default;

  MethodMeta& operator=(const MethodMeta& other) = default;
  MethodMeta& operator=(MethodMeta&& other) = default;

  MethodType type() const {
    return type_;
  }

  int timeout_ms() const {
    return timeout_ms_;
  }
  void set_timeout_ms(int timeout_ms) {
    timeout_ms_ = timeout_ms;
  }

  JavaExceptions::Exception exception_when_timedout() const {
    return exception_when_timedout_;
  }

 private:
  friend class MethodMetaBuilder;

  MethodMeta(const std::string& name,
             MethodType type,
             int timeout_ms,
             JavaExceptions::Exception exception_when_timedout)
      : name_(name),
        type_(type),
        timeout_ms_(timeout_ms),
        exception_when_timedout_(exception_when_timedout) {}

  std::string name_;
  MethodMeta::MethodType type_{MethodMeta::MethodType::kNormal};
  int timeout_ms_{0};
  JavaExceptions::Exception exception_when_timedout_{
    JavaExceptions::Exception::kStandbyException
  };
};

class MethodMetaBuilder {
 public:
  MethodMetaBuilder() = default;
  ~MethodMetaBuilder() = default;

  MethodMetaBuilder& SetType(MethodMeta::MethodType type) {
    type_ = type;
    return *this;
  }

  MethodMetaBuilder& SetName(std::string name) {
    name_ = std::move(name);
    return *this;
  }

  MethodMetaBuilder& SetTimeoutMs(int timeout_ms) {
    timeout_ms_ = timeout_ms;
    return *this;
  }

  MethodMetaBuilder& SetExceptionWhenTimedout(JavaExceptions::Exception e) {
    exception_when_timedout_ = e;
    return *this;
  }

  MethodMeta Build() const {
    return MethodMeta(name_, type_, timeout_ms_, exception_when_timedout_);
  }

 private:
  std::string name_;
  MethodMeta::MethodType type_{MethodMeta::MethodType::kNormal};
  int timeout_ms_{0};
  JavaExceptions::Exception exception_when_timedout_{
    JavaExceptions::Exception::kStandbyException
  };
};

class ServiceMeta {
 public:
  explicit ServiceMeta(std::shared_ptr<google::protobuf::Service> service,
                       int timeout_ms)
      : timeout_ms_(timeout_ms),
        service_(service) {
    assert(service_.get());
  }

  virtual ~ServiceMeta() {}

  ServiceMeta(const ServiceMeta&) = delete;
  ServiceMeta& operator=(const ServiceMeta&) = delete;

  int timeout_ms() const {
    return timeout_ms_;
  }

  std::shared_ptr<google::protobuf::Service> service() const {
    return service_;
  }

  void AddMethodMeta(const std::string& method, const MethodMeta& meta) {
    methods_[method] = meta;
  }

  MethodMeta GetMethodMeta(const std::string& method) const {
    auto itr = methods_.find(method);
    if (itr != methods_.end()) {
      return itr->second;
    }
    return {};
  }

 private:
  int timeout_ms_;
  std::shared_ptr<google::protobuf::Service> service_;
  std::unordered_map<std::string, MethodMeta> methods_;
};

}  // namespace dancenn

#endif  // SERVICE_SERVICE_META_H_
