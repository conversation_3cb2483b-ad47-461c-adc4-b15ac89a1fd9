//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/base/ip_address.h>
#include <hdfs.pb.h>

#include "edit/sender_base.h"
#include "rpc/rpc_controller.h"

namespace dancenn {

class RpcController;

class ServiceUtil {
 public:
  static cnetpp::base::IPAddress GetClientAddress(RpcController* c);

  static cloudfs::LocationTag GetClientLocationTag(RpcController* c);

  static LogRpcInfo GetLogRpcInfo(RpcController* c);
};

}  // namespace dancenn
