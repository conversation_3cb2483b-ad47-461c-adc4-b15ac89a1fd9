//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "service_util.h"

#include <absl/strings/str_split.h>
#include <glog/logging.h>
#include <gflags/gflags.h>

#include "../base/string_utils.h"

DECLARE_bool(run_ut);

namespace dancenn
{

cnetpp::base::IPAddress ServiceUtil::GetClientAddress(RpcController* c) {
  // Get client ip from rpc request header
  cnetpp::base::IPAddress writer_address;
  if (!c->rpc_request_header()->has_clientaddress() ||
      !cnetpp::base::IPAddress::LiteralToNumber(
          c->rpc_request_header()->clientaddress(), &writer_address)) {
    writer_address =
        c->rpc_connection()->tcp_connection()->remote_end_point().address();
  }
  // handle IPv6
  if (writer_address.IsIPv4Mapped()) {
    // ad-hoc parse ipv4 mapped ipv6
    cnetpp::base::IPAddress real_ip;
    writer_address.GetIPv4FromIPv6(&real_ip);
    return real_ip;
  } else {
    return writer_address;
  }
}

cloudfs::LocationTag ServiceUtil::GetClientLocationTag(RpcController* c) {
  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "location_tag" && b.has_value()) {
      std::string location_tag_str = b.value();
      std::vector<std::string> v = absl::StrSplit(location_tag_str, '/');
      if (v.size() != 4 && v.size() != 5) {
        LOG(ERROR) << "baggage have location_tag, but format mismatch";
        return cloudfs::LocationTag();
      }

      cloudfs::LocationTag tag;
      tag.set_az(v[1]);
      tag.set_switch_(v[2]);
      tag.set_host(v[3]);
      return tag;
    }
  }
  return cloudfs::LocationTag();
}

LogRpcInfo ServiceUtil::GetLogRpcInfo(RpcController* c) {
  auto original_client_id = c->rpc_request_header()->clientid();
  auto original_call_id =
      static_cast<uint32_t>(c->rpc_request_header()->callid());
  std::string baggage_client_id_raw;
  std::string baggage_client_id;
  uint32_t baggage_call_id = 0;
  auto client_id = original_client_id;
  auto call_id = original_call_id;
  CHECK_EQ(original_client_id.size(), 16);

  bool flag_log = false;

  for (const auto& b : c->rpc_request_header()->baggages()) {
    if (b.has_name() && b.name() == "clientId" && b.has_value()) {
      baggage_client_id = b.value();
      if (!StringUtils::IsHexString(baggage_client_id)) {
        LOG(ERROR) << "baggage have clientId, but not HexString";
        flag_log = true;
        continue;
      }
      baggage_client_id_raw = StringUtils::HexStringTo(baggage_client_id);
      if (baggage_client_id_raw.size() != 16) {
        LOG(ERROR) << "baggage have HexString clientId, but length error";
        flag_log = true;
        continue;
      }
      client_id = baggage_client_id_raw;
    }
    if (b.has_name() && b.name() == "callId" && b.has_value()) {
      try {
        baggage_call_id = stoi(b.value());
        call_id = baggage_call_id;
      } catch (...) {
        LOG(ERROR) << "callId in baggages is not a number. callId="
                   << b.value();
        flag_log = true;
      }
    }
  }
  if (!FLAGS_run_ut && (VLOG_IS_ON(10) || flag_log)) {
    auto proxy_address = c->rpc_connection()
                             ->tcp_connection()
                             ->remote_end_point()
                             .address()
                             .ToString();
    LOG(INFO) << "GetLogRpcInfo "
              << " | "
              << " proxy_address=" << proxy_address
              << " client_address=" << GetClientAddress(c).ToString() << " | "
              << " original_client_id(" << original_client_id.size()
              << ")=" << StringUtils::ToHexString(original_client_id)
              << " original_call_id=" << original_call_id << " | "
              << " baggage_client_id(" << baggage_client_id.size()
              << ")=" << baggage_client_id << " baggage_client_id_raw("
              << baggage_client_id_raw.size()
              << ")=" << StringUtils::ToHexString(baggage_client_id_raw)
              << " baggage_call_id=" << baggage_call_id << " | "
              << " client_id(" << client_id.size()
              << ")=" << StringUtils::ToHexString(client_id)
              << " call_id=" << call_id << " | "
              << " header=" << c->rpc_request_header()->ShortDebugString()
              << " body=" << c->request()->ShortDebugString();
  }
  CHECK_EQ(client_id.size(), 16);
  return LogRpcInfo(std::move(client_id), call_id);
}

} // namespace dancenn
