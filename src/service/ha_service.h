// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_HA_SERVICE_H_
#define SERVICE_HA_SERVICE_H_

#include <memory>

#include "HAServiceProtocol.pb.h"  // NOLINT(build/include)
#include "ha/ha_state.h"
#include "safemode/safemode.h"
#include "service/method_recorder.h"

namespace dancenn {

class HAService : public cloudfs::HAServiceProtocolService {
 public:
  HAService(HAStateBase* ha_state, SafeModeBase* safemode);
  virtual ~HAService();

  HAService(const HAService&) = delete;
  HAService& operator=(const HAService&) = delete;

  void monitorHealth(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::MonitorHealthRequestProto* request,
      ::cloudfs::MonitorHealthResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void transitionToActive(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::TransitionToActiveRequestProto* request,
      ::cloudfs::TransitionToActiveResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void transitionToStandby(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::TransitionToStandbyRequestProto* request,
      ::cloudfs::TransitionToStandbyResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void getServiceStatus(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::GetServiceStatusRequestProto* request,
      ::cloudfs::GetServiceStatusResponseProto* response,
      ::google::protobuf::Closure* done) override;

 private:
  HAStateBase* ha_state_{nullptr};
  SafeModeBase* safemode_{nullptr};

  friend class MethodRecorder<HAService>;
  std::unordered_map<std::string, MethodMetric> method_metrics_;

  void InitMetrics();

  std::unique_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
};

}  // namespace dancenn

#endif  // SERVICE_HA_SERVICE_H_

