// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef SERVICE_NAMENODE_SERVICE_H_
#define SERVICE_NAMENODE_SERVICE_H_

#include <NamenodeProtocol.pb.h>
#include <memory>

#include "service/method_recorder.h"
#include "service/service_meta.h"

namespace dancenn {

class BlockManager;
class DatanodeManager;
class NameSpace;
class HAStateBase;

class NameNodeService : public
    cloudfs::namenode::NamenodeProtocolService {
 public:
  explicit NameNodeService(
      std::shared_ptr<DatanodeManager> datanode_manager,
      std::shared_ptr<BlockManager> block_map,
      std::shared_ptr<NameSpace> name_space,
      HAStateBase* ha_state);
  virtual ~NameNodeService() {
  }

  NameNodeService(const NameNodeService&) = delete;
  NameNodeService& operator=(const NameNodeService&) = delete;

  void versionRequest(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::VersionRequestProto* request,
      ::cloudfs::VersionResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void isUpgradeFinalized(
      ::google::protobuf::RpcController *controller,
      const ::cloudfs::namenode::IsUpgradeFinalizedRequestProto *request,
      ::cloudfs::namenode::IsUpgradeFinalizedResponseProto *response,
      ::google::protobuf::Closure *done) override;

  void getBlocks(::google::protobuf::RpcController* controller,
      const ::cloudfs::namenode::GetBlocksRequestProto* request,
      ::cloudfs::namenode::GetBlocksResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void SetMethodMetas(std::shared_ptr<ServiceMeta> sm);

 private:
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<NameSpace> namespace_;
  HAStateBase* ha_state_{nullptr};

  friend class MethodRecorder<NameNodeService>;
  std::unordered_map<std::string, MethodMetric> method_metrics_;

  void InitMetrics();
};

}  // namespace dancenn

#endif  // SERVICE_NAMENODE_SERVICE_H_

