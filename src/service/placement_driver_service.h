// Copyright 2017 He <PERSON><PERSON>yi <<EMAIL>>

#ifndef SERVICE_PLACEMENT_DRIVER_SERVICE_H_
#define SERVICE_PLACEMENT_DRIVER_SERVICE_H_

#include <PlacementDriverProtocol.pb.h>

#include <memory>
#include <unordered_map>
#include <string>

#include "base/read_write_lock.h"
#include "base/dictionary.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/block_placement.h"

namespace dancenn {

class PlacementDriverService
    : public cloudfs::namenode::PlacementDriverService {
 public:
  PlacementDriverService();
  ~PlacementDriverService() override;

  PlacementDriverService(const PlacementDriverService&) = delete;
  PlacementDriverService& operator=(const PlacementDriverService&) = delete;

  void reportDatanodes(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::namenode::ReportDatanodesRequestProto* request,
      ::cloudfs::namenode::ReportDatanodesResponseProto* response,
      ::google::protobuf::Closure* done) override;

  void chooseTarget(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::namenode::ChooseTargetRequestProto* request,
      ::cloudfs::namenode::ChooseTargetResponseProto* response,
      ::google::protobuf::Closure* done) override;

 private:
  ReadWriteLock rwlock_;
  // for demo purpose
  std::unordered_map<uint32_t, std::unique_ptr<BlockPlacement>> placements_;
  std::unordered_map<uint32_t, DatanodeInfoPtr> datanodes_;
  Dictionary<std::string, uint32_t> dn_ids_;  // ip <-> dn_id
};

}  // namespace dancenn

#endif  // SERVICE_PLACEMENT_DRIVER_SERVICE_H_

