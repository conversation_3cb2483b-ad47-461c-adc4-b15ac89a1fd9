// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef SERVICE_METHOD_RECORDER_H_
#define SERVICE_METHOD_RECORDER_H_

#include <google/protobuf/service.h>
#include <glog/logging.h>

#include <chrono>
#include <memory>

#include "base/metrics.h"
#include "rpc/rpc_controller.h"

namespace dancenn {

typedef struct MethodMetric {
  MetricID num_ops_;
  MetricID num_success_ops_;
  MetricID duration_;
  MetricID success_duration_;
} MethodMetric;

template<class T>
class MethodRecorder {
 public:
  MethodRecorder(T* service,
                 const std::string& name,
                 RpcController* controller,
                 ::google::protobuf::Closure* done)
      : service_(service),
        controller_(controller),
        done_(done) {
    CHECK_NOTNULL(service_);
    CHECK_NOTNULL(controller_);
    CHECK_NOTNULL(done_);
    {
      auto itr = service_->method_metrics_.find(name);
      CHECK(itr != service_->method_metrics_.end());
      num_ops_          = itr->second.num_ops_;
      num_success_ops_  = itr->second.num_success_ops_;
      duration_         = itr->second.duration_;
      success_duration_ = itr->second.success_duration_;
    }
    start_ = std::chrono::steady_clock::now();
  }

  ~MethodRecorder() {
    auto success = !controller_->Failed();
    done_->Run();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - start_).count();
    MFC(num_ops_)->Inc();
    MFH(duration_)->Update(duration);
    if (success) {
      MFC(num_success_ops_)->Inc();
      MFH(success_duration_)->Update(duration);
    }
  }

 private:
  T* service_{nullptr};
  RpcController* controller_{nullptr};
  ::google::protobuf::Closure* done_{nullptr};
  MetricID num_ops_;
  MetricID num_success_ops_;
  MetricID duration_;
  MetricID success_duration_;
  std::chrono::steady_clock::time_point start_;
};

}  // namespace dancenn

#endif  // SERVICE_METHOD_RECORDER_H_

