#!/bin/bash

BLUE_COLOR='\E[1;34m'
RES='\E[0m'

echo -e "${BLUE_COLOR}Building proto files...${RES}"

cd `dirname $0`

CLOUDFS_PB="generated/cloudfs"
if [ -d "$CLOUDFS_PB" ]
then
  echo -e "${BLUE_COLOR}clear cloudfs proto files${RES}"
  rm -rf ${CLOUDFS_PB}/*
fi

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:../../third_party/cfs_dancenn_thirdparty/builds/third_party/protobuf/output/lib
protoc=../../third_party/cfs_dancenn_thirdparty/builds/third_party/protobuf/output/bin/protoc

JAVA_HDFS_PB="../../java/cfs-ranger-bridger/src/main/java"
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/HAServiceProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/IpcConnectionContext.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/ProtobufRpcEngine.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/RpcHeader.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/Security.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/ReconfigurationProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/acl.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/ClientDatanodeProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/ClientNamenodeProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/DatanodeProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/datatransfer.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/encryption.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/fsimage.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/hdfs.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/inotify.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/JournalProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/lifecycle.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/NamenodeProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/PlacementDriverProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/QJournalProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/TestProtocol.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/xattr.proto
${protoc} -I ./cloudfs --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/lifecycle.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn dancenn/persistent_flags.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn dancenn/namesystem_info.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/inode.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/block_info_proto.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/audit_log.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/datanode_info.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/edit_log.proto
${protoc} -I ./dancenn  --cpp_out=./generated/dancenn --java_out=$JAVA_HDFS_PB dancenn/status_code.proto
${protoc} -I ./btrace --cpp_out=./generated/btrace --java_out=$JAVA_HDFS_PB btrace/btrace.proto
${protoc} -I ./cloudfs -I ./dancenn --cpp_out=./generated/cloudfs --java_out=$JAVA_HDFS_PB cloudfs/ranger.proto
${protoc} -I ./databus --cpp_out=./generated/databus --java_out=$JAVA_HDFS_PB databus/collector.proto

echo -e "${BLUE_COLOR}Finished building proto files${RES}"
