syntax = "proto2";


import "inode.proto";

package cloudfs;
import "acl.proto";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "RangerProtos";
option java_generate_equals_and_hash = true;
option java_generic_services = true;


service RangerBridgerService {
  rpc IsAccessAllowed(RangerRequestListPB) returns (RangerResponseListPB) {}
}

message KVPB {
  required string key = 1;

  required string value = 2;
}

message RangerAccessRequestPB {
  required string path = 1;

  optional string path_owner = 2;

  required cloudfs.AclEntryProto.FsActionProto fs_action = 3;

  required string user = 4;

  // This is not used now, but I keep it here for potential change.
  repeated string user_groups = 5;

  optional string remote_ip = 6;

  // FIXME: Supported: the token, so far we don't bring those things.
  optional dancenn.INode final_inode = 7;

  required int64 access_time = 8;

  required string path_to_be_verified = 9;
}

message RangerRequestListPB {
  required RangerAccessRequestPB access_request = 1;

  optional bool flush_audit_if_allow = 2;
}

// Describes a list of authorization descison responded from the Ranger
// service for a request for a single user. Note that the order of
// responses is determined by the order of requests in RangerRequestListPB.
message RangerResponseListPB {
  optional RangerAccessResponsePB access_response = 1;

  optional bool audit_flushed = 2;
}

message RangerAuditRequestPB {

  // LogHadoop or LogRangerResult.
  required bool is_allowed = 1;

  required int64 event_time = 2;

  required string access_type = 3;

  required string resource_path = 4;

  optional string result_reason = 5;

  required bool access_result = 6;

  optional int32 policy_id = 7;

  optional int64 policy_version = 8;

  required string action = 9;

  repeated KVPB additional_info = 10;

  optional int32 retry_cnt = 11;

  optional string zone_name = 12;
}

message RangerAuditResponsePB {
  required bool success = 1;

  required int32 retry_cnt = 2;
}

message RangerAccessResponsePB {

 required int32 policy_type = 1;

 required bool is_access_determined = 2;

 required bool is_allowed = 3;

 required int64 policy_id = 4;

 required int32 policy_priority = 5;

 optional string zone_name = 6;

 optional int64 policy_version = 7;

 required int64 evaluated_policies_count = 8;

 optional string reason = 9;

 repeated KVPB additional_info = 10;

}
