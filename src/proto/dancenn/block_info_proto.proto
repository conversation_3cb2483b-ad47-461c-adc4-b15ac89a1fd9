package dancenn;

import "hdfs.proto";
import "lifecycle.proto";
import "DatanodeProtocol.proto";

option java_package = "com.bytedance.cloudfs.proto";
option java_outer_classname = "BlockInfoProtos";
option java_generate_equals_and_hash = true;
option java_generic_services = true;

message ReplicaInfoProto {
  enum Reporter {
    kNamenode = 0;
    kDatanode = 1;
    kClient = 2;
  }

  optional uint32 report_ts = 1 [default = 0];
  optional uint32 invalidate_ts = 2 [default = 0];
  optional Reporter reporter = 3 [default = kNamenode];
  optional bool tried_as_primary_4_block_recovery = 4 [default = false];
  optional bool is_bad = 6 [default = false];
  optional cloudfs.ReplicaStateProto state = 7 [default = TEMPORARY];
  optional uint64 gen_stamp = 8 [default = 0];
  optional uint64 num_bytes = 9 [default = 0];
  optional string dn_uuid = 10;
}

message BlockInfoProto {
  enum Status {
    kUnderConstruction = 1;
    kUnderRecovery = 2;
    kCommitted = 3;
    kComplete = 0;
    kUploadIssued = 5; // Deprecated
    kPersisted = 4;
    kDeprecated = 6;   // Deprecated
    kDeleted = 7;      // Deprecated
  }

  enum Type {
    kHDFSBlock = 1;
    kACCBlock = 2;
  }
  required Status state = 1;
  required uint64 block_id = 2;
  required uint64 gen_stamp = 3;
  required uint64 num_bytes = 4;
  required uint64 inode_id = 5;
  // Set parent inode id to kInvalidINodeId when loading to BlockInfo.
  required int64 expected_rep = 6;
  optional cloudfs.IoMode write_mode = 14 [default = DATANODE_BLOCK];

  optional string pufs_name = 7;
  optional uint32 upload_issued_times = 8;
  repeated string aborted_upload_ids = 9;
  optional string curr_upload_id = 10;
  optional string dn_uuid = 11;
  optional uint64 nn_exp_ts = 12;
  optional uint64 dn_exp_ts = 13;

  optional uint64 pufs_offset = 15;
  optional Type type = 16 [default=kHDFSBlock];
  optional cloudfs.datanode.UploadType upload_type = 17;
  optional bool key_block = 18 [default=false];
  optional uint32 part_num = 19;
  optional string etag = 20;
  optional uint64 bundle_offset = 21;
  optional uint64 bundle_length = 22;
  repeated ReplicaInfoProto replicas = 23;
  // This is a queue with a maximum size of 8 or 16.
  // Used to maintain block index.
  
  // Timestamp in millisecond.
  optional uint64 version = 24 [default = 0];

  // For block recovery.
  optional uint64 recovery_gen_stamp = 25 [default = 0];
  optional string primary_dn_uuid_4_block_recovery = 26;

  repeated string deleted_dn_uuids = 27;

  // hint whether replicas on DN need to be aligned
  optional bool need_align = 28 [default = false];
}
