package dancenn;

message EditLogConf {
  message PreviousEditLogConf {
    message HAEditLogConf {
      optional int64 syncer_pending_begin_txid = 1;
      optional int64 syncer_pending_end_txid = 2;
      optional int64 committer_pending_begin_txid = 3;
      optional int64 committer_pending_end_txid = 4;
    }

    message NonHAEditLogConf {
    }

    optional int64 pending_begin_txid = 1;
    optional int64 pending_end_txid = 2;
    optional int64 reserved_begin_txid = 3;
    optional int64 reserved_end_txid = 4;
    optional uint64 last_allocated_block_id = 5;
    optional uint64 last_generation_stamp_v2 = 6;
    optional bool is_ha_edit_log_conf = 7;
    optional HAEditLogConf ha_edit_log_conf = 8;
    optional NonHAEditLogConf non_ha_edit_log_conf = 9;
  }

  enum HAMode {
    HA = 1;
    ActiveNonHA = 2;
    StandbyNonHA = 3;
    // NonHA = ActiveNonHA + StandbyNonHA
    NonHA = 4;
  }

  optional HAMode mode = 1;
  optional int64 conf_begin_txid = 2;
  optional int64 conf_end_txid = 3;
  optional PreviousEditLogConf previous_edit_log_conf = 4;
}
