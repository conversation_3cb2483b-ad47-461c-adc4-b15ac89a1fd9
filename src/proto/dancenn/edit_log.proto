package dancenn;

import "acl.proto";
import "block_info_proto.proto";
import "hdfs.proto";
import "inode.proto";
import "lifecycle.proto";
import "xattr.proto";
import "fsimage.proto";

message BlockInfoProtos {
  repeated BlockInfoProto content = 1;
  optional string trigger = 2;
}

// Next Id: 4
message BlockInfoProtoWithLocs {
  required BlockInfoProto bip = 1;
  repeated string dns = 2;
  repeated string storages = 3;
}

message SnapshotLog {
  required INode inode = 1;
  required uint64 snapshot_root_id = 2;
}

// Next Id: 22
message FileToBeOpen {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 16 [ default = false ];

  required string path = 1;
  required INode inode = 2;             // new inode
  required bool overwrite = 3;
  optional INode parent = 5;
  optional bool move_to_recycle_bin = 6;
  optional string rb_path = 7;
  optional INode rb_inode = 8;
  optional INode rb_parent = 9;
  optional INode overwrite_inode = 10;  // old inode, if overwritten

  repeated BlockInfoProto add_block_bips = 20 [ deprecated = true ]; // Deprecate in 5.12.2
  repeated BlockInfoProtoWithLocs add_block_bips_with_locs = 21;
  // inodestat info
  repeated uint64 ancestors_id = 17;
  repeated uint64 rb_ancestors_id = 12;

  // snapshot info, empty if no snapshot data changes
  optional SnapshotLog old_inode_snaplog = 13;
  optional SnapshotLog parent_snaplog = 14;
  optional SnapshotLog rb_parent_snaplog = 15;
}

message FileToBeAppend {
  // common info
  optional RpcInfoPB log_rpc_info = 7;
  required bool physical_applyable = 8 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  required INode parent = 3;
  required INode old_inode = 4;
  repeated uint64 ancestors_id = 5;
  optional SnapshotLog inode_snaplog = 6;
}

// Next Id: 13
message BlockToBeAdd {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 9 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  // https://stackoverflow.com/questions/45431685/protocol-buffer-does-changing-field-name-break-the-message
  // Changing a field name will not affect the protobuf
  // encoding or compatibility between applications that
  // use proto definitions which differ only by field names.
  // The binary protobuf encoding is based on tag numbers,
  // so that is what you need to preserve.
  optional BlockInfoProto penultimate_bip = 3 [ deprecated = true ]; // The original name was penultimate_bip_tbc. Deprecate in 5.12.2
  optional BlockInfoProto last_bip = 4 [ deprecated = true ];        // The original name was last_bip_tbuc. Deprecate in 5.12.2
  optional INode old_inode = 10;
  repeated uint64 ancestors_id = 7;
  optional SnapshotLog inode_snaplog = 8;
  optional BlockInfoProtoWithLocs penultimate_bip_with_locs = 11;
  optional BlockInfoProtoWithLocs last_bip_with_locs = 12;
}

message BlockToBeAbandon {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;

  optional uint64 abandoned_blk_id = 4;

  optional INode old_inode = 8;
  repeated uint64 ancestors_id = 6;
}

// Next Id: 9
message PipelineToBeUpdate {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional BlockInfoProto last_bip_tbuc = 3 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs last_bip_tbuc_with_locs = 8;
  optional INode old_inode = 5;
  repeated uint64 ancestors_id = 6;
}

// Next Id: 10
message FileToBeSync {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 8 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional BlockInfoProto last_bip_tbuc = 3 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs last_bip_tbuc_with_locs = 9;
  optional INode old_inode = 5;
  repeated uint64 ancestors_id = 6;
  optional SnapshotLog inode_snaplog = 7;
}

message BlockToBeCommitSynchronization {
  optional string path = 1;
  optional bool delete_block = 2;
  optional bool close_file = 3;
  optional INode inode = 4;
  optional BlockInfoProto last_bip = 5;
  optional INode old_inode = 6;
  repeated uint64 ancestors_id = 7;
  optional SnapshotLog inode_snaplog = 8;
}

// Next Id: 9
message BlocksToBeUpdate {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional BlockInfoProto last_bip = 3 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs last_bip_with_locs = 8;
  optional INode old_inode = 5;
  repeated uint64 ancestors_id = 6;
}

// Next Id: 13
message FileToBeClose {
  // common info
  optional RpcInfoPB log_rpc_info = 8;
  optional bool physical_applyable = 9 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional BlockInfoProto last_bip = 3 [ deprecated = true ]; // The original name was last_bip_tbc. Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs last_bip_with_locs = 12;

  optional uint64 abandoned_blk_id = 4;
  optional INode old_inode = 10;
  repeated uint64 ancestors_id = 11;
  optional SnapshotLog inode_snaplog = 7;
}

message LeaseToBeReassign {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 8;
  required string lease_holder = 3;
  required string new_holder = 4;
  optional SnapshotLog inode_snaplog = 5;
}

message BlockIdToBeAllocate {
  required uint64 new_block_id = 1;
}

message GenStampToBeSet {
  required uint64 new_gen_stamp = 1;
}

// Next Id: 13
message FileToBeConcat {
  // common info
  optional RpcInfoPB log_rpc_info = 9;
  optional bool physical_applyable = 11 [ default = false ];

  required string target_path = 1;
  repeated string src_paths = 2;
  required string parent_path = 3;

  required INode target_inode = 4;
  optional INode old_target_inode = 10;
  repeated INode src_inodes = 5;
  repeated BlockInfoProto target_bips = 6 [ deprecated = true ]; // Deprecate in 5.12.2
  repeated BlockInfoProtoWithLocs target_bips_with_locs = 12;
  required INode parent = 7;

  required uint64 timestamp_in_ms = 8;
}

// Next Id: 10
message FileAndBlockToBeMerge {
  // common info
  optional bool physical_applyable = 8 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  required BlockInfoProto merged_bip = 3 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs merged_bip_with_locs = 9;
  repeated cloudfs.BlockProto depred_blks = 4;
  optional INode old_inode = 5;
  repeated uint64 ancestors_id = 6;
  optional SnapshotLog inode_snaplog = 7;
}

// Next Id: 9
message INodeToPin {
  // common info
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 6;
  optional JobInfoOpBody job = 3;
  optional string cancel_job_id = 4;
  optional RpcInfoPB log_rpc_info = 5;
  optional bool update_txid = 8;
}

message INodeToReconcile {
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 6;
  repeated string cancel_job_id = 3;
  repeated int64 expired_ttl = 4;
  repeated int64 new_ttl = 5;
}

// Next Id: 5
message BlkToBeApproveUpload {
  required BlockInfoProto bip = 1 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProto old_bip = 2 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs bip_with_locs = 3;
  optional BlockInfoProtoWithLocs old_bip_with_locs = 4;
}

// Next Id: 5
message BlkToBePersist {
  required BlockInfoProto bip = 1 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProto old_bip = 2 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs bip_with_locs = 3;
  optional BlockInfoProtoWithLocs old_bip_with_locs = 4;
}

// Next Id: 5
message DepringBlksToBeDel {
  repeated uint64 dangling_blk_ids = 1 [ deprecated = true ]; // Deprecate in 5.12.2
  repeated BlockInfoProto depred_bips = 2 [ deprecated = true ]; // Deprecate in 5.12.2
  optional BlockInfoProtoWithLocs bip_with_locs = 3;
  optional BlockInfoProtoWithLocs old_bip_with_locs = 4;
}

message ATimeToBeUpdate {
  required uint64 inode_id = 1;
  required uint64 atime = 2;
}

message ATimeToBeUpdateProtos {
  repeated ATimeToBeUpdate atime_to_be_updates = 1;
}

message DepredBlksToBeDel {
  repeated uint64 blk_ids = 1;
}

message DirToBeMake {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode parent = 3;
  repeated uint64 ancestors_id = 4;
  optional SnapshotLog parent_snaplog = 5;
}

message INodeToBeDelete {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 9 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode parent = 3;
  required uint64 timestamp_in_ms = 4;
  repeated uint64 ancestors_id = 10;
  optional SnapshotLog inode_snaplog = 7;
  optional SnapshotLog parent_snaplog = 8;
}

message INodeToBeRenameOld {
  // common info
  optional RpcInfoPB log_rpc_info = 8;
  optional bool physical_applyable = 14 [ default = false ];

  required string src_path = 1;
  required string dst_path = 2;
  required INode src_inode = 3;   // old src inode
  required INode dst_inode = 4;   // new dst inode
  required INode src_parent = 5;  // new src parent
  required INode dst_parent = 6;  // new dst parent
  required uint64 timestamp_in_ms = 7;

  repeated uint64 src_ancestors_id = 9;
  repeated uint64 dst_ancestors_id = 10;

  // snapshot info below, empty if no snapshot data changes
  optional SnapshotLog src_inode_snaplog = 11;
  optional SnapshotLog src_parent_snaplog = 12;
  optional SnapshotLog dst_parent_snaplog = 13;
}

message INodeToBeRename {
  // common info
  optional RpcInfoPB log_rpc_info = 10;
  optional bool physical_applyable = 23 [ default = false ];

  message RenameOptions {
    required string value = 1;
  }
  required string src_path = 1;
  required string dst_path = 2;
  required INode src_inode = 3;         // old src inode
  required INode dst_inode = 4;         // new dst inode
  required INode src_parent = 5;        // new src parent
  required INode dst_parent = 6;        // new dst parent
  optional INode overwrite_inode = 7;   // old dst inode
  required uint64 timestamp_in_ms = 8;
  required RenameOptions rename_options = 9;
  optional bool move_to_recycle_bin = 11;
  optional string rb_path = 12;
  optional INode rb_inode = 13;         // new rb inode
  optional INode rb_parent = 14;        // new rb parent

  repeated uint64 src_ancestors_id = 24;
  repeated uint64 dst_ancestors_id = 16;
  repeated uint64 rb_ancestors_id = 17;

  // snapshot info below, empty if no snapshot data changes
  optional SnapshotLog src_inode_snaplog = 18;
  optional SnapshotLog old_dst_inode_snaplog = 19;
  optional SnapshotLog src_parent_snaplog = 20;
  optional SnapshotLog dst_parent_snaplog = 21;
  optional SnapshotLog rb_parent_snaplog = 22;
}

message INodeToSetReplication {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  required uint32 replication = 3;
  optional SnapshotLog inode_snaplog = 10;
}

message INodeToSetStoragePolicy {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  required uint32 policy_id = 3;
  optional SnapshotLog inode_snaplog = 4;
}

message INodeToSetReplicaPolicy {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  required uint32 policy_id = 3;
  optional SnapshotLog inode_snaplog = 4;
}

message DirToSetReplicaPolicy {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 8;
  required uint32 policy_id = 3;
  required string dc = 4;
  optional SnapshotLog inode_snaplog = 5;
}

message INodeToSetQuota {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required string src = 1;
  required uint64 ns_quota = 2;
  required uint64 ds_quota = 3;
  optional SnapshotLog inode_snaplog = 4;
};

message INodeToSetPermissions {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  required uint32 permissions = 3;
  optional SnapshotLog inode_snaplog = 4;
}

message INodeToSetOwner {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 8;
  required string username = 3;
  required string groupname = 4;
  optional SnapshotLog inode_snaplog = 5;
}

message INodeToSetTimes {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required uint64 mtime = 2;
  required uint64 atime = 3;
  optional INode inode = 4;
  optional INode old_inode = 8;
  optional SnapshotLog inode_snaplog = 5;
}

message INodeToSymlink {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 4 [ default = false ];

  required string path = 1;
  required INode node = 2;
}

message INodeToSetAcl {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 4 [ default = false ];

  required string path = 1;
  repeated cloudfs.AclEntryProto acl_entries = 2;
}

message INodeToSetXAttrs {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  repeated cloudfs.XAttrProto xAttrs = 3;
  optional SnapshotLog inode_snaplog = 5;
}

message INodeToRemoveXAttrs {
  // common info
  optional RpcInfoPB log_rpc_info = 4;
  optional bool physical_applyable = 6 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 7;
  repeated cloudfs.XAttrProto xAttrs = 3;
  optional SnapshotLog inode_snaplog = 5;
}

message SnapshotToAllow {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 4 [ default = false ];

  required string path = 1;
  optional INode inode = 2;
}

message SnapshotToDisallow {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 4 [ default = false ];

  required string path = 1;
  optional INode inode = 2;
}

message SnapshotToCreate {
  // common info
  optional RpcInfoPB log_rpc_info = 7;
  optional bool physical_applyable = 8 [ default = false ];

  required string path = 1;
  required string name = 2;
  required uint64 timestamp_in_ms = 3;
  optional INode inode = 4;
  optional uint64 new_snapshot_id = 5;
}

message SnapshotToDelete {
  // common info
  optional RpcInfoPB log_rpc_info = 6;
  optional bool physical_applyable = 7 [ default = false ];

  required string path = 1;
  required string name = 2;
  required uint64 timestamp_in_ms = 3;
  optional INode inode = 4;
}

message SnapshotToRename {
  // common info
  optional RpcInfoPB log_rpc_info = 7;
  optional bool physical_applyable = 8 [ default = false ];

  required string path = 1;
  required string old_name = 2;
  required string new_name = 3;
  required uint64 timestamp_in_ms = 4;
  optional INode inode = 5;
  optional SnapshotLog inode_snaplog = 6;
}

message LifecyclePolicyToBeSet {
  // common info
  optional RpcInfoPB log_rpc_info = 5;
  optional bool physical_applyable = 6 [ default = false ];

  required uint64 inode_id = 2;
  required string path = 1;
  required uint64 timestamp_ms = 3;
  required cloudfs.LifecyclePolicyProto policy = 4;
}

message LifecyclePolicyToBeUnset {
  // common info
  optional RpcInfoPB log_rpc_info = 3;
  optional bool physical_applyable = 4 [ default = false ];

  required uint64 inode_id = 2;
  optional string path = 1;
}

message AZBlacklist {
  required string azs = 1;
}

message AccSyncDummyOpBody {}

message FileWithBlocks {
  required INode inode = 1;
  repeated BlockInfoProto block_info = 2;
};

message AccSyncListingBatchAddOpBody {
  // common info
  optional bool physical_applyable = 4 [ default = false ];

  required string dir_path = 1;
  required INode dir_inode = 2;
  repeated FileWithBlocks files_to_add = 3;
}

message AccSyncListingBatchUpdateOpBody {
  // common info
  optional bool physical_applyable = 5 [ default = false ];

  required string dir_path = 1;
  required INode dir_inode = 2;
  repeated INode files_to_update = 3;
  repeated INode old_files = 4;
}

message AccSyncUpdateINodeOpBody {
  // common info
  optional bool physical_applyable = 4 [ default = false ];

  required string path = 1;
  required INode inode = 2;
  optional INode old_inode = 3;
};

message AccSyncAddFileOpBody {
  // common info
  optional bool physical_applyable = 5 [ default = false ];

  required string path = 1;
  required INode parent = 2;
  required FileWithBlocks file_blocks = 3;
  optional INode old_file_to_del = 4;
};

message AccPersistFileOpBody {
  // common info
  optional bool physical_applyable = 5 [ default = false ];

  required string path = 1;
  required INode parent = 2;
  required INode inode = 3;
  optional INode old_inode = 4;
};

message AccUpdateBlockInfoOpBody {
  // common info
  optional bool physical_applyable = 3 [ default = false ];

  required BlockInfoProto bip = 1;
  optional BlockInfoProto old_bip = 2;
};

message JobStatusOpBody {
  enum WorkflowState {
    CREATED = 0;
    PROCESSING = 1;
    SUCCESS = 2;
    FAILED = 3;
    CANCELED = 4;
    TIMEOUT = 5;
    THROTTLED = 6;
  }
  required WorkflowState state = 1;
  required uint64 created_timestamp = 2;
  optional uint64 complete_timestamp = 3;
  optional uint32 total_task_number = 4 [ default = 0 ];
  optional uint32 success_task_number = 5 [ default = 0 ];
  optional uint32 failed_task_number = 6 [ default = 0 ];
  optional uint32 canceled_task_number = 7 [ default = 0 ];
  optional uint32 timeout_task_number = 8 [ default = 0 ];
  optional uint32 throttled_task_number = 9 [ default = 0 ];
};

message ReconcileINodeAttrsJobStatus {
  // common info
  optional bool physical_applyable = 3 [ default = false ];

  required string path = 1;
  required INode inode = 2;
};

message JobInfoOpBody {
  // common info
  optional bool physical_applyable = 7 [ default = false ];

  enum Type {
    UNKNOWN = 0;
    LoadDataJob = 1;
    FreeJob = 2;
    LoadMetadataJob = 3;
    ReconcileINodeAttrsJob = 4;
    SetReplicaJob = 5;
    ResidentDataJob = 6;
    ChainJob = 7;
    CopyReplica = 8;
  }

  required string job_id = 1;
  required Type job_type = 2;
  required JobStatusOpBody job_status = 3;
  optional bool uncached = 4 [ default = false ];
  optional ReconcileINodeAttrsJobStatus reconcile_inode_attr_job_status = 5;
  optional bool notify_standby = 6 [ default = false ];
};


// Next Id: 12
message BatchInodeToCreate {
  // Next Id: 5
  message CreateEntry {
    optional string path = 1;
    optional INode inode = 2;

    optional BlockInfoProto add_block_bips = 3 [ deprecated = true ]; // Deprecate in 5.12.2
    repeated BlockInfoProtoWithLocs add_block_bips_with_locs = 4;
  }

  repeated CreateEntry files = 1;

  optional RpcInfoPB log_rpc_info = 2;
  optional INode parent = 3;
  optional uint64 timestamp_in_ms = 4;

  repeated INode overwritten_inodes = 5;
  repeated BlockInfoProto overwritten_bips = 6;

  // optional bool move_to_recycle_bin = 7;
  // optional string rb_path = 8;
  // optional INode rb_inode = 9;
  // optional INode rb_parent = 10;
};

// Next Id: 15
message BatchInodeToComplete {
  repeated string paths = 1;

  repeated INode complete_inodes = 2;
  repeated INode deleted_inodes = 3;
  repeated BlockInfoProto bips = 4 [ deprecated = true ]; // Deprecate in 5.12.2
  repeated BlockInfoProto deleted_bips = 5 [ deprecated = true ]; // Deprecate in 5.12.2
  repeated BlockInfoProtoWithLocs bips_with_locs = 13;
  repeated BlockInfoProtoWithLocs deleted_bips_with_locs = 14;

  // for check
  repeated INode original_inodes = 6;

  optional INode parent = 10;
  optional uint64 timestamp_in_ms = 11;
  optional RpcInfoPB log_rpc_info = 12;
};

message BatchInodeToDelete {
  // Next Id: 5
  message DeleteEntry {
    optional string path = 1;
    optional INode inode = 2;
    repeated BlockInfoProto bips = 3 [ deprecated = true ]; // Deprecate in 5.12.2
    repeated BlockInfoProtoWithLocs bips_with_locs = 4;
  }
  repeated DeleteEntry files = 1;

  optional INode parent = 2;
  optional uint64 timestamp_in_ms = 3;
  optional RpcInfoPB log_rpc_info = 4;
};
