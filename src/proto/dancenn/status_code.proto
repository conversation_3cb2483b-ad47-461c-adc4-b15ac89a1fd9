package dancenn;

option java_package = "com.bytedance.cloudfs.proto";
option java_outer_classname = "StatusCodeProtos";
option java_generate_equals_and_hash = true;
option java_generic_services = true;

enum StatusCode {
  kError = -1;
  kOK = 0;
  kFileExists = 1;
  kFileNotFound = 2;
  kDirExists = 3;
  kDirNotFound = 4;
  kBadParameter = 5;
  kUpdateError = 6;
  kFileStatusError = 7;
  kFileTypeError = 8;

  kNotMinReplicated = 20;
  kIsRetry = 21;

  kLeaseError = 30;

  kNotEnoughDN = 40;
}
