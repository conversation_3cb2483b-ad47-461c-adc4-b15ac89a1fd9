package dancenn;

import "hdfs.proto";
import "acl.proto";
import "xattr.proto";

option java_package = "org.apache.hadoop.hdfs.protocol.proto";
option java_outer_classname = "INodeProtos";
option java_generate_equals_and_hash = true;

message PermissionStatus {
  required string username = 1;
  required string groupname = 2;
  required uint32 permission = 3;
}

message FileUnderConstructionFeature {
  optional string client_name = 1;
  optional string client_machine = 2;
}

message RpcInfoPB {
  optional string rpc_client_id = 1;
  optional uint32 rpc_call_id = 2;
}

message QuotaPolicyProto {
  optional uint64 inode_limit = 1;
  optional uint64 file_limit = 2;
  optional uint64 dir_limit = 3;
  optional uint64 data_size_limit = 4;
}

message MountPoint {
  repeated string allow_entries = 1;
  // Empty string = All.
  repeated string deny_entries = 2;
}

message PinStatus {
  required bool pinned = 1;
  required int64 ttl = 2;
  required int64 txid = 3;
  optional bool recursive = 4;
  optional string job_id = 5; // for cancel job
  optional bool resident_data = 6;
}

enum UfsFileWriteType {
  kUfsFileWriteTypeAsync = 1;           // 异步写入
  kUfsFileWriteTypeThrough = 2;         // 同步写入
  kUfsFileWriteTypeInvalid = 3;
}
enum UfsFileCreateType {
  kUfsFileCreateTypeNormal = 1; // Upload or Put to TOS
  kUfsFileCreateTypeAppend = 2; // Append to TOS
  kUfsFileCreateTypeInvalid = 100; // Append to TOS
}
enum UfsFileState {
  kUfsFileStateLocal = 1;         // Inode 只保存在 CloudFS
  kUfsFileStateToBePersisted = 2; // Inode 需要被会刷到 TOS
  kUfsFileStatePersisted = 3;     // Inode 已经持久化到 TOS
}
message UfsFileInfoProto {
  required UfsFileState file_state = 1;
  required UfsFileCreateType create_type = 2;
  optional UfsFileWriteType write_type = 3;
  required string etag = 4;
  required uint64 size = 5;
  required uint64 last_modified_ts = 6;
  required uint64 sync_ts = 7; // epoch second
  optional string tagging = 8;
  optional string upload_id = 9;
  optional string key = 11;
  optional uint64 complete_mtime = 12;
}

enum UfsDirState {
  kUfsDirStateIncomplete = 2;    // 目录是不完整的，没有从 TOS Sync 过
  kUfsDirStateSynced = 3;        // 目录从 TOS 完整的 Sync 下来
}
enum UfsDirType {
  kUfsDirTypeLocal = 1;
  kUfsDirTypeUfs = 2;
}
message UfsDirInfoProto {
  required UfsDirState state = 1;
  required UfsDirType type = 2;
  required uint64 sync_ts = 3;
  required uint64 children_sync_ts = 4;
  optional bool need_prefetch = 5 [default = true];
}

message Snapshot {
  // required: auto increment
  optional uint32 snapshot_id = 1;
  // required: snapshot name
  optional string name = 2;
  // required：for read
  optional int64 create_txid = 3;
  // required: delete mark
  optional bool deleted = 4;
  optional uint64 mtime = 5;
}

message SnapshotReference {
  // used to locate a snapshot inode
  optional uint64 inode_id = 1;
  optional int64 last_update_txid = 2;
}

message MergeBlockContext {
  required cloudfs.ExtendedBlockProto block = 1;
  repeated cloudfs.BlockProto oldBlocks = 2;
}

message INode {
  enum Type {
    kFile = 1;
    kDirectory = 2;
    kSymLink = 3;
  }
  enum Status {
    kFileUnderConstruction = 1;
    kFileComplete = 2;
  }
  required uint64 id = 1;
  required uint64 parent_id = 2;
  required string name = 3;
  required PermissionStatus permission = 4;
  required Type type = 5;

  required uint64 mtime = 6;
  required uint64 atime = 7;
  optional uint64 access_pattern = 8; // additional feature, modify with atime

  optional string symlink = 9; // symlink

  optional uint32 preferred_blk_size = 10; // file
  optional uint32 storage_policy_id = 11; // file
  optional uint32 replication = 12; // file
  repeated cloudfs.BlockProto blocks = 13; // file; last block status?
  optional FileUnderConstructionFeature uc = 14; // file
  repeated cloudfs.AclEntryProto acls = 15; // file or directory
  repeated cloudfs.XAttrProto xattrs = 16; // file or directory
  optional Status status = 17; // file
  
  repeated MergeBlockContext mergingBlocks = 18;
  repeated cloudfs.BlockProto mergedBlocks = 19; // Snapshot may access these blocks

  optional int64 last_update_txid = 20;
  // For a normal inode, it is the txid when the inode is created.
  // For a snapshot inode, it is the txid when the snapshot inode is created.
  optional int64 create_txid = 21;
  // optional: txid of deleting inode op. Used to indicate deletion operation.
  // Only valid in snapshot inodes.
  optional int64 delete_txid = 22;

  // true if the INode is a snapshot root directory
  optional bool is_snapshottable = 30 [default = false];
  // record all snapshots in the snapshot root directory INode
  repeated Snapshot snapshots = 31;
  // record the snapshot inode referring current inode.
  // For a normal inode, it means the subtree is in at least one snapshot's view,
  // just snapshot before modification.
  // For a snapshot inode, it means the inode should not be removed until its references
  // gone, and only in snapshot inode this field would have more than one elements.
  repeated SnapshotReference snapshot_references = 32;

  optional PinStatus pin_status = 33;

  // For Acc Mode
  optional UfsFileInfoProto ufs_file_info = 100;
  optional UfsDirInfoProto ufs_dir_info = 101;
}

message INodeParentInfoPB {
  optional uint64 parent_id = 1;
  optional string name = 2;
}
