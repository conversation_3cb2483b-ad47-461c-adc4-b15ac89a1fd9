package dancenn;

import "hdfs.proto";
import "acl.proto";
import "xattr.proto";

message DatanodeInfoEntryPB {
  // required
  optional uint64 internal_id  = 1;
  // required
  optional string uuid  = 2;
  // deprecated
  optional cloudfs.DatanodeIDProto address = 3;
  // deprecated
  optional string ip_address = 4;

  // required
  optional string nodezone_id = 5;

  // deprecated, use storages instead
  repeated string storages_uuid = 6;
  repeated cloudfs.DatanodeStorageProto storages = 11;

  // deprecated
  optional bool is_dead = 7;

  optional bool is_stale = 9;

  optional int32 version = 10;

  // deprecated, replaced by AdminState
  optional bool is_decommission = 8; 
  optional cloudfs.DatanodeInfoProto.AdminState admin_state = 12 [default = NORMAL];
}
