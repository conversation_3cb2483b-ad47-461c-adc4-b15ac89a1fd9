syntax = "proto2";

package dancenn;

option java_package = "com.bytedance.cloudfs.proto";
option java_outer_classname = "AuditLogProtos";
option java_generate_equals_and_hash = true;
option java_generic_services = true;

import "ClientNamenodeProtocol.proto";

message AuditLog {
  enum Method {
    // Dir tree related.
    kMkdirs = 1001;
    kDelete = 1002;
    kRename = 1003;
    kRename2 = 1004;
    kGetListing = 1005;
    kGetContentSummary = 1006;

    // File related.
    kCreate = 2001;
    kAppend = 2002;
    kMsync = 2003;
    kFsync = 2004;
    kComplete = 2005;
    kGetFileInfo = 2006;
    kIsFileClosed = 2007;
    kCommitLastBlock = 2008;
    kConcat = 2009;

    // Lease related.
    kRenewLease = 3001;
    kRecoverLease = 3002;

    // Block related.
    kAddBlock = 4001;
    kAbandonBlock = 4002;
    kReportBadBlocks = 4003;
    kGetBlockLocations = 4004;
    kGetHyperBlockLocations = 4005;

    // Set*/Get* related.
    kSetReplication = 5001;
    kSetStoragePolicy = 5002;
    kGetStoragePolicies = 5003;
    kSetReplicaPolicy = 5004; // Deprecated
    kSetReplicationAttrOnly = 5005;
    kGetReplicaPolicy = 5006; // Deprecated
    kSetReadPolicy = 5007; // Deprecated
    kGetReadPolicy = 5008; // Deprecated
    kSetPermission = 5009;
    kSetOwner = 5010;
    kSetLifecyclePolicy = 5011;
    kUnsetLifecyclePolicy = 5012;
    kGetLifecyclePolicy = 5013;
    kSetDirPolicy = 5014;
    kRemoveDirPolicy = 5015;
    kGetDirPolicy = 5016;

    // Snapshot related.
    kAllowSnapshot = 7001;
    kDisallowSnapshot = 7002;
    kCreateSnapshot = 7003;
    kDeleteSnapshot = 7004;
    kRenameSnapshot = 7005;
    kGetSnapshottableDirListing = 7006;
    kGetSnapshotDiffReport = 7007;

    // Others.
    kGetHAServiceState = 6001;
    kLoadMetadata = 6002;
    kLoadData = 6003;
    kFree = 6004;
    kLookupJob = 6005;
    kCancelJob = 6006;
    kPin = 6007;
    kReconcileINodeAttrs = 6008;
    kBatchCreateFile = 6010;
    kBatchCompleteFile = 6011;
    kBatchDeleteFile = 6012;
    kBatchGetFile = 6013;
    kUpdateATimeProtos = 6014;
  }

  message Request {
    // Dir tree related.
    optional cloudfs.MkdirsRequestProto mkdirs = 1001;
    optional cloudfs.DeleteRequestProto delete = 1002;
    optional cloudfs.RenameRequestProto rename = 1003;
    optional cloudfs.Rename2RequestProto rename2 = 1004;
    optional cloudfs.GetListingRequestProto get_listing = 1005;
    optional cloudfs.GetContentSummaryRequestProto get_content_summary = 1006;

    // File related.
    optional cloudfs.CreateRequestProto create = 2001;
    optional cloudfs.AppendRequestProto append = 2002;
    optional cloudfs.MsyncRequestProto msync = 2003;
    optional cloudfs.FsyncRequestProto fsync = 2004;
    optional cloudfs.CompleteRequestProto complete = 2005;
    optional cloudfs.GetFileInfoRequestProto get_file_info = 2006;
    optional cloudfs.IsFileClosedRequestProto is_file_close = 2007;
    optional cloudfs.CommitLastBlockRequestProto commit_last_block = 2008;
    optional cloudfs.ConcatRequestProto concat = 2009;

    // Lease related.
    optional cloudfs.RenewLeaseRequestProto renew_lease = 3001;
    optional cloudfs.RecoverLeaseRequestProto recover_lease = 3002;

    // Block related.
    optional cloudfs.AddBlockRequestProto add_block = 4001;
    optional cloudfs.AbandonBlockRequestProto abandon_block = 4002;
    optional cloudfs.ReportBadBlocksRequestProto report_bad_blocks = 4003;
    optional cloudfs.GetBlockLocationsRequestProto get_block_locations = 4004;
    optional cloudfs.GetHyperBlockLocationsRequestProto get_hyper_block_locations = 4005;

    // Set*/Get* related.
    optional cloudfs.SetReplicationRequestProto set_replication = 5001;
    optional cloudfs.SetStoragePolicyRequestProto set_storage_policy = 5002;
    optional cloudfs.GetStoragePoliciesRequestProto get_storage_policies = 5003;
    optional cloudfs.SetReplicaPolicyRequestProto set_replica_policy = 5004;
    optional cloudfs.GetReplicaPolicyRequestProto get_replica_policy = 5006;
    optional cloudfs.SetReadPolicyRequestProto set_read_policy = 5007;
    optional cloudfs.GetReadPolicyRequestProto get_read_policy = 5008;
    optional cloudfs.SetPermissionRequestProto set_permission = 5009;
    optional cloudfs.SetOwnerRequestProto set_owner = 5010;
    optional cloudfs.SetLifecyclePolicyRequestProto set_lifecycle_policy = 5011;
    optional cloudfs.UnsetLifecyclePolicyRequestProto unset_lifecycle_policy = 5012;
    optional cloudfs.GetLifecyclePolicyRequestProto get_lifecycle_policy = 5013;
    optional cloudfs.SetDirPolicyRequestProto set_dir_policy = 5014;
    optional cloudfs.RemoveDirPolicyRequestProto remove_dir_policy = 5015;
    optional cloudfs.GetDirPolicyRequestProto get_dir_policy = 5016;

    // Snapshot related.
    optional cloudfs.AllowSnapshotRequestProto allow_snapshot = 7001;
    optional cloudfs.DisallowSnapshotRequestProto disallow_snapshot = 7002;
    optional cloudfs.CreateSnapshotRequestProto create_snapshot = 7003;
    optional cloudfs.DeleteSnapshotRequestProto delete_snapshot = 7004;
    optional cloudfs.RenameSnapshotRequestProto rename_snapshot = 7005;
    optional cloudfs.GetSnapshottableDirListingRequestProto get_snapshottable_dir_listing = 7006;
    optional cloudfs.GetSnapshotDiffReportRequestProto get_snapshot_diff_report = 7007;

    // Others.
    optional cloudfs.HAServiceStateRequestProto get_ha_service_state = 6001;
    optional cloudfs.LoadRequestProto load = 6002;
    optional cloudfs.FreeRequestProto free = 6003;
    optional cloudfs.LookupJobRequestProto lookup_job = 6004;
    optional cloudfs.CancelJobRequestProto cancel_job = 6005;
    optional cloudfs.PinRequestProto pin = 6006;

    optional cloudfs.BatchCreateFileRequestProto batch_create = 6010;
    optional cloudfs.BatchCompleteFileRequestProto batch_complete = 6011;
    optional cloudfs.BatchDeleteFileRequestProto batch_delete = 6012;
    optional cloudfs.BatchGetFileRequestProto batch_get = 6013;
  }

  message Response {
    // Dir tree related.
    optional cloudfs.MkdirsResponseProto mkdirs = 1001;
    optional cloudfs.DeleteResponseProto delete = 1002;
    optional cloudfs.RenameResponseProto rename = 1003;
    optional cloudfs.Rename2ResponseProto rename2 = 1004;
    optional cloudfs.GetListingResponseProto get_listing = 1005;
    optional cloudfs.GetContentSummaryResponseProto get_content_summary = 1006;

    // File related.
    optional cloudfs.CreateResponseProto create = 2001;
    optional cloudfs.AppendResponseProto append = 2002;
    optional cloudfs.MsyncResponseProto msync = 2003;
    optional cloudfs.FsyncResponseProto fsync = 2004;
    optional cloudfs.CompleteResponseProto complete = 2005;
    optional cloudfs.GetFileInfoResponseProto get_file_info = 2006;
    optional cloudfs.IsFileClosedResponseProto is_file_close = 2007;
    optional cloudfs.CommitLastBlockResponseProto commit_last_block = 2008;
    optional cloudfs.ConcatResponseProto concat = 2009;

    // Lease related.
    optional cloudfs.RenewLeaseResponseProto renew_lease = 3001;
    optional cloudfs.RecoverLeaseResponseProto recover_lease = 3002;

    // Block related.
    optional cloudfs.AddBlockResponseProto add_block = 4001;
    optional cloudfs.AbandonBlockResponseProto abandon_block = 4002;
    optional cloudfs.ReportBadBlocksResponseProto report_bad_blocks = 4003;
    optional cloudfs.GetBlockLocationsResponseProto get_block_locations = 4004;
    optional cloudfs.GetHyperBlockLocationsResponseProto get_hyper_block_locations = 4005;

    // Set*/Get* related.
    optional cloudfs.SetReplicationResponseProto set_replication = 5001;
    optional cloudfs.SetStoragePolicyResponseProto set_storage_policy = 5002;
    optional cloudfs.GetStoragePoliciesResponseProto get_storage_policies = 5003;
    optional cloudfs.SetReplicaPolicyResponseProto set_replica_policy = 5004;
    optional cloudfs.GetReplicaPolicyResponseProto get_replica_policy = 5006;
    optional cloudfs.SetReadPolicyResponseProto set_read_policy = 5007;
    optional cloudfs.GetReadPolicyResponseProto get_read_policy = 5008;
    optional cloudfs.SetPermissionResponseProto set_permission = 5009;
    optional cloudfs.SetOwnerResponseProto set_owner = 5010;
    optional cloudfs.SetLifecyclePolicyResponseProto set_lifecycle_policy = 5011;
    optional cloudfs.UnsetLifecyclePolicyResponseProto unset_lifecycle_policy = 5012;
    optional cloudfs.GetLifecyclePolicyResponseProto get_lifecycle_policy = 5013;
    optional cloudfs.SetDirPolicyResponseProto set_dir_policy = 5014;
    optional cloudfs.RemoveDirPolicyResponseProto remove_dir_policy = 5015;
    optional cloudfs.GetDirPolicyResponseProto get_dir_policy = 5016;

    // Snapshot related.
    optional cloudfs.AllowSnapshotResponseProto allow_snapshot = 7001;
    optional cloudfs.DisallowSnapshotResponseProto disallow_snapshot = 7002;
    optional cloudfs.CreateSnapshotResponseProto create_snapshot = 7003;
    optional cloudfs.DeleteSnapshotResponseProto delete_snapshot = 7004;
    optional cloudfs.RenameSnapshotResponseProto rename_snapshot = 7005;
    optional cloudfs.GetSnapshottableDirListingResponseProto get_snapshottable_dir_listing = 7006;
    optional cloudfs.GetSnapshotDiffReportResponseProto get_snapshot_diff_report = 7007;

    // Others.
    optional cloudfs.HAServiceStateResponseProto get_ha_service_state = 6001;
    optional cloudfs.LoadResponseProto load = 6002;
    optional cloudfs.FreeResponseProto free = 6003;
    optional cloudfs.LookupJobResponseProto lookup_job = 6004;
    optional cloudfs.CancelJobResponseProto cancel_job = 6005;
    optional cloudfs.PinResponseProto pin = 6006;

    optional cloudfs.BatchCreateFileResponseProto batch_create = 6010;
    optional cloudfs.BatchCompleteFileResponseProto batch_complete = 6011;
    optional cloudfs.BatchDeleteFileResponseProto batch_delete = 6012;
    optional cloudfs.BatchGetFileResponseProto batch_get = 6013;
  }

  required string cfs_region = 1;
  required string cfs_env =  2;
  required string cfs_cluster = 3;
  required int64 filesystem_id = 4;
  required int64 namespace_id = 5;
  required int64 ts_nanoseconds = 14;
  optional int64 thread_id = 15;

  required Method method = 6;
  required string user = 7;
  required string grp = 8;
  required string ip_addr = 9;
  required bool is_successful = 10;
  required string status = 13;
  required Request request = 11;
  required Response response = 12;
}
