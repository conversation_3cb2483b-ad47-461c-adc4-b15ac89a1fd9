option java_package = "com.bytedance.btrace.proto";

option java_generic_services = true;

option java_generate_equals_and_hash = true;

option java_multiple_files = true;

option optimize_for = SPEED;

package btrace;

enum ApplicationIdType {
    YARN = 1;
    PSM = 2;
    PRIEST_TASK = 3;
    TSS_JOB = 4;
    SYSTEMD_SERVICE = 5;
}

enum ByteSystemType {
    HDFS = 1;
    KAFKA = 2;
}

enum OperationType {
    PRODUCE = 1;
    FETCH = 2;
    READ = 3;
    CREATE = 4;
    APPEND = 5;
    DELETE = 6;
    RENAME = 7;
}

message ApplicationTag {
    required ApplicationIdType application_id_type = 1;
    required string application_id = 2;
}

message ByteTraceId {
    optional string user_agent = 1;
    optional string user = 2;
    repeated ApplicationTag application_tags = 3;
}

message ByteSystemId {
    required ByteSystemType type = 1;
    required string server_ip_address = 2;
    optional string cluster_name = 3;
}

message ByteTraceLog {
    required fixed64 local_timestamp_ms = 1;
    required ByteTraceId btid = 2;
    required ByteSystemId sysid = 3;
    optional string resource_path = 4;
    required OperationType operation_type = 5;
    required int64 operation_cost_ms = 6;
    optional string extra = 7;
}
