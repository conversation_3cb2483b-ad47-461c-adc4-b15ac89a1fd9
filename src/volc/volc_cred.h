
//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <string>

namespace dancenn {

enum VolcCredType {
  kVolcCredFixed = 0,
  kVolcCredSts = 1,
};

struct VolcCredential {
  VolcCredential() = default;

  VolcCredential(std::string _ak, std::string _sk)
      : type(kVolcCredFixed), ak(std::move(_ak)), sk(std::move(_sk)) {
  }

  VolcCredential(std::string _ak, std::string _sk, std::string _token)
      : type(kVolcCredSts),
        ak(std::move(_ak)),
        sk(std::move(_sk)),
        token(std::move(_token)) {
  }

  VolcCredType type;
  std::string ak;
  std::string sk;
  std::string token;
};

}  // namespace dancenn