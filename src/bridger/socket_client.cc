#include "bridger/socket_client.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <chrono>
#include <cstdio>
#include <string>
#include <sys/socket.h>
#include <sys/un.h>
#include <thread>
#include <vector>
#include <chrono>
#include <arpa/inet.h>

#include "ranger.pb.h"
#include "base/time_util.h"

using std::string;
using std::vector;

DECLARE_string(ranger_sock_path);

/*
 * A bridge to talk to java client.
 * AF_UNIX via SOCKET_STREAM, deploy as side car in pod.
 * */
namespace dancenn {
namespace bridger {

namespace {

bool BlockingWrite(int fd, const char* buf, size_t buflen, uint64_t deadline)  {

  size_t total_written = 0;

  // TODO(wangning.ito): we may have a timeout mechanism.
  while (total_written < buflen) {
    int res;
    int32_t num_to_write = buflen - total_written;
    int64_t timeout = deadline - TimeUtil::GetNowEpochMs();
    if (timeout < 0) {
      // TODO(wangning.ito): we may need a enum to represent timeout.
      return false;
    }
    RETRY_ON_EINTR(res, send(fd, buf, num_to_write, MSG_NOSIGNAL));
    if (res < 0) {
      if (errno == EINTR) {
        continue;
      } else if (errno == EAGAIN) {
        // Timeout.
        break;
      }
    } else {
      total_written += res;
    }
    if (res == 0) {
      break;
    }
  }
  if (total_written < buflen) {
    return false;
  }
  return true;
}

bool BlockingRecv(int fd, char* buf, size_t buflen, uint64_t deadline) {
  size_t total_read = 0;
  while (total_read < buflen) {
    int32_t res;
    int32_t num_to_read = buflen - total_read;
    int timeout = deadline - TimeUtil::GetNowEpochMs();
    if (timeout < 0) {
      // TODO(wangning.ito): we may need a enum to represent timeout.
      return false;
    }
    RETRY_ON_EINTR(res, recv(fd, buf, num_to_read, 0));
    total_read += res;
    if (res < 0) {
      if (errno == EINTR) {
        continue;
      } else if (errno == EAGAIN) {
        break;
      }
      break;
    } else if (res == 0) {
      break;
    }
  }
  if (total_read < buflen) {
    return false;
  }

  return true;
}

}  // namespace


Status SockAddr::ParseFromUnixPath(const std::string& path) {
  // @xx is not supported.
  //
  int real_len = strlen(path.c_str());
  if (real_len != path.size()) {
    return Status(JavaExceptions::kIOException, "Path invalid:[" + path + "]");
  }

  storage_.un.sun_family = AF_UNIX;
  memcpy(storage_.un.sun_path, path.c_str(), real_len + 1);

  len_ = sizeof(struct sockaddr_un);

  return Status();
}

Status SockAddr::ParseFromHostPort(const std::string& host, int port) {
  struct in_addr addr;
  if (inet_pton(AF_INET, host.c_str(), &addr) != 1) {
    return Status(JavaExceptions::kIOException, "Invalid IP address" + host);
  }

  constexpr auto len = offsetof(struct sockaddr_in, sin_zero);
  len_ = len;

  storage_.in.sin_family = AF_INET;
  storage_.in.sin_addr = addr;
  storage_.in.sin_port = htons(port);

  return Status();
}

Status ConnectToServer(const SockAddr& addr, SocketClient* client) {
  RETURN_NOT_OK(client->Init(addr.family()));
  RETURN_NOT_OK(client->Connect(addr));
  RETURN_NOT_OK(client->SetRecvTimeout(100));
  RETURN_NOT_OK(client->SetSendTimeout(100));
  return Status();
}

SocketClient::SocketClient() : fd_(-1) { }

SocketClient::~SocketClient() {
  Close();
}

Status SocketClient::Init(int family, int /*flags*/) {
  int fd = ::socket(family, SOCK_STREAM | SOCK_CLOEXEC, /*auto protocol*/0);
  if (fd < 0) {
    LOG(ERROR) << "Unable to open socket: " << ErrnoToString(errno);
    return Status(JavaExceptions::kIOException, "Unable to init");
  }
  fd_ = fd;
  return Status();
}

Status SocketClient::Connect(const SockAddr& sockaddr) {
  int ret;

  RETRY_ON_EINTR(ret, ::connect(fd_, sockaddr.addr(), sockaddr.addrlen()));

  if (ret < 0) {
    int err = errno;
    return Status(JavaExceptions::kIOException, "Unable to connect " + ErrnoToString(err));
  }

  return Status();
};

void SocketClient::Close() {
  if (fd_ < 0) {
    return;
  }
  int ret;
  RETRY_ON_EINTR(ret, ::close(fd_));
  if (ret < 0) {
    // Unable to close;
    return;
  }
  fd_ = -1;
}

Status SocketClient::SendMessage(const google::protobuf::Message& m,
                                 google::protobuf::Message* res,
                                 uint32_t millis) const {

  if (m.ByteSize() > 1 * 1024 * 1024) {
    // FIXME: return a failure.
    return Status(JavaExceptions::kInvalidRequestException, "The request size exceed 1MB.");
  }
  auto deadline = TimeUtil::GetNowEpochMsFor(millis);
  uint32_t len = m.ByteSize();

  vector<char> body_buf;
  body_buf.resize(len);
  // 1. encode length with big endian.
  char header_len[4];
  platform::WriteBigEndian<uint32_t>(header_len, 0, len);
  // 2. fill in body.
  m.SerializeToArray(body_buf.data(), len);

  // send or write.
  bool b = BlockingWrite(fd_, header_len, 4, deadline);
  if (!b) {
    return Status(JavaExceptions::kInvalidRequestException, "Unable to send out data head.");
  }

  b = BlockingWrite(fd_, body_buf.data(), body_buf.size(), deadline);
  if (!b) {
    return Status(JavaExceptions::kInvalidRequestException, "Unable to send out data body.");
  }
  char ret_len[4];
  b = BlockingRecv(fd_, ret_len, 4, deadline);
  if (!b) {
    return Status(JavaExceptions::kInvalidRequestException, "Unable to readback data head.");
  }

  auto ret_length = platform::ReadBigEndian<uint32_t>(ret_len, 0);

  vector<char> ret_body;
  ret_body.resize(ret_length);
  b = BlockingRecv(fd_, ret_body.data(), ret_length, TimeUtil::GetNowEpochMsFor(millis));

  if (!b) {
    return Status(JavaExceptions::kInvalidRequestException, "Unable to read out data body.");
  }
  res->ParseFromArray(ret_body.data(), ret_body.size());

  return Status();
}


}  // namespace bridger
}  // namespace dancenn

