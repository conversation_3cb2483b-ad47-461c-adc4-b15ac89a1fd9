#pragma once

#include "ranger.pb.h"
#include "bridger/socket_client.h"

DECLARE_string(permission_model);
DECLARE_string(ranger_sock_path);
DECLARE_uint32(ranger_sock_timeout_millis);

namespace dancenn {
namespace bridger {

template <typename REQ, typename RES>
class BridgeWrapper {
 public:
  explicit BridgeWrapper(const SockAddr& sock_addr) : addr_(sock_addr) { };

  Status SendRequest(const REQ& req, RES* res, int millis) const {
    SocketClient client;
    // TODO(wangning.ito): we may reuse connection.
    RETURN_NOT_OK(ConnectToServer(addr_, &client));
    return client.SendMessage(req, res, millis);
  };

 private:
  SockAddr addr_;
};

class RangerBridger {
public:

  explicit RangerBridger(const SockAddr& sock_addr): bridger_(sock_addr) {} ;

  Status SendRequest(const cloudfs::RangerRequestListPB& req,
                     cloudfs::RangerResponseListPB* res) const {
    return bridger_.SendRequest(req, res, FLAGS_ranger_sock_timeout_millis);
  }

private:
  BridgeWrapper<cloudfs::RangerRequestListPB, cloudfs::RangerResponseListPB> bridger_;
};

}  // namespace bridger
}  // namespace dancenn
