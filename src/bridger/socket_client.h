#pragma once

#include <string>

#include <sys/socket.h>
#include <sys/un.h>
#include <netinet/in.h>
#include <sys/time.h>

#include "google/protobuf/message.h"
#include "base/status.h"

#define RETRY_ON_EINTR(err, expr) do { \
  static_assert(std::is_signed<decltype(err)>::value, \
                #err " must be a signed integer"); \
  (err) = (expr); \
} while ((err) == -1 && errno == EINTR)

/*
 * A bridger to talk to java client.
 * AF_UNIX/AF_INET via SOCKET_STREAM, deploy as side car in pod.
 *
 * */
namespace dancenn {
namespace bridger {

inline std::string ErrnoToString(int err) {
  char buf[512];
  int buf_len = sizeof(buf);
  // Using POSIX version 'int strerror_r(...)'.
  char* ret = strerror_r(err, buf, buf_len);
  if (ret != buf) {
    strncpy(buf, "unknown error", buf_len);
    buf[buf_len - 1] = '\0';
  }
  return std::string(buf) + "; errno=" + std::to_string(err);
}

class SockAddr {
 public:

  // AF_UNIX
  Status ParseFromUnixPath(const std::string& path);

  // AF_INET
  Status ParseFromHostPort(const std::string& host, int port);

  const struct sockaddr* addr() const {
   return reinterpret_cast<const sockaddr*>(&storage_);
  }

  sa_family_t family() const {
    return storage_.share.ss_family;
  }

  bool is_ip() const {
    return family() == AF_INET;
  }

  bool is_unix() const {
    return family() == AF_UNIX;
  }

  socklen_t addrlen() const {
    if (is_ip()) {
      return sizeof(struct sockaddr_in);
    }
    return len_;
  }

  bool initialized() {
    return len_ != 0;
  }

 private:

  socklen_t len_ = 0;

  union {
    sockaddr_storage share;
    sockaddr_in in;
    sockaddr_un un;
  } storage_;
};

/*
 * For server. Init -> BindAndListen.
 * For client, Init -> Connect.
 * Keep a fd for write purpose.
 * */
class SocketClient {
 public:
  explicit SocketClient();

  ~SocketClient();

  Status Init(int family, int flags = 1);

  Status Connect(const SockAddr& sockaddr);

  bool InitOnce();

  Status SendAndRead();

  Status SendMessage(const google::protobuf::Message& m,
                     google::protobuf::Message* res,
                     uint32_t millis = 200) const;

  Status SetSendTimeout(int64_t millis) {
    return SetTimeout(SO_SNDTIMEO, "SO_SNDTIMEO", millis);
  }

  Status SetRecvTimeout(int64_t millis) {
    return SetTimeout(SO_RCVTIMEO, "SO_RCVTIMEO", millis);
  }

  Status SetTimeout(int opt, const char* optname, int64_t millis) {
    struct timeval tv;
    tv.tv_usec = 1000 * millis;

    if (::setsockopt(fd_, SOL_SOCKET, opt, &tv, sizeof(tv)) == -1) {
      return Status(JavaExceptions::kIOException, "Unable to set" + std::string(optname));
    }

    return Status::OK();
  }

 private:

  void Close();

  int fd_;
};

Status ConnectToServer(const SockAddr& addr, SocketClient* client);

}  // namespace bridger
}  // namespace dancenn
