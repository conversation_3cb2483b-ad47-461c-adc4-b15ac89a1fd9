// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCKS_SCANNER_LISTENER_H_
#define BLOCK_MANAGER_BLOCKS_SCANNER_LISTENER_H_

#include <string>
#include <chrono>

#include "namespace/meta_scanner.h"

namespace dancenn {

class BlockManager;

class BlocksScannerListener : public MetaScanner::Listener {
 public:
  explicit BlocksScannerListener(BlockManager* bm)
      : MetaScanner::Listener(desc()),
        bm_(bm) {
  }

  bool PreScan() override;
  std::vector<INodeID> ScanIndexes() override;
  bool Handle(const std::string& full_path, const INode& inode) override;
  void PostScan() override;

 private:
  static const std::string desc() {
    return "blocks_scanner_listener";
  }

  BlockManager* bm_{nullptr};
  std::chrono::steady_clock::time_point scan_start_;
  int64_t num_invalid_ {0};
  int64_t num_under_replicated_ {0};
  int64_t num_over_replicated_ {0};
  int64_t num_postponed_ {0};
  int64_t num_corrupt_ {0};
  int64_t num_under_construction_ {0};
  int64_t processed_ {0};
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCKS_SCANNER_LISTENER_H_

