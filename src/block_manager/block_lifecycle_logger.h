#ifndef BLOCK_LIFECYCLE_LOGGER_H
#define BLOCK_LIFECYCLE_LOGGER_H

#include <cnetpp/concurrency/thread_pool.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <iostream>
#include <shared_mutex>
#include <unordered_map>
#include <vector>

#include "base/databus.h"
#include "base/stop_watch.h"
#include "block_manager/block_lifecycle_map_slice.h"
#include "hdfs.pb.h"

DECLARE_int32(flush_blocklifecycle_interval_ms);
DECLARE_bool(block_lifecycle_enable);
DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_string(nn_local_ip);
namespace dancenn {

using cloudfs::ReplicaStateProto;
using BlockID = uint64_t;

class BlockLifecycleLogger {
 public:
  BlockLifecycleLogger();
  ~BlockLifecycleLogger();

  std::unique_ptr<BlockLifeCycleMapSlice>& slice(BlockID blk_id);

  void LogTransferCommand(uint64_t blk_id,
                          uint64_t gs,
                          std::string& src_dn,
                          std::string& dst_dn);

  void LogIncrementalBlockReport(uint64_t blk_id,
                                 const std::string& dn,
                                 uint64_t gs,
                                 const std::string& op,
                                 uint32_t numbytes,
                                 ReplicaStateProto replica_status);

  void SendLogToDatabus();

  static BlockLifecycleLogger& GetSingleton();

  void EnqueueDNData(const char* fmt, ...);

 private:
  class SendLogTask : public cnetpp::concurrency::Task {
   public:
    explicit SendLogTask(BlockLifecycleLogger* logger) : logger_(logger) {
    }
    virtual ~SendLogTask() {
    }

    bool operator()(void* args = nullptr) override;

   private:
    BlockLifecycleLogger* logger_;
  };

  MetricID abandon_lifecycle_log_count_;
  MetricID enqueue_log_time_;

  size_t num_slices_;
  size_t slice_mask_;
  std::vector<std::unique_ptr<BlockLifeCycleMapSlice>> slices_;
  std::unique_ptr<cnetpp::concurrency::ThreadPool> log_sender_;
  std::shared_ptr<DatabusChannel> blk_channel_;

  std::vector<std::string> dn_records_to_send_;
  mutable std::shared_timed_mutex dn_channel_lock_;
  std::shared_ptr<DatabusChannel> dn_channel_;
};

#define DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(blk_id, fmt, ...)                    \
  if (is_active_ && FLAGS_block_lifecycle_enable) {                            \
    StopWatch sw(metrics_.enqueue_blk_log_time_);                              \
    sw.Start();                                                                \
                                                                               \
    auto& s = BlockLifecycleLogger::GetSingleton().slice(blk_id);              \
    std::unique_lock<BlockLifeCycleMapSlice> guard(*s);                        \
    s->EnqueueLog(("{\"collect_time\":%" PRIu64 ", \"cfs_region\":\"%s\" "     \
                   ", \"cfs_env\": \"%s\", \"cfs_cluster\": \"%s\""            \
                   ", \"filesystem_id\":%" PRIu64                              \
                   ", \"namespace_id\":%" PRIu64 ", \"nn_ip\": \"%s\", " fmt), \
                  std::chrono::duration_cast<std::chrono::milliseconds>(       \
                      std::chrono::system_clock::now().time_since_epoch())     \
                      .count(),                                                \
                  FLAGS_cfs_region.c_str(),                                    \
                  FLAGS_cfs_env.c_str(),                                       \
                  FLAGS_cfs_cluster.c_str(),                                   \
                  FLAGS_filesystem_id,                                         \
                  FLAGS_namespace_id,                                          \
                  FLAGS_nn_local_ip.c_str(),                                   \
                  ##__VA_ARGS__);                                              \
                                                                               \
    sw.NextStep();                                                             \
    MFH(metrics_.enqueue_blk_log_time_)                                        \
        ->Update(                                                              \
            std::chrono::duration_cast<std::chrono::nanoseconds>(sw.GetTime()) \
                .count());                                                     \
  }

#define DANCENN_BLOCKLIFECYCLE_DN_LOG(fmt, ...)                       \
  if (is_active_ && FLAGS_block_lifecycle_enable) {                   \
    BlockLifecycleLogger::GetSingleton().EnqueueDNData(               \
        ("{\"collect_time\":%" PRIu64 ", \"cfs_region\":\"%s\" "      \
         ", \"cfs_env\": \"%s\", \"cfs_cluster\": \"%s\""             \
         ", \"filesystem_id\":%" PRIu64 ", \"namespace_id\":%" PRIu64 \
         ", \"nn_ip\": \"%s\", " fmt),                                \
        std::chrono::duration_cast<std::chrono::milliseconds>(        \
            std::chrono::system_clock::now().time_since_epoch())      \
            .count(),                                                 \
        FLAGS_cfs_region.c_str(),                                     \
        FLAGS_cfs_env.c_str(),                                        \
        FLAGS_cfs_cluster.c_str(),                                    \
        FLAGS_filesystem_id,                                          \
        FLAGS_namespace_id,                                           \
        FLAGS_nn_local_ip.c_str(),                                    \
        ##__VA_ARGS__);                                               \
  }
}  // namespace dancenn

#endif  // BLOCK_LIFECYCLE_LOGGER_H
