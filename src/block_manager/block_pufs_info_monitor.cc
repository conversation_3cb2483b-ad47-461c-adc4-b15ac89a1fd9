// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/22
// Description

#include "block_manager/block_pufs_info_monitor.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(block_pufs_info_monitor_interval_ms);

#define RETURN_IF_HA_IN_TRANSITION()    \
  if (bm_->ha_state_->InTransition()) { \
    return;                             \
  }

namespace dancenn {

// MonitorDeprecatingTask
BlockPufsInfoMonitor::MonitorDeprecatingTask::MonitorDeprecatingTask(
    BlockPufsInfoMonitor* monitor)
    : monitor_(monitor) {
}

bool BlockPufsInfoMonitor::MonitorDeprecatingTask::operator()(void* arg) {
  while (!stop_) {
    monitor_->DoDeprecating();

    int interval = FLAGS_block_pufs_info_monitor_interval_ms;
    VLOG(15) << "[MonitorDeprecatingTask] Block pufs info monitor will sleep: "
             << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
  }
  return true;
}

void BlockPufsInfoMonitor::MonitorDeprecatingTask::Stop() {
  std::unique_lock<std::mutex> lock(mu_);
  stop_ = true;
  cond_.notify_all();
}

// MonitorDeprecatedTask
BlockPufsInfoMonitor::MonitorDeprecatedTask::MonitorDeprecatedTask(
    BlockPufsInfoMonitor* monitor)
    : monitor_(monitor) {
}

bool BlockPufsInfoMonitor::MonitorDeprecatedTask::operator()(void* arg) {
  while (!stop_) {
    monitor_->DoDeprecated();

    int interval = FLAGS_block_pufs_info_monitor_interval_ms;
    VLOG(15) << "[MonitorDeprecatedTask] Block pufs info monitor will sleep: "
             << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
  }
  return true;
}

void BlockPufsInfoMonitor::MonitorDeprecatedTask::Stop() {
  std::unique_lock<std::mutex> lock(mu_);
  stop_ = true;
  cond_.notify_all();
}

BlockPufsInfoMonitor::BlockPufsInfoMonitor(BlockManager* bm)
    : bm_(bm) {
}

BlockPufsInfoMonitor::~BlockPufsInfoMonitor() {
  Stop();
}

void BlockPufsInfoMonitor::Start() {
  CHECK(!deprecating_worker_.get());
  deprecating_worker_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::shared_ptr<cnetpp::concurrency::Task>(
          new MonitorDeprecatingTask(this)),
      "MonitorDeprecating");
  deprecating_worker_->Start();

  CHECK(!deprecated_worker_.get());
  deprecated_worker_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::shared_ptr<cnetpp::concurrency::Task>(
          new MonitorDeprecatedTask(this)),
      "MonitorDeprecated");
  deprecated_worker_->Start();
}

void BlockPufsInfoMonitor::Stop() {
  if (deprecating_worker_) {
    deprecating_worker_->Stop();
    deprecating_worker_.reset();
  }
  if (deprecated_worker_) {
    deprecated_worker_->Stop();
    deprecated_worker_.reset();
  }
}

void BlockPufsInfoMonitor::DoDeprecating() {
  RETURN_IF_HA_IN_TRANSITION();
  VLOG(15) << "[DoDeprecating] Start to do some block pufs info works.";
  auto res = bm_->ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (res.first.HasException()) {
    VLOG(15) << "Skip due to standby mode.";
    return;
  }
  RETURN_IF_HA_IN_TRANSITION();

  // NOTICE: We should recycle deprecating blocks even in safemode.
  // Otherwise, deadlock may happen between deprecating blocks recycler and
  // safemode checker.
  // Assume here are 100 deprecating blocks and no other blocks.
  // Safemode checker: I am waiting recycler clean deprecating blocks,
  //                   then I can exit safemode.
  // Recycler: I am waiting safemode checker exits, then I can recycle blocks.
  auto start = std::chrono::steady_clock::now();
  bm_->HandleDeprecatingBlocks();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::steady_clock::now() - start)
                      .count();
  LOG_IF(INFO, VLOG_IS_ON(15) || duration > 1000)
      << "[DoDeprecating] Block pufs info monitor time: " << duration << "(ms)";
}

void BlockPufsInfoMonitor::DoDeprecated() {
  RETURN_IF_HA_IN_TRANSITION();
  VLOG(15) << "[DoDeprecated] Start to do some block pufs info works.";
  auto res = bm_->ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (res.first.HasException()) {
    VLOG(15) << "Skip due to standby mode.";
    return;
  }
  RETURN_IF_HA_IN_TRANSITION();

  if (bm_->safemode_->IsOn()) {
    VLOG(15) << "Skip due to safemode is on.";
    return;
  }
  RETURN_IF_HA_IN_TRANSITION();
  auto start = std::chrono::steady_clock::now();
  bm_->HandleDeprecatedBlocks();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::steady_clock::now() - start)
                      .count();
  LOG_IF(INFO, VLOG_IS_ON(15) || duration > 1000)
      << "[DoDeprecated] Block pufs info monitor time: " << duration << "(ms)";
}

}  // namespace dancenn
