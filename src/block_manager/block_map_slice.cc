// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/block_map_slice.h"

#include <glog/logging.h>

#include <algorithm>
#include <cassert>
#include <chrono>
#include <cmath>
#include <cstdint>
#include <cstring>
#include <deque>
#include <random>
#include <shared_mutex>
#include <utility>

#include "base/constants.h"
#include "base/file_utils.h"
#include "base/hash.h"
#include "base/logger_metrics.h"
#include "base/stop_watch.h"
#include "block_info.h"
#include "block_manager/block_info_proto.h"
#include "block_manager/block_pufs_info.h"
#include "block_map_slice.h"
#include "datanode_manager/datanode_manager.h"
#include "inode.pb.h"  // NOLINT
#include "namespace/namespace.h"

DECLARE_int32(blockmap_num_slice);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_int32(blockmap_replication_work_multiplier);
DECLARE_int32(blockmap_max_replication_streams);
DECLARE_int32(blockmap_max_replication_streams_hard_limit);
DECLARE_int32(blockmap_invalidate_limit_per_slice);
DECLARE_int32(blockmap_invalidate_limit_per_dn);
DECLARE_int32(blockmap_truncatable_limit_per_slice);
DECLARE_int32(blockmap_truncatable_limit_per_dn);
DECLARE_int32(blockmap_sealed_limit_per_slice);
DECLARE_double(blockmap_invalidate_work_pct);
DECLARE_int32(dfs_replication_pending_timeout_ms);
DECLARE_double(blockmap_replication_chose_loocal_dc_pct);
DECLARE_int32(blockmap_num_postponed_blocks_rescan);
DECLARE_bool(clear_storage_when_convert_to_uc);
DECLARE_int32(acquire_block_map_slice_lock_slow_threshold_ms);
DECLARE_bool(load_local_block_to_blockmap);
DECLARE_bool(load_persisted_block_to_blockmap);
DECLARE_bool(enable_storage_class);
DECLARE_bool(block_ignore_not_exist_fatal);
DECLARE_bool(log_block_map_detail);
DECLARE_bool(misinvalidated_block_enable_record);
DECLARE_bool(check_loose_mode);

namespace dancenn {

const static size_t BEST_BUCKETS[] =
    {49921, 32771, 5519, 1447, 233, 23, 7, 3, 0};
inline static size_t ChooseBestBuckets(size_t n) {
  size_t x = BEST_BUCKETS[0];
  for (int i = 0; BEST_BUCKETS[i] != 0 && n <= BEST_BUCKETS[i]; i++) {
    x = BEST_BUCKETS[i];
  }
  return x;
}

BlockInfoGuard::BlockInfoGuard(BlockMapSlice* slice,
                               BlockID block_id,
                               bool load_to_memory) {
  Locate(slice, block_id, 0, load_to_memory);
}

BlockInfoGuard::BlockInfoGuard(BlockMapSlice* slice,
                               BlockID block_id,
                               uint8_t replica_num,
                               bool load_to_memory) {
  Locate(slice, block_id, replica_num, load_to_memory);
}

BlockInfoGuard::~BlockInfoGuard() {
  if (need_delete_) {
    std::free(element_);
  }
}

BlockInfo* BlockInfoGuard::GetBlockInfo() {
  return element_;
}

void BlockInfoGuard::Locate(BlockMapSlice* slice,
                            BlockID block_id,
                            uint8_t replica_num,
                            bool load_to_memory) {
  if (FLAGS_load_persisted_block_to_blockmap) {
    element_ = slice->Locate(block_id, replica_num, true, &need_delete_);
  } else {
    element_ =
        slice->Locate(block_id, replica_num, load_to_memory, &need_delete_);
  }
}

BlockMapSlice::BlockMapSlice(uint32_t slice_id, BlockManagerMetrics* metrics)
    : slice_id_(slice_id),
      metrics_(metrics),
      unique_lock_acquire_ts_in_ms_(0),
      last_access_blk_id_(kInvalidBlockID),
      access_meta_storage_(false),
      lock_holder_file_(nullptr),
      lock_holder_line_number_(0),
      pending_replications_(
          std::chrono::milliseconds(FLAGS_dfs_replication_pending_timeout_ms)),
      num_buckets_(ChooseBestBuckets(FLAGS_blockmap_num_bucket_each_slice)) {
  CHECK_NOTNULL(metrics_);
  buckets_ = reinterpret_cast<BlockInfo**>(
      std::malloc(sizeof(BlockInfo*) * num_buckets_));
  std::memset(buckets_, 0, sizeof(BlockInfo*) * num_buckets_);
}

BlockMapSlice::~BlockMapSlice() {
  // Consciously leave memory leak here to fasten shutdown speed, as it only
  // happens when process exits.
#if 0
  for (size_t i = 0; i < num_buckets_; i++) {
    BlockInfo* curr = buckets_[i];
    while (curr) {
      BlockInfo* next = curr->next();
      std::free(curr);
      curr = next;
    }
  }
  std::free(buckets_);
#endif
}

void BlockMapSlice::lock() {
  using namespace std::chrono;  // NOLINT(build/namespaces)
  auto threshold_ms = FLAGS_acquire_block_map_slice_lock_slow_threshold_ms;
  int64_t start = 0;
  if (threshold_ms != 0) {
    start = duration_cast<milliseconds>(system_clock::now().time_since_epoch())
                .count();
  }

  rwlock_.lock();

  if (threshold_ms != 0) {
    auto now =
        duration_cast<milliseconds>(system_clock::now().time_since_epoch())
            .count();
    auto cost_ms = now - start;
    if (cost_ms >= threshold_ms) {
      LOG(WARNING) << "Acquire block map slice costs " << cost_ms << "ms"
                   << ", this: " << this
                   << ", last access block: " << last_access_blk_id_
                   << ", access meta storage: " << access_meta_storage_
                   << ", lock_holder: " << (lock_holder_file_ ? lock_holder_file_ : "")
                   << ", line number: " << lock_holder_line_number_;
    }
    unique_lock_acquire_ts_in_ms_ = now;
  }
}

void BlockMapSlice::unlock() {
  using namespace std::chrono;  // NOLINT(build/namespaces)
  auto threshold_ms = FLAGS_acquire_block_map_slice_lock_slow_threshold_ms;
  if (threshold_ms != 0 && unique_lock_acquire_ts_in_ms_ != 0) {
    auto now =
        duration_cast<milliseconds>(system_clock::now().time_since_epoch())
            .count();
    auto dur_ms = now - unique_lock_acquire_ts_in_ms_;
    if (dur_ms >= threshold_ms) {
      LOG(WARNING) << "Hold block map slice for " << dur_ms << "ms"
                   << ", this: " << this
                   << ", last access block: " << last_access_blk_id_
                   << ", access meta storage: " << access_meta_storage_
                   << ", lock_holder: " << (lock_holder_file_ ? lock_holder_file_ : "")
                   << ", line number: " << lock_holder_line_number_;
    }
  }
  unique_lock_acquire_ts_in_ms_ = 0;
  access_meta_storage_ = false;

  rwlock_.unlock();
}

void BlockMapSlice::TellLockHolder(const char* file, int lineno) {
  lock_holder_file_ = file;
  lock_holder_line_number_ = lineno;
}

void BlockMapSlice::SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage) {
  meta_storage_ = meta_storage;
}

void BlockMapSlice::SetDatanodeManager(
    std::shared_ptr<DatanodeManager> datanode_manager) {
  datanode_manager_ = datanode_manager;
}

BlockInfo* BlockMapSlice::Locate(BlockID blk_id) {
  bool ignored;
  return Locate(blk_id, 0, true, &ignored);
}

BlockInfo* BlockMapSlice::Locate(BlockID blk_id,
                                 uint8_t replica_num,
                                 bool load_to_memory,
                                 bool* need_delete) {
  last_access_blk_id_ = blk_id;
  size_t bucket = BlockIdBucket(blk_id);
  BlockInfo* curr = buckets_[bucket];
  while (curr) {
    if (curr->id() == blk_id) {
      break;
    }
    curr = curr->next();
  }

  // TODO(ruanjunbin): Test unload from memory and load from meta storage
  // keep BlockInfo unchanged.
  if (curr != nullptr) {
    *need_delete = false;
  } else {
    if (meta_storage_ == nullptr) {
      return nullptr;
    }
    BlockInfoProto bip;
    if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
      return nullptr;
    }
    if (bip.state() == BlockInfoProto::kDeprecated) {
      return nullptr;
    }

    access_meta_storage_ = true;

    if (bip.state() != BlockInfoProto::kPersisted) {
      if (FLAGS_load_local_block_to_blockmap) {
        // Theoretically, we shouldn't load any block whose state !=
        // kPersisted. Because local blocks are always kept in memory. But
        // just in case.
        LOG_WITH_LEVEL(ERROR)
            << "LoadUnexpectedBlock " << PBConverter::ToCompactJsonString(bip);
        MFC(LoggerMetrics::Instance().error_)->Inc();
      }
      *need_delete = false;
    } else {
      if (load_to_memory) {
        *need_delete = false;
      } else {
        *need_delete = true;
      }
    }

    BlockUCState state = BlockUCState::kComplete;
    switch (bip.state()) {
      case BlockInfoProto::kUnderConstruction: {
        auto uc = std::make_shared<UnderConstructionState>();
        if (!uc_states_.insert(std::make_pair(blk_id, uc)).second) {
          LOG_WITH_LEVEL(ERROR) << "Add under constructed " << blk_id
                                << " found legacy entry in uc_states_";
          MFC(LoggerMetrics::Instance().error_)->Inc();
        }
        state = BlockUCState::kUnderConstruction;
      } break;
      case BlockInfoProto::kUnderRecovery: {
        state = BlockUCState::kUnderRecovery;
      } break;
      case BlockInfoProto::kCommitted: {
        state = BlockUCState::kCommitted;
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued: {
        state = BlockUCState::kComplete;
      } break;
      case BlockInfoProto::kPersisted: {
        state = BlockUCState::kPersisted;
      } break;
      case BlockInfoProto::kDeprecated:
      default: {
        LOG(FATAL) << blk_id;
      } break;
    }

    uint8_t capacity = FLAGS_dfs_replication;
    std::vector<std::string> dn_uuids;
    std::vector<StorageClassReportProto> reports;
    if (FLAGS_enable_storage_class) {
      meta_storage_->GetStorageClassReports(blk_id, &dn_uuids, &reports);
    } else {
      // no storage class
    }
    if (dn_uuids.size() > capacity) {
      capacity = dn_uuids.size();
    }

    BlockInfo* element = AllocateElement(capacity);
    element->Init(bip.inode_id(),
                  kInvalidINodeId,
                  Block(bip.block_id(), bip.num_bytes(), bip.gen_stamp()),
                  capacity,
                  bip.write_mode(),
                  state);

    if (replica_num == 0) {
      element->set_expected_rep(bip.expected_rep());
    } else {
      element->set_expected_rep(replica_num);
    }

    for (auto& uuid: dn_uuids) {
      DatanodeID dnid = datanode_manager_->GetDatanodeInterId(uuid);
      if (!element->AddStorage(dnid)) {
        LOG(ERROR) << "Failed to add storage for block " << element->ToString()
                   << " new dn " << dnid;
      }
    }

    if (VLOG_IS_ON(14)) {
      std::stringstream ss;
      for (auto& uuid: dn_uuids) {
        DatanodeID dnid = datanode_manager_->GetDatanodeInterId(uuid);
        ss << dnid << " ";
      }
      LOG(INFO) << "Locate block info from meta storage " << bip.block_id()
                << " " << ss.str();
    }

    if (*need_delete == false) {
      // Slice will keep track of this element
      element->SetNext(buckets_[bucket]);
      buckets_[bucket] = element;
    }
    curr = element;
  }

  VLOG(14) << "Slice locate result: " << curr->ToString();

  return curr;
}

size_t BlockMapSlice::BlockIdBucket(BlockID blk_id) const {
  return blk_id % num_buckets_;
}

BlockInfo* BlockMapSlice::AllocateElement(uint8_t capacity) {
  if (capacity < 1) {
    capacity = 1;
  }
  size_t size = sizeof(BlockInfo) + sizeof(DatanodeID) * (capacity);
  BlockInfo* element = reinterpret_cast<BlockInfo*>(std::malloc(size));
  std::memset(element, 0, size);
  element->set_capacity(capacity);
  return element;
}

BlockInfo* BlockMapSlice::CopyElement(BlockInfo* orig, uint8_t capacity) {
  if (capacity < 1) {
    capacity = 1;
  }
  size_t orig_size =
      sizeof(BlockInfo) + sizeof(DatanodeID) * (orig->capacity());
  BlockInfo* element = AllocateElement(capacity);
  std::memcpy(element, orig, orig_size);
  element->set_capacity(capacity);
  return element;
}

void BlockMapSlice::AddBlock(uint64_t inode_id,
                             uint64_t parent_id,
                             const Block& blk,
                             uint8_t capacity,
                             cloudfs::IoMode write_mode,
                             const std::vector<DatanodeID>& dns,
                             BlockUCState state) {
  CHECK(!(write_mode == cloudfs::TOS_BLOCK && !dns.empty())) << blk;

  BlockInfo* element = Locate(blk.id);
  if (element) {
    VLOG(10) << "AddBlock already exist" << " B" << blk.id;
    return;
  }

  StopWatch sw(metrics_->add_block_allocate_element_time_);
  sw.Start();
  element = AllocateElement(capacity);
  element->Init(inode_id, parent_id, blk, capacity, write_mode, state);
  VLOG(10) << "AllocateElement" << " B" << blk.id;

  sw.NextStep(metrics_->add_block_insert_into_bucket_time_);

  size_t bucket = BlockIdBucket(blk.id);
  element->SetNext(buckets_[bucket]);
  buckets_[bucket] = element;
  VLOG(10) << "Insert into Bucket" << " B" << blk.id;

  if (HasBeenComplete(state)) {
    sw.NextStep(metrics_->add_block_add_storage_time_);
    for (const auto& dn : dns) {
      element = AddStorage(element, dn);
    }
    VLOG(10) << "add block " << " B" << blk.id << " complete";
  } else {
    sw.NextStep(metrics_->add_block_insert_uc_state_time_);
    auto uc = std::make_shared<UnderConstructionState>(blk, dns);
    VLOG(10) << "add block B" << blk.id << " with " << dns.size()
             << " uc storages";
    if (!uc_states_.insert(std::make_pair(blk.id, uc)).second) {
      LOG(FATAL) << "Add under constructed " << blk
                 << " found legacy entry in uc_states_";
    }
  }
  VLOG(10) << "add block B" << blk.id << " finish ";
}

void BlockMapSlice::LoadBlock(uint64_t inode_id,
                              uint64_t parent_id,
                              const Block& blk,
                              uint8_t capacity,
                              cloudfs::IoMode write_mode,
                              const std::vector<DatanodeID>& dns,
                              BlockUCState state) {
  BlockInfoProto bip;
  if (HasBeenComplete(state) && meta_storage_ &&
      meta_storage_->GetBlockInfo(blk.id, &bip)) {
    BlockInfo bi;
    bi.Init(inode_id, parent_id, blk, 0, write_mode, BlockUCState::kPersisted);
    if (bi.IsSafeToRelease(bip)) {
      // Don't load all block info at once.
      return;
    }
  }
  if (bip.has_state() && bip.state() == BlockInfoProto::kPersisted) {
    MFC(LoggerMetrics::Instance().error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << "Load persisted B" << blk.id;
  }

  BlockInfo* element = AllocateElement(capacity);
  // state can't be kPersisted because IsSafeToRelease returns false.
  element->Init(inode_id, parent_id, blk, capacity, write_mode, state);
  auto bucket = BlockIdBucket(blk.id);
  element->SetNext(buckets_[bucket]);
  buckets_[bucket] = element;

  if (!HasBeenComplete(state)) {
    DLOG(INFO) << "Load block B" << blk.id << " with " << (int)element->size()
               << " uc storages";
    auto uc = std::make_shared<UnderConstructionState>(blk, dns);
    VLOG(10) << "load block B" << blk.id << " with " << dns.size()
             << " uc storages";
    if (!uc_states_.insert(std::make_pair(blk.id, uc)).second) {
      if (FLAGS_check_loose_mode) {
        LOG(ERROR) << "[LooseMode]" << "Add under constructed " << blk
                   << " found legacy entry in uc_states_";
      } else {
        LOG(FATAL) << "Add under constructed " << blk
                   << " found legacy entry in uc_states_";
      }
    }
  }
}

bool BlockMapSlice::RemoveBlock(BlockID blk_id) {
  size_t bucket = BlockIdBucket(blk_id);
  BlockInfo* curr = buckets_[bucket];
  if (!curr) {
    return false;
  }

  if (curr->id() == blk_id) {
    buckets_[bucket] = curr->next();
  } else {
    bool found = false;
    while (curr->next()) {
      if (curr->next()->id() == blk_id) {
        BlockInfo* element = curr->next();
        curr->SetNext(curr->next()->next());
        curr = element;
        found = true;
        break;
      }
      curr = curr->next();
    }
    if (!found) {
      return false;
    }
  }
  CHECK_NOTNULL(curr);
  if (curr->GetInTransaction()) {
    LOG(ERROR) << "Remove in transaction block B" << blk_id;
  }
  std::free(curr);
  uc_states_.erase(blk_id);
  VLOG(10) << "Remove block B" << blk_id;
  return true;
}

std::vector<DatanodeID> BlockMapSlice::GetBlockStorages(BlockID blk_id) {
  BlockInfoGuard bi_guard(this, blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    if (FLAGS_block_ignore_not_exist_fatal) {
      LOG(ERROR) << "GetBlockStorages for B" << blk_id << " but not found";
    } else {
      LOG(FATAL) << "GetBlockStorages for B" << blk_id << " but not found";
    };
    return {};
  }
  std::vector<DatanodeID> res;
  if (bi->HasBeenComplete()) {
    for (size_t i = 0; i < bi->size(); ++i) {
      res.emplace_back(bi->storage_id(i));
    }
  } else {
    res = GetExpectedLocations(blk_id);
  }
  std::random_device rd;
  std::mt19937 re(rd());
  std::shuffle(res.begin(), res.end(), re);
  return res;
}

std::vector<DatanodeID> BlockMapSlice::GetBlockStoragesSafe(BlockID blk_id) {
  BlockInfoGuard bi_guard(this, blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return {};
  }
  std::vector<DatanodeID> res;
  if (bi->HasBeenComplete()) {
    for (size_t i = 0; i < bi->size(); ++i) {
      res.emplace_back(bi->storage_id(i));
    }
  } else {
    res = GetExpectedLocations(blk_id);
  }
  return res;
}

std::vector<DatanodeID> BlockMapSlice::GetExpectedLocations(BlockID blk_id) {
  auto uc = GetUcInternal(blk_id);
  CHECK(uc != nullptr) << "Cannot find uc for B" << blk_id;
  return uc->GetStorages();
}

std::shared_ptr<UnderConstructionState> BlockMapSlice::FilterInvalidReplica(
    BlockID blk_id,
    uint64_t gs) {
  auto uc = GetUcInternal(blk_id);
  CHECK(uc != nullptr) << "FilterInvalidReplica B" << blk_id
                       << " cannot be found in uc state";
  uc->FilterInvalidReplica(gs);
  return uc;
}

// NOTICE: slice write lock should be held
ReportedReplicaResult BlockMapSlice::ProcessReportedBlock(
    DatanodeID dn_id,
    const Block& reported_block,
    ReplicaStateProto state,
    bool is_active,
    bool dn_decommission,
    uint8_t live_replica) {
  BlockInfoGuard bi_guard(this, reported_block.id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    // for standby, bi is NULL in two cases:
    // 1. report an elderly block, who ws deleted long time ago.
    // 2. report a younger block, meanwhile the editlog tailer work is not
    //    ready yet, because 'OP_ADD_BLOCK' is fall behind
    //    the 'OP_SET_GENSTAMP_V2'.
    // to sum up, we need to deal with the block later.
    if (is_active) {
      AddToInvalidateBlock(dn_id, reported_block, "ProcessReportedBlock");
    }
    EraseCorruptBlock(reported_block.id);
    EraseSealedBlock(reported_block.id);
    return INVALIDATE;
  }

  if (CheckBlockCorrupt(
          bi, state, reported_block, dn_decommission, live_replica)) {
    bool already_exists;
    AddStorage(bi, dn_id, &already_exists);
    AddToCorruptBlock(reported_block.id, dn_id, reported_block);
    if (reported_block.gs == kBlockProtocolV2GenerationStamp) {
      // reported_len > store_len is UNFINALIZED Block
      AddToSealedBlock(reported_block.id, dn_id, reported_block);
    }
    return CORRUPT;
  }

  if (CheckUnderConstruction(bi, state)) {
    auto uc = GetUcInternal(reported_block.id);
    if (!uc) {
      LOG(FATAL) << "Can not find uc state for under constructed block "
                 << reported_block;
    }

    uc->AddReplica(dn_id, reported_block.gs, reported_block.num_bytes);
    if (state == ReplicaStateProto::FINALIZED) {
      // WHY?? replica will be added when block is complete
      return ADD;
    }
    if (state == cloudfs::ReplicaStateProto::SEALED) {
      // DN progress: sealed->IBR/FBR->truncatable&finalized->IBR->clear
      VLOG(10) << "bi->uc_state=" << BlockUCStateToString(bi->uc_state())
               << " state=" << ReplicaStateProto_Name(state)
               << " AddToSealedBlock";
      bool already_exists;
      AddStorage(bi, dn_id, &already_exists);
      AddToSealedBlock(reported_block.id, dn_id, reported_block);
      return NOTHING;
    }

    if (reported_block.gs == kBlockProtocolV2GenerationStamp) {
      // UC also need to add to UNFINALIZED block
      AddToSealedBlock(reported_block.id, dn_id, reported_block);
    }

    // HDFS original: Add block to 「storage block index」 when received
    // FINALIZED block report.

    // CFS update: Add block to 「storage block index」 once DN reported.
    // No matter the replica state is FINALIZED or RECEVING.
    // `return 4` when replica state is NOT `ReplicaStateProto::FINALIZED`
    // may do subtle damages to belows 3 usages:
    // 1. StorageInfo::RemoveBlocks
    //    => DatanodeInfo::RemoveBlocks
    //      => DatanodeManager::RemoveBlocks
    //        「no damages」
    // 2. StorageInfo::ExtractBlocks
    //    => DatanodeInfo::MarkStorageAsFailed
    //       「no damages」
    // 3. StorageInfo::GetBlocks
    //    => DatanodeInfo::GetBlocks
    //      => DatanodeManager::UpdateReplicatedBlock
    //         「This method is not used in CFS」
    //         see commit: acfcf76 "Add replication progress for
    //         decommission".
    VLOG(10) << "To add block with state " << state << " to dn storage";
    return ADD_TO_BLOCK_INDEX;
  }

  if (state == cloudfs::ReplicaStateProto::SEALED) {
    // DN progress: sealed->IBR/FBR->truncatable&finalized->IBR->clear
    VLOG(10) << "bi->uc_state=" << BlockUCStateToString(bi->uc_state())
             << " state=" << ReplicaStateProto_Name(state)
             << " AddToSealedBlock";
    bool already_exists;
    AddStorage(bi, dn_id, &already_exists);
    AddToSealedBlock(reported_block.id, dn_id, reported_block);
    return NOTHING;
  }

  if (state == ReplicaStateProto::FINALIZED) {
    return ADD;
  }
  return NOTHING;
}

BlockInfo* BlockMapSlice::AddStorage(BlockInfo* orig,
                                     DatanodeID storage_id,
                                     bool* already_exist) {
  // Reserve some place for excess replicas.
  if (static_cast<int>(orig->size()) >= FLAGS_dfs_replication_max * 2 - 1) {
    return orig;
  }
  if (orig->AddStorage(storage_id, already_exist)) {
    return orig;
  }

  // Do not AddStorage for persisted blocks, the BlockInfo might not be in
  // BlockMapSlice. We have replica info in StorageReportClass CF in
  // MetaStorage.
  {
    auto bucket = BlockIdBucket(orig->id());
    BlockInfo* curr = buckets_[bucket];
    do {
      if (curr == nullptr) {
        return orig;
      }
      if (curr->id() == orig->id()) {
        break;
      }
      curr = curr->next();
    } while (true);
  }

  uint8_t new_capacity =
      std::min(static_cast<int>(orig->capacity()) * 2,
               static_cast<int>(FLAGS_dfs_replication_max * 2 - 1));
  BlockInfo* element = CopyElement(orig, new_capacity);
  if (!element->AddStorage(storage_id, already_exist)) {
    LOG(INFO) << "new_capacity=" << static_cast<int>(new_capacity);
    LOG(FATAL) << "Add Storage fail on newly allocated block B" << orig->id()
               << " storage_id=" << storage_id
               << " size=" << static_cast<int>(element->size())
               << " old_capacity=" << static_cast<int>(orig->capacity())
               << " new_capacity=" << static_cast<int>(element->capacity())
               << " FLAGS_dfs_replication_max=" << FLAGS_dfs_replication_max;
  }
  ReplaceElement(orig, element);
  return element;
}

BlockInfo* BlockMapSlice::AddStorages(BlockInfo* orig,
                                      const std::vector<DatanodeID>& dns) {
  auto element = orig;
  for (auto dn : dns) {
    element = AddStorage(element, dn);
  }
  return element;
}

//// slice read lock should already been held
bool BlockMapSlice::CheckBlockCorrupt(const BlockInfo* stored_block,
                                      ReplicaStateProto reported_state,
                                      const Block& reported_block,
                                      bool dn_decommission,
                                      uint8_t live_replica) {
  uint64_t report_gen_stamp = reported_block.gs;
  uint64_t report_size = reported_block.num_bytes;
  switch (reported_state) {
    case ReplicaStateProto::SEALED:
      if (report_gen_stamp != stored_block->gs()) {
        // SEALED state should not have gs
        // in Block Protocol V2, all block's gs == 0
        VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                << "stored state "
                << static_cast<int8_t>(stored_block->uc_state()) << " gen "
                << stored_block->gs() << ". reported state "
                << ReplicaStateProto_Name(reported_state) << " gen "
                << report_gen_stamp;
        return true;
      }

      // for sealed block, no enough length is considered corrupt
      if (report_size < stored_block->num_bytes()) {
        VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                << "stored state "
                << static_cast<uint32_t>(stored_block->uc_state()) << " size "
                << stored_block->num_bytes() << ". reported state "
                << ReplicaStateProto_Name(reported_state) << " FINALIZED size "
                << report_size;
        return true;
      }
      return false;
    case ReplicaStateProto::FINALIZED:
      switch (stored_block->uc_state()) {
        case BlockUCState::kPersisted:
        case BlockUCState::kComplete:
        case BlockUCState::kCommitted:
          if (report_gen_stamp != stored_block->gs()) {
            VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                    << "stored state "
                    << static_cast<int8_t>(stored_block->uc_state())
                    << " gen " << stored_block->gs()
                    << ". reported state "
                    << ReplicaStateProto_Name(reported_state)
                    << " gen " << report_gen_stamp;
            return true;
          }
          if (report_size != stored_block->num_bytes()) {
            VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                    << "stored state "
                    << static_cast<uint32_t>(stored_block->uc_state())
                    << " size " << stored_block->num_bytes()
                    << ". reported state "
                    << ReplicaStateProto_Name(reported_state)
                    << " FINALIZED size " << report_size;
            return true;
          }
          return false;
        case BlockUCState::kUnderConstruction:
          if (stored_block->gs() > report_gen_stamp) {
            VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                    << "stored state "
                    << static_cast<int8_t>(stored_block->uc_state())
                    << " gen " << stored_block->gs()
                    << ". reported state "
                    << ReplicaStateProto_Name(reported_state)
                    << " gen " << report_gen_stamp;
            return true;
          }
          return false;
        default:
          return false;
      }
    case ReplicaStateProto::RBW:
    case ReplicaStateProto::RWR:
      if (!stored_block->HasBeenComplete()) {
        // see hadoop3
        if (report_gen_stamp < stored_block->gs()) {
          VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                  << "stored state "
                  << static_cast<int8_t>(stored_block->uc_state()) << " gen "
                  << stored_block->gs() << ". reported state "
                  << ReplicaStateProto_Name(reported_state) << " gen "
                  << report_gen_stamp;
          return true;
        }

        // decommission as corrupt
        if (!dn_decommission) {
          return false;
        }
        auto uc = GetUcInternal(stored_block->id());
        if (!uc) {
          return false;
        }
        int safer_replica_num = 0;
        for (auto& kv : uc->expected_locations()) {
          if (report_gen_stamp < kv.second.gs &&
              report_size <= kv.second.nbytes) {
            safer_replica_num++;
          }
        }
        if (stored_block->expected_rep() <= safer_replica_num) {
          LOG(WARNING)
              << "Reported replica " << reported_block
              << " with state " << reported_state
              << " is identified as corrupt, since DN is in decommission.";
          return true;
        }
        return false;
      }
      if (report_gen_stamp != stored_block->gs()) {
        VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                << "stored state "
                << static_cast<int8_t>(stored_block->uc_state())
                << " gen " << stored_block->gs()
                << ". reported state "
                << ReplicaStateProto_Name(reported_state)
                << " gen " << report_gen_stamp;
        return true;
      }
      // Consider the following scenario:
      // 1. Client writes a block to DN4 and DN2, DN4 and DN2 both have a RBW
      //    replica which length >= 36315136 now.
      // 2. DN2 is busy now, so only DN4 sends ack to client.
      // 3. Client asks a new DN for pipeline recovery, NN gives it DN5.
      // 4. DN2 transfers replica to DN5, but DN5 is busy too, so it only
      //    receives 36130816 bytes, and state of this replica is RBW.
      //    And it doesn't send receving info to NN (I don't know why),
      //    so NN doesn't know DN5 has an incomplete replica.
      // 5. Client calls updateBlockForPipeline failed.
      // 6. Client calls completeFile, NN does nothing because the last block
      //    hasn't been committed.
      // 7. After 1 hour, NN let DN2 and DN4 do block recovery.
      // 8. After 6 hours, DN5 tells NN I have a RBW replica, but NN
      //    does nothing because block is persisted.
      // https://bytedance.feishu.cn/docs/doccntRKHkMRSrwr3A4d73nQUl6#
      if (stored_block->IsPersisted()) {
        if (NameSpace::IsAccMode()) {
          // In ACC mode, normal block is considered to be persisted only when file is persisted.
          INode inode;
          auto s = meta_storage_->GetINode(stored_block->inode_id(), &inode);
          if (s == kFileNotFound) {
            return true;
          } else if (s != kOK) {
            LOG(ERROR) << "Failed to get inode for block " << stored_block->ToString();
            return false;
          } else {
            if (inode.ufs_file_info().file_state() == kUfsFileStateToBePersisted) {
              VLOG(8) << "B" << stored_block->id() << " may not be persisted.";
              return false;
            } else if (live_replica < inode.replication()) {
              return false;
            } else {
              VLOG(8) << "B" << stored_block->id() << " is corrupted.";
              return true;
            }
          }
        } else {
          VLOG(8) << "B" << stored_block->id() << " is corrupted.";
          return true;
        }
      }
      // complete block, same generation stamp
      if (reported_state != ReplicaStateProto::RBW) {
        VLOG(8) << "B" << stored_block->id() << " is corrupted. "
                << "stored state "
                << static_cast<int8_t>(stored_block->uc_state())
                << ". reported state " << ReplicaStateProto_Name(reported_state);
        return true;
      }
      return false;
    case ReplicaStateProto::RUR:        // should not be reported
    case ReplicaStateProto::TEMPORARY:  // should not be reported
    default:
      // should not crash, just log an error message
      LOG(ERROR) << "B" << stored_block->id() << " unexpected state. "
                 << "stored state "
                 << static_cast<int8_t>(stored_block->uc_state())
                 << " reported state "
                 << ReplicaStateProto_Name(reported_state);
      return true;
  }
}

bool BlockMapSlice::CheckUnderConstruction(const BlockInfo* stored_block,
                                           ReplicaStateProto reported_state) {
  switch (reported_state) {
    case ReplicaStateProto::FINALIZED:
    case ReplicaStateProto::SEALED:
      switch (stored_block->uc_state()) {
        case BlockUCState::kUnderConstruction:
        case BlockUCState::kUnderRecovery:
          return true;
        default:
          return false;
      }
    case ReplicaStateProto::RBW:
    case ReplicaStateProto::RWR:
      return !stored_block->HasBeenComplete();
    case ReplicaStateProto::RUR:        // should not be reported
    case ReplicaStateProto::TEMPORARY:  // should not be reported
    default:
      LOG(ERROR) << "Unknown reported state "
                 << ReplicaStateProto_Name(reported_state);
      return false;
  }
}

// TODO(ruanjunbin): Please check every place calling GetUcInternal.
// Because we add a new state kPersisted in BlockInfo.
std::shared_ptr<UnderConstructionState> BlockMapSlice::GetUcInternal(
    BlockID block_id) {
  auto it = uc_states_.find(block_id);
  if (it == uc_states_.end()) {
    return nullptr;
  }
  return it->second;
}

void BlockMapSlice::ReplaceElement(BlockInfo* orig, BlockInfo* element) {
  CHECK_EQ(orig->id(), element->id());
  BlockID blk_id = orig->id();

  auto bucket = BlockIdBucket(blk_id);
  BlockInfo* curr = buckets_[bucket];
  CHECK_NOTNULL(curr);
  if (curr == orig) {
    buckets_[bucket] = element;
  } else {
    while (curr->next()) {
      if (curr->next() == orig) {
        curr->SetNext(element);
        break;
      }
      curr = curr->next();
    }
  }
  element->SetNext(orig->next());
  std::free(orig);
}

std::vector<DatanodeID> BlockMapSlice::storage_ids(BlockInfo* bi) {
  std::vector<DatanodeID> ids;
  ids.reserve(bi->size());
  for (size_t i = 0; i < bi->size(); ++i) {
    ids.emplace_back(bi->storage_id(i));
  }
  return ids;
}

bool BlockMapSlice::CommitBlock(BlockInfo* bi, const Block& commit_block) {
  if (bi->Commit(commit_block)) {
    // Should be filter out the out-dated replicas here. But since we do not
    // acquire the lock for the entire process of block report, we could be
    // add replica to block_info instead of un state. So the gen stamp here
    // may not be accurate. We will filter the out-dated replicas when we
    // complete this block;
    if (bi->gs() != kBlockProtocolV2GenerationStamp) {
      FilterInvalidReplica(commit_block.id, commit_block.gs);
    }
    return true;
  }
  return false;
}

bool BlockMapSlice::CompleteBlock(BlockInfo* orig,
                                  bool force,
                                  BlockUCState* prev_state) {
  if (prev_state != nullptr) {
    *prev_state = orig->uc_state();
  }

  // TODO(ruanjunbin): Reuse last block will break a lot of assumption.
  // Please be careful.
  // kUnderConstruction: Client uploads block to two datanodes.
  //                     Datanodes send ack to client after receive block.
  // kCommitted:         Client commits block.
  // kComplete:          Datanodes report block to NN and complete it.
  // kPersisted:         Datanode uploads block to under filesystem (TOS).
  if (orig->IsPersisted()) {
    uc_states_.erase(orig->id());
    return true;
  }

  auto uc = GetUcInternal(orig->id());
  if (uc == nullptr) {
    VLOG(12) << "try to complete B" << orig->id() << " with no uc";
    return false;
  }

  auto storage_num = orig->size();
  if (!force && storage_num < FLAGS_dfs_replication_min) {
    LOG(INFO) << "try to complete B" << orig->id() << " with too few replica "
              << storage_num;
    return false;
  }

  if (!force && orig->uc_state() != BlockUCState::kCommitted) {
    LOG(INFO) << "try to complete B" << orig->id() << " with wrong state "
              << static_cast<int>(orig->uc_state());
    return false;
  }

  uc_states_.erase(orig->id());
  orig->Complete();
  VLOG(10) << "Complete B" << orig->id();
  return true;
}

bool BlockMapSlice::PersistBlock(BlockInfo* bi,
                                 const std::string& blockpool_id,
                                 const Block& persist_block) {
  if (!bi->Persist(persist_block)) {
    return false;
  }
  auto uc = GetUcInternal(bi->id());
  if (uc) {
    for (const auto& kv : uc->expected_locations()) {
      DatanodeID dn_id = kv.first;
      auto gs = kv.second.gs;
      if (gs > persist_block.gs) {
        LOG(ERROR) << "DN" << dn_id << " has a block B" << persist_block.id
                   << "which gs (" << gs << ") is bigger than gs ("
                   << persist_block.gs << ") of pufs block.";
        continue;
      }
    }
  }
  uc_states_.erase(bi->id());
  VLOG(10) << "PersistBlock B" << bi->id();
  return true;
}

Status BlockMapSlice::CommitBlockSynchronization(
    const CommitBlockSynchronizationRequestProto& request) {
  BlockID blk_id = request.block().blockid();
  BlockInfo* bi = Locate(blk_id);
  if (!bi) {
    if (request.deleteblock()) {
      // This may be a retry attempt so ignore the failure
      // to locate the block.
      return Status();
    }
    LOG(INFO) << "Commit sync cannot find B" << blk_id;
    return Status(JavaExceptions::kIOException,
                  "Block " + std::to_string(blk_id) + " not found");
  }
  auto uc = GetUcInternal(blk_id);
  if (uc == nullptr || bi->HasBeenComplete()) {
    DLOG(INFO) << "Unexpected block sync B" << blk_id << " with recover_id "
               << request.newgenstamp();
    return Status();
  }

  if (request.newgenstamp() != uc->recovery_id()) {
    LOG(INFO) << "The recovery id " << request.newgenstamp()
              << " does not match current recovery id " << uc->recovery_id()
              << " for B" << blk_id;
    return Status(JavaExceptions::kIOException,
                  "The recovery id " + std::to_string(request.newgenstamp()) +
                      " does not match current recovery id " +
                      std::to_string(uc->recovery_id()) + " for block " +
                      std::to_string(blk_id));
  }

  if (request.deleteblock()) {
    // Do not process here, BlockManager will take care of this situation.
    // Since we need to clean up corrupt/invalidate queue as well.
  } else {
    bi->UpdateLength(request.newlength());
    bi->UpdateGs(request.newgenstamp());
    bi->set_uc_state(BlockUCState::kUnderConstruction);
  }
  return Status();
}

// remove all storages from bucket and insert them to the uc_state_
void BlockMapSlice::ConvertToUnderConstruction(BlockInfo* bi) {
  bi->set_uc_state(BlockUCState::kUnderConstruction);
  std::vector<DatanodeID> storages;
  for (size_t i = 0; i < bi->size(); ++i) {
    storages.emplace_back(bi->storage_id(i));
  }

  // https://bytedance.feishu.cn/docs/doccnm1R54KkSkiU7rFkdsnm6hf
  if (FLAGS_clear_storage_when_convert_to_uc) {
    bi->ClearStorages();
  }

  auto uc = std::make_shared<UnderConstructionState>(bi->blk(), storages);
  DLOG(INFO) << "Convert to UC B" << bi->id();
  if (!uc_states_.insert(std::make_pair(bi->id(), uc)).second) {
    LOG(FATAL) << "Add under constructed " << bi->id()
               << " found legacy entry in uc_states_";
  }
}

Status BlockMapSlice::UpdatePipeline(const ExtendedBlockProto& new_block,
                                     const std::vector<DatanodeID>& new_nodes) {
  auto blk_id = new_block.blockid();
  auto len = new_block.numbytes();
  auto gs = new_block.generationstamp();
  BlockInfo* bi = Locate(blk_id);
  if (!bi) {
    LOG(INFO) << "UpdatePipeline cannot find B" << blk_id;
    return Status(JavaExceptions::kIOException,
                  "Block " + std::to_string(blk_id) + " not found");
  }

  bi->UpdateGs(gs);
  bi->UpdateLength(len);
  auto uc = FilterInvalidReplica(blk_id, gs);
  for (auto node : new_nodes) {
    uc->AddReplica(node, gs, len);
  }
  return Status();
}

bool BlockMapSlice::GetRecoverCommand(BlockID blk_id,
                                      const std::string& bpid,
                                      std::vector<DatanodeID>* dns,
                                      RecoveringBlockProto* recovering_blk) {
  auto uc = GetUcInternal(blk_id);
  if (!uc) {
    return false;
  }

  if (uc->recovery_id() == kInvalidGenerationStamp ||
      uc->is_recover_cmd_sent()) {
    return false;
  }

  uc->set_is_recover_cmd_sent();
  recovering_blk->set_newgenstamp(uc->recovery_id());
  auto located = recovering_blk->mutable_block();
  BlockInfo* bi = Locate(blk_id);
  CHECK_NOTNULL(bi);
  located->mutable_b()->set_blockid(bi->id());
  located->mutable_b()->set_generationstamp(bi->gs());
  located->mutable_b()->set_poolid(bpid);
  located->mutable_b()->set_numbytes(bi->num_bytes());
  located->set_offset(-1);
  located->set_corrupt(false);
  auto token_proto = located->mutable_blocktoken();
  token_proto->set_identifier("");
  token_proto->set_password("");
  token_proto->set_kind("");
  token_proto->set_service("");
  *dns = uc->GetStorages();
  return true;
}

size_t BlockMapSlice::GetUCSize() {
  return uc_states_.size();
}

bool BlockMapSlice::TraverseAllBlock(
    std::function<bool(BlockInfo**, size_t)> cb) {
  lock_shared();
  auto res = cb(buckets_, num_buckets_);
  unlock_shared();
  return res;
}

bool BlockMapSlice::TraverseBuckets(size_t begin_bucket_id,
                                    size_t n,
                                    std::function<bool(BlockInfo*)> cb) {
  CHECK_GT(n, 0) << "Invalid n: " << n;
  if (begin_bucket_id >= num_buckets_) {
    return false;
  }

  std::shared_lock<ReadWriteLockLight> lock(rwlock_);
  for (size_t i = begin_bucket_id; i < num_buckets_ && n > 0; ++i, --n) {
    auto bucket = buckets_[i];
    while (bucket) {
      if (!cb(bucket)) {
        return false;
      }
      bucket = bucket->next();
    }
  }
  return true;
}

bool BlockMapSlice::IsBlockInvalidate(const Block& block, DatanodeID dn_id) {
  auto it = to_invalidate_.find(dn_id);
  if (it != to_invalidate_.end()) {
    if (it->second.find(block) != it->second.end()) {
      return true;
    }
  }
  return false;
}

void BlockMapSlice::ClearInvalidateBlock() {
  to_invalidate_.clear();
  to_invalidate_block_size_ = 0;
}

uint64_t BlockMapSlice::GetInvalidateBlockNum() {
  uint64_t num = 0;
  for (auto& iter : to_invalidate_) {
    num += iter.second.size();
  }
  if (num != to_invalidate_block_size_) {
    LOG_WITH_LEVEL(WARNING)
        << "[slice=" << slice_id_ << "]"
        << "BlockMapSlice invalidate block num count error"
        << " count=" << num << " cached=" << to_invalidate_block_size_;
    MFC(LoggerMetrics::Instance().warn_)->Inc();

    to_invalidate_block_size_ = num;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[GetInvalidateBlockNum] Fix to_invalidate_block_size_"
        << " to_invalidate_block_size_=" << to_invalidate_block_size_;
  }
  return num;
}

std::unordered_set<Block, BlockHash>* BlockMapSlice::GetInvalidateBlock(
    DatanodeID dn_id) {
  auto it = to_invalidate_.find(dn_id);
  if (it != to_invalidate_.end()) {
    return &(it->second);
  }
  return nullptr;
}

void BlockMapSlice::GetInvalidateBlock(
    std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>*
        blks) {
  for (auto iter = to_invalidate_.begin(); iter != to_invalidate_.end();) {
    auto blks_iter = blks->find(iter->first);
    if (blks_iter != blks->end()) {
      if (blks_iter->second.size() > FLAGS_blockmap_invalidate_limit_per_dn) {
        iter++;
        continue;
      }
      blks_iter->second.insert(iter->second.begin(), iter->second.end());
    } else {
      auto its =
          blks->emplace(std::make_pair(iter->first,
                                       std::unordered_set<Block, BlockHash>()))
              .first;
      its->second.insert(iter->second.begin(), iter->second.end());
    }

    int64_t delta = -static_cast<int64_t>(iter->second.size());
    to_invalidate_block_size_ += delta;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[EraseInvalidateBlock] to_invalidate_block_size_ -= "
           "it->second.size();"
        << " delta=" << delta
        << " to_invalidate_block_size_=" << to_invalidate_block_size_;

    iter = to_invalidate_.erase(iter);
  }
}

void BlockMapSlice::EraseInvalidateBlock(DatanodeID dn_id) {
  auto it = to_invalidate_.find(dn_id);
  if (it != to_invalidate_.end()) {
    int64_t delta = -static_cast<int64_t>(it->second.size());
    to_invalidate_block_size_ -= it->second.size();
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[EraseInvalidateBlock] to_invalidate_block_size_ -= "
           "it->second.size();"
        << " delta=" << delta
        << " to_invalidate_block_size_=" << to_invalidate_block_size_;
    to_invalidate_.erase(it);
  }
}

void BlockMapSlice::AddToInvalidateBlock(DatanodeID dn_id,
                                         const Block& block,
                                         const std::string& reason) {
  if (to_invalidate_block_size_ >= FLAGS_blockmap_invalidate_limit_per_slice) {
    VLOG(8) << "[slice=" << slice_id_ << "]"
            << "BlockMapSlice Invalidate queue overflow."
            << " dn_id=" << dn_id << " blk_id=" << block.id;
    // TODO(xiong): not break now. maybe in version 4.6.2
    // return;
  }
  if (dn_id != kInvalidDatanodeID && block.id != kInvalidBlockID) {
    auto it = to_invalidate_.find(dn_id);
    if (it == to_invalidate_.end()) {
      it = to_invalidate_
               .emplace(std::make_pair(dn_id,
                                       std::unordered_set<Block, BlockHash>()))
               .first;
    }

    if (it->second.insert(block).second) {
      to_invalidate_block_size_++;
      VLOG_OR_IF(15, FLAGS_log_block_map_detail)
          << "[slice=" << slice_id_ << "]"
          << "[AddToInvalidateBlock] to_invalidate_block_size_++;"
          << " delta=" << 1
          << " to_invalidate_block_size_=" << to_invalidate_block_size_;
      VLOG(8) << "[slice=" << slice_id_ << "]" << "AddToInvalidateBlock, "
              << block.ToString() << ", dn_id: " << dn_id
              << ", reason: " << reason;
    }
  }
}

void BlockMapSlice::RemoveFromInvalidateBlock(DatanodeID dn_id,
                                              const Block& block) {
  auto it = to_invalidate_.find(dn_id);
  if (it == to_invalidate_.end()) {
    return;
  }
  auto& blocks = it->second;
  if (blocks.erase(block) != 0) {
    to_invalidate_block_size_--;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[RemoveFromInvalidateBlock] to_invalidate_block_size_--;"
        << " delta=" << -1
        << " to_invalidate_block_size_=" << to_invalidate_block_size_;

    if (blocks.empty()) {
      to_invalidate_.erase(it);
    }
  }
}

bool BlockMapSlice::IsBlockTruncatable(const Block& block, DatanodeID dn_id) {
  auto it = to_truncate_.find(dn_id);
  if (it != to_truncate_.end()) {
    if (it->second.find(block) != it->second.end()) {
      return true;
    }
  }
  return false;
}

void BlockMapSlice::ClearTruncatableBlock() {
  to_truncate_.clear();
  to_truncate_block_size_ = 0;
}

uint64_t BlockMapSlice::GetTruncatableBlockNum() {
  uint64_t num = 0;
  for (auto& iter : to_truncate_) {
    num += iter.second.size();
  }
  if (num != to_truncate_block_size_) {
    LOG_WITH_LEVEL(WARNING)
        << "[slice=" << slice_id_ << "]" << "truncatable block num count error"
        << " count=" << num << " cached=" << to_truncate_block_size_;
    MFC(LoggerMetrics::Instance().warn_)->Inc();

    to_truncate_block_size_ = num;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[GetInvalidateBlockNum] Fix to_truncate_block_size_"
        << " to_truncate_block_size_=" << to_truncate_block_size_;
  }
  return num;
}

uint64_t BlockMapSlice::GetTruncatableBlockNum(DatanodeID dn_id) {
  auto it = to_truncate_.find(dn_id);
  if (it != to_truncate_.end()) {
    return it->second.size();
  }
  return 0;
}

std::unordered_set<Block, BlockHash>* BlockMapSlice::PeekTruncatableBlock(
    DatanodeID dn_id) {
  auto it = to_truncate_.find(dn_id);
  if (it != to_truncate_.end()) {
    return &(it->second);
  }
  return nullptr;
}

void BlockMapSlice::PopTruncatableBlock(
    std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>*
        blks) {
  for (auto it = to_truncate_.begin(); it != to_truncate_.end();) {
    auto blks_it = blks->find(it->first);
    if (blks_it != blks->end()) {
      if (blks_it->second.size() > FLAGS_blockmap_truncatable_limit_per_dn) {
        it++;
        continue;
      }
      blks_it->second.insert(it->second.begin(), it->second.end());
    } else {
      auto its =
          blks->emplace(std::make_pair(it->first,
                                       std::unordered_set<Block, BlockHash>()))
              .first;
      its->second.insert(it->second.begin(), it->second.end());
    }

    int64_t delta = -static_cast<int64_t>(it->second.size());
    to_truncate_block_size_ += delta;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[PopTruncatableBlock] to_truncate_block_size_ -= it->second.size();"
        << " delta=" << delta
        << " to_truncate_block_size_=" << to_truncate_block_size_;

    it = to_truncate_.erase(it);
  }
}

void BlockMapSlice::EraseTruncatableBlock(DatanodeID dn_id) {
  auto it = to_truncate_.find(dn_id);
  if (it != to_truncate_.end()) {
    int64_t delta = -static_cast<int64_t>(it->second.size());
    to_truncate_block_size_ += delta;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[EraseTruncatableBlock] to_truncate_block_size_ -= "
           "it->second.size();"
        << " delta=" << delta
        << " to_truncate_block_size_=" << to_truncate_block_size_;

    to_truncate_.erase(it);
  }
}

void BlockMapSlice::PushTruncatableBlock(DatanodeID dn_id,
                                         const Block& block,
                                         const std::string& reason) {
  if (to_truncate_block_size_ >= FLAGS_blockmap_truncatable_limit_per_slice) {
    VLOG(11) << "[slice=" << slice_id_ << "]"
             << "BlockMapSlice Truncate queue overflow."
             << " dn_id=" << dn_id << " blk_id=" << block.id;
    return;
  }

  if (dn_id != kInvalidDatanodeID && block.id != kInvalidBlockID) {
    auto it = to_truncate_.find(dn_id);
    if (it == to_truncate_.end()) {
      it = to_truncate_
               .emplace(std::make_pair(dn_id,
                                       std::unordered_set<Block, BlockHash>()))
               .first;
    }

    if (it->second.insert(block).second) {
      to_truncate_block_size_++;
      VLOG_OR_IF(15, FLAGS_log_block_map_detail)
          << "[slice=" << slice_id_ << "]"
          << "[PushTruncatableBlock] to_truncate_block_size_ ++"
          << " delta=" << 1
          << " to_truncate_block_size_=" << to_truncate_block_size_;

      VLOG(10) << "[slice=" << slice_id_ << "]" << "PushTruncatableBlock, "
               << block.ToString() << ", dn_id: " << dn_id
               << ", reason: " << reason;
    }
  }
}

void BlockMapSlice::RemoveFromTruncatableBlock(DatanodeID dn_id,
                                               const Block& block) {
  auto it = to_truncate_.find(dn_id);
  if (it == to_truncate_.end()) {
    return;
  }
  auto& blocks = it->second;
  if (blocks.erase(block) != 0) {
    to_truncate_block_size_--;
    VLOG_OR_IF(15, FLAGS_log_block_map_detail)
        << "[slice=" << slice_id_ << "]"
        << "[RemoveFromTruncatableBlock] to_truncate_block_size_ --"
        << " delta=" << -1
        << " to_truncate_block_size_=" << to_truncate_block_size_;

    if (blocks.empty()) {
      to_truncate_.erase(it);
    }
  }
}

void BlockMapSlice::AddUploadCmd(DatanodeID dn_id, const UploadCmd& cmd) {
  auto it = upload_cmds_[dn_id].find(cmd);
  if (it != upload_cmds_[dn_id].end()) {
    VLOG(8) << "Received repeated upload cmd with block B"
            << cmd.block().blockid();
    upload_cmds_[dn_id].erase(it);
  }
  upload_cmds_[dn_id].emplace(cmd);
}

std::unordered_map<DatanodeID, UploadCmdSet> BlockMapSlice::GetUploadCmds() {
  return std::move(upload_cmds_);
}

void BlockMapSlice::AddNotifyEvictableCmd(DatanodeID dn_id,
                                          const NotifyEvictableCmd& cmd) {
  auto it = notify_evictable_cmds_[dn_id].find(cmd);
  if (it != notify_evictable_cmds_[dn_id].end()) {
    VLOG(8) << "Received repeated notify evictable cmd with block B"
            << cmd.block().blockid();
    notify_evictable_cmds_[dn_id].erase(it);
  }
  notify_evictable_cmds_[dn_id].emplace(cmd);
}

std::unordered_map<DatanodeID, NotifyEvictableCmdSet> BlockMapSlice::
    GetNotifyEvictableCmds() {
  return std::move(notify_evictable_cmds_);
}

uint64_t BlockMapSlice::GetCorruptBlockNum() {
  return corrupt_blocks_.size();
}

bool BlockMapSlice::IsBlockCorrupt(BlockID blk_id, DatanodeID dn_id) {
  auto it = corrupt_blocks_.find(blk_id);
  if (it != corrupt_blocks_.end()) {
    if (it->second.find(dn_id) != it->second.end()) {
      return true;
    }
  }
  return false;
}

std::unordered_map<DatanodeID, Block>* BlockMapSlice::GetCorruptBlock(
    BlockID blk_id) {
  auto it = corrupt_blocks_.find(blk_id);
  if (it != corrupt_blocks_.end()) {
    return &(it->second);
  }
  return nullptr;
}

std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>*
BlockMapSlice::GetCorruptBlock() {
  return &corrupt_blocks_;
}

void BlockMapSlice::EraseCorruptBlock(BlockID blk_id) {
  corrupt_blocks_.erase(blk_id);
}

void BlockMapSlice::AddToCorruptBlock(BlockID blk_id,
                                      DatanodeID dn_id,
                                      Block blk) {
  if (blk_id != kInvalidBlockID && dn_id != kInvalidDatanodeID) {
    auto it = corrupt_blocks_.find(blk_id);
    if (it == corrupt_blocks_.end()) {
      std::unordered_map<DatanodeID, Block> new_corrupt_block{
          std::make_pair(dn_id, blk)};
      corrupt_blocks_.emplace(std::make_pair(blk_id, new_corrupt_block));
    } else {
      it->second[dn_id] = blk;
    }
  }
}

void BlockMapSlice::RemoveFromCorruptBlock(BlockID blk_id, DatanodeID dn_id) {
  auto it = corrupt_blocks_.find(blk_id);
  if (it == corrupt_blocks_.end()) {
    return;
  }
  auto& dns = it->second;
  if (dns.erase(dn_id) != 0 && dns.empty()) {
    corrupt_blocks_.erase(it);
  }
}

bool BlockMapSlice::IsBlockExcess(BlockID blk_id, DatanodeID dn_id) {
  auto itr = excess_replicas_.find(dn_id);
  if (itr != excess_replicas_.end()) {
    if (itr->second.find(blk_id) != itr->second.end()) {
      return true;
    }
  }
  return false;
}

void BlockMapSlice::ClearExcessBlock() {
  excess_replicas_.clear();
}

uint64_t BlockMapSlice::GetExcessBlockNum() {
  uint64_t num = 0;
  for (auto iter = excess_replicas_.begin(); iter != excess_replicas_.end();
       ++iter) {
    num += iter->second.size();
  }
  return num;
}

void BlockMapSlice::EraseExcessBlock(DatanodeID dn_id) {
  auto it = excess_replicas_.find(dn_id);
  if (it != excess_replicas_.end()) {
    excess_replicas_.erase(it);
  }
}

void BlockMapSlice::AddToExcess(BlockID blk_id, DatanodeID dn_id) {
  if (blk_id != kInvalidBlockID && dn_id != kInvalidDatanodeID) {
    auto it = excess_replicas_.find(dn_id);
    if (it == excess_replicas_.end()) {
      it = excess_replicas_
               .emplace(std::make_pair(dn_id, std::unordered_set<BlockID>()))
               .first;
    }
    it->second.emplace(blk_id);
  }
}

void BlockMapSlice::RemoveFromExcess(BlockID blk_id, DatanodeID dn_id) {
  if (blk_id != kInvalidBlockID && dn_id != kInvalidDatanodeID) {
    auto it = excess_replicas_.find(dn_id);
    if (it != excess_replicas_.end()) {
      it->second.erase(blk_id);
      if (it->second.empty()) {
        excess_replicas_.erase(it);
      }
    }
  }
}

uint64_t BlockMapSlice::GetSealedBlockNum() {
  return sealed_blocks_.size();
}

bool BlockMapSlice::IsBlockSealed(BlockID blk_id, DatanodeID dn_id) {
  auto it = sealed_blocks_.find(blk_id);
  if (it != sealed_blocks_.end()) {
    if (it->second.find(dn_id) != it->second.end()) {
      return true;
    }
  }
  return false;
}

std::unordered_map<DatanodeID, Block>* BlockMapSlice::GetSealedBlock(
    BlockID blk_id) {
  auto it = sealed_blocks_.find(blk_id);
  if (it != sealed_blocks_.end()) {
    return &(it->second);
  }
  return nullptr;
}

std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>*
BlockMapSlice::GetSealedBlock() {
  return &sealed_blocks_;
}

void BlockMapSlice::MoveSealedBlockToTruncatable(
    std::function<bool()> checker) {
#define RETURN_IF_CHECK_FAIL() \
  if (!checker()) {            \
    return;                    \
  }

  for (auto it = sealed_blocks_.begin(); it != sealed_blocks_.end();) {
    BlockID block_id = it->first;
    // is block committed
    auto bi = Locate(block_id);
    RETURN_IF_CHECK_FAIL();

    if (!bi) {
      VLOG(10) << "MoveSealedBlockToTruncatable cannot find B" << block_id;
      it = sealed_blocks_.erase(it);
      continue;
    }

    if (!bi->HasBeenCommitted()) {
      VLOG(10) << "MoveSealedBlockToTruncatable, blk: " << bi->ToString()
               << ", not committed";
      // not committed
      it++;
      continue;
    }

    RETURN_IF_CHECK_FAIL();
    for (auto& pair : it->second) {
      RETURN_IF_CHECK_FAIL();
      auto dn_id = pair.first;

      // TODO(xiong): add some check?
      VLOG(10) << "MoveSealedBlockToTruncatable, blk: " << bi->ToString()
               << ", dn_id: " << dn_id << ", Add";

      // use stored block length to truncate
      PushTruncatableBlock(dn_id, bi->blk(), "MoveSealedBlockToTruncatable");
    }

    it++;
  }
#undef RETURN_IF_CHECK_FAIL
}

void BlockMapSlice::EraseSealedBlock(BlockID blk_id) {
  sealed_blocks_.erase(blk_id);
}

void BlockMapSlice::AddToSealedBlock(BlockID blk_id,
                                     DatanodeID dn_id,
                                     Block blk) {
  if (blk_id != kInvalidBlockID && dn_id != kInvalidDatanodeID) {
    auto it = sealed_blocks_.find(blk_id);
    if (it == sealed_blocks_.end()) {
      if (sealed_blocks_.size() >= FLAGS_blockmap_sealed_limit_per_slice) {
        LOG_WITH_LEVEL(ERROR) << "[slice=" << slice_id_ << "]"
                              << "Sealed queue overflow, blk_id=" << blk_id;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return;
      }

      std::unordered_map<DatanodeID, Block> new_sealed_block{
          std::make_pair(dn_id, blk)};
      sealed_blocks_.emplace(std::make_pair(blk_id, new_sealed_block));
    } else {
      it->second[dn_id] = blk;
    }
  }
}

void BlockMapSlice::RemoveFromSealedBlock(BlockID blk_id, DatanodeID dn_id) {
  auto it = sealed_blocks_.find(blk_id);
  if (it == sealed_blocks_.end()) {
    return;
  }
  auto& dns = it->second;
  if (dns.erase(dn_id) != 0 && dns.empty()) {
    sealed_blocks_.erase(it);
  }
}

void BlockMapSlice::ClearPendingReplications() {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  pending_replications_.Clear();
}

void BlockMapSlice::RemovePendingReplications(BlockID blk_id) {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  pending_replications_.Remove(blk_id);
}

uint64_t BlockMapSlice::GetPendingReplicationsNum() {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  return pending_replications_.Size();
}

size_t BlockMapSlice::NumReplicas(BlockID blk_id) {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  return pending_replications_.NumReplicas(blk_id);
}

void BlockMapSlice::IncrementReplicas(BlockID blk_id,
                                      const std::vector<DatanodeID>& targets) {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  pending_replications_.Increment(blk_id, targets);
}

void BlockMapSlice::DecrementReplicas(BlockID blk_id, DatanodeID dn_id) {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  pending_replications_.Decrement(blk_id, dn_id);
}

void BlockMapSlice::TimedOutBlocks(std::vector<BlockID>* timeout_blocks) {
  std::lock_guard<std::mutex> guard(pending_replications_mutex_);
  return pending_replications_.TimedOutBlocks(timeout_blocks);
}

void BlockMapSlice::EnqueuePendingFutureBlks(const DatanodeStorageProto& dsp,
                                             const Block& block,
                                             DatanodeID dn_id,
                                             const ReplicaStateProto& rsp) {
  auto iter = pending_future_blks_.find(block.id);
  if (iter == pending_future_blks_.end()) {
    iter =
        pending_future_blks_
            .emplace(std::make_pair(block.id, std::deque<ReportedBlockInfo>()))
            .first;
  }
  iter->second.emplace_back(ReportedBlockInfo{block, dn_id, dsp, rsp});
}

void BlockMapSlice::RemoveAllPendingFutureBlks(DatanodeID dn_id) {
  for (auto iter = pending_future_blks_.begin();
       iter != pending_future_blks_.end();) {
    for (auto it = iter->second.begin(); it != iter->second.end();) {
      if (it->dn_id == dn_id) {
        it = iter->second.erase(it);
      } else {
        ++it;
      }
    }
    if (iter->second.empty()) {
      iter = pending_future_blks_.erase(iter);
      continue;
    }
    ++iter;
  }
}

void BlockMapSlice::DequeuePendingFutureBlks(
    BlockID block_id,
    std::deque<ReportedBlockInfo>* rbis) {
  auto iter = pending_future_blks_.find(block_id);
  if (iter != pending_future_blks_.end()) {
    rbis->swap(iter->second);
    pending_future_blks_.erase(iter);
  }
}

void BlockMapSlice::DequeuePendingFutureBlks(
    std::deque<ReportedBlockInfo>* rbis) {
  for (const auto& bq : pending_future_blks_) {
    rbis->insert(rbis->end(), bq.second.begin(), bq.second.end());
  }
  pending_future_blks_.clear();
}

size_t BlockMapSlice::NumFutureBlocks() {
  return pending_future_blks_.size();
}

void BlockMapSlice::EnqueuePendingMisinvalidatedBlks(
    const ReportedBlockInfo& rbi) {
  if (!FLAGS_misinvalidated_block_enable_record) {
    return;
  }
  auto iter = pending_misinvalidated_blks_.find(rbi.blk.id);
  if (iter == pending_misinvalidated_blks_.end()) {
    iter = pending_misinvalidated_blks_
               .emplace(
                   std::make_pair(rbi.blk.id, std::deque<ReportedBlockInfo>()))
               .first;
  }
  iter->second.emplace_back(ReportedBlockInfo{
      rbi.blk, rbi.dn_id, rbi.dsp, rbi.rsp, rbi.create_time});
}

void BlockMapSlice::DequeuePendingMisinvalidatedBlks(
    std::deque<ReportedBlockInfo>* rbis) {
  for (const auto& bq : pending_misinvalidated_blks_) {
    rbis->insert(rbis->end(), bq.second.begin(), bq.second.end());
  }
  pending_misinvalidated_blks_.clear();
}

size_t BlockMapSlice::NumMisinvalidatedBlks() {
  return pending_misinvalidated_blks_.size();
}

void BlockMapSlice::EnqueuePendingPersistedBlks(const Block& blk) {
  auto r = pending_persisted_blks_.insert(blk);
  if (!r.second) {
    const Block& old_blk = *r.first;
    if (blk.id != old_blk.id || blk.num_bytes != old_blk.num_bytes ||
        blk.gs != old_blk.gs) {
      LOG(ERROR) << "Repeated pending persisted blk: " << blk
                 << ", old blk: " << old_blk;
    }
  }
}

bool BlockMapSlice::RemovePendingPersistedBlks(const Block& blk) {
  return pending_persisted_blks_.erase(blk) > 0;
}

size_t BlockMapSlice::NumPersistedBlks() {
  return pending_persisted_blks_.size();
}

void BlockMapSlice::ReleasePersistedAndCacheFreeBlocks() {
  for (std::size_t i = 0; i < num_buckets_; i++) {
    for (BlockInfo *prev = nullptr, *curr = buckets_[i]; curr != nullptr;) {
      BlockInfo* to_be_released = nullptr;
      std::unique_ptr<BlockMapSlice, std::function<void(BlockMapSlice*)>>
          scoped_guard(this,
                       [&to_be_released, &prev, &curr](BlockMapSlice* ignored) {
                         if (to_be_released == nullptr) {
                           prev = curr;
                           curr = curr->next();
                         }
                       });

      if (!curr->IsPersisted() || curr->size() != 0) {
        continue;
      }
      BlockInfoProto bip;
      if (!meta_storage_->GetBlockInfo(curr->id(), &bip)) {
        LOG(ERROR) << "Missing B" << curr->id();
        continue;
      }
      if (!curr->IsSafeToRelease(bip)) {
        LOG(WARNING) << "B" << curr->id() << " is unsafe to release block info";
        continue;
      }

      to_be_released = curr;
      if (prev != nullptr) {
        prev->SetNext(curr->next());
        curr = curr->next();
      } else {
        buckets_[i] = curr->next();
        prev = nullptr;
        curr = buckets_[i];
      }
      LOG(INFO) << "Release block B" << to_be_released->id();
      std::free(to_be_released);
    }
  }
}

}  // namespace dancenn
