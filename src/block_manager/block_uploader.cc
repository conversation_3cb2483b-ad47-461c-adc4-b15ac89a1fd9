// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/02/16
// Description

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <mutex>
#include <string>

#include "base/closure.h"
#include "base/constants.h"
#include "base/logger_metrics.h"
#include "base/metrics.h"
#include "base/pb_converter.h"
#include "base/vlock.h"
#include "block_manager/block.h"
#include "block_manager/block_info.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_map_slice.h"
#include "datanode_manager/datanode_info.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/cloudfs/lifecycle.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "security/block_token_identifier.h"

DECLARE_bool(disable_block_uploader);
DECLARE_int32(namespace_type);
DECLARE_bool(trace_rpc_log);
DECLARE_uint32(ufs_evict_append_object_max_block_cnt_when_persist);
DECLARE_bool(block_expected_replica_determined_by_inode);
DECLARE_bool(block_ignore_not_exist_fatal);
DECLARE_bool(log_send_ne_cmd);
DECLARE_bool(log_ufs_persist_detail);

namespace dancenn {

std::ostream& operator<<(std::ostream& os,
                         const BlockManager::NegoedResult& action) {
  os << "NegoedResult::";
  switch (action) {
    case BlockManager::NegoedResult::kApprove:
      os << "kApprove";
      break;
    case BlockManager::NegoedResult::kDeny:
      os << "kDeny";
      break;
    case BlockManager::NegoedResult::kNotifyUpload:
      os << "kNotifyUpload";
      break;
    case BlockManager::NegoedResult::kNotifyEvictable:
      os << "kNotifyEvictable";
      break;
    case BlockManager::NegoedResult::kPersistAndEvict:
      os << "kPersistAndEvict";
      break;
    case BlockManager::NegoedResult::kError:
      os << "kError";
      break;
  };

  return os;
}

bool BlockManager::IsBlockReadyToPersisted(const BlockInfoProto& bip,
                                           const std::string& pufs_name,
                                           const std::string& upload_id) {
  return bip.curr_upload_id() == upload_id && bip.pufs_name() == pufs_name &&
         bip.has_etag() && !bip.etag().empty();
}

std::pair<BlockManager::NegoedResult, BlockInfoProto> BlockManager::
    NegoInHdfsMode(BlockID block_id,
                   const std::string& dn_uuid,
                   int64_t currentTsInSec) {
  // NameSpace::AsyncAddBlock changes BlockInfo before changing BlockInfoProto,
  // so checking BlockInfoProto is necessary.
  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(block_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << block_id;
    return {kError, bip};
  }

  CheckHdfsBlock(bip);

  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kCommitted:
      return {kDeny, bip};
    case BlockInfoProto::kComplete:
      return {kApprove, bip};
    case BlockInfoProto::kUploadIssued:
      if (currentTsInSec > bip.nn_exp_ts() ||
          datanode_manager_->GetDatanodeFromUuid(bip.dn_uuid())->IsStale()) {
        return {kApprove, bip};
      } else if (dn_uuid == bip.dn_uuid()) {
        return {kNotifyUpload, bip};
      } else {
        return {kDeny, bip};
      }
    case BlockInfoProto::kPersisted:
    case BlockInfoProto::kDeprecated:
      return {kNotifyEvictable, bip};
    default:
      LOG(ERROR) << "UnexpectedBlock " << PBConverter::ToCompactJsonString(bip);
      return {kDeny, bip};
  }
}

std::pair<BlockManager::NegoedResult, BlockInfoProto> BlockManager::
    NegoInAccMode(BlockID block_id,
                  const std::string& dn_uuid,
                  int64_t currentTsInSec,
                  BlockMapSlice* s) {
  // NameSpace::AsyncAddBlock changes BlockInfo before changing BlockInfoProto,
  // so checking BlockInfoProto is necessary.
  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(block_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << block_id;
    return {kError, bip};
  }

  CheckAccBlock(bip);

  bool is_append_block = bip.upload_type() == cloudfs::datanode::APPEND;
  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction:
      return {kDeny, bip};
    case BlockInfoProto::kCommitted:
    case BlockInfoProto::kComplete:
      if (is_append_block) {
        BlockInfoProto prev_bip;
        auto ret = AccLockBlockAndGetPrevBlock(bip, s, &prev_bip);
        if (!ret) {
          VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
              << "AccLockBlockAndGetPrevBlock failed";
          return {kError, bip};
        }
        VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
            << "AccLockBlockAndGetPrevBlock prev_bip="
            << prev_bip.ShortDebugString();
        if (prev_bip.block_id() == kInvalidBlockID) {
          return {kDeny, bip};
        } else if (prev_bip.state() == BlockInfoProto::kPersisted) {
          return {kApprove, bip};
        } else {
          return {kDeny, bip};
        }
      } else {
        if (bip.key_block()) {
          if (bip.curr_upload_id().empty()) {
            return {kDeny, bip};
          } else {
            return {kApprove, bip};
          }
        } else {
          INode inode;
          auto status = AccGetINodeReadOnly(bip.inode_id(), &inode);
          if (status.HasException()) {
            VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
                << "AccGetINodeReadOnly failed. status=" << status.ToString();
            return {kError, bip};
          }
          VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
              << "AccGetINodeReadOnly inode=" << inode.ShortDebugString();
          if (inode.id() == kInvalidINodeId) {
            LOG(WARNING) << "INode not found for block "
                         << bip.ShortDebugString();
            return {kDeny, bip};
          } else if (inode.ufs_file_info().file_state() ==
                     kUfsFileStatePersisted) {
            return {kPersistAndEvict, bip};
          } else {
            return {kDeny, bip};
          }
        }
      }
    case BlockInfoProto::kUploadIssued:
      if (currentTsInSec > bip.nn_exp_ts() ||
          datanode_manager_->GetDatanodeFromUuid(bip.dn_uuid())->IsStale()) {
        if (is_append_block) {
          return {kApprove, bip};
        } else if (bip.curr_upload_id().empty()) {
          LOG(ERROR) << "Upload id should not be empty B" << bip.block_id();
          return {kDeny, bip};
        } else {
          return {kApprove, bip};
        }
      } else if (dn_uuid == bip.dn_uuid()) {
        return {kNotifyUpload, bip};
      } else {
        return {kDeny, bip};
      }
    case BlockInfoProto::kPersisted:
      if (is_append_block) {
        return {kNotifyEvictable, bip};
      } else {
        INode inode;
        auto status = AccGetINodeReadOnly(bip.inode_id(), &inode);
        if (status.HasException()) {
          VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
              << "AccGetINodeReadOnly failed. status=" << status.ToString();
          return {kError, bip};
        }
        VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
            << "AccGetINodeReadOnly inode=" << inode.ShortDebugString();
        if (inode.id() == kInvalidINodeId) {
          LOG(WARNING) << "INode not found for block "
                       << bip.ShortDebugString();
          return {kDeny, bip};
        } else if (inode.ufs_file_info().file_state() ==
                   kUfsFileStatePersisted) {
          return {kNotifyEvictable, bip};
        } else if (inode.ufs_file_info().file_state() ==
                   kUfsFileStateToBePersisted) {
          // Non-KeyBlock does not need upload
          if (!bip.key_block()) {
            return {kDeny, bip};
          }

          // UploadId is not generated
          if (!bip.has_curr_upload_id() || bip.curr_upload_id().empty()) {
            return {kDeny, bip};
          }

          // Block has been uploaded with upload id, wait NS->Persist check
          if (bip.has_etag() && !bip.etag().empty()) {
            return {kDeny, bip};
          }

          // Block need upload, change DN if expired
          if (currentTsInSec > bip.nn_exp_ts() ||
              datanode_manager_->GetDatanodeFromUuid(bip.dn_uuid())
                  ->IsStale()) {
            return {kApprove, bip};
          } else if (dn_uuid == bip.dn_uuid()) {
            return {kNotifyUpload, bip};
          } else {
            return {kDeny, bip};
          }
        } else {
          return {kDeny, bip};
        }
      }
    case BlockInfoProto::kDeprecated:
      return {kNotifyEvictable, bip};
    default:
      LOG(ERROR) << "UnexpectedBlock " << PBConverter::ToCompactJsonString(bip);
      return {kDeny, bip};
  }
}

void BlockManager::ApproveUploadCallback(
    const BlockInfoProto& bip,
    BlockInfoInTransactionGuardPtr bi_tx_guard) {
  BlockID blk_id = bip.block_id();
  std::unique_ptr<BlockMapSlice>& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (bi == nullptr) {
    MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << "Approve upload request for missing B" << blk_id;
    return;
  }
  if (!bi_tx_guard->Unlock(__FILE__, __LINE__)) {
    MFC(LoggerMetrics::Instance().unlock_block_info_in_transaction_lock_failed_)
        ->Inc();
    LOG_WITH_LEVEL(ERROR) << "Unlock in transaction lock failed, B" << blk_id;
  }
  guard.unlock();

  // If DanceNN restarts, it will replay all edit logs first.
  // So DanceNN won't reach here if it restarts after writing edit log but
  // before writing meta storage.
  NotifyBlockUpload(bip);
}

void BlockManager::PersistBlockCallback(
    const BlockInfoProto& bip,
    BlockInfoInTransactionGuardPtr bi_tx_guard,
    bool evict_in_acc_mode) {
  BlockID blk_id = bip.block_id();
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (bi == nullptr) {
    MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
    LOG_WITH_LEVEL(ERROR) << "Persist missing B" << blk_id;
    return;
  }
  if (!PersistBlockUnsafe(
          s.get(),
          Block{/*id*/ blk_id,
                /*num_bytes*/ static_cast<uint32_t>(bip.num_bytes()),
                /*gs*/ bip.gen_stamp()})) {
    LOG(ERROR) << "B" << blk_id << " can't be persisted, " << bi->ToString();
    if (!(NameSpace::IsAccMode() && bi->IsPersisted())) {
      //  In ACC mode, a block might be uploaded more than once since user
      //  might rename the file during uploading. NN will re-issue upload
      //  cmd to DN but the bi already become kPersisted when first upload
      //  succeeded. In this case, we do not want a fail metric.
      MFC(metrics_.persist_block_fail_count_)->Inc();
    }
  } else {
    MFC(metrics_.persist_block_succ_count_)->Inc();
  }

  if (!evict_in_acc_mode && NameSpace::IsAccMode()) {
    CheckAccBlock(bip);
    CHECK(bi_tx_guard->Unlock(__FILE__, __LINE__)) << blk_id;
    LOG(INFO) << "Acc block should be evicted next time B" << blk_id;
    return;
  }

  // TODO(ruanjunbin): Use SetReplica?
  if (bi->expected_rep() > 1) {
    bi->set_expected_rep(1);
    // NOTICE: Please note whether content stale is true if INVALIDATE
    // commands aren't sent to data nodes.
    ProcessOverReplicatedBlock(bi, 1);
  }

  NotifyBlockEvictable(bip, bi);

  if (!bi_tx_guard->Unlock(__FILE__, __LINE__)) {
    MFC(LoggerMetrics::Instance().unlock_block_info_in_transaction_lock_failed_)
        ->Inc();
    LOG_WITH_LEVEL(ERROR) << "Unlock in transaction lock failed, B" << blk_id;
  }
}

void BlockManager::NotifyAllBlockEvictable(const INode& inode) {
  VLOG_OR_IF(10, FLAGS_log_send_ne_cmd)
      << "NotifyAllBlockEvictable inode=" << inode.ShortDebugString();

  if (inode.status() != INode_Status_kFileComplete) {
    LOG(ERROR) << "NotifyAllBlockEvictable "
               << "inode is UC";
    return;
  }

  if (!inode.has_ufs_file_info()) {
    LOG(ERROR) << "NotifyAllBlockEvictable "
               << "inode not ufs file";
    return;
  }
  if (inode.ufs_file_info().file_state() !=
      UfsFileState::kUfsFileStatePersisted) {
    LOG(ERROR) << "NotifyAllBlockEvictable "
               << "inode not in kUfsFileStatePersisted";
    return;
  }

  for (int i = 0; i < inode.blocks_size(); i++) {
    if (inode.ufs_file_info().create_type() ==
        UfsFileCreateType::kUfsFileCreateTypeAppend) {
      if (i + FLAGS_ufs_evict_append_object_max_block_cnt_when_persist <
          inode.blocks_size()) {
        continue;
      }
    }

    const auto& bp = inode.blocks(i);
    BlockID blk_id = bp.blockid();

    BlockInfoProto bip;
    if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
      LOG(ERROR) << "MissingBlockInMetaStorage B" << blk_id;
      continue;
    }
    if (!(bp.blockid() == bip.block_id() && bp.genstamp() == bip.gen_stamp() &&
          bp.numbytes() == bip.num_bytes())) {
      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "BlockProto is mismatched with BlockInfoProto"
          << ", bp: " << bp.blockid() << "/" << bp.genstamp() << "/"
          << bp.numbytes()
          << ", bip: " << PBConverter::ToCompactJsonString(bip);
      continue;
    }

    auto& s = slice(blk_id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), blk_id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (bi == nullptr) {
      MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
      LOG_WITH_LEVEL(ERROR) << "Persist missing B" << blk_id;
      continue;
    }

    NotifyBlockEvictable(bip, bi);
  }
}

void BlockManager::NotifyBlockEvictable(const BlockInfoProto& bip,
                                        BlockInfo* bi) {
  VLOG_OR_IF(10, FLAGS_log_send_ne_cmd)
      << "Add Evictable cmd of B" << bip.block_id();

  bool pin = false;
  INode inode;
  if (meta_storage_->GetINode(bip.inode_id(), &inode) != StatusCode::kOK) {
    pin = false;
  } else {
    pin = inode.has_pin_status() && inode.pin_status().pinned();
  }
  for (int i = 0; i < bi->size(); i++) {
    if (i >= inode.replication()) {
      pin = false;
    }
    cloudfs::StorageClassProto cls;
    GetCurrentStorageClass(meta_storage_.get(), bip.inode_id(), &cls);
    ne_cmd_mgr_.Add(
        bi->storage_id(i),
        GetNotifyEvictableCmd(ns_->blockpool_id(), bip, cls, pin));
  }
}

// BlockMapSlice Lock MUST be unlocked when calling this method
// In FillUploadCmdAccMode, we might call NameSpace::CreateLocatedBlocks to
// acquire block locations which might acquire lock on other block info
void BlockManager::NotifyBlockUpload(const BlockInfoProto& bip) {
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "Add upload cmd of B" << bip.block_id();
  UploadCmd cmd = GetUploadCmd(ns_->blockpool_id(), bip);

  if (NameSpace::IsAccMode()) {
    if (FillUploadCmdAccMode(bip, &cmd) == false) {
      LOG(ERROR) << "FillUploadCmdAccMode failed: " << bip.ShortDebugString();
      return;
    }
  }
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "NotifyBlockUpload " << " dn_uuid=" << bip.dn_uuid()
      << "cmd=" << cmd.ShortDebugString();
  // LOG(INFO) << cmd.ShortDebugString() << " " << cmd.IsInitialized();
  upload_cmd_mgr_.Add(datanode_manager_->GetDatanodeInterId(bip.dn_uuid()),
                      std::move(cmd));
}

void BlockManager::ApproveUploadRequest(
    const BlockInfoProto& bip,
    const BlockInfoProto& old_bip,
    vshared_lock&& ha_barrier,
    BlockInfoInTransactionGuardPtr bi_tx_guard) {
  auto txid = edit_log_sender_->LogApproveUploadBlk(bip, old_bip);
  CHECK_NE(txid, kInvalidTxId);
  auto done = new RpcClosure(
      [this, bip, bi_tx_guard2 = std::move(bi_tx_guard)](Status ignored) {
        ApproveUploadCallback(bip, std::move(bi_tx_guard2));
      },
      true);
  done->set_barrier(std::move(ha_barrier));
  meta_storage_->PutBlockInfo(bip, &old_bip, txid, done);
}

void BlockManager::PersistBlockRequest(
    const BlockInfoProto& bip,
    const BlockInfoProto& old_bip,
    vshared_lock&& ha_barrier,
    BlockInfoInTransactionGuardPtr bi_tx_guard,
    bool evict_in_acc_mode) {
  auto txid = edit_log_sender_->LogPersistBlk(bip, old_bip);
  CHECK_NE(txid, kInvalidTxId);
  auto done = new RpcClosure(
      [this, bip, bi_tx_guard = std::move(bi_tx_guard), evict_in_acc_mode](
          Status ignored) {
        PersistBlockCallback(bip,
                             std::move(bi_tx_guard),
                             /*evict_in_acc_mode=*/evict_in_acc_mode);
        if (NameSpace::IsAccMode()) {
          ns_->ufs_env()->upload_monitor()->TriggerFilePersist(bip.inode_id(),
                                                               false);
        }
      },
      true);
  done->set_barrier(std::move(ha_barrier));
  meta_storage_->MoveBlockInfoToPersisted(bip, &old_bip, txid, done);
}

void BlockManager::FinishBipAccMode(const std::string& upload_id,
                                    const std::string& etag,
                                    vshared_lock&& ha_barrier,
                                    BlockInfoProto&& bip,
                                    std::unique_lock<BlockMapSlice>&& s_lock,
                                    BlockMapSlice* s,
                                    BlockInfo* bi) {
  INode inode;
  auto status = AccGetINodeReadOnly(bip.inode_id(), &inode);
  if (status.HasException()) {
    LOG(WARNING) << "INode not found for block " << bip.ShortDebugString();
    return;
  }
  if (inode.ufs_file_info().file_state() ==
      UfsFileState::kUfsFileStatePersisted) {
    NotifyBlockEvictable(bip, bi);
    return;
  }
  if (bip.curr_upload_id() != upload_id) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "bip upload id mismatch, upload_id " << upload_id << " bip "
        << bip.ShortDebugString();
    if (!bip.key_block()) {
      LOG(ERROR) << "Non key block should not be upload B" << bip.block_id();
      return;
    }
    s_lock.unlock();
    NotifyBlockUpload(bip);
    return;
  }
  if (bip.has_etag() && (bip.etag() == etag)) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Block has been updated with correct upload id B" << bi->id()
        << " upload id " << upload_id << " pufs name " << bip.pufs_name();
    return;
  }

  auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
      s, bi->id(), __FILE__, __LINE__);
  if (!bi_tx_guard->OwnLock()) {
    LOG(INFO) << "LockBlockFailed B" << bi->id();
    return;
  }
  if (etag.empty()) {
    LOG_WITH_LEVEL(WARNING)
        << "Etag should not be empty: " << bip.ShortDebugString();
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return;
  }
  BlockInfoProto old_bip = bip;
  bip.set_etag(etag);

  auto txid = kInvalidTxId;
  txid = edit_log_sender_->LogAccUpdateBlockInfo(bip, old_bip);
  CHECK_NE(txid, kInvalidTxId);

  auto done = new RpcClosure(
      [this, bip, bi_tx_guard = std::move(bi_tx_guard)](const Status& ignored) {
        // for unlock
        VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
            << "Update Block info proto finished " << bip.ShortDebugString();
        BlockID blk_id = bip.block_id();
        auto& s = slice(blk_id);
        std::unique_lock<BlockMapSlice> guard(*s);
        s->TellLockHolder(__FILE__, __LINE__);
        BlockInfoGuard bi_guard(s.get(), blk_id, true);
        BlockInfo* bi = bi_guard.GetBlockInfo();
        if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
          LOG(ERROR) << "block not exist, block_id=" << blk_id;
          CHECK(bi_tx_guard->Unlock(__FILE__, __LINE__));
          return;
        }
        CHECK_NOTNULL(bi);
        CHECK(bi_tx_guard->Unlock(__FILE__, __LINE__));
      },
      true);
  done->set_barrier(std::move(ha_barrier));
  meta_storage_->OrderedUpdateUfsBlockInfo(txid, bip, &old_bip, done);
}

void BlockManager::TriggerUploadChooseDN(const BlockProto& bp,
                                         const std::string& upload_id,
                                         const std::string& pufs_name) {
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadChooseDN"
      << " bp=" << bp.ShortDebugString() << " upload_id=" << upload_id
      << " pufs_name=" << pufs_name;
  MFC(ns_->metrics().trigger_block_upload_cnt_)->Inc();
  auto dn_ids = ns_->block_manager()->GetBlockStorage(bp.blockid());

  if (dn_ids.empty()) {
    LOG(INFO) << "TriggerUploadChooseDN has no dn, block_id=" << bp.blockid();
    return;
  }

  std::vector<DatanodeInfoPtr> alive_dns;
  {
    auto block_id = bp.blockid();
    auto& s = slice(block_id);
    std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
    s->TellLockHolder(__FILE__, __LINE__);

    for (auto dn_id : dn_ids) {
      auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
      if (dn == nullptr || dn->IsStale() || !dn->IsFunctional()) {
        VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
            << "TriggerUploadChooseDN skip dead node, dn_id=" << dn_id
            << " dn_uuid=" << (dn == nullptr ? "nullptr" : dn->uuid());
        continue;
      }
      if (s->IsBlockSealed(block_id, dn_id)) {
        VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
            << "TriggerUploadChooseDN skip sealed block, dn_id=" << dn_id
            << " dn_uuid=" << (dn == nullptr ? "nullptr" : dn->uuid())
            << " block_id=" << block_id;
        continue;
      }

      VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
          << "TriggerUploadChooseDN candidate_dn=" << dn->ToShortString();
      alive_dns.push_back(dn);
    }
  }

  if (alive_dns.empty()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadChooseDN has no alive dn, block_id=" << bp.blockid();
    return;
  }

  std::random_device rd;
  std::mt19937 re(rd());
  std::shuffle(alive_dns.begin(), alive_dns.end(), re);
  auto dn = alive_dns[0];
  CHECK_NOTNULL(dn);

  // trigger like IBR
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "TriggerUploadChooseDN " << dn->ToShortString();
  CHECK_NOTNULL(bg_uploader_worker_);
  {
    std::lock_guard<std::mutex> guard(bg_uploader_worker_task_mutex_);
    if (bg_uploader_worker_task_set_.find(bp.blockid()) !=
        bg_uploader_worker_task_set_.end()) {
      return;
    } else {
      bg_uploader_worker_task_set_.insert(bp.blockid());
    }
  }

  MFC(ns_->ufs_uploading_mgr()->metric_bg_tasks_)->Inc(1);

  bg_uploader_worker_->AddTask([=] {
    VLOG_IF(10, FLAGS_log_ufs_persist_detail)
        << "TriggerUploadChooseDN BG ProcessUploadIdNegoedBlock"
        << dn->ToShortString();
    ProcessUploadIdNegoedBlock(bp, dn->id(), dn->uuid(), upload_id, pufs_name);

    {
      std::lock_guard<std::mutex> guard(bg_uploader_worker_task_mutex_);
      bg_uploader_worker_task_set_.erase(bp.blockid());
    }

    MFC(ns_->ufs_uploading_mgr()->metric_bg_tasks_)->Inc(-1);
    return true;
  });
}

// Note: Caller already releases unique lock of block map slice.
// guard.unlock();
void BlockManager::ProcessUploadIdNegoedBlock(
    const BlockProto& bp,
    DatanodeID dn_id,
    const std::string& dn_uuid,
    const std::string& upload_id,
    const std::string& block_pufs_name) {
  if (FLAGS_disable_block_uploader) {
    return;
  }
  if (!is_active_) {
    return;
  }
  // When processing upload-id-negoed block or upload-succeed block,
  // we may need HAState::barrier_.
  // We should take a lock order convention to avoid deadlock:
  // starting with HAState::barrier, followed by path lock,
  // and finally block map slice lock.
  // See NameSpace::GetBlockLocation and NameSpace::AsyncAddBlock
  // for more information.
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    return;
  }

  BlockID blk_id = bp.blockid();
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    LOG(WARNING) << "Inc upload id negoed block report B" << blk_id
                 << " from DN" << dn_id << " cannot find matched block info";
    return;
  }
  if (!bi->HasBeenCommitted()) {
    LOG(INFO) << "B" << bi->id() << " hasn't been committed.";
    return;
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "ProcessUploadIdNegoedBlock "
      << " bp=" << bp.ShortDebugString() << " dn_id=" << dn_id
      << " dn_uuid=" << dn_uuid << " upload_id=" << upload_id
      << " block_pufs_name=" << block_pufs_name;
  int64_t currentTsInSec = GetCurrentTsInSec();
  auto action = kDeny;
  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << blk_id;
    return;
  }
  BlockInfoProto old_bip = bip;
  if (!(bp.blockid() == bip.block_id() &&
        bp.genstamp() == bip.gen_stamp() &&
        bp.numbytes() == bip.num_bytes())) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "BlockProto is mismatched with BlockInfoProto"
        << ", bp: " << bp.blockid() << "/" << bp.genstamp() << "/"
        << bp.numbytes() << ", bip: " << PBConverter::ToCompactJsonString(bip);
    return;
  }

  if (NameSpace::IsHdfsMode()) {
    std::tie(action, bip) = NegoInHdfsMode(bi->id(), dn_uuid, currentTsInSec);
    old_bip = bip;
    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "ProcessUploadIdNegoedBlock NegoInHdfsMode "
        << " bip=" << bip.ShortDebugString() << " action=" << action;
  }

  if (NameSpace::IsAccMode()) {
    std::tie(action, bip) =
        NegoInAccMode(bi->id(), dn_uuid, currentTsInSec, s.get());
    old_bip = bip;
    VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
        << "ProcessUploadIdNegoedBlock NegoInAccMode "
        << " bip=" << bip.ShortDebugString() << " action=" << action;
    // if this is an appendable object block, block map slice lock might be
    // unlocked during nego, the block info might change after NegoInAccMode
    bi = s->Locate(blk_id);
    if (bi == nullptr) {
      LOG(WARNING) << "Inc upload id negoed block report B" << blk_id
                   << " from DN" << dn_id << " cannot find matched block info";
      return;
    }
  }

  if (action == kError) {
    // fast return
    return;
  }

  auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
      s.get(), bi->id(), __FILE__, __LINE__);
  if (!bi_tx_guard->OwnLock()) {
    LOG(INFO) << "LockBlockFailed B" << bi->id();
    return;
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "Handle upload nego request, B" << bi->id()
      << ", action: " << static_cast<int>(action);

  switch (action) {
    case kApprove:
      MFC(metrics_.upload_nego_ops_approve_)->Inc();
      FillBipWhenApprove(
          block_pufs_name, upload_id, dn_uuid, currentTsInSec, &bip);
      block_map_slice_lock.unlock();

      ApproveUploadRequest(
          bip, old_bip, std::move(cres.second), std::move(bi_tx_guard));
      break;
    case kDeny:
      MFC(metrics_.upload_nego_ops_deny_)->Inc();
      break;
    case kNotifyUpload:
      bi_tx_guard->Unlock(__FILE__, __LINE__);
      block_map_slice_lock.unlock();
      MFC(metrics_.upload_nego_ops_notify_upload_)->Inc();
      NotifyBlockUpload(bip);
      break;
    case kNotifyEvictable:
      MFC(metrics_.upload_nego_ops_notify_evictable_)->Inc();
      NotifyBlockEvictable(bip, bi);
      break;
    case kPersistAndEvict:
      block_map_slice_lock.unlock();

      MFC(metrics_.upload_nego_ops_persist_evictable_)->Inc();

      bip.set_state(BlockInfoProto::kPersisted);

      PersistBlockRequest(bip,
                          old_bip,
                          std::move(cres.second),
                          std::move(bi_tx_guard),
                          /*evict_in_acc_mode=*/true);
      break;
    default:
      LOG(FATAL) << "Unknown action " << action;
  }
}

// Note: Caller already releases unique lock of block map slice.
// guard.unlock();
void BlockManager::ProcessUploadBlockSucceed(const BlockProto& bp,
                                             DatanodeID dn_id,
                                             const std::string& upload_id,
                                             const std::string& block_pufs_name,
                                             const std::string& etag) {
  if (FLAGS_disable_block_uploader) {
    return;
  }
  if (!is_active_) {
    return;
  }
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    return;
  }

  VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
      << "ProcessUploadBlockSucceed " << " bp=" << bp.ShortDebugString()
      << " dn_id=" << dn_id << " upload_id=" << upload_id
      << " block_pufs_name=" << block_pufs_name << " etag=" << etag;

  BlockID blk_id = bp.blockid();
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    // https://bits.bytedance.net/meego/cfs/issue/detail/2050743
    // NN can't find block info in the following scenario:
    // 1. NN failover.
    // 2. DN sent upload succeed block report.
    // 3. DN sent full block report.
    LOG(WARNING) << "Inc upload succeed block report B" << blk_id << " from DN"
                 << dn_id << " cannot find matched block info";
    return;
  }

  auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
      s.get(), bi->id(), __FILE__, __LINE__);
  if (!bi_tx_guard->OwnLock()) {
    LOG(INFO) << "LockBlockFailed B" << bi->id();
    return;
  }
  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << blk_id;
    return;
  }
  BlockInfoProto old_bip = bip;
  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "ProcessUploadBlockSucceedUnsafe " << bip.ShortDebugString();
  if (!(bp.blockid() == bip.block_id() &&
        bp.genstamp() == bip.gen_stamp() &&
        bp.numbytes() == bip.num_bytes())) {
    LOG(ERROR) << "BlockProto is mismatched with BlockInfoProto"
               << ", bp: " << bp.blockid() << "/" << bp.genstamp() << "/"
               << bp.numbytes()
               << ", bip: " << PBConverter::ToCompactJsonString(bip);
    return;
  }

  // DNs will use PUT instead of MULTI_PART_UPLOAD to upload small files.
  // So BlockInfoProto::kUploadIssued isn't a MUST state.
  // Don't check it anymore.
  // if (bip.state() != BlockInfoProto::kUploadIssued) {
  // if (bip.curr_upload_id() != upload_id) {
  switch (bip.state()) {
    case BlockInfoProto::kPersisted: {
      if (NameSpace::IsHdfsMode()) {
        CheckHdfsBlock(bip);
        NotifyBlockEvictable(bip, bi);
        return;
      }

      if (NameSpace::IsAccMode()) {
        CheckAccBlock(bip);
        if (bip.upload_type() == cloudfs::datanode::APPEND) {
          NotifyBlockEvictable(bip, bi);
        } else {
          bi_tx_guard->Unlock(__FILE__, __LINE__);

          FinishBipAccMode(upload_id,
                           etag,
                           std::move(cres.second),
                           std::move(bip),
                           std::move(block_map_slice_lock),
                           s.get(),
                           bi);
        }
        return;
      }
    } break;

    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued:
      if (NameSpace::IsAccMode()) {
        CheckAccBlock(bip);
        if (bip.curr_upload_id() != upload_id) {
          VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
              << "bip upload id mismatch, upload_id " << upload_id << " bip "
              << bip.ShortDebugString();
          if (!bip.key_block()) {
            LOG(ERROR) << "Non key block should not be upload B"
                       << bip.block_id();
            return;
          }
          bi_tx_guard->Unlock(__FILE__, __LINE__);
          block_map_slice_lock.unlock();
          NotifyBlockUpload(bip);
          return;
        }
      }

      if (block_pufs_name != bip.pufs_name()) {
        // this may happen during pufs_name pattern changes
        // https://meego.feishu.cn/storage-tob/issue/detail/16871774
        MFC(metrics_.persist_block_pufs_name_mismatch_)->Inc();
        LOG(ERROR) << "PufsName is mismatched, negoed " << bip.pufs_name()
                   << ", uploaded " << block_pufs_name;
        // use the uploaded one, instead of current negoed one
        bip.set_pufs_name(block_pufs_name);
      }

      block_map_slice_lock.unlock();

      bip.set_state(BlockInfoProto::kPersisted);
      if (NameSpace::IsAccMode()) {
        CheckAccBlock(bip);
        if (etag.empty()) {
          LOG_WITH_LEVEL(WARNING)
              << "Etag should not be empty: " << bip.ShortDebugString();
          MFC(LoggerMetrics::Instance().warn_)->Inc();
          return;
        }
        bip.set_etag(etag);
      }

      PersistBlockRequest(
          bip,
          old_bip,
          std::move(cres.second),
          std::move(bi_tx_guard),
          /*evict_in_acc_mode=*/bip.upload_type() == cloudfs::datanode::APPEND);
      break;
    default:
      LOG(ERROR) << "UnexpectedBlock " << PBConverter::ToCompactJsonString(bip);
  }
}

void BlockManager::ProcessUploadBlockFailed(
    const BlockProto& bp,
    DatanodeID dn_id,
    const std::string& upload_id,
    const std::string& block_pufs_name,
    const std::string& etag,
    const ::cloudfs::datanode::FailedMessage& failed_msg) {
  if (!is_active_) {
    return;
  }
  VLOG_OR_IF(5, FLAGS_log_ufs_persist_detail)
      << "ProcessUploadBlockFailed "
      << " bp=" << bp.ShortDebugString() << " dn_id=" << dn_id
      << " upload_id=" << upload_id << " block_pufs_name=" << block_pufs_name
      << " etag=" << etag << " failed_msg=" << failed_msg.ShortDebugString();
  BlockInfoProto bip;
  bool ret = meta_storage_->GetBlockInfo(bp.blockid(), &bip);
  if (!ret) {
    return;
  }
  if (NameSpace::IsAccMode()) {
    ns_->ufs_env()->upload_monitor()->TriggerFilePersist(bip.inode_id(), true);
  }
}

void BlockManager::ProcessPersistNonKeyBlock(const INode& inode,
                                             BlockID block_id) {
  if (!is_active_) {
    return;
  }

  // When processing upload-id-negoed block or upload-succeed block,
  // we may need HAState::barrier_.
  // We should take a lock order convention to avoid deadlock:
  // starting with HAState::barrier, followed by path lock,
  // and finally block map slice lock.
  // See NameSpace::GetBlockLocation and NameSpace::AsyncAddBlock
  // for more information.
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    return;
  }

  auto& s = slice(block_id);
  std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), block_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    LOG(WARNING) << "ProcessPersistNonKeyBlock, block not found. block_id="
                 << block_id;
    return;
  }
  if (!bi->HasBeenCommitted()) {
    LOG(INFO) << "B" << bi->id() << " hasn't been committed.";
    return;
  }

  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(block_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << block_id;
    return;
  }

  VLOG_OR_IF(10, FLAGS_log_ufs_persist_detail)
      << "ProcessPersistNonKeyBlock "
      << " bip=" << bip.ShortDebugString();

  if (bip.key_block()) {
    // skip key block
    return;
  }

  auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
      s.get(), bi->id(), __FILE__, __LINE__);
  if (!bi_tx_guard->OwnLock()) {
    LOG(INFO) << "LockBlockFailed B" << bi->id();
    return;
  }

  block_map_slice_lock.unlock();

  BlockInfoProto old_bip = bip;
  bip.set_state(BlockInfoProto::kPersisted);

  PersistBlockRequest(bip,
                      old_bip,
                      std::move(cres.second),
                      std::move(bi_tx_guard),
                      /*evict_in_acc_mode=*/true);
}

Status BlockManager::AccGetINodeReadOnly(INodeID inode_id, INode* inode) {
  CHECK_NOTNULL(inode);

  auto code = ns_->meta_storage_->GetINode(inode_id, inode);
  if (code == StatusCode::kOK) {
    if (!inode->has_ufs_file_info()) {
      return Status(JavaExceptions::Exception::kFileNotFoundException,
                    absl::StrFormat("Inode has no ufs_file_info %d inode=%s",
                                    inode->id(),
                                    inode->ShortDebugString()));
    }
    return {};
  } else {
    return Status(JavaExceptions::Exception::kFileNotFoundException,
                  absl::StrFormat("File not found: %d", inode_id));
  }
}

// deprecated
bool BlockManager::AccLockBlockAndGetINode(const BlockInfoProto& bip,
                                           BlockMapSlice* s,
                                           INode* inode) {
  s->unlock();

  std::string ignored_path;
  RWLockManager::DirTreeLockPtr path_locks;
  Status status = ns_->GetINodeAndLockPathByFileId(
      bip.inode_id(), &ignored_path, &path_locks, inode, nullptr, false);
  if (!status.IsOK()) {
    LOG(WARNING) << "Failed to lock inode by path " << bip.ShortDebugString()
                 << " res " << status.ToString();
    inode->set_id(kInvalidINodeId);
    s->lock();
    s->TellLockHolder(__FILE__, __LINE__);
    return true;
  }
  CHECK(inode->has_ufs_file_info());

  s->lock();
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoProto bip_checker;
  if (!meta_storage_->GetBlockInfo(bip.block_id(), &bip_checker)) {
    LOG(WARNING) << "BIP not found " << bip.ShortDebugString();
    inode->set_id(kInvalidINodeId);
    return true;
  }
  return bip_checker.state() == bip.state();
}

bool BlockManager::AccLockBlockAndGetPrevBlock(const BlockInfoProto& bip,
                                               BlockMapSlice* s,
                                               BlockInfoProto* prev_bip) {
  if (bip.pufs_offset() == 0) {
    // Dummy prev block
    prev_bip->set_state(BlockInfoProto::kPersisted);
    return true;
  }

  s->unlock();

  INode inode;
  std::string ignored_path;
  RWLockManager::DirTreeLockPtr path_locks;
  Status status = ns_->GetINodeAndLockPathByFileId(
      bip.inode_id(), &ignored_path, &path_locks, &inode, nullptr, false);
  if (!status.IsOK()) {
    LOG(WARNING) << "Failed to lock inode by path " << bip.ShortDebugString()
                 << " res " << status.ToString();
    prev_bip->set_block_id(kInvalidBlockID);
    s->lock();
    s->TellLockHolder(__FILE__, __LINE__);
    return true;
  }
  CHECK(inode.has_ufs_file_info());

  int64_t prev_bid = kInvalidBlockID;
  for (int i = 0; i + 1 < inode.blocks_size(); i++) {
    prev_bid = inode.blocks(i).blockid();
    if (inode.blocks(i + 1).blockid() == bip.block_id()) {
      break;
    }
  }
  auto&& prev_s = slice(prev_bid);
  prev_s->lock_shared();
  bool found_prev = meta_storage_->GetBlockInfo(prev_bid, prev_bip);
  prev_s->unlock_shared();
  if (!found_prev) {
    LOG(WARNING) << "Prev BIP not found " << inode.ShortDebugString() << " "
                 << bip.ShortDebugString();
    prev_bip->set_block_id(kInvalidBlockID);
    s->lock();
    s->TellLockHolder(__FILE__, __LINE__);
    return true;
  }

  s->lock();
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoProto bip_checker;
  if (!meta_storage_->GetBlockInfo(bip.block_id(), &bip_checker)) {
    LOG(WARNING) << "BIP not found " << bip.ShortDebugString();
    prev_bip->set_block_id(kInvalidBlockID);
    return true;
  }
  return bip_checker.state() == bip.state();
}

bool BlockManager::GetAccBlockPersistInfoOrRetryUpload(
    BlockID id,
    Ufs* ufs,
    const UfsFileInfoProto& ufs_info,
    bool is_last_block,
    BlockInfoProto* bip_out) {
  auto& s = slice(id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  RPC_SW_CTX_INIT(rpc_sw_ctx,
                  "[GetAccBlockPersistInfoOrRetryUpload]",
                  absl::StrFormat("block_id=%d", id));
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  BlockInfoGuard bi_guard(s.get(), id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << id;
    return false;
  }
  CHECK_NOTNULL(bi);
  BlockInfoProto bip;
  CHECK(meta_storage_->GetBlockInfo(bi->id(), &bip));
  bip_out->CopyFrom(bip);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "GetBlockInfo");

  const auto& pufs_name = ufs->GetBlockPufsName(ufs_info, bip);
  const auto& upload_id = ufs_info.upload_id();

  if (!bip.key_block()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Block is not key block " << bip.ShortDebugString();
    if (!is_last_block) {
      return true;
    }

    // In rare cases, acc block info for last block is not set.
    // i.g. AddBlock 1 -> AddBlock 2 -> AbandonBlock 2 -> Close file
    // Block 1 is not key block in old implementation.
    LOG(INFO) << "Last block is not key block " << bip.ShortDebugString();
    auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
        s.get(), bi->id(), __FILE__, __LINE__);
    if (!bi_tx_guard->OwnLock()) {
      LOG(WARNING) << "LockBlockFailed B" << id;
      return false;
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "BlockInfoInTransactionGuard");

    guard.unlock();

    INode inode;
    meta_storage_->GetINode(bip.inode_id(), &inode);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "GetINode");

    ns_->SetBlockAccInfo(&inode, &bip, true);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "SetBlockAccInfo");

    BlockInfoProto old_bip = bip;
    bip.set_pufs_name(pufs_name);
    bip.set_curr_upload_id(upload_id);
    bip.clear_etag();

    int64_t txid = edit_log_sender_->LogAccUpdateBlockInfo(bip, old_bip);
    CHECK_NE(txid, kInvalidTxId);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "LogAccUpdateBlockInfo");

    SynchronizedRpcClosure done;
    meta_storage_->OrderedUpdateUfsBlockInfo(txid, bip, &old_bip, &done);
    done.Await();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "OrderedUpdateUfsBlockInfo");

    guard.lock();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "relock");
    bi = s->Locate(id);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "Locate");
    if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
      LOG(ERROR) << "block not exist, block_id=" << id;
      return false;
    }
    CHECK_NOTNULL(bi);
    CHECK(bi_tx_guard->Unlock(__FILE__, __LINE__));
    RPC_SW_CTX_LOG(rpc_sw_ctx, "Unlock");
  }
  if (IsBlockReadyToPersisted(bip, pufs_name, upload_id)) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "IsBlockReadyToPersisted");
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Block has upload with correct upload id " << bip.ShortDebugString();
    return true;
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "IsBlockReadyToPersisted");

  if (bip.curr_upload_id() != upload_id || bip.pufs_name() != pufs_name) {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "!= upload_id");

    auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
        s.get(), bi->id(), __FILE__, __LINE__);
    if (!bi_tx_guard->OwnLock()) {
      LOG(WARNING) << "LockBlockFailed B" << id;
      return false;
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "BlockInfoInTransactionGuard");

    guard.unlock();

    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Upload id or pufs name mismatch. update bip "
        << bip.ShortDebugString() << ". new upload id " << upload_id
        << " new pufs name " << pufs_name;

    BlockInfoProto old_bip = bip;
    bip.set_pufs_name(pufs_name);
    bip.set_curr_upload_id(upload_id);
    bip.clear_etag();

    int64_t txid = edit_log_sender_->LogAccUpdateBlockInfo(bip, old_bip);
    CHECK_NE(txid, kInvalidTxId);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "LogAccUpdateBlockInfo");

    SynchronizedRpcClosure done;
    meta_storage_->OrderedUpdateUfsBlockInfo(txid, bip, &old_bip, &done);
    done.Await();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "OrderedUpdateUfsBlockInfo");

    guard.lock();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "relock");
    bi = s->Locate(id);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "Locate");
    CHECK_NOTNULL(bi);
    if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
      LOG(ERROR) << "block not exist, block_id=" << id;
      return false;
    }
    CHECK(bi_tx_guard->Unlock(__FILE__, __LINE__));
    RPC_SW_CTX_LOG(rpc_sw_ctx, "Unlock");
  } else if (!bip.has_etag() || bip.etag().empty()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "Upload etag is empty, bip=" << bip.ShortDebugString();
  }

  if (!bip.has_dn_uuid()) {
    VLOG_OR_IF(8, FLAGS_log_ufs_persist_detail)
        << "No dn applied upload for this block " << bip.ShortDebugString();
    return false;
  }

  guard.unlock();
  NotifyBlockUpload(bip);
  RPC_SW_CTX_LOG(rpc_sw_ctx, "NotifyBlockUpload");

  return false;
}

void BlockManager::ProcessMergedBlock(
    const BlockProto& bp,
    DatanodeID dn_id,
    const cloudfs::DatanodeStorageProto& storage,
    const std::string& block_pufs_name) {
  if (!is_active_) {
    LOG(ERROR) << "ProcessMergedBlock is not active";
    return;
  }

  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    LOG(ERROR) << "ProcessMergedBlock ha check failed";
    return;
  }

  auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
  if (dn == nullptr || !dn->CheckAndUpdateHeartbeat()) {
    LOG(ERROR) << "ProcessMergedBlock from dead dn " << dn->uuid();
    return;
  }

  VLOG(8) << "ProcessMergedBlock start for B" << bp.blockid();

  INodeInPath iip;

  std::string path;
  std::vector<INode> ancestors;
  RpcClosure* done = NewRpcCallback();
  ClosureGuard guard(done);
  {
    BlockInfoProto bip;
    if (!meta_storage_->GetBlockInfo(bp.blockid(), &bip)) {
      LOG(WARNING) << "Failed to get bip for merged block B" << bp.blockid();
      return;
    }
    std::string ignored;
    if (ns_->GetLastINodeInPathByFileId(ignored,
                                        bip.inode_id(),
                                        &path,
                                        &iip,
                                        /*ancestors=*/nullptr,
                                        done,
                                        /*write_lock=*/true) != kOK) {
      LOG(WARNING) << "Failed to get inode for merged block B" << bp.blockid()
                   << " inode " << bip.inode_id();
      return;
    }
  }
  INode& inode = iip.MutableInode();
  const INode old_inode = iip.OldInode();

  size_t merge_task_idx = 0;
  MergeBlockContext merge_task;
  for (auto& t : inode.mergingblocks()) {
    if (t.block().blockid() == bp.blockid()) {
      merge_task = t;
      break;
    }
    merge_task_idx++;
  }

  if (merge_task_idx == inode.mergingblocks_size()) {
    LOG(INFO) << "Merge task not found in inode";
    return;
  }

  inode.mutable_mergingblocks()->DeleteSubrange(merge_task_idx, 1);

  auto& merged_blk = merge_task.block();
  if (merged_blk.blockid() != bp.blockid() ||
      merged_blk.numbytes() != bp.numbytes() ||
      merged_blk.generationstamp() != bp.genstamp()) {
    LOG_WITH_LEVEL(ERROR) << "Merged block mismatch, ours: "
                          << merged_blk.ShortDebugString()
                          << " theirs: " << bp.ShortDebugString();
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }

  auto& s = slice(bp.blockid());
  std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), bp.blockid(), true);
  BlockInfo* bi = bi_guard.GetBlockInfo();

  if (!bi) {
    LOG_WITH_LEVEL(WARNING)
        << "Inc upload id negoed block report B" << bp.blockid() << " from DN"
        << dn_id << " cannot find matched block info";
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return;
  }

  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(bi->id(), &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << bi->id();
    return;
  }

  auto bi_tx_guard = std::make_shared<BlockInfoInTransactionGuard>(
      s.get(), bi->id(), __FILE__, __LINE__);
  if (!bi_tx_guard->OwnLock()) {
    LOG(ERROR) << "LockBlockFailed B" << bi->id();
    return;
  }

  if (bip.num_bytes() != bp.numbytes() || bip.gen_stamp() != bp.genstamp() ||
      bip.pufs_name() != block_pufs_name) {
    LOG_WITH_LEVEL(ERROR) << "Merged block mismatch, metastorage: "
                          << bip.ShortDebugString()
                          << " theirs: " << bp.ShortDebugString();
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return;
  }

  VLOG(10) << "ProcessMergedBlock check finished B" << bp.blockid();

  // Simulate IBR process when block is received on DN, to make the block state
  // correct in BlockManager
  Block b{bp.blockid(), static_cast<uint32_t>(bp.numbytes()), bp.genstamp()};
  ReportedReplicaResult res =
      s->ProcessReportedBlock(dn_id,
                              b,
                              ReplicaStateProto::FINALIZED,
                              is_active_,
                              dn->IsDecommissionInProgress(),
                              CountReplica(bi).live);
  if (res != ADD) {
    LOG(ERROR) << "ProcessMergedBlock wrong IBR result " << res << " B"
               << bp.blockid();
    return;
  }
  if (!s->AddStorage(bi, dn_id)) {
    LOG(ERROR) << "ProcessMergedBlock failed to add storage B" << bp.blockid();
    return;
  }
  // Do not do data_manager_->AddBlocks(), because
  // 1) BlockIndex only used for FBR, and will be corrected during FBR
  // 2) Might cause deadlock when acquiring both BlockMapSlice and DatanodeInfo
  // Hopefully this is correct :)

  // Simulate client action when addBlock to make block state correct in
  // BlockManager
  if (!CommitOrCompleteLastBlockUnsafe(b, s.get(), bi, false)) {
    LOG(ERROR) << "ProcessMergedBlock failed to commit complete B"
               << bp.blockid();
    return;
  }

  switch (FLAGS_namespace_type) {
    case cloudfs::NamespaceType::TOS_MANAGED: {
      if (!PersistBlockUnsafe(s.get(), b)) {
        LOG(ERROR) << "ProcessMergedBlock failed to persist B" << bp.blockid();
        return;
      }
      if (!bi->IsPersisted()) {
        LOG_WITH_LEVEL(ERROR) << "ProcessMergedBlock failed B" << bp.blockid()
                              << " not persisted";
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return;
      }
      bip.set_state(BlockInfoProto::kPersisted);
    } break;
    case cloudfs::NamespaceType::LOCAL:
    case cloudfs::NamespaceType::TOS_LOCAL: {
      if (!bi->HasBeenComplete()) {
        LOG_WITH_LEVEL(ERROR) << "ProcessMergedBlock failed B" << bp.blockid()
                              << " not completed";
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return;
      }
      bip.set_state(BlockInfoProto::kComplete);
    } break;
    default: {
      LOG_WITH_LEVEL(ERROR) << "Should not merge for this ns type";
      MFC(LoggerMetrics::Instance().error_)->Inc();
    } break;
  }

  std::vector<BlockProto> to_remove;
  to_remove.resize(merge_task.oldblocks_size());
  for (size_t i = 0; i < merge_task.oldblocks_size(); i++) {
    to_remove[i].CopyFrom(merge_task.oldblocks(i));
  }
  if (to_remove.size() < 2) {
    LOG_WITH_LEVEL(ERROR) << "Failed to find to_remove blocks in inode "
                          << inode.ShortDebugString() << " merge task "
                          << merge_task.ShortDebugString();
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }

  size_t orig_idx = 0, remove_idx = 0, first_remove_idx = 0;
  uint64_t offset = 0, length = 0;

  // Find first to_remove block.
  while (orig_idx < inode.blocks_size()) {
    if (inode.blocks(orig_idx).blockid() == to_remove[0].blockid()) {
      break;
    }
    offset += inode.blocks(orig_idx).numbytes();
    orig_idx++;
  }
  first_remove_idx = orig_idx;

  // Check to_remove blocks are consistent with blocks in inode
  while (remove_idx < to_remove.size()) {
    if (inode.blocks(orig_idx).blockid() != to_remove[remove_idx].blockid()) {
      LOG_WITH_LEVEL(ERROR)
          << "Merged block mismatch inode " << inode.ShortDebugString()
          << " merge task " << merge_task.ShortDebugString();
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return;
    }
    length += inode.blocks(orig_idx).numbytes();
    orig_idx++;
    remove_idx++;
  }

  if (offset != merged_blk.offset() || length != merged_blk.numbytes()) {
    LOG_WITH_LEVEL(ERROR) << "Offset or length mismatch, source: " << offset
                          << " " << length
                          << ", target: " << merged_blk.offset() << " "
                          << merged_blk.numbytes();
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }

  inode.mutable_blocks(first_remove_idx)->CopyFrom(bp);
  inode.mutable_blocks()->DeleteSubrange(first_remove_idx + 1,
                                         to_remove.size() - 1);
  for (auto& b : to_remove) {
    inode.add_mergedblocks()->CopyFrom(b);
  }

  VLOG(10) << "ProcessMergedBlock process inode finished " << inode.id();

  guard.release();
  done->set_callback(
      [bip, bi_tx_guard = std::move(bi_tx_guard), this](const Status& ignored) {
        // guard
        {
          BlockID blk_id = bip.block_id();
          auto& s = slice(blk_id);
          std::unique_lock<BlockMapSlice> guard(*s);
          s->TellLockHolder(__FILE__, __LINE__);
          BlockInfoGuard bi_guard(s.get(), blk_id, true);
          BlockInfo* bi = bi_guard.GetBlockInfo();
          if (bi == nullptr) {
            MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
            LOG_WITH_LEVEL(ERROR) << "Persist missing B" << blk_id;
            return;
          }
          if (!bi_tx_guard->Unlock(__FILE__, __LINE__)) {
            MFC(LoggerMetrics::Instance()
                    .unlock_block_info_in_transaction_lock_failed_)
                ->Inc();
            LOG_WITH_LEVEL(ERROR)
                << "Unlock in transaction lock failed, B" << blk_id;
          }
        }

        switch (FLAGS_namespace_type) {
          case cloudfs::NamespaceType::TOS_MANAGED:
            // Do nothing
            break;
          case cloudfs::NamespaceType::LOCAL:
          case cloudfs::NamespaceType::TOS_LOCAL: {
            INode inode;
            if (!FLAGS_block_expected_replica_determined_by_inode ||
                ns_ == nullptr || !ns_->GetINode(bip.inode_id(), &inode)) {
              inode.set_id(kInvalidINodeId);
            }

            std::vector<BlockProto> bp;
            bp.resize(1);
            bp.front().set_blockid(bip.block_id());
            bp.front().set_genstamp(bip.gen_stamp());
            bp.front().set_numbytes(bip.num_bytes());
            CheckReplica(bp, &inode, nullptr);
          } break;
          default:
            LOG(ERROR) << "Should not merge for this ns type";
            MFC(LoggerMetrics::Instance().error_)->Inc();
            break;
        }
    VLOG(8) << "ProcessMergedBlock finished " << bip.ShortDebugString();
  });

  std::vector<INodeID> ancestors_id;
  std::for_each(ancestors.begin(),
                ancestors.end(),
                [&ancestors_id] (INode inode) {
                  ancestors_id.push_back(inode.id());
                });
  SnapshotLog inode_snaplog;
  iip.GenerateSnapshotLog(&inode_snaplog);

  int64_t txid = kInvalidTxId;
  txid = edit_log_sender_->LogMergeBlocks(path,
                                          inode,
                                          bip,
                                          std::vector<BlockProto>(),
                                          old_inode,
                                          ancestors_id,
                                          inode_snaplog);
  CHECK_NE(txid, kInvalidTxId);

  done->set_barrier(std::move(cres.second));
  meta_storage_->OrderedUpdateINodeMergeBlock(&iip.MutableInode(),
                                              &old_inode,
                                              bip,
                                              std::vector<BlockProto>(),
                                              inode_snaplog,
                                              txid,
                                              done);
}

// already get slice lock
void BlockManager::ProcessCheckWriteCacheINodeState(BlockMapSlice* s,
                                                    const BlockID block_id,
                                                    DatanodeInfoPtr dn) {
  if (!NameSpace::IsAccMode()) {
    return;
  }

  auto dn_id = dn->id();
  auto dn_uuid = dn->uuid();

  BlockInfoGuard bi_guard(s, block_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    VLOG(10) << "ProcessCheckWriteCacheINodeState B" << block_id << " from DN"
             << dn_id << " cannot find matched block info";
    return;
  }
  if (!bi->HasBeenCommitted()) {
    LOG(INFO) << "B" << bi->id() << " hasn't been committed.";
    return;
  }

  VLOG(10) << "ProcessCheckWriteCacheINodeState "
           << " dn_id=" << dn_id << " dn_uuid=" << dn_uuid;

  BlockInfoProto bip;
  if (!meta_storage_->GetBlockInfo(block_id, &bip)) {
    LOG(ERROR) << "MissingBlockInMetaStorage B" << block_id;
    return;
  }

  VLOG(10) << "ProcessCheckWriteCacheINodeState "
           << " dn_id=" << dn_id << " dn_uuid=" << dn_uuid
           << " bp=" << bip.ShortDebugString();

  if (bip.upload_type() == cloudfs::datanode::APPEND) {
    VLOG(10) << "ProcessCheckWriteCacheINodeState, bip is append."
             << " bip=" << bip.ShortDebugString()
             << " dn=" << dn->ToShortString();
    if (bip.state() == BlockInfoProto::kPersisted) {
      NotifyBlockEvictable(bip, bi);
    }
    return;
  }

  INode inode;
  auto status = AccGetINodeReadOnly(bip.inode_id(), &inode);
  if (status.HasException()) {
    LOG(WARNING) << "INode not found for block " << bip.ShortDebugString();
    return;
  }
  if (inode.ufs_file_info().file_state() ==
      UfsFileState::kUfsFileStatePersisted) {
    VLOG(10) << "ProcessCheckWriteCacheINodeState, inode is persisted."
             << " inode=" << inode.ShortDebugString()
             << " bip=" << bip.ShortDebugString()
             << " dn=" << dn->ToShortString();
    NotifyBlockEvictable(bip, bi);
    return;
  }
}

bool BlockManager::FillUploadCmdAccMode(const BlockInfoProto& bip,
                                        UploadCmd* cmd) {
  CheckAccBlock(bip);

  cmd->set_uploadtype(bip.upload_type());
  cmd->set_appendoffset(bip.pufs_offset());
  cmd->add_partnums(bip.part_num());
  if (bip.bundle_length() > 0) {
    INode inode;
    if (ns_->meta_storage()->GetINode(bip.inode_id(), &inode) != kOK) {
      LOG(ERROR) << "Failed to get INode of block " << bip.block_id();
      return false;
    }
    ReadAdvice advice(kDefaultReadPolicy);
    UserGroupInfo default_ugi = UserGroupInfo();
    std::vector<AccessMode> modes{AccessMode::READ,
                                  AccessMode::WRITE,
                                  AccessMode::COPY,
                                  AccessMode::REPLACE};
    cloudfs::LocatedBlocksProto lbs;
    // NO path lock, so the following method might fail
    // It is acceptable that DN get a block with no locations
    ns_->CreateLocatedBlocks(inode,
                             bip.bundle_offset(),
                             bip.bundle_length(),
                             cloudfs::IoMode::DATANODE_BLOCK,
                             advice,
                             // ACC does not support snapshot read.
                             /*is_snapshot_read=*/false,
                             false,
                             NetworkLocationInfo(),
                             default_ugi,
                             modes,
                             &lbs);
    for (auto lb : lbs.blocks()) {
      auto t = cmd->add_prevblocks();
      *(t) = lb;
    }
  }
  return true;
}

}  // namespace dancenn
