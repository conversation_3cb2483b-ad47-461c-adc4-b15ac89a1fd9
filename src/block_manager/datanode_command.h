// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/01
// Description

#ifndef BLOCK_MANAGER_DATANODE_COMMAND_H_
#define BLOCK_MANAGER_DATANODE_COMMAND_H_

#include <cstddef>
#include <cstdint>
#include <functional>
#include <unordered_set>

#include "glog/logging.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"

namespace dancenn {

// TODO(ruanjunbin): Test.
template <typename T>
struct DnCmdHash {
  std::size_t operator()(const T& cmd) const {
    if (!cmd.has_block() || !cmd.block().has_blockid()) {
      return 0;
    }
    return std::hash<uint64_t>()(cmd.block().blockid());
  }
};

// TODO(ruanjunbin): Test.
template <typename T>
struct DnCmdEqualTo {
  bool operator()(const T& lhs, const T& rhs) const {
    bool lhs_is_invalid = !lhs.has_block() || !lhs.block().has_blockid();
    bool rhs_is_invalid = !rhs.has_block() || !rhs.block().has_blockid();
    if (lhs_is_invalid || rhs_is_invalid) {
      LOG(ERROR) << "UploadCmdEqualTo found invalid cmd.";
    }
    if (lhs_is_invalid != rhs_is_invalid) {
      return false;
    }
    // We consider invalid cmds are equal.
    if (lhs_is_invalid) {
      return true;
    }
    return lhs.block().blockid() == rhs.block().blockid();
  }
};

// TODO(ruanjunbin): Test set properties.

using UploadCmd = cloudfs::datanode::UploadCommandProto;
using UploadCmdSet = std::
    unordered_set<UploadCmd, DnCmdHash<UploadCmd>, DnCmdEqualTo<UploadCmd>>;

using NotifyEvictableCmd = cloudfs::datanode::NotifyEvictableCommandProto;
using NotifyEvictableCmdSet =
    std::unordered_set<NotifyEvictableCmd,
                       DnCmdHash<NotifyEvictableCmd>,
                       DnCmdEqualTo<NotifyEvictableCmd>>;

using InvalidatePufsCmd = cloudfs::datanode::InvalidatePufsCommandProto;
using LoadCmd = cloudfs::datanode::LoadCommandProto;

using MergeCmd = cloudfs::datanode::MergeCommandProto;

}  // namespace dancenn

#endif  // BLOCK_MANAGER_DATANODE_COMMAND_H_
