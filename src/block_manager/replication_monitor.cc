// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "replication_monitor.h"

#include <glog/logging.h>

#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(replication_monitor_interval_ms);

namespace dancenn {

void ReplicationMonitor::Do() {
  StopWatch sw;
  sw.Start();

  LOG(INFO) << "Start to do some replication works.";
  if (bm_->safemode_->IsOn()) {
    LOG(INFO) << "Skip due to safemode is on.";
    return;
  }
  if (bm_->IsPopulatingReplicationQueues()) {
    bm_->ComputeDatanodeWork();
    LOG(INFO) << "Compute datanode time: "
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    bm_->ProcessPendingReplications();
    LOG(INFO) << "Process pending replications time: "
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    bm_->RescanPostponedMisreplicatedBlocks();
    LOG(INFO) << "RescanPostponedMisreplicatedBlocks time: "
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  LOG(INFO) << "Replication monitor time: "
            << StringUtils::FormatWithCommas(sw.NextStepTime()) << "(us)";
}

bool ReplicationMonitor::Task::operator()(void* arg) {
  (void) arg;

  while (!stop_) {
    monitor_->Do();

    int interval = FLAGS_replication_monitor_interval_ms;
    LOG(INFO) << "Replication monitor will sleep: " << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    if (stop_) {
      break;
    }
    monitor_->cond_.wait_for(lock, period, [this] () -> bool { return stop_; });
  }
  return true;
}

}  // namespace dancenn

