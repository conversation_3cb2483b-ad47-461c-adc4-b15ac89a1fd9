// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCK_MANAGER_METRICS_H_
#define BLOCK_MANAGER_BLOCK_MANAGER_METRICS_H_

#include <memory>
#include <shared_mutex>

#include "base/metrics.h"
#include "base/read_write_lock.h"
#include "under_replicated_blocks.h"

namespace dancenn {

class BlockManager;

struct BlockManagerMetrics {
  explicit BlockManagerMetrics(BlockManager* bm);

  ~BlockManagerMetrics() = default;
  BlockManagerMetrics(const BlockManagerMetrics&) = delete;
  BlockManagerMetrics& operator=(const BlockManagerMetrics&) = delete;

  void IncBlockTransTraffic(const std::string& src_dc,
                            const std::string& tgt_dc,
                            const std::string& type,
                            int64_t num_bytes);

  std::shared_ptr<Metrics> metrics_;

  std::shared_ptr<Gauge> num_blocks_;
  std::shared_ptr<Gauge> num_uc_;
  std::shared_ptr<Gauge> num_postponed_misreplicated_;
  std::shared_ptr<Gauge> num_invalidate_;
  std::shared_ptr<Gauge> num_truncatable_;
  std::shared_ptr<Gauge> num_corrupt_;
  std::shared_ptr<Gauge> num_sealed_;
  std::shared_ptr<Gauge> num_recover_;
  std::shared_ptr<Gauge> num_excess_;
  std::shared_ptr<Gauge> num_in_replication_queue_;
  std::shared_ptr<Gauge> num_future_blocks_;
  std::shared_ptr<Gauge> num_misinvalidated_blocks_;
  std::vector<std::shared_ptr<Gauge>> num_under_replicated_;
  std::shared_ptr<Gauge> num_pending_replication_blocks_;

  // ccp_last_block = CommitOrCompleteOrPersistLastBlock
  MetricID ccp_last_block_lock_acquire_time_;
  MetricID ccp_last_block_locate_time_;
  MetricID ccp_last_block_commit_time_;
  MetricID ccp_last_block_complete_time_;
  MetricID ccp_last_block_inc_safe_block_count_time_;

  MetricID add_block_lock_acquire_time_;
  MetricID add_block_allocate_element_time_;
  MetricID add_block_insert_into_bucket_time_;
  MetricID add_block_add_storage_time_;
  MetricID add_block_insert_uc_state_time_;

  MetricID repl_work_choose_under_repl_blks_time_;
  MetricID repl_work_lock_acquire_time_;
  MetricID repl_work_get_block_info_time_;
  MetricID repl_work_get_placement_advice_time_;
  MetricID repl_work_choose_src_dn_time_;
  MetricID repl_work_has_enough_rack_time_;
  MetricID repl_work_build_src_path_;
  MetricID repl_work_choose_tgt_dn_time_;
  MetricID repl_work_add_to_be_replicated_time_;

  // compute replication work
  MetricID repl_work_all_scheduled_counter_;
  // move dc
  MetricID repl_work_scheduled_move_dc_counter_;
  // decommission
  MetricID repl_work_scheduled_decommission_counter_;
  // under replicated or not enough rack
  MetricID repl_work_scheduled_normal_counter_;

  // compute replication work failed reason
  MetricID repl_work_choose_src_failed_counter_;
  MetricID repl_work_choose_target_failed_counter_;
  MetricID repl_work_no_need_replication_counter_;
  MetricID repl_work_cross_dc_throttled_counter_;

  std::array<MetricID, UnderReplicatedBlocks::kPriorityCount>
      repl_work_processed_blk_counter_;
  MetricID repl_work_move_back_to_queue_counter_;

  MetricID persist_block_fail_count_;
  MetricID persist_block_succ_count_;
  MetricID persist_block_pufs_name_mismatch_;

  MetricID scan_slice_lock_acquire_time_;
  MetricID scan_slice_release_blocks_time_;

  MetricID enqueue_blk_log_time_;
  // from, to, type->count/bytes
  std::unordered_map<std::string, std::pair<MetricID, MetricID>> blk_trans_;
  ReadWriteLock blk_trans_mutex_;

  // fbr: BlockReport
  MetricID fbr_decode_blocks_failed_;

  // ibr: IncrementalBlockReport
  MetricID ibr_evicted_ops_;
  MetricID ibr_sealed_ops_;  // deprecated
  MetricID ibr_deleted_ops_;
  MetricID ibr_received_ops_;
  MetricID ibr_receiving_ops_;
  MetricID ibr_upload_id_negoed_ops_;
  MetricID ibr_upload_succeed_ops_;
  MetricID ibr_upload_failed_ops_;
  MetricID ibr_load_failed_ops_;
  MetricID ibr_pufs_deleted_ops_;
  //
  MetricID ibr_total_time_;
  MetricID ibr_storage_process_time_;
  MetricID ibr_block_index_update_time_;
  MetricID ibr_lock_acquire_time_;
  MetricID ibr_deleted_blk_process_time_;
  MetricID ibr_sealed_blk_process_time_;
  MetricID ibr_evicted_blk_process_time_;
  MetricID ibr_received_blk_process_time_;
  MetricID ibr_receiving_blk_process_time_;
  MetricID ibr_upload_id_negoed_blk_process_time_;
  MetricID ibr_upload_succeed_blk_process_time_;
  MetricID ibr_upload_failed_blk_process_time_;
  MetricID ibr_pufs_deleted_blk_process_time_;

  // NN drive: nego
  MetricID upload_nego_ops_approve_;
  MetricID upload_nego_ops_deny_;
  MetricID upload_nego_ops_notify_upload_;
  MetricID upload_nego_ops_notify_evictable_;
  MetricID upload_nego_ops_persist_evictable_;

  // pusb: ProcessUploadSucceedBlock
  MetricID pusb_callback_persist_block_time_;
  MetricID pusb_callback_process_over_replica_time_;
  MetricID pusb_callback_add_notify_evictable_cmd_time_;  // Deprecated

  // JobManager
  MetricID load_cmd_block_count_;
  MetricID add_transfer_block_count_;
  MetricID add_transfer_block_failed_count_;

  // datanode cmd
  MetricID dn_cmd_cnt_balancer_;  // unused
  MetricID dn_cmd_cnt_block_transfer_;
  MetricID dn_cmd_cnt_block_invalidate_;
  MetricID dn_cmd_cnt_block_shutdown_;   // shutdown datanode
  MetricID dn_cmd_cnt_block_finalized_;  // truncate and finalized block
  MetricID dn_cmd_cnt_block_recovery_;
  MetricID dn_cmd_cnt_finalize_;  // finalize upgrade, unused
  MetricID dn_cmd_cnt_key_update_;
  MetricID dn_cmd_cnt_register_;
  MetricID dn_cmd_cnt_unused_upgrade_;    // unused
  MetricID dn_cmd_cnt_null_datanode_;     // unused
  MetricID dn_cmd_cnt_block_id_cache_;    // unused
  MetricID dn_cmd_cnt_block_id_uncache_;  // unused
  MetricID dn_cmd_cnt_block_id_lifecycle_hot_;
  MetricID dn_cmd_cnt_block_id_lifecycle_warm_;
  MetricID dn_cmd_cnt_block_id_lifecycle_cold_;
  MetricID dn_cmd_cnt_block_id_pin_;
  MetricID dn_cmd_cnt_block_id_unpin_;
  MetricID dn_cmd_cnt_upload_;
  MetricID dn_cmd_cnt_notify_evictable_;
  MetricID dn_cmd_cnt_invalidate_pufs_;
  MetricID dn_cmd_cnt_block_report_;
  MetricID dn_cmd_cnt_merge_;
  MetricID dn_cmd_cnt_abort_block_report_;
  MetricID dn_cmd_cnt_load_;  // cache load data

  std::shared_ptr<Gauge> bm_uploader_ongoing_;
  std::shared_ptr<Gauge> bm_uploader_pending_;
  std::shared_ptr<Gauge> bm_evict_deleter_ongoing_;
  std::shared_ptr<Gauge> bm_evict_deleter_pending_;

  // datanode cmd time
  MetricID dn_cmd_time_total_;
  MetricID dn_cmd_time_block_recover_;
  MetricID dn_cmd_time_block_transfer_;
  MetricID dn_cmd_time_block_invalidate_;
  MetricID dn_cmd_time_lifecycle_;
  MetricID dn_cmd_time_key_update_;
  MetricID dn_cmd_time_upload_;
  MetricID dn_cmd_time_notify_evictable_;
  MetricID dn_cmd_time_load_;
  MetricID dn_cmd_time_invalidate_pufs_;
  MetricID dn_cmd_time_block_id_;
  MetricID dn_cmd_time_merge_;
  MetricID dn_cmd_time_truncatable_;

 private:
  BlockManager* bm_{nullptr};
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_MANAGER_METRICS_H_

