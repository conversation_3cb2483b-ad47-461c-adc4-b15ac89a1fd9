// Copyright 2019 <PERSON> <<EMAIL>>

#include "block_manager/misinvalidated_block_monitor.h"

#include <glog/logging.h>

#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(misinvalidated_block_monitor_interval_ms);

namespace dancenn {

void MisinvalidatedBlockMonitor::Do() {
  LOG(INFO) << "Start to do some misinvalidated block count works.";
  if (bm_->safemode_->IsOn()) {
    LOG(INFO) << "Skip due to safemode is on.";
    return;
  }
  auto start = std::chrono::steady_clock::now();
  uint64_t num = bm_->ProcessAllPendingMisinvalidatedBlks();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now() - start).count();
  LOG(INFO) << "Misinvalidated block monitor time: " << duration << "(ms)"
    << ", num: " << num;
}

bool MisinvalidatedBlockMonitor::Task::operator()(void* arg) {
  (void) arg;

  while (!stop_) {
    monitor_->Do();

    int interval = FLAGS_misinvalidated_block_monitor_interval_ms;
    LOG(INFO) << "Misinvalidated block monitor will sleep: " 
      << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this] () -> bool { return stop_; });
  }
  return true;
}

}  // namespace dancenn

