// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCK_INFO_H_
#define BLOCK_MANAGER_BLOCK_INFO_H_

#include <cstdint>
#include <cstdlib>
#include <limits>
#include <string>

#include "base/constants.h"
#include "block_manager/block.h"
#include "block_manager/block_info_proto.h"
#include "datanode_manager/datanode_info.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"

namespace dancenn {

class BlockMapSlice;

enum class BlockUCState : int8_t {
  kComplete = 0,
  kUnderConstruction = 1,
  kUnderRecovery = 2,
  kCommitted = 3,
  kPersisted = 4,
};

std::string BlockUCStateToString(const BlockUCState& s);

bool HasBeenCommitted(BlockUCState s);
bool HasBeenComplete(BlockUCState s);

#define PACKED __attribute__((packed))

// instance of the struct could have variable length
// NOTICE: Not thread safe, protected by BlockMapSlice lock
class BlockInfo {
 public:
  BlockInfo()
      : size_(0), write_mode_(cloudfs::DATANODE_BLOCK), in_transaction_(false) {
  }
  explicit BlockInfo(BlockID id)
      : blk_(id, 0, kLastReservedGenerationStamp),
        write_mode_(cloudfs::DATANODE_BLOCK),
        in_transaction_(false) {
  }
  explicit BlockInfo(const BlockInfo& bi)
      : blk_(bi.blk_),
        inode_id_(bi.inode_id_),
        parent_id_(bi.parent_id_),
        expected_rep_(bi.expected_rep_),
        write_mode_(cloudfs::DATANODE_BLOCK),
        in_transaction_(false) {
  }

  const Block& blk() const { return blk_; }
  BlockInfo* next() const { return next_; }
  uint8_t capacity() const { return capacity_; }
  uint8_t size() const { return size_; }
  uint8_t expected_rep() const { return expected_rep_; }
  BlockUCState uc_state() const { return uc_state_; }
  uint64_t id() const { return blk_.id; }
  uint64_t inode_id() const { return inode_id_; }
  uint64_t parent_id() const { return parent_id_; }
  uint32_t num_bytes() const { return blk_.num_bytes; }
  uint64_t gs() const { return blk_.gs; }
  cloudfs::IoMode write_mode() const {
    return write_mode_;
  }

  DatanodeID storage_id(size_t index) const;
  const DatanodeID* storage_ids() const {
    return reinterpret_cast<const DatanodeID*>(&first_storage_id_);
  }
  DatanodeID* storage_ids() {
    return reinterpret_cast<DatanodeID*>(&first_storage_id_);
  }

  size_t IndexOf(DatanodeID storage_id) const;

  void set_capacity(uint8_t capacity);
  void Init(uint64_t inode_id,
            uint64_t parent_id,
            const Block& blk,
            uint8_t capacity,
            cloudfs::IoMode write_mode,
            BlockUCState state);

  void SetNext(BlockInfo* next) { next_ = next; }
  bool AddStorage(DatanodeID storage_id, bool* already_exist = nullptr);
  bool RemoveStorage(DatanodeID storage_id);
  void ClearStorages();

  void UpdateLength(uint64_t num_bytes) { blk_.num_bytes = num_bytes; }
  void UpdateGs(uint64_t gs) { blk_.gs = gs; }
  void UpdateINode(uint64_t inode) {
    inode_id_ = inode;
  }

  bool HasBeenCommitted() const {
    return ::dancenn::HasBeenCommitted(uc_state_);
  }
  bool HasBeenComplete() const {
    return ::dancenn::HasBeenComplete(uc_state_);
  }

  bool ReadyToComplete() const {
    if (gs() == kBlockProtocolV2GenerationStamp) {
      return HasBeenCommitted();
    } else {
      return HasBeenComplete();
    }
  }
  bool IsCommitted() const {
    return uc_state_ == BlockUCState::kCommitted;
  }
  bool IsComplete() const {
    return uc_state_ == BlockUCState::kComplete;
  }
  bool IsPersisted() const {
    return uc_state_ == BlockUCState::kPersisted;
  }
  bool Commit(const Block& blk);
  void Complete() { uc_state_ = BlockUCState::kComplete; }
  bool Persist(const Block& blk);
  void set_uc_state(const BlockUCState& state) { uc_state_ = state; }
  void set_expected_rep(uint8_t expected_rep) { expected_rep_ = expected_rep; }

  uint8_t last_replication_index() const { return last_replication_index_; }
  void set_last_replication_index(uint8_t idx) { last_replication_index_ = idx; }
  void reset_last_replication_index() {
    set_last_replication_index(std::numeric_limits<uint8_t>::max());
  }

  bool GetInTransaction();
  // TODO(xiong): make private
  bool TryLockInTransaction(const char* file, uint32_t line);
  bool TryUnlockInTransaction(const char* file, uint32_t line);
  bool IsSafeToRelease(const BlockInfoProto& bip);

  // for debug purpose
  std::string ToString() const;
  std::string GetLockHolder() {
    return std::string(lock_file_) + ":" + std::to_string(lock_line_);
  }

 private:
  BlockInfo* next_{nullptr};
  Block blk_;

  uint64_t inode_id_{kInvalidINodeId};
  uint64_t parent_id_{kInvalidINodeId};
  uint8_t last_replication_index_{std::numeric_limits<uint8_t>::max()};

  // TODO(ruanjunbin): Use "PACKED" again.
  BlockUCState uc_state_;
  uint8_t capacity_;
  uint8_t size_;
  uint8_t expected_rep_;
  cloudfs::IoMode write_mode_;

  // Because BlockManager updates block pufs infos into edit logs and
  // local meta storage asynchronously, we need a "readers-writer lock" to
  // make sure at most only one person is updating.
  uint8_t lock_transaction_failed_cnt_;
  bool in_transaction_;

  // for debug
  uint32_t lock_line_;
  const char* lock_file_{nullptr};
  uint32_t prev_unlock_line_;
  const char* prev_unlock_file_{nullptr};

  // repeated
  DatanodeID first_storage_id_[0];
};

inline bool operator==(const BlockInfo& bi, const BlockInfoProto& bip) {
  return bi.id() == bip.block_id() && bi.gs() == bip.gen_stamp() &&
         bi.num_bytes() == bip.num_bytes() && bi.inode_id() == bip.inode_id();
  // TODO(ruanjunbin): Compare state.
}

inline bool operator!=(const BlockInfo& bi, const BlockInfoProto& bip) {
  return !(bi == bip);
}

inline bool operator==(const BlockInfoProto& bip, const BlockInfo& bi) {
  return bi == bip;
}

inline bool operator!=(const BlockInfoProto& bip, const BlockInfo& bi) {
  return !(bip == bi);
}

class BlockInfoInTransactionGuard {
 public:
  explicit BlockInfoInTransactionGuard(BlockMapSlice* slice,
                                       BlockID block_id,
                                       const char* file,
                                       int line);
  ~BlockInfoInTransactionGuard();

  bool OwnLock() const;
  bool Lock(const char* file, int line);
  bool Unlock(const char* file, int line);

 private:
  BlockMapSlice* slice_{nullptr};
  BlockID block_id_;
  bool own_lock_;
};

using BlockInfoInTransactionGuardPtr =
    std::shared_ptr<BlockInfoInTransactionGuard>;

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_INFO_H_
