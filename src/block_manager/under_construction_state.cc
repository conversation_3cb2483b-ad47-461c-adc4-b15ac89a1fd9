// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/under_construction_state.h"

#include <glog/logging.h>

namespace dancenn {

UnderConstructionState::UnderConstructionState(
    const Block& b,
    const std::vector<DatanodeID>& dns)
    : primary_storage_id_(0),
      recovery_id_(kInvalidGenerationStamp),
      is_recover_cmd_sent_(false) {
  for (const auto& dn : dns) {
    expected_locations_.emplace(std::make_pair(dn, UCInfo(b.gs, 0)));
    tried_as_primary_.emplace(std::make_pair(dn, false));
  }
}

UnderConstructionState::UnderConstructionState(
    const UnderConstructionState& other)
    : expected_locations_(other.expected_locations()),
      tried_as_primary_(other.immutable_tried_as_primary()),
      primary_storage_id_(other.primary_storage_id()),
      recovery_id_(other.recovery_id()),
      is_recover_cmd_sent_(other.is_recover_cmd_sent_) {
}

void UnderConstructionState::AddReplica(DatanodeID dn_id,
                                        uint64_t gs,
                                        uint64_t nbytes) {
  UCInfo info(gs, nbytes);
  expected_locations_[dn_id] = info;
  tried_as_primary_[dn_id] = false;
}

void UnderConstructionState::FilterInvalidReplica(uint64_t gs) {
  for (auto it = expected_locations_.begin();
       it != expected_locations_.end();) {
    if (it->second.gs != gs) {
      VLOG(10) << "FilterInvalidReplica " << gs << " " << it->second.gs;
      tried_as_primary_.erase(it->first);
      it = expected_locations_.erase(it);
    } else {
      ++it;
    }
  }
}

std::vector<DatanodeID> UnderConstructionState::GetStorages() {
  std::vector<DatanodeID> storages;
  for (const auto& e : expected_locations_) {
    storages.emplace_back(e.first);
  }
  return storages;
}

}  // namespace dancenn
