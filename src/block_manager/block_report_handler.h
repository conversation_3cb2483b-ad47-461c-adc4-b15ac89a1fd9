// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/09/21
// Description

#ifndef BLOCK_MANAGER_BLOCK_REPORT_HANDLER_H_
#define BLOCK_MANAGER_BLOCK_REPORT_HANDLER_H_

#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <google/protobuf/repeated_field.h>  // For RepeatedField, RepeatedPtrField.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For BlockReportRequestProto, HeartbeatResponseProto, StorageBlockReportProto, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For BlockProto, LocatedBlockProto, RecoveringBlockProto, NamespaceType.

#include <atomic>                   // For atomic.
#include <condition_variable>       // For conditional_variable.
#include <cstdint>                  // For uint32_t, uint64_t, etc.
#include <functional>               // For function.
#include <memory>                   // For shared_ptr, unique_ptr.
#include <mutex>                    // For mutex.
#include <sstream>                  // For stringstream.
#include <string>                   // For string.
#include <unordered_set>            // For unordered_set.
#include <vector>                   // For vector.

#include "base/closure.h"           // Closure, RpcClosure, ClosureGuard.
#include "base/count_down_latch.h"  // For CountDownLatch.
#include "base/read_write_lock.h"   // For ReadWriteLock.
#include "base/status.h"            // For Status.
#include "base/stop_watch.h"        // For LightweightStopWatch.
#include "block_manager/bip_write_manager.h"  // For BIPWriteManager, BLockComponents, etc.
#include "block_manager/block.h"              // For BlockID.
#include "block_manager/block_manager.h"  // For BlockManager, BlkInfo.
#include "block_manager/datanode_command_manager.h"  // For UploadCmdMgr, NeCmdMgr.
#include "datanode_manager/datanode_info.h"          // For DatanodeID.
#include "edit/sender_base.h"                        // For EditLogSenderBase.
#include "namespace/meta_storage.h"                  // For MetaStorage.

namespace dancenn {

/**
 * We are processing FBR in BlockManager AND BlockReportHandler twice now.
 * Structs like StorageReportContext, BlockReportContext are for BlockManager
 * use only while FullBlockReportContext is for BlockReportManager use only.
 * In the future, we will discard BlockManager and use BlockReportManager only.
 *
 * Naming is bad here, but it is a temporary status.
 *
 * For FullBlockReportContext, we need to add functions like BlockDiff and
 * splitted FBR.
 */
struct StorageReportContext {
  DatanodeStorageProto storage;

  // one rpc
  std::atomic<uint32_t> slice_thread_count;
  std::vector<std::vector<BlkInfo>> blocks_decoded;

  // across all rpcs
  std::vector<BlockID> all_reported_blks;

  // snapshot before FBR
  std::vector<BlockID> snapshot;

  // delta sets for IBR
  std::unordered_set<BlockID> delta_blks;
};

struct BlockReportContext {
  int64_t block_report_id;
  int32_t cur_rpc;
  int32_t total_rpc;

  cloudfs::datanode::BlockReportCommandProto_Type type;

  std::unordered_map<std::string, std::shared_ptr<StorageReportContext>>
      storage_ctx_map;

  std::string ToString() const {
    std::stringstream ss;
    ss.imbue(std::locale::classic());
    ss << "BlockReportContext: id=" << block_report_id
       << ", type=" << static_cast<int>(type) << ", cur_rpc=" << cur_rpc
       << ", total_rpc=" << total_rpc;
    return ss.str();
  }
};

struct FullBlockReportContext {
  std::string dn_uuid;
  std::vector<BlkInfo> report;
  std::atomic<int32_t> ongoing_task_cnt;
  Closure* done{nullptr};
  LightweightStopWatch sw;
};
struct FullBlockReportTask {
  std::shared_ptr<FullBlockReportContext> context;
  // [from_idx, to_idx)
  uint64_t from_idx;
  uint64_t to_idx;
  LightweightStopWatch sw;
};

class HAStateBase;
class BlockReportHandler {
 public:
  BlockReportHandler(HAStateBase* ha_state,
                     std::shared_ptr<EditLogSenderBase> edit_log_sender,
                     std::shared_ptr<MetaStorage> meta_storage,
                     BlockManager* block_manager,
                     BIPWriteManager* bip_write_manager);
  BlockReportHandler(const BlockReportHandler& other) = delete;
  BlockReportHandler(BlockReportHandler&& other) = delete;
  BlockReportHandler& operator=(const BlockReportHandler& other) = delete;
  BlockReportHandler& operator=(BlockReportHandler&& other) = delete;
  ~BlockReportHandler() = default;

  Status IncrementalBlockReport(
      const std::string& dn_uuid,
      const google::protobuf::RepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>& report,
      const std::function<
          void(std::vector<cloudfs::datanode::UploadCommandProto>&&)>&
          process_upload_cmds,
      const std::function<
          void(std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&)>&
          process_ne_cmds);

  Status DecodeStorageBlockReportProto(
      const std::string& dn_uuid,
      const cloudfs::datanode::StorageBlockReportProto& raw_report,
      std::vector<BlkInfo>* decoded_report);
  Status MakeFullBlockReportTasks(const std::string& dn_uuid,
                                  std::vector<BlkInfo>&& decoded_report,
                                  std::vector<FullBlockReportTask>* tasks);
  Status ExecuteFullBlockReportTask(FullBlockReportTask task);
  Status FullBlockReportCallback(
      std::shared_ptr<FullBlockReportContext> context);

 private:
  void ProcessIncrementalBlockReportPerStorage(
      const std::string& dn_uuid,
      const cloudfs::datanode::StorageReceivedDeletedBlocksProto& storage,
      const std::function<
          void(std::vector<cloudfs::datanode::UploadCommandProto>&&)>&
          process_upload_cmds,
      const std::function<
          void(std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&)>&
          process_ne_cmds);
  void ProcessIncrementalBlockReportPerPartition(
      const std::string& dn_uuid,
      const cloudfs::datanode::StorageReceivedDeletedBlocksProto& storage,
      // [begin, end)
      int32_t begin,
      int32_t end,
      std::vector<cloudfs::datanode::UploadCommandProto>* upload_cmds,
      std::vector<cloudfs::datanode::NotifyEvictableCommandProto>* ne_cmds,
      CountDownLatch* latch);

  Status DecodeStorageBlockReportProtoV1(
      const std::string& dn_uuid,
      const google::protobuf::RepeatedField<uint64_t>& raw_report,
      std::vector<BlkInfo>* decoded_report);
  Status DecodeStorageBlockReportProtoV2(const std::string& dn_uuid,
                                         const std::string& raw_report,
                                         std::vector<BlkInfo>* decoded_report);

 private:
  HAStateBase* ha_state_{nullptr};
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MetaStorage> meta_storage_;
  BlockManager* block_manager_{nullptr};
  BIPWriteManager* bip_write_manager_{nullptr};
};

// NN controls pace for DN to do FBR
// 1. If a DN meets the requirement to do FBR, NN try lock one slot.
// 2. If lock slot success, issue BlockReportCmd to DN and create context.
// 3. Process paged block report from DN.
// 4. Free slot and destroy the context.
//
// There is a thread check DN last FBR time in background.
//
// DatanodeInfo::LockForBlockReport() Already called by caller.
//
// Special scene:
// - ACC Mode: check whether the write cache can be converted to read cache
// - ByteRPC protocol: check sealed blocks
class BlockReportScheduler {
 public:
  virtual ~BlockReportScheduler() = 0;

  virtual void SetBlockManager(BlockManager* bm) = 0;
  virtual void SetDatanodeManager(DatanodeManager* dm) = 0;

  virtual void Run() = 0;
  virtual void Stop() = 0;

  virtual void AddDatanode(DatanodeID id) = 0;
  virtual void RemoveDatanode(DatanodeID id) = 0;

  virtual void InitSlot4Test(DatanodeID id) = 0;
  virtual bool LockSlot(DatanodeID id, bool force, bool fast_fbr) = 0;
  virtual void FreeSlot(DatanodeID id) = 0;

  virtual void UpdateBlockReportRecord(DatanodeID id,
                                       bool finished,
                                       bool fast_fbr) = 0;
  virtual void ResetBlockReportRecord(DatanodeID id) = 0;
  virtual void ClearBlockReportRecord() = 0;
  virtual std::shared_ptr<BlockReportContext> GetBlockReportContext(
      DatanodeID id) = 0;

  virtual size_t GetCommand(
      DatanodeID id,
      cloudfs::datanode::HeartbeatResponseProto* response) = 0;

  // For IBR use when DN is doing FBR
  virtual void AddDeltaBlocks(DatanodeID dn_id,
                              const std::string& storage_id,
                              const std::vector<BlockID>& blks) = 0;
};

class BlockReportSchedulerImpl : public BlockReportScheduler {
 public:
  BlockReportSchedulerImpl();

  void SetBlockManager(BlockManager* bm);
  void SetDatanodeManager(DatanodeManager* dm);

  void Run() override;
  void Stop() override;

  void AddDatanode(DatanodeID id) override;
  void RemoveDatanode(DatanodeID id) override;

  void InitSlot4Test(DatanodeID id) override;
  bool LockSlot(DatanodeID id, bool force, bool fast_fbr) override;
  void FreeSlot(DatanodeID id) override;

  void AddDeltaBlocks(DatanodeID dn_id,
                      const std::string& storage_id,
                      const std::vector<BlockID>& blks) override;

  void UpdateBlockReportRecord(DatanodeID id,
                               bool finished,
                               bool fast_fbr) override;
  void ResetBlockReportRecord(DatanodeID id) override;
  void ClearBlockReportRecord() override;
  std::shared_ptr<BlockReportContext> GetBlockReportContext(
      DatanodeID id) override;

  size_t GetCommand(
      DatanodeID id,
      cloudfs::datanode::HeartbeatResponseProto* response) override;

 private:
  void StartBlockReport(DatanodeID id, bool fast_fbr);
  void FinishBlockReport(DatanodeID id);

  void ScanDnAndTriggerFBR();

  void FinishAllBlockReport();

  struct Slot {
    Slot() = delete;
    explicit Slot(std::chrono::system_clock::time_point last_update_time,
                  std::shared_ptr<BlockReportContext> ctx)
        : last_update_time_(last_update_time), ctx_(ctx) {
    }
    bool Expired(std::chrono::system_clock::time_point);
    std::chrono::system_clock::time_point last_update_time_;
    std::shared_ptr<BlockReportContext> ctx_;
  };

  DatanodeManager* dm_{nullptr};
  BlockManager* bm_{nullptr};
  std::unique_ptr<BrCmdMgr> br_cm_;
  std::unique_ptr<AbortBrCmdMgr> abr_cm_;

  std::atomic<bool> running_;
  std::mutex scanner_mut_;
  std::condition_variable scanner_cv_;
  std::unique_ptr<Thread> scanner_;
  // TODO(xiong): useless, remove it
  std::set<DatanodeID> block_report_history_;

  std::map<DatanodeID, Slot> slots_;
  std::mutex slots_mut_;
};

class BlockReportManager {
 public:
  explicit BlockReportManager();
  explicit BlockReportManager(HAStateBase* ha_state);

  void Start(NameSpace* name_space,
             DatanodeManager* datanode_manager,
             BlockManager* block_manager);
  void Stop();

  void StartActive(std::shared_ptr<EditLogSenderBase> edit_log_sender,
                   std::shared_ptr<MetaStorage> meta_storage,
                   BIPWriteManager* bip_write_manager);
  void StopActive();
  void StartStandby();
  void StopStandby();

  void TestOnlySetEditLogSender(std::shared_ptr<EditLogSenderBase> sender);

  void AddDatanode(DatanodeID id);
  void RemoveDatanode(DatanodeID id);

  void InitFullBlockReport4Test(DatanodeID id);
  bool TriggerFullBlockReport(DatanodeID id, bool force, bool fast);
  void FinishFullBlockReport(DatanodeID id);

  // A/B Test stage methods START
  void AddDeltaBlocks(DatanodeID dn_id,
                      const std::string& storage_id,
                      const std::vector<BlockID>& blks);
  void UpdateBlockReportRecord(DatanodeID id, bool finished, bool fast_fbr);
  void ResetBlockReportRecord(DatanodeID id);
  void ClearBlockReportRecord();
  std::shared_ptr<BlockReportContext> GetBlockReportContext(DatanodeID id);
  // A/B Test stage methods END

  void IncrementalBlockReport(
      DatanodeID dn_id,
      const std::string& dn_uuid,
      const google::protobuf::RepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>& report);
  void FullBlockReport(
      const std::string& dn_uuid,
      const cloudfs::datanode::BlockReportRequestProto& request);
  void ReissueCommand(const std::string& dn_uuid,
                      cloudfs::datanode::UploadCommandProto* upload_cmd,
                      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);

  void GetCommands(DatanodeID dn_id,
                   cloudfs::datanode::HeartbeatResponseProto* response);
  size_t GetBlockReportCommand(
      DatanodeID dn_id,
      cloudfs::datanode::HeartbeatResponseProto* response);

 private:
  // Active & Standby.
  HAStateBase* ha_state_{nullptr};
  NameSpace* name_space_{nullptr};
  DatanodeManager* datanode_manager_{nullptr};
  BlockManager* block_manager_{nullptr};
  std::unique_ptr<BlockReportScheduler> fbr_scheduler_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> fbr_workers_;

  // Active Only.
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MetaStorage> meta_storage_;
  BIPWriteManager* bip_write_manager_{nullptr};
  UploadCmdMgr upload_cmd_mgr_;
  NeCmdMgr ne_cmd_mgr_;
  LoadCmdMgr load_cmd_mgr_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_REPORT_HANDLER_H_
