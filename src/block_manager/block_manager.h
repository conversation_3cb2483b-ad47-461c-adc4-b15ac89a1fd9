// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCK_MANAGER_H_
#define BLOCK_MANAGER_BLOCK_MANAGER_H_

#include <cnetpp/base/ip_address.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <stdio.h>

#include <atomic>
#include <cstdint>
#include <cstdlib>
#include <deque>
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "DatanodeProtocol.pb.h"  // NOLINT(build/include_subdir)
#include "base/closure.h"
#include "base/read_write_lock.h"
#include "base/rw_spinlock.h"
#include "base/status.h"
#include "block_manager/bip_write_manager.h"
#include "block_manager/block.h"
#include "block_manager/block_info.h"
#include "block_manager/block_info_proto.h"
#include "block_manager/block_lifecycle_logger.h"
#include "block_manager/block_manager_metrics.h"
#include "block_manager/block_map_slice.h"
#include "block_manager/block_pufs_info.h"
#include "block_manager/block_pufs_info_monitor.h"
#include "block_manager/blocks_scanner_listener.h"
#include "block_manager/datanode_command_manager.h"
#include "block_manager/invalidate_block_monitor.h"
#include "block_manager/misinvalidated_block_monitor.h"
#include "block_manager/replication_monitor.h"
#include "block_manager/sealed_monitor.h"
#include "block_manager/status_monitor.h"
#include "block_manager/truncatable_block_monitor.h"
#include "block_manager/under_replicated_blocks.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/storage_info.h"
#include "edit/edit_log_context.h"
#include "edit/sender_base.h"
#include "ha/ha_state_base.h"
#include "lifecycle.pb.h"
#include "namespace/meta_scanner.h"
#include "namespace/meta_storage.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "safemode/safemode_base.h"

using Thread = cnetpp::concurrency::Thread;

namespace dancenn {

class DepringBlockRecycler;
class DepredBlockRecycler;
class TosBlockDeleter;
class UploadProcessTest;
class SafeModeWithBMTest;
class BlockManagerTestV2;
class ActiveStandbySyncEditlogTest;
class JobManager;
class Ufs;

struct BlockMapStat {
  BlockMapStat()
      : num_under_replicated(0),
        num_pending_replication(0),
        num_corrupt_block(0),
        num_sealed_block(0),
        num_missing(0),
        num_missing_one_block(0),
        num_block(0),
        num_zero_replica_block(0),
        num_uc(0),
        num_postponed_misreplicated(0),
        num_invalidate(0),
        num_recover(0),
        num_excess_replicas(0),
        num_replication_queue(0),
        num_future_blocks(0),
        num_misinvalidated_blocks(0) {}
  uint64_t num_under_replicated;
  uint64_t num_pending_replication;
  uint64_t num_corrupt_block;
  uint64_t num_sealed_block;
  uint64_t num_missing;
  uint64_t num_missing_one_block;
  uint64_t num_block;
  uint64_t num_zero_replica_block;
  uint64_t num_uc;
  uint64_t num_postponed_misreplicated;
  uint64_t num_invalidate;
  uint64_t num_truncatable;
  uint64_t num_replication_queue;
  uint64_t num_recover;
  uint64_t num_excess_replicas;
  uint64_t num_future_blocks;
  uint64_t num_misinvalidated_blocks;
  std::string ToString() const;
};

struct ReplicaNum {
  uint8_t live;
  uint8_t corrupt;
  uint8_t excess;
  uint8_t stale;
  uint8_t decommission;
};
std::ostream& operator<<(std::ostream& os, const ReplicaNum& rn);

struct BlockTargetPair {
  Block blk;
  std::vector<DatanodeID> targets;
  cloudfs::IOPriority io_priority;
};

struct BlockReplication {
  Block blk;
  ReplicaNum rn;
  uint8_t expected_rep;
};

#ifndef PACKED
#define PACKED __attribute__((packed))
#endif  // PACKED

struct BlkInfo {
  uint64_t id;
  uint64_t gs;
  uint64_t len;
  cloudfs::ReplicaStateProto replica_state;
  cloudfs::StorageClassReportProto storage_class_report;
}; // TODO(xuexiang.xx): Add PACKED when deprecated storage class field removed

struct RpcDoneContext {
  std::atomic<uint32_t> storage_count;
  Closure* done{nullptr};
};

// used to provide detailed info about a block to avoid acquire
// and release lock again and again.
struct DetailedBlock {
  BlockProto bp_;
  Block blk_;
  BlockUCState uc_;
  std::string pufs_name_;
  std::vector<DatanodeID> storage_;
  std::vector<cloudfs::StorageClassReportProto> storage_class_report_;
  uint8_t live_replica_;

  DetailedBlock() = default;
  explicit DetailedBlock(const BlockProto& bp)
      : bp_(bp), blk_(bp.blockid(), bp.numbytes(), bp.genstamp()){};

  DetailedBlock(
      const BlockProto& bp,
      const Block& blk,
      const BlockUCState& uc,
      const std::string& pufs_name,
      std::vector<DatanodeID> storage,
      std::vector<cloudfs::StorageClassReportProto> storage_class_report,
      ReplicaNum& rn)
      : bp_(bp),
        blk_(blk),
        uc_(uc),
        pufs_name_(pufs_name),
        storage_(std::move(storage)),
        storage_class_report_(std::move(storage_class_report)),
        live_replica_(rn.live) {
  }

  BlockID GetBlockID() const { return bp_.blockid(); }

  uint32_t GetBlockSize() const { return blk_.num_bytes; }
};

struct BlockReportContext;
struct StorageReportContext;
class BlockReportManager;

class DatanodeManager;

// NOTICE:
// The order to initialize BlockManager
// 1. constructor()
// 2. set_datanode_manager()
// 3. SetMetaScanner()
// And then the dancenn is in standby mode(by default)
//
// To transit to active mode:
// StartActive()
//
// To transit back to standby mode:
// StopActive()
//
class BlockManager {
 public:
  explicit BlockManager(std::shared_ptr<EditLogContextBase> edit_log_ctx = nullptr);
  ~BlockManager();
  void set_datanode_manager(std::shared_ptr<DatanodeManager> datanode_manager);
  void set_job_manager(std::shared_ptr<JobManager> job_manager);
  void SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage);
  void SetMetaScanner(std::shared_ptr<MetaScanner> meta_scanner);
  void TestOnlySetEditLogCtx(std::shared_ptr<EditLogContextBase> edit_log_ctx);
  void TestOnlySetEditLogSender(std::shared_ptr<EditLogSenderBase> sender);
  void TestOnlySetBIPWriteManager(BIPWriteManagerBase* bip_write_manager);
  void TestOnlySetIsActive(bool is_active);
  BlockPufsInfoMonitor* TestOnlyGetBlockPufsInfoMonitor();

  void set_ha_state(HAStateBase* ha_state);
  void set_ns(NameSpace* ns);
  void set_safemode(SafeModeBase* safemode);
  void set_block_report_manager(BlockReportManager* block_report_manager);

  NameSpace* ns() {
    return ns_;
  }
  BlockManagerMetrics* metrics() { return &metrics_; }

  void StartActive(BIPWriteManagerBase* bip_write_manager = nullptr);
  void StopActive();
  void StartStandby();
  void StopStandby();
  void AwaitHaSwitch();

  const UnderReplicatedBlocks& needed_replications() const {
    return needed_replications_;
  }

  uint64_t GetBlockNum() { return block_count_.load(); }
  uint64_t GetZeroReplicaBlockNum() { return zero_replica_block_count_.load(); }

  DatanodeID GetBlockDatanodeID(BlockID blk_id, size_t index);

  virtual uint64_t GetINodeId(BlockID blk_id);

  virtual Block AddBlock(const Block& blk,
                         uint64_t inode_id,
                         uint64_t parent_id,
                         uint8_t capacity,
                         cloudfs::IoMode write_mode,
                         const std::vector<DatanodeID>& dns,
                         BlockUCState state);
  void AddBlock(BlockID blk_id,
                uint64_t inode_id,
                uint64_t parent_id,
                uint32_t len,
                uint64_t gs,
                uint8_t capacity,
                cloudfs::IoMode write_mode,
                BlockUCState state);
  void SetBlockNum(uint8_t capacity);
  void LoadBlock(uint64_t inode_id,
                 uint64_t parent_id,
                 uint8_t capacity,
                 cloudfs::IoMode write_mode,
                 const Block& blk,
                 const std::vector<DatanodeID>& dns,
                 BlockUCState state);
  void LoadLocalBlocks();

  bool RemoveBlock(BlockID blk_id, bool check_in_transaction);
  void RemoveBlocksAndUpdateSafeMode(const std::vector<BlockInfoProto>& blocks);
  virtual void RemoveBlocksAndUpdateSafeMode(
      const std::vector<cloudfs::BlockProto>& blocks);
  bool RemoveStorage4Http(BlockID blk_id, DatanodeID dn_id);

  virtual BlockUCState GetBlockUCState(BlockID blk_id);
  cloudfs::IoMode GetBlockIoMode(BlockID blk_id);

  // Status.code == kOK indicate the block needs release, vice versa
  virtual Status NeedRelease(const std::vector<BlockProto>& blocks,
                             bool* remove_last_block,
                             Block* last_blk);
  virtual void InitRecover(const cloudfs::BlockProto& block,
                           uint64_t recovery_id,
                           bool close_file);

  virtual Status IsLastBlkReadyToComplete(const Block& blk_from_client);
  virtual bool CommitOrCompleteOrPersistLastBlock(const Block& blk_from_client,
                                                  bool force = false);
  bool CommitOrCompleteLastBlockUnsafe(const Block& blk_from_client,
                                       BlockMapSlice* s,
                                       BlockInfo* cur_bi,
                                       bool force = false,
                                       StopWatchContext* rpc_sw_ctx = nullptr);
  bool PersistBlock(const Block& block);
  bool PersistBlockUnsafe(BlockMapSlice* s, const Block& block);
  std::vector<DatanodeID> ConvertBlockToUnderConstruction(
      const std::string& bp_id,
      const cloudfs::BlockProto& blk,
      const INode& inode);

  // virtual for mock
  virtual void UpdateLength(BlockID blk_id, uint64_t len);
  void UpdateGenStamp(BlockID blk_id, uint64_t gen_stamp);
  void UpdateINode(BlockID blk_id, uint64_t inode_id);

  virtual Status CommitBlockSynchronization(
      const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
          request);  // NOLINT(whitespace/line_length)

  virtual Block GetBlock(BlockID blk_id);

  DetailedBlock GetDetailedBlock(BlockProto blk_id, bool need_storage_class_report = false);

  uint64_t GetBlockINodeID(BlockID blk_id);
  size_t SpeedUpUCBlockRelease(const std::vector<BlockID>& blk_ids);
  virtual void CheckReplica(const std::vector<cloudfs::BlockProto>& blocks,
                            const INode* inode = nullptr,
                            StopWatchContext* rpc_sw_ctx = nullptr);

  bool HasEnoughRack(const BlockInfo* bi, const INode* inode);

  void SetReplica(const std::vector<cloudfs::BlockProto>& blocks,
                  DatanodeID old_replica,
                  DatanodeID new_replica,
                  bool need_recover);

  std::vector<DatanodeID> GetBlockStorage(BlockID block_id);
  std::vector<DatanodeID> GetBlockStorageSafe(BlockID block_id);

  void AsyncBlockReport(
      const std::string& dn_uuid,
      const cloudfs::datanode::BlockReportRequestProto* request,
      RpcClosure* rpc_done) noexcept;
  void ProcessBlockReport(
      DatanodeID dn_id,
      DatanodeInfoPtr dn,
      std::shared_ptr<StorageInfo> storage,
      const cloudfs::datanode::StorageBlockReportProto& report,
      std::shared_ptr<BlockReportContext> blk_report_ctx,
      std::shared_ptr<StorageReportContext> storage_report_ctx,
      int report_idx,
      int worker_idx,
      std::shared_ptr<RpcDoneContext> done,
      const cloudfs::datanode::BlockReportRequestProto* request);
  void FinishStorageReport(
      DatanodeID dn_id,
      DatanodeInfoPtr dn,
      std::shared_ptr<BlockReportContext> blk_report_ctx,
      std::shared_ptr<StorageReportContext> storage_report_ctx,
      std::atomic<bool>* running);
  void BlockDiffRemoveBlocks(
      DatanodeID dn_id,
      DatanodeInfoPtr dn,
      std::shared_ptr<StorageReportContext> storage_report_ctx,
      const std::vector<BlockID>& not_reported);

  typedef ::google::protobuf::RepeatedPtrField<cloudfs::LocatedBlockProto>
      RepeatedLocatedBlock;
  Status ReportBadBlocks(const RepeatedLocatedBlock& blocks);

  typedef ::google::protobuf::RepeatedPtrField<
      cloudfs::datanode::StorageReceivedDeletedBlocksProto>
      RepeatedIncBlockReport;
  Status IncrementalBlockReport(const std::string& dn_uuid,
                                const RepeatedIncBlockReport& report) noexcept;

  virtual Status AnalyzeFileBlocks(const Block& previous,
                                   const Block& stored_last,
                                   const Block& stored_penultimate,
                                   uint32_t preferred_block_size,
                                   std::vector<DatanodeID>* targets);

  // virtual for mock
  virtual Status AnalyzeFileBlocksToCommit(const Block& previous,
                                           const Block& stored_last);

  typedef ::google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>
      RepeatedDatanode;
  Status UpdatePipeline(const cloudfs::ExtendedBlockProto& new_block,
                        const RepeatedDatanode& new_nodes);

  void GetCommands(DatanodeID dn_id,
                   const std::string& bp_id,
                   int32_t transfer_inprogress,
                   cloudfs::datanode::HeartbeatResponseProto* response,
                   bool has_version = false);
  void GetBlockReportCommand(
      DatanodeID dn_id,
      cloudfs::datanode::HeartbeatResponseProto* response);

  virtual bool BlockHasBeenCommitted(BlockID block_id);
  virtual bool BlockHasBeenComplete(BlockID block_id);
  virtual bool BlockReadyToComplete(BlockID block_id);
  bool IsPopulatingReplicationQueues();

  bool BlockNeedTransferUnsafe(const BlockInfo* bi);

  // A simple result enum for the result of
  // ProcessMisReplicatedBlock(const Block&, const INode&)
  enum class MisReplicationResult {
    // The block should be invalidated since it belongs to a deleted file.
    kInvalid,
    // The block is currently under-replicated.
    kUnderReplicated,
    // The block is currently over-replicated
    kOverReplicated,
    // A decision can't currently be made about this block.
    kPostPone,
    // The block is under construction, so should be ignored
    kUnderConstruction,
    kCorrupt,
    // The block is properly replicated
    kOK,
  };

  MisReplicationResult ProcessMisReplicatedBlock(const Block& blk,
                                                 const INode& inode);
  MisReplicationResult ProcessMisReplicatedBlock(const BlockInfo* bi,
                                                 const INode& inode);
  void RemoveBlocksAssociatedTo(DatanodeID dn_id,
                                const std::vector<BlockID>& blocks);
  void CleanupDatanode(DatanodeID dn_id);
  void AddDecommissionBlocks(DatanodeID dn_id,
                             const std::string& dn_ip,
                             const std::vector<BlockID>& blocks);
  bool AddTransferBlocks(const BlockID block,
                         int32_t required_replicas,
                         UnderReplicatedBlocks::Priority priority);
  size_t CountReplicatedBlocks(const std::vector<BlockID>& blocks);

  BlockMapStat stat();
  void UpdateState();
  void GetCorruptFilesInfo(uint32_t offset,
                           uint32_t limit,
                           uint32_t* total,
                           std::ostringstream& os);
  void DumpBlockSimpleInfo(BlockID blk_id, std::ostringstream& os);
  void DumpBlockInfo(BlockID blk_id,
                     const BlockProto* blk_in_inode,
                     bool dump_block_detail,
                     std::ostream& os);
  std::string DumpBlocks(std::string dump_path);
  std::string DumpBlocksInParquet(std::string parquet_compress_type,
                                  std::string dump_path);

  void ProcessPendingFutureBlks(BlockID block_id,
                                uint64_t current_block_id = 0,
                                uint64_t current_gsv2 = 0);
  void EnqueuePendingPersistedBlks(Block block);
  void ProcessPendingPersistedBlks(Block block, uint64_t current_gsv2);

  std::pair<size_t, std::deque<BlockID>> GetCorruptBlockIDs(int64_t offset,
                                                            int64_t limit);
  std::pair<size_t, std::deque<BlockID>> GetSealedBlockIDs(int64_t offset,
                                                           int64_t limit);
  std::pair<size_t, std::deque<BlockID>> GetTruncatableBlockIDs(
      DatanodeID dn_id,
      int64_t offset,
      int64_t limit);

  Status GetBlocksWithLocations(
      DatanodeInfoPtr dn,
      uint64_t size,
      cloudfs::BlocksWithLocationsProto* blocks_proto);

  void ProcessAndDumpFsckBlockReplication(std::vector<BlockID>& block_ids,
                                          std::ostream& os);

  // TODO(ruanjunbin): Standby can put deprecating block to deprecated directly.
  void HandleDeprecatingBlocks();
  void HandleDeprecatedBlocks();

  typedef ::google::protobuf::RepeatedField<uint64_t>
      RepeatedBlocks;
  bool DecodeBlocksV1(const RepeatedBlocks& blocks_encoded,
                      int slice_num,
                      std::vector<std::vector<BlkInfo>>* blocks_decoded,
                      uint64_t* fin_num,
                      uint64_t* uc_num);
  bool DecodeBlocksV2(const std::string& blocks_encoded,
                      int slice_num,
                      std::vector<std::vector<BlkInfo>>* blocks_decoded,
                      uint64_t* fin_num,
                      uint64_t* uc_num);
  Status DecodeBlocks(const cloudfs::datanode::StorageBlockReportProto& report,
                      int slice_num,
                      std::vector<std::vector<BlkInfo>>* blocks_decoded,
                      uint64_t* fin_num,
                      uint64_t* uc_num);
  void AddBlockReportCandidate(DatanodeID id);
  void RemoveBlockReportCandidate(DatanodeID id);
  void ResetBlockReportHistory(DatanodeID id);
  bool TriggerBlockReport(DatanodeID id, bool fast);
  void TriggerAllBlockReport();
  void InitBlockReport4Test(DatanodeID id);

  // Acc mode

  // File may be moved during writing, some blocks might be uploaded with
  // outdated upload id. We abort old upload id and acquire a new one, and
  // re-issue upload cmds.
  bool GetAccBlockPersistInfoOrRetryUpload(BlockID id,
                                           Ufs* ufs,
                                           const UfsFileInfoProto& ufs_info,
                                           bool is_last_block,
                                           BlockInfoProto* bip_out);

  void TriggerUploadChooseDN(const BlockProto& bp,
                             const std::string& upload_id,
                             const std::string& pufs_name);

  void AddBlocksToInvalidate(const std::vector<BlockProto>& blocks,
                             const std::string& reason);

  bool AddLoadCmd(
      DatanodeID dn_id,
      std::vector<std::shared_ptr<cloudfs::ExtendedBlockProto>>& blocks);

  Status AddMergeBlockCmd(DatanodeID dn_id,
                          const cloudfs::LocatedBlockProto& lb,
                          const cloudfs::LocatedBlocksProto& old_blks);
  // Force bi to state with NO check
  bool UpdateMergeBlockUCState(const Block& b,
                               BlockUCState state);

  enum NegoedResult {
    kApprove,
    kDeny,
    kNotifyUpload,
    kNotifyEvictable,
    // ACC ns, block are bundled into at least 100MB bundles. Only last block in
    // the bundle will receive upload command. Once all blocks has been
    // uploaded, blocks in bundle but not the last one will move to persisted
    // state, and evictable.
    kPersistAndEvict,
    kError,
  };

  void NotifyAllBlockEvictable(const INode& inode);

  virtual void UpdateBlockPinStatus(const INode& inode, const BlockID bl);

  void ProcessPersistNonKeyBlock(const INode& inode, const BlockID block_id);

  uint32_t BlockExpectedReplica(const BlockInfo& bi, const INode* inode);

  uint32_t BlockExpectedReplica(const BlockInfoProto& bip, const INode* inode);

  static bool IsBlockReadyToPersisted(const BlockInfoProto& bip,
                                      const std::string& pufs_name,
                                      const std::string& upload_id);

 private:
  std::pair<NegoedResult, BlockInfoProto> NegoInHdfsMode(
      BlockID block_id,
      const std::string& dn_uuid,
      int64_t currentTsInSec);
  std::pair<NegoedResult, BlockInfoProto> NegoInAccMode(
      BlockID block_id,
      const std::string& dn_uuid,
      int64_t currentTsInSec,
      BlockMapSlice* s);

  void NotifyBlockEvictable(const BlockInfoProto& bip, BlockInfo* bi);
  bool FillUploadCmdAccMode(const BlockInfoProto& bip, UploadCmd* cmd);
  void NotifyBlockUpload(const BlockInfoProto& bip);
  void ApproveUploadCallback(const BlockInfoProto& bip,
                             BlockInfoInTransactionGuardPtr bi_tx_guard);
  void PersistBlockCallback(const BlockInfoProto& bip,
                            BlockInfoInTransactionGuardPtr bi_tx_guard,
                            bool evict_in_acc_mode);
  void ApproveUploadRequest(const BlockInfoProto& bip,
                            const BlockInfoProto& old_bip,
                            vshared_lock&& ha_barrier,
                            BlockInfoInTransactionGuardPtr bi_tx_guard);
  void PersistBlockRequest(const BlockInfoProto& bip,
                           const BlockInfoProto& old_bip,
                           vshared_lock&& ha_barrier,
                           BlockInfoInTransactionGuardPtr bi_tx_guard,
                           bool evict_in_acc_mode);
  void FinishBipAccMode(const std::string& upload_id,
                        const std::string& etag,
                        vshared_lock&& ha_barrier,
                        BlockInfoProto&& bip,
                        std::unique_lock<BlockMapSlice>&& s_lock,
                        BlockMapSlice* s,
                        BlockInfo* bi);

  void ProcessUploadIdNegoedBlock(const BlockProto& bp,
                                  DatanodeID dn_id,
                                  const std::string& dn_uuid,
                                  const std::string& upload_id,
                                  const std::string& block_pufs_name);
  void ProcessUploadBlockSucceed(const BlockProto& bp,
                                 DatanodeID dn_id,
                                 const std::string& upload_id,
                                 const std::string& block_pufs_name,
                                 const std::string& etag);
  void ProcessUploadBlockFailed(
      const BlockProto& bp,
      DatanodeID dn_id,
      const std::string& upload_id,
      const std::string& block_pufs_name,
      const std::string& etag,
      const ::cloudfs::datanode::FailedMessage& failed_msg);

  void ProcessBlockCheckDuringFastReport(BlockMapSlice* s,
                                         const BlockID block_id,
                                         DatanodeInfoPtr dn);
  void ProcessCheckWriteCacheINodeState(BlockMapSlice* s,
                                        const BlockID block_id,
                                        DatanodeInfoPtr dn);

  void CommitStorageClassReports(
      const std::string& dn_uuid,
      std::vector<std::pair<BlockID, StorageClassReportProto>>& report,
      bool wait);

  void ProcessAllPendingReportedBlock();
  uint64_t ProcessAllPendingMisinvalidatedBlks();
  void ProcessPendingReportedBlock(const std::deque<ReportedBlockInfo>& rbis,
                                   bool is_misinvalidated_blk = false,
                                   uint64_t current_block_id = 0,
                                   uint64_t current_gsv2 = 0);
  void ProcessCorruptReplica(DatanodeID dn_id,
                             const Block& reported_block,
                             const std::string& reason);

  void ProcessOverReplicatedBlock(const BlockInfo* bi,
                                  size_t replication,
                                  DatanodeID added_dn = kInvalidDatanodeID,
                                  DatanodeID del_dn_hint = kInvalidDatanodeID);
  void ProcessMergedBlock(const BlockProto& bp,
                          DatanodeID dn_id,
                          const cloudfs::DatanodeStorageProto& storage,
                          const std::string& block_pufs_name);
  void ChooseExcessReplicates(
      std::unordered_set<DatanodeInfoPtr>& non_excess_dns,
      const BlockInfo* bi,
      size_t replication,
      DatanodeInfoPtr added_dn,
      DatanodeInfoPtr del_dn_hint);

  DatanodeInfoPtr ChooseSourceDatanode(
      BlockInfo* bi,
      UnderReplicatedBlocks::Priority pri,
      std::unordered_map<std::string, int> expected_placement,
      std::unordered_set<DatanodeInfoPtr>* containing_dns,
      std::unordered_set<DatanodeInfoPtr>* live_dns,
      std::unordered_set<DatanodeInfoPtr>* decommission_dns,
      ReplicaNum* rn);

  void AdjustByChosenReplica(
      std::unordered_map<std::string, std::unordered_set<DatanodeInfoPtr>>&
          rack_map,
      std::unordered_set<DatanodeInfoPtr>& more_than_one,
      std::unordered_set<DatanodeInfoPtr>& exactly_one,
      DatanodeInfoPtr choose_dn);
  void PostponeBlock(BlockID blk_id);
  bool RemoveBlock(BlockID block_id);
  bool RemoveBlock(BlockID block_id, BlockMapSlice* slice);

  bool RemoveStorageInternal(BlockID blk_id, DatanodeID dn_id);
  bool RemoveCachedBlockInfo(BlockID blk_id);

  bool InvalidateCorruptBlock(BlockInfo* bi,
                              DatanodeID dn_id,
                              const Block& blk,
                              int live_dn,
                              int stale_dn);

  void AddToInvalidate(const cloudfs::BlockProto& block,
                       const std::string& reason = "");

  void AddToRecover(BlockID blk_id, DatanodeID dn_id, bool close_file);

  size_t ComputeReplicationWork(size_t blocks_to_process);

  // return true if replication work is scheduled.
  // 'retryable' is set to true if compute replication work failed but it is
  // retryable
  bool ProcessBlockNeedReplicate(const Block& blk,
                                 size_t pri,
                                 int64_t max_cross_dc_bytes,
                                 int64_t decommission_max_cross_dc_bytes,
                                 int64_t* now_cross_dc_bytes,
                                 int64_t* decommission_now_cross_dc_bytes,
                                 bool* retryable);

  // caller should task responsible for acquiring block map slice's lock
  size_t ComputeSatisfiedPlacementReplicas(
      dancenn::BlockInfo* blk_info,
      std::unordered_map<std::string, int> expected_placement);

  void AddBlockToBeReplicated(DatanodeInfoPtr src_dn,
                              const Block& block,
                              const std::vector<DatanodeID>& targets,
                              cloudfs::IOPriority priority);

  ReplicaNum CountReplica(const BlockInfo* bi);

  uint8_t GetDatanodeContentStale(const BlockInfo* bi);

  void UpdateNeededReplications(const BlockInfo* bi,
                                int cur_replicas_delta,
                                int expected_replicas_delta,
                                const std::string& reason = "",
                                const DatanodeID dn_id = kInvalidDatanodeID);

  bool AddStoredBlock(DatanodeID dn_id,
                      const Block& blk,
                      ReplicaNum* rn,
                      DatanodeID del_node_hint = kInvalidDatanodeID);

  bool IsNeededReplication(const BlockInfo* bi,
                           const INode* inode,
                           int expected_replication,
                           int num_live_replica);

  // test false-positive bi->IsPersisted()
  bool IsFalsePersistedAccBlock(const BlockInfo* bi, const INode* inode);

  bool IsResidentInCache(const BlockInfo* bi, const INode* inode);

  bool ShouldPostponeBlocksFromFutureV2(uint64_t block_id,
                                        uint64_t genstamp,
                                        uint64_t current_block_id = 0,
                                        uint64_t current_gsv2 = 0);
  bool ShouldPostponeBlocksFromFuture(uint64_t genstamp,
                                      uint64_t current_gsv2 = 0);

  bool GetStoragePolicy(BlockInfo* bi, StoragePolicyId* storage_policy);

  // Read the storage policy, replica policy from inode.
  // We put them in one function in order to read inode for only one time.
  bool GetPlacementAdvice(const BlockInfo* bi, PlacementAdvice* advice);

  size_t ComputeDatanodeWork();
  void ProcessPendingReplications();
  void RescanPostponedMisreplicatedBlocks();

 public:
  void ComputeInvalidateBlockWork();
  void ComputeTruncatableBlockWork();
  void ComputeSealedBlockWork();

  std::unique_ptr<BlockMapSlice>& TestOnlyGetSlice(BlockID blk_id) {
    return slice(blk_id);
  }

 private:
  std::string GetDNIPStr(const std::vector<DatanodeID>& dns);
  std::string GetStateStr(BlockUCState state);

  std::unique_ptr<BlockMapSlice>& slice(BlockID blk_id) {
    return slices_[blk_id & slice_mask_];
  }

  virtual int64_t GetCurrentTsInSec();

  // use snapshot read
  Status AccGetINodeReadOnly(INodeID inode_id, INode* inode);

  // Deprecated
  // Block map slice lock already hold
  // Return false need retry
  // INode id is invalid if path not found
  bool AccLockBlockAndGetINode(const BlockInfoProto& bip,
                               BlockMapSlice* s,
                               INode* inode);
  // Block map slice lock already hold
  // Return false need retry
  bool AccLockBlockAndGetPrevBlock(const BlockInfoProto& bip,
                                   BlockMapSlice* s,
                                   BlockInfoProto* prev_bip);

  bool LoadBlockForZeroReplicaEnable(BlockInfo* bi, size_t pending_replica);

  std::atomic<bool> is_active_{false};
  DatanodeManager* datanode_manager_{nullptr};
  std::shared_ptr<JobManager> job_manager_;
  HAStateBase* ha_state_{nullptr};
  SafeModeBase* safemode_{nullptr};
  NameSpace* ns_{nullptr};

  size_t num_slices_;
  size_t slice_mask_;

  // NOTICE: We assume slices_ is immutable after initialization.
  // Don't add or remove BlockMapSlice after initialization.
  // But modify BlockMapSlice is allowed.
  std::vector<std::unique_ptr<BlockMapSlice>> slices_;

  // NOTICE: Never try to get slice lock after you got this rwlock_
  // mutable ReadWriteLock rwlock_;
  mutable RWSpinlock rwlock_;
  std::unordered_set<BlockID> postponed_misreplicated_blocks_;
  std::unordered_map<DatanodeID,
                     std::unordered_map<BlockID, /*close_file=*/bool>>
      to_recover_;

  UnderReplicatedBlocks needed_replications_;
  std::unordered_map<DatanodeID, std::deque<BlockTargetPair>>
      dn_replication_queues_;

  std::unique_ptr<ReplicationMonitor> replication_monitor_;
  std::unique_ptr<InvalidateBlockMonitor> invalidate_block_monitor_;
  std::unique_ptr<MisinvalidatedBlockMonitor> misinvalidated_block_monitor_;
  std::unique_ptr<BlockPufsInfoMonitor> block_pufs_info_monitor_;
  std::unique_ptr<TruncatableBlockMonitor> truncatable_block_monitor_;
  std::unique_ptr<SealedBlockMonitor> sealed_block_monitor_;

  friend class UnderReplicatedBlocks;
  friend class PendingReplicationBlocks;
  friend class ReplicationMonitor;
  friend class BlocksScannerListener;
  friend class InvalidateBlockMonitor;
  friend class MisinvalidatedBlockMonitor;
  friend class BlockPufsInfoMonitor;
  friend class StatusMonitor;
  friend class TruncatableBlockMonitor;
  friend class SealedBlockMonitor;
  friend class BlockManagerTest;

  ReadWriteLock stat_lock_;
  BlockMapStat stat_;

  std::atomic<uint64_t> block_count_;
  // According to cppreference, until C++20, the default constructor
  // "atomic() noexcept = default;" is trivial, no initialization takes place
  // other than zero initialization of static and thread-local objects.
  // Fixing Atomic Initialization, Rev0
  // http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2018/p0883r0.pdf
  // So init it by hand is a good behavior.
  std::atomic<uint64_t> zero_replica_block_count_;

  std::shared_ptr<MetaScanner> meta_scanner_;
  std::shared_ptr<BlocksScannerListener> blocks_scanner_listener_;

  friend struct BlockManagerMetrics;
  BlockManagerMetrics metrics_;

  std::shared_ptr<cnetpp::concurrency::Task> slice_scan_task_;
  std::unique_ptr<cnetpp::concurrency::Thread> slice_scan_worker_;
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>>
      blk_report_thread_;
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>>
      dirty_bip_report_thread_;
  // Only used by tos block.
  std::unique_ptr<cnetpp::concurrency::ThreadPool> block_recovery_thread_;

  std::mutex cross_dc_throttle_config_mutex_;
  std::shared_ptr<cnetpp::concurrency::ThreadPool> blk_replication_workers_;

  std::atomic<uint64_t> invalidate_blk_count_;
  std::atomic<uint64_t> truncatable_blk_count_;
  std::atomic<uint64_t> sealed_blk_count_;

  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  // edit_log_sender_ should be a unique_ptr.
  // Refer to NameSpace::edit_log_sender_-.
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MetaStorage> meta_storage_;

  std::mutex bg_uploader_worker_task_mutex_;
  std::unordered_set<uint64_t> bg_uploader_worker_task_set_;
  std::unique_ptr<cnetpp::concurrency::ThreadPool> bg_uploader_worker_{nullptr};

  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>>
      bg_evict_deleter_worker_;

  UploadCmdMgr upload_cmd_mgr_;
  NeCmdMgr ne_cmd_mgr_;
  LoadCmdMgr load_cmd_mgr_;
  MergeCmdMgr merge_cmd_mgr_;
  BlockIdCmdMgr blockid_cmd_mgr_;

  std::unique_ptr<DepringBlockRecycler> depring_block_recycler_;
  std::unique_ptr<DepredBlockRecycler> depred_block_recycler_;
  std::unique_ptr<TosBlockDeleter> tos_block_deleter_;
  BIPWriteManagerBase* bip_write_manager_{nullptr};

  BlockReportManager* block_report_manager_{nullptr};
  // During transfer, if a block is not in the cache, will be load for the file.
  // For multiple blocks of the same file, only load once per round.
  std::unordered_set<INodeID> replicate_block_loaded_inodes_cache_;

  CountDownLatch ha_switch_latch_{0};

  friend class DepringBlockRecycler;
  friend class UploadProcessTest;
  friend class SafeModeWithBMTest;
  friend class NameSpaceTest;
  friend class ActiveStandbySyncEditlogTest;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_MANAGER_H_
