// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/22
// Description

#ifndef BLOCK_MANAGER_BLOCK_PUFS_INFO_MONITOR_H_
#define BLOCK_MANAGER_BLOCK_PUFS_INFO_MONITOR_H_

#include <cnetpp/concurrency/thread.h>
#include <condition_variable>
#include <memory>
#include <mutex>

namespace dancenn {

class BlockManager;

class BlockPufsInfoMonitor {
 private:
  class MonitorDeprecatingTask : public cnetpp::concurrency::Task {
   public:
    explicit MonitorDeprecatingTask(BlockPufsInfoMonitor* monitor);

    bool operator()(void* arg) override;
    void Stop() override;

   private:
    BlockPufsInfoMonitor* monitor_{nullptr};
    std::mutex mu_;
    std::condition_variable cond_;
  };
  class MonitorDeprecatedTask : public cnetpp::concurrency::Task {
   public:
    explicit MonitorDeprecatedTask(BlockPufsInfoMonitor* monitor);

    bool operator()(void* arg) override;
    void Stop() override;

   private:
    BlockPufsInfoMonitor* monitor_{nullptr};
    std::mutex mu_;
    std::condition_variable cond_;
  };
  friend class MonitorDeprecatingTask;
  friend class MonitorDeprecatedTask;

 public:
  explicit BlockPufsInfoMonitor(BlockManager* bm);
  ~BlockPufsInfoMonitor();

  void Start();
  void Stop();

 private:
  void DoDeprecating();
  void DoDeprecated();

 private:
  BlockManager* bm_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> deprecating_worker_;
  std::unique_ptr<cnetpp::concurrency::Thread> deprecated_worker_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_PUFS_INFO_MONITOR_H_
