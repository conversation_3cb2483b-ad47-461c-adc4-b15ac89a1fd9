// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/17
// Description

#ifndef BLOCK_MANAGER_BLOCK_PUFS_NAME_GEN_H_
#define BLOCK_MANAGER_BLOCK_PUFS_NAME_GEN_H_

#include <cstdint>
#include <string>

#include "block_manager/block.h"

namespace dancenn {

class BlockPufsNameGen {
 public:
  static BlockPufsNameGen Default();

  BlockPufsNameGen(const std::string& tos_bucket,
                   const std::string& tos_prefix,
                   int64_t ns_id);
  // <tos_prefix>/<ns_id>/block/<block_id>-<random-number>.block
  std::string Get(BlockID blk_id);
  // trn:tos:::<tos_bucket>/<tos_prefix>/<ns_id>/block/*
  std::string GetTosResource();

 private:
  std::string tos_bucket_;
  std::string tos_prefix_;
  int64_t ns_id_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_PUFS_NAME_GEN_H_
