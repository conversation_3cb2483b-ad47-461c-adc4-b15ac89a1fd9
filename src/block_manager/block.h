// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCK_H_
#define BLOCK_MANAGER_BLOCK_H_

#include <hdfs.pb.h>  // NOLINT (build/include_order)

#include <cstdint>
#include <cstdlib>
#include <ostream>
#include <string>

#include "base/constants.h"
#include "base/murmur_hash.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

using BlockID = uint64_t;
const BlockID kInvalidBlockID = UINT64_MAX;

using cloudfs::BlockProto;

struct Block {
  BlockID id;
  uint32_t num_bytes;
  uint64_t gs;

  Block()
      : id(kInvalidBlockID),
        num_bytes(0),
        gs(kLastReservedGenerationStamp) {
  }

  Block(BlockID id, uint32_t len, uint64_t gs) {
    this->id = id;
    this->num_bytes = len;
    this->gs = gs;
  }

  explicit Block(const BlockProto& blk) {
    id = blk.blockid();
    num_bytes = blk.numbytes();
    gs = blk.genstamp();
  }

  explicit Block(const BlockInfoProto& bip) {
    id = bip.block_id();
    num_bytes = bip.num_bytes();
    gs = bip.gen_stamp();
  }

  void Init(BlockID id, uint32_t len, uint64_t gs) {
    this->id = id;
    this->num_bytes = len;
    this->gs = gs;
  }

  bool operator ==(const Block& b) const {
    return b.id == id;
  }

  std::string ToString() const {
    std::string str = "block { B" + std::to_string(id) +
        ", num_bytes " + std::to_string(num_bytes) +
        ", gs " + std::to_string(gs) + " }";
    return str;
  }

  friend std::ostream& operator<<(std::ostream& out, const Block& block) {
    out << "block { B" << block.id
        << ", num_bytes " << block.num_bytes
        << ", gs " << block.gs << " }";
    return out;
  }
};

struct BlockHash {
  size_t operator() (const Block& block) const {
    return std::hash<BlockID>{}(block.id);
  }
};

struct BlockHashUnordered {
  size_t operator()(const Block& block) const {
    static const auto kSeed = static_cast<uint32_t>(0xc70f6907UL);

    return murmur_hash_32(
        reinterpret_cast<const char*>(&block.id), sizeof(block.id), kSeed);
  }
};

inline bool IsBlockIDValid(BlockID blk_id) {
  return blk_id != 0 && blk_id != kInvalidBlockID;
}

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_H_

