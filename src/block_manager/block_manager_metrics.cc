// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/block_manager_metrics.h"

#include "block_manager/block_manager.h"

namespace dancenn {

BlockManagerMetrics::BlockManagerMetrics(BlockManager* bm) : bm_(bm) {
  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("BlockManager");
  num_blocks_ = metrics_->RegisterGauge("NumBlocks",
      [bm] () -> double {
        return bm->GetBlockNum();
      });
  num_uc_ = metrics_->RegisterGauge("NumUC",
      [bm] () -> double {
        return bm->stat_.num_uc;
      });
  num_postponed_misreplicated_ =
    metrics_->RegisterGauge("NumPostponedMisreplicated",
        [bm] () -> double {
          return bm->stat_.num_postponed_misreplicated;
        });
  num_invalidate_ = metrics_->RegisterGauge("NumInvalidate",
      [bm] () -> double {
        return bm->stat_.num_invalidate;
      });
  num_truncatable_ = metrics_->RegisterGauge(
      "NumTruncatable", [bm]() -> double { return bm->stat_.num_truncatable; });
  num_corrupt_ = metrics_->RegisterGauge("NumCorrupt",
      [bm] () -> double { return bm->stat_.num_corrupt_block; });
  num_sealed_ = metrics_->RegisterGauge(
      "NumSealed", [bm]() -> double { return bm->stat_.num_sealed_block; });
  num_recover_ = metrics_->RegisterGauge("NumRecover",
      [bm] () -> double {
        return bm->stat_.num_recover;
      });
  num_excess_ = metrics_->RegisterGauge("NumExcess",
      [bm] () -> double {
        return bm->stat_.num_excess_replicas;
      });
  num_in_replication_queue_ = metrics_->RegisterGauge("NumInReplicationQueue",
      [bm] () -> double {
        return bm->stat_.num_replication_queue;
      });
  num_future_blocks_ = metrics_->RegisterGauge("NumFutureBlocks",
      [bm] () -> double {
        return bm->stat_.num_future_blocks;
      });
  num_misinvalidated_blocks_ =
    metrics_->RegisterGauge("NumMisinvalidatedBlocks",
        [bm] () -> double {
          return bm->stat_.num_misinvalidated_blocks;
        });
  num_pending_replication_blocks_ =
    metrics_->RegisterGauge("NumPendingReplicationBlocks",
        [bm] () -> double {
          return bm->stat_.num_pending_replication;
        });

  ccp_last_block_lock_acquire_time_ =
    metrics_->RegisterHistogram(
        "CommitOrCompleteLastBlockTime#step=LockAcquire");
  ccp_last_block_locate_time_ =
    metrics_->RegisterHistogram("CommitOrCompleteLastBlockTime#step=Locate");
  ccp_last_block_commit_time_ =
    metrics_->RegisterHistogram("CommitOrCompleteLastBlockTime#step=Commit");
  ccp_last_block_complete_time_ =
    metrics_->RegisterHistogram("CommitOrCompleteLastBlockTime#step=Complete");
  ccp_last_block_inc_safe_block_count_time_ =
    metrics_->RegisterHistogram(
        "CommitOrCompleteLastBlockTime#step=IncSafeBlockCount");

  add_block_lock_acquire_time_ =
    metrics_->RegisterHistogram("AddBlockTime#step=LockAcquire");
  add_block_allocate_element_time_ =
    metrics_->RegisterHistogram("AddBlockTime#step=AllocateElement");
  add_block_insert_into_bucket_time_ =
    metrics_->RegisterHistogram("AddBlockTime#step=InsertIntoBucket");
  add_block_add_storage_time_ =
    metrics_->RegisterHistogram("AddBlockTime#step=AddStorage");
  add_block_insert_uc_state_time_ =
    metrics_->RegisterHistogram("AddBlockTime#step=InsertUCState");

  scan_slice_lock_acquire_time_ =
    metrics_->RegisterHistogram("ScanSliceTime#step=LockAcquire");
  scan_slice_release_blocks_time_ =
    metrics_->RegisterHistogram("ScanSliceTime#step=ReleaseBlocks");

  enqueue_blk_log_time_ =
    metrics_->RegisterHistogram("EnqueueBLKLifecycleTime");

  repl_work_choose_under_repl_blks_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=ChooseUnderReplicatedBlocks");
  repl_work_lock_acquire_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=LockAcquire");
  repl_work_get_block_info_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=GetBlockInfo");
  repl_work_get_placement_advice_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=GetPlacementAdvice");
  repl_work_choose_src_dn_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=ChooseSourceDatanode");
  repl_work_has_enough_rack_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=HasEnoughRack");
  repl_work_build_src_path_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=BuildSrcPath");
  repl_work_choose_tgt_dn_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=ChooseTarget4Recover");
  repl_work_add_to_be_replicated_time_ = metrics_->RegisterHistogram(
      "ComputeReplicationWorkTime#step=AddToBeReplicated");

  // compute replication work
  repl_work_all_scheduled_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Scheduled#type=all");
  repl_work_scheduled_move_dc_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Scheduled#type=move_dc");
  repl_work_scheduled_decommission_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Scheduled#type=decommission");
  repl_work_scheduled_normal_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Scheduled#type=normal");

  for (size_t p = 0; p < UnderReplicatedBlocks::kPriorityCount; p++) {
    repl_work_processed_blk_counter_[p] = metrics_->RegisterCounter(
        "ComputeReplicationWorkCounter.Processed#priority=" +
        std::to_string(p) + "#priority_str=" +
        UnderReplicatedBlocks::PriorityDesc(
            static_cast<UnderReplicatedBlocks::Priority>(p)));
  }

  repl_work_move_back_to_queue_counter_ =
      metrics_->RegisterCounter("ComputeReplicationWorkCounter.MoveBack");

  repl_work_choose_src_failed_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Failed#reason=choose_src_failed");
  repl_work_choose_target_failed_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Failed#reason=choose_target_failed");
  repl_work_no_need_replication_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Failed#reason=no_need_replication");
  repl_work_cross_dc_throttled_counter_ = metrics_->RegisterCounter(
      "ComputeReplicationWorkCounter.Failed#reason=cross_dc_throttled");

  persist_block_fail_count_ = metrics_->RegisterCounter(
      "PersistBlock#status=fail");
  persist_block_succ_count_ = metrics_->RegisterCounter(
      "PersistBlock#status=succ");
  persist_block_pufs_name_mismatch_ = metrics_->RegisterCounter(
      "PersistBlockPufsNameMismatch");

  for (size_t i = 0; i < bm->needed_replications().kPriorityCount; ++i) {
    num_under_replicated_.emplace_back(
        metrics_->RegisterGauge(
          "NumUnderReplicated#priority=" + std::to_string(i) +
          "#priority_str=" +
          UnderReplicatedBlocks::PriorityDesc(
            static_cast<UnderReplicatedBlocks::Priority>(i)),
          [bm, i] () -> double {
            auto sep = bm->needed_replications().SizeEachPriority();
            return sep[i];
          }));
  }

  fbr_decode_blocks_failed_ =
      metrics_->RegisterCounter("BlockReport#type=DecodeBlocksFailed");

  ibr_evicted_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=Evicted");
  ibr_sealed_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=Sealed");
  ibr_deleted_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=Deleted");
  ibr_received_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=Received");
  ibr_receiving_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=Receiving");
  ibr_upload_id_negoed_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=UploadIdNegoed");
  ibr_upload_succeed_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=UploadSucceed");
  ibr_upload_failed_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=UploadFailed");
  ibr_load_failed_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=LoadFailed");
  ibr_pufs_deleted_ops_ =
      metrics_->RegisterCounter("IncrementalBlockReport#type=PufsDeleted");

  ibr_total_time_ =
      metrics_->RegisterHistogram("IncrementalBlockReport#step=Total");
  ibr_storage_process_time_ =
      metrics_->RegisterHistogram("IncrementalBlockReport#step=ProcessStorage");
  ibr_block_index_update_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=UpdateBlockIndex");
  ibr_lock_acquire_time_ =
      metrics_->RegisterHistogram("IncrementalBlockReport#step=AcquireLock");
  ibr_deleted_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessDeletedBlk");
  ibr_sealed_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessSealedBlk");
  ibr_evicted_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessEvictedBlk");
  ibr_received_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessReceivedBlk");
  ibr_receiving_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessReceivingBlk");
  ibr_upload_id_negoed_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessUploadIdNegoedBlk");
  ibr_upload_succeed_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessUploadSucceedBlk");
  ibr_upload_failed_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessUploadFailedBlk");
  ibr_pufs_deleted_blk_process_time_ = metrics_->RegisterHistogram(
      "IncrementalBlockReport#step=ProcessDeletedBlk");

  upload_nego_ops_approve_ =
      metrics_->RegisterCounter("UploadNego#type=Approve");
  upload_nego_ops_deny_ = metrics_->RegisterCounter("UploadNego#type=Deny");
  upload_nego_ops_notify_upload_ =
      metrics_->RegisterCounter("UploadNego#type=NotifyUpload");
  upload_nego_ops_notify_evictable_ =
      metrics_->RegisterCounter("UploadNego#type=NotifyEvictable");
  upload_nego_ops_persist_evictable_ =
      metrics_->RegisterCounter("UploadNego#type=PersisEvictable");

  pusb_callback_persist_block_time_ = metrics_->RegisterHistogram(
      "ProcessUploadSucceedBlock#step=PersistBlock");
  pusb_callback_process_over_replica_time_ = metrics_->RegisterHistogram(
      "ProcessUploadSucceedBlock#step=ProcessOverReplica");
  pusb_callback_add_notify_evictable_cmd_time_ = metrics_->RegisterHistogram(
      "ProcessUploadSucceedBlock#step=AddNotifyEvictableCmd");

  load_cmd_block_count_ = metrics_->RegisterCounter("LoadCmdBlock");
  add_transfer_block_count_ = metrics_->RegisterCounter("AddTransferBlock");
  add_transfer_block_failed_count_ = metrics_->RegisterCounter("AddTransferBlockFailed");

  dn_cmd_cnt_balancer_ =
      metrics_->RegisterCounter("DatanodeCommand#type=balancer");
  dn_cmd_cnt_block_transfer_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_transfer");
  dn_cmd_cnt_block_invalidate_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_invalidate");
  dn_cmd_cnt_block_shutdown_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_shutdown");
  dn_cmd_cnt_block_finalized_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_finalized");
  dn_cmd_cnt_block_recovery_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_recovery");
  dn_cmd_cnt_finalize_ =
      metrics_->RegisterCounter("DatanodeCommand#type=finalize");
  dn_cmd_cnt_key_update_ =
      metrics_->RegisterCounter("DatanodeCommand#type=key_update");
  dn_cmd_cnt_register_ =
      metrics_->RegisterCounter("DatanodeCommand#type=register");
  dn_cmd_cnt_unused_upgrade_ =
      metrics_->RegisterCounter("DatanodeCommand#type=unused_upgrade");
  dn_cmd_cnt_null_datanode_ =
      metrics_->RegisterCounter("DatanodeCommand#type=null_datanode");
  dn_cmd_cnt_block_id_cache_ =
      metrics_->RegisterCounter("DatanodeCommand#type=cache");
  dn_cmd_cnt_block_id_uncache_ =
      metrics_->RegisterCounter("DatanodeCommand#type=uncache");
  dn_cmd_cnt_block_id_lifecycle_hot_ =
      metrics_->RegisterCounter("DatanodeCommand#type=lifecycle_hot");
  dn_cmd_cnt_block_id_lifecycle_warm_ =
      metrics_->RegisterCounter("DatanodeCommand#type=lifecycle_warm");
  dn_cmd_cnt_block_id_lifecycle_cold_ =
      metrics_->RegisterCounter("DatanodeCommand#type=lifecycle_cold");
  dn_cmd_cnt_block_id_pin_ =
      metrics_->RegisterCounter("DatanodeCommand#type=pin");
  dn_cmd_cnt_block_id_unpin_ =
      metrics_->RegisterCounter("DatanodeCommand#type=unpin");
  dn_cmd_cnt_upload_ = metrics_->RegisterCounter("DatanodeCommand#type=upload");
  dn_cmd_cnt_notify_evictable_ =
      metrics_->RegisterCounter("DatanodeCommand#type=notify_evictable");
  dn_cmd_cnt_invalidate_pufs_ =
      metrics_->RegisterCounter("DatanodeCommand#type=invalidate_pufs");
  dn_cmd_cnt_block_report_ =
      metrics_->RegisterCounter("DatanodeCommand#type=block_report");
  dn_cmd_cnt_merge_ = metrics_->RegisterCounter("DatanodeCommand#type=merge");
  dn_cmd_cnt_abort_block_report_ =
      metrics_->RegisterCounter("DatanodeCommand#type=abort_block_report");
  dn_cmd_cnt_load_ = metrics_->RegisterCounter("DatanodeCommand#type=load");

  bm_uploader_ongoing_ =
      metrics_->RegisterGauge("BgUploader.Ongoing", [this]() -> double {
        if (bm_->bg_uploader_worker_) {
          auto cnt = bm_->bg_uploader_worker_->NumRunningTasks();
          return static_cast<double>(cnt);
        } else {
          return 0;
        }
      });

  bm_uploader_pending_ =
      metrics_->RegisterGauge("BgUploader.Pending", [this]() -> double {
        if (bm_->bg_uploader_worker_) {
          auto cnt = bm_->bg_uploader_worker_->PendingCount();
          return static_cast<double>(cnt);
        } else {
          return 0;
        }
      });

  bm_evict_deleter_ongoing_ =
      metrics_->RegisterGauge("BgEvictDeleter.Ongoing", [this]() -> double {
        if (LIKELY(bm_->bg_evict_deleter_worker_.size())) {
          size_t cnt = 0;
          for (auto& w : bm_->bg_evict_deleter_worker_) {
            cnt += w->NumRunningTasks();
          }
          return static_cast<double>(cnt);
        } else {
          return 0;
        }
      });

  bm_evict_deleter_pending_ =
      metrics_->RegisterGauge("BgEvictDeleter.Pending", [this]() -> double {
        if (LIKELY(bm_->bg_evict_deleter_worker_.size())) {
          size_t cnt = 0;
          for (auto& w : bm_->bg_evict_deleter_worker_) {
            cnt += w->PendingCount();
          }
          return static_cast<double>(cnt);
        } else {
          return 0;
        }
      });

  dn_cmd_time_total_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=Total");
  dn_cmd_time_block_recover_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=BlockRecover");
  dn_cmd_time_block_transfer_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=BlockTransfer");
  dn_cmd_time_block_invalidate_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=BlockInvalidate");
  dn_cmd_time_lifecycle_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=Lifecycle");
  dn_cmd_time_key_update_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=KeyUpdate");
  dn_cmd_time_upload_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=UploadCmd");
  dn_cmd_time_notify_evictable_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=NotifyEvictable");
  dn_cmd_time_load_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=Load");
  dn_cmd_time_invalidate_pufs_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=InvalidatePufs");
  dn_cmd_time_block_id_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=BlockId");
  dn_cmd_time_merge_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=Merge");
  dn_cmd_time_truncatable_ =
      metrics_->RegisterHistogram("DatanodeCommand.Time#step=Truncatable");
}

void BlockManagerMetrics::IncBlockTransTraffic(const std::string& src_dc,
                                               const std::string& tgt_dc,
                                               const std::string& type,
                                               int64_t num_bytes) {
  auto key = src_dc + "|" + tgt_dc + "|" + type;
  {
    std::shared_lock<ReadWriteLock> rguard(blk_trans_mutex_);
    auto itr = blk_trans_.find(key);
    if (itr != blk_trans_.end()) {
      auto& pair = itr->second;
      auto& count_metrics = pair.first;
      auto& bytes_metrics = pair.second;
      MFC(count_metrics)->Inc();
      MFC(bytes_metrics)->Inc(num_bytes);
      return;
    }
  }
  {
    std::string count_key =
        "BlockTransfer.count#cross_dc=" + type + "#src_dc=" + src_dc +
        "#tgt_dc=" + tgt_dc;
    std::string bytes_key =
        "BlockTransfer.bytes#cross_dc=" + type + "#src_dc=" + src_dc +
        "#tgt_dc=" + tgt_dc;
    std::unique_lock<ReadWriteLock> wguard(blk_trans_mutex_);
    auto itr = blk_trans_.find(key);
    if (itr == blk_trans_.end()) {
      auto pair = std::make_pair(metrics_->RegisterCounter(count_key),
                                 metrics_->RegisterCounter(bytes_key));
      itr = blk_trans_.emplace(key, pair).first;
    }
    auto& pair = itr->second;
    auto& count_metrics = pair.first;
    auto& bytes_metrics = pair.second;
    MFC(count_metrics)->Inc();
    MFC(bytes_metrics)->Inc(num_bytes);
  }
}

}  // namespace dancenn

