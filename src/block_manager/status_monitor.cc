#include "status_monitor.h"

#include <glog/logging.h>

#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(status_monitor_interval_ms);

namespace dancenn {

void StatusMonitor::Do() {
  LOG(INFO) << "Start to do some Status works.";
  if (bm_->safemode_->IsOn()) {
    LOG(INFO) << "Skip due to safemode is on.";
    return;
  }
  auto start = std::chrono::steady_clock::now();
  bm_->UpdateState();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now() - start).count();
  LOG(INFO) << "Status monitor time: " << duration << "(ms)";
}

bool StatusMonitor::Task::operator()(void* arg) {
  (void) arg;

  while (!stop_) {
    monitor_->Do();

    int interval = FLAGS_status_monitor_interval_ms;
    LOG(INFO) << "Status monitor will sleep: " << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this] () -> bool { return stop_; });
  }
  return true;
}

}  // namespace dancenn

