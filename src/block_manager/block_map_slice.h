// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_BLOCK_MAP_SLICE_H_
#define BLOCK_MANAGER_BLOCK_MAP_SLICE_H_

#include <cstdint>
#include <cstdlib>
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/read_write_lock.h"
#include "base/status.h"
#include "base/stop_watch.h"
#include "block_info.h"
#include "block_manager/block.h"
#include "block_manager/block_info.h"
#include "block_manager/datanode_command.h"
#include "block_manager/pending_replication_blocks.h"
#include "block_manager/under_construction_state.h"
#include "namespace/meta_storage.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"

namespace dancenn {

using cloudfs::DatanodeStorageProto;
using cloudfs::ExtendedBlockProto;
using cloudfs::RecoveringBlockProto;
using cloudfs::ReplicaStateProto;
using cloudfs::datanode::CommitBlockSynchronizationRequestProto;

struct ReportedBlockInfo {
  Block blk;
  DatanodeID dn_id;
  DatanodeStorageProto dsp;
  ReplicaStateProto rsp;
  std::chrono::time_point<std::chrono::steady_clock> create_time;
};

enum ReportedReplicaResult {
  NOTHING = 0,
  ADD = 1,
  INVALIDATE = 2,
  CORRUPT = 3,
  ADD_TO_BLOCK_INDEX = 4,
  TRUNCATE_BLOCK = 5,
};

struct BlockManagerMetrics;

class BlockMapSlice;
// BlockMapSlice::Locate may or may not load block to memory.
// This class helps manage the lifecycle of BlockInfo when `load_to_memory` is
// false. When `load_to_memory` is true, BlockInfo is always managed by
// BlockMapSlice, and is OK to use the BlockInfo even after guard has been
// destroyed.
// Best practice:
// Set load_to_memory to false only when you are SURE that BlockManager do NOT
// need to keep the BlockInfo. Otherwise, set it to true is a safe option.
class BlockInfoGuard {
 public:
  BlockInfoGuard() = delete;
  explicit BlockInfoGuard(BlockMapSlice* slice,
                          BlockID block_id,
                          bool load_to_memory);
  explicit BlockInfoGuard(BlockMapSlice* slice,
                          BlockID block_id,
                          uint8_t replica_num,
                          bool load_to_memory);
  BlockInfoGuard(const BlockInfoGuard& o) = delete;
  ~BlockInfoGuard();
  BlockInfo* GetBlockInfo();

 private:
  void Locate(BlockMapSlice* slice,
              BlockID block_id,
              uint8_t replica_num,
              bool load_to_memory);

 private:
  BlockInfo* element_{nullptr};
  bool need_delete_{false};
};

class DatanodeManager;
// NOTICE: Be sure the rwlock_ has already been held properly by
// the BlockManager before read or modify the data
class BlockMapSlice {
 public:
  explicit BlockMapSlice(uint32_t slice_id, BlockManagerMetrics* metrics);
  BlockMapSlice(const BlockMapSlice& a) = delete;
  ~BlockMapSlice();

  void lock_shared() { rwlock_.lock_shared(); }
  void unlock_shared() { rwlock_.unlock_shared(); }
  void lock();
  void unlock();
  void TellLockHolder(const char* file, int lineno);

  size_t num_bucket() const {
    return num_buckets_;
  }

  void SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage);
  void SetDatanodeManager(std::shared_ptr<DatanodeManager> datanode_manager);

  // DO NOT use Locate() from outside of BlockMapSlice
  // To get BlockInfo, caller should use BlockInfoGuard
  // Return address can be either:
  // a) Newly allocated element for persisted blocks, caller is responsible for
  // freeing the memory. `need_delete` will be set to true.
  // b) Records in slice for non-persisted block, slice will
  // maintain the lifecycle for the element. `need_delete` will be set to false.
  BlockInfo* Locate(BlockID blk_id,
                    uint8_t replica_num,
                    bool load_to_memory,
                    bool* need_delete);
  // `need_delete` can be ignored if `load_to_memory` is true
  BlockInfo* Locate(BlockID blk_id);

  void AddBlock(uint64_t inode_id,
                uint64_t parent_id,
                const Block& blk,
                uint8_t capacity,
                cloudfs::IoMode write_mode,
                const std::vector<DatanodeID>& dns,
                BlockUCState state);

  void LoadBlock(uint64_t inode_id,
                 uint64_t parent_id,
                 const Block& blk,
                 uint8_t capacity,
                 cloudfs::IoMode write_mode,
                 const std::vector<DatanodeID>& dns,
                 BlockUCState state);
  bool RemoveBlock(BlockID blk_id);

  BlockInfo* AddStorage(BlockInfo* orig,
                        DatanodeID storage_id,
                        bool* already_exist = nullptr);
  BlockInfo* AddStorages(BlockInfo* orig, const std::vector<DatanodeID>& dns);

  std::vector<DatanodeID> GetBlockStorages(BlockID blk_id);
  std::vector<DatanodeID> GetBlockStoragesSafe(BlockID blk_id);

  // NOTICE: slice write lock should be held
  ReportedReplicaResult ProcessReportedBlock(DatanodeID dn_id,
                                             const Block& block,
                                             ReplicaStateProto state,
                                             bool is_active = true,
                                             bool dn_decommission = false,
                                             uint8_t live_replica = 1);

  bool CompleteBlock(BlockInfo* orig,
                     bool force,
                     BlockUCState* prev_state = nullptr);

  bool CommitBlock(BlockInfo* bi, const Block& commit_block);

  bool PersistBlock(BlockInfo* bi,
                    const std::string& blockpool_id,
                    const Block& persist_block);

  Status CommitBlockSynchronization(
      const CommitBlockSynchronizationRequestProto& request);

  void UpdateRecoverPrimary(
      BlockID blk_id,
      uint64_t new_gs,
      DatanodeID primary,
      const std::unordered_map<DatanodeID, bool>& tried_as_primary);

  Status UpdatePipeline(const ExtendedBlockProto& new_block,
                        const std::vector<DatanodeID>& new_nodes);

  bool GetRecoverCommand(BlockID blk_id,
                         const std::string& bp_id,
                         std::vector<DatanodeID>* dns,
                         RecoveringBlockProto* recovering_blk);

  std::shared_ptr<UnderConstructionState> GetUcInternal(BlockID blk_id);
  size_t GetUCSize();

  void ConvertToUnderConstruction(BlockInfo* bi);

  std::vector<DatanodeID> GetExpectedLocations(BlockID blk_id);

  bool TraverseAllBlock(std::function<bool(BlockInfo**, size_t)> cb);
  // return false means we've processed all buckets, else return true
  bool TraverseBuckets(size_t begin_bucket_id,
                       size_t n,
                       std::function<bool(BlockInfo*)> cb);

  bool IsBlockInvalidate(const Block& block, DatanodeID dn_id);
  void ClearInvalidateBlock();
  uint64_t GetInvalidateBlockNum();
  // Deprecated: unused
  std::unordered_set<Block, BlockHash>* GetInvalidateBlock(DatanodeID dn_id);
  void GetInvalidateBlock(
      std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>*
          blks);
  void EraseInvalidateBlock(DatanodeID dn_id);
  void AddToInvalidateBlock(DatanodeID dn_id,
                            const Block& block,
                            const std::string& reason = "");
  void RemoveFromInvalidateBlock(DatanodeID dn_id, const Block& block);

  bool IsBlockTruncatable(const Block& block, DatanodeID dn_id);
  void ClearTruncatableBlock();
  uint64_t GetTruncatableBlockNum();
  uint64_t GetTruncatableBlockNum(DatanodeID dn_id);
  std::unordered_set<Block, BlockHash>* PeekTruncatableBlock(DatanodeID dn_id);
  void PopTruncatableBlock(
      std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>*
          blks);
  void EraseTruncatableBlock(DatanodeID dn_id);
  void PushTruncatableBlock(DatanodeID dn_id,
                            const Block& block,
                            const std::string& reason = "");
  void RemoveFromTruncatableBlock(DatanodeID dn_id, const Block& block);

  // Deprecated: unused
  void AddUploadCmd(DatanodeID dn_id, const UploadCmd& cmd);
  // Deprecated: unused
  std::unordered_map<DatanodeID, UploadCmdSet> GetUploadCmds();
  // Deprecated: unused
  void AddNotifyEvictableCmd(DatanodeID dn_id, const NotifyEvictableCmd& cmd);
  // Deprecated: unused
  std::unordered_map<DatanodeID, NotifyEvictableCmdSet>
  GetNotifyEvictableCmds();

  uint64_t GetCorruptBlockNum();
  bool IsBlockCorrupt(BlockID blk_id, DatanodeID dn_id);
  std::unordered_map<DatanodeID, Block>* GetCorruptBlock(BlockID blk_id);
  std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>*
  GetCorruptBlock();
  void EraseCorruptBlock(BlockID blk_id);
  void AddToCorruptBlock(BlockID blk_id, DatanodeID dn_id, Block blk);
  void RemoveFromCorruptBlock(BlockID blk_id, DatanodeID dn_id);

  bool IsBlockExcess(BlockID blk_id, DatanodeID dn_id);
  void ClearExcessBlock();
  uint64_t GetExcessBlockNum();
  void EraseExcessBlock(DatanodeID dn_id);
  void AddToExcess(BlockID blk_id, DatanodeID dn_id);
  void RemoveFromExcess(BlockID blk_id, DatanodeID dn_id);

  uint64_t GetSealedBlockNum();
  bool IsBlockSealed(BlockID blk_id, DatanodeID dn_id);
  // NOTICE: unused function
  std::unordered_map<DatanodeID, Block>* GetSealedBlock(BlockID blk_id);
  std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>*
  GetSealedBlock();
  void MoveSealedBlockToTruncatable(std::function<bool()> checker);
  void EraseSealedBlock(BlockID blk_id);
  void AddToSealedBlock(BlockID blk_id, DatanodeID dn_id, Block blk);
  void RemoveFromSealedBlock(BlockID blk_id, DatanodeID dn_id);

  void ClearPendingReplications();
  void RemovePendingReplications(BlockID blk_id);
  uint64_t GetPendingReplicationsNum();
  size_t NumReplicas(BlockID blk_id);
  void IncrementReplicas(BlockID blk_id,
                         const std::vector<DatanodeID>& targets);
  void DecrementReplicas(BlockID blk_id, DatanodeID dn_id);
  void TimedOutBlocks(std::vector<BlockID>* timeout_blocks);

  void EnqueuePendingFutureBlks(const DatanodeStorageProto& dsp,
                                const Block& block,
                                DatanodeID dn_id,
                                const ReplicaStateProto& rsp);
  void RemoveAllPendingFutureBlks(DatanodeID dn_id);
  void DequeuePendingFutureBlks(BlockID block_id,
                                std::deque<ReportedBlockInfo>* rbis);
  void DequeuePendingFutureBlks(std::deque<ReportedBlockInfo>* rbis);
  size_t NumFutureBlocks();

  void EnqueuePendingMisinvalidatedBlks(const ReportedBlockInfo& rbi);
  void DequeuePendingMisinvalidatedBlks(std::deque<ReportedBlockInfo>* rbis);
  size_t NumMisinvalidatedBlks();

  void EnqueuePendingPersistedBlks(const Block& blk);
  bool RemovePendingPersistedBlks(const Block& blk);
  size_t NumPersistedBlks();

  // NOTICE: Slice lock is already taken.
  void ReleasePersistedAndCacheFreeBlocks();

 private:
  uint32_t slice_id_;

  friend struct BlockManagerMetrics;
  BlockManagerMetrics* metrics_{nullptr};

  // We found that lock acquire time is very long.
  // In billion-small-files namespace:
  // avg: 10ms, p99: 300ms, p999: 2s
  // We want to know who take lock for a long time, then blocking others.
  int64_t unique_lock_acquire_ts_in_ms_;
  BlockID last_access_blk_id_;
  bool access_meta_storage_;
  const char* lock_holder_file_{nullptr};
  int lock_holder_line_number_;

  std::shared_ptr<MetaStorage> meta_storage_;
  std::shared_ptr<DatanodeManager> datanode_manager_;

  ReadWriteLockLight rwlock_;
  size_t num_buckets_;
  BlockInfo** buckets_{nullptr};
  std::unordered_map<BlockID, std::shared_ptr<UnderConstructionState>>
      uc_states_;

  size_t BlockIdHash(BlockID blk_id) const;
  size_t BlockIdBucket(BlockID blk_id) const;
  BlockInfo* AllocateElement(uint8_t capacity);
  BlockInfo* CopyElement(BlockInfo* orig, uint8_t capacity);
  void ReplaceElement(BlockInfo* orig, BlockInfo* element);

  std::shared_ptr<UnderConstructionState> FilterInvalidReplica(
      BlockID blk_id,
      uint64_t generation_stamp);

  bool CheckBlockCorrupt(const BlockInfo* stored_block,
                         ReplicaStateProto report_state,
                         const Block& reported_block,
                         bool dn_decommission,
                         uint8_t live_replica);
  bool CheckUnderConstruction(const BlockInfo* stored_block,
                              ReplicaStateProto reported_state);

  void UpdateGenerationStamp(BlockInfo* element, uint64_t gs);

  std::vector<DatanodeID> storage_ids(BlockInfo* bi);

  std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>
      to_invalidate_;
  int64_t to_invalidate_block_size_{0};
  std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>
      to_truncate_;
  int64_t to_truncate_block_size_{0};
  std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>
      sealed_blocks_;
  std::unordered_map<BlockID, std::unordered_map<DatanodeID, Block>>
      corrupt_blocks_;
  std::unordered_map<DatanodeID, std::unordered_set<BlockID>> excess_replicas_;

  // Deprecated: unused
  // Notice: Changing block pufs state to BlockPufsInfo before
  // sending command is by design.
  // Persist block pufs info -> Record unsent cmds in memory -> Send
  // When name node failover, we have two choices:
  // 1. Send two commands to data node.
  //    Started -> Send Cmd -> Failover -> Send Cmd -> Persist block pufs info
  // 2. Send no command to data node.
  //    Persist block pufs info -> Failover
  // We choose the former.
  std::unordered_map<DatanodeID, UploadCmdSet> upload_cmds_;
  std::unordered_map<DatanodeID, NotifyEvictableCmdSet> notify_evictable_cmds_;

  // for standby, blk report is faster than editlog tailer,
  // so we we need to record them then deal with later.
  std::unordered_map<BlockID, std::deque<ReportedBlockInfo>>
      pending_future_blks_;

  // for standby, 'mis_invalidated_blks' will be generated in two cases:
  // 1. report an elderly block, who ws deleted long time ago.
  // 2. report a younger block, meanwhile the editlog tailer work is not
  //    ready yet, because 'OP_ADD_BLOCK' is fall behind
  //    the 'OP_SET_GENSTAMP_V2'.
  // to sum up, we need to record them then deal with later.
  std::unordered_map<BlockID, std::deque<ReportedBlockInfo>>
      pending_misinvalidated_blks_;

  struct PendingPersistedBlkEqualTo {
    bool operator()(const Block& lhs, const Block& rhs) const {
      return lhs.id == rhs.id && lhs.num_bytes == rhs.num_bytes &&
             lhs.gs == rhs.gs;
    }
  };
  std::unordered_set<Block, BlockHash, PendingPersistedBlkEqualTo>
      pending_persisted_blks_;

  PendingReplicationBlocks pending_replications_;
  std::mutex pending_replications_mutex_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_MAP_SLICE_H_
