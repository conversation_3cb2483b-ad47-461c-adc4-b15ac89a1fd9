// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/08/16
// Description

#ifndef BLOCK_MANAGER_BLOCK_INFO_PROTO_H_
#define BLOCK_MANAGER_BLOCK_INFO_PROTO_H_

#include <gflags/gflags.h>
#include <glog/logging.h>

#include <random>

#include "base/logger_metrics.h"
#include "block_manager/datanode_command.h"
#include "namespace/inode.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

DECLARE_int32(min_upload_timeout_s);
DECLARE_int32(max_upload_timeout_s);
DECLARE_int32(randomized_upload_timeout_s);
DECLARE_int32(nn_dn_clock_drift_s);
DECLARE_int32(namespace_type);

namespace dancenn {

inline void MakeBlockInfoProto(const cloudfs::BlockProto& bp,
                               BlockInfoProto_Status state,
                               INodeID inode_id,
                               uint32_t rep_num,
                               BlockInfoProto* bip) {
  CHECK_NOTNULL(bip);
  bip->set_state(state);
  bip->set_block_id(bp.blockid());
  bip->set_gen_stamp(bp.genstamp());
  bip->set_num_bytes(bp.numbytes());
  bip->set_inode_id(inode_id);
  bip->set_expected_rep(rep_num);
}

inline bool MakeCommittedPenultimateBlockInfoProto(const INode& inode,
                                                   BlockInfoProto* bip) {
  CHECK_NOTNULL(bip);
  auto bsize = inode.blocks_size();
  if (bsize < 2) {
    return false;
  }
  const cloudfs::BlockProto& penultimate_bp = inode.blocks(bsize - 2);
  MakeBlockInfoProto(
      penultimate_bp,
      BlockInfoProto::kComplete,
      inode.id(),
      inode.has_replication() ? inode.replication() : FLAGS_dfs_replication,
      bip);
  return true;
}

inline bool MakeLastBlockInfoProto(const INode& inode, BlockInfoProto* bip) {
  CHECK_NOTNULL(bip);
  auto bsize = inode.blocks_size();
  if (bsize < 1) {
    return false;
  }
  const cloudfs::BlockProto& last_bp = inode.blocks(bsize - 1);
  MakeBlockInfoProto(
      last_bp,
      BlockInfoProto::kUnderConstruction,
      inode.id(),
      inode.has_replication() ? inode.replication() : FLAGS_dfs_replication,
      bip);
  return true;
}

inline UploadCmd GetUploadCmd(const std::string& blockpool_id,
                              const BlockInfoProto& bip) {
  UploadCmd cmd;
  cmd.set_dnuuid(bip.dn_uuid());
  cmd.set_blockpoolid(blockpool_id);
  cmd.mutable_block()->set_blockid(bip.block_id());
  cmd.mutable_block()->set_genstamp(bip.gen_stamp());
  cmd.mutable_block()->set_numbytes(bip.num_bytes());
  cmd.set_blockpufsname(bip.pufs_name());
  cmd.set_expts(bip.dn_exp_ts());
  cmd.set_uploadid(bip.curr_upload_id());
  for (const auto& aborted_upload_id : bip.aborted_upload_ids()) {
    cmd.add_aborteduploadids(aborted_upload_id);
  }
  return cmd;
}

inline NotifyEvictableCmd GetNotifyEvictableCmd(const std::string& blockpool_id,
                                                const BlockInfoProto& bip,
                                                const cloudfs::StorageClassProto& cls,
                                                bool pinned) {
  NotifyEvictableCmd cmd;
  cmd.set_blockpoolid(blockpool_id);
  cmd.mutable_block()->set_blockid(bip.block_id());
  cmd.mutable_block()->set_genstamp(bip.gen_stamp());
  cmd.mutable_block()->set_numbytes(bip.num_bytes());
  cmd.set_cls(cls);
  cmd.set_pinned(pinned);
  return cmd;
}

inline void CheckAccBlock(const BlockInfoProto& bip) {
  CHECK(bip.has_type() && bip.type() == BlockInfoProto_Type_kACCBlock);
}

inline void CheckHdfsBlock(const BlockInfoProto& bip) {
  CHECK(!bip.has_type() || bip.type() == BlockInfoProto_Type_kHDFSBlock);
}

inline void FillBipWhenApprove(const std::string& block_pufs_name,
                               const std::string& upload_id,
                               const std::string& dn_uuid,
                               int64_t currentTsInSec,
                               BlockInfoProto* bip) {
  if (bip->type() == BlockInfoProto::Type::BlockInfoProto_Type_kHDFSBlock) {
    bip->set_pufs_name(block_pufs_name);
    constexpr int aborted_upload_ids_max_size = 512;
    if (bip->aborted_upload_ids_size() > aborted_upload_ids_max_size) {
      bip->mutable_aborted_upload_ids()->DeleteSubrange(
          0, bip->aborted_upload_ids_size() - aborted_upload_ids_max_size);
      MFC(LoggerMetrics::Instance().error_)->Inc();
      LOG_WITH_LEVEL(ERROR)
          << "Limit aborted upload ids size of " << bip->block_id();
    }
    if (bip->has_curr_upload_id() && bip->curr_upload_id() != upload_id) {
      bip->add_aborted_upload_ids(bip->curr_upload_id());
    }
    bip->set_curr_upload_id(upload_id);
  }
  if (bip->type() == BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock) {
    bip->set_pufs_name(block_pufs_name);
  }
  bip->set_state(BlockInfoProto::kUploadIssued);
  auto times = bip->has_upload_issued_times() ? bip->upload_issued_times() : 0;
  times += 1;
  bip->set_upload_issued_times(times);
  bip->set_dn_uuid(dn_uuid);
  int32_t interval = static_cast<int32_t>(FLAGS_min_upload_timeout_s * times);
  if (interval > FLAGS_max_upload_timeout_s) {
    interval = FLAGS_max_upload_timeout_s;
  }
  bip->set_dn_exp_ts(currentTsInSec + interval);

  std::random_device rd;
  std::mt19937 gen(rd());
  std::uniform_int_distribution<uint32_t> dis;
  auto rand_s = dis(gen) % FLAGS_randomized_upload_timeout_s;

  bip->set_nn_exp_ts(bip->dn_exp_ts() + FLAGS_nn_dn_clock_drift_s + rand_s);
}

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_INFO_PROTO_H_
