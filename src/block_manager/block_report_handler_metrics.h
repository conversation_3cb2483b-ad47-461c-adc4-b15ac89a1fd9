// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/01/28
// Description

#ifndef BLOCK_MANAGER_BLOCK_SERVICE_METRICS_H_
#define BLOCK_MANAGER_BLOCK_SERVICE_METRICS_H_

#include "base/metrics.h"  // For MetricID, MetricsCenter.

namespace dancenn {

class BlockReportHandlerMetrics {
 public:
  static BlockReportHandlerMetrics& Instance();

  BlockReportHandlerMetrics(const BlockReportHandlerMetrics& other) = delete;
  BlockReportHandlerMetrics(BlockReportHandlerMetrics&& other) = delete;
  BlockReportHandlerMetrics& operator=(const BlockReportHandlerMetrics& other) =
      delete;
  BlockReportHandlerMetrics& operator=(BlockReportHandlerMetrics&& other) =
      delete;
  ~BlockReportHandlerMetrics() = default;

 private:
  BlockReportHandlerMetrics() = default;

 public:
  // ibr: IncrementalBlockReport
  MetricID ibr_deleted_ops_;
  MetricID ibr_received_ops_;
  MetricID ibr_receiving_ops_;
  MetricID ibr_upload_ops_;
  MetricID ibr_persist_ops_;
  MetricID ibr_pufs_deleted_ops_;
  //
  MetricID ibr_total_time_;
  MetricID ibr_storage_time_;
  MetricID ibr_deleted_time_;
  MetricID ibr_received_time_;
  MetricID ibr_receiving_time_;
  MetricID ibr_upload_time_;
  MetricID ibr_persist_time_;
  MetricID ibr_pufs_deleted_time_;
  //
  MetricID ibr_bip_batch_sz_;
  MetricID ibr_call_mgr_write_time_;
  MetricID ibr_write_editlog_time_;
  MetricID ibr_write_meta_storage_time_;
  MetricID ibr_wait_callback_time_;
  MetricID ibr_execute_callback_time_;

  // fbr: FullBlockReport
  MetricID fbr_decode_raw_report_time_;
  MetricID fbr_make_tasks_time_;
  MetricID fbr_wait_callback_time_;
  MetricID fbr_callback_time_;
  //
  MetricID fbr_per_task_wait_execute_time_;
  MetricID fbr_per_task_execute_time_;
  MetricID fbr_per_task_log_edit_time_;
  MetricID fbr_per_task_wait_callback_time_;
  MetricID fbr_per_task_callback_time_;
};

};  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_SERVICE_METRICS_H_
