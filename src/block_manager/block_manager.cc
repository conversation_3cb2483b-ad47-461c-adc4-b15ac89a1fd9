// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/block_manager.h"

#include <absl/strings/str_format.h>
#include <aws/s3/model/HeadObjectRequest.h>
#include <fiu-control.h>
#include <fiu.h>
#include <glog/logging.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>

#include <algorithm>
#include <atomic>
#include <cassert>
#include <chrono>
#include <cmath>
#include <condition_variable>
#include <cstdint>
#include <cstring>
#include <deque>
#include <exception>
#include <limits>
#include <memory>
#include <mutex>
#include <random>
#include <shared_mutex>
#include <stdexcept>
#include <utility>

#include "base/committer_channel_context.h"
#include "base/constants.h"
#include "base/data_center_table.h"
#include "base/file_utils.h"
#include "base/hash.h"
#include "base/logger_metrics.h"
#include "base/parquet_writer.h"
#include "base/stop_watch.h"
#include "base/time_util.h"
#include "block_manager/block_pufs_info.h"
#include "block_manager/block_recycler.h"
#include "block_manager/datanode_command.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/sender.h"
#include "inode.pb.h"  // NOLINT
#include "namespace/lifecycle_policy_util.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "ufs/tos/tos_checksum_utils.h"

DECLARE_int32(blockmap_num_slice);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_int32(blockmap_sealed_limit_per_slice);
DECLARE_int32(blockmap_truncatable_limit_per_dn);
DECLARE_int32(blockmap_replication_work_multiplier);
DECLARE_int32(blockmap_max_replication_streams);
DECLARE_int32(blockmap_max_replication_streams_hard_limit);
DECLARE_uint64(blockmap_max_replication_replication_bytes);
DECLARE_int32(blockmap_max_replication_streams_decommission);
DECLARE_double(blockmap_invalidate_work_pct);
DECLARE_int32(dfs_replication_pending_timeout_ms);
DECLARE_double(blockmap_replication_chose_loocal_dc_pct);
DECLARE_int32(blockmap_num_postponed_blocks_rescan);
DECLARE_bool(blockmap_check_enough_racks);
DECLARE_int32(blk_report_thread_count);
DECLARE_int32(block_recovery_thread_count);
DECLARE_int32(blk_replication_thread_count);
DECLARE_int32(blk_uploader_thread_count);
DECLARE_int32(misinvalidated_block_queue_item_timeout_sec);
DECLARE_bool(append_reuse_last_block);
DECLARE_int64(blockmap_replication_cross_dc_limit_GBps);
DECLARE_int64(decommission_cross_dc_limit_GBps);
DECLARE_int32(replication_monitor_interval_ms);
DECLARE_bool(security_key_enable);
DECLARE_bool(ibr_deleted_replica_disable_cached_block_info);
DECLARE_int32(scan_one_block_map_slice_interval_ms);
DECLARE_int32(namespace_type);
DECLARE_int32(upload_cmds_max_size);
DECLARE_int32(ne_cmds_max_size);
DECLARE_int32(load_cmds_max_size);
DECLARE_int32(merge_cmds_max_size);
DECLARE_int32(pin_cmds_max_size);
DECLARE_string(tos_bucket);
DECLARE_uint64(fbr_protobuf_stream_limit_mb);
DECLARE_bool(lifecycle_enable);
DECLARE_uint32(datanode_max_block_number_in_decommission);
DECLARE_bool(move_back_need_replication_queue);
DECLARE_uint32(repl_work_max_process_missing_block);
DECLARE_bool(block_manager_allow_transfer_persisted_blocks);
DECLARE_bool(log_dn_negoed_ibr);
DECLARE_bool(skip_dn_negoed_ibr);
DECLARE_bool(enable_storage_class);
DECLARE_bool(block_expected_replica_determined_by_inode);
DECLARE_int32(load_compatible_dn_version);
DECLARE_int32(del_depring_blks_recycle_times);
DECLARE_int32(del_depred_blks_recycle_times);
DECLARE_uint64(replication_grace_time_ms);
DECLARE_uint64(replication_grace_last_block_num);
DECLARE_bool(enable_transfer_persist_block);
DECLARE_bool(enable_load_for_complete_replica);
DECLARE_bool(enable_load_task_generate_ufs_name);
DECLARE_bool(trace_rpc_log);
DECLARE_bool(block_ignore_not_exist_fatal);
DECLARE_bool(log_block_transfer_detail);
DECLARE_bool(save_storage_from_uc_complete_in_sdkv2);
DECLARE_bool(block_manager_enable_transfer_blocks);
DECLARE_int32(blk_process_all_pending_batch_size);
DECLARE_int32(block_diff_batch_size);
DECLARE_int32(blk_delete_file_by_dn_evict_thread_count);
DECLARE_bool(enable_ufs_evict_write_cache);
DECLARE_bool(enable_ufs_evict_write_cache_delete_file);
DECLARE_uint32(ufs_evict_write_cache_max_num_pending_tasks);
DECLARE_bool(log_evict_delete_file);
DECLARE_bool(log_dn_all_ibr);
DECLARE_bool(clear_corrupt_block_for_uc_block);
DECLARE_bool(run_ut);
DECLARE_bool(ha_async_run_switch_task);

// HAState can change ant any time
#define RETURN_IF_NO_ACTIVE()             \
  if (!IsPopulatingReplicationQueues()) { \
    return;                               \
  }

#define RETURN_IF_NO_ACTIVE_VALUE(v)      \
  if (!IsPopulatingReplicationQueues()) { \
    return v;                             \
  }

#define BREAK_IF_HA_IN_TRANSITION() \
  if (ha_state_->InTransition()) {  \
    break;                          \
  }

namespace dancenn {
namespace {

class BlockMapSliceScanTask : public cnetpp::concurrency::Task {
 public:
  BlockMapSliceScanTask(BlockManagerMetrics* metrics,
                        std::vector<std::unique_ptr<BlockMapSlice>>&
                            slices  // NOLINT(runtime/references)
                        )
      : metrics_(metrics), slices_(slices) {
    CHECK_NOTNULL(metrics_);
  }

  bool operator()(void* arg = nullptr) override {
    std::unique_lock<std::mutex> guard(mu_);
    while (!IsStopped()) {
      for (std::unique_ptr<BlockMapSlice>& slice : slices_) {
        {
          StopWatch sw;
          sw.Start();
          std::unique_lock<BlockMapSlice> _(*slice);
          slice->TellLockHolder(__FILE__, __LINE__);
          MFH(metrics_->scan_slice_lock_acquire_time_)
              ->Update(sw.NextStepTime());
          slice->ReleasePersistedAndCacheFreeBlocks();
          VLOG(8) << "Scan block map slice end";
          MFH(metrics_->scan_slice_release_blocks_time_)
              ->Update(std::chrono::duration_cast<std::chrono::microseconds>(
                           sw.GetTime())
                           .count());
        }
        cv_.wait_for(
            guard,
            std::chrono::milliseconds(FLAGS_scan_one_block_map_slice_interval_ms),
            [this]() -> bool { return IsStopped(); });
        if (IsStopped()) {
          return true;
        }
      }
    }
    return true;
  }

  void Stop() override {
    std::unique_lock<std::mutex> guard(mu_);
    cnetpp::concurrency::Task::Stop();
    cv_.notify_all();
  }

 private:
  std::mutex mu_;
  std::condition_variable cv_;
  BlockManagerMetrics* metrics_;
  std::vector<std::unique_ptr<BlockMapSlice>>& slices_;
};

}  // namespace

using cloudfs::RecoveringBlockProto;
using cloudfs::ReplicaStateProto;
using cloudfs::StorageClassProto;
using cloudfs::datanode::BlockCommandProto;
using cloudfs::datanode::BlockReportBlockInfoProtoV2;
using cloudfs::IOPriority;

std::ostream& operator<<(std::ostream& os, const ReplicaNum& rn) {
  return os << "ReplicaNum("
            << "live=" << static_cast<int>(rn.live)
            << " corrupt=" << static_cast<int>(rn.corrupt)
            << " excess=" << static_cast<int>(rn.excess)
            << " stale=" << static_cast<int>(rn.stale)
            << " decommission=" << static_cast<int>(rn.decommission) << ")";
}

std::string BlockMapStat::ToString() const {
  std::stringstream ss;
  ss << "NumUnderReplicated: " << num_under_replicated
     << ", NumPendingReplication: " << num_pending_replication
     << ", NumCorruptBlock: " << num_corrupt_block
     << ", NumSealedBlock: " << num_sealed_block
     << ", NumMissing: " << num_missing
     << ", NumMissingOneBlock: " << num_missing_one_block
     << ", NumBlock: " << num_block << ", NumUC: " << num_uc
     << ", NumPostponedMisreplicated: " << num_postponed_misreplicated
     << ", NumInvalidate: " << num_invalidate
     << ", NumTruncatable: " << num_truncatable
     << ", NumReplicationQueue: " << num_replication_queue
     << ", NumRecover: " << num_recover
     << ", NumExcessReplicas: " << num_excess_replicas
     << ", NumFutureBlocks: " << num_future_blocks
     << ", NumMisinvalidatedBlocks: " << num_misinvalidated_blocks;
  return ss.str();
}

BlockManager::BlockManager(std::shared_ptr<EditLogContextBase> edit_log_ctx)
    : num_slices_(static_cast<size_t>(FLAGS_blockmap_num_slice)),
      metrics_(this),
      edit_log_ctx_(edit_log_ctx),
      upload_cmd_mgr_(&FLAGS_upload_cmds_max_size),
      ne_cmd_mgr_(&FLAGS_ne_cmds_max_size),
      load_cmd_mgr_(&FLAGS_load_cmds_max_size),
      merge_cmd_mgr_(&FLAGS_merge_cmds_max_size),
      blockid_cmd_mgr_(&FLAGS_pin_cmds_max_size),
      bip_write_manager_(nullptr) {
  block_count_.store(0);
  zero_replica_block_count_.store(0);
  invalidate_blk_count_.store(0);
  truncatable_blk_count_.store(0);
  sealed_blk_count_.store(0);
  slice_mask_ = num_slices_ - 1;
  CHECK_EQ(slice_mask_ & num_slices_, 0)
      << "FLAGS_blockmap_num_slice must be power of 2";
  for (size_t i = 0; i < num_slices_; i++) {
    slices_.emplace_back(new BlockMapSlice(i, &metrics_));
  }

  for (int i = 0; i < FLAGS_blk_report_thread_count; i++) {
    auto t = std::make_shared<cnetpp::concurrency::ThreadPool>(
        "blk-rpt-" + std::to_string(i));
    t->set_num_threads(1);
    t->Start();
    blk_report_thread_.emplace_back(t);
    t = std::make_shared<cnetpp::concurrency::ThreadPool>("dirty-bip-blk-rpt-" +
                                                          std::to_string(i));
    t->set_num_threads(1);
    t->Start();
    dirty_bip_report_thread_.emplace_back(t);
  }

  block_recovery_thread_.reset(
      new cnetpp::concurrency::ThreadPool("block-recovery-thread-"));
  block_recovery_thread_->set_num_threads(FLAGS_block_recovery_thread_count);
  block_recovery_thread_->Start();

  blk_replication_workers_ =
      std::make_shared<cnetpp::concurrency::ThreadPool>("blk-replication");
  blk_replication_workers_->set_num_threads(FLAGS_blk_replication_thread_count);
  blk_replication_workers_->Start();

  bg_uploader_worker_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("blk-uploader");
  bg_uploader_worker_->set_num_threads(FLAGS_blk_uploader_thread_count);
  bg_uploader_worker_->set_user_context((void*)new CommitterChannelContext(
      "blk-uploader", FLAGS_blk_uploader_thread_count));
  bg_uploader_worker_->Start();

  for (int i = 0; i < FLAGS_blk_delete_file_by_dn_evict_thread_count; ++i) {
    auto worker = std::make_shared<cnetpp::concurrency::ThreadPool>(
        "blk-dn-evict" + std::to_string(i));

    worker->set_num_threads(1);
    worker->set_user_context((void*)new CommitterChannelContext(
        "blk-dn-evict" + std::to_string(i), 1));
    worker->set_max_num_pending_tasks(
        FLAGS_ufs_evict_write_cache_max_num_pending_tasks /
        FLAGS_blk_delete_file_by_dn_evict_thread_count);
    worker->Start();

    bg_evict_deleter_worker_.push_back(worker);
  }

  blocks_scanner_listener_ = std::make_shared<BlocksScannerListener>(this);

  depring_block_recycler_.reset(
      new DepringBlockRecycler(ha_state_, this, edit_log_ctx_, meta_storage_));
  depred_block_recycler_.reset(
      new DepredBlockRecycler(ha_state_, this, edit_log_ctx_, meta_storage_));
  tos_block_deleter_.reset(new TosBlockDeleter(depred_block_recycler_.get()));
}

BlockManager::~BlockManager() {
  for (auto& t : blk_report_thread_) {
    t->Stop();
  }
  for (auto& t : dirty_bip_report_thread_) {
    t->Stop();
  }
  // MUST Stop replication monitor before blk_replication_workers_,
  // otherwise it will cause dead lock on stop, see
  // https://meego.feishu.cn/cfs/issue/detail/8636487
  if (replication_monitor_) {
    replication_monitor_->Stop();
    replication_monitor_.reset();
  }

  block_recovery_thread_->Stop();
  blk_replication_workers_->Stop();
  if (misinvalidated_block_monitor_) {
    misinvalidated_block_monitor_->Stop();
    misinvalidated_block_monitor_.reset();
  }
  if (meta_scanner_) {
    meta_scanner_->DeregisterListener(
        std::static_pointer_cast<MetaScanner::Listener>(
            blocks_scanner_listener_));
  }
  if (bg_uploader_worker_) {
    bg_uploader_worker_->Stop();
    bg_uploader_worker_.reset();
  }
  blocks_scanner_listener_.reset();
}

void BlockManager::StartActive(BIPWriteManagerBase* bip_write_manager) {
  StopWatch sw;
  sw.Start();

  is_active_ = true;
  datanode_manager_->MarkAllStaleAfterFailover();
  LOG(INFO)
      << "[HA] "
      << "BlockManager: datanode_manager_->MarkAllStaleAfterFailover time:"
      << StringUtils::FormatWithCommas(sw.NextStepTime());

  datanode_manager_->StartActive();
  LOG(INFO) << "[HA] "
            << "BlockManager: datanode_manager_->StartActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  if (!replication_monitor_) {
    replication_monitor_ = std::make_unique<ReplicationMonitor>(this);
    replication_monitor_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: replication_monitor_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (!invalidate_block_monitor_) {
    invalidate_block_monitor_ = std::make_unique<InvalidateBlockMonitor>(this);
    invalidate_block_monitor_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: invalidate_block_monitor_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (!truncatable_block_monitor_) {
    truncatable_block_monitor_ =
        std::make_unique<TruncatableBlockMonitor>(this);
    truncatable_block_monitor_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: truncatable_block_monitor_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (!sealed_block_monitor_) {
    sealed_block_monitor_ = std::make_unique<SealedBlockMonitor>(this);
    sealed_block_monitor_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: sealed_block_monitor_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (!slice_scan_worker_) {
    slice_scan_task_.reset(new BlockMapSliceScanTask(&metrics_, slices_));
    slice_scan_worker_.reset(
        new cnetpp::concurrency::Thread(slice_scan_task_, "SliceScanner"));
    slice_scan_worker_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: slice_scan_worker_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  bip_write_manager_ = bip_write_manager;
  depring_block_recycler_->StartActive(bip_write_manager_);
  LOG(INFO) << "[HA] "
            << "BlockManager: depring_block_recycler_->StartActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  depred_block_recycler_->StartActive(bip_write_manager_);
  LOG(INFO) << "[HA] "
            << "BlockManager: depred_block_recycler_->StartActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  tos_block_deleter_->StartActive();
  LOG(INFO) << "[HA] "
            << "BlockManager: tos_block_deleter_->StartActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  if (!block_pufs_info_monitor_) {
    block_pufs_info_monitor_ = std::make_unique<BlockPufsInfoMonitor>(this);
    block_pufs_info_monitor_->Start();
    LOG(INFO) << "[HA] "
              << "BlockManager: block_pufs_info_monitor_->Start time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  if (edit_log_sender_ == nullptr) {
    edit_log_sender_.reset(new EditLogSender(edit_log_ctx_));
    LOG(INFO) << "[HA] "
              << "BlockManager: new edit_log_sender time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  if (FLAGS_ha_async_run_switch_task && ns_ && ns_->GetBgThreadPool()) {
    ha_switch_latch_.Await();
    ha_switch_latch_.Reset(1);

    ns_->GetBgThreadPool()->AddTask([&]() -> bool {
      StopWatch sw;
      sw.Start();

      LOG(INFO) << "[HA] "
                << "BlockManager: slices_ size=" << slices_.size()
                << " ProcessAllPendingReportedBlock start";

      ProcessAllPendingReportedBlock();

      ha_switch_latch_.CountDown();
      LOG(INFO) << "[HA] "
                << "BlockManager: ProcessAllPendingReportedBlock time:"
                << StringUtils::FormatWithCommas(sw.NextStepTime());

      return true;
    });
  } else {
    ProcessAllPendingReportedBlock();

    LOG(INFO) << "[HA] "
              << "BlockManager: ProcessAllPendingReportedBlock time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  // TODO(ruanjunbin): pending persisted blks?
}

void BlockManager::StopActive() {
  StopWatch sw;
  sw.Start();

  is_active_ = false;
  datanode_manager_->StopActive();
  LOG(INFO) << "[HA] "
            << "BlockManager: datanode_manager_->StopActive() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  if (replication_monitor_) {
    replication_monitor_->Stop();
    replication_monitor_.reset();
    LOG(INFO) << "[HA] " << "BlockManager: stop replication monitor time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (invalidate_block_monitor_) {
    invalidate_block_monitor_->Stop();
    invalidate_block_monitor_.reset();
    LOG(INFO) << "[HA] " << "BlockManager: stop invalidate block monitor time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (block_pufs_info_monitor_) {
    block_pufs_info_monitor_->Stop();
    block_pufs_info_monitor_.reset();
    LOG(INFO) << "[HA] " << "BlockManager: stop block pufs info monitor time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (truncatable_block_monitor_) {
    truncatable_block_monitor_->Stop();
    truncatable_block_monitor_.reset();
    LOG(INFO) << "[HA] " << "BlockManager: stop truncatable block monitor time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (sealed_block_monitor_) {
    sealed_block_monitor_->Stop();
    sealed_block_monitor_.reset();
    LOG(INFO) << "[HA] " << "BlockManager: stop sealed block monitor time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (slice_scan_task_) {
    slice_scan_task_->Stop();
    LOG(INFO) << "[HA] "
              << "BlockManager: slice_scan_task_->Stop() time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  if (slice_scan_worker_) {
    slice_scan_worker_->Stop();
    LOG(INFO) << "[HA] "
              << "BlockManager: slice_scan_worker_->Stop() time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  // std::this_thread::sleep_for(std::chrono::milliseconds(100));

  if (FLAGS_ha_async_run_switch_task && ns_ && ns_->GetBgThreadPool()) {
    ha_switch_latch_.Await();
    ha_switch_latch_.Reset(2);

    ns_->GetBgThreadPool()->AddTask([&]() -> bool {
      StopWatch sw;
      sw.Start();

      decltype(needed_replications_) other;
      needed_replications_.Swap(other);
      LOG(INFO) << "[HA] "
                << "BlockManager: swap needed replications time:"
                << StringUtils::FormatWithCommas(sw.NextStepTime());

      ha_switch_latch_.CountDown();
      // not block HA switch, not need to latch

      other.Clear();
      LOG(INFO) << "[HA] "
                << "BlockManager: clear other time:"
                << StringUtils::FormatWithCommas(sw.NextStepTime());
      return true;
    });
  } else {
    needed_replications_.Clear();
    LOG(INFO) << "[HA] " << "BlockManager: clear needed replications time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  {
    std::unique_lock<RWSpinlock> guard(rwlock_);
    to_recover_.clear();
    dn_replication_queues_.clear();
    LOG(INFO)
        << "[HA] "
        << "BlockManager: clear to_recover_ and dn_replication_queues_ time:"
        << StringUtils::FormatWithCommas(sw.NextStepTime());
    // Do not clear corrupt_blocks_.
  }

  {
    invalidate_blk_count_.store(0);
    datanode_manager_->ClearInvalidateBlock();
    LOG(INFO) << "[HA] " << "BlockManager: clear invalidate block time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  {
    truncatable_blk_count_.store(0);
    datanode_manager_->ClearTruncatableBlock();
    LOG(INFO) << "[HA] " << "BlockManager: clear truncatable block time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  {
    sealed_blk_count_.store(0);
    LOG(INFO) << "[HA] "
              << "BlockManager: clear sealed block time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  if (FLAGS_ha_async_run_switch_task && ns_ && ns_->GetBgThreadPool()) {
    ns_->GetBgThreadPool()->AddTask([&]() -> bool {
      StopWatch sw;
      sw.Start();

      LOG(INFO) << "[HA] "
                << "BlockManager: clear slices_ size=" << slices_.size()
                << " start";

      for (auto& s : slices_) {
        std::unique_lock<BlockMapSlice> guard(*s);
        s->TellLockHolder(__FILE__, __LINE__);
        s->ClearInvalidateBlock();
        s->ClearExcessBlock();
        s->ClearPendingReplications();
      }

      LOG(INFO) << "[HA] "
                << "BlockManager: clear slices_ size=" << slices_.size()
                << " time:" << StringUtils::FormatWithCommas(sw.NextStepTime());

      ha_switch_latch_.CountDown();
      return true;
    });
  } else {
    for (auto& s : slices_) {
      std::unique_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      s->ClearInvalidateBlock();
      s->ClearExcessBlock();
      s->ClearPendingReplications();
    }
    LOG(INFO) << "[HA] "
              << "BlockManager: clear slices_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  depring_block_recycler_->StopActive();
  LOG(INFO) << "[HA] "
            << "BlockManager: depring_block_recycler_->StopActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  depred_block_recycler_->StopActive();
  LOG(INFO) << "[HA] "
            << "BlockManager: depred_block_recycler_->StopActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  tos_block_deleter_->StopActive();
  bip_write_manager_ = nullptr;
  LOG(INFO) << "[HA] "
            << "BlockManager: tos_block_deleter_->StopActive time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  edit_log_sender_->Sync(true);
  edit_log_sender_.reset();
  LOG(INFO) << "[HA] "
            << "BlockManager: edit_log_sender_.reset() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void BlockManager::StartStandby() {
  StopWatch sw;
  sw.Start();

  if (!misinvalidated_block_monitor_) {
    misinvalidated_block_monitor_ =
        std::make_unique<MisinvalidatedBlockMonitor>(this);
    misinvalidated_block_monitor_->Start();

    LOG(INFO) << "[HA] "
              << "BlockManager::StartStandby: start "
                 "misinvalidated_block_monitor_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
}

void BlockManager::StopStandby() {
  StopWatch sw;
  sw.Start();

  if (misinvalidated_block_monitor_) {
    misinvalidated_block_monitor_->Stop();
    misinvalidated_block_monitor_.reset();
    LOG(INFO) << "[HA] "
              << "BlockManager::StartStandby: start "
                 "misinvalidated_block_monitor_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
}

void BlockManager::AwaitHaSwitch() {
  StopWatch sw;
  sw.Start();

  LOG(INFO) << "[HA] "
            << "BlockManager::AwaitHaSwitch: start "
               " time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  ha_switch_latch_.Await();

  LOG(INFO) << "[HA] "
            << "BlockManager::AwaitHaSwitch: finish "
               " time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void BlockManager::set_datanode_manager(
    std::shared_ptr<DatanodeManager> datanode_manager) {
  CHECK_NOTNULL(datanode_manager);
  datanode_manager_ = datanode_manager.get();
  for (auto& s : slices_) {
    // This function is called at startup, no need to take lock.
    s->SetDatanodeManager(datanode_manager);
  }
}

void BlockManager::set_job_manager(std::shared_ptr<JobManager> job_manager) {
  CHECK_NOTNULL(job_manager);
  job_manager_ = job_manager;
}

void BlockManager::SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage) {
  meta_storage_ = meta_storage;
  for (auto& s : slices_) {
    // This function is called at startup, no need to take lock.
    s->SetMetaStorage(meta_storage_);
  }
  depring_block_recycler_->SetMetaStorage(meta_storage_);
  depred_block_recycler_->SetMetaStorage(meta_storage_);
}

void BlockManager::SetMetaScanner(std::shared_ptr<MetaScanner> meta_scanner) {
  meta_scanner_ = std::move(meta_scanner);
  meta_scanner_->RegisterListener(
      std::static_pointer_cast<MetaScanner::Listener>(
          blocks_scanner_listener_));
}

void BlockManager::TestOnlySetEditLogCtx(
    std::shared_ptr<EditLogContextBase> edit_log_ctx) {
  edit_log_ctx_ = edit_log_ctx;
}

void BlockManager::TestOnlySetEditLogSender(
    std::shared_ptr<EditLogSenderBase> sender) {
  edit_log_sender_ = sender;
  depring_block_recycler_->TestOnlySetEditLogSender(sender);
  depred_block_recycler_->TestOnlySetEditLogSender(sender);
}

void BlockManager::TestOnlySetBIPWriteManager(
    BIPWriteManagerBase* bip_write_manager) {
  bip_write_manager_ = bip_write_manager;
}

void BlockManager::TestOnlySetIsActive(bool is_active) {
  is_active_ = is_active;
}

BlockPufsInfoMonitor* BlockManager::TestOnlyGetBlockPufsInfoMonitor() {
  return block_pufs_info_monitor_.get();
}

void BlockManager::set_ns(NameSpace* ns) {
  ns_ = ns;
  CHECK_NOTNULL(ns_);
  depred_block_recycler_->SetBlockPoolId(ns->blockpool_id());
  tos_block_deleter_->SetNameSpace(ns_);
}

void BlockManager::set_ha_state(HAStateBase* ha_state) {
  ha_state_ = ha_state;
  CHECK_NOTNULL(ha_state_);
  depring_block_recycler_->SetHAState(ha_state);
  depred_block_recycler_->SetHAState(ha_state);
}

void BlockManager::set_safemode(SafeModeBase* safemode) {
  safemode_ = safemode;
  CHECK_NOTNULL(safemode_);
}

void BlockManager::set_block_report_manager(BlockReportManager* block_report_manager) {
  block_report_manager_ = block_report_manager;
  CHECK_NOTNULL(block_report_manager_);
}

DatanodeID BlockManager::GetBlockDatanodeID(BlockID blk_id, size_t index) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return kInvalidDatanodeID;
  }
  return bi->storage_id(index);
}

//// OK
uint64_t BlockManager::GetINodeId(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return kInvalidINodeId;
  }
  return bi->inode_id();
}

Block BlockManager::AddBlock(const Block& blk,
                             uint64_t inode_id,
                             uint64_t parent_id,
                             uint8_t capacity,
                             cloudfs::IoMode write_mode,
                             const std::vector<DatanodeID>& dns,
                             BlockUCState state) {
  StopWatch sw(metrics_.add_block_lock_acquire_time_);
  sw.Start();

  auto& s = slice(blk.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  sw.NextStep();
  MFH(metrics_.add_block_lock_acquire_time_)
      ->Update(
          std::chrono::duration_cast<std::chrono::microseconds>(sw.GetTime())
              .count());

  s->AddBlock(inode_id, parent_id, blk, capacity, write_mode, dns, state);
  if (capacity > 0) {
    ++block_count_;
  } else {
    ++zero_replica_block_count_;
  }
  VLOG(8) << "add block " << blk << " dns.size()=" << dns.size();

  DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(blk.id,
                                    "\"blk_id\":%" PRIu64
                                    ","
                                    "\"op\":\"add_block\","
                                    "\"gs\":%" PRIu64
                                    ","
                                    "\"numbytes\":%d,"
                                    "\"dns\":\"%s\","
                                    "\"status\":\"%s\"}",
                                    blk.id,
                                    blk.gs,
                                    blk.num_bytes,
                                    GetDNIPStr(dns).c_str(),
                                    GetStateStr(state).c_str());

  return blk;
}

void BlockManager::AddBlock(BlockID blk_id,
                            uint64_t inode_id,
                            uint64_t parent_id,
                            uint32_t len,
                            uint64_t gs,
                            uint8_t capacity,
                            cloudfs::IoMode write_mode,
                            BlockUCState state) {
  Block blk(blk_id, len, gs);
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  s->AddBlock(inode_id, parent_id, blk, capacity, write_mode, {}, state);
  VLOG(10) << "add block " << blk << " with no dns";

  if (capacity > 0) {
    ++block_count_;
  } else {
    ++zero_replica_block_count_;
  }
}

void BlockManager::SetBlockNum(uint8_t capacity) {
  if (capacity > 0) {
    ++block_count_;
  } else {
    ++zero_replica_block_count_;
  }
}

void BlockManager::LoadBlock(uint64_t inode_id,
                             uint64_t parent_id,
                             uint8_t capacity,
                             cloudfs::IoMode write_mode,
                             const Block& blk,
                             const std::vector<DatanodeID>& dns,
                             BlockUCState state) {
  if (capacity > FLAGS_dfs_replication_max) {
    // Old block may not be checked if the namenode has restarted.
    LOG(WARNING) << blk << " replica number is too large, reset to max: "
                 << FLAGS_dfs_replication_max;
    capacity = FLAGS_dfs_replication_max;
  }

  auto& s = slice(blk.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  s->LoadBlock(inode_id, parent_id, blk, capacity, write_mode, dns, state);
  if (capacity > 0) {
    ++block_count_;
  } else {
    ++zero_replica_block_count_;
  }

  // TODO(ruanjunbin): Call ProcessOverReplicatedBlock will cause program
  // coredump because BlockManager::ns_ hasn't been initialized.
  // So how to invalidate over replicated block after failover?
}

void BlockManager::LoadLocalBlocks() {
  CHECK_NOTNULL(meta_storage_);
  meta_storage_->ScanLocalBlocks([this](const BlockInfoProto& bip) {
    block_count_++;
    Block blk{/*id*/ bip.block_id(),
              /*num_bytes*/ static_cast<uint32_t>(bip.num_bytes()),
              /*gs*/ bip.gen_stamp()};
    auto& s = slice(blk.id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    s->LoadBlock(bip.inode_id(),
                 kInvalidINodeId,
                 blk,
                 BlockExpectedReplica(bip, /*inode=*/nullptr),
                 bip.write_mode(),
                 /*dn_id*/std::vector<DatanodeID>(),
                 bip.state() == BlockInfoProto::kUnderConstruction
                     ? BlockUCState::kUnderConstruction
                     : BlockUCState::kComplete);
  });
}

bool BlockManager::RemoveBlock(BlockID blk_id, bool check_in_transaction) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return false;
  }
  if (!bi->TryLockInTransaction(__FILE__, __LINE__)) {
    if (check_in_transaction) {
      return false;
    }
  }
  bi->TryUnlockInTransaction(__FILE__, __LINE__);
  RemoveBlock(blk_id);
  return true;
}

void BlockManager::RemoveBlocksAndUpdateSafeMode(
    const std::vector<BlockInfoProto>& bips) {
  std::vector<BlockProto> bps;
  for (const BlockInfoProto& bip : bips) {
    BlockProto bp;
    bp.set_blockid(bip.block_id());
    bp.set_genstamp(bip.gen_stamp());
    bp.set_numbytes(bip.num_bytes());
    bps.emplace_back(bp);
  }
  RemoveBlocksAndUpdateSafeMode(bps);
}

void BlockManager::RemoveBlocksAndUpdateSafeMode(
    const std::vector<BlockProto>& blocks) {
  int32_t num_removed_complete = 0;
  int32_t num_removed_safe = 0;
  for (const auto& block : blocks) {
    auto& s = slice(block.blockid());
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block.blockid(), true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      continue;
    }
    if (bi->IsComplete()) {
      ++num_removed_complete;
      if (bi->size() >= FLAGS_dfs_replication_min) {
        ++num_removed_safe;
      }
    }
    AddToInvalidate(block, "RemoveBlocksAndUpdateSafeMode");
    needed_replications_.Remove(Block(block),
                                UnderReplicatedBlocks::Priority::kLast);
    s->RemovePendingReplications(block.blockid());
    INode inode;
    if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
        !ns_->GetINode(bi->inode_id(), &inode)) {
      inode.set_id(kInvalidINodeId);
    }
    if (BlockExpectedReplica(*bi, &inode) > 0) {
      if (!bi->IsPersisted()) {
        --block_count_;
      }
    } else {
      --zero_replica_block_count_;
    }
    // bi is nullptr after RemoveBlock
    RemoveBlock(block.blockid());

    DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(block.blockid(),
                                      "\"blk_id\":%" PRIu64
                                      ","
                                      "\"op\":\"remove\","
                                      "\"gs\":%" PRIu64
                                      ","
                                      "\"numbytes\":%d}",
                                      block.blockid(),
                                      block.genstamp(),
                                      block.numbytes());
  }
  safemode_->AdjustSafeModeBlockTotals(-num_removed_safe,
                                       -num_removed_complete);
}

BlockUCState BlockManager::GetBlockUCState(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << blk_id;
    return BlockUCState::kComplete;
  }
  CHECK_NOTNULL(bi);
  return bi->uc_state();
}

cloudfs::IoMode BlockManager::GetBlockIoMode(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (bi == nullptr) {
    return cloudfs::IoMode::DATANODE_BLOCK;
  }
  return bi->write_mode();
}

// @return
//   IsFalse(): No block recovery required.
//   IsOK(): Block recovery should be performed.
//   HasException(): An uncaught exception occurred; raise the error.
//
// @return        @remove_last_block  @last_blk
// IsFalse()      true/false          InvalidBlk/last_bp
// IsOK()         false               InvalidBlk
// HasException() undefined           undefined
//
// The file contains these blocks is read locked by namespace
Status BlockManager::NeedRelease(const std::vector<BlockProto>& blocks,
                                 bool* remove_last_block,
                                 Block* last_blk) {
  if (blocks.size() == 0) {
    // empty file
    *remove_last_block = false;
    *last_blk = Block{kInvalidBlockID, 0, 0};
    return Status(Code::kFalse, "");
  }

  // just double check block status in case of bug
  if (blocks.size() > 2) {
    for (size_t i = 0; i < blocks.size() - 2; ++i) {
      auto blk_id = blocks[i].blockid();
      auto& s = slice(blk_id);
      std::shared_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      BlockInfoGuard bi_guard(s.get(), blk_id, true);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
        LOG(ERROR) << "block not exist, block_id=" << blk_id;
        return Status(JavaExceptions::kIOException, "The blocks not exist");
      }
      CHECK_NOTNULL(bi);
      if (!bi->ReadyToComplete()) {
        return Status(JavaExceptions::kIOException,
                      "The blocks has unexpected state");
      }
    }
  }

  std::function<void()> release_lock = []() {};
  DEFER([&]() { release_lock(); });

  BlockInfo* last_bi = nullptr;
  BlockInfo* penultimate_bi = nullptr;
  BlockID last_blk_id = kInvalidBlockID;
  BlockID penultimate_blk_id = kInvalidBlockID;
  BlockProto last_bp;
  BlockProto penultimate_bp;
  bool last_blk_min_repl = false;
  bool penultimate_blk_min_repl = false;

  if (blocks.size() >= 2) {
    last_blk_id = blocks[blocks.size() - 1].blockid();
    penultimate_blk_id = blocks[blocks.size() - 2].blockid();
    last_bp = blocks[blocks.size() - 1];
    penultimate_bp = blocks[blocks.size() - 2];
    auto last_s = slice(last_blk_id).get();
    auto penultimate_s = slice(penultimate_blk_id).get();
    // The penultimate block and last block are in the same slice
    if (last_s == penultimate_s) {
      last_s->lock();
      release_lock = [last_s]() { last_s->unlock(); };
    } else {
      // avoid dead lock
      if (last_blk_id < penultimate_blk_id) {
        last_s->lock();
        penultimate_s->lock_shared();
        penultimate_s->TellLockHolder(__FILE__, __LINE__);
        release_lock = [last_s, penultimate_s]() {
          penultimate_s->unlock_shared();
          last_s->unlock();
        };
      } else {
        penultimate_s->lock_shared();
        penultimate_s->TellLockHolder(__FILE__, __LINE__);
        last_s->lock();
        release_lock = [last_s, penultimate_s]() {
          last_s->unlock();
          penultimate_s->unlock_shared();
        };
      }
    }
    BlockInfoGuard last_bi_guard(last_s, last_blk_id, true);
    last_bi = last_bi_guard.GetBlockInfo();
    if (FLAGS_block_ignore_not_exist_fatal && last_bi == nullptr) {
      auto msg = absl::StrFormat("block not exist, last_bi=%lu", last_blk_id);
      LOG(ERROR) << msg;
      return Status(JavaExceptions::kIOException, msg);
    }
    CHECK_NOTNULL(last_bi);
    BlockInfoGuard penultimate_bi_guard(penultimate_s, penultimate_blk_id, true);
    penultimate_bi = penultimate_bi_guard.GetBlockInfo();
    if (FLAGS_block_ignore_not_exist_fatal && penultimate_bi == nullptr) {
      auto msg = absl::StrFormat("block not exist, penultimate_blk_id=%lu",
                                 penultimate_blk_id);
      LOG(ERROR) << msg;
      return Status(JavaExceptions::kIOException, msg);
    }
    CHECK_NOTNULL(penultimate_bi);
  }

  if (blocks.size() == 1) {
    last_blk_id = blocks[blocks.size() - 1].blockid();
    last_bp = blocks[blocks.size() - 1];
    auto last_s = slice(last_blk_id).get();
    last_s->lock();
    release_lock = [last_s]() { last_s->unlock(); };
    BlockInfoGuard last_bi_guard(last_s, last_blk_id, true);
    last_bi = last_bi_guard.GetBlockInfo();
    if (FLAGS_block_ignore_not_exist_fatal && last_bi == nullptr) {
      auto msg = absl::StrFormat("block not exist, last_bi=%lu", last_blk_id);
      LOG(ERROR) << msg;
      return Status(JavaExceptions::kIOException, msg);
    }
    CHECK_NOTNULL(last_bi);
  }

  // All locks are held properly and also
  // we've obtained the pointers of last block and penultimate block.

  // The penultimate block if exists must be either COMPLETE, COMMITTED
  // or PERSISTED.
  if (penultimate_bi && !penultimate_bi->HasBeenCommitted()) {
    return Status(
        JavaExceptions::kIOException,
        absl::StrFormat(
            "The penultimate block {id:%d,gs:%d,num_bytes:%d} is not committed",
            penultimate_bi->blk().id,
            penultimate_bi->blk().gs,
            penultimate_bi->blk().num_bytes));
  }

  if (!penultimate_bi || penultimate_bi->HasBeenComplete() ||
      CountReplica(penultimate_bi).live >= FLAGS_dfs_replication_min) {
    penultimate_blk_min_repl = true;
  }

  if (last_bi->HasBeenComplete() ||
      (last_bi->IsCommitted() &&
       CountReplica(last_bi).live >= FLAGS_dfs_replication_min)) {
    last_blk_min_repl = true;
  }

  // The penultimate block(if exists) and the last block
  // both reached min replication, just close file
  if (penultimate_blk_min_repl && last_blk_min_repl) {
    // Update last block to right state, then call finalize_cb/FinalizeFile
    // to update inode in meta storage.
    // Attention!! get numbytes of last blk from BlocksMap.
    *remove_last_block = false;
    *last_blk = last_bi->blk();
    return Status(Code::kFalse, "");
  }

  switch (last_bi->uc_state()) {
    case BlockUCState::kPersisted:
    case BlockUCState::kComplete:
      LOG(FATAL) << "Already checked that the last block is incomplete B"
                 << last_bi->id();
      return Status(JavaExceptions::kIOException);
    case BlockUCState::kCommitted:
      // We checked that !penultimate_blk_min_repl || !last_blk_min_repl
      return Status(JavaExceptions::kAlreadyBeingCreatedException,
                    "Failed to release lease. Committed blocks are waiting to "
                    "be minimally replicated. Try again later.");
    case BlockUCState::kUnderConstruction:
    case BlockUCState::kUnderRecovery:
      auto last_s = slice(last_bi->id()).get();
      auto uc = last_s->GetUcInternal(last_bi->id());
      // There is no datanode reported to this block.
      // maybe client have crashed before writing data to pipeline.
      // This blocks doesn't need any recovery.
      // We can remove this block and close the file.
      if (last_bi->write_mode() == cloudfs::IoMode::DATANODE_BLOCK &&
          last_bi->num_bytes() == 0 &&
          (!uc || uc->NumReportedStorages() == 0)) {
        // TODO(ruanjunbin): Delete this code in next release version.
        // HandleDeprecatingBlocks will remove block instead.
        // RemoveBlock(last_bi->id(), last_s);
        *remove_last_block = true;
        *last_blk = Block(kInvalidBlockID, 0, 0);
        release_lock();
        release_lock = []() {};
        *remove_last_block = true;
        *last_blk = Block(kInvalidBlockID, 0, 0);

        LOG(WARNING) << "ReleaseLease, Block: " << last_bi->id()
                     << " removed empty last block and closed file";
        return Status(Code::kFalse, "");
      }

      *remove_last_block = false;
      *last_blk = Block(kInvalidBlockID, 0, 0);
      return Status(Code::kOK, "");
  }
  LOG(FATAL) << "release B" << last_bi->id() << " find unknown state "
             << static_cast<int>(last_bi->uc_state());
  return Status(JavaExceptions::kIOException);
}

void BlockManager::InitRecover(const BlockProto& block,
                               uint64_t recovery_id,
                               bool close_file) {
  auto& s = slice(block.blockid());
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), block.blockid(), true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  bi->set_uc_state(BlockUCState::kUnderRecovery);
  auto uc = s->GetUcInternal(block.blockid());
  if (!uc) {
    LOG(INFO) << "Init recover for B" << block.blockid() << " did not find uc";
    return;
  }

  if (bi->write_mode() == cloudfs::IoMode::DATANODE_BLOCK) {
    auto primary =
        datanode_manager_->ChooseBlockRecoverPrimary(&(uc->tried_as_primary()));
    if (primary != kInvalidDatanodeID) {
      uc->set_primary_storage_id(primary);
      uc->set_recovery_id(recovery_id);
      AddToRecover(
          block.blockid(),
          primary,
          // Block protocol v2 always close file.
          recovery_id == kBlockProtocolV2GenerationStamp ? true : close_file);
      LOG(INFO) << "Add recover job B" << block.blockid() << " DN" << primary
                << " with recovery id " << recovery_id;
    } else {
      LOG(ERROR) << "Failed to choose primary for B" << block.blockid()
                 << ", replica num: " << uc->NumReportedStorages();
    }
  } else {
    // NOTICE: Do not update gs. Use old gs instead.
    uc->set_recovery_id(bi->blk().gs);
    block_recovery_thread_->AddTask([this, blk = bi->blk(), close_file]() {
      if (!ha_state_->IsActive() || ha_state_->InTransition()) {
        return true;
      }
      BlockInfoProto bip;
      if (!meta_storage_->GetBlockInfo(blk.id, &bip)) {
        LOG(WARNING) << "BlockInfoProto not found when block recovery";
        return true;
      }
      int64_t new_length = 0;

      if (ns_->ufs_env() == nullptr) {
        LOG_WITH_LEVEL(ERROR) << "no tos mode";
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return true;
      }
      Status status = CHECK_NOTNULL(ns_->ufs_env())
                          ->GetBlockLengthFromUfs(bip.pufs_name(), &new_length);
      if (!status.IsOK()) {
        LOG_WITH_LEVEL(WARNING)
            << absl::StrFormat("Get length of B%d from tos failed, code: %d",
                               bip.block_id(),
                               status.code());
        MFC(LoggerMetrics::Instance().warn_)->Inc();
        return true;
      }
      cloudfs::datanode::CommitBlockSynchronizationRequestProto commit_block;
      commit_block.mutable_block()->set_poolid(ns_->blockpool_id());
      commit_block.mutable_block()->set_blockid(blk.id);
      commit_block.mutable_block()->set_generationstamp(blk.gs);
      commit_block.mutable_block()->set_numbytes(blk.num_bytes);
      // NOTICE: Do not update gs. Use old gs instead.
      commit_block.set_newgenstamp(blk.gs);
      commit_block.set_newlength(new_length);
      commit_block.set_closefile(true);
      commit_block.set_deleteblock(new_length == 0);
      commit_block.set_closefile(close_file);
      // TODO: Test it carefully.
      ns_->CommitBlockSynchronization(commit_block);
      return true;
    });
  }
}

bool BlockManager::CommitOrCompleteOrPersistLastBlock(
    const Block& blk_from_client,
    bool force) {
  if (blk_from_client.id == kInvalidBlockID ||
      blk_from_client.id ==
          0 /* TODO(yangjinfeng02) is 0 a valid block id? */) {
    // LOG(INFO) << "commit with none";
    return false;
  }

  StopWatch sw(metrics_.ccp_last_block_lock_acquire_time_);
  sw.Start();

  auto& s = slice(blk_from_client.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  sw.NextStep(metrics_.ccp_last_block_locate_time_);

  RPC_SW_CTX_INIT(rpc_sw_ctx,
                  "[CommitOrCompleteOrPersistLastBlock]",
                  absl::StrFormat("block_id=%d", blk_from_client.id));
  RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

  BlockInfoGuard bi_guard(s.get(), blk_from_client.id, true);
  BlockInfo* cur_bi = bi_guard.GetBlockInfo();
  if (!cur_bi) {
    LOG(INFO) << "commit cannot find " << blk_from_client;
    return false;
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "GetBlockInfo");

  sw.NextStep();

  switch (cur_bi->write_mode()) {
    case cloudfs::DATANODE_BLOCK: {
      auto res = CommitOrCompleteLastBlockUnsafe(
          blk_from_client, s.get(), cur_bi, force, rpc_sw_ctx.get());
      RPC_SW_CTX_LOG(rpc_sw_ctx, "CommitOrCompleteLastBlockUnsafe");
      return res;
    }
    case cloudfs::TOS_BLOCK: {
      CHECK_EQ(cur_bi->gs(), blk_from_client.gs) << blk_from_client.id;
      DLOG(INFO) << "blk_from_client=" << blk_from_client;
      cur_bi->UpdateLength(blk_from_client.num_bytes);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "UpdateLength");
      auto res = CommitOrCompleteLastBlockUnsafe(
              blk_from_client, s.get(), cur_bi, force, rpc_sw_ctx.get()) &&
          PersistBlockUnsafe(s.get(), blk_from_client);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "CommitOrCompleteLastBlockUnsafe");

      return res;
    }
    default:
      CHECK(false) << blk_from_client << cur_bi->write_mode();
      // Compiler doesn't know CHECK(false) will make program coredump.
      return false;
  }
}

Status BlockManager::IsLastBlkReadyToComplete(const Block& blk_from_client) {
  const auto& s = slice(blk_from_client.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_from_client.id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();;
  if (!bi) {
    std::string msg = absl::StrFormat(
        "Try to commit/complete nonexistent last block, "
        "blk_from_client: {id: %d, gs: %d, len: %d}",
        blk_from_client.id,
        blk_from_client.gs,
        blk_from_client.num_bytes);
    LOG(ERROR) << msg;
    return Status(Code::kFalse, std::move(msg));
  }

  if (bi->gs() == kBlockProtocolV2GenerationStamp) {
    // byterpc block protocol
    if (!(blk_from_client.gs == bi->gs())) {
      std::string msg = absl::StrFormat(
          "Try to commit/complete corrupt last block, "
          "blk_from_client: {id: %d, gs: %d, len: %d}, "
          "bi: {id: %d, gs: %d, len: %d}",
          blk_from_client.id,
          blk_from_client.gs,
          blk_from_client.num_bytes,
          bi->id(),
          bi->gs(),
          bi->num_bytes());
      LOG(ERROR) << msg;
      return Status(Code::kFalse, std::move(msg));
    }
    return {};
  } else {
    if (bi->uc_state() == BlockUCState::kUnderRecovery) {
      std::string msg = absl::StrFormat(
          "Try to commit/complete under recovery last block, "
          "bi: {id: %d, gs: %d, len: %d}",
          bi->id(),
          bi->gs(),
          bi->num_bytes());
      LOG(ERROR) << msg;
      // Refer to HDFS-10240.
      // https://github.com/apache/hadoop/blob/7a3bc90b05f257c8ace2f76d74264906f0f7a932/hadoop-hdfs-project/hadoop-hdfs/src/main/java/org/apache/hadoop/hdfs/server/blockmanagement/BlockManager.java#L1097
      return Status(JavaExceptions::kIOException, std::move(msg));
    }
    // hdfs block protocol
    if (!(blk_from_client.gs == bi->gs() &&
          blk_from_client.num_bytes >= bi->num_bytes())) {
      std::string msg = absl::StrFormat(
          "Try to commit/complete corrupt last block, "
          "blk_from_client: {id: %d, gs: %d, len: %d}, "
          "bi: {id: %d, gs: %d, len: %d}",
          blk_from_client.id,
          blk_from_client.gs,
          blk_from_client.num_bytes,
          bi->id(),
          bi->gs(),
          bi->num_bytes());
      LOG(ERROR) << msg;
      return Status(Code::kFalse, std::move(msg));
    }
  }

  if (bi->write_mode() == cloudfs::IoMode::TOS_BLOCK) {
    return Status();
  }

  auto rn = CountReplica(bi);
  LOG(INFO) << (int)rn.corrupt << " " << (int)rn.decommission << " " << (int)rn.excess << " " << (int)rn.live << " " << (int)rn.stale;
  if (rn.live < FLAGS_dfs_replication_min) {
    std::string msg = absl::StrFormat(
        "Try to commit/complete last block without enough replica, "
        "blk_from_client: {id: %d, gs: %d, len: %d}, "
        "replica_num: {live: %d, corrupt: %d}",
        blk_from_client.id,
        blk_from_client.gs,
        blk_from_client.num_bytes,
        rn.live,
        rn.corrupt);
    LOG(WARNING) << msg;
    return Status(Code::kFalse, std::move(msg));
  }
  return Status();
}

bool BlockManager::CommitOrCompleteLastBlockUnsafe(
    const Block& blk_from_client,
    BlockMapSlice* s,
    BlockInfo* cur_bi,
    bool force,
    StopWatchContext* rpc_sw_ctx) {
  RPC_SW_CTX_LOG(rpc_sw_ctx, "CommitOrCompleteLastBlockUnsafe");
  CHECK_NOTNULL(cur_bi);
  if (cur_bi->HasBeenComplete()) {
    VLOG(8) << "commit " << blk_from_client << " already complete";
    return false;
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "HasBeenComplete");

  StopWatch sw(metrics_.ccp_last_block_commit_time_);
  sw.Start();

  auto ret = s->CommitBlock(cur_bi, blk_from_client);
  if (ret) {
    VLOG(8) << "commit " << blk_from_client;
  } else {
    LOG(INFO) << "commit " << blk_from_client << " failed";
  }
  RPC_SW_CTX_LOG(rpc_sw_ctx, "CommitBlock");

  std::vector<DatanodeID> dns;
  bool flag_save_storage_from_uc =
      FLAGS_save_storage_from_uc_complete_in_sdkv2 &&
      cur_bi->blk().gs == kBlockProtocolV2GenerationStamp;
  if (flag_save_storage_from_uc) {
    dns = s->GetBlockStoragesSafe(blk_from_client.id);
    VLOG(10) << "GetBlockStoragesSafe from uc to complete"
             << " block_id=" << blk_from_client.id
             << " dns.size()=" << dns.size();
    RPC_SW_CTX_LOG(rpc_sw_ctx, "Get UC DNS");
  }

  sw.NextStep(metrics_.ccp_last_block_complete_time_);

  auto prev_state = BlockUCState::kComplete;
  if ((CountReplica(cur_bi).live >= FLAGS_dfs_replication_min || force) &&
      s->CompleteBlock(cur_bi, force, &prev_state)) {
    DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(cur_bi->id(),
                                      "\"blk_id\":%" PRIu64
                                      ","
                                      "\"op\":\"complete\","
                                      "\"gs\":%" PRIu64
                                      ","
                                      "\"numbytes\":%d,"
                                      "\"status\":\"complete\","
                                      "\"prev_status\":\"%s\"}",
                                      cur_bi->id(),
                                      cur_bi->gs(),
                                      cur_bi->num_bytes(),
                                      GetStateStr(prev_state).c_str());
    RPC_SW_CTX_LOG(rpc_sw_ctx, "CountReplica & CompleteBlock");

    sw.NextStep(metrics_.ccp_last_block_inc_safe_block_count_time_);
    // TODO(ruanjunbin): Gap between set meta storage and memory.
    if (prev_state != BlockUCState::kPersisted && !cur_bi->IsPersisted()) {
      safemode_->AdjustSafeModeBlockTotals(0, 1);
      safemode_->IncrementSafeBlockCount(FLAGS_dfs_replication_min);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "AdjustSafeModeBlockTotals");
    }

    VLOG(8) << "complete B" << blk_from_client.id;
    if (flag_save_storage_from_uc) {
      cur_bi = s->AddStorages(cur_bi, dns);
      VLOG(10) << "AddStorages from uc to complete"
               << " block_id=" << blk_from_client.id
               << " dns.size()=" << dns.size();
      RPC_SW_CTX_LOG(rpc_sw_ctx, "Save UC DNS");
    }
  } else {
    RPC_SW_CTX_LOG(rpc_sw_ctx, "CountReplica & CompleteBlock");
  }

  return ret;
}

bool BlockManager::PersistBlock(const Block& block) {
  auto& s = slice(block.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  return PersistBlockUnsafe(s.get(), block);
}

bool BlockManager::PersistBlockUnsafe(BlockMapSlice* s, const Block& block) {
  // Slice will not be nullptr forever.
  CHECK_NOTNULL(s);
  BlockInfoGuard bi_guard(s, block.id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    LOG(ERROR) << "Try to persist not existed block B" << block.id;
    return false;
  }
  auto prev_state = bi->uc_state();
  bool r = s->PersistBlock(bi, ns_->blockpool_id(), block);
  if (r) {
    block_count_--;
    if (prev_state == BlockUCState::kComplete) {
      safemode_->AdjustSafeModeBlockTotals(
          bi->size() >= FLAGS_dfs_replication_min ? -1 : 0, -1);
    }
  }
  return r;
}

std::vector<DatanodeID> BlockManager::ConvertBlockToUnderConstruction(
    const std::string& bp_id,
    const BlockProto& block,
    const INode& inode) {
  auto& s = slice(block.blockid());
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), block.blockid(), true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << block.blockid();
    return {};
  }
  CHECK_NOTNULL(bi);

  auto rn = CountReplica(bi);

  auto prev_state = bi->uc_state();
  std::shared_ptr<UnderConstructionState> uc;
  if (bi->HasBeenComplete()) {
    DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(bi->id(),
                                      "\"blk_id\":%" PRIu64
                                      ","
                                      "\"op\":\"to_uc\","
                                      "\"gs\":%" PRIu64
                                      ","
                                      "\"numbytes\":%d,"
                                      "\"status\":\"uc\","
                                      "\"prev_status\":\"%s\"}",
                                      bi->id(),
                                      bi->gs(),
                                      bi->num_bytes(),
                                      GetStateStr(bi->uc_state()).c_str());

    s->ConvertToUnderConstruction(bi);
  }

  Block blk{bi->id(), bi->num_bytes(), bi->gs()};
  needed_replications_.Remove(
      blk, rn.live, BlockExpectedReplica(*bi, &inode));
  s->RemovePendingReplications(block.blockid());
  auto sids = s->GetExpectedLocations(bi->id());
  for (auto& dn_id : sids) {
    s->RemoveFromInvalidateBlock(dn_id, blk);
    // NOTICE: truncatable block should not reopen to write.
    s->RemoveFromTruncatableBlock(dn_id, blk);
  }
  if (prev_state == BlockUCState::kComplete) {
    safemode_->AdjustSafeModeBlockTotals(
        sids.size() >= FLAGS_dfs_replication_min ? -1 : 0, -1);
  }
  return sids;
}

void BlockManager::UpdateState() {
  BlockMapStat stat;
  stat.num_under_replicated = needed_replications_.size();
  stat.num_missing = needed_replications_.GetCorruptBlockSize();
  stat.num_missing_one_block =
      needed_replications_.GetCorruptReplOneBlockSize();
  stat.num_block = block_count_.load();
  stat.num_zero_replica_block = zero_replica_block_count_.load();
  stat.num_invalidate = invalidate_blk_count_.load();
  stat.num_truncatable = truncatable_blk_count_.load();
  int64_t btime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  for (auto& s : slices_) {
    std::shared_lock<BlockMapSlice> guard(*s);
    stat.num_uc += s->GetUCSize();
    stat.num_sealed_block += s->GetSealedBlockNum();
    stat.num_corrupt_block += s->GetCorruptBlockNum();
    stat.num_excess_replicas += s->GetExcessBlockNum();
    stat.num_pending_replication += s->GetPendingReplicationsNum();
    stat.num_future_blocks += s->NumFutureBlocks();
    stat.num_misinvalidated_blocks += s->NumMisinvalidatedBlks();
  }
  int64_t etime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  {
    std::shared_lock<RWSpinlock> lock(rwlock_);
    stat.num_postponed_misreplicated = postponed_misreplicated_blocks_.size();
    stat.num_replication_queue = 0;
    for (auto iter = dn_replication_queues_.begin();
         iter != dn_replication_queues_.end();
         ++iter) {
      stat.num_replication_queue += iter->second.size();
    }
    stat.num_recover = 0;
    for (auto iter = to_recover_.begin(); iter != to_recover_.end(); ++iter) {
      stat.num_recover += iter->second.size();
    }
  }
  std::string str = stat.ToString();
  {
    std::unique_lock<ReadWriteLock> lock(stat_lock_);
    stat_ = std::move(stat);
  }
  LOG(INFO) << "[" << str << ", TraverseSliceTime: " << (etime - btime)
            << "ms]";
}

std::string BlockManager::DumpBlocksInParquet(std::string compress_type,
                                              std::string file_name) {
  if (file_name == "") {
    std::string res =
        "Not Support dump parquet block info with empty filename.";
    return res;
  }

  std::shared_ptr<ParquetWriter> parquet_writer =
      std::make_shared<ParquetWriter>(file_name, compress_type);
  if (!parquet_writer->Init()) {
    std::string res = "Init parquet file failed. Failed to dump blocksmap to " +
                      file_name +
                      cnetpp::concurrency::ThisThread::GetLastErrorString();
    LOG(ERROR) << res;
    return res;
  }

  // reuse one malloc buffer. Realloc the buffer if it is too small.
  int bufferSize = 1024 * 1024 * sizeof(char);
  std::unique_ptr<char[]> dns_buffer =
      std::unique_ptr<char[]>(new char[bufferSize]);

  auto dn_manager = datanode_manager_;

  for (auto& slice : slices_) {
    slice->TraverseAllBlock(
        [&parquet_writer, &dn_manager, &dns_buffer, &bufferSize](
            BlockInfo** buckets_, size_t num_buckets) {
          char* raw_ptr = dns_buffer.get();

          for (size_t i = 0; i < num_buckets; i++) {
            auto bucket = buckets_[i];
            while (bucket) {
              int dn_size = bucket->size();
              // type + ip_array; Type->0 means ipv4 address; Type->1 means ipv6
              // address; Because there maybe ipv6 datanode in the future. One
              // ipv6_bytearray may occupy 16 bytes. So we keep 16 bytes for one
              // ip_byte_array.
              if (dn_size * 17 * sizeof(char) > bufferSize) {
                bufferSize = dn_size * 17 * sizeof(char);
                dns_buffer = std::unique_ptr<char[]>(new char[bufferSize]);
                raw_ptr = dns_buffer.get();
              }

              int array_pos = 0;
              auto dn_info_ptr = bucket->storage_ids();
              for (size_t i = 0; i < bucket->size(); ++i) {
                auto dn_info = dn_manager->GetDatanodeFromId(dn_info_ptr[i]);
                if (UNLIKELY(!dn_info)) {
                  continue;
                }

                auto ip_family = dn_info->ip().Family();
                if (ip_family == AF_INET) {
                  raw_ptr[array_pos++] = 0x00;
                } else if (ip_family == AF_INET6) {
                  raw_ptr[array_pos++] = 0x01;
                } else {
                  continue;
                }

                // Directly use memcpy to copy data.
                auto address = dn_info->ip().address();
                memcpy(raw_ptr + array_pos, address.data(), address.size());
                array_pos += address.size();
              }

              if (array_pos == 0) {
                // No block info. Using 0x02 to represent missing block
                // condition.
                raw_ptr[array_pos++] = 0x02;
              }

              parquet_writer->WriteRow(bucket->inode_id(),
                                       bucket->id(),
                                       bucket->gs(),
                                       bucket->num_bytes(),
                                       raw_ptr,
                                       array_pos);
              bucket = bucket->next();
            }
          }
          return true;
        });
  }

  std::string res = "Dump block info success. File path: " + file_name;
  LOG(INFO) << res;
  return res;
}

std::string BlockManager::DumpBlocks(std::string dump_path) {
  auto fd = open(&(dump_path[0]), O_RDWR | O_CREAT);
  if (fd == -1) {
    std::string res = "Failed to dump blocksmap to " + dump_path +
                      cnetpp::concurrency::ThisThread::GetLastErrorString();
    LOG(ERROR) << res;
    return res;
  }

  DEFER([&] { close(fd); });

  auto res = "Dumped the blocksmap to " + dump_path;

  std::vector<BlockID> blks;
  for (auto& slice : slices_) {
    const size_t batch = 10;
    for (size_t i = 0; i < FLAGS_blockmap_num_bucket_each_slice; i += batch) {
      slice->TraverseBuckets(i, batch, [&blks](BlockInfo* b) {
        blks.emplace_back(b->id());
        return true;
      });
      std::stringstream ss;
      for (auto& b : blks) {
        DumpBlockInfo(b, nullptr, false, ss);
      }
      auto str = ss.str();
      const char* p = str.data();
      size_t s = 0;
      while (s < str.length()) {
        auto r = write(fd, p, str.length() - s);
        if (r == -1) {
          res = "Failed to dump blocksmap to " + dump_path +
                cnetpp::concurrency::ThisThread::GetLastErrorString();
          LOG(ERROR) << res;
          return res;
        }
        p += r;
        s += r;
      }
      blks.clear();
    }
  }
  LOG(INFO) << res;
  return res;
}

void BlockManager::GetCorruptFilesInfo(uint32_t offset,
                                       uint32_t limit,
                                       uint32_t* total,
                                       std::ostringstream& os) {
  std::vector<UnderReplicatedBlocks::BlockCorruptHint> result;
  needed_replications_.ListCorruptBlocks(offset, limit, total, &result);
  bool first = true;
  for (auto& hint : result) {
    std::string ip = "";
    if (hint.dn_id != kInvalidDatanodeID) {
      auto dn_info = datanode_manager_->GetDatanodeFromId(hint.dn_id);
      if (!dn_info) {
        LOG(WARNING) << "GetCorruptFilesInfo, dn: " << hint.dn_id
                     << " does not exist";
        continue;
      }
      ip = dn_info->ip().ToString();
    }
    std::string path = "";
    if (hint.inode_id != kInvalidINodeId) {
      path = ns_->BuildFullPath(hint.inode_id);
    }
    if (path.empty()) {
      LOG(WARNING) << "GetCorruptFilesInfo, block: " << hint.block_id
                   << ", inode: " << hint.inode_id << " does not exist";
      continue;
    }
    std::time_t last_time = std::chrono::system_clock::to_time_t(hint.time);
    char time_cstr[30];
    std::string time_str(::ctime_r(&last_time, time_cstr));

    // output
    if (first) {
      first = false;
    } else {
      os << ",";
    };
    os << "{"
       << "\"file\":\"" << path << "\","
       << "\"block\":\"" << hint.block_id << "\","
       << "\"last_dn\":\"" << ip << "\","
       << "\"reason\":\"" << hint.reason << "\","
       << "\"time\":\"" << time_str.substr(0, time_str.length() - 1) << "\""
       << "}";
  }
}

void BlockManager::DumpBlockSimpleInfo(BlockID blk_id, std::ostringstream& os) {
  os << blk_id;

  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) return;

  int stale = 0;
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn_info = datanode_manager_->GetDatanodeFromId(sid);
    if (!dn_info) {
      continue;
    }
    os << ", " << dn_info->ip().ToString();
    if (dn_info->IsDecommissionInProgress()) {
      os << "#D";
    }
    if (s->IsBlockCorrupt(bi->id(), sid)) {
      os << "#C";
    } else if (s->IsBlockExcess(bi->id(), sid)) {
      os << "#E";
    } else {
      os << "#L";
    }
    if (s->IsBlockSealed(bi->id(), sid)) {
      os << "#S";
    }
    if (dn_info->content_stale()) {
      ++stale;
    }
  }
  os << "@" << stale;

  auto full_path = ns_->BuildFullPath(bi->inode_id());
  if (!full_path.empty()) {
    os << ", " << full_path;
  }
}

void BlockManager::DumpBlockInfo(BlockID blk_id,
                                 const BlockProto* blk_in_inode,
                                 bool dump_block_detail,
                                 std::ostream& os) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return;
  }

  os << "blk_" << blk_id;
  os << ", len=" << bi->num_bytes();
  if (blk_in_inode && bi->num_bytes() != blk_in_inode->numbytes()) {
    os << "[in_inode=" << blk_in_inode->numbytes() << "]";
  }
  os << ", gs=" << bi->gs();
  if (blk_in_inode && bi->gs() != blk_in_inode->genstamp()) {
    os << "[in_inode=" << blk_in_inode->genstamp() << "]";
  }
  os << ", uc=" << BlockUCStateToString(bi->uc_state());
  if (dump_block_detail) {
    os << ", need_replication=" << needed_replications_.Contains(bi->blk());
    os << ", num_pending_replication=" << s->NumReplicas(blk_id);
  }
  if (!bi->HasBeenComplete()) {
    auto uc_state = s->GetUcInternal(blk_id);
    if (!uc_state) {
      LOG(ERROR) << "UC block " << blk_id << " is not in uc state";
      return;
    }
    os << ", recovery_id=" << uc_state->recovery_id()
       << ", primary_storage_id=" << uc_state->primary_storage_id()
       << ", expected_locations=[";
    for (const auto& loc : uc_state->expected_locations()) {
      auto dn_id = loc.first;
      auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
      if (!dn) {
        LOG(ERROR) << "dn " << dn_id << " does not exist";
        continue;
      }
      os << absl::StrFormat("%s@(%lld,%lld), ",
                            dn->ip().ToString(),
                            loc.second.nbytes,
                            loc.second.gs);
    }
    os << "], tried_as_primary_map={";
    for (const auto& tried : uc_state->tried_as_primary()) {
      auto dn_id = tried.first;
      auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
      if (!dn) {
        LOG(ERROR) << "dn " << dn_id << " does not exist";
        continue;
      }
      os << dn->ip().ToString() << "->" << static_cast<int>(tried.second)
         << ", ";
    }
    os << "}";
  }
  os << ", ";
  auto rn = CountReplica(bi);
  os << rn;
  os << ", [";
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn_info = datanode_manager_->GetDatanodeFromId(sid);
    if (!dn_info) {
      continue;
    }
    if (i > 0) {
      os << ", ";
    }
    os << dn_info->GetFullLocationString();
    if (!dn_info->nodezone_id().empty()) {
      os << "(nodezone=" << dn_info->nodezone_id() << ")";
    }
    if (s->IsBlockCorrupt(blk_id, sid)) {
      os << "(Corrupt)";
    }
    if (s->IsBlockSealed(blk_id, sid)) {
      os << "(Sealed)";
    }
    if (s->IsBlockExcess(blk_id, sid)) {
      os << "(Excess)";
    }
    if (dn_info->IsDecommissionInProgress()) {
      os << "(Decommission)";
    }
    if (dump_block_detail) {
      // dump dn replication queue info
      std::unique_lock<RWSpinlock> lock(rwlock_);
      auto iter = dn_replication_queues_.find(dn_info->id());
      if (iter != dn_replication_queues_.end()) {
        for (auto pair : iter->second) {
          if (pair.blk.id == blk_id) {
            os << "(replication_targets=";
            for (size_t j = 0; j < pair.targets.size(); j++) {
              if (j > 0) {
                os << ",";
              }
              auto dn = datanode_manager_->GetDatanodeFromId(pair.targets[j]);
              os << dn->ip().ToString();
            }
            os << ")";
          }
        }
      }
    }
  }
  os << "]\n";
  if (dump_block_detail) {
    BlockInfoProto bip;
    if (meta_storage_->GetBlockInfo(blk_id, &bip)) {
      os << "  bip=" << bip.ShortDebugString();
    } else {
      os << "BlockInfoProto not found in DB";
    }
    os << "\n";
  }
}

void BlockManager::CheckReplica(const std::vector<BlockProto>& blocks,
                                const INode* inode,
                                StopWatchContext* rpc_sw_ctx) {
  for (const auto& block : blocks) {
    RPC_SW_CTX_LOG(rpc_sw_ctx,
                   "CheckReplica block_id=" + std::to_string(block.blockid()));

    auto& s = slice(block.blockid());
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after BlockMapSlice lock");

    BlockInfoGuard bi_guard(s.get(), block.blockid(), true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
      LOG(ERROR) << "block not exist, block_id=" << block.blockid();
      return;
    }
    CHECK_NOTNULL(bi);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after GetBlockInfo");

    uint32_t expected = BlockExpectedReplica(*bi, inode);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after BlockExpectedReplica");

    auto rn = CountReplica(bi);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after CountReplica");

    if (IsNeededReplication(bi, inode, expected, rn.live)) {
      needed_replications_.Add(
          bi, rn.live, expected, "Check replica when finalize file");
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "after IsNeededReplication");

    if (rn.live > expected) {
      ProcessOverReplicatedBlock(bi, expected);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "after ProcessOverReplicatedBlock");
    }
  }
}

// NOTICE: slice lock should already be head
bool BlockManager::HasEnoughRack(const BlockInfo* bi, const INode* inode) {
  if (!FLAGS_blockmap_check_enough_racks) {
    return true;
  }

  auto& s = slice(bi->id());
  auto corrupted = s->GetCorruptBlock(bi->id());
  auto rep = BlockExpectedReplica(*bi, inode);

  std::string rack_name;
  if (rep == 1 || (rep > 1 && !datanode_manager_->EverBeenMultiRack())) {
    return true;
  }
  bool need_log = VLOG_IS_ON(2);
  std::string dns;
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn_info = datanode_manager_->GetDatanodeFromId(sid);
    CHECK_NOTNULL(dn_info);
    bool not_corrupted =
        corrupted == nullptr || corrupted->find(sid) == corrupted->end();
    if (not_corrupted) {
      if (rack_name.empty()) {
        rack_name = dn_info->rack();
      } else if (rack_name.compare(dn_info->rack()) != 0) {
        return true;
      }
    }
    if (need_log) {
      dns += dn_info->GetFullLocationString() +
             "(corrupted=" + std::to_string(!not_corrupted) + "), ";
    }
  }
  if (need_log) {
    LOG(INFO) << "Not enough rack, blk_id: " << bi->id() << ", dns: " << dns;
  }
  return false;
}

void BlockManager::SetReplica(const std::vector<BlockProto>& blocks,
                              uint32_t old_replica,
                              uint32_t new_replica,
                              bool need_recover) {
  if (old_replica == new_replica) {
    return;
  }

  for (const auto& b : blocks) {
    // Set normal file to bytecool file.
    if (new_replica == 0) {
      --block_count_;
      ++zero_replica_block_count_;
    }
    auto blk_id = b.blockid();
    auto& s = slice(blk_id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), blk_id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
      LOG(ERROR) << "block not exist, block_id=" << blk_id;
      continue;
    }
    CHECK_NOTNULL(bi);
    bi->set_expected_rep(new_replica);
    if (!need_recover) {
      continue;
    }
    UpdateNeededReplications(bi, 0, new_replica - old_replica, "SetReplica");
    if (old_replica > new_replica) {
      ProcessOverReplicatedBlock(bi, new_replica);
    }

    DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(bi->id(),
                                      "\"blk_id\":%" PRIu64
                                      ","
                                      "\"op\":\"set_replica\","
                                      "\"gs\":%" PRIu64
                                      ","
                                      "\"numbytes\":%d,"
                                      "\"status\":\"%s\","
                                      "\"replica\":%d,"
                                      "\"old_replica\":%d}",
                                      bi->id(),
                                      bi->gs(),
                                      bi->num_bytes(),
                                      GetStateStr(bi->uc_state()).c_str(),
                                      new_replica,
                                      old_replica);
  }
}

void BlockManager::UpdateLength(BlockID blk_id, uint64_t len) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << blk_id;
    return;
  }
  CHECK_NOTNULL(bi);
  bi->UpdateLength(len);
}

void BlockManager::UpdateGenStamp(BlockID blk_id, uint64_t gen_stamp) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << blk_id;
    return;
  }
  CHECK_NOTNULL(bi);
  bi->UpdateGs(gen_stamp);
}

void BlockManager::UpdateINode(BlockID blk_id, uint64_t inode_id) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
    LOG(ERROR) << "block not exist, block_id=" << blk_id;
    return;
  }
  CHECK_NOTNULL(bi);
  bi->UpdateINode(inode_id);
}

Status BlockManager::CommitBlockSynchronization(
    const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
        request) {  // NOLINT(whitespace/line_length)
  auto& s = slice(request.block().blockid());
  {
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    auto ret = s->CommitBlockSynchronization(request);
    if (ret.HasException()) {
      return ret;
    }
    if (request.deleteblock()) {
      // RemoveBlock(request.block().blockid(), s.get());
    } else {
      {
        auto dn_ids = datanode_manager_->AddBlockStorages(
            request, request.block().blockid());
        BlockInfoGuard bi_guard(s.get(), request.block().blockid(), true);
        BlockInfo* bi = bi_guard.GetBlockInfo();
        if (FLAGS_block_ignore_not_exist_fatal && bi == nullptr) {
          LOG(ERROR) << "block not exist, block_id="
                     << request.block().blockid();
          return Status(JavaExceptions::kIOException, "block not exist");
        }
        CHECK_NOTNULL(bi);
        // clear old storages because some of them may not reported recovered
        std::vector<DatanodeID> storages;
        for (size_t i = 0; i < bi->size(); ++i) {
          storages.emplace_back(bi->storage_id(i));
        }
        bool isCorrupt = true;
        for (const auto& stored_dn : storages) {
          isCorrupt = true;
          for (const auto& tar_dn : dn_ids) {
            if (tar_dn == stored_dn) {
              isCorrupt = false;
              break;
            }
          }
          if (isCorrupt) {
            s->AddToCorruptBlock(bi->id(),
                                 stored_dn,
                                 Block(bi->id(), bi->num_bytes(), bi->gs()));
          }
        }
        bi = s->AddStorages(bi, dn_ids);
      }
    }
  }
  // TODO: This may be a bug, please see
  // BlockManagerTestV2.CommitBlockSynDeleteBlockAndCloseFile.
  if (!request.deleteblock()) {
    Block blk(request.block().blockid(),
              static_cast<uint32_t>(request.newlength()),
              request.newgenstamp());
    CommitOrCompleteOrPersistLastBlock(blk);
  }
  return Status();
}

Block BlockManager::GetBlock(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);

  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return Block();
  }
  return bi->blk();
}

DetailedBlock BlockManager::GetDetailedBlock(BlockProto bp,
                                             bool need_storage_class_report) {
  auto blk_id = bp.blockid();
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);

  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    // for snapshot-read
    // maybe INode has been deleted, we can't find the BlockInfo in BlockManager
    // so use the BlockInfo in the INode as a substitute
    return DetailedBlock{bp};
  }

  bp.set_numbytes(bi->blk().num_bytes);

  BlockInfoProto bip;
  meta_storage_->GetBlockInfo(blk_id, &bip);

  auto storages = s->GetBlockStorages(blk_id);

  std::vector<cloudfs::StorageClassReportProto> scr;
  if (need_storage_class_report) {
    std::vector<std::string> uuids;
    std::vector<cloudfs::StorageClassReportProto> tmp_scr;
    meta_storage_->GetStorageClassReports(blk_id, &uuids, &tmp_scr);
    for (size_t i = 0; i < storages.size(); i++) {
      for (size_t j = 0; j < uuids.size(); j++) {
        DatanodeID id = datanode_manager_->GetDatanodeInterId(uuids[j]);
        if (storages[i] == id) {
          scr.emplace_back(tmp_scr[j]);
          break;
        }
      }
    }
  }

  ReplicaNum rn = CountReplica(bi);

  return DetailedBlock{bp,
                       bi->blk(),
                       bi->uc_state(),
                       bip.has_pufs_name() ? bip.pufs_name() : "",
                       storages,
                       scr,
                       rn};
}

uint64_t BlockManager::GetBlockINodeID(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return kInvalidINodeId;
  }
  return bi->inode_id();
}

size_t BlockManager::SpeedUpUCBlockRelease(
    const std::vector<BlockID>& blk_ids) {
  std::set<INodeID> inode_ids;
  for (auto& blk_id : blk_ids) {
    auto& s = slice(blk_id);
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), blk_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      LOG(WARNING) << "Speed up missed block with block id " << blk_id;
      continue;
    }
    if (bi->uc_state() != BlockUCState::kUnderConstruction) {
      continue;
    }
    VLOG(10) << "Decommission found blk " << bi->ToString();
    inode_ids.insert(bi->inode_id());
  }
  LOG(INFO) << "Decommission uc inodes num " << inode_ids.size();
  for (auto& inode_id : inode_ids) {
    ns_->SpeedUpRelease(inode_id);
  }
  LOG(INFO) << "Decommission speed up finished";
  return inode_ids.size();
}

std::vector<DatanodeID> BlockManager::GetBlockStorage(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  return s->GetBlockStorages(blk_id);
}

std::vector<DatanodeID> BlockManager::GetBlockStorageSafe(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  return s->GetBlockStoragesSafe(blk_id);
}

bool BlockManager::RemoveStorage4Http(BlockID blk_id, DatanodeID dn_id) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  return RemoveStorageInternal(blk_id, dn_id);
}

// NOTICE: slice write lock should already been held
// Modify (block-->datanode) map. Possibly generate replication tasks, if the
// removed block is still valid.
bool BlockManager::RemoveStorageInternal(BlockID blk_id, DatanodeID dn_id) {
  auto& s = slice(blk_id);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return false;
  }
  s->RemoveFromCorruptBlock(bi->id(), dn_id);
  s->RemoveFromSealedBlock(bi->id(), dn_id);
  s->RemoveFromInvalidateBlock(dn_id,
                               Block(bi->id(), bi->num_bytes(), bi->gs()));
  s->RemoveFromTruncatableBlock(dn_id,
                                Block(bi->id(), bi->num_bytes(), bi->gs()));
  s->RemoveFromExcess(bi->id(), dn_id);
  if (bi->RemoveStorage(dn_id)) {
    VLOG(8) << "Remove storage, blk_id: " << blk_id << ", dn_id: " << dn_id;
    if (bi->IsComplete()) {
      safemode_->DecrementSafeBlockCount(CountReplica(bi).live);
    }
    UpdateNeededReplications(bi, -1, 0, "RemoveStorageInternal", dn_id);
    if (bi->size() == 0) {
      do {
        BlockInfoProto bip;
        if (!meta_storage_->GetBlockInfo(bi->id(), &bip)) {
          LOG(ERROR) << "MissingBlockInMetaStorage B" << bi->id();
          break;
        }
        if (!bi->IsSafeToRelease(bip)) {
          LOG(WARNING) << "B" << bi->id() << " is unsafe to release block info";
          break;
        }
        VLOG(10) << "Delete zero replica BlockInfo, B" << bi->id();
        s->RemoveBlock(bi->id());
        bi = nullptr;
      } while (false);  // This is for break statement.
    }
    return true;
  }
  return false;
}

bool BlockManager::RemoveCachedBlockInfo(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return false;
  }
  bool is_deleted = false;
  do {
    BlockInfoProto bip;
    if (!meta_storage_->GetBlockInfo(bi->id(), &bip)) {
      LOG(ERROR) << "MissingBlockInMetaStorage B" << bi->id();
      break;
    }
    if (!bi->IsSafeToRelease(bip)) {
      break;
    }
    is_deleted = s->RemoveBlock(bi->id());
    bi = nullptr;
  } while (false);  // This is for break statement.
  return is_deleted;
}

void BlockManager::AsyncBlockReport(
    const std::string& dn_uuid,
    const cloudfs::datanode::BlockReportRequestProto* request,
    RpcClosure* rpc_done) noexcept {
  ClosureGuard done_guard(rpc_done);
  if (ha_state_->GetHAMode() == EditLogConf::StandbyNonHA) {
    rpc_done->set_status(Status(JavaExceptions::kNoException, "StandbyNonHA"));
    return;
  }

  auto dn_id = datanode_manager_->GetDatanodeInterId(dn_uuid);
  if (dn_id == kInvalidDatanodeID) {
    rpc_done->set_status(
        Status(JavaExceptions::kIOException,
               "Block report from unregistered node: " + dn_uuid));
    return;
  }

  auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
  if (dn == nullptr || !dn->CheckAndUpdateHeartbeat()) {
    rpc_done->set_status(Status(JavaExceptions::kIOException,
                                "Block report from dead node: " + dn_uuid));
    return;
  }

  dn->LockForBlockReport();

  LOG(INFO) << "Received block report from " << dn_id;

  auto blk_report_ctx = block_report_manager_->GetBlockReportContext(dn_id);

  if (blk_report_ctx == nullptr) {
    LOG_WITH_LEVEL(WARNING) << "Unexpected FBR from dn " << dn_id;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    rpc_done->set_status(
        Status(JavaExceptions::kIOException, "Unexpected FBR"));
    dn->UnlockForBlockReport();
    return;
  }

  if (!request->has_context() || !request->context().has_id()) {
    LOG(ERROR) << "FBR is bad-formatted, missing context: "
               << request->ShortDebugString();
    rpc_done->set_status(
        Status(JavaExceptions::kIOException, "FBR is missing context"));
    block_report_manager_->FinishFullBlockReport(dn_id);
    dn->UnlockForBlockReport();
    return;
  }

  // First page in FBR, record report_id
  if (blk_report_ctx->block_report_id == -1) {
    blk_report_ctx->block_report_id = request->context().id();
    blk_report_ctx->cur_rpc = request->context().currpc() - 1;
    blk_report_ctx->total_rpc = request->context().totalrpcs();
  }

  // Check consistency with previous pages
  if (blk_report_ctx->block_report_id != request->context().id() ||
      blk_report_ctx->cur_rpc + 1 != request->context().currpc() ||
      blk_report_ctx->total_rpc != request->context().totalrpcs()) {
    LOG(ERROR) << "Unexpected FBR from dn " << dn_id
               << " block report context mismatch "
               << blk_report_ctx->ToString() << " vs "
               << request->context().ShortDebugString();
    rpc_done->set_status(
        Status(JavaExceptions::kIOException, "FBR context mismatch"));
    block_report_manager_->FinishFullBlockReport(dn_id);
    dn->UnlockForBlockReport();
    return;
  }

  blk_report_ctx->cur_rpc = request->context().currpc();

  // Storage is not freezed, either NN not received FBR during preparing window
  // or the block index is under heavy pressure. DN should wait for block report
  // cmd next time.
  if (!dn->IsDoingFBR()) {
    LOG(ERROR) << "Storage is not freezed for dn " << dn_id;
    rpc_done->set_status(
        Status(JavaExceptions::kIOException,
               "NN aborted FBR, should wait for next block report cmd"));
    block_report_manager_->FinishFullBlockReport(dn_id);
    dn->UnlockForBlockReport();
    return;
  }

  block_report_manager_->UpdateBlockReportRecord(
      dn_id, false, /*ignore*/ false);

  auto done_ctx = std::make_shared<RpcDoneContext>();
  done_ctx->storage_count = request->reports_size();
  done_ctx->done = done_guard.release();

  LOG(INFO) << "Start process FBR from dn " << dn_id << ", "
            << blk_report_ctx->ToString();

  for (int report_idx = 0; report_idx < request->reports_size(); ++report_idx) {
    auto& report = request->reports(report_idx);

    auto storage = dn->GetOrCreateStorage(report.storage());

    // TODO(zhuangsiyu): Deprecated in 4.6.2
    if ((safemode_->IsStartingUp()) && (blk_report_ctx->cur_rpc == 0) &&
        (!storage->ConditionalIncBlockReportCount())) {
      VLOG(8) << "Discarded non-initial block report, "
              << "because dancenn still in startup phase, dn_id: " << dn_id
              << ", storage_id: " << report_idx;
      if (done_ctx->storage_count.fetch_sub(1) == 1) {
        // the last storage
        if (blk_report_ctx->cur_rpc >= blk_report_ctx->total_rpc - 1) {
          // Set total rpc to MAX, finish FBR will mark it failed
          blk_report_ctx->total_rpc = std::numeric_limits<int32_t>::max();
          block_report_manager_->FinishFullBlockReport(dn_id);
        }
        dn->UnlockForBlockReport();
        done_ctx->done->Run();
      }
      continue;
    }

    // BlockReportCmd is issued by NN with heartbeat, this branch should never
    // run.
    if (storage->GetBlockReportCount() != 0 &&
        !dn->heartbeated_since_failover()) {
      done_ctx->done->set_status(
          Status(JavaExceptions::kInvalidRequestException,
                 "Should send heartbeat first"));
      if (done_ctx->storage_count.fetch_sub(1) == 1) {
        // the last storage
        if (blk_report_ctx->cur_rpc >= blk_report_ctx->total_rpc - 1) {
          // Set total rpc to MAX, finish FBR will mark it failed
          blk_report_ctx->total_rpc = std::numeric_limits<int32_t>::max();
          block_report_manager_->FinishFullBlockReport(dn_id);
        }
        dn->UnlockForBlockReport();
        done_ctx->done->Run();
      }
      continue;
    }

    auto worker_num = blk_report_thread_.size();

    auto& storage_report_ctx =
        blk_report_ctx->storage_ctx_map[report.storage().storageuuid()];
    if (storage_report_ctx == nullptr) {
      /**
       * Newly added storage, NN do not have snapshot for it.
       */
      storage_report_ctx.reset(new StorageReportContext);
    }
    storage_report_ctx->storage = report.storage();
    storage_report_ctx->slice_thread_count = worker_num;

    uint64_t fin_num = 0, uc_num = 0;
    Status st = DecodeBlocks(report,
                             worker_num,
                             &storage_report_ctx->blocks_decoded,
                             &fin_num,
                             &uc_num);
    if (!st.IsOK()) {
      done_ctx->done->set_status(
          Status(JavaExceptions::kInvalidRequestException,
                 "failed to decode blocks from FBR protobuf"));
      if (done_ctx->storage_count.fetch_sub(1) == 1) {
        // the last storage
        if (blk_report_ctx->cur_rpc >= blk_report_ctx->total_rpc - 1) {
          // Set total rpc to MAX, finish FBR will mark it failed
          blk_report_ctx->total_rpc = std::numeric_limits<int32_t>::max();
          block_report_manager_->FinishFullBlockReport(dn_id);
        }
        dn->UnlockForBlockReport();
        done_ctx->done->Run();
      }
      continue;
    }

    DANCENN_BLOCKLIFECYCLE_DN_LOG(
        "\"dn\":\"%s\","
        "\"op\":\"full_block_report\","
        "\"finalize\":%" PRIu64
        ","
        "\"uc\":%" PRIu64 "}",
        dn->ip().ToString().c_str(),
        fin_num,
        uc_num);

    for (int worker_idx = 0; worker_idx < worker_num; worker_idx++) {
      blk_report_thread_[worker_idx]->AddTask([dn_id,
                                               dn,
                                               storage,
                                               report,
                                               blk_report_ctx,
                                               storage_report_ctx,
                                               report_idx,
                                               worker_idx,
                                               done_ctx,
                                               request,
                                               this]() -> bool {
        ProcessBlockReport(dn_id,
                           dn,
                           storage,
                           report,
                           blk_report_ctx,
                           storage_report_ctx,
                           report_idx,
                           worker_idx,
                           done_ctx,
                           request);
        return true;
      });
    }
  }
}

void BlockManager::ProcessBlockReport(
    DatanodeID dn_id,
    DatanodeInfoPtr dn,
    std::shared_ptr<StorageInfo> storage,
    const cloudfs::datanode::StorageBlockReportProto& report,
    std::shared_ptr<BlockReportContext> blk_report_ctx,
    std::shared_ptr<StorageReportContext> storage_report_ctx,
    int report_idx,
    int worker_idx,
    std::shared_ptr<RpcDoneContext> done,
    const cloudfs::datanode::BlockReportRequestProto* request) {

  auto& blocks = storage_report_ctx->blocks_decoded[worker_idx];

  std::vector<BlockID> to_add;
  to_add.reserve(blocks.size());
  std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
  for (auto& blk : blocks) {
    RPC_SW_CTX_INIT(
        rpc_sw_ctx, "[FBR]", absl::StrFormat("block_id=%d", blk.id));
    RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

    if (storage_report_ctx->delta_blks.find(blk.id) !=
        storage_report_ctx->delta_blks.end()) {
      VLOG(12) << "ProcessBlockReport B" << blk.id << " hit delta set, ignored";
      continue;
    }

    Block block;
    block.Init(blk.id, static_cast<uint32_t>(blk.len), blk.gs);
    ReplicaStateProto state = blk.replica_state;

    replica_report.emplace_back(block.id, blk.storage_class_report);

    auto& s = slice(block.id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "add lock");

    if (ShouldPostponeBlocksFromFutureV2(block.id, block.gs)) {
      s->EnqueuePendingFutureBlks(report.storage(), block, dn_id, state);
      continue;
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "should postpone");

    if (s->IsBlockInvalidate(block, dn_id)) {
      continue;
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "is block invalidate");

    BlockInfoGuard bi_guard(s.get(), block.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    uint8_t live_rep = 0;
    if (bi == nullptr) {
      VLOG(10) << "Failed to get block info b" << block.id << " dn "
               << dn->hostname();
    } else {
      live_rep = CountReplica(bi).live;
    }
    auto res = s->ProcessReportedBlock(dn_id,
                                       block,
                                       state,
                                       is_active_,
                                       dn->IsDecommissionInProgress(),
                                       live_rep);
    RPC_SW_CTX_LOG(rpc_sw_ctx, "ProcessReportedBlock");

    switch (res) {
      case ADD:
        ReplicaNum rn;
        if (AddStoredBlock(dn_id, block, &rn)) {
          to_add.emplace_back(block.id);
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "do ADD");
        break;
      case INVALIDATE:
        // for active, not need to record misinvalidated block
        if (!is_active_) {
          VLOG(8) << "ProcessBlockReport misinvalidated block: "
                  << block.ToString() << ", state: " << state;
          s->EnqueuePendingMisinvalidatedBlks(
              ReportedBlockInfo{block,
                                dn_id,
                                report.storage(),
                                state,
                                std::chrono::steady_clock::now()});
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "do INVALIDATE");
        break;
      case CORRUPT:
        if (IsPopulatingReplicationQueues()) {
          ProcessCorruptReplica(dn_id, block, "ProcessBlockReport");
        }
        RPC_SW_CTX_LOG(rpc_sw_ctx, "do CORRUPT");
        break;
      case ADD_TO_BLOCK_INDEX:
        to_add.emplace_back(block.id);
        RPC_SW_CTX_LOG(rpc_sw_ctx, "do ADD_TO_BLOCK_INDEX");
        break;
      case NOTHING:
        RPC_SW_CTX_LOG(rpc_sw_ctx, "do NOTHING");
        break;
      default:
        LOG(ERROR) << "unknown replica mark: " << res;
        break;
    }

    if (blk_report_ctx->type ==
        cloudfs::datanode::BlockReportCommandProto_Type_FAST) {
      ProcessBlockCheckDuringFastReport(s.get(), block.id, dn);
    }
    RPC_SW_CTX_LOG(rpc_sw_ctx, "do ProcessBlockCheckDuringFastReport");
  }

  dn->AddBlocks(report.storage(), to_add);

  CommitStorageClassReports(dn->uuid(), replica_report, true);

  if (storage_report_ctx->slice_thread_count.fetch_sub(1) > 1) {
    // not last slice of this storage
    return;
  }

  // only when all slices of current storage are processed successfully,
  // can we calculate the accurate failed storages and not reported blocks.
  if (done->done->status().IsOK()) {
    bool is_last = (report_idx == request->reports_size() - 1) &&
                   (blk_report_ctx->cur_rpc >= blk_report_ctx->total_rpc - 1);
    auto failed_storages = dn->UpdateBlockReportStatus(
        report.storage().storageuuid(),
        request->context(),
        is_last);
    std::vector<uint64_t> block_ids;
    for (auto& storage_uuid : failed_storages) {
      std::vector<uint64_t> blks = dn->MarkStorageAsFailed(storage_uuid);
      LOG(INFO) << "Remove failed storage: " << storage_uuid << " : "
                << dn->ip().ToString();
      block_ids.insert(block_ids.end(), blks.begin(), blks.end());
    }
    RemoveBlocksAssociatedTo(dn->id(), block_ids);

    for (auto& blks : storage_report_ctx->blocks_decoded) {
      for (auto& blk : blks) {
        storage_report_ctx->all_reported_blks.push_back(blk.id);
      }
    }

    std::vector<std::vector<BlkInfo>>().swap(storage_report_ctx->blocks_decoded);
  }

  // all slices of current storage processed, check if all other storages done.
  if (done->storage_count.fetch_sub(1) == 1) {
    if (blk_report_ctx->cur_rpc >= blk_report_ctx->total_rpc - 1) {
      LOG(INFO) << "Finishing FBR for dn " << dn_id;
      if (blk_report_ctx->type ==
          cloudfs::datanode::BlockReportCommandProto_Type_NORMAL) {
        block_report_manager_->UpdateBlockReportRecord(dn_id, true, false);
      } else {
        block_report_manager_->UpdateBlockReportRecord(dn_id, true, true);
      }
      block_report_manager_->FinishFullBlockReport(dn_id);
    }

    LOG(INFO) << "Finish process FBR from dn " << dn_id << ", "
              << blk_report_ctx->ToString();

    dn->UnlockForBlockReport();
    done->done->Run();
  }
}

void BlockManager::FinishStorageReport(
    DatanodeID dn_id,
    DatanodeInfoPtr dn,
    std::shared_ptr<BlockReportContext> blk_report_ctx,
    std::shared_ptr<StorageReportContext> storage_report_ctx,
    std::atomic<bool>* running) {
  auto& blks = storage_report_ctx->all_reported_blks;
  std::sort(blks.begin(), blks.end());

  std::vector<BlockID> not_reported;
  int not_reported_count = 0;

  for (auto blk : storage_report_ctx->snapshot) {
    if (*running == false) {
      LOG(WARNING) << "FinishStorageReport aborted";
      break;
    }

    if (storage_report_ctx->delta_blks.find(blk) !=
        storage_report_ctx->delta_blks.end()) {
      continue;
    }

    if (!std::binary_search(blks.begin(), blks.end(), blk)) {
      not_reported.emplace_back(blk);
    }

    if (not_reported.size() >= FLAGS_block_diff_batch_size) {
      BlockDiffRemoveBlocks(dn_id, dn, storage_report_ctx, not_reported);
      not_reported_count += not_reported.size();
      std::vector<BlockID>().swap(not_reported);
    }
  }

  {
    BlockDiffRemoveBlocks(dn_id, dn, storage_report_ctx, not_reported);
    not_reported_count += not_reported.size();
    std::vector<BlockID>().swap(not_reported);
  }

  LOG(INFO) << "FinishStorageReport received " << blks.size()
            << " blocks, snapshot size " << storage_report_ctx->snapshot.size()
            << ", delta " << storage_report_ctx->delta_blks.size()
            << ", will remove " << not_reported_count << " blocks from dn "
            << dn_id;
}

void BlockManager::BlockDiffRemoveBlocks(
    DatanodeID dn_id,
    DatanodeInfoPtr dn,
    std::shared_ptr<StorageReportContext> storage_report_ctx,
    const std::vector<BlockID>& not_reported) {
  std::stringstream log_ss;
  if (VLOG_IS_ON(8)) {
    log_ss << "RemoveBlocks for dn " << dn_id << " ";
  }

  std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
  for (auto& b : not_reported) {
    auto& s = slice(b);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RemoveStorageInternal(b, dn_id);

    StorageClassReportProto scr;
    scr.set_stcls(StorageClassProto::NONE);
    replica_report.emplace_back(b, scr);

    if (VLOG_IS_ON(8)) {
      log_ss << b << ",";
    }
  }

  dn->RemoveBlocks(storage_report_ctx->storage, not_reported);
  CommitStorageClassReports(dn->uuid(), replica_report, true);

  VLOG(8) << log_ss.str();
  return;
}

Status BlockManager::ReportBadBlocks(const RepeatedLocatedBlock& blocks) {
  auto status = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (status.first.HasException()) {
    return status.first;
  }
  for (auto& block : blocks) {
    auto dns = block.locs();
    auto b = block.b();
    auto& s = slice(b.blockid());
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), b.blockid(), false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      LOG(WARNING) << "Report bad Blocks, B" << b.blockid() << " not found";
      continue;
    }
    LOG(INFO) << "Report bad Blocks, B" << b.blockid();
    for (auto& dn : dns) {
      auto dn_id =
          datanode_manager_->GetDatanodeInterId(dn.id().datanodeuuid());
      if (dn_id == kInvalidDatanodeID) {
        LOG(WARNING) << "Report bad Blocks, B" << b.blockid()
                     << ", dn: " << dn.id().ipaddr() << " does not exist";
        continue;
      }

      Block block = Block(b.blockid(), b.numbytes(), b.generationstamp());
      s->AddToCorruptBlock(b.blockid(), dn_id, block);
      ProcessCorruptReplica(dn_id, block, "ReportBadBlocks");
    }
  }
  return Status();
}

Status BlockManager::IncrementalBlockReport(
    const std::string& dn_uuid,
    const RepeatedIncBlockReport& report) noexcept {
  StopWatch ibr_sw(metrics_.ibr_total_time_);
  ibr_sw.Start();
  struct {
    int evicted = 0;
    int sealed = 0;
    int deleted = 0;
    int received = 0;
    int receiving = 0;
    int upload_id_negoed = 0;
    int upload_succeed = 0;
    int upload_failed = 0;
    int pufs_deleted = 0;
    int storage_class_reported = 0;
    int load_failed = 0;
    int merged = 0;
    int others = 0;
  } blk_stat;
  DEFER([&]() {
    ibr_sw.NextStep();
    if (VLOG_IS_ON(8)) {
      auto t = std::chrono::duration_cast<std::chrono::milliseconds>(
                   ibr_sw.GetTime())
                   .count();
      if (t >= 30 || FLAGS_run_ut) {
        VLOG(8) << "Incremental block report spent " << t << "ms"
                << ", evicted: " << blk_stat.evicted
                << ", sealed: " << blk_stat.sealed
                << ", deleted: " << blk_stat.deleted
                << ", received: " << blk_stat.received
                << ", receiving: " << blk_stat.receiving
                << ", upload_id_negoed: " << blk_stat.upload_id_negoed
                << ", upload_succeed: " << blk_stat.upload_succeed
                << ", upload_failed: " << blk_stat.upload_failed
                << ", load_failed: " << blk_stat.load_failed
                << ", pufs_delete: " << blk_stat.pufs_deleted
                << ", storage_class_reported: " << blk_stat.storage_class_reported
                << ", merged: " << blk_stat.merged
                << ", others: " << blk_stat.others;
      }
    }
  });

  if (ha_state_->GetHAMode() == EditLogConf::StandbyNonHA) {
    return Status(JavaExceptions::kNoException, "StandbyNonHA");
  }

  auto dn_id = datanode_manager_->GetDatanodeInterId(dn_uuid);
  if (dn_id == kInvalidDatanodeID) {
    return Status{
        JavaExceptions::kIOException,
        "Incremental block report from unregistered node: " + dn_uuid};
  }
  auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
  if (dn == nullptr || !dn->CheckAndUpdateHeartbeat()) {
    return Status{JavaExceptions::kIOException,
                  "Incremental block report from dead node: " + dn_uuid};
  }

  dn->LockForBlockReport();

  using cloudfs::datanode::ReceivedDeletedBlockInfoProto;
  uint64_t block_num = 0;

  for (const auto& storage : report) {
    StopWatch storage_sw(metrics_.ibr_storage_process_time_);
    storage_sw.Start();
    std::vector<BlockID> to_add;
    std::vector<BlockID> to_remove;

    block_num += storage.blocks().size();

    std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
    for (const auto& block : storage.blocks()) {
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      if (block.status() == ReceivedDeletedBlockInfoProto::DELETED ||
          block.status() == ReceivedDeletedBlockInfoProto::RECEIVED ||
          block.status() == ReceivedDeletedBlockInfoProto::REPORT_STORAGE_CLASS) {
        if (block.status() != ReceivedDeletedBlockInfoProto::DELETED &&
            block.has_storageclass()) {
          scr.set_stcls(block.storageclass());
          // other case, remove the previous report by passing NONE
        }
        if (block.has_pinned()) {
          scr.set_pinned(block.pinned());
        }
        replica_report.emplace_back(block.block().blockid(), scr);
      }

      ReplicaStateProto state;
      StopWatch blk_sw(metrics_.ibr_lock_acquire_time_);
      blk_sw.Start();

      RPC_SW_CTX_INIT(rpc_sw_ctx,
                      "[IBR]",
                      absl::StrFormat("block_id=%d", block.block().blockid()));
      RPC_SW_CTX_LOG(rpc_sw_ctx, "start");

      Block b{block.block().blockid(),
              static_cast<uint32_t>(block.block().numbytes()),
              block.block().genstamp()};
      auto& s = slice(block.block().blockid());
      std::unique_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      RPC_SW_CTX_LOG(rpc_sw_ctx, "add lock");

      if (ShouldPostponeBlocksFromFutureV2(block.block().blockid(),
                                           block.block().genstamp())) {
        if (block.status() != ReceivedDeletedBlockInfoProto::DELETED) {
          VLOG(8) << "Postpone block from future: " << b.ToString();
          state = (block.status() == ReceivedDeletedBlockInfoProto::RECEIVED)
                      ? ReplicaStateProto::FINALIZED
                      : ReplicaStateProto::RBW;
          s->EnqueuePendingFutureBlks(storage.storage(), b, dn_id, state);
        }
        continue;
      }
      RPC_SW_CTX_LOG(rpc_sw_ctx, "should postpone");

      ReportedReplicaResult res = NOTHING;
      std::string report_type{"unknown_rep"};
      DatanodeID del_node_hint = kInvalidDatanodeID;

      BlockInfoGuard bi_guard(s.get(), block.block().blockid(), false);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      uint8_t live_rep = 0;
      if (bi == nullptr) {
        VLOG(10) << "Failed to get block info b" << block.block().blockid()
                 << " dn " << dn->hostname();
      } else {
        live_rep = CountReplica(bi).live;
      }
      RPC_SW_CTX_LOG(rpc_sw_ctx, "count replica");

      switch (block.status()) {
        case ReceivedDeletedBlockInfoProto::EVICT_BLOCK:
          blk_stat.evicted++;
          MFC(metrics_.ibr_evicted_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_evicted_blk_process_time_);
          VLOG_OR_IF(8, FLAGS_log_evict_delete_file || FLAGS_log_dn_all_ibr)
              << "ReceivedDeletedBlockInfoProto evict blk: blk=" << b.ToString()
              << " dn_id=" << dn_id;

          res = NOTHING;

          do {
            if (!FLAGS_enable_ufs_evict_write_cache) {
              break;
            }
            if (!FLAGS_enable_ufs_evict_write_cache_delete_file) {
              break;
            }
            if (!ha_state_->IsActive() || ha_state_->InTransition()) {
              break;
            }

            auto idx = dn_id % FLAGS_blk_delete_file_by_dn_evict_thread_count;
            auto ret = bg_evict_deleter_worker_[idx]->AddTask([=]() -> bool {
              ns_->DeleteFileByDnEvict(dn_id, dn->ip().ToString(), block);
              return true;
            });
            if (!ret) {
              LOG(INFO) << "evict blk overflow." << " dn_id=" << dn_id
                        << " dn_ip=" << dn->ip().ToString()
                        << " block_id=" << block.block().blockid();
            }
          } while (0);

          report_type = "evict_rep";
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process EVICT_BLOCK");
          break;
        case ReceivedDeletedBlockInfoProto::SEALED:  // deprecated
          blk_stat.sealed++;
          MFC(metrics_.ibr_sealed_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_sealed_blk_process_time_);
          VLOG_OR_IF(10, FLAGS_log_dn_all_ibr)
              << "ReceivedDeletedBlockInfoProto sealed blk: blk="
              << b.ToString() << " dn_id=" << dn_id;

          res = s->ProcessReportedBlock(dn_id,
                                        b,
                                        ReplicaStateProto::SEALED,
                                        is_active_,
                                        dn->IsDecommissionInProgress(),
                                        live_rep);
          // job_manager_->TrackBlockTask(block.block().blockid(),
          //                              BlockStatus::RECEIVED);
          state = ReplicaStateProto::SEALED;
          report_type = "sealed_rep";
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process SEALED");
          break;
        case ReceivedDeletedBlockInfoProto::DELETED:
          blk_stat.deleted++;
          MFC(metrics_.ibr_deleted_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_deleted_blk_process_time_);
          VLOG_OR_IF(10, FLAGS_log_dn_all_ibr)
              << "ReceivedDeletedBlockInfoProto: blk=" << b.ToString()
              << " dn_id=" << dn_id;

          to_remove.push_back(block.block().blockid());
          RemoveStorageInternal(block.block().blockid(), dn_id);

          job_manager_->TrackBlockTask(block.block().blockid(),
                                       BlockStatus::DELETED);
          report_type = "del_rep";
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process DELETED");
          break;
        case ReceivedDeletedBlockInfoProto::RECEIVED:
          blk_stat.received++;
          MFC(metrics_.ibr_received_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_received_blk_process_time_);
          VLOG_OR_IF(10, FLAGS_log_dn_all_ibr)
              << "ReceivedDeletedBlockInfoProto::RECEIVED blk=" << b.ToString()
              << " dn_id=" << dn_id;
          res = s->ProcessReportedBlock(dn_id,
                                        b,
                                        ReplicaStateProto::FINALIZED,
                                        is_active_,
                                        dn->IsDecommissionInProgress(),
                                        live_rep);
          job_manager_->TrackBlockTask(block.block().blockid(),
                                       BlockStatus::RECEIVED);
          if (block.has_deletehint()) {
            del_node_hint =
                datanode_manager_->GetDatanodeInterId(block.deletehint());
          }
          state = ReplicaStateProto::FINALIZED;
          report_type = "recvd_rep";
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process RECEIVED");
          break;
        case ReceivedDeletedBlockInfoProto::RECEIVING:
          blk_stat.receiving++;
          MFC(metrics_.ibr_receiving_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_receiving_blk_process_time_);
          VLOG_OR_IF(10, FLAGS_log_dn_all_ibr)
              << "ReceivedDeletedBlockInfoProto::RECEIVING blk=" << b.ToString()
              << " dn_id=" << dn_id;
          res = s->ProcessReportedBlock(dn_id,
                                        b,
                                        ReplicaStateProto::RBW,
                                        is_active_,
                                        dn->IsDecommissionInProgress(),
                                        live_rep);
          state = ReplicaStateProto::RBW;
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process RECEIVING");
          break;
        case ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED:
          // Release block map slice lock is safe when res == NOTHING.
          guard.unlock();
          blk_stat.upload_id_negoed++;
          MFC(metrics_.ibr_upload_id_negoed_ops_)->Inc();
          if (FLAGS_log_dn_negoed_ibr) {
            LOG(INFO) << "IBR::UPLOAD_ID_NEGOED from dn_id=" << dn_id
                      << " ip=" << dn->ip().ToString();
          }
          if (FLAGS_skip_dn_negoed_ibr) {
            VLOG(10) << "skip IBR::UPLOAD_ID_NEGOED from dn_id=" << dn_id
                     << " ip=" << dn->ip().ToString();
            break;
          }
          blk_sw.NextStep(metrics_.ibr_upload_id_negoed_blk_process_time_);

          // If open reuse last block, UPLOAD_ID_NEGOED is a MUST action.
          // UPLOAD_ID_NEGOED acts as a sealed marker.
          // NN promises no one can change it anymore.
          // TODO(ruanjunbin): safemode?
          ProcessUploadIdNegoedBlock(block.block(),
                                     dn_id,
                                     dn->uuid(),
                                     block.uploadid(),
                                     block.pufsname());

          RPC_SW_CTX_LOG(rpc_sw_ctx, "process UPLOAD_ID_NEGOED");
          break;
        case ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED:
          // Release block map slice lock is safe when res == NOTHING.
          guard.unlock();
          blk_stat.upload_succeed++;
          MFC(metrics_.ibr_upload_succeed_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_upload_succeed_blk_process_time_);

          ProcessUploadBlockSucceed(block.block(),
                                    dn_id,
                                    block.uploadid(),
                                    block.pufsname(),
                                    block.has_etag() ? block.etag() : "");
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process UPLOAD_SUCCEED");
          break;
        case ReceivedDeletedBlockInfoProto::UPLOAD_FAILED:
          // Release block map slice lock is safe when res == NOTHING.
          guard.unlock();
          blk_stat.upload_failed++;
          MFC(metrics_.ibr_upload_failed_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_upload_failed_blk_process_time_);

          ProcessUploadBlockFailed(block.block(),
                                   dn_id,
                                   block.uploadid(),
                                   block.pufsname(),
                                   block.has_etag() ? block.etag() : "",
                                   block.failed_msg());
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process UPLOAD_FAILED");
          break;
        case ReceivedDeletedBlockInfoProto::PUFS_DELETED:
          // The initial value of res is NOTHING.
          CHECK_EQ(res, NOTHING);
          blk_stat.pufs_deleted++;
          MFC(metrics_.ibr_pufs_deleted_ops_)->Inc();
          blk_sw.NextStep(metrics_.ibr_pufs_deleted_blk_process_time_);

          if (depred_block_recycler_) {
            depred_block_recycler_->AddToOpDelDepredBlksQueue(
                block.block().blockid());
          }

          report_type = "recving_rep";
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process PUFS_DELETED");
          break;
        case ReceivedDeletedBlockInfoProto::REPORT_STORAGE_CLASS:
          blk_stat.storage_class_reported++;
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process REPORT_STORAGE_CLASS");
          break;
        case ReceivedDeletedBlockInfoProto::MERGED:
          guard.unlock();
          blk_stat.merged++;

          ProcessMergedBlock(block.block(),
                             dn_id,
                             storage.storage(),
                             block.has_pufsname() ? block.pufsname() : "");

          RPC_SW_CTX_LOG(rpc_sw_ctx, "process MERGED");
          break;
        case ReceivedDeletedBlockInfoProto::LOAD_FAILED:
          // Not handle because LOAD_FAILED reported by datanode is a retryable
          // exception job_manager_->TrackBlockTask(block.block().blockid(),
          // BlockStatus::FAILED);
          MFC(metrics_.ibr_load_failed_ops_)->Inc();
          LOG(INFO) << "ReceivedDeletedBlockInfoProto load failed blk: blk="
                  << b.ToString() << " dn_id=" << dn_id;
          blk_stat.load_failed++;
          break;
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process LOAD_FAILED");
        default:
          blk_stat.others++;
          LOG(WARNING) << "Inc block report B" << block.block().blockid()
                       << " from DN" << dn_id
                       << " with unknown type: " << block.status();
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process default");
      }

      if (is_active_ && FLAGS_block_lifecycle_enable) {
        BlockLifecycleLogger::GetSingleton().LogIncrementalBlockReport(
            b.id, dn->ip().ToString(), b.gs, report_type, b.num_bytes, state);
      }
      RPC_SW_CTX_LOG(rpc_sw_ctx, "process block_lifecycle");

      switch (res) {
        case ADD:
          ReplicaNum rn;
          if (AddStoredBlock(dn_id, b, &rn, del_node_hint)) {
            to_add.emplace_back(b.id);
          }
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process do ADD");
          break;
        case INVALIDATE:
          // for active, not need to record misinvalidated block
          if (!is_active_) {
            VLOG(10) << "IncrementalBlockReport misinvalidate block: "
                     << b.ToString() << ", state: " << state;
            s->EnqueuePendingMisinvalidatedBlks(
                ReportedBlockInfo{b,
                                  dn_id,
                                  storage.storage(),
                                  state,
                                  std::chrono::steady_clock::now()});
          }
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process do INVALIDATE");
          break;
        case CORRUPT:
          if (IsPopulatingReplicationQueues()) {
            ProcessCorruptReplica(dn_id, b, "IncrementalBlockReport");
          }
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process do CORRUPT");
          break;
        case ADD_TO_BLOCK_INDEX:
          to_add.emplace_back(b.id);
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process do ADD_TO_BLOCK_INDEX");
          break;
        case NOTHING:
          RPC_SW_CTX_LOG(rpc_sw_ctx, "process do NOTHING");
          break;
        default:
          LOG(ERROR) << "Unknown reported replica mark: " << res;
      }
      blk_sw.NextStep();
    }
    // TODO(ranpanfeng)
    // AddBlock should be executed in ProcessReportResult to dealing with
    // 3 situations: ADDED, REPLACED and ALREADY_EXIST
    storage_sw.NextStep(metrics_.ibr_block_index_update_time_);
    datanode_manager_->AddBlocks(dn_id, storage.storage(), to_add);
    datanode_manager_->RemoveBlocks(dn_id, storage.storage(), to_remove);
    storage_sw.NextStep();

    CommitStorageClassReports(dn->uuid(), replica_report, true);
    // Remove blockInfo cache to prevent inconsistencies between metastorage and
    // in-memory blockInfo.
    if (FLAGS_ibr_deleted_replica_disable_cached_block_info) {
      for (auto& blk_id : to_remove) {
        RemoveCachedBlockInfo(blk_id);
      }
    }

    if (dn->IsDoingFBR()) {
      block_report_manager_->AddDeltaBlocks(dn_id, storage.storageuuid(), to_add);
      block_report_manager_->AddDeltaBlocks(dn_id, storage.storageuuid(), to_remove);
    }
  }

  dn->UnlockForBlockReport();

  return Status();
}

Status BlockManager::AnalyzeFileBlocks(const Block& previous,
                                       const Block& stored_last,
                                       const Block& stored_penultimate,
                                       uint32_t preferred_block_size,
                                       std::vector<DatanodeID>* targets) {
  auto stored_last_s = slice(stored_last.id).get();
  auto stored_penultimate_s = slice(stored_penultimate.id).get();

  std::function<void()> release_lock = []() {};
  DEFER([&]() { release_lock(); });

  // Lock slice in order
  if (stored_last_s == stored_penultimate_s) {
    stored_last_s->lock_shared();
    stored_last_s->TellLockHolder(__FILE__, __LINE__);
    release_lock = [stored_last_s]() { stored_last_s->unlock_shared(); };
  } else {
    if (stored_last.id < stored_penultimate.id) {
      stored_last_s->lock_shared();
      stored_last_s->TellLockHolder(__FILE__, __LINE__);
      stored_penultimate_s->lock_shared();
      stored_penultimate_s->TellLockHolder(__FILE__, __LINE__);
      release_lock = [stored_last_s, stored_penultimate_s]() {
        stored_penultimate_s->unlock_shared();
        stored_last_s->unlock_shared();
      };
    } else {
      stored_penultimate_s->lock_shared();
      stored_penultimate_s->TellLockHolder(__FILE__, __LINE__);
      stored_last_s->lock_shared();
      stored_last_s->TellLockHolder(__FILE__, __LINE__);
      release_lock = [stored_last_s, stored_penultimate_s]() {
        stored_last_s->unlock_shared();
        stored_penultimate_s->unlock_shared();
      };
    }
  }

  BlockInfoGuard stored_penultimate_bi_guard(
      stored_penultimate_s, stored_penultimate.id, true);
  BlockInfo* stored_penultimate_bi = stored_penultimate_bi_guard.GetBlockInfo();
  BlockInfoGuard stored_last_bi_guard(stored_last_s, stored_last.id, true);
  BlockInfo* stored_last_bi = stored_last_bi_guard.GetBlockInfo();
  if (previous.gs != stored_last.gs || previous.id != stored_last.id) {
    // 1) This is an append request on a exactly full block. Or
    //    Add->Abandon->Add and client lost previous block after abandon, we need
    //    to wait for last block to be complete.
    // 2) This is a retry due to rpc timeout or HA fail over. Client should not
    //    have writen to this block yet.
    // 3) Something went wrong, this should not happen.

    if (previous.id == 0 && stored_last_bi &&
        (stored_last_bi->num_bytes() >= preferred_block_size ||
         FLAGS_append_reuse_last_block == false) &&
        stored_last_bi->HasBeenCommitted()) {
      // Case 1
      if (stored_last_bi->ReadyToComplete()) {
        VLOG(8) << "Handling block allocation to a complete previous block B"
                << stored_last;
      } else {
        VLOG(8) << stored_last_bi->ToString() << " is not ready to complete";
        return Status(JavaExceptions::Exception::kNotReplicatedYetException);
      }
    } else if (previous.id == stored_penultimate.id &&
               previous.gs == stored_penultimate.gs) {
      if (stored_last_bi->num_bytes() != 0) {
        LOG(WARNING) << "Retry add block on a non-empty block " << stored_last;
        return Status(
            JavaExceptions::Exception::kIOException,
            "Add block on a non-empty block: " + stored_last.ToString());
      }
      // Case 2
      *targets = stored_last_s->GetExpectedLocations(stored_last_bi->id());
      LOG(INFO) << "Retry addblock: B" << stored_last_bi->id();
      return Status(Code::kIsRetry);
    } else {
      // Case 3
      LOG(WARNING) << "Reported previous block " << previous
                   << " does not match stored last " << stored_last
                   << " or penultimate " << stored_penultimate;
      return Status(
          JavaExceptions::Exception::kIOException,
          "Reported previous block: " + previous.ToString() +
              " does not match stored last block: " + stored_last.ToString() +
              " or penultimate block: " + stored_penultimate.ToString());
    }
  }
  if (stored_last_bi &&
      stored_last_bi->uc_state() == BlockUCState::kUnderRecovery) {
    // Refer to HDFS-10240.
    // https://github.com/apache/hadoop/blob/7a3bc90b05f257c8ace2f76d74264906f0f7a932/hadoop-hdfs-project/hadoop-hdfs/src/main/java/org/apache/hadoop/hdfs/server/blockmanagement/BlockManager.java#L1097
    return Status(JavaExceptions::Exception::kIOException,
                  "The last block is under recovery");
  }
  if (stored_penultimate_bi && !stored_penultimate_bi->ReadyToComplete()) {
    VLOG(8) << stored_penultimate_bi->ToString() << " is not ready to complete";
    return Status(JavaExceptions::Exception::kNotReplicatedYetException);
  }

  return Status();
}

Status BlockManager::AnalyzeFileBlocksToCommit(const Block& previous,
                                               const Block& stored_last) {
  auto& s = slice(stored_last.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);

  auto stored_last_bi = s->Locate(stored_last.id);
  if (stored_last_bi == nullptr) {
    std::string msg = absl::StrFormat(
        "Reported previous block: %s, block not found", previous.ToString());

    LOG(WARNING) << msg;
    return Status(JavaExceptions::Exception::kIOException, msg);
  }

  if (previous.gs != kBlockProtocolV2GenerationStamp) {
    std::string msg = absl::StrFormat(
        "Reported previous block: %s, gs != kBlockProtocolV2GenerationStamp",
        previous.ToString());

    LOG(WARNING) << msg;
    return Status(JavaExceptions::Exception::kIOException, msg);
  }

  if (previous.gs != stored_last.gs || previous.id != stored_last.id) {
    std::string msg = absl::StrFormat(
        "Reported previous block %s  does not match stored last %s",
        previous.ToString(),
        stored_last.ToString());

    LOG(WARNING) << msg;
    return Status(JavaExceptions::Exception::kIOException, msg);
  }

  if (stored_last_bi->HasBeenCommitted()) {
    std::string msg = absl::StrFormat(
        "Reported previous block: %s has been committed, stored_last: %s",
        previous.ToString(),
        stored_last.ToString());

    LOG(WARNING) << msg;
    return Status(JavaExceptions::Exception::kIOException, msg);
  }
  return Status();
}

Status BlockManager::UpdatePipeline(const ExtendedBlockProto& new_block,
                                    const RepeatedDatanode& new_nodes) {
  std::vector<DatanodeID> dns;
  for (auto it = new_nodes.begin(); it != new_nodes.end(); it++) {
    auto dn_id = datanode_manager_->GetDatanodeInterId(it->datanodeuuid());
    if (dn_id == kInvalidDatanodeID) {
      LOG(INFO) << "UpdatePipeline cannot find datanode" << it->datanodeuuid();
      return Status(JavaExceptions::kIOException,
                    "Datanode " + it->datanodeuuid() + " not found");
    }
    dns.push_back(dn_id);
  }

  DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(new_block.blockid(),
                                    "\"blk_id\":%" PRIu64
                                    ","
                                    "\"op\":\"update_pipeline\","
                                    "\"gs\":%" PRIu64
                                    ","
                                    "\"numbytes\":%d,"
                                    "\"dns\":\"%s\"}",
                                    new_block.blockid(),
                                    new_block.generationstamp(),
                                    new_block.numbytes(),
                                    GetDNIPStr(dns).c_str());

  auto& s = slice(new_block.blockid());
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  return s->UpdatePipeline(new_block, dns);
}

void BlockManager::GetCommands(
    DatanodeID dn_id,
    const std::string& bpid,
    int32_t transfer_inprogress,
    cloudfs::datanode::HeartbeatResponseProto* response,
    bool has_version) {
  // We've got the barrier lock which indicates we are active now
  if (safemode_->IsOn()) {
    return;
  }

  StopWatch sw_total(metrics_.dn_cmd_time_total_);
  sw_total.Start();

  auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
  std::string dn_ip = dn->ip().ToString();
  int32_t recover_cmd_num = 0;
  int32_t replicate_cmd_num = 0;
  int32_t invalidate_cmd_num = 0;
  int32_t upload_cmd_num = 0;
  int32_t ne_cmd_num = 0;
  int32_t load_cmd_num = 0;
  int32_t merge_cmd_num = 0;
  int32_t truncatable_cmd_num = 0;
  int32_t block_id_cmd_num = 0;

  StopWatch sw;
  sw.Start();
  // 1st, get recover commands
  sw.NextStep(metrics_.dn_cmd_time_block_recover_);
  std::unordered_map<uint64_t, bool> to_recover;
  {
    std::unique_lock<RWSpinlock> lock(rwlock_);
    auto it = to_recover_.find(dn_id);
    if (it != to_recover_.end()) {
      std::swap(it->second, to_recover);
    }
  }
  if (!to_recover.empty()) {
    auto recover_cmd = response->add_cmds();
    recover_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand);
    auto block_cmd = recover_cmd->mutable_recoverycmd();
    for (const auto& pair : to_recover) {
      auto blk_id = pair.first;
      auto close_file = pair.second;
      std::vector<DatanodeID> storages;
      RecoveringBlockProto recovering_blk;
      auto& s = slice(blk_id);
      std::shared_lock<BlockMapSlice> guard(*s);
      if (!s->GetRecoverCommand(blk_id, bpid, &storages, &recovering_blk)) {
        LOG(WARNING) << "GetRecoverCommand failed, dn: " << dn_ip
                     << ", blk: " << blk_id;
        continue;
      }
      recovering_blk.set_closefile(close_file);
      std::vector<DatanodeID> no_stale;
      for (auto recovery_dn_id : storages) {
        if (!datanode_manager_->IsDatanodeStale(recovery_dn_id)) {
          no_stale.emplace_back(recovery_dn_id);
        }
      }
      auto& recover_locations = storages;
      if (no_stale.size() > 1) {
        if (no_stale.size() != storages.size()) {
          LOG(INFO) << "Recover block cmd, B" << blk_id
                    << " storage size: " << storages.size()
                    << ", stale dn size: "
                    << (storages.size() - no_stale.size());
        }
        recover_locations = no_stale;
      }
      if (!recover_locations.empty()) {
        BlockInfoGuard bi_guard(s.get(), blk_id, true);
        BlockInfo* bi = bi_guard.GetBlockInfo();
        StoragePolicyId storage_policy_id;
        if (bi && GetStoragePolicy(bi, &storage_policy_id)) {
          datanode_manager_->ConstructLocatedBlock(
              recover_locations,
              storage_policy_id,
              recovering_blk.mutable_block());
          block_cmd->add_blocks()->CopyFrom(recovering_blk);
        }
      }
    }
    recover_cmd_num = block_cmd->blocks_size();
    MFC(metrics_.dn_cmd_cnt_block_recovery_)->Inc(recover_cmd_num);
    VLOG(8) << "Get recover block cmd, num: " << recover_cmd_num
            << ", dn: " << dn_ip;
  }
  // 2nd, get replication commands
  sw.NextStep(metrics_.dn_cmd_time_block_transfer_);
  {
    auto cow = std::deque<BlockTargetPair>();
    {
      std::unique_lock<RWSpinlock> lock(rwlock_);
      auto it = dn_replication_queues_.find(dn_id);
      if (it != dn_replication_queues_.end()) {
        it->second.swap(cow);
        dn_replication_queues_.erase(it);
      }
    }

    auto max_transfer =
        FLAGS_blockmap_max_replication_streams - transfer_inprogress;
    auto replication_cmd = response->add_cmds();
    replication_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::BlockCommand);
    auto block_cmd = replication_cmd->mutable_blkcmd();
    block_cmd->set_action(BlockCommandProto::TRANSFER);
    block_cmd->set_blockpoolid(bpid);
    while (max_transfer-- > 0 && !cow.empty()) {
      auto pair = cow.front();
      DEFER([&cow]() { cow.pop_front(); });
      auto& s = slice(pair.blk.id);
      std::shared_lock<BlockMapSlice> guard(*s);
      BlockInfoGuard bi_guard(s.get(), pair.blk.id, true);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      StoragePolicyId storage_policy_id;
      if (!bi || !GetStoragePolicy(bi, &storage_policy_id)) {
        LOG(WARNING) << "Replication block cmd, block: " << pair.blk.id
                     << " not exist or inode does not exist";
        continue;
      }
      auto blk = block_cmd->add_blocks();
      blk->set_blockid(pair.blk.id);
      blk->set_numbytes(pair.blk.num_bytes);
      blk->set_genstamp(pair.blk.gs);

      VLOG(10) << "Replication block cmd, dn: " << dn_ip
               << ", blk: " << pair.blk.id;

      datanode_manager_->ConstructBlockCommand(
          pair.targets, storage_policy_id, block_cmd);
      block_cmd->add_priorities(pair.io_priority);

      if (is_active_ && FLAGS_block_lifecycle_enable) {
        // Log transfer command.
        for (auto target : block_cmd->targets()) {
          for (auto dn : target.datanodes()) {
            std::string target_dn_ip = dn.id().ipaddr();
            BlockLifecycleLogger::GetSingleton().LogTransferCommand(
                pair.blk.id, pair.blk.gs, dn_ip, target_dn_ip);
          }
        }
      }
    }
    replicate_cmd_num = block_cmd->blocks_size();
    {
      std::unique_lock<RWSpinlock> lock(rwlock_);
      auto it = dn_replication_queues_.find(dn_id);
      if (it != dn_replication_queues_.end()) {
        while (!cow.empty()) {
          it->second.push_front(cow.back());
          cow.pop_back();
        }
        if (it->second.empty()) {
          dn_replication_queues_.erase(it);
        }
      } else {
        if (!cow.empty()) {
          dn_replication_queues_[dn_id] = std::move(cow);
        }
      }
    }
    auto cmd = replication_cmd->blkcmd();

    MFC(metrics_.dn_cmd_cnt_block_transfer_)->Inc(replicate_cmd_num);
    VLOG_IF(8, replicate_cmd_num > 0)
        << "Get replication block cmd, num: " << replicate_cmd_num
        << ", dn: " << dn_ip;
  }

  // 3rd, get invalidation commands
  sw.NextStep(metrics_.dn_cmd_time_block_invalidate_);
  {
    auto invalidate_cmd = response->add_cmds();
    invalidate_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::BlockCommand);
    auto block_cmd = invalidate_cmd->mutable_blkcmd();
    block_cmd->set_action(BlockCommandProto::INVALIDATE);
    block_cmd->set_blockpoolid(bpid);
    uint32_t num = dn->PopInvalidateBlock(block_cmd);
    invalidate_blk_count_.fetch_sub(num);
    invalidate_cmd_num = invalidate_cmd->blkcmd().blocks_size();
    MFC(metrics_.dn_cmd_cnt_block_invalidate_)->Inc(invalidate_cmd_num);
    VLOG_IF(8, invalidate_cmd_num > 0)
        << "Get invalidation commands, num: " << invalidate_cmd_num
        << ", dn: " << dn_ip;
  }

  // 4th, get cache related commands
  sw.NextStep(metrics_.dn_cmd_time_lifecycle_);
  {
    // TODO(xuex) version control
    if (has_version && ns_->lifecycle_scanner()) {
      ns_->lifecycle_scanner()->GetStorageClassCmd(
          dn->uuid(), bpid, response, &metrics_);
    }
  }

  // 5th, ignore key related commands
  sw.NextStep(metrics_.dn_cmd_time_key_update_);
  {
    if (FLAGS_security_key_enable && ns_->key_manager() &&
        dn->need_key_update()) {
      auto key_update_cmd = response->add_cmds();
      key_update_cmd->set_cmdtype(
          cloudfs::datanode::DatanodeCommandProto::KeyUpdateCommand);
      auto key_cmd = key_update_cmd->mutable_keyupdatecmd();
      auto keys = key_cmd->mutable_keys();
      ns_->key_manager()->GetExportKeys(keys);
      dn->set_need_key_update(false);
      MFC(metrics_.dn_cmd_cnt_key_update_)->Inc(1);
    }
  }
  // 6th, get balancer bandwidth commands
  // 7th, get pufs commands
  {
    if (FLAGS_namespace_type != cloudfs::NamespaceType::LOCAL) {
      sw.NextStep(metrics_.dn_cmd_time_upload_);
      upload_cmd_num = upload_cmd_mgr_.Get(dn_id, response);
      MFC(metrics_.dn_cmd_cnt_upload_)->Inc(upload_cmd_num);

      sw.NextStep(metrics_.dn_cmd_time_notify_evictable_);
      ne_cmd_num = ne_cmd_mgr_.Get(dn_id, response);
      MFC(metrics_.dn_cmd_cnt_notify_evictable_)->Inc(ne_cmd_num);

      sw.NextStep(metrics_.dn_cmd_time_load_);
      load_cmd_num = load_cmd_mgr_.Get(dn_id, response);
      MFC(metrics_.dn_cmd_cnt_load_)->Inc(load_cmd_num);

      sw.NextStep(metrics_.dn_cmd_time_invalidate_pufs_);
      dn->GetInvalidatePufsCmds(response, &metrics_);
      depred_block_recycler_->GetInvalidatePufsCmds(response, &metrics_);
    }

    sw.NextStep(metrics_.dn_cmd_time_block_id_);
    block_id_cmd_num = blockid_cmd_mgr_.Get(dn_id, response, &metrics_);

    sw.NextStep(metrics_.dn_cmd_time_merge_);
    merge_cmd_num = merge_cmd_mgr_.Get(dn_id, response, &metrics_);
  }

  // 8th, get truncatable commands
  {
    sw.NextStep(metrics_.dn_cmd_time_truncatable_);
    cloudfs::datanode::BlockCommandProto cmd;

    uint32_t num = dn->PopTruncatableBlock(&cmd);
    if (num != 0) {
      auto truncatable_cmd = response->add_cmds();
      truncatable_cmd->set_cmdtype(
          cloudfs::datanode::DatanodeCommandProto::BlockCommand);
      auto block_cmd = truncatable_cmd->mutable_blkcmd();
      block_cmd->Swap(&cmd);
      block_cmd->set_action(BlockCommandProto::FINALIZED);
      block_cmd->set_blockpoolid(bpid);

      truncatable_blk_count_.fetch_sub(num);
      truncatable_cmd_num = truncatable_cmd->blkcmd().blocks_size();
      MFC(metrics_.dn_cmd_cnt_block_finalized_)->Inc(truncatable_cmd_num);
      VLOG_IF(8, truncatable_cmd_num > 0)
          << "Get truncatable commands, num: " << truncatable_cmd_num
          << ", dn: " << dn_ip;
    }
  }

  VLOG(8) << "Got heartbeat command for DN: " << dn_ip
          << ", recover cmd num: " << recover_cmd_num
          << ", replicate cmd num " << replicate_cmd_num
          << ", invalidate cmd num: " << invalidate_cmd_num
          << ", upload_cmd_num: " << upload_cmd_num
          << ", ne_cmd_num: " << ne_cmd_num
          << ", load_cmd_num: " << load_cmd_num
          << ", merge_cmd_num: " << merge_cmd_num
          << ", truncatable_cmd_num:" << truncatable_cmd_num
          << ", block_id_cmd_num: " << block_id_cmd_num;
  if (VLOG_IS_ON(10)) {
    VLOG(10) << response->ShortDebugString();
  }
}

void BlockManager::GetBlockReportCommand(
    DatanodeID dn_id,
    cloudfs::datanode::HeartbeatResponseProto* response) {
  int ret = block_report_manager_->GetBlockReportCommand(dn_id, response);
  if (ret > 0) {
    LOG(INFO) << "Get blockreport command for dn " << dn_id;
  }
}

bool BlockManager::IsPopulatingReplicationQueues() {
  CHECK_NOTNULL(ha_state_);
  CHECK_NOTNULL(safemode_);
  return ha_state_->ShouldPopulateReplicationQueues() && !safemode_->IsOn() &&
         is_active_ && !ha_state_->InTransition();
}

bool BlockManager::BlockHasBeenCommitted(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  auto bi = s->Locate(blk_id);
  if (!bi) {
    LOG(INFO) << "HasBeenCommitted cannot find B" << blk_id;
    return false;
  }
  return bi->HasBeenCommitted();
}

bool BlockManager::BlockHasBeenComplete(BlockID blk_id) {
  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  BlockInfoGuard bi_guard(s.get(), blk_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    LOG(INFO) << "HasBeenBlockComplete cannot find B" << blk_id;
    return false;
  }
  return bi->HasBeenComplete();
}

bool BlockManager::BlockReadyToComplete(BlockID blk_id) {
  // in hdfs protocol, block should be complete
  // in block protocol v2, block could be committed or complete

  auto& s = slice(blk_id);
  std::shared_lock<BlockMapSlice> guard(*s);
  auto bi = s->Locate(blk_id);
  if (!bi) {
    LOG(INFO) << "BlockReadyToComplete cannot find B" << blk_id;
    return false;
  }
  if (bi->gs() == kBlockProtocolV2GenerationStamp) {
    return bi->HasBeenCommitted();
  } else {
    return bi->HasBeenComplete();
  }
}

bool BlockManager::BlockNeedTransferUnsafe(const BlockInfo* bi) {
  CHECK_NOTNULL(bi);

  if (!FLAGS_block_manager_enable_transfer_blocks) {
    return false;
  }

  if (bi->uc_state() == BlockUCState::kPersisted) {
    if (!FLAGS_block_manager_allow_transfer_persisted_blocks) {
      return false;
    }

    INode inode;
    if (ns_->GetINode(bi->inode_id(), &inode)) {
      if (inode.has_uc()) {
        // write cache
        return true;
      } else {
        // read cache

        // TODO(xiong): if user set read_replication, DanceNN also needs to
        // transfer blocks to maintain the number of replicas.
        VLOG(10) << "Block " << bi->id() << " need transfer";
        return true;
      }
    }
    LOG(INFO) << "block: " << bi->id() << " inode does not exist.";
    return false;
  }

  return true;
}

BlockManager::MisReplicationResult BlockManager::ProcessMisReplicatedBlock(
    const Block& blk,
    const INode& inode) {
  if (safemode_->IsOn()) {
    return MisReplicationResult::kOK;
  }
  auto& s = slice(blk.id);
  std::shared_lock<BlockMapSlice> guard(*s);
  BlockInfoGuard bi_guard(s.get(), blk.id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi || !IsBlockIDValid(bi->blk().id)) {
    VLOG(8) << "ProcessMisReplicatedBlock no exist block " << blk.id;
    return MisReplicationResult::kInvalid;
  }
  // Scan standby corrupt blocks
  if (!ha_state_->IsActive() || ha_state_->InTransition()) {
    if (safemode_->IsOn() || !bi->HasBeenComplete()) {
      return MisReplicationResult::kOK;
    }
    auto rn = CountReplica(bi);
    if (IsNeededReplication(bi, &inode, inode.replication(), rn.live) &&
        needed_replications_.IsCorruptBlock(bi, rn.live, inode.replication())) {
      auto path = ns_->BuildFullPath(bi->inode_id());
      VLOG(11) << "Scan corrupt block, bi: " << bi->ToString()
               << ", path: " << path;
      return MisReplicationResult::kCorrupt;
    }
    return MisReplicationResult::kOK;
  } else {
    return ProcessMisReplicatedBlock(bi, inode);
  }
}

// NOTICE: slice read lock should already been held
BlockManager::MisReplicationResult BlockManager::ProcessMisReplicatedBlock(
    const BlockInfo* bi,
    const INode& inode) {
  if (!bi->HasBeenComplete()) {
    // Incomplete blocks are never considered mis-replicated --
    // they'll be reached when they are completed or recovered.
    return MisReplicationResult::kUnderConstruction;
  }
  auto rn = CountReplica(bi);
  if (IsNeededReplication(bi, &inode, inode.replication(), rn.live)) {
    needed_replications_.Add(
        bi, rn.live, inode.replication(), "Process mis replicated block");
    return MisReplicationResult::kUnderReplicated;
  }
  if (rn.live > inode.replication()) {
    if (rn.stale > 0) {
      // If any of the replicas of this block are on nodes that are
      // considered "stale", then these replicas may in fact have
      // already been deleted. So, we cannot safely act on the
      // over-replication until a later point in time, when
      // the "stale" nodes have block reported.
      return MisReplicationResult::kPostPone;
    }
    // over-replicated block
    ProcessOverReplicatedBlock(bi, inode.replication());
    return MisReplicationResult::kOverReplicated;
  }
  return MisReplicationResult::kOK;
}

BlockMapStat BlockManager::stat() {
  std::shared_lock<ReadWriteLock> lock(stat_lock_);
  return stat_;
}

void BlockManager::RemoveBlocksAssociatedTo(
    DatanodeID dn_id,
    const std::vector<BlockID>& blocks) {
  if (blocks.empty()) {
    return;
  }
  std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
  auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
  for (auto block_id : blocks) {
    auto& s = slice(block_id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      VLOG(8) << "Remove Block not exist, B" << block_id;
      continue;
    }
    if (bi->RemoveStorage(dn_id)) {
      auto live_number = CountReplica(bi).live;
      if (live_number == 0) {
        DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(bi->id(),
                                          "\"blk_id\":%" PRIu64
                                          ","
                                          "\"op\":\"lost_replica\","
                                          "\"gs\":%" PRIu64
                                          ","
                                          "\"numbytes\":%d,"
                                          "\"report_dn_ip\":\"%s\"}",
                                          bi->id(),
                                          bi->gs(),
                                          bi->num_bytes(),
                                          dn->ip().ToString().c_str());
      }
      if (bi->IsComplete()) {
        safemode_->DecrementSafeBlockCount(live_number);
      }
      UpdateNeededReplications(bi, -1, 0, "RemoveBlocksAssociatedTo", dn_id);
    }
    s->RemoveFromCorruptBlock(block_id, dn_id);
    s->RemoveFromSealedBlock(block_id, dn_id);

    StorageClassReportProto scr;
    scr.set_stcls(StorageClassProto::NONE);
    replica_report.emplace_back(block_id, scr);
  }
  CommitStorageClassReports(dn->uuid(), replica_report, true);
}

void BlockManager::CleanupDatanode(DatanodeID dn_id) {
  for (auto& s : slices_) {
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    s->EraseInvalidateBlock(dn_id);
    s->EraseTruncatableBlock(dn_id);
    s->EraseExcessBlock(dn_id);
    s->RemoveAllPendingFutureBlks(dn_id);
  }
}

void BlockManager::AddDecommissionBlocks(DatanodeID dn_id,
                                         const std::string& dn_ip,
                                         const std::vector<BlockID>& blocks) {
  if (blocks.empty()) {
    return;
  }
  RETURN_IF_NO_ACTIVE();
  for (auto block_id : blocks) {
    auto& s = slice(block_id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      VLOG(8) << "Decommission Block not exist, B" << block_id;
      continue;
    }

    INode inode;
    if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
        !ns_->GetINode(bi->inode_id(), &inode)) {
      inode.set_id(kInvalidINodeId);
    }

    auto rn = CountReplica(bi);
    auto cur_expected_replicas = BlockExpectedReplica(*bi, &inode);
    auto real_live_replicas = 0;
    if (rn.live >= rn.decommission) {
      real_live_replicas = rn.live - rn.decommission;
    }

    // In the following cases there is no need to repair block again.
    // 1. replica on decommission DN is already marked `Excess`(will be
    // deleted soon),
    // 2. block already in need replication queue(will be scheduled to do
    // block repair)
    // 3. block already in pending_replication_queue (already scheduled
    // replication work)
    // notice: replicas that marked 'Excess' will not be count as live replicas!
    do {
      if (s->IsBlockExcess(block_id, dn_id)) {
        break;
      }
      if (!IsNeededReplication(bi,
                               &inode,
                               cur_expected_replicas,
                               real_live_replicas)) {
        break;
      }
      VLOG(2) << "[AddDecommissionBlocks] dn_ip=" << dn_ip << " bi=" << bi->id()
              << " rn=" << rn << " cur_expected_replicas="
              << static_cast<int>(cur_expected_replicas);
      if (needed_replications_.Contains(bi->blk())) {
        break;
      }
      if (s->NumReplicas(block_id) != 0) {
        break;
      }
      RETURN_IF_NO_ACTIVE();
      if (needed_replications_.SizePriority(
              UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed) >=
          FLAGS_datanode_max_block_number_in_decommission) {
        break;
      }
      needed_replications_.Add(
          bi,
          real_live_replicas,
          cur_expected_replicas,
          "Decommission",
          UnderReplicatedBlocks::Priority::kUnderReplicated);

    } while (0);

    if (rn.live > cur_expected_replicas) {
      VLOG(2) << "[AddDecommissionBlocks To OverReplication] dn_ip=" << dn_ip
              << " bi=" << bi->id() << " rn=" << rn << " cur_expected_replicas="
              << static_cast<int>(cur_expected_replicas);
      ProcessOverReplicatedBlock(bi, cur_expected_replicas);
    }
  }
}

bool BlockManager::AddTransferBlocks(const BlockID block_id,
                                     int32_t required_replicas,
                                     UnderReplicatedBlocks::Priority priority) {
  VLOG(10) << "Add transfer " << block_id << " for "
          << required_replicas << " replicas";
  auto& s = slice(block_id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  BlockInfoGuard bi_guard(s.get(), block_id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    VLOG(8) << "Transfer Block not exist, B" << block_id;
    return false;
  }

  auto rn = CountReplica(bi);
  int32_t expected_rep = required_replicas - rn.live;
  if (expected_rep <= 0) {
    return false;
  }
  auto res = needed_replications_.Add(
      bi, rn.live, expected_rep, "CopyReplica", priority);
  if (res) {
    MFC(metrics_.add_transfer_block_count_)->Inc();
  } else {
    MFC(metrics_.add_transfer_block_failed_count_)->Inc();
  }
  return res;
}

size_t BlockManager::CountReplicatedBlocks(const std::vector<BlockID>& blocks) {
  size_t replicated_blocks = 0;

  for (auto block_id : blocks) {
    auto& s = slice(block_id);
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      VLOG(8) << "Decommission Block not exist, B" << block_id;
      continue;
    }

    INode inode;
    if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
        !ns_->GetINode(bi->inode_id(), &inode)) {
      inode.set_id(kInvalidINodeId);
    }
    auto rn = CountReplica(bi);
    auto cur_expected_replicas = BlockExpectedReplica(*bi, &inode);

    auto real_live_replicas = 0;
    if (rn.live >= rn.decommission) {
      real_live_replicas = rn.live - rn.decommission;
    }
    if (real_live_replicas >= cur_expected_replicas) {
      replicated_blocks++;
    }
  }
  return replicated_blocks;
}

// NOTICE: slice lock should already been held
bool BlockManager::IsNeededReplication(const BlockInfo* bi,
                                       const INode* inode,
                                       int expected_replication,
                                       int num_live_replica) {
  if (num_live_replica >= expected_replication) {
    return false;
  } else {
    if (IsFalsePersistedAccBlock(bi, inode)) {
      return true;
    }
    return bi->IsComplete() || IsResidentInCache(bi, inode);
  }
}

// NOTICE: slice lock should already been held
bool BlockManager::IsFalsePersistedAccBlock(const BlockInfo* bi,
                                            const INode* inode) {
  if (bi->IsPersisted()) {
    // ACC special case: FileNotPersisted

    // inode not found
    if (inode == nullptr) {
      return false;
    }

    // inode not found
    if (inode->id() == kInvalidINodeId) {
      return false;
    }

    if (ns_ && ns_->IsAccMode() && inode->has_ufs_file_info()) {
      if (inode->ufs_file_info().file_state() ==
              UfsFileState::kUfsFileStateToBePersisted &&
          inode->ufs_file_info().create_type() ==
              UfsFileCreateType::kUfsFileCreateTypeNormal) {
        return true;
      }
    }
  }
  return false;
}

bool BlockManager::IsResidentInCache(const BlockInfo* bi, const INode* inode) {
  if (!FLAGS_enable_transfer_persist_block) {
    return false;
  }
  if (!bi->IsPersisted()) {
    return false;
  }

  // inode not found
  if (inode == nullptr) {
    return false;
  }

  // inode not found
  if (inode->id() == kInvalidINodeId) {
    return false;
  }

  //  The old version's synced metadata doesn't set the ufs key. Do not
  //  duplicate in this case For backwards compatibility.
  if (ns_ && ns_->IsAccMode() && inode->has_ufs_file_info()) {
    if (inode->ufs_file_info().has_key() &&
        inode->ufs_file_info().key().empty()) {
      LOG(INFO) << "Do not copy replica because UFS key is null" << inode->id();
      return false;
    }
  }

  return inode->has_pin_status() && inode->pin_status().pinned() &&
         inode->pin_status().resident_data();
}

bool BlockManager::ShouldPostponeBlocksFromFutureV2(uint64_t block_id,
                                                    uint64_t genstamp,
                                                    uint64_t current_block_id,
                                                    uint64_t current_gsv2) {
  if (is_active_) {
    return false;
  }

  if (genstamp == kBlockProtocolV2GenerationStamp) {
    if (current_block_id == 0) {
      current_block_id = ns_->last_allocated_block_id();
    }
    return block_id > current_block_id;
  } else {
    return ShouldPostponeBlocksFromFuture(genstamp, current_gsv2);
  }
}

bool BlockManager::ShouldPostponeBlocksFromFuture(uint64_t genstamp,
                                                  uint64_t current_gsv2) {
  // When running inside a Standby node,
  // the node may receive block reports from datanodes before receiving
  // the corresponding namespace edits from the active NameNode.
  // Thus, it will postpone them for later processing,
  // instead of marking the blocks as corrupt.
  return !is_active_ && ns_->IsGenStampInFuture(genstamp, current_gsv2);
}

// NOTICE: slice lock should already been held
bool BlockManager::GetStoragePolicy(BlockInfo* bi,
                                    StoragePolicyId* storage_policy_id) {
  CHECK_NOTNULL(bi);
  INode inode;
  // Maybe need to optimize performance
  if (ns_->GetINode(bi->inode_id(), &inode)) {
    *storage_policy_id =
        static_cast<StoragePolicyId>(inode.storage_policy_id());
    return true;
  }
  LOG(INFO) << "block: " << bi->id() << " iNode does not exist.";
  return false;
}

Status BlockManager::GetBlocksWithLocations(
    DatanodeInfoPtr dn,
    uint64_t size,
    cloudfs::BlocksWithLocationsProto* blocks_proto) {
  auto cres = ha_state_->CheckOperation(OperationsCategory::kRead);
  if (cres.first.HasException()) {
    return cres.first;
  }
  uint64_t total_size = 0;
  dn->GetBlocksWithLocations(
      [&, this](uint64_t block_id,
                const std::string& dn_uuid,
                const std::string& storage_uuid,
                const StorageTypeProto& storage_type) -> bool {
        if (total_size >= size) {
          return false;
        }
        auto& s = slice(block_id);
        std::shared_lock<BlockMapSlice> guard(*s);
        s->TellLockHolder(__FILE__, __LINE__);
        BlockInfoGuard bi_guard(s.get(), block_id, false);
        BlockInfo* bi = bi_guard.GetBlockInfo();
        if (!bi) {
          VLOG(8) << "GetBlocksWithLocations, not find blk:" << block_id;
          return true;
        }
        auto block = blocks_proto->add_blocks();
        block->mutable_block()->set_blockid(block_id);
        block->mutable_block()->set_numbytes(bi->num_bytes());
        block->mutable_block()->set_genstamp(bi->gs());

        auto dn_ids = bi->storage_ids();
        for (size_t i = 0; i < bi->size(); ++i) {
          auto dn = datanode_manager_->GetDatanodeFromId(dn_ids[i]);
          CHECK_NOTNULL(dn);
          CHECK_GT(dn->address().datanodeuuid().size(), 0);
          block->add_datanodeuuids(dn->address().datanodeuuid());
          block->add_storagetypes(storage_type);
          // Notice: storage_uuid is not stored in block info,
          // and the client does not need it, filling in here arbitrarily
          block->add_storageuuids(storage_uuid);
        }
        total_size += bi->num_bytes();
        return true;
      });
  return Status();
}

// NOTICE: slice lock should already been held
bool BlockManager::GetPlacementAdvice(const BlockInfo* bi,
                                      PlacementAdvice* advice) {
  CHECK_NOTNULL(advice);
  if (!ns_->GetStoragePolicyByINodeId(
          bi->inode_id(), bi->parent_id(), &advice->storage_policy_id)) {
    return false;
  }
  ReplicaPolicy policy;
  if (!ns_->GetReplicaPolicyByINodeId(bi->inode_id(), &policy)) {
    return false;
  }
  advice->Init(policy);
  return true;
}

void BlockManager::ProcessPendingFutureBlks(BlockID block_id,
                                            uint64_t current_block_id,
                                            uint64_t current_gsv2) {
  std::deque<ReportedBlockInfo> rbis;
  auto& s = slice(block_id);
  {
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    s->DequeuePendingFutureBlks(block_id, &rbis);
  }
  if (rbis.empty()) {
    return;  // Early return
  }
  ProcessPendingReportedBlock(rbis, false, current_block_id, current_gsv2);
}

void BlockManager::EnqueuePendingPersistedBlks(Block block) {
  auto& s = slice(block.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  s->EnqueuePendingPersistedBlks(block);
}

void BlockManager::ProcessPendingPersistedBlks(Block block,
                                               uint64_t current_gsv2) {
  auto& s = slice(block.id);
  std::unique_lock<BlockMapSlice> guard(*s);
  s->TellLockHolder(__FILE__, __LINE__);
  if (s->RemovePendingPersistedBlks(block)) {
    BlockInfoGuard bi_guard(s.get(), block.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!PersistBlockUnsafe(s.get(), block)) {
      LOG(ERROR) << "B" << block.id << " can't be persisted, " << block;
    }
  }
}

void BlockManager::ProcessAllPendingReportedBlock() {
  int64_t future_blks_size = 0;
  int64_t misinvalidated_blks_size = 0;
  std::deque<ReportedBlockInfo> future_blks;
  std::deque<ReportedBlockInfo> misinvalidated_blks;
  for (auto& s : slices_) {
    {
      std::unique_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      s->DequeuePendingFutureBlks(&future_blks);
      s->DequeuePendingMisinvalidatedBlks(&misinvalidated_blks);
    }
    int batch_size = FLAGS_blk_process_all_pending_batch_size;
    if (batch_size != 0 && (future_blks.size() > batch_size ||
                            misinvalidated_blks.size() > batch_size)) {
      ProcessPendingReportedBlock(future_blks);
      ProcessPendingReportedBlock(misinvalidated_blks, true);

      future_blks_size += future_blks.size();
      misinvalidated_blks_size += misinvalidated_blks.size();

      future_blks.clear();
      misinvalidated_blks.clear();
    }
  }

  ProcessPendingReportedBlock(future_blks);
  ProcessPendingReportedBlock(misinvalidated_blks, true);

  future_blks_size += future_blks.size();
  misinvalidated_blks_size += misinvalidated_blks.size();
  LOG(INFO) << "ProcessAllPendingReportedBlock "
            << " future_blks_size=" << future_blks_size
            << " misinvalidated_blks_size=" << misinvalidated_blks_size;
}

uint64_t BlockManager::ProcessAllPendingMisinvalidatedBlks() {
  std::deque<ReportedBlockInfo> rbis;
  for (auto& s : slices_) {
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    s->DequeuePendingMisinvalidatedBlks(&rbis);
  }
  if (rbis.empty()) {
    return 0;
  }
  ProcessPendingReportedBlock(rbis, true);
  return rbis.size();
}

void BlockManager::ProcessPendingReportedBlock(
    const std::deque<ReportedBlockInfo>& rbis,
    bool is_misinvalidated_blk,
    uint64_t current_block_id,
    uint64_t current_gsv2) {
  for (const auto& rbi : rbis) {
    // for misinvalidated_blks, we need to give up the timeout item
    if (is_misinvalidated_blk) {
      auto diff = std::chrono::duration_cast<std::chrono::seconds>(
                      std::chrono::steady_clock::now() - rbi.create_time)
                      .count();
      if (diff > FLAGS_misinvalidated_block_queue_item_timeout_sec) {
        continue;
      }
    }

    std::string dnuuid;
    {
      auto& s = slice(rbi.blk.id);
      std::unique_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);

      if (ShouldPostponeBlocksFromFutureV2(
              rbi.blk.id, rbi.blk.gs, current_block_id, current_gsv2)) {
        VLOG(15) << "Postpone block from future again, " << rbi.blk.ToString()
                 << ", is_misinvalidated_blk: " << is_misinvalidated_blk;
        s->EnqueuePendingFutureBlks(rbi.dsp, rbi.blk, rbi.dn_id, rbi.rsp);
        continue;
      }


    BlockInfoGuard bi_guard(s.get(), rbi.blk.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    auto dn = datanode_manager_->GetDatanodeFromId(rbi.dn_id);
    uint8_t live = 0;
    if (bi != nullptr) {
        live = CountReplica(bi).live;
    } else {
        LOG(INFO) << "Failed to get block info for B" << rbi.blk.id;
    }
    auto res = s->ProcessReportedBlock(rbi.dn_id,
                                       rbi.blk,
                                       rbi.rsp,
                                       is_active_,
                                       dn->IsDecommissionInProgress(),
                                       live);

      std::vector<BlockID> to_add;
      switch (res) {
        case ADD:
          ReplicaNum rn;
          if (AddStoredBlock(rbi.dn_id, rbi.blk, &rn)) {
            to_add.emplace_back(rbi.blk.id);
          }
          break;
        case INVALIDATE:
          // for active, not need to record misinvalidated block
          if (!is_active_) {
            VLOG(8) << "ProcessPendingReportedBlock misinvalidated block: "
                    << rbi.blk.ToString()
                    << ", is_misinvalidated_blk: " << is_misinvalidated_blk;
            if (is_misinvalidated_blk) {
              // reenter, needn't to change create_time
              s->EnqueuePendingMisinvalidatedBlks(rbi);
            } else {
              s->EnqueuePendingMisinvalidatedBlks(
                  ReportedBlockInfo{rbi.blk,
                                    rbi.dn_id,
                                    rbi.dsp,
                                    rbi.rsp,
                                    std::chrono::steady_clock::now()});
            }
          }
          break;
        case CORRUPT:
          if (IsPopulatingReplicationQueues()) {
            ProcessCorruptReplica(
                rbi.dn_id, rbi.blk, "ProcessPendingReportedBlock");
          }
          break;
        case ADD_TO_BLOCK_INDEX:
          to_add.emplace_back(rbi.blk.id);
          break;
        case NOTHING:
          break;
        default:
          LOG(ERROR) << "unknown replica mark: " << res;
      }

      if (!to_add.empty()) {
        datanode_manager_->AddBlocks(rbi.dn_id, rbi.dsp, to_add);
      }

      dnuuid = dn->uuid();
    }
  }
}

// NOTICE: slice lock should already been held
// TODO(xiong): add replication_for_read
void BlockManager::ProcessOverReplicatedBlock(const BlockInfo* bi,
                                              size_t replication,
                                              DatanodeID added_dn,
                                              DatanodeID del_dn_hint) {
  if (del_dn_hint == added_dn) {
    del_dn_hint = kInvalidDatanodeID;
  }

  auto added_dn_info = datanode_manager_->GetDatanodeFromId(added_dn);
  auto del_dn_info = datanode_manager_->GetDatanodeFromId(del_dn_hint);
  VLOG(2) << "[ProcessOverReplicatedBlock]"
          << " blk_id=" << bi->id()
          << " replication=" << static_cast<int>(replication) << " added_dn="
          << (added_dn_info == nullptr ? "null"
                                       : added_dn_info->GetFullLocationString())
          << " del_dn_hint="
          << (del_dn_info == nullptr ? "null"
                                     : del_dn_info->GetFullLocationString());

  std::unordered_set<DatanodeInfoPtr> non_excess_dns;
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn = datanode_manager_->GetDatanodeFromId(sid);
    CHECK_NOTNULL(dn);
    auto& s = slice(bi->id());
    if (dn->content_stale()) {
      // Balance, Decommission only, ignore storage content state
      if (del_dn_hint != kInvalidDatanodeID) {
        s->AddToInvalidateBlock(del_dn_hint,
                                Block(bi->id(), bi->num_bytes(), bi->gs()),
                                "ProcessOverReplicatedBlock");
        VLOG(8) << "ProcessOverReplicatedBlock, ignore storage content state"
                   " directly delete del_dn_hint= "
                << (del_dn_info == nullptr
                        ? "null"
                        : del_dn_info->GetFullLocationString())
                << ", blk_" << bi->id();
      } else {
        PostponeBlock(bi->id());
      }
      return;
    }
    if (s->IsBlockExcess(bi->id(), sid)) {
      continue;
    }
    if (s->IsBlockCorrupt(bi->id(), sid)) {
      continue;
    }
    non_excess_dns.insert(dn);
  }

  // Decommission first
  for (auto it = non_excess_dns.begin(); it != non_excess_dns.end();) {
    if (non_excess_dns.size() <= replication) {
      return;
    }

    auto dn = *it;
    if (dn->IsDecommissionInProgress()) {
      auto& s = slice(bi->id());
      s->AddToExcess(bi->id(), dn->id());
      s->AddToInvalidateBlock(dn->id(),
                              Block(bi->id(), bi->num_bytes(), bi->gs()),
                              "ChooseReplicaToDelete");

      VLOG(8) << "ProcessOverReplicatedBlock, Choose Decommissioning dn="
              << dn->GetFullLocationString() << ", blk_" << bi->id();

      it = non_excess_dns.erase(it);
      continue;
    }

    it++;
  }

  // Normal
  if (non_excess_dns.size() <= replication) {
    return;
  }
  if (bi->uc_state() != BlockUCState::kPersisted) {
    // TODO(zhuangsiyu): consider the scene of PIN
    ChooseExcessReplicates(
        non_excess_dns, bi, replication, added_dn_info, del_dn_info);
  }
}

// NOTICE: slice lock should already been held
void BlockManager::ChooseExcessReplicates(
    std::unordered_set<DatanodeInfoPtr>& non_excess_dns,
    const BlockInfo* bi,
    size_t replication,
    DatanodeInfoPtr added_dn,
    DatanodeInfoPtr del_dn_hint) {
  if (non_excess_dns.find(added_dn) == non_excess_dns.end()) {
    added_dn = nullptr;
  }
  if (non_excess_dns.find(del_dn_hint) == non_excess_dns.end()) {
    del_dn_hint = nullptr;
  }

  PlacementAdvice advice;
  GetPlacementAdvice(bi, &advice);

  if (VLOG_IS_ON(2)) {
    std::string dns_str;
    for (auto dn : non_excess_dns) {
      dns_str += dn->GetFullLocationString();
      dns_str += " ";
    }
    LOG(INFO) << "[ChooseExcessReplicates]"
              << " blk_id=" << bi->id()
              << " replication=" << static_cast<int>(replication)
              << " non_excess_dns[" << non_excess_dns.size() << "]=" << dns_str
              << " advice=" << advice.ToString() << " added_dn="
              << (added_dn == nullptr ? "null"
                                      : added_dn->GetFullLocationString())
              << " del_dn_hint="
              << (del_dn_hint == nullptr
                      ? "null"
                      : del_dn_hint->GetFullLocationString());
  }

  // Split nodes into two sets:
  // more_than_one: contains more than one replica on one rack.
  // exactly_one: contains only one replica on one rack.
  std::unordered_set<DatanodeInfoPtr> more_than_one;
  std::unordered_set<DatanodeInfoPtr> exactly_one;
  std::unordered_map<std::string, std::unordered_set<DatanodeInfoPtr>> rack_map;
  for (const auto dn : non_excess_dns) {
    auto location = dn->GetLocationString();
    rack_map[location].insert(dn);
  }
  for (auto& pair : rack_map) {
    auto& dns = pair.second;
    auto& dn_set = dns.size() == 1 ? exactly_one : more_than_one;
    for (auto& dn : dns) {
      dn_set.emplace(dn);
    }
  }

  bool use_hint = true;
  // Pick one node to delete that favors the delete hint.
  // Otherwise pick one with least space from priSet if it is not empty.
  // Otherwise one node with least space from remains.
  while (non_excess_dns.size() > replication) {
    DatanodeInfoPtr cur = nullptr;
    // Check if we can use del hint
    if (use_hint && del_dn_hint != nullptr) {
      // check if removing del hint reduces the number of racks
      if (more_than_one.count(del_dn_hint)) {
        // del hint and some other nodes are under the same rack
        cur = del_dn_hint;
      } else if (added_dn != nullptr && !more_than_one.count(added_dn)) {
        // del_dn_hint in a rack which only have one replica
        // but the added node adds to a new rack
        cur = del_dn_hint;
      }
    }
    // only consider delHint for the first case
    use_hint = false;

    // regular excessive replica removal
    if (cur == nullptr) {
      cur = datanode_manager_->ChooseReplicaToDelete(
          {bi->id(), bi->num_bytes(), bi->gs()},
          replication,
          more_than_one,
          exactly_one,
          advice);
      CHECK_NOTNULL(cur);
      VLOG(2) << "ChooseReplicaToDelete dn: " << cur->GetFullLocationString()
              << ", blk_" << bi->id();
    } else {
      VLOG(2) << "ChooseExcessReplicates, use del hint dn: "
              << cur->GetFullLocationString() << ", blk_" << bi->id();
    }
    auto& s = slice(bi->id());
    s->AddToExcess(bi->id(), cur->id());
    s->AddToInvalidateBlock(cur->id(),
                            Block(bi->id(), bi->num_bytes(), bi->gs()),
                            "ChooseReplicaToDelete");
    non_excess_dns.erase(cur);
    AdjustByChosenReplica(rack_map, more_than_one, exactly_one, cur);
  }
}

// NOTICE: slice lock should already been held
void BlockManager::AdjustByChosenReplica(
    std::unordered_map<std::string, std::unordered_set<DatanodeInfoPtr>>&
        rack_map,
    std::unordered_set<DatanodeInfoPtr>& more_than_one,
    std::unordered_set<DatanodeInfoPtr>& exactly_one,
    DatanodeInfoPtr choose_dn) {
  auto location = choose_dn->GetLocationString();
  auto& rack = rack_map[location];
  rack.erase(choose_dn);

  if (more_than_one.count(choose_dn)) {
    more_than_one.erase(choose_dn);
    if (rack.size() == 1) {
      exactly_one.emplace(choose_dn);
    }
  } else {
    exactly_one.erase(choose_dn);
    rack_map.erase(location);
  }
}

// NOTICE: slice lock should already been held
DatanodeInfoPtr BlockManager::ChooseSourceDatanode(
    BlockInfo* bi,
    const UnderReplicatedBlocks::Priority pri,
    std::unordered_map<std::string, int> expected_placement,
    std::unordered_set<DatanodeInfoPtr>* containing_dns,
    std::unordered_set<DatanodeInfoPtr>* live_dns,
    std::unordered_set<DatanodeInfoPtr>* decommission_dns,
    ReplicaNum* rn) {
  CHECK_NOTNULL(containing_dns);
  CHECK_NOTNULL(live_dns);
  CHECK_NOTNULL(rn);

  *rn = {0, 0, 0, 0, 0};

  std::string advised_dc;
  auto& s = slice(bi->id());
  auto dns = bi->storage_ids();
  if (expected_placement.size() > 1 && advised_dc.empty()) {
    if (pri == UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed) {
      // for bad rack distributed
      auto candidate = std::max_element(expected_placement.begin(),
                                        expected_placement.end(),
                                        [](const auto& a, const auto& b) {
                                          return a.second < b.second;
                                        })
                           ->second;
      advised_dc = candidate;
    }
    // calc rep in each DC
    for (size_t i = 0; i < bi->size(); ++i) {
      auto dn_id = dns[i];
      auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
      CHECK_NOTNULL(dn);
      if (dn->IsDecommissionInProgress() ||
          s->IsBlockCorrupt(bi->id(), dn_id) ||
          s->IsBlockExcess(bi->id(), dn_id)) {
        continue;
      } else {
        auto dc = dn->dc_name();
        expected_placement[dc]--;
      }
    }
    // erase satisfied DC
    for (auto iter = expected_placement.begin();
         iter != expected_placement.end();) {
      if (iter->second <= 0) {
        iter = expected_placement.erase(iter);
      } else {
        iter++;
      }
    }
    // only one DC is not satisfied
    if (expected_placement.size() == 1) {
      advised_dc = expected_placement.begin()->first;
      VLOG(2) << "advised_dc for recovery is " << advised_dc;
    }
  }

  std::shared_lock<RWSpinlock> lock(rwlock_);
  DatanodeInfoPtr src_dn = nullptr;
  std::random_device rd;
  std::mt19937 gen(rd());
  auto last_replication_index = bi->last_replication_index();
  bool first_time =
      last_replication_index == std::numeric_limits<uint8_t>::max();
  uint8_t choose_idx = std::numeric_limits<uint8_t>::max();
  for (size_t i = 0; i < bi->size(); ++i) {
    size_t idx = i;
    if (!first_time) {
      idx = (idx + last_replication_index + 1) % bi->size();
    }
    auto dn_id = dns[idx];
    auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
    CHECK_NOTNULL(dn);

    containing_dns->insert(dn);

    if (dn->content_stale()) {
      ++rn->stale;
    }
    if (dn->IsDecommissionInProgress()) {
      ++rn->decommission;
      decommission_dns->insert(dn);
    }

    if (s->IsBlockCorrupt(bi->id(), dn_id)) {
      ++rn->corrupt;
      continue;
    } else if (s->IsBlockExcess(bi->id(), dn_id)) {
      ++rn->excess;
    } else {
      ++rn->live;
      live_dns->insert(dn);
    }

    auto it = dn_replication_queues_.find(dn_id);
    if (it != dn_replication_queues_.end()) {
      if (dn->IsDecommissionInProgress()) {
        if (it->second.size() >
            FLAGS_blockmap_max_replication_streams_decommission) {
          continue;
        }
      } else {
        if (pri != UnderReplicatedBlocks::Priority::kHighestPriority &&
            it->second.size() >= FLAGS_blockmap_max_replication_streams) {
          continue;
        }
        if (it->second.size() >=
            FLAGS_blockmap_max_replication_streams_hard_limit) {
          continue;
        }
        uint64_t total_replication_bytes = 0;
        for (BlockTargetPair& pair : it->second) {
          total_replication_bytes += pair.blk.num_bytes;
        }
        if (total_replication_bytes >=
            FLAGS_blockmap_max_replication_replication_bytes) {
          VLOG(10) << dn->hostname() << " replication exceed "
                   << FLAGS_blockmap_max_replication_replication_bytes;
          continue;
        }
      }
    }
    if (src_dn == nullptr) {
      src_dn = dn;
      choose_idx = idx;
      continue;
    } else {
      // if it's not the first time, we prefer to choose DN in turns
      if (!first_time) {
        continue;
      }
    }
    bool satisfied = true;
    if (!advised_dc.empty()) {
      auto dc = dn->dc_name();
      std::uniform_real_distribution<> dis(0.0, 1.0);
      auto rand = dis(gen);
      satisfied = (rand <= FLAGS_blockmap_replication_chose_loocal_dc_pct &&
                   dc == advised_dc) ||
                  (rand > FLAGS_blockmap_replication_chose_loocal_dc_pct &&
                   dc != advised_dc);
    }
    VLOG(2) << "bi=" << bi->id() << " dn=" << dn->GetFullLocationString()
            << " satisfied=" << satisfied;
    if (satisfied) {
      // we prefer nodes that are in DECOMMISSION_INPROGRESS state
      if (dn->IsDecommissionInProgress()) {
        src_dn = dn;
        choose_idx = idx;
        continue;
      }
      if (src_dn->IsDecommissionInProgress()) {
        continue;
      }

      // random
      std::uniform_int_distribution<> dis(0, 1);
      if (dis(gen) == 0) {
        src_dn = dn;
        choose_idx = idx;
      }
    }
  }

  bi->set_last_replication_index(choose_idx);

  VLOG_OR_IF(10, FLAGS_log_block_transfer_detail)
      << "bi=" << bi->id()
      << " last_replication_index=" << static_cast<int>(last_replication_index)
      << " choose_idx=" << static_cast<int>(choose_idx) << " src_dn="
      << (src_dn == nullptr ? "nullptr" : src_dn->GetFullLocationString());

  return src_dn;
}

size_t BlockManager::ComputeDatanodeWork() {
  if (safemode_->IsOn()) {
    return 0;
  }
  size_t num_live = datanode_manager_->NumLiveDatanodes();
  auto blocks_to_process = static_cast<size_t>(
      num_live * FLAGS_blockmap_replication_work_multiplier);
  auto work = ComputeReplicationWork(blocks_to_process);
  return work;
}

void BlockManager::ComputeInvalidateBlockWork() {
  if (safemode_->IsOn()) {
    return;
  }
  uint64_t invalidate_blk_count = 0;
  std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>
      invalidate_blks;
  for (auto& s : slices_) {
    RETURN_IF_NO_ACTIVE();
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RETURN_IF_NO_ACTIVE();
    invalidate_blk_count += s->GetInvalidateBlockNum();
    s->GetInvalidateBlock(&invalidate_blks);
  }
  invalidate_blk_count_.store(invalidate_blk_count);
  for (auto& b : invalidate_blks) {
    auto dn = datanode_manager_->GetDatanodeFromId(b.first);
    RETURN_IF_NO_ACTIVE();
    dn->PushInvalidateBlock(b.second);
    RETURN_IF_NO_ACTIVE();
  }
}

void BlockManager::ComputeTruncatableBlockWork() {
  VLOG(8) << "ComputeTruncatableBlockWork";
  RETURN_IF_NO_ACTIVE();
  uint64_t truncatable_blk_count = 0;
  for (auto& s : slices_) {
    RETURN_IF_NO_ACTIVE();

    std::unordered_map<DatanodeID, std::unordered_set<Block, BlockHash>>
        truncatable_blks;
    {
      std::unique_lock<BlockMapSlice> guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      truncatable_blk_count += s->GetTruncatableBlockNum();

      RETURN_IF_NO_ACTIVE();
      s->PopTruncatableBlock(&truncatable_blks);
    }

    RETURN_IF_NO_ACTIVE();
    for (auto& b : truncatable_blks) {
      auto dn = datanode_manager_->GetDatanodeFromId(b.first);
      RETURN_IF_NO_ACTIVE();
      if (dn == nullptr) {
        LOG(ERROR) << "unknown dn_id=" << b.first;
      }
      RETURN_IF_NO_ACTIVE();
      dn->PushTruncatableBlock(b.second);
      RETURN_IF_NO_ACTIVE();
    }
  }
  truncatable_blk_count_.store(truncatable_blk_count);
}

void BlockManager::ComputeSealedBlockWork() {
  VLOG(8) << "ComputeSealedBlockWork";
  RETURN_IF_NO_ACTIVE();
  uint64_t sealed_blk_count = 0;
  for (auto& s : slices_) {
    RETURN_IF_NO_ACTIVE();
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    sealed_blk_count += s->GetSealedBlockNum();

    RETURN_IF_NO_ACTIVE();
    s->MoveSealedBlockToTruncatable(
        [&]() { return IsPopulatingReplicationQueues(); });
  }
  sealed_blk_count_.store(sealed_blk_count);
}

void BlockManager::ProcessAndDumpFsckBlockReplication(
    std::vector<BlockID>& block_ids,
    std::ostream& os) {
  std::vector<BlockID> replication_blocks;
  std::vector<BlockID> healthy_blocks;

  // process blocks to be replicated
  for (auto block_id : block_ids) {
    auto& s = slice(block_id);
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (bi == nullptr) {
      os << "Could not locate block: B" << block_id << "\n";
      continue;
    }
    INode inode;
    if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
        !ns_->GetINode(bi->inode_id(), &inode)) {
      inode.set_id(kInvalidINodeId);
    }
    uint32_t expected = BlockExpectedReplica(*bi, &inode);
    auto rn = CountReplica(bi);
    if (IsNeededReplication(bi, &inode, expected, rn.live)) {
      needed_replications_.Add(
          bi,
          rn.live,
          expected,
          "Process fsck block replication",
          UnderReplicatedBlocks::Priority::kHighestPriority);
      replication_blocks.push_back(block_id);
    } else {
      healthy_blocks.push_back(block_id);
    }
  }
  replication_monitor_->TriggerReplicationWork();

  // print replication result
  os << "Find unhealthy blocks: [";
  for (size_t i = 0; i < replication_blocks.size(); i++) {
    if (i) {
      os << ", ";
    }
    os << replication_blocks[i];
  }
  os << "]\n";
  os << "Healthy Blocks (no need to repair): [";
  for (size_t i = 0; i < healthy_blocks.size(); i++) {
    if (i) {
      os << ", ";
    }
    os << healthy_blocks[i];
  }
  os << "]\n";
}

bool BlockManager::LoadBlockForZeroReplicaEnable(BlockInfo* bi, size_t pending_replica) {
  if (bi == nullptr) {
    return false;
  }
  return FLAGS_enable_load_for_complete_replica && ns_->IsAccMode() &&
         bi->IsPersisted() && pending_replica == 0;
}

// return true if replication work is scheduled.
// 'retryable' is set to true if compute replication work failed but it is
// retryable
bool BlockManager::ProcessBlockNeedReplicate(
    const Block& blk,
    size_t pri,
    int64_t max_cross_dc_bytes,
    int64_t decommission_max_cross_dc_bytes,
    int64_t* now_cross_dc_bytes,
    int64_t* decommission_now_cross_dc_bytes,
    bool* retryable) {
  CHECK_NOTNULL(retryable);
  *retryable = false;
  DEFER([&] { MFC(metrics_.repl_work_processed_blk_counter_[pri])->Inc(); });
  StopWatch sw(metrics_.repl_work_lock_acquire_time_);
  sw.Start();

  auto& s = slice(blk.id);
  std::shared_lock<BlockMapSlice> guard(*s);

  sw.NextStep(metrics_.repl_work_get_block_info_time_);

  BlockInfoGuard bi_guard(s.get(), blk.id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi || !bi->HasBeenComplete()) {
    return false;
  }

  if (!BlockNeedTransferUnsafe(bi)) {
    return false;
  }

  // defence 0 replica block
  INode inode;
  if (ns_ != nullptr && (ns_->GetINode(bi->inode_id(), &inode))) {
    if (inode.replication() == 0) {
      VLOG(2) << "zero replica block wrongly put into need_replication "
                 "queue, block="
              << blk.id << ", pri= " << pri
              << "blk_expected_rep=" << (int)BlockExpectedReplica(*bi, &inode)
              << ", skip it.";
      return false;
    }
  } else {
    inode.set_id(kInvalidINodeId);
  }

  // should replication by mtime
  int64_t time_now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::system_clock::now().time_since_epoch())
                            .count();
  if (inode.mtime() + FLAGS_replication_grace_time_ms >= time_now_ms) {
    bool should_replication = false;
    for (int i = 0;
         i + FLAGS_replication_grace_last_block_num < inode.blocks_size();
         ++i) {
      if (inode.blocks(i).blockid() == blk.id) {
        should_replication = true;
      }
    }
    if (!should_replication) {
      VLOG(8) << "is too early to transfer block."
              << " inode=" << inode.ShortDebugString()
              << " block_id=" << blk.id;
      return false;
    }
  }

  sw.NextStep(metrics_.repl_work_get_placement_advice_time_);

  PlacementAdvice advice;
  if (!GetPlacementAdvice(bi, &advice)) {
    LOG(WARNING) << "block: " << bi->id() << " inode does not exist.";
    return false;
  }

  auto expected_placement = datanode_manager_->GetExpectedPlacement(
      advice, BlockExpectedReplica(*bi, &inode));
  std::string expected_placement_str;
  if (VLOG_IS_ON(2)) {
    std::ostringstream oss;
    oss << "[";
    for (auto& p : expected_placement) {
      oss << "(" << p.first << "=" << p.second << "), ";
    }
    oss << "]";
    expected_placement_str = oss.str();
  }

  sw.NextStep(metrics_.repl_work_choose_src_dn_time_);

  std::unordered_set<DatanodeInfoPtr> containing_dns;
  std::unordered_set<DatanodeInfoPtr> live_dns;
  std::unordered_set<DatanodeInfoPtr> decommission_dns;
  ReplicaNum rn;

  auto pri_enum = static_cast<UnderReplicatedBlocks::Priority>(pri);
  auto src_dn = ChooseSourceDatanode(bi,
                                     pri_enum,
                                     expected_placement,
                                     &containing_dns,
                                     &live_dns,
                                     &decommission_dns,
                                     &rn);
  if (src_dn == nullptr) {
    if (LoadBlockForZeroReplicaEnable(bi, s->NumReplicas(blk.id))) {
      if (replicate_block_loaded_inodes_cache_.find(inode.id()) !=
          replicate_block_loaded_inodes_cache_.end()) {
        // The file has been loaded. Try transfer next round.
        return true;
      }
      std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
      opt->data = true;
      // Make sure ufs key is set if enable_load_task_generate_ufs_name is false
      if (!FLAGS_enable_load_task_generate_ufs_name) {
        if (ns_->IsAccMode() && inode.has_ufs_file_info() &&
            inode.ufs_file_info().has_key()) {
          opt->ufs_key = inode.ufs_file_info().key();
        }
        if (opt->ufs_key.empty()) {
          LOG(INFO) << "INode " << inode.id() << " ufs file has not key ";
          return false;
        }
      }

      // Choose target DN
      std::vector<DatanodeID> dn_ids;
      int32_t target_dn_size = 1;
      {
        ReadAdvice advice(kDefaultReadPolicy);
        advice.version_checker = [](ProductVersion version) -> bool {
          // Minimum version for Load&Free is 1.5.0
          return version >= FLAGS_load_compatible_dn_version;
        };
        BlockProto bp;
        bp.set_blockid(bi->id());
        DetailedBlock detailed_blk = GetDetailedBlock(bp);

        if (!datanode_manager_->ChooseTarget4Read(
                detailed_blk,
                target_dn_size,
                advice,
                NetworkLocationInfo(),
                DatanodeManager::RepeatedDatanodeInfo(),
                &dn_ids) ||
            dn_ids.empty()) {
          LOG_WITH_LEVEL(WARNING)
              << "Failed to choose target to load block B" << bi->id();
          MFC(LoggerMetrics::Instance().warn_)->Inc();
        }
      }

      if (dn_ids.size() != target_dn_size) {
        LOG(WARNING) << "Expected target dn size is " << target_dn_size
                     << " but got " << dn_ids.size();
        if (dn_ids.empty()) {
          // Do not retry
          return true;
        } else if (dn_ids.size() > target_dn_size) {
          dn_ids.erase(dn_ids.begin() + target_dn_size, dn_ids.end());
        }
      }

      ManagedJobId job_id;
      LOG(INFO) << "Submit load data job for transfer replica B" << blk.id;
      Status status =
          job_manager_->SubmitLoadDataJob(&inode, opt, &job_id, dn_ids);
      if (!status.IsOK()) {
        LOG(WARNING) << "Failed to submit load data job for " << bi->id()
                     << " status: " << status.ToString();
        return false;
      }

      replicate_block_loaded_inodes_cache_.emplace(inode.id());
      s->IncrementReplicas(blk.id, dn_ids);
      return true;
    } else {
      bool blockPersisted = bi->IsPersisted();
      if (pri_enum != UnderReplicatedBlocks::Priority::kWithCorruptBlocks &&
          !blockPersisted && rn.live > 0) {
        MFC(metrics_.repl_work_choose_src_failed_counter_)->Inc();
        *retryable = true;
      }
      VLOG(2) << "Block: " << blk
              << " cannot be replicated from any node. pri: " << pri
              << ", live_dns=" << (int)rn.live << ", retryable=" << *retryable
              << ", expected_rep=" << (int)BlockExpectedReplica(*bi, &inode);
      return false;
    }
  }
  CHECK_GE(live_dns.size(), rn.live);

  sw.NextStep(metrics_.repl_work_has_enough_rack_time_);
  int expected_replicas = BlockExpectedReplica(*bi, &inode);
  int real_live_replicas = 0;
  if (rn.live > rn.decommission) {
    real_live_replicas = rn.live - rn.decommission;
  }
  int num_effective_replicas = real_live_replicas + s->NumReplicas(blk.id);
  int satisfied_placement_replicas =
      ComputeSatisfiedPlacementReplicas(bi, std::move(expected_placement));
  if (num_effective_replicas >= expected_replicas &&
      (HasEnoughRack(bi, &inode) ||
       num_effective_replicas > real_live_replicas) &&
      decommission_dns.empty() &&
      (expected_replicas == satisfied_placement_replicas)) {
    VLOG(8) << "Block Has enough replica, num_effective_replicas: "
            << num_effective_replicas << ", live: " << static_cast<int>(rn.live)
            << "real_live: " << real_live_replicas
            << ", expected: " << expected_replicas
            << ", satisfied_placement: " << satisfied_placement_replicas
            << ", expected_placement: " << expected_placement_str
            << ", blk: " << blk.id;
    MFC(metrics_.repl_work_no_need_replication_counter_)->Inc();
    return false;
  }

  auto num_additional_replicas =
      std::max(1, expected_replicas - num_effective_replicas);
  // for MoveDC
  num_additional_replicas =
      std::max(num_additional_replicas,
               expected_replicas - satisfied_placement_replicas);
  VLOG_OR_IF(10, FLAGS_log_block_transfer_detail)
      << "Block B" << blk.id << " need additional " << num_additional_replicas
      << " replicas. expected=" << expected_replicas
      << ", effective=" << num_effective_replicas
      << ", expected_placement: " << expected_placement_str
      << ", satisfied_placement=" << satisfied_placement_replicas;

  sw.NextStep(metrics_.repl_work_build_src_path_);
  auto src_path = ns_->BuildFullPath(bi->inode_id());

  sw.NextStep(metrics_.repl_work_choose_tgt_dn_time_);
  std::vector<DatanodeID> targets;
  auto favored_dns =
      datanode_manager_->GetFavored4Decommission(decommission_dns);

  std::random_device rd;
  std::mt19937 re(rd());
  std::shuffle(favored_dns.begin(), favored_dns.end(), re);

  // decommission DN should not be taken into consideration when choose target
  // for recovery, or ChooseTarget4Recover() will misunderstand that
  // decommission DC (which DC decommission DN belong) has enough replica and
  // choose recover target from other DC.
  for (auto& dn : decommission_dns) {
    live_dns.erase(dn);
  }
  if (!datanode_manager_->ChooseTarget4Recover(src_path,
                                               num_additional_replicas,
                                               bi->num_bytes(),
                                               advice,
                                               NetworkLocationInfo(src_dn),
                                               favored_dns,
                                               live_dns,
                                               &containing_dns,
                                               &targets)) {
    VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
        << "Choose Replication target failed, src: " << src_dn->ip().ToString()
        << ", live_dns(not decommission): " << live_dns.size()
        << ", decommission_dns: " << decommission_dns.size()
        << ", containing_dns: " << containing_dns.size()
        << ", placement advice: " << advice.ToString()
        << ", path: " << src_path;
    MFC(metrics_.repl_work_choose_target_failed_counter_)->Inc();
    return false;
  }
  if (FLAGS_log_block_transfer_detail ||
      VLOG_IS_ON(2) && (!decommission_dns.empty() ||
                        satisfied_placement_replicas < real_live_replicas)) {
    std::string favored_str;
    std::string decommission_str;
    std::string tgt_str;
    for (auto dn : favored_dns) {
      favored_str += dn->GetFullLocationString();
      favored_str += " ";
    }
    for (auto dn : decommission_dns) {
      decommission_str += dn->GetFullLocationString();
      decommission_str += " ";
    }
    for (auto dn_id : targets) {
      auto dn = datanode_manager_->GetDatanodeFromId(dn_id);
      tgt_str += dn->GetFullLocationString();
      tgt_str += " ";
    }
    LOG_IF(INFO, !decommission_dns.empty())
        << "ComputeReplicationWork for Decommission bi=" << blk.id
        << " decommission_dns[" << decommission_dns.size()
        << "]=" << decommission_str << " favored_dns[" << favored_dns.size()
        << "]=" << favored_str << " src_dn=" << src_dn->GetFullLocationString()
        << " ->"
        << " target_dns[" << targets.size() << "]=" << tgt_str << " | "
        << "use_decommission="
        << (src_dn->IsDecommissionInProgress() ? "true" : "false");
    LOG_IF(INFO,
           decommission_dns.empty() &&
               satisfied_placement_replicas < real_live_replicas)
        << "ComputeReplicationWork for MoveDC. bi=" << blk.id
        << ", placement_advice: " << advice.ToString()
        << ", expected_placement: " << expected_placement_str
        << ", satisfied_replica: " << satisfied_placement_replicas
        << ", real_live_replica: " << real_live_replicas
        << ", expected: " << expected_replicas
        << " src_dn=" << src_dn->GetFullLocationString() << " ->"
        << " target_dns[" << targets.size() << "]=" << tgt_str;
  }
  VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
      << "Choose Replication blk_id: " << blk.id
      << ", targets: " << targets.size();
  for (auto iter = targets.begin(); iter != targets.end();) {
    auto&& dn_id = *iter;
    auto&& tgt_dn = datanode_manager_->GetDatanodeFromId(dn_id);
    VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
        << "Choose target, blk: " << blk.id << ", "
        << src_dn->GetFullLocationString() << " -> "
        << tgt_dn->GetFullLocationString();

    // cross dc Throttling & Metrics
    auto blk_bytes = bi->num_bytes();
    auto src_dc = GetGlobalDataCenterTable().Name(src_dn->dc());
    auto tgt_dc = GetGlobalDataCenterTable().Name(tgt_dn->dc());
    if (src_dc != tgt_dc) {
      // lock param cross_dc_throttle_config_lock_
      std::lock_guard<std::mutex> throttle_guard(
          cross_dc_throttle_config_mutex_);
      if (*now_cross_dc_bytes + blk_bytes >= max_cross_dc_bytes &&
          pri_enum != UnderReplicatedBlocks::Priority::kHighestPriority) {
        VLOG_OR_IF(4, FLAGS_log_block_transfer_detail)
            << "Throttling Cross DC,"
            << " cross_dc_bytes_limit=" << max_cross_dc_bytes
            << " now_cross_dc_bytes=" << *now_cross_dc_bytes
            << " blk_bytes=" << blk_bytes;
        metrics_.IncBlockTransTraffic(src_dc, tgt_dc, "throttling", blk_bytes);
        iter = targets.erase(iter);
        continue;
      } else if (!decommission_dns.empty() &&
                 *decommission_now_cross_dc_bytes + blk_bytes >=
                     decommission_max_cross_dc_bytes &&
                 pri_enum !=
                     UnderReplicatedBlocks::Priority::kHighestPriority) {
        VLOG_OR_IF(4, FLAGS_log_block_transfer_detail)
            << "Decommission Throttling Cross DC,"
            << " cross_dc_bytes_limit=" << decommission_max_cross_dc_bytes
            << " now_cross_dc_bytes=" << *decommission_now_cross_dc_bytes
            << " blk_bytes=" << blk_bytes;
        metrics_.IncBlockTransTraffic(
            src_dc, tgt_dc, "decommission_throttling", blk_bytes);
        iter = targets.erase(iter);
        continue;
      } else {
        *now_cross_dc_bytes += blk_bytes;
        if (!decommission_dns.empty()) {
          *decommission_now_cross_dc_bytes += blk_bytes;
          metrics_.IncBlockTransTraffic(
              src_dc, tgt_dc, "decommission_true", blk_bytes);
        }
        metrics_.IncBlockTransTraffic(src_dc, tgt_dc, "true", blk_bytes);
      }
    } else {
      metrics_.IncBlockTransTraffic(src_dc, tgt_dc, "false", blk_bytes);
      if (!decommission_dns.empty()) {
        metrics_.IncBlockTransTraffic(
            src_dc, tgt_dc, "decommission_false", blk_bytes);
      }
    }
    iter++;
  }
  if (targets.empty()) {
    MFC(metrics_.repl_work_cross_dc_throttled_counter_)->Inc();
    *retryable = true;
    return false;
  }
  if (!IsPopulatingReplicationQueues()) {
    *retryable = true;
    return false;
  }
  sw.NextStep(metrics_.repl_work_add_to_be_replicated_time_);
  s->IncrementReplicas(blk.id, targets);

  auto io_priority = UnderReplicatedBlocks::ToIOPriority(pri_enum);
  AddBlockToBeReplicated(src_dn, blk, targets, io_priority);
  MFC(metrics_.repl_work_all_scheduled_counter_)->Inc();
  if (!decommission_dns.empty()) {
    MFC(metrics_.repl_work_scheduled_decommission_counter_)->Inc();
  } else if (satisfied_placement_replicas < real_live_replicas) {
    MFC(metrics_.repl_work_scheduled_move_dc_counter_)->Inc();
  } else {
    MFC(metrics_.repl_work_scheduled_normal_counter_)->Inc();
  }

  if (VLOG_IS_ON(8) || FLAGS_log_block_transfer_detail) {
    std::stringstream debug;
    debug << "[ProcessBlockNeedReplicate] result: ";
    debug << "blk: " << blk.id;
    debug << ", real_live_replica: " << real_live_replicas;
    debug << ", num_effective_replicas: " << num_effective_replicas;
    debug << ", satisfied_replica: " << satisfied_placement_replicas;
    debug << ", expected_placement: " << expected_placement_str;
    debug << ", num_additional_replicas: " << num_additional_replicas;
    debug << ", rn=" << rn;
    debug << ", block_info: " << bi->ToString();
    INode dinode;
    if (ns_->GetINode(bi->inode_id(), &dinode)) {
      debug << ", corresponding file: " << dinode.ShortDebugString();
    }
    LOG(INFO) << debug.str();
  }
  return true;
}

size_t BlockManager::ComputeReplicationWork(size_t blocks_to_process) {
  auto begin = std::chrono::steady_clock::now();
  auto last = begin;
  const int64_t limit_bytes_per_second =
      FLAGS_blockmap_replication_cross_dc_limit_GBps * 1024 * 1024 * 1024;
  int64_t max_cross_dc_bytes =
      limit_bytes_per_second / 1000 * FLAGS_replication_monitor_interval_ms;
  const int64_t decommission_limit_bytes_per_second =
      FLAGS_decommission_cross_dc_limit_GBps * 1024 * 1024 * 1024;
  int64_t decommission_max_cross_dc_bytes =
      decommission_limit_bytes_per_second / 1000 *
      FLAGS_replication_monitor_interval_ms;
  int64_t now_cross_dc_bytes = 0;
  int64_t decommission_now_cross_dc_bytes = 0;
  auto blocks_to_replicate = needed_replications_.ChooseUnderReplicatedBlocks(
      blocks_to_process, FLAGS_repl_work_max_process_missing_block);
  MFH(metrics_.repl_work_choose_under_repl_blks_time_)
      ->Update(std::chrono::duration_cast<std::chrono::microseconds>(
                   std::chrono::steady_clock::now() - begin)
                   .count());

  std::atomic<uint64_t> num_scheduled_work(0);
  std::atomic<uint64_t> num_processing_blocks(0);
  VLOG_OR_IF(10, FLAGS_log_block_transfer_detail)
      << "Throttling Cross DC Config:"
      << " FLAGS_replication_monitor_interval_ms="
      << FLAGS_replication_monitor_interval_ms
      << " FLAGS_blockmap_replication_cross_dc_limit_GBps="
      << FLAGS_blockmap_replication_cross_dc_limit_GBps
      << " FLAGS_decommission_cross_dc_limit_GBps="
      << FLAGS_decommission_cross_dc_limit_GBps
      << " max_cross_dc_bytes=" << max_cross_dc_bytes
      << " decommission_max_cross_dc_bytes=" << decommission_max_cross_dc_bytes;

  // Cached for loaded file per round, so clear it before processing
  replicate_block_loaded_inodes_cache_.clear();
  for (size_t pri = 0; pri < UnderReplicatedBlocks::kPriorityCount; ++pri) {
    VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
        << "Replicate blocks, pri: " << pri
        << ", count: " << blocks_to_replicate[pri].size();
    num_processing_blocks.fetch_add(blocks_to_replicate[pri].size());
    for (auto& blk : blocks_to_replicate[pri]) {
      RETURN_IF_NO_ACTIVE_VALUE(0);
      blk_replication_workers_->AddTask([&, pri]() -> bool {
        RETURN_IF_NO_ACTIVE_VALUE(true);
        auto now = std::chrono::steady_clock::now();
        auto duration_begin_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(now - begin)
                .count();
        {
          // lock param last & max_cross_dc_bytes &
          // decommission_max_cross_dc_bytes
          std::lock_guard<std::mutex> throttle_guard(
              cross_dc_throttle_config_mutex_);
          auto duration_ms =
              std::chrono::duration_cast<std::chrono::milliseconds>(now - last)
                  .count();
          if (duration_ms > 1000) {
            max_cross_dc_bytes += duration_ms * limit_bytes_per_second / 1000;
            decommission_max_cross_dc_bytes +=
                duration_ms * decommission_limit_bytes_per_second / 1000;
            VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
                << "Throttling Cross DC Config Update:"
                << " duration_from_last_ms=" << duration_ms
                << " duration_from_begin_ms=" << duration_begin_ms
                << " max_cross_dc_bytes=" << max_cross_dc_bytes
                << " decommission_max_cross_dc_bytes="
                << decommission_max_cross_dc_bytes;
            last = now;
          }
        }

        RETURN_IF_NO_ACTIVE_VALUE(true);
        bool retryable = false;
        bool scheduled =
            ProcessBlockNeedReplicate(blk,
                                      pri,
                                      max_cross_dc_bytes,
                                      decommission_max_cross_dc_bytes,
                                      &now_cross_dc_bytes,
                                      &decommission_now_cross_dc_bytes,
                                      &retryable);
        // move back to under_replicated_blocks if retryable
        if (FLAGS_move_back_need_replication_queue && !scheduled && retryable) {
          if (needed_replications_.MoveBack(
                  blk, static_cast<UnderReplicatedBlocks::Priority>(pri))) {
            VLOG_OR_IF(5, FLAGS_log_block_transfer_detail)
                << "block has been moved back into needed replication "
                   "queue, pri="
                    << pri << ", id=" << blk.id;
            MFC(metrics_.repl_work_move_back_to_queue_counter_)->Inc();
          } else {
            VLOG_OR_IF(5, FLAGS_log_block_transfer_detail)
                << "trying to move back block into needed replication "
                   "queue but already exists. block="
                    << blk.id << ", old priority=" << pri;
          }
        } else if (scheduled) {
          num_scheduled_work += 1;
        }
        num_processing_blocks -= 1;
        return true;
      });
    }
  }

  uint64_t res = num_processing_blocks;
  while (res > 0) {
    VLOG_OR_IF(8, FLAGS_log_block_transfer_detail)
        << "ComputeReplicationWork pending blocks " << res;
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    res = num_processing_blocks;

    RETURN_IF_NO_ACTIVE_VALUE(0);
  }

  LOG(INFO) << "ComputeReplicationWork done"
            << ", blocks_to_process: " << blocks_to_process
            << ", num_scheduled_work: " << num_scheduled_work;

  return num_scheduled_work;
}

void BlockManager::AddBlockToBeReplicated(
    DatanodeInfoPtr src_dn,
    const Block& block,
    const std::vector<DatanodeID>& targets,
    IOPriority priority) {
  std::unique_lock<RWSpinlock> lock(rwlock_);
  auto iter = dn_replication_queues_.find(src_dn->id());
  if (iter == dn_replication_queues_.end()) {
    iter = dn_replication_queues_
               .emplace(src_dn->id(), std::deque<BlockTargetPair>())
               .first;
  }
  VLOG(8) << "Add replicated block queue, dn: " << src_dn->ip().ToString()
          << ", blk: " << block.id << ", taget size: " << targets.size()
          << ", priority: " << priority;
  iter->second.emplace_back(BlockTargetPair{block, targets, priority});
}

// NOTICE: slice lock should already been held
bool BlockManager::InvalidateCorruptBlock(BlockInfo* bi,
                                          DatanodeID dn_id,
                                          const Block& blk,
                                          int live_dn,
                                          int stale_dn) {
  if (stale_dn > 0) {
    PostponeBlock(bi->id());
  } else if (live_dn > 0 || bi->IsPersisted()) {
    auto& s = slice(blk.id);
    s->AddToInvalidateBlock(dn_id, blk, "InvalidateCorruptBlock");
    return true;
  }
  return false;
}

void BlockManager::ProcessPendingReplications() {
  for (auto& s : slices_) {
    RETURN_IF_NO_ACTIVE();
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RETURN_IF_NO_ACTIVE();

    std::vector<BlockID> timeout_blocks;
    s->TimedOutBlocks(&timeout_blocks);
    RETURN_IF_NO_ACTIVE();

    for (auto blk_id : timeout_blocks) {
      RETURN_IF_NO_ACTIVE();
      BlockInfoGuard bi_guard(s.get(), blk_id, false);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      if (!bi) {
        continue;
      }

      INode inode;
      if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
          !ns_->GetINode(bi->inode_id(), &inode)) {
        inode.set_id(kInvalidINodeId);
      }
      auto expect_replica = BlockExpectedReplica(*bi, &inode);
      auto rn = CountReplica(bi);
      if (IsNeededReplication(bi, &inode, expect_replica, rn.live)) {
        needed_replications_.Add(
            bi, rn.live, expect_replica, "Process pending replications");
      }
    }
  }
}

void BlockManager::RescanPostponedMisreplicatedBlocks() {
  // copy out to avoid acquiring slice lock before
  // releasing the global rwlock in case of potential deadlock
  std::unordered_set<BlockID> copied_postponed_blocks;
  auto start_time = std::chrono::system_clock::now();
  int64_t blocks_per_rescan = FLAGS_blockmap_num_postponed_blocks_rescan;
  int64_t base = 0;
  {
    RETURN_IF_NO_ACTIVE();
    std::unique_lock<RWSpinlock> lock(rwlock_);
    RETURN_IF_NO_ACTIVE();
    if (postponed_misreplicated_blocks_.empty()) {
      return;
    }
    int64_t start_index = 0;
    int64_t start_blocks_num =
        static_cast<int64_t>(postponed_misreplicated_blocks_.size());
    base = start_blocks_num - blocks_per_rescan;
    if (base > 0) {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<> dis(0, base);
      start_index = dis(gen);
    }
    auto iter = postponed_misreplicated_blocks_.begin();
    for (int64_t i = 0; i < start_index; ++i) {
      ++iter;
    }
    for (int64_t i = 0;
         i < blocks_per_rescan && iter != postponed_misreplicated_blocks_.end();
         ++i, iter++) {
      copied_postponed_blocks.emplace(*iter);
    }
    // we can safely release the global rwlock and then
    // begin to process postpone blocks
  }

  RETURN_IF_NO_ACTIVE();
  auto expected_to_process = copied_postponed_blocks.size();
  for (auto itr = copied_postponed_blocks.begin();
       itr != copied_postponed_blocks.end();) {
    RETURN_IF_NO_ACTIVE();
    auto& s = slice(*itr);
    std::shared_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    RETURN_IF_NO_ACTIVE();

    BlockInfoGuard bi_guard(s.get(), *itr, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      itr++;
      continue;
    }
    INode inode;
    if (!ns_->GetINode(bi->inode_id(), &inode)) {
      itr++;
      continue;
    }
    RETURN_IF_NO_ACTIVE();
    auto res = ProcessMisReplicatedBlock(bi, inode);
    if (res != MisReplicationResult::kPostPone) {
      itr++;
      continue;
    }
    itr = copied_postponed_blocks.erase(itr);
  }
  RETURN_IF_NO_ACTIVE();

  size_t left = 0;
  if (expected_to_process != copied_postponed_blocks.size()) {
    std::unique_lock<RWSpinlock> guard(rwlock_);
    for (auto pb : copied_postponed_blocks) {
      postponed_misreplicated_blocks_.erase(pb);
    }
    left = postponed_misreplicated_blocks_.size();
  }
  auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(
      std::chrono::system_clock::now() - start_time);
  LOG(INFO) << "Rescan of postponedMisreplicatedBlocks completed in "
            << elapsed.count() << "us. " << left << " blocks are left. "
            << copied_postponed_blocks.size() << " blocks are removed.";
}

void BlockManager::PostponeBlock(BlockID blk_id) {
  std::unique_lock<RWSpinlock> guard(rwlock_);
  postponed_misreplicated_blocks_.emplace(blk_id);
}

// slice write lock should already been held
bool BlockManager::RemoveBlock(BlockID blk_id) {
  return RemoveBlock(blk_id, slice(blk_id).get());
}

// slice write lock should already been held
bool BlockManager::RemoveBlock(BlockID blk_id, BlockMapSlice* slice) {
  slice->EraseCorruptBlock(blk_id);
  slice->EraseSealedBlock(blk_id);
  return slice->RemoveBlock(blk_id);
}

std::pair<size_t, std::deque<BlockID>> BlockManager::GetCorruptBlockIDs(
    int64_t offset,
    int64_t limit) {
  if (offset <= 0) offset = 0;
  if (limit <= 0) limit = INT64_MAX;

  size_t total = 0;
  std::deque<BlockID> v;
  {
    int64_t index = 0;
    int64_t count = 0;
    for (auto& s : slices_) {
      std::shared_lock<BlockMapSlice> guard(*s);
      auto c = s->GetCorruptBlock();
      total += s->GetCorruptBlockNum();

      if (c == nullptr) {
        continue;
      }
      for (auto itr = c->begin(); itr != c->end(); ++itr) {
        if (index < offset) {
          index++;
          continue;
        }
        if (count < limit) {
          v.emplace_back(itr->first);
          count++;
        } else {
          break;
        }
      }
    }
  }
  return {total, v};
}

std::pair<size_t, std::deque<BlockID>> BlockManager::GetSealedBlockIDs(
    int64_t offset,
    int64_t limit) {
  if (offset <= 0) offset = 0;
  if (limit <= 0) limit = FLAGS_blockmap_sealed_limit_per_slice;
  size_t total = 0;
  std::deque<BlockID> v;
  {
    int64_t index = 0;
    int64_t count = 0;
    for (auto& s : slices_) {
      std::shared_lock<BlockMapSlice> guard(*s);
      auto c = s->GetSealedBlock();
      total += s->GetSealedBlockNum();

      if (c == nullptr) {
        continue;
      }
      for (auto itr = c->begin(); itr != c->end(); ++itr) {
        if (index < offset) {
          index++;
          continue;
        }
        if (count < limit) {
          v.emplace_back(itr->first);
          count++;
        } else {
          break;
        }
      }
    }
  }
  return {total, v};
}

std::pair<size_t, std::deque<BlockID>> BlockManager::GetTruncatableBlockIDs(
    DatanodeID dn_id,
    int64_t offset,
    int64_t limit) {
  if (offset <= 0) offset = 0;
  if (limit <= 0) limit = FLAGS_blockmap_truncatable_limit_per_dn;
  size_t total = 0;
  std::deque<BlockID> v;
  {
    int64_t index = 0;
    int64_t count = 0;
    for (auto& s : slices_) {
      std::shared_lock<BlockMapSlice> guard(*s);
      auto c = s->PeekTruncatableBlock(dn_id);
      total += s->GetTruncatableBlockNum(dn_id);

      if (c == nullptr) {
        continue;
      }
      for (auto itr = c->begin(); itr != c->end(); ++itr) {
        if (index < offset) {
          index++;
          continue;
        }
        if (count < limit) {
          v.emplace_back(itr->id);
          count++;
        } else {
          break;
        }
      }
    }
  }
  return {total, v};
}

void BlockManager::AddBlocksToInvalidate(const std::vector<BlockProto>& blocks,
                                         const std::string& reason) {
  for (const BlockProto& block : blocks) {
    VLOG(10) << "Add to invalidate block: " << block.ShortDebugString();
    auto& s = slice(block.blockid());
    std::unique_lock<BlockMapSlice> guard(*s);
    s->TellLockHolder(__FILE__, __LINE__);
    BlockInfoGuard bi_guard(s.get(), block.blockid(), false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (!bi) {
      continue;
    }
    AddToInvalidate(block, reason);
  }
}

// NOTICE slice lock should already been held
void BlockManager::AddToInvalidate(const BlockProto& block,
                                   const std::string& reason) {
  RETURN_IF_NO_ACTIVE();

  auto& s = slice(block.blockid());
  BlockInfoGuard bi_guard(s.get(), block.blockid(), false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  if (!bi) {
    return;
  }

  DatanodeID* storages = nullptr;
  std::vector<DatanodeID> t;
  size_t count = 0;
  if (bi->HasBeenComplete()) {
    storages = bi->storage_ids();
    count = bi->size();
  } else {
    auto uc = s->GetUcInternal(block.blockid());
    t = uc->GetStorages();
    storages = &(t[0]);
    count = t.size();
  }

  RETURN_IF_NO_ACTIVE();
  for (size_t i = 0; i < count; ++i) {
    s->AddToInvalidateBlock(storages[i], Block(block), reason);
  }
}

void BlockManager::AddToRecover(BlockID block_id,
                                DatanodeID dn_id,
                                bool close_file) {
  if (dn_id != kInvalidDatanodeID) {
    std::unique_lock<RWSpinlock> lock(rwlock_);
    auto it = to_recover_.find(dn_id);
    if (it == to_recover_.end()) {
      it = to_recover_
               .emplace(
                   std::make_pair(dn_id, std::unordered_map<uint64_t, bool>()))
               .first;
    }
    it->second[block_id] = close_file;
  }
}

// NOTICE: slice lock should already been held
void BlockManager::UpdateNeededReplications(const BlockInfo* bi,
                                            int cur_replicas_delta,
                                            int expected_replicas_delta,
                                            const std::string& reason,
                                            const DatanodeID dn_id) {
  RETURN_IF_NO_ACTIVE();

  INode inode;
  if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
      !ns_->GetINode(bi->inode_id(), &inode)) {
    inode.set_id(kInvalidINodeId);
  }
  auto rn = CountReplica(bi);
  auto cur_expected_replicas = BlockExpectedReplica(*bi, &inode);
  Block blk(bi->id(), bi->num_bytes(), bi->gs());
  if (IsNeededReplication(bi, &inode, cur_expected_replicas, rn.live)) {
    needed_replications_.Update(bi,
                                rn.live,
                                cur_expected_replicas,
                                cur_replicas_delta,
                                expected_replicas_delta,
                                reason,
                                dn_id);
  } else {
    int old_replicas = rn.live - cur_replicas_delta;
    int old_expected_replicas = cur_expected_replicas - expected_replicas_delta;
    needed_replications_.Remove(blk, old_replicas, old_expected_replicas);
  }
}

// NOTICE: slice lock should already been held
ReplicaNum BlockManager::CountReplica(const BlockInfo* bi) {
  uint8_t live = 0;
  uint8_t corrupt = 0;
  uint8_t excess = 0;
  uint8_t stale = 0;
  uint8_t decommission = 0;
  auto& s = slice(bi->id());
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn = datanode_manager_->GetDatanodeFromId(sid);
    CHECK_NOTNULL(dn);
    if (dn->content_stale()) {
      ++stale;
    }
    if (dn->IsDecommissionInProgress()) {
      ++decommission;
    }
    if (s->IsBlockCorrupt(bi->id(), sid)) {
      ++corrupt;
    } else if (s->IsBlockExcess(bi->id(), sid)) {
      ++excess;
    } else {
      ++live;
    }
  }

  return ReplicaNum{live, corrupt, excess, stale, decommission};
}

// NOTICE: slice lock should already been held
uint8_t BlockManager::GetDatanodeContentStale(const BlockInfo* bi) {
  uint8_t stale = 0;
  for (size_t i = 0; i < bi->size(); ++i) {
    auto sid = bi->storage_id(i);
    auto dn = datanode_manager_->GetDatanodeFromId(sid);
    CHECK_NOTNULL(dn);
    if (dn->content_stale()) {
      ++stale;
    }
  }
  return stale;
}

// NOTICE: slice write lock should already been held
bool BlockManager::AddStoredBlock(DatanodeID dn_id,
                                  const Block& blk,
                                  ReplicaNum* rn,
                                  DatanodeID del_node_hint) {
  auto& s = slice(blk.id);
  BlockInfoGuard bi_guard(s.get(), blk.id, false);
  BlockInfo* bi = bi_guard.GetBlockInfo();
  VLOG(12) << "[ProcessReportResult" << blk << "]:"
           << " dn_id=" << dn_id
           << " bi is null=" << static_cast<int>(bi == nullptr);
  if (!bi) {
    return false;
  }

  // TODO(ranpanfeng)
  // Referring to DatanodeStorageInfo.java$AddBlockResult,
  // there maybe exist risk.
  bool already_exist = false;
  bi = s->AddStorage(bi, dn_id, &already_exist);

  s->RemoveFromCorruptBlock(blk.id, dn_id);
  s->RemoveFromSealedBlock(blk.id, dn_id);

  if (already_exist) {
    if (blk.gs != kBlockProtocolV2GenerationStamp) {
      // v1
      VLOG(12) << "Block B" << bi->id() << "already exist, skip";
      return false;
    } else {
      VLOG(12) << "Block B" << bi->id() << "already exist, but BlockProtocolV2";
    }
  }

  *rn = CountReplica(bi);
  auto num_current_replica = rn->live + s->NumReplicas(blk.id);

  VLOG(12) << "[ProcessReportResult" << blk << "]:" << *rn
           << " num_current_replica=" << static_cast<int>(num_current_replica)
           << " uc_state=" << static_cast<int>(bi->uc_state());

  // Complete block when # of its replicas is enough to tolerate data loss
  // later after it has been committed by the client at first.
  auto prev_state = BlockUCState::kComplete;
  auto is_complete = true;
  if (bi->IsCommitted() && rn->live >= FLAGS_dfs_replication_min) {
    is_complete = s->CompleteBlock(bi, false, &prev_state);
    if (is_complete) {
      DANCENN_LOCKED_BLOCKLIFECYCLE_LOG(bi->id(),
                                        "\"blk_id\":%" PRIu64
                                        ","
                                        "\"op\":\"complete\","
                                        "\"gs\":%" PRIu64
                                        ","
                                        "\"numbytes\":%d,"
                                        "\"status\":\"complete\","
                                        "\"prev_status\":\"%s\"}",
                                        bi->id(),
                                        bi->gs(),
                                        bi->num_bytes(),
                                        GetStateStr(prev_state).c_str());
    }
  } else {
    is_complete = false;
  }

  // # of safe block should increment when replicas of either
  // committed block or complete block is reported.
  // case#1: for committed block, when state of a block replica
  //  turns to FINALIZED from RBW
  // case#2: for complete block, when a complete block is
  //  reported to a NameNode that restarted before a while.
  // issue: duplicated block reports maybe trigger
  //  # of safe block spurious incrementing
  if (is_complete) {
    if (prev_state != BlockUCState::kPersisted && !bi->IsPersisted()) {
      safemode_->IncrementSafeBlockCount(num_current_replica);
    }
    s->DecrementReplicas(blk.id, dn_id);
  }

  // do not process replication during uc state
  if (!bi->HasBeenCommitted()) {
    do {
      if (!FLAGS_clear_corrupt_block_for_uc_block) {
        break;
      }

      auto corrupted = s->GetCorruptBlock(blk.id);
      if (corrupted == nullptr) {
        break;
      }

      auto uc = s->GetUcInternal(blk.id);
      if (!uc) {
        break;
      }
      auto uc_replica_number = uc->NumReportedStorages();

      INode inode;
      if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
          !ns_->GetINode(bi->inode_id(), &inode)) {
        inode.set_id(kInvalidINodeId);
      }
      auto expected_replication = BlockExpectedReplica(*bi, &inode);

      if (uc_replica_number < expected_replication) {
        break;
      }

      // remove corrupt
      for (const auto d : *corrupted) {
        ProcessCorruptReplica(d.first, d.second, "AddStoredBlock");
      }
    } while (false);

    return true;
  }

  // do not try to handle over/under-replicated blocks during first safe mode
  if (IsPopulatingReplicationQueues()) {
    INode inode;
    if (!FLAGS_block_expected_replica_determined_by_inode || ns_ == nullptr ||
        !ns_->GetINode(bi->inode_id(), &inode)) {
      inode.set_id(kInvalidINodeId);
    }
    auto expected_replication = BlockExpectedReplica(*bi, &inode);
    auto cur_replica_delta = 1;
    if (!IsNeededReplication(
            bi, &inode, expected_replication, num_current_replica)) {
      needed_replications_.Remove(
          bi->blk(), num_current_replica, expected_replication);
    } else {
      UpdateNeededReplications(bi, cur_replica_delta, 0, "AddStoredBlock");
    }

    // Decommission has not effect on complete block
    if (num_current_replica > expected_replication) {
      ProcessOverReplicatedBlock(
          bi, expected_replication, dn_id, del_node_hint);
    }

    // If the file replication has reached desired value or is persisted
    // we can remove any corrupt replicas the block may have
    auto corrupted = s->GetCorruptBlock(blk.id);
    if (corrupted != nullptr &&
        (rn->live >= expected_replication || bi->IsPersisted())) {
      for (const auto d : *corrupted) {
        ProcessCorruptReplica(d.first, d.second, "AddStoredBlock");
      }
    }
  }
  return true;
}

// IsPopulatingReplicationQueues() should have been check
void BlockManager::ProcessCorruptReplica(DatanodeID dn_id,
                                         const Block& reported_block,
                                         const std::string& reason) {
  auto& s = slice(reported_block.id);
  BlockInfoGuard bi_guard(s.get(), reported_block.id, true);
  BlockInfo* stored_block = bi_guard.GetBlockInfo();

  VLOG(10) << "ProcessCorruptReplica" << " block_id=" << reported_block.id
           << " dn_id=" << dn_id << " reason=" << reason
           << " stored_block=" << stored_block->blk().ToString()
           << " reported_block=" << reported_block.ToString();

  // check if input replica(dn_id) is corrupt
  if (!s->IsBlockCorrupt(reported_block.id, dn_id)) {
    LOG(ERROR) << "replica is not corrupt. block=" << reported_block.id
               << ", dn_id=" << dn_id
               << ", process corrupt replica reason=" << reason
               << "; will skip processing corrupt replica.";
    return;
  }
  INode inode;
  if (ns_ == nullptr || !ns_->GetINode(stored_block->inode_id(), &inode)) {
    inode.set_id(kInvalidINodeId);
  }
  auto rn = CountReplica(stored_block);
  uint32_t expected_rep =
      BlockExpectedReplica(*stored_block, &inode);
  VLOG(10) << "expected_rep=" << expected_rep;

  bool has_enough_live_replicas = rn.live >= expected_rep;
  bool min_replication_satisfied = rn.live >= FLAGS_dfs_replication_min;
  bool has_more_corrupt_replicas =
      min_replication_satisfied &&
      (rn.live + rn.corrupt) > expected_rep;
  bool corrupted_during_write =
      min_replication_satisfied && stored_block->gs() > reported_block.gs;

  int live_dn = rn.live;
  // direct remove uc block during write
  do {
    if (!FLAGS_clear_corrupt_block_for_uc_block) {
      break;
    }

    auto corrupted = s->GetCorruptBlock(reported_block.id);
    if (corrupted == nullptr) {
      break;
    }
    auto corrupt_num = static_cast<int>(corrupted->size());
    VLOG(10) << "corrupt_num=" << corrupt_num;

    auto uc = s->GetUcInternal(reported_block.id);
    if (!uc) {
      break;
    }
    auto uc_replica_number = uc->NumReportedStorages();
    VLOG(10) << "uc_replica_number=" << uc_replica_number;

    int safer_replica_num = 0;
    for (auto& kv : uc->expected_locations()) {
      if (stored_block->gs() <= kv.second.gs &&
          stored_block->num_bytes() <= kv.second.nbytes) {
        safer_replica_num++;
      }
    }
    VLOG(10) << "safer_replica_num=" << safer_replica_num;

    // if we have enough replicas, we can remove corrupt replicas
    if (safer_replica_num < expected_rep) {
      break;
    }

    live_dn = safer_replica_num;
    corrupted_during_write = true;
    VLOG(10) << "corrupted_during_write = true";
  } while (false);

  bool is_persisted = stored_block->IsPersisted();
  // case 1: have enough number of live replicas
  // case 2: corrupted replicas + live replicas > Replication factor
  // case 3: Block is marked corrupt due to failure while writing. In this
  //         case genstamp will be different than that of valid block.
  // case 4: block is persisted
  // In all these cases we can delete the replica.
  // In case of 3, rbw block will be deleted and valid block can be replicated
  if (has_enough_live_replicas || has_more_corrupt_replicas ||
      corrupted_during_write || is_persisted) {
    // the block is over-replicated so invalidate the replicas immediately
    s->AddToExcess(reported_block.id, dn_id);
    InvalidateCorruptBlock(
        stored_block, dn_id, reported_block, live_dn, rn.stale);
  } else if (IsPopulatingReplicationQueues()) {
    // add the block to neededReplication
    UpdateNeededReplications(stored_block, -1, 0, reason, dn_id);
  }
}

std::string BlockManager::GetDNIPStr(
    const std::vector<dancenn::DatanodeID>& dns) {
  std::string ip_str = "";
  for (auto dn : dns) {
    ip_str += datanode_manager_->GetDatanodeIpFromId(dn);
    ip_str += ",";
  }
  return ip_str;
}

std::string BlockManager::GetStateStr(dancenn::BlockUCState state) {
  switch (state) {
    case BlockUCState::kPersisted:
      return "persisted";
    case BlockUCState::kComplete:
      return "complete";
    case BlockUCState::kCommitted:
      return "committed";
    case BlockUCState::kUnderConstruction:
      return "uc";
    case BlockUCState::kUnderRecovery:
      return "ur";
    default:
      return "unknow";
  }
}

// caller should task responsible for acquiring block map slice's lock
size_t BlockManager::ComputeSatisfiedPlacementReplicas(
    dancenn::BlockInfo* blk_info,
    std::unordered_map<std::string, int> expected_placement) {
  int result = 0;
  auto& s = slice(blk_info->id());
  auto dns = blk_info->storage_ids();
  for (size_t i = 0; i < blk_info->size(); ++i) {
    auto dn_id = dns[i];
    auto dn = datanode_manager_->GetDatanodeFromId(dns[i]);
    CHECK_NOTNULL(dn);
    if (dn->IsDecommissionInProgress() ||
        s->IsBlockCorrupt(blk_info->id(), dn_id) ||
        s->IsBlockExcess(blk_info->id(), dn_id)) {
      continue;
    } else {
      auto dc = dn->dc_name();
      if (expected_placement.find(dc) != expected_placement.end() &&
          expected_placement[dc] > 0) {
        expected_placement[dc]--;
        result += 1;
      } else if (expected_placement.find("") != expected_placement.end() &&
                 expected_placement[""] > 0) {
        // BlockPlacementDefault::GetExpectedPlacement return {"", rep_num} as
        // result, the branch is supposed to handle this special case.
        // "" can represent any DC, but all 'rep_num' replicas should be placed
        // in same DC.
        expected_placement[dc] = expected_placement[""] - 1;
        expected_placement[""] = 0;
        result += 1;
      }
    }
  }
  VLOG_OR_IF(10, FLAGS_log_block_transfer_detail)
      << "Block " << blk_info->id() << " has " << result
      << " replicas that satisfied expect placement";
  return result;
}

void BlockManager::HandleDeprecatingBlocks() {
  for (int i = 0; i < FLAGS_del_depring_blks_recycle_times; ++i) {
    BREAK_IF_HA_IN_TRANSITION();

    bool ret = depring_block_recycler_->Recycle();
    if (!ret) {
      break;
    }
  }
}

void BlockManager::HandleDeprecatedBlocks() {
  for (int i = 0; i < FLAGS_del_depred_blks_recycle_times; ++i) {
    BREAK_IF_HA_IN_TRANSITION();

    bool ret = depred_block_recycler_->Recycle();
    VLOG(10) << "after depred_block_recycler_->Recycle()";
    BREAK_IF_HA_IN_TRANSITION();

    depred_block_recycler_->ConsumeOpDelDepredBlksQueue();
    VLOG(10) << "after ConsumeOpDelDepredBlksQueue";
    if (!ret) {
      break;
    }
  }
}

int64_t BlockManager::GetCurrentTsInSec() {
  return std::chrono::duration_cast<std::chrono::seconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

bool BlockManager::DecodeBlocksV1(
    const RepeatedBlocks& blocks_encoded,
    int worker_num,
    std::vector<std::vector<BlkInfo>>* blocks_decoded,
    uint64_t* fin_num,
    uint64_t* uc_num) {
  if (blocks_encoded.size() < 2) {
    return false;
  }

  uint64_t nfin = blocks_encoded.Get(0);
  uint64_t nuc = blocks_encoded.Get(1);

  for (int i = 0; i < worker_num; i++) {
    blocks_decoded->at(i).reserve((nfin + nuc) / (worker_num + 1));
  }

  if (nfin) {
    uint64_t upper = 2 + nfin * 3;
    for (uint64_t i = 2; i < upper; i += 3) {
      uint64_t id = blocks_encoded.Get(i);
      uint64_t len = blocks_encoded.Get(i + 1);
      uint64_t gs = blocks_encoded.Get(i + 2);
      uint32_t worker_idx = (id & slice_mask_) % worker_num;
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      blocks_decoded->at(worker_idx).emplace_back(
          BlkInfo { id,
                    gs,
                    len,
                    ReplicaStateProto::FINALIZED,
                    scr });
    }
  }

  if (nuc) {
    uint64_t counter = 0;
    for (uint64_t i = 2 + (nfin + 1) * 3; i < blocks_encoded.size();
         i += 4) {
      if (counter++ >= nuc) {
        break;
      }
      uint64_t id = blocks_encoded.Get(i);
      uint64_t len = blocks_encoded.Get(i + 1);
      uint64_t gs = blocks_encoded.Get(i + 2);
      ReplicaStateProto replica_state =
          static_cast<ReplicaStateProto>(blocks_encoded.Get(i + 3));
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      uint32_t worker_idx = (id & slice_mask_) % worker_num;
      blocks_decoded->at(worker_idx).emplace_back(
          BlkInfo { id,
                    gs,
                    len,
                    replica_state,
                    scr });
    }
  }

  *fin_num = nfin;
  *uc_num = nuc;
  return true;
}

bool BlockManager::DecodeBlocksV2(
    const std::string& blocks_encoded,
    int worker_num,
    std::vector<std::vector<BlkInfo>>* blocks_decoded,
    uint64_t* fin_num,
    uint64_t* uc_num) {

  google::protobuf::io::ArrayInputStream
      array_input_strm(blocks_encoded.data(), blocks_encoded.size());
  google::protobuf::io::GzipInputStream
      gzip_input_strm(&array_input_strm);
  google::protobuf::io::CodedInputStream
      coded_input_strm(&gzip_input_strm);
  coded_input_strm.SetTotalBytesLimit(
      FLAGS_fbr_protobuf_stream_limit_mb << 20, 64UL << 20);
  BlockReportBlockInfoProtoV2 proto;
  bool ok = proto.ParseFromCodedStream(&coded_input_strm);
  if (!ok) {
    LOG(ERROR) << absl::StrFormat(
        "Failed to parse blocks to BlockReportBlockInfoProtoV2.");
    return false;
  }

  for (int i = 0; i < worker_num; i++) {
    blocks_decoded->at(i).reserve(proto.blocks_size() / (worker_num + 1));
  }

  uint64_t nfin = 0;
  uint64_t nuc = 0;
  for (int i = 0; i < proto.blocks_size(); i++) {
    auto& blk = proto.blocks(i);
    uint32_t worker_idx = (blk.block().blockid() & slice_mask_) % worker_num;
    StorageClassReportProto scr;
    scr.set_stcls(blk.storageclass());
    scr.set_pinned(blk.pinned());
    blocks_decoded->at(worker_idx).emplace_back(
        BlkInfo { blk.block().blockid(),
                  blk.block().genstamp(),
                  blk.block().numbytes(),
                  blk.replicastate(),
                  scr });
    if (blk.replicastate() == ReplicaStateProto::FINALIZED) {
      nfin++;
    } else {
      nuc++;
    }
  }

  *fin_num = nfin;
  *uc_num = nuc;
  return true;
}

Status BlockManager::DecodeBlocks(
    const cloudfs::datanode::StorageBlockReportProto& report,
    int worker_num,
    std::vector<std::vector<BlkInfo>>* blocks_decoded,
    uint64_t* fin_num,
    uint64_t* uc_num) {

  blocks_decoded->resize(worker_num);

  bool ok;
  switch (report.blocksformatversion()) {
    case cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1: {
      ok = DecodeBlocksV1(report.blocks(),
                          worker_num,
                          blocks_decoded,
                          fin_num,
                          uc_num);
      break;
    }
    case cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V2: {
      ok = DecodeBlocksV2(report.blocksv2(),
                          worker_num,
                          blocks_decoded,
                          fin_num,
                          uc_num);
      break;
    }
    default:
      std::string msg = absl::StrFormat(
          "Unexpected block format %d in FBR %s",
          report.blocksformatversion(), report.ShortDebugString());
      LOG(ERROR) << msg;
      return Status(JavaExceptions::kInvalidRequestException,
                    Code::kError,
                    msg);
  }
  if (!ok) {
    MFC(metrics_.fbr_decode_blocks_failed_)->Inc();
    std::string msg = absl::StrFormat(
        "Failed to parse blocks from StorageBlockReportProto %s.",
        report.ShortDebugString());
    LOG(ERROR) << msg;
    return Status(JavaExceptions::kInvalidRequestException,
                  Code::kError,
                  msg);
  }

  for (auto it = blocks_decoded->begin();
       it != blocks_decoded->end();
       it++) {
    it->shrink_to_fit();
  }

  return Status();
}

void BlockManager::ProcessBlockCheckDuringFastReport(BlockMapSlice* s,
                                                     const BlockID block_id,
                                                     DatanodeInfoPtr dn) {
  ProcessCheckWriteCacheINodeState(s, block_id, dn);
}

void BlockManager::CommitStorageClassReports(
    const std::string& dn_uuid,
    std::vector<std::pair<BlockID, StorageClassReportProto>>& replica_report,
    bool wait) {
  if (!FLAGS_enable_storage_class) {
    return;
  }

  // both active and standby shall update local reported result
  if (replica_report.empty()) {
    return;
  }

  // XXX cls==NONE indicates remove corresponding entry,
  // it shall NEVER be persisted into MetaStorage
  auto done = std::make_shared<SynchronizedClosure>();
  meta_storage_->UpdateStorageClassReportsAsync(dn_uuid,
                                                replica_report,
                                                done.get());
  done->Await();
  if (!done->status().IsOK()) {
    LOG(WARNING) << absl::StrFormat(
        "fail to update StorageClassReport on DN %s: %s",
        dn_uuid, done->status().ToString());
  }
}

void BlockManager::AddBlockReportCandidate(DatanodeID id) {
  block_report_manager_->AddDatanode(id);
}

void BlockManager::RemoveBlockReportCandidate(DatanodeID id) {
  block_report_manager_->RemoveDatanode(id);
}

void BlockManager::ResetBlockReportHistory(DatanodeID id) {
  block_report_manager_->ResetBlockReportRecord(id);
}

void BlockManager::InitBlockReport4Test(DatanodeID id) {
  block_report_manager_->InitFullBlockReport4Test(id);
}

bool BlockManager::TriggerBlockReport(DatanodeID id, bool fast) {
  auto dn = datanode_manager_->GetDatanodeFromId(id);
  if (!dn) {
    return false;
  }
  dn->LockForBlockReport();
  block_report_manager_->TriggerFullBlockReport(id, true, fast);
  dn->UnlockForBlockReport();
  return true;
}

void BlockManager::TriggerAllBlockReport() {
  block_report_manager_->ClearBlockReportRecord();
}

Status BlockManager::AddMergeBlockCmd(
    DatanodeID dn_id,
    const cloudfs::LocatedBlockProto& lb,
    const cloudfs::LocatedBlocksProto& old_blks) {
  MergeCmd cmd;
  cmd.mutable_block()->CopyFrom(lb.b());
  for (const auto& b : old_blks.blocks()) {
    auto p = cmd.add_oldblocks();
    p->CopyFrom(b);
  }
  if (!merge_cmd_mgr_.Add(dn_id, std::move(cmd))) {
    LOG(INFO) << "Failed to add merge cmd for block " << lb.ShortDebugString();
    return Status(Code::kFalse);
  }
  return Status::OK();
}

bool BlockManager::UpdateMergeBlockUCState(const Block& b, BlockUCState state) {
  if (state != BlockUCState::kComplete && state != BlockUCState::kPersisted) {
    LOG(ERROR) << "Unexpected state " << static_cast<int8_t>(state) << " for B"
               << b.id;
    return false;
  }

  auto& s = slice(b.id);
  std::unique_lock<BlockMapSlice> block_map_slice_lock(*s);
  s->TellLockHolder(__FILE__, __LINE__);

  BlockInfoGuard bi_guard(s.get(), b.id, true);
  BlockInfo* bi = bi_guard.GetBlockInfo();

  if (!bi) {
    LOG(ERROR) << "Failed to find bi for B" << b.id;
    return false;
  }

  if (!s->CommitBlock(bi, b)) {
    LOG(ERROR) << "Failed to commit block B" << b.id;
    return false;
  }

  if (!s->CompleteBlock(bi, true)) {
    LOG(ERROR) << "Failed to complete block B" << b.id;
    return false;
  }

  if (state == BlockUCState::kPersisted &&
      !s->PersistBlock(bi, ns_->blockpool_id(), b)) {
    LOG(ERROR) << "Failed to persist block B" << b.id;
    return false;
  }

  return true;
}

void BlockManager::UpdateBlockPinStatus(const INode& inode,
                                        const BlockID blk) {
  // Current replicas
  std::vector<std::string> dn_uuids;
  std::vector<StorageClassReportProto> reports;
  meta_storage_->GetStorageClassReports(blk, &dn_uuids, &reports);
  uint32_t pinned_replica = 0;
  for (size_t i = 0; i < dn_uuids.size(); i++) {
    if (reports[i].pinned()) {
      pinned_replica++;
    }
  }

  // Target replicas
  bool pin = inode.pin_status().pinned();
  uint32_t target_replica = inode.replication();
  if (!pin) {
    target_replica = 0;
  }

  for (size_t i = 0; i < dn_uuids.size(); i++) {
    if (reports[i].pinned() != pin) {
      DatanodeID dn_id = datanode_manager_->GetDatanodeInterId(dn_uuids[i]);
      BlockIdCommandProto cmd;
      if (target_replica > pinned_replica) {
        cmd.set_action(cloudfs::datanode::BlockIdCommandProto_Action_PIN);
        pinned_replica++;
      } else if (target_replica < pinned_replica) {
        cmd.set_action(cloudfs::datanode::BlockIdCommandProto_Action_UNPIN);
        pinned_replica--;
      } else {
        break;
      }
      cmd.set_blockpoolid(ns_->blockpool_id());
      cmd.add_blockids(blk);
      VLOG(8) << "UpdateBlockPinStatus generated cmds: "
              << cmd.ShortDebugString();
      blockid_cmd_mgr_.Add(dn_id, std::move(cmd));
    }
  }
}

uint32_t BlockManager::BlockExpectedReplica(const BlockInfo& bi,
                                            const INode* inode) {
  if (FLAGS_block_expected_replica_determined_by_inode) {
    if (inode == nullptr) {
      return bi.expected_rep();
    }
    if (inode->id() == kInvalidINodeId) {
      return bi.expected_rep();
    }
    return inode->replication();
  } else {
    return bi.expected_rep();
  }
}

uint32_t BlockManager::BlockExpectedReplica(const BlockInfoProto& bip,
                                            const INode* inode) {
  if (FLAGS_block_expected_replica_determined_by_inode) {
    if (inode == nullptr) {
      return bip.expected_rep();
    }
    if (inode->id() == kInvalidINodeId) {
      return bip.expected_rep();
    }
    return inode->replication();
  } else {
    return bip.expected_rep();
  }
}

}  // namespace dancenn
