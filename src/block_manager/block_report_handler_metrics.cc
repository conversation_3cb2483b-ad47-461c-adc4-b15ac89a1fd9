// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/01/28
// Description

#include "block_manager/block_report_handler_metrics.h"

#include <mutex>  // For std::once_flag, std::call_once.

namespace dancenn {

BlockReportHandlerMetrics& BlockReportHandlerMetrics::Instance() {
  static BlockReportHandlerMetrics m;
  static std::once_flag flag;
  std::call_once(flag, []() {
    auto center = MetricsCenter::Instance();
    auto ms = center->RegisterMetrics("BlockReportHandler");

    m.ibr_deleted_ops_ = ms->RegisterCounter("IBRO#type=Deleted");
    m.ibr_received_ops_ = ms->RegisterCounter("IBRO#type=Received");
    m.ibr_receiving_ops_ = ms->RegisterCounter("IBRO#type=Receiving");
    m.ibr_upload_ops_ = ms->RegisterCounter("IBRO#type=Upload");
    m.ibr_persist_ops_ = ms->RegisterCounter("IBRO#type=Persist");
    m.ibr_pufs_deleted_ops_ = ms->RegisterCounter("IBRO#type=PufsDeleted");

    m.ibr_total_time_ = ms->RegisterHistogram("IBRT#step=Total");
    m.ibr_storage_time_ = ms->RegisterHistogram("IBRT#step=ProcessStorage");
    m.ibr_deleted_time_ = ms->RegisterHistogram("IBRT#step=Deleted");
    m.ibr_received_time_ = ms->RegisterHistogram("IBRT#step=Received");
    m.ibr_receiving_time_ = ms->RegisterHistogram("IBRT#step=Receiving");
    m.ibr_upload_time_ = ms->RegisterHistogram("IBRT#step=Upload");
    m.ibr_persist_time_ = ms->RegisterHistogram("IBRT#step=Persist");
    m.ibr_pufs_deleted_time_ = ms->RegisterHistogram("IBRT#step=PufsDeleted");

    m.fbr_decode_raw_report_time_ =
        ms->RegisterHistogram("FbrTime#step=DecodeRawReport");
    m.fbr_make_tasks_time_ = ms->RegisterHistogram("FbrTime#step=MakeTasks");
    m.fbr_wait_callback_time_ =
        ms->RegisterHistogram("FbrTime#step=WaitCallback");
    m.fbr_callback_time_ = ms->RegisterHistogram("FbrTime#step=Callback");

    m.fbr_per_task_wait_execute_time_ =
        ms->RegisterHistogram("FbrPerTaskTime#step=WaitExecute");
    m.fbr_per_task_execute_time_ =
        ms->RegisterHistogram("FbrPerTaskTime#step=Execute");
    m.fbr_per_task_log_edit_time_ =
        ms->RegisterHistogram("FbrPerTaskTime#step=LogEdit");
    m.fbr_per_task_wait_callback_time_ =
        ms->RegisterHistogram("FbrPerTaskTime#step=WaitCallback");
    m.fbr_per_task_callback_time_ =
        ms->RegisterHistogram("FbrPerTaskTime#Step=Callback");
  });
  return m;
}

}  // namespace dancenn
