// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/blocks_scanner_listener.h"

#include <glog/logging.h>

#include <string>
#include <chrono>

#include "block_manager/block_manager.h"
#include "namespace/meta_scanner.h"

namespace dancenn {

bool BlocksScannerListener::PreScan() {
  scan_start_ = std::chrono::steady_clock::now();
  num_invalid_ = 0;
  num_under_replicated_ = 0;
  num_over_replicated_ = 0;
  num_postponed_ = 0;
  num_under_construction_ = 0;
  processed_ = 0;
  num_corrupt_ = 0;
  return true;
}

std::vector<INodeID> BlocksScannerListener::ScanIndexes() {
  return {kRootINodeId};
}

bool BlocksScannerListener::Handle(const std::string& full_path,
                                   const INode& inode) {
  (void) full_path;
  if (inode.type() == INode_Type_kFile) {
    for (auto &b : inode.blocks()) {
      Block blk(b);
      auto res = bm_->ProcessMisReplicatedBlock(blk, inode);
      switch (res) {
        case BlockManager::MisReplicationResult::kUnderReplicated:
          num_under_replicated_++;
          break;
        case BlockManager::MisReplicationResult::kOverReplicated:
          num_over_replicated_++;
          break;
        case BlockManager::MisReplicationResult::kInvalid:
          num_invalid_++;
          break;
        case BlockManager::MisReplicationResult::kPostPone:
          num_postponed_++;
          // We don't need a separate set to queue the postponed blocks, because
          // we have a periodical scanner.
          // postponeBlock(block);
          break;
        case BlockManager::MisReplicationResult::kCorrupt:
          num_corrupt_++;
          break;
        case BlockManager::MisReplicationResult::kUnderConstruction:
          num_under_construction_++;
          break;
        case BlockManager::MisReplicationResult::kOK:
          break;
        default:
          LOG(FATAL) << "Never happen, invalid misreplication result";
      }
      processed_++;
    }
  }
  return true;
}

void BlocksScannerListener::PostScan() {
  auto scan_end = std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      scan_end - scan_start_).count();

  std::stringstream postpone_ss;
  if (num_postponed_ > 0) {
    postpone_ss << " (" << num_postponed_ << " postponed)";
  }
  LOG(INFO) << "Total number of blocks            = " << processed_;
  LOG(INFO) << "Number of invalid blocks          = " << num_invalid_;
  LOG(INFO) << "Number of corrupt blocks          = " << num_corrupt_;
  LOG(INFO) << "Number of under-replicated blocks = " << num_under_replicated_;
  LOG(INFO) << "Number of  over-replicated blocks = " << num_over_replicated_
            << postpone_ss.str();
  LOG(INFO) << "Number of blocks being written    = "
            << num_under_construction_;
  LOG(INFO) << "STATE* Replication Queue initialization scan for invalid, "
               "over- and under-replicated blocks completed in "
            << duration << " msec";
}

}  // namespace dancenn

