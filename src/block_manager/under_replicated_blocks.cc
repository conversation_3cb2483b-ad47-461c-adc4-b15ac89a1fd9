// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/under_replicated_blocks.h"

#include <glog/logging.h>

#include <array>
#include <mutex>
#include <random>
#include <unordered_map>
#include <unordered_set>

#include "base/defer.h"
#include "block_manager/block.h"
#include "inode.pb.h"

DECLARE_bool(log_block_corrupt);

namespace dancenn {

size_t UnderReplicatedBlocks::GetUnderReplicatedBlockCount() const {
  std::lock_guard<std::mutex> guard(mutex_);
  size_t size = 0;
  for (int i = 0; i < static_cast<int>(Priority::kLast); i++) {
    if (i != static_cast<int>(Priority::kWithCorruptBlocks)) {
      size += priority_queues_[i].size();
    }
  }
  return size;
}

void UnderReplicatedBlocks::ListCorruptBlocks(
    uint32_t offset,
    uint32_t count,
    uint32_t* total,
    std::vector<UnderReplicatedBlocks::BlockCorruptHint>* result) {
  auto pri = static_cast<int>(Priority::kWithCorruptBlocks);
  std::lock_guard<std::mutex> guard(mutex_);
  LOG(INFO) << "ListCorruptBlocks, total: " << priority_queues_[pri].size()
            << ", hint: " << blocks_corrupt_hint_.size()
            << ", offset: " << offset << ", count: " << count;
  uint32_t index = 0;
  *total = priority_queues_[pri].size();
  for (auto itr = priority_queues_[pri].begin();
       itr != priority_queues_[pri].end();
       ++itr) {
    if (index++ < offset) {
      continue;
    }
    auto hint = blocks_corrupt_hint_.find(itr->id);
    if (hint == blocks_corrupt_hint_.end()) {
      LOG(WARNING) << "ListCorruptBlocks, Block: " << itr->id
                   << " hint does "
                      "not exist.";
      continue;
    }
    result->push_back(hint->second);
    if (index >= offset + count) {
      break;
    }
  }
}

bool UnderReplicatedBlocks::Contains(const Block& block) {
  std::lock_guard<std::mutex> guard(mutex_);
  for (auto& s : priority_queues_) {
    if (s.find(block) != s.end()) {
      return true;
    }
  }
  return false;
}

bool UnderReplicatedBlocks::IsCorruptBlock(const BlockInfo* block_info,
                                           int cur_replicas,
                                           int expected_replicas) {
  auto& block = block_info->blk();
  auto pri = GetPriority(block, cur_replicas, expected_replicas);
  return pri == Priority::kWithCorruptBlocks;
}

bool UnderReplicatedBlocks::MoveBack(const Block& block, Priority pri) {
  std::lock_guard<std::mutex> guard(mutex_);
  // if a new need_replication record has been put input queue, just return
  for (auto& s : priority_queues_) {
    if (s.find(block) != s.end()) {
      return false;
    }
  }
  if (static_cast<int>(pri) >= static_cast<int>(Priority::kWithCorruptBlocks)) {
    LOG(WARNING) << "invalid old priority for move back: "
                 << static_cast<int>(pri);
    return false;
  }
  if (priority_queues_[static_cast<int>(pri)].emplace(block).second) {
    return true;
  }
  return false;
}

// NOTICE: slice read lock should already been held
bool UnderReplicatedBlocks::Add(const BlockInfo* block_info,
                                int cur_replicas,
                                int expected_replicas,
                                const std::string& reason,
                                Priority priority) {
  DLOG(INFO) << "Add under replicated blk: " << block_info->id()
             << ", cur_replicas: " << cur_replicas
             << ", expected_replicas: " << expected_replicas;

  CHECK_GE(cur_replicas, 0) << "Negative replicas!";
  auto& block = block_info->blk();
  auto pri = priority;
  if (pri == Priority::kLast) {
    pri = GetPriority(block, cur_replicas, expected_replicas);
  }
  if (block_info->IsPersisted()) {
    pri = Priority::kUnderReplicated;
  }
  VLOG_IF(2, reason == "Decommission")
      << "[AddDecommissionBlocks To UnderReplicatedBlocks] bi="
      << block_info->id() << " cur_replicas=" << cur_replicas
      << " expected_replicas=" << expected_replicas
      << " pri=" << std::string(PriorityDesc(pri));

  std::lock_guard<std::mutex> guard(mutex_);
  auto stale_removed_pri = RemoveInternal(block, Priority::kLast);
  if (stale_removed_pri == Priority::kWithCorruptBlocks &&
      expected_replicas == 1 && corrupt_repl_one_blocks_ > 0) {
    corrupt_repl_one_blocks_--;
  }
  if (priority_queues_[static_cast<int>(pri)].emplace(block).second) {
    if (pri == Priority::kWithCorruptBlocks) {
      if (expected_replicas == 1) {
        corrupt_repl_one_blocks_++;
      }
      AddToCorruptHint(block_info, reason);
    }
    VLOG_OR_IF(8, FLAGS_log_block_corrupt)
        << "reason=" << reason << ", " << block << " has only " << cur_replicas
        << " replicas and need " << expected_replicas
        << " replicas so is added to needed_replications "
        << "at priority level " << PriorityDesc(pri) << ".";

    return true;
  }
  return false;
}

bool UnderReplicatedBlocks::Remove(const Block& block,
                                   int old_replicas,
                                   int old_expected_replicas) {
  auto pri = GetPriority(block, old_replicas, old_expected_replicas);
  std::lock_guard<std::mutex> guard(mutex_);
  auto removed_pri = RemoveInternal(block, pri);
  if (pri == Priority::kWithCorruptBlocks && old_expected_replicas == 1 &&
      removed_pri != Priority::kLast) {
    if (corrupt_repl_one_blocks_ > 0) {
      corrupt_repl_one_blocks_--;
    } else {
      LOG(WARNING) << "Number of corrupt blocks with replication factor 1 "
                   << "should be non-negative";
    }
  }
  return removed_pri != Priority::kLast;
}

UnderReplicatedBlocks::Priority UnderReplicatedBlocks::RemoveInternal(
    const Block& block, Priority pri) {
  if (pri < Priority::kLast &&
      priority_queues_[static_cast<int>(pri)].erase(block) > 0) {
    if (pri == Priority::kWithCorruptBlocks) {
      blocks_corrupt_hint_.erase(block.id);
      VLOG_OR_IF(11, FLAGS_log_block_corrupt)
          << "Remove Corrupt Block: " << block.id;
    }
    DLOG(INFO) << "Removing " << block << " from priority queue "
               << PriorityDesc(pri);
    return pri;
  } else {
    // Try to remove the block from all queues if the block was
    // not found in the queue for the given priority level.
    for (int i = 0; i < kPriorityCount; i++) {
      if (priority_queues_[i].erase(block) > 0) {
        if (static_cast<Priority>(i) == Priority::kWithCorruptBlocks) {
          blocks_corrupt_hint_.erase(block.id);
          VLOG_OR_IF(11, FLAGS_log_block_corrupt)
              << "Remove Corrupt Block: " << block.id;
        }
        DLOG(INFO) << "Removing " << block << " from priority queue "
                   << PriorityDesc(pri) << " " << i;
        return static_cast<Priority>(i);
      }
    }
  }
  return Priority::kLast;
}

void UnderReplicatedBlocks::Update(const BlockInfo* block_info,
                                   int cur_replicas,
                                   int cur_expected_replicas,
                                   int cur_replicas_delta,
                                   int expected_replicas_delta,
                                   const std::string& reason,
                                   const DatanodeID dn_id) {
  int old_replicas = cur_replicas - cur_replicas_delta;
  int old_expected_replicas = cur_expected_replicas - expected_replicas_delta;
  auto& block = block_info->blk();
  auto cur_pri =
      GetPriority(block, cur_replicas, cur_expected_replicas, "cur_replicas");
  auto old_pri =
      GetPriority(block, old_replicas, old_expected_replicas, "old_replicas");
  DLOG(INFO) << "Update need blocks, blk:" << block << " cur_replicas "
             << cur_replicas << " cur_expected_replicas "
             << cur_expected_replicas << " old_replicas " << old_replicas
             << " old_expected_replicas " << old_expected_replicas
             << " cur_pri " << PriorityDesc(cur_pri) << " oldPri "
             << PriorityDesc(old_pri);
  std::lock_guard<std::mutex> guard(mutex_);
  if (old_pri != cur_pri) {
    RemoveInternal(block, old_pri);
  }
  if (priority_queues_[static_cast<int>(cur_pri)].emplace(block).second) {
    if (cur_pri == Priority::kWithCorruptBlocks) {
      AddToCorruptHint(block_info, reason, dn_id);
    }
    VLOG_OR_IF(8, FLAGS_log_block_corrupt)
        << "reason=" << reason << ", " << block << " has only " << cur_replicas
        << " replicas and needs " << cur_expected_replicas
        << " replicas so is added to needed_replications at "
        << "priority level " << PriorityDesc(cur_pri);
  }
  if (old_pri != cur_pri || expected_replicas_delta != 0) {
    // corrupt_repl_one_blocks_ could possibly change
    if (cur_pri == Priority::kWithCorruptBlocks && cur_expected_replicas == 1) {
      // add a new corrupt block with replication factor 1
      corrupt_repl_one_blocks_++;
    } else if (old_pri == Priority::kWithCorruptBlocks &&
               cur_expected_replicas - expected_replicas_delta == 1) {
      // remove an existing corrupt block with replication factor 1
      corrupt_repl_one_blocks_--;
    }
  }
}
std::array<std::vector<Block>, UnderReplicatedBlocks::kPriorityCount>
UnderReplicatedBlocks::ChooseUnderReplicatedBlocks(int blocks_to_process,
                                                   int corrupt_blk_to_process) {
  // initialize data structure for the return value
  std::array<std::vector<Block>, kPriorityCount> res;
  if (size() == 0) {  // There are no blocks to collect.
    return res;
  }

  int block_count = 0;
  std::lock_guard<std::mutex> guard(mutex_);
  for (size_t p = 0; p < kPriorityCount - 1; p++) {
    for (auto itr = priority_queues_[p].begin();
         block_count < blocks_to_process && itr != priority_queues_[p].end();) {
      res[p].push_back(*itr);
      itr = priority_queues_[p].erase(itr);
      block_count++;
    }
  }
  // corrupt block will be reserved after scan, as time goes by, the raising of
  // missing block number will decrease the speed of block recovery.
  // Truncate corrupt block result to specified size to speed up
  // ComputeReplicationWork func.
  size_t p = static_cast<size_t>(Priority::kWithCorruptBlocks);
  for (auto iter = priority_queues_[p].begin();
       block_count < blocks_to_process && iter != priority_queues_[p].end();) {
    // Corrupt block need to be reserved
    res[p].push_back(*iter);
    iter++;
    block_count++;
  }
  if (res[p].size() > corrupt_blk_to_process && corrupt_blk_to_process >= 0) {
    std::random_device rd;
    std::mt19937 re(rd());
    std::shuffle(res[p].begin(), res[p].end(), re);
    res[p].resize(corrupt_blk_to_process);
  }
  return res;
}

UnderReplicatedBlocks::Priority UnderReplicatedBlocks::GetPriority(
    const Block& block,
    int cur_replicas,
    int expected_replicas,
    const std::string& debug_msg) {
  if (cur_replicas < 0) {
    LOG(ERROR) << "[GetPriority Negative replicas!] block=" << block
               << " cur_replicas=" << cur_replicas
               << " debug_msg=" << debug_msg;
    cur_replicas = 0;
  }
  CHECK_GE(cur_replicas, 0) << "Negative replicas!";
  // Remove conditions related to decommission
  // the priority about decommission is no longer processed here
  if (cur_replicas >= expected_replicas) {
    // Block has enough copies, but not enough racks
    return Priority::kReplicasBadlyDistributed;
  } else if (cur_replicas == 0) {
    // all we have are corrupt blocks
    return Priority::kWithCorruptBlocks;
  } else if (cur_replicas == 1) {
    // only on replica -risk of loss
    // highest priority
    return Priority::kHighestPriority;
  } else if ((cur_replicas * 3) < expected_replicas) {
    // there is less than a third as many blocks as requested;
    // this is considered very under-replicated
    return Priority::kVeryUnderReplicated;
  } else {
    // add to the normal queue for under replicated blocks
    return Priority::kUnderReplicated;
  }
}

const char* UnderReplicatedBlocks::PriorityDesc(Priority pri) {
  switch (pri) {
    case Priority::kHighestPriority:
      return "kHighestPriority";
    case Priority::kVeryUnderReplicated:
      return "kVeryUnderReplicated";
    case Priority::kUnderReplicated:
      return "kUnderReplicated";
    case Priority::kReplicasBadlyDistributed:
      return "kReplicasBadlyDistributed";
    case Priority::kWithCorruptBlocks:
      return "kWithCorruptBlocks";
    case Priority::kLast:
      return "kLast";
    default:
      LOG(FATAL) << "Never happen";
  }
}

void UnderReplicatedBlocks::AddToCorruptHint(const BlockInfo* block_info,
                                             const std::string& reason,
                                             const DatanodeID dn_id) {
  auto inode_id = block_info->inode_id();
  auto time = std::chrono::system_clock::now();
  if (blocks_corrupt_hint_.find(block_info->id()) ==
      blocks_corrupt_hint_.end()) {
    blocks_corrupt_hint_.emplace(
        block_info->id(),
        BlockCorruptHint{inode_id, block_info->id(), dn_id, reason, time});
    VLOG_OR_IF(11, FLAGS_log_block_corrupt)
        << "Add corrupt block: " << block_info->id() << ", inode: " << inode_id
        << ", reason: " << reason;
  }
}

}  // namespace dancenn
