#pragma once

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>

namespace dancenn {

class BlockManager;

// Move `Finalize` command from global queue to dn_info queue
// Finalize = truncate block, see
// https://bytedance.larkoffice.com/wiki/P21Zwdx3xiPGpzk5cjdc3Knzn2b
class TruncatableBlockMonitor {
 public:
  explicit TruncatableBlockMonitor(BlockManager* bm) : bm_(bm) {
  }
  ~TruncatableBlockMonitor() {
    Stop();
  }

  void Start() {
    CHECK(!worker_.get());
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(new Task(this)),
        "TrublkMonitor");
    worker_->Start();
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

 private:
  BlockManager* bm_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  friend class Task;

  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(TruncatableBlockMonitor* monitor) : monitor_(monitor) {
    }

    bool operator()(void* arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

   private:
    TruncatableBlockMonitor* monitor_{nullptr};
    std::mutex mu_;
    std::condition_variable cond_;
  };

  void Do();
};

}  // namespace dancenn
