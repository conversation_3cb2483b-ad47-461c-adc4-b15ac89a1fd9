// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_UNDER_REPLICATED_BLOCKS_H_
#define BLOCK_MANAGER_UNDER_REPLICATED_BLOCKS_H_

#include <glog/logging.h>

#include <array>
#include <mutex>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/defer.h"
#include "base/stop_watch.h"
#include "block_manager/block.h"
#include "block_manager/block_info.h"
#include "inode.pb.h"  // NOLINT(build/include)
#include "proto/generated/cloudfs/hdfs.pb.h"

namespace dancenn {

// Keep prioritized queues of under replicated blocks.
// Blocks have replication priority, with priority kHighestPriority
// indicating the highest priority.
// Having a prioritised queue allows the BlockManager to select
// which blocks to replicate first -it tries to give priority to data
// that is most at risk or considered most valuable.
//
// The policy for choosing which priority to give added blocks
// is implemented in GetPriority(const Block&, int, int).
// The queue order is as follows:
//   kHighestPriority: the blocks that must be replicated first.
//   That is blocks with only one copy, or blocks with zero live copies
//   but a copy in a node being decommissioned. These blocks are at risk of
//   loss if the disk or server on which they remain fails.
//   kVeryUnderReplicated: blocks that are very under-replicated
//   compared to their expected values. Currently that means the ratio of the
//   ratio of actual:expected means that there is less than 1:3.
//   These blocks may not be at risk, but they are clearly considered
//   "important".
//   kUnderReplicated: blocks that are also under replicated, and the
//   ratio of actual:expected is good enough that they do not need to go
//   into the kVeryUnderReplicated queue.
//   kReplicasBadlyDistributed: there are as least as many copies of
//   a block as required, but the blocks are not adequately distributed.
//   Loss of a rack/switch could take all copies off-line.
//   kWithCorruptBlocks: This is for blocks that are corrupt and for
//   which there are no-non-corrupt copies (currently) available. The policy
//   here is to keep those corrupt blocks replicated, but give blocks that are
//   not corrupt higher priority.
class UnderReplicatedBlocks {
 public:
  enum class Priority {
    // The queue with the highest priority
    kHighestPriority = 0,
    // The queue for blocks that are very below their expected value
    kVeryUnderReplicated = 1,
    // The queue for "normally" under-replicated blocks
    kUnderReplicated = 2,
    // The queue for blocks that have the right number of replicas,
    // but which the block manager felt were badly distributed
    kReplicasBadlyDistributed = 3,
    // The queue for corrupt blocks
    kWithCorruptBlocks = 4,
    // The total number of queues
    kLast = 5,
  };

  static cloudfs::IOPriority ToIOPriority(Priority value) {
    switch (value) {
      case Priority::kHighestPriority:
        return cloudfs::IOPriority::PRIORITY_CRITICAL;
      case Priority::kVeryUnderReplicated:
        return cloudfs::IOPriority::PRIORITY_REAL_TIME;
      case Priority::kUnderReplicated:
        return cloudfs::IOPriority::PRIORITY_ELASTIC;
      case Priority::kReplicasBadlyDistributed:
        return cloudfs::IOPriority::PRIORITY_ELASTIC;
      case Priority::kWithCorruptBlocks:
        return cloudfs::IOPriority::PRIORITY_BEST_EFFORT;
      case Priority::kLast:
        return cloudfs::IOPriority::PRIORITY_SCAVENGER;
      default:
        return cloudfs::IOPriority::PRIORITY_BEST_EFFORT;
    }
  }

  struct BlockCorruptHint {
    uint64_t inode_id;
    uint64_t block_id;
    DatanodeID dn_id;
    std::string reason;
    std::chrono::system_clock::time_point time;
  };

  static const size_t kPriorityCount = static_cast<size_t>(Priority::kLast);

  static const char* PriorityDesc(Priority pri);

  void Clear() {
    StopWatch sw;
    sw.Start();

    std::lock_guard<std::mutex> guard(mutex_);
    LOG(INFO) << "[HA] "
              << "UnderReplicatedBlocks::clear mutex time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    for (auto& pq : priority_queues_) {
      pq.clear();
    }
    LOG(INFO) << "[HA] "
              << "UnderReplicatedBlocks::clear priority_queues_.clear() time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    blocks_corrupt_hint_.clear();
    LOG(INFO)
        << "[HA] "
        << "UnderReplicatedBlocks::clear blocks_corrupt_hint_.clear() time:"
        << StringUtils::FormatWithCommas(sw.NextStepTime());
  }
  void Swap(UnderReplicatedBlocks& other) {
    StopWatch sw;
    sw.Start();

    std::lock_guard<std::mutex> guard(mutex_);
    LOG(INFO) << "[HA] "
              << "UnderReplicatedBlocks::clear mutex time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    other.priority_queues_.swap(priority_queues_);
    LOG(INFO) << "[HA] "
              << "UnderReplicatedBlocks::clear swap priority_queues_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());

    other.blocks_corrupt_hint_.swap(blocks_corrupt_hint_);
    other.corrupt_repl_one_blocks_ = corrupt_repl_one_blocks_;
    corrupt_repl_one_blocks_ = 0;
    LOG(INFO) << "[HA] "
              << "UnderReplicatedBlocks::clear swap blocks_corrupt_hint_ time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  size_t size() const {
    size_t res = 0;
    std::lock_guard<std::mutex> guard(mutex_);
    for (auto& pq : priority_queues_) {
      res += pq.size();
    }
    return res;
  }

  size_t SizePriority(Priority priority = Priority::kLast) const {
    if (priority == Priority::kLast) {
      return 0;
    }

    std::lock_guard<std::mutex> guard(mutex_);
    return priority_queues_[static_cast<int>(priority)].size();
  }

  std::array<size_t, kPriorityCount> SizeEachPriority() const {
    std::array<size_t, kPriorityCount> res;
    std::lock_guard<std::mutex> guard(mutex_);
    for (size_t i = 0; i < priority_queues_.size(); ++i) {
      res[i] = priority_queues_[i].size();
    }
    return res;
  };

  // Return the number of under replication blocks excluding corrupt blocks
  size_t GetUnderReplicatedBlockCount() const;

  void ListCorruptBlocks(
      uint32_t offset,
      uint32_t count,
      uint32_t* total,
      std::vector<UnderReplicatedBlocks::BlockCorruptHint>* result);

  // Return the number of corrupt blocks
  size_t GetCorruptBlockSize() const {
    auto priority = static_cast<int>(Priority::kWithCorruptBlocks);
    std::lock_guard<std::mutex> guard(mutex_);
    return priority_queues_[priority].size();
  }

  // Return the number of corrupt blocks with replication factor 1
  size_t GetCorruptReplOneBlockSize() const { return corrupt_repl_one_blocks_; }

  // Check if a block is in the neededReplication queue
  bool Contains(const Block& block);

  // add a block to an under replication queue according to its priority
  bool Add(const BlockInfo* block_info,
           int cur_replicas,
           int expected_replicas,
           const std::string& reason = "",
           Priority priority = Priority::kLast);

  // remove a block from a under replication queue
  bool Remove(const Block& block, int old_replicas, int old_expected_replicas);

  // Remove a block from the under replication queues.
  // The pri parameter is a hint of which queue to query
  // first: if negative or >= Priority::kLast this shortcutting
  // is not attempted.
  // If the block is not found in the nominated queue, an attempt is made to
  // remove it from all queues.
  bool Remove(const Block& block, Priority pri) {
    std::lock_guard<std::mutex> guard(mutex_);
    return RemoveInternal(block, pri) != Priority::kLast;
  }

  bool MoveBack(const Block& block, Priority priority);

  // Recalculate and potentially update the priority level of a block.
  //
  // If the block priority has changed from before an attempt is made to
  // remove it from the block queue. Regardless of whether or not the block
  // is in the block queue of (recalculate) priority, an attempt is made
  // to add it to that queue. This ensures that the block will be
  // in its expected priority queue (and only that queue) by the end of the
  // method call.
  void Update(const BlockInfo* block_info,
              int cur_replicas,
              int cur_expected_replicas,
              int cur_replicas_delta,
              int expected_replicas_delta,
              const std::string& reason = "",
              const DatanodeID dn_id = kInvalidDatanodeID);

  // Get a list of block lists to be replicated. The index of block lists
  // represents its replication priority. Iterates through all priority lists
  // and find the elements.
  // Note: We'll remove the selected blocks.
  std::array<std::vector<Block>, kPriorityCount> ChooseUnderReplicatedBlocks(
      int blocks_to_process,
      int corrupt_blk_to_process = -1);

  bool IsCorruptBlock(const BlockInfo* block_info,
                      int cur_replicas,
                      int expected_replicas);

 private:
  mutable std::mutex mutex_;
  std::array<std::unordered_set<Block, BlockHashUnordered>, kPriorityCount>
      priority_queues_;
  size_t corrupt_repl_one_blocks_{0};
  std::unordered_map<uint64_t, BlockCorruptHint> blocks_corrupt_hint_;

  Priority RemoveInternal(const Block& block, Priority pri);

  static Priority GetPriority(const Block& block,
                              int cur_replicas,
                              int expected_replicas,
                              const std::string& debug_msg = "");

  void AddToCorruptHint(const BlockInfo* blockInfo,
                        const std::string& reason = "",
                        const DatanodeID dn_id = kInvalidDatanodeID);
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_UNDER_REPLICATED_BLOCKS_H
