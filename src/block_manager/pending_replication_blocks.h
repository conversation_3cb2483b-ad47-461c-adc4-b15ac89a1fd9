// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_PENDING_REPLICATION_BLOCKS_H_
#define BLOCK_MANAGER_PENDING_REPLICATION_BLOCKS_H_

#include <cnetpp/concurrency/thread.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>
#include <unordered_map>
#include <vector>

#include "datanode_manager/datanode_info.h"
#include "block_manager/block.h"

namespace dancenn {

// PendingReplicationBlocks does the bookkeeping of all
// blocks that are getting replicated.
//
// It does the following:
// 1)  record blocks that are getting replicated at this instant.
// 2)  a coarse grain timer to track age of replication request
// 3)  a thread that periodically identifies replication-requests
//     that never made it.
class PendingReplicationBlocks {
 public:
  explicit PendingReplicationBlocks(std::chrono::milliseconds timeout) {
    if (timeout.count() > 0) {
      timeout_ = timeout;
    }
  }

  ~PendingReplicationBlocks() {
    Stop();
  }

  void Start() {
    timer_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(
            new TimerTask(this, timeout_)), "prb");
    timer_->Start();
  }

  void Stop() {
    if (timer_) {
      timer_->Stop();
      timer_.reset();
    }
  }

  // Add a block to the list of pending Replications
  void Increment(BlockID block, const std::vector<DatanodeID>& targets);

  // One replication request for this block has finished.
  // Decrement the number of pending replication requests
  // for this block.
  void Decrement(BlockID block, DatanodeID dn);

  // Remove the record about the given block from pendingReplications.
  void Remove(BlockID block) {
    pending_replications_.erase(block);
  }

  void Clear() {
    pending_replications_.clear();
  }

  // The total number of blocks that are undergoing replication
  size_t Size() const {
    return pending_replications_.size();
  }

  // How many copies of this block is pending replication?
  size_t NumReplicas(BlockID block);

  // Returns a list of blocks that have timed out their replication requests.
  void TimedOutBlocks(std::vector<BlockID>* timeout_blocks);

  class PendingBlockInfo {
   public:
    explicit PendingBlockInfo(const std::vector<DatanodeID> targets)
        : targets_(targets) {
      set_ts();
    }

    std::chrono::steady_clock::time_point get_ts() const {
      return ts_;
    }
    void set_ts() {
      ts_ = std::chrono::steady_clock::now();
    }

    void IncrementReplicas(const std::vector<DatanodeID>& targets) {
      targets_.insert(targets_.end(), targets.begin(), targets.end());
    }

    void DecrementReplicas(DatanodeID dn) {
      targets_.erase(
          std::remove(targets_.begin(), targets_.end(), dn), targets_.end());
    }

    size_t NumReplicas() const {
      return targets_.size();
    }

   private:
    std::chrono::steady_clock::time_point ts_;
    std::vector<DatanodeID> targets_;
  };
  using PendingBlockInfoUPtr = std::unique_ptr<PendingBlockInfo>;

 private:
  void PendingReplicationCheck();

  mutable std::mutex mutex_;
  std::unordered_map<BlockID, PendingBlockInfoUPtr> pending_replications_;
  std::vector<BlockID> timedout_items_;
  std::unique_ptr<cnetpp::concurrency::Thread> timer_;
  std::shared_ptr<cnetpp::concurrency::Task> timer_task_;
  friend class TimerTask;

  class TimerTask : public cnetpp::concurrency::Task {
   public:
    TimerTask(PendingReplicationBlocks* manager,
              std::chrono::milliseconds period)
        : manager_(manager),
          period_(period) {
    }

    bool operator()(void* arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

   private:
    PendingReplicationBlocks* manager_{nullptr};
    std::chrono::milliseconds period_;
    std::mutex mu_;
    std::condition_variable cond_;
  };

  std::chrono::milliseconds timeout_ { 300 * 1000 };
  static const std::chrono::milliseconds kDefaultRecheckInterval;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_PENDING_REPLICATION_BLOCKS_H_

