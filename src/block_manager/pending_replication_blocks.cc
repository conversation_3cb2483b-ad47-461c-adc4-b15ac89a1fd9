// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/pending_replication_blocks.h"

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>
#include <unordered_map>
#include <vector>

namespace dancenn {

void PendingReplicationBlocks::Increment(
    BlockID block, const std::vector<DatanodeID>& targets) {
  auto itr = pending_replications_.find(block);
  if (itr == pending_replications_.end()) {
    pending_replications_.emplace(
        block, std::make_unique<PendingBlockInfo>(targets));
  } else {
    itr->second->IncrementReplicas(targets);
    itr->second->set_ts();
  }
}

void PendingReplicationBlocks::Decrement(BlockID block, DatanodeID dn) {
  auto itr = pending_replications_.find(block);
  if (itr != pending_replications_.end()) {
    itr->second->DecrementReplicas(dn);
    DLOG(INFO) << "Removing pending replication for blk: "
               << block << ", dn: " << dn
               << ", rest: " << itr->second->NumReplicas();
    if (itr->second->NumReplicas() == 0) {
      pending_replications_.erase(itr);
    }
  }
}

size_t PendingReplicationBlocks::NumReplicas(BlockID block) {
  auto itr = pending_replications_.find(block);
  if (itr != pending_replications_.end()) {
    return itr->second->NumReplicas();
  }
  return 0;
}

void PendingReplicationBlocks::TimedOutBlocks(
    std::vector<BlockID>* timeout_blocks) {
  if (pending_replications_.empty()) {
    return;
  }
  auto now = std::chrono::steady_clock::now();
  for (auto itr = pending_replications_.begin();
     itr != pending_replications_.end();) {
    if (now > itr->second->get_ts() + timeout_) {
      timeout_blocks->push_back(itr->first);
      itr = pending_replications_.erase(itr);
    } else {
      itr++;
    }
  }
}

void PendingReplicationBlocks::PendingReplicationCheck() {
  DLOG(INFO) << "PendingReplicationMonitor checking Q";
  std::lock_guard<std::mutex> guard(mutex_);
  auto now = std::chrono::steady_clock::now();
  for (auto itr = pending_replications_.begin();
       itr != pending_replications_.end();) {
    if (now > itr->second->get_ts() + timeout_) {
      timedout_items_.push_back(itr->first);
      DLOG(WARNING) << "PendingReplicationMonitor timed out, blk: " <<
                    itr->first;
      itr = pending_replications_.erase(itr);
    } else {
      itr++;
    }
  }
}

bool PendingReplicationBlocks::TimerTask::operator()(void* arg) {
  while (!stop_) {
    manager_->PendingReplicationCheck();
    auto sleep = std::min(period_, manager_->kDefaultRecheckInterval);
    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, sleep, [this] () -> bool { return stop_; });
  }
  return true;
}

const std::chrono::milliseconds
    PendingReplicationBlocks::kDefaultRecheckInterval { 300 * 1000 };

}  // namespace dancenn

