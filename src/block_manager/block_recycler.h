// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/02
// Description

#ifndef BLOCK_MANAGER_BLOCK_RECYCLER_H_
#define BLOCK_MANAGER_BLOCK_RECYCLER_H_

#include <cnetpp/concurrency/thread.h>
#include <rocksdb/db.h>

#include <atomic>
#include <cstdint>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <utility>
#include <vector>

#include "base/status.h"
#include "base/vlock.h"
#include "block_manager/bip_write_manager.h"
#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_map_slice.h"
#include "edit/edit_log_context.h"
#include "edit/sender.h"
#include "ha/ha_state_base.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

// NN1 writes edit log with txid=10. --> (flying on network)
//                                    |
//            NN2 becomes active and write edit log with txid=10.
// NN writes edit log with txid=10. --> NN writes rocksdb buffer with txid=10.
//                                   |
//  NN becomes standby and EditLogTailer writes rocksdb buffer with txid=10.
// How to avoid the above cases?
// 1. HAState::SetState takes unique lock of HAState::barrier_.
//    It means no one is holding shared lock of HAState::barrier_.
//    It promises that no write operation is being performed.
//    e.g. BlockRecycler::CheckOperation
// 2. HAState::SetState -> NameSpace::WaitNoPending(/*include_bg=*/false)
//    It promises all edit logs and RocksDB WriteBatches submitted
//    before this moment has been flushed to disk.
//    Notice, no one but fsimage transfer will submit background tasks.
// 3. NameSpace::StopActive -> BlockManager::StopActive
//                          -> BlockRecycler::StopActive
//                          -> BlockRecycler::is_active_ = false
//    It means BlockRecycler won't try to take shared lock of
//    HAState::barrier_ from now on.
// 4. EditLogSenderBase::Sync(true) -> EditLogContext::LogSync(true)
//    It promises all edit logs (in C++ and Java buffers) have been flushed to
//    book keeper. No edit log is flying.
// 5. This nn becomes standby nn.
// 6. HAState::SetState releases unique lock of HAState::barrier_.
class BlockRecycler {
 public:
  BlockRecycler(HAStateBase* ha_state,
                BlockManager* block_manager,
                std::shared_ptr<EditLogContextBase> edit_log_ctx,
                std::shared_ptr<MetaStorage> meta_storage);
  // For BlockManager::set_ha_state.
  void SetHAState(HAStateBase* ha_state);
  // For BlockManager::SetMetaStorage.
  void SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage);
  void TestOnlySetEditLogSender(std::shared_ptr<EditLogSenderBase> sender);

  virtual void StartActive(BIPWriteManagerBase* bip_write_manager);
  virtual void StopActive();

  BlockID DecodeBlockID(rocksdb::Slice);

 protected:
  std::pair<Status, dancenn::vshared_lock> CheckOperation();
  bool SeekIterator(rocksdb::Iterator* it,
                    const std::string& last_iterator,
                    bool allow_seek_to_first);
  virtual std::unique_ptr<EditLogSenderBase> CreateEditLogSender();

 protected:
  std::atomic<bool> is_active_;
  HAStateBase* ha_state_{nullptr};
  BlockManager* block_manager_{nullptr};
  BIPWriteManagerBase* bip_write_manager_{nullptr};
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MetaStorage> meta_storage_;
};

class DepringBlockRecycler : public BlockRecycler {
 public:
  DepringBlockRecycler(HAStateBase* ha_state,
                       BlockManager* block_manager,
                       std::shared_ptr<EditLogContextBase> edit_log_ctx,
                       std::shared_ptr<MetaStorage> meta_storage);

  bool Recycle();
  std::unique_ptr<BlockMapSlice>& TestOnlySlice(BlockID blk_id);

 private:
  bool CanProcessMoreBlks();

 private:
  std::string last_iterator_;
  std::atomic<int> ongoing_op_del_depring_blks_size_;
};

//                                        +----------------------------+
//                                        |InvalidatePufsCommandProto_1|
//                         +--------------+----------------------------+
// +---------------+       |              |                            |
// | depred_blk_10 |       +--------------+InvalidatePufsCommandProto_2|
// |pufs_name != ""+--1.-->|depred_blk_10 |                            |
// +---------------+       +--------------+-------------------------+--+
// |               |                                               2.
// |               |                                                |
// +---------------+ Put into op                                    v
// |depred_blk_1025+--directly.                                    +--+
// |pufs_name == ""|     |                                         |DN|
// +---------------+     |                                         ++-+
// +-----------------+   |                                          |
// |OpDelDepredBlks_1|   v                      3. PUFS_DELETE blk_1025
// +-----------------+--------+                                     |
// |                 |blk_1025|                                     v
// |OpDelDepredBlks_2+--------+    +------------------------------------+
// |                 | blk_10 |<-4.+BlockManager::IncrementalBlockReport|
// +------+----------+--------+    +------------------------------------+
//       5.
//        |
//        v
// +------------+
// |BK & RocksDB|
// +------------+
//
// 1. The size of InvalidatePufsCommandProto queue is limited by
//    cached_invalidate_pufs_cmd_max_size.
// 2. There are at most invalidate_pufs_cmd_batch_size blocks in
//    one InvalidatePufsCommandProto.
//
// 1. The size of OpDelDepredBlks queue is limited by
//    ongoing_op_del_depred_blks_max_size.
// 2. OpDelDepredBlks is sent to BK & RocksDB if:
//    a. Size of blocks >= op_del_depred_blks_batch_size.
//    b. Wait too long.
class DepredBlockRecycler : public BlockRecycler {
 public:
  DepredBlockRecycler(HAStateBase* ha_state,
                      BlockManager* block_manager,
                      std::shared_ptr<EditLogContextBase> edit_log_ctx,
                      std::shared_ptr<MetaStorage> meta_storage);
  // For BlockManager::set_ns.
  void SetBlockPoolId(const std::string& bpid);
  void StartActive(BIPWriteManagerBase* bip_write_manager) override;
  void StopActive() override;

  bool Recycle();
  int ConsumeOpDelDepredBlksQueue();

  bool GetInvalidatePufsCmds(
      cloudfs::datanode::HeartbeatResponseProto* response,
      BlockManagerMetrics* bm_metrics = nullptr);
  void AddToOpDelDepredBlksQueue(BlockID blk_id);

 private:
  bool CanProcessMoreBlks();

 private:
  std::string blockpool_id_;
  std::string last_iterator_;

  // The order of taking locks:
  // 1. invalidate_pufs_cmd_queue_mutex_
  // 2. op_del_depred_blks_queue_mutex_
  std::mutex invalidate_pufs_cmd_queue_mutex_;
  int64_t iter_age_;
  int64_t meet_invalid_iter_age_;
  std::queue<cloudfs::datanode::InvalidatePufsCommandProto>
      invalidate_pufs_cmd_queue_;

  std::mutex op_del_depred_blks_queue_mutex_;
  int ongoing_op_del_depred_blks_size_;
  int op_del_depred_blks_age_;
  std::queue<std::pair</*age=*/int64_t, DepredBlksToBeDel>>
      op_del_depred_blks_queue_;
};

class TosBlockDeleter {
 public:
  explicit TosBlockDeleter(DepredBlockRecycler* depred_block_recycler);
  // For BlockManager::set_ns.
  void SetNameSpace(NameSpace* ns);

  void StartActive();
  void StopActive();

 private:
  NameSpace* ns_{nullptr};
  DepredBlockRecycler* depred_block_recycler_{nullptr};
  // TODO(ruanjunbin): Use async and callback, do not open a new thread.
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_RECYCLER_H_
