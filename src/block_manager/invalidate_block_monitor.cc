// Copyright 2019 <PERSON> <<EMAIL>>

#include "block_manager/invalidate_block_monitor.h"

#include <glog/logging.h>

#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(invalidate_block_monitor_interval_ms);

namespace dancenn {

void InvalidateBlockMonitor::Do() {
  LOG(INFO) << "Start to do some invalidate block count works.";
  auto res = bm_->ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (res.first.HasException()) {
    LOG(INFO) << "Skip due to standby mode.";
    return;
  }
  if (bm_->safemode_->IsOn()) {
    LOG(INFO) << "Skip due to safemode is on.";
    return;
  }
  auto start = std::chrono::steady_clock::now();
  if (bm_->IsPopulatingReplicationQueues()) {
    bm_->ComputeInvalidateBlockWork();
  }
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::steady_clock::now() - start).count();
  LOG(INFO) << "Invalidate block monitor time: " << duration << "(ms)";
}

bool InvalidateBlockMonitor::Task::operator()(void* arg) {
  (void) arg;

  while (!stop_) {
    monitor_->Do();

    int interval = FLAGS_invalidate_block_monitor_interval_ms;
    LOG(INFO) << "Invalidate block monitor will sleep: " << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this] () -> bool { return stop_; });
  }
  return true;
}

}  // namespace dancenn

