//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "datanode_command_manager.h"

#include "base/logger_metrics.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "security/block_token_identifier.h"

DECLARE_int32(namespace_type);
DECLARE_bool(log_dn_cmd_overflow);

namespace dancenn {

template <typename CmdType>
bool DatanodeCommandManager<CmdType>::Add(DatanodeID dn_id, CmdType&& cmd) {
  /**
   * In ACC mode, cmd might be not initialized here. Do not check it.
   */

  StopWatch sw;
  sw.Start();
  DEFER([&]() {
    sw.NextStep();
    auto cost_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
            .count();
    if (cost_ms > 1) {
      // DatanodeCommandSlice, just as BlockMapSlice
      LOG(INFO) << __PRETTY_FUNCTION__ << " is slow";
    }
  });

  CmdsType* cmds = nullptr;
  {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    auto it = dn_to_cmds_.find(dn_id);
    if (it != dn_to_cmds_.end()) {
      cmds = &(it->second);
    }
  }
  if (cmds == nullptr) {
    std::unique_lock<ReadWriteLock> _(rwlock_);
    cmds = &(dn_to_cmds_[dn_id]);
  }
  CHECK_NOTNULL(cmds);

  return AddCmdInternal(cmds, cmd);
}

template <typename CmdType>
bool DatanodeCommandManager<CmdType>::AddCmdInternal(CmdsType* cmds,
                                                     CmdType cmd) {
  std::lock_guard<std::mutex> _(cmds->first);
  if (cmds->second.size() >= *max_cmd_size_each_dn_) {
    VLOG_OR_IF(10, FLAGS_log_dn_cmd_overflow)
        << "Too many cmds in " << __PRETTY_FUNCTION__;
    return false;
  }
  cmds->second.emplace(cmd.block().blockid(), CmdType())
      .first->second.Swap(&cmd);
  return true;
}

template <>
bool BrCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::BlockReportCommandProto>>*
        cmds,
    cloudfs::datanode::BlockReportCommandProto cmd) {
  std::lock_guard<std::mutex> _(cmds->first);
  if (cmds->second.size() >= *max_cmd_size_each_dn_) {
    LOG(INFO) << "Too many cmds in " << __PRETTY_FUNCTION__;
    return false;
  }
  cmds->second
      .emplace(kInvalidBlockID, cloudfs::datanode::BlockReportCommandProto())
      .first->second.Swap(&cmd);
  return true;
}

template <>
bool AbortBrCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::AbortBlockReportCommandProto>>* cmds,
    cloudfs::datanode::AbortBlockReportCommandProto cmd) {
  std::lock_guard<std::mutex> _(cmds->first);
  if (cmds->second.size() >= *max_cmd_size_each_dn_) {
    LOG(INFO) << "Too many cmds in " << __PRETTY_FUNCTION__;
    return false;
  }
  cmds->second
      .emplace(kInvalidBlockID,
               cloudfs::datanode::AbortBlockReportCommandProto())
      .first->second.Swap(&cmd);
  return true;
}

template <>
bool LoadCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::LoadCommandProto>>*
        cmds,
    cloudfs::datanode::LoadCommandProto cmd) {
  std::lock_guard<std::mutex> _(cmds->first);
  if (cmds->second.size() >= *max_cmd_size_each_dn_) {
    LOG(INFO) << "Too many load cmds in " << __PRETTY_FUNCTION__;
    return false;
  }
  for (auto& blk : cmd.blocks()) {
    auto blk_id = blk.blockid();
    VLOG(10) << "Add load cmd for blk " << blk_id;
    auto iter = cmds->second.find(blk_id);
    if (iter == cmds->second.end()) {
      cmds->second.emplace(blk_id, cloudfs::datanode::LoadCommandProto())
          .first->second.Swap(&cmd);
      break;
    }
  }
  return true;
}

template <typename CmdType>
std::size_t DatanodeCommandManager<CmdType>::Get(
    DatanodeID dn_id,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  CmdsType* cmds = nullptr;
  {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    auto it = dn_to_cmds_.find(dn_id);
    if (it != dn_to_cmds_.end()) {
      cmds = &(it->second);
    }
  }
  if (cmds == nullptr) {
    return 0;
  }

  return GetCmdInternal(cmds, response, bm_metrics);
}

template <>
std::size_t UploadCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::UploadCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  for (auto it = cmds->second.begin(); it != cmds->second.end(); it++) {
    auto& cmd = it->second;
    auto upload_cmd = response->add_cmds();
    upload_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::UploadCommand);
    upload_cmd->mutable_uploadcmd()->Swap(&cmd);
    CHECK(upload_cmd->IsInitialized())
        << upload_cmd->InitializationErrorString();
  }
  size = cmds->second.size();
  cmds->second.clear();
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_upload_)->Inc(size);
  }
  return size;
}

template <>
std::size_t NeCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::NotifyEvictableCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  for (auto it = cmds->second.begin(); it != cmds->second.end(); it++) {
    auto& cmd = it->second;
    auto ne_cmd = response->add_cmds();
    ne_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::NotifyEvictableCommand);
    ne_cmd->mutable_necmd()->Swap(&cmd);
    CHECK(ne_cmd->IsInitialized()) << ne_cmd->InitializationErrorString();
  }
  size = cmds->second.size();
  cmds->second.clear();
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_notify_evictable_)->Inc(size);
  }
  return size;
}

template <>
std::size_t MergeCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::MergeCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  for (auto it = cmds->second.begin(); it != cmds->second.end(); it++) {
    auto& cmd = it->second;
    auto merge_cmd = response->add_cmds();
    merge_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::MergeCommand);
    merge_cmd->mutable_mergecmd()->Swap(&cmd);
    CHECK(merge_cmd->IsInitialized()) << merge_cmd->InitializationErrorString();
  }
  size = cmds->second.size();
  cmds->second.clear();
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_merge_)->Inc(size);
  }
  return size;
}

template <>
std::size_t BrCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::BlockReportCommandProto>>*
        cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  if (!cmds->second.empty()) {
    auto br_cmd = response->add_cmds();
    br_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::BlockReportCommand);
    br_cmd->mutable_blockreportcmd()->Swap(&(cmds->second.at(kInvalidBlockID)));
    CHECK(br_cmd->IsInitialized()) << br_cmd->InitializationErrorString();
    size = 1;
    cmds->second.clear();
  }
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_block_report_)->Inc(size);
  }
  return size;
}

template <>
std::size_t AbortBrCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::AbortBlockReportCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  if (!cmds->second.empty()) {
    auto abr_cmd = response->add_cmds();
    abr_cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto::AbortBlockReportCommand);
    abr_cmd->mutable_abortblockreportcmd()->Swap(
        &(cmds->second.at(kInvalidBlockID)));
    CHECK(abr_cmd->IsInitialized()) << abr_cmd->InitializationErrorString();
    size = 1;
    cmds->second.clear();
  }
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_abort_block_report_)->Inc(size);
  }
  return size;
}

template <>
std::size_t LoadCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::LoadCommandProto>>*
        cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  for (auto it = cmds->second.begin(); it != cmds->second.end(); it++) {
    auto& cmd = it->second;

    size += cmd.blocks_size();
    auto load_cmd = response->add_cmds();
    load_cmd->set_cmdtype(cloudfs::datanode::DatanodeCommandProto::LoadCommand);
    load_cmd->mutable_loadcmd()->Swap(&cmd);
    CHECK(load_cmd->IsInitialized());
  }
  cmds->second.clear();
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_load_)->Inc(size);
  }
  return size;
}

template <>
bool BlockIdCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::BlockIdCommandProto>>*
        cmds,
    cloudfs::datanode::BlockIdCommandProto cmd) {
  std::lock_guard<std::mutex> _(cmds->first);
  if (cmd.blockids_size() == 1) {
    cmds->second[cmd.blockids(0)] = std::move(cmd);
    return true;
  }

  for (auto& blk : cmd.blockids()) {
    cloudfs::datanode::BlockIdCommandProto new_cmd;
    new_cmd.set_action(cmd.action());
    new_cmd.set_blockpoolid(cmd.blockpoolid());
    new_cmd.add_blockids(blk);
    cmds->second[blk] = new_cmd;
  }
  return true;
}

template <>
std::size_t BlockIdCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::BlockIdCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  std::size_t size = 0;
  std::lock_guard<std::mutex> _(cmds->first);
  cloudfs::datanode::BlockIdCommandProto pin_cmd, unpin_cmd;
  pin_cmd.set_action(cloudfs::datanode::BlockIdCommandProto_Action_PIN);
  unpin_cmd.set_action(cloudfs::datanode::BlockIdCommandProto_Action_UNPIN);
  for (auto it = cmds->second.begin(); it != cmds->second.end(); it++) {
    auto& cmd = it->second;
    switch (cmd.action()) {
      case cloudfs::datanode::BlockIdCommandProto_Action_PIN:
        if (pin_cmd.blockpoolid().empty()) {
          pin_cmd.set_blockpoolid(cmd.blockpoolid());
        }
        for (auto& blk : cmd.blockids()) {
          pin_cmd.add_blockids(blk);
        }
        break;
      case cloudfs::datanode::BlockIdCommandProto_Action_UNPIN:
        if (unpin_cmd.blockpoolid().empty()) {
          unpin_cmd.set_blockpoolid(cmd.blockpoolid());
        }
        for (auto& blk : cmd.blockids()) {
          unpin_cmd.add_blockids(blk);
        }
        break;
      default:
        LOG(ERROR) << "Unexpected action";
        break;
    }
  }
  if (pin_cmd.blockids_size() > 0) {
    auto cmd = response->add_cmds();
    cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto_Type_BlockIdCommand);
    cmd->mutable_blkidcmd()->Swap(&pin_cmd);
    size++;
    if (bm_metrics) {
      MFC(bm_metrics->dn_cmd_cnt_block_id_pin_)->Inc(1);
    }
  }
  if (unpin_cmd.blockids_size() > 0) {
    auto cmd = response->add_cmds();
    cmd->set_cmdtype(
        cloudfs::datanode::DatanodeCommandProto_Type_BlockIdCommand);
    cmd->mutable_blkidcmd()->Swap(&unpin_cmd);
    size++;
    if (bm_metrics) {
      MFC(bm_metrics->dn_cmd_cnt_block_id_unpin_)->Inc(1);
    }
  }
  cmds->second.clear();
  return size;
}

template class DatanodeCommandManager<cloudfs::datanode::UploadCommandProto>;
template class DatanodeCommandManager<
    cloudfs::datanode::NotifyEvictableCommandProto>;
template class DatanodeCommandManager<
    cloudfs::datanode::BlockReportCommandProto>;
template class DatanodeCommandManager<
    cloudfs::datanode::AbortBlockReportCommandProto>;
template class DatanodeCommandManager<cloudfs::datanode::LoadCommandProto>;
template class DatanodeCommandManager<cloudfs::datanode::MergeCommandProto>;
template class DatanodeCommandManager<cloudfs::datanode::BlockIdCommandProto>;

}  // namespace dancenn
