#pragma once

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>

namespace dancenn {

class BlockManager;

// NOTICE: The concept of SEALED does not exist in NN.
// All mentioned SEALED actually means UNFINALIZED.
// NN is responsible for promoting the status of all UNFINALIZED Blocks
// TODO(xiong): remove the term `sealed` replace with term `Unfinalized`

// check sealed block and make `truncate block` command
// Finalize = truncate block, see
// https://bytedance.larkoffice.com/wiki/P21Zwdx3xiPGpzk5cjdc3Knzn2b
class SealedBlockMonitor {
 public:
  explicit SealedBlockMonitor(BlockManager* bm) : bm_(bm) {
  }
  ~SealedBlockMonitor() {
    Stop();
  }

  void Start() {
    CHECK(!worker_.get());
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(new Task(this)),
        "SlblkMonitor");
    worker_->Start();
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

 private:
  BlockManager* bm_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  friend class Task;

  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(SealedBlockMonitor* monitor) : monitor_(monitor) {
    }

    bool operator()(void* arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

   private:
    SealedBlockMonitor* monitor_{nullptr};
    std::mutex mu_;
    std::condition_variable cond_;
  };

  void Do();
};

}  // namespace dancenn
