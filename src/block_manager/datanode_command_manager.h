// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/24
// Description

#ifndef BLOCK_MANAGER_DATANODE_COMMAND_MANAGER_H_
#define BLOCK_MANAGER_DATANODE_COMMAND_MANAGER_H_

#include <glog/logging.h>

#include <map>
#include <mutex>
#include <unordered_map>
#include <utility>

#include "base/defer.h"
#include "base/read_write_lock.h"
#include "base/stop_watch.h"
#include "block_manager/block_manager_metrics.h"
#include "datanode_manager/datanode_info.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"

using BlockID = uint64_t;

namespace dancenn {

class BlockManagerMetrics;

template <typename CmdType>
class DatanodeCommandManager {
 public:
  // max_cmd_size_each_dn = &FLAGS_xxx,
  // and we can change *max_cmd_size_each_dn dynamically.
  // See test/base/gflags_test.cc for more infos.
  explicit DatanodeCommandManager(int32_t* max_cmd_size_each_dn)
      : max_cmd_size_each_dn_(max_cmd_size_each_dn) {
    CHECK_NOTNULL(max_cmd_size_each_dn_);
  }

  bool Add(DatanodeID dn_id, CmdType&& cmd);
  std::size_t Get(DatanodeID dn_id,
                  cloudfs::datanode::HeartbeatResponseProto* response,
                  BlockManagerMetrics* bm_metrics = nullptr);

 private:
  using CmdsType = std::pair<std::mutex, std::unordered_map<BlockID, CmdType>>;
  bool AddCmdInternal(CmdsType* cmds, CmdType cmd);
  std::size_t GetCmdInternal(
      CmdsType* cmds,
      cloudfs::datanode::HeartbeatResponseProto* response,
      BlockManagerMetrics* bm_metrics);

 private:
  int32_t* const max_cmd_size_each_dn_;
  ReadWriteLock rwlock_;
  // NOTICE: Don't use vector!
  // When you use vector, resize/push_back may invalidate old elements!
  std::map<DatanodeID, CmdsType> dn_to_cmds_;
};

using UploadCmdMgr =
    DatanodeCommandManager<cloudfs::datanode::UploadCommandProto>;
using NeCmdMgr =
    DatanodeCommandManager<cloudfs::datanode::NotifyEvictableCommandProto>;
using BrCmdMgr =
    DatanodeCommandManager<cloudfs::datanode::BlockReportCommandProto>;
using AbortBrCmdMgr = 
    DatanodeCommandManager<cloudfs::datanode::AbortBlockReportCommandProto>;
using LoadCmdMgr = DatanodeCommandManager<cloudfs::datanode::LoadCommandProto>;
using MergeCmdMgr =
    DatanodeCommandManager<cloudfs::datanode::MergeCommandProto>;
using BlockIdCmdMgr =
    DatanodeCommandManager<cloudfs::datanode::BlockIdCommandProto>;

template <>
bool BrCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::BlockReportCommandProto>>*
        cmds,
    cloudfs::datanode::BlockReportCommandProto cmd);

template <>
bool AbortBrCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::AbortBlockReportCommandProto>>*
        cmds,
    cloudfs::datanode::AbortBlockReportCommandProto cmd);

template <>
std::size_t UploadCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::UploadCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
std::size_t NeCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::NotifyEvictableCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
std::size_t MergeCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::MergeCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
std::size_t BrCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID,
                                 cloudfs::datanode::BlockReportCommandProto>>*
        cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
std::size_t AbortBrCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<
                  BlockID,
                  cloudfs::datanode::AbortBlockReportCommandProto>>* cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
std::size_t LoadCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::LoadCommandProto>>*
        cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
bool LoadCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::LoadCommandProto>>*
        cmds,
    cloudfs::datanode::LoadCommandProto cmd);

template <>
std::size_t BlockIdCmdMgr::GetCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::BlockIdCommandProto>>*
        cmds,
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics);

template <>
bool BlockIdCmdMgr::AddCmdInternal(
    std::pair<std::mutex,
              std::unordered_map<BlockID, cloudfs::datanode::BlockIdCommandProto>>*
        cmds,
    cloudfs::datanode::BlockIdCommandProto cmd);

}  // namespace dancenn

#endif  // BLOCK_MANAGER_DATANODE_COMMAND_MANAGER_H_
