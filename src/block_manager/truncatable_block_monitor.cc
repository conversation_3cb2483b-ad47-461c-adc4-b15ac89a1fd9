#include "truncatable_block_monitor.h"

#include <glog/logging.h>

#include <chrono>

#include "block_manager/block_manager.h"

DECLARE_int32(truncatable_block_monitor_interval_ms);
DECLARE_bool(run_ut);

namespace dancenn {

void TruncatableBlockMonitor::Do() {
  LOG_IF(INFO, !FLAGS_run_ut) << "Start to do some truncate block count works.";
  auto res = bm_->ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (res.first.HasException()) {
    LOG_IF(INFO, !FLAGS_run_ut) << "Skip due to standby mode.";
    return;
  }
  if (bm_->safemode_->IsOn()) {
    LOG_IF(INFO, !FLAGS_run_ut) << "Skip due to safemode is on.";
    return;
  }
  auto start = std::chrono::steady_clock::now();
  if (bm_->IsPopulatingReplicationQueues()) {
    bm_->ComputeTruncatableBlockWork();
  }
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::steady_clock::now() - start)
                      .count();
  LOG_IF(INFO, !FLAGS_run_ut)
      << "Truncatable block monitor time: " << duration << "(ms)";
}

bool TruncatableBlockMonitor::Task::operator()(void* arg) {
  (void)arg;

  while (!stop_) {
    monitor_->Do();

    int interval = FLAGS_truncatable_block_monitor_interval_ms;
    LOG_IF(INFO, !FLAGS_run_ut)
        << "Truncatable block monitor will sleep: " << interval << "(ms)";
    std::chrono::milliseconds period{interval};

    std::unique_lock<std::mutex> lock(mu_);
    cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
  }
  return true;
}

}  // namespace dancenn
