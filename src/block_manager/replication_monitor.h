// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_REPLICATION_MONITOR_H_
#define BLOCK_MANAGER_REPLICATION_MONITOR_H_

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>

namespace dancenn {

class BlockManager;

class ReplicationMonitor {
 public:
  explicit ReplicationMonitor(BlockManager* bm) : bm_(bm) {}
  ~ReplicationMonitor() {
    Stop();
  }

  void Start() {
    CHECK(!worker_.get());
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(
            new Task(this)), "ReplMonitor");
    worker_->Start();
  }

  void TriggerReplicationWork()  {
    if (worker_) {
      cond_.notify_all();
    }
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

 private:
  BlockManager* bm_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::condition_variable cond_;
  friend class Task;

  class Task : public cnetpp::concurrency::Task {
   public:
    Task(ReplicationMonitor* monitor) : monitor_(monitor) {}

    bool operator()(void* arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      monitor_->cond_.notify_all();
    }

   private:
    ReplicationMonitor* monitor_{nullptr};
    std::mutex mu_;
  };

  void Do();
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_REPLICATION_MONITOR_H_

