// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "block_manager/block_info.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include "base/logger_metrics.h"
#include "base/pb_converter.h"
#include "block_manager/block_map_slice.h"

DECLARE_uint32(dfs_replication_max);

namespace dancenn {

std::string BlockUCStateToString(const BlockUCState& s) {
  switch (s) {
    case BlockUCState::kComplete:
      return "BlockUCState::kComplete";
    case BlockUCState::kUnderConstruction:
      return "BlockUCState::kUnderConstruction";
    case BlockUCState::kUnderRecovery:
      return "BlockUCState::kUnderRecovery";
    case BlockUCState::kCommitted:
      return "BlockUCState::kCommitted";
    case BlockUCState::kPersisted:
      return "BlockUCState::kPersisted";
    default:
      LOG(FATAL) << "unreachable code";
      return "";
  }
  LOG(FATAL) << "unreachable code";
  return "";
}

bool HasBeenCommitted(BlockUCState s) {
  return s == BlockUCState::kCommitted || s == BlockUCState::kComplete ||
         s == BlockUCState::kPersisted;
}

bool HasBeenComplete(BlockUCState s) {
  return s == BlockUCState::kComplete || s == BlockUCState::kPersisted;
}

// BlockInfo

std::string BlockInfo::ToString() const {
  std::string res = blk().ToString();
  res = res + "{";
  res = res + "inode_id: " + std::to_string(inode_id_) + ", ";
  res = res + "parent_id: " + std::to_string(parent_id_) + ", ";
  res = res + "expect_rep: " + std::to_string(expected_rep_) + ", ";
  res = res + "uc_state: " + std::to_string(static_cast<int>(uc_state_)) + ", ";
  res = res + "last_replication_index: " + std::to_string(last_replication_index_) + ", ";
  res = res + "changing_pufs_info: " + std::to_string(in_transaction_) + ", ";
  res = res + "storages: ";
  for (int i = 0; i < size_; ++i) {
    res += std::to_string(first_storage_id_[i]) + "|";
  }
  if (size_ > 0) {
    res.resize(res.length() - 1);
  }
  res = res + "}";
  return res;
}

DatanodeID BlockInfo::storage_id(size_t index) const {
  if (index < size_) {
    return storage_ids()[index];
  }
  return kInvalidDatanodeID;
}

size_t BlockInfo::IndexOf(DatanodeID storage_id) const {
  for (uint8_t i = 0; i < size_; i++) {
    if (storage_ids()[i] == storage_id) {
      return i;
    }
  }
  return static_cast<size_t>(-1);
}

void BlockInfo::Init(uint64_t inode_id,
                     uint64_t parent_id,
                     const Block& blk,
                     uint8_t capacity,
                     cloudfs::IoMode write_mode,
                     BlockUCState state) {
  inode_id_ = inode_id;
  parent_id_ = parent_id;
  blk_ = blk;
  capacity_ = capacity;
  size_ = 0;
  expected_rep_ = capacity;
  write_mode_ = write_mode;
  uc_state_ = state;

  lock_transaction_failed_cnt_ = 0;

  lock_file_ = "unlocked";
  lock_line_ = 0;

  prev_unlock_file_ = "unlocked";
  prev_unlock_line_ = 0;
}

void BlockInfo::set_capacity(uint8_t capacity) {
  capacity_ = capacity;
}

bool BlockInfo::AddStorage(uint32_t storage_id, bool* already_exist) {
  for (uint8_t i = 0; i < size_; ++i) {
    if (storage_ids()[i] == storage_id) {
      if (already_exist) {
        *already_exist = true;
      }
      return true;
    }
  }
  if (size_ >= capacity_) {
    return false;
  }
  storage_ids()[size_++] = storage_id;
  reset_last_replication_index();
  return true;
}

bool BlockInfo::RemoveStorage(uint32_t storage_id) {
  for (uint8_t i = 0; i < size_; ++i) {
    if (storage_ids()[i] == storage_id) {
      if (i != (size_ - 1)) {
        storage_ids()[i] = storage_ids()[size_ - 1];
      }
      --size_;
      reset_last_replication_index();
      return true;
    }
  }
  return false;
}

void BlockInfo::ClearStorages() {
  size_ = 0;
  reset_last_replication_index();
}

bool BlockInfo::Commit(const Block& blk) {
  // TODO(xiong): maybe sealed and truncated
  LOG_IF(WARNING, blk.num_bytes < blk_.num_bytes)
    << "Commit size(" << blk.num_bytes << ") is less than record size("
    << blk_.num_bytes << "). commit block " << blk
    << ", record block " << blk_;
  if (uc_state_ == BlockUCState::kUnderRecovery) {
    LOG(WARNING) << "Try to commit under recovery block " << blk_.id;
  }

  LOG_IF(WARNING,
         blk.gs != kBlockProtocolV2GenerationStamp &&
             blk.num_bytes < blk_.num_bytes)
      << "Commit size(" << blk.num_bytes << ") is less than record size("
      << blk_.num_bytes << "). commit block " << blk << ", record block "
      << blk_;

  CHECK(blk.id == blk_.id)
    << "Try to commit block " << blk_ << " with wrong id " << blk;

  if (HasBeenCommitted()) {
    return false;
  }

  blk_ = blk;
  uc_state_ = BlockUCState::kCommitted;
  VLOG(12) << "Commit B" << blk.id;
  return true;
}

bool BlockInfo::Persist(const Block& blk) {
  if (id() != blk.id || num_bytes() != blk.num_bytes || gs() != blk.gs) {
    // 1. Replaying OpSetBlockPufsInfo before replaying OpAddBlock.
    // 2. Reuse last block?
    LOG(WARNING) << "Failed to persist unmatched blk, " << ToString()
                 << ", " << blk;
    return false;
  }
  if (uc_state_ != BlockUCState::kCommitted &&
      uc_state_ != BlockUCState::kComplete) {
    return false;
  }
  uc_state_ = BlockUCState::kPersisted;
  return true;
}

bool BlockInfo::GetInTransaction() {
  return in_transaction_;
}

bool BlockInfo::TryLockInTransaction(const char* file, uint32_t line) {
  if (in_transaction_) {
    LOG(ERROR) << "BlockInfo is in transaction, B" << id()
               << ", lock holder is " << lock_file_ << ":" << lock_line_
               << ", prev unlock is " << prev_unlock_file_ << ":"
               << prev_unlock_line_ << ", new holder is " << file << ":"
               << line;
    if (lock_transaction_failed_cnt_++ > 10) {
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }
    return false;
  }
  in_transaction_ = true;
  lock_file_ = file;
  lock_line_ = line;
  return true;
}

bool BlockInfo::TryUnlockInTransaction(const char* file, uint32_t line) {
  if (!in_transaction_) {
    LOG_WITH_LEVEL(ERROR) << "BlockInfo is not in transaction, B" << id()
                          << ", lock holder is " << lock_file_ << ":" << lock_line_
               << ", prev unlock is " << prev_unlock_file_ << ":"
               << prev_unlock_line_ << ", now is " << file << ":" << line;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  }
  in_transaction_ = false;
  lock_transaction_failed_cnt_ = 0;
  prev_unlock_file_ = file;
  prev_unlock_line_ = line;
  return true;
}

bool BlockInfo::IsSafeToRelease(const BlockInfoProto& bip) {
  if (bip.block_id() != id() || bip.gen_stamp() != gs() ||
      bip.num_bytes() != num_bytes() || bip.inode_id() != inode_id_) {
    LOG(ERROR) << "BlockInfoProto is different from BlockInfo: " << ToString()
               << " " << PBConverter::ToCompactJsonString(bip);
    return false;
  }
  if (in_transaction_) {
    LOG(INFO) << "BlockInfo is in transaction, B" << id() << ", lock holder is "
              << lock_file_ << ":" << lock_line_;
    if (lock_transaction_failed_cnt_++ > 10) {
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }
    return false;
  }
  if (uc_state_ != BlockUCState::kPersisted) {
    VLOG(12) << "Block is not persisted, B" << id();
    return false;
  }
  if (bip.state() != BlockInfoProto::kPersisted &&
      bip.state() != BlockInfoProto::kDeprecated) {
    VLOG(12) << "B" << id() << " isn't persisted.";
    return false;
  }
  return true;
}

BlockInfoInTransactionGuard::BlockInfoInTransactionGuard(BlockMapSlice* slice,
                                                         BlockID block_id,
                                                         const char* file,
                                                         int line)
    : slice_(slice), block_id_(block_id) {
  CHECK_NOTNULL(slice_);
  auto bi = slice_->Locate(block_id_);
  CHECK_NOTNULL(bi);
  own_lock_ = bi->TryLockInTransaction(file, line);
}

BlockInfoInTransactionGuard::~BlockInfoInTransactionGuard() {
  if (own_lock_) {
    auto bi = slice_->Locate(block_id_);
    CHECK_NOTNULL(bi);
    CHECK(bi->TryUnlockInTransaction(__FILE__, __LINE__)) << bi->id();

    own_lock_ = false;
  }
}

bool BlockInfoInTransactionGuard::OwnLock() const {
  return own_lock_;
}

bool BlockInfoInTransactionGuard::Lock(const char* file, int line) {
  auto bi = slice_->Locate(block_id_);
  CHECK_NOTNULL(bi);
  return own_lock_ = bi->TryLockInTransaction(file, line);
}

bool BlockInfoInTransactionGuard::Unlock(const char* file, int line) {
  auto bi = slice_->Locate(block_id_);
  CHECK_NOTNULL(bi);

  own_lock_ = false;
  return bi->TryUnlockInTransaction(file, line);
}

}  // namespace dancenn
