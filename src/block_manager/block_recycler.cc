// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/02
// Description

#include "block_manager/block_recycler.h"

#include <cnetpp/concurrency/task.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <condition_variable>
#include <set>

#include "base/closure.h"
#include "base/logger_metrics.h"
#include "base/platform.h"
#include "ha/operations.h"

DECLARE_int32(namespace_type);

DECLARE_int32(ongoing_op_del_depring_blks_max_size);
DECLARE_int32(del_depring_blks_batch_size);

DECLARE_int32(op_del_depred_blks_batch_size);
DECLARE_int32(wait_age_before_seek_depred_blks_iter_to_first);

DECLARE_int32(ongoing_op_del_depred_blks_max_size);
DECLARE_int32(del_depred_blks_batch_size);
DECLARE_int32(not_full_op_del_depred_blks_mature_age);
DECLARE_int32(cached_invalidate_pufs_cmd_max_size);
DECLARE_int32(invalidate_pufs_cmd_batch_size);

DECLARE_string(tos_endpoint);
DECLARE_string(tos_bucket);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_string(tos_region);
DECLARE_bool(tos_virtual_host_style);

DECLARE_int32(delete_tos_block_interval_ms);

#define RETURN_IF_HA_IN_TRANSITION(v) \
  if (ha_state_->InTransition()) {          \
    return v;                               \
  }

namespace dancenn {

BlockRecycler::BlockRecycler(HAStateBase* ha_state,
                             BlockManager* block_manager,
                             std::shared_ptr<EditLogContextBase> edit_log_ctx,
                             std::shared_ptr<MetaStorage> meta_storage)
    : is_active_(false),
      ha_state_(ha_state),
      block_manager_(block_manager),
      bip_write_manager_(nullptr),
      edit_log_ctx_(edit_log_ctx),
      meta_storage_(meta_storage) {
}

void BlockRecycler::SetHAState(HAStateBase* ha_state) {
  ha_state_ = ha_state;
}

void BlockRecycler::SetMetaStorage(std::shared_ptr<MetaStorage> meta_storage) {
  meta_storage_ = meta_storage;
}

void BlockRecycler::TestOnlySetEditLogSender(
    std::shared_ptr<EditLogSenderBase> sender) {
  LOG(INFO) << "BlockRecycler set testonly edit log sender: " << sender.get();
  edit_log_sender_ = sender;
}

void BlockRecycler::StartActive(BIPWriteManagerBase* bip_write_manager) {
  if (is_active_) {
    return;
  }
  is_active_ = true;
  bip_write_manager_ = bip_write_manager;
  // HAState::StartActiveServicesInternal already called
  // EditLogContext::OpenForWrite for us.
  if (edit_log_sender_ == nullptr) {
    edit_log_sender_ = CreateEditLogSender();
  }
}

void BlockRecycler::StopActive() {
  if (!is_active_) {
    return;
  }
  meta_storage_->WaitNoPending();
  edit_log_sender_->Sync(true);
  // HAState::StopStandbyServicesInternal already called EditLogContext::Close.
  edit_log_sender_.reset();
  bip_write_manager_ = nullptr;
  is_active_ = false;
}

std::pair<Status, dancenn::vshared_lock> BlockRecycler::CheckOperation() {
  return ha_state_->CheckOperation(OperationsCategory::kWrite);
}

bool BlockRecycler::SeekIterator(rocksdb::Iterator* it,
                                 const std::string& last_iterator,
                                 bool allow_seek_to_first) {
  CHECK_NOTNULL(it);
  if (last_iterator.empty() && !allow_seek_to_first) {
    return false;
  }
  VLOG(10) << "BlockRecycler::SeekIterator Trace" << " before Seek";
  it->Seek(last_iterator);
  VLOG(10) << "BlockRecycler::SeekIterator Trace" << " after Seek";
  CHECK(it->status().ok());
  if (!it->Valid()) {
    if (last_iterator.empty() || !allow_seek_to_first) {
      // Wait all edit logs finished.
      // Avoid rescanning same deprecating blks.
      return false;
    }
    VLOG(10) << "BlockRecycler::SeekIterator Trace"
             << " after SeekToFirst";
    it->SeekToFirst();
    VLOG(10) << "BlockRecycler::SeekIterator Trace"
             << " after SeekToFirst";
    CHECK(it->status().ok());
  }
  return it->Valid();
}

BlockID BlockRecycler::DecodeBlockID(rocksdb::Slice slice) {
  CHECK_EQ(slice.size(), sizeof(BlockID) / sizeof(uint8_t));
  return platform::ReadBigEndian<BlockID>(slice.data(), 0);
}

std::unique_ptr<EditLogSenderBase> BlockRecycler::CreateEditLogSender() {
  return std::unique_ptr<EditLogSenderBase>(new EditLogSender(edit_log_ctx_));
}

DepringBlockRecycler::DepringBlockRecycler(
    HAStateBase* ha_state,
    BlockManager* block_manager,
    std::shared_ptr<EditLogContextBase> edit_log_ctx,
    std::shared_ptr<MetaStorage> meta_storage)
    : BlockRecycler(ha_state, block_manager, edit_log_ctx, meta_storage),
      ongoing_op_del_depring_blks_size_(0) {
}

bool DepringBlockRecycler::Recycle() {
  if (!CanProcessMoreBlks()) {
    return false;
  }
  VLOG(10) << "DepringBlockRecycler::Recycle trace " << "CanProcessMoreBlks";
  std::unique_ptr<rocksdb::Iterator> it =
      meta_storage_->GetDeprecatingBlkIterator();
  if (!SeekIterator(
          it.get(), last_iterator_, ongoing_op_del_depring_blks_size_ == 0)) {
    VLOG(15) << "SkipRecycleDeprecatingBlock, invalid iterator";
    return false;
  }
  VLOG(10) << "DepringBlockRecycler::Recycle trace " << "SeekIterator";
  auto cres = CheckOperation();
  if (cres.first.HasException()) {
    return false;
  }
  VLOG(10) << "DepringBlockRecycler::Recycle trace " << "CheckOperation";
  RETURN_IF_HA_IN_TRANSITION(false);

  DepringBlksToBeDel depring_blks;
  int missing_blk_cnt = 0;
  int in_transaction_blk_cnt = 0;
  int succeed_blk_cnt = 0;
  int total_scanned_blk_cnt = 0;
  {
    for (; it->Valid() && depring_blks.dangling_blk_ids_size() +
                                  depring_blks.depred_bips_size() <
                              FLAGS_del_depring_blks_batch_size;
         it->Next()) {
      RETURN_IF_HA_IN_TRANSITION(false);
      total_scanned_blk_cnt++;
      BlockID blk_id = DecodeBlockID(it->key());
      std::unique_ptr<BlockMapSlice>& s = block_manager_->slice(blk_id);
      std::unique_lock<BlockMapSlice> slice_guard(*s);
      s->TellLockHolder(__FILE__, __LINE__);
      BlockInfoGuard bi_guard(s.get(), blk_id, true);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      if (!bi) {
        LOG(ERROR) << "MissingBlock B" << blk_id;
        missing_blk_cnt++;
        depring_blks.add_dangling_blk_ids(blk_id);
        continue;
      }
      if (!bi->TryLockInTransaction(__FILE__, __LINE__)) {
        in_transaction_blk_cnt++;
        continue;
      }
      succeed_blk_cnt++;
      BlockInfoProto bip;
      if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
        // TryUnlockInTransaction always returns true
        // because TryLockInTransaction returned true before.
        CHECK(bi->TryUnlockInTransaction(__FILE__, __LINE__)) << blk_id;
        MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
        LOG_WITH_LEVEL(ERROR) << "Delete deprecating, missing B" << blk_id;
      } else {
        bip.set_state(BlockInfoProto::kDeprecated);
        depring_blks.add_depred_bips()->CopyFrom(bip);
      }
    }
  }
  VLOG(8) << "total_scanned_blk_cnt=" << total_scanned_blk_cnt
          << " missing blk cnt=" << missing_blk_cnt
          << " in transaction blk cnt=" << in_transaction_blk_cnt++
          << " succeed_blk_cnt=" << succeed_blk_cnt;
  VLOG(8) << "depring_blks.dangling_blk_ids_size()="
          << depring_blks.dangling_blk_ids_size()
          << " depring_blks.depred_bips_size()="
          << depring_blks.depred_bips_size();
  if (depring_blks.dangling_blk_ids_size() + depring_blks.depred_bips_size() ==
      0) {
    return false;
  }

  RpcClosure* done = new RpcClosure(
      [this, depring_blks](Status ignored) {
        VLOG(10) << "DepringBlockRecycler::Recycle trace " << "in callback";
        for (const BlockInfoProto& bip : depring_blks.depred_bips()) {
          {
            BlockID blk_id = bip.block_id();
            std::unique_ptr<BlockMapSlice>& s = block_manager_->slice(blk_id);
            std::unique_lock<BlockMapSlice> slice_guard(*s);
            s->TellLockHolder(__FILE__, __LINE__);
            BlockInfoGuard bi_guard(s.get(), blk_id, true);
            BlockInfo* bi = bi_guard.GetBlockInfo();
            if (!bi) {
              LOG(ERROR) << "MissingBlock B" << blk_id;
              continue;
            }
            if (!bi->TryUnlockInTransaction(__FILE__, __LINE__)) {
              MFC(LoggerMetrics::Instance()
                      .unlock_block_info_in_transaction_lock_failed_)
                  ->Inc();
              LOG_WITH_LEVEL(ERROR)
                  << "Unlock in transaction lock failed, B" << blk_id;
            }
          }
          VLOG(10) << "DepringBlockRecycler::Recycle trace "
                   << "in callback after for";
        }
        block_manager_->RemoveBlocksAndUpdateSafeMode(
            std::vector<BlockInfoProto>(depring_blks.depred_bips().begin(),
                                        depring_blks.depred_bips().end()));
        ongoing_op_del_depring_blks_size_--;
        VLOG(10) << "DepringBlockRecycler::Recycle trace "
                 << "in callback after RemoveBlocksAndUpdateSafeMode";
      },
      true);
  done->set_barrier(std::move(cres.second));
  ClosureGuard done_guard(done);
  ongoing_op_del_depring_blks_size_++;
  auto txid = edit_log_sender_->LogDelDepringBlks(depring_blks);
  VLOG(10) << "DepringBlockRecycler::Recycle trace "
           << "after EditLog";

  meta_storage_->DelDepringBlks(depring_blks, txid, { done_guard.release() });
  VLOG(10) << "DepringBlockRecycler::Recycle trace "
           << "after push DB";

  if (it->Valid()) {
    last_iterator_ = it->key().ToString();
  } else {
    last_iterator_ = "";
  }
  return true;
}

std::unique_ptr<BlockMapSlice>& DepringBlockRecycler::TestOnlySlice(
    BlockID blk_id) {
  return block_manager_->slice(blk_id);
}

bool DepringBlockRecycler::CanProcessMoreBlks() {
  if (!is_active_) {
    return false;
  }
  RETURN_IF_HA_IN_TRANSITION(false);
  int ongoing_op_del_depring_blks_size =
      ongoing_op_del_depring_blks_size_.load();
  CHECK_GE(ongoing_op_del_depring_blks_size, 0);
  if (ongoing_op_del_depring_blks_size >=
      FLAGS_ongoing_op_del_depring_blks_max_size) {
    VLOG(15) << "SkipRecycleDeprecatingBlock, too many ongoing editlogs: "
             << ongoing_op_del_depring_blks_size;
    return false;
  }
  return true;
}

DepredBlockRecycler::DepredBlockRecycler(
    HAStateBase* ha_state,
    BlockManager* block_manager,
    std::shared_ptr<EditLogContextBase> edit_log_ctx,
    std::shared_ptr<MetaStorage> meta_storage)
    : BlockRecycler(ha_state, block_manager, edit_log_ctx, meta_storage),
      iter_age_(0),
      meet_invalid_iter_age_(0),
      ongoing_op_del_depred_blks_size_(0),
      op_del_depred_blks_age_(0) {
}

void DepredBlockRecycler::SetBlockPoolId(const std::string& bpid) {
  blockpool_id_ = bpid;
}

void DepredBlockRecycler::StartActive(BIPWriteManagerBase* bip_write_manager) {
  BlockRecycler::StartActive(bip_write_manager);
  std::lock_guard<std::mutex> _(op_del_depred_blks_queue_mutex_);
  decltype(op_del_depred_blks_queue_) empty_queue;
  op_del_depred_blks_queue_.swap(empty_queue);
  CHECK(op_del_depred_blks_queue_.empty());
}

void DepredBlockRecycler::StopActive() {
  BlockRecycler::StopActive();
}

bool DepredBlockRecycler::Recycle() {
  iter_age_++;
  if (!CanProcessMoreBlks()) {
    return false;
  }
  VLOG(10) << "DepredBlockRecycler::Recycle Trace" << " Init";

  std::unique_ptr<rocksdb::Iterator> it =
      meta_storage_->GetDeprecatedBlkIterator();
  VLOG(10) << "DepredBlockRecycler::Recycle Trace"
           << " GetDeprecatedBlkIterator";

  bool allow_seek_to_first = true;
  if (iter_age_ > 1 &&
      iter_age_ - meet_invalid_iter_age_ <
          FLAGS_wait_age_before_seek_depred_blks_iter_to_first) {
    allow_seek_to_first = false;
  }
  if (allow_seek_to_first) {
    std::lock_guard<std::mutex> _1(invalidate_pufs_cmd_queue_mutex_);
    std::lock_guard<std::mutex> _2(op_del_depred_blks_queue_mutex_);
    VLOG(10) << "DepredBlockRecycler::Recycle Trace" << " add lock";

    allow_seek_to_first = invalidate_pufs_cmd_queue_.empty() &&
                          (ongoing_op_del_depred_blks_size_ == 0 &&
                           op_del_depred_blks_queue_.empty());
  }
  RETURN_IF_HA_IN_TRANSITION(false);
  if (!SeekIterator(it.get(), last_iterator_, allow_seek_to_first)) {
    VLOG(15) << "SkipRecycleDeprecatedBlock, invalid iterator";
    return false;
  }
  RETURN_IF_HA_IN_TRANSITION(false);
  VLOG(10) << "DepredBlockRecycler::Recycle Trace" << " after Seek";

  for (int i = 0; i < FLAGS_del_depred_blks_batch_size && it->Valid() &&
                  CanProcessMoreBlks();
       i++, it->Next()) {
    RETURN_IF_HA_IN_TRANSITION(false);
    BlockID blk_id = DecodeBlockID(it->key());
    BlockInfoProto bip;
    if (!meta_storage_->GetBlockInfo(blk_id, &bip)) {
      MFC(LoggerMetrics::Instance().missing_block_error_)->Inc();
      LOG_WITH_LEVEL(ERROR) << "Delete deprecated, missing B" << blk_id;
      AddToOpDelDepredBlksQueue(blk_id);
    } else {
      // Hdfs mode need DN to delete pufs object
      if (NameSpace::IsHdfsMode() && bip.has_pufs_name() && !bip.pufs_name().empty()) {
        std::lock_guard<std::mutex> _(invalidate_pufs_cmd_queue_mutex_);
        if (invalidate_pufs_cmd_queue_.empty() ||
            invalidate_pufs_cmd_queue_.back().blocks_size() >=
                FLAGS_invalidate_pufs_cmd_batch_size) {
          invalidate_pufs_cmd_queue_.push(
              cloudfs::datanode::InvalidatePufsCommandProto());
        }
        ExtendedBlockProto* b = invalidate_pufs_cmd_queue_.back().add_blocks();
        b->set_poolid(blockpool_id_);
        b->set_blockid(bip.block_id());
        b->set_generationstamp(bip.gen_stamp());
        b->set_numbytes(bip.num_bytes());
        b->set_blockpufsname(bip.pufs_name());
      } else {
        AddToOpDelDepredBlksQueue(blk_id);
      }
    }
  }
  VLOG(10) << "DepredBlockRecycler::Recycle Trace" << " after for";

  if (it->Valid()) {
    last_iterator_ = it->key().ToString();
  } else {
    last_iterator_ = "";
    meet_invalid_iter_age_ = iter_age_;
  }
  return true;
}

int DepredBlockRecycler::ConsumeOpDelDepredBlksQueue() {
  op_del_depred_blks_age_++;
  if (!is_active_) {
    return false;
  }
  std::lock_guard<std::mutex> _(op_del_depred_blks_queue_mutex_);
  if (op_del_depred_blks_queue_.empty()) {
    return false;
  }
  VLOG(10) << "DepredBlockRecycler Trace" << " init";

  int sent_op_cnt = 0;
  while (!op_del_depred_blks_queue_.empty()) {
    VLOG(10) << "DepredBlockRecycler Trace" << " one while after init";
    int age = op_del_depred_blks_queue_.front().first;
    const auto& depred_blks = op_del_depred_blks_queue_.front().second;
    if (depred_blks.blk_ids_size() < FLAGS_op_del_depred_blks_batch_size &&
        op_del_depred_blks_age_ - age <
            FLAGS_not_full_op_del_depred_blks_mature_age) {
      break;
    }
    // HAState::CheckOperation promises:
    // When it can't take shared lock of barrier_, it returns false immediately.
    // No blocking happens, so we don't need to consider lock order of
    // op_del_depred_blks_queue_mutex_ and barrier_.
    auto cres = CheckOperation();
    if (cres.first.HasException()) {
      break;
    }
    VLOG(10) << "DepredBlockRecycler Trace" << " after CheckOperation";

    ongoing_op_del_depred_blks_size_++;
    RpcClosure* done = new RpcClosure(
        [this](Status ignored) {
          std::lock_guard<std::mutex> _(op_del_depred_blks_queue_mutex_);
          ongoing_op_del_depred_blks_size_--;
        },
        true);
    done->set_barrier(std::move(cres.second));
    auto txid = edit_log_sender_->LogDelDepredBlks(depred_blks);
    VLOG(10) << "DepredBlockRecycler Trace" << " after EditLog";

    meta_storage_->DelDepredBlks(depred_blks, txid, done);
    VLOG(10) << "DepredBlockRecycler Trace" << " after Push DB";

    sent_op_cnt++;
    op_del_depred_blks_queue_.pop();
  }
  return sent_op_cnt;
}

bool DepredBlockRecycler::GetInvalidatePufsCmds(
    cloudfs::datanode::HeartbeatResponseProto* response,
    BlockManagerMetrics* bm_metrics) {
  CHECK_NOTNULL(response);
  std::lock_guard<std::mutex> _(invalidate_pufs_cmd_queue_mutex_);
  if (invalidate_pufs_cmd_queue_.empty()) {
    VLOG(10) << "Returns 0 invalidate pufs cmds";
    return false;
  }
  auto& cmd = invalidate_pufs_cmd_queue_.front();
  if (!cmd.IsInitialized() || cmd.blocks_size() == 0) {
    LOG(ERROR) << "EmptyInvalidatePufsCmd, queue size: "
               << invalidate_pufs_cmd_queue_.size();
    return false;
  }
  auto invalidate_cmd = response->add_cmds();
  invalidate_cmd->set_cmdtype(
      cloudfs::datanode::DatanodeCommandProto::InvalidatePufsCommand);
  VLOG(10) << "Returns " << cmd.blocks_size() << " invalidate pufs cmds";
  invalidate_cmd->mutable_invalidatepufscmd()->Swap(&cmd);
  if (bm_metrics) {
    MFC(bm_metrics->dn_cmd_cnt_invalidate_pufs_)
        ->Inc(invalidate_cmd->invalidatepufscmd().blocks_size());
  }
  invalidate_pufs_cmd_queue_.pop();
  return true;
}

void DepredBlockRecycler::AddToOpDelDepredBlksQueue(BlockID blk_id) {
  if (!is_active_) {
    return;
  }
  RETURN_IF_HA_IN_TRANSITION(void());
  std::lock_guard<std::mutex> _(op_del_depred_blks_queue_mutex_);
  if (op_del_depred_blks_queue_.empty() ||
      op_del_depred_blks_queue_.back().second.blk_ids_size() >=
          FLAGS_op_del_depred_blks_batch_size) {
    op_del_depred_blks_queue_.emplace(std::pair<int, DepredBlksToBeDel>(
        op_del_depred_blks_age_, DepredBlksToBeDel()));
  }
  op_del_depred_blks_queue_.back().second.add_blk_ids(blk_id);
}

bool DepredBlockRecycler::CanProcessMoreBlks() {
  if (!is_active_) {
    return false;
  }
  RETURN_IF_HA_IN_TRANSITION(false);
  std::lock_guard<std::mutex> _1(invalidate_pufs_cmd_queue_mutex_);
  if (invalidate_pufs_cmd_queue_.size() >
      FLAGS_cached_invalidate_pufs_cmd_max_size) {
    return false;
  } else if (invalidate_pufs_cmd_queue_.size() ==
             FLAGS_cached_invalidate_pufs_cmd_max_size) {
    return !invalidate_pufs_cmd_queue_.empty() &&
           invalidate_pufs_cmd_queue_.back().blocks_size() <
               FLAGS_invalidate_pufs_cmd_batch_size;
  }
  std::lock_guard<std::mutex> _2(op_del_depred_blks_queue_mutex_);
  CHECK_GE(ongoing_op_del_depred_blks_size_, 0);
  int ongoing_op_del_depred_blks_size =
      ongoing_op_del_depred_blks_size_ + op_del_depred_blks_queue_.size();
  if (ongoing_op_del_depred_blks_size >
      FLAGS_ongoing_op_del_depred_blks_max_size) {
    return false;
  } else if (ongoing_op_del_depred_blks_size ==
             FLAGS_ongoing_op_del_depred_blks_max_size) {
    if (!op_del_depred_blks_queue_.empty() &&
        op_del_depred_blks_queue_.back().second.blk_ids_size() <
            FLAGS_op_del_depred_blks_batch_size) {
      return true;
    }
    return false;
  } else {
    return true;
  }
}

class DeleteTosBlockTask : public cnetpp::concurrency::Task {
 public:
  DeleteTosBlockTask(NameSpace* ns, DepredBlockRecycler* depred_block_recycler)
      : ns_(ns), depred_block_recycler_(depred_block_recycler) {
    CHECK(ns_);
    CHECK(depred_block_recycler_);
  }

  bool operator()(void* arg) override {
    while (!stop_) {
      DeleteBlocks();
      int interval = FLAGS_delete_tos_block_interval_ms;
      VLOG(8) << "Tos block deleter will sleep: " << interval << "(ms)";
      std::chrono::milliseconds period{interval};
      std::unique_lock<std::mutex> lock(mu_);
      cond_.wait_for(lock, period, [this]() -> bool { return stop_; });
    }
    return true;
  }

  void Stop() {
    std::unique_lock<std::mutex> lock(mu_);
    stop_ = true;
    cond_.notify_all();
  }

 private:
  void DeleteBlocks() {
    cloudfs::datanode::HeartbeatResponseProto heartbeat_resp;
    if (!depred_block_recycler_->GetInvalidatePufsCmds(&heartbeat_resp)) {
      return;
    }

    // UFS Process
    if (ns_->ufs_env() == nullptr) {
      LOG_WITH_LEVEL(ERROR) << "no tos mode";
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return;
    }
    auto s = CHECK_NOTNULL(ns_->ufs_env())->DeleteBlocks(heartbeat_resp);

    if (!s.IsOK()) {
      VLOG(8) << "Delete blocks in UFS failed, skip CFS process";
      return;
    }

    // CFS process
    std::vector<BlockID> blks_to_delete;

    for (const auto& cmd : heartbeat_resp.cmds()) {
      if (!(cmd.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                 InvalidatePufsCommand &&
            cmd.has_invalidatepufscmd())) {
        continue;
      }
      const auto& delete_cmd = cmd.invalidatepufscmd();

      for (const auto& block : delete_cmd.blocks()) {
        blks_to_delete.push_back(block.blockid());
      }
    }

    for (BlockID blk_id : blks_to_delete) {
      depred_block_recycler_->AddToOpDelDepredBlksQueue(blk_id);
    }
  }

 private:
  NameSpace* ns_;
  DepredBlockRecycler* depred_block_recycler_;
  std::mutex mu_;
  std::condition_variable cond_;
};

TosBlockDeleter::TosBlockDeleter(DepredBlockRecycler* depred_block_recycler)
    : depred_block_recycler_(depred_block_recycler) {
  CHECK(depred_block_recycler_);
}

void TosBlockDeleter::SetNameSpace(NameSpace* ns) {
  ns_ = ns;
  CHECK(ns_);
}

void TosBlockDeleter::StartActive() {
  CHECK(!worker_);
  worker_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::shared_ptr<cnetpp::concurrency::Task>(
          new DeleteTosBlockTask(ns_, depred_block_recycler_)),
      "DeleteTosBlockTask");
  worker_->Start();
}

void TosBlockDeleter::StopActive() {
  if (worker_) {
    worker_->Stop();
    worker_.reset();
  }
}

}  // namespace dancenn
