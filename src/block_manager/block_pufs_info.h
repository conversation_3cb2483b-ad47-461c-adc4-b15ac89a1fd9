// Copyright(c) @2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/05/10
// Description

#ifndef BLOCK_MANAGER_BLOCK_PUFS_INFO_H_
#define BLOCK_MANAGER_BLOCK_PUFS_INFO_H_

#include <cstdint>
#include <ostream>  // NOLINT(readability/streams)
#include <set>
#include <string>

#include "block_manager/block_info_proto.h"
#include "block_manager/datanode_command.h"
#include "cnetpp/base/csonpp.h"

namespace dancenn {

// BlockPufsState will be persisted to book keeper and RocksDB.
// Please assign integral value to it.
enum class BlockPufsState : int8_t {
  kLocal = 0,
  kUploadIssued = 2,
  kUploadFailed = 3,
  kPersisted = 4,
  kDeprecated = 5,
  kDeleted = 6,
};

std::ostream& operator<<(std::ostream& ss, const BlockPufsState& state);

class BlockPufsInfo {
 public:
  cnetpp::base::Value SerializeToJson() const;
  std::string SerializeToJsonString() const;
  bool DeserializeFromJson(const cnetpp::base::Value& jsonValue);
  bool DeserializeFromJsonString(const std::string& jsonString);

  UploadCmd GetUploadCmd() const;
  NotifyEvictableCmd GetNotifyEvictableCmd() const;
  bool FromBlockInfoProto(const std::string& blockpool_id,
                          const BlockInfoProto& bip);
  BlockInfoProto GetBlockInfoProto() const;
  bool GetBlockInfoProto(BlockInfoProto* bip) const;

 public:
  std::string block_pool_id_;
  uint64_t block_id_;
  uint64_t gen_stamp_;
  uint64_t num_bytes_;

  uint64_t inode_id_;
  // TODO(ruanjunbin): Need to persist BlockInfo::last_replication_index_?

  BlockPufsState state_;
  std::string pufs_name_;

  int upload_issued_times_;
  // NOTICE: Please use DatanodeInfo::uuid_ instead of
  // DatanodeInfo::internal_id_, internal id may change if name node restarts.
  std::string dn_uuid_;
  std::set<std::string> aborted_upload_ids_;
  std::string upload_id_;
  // In seconds.
  uint64_t nn_exp_ts_;
  uint64_t dn_exp_ts_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_BLOCK_PUFS_INFO_H_
