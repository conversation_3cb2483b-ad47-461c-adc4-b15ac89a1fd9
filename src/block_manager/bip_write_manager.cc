// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/09/02
// Description

#include "block_manager/bip_write_manager.h"

#include <absl/strings/str_format.h>  // For StrFormat.
#include <gflags/gflags.h>            // For DECLARE_uint32, etc.
#include <glog/logging.h>             // For CHECK, LOG.

#include <algorithm>          // For any_of, count_if, sort, max, shuffle.
#include <chrono>             // For chrono.
#include <cstddef>            // For size_t.
#include <nlohmann/json.hpp>  // For json.
#include <set>                // For set.
#include <shared_mutex>       // For shared_lock.
#include <tuple>              // For forward_as_tuple.

#include "base/defer.h"                         // For DEFER.
#include "base/java_exceptions.h"               // For JavaExceptions.
#include "base/logger_metrics.h"                // For LoggerMetrics.
#include "base/time_util.h"                     // For TimeUtilV2.
#include "base/to_json_string.h"                // For ToJsonCompactString.
#include "datanode_manager/advice.h"            // For PlacementAdvice.
#include "datanode_manager/datanode_manager.h"  // For DatanodeManager.
#include "datanode_manager/storage_policy.h"  // For StoragePolicyId, kBlockStoragePolicyIdUnspecified.
#include "namespace/namespace.h"              // For NS type check

DECLARE_int32(namespace_type);

DECLARE_string(block_index_policy);

DECLARE_int32(acquire_dirty_bip_blk_lock_slow_log_ms);
DECLARE_int32(hold_dirty_bip_blk_lock_slow_log_ms);
DECLARE_int32(acquire_dirty_bip_bucket_lock_slow_log_ms);
DECLARE_int32(hold_dirty_bip_bucket_lock_slow_log_ms);
DECLARE_int32(acquire_block_index_lock_slow_log_ms);
DECLARE_int32(hold_block_index_lock_slow_log_ms);

DECLARE_int32(dirty_block_info_proto_bucket_num);
DECLARE_int32(async_write_dirty_bip_batch_size);

DECLARE_int32(min_upload_timeout_s);
DECLARE_int32(max_upload_timeout_s);
DECLARE_int32(nn_dn_clock_drift_s);
DECLARE_int32(datanode_keep_alive_timeout_sec);

DECLARE_uint32(dfs_replication_min);
DECLARE_int32(block_report_hard_limit_ms);

DECLARE_uint64(acc_append_max_size);
DECLARE_uint32(acc_mpu_max_part);
DECLARE_uint64(acc_mpu_part_threshold);

namespace dancenn {
namespace {

BDebugMsg kDefaultDirtyBipDebugMsg{"", 0, 0};

bool AreEqual(BlockUCState s1, BlockInfoProto::Status s2) {
  if (s2 == BlockInfoProto::kUploadIssued) {
    return s1 == BlockUCState::kCommitted || s1 == BlockUCState::kComplete;
  }
  return static_cast<int>(s1) == static_cast<int>(s2);
}

ReplicaInfoProto& GetOrCreateReplica(BlockInfoProto* bip,
                                     const std::string& dn_uuid) {
  CHECK_NOTNULL(bip);
  ReplicaInfoProto* replica = nullptr;
  for (auto& r : *bip->mutable_replicas()) {
    if (r.dn_uuid() == dn_uuid) {
      replica = &r;
      break;
    }
  }
  if (replica == nullptr) {
    replica = bip->add_replicas();
  }
  return *replica;
}

void AddNewReplicas(BlockInfoProto* bip,
                    uint64_t gen_stamp,
                    const std::vector<std::string>& dn_uuids) {
  CHECK_NOTNULL(bip);
  uint32_t report_ts = TimeUtilV2::GetNowEpochMs() / 1000;
  for (const std::string& dn_uuid : dn_uuids) {
    ReplicaInfoProto& replica = GetOrCreateReplica(bip, dn_uuid);
    replica.Clear();
    replica.set_report_ts(report_ts);
    replica.set_reporter(ReplicaInfoProto::kNamenode);
    replica.set_gen_stamp(gen_stamp);
    replica.set_dn_uuid(dn_uuid);
  }
}

bool DelReplica(BlockInfoProto* bip, const std::string& dn_uuid) {
  CHECK_NOTNULL(bip);
  for (int i = 0; i < bip->replicas_size(); i++) {
    if (bip->replicas(i).dn_uuid() == dn_uuid) {
      bip->mutable_replicas()->DeleteSubrange(i, 1);
      return true;
    }
  }
  return false;
}

bool ShouldReplicaReportTsFollowFBRTs(const ReplicaInfoProto& replica,
                                      const std::string& dn_uuid) {
  return replica.dn_uuid() == dn_uuid &&
         replica.state() == cloudfs::ReplicaStateProto::FINALIZED &&
         replica.reporter() == ReplicaInfoProto::kDatanode &&
         !replica.has_invalidate_ts() && !replica.is_bad();
}

}  // namespace

BMetrics& BMetrics::Instance() {
  static BMetrics b_metrics;
  static std::once_flag flag;
  std::call_once(flag, []() {
    auto center = MetricsCenter::Instance();
    auto metrics = center->RegisterMetrics("BIPWriteManager");
    b_metrics.acquire_blk_lock_time =
        metrics->RegisterHistogram("AcquireBlkLockTime");
    b_metrics.hold_blk_lock_time =
        metrics->RegisterHistogram("HoldBlkLockTime");
    b_metrics.acquire_bucket_lock_time =
        metrics->RegisterHistogram("AcquireBucketLockTime");
    b_metrics.hold_bucket_lock_time =
        metrics->RegisterHistogram("HoldBucketLockTime");
    b_metrics.get_or_load_dirty_block_info_proto_if_missing_time =
        metrics->RegisterHistogram("GetOrLoadDirtyBlockInfoProtoIfMissing");
    b_metrics.upload_is_denied_num = metrics->RegisterCounter("UploadIsDenied");
  });
  return b_metrics;
}

ReplicaNumV2::ReplicaNumV2()
    : invalidating(0),
      corrupt(0),
      stale(0),
      decommission(0),
      replicating(0),
      healthy(0) {
}

ReplicaNumV2 ReplicaNumV2::CountReplica(DatanodeManager* datanode_manager,
                                        const BlockInfoProto& bip) {
  return ReplicaNumV2::CountReplica(datanode_manager,
                                    const_cast<BlockInfoProto*>(&bip));
}

ReplicaNumV2 ReplicaNumV2::CountReplica(
    DatanodeManager* datanode_manager,
    BlockInfoProto* bip,
    std::unordered_set<DatanodeInfoPtr>* containing_dns,
    std::unordered_set<DatanodeInfoPtr>* decommission_dns,
    std::vector<ReplicaInfoProto*>* healthy_replicas,
    std::unordered_set<DatanodeInfoPtr>* healthy_dns) {
  CHECK_NOTNULL(datanode_manager);
  ReplicaNumV2 rn;
  auto current_ms = TimeUtilV2::GetNowEpochMs();
  for (ReplicaInfoProto& replica : *bip->mutable_replicas()) {
    DatanodeInfoPtr dn =
        datanode_manager->GetDatanodeFromUuid(replica.dn_uuid());
    DEFER([&]() {
      if (containing_dns != nullptr && dn != nullptr) {
        containing_dns->emplace(dn);
      }
    });
    if (replica.dn_uuid().empty()) {
      LOG(ERROR) << "dn uuid is missing: " << ToJsonCompactString(*bip);
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }
    // NOTICE: The order of following if statements is very important!
    // If the replica exists, does its data match expectations?
    if (IsCorrupt(*bip, replica)) {
      rn.corrupt++;
    }
    // Does the replica exist?
    else if (replica.invalidate_ts() > 0) {
      rn.invalidating++;
    } else if (dn == nullptr || !dn->IsAlive() ||
               (!ShouldReplicaReportTsFollowFBRTs(replica, dn->uuid()) &&
                current_ms >= ((uint64_t)replica.report_ts()) * 1000 +
                                  FLAGS_block_report_hard_limit_ms)) {
      rn.stale++;
    } else if (replica.reporter() == ReplicaInfoProto::kNamenode) {
      rn.replicating++;
    } else if (dn->IsDecommissionInProgress()) {
      rn.decommission++;
      if (decommission_dns != nullptr) {
        decommission_dns->emplace(dn);
      }
    } else {
      rn.healthy++;
      if (healthy_dns != nullptr) {
        healthy_dns->emplace(dn);
      }
      if (healthy_replicas != nullptr) {
        healthy_replicas->emplace_back(&replica);
      }
    }
  }
  return rn;
}

bool ReplicaNumV2::IsCorrupt(const BlockInfoProto& bip,
                             const ReplicaInfoProto& replica) {
  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery: {
      return !(replica.gen_stamp() == bip.gen_stamp() &&
               // replica.num_bytes() >= bip.num_bytes() &&
               (replica.state() == cloudfs::ReplicaStateProto::RBW ||
                replica.state() == cloudfs::ReplicaStateProto::FINALIZED));
    } break;
    case BlockInfoProto::kCommitted:
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued:
    case BlockInfoProto::kPersisted: {
      return !(replica.gen_stamp() == bip.gen_stamp() &&
               replica.num_bytes() == bip.num_bytes() &&
               replica.state() == cloudfs::ReplicaStateProto::FINALIZED);
    } break;
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted: {
      return false;
    } break;
    default: {
      LOG(FATAL) << ToJsonCompactString(bip);
      return false;
    } break;
  }
}

// The smaller the score, the less healthy the replica.
struct ReplicaHealthScore {
  bool has_been_reported_recently;
  bool has_not_been_invalidated;
  bool is_datanode_alive;
  bool is_not_bad;
  bool is_not_corrupt;
  bool is_datanode_not_in_decommission;
  uint64_t gen_stamp;
  uint32_t num_bytes;
  uint32_t report_ts;

  bool operator<(const ReplicaHealthScore& rhs) {
    if (has_been_reported_recently != rhs.has_been_reported_recently) {
      return has_been_reported_recently < rhs.has_been_reported_recently;
    }
    if (has_not_been_invalidated != rhs.has_not_been_invalidated) {
      return has_not_been_invalidated < rhs.has_not_been_invalidated;
    }
    if (is_datanode_alive != rhs.is_datanode_alive) {
      return is_datanode_alive < rhs.is_datanode_alive;
    }
    if (is_not_bad != rhs.is_not_bad) {
      return is_not_bad < rhs.is_not_bad;
    }
    if (is_not_corrupt != rhs.is_not_corrupt) {
      return is_not_corrupt < rhs.is_not_corrupt;
    }
    if (is_datanode_not_in_decommission !=
        rhs.is_datanode_not_in_decommission) {
      return is_datanode_not_in_decommission <
             rhs.is_datanode_not_in_decommission;
    }
    if (gen_stamp != rhs.gen_stamp) {
      return gen_stamp < rhs.gen_stamp;
    }
    if (num_bytes != rhs.num_bytes) {
      return num_bytes < rhs.num_bytes;
    }
    if (report_ts != rhs.report_ts) {
      return report_ts < rhs.report_ts;
    }
    return false;
  }
};

// class SortReplicas {
//  public:
//   explicit SortReplicas(DatanodeManager* datanode_manager, BlockInfoProto*
//   bip)
//       : datanode_manager_(datanode_manager),
//         bip_(bip),
//         current_sec_(TimeUtilV2::GetNowEpochMs() / 1000) {
//     CHECK_NOTNULL(datanode_manager_);
//     CHECK_NOTNULL(bip_);
//   }
//
//   void operator()() {
//     // std::sort will call DatanodeInfo::IsAlive/IsDecommissionInProgress
//     multi
//     // times, we must make sure that return same results.
//     for (const ReplicaInfoProto& replica : bip_->replicas()) {
//       const std::string& dn_uuid = replica.dn_uuid();
//       DatanodeInfoPtr dn = datanode_manager_->GetDatanodeFromUuid(dn_uuid);
//       dn_status_[dn_uuid].first = dn != nullptr && dn->IsAlive();
//       dn_status_[dn_uuid].second =
//           dn != nullptr && dn->IsDecommissionInProgress();
//     }
//     std::sort(bip_->mutable_replicas()->begin(),
//               bip_->mutable_replicas()->end(),
//               [this](const ReplicaInfoProto& lhs, const ReplicaInfoProto&
//               rhs) {
//                 return GetHealthScore(lhs) < GetHealthScore(rhs);
//               });
//   }
//
//  private:
//   ReplicaHealthScore GetHealthScore(const ReplicaInfoProto& replica) {
//     ReplicaHealthScore score;
//     score.has_been_reported_recently =
//         replica.reporter() != ReplicaInfoProto::kNamenode &&
//         current_sec_ > replica.report_ts() +
//         FLAGS_block_report_hard_limit_ms;
//     score.has_not_been_invalidated = replica.invalidate_ts() == 0;
//     score.is_datanode_alive = dn_status_.at(replica.dn_uuid()).first;
//     score.is_not_bad = !replica.is_bad();
//     score.is_not_corrupt =
//         // Case A.
//         ((bip_->state() == BlockInfoProto::kUnderConstruction ||
//           bip_->state() == BlockInfoProto::kUnderRecovery) &&
//          (replica.gen_stamp() >= bip_->gen_stamp() &&
//           replica.num_bytes() >= bip_->num_bytes())) ||
//         // Case B.
//         ((bip_->state() == BlockInfoProto::kCommitted ||
//           bip_->state() == BlockInfoProto::kComplete ||
//           bip_->state() == BlockInfoProto::kUploadIssued ||
//           bip_->state() == BlockInfoProto::kPersisted) &&
//          (replica.gen_stamp() == bip_->gen_stamp() &&
//           replica.num_bytes() >= bip_->gen_stamp()));
//     score.is_datanode_not_in_decommission =
//         !dn_status_.at(replica.dn_uuid()).second;
//     score.gen_stamp = replica.gen_stamp();
//     score.num_bytes = replica.num_bytes();
//     score.report_ts = replica.report_ts();
//     return score;
//   }
//
//  private:
//   DatanodeManager* datanode_manager_;
//   BlockInfoProto* bip_;
//   uint64_t current_sec_;
//   std::unordered_map<
//       /*dn_uuid=*/std::string,
//       std::pair</*is_dn_alive=*/bool, /*is_dn_in_decommission=*/bool>>
//       dn_status_;
// };

#define LOCK_WITH_DEBUG_MSG(                                          \
    lock_statement, metric_id, slow_log_ms, object_type)              \
  {                                                                   \
    auto stop_watch_ms = TimeUtilV2::GetNowEpochMs4Log();             \
                                                                      \
    do {                                                              \
      lock_statement;                                                 \
    } while (0);                                                      \
                                                                      \
    auto current_ms = TimeUtilV2::GetNowEpochMs4Log();                \
    auto duration_ms = current_ms - stop_watch_ms;                    \
    MFH(metric_id)->Update(duration_ms);                              \
    if (duration_ms >= slow_log_ms) {                                 \
      using LogMsgFmtT =                                              \
          absl::ParsedFormat<'s', 'd', 's', 'd', 'd', 's', 'd', 'd'>; \
      static std::once_flag flag;                                     \
      static std::unique_ptr<LogMsgFmtT> log_msg_fmt;                 \
      std::call_once(flag, []() {                                     \
        log_msg_fmt = LogMsgFmtT::New(                                \
            "L{%s:%d} acquires %s lock to access blk %d for %dms, "   \
            "L{%s:%d} held it to access blk %d before.");             \
        CHECK(log_msg_fmt);                                           \
      });                                                             \
      LOG(INFO) << absl::StrFormat(*log_msg_fmt,                      \
                                   debug_msg.filename,                \
                                   debug_msg.line_number,             \
                                   object_type,                       \
                                   debug_msg.blk_id,                  \
                                   duration_ms,                       \
                                   debug_msg_.filename,               \
                                   debug_msg_.line_number,            \
                                   debug_msg_.blk_id);                \
    }                                                                 \
    stop_watch_ms_ = current_ms;                                      \
    debug_msg_ = debug_msg;                                           \
  }
#define UNLOCK_WITH_DEBUG_MSG(                                        \
    unlock_statement, metric_id, slow_log_ms, object_type)            \
  {                                                                   \
    auto current_ms = TimeUtilV2::GetNowEpochMs4Log();                \
    auto duration_ms = current_ms - stop_watch_ms_;                   \
    MFH(metric_id)->Update(duration_ms);                              \
    if (duration_ms >= slow_log_ms) {                                 \
      using LogMsgFmtT = absl::ParsedFormat<'s', 'd', 's', 'd', 'd'>; \
      static std::once_flag flag;                                     \
      static std::unique_ptr<LogMsgFmtT> log_msg_fmt;                 \
      std::call_once(flag, []() {                                     \
        log_msg_fmt = LogMsgFmtT::New(                                \
            "L{%s:%d} held %s lock to access blk %d for %dms");       \
        CHECK(log_msg_fmt);                                           \
      });                                                             \
      LOG(INFO) << absl::StrFormat(*log_msg_fmt,                      \
                                   debug_msg_.filename,               \
                                   debug_msg_.line_number,            \
                                   object_type,                       \
                                   debug_msg_.blk_id,                 \
                                   duration_ms);                      \
    }                                                                 \
    stop_watch_ms_ = 0;                                               \
    /* debug_msg_ = kDefaultDirtyBipDebugMsg; */                      \
                                                                      \
    do {                                                              \
      unlock_statement;                                               \
    } while (0);                                                      \
  }

template <typename M>
BUniqueLock<M>::BUniqueLock() : mtx_(nullptr) {
  // DLOG(INFO) << "NoLock " << this;
}

template <typename M>
BUniqueLock<M>::BUniqueLock(M* mtx, const BDebugMsg& debug_msg) : mtx_(mtx) {
  CHECK_NOTNULL(mtx_);
  mtx_->Lock(debug_msg);
  // DLOG(INFO) << "Lock " << this;
}

template <typename M>
BUniqueLock<M>::BUniqueLock(BUniqueLock<M>&& other) {
  mtx_ = other.mtx_;
  other.mtx_ = nullptr;
}

template <typename M>
BUniqueLock<M>& BUniqueLock<M>::operator=(BUniqueLock<M>&& other) {
  // https://en.cppreference.com/w/cpp/language/operators#Assignment_operator
  if (this == &other) {
    return *this;
  }
  // Release resource in *this.
  if (mtx_ != nullptr) {
    mtx_->Unlock();
    LOG(ERROR) << "Call move assignment of BUniqueLock with not trivial this";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }
  mtx_ = other.mtx_;
  other.mtx_ = nullptr;
  return *this;
}

template <typename M>
BUniqueLock<M>::~BUniqueLock() {
  if (mtx_ != nullptr) {
    mtx_->Unlock();
    // DLOG(INFO) << "UnLock " << this;
  } else {
    // DLOG(INFO) << "NoUnLock " << this;
  }
}

// Explicit instantiation declaration.
template class BUniqueLock<DirtyBlockInfoProto>;
template class BUniqueLock<DirtyBlockInfoProtoBucket>;

BIPLockComponents::BIPLockComponents() : test_only_id_(0) {
}

BIPLockComponents::BIPLockComponents(int test_only_id)
    : test_only_id_(test_only_id) {
}

int BIPLockComponents::TestOnlyId() const {
  return test_only_id_;
}

void BIPLockComponents::Add(BlockID blk_id,
                            DirtyBlockInfoProto* dbip,
                            BUniqueLock<DirtyBlockInfoProto>&& lock) {
  CHECK(locks_.find(blk_id) == locks_.end());
  CHECK(dbip);
  locks_.emplace(std::piecewise_construct,
                 std::forward_as_tuple(blk_id),
                 std::forward_as_tuple(dbip, std::move(lock)));
}

DirtyBlockInfoProto* BIPLockComponents::Get(BlockID blk_id) const {
  auto it = locks_.find(blk_id);
  return (it != locks_.end()) ? it->second.first : nullptr;
}

void BIPLockComponents::Reset() {
  locks_.clear();
}

int BIPLockComponents::TestOnlySize() const {
  return locks_.size();
}

DirtyBlockInfoProto::DirtyBlockInfoProto(BlockID blk_id)
    : mtx_(new std::mutex),
      waiters_(0),
      test_only_is_locked_(false),
      unstarted_tx_num_(0),
      ongoing_tx_num_(0),
      stop_watch_ms_(0),
      debug_msg_(kDefaultDirtyBipDebugMsg) {
  CHECK(!bip_.IsInitialized());
  bip_.set_block_id(blk_id);
}

DirtyBlockInfoProto::~DirtyBlockInfoProto() {
  CHECK(!mtx_.get()) << bip_.block_id();
}

bool DirtyBlockInfoProto::TryLock4GC(const BDebugMsg& debug_msg) {
  if (!mtx_->try_lock()) {
    return false;
  }
  // Since GarbageCollector does not call PreLock before calling TryLock4GC,
  // there is no need to decrement waiters_.
  CHECK_GE(waiters_.load(), 0) << ToString();
  test_only_is_locked_ = true;
  stop_watch_ms_ = TimeUtilV2::GetNowEpochMs4Log();
  debug_msg_ = debug_msg;
  return true;
}

void DirtyBlockInfoProto::PreLock() {
  CHECK_GE(waiters_.fetch_add(1), 0) << ToString();
}

void DirtyBlockInfoProto::Lock(const BDebugMsg& debug_msg) {
  LOCK_WITH_DEBUG_MSG(mtx_->lock(),
                      (BMetrics::Instance().acquire_blk_lock_time),
                      FLAGS_acquire_dirty_bip_blk_lock_slow_log_ms,
                      "blk");
  CHECK_GT(waiters_.fetch_sub(1), 0) << ToString();
  test_only_is_locked_ = true;
}

void DirtyBlockInfoProto::Unlock() {
  if (unstarted_tx_num_ != 0) {
    LOG(ERROR) << "unstarted_tx_num_ != 0 when releasing lock: " << ToString();
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }
  test_only_is_locked_ = false;
  UNLOCK_WITH_DEBUG_MSG(mtx_->unlock(),
                        (BMetrics::Instance().hold_blk_lock_time),
                        FLAGS_hold_dirty_bip_blk_lock_slow_log_ms,
                        "blk");
}

std::unique_ptr<std::mutex> DirtyBlockInfoProto::ReleaseLock() {
  DCHECK(TestOnlyIsLocked());
  test_only_is_locked_ = false;
  return std::move(mtx_);
}

bool DirtyBlockInfoProto::TestOnlyIsLocked() const {
  return test_only_is_locked_;
}

bool DirtyBlockInfoProto::IsInitialized() const {
  DCHECK(TestOnlyIsLocked());
  return bip_.IsInitialized();
}

std::string DirtyBlockInfoProto::ToString() const {
  // DCHECK(TestOnlyIsLocked());
  return absl::StrFormat("bip_:%s,unstarted_tx_num_:%d,ongoing_tx_num_:%d",
                         ToJsonCompactString(bip_),
                         unstarted_tx_num_,
                         ongoing_tx_num_);
}

void DirtyBlockInfoProto::Load(const BlockInfoProto& bip) {
  DCHECK(TestOnlyIsLocked());
  CHECK(!IsInitialized());
  CHECK(bip.IsInitialized()) << bip.InitializationErrorString();
  bip_ = bip;
}

BlockInfoProto& DirtyBlockInfoProto::Get() {
  DCHECK(TestOnlyIsLocked());
  return bip_;
}

const BlockInfoProto& DirtyBlockInfoProto::Get() const {
  DCHECK(TestOnlyIsLocked());
  return bip_;
}

bool DirtyBlockInfoProto::IsFlushed() const {
  DCHECK(TestOnlyIsLocked());
  CHECK(IsInitialized()) << ToString();
  auto waiters = waiters_.load();
  CHECK_GE(waiters, 0);
  CHECK_GE(unstarted_tx_num_, 0);
  CHECK_GE(ongoing_tx_num_, 0);
  return waiters == 0 && unstarted_tx_num_ == 0 && ongoing_tx_num_ == 0;
}

void DirtyBlockInfoProto::IncVersion() {
  DCHECK(TestOnlyIsLocked());
  // bip_.version() is zero if bip_ is an old format.
  // CHECK_GT(bip_.version(), 0);
  uint64_t version = TimeUtilV2::GetNowEpochMs();
  // In case clock drift happens.
  if (UNLIKELY(version <= bip_.version())) {
    version = bip_.version() + 1;
  }
  bip_.set_version(version);
  unstarted_tx_num_++;
}

Status DirtyBlockInfoProto::PreCommit(BlockInfoProto* bip) {
  DCHECK(TestOnlyIsLocked());
  CHECK_NOTNULL(bip);
  if (!IsInitialized()) {
    std::string msg =
        absl::StrFormat("Try to write uninitialized B%d", bip_.block_id());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (unstarted_tx_num_ <= 0) {
    std::string msg =
        absl::StrFormat("Try to write B%d with unstarted_tx_num_: %d",
                        bip_.block_id(),
                        unstarted_tx_num_);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  unstarted_tx_num_--;
  CHECK_GE(unstarted_tx_num_, 0);
  ongoing_tx_num_++;
  *bip = bip_;
  return Status();
}

Status DirtyBlockInfoProto::PostCommit(const BlockInfoProto& bip) {
  DCHECK(TestOnlyIsLocked());
  CHECK_EQ(bip.block_id(), bip_.block_id()) << ToString();
  if (ongoing_tx_num_ <= 0) {
    std::string msg =
        absl::StrFormat("Try to callback B%d with ongoing_tx_num_: %d",
                        bip_.block_id(),
                        ongoing_tx_num_);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  ongoing_tx_num_--;
  CHECK_GE(ongoing_tx_num_, 0) << ToString();
  return Status();
}

Status DirtyBlockInfoProto::IsEqualTo(const Block& blk_from_dn,
                                      const std::string& dn_uuid) const {
  DCHECK(TestOnlyIsLocked());
  if (!IsInitialized()) {
    std::string msg = absl::StrFormat(
        "DN{uuid:%s} tries to compare non-initialized B%d with "
        "wrong replica{id:%d,gs:%d,num_bytes:%d}",
        dn_uuid,
        bip_.block_id(),
        blk_from_dn.id,
        blk_from_dn.gs,
        blk_from_dn.num_bytes);
    // https://bits.bytedance.net/meego/cfs/issue/detail/2050743
    // NN can't find block info in the following scenario:
    // 1. NN failover.
    // 2. DN sent upload succeed block report.
    // 3. DN sent full block report.
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  if (!(blk_from_dn.id == bip_.block_id() &&
        blk_from_dn.gs == bip_.gen_stamp() &&
        blk_from_dn.num_bytes == bip_.num_bytes())) {
    if (blk_from_dn.id == bip_.block_id() &&
        blk_from_dn.gs == bip_.gen_stamp() &&  //
        bip_.num_bytes() == 0 &&
        (bip_.state() == BlockInfoProto::kUnderConstruction ||
         bip_.state() == BlockInfoProto::kUnderRecovery)) {
      // This is a normal case.
      return Status(JavaExceptions::kIOException, "Block is not committed");
    } else {
      std::string msg = absl::StrFormat(
          "DN{uuid:%s} tries to compare block {id:%d,gs:%d,num_bytes:%d} with "
          "wrong replica{id:%d,gs:%d,num_bytes:%d}",
          dn_uuid,
          bip_.block_id(),
          bip_.gen_stamp(),
          bip_.num_bytes(),
          blk_from_dn.id,
          blk_from_dn.gs,
          blk_from_dn.num_bytes);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
  }
  return Status();
}

Status DirtyBlockInfoProto::GetUploadCommand(
    const std::string& blockpool_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd) {
  DCHECK(TestOnlyIsLocked());
  CHECK_NOTNULL(upload_cmd);
  if (!IsInitialized()) {
    std::string msg = absl::StrFormat(
        "Try to GetUploadCommand from uninitialized B%d", bip_.block_id());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (NameSpace::IsHdfsMode()) {
    if (bip_.state() != BlockInfoProto::kUploadIssued) {
      std::string msg = absl::StrFormat(
          "Try to GetUploadCommand from not-upload-issued "
          "B{id:%d,gs:%d,num_bytes:%d,state:%d}",
          bip_.block_id(),
          bip_.gen_stamp(),
          bip_.num_bytes(),
          bip_.state());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    upload_cmd->set_blockpoolid(blockpool_id);
    upload_cmd->set_dnuuid(bip_.dn_uuid());
    upload_cmd->mutable_block()->set_blockid(bip_.block_id());
    upload_cmd->mutable_block()->set_genstamp(bip_.gen_stamp());
    upload_cmd->mutable_block()->set_numbytes(bip_.num_bytes());
    upload_cmd->set_blockpufsname(bip_.pufs_name());
    upload_cmd->set_expts(bip_.dn_exp_ts());
    upload_cmd->set_uploadid(bip_.curr_upload_id());
    for (const auto& aborted_upload_id : bip_.aborted_upload_ids()) {
      upload_cmd->add_aborteduploadids(aborted_upload_id);
    }
    CHECK(upload_cmd->IsInitialized())
        << upload_cmd->InitializationErrorString();
  }

  if (NameSpace::IsAccMode()) {
    if (bip_.state() != BlockInfoProto::kUploadIssued &&
        bip_.state() != BlockInfoProto::kPersisted) {
      std::string msg = absl::StrFormat(
          "Try to GetUploadCommand from not-upload-issued "
          "B{id:%d,gs:%d,num_bytes:%d,state:%d}",
          bip_.block_id(),
          bip_.gen_stamp(),
          bip_.num_bytes(),
          bip_.state());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    upload_cmd->set_blockpoolid(blockpool_id);
    upload_cmd->set_dnuuid(bip_.dn_uuid());
    upload_cmd->mutable_block()->set_blockid(bip_.block_id());
    upload_cmd->mutable_block()->set_genstamp(bip_.gen_stamp());
    upload_cmd->mutable_block()->set_numbytes(bip_.num_bytes());
    upload_cmd->set_blockpufsname(bip_.pufs_name());
    upload_cmd->set_expts(bip_.dn_exp_ts());
    upload_cmd->set_uploadid(bip_.curr_upload_id());
    for (const auto& aborted_upload_id : bip_.aborted_upload_ids()) {
      upload_cmd->add_aborteduploadids(aborted_upload_id);
    }
    upload_cmd->set_uploadtype(bip_.upload_type());
    upload_cmd->set_appendoffset(bip_.pufs_offset());
    upload_cmd->add_partnums(bip_.part_num());
    if (bip_.bundle_length() > 0) {
      /**
       * We use first LocatedBlock in CMD to store offset and length for all
       * prev_blocks. And we set dummy field to pass the check. Only numBytes
       * and offset are useful.
       */
      auto pb = upload_cmd->add_prevblocks();
      auto eb = pb->mutable_b();
      eb->set_poolid("");
      eb->set_blockid(kInvalidBlockID);
      eb->set_generationstamp(0);
      eb->set_offset(bip_.bundle_offset());    // useful
      eb->set_numbytes(bip_.bundle_length());  // useful
      pb->set_offset(0);
      pb->set_corrupt(false);
      auto token = pb->mutable_blocktoken();
      token->set_identifier("");
      token->set_password("");
      token->set_kind("");
      token->set_service("");
    }
    CHECK(upload_cmd->IsInitialized())
        << upload_cmd->InitializationErrorString();
  }
  return Status();
}

Status DirtyBlockInfoProto::GetNotifyEvictableCommand(
    const std::string& blockpool_id,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  DCHECK(TestOnlyIsLocked());
  CHECK_NOTNULL(ne_cmd);
  if (!IsInitialized()) {
    std::string msg = absl::StrFormat(
        "Try to GetNECommand from uninitialized B%d", bip_.block_id());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (!(bip_.state() == BlockInfoProto::kPersisted ||
        bip_.state() == BlockInfoProto::kDeprecated ||
        bip_.state() == BlockInfoProto::kDeleted)) {
    std::string msg = absl::StrFormat(
        "Try to GetNECommand from not-persisted "
        "B{id:%d,gs:%d,num_bytes:%d,state:%d}",
        bip_.block_id(),
        bip_.gen_stamp(),
        bip_.num_bytes(),
        bip_.state());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  ne_cmd->set_blockpoolid(blockpool_id);
  ne_cmd->mutable_block()->set_blockid(bip_.block_id());
  ne_cmd->mutable_block()->set_genstamp(bip_.gen_stamp());
  ne_cmd->mutable_block()->set_numbytes(bip_.num_bytes());
  CHECK(ne_cmd->IsInitialized()) << ne_cmd->InitializationErrorString();
  return Status();
}

DirtyBlockInfoProtoBucket::DirtyBlockInfoProtoBucket()
    : test_only_is_locked_(false),
      stop_watch_ms_(0),
      debug_msg_(kDefaultDirtyBipDebugMsg) {
}

DirtyBlockInfoProtoBucket::DirtyBlockInfoProtoBucket(
    DirtyBlockInfoProtoBucket&& other) {
  LOG(FATAL) << "Please reserve BIPWriteManager::buckets_ first";
}

DirtyBlockInfoProtoBucket::~DirtyBlockInfoProtoBucket() {
  DCHECK(!TestOnlyIsLocked());
  // Active -> Standby, Gracefully Shutdown.
  for (auto& kv : bips_) {
    DirtyBlockInfoProto& dbip = *kv.second;
    dbip.PreLock();
    dbip.Lock({__FILE__, __LINE__, kv.first});
    CHECK(!dbip.IsInitialized() || dbip.IsFlushed()) << dbip.ToString();
    std::unique_ptr<std::mutex> mtx = dbip.ReleaseLock();
    mtx->unlock();
  }
}

void DirtyBlockInfoProtoBucket::Lock(const BDebugMsg& debug_msg) {
  LOCK_WITH_DEBUG_MSG(mtx_.Lock(),
                      (BMetrics::Instance().acquire_bucket_lock_time),
                      FLAGS_acquire_dirty_bip_bucket_lock_slow_log_ms,
                      "bucket");
  test_only_is_locked_ = true;
}

void DirtyBlockInfoProtoBucket::Unlock() {
  test_only_is_locked_ = false;
  UNLOCK_WITH_DEBUG_MSG(mtx_.Unlock(),
                        (BMetrics::Instance().hold_bucket_lock_time),
                        FLAGS_hold_dirty_bip_bucket_lock_slow_log_ms,
                        "bucket");
}

bool DirtyBlockInfoProtoBucket::TestOnlyIsLocked() const {
  return test_only_is_locked_;
}

DirtyBlockInfoProto* DirtyBlockInfoProtoBucket::GetOrCreate(BlockID blk_id) {
  DCHECK(TestOnlyIsLocked());
  auto it = bips_.find(blk_id);
  // Based on https://en.cppreference.com/w/cpp/container/unordered_map/emplace
  // std::unordered_map::emplace may construct a new element even if an element
  // with the same key already exists in the container. If this happens, the
  // newly constructed element will be destroyed right away.
  // If such a scenario occurs, the program will crash at the destructor of
  // DirtyBlockInfoProto because the function ReleaseLock hasn't been invoked
  // prior to the destructor call.
  // Therefore, it's important to ensure that an element with the same key
  // does not already exist in the container before calling emplace.
  if (it == bips_.end()) {
    auto r =
        bips_.emplace(blk_id, std::make_unique<DirtyBlockInfoProto>(blk_id));
    CHECK(r.second);
    it = r.first;
  }
  return it->second.get();
}

BIPWriteManager::BIPWriteManager(
    const std::string& blockpool_id,
    std::shared_ptr<MetaStorage> meta_storage,
    std::shared_ptr<DatanodeManager> datanode_manager)
    : blockpool_id_(blockpool_id),
      meta_storage_(meta_storage),
      datanode_manager_(datanode_manager),
      buckets_(FLAGS_dirty_block_info_proto_bucket_num),
      bucket_mask_(FLAGS_dirty_block_info_proto_bucket_num - 1) {
  CHECK(!blockpool_id_.empty());
  // CHECK_NOTNULL(meta_storage_.get());
  // CHECK_NOTNULL(datanode_manager_.get());
  CHECK_GT(buckets_.size(), 0);
  CHECK_EQ(bucket_mask_, buckets_.size() - 1);
  CHECK_EQ(bucket_mask_ & buckets_.size(), 0)
      << "Bucket size should be power of 2";
}

BIPLockComponents BIPWriteManager::Lock(const std::set<BlockID>& blk_ids,
                                        const char* filename,
                                        uint32_t line_number) {
  BIPLockComponents blk_lck_comps;
  for (BlockID blk_id : blk_ids) {
    if (IsBlockIDValid(blk_id)) {
      DirtyBlockInfoProto* dbip = nullptr;
      {
        BlockID bkt_id = blk_id & bucket_mask_;
        DirtyBlockInfoProtoBucket& bkt = buckets_[bkt_id];
        BUniqueLock<DirtyBlockInfoProtoBucket> bkt_guard(
            &bkt, {filename, line_number, blk_id});
        dbip = bkt.GetOrCreate(blk_id);
        dbip->PreLock();
      }
      // Maybe leave some uninitialized DirtyBlockInfoProto to the gc thread.
      blk_lck_comps.Add(blk_id,
                        dbip,
                        BUniqueLock<DirtyBlockInfoProto>(
                            dbip, {filename, line_number, blk_id}));
    }
  }
  return std::move(blk_lck_comps);
}

BIPLockComponents BIPWriteManager::Lock(const INode& inode,
                                        const char* filename,
                                        uint32_t line_number) {
  std::set<BlockID> blk_ids;
  for (const BlockProto& bp : inode.blocks()) {
    blk_ids.emplace(bp.blockid());
  }
  return Lock(blk_ids, filename, line_number);
}

BIPLockComponents BIPWriteManager::Lock(const std::vector<BlockInfoProto>& bips,
                                        const char* filename,
                                        uint32_t line_number) {
  std::set<BlockID> blk_ids;
  for (const BlockInfoProto& bip : bips) {
    blk_ids.emplace(bip.block_id());
  }
  return Lock(blk_ids, filename, line_number);
}

Status BIPWriteManager::PreCommit(const BIPLockComponents& blk_lck_comps,
                                  BlockID blk_id,
                                  BlockInfoProto* bip) {
  CHECK_NOTNULL(bip);
  if (!IsBlockIDValid(blk_id)) {
    return Status();
  }
  // Caller already holds ha barrier provided by HAState::CheckOperation.
  // Caller already holds lock of DirtyBlockInfoProto.
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when PreCommit", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  return dbip->PreCommit(bip);
}

Status BIPWriteManager::PostCommit(const BIPLockComponents& blk_lck_comps,
                                   const BlockInfoProto& bip) {
  BlockID blk_id = bip.block_id();
  if (!bip.IsInitialized()) {
    return Status();
  }
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when PostCommit", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  return dbip->PostCommit(bip);
}

DirtyBlockInfoProto& BIPWriteManager::TestOnlyGetBlock(BlockID blk_id) {
  BlockID bkt_id = blk_id & bucket_mask_;
  DirtyBlockInfoProtoBucket& bkt = buckets_[bkt_id];
  BUniqueLock<DirtyBlockInfoProtoBucket> _(&bkt, {__FILE__, __LINE__, blk_id});
  return *bkt.GetOrCreate(blk_id);
}

void BIPWriteManager::AddBlock(const BIPLockComponents& blk_lck_comps,
                               const Block& blk,
                               const INode& inode,
                               cloudfs::IoMode write_mode,
                               const std::string& pufs_name,
                               const std::vector<std::string>& dn_uuids) {
  auto blk_id = blk.id;
  DirtyBlockInfoProto* dbip = nullptr;
  CHECK(GetOrLoadDirtyBlockInfoProtoIfMissing(blk_lck_comps,
                                              blk_id,
                                              /*is_creating=*/true,
                                              &dbip)
            .IsOK())
      << blk_id;

  CHECK(!dbip->IsInitialized());
  BlockInfoProto& bip = dbip->Get();
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(blk.id);
  bip.set_gen_stamp(blk.gs);
  bip.set_num_bytes(blk.num_bytes);
  bip.set_inode_id(inode.id());
  bip.set_expected_rep(inode.replication());
  bip.set_write_mode(write_mode);
  if (NameSpace::IsAccMode()) {
    bip.set_type(BlockInfoProto::kACCBlock);
    bip.set_pufs_name(inode.ufs_file_info().key());
    if (inode.ufs_file_info().create_type() ==
        UfsFileCreateType::kUfsFileCreateTypeNormal) {
      bip.set_part_num(1);
      bip.set_bundle_length(0);
      bip.set_bundle_offset(0);
    }
  } else {
    bip.set_type(BlockInfoProto::kHDFSBlock);
    bip.set_pufs_name(pufs_name);
  }
  AddNewReplicas(&dbip->Get(), blk.gs, dn_uuids);
  dbip->IncVersion();
  CHECK(bip.IsInitialized()) << blk_id;
}

Status BIPWriteManager::CompletePenultBlkAndCommitLastBlk(
    const BIPLockComponents& blk_lck_comps,
    const std::string& src,
    const INode& inode,
    const Block& penult_blk,
    const Block& last_blk_from_cli,
    const Block& last_blk_from_inode,
    bool complete_last_blk,
    absl::optional<BlockUCState> force_set_penult_blk_state,
    absl::optional<BlockUCState> force_set_last_blk_state) {
  // penult_blk_id can be 0 or kInvalidBlockID.
  BlockID penult_blk_id = penult_blk.id;
  BlockID last_blk_id = last_blk_from_inode.id;
  DirtyBlockInfoProto* penult_dbip = nullptr;
  DirtyBlockInfoProto* last_dbip = nullptr;
  Status s;
  // Caller will call this function even if there is no block in file.
  if (IsBlockIDValid(penult_blk_id)) {
    s = GetOrLoadDirtyBlockInfoProtoIfMissing(
        blk_lck_comps, penult_blk_id, false, &penult_dbip);
    if (!s.IsOK()) {
      std::string msg = absl::StrFormat(
          "Found missing penult B%d when CompletePenultBlkAndCommitLastBlk",
          penult_blk_id);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
  }
  if (IsBlockIDValid(last_blk_id)) {
    s = GetOrLoadDirtyBlockInfoProtoIfMissing(
        blk_lck_comps, last_blk_id, false, &last_dbip);
    if (!s.IsOK()) {
      std::string msg = absl::StrFormat(
          "Found missing last B%d when CompletePenultBlkAndCommitLastBlk",
          last_blk_id);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
  }

  // bundled_bip and pufs_offset are for ACC mode
  uint64_t pufs_offset = 0;
  if (NameSpace::IsAccMode()) {
    CheckAccINode(inode);
    for (int i = 0; i < inode.blocks_size(); i++) {
      if (inode.blocks(i).blockid() == last_blk_id) {
        break;
      }
      pufs_offset += inode.blocks(i).numbytes();
    }

    // Some checks for AddBlock, not for FinalizeFile
    if (inode.status() == INode_Status_kFileUnderConstruction) {
      // Append object
      if (inode.ufs_file_info().create_type() ==
          UfsFileCreateType::kUfsFileCreateTypeAppend) {
        if (pufs_offset + last_blk_from_cli.num_bytes >
            FLAGS_acc_append_max_size) {
          LOG(ERROR) << "Exceed max size for append object, inode: "
                     << inode.ShortDebugString()
                     << ", pufs_offset: " << pufs_offset
                     << ", num_bytes: " << last_blk_from_cli.num_bytes
                     << ", acc_append_max_size " << FLAGS_acc_append_max_size;
          return Status(JavaExceptions::Exception::kIOException,
                        "Exceed max size for append object");
        }
      }

      // Normal object
      if (inode.ufs_file_info().create_type() ==
          UfsFileCreateType::kUfsFileCreateTypeNormal) {
        if (IsBlockIDValid(penult_blk_id)) {
          const BlockInfoProto& bundled_bip = penult_dbip->Get();
          if (bundled_bip.key_block()) {
            //  penultimate + last == max_part
            //  The new block may have max_part + 1 as part_num
            if (bundled_bip.part_num() + 1 == FLAGS_acc_mpu_max_part) {
              LOG(ERROR)
                  << "Exceed max part for a MPU, cannot add block. inode: "
                  << inode.ShortDebugString();
              return Status(JavaExceptions::Exception::kIOException,
                            "Exceed max part for a MPU, cannot add block");
            }
          }
          LOG(INFO) << "bundled_bip " << bundled_bip.ShortDebugString();
        }
      }
    }
  }

  // Suffix "c" means it is a copy of last_dbip.
  BlockInfoProto penult_bip_c;
  if (IsBlockIDValid(penult_blk_id)) {
    CHECK_NOTNULL(penult_dbip);
    CHECK(penult_dbip->IsInitialized());
    penult_bip_c = penult_dbip->Get();
    switch (penult_bip_c.state()) {
      case BlockInfoProto::kCommitted: {
        CHECK_EQ(penult_bip_c.write_mode(), cloudfs::IoMode::DATANODE_BLOCK)
            << penult_dbip->ToString();
        if (ReplicaNumV2::CountReplica(datanode_manager_.get(), penult_bip_c)
                    .healthy >= FLAGS_dfs_replication_min ||
            (force_set_penult_blk_state.has_value() &&
             force_set_penult_blk_state.value() == BlockUCState::kComplete)) {
          penult_bip_c.set_state(BlockInfoProto::kComplete);
        } else {
          if (penult_bip_c.gen_stamp() == kBlockProtocolV2GenerationStamp) {
            // pass
          } else {
            // Keep compatible with NameSpace::AsyncAddBlock.
            return Status(
                JavaExceptions::kNotReplicatedYetException,
                absl::StrFormat(
                    "Not replicated yet: %s, B%d does not have enough replicas",
                    src,
                    penult_blk_id));
          }
        }
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued:
      case BlockInfoProto::kPersisted: {
      } break;
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery:
      case BlockInfoProto::kDeprecated:
      case BlockInfoProto::kDeleted:
      default: {
        std::string msg =
            absl::StrFormat("Try to complete B%d with inconsistent state %d",
                            penult_blk_id,
                            static_cast<int>(penult_bip_c.state()));
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      } break;
    }
  }
  if (force_set_penult_blk_state.has_value() &&
      !AreEqual(force_set_penult_blk_state.value(), penult_bip_c.state())) {
    do {
      if (force_set_penult_blk_state.value() == BlockUCState::kComplete &&
          penult_bip_c.state() == BlockInfoProto::kPersisted) {
        // There is a small gap between BlockManager and
        // BIPWriteManager during persisting block.
        // Remove it after disable BlockManager.
        break;
      }
      if (penult_bip_c.state() == BlockInfoProto::kComplete &&
          penult_bip_c.gen_stamp() == kBlockProtocolV2GenerationStamp) {
        // TODO(xiong): find root cause
        // ignore for v2 protocol
        break;
      }
      std::string msg = absl::StrFormat(
          "State of penult_bip_c is not equal to force_set_penult_blk_state, "
          "penult_bip_c:{id:%d,gs:%d,num_bytes:%d,state:%d}, "
          "force_set_penult_blk_state:%d",
          penult_bip_c.block_id(),
          penult_bip_c.gen_stamp(),
          penult_bip_c.num_bytes(),
          static_cast<int>(penult_bip_c.state()),
          force_set_penult_blk_state.value());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    } while (false);
  }

  BlockInfoProto last_bip_c;
  if (IsBlockIDValid(last_blk_id)) {
    CHECK_NOTNULL(last_dbip);
    CHECK(last_dbip->IsInitialized());
    // Suffix "c" means it is a copy of last_dbip.
    last_bip_c = last_dbip->Get();
    switch (last_bip_c.state()) {
      case BlockInfoProto::kUnderConstruction: {
        // Check if the relationship between last_blk_from_cli and
        // last_blk_from_inode is valid.
        if (!(last_blk_from_cli.id == last_blk_from_inode.id &&
              last_blk_from_cli.gs == last_blk_from_inode.gs &&
              last_blk_from_cli.num_bytes >= last_blk_from_inode.num_bytes)) {
          std::string msg = absl::StrFormat(
              "Try to commit corrupt last block, "
              "last_blk_from_cli: {id:%d,gs:%d,len:%d}, "
              "last_bp: {id:%d,gs:%d,len:%d}",
              last_blk_from_cli.id,
              last_blk_from_cli.gs,
              last_blk_from_cli.num_bytes,
              last_blk_from_inode.id,
              last_blk_from_inode.gs,
              last_blk_from_inode.num_bytes);
          LOG(ERROR) << msg;
          MFC(LoggerMetrics::Instance().error_)->Inc();
          return Status(JavaExceptions::kIOException, msg);
        }

        // In most cases, last_blk_from_inode{gs,len} = last_bip_c{gs,len}.
        // But NameSpace::AsyncUpdatePipeline has bugs in
        // older version, and last_bip_c.gs < last_blk_from_inode.gs is
        // possible. So we use last_blk_from_inode to update last_bip_c here.
        if (last_blk_from_cli.id == last_blk_from_inode.id &&
            last_blk_from_cli.gs == last_blk_from_inode.gs &&
            last_blk_from_cli.num_bytes == last_blk_from_inode.num_bytes) {
          // TODO(ruanjunbin)
          // Refactor NameSpace::FinalizeFile and remove this branch.
          last_bip_c.set_gen_stamp(last_blk_from_cli.gs);
          last_bip_c.set_num_bytes(last_blk_from_inode.num_bytes);
        } else if (last_bip_c.gen_stamp() < last_blk_from_inode.gs) {
          LOG(ERROR) << absl::StrFormat(
              "last_bip_c.gen_stamp() < last_blk_from_inode.gs, "
              "last_bip_c: {id:%d,gs:%d,num_bytes:%d}, "
              "last_bp: {id:%d,gs:%d,num_bytes:%d}",
              last_bip_c.block_id(),
              last_bip_c.gen_stamp(),
              last_bip_c.num_bytes(),
              last_blk_from_inode.id,
              last_blk_from_inode.gs,
              last_blk_from_inode.num_bytes);
          MFC(LoggerMetrics::Instance().error_)->Inc();
          last_bip_c.set_gen_stamp(last_blk_from_inode.gs);
          last_bip_c.set_num_bytes(last_blk_from_inode.num_bytes);
        } else if (!(last_bip_c.gen_stamp() == last_blk_from_inode.gs &&
                     last_bip_c.num_bytes() == last_blk_from_inode.num_bytes)) {
          // NOTICE: last_blk_from_inode is from old inode, not from cli.
          // BlockInfoProto MUST be consistent with INode.
          std::string msg = absl::StrFormat(
              "Try to commit inconsistent block B%d, "
              "last_bip_c: {id:%d,gs:%d,num_bytes:%d}, "
              "last_blk_from_inode: {id:%d,gs:%d,num_bytes:%d}",
              last_blk_id,
              last_bip_c.block_id(),
              last_bip_c.gen_stamp(),
              last_bip_c.num_bytes(),
              last_blk_from_inode.id,
              last_blk_from_inode.gs,
              last_blk_from_inode.num_bytes);
          LOG(ERROR) << msg;
          MFC(LoggerMetrics::Instance().error_)->Inc();
          return Status(JavaExceptions::kIOException, msg);
        }

        // Use last_blk_from_cli to update last_bip_c.
        if (!(last_bip_c.gen_stamp() == last_blk_from_cli.gs &&
              last_bip_c.num_bytes() <= last_blk_from_cli.num_bytes)) {
          // The UT cannot cover the following code,
          // as it is impossible to execute under any circumstances.
          //
          //    last_bip_c.gen_stamp()
          // == last_blk_from_inode.gs
          // == last_blk_from_cli.gs
          //
          //    last_bip_c.num_bytes()
          // == last_blk_from_inode.num_bytes
          // <= last_blk_from_cli.num_bytes
          std::string msg = absl::StrFormat(
              "Try to commit block with a inconsistent input, "
              "last_bip_c: %s, last_blk_from_cli: {id:%d,gs:%d,num_bytes:%d}",
              ToJsonCompactString(last_bip_c),
              last_blk_from_cli.id,
              last_blk_from_cli.gs,
              last_blk_from_cli.num_bytes);
          LOG(ERROR) << msg;
          MFC(LoggerMetrics::Instance().error_)->Inc();
          return Status(JavaExceptions::kIOException, msg);
        }
        last_bip_c.set_num_bytes(last_blk_from_cli.num_bytes);

        if (NameSpace::IsAccMode()) {
          CheckAccINode(inode);
          CheckAccBlock(last_bip_c);

          last_bip_c.set_pufs_offset(pufs_offset);

          // Append object
          if (inode.ufs_file_info().create_type() ==
              UfsFileCreateType::kUfsFileCreateTypeAppend) {
            last_bip_c.set_key_block(true);
            last_bip_c.set_upload_type(cloudfs::datanode::APPEND);
            last_bip_c.set_pufs_name(inode.ufs_file_info().key());
          }

          // Normal object
          if (inode.ufs_file_info().create_type() ==
              UfsFileCreateType::kUfsFileCreateTypeNormal) {
            uint64_t total_size = last_bip_c.num_bytes();
            if (IsBlockIDValid(penult_blk_id)) {
              const BlockInfoProto& bundled_bip = penult_dbip->Get();
              if (bundled_bip.block_id() != kInvalidBlockID) {
                if (bundled_bip.key_block()) {
                  last_bip_c.set_part_num(bundled_bip.part_num() + 1);
                  last_bip_c.set_bundle_offset(bundled_bip.bundle_offset() +
                                               bundled_bip.bundle_length() +
                                               bundled_bip.num_bytes());
                  last_bip_c.set_bundle_length(0);
                } else {
                  last_bip_c.set_part_num(bundled_bip.part_num());
                  last_bip_c.set_bundle_offset(bundled_bip.bundle_offset());
                  last_bip_c.set_bundle_length(bundled_bip.bundle_length() +
                                               bundled_bip.num_bytes());

                  total_size +=
                      bundled_bip.bundle_length() + bundled_bip.num_bytes();
                }
              }
            }

            if ((total_size >= FLAGS_acc_mpu_part_threshold) ||
                (inode.status() == INode_Status_kFileComplete)) {
              last_bip_c.set_key_block(true);
              last_bip_c.set_upload_type(cloudfs::datanode::UPLOAD);
              last_bip_c.set_pufs_name(inode.ufs_file_info().key());
              last_bip_c.set_curr_upload_id(inode.ufs_file_info().upload_id());
              last_bip_c.clear_etag();
            }
          }

          if (force_set_last_blk_state.has_value() &&
              (force_set_last_blk_state.value() == BlockUCState::kCommitted ||
               force_set_last_blk_state.value() == BlockUCState::kComplete)) {
            last_bip_c.set_state(static_cast<BlockInfoProto::Status>(
                force_set_last_blk_state.value()));
          } else {
            if (ReplicaNumV2::CountReplica(datanode_manager_.get(), last_bip_c)
                    .healthy >= FLAGS_dfs_replication_min) {
              last_bip_c.set_state(BlockInfoProto::kComplete);
            } else {
              last_bip_c.set_state(BlockInfoProto::kCommitted);
            }
          }
        }

        if (NameSpace::IsHdfsMode()) {
          if (last_bip_c.has_write_mode() &&
              last_bip_c.write_mode() == cloudfs::IoMode::TOS_BLOCK) {
            last_bip_c.set_state(BlockInfoProto::kPersisted);
          } else {
            if (force_set_last_blk_state.has_value() &&
                (force_set_last_blk_state.value() == BlockUCState::kCommitted ||
                 force_set_last_blk_state.value() == BlockUCState::kComplete)) {
              last_bip_c.set_state(static_cast<BlockInfoProto::Status>(
                  force_set_last_blk_state.value()));
            } else {
              if (ReplicaNumV2::CountReplica(datanode_manager_.get(),
                                             last_bip_c)
                      .healthy >= FLAGS_dfs_replication_min) {
                last_bip_c.set_state(BlockInfoProto::kComplete);
              } else {
                last_bip_c.set_state(BlockInfoProto::kCommitted);
              }
            }
          }
        }
      } break;
      case BlockInfoProto::kUnderRecovery: {
        std::string msg = absl::StrFormat(
            "Client commit under recovery block: "
            "{id:%d,gs:%d,num_bytes:%d,recovery_gen_stamp:%d}",
            last_bip_c.block_id(),
            last_bip_c.gen_stamp(),
            last_bip_c.num_bytes(),
            last_bip_c.recovery_gen_stamp());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        // Don't let user commit this block, otherwise data corrupt may happens.
        // Because DNs is doing block recovery now,
        // gs and num_bytes of replicas may change.
        return Status(JavaExceptions::kIOException, msg);
      } break;
      case BlockInfoProto::kCommitted: {
        if (force_set_last_blk_state.has_value() &&
            (force_set_last_blk_state.value() == BlockUCState::kCommitted ||
             force_set_last_blk_state.value() == BlockUCState::kComplete)) {
          last_bip_c.set_state(static_cast<BlockInfoProto::Status>(
              force_set_last_blk_state.value()));
        } else if (ReplicaNumV2::CountReplica(datanode_manager_.get(),
                                              last_bip_c)
                       .healthy >= FLAGS_dfs_replication_min) {
          last_bip_c.set_state(BlockInfoProto::kComplete);
        }
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued:
      case BlockInfoProto::kPersisted: {
      } break;
      case BlockInfoProto::kDeprecated:
      case BlockInfoProto::kDeleted:
      default: {
        std::string msg = absl::StrFormat(
            "Try to commit last block B%d with inconsistent state %d",
            last_blk_id,
            static_cast<int>(last_bip_c.state()));
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      } break;
    }
  }
  if (complete_last_blk && last_bip_c.state() == BlockInfoProto::kCommitted) {
    // https://bytedance.feishu.cn/docx/doxcnoaLpB3XzKX3CVM1mr1XEBd
    // Consider the following scenario from BMQ:
    // 1. Client fails to write replicas to DNS.
    //    Replicas are RBW in DNs.
    // 2. Client calls complete to close file.
    //    NN commits the last block.
    //    But there is not enough replica to complete the last
    //    block. So NN won't finalize file, then returns false to
    //    client.
    // 3. Client calls recoverLease,
    //    NN returns "Failed to release lease" error.
    // FIX: Check if the last blk is ready to be completed before
    //      commit it.
    std::string msg = absl::StrFormat(
        "Try to complete last block without enough replica, "
        "blk_from_cli: {id:%d,gs:%d,num_bytes:%d}",
        last_blk_from_cli.id,
        last_blk_from_cli.gs,
        last_blk_from_cli.num_bytes);
    LOG(INFO) << msg;
    return Status(Code::kFalse, msg);
  }
  if (force_set_last_blk_state.has_value() &&
      !AreEqual(force_set_last_blk_state.value(), last_bip_c.state())) {
    std::string msg = absl::StrFormat(
        "State of last_bip_c is not equal to force_set_last_blk_state, "
        "last_bip_c:{id:%d,gs:%d,num_bytes:%d,state:%d}, "
        "force_set_last_blk_state:%d",
        last_bip_c.block_id(),
        last_bip_c.gen_stamp(),
        last_bip_c.num_bytes(),
        static_cast<int>(last_bip_c.state()),
        force_set_last_blk_state.value());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  if (IsBlockIDValid(penult_blk_id)) {
    CHECK(penult_bip_c.IsInitialized());
    penult_dbip->Get().Swap(&penult_bip_c);
    penult_dbip->IncVersion();
  }
  if (IsBlockIDValid(last_blk_id)) {
    CHECK(last_bip_c.IsInitialized());
    last_dbip->Get().Swap(&last_bip_c);
    last_dbip->IncVersion();
  }
  return Status();
}

Status BIPWriteManager::UpdatePipeline(
    const BIPLockComponents& blk_lck_comps,
    const cloudfs::ExtendedBlockProto& new_blk_from_cli,
    const google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>&
        new_nodes) {
  auto blk_id = new_blk_from_cli.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when UpdatePipeline", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (dbip->Get().state() != BlockInfoProto::kUnderConstruction) {
    std::string msg = absl::StrFormat(
        "Try to update pipeline not-under-construction block "
        "{id:%d,gs:%d,num_bytes:%d,state:%d}",
        dbip->Get().block_id(),
        dbip->Get().gen_stamp(),
        dbip->Get().num_bytes(),
        static_cast<int>(dbip->Get().state()));
    LOG(WARNING) << msg;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (new_blk_from_cli.generationstamp() <= dbip->Get().gen_stamp() ||
      new_blk_from_cli.numbytes() < dbip->Get().num_bytes()) {
    std::string msg = absl::StrFormat(
        "Update block {id:%d,gs:%d,num_bytes:%d} "
        "to {gs:%d,num_bytes:%d} is forbidden",
        blk_id,
        dbip->Get().gen_stamp(),
        dbip->Get().num_bytes(),
        new_blk_from_cli.generationstamp(),
        new_blk_from_cli.numbytes());
    LOG(WARNING) << msg;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
    // Keep compatible with BlockMapSlice::UpdatePipeline.
    return Status(JavaExceptions::kIOException, msg);
  }

  dbip->Get().set_gen_stamp(new_blk_from_cli.generationstamp());
  dbip->Get().set_num_bytes(new_blk_from_cli.numbytes());
  auto report_ts = TimeUtilV2::GetNowEpochMs() / 1000;
  for (const auto& new_node : new_nodes) {
    const std::string& dn_uuid = new_node.datanodeuuid();
    if (dn_uuid.empty()) {
      LOG(WARNING) << "Found empty dn uuid in new_nodes, blk_id: " << blk_id;
      MFC(LoggerMetrics::Instance().warn_)->Inc();
      continue;
    }
    ReplicaInfoProto& replica = GetOrCreateReplica(&dbip->Get(), dn_uuid);
    replica.set_report_ts(report_ts);
    // The client calls AsyncUpdatePipeline after calling datanodes'
    // createBlockOutputStream, so replica infos are reliable.
    // https://code.byted.org/inf/native_dfs_client/blob/a6614ca7ef8eb45751b8cdc18aa27be326be565c/src/client/OutputDataStreamer.cpp#L478
    replica.set_reporter(ReplicaInfoProto::kClient);
    replica.set_state(cloudfs::ReplicaStateProto::RBW);
    replica.set_gen_stamp(new_blk_from_cli.generationstamp());
    replica.set_num_bytes(new_blk_from_cli.numbytes());
    replica.set_dn_uuid(dn_uuid);
  }
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::AbandonBlock(const BIPLockComponents& blk_lck_comps,
                                     BlockID blk_id) {
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    // It is tricky to return kIOException instead of kIsRetry.
    // But we have to do this tricky job.
    // See DepringBlockRecycler::Recycle for more infos.
    // return Status(Code::kIsRetry);
    return Status(JavaExceptions::kIOException);
  }
  if (dbip->Get().state() == BlockInfoProto::kDeprecated ||
      dbip->Get().state() == BlockInfoProto::kDeleted) {
    std::string msg =
        absl::StrFormat("Try to abandon already deprecated B%d", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(Code::kIsRetry);
  }

  dbip->Get().set_state(BlockInfoProto::kDeprecated);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::Fsync(const BIPLockComponents& blk_lck_comps,
                              const cloudfs::BlockProto& bp,
                              uint64_t new_num_bytes_from_cli) {
  BlockID blk_id = bp.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg = absl::StrFormat("Found missing B%d when Fsync", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  // NOTICE: WriteLockTree promises client won't call fsync and
  //         addBlock/closeFile concurrently.
  // We should not let cli update len if block state is kUnderRecovery.
  // When block recovery happens, cli doesn't know len of this block.
  // Only datanodes know len of this block.
  if (dbip->Get().state() != BlockInfoProto::kUnderConstruction) {
    return Status(
        JavaExceptions::kIOException,
        absl::StrFormat("Update block length on a not-under-construction block "
                        "{id:%d,gs:%d,num_bytes:%d,state:%d}",
                        blk_id,
                        dbip->Get().gen_stamp(),
                        dbip->Get().num_bytes(),
                        static_cast<int>(dbip->Get().state())));
  }
  if (dbip->Get().gen_stamp() > bp.genstamp()) {
    std::string msg = absl::StrFormat(
        "Try to fsync a inconsistent block "
        "{id:%d,gs:%d,num_bytes:%d} with gs %d",
        dbip->Get().block_id(),
        dbip->Get().gen_stamp(),
        dbip->Get().num_bytes(),
        bp.genstamp());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  dbip->Get().set_gen_stamp(bp.genstamp());
  dbip->Get().set_num_bytes(new_num_bytes_from_cli);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::ConcatUpdateBlockINodeId(
    const BIPLockComponents& blk_lck_comps,
    const cloudfs::BlockProto& bp,
    const INode& target_inode,
    uint64_t pufs_offset,
    BlockInfoProto* last_bip,
    bool is_last) {
  BlockID blk_id = bp.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg = absl::StrFormat("Found missing B%d when Fsync", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  // NOTICE: WriteLockTree promises client won't call fsync and
  //         addBlock/closeFile concurrently.
  // We should not let cli update len if block state is kUnderRecovery.
  // When block recovery happens, cli doesn't know len of this block.
  // Only datanodes know len of this block.
  if (dbip->Get().state() == BlockInfoProto::kUploadIssued ||
      dbip->Get().state() == BlockInfoProto::kPersisted ||
      dbip->Get().state() == BlockInfoProto::kDeprecated ||
      dbip->Get().state() == BlockInfoProto::kDeleted) {
    return Status(
        JavaExceptions::Exception::kIOException,
        absl::StrFormat("block is uploading/uploaded to UFS, not allowed to "
                        "concat, block_id=%ld, bip=%s",
                        bp.blockid(),
                        dbip->Get().ShortDebugString()));
  }
  if (dbip->Get().block_id() != bp.blockid() ||
      dbip->Get().gen_stamp() != bp.genstamp() ||
      dbip->Get().num_bytes() != bp.numbytes()) {
    std::string msg = absl::StrFormat(
        "Try to fsync a inconsistent block "
        "{id:%d,gs:%d,num_bytes:%d} with gs %d",
        dbip->Get().block_id(),
        dbip->Get().gen_stamp(),
        dbip->Get().num_bytes(),
        bp.genstamp());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  dbip->Get().set_inode_id(target_inode.id());
  if (NameSpace::IsAccMode()) {
    dbip->Get().set_pufs_name(target_inode.ufs_file_info().key());
    dbip->Get().set_pufs_offset(pufs_offset);

    NameSpace::UpdateBipInfoForBundleUpload(
        target_inode, &dbip->Get(), last_bip, is_last);
  }

  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::CommitBlockSynchronization(
    const BIPLockComponents& blk_lck_comps,
    const cloudfs::datanode::CommitBlockSynchronizationRequestProto& request,
    absl::optional<BlockUCState> force_set_blk_state) {
  BlockID blk_id = request.block().blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  if (!GetOrLoadDirtyBlockInfoProtoIfMissing(
           blk_lck_comps, blk_id, false, &dbip)
           .IsOK()) {
    // Retry.
    if (request.deleteblock()) {
      if (force_set_blk_state.has_value() &&
          !AreEqual(force_set_blk_state.value(), BlockInfoProto::kDeprecated)) {
        std::string msg = absl::StrFormat(
            "request.deleteblock() is true "
            "but force_set_blk_state.value() is %d",
            force_set_blk_state.value());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      }
      return Status(Code::kIsRetry);
    } else {
      std::string msg = absl::StrFormat(
          "Found missing B%d when CommitBlockSynchronization", blk_id);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
  }

  BlockInfoProto last_bip_c = dbip->Get();
  if (last_bip_c.recovery_gen_stamp() != request.newgenstamp()) {
    return Status(JavaExceptions::kIOException,
                  absl::StrFormat("The recovery id %d does not match current "
                                  "recovery id %d for block B%d",
                                  request.newgenstamp(),
                                  last_bip_c.recovery_gen_stamp(),
                                  blk_id));
  }

  last_bip_c.set_gen_stamp(request.newgenstamp());
  last_bip_c.set_num_bytes(request.newlength());
  if (request.deleteblock()) {
    if (last_bip_c.state() == BlockInfoProto::kDeprecated) {
      return Status(Code::kIsRetry);
    }
    last_bip_c.set_state(BlockInfoProto::kDeprecated);
  } else {
    uint32_t report_ts = TimeUtilV2::GetNowEpochMs() / 1000;
    for (const auto& new_target : request.newtaragets()) {
      ReplicaInfoProto& replica =
          GetOrCreateReplica(&last_bip_c, new_target.datanodeuuid());
      replica.Clear();
      replica.set_report_ts(report_ts);
      replica.set_reporter(ReplicaInfoProto::kDatanode);
      replica.set_state(cloudfs::ReplicaStateProto::FINALIZED);
      replica.set_gen_stamp(request.newgenstamp());
      replica.set_num_bytes(request.newlength());
      replica.set_dn_uuid(new_target.datanodeuuid());
    }
    if (request.closefile()) {
      if (last_bip_c.has_write_mode() &&
          last_bip_c.write_mode() == cloudfs::IoMode::TOS_BLOCK) {
        last_bip_c.set_state(BlockInfoProto::kPersisted);
      } else {
        if (force_set_blk_state.has_value() &&
            (force_set_blk_state.value() == BlockUCState::kCommitted ||
             force_set_blk_state.value() == BlockUCState::kComplete)) {
          last_bip_c.set_state(
              static_cast<BlockInfoProto::Status>(force_set_blk_state.value()));
        } else {
          if (ReplicaNumV2::CountReplica(datanode_manager_.get(), last_bip_c)
                  .healthy >= FLAGS_dfs_replication_min) {
            last_bip_c.set_state(BlockInfoProto::kComplete);
          } else {
            last_bip_c.set_state(BlockInfoProto::kCommitted);
          }
        }
      }
    } else {
      last_bip_c.set_state(BlockInfoProto::kUnderConstruction);
    }
  }

  if (force_set_blk_state.has_value()) {
    if (!AreEqual(force_set_blk_state.value(), last_bip_c.state())) {
      std::string msg = absl::StrFormat(
          "State of last_bip_c is not equal to force_set_last_blk_state, "
          "last_bip_c:{id:%d,gs:%d,num_bytes:%d,state:%d}, "
          "force_set_last_blk_state:%d",
          last_bip_c.block_id(),
          last_bip_c.gen_stamp(),
          last_bip_c.num_bytes(),
          static_cast<int>(last_bip_c.state()),
          force_set_blk_state.value());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
  }

  dbip->Get().Swap(&last_bip_c);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::CommitSealedBlock(
    const BIPLockComponents& blk_lck_comps,
    const BlockProto& bp,
    uint32_t commit_length) {
  BlockID blk_id = bp.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when CommitSealedBlock", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  if (dbip->Get().state() != BlockInfoProto::kUnderConstruction &&
      dbip->Get().state() != BlockInfoProto::kUnderRecovery) {
    return Status(
        JavaExceptions::kIOException,
                  absl::StrFormat("Update block length on not a "
                                  "under-construction or under-recovery block "
                                  "{id:%d,gs:%d,num_bytes:%d,state:%d}",
                        blk_id,
                        dbip->Get().gen_stamp(),
                        dbip->Get().num_bytes(),
                        static_cast<int>(dbip->Get().state())));
  }
  if (dbip->Get().gen_stamp() != kBlockProtocolV2GenerationStamp ||
      bp.genstamp() != kBlockProtocolV2GenerationStamp) {
    std::string msg = absl::StrFormat(
        "Try to CommitSealedBlock in a block not in V2 Protocol"
        "{id:%d,gs:%d,num_bytes:%d} with gs %d",
        dbip->Get().block_id(),
        dbip->Get().gen_stamp(),
        dbip->Get().num_bytes(),
        bp.genstamp());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  dbip->Get().set_gen_stamp(bp.genstamp());
  dbip->Get().set_num_bytes(commit_length);
  dbip->Get().set_state(BlockInfoProto::kCommitted);
  dbip->Get().set_need_align(true);

  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::SetReplication(const BIPLockComponents& blk_lck_comps,
                                       const BlockProto& bp,
                                       uint32_t replica_num) {
  BlockID blk_id = bp.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when SetReplication", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  dbip->Get().set_expected_rep(replica_num);
  dbip->IncVersion();
  return Status();
}

// Inspired by BlockManager::NeedRelease.
Status BIPWriteManager::ReleaseLease(const BIPLockComponents& blk_lck_comps,
                                     const cloudfs::BlockProto& penult_bp,
                                     const cloudfs::BlockProto& last_bp) {
  BlockID penult_blk_id = penult_bp.blockid();
  BlockID last_blk_id = last_bp.blockid();
  const DirtyBlockInfoProto* penult_dbip = nullptr;
  DirtyBlockInfoProto* last_dbip = nullptr;
  Status s;
  if (IsBlockIDValid(penult_blk_id)) {
    DirtyBlockInfoProto* t = nullptr;
    s = GetOrLoadDirtyBlockInfoProtoIfMissing(
        blk_lck_comps, penult_blk_id, false, &t);
    if (!s.IsOK()) {
      std::string msg = absl::StrFormat(
          "Found missing penult B%d when ReleaseLease", penult_blk_id);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    penult_dbip = t;
  }
  s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, last_blk_id, false, &last_dbip);
  if (!s.IsOK()) {
    std::string msg = absl::StrFormat(
        "Found missing last B%d when ReleaseLease", last_blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  CHECK_EQ(IsBlockIDValid(penult_blk_id), penult_dbip != nullptr);
  CHECK_EQ(IsBlockIDValid(last_blk_id), last_dbip != nullptr);

  BlockInfoProto penult_bip_c;
  bool penult_blk_min_repl = true;
  if (penult_dbip) {
    penult_bip_c = penult_dbip->Get();
    switch (penult_bip_c.state()) {
      case BlockInfoProto::kCommitted: {
        penult_blk_min_repl =
            ReplicaNumV2::CountReplica(datanode_manager_.get(), penult_bip_c)
                .healthy >= FLAGS_dfs_replication_min;
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued:
      case BlockInfoProto::kPersisted: {
        // Do nothing and keep penult_blk_min_repl true.
      } break;
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery:
      case BlockInfoProto::kDeprecated:
      case BlockInfoProto::kDeleted:
      default: {
        std::string msg = absl::StrFormat(
            "Try to release lease on illegal penultimate block "
            "{id:%d,gs:%d,num_bytes:%d,state:%d}",
            penult_blk_id,
            penult_bip_c.gen_stamp(),
            penult_bip_c.num_bytes(),
            static_cast<int>(penult_bip_c.state()));
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      } break;
    }
  }

  BlockInfoProto last_bip_c;
  bool last_blk_min_repl = true;
  if (last_dbip) {
    last_bip_c = last_dbip->Get();
    switch (last_bip_c.state()) {
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery: {
        last_blk_min_repl = false;
      } break;
      case BlockInfoProto::kCommitted: {
        last_blk_min_repl =
            ReplicaNumV2::CountReplica(datanode_manager_.get(), last_bip_c)
                .healthy >= FLAGS_dfs_replication_min;
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued:
      case BlockInfoProto::kPersisted: {
        // Do nothing and keep last_blk_min_repl true.
      } break;
      case BlockInfoProto::kDeprecated:
      case BlockInfoProto::kDeleted:
      default: {
        std::string msg =
            absl::StrFormat("The unexpected state of last block B%d is %d",
                            last_blk_id,
                            static_cast<int>(last_bip_c.state()));
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      } break;
    }
  }

  // Reach here if inode.blocks_size() == 0, etc.
  if (penult_blk_min_repl && last_blk_min_repl) {
    // TODO: How do caller know if it need to call
    // BIPWriteManager::PreCommit?
    return Status(Code::kFalse, "The last two blocks are both min-repl");
  } else {
    // Reach here => inode.blocks_size() > 0
    //            => last_dbip != nullptr
    //            => last_bip_c.IsInitialized()
    CHECK(last_bip_c.IsInitialized()) << last_bip_c.InitializationErrorString();
    switch (last_bip_c.state()) {
      case BlockInfoProto::kUnderConstruction:
      case BlockInfoProto::kUnderRecovery: {
        // There is no datanode reported to this block.
        // maybe client have crashed before writing data to pipeline.
        // This blocks doesn't need any recovery.
        // We can remove this block and close the file.
        if (last_bip_c.num_bytes() == 0 &&
            last_bip_c.write_mode() == cloudfs::IoMode::DATANODE_BLOCK &&
            // std::any_of returns false if the range is empty.
            !std::any_of(
                last_bip_c.replicas().begin(),
                last_bip_c.replicas().end(),
                [&last_bip_c](const ReplicaInfoProto& replica) {
                  return (replica.reporter() != ReplicaInfoProto::kNamenode) ||
                         (replica.gen_stamp() != last_bip_c.gen_stamp()) ||
                         (replica.num_bytes() != 0);
                })) {
          last_bip_c.set_state(BlockInfoProto::kDeprecated);
          last_dbip->Get().Swap(&last_bip_c);
          last_dbip->IncVersion();
          return Status(Code::kAbandonLastBlock);
        } else {
          return Status();
        }
      } break;
      case BlockInfoProto::kCommitted: {
        // We checked that !penult_blk_min_repl || !last_blk_min_repl.
        std::string msg = absl::StrFormat(
            "Failed to release lease. "
            "Committed last block {id:%d,gs:%d,num_bytes:%d} "
            "are waiting to be minimally replicated. "
            "Please try again later.",
            last_bip_c.block_id(),
            last_bip_c.gen_stamp(),
            last_bip_c.num_bytes());
        LOG(WARNING) << msg;
        MFC(LoggerMetrics::Instance().warn_)->Inc();
        return Status(JavaExceptions::kAlreadyBeingCreatedException, msg);
      } break;
      case BlockInfoProto::kComplete:
      case BlockInfoProto::kUploadIssued:
      case BlockInfoProto::kPersisted: {
        CHECK(!penult_blk_min_repl) << ToJsonCompactString(penult_bip_c) << ", "
                                    << ToJsonCompactString(last_bip_c);
        std::string msg = absl::StrFormat(
            "Failed to release lease. "
            "Committed penultimate block {id:%d,gs:%d,num_bytes:%d} "
            "are waiting to be minimally replicated. "
            "Please try again later.",
            penult_bip_c.block_id(),
            penult_bip_c.gen_stamp(),
            penult_bip_c.num_bytes());
        LOG(WARNING) << msg;
        MFC(LoggerMetrics::Instance().warn_)->Inc();
        return Status(JavaExceptions::kAlreadyBeingCreatedException, msg);
      } break;
      case BlockInfoProto::kDeprecated:
      case BlockInfoProto::kDeleted:
      default: {
        std::string msg =
            absl::StrFormat("The unexpected state of last block B%d is %d",
                            last_blk_id,
                            static_cast<int>(last_bip_c.state()));
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      } break;
    }
  }
  LOG(FATAL) << "unreachable";
  return Status(JavaExceptions::kIOException, "unreachable");
}

Status BIPWriteManager::InitRecover(
    const BIPLockComponents& blk_lck_comps,
    const BlockProto& bp,
    uint64_t recovery_gen_stamp,
    absl::optional<std::string> force_set_primary_dn_uuid,
    std::string* primary_dn_uuid,
    cloudfs::RecoveringBlockProto* recovering_block) {
  std::string primary_dn_uuid_holder;
  if (primary_dn_uuid == nullptr) {
    primary_dn_uuid = &primary_dn_uuid_holder;
  }

  BlockID blk_id = bp.blockid();
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when InitRecover", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  BlockInfoProto bip_c = dbip->Get();
  auto state = bip_c.state();
  switch (bip_c.state()) {
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery: {
    } break;
    case BlockInfoProto::kCommitted:
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued:
    case BlockInfoProto::kPersisted:
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted:
    default: {
      std::string msg = absl::StrFormat(
          "Try to recover not-uc/ur block {id:%d,gs:%d,num_bytes:%d,state:%d}",
          bip_c.block_id(),
          bip_c.gen_stamp(),
          bip_c.num_bytes(),
          static_cast<int>(bip_c.state()));
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    } break;
  }

  if (bip_c.has_write_mode() &&
      bip_c.write_mode() == cloudfs::IoMode::TOS_BLOCK) {
    bip_c.set_state(BlockInfoProto::kUnderRecovery);
    // NOTICE: Do not update gs. Use old gs instead.
    bip_c.set_recovery_gen_stamp(bip_c.gen_stamp());
  } else {
    if (force_set_primary_dn_uuid.has_value()) {
      *primary_dn_uuid = force_set_primary_dn_uuid.value();
    } else {
      if (!ChoosePrimaryDn4BlockRecovery(&bip_c, primary_dn_uuid)) {
        std::string msg = absl::StrFormat(
            "No primary datanode for block recovery for B%d", blk_id);
        LOG(WARNING) << msg;
        MFC(LoggerMetrics::Instance().warn_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      }
    }

    bip_c.set_state(BlockInfoProto::kUnderRecovery);
    if (!(bip_c.gen_stamp() < recovery_gen_stamp &&
          bip_c.recovery_gen_stamp() < recovery_gen_stamp)) {
      std::string msg = absl::StrFormat(
          "Try to recover block {id:%d,gen_stamp:%d,recovery_gen_stamp:%d} "
          "with a smaller recovery_gen_stamp %d",
          blk_id,
          bip_c.gen_stamp(),
          bip_c.recovery_gen_stamp(),
          recovery_gen_stamp);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    }
    bip_c.set_recovery_gen_stamp(recovery_gen_stamp);
    bip_c.set_primary_dn_uuid_4_block_recovery(*primary_dn_uuid);

    if (recovering_block) {
      recovering_block->set_newgenstamp(recovery_gen_stamp);
      auto located = recovering_block->mutable_block();
      // Set recovering_block::block::b whose type is ExtendedBlockProto.
      auto b = located->mutable_b();
      b->set_poolid(blockpool_id_);
      b->set_blockid(blk_id);
      b->set_generationstamp(bip_c.gen_stamp());
      b->set_numbytes(bip_c.num_bytes());
      b->set_blockpufsname(bip_c.pufs_name());
      located->set_offset(-1);
      located->set_corrupt(false);
      // Set recovering_block::block::blockToken.
      auto token_proto = located->mutable_blocktoken();
      token_proto->set_identifier("");
      token_proto->set_password("");
      token_proto->set_kind("");
      token_proto->set_service("");
      // Set recovering_block::block::locs whose type is DatanodeInfoProto,
      //     recovering_block::block::storageTypes whose type is
      //     StorageTypeProto, recovering_block::block::storageIDs whose type is
      //     string.
      std::unordered_set<DatanodeInfoPtr> containing_dns;
      // How does datanode do block recovery?
      // Datanode will filter replicas by gs and len,
      // so namenode can skip this step.
      // https://code.byted.org/storage/bytestore/blob/1f5c7c67067e40c4b56ec31713ebb4ffa7267850/bytestore/chunkserver/hdfs/block_recovery.cc#L148
      // Or namenode can execute this step itself.
      for (const ReplicaInfoProto& replica : bip_c.replicas()) {
        if (replica.gen_stamp() == bip_c.gen_stamp()) {
          DatanodeInfoPtr dn =
              datanode_manager_->GetDatanodeFromUuid(replica.dn_uuid());
          if (dn != nullptr) {
            containing_dns.emplace(dn);
          }
        }
      }
      std::unordered_set<DatanodeInfoPtr> no_stale_dns;
      for (DatanodeInfoPtr dn : containing_dns) {
        if (!dn->IsStale()) {
          no_stale_dns.emplace(dn);
        }
      }
      auto recover_locs =
          !no_stale_dns.empty() ? &no_stale_dns : &containing_dns;
      if (!recover_locs->empty()) {
        std::vector<DatanodeID> dn_ids;
        for (DatanodeInfoPtr dn : *recover_locs) {
          dn_ids.emplace_back(dn->id());
        }
        // Currently, we do not support storage policy id.
        // Add it when we need it.
        StoragePolicyId storage_policy_id = kBlockStoragePolicyIdUnspecified;
        datanode_manager_->ConstructLocatedBlock(
            dn_ids, storage_policy_id, located);
      } else {
        LOG(INFO) << "There is no locs for block recovery, B" << blk_id;
      }
    }
  }

  dbip->Get().Swap(&bip_c);
  dbip->IncVersion();
  return Status();
}

bool BIPWriteManager::ChoosePrimaryDn4BlockRecovery(
    BlockInfoProto* bip,
    std::string* primary_dn_uuid) {
  CHECK_NOTNULL(bip);
  CHECK_NOTNULL(primary_dn_uuid);
  std::vector<ReplicaInfoProto*> healthy_replicas;
  std::unordered_set<DatanodeInfoPtr> healthy_dns;
  if (ReplicaNumV2::CountReplica(datanode_manager_.get(),
                                 bip,
                                 nullptr,
                                 nullptr,
                                 &healthy_replicas,
                                 &healthy_dns)
          .healthy == 0) {
    return false;
  }

  bool all_dns_tried_as_primary = true;
  for (ReplicaInfoProto* replica : healthy_replicas) {
    all_dns_tried_as_primary &= replica->tried_as_primary_4_block_recovery();
  }
  if (all_dns_tried_as_primary) {
    for (ReplicaInfoProto* replica : healthy_replicas) {
      replica->set_tried_as_primary_4_block_recovery(false);
    }
  }

  primary_dn_uuid->clear();
  // Refer to DatanodeManager::ChooseBlockRecoverPrimary.
  uint64_t most_recent_update_time =
      TimeUtilV2::GetNowEpochMs() -
      FLAGS_datanode_keep_alive_timeout_sec * 1000 * 2;
  for (ReplicaInfoProto* replica : healthy_replicas) {
    if (replica->tried_as_primary_4_block_recovery()) {
      continue;
    }
    DatanodeInfoPtr dn =
        datanode_manager_->GetDatanodeFromUuid(replica->dn_uuid());
    if (dn == nullptr) {
      continue;
    }
    if (!dn->IsAlive() && !dn->IsDecommissionInProgress()) {
      continue;
    }
    uint64_t last_heartbeat =
        std::chrono::time_point_cast<std::chrono::milliseconds>(
            dn->last_heartbeat())
            .time_since_epoch()
            .count();
    if (last_heartbeat > most_recent_update_time) {
      *primary_dn_uuid = dn->uuid();
      most_recent_update_time = last_heartbeat;
    }
  }

  if (primary_dn_uuid->empty()) {
    return false;
  }
  ReplicaInfoProto* replica = nullptr;
  for (auto& r : *bip->mutable_replicas()) {
    if (r.dn_uuid() == *primary_dn_uuid) {
      replica = &r;
      break;
    }
  }
  CHECK_NOTNULL(replica);
  replica->set_tried_as_primary_4_block_recovery(true);
  return true;
}

Status BIPWriteManager::UploadBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  CHECK_NOTNULL(upload_cmd);
  CHECK_NOTNULL(ne_cmd);
  if (NameSpace::IsHdfsMode()) {
    return UploadHdfsBlock(blk_lck_comps,
                           blk_from_dn,
                           dn_uuid,
                           pufs_name,
                           upload_id,
                           upload_cmd,
                           ne_cmd);
  }
  if (NameSpace::IsAccMode()) {
    return UploadAccBlock(blk_lck_comps,
                          blk_from_dn,
                          dn_uuid,
                          pufs_name,
                          upload_id,
                          upload_cmd,
                          ne_cmd);
  }
  LOG(FATAL) << "unreachable";
  return Status::OK();
}

Status BIPWriteManager::UploadHdfsBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  if (dn_uuid.empty() || pufs_name.empty()) {
    return Status(JavaExceptions::kIOException, "dn_uuid/pufs_name is empty");
  }

  BlockID blk_id = blk_from_dn.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when UploadBlock", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  s = dbip->IsEqualTo(blk_from_dn, dn_uuid);
  if (!s.IsOK()) {
    return s;
  }

  uint64_t current_sec = TimeUtilV2::GetNowEpochMs() / 1000;
  switch (dbip->Get().state()) {
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued: {
      // Approve of dn's uploading if:
      if (dbip->Get().nn_exp_ts() < current_sec) {
        dbip->Get().set_state(BlockInfoProto::kUploadIssued);
        dbip->Get().set_pufs_name(pufs_name);
        auto times = dbip->Get().upload_issued_times() + 1;
        dbip->Get().set_upload_issued_times(times);
        if (dbip->Get().has_curr_upload_id() &&
            dbip->Get().curr_upload_id() != upload_id) {
          *dbip->Get().add_aborted_upload_ids() = dbip->Get().curr_upload_id();
        }
        constexpr int aborted_upload_ids_max_size = 1024;
        if (dbip->Get().aborted_upload_ids_size() >
            aborted_upload_ids_max_size) {
          dbip->Get().mutable_aborted_upload_ids()->DeleteSubrange(
              0,
              dbip->Get().aborted_upload_ids_size() -
                  aborted_upload_ids_max_size);
        }
        dbip->Get().set_curr_upload_id(upload_id);
        dbip->Get().set_dn_uuid(dn_uuid);
        int32_t interval = FLAGS_min_upload_timeout_s * times;
        if (interval > FLAGS_max_upload_timeout_s) {
          interval = FLAGS_max_upload_timeout_s;
        }
        dbip->Get().set_dn_exp_ts(current_sec + interval);
        dbip->Get().set_nn_exp_ts(dbip->Get().dn_exp_ts() +
                                  FLAGS_nn_dn_clock_drift_s);
        dbip->IncVersion();
        dbip->GetUploadCommand(blockpool_id_, upload_cmd);
        return Status();
      } else if ((dbip->Get().pufs_name() == pufs_name &&
                  dbip->Get().curr_upload_id() == upload_id &&
                  dbip->Get().dn_uuid() == dn_uuid)) {
        if (dbip->IsFlushed()) {
          dbip->GetUploadCommand(blockpool_id_, upload_cmd);
        }
        return Status(Code::kIsRetry);
      } else {
        std::string msg = absl::StrFormat(
            "Do not approve upload request from DN{uuid:%s} due to "
            "B%d is being uploaded by DN{uuid:%s}",
            dn_uuid,
            blk_id,
            dbip->Get().dn_uuid());
        MFC(BMetrics::Instance().upload_is_denied_num)->Inc();
        LOG(INFO) << msg;
        return Status(JavaExceptions::kIOException, msg);
      }
    } break;
    case BlockInfoProto::kPersisted:
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted: {
      if (dbip->IsFlushed()) {
        // TODO: Improve performance.
        // Maybe the state of BlockInfoProto on DB is already persisted
        // but IsFlushed() returns false.
        dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      }
      return Status(Code::kIsRetry);
    } break;
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery:
    case BlockInfoProto::kCommitted:
    default: {
      std::string msg =
          absl::StrFormat("Try to upload B%d with inconsistent state %d",
                          blk_id,
                          static_cast<int>(dbip->Get().state()));
      LOG(INFO) << msg;
      return Status(JavaExceptions::kIOException, msg);
    } break;
  }
  LOG(FATAL) << "unreachable";
  return Status(JavaExceptions::kIOException, "unreachable");
}

Status BIPWriteManager::UploadAccBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  if (dn_uuid.empty()) {
    return Status(JavaExceptions::kIOException, "dn_uuid is empty");
  }

  BlockID blk_id = blk_from_dn.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when UploadBlock", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  s = dbip->IsEqualTo(blk_from_dn, dn_uuid);
  if (!s.IsOK()) {
    return s;
  }

  enum {
    kUpload,
    kUploadRetry,
    kDeny,
    kEvict,
    kEvictBundle,
  };

  int action = kDeny;
  std::string msg;
  uint64_t current_sec = TimeUtilV2::GetNowEpochMs() / 1000;
  BlockInfoProto& bip = dbip->Get();
  CheckAccBlock(bip);
  bool is_append_block = bip.upload_type() == cloudfs::datanode::APPEND;

  switch (bip.state()) {
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued: {
      if (is_append_block) {
        // Append block
        if (bip.pufs_offset() > 0) {
          BlockInfoProto prev_bip;
          int64_t prev_bid = kInvalidBlockID;
          INode inode;
          if (meta_storage_->GetINode(bip.inode_id(), &inode) != kOK) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "INode{id:%d} is not found for B%d",
                dn_uuid,
                bip.inode_id(),
                blk_id);
            break;
          }
          for (int i = 0; i + 1 < inode.blocks_size(); i++) {
            if (inode.blocks(i + 1).blockid() == bip.block_id()) {
              prev_bid = inode.blocks(i).blockid();
              break;
            }
          }
          if (prev_bid == kInvalidBlockID) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "previous block is not found for B%d",
                dn_uuid,
                blk_id);
            break;
          }
          if (!meta_storage_->GetBlockInfo(prev_bid, &prev_bip)) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "previous bip is not found for B%d",
                dn_uuid,
                blk_id);
            break;
          }
          if (prev_bip.state() != BlockInfoProto::kPersisted) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "previous bip{B%d} is not persisted for B%d",
                dn_uuid,
                prev_bid,
                blk_id);
            break;
          }
        }
      } else {
        // Non-append block
        if (bip.key_block()) {
          if (bip.curr_upload_id().empty()) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "B%d does not have upload id",
                dn_uuid,
                blk_id);
            break;
          }
        } else {
          INode inode;
          if (meta_storage_->GetINode(bip.inode_id(), &inode) != kOK) {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "INode{id:%d} is not found for B%d",
                dn_uuid,
                bip.inode_id(),
                blk_id);
            break;
          }
          if (inode.ufs_file_info().file_state() == kUfsFileStatePersisted) {
            action = kEvictBundle;
            msg = absl::StrFormat(
                "Issue evict cmd to DN{uuid:%s} for persisted INode{id:%d} for "
                "B%d",
                dn_uuid,
                bip.inode_id(),
                blk_id);
            break;
          } else {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve evict request from DN{uuid:%s} due to "
                "INode{id:%d} is not persisted for B%d",
                dn_uuid,
                bip.inode_id(),
                blk_id);
            break;
          }
        }
      }

      // All type-related checks passed, common upload check
      if (current_sec > bip.nn_exp_ts()) {
        action = kUpload;
        msg = absl::StrFormat(
            "Approve upload request from DN{uuid:%s} for B%d", dn_uuid, blk_id);
        break;
      } else if (dn_uuid == bip.dn_uuid()) {
        msg = absl::StrFormat(
            "Approve upload retry request from DN{uuid:%s} for B%d",
            dn_uuid,
            blk_id);
        action = kUploadRetry;
        break;
      } else {
        action = kDeny;
        msg = absl::StrFormat(
            "Do not approve upload request from DN{uuid:%s} due to "
            "B%d is being uploaded by DN{uuid:%s}",
            dn_uuid,
            blk_id,
            dbip->Get().dn_uuid());
        break;
      }
    } break;
    case BlockInfoProto::kPersisted: {
      if (is_append_block) {
        action = kEvict;
        msg = absl::StrFormat(
            "Issue evict instead of approve to DN{uuid:%s} since B%d is "
            "persisted",
            dn_uuid,
            blk_id);
        break;
      } else {
        INode inode;
        if (meta_storage_->GetINode(bip.inode_id(), &inode) != kOK) {
          action = kDeny;
          msg = absl::StrFormat(
              "Do not issue evict request from DN{uuid:%s} due to "
              "INode{id:%d} is not found for B%d",
              dn_uuid,
              blk_id,
              bip.inode_id());
          break;
        }
        if (inode.ufs_file_info().file_state() == kUfsFileStatePersisted) {
          action = kEvict;
          msg = absl::StrFormat(
              "Issue evict instead of approve to DN{uuid:%s} since "
              "INode{id:%d} is persisted for B%d",
              dn_uuid,
              inode.id(),
              blk_id);
          break;
        }

        if (!bip.has_etag() || bip.etag().empty()) {
          if (current_sec > bip.nn_exp_ts()) {
            action = kUpload;
            msg = absl::StrFormat(
                "Approve upload request from DN{uuid:%s} for B%d since etag is "
                "empty",
                dn_uuid,
                blk_id);
            break;
          } else if (dn_uuid == bip.dn_uuid()) {
            action = kUploadRetry;
            msg = absl::StrFormat(
                "Approve upload retry request from DN{uuid:%s} for B%d since "
                "etag is empty",
                dn_uuid,
                blk_id);
            break;
          } else {
            action = kDeny;
            msg = absl::StrFormat(
                "Do not approve upload request from DN{uuid:%s} due to "
                "B%d is being uploaded by DN{uuid:%s}",
                dn_uuid,
                blk_id,
                dbip->Get().dn_uuid());
            break;
          }
        }

        action = kDeny;
        break;
      }
    } break;
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted: {
      action = kEvict;
      msg = absl::StrFormat("Remove deprecated or deleted B%d", blk_id);
      break;
    } break;
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery:
    case BlockInfoProto::kCommitted:
    default: {
      action = kDeny;
      msg = absl::StrFormat("Try to upload B%d with inconsistent state %d",
                            blk_id,
                            static_cast<int>(dbip->Get().state()));
    } break;
  }

  LOG(INFO) << "Compute action finished, result: " << action
            << ", msg: " << msg;

  switch (action) {
    case kUpload: {
      // Do not change state in case of rename after persisted
      if (bip.state() != BlockInfoProto::kPersisted) {
        bip.set_state(BlockInfoProto::kUploadIssued);
      }
      auto times = bip.upload_issued_times() + 1;
      bip.set_upload_issued_times(times);
      bip.set_dn_uuid(dn_uuid);
      int32_t interval = FLAGS_min_upload_timeout_s * times;
      if (interval > FLAGS_max_upload_timeout_s) {
        interval = FLAGS_max_upload_timeout_s;
      }
      bip.set_dn_exp_ts(current_sec + interval);
      bip.set_nn_exp_ts(current_sec + interval + FLAGS_nn_dn_clock_drift_s);
      dbip->IncVersion();
      dbip->GetUploadCommand(blockpool_id_, upload_cmd);
      return Status::OK();
    } break;
    case kUploadRetry: {
      if (dbip->IsFlushed()) {
        dbip->GetUploadCommand(blockpool_id_, upload_cmd);
      }
      return Status(Code::kIsRetry);
    } break;
    case kEvict: {
      if (dbip->IsFlushed()) {
        dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      }
      return Status(Code::kIsRetry);
    } break;
    case kEvictBundle: {
      bip.set_state(BlockInfoProto::kPersisted);
      dbip->IncVersion();
      dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      return Status::OK();
    } break;
    case kDeny:
    default: {
      return Status(JavaExceptions::kIOException, msg);
    } break;
  }
}

Status BIPWriteManager::PersistBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    const absl::optional<std::string>& etag,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  CHECK_NOTNULL(ne_cmd);
  if (NameSpace::IsHdfsMode()) {
    return PersistHdfsBlock(blk_lck_comps,
                            blk_from_dn,
                            dn_uuid,
                            pufs_name,
                            upload_id,
                            upload_cmd,
                            ne_cmd);
  }
  if (NameSpace::IsAccMode()) {
    return PersistAccBlock(blk_lck_comps,
                           blk_from_dn,
                           dn_uuid,
                           pufs_name,
                           upload_id,
                           etag,
                           upload_cmd,
                           ne_cmd);
  }
  LOG(FATAL) << "unreachable";
  return Status::OK();
}

Status BIPWriteManager::PersistHdfsBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  if (dn_uuid.empty() || pufs_name.empty()) {
    return Status(JavaExceptions::kIOException, "dn_uuid/pufs_name is empty");
  }

  BlockID blk_id = blk_from_dn.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when PersistBlock", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  s = dbip->IsEqualTo(blk_from_dn, dn_uuid);
  if (!s.IsOK()) {
    return s;
  }

  switch (dbip->Get().state()) {
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery:
    case BlockInfoProto::kCommitted: {
      std::string msg =
          absl::StrFormat("DN{uuid:%s} tries to persist non-complete B%d",
                          dn_uuid,
                          dbip->Get().block_id());
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      return Status(JavaExceptions::kIOException, msg);
    } break;
    // DNs will use PUT instead of MULTI_PART_UPLOAD to upload small files.
    // So BlockInfoProto::kUploadIssued isn't a MUST state.
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued: {
      if (!dbip->Get().dn_uuid().empty() && dn_uuid != dbip->Get().dn_uuid()) {
        std::string msg =
            absl::StrFormat("DN{%s} persists B%d uploaded by DN{%s}",
                            dn_uuid,
                            dbip->Get().block_id(),
                            dbip->Get().dn_uuid());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      }
      if (!dbip->Get().pufs_name().empty() &&
          pufs_name != dbip->Get().pufs_name()) {
        std::string msg = absl::StrFormat(
            "DN{%s} persists B%d with an different pufs name: %s, "
            "origin pufs name is %s",
            dn_uuid,
            dbip->Get().block_id(),
            pufs_name,
            dbip->Get().pufs_name());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      }
      if (!dbip->Get().curr_upload_id().empty() &&
          upload_id != dbip->Get().curr_upload_id()) {
        std::string msg = absl::StrFormat(
            "DN{%s} persists B%d with an different upload id: %s, "
            "origin upload id is %s",
            dn_uuid,
            dbip->Get().block_id(),
            upload_id,
            dbip->Get().curr_upload_id());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        return Status(JavaExceptions::kIOException, msg);
      }
      dbip->Get().set_state(BlockInfoProto::kPersisted);
      dbip->IncVersion();
      dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      return Status();
    } break;
    case BlockInfoProto::kPersisted: {
      if (dbip->IsFlushed()) {
        // TODO: Improve performance.
        // Maybe the state of BlockInfoProto on DB is already persisted
        // but IsFlushed() returns false.
        dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      }
      return Status(Code::kIsRetry);
    } break;
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted:
    default: {
      std::string msg = absl::StrFormat(
          "DN{uuid:%s} tries to persist B%d with inconsistent state %d",
          dn_uuid,
          blk_id,
          static_cast<int>(dbip->Get().state()));
      return Status(JavaExceptions::kIOException, msg);
    } break;
  }
  LOG(FATAL) << "unreachable";
  return Status(JavaExceptions::kIOException, "unreachable");
}

Status BIPWriteManager::DeleteBlock(const BIPLockComponents& blk_lck_comps,
                                    const Block& blk_from_dn) {
  BlockID blk_id = blk_from_dn.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  // DeleteBlock
  // PreCommit
  // -> DeleteBlock: state() == BlockInfoProto::kDeleted
  // PostCommit
  if (!s.IsOK() || dbip->Get().state() == BlockInfoProto::kDeleted) {
    LOG(INFO) << "Delete request retry on B" << blk_from_dn.id;
    return Status(Code::kIsRetry);
  }
  if (dbip->Get().state() != BlockInfoProto::kDeprecated) {
    std::string msg =
        absl::StrFormat("Delete non-deprecated B%d, whose state is %d",
                        blk_id,
                        static_cast<int>(dbip->Get().state()));
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if (dbip->Get().replicas_size() != 0) {
    LOG(WARNING) << "Delete non-zero-replica block: " << dbip->ToString();
  }
  dbip->Get().set_state(BlockInfoProto::kDeleted);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::PersistAccBlock(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk_from_dn,
    const std::string& dn_uuid,
    const std::string& pufs_name,
    const std::string& upload_id,
    const absl::optional<std::string>& etag,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  if (dn_uuid.empty()) {
    return Status(JavaExceptions::kIOException, "dn_uuid is empty");
  }

  BlockID blk_id = blk_from_dn.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when PersistBlock", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  s = dbip->IsEqualTo(blk_from_dn, dn_uuid);
  if (!s.IsOK()) {
    return s;
  }

  enum {
    kUpload,
    kDeny,
    kPersist,
    kEvict,
  };

  int action = kDeny;
  std::string msg;
  uint64_t current_sec = TimeUtilV2::GetNowEpochMs() / 1000;
  BlockInfoProto& bip = dbip->Get();
  CheckAccBlock(bip);
  bool is_append_block = bip.upload_type() == cloudfs::datanode::APPEND;

  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction:
    case BlockInfoProto::kUnderRecovery:
    case BlockInfoProto::kCommitted: {
      action = kDeny;
      msg = absl::StrFormat(
          "DN{uuid:%s} tries to persist non-complete B%d", dn_uuid, blk_id);
      LOG(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      break;
    } break;
    case BlockInfoProto::kComplete:
    case BlockInfoProto::kUploadIssued: {
      if (dn_uuid != bip.dn_uuid()) {
        action = kDeny;
        msg =
            absl::StrFormat("DN{uuid:%s} persists B%d uploaded by DN{uuid:%s}",
                            dn_uuid,
                            blk_id,
                            bip.dn_uuid());
        LOG(ERROR) << msg;
        MFC(LoggerMetrics::Instance().error_)->Inc();
        break;
      }
      if (is_append_block) {
        action = kPersist;
        msg = absl::StrFormat(
            "Issue evict to DN{uuid:%s} for B%d", dn_uuid, blk_id);
        break;
      } else {
        if (!bip.key_block()) {
          action = kDeny;
          msg = absl::StrFormat(
              "Upload success from DN{uuid:%s} for non-key block B%d",
              dn_uuid,
              blk_id);
          LOG(ERROR) << msg;
          MFC(LoggerMetrics::Instance().error_)->Inc();
          break;
        }
        if (!bip.has_curr_upload_id() || !bip.has_pufs_name() ||
            upload_id != bip.curr_upload_id() || pufs_name != bip.pufs_name()) {
          action = kUpload;
          msg = absl::StrFormat(
              "Upload id or pufs name from DN{uuid:%s} mismatch for B%d, "
              "theirs %s/%s, ours %s/%s",
              dn_uuid,
              upload_id,
              pufs_name,
              bip.curr_upload_id(),
              bip.pufs_name());
          break;
        }
        if (!etag.has_value()) {
          action = kDeny;
          msg =
              absl::StrFormat("MPU does not have etag from DN{uuid:%s} for B%d",
                              dn_uuid,
                              blk_id);
          LOG(ERROR) << msg;
          MFC(LoggerMetrics::Instance().error_)->Inc();
          break;
        }

        action = kPersist;
        msg = absl::StrFormat("Persist B%d from DN{uuid:%s} with etag %s",
                              blk_id,
                              dn_uuid,
                              etag.value());
        break;
      }
    } break;
    case BlockInfoProto::kPersisted: {
      if (is_append_block) {
        action = kEvict;
        msg = absl::StrFormat(
            "Issue evict to DN{uuid:%s} for B%d", dn_uuid, blk_id);
        break;
      } else {
        INode inode;
        if (meta_storage_->GetINode(bip.inode_id(), &inode) != kOK) {
          action = kDeny;
          msg = absl::StrFormat(
              "Do not issue evict request to DN{uuid:%s} due to "
              "INode{id:%d} is not found for B%d",
              dn_uuid,
              bip.inode_id(),
              blk_id);
          break;
        }
        if (inode.ufs_file_info().file_state() ==
            UfsFileState::kUfsFileStatePersisted) {
          action = kEvict;
          msg = absl::StrFormat(
              "Issue evict request to DN{uuid:%s} for B%d", dn_uuid, blk_id);
          break;
        }
        if (!bip.has_curr_upload_id() || !bip.has_pufs_name() ||
            upload_id != bip.curr_upload_id() || pufs_name != bip.pufs_name()) {
          action = kUpload;
          msg = absl::StrFormat(
              "Upload id or pufs name from DN{uuid:%s} mismatch for B%d, "
              "theirs %s/%s, ours %s/%s",
              dn_uuid,
              upload_id,
              pufs_name,
              bip.curr_upload_id(),
              bip.pufs_name());
          break;
        }
        if (bip.has_etag() && bip.etag() == etag) {
          action = kDeny;
          msg = absl::StrFormat(
              "Do not issue evict to DN{uuid:%s} for B%d due to file is not "
              "persisted",
              dn_uuid,
              blk_id);
          break;
        }

        action = kPersist;
        msg = absl::StrFormat("Persist B%d from DN{uuid:%s} with etag %s",
                              blk_id,
                              dn_uuid,
                              etag.value());
        break;
      }
    } break;
    case BlockInfoProto::kDeprecated:
    case BlockInfoProto::kDeleted:
    default: {
      action = kDeny;
      msg = absl::StrFormat(
          "DN{uuid:%s} tries to persist B%d with inconsistent state %d",
          dn_uuid,
          blk_id,
          static_cast<int>(bip.state()));
      break;
    } break;
  }

  LOG(INFO) << "Compute action finished, result: " << action
            << ", msg: " << msg;

  switch (action) {
    case kUpload: {
      auto times = bip.upload_issued_times() + 1;
      bip.set_upload_issued_times(times);
      bip.set_dn_uuid(dn_uuid);
      int32_t interval = FLAGS_min_upload_timeout_s * times;
      if (interval > FLAGS_max_upload_timeout_s) {
        interval = FLAGS_max_upload_timeout_s;
      }
      bip.set_dn_exp_ts(current_sec + interval);
      bip.set_nn_exp_ts(current_sec + interval + FLAGS_nn_dn_clock_drift_s);
      dbip->IncVersion();
      dbip->GetUploadCommand(blockpool_id_, upload_cmd);
      return Status::OK();
    } break;
    case kPersist: {
      bip.set_state(BlockInfoProto::kPersisted);
      if (!is_append_block) {
        bip.set_etag(etag.value());
      }
      dbip->IncVersion();
      if (is_append_block) {
        dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      }
      return Status::OK();
    } break;
    case kEvict: {
      if (dbip->IsFlushed()) {
        dbip->GetNotifyEvictableCommand(blockpool_id_, ne_cmd);
      }
      return Status(Code::kIsRetry);
    } break;
    case kDeny:
    default: {
      return Status(JavaExceptions::kIOException, msg);
    } break;
  };
}

// TODO(ruanjunbin,zhuangsiyu)
// Return value should keep identical with UploadBlock.
// s.HasException() means the block has some problems, upload failed.
// s.IsOK() means caller should do upload block.
// s.IsFalse() means caller should not upload block or sync BlockInfoProto.
Status BIPWriteManager::CheckAccPersistedBlocks(
    const BIPLockComponents& blk_lck_comps,
    BlockID blk_id,
    const std::string& pufs_name,
    const std::string& upload_id,
    cloudfs::datanode::UploadCommandProto* upload_cmd) {
  DirtyBlockInfoProto* dbip = nullptr;
  Status s;
  s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg = absl::StrFormat(
        "Found missing penult B%d when CompletePenultBlkAndCommitLastBlk",
        blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  BlockInfoProto& bip = dbip->Get();

  if (!bip.key_block()) {
    LOG(INFO) << "B" << blk_id << " is not key block";
    return Status(Code::kOK, "NonKeyBlock");
  }

  if (bip.has_curr_upload_id() && bip.has_pufs_name() &&
      bip.curr_upload_id() == upload_id && bip.pufs_name() == pufs_name) {
    if (bip.has_etag() && !bip.etag().empty()) {
      LOG(INFO) << "B" << blk_id << " has been uploaded with correct upload id";
      return Status(Code::kOK, "HasBeenUploaded");
    }
    if (bip.has_dn_uuid() && dbip->IsFlushed()) {
      dbip->GetUploadCommand(blockpool_id_, upload_cmd);
    }
    return Status(Code::kIsRetry);
  } else {
    LOG(INFO) << "Upload id or pufs name mismatch. bip " << bip.curr_upload_id()
              << " / " << bip.pufs_name() << ". inode " << upload_id << " / "
              << pufs_name;

    bip.set_pufs_name(pufs_name);
    bip.set_curr_upload_id(upload_id);
    bip.clear_etag();
    dbip->IncVersion();
    if (bip.has_dn_uuid()) {
      dbip->GetUploadCommand(blockpool_id_, upload_cmd);
    }
    return Status(Code::kFalse);
  }
  return Status::OK();
}

// Status BIPWriteManager::PrepareToTransfer(
//     BlockID blk_id,
//     const std::vector<std::string>& target_dn_uuids) {
//   DirtyBlockInfoProto* dbip = nullptr;
//   Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(blk_lck_comps,blk_id,
//   false, &dbip); if (!s.IsOK()) {
//     return s;
//   }
//   if (dbip->Get().state() == BlockInfoProto::kDeprecated) {
//     std::string msg = absl::StrFormat(
//         "Prepare for transfer replica on deprecated B%d", blk_id);
//     LOG(INFO) << msg;
//     return Status(JavaExceptions::kIOException, msg);
//   }
//   AddNewReplicas(&dbip->Get(), /*invalid gen stamp=*/0, target_dn_uuids);
//   dbip->IncVersion();
//   return Status();
// }

// Status BIPWriteManager::ProcessOverReplicatedBlk(
//     BlockID blk_id,
//     std::vector<ReplicaInfoProto>* replicas_to_be_invalidated) {
//   CHECK_NOTNULL(replicas_to_be_invalidated);
//   DirtyBlockInfoProto* dbip = nullptr;
//   Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(blk_lck_comps,blk_id,
//   false, &dbip); if (!s.IsOK()) {
//     return s;
//   }
//   switch (dbip->Get().state()) {
//     case BlockInfoProto::kUnderConstruction:
//     case BlockInfoProto::kUnderRecovery:
//     case BlockInfoProto::kCommitted: {
//       std::string msg = absl::StrFormat(
//           "Process over replicated but non-completed B%d", blk_id);
//       LOG(ERROR) << msg;
//       MFC(LoggerMetrics::Instance().error_)->Inc();
//       return Status(JavaExceptions::kIOException, msg);
//     } break;
//     case BlockInfoProto::kComplete:
//     case BlockInfoProto::kUploadIssued:
//     case BlockInfoProto::kPersisted: {
//       if (ReplicaNumV2::CountReplica(datanode_manager_.get(), dbip->Get())
//               .healthy < dbip->Get().expected_rep()) {
//         std::string msg = absl::StrFormat(
//             "Process over replicated but under replicated B%d", blk_id);
//         LOG(WARNING) << msg;
//         MFC(LoggerMetrics::Instance().warn_)->Inc();
//         return Status(JavaExceptions::kIOException, msg);
//       } else {
//         SortReplicas(datanode_manager_.get(), &dbip->Get())();
//         uint32_t invalidate_sec = TimeUtilV2::GetNowEpochMs() / 1000;
//         auto replicas_size = dbip->Get().replicas_size();
//         auto expected_rep = dbip->Get().expected_rep();
//         CHECK_LE(replicas_size, expected_rep);
//         for (int i = 0; i < replicas_size - expected_rep; i++) {
//           auto replica = dbip->Get().mutable_replicas(i);
//           replicas_to_be_invalidated->push_back(*replica);
//           replica->set_invalidate_ts(invalidate_sec);
//         }
//         dbip->IncVersion();
//         return Status();
//       }
//     } break;
//     case BlockInfoProto::kDeprecated:
//     case BlockInfoProto::kDeleted:
//     default: {
//       std::string msg = absl::StrFormat(
//           "Process over replicated B%d with inconsistent state %d",
//           blk_id,
//           dbip->Get().state());
//       LOG(ERROR) << msg;
//       MFC(LoggerMetrics::Instance().error_)->Inc();
//       return Status(JavaExceptions::kIOException, msg);
//     } break;
//   }
//   LOG(FATAL) << "unreachable";
//   return Status(JavaExceptions::kIOException, "unreachable");
// }

Status BIPWriteManager::ReportDeletedReplica(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk,
    const std::string& dn_uuid) {
  BlockID blk_id = blk.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when ReportDeletedReplica", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }
  DelReplica(&dbip->Get(), dn_uuid);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::ReportExistedReplica(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk,
    const std::string& dn_uuid,
    cloudfs::ReplicaStateProto state) {
  BlockID blk_id = blk.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when ReportExistedReplica", blk_id);
    LOG(INFO) << msg;
    return Status(JavaExceptions::kIOException, msg);
  }

  if (state == cloudfs::ReplicaStateProto::FINALIZED) {
    for (const auto& replica : dbip->Get().replicas()) {
      if (ShouldReplicaReportTsFollowFBRTs(replica, dn_uuid) &&
          replica.gen_stamp() == blk.gs &&
          replica.num_bytes() == blk.num_bytes) {
        // Report timestamp of finalized replica
        // follows full block report timestamp of its datanode.
        return Status(Code::kIsRetry);
      }
    }
  }

  ReplicaInfoProto& replica = GetOrCreateReplica(&dbip->Get(), dn_uuid);
  replica.set_report_ts(TimeUtilV2::GetNowEpochMs() / 1000);
  replica.set_reporter(ReplicaInfoProto::kDatanode);
  replica.set_state(state);
  replica.set_gen_stamp(blk.gs);
  replica.set_num_bytes(blk.num_bytes);
  replica.set_dn_uuid(dn_uuid);
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::ReportBadReplicas(
    const BIPLockComponents& blk_lck_comps,
    const Block& blk,
    const std::vector<std::string>& dn_uuids) {
  BlockID blk_id = blk.id;
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, false, &dbip);
  if (!s.IsOK()) {
    std::string msg =
        absl::StrFormat("Found missing B%d when ReportBadReplicas", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  auto report_ts = TimeUtilV2::GetNowEpochMs() / 1000;
  for (const std::string& dn_uuid : dn_uuids) {
    ReplicaInfoProto& replica = GetOrCreateReplica(&dbip->Get(), dn_uuid);
    replica.set_report_ts(report_ts);
    replica.set_is_bad(true);
    replica.set_reporter(ReplicaInfoProto::kDatanode);
    replica.set_state(cloudfs::ReplicaStateProto::TEMPORARY);
    replica.set_gen_stamp(blk.gs);
    replica.set_num_bytes(blk.num_bytes);
    replica.set_dn_uuid(dn_uuid);
  }
  dbip->IncVersion();
  return Status();
}

Status BIPWriteManager::TestOnlyGetOrLoadDirtyBlockInfoProtoIfMissing(
    const BIPLockComponents& blk_lck_comps,
    BlockID blk_id,
    bool is_creating,
    DirtyBlockInfoProto** dbip) {
  return GetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, blk_id, is_creating, dbip);
}

Status BIPWriteManager::GetOrLoadDirtyBlockInfoProtoIfMissing(
    const BIPLockComponents& blk_lck_comps,
    BlockID blk_id,
    bool is_creating,
    DirtyBlockInfoProto** dbip) {
  StopWatch sw(
      BMetrics::Instance().get_or_load_dirty_block_info_proto_if_missing_time);
  sw.Start();
  CHECK_NOTNULL(dbip);

  if (!IsBlockIDValid(blk_id)) {
    return Status(JavaExceptions::kIOException);
  }

  // Consider a scenario where a thread sequentially executes the following
  // functions in BlockReportHandler::ExecuteFullBlockReportTask:
  // (a) DirtyBlockInfoProtoBucket::Lock
  // (b) DirtyBlockInfoProto::Lock
  // (c) DirtyBlockInfoProtoBucket::Unlock
  // (d) BIPWriteManager::GetOrLoadDirtyBlockInfoProtoIfMissing
  //   (d.1) DirtyBlockInfoProtoBucket::GetOrCreate
  // It's crucial to note that the thread executes (d.1) without the protection
  // of DirtyBlockInfoProtoBucket::mtx_. This can lead to undefined behavior if
  // another thread is concurrently inserting an element into
  // DirtyBlockInfoProtoBucket, which triggers the unordered_map inside
  // DirtyBlockInfoProtoBucket to rehash.
  //
  // BlockID bkt_id = blk_id & bucket_mask_;
  // DirtyBlockInfoProtoBucket& bkt = buckets_[bkt_id];
  // *dbip = bkt.GetOrCreate(blk_id);
  // if ((*dbip)->IsInitialized()) {
  //   return Status();
  // }
  //
  // Applying a lock to DirtyBlockInfoProtoBucket at this point is not a viable
  // solution. This approach would violate the established lock order, which
  // requires the lock of DirtyBlockInfoProtoBucket to be taken first, followed
  // by the lock of DirtyBlockInfoProto. It's important to note that the lock of
  // DirtyBlockInfoProto is already taken externally.
  //
  // The following code illustrates a potential solution to the issue.
  *dbip = blk_lck_comps.Get(blk_id);
  if (*dbip == nullptr) {
    std::string msg = absl::StrFormat("%d not found in blk_lck_comps", blk_id);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  if ((*dbip)->IsInitialized()) {
    return Status();
  }

  BlockInfoProto bip;
  if (meta_storage_->GetBlockInfo(blk_id, &bip)) {
    (*dbip)->Load(bip);
    return Status();
  }
  if (is_creating) {
    return Status();
  } else {
    // Do not log error here.
    // Otherwise there will be too many error logs even if everything is okay.
    // Leave uninitialized DirtyBlockInfoProto to the gc thread.
    return Status(JavaExceptions::kIOException);
  }
}

}  // namespace dancenn
