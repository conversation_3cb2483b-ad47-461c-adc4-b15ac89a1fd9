// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_UNDER_CONSTRUCTION_STATE_H_
#define BLOCK_MANAGER_UNDER_CONSTRUCTION_STATE_H_

#include <vector>
#include <unordered_map>

#include "datanode_manager/datanode_info.h"

namespace dancenn {

// This class is used to store information for an under constructed block.
// Since replicas can have different generation stamps due to lease recover,
// we use a map to store generation stamp for each replica
struct UCInfo {
  UCInfo() : gs(0), nbytes(0) {}
  UCInfo(uint64_t g, uint64_t n)
    : gs(g), nbytes(n) {}

  uint64_t gs;
  uint64_t nbytes;
};

class UnderConstructionState {
 public:
  UnderConstructionState()
      : recovery_id_(0), is_recover_cmd_sent_(false), primary_storage_id_(0) {
  }
  explicit UnderConstructionState(const Block& b, const std::vector<DatanodeID>& dns);
  UnderConstructionState(const UnderConstructionState& other);

  std::unordered_map<DatanodeID, UCInfo> expected_locations() const {
    return expected_locations_;
  }
  std::unordered_map<DatanodeID, bool>& tried_as_primary() {
    return tried_as_primary_;
  }
  std::unordered_map<DatanodeID, bool> immutable_tried_as_primary() const {
    return tried_as_primary_;
  }
  void set_tried_as_primary(const std::unordered_map<DatanodeID, bool>& t) {
    tried_as_primary_.clear();
    tried_as_primary_ = t;
  }
  DatanodeID primary_storage_id() const { return primary_storage_id_; }
  void set_primary_storage_id(DatanodeID primary) {
    primary_storage_id_ = primary;
  }

  uint64_t recovery_id() const { return recovery_id_; }
  void set_recovery_id(uint64_t recover_id) {
    recovery_id_ = recover_id;
    is_recover_cmd_sent_ = false;
  }
  bool is_recover_cmd_sent() const {
    return is_recover_cmd_sent_;
  }
  void set_is_recover_cmd_sent() {
    is_recover_cmd_sent_ = true;
  }

  void AddReplica(DatanodeID dn_id, uint64_t gs, uint64_t nbytes);

  void FilterInvalidReplica(uint64_t gs);
  size_t NumReportedStorages() { return tried_as_primary_.size(); }
  std::vector<DatanodeID> GetStorages();

 private:
  // datanode -> {generation stamps, num bytes}
  std::unordered_map<DatanodeID, UCInfo> expected_locations_;
  // datanode -> tried as primary
  std::unordered_map<DatanodeID, bool> tried_as_primary_;
  uint64_t recovery_id_;
  bool is_recover_cmd_sent_;
  DatanodeID primary_storage_id_;  // TODO(liyuan) maybe we don't need this here
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_UNDER_CONSTRUCTION_STATE_H_

