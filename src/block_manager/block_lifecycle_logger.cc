#include "block_lifecycle_logger.h"
#include <inttypes.h>

DECLARE_string(nameservice);
DECLARE_int32(transfer_timeout_threshold_sec);
DECLARE_int32(flush_blocklifecycle_interval_ms);

DECLARE_int32(block_lifecycle_num_slice);
DECLARE_string(block_lifecycle_dn_data_channel);
DECLARE_string(block_lifecycle_blk_data_channel);

namespace dancenn {

using cloudfs::ReplicaStateProto;

BlockLifecycleLogger& BlockLifecycleLogger::GetSingleton() {
  static BlockLifecycleLogger logger;
  return logger;
}

BlockLifecycleLogger::BlockLifecycleLogger()
    : num_slices_(static_cast<size_t>(FLAGS_block_lifecycle_num_slice)) {
  slice_mask_ = num_slices_ - 1;
  CHECK_EQ(slice_mask_ & num_slices_, 0)
      << "FLAGS_transfermap_num_slice must be power of 2";

  // Init metrics.
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("BlockLifeCycle");
  enqueue_log_time_ =  metrics->RegisterHistogram("EnqueueBLKLifecycleTime");
  abandon_lifecycle_log_count_ = metrics->RegisterCounter(
      "abandon_log_count");
  for (size_t i = 0; i < num_slices_; i++) {
    slices_.emplace_back(new BlockLifeCycleMapSlice(abandon_lifecycle_log_count_));
  }

  // Init Databus.
  // channel_ = Databus::GetChannel("dancenn_block_lifecycle_test");
  dn_channel_ = Databus::GetChannel(FLAGS_block_lifecycle_dn_data_channel);
  blk_channel_ = Databus::GetChannel(FLAGS_block_lifecycle_blk_data_channel);

  // Init background log sender.
  if (!log_sender_) {
    log_sender_ = std::make_unique<cnetpp::concurrency::ThreadPool>(
        "BlockLifecycleLogSender", true);
    log_sender_->set_num_threads(1);
    log_sender_->Start();
    log_sender_->AddTask(std::static_pointer_cast<cnetpp::concurrency::Task>(
        std::make_shared<SendLogTask>(this)));
  }
}

BlockLifecycleLogger::~BlockLifecycleLogger() {
  if (log_sender_) {
    log_sender_->Stop();
    log_sender_.reset();
  }
}

void BlockLifecycleLogger::EnqueueDNData(const char *fmt, ...) {
  static const int kBufferLength = 2048;
  std::string buf(kBufferLength, 0);
  va_list ap;
  va_start(ap, fmt);

  int need = vsnprintf(&(buf[0]), kBufferLength, fmt, ap);
  if (need > kBufferLength) {
    buf.resize(need + 1, 0);
    va_list dups2_ap;
    va_copy(dups2_ap, ap);
    need = vsnprintf(&(buf[0]), need + 1, fmt, dups2_ap);
    va_end(dups2_ap);
  }

  va_end(ap);

  if (need != -1) {
    buf.resize(need);
    std::unique_lock<std::shared_timed_mutex> guard(dn_channel_lock_);
    dn_records_to_send_.emplace_back(std::move(buf));
  }
}

void BlockLifecycleLogger::LogTransferCommand(uint64_t blk_id, uint64_t gs,
                                               std::string& src_dn,
                                               std::string& dst_dn) {
  TransferBlockRecord record(blk_id, gs, src_dn, dst_dn);

  auto& s = slice(blk_id);
  std::unique_lock<BlockLifeCycleMapSlice> guard(*s);
  s->LogTransferBlockRecord(record);
}

void BlockLifecycleLogger::LogIncrementalBlockReport(
    uint64_t blk_id, const std::string& dn, uint64_t gs, const std::string& op,
    uint32_t numbytes, ReplicaStateProto replica_status) {

  std::string replica_status_str;
  switch (replica_status) {
    case ReplicaStateProto::FINALIZED:
      replica_status_str = "FINALIZED";
      break;
    case ReplicaStateProto::RBW:
      replica_status_str = "RBW";
      break;
    case ReplicaStateProto::RWR:
      replica_status_str = "RWR";
      break;
    case ReplicaStateProto::RUR:
      replica_status_str = "RUR";
      break;
    case ReplicaStateProto::TEMPORARY:
      replica_status_str = "TEMPORARY";
      break;
    default:
      replica_status_str = "UNKNOWN";
  }

  auto& s = slice(blk_id);
  std::unique_lock<BlockLifeCycleMapSlice> guard(*s);
  s->LogIncrementalBlockReport(blk_id, dn, gs, op, numbytes,
                               replica_status_str);
}

std::unique_ptr<BlockLifeCycleMapSlice>& BlockLifecycleLogger::slice(
    dancenn::BlockID blk_id) {
  return slices_[blk_id & slice_mask_];
}

void BlockLifecycleLogger::SendLogToDatabus() {
  for (auto& s : slices_) {
    std::vector<std::string> log_batch;
    {
      std::unique_lock<BlockLifeCycleMapSlice> guard(*s);
      // Clean timeout transfer command.
      s->ScanTimeoutTransferCmd();
      log_batch.swap(s->GetRecordsToSend());
    }

    // Do not hold lock when send log
    if (log_batch.size() > 0) {
      blk_channel_->Emit(log_batch);
    }
  }

  std::vector<std::string> dn_log_batch;
  {
    std::unique_lock<std::shared_timed_mutex> guard(dn_channel_lock_);
    dn_log_batch.swap(dn_records_to_send_);
  }

  if (dn_log_batch.size() > 0) {
    dn_channel_->Emit(dn_log_batch);
  }
}

bool BlockLifecycleLogger::SendLogTask::operator()(void* args) {
  auto last_update = std::chrono::system_clock::now();
  // Flush all the log at one time.
  logger_->SendLogToDatabus();
  auto now = std::chrono::system_clock::now();

  auto check_time_point =
      last_update +
      std::chrono::milliseconds(FLAGS_flush_blocklifecycle_interval_ms);

  if (check_time_point <= now && !stop_) {
    logger_->log_sender_->AddTask(
        std::static_pointer_cast<cnetpp::concurrency::Task>(
            std::make_shared<SendLogTask>(logger_)));
  } else {
    if (!stop_) {
      auto wait_for = std::chrono::duration_cast<std::chrono::milliseconds>(
          check_time_point - now);
      logger_->log_sender_->AddDelayTask(
          std::static_pointer_cast<cnetpp::concurrency::Task>(
              std::make_shared<SendLogTask>(logger_)),
          wait_for);
      LOG(INFO) << "Schedule next byte lifecycle data flush "
                << wait_for.count() << " ms";
    }
  }
  return true;
}

}  // namespace dancenn