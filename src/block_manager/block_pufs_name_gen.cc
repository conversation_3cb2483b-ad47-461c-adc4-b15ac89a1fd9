// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/17
// Description

#include "block_manager/block_pufs_name_gen.h"

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <openssl/md5.h>

#include <limits>

#include "base/platform.h"
#include "hdfs.pb.h"

DECLARE_int32(namespace_type);
DECLARE_string(tos_bucket);
DECLARE_string(tos_prefix);
DECLARE_int64(namespace_id);
DECLARE_uint64(tos_suffix_salt);
DECLARE_string(hdfs_prefix);

namespace dancenn {

BlockPufsNameGen BlockPufsNameGen::Default() {
  switch (FLAGS_namespace_type) {
    case cloudfs::ACC_HDFS:
      return {"", FLAGS_hdfs_prefix, FLAGS_namespace_id};
    default:
      return {FLAGS_tos_bucket, FLAGS_tos_prefix, FLAGS_namespace_id};
  }
}

BlockPufsNameGen::BlockPufsNameGen(const std::string& tos_bucket,
                                   const std::string& tos_prefix,
                                   int64_t ns_id)
    : tos_bucket_(tos_bucket), tos_prefix_(tos_prefix), ns_id_(ns_id) {
  if (!tos_prefix_.empty() && tos_prefix_.back() != '/') {
    tos_prefix_.push_back('/');
  }
  CHECK(tos_prefix_.empty() || tos_prefix_.back() == '/');

}

std::string BlockPufsNameGen::Get(BlockID blk_id) {
  // According to https://en.wikipedia.org/wiki/Salt_(cryptography),
  // using the same salt for all passwords is dangerous
  // because a precomputed table which simply accounts for the salt
  // will render the salt useless.
  // But using one salt for one block is too expensive for datanodes.
  // So we decide to use one salt for one namespace.
  uint64_t data = blk_id + FLAGS_tos_suffix_salt;
  // hashlib.md5(int.to_bytes(<block_id> + FLAGS_tos_suffix_salt,
  //                          8,
  //                          'little')).hexdigest()
  // https://www.openssl.org/docs/man1.0.2/man3/MD5.html
  // Return 1 for success, 0 otherwise.
  MD5_CTX md5_ctx;
  CHECK_EQ(MD5_Init(&md5_ctx), 1);
  // Return 1 for success, 0 otherwise.
  CHECK_EQ(MD5_Update(&md5_ctx, &data, sizeof(data)), 1);
  // MD5_Final() places the message digest in md,
  // which must have space for MD5_DIGEST_LENGTH == 16 bytes of output,
  // and erases the MD5_CTX.
  // https://github.com/openssl/openssl/blob/ef917549f5867d269d359155ff67b8ccb5e66a76/include/openssl/md5.h#L28
  uint8_t md5[16] = {0};
  CHECK_EQ(MD5_Final(md5, &md5_ctx), 1);
  uint64_t v1 = platform::ReadBigEndian<uint64_t>(&md5[0], 0);
  uint64_t v2 = platform::ReadBigEndian<uint64_t>(&md5[8], 0);
  return absl::StrFormat(
      "%s%d/block/%d-%016llx%016llx.block",
      tos_prefix_, ns_id_, blk_id, v1, v2);
}

std::string BlockPufsNameGen::GetTosResource() {
  return absl::StrFormat(
      "trn:tos:::%s/%s%d/block/*", tos_bucket_, tos_prefix_, ns_id_);
}

}  // namespace dancenn
