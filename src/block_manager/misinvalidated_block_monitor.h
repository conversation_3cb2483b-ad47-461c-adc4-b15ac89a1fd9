// Copyright 2019 <PERSON> <<EMAIL>>

#ifndef BLOCK_MANAGER_MISINVALIDATE_BLOCK_MONITOR_H_
#define BLOCK_MANAGER_MISINVALIDATE_BLOCK_MONITOR_H_

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <mutex>

namespace dancenn {

class BlockManager;

class MisinvalidatedBlockMonitor {
 public:
  explicit MisinvalidatedBlockMonitor(BlockManager* bm) : bm_(bm) {}
  ~MisinvalidatedBlockMonitor() {
    Stop();
  }

  void Start() {
    CHECK(!worker_.get());
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(
            new Task(this)), "MisInvBlk");
    worker_->Start();
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

 private:
  BlockManager* bm_{nullptr};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  friend class Task;

  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(MisinvalidatedBlockMonitor* monitor) : monitor_(monitor) {}

    bool operator()(void* arg) override;

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

   private:
    MisinvalidatedBlockMonitor* monitor_{nullptr};
    std::mutex mu_;
    std::condition_variable cond_;
  };

  void Do();
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_MISINVALIDATE_BLOCK_MONITOR_H_

