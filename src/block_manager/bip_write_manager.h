// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2022/08/26
// Description

#ifndef BLOCK_MANAGER_DIRTY_BLOCK_INFO_PROTO_MANAGER_H_
#define BLOCK_MANAGER_DIRTY_BLOCK_INFO_PROTO_MANAGER_H_

#include <absl/types/optional.h>             // For optional.
#include <cnetpp/concurrency/spin_lock.h>    // SpinLock.
#include <cnetpp/concurrency/task.h>         // For Task.
#include <cnetpp/concurrency/thread.h>       // For Thread.
#include <google/protobuf/repeated_field.h>  // For RepeatedPtrField.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For DatanodeCommandProto, NotifyEvictableCommandProto, UploadCommandProto, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For ReplicaStateProto, IoMode, BlockProto, ExtendedBlockProto, DatanodeIDProto, NamespaceType, IoMode, RecoveringBlockProto, etc.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto, ReplicaInfoProto.
#include <proto/generated/dancenn/edit_log.pb.h>  // For BlockInfoProtos.
#include <proto/generated/dancenn/inode.pb.h>     // For INode.

#include <atomic>              // For atomic.
#include <condition_variable>  // For condition_variable.
#include <cstdint>             // For uint32_t, uint64_t.
#include <functional>          // For function.
#include <list>                // For list.
#include <map>                 // For map.
#include <memory>              // For shared_ptr, unique_ptr, make_unique.
#include <mutex>               // For mutex, once_flag, call_once.
#include <set>                 // For set.
#include <string>              // For string.
#include <unordered_map>       // For unordered_map.
#include <unordered_set>       // For unordered_set.
#include <utility>             // For move, piecewise_construct, pair.
#include <vector>              // For vector.

#include "base/closure.h"  // For SynchronizedClosure.
#include "base/metrics.h"  // For MetricID, MetricsCenter.
#include "base/status.h"   // For Status.
#include "block_manager/block.h"  // For Block, BlockID, kInvalidBlockID, IsBlockIDValid.
#include "block_manager/block_info.h"        // For BlockUCState.
#include "datanode_manager/datanode_info.h"  // For DatanodeInfoPtr, DatanodeID.
#include "namespace/inode.h"                 // For INodeID.
#include "namespace/meta_storage.h"          // For MetaStorage.

namespace dancenn {

class BMetrics {
 public:
  static BMetrics& Instance();

 private:
  BMetrics() = default;

 public:
  MetricID acquire_blk_lock_time;
  MetricID hold_blk_lock_time;
  MetricID acquire_bucket_lock_time;
  MetricID hold_bucket_lock_time;
  MetricID get_or_load_dirty_block_info_proto_if_missing_time;

  MetricID upload_is_denied_num;
};

class DatanodeManager;
struct ReplicaNumV2 {
  ReplicaNumV2();

  // The following states of replicas are mutually exclusive. For example:
  // A healthy replica cannot be a corrupt replica,
  // a stale replica cannot be a invalidating replica.
  uint8_t invalidating;
  uint8_t corrupt;
  // Number of replicas with very early reporting time.
  uint8_t stale;
  uint8_t replicating;
  uint8_t decommission;
  uint8_t healthy;

  static ReplicaNumV2 CountReplica(DatanodeManager* datanode_manager,
                                   const BlockInfoProto& bip);
  static ReplicaNumV2 CountReplica(
      DatanodeManager* datanode_manager,
      BlockInfoProto* bip,
      std::unordered_set<DatanodeInfoPtr>* containing_dns = nullptr,
      std::unordered_set<DatanodeInfoPtr>* decommission_dns = nullptr,
      std::vector<ReplicaInfoProto*>* healthy_replicas = nullptr,
      std::unordered_set<DatanodeInfoPtr>* healthy_dns = nullptr);

 private:
  static bool IsCorrupt(const BlockInfoProto& bip,
                        const ReplicaInfoProto& replica);
};

struct BDebugMsg {
  const char* filename;
  uint32_t line_number;
  // uint32_t internal_id;
  BlockID blk_id;
};
// DirtyBlockInfoProto use BDebugMsg as a field,
// so we do not want BDebugMsg to be a very big structure.
static_assert(sizeof(BDebugMsg) == 24, "size of BDebugMsg is not 24");

template <typename M>
class BUniqueLock {
 public:
  BUniqueLock();
  BUniqueLock(M* mtx, const BDebugMsg& debug_msg);
  BUniqueLock(const BUniqueLock& other) = delete;
  BUniqueLock(BUniqueLock&& other);
  BUniqueLock& operator=(const BUniqueLock& other) = delete;
  BUniqueLock& operator=(BUniqueLock&& other);
  ~BUniqueLock();

 private:
  M* mtx_;
};

class DirtyBlockInfoProto;
class BIPLockComponents {
 public:
  BIPLockComponents();
  explicit BIPLockComponents(int test_only_id);
  BIPLockComponents(const BIPLockComponents& other) = delete;
  BIPLockComponents(BIPLockComponents&& other) = default;
  BIPLockComponents& operator=(const BIPLockComponents& other) = delete;
  BIPLockComponents& operator=(BIPLockComponents&& other) = default;
  ~BIPLockComponents() = default;

  int TestOnlyId() const;

  void Add(BlockID blk_id,
           DirtyBlockInfoProto* dbip,
           BUniqueLock<DirtyBlockInfoProto>&& lock);
  DirtyBlockInfoProto* Get(BlockID blk_id) const;
  void Reset();
  int TestOnlySize() const;

 private:
  int test_only_id_;
  std::map<BlockID,
           std::pair<DirtyBlockInfoProto*, BUniqueLock<DirtyBlockInfoProto>>>
      locks_;
};

class DirtyBlockInfoProto {
 public:
  explicit DirtyBlockInfoProto(BlockID blk_id);
  DirtyBlockInfoProto(const DirtyBlockInfoProto& other) = delete;
  DirtyBlockInfoProto(DirtyBlockInfoProto&& other) = delete;
  DirtyBlockInfoProto& operator=(const DirtyBlockInfoProto& other) = delete;
  DirtyBlockInfoProto& operator=(DirtyBlockInfoProto&& other) = delete;
  ~DirtyBlockInfoProto();

  bool TryLock4GC(const BDebugMsg& debug_msg);
  void PreLock();
  void Lock(const BDebugMsg& debug_msg);
  void Unlock();
  std::unique_ptr<std::mutex> ReleaseLock();
  bool TestOnlyIsLocked() const;

  bool IsInitialized() const;
  std::string ToString() const;
  void Load(const BlockInfoProto& bip);
  BlockInfoProto& Get();
  const BlockInfoProto& Get() const;

  bool IsFlushed() const;
  void IncVersion();
  Status PreCommit(BlockInfoProto* bip);
  Status PostCommit(const BlockInfoProto& bip);

  Status IsEqualTo(const Block& blk_from_dn, const std::string& dn_uuid) const;

  Status GetUploadCommand(const std::string& blockpool_id,
                          cloudfs::datanode::UploadCommandProto* upload_cmd);
  Status GetNotifyEvictableCommand(
      const std::string& blockpool_id,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);

 private:
  std::atomic<int64_t> waiters_;
  std::unique_ptr<std::mutex> mtx_;
  bool test_only_is_locked_;
  BlockInfoProto bip_;
  int32_t unstarted_tx_num_;
  int32_t ongoing_tx_num_;
  // About debug infos.
  uint64_t stop_watch_ms_;
  BDebugMsg debug_msg_;
};

class DirtyBlockInfoProtoBucket {
 public:
  DirtyBlockInfoProtoBucket();
  DirtyBlockInfoProtoBucket(const DirtyBlockInfoProtoBucket& other) = delete;
  DirtyBlockInfoProtoBucket(DirtyBlockInfoProtoBucket&& other);
  DirtyBlockInfoProtoBucket& operator=(const DirtyBlockInfoProtoBucket& other) =
      delete;
  DirtyBlockInfoProtoBucket& operator=(DirtyBlockInfoProtoBucket&& other) =
      delete;
  ~DirtyBlockInfoProtoBucket();

  void Lock(const BDebugMsg& debug_msg);
  void Unlock();
  bool TestOnlyIsLocked() const;

  // In the following way, we just hold bucket lock for a very short time.
  // See BIPWriteManager::Lock.
  DirtyBlockInfoProto* GetOrCreate(BlockID blk_id);

 private:
  cnetpp::concurrency::SpinLock mtx_;
  bool test_only_is_locked_;
  std::unordered_map<BlockID, std::unique_ptr<DirtyBlockInfoProto>> bips_;
  // About debug infos.
  uint64_t stop_watch_ms_;
  BDebugMsg debug_msg_;
};

class BIPWriteManagerBase {
 public:
  virtual BIPLockComponents Lock(const std::set<BlockID>& blk_ids,
                                 const char* filename,
                                 uint32_t line_number) = 0;
  virtual BIPLockComponents Lock(const INode& inode,
                                 const char* filename,
                                 uint32_t line_number) = 0;
  virtual BIPLockComponents Lock(const std::vector<BlockInfoProto>& bips,
                                 const char* filename,
                                 uint32_t line_number) = 0;

  virtual Status PreCommit(const BIPLockComponents& blk_lck_comps,
                           BlockID blk_id,
                           BlockInfoProto* bip) = 0;
  virtual Status PostCommit(const BIPLockComponents& blk_lck_comps,
                            const BlockInfoProto& bip) = 0;

  // CliWrite.
  virtual void AddBlock(const BIPLockComponents& blk_lck_comps,
                        const Block& blk,
                        const INode& inode,
                        cloudfs::IoMode write_mode,
                        const std::string& pufs_name,
                        const std::vector<std::string>& dn_uuids) = 0;
  virtual Status CompletePenultBlkAndCommitLastBlk(
      const BIPLockComponents& blk_lck_comps,
      const std::string& src,
      const INode& inode,
      const Block& penult_blk,
      const Block& last_blk_from_cli,
      const Block& last_blk_from_inode,
      bool complete_last_blk,
      absl::optional<BlockUCState> force_set_penult_blk_state,
      absl::optional<BlockUCState> force_set_last_blk_state) = 0;
  virtual Status UpdatePipeline(
      const BIPLockComponents& blk_lck_comps,
      const cloudfs::ExtendedBlockProto& new_blk_from_cli,
      const google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>&
          new_nodes) = 0;
  virtual Status AbandonBlock(const BIPLockComponents& blk_lck_comps,
                              BlockID blk_id) = 0;
  virtual Status Fsync(const BIPLockComponents& blk_lck_comps,
                       const cloudfs::BlockProto& bp,
                       uint64_t new_num_bytes_from_cli) = 0;
  virtual Status ConcatUpdateBlockINodeId(
      const BIPLockComponents& blk_lck_comps,
      const cloudfs::BlockProto& bp,
      const INode& target_inode,
      uint64_t pufs_offset,
      BlockInfoProto* last_bip,
      bool is_last) = 0;
  virtual Status CommitBlockSynchronization(
      const BIPLockComponents& blk_lck_comps,
      const cloudfs::datanode::CommitBlockSynchronizationRequestProto& request,
      absl::optional<BlockUCState> force_set_blk_state) = 0;
  virtual Status SetReplication(const BIPLockComponents& blk_lck_comps,
                                const BlockProto& bp,
                                uint32_t replica_num) = 0;
  virtual Status ReleaseLease(const BIPLockComponents& blk_lck_comps,
                              const cloudfs::BlockProto& penult_bp,
                              const cloudfs::BlockProto& last_bp) = 0;
  virtual Status InitRecover(
      const BIPLockComponents& blk_lck_comps,
      const BlockProto& bp,
      uint64_t recovery_gen_stamp,
      absl::optional<std::string> force_set_primary_dn_uuid,
      std::string* primary_dn_uuid = nullptr,
      cloudfs::RecoveringBlockProto* recovering_block = nullptr) = 0;

  // DnWrite.
  virtual Status UploadBlock(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk_from_dn,
      const std::string& dn_uuid,
      const std::string& pufs_name,
      const std::string& upload_id,
      cloudfs::datanode::UploadCommandProto* upload_cmd,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) = 0;
  virtual Status PersistBlock(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk_from_dn,
      const std::string& dn_uuid,
      const std::string& pufs_name,
      const std::string& upload_id,
      const absl::optional<std::string>& etag,
      cloudfs::datanode::UploadCommandProto* upload_cmd,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) = 0;
  virtual Status DeleteBlock(const BIPLockComponents& blk_lck_comps,
                             const Block& blk_from_dn) = 0;
  virtual Status ReportDeletedReplica(const BIPLockComponents& blk_lck_comps,
                                      const Block& blk,
                                      const std::string& dn_uuid) = 0;
  virtual Status ReportExistedReplica(const BIPLockComponents& blk_lck_comps,
                                      const Block& blk,
                                      const std::string& dn_uuid,
                                      cloudfs::ReplicaStateProto state) = 0;
  virtual Status ReportBadReplicas(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk,
      const std::vector<std::string>& dn_uuids) = 0;
};

// Please notice locking order of different components:
// 1. HAState::CheckOperation
// 2. DirtyBlockInfoProto
class BIPWriteManager : public BIPWriteManagerBase {
 public:
  BIPWriteManager(const std::string& blockpool_id,
                  std::shared_ptr<MetaStorage> meta_storage,
                  std::shared_ptr<DatanodeManager> datanode_manager);
  ~BIPWriteManager() = default;

  // To ensure safety, please take locks before invoking any other methods.
  // It is recommended to release locks after retrieving the transaction ID,
  // as this will allow later writers to
  // access the latest BlockInfoProto through memory
  // instead of a stale one from the persistent kv system.
  BIPLockComponents Lock(const std::set<BlockID>& blk_ids,
                         const char* filename,
                         uint32_t line_number) override;
  BIPLockComponents Lock(const INode& inode,
                         const char* filename,
                         uint32_t line_number) override;
  BIPLockComponents Lock(const std::vector<BlockInfoProto>& bips,
                         const char* filename,
                         uint32_t line_number) override;

  Status PreCommit(const BIPLockComponents& blk_lck_comps,
                   BlockID blk_id,
                   BlockInfoProto* bip) override;
  Status PostCommit(const BIPLockComponents& blk_lck_comps,
                    const BlockInfoProto& bip) override;
  DirtyBlockInfoProto& TestOnlyGetBlock(BlockID blk_id);

  // CliWrite.
  void AddBlock(const BIPLockComponents& blk_lck_comps,
                const Block& blk,
                const INode& inode,
                cloudfs::IoMode write_mode,
                const std::string& pufs_name,
                const std::vector<std::string>& dn_uuids) override;
  Status CompletePenultBlkAndCommitLastBlk(
      const BIPLockComponents& blk_lck_comps,
      const std::string& src,
      const INode& inode,
      const Block& penult_blk,
      const Block& last_blk_from_cli,
      const Block& last_blk_from_inode,
      bool complete_last_blk,
      absl::optional<BlockUCState> force_set_penult_blk_state,
      absl::optional<BlockUCState> force_set_last_blk_state) override;
  Status UpdatePipeline(
      const BIPLockComponents& blk_lck_comps,
      const cloudfs::ExtendedBlockProto& new_blk_from_cli,
      const google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>&
          new_nodes);
  Status AbandonBlock(const BIPLockComponents& blk_lck_comps,
                      BlockID blk_id) override;
  Status Fsync(const BIPLockComponents& blk_lck_comps,
               const cloudfs::BlockProto& bp,
               uint64_t new_num_bytes_from_cli) override;
  Status ConcatUpdateBlockINodeId(const BIPLockComponents& blk_lck_comps,
                                  const cloudfs::BlockProto& bp,
                                  const INode& target_inode,
                                  uint64_t pufs_offset,
                                  BlockInfoProto* last_bip,
                                  bool is_last) override;
  // s.HasException() means the block has some problems, commit block failed.
  // s.code() == Code::kIsRetry means this is a retry request.
  // s.IsOK() means request is succeed.
  Status CommitBlockSynchronization(
      const BIPLockComponents& blk_lck_comps,
      const cloudfs::datanode::CommitBlockSynchronizationRequestProto& request,
      absl::optional<BlockUCState> force_set_blk_state) override;
  // ref: https://bytedance.larkoffice.com/wiki/P21Zwdx3xiPGpzk5cjdc3Knzn2b
  Status CommitSealedBlock(const BIPLockComponents& blk_lck_comps,
                           const BlockProto& bp,
                           uint32_t commit_length);
  Status SetReplication(const BIPLockComponents& blk_lck_comps,
                        const BlockProto& bp,
                        uint32_t replica_num) override;
  // Return if need to do block recovery or not.
  // s.HasException() means the file has some problems, release lease failed.
  // s.code() == Code::kAbandonLastBlock means we have abandoned the last block.
  // s.IsFalse() means caller should not do block recovery.
  // s.IsOK() means caller should do block recovery.
  Status ReleaseLease(const BIPLockComponents& blk_lck_comps,
                      const cloudfs::BlockProto& penult_bp,
                      const cloudfs::BlockProto& last_bp) override;
  Status InitRecover(
      const BIPLockComponents& blk_lck_comps,
      const BlockProto& bp,
      uint64_t recovery_gen_stamp,
      absl::optional<std::string> force_set_primary_dn_uuid,
      std::string* primary_dn_uuid = nullptr,
      cloudfs::RecoveringBlockProto* recovering_block = nullptr) override;

  // DnWrite.
  // s.HasException() means the block has some problems, upload failed.
  // s.IsOK() means caller should do upload block.
  // s.IsFalse() means caller should not upload block or sync BlockInfoProto.
  Status UploadBlock(const BIPLockComponents& blk_lck_comps,
                     const Block& blk_from_dn,
                     const std::string& dn_uuid,
                     const std::string& pufs_name,
                     const std::string& upload_id,
                     cloudfs::datanode::UploadCommandProto* upload_cmd,
                     cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);
  Status PersistBlock(const BIPLockComponents& blk_lck_comps,
                      const Block& blk_from_dn,
                      const std::string& dn_uuid,
                      const std::string& pufs_name,
                      const std::string& upload_id,
                      const absl::optional<std::string>& etag,
                      cloudfs::datanode::UploadCommandProto* upload_cmd,
                      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);
  Status CheckAccPersistedBlocks(
      const BIPLockComponents& blk_lck_comps,
      BlockID blk_id,
      const std::string& pufs_name,
      const std::string& upload_id,
      cloudfs::datanode::UploadCommandProto* upload_cmd);
  Status DeleteBlock(const BIPLockComponents& blk_lck_comps,
                     const Block& blk_from_dn);
  // Status PrepareToTransfer(BlockID blk_id,
  //                          const std::vector<std::string>& target_dn_uuids);
  // Status ProcessOverReplicatedBlk(
  //     BlockID blk_id,
  //     std::vector<ReplicaInfoProto>* replicas_to_be_invalidated);
  Status ReportDeletedReplica(const BIPLockComponents& blk_lck_comps,
                              const Block& blk,
                              const std::string& dn_uuid) override;
  Status ReportExistedReplica(const BIPLockComponents& blk_lck_comps,
                              const Block& blk,
                              const std::string& dn_uuid,
                              cloudfs::ReplicaStateProto state) override;
  // Both clients and data nodes will call this function,
  // but we consider it an DnWrite.
  Status ReportBadReplicas(const BIPLockComponents& blk_lck_comps,
                           const Block& blk,
                           const std::vector<std::string>& dn_uuids) override;

  Status TestOnlyGetOrLoadDirtyBlockInfoProtoIfMissing(
      const BIPLockComponents& blk_lck_comps,
      BlockID blk_id,
      bool is_creating,
      DirtyBlockInfoProto** dbip);

 private:
  Status GetOrLoadDirtyBlockInfoProtoIfMissing(
      const BIPLockComponents& blk_lck_comps,
      BlockID blk_id,
      bool is_creating,
      DirtyBlockInfoProto** dbip);

  bool ChoosePrimaryDn4BlockRecovery(BlockInfoProto* bip,
                                     std::string* primary_dn_uuid);

  Status UploadHdfsBlock(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk_from_dn,
      const std::string& dn_uuid,
      const std::string& pufs_name,
      const std::string& upload_id,
      cloudfs::datanode::UploadCommandProto* upload_cmd,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);
  Status UploadAccBlock(const BIPLockComponents& blk_lck_comps,
                        const Block& blk_from_dn,
                        const std::string& dn_uuid,
                        const std::string& pufs_name,
                        const std::string& upload_id,
                        cloudfs::datanode::UploadCommandProto* upload_cmd,
                        cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);
  Status PersistHdfsBlock(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk_from_dn,
      const std::string& dn_uuid,
      const std::string& pufs_name,
      const std::string& upload_id,
      cloudfs::datanode::UploadCommandProto* upload_cmd,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);
  Status PersistAccBlock(
      const BIPLockComponents& blk_lck_comps,
      const Block& blk_from_dn,
      const std::string& dn_uuid,
      const std::string& pufs_name,
      const std::string& upload_id,
      const absl::optional<std::string>& etag,
      cloudfs::datanode::UploadCommandProto* upload_cmd,
      cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd);

 private:
  std::string blockpool_id_;
  std::shared_ptr<MetaStorage> meta_storage_;
  std::shared_ptr<DatanodeManager> datanode_manager_;

  std::vector<DirtyBlockInfoProtoBucket> buckets_;
  uint32_t bucket_mask_;
};

}  // namespace dancenn

#endif  // BLOCK_MANAGER_DIRTY_BLOCK_INFO_PROTO_MANAGER_H_
