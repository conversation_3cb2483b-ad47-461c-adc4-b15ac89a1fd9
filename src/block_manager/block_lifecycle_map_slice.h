#ifndef BLOCK_LIFECYCLE_MAP_SLICE_H
#define BLOCK_LIFECYCLE_MAP_SLICE_H

#include "base/metric.h"
#include "base/metrics.h"
#include "base/databus.h"
#include "hdfs.pb.h"

namespace dancenn {
using cloudfs::ReplicaStateProto;
using BlockID = uint64_t;

struct TransferBlockRecord {
  uint64_t blk_id_;
  uint64_t gs_;
  uint64_t transfer_start_timestamp_;
  // Block Recover or Block Transfer
  std::string src_dn_;
  std::string dst_dn_;

  TransferBlockRecord(uint64_t blk_id, uint64_t gs, std::string &src_dn,
                      std::string &dst_dn)
      : blk_id_(blk_id), gs_(gs), src_dn_(src_dn), dst_dn_(dst_dn) {
    transfer_start_timestamp_ =
        std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
  }
};

class BlockLifeCycleMapSlice {
 public:
  BlockLifeCycleMapSlice(MetricID metricId): abandon_log_count_(metricId) {}

  std::vector<std::string> &GetRecordsToSend();

  void LogTransferBlockRecord(TransferBlockRecord &transfer_record);

  void ScanTimeoutTransferCmd();

  void LogIncrementalBlockReport(uint64_t blk_id, const std::string &dn,
                                 uint64_t gs, const std::string &op,
                                 uint32_t numbytes,
                                 std::string &replica_status);

  void EnqueueLog(const char *fmt, ...);

  void lock_shared() { rwlock_.lock_shared(); }

  void unlock_shared() { rwlock_.unlock_shared(); }

  void lock() { rwlock_.lock(); }

  void unlock() { rwlock_.unlock(); }

 private:
  MetricID  abandon_log_count_;
  mutable std::shared_timed_mutex rwlock_;
  std::vector<std::string> records_to_send_;
  std::unordered_map<BlockID, std::vector<TransferBlockRecord>> pending_transfer_cmd_;
};

}  // namespace dancenn

#endif  // BLOCK_LIFECYCLE_MAP_SLICE_H
