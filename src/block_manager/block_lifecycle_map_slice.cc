#include <cnetpp/concurrency/thread_pool.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <shared_mutex>
#include <unordered_map>
#include <vector>
#include "block_manager/block_lifecycle_map_slice.h"
#include <iostream>

DECLARE_string(nameservice);
DECLARE_string(nn_local_ip);
DECLARE_int32(block_lifecycle_num_slice);
DECLARE_int32(transfer_timeout_threshold_sec);
DECLARE_int32(flush_blocklifecycle_interval_ms);

namespace dancenn {
using cloudfs::ReplicaStateProto;

#define DANCENN_BLOCKLIFECYCLE_LOG(fmt, ...)                                         \
  EnqueueLog(("{\"collect_time\": %" PRIu64 ", \"nameservice\": \"%s\", \"nn_ip\": \"%s\", " fmt),        \
             std::chrono::duration_cast<std::chrono::milliseconds>(                  \
                 std::chrono::system_clock::now().time_since_epoch())                \
                 .count(),                                                           \
             FLAGS_nameservice.c_str(), FLAGS_nn_local_ip.c_str(),##__VA_ARGS__);

std::vector<std::string>& BlockLifeCycleMapSlice::GetRecordsToSend() {
  return records_to_send_;
}

void BlockLifeCycleMapSlice::ScanTimeoutTransferCmd() {
  uint64_t current_timestamp =
      std::chrono::duration_cast<std::chrono::seconds>(
          std::chrono::system_clock::now().time_since_epoch())
          .count();

  for (auto iter = pending_transfer_cmd_.begin();
       iter != pending_transfer_cmd_.end();) {
    std::vector<TransferBlockRecord>& records = iter->second;
    for (auto record_iter = records.begin(); record_iter != records.end();) {
      // Clean timeout commands.
      if (current_timestamp - record_iter->transfer_start_timestamp_ >
          FLAGS_transfer_timeout_threshold_sec) {
        record_iter = records.erase(record_iter);
      } else {
        record_iter++;
      }
    }

    // If no transfer command is related to this blk_id. Clean this entry in
    // pending_transfer_cmd_.
    if (records.size() == 0) {
      iter = pending_transfer_cmd_.erase(iter);
    } else {
      iter++;
    }
  }
}

void BlockLifeCycleMapSlice::EnqueueLog(const char* fmt, ...) {
  static const int kBufferLength = 2048;
  std::string buf(kBufferLength, 0);
  va_list ap;
  va_start(ap, fmt);

  int need = vsnprintf(&(buf[0]), kBufferLength, fmt, ap);
  if (need > kBufferLength) {
    buf.resize(need + 1, 0);
    va_list dups2_ap;
    va_start(dups2_ap, fmt);
    need = vsnprintf(&(buf[0]), need + 1, fmt, dups2_ap);
    va_end(dups2_ap);
  }
  va_end(ap);

  if (need != -1) {
    buf.resize(need);
    if (records_to_send_.size() > 10000) {
      // Too many logs not sent.
      // Should additional log be abandoned, in order not to occupy too many memory？
      MFC(abandon_log_count_)->Inc();
    } else {
      records_to_send_.emplace_back(std::move(buf));
    }
  }
}

void BlockLifeCycleMapSlice::LogTransferBlockRecord(
    dancenn::TransferBlockRecord& transfer_record) {
  auto blk_id = transfer_record.blk_id_;
  auto iter = pending_transfer_cmd_.find(blk_id);

  if (iter != pending_transfer_cmd_.end()) {
    std::vector<TransferBlockRecord>& records = iter->second;
    // Check if block_transfer_command for this blk has already exist before.
    for (auto record_iter = records.begin(); record_iter != records.end();) {
      // Regard the previous command as timeout or failed.
      if (record_iter->dst_dn_ == transfer_record.dst_dn_) {
        record_iter = records.erase(record_iter);
      } else {
        record_iter++;
      }
    }

    records.push_back(transfer_record);
  } else {
    // No record for this blk.
    pending_transfer_cmd_.emplace(std::make_pair(
        blk_id, std::vector<TransferBlockRecord>{transfer_record}));
  }


  DANCENN_BLOCKLIFECYCLE_LOG(
      "\"blk_id\":%" PRIu64 ","
      "\"gs\":%" PRIu64 ","
      "\"op\":\"transfer_blk\","
      "\"transfer_src_dn\":\"%s\","
      "\"transfer_dst_dn\":\"%s\"}",
      transfer_record.blk_id_, transfer_record.gs_,
      transfer_record.src_dn_.c_str(), transfer_record.dst_dn_.c_str());
}

void BlockLifeCycleMapSlice::LogIncrementalBlockReport(
    uint64_t blk_id, const std::string& dn, uint64_t gs, const std::string& op,
    uint32_t numbytes, std::string& replica_status) {
  // Calculate transfer block speed.
  // When a transfer block success. There may be multi transfer command for this
  // blk. We take the latest transfer_block_command for the start timestamp of
  // block-transfer.
  auto iter = pending_transfer_cmd_.find(blk_id);
  if (iter != pending_transfer_cmd_.end()) {
    uint64_t current_timestamp =
        std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();

    std::vector<TransferBlockRecord>& records = iter->second;
    for (auto record_iter = records.begin(); record_iter != records.end();) {
      if (record_iter->dst_dn_ == dn && record_iter->gs_ == gs) {
        DANCENN_BLOCKLIFECYCLE_LOG(
            "\"blk_id\":%" PRIu64 ","
            "\"gs\":%" PRIu64 ","
            "\"numbytes\":%d,"
            "\"op\":\"transfer_blk_success\","
            "\"transfer_src_dn\":\"%s\","
            "\"transfer_dst_dn\":\"%s\","
            "\"start_timestamp\":%d,"
            "\"end_timestamp\":%d}",
            record_iter->blk_id_, record_iter->gs_, numbytes,
            record_iter->src_dn_.c_str(), record_iter->dst_dn_.c_str(),
            record_iter->transfer_start_timestamp_, current_timestamp);

        record_iter = records.erase(record_iter);
      } else {
        record_iter++;
      }
    }

    if (records.size() == 0) {
      // All pending records has benn processed
      pending_transfer_cmd_.erase(blk_id);
    }
  }

  // Just log the block report itself, which is part of the blocklifecycle.
  DANCENN_BLOCKLIFECYCLE_LOG(
      "\"blk_id\":%" PRIu64 ","
      "\"gs\":%" PRIu64 ","
      "\"numbytes\":%d,"
      "\"op\":\"%s\","
      "\"report_dn_ip\":\"%s\","
      "\"replica_status\":\"%s\"}",
      blk_id, gs, numbytes, op.c_str(), dn.c_str(), replica_status.c_str());
}

}  // namespace dancenn