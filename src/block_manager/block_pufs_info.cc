// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/05/10
// Description

#include "block_manager/block_pufs_info.h"

#include <gflags/gflags.h>

#include <utility>

#include "base/pb_converter.h"

DECLARE_int32(dfs_replication);

namespace dancenn {
namespace {

const char* kBpidKey = "block_pool_id";
const char* kBlockIdKey = "block_id";
const char* kGenStampKey = "gen_stamp";
const char* kNumBytesKey = "num_bytes";
const char* kINodeIdKey = "inode_id";
const char* kStateKey = "state";
const char* kPufsNameKey = "pufs_name";
const char* kUploadIssuedTimesKey = "upload_issued_times_";
const char* kDnUuidKey = "dn_uuid";
const char* kAbortedUploadIdsKey = "aborted_upload_ids";
const char* kUploadIdKey = "upload_id";
const char* kNnExpTsKey = "nn_exp_ts";
const char* kDnExpTsKey = "dn_exp_ts";

}  // namespace

std::ostream& operator<<(std::ostream& ss, const BlockPufsState& state) {
  ss << static_cast<int8_t>(state);
  return ss;
}

#define PARSE_STRING(key, field)                                    \
  if ((it = json_obj.Find(key)) == end || !it->second.IsString()) { \
    return false;                                                   \
  }                                                                 \
  (field) = it->second.AsString();

#define PARSE_INTEGER(key, field)                                     \
  if ((it = json_obj.Find(key)) == end || !it->second.IsIntegral()) { \
    return false;                                                     \
  }                                                                   \
  (field) = it->second.AsInteger();

cnetpp::base::Value BlockPufsInfo::SerializeToJson() const {
  cnetpp::base::Object json_obj;
  json_obj[kBpidKey] = block_pool_id_;
  json_obj[kBlockIdKey] = block_id_;
  json_obj[kGenStampKey] = gen_stamp_;
  json_obj[kNumBytesKey] = num_bytes_;
  json_obj[kINodeIdKey] = inode_id_;
  json_obj[kStateKey] = static_cast<int>(state_);
  json_obj[kPufsNameKey] = pufs_name_;
  json_obj[kUploadIssuedTimesKey] = upload_issued_times_;
  json_obj[kDnUuidKey] = dn_uuid_;
  cnetpp::base::Array aborted_upload_ids;
  for (const auto& aborted_upload_id : aborted_upload_ids_) {
    aborted_upload_ids.Append(cnetpp::base::Value(aborted_upload_id));
  }
  json_obj[kAbortedUploadIdsKey] = aborted_upload_ids;
  json_obj[kUploadIdKey] = upload_id_;
  json_obj[kNnExpTsKey] = nn_exp_ts_;
  json_obj[kDnExpTsKey] = dn_exp_ts_;
  return cnetpp::base::Value(std::move(json_obj));
}

std::string BlockPufsInfo::SerializeToJsonString() const {
  return cnetpp::base::Parser::Serialize(SerializeToJson());
}

bool BlockPufsInfo::DeserializeFromJson(const cnetpp::base::Value& json_value) {
  if (!json_value.IsObject()) {
    return false;
  }
  cnetpp::base::Object json_obj = json_value.AsObject();
  auto end = json_obj.End();
  auto it = end;
  // Note(ruanjunbin): If you add a new field, please make it optional.
  PARSE_STRING(kBpidKey, block_pool_id_);
  PARSE_INTEGER(kBlockIdKey, block_id_);
  PARSE_INTEGER(kGenStampKey, gen_stamp_);
  PARSE_INTEGER(kNumBytesKey, num_bytes_);
  PARSE_INTEGER(kINodeIdKey, inode_id_);

  int state = 0;
  PARSE_INTEGER(kStateKey, state);
  state_ = static_cast<BlockPufsState>(state);
  PARSE_STRING(kPufsNameKey, pufs_name_);

  PARSE_INTEGER(kUploadIssuedTimesKey, upload_issued_times_);
  PARSE_STRING(kDnUuidKey, dn_uuid_);
  if ((it = json_obj.Find(kAbortedUploadIdsKey)) == end ||
      !it->second.IsArray()) {
    return false;
  }
  cnetpp::base::Array aborted_upload_ids = it->second.AsArray();
  for (auto it = aborted_upload_ids.Begin(); it != aborted_upload_ids.End();
       it++) {
    if (!it->IsString()) {
      return false;
    }
    aborted_upload_ids_.emplace(it->AsString());
  }
  PARSE_STRING(kUploadIdKey, upload_id_);
  PARSE_INTEGER(kNnExpTsKey, nn_exp_ts_);
  PARSE_INTEGER(kDnExpTsKey, dn_exp_ts_);
  return true;
}

bool BlockPufsInfo::DeserializeFromJsonString(const std::string& json_string) {
  cnetpp::base::Value json_value;
  if (!cnetpp::base::Parser::Deserialize(json_string, &json_value)) {
    return false;
  }
  return DeserializeFromJson(json_value);
}

#undef PARSE_STRING
#undef PARSE_INTEGER

UploadCmd BlockPufsInfo::GetUploadCmd() const {
  UploadCmd cmd;
  cmd.set_dnuuid(dn_uuid_);
  cmd.set_blockpoolid(block_pool_id_);
  cmd.mutable_block()->set_blockid(block_id_);
  cmd.mutable_block()->set_genstamp(gen_stamp_);
  cmd.mutable_block()->set_numbytes(num_bytes_);
  cmd.set_blockpufsname(pufs_name_);
  cmd.set_expts(dn_exp_ts_);
  cmd.set_uploadid(upload_id_);
  for (const auto& aborted_upload_id : aborted_upload_ids_) {
    cmd.add_aborteduploadids(aborted_upload_id);
  }
  return cmd;
}

NotifyEvictableCmd BlockPufsInfo::GetNotifyEvictableCmd() const {
  NotifyEvictableCmd cmd;
  cmd.set_blockpoolid(block_pool_id_);
  cmd.mutable_block()->set_blockid(block_id_);
  cmd.mutable_block()->set_genstamp(gen_stamp_);
  cmd.mutable_block()->set_numbytes(num_bytes_);
  return cmd;
}

bool BlockPufsInfo::FromBlockInfoProto(const std::string& blockpool_id,
                                       const BlockInfoProto& bip) {
  block_pool_id_ = blockpool_id;
  block_id_ = bip.block_id();
  gen_stamp_ = bip.gen_stamp();
  num_bytes_ = bip.num_bytes();
  inode_id_ = bip.inode_id();
  switch (bip.state()) {
    case BlockInfoProto::kUnderConstruction:
      state_ = BlockPufsState::kLocal;
      break;
    case BlockInfoProto::kCommitted:
    case BlockInfoProto::kComplete:
      state_ = BlockPufsState::kLocal;
      break;
    case BlockInfoProto::kUploadIssued:
      state_ = BlockPufsState::kUploadIssued;
      break;
    case BlockInfoProto::kPersisted:
      state_ = BlockPufsState::kPersisted;
      break;
    case BlockInfoProto::kDeprecated:
      state_ = BlockPufsState::kDeprecated;
      break;
    default:
      LOG(ERROR) << "UnexpectedBlock " << PBConverter::ToCompactJsonString(bip);
      return false;
  }
  pufs_name_ = bip.has_pufs_name() ? bip.pufs_name() : "";
  upload_issued_times_ =
      bip.has_upload_issued_times() ? bip.upload_issued_times() : 0;
  dn_uuid_ = bip.dn_uuid();
  for (const std::string& aborted_upload_id : bip.aborted_upload_ids()) {
    aborted_upload_ids_.insert(aborted_upload_id);
  }
  upload_id_ = bip.has_curr_upload_id() ? bip.curr_upload_id() : "";
  nn_exp_ts_ = bip.has_nn_exp_ts() ? bip.nn_exp_ts() : 0;
  dn_exp_ts_ = bip.has_dn_exp_ts() ? bip.dn_exp_ts() : 0;
  return true;
}

BlockInfoProto BlockPufsInfo::GetBlockInfoProto() const {
  BlockInfoProto bip;
  // BlockPufsInfo is deprecated, the edit log applier is the only caller.
  // So the return value is always true.
  CHECK(GetBlockInfoProto(&bip));
  return bip;
}

bool BlockPufsInfo::GetBlockInfoProto(BlockInfoProto* bip) const {
  CHECK_NOTNULL(bip);
  switch (state_) {
    case BlockPufsState::kUploadIssued:
      bip->set_state(BlockInfoProto::kUploadIssued);
      break;
    case BlockPufsState::kPersisted:
      bip->set_state(BlockInfoProto::kPersisted);
      break;
    case BlockPufsState::kDeprecated:
      bip->set_state(BlockInfoProto::kDeprecated);
      break;
    case BlockPufsState::kLocal:
    case BlockPufsState::kUploadFailed:
    case BlockPufsState::kDeleted:
    default:
      LOG(ERROR) << "UnexpectedBlock " << SerializeToJsonString();
      return false;
  }
  bip->set_block_id(block_id_);
  bip->set_gen_stamp(gen_stamp_);
  bip->set_num_bytes(num_bytes_);
  bip->set_inode_id(inode_id_);
  bip->set_expected_rep(FLAGS_dfs_replication);
  bip->set_pufs_name(pufs_name_);
  bip->set_upload_issued_times(upload_issued_times_);
  for (const std::string& aborted_upload_id : aborted_upload_ids_) {
    *bip->add_aborted_upload_ids() = aborted_upload_id;
  }
  bip->set_curr_upload_id(upload_id_);
  bip->set_dn_uuid(dn_uuid_);
  bip->set_nn_exp_ts(nn_exp_ts_);
  bip->set_dn_exp_ts(dn_exp_ts_);
  return true;
}

}  // namespace dancenn
