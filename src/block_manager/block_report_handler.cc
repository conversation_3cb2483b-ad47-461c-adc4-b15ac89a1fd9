// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/09/21
// Description

#include "block_manager/block_report_handler.h"

#include <absl/strings/str_format.h>         // For StrFormat.
#include <gflags/gflags.h>                   // For DECLARE_uint32, etc.
#include <glog/logging.h>                    // For CHECK, LOG.
#include <google/protobuf/io/gzip_stream.h>  // For GzipInputStream.
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>  // For ArrayInputStream.
#include <proto/generated/dancenn/edit_log.pb.h>  // For BlockInfoProtos.

#include <algorithm>  // For min.
#include <atomic>     // For atomic.
#include <limits>
#include <mutex>      // For std::once_flag, std::call_once.
#include <set>        // For set.
#include <utility>    // For move.

#include "base/defer.h"                                  // For DEFER.
#include "base/java_exceptions.h"                        // For JavaExceptions.
#include "base/logger_metrics.h"                         // For LoggerMetrics.
#include "base/metrics.h"                                // For MFC.
#include "base/stop_watch.h"                             // For StopWatch.
#include "base/vlock.h"                                  // For vshared_lock.
#include "block_manager/block_report_handler_metrics.h"  // For BlockReportHandlerMetrics.
#include "namespace/namespace.h"                         // For NameSpace.
#include "proto/generated/cloudfs/lifecycle.pb.h"  // For StorageClassProto.

DECLARE_int32(upload_cmds_max_size);
DECLARE_int32(ne_cmds_max_size);
DECLARE_int32(br_cmds_max_size);
DECLARE_int32(load_cmds_max_size);
DECLARE_int32(namespace_type);
DECLARE_bool(dancenn_observe_mode_on);

DECLARE_int32(max_ongoing_block_report_req_count);
DECLARE_int32(max_ongoing_block_report_req_count_hard_limit);
DECLARE_uint32(block_report_delta_set_max_size);
DECLARE_uint64(block_report_interval_sec);
DECLARE_uint64(block_report_fast_fbr_interval_sec);
DECLARE_uint64(block_report_window_sec);
DECLARE_uint64(block_report_scan_interval_sec);
DECLARE_uint64(block_report_batch_interval_ms);
DECLARE_uint64(block_report_batch_size);
DECLARE_uint64(block_report_fast_batch_size);

DECLARE_int32(blk_report_thread_count);
DECLARE_int32(process_block_report_batch_size);
DECLARE_bool(ha_async_run_switch_task);

namespace dancenn {

BlockReportHandler::BlockReportHandler(
    HAStateBase* ha_state,
    std::shared_ptr<EditLogSenderBase> edit_log_sender,
    std::shared_ptr<MetaStorage> meta_storage,
    BlockManager* block_manager,
    BIPWriteManager* bip_write_manager)
    : ha_state_(ha_state),
      edit_log_sender_(edit_log_sender),
      meta_storage_(meta_storage),
      block_manager_(block_manager),
      bip_write_manager_(bip_write_manager) {
  CHECK(ha_state_);
  // CHECK(edit_log_sender_);
  // block_manager_ == nullptr when running uts.
  // CHECK(block_manager_);
  // CHECK(meta_storage_);
  // CHECK(bip_write_manager_);
}

Status BlockReportHandler::IncrementalBlockReport(
    const std::string& dn_uuid,
    const google::protobuf::RepeatedPtrField<
        cloudfs::datanode::StorageReceivedDeletedBlocksProto>& report,
    const std::function<
        void(std::vector<cloudfs::datanode::UploadCommandProto>&&)>&
        process_upload_cmds,
    const std::function<
        void(std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&)>&
        process_ne_cmds) {
  StopWatch ibr_sw(BlockReportHandlerMetrics::Instance().ibr_total_time_);
  if (!ha_state_->IsActive() || ha_state_->InTransition()) {
    return Status(JavaExceptions::kStandbyException,
                  "Skip IncrementalBlockReport due to standby");
  }
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    const char* msg =
        "Skip IncrementalBlockReport due to in transition to standby";
    LOG(INFO) << msg;
    return Status(JavaExceptions::kStandbyException, msg);
  }
  CHECK(edit_log_sender_);
  CHECK(bip_write_manager_);
  // These two CHECK statements will not fail. Because:
  //
  //    !HAState::CheckOperation(kWrite).first.HasException()
  // => HAState::CheckOperation(kWrite).second.owns_lock()
  // => HAState::SetState can not take unique lock of HAState::barrier_.
  // => HAState::SetState can not call SetStateInternal(cloudfs::STANDBY).
  // => BlockReportManager::StopActive has not been called.
  //
  //    !HAState::CheckOperation(kWrite).first.HasException()
  // =>    HAState::ha_state_ == cloudfs::ACTIVE
  //    && No one is taking unique lock of HAState::barrier_.
  // => NameSpace::StartActive has been called.
  // => BlockReportManager::StartActive has been called.
  //
  // Then, we have:
  //    !HAState::CheckOperation(kWrite).first.HasException()
  // => edit_log_sender_ != nullptr && meta_storage_ != nullptr && ...

  for (const auto& storage : report) {
    ProcessIncrementalBlockReportPerStorage(
        dn_uuid, storage, process_upload_cmds, process_ne_cmds);
  }
  return Status();
}

void BlockReportHandler::ProcessIncrementalBlockReportPerStorage(
    const std::string& dn_uuid,
    const cloudfs::datanode::StorageReceivedDeletedBlocksProto& storage,
    const std::function<
        void(std::vector<cloudfs::datanode::UploadCommandProto>&&)>&
        process_upload_cmds,
    const std::function<
        void(std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&)>&
        process_ne_cmds) {
  StopWatch storage_sw(BlockReportHandlerMetrics::Instance().ibr_storage_time_);
  int32_t partition_size =
      (storage.blocks_size() + (FLAGS_process_block_report_batch_size - 1)) /
      FLAGS_process_block_report_batch_size;
  std::vector<cloudfs::datanode::UploadCommandProto> upload_cmds;
  std::vector<cloudfs::datanode::NotifyEvictableCommandProto> ne_cmds;
  CountDownLatch latch(partition_size);
  for (int32_t partition = 0; partition < partition_size; partition++) {
    // [begin, end)
    int32_t begin = partition * FLAGS_process_block_report_batch_size;
    int32_t end =
        std::min((partition + 1) * FLAGS_process_block_report_batch_size,
                 storage.blocks_size());
    ProcessIncrementalBlockReportPerPartition(
        dn_uuid, storage, begin, end, &upload_cmds, &ne_cmds, &latch);
  }
  // If it waits for async, this code can perform better.
  // Improve it when needed.
  latch.Await();
  process_upload_cmds(std::move(upload_cmds));
  process_ne_cmds(std::move(ne_cmds));
}

void BlockReportHandler::ProcessIncrementalBlockReportPerPartition(
    const std::string& dn_uuid,
    const cloudfs::datanode::StorageReceivedDeletedBlocksProto& storage,
    // [begin, end)
    int32_t begin,
    int32_t end,
    std::vector<cloudfs::datanode::UploadCommandProto>* upload_cmds,
    std::vector<cloudfs::datanode::NotifyEvictableCommandProto>* ne_cmds,
    CountDownLatch* latch) {
  std::unique_ptr<CountDownLatch, std::function<void(CountDownLatch*)>>
      latch_guard(latch, [](CountDownLatch* l) { l->CountDown(); });
  if (!ha_state_->IsActive() || ha_state_->InTransition()) {
    LOG(INFO) << "Skip incremental block report due to standby";
    return;
  }
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (!cres.first.IsOK()) {
    LOG(INFO)
        << "Skip incremental block report due to in transition to standby";
    return;
  }

  std::set<BlockID> blk_ids;
  for (int32_t i = begin; i < end; i++) {
    blk_ids.emplace(storage.blocks(i).block().blockid());
  }
  BIPLockComponents blk_lck_comps =
      bip_write_manager_->Lock(blk_ids, __FILE__, __LINE__);

  BlockInfoProtos bips;
  for (int32_t i = begin; i < end; i++) {
    const auto& b = storage.blocks(i);
    Block blk(b.block().blockid(),
              static_cast<uint32_t>(b.block().numbytes()),
              b.block().genstamp());
    switch (b.status()) {
      using cloudfs::ReplicaStateProto;
      using cloudfs::datanode::ReceivedDeletedBlockInfoProto;
      case ReceivedDeletedBlockInfoProto::RECEIVING: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_receiving_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_receiving_time_);
        if (bip_write_manager_
                ->ReportExistedReplica(
                    blk_lck_comps, blk, dn_uuid, ReplicaStateProto::RBW)
                .IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
      } break;
      case ReceivedDeletedBlockInfoProto::RECEIVED: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_received_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_received_time_);
        if (bip_write_manager_
                ->ReportExistedReplica(
                    blk_lck_comps, blk, dn_uuid, ReplicaStateProto::FINALIZED)
                .IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
      } break;
      case ReceivedDeletedBlockInfoProto::DELETED: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_deleted_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_deleted_time_);
        if (bip_write_manager_
                ->ReportDeletedReplica(blk_lck_comps, blk, dn_uuid)
                .IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
      } break;
      case ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_upload_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_upload_time_);
        cloudfs::datanode::UploadCommandProto upload_cmd;
        cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
        Status s = bip_write_manager_->UploadBlock(blk_lck_comps,
                                                   blk,
                                                   dn_uuid,
                                                   b.pufsname(),
                                                   b.uploadid(),
                                                   &upload_cmd,
                                                   &ne_cmd);
        if (s.IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
        CHECK(!(upload_cmd.IsInitialized() && ne_cmd.IsInitialized()));
        if (upload_cmd.IsInitialized()) {
          CHECK(s.IsOK() || s.code() == Code::kIsRetry);
          upload_cmds->emplace_back();
          upload_cmds->back().Swap(&upload_cmd);
        }
        if (ne_cmd.IsInitialized()) {
          CHECK(s.IsOK() || s.code() == Code::kIsRetry);
          ne_cmds->emplace_back();
          ne_cmds->back().Swap(&ne_cmd);
        }
      } break;
      case ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_persist_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_persist_time_);
        cloudfs::datanode::UploadCommandProto upload_cmd;
        cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
        Status s = bip_write_manager_->PersistBlock(
            blk_lck_comps,
            blk,
            dn_uuid,
            b.pufsname(),
            b.uploadid(),
            b.has_etag() ? absl::make_optional<std::string>(b.etag())
                         : absl::nullopt,
            &upload_cmd,
            &ne_cmd);
        if (s.IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
        if (upload_cmd.IsInitialized()) {
          CHECK(s.IsOK() || s.code() == Code::kIsRetry);
          upload_cmds->emplace_back();
          upload_cmds->back().Swap(&upload_cmd);
        }
        if (ne_cmd.IsInitialized()) {
          CHECK(s.IsOK() || s.code() == Code::kIsRetry);
          ne_cmds->emplace_back();
          ne_cmds->back().Swap(&ne_cmd);
        }
      } break;
      case ReceivedDeletedBlockInfoProto::PUFS_DELETED: {
        MFC(BlockReportHandlerMetrics::Instance().ibr_pufs_deleted_ops_)->Inc();
        StopWatch blk_sw(
            BlockReportHandlerMetrics::Instance().ibr_pufs_deleted_time_);
        Status s = bip_write_manager_->DeleteBlock(blk_lck_comps, blk);
        if (s.IsOK()) {
          BlockInfoProto bip;
          if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip)
                  .IsOK()) {
            bips.add_content()->Swap(&bip);
          }
        }
      } break;
      case ReceivedDeletedBlockInfoProto::REPORT_STORAGE_CLASS: {
        // TODO(xuexiang.xx)
        LOG(INFO) << "Drop REPORT_STORAGE_CLASS in bipv2";
      } break;
      default: {
        LOG(ERROR) << "Meet unknown status in incremental block report: "
                   << b.status();
        MFC(LoggerMetrics::Instance().error_)->Inc();
      } break;
    }
  }
  bips.set_trigger(dn_uuid);

  if (bips.content_size() != 0) {
    int64_t txid = edit_log_sender_->LogFlushBlockInfoProtos(bips);
    // Releasing locks at here is safe.
    blk_lck_comps.Reset();
    latch_guard.release();
    RpcClosure* done = new RpcClosure(
        [this, latch, blk_ids = std::move(blk_ids), bips = std::move(bips)](
            const Status& ignored) {
          {
            BIPLockComponents blk_lck_comps =
                bip_write_manager_->Lock(blk_ids, __FILE__, __LINE__);
            for (const BlockInfoProto& bip : bips.content()) {
              bip_write_manager_->PostCommit(blk_lck_comps, bip);
            }
          }
          for (const BlockInfoProto& bip : bips.content()) {
            if (bip.state() == BlockInfoProto::kPersisted &&
                block_manager_ != nullptr) {
              BlockUCState uc_state =
                  block_manager_->GetBlockUCState(bip.block_id());
              if (uc_state != BlockUCState::kPersisted) {
                if (!block_manager_->PersistBlock(Block(
                        bip.block_id(), bip.num_bytes(), bip.gen_stamp()))) {
                  LOG(ERROR) << "Call BlockManager::PersistBlock failed for B"
                             << bip.block_id()
                             << ", uc_state: " << static_cast<int>(uc_state);
                  MFC(LoggerMetrics::Instance().error_)->Inc();
                } else {
                  VLOG(15) << "Call BlockManager::PersistBlock succeed";
                }
              }
            }
          }
          latch->CountDown();
        },
        true);
    done->set_barrier(std::move(cres.second));
    meta_storage_->FlushBlockInfoProtos(bips, txid, done);
  }
}

// Refer to BlockManager::DecodeBlocks.
Status BlockReportHandler::DecodeStorageBlockReportProto(
    const std::string& dn_uuid,
    const cloudfs::datanode::StorageBlockReportProto& raw_report,
    std::vector<BlkInfo>* decoded_report) {
  switch (raw_report.blocksformatversion()) {
    case cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1: {
      return DecodeStorageBlockReportProtoV1(
          dn_uuid, raw_report.blocks(), decoded_report);
    } break;
    case cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V2: {
      return DecodeStorageBlockReportProtoV2(
          dn_uuid, raw_report.blocksv2(), decoded_report);
    } break;
  }
  std::string msg = absl::StrFormat("Unexpected block format %d in FBR %s",
                                    raw_report.blocksformatversion(),
                                    raw_report.ShortDebugString());
  LOG(ERROR) << msg;
  MFC(LoggerMetrics::Instance().error_)->Inc();
  return Status(JavaExceptions::kInvalidRequestException, Code::kError, msg);
}

// Refer to BlockManager::DecodeBlocksV1.
Status BlockReportHandler::DecodeStorageBlockReportProtoV1(
    const std::string& dn_uuid,
    const google::protobuf::RepeatedField<uint64_t>& raw_report,
    std::vector<BlkInfo>* decoded_report) {
  CHECK_NOTNULL(decoded_report);
  if (raw_report.size() == 0) {
    return Status();
  }
  // +------------------+---------------------+-----+-----+-------+
  // | meta             | finalize_num        |     |     |       |
  // |                  +---------------------+-----+-----+-------+
  // |                  | under_construct_num |     |     |       |
  // +------------------+---------------------+-----+-----+-------+
  // | finalized_blocks | id                  | len | gs  |       |
  // |                  +---------------------+-----+-----+-------+
  // |                  | ...                 | ... | ... |       |
  // |                  +---------------------+-----+-----+-------+
  // |                  | id                  | len | gs  |       |
  // +------------------+---------------------+-----+-----+-------+
  // | delimiter        | -1                  | -1  | -1  |       |
  // +------------------+---------------------+-----+-----+-------+
  // | uc_blocks        | id                  | len | gs  | state |
  // |                  +---------------------+-----+-----+-------+
  // |                  | ...                 | ... | ... | ...   |
  // |                  +---------------------+-----+-----+-------+
  // |                  | id                  | len | gs  | state |
  // +------------------+---------------------+-----+-----+-------+
  // See BlockListAsLongs::Builder::Build for details.
  if (raw_report.size() < 2) {
    std::string msg = absl::StrFormat(
        "Size of raw_report from %s is %d", dn_uuid, raw_report.size());
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  uint64_t finalize_num = raw_report.Get(0);
  uint64_t uc_num = raw_report.Get(1);
  uint64_t delimiter_off = 2 + finalize_num * 3;
  if (!(raw_report.size() >= 2 + finalize_num * 3 +
                                 /*delimiter_num=*/1 * 3 + uc_num * 4 &&
        (raw_report.Get(delimiter_off + 0) == -1 &&
         raw_report.Get(delimiter_off + 1) == -1 &&
         raw_report.Get(delimiter_off + 2) == -1))) {
    std::string msg = absl::StrFormat(
        "Invalid full block report from %s, "
        "report.blocks_size():%d, finalize_num:%d, uc_num:%d, "
        "delimiter:{%d,%d,%d}",
        dn_uuid,
        raw_report.size(),
        finalize_num,
        uc_num,
        delimiter_off + 0 < raw_report.size()
            ? raw_report.Get(delimiter_off + 0)
            : 0,
        delimiter_off + 1 < raw_report.size()
            ? raw_report.Get(delimiter_off + 1)
            : 0,
        delimiter_off + 2 < raw_report.size()
            ? raw_report.Get(delimiter_off + 2)
            : 0);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }

  decoded_report->reserve(decoded_report->size() + finalize_num + uc_num);
  for (uint64_t i = 0; i < finalize_num; i++) {
    uint64_t offset = 2 + i * 3;
    StorageClassReportProto scr;
    scr.set_stcls(StorageClassProto::NONE);
    scr.set_pinned(false);
    decoded_report->emplace_back(
        BlkInfo{.id = raw_report.Get(offset),
                .gs = raw_report.Get(offset + 2),
                .len = raw_report.Get(offset + 1),
                .replica_state = cloudfs::ReplicaStateProto::FINALIZED,
                .storage_class_report = scr});
  }
  for (uint64_t i = 0; i < uc_num; i++) {
    uint64_t offset = 2 + (finalize_num + 1) * 3 + i * 4;
    StorageClassReportProto scr;
    scr.set_stcls(StorageClassProto::NONE);
    scr.set_pinned(false);
    decoded_report->emplace_back(BlkInfo{
        .id = raw_report.Get(offset),
        .gs = raw_report.Get(offset + 2),
        .len = raw_report.Get(offset + 1),
        .replica_state =
            static_cast<cloudfs::ReplicaStateProto>(raw_report.Get(offset + 3)),
        .storage_class_report = scr});
  }
  return Status();
}

// Refer to BlockManager::DecodeBlocksV2.
Status BlockReportHandler::DecodeStorageBlockReportProtoV2(
    const std::string& dn_uuid,
    const std::string& raw_report,
    std::vector<BlkInfo>* decoded_report) {
  CHECK_NOTNULL(decoded_report);
  google::protobuf::io::ArrayInputStream inputStream(raw_report.data(),
                                                     raw_report.size());
  google::protobuf::io::GzipInputStream gzipStream(&inputStream);
  cloudfs::datanode::BlockReportBlockInfoProtoV2 proto;
  if (!proto.ParseFromZeroCopyStream(&gzipStream)) {
    std::string msg = absl::StrFormat(
        "Failed to decode BlockReportBlockInfoProtoV2 from %s", dn_uuid);
    LOG(ERROR) << msg;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return Status(JavaExceptions::kIOException, msg);
  }
  for (const auto& blk : proto.blocks()) {
    StorageClassReportProto scr;
    scr.set_stcls(blk.storageclass());
    scr.set_pinned(blk.pinned());
    decoded_report->emplace_back(BlkInfo{.id = blk.block().blockid(),
                                         .gs = blk.block().genstamp(),
                                         .len = blk.block().numbytes(),
                                         .replica_state = blk.replicastate(),
                                         .storage_class_report = scr});
  }
  return Status();
}

// Almost copy from BlockManager::AsyncBlockReport.
Status BlockReportHandler::MakeFullBlockReportTasks(
    const std::string& dn_uuid,
    std::vector<BlkInfo>&& decoded_report,
    std::vector<FullBlockReportTask>* tasks) {
  CHECK_NOTNULL(tasks);
  // Prepare block report context.
  auto blk_report_ctx = std::make_shared<FullBlockReportContext>();
  // Currently, we need to copy report by value.
  // Copy it by reference if we can control lifecycle of rpc request.
  blk_report_ctx->dn_uuid = dn_uuid;
  blk_report_ctx->report = std::move(decoded_report);
  auto batch_size = FLAGS_process_block_report_batch_size;
  uint64_t task_cnt =
      (blk_report_ctx->report.size() + (batch_size - 1)) / batch_size;
  blk_report_ctx->ongoing_task_cnt = task_cnt;
  // After removing BlockManager, done will be set to
  // FullBlockReportCallback. It will call the following functions:
  // 1. ProcessFailedStorages.
  // 2. BlockDiff.
  // 3. SendResponseToDN.
  // See BlockManager::AsyncBlockReport for more infos.
  blk_report_ctx->done = new RpcClosure(
      [this, blk_report_ctx](const Status& ignored) {
        return FullBlockReportCallback(blk_report_ctx);
      },
      true);
  blk_report_ctx->sw.RecordAndNextStep();
  tasks->reserve(tasks->size() + task_cnt);
  for (uint64_t i = 0; i < task_cnt; i++) {
    tasks->emplace_back(FullBlockReportTask{
        .context = blk_report_ctx,
        .from_idx = i * batch_size,
        .to_idx =
            std::min((i + 1) * batch_size, blk_report_ctx->report.size())});
    CHECK_LE(tasks->back().from_idx, tasks->back().to_idx);
  }
  return Status();
}

// Almost copy from BlockManager::ProcessBlockReport.
Status BlockReportHandler::ExecuteFullBlockReportTask(
    FullBlockReportTask task) {
  task.sw.RecordAndNextStep(
      BlockReportHandlerMetrics::Instance().fbr_per_task_wait_execute_time_);
  std::unique_ptr<FullBlockReportTask,
                  std::function<void(FullBlockReportTask*)>>
      task_guard(&task, [](FullBlockReportTask* task) {
        if (task->context->ongoing_task_cnt.fetch_sub(1) == 1) {
          if (task->context->done) {
            task->context->done->Run();
          }
        }
      });
  if (!ha_state_->IsActive() || ha_state_->InTransition()) {
    return Status(JavaExceptions::kStandbyException);
  }
  auto cres = ha_state_->CheckOperation(OperationsCategory::kWrite);
  if (cres.first.HasException()) {
    LOG(INFO) << "Skip IncrementalBlockReport due to in transition to standby";
    return Status(JavaExceptions::kStandbyException);
  }
  CHECK(edit_log_sender_);
  CHECK(bip_write_manager_);
  // These two CHECK statements will not fail.
  // See IncrementalBlockReport for more infos.

  const auto& report = task.context->report;
  std::set<BlockID> blk_ids;
  for (uint64_t i = task.from_idx; i < task.to_idx; i++) {
    blk_ids.emplace(report[i].id);
  }
  BIPLockComponents blk_lck_comps =
      bip_write_manager_->Lock(blk_ids, __FILE__, __LINE__);
  BlockInfoProtos bips;
  for (uint64_t i = task.from_idx; i < task.to_idx; i++) {
    // Block(BlockID id, uint32_t len, uint64_t gs)
    Block blk(report[i].id, report[i].len, report[i].gs);
    Status s = bip_write_manager_->ReportExistedReplica(
        blk_lck_comps, blk, task.context->dn_uuid, report[i].replica_state);
    if (s.IsOK()) {
      BlockInfoProto bip;
      if (bip_write_manager_->PreCommit(blk_lck_comps, blk.id, &bip).IsOK()) {
        bips.add_content()->Swap(&bip);
      }
    }
  }
  bips.set_trigger(task.context->dn_uuid);

  task.sw.RecordAndNextStep(
      BlockReportHandlerMetrics::Instance().fbr_per_task_execute_time_);
  if (bips.content_size() != 0) {
    int64_t txid = edit_log_sender_->LogFlushBlockInfoProtos(bips);
    task.sw.RecordAndNextStep(
        BlockReportHandlerMetrics::Instance().fbr_per_task_log_edit_time_);
    blk_lck_comps.Reset();
    task_guard.release();

    RpcClosure* done = new RpcClosure(
        [this, task, blk_ids = std::move(blk_ids), bips](
            const Status& ignored) mutable {
          task.sw.RecordAndNextStep(BlockReportHandlerMetrics::Instance()
                                        .fbr_per_task_wait_callback_time_);
          {
            BIPLockComponents blk_lck_comps =
                bip_write_manager_->Lock(blk_ids, __FILE__, __LINE__);
            for (const BlockInfoProto& bip : bips.content()) {
              bip_write_manager_->PostCommit(blk_lck_comps, bip);
            }
          }
          if (task.context->ongoing_task_cnt.fetch_sub(1) == 1) {
            if (task.context->done != nullptr) {
              task.context->done->Run();
            }
          }
          task.sw.RecordAndNextStep(BlockReportHandlerMetrics::Instance()
                                        .fbr_per_task_callback_time_);
        },
        true);
    done->set_barrier(std::move(cres.second));
    meta_storage_->FlushBlockInfoProtos(bips, txid, done);
  }
  return Status();
}

Status BlockReportHandler::FullBlockReportCallback(
    std::shared_ptr<FullBlockReportContext> context) {
  context->sw.RecordAndNextStep(
      BlockReportHandlerMetrics::Instance().fbr_wait_callback_time_);
  context->sw.RecordAndNextStep(
      BlockReportHandlerMetrics::Instance().fbr_callback_time_);
  return Status();
}

BlockReportManager::BlockReportManager() : ha_state_(nullptr),
    block_manager_(nullptr), bip_write_manager_(nullptr),
    upload_cmd_mgr_(&FLAGS_upload_cmds_max_size),
    ne_cmd_mgr_(&FLAGS_ne_cmds_max_size),
    load_cmd_mgr_(&FLAGS_load_cmds_max_size) {
}

BlockReportManager::BlockReportManager(HAStateBase* ha_state)
    : ha_state_(ha_state),
      block_manager_(nullptr),
      bip_write_manager_(nullptr),
      upload_cmd_mgr_(&FLAGS_upload_cmds_max_size),
      ne_cmd_mgr_(&FLAGS_ne_cmds_max_size),
      load_cmd_mgr_(&FLAGS_load_cmds_max_size)  {
  CHECK(ha_state_);
}

void BlockReportManager::Start(NameSpace* name_space,
                               DatanodeManager* datanode_manager,
                               BlockManager* block_manager) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  CHECK(name_space);
  name_space_ = name_space;
  CHECK(datanode_manager);
  datanode_manager_ = datanode_manager;
  CHECK(block_manager);
  block_manager_ = block_manager;

  CHECK(!fbr_scheduler_);
  fbr_scheduler_ = std::make_unique<BlockReportSchedulerImpl>();
  fbr_scheduler_->SetDatanodeManager(datanode_manager_);
  fbr_scheduler_->SetBlockManager(block_manager_);

  CHECK(!fbr_workers_);
  fbr_workers_ =
      std::make_shared<cnetpp::concurrency::ThreadPool>("block-report-mgr-fbr");
  fbr_workers_->set_num_threads(FLAGS_blk_report_thread_count);
  fbr_workers_->Start();
}

void BlockReportManager::Stop() {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  if (fbr_workers_) {
    fbr_workers_->Stop(true);
    fbr_workers_.reset();
  }

  if (fbr_scheduler_) {
    fbr_scheduler_->Stop();
    fbr_scheduler_.reset();
  }
}

void BlockReportManager::StartActive(
    std::shared_ptr<EditLogSenderBase> edit_log_sender,
    std::shared_ptr<MetaStorage> meta_storage,
    BIPWriteManager* bip_write_manager) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  StopWatch sw;
  sw.Start();

  CHECK(fbr_scheduler_);
  fbr_scheduler_->Run();
  LOG(INFO) << "[HA Async] "
            << "BlockReportManager::StartActive: fbr_scheduler_->Stop() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void BlockReportManager::StopActive() {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  StopWatch sw;
  sw.Start();

  CHECK(fbr_scheduler_);
  fbr_scheduler_->Stop();
  LOG(INFO) << "[HA Async] "
            << "BlockReportManager::StopActive: fbr_scheduler_->Stop() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());

  // upload_cmd_mgr_.Clear();
  // ng_cmg_mgr_.Clear();
  // load_cmd_mgr_.Clear();
}

void BlockReportManager::StartStandby() {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  StopWatch sw;
  sw.Start();

  CHECK(fbr_scheduler_);
  fbr_scheduler_->Run();
  LOG(INFO) << "[HA Async] "
            << "BlockReportManager::StartStandby: fbr_scheduler_->Run() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void BlockReportManager::StopStandby() {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  StopWatch sw;
  sw.Start();

  CHECK(fbr_scheduler_);
  fbr_scheduler_->Stop();
  LOG(INFO) << "[HA Async] "
            << "BlockReportManager::StopStandby: fbr_scheduler_->Stop() time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

void BlockReportManager::TestOnlySetEditLogSender(
    std::shared_ptr<EditLogSenderBase> sender) {
  edit_log_sender_ = sender;
}

void BlockReportManager::IncrementalBlockReport(
    DatanodeID dn_id,
    const std::string& dn_uuid,
    const google::protobuf::RepeatedPtrField<
        cloudfs::datanode::StorageReceivedDeletedBlocksProto>& report) {
}

void BlockReportManager::FullBlockReport(
    const std::string& dn_uuid,
    const cloudfs::datanode::BlockReportRequestProto& request) {
}

void BlockReportManager::ReissueCommand(
    const std::string& dn_uuid,
    cloudfs::datanode::UploadCommandProto* upload_cmd,
    cloudfs::datanode::NotifyEvictableCommandProto* ne_cmd) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  if (upload_cmd != nullptr) {
    CHECK(upload_cmd->IsInitialized())
        << upload_cmd->InitializationErrorString();
    DatanodeID dn_id = datanode_manager_->GetDatanodeInterId(dn_uuid);
    upload_cmd_mgr_.Add(dn_id, std::move(*upload_cmd));
  }
  if (ne_cmd != nullptr) {
    CHECK(ne_cmd->IsInitialized()) << ne_cmd->InitializationErrorString();
    DatanodeID dn_id = datanode_manager_->GetDatanodeInterId(dn_uuid);
    ne_cmd_mgr_.Add(dn_id, std::move(*ne_cmd));
  }
}

void BlockReportManager::AddDatanode(DatanodeID id) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->AddDatanode(id);
}

void BlockReportManager::RemoveDatanode(DatanodeID id) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->RemoveDatanode(id);
}

void BlockReportManager::InitFullBlockReport4Test(DatanodeID id) {
  fbr_scheduler_->InitSlot4Test(id);
}

bool BlockReportManager::TriggerFullBlockReport(DatanodeID id,
                                                bool force,
                                                bool fast) {
  if (FLAGS_dancenn_observe_mode_on) {
    return false;
  }

  return fbr_scheduler_->LockSlot(id, force, fast);
}

void BlockReportManager::FinishFullBlockReport(DatanodeID id) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->FreeSlot(id);
}

void BlockReportManager::AddDeltaBlocks(DatanodeID dn_id,
                                        const std::string& storage_id,
                                        const std::vector<BlockID>& blks) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->AddDeltaBlocks(dn_id, storage_id, blks);
}

void BlockReportManager::UpdateBlockReportRecord(DatanodeID id,
                                                 bool finished,
                                                 bool fast_fbr) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->UpdateBlockReportRecord(id, finished, fast_fbr);
}

void BlockReportManager::ResetBlockReportRecord(DatanodeID id) {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->ResetBlockReportRecord(id);
}

void BlockReportManager::ClearBlockReportRecord() {
  if (FLAGS_dancenn_observe_mode_on) {
    return;
  }

  fbr_scheduler_->ClearBlockReportRecord();
}

std::shared_ptr<BlockReportContext> BlockReportManager::GetBlockReportContext(
    DatanodeID id) {
  if (FLAGS_dancenn_observe_mode_on) {
    return nullptr;
  }

  return fbr_scheduler_->GetBlockReportContext(id);
}

void BlockReportManager::GetCommands(
    DatanodeID dn_id,
    cloudfs::datanode::HeartbeatResponseProto* response) {
  if (FLAGS_namespace_type == cloudfs::NamespaceType::LOCAL) {
    return;
  }

  auto upload_cmd_size = upload_cmd_mgr_.Get(dn_id, response);
  if (upload_cmd_size > 0) {
    LOG(ERROR) << "Logical error happens, "
                  "BlockReportManager should not generate upload commands";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  auto ne_cmd_size = ne_cmd_mgr_.Get(dn_id, response);
  if (ne_cmd_size > 0) {
    LOG(ERROR) << "Logical error happens, "
                  "BlockReportManager should not generated ne commands";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  auto load_cmd_size = load_cmd_mgr_.Get(dn_id, response);
  if (load_cmd_size > 0) {
    LOG(ERROR) << "Logical error happens, "
                  "BlockReportManager should not generate load commands";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }
}

size_t BlockReportManager::GetBlockReportCommand(
    DatanodeID dn_id,
    cloudfs::datanode::HeartbeatResponseProto* response) {
  if (FLAGS_dancenn_observe_mode_on) {
    return 0;
  }

  auto br_cmd_size = fbr_scheduler_->GetCommand(dn_id, response);
  if (br_cmd_size > 0) {
    LOG(INFO) << "Get BlockReportCmd for dn " << dn_id;
  }
  return br_cmd_size;
}

BlockReportScheduler::~BlockReportScheduler() = default;

BlockReportSchedulerImpl::BlockReportSchedulerImpl() {
  br_cm_ = std::make_unique<BrCmdMgr>(&FLAGS_br_cmds_max_size);
  abr_cm_ = std::make_unique<AbortBrCmdMgr>(&FLAGS_br_cmds_max_size);
}

void BlockReportSchedulerImpl::SetBlockManager(BlockManager* bm) {
  bm_ = bm;
}

void BlockReportSchedulerImpl::SetDatanodeManager(DatanodeManager* dm) {
  dm_ = dm;
}

#define RETURN_IF_NO_RUNNING(v) \
  if (!running_) {              \
    return v;                   \
  }
void BlockReportSchedulerImpl::Run() {
  running_ = true;
  scanner_.reset(new Thread([this]() -> bool {
    while (running_) {
      std::unique_lock<std::mutex> lock(scanner_mut_);

      RETURN_IF_NO_RUNNING(true);
      ScanDnAndTriggerFBR();
      RETURN_IF_NO_RUNNING(true);

      scanner_cv_.wait_for(
          lock, std::chrono::seconds(FLAGS_block_report_scan_interval_sec));
    }
    return true;
  }));
  scanner_->Start();
}

void BlockReportSchedulerImpl::Stop() {
  StopWatch sw;
  sw.Start();
  {
    std::lock_guard<std::mutex> lock(scanner_mut_);
    running_ = false;
  }
  scanner_cv_.notify_all();

  LOG(INFO) << "[HA Async] "
            << "BlockReportSchedulerImpl::Stop: notify time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
  if (scanner_) {
    scanner_->Stop();
    LOG(INFO) << "[HA Async] "
              << "BlockReportSchedulerImpl::Stop: scanner Stop time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
    scanner_->Join();
    LOG(INFO) << "[HA Async] "
              << "BlockReportSchedulerImpl::Stop: scanner Join time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
    scanner_.reset();
    LOG(INFO) << "[HA Async] "
              << "BlockReportSchedulerImpl::Stop: scanner reset time:"
              << StringUtils::FormatWithCommas(sw.NextStepTime());
  }

  if (FLAGS_ha_async_run_switch_task && bm_ && bm_->ns() &&
      bm_->ns()->GetBgThreadPool()) {
    bm_->ns()->GetBgThreadPool()->AddTask([&]() -> bool {
      StopWatch sw1;
      sw1.Start();

      LOG(INFO) << "[HA Async] "
                << "BlockReportSchedulerImpl::Start FinishAllBlockReport time:"
                << StringUtils::FormatWithCommas(sw1.NextStepTime());

      FinishAllBlockReport();

      LOG(INFO) << "[HA Async] "
                << "BlockReportSchedulerImpl::Stop FinishAllBlockReport time:"
                << StringUtils::FormatWithCommas(sw1.NextStepTime());

      return true;
    });
  } else {
    StopWatch sw1;
    sw1.Start();

    FinishAllBlockReport();
    LOG(INFO) << "[HA Async] "
              << "BlockReportSchedulerImpl::Stop: FinishAllBlockReport time:"
              << StringUtils::FormatWithCommas(sw1.NextStepTime());
  }
}

void BlockReportSchedulerImpl::ScanDnAndTriggerFBR() {
  LOG(INFO) << "BlockReportScheduler start to scan datanode";

  // <dn_id, last_report_timestamp>
  std::vector<std::pair<DatanodeID, std::chrono::system_clock::time_point>>
      fast_fbr_candidates, normal_fbr_candidates;

  for (const auto& dn_id : block_report_history_) {
    RETURN_IF_NO_RUNNING(void());

    auto dn = dm_->GetDatanodeFromId(dn_id);

    if (!dn) {
      LOG(ERROR) << "DN not found in dm " << dn_id;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      continue;
    }
    if (!dn->IsAlive()) {
      LOG(INFO) << "DN is not alive dn=" << dn->ToShortString();
      continue;
    }

    auto now = std::chrono::system_clock::now();
    auto last_normal_fbr = dn->GetLastNormalFbrTimepoint();
    auto last_fast_fbr = dn->GetLastFastFbrTimepoint();

    if (dn->content_stale() || dn->IsDecommissionInProgress()) {
      fast_fbr_candidates.emplace_back(std::make_pair<>(dn_id, last_fast_fbr));
    } else if (TimeUtil::ToSeconds(last_fast_fbr, now) >
               FLAGS_block_report_fast_fbr_interval_sec) {
      fast_fbr_candidates.emplace_back(std::make_pair<>(dn_id, last_fast_fbr));
    } else if (TimeUtil::ToSeconds(last_normal_fbr, now) >
               FLAGS_block_report_interval_sec) {
      normal_fbr_candidates.emplace_back(
          std::make_pair<>(dn_id, last_normal_fbr));
    }
  }
  RETURN_IF_NO_RUNNING(void());

  std::sort(
      fast_fbr_candidates.begin(),
      fast_fbr_candidates.end(),
      [](const auto& c1, const auto& c2) { return c1.second < c2.second; });

  std::sort(
      normal_fbr_candidates.begin(),
      normal_fbr_candidates.end(),
      [](const auto& c1, const auto& c2) { return c1.second < c2.second; });

  RETURN_IF_NO_RUNNING(void());
  for (auto& c : fast_fbr_candidates) {
    RETURN_IF_NO_RUNNING(void());

    auto dn_id = c.first;
    auto dn = dm_->GetDatanodeFromId(dn_id);

    if (!dn) {
      LOG(ERROR) << "DN not found in dm " << dn_id;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      continue;
    }

    if (dn->TryLockForBlockReport()) {
      if (!LockSlot(dn_id,
                    /*force*/ false,
                    /*fast_fbr*/ true)) {
        LOG(INFO) << "Failed to hold one slot for dn " << dn_id
                  << " content stale " << dn->content_stale();
      }
      dn->UnlockForBlockReport();
    }
  }

  for (auto& c : normal_fbr_candidates) {
    RETURN_IF_NO_RUNNING(void());

    auto dn_id = c.first;
    auto dn = dm_->GetDatanodeFromId(dn_id);

    if (!dn) {
      LOG(ERROR) << "DN not found in dm " << dn_id;
      MFC(LoggerMetrics::Instance().error_)->Inc();
      continue;
    }

    if (dn->TryLockForBlockReport()) {
      if (!LockSlot(dn_id,
                    /*force*/ false,
                    /*fast_fbr*/ false)) {
        LOG(INFO) << "Failed to hold one slot for dn " << dn_id
                  << " content stale " << dn->content_stale();
      }
      dn->UnlockForBlockReport();
    }
  }

  LOG(INFO) << "BlockReportScheduler finish to scan datanode";
}

void BlockReportSchedulerImpl::AddDatanode(DatanodeID id) {
  {
    std::lock_guard<std::mutex> lock(scanner_mut_);
    block_report_history_.insert(id);
  }
  scanner_cv_.notify_all();
}

void BlockReportSchedulerImpl::RemoveDatanode(DatanodeID id) {
  std::lock_guard<std::mutex> lock(scanner_mut_);
  block_report_history_.erase(id);
}

void BlockReportSchedulerImpl::AddDeltaBlocks(DatanodeID dn_id,
                                   const std::string& storage_id,
                                   const std::vector<BlockID>& blks) {
  std::lock_guard<std::mutex> guard(slots_mut_);
  if (slots_.find(dn_id) == slots_.end()) {
    return;
  }
  auto ctx = slots_.at(dn_id).ctx_;
  auto& storage_report_ctx = ctx->storage_ctx_map[storage_id];
  if (storage_report_ctx == nullptr) {
    storage_report_ctx.reset(new StorageReportContext);
  }

  for (auto b : blks) {
    storage_report_ctx->delta_blks.insert(b);
    VLOG(12) << "Add delta block for dn " << dn_id << " B" << b;
  }

  if (ctx->type == cloudfs::datanode::BlockReportCommandProto_Type_FAST) {
    return;
  }

  if (storage_report_ctx->delta_blks.size() >
      FLAGS_block_report_delta_set_max_size) {
    LOG(INFO) << "IBR has too many delta blks for dn " << dn_id
              << ", size=" << storage_report_ctx->delta_blks.size();
    FinishBlockReport(dn_id);
    slots_.erase(dn_id);
  }
}

void BlockReportSchedulerImpl::UpdateBlockReportRecord(DatanodeID id,
                                                       bool finished,
                                                       bool fast_fbr) {
  if (finished) {
    std::lock_guard<std::mutex> lock(scanner_mut_);

    auto dn = dm_->GetDatanodeFromId(id);
    if (!dn) {
      LOG(ERROR) << "DN not found in dm " << id;
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }

    if (fast_fbr) {
      dn->UpdateLastFastFbrTimepoint();
    } else {
      dn->UpdateLastNormalFbrTimepoint();
    }
  } else {
    std::lock_guard<std::mutex> lock(slots_mut_);
    auto it = slots_.find(id);
    if (it == slots_.end()) {
      LOG(WARNING) << "DN " << id << " not found in slots!";
      MFC(LoggerMetrics::Instance().warn_)->Inc();
    } else {
      it->second.last_update_time_ = std::chrono::system_clock::now();
    }
  }
}

void BlockReportSchedulerImpl::ResetBlockReportRecord(DatanodeID id) {
  std::lock_guard<std::mutex> lock(scanner_mut_);
  auto it = block_report_history_.find(id);
  if (it == block_report_history_.end()) {
    return;
  }

  auto dn = dm_->GetDatanodeFromId(id);
  if (!dn) {
    LOG(ERROR) << "DN not found in dm " << id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  dn->ResetLastFastFbrTimepoint();
  dn->ResetLastNormalFbrTimepoint();
}

void BlockReportSchedulerImpl::ClearBlockReportRecord() {
  {
    std::lock_guard<std::mutex> lock(scanner_mut_);
    for (auto& dn_id : block_report_history_) {
      auto dn = dm_->GetDatanodeFromId(dn_id);
      if (!dn) {
        LOG(ERROR) << "DN not found in dm " << dn_id;
        MFC(LoggerMetrics::Instance().error_)->Inc();
      }

      dn->ResetLastFastFbrTimepoint();
      dn->ResetLastNormalFbrTimepoint();
    }
  }

  scanner_cv_.notify_all();
}

std::shared_ptr<BlockReportContext> BlockReportSchedulerImpl::GetBlockReportContext(
    DatanodeID id) {
  std::lock_guard<std::mutex> guard(slots_mut_);
  if (slots_.find(id) == slots_.end()) {
    LOG(ERROR) << "DN " << id << " not found in slots!";
    return nullptr;
  }
  return slots_.at(id).ctx_;
}

bool BlockReportSchedulerImpl::Slot::Expired(
    std::chrono::system_clock::time_point now) {
  return TimeUtil::ToSeconds(last_update_time_, now) >
         FLAGS_block_report_window_sec;
}

void BlockReportSchedulerImpl::InitSlot4Test(DatanodeID id) {
  std::lock_guard<std::mutex> guard(slots_mut_);

  auto now = std::chrono::system_clock::now();

  std::shared_ptr<BlockReportContext> ctx =
      std::make_shared<BlockReportContext>();
  ctx->block_report_id = -1;
  ctx->cur_rpc = -1;
  ctx->total_rpc = std::numeric_limits<int32_t>::max();
  // Success hold slot, issue cmd
  slots_.insert({id, Slot(now, ctx)});

  auto dn = dm_->GetDatanodeFromId(id);
  if (!dn) {
    LOG(ERROR) << "DN not found in dm " << id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }

  dn->StartFBR();
  cloudfs::datanode::BlockReportCommandProto cmd;
  if (dn->content_stale()) {
    ctx->type = cloudfs::datanode::BlockReportCommandProto_Type_FAST;
  } else {
    ctx->type = cloudfs::datanode::BlockReportCommandProto_Type_NORMAL;
  }
  LOG(INFO) << "InitSlot4Test for dn " << id << " " << cmd.ShortDebugString();
}

// Will issue FBR cmd if lock success
bool BlockReportSchedulerImpl::LockSlot(DatanodeID id,
                                        bool force,
                                        bool fast_fbr) {
  RETURN_IF_NO_RUNNING(false);
  std::lock_guard<std::mutex> guard(slots_mut_);
  // We hope FAST FBR has higher priority than NORMAL FBR, but it can NOT
  // preempt NORMAL FBR.
  //
  // max_ongoing and max_ongoing_hard are designed to limit two type of FBR with
  // different priority. max_ongoing in slots can hold both NORMAL and FAST FBR
  // while max_ongoing_hard can hold FAST FBR only.
  //
  // In an example with max_going is set to 2 and max_ongoing is set to 4, first
  // two slots can hold NORMAL or FAST FBR but the last two can only hold FAST
  // FBR.
  // A possible scenario is:
  // Slots: |0|1|2|3|
  // Type:  |N|F|F|F|
  // An impossible scenario is:
  // Slots: |0|1|2|3|
  // Type:  |N|F|N|F|
  int max_ongoing = FLAGS_max_ongoing_block_report_req_count;
  if (max_ongoing == 0) {
    max_ongoing = INT_MAX;
  }
  int max_ongoing_hard = FLAGS_max_ongoing_block_report_req_count_hard_limit;
  if (max_ongoing_hard == 0) {
    max_ongoing_hard = INT_MAX;
  }

  auto now = std::chrono::system_clock::now();
  auto dn = dm_->GetDatanodeFromId(id);
  if (!dn) {
    LOG(ERROR) << "DN not found in dm " << id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    // Return false might block FBR for normal DN
    return true;
  }

  // Try cleanup slots
  for (auto it = slots_.begin(); it != slots_.end();) {
    RETURN_IF_NO_RUNNING(false);

    if (!it->second.Expired(now)) {
      it++;
      continue;
    }
    LOG(INFO) << "BlockReportScheduler cleanup slot for dn " << it->first;
    if (it->first == id) {
      FinishBlockReport(it->first);
      it = slots_.erase(it);
    } else {
      auto victim = dm_->GetDatanodeFromId(it->first);
      if (!victim) {
        LOG(ERROR) << "DN not found in dm " << it->first;
        MFC(LoggerMetrics::Instance().error_)->Inc();
      }
      // Lock order is wrong, so we have to use TryLock here
      if (victim->TryLockForBlockReport()) {
        FinishBlockReport(it->first);
        victim->UnlockForBlockReport();
        it = slots_.erase(it);
      } else {
        it++;
        continue;
      }
    }
  }

  // Cannot find slot
  if ((!force) &&
      ((slots_.size() >= max_ongoing_hard) ||
       (slots_.size() >= max_ongoing && !(dn->content_stale() || fast_fbr)))) {
    LOG(INFO) << "Failed to find slot, current slots size is " << slots_.size()
              << " max ongoing hard is " << max_ongoing_hard
              << " max ongoing is " << max_ongoing << " id " << id
              << " fast=" << (dn->content_stale() || fast_fbr);
    return false;
  }

  // Already hold slot, still in waiting BR window
  auto it = slots_.find(id);
  if (it != slots_.end()) {
    LOG(INFO) << "Still in waiting BR window dn " << id << ", force lock "
              << force;
    if (force) {
      FinishBlockReport(it->first);
      slots_.erase(it);
    } else {
      return false;
    }
  }

  std::shared_ptr<BlockReportContext> ctx =
      std::make_shared<BlockReportContext>();
  ctx->block_report_id = -1;
  ctx->cur_rpc = -1;
  ctx->total_rpc = std::numeric_limits<int32_t>::max();
  // Success hold slot, issue cmd
  slots_.insert({id, Slot(now, ctx)});
  StartBlockReport(id, fast_fbr);
  return true;
}

void BlockReportSchedulerImpl::FreeSlot(DatanodeID id) {
  std::lock_guard<std::mutex> guard(slots_mut_);
  if (slots_.find(id) == slots_.end()) {
    LOG(ERROR) << "DN " << id << " not found in slots!";
    return;
  }
  FinishBlockReport(id);
  slots_.erase(id);
  scanner_cv_.notify_all();
}

size_t BlockReportSchedulerImpl::GetCommand(
    DatanodeID id,
    cloudfs::datanode::HeartbeatResponseProto* response) {
  size_t br_cm_cnt = 0;
  br_cm_cnt = abr_cm_->Get(id, response);
  if (br_cm_cnt > 0) {
    LOG(INFO) << "Get abort block report cmd for dn " << id << " response is "
              << response->ShortDebugString();
    return br_cm_cnt;
  }
  br_cm_cnt = br_cm_->Get(id, response);
  if (br_cm_cnt > 0) {
    LOG(INFO) << "Get block report cmd for dn " << id << " response is "
              << response->ShortDebugString();
    return br_cm_cnt;
  }
  return 0;
}

void BlockReportSchedulerImpl::StartBlockReport(DatanodeID id, bool fast_fbr) {
  auto ctx = slots_.at(id).ctx_;
  auto dn = dm_->GetDatanodeFromId(id);
  if (!dn) {
    LOG(ERROR) << "DN not found in dm " << id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }

  dn->StartFBR();
  cloudfs::datanode::BlockReportCommandProto cmd;
  if (fast_fbr || dn->content_stale()) {
    ctx->type = cloudfs::datanode::BlockReportCommandProto_Type_FAST;
    cmd.set_reportinterval(0);
    cmd.set_batchsize(FLAGS_block_report_fast_batch_size);
    cmd.set_type(ctx->type);
  } else {
    ctx->type = cloudfs::datanode::BlockReportCommandProto_Type_NORMAL;
    std::unordered_map<std::string, std::vector<BlockID>> tmp;
    dn->GetBlocksGroupByStorage(&tmp);
    for (auto& storage : tmp) {
      auto& storage_report_ctx = ctx->storage_ctx_map[storage.first];
      storage_report_ctx.reset(new StorageReportContext);
      storage_report_ctx->snapshot = storage.second;
      // Storage will be set when received first FBR from DN
    }
    cmd.set_reportinterval(FLAGS_block_report_batch_interval_ms);
    cmd.set_batchsize(FLAGS_block_report_batch_size);
    cmd.set_type(ctx->type);
  }
  LOG(INFO) << "Add blockreport command for dn " << id << " "
            << cmd.ShortDebugString();
  br_cm_->Add(id, std::move(cmd));
}

void BlockReportSchedulerImpl::FinishBlockReport(DatanodeID id) {
  auto ctx = slots_.at(id).ctx_;
  auto dn = dm_->GetDatanodeFromId(id);
  if (!dn) {
    LOG(ERROR) << "DN not found in dm " << id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return;
  }

  if (ctx->block_report_id != -1) {
    // DN sent at least one report

    if (ctx->cur_rpc >= ctx->total_rpc - 1) {
      // FBR success, do block diff
      for (auto& s : ctx->storage_ctx_map) {
        bm_->FinishStorageReport(id, dn, ctx, s.second, &running_);
      }

      dn->unset_content_stale();
    } else {
      // FBR failed, issue abort cmd
      cloudfs::datanode::AbortBlockReportCommandProto cmd;
      cmd.set_id(ctx->block_report_id);
      abr_cm_->Add(id, std::move(cmd));
      LOG(WARNING) << "FBR failed, issue abort cmd to dn " << id;
      MFC(LoggerMetrics::Instance().warn_)->Inc();
    }
  } else {
    LOG(WARNING) << "DN never send FBR to NN during window, dn " << id;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
  }

  dn->FinishFBR();
}

void BlockReportSchedulerImpl::FinishAllBlockReport() {
  StopWatch sw;
  sw.Start();
  do {
    std::set<DatanodeInfoPtr> dns;
    {
      std::unique_lock<std::mutex> lock(slots_mut_);
      VLOG(10) << "[HA Async] "
               << "BlockReportSchedulerImpl::FinishAllBlockReport: mutex time:"
               << StringUtils::FormatWithCommas(sw.NextStepTime());

      if (slots_.empty()) {
        break;
      }

      for (auto it = slots_.begin(); it != slots_.end();) {
        auto dn = dm_->GetDatanodeFromId(it->first);
        if (!dn) {
          LOG(ERROR) << "DN not found in dm " << it->first;
          it = slots_.erase(it);
          continue;
        }
        dns.insert(dn);
        it++;
      }
    }
    VLOG(10) << "[HA Async] "
             << "BlockReportSchedulerImpl::FinishAllBlockReport: "
             << "slots.size()=" << slots_.size() << " action time:"
             << StringUtils::FormatWithCommas(sw.NextStepTime());

    for (auto dn : dns) {
      dn->LockForBlockReport();
      VLOG(10) << "[HA Async] "
               << "BlockReportSchedulerImpl::FinishAllBlockReport: "
               << "dn_id=" << dn->id() << " LockForBlockReport time:"
               << StringUtils::FormatWithCommas(sw.NextStepTime());

      FreeSlot(dn->id());
      VLOG(10) << "[HA Async] "
               << "BlockReportSchedulerImpl::FinishAllBlockReport: "
               << "dn_id=" << dn->id() << " FreeSlot time:"
               << StringUtils::FormatWithCommas(sw.NextStepTime());

      dn->UnlockForBlockReport();
      VLOG(10) << "[HA Async] "
               << "BlockReportSchedulerImpl::FinishAllBlockReport: "
               << "dn_id=" << dn->id() << " UnlockForBlockReport time:"
               << StringUtils::FormatWithCommas(sw.NextStepTime());
    }

    LOG(INFO) << "Stopping block report manager, but slots is not empty, sleep "
                 "and retry...";
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  } while (true);

  LOG(INFO) << "[HA Async] "
            << "BlockReportSchedulerImpl::FinishAllBlockReport: finish time:"
            << StringUtils::FormatWithCommas(sw.NextStepTime());
}

}  // namespace dancenn
