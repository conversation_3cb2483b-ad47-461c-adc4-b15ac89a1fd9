// Copyright 2021 Mu <PERSON>ong <<EMAIL>>

#ifndef OP_TASK_OP_TASK_MANAGER_H_
#define OP_TASK_OP_TASK_MANAGER_H_

#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <memory>
#include <string>
#include <unordered_map>

#include "base/status.h"
#include "base/read_write_lock.h"
#include "dn_replacement_op_task.h"

namespace dancenn {

class DatanodeManager;
class HAStateBase;

class OpTaskManager {
 public:
  OpTaskManager(std::shared_ptr<DatanodeManager> datanode_manager,
                std::shared_ptr<HAStateBase> ha_state);
  ~OpTaskManager();

  void Start();
  void Stop();

  void ScheduleLoop();

  void TriggerSchedule();

  Status TryAddDnReplacementTaskFromJson(const std::string& task_id,
                                         const std::string& json_str);
  Status TryAddDnReplacementTask(DnReplacementOpTaskPtr op_task);
  Status RemoveDnReplacementTask(const std::string& task_id);
  std::vector<DnReplacementOpTaskPtr> ListDnReplacementTask();

  DnReplacementOpTaskPtr GetDnReplacementTask(const std::string& task_id);
  DnReplacementOpTaskPtr GetDnReplacementTaskByDn(const std::string& dn_ip);

 private:
  // return true if still have task
  bool DoTask();

  void CancelAllTask();

  bool DoDnReplacementTask();

  bool DnsOverlapInternal(const std::unordered_set<std::string>& dns,
                          std::unordered_set<std::string>& overlap_dns);

 private:
  std::condition_variable schedule_cv_;
  std::mutex schedule_mutex_;
  ReadWriteLock task_mutex_;
  std::atomic<bool> running_{true};

  std::unique_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
 private:
  std::unordered_map<std::string, DnReplacementOpTaskPtr> dn_replacement_table_;
  std::unordered_map<std::string, DnReplacementOpTaskPtr> dn_to_task_;

  std::shared_ptr<DatanodeManager> datanode_manager_{nullptr};
  std::shared_ptr<HAStateBase> ha_state_{nullptr};
};

}  // namespace dancenn

#endif  // OP_TASK_OP_TASK_MANAGER_H_