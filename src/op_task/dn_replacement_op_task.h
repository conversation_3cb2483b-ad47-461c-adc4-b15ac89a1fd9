// Copyright 2021 Mu <PERSON>ong <<EMAIL>>

#ifndef OP_TASK_DN_REPLACEMENT_OP_TASK_H_
#define OP_TASK_DN_REPLACEMENT_OP_TASK_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <chrono>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "datanode_manager/datanode_info.h"
#include "op_task.h"

namespace dancenn {

class DatanodeManager;

class DnReplacementOpTask : public OpTask {
 public:
  struct SrcDnState {
    std::string socket;
    DatanodeInfoPtr dn_info{nullptr};
    std::atomic<int> block_start;
    std::atomic<int> block_target;
    std::atomic<int> block_to_move;
    std::atomic<int> block_now;
    std::atomic<int> replicated_blocks;

    std::atomic<bool> finish{false};

    std::string ToString();
  };

 public:
  DnReplacementOpTask(std::string task_id,
                      std::shared_ptr<DatanodeManager> datanode_manager);
  ~DnReplacementOpTask() override = default;

  void Start() override;
  void RunOnce() override;
  void Cancel() override;
  void RefreshState() override;

  void RefreshStateInternal();

  const std::unordered_set<std::string>& src_dns() const { return src_dns_; }
  const std::unordered_set<std::string>& dst_dns() const { return dst_dns_; }

  std::string ToJsonString(bool detail = false) override;
  Status FromJsonString(const std::string& str) override;

 private:
  void CheckDn(const std::string& dn_socket);

 private:
  std::mutex mutex_;
  bool stop_{false};
  std::unordered_set<std::string> src_dns_;
  std::unordered_set<std::string> dst_dns_;

  // 1 ~ 100
  int move_rate_{0};

  std::unordered_map<std::string, SrcDnState> src_state_table_;
  std::shared_ptr<DatanodeManager> datanode_manager_{nullptr};

  std::atomic<int> finished_dns_{0};

  std::atomic<int64_t> total_block_start_{0};
  std::atomic<int64_t> total_block_now_{0};
  std::atomic<int64_t> total_block_target_{0};
};
using DnReplacementOpTaskPtr = std::shared_ptr<DnReplacementOpTask>;

}  // namespace dancenn

#endif  // OP_TASK_DN_REPLACEMENT_OP_TASK_H_