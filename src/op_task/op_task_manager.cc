// Copyright 2021 Mu <PERSON> <<EMAIL>>

#include "op_task_manager.h"

#include <algorithm>

#include "datanode_manager/datanode_manager.h"

DECLARE_uint32(op_task_max_size);
DECLARE_uint32(op_task_max_dns);
DECLARE_uint32(op_task_check_interval_sec);

namespace dancenn {

OpTaskManager::OpTaskManager(std::shared_ptr<DatanodeManager> datanode_manager,
                             std::shared_ptr<HAStateBase> ha_state)
    : datanode_manager_(std::move(datanode_manager)),
      ha_state_(std::move(ha_state)) {
  CHECK_NOTNULL(datanode_manager_);
  CHECK_NOTNULL(ha_state_);

  thread_pool_ =
      std::make_unique<cnetpp::concurrency::ThreadPool>("optask", true);
  thread_pool_->set_num_threads(static_cast<size_t>(1));
}

OpTaskManager::~OpTaskManager() { Stop(); }

void OpTaskManager::Start() {
  thread_pool_->Start();
  thread_pool_->AddTask([this]() -> bool {
    ScheduleLoop();
    return true;
  });
}

void OpTaskManager::Stop() {
  CancelAllTask();
  running_.store(false);
  TriggerSchedule();
  thread_pool_->Stop(false);
}

void OpTaskManager::TriggerSchedule() {
  std::unique_lock<std::mutex> lock(schedule_mutex_, std::try_to_lock);
  if (lock.owns_lock()) {
    schedule_cv_.notify_all();
  }
}

void OpTaskManager::ScheduleLoop() {
  while (running_.load()) {
    std::unique_lock<std::mutex> lock(schedule_mutex_);

    while (running_.load() && DoTask()) {
      schedule_cv_.wait_for(lock, std::chrono::seconds(1));
    }

    if (running_.load()) {
      auto to_sleep = std::chrono::seconds(FLAGS_op_task_check_interval_sec);
      schedule_cv_.wait_for(lock, to_sleep);
    }
  }
}

bool OpTaskManager::DoTask() {
  if (!ha_state_->IsActive() || ha_state_->InTransition()) {
    CancelAllTask();
    return false;
  }

  LOG(INFO) << "OpTaskManager::DoTask";
  auto rtn = false;
  rtn |= DoDnReplacementTask();
  return rtn;
}

void OpTaskManager::CancelAllTask() {
  std::unique_lock<ReadWriteLock> guard(task_mutex_);

  for (auto& pair : dn_replacement_table_) {
    auto task_id = pair.first;
    auto task = pair.second;
    task->Cancel();
  }
  dn_replacement_table_.clear();
}

bool OpTaskManager::DoDnReplacementTask() {
  auto tasks = ListDnReplacementTask();

  LOG(INFO) << "OpTaskManager::DoDnReplacementTask size=" << tasks.size();
  for (auto& task : tasks) {
    task->RunOnce();
  }

  return !tasks.empty();
}

Status OpTaskManager::TryAddDnReplacementTask(DnReplacementOpTaskPtr op_task) {
  std::unique_lock<ReadWriteLock> guard(task_mutex_);

  if (dn_replacement_table_.count(op_task->task_id())) {
    return Status(JavaExceptions::Exception::kIOException,
                  "Reject, DnReplacementOpTask Already Exist. task_id=" +
                      op_task->task_id());
  }

  std::unordered_set<std::string> overlap_dns;
  if (DnsOverlapInternal(op_task->src_dns(), overlap_dns)) {
    std::string dns_str;
    for (auto dn : overlap_dns) {
      dns_str += dn;
      dns_str += ",";
    }
    return Status(JavaExceptions::Exception::kIOException,
                  "Reject, DnReplacementOpTask SrcDNs Overlap. task_id=" +
                      op_task->task_id() + " overlap_dns=" + dns_str);
  }

  if (op_task->src_dns().size() > FLAGS_op_task_max_dns ||
      op_task->dst_dns().size() > FLAGS_op_task_max_dns) {
    return Status(JavaExceptions::Exception::kIOException,
                  "Reject, DnReplacementOpTask SrcDNs/DstDns size is too "
                  "large. task_id=" +
                      op_task->task_id() + " FLAGS_op_task_max_dns=" +
                      std::to_string(FLAGS_op_task_max_dns));
  }

  if (dn_replacement_table_.size() > FLAGS_op_task_max_size) {
    return Status(JavaExceptions::Exception::kIOException,
                  "Reject, Too Many Task is Running. task_id=" +
                      op_task->task_id() + " FLAGS_op_task_max_size=" +
                      std::to_string(FLAGS_op_task_max_size));
  }

  dn_replacement_table_[op_task->task_id()] = op_task;
  for (auto& dn_socket : op_task->src_dns()) {
    dn_to_task_[dn_socket] = op_task;
  }
  op_task->Start();

  TriggerSchedule();

  return Status();
}

Status OpTaskManager::TryAddDnReplacementTaskFromJson(
    const std::string& task_id, const std::string& json_str) {
  auto task = std::make_shared<DnReplacementOpTask>(task_id, datanode_manager_);
  auto s = task->FromJsonString(json_str);
  if (s.HasException()) {
    return s;
  }
  return TryAddDnReplacementTask(task);
}

Status OpTaskManager::RemoveDnReplacementTask(const std::string& task_id) {
  std::unique_lock<ReadWriteLock> guard(task_mutex_);

  auto iter = dn_replacement_table_.find(task_id);
  if (iter == dn_replacement_table_.end()) {
    return Status();
  }
  auto task = iter->second;
  task->Cancel();
  dn_replacement_table_.erase(iter);

  for (auto dn : task->src_dns()) {
    dn_to_task_.erase(dn);
  }

  return Status();
}

std::vector<DnReplacementOpTaskPtr> OpTaskManager::ListDnReplacementTask() {
  std::shared_lock<ReadWriteLock> guard(task_mutex_);

  std::vector<DnReplacementOpTaskPtr> ret;
  ret.reserve(dn_replacement_table_.size());
  for (auto& pair : dn_replacement_table_) {
    ret.push_back(pair.second);
  }

  std::sort(
      ret.begin(), ret.end(),
      [](const DnReplacementOpTaskPtr& a, const DnReplacementOpTaskPtr& b) {
        return a->task_id() < b->task_id();
      });

  return ret;
}

DnReplacementOpTaskPtr OpTaskManager::GetDnReplacementTask(
    const std::string& task_id) {
  std::shared_lock<ReadWriteLock> guard(task_mutex_);

  auto iter = dn_replacement_table_.find(task_id);
  if (iter == dn_replacement_table_.end()) {
    return nullptr;
  } else {
    return iter->second;
  }
}

DnReplacementOpTaskPtr OpTaskManager::GetDnReplacementTaskByDn(
    const std::string& dn_socket) {
  std::shared_lock<ReadWriteLock> guard(task_mutex_);

  auto iter = dn_to_task_.find(dn_socket);
  if (iter == dn_to_task_.end()) {
    return nullptr;
  } else {
    return iter->second;
  }
}

bool OpTaskManager::DnsOverlapInternal(
    const std::unordered_set<std::string>& dns,
    std::unordered_set<std::string>& overlap_dns) {
  for (const auto& dn : dns) {
    if (dn_to_task_.count(dn)) {
      overlap_dns.insert(dn);
    }
  }
  return !overlap_dns.empty();
}

}  // namespace dancenn