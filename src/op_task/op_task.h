// Copyright 2021 Mu <PERSON> <<EMAIL>>

#ifndef OP_TASK_OP_TASK_H_
#define OP_TASK_OP_TASK_H_

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>

#include <atomic>
#include <chrono>
#include <string>

#include "base/status.h"
#include "base/time_util.h"

namespace dancenn {

class OpTask {
 public:
  explicit OpTask(std::string task_id) : task_id_(task_id) {
    start_timestamp_ms_ = TimeUtil::GetNowEpochMs();
  }
  virtual ~OpTask() = default;

  virtual void Start() = 0;

  // Use Cooperative Scheduling, run out of task's time slice and yield
  virtual void RunOnce() = 0;

  virtual void Cancel() = 0;

  virtual void RefreshState() = 0;

  void SetFinish() {
    if (finish_) {
      return;
    }
    end_timestamp_ms_ = TimeUtil::GetNowEpochMs();
    finish_ = true;
  }

  bool IsFinish() const { return finish_.load(); }

  const std::string& task_id() const { return task_id_; }

  virtual std::string ToJsonString(bool detail = false) = 0;
  virtual Status FromJsonString(const std::string& str) = 0;

 protected:
  std::atomic<uint64_t> start_timestamp_ms_{0};
  std::atomic<uint64_t> end_timestamp_ms_{0};
  std::atomic<bool> finish_{false};

 private:
  const std::string task_id_;
};

}  // namespace dancenn

#endif  // OP_TASK_OP_TASK_H_