#include "dn_replacement_op_task.h"

#include <cnetpp/base/csonpp.h>
#include <glog/logging.h>

#include <sstream>
#include <utility>

#include "datanode_manager/datanode_manager.h"

namespace dancenn {

std::string DnReplacementOpTask::SrcDnState::ToString() {
  std::ostringstream oss;
  oss << "dn=" << socket;
  oss << " dn_uuid=" << (dn_info == nullptr ? "null" : dn_info->uuid());
  oss << " block_now=" << block_now;
  oss << " replicated_blocks=" << replicated_blocks;
  oss << " task=" << block_start << "->" << block_target;
  oss << " to_move=" << block_to_move;
  oss << " moved=" << block_start - block_now;
  oss << " finish=" << finish;

  return oss.str();
}

DnReplacementOpTask::DnReplacementOpTask(
    std::string task_id, std::shared_ptr<DatanodeManager> datanode_manager)
    : OpTask(std::move(task_id)),
      datanode_manager_(std::move(datanode_manager)) {
  CHECK_NOTNULL(datanode_manager_);
}

void DnReplacementOpTask::Start() {
  std::lock_guard<std::mutex> guard(mutex_);

  CHECK(!src_dns_.empty());
  LOG(INFO) << "[DnReplacementOpTask Start] task_id=" << task_id();

  auto rate = static_cast<double>(move_rate_) / 100;
  for (const auto& dn_socket : src_dns_) {
    SrcDnState& state = src_state_table_[dn_socket];
    state.socket = dn_socket;
    state.dn_info = datanode_manager_->GetDatanodeFromSocket(dn_socket);
    if (state.dn_info) {
      state.block_start = state.dn_info->NumBlocks();
      state.block_to_move = static_cast<int>(state.dn_info->NumBlocks() * rate);
      state.block_now = state.block_start.load();
      state.block_target = state.block_start - state.block_to_move;
      state.replicated_blocks = state.dn_info->replicated_blocks();
    }
    LOG(INFO) << "[DnReplacementOpTask Init] task_id=" << task_id()
              << " state=" << state.ToString();
  }
}

void DnReplacementOpTask::RunOnce() {
  std::lock_guard<std::mutex> guard(mutex_);

  if (stop_) {
    return;
  }

  for (const auto& src : src_dns_) {
    CheckDn(src);
  }

  RefreshStateInternal();
}

void DnReplacementOpTask::Cancel() {
  std::lock_guard<std::mutex> guard(mutex_);

  stop_ = true;
  LOG(INFO) << "[DnReplacementOpTask Cancel] task_id=" << task_id();

  for (const auto& dn_socket : src_dns_) {
    SrcDnState& state = src_state_table_[dn_socket];

    if (state.dn_info) {
      state.dn_info->SetNonDecommissioning();
    }

    LOG(INFO) << "[DN Replacement task] task=" << task_id()
              << " Cancel Decommission DN " << state.ToString();
  }

  datanode_manager_.reset();
}

void DnReplacementOpTask::RefreshState() {
  std::lock_guard<std::mutex> guard(mutex_);

  RefreshStateInternal();
}

void DnReplacementOpTask::RefreshStateInternal() {
  if (IsFinish()) {
    return;
  }

  auto now = std::chrono::system_clock::now();
  size_t finished_dns = 0;
  int64_t total_block_start = 0;
  int64_t total_block_now = 0;
  int64_t total_block_target = 0;
  for (const auto& dn_socket : src_dns_) {
    auto& state = src_state_table_[dn_socket];
    if (state.finish) {
      finished_dns++;
    }
    total_block_start += state.block_start;
    total_block_now += state.block_now;
    total_block_target += state.block_target;
  }

  total_block_start_ = total_block_start;
  total_block_now_ = total_block_now;
  total_block_target_ = total_block_target;
  finished_dns_ = finished_dns;

  if (finished_dns == src_dns_.size()) {
    SetFinish();
    LOG(INFO) << "[DN Replacement task] task=" << task_id() << " Finish. "
              << ToJsonString();
  }
}

void DnReplacementOpTask::CheckDn(const std::string& dn_socket) {
  auto& state = src_state_table_[dn_socket];
  if (!state.dn_info) {
    return;
  }

  if (state.finish) {
    return;
  }

  state.block_now = state.dn_info->NumBlocks();
  state.replicated_blocks = state.dn_info->replicated_blocks();

  if (state.block_now <= state.block_target) {
    state.finish = true;
    state.dn_info->SetNonDecommissioning();
    LOG(INFO) << "[DN Replacement task] task=" << task_id()
              << " Finish Decommission DN " << state.ToString();
    return;
  }

  if (!state.dn_info->IsDecommissionInProgress()) {
    LOG(INFO) << "[DN Replacement task] task=" << task_id()
              << " Start Decommission DN " << state.ToString();
    state.dn_info->SetDecommissioning();
  }
}

std::string DnReplacementOpTask::ToJsonString(bool detail) {
  cnetpp::base::Object obj;

  obj["task_id"] = task_id();

  cnetpp::base::Array src_json;
  for (const auto& src : src_dns_) {
    src_json.Append(cnetpp::base::Value(src));
  }
  obj["src_dns"] = src_json;

  cnetpp::base::Array dst_json;
  for (const auto& dst : dst_dns_) {
    dst_json.Append(cnetpp::base::Value(dst));
  }
  obj["dst_dns"] = dst_json;

  int64_t total_number_start = total_block_start_.load();
  int64_t total_number_now = total_block_now_.load();
  int64_t total_number_target = total_block_target_.load();
  size_t finished_dns = finished_dns_.load();

  if (detail) {
    cnetpp::base::Object detail_json;
    cnetpp::base::Array detail_msg;
    cnetpp::base::Array src_json;
    cnetpp::base::Array dst_json;
    for (const auto& dn_socket : src_dns_) {
      auto& state = src_state_table_[dn_socket];

      detail_msg.Append(cnetpp::base::Value(state.ToString()));
    }
    for (const auto& dn_socket : src_dns_) {
      auto dn = datanode_manager_->GetDatanodeFromSocket(dn_socket);
      if (dn) {
        auto json = dn->ToJson();
        src_json.Append(cnetpp::base::Value(json));
      }
    }
    for (const auto& dn_socket : dst_dns_) {
      auto dn = datanode_manager_->GetDatanodeFromSocket(dn_socket);
      if (dn) {
        auto json = dn->ToJson();
        src_json.Append(cnetpp::base::Value(json));
      }
    }

    detail_json["msg"] = detail_msg;
    detail_json["src"] = src_json;
    detail_json["dst"] = dst_json;
    obj["detail"] = detail_json;
  }

  int64_t balance_block_num = total_number_start - total_number_target;
  int64_t balance_block_now = total_number_start - total_number_now;

  auto start_time = start_timestamp_ms_.load() / 1000;
  auto end_time = end_timestamp_ms_.load() / 1000;
  if (end_time == 0) {
    end_time = TimeUtil::GetNowEpochMs() / 1000;
  }
  auto duration = end_time - start_time;
  auto start_time_str = TimeUtil::EpochToString(start_time);
  auto end_time_str = TimeUtil::EpochToString(end_time);
  double speed_ps = 0;
  if (duration > 0) {
    speed_ps = static_cast<double>(balance_block_now) / duration;
  }

  cnetpp::base::Object obj_time;
  obj_time["start_time"] = start_time;
  obj_time["end_time"] = end_time;
  obj_time["start_time_str"] = start_time_str;
  obj_time["end_time_str"] = end_time_str;
  obj_time["duration"] = duration;
  obj_time["speed_ps"] = speed_ps;
  obj["time"] = obj_time;

  obj["balance_block_rate"] = move_rate_;
  obj["balance_block_num"] = balance_block_num;
  obj["balance_block_now"] = balance_block_now;
  obj["total_number_start"] = total_number_start;
  obj["total_number_now"] = total_number_now;
  obj["total_number_target"] = total_number_target;
  obj["finished_dns"] = static_cast<int>(finished_dns);
  obj["finished"] = IsFinish();

  std::string ret;
  cnetpp::base::Parser::Serialize(cnetpp::base::Value(std::move(obj)), &ret);
  return ret;
}

Status DnReplacementOpTask::FromJsonString(const std::string& str) {
  // Deserialize
  cnetpp::base::Value received_json;
  if (!cnetpp::base::Parser::Deserialize(str, &received_json) ||
      !received_json.IsObject()) {
    auto s = Status(JavaExceptions::Exception::kIOException,
                    "Failed to deserialize JSON.");
    LOG(WARNING) << "str=" << str << " status=" << s.ToString();
    return s;
  }
  auto config_obj = received_json.AsObject();

  // balance_block_rate
  {
    auto entry = config_obj.Find("balance_block_rate");
    if (entry == config_obj.End() || !entry->second.IsIntegral()) {
      auto s = Status(JavaExceptions::Exception::kIOException,
                      "`balance_block_rate` not found or not integral");
      LOG(WARNING) << "str=" << str << " status=" << s.ToString();
      return s;
    }

    move_rate_ = entry->second.AsInteger();
    if (move_rate_ <= 0 || move_rate_ > 100) {
      auto s = Status(JavaExceptions::Exception::kIOException,
                      "`balance_block_rate` not invalid. need (0,100], actual" +
                          std::to_string(move_rate_));
      LOG(WARNING) << "str=" << str << " status=" << s.ToString();
      return s;
    }
  }

  // src_dns
  {
    cnetpp::base::Array array;
    auto entry = config_obj.Find("src_dns");
    if (entry == config_obj.End() || !entry->second.IsArray()) {
      auto s = Status(JavaExceptions::Exception::kIOException,
                      "`src_dns` not found or not socket array." +
                          std::to_string(move_rate_));
      LOG(WARNING) << "str=" << str << " status=" << s.ToString();
      return s;
    }
    array = entry->second.AsArray();

    for (auto itr = array.Begin(); itr != array.End(); ++itr) {
      if (!itr->IsString()) {
        auto s = Status(
            JavaExceptions::Exception::kIOException,
            "`src_dns` item not valid string." + std::to_string(move_rate_));
        LOG(WARNING) << "str=" << str << " status=" << s.ToString();
        return s;
      }
      auto src_dn = itr->AsString();
      src_dns_.insert(src_dn);
    }
  }

  // dst_dns
  {
    cnetpp::base::Array array;
    auto entry = config_obj.Find("dst_dns");
    if (entry == config_obj.End() || !entry->second.IsArray()) {
      auto s = Status(JavaExceptions::Exception::kIOException,
                      "`dst_dns` not found or not socket array." +
                          std::to_string(move_rate_));
      LOG(WARNING) << "str=" << str << " status=" << s.ToString();
      return s;
    }
    array = entry->second.AsArray();

    for (auto itr = array.Begin(); itr != array.End(); ++itr) {
      if (!itr->IsString()) {
        auto s = Status(
            JavaExceptions::Exception::kIOException,
            "`dst_dns` item not valid string." + std::to_string(move_rate_));
        LOG(WARNING) << "str=" << str << " status=" << s.ToString();
        return s;
      }
      auto dst_dn = itr->AsString();
      dst_dns_.insert(dst_dn);
    }
  }

  return Status();
}

}  // namespace dancenn