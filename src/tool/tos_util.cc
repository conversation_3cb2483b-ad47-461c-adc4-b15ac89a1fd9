#include <aws/core/Aws.h>
#include <glog/logging.h>
#include <openssl/crypto.h>
#include <openssl/err.h>
#include <openssl/evp.h>

#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/tos_ufs/tos_ufs.h"
#include "ufs/ufs_auth_conf.h"

DECLARE_string(tos_endpoint);
DECLARE_string(tos_bucket);
DECLARE_string(tos_prefix);
DECLARE_string(tos_region);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  Aws::SDKOptions aws_sdk_options;
  aws_sdk_options.loggingOptions.logLevel =
      Aws::Utils::Logging::LogLevel::Debug;
  aws_sdk_options.httpOptions.installSigPipeHandler = true;
  Aws::InitAPI(aws_sdk_options);

  CHECK(dancenn::UfsAuthConf::Instance().Init().IsOK());

  dancenn::TosConfig config_;

  config_.bucket = FLAGS_tos_bucket;
  config_.endpoint = FLAGS_tos_endpoint;
  config_.prefix = FLAGS_tos_prefix;
  config_.region = FLAGS_tos_region;

  auto ufs_config_ = dancenn::TosConfig::CreateUfsConfig(config_);

  auto cred_keeper = std::make_shared<dancenn::TosCredKeeper>();
  CHECK(cred_keeper->Start().IsOK());

  auto cred = std::shared_ptr<dancenn::TosCredentialProvider>(
      new dancenn::StaticTosCredentialProvider(cred_keeper));

  std::shared_ptr<dancenn::TosUfs> ufs_ =
      std::make_shared<dancenn::TosUfs>(ufs_config_, config_, cred);
  ufs_->Init();

  std::string path = "/";
  if (!FLAGS_tos_prefix.empty()) {
    path.append(FLAGS_tos_prefix);
  }

  {
    dancenn::UfsFileStatus file_status;
    auto s = ufs_->GetFileStatus(path, &file_status);
    CHECK(s.IsOK());
    LOG(INFO) << "file status: " << file_status.FileName();
  }

  {
    dancenn::ListFilesResult res;
    auto s = ufs_->ListFiles(path, dancenn::ListFilesOption(), &res);
    CHECK(s.IsOK());
    std::stringstream ss;
    ss << "Files: ";
    for (auto& file : res.files) {
      ss << " file:" << file.FileName() << " len:" << file.FileSize()
         << std::endl;
    }
    ss << std::endl;
    LOG(INFO) << "list file result: " << ss.str();
  }

  CRYPTO_cleanup_all_ex_data();
  ERR_free_strings();
  ERR_remove_state(0);
  EVP_cleanup();

  return 0;
}