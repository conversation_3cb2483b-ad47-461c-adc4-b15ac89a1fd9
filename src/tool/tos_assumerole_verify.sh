#!/bin/bash

# 脚本依赖 AK SK 敏感信息，速查运行查看：https://bytedance.larkoffice.com/wiki/Cr39wm8O6ivMUxk9zUEcgqbxnT3

# 两个个场景，--type 参数：
# 1. 统一OpenAPI Host + opentop
# 2. 自举OpenAPI Host + innertop

# 三个用例
# 1. 不开启ufs_auth, 旧的 iam_account_id + assume_role_name 参数
# 2. 开启ufs_auth, 新的 role_info 参数，直接 Assume 租户 Role
# 3. 开启ufs_auth, 新的 role_info 参数，RoleChain

# 多套环境，--env 参数

set +x
set -e
set -o noglob

SOURCE_DIR="$(dirname "${BASH_SOURCE[0]}")"
export LD_LIBRARY_PATH=$SOURCE_DIR/../lib

if [[ $# -eq 0 ]]; then
  echo "用法：$0 --type {opentop|innertop} --region <REGION> --ak <AK> --sk <SK> [--env {cfs|cfs_ppe}]"
  echo "用法：--env 可选参数: cfs -> 火山线上 cfs; cfs_ppe -> 火山线上 cfs_ppe; 其他待补充"
  echo "文档：https://bytedance.larkoffice.com/wiki/Cr39wm8O6ivMUxk9zUEcgqbxnT3"
  exit 0
fi

# 使用 getopt 定义参数规则（支持长选项）
options=$(getopt -o "" --long "type:,env:,region:,ak:,sk:" -n "$0" -- "$@") || exit 1
eval set -- "$options"

# 初始化变量
type_value=""       # --type 必填
env_value="cfs"     # --env 默认值
region_value=""     # --region 必填
ak_value=""         # --ak 必填
sk_value=""         # --sk 必填

# 参数解析逻辑
while true; do
    case "$1" in
        --type)
            type_value="$2"
            # 验证 type 参数合法性
            if [[ "$type_value" != "opentop" && "$type_value" != "innertop" ]]; then
                echo "错误：--type 必须是 opentop 或 innertop" >&2
                exit 1
            fi
            shift 2
            ;;
        --env)
            env_value="$2"
            # 验证 env 参数合法性
            if [[ "$env_value" != "cfs" && "$env_value" != "cfs_ppe" ]]; then
                echo "错误：--env 必须是 cfs 或 cfs_ppe" >&2
                exit 1
            fi
            shift 2
            ;;
        --region)
            region_value="$2"
            shift 2
            ;;
        --ak)
            ak_value="$2"
            shift 2
            ;;
        --sk)
            sk_value="$2"
            shift 2
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "未知选项：$1" >&2
            exit 1
            ;;
    esac
done

# 检查所有必填参数
required_params=("type_value" "region_value" "ak_value" "sk_value")
for param in "${required_params[@]}"; do
    if [[ -z "${!param}" ]]; then
        param_name=${param%_value}
        echo "错误：必须提供 --${param_name//_/-} 参数" >&2
        exit 1
    fi
done

# 输出结果
echo "Config"
echo "--type   = $type_value"
echo "--env    = $env_value"
echo "--region = $region_value"
echo "--ak     = $ak_value"
echo "--sk     = $sk_value"

OPENTOP_URL='https://open.volcengineapi.com'
OPENTOP_STS_URL="https://sts.$CFS_REGION.volcengineapi.com"
INNER_TOP_URL="https://sts.$region_value.innerapi.commonpcs.com"

CFS_IAM_ACCOUNT_ID='**********'
CFS_ASSUME_ROLE_NAME='ServiceRoleForCFS'
CFS_TENANT_ROLE='[{"AccountId":"**********","Role":"ServiceRoleForCFS"}]'
CFS_MAAS_ROLECHAIN='[{"AccountId":"","Role":"arkROLEArkTrustRoleForCFS"},{"AccountId":"**********","Role":"ServiceRoleForArk"}]'

CFS_PPE_IAM_ACCOUNT_ID='**********'
CFS_PPE_ASSUME_ROLE_NAME='ServiceRoleForCFSPPE'
CFS_PPE_TENANT_ROLE='[{"AccountId":"**********","Role":"ServiceRoleForCFSPPE"}]'
CFS_PPE_MAAS_ROLECHAIN='[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"ServiceRoleForMLMaaSBOE"}]'

if [ "$type_value" = "opentop" ]; then
  TOP=$OPENTOP_URL
else
  TOP=$INNER_TOP_URL
fi

if [  "$env_value" = "cfs" ]; then
  IAM_ACCOUNT_ID=$CFS_IAM_ACCOUNT_ID
  ASSUME_ROLE_NAME=$CFS_ASSUME_ROLE_NAME
  TENANT_ROLE=$CFS_TENANT_ROLE
  ROLECHAIN=$CFS_MAAS_ROLECHAIN
elif [  "$env_value" = "cfs_ppe" ]; then
  IAM_ACCOUNT_ID=$CFS_PPE_IAM_ACCOUNT_ID
  ASSUME_ROLE_NAME=$CFS_PPE_ASSUME_ROLE_NAME
  TENANT_ROLE=$CFS_PPE_TENANT_ROLE
  ROLECHAIN=$CFS_PPE_MAAS_ROLECHAIN
else
  echo "错误：--env 必须是 cfs 或 cfs_ppe" >&2
  exit 1
fi

NAME="$type_value-$env_value"

echo "=========== Case 1. 不开启ufs_auth, 旧的 iam_account_id + assume_role_name 参数 ============"
$SOURCE_DIR/tos_assumerole_verify --vmodule=*=8 --name "$NAME-Case1" --tos_prefix="" --ufs_auth_enabled=false --use_fixed_ak=false --cfs_service_region=$region_value --iam_top_url=$TOP --cfs_service_ak=$ak_value --cfs_service_sk=$sk_value --iam_account_id=$IAM_ACCOUNT_ID --assume_role_name=$ASSUME_ROLE_NAME

echo "=========== Case 2. 开启ufs_auth, 新的 role_info 参数，直接 Assume 租户 Role ================"
$SOURCE_DIR/tos_assumerole_verify --vmodule=*=8 --name "$NAME-Case2" --tos_prefix="" --ufs_auth_enabled=true --cfs_service_region=$region_value --iam_top_url=$TOP --cfs_service_ak=$ak_value --cfs_service_sk=$sk_value --ufs_auth_policy=role --ufs_auth_role_info=$(echo -n $TENANT_ROLE | base64 -w 0)

echo "=========== Case 3. 开启ufs_auth, 新的 role_info 参数，RoleChain ==========================="
$SOURCE_DIR/tos_assumerole_verify --vmodule=*=8 --name "$NAME-Case3" --tos_prefix="" --ufs_auth_enabled=true --cfs_service_region=$region_value --iam_top_url=$TOP --cfs_service_ak=$ak_value --cfs_service_sk=$sk_value --ufs_auth_policy=role --ufs_auth_role_info=$(echo -n $ROLECHAIN | base64 -w 0)

echo "=========== All done ====================================================================="
