// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/11/23
// Description

#include <absl/strings/str_format.h>         // For StrFormat.
#include <cnetpp/base/string_piece.h>        // For StringPiece.
#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <gflags/gflags.h>                   // For DECLARE_string, etc.
#include <glog/logging.h>                    // For CHECK, etc.
#include <rocksdb/db.h>                      // For ColumnFamilyHandle, etc.
#include <rocksdb/iterator.h>                // For Iterator.
#include <rocksdb/metadata.h>                // For ColumnFamilyMetaData.
#include <rocksdb/options.h>                 // For ReadOptions.
#include <rocksdb/slice.h>                   // For Slice.
#include <rocksdb/status.h>                  // For status.

#include <algorithm>      // For accumulate, max.
#include <atomic>         // For atomic, memory_order_acq_rel.
#include <chrono>         // For chrono.
#include <cstddef>        // For size_t.
#include <cstdint>        // For uint32_t, etc.
#include <functional>     // For function.
#include <map>            // For map.
#include <memory>         // For unique_ptr, etc.
#include <mutex>          // For mutex, shared_lock, unique_lock.
#include <set>            // For set.
#include <string>         // For stoll, string.
#include <thread>         // For this_thread.
#include <unordered_map>  // For unordered_map.
#include <utility>        // For pair.
#include <vector>         // For vector.

#include "base/constants.h"  // For kLastAllocatedBlockIdKey, kLastReservedBlockId, kRootINodeId.
#include "base/defer.h"                        // For DEFER.
#include "base/platform.h"                     // For ReadBigEndian.
#include "base/read_write_lock.h"              // For ReadWriteLock.
#include "base/stop_watch.h"                   // For StopWatch.
#include "base/to_json_string.h"               // For ToJsonCompactString.
#include "block_manager/block.h"               // For BlockID.
#include "namespace/inode.h"                   // For INodeID.
#include "namespace/meta_storage.h"            // For MetaStorage.
#include "namespace/meta_storage_constants.h"  // For kINodeDefaultCFIndex, etc.
#include "proto/generated/cloudfs/hdfs.pb.h"   // For BlockProto.
#include "proto/generated/dancenn/block_info_proto.pb.h"  // For BlockInfoProto.
#include "proto/generated/dancenn/inode.pb.h"             // For INode.
#include "proto/generated/dancenn/status_code.pb.h"       // For StatusCode.

DECLARE_string(namespace_meta_storage_path);
DECLARE_int32(dfs_load_from_metastorage_threadpool_count);
DECLARE_int32(dfs_load_from_metastorage_thread_count_per_pool);
DECLARE_int32(blockmap_num_slice);
DEFINE_int32(inodemap_num_slice, 262144, "Number of slice for inode");

struct Bug {
  std::string name;
  std::string doc;
  // https://code.byted.org/inf/dancenn/blob/${commit_id}/${loc}
  std::string commit_id;
  std::string loc;
  bool need_fix;
};
DEFINE_bool(fix_bug_update_pipeline_not_update_gs_of_bip, false, "");
Bug kBugUpdatePipelineNotUpdateGsOfBlockInfoProto{
    .name = "BugUpdatePipelineNotUpdateGsOfBlockInfoProto",
    .doc = "https://bytedance.feishu.cn/docx/S9sidm4bioXhbwxuXQjcoyz3nzc",
    .commit_id = "906b2f0d9820c17ae0614046885f2079ca7ae17e",
    .loc = "src/namespace/namespace.cc#L7697",
    .need_fix = false,
};
DEFINE_bool(fix_bug_fsync_not_update_len_of_bip, false, "");
Bug kBugFsyncNotUpdateLenOfBlockInfoProto{
    .name = "BugFsyncNotUpdateLenOfBlockInfoProto",
    .doc = "https://bytedance.feishu.cn/docx/S9sidm4bioXhbwxuXQjcoyz3nzc",
    .commit_id = "906b2f0d9820c17ae0614046885f2079ca7ae17e",
    .loc = "src/namespace/namespace.cc#L4117",
    .need_fix = false,
};
DEFINE_bool(fix_bug_try_to_delete_not_deprecated_block, false, "");
Bug kBugTryToDeleteNotDeprecatedBlock{
    .name = "BugTryToDeleteNotDeprecatedBlock",
    .doc = "https://bytedance.feishu.cn/docx/doxcnNmQFAQBgtUhCqJes4HdEVg",
    .commit_id = "d7e0f306ccc8ec98f5defb5cea4ac62d0c870e1b",
    .loc = "src/namespace/editlog_apply.cc#L987",
    .need_fix = false,
};
DEFINE_bool(fix_bug_apply_op_add_with_overwrite_not_del_bip, false, "");
Bug kBugApplyOpAddWithOverwriteNotDeleteBlockInfoProto{
    .name = "BugApplyOpAddWithOverwriteNotDeleteBlockInfoProto",
    .doc = "https://bytedance.feishu.cn/docx/S9sidm4bioXhbwxuXQjcoyz3nzc",
    .commit_id = "906b2f0d9820c17ae0614046885f2079ca7ae17e",
    .loc = "src/namespace/editlog_apply.cc#L70",
    .need_fix = false,
};

namespace dancenn {
namespace vc {

struct Block {
  // Basic infos.
  uint64_t gen_stamp : 64;
  // 2^57(Byte) = 128(EiB), there is no block that contains so many bytes.
  uint64_t num_bytes : 57;
  uint64_t state : 3;
  uint64_t dangling : 1;
  // About block indices.
  uint64_t has_local_block_index : 1;
  uint64_t has_deprecating_block_index : 1;
  uint64_t has_deprecated_block_index : 1;

  Block()
      : gen_stamp(0),
        num_bytes(0),
        state(0),
        dangling(true),
        has_local_block_index(false),
        has_deprecating_block_index(false),
        has_deprecated_block_index(false) {
  }

  explicit Block(const BlockInfoProto& bip) {
    gen_stamp = bip.gen_stamp();
    num_bytes = bip.num_bytes();
    state = static_cast<int>(bip.state());
    dangling = true;
    has_local_block_index = false;
    has_deprecating_block_index = false;
    has_deprecated_block_index = false;
  }
};
// https://engineering.fb.com/2011/01/03/core-data/scalable-memory-allocation-using-jemalloc/
// jemalloc implements three main size class categories as follows (assuming
// default configuration on a 64-bit system):
// Small: [8], [16, 32, 48, …, 128], ...
static_assert(sizeof(Block) == sizeof(uint64_t) * 2,
              "vc::Block costs too many space");

enum class INodeDanglingState {
  kUnknown = 0,
  kPendingDelete = 1,
  kDangling = 2,
  kInUse = 3,
  kNotExisted = 4,
};
struct INode {
  uint64_t dangling_state : 3;
  uint64_t parent_id : 61;
};
static_assert(sizeof(INode) == sizeof(uint64_t),
              "vc::INode costs too many space");

}  // namespace vc

// If you want to build an INode tree, there are two methods to choose from:
// (parallel) dfs or bfs. Dfs is very slow due to RocksDB's broken locality,
// bfs costs too much memory.
// Moreover, it is difficult to split many sub-inodes of the same parent
// inode into different tasks.
//
// However, you don't need an INode tree most of the time.
// If you need it, building the full path from bottom to top is acceptable.
// Then you scan inodes by RocksDB range.
// Just as https://code.byted.org/inf/dancenn/merge_requests/1289/commits does.
class CorrectnessVerifier : private MetaStorage {
 public:
  CorrectnessVerifier() : MetaStorage(FLAGS_namespace_meta_storage_path) {
    CHECK_EQ(FLAGS_inodemap_num_slice & (FLAGS_inodemap_num_slice - 1), 0);
    CHECK_EQ(FLAGS_blockmap_num_slice & (FLAGS_blockmap_num_slice - 1), 0);
  }

  void Launch() {
    MetaStorage::Launch();
    for (int32_t i = 0; i < FLAGS_dfs_load_from_metastorage_threadpool_count;
         i++) {
      auto tp = std::make_shared<cnetpp::concurrency::ThreadPool>(
          "LoadBM-" + std::to_string(i));
      tp->set_num_threads(
          FLAGS_dfs_load_from_metastorage_thread_count_per_pool);
      tp->Start();
      workers_.emplace_back(tp);
    }
  }

  void Shutdown() {
    MetaStorage::Shutdown();
    for (std::shared_ptr<cnetpp::concurrency::ThreadPool> worker : workers_) {
      worker->Stop(true);
    }
  }

  void Run() {
    LoadNumINodes();
    CHECK_GT(GetNumINodes(), 0);
    // Init block map slices.
    auto estimate_block_num = GetNumINodes() * 3;
    block_map_slices_.resize(FLAGS_blockmap_num_slice);
    for (auto i = 0; i < FLAGS_blockmap_num_slice; i++) {
      block_map_slices_[i].first.reset(new ReadWriteLock);
      block_map_slices_[i].second.reserve(estimate_block_num /
                                          FLAGS_blockmap_num_slice);
    }
    // Init inode map slices.
    inode_map_slices_.resize(FLAGS_inodemap_num_slice);
    for (auto i = 0; i < FLAGS_inodemap_num_slice; i++) {
      inode_map_slices_[i].first.reset(new ReadWriteLock);
      inode_map_slices_[i].second.reserve(GetNumINodes() /
                                          FLAGS_inodemap_num_slice);
    }
    LoadBlockInfoProtos();
    LoadLocalBlocks();
    LoadDeprecatingBlocks();
    LoadDeprecatedBlocks();
    LoadINodes();
    LoadPendingDeleteINodes();
    VerifyINodeIndex();
    FindDanglingINodes();
    VerifyBlockInfoProto();
  }

 private:
  void LoadBlockInfoProtos() {
    BlockID inclusive_lower_block_id_bound = kLastReservedBlockId;
    std::string block_id;
    CHECK(GetNameSystemInfo(kLastAllocatedBlockIdKey, &block_id));
    CHECK_EQ(block_id.length(), sizeof(BlockID));
    BlockID exclusive_upper_block_id_bound =
        platform::ReadBigEndian<BlockID>(&(block_id[0]), 0) + 1;
    const uint64_t kMaximalBlocksCountPerRange = static_cast<uint64_t>(1e6);
    std::vector<std::pair<std::string, std::string>> key_ranges;
    CHECK_LT(inclusive_lower_block_id_bound, exclusive_upper_block_id_bound);
    while (inclusive_lower_block_id_bound < exclusive_upper_block_id_bound) {
      BlockID t = std::min<BlockID>(
          inclusive_lower_block_id_bound + kMaximalBlocksCountPerRange,
          exclusive_upper_block_id_bound);
      key_ranges.emplace_back(std::pair<std::string, std::string>(
          EncodeBlockID(inclusive_lower_block_id_bound), EncodeBlockID(t)));
      inclusive_lower_block_id_bound = t;
    }
    CHECK_EQ(inclusive_lower_block_id_bound, exclusive_upper_block_id_bound);
    auto num_ranges = key_ranges.size();
    std::vector<int64_t> block_num_per_ranges(num_ranges, 0);
    std::atomic<int64_t> num_ranges_remaining(num_ranges);
    for (auto range_idx = 0; range_idx < num_ranges; range_idx++) {
      workers_[range_idx % workers_.size()]->AddTask(
          [this,
           key_range = key_ranges[range_idx],
           p_block_num = &block_num_per_ranges[range_idx],
           &num_ranges_remaining]() {
            ForEachBlockInfoProtoInRange(
                key_range.first,
                key_range.second,
                [this, p_block_num](const BlockInfoProto& bip) {
                  (*p_block_num)++;
                  BlockID blk_id = bip.block_id();
                  auto slice_id = blk_id & (FLAGS_blockmap_num_slice - 1);
                  auto& slice = block_map_slices_[slice_id];
                  std::unique_lock<ReadWriteLock> _(*slice.first);
                  slice.second.emplace(blk_id, vc::Block(bip));
                  return true;
                });
            num_ranges_remaining--;
            return true;
          });
    }
    int64_t last = num_ranges_remaining;
    while (last != 0) {
      LOG(INFO) << "num_ranges_remaining: %d" << last;
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      last = num_ranges_remaining;
    }
    LOG(INFO) << absl::StrFormat(
        "MetaStorage: load %d blocks",
        std::accumulate(
            block_num_per_ranges.begin(), block_num_per_ranges.end(), 0));
  }

  void LoadLocalBlocks() {
    LOG(INFO) << "[LoadLocalBlocks] Begin";
    ScanLocalBlocks([this](const BlockInfoProto& bip) {
      auto slice_id = bip.block_id() & (FLAGS_blockmap_num_slice - 1);
      auto& slice = block_map_slices_[slice_id].second;
      auto it = slice.find(bip.block_id());
      if (it == slice.end()) {
        LOG(ERROR) << "Found dangling local block, B" << bip.block_id();
      }
      it->second.has_local_block_index = true;
    });
    LOG(INFO) << "[LoadLocalBlocks] End";
  }

  void LoadDeprecatingBlocks() {
    LOG(INFO) << "[LoadDeprecatingBlocks] Begin";
    std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
        rocksdb::ReadOptions(), handles_[kDeprecatingBlockCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockID blk_id = DecodeBlockID(it->key());
      CHECK_NE(blk_id, kInvalidBlockID);
      auto slice_id = blk_id & (FLAGS_blockmap_num_slice - 1);
      auto& slice = block_map_slices_[slice_id].second;
      auto it = slice.find(blk_id);
      if (it == slice.end()) {
        LOG(ERROR) << "Found dangling deprecating block, B" << blk_id;
        continue;
      }
      it->second.has_deprecating_block_index = true;
    }
    CHECK(it->status().ok())
        << "LoadDeprecatingBlocks meets error: " << it->status().ToString();
    LOG(INFO) << "[LoadDeprecatingBlocks] End";
  }

  void LoadDeprecatedBlocks() {
    LOG(INFO) << "[LoadDeprecatedBlocks] Begin";
    std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
        rocksdb::ReadOptions(), handles_[kDeprecatedBlockCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockID blk_id = DecodeBlockID(it->key());
      CHECK_NE(blk_id, kInvalidBlockID);
      auto slice_id = blk_id & (FLAGS_blockmap_num_slice - 1);
      auto& slice = block_map_slices_[slice_id].second;
      auto it = slice.find(blk_id);
      if (it == slice.end()) {
        LOG(ERROR) << "Found dangling deprecated block, B" << blk_id;
        continue;
      }
      it->second.has_deprecated_block_index = true;
    }
    CHECK(it->status().ok())
        << "LoadDeprecatedBlocks meets error: " << it->status().ToString();
    LOG(INFO) << "[LoadDeprecatedBlocks] End";
  }

  // Copy from https://code.byted.org/inf/dancenn/merge_requests/1289.
  void LoadINodes() {
    std::vector<std::pair<std::string, std::string>> key_ranges =
        SplitKeyRange(kINodeDefaultCFIndex);
    CHECK(!key_ranges.empty());
    auto num_ranges = key_ranges.size();
    std::vector<int64_t> inode_num_per_ranges(num_ranges, 0);
    std::atomic<int64_t> num_ranges_remaining(num_ranges);
    std::atomic<uint64_t> range_idx(0);
    for (auto range_idx = 0; range_idx < num_ranges; range_idx++) {
      workers_[range_idx % workers_.size()]->AddTask(
          [this,
           key_range = key_ranges[range_idx],
           p_inode_num = &inode_num_per_ranges[range_idx],
           &num_ranges_remaining]() {
            ForEachINodeInRange(
                key_range.first,
                key_range.second,
                [this, p_inode_num](const INode& inode) {
                  // 1.
                  switch (inode.type()) {
                    case INode::kDirectory: {
                    } break;
                    case INode::kFile: {
                      CompareBlockProtoWithBlockInfoProto(inode);
                    } break;
                    default: {
                      LOG(FATAL) << ToJsonCompactString(inode);
                    } break;
                  }
                  // 2.
                  auto slice_id = inode.id() & (FLAGS_inodemap_num_slice - 1);
                  auto& slice = inode_map_slices_[slice_id];
                  std::unique_lock<ReadWriteLock> _(*slice.first);
                  CHECK(slice.second
                            .emplace(
                                inode.id(),
                                vc::INode{.dangling_state = static_cast<int>(
                                              vc::INodeDanglingState::kUnknown),
                                          .parent_id = inode.parent_id()})
                            .second);
                  // 3.
                  (*p_inode_num)++;
                });
            num_ranges_remaining--;
            return true;
          });
    }

    int64_t last = num_ranges_remaining;
    while (last != 0) {
      LOG(INFO) << "num_ranges_remaining: " << last;
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      last = num_ranges_remaining;
    }
    LOG(INFO) << absl::StrFormat(
        "MetaStorage: load %d inodes, expected: %d",
        std::accumulate(
            inode_num_per_ranges.begin(), inode_num_per_ranges.end(), 0),
        GetNumINodes());
  }

  void CompareBlockProtoWithBlockInfoProto(const INode& file) {
    for (const BlockProto& bp : file.blocks()) {
      BlockID blk_id = bp.blockid();
      auto slice_id = blk_id & (FLAGS_blockmap_num_slice - 1);
      auto& slice = block_map_slices_[slice_id];
      std::unique_lock<ReadWriteLock> _(*slice.first);
      auto it = slice.second.find(blk_id);
      if (it == slice.second.end()) {
        LOG(ERROR) << "Missing BlockInfoProto B" << blk_id
                   << ", path: " << BuildFullPath(file.id());
        continue;
      }
      vc::Block& blk = it->second;
      blk.dangling = false;
      if (!(blk.gen_stamp == bp.genstamp() && blk.num_bytes == bp.numbytes())) {
        bool need_fix = false;
        if (blk.gen_stamp < bp.genstamp()) {
          LOG(WARNING) << kBugUpdatePipelineNotUpdateGsOfBlockInfoProto.name
                       << ", B" << blk_id;
          need_fix = kBugUpdatePipelineNotUpdateGsOfBlockInfoProto.need_fix;
        } else if (blk.gen_stamp == bp.genstamp() && blk.num_bytes == 0) {
          LOG(WARNING) << kBugFsyncNotUpdateLenOfBlockInfoProto.name << ", B"
                       << blk_id;
          need_fix = kBugFsyncNotUpdateLenOfBlockInfoProto.need_fix;
        } else {
          LOG(ERROR) << "Not matched BlockInfoProto, {gs:" << blk.gen_stamp
                     << ",len:" << blk.num_bytes << "}"
                     << " != " << ToJsonCompactString(file)
                     << ", path:" << BuildFullPath(file.id());
        }
        // Fix BlockInfoProto.
        if (need_fix) {
          BlockInfoProto bip;
          CHECK(GetBlockInfo(blk_id, &bip));
          bip.set_gen_stamp(bp.genstamp());
          bip.set_num_bytes(bp.numbytes());
          std::string value;
          CHECK(bip.SerializeToString(&value));
          CHECK(rocks_db_
                    ->Put(rocksdb::WriteOptions(),
                          handles_[kBlockInfoProtoCFIndex],
                          EncodeBlockID(bip.block_id()),
                          value)
                    .ok());
        }
      }
    }
  }

  void LoadPendingDeleteINodes() {
    LOG(INFO) << "[LoadPendingDeleteINodes] Begin";
    ScanPendingDeleteCF([this](const INode& inode) {
      auto slice_id = inode.id() & (FLAGS_inodemap_num_slice - 1);
      auto& slice = inode_map_slices_[slice_id].second;
      auto r =
          slice.emplace(inode.id(),
                        vc::INode{.dangling_state = static_cast<int>(
                                      vc::INodeDanglingState::kPendingDelete),
                                  .parent_id = inode.parent_id()});
      CHECK(r.second) << "Found inode of pending delete, "
                      << ToJsonCompactString(inode);
      r.first->second.dangling_state =
          static_cast<int>(vc::INodeDanglingState::kPendingDelete);
      return true;
    });
    LOG(INFO) << "[LoadPendingDeleteINodes] End";
  }

  // TODO(ruanjunbin)
  void VerifyINodeIndex() {
  }

  void FindDanglingINodes() {
    LOG(INFO) << "[FindDanglingINodes] Begin";
    for (auto& slice : inode_map_slices_) {
      for (auto& kv : slice.second) {
        INodeID inode_id = kv.first;
        vc::INode& inode = kv.second;
        inode.dangling_state =
            static_cast<int>(GetINodeDanglingState(inode_id));
      }
    }
    for (auto& slice : inode_map_slices_) {
      for (auto& kv : slice.second) {
        INodeID inode_id = kv.first;
        vc::INode& inode = kv.second;
        switch (static_cast<vc::INodeDanglingState>(inode.dangling_state)) {
          case vc::INodeDanglingState::kInUse:
          case vc::INodeDanglingState::kPendingDelete: {
            // Do nothing.
          } break;
          case vc::INodeDanglingState::kDangling: {
            LOG(ERROR) << "Found dangling inode, " << inode_id;
          } break;
          case vc::INodeDanglingState::kUnknown:
          case vc::INodeDanglingState::kNotExisted:
          default: {
            LOG(FATAL) << inode_id << ", " << inode.dangling_state;
          } break;
        }
      }
    }
    LOG(INFO) << "[FindDanglingINodes] End";
  }

  vc::INodeDanglingState GetINodeDanglingState(INodeID inode_id) {
    auto slice_id = inode_id & (FLAGS_inodemap_num_slice - 1);
    auto& slice = inode_map_slices_[slice_id].second;
    auto it = slice.find(inode_id);
    if (it == slice.end()) {
      return vc::INodeDanglingState::kNotExisted;
    }
    auto& inode = it->second;
    if (inode.dangling_state ==
        static_cast<int>(vc::INodeDanglingState::kUnknown)) {
      if (inode_id == kRootINodeId) {
        inode.dangling_state = static_cast<int>(vc::INodeDanglingState::kInUse);
      } else {
        auto parent_dangling_state = GetINodeDanglingState(inode.parent_id);
        switch (parent_dangling_state) {
          case vc::INodeDanglingState::kPendingDelete:
          case vc::INodeDanglingState::kDangling:
          case vc::INodeDanglingState::kInUse: {
            inode.dangling_state = static_cast<int>(parent_dangling_state);
          } break;
          case vc::INodeDanglingState::kNotExisted: {
            inode.dangling_state =
                static_cast<int>(vc::INodeDanglingState::kDangling);
          } break;
          case vc::INodeDanglingState::kUnknown:
          default: {
            LOG(FATAL) << static_cast<int>(parent_dangling_state);
          }
        }
      }
    }
    auto s = static_cast<vc::INodeDanglingState>(inode.dangling_state);
    CHECK(s != vc::INodeDanglingState::kUnknown);
    CHECK(s != vc::INodeDanglingState::kNotExisted);
    return s;
  }

  void VerifyBlockInfoProto() {
    LOG(INFO) << "[VerifyBlockInfoProto] Begin";
    for (const auto& slice : block_map_slices_) {
      for (const auto& kv : slice.second) {
        BlockID blk_id = kv.first;
        const vc::Block& blk = kv.second;
        const char* err_msg_indices_not_consistent =
            "BlockInfoProto B%d is not consistent with indices"
            ", has_local_block_index: %d"
            ", has_deprecated_block_index: %d";
        switch (static_cast<BlockInfoProto::Status>(blk.state)) {
          case BlockInfoProto::kUnderConstruction:
          case BlockInfoProto::kCommitted:
          case BlockInfoProto::kComplete:
          case BlockInfoProto::kUploadIssued: {
            if ((!blk.has_local_block_index ||
                 blk.has_deprecated_block_index) &&
                !blk.dangling) {
              LOG(ERROR) << absl::StrFormat(err_msg_indices_not_consistent,
                                            blk_id,
                                            blk.has_local_block_index,
                                            blk.has_deprecated_block_index);
            }
          } break;
          case BlockInfoProto::kPersisted: {
            if ((blk.has_local_block_index || blk.has_deprecated_block_index) &&
                !blk.dangling) {
              LOG(ERROR) << absl::StrFormat(err_msg_indices_not_consistent,
                                            blk_id,
                                            blk.has_local_block_index,
                                            blk.has_deprecated_block_index);
            }
          } break;
          case BlockInfoProto::kDeprecated: {
            if (blk.has_local_block_index || !blk.has_deprecated_block_index) {
              LOG(ERROR) << absl::StrFormat(err_msg_indices_not_consistent,
                                            blk_id,
                                            blk.has_local_block_index,
                                            blk.has_deprecated_block_index);
            }
          } break;
          default: {
            LOG(FATAL) << "B" << blk_id << " has unknown state: " << blk.state;
          } break;
        }
        if (blk.dangling && blk.state != BlockInfoProto::kDeprecated &&
            blk.has_deprecated_block_index) {
          LOG(ERROR) << kBugTryToDeleteNotDeprecatedBlock.name << ", B"
                     << blk_id;
          if (kBugTryToDeleteNotDeprecatedBlock.need_fix) {
            BlockInfoProto bip;
            CHECK(GetBlockInfo(blk_id, &bip));
            bip.set_state(BlockInfoProto::kDeprecated);
            auto wb = CreateWriteBatch();
            std::string key = EncodeBlockID(blk_id);
            std::string value;
            CHECK(bip.SerializeToString(&value));
            wb->Put(handles_[kBlockInfoProtoCFIndex], key, value);
            wb->Delete(handles_[kLocalBlockCFIndex], key);
            wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
            wb->Put(handles_[kDeprecatedBlockCFIndex], key, "");
            rocksdb::Status s =
                rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
            CHECK(s.ok()) << s.ToString();
          }
        }
        bool will_be_cleaned =
            blk.has_deprecating_block_index || blk.has_deprecated_block_index;
        if (blk.dangling && !will_be_cleaned) {
          LOG(ERROR) << kBugApplyOpAddWithOverwriteNotDeleteBlockInfoProto.name
                     << ", B" << blk_id;
          if (kBugApplyOpAddWithOverwriteNotDeleteBlockInfoProto.need_fix) {
            auto wb = CreateWriteBatch();
            std::string key = EncodeBlockID(blk_id);
            wb->Delete(handles_[kBlockInfoProtoCFIndex], key);
            wb->Delete(handles_[kLocalBlockCFIndex], key);
            wb->Delete(handles_[kDeprecatingBlockCFIndex], key);
            wb->Delete(handles_[kDeprecatedBlockCFIndex], key);
            rocksdb::Status s =
                rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
            CHECK(s.ok()) << s.ToString();
          }
        }
        if (!blk.dangling && will_be_cleaned) {
          LOG(ERROR) << "Found missing B" << blk_id;
        }
      }
    }
    LOG(INFO) << "[VerifyBlockInfoProto] End";
  }

  std::string BuildFullPath(INodeID inode_id) {
    std::string full_path;
    full_path.reserve(64);
    while (inode_id != kRootINodeId) {
      INode inode;
      if (GetINode(inode_id, &inode) != StatusCode::kOK) {
        full_path = "<NotFound>";
        break;
      }
      full_path.insert(0, inode.name());
      full_path.insert(0, "/");
      inode_id = inode.parent_id();
    }
    return full_path;
  }

  // Copy from https://code.byted.org/inf/dancenn/merge_requests/1289.
  // Delete me if merge above commit.
  std::vector<std::pair<std::string, std::string>> SplitKeyRange(
      uint32_t column_family_index) {
    rocksdb::ColumnFamilyHandle* column_family = handles_[column_family_index];
    rocksdb::ColumnFamilyMetaData column_family_meta_data;
    rocks_db_->GetColumnFamilyMetaData(column_family, &column_family_meta_data);
    CHECK_EQ(column_family_meta_data.name, column_family->GetName());
    CHECK_GT(column_family_meta_data.size, 0);

    // map the level of sst files to the correspond set of smallest
    // keys of those files, for ensuring the ascending order of the
    // keys in first_keys_of_sst_files.
    std::map<uint32_t, std::set<std::string>>
        level_to_smallest_keys_of_sst_files;
    for (const auto& level_meta_data : column_family_meta_data.levels) {
      for (const auto& sst_file_meta_data : level_meta_data.files) {
        level_to_smallest_keys_of_sst_files[level_meta_data.level].insert(
            sst_file_meta_data.smallestkey);
      }
    }
    uint32_t largest_level =
        level_to_smallest_keys_of_sst_files.rbegin()->first;
    // The index start from 0, so index 1 indicates level 2.
    // There is no sst file data in rocksdb at or below level 2,
    // and there is no guarantee that the keys are sorted.
    CHECK_GE(largest_level, 1)
        << "No meta data in sst files in or below level 2";

    const std::set<std::string>& first_keys_set =
        level_to_smallest_keys_of_sst_files[largest_level];
    LOG(INFO) << "Load sst files from largest level " << largest_level;
    std::vector<std::string> first_keys_of_sst_files(first_keys_set.begin(),
                                                     first_keys_set.end());
    std::vector<std::string> first_keys_of_splitted_ranges(0);

    first_keys_of_splitted_ranges = first_keys_of_sst_files;
    // Empty string at the beginning indicates the first key of the whole
    // column family, and can be obtained by calling the GetSmallestKey
    // method.
    first_keys_of_splitted_ranges[0] = "";
    // Empty string indicates a token key that
    // is just bigger than all the keys in this cf, it does not exist but can
    // be achieved by calling the Valid method of rocksdb's iterator.
    first_keys_of_splitted_ranges.push_back("");

    std::vector<std::pair<std::string, std::string>> ranges_splitted;
    // The last key is a token empty string,
    // and i will end at second to last index.
    for (size_t i = 0; i < first_keys_of_splitted_ranges.size() - 1; i++) {
      ranges_splitted.emplace_back(first_keys_of_splitted_ranges[i],
                                   first_keys_of_splitted_ranges[i + 1]);
    }
    return ranges_splitted;
  }

  void ForEachINodeInRange(const std::string& inclusive_lower_key_bound,
                           const std::string& exclusive_upper_key_bound,
                           const std::function<void(const INode&)>& cb) {
    auto transfer_to_readable_key =
        [this](const std::string& key) -> std::string {
      if (key.empty()) {
        return key;
      }
      uint64_t pid;
      uint64_t id;
      std::string name;
      DecodeStoreKey(
          cnetpp::base::StringPiece(key.data(), key.size()), &pid, &name, &id);
      return std::to_string(pid) + "/" + name + "/" + std::to_string(id);
    };
    auto parse_value = [](rocksdb::Slice value, INode* out) {
      return out->ParseFromArray(value.data(), value.size());
    };
    ForEachKeyInRange<INode>(inclusive_lower_key_bound,
                             exclusive_upper_key_bound,
                             kINodeDefaultCFIndex,
                             transfer_to_readable_key,
                             parse_value,
                             cb);
  }

  void ForEachBlockInfoProtoInRange(
      const std::string& inclusive_lower_key_bound,
      const std::string& exclusive_upper_key_bound,
      const std::function<void(const BlockInfoProto&)>& cb) {
    auto transfer_to_readable_key =
        [this](const std::string& key) -> std::string {
      return key.empty() ? "" : std::to_string(DecodeBlockID(key));
    };
    auto parse_value = [](rocksdb::Slice value, BlockInfoProto* out) {
      return out->ParseFromArray(value.data(), value.size());
    };
    ForEachKeyInRange<BlockInfoProto>(inclusive_lower_key_bound,
                                      exclusive_upper_key_bound,
                                      kBlockInfoProtoCFIndex,
                                      transfer_to_readable_key,
                                      parse_value,
                                      cb);
  }

  // Copy from https://code.byted.org/inf/dancenn/merge_requests/1289.
  // Delete me if merge above commit.
  template <typename T>
  void ForEachKeyInRange(
      const std::string& inclusive_lower_key_bound,
      const std::string& exclusive_upper_key_bound,
      uint32_t column_family_index,
      const std::function<std::string(const std::string& key)>&
          transfer_to_readable_key,
      const std::function<bool(rocksdb::Slice value, T* out)>& parse_value,
      const std::function<void(const T&)>& cb) {
    std::string key_range =
        "[" + transfer_to_readable_key(inclusive_lower_key_bound) + "," +
        transfer_to_readable_key(exclusive_upper_key_bound) + ")";
    LOG(INFO) << "[ForEachKeyInRange] Begin scan all keys in key range "
              << key_range;

    rocksdb::ReadOptions opt;
    opt.total_order_seek = true;
    auto iter = std::unique_ptr<rocksdb::Iterator>(
        rocks_db_->NewIterator(opt, handles_[column_family_index]));
    StopWatch sw;
    sw.Start();
    if (inclusive_lower_key_bound.empty()) {
      iter->SeekToFirst();
    } else {
      iter->Seek(inclusive_lower_key_bound);
    }
    uint64_t seek_time = sw.NextStepTime();

    StopWatch sw_per_step;
    sw_per_step.Start();
    uint64_t parse_value_time = 0, execute_cb_time = 0, iter_next_time = 0;
    uint64_t num_keys_scanned = 0;
    while (iter->Valid()) {
      std::string key(iter->key().data(), iter->key().size());
      if (!exclusive_upper_key_bound.empty() &&
          (key >= exclusive_upper_key_bound)) {
        break;
      }
      T element;
      bool parse_value_succeed = parse_value(iter->value(), &element);
      parse_value_time += sw_per_step.NextStepTime();
      if (parse_value_succeed) {
        num_keys_scanned++;
        cb(element);
        execute_cb_time += sw_per_step.NextStepTime();
      } else {
        // Scanning by key range may introduce some old invalid key-value
        // pairs like (0//0, invalid_value), these inodes won't be scanned
        // when traversing the directory tree but can interrupt this method.
        // This bypass check can be removed after removing invalid inodes
        // using the invalid_data_cleaner tools.
        LOG(WARNING) << "Failed to parse inode from meta storage, key string: "
                     << transfer_to_readable_key(key);
      }
      iter->Next();
      iter_next_time += sw_per_step.NextStepTime();
    }
    sw_per_step.Stop();

    LOG(INFO) << "MetaStorage: scan all the keys in the key range " << key_range
              << " of column family " << column_family_index
              << ", count: " << num_keys_scanned  //
              << ", seek time: " << seek_time
              << ", loop time: " << sw.NextStepTime()
              << ", parse value time: " << parse_value_time
              << ", execute cb time: " << execute_cb_time
              << ", iter next time: " << iter_next_time;
    rocksdb::Status st = iter->status();
    CHECK(st.ok()) << st.ToString();
  }

  std::unique_ptr<rocksdb::DB> OpenRocksDB(const rocksdb::DBOptions& opt) {
    if (kBugUpdatePipelineNotUpdateGsOfBlockInfoProto.need_fix ||
        kBugFsyncNotUpdateLenOfBlockInfoProto.need_fix ||
        kBugApplyOpAddWithOverwriteNotDeleteBlockInfoProto.need_fix) {
      return MetaStorage::OpenRocksDB(opt);
    } else {
      rocksdb::DB* p = nullptr;
      rocksdb::Status s = rocksdb::DB::OpenForReadOnly(
          opt, db_path_, column_families_, &handles_, &p);
      CHECK(s.ok()) << "Failed to open DB: " << db_path_
                    << ", status: " << s.ToString();
      return std::unique_ptr<rocksdb::DB>(p);
    }
  }

 private:
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>> workers_;
  std::vector<std::pair<std::unique_ptr<ReadWriteLock>,
                        std::unordered_map<BlockID, vc::Block>>>
      block_map_slices_;
  std::vector<std::pair<std::unique_ptr<ReadWriteLock>,
                        std::unordered_map<INodeID, vc::INode>>>
      inode_map_slices_;
};

}  // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  kBugUpdatePipelineNotUpdateGsOfBlockInfoProto.need_fix =
      FLAGS_fix_bug_update_pipeline_not_update_gs_of_bip;
  kBugFsyncNotUpdateLenOfBlockInfoProto.need_fix =
      FLAGS_fix_bug_fsync_not_update_len_of_bip;
  kBugTryToDeleteNotDeprecatedBlock.need_fix =
      FLAGS_fix_bug_try_to_delete_not_deprecated_block;
  kBugApplyOpAddWithOverwriteNotDeleteBlockInfoProto.need_fix =
      FLAGS_fix_bug_apply_op_add_with_overwrite_not_del_bip;
  dancenn::CorrectnessVerifier inode_scanner;
  inode_scanner.Launch();
  inode_scanner.Run();
  inode_scanner.Shutdown();
  return 0;
}
