// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/21
// Description

#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/tcp/tcp_client.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <chrono>
#include <memory>
#include <regex>
#include <thread>

#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "rpc/pooled_rpc_channel.h"
#include "rpc/rpc_client_options.h"
#include "rpc/rpc_controller.h"
#include "service/client_namenode_service.h"

DEFINE_string(nn_host, "127.0.0.1", "HA rpc host for target DanceNN");
DEFINE_int32(nn_port, 12345678, "HA rpc port for target DanceNN");
DEFINE_string(path, "/not-existed-file", "file path");
DEFINE_bool(recover_lease, false, "call recoverLease or not");

int main(int argc, char* argv[]) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  LOG(WARNING) << std::regex_replace(R"(
    |This tool will:
    |1. Violently grabbing a lease from user.
    |2. Do lease recovery, and dn will close file.
    |Please make sure you have contacted user before using this tool!
    |If you recover lease on a file opening by hdfs cli,
    |hdfs cli will delete it!
    |And you have 5 seconds to exit. Just press Ctrl-C.
  )",
                                     std::regex(".*\\|"),
                                     "");
  using namespace std::chrono_literals;  // NOLINT(build/namespaces)
  std::this_thread::sleep_for(5s);

  dancenn::RpcClientOptions options;
  options.set_max_open_connections_per_user_and_fs(10);
  options.set_max_pending_calls_per_user_and_fs(1100);
  options.set_request_timeout_ms(1000000);
  options.set_send_buffer_size(65536);
  options.set_receive_buffer_size(65536);
  options.set_tcp_receive_buffer_size(65536);
  options.set_tcp_send_buffer_size(65536);
  options.set_network_thread_count(4);
  options.set_client_id("recover_lease");
  options.set_user("tiger");
  options.set_protocol_name("org.apache.hadoop.hdfs.protocol.ClientProtocol");
  options.set_protocol_version(1);

  auto tcp_client = std::make_shared<cnetpp::tcp::TcpClient>();
  tcp_client->Launch("client_cli");
  auto handlers =
      std::make_shared<cnetpp::concurrency::ThreadPool>("force_close_file");
  handlers->set_num_threads(1);
  handlers->Start();
  auto endpoint =
      std::make_shared<cnetpp::base::EndPoint>(FLAGS_nn_host, FLAGS_nn_port);
  auto channel = std::make_shared<dancenn::PooledRpcChannel>(
      tcp_client, options, *endpoint, handlers);
  std::shared_ptr<dancenn::ClientNamenodeService::Stub> client(
      new dancenn::ClientNamenodeService::Stub(channel.get()));

  if (FLAGS_recover_lease) {
    dancenn::RpcController controller;
    cloudfs::RecoverLeaseRequestProto req;
    cloudfs::RecoverLeaseResponseProto resp;
    req.set_src(FLAGS_path);
    req.set_clientname("0123456789ABCDEF");
    client->recoverLease(&controller, &req, &resp, nullptr);
    CHECK(controller.status() == dancenn::RpcStatus::kSuccess)
        << controller.ErrorText();
    LOG(INFO) << "recover lease succeed, resp: " << resp.DebugString();
  }

  channel->Shutdown();
  handlers->Stop(true);
  return 0;
}
