// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/23
// Description

#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/Delete.h>
#include <aws/s3/model/DeleteObjectsRequest.h>
#include <aws/s3/model/ObjectIdentifier.h>
#include <cnetpp/base/csonpp.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <utility>

DECLARE_string(tos_endpoint);
DECLARE_string(tos_bucket);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_string(tos_region);

DEFINE_string(objs_to_delete,
              "[]",
              R"(keys of objects to be deleted, such as ["1.txt", "2.txt"])");

// A useful stack when you meets problem:
// Aws::Http::CurlHttpClient::MakeRequest
// Aws::Client::AWSClient::AttemptOneRequest
// Aws::Client::AWSClient::AttemptExhaustively
// Aws::Client::AWSXMLClient::MakeRequest
// Aws::S3::S3Client::DeleteObjects
//
// Example, delete s3://cloudfs/tmp/dancenn:
// ./builds/delete_tos_objects                   \
//   // There is no "https://" at front!
//   --tos_endpoint=tos-s3-cn-beijing.volces.com \
//   --tos_bucket=cloudfs                        \
//   --tos_access_key_id=xxx                     \
//   --tos_secret_access_key=xxx                 \
//   --tos_region=cn-beijing                     \
//   // There is no "/" at front!
//   --objs_to_delete='["tmp/dancenn"]'
int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  cnetpp::base::Value value;
  CHECK(cnetpp::base::Parser::Deserialize(FLAGS_objs_to_delete, &value));
  CHECK(value.IsArray());
  const cnetpp::base::Array& arr = value.AsArray();

  Aws::S3::Model::Delete objs_to_delete;
  for (auto it = arr.CBegin(); it != arr.CEnd(); it++) {
    CHECK(it->IsString());
    Aws::S3::Model::ObjectIdentifier id;
    id.SetKey(it->AsString());
    objs_to_delete.AddObjects(id);
  }
  // https://bytedance.feishu.cn/docs/doccnnXSEyReUfCJPOeSoKFRXUb
  // The request contains a list of up to 1000 keys that you want to delete.
  Aws::S3::Model::DeleteObjectsRequest request;
  request.SetBucket(FLAGS_tos_bucket);
  request.SetDelete(std::move(objs_to_delete));

  Aws::SDKOptions options;
  options.loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Debug;
  Aws::InitAPI(options);

  Aws::Client::ClientConfiguration clientCfg;
  clientCfg.endpointOverride = FLAGS_tos_endpoint;
  clientCfg.region = FLAGS_tos_region;
  Aws::Auth::AWSCredentials credentials(FLAGS_tos_access_key_id,
                                        FLAGS_tos_secret_access_key);
  Aws::S3::S3Client s3client(credentials, clientCfg);
  Aws::S3::Model::DeleteObjectsOutcome response =
      s3client.DeleteObjects(request);
  CHECK(response.IsSuccess());

  Aws::ShutdownAPI(options);
}
