// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2022/11/28
// Description:
// This tool simply removes dangling inodes,
// deprecating blocks, and deprecated blocks directly.
// And leave the corresponding data in the origin namespace for recycling.

#include <gflags/gflags.h>  // For DECLARE_string, etc.
#include <glog/logging.h>   // For LOG, etc.

#include <cstdint>     // For int64_t.
#include <functional>  // For function.
#include <map>         // For map.
#include <mutex>       // For call_once, mutex, once_flag, unique_lock.
#include <vector>      // For vector.

#include "ufs/persistent_ufs_info.h"  // For PersistentUfsInfo.
#include "base/constants.h"       // For kBlockPoolIdKey, kRootINodeId, etc.
#include "base/path_util.h"       // For NormalizePath, SplitPath.
#include "block_manager/block.h"  // For BlockID.
#include "cnetpp/concurrency/this_thread.h"               // For ThisThread.
#include "cnetpp/concurrency/thread_pool.h"               // For ThreadPool.
#include "namespace/meta_storage.h"                       // For MetaStorage.
#include "namespace/meta_storage_write_task.h"            // For WriteTask.
#include "proto/generated/cloudfs/hdfs.pb.h"              // For BlockProto.
#include "proto/generated/dancenn/block_info_proto.pb.h"  // For BlockInfoProto.
#include "proto/generated/dancenn/inode.pb.h"             // For INode.
#include "proto/generated/dancenn/status_code.pb.h"       // For StatusCode.
#include "rocksdb/options.h"                              // For WriteOptions.

DECLARE_string(namespace_meta_storage_path);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_int32(namespace_type);
DECLARE_string(tos_endpoint);
DECLARE_string(tos_region);
DECLARE_string(tos_bucket);

DEFINE_bool(remove_dangling_inode,
            false,
            "Remove dangling inodes directly, lease tos blocks alone");
DEFINE_bool(remove_deprecating_blocks,
            false,
            "Remove deprecating blocks directly, leave tos blocks alone");
DEFINE_bool(remove_deprecated_blocks,
            false,
            "Remove deprecated blocks, leave tos blocks alone");
DEFINE_bool(preserve_sub_tree, true, "preserve or crop sub tree");
DEFINE_string(sub_tree_path, "/", "Root path of reserved inode tree");
DEFINE_int32(cropper_thread_count,
             32,
             "Use how many threads to crop inode tree?");
DEFINE_bool(modify_namesystem_info, false, "Modify namesystem info or not");
DEFINE_string(blockpool_id, "", "block pool id");
DEFINE_int32(layout_version, 0, "layout version");
DEFINE_int64(ctime, 0, "ctime");
DEFINE_string(cluster_id, "", "cluster id");

namespace dancenn {

const uint32_t kToolINodeDefaultCFIndex = 0;
const uint32_t kToolINodePendingDeleteCFIndex = 1;
const uint32_t kToolBlockInfoProtoCFIndex = 8;
const uint32_t kToolLocalBlockCFIndex = 9;
const uint32_t kToolDeprecatingBlockCFIndex = 10;
const uint32_t kToolDeprecatedBlockCFIndex = 11;

class INodeTreeCropper : private MetaStorage {
 public:
  INodeTreeCropper()
      : MetaStorage(FLAGS_namespace_meta_storage_path),
        worker_("INodeTreeCropper-") {
    worker_.set_num_threads(FLAGS_cropper_thread_count);
  }

  void Launch() {
    MetaStorage::Launch();
    worker_.Start();
    // Init MetaStorage::num_inodes_,
    // otherwise MetaStorage::BatchWrite won't consider num_inodes_delta.
    LoadNumINodes();
  }

  void Shutdown() {
    MetaStorage::Shutdown();
    worker_.Stop();
  }

  void Run() {
    if (FLAGS_remove_dangling_inode) {
      RemoveDanglingINodes();
    }
    if (FLAGS_preserve_sub_tree) {
      RemoveRedundantSubTrees();
    } else {
      RemoveSubTree();
    }
    if (FLAGS_remove_deprecating_blocks) {
      worker_.AddTask([this]() {
        RemoveDeprecatingBlocks();
        return true;
      });
    }
    if (FLAGS_remove_deprecated_blocks) {
      worker_.AddTask([this]() {
        RemoveDeprecatedBlocks();
        return true;
      });
    }

    auto not_finished_task_num =
        worker_.PendingCount() + worker_.NumRunningTasks();
    while (not_finished_task_num != 0) {
      std::this_thread::sleep_for(std::chrono::seconds(5));
      not_finished_task_num =
          worker_.PendingCount() + worker_.NumRunningTasks();
      LOG(INFO) << "Not finished task: " << not_finished_task_num;
    }

    if (FLAGS_modify_namesystem_info) {
      ModifyNameSystemInfo();
    }
    for (auto& kv : pending_delete_inodes_) {
      FlushPendingDeleteItems(&pending_delete_inodes_, &kv.second, 0);
    }
    for (auto& kv : pending_delete_blks_) {
      FlushPendingDeleteItems(&pending_delete_blks_, &kv.second, 0);
    }
    WaitNoPending();
  }

 private:
  // Refer to BlockLoader::DeleteDanglingINodes.
  void RemoveDanglingINodes() {
    ScanPendingDeleteCF([this](const INode& inode) {
      if (inode.type() != INode::kDirectory) {
        LOG(FATAL) << "Invalid file type: " << inode.type()
                   << " when background deleting.";
      }
      worker_.AddTask([this, inode]() {
        RemoveINode(inode);
        return true;
      });
      // DeleteINode(inode, /*dangling=*/true, /*skip_pd_cf=*/false);
      auto wb = CreateWriteBatch();
      auto id = platform::HostToBigEndian(inode.id());
      wb->Delete(
          handles_[kToolINodePendingDeleteCFIndex],
          rocksdb::Slice(reinterpret_cast<const char*>(&id), sizeof(id)));
      rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
      return true;
    });
  }

  void RemoveDeprecatingBlocks() {
    rocksdb::ReadOptions opt;
    opt.total_order_seek = true;
    std::unique_ptr<rocksdb::Iterator> it(
        rocks_db_->NewIterator(opt, handles_[kToolDeprecatingBlockCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockID blk_id = DecodeBlockID(it->key());
      CHECK_NE(blk_id, kInvalidBlockID);
      InsertPendingDeleteBlk(blk_id);
    }
  }

  void RemoveDeprecatedBlocks() {
    rocksdb::ReadOptions opt;
    opt.total_order_seek = true;
    std::unique_ptr<rocksdb::Iterator> it(
        rocks_db_->NewIterator(opt, handles_[kToolDeprecatedBlockCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockID blk_id = DecodeBlockID(it->key());
      CHECK_NE(blk_id, kInvalidBlockID);
      InsertPendingDeleteBlk(blk_id);
    }
  }

  // Call when FLAGS_preserve_sub_tree is false.
  void RemoveSubTree() {
    CHECK(!FLAGS_sub_tree_path.empty());
    std::string normalized_path;
    CHECK(NormalizePath(
        FLAGS_sub_tree_path, /*(useless) username=*/"", &normalized_path));
    std::vector<cnetpp::base::StringPiece> path_components;
    CHECK(SplitPath(normalized_path, &path_components));
    INode inode;
    INodeID inode_id = kRootINodeId;
    for (auto i = 0; i < path_components.size(); ++i) {
      inode.Clear();
      CHECK_EQ(GetINode(inode_id, path_components[i].as_string(), &inode),
               StatusCode::kOK);
      inode_id = inode.id();
    }
    RemoveINode(inode);
  }

  // Called when FLAGS_preserve_sub_tree is true.
  void RemoveRedundantSubTrees() {
    CHECK(!FLAGS_sub_tree_path.empty());
    std::string normalized_path;
    CHECK(NormalizePath(
        FLAGS_sub_tree_path, /*(useless) username=*/"", &normalized_path));
    std::vector<cnetpp::base::StringPiece> path_components;
    CHECK(SplitPath(normalized_path, &path_components));
    RemoveRedundantSubTrees(GetRootINode(), path_components, 0);
  }

  void RemoveRedundantSubTrees(
      const INode& parent,
      const std::vector<cnetpp::base::StringPiece>& path_components,
      int idx) {
    if (idx >= path_components.size()) {
      return;
    }
    ForEachDir(parent.id(),
               parent.name(),
               [this, &path_components, idx](const std::string full_path,
                                             const INode& inode) {
                 if (inode.id() != kRootINodeId) {
                   if (inode.name() != path_components[idx]) {
                     LOG(INFO) << "Remove redundant sub tree: " << full_path;
                     RemoveINode(inode);
                   } else {
                     RemoveRedundantSubTrees(inode, path_components, idx + 1);
                   }
                 }
                 return true;
               });
  }

  void RemoveINode(const INode& inode) {
    switch (inode.type()) {
      case INode::kDirectory: {
        worker_.AddTask([this, inode]() {
          RemoveDir(inode);
          return true;
        });
      } break;
      case INode::kFile: {
        RemoveFile(inode);
      } break;
      default: {
        LOG(FATAL);
      } break;
    }
  }

  void RemoveDir(const INode& dir) {
    CHECK_EQ(dir.type(), INode::kDirectory);
    InsertPendingDeleteINode(dir);
    ForEachDir(dir.id(),
               dir.name(),
               [this](const std::string& full_path, const INode& sub_inode) {
                 RemoveINode(sub_inode);
                 return true;
               });
  }

  void ModifyNameSystemInfo() {
    // LocalSaveFileSystemId
    auto filesystem_id =
        platform::HostToBigEndian(static_cast<uint64_t>(FLAGS_filesystem_id));
    auto filesystem_id_s = cnetpp::base::StringPiece(
        reinterpret_cast<const uint8_t*>(&filesystem_id), sizeof(uint64_t));
    PutNameSystemInfo(kFileSystemIdKey, filesystem_id_s);
    // LocalSaveNameSpaceId
    auto namespace_id =
        platform::HostToBigEndian(static_cast<uint64_t>(FLAGS_namespace_id));
    auto namespace_id_s = cnetpp::base::StringPiece(
        reinterpret_cast<const uint8_t*>(&namespace_id), sizeof(uint64_t));
    PutNameSystemInfo(kNameSpaceIdKey, namespace_id_s);
    // LocalSaveNameSpaceType
    auto namespace_type =
        platform::HostToBigEndian(static_cast<uint32_t>(FLAGS_namespace_type));
    auto namespace_type_s = cnetpp::base::StringPiece(
        reinterpret_cast<const uint8_t*>(&namespace_type), sizeof(uint32_t));
    PutNameSystemInfo(kNameSpaceTypeKey, namespace_type_s);
    // LocalSaveBlockPoolId
    PutNameSystemInfo(kBlockPoolIdKey, FLAGS_blockpool_id);
    // LocalSavePersistentUfsInfo
    PersistentUfsInfo persistent_ufs_info;
    persistent_ufs_info.protocol = PersistentUfsProtocol::kTos;
    TosInfo& tos_info = persistent_ufs_info.tos_info;
    tos_info.endpoint = FLAGS_tos_endpoint;
    tos_info.region = FLAGS_tos_region;
    tos_info.bucket = FLAGS_tos_bucket;
    PutNameSystemInfo(
        kPersistentUfsInfoKey,
        cnetpp::base::Parser::Serialize(persistent_ufs_info.SerializeToJson()));
    // LocalSaveLayoutVersion
    auto layout_version =
        platform::HostToBigEndian(static_cast<uint32_t>(FLAGS_layout_version));
    auto layout_version_s = cnetpp::base::StringPiece(
        reinterpret_cast<const uint8_t*>(&layout_version), sizeof(uint32_t));
    PutNameSystemInfo(kLayoutVersionKey, layout_version_s);
    // LocalSaveCTime
    auto ctime = platform::HostToBigEndian(static_cast<uint64_t>(FLAGS_ctime));
    auto ctime_s = cnetpp::base::StringPiece(
        reinterpret_cast<const uint8_t*>(&ctime), sizeof(uint64_t));
    PutNameSystemInfo(kCTimeKey, ctime_s);
    // LocalSaveClusterId
    PutNameSystemInfo(kClusterIdKey, FLAGS_cluster_id);
  }

  void RemoveFile(const INode& file) {
    CHECK_EQ(file.type(), INode::kFile);
    InsertPendingDeleteINode(file);
    for (const BlockProto& bp : file.blocks()) {
      InsertPendingDeleteBlk(bp.blockid());
    }
  }

  void InsertPendingDeleteINode(const INode& inode) {
    InsertPendingDeleteItem<INode>(&pending_delete_inodes_, inode);
  }

  void Flush(const std::vector<INode>& pending_delete_inodes) {
    auto wb = CreateWriteBatch();
    int64_t num_inodes_delta = 0;
    // Refer to MetaStorage::DeleteINodesAsync.
    // But skip writing about block info proto and namespace usage.
    for (const INode& inode : pending_delete_inodes) {
      std::string key;
      EncodeStoreKey(inode.parent_id(), inode.name(), inode.id(), &key);
      wb->Delete(handles_[kToolINodeDefaultCFIndex], key);
      // FillDeprecatingBlockWriteBatch(inode, wb.get());
      // FillDeleteStatWriteBatch(inode, wb.get());
      num_inodes_delta += UpdateParentIndexWriteBatch(inode, true, wb.get());
    }
    meta_storage::WriteTask write_task;
    write_task.set_wb(std::move(wb));
    write_task.set_num_inodes_delta(num_inodes_delta);
    BatchWrite(std::vector<meta_storage::WriteTask*>{&write_task});
  }

  void InsertPendingDeleteBlk(BlockID blk_id) {
    InsertPendingDeleteItem<BlockID>(&pending_delete_blks_, blk_id);
  }

  void Flush(const std::vector<BlockID>& pending_delete_blks) {
    auto wb = MetaStorage::CreateWriteBatch();
    for (BlockID blk_id : pending_delete_blks) {
      std::string key = EncodeBlockID(blk_id);
      wb->Delete(handles_[kToolBlockInfoProtoCFIndex], key);
      wb->Delete(handles_[kToolLocalBlockCFIndex], key);
      wb->Delete(handles_[kToolDeprecatingBlockCFIndex], key);
      wb->Delete(handles_[kToolDeprecatedBlockCFIndex], key);
    }
    rocks_db_->Write(rocksdb::WriteOptions(), wb.get());
  }

  template <typename T>
  void InsertPendingDeleteItem(
      std::map<int, std::vector<T>>* pending_delete_items_map,
      const T& ele) {
    auto pending_delete_items =
        GetThreadLocalPendingDeleteItems(pending_delete_items_map);
    pending_delete_items->emplace_back(ele);
    FlushPendingDeleteItems(
        pending_delete_items_map, pending_delete_items, 1024 * 1024);
  }

  template <typename T>
  void FlushPendingDeleteItems(
      std::map<int, std::vector<T>>* pending_delete_items_map,
      std::vector<T>* pending_delete_items,
      int max_size) {
    if (pending_delete_items == nullptr) {
      pending_delete_items =
          GetThreadLocalPendingDeleteItems(pending_delete_items_map);
    }
    if (pending_delete_items->size() < max_size) {
      return;
    }
    Flush(*pending_delete_items);
    pending_delete_items->clear();
    pending_delete_items->reserve(max_size);
  }

  template <typename T>
  std::vector<T>* GetThreadLocalPendingDeleteItems(
      std::map<int, std::vector<T>>* pending_delete_items_map) {
    thread_local std::vector<T>* pending_delete_items = nullptr;
    thread_local std::once_flag flag;
    std::call_once(flag, [this, pending_delete_items_map]() {
      int thread_id = cnetpp::concurrency::ThisThread::GetId();
      std::unique_lock<std::mutex> _(mtx_);
      auto r = pending_delete_items_map->emplace(thread_id, std::vector<T>());
      CHECK(r.second);
      pending_delete_items = &r.first->second;
    });
    CHECK_NOTNULL(pending_delete_items);
    return pending_delete_items;
  }

 private:
  cnetpp::concurrency::ThreadPool worker_;
  std::mutex mtx_;
  std::map<int /*thread_id*/, std::vector<INode>> pending_delete_inodes_;
  std::map<int /*thread_id*/, std::vector<BlockID>> pending_delete_blks_;
};

}  // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  dancenn::INodeTreeCropper inode_tree_cropper;
  inode_tree_cropper.Launch();
  inode_tree_cropper.Run();
  inode_tree_cropper.Shutdown();
  return 0;
}