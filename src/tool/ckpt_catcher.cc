// Copyright (c) @ 2024.
// All right reserved.
//
// Author: xuexiang <<EMAIL>>
// Created: 2024/07/31
// Description
//

#include <absl/strings/str_format.h>
#include <gflags/gflags.h>

#include <cstddef>
#include <cstdint>
#include <functional>
#include <memory>
#include <string>

#include "base/defer.h"
#include "base/read_write_lock.h"
#include "edit/deserializer.h"
#include "ha/ha_state.h"
#include "namespace/namespace.h"
#include "namespace/meta_storage.h"
#include "namespace/meta_storage_util.h"
#include "test/mock_safe_mode.h"

DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);
DECLARE_int32(client_veryslow_rpc_handler_count);
DECLARE_int32(client_veryveryslow_rpc_handler_count);
DECLARE_string(java_classpath);
DECLARE_int32(java_heap_size_mb);

DEFINE_string(active_ckpt_path, "", "RocksDB Checkpoint path for Active NN");
DEFINE_string(standby_ckpt_path, "", "RocksDB Checkpoint path for Standby NN");

namespace dancenn {

class CkptCatcher {
 public:
  CkptCatcher(const std::string& active_ckpt_path,
              const std::string& standby_ckpt_path)
      : active_ckpt_path_(active_ckpt_path),
        standby_ckpt_path_(standby_ckpt_path) {}
  ~CkptCatcher() {}
  void Run();

 protected:
  uint64_t GetTxid(const std::string& ckpt_path);
  void StartStandbyNN();
  void ApplyEditLogs(uint64_t txid);
  void CleanupLocalData();
  void StopStandbyNN();

 protected:
  const std::string active_ckpt_path_;
  const std::string standby_ckpt_path_;
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<DatanodeManager> dn_mgr_;
  std::shared_ptr<BlockManager> blk_mgr_;
  std::shared_ptr<EditLogContextBase> edit_ctx_;
  std::shared_ptr<SafeModeBase> safemode_;
  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<JavaRuntime> jvm_;
};

void CkptCatcher::Run() {
  uint64_t active_txid = GetTxid(active_ckpt_path_);
  uint64_t standby_txid = GetTxid(standby_ckpt_path_);
  CHECK_NE(active_txid, kInvalidTxId);
  CHECK_NE(standby_txid, kInvalidTxId);
  CHECK_GE(active_txid, standby_txid);
  StartStandbyNN();
  if (standby_txid < active_txid) {
    ApplyEditLogs(active_txid);
  }
  CleanupLocalData();
  StopStandbyNN();
  standby_txid = GetTxid(standby_ckpt_path_);
  CHECK_EQ(active_txid, standby_txid);
  LOG(INFO) << absl::StrFormat(
      "txid between Active and Standby are aligned to %lu",
      active_txid);
}

uint64_t CkptCatcher::GetTxid(const std::string& ckpt_path) {
  auto ms = std::make_shared<ReadOnlyMetaStorage>(ckpt_path);
  ms->Launch();
  DEFER([ms] () {
    ms->Shutdown();
  });
  return ms->GetLastCkptTxId();
}

void CkptCatcher::StartStandbyNN() {
  // XXX simplified implementation of DanceNN process
  auto jvm = std::make_shared<dancenn::JavaRuntime>(
      FLAGS_java_classpath,
      FLAGS_java_heap_size_mb);
  auto edit_ctx = std::make_shared<dancenn::HAFlexibleEditLogContext>(
      jvm.get(),
      FLAGS_client_slow_rpc_handler_count);
  auto dn_mgr = std::make_shared<DatanodeManager>();
  auto blk_mgr = std::make_shared<BlockManager>(edit_ctx);
  auto barrier = std::make_shared<dancenn::VRWLock>(
      FLAGS_client_slow_rpc_handler_count + FLAGS_client_normal_rpc_handler_count);
  auto safemode = std::make_shared<MockSafeMode>();
  auto ha_state = std::make_shared<HAState>(
      jvm,
      edit_ctx,
      barrier,
      nullptr);

  auto ns = std::make_shared<NameSpace>(standby_ckpt_path_,
                                        edit_ctx,
                                        blk_mgr,
                                        dn_mgr,
                                        nullptr,
                                        std::make_shared<DataCenters>(),
                                        nullptr,
                                        nullptr);
  ns->set_safemode(safemode.get());
  ns->set_ha_state(ha_state.get());
  blk_mgr->set_ha_state(ha_state.get());
  blk_mgr->set_safemode(safemode.get());
  blk_mgr->set_ns(ns.get());
  dn_mgr->set_block_manager(blk_mgr.get());
  ns->Start();

  // TODO(xuex) stop NS::checkpointer_ here
  ns->StopBGDeletionWorker();
  ns->StopLeaseMonitor();
  ns->StartStandby();

  jvm_ = jvm;
  edit_ctx_ = edit_ctx;
  dn_mgr_ = dn_mgr;
  blk_mgr_ = blk_mgr;
  safemode_ = safemode;
  ha_state_ = ha_state;
  ns_ = ns;
}

void CkptCatcher::ApplyEditLogs(uint64_t txid_target) {
  uint64_t txid_applied = ns_->GetLastCkptTxId();
  OpDeSerializer deser;

  edit_ctx_->OpenForRead();
  while (txid_applied != txid_target) {
    auto input = edit_ctx_->CreateInputContext(
        txid_applied + 1, 0, true);
    CHECK(input);

    while (true) {
      std::string ser_op;
      if (!input->ReadOp(&ser_op)) {
        break;
      }
      if (ser_op.length() == 0) {
        break;
      }

      std::shared_ptr<EditLogOp> des_op = deser.Deserialize(ser_op);
      ns_->Apply(des_op);
      txid_applied = des_op->txid();
      if (txid_applied == txid_target) {
        break;
      }
    }
  }

  edit_ctx_->Close();
  ns_->WaitNoPending();
}

void CkptCatcher::CleanupLocalData() {
  // cleanup data that not synced by editlog

  // Pending Delete CF
  ns_->ProcessPendingDeleteCF([] (int64_t) -> bool { return false; });
}

void CkptCatcher::StopStandbyNN() {
  dn_mgr_->Stop();
  dn_mgr_.reset();
  blk_mgr_.reset();
  ha_state_.reset();
  safemode_.reset();
  ns_.reset();
  edit_ctx_.reset();
}

} // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + "");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  dancenn::CkptCatcher ckpt_catcher(FLAGS_active_ckpt_path,
                                    FLAGS_standby_ckpt_path);
  ckpt_catcher.Run();
  return 0;
}
