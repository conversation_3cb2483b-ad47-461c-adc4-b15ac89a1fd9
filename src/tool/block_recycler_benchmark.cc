// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/21
// Description

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <rocksdb/db.h>

#include <string>

#include "base/platform.h"
#include "base/constants.h"
#include "block_manager/block.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/meta_storage_constants.h"
#include "namespace/namespace_stat.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

DEFINE_string(db, "xxx", "RocksDB path.");
DEFINE_uint64(dangling_depring_blk_id_min,
              12345678,
              "Min id of deprecating block without bip");
DEFINE_uint64(dangling_depring_blk_id_max,
              22345678,
              "Max id of deprecating block without bip");
DEFINE_uint64(uploaded_depring_blk_id_min,
              32345678,
              "Min id of uploaded and deprecating block with bip");
DEFINE_uint64(uploaded_depring_blk_id_max,
              42345678,
              "Max id of uploaded and deprecating block with bip");
DEFINE_uint64(non_uploaded_depring_blk_id_min,
              52345678,
              "Min id of non-uploaded and deprecating block with bip");
DEFINE_uint64(non_uploaded_depring_blk_id_max,
              62345678,
              "Min id of non-uploaded and deprecating block with bip");

std::string EncodeBlockID(dancenn::BlockID blk_id) {
  std::string blk_id_str;
  blk_id_str.resize(sizeof(dancenn::BlockID) / sizeof(uint8_t));
  dancenn::platform::WriteBigEndian(
      const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  return blk_id_str;
}

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  LOG(INFO) << "RocksDB path: " << FLAGS_db;
  rocksdb::DBOptions opt;
  opt.create_if_missing = false;
  opt.create_missing_column_families = false;
  std::vector<rocksdb::ColumnFamilyDescriptor> column_families;
  auto cfopt = rocksdb::ColumnFamilyOptions();
  auto nsstat_cfopt = rocksdb::ColumnFamilyOptions(cfopt);
  nsstat_cfopt.merge_operator = std::shared_ptr<rocksdb::MergeOperator>
                                    (new dancenn::INodeStatMergeOperator());
  auto scr_cfopt = rocksdb::ColumnFamilyOptions(cfopt);
  scr_cfopt.merge_operator = std::shared_ptr<rocksdb::MergeOperator>
                                 (new dancenn::StorageClassReportMergeOperator());
  column_families.emplace_back(dancenn::kINodeDefaultCFName, cfopt);
  column_families.emplace_back(dancenn::kINodePendingDeleteCFName, cfopt);
  column_families.emplace_back(dancenn::kNameSystemInfoCFName, cfopt);
  column_families.emplace_back(dancenn::kAccessCounterCFName, cfopt);
  column_families.emplace_back(dancenn::kINodeIndexCFName, cfopt);
  column_families.emplace_back(dancenn::kLegacyBlockPufsInfoCFName, cfopt);
  column_families.emplace_back(dancenn::kLegacyDeprecatedBlockPufsInfoCFName, cfopt);
  column_families.emplace_back(dancenn::kLegacyBlockInfoProtoCFName, cfopt);
  column_families.emplace_back(dancenn::kBlockInfoProtoCFName, cfopt);
  column_families.emplace_back(dancenn::kLocalBlockCFName, cfopt);
  column_families.emplace_back(dancenn::kDeprecatingBlockCFName, cfopt);
  column_families.emplace_back(dancenn::kDeprecatedBlockCFName, cfopt);
  column_families.emplace_back(dancenn::kINodeStatCFName, nsstat_cfopt);
  column_families.emplace_back(dancenn::kLifecyclePolicyCFName, cfopt);
  column_families.emplace_back(dancenn::kStorageClassStatCFName, cfopt);
  column_families.emplace_back(dancenn::kStorageClassReportCFName, scr_cfopt);
  std::vector<rocksdb::ColumnFamilyHandle*> handles;
  rocksdb::DB* p = nullptr;
  CHECK(rocksdb::DB::Open(opt, FLAGS_db, column_families, &handles, &p).ok())
      << "Failed to open RocksDB at " << FLAGS_db;
  CHECK_NOTNULL(p);
  std::unique_ptr<rocksdb::DB> rocks_db(p);

  auto batch_size = 5000;
  std::unique_ptr<rocksdb::WriteBatch> wb(new rocksdb::WriteBatch());
  for (dancenn::BlockID b = FLAGS_dangling_depring_blk_id_min;
       b < FLAGS_dangling_depring_blk_id_min;
       b++) {
    wb->Put(handles[dancenn::kDeprecatingBlockCFIndex], EncodeBlockID(b), "");
    if ((b - FLAGS_dangling_depring_blk_id_min) % batch_size) {
      rocks_db->Write(rocksdb::WriteOptions(), wb.get());
      wb.reset(new rocksdb::WriteBatch());
    }
  }
  rocks_db->Write(rocksdb::WriteOptions(), wb.get());

  wb.reset(new rocksdb::WriteBatch());
  for (dancenn::BlockID b = FLAGS_uploaded_depring_blk_id_min;
       b < FLAGS_uploaded_depring_blk_id_max;
       b++) {
    dancenn::BlockInfoProto bip;
    bip.set_state(dancenn::BlockInfoProto::kPersisted);
    bip.set_block_id(b);
    bip.set_gen_stamp(1000);
    bip.set_num_bytes(1024);
    bip.set_inode_id(dancenn::kLastReservedINodeId + 1);
    bip.set_expected_rep(1);
    bip.set_pufs_name(std::to_string(b));
    wb->Put(
        handles[dancenn::kDeprecatingBlockCFIndex], EncodeBlockID(bip.block_id()), "");
    wb->Put(handles[dancenn::kBlockInfoProtoCFIndex],
            EncodeBlockID(bip.block_id()),
            bip.SerializeAsString());
    if ((b - FLAGS_dangling_depring_blk_id_min) % batch_size) {
      rocks_db->Write(rocksdb::WriteOptions(), wb.get());
      wb.reset(new rocksdb::WriteBatch());
    }
  }
  rocks_db->Write(rocksdb::WriteOptions(), wb.get());

  wb.reset(new rocksdb::WriteBatch());
  for (dancenn::BlockID b = FLAGS_non_uploaded_depring_blk_id_min;
       b < FLAGS_non_uploaded_depring_blk_id_max;
       b++) {
    dancenn::BlockInfoProto bip;
    bip.set_state(dancenn::BlockInfoProto::kPersisted);
    bip.set_block_id(b);
    bip.set_gen_stamp(1000);
    bip.set_num_bytes(1024);
    bip.set_inode_id(dancenn::kLastReservedINodeId + 1);
    bip.set_expected_rep(1);
    wb->Put(
        handles[dancenn::kDeprecatingBlockCFIndex], EncodeBlockID(bip.block_id()), "");
    wb->Put(handles[dancenn::kBlockInfoProtoCFIndex],
            EncodeBlockID(bip.block_id()),
            bip.SerializeAsString());
    if ((b - FLAGS_dangling_depring_blk_id_min) % batch_size) {
      rocks_db->Write(rocksdb::WriteOptions(), wb.get());
      wb.reset(new rocksdb::WriteBatch());
    }
  }
  rocks_db->Write(rocksdb::WriteOptions(), wb.get());
}
