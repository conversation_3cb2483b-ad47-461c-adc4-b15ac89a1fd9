// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/12/13
// Description

#include <absl/strings/str_format.h>         // For StrFormat.

#include <arrow/io/file.h>                   // For FileOutputStream.
#undef RETURN_NOT_OK  // Fix symbol conflict

#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <gflags/gflags.h>                   // For DECLARE_int32, etc.
#include <glog/logging.h>                    // For CHECK, etc.
#include <parquet/column_writer.h>           // For Int64Writer.
#include <parquet/file_writer.h>  // For ParquetFileWriter, RowGroupWriter.
#include <parquet/properties.h>   // For WriterProperties.
#include <parquet/schema.h>       // For GroupNode, NodeVector, PrimitiveNode.
#include <parquet/types.h>        // For LogicalType, Repetition, Type.
#include <rocksdb/iterator.h>     // For Iterator.

#include <memory>   // For shared_ptr, static_pointer_cast, unique_ptr.
#include <string>   // For string.
#include <utility>  // For pair.
#include <vector>   // For vector.

#include "base/constants.h"                    // For kLastAllocatedBlockIdKey.
#include "base/stop_watch.h"                   // For StopWatch.
#include "block_manager/block.h"               // For BlockID, kInvalidBlockID.
#include "namespace/inode.h"                   // For INodeID.
#include "namespace/meta_storage.h"            // For ReadOnlyMetaStorage.
#include "namespace/meta_storage_constants.h"  // For kINodeDefaultCFIndex, etc.
#include "proto/generated/cloudfs/hdfs.pb.h"  // For BlockProto.
#include "proto/generated/dancenn/block_info_proto.pb.h"  // For BlockInfoProto.
#include "proto/generated/dancenn/inode.pb.h"  // For INode, INodeParentInfoPB.

DECLARE_string(namespace_meta_storage_path);
DECLARE_int32(dfs_load_from_metastorage_threadpool_count);
DECLARE_int32(dfs_load_from_metastorage_thread_count_per_pool);
DEFINE_string(output_dir, "/tmp", "Output dir location");
DEFINE_int32(rows_per_row_group, 2000000, "How many rows per row group?");

namespace dancenn {
namespace dumper {

using parquet::ConvertedType;
using parquet::LogicalType;
using parquet::Repetition;
using parquet::Type;
using parquet::schema::GroupNode;
using parquet::schema::PrimitiveNode;

// https://github.com/apache/arrow/blob/apache-arrow-4.0.1/cpp/examples/parquet/low_level_api/reader_writer.cc
class ParquetWriter {
 public:
  void Init(const std::string& out_file_path) {
    using FileClass = arrow::io::FileOutputStream;
    std::shared_ptr<FileClass> out_file;
    PARQUET_ASSIGN_OR_THROW(out_file, FileClass::Open(out_file_path));
    auto schema = GetSchema();
    parquet::WriterProperties::Builder builder;
    builder.compression(parquet::Compression::UNCOMPRESSED);
    std::shared_ptr<parquet::WriterProperties> props = builder.build();
    file_writer_ = parquet::ParquetFileWriter::Open(out_file, schema, props);
    rg_writer_ = nullptr;
    current_row_index_in_rowgroup_ = 0;
  }

  virtual ~ParquetWriter() {
    if (rg_writer_) {
      rg_writer_->Close();
      rg_writer_ = nullptr;
    }
    file_writer_->Close();
  }

 protected:
  virtual std::shared_ptr<parquet::schema::GroupNode> GetSchema() = 0;

  void SetRowGroupWriter() {
    if (current_row_index_in_rowgroup_ >= FLAGS_rows_per_row_group) {
      current_row_index_in_rowgroup_ = 0;
      rg_writer_->Close();
      rg_writer_ = nullptr;
    }
    if (!rg_writer_) {
      rg_writer_ = file_writer_->AppendBufferedRowGroup();
    }
    current_row_index_in_rowgroup_++;
  }

 protected:
  std::shared_ptr<parquet::ParquetFileWriter> file_writer_;
  parquet::RowGroupWriter* rg_writer_;
  int current_row_index_in_rowgroup_;
};

class BlockInfoProtoParquetWriter : public ParquetWriter {
 public:
  void WriteRow(const BlockInfoProto& bip) {
    SetRowGroupWriter();
    int64_t block_id = bip.block_id();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(0))
        ->WriteBatch(1, nullptr, nullptr, &block_id);
    int64_t gen_stamp = bip.gen_stamp();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(1))
        ->WriteBatch(1, nullptr, nullptr, &gen_stamp);
    int64_t num_bytes = bip.num_bytes();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(2))
        ->WriteBatch(1, nullptr, nullptr, &num_bytes);
    int64_t state = bip.state();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(3))
        ->WriteBatch(1, nullptr, nullptr, &state);
    int64_t inode_id = bip.inode_id();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(4))
        ->WriteBatch(1, nullptr, nullptr, &inode_id);
    {
      parquet::ByteArray value;
      value.ptr = reinterpret_cast<const uint8_t*>(bip.pufs_name().c_str());
      value.len = bip.pufs_name().size();
      static_cast<parquet::ByteArrayWriter*>(rg_writer_->column(5))
          ->WriteBatch(1, nullptr, nullptr, &value);
    }
  }

 protected:
  std::shared_ptr<GroupNode> GetSchema() override {
    parquet::schema::NodeVector fields;
    fields.push_back(PrimitiveNode::Make(
        "block_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "gen_stamp", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "num_bytes", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "state", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "inode_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make("pufs_name",
                                         Repetition::REQUIRED,
                                         Type::BYTE_ARRAY,
                                         ConvertedType::NONE));
    return std::static_pointer_cast<GroupNode>(
        GroupNode::Make("schema", Repetition::REQUIRED, fields));
  }
};

class BlockIndexParquetWriter : public ParquetWriter {
 public:
  void WriteRow(BlockID block_id) {
    SetRowGroupWriter();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(0))
        ->WriteBatch(
            1, nullptr, nullptr, reinterpret_cast<int64_t*>(&block_id));
  }

 protected:
  virtual std::shared_ptr<GroupNode> GetSchema() override {
    parquet::schema::NodeVector fields;
    fields.push_back(PrimitiveNode::Make(
        "block_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    return std::static_pointer_cast<GroupNode>(
        GroupNode::Make("schema", Repetition::REQUIRED, fields));
  }
};

class BlockProtoParquetWriter : public ParquetWriter {
 public:
  void WriteRow(const BlockProto& bp, INodeID inode_id) {
    SetRowGroupWriter();
    int64_t block_id = bp.blockid();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(0))
        ->WriteBatch(1, nullptr, nullptr, &block_id);
    int64_t gen_stamp = bp.genstamp();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(1))
        ->WriteBatch(1, nullptr, nullptr, &gen_stamp);
    int64_t num_bytes = bp.numbytes();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(2))
        ->WriteBatch(1, nullptr, nullptr, &num_bytes);
    static_cast<parquet::Int64Writer*>(rg_writer_->column(3))
        ->WriteBatch(
            1, nullptr, nullptr, reinterpret_cast<int64_t*>(&inode_id));
  }

 protected:
  virtual std::shared_ptr<GroupNode> GetSchema() override {
    parquet::schema::NodeVector fields;
    fields.push_back(PrimitiveNode::Make(
        "block_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "gen_stamp", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "num_bytes", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "inode_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    return std::static_pointer_cast<GroupNode>(
        GroupNode::Make("schema", Repetition::REQUIRED, fields));
  }
};

class INodeParquetWriter : public ParquetWriter {
 public:
  void WriteRow(const INode& inode) {
    SetRowGroupWriter();
    int64_t id = inode.id();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(0))
        ->WriteBatch(1, nullptr, nullptr, &id);
    int64_t parent_id = inode.parent_id();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(1))
        ->WriteBatch(1, nullptr, nullptr, &parent_id);
    int32_t type = inode.type();
    static_cast<parquet::Int32Writer*>(rg_writer_->column(2))
        ->WriteBatch(1, nullptr, nullptr, &type);
    int32_t status = inode.status();
    static_cast<parquet::Int32Writer*>(rg_writer_->column(3))
        ->WriteBatch(1, nullptr, nullptr, &status);
  }

 protected:
  virtual std::shared_ptr<GroupNode> GetSchema() override {
    parquet::schema::NodeVector fields;
    fields.push_back(PrimitiveNode::Make(
        "id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "parent_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "type", Repetition::REQUIRED, Type::INT32, ConvertedType::NONE));
    fields.push_back(PrimitiveNode::Make(
        "status", Repetition::REQUIRED, Type::INT32, ConvertedType::NONE));
    return std::static_pointer_cast<GroupNode>(
        GroupNode::Make("schema", Repetition::REQUIRED, fields));
  }
};

class INodeIndexParquetWriter : public ParquetWriter {
 public:
  void WriteRow(INodeID inode_id, const INodeParentInfoPB& inode_index) {
    SetRowGroupWriter();
    static_cast<parquet::Int64Writer*>(rg_writer_->column(0))
        ->WriteBatch(
            1, nullptr, nullptr, reinterpret_cast<int64_t*>(&inode_id));
  }

 protected:
  virtual std::shared_ptr<GroupNode> GetSchema() override {
    parquet::schema::NodeVector fields;
    fields.push_back(PrimitiveNode::Make(
        "inode_id", Repetition::REQUIRED, Type::INT64, ConvertedType::NONE));
    return std::static_pointer_cast<GroupNode>(
        GroupNode::Make("schema", Repetition::REQUIRED, fields));
  }
};

class Dumper : protected ReadOnlyMetaStorage {
 public:
  Dumper() : ReadOnlyMetaStorage(FLAGS_namespace_meta_storage_path) {
  }

  void Launch() {
    ReadOnlyMetaStorage::Launch();
    for (int32_t i = 0; i < FLAGS_dfs_load_from_metastorage_threadpool_count;
         i++) {
      auto tp = std::make_shared<cnetpp::concurrency::ThreadPool>(
          "LoadBM-" + std::to_string(i));
      tp->set_num_threads(
          FLAGS_dfs_load_from_metastorage_thread_count_per_pool);
      tp->Start();
      workers_.emplace_back(tp);
    }
  }

  void Shutdown() {
    ReadOnlyMetaStorage::Shutdown();
    for (std::shared_ptr<cnetpp::concurrency::ThreadPool> worker : workers_) {
      worker->Stop(true);
    }
  }

 protected:
  // Copy from https://code.byted.org/inf/dancenn/merge_requests/1289.
  // Delete me if merge above commit.
  std::vector<std::pair<std::string, std::string>> SplitKeyRanges(
      uint32_t column_family_index) {
    rocksdb::ColumnFamilyHandle* column_family = handles_[column_family_index];
    rocksdb::ColumnFamilyMetaData column_family_meta_data;
    rocks_db_->GetColumnFamilyMetaData(column_family, &column_family_meta_data);
    CHECK_EQ(column_family_meta_data.name, column_family->GetName());
    CHECK_GT(column_family_meta_data.size, 0);

    // map the level of sst files to the correspond set of smallest
    // keys of those files, for ensuring the ascending order of the
    // keys in first_keys_of_sst_files.
    std::map<uint32_t, std::set<std::string>>
        level_to_smallest_keys_of_sst_files;
    for (const auto& level_meta_data : column_family_meta_data.levels) {
      for (const auto& sst_file_meta_data : level_meta_data.files) {
        level_to_smallest_keys_of_sst_files[level_meta_data.level].insert(
            sst_file_meta_data.smallestkey);
      }
    }
    uint32_t largest_level =
        level_to_smallest_keys_of_sst_files.rbegin()->first;
    // The index start from 0, so index 1 indicates level 2.
    // There is no sst file data in rocksdb at or below level 2,
    // and there is no guarantee that the keys are sorted.
    CHECK_GE(largest_level, 1)
        << "No meta data in sst files in or below level 2";

    const std::set<std::string>& first_keys_set =
        level_to_smallest_keys_of_sst_files[largest_level];
    LOG(INFO) << "Load sst files from largest level " << largest_level;
    std::vector<std::string> first_keys_of_sst_files(first_keys_set.begin(),
                                                     first_keys_set.end());
    std::vector<std::string> first_keys_of_splitted_ranges(0);

    first_keys_of_splitted_ranges = first_keys_of_sst_files;
    // Empty string at the beginning indicates the first key of the whole
    // column family, and can be obtained by calling the GetSmallestKey
    // method.
    first_keys_of_splitted_ranges[0] = "";
    // Empty string indicates a token key that
    // is just bigger than all the keys in this cf, it dose not exist but can
    // be achieved by calling the Valid method of rocksdb's iterator.
    first_keys_of_splitted_ranges.push_back("");

    std::vector<std::pair<std::string, std::string>> ranges_splitted;
    // The last key is an token empty string,
    // and i will end at second to last index.
    for (size_t i = 0; i < first_keys_of_splitted_ranges.size() - 1; i++) {
      ranges_splitted.emplace_back(first_keys_of_splitted_ranges[i],
                                   first_keys_of_splitted_ranges[i + 1]);
    }
    return ranges_splitted;
  }

  // Copy from https://code.byted.org/inf/dancenn/merge_requests/1289.
  // Delete me if merge above commit.
  template <typename Key, typename Value>
  void ForEachKeyInRange(
      const std::string& inclusive_lower_key_bound,
      const std::string& exclusive_upper_key_bound,
      uint32_t column_family_index,
      const std::function<std::string(const std::string& key)>&
          transfer_to_readable_key,
      const std::function<bool(rocksdb::Slice key, Key* out)>& parse_key,
      const std::function<bool(rocksdb::Slice value, Value* out)>& parse_value,
      const std::function<void(const Key& key, const Value&)>& cb) {
    std::string key_range =
        "[" + transfer_to_readable_key(inclusive_lower_key_bound) + "," +
        transfer_to_readable_key(exclusive_upper_key_bound) + ")";
    LOG(INFO) << "[ForEachKeyInRange] Begin scan all keys in key range "
              << key_range;

    rocksdb::ReadOptions opt;
    // Important!
    opt.total_order_seek = true;
    auto iter = std::unique_ptr<rocksdb::Iterator>(
        rocks_db_->NewIterator(opt, handles_[column_family_index]));
    StopWatch sw;
    sw.Start();
    if (inclusive_lower_key_bound.empty()) {
      iter->SeekToFirst();
    } else {
      iter->Seek(inclusive_lower_key_bound);
    }
    uint64_t seek_time = sw.NextStepTime();

    StopWatch sw_per_step;
    sw_per_step.Start();
    uint64_t parse_value_time = 0, execute_cb_time = 0, iter_next_time = 0;
    uint64_t num_keys_scanned = 0;
    while (iter->Valid()) {
      std::string key(iter->key().data(), iter->key().size());
      if (!exclusive_upper_key_bound.empty() &&
          (key >= exclusive_upper_key_bound)) {
        break;
      }
      Key parsed_key;
      CHECK(parse_key(iter->key(), &parsed_key));
      Value parsed_value;
      bool parse_value_succeed = parse_value(iter->value(), &parsed_value);
      parse_value_time += sw_per_step.NextStepTime();
      if (parse_value_succeed) {
        num_keys_scanned++;
        cb(parsed_key, parsed_value);
        execute_cb_time += sw_per_step.NextStepTime();
      } else {
        // Scanning by key range may introduce some old invalid key-value
        // pairs like (0//0, invalid_value), these inodes won't be scanned
        // when traversing the directory tree but can interrupt this method.
        // This bypass check can be removed after removing invalid inodes
        // using the invalid_data_cleaner tools.
        LOG(WARNING) << "Failed to parse inode from meta storage, key string: "
                     << transfer_to_readable_key(key);
      }
      iter->Next();
      iter_next_time += sw_per_step.NextStepTime();
    }
    sw_per_step.Stop();

    LOG(INFO) << "MetaStorage: scan all the keys in the key range " << key_range
              << " of column family " << column_family_index
              << ", count: " << num_keys_scanned  //
              << ", seek time: " << seek_time
              << ", loop time: " << sw.NextStepTime()
              << ", parse value time: " << parse_value_time
              << ", execute cb time: " << execute_cb_time
              << ", iter next time: " << iter_next_time;
    rocksdb::Status st = iter->status();
    CHECK(st.ok()) << st.ToString();
  }

 protected:
  std::vector<std::shared_ptr<cnetpp::concurrency::ThreadPool>> workers_;
};

class BlockInfoProtoDumper : public Dumper {
 public:
  void Dump() {
    DumpBlockInfoProto();
    DumpBlockIndex(kLocalBlockCFIndex, kLocalBlockCFName);
    DumpBlockIndex(kDeprecatingBlockCFIndex, kDeprecatingBlockCFName);
    DumpBlockIndex(kDeprecatedBlockCFIndex, kDeprecatedBlockCFName);
  }

 private:
  void DumpBlockInfoProto() {
    auto key_ranges = SplitKeyRanges();
    auto num_ranges = key_ranges.size();
    std::atomic<int64_t> num_ranges_remaining(num_ranges);
    for (auto range_idx = 0; range_idx < num_ranges; range_idx++) {
      workers_[range_idx % workers_.size()]->AddTask(
          [this,
           range_idx,
           key_range = key_ranges[range_idx],
           &num_ranges_remaining]() {
            BlockInfoProtoParquetWriter writer;
            writer.Init(absl::StrFormat("%s/%s/%d.parquet",
                                        FLAGS_output_dir,
                                        kBlockInfoProtoCFName,
                                        range_idx));
            ForEachBlockInfoProtoInRange(
                key_range.first,
                key_range.second,
                [this, &writer](const BlockInfoProto& bip) {
                  writer.WriteRow(bip);
                  return true;
                });
            num_ranges_remaining--;
            return true;
          });
    }
    int64_t last = num_ranges_remaining;
    while (last != 0) {
      LOG(INFO) << "num_ranges_remaining: " << last;
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      last = num_ranges_remaining;
    }
  }

  std::vector<std::pair<std::string, std::string>> SplitKeyRanges() {
    BlockID inclusive_lower_block_id_bound = kLastReservedBlockId;
    std::string block_id;
    CHECK(GetNameSystemInfo(kLastAllocatedBlockIdKey, &block_id));
    CHECK_EQ(block_id.length(), sizeof(BlockID));
    BlockID exclusive_upper_block_id_bound =
        platform::ReadBigEndian<BlockID>(&(block_id[0]), 0) + 1;
    const uint64_t kMaximalBlocksCountPerRange = static_cast<uint64_t>(1e6);
    std::vector<std::pair<std::string, std::string>> key_ranges;
    CHECK_LT(inclusive_lower_block_id_bound, exclusive_upper_block_id_bound);
    while (inclusive_lower_block_id_bound < exclusive_upper_block_id_bound) {
      BlockID t = std::min<BlockID>(
          inclusive_lower_block_id_bound + kMaximalBlocksCountPerRange,
          exclusive_upper_block_id_bound);
      key_ranges.emplace_back(std::pair<std::string, std::string>(
          EncodeBlockID(inclusive_lower_block_id_bound), EncodeBlockID(t)));
      inclusive_lower_block_id_bound = t;
    }
    CHECK_EQ(inclusive_lower_block_id_bound, exclusive_upper_block_id_bound);
    return key_ranges;
  }

  void ForEachBlockInfoProtoInRange(
      const std::string& inclusive_lower_key_bound,
      const std::string& exclusive_upper_key_bound,
      const std::function<void(const BlockInfoProto&)>& cb) {
    auto transfer_to_readable_key =
        [this](const std::string& key) -> std::string {
      return key.empty() ? "" : std::to_string(DecodeBlockID(key));
    };
    auto parse_key = [](rocksdb::Slice key, BlockID* out) {
      // No need to parse block id.
      return true;
    };
    auto parse_value = [](rocksdb::Slice value, BlockInfoProto* out) {
      return out->ParseFromArray(value.data(), value.size());
    };
    ForEachKeyInRange<BlockID, BlockInfoProto>(
        inclusive_lower_key_bound,
        exclusive_upper_key_bound,
        kBlockInfoProtoCFIndex,
        transfer_to_readable_key,
        parse_key,
        parse_value,
        [&cb](const BlockID& key, const BlockInfoProto& value) { cb(value); });
  }

  void DumpBlockIndex(uint32_t cf_index, const char* cf_name) {
    BlockIndexParquetWriter writer;
    writer.Init(absl::StrFormat("%s/%s.parquet", FLAGS_output_dir, cf_name));
    std::unique_ptr<rocksdb::Iterator> it(
        rocks_db_->NewIterator(rocksdb::ReadOptions(), handles_[cf_index]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockID blk_id = DecodeBlockID(it->key());
      writer.WriteRow(blk_id);
    }
  }
};

class INodeDumper : public Dumper {
 public:
  void Dump() {
    DumpDefaultINode();
    DumpPendingDeleteINode();
    DumpINodeIndex();
  }

 private:
  void DumpDefaultINode() {
    std::vector<std::pair<std::string, std::string>> key_ranges =
        SplitKeyRanges(kINodeDefaultCFIndex);
    CHECK(!key_ranges.empty());
    auto num_ranges = key_ranges.size();
    std::atomic<int64_t> num_ranges_remaining(num_ranges);
    std::atomic<uint64_t> range_idx(0);
    for (auto range_idx = 0; range_idx < num_ranges; range_idx++) {
      workers_[range_idx % workers_.size()]->AddTask(
          [this,
           range_idx,
           key_range = key_ranges[range_idx],
           &num_ranges_remaining]() {
            INodeParquetWriter inode_writer;
            inode_writer.Init(absl::StrFormat("%s/%s/inode-%d.parquet",
                                              FLAGS_output_dir,
                                              kINodeDefaultCFName,
                                              range_idx));
            BlockProtoParquetWriter bp_writer;
            bp_writer.Init(absl::StrFormat("%s/%s/bp-%d.parquet",
                                           FLAGS_output_dir,
                                           kINodeDefaultCFName,
                                           range_idx));
            ForEachINodeInRange(
                key_range.first,
                key_range.second,
                [this, &inode_writer, &bp_writer](const INode& inode) {
                  inode_writer.WriteRow(inode);
                  for (const auto& bp : inode.blocks()) {
                    bp_writer.WriteRow(bp, inode.id());
                  }
                });
            num_ranges_remaining--;
            return true;
          });
    }
    int64_t last = num_ranges_remaining;
    while (last != 0) {
      LOG(INFO) << "num_ranges_remaining: " << last;
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      last = num_ranges_remaining;
    }
  }

  void ForEachINodeInRange(const std::string& inclusive_lower_key_bound,
                           const std::string& exclusive_upper_key_bound,
                           const std::function<void(const INode&)>& cb) {
    auto transfer_to_readable_key =
        [this](const std::string& key) -> std::string {
      if (key.empty()) {
        return key;
      }
      uint64_t pid;
      uint64_t id;
      std::string name;
      DecodeStoreKey(
          cnetpp::base::StringPiece(key.data(), key.size()), &pid, &name, &id);
      return std::to_string(pid) + "/" + name + "/" + std::to_string(id);
    };
    auto parse_key = [](rocksdb::Slice key, INodeID* out) {
      // No need to parse key.
      return true;
    };
    auto parse_value = [](rocksdb::Slice value, INode* out) {
      return out->ParseFromArray(value.data(), value.size());
    };
    ForEachKeyInRange<INodeID, INode>(
        inclusive_lower_key_bound,
        exclusive_upper_key_bound,
        kINodeDefaultCFIndex,
        transfer_to_readable_key,
        parse_key,
        parse_value,
        [&cb](const INodeID& key, const INode& value) { cb(value); });
  }

  void DumpPendingDeleteINode() {
    INodeParquetWriter inode_writer;
    inode_writer.Init(absl::StrFormat(
        "%s/%s/inode.parquet", FLAGS_output_dir, kINodePendingDeleteCFName));
    BlockProtoParquetWriter bp_writer;
    bp_writer.Init(absl::StrFormat(
        "%s/%s/bp.parquet", FLAGS_output_dir, kINodePendingDeleteCFName));
    ScanPendingDeleteCF([&inode_writer, &bp_writer](const INode& inode) {
      inode_writer.WriteRow(inode);
      for (const auto& bp : inode.blocks()) {
        bp_writer.WriteRow(bp, inode.id());
      }
      return true;
    });
  }

  void DumpINodeIndex() {
    std::vector<std::pair<std::string, std::string>> key_ranges =
        SplitKeyRanges(kINodeIndexCFIndex);
    CHECK(!key_ranges.empty());
    auto num_ranges = key_ranges.size();
    std::atomic<int64_t> num_ranges_remaining(num_ranges);
    std::atomic<uint64_t> range_idx(0);
    for (auto range_idx = 0; range_idx < num_ranges; range_idx++) {
      workers_[range_idx % workers_.size()]->AddTask(
          [this,
           range_idx,
           key_range = key_ranges[range_idx],
           &num_ranges_remaining]() {
            INodeIndexParquetWriter writer;
            writer.Init(absl::StrFormat("%s/%s/inode-%d.parquet",
                                        FLAGS_output_dir,
                                        kINodeIndexCFName,
                                        range_idx));
            ForEachINodeIndexInRange(
                key_range.first,
                key_range.second,
                [this, &writer](const INodeID& inode_id,
                                const INodeParentInfoPB& inode_index) {
                  writer.WriteRow(inode_id, inode_index);
                });
            num_ranges_remaining--;
            return true;
          });
    }
    int64_t last = num_ranges_remaining;
    while (last != 0) {
      LOG(INFO) << "num_ranges_remaining: " << last;
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      last = num_ranges_remaining;
    }
  }

  void ForEachINodeIndexInRange(
      const std::string& inclusive_lower_key_bound,
      const std::string& exclusive_upper_key_bound,
      const std::function<void(const INodeID& key,
                               const INodeParentInfoPB& value)>& cb) {
    auto transfer_to_readable_key =
        [this](const std::string& key) -> std::string {
      if (key.empty()) {
        return key;
      }
      CHECK_EQ(key.size(), 10);
      CHECK_EQ(key[0], /*INodeIndexType::kParentIndex=*/0);
      CHECK_EQ(key[1], '/');
      return std::to_string(platform::ReadBigEndian<INodeID>(key.data(), 2));
    };
    auto parse_key = [](rocksdb::Slice key, INodeID* out) {
      CHECK_EQ(key.size(), 10);
      CHECK_EQ(key[0], /*INodeIndexType::kParentIndex=*/0);
      CHECK_EQ(key[1], '/');
      *out = platform::ReadBigEndian<INodeID>(key.data(), 2);
      return true;
    };
    auto parse_value = [](rocksdb::Slice value, INodeParentInfoPB* out) {
      return out->ParseFromArray(value.data(), value.size());
    };
    ForEachKeyInRange<INodeID, INodeParentInfoPB>(inclusive_lower_key_bound,
                                                  exclusive_upper_key_bound,
                                                  kINodeIndexCFIndex,
                                                  transfer_to_readable_key,
                                                  parse_key,
                                                  parse_value,
                                                  cb);
  }
};

}  // namespace dumper
}  // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  dancenn::dumper::BlockInfoProtoDumper block_info_proto_dumper;
  block_info_proto_dumper.Launch();
  block_info_proto_dumper.Dump();
  block_info_proto_dumper.Shutdown();
  dancenn::dumper::INodeDumper inode_dumper;
  inode_dumper.Launch();
  inode_dumper.Dump();
  inode_dumper.Shutdown();
}
