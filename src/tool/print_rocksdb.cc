// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2021/08/17
// Description

#include <absl/strings/str_format.h>   // For StrFormat.
#include <cnetpp/base/string_piece.h>  // For StringPiece.
#include <gflags/gflags.h>  // For DEFINE_string, SetUsageMessage, etc.
#include <glog/logging.h>   // For CHECK, LOG.
#include <rocksdb/db.h>     // For Iterator.

#include <cstdint>  // For uint32_t, uint64_t.
#include <memory>   // For unique_ptr.
#include <string>   // For string.
#include <vector>   // For vector.

#include "absl/strings/substitute.h"
#include "base/pb_converter.h"               // For PBConverter.
#include "base/platform.h"                   // For ReadBigEndian.
#include "base/to_json_string.h"             // For ToJsonCompactString.
#include "block_manager/block.h"             // For BlockID.
#include "block_manager/block_info_proto.h"  // For BlockInfoProto.
#include "datanode_manager/datanode_info.h"  // For DatanodeID, kInvalidDatanodeID.
#include "namespace/inode.h"                 // For INode, INodeID.
#include "namespace/meta_storage.h"          // For ReadOnlyMetaStorage.
#include "namespace/meta_storage_constants.h"            // For constants.
#include "namespace/namespace_stat.h"                    // For INodeStat.
#include "proto/generated/dancenn/namesystem_info.pb.h"  // For EditLogConf.
#include "proto/generated/dancenn/status_code.pb.h"      // For StatusCode.

DECLARE_string(namespace_meta_storage_path);
DECLARE_bool(drop_storage_class_report);

DEFINE_string(column_family,
              "block_info_proto",
              "Column family name: e.g. block_info_proto.");
DEFINE_uint64(block_id, dancenn::kInvalidBlockID, "Block id.");
DEFINE_uint64(inode_id, dancenn::kRootINodeId, "INode id.");
DEFINE_int32(fragment_compact_ratio,
             -1,
             "Filter fragment files which block compaction ratio >= N:1");
DEFINE_bool(print_inode_id_only,
            false,
            "Whether print inode id only during PrintINodes()");

// snapshot related
DEFINE_uint64(snapshot_parent_id,
              dancenn::kRootINodeId,
              "Parent id for snapshot table.");
DEFINE_uint64(snapshot_txid, dancenn::kInvalidTxId, "Last update txid");
DEFINE_string(snapshot_print_mode,
              "inode",
              "Values: inode/ref; inode: print inode normally; ref: print "
              "snapshot inodes which have snapshot reference");

namespace dancenn {

std::string EncodeBlockID(BlockID blk_id) {
  std::string blk_id_str;
  blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  return blk_id_str;
}

class MetaStorageTool : private ReadOnlyMetaStorage {
 public:
  MetaStorageTool() : ReadOnlyMetaStorage(FLAGS_namespace_meta_storage_path) {
    Launch();
  }

  void PrintNameSystemInfo() {
    std::vector<std::string> u32_keys{
        kLayoutVersionKey,
        kNameSpaceTypeKey,
    };
    std::vector<std::string> u64_keys{kCTimeKey,
                                      kFileSystemIdKey,
                                      kNameSpaceIdKey,
                                      kGenerationStampV1LimitKey,
                                      kGenerationStampV1Key,
                                      kGenerationStampV2Key,
                                      kLastAllocatedBlockIdKey,
                                      kLastINodeIdKey,
                                      kNumINodesKey,
                                      kLastCkptTxIdKey,
                                      kLastSnapshotIdKey,
                                      kNumSnapshotsKey};
    std::vector<std::string> string_keys{kClusterIdKey,
                                         kPersistentUfsInfoKey,
                                         kBlockPoolIdKey,
                                         kBlockInfoVersion,
                                         kAZBlacklistKey,
                                         kDirPolicyPersistedKey};

    auto iter_holder = GetINodeStatIterators();
    auto iter = iter_holder.NameSystemInfoIter();
    for (const std::string& key : u32_keys) {
      std::string s;
      CHECK(GetNameSystemInfo(key, &s, iter)) << key;
      uint32_t value = platform::ReadBigEndian<uint32_t>(s.c_str(), 0);
      LOG(INFO) << absl::StrFormat("%s:%d", key, value);
    }
    for (const std::string& key : u64_keys) {
      std::string s("(null)");
      (void)GetNameSystemInfo(key, &s, iter);
      uint64_t value = platform::ReadBigEndian<uint64_t>(s.c_str(), 0);
      LOG(INFO) << absl::StrFormat("%s:%d", key, value);
    }
    for (const std::string& key : string_keys) {
      std::string value("(null)");
      (void)GetNameSystemInfo(key, &value, iter);
      LOG(INFO) << absl::StrFormat("%s:%s", key, value);
    }
    {
      std::string value("(null)");
      if (GetNameSystemInfo(kEditLogConfKey, &value, iter)) {
        EditLogConf conf;
        conf.ParseFromString(value);
        value = ToJsonCompactString(conf);
      }
      LOG(INFO) << absl::StrFormat("%s:%s", kEditLogConfKey, value);
    }
  }

  void PrintINode(INodeID id) {
    auto iter_holder = GetINodeStatIterators();
    PrintLastCkptTxId(iter_holder.NameSystemInfoIter());
    INode inode;
    auto sc = GetINode(id, &inode, iter_holder.INodeIter());
    CHECK_EQ(sc, StatusCode::kOK)
        << absl::StrFormat("Get info failed, status code: %d", sc);
    LOG(INFO) << PBConverter::ToCompactJsonString(inode);
  }

  bool IsFragmentFile(const INode& inode) {
    if (inode.type() != INode_Type_kFile) {
      return false;
    }
    if (inode.blocks_size() == 0) {
      return false;
    }

    uint64_t fsize = 0;
    for (auto it = inode.blocks().begin();
         it != inode.blocks().end();
         it++) {
      fsize += it->numbytes();
    }
    if (fsize == 0) {
      return false;
    }
    CHECK_GT(fsize, 0);
    uint64_t nblk_old = inode.blocks_size();
    CHECK_GT(nblk_old, 0);
    uint64_t blksize = FLAGS_dfs_block_size;
    uint64_t nblk_new = (fsize + blksize - 1) / blksize;
    CHECK_GT(nblk_new, 0);

    if (nblk_old / nblk_new < FLAGS_fragment_compact_ratio) {
      return false;
    }
    return true;
  }

  void PrintINodes() {
    auto iter_holder = GetINodeStatIterators();
    PrintLastCkptTxId(iter_holder.NameSystemInfoIter());
    auto it = iter_holder.INodeIter();
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      INode inode;
      if (!inode.ParseFromArray(it->value().data(), it->value().size())) {
        LOG(ERROR) << "Cannot deserialize element: " << it->key().ToString();
        continue;
      }

      if (FLAGS_fragment_compact_ratio > 0 &&
          !IsFragmentFile(inode)) {
        continue;
      }

      std::string msg;
      if (FLAGS_print_inode_id_only) {
        msg = absl::StrFormat("%lu", inode.id());
      } else {
        msg = PBConverter::ToCompactJsonString(inode);
      }
      LOG(INFO) << msg;
    }
    CHECK(it->status().ok()) << it->status().ToString();
  }

  void PrintPendingDeleteINodes() {
    auto snapshot_holder = GetSnapshot();

    { // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto pd_iter_holder = GetIterator(snap, kINodePendingDeleteCFIndex);
      auto pd_iter = pd_iter_holder->iter();

      for (pd_iter->SeekToFirst();
           pd_iter->Valid() && pd_iter->status().ok();
           pd_iter->Next()) {
        INodeID inode_id = DecodeINodeID(pd_iter->key());
        if (inode_id == kInvalidINodeId) {
          LOG(ERROR) << absl::StrFormat(
              "Failed to parse key of PendingDelete ColumnFamily");
          continue;
        }

        INode inode;
        if (!inode.ParseFromArray(pd_iter->value().data(),
                                  pd_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse PendingDelete of inode %lu.",
              inode_id);
        }

        LOG(INFO) << absl::StrFormat(
            "INodeID: %lu, INode: %s",
            inode_id, inode.ShortDebugString());
      }
    }
  }

  void PrintINodeStats() {
    auto snapshot_holder = GetSnapshot();

    { // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto instat_iter_holder = GetIterator(snap, kINodePendingDeleteCFIndex);
      auto instat_iter = instat_iter_holder->iter();

      for (instat_iter->SeekToFirst();
           instat_iter->Valid() && instat_iter->status().ok();
           instat_iter->Next()) {
        INodeID inode_id = DecodeINodeID(instat_iter->key());
        if (inode_id == kInvalidINodeId) {
          LOG(ERROR) << absl::StrFormat(
              "Failed to parse key of INodeStat ColumnFamily");
          continue;
        }

        dancenn::INodeStat instat;
        Status st = dancenn::INodeStatUtils::Parse(
            instat_iter->value().ToString(), &instat);
        if (!st.IsOK()) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse INodeStat of inode %lu: %s",
              inode_id, st.ToString());
        }

        LOG(INFO) << absl::StrFormat(
            "INodeID: %lu, INodeStat: %s",
            inode_id, instat.ToString());
      }
    }
  }

  void PrintSnapshotINodes(INodeID parent_id, INodeID inode_id, uint64_t txid) {
    LOG(INFO) << "PrintSnapshotINodes " << FLAGS_snapshot_print_mode << " "
              << parent_id << " " << inode_id;

    uint64_t print_num = 0, all_num = 0;
    std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
        rocksdb::ReadOptions(), handles_[kSnapshotInodeCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      uint64_t p_id, id, last_update_txid, snapshot_root_id;
      std::string name;
      const auto key_str =
          cnetpp::base::StringPiece(it->key().data(), it->key().size());
      DecodeSnapshotINodeKey(
          key_str, &p_id, &name, &id, &last_update_txid, &snapshot_root_id);
      all_num++;

      INode inode;
      // filter and get inode
      if (FLAGS_snapshot_print_mode == "inode") {
        if (parent_id != dancenn::kRootINodeId && p_id != parent_id) {
          continue;
        }
        if (inode_id != dancenn::kRootINodeId && id != inode_id) {
          continue;
        }
        if (txid != dancenn::kInvalidTxId && last_update_txid != txid) {
          continue;
        }
        if (!inode.ParseFromArray(it->value().data(), it->value().size())) {
          LOG(ERROR) << "Cannot deserialize element: " << it->key().ToString();
          continue;
        }
      } else if (FLAGS_snapshot_print_mode == "ref") {
        if (!inode.ParseFromArray(it->value().data(), it->value().size())) {
          LOG(ERROR) << "Cannot deserialize element: " << it->key().ToString();
          continue;
        }
        if (inode.snapshot_references_size() == 0) {
          continue;
        }
      } else {
        LOG(FATAL) << "Unknown snapshot_print_mode: "
                   << FLAGS_snapshot_print_mode;
      }

      print_num++;
      LOG(INFO) << absl::Substitute("SnapshotINode Key: $0/$1/$2/$3/$4\n",
                                    p_id,
                                    name,
                                    id,
                                    last_update_txid,
                                    snapshot_root_id);
      LOG(INFO) << PBConverter::ToCompactJsonString(inode);
    }
    LOG(INFO) << absl::Substitute("print $0 of $1 inodes", print_num, all_num);
    CHECK(it->status().ok()) << it->status().ToString();
  }

  void PrintSnapshotINodeIndexes(INodeID parent_id, INodeID inode_id) {
    uint64_t print_num = 0, all_num = 0;
    std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
        rocksdb::ReadOptions(), handles_[kSnapshotINodeIndexCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      uint64_t p_id, id, last_update_txid;
      const auto key_str =
          cnetpp::base::StringPiece(it->key().data(), it->key().size());
      DecodeSnapshotINodeIndexKey(key_str, &id, &last_update_txid);
      p_id = DecodeInteger<uint64_t>(it->value());
      all_num++;

      INode inode;
      if (parent_id != dancenn::kRootINodeId && p_id != parent_id) {
        continue;
      }
      if (inode_id != dancenn::kRootINodeId && id != inode_id) {
        continue;
      }
      print_num++;
      LOG(INFO) << absl::Substitute(
          "SnapshotINodeIndex Key: $0/$1, Value: $2\n",
          id,
          last_update_txid,
          p_id);
    }
    LOG(INFO) << absl::Substitute("print $0 of $1 inodes", print_num, all_num);
    CHECK(it->status().ok()) << it->status().ToString();
  }

  void PrintBlockInfoProto(BlockID blk_id) {
    BlockInfoProto bip;
    CHECK(GetBlockInfo(blk_id, &bip));
    LOG(INFO) << PBConverter::ToCompactJsonString(bip);
  }

  void PrintBlockInfoProtos() {
    std::unique_ptr<rocksdb::Iterator> it(rocks_db_->NewIterator(
        rocksdb::ReadOptions(), handles_[kBlockInfoProtoCFIndex]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      BlockInfoProto bip;
      if (!bip.ParseFromArray(it->value().data(), it->value().size())) {
        LOG(ERROR) << "Cannot deserialize element: " << it->key().ToString();
      } else {
        LOG(INFO) << PBConverter::ToCompactJsonString(bip);
      }
    }
    CHECK(it->status().ok()) << it->status().ToString();
  }

  void PrintBlockIndex(uint32_t cf_index, BlockID blk_id) {
    std::unique_ptr<rocksdb::Iterator> it(
        rocks_db_->NewIterator(rocksdb::ReadOptions(), handles_[cf_index]));
    it->Seek(EncodeBlockID(FLAGS_block_id));
    CHECK(it->Valid() && it->status().ok());
    CHECK_EQ(it->key().size(), sizeof(BlockID) / sizeof(uint8_t));
    LOG(INFO) << absl::StrFormat(
        "Found block index %d: %s",
        blk_id,
        (platform::ReadBigEndian<BlockID>(it->key().data(), 0) == blk_id
             ? "true"
             : "false"));
  }

  void PrintBlockIndices(uint32_t cf_index) {
    std::unique_ptr<rocksdb::Iterator> it(
        rocks_db_->NewIterator(rocksdb::ReadOptions(), handles_[cf_index]));
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
      CHECK_EQ(it->key().size(), sizeof(BlockID) / sizeof(uint8_t));
      LOG(INFO) << platform::ReadBigEndian<BlockID>(it->key().data(), 0);
    }
    CHECK(it->status().ok()) << it->status().ToString();
  }

  void PrintLifecyclePolicy() {
    auto snapshot_holder = GetSnapshot();

    { // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto lifecycle_iter_holder = GetIterator(snap, kLifecyclePolicyCFIndex);
      auto inode_iter_holder = GetIterator(snap, kINodeDefaultCFIndex);
      auto lifecycle_iter = lifecycle_iter_holder->iter();
      auto inode_iter = inode_iter_holder->iter();

      for (lifecycle_iter->SeekToFirst();
           lifecycle_iter->Valid() && lifecycle_iter->status().ok();
           lifecycle_iter->Next()) {
        INodeID inode_id = DecodeINodeID(lifecycle_iter->key());
        if (inode_id == kInvalidINodeId) {
          LOG(ERROR) << absl::StrFormat(
              "Failed to parse key of LifecyclePolicy ColumnFamily");
          continue;
        }

        cloudfs::LifecyclePolicyInfoProto proto;
        if (!proto.ParseFromArray(lifecycle_iter->value().data(),
                                  lifecycle_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse LifecyclePolicyInfoProto of inode %lu.",
              inode_id);
        }

        INode inode;
        auto sc = GetINode(inode_id, &inode, inode_iter);
        std::string msg = (sc == StatusCode::kOK) ? "(valid)" : "(invalid)";

        LOG(INFO) << absl::StrFormat(
            "INodeID: %lu%s, LifecyclePolicyInfoProto: %s",
            inode_id, msg, PBConverter::ToCompactJsonString(proto));
      }
    }
  }

  void PrintStorageClassStat() {
    auto snapshot_holder = GetSnapshot();

    { // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scs_iter_holder = GetIterator(snap, kStorageClassStatCFIndex);
      auto scs_iter = scs_iter_holder->iter();

      for (scs_iter->SeekToFirst();
           scs_iter->Valid() && scs_iter->status().ok();
           scs_iter->Next()) {
        INodeID id = DecodeINodeID(scs_iter->key());
        CHECK_NE(id, kInvalidINodeId);

        cloudfs::StorageClassStatProto proto;
        if (!proto.ParseFromArray(scs_iter->value().data(),
                                  scs_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse StorageClassStatProto of inode %lu.", id);
        }

        LOG(INFO) << absl::StrFormat(
            "INodeID: %lu, StorageClassStatProto: %s",
            id, PBConverter::ToCompactJsonString(proto));
      }
    }
  }

  void PrintStorageClassReport() {
    auto snapshot_holder = GetSnapshot();

    { // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scr_iter_holder = GetIterator(snap, kStorageClassReportCFIndex);
      auto scr_iter = scr_iter_holder->iter();

      for (scr_iter->SeekToFirst();
           scr_iter->Valid() && scr_iter->status().ok();
           scr_iter->Next()) {
        BlockID blkid;
        std::string dnuuid;
        DecodeSCRKey(scr_iter->key(), &blkid, &dnuuid);
        CHECK_NE(blkid, kInvalidBlockID);

        cloudfs::StorageClassReportProto proto;
        if (!proto.ParseFromArray(scr_iter->value().data(),
                                  scr_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse StorageClassReportProto of block %lu.",
              blkid);
        }

        LOG(INFO) << absl::StrFormat(
            "BlockID: %lu, DN uuid: %s, StorageClassReportProto: %s",
            blkid, dnuuid, PBConverter::ToCompactJsonString(proto));
      }
    }
  }

  void PrintLease() {
    auto snapshot_holder = GetSnapshot();
    {
      // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scr_iter_holder = GetIterator(snap, kLeaseCFIndex);
      auto scr_iter = scr_iter_holder->iter();
      ScanLease(
          [](INodeID inode_id, const std::string& client_name) {
            LOG(INFO) << absl::StrFormat(
                "INodeID: %lu, ClientName: %s", inode_id, client_name);
          },
          scr_iter);
    }
  }

  void PrintDatanodeInfo() {
    auto snapshot_holder = GetSnapshot();

    {  // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scr_iter_holder = GetIterator(snap, kDatanodeInfoCFIndex);
      auto scr_iter = scr_iter_holder->iter();

      for (scr_iter->SeekToFirst();
           scr_iter->Valid() && scr_iter->status().ok();
           scr_iter->Next()) {
        DatanodeID dn_id = kInvalidDatanodeID;
        CHECK(DecodeDatanodeInfoKey(
            cnetpp::base::StringPiece(scr_iter->key().data(),
                                      scr_iter->key().size()),
            &dn_id));
        CHECK_NE(dn_id, kInvalidDatanodeID);

        DatanodeInfoEntryPB proto;
        if (!proto.ParseFromArray(scr_iter->value().data(),
                                  scr_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse DatanodeInfoEntryPB of datanode %u.", dn_id);
        }
        CHECK_EQ(dn_id, proto.internal_id());

        LOG(INFO) << PBConverter::ToCompactJsonString(proto);
      }
    }
  }

  void PrintJobInfo() {
    auto snapshot_holder = GetSnapshot();

    {  // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scr_iter_holder = GetIterator(snap, kJobInfoCFIndex);
      auto scr_iter = scr_iter_holder->iter();

      for (scr_iter->SeekToFirst();
           scr_iter->Valid() && scr_iter->status().ok();
           scr_iter->Next()) {
        ManagedJobId job_id = cnetpp::base::StringPiece(scr_iter->key().data(),
                                                        scr_iter->key().size())
                                  .as_string();

        JobInfoOpBody proto;
        if (!proto.ParseFromArray(scr_iter->value().data(),
                                  scr_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat(
              "Failed to parse JobInfoOpBody of %s.", job_id);
        }
        CHECK_EQ(job_id, proto.job_id());

        LOG(INFO) << PBConverter::ToCompactJsonString(proto);
      }
    }
  }

  void PrintWriteBackTask() {
    auto snapshot_holder = GetSnapshot();
    {  // iterator scope inside snapshot scope
      auto snap = snapshot_holder->snapshot();
      auto scr_iter_holder = GetIterator(snap, kWriteBackTaskCFIndex);
      auto scr_iter = scr_iter_holder->iter();

      for (scr_iter->SeekToFirst();
           scr_iter->Valid() && scr_iter->status().ok();
           scr_iter->Next()) {
        INodeID id = DecodeINodeID(scr_iter->key());
        CHECK_NE(id, kInvalidINodeId);

        INode node;
        if (!node.ParseFromArray(scr_iter->value().data(),
                                 scr_iter->value().size())) {
          LOG(WARNING) << absl::StrFormat("Failed to parse INode of %lu.", id);
        }
        CHECK_EQ(id, node.id());

        LOG(INFO) <<PBConverter::ToCompactJsonString(node);
      }
    }
  }

 private:
  void PrintLastCkptTxId(MetaStorageIterPtr iter) {
    std::string s;
    GetNameSystemInfo(kLastCkptTxIdKey, &s, iter);
    uint64_t value = platform::ReadBigEndian<uint64_t>(s.c_str(), 0);
    LOG(INFO) << absl::StrFormat("Last ckpt txid is %d", value);
  }
};

}  // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  FLAGS_drop_storage_class_report = false;

  dancenn::MetaStorageTool meta_storage_tool;
  if (FLAGS_column_family == dancenn::kNameSystemInfoCFName) {
    meta_storage_tool.PrintNameSystemInfo();
  } else if (FLAGS_column_family == dancenn::kBlockInfoProtoCFName) {
    if (FLAGS_block_id == dancenn::kInvalidBlockID) {
      meta_storage_tool.PrintBlockInfoProtos();
    } else {
      meta_storage_tool.PrintBlockInfoProto(FLAGS_block_id);
    }
  } else if (FLAGS_column_family == dancenn::kLocalBlockCFName) {
    if (FLAGS_block_id == dancenn::kInvalidBlockID) {
      meta_storage_tool.PrintBlockIndices(dancenn::kLocalBlockCFIndex);
    } else {
      meta_storage_tool.PrintBlockIndex(dancenn::kLocalBlockCFIndex, FLAGS_block_id);
    }
  } else if (FLAGS_column_family == dancenn::kDeprecatingBlockCFName) {
    if (FLAGS_block_id == dancenn::kInvalidBlockID) {
      meta_storage_tool.PrintBlockIndices(dancenn::kDeprecatingBlockCFIndex);
    } else {
      meta_storage_tool.PrintBlockIndex(dancenn::kDeprecatingBlockCFIndex,
                                        FLAGS_block_id);
    }
  } else if (FLAGS_column_family == dancenn::kDeprecatedBlockCFName) {
    if (FLAGS_block_id == dancenn::kInvalidBlockID) {
      meta_storage_tool.PrintBlockIndices(dancenn::kDeprecatedBlockCFIndex);
    } else {
      meta_storage_tool.PrintBlockIndex(dancenn::kDeprecatedBlockCFIndex,
                                        FLAGS_block_id);
    }
  } else if (FLAGS_column_family == dancenn::kINodeDefaultCFName) {
    if (FLAGS_inode_id == dancenn::kRootINodeId) {
      meta_storage_tool.PrintINodes();
    } else {
      meta_storage_tool.PrintINode(FLAGS_inode_id);
    }
  } else if (FLAGS_column_family == dancenn::kINodePendingDeleteCFName) {
    meta_storage_tool.PrintPendingDeleteINodes();
  } else if (FLAGS_column_family == dancenn::kINodeStatCFName) {
    meta_storage_tool.PrintINodeStats();
  } else if (FLAGS_column_family == dancenn::kSnapshotInodeCFName) {
    meta_storage_tool.PrintSnapshotINodes(
        FLAGS_snapshot_parent_id, FLAGS_inode_id, FLAGS_snapshot_txid);
  } else if (FLAGS_column_family == dancenn::kSnapshotINodeIndexCFName) {
    meta_storage_tool.PrintSnapshotINodeIndexes(FLAGS_snapshot_parent_id,
                                                FLAGS_inode_id);
  } else if (FLAGS_column_family == dancenn::kLifecyclePolicyCFName) {
    meta_storage_tool.PrintLifecyclePolicy();
  } else if (FLAGS_column_family == dancenn::kStorageClassStatCFName) {
    meta_storage_tool.PrintStorageClassStat();
  } else if (FLAGS_column_family == dancenn::kStorageClassReportCFName) {
    meta_storage_tool.PrintStorageClassReport();
  } else if (FLAGS_column_family == dancenn::kLeaseCFName) {
    meta_storage_tool.PrintLease();
  } else if (FLAGS_column_family == dancenn::kDatanodeInfoCFName) {
    meta_storage_tool.PrintDatanodeInfo();
  } else if (FLAGS_column_family == dancenn::kJobInfoCFName) {
    meta_storage_tool.PrintJobInfo();
  } else if (FLAGS_column_family == dancenn::kWriteBackTaskCFName) {
    meta_storage_tool.PrintWriteBackTask();
  } else {
    CHECK(false) << "Not supported column family";
  }
}
