// Copyright (c) @ 2024.
// All right reserved.
//
// Author: xuexiang <<EMAIL>>
// Created: 2024/07/31
// Description
//

#include <absl/strings/str_format.h>
#include <gflags/gflags.h>

#include <cstddef>
#include <cstdint>
#include <memory>
#include <string>

#include "namespace/meta_storage.h"
#include "namespace/meta_storage_util.h"

DEFINE_string(active_ckpt_path, "", "RocksDB Checkpoint path for Active NN");
DEFINE_string(standby_ckpt_path, "", "RocksDB Checkpoint path for Standby NN");

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + "");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  dancenn::DBComparator ckpt_comptr(FLAGS_active_ckpt_path,
                                    FLAGS_standby_ckpt_path);
  bool consistent = ckpt_comptr.CheckDBConsistency();
  LOG(INFO) << absl::StrFormat(
      "Data between %s and %s is %s",
      FLAGS_active_ckpt_path, FLAGS_standby_ckpt_path,
      (consistent ? "consistent" : "INCONSISTENT"));
  return 0;
}
