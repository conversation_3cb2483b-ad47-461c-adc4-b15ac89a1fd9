#include "namespace/namespace.h"

#include <gflags/gflags.h>
#include <iostream>

DECLARE_string(namespace_meta_storage_path);
DECLARE_string(fsimage_dir);
DECLARE_string(fsimage_file_name);
DECLARE_int32(dfs_meta_storage_parent_map_mask);
DECLARE_int32(dfs_meta_storage_rocksdb_num_levels);
DECLARE_int32(dfs_meta_storage_rocksdb_write_buffer_size_mb);
DECLARE_int32(dfs_meta_storage_rocksdb_level0_file_num_compaction_triger);
DECLARE_int64(dfs_meta_storage_rocksdb_max_bytes_for_level_base);
DECLARE_int32(dfs_meta_storage_rocksdb_level0_stop_writes_trigger);
DECLARE_int32(bg_auto_compact_all_deletion_threshold);
DECLARE_int32(bg_auto_compact_interval_in_min);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " --namespace_meta_storage_path=<db_path> "
                                               + " --fsimage_dir=<dir> "
                                               + " --fsimage_file_name=<filename> ");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  if (FLAGS_namespace_meta_storage_path.empty() ||
      FLAGS_fsimage_dir.empty() ||
      FLAGS_fsimage_file_name.empty()) {
    std::cerr << gflags::ProgramUsage() << std::endl;;
    exit(1);
  }

  // fsimage is always empty in CloudFS, so we don't have to use this
  // parent_map_ any more.
  FLAGS_dfs_meta_storage_parent_map_mask = 0;

  FLAGS_dfs_meta_storage_rocksdb_level0_stop_writes_trigger = 8;
  FLAGS_dfs_meta_storage_rocksdb_level0_file_num_compaction_triger = 4;
  FLAGS_dfs_meta_storage_rocksdb_max_bytes_for_level_base=256 * 1024 * 1024;
  FLAGS_dfs_meta_storage_rocksdb_num_levels=2;
  FLAGS_dfs_meta_storage_rocksdb_write_buffer_size_mb=4;
  FLAGS_bg_auto_compact_all_deletion_threshold=INT32_MAX;
  FLAGS_bg_auto_compact_interval_in_min=30 * 24 * 3600;
  LOG(INFO) << "--namespace_meta_storage_path=" << FLAGS_namespace_meta_storage_path;
  LOG(INFO) << "--fsimage_dir=" << FLAGS_fsimage_dir;
  LOG(INFO) << "--fsimage_file_name=" << FLAGS_fsimage_file_name;
  LOG(INFO) << "--dfs_meta_storage_rocksdb_num_levels=" << FLAGS_dfs_meta_storage_rocksdb_num_levels;

  LOG(INFO) << "Start to transfer fsimage to rocksdb...";
  auto ns = std::make_unique<dancenn::NameSpace>(FLAGS_namespace_meta_storage_path);
  ns.reset();
  LOG(INFO) << "Finish transfer fsimage to rocksdb...";

  FLAGS_dfs_meta_storage_rocksdb_level0_stop_writes_trigger = 1;
  FLAGS_dfs_meta_storage_rocksdb_level0_file_num_compaction_triger = 4;
  FLAGS_dfs_meta_storage_rocksdb_max_bytes_for_level_base=256 * 1024 * 1024;
  LOG(INFO) << "Start compact rocksdb to two levels..";
  auto meta_storage = std::make_unique<dancenn::MetaStorage>(FLAGS_namespace_meta_storage_path);
  meta_storage->Launch();
  meta_storage->SetCompactAllStyle(false);
  meta_storage->ForceCompactAll();
  meta_storage->Shutdown();

  LOG(INFO) << "Finish compact rocksdb to two levels...";

  return 0;
}
