#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/ListObjectsV2Request.h>
#include <glog/logging.h>
#include <openssl/crypto.h>
#include <openssl/err.h>
#include <openssl/evp.h>

DECLARE_string(tos_endpoint);
DECLARE_string(tos_bucket);
DECLARE_string(tos_prefix);
DECLARE_string(tos_region);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_int32(ufs_s3_client_req_timeout_ms);
DECLARE_int32(ufs_s3_client_conn_timeout_ms);
DECLARE_int32(ufs_s3_client_max_http_conns);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  Aws::SDKOptions options;
  options.loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Debug;
  options.httpOptions.installSigPipeHandler = true;
  Aws::InitAPI(options);

  LOG(INFO) << "Create S3Client started.";
  LOG(INFO) << "tos_endpoint: " << FLAGS_tos_endpoint;
  LOG(INFO) << "tos_bucket: " << FLAGS_tos_bucket;
  LOG(INFO) << "tos_prefix: " << FLAGS_tos_prefix;
  LOG(INFO) << "tos_region: " << FLAGS_tos_region;
  LOG(INFO) << "tos_access_key_id: " << FLAGS_tos_access_key_id;
  LOG(INFO) << "tos_secret_access_key: " << FLAGS_tos_secret_access_key;
  LOG(INFO) << "ufs_s3_client_req_timeout_ms: "
            << FLAGS_ufs_s3_client_req_timeout_ms;
  LOG(INFO) << "ufs_s3_client_conn_timeout_ms: "
            << FLAGS_ufs_s3_client_conn_timeout_ms;
  LOG(INFO) << "ufs_s3_client_max_http_conns: "
            << FLAGS_ufs_s3_client_max_http_conns;

  Aws::Auth::AWSCredentials credentials(
      FLAGS_tos_access_key_id, FLAGS_tos_secret_access_key, "");

  Aws::Client::ClientConfiguration clientCfg;
  clientCfg.endpointOverride = FLAGS_tos_endpoint;
  clientCfg.region = FLAGS_tos_region;
  clientCfg.httpRequestTimeoutMs = FLAGS_ufs_s3_client_req_timeout_ms;
  clientCfg.requestTimeoutMs = FLAGS_ufs_s3_client_req_timeout_ms;
  clientCfg.connectTimeoutMs = FLAGS_ufs_s3_client_conn_timeout_ms;
  clientCfg.maxConnections = FLAGS_ufs_s3_client_max_http_conns;
  auto s3_client = std::make_shared<Aws::S3::S3Client>(
      credentials,
      clientCfg,
      Aws::Client::AWSAuthV4Signer::PayloadSigningPolicy::Never,
      /*useVirtualAddressing=*/false);

  // 构建 URL
  Aws::String url = "http://" + FLAGS_tos_endpoint + "/" + FLAGS_tos_bucket;

  Aws::S3::Model::ListObjectsV2Request listObjectsRequest;
  listObjectsRequest.SetBucket(FLAGS_tos_bucket);

  auto listObjectsOutcome = s3_client->ListObjectsV2(listObjectsRequest);

  if (listObjectsOutcome.IsSuccess()) {
    LOG(INFO) << "Objects in bucket '" << FLAGS_tos_bucket << "':";
    for (const auto& object : listObjectsOutcome.GetResult().GetContents()) {
      LOG(INFO) << object.GetKey();
    }
  } else {
    LOG(INFO) << "Failed to list objects in bucket '" << FLAGS_tos_bucket
              << "': " << listObjectsOutcome.GetError().GetMessage();
  }

  Aws::ShutdownAPI(options);

  return 0;
}