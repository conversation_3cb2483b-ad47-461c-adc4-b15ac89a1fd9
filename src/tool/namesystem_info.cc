// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/22
// Description

#include <cnetpp/base/string_piece.h>  // For StringPiece.
#include <gflags/gflags.h>             // For DECLARE_string, DECLARE_int32.

#include <cstdint>  // For uint8_t, uint32_t.
#include <string>   // For string.

#include "base/constants.h"          // For kNameSpaceTypeKey.
#include "base/platform.h"           // For HostToBigEndian.
#include "namespace/meta_storage.h"  // For MetaStorage.

DECLARE_string(namespace_meta_storage_path);
DECLARE_int32(namespace_type);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (argc != 1) {
    exit(EXIT_FAILURE);
  }

  dancenn::MetaStorage meta_storage(FLAGS_namespace_meta_storage_path);
  meta_storage.Launch();

  auto t = dancenn::platform::HostToBigEndian(
      static_cast<uint32_t>(FLAGS_namespace_type));
  auto v = cnetpp::base::StringPiece(reinterpret_cast<const uint8_t*>(&t),
                                     sizeof(uint32_t));
  meta_storage.PutNameSystemInfo(dancenn::kNameSpaceTypeKey, v);

  meta_storage.Shutdown();
}
