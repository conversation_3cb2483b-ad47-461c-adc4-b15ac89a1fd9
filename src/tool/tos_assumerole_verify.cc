// Copyright (c) @ 2025. Bytedance all right reserved.

#include <gflags/gflags.h>  // For DEFINE_string, SetUsageMessage, etc.
#include <glog/logging.h>   // For CHECK, LOG.

#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/ufs_auth_conf.h"

DEFINE_string(name, "<>", "case name");
DECLARE_bool(use_fixed_ak);
DECLARE_string(iam_account_id);
DECLARE_string(assume_role_name);
DECLARE_string(cfs_service_ak);
DECLARE_string(cfs_service_sk);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_bool(ufs_auth_enabled);
DECLARE_string(ufs_auth_policy);
DECLARE_string(ufs_auth_fixed_ak);
DECLARE_string(ufs_auth_fixed_sk);
DECLARE_string(ufs_auth_fixed_token);
DECLARE_string(ufs_auth_role_info);
DECLARE_string(iam_top_url);
DECLARE_string(cfs_service_region);

namespace dancenn {
}

int main(int argc, char** argv) {
    gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
    gflags::ParseCommandLineFlags(&argc, &argv, true);

    LOG(INFO) << "Configs: " 
        << "\n use_fixed_ak=" << FLAGS_use_fixed_ak
        << "\n iam_account_id=" << FLAGS_iam_account_id
        << "\n assume_role_name=" << FLAGS_assume_role_name
        << "\n cfs_service_ak=" << FLAGS_cfs_service_ak
        << "\n cfs_service_sk=" << FLAGS_cfs_service_sk
        << "\n tos_access_key_id=" << FLAGS_tos_access_key_id
        << "\n tos_secret_access_key=" << FLAGS_tos_secret_access_key
        << "\n ufs_auth_enabled=" << FLAGS_ufs_auth_enabled
        << "\n ufs_auth_policy=" << FLAGS_ufs_auth_policy
        << "\n ufs_auth_fixed_ak=" << FLAGS_ufs_auth_fixed_ak
        << "\n ufs_auth_fixed_sk=" << FLAGS_ufs_auth_fixed_sk
        << "\n ufs_auth_fixed_token=" << FLAGS_ufs_auth_fixed_token
        << "\n ufs_auth_role_info=" << FLAGS_ufs_auth_role_info
        << "\n iam_top_url=" << FLAGS_iam_top_url
        << "\n cfs_service_region=" << FLAGS_cfs_service_region;

    CHECK(dancenn::UfsAuthConf::Instance().Init().IsOK());

    dancenn::TosCredKeeper k;
    auto s = k.TEST_FetchOnce();
    CHECK(s.IsOK());

    auto inner = k.InnerCredential();
    LOG(INFO) << "=============================================================================";
    LOG(INFO) << "Verfication succeeded. case: " << FLAGS_name << ", ak: " << inner->ak << ", sk: " << inner->sk;
    LOG(INFO) << "=============================================================================";

    return 0;
}