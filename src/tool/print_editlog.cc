// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: ranpan<PERSON> <<EMAIL>>
//

#include <gflags/gflags.h>

#include <iostream>
#include <memory>
#include <string>

#include "test/dancenn_test_base.h"

DEFINE_int64(from_txid, 1, "read from #from_txid");
DEFINE_int64(to_txid, 0, "read until #to_txid");

DECLARE_string(dump_parse_failed_protobuf_path);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (argc != 1) {
    exit(EXIT_FAILURE);
  }
  FLAGS_dump_parse_failed_protobuf_path = "/tmp/protobuf.";

  LOG(INFO) << "from_txid=" << FLAGS_from_txid;
  LOG(INFO) << "to_txid=" << FLAGS_to_txid;

  auto jvm = dancenn::test::CreateVMOnlyOnce();
  auto ctx = std::make_shared<dancenn::HAEditLogContext>(jvm.get(), 1);
  ctx->OpenForRead();
  CHECK_JVM_EXCEPTION(jvm);
  auto ictx = ctx->CreateInputContext(FLAGS_from_txid, FLAGS_to_txid, true);
  CHECK_JVM_EXCEPTION(jvm);
  auto deserializer = std::make_shared<dancenn::OpDeSerializer>();

  while (true) {
    std::string s;
    CHECK(ictx->ReadOp(&s));
    CHECK_JVM_EXCEPTION(jvm);
    if (s.length() == 0) {
      break;
    }
    auto op = deserializer->Deserialize(s);
    std::cout << op->SerializeToJsonString() << std::endl;
    if (op->txid() == FLAGS_to_txid) {
      break;
    }
  }
}
