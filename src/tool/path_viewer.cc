// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/07/01
// Description

#include <absl/strings/str_format.h>  // For StrFormat.
#include <gflags/gflags.h>  // For DEFINE_string, SetUsageMessage, etc.
#include <glog/logging.h>   // For LOG.

#include <memory>  // For shared_ptr.
#include <string>  // For string.

#include "base/path_util.h"          // For NormalizePath, SplitPath.
#include "base/pb_converter.h"       // For PBConverter.
#include "namespace/inode.h"         // For INode.
#include "namespace/meta_storage.h"  // For ReadOnlyMetaStorage.

DEFINE_string(src, "/", "Path, such as /a/b/c");
DEFINE_bool(list_sub_inodes, false, "List sub inodes or not");
DECLARE_string(namespace_meta_storage_path);

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, /*remove_flags=*/true);

  CHECK(!FLAGS_src.empty()) << "src is empty.";
  std::string normalized_path;
  CHECK(dancenn::NormalizePath(FLAGS_src, /*username=*/"", &normalized_path))
      << absl::StrFormat("Invalid src: %s", FLAGS_src);
  std::vector<cnetpp::base::StringPiece> path_components;
  CHECK(dancenn::SplitPath(normalized_path, &path_components))
      << absl::StrFormat("Invalid src: %s", FLAGS_src);

  std::shared_ptr<dancenn::ReadOnlyMetaStorage> meta_storage =
      std::make_shared<dancenn::ReadOnlyMetaStorage>(
          FLAGS_namespace_meta_storage_path);
  meta_storage->Launch();
  auto iter_holder = meta_storage->GetINodeStatIterators();
  dancenn::INode last_inode = meta_storage->GetRootINode();
  for (const auto& c : path_components) {
    auto id = last_inode.id();
    CHECK_EQ(meta_storage->GetINode(
                 id, c.as_string(), &last_inode, iter_holder.INodeIter()),
             dancenn::StatusCode::kOK)
        << absl::StrFormat("INode %d not found", id);
    LOG(INFO) << dancenn::PBConverter::ToCompactJsonString(last_inode);
  }

  if (FLAGS_list_sub_inodes) {
    LOG(INFO) << "The following are sub inodes:";
    meta_storage->GetSubINodes(
        last_inode.id(),
        /*start_after=*/"",
        [](const dancenn::INode& sub_inode) -> bool {
          LOG(INFO) << dancenn::PBConverter::ToCompactJsonString(sub_inode);
          return true;
        },
        iter_holder.INodeIter());
  }

  return 0;
}