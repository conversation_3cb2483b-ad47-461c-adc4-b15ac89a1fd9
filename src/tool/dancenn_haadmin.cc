// Copyright 2018 Ra<PERSON><<EMAIL>>

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <cnetpp/tcp/tcp_client.h>
#include <cnetpp/concurrency/thread_pool.h>

#include <iostream>  // NOLINT(readability/streams)

#include "proto/generated/cloudfs/HAServiceProtocol.pb.h"
#include "rpc/pooled_rpc_channel.h"
#include "service/ha_service.h"

DEFINE_string(nn_host,
  "127.0.0.1", "HA rpc host for target DanceNN");
DEFINE_int32(nn_port, 5062, "HA rpc port for target DanceNN");
DEFINE_bool(transitionToActive,
  false, "notify target DanceNN to enter Active state");
DEFINE_bool(transitionToStandby,
  false, "notify target DanceNN to enter Standby state");

static bool validateArguments() {
  // at most one flag is on
  return !(FLAGS_transitionToActive && FLAGS_transitionToStandby);
}

int main(int argc, char *argv[]) {
  int original_argc = argc;
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  std::string help_message =
    "USAGE: dancenn_haadmin --nn_host=${host} --nn_port=${port}"
    " [--transitionToActive] [--transitionToStandby]\n\n"
    "If neither --transitionToActive nor --transitionToStandby"
    " flag is given, just print current service state"
    "(active|standby).\n"
    "Both two flags can not be designated in the same time.";

  if (original_argc == 1 || argc != 1 || !validateArguments()) {
    std::cerr << "original_argc=" << original_argc << std::endl;
    std::cerr << "argc=" << argc << std::endl;
    std::cerr << "Illegal usage!!!" << std::endl;
    std::cerr << help_message << std::endl;
    exit(EXIT_FAILURE);
  }

  google::InitGoogleLogging(argv[0]);
  google::InstallFailureSignalHandler();

  dancenn::RpcClientOptions options;
  options.set_max_open_connections_per_user_and_fs(10);
  options.set_max_pending_calls_per_user_and_fs(1100);
  options.set_request_timeout_ms(1000000);
  options.set_send_buffer_size(65536);
  options.set_receive_buffer_size(65536);
  options.set_tcp_receive_buffer_size(65536);
  options.set_tcp_send_buffer_size(65536);
  options.set_network_thread_count(4);
  options.set_client_id("hdfs_haadmin");
  options.set_user("tiger");
  options.set_protocol_name("org.apache.hadoop.ha.HAServiceProtocol");
  options.set_protocol_version(1);

  auto tcp_client = std::make_shared<cnetpp::tcp::TcpClient>();
  tcp_client->Launch("ha_cli");

  auto handlers =
    std::make_shared<cnetpp::concurrency::ThreadPool>(
      "haadmin");
  handlers->set_num_threads(1);
  handlers->Start();

  auto endpoint = std::make_shared<cnetpp::base::EndPoint>(
    FLAGS_nn_host, FLAGS_nn_port);
  auto channel = std::make_shared<dancenn::PooledRpcChannel>(
    tcp_client, options, *endpoint, handlers);

  std::shared_ptr<dancenn::HAService::Stub> client(
    new dancenn::HAService::Stub(channel.get()));

  if (FLAGS_transitionToActive) {
    dancenn::RpcController c;
    cloudfs::TransitionToActiveRequestProto req;
    cloudfs::TransitionToActiveResponseProto resp;
    req.mutable_reqinfo()->set_reqsource(cloudfs::REQUEST_BY_USER);

    client->transitionToActive(&c, &req, &resp, nullptr);

    if (c.status() == dancenn::RpcStatus::kSuccess) {
      std::cout << "transitionToActive OK:" << resp.DebugString() << std::endl;
    } else {
      std::cout << "transitionToActive ERR: " << c.ErrorText() << std::endl;
    }
  } else if (FLAGS_transitionToStandby) {
    dancenn::RpcController c;
    cloudfs::TransitionToStandbyRequestProto req;
    cloudfs::TransitionToStandbyResponseProto resp;
    req.mutable_reqinfo()->set_reqsource(cloudfs::REQUEST_BY_USER);

    client->transitionToStandby(&c, &req, &resp, nullptr);

    if (c.status() == dancenn::RpcStatus::kSuccess) {
      std::cout << "transitionToStandby OK:" << resp.DebugString() << std::endl;
    } else {
      std::cout << "transitionToStandby ERR: " << c.ErrorText() << std::endl;
    }
  }

  {
    dancenn::RpcController c;
    cloudfs::GetServiceStatusRequestProto req;
    cloudfs::GetServiceStatusResponseProto resp;
    client->getServiceStatus(&c, &req, &resp, nullptr);
    if (c.status() == dancenn::RpcStatus::kSuccess) {
      std::cout << "getServiceStatus OK:" << resp.DebugString() << std::endl;
    } else {
      std::cout << "getServiceStatus ERR: " << c.ErrorText() << std::endl;
    }
  }
  {
    dancenn::RpcController c;
    cloudfs::MonitorHealthRequestProto req;
    cloudfs::MonitorHealthResponseProto resp;
    client->monitorHealth(&c, &req, &resp, nullptr);
    if (c.status() ==  dancenn::RpcStatus::kSuccess) {
      std::cout << "monitorHealth OK." << std::endl;
    } else {
      std::cout << "monitorHealth ERR: " << c.ErrorText() << std::endl;
    }
  }

  channel->Shutdown();
  handlers->Stop(true);
}
