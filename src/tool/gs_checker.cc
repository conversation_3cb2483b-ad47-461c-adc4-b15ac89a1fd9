// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/09/08
// Description

#include <absl/strings/str_format.h>          // For StrFormat.
#include <gflags/gflags.h>                    // For DECLARE_string, etc.
#include <glog/logging.h>                     // For LOG.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For BlockProto.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.
#include <proto/generated/dancenn/inode.pb.h>             // For INode.
#include <rocksdb/db.h>  // For ReadOptions, ColumnFamilyHandle, Status.

#include <cstdint>     // For int64_t.
#include <functional>  // For function.
#include <mutex>       // For once_flag, call_once.
#include <vector>      // For vector.

#include "namespace/meta_storage.h"  // For ReadOnlyMetaStorage, MetaStorageIterPtr.
#include "namespace/meta_storage_constants.h"  // For kINodeDefaultCFIndex, etc.

DECLARE_string(namespace_meta_storage_path);

namespace dancenn {

class GsChecker : private ReadOnlyMetaStorage {
 public:
  GsChecker()
      : ReadOnlyMetaStorage(FLAGS_namespace_meta_storage_path),
        inode_cnt_(0),
        succeed_blk_cnt_(0),
        failed_blk_cnt_(0) {
    Launch();

    std::vector<MetaStorageIterPtr> iterators;
    CHECK(rocks_db_
              ->NewIterators(rocksdb::ReadOptions(),
                             std::vector<rocksdb::ColumnFamilyHandle*>{
                                 handles_.at(kINodeDefaultCFIndex),
                                 handles_.at(kBlockInfoProtoCFIndex)},
                             &iterators)
              .ok());
    CHECK_EQ(iterators.size(), 2);
    inode_iter_ = iterators[0];
    bip_iter_ = iterators[1];
  }

  void Check() {
    AnalyseSubINodes(GetRootINode());
    LOG(INFO) << "There are " << inode_cnt_ << " inodes";
    LOG(INFO) << "There are " << succeed_blk_cnt_ << " succeed blocks";
    LOG(INFO) << "There are " << failed_blk_cnt_ << " failed blocks";
  }

 private:
  bool AnalyseSubINodes(const INode& inode) {
    inode_cnt_++;
    switch (inode.type()) {
      case INode::kFile: {
        for (const BlockProto& bp : inode.blocks()) {
          BlockInfoProto bip;
          std::string value;
          bip_iter_->Seek(EncodeBlockID(bp.blockid()));
          if (!(bip_iter_->Valid() &&
                bip.ParseFromArray(bip_iter_->value().data(),
                                   bip_iter_->value().size()) &&
                bip.block_id() == bp.blockid())) {
            LOG(WARNING) << "BlockInfoProto B" << bp.blockid() << " not found";
            failed_blk_cnt_++;
            continue;
          }
          if (!(bip.gen_stamp() == bp.genstamp() &&
                (bip.state() == BlockInfoProto::kUnderConstruction ||
                 bip.num_bytes() == bp.numbytes()))) {
            using LogMsgFmtT =
                absl::ParsedFormat<'d', 'd', 'd', 'd', 'd', 'd', 's'>;
            static std::once_flag flag;
            static std::unique_ptr<LogMsgFmtT> log_msg_fmt;
            std::call_once(flag, []() {
              log_msg_fmt = LogMsgFmtT::New(
                  "BlockProto{%d,%d,%d}, BlockInfoProto{%d,%d,%d,%s}");
              CHECK(log_msg_fmt);
            });
            LOG(WARNING) << absl::StrFormat(*log_msg_fmt,
                                            bp.blockid(),
                                            bp.genstamp(),
                                            bp.numbytes(),
                                            bip.block_id(),
                                            bip.gen_stamp(),
                                            bip.num_bytes(),
                                            bip.pufs_name());
            failed_blk_cnt_++;
          } else {
            succeed_blk_cnt_++;
          }
        }
      } break;
      case INode::kDirectory: {
        std::string stashed_key(inode_iter_->key().data(),
                                inode_iter_->key().size());
        GetSubINodes(
            inode.id(),
            "",
            [this](const INode& inode) { return AnalyseSubINodes(inode); },
            inode_iter_);
        inode_iter_->Seek(stashed_key);
      } break;
      default:
        break;
    }
    return true;
  }

 private:
  MetaStorageIterPtr inode_iter_;
  MetaStorageIterPtr bip_iter_;
  int64_t inode_cnt_;
  int64_t succeed_blk_cnt_;
  int64_t failed_blk_cnt_;
};

}  // namespace dancenn

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  dancenn::GsChecker gs_checker;
  gs_checker.Check();
}
