// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2022/06/20
// Description

#include <absl/strings/str_format.h>  // For StrFormat.
#include <gflags/gflags.h>            // For DECLARE_string.
#include <glog/logging.h>             // For CHECK, LOG.

#include <memory>  // For shared_ptr.
#include <string>  // For string.

#include "base/path_util.h"                    // For NormalizePath, SplitPath.
#include "base/platform.h"                     // For WriteBigEndian.
#include "namespace/inode.h"                   // For INode.
#include "namespace/meta_storage.h"  // For ReadOnlyMetaStorage, MetaStorage.
#include "namespace/namespace_stat_checker.h"  // For CheckINodeStatOp.
#include "proto/generated/dancenn/status_code.pb.h"  // For StatusCode.
#include "namespace/meta_storage_constants.h"  // For MetaStorage CFIndex.

DECLARE_string(namespace_meta_storage_path);
DEFINE_string(src, "/", "path to check dirstat");

int main(int argc, char** argv) {
  gflags::SetUsageMessage(std::string(argv[0]) + " [FLAGS]...");
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  std::shared_ptr<dancenn::MetaStorage> meta_storage(
      new dancenn::ReadOnlyMetaStorage(FLAGS_namespace_meta_storage_path));
  meta_storage->Launch();

  std::string normalized_path;
  CHECK(dancenn::NormalizePath(FLAGS_src, /*username=*/"", &normalized_path))
      << absl::StrFormat("Invalid src: %s", FLAGS_src);
  std::vector<cnetpp::base::StringPiece> path_components;
  CHECK(dancenn::SplitPath(normalized_path, &path_components))
      << absl::StrFormat("Invalid src: %s", FLAGS_src);

  auto iter_holder = meta_storage->GetINodeStatIterators();
  dancenn::INode last_inode = meta_storage->GetRootINode();
  for (const auto& c : path_components) {
    auto id = last_inode.id();
    CHECK_EQ(meta_storage->GetINode(
                 id, c.as_string(), &last_inode, iter_holder.INodeIter()),
             dancenn::StatusCode::kOK)
        << absl::StrFormat("INode %d not found", id);
  }

  // Refer MetaStorage::EncodeStoreKey.
  std::string start_from;
  start_from.reserve(18 + last_inode.name().size());
  start_from.resize(8);
  dancenn::platform::WriteBigEndian(&start_from[0], 0, last_inode.parent_id());
  start_from.append("/");
  start_from.append(last_inode.name());
  start_from.append("/");
  start_from.resize(18 + last_inode.name().size());
  dancenn::platform::WriteBigEndian(
      &start_from[0], 10 + last_inode.name().size(), last_inode.id());

  auto snapshot_holder = meta_storage->GetSnapshot();
  {
    // iterator holder scope, release before snapshot.
    auto snap = snapshot_holder->snapshot();
    auto nsinfo_iter_holder = meta_storage->GetIterator(snap, dancenn::kNameSystemInfoCFIndex);
    auto inode_iter_holder = meta_storage->GetIterator(snap, dancenn::kINodeDefaultCFIndex);
    auto instat_iter_holder = meta_storage->GetIterator(snap, dancenn::kINodeStatCFIndex);
    dancenn::CheckINodeStatOp op(meta_storage,
                        snap,
                        nsinfo_iter_holder->iter(),
                        inode_iter_holder->iter(),
                        instat_iter_holder->iter(),
                        start_from,
                        true);
    op.Check();
    LOG(INFO) << op.GetDebugMsg();
  }
  return 0;
}
