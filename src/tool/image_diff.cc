// Copyright 2017 He <PERSON> <<EMAIL>>

#include <glog/logging.h>
#include <gflags/gflags.h>
#include <cnetpp/base/string_piece.h>

#include "base/constants.h"
#include "base/platform.h"
#include "server/server_common.hpp"
#include "fsimage/fsimage_loader.h"
#include "namespace/meta_storage.h"

DECLARE_string(fsimage_dir);
DECLARE_string(fsimage_file_name);
DECLARE_string(namespace_meta_storage_path);
DECLARE_bool(image_diff_load);
DECLARE_bool(image_diff_compare);
DECLARE_bool(image_diff_clear);

namespace dancenn {

#define INODE_DIFF(a, b, f) { \
  auto x = a.f; \
  auto y = b.f; \
  if (x != y) { \
    LOG(ERROR) << "Different " << #f << " for inode " << a.name() \
        << " parent_id " << a.parent_id() \
        << ". a is " << x << ". b is " << y; \
  } \
}

void diff(const INode& a, const INode& b) {
  INODE_DIFF(a, b, permission().username());
  INODE_DIFF(a, b, permission().groupname());
  INODE_DIFF(a, b, permission().permission());
  INODE_DIFF(a, b, mtime());
  INODE_DIFF(a, b, atime());
  INODE_DIFF(a, b, type());
  if (a.type() == INode::kFile) {
    INODE_DIFF(a, b, preferred_blk_size());
    INODE_DIFF(a, b, storage_policy_id());
    INODE_DIFF(a, b, replication());
    INODE_DIFF(a, b, access_pattern());
    INODE_DIFF(a, b, blocks().size());
    for (size_t i = 0; i < a.blocks().size(); i++) {
      INODE_DIFF(a, b, blocks(i).blockid());
      INODE_DIFF(a, b, blocks(i).genstamp());
      INODE_DIFF(a, b, blocks(i).numbytes());
    }
  }
}

int Run() {
  LOG(INFO) << "Will load fsimage: "
      << FLAGS_fsimage_file_name << " from " << FLAGS_fsimage_dir;
  FSImageLoader loader(nullptr);
  MetaStorage meta(FLAGS_namespace_meta_storage_path);
  meta.Launch();
  if (FLAGS_image_diff_clear) {
    meta.Clear();
  }
  loader.set_load_inode_cb([&](const INode& inode) {
    if (FLAGS_image_diff_load) {
      meta.InsertINode(std::make_shared<INode>(inode));
    }
    if (FLAGS_image_diff_compare) {
      INode saved_inode;
      auto ret = meta.GetINode(inode.parent_id(), inode.name(), &saved_inode);
      if (ret != StatusCode::kOK) {
        LOG(ERROR) << "Failed to get inode " << inode.name()
            << " parent_id " << inode.parent_id()
            << ". StatusCode is " << ret;
      } else {
        diff(inode, saved_inode);
      }
    }
  });
  loader.Load();
  if (FLAGS_image_diff_load) {
    uint64_t last_transaction_id = loader.last_transaction_id();
    auto id = platform::HostToBigEndian(last_transaction_id);
    auto v = cnetpp::base::StringPiece(reinterpret_cast<const uint8_t*>(&id),
      sizeof(uint64_t));
    meta.PutNameSystemInfo(kLastCkptTxIdKey, v);
  }
  meta.Shutdown();
  return 0;
}

}  // namespace dancenn

int main(int argc, char* argv[]) {
  ServerContext sc;
  InitEnvAndFlags(argc, argv, &sc);
  return dancenn::Run();
}
