
#include "job_manager/managed_task.h"

#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include <cinttypes>
#include <memory>
#include <string>
#include <vector>

#include "inode.pb.h"
#include "job_manager/job_manager_def.h"
#include "job_manager/managed_job_impl.h"
#include "job_manager/managed_task_impl.h"
#include "job_manager/workflow.h"
#include "job_manager/workflow_id_generator.h"
#include "job_test.h"

DECLARE_uint32(managed_task_free_execute_timeout_ms);
DECLARE_uint32(managed_task_load_execute_timeout_ms);
DECLARE_int32(managed_task_load_max_retry_times);

namespace dancenn {
class ManagedTaskTest : public JobTest {
 public:
  std::shared_ptr<bool> GetCancelFlag(ManagedLoadMetadataTask& task) {
    return task.cancel_flag_;
  }

  void UpdateStateDirectly(std::shared_ptr<ManagedTask> task,
                           WorkflowState state) {
    task->state_ = state;
  }

  std::unordered_map<DatanodeID,
                     std::vector<std::shared_ptr<cloudfs::ExtendedBlockProto>>>
  GetBatchLoadCmd() {
    return job_manager_->batch_load_cmd_;
  }
};

TEST_F(ManagedTaskTest, TestCountForJobState) {
  ManagedJobId job_id = "JOB_1";
  std::unordered_set<DatanodeID> dn_id = {1};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);

  auto task = std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, nullptr);

  ManagedJobState job_state;
  bool res = task->CountForJobState(job_state);
  // Undone state do not count for job
  ASSERT_FALSE(res);

  task->UpdateState(WorkflowState::SUCCESS);
  res = task->CountForJobState(job_state);
  // Count for job
  ASSERT_TRUE(res);
}

TEST_F(ManagedTaskTest, TestProcessBlockReport) {
  ManagedJobId job_id = "JOB_1";
  std::unordered_set<DatanodeID> dn_id = {1};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);

  // LOAD processBlockReport
  {
    auto load_task =
        std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, job_manager_.get());
    load_task->ProcessBlockReport(BlockStatus::RECEIVED);
    ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::SUCCESS);

    UpdateStateDirectly(load_task, WorkflowState::PROCESSING);
    load_task->ProcessBlockReport(BlockStatus::DELETED);
    ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::CANCELED);

    UpdateStateDirectly(load_task, WorkflowState::PROCESSING);
    load_task->ProcessBlockReport(BlockStatus::CANCELED);
    ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::CANCELED);

    UpdateStateDirectly(load_task, WorkflowState::PROCESSING);
    load_task->ProcessBlockReport(BlockStatus::FAILED);
    ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::FAILED);
  }

  // Free processBlockReport
  {
    auto free_task = std::make_shared<ManagedFreeTask>(job_id, blk, job_manager_.get());
    free_task->ProcessBlockReport(BlockStatus::RECEIVED);
    ASSERT_EQ(free_task->GetWorkflowState(), WorkflowState::CREATED);

    UpdateStateDirectly(free_task, WorkflowState::PROCESSING);
    free_task->ProcessBlockReport(BlockStatus::DELETED);
    ASSERT_EQ(free_task->GetWorkflowState(), WorkflowState::SUCCESS);

    UpdateStateDirectly(free_task, WorkflowState::PROCESSING);
    free_task->ProcessBlockReport(BlockStatus::CANCELED);
    ASSERT_EQ(free_task->GetWorkflowState(), WorkflowState::CANCELED);

    UpdateStateDirectly(free_task, WorkflowState::PROCESSING);
    free_task->ProcessBlockReport(BlockStatus::FAILED);
    ASSERT_EQ(free_task->GetWorkflowState(), WorkflowState::FAILED);
  }

  // CopyReplicaTask processBlockReport
  {
    int32_t required_replica = 2;
    auto copy_task_canceled = std::make_shared<CopyReplicaTask>(
        job_id, blk, required_replica, job_manager_.get());
    // The blk is empty
    ASSERT_EQ(copy_task_canceled->GetWorkflowState(), WorkflowState::CREATED);
    copy_task_canceled->ProcessBlockReport(BlockStatus::DELETED);
    ASSERT_EQ(copy_task_canceled->GetWorkflowState(), WorkflowState::CANCELED);

    auto bm = job_manager_->GetBlockManager();
    bm->AddBlock(blk.blockid(),
                 2,
                 1,
                 blk.numbytes(),
                 blk.generationstamp(),
                 1,
                 cloudfs::IoMode::DATANODE_BLOCK,
                 BlockUCState::kComplete);

    auto copy_task = std::make_shared<CopyReplicaTask>(
        job_id, blk, 0, job_manager_.get());
    ASSERT_EQ(copy_task->GetWorkflowState(), WorkflowState::CREATED);
    // Enough required replica
    copy_task->ProcessBlockReport(BlockStatus::RECEIVED);
    ASSERT_EQ(copy_task->GetWorkflowState(), WorkflowState::SUCCESS);

    UpdateStateDirectly(copy_task, WorkflowState::PROCESSING);
    copy_task->ProcessBlockReport(BlockStatus::CANCELED);
    ASSERT_EQ(copy_task->GetWorkflowState(), WorkflowState::CANCELED);

    UpdateStateDirectly(copy_task, WorkflowState::PROCESSING);
    copy_task->ProcessBlockReport(BlockStatus::FAILED);
    ASSERT_EQ(copy_task->GetWorkflowState(), WorkflowState::FAILED);

    UpdateStateDirectly(copy_task, WorkflowState::PROCESSING);
    copy_task->SubmitTaskPlan();
    ASSERT_FALSE(copy_task->TaskExecTimeout());
    ASSERT_TRUE(copy_task->ShouldRetry());
    copy_task->IsTaskComplete();

    UpdateStateDirectly(copy_task, WorkflowState::PROCESSING);
    copy_task->ProcessBlockReport(BlockStatus::DELETED);
  }
}

TEST_F(ManagedTaskTest, TestChainTask) {
  ManagedJobId job_id = "JOB_1";
  std::unordered_set<DatanodeID> dn_id = {1};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);

  auto bm = job_manager_->GetBlockManager();
  bm->AddBlock(blk.blockid(),
               2,
               1,
               blk.numbytes(),
               blk.generationstamp(),
               1,
               cloudfs::IoMode::DATANODE_BLOCK,
               BlockUCState::kComplete);
  std::shared_ptr<ChainTask> chain_task = std::make_shared<ChainTask>(
      ManagedJobType::CHAIN, job_id, job_manager_.get());
  // Chain task with two sub tasks
  auto load_task =
      std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, job_manager_.get());
  auto copy_replica_task =
      std::make_shared<CopyReplicaTask>(job_id, blk, 1, job_manager_.get());
  chain_task->AppendTask(load_task)->AppendTask(copy_replica_task);

  uint64_t inode_id = 2;
  chain_task->SetInodeId(inode_id);
  auto sub_tasks = chain_task->SubTasks();
  for (auto t : sub_tasks) {
    ASSERT_EQ(inode_id, t->GetInodeId());
  }
  ASSERT_EQ(sub_tasks.size(), 2);

  chain_task->UpdateState(WorkflowState::FAILED);
  // Update first task failed
  ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::FAILED);
  ASSERT_EQ(chain_task->GetWorkflowState(), WorkflowState::FAILED);
  // Update first undone task success
  chain_task->UpdateState(WorkflowState::SUCCESS);
  chain_task->ToString();
  ASSERT_EQ(copy_replica_task->GetWorkflowState(), WorkflowState::SUCCESS);
  ASSERT_EQ(chain_task->GetWorkflowState(), WorkflowState::FAILED);
  bool isTaskDone = chain_task->IsDone();
  ASSERT_TRUE(isTaskDone);
  ASSERT_TRUE(chain_task->IsTaskComplete());

  std::shared_ptr<ChainTask> chain_task_1 = std::make_shared<ChainTask>(
      ManagedJobType::CHAIN, job_id, job_manager_.get());
  // Chain task with two sub tasks
  auto load_task_1 =
      std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, job_manager_.get());
  auto copy_replica_task_1 =
      std::make_shared<CopyReplicaTask>(job_id, blk, 1, job_manager_.get());
  chain_task_1->AppendTask(load_task_1)->AppendTask(copy_replica_task_1);

  chain_task_1->UpdateSubmitTimePoint();
  ASSERT_FALSE(chain_task_1->TaskExecTimeout());
  ASSERT_FALSE(chain_task_1->IsTaskComplete());
  ASSERT_TRUE(chain_task_1->ShouldRetry());
  ASSERT_TRUE(chain_task_1->IsTaskPlanIdempotent());
  chain_task_1->GetLastSubmitTime();
  chain_task_1->AddRetryTimes();
  chain_task_1->Cancel();
  ASSERT_EQ(WorkflowState::CANCELED, chain_task_1->GetWorkflowState());
  chain_task_1->ProcessBlockReport(BlockStatus::RECEIVED);
}

TEST_F(ManagedTaskTest, TestTask) {
  ManagedJobId job_id = "JOB_1";
  std::unordered_set<DatanodeID> dn_id = {1};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);

  auto load_task =
      std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, nullptr);
  load_task->ToString();
  load_task->UpdateSubmitTimePoint();

  ASSERT_FALSE(load_task->TaskExecTimeout());
  FLAGS_managed_task_load_execute_timeout_ms = 0;
  ASSERT_TRUE(load_task->TaskExecTimeout());
  ASSERT_TRUE(load_task->ShouldRetry());
  FLAGS_managed_task_load_max_retry_times = 0;
  ASSERT_FALSE(load_task->ShouldRetry());

  ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::CREATED);
  ASSERT_FALSE(load_task->IsDone());
  load_task->UpdateState(WorkflowState::SUCCESS);
  ASSERT_EQ(load_task->GetWorkflowState(), WorkflowState::SUCCESS);
  ASSERT_TRUE(load_task->IsDone());

  ManagedJobState job_state;
  ASSERT_EQ(job_state.GetSuccessTaskNum(), 0);

  load_task->CountForJobState(job_state);
  ASSERT_EQ(job_state.GetSuccessTaskNum(), 1);

  // Load Metadata task
  std::shared_ptr<RecursiveListingOption> opt =
      std::make_shared<RecursiveListingOption>();
  ManagedLoadMetadataTask load_metadata_task(job_id, nullptr, opt);
  load_metadata_task.ToString();

  ManagedJobState metadata_job_state;
  uint64_t mock_list_cnt = 10;
  *opt->list_count = mock_list_cnt;
  load_metadata_task.CountForJobState(metadata_job_state);
  ASSERT_EQ(metadata_job_state.GetTotalTaskNum(), mock_list_cnt + 1);
  ASSERT_EQ(metadata_job_state.GetSuccessTaskNum(), mock_list_cnt);

  load_metadata_task.UpdateState(WorkflowState::SUCCESS);
  load_metadata_task.CountForJobState(metadata_job_state);
  ASSERT_EQ(metadata_job_state.GetTotalTaskNum(), mock_list_cnt);
  ASSERT_EQ(metadata_job_state.GetSuccessTaskNum(), mock_list_cnt);

  ASSERT_FALSE(*GetCancelFlag(load_metadata_task));
  *opt->cancel_flag = true;
  ASSERT_TRUE(*GetCancelFlag(load_metadata_task));
}

TEST_F(ManagedTaskTest, LoadTaskSubmitTaskPlan) {
  ManagedJobId job_id = "JOB_1";
  std::unordered_set<DatanodeID> dn_id = {1};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);
  std::string mock_key = "mock_key";

  auto task =
      std::make_shared<ManagedLoadTask>(job_id, dn_id, blk, job_manager_.get());

  std::function<std::string(uint64_t)> cb = [this, mock_key](uint64_t inode_id) {
    return std::string(mock_key);
  };
  job_manager_->SetNsIdToPufsNameCB(cb);

  task->SubmitTaskPlan();
  ASSERT_EQ(1, GetBatchLoadCmd().size());
}

CreateRequestProto MakeCreateRequest() {
  CreateRequestProto create_request;
  create_request.set_src("");
  create_request.mutable_masked()->set_perm(0);
  create_request.set_clientname("client");
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
  create_request.set_createparent(false);
  create_request.set_replication(1);
  create_request.set_blocksize(128 * 1024 * 1024);
  return create_request;
}

// TEST_F(ManagedTaskTest, SetReplicationTask) {
//   ManagedJobId job_id = "JOB_1";
//   std::string path = "/test";
//   auto task1 = std::make_shared<SetReplicationTask>(job_id, path, 1, job_manager_.get());

//   // File not found
//   task1->SubmitTaskPlan();
//   ASSERT_EQ(WorkflowState::FAILED, task1->GetWorkflowState());

//   auto ns = job_manager_->GetNameSpace();
//   auto create_request = MakeCreateRequest();
//   create_request.set_src(path);
//   CreateResponseProto* create_response;
//   PermissionStatus p;
//   ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, create_response)
//                    .HasException());

//   auto task2 = std::make_shared<SetReplicationTask>(job_id, path, 1, job_manager_.get());
//   task2->SubmitTaskPlan();
//   ASSERT_EQ(WorkflowState::SUCCESS, task2->GetWorkflowState());
// }

TEST_F(ManagedTaskTest, LoadTaskCreator) {
  auto load_opt = std::make_shared<DataJobOption>();
  load_opt->replica_num = 2;
  std::vector<DatanodeID> dns = {1};
  std::shared_ptr<TaskCreator> task_creator =
      std::make_shared<LoadDataTaskCreator>(
          ManagedJobType::LOAD_DATA, "job_id", load_opt, job_manager_.get(), dns);

  ::cloudfs::LocatedBlockProto blk;
  blk.mutable_b()->set_poolid("This is a pool id");
  blk.mutable_b()->set_blockid(100000);
  blk.mutable_b()->set_numbytes(10);
  blk.mutable_b()->set_generationstamp(1000000);
  blk.set_offset(10);
  blk.set_corrupt(false);

  auto task = task_creator->ConstructTask(blk);
  ASSERT_NE(nullptr, task);
}

}  // namespace dancenn