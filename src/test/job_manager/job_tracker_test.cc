

#include <gtest/gtest.h>

#include <cstdint>
#include <memory>

#include "job_manager/managed_job_impl.h"
#include "job_test.h"

DECLARE_uint32(job_tracker_inspect_period_sec);
DECLARE_uint32(job_tracker_max_pending_block_tasks);

namespace dancenn {
class JobTrackerTest : public JobTest {
 public:
  std::map<ManagedJobId, std::shared_ptr<ManagedJob>>& GetJobQueue(
      std::shared_ptr<JobTracker> tracker);
  std::unordered_map<uint64_t, ManagedTaskSet>& GetTaskQueue(
      std::shared_ptr<JobTracker> tracker);
  void InspectBlockTasks(std::shared_ptr<JobTracker> tracker);
  void InspectManagedJob(std::shared_ptr<JobTracker> tracker);
  void InspectMetaStorageJobInfo(std::shared_ptr<JobTracker> tracker);
};

std::map<ManagedJobId, std::shared_ptr<ManagedJob>>& JobTrackerTest::
    GetJobQueue(std::shared_ptr<JobTracker> tracker) {
  return tracker->job_id_to_progress_;
}

std::unordered_map<uint64_t, ManagedTaskSet>& JobTrackerTest::GetTaskQueue(
    std::shared_ptr<JobTracker> tracker) {
  return tracker->blk_id_to_tasks_;
}

void JobTrackerTest::InspectBlockTasks(std::shared_ptr<JobTracker> tracker) {
  tracker->InspectBlockTasks();
}

void JobTrackerTest::InspectManagedJob(std::shared_ptr<JobTracker> tracker) {
  tracker->InspectManagedJob();
}

void JobTrackerTest::InspectMetaStorageJobInfo(
    std::shared_ptr<JobTracker> tracker) {
  tracker->InspectMetaStorageJobInfo();
}

TEST_F(JobTrackerTest, TestRegisterJob) {
  auto job_tracker = job_manager_->GetJobTracker();

  ManagedJobId job_id;
  INode inode;
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  std::shared_ptr<ManagedJob> job1 = std::make_shared<LoadDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &job_id);
  std::shared_ptr<ManagedJob> job2 = std::make_shared<LoadDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &job_id);

  bool res = job_tracker->RegisterJob(job1);
  ASSERT_TRUE(res);
  ManagedJobState state;
  Status status = job_tracker->LookupJobState(job_id, state);
  ASSERT_TRUE(status.IsOK());

  res = job_tracker->RegisterJob(job2);
  ASSERT_FALSE(res);

  std::map<ManagedJobId, std::shared_ptr<ManagedJob>>& job_queue =
      GetJobQueue(job_tracker);
  auto iter = job_queue.find(job_id);
  ASSERT_TRUE(iter != job_queue.end());
}

TEST_F(JobTrackerTest, TestRegisterTask) {
    auto job_tracker = job_manager_->GetJobTracker();
  cloudfs::ExtendedBlockProto blk;
  uint64_t blk_id = 1;
  blk.set_blockid(blk_id);
  blk.set_generationstamp(1002);
  blk.set_numbytes(2048);

  ManagedJobId chain_job_id = WorkflowIdGenerator::Instance().nextJobId();
  std::shared_ptr<ManagedTask> blk_task =
      std::make_shared<ManagedFreeTask>(chain_job_id, blk, job_manager_.get());

  // Register chain task
  std::shared_ptr<ChainTask> chain_task = std::make_shared<ChainTask>(
      ManagedJobType::CHAIN, chain_job_id, job_manager_.get());
  chain_task->AppendTask(blk_task);

      std::shared_ptr<ManagedTask> chain_managed_task =
        std::dynamic_pointer_cast<ManagedTask>(chain_task);

  std::shared_ptr<ManagedJob> chain_job =
      std::make_shared<SingleTaskJob>(job_manager_.get(),
                                      mock_task_handler_,
                                      chain_managed_task,
                                      ManagedJobType::CHAIN,
                                      &chain_job_id,
                                      false);
  // Job not register
  Status status = job_tracker->RegisterTask(chain_managed_task);
  ASSERT_EQ(Code::kJobNotFound, status.code());
  bool res = job_tracker->RegisterJob(chain_job);
  ASSERT_TRUE(res);
  status = job_tracker->RegisterTask(chain_managed_task);
  ASSERT_TRUE(status.IsOK());
  // Track for block task
  std::unordered_map<uint64_t, ManagedTaskSet>& task_queue =
      GetTaskQueue(job_tracker);
  auto iter = task_queue.find(blk_id);
  ASSERT_TRUE(iter != task_queue.end());
}

TEST_F(JobTrackerTest, TestJobTracker) {
  // Stop inspect
  auto job_tracker = job_manager_->GetJobTracker();

  ManagedJobId job_id;
  INode inode;
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  std::shared_ptr<ManagedJob> load_job = std::make_shared<LoadDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &job_id);

  std::map<ManagedJobId, std::shared_ptr<ManagedJob>>& job_queue =
      GetJobQueue(job_tracker);
  std::unordered_map<uint64_t, ManagedTaskSet>& task_queue =
      GetTaskQueue(job_tracker);

  ASSERT_EQ(job_queue.size(), 0);
  bool ret = job_tracker->RegisterJob(load_job);
  ASSERT_TRUE(ret);
  ASSERT_EQ(job_queue.size(), 1);
  ASSERT_TRUE(job_manager_->GetJobTracker()->ContainJob(job_id));

  ManagedJobState job_state;
  job_tracker->LookupJobState(job_id, job_state);

  // Register task
  DatanodeInfoPtr dn = datanode_manager_->GetDatanodeFromUuid(DN_UUID);
  std::unordered_set<DatanodeID> dn_id = {dn->id()};
  std::vector<ExtendedBlockProto> blocks;
  ExtendedBlockProto blk;
  blk.set_poolid("pool1");
  blk.set_blockid(1);
  blk.set_generationstamp(1);
  blk.set_numbytes(1);
  blocks.emplace_back(blk);
  blk.set_blockid(2);
  blocks.emplace_back(blk);

  for (auto blk : blocks) {
    auto task = std::make_shared<ManagedLoadTask>(
        job_id, dn_id, blk, job_manager_.get());
    job_tracker->RegisterTask(task);
  }

  // Throttle task
  ASSERT_FALSE(job_tracker->ThrottleTaskRegister());
  FLAGS_job_tracker_max_pending_block_tasks = 1;
  ASSERT_TRUE(job_tracker->ThrottleTaskRegister());

  // Mock DN load block
  job_tracker->TrackBlockTask(1, BlockStatus::RECEIVED);
  job_tracker->TrackBlockTask(2, BlockStatus::RECEIVED);

  ASSERT_EQ(task_queue.size(), 2);
  for (auto& pair : task_queue) {
    ManagedTaskSet& task_set = pair.second;
    ASSERT_EQ(task_set.size(), 1);
    for (std::shared_ptr<ManagedTask> task_ptr : task_set) {
      ASSERT_EQ(ManagedJobType::LOAD_DATA, task_ptr->GetTaskType());
      ASSERT_TRUE(task_ptr->IsTaskComplete());
      ASSERT_EQ(job_id, task_ptr->GetJobId());
    }
  }

  InspectBlockTasks(job_tracker);
  ASSERT_EQ(task_queue.size(), 0);

  InspectManagedJob(job_tracker);
  ASSERT_EQ(job_queue.size(), 1);

  job_tracker->SetJobSubmitted(job_id);
  InspectManagedJob(job_tracker);
  ASSERT_EQ(job_queue.size(), 0);

  std::shared_ptr<ManagedJob> free_job = std::make_shared<FreeDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &job_id);
  ret = job_tracker->RegisterJob(free_job);
  ASSERT_TRUE(ret);

  auto status = job_tracker->CompleteJob(job_id, WorkflowState::CANCELED);
  ASSERT_TRUE(status.IsOK());

  ManagedJobState free_job_state;
  job_tracker->LookupJobState(job_id, free_job_state);
  ASSERT_EQ(ManagedJobType::FREE_DATA, free_job_state.GetJobType());
  ASSERT_EQ(WorkflowState::CANCELED, free_job_state.GetWorkflowState());

  std::vector<ManagedJobId> jobs;
  job_tracker->ListJob(jobs, ManagedJobType::FREE_DATA);
  ASSERT_EQ(jobs.size(), 1);

  jobs.clear();
  job_tracker->ListJob(jobs, ManagedJobType::LOAD_DATA);
  ASSERT_EQ(jobs.size(), 0);

  jobs.clear();
  job_tracker->ListJob(jobs, ManagedJobType::UNKNOWN);
  ASSERT_EQ(jobs.size(), 1);

  // Process invalid job
  const std::string job_id_only_in_rocksdb = "invalid_job_id";
  JobStatusOpBody job_body;

  job_body.set_state(::dancenn::JobStatusOpBody_WorkflowState::
                         JobStatusOpBody_WorkflowState_CREATED);
  job_body.set_created_timestamp(0);
  ManagedJobState job_state_only_in_rocksdb;

  auto sleepTime = FLAGS_job_tracker_metastorage_inspect_period_sec;
  FLAGS_job_tracker_metastorage_inspect_period_sec = 10000;
  sleep(sleepTime);

  job_manager_->UpdateJobStatusHA(
      job_id_only_in_rocksdb, ManagedJobType::LOAD_DATA, job_body, false);
  status = job_manager_->LookupJobState(job_id_only_in_rocksdb,
                                        job_state_only_in_rocksdb);
  while (!status.IsOK()) {
    status = job_manager_->LookupJobState(job_id_only_in_rocksdb,
                                          job_state_only_in_rocksdb);
  }
  ASSERT_TRUE(status.IsOK());

  InspectMetaStorageJobInfo(job_tracker);
  status = job_manager_->LookupJobState(job_id_only_in_rocksdb,
                                        job_state_only_in_rocksdb);
  while (status.IsOK()) {
    status = job_manager_->LookupJobState(job_id_only_in_rocksdb,
                                          job_state_only_in_rocksdb);
  }
  ASSERT_FALSE(status.IsOK());
}

}  // namespace dancenn