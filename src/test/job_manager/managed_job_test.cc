
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include <cstddef>
#include <memory>
#include <string>
#include <vector>

#include "inode.pb.h"
#include "job_manager/job_manager_def.h"
#include "job_manager/managed_job_impl.h"
#include "job_manager/managed_task_impl.h"
#include "job_manager/workflow_id_generator.h"
#include "job_test.h"

DECLARE_int32(managed_metadata_job_exec_timeout_ms);

namespace dancenn {
class ManagedJobTest : public JobTest {
 public:
  void CompleteState(WrappedJob& load_job, WorkflowState state) {
    load_job.job_state_.CompleteState(state);
  }

  size_t GetCurrJobIdx(std::shared_ptr<OrderedChainJob> job) {
    return job->curr_job_idx_;
  }

  WorkflowState SubJobWorkflowState(std::shared_ptr<OrderedChainJob> job) {
    return job->SubJobWorkflowState();
  }

  //  void SetUp() override {
  //    mock_task_handler_ = std::make_shared<ManagedTaskHandler>();
  //    mock_task_handler_->Start();
  //  }

  //  std::shared_ptr<ManagedTaskHandler> mock_task_handler_;
};

class MockTaskCreator : public TaskCreator {
 public:
  MockTaskCreator(ManagedJobType type, const ManagedJobId& job_id)
      : TaskCreator(type, job_id) {
  }

  virtual std::shared_ptr<ManagedTask> ConstructTask() override {
    return nullptr;
  }
};

TEST_F(ManagedJobTest, TestChainJob) {
  INode inode;
  ManagedJobId resident_job_id;
  std::shared_ptr<ManagedTask> data_resident_task =
      std::make_shared<MarkResidentDataTask>(
          resident_job_id, "mock_path", &inode, true, job_manager_.get());
  std::shared_ptr<ManagedJob> resident_job =
      std::make_shared<SingleTaskJob>(job_manager_.get(),
                                      mock_task_handler_,
                                      data_resident_task,
                                      ManagedJobType::RESIDENT_DATA,
                                      &resident_job_id,
                                      false);
  LOG(INFO) << "resident_job_id: " << resident_job_id;
  ASSERT_FALSE(resident_job_id.empty());

  ManagedJobId load_job_id = WorkflowIdGenerator::Instance().nextJobId();
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  std::shared_ptr<ManagedJob> load_job = std::make_shared<LoadDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &load_job_id, false);

  std::vector<std::shared_ptr<ManagedJob>> sub_jobs{load_job, resident_job};
  ManagedJobId chain_job_id;
  std::shared_ptr<OrderedChainJob> chain_job =
      std::make_shared<OrderedChainJob>(job_manager_.get(),
                                        mock_task_handler_,
                                        mock_job_handler_,
                                        sub_jobs,
                                        &chain_job_id);

  chain_job->GetJobState().SetJobSubmitted();
  // All sub task is created
  ASSERT_EQ(SubJobWorkflowState(chain_job), WorkflowState::CREATED);
  ASSERT_EQ(0, GetCurrJobIdx(chain_job));

  // Update first job state to success
  load_job->Complete();
  chain_job->RefreshWorkflowState();
  // Move the index to the next unfinished job
  ASSERT_EQ(1, GetCurrJobIdx(chain_job));
  ASSERT_EQ(SubJobWorkflowState(chain_job), WorkflowState::CREATED);

  // Update second job state to failed
  resident_job->Complete(WorkflowState::FAILED);
  chain_job->RefreshWorkflowState();
  // Complete chain job state. No need to move the index
  ASSERT_EQ(1, GetCurrJobIdx(chain_job));
  ASSERT_EQ(SubJobWorkflowState(chain_job), WorkflowState::FAILED);

  ASSERT_EQ(WorkflowState::FAILED, chain_job->GetJobState().GetWorkflowState());
}

TEST_F(ManagedJobTest, RefreshWorkflowState) {
  INode inode;
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  ManagedJobId job_id = "JOB_1";
  std::shared_ptr<TaskCreator> creator =
      std::make_shared<MockTaskCreator>(ManagedJobType::LOAD_DATA, job_id);
  WrappedJob mock_timeout_job(
      job_manager_.get(), mock_task_handler_, &inode, opt, creator, &job_id);
  WrappedJob mock_job(
      job_manager_.get(), mock_task_handler_, &inode, opt, creator, &job_id);

  auto& job_state = mock_job.GetJobState();
  job_state.SetJobSubmitted();
  job_state.SetTotalTaskNum(10);
  mock_job.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::CREATED, job_state.GetWorkflowState());

  job_state.SetSuccessTaskNum(9);
  job_state.AddThrottledTaskNum();
  mock_job.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::THROTTLED, job_state.GetWorkflowState());

  WrappedJob mock_job1(
      job_manager_.get(), mock_task_handler_, &inode, opt, creator, &job_id);
  auto& job_state1 = mock_job1.GetJobState();
  job_state1.SetJobSubmitted();
  job_state1.SetTotalTaskNum(10);
  job_state1.SetSuccessTaskNum(10);
  mock_job1.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::SUCCESS, job_state1.GetWorkflowState());

  WrappedJob mock_job2(
      job_manager_.get(), mock_task_handler_, &inode, opt, creator, &job_id);
  auto& job_state2 = mock_job2.GetJobState();
  job_state2.SetJobSubmitted();
  job_state2.SetTotalTaskNum(10);
  job_state2.SetSuccessTaskNum(9);
  job_state2.AddCanceledTaskNum(1);
  mock_job2.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::SUCCESS, job_state2.GetWorkflowState());

  WrappedJob mock_job3(
      job_manager_.get(), mock_task_handler_, &inode, opt, creator, &job_id);
  auto& job_state3 = mock_job3.GetJobState();
  job_state3.SetJobSubmitted();
  job_state3.SetTotalTaskNum(10);
  job_state3.SetSuccessTaskNum(9);
  job_state3.AddFailedTaskNum(1);
  mock_job3.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::FAILED, job_state3.GetWorkflowState());

  auto& job_state4 = mock_timeout_job.GetJobState();
  job_state4.SetJobSubmitted();
  FLAGS_managed_metadata_job_exec_timeout_ms = 0;
  mock_timeout_job.RefreshWorkflowState();
  ASSERT_EQ(WorkflowState::TIMEOUT, job_state4.GetWorkflowState());
}

TEST_F(ManagedJobTest, TestJob) {
  INode inode;
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  ManagedJobId job_id = "JOB_1";
  std::shared_ptr<TaskCreator> creator =
      std::make_shared<MockTaskCreator>(ManagedJobType::LOAD_DATA, job_id);
  WrappedJob mock_job(
      nullptr, mock_task_handler_, &inode, opt, creator, &job_id);

  EXPECT_EQ(mock_job.GetJobId(), job_id);
  EXPECT_EQ(mock_job.GetJobType(), ManagedJobType::LOAD_DATA);
  EXPECT_EQ(mock_job.GetJobState().GetWorkflowState(), WorkflowState::CREATED);
  EXPECT_FALSE(mock_job.IsComplete());

  CompleteState(mock_job, WorkflowState::SUCCESS);
  EXPECT_EQ(mock_job.GetJobState().GetWorkflowState(), WorkflowState::SUCCESS);
  EXPECT_TRUE(mock_job.IsComplete());

  EXPECT_FALSE(mock_job.ExecutionTimeout());
  FLAGS_managed_metadata_job_exec_timeout_ms = 0;
  EXPECT_TRUE(mock_job.ExecutionTimeout());
}

TEST_F(ManagedJobTest, TestManagedJobState) {
  std::string job_id = "JOB_1";
  ManagedJobState job_state{job_id, ManagedJobType::LOAD_DATA};

  int success_tasks = 1;
  int fail_tasks = 3;
  int cancel_tasks = 5;

  job_state.AddTotalTaskNum(success_tasks + fail_tasks + cancel_tasks);
  job_state.AddSuccessTaskNum(success_tasks - 1);
  job_state.AddFailedTaskNum(fail_tasks);
  job_state.AddCanceledTaskNum(cancel_tasks);

  ASSERT_EQ(job_state.GetJobId(), job_id);
  ASSERT_EQ(job_state.GetJobType(), ManagedJobType::LOAD_DATA);
  ASSERT_EQ(success_tasks - 1, job_state.GetSuccessTaskNum());
  ASSERT_EQ(fail_tasks, job_state.GetFailedTaskNum());
  ASSERT_EQ(cancel_tasks, job_state.GetCanceledTaskNum());
  ASSERT_EQ(success_tasks + fail_tasks + cancel_tasks,
            job_state.GetTotalTaskNum());

  ASSERT_EQ(WorkflowState::CREATED, job_state.GetWorkflowState());
  job_state.UpdateWorkflowState(WorkflowState::PROCESSING);
  ASSERT_EQ(WorkflowState::PROCESSING, job_state.GetWorkflowState());
}

TEST_F(ManagedJobTest, ChainJobConsumeManagedJob) {
    INode inode;
  ManagedJobId resident_job_id;
  std::shared_ptr<ManagedTask> data_resident_task =
      std::make_shared<MarkResidentDataTask>(
          resident_job_id, "mock_path", &inode, true, job_manager_.get());
  std::shared_ptr<ManagedJob> resident_job =
      std::make_shared<SingleTaskJob>(job_manager_.get(),
                                      mock_task_handler_,
                                      data_resident_task,
                                      ManagedJobType::RESIDENT_DATA,
                                      &resident_job_id,
                                      false);
  LOG(INFO) << "resident_job_id: " << resident_job_id;
  ASSERT_FALSE(resident_job_id.empty());

  ManagedJobId load_job_id = WorkflowIdGenerator::Instance().nextJobId();
  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();
  std::shared_ptr<ManagedJob> load_job = std::make_shared<LoadDataJob>(
      job_manager_.get(), mock_task_handler_, &inode, opt, &load_job_id, false);

  std::vector<std::shared_ptr<ManagedJob>> empty_sub_jobs{};
  ManagedJobId empty_chain_job_id;
  std::shared_ptr<OrderedChainJob> empty_chain_job =
      std::make_shared<OrderedChainJob>(job_manager_.get(),
                                        mock_task_handler_,
                                        mock_job_handler_,
                                        empty_sub_jobs,
                                        &empty_chain_job_id);
  empty_chain_job->ConsumeManagedJob();
  EXPECT_EQ(WorkflowState::SUCCESS,
            empty_chain_job->GetJobState().GetWorkflowState());

  std::vector<std::shared_ptr<ManagedJob>> sub_jobs{load_job, resident_job};
  ManagedJobId chain_job_id;
  std::shared_ptr<OrderedChainJob> chain_job =
      std::make_shared<OrderedChainJob>(job_manager_.get(),
                                        mock_task_handler_,
                                        mock_job_handler_,
                                        sub_jobs,
                                        &chain_job_id);
  chain_job->ConsumeManagedJob();
  ASSERT_TRUE(chain_job->GetJobState().IsJobSubmitted());
}

TEST_F(ManagedJobTest, TestCountForJobState) {
  std::string job_id = "JOB_1";
  ManagedJobState job_state{job_id, ManagedJobType::LOAD_DATA};

  WorkflowState state = WorkflowState::SUCCESS;
  bool res = job_state.CountForJobState(state);
  ASSERT_TRUE(res);
  state = WorkflowState::FAILED;
  res = job_state.CountForJobState(state);
  ASSERT_TRUE(res);
  state = WorkflowState::CANCELED;
  res = job_state.CountForJobState(state);
  ASSERT_TRUE(res);
  state = WorkflowState::TIMEOUT;
  res = job_state.CountForJobState(state);
  ASSERT_TRUE(res);
  state = WorkflowState::THROTTLED;
  res = job_state.CountForJobState(state);
  ASSERT_TRUE(res);
  state = WorkflowState::PROCESSING;
  res = job_state.CountForJobState(state);
  ASSERT_FALSE(res);
  ASSERT_EQ(1, job_state.GetSuccessTaskNum());
  ASSERT_EQ(1, job_state.GetFailedTaskNum());
  ASSERT_EQ(1, job_state.GetCanceledTaskNum());
  ASSERT_EQ(1, job_state.GetTimeoutTaskNum());
  ASSERT_EQ(1, job_state.GetThrottledTaskNum());
}

}  // namespace dancenn