#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include "inode.pb.h"
#include "job_manager/job_manager_def.h"
#include "job_manager/job_manager.h"
#include "job_test.h"

namespace dancenn {
class JobManagerTest : public JobTest {};

TEST_F(JobManagerTest, SubmitLoadMultiReplicaJob) {
  INode inode;
  inode.set_id(1);
  inode.set_replication(2);
  PinStatus* pin_status = inode.mutable_pin_status();
  pin_status->set_pinned(true);
  ManagedJobId job_id = "job_id";

  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();

  Status status =
      job_manager_->SubmitLoadMultiReplicaJob(&inode, "/load", opt, &job_id);
  ASSERT_TRUE(status.IsOK());
}

TEST_F(JobManagerTest, SubmitFreeDataJob) {
  INode inode;
  inode.set_id(1);
  inode.set_replication(2);
  PinStatus* pin_status = inode.mutable_pin_status();
  pin_status->set_pinned(true);
  pin_status->set_resident_data(true);
  ManagedJobId job_id = "job_id";

  std::shared_ptr<DataJobOption> opt = std::make_shared<DataJobOption>();

  Status status =
      job_manager_->SubmitFreeDataJob(&inode, "/load", opt, &job_id);
  ASSERT_TRUE(status.IsOK());
}

TEST_F(JobManagerTest, CheckJobManagerStatus) {
  auto job_manager = std::make_shared<JobManager>(nullptr, nullptr);
  Status status = job_manager->CheckJobManagerStatus();
  ASSERT_FALSE(status.IsOK());

  job_manager->Start();
  status = job_manager->CheckJobManagerStatus();
  ASSERT_TRUE(status.IsOK());

  job_manager->Stop();
  job_manager->Stop();
  status = job_manager->CheckJobManagerStatus();
  ASSERT_FALSE(status.IsOK());
}

}  // namespace dancenn
