#include "job_test.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_string(all_datacenters);
DECLARE_int32(namespace_type);
DECLARE_uint32(job_tracker_inspect_period_sec);
DECLARE_uint32(job_tracker_max_pending_block_tasks);

namespace dancenn {

void JobTest::SetUp() {
  FLAGS_run_ut = true;
  FLAGS_all_datacenters = "LF,HL,LQ";
  FLAGS_lease_expired_soft_limit_ms = 1;
  FLAGS_lease_expired_hard_limit_ms = 1;
  FLAGS_datanode_keep_alive_timeout_sec = 2000;

  ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

  ugi_ = UserGroupInfo("root", "supergroup");
  datanode_manager_ = std::make_shared<DatanodeManager>();
  block_manager_ = std::make_shared<BlockManager>();
  std::shared_ptr<EditLogContextBase> edit_log_ctx =
      std::static_pointer_cast<EditLogContextBase>(
          std::make_shared<MockEditLogContext>());

  MockFSImageTransfer(db_path_).Transfer();
  job_manager_ =
      std::make_shared<JobManager>(datanode_manager_, block_manager_);
  block_manager_->set_job_manager(job_manager_);
  mock_task_handler_ = std::make_shared<ManagedTaskHandler>();
  mock_job_handler_ = std::make_shared<ManagedJobHandler>();
  mock_task_handler_->Start();
  mock_job_handler_->Start();

  ns_.reset(new MockNameSpace(db_path_,
                              edit_log_ctx,
                              block_manager_,
                              datanode_manager_,
                              std::make_shared<DataCenters>(),
                              nullptr,
                              nullptr,
                              ugi_,
                              job_manager_));
  auto ha_state = std::make_unique<MockHAState>();
  ha_state->should_populate_replication_queues = true;
  ha_state_ = std::move(ha_state);
  safe_mode_ = std::make_unique<MockSafeMode>();
  ns_->set_safemode(safe_mode_.get());
  ns_->set_ha_state(ha_state_.get());
  block_manager_->set_datanode_manager(datanode_manager_);
  block_manager_->set_ha_state(ha_state_.get());
  block_manager_->set_safemode(safe_mode_.get());
  block_manager_->set_ns(ns_.get());
  datanode_manager_->set_block_manager(block_manager_.get());
  ns_->Start();
  ns_->StartActive();

  job_manager_->set_ns(ns_);

  // add a datanode to the cluster
  auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg.mutable_datanodeid()->set_datanodeuuid(DN_UUID);
  cnetpp::base::IPAddress ip("***********");
  datanode_manager_->Register(reg.datanodeid(), &reg, ip);
  datanode_manager_->RefreshConfig();

  StartHeartbeat();

  // mock edit log sender
  auto last_tx_id = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx, last_tx_id));
  ns_->TestOnlySetEditLogSender(std::move(sender));
}

void JobTest::StartHeartbeat() {
  stop_ = false;
  pause_ = false;
  CountDownLatch latch(1);
  heartbeat_thread_ = std::thread([&latch, this]() {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    cnetpp::base::IPAddress ip("***********");
    RepeatedStorageReport reports;
    auto r = reports.Add();
    r->set_storageuuid("storage1");
    r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    r->mutable_storage()->set_storageuuid("storage1");
    HeartbeatRequestProto request;
    request.mutable_registration()->CopyFrom(reg);
    request.mutable_reports()->CopyFrom(reports);
    bool heartbeated = false;
    while (!stop_) {
      if (!pause_) {
        DatanodeManager::RepeatedCmds cmds;
        datanode_manager_->Heartbeat(request, &cmds);
        if (!heartbeated) {
          latch.CountDown();
          heartbeated = true;
        }
      }
      std::this_thread::sleep_for(std::chrono::seconds(1));
    }
  });
  latch.Await();
}

void JobTest::TearDown() {
  stop_ = true;
  heartbeat_thread_.join();
  ns_->StopActive();
  ns_.reset();
  FileUtils::DeleteDirectoryRecursively(db_path_);
}

}  // namespace dancenn