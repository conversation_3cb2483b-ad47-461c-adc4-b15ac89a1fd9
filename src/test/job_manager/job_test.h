
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include <memory>
#include <string>
#include <vector>

#include "base/file_utils.h"
#include "base/path_util.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

namespace dancenn {
class JobTest : public testing::Test {
 public:
  void SetUp() override;
  void TearDown() override;

  void StartHeartbeat();

  std::thread heartbeat_thread_;
  bool stop_;
  bool pause_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::shared_ptr<JobManager> job_manager_;
  std::unique_ptr<SafeModeBase> safe_mode_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MockNameSpace> ns_;
  UserGroupInfo ugi_;
  const std::string CLIENT_IP = "***********";
  LogRpcInfo default_rpc_info_ = LogRpcInfo("", 0);
  const std::string CLIENT_NAME = "test_client";
  const std::string STORAGE_UUID = "storage";
  const std::string DN_UUID = "datanode1";
  std::shared_ptr<ManagedTaskHandler> mock_task_handler_;
  std::shared_ptr<ManagedJobHandler> mock_job_handler_;
};

}  // namespace dancenn