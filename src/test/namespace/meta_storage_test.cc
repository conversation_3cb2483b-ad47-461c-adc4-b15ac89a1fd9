//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include "namespace/meta_storage.h"

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <cstdlib>
#include <cstring>
#include <functional>
#include <memory>

#include "base/file_utils.h"
#include "namespace/inode.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "proto/generated/dancenn/status_code.pb.h"
#include "test/dancenn_test_enviroment.h"
#include "test/namespace/gmock_meta_storage.h"
#include "test/namespace/gmock_writer.h"
#include "test/proto/generated/dancenn/block_info_proto.h"
#include "test/proto/generated/dancenn/edit_log.h"

DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_bool(dfs_meta_storage_rocksdb_perf_scr_enabled);
DECLARE_uint64(dfs_meta_storage_update_scr_batch_size);

namespace dancenn {
namespace {

rocksdb::Slice EncodeBlockID(BlockID blk_id) {
  static std::vector<std::string> slice_holders;
  std::string blk_id_str;
  blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  slice_holders.push_back(blk_id_str);
  return slice_holders.back();
}

}  // namespace

class MetaStorageTest : public testing::Test {
 public:
  void SetUp() override {
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_store_.reset(new MetaStorage(db_path_));
    meta_store_->Launch();
    meta_store_->SaveNumINodes(0);
    auto be_last_inode_id = platform::HostToBigEndian(static_cast<INodeID>(1));

    meta_store_->PutNameSystemInfo(
        kLastINodeIdKey,
        cnetpp::base::StringPiece(reinterpret_cast<char*>(&be_last_inode_id),
                                  sizeof(be_last_inode_id)));
    meta_store_->ResetLastAppliedTxId(0);
    meta_store_->ResetLastInodeId(1);
    counter_.store(0);
  }

  void TearDown() override {
    meta_store_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  void TestOrderedInsertINode(INode* new_inode,
                              int64_t txid,
                              Closure* done = nullptr,
                              INode* parent = nullptr) {
    std::vector<INode*> inodes_add = { new_inode };
    if (parent) {
      INodeInPath parent_iip;
      parent->Swap(&parent_iip.MutableInodeUnsafe());
      std::vector<INodeAndSnapshot> parents;
      SnapshotLog dummy_snaplog;
      parents.emplace_back(&parent_iip.MutableInode(), &dummy_snaplog);
      meta_store_->OrderedCommitINodes(&inodes_add,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       &parents,
                                       nullptr,
                                       {},
                                       {},
                                       txid,
                                       {done});
      parent->Swap(&parent_iip.MutableInodeUnsafe());
    } else {
      meta_store_->OrderedCommitINodes(&inodes_add,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       {},
                                       {},
                                       txid,
                                       {done});
    }
  }

  INodeID LoadLastInodeId() {
    std::string inode_id;
    meta_store_->GetNameSystemInfo(kLastINodeIdKey, &inode_id);
    return platform::ReadBigEndian<INodeID>(&(inode_id[0]), 0);
  }

  std::shared_ptr<INode> CreateINode(uint64_t pid,
                                     const std::string& file_name,
                                     uint64_t id_file,
                                     INode::Type type = INode_Type_kFile) {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("root");
    p.set_permission(FsPermission::GetFileDefault().ToShort());

    auto inode_a = std::make_shared<INode>();
    MakeINode(id_file, pid, file_name, p, type, inode_a.get());
    return inode_a;
  }

  std::unique_ptr<MetaStorage> meta_store_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::atomic<uint64_t> counter_;
  SnapshotLog snaplog_;
};

TEST_F(MetaStorageTest, NameSystemInfo) {
  std::string k1 = "aa";
  std::string v1 = "aaaa";
  std::string v1_resp;
  EXPECT_FALSE(meta_store_->GetNameSystemInfo(k1, &v1_resp));

  meta_store_->PutNameSystemInfo(k1, v1);
  EXPECT_TRUE(meta_store_->GetNameSystemInfo(k1, &v1_resp));
  EXPECT_EQ(v1, v1_resp);

  EXPECT_FALSE(meta_store_->GetNameSystemInfo("a", &v1_resp));
  EXPECT_FALSE(meta_store_->GetNameSystemInfo("aaa", &v1_resp));
}

TEST_F(MetaStorageTest, PutINode) {
  uint64_t pid = 1000;
  std::string a = "aaaa_dir";
  std::string b = "bbbbb_dir";
  std::string c = "aaa";
  uint64_t id_a = 1001;
  uint64_t id_b = 1002;
  uint64_t id_c = 1003;

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  auto inode_a = std::make_shared<INode>();
  MakeINode(id_a, pid, a, p, INode::kDirectory, inode_a.get());

  auto inode_b = std::make_shared<INode>();
  MakeINode(id_b, pid, b, p, INode::kDirectory, inode_b.get());

  auto inode_c = std::make_shared<INode>();
  MakeINode(id_c, pid, c, p, INode::kDirectory, inode_c.get());

  meta_store_->InsertINode(inode_a);
  meta_store_->InsertINode(inode_b);
  meta_store_->InsertINode(inode_c);

  auto stored_a = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, a, &stored_a) == kOK);
  auto stored_b = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, b, &stored_b) == kOK);
  auto stored_c = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, c, &stored_c) == kOK);

  meta_store_->TestOnlyDeleteINode(*inode_c);
  ASSERT_FALSE(meta_store_->GetINode(pid, c, &stored_c) == kOK);

  ASSERT_EQ(stored_a.id(), id_a);
  ASSERT_EQ(stored_a.parent_id(), pid);
  ASSERT_EQ(stored_a.name(), a);
  ASSERT_EQ(stored_a.type(), INode::kDirectory);

  uint64_t stored_id_b = meta_store_->GetINodeId(pid, b);
  uint64_t error_id = meta_store_->GetINodeId(id_b, b);
  ASSERT_EQ(stored_id_b, id_b);
  ASSERT_EQ(error_id, 0);

  meta_store_->TestOnlyDeleteINode(*inode_a);
  meta_store_->TestOnlyDeleteINode(*inode_b);
  INodeID cur_last_inode_id = LoadLastInodeId();
  // Put() do not change last inode id
  ASSERT_EQ(cur_last_inode_id, 1);
}

TEST_F(MetaStorageTest, PutFile) {
  uint64_t pid = 1000;
  std::string a = "aaaa_file";
  std::string b = "bbbbb_file";
  std::string c = "aaa";
  uint64_t id_a = 1001;
  uint64_t id_b = 1002;
  uint64_t id_c = 1003;

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  auto inode_a = std::make_shared<INode>();
  auto inode_b = std::make_shared<INode>();
  auto inode_c = std::make_shared<INode>();
  MakeINodeFile(id_a, pid, a, p, inode_a.get());
  MakeINodeFile(id_b, pid, b, p, inode_b.get());
  MakeINodeFile(id_c, pid, c, p, inode_c.get());
  meta_store_->InsertINode(inode_a);
  meta_store_->InsertINode(inode_b);
  meta_store_->InsertINode(inode_c);

  auto stored_a = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, a, &stored_a) == kOK);
  auto stored_b = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, b, &stored_b) == kOK);
  auto stored_c = INode();
  ASSERT_TRUE(meta_store_->GetINode(pid, c, &stored_c) == kOK);

  meta_store_->TestOnlyDeleteINode(*inode_c);
  ASSERT_FALSE(meta_store_->GetINode(pid, c, &stored_c) == kOK);

  ASSERT_EQ(stored_a.id(), id_a);
  ASSERT_EQ(stored_a.parent_id(), pid);
  ASSERT_EQ(stored_a.name(), a);
  ASSERT_EQ(stored_a.type(), INode::kFile);

  uint64_t stored_id_b = meta_store_->GetINodeId(pid, b);
  uint64_t error_id = meta_store_->GetINodeId(id_b, b);
  ASSERT_EQ(stored_id_b, id_b);
  ASSERT_EQ(error_id, 0);

  meta_store_->TestOnlyDeleteINode(*inode_a);
  meta_store_->TestOnlyDeleteINode(*inode_b);
}

TEST_F(MetaStorageTest, GetSubINodes) {
  std::string start_after = "";
  bool has_more = false;
  std::vector<INode> sub_inodes;
  meta_store_->GetSubINodes(
      kRootINodeId, start_after, -1, &sub_inodes, &has_more);
  for (size_t i = 0; i < sub_inodes.size(); i++) {
    meta_store_->TestOnlyDeleteINode(sub_inodes[i]);
  }
  meta_store_->GetSubINodes(
      kRootINodeId, start_after, -1, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 0);
  ASSERT_EQ(has_more, false);

  std::string a = "aaaa_file";
  std::string b = "bbbbb_file";

  uint64_t id_a = 1001;
  uint64_t id_b = 1002;

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  auto inode_a = std::make_shared<INode>();
  auto inode_b = std::make_shared<INode>();
  MakeINodeFile(id_a, kRootINodeId, a, p, inode_a.get());
  MakeINodeFile(id_b, kRootINodeId, b, p, inode_b.get());
  meta_store_->InsertINode(inode_a);
  meta_store_->InsertINode(inode_b);

  meta_store_->GetSubINodes(
      kRootINodeId, start_after, -1, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 2);
  ASSERT_EQ(has_more, false);
  for (size_t i = 0; i < sub_inodes.size(); i++) {
    LOG(INFO) << "-------" << i;
    LOG(INFO) << "id:" << sub_inodes[i].id();
    LOG(INFO) << "name:" << sub_inodes[i].name();
    LOG(INFO) << "parent_id:" << sub_inodes[i].parent_id();
    LOG(INFO) << "type:" << sub_inodes[i].type();
    LOG(INFO) << "user:" << sub_inodes[i].permission().username();
    LOG(INFO) << "group:" << sub_inodes[i].permission().groupname();
    LOG(INFO) << "mask:" << sub_inodes[i].permission().permission();
    LOG(INFO) << "atime:" << sub_inodes[i].atime();
    LOG(INFO) << "mtime:" << sub_inodes[i].mtime();
  }

  std::string c = "cc";
  std::string d = "dd";
  std::string e = "ee";

  uint64_t id_c = 1003;
  uint64_t id_d = 1004;
  uint64_t id_e = 1005;

  auto inode_c = std::make_shared<INode>();
  auto inode_d = std::make_shared<INode>();
  auto inode_e = std::make_shared<INode>();

  MakeINodeFile(id_c, kRootINodeId, c, p, inode_c.get());
  MakeINodeFile(id_d, kRootINodeId, d, p, inode_d.get());
  MakeINodeFile(id_e, kRootINodeId, e, p, inode_e.get());

  meta_store_->InsertINode(inode_c);
  meta_store_->InsertINode(inode_d);
  meta_store_->InsertINode(inode_e);

  meta_store_->GetSubINodes(kRootINodeId, c, 1, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 1);
  ASSERT_EQ(sub_inodes[0].name(), d);
  ASSERT_EQ(has_more, true);

  meta_store_->GetSubINodes(kRootINodeId, b, 2, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 2);
  ASSERT_EQ(sub_inodes[0].name(), c);
  ASSERT_EQ(sub_inodes[1].name(), d);
  ASSERT_EQ(has_more, true);

  meta_store_->GetSubINodes(kRootINodeId, b, 10, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 3);
  ASSERT_EQ(has_more, false);

  meta_store_->GetSubINodes(kRootINodeId, c, -1, &sub_inodes, &has_more);
  ASSERT_EQ(sub_inodes.size(), 2);
  ASSERT_EQ(has_more, false);
}

// GetINode need to scan all parent dir files
TEST_F(MetaStorageTest, GetINode) {
  PermissionStatus p;
  auto inode_a = std::make_shared<INode>();
  MakeINode(
      kLastReservedINodeId + 1, kRootINodeId, "a", p, INode::kDirectory, inode_a.get());
  auto inode_b = std::make_shared<INode>();
  MakeINode(kLastReservedINodeId + 2,
            kLastReservedINodeId + 1,
            "b",
            p,
            INode::kDirectory,
            inode_b.get());
  auto inode_c = std::make_shared<INode>();
  MakeINode(
      kLastReservedINodeId + 3, kLastReservedINodeId + 2, "c", p, INode::kFile, inode_c.get());
  meta_store_->InsertINode(inode_a);
  meta_store_->InsertINode(inode_b);
  meta_store_->InsertINode(inode_c);
  ASSERT_EQ(meta_store_->NumINodes(), kNumReservedINode + 3);

  ASSERT_EQ(meta_store_->GetINodeParentId(kLastReservedINodeId + 3), kLastReservedINodeId + 2);

  INode inode;
  ASSERT_EQ(meta_store_->GetINode(kLastReservedINodeId + 1, &inode), kOK);
  ASSERT_EQ(inode.name(), "a");
  ASSERT_EQ(meta_store_->GetINode(kLastReservedINodeId + 4, &inode), kFileNotFound);
  ASSERT_EQ(meta_store_->GetINode(kRootINodeId, &inode), kOK);
  ASSERT_EQ(inode.id(), kRootINodeId);
}

TEST_F(MetaStorageTest, GetINodeMore) {
  int total_file_count = 4;
  for (int i = 1; i <= total_file_count; ++i) {
    std::string file_name = "file" + std::to_string(i);
    std::shared_ptr<INode> inode =
        CreateINode(kRootINodeId, file_name, kLastReservedINodeId + i);
    meta_store_->InsertINode(inode);
  }
  // GetINode
  INode inode;
  for (int i = 1; i <= total_file_count; ++i) {
    ASSERT_EQ(meta_store_->GetINode(kLastReservedINodeId + i, &inode), kOK);
    std::string file_name = "file" + std::to_string(i);
    ASSERT_EQ(meta_store_->GetINode(kRootINodeId, file_name, &inode), kOK);
  }
  for (int i = total_file_count + 1; i <= total_file_count * 2; ++i) {
    ASSERT_EQ(meta_store_->GetINode(kLastReservedINodeId + i, &inode), kFileNotFound);
    std::string file_name = "file" + std::to_string(i);
    ASSERT_EQ(meta_store_->GetINode(kRootINodeId, file_name, &inode),
              kFileNotFound);
  }

  uint64_t parent_id = meta_store_->GetINodeId(kRootINodeId, "file1");
  LOG(INFO) << "parent_id: " << parent_id << " kRootINodeId: " << kRootINodeId;

  // GetSubINodes
  int limit = 3;
  std::string start_after = "";
  int cur_count = 0;
  for (int i = 1; i <= total_file_count; i += limit) {
    std::vector<INode> inodes;
    bool has_more = false;
    meta_store_->GetSubINodes(
        kRootINodeId, start_after, limit, &inodes, &has_more);
    ASSERT_LE(inodes.size(), limit);
    start_after = inodes.back().name();
    cur_count += inodes.size();
  }
  ASSERT_EQ(cur_count, kNumReservedINode + total_file_count);

  // HasSubINodes
  ASSERT_TRUE(meta_store_->HasSubINodes(kRootINodeId));
  ASSERT_FALSE(meta_store_->HasSubINodes(kLastReservedINodeId + 1));
  ASSERT_EQ(meta_store_->NumSubINodes(kRootINodeId),
            kNumReservedINode + total_file_count);
}

TEST_F(MetaStorageTest, OrderedOperations) {
  uint64_t pid = 1000;
  std::string a = "aaaa_dir";
  uint64_t id_a = 1001;

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  auto inode_a = INode();
  MakeINode(id_a, pid, a, p, INode::kDirectory, &inode_a);

  auto stored_a = INode();

  {
    SynchronizedClosure done1;
    // First delete then put
    std::thread t1(
        [&]() { TestOrderedInsertINode(&inode_a, 3, &done1); });
    std::thread t2([&]() {
      TestOrderedInsertINode(&inode_a, 1);
      meta_store_->OrderedDeleteINode(inode_a, 2);
    });
    meta_store_->TxFinish(1, 3);
    done1.Await();
    t1.join();
    t2.join();

    ASSERT_TRUE(meta_store_->GetINode(pid, a, &stored_a) == kOK);

    ASSERT_EQ(stored_a.id(), id_a);
    ASSERT_EQ(stored_a.parent_id(), pid);
    ASSERT_EQ(stored_a.name(), a);
    ASSERT_EQ(stored_a.type(), INode::kDirectory);
    INodeID cur_last_inode_id = LoadLastInodeId();
    ASSERT_EQ(cur_last_inode_id, 1001);
  }

  {
    stored_a.Clear();

    // First delete then put and delete
    SynchronizedClosure done1;
    meta_store_->OrderedDeleteINode(inode_a, 4, &done1);
    meta_store_->TxFinish(4, 1);
    done1.Await();

    ASSERT_FALSE(meta_store_->GetINode(pid, a, &stored_a) == kOK);

    SynchronizedClosure done2;
    meta_store_->OrderedDeleteINode(inode_a, 5);

    TestOrderedInsertINode(&inode_a, 6, &done2);
    meta_store_->TxFinish(5, 2);

    done2.Await();
    stored_a.Clear();

    ASSERT_TRUE(meta_store_->GetINode(pid, a, &stored_a) == kOK);

    ASSERT_EQ(stored_a.id(), id_a);
    ASSERT_EQ(stored_a.parent_id(), pid);
    ASSERT_EQ(stored_a.name(), a);
    ASSERT_EQ(stored_a.type(), INode::kDirectory);
    INodeID cur_last_inode_id = LoadLastInodeId();
    ASSERT_EQ(cur_last_inode_id, 1001);
  }
}

TEST_F(MetaStorageTest, ScanPendingDeleteCF) {
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  uint64_t pid_a = 1000;
  std::string a = "dir1";
  uint64_t id_a = 1001;
  auto inode_a = std::make_shared<INode>();
  MakeINode(id_a, pid_a, a, p, INode::kDirectory, inode_a.get());
  meta_store_->InsertINode(inode_a);

  uint64_t pid_b = 1001;
  std::string b = "child_of_dir1";
  uint64_t id_b = 1002;
  auto inode_b = std::make_shared<INode>();
  MakeINode(id_b, pid_b, b, p, INode::kDirectory, inode_b.get());
  meta_store_->InsertINode(inode_b);

  uint64_t pid_c = 1002;
  std::string c = "child_of_dir2";
  uint64_t id_c = 1003;
  auto inode_c = std::make_shared<INode>();
  MakeINode(id_c, pid_c, c, p, INode::kFile, inode_c.get());
  meta_store_->InsertINode(inode_c);

  {
    SynchronizedClosure done;
    meta_store_->DeletePDINodesAsync({*inode_c}, &done);
    done.Await();
    meta_store_->TestOnlyDeleteINode(*inode_a);
    int num_should_bg_delete = 0;
    INode t;
    meta_store_->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      num_should_bg_delete++;
      t = inode;
      return true;
    });
    ASSERT_EQ(id_a, t.id());
    ASSERT_EQ(pid_a, t.parent_id());
    ASSERT_EQ(INode::kDirectory, t.type());
    ASSERT_EQ(1, num_should_bg_delete);
  }

  {
    SynchronizedClosure done;
    meta_store_->DeletePDINodesAsync({*inode_a}, &done);
    done.Await();
    int num_should_bg_delete = 0;
    meta_store_->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      num_should_bg_delete++;
      return true;
    });
    ASSERT_EQ(0, num_should_bg_delete);
  }
}

TEST_F(MetaStorageTest, PerfPutFile) {
  uint64_t pid = 1000;
  std::string a = "aaaa_file";

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  // 10000 files 212ms in MacOS
  auto start = std::chrono::high_resolution_clock::now();
  int total_file_count = 10000;
  for (int i = 1; i <= total_file_count; ++i) {
    std::string file_name = a + std::to_string(i);
    uint64_t id_file = pid + i;
    auto inode_a = std::make_shared<INode>();
    MakeINodeFile(id_file, pid, file_name, p, inode_a.get());
    meta_store_->InsertINode(inode_a);
  }
  auto end = std::chrono::high_resolution_clock::now();
  auto duration =
      std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
          .count();
  LOG(INFO) << "Create " << total_file_count << " files, cost " << duration
            << " ms, avg: " << duration / total_file_count;

  start = std::chrono::high_resolution_clock::now();
  for (int i = 1; i <= total_file_count; ++i) {
    std::string file_name = a + std::to_string(i);
    auto stored_a = INode();
    ASSERT_TRUE(meta_store_->GetINode(pid, file_name, &stored_a) == kOK);
    meta_store_->TestOnlyDeleteINode(stored_a);
    ASSERT_FALSE(meta_store_->GetINode(pid, file_name, &stored_a) == kOK);
  }
  end = std::chrono::high_resolution_clock::now();
  duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
                 .count();
  LOG(INFO) << "Delete " << total_file_count << " files, cost " << duration
            << " ms, avg: " << duration / total_file_count;

  LOG(INFO) << "Success in Perf put file";
}

TEST_F(MetaStorageTest, PerfOrderedPutFile) {
  uint64_t pid = 1000;
  std::string a = "aaaa_file";

  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());

  // 10000 files 345ms in MacOS
  auto start = std::chrono::high_resolution_clock::now();
  int total_file_count = 10000;
  // simulate the out-of-order, extreme case
  SynchronizedClosure done3;
  std::thread t1([&]() {
    for (int i = total_file_count / 2; i <= total_file_count; ++i) {
      std::string file_name = a + std::to_string(i);
      uint64_t id_file = pid + i;
      auto inode_a = std::make_shared<INode>();
      MakeINodeFile(id_file, pid, file_name, p, inode_a.get());
      if (i == total_file_count) {
        TestOrderedInsertINode(inode_a.get(), i, &done3);
      } else {
        TestOrderedInsertINode(inode_a.get(), i);
      }
    }
  });
  std::thread t2([&]() {
    for (int i = 1; i < total_file_count / 2; ++i) {
      std::string file_name = a + std::to_string(i);
      uint64_t id_file = pid + i;
      auto inode_a = std::make_shared<INode>();
      MakeINodeFile(id_file, pid, file_name, p, inode_a.get());
      TestOrderedInsertINode(inode_a.get(), i);
    }
  });
  meta_store_->TxFinish(1, total_file_count);
  t1.join();
  t2.join();
  done3.Await();

  auto end = std::chrono::high_resolution_clock::now();
  auto duration =
      std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
          .count();
  LOG(INFO) << "Orderedput " << total_file_count << " files, cost " << duration
            << " ms, avg: " << duration / total_file_count;
}

TEST_F(MetaStorageTest, MigrateBlockInfoFromV1ToV2DuringStartUp) {
  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kUnderConstruction);
  bip1.set_block_id(1);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(1024);
  bip1.set_inode_id(kLastReservedINodeId + 1);
  bip1.set_expected_rep(2);
  meta_store_->TestOnlyPutLegacyBlockInfoProto(bip1);

  BlockInfoProto bip2;
  bip2.set_state(BlockInfoProto::kComplete);
  bip2.set_block_id(2);
  bip2.set_gen_stamp(1002);
  bip2.set_num_bytes(2048);
  bip2.set_inode_id(kLastReservedINodeId + 2);
  bip2.set_expected_rep(1);
  meta_store_->TestOnlyPutLegacyBlockInfoProto(bip2);

  BlockPufsInfo info2;
  info2.block_pool_id_ = "bp-1";
  info2.block_id_ = 2;
  info2.gen_stamp_ = 1002;
  info2.num_bytes_ = 2048;
  info2.inode_id_ = kLastReservedINodeId + 2;
  info2.state_ = BlockPufsState::kUploadIssued;
  info2.pufs_name_ = "bp-1:2";
  info2.upload_issued_times_ = 1;
  info2.dn_uuid_ = "dn-1";
  info2.upload_id_ = "upload-1";
  meta_store_->TestOnlyPutLegacyBlockPufsInfo(info2);

  BlockPufsInfo info3;
  info3.block_pool_id_ = "bp-1";
  info3.block_id_ = 3;
  info3.gen_stamp_ = 1003;
  info3.num_bytes_ = 4096;
  info3.inode_id_ = kLastReservedINodeId + 3;
  info3.state_ = BlockPufsState::kPersisted;
  info3.pufs_name_ = "bp-1:3";
  info3.upload_issued_times_ = 1;
  info3.dn_uuid_ = "dn-1";
  info3.upload_id_ = "upload-1";
  meta_store_->TestOnlyPutLegacyBlockPufsInfo(info3);

  BlockPufsInfo info4;
  info4.block_pool_id_ = "bp-1";
  info4.block_id_ = 4;
  info4.gen_stamp_ = 1004;
  info4.num_bytes_ = 8192;
  info4.inode_id_ = kLastReservedINodeId + 4;
  info4.state_ = BlockPufsState::kDeprecated;
  meta_store_->TestOnlyPutLegacyBlockPufsInfo(info4);
  meta_store_->TestOnlyPutLegacyDeprecatedBlockPufsInfo(4);

  BlockPufsInfo info5;
  info5.block_pool_id_ = "bp-1";
  info5.block_id_ = 5;
  info5.gen_stamp_ = 1005;
  info5.num_bytes_ = 16384;
  info5.inode_id_ = kLastReservedINodeId + 5;
  // Upload and delete conflict.
  info5.state_ = BlockPufsState::kPersisted;
  meta_store_->TestOnlyPutLegacyBlockPufsInfo(info5);
  meta_store_->TestOnlyPutLegacyDeprecatedBlockPufsInfo(5);

  meta_store_->MigrateBlockInfoFromV1ToV2DuringStartUp();
  std::string version;
  EXPECT_TRUE(meta_store_->GetNameSystemInfo(kBlockInfoVersion, &version));
  EXPECT_EQ(version, kBlockInfoVersionV2);

  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip1.block_id(), 1);
  EXPECT_EQ(bip1.gen_stamp(), 1001);
  EXPECT_EQ(bip1.num_bytes(), 1024);
  EXPECT_EQ(bip1.inode_id(), kLastReservedINodeId + 1);
  EXPECT_EQ(bip1.expected_rep(), 2);
  EXPECT_TRUE(meta_store_->TestOnlyIsLocalBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(1));

  bip2.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(2, &bip2));
  EXPECT_EQ(bip2.block_id(), 2);
  EXPECT_EQ(bip2.gen_stamp(), 1002);
  EXPECT_EQ(bip2.num_bytes(), 2048);
  EXPECT_EQ(bip2.inode_id(), kLastReservedINodeId + 2);
  EXPECT_EQ(bip2.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(bip2.pufs_name(), "bp-1:2");
  EXPECT_EQ(bip2.upload_issued_times(), 1);
  EXPECT_EQ(bip2.dn_uuid(), "dn-1");
  EXPECT_EQ(bip2.curr_upload_id(), "upload-1");
  EXPECT_TRUE(meta_store_->TestOnlyIsLocalBlockExisted(2));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(2));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(2));

  BlockInfoProto bip3;
  EXPECT_TRUE(meta_store_->GetBlockInfo(3, &bip3));
  EXPECT_EQ(bip3.block_id(), 3);
  EXPECT_EQ(bip3.gen_stamp(), 1003);
  EXPECT_EQ(bip3.num_bytes(), 4096);
  EXPECT_EQ(bip3.inode_id(), kLastReservedINodeId + 3);
  EXPECT_EQ(bip3.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(bip3.pufs_name(), "bp-1:3");
  EXPECT_EQ(bip3.upload_issued_times(), 1);
  EXPECT_EQ(bip3.dn_uuid(), "dn-1");
  EXPECT_EQ(bip3.curr_upload_id(), "upload-1");
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(3));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(3));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(3));

  BlockInfoProto bip4;
  EXPECT_TRUE(meta_store_->GetBlockInfo(4, &bip4));
  EXPECT_EQ(bip4.block_id(), 4);
  EXPECT_EQ(bip4.gen_stamp(), 1004);
  EXPECT_EQ(bip4.num_bytes(), 8192);
  EXPECT_EQ(bip4.inode_id(), kLastReservedINodeId + 4);
  EXPECT_EQ(bip4.state(), BlockInfoProto::kDeprecated);
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(4));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(4));
  EXPECT_TRUE(meta_store_->TestOnlyIsDeprecatedBlockExisted(4));

  BlockInfoProto bip5;
  EXPECT_TRUE(meta_store_->GetBlockInfo(5, &bip5));
  EXPECT_EQ(bip5.block_id(), 5);
  EXPECT_EQ(bip5.gen_stamp(), 1005);
  EXPECT_EQ(bip5.num_bytes(), 16384);
  EXPECT_EQ(bip5.inode_id(), kLastReservedINodeId + 5);
  EXPECT_EQ(bip5.state(), BlockInfoProto::kPersisted);
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(5));
  EXPECT_TRUE(meta_store_->TestOnlyIsDeprecatingBlockExisted(5));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(5));
}

TEST_F(MetaStorageTest, BlockInfoOps) {
  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kUnderConstruction);
  bip1.set_block_id(1);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(1024);
  bip1.set_inode_id(kLastReservedINodeId + 1);
  bip1.set_expected_rep(2);
  SynchronizedClosure done1;
  meta_store_->PutBlockInfo(bip1, nullptr, 1, &done1);
  meta_store_->TxFinish(1, 1);
  done1.Await();
  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip1.block_id(), 1);
  EXPECT_EQ(bip1.gen_stamp(), 1001);
  EXPECT_EQ(bip1.num_bytes(), 1024);
  EXPECT_EQ(bip1.inode_id(), kLastReservedINodeId + 1);
  EXPECT_EQ(bip1.expected_rep(), 2);
  EXPECT_TRUE(meta_store_->TestOnlyIsLocalBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(1));

  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);

  SynchronizedClosure done2;
  bip1.set_state(BlockInfoProto::kPersisted);
  meta_store_->MoveBlockInfoToPersisted(bip1, nullptr, 2, &done2);
  meta_store_->TxFinish(2, 1);
  done2.Await();
  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(bip1.block_id(), 1);
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(1));

  bip1.set_state(BlockInfoProto::kDeprecated);
  meta_store_->MoveBlockInfoToDeprecated(bip1);
  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(bip1.block_id(), 1);
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  EXPECT_TRUE(meta_store_->TestOnlyIsDeprecatedBlockExisted(1));

  bips.clear();
  meta_store_->ScanDeprecatedBlocks(&bips);
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(bips[0].block_id(), 1);

  SynchronizedClosure done3;
  meta_store_->DeleteBlockInfo(1, 3, &done3);
  meta_store_->TxFinish(3, 1);
  done3.Await();
  EXPECT_FALSE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_FALSE(meta_store_->TestOnlyIsLocalBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatedBlockExisted(1));
}

TEST_F(MetaStorageTest, OrderedUpdateINodeAndBlockInfos) {
  // 1. Add local block index and block info proto for the last one block.
  INode inode;
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  cloudfs::BlockProto& bp1 = *inode.add_blocks();
  bp1.set_blockid(1);
  bp1.set_genstamp(1000);
  bp1.set_numbytes(1024);
  SynchronizedClosure done1;
  meta_store_->OrderedUpdateINodeAndAddBlock(inode, 1, &done1);
  done1.Await();
  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].gen_stamp(), 1000);
  EXPECT_EQ(bips[0].num_bytes(), 1024);
  EXPECT_EQ(bips[0].inode_id(), kLastReservedINodeId + 1);
  // 2. Change block info proto for the penultimate block.
  cloudfs::BlockProto& bp2 = *inode.add_blocks();
  bp2.set_blockid(2);
  bp2.set_genstamp(1001);
  bp2.set_numbytes(2048);
  SynchronizedClosure done2;
  meta_store_->OrderedUpdateINodeAndAddBlock(inode, 2, &done2);
  done2.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  std::sort(bips.begin(),
            bips.end(),
            [](const BlockInfoProto& lhs, const BlockInfoProto& rhs) {
              return lhs.block_id() < rhs.block_id();
            });
  EXPECT_EQ(bips.size(), 2);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bips[1].block_id(), 2);
  EXPECT_EQ(bips[1].state(), BlockInfoProto::kUnderConstruction);
  // 3. Avoid changing penultimate block if its state is kUploadIssued or
  //    kPersisted.
  BlockInfoProto bip1 = bips[0];
  bip1.set_state(BlockInfoProto::kUploadIssued);
  meta_store_->TestOnlyPutBlockInfo(bip1);
  SynchronizedClosure done3;
  meta_store_->OrderedUpdateINodeAndAddBlock(inode, 3, &done3);
  done3.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 2);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUploadIssued);
  // 4. Put block which block id is origin_last_blk_to_del to deprecating block.
  inode.mutable_blocks()->RemoveLast();
  SynchronizedClosure done4;
  meta_store_->OrderedUpdateINodeAndFinalizeBlocks(inode, 2, 4, &done4);
  done4.Await();
  std::vector<BlockID> blk_ids;
  meta_store_->ScanDeprecatingBlocks(&blk_ids);
  EXPECT_EQ(blk_ids.size(), 1);
  EXPECT_EQ(blk_ids[0], 2);
  // 5. Avoid committing last block if its state is kUploadIssued or kPersisted.
  SynchronizedClosure done5;
  meta_store_->OrderedUpdateINodeAndFinalizeBlocks(
      inode, kInvalidBlockID, 5, &done5);
  done5.Await();
  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.block_id(), 1);
  EXPECT_EQ(bip1.state(), BlockInfoProto::kUploadIssued);
}

TEST_F(MetaStorageTest, OrderedAddBlock) {
  // 1. Add local block index and block info proto for the last one block.
  INodeInPath iip;
  INode& inode = iip.MutableInodeUnsafe();
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  cloudfs::BlockProto& bp1 = *inode.add_blocks();
  bp1.set_blockid(1);
  bp1.set_genstamp(1000);
  bp1.set_numbytes(1024);
  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kUnderConstruction);
  bip1.set_block_id(1);
  bip1.set_gen_stamp(1000);
  bip1.set_num_bytes(1024);
  bip1.set_inode_id(kLastReservedINodeId + 1);
  bip1.set_expected_rep(2);
  SynchronizedClosure done1;
  meta_store_->OrderedAddBlock(
      &iip.MutableInode(), nullptr, bip1, nullptr, snaplog_, 1, &done1);
  done1.Await();
  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].gen_stamp(), 1000);
  EXPECT_EQ(bips[0].num_bytes(), 1024);
  EXPECT_EQ(bips[0].inode_id(), kLastReservedINodeId + 1);
  INode inode_from_ms;
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode.blocks_size(), 1);
  // 2. Change block info proto for the penultimate block.
  bip1.set_state(BlockInfoProto::kComplete);
  cloudfs::BlockProto& bp2 = *inode.add_blocks();
  bp2.set_blockid(2);
  bp2.set_genstamp(1001);
  bp2.set_numbytes(2048);
  BlockInfoProto bip2;
  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(2);
  bip2.set_gen_stamp(1001);
  bip2.set_num_bytes(2048);
  bip2.set_inode_id(kLastReservedINodeId + 1);
  bip2.set_expected_rep(2);
  SynchronizedClosure done2;
  meta_store_->OrderedAddBlock(
      &iip.MutableInode(), &bip1, bip2, nullptr, snaplog_, 2, &done2);
  done2.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  std::sort(bips.begin(),
            bips.end(),
            [](const BlockInfoProto& lhs, const BlockInfoProto& rhs) {
              return lhs.block_id() < rhs.block_id();
            });
  EXPECT_EQ(bips.size(), 2);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bips[1].block_id(), 2);
  EXPECT_EQ(bips[1].state(), BlockInfoProto::kUnderConstruction);
  inode_from_ms.Clear();
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode.blocks_size(), 2);
  // 3. Delete local block if the penultimate block is persisted.
  bip1.set_state(BlockInfoProto::kPersisted);
  SynchronizedClosure done3;
  meta_store_->OrderedAddBlock(
      &iip.MutableInode(), &bip1, bip2, nullptr, snaplog_, 3, &done3);
  done3.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  std::sort(bips.begin(),
            bips.end(),
            [](const BlockInfoProto& lhs, const BlockInfoProto& rhs) {
              return lhs.block_id() < rhs.block_id();
            });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].block_id(), 2);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  bip1.Clear();
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip1));
  EXPECT_EQ(bip1.state(), BlockInfoProto::kPersisted);
  inode_from_ms.Clear();
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode.blocks_size(), 2);
}

TEST_F(MetaStorageTest, OrderedAbandonBlock) {
  // 1. Abandon block succeed.
  INode inode;
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  SynchronizedClosure done1;
  meta_store_->OrderedAbandonBlock(inode, 1, nullptr, 1, &done1);
  done1.Await();
  EXPECT_TRUE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  INode inode_from_ms;
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode.SerializeAsString(), inode_from_ms.SerializeAsString());
  // 2. Abandon using block failed.
  auto b = inode.add_blocks();
  b->set_blockid(3);
  b->set_genstamp(1000);
  b->set_numbytes(1024);
  SynchronizedClosure done2;
  meta_store_->OrderedAbandonBlock(inode, 3, nullptr, 2, &done2);
  done2.Await();
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(3));
  inode_from_ms.Clear();
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode_from_ms.SerializeAsString(), inode.SerializeAsString());
}

TEST_F(MetaStorageTest, OrderedUpdatePipeline) {
  INode inode;
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  auto b = inode.add_blocks();
  b->set_blockid(1);
  b->set_genstamp(1000);
  b->set_numbytes(1024);
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(1);
  bip.set_gen_stamp(1000);
  bip.set_num_bytes(1024);
  bip.set_inode_id(kLastReservedINodeId + 1);
  bip.set_expected_rep(2);
  SynchronizedClosure done;
  meta_store_->OrderedUpdatePipeline(inode, bip, nullptr, 1, &done);
  done.Await();
  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].gen_stamp(), 1000);
  EXPECT_EQ(bips[0].num_bytes(), 1024);
  EXPECT_EQ(bips[0].inode_id(), kLastReservedINodeId + 1);
  INode inode_from_ms;
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode_from_ms.SerializeAsString(), inode.SerializeAsString());
}

TEST_F(MetaStorageTest, OrderedFsync) {
  INodeInPath iip;
  INode& inode = iip.MutableInodeUnsafe();
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  auto b = inode.add_blocks();
  b->set_blockid(1);
  b->set_genstamp(1000);
  b->set_numbytes(1024);
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(1);
  bip.set_gen_stamp(1000);
  bip.set_num_bytes(1024);
  bip.set_inode_id(kLastReservedINodeId + 1);
  bip.set_expected_rep(2);
  SynchronizedClosure done;
  meta_store_->OrderedFsync(&iip.MutableInode(), &bip, nullptr, snaplog_, 1, &done);
  done.Await();
  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);
  EXPECT_EQ(bips[0].gen_stamp(), 1000);
  EXPECT_EQ(bips[0].num_bytes(), 1024);
  EXPECT_EQ(bips[0].inode_id(), kLastReservedINodeId + 1);
  INode inode_from_ms;
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode_from_ms.SerializeAsString(), inode.SerializeAsString());
}

TEST_F(MetaStorageTest, OrderedCloseFile) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(1);
  bip.set_gen_stamp(1000);
  bip.set_num_bytes(1024);
  bip.set_inode_id(kLastReservedINodeId + 1);
  bip.set_expected_rep(2);
  meta_store_->TestOnlyPutBlockInfo(bip, false);
  std::vector<BlockInfoProto> bips;
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bips[0].block_id(), 1);

  // 1. Committed last block will not delete local block index.
  INodeInPath iip;
  INode& inode = iip.MutableInodeUnsafe();
  MakeINode(kLastReservedINodeId + 1,
            kRootINodeId,
            "ls",
            PermissionStatus(),
            INode::kFile,
            &inode);
  auto b = inode.add_blocks();
  b->set_blockid(1);
  b->set_genstamp(1000);
  b->set_numbytes(1024);
  bip.set_state(BlockInfoProto::kComplete);
  SynchronizedClosure done1;
  meta_store_->OrderedCloseFile(&iip.MutableInode(),
                                &bip,
                                kInvalidBlockID,
                                nullptr,
                                snaplog_,
                                1,
                                &done1);
  done1.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 1);
  EXPECT_EQ(bips[0].state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bips[0].block_id(), 1);
  INode inode_from_ms;
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode_from_ms.SerializeAsString(), inode.SerializeAsString());
  // 2. Persisted last block will delete local block index.
  bip.set_state(BlockInfoProto::kPersisted);
  SynchronizedClosure done2;
  meta_store_->OrderedCloseFile(&iip.MutableInode(),
                                &bip,
                                kInvalidBlockID,
                                nullptr,
                                snaplog_,
                                2,
                                &done2);
  done2.Await();
  bips.clear();
  meta_store_->ScanLocalBlocks(
      [&bips](const BlockInfoProto& bip) { bips.push_back(bip); });
  EXPECT_EQ(bips.size(), 0);
  BlockInfoProto bip_from_ms;
  EXPECT_TRUE(meta_store_->GetBlockInfo(1, &bip_from_ms));
  EXPECT_EQ(bip_from_ms.SerializeAsString(), bip.SerializeAsString());
  inode_from_ms.Clear();
  EXPECT_EQ(meta_store_->GetINode(kRootINodeId, "ls", &inode_from_ms),
            StatusCode::kOK);
  EXPECT_EQ(inode_from_ms.SerializeAsString(), inode.SerializeAsString());
  // 3. Abandon using block failed.
  SynchronizedClosure done3;
  meta_store_->OrderedCloseFile(&iip.MutableInode(),
                                &bip,
                                1,
                                nullptr,
                                snaplog_,
                                3,
                                &done3);
  done3.Await();
  EXPECT_FALSE(meta_store_->TestOnlyIsDeprecatingBlockExisted(1));
  // 4. Abandon block succeed.
  SynchronizedClosure done4;
  meta_store_->OrderedCloseFile(&iip.MutableInode(),
                                &bip,
                                2,
                                nullptr,
                                snaplog_,
                                4,
                                &done4);
  done4.Await();
  EXPECT_TRUE(meta_store_->TestOnlyIsDeprecatingBlockExisted(2));
}

class MetaStorageTestV2 : public testing::Test {
 public:
  void SetUp() override {
    gmock_meta_storage_writer_ =
        new testing::StrictMock<meta_storage::GMockWriter>();
    meta_storage_.SetWriter(
        std::shared_ptr<meta_storage::Writer>(gmock_meta_storage_writer_));
  }

  void TearDown() override {
  }

 protected:
  testing::StrictMock<meta_storage::GMockWriter>* gmock_meta_storage_writer_;
  GMockMetaStorage meta_storage_;
};

class MetaStorageFlushBlockInfoProtosTest : public MetaStorageTestV2 {};

TEST_F(MetaStorageFlushBlockInfoProtosTest, FlushLocalBlockInfoProtos) {
  auto wb = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(meta_storage_, CreateWriteBatch())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb,
              Put(kLocalBlockCFHandle,
                  testing::AnyOf(EncodeBlockID(1076015580),
                                 EncodeBlockID(1076015581),
                                 EncodeBlockID(1076015582),
                                 EncodeBlockID(1076015583),
                                 EncodeBlockID(1076015584)),
                  rocksdb::Slice()))
      .Times(5)
      .WillRepeatedly(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*wb,
              Put(kBlockInfoProtoCFHandle,
                  testing::AnyOf(EncodeBlockID(1076015580),
                                 EncodeBlockID(1076015581),
                                 EncodeBlockID(1076015582),
                                 EncodeBlockID(1076015583),
                                 EncodeBlockID(1076015584)),
                  testing::_))
      .Times(5)
      .WillRepeatedly(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(testing::_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  SynchronizedRpcClosure done;
  meta_storage_.MetaStorage::FlushBlockInfoProtos(
      BlockInfoProtosBuilder()
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kUnderConstruction)
                          .SetBlockId(1076015580)
                          .SetGenStamp(1001)
                          .SetNumBytes(1024)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kUnderConstruction)
                          .SetBlockId(1076015581)
                          .SetGenStamp(1002)
                          .SetNumBytes(1025)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kCommitted)
                          .SetBlockId(1076015582)
                          .SetGenStamp(1003)
                          .SetNumBytes(1026)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kComplete)
                          .SetBlockId(1076015583)
                          .SetGenStamp(1004)
                          .SetNumBytes(1027)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kUploadIssued)
                          .SetBlockId(1076015584)
                          .SetGenStamp(1005)
                          .SetNumBytes(1028)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .Build(),
      1,
      &done);
  done.Await();
}

TEST_F(MetaStorageFlushBlockInfoProtosTest, FlushPersistedBlockInfoProto) {
  auto wb = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(meta_storage_, CreateWriteBatch())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Delete(kLocalBlockCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(
      *wb, Put(kBlockInfoProtoCFHandle, EncodeBlockID(1076015580), testing::_))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(testing::_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  SynchronizedRpcClosure done;
  meta_storage_.MetaStorage::FlushBlockInfoProtos(
      BlockInfoProtosBuilder()
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kPersisted)
                          .SetBlockId(1076015580)
                          .SetGenStamp(1001)
                          .SetNumBytes(1024)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .Build(),
      1,
      &done);
  done.Await();
}

TEST_F(MetaStorageFlushBlockInfoProtosTest, FlushDeprecatedBlockInfoProto) {
  auto wb = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(meta_storage_, CreateWriteBatch())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Delete(kLocalBlockCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Delete(kDeprecatingBlockCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*wb,
              Put(kDeprecatedBlockCFHandle,
                  EncodeBlockID(1076015580),
                  rocksdb::Slice()))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(
      *wb, Put(kBlockInfoProtoCFHandle, EncodeBlockID(1076015580), testing::_))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(testing::_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  SynchronizedRpcClosure done;
  meta_storage_.MetaStorage::FlushBlockInfoProtos(
      BlockInfoProtosBuilder()
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kDeprecated)
                          .SetBlockId(1076015580)
                          .SetGenStamp(1001)
                          .SetNumBytes(1024)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .Build(),
      1,
      &done);
  done.Await();
}

TEST_F(MetaStorageFlushBlockInfoProtosTest, FlushDeletedBlockInfoProto) {
  auto wb = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(meta_storage_, CreateWriteBatch())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Delete(kLocalBlockCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Delete(kDeprecatedBlockCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Delete(kBlockInfoProtoCFHandle, EncodeBlockID(1076015580)))
      .Times(1)
      .WillOnce(testing::Return(rocksdb::Status()));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(testing::_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  SynchronizedRpcClosure done;
  meta_storage_.MetaStorage::FlushBlockInfoProtos(
      BlockInfoProtosBuilder()
          .AddContent(BlockInfoProtoBuilder()
                          .SetState(BlockInfoProto::kDeleted)
                          .SetBlockId(1076015580)
                          .SetGenStamp(1001)
                          .SetNumBytes(1024)
                          .SetINodeId(17327)
                          .SetExpectedRep(3)
                          .Build())
          .Build(),
      1,
      &done);
  done.Await();
}

TEST_F(MetaStorageTest, ReportStorageClass) {
  FLAGS_dfs_meta_storage_rocksdb_perf_scr_enabled = true;
  auto scr_batch_size_old_val = FLAGS_dfs_meta_storage_update_scr_batch_size;
  FLAGS_dfs_meta_storage_update_scr_batch_size = 1;
  DEFER([&]() { FLAGS_dfs_meta_storage_update_scr_batch_size = scr_batch_size_old_val; });

  const int nblock = 100000;
  const int nop = 100000;
  const int ndn = 1;
  CHECK_GT(nop, FLAGS_dfs_meta_storage_update_scr_batch_size);

  struct stcls_report {
    StorageClassProto dnuuid_classes_mapping[ndn] = { StorageClassProto::NONE };
  } expected_reports_arr[nblock];

  auto tp_cb = [&] () -> bool {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> blkid_dis(0, nblock - 1);
    std::uniform_int_distribution<> dn_dis(0, ndn - 1);
    std::uniform_int_distribution<> cls_dis(cloudfs::StorageClassProto_MIN,
                                            cloudfs::StorageClassProto_MAX);

    std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
    int dnid = dn_dis(gen);
    for (int opi = 0; opi < nop; opi++) {
      int blkid = blkid_dis(gen);
      StorageClassProto cls = static_cast<StorageClassProto>(cls_dis(gen));
      auto& expected_reports = expected_reports_arr[blkid];

      // 1. update meta_storage
      StorageClassReportProto scr;
      scr.set_stcls(cls);
      replica_report.emplace_back(blkid, scr);

      // 2. update local result
      expected_reports.dnuuid_classes_mapping[dnid] = cls;
    }
    auto dnuuid = std::to_string(dnid);
    SynchronizedClosure done;
    meta_store_->UpdateStorageClassReportsAsync(dnuuid, replica_report, &done);
    done.Await();

    return true;
  };
  cnetpp::concurrency::ThreadPool tp("report_storage_class");
  tp.set_num_threads(1);
  tp.Start();
  tp.AddTask(tp_cb);

  meta_store_->WaitNoPending(true);
  tp.Stop(true);

  for (int blkid = 0; blkid < nblock; blkid++) {
    auto& expected_mapping = expected_reports_arr[blkid].dnuuid_classes_mapping;

    std::vector<std::string> dnuuids;
    std::vector<StorageClassReportProto> reports;
    meta_store_->GetStorageClassReports(blkid, &dnuuids, &reports);
    if (reports.empty()) {
      for (int i = 0; i < ndn; i++) {
        ASSERT_EQ(expected_mapping[i], StorageClassProto::NONE);
      }
      continue;
    }

    ASSERT_EQ(dnuuids.size(), reports.size());
    ASSERT_LE(dnuuids.size(), ndn);
    StorageClassProto persisted_mapping[ndn] = { StorageClassProto::NONE };
    for (int i = 0; i < dnuuids.size(); i++) {
      int dnid = std::stoi(dnuuids.at(i));
      ASSERT_NE(reports.at(i).stcls(), StorageClassProto::NONE);
      persisted_mapping[dnid] = reports.at(i).stcls();
    }
    for (int i = 0; i < ndn; i++) {
      if (persisted_mapping[i] == StorageClassProto::NONE) {
        continue;
      }
      ASSERT_EQ(persisted_mapping[i], expected_mapping[i]);
    }
  }
}

TEST_F(MetaStorageTest, ConcurrentReportStorageClass) {
  FLAGS_dfs_meta_storage_rocksdb_perf_scr_enabled = true;

  const int nblock = 16;
  const int nthread = 16;
  const int nop = 100000;
  const int ndn = 3;

  struct stcls_report {
    std::mutex mtx;
    StorageClassProto dnuuid_classes_mapping[ndn] =
        { StorageClassProto::NONE, StorageClassProto::NONE, StorageClassProto::NONE };
  } expected_reports_arr[nblock];

  auto tp_cb = [&] () -> bool {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> blkid_dis(0, nblock - 1);
    std::uniform_int_distribution<> dn_dis(0, ndn - 1);
    std::uniform_int_distribution<> cls_dis(cloudfs::StorageClassProto_MIN,
                                            cloudfs::StorageClassProto_MAX);
    for (int opi = 0; opi < nop; opi++) {
      int blkid = blkid_dis(gen);
      int dnid = dn_dis(gen);
      auto dnuuid = std::to_string(dnid);
      StorageClassProto cls = static_cast<StorageClassProto>(cls_dis(gen));
      auto& expected_reports = expected_reports_arr[blkid];

      std::unique_lock<std::mutex> lock(expected_reports.mtx);

      // 1. update meta_storage
      std::vector<std::pair<BlockID, StorageClassReportProto>> replica_report;
      StorageClassReportProto scr;
      scr.set_stcls(cls);
      replica_report.emplace_back(blkid, scr);
      SynchronizedClosure done;
      meta_store_->UpdateStorageClassReportsAsync(dnuuid,
                                                  replica_report,
                                                  &done);
      done.Await();

      // 2. update local result
      expected_reports.dnuuid_classes_mapping[dnid] = cls;
    }
    return true;
  };
  cnetpp::concurrency::ThreadPool tp("concurrent_report_storage_class");
  tp.set_num_threads(nthread);
  tp.Start();
  for (int i = 0; i < nthread; i++) {
    tp.AddTask(tp_cb);
  }

  meta_store_->WaitNoPending(true);
  tp.Stop(true);

  for (int blkid = 0; blkid < nblock; blkid++) {
    auto& expected_mapping = expected_reports_arr[blkid].dnuuid_classes_mapping;

    std::vector<std::string> dnuuids;
    std::vector<StorageClassReportProto> reports;
    meta_store_->GetStorageClassReports(blkid, &dnuuids, &reports);
    if (reports.empty()) {
      for (int i = 0; i < ndn; i++) {
        ASSERT_EQ(expected_mapping[i], StorageClassProto::NONE);
      }
      continue;
    }

    ASSERT_EQ(dnuuids.size(), reports.size());
    ASSERT_LE(dnuuids.size(), ndn);
    StorageClassProto persisted_mapping[ndn] =
        { StorageClassProto::NONE,
          StorageClassProto::NONE,
          StorageClassProto::NONE };
    for (int i = 0; i < dnuuids.size(); i++) {
      int dnid = std::stoi(dnuuids.at(i));
      ASSERT_NE(reports.at(i).stcls(), StorageClassProto::NONE);
      persisted_mapping[dnid] = reports.at(i).stcls();
    }
    for (int i = 0; i < ndn; i++) {
      if (persisted_mapping[i] == StorageClassProto::NONE) {
        continue;
      }
      ASSERT_EQ(persisted_mapping[i], expected_mapping[i]);
    }
  }
}

TEST_F(MetaStorageTest, GetSubINodesWithUpperBound) {
  std::vector<std::string> inode_names{
      "hello", "你好", "こんにちは", "Hallo", "Привет"};
  for (auto i = 0; i < inode_names.size(); i++) {
    std::shared_ptr<INode> inode = std::make_shared<INode>();
    MakeINode(kLastReservedINodeId + 1 + i,
              kRootINodeId,
              inode_names[i],
              PermissionStatus(),
              INode::kFile,
              inode.get());
    meta_store_->InsertINode(inode);

    std::set<std::string> sub_inode_names;
    meta_store_->GetSubINodesStartAt(
        kRootINodeId,
        "",
        [&](const INode& inode) {
          sub_inode_names.emplace(inode.name());
          return true;
        },
        nullptr);
    EXPECT_EQ(sub_inode_names.size(), i + 1);
    for (const auto& sub_inode_name : sub_inode_names) {
      EXPECT_TRUE(std::find(inode_names.begin(),
                            inode_names.end(),
                            sub_inode_name) != inode_names.end());
    }

    sub_inode_names.clear();
    meta_store_->GetSubINodes(
        kRootINodeId,
        "",
        [&](const INode& inode) {
          sub_inode_names.emplace(inode.name());
          return true;
        },
        nullptr);
    EXPECT_EQ(sub_inode_names.size(), i + 1);
    for (const auto& sub_inode_name : sub_inode_names) {
      EXPECT_TRUE(std::find(inode_names.begin(),
                            inode_names.end(),
                            sub_inode_name) != inode_names.end());
    }
  }
}

TEST(DecodeINodeKey, FailWhenWriteV1INodeKeyButReadV2) {
  MetaStorage meta_store("/data00/dancenn_data/rocksdb_tmp");
  FLAGS_dfs_meta_storage_inode_key_v2 = false;
  std::string key;
  EXPECT_TRUE(meta_store.EncodeStoreKey(17123, "sub", 18112, &key));
  FLAGS_dfs_meta_storage_inode_key_v2 = true;
  INodeID parent_inode_id = kInvalidINodeId;
  std::string name;
  INodeID inode_id = kInvalidINodeId;
  Status s = meta_store.DecodeStoreKeyInternal(
      key, &parent_inode_id, &name, &inode_id);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "The tenth character from the end is not \0");
}

TEST(DecodeINodeKey, FailWhenWriteV2INodeKeyButReadV1) {
  MetaStorage meta_store("/data00/dancenn_data/rocksdb_tmp");
  FLAGS_dfs_meta_storage_inode_key_v2 = true;
  std::string key;
  EXPECT_TRUE(meta_store.EncodeStoreKey(17123, "sub", 18112, &key));
  FLAGS_dfs_meta_storage_inode_key_v2 = false;
  INodeID parent_inode_id = kInvalidINodeId;
  std::string name;
  INodeID inode_id = kInvalidINodeId;
  Status s = meta_store.DecodeStoreKeyInternal(
      key, &parent_inode_id, &name, &inode_id);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "The last character is \0");
}

TEST(DecodeINodeKey, SucceedWhenWriteV1INodeKeyAndReadV1) {
  MetaStorage meta_store("/data00/dancenn_data/rocksdb_tmp");
  FLAGS_dfs_meta_storage_inode_key_v2 = false;
  std::string key;
  EXPECT_TRUE(meta_store.EncodeStoreKey(17123, "sub", 18112, &key));
  INodeID parent_inode_id = kInvalidINodeId;
  std::string name;
  INodeID inode_id = kInvalidINodeId;
  EXPECT_TRUE(
      meta_store.DecodeStoreKeyInternal(key, &parent_inode_id, &name, &inode_id)
          .IsOK());
  EXPECT_EQ(parent_inode_id, 17123);
  EXPECT_EQ(name, "sub");
  EXPECT_EQ(inode_id, 18112);
}

TEST(DecodeINodeKey, SucceedWhenWriteV2INodeKeyAndReadV2) {
  MetaStorage meta_store("/data00/dancenn_data/rocksdb_tmp");
  FLAGS_dfs_meta_storage_inode_key_v2 = true;
  std::string key;
  EXPECT_TRUE(meta_store.EncodeStoreKey(17123, "sub", 18112, &key));
  INodeID parent_inode_id = kInvalidINodeId;
  std::string name;
  INodeID inode_id = kInvalidINodeId;
  EXPECT_TRUE(
      meta_store.DecodeStoreKeyInternal(key, &parent_inode_id, &name, &inode_id)
          .IsOK());
  EXPECT_EQ(parent_inode_id, 17123);
  EXPECT_EQ(name, "sub");
  EXPECT_EQ(inode_id, 18112);
}

TEST(VerifyINode, Test01) {
  uint8_t bytes_1[] = {
      0x08, 0x90, 0xfb, 0x8e, 0x07, 0x10, 0xc6, 0xdb, 0x8d, 0x07, 0x1a, 0x02,
      0x66, 0x32, 0x22, 0x2a, 0x0a, 0x19, 0x6c, 0x75, 0x6a, 0x69, 0x61, 0x6e,
      0x68, 0x75, 0x69, 0x2e, 0x31, 0x40, 0x62, 0x79, 0x74, 0x65, 0x64, 0x61,
      0x6e, 0x63, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x12, 0x0a, 0x73, 0x75, 0x70,
      0x65, 0x72, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xa4, 0x03, 0x28, 0x01,
      0x30, 0xee, 0xc2, 0x88, 0xaf, 0xf7, 0x31, 0x38, 0xc6, 0xc2, 0x88, 0xaf,
      0xf7, 0x31, 0x50, 0x80, 0x80, 0x80, 0x40, 0x58, 0x00, 0x60, 0x03, 0x6a,
      0x0d, 0x08, 0x8d, 0x8f, 0xc6, 0x87, 0x04, 0x10, 0xf5, 0x96, 0xc6, 0x07,
      0x18, 0x02, 0x82, 0x01, 0x30, 0x08, 0x03, 0x12, 0x14, 0x68, 0x64, 0x66,
      0x73, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
      0x2e, 0x68, 0x61, 0x73, 0x68, 0x1a, 0x16, 0x0a, 0x10, 0x50, 0xd4, 0xf7,
      0x8c, 0x83, 0x50, 0x40, 0x90, 0xa0, 0xee, 0xb9, 0x5c, 0x7b, 0xd7, 0x97,
      0x08, 0x10, 0x8f, 0xef, 0x1b, 0x88, 0x01, 0x02, 0xa0, 0x01, 0xa0, 0xce,
      0xc4, 0x2e, 0xa8, 0x01, 0xc6, 0xcd, 0xc4, 0x2e, 0xa2, 0x06, 0x25, 0x08,
      0x02, 0x10, 0x01, 0x18, 0x01, 0x22, 0x00, 0x28, 0x00, 0x30, 0xc9, 0x95,
      0x8c, 0xb2, 0x06, 0x38, 0xc9, 0x95, 0x8c, 0xb2, 0x06, 0x4a, 0x00, 0x5a,
      0x0b, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x69, 0x72, 0x2f, 0x66, 0x32,
      0x8a, 0x02, 0x11, 0x08, 0x00, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0x01, 0x18, 0x00, 0x20, 0x00};
  INode inode_1;
  EXPECT_TRUE(inode_1.ParseFromArray(&bytes_1[0], sizeof(bytes_1)));

  uint8_t bytes_2[] = {
      0x08, 0x90, 0xfb, 0x8e, 0x07, 0x10, 0xc6, 0xdb, 0x8d, 0x07, 0x1a, 0x02,
      0x66, 0x32, 0x22, 0x2a, 0x0a, 0x19, 0x6c, 0x75, 0x6a, 0x69, 0x61, 0x6e,
      0x68, 0x75, 0x69, 0x2e, 0x31, 0x40, 0x62, 0x79, 0x74, 0x65, 0x64, 0x61,
      0x6e, 0x63, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x12, 0x0a, 0x73, 0x75, 0x70,
      0x65, 0x72, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xa4, 0x03, 0x28, 0x01,
      0x30, 0xee, 0xc2, 0x88, 0xaf, 0xf7, 0x31, 0x38, 0xc6, 0xc2, 0x88, 0xaf,
      0xf7, 0x31, 0x50, 0x80, 0x80, 0x80, 0x40, 0x58, 0x00, 0x60, 0x03, 0x6a,
      0x0d, 0x08, 0x8d, 0x8f, 0xc6, 0x87, 0x04, 0x10, 0xf5, 0x96, 0xc6, 0x07,
      0x18, 0x02, 0x82, 0x01, 0x30, 0x08, 0x03, 0x12, 0x14, 0x68, 0x64, 0x66,
      0x73, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
      0x2e, 0x68, 0x61, 0x73, 0x68, 0x1a, 0x16, 0x0a, 0x10, 0x50, 0xd4, 0xf7,
      0x8c, 0x83, 0x50, 0x40, 0x90, 0xa0, 0xee, 0xb9, 0x5c, 0x7b, 0xd7, 0x97,
      0x08, 0x10, 0x8f, 0xef, 0x1b, 0x88, 0x01, 0x02, 0xa0, 0x01, 0xa0, 0xce,
      0xc4, 0x2e, 0xa8, 0x01, 0xc6, 0xcd, 0xc4, 0x2e, 0x8a, 0x02, 0x11, 0x08,
      0x00, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
      0x18, 0x00, 0x20, 0x00, 0xa2, 0x06, 0x25, 0x08, 0x02, 0x10, 0x01, 0x18,
      0x01, 0x22, 0x00, 0x28, 0x00, 0x30, 0xc9, 0x95, 0x8c, 0xb2, 0x06, 0x38,
      0xc9, 0x95, 0x8c, 0xb2, 0x06, 0x4a, 0x00, 0x5a, 0x0b, 0x74, 0x65, 0x73,
      0x74, 0x5f, 0x64, 0x69, 0x72, 0x2f, 0x66, 0x32};
  INode inode_2;
  EXPECT_TRUE(inode_2.ParseFromArray(&bytes_2[0], sizeof(bytes_2)));

  // Two identical inode protobuf objects may produce different byte sequences
  // after serialization due to variations in field order. Therefore, comparing
  // bytes to determine if two inodes are equal is unreliable. However, using
  // `ShortDebugString` for comparison is reliable. For more information, refer
  // to the `verify_kvs` logic in `MetaStorage::BatchWrite`.
  // https://bytedance.larkoffice.com/docx/T4godpKgeoEpHlx7pkPc0PQTnwe
  EXPECT_NE(
      std::memcmp(
          &bytes_1[0], &bytes_2[0], std::min(sizeof(bytes_1), sizeof(bytes_2))),
      0);
  EXPECT_EQ(inode_1.ShortDebugString(), inode_2.ShortDebugString());
}

}  // namespace dancenn
