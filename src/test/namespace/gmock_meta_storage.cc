// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/02/20
// Description

#include "test/namespace/gmock_meta_storage.h"

namespace dancenn {

rocksdb::ColumnFamilyHandle* kINodeDefaultCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(0);
rocksdb::ColumnFamilyHandle* kINodePendingDeleteCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(1);
rocksdb::ColumnFamilyHandle* kNameSystemInfoCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(2);
rocksdb::ColumnFamilyHandle* kAccessCounterCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(3);
rocksdb::ColumnFamilyHandle* kINodeIndexCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(4);
rocksdb::ColumnFamilyHandle* kLegacyBlockPufsInfoCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(5);
rocksdb::ColumnFamilyHandle* kLegacyDeprecatedBlockPufsInfoCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(6);
rocksdb::ColumnFamilyHandle* kLegacyBlockInfoProtoCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(7);
rocksdb::ColumnFamilyHandle* kBlockInfoProtoCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(8);
rocksdb::ColumnFamilyHandle* kLocalBlockCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(9);
rocksdb::ColumnFamilyHandle* kDeprecatingBlockCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(10);
rocksdb::ColumnFamilyHandle* kDeprecatedBlockCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(11);
rocksdb::ColumnFamilyHandle* kINodeStatCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(12);
rocksdb::ColumnFamilyHandle* kLeaseCFHandle =
    reinterpret_cast<rocksdb::ColumnFamilyHandle*>(16);
const uint32_t kMaxCFIndex = 17;

std::string GMockMetaStorage::EncodeINodeID(INodeID inode_id) {
  return MetaStorage::EncodeINodeID(inode_id);
}

std::string GMockMetaStorage::EncodeBlockID(BlockID blk_id) {
  return MetaStorage::EncodeBlockID(blk_id);
}

}  // namespace dancenn
