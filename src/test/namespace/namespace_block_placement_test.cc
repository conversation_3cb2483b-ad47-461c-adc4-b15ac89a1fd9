//
// Copyright (c) 2019 Bytedance Inc. All rights reserved.
// Author: jiangxunyang <<EMAIL>>
//

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <random>

#include "base/file_utils.h"
#include "base/path_util.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/block_placement_nodezone.h"
#include "namespace/create_flag.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(run_ut);

DECLARE_string(all_datacenters);

DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);

DECLARE_int32(nodezone_map_refresh_period_ms);
DECLARE_string(nodezone_map_config_file_path);
DECLARE_int32(dir_to_nodezone_group_refresh_period_ms);
DECLARE_string(dir_to_nodezone_group_config_file_path);

namespace dancenn {

using DNDesc = std::tuple<
    DatanodeID, std::string, std::string, std::string, uint64_t>;
using DNDescList = std::vector<DNDesc>;
using Testbed = std::unordered_map<DatanodeID, DatanodeInfoPtr>;

class NameSpaceWrapper;
using NameSpaceWrapperPtr = std::shared_ptr<NameSpaceWrapper>;

LogRpcInfo default_rpc_info("", 0);

struct SimpleDatanodeInfo {
  std::string uuid;
  std::string ip;
};

struct NameSpaceWrapper {
  std::shared_ptr<MockHAState> ha_state;
  std::shared_ptr<MockSafeMode> safemode;
  std::shared_ptr<MockNameSpace> ns;
  std::shared_ptr<DatanodeManager> datanode_manager;
  std::shared_ptr<BlockManager> block_manager;
  std::atomic<bool> stop;
  std::atomic<bool> pause;
  std::thread heartbeat_thread;
  std::map<std::string, SimpleDatanodeInfo> dn_map;

  void StopHeartbeat() {
    // Wait for heartbeat thread to exit
    stop = true;
    std::this_thread::sleep_for(std::chrono::seconds(2));
    heartbeat_thread.join();
  }
  void GetDiskRequest(
      cloudfs::datanode::HeartbeatRequestProto* request,
      const std::string& uuid,
      const cloudfs::datanode::DatanodeRegistrationProto& reg,
      uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid(uuid);
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
    request->mutable_registration()->CopyFrom(reg);
  }

  void RegisterDN(std::string uuid, std::string ipstr) {
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(uuid);
    cnetpp::base::IPAddress ip(ipstr);

    {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid(uuid);
      reg.mutable_datanodeid()->set_ipaddr(ipstr);
      cnetpp::base::IPAddress ip_addr(ipstr);
      this->datanode_manager->Register(reg.datanodeid(), &reg, ip_addr);
      DatanodeManager::RepeatedCmds cmds;
      HeartbeatRequestProto request;
      this->GetDiskRequest(&request, uuid, reg, 102400);
      this->datanode_manager->Heartbeat(request, &cmds);
    }

    this->dn_map.emplace(ipstr, SimpleDatanodeInfo{uuid, ipstr});
  }

  std::string GetDatanodeUUidFromIp(std::string ipstr) {
    auto itr = dn_map.find(ipstr);
    if (itr == dn_map.end()) {
      return "";
    }
    return itr->second.uuid;
  }

  void Stop() {
    stop = true;
    LOG(INFO) << "Wait for heartbeat thread to quit.";
    if (heartbeat_thread.joinable()) {
      heartbeat_thread.join();
    }
    ns->StopActive();
    ns->Stop();
  }
};

class NameSpaceBlockPlacementTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;
    FLAGS_all_datacenters = "HL,LF";

    FLAGS_lease_expired_soft_limit_ms = 200;
    FLAGS_lease_expired_hard_limit_ms = 400;
    FLAGS_datanode_keep_alive_timeout_sec = 1000;
    FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
    FLAGS_blockmap_num_bucket_each_slice = 1;
    FLAGS_blockmap_num_slice = 1;
    FLAGS_dfs_replication_min = 1;

    FLAGS_nodezone_map_refresh_period_ms = 1 * 1000;
    FLAGS_nodezone_map_config_file_path = "./ittest.namespace_blockplacement.node_zone_map";
    auto nodezone_map_str = R"(
[
{
 "nodePaths":[],
 "remainingRatio":0.9,
 "tag": {"id": "default"},
 "id": "NodeZone0"
}
]
)";
    FileUtils::WriteFile(FLAGS_nodezone_map_config_file_path, nodezone_map_str);

    FLAGS_dir_to_nodezone_group_refresh_period_ms = 1 * 1000;
    FLAGS_dir_to_nodezone_group_config_file_path = "./ittest.namespace_blockplacement.dir_to_node_zone_tag";
    FileUtils::WriteFile(FLAGS_dir_to_nodezone_group_config_file_path , "{}");

    nodezone_map_str_[0] = R"(
[
{
 "nodePaths":[
  "/LF/0d0100/10.13.1.1:5080",
  "/LF/0d0100/10.13.1.2:5080",
  "/LF/0d0100/10.13.1.3:5080",

  "/LF/0d0101/10.13.1.84:5080",
  "/LF/0d0101/10.13.1.85:5080",
  "/LF/0d0101/10.13.1.86:5080",

  "/HL/150100/10.21.1.1:5080",
  "/HL/150100/10.21.1.2:5080",
  "/HL/150100/10.21.1.3:5080",

  "/HL/150101/10.21.1.84:5080",
  "/HL/150101/10.21.1.85:5080",
  "/HL/150101/10.21.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "default"},
 "id": "NodeZone0"
},
{
 "nodePaths":[
  "/LF/0e0100/10.14.1.1:5080",
  "/LF/0e0100/10.14.1.2:5080",
  "/LF/0e0100/10.14.1.3:5080",

  "/LF/0e0101/10.14.1.84:5080",
  "/LF/0e0101/10.14.1.85:5080",
  "/LF/0e0101/10.14.1.86:5080",

  "/HL/160100/10.22.1.1:5080",
  "/HL/160100/10.22.1.2:5080",
  "/HL/160100/10.22.1.3:5080",

  "/HL/160101/10.22.1.84:5080",
  "/HL/160101/10.22.1.85:5080",
  "/HL/160101/10.22.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testz"},
 "id": "NodeZone1"
},
{
 "nodePaths":[
  "/LF/0f0100/*********:5080",
  "/LF/0f0100/*********:5080",
  "/LF/0f0100/10.15.1.3:5080",

  "/LF/0f0101/10.15.1.84:5080",
  "/LF/0f0101/10.15.1.85:5080",
  "/LF/0f0101/10.15.1.86:5080",

  "/HL/170100/*********:5080",
  "/HL/170100/*********:5080",
  "/HL/170100/10.23.1.3:5080",

  "/HL/170101/10.23.1.84:5080",
  "/HL/170101/10.23.1.85:5080",
  "/HL/170101/10.23.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testz"},
 "id": "NodeZone2"
}
]
)"
    ;

    nodezone_map_str_[1] = R"(
[
{
 "nodePaths":[
  "/LF/0d0100/10.13.1.1:5080",
  "/LF/0d0100/10.13.1.2:5080",
  "/LF/0d0100/10.13.1.3:5080",

  "/LF/0d0101/10.13.1.84:5080",
  "/LF/0d0101/10.13.1.85:5080",
  "/LF/0d0101/10.13.1.86:5080",

  "/HL/150100/10.21.1.1:5080",
  "/HL/150100/10.21.1.2:5080",
  "/HL/150100/10.21.1.3:5080",

  "/HL/150101/10.21.1.84:5080",
  "/HL/150101/10.21.1.85:5080",
  "/HL/150101/10.21.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "default"},
 "id": "NodeZone0"
},
{
 "nodePaths":[
  "/LF/0e0100/10.14.1.1:5080",
  "/LF/0e0100/10.14.1.2:5080",
  "/LF/0e0100/10.14.1.3:5080",

  "/LF/0e0101/10.14.1.84:5080",
  "/LF/0e0101/10.14.1.85:5080",
  "/LF/0e0101/10.14.1.86:5080",

  "/HL/160100/10.22.1.1:5080",
  "/HL/160100/10.22.1.2:5080",
  "/HL/160100/10.22.1.3:5080",

  "/HL/160101/10.22.1.84:5080",
  "/HL/160101/10.22.1.85:5080",
  "/HL/160101/10.22.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testz"},
 "id": "NodeZone1"
},
{
 "nodePaths":[
  "/LF/0f0100/*********:5080",
  "/LF/0f0100/10.15.1.3:5080",

  "/LF/0f0101/10.15.1.84:5080",
  "/LF/0f0101/10.15.1.85:5080",
  "/LF/0f0101/10.15.1.86:5080",

  "/HL/170100/*********:5080",
  "/HL/170100/10.23.1.3:5080",

  "/HL/170101/10.23.1.84:5080",
  "/HL/170101/10.23.1.85:5080",
  "/HL/170101/10.23.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testz"},
 "id": "NodeZone2"
}
]
)"
    ;

    dir_to_nodezonetag_str_[0] = R"(
{"/user/tiger/warehouse/testz": "testz"}
)"
    ;
  }

  void TearDown() override {
  }

  void GetDiskRequest(HeartbeatRequestProto* request, uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid("disk");
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = true;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  CreateRequestProto MakeCreateRequest() {
    CreateRequestProto create_request;
    create_request.set_src("");
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(false);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  PermissionStatus MakePermission() {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("root");
    p.set_permission(FsPermission::GetFileDefault().ToShort());
    return p;
  }

  AppendRequestProto MakeAppendRequest(const std::string &src,
                                       const std::string &client_name) {
    AppendRequestProto append_request;
    append_request.set_src(src);
    append_request.set_clientname(client_name);
    return append_request;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
  }


  HeartbeatRequestProto CreateHeartBeatRequest(std::string dn_uuid) {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid(dn_uuid);
      HeartbeatRequestProto request;

      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");

      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);

      return request;
  }

  void StartHeartbeat(NameSpaceWrapperPtr ns_wrapper) {
    ns_wrapper->stop = false;
    ns_wrapper->pause = false;
    CountDownLatch latch(1);
    ns_wrapper->heartbeat_thread = std::thread([&latch, ns_wrapper, this]() {

      bool heartbeated = false;
      while (!ns_wrapper->stop) {
        if (!ns_wrapper->pause) {

          DatanodeManager::RepeatedCmds cmds;

          for (auto itr = ns_wrapper->dn_map.begin();
               itr != ns_wrapper->dn_map.end();
               ++itr) {
            HeartbeatRequestProto request = CreateHeartBeatRequest(itr->second.uuid);
            ns_wrapper->datanode_manager->Heartbeat(request, &cmds);
          }

          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  NameSpaceWrapperPtr MakeNS(std::string block_placement_name) {
    FLAGS_block_placement_policy.assign(block_placement_name);

    NameSpaceWrapperPtr ret(new NameSpaceWrapper());

    char db_path[] = "rocksdb_XXXXXX";
    CHECK(mkdtemp(db_path) == db_path);
    LOG(INFO) << "MakeNS new DB dir: " << db_path;

    auto edit_log_ctx = CreateContext();
    ret->datanode_manager.reset(new DatanodeManager());
    ret->block_manager.reset(new BlockManager(edit_log_ctx));
    MockFSImageTransfer(db_path).Transfer();
    ret->ns.reset(new MockNameSpace(db_path,
                                    edit_log_ctx,
                                    ret->block_manager,
                                    ret->datanode_manager,
                                    std::make_shared<DataCenters>(),
                                    UfsEnv::Create()));
    ret->ha_state.reset(new MockHAState());
    ret->safemode.reset(new MockSafeMode());
    ret->ns->set_safemode(ret->safemode.get());
    ret->ns->set_ha_state(ret->ha_state.get());
    ret->block_manager->set_ha_state(ret->ha_state.get());
    ret->block_manager->set_safemode(ret->safemode.get());
    ret->block_manager->set_ns(ret->ns.get());
    ret->datanode_manager->set_block_manager(ret->block_manager.get());
    ret->ns->Start();
    ret->ns->StartActive();
    ret->ns->StartLeaseMonitor();

    auto bp = ExtractBlockPlacementNodeZoneFromNS(ret->ns);
    bp->SetNodeZoneMapRefresherTestData(nodezone_map_str_[0]);
    bp->ForceRefreshNodeZoneMapForTest();

    // add a datanode to the cluster
    // ret->RegisterDN("", "");
    ret->RegisterDN("dn.10.13.1.1", "10.13.1.1");
    ret->RegisterDN("dn.10.21.1.1", "10.21.1.1");
    ret->RegisterDN("dn.10.14.1.1", "10.14.1.1");
    ret->RegisterDN("dn.10.22.1.1", "10.22.1.1");
    ret->RegisterDN("dn.*********", "*********");
    ret->RegisterDN("dn.*********", "*********");

    StartHeartbeat(ret);
    // mock edit log sender
    auto last_tx_id = ret->ns->GetLastCkptTxId();
    auto sender =
        std::unique_ptr<EditLogSenderBase>(new MockEditLogSender(edit_log_ctx, last_tx_id));
    ret->ns->TestOnlySetEditLogSender(std::move(sender));

    LOG(INFO) << "Set TestOnly editlog sender successfully.";

    return ret;
  };

  BlockPlacementNodeZone* ExtractBlockPlacementNodeZoneFromNS(std::shared_ptr<MockNameSpace> ns) {
    return (BlockPlacementNodeZone*)ns->datanode_manager()->block_placement_.get();
  }

 protected:
  std::string nodezone_map_str_[2];
  std::string dir_to_nodezonetag_str_[1];
  std::unordered_map<DatanodeID, std::string> dn_dc_;
};

TEST_F(NameSpaceBlockPlacementTest, TestAddBlock) {
  auto ns_wrapper = MakeNS("nodezone");
  auto ns = ns_wrapper->ns;
  auto bp = ExtractBlockPlacementNodeZoneFromNS(ns);
  bp->SetNodeZoneMapRefresherTestData(nodezone_map_str_[0]);
  bp->SetDir2ZoneGroupRefresherTestData(dir_to_nodezonetag_str_[0]);
  std::string case0_nodezone_id;
  std::string case1_nodezone_id;
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  // wait bp set test data done

  auto p = MakePermission();
  ASSERT_TRUE(!ns->MkDirs("/user/tiger/warehouse/testz", p, true).HasException());
  std::string path = "/user/tiger/warehouse/testz/hi";

  cnetpp::base::IPAddress client_ip("***********");
  {
    // empty file
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    {
      ns->Delete(path, false);
      auto status = ns->CreateFile(path, p, create_request,
                     &create_response);
      ASSERT_FALSE(status.HasException());
    }
    DEFER([&]() { ns->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;

    add_request.mutable_previous()->set_blockid(10);
    ASSERT_STREQ(
        ns->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response)
            .ExceptionStr()
            .c_str(),
        JavaExceptions::IOException());
    add_request.mutable_previous()->Clear();
    add_response.Clear();

    ASSERT_FALSE(
        ns->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response)
            .HasException());
    ASSERT_EQ(add_response.block().offset(), 0);
    // retry
    AddBlockResponseProto add_response2;
    ASSERT_FALSE(
        ns->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response2)
            .HasException());
    ASSERT_EQ(add_response.block().b().blockid(),
              add_response2.block().b().blockid());
    ASSERT_EQ(add_response.block().offset(), add_response2.block().offset());
    ASSERT_EQ(add_response.block().locs_size(), 1);
    ASSERT_EQ(add_response.block().locs(0).id().ipaddr(),
              add_response2.block().locs(0).id().ipaddr());
    {
      auto nodezone_tag = bp->GetZoneGroupIdByDnip(add_response.block().locs(0).id().ipaddr());
      auto nodezone2_tag = bp->GetZoneGroupIdByDnip(add_response2.block().locs(0).id().ipaddr());
      ASSERT_EQ(nodezone_tag, "testz");
      ASSERT_EQ(nodezone2_tag, "testz");
      case0_nodezone_id = bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr());
    }

    FsyncRequestProto sync_request;
    sync_request.set_src(path);
    sync_request.set_client("client");
    sync_request.set_lastblocklength(100);
    sync_request.set_fileid(create_response.fs().fileid());
    ASSERT_FALSE(ns->Fsync(path, sync_request).HasException());
    auto status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response2);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    // TODO(liyuan) append on a full block
  }
  {
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ns->Delete(path, false);
    ASSERT_FALSE(ns->CreateFile(path, p, create_request,
                                 &create_response).HasException());
    DEFER([&]() { ns->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;
    auto status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_FALSE(status.HasException());
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               100,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    auto dn_uuid = ns_wrapper->GetDatanodeUUidFromIp(add_response.block().locs(0).id().ipaddr());
    ns->block_manager()->IncrementalBlockReport(dn_uuid, report);

    {
      auto nodezone_tag = bp->GetZoneGroupIdByDnip(add_response.block().locs(0).id().ipaddr());
      ASSERT_EQ(nodezone_tag, "testz");
      case1_nodezone_id = bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr());
    }

    ns_wrapper->RegisterDN("dn.*********", "*********");
    ns_wrapper->RegisterDN("dn.*********", "*********");
    bp->SetNodeZoneMapRefresherTestData(nodezone_map_str_[1]);
    ns_wrapper->RegisterDN("dn.**********", "**********");
    bp->ForceRefreshNodeZoneMapForTest();

    //// second block
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    // gs mismatch
    auto block_id = add_response.block().b().blockid();
    auto gs = add_response.block().b().generationstamp();
    add_response.Clear();
    add_request.mutable_previous()->set_generationstamp(gs + 1);
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(), "Reported previous block: block { B"
        + std::to_string(block_id) + ", num_bytes 0, gs "
        + std::to_string(gs + 1)
        + " } does not match stored last block: block { B"
        + std::to_string(block_id)
        + ", num_bytes 0, gs " + std::to_string(gs)
        + " } or penultimate block: block { B0, num_bytes 0, gs 0 }");
    ASSERT_FALSE(add_response.has_block());
    // block id mismatch
    add_request.mutable_previous()->set_generationstamp(gs);
    add_request.mutable_previous()->set_blockid(block_id + 1);
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(), "Reported previous block: block { B"
        + std::to_string(block_id + 1) + ", num_bytes 0, gs "
        + std::to_string(gs)
        + " } does not match stored last block: block { B"
        + std::to_string(block_id)
        + ", num_bytes 0, gs " + std::to_string(gs)
        + " } or penultimate block: block { B0, num_bytes 0, gs 0 }");
    ASSERT_FALSE(add_response.has_block());
    // matching block id & gs
    add_request.mutable_previous()->set_generationstamp(gs);
    add_request.mutable_previous()->set_blockid(block_id);
    add_request.mutable_previous()->set_numbytes(100);
    add_response.Clear();
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    LOG(INFO) << add_response.ShortDebugString();
    ASSERT_FALSE(status.HasException());
    ASSERT_EQ(add_response.block().b().blockid(), block_id + 1);
    ASSERT_EQ(add_response.block().b().generationstamp(), gs + 1);
    ASSERT_EQ(add_response.block().offset(), 100);
    ASSERT_EQ(add_response.block().corrupt(), false);
    ASSERT_EQ(add_response.block().locs_size(), 1);
    ASSERT_EQ(add_response.block().storagetypes(0), StorageTypeProto::DISK);
    auto state = ns->block_manager()->GetBlockUCState(block_id);
    ASSERT_EQ(state, BlockUCState::kComplete);

    {
      auto nodezone_tag = bp->GetZoneGroupIdByDnip(add_response.block().locs(0).id().ipaddr());
      ASSERT_EQ(nodezone_tag, "testz");
      ASSERT_NE(bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr()),
                "*********");
      ASSERT_NE(bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr()),
                "*********");
    }

    //// third block, first one is complete
    FsyncRequestProto sync_request;
    sync_request.set_src(path);
    sync_request.set_client("client");
    sync_request.set_lastblocklength(100);
    ASSERT_TRUE(!ns->Fsync(path, sync_request).HasException());
    add_request.mutable_previous()->set_blockid(block_id);
    add_request.mutable_previous()->set_generationstamp(gs);
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(), "Add block on a non-empty block: block { B"
        + std::to_string(block_id + 1) + ", num_bytes 100, gs "
        + std::to_string(gs + 1) + " }");
  }
  {
    //// third block, first is not completed
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ns->Delete(path, false);
    ASSERT_FALSE(ns->CreateFile(path, p, create_request,
                                 &create_response).HasException());
    DEFER([&]() { ns->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;
    auto status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    auto block_id = add_response.block().b().blockid();
    ASSERT_FALSE(status.HasException());

    case1_nodezone_id = bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr());

    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);

    ASSERT_EQ(case1_nodezone_id,
              bp->GetZoneIdByDnip(add_response.block().locs(0).id().ipaddr()));

    ASSERT_FALSE(status.HasException());
    auto state = ns->block_manager()->GetBlockUCState(block_id);
    ASSERT_EQ(state, BlockUCState::kCommitted);
    state = ns->block_manager()->GetBlockUCState(block_id + 1);
    ASSERT_EQ(state, BlockUCState::kUnderConstruction);
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    status = ns->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(),
                 JavaExceptions::NotReplicatedYetException());
    ASSERT_EQ(status.message(), "Not replicated yet: /user/tiger/warehouse/testz/hi");
  }
  ns->StopActive();
  ns->Stop();

  ns_wrapper->StopHeartbeat();
  ns_wrapper->block_manager.reset();
  ns_wrapper->datanode_manager.reset();
  ns_wrapper->ha_state.reset();
  ns_wrapper->safemode.reset();
  ns_wrapper->stop = true;
  ns_wrapper->ns.reset();
  ns_wrapper.reset();
}

}  // namespace dancenn

