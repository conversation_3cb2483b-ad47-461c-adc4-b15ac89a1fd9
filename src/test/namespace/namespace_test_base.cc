#include "test/namespace/namespace_test_base.h"

namespace dancenn {

LogRpcInfo NameSpaceTestBase::default_rpc_info = LogRpcInfo("", 0);
uint64_t NameSpaceTestBase::default_soft_limit_ms =
    FLAGS_lease_expired_soft_limit_ms;
uint64_t NameSpaceTestBase::default_hard_limit_ms =
    FLAGS_lease_expired_hard_limit_ms;
int32_t NameSpaceTestBase::default_dn_keep_alive_timeout_sec =
    FLAGS_datanode_keep_alive_timeout_sec;
int32_t NameSpaceTestBase::default_datanode_stale_interval_ms =
    FLAGS_datanode_stale_interval_ms;
int32_t NameSpaceTestBase::default_blockmap_num_bucket_each_slice =
    FLAGS_blockmap_num_bucket_each_slice;
int32_t NameSpaceTestBase::default_blockmap_num_slice =
    FLAGS_blockmap_num_slice;
uint32_t NameSpaceTestBase::default_dfs_replication_min =
    FLAGS_dfs_replication_min;

void NameSpaceTestBase::SetUp() {
  FLAGS_run_ut = true;
  FLAGS_all_datacenters = "LF,HL,LQ";
  FLAGS_lease_expired_soft_limit_ms = 200;
  FLAGS_lease_expired_hard_limit_ms = 400;
  FLAGS_lease_monitor_interval_ms = 400;
  FLAGS_datanode_keep_alive_timeout_sec = 1000;
  FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
  FLAGS_blockmap_num_bucket_each_slice = 1;
  FLAGS_blockmap_num_slice = 1;
  FLAGS_dfs_replication_min = 1;
  FLAGS_dfs_replication_max = 3;
  FLAGS_bytecool_feature_enabled = true;
  FLAGS_recycle_bin_enable = false;
  FLAGS_recycle_bin_scanner_enable = true;
  FLAGS_recycle_bin_scanner_interval_sec = 1;
  FLAGS_recycle_bin_retention_day = 0;
  FLAGS_client_replication_support = true;
  FLAGS_enable_snapshot_feature = true;
  /*todo setup ugi.*/

  FLAGS_permission_enabled = true;
  FLAGS_permission_model = "posix";
  ugi_ = UserGroupInfo("root", "supergroup");
  recycle_bin_dir_.append("/");
  recycle_bin_path_.append("/" + kRecycleBinDirNameString);
  recycle_userbin_path_.append(recycle_bin_path_ + "/" + ugi_.current_user());
  recycle_datebin_path_.append(recycle_userbin_path_ + "/" + TimeUtil::GetNowYMD());

  ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

  datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
  edit_log_ctx_ = CreateContext();
  block_manager_.reset(new BlockManager(edit_log_ctx_));
  block_manager_->TestOnlySetEditLogCtx(edit_log_ctx_);
  MockFSImageTransfer(db_path_).Transfer();
  ufs_env_ = UfsEnv::Create();
  ns_.reset(new MockNameSpace(db_path_,
                              edit_log_ctx_,
                              block_manager_,
                              datanode_manager_,
                              std::make_shared<DataCenters>(),
                              ufs_env_));
  ha_state_ = std::make_unique<MockHAState>();
  safemode_ = std::make_unique<MockSafeMode>();
  ns_->set_safemode(safemode_.get());
  ns_->set_ha_state(ha_state_.get());
  block_manager_->set_ha_state(ha_state_.get());
  block_manager_->set_safemode(safemode_.get());
  block_manager_->set_ns(ns_.get());
  datanode_manager_->set_block_manager(block_manager_.get());

  // mock edit log sender
  auto last_tx_id = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx_, last_tx_id));
  ns_->TestOnlySetEditLogSender(std::move(sender));

  ns_->Start();
  ns_->StartActive();

  // add a datanode to the cluster
  auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
  cnetpp::base::IPAddress ip("***********");
  datanode_manager_->Register(reg.datanodeid(), &reg, ip);
  datanode_manager_->RefreshConfig();
  StartStandBy();

  StartHeartbeat();

  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();
}

void NameSpaceTestBase::TearDown()  {
  FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
  FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
  FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
  FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
  FLAGS_blockmap_num_bucket_each_slice = default_blockmap_num_bucket_each_slice;
  FLAGS_blockmap_num_slice = default_blockmap_num_slice;
  FLAGS_dfs_replication_min = default_dfs_replication_min;

  stop_ = true;
  heartbeat_thread_.join();
  if (!transited_) {
    ns_->StopActive();
    ns_->Stop();
  }
  ns_.reset();
  FileUtils::DeleteDirectoryRecursively(db_path_);
}

void NameSpaceTestBase::AddHyperFile(
    const std::string& path,
    const std::vector<int32_t>& hyper_block_size,
    bool mock_broken,
    int32_t block_num) {
  std::vector<std::string> hyper_block_names;
  for (int i = 0; i < hyper_block_size.size(); i++) {
    std::string tmp_name = "_part00" + std::to_string(i) + "_";
    for (int j = 0; j < 8; j++) {
      tmp_name += std::to_string(i);
    }
    hyper_block_names.emplace_back(tmp_name);
  }

  HyperCacheMeta hyper_file_meta;
  hyper_file_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);
  for (const auto& hyper_block_name : hyper_block_names) {
    hyper_file_meta.add_hyperblock(hyper_block_name);
  }
  hyper_file_meta.set_stripewidth(1024 * 1024);

  CreateResponseProto response;
  auto create_hyper_file_request = MakeCreateRequest();
  create_hyper_file_request.set_createparent(true);
  auto attr_hyper_file = create_hyper_file_request.add_attr();
  attr_hyper_file->set_namespace_(
      ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  attr_hyper_file->set_name(kHyperFileKey);
  attr_hyper_file->set_value(hyper_file_meta.SerializeAsString());

  auto p = MakePermission();
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_hyper_file_request, &response)
                   .HasException());

  int i = 0, j = 0;
  for (const auto& hyper_block_name : hyper_block_names) {
    std::string hyper_block_full_path = path + hyper_block_name;

    HyperCacheMeta hyper_block_meta;
    hyper_block_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);

    CreateResponseProto create_hyper_block_response;
    auto create_hyper_block_request = MakeCreateRequest();
    create_hyper_block_request.set_src(hyper_block_full_path);
    create_hyper_block_request.set_replication(1);

    auto attr_hyper_block = create_hyper_block_request.add_attr();
    attr_hyper_block->set_namespace_(
        ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    attr_hyper_block->set_name(kHyperBlockKey);
    attr_hyper_block->set_value(hyper_block_meta.SerializeAsString());
    ASSERT_TRUE(!ns_->CreateFile(hyper_block_full_path,
                                 p,
                                 create_hyper_block_request,
                                 &create_hyper_block_response)
                     .HasException());
    j++;
    if (mock_broken && j + 1 == hyper_block_names.size()) {
      // Skip creating the last hyper block, so that a broken HyperFile is
      // created. Used by test cases which need to verify whether dancenn
      // perform as expected when hyperfile is broken.
      break;
    }
  }

  for (const auto& hyper_block_name : hyper_block_names) {
    std::string hyper_block_full_path = path + hyper_block_name;
    cnetpp::base::IPAddress client_ip("***********");
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;

    for (int j = 0; j < block_num; j++) {
      if (j > 0) {
        // Add 2nd block
        auto block_id_1st = add_response.block().b().blockid();
        auto gs_1st = add_response.block().b().generationstamp();

        add_request.mutable_previous()->CopyFrom(add_response.block().b());
        add_request.mutable_previous()->set_generationstamp(gs_1st);
        add_request.mutable_previous()->set_blockid(block_id_1st);
        add_request.mutable_previous()->set_numbytes(hyper_block_size[i]);
      }

      add_response.Clear();
      auto add_status = ns_->AddBlock(hyper_block_full_path,
                                      client_ip,
                                      default_rpc_info,
                                      add_request,
                                      &add_response);
      DLOG(ERROR) << add_status.ToString();

      ASSERT_FALSE(add_status.HasException());
      DLOG(ERROR) << add_response.ShortDebugString();

      BlockManager::RepeatedIncBlockReport report;
      MakeReport(
          add_response.block().b().blockid(),
          add_response.block().b().generationstamp(),
          hyper_block_size[i],
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
          &report);

      auto dn_locs = add_response.block().locs();
      for (auto& dn_loc : dn_locs) {
        block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                               report);
      }

      report.Clear();
      MakeReport(
          add_response.block().b().blockid(),
          add_response.block().b().generationstamp(),
          hyper_block_size[i],
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &report);
      for (auto& dn_loc : dn_locs) {
        block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                               report);
      }
    }

    CompleteRequestProto complete_request;
    complete_request.set_src(hyper_block_full_path);
    complete_request.set_clientname("client");
    complete_request.mutable_last()->CopyFrom(add_response.block().b());
    complete_request.mutable_last()->set_numbytes(hyper_block_size[i]);
    DLOG(ERROR) << complete_request.ShortDebugString();
    ASSERT_TRUE(!ns_->CompleteFile(hyper_block_full_path, complete_request)
                     .HasException());

    i++;

    if (mock_broken && i + 1 == hyper_block_names.size()) {
      // Skip creating the last hyper block, so that a broken HyperFile is
      // created. Used by test cases which need to verify whether dancenn
      // perform as expected when hyperfile is broken.
      break;
    }
  }
}

void NameSpaceTestBase::AddHyperFile(
    const std::string& path,
    const std::vector<std::string>& hyper_block_names,
    uint64_t stripe_width) {
  HyperCacheMeta meta;
  meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);
  for (auto& hyper_block_name : hyper_block_names) {
    meta.add_hyperblock(hyper_block_name);
  }

  meta.set_stripewidth(stripe_width);

  auto create_hyper_file_request = MakeCreateRequest();
  auto attr = create_hyper_file_request.add_attr();
  attr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  attr->set_name(kHyperFileKey);
  attr->set_value(meta.SerializeAsString());

  auto create_hyper_block_request = MakeCreateRequest();
  auto p = MakePermission();
  CreateResponseProto response;

  ASSERT_TRUE(
      ns_->CreateFile(path, p, create_hyper_file_request, &response).IsOK());
}

void NameSpaceTestBase::AddFile(const std::string& path,
                                uint64_t len,
                                uint32_t replica,
                                bool need_complete,
                                AddBlockResponseProto* add_response,
                                CreateResponseProto* create_response,
                                bool create_parent,
                                uint32_t create_flag) {
  auto create_request = MakeCreateRequest();
  create_request.set_replication(replica);
  create_request.set_createparent(create_parent);
  create_request.set_createflag(create_flag);
  PermissionStatus p;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, create_response)
                   .HasException());

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, add_response)
           .HasException());

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response->block().b().blockid(),
             add_response->block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);

  if (need_complete) {
    report.Clear();
    MakeReport(add_response->block().b().blockid(),
               add_response->block().b().generationstamp(),
               len,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);

    CompleteRequestProto complete_request;
    complete_request.set_src(path);
    complete_request.set_clientname("client");
    complete_request.mutable_last()->CopyFrom(add_response->block().b());
    complete_request.mutable_last()->set_numbytes(len);
    ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
  }
}

void NameSpaceTestBase::AppendFile(const std::string& path,
                                   uint64_t len,
                                   uint32_t replica,
                                   bool need_complete,
                                   AddBlockResponseProto* add_response,
                                   AppendResponseProto* append_response) {
  // reusing last block is not supported yet, test addBlock
  auto store_flag = FLAGS_append_reuse_last_block;
  FLAGS_append_reuse_last_block = false;
  DEFER([&](){
    FLAGS_append_reuse_last_block = store_flag;
  });
  std::string client_name = "client";
  std::string client_machine = "client_machine";
  auto append_request = MakeAppendRequest(path, client_name);
  DANCENN_ASSERT_OK(ns_->Append(
      path, client_machine, default_rpc_info, append_request, append_response));

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  DANCENN_ASSERT_OK(ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, add_response));
  const auto& block = add_response->block();

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(block.b().blockid(),
             block.b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);

  if (need_complete) {
    report.Clear();
    MakeReport(block.b().blockid(),
               block.b().generationstamp(),
               len,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);

    CompleteRequestProto complete_request;
    complete_request.set_src(path);
    complete_request.set_clientname(client_name);
    complete_request.mutable_last()->CopyFrom(block.b());
    complete_request.mutable_last()->set_numbytes(len);
    DANCENN_ASSERT_OK(ns_->CompleteFile(path, complete_request));
  }
}

CommitBlockSynchronizationRequestProto NameSpaceTestBase::GetCommitSyncRequest(
    uint32_t len,
    const AddBlockResponseProto& add_response,
    bool close,
    bool del) {
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  auto recover_cmd = heartbeat_response.cmds(0).recoverycmd();
  CommitBlockSynchronizationRequestProto commit_request;
  commit_request.mutable_block()->CopyFrom(add_response.block().b());
  commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp());
  commit_request.set_newlength(len);
  commit_request.set_closefile(close);
  commit_request.set_deleteblock(del);
  return commit_request;
}

}
