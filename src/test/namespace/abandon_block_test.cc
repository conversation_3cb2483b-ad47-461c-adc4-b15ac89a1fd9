// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/17
// Description:
// This test suite want to make sure block info proto in RocksDB will
// be deleted when last block is abandoned.
// The last block will be abandoned in the following 3 cases:
// 1. Client calls ClientNamenodeService::abandonBlock.
// 2. Datanode calls DatanodeService::commitBlockSynchronization with
//    parameters closeFile = true and deleteBlock = true.
// 3. Datanode calls DatanodeService::commitBlockSynchronization with
//    parameters closeFile = false and deleteBlock = true.

#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <rocksdb/db.h>

#include <memory>
#include <string>
#include <vector>

#include "base/closure.h"
#include "base/constants.h"
#include "base/metrics.h"
#include "base/path_util.h"
#include "base/platform.h"
#include "base/rwlock_manager.h"
#include "base/status.h"
#include "base/string_utils.h"
#include "block_manager/block_info.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_pufs_info.h"
#include "edit/op/all.h"
#include "edit/sender_base.h"
#include "ha/operations.h"
#include "lease/lease_manager_base.h"
#include "namespace/inode.h"
#include "namespace/meta_storage.h"
#include "namespace/meta_storage_writer.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/inode.pb.h"
#include "proto/generated/dancenn/status_code.pb.h"
#include "test/block_manager/gmock_block_manager.h"
#include "test/gmock_edit_log_sender.h"
#include "test/lease/gmock_lease_manager.h"
#include "test/matcher.h"
#include "test/mock_ha_state.h"
#include "test/namespace/gmock_meta_storage.h"
#include "test/namespace/gmock_writer.h"
#include "test/safemode/gmock_safemode.h"

using namespace testing;  // NOLINT(build/namespaces)

DECLARE_uint32(dfs_rwlocks_static_depth);
DECLARE_bool(dfs_meta_storage_inode_key_v2);

namespace cloudfs {

bool operator==(const cloudfs::BlockProto& lhs,
                const cloudfs::BlockProto& rhs) {
  return lhs.blockid() == rhs.blockid() && lhs.genstamp() == rhs.genstamp() &&
         lhs.numbytes() == rhs.numbytes();
}

}  // namespace cloudfs

namespace dancenn {

bool operator==(const INode& lhs, const INode& rhs) {
  return lhs.id() == rhs.id() && lhs.parent_id() == rhs.parent_id() &&
         lhs.name() == rhs.name() && lhs.type() == rhs.type() &&
         lhs.blocks_size() == rhs.blocks_size();
}

class AbandonBlockUT : public Test {
 public:
  void SetUp() override {
    FileFinalizer* file_finalizer = new FileFinalizer();
    ns_ = std::make_unique<NameSpace>(
        nullptr,
        nullptr,
        std::shared_ptr<BlockManager>(),
        std::unique_ptr<BlockReportManager>(),
        std::shared_ptr<DatanodeManager>(),
        std::unique_ptr<LeaseManager>(),
        std::make_unique<LeaseMonitor>(
            nullptr,
            [](uint64_t inode_id,
               const std::string& orig_holder,
               const std::string& new_holder) { return Status(); }),
        std::shared_ptr<EditLogContextBase>(),
        std::shared_ptr<EditLogSenderBase>(),
        std::shared_ptr<MetaStorage>(),
        std::unique_ptr<RWLockManager>(),
        std::shared_ptr<KeyManager>(),
        std::shared_ptr<DataCenters>(),
        std::shared_ptr<AccessCounterManager>(),
        std::unique_ptr<FileFinalizerBase>(file_finalizer),
        std::shared_ptr<JobManager>());
    ns_->set_rwlock_manager(
        std::make_unique<RWLockManager>(FLAGS_dfs_rwlocks_static_depth));
    ha_state_ = std::make_unique<MockHAState>();
    ha_state_->s_ = Status(Code::kOK);
    ns_->set_ha_state(ha_state_.get());
    safemode_ = std::make_unique<GMockSafeMode>();
    ns_->set_safemode(safemode_.get());
    meta_storage_ = new GMockMetaStorage();
    meta_storage_writer_ = new meta_storage::GMockWriter();
    meta_storage_->SetWriter(
        std::shared_ptr<meta_storage::Writer>(meta_storage_writer_));
    ns_->set_meta_storage(std::unique_ptr<MetaStorage>(meta_storage_));
    lease_manager_ = new GMockLeaseManager();
    ns_->set_lease_manager(std::unique_ptr<LeaseManagerBase>(lease_manager_));
    edit_log_sender_ = new GMockEditLogSender();
    ns_->TestOnlySetEditLogSender(
        std::unique_ptr<EditLogSenderBase>(edit_log_sender_));
    block_manager_ = new GMockBlockManager();
    ns_->set_block_manager(std::shared_ptr<BlockManager>(block_manager_));
    file_finalizer->Start(ns_.get(),
                          block_manager_,
                          nullptr,
                          lease_manager_,
                          edit_log_sender_,
                          meta_storage_,
                          nullptr);

    permission_.Clear();
    permission_.set_username("user");
    permission_.set_groupname("group");
    permission_.set_permission(0);

    root_inode_.Clear();
    MakeINode(kRootINodeId,
              kRootINodeId,
              kRootName,
              permission_,
              INode::kDirectory,
              &root_inode_);

    extended_bp_.Clear();
    extended_bp_.set_poolid("BP-18014398509482091-1630997094871");
    extended_bp_.set_blockid(1);
    extended_bp_.set_generationstamp(1000);
    extended_bp_.set_numbytes(1024);
    bp_.Clear();
    bp_.set_blockid(extended_bp_.blockid());
    bp_.set_genstamp(extended_bp_.generationstamp());
    bp_.set_numbytes(extended_bp_.numbytes());
    ls_inode_.Clear();
    MakeINode(kLastReservedINodeId + 1,
              kRootINodeId,
              "ls",
              permission_,
              INode::kFile,
              &ls_inode_);
    ls_inode_.set_status(INode::kFileUnderConstruction);
    PinStatus* pin_status = ls_inode_.mutable_pin_status();
    pin_status->set_pinned(false);
    pin_status->set_ttl(-1);
    pin_status->set_txid(0);
    pin_status->set_recursive(false);
    *ls_inode_.add_blocks() = bp_;
    block_pufs_info_.block_pool_id_ = extended_bp_.poolid();
    block_pufs_info_.block_id_ = extended_bp_.blockid();
    block_pufs_info_.gen_stamp_ = extended_bp_.generationstamp();
    block_pufs_info_.inode_id_ = ls_inode_.id();
    block_pufs_info_.state_ = BlockPufsState::kUploadIssued;
    bip_ = block_pufs_info_.GetBlockInfoProto();
  }

  rocksdb::Slice GetKey(const INode& inode) {
    if (FLAGS_dfs_meta_storage_inode_key_v2) {
      std::string key;
      key.reserve(19 + inode.name().size());
      key.resize(8);
      platform::WriteBigEndian(&key[0], 0, inode.parent_id());
      key.append("/");
      key.append(inode.name());
      key.insert(key.end(), '\0');
      key.append("/");
      key.resize(19 + inode.name().size());
      platform::WriteBigEndian(&key[0], 11 + inode.name().size(), inode.id());
      return GetSlice(key);
    } else {
      std::string key;
      key.reserve(18 + inode.name().size());
      key.resize(8);
      platform::WriteBigEndian(&key[0], 0, inode.parent_id());
      key.append("/");
      key.append(inode.name());
      key.append("/");
      key.resize(18 + inode.name().size());
      platform::WriteBigEndian(&key[0], 10 + inode.name().size(), inode.id());
      return GetSlice(key);
    }
  }

  rocksdb::Slice GetValue(const INode& inode) {
    std::string value;
    inode.SerializeToString(&value);
    return GetSlice(value);
  }

  rocksdb::Slice GetSlice(const std::string& str) {
    slice_holders_.push_back(str);
    return slice_holders_.back();
  }

  rocksdb::Slice GetSlice(BlockID blk_id) {
    return GetSlice(std::to_string(blk_id));
  }

  rocksdb::Slice EncodeBlockID(BlockID blk_id) {
    std::string blk_id_str;
    blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
    platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
    return GetSlice(blk_id_str);
  }

 protected:
  std::unique_ptr<NameSpace> ns_;
  std::unique_ptr<MockHAState> ha_state_;
  std::unique_ptr<GMockSafeMode> safemode_;
  GMockMetaStorage* meta_storage_;
  meta_storage::GMockWriter* meta_storage_writer_;
  GMockLeaseManager* lease_manager_;
  GMockEditLogSender* edit_log_sender_;
  GMockBlockManager* block_manager_;

  PermissionStatus permission_;
  INode root_inode_;
  cloudfs::ExtendedBlockProto extended_bp_;
  cloudfs::BlockProto bp_;
  INode ls_inode_;
  BlockPufsInfo block_pufs_info_;
  BlockInfoProto bip_;

  // rocksdb::Slice won't do deep copy of std::string.
  // So we should put std::string in slice_holders_ before
  // creating rocksdb::Slice by it.
  std::vector<std::string> slice_holders_;
};

TEST_F(AbandonBlockUT, ClientAbandonBlock) {
  cloudfs::AbandonBlockRequestProto request;
  *request.mutable_b() = extended_bp_;
  request.set_src("/ls");
  request.set_holder("client-0");

  EXPECT_CALL(*safemode_, IsOn()).WillRepeatedly(Return(false));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(Return(root_inode_));
  ls_inode_.set_status(INode::kFileUnderConstruction);
  ls_inode_.mutable_uc()->set_client_name("client-0");
  EXPECT_CALL(*meta_storage_, GetINode(kRootINodeId, "ls", _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<2>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(bp_.blockid(), _))
      .WillRepeatedly(DoAll(SetArgPointee<1>(bip_), Return(true)));
  EXPECT_CALL(*lease_manager_, CheckLease("client-0", ls_inode_.id()))
      .WillRepeatedly(Return(true));

  INode ls_inode_abandon_blk = ls_inode_;
  ls_inode_abandon_blk.mutable_blocks()->RemoveLast();
  EXPECT_CALL(*edit_log_sender_,
              LogAbandonBlock("/ls",
                              ls_inode_abandon_blk,
                              bp_.blockid(),
                              ls_inode_,
                              testing::_,
                              LogRpcInfo()))
      .Times(1)
      .WillOnce(Return(1));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  GMockWriteBatch* wb = new GMockWriteBatch();
  EXPECT_CALL(*meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));

  rocksdb::Slice key1 = GetKey(ls_inode_abandon_blk);
  rocksdb::Slice val1 = GetValue(ls_inode_abandon_blk);
  LOG(INFO) << "key1: " << StringUtils::ToHexString(key1.ToString())
            << " val1: " << StringUtils::ToHexString(val1.ToString());
  EXPECT_CALL(*wb, Put(kINodeDefaultCFHandle, key1, val1))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));

  rocksdb::Slice key2 = EncodeBlockID(bp_.blockid());
  rocksdb::Slice val2 = GetSlice("");
  LOG(INFO) << "key2: " << StringUtils::ToHexString(key2.ToString())
            << " val2: " << StringUtils::ToHexString(val2.ToString());
  EXPECT_CALL(*wb, Put(kDeprecatingBlockCFHandle, key2, val2))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Put(kLeaseCFHandle, testing::_, testing::_)).Times(1);
  {
    InSequence update_rocksdb_before_updating_memory;
    EXPECT_CALL(*meta_storage_writer_, Push(_))
        .Times(1)
        .WillOnce([](meta_storage::WriteTask* task) {
          task->Next();
          delete task;
        });
    // EXPECT_CALL(*block_manager_,
    //             RemoveBlocksAndUpdateSafeMode(ElementsAre(bp_)))
    //     .Times(1);
  }
  // ns_->lease_manager()->AddLease("client-0", ls_inode_abandon_blk.id());
  ns_->AsyncAbandonBlock(
      "/ls",
      &request,
      LogRpcInfo(),
      new RpcClosure([](const Status& s) { EXPECT_FALSE(s.HasException()); },
                     true));
  EXPECT_EQ(block_manager_->BlockManager::GetINodeId(bp_.blockid()),
            kInvalidINodeId);
}

TEST_F(AbandonBlockUT, ReplayClientAbandonBlock) {
  auto op = new OpUpdateBlocks();
  op->SetOpCode(OP_UPDATE_BLOCKS);
  op->SetTxid(1);
  op->set_path("/ls");
  auto ctx = std::make_shared<ApplyContext>(MetricID());
  ctx->op.reset(op);
  EXPECT_TRUE(SplitPath(op->path(), &ctx->src_path_components));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(Return(root_inode_));
  EXPECT_CALL(*meta_storage_, GetINode(kRootINodeId, "ls", _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<2>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(bp_.blockid(), _))
      .WillRepeatedly(DoAll(SetArgPointee<1>(bip_), Return(true)));

  INode ls_inode_abandon_blk = ls_inode_;
  ls_inode_abandon_blk.mutable_blocks()->RemoveLast();
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINodeAndDeleteLastBlock(testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](const INode& inode,
                                       BlockID last_blk_to_del,
                                       const INode* old_inode,
                                       int64_t txid,
                                       Closure* done,
                                       INodeStatChangeRecorder* recorder) {
        return meta_storage_->MetaStorage::OrderedUpdateINodeAndDeleteLastBlock(
            inode, last_blk_to_del, old_inode, txid, done, recorder);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  GMockWriteBatch* wb = new GMockWriteBatch();
  EXPECT_CALL(*meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Put(kINodeDefaultCFHandle, _, GetValue(ls_inode_abandon_blk)))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb,
              Put(kDeprecatingBlockCFHandle,
                  EncodeBlockID(bp_.blockid()),
                  GetSlice("")))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*meta_storage_writer_, Push(_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) { delete task; });
  ns_->ApplyOpUpdateBlocks(ctx);
}

TEST_F(AbandonBlockUT, DnCommitBlockSyncDeleteBlockAndCloseFile) {
  cloudfs::datanode::CommitBlockSynchronizationRequestProto request;
  *request.mutable_block() = extended_bp_;
  request.set_newgenstamp(0);
  request.set_newlength(0);
  request.set_closefile(true);
  request.set_deleteblock(true);

  ls_inode_.mutable_uc()->set_client_name("client-0");
  ls_inode_.mutable_uc()->set_client_machine("n248-140-004");

  EXPECT_CALL(*safemode_, IsOn()).WillRepeatedly(Return(false));
  EXPECT_CALL(*block_manager_, GetINodeId(bp_.blockid()))
      .Times(1)
      .WillOnce(testing::Return(ls_inode_.id()));
  EXPECT_CALL(*meta_storage_, GetINode(ls_inode_.id(), _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<1>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(Return(root_inode_));
  EXPECT_CALL(*meta_storage_,
              GetINode(root_inode_.id(), ls_inode_.name(), _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<2>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(bp_.blockid(), _))
      .WillRepeatedly(DoAll(SetArgPointee<1>(bip_), Return(true)));
  INode ls_inode_abandon_blk = ls_inode_;
  ls_inode_abandon_blk.mutable_blocks()->RemoveLast();
  ls_inode_abandon_blk.clear_uc();
  ls_inode_abandon_blk.set_status(INode::kFileComplete);
  EXPECT_CALL(*edit_log_sender_,
              LogCloseFileV2("/ls",
                             ls_inode_abandon_blk,
                             testing::_,
                             bp_.blockid(),
                             ls_inode_,
                             testing::_,
                             testing::_))
      // Pointee(BlockInfoProtoEq(BlockInfoProto())),
      // bp_.blockid()))
      .Times(1)
      .WillOnce(Return(1));
  GMockWriteBatch* wb = new GMockWriteBatch();
  EXPECT_CALL(*meta_storage_, OrderedCloseFile(_, _, _, _, _, _, _, _))
      .Times(1)
      .WillOnce(Invoke([this](INode* minode,
                              const BlockInfoProto* last_bip,
                              BlockID last_blk_to_be_abandoned,
                              const INode* old_inode,
                              SnapshotLog& snaplog,
                              int64_t txid,
                              Closure* done,
                              INodeStatChangeRecorder* recorder) {
        return meta_storage_->MetaStorage::OrderedCloseFile(
            minode, last_bip, last_blk_to_be_abandoned, old_inode, snaplog, txid, done, recorder);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  EXPECT_CALL(*meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb,
              Put(kINodeDefaultCFHandle,
                  GetKey(ls_inode_abandon_blk),
                  // GetValue(ls_inode_abandon_blk) != actual value
                  // because of mtime.
                  _))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(
      *wb,
      Put(kDeprecatingBlockCFHandle, EncodeBlockID(bp_.blockid()), GetSlice("")))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  {
    InSequence update_rocksdb_before_updating_memory;
    // TODO(ruanjunbin): BlockManager::CommitBlockSynchronization calls
    // RemoveBlock instead of RemoveBlocksAndUpdateSafeMode. Change it.
    EXPECT_CALL(*meta_storage_writer_, Push(_))
        .Times(1)
        .WillOnce([](meta_storage::WriteTask* task) {
          task->Next();
          delete task;
        });
  }
  ns_->CommitBlockSynchronization(request);
}

TEST_F(AbandonBlockUT, ReplayDnCommitBlockSyncDeleteBlockAndCloseFile) {
  auto op = new OpClose();
  op->SetOpCode(OP_CLOSE);
  op->SetTxid(1);
  op->set_inodeId(ls_inode_.id());
  op->set_path("/ls");
  op->set_blockSize(0);
  auto ctx = std::make_shared<ApplyContext>(MetricID());
  ctx->done = NewRpcCallback();
  ctx->op.reset(op);
  EXPECT_TRUE(SplitPath(op->path(), &ctx->src_path_components));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(Return(root_inode_));
  EXPECT_CALL(*meta_storage_, GetINode(kRootINodeId, "ls", _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<2>(ls_inode_), Return(StatusCode::kOK)));

  // EXPECT_CALL(*lease_manager_, RemoveLease(ls_inode_.id()))
  //     .Times(1)
  //     .WillOnce(testing::Return(true));

  INode ls_inode_abandon_blk = ls_inode_;
  ls_inode_abandon_blk.mutable_blocks()->RemoveLast();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(bp_.blockid(), _))
      .WillRepeatedly(DoAll(SetArgPointee<1>(bip_), Return(true)));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  GMockWriteBatch* wb = new GMockWriteBatch();
  EXPECT_CALL(*meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Put(kINodeDefaultCFHandle, GetKey(ls_inode_abandon_blk), _))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb,
              Put(kDeprecatingBlockCFHandle,
                  EncodeBlockID(bp_.blockid()),
                  GetSlice("")))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*meta_storage_writer_, Push(_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) { delete task; });
  ns_->ApplyOpClose(ctx);
}

TEST_F(AbandonBlockUT, DnCommitBlockSyncDeleteBlockButDontCloseFile) {
  cloudfs::datanode::CommitBlockSynchronizationRequestProto request;
  *request.mutable_block() = extended_bp_;
  request.set_newgenstamp(0);
  request.set_newlength(0);
  request.set_closefile(false);
  request.set_deleteblock(true);

  ls_inode_.mutable_uc()->set_client_name("client-0");
  ls_inode_.mutable_uc()->set_client_machine("n248-140-004");

  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(Return(false));
  EXPECT_CALL(*block_manager_, GetINodeId(bp_.blockid()))
      .Times(1)
      .WillOnce(testing::Return(ls_inode_.id()));
  EXPECT_CALL(*meta_storage_, GetINode(ls_inode_.id(), _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<1>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(Return(root_inode_));
  EXPECT_CALL(*meta_storage_,
              GetINode(root_inode_.id(), ls_inode_.name(), _, nullptr))
      .WillRepeatedly(
          DoAll(SetArgPointee<2>(ls_inode_), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(bp_.blockid(), _))
      .WillRepeatedly(DoAll(SetArgPointee<1>(bip_), Return(true)));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization)
      .Times(1)
      .WillOnce(Return(Status{}));

  INode ls_inode_abandon_blk = ls_inode_;
  ls_inode_abandon_blk.mutable_blocks()->RemoveLast();
  EXPECT_CALL(
      *edit_log_sender_,
      LogUpdateBlocksV2(
          "/ls", ls_inode_abandon_blk, ls_inode_, testing::_, LogRpcInfo()))
      .Times(1)
      .WillOnce(Return(1));
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINodeAndDeleteLastBlock(testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_,
                                                   testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](const INode& inode,
                                       BlockID last_blk_to_del,
                                       const INode* old_inode,
                                       int64_t txid,
                                       Closure* done,
                                       INodeStatChangeRecorder* recorder) {
        return meta_storage_->MetaStorage::OrderedUpdateINodeAndDeleteLastBlock(
            inode, last_blk_to_del, old_inode, txid, done, recorder);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::_, testing::_, testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  GMockWriteBatch* wb = new GMockWriteBatch();
  EXPECT_CALL(*meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Put(kINodeDefaultCFHandle, GetKey(ls_inode_abandon_blk), _))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(
      *wb,
      Put(kDeprecatingBlockCFHandle, EncodeBlockID(bp_.blockid()), GetSlice("")))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Put(kLeaseCFHandle, testing::_, testing::_)).Times(1);
  EXPECT_CALL(*meta_storage_writer_, Push(_))
      .Times(1)
      .WillOnce([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  ns_->CommitBlockSynchronization(request);
}

// ReplayDnCommitBlockSyncDeleteBlockButDontCloseFile =
// ReplayClientAbandonBlock

}  // namespace dancenn
