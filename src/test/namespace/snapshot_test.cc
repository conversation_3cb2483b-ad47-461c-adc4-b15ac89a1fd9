#include "base/java_exceptions.h"
#include "snapshot/snapshot_manager_impl.h"
#include "test/namespace/namespace_test_base.h"

namespace dancenn {

// Test snapshot related function in NameSpace class
// SnapshotManagerImpl is also tested
class NameSpaceSnapshotTest : public NameSpaceTestBase {
 public:
  const SnapshotRootMap& GetSnapshotRootMap() {
    return dynamic_cast<const SnapshotManagerImpl&>(
               ns_->TestOnlyGetSnapshotManager())
        .TestOnlySnapshotRootMap();
  }
};

TEST_F(NameSpaceSnapshotTest, AllowAndDisAllowSnapshot) {
  std::string test_path_1 = "/src";
  std::string test_path_2 = "/src/p1";
  std::string test_path_3 = "/src/p1/p2";
  std::string test_path_4 = "/sr";
  auto create_request = MakeCreateRequest();
  auto p = MakePermission();
  CreateResponseProto create_response;
  int snapshot_root_num = 0;

  // test can not allow/disallow snapshot on not exist path
  {
    ns_->Delete(test_path_1, true);
    DANCENN_ASSERT_STATUS(ns_->AllowSnapshot(test_path_1),
                          JavaExceptions::kFileNotFoundException);
    ASSERT_EQ(snapshot_root_num, GetSnapshotRootMap().size());
    DANCENN_ASSERT_STATUS(ns_->DisAllowSnapshot(test_path_1),
                          JavaExceptions::kFileNotFoundException);
  }
  // test can not allow/disallow snapshot on file
  {
    std::string file = "/file";
    DANCENN_ASSERT_OK(
        ns_->CreateFile(file, p, create_request, &create_response));
    DANCENN_ASSERT_STATUS(ns_->AllowSnapshot(file),
                          JavaExceptions::kSnapshotException);
    DANCENN_ASSERT_STATUS(ns_->DisAllowSnapshot(file),
                          JavaExceptions::kSnapshotException);
  }
  // test can not allow nested snapshot roots
  {
    DANCENN_ASSERT_OK(ns_->MkDirs(test_path_3, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->MkDirs(test_path_4, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_2));

    auto status = ns_->AllowSnapshot(test_path_1);
    DANCENN_ASSERT_STATUS(status, JavaExceptions::kSnapshotException);
    ASSERT_NE(std::string::npos, status.ToString().find("subdirectory"))
        << status.ToString();

    status = ns_->AllowSnapshot(test_path_3);
    DANCENN_ASSERT_STATUS(status, JavaExceptions::kSnapshotException);
    ASSERT_NE(std::string::npos, status.ToString().find("ancestor"))
        << status.ToString();

    ASSERT_EQ(++snapshot_root_num, GetSnapshotRootMap().size());
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_4));
    ASSERT_EQ(++snapshot_root_num, GetSnapshotRootMap().size());

    // can allow on dir which is already snapshottable
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_2));
    ASSERT_EQ(snapshot_root_num, GetSnapshotRootMap().size());
    ::cloudfs::SnapshottableDirectoryListingProto result;
    DANCENN_ASSERT_OK(ns_->GetSnapshottableDirListing(&result));
    ASSERT_EQ(2, result.snapshottabledirlisting_size());
    const auto& snapshot_dirs = result.snapshottabledirlisting();
    ASSERT_EQ(test_path_4, snapshot_dirs.Get(0).dirstatus().path());
    ASSERT_EQ(test_path_2, snapshot_dirs.Get(1).dirstatus().path());
  }
  // test disallow snapshot
  {
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_path_2));
    ASSERT_EQ(--snapshot_root_num, GetSnapshotRootMap().size());

    // can disallow on dir which is already not snapshottable
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_path_2));
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_path_3));
    ASSERT_EQ(snapshot_root_num, GetSnapshotRootMap().size());

    // can not disallow snapshot root which has snapshots
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_2));
    std::string snapshot_path;
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_2, "a", &snapshot_path));
    ASSERT_EQ("/src/p1/.snapshot/a", snapshot_path);
    DANCENN_ASSERT_STATUS(ns_->DisAllowSnapshot(test_path_2),
                          JavaExceptions::kSnapshotException);
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_path_2, "a"));
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_path_2));
  }
}

TEST_F(NameSpaceSnapshotTest, CreateDeleteRenameSnapshot) {
  std::string test_path_1 = "/src";
  std::string test_path_2 = "/src2";
  std::string test_path_3 = "/rename";
  auto p = MakePermission();
  int snapshot_num = 0;

  DANCENN_ASSERT_OK(ns_->MkDirs(test_path_3, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_3));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_3, "re"));   // snapshot id++
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_3, "re2"));  // snapshot id++
  snapshot_num += 2;

  // can not create snapshot on not snapshottable path
  {
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, ""),
                          JavaExceptions::kFileNotFoundException);
    DANCENN_ASSERT_OK(ns_->MkDirs(test_path_1, p, true /*create_parent*/));
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, ""),
                          JavaExceptions::kSnapshotException);
  }
  // snapshot name is empty
  {
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_1));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_1, ""));  // snapshot id++
    snapshot_num++;

    DANCENN_ASSERT_STATUS(ns_->RenameSnapshot(test_path_3, "re", ""),
                          JavaExceptions::kInvalidPathException);
  }
  // can not create/rename_to snapshot if the name is illegal
  {
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, ".snapshot"),
                          JavaExceptions::kInvalidPathException);
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, "a/b"),
                          JavaExceptions::kInvalidPathException);

    DANCENN_ASSERT_STATUS(ns_->RenameSnapshot(test_path_3, "re", ".snapshot"),
                          JavaExceptions::kInvalidPathException);
    DANCENN_ASSERT_STATUS(ns_->RenameSnapshot(test_path_3, "re", "a/b"),
                          JavaExceptions::kInvalidPathException);
  }
  // can not create/rename_to snapshot if there exists the same name snapshot
  {
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_1, "2"));  // snapshot id++
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, "2"),
                          JavaExceptions::kSnapshotException);
    DANCENN_ASSERT_STATUS(ns_->RenameSnapshot(test_path_3, "re", "re2"),
                          JavaExceptions::kSnapshotException);

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_path_1, "2"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_path_3, "re2"));
    snapshot_num -= 2;

    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_1, "2"));  // snapshot id++
    DANCENN_ASSERT_OK(ns_->RenameSnapshot(test_path_3, "re", "re2"));
  }
  // test snapshot num limit of per-root, test snapshot max id
  {
    dynamic_cast<SnapshotManagerImpl&>(ns_->TestOnlyGetSnapshotManager())
        .SetLimits(6, 2);
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_1, "3"),
                          JavaExceptions::kSnapshotException);
    DANCENN_ASSERT_OK(ns_->MkDirs(test_path_2, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_2));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_2, "3"));  // snapshot id++
    DANCENN_ASSERT_STATUS(ns_->CreateSnapshot(test_path_2, "4"),
                          JavaExceptions::kSnapshotException);
  }
}

TEST_F(NameSpaceSnapshotTest, SnapshotManagerLaunch) {
  std::string test_path_1 = "/p/src";
  std::string test_path_2 = "/q/src";
  std::string test_path_3 = "/r/src";
  auto p = MakePermission();

  DANCENN_ASSERT_OK(ns_->MkDirs(test_path_1, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->MkDirs(test_path_2, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->MkDirs(test_path_3, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_1));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_2));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_path_3));
  DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_path_3));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_1, ""));   // snapshot id 1
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_2, "a"));  // snapshot id 2
  DANCENN_ASSERT_OK(ns_->RenameSnapshot(test_path_2, "a", "b"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_path_2, "a"));  // snapshot id 3
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_path_2, "b"));

  auto meta_storage = ns_->TestOnlyGetMetaStorage();
  SnapshotManagerImpl temp(meta_storage);
  temp.Launch();
  ASSERT_EQ(2, temp.TestOnlySnapshotRootMap().size());
  for (const auto& elem : temp.TestOnlySnapshotRootMap()) {
    INode iNode;
    std::vector<cnetpp::base::StringPiece> path_components;
    ASSERT_EQ(kOK, meta_storage->GetINode(elem.first, &iNode));
    ASSERT_TRUE(SplitPath(elem.second, &path_components));
    ASSERT_EQ(iNode.name(), path_components.back());
  }

  uint64_t next_snapshot_id;
  DANCENN_ASSERT_OK(temp.AcquireSnapshotID(&next_snapshot_id));
  ASSERT_EQ(4, next_snapshot_id);
}

}  // namespace dancenn
