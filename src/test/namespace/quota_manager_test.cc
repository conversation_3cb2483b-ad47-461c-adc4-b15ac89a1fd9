// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/01
// Description

#include "namespace/quota_manager.h"

#include <absl/strings/match.h>
#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <cstdint>
#include <limits>
#include <memory>
#include <vector>

#include "base/constants.h"
#include "base/java_exceptions.h"
#include "base/status.h"
#include "namespace/namespace_scrub_inodestat.h"
#include "namespace/xattr.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "test/namespace/inode.h"

DECLARE_int32(quota_expire_interval_sec_soft_limit);
DECLARE_int32(quota_expire_interval_sec_hard_limit);
DECLARE_int32(quota_expire_txid_gap_soft_limit);
DECLARE_int32(quota_expire_txid_gap_hard_limit);
DECLARE_int32(max_quota_batch_size_to_update);
DECLARE_int32(quotamap_num_slice);
DECLARE_int32(quotamap_num_element_each_slice);

using testing::_;
using testing::DoAll;
using testing::ElementsAre;
using testing::Property;
using testing::Return;
using testing::SetArgPointee;

namespace dancenn {

TEST(QuotaMapSliceTest, GetAndSet) {
  QuotaMapSlice slice;
  slice.Set(1,
            Quota{.last_update_sec = 0,
                  .snapshot_txid = 0,
                  .inode_stat = INodeStat(1)});
  Quota quota;
  EXPECT_TRUE(slice.Get(1, &quota));
  EXPECT_EQ(quota.inode_stat.inode_id, 1);
}

TEST(QuotaMapSliceTest, EvictIfFull) {
  QuotaMapSlice slice;
  auto max_inode_id = FLAGS_quotamap_num_element_each_slice + 1;
  for (auto i = 1; i <= max_inode_id; i++) {
    slice.Set(i,
              Quota{.last_update_sec = 0,
                    .snapshot_txid = 0,
                    .inode_stat = INodeStat(i)});
  }
  Quota quota;
  EXPECT_FALSE(slice.Get(1, &quota));
  EXPECT_TRUE(slice.Get(max_inode_id, &quota));
  EXPECT_EQ(quota.inode_stat.inode_id, max_inode_id);
}

TEST(QuotaMapSliceTest, DontReorderIfSetExistedQuota) {
  QuotaMapSlice slice;
  for (auto i = 1; i <= 3; i++) {
    slice.Set(i,
              Quota{.last_update_sec = 0,
                    .snapshot_txid = 0,
                    .inode_stat = INodeStat(i)});
  }
  slice.Set(2,
            Quota{.last_update_sec = 0,
                  .snapshot_txid = 0,
                  .inode_stat = INodeStat(2)});
  cloudfs::GetINodeStatRequestProto request;
  slice.GetExpiredQuotas(std::numeric_limits<int64_t>::max(),
                         std::numeric_limits<TxID>::max(),
                         std::numeric_limits<int>::max(),
                         &request);
  EXPECT_EQ(request.inodeids_size(), 3);
  EXPECT_EQ(request.inodeids(0), 3);
  EXPECT_EQ(request.inodeids(1), 2);
  EXPECT_EQ(request.inodeids(2), 1);
}

TEST(QuotaMapSliceTest, ReorderIfGetQuota) {
  QuotaMapSlice slice;
  for (auto i = 1; i <= 3; i++) {
    slice.Set(i,
              Quota{.last_update_sec = 0,
                    .snapshot_txid = 0,
                    .inode_stat = INodeStat(i)});
  }
  Quota quota;
  slice.Get(2, &quota);
  cloudfs::GetINodeStatRequestProto request;
  slice.GetExpiredQuotas(std::numeric_limits<int64_t>::max(),
                         std::numeric_limits<TxID>::max(),
                         std::numeric_limits<int>::max(),
                         &request);
  EXPECT_EQ(request.inodeids_size(), 3);
  EXPECT_EQ(request.inodeids(0), 2);
  EXPECT_EQ(request.inodeids(1), 3);
  EXPECT_EQ(request.inodeids(2), 1);
}

class GMockQuotaClient : public QuotaClient {
 public:
  MOCK_METHOD2(GetQuotas,
               bool(const cloudfs::GetINodeStatRequestProto& request,
                    cloudfs::GetINodeStatResponseProto* response));
};

class GMockQuotaManager : public QuotaManager {
 public:
  GMockQuotaManager(std::shared_ptr<QuotaClient> quota_client)
      : QuotaManager(quota_client) {
  }

  MOCK_METHOD0(GetCurrentTsInSec, int64_t());
};

class QuotaManagerTest : public testing::Test {
 public:
  void SetUp() override {
    cli_ = std::make_shared<GMockQuotaClient>();
    mgr_ = std::make_unique<GMockQuotaManager>(
        std::static_pointer_cast<QuotaClient>(cli_));

    PermissionStatus permission = PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("tiger")
                                      .SetPermission(777)
                                      .Build();
    a_ = INodeBuilder().SetId(16386).SetParentId(16385).SetName("a").SetType(
        INode::kDirectory);
    b_ = INodeBuilder().SetId(16387).SetParentId(16386).SetName("b").SetType(
        INode::kDirectory);
    c_ = INodeBuilder().SetId(16388).SetParentId(16387).SetName("c").SetType(
        INode::kDirectory);

    zero_inode_stat_.set_inodenum(0);
    zero_inode_stat_.set_filenum(0);
    zero_inode_stat_.set_dirnum(0);
    zero_inode_stat_.set_blocknum(0);
    zero_inode_stat_.set_datasize(0);
  }

 protected:
  std::shared_ptr<GMockQuotaClient> cli_;
  std::unique_ptr<GMockQuotaManager> mgr_;
  // /a/b/c
  INodeBuilder a_;
  INodeBuilder b_;
  INodeBuilder c_;
  cloudfs::GetINodeStatResponseProto::INodeStat zero_inode_stat_;
};

class QuotaManagerCheckInternal : public QuotaManagerTest {};

TEST_F(QuotaManagerCheckInternal, PassWhenNoQuotaRule) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).Times(1).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_, GetQuotas(_, _)).Times(0);
  std::vector<INode> ancestors{a_.Build(), b_.Build(), c_.Build()};
  auto s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(1, 1, 1), 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "NoQuotaPolicy");
}

TEST_F(QuotaManagerCheckInternal, PassWhenLossConnectionWithObserver) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec())
      .Times(2)
      .WillOnce(Return(FLAGS_quota_expire_interval_sec_hard_limit + 1))
      .WillOnce(Return(0));
  std::vector<INode> ancestors{
      c_.SetXAttr(kQuotaPolicyXAttr,
                  QuotaPolicyBuilder().SetDataSizeLimit(0).Build())
          .Build()};
  auto s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(0, 0, 128), 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "LossConnWithObByTs");
  s = mgr_->CheckCreateOrAdd(ancestors,
                             QuotaDelta(0, 0, 128),
                             FLAGS_quota_expire_txid_gap_hard_limit + 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "LossConnWithObByTxId");
}

TEST_F(QuotaManagerCheckInternal, RefreshSyncIfSrcOrDstINodeStatMiss) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_,
              GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                 ElementsAre(/*src_inode=*/16389,
                                             /*old_dst_inode=*/16390,
                                             /*a=*/16386,
                                             /*c=*/16388)),
                        _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(
                    GetINodeStatResponseBuilder()
                        .AddINodeStat(INodeStatBuilder()
                                          .SetINodeId(/*src_inode=*/16389)
                                          .SetDataSize(1025)
                                          .Build())
                        .AddINodeStat(INodeStatBuilder()
                                          .SetINodeId(/*old_dst_inode=*/16390)
                                          .SetDataSize(0)
                                          .Build())
                        .AddINodeStat(INodeStatBuilder()
                                          .SetINodeId(/*a=*/16386)
                                          .SetINodeNum(4)
                                          .SetDirNum(3)
                                          .SetFileNum(1)
                                          .Build())
                        .AddINodeStat(INodeStatBuilder()
                                          .SetINodeId(/*c=*/16388)
                                          .SetINodeNum(2)
                                          .SetDirNum(1)
                                          .SetFileNum(1)
                                          .Build())
                        .Build()),
                Return(true)));
  std::vector<INode> ancestors{
      a_.SetXAttr(kQuotaPolicyXAttr,
                  QuotaPolicyBuilder().SetFileLimit(1).Build())
          .Build(),
      b_.Build(),
      c_.SetXAttr(kQuotaPolicyXAttr,
                  QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
          .Build()};
  INode src_inode =
      INodeBuilder().SetId(16389).SetType(INode::kDirectory).Build();
  INode old_dst_inode =
      INodeBuilder().SetId(16390).SetType(INode::kDirectory).Build();
  auto s = mgr_->CheckRename(ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "c is exceeded: policy=dataSize"));
}

TEST_F(QuotaManagerCheckInternal, PassIfRefreshMissingSrcDstINodeStatFailed) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_,
              GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                 ElementsAre(/*src_inode=*/16389,
                                             /*old_dst_inode=*/16390,
                                             /*a=*/16386,
                                             /*c=*/16388)),
                        _))
      .Times(1)
      .WillOnce(Return(false));
  std::vector<INode> ancestors{
      a_.SetXAttr(kQuotaPolicyXAttr,
                  QuotaPolicyBuilder().SetFileLimit(1).Build())
          .Build(),
      b_.Build(),
      c_.SetXAttr(kQuotaPolicyXAttr,
                  QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
          .Build()};
  INode src_inode =
      INodeBuilder().SetId(16389).SetType(INode::kDirectory).Build();
  INode old_dst_inode =
      INodeBuilder().SetId(16390).SetType(INode::kDirectory).Build();
  auto s = mgr_->CheckRename(ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "SrcOrOldDstQuotaNotFound");
}

TEST_F(QuotaManagerCheckInternal, RefreshSyncIfAncestorINodeStatMiss) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_,
              GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                 ElementsAre(/*a=*/16386)),
                        _))
      .Times(1)
      .WillOnce(DoAll(
          SetArgPointee<1>(GetINodeStatResponseBuilder()
                               .AddINodeStat(INodeStatBuilder()
                                                 .SetINodeId(/*a=*/16386)
                                                 .SetDataSize(1024 - 128 + 1)
                                                 .Build())
                               .Build()),
          Return(true)));
  auto s = mgr_->CheckCreateOrAdd(
      std::vector<INode>{
          a_.SetXAttr(kQuotaPolicyXAttr,
                      QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
              .Build()},
      QuotaDelta(0, 0, 128),
      1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "of a is exceeded"));
}

TEST_F(QuotaManagerCheckInternal, PassIfRefreshMissingAncestorINodeStatFailed) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_,
              GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                 ElementsAre(
                                     /*a=*/16386)),
                        _))
      .Times(1)
      .WillOnce(Return(false));
  auto s = mgr_->CheckCreateOrAdd(
      std::vector<INode>{
          a_.SetXAttr(kQuotaPolicyXAttr,
                      QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
              .Build()},
      QuotaDelta(0, 0, 128),
      1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "");
}

TEST_F(QuotaManagerCheckInternal, RefreshAsyncIfAncestorINodeStatMiss) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  mgr_->TestOnlySetQuota(
      Quota{.last_update_sec = 1,
            .snapshot_txid = 1,
            .inode_stat = INodeStat(INodeStatBuilder()
                                        .SetINodeId(/*a=*/16386)
                                        .SetDataSize(1024 - 128 + 1)
                                        .Build())});
  auto s = mgr_->CheckCreateOrAdd(
      std::vector<INode>{
          a_.SetXAttr(kQuotaPolicyXAttr,
                      QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
              .Build(),
          b_.Build(),
          c_.SetXAttr(kQuotaPolicyXAttr,
                      QuotaPolicyBuilder().SetDataSizeLimit(1024).Build())
              .Build()},
      QuotaDelta(0, 0, 128),
      1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "of a is exceeded"));
  Quota c_quota;
  EXPECT_FALSE(mgr_->TestOnlyGetQuota(/*c=*/16388, &c_quota));

  EXPECT_CALL(*cli_,
              GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                 ElementsAre(
                                     /*c=*/16388)),
                        _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(GetINodeStatResponseBuilder()
                                     .AddINodeStat(INodeStatBuilder()
                                                       .SetINodeId(/*c=*/16388)
                                                       .SetINodeNum(4)
                                                       .Build())
                                     .Build()),
                Return(true)));
  mgr_->SendHeartbeat(1);
  EXPECT_TRUE(mgr_->TestOnlyGetQuota(/*c=*/16388, &c_quota));
  EXPECT_EQ(c_quota.inode_stat.inode_id, 16388);
}

class QuotaManagerSatisfyQuotaPolicyTest : public QuotaManagerTest {};

TEST_F(QuotaManagerSatisfyQuotaPolicyTest, Test01) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  mgr_->TestOnlySetQuota(
      Quota{.last_update_sec = 1,
            .snapshot_txid = 1,
            .inode_stat = INodeStat(INodeStatBuilder()
                                        .SetINodeId(/*a=*/16386)
                                        .SetINodeNum(200)
                                        .SetDirNum(100)
                                        .SetFileNum(100)
                                        .SetDataSize(1024)
                                        .Build())});
  std::vector<INode> ancestors{a_.SetXAttr(kQuotaPolicyXAttr,
                                           QuotaPolicyBuilder()
                                               .SetINodeLimit(400)
                                               .SetDirLimit(200)
                                               .SetFileLimit(200)
                                               .SetDataSizeLimit(2048)
                                               .Build())
                                   .Build()};
  auto s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(99, 99, 1023), 1);
  EXPECT_TRUE(s.IsOK());

  s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(100, 100, 1023), 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "policy=inode"));

  s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(100, 99, 1023), 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "policy=dir"));

  s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(99, 100, 1023), 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "policy=file"));

  s = mgr_->CheckCreateOrAdd(ancestors, QuotaDelta(99, 99, 1024), 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "policy=dataSize"));
}

class QuotaManagerSendHeartbeat : public QuotaManagerTest {};

TEST_F(QuotaManagerSendHeartbeat, SendHeartbeat01) {
  FLAGS_max_quota_batch_size_to_update = 5;
  FLAGS_quotamap_num_slice = 4;
  FLAGS_quotamap_num_element_each_slice = 5;
  QuotaManagerTest::SetUp();

  EXPECT_CALL(*mgr_, GetCurrentTsInSec())
      .WillRepeatedly(Return(FLAGS_quota_expire_interval_sec_soft_limit));
  {
    testing::InSequence s;
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre(16, 12, 8, 4, 0)),
                          _))
        .Times(1)
        .WillOnce(DoAll(
            SetArgPointee<1>(
                GetINodeStatResponseBuilder()
                    .AddINodeStat(INodeStatBuilder().SetINodeId(16).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(12).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(8).Build())
                    .SetUpdateTsInSec(
                        FLAGS_quota_expire_interval_sec_soft_limit)
                    .SetSnapshotTxId(20)
                    .Build()),
            Return(true)));
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre(17, 13, 9, 5, 1)),
                          _))
        .Times(1)
        .WillOnce(DoAll(
            SetArgPointee<1>(
                GetINodeStatResponseBuilder()
                    .AddINodeStat(INodeStatBuilder().SetINodeId(17).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(13).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(9).Build())
                    .SetUpdateTsInSec(
                        FLAGS_quota_expire_interval_sec_soft_limit)
                    .SetSnapshotTxId(20)
                    .Build()),
            Return(true)));
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre(4, 0, 5, 1)),
                          _))
        .Times(1)
        .WillOnce(DoAll(
            SetArgPointee<1>(
                GetINodeStatResponseBuilder()
                    .AddINodeStat(INodeStatBuilder().SetINodeId(4).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(0).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(5).Build())
                    .AddINodeStat(INodeStatBuilder().SetINodeId(1).Build())
                    .SetUpdateTsInSec(
                        FLAGS_quota_expire_interval_sec_soft_limit)
                    .SetSnapshotTxId(30)
                    .Build()),
            Return(true)));
  }
  for (INodeID id : std::vector<INodeID>{0, 4, 8, 12, 16, 1, 5, 9, 13, 17}) {
    mgr_->TestOnlySetQuota(Quota{
        .last_update_sec = 0, .snapshot_txid = 0, .inode_stat = INodeStat(id)});
  }
  mgr_->SendHeartbeat(0);
  mgr_->SendHeartbeat(0);
  mgr_->SendHeartbeat(0);

  for (INodeID id : std::vector<INodeID>{8, 12, 16, 9, 13, 17}) {
    Quota quota;
    mgr_->TestOnlyGetQuota(id, &quota);
    EXPECT_EQ(quota.last_update_sec,
              FLAGS_quota_expire_interval_sec_soft_limit);
    EXPECT_EQ(quota.inode_stat.inode_id, id);
    EXPECT_EQ(quota.snapshot_txid, 20);
  }
  for (INodeID id : std::vector<INodeID>{4, 0, 5, 1}) {
    Quota quota;
    mgr_->TestOnlyGetQuota(id, &quota);
    EXPECT_EQ(quota.last_update_sec,
              FLAGS_quota_expire_interval_sec_soft_limit);
    EXPECT_EQ(quota.inode_stat.inode_id, id);
    EXPECT_EQ(quota.snapshot_txid, 30);
  }
  EXPECT_EQ(mgr_->GetCurrentTsInSec(),
            FLAGS_quota_expire_interval_sec_soft_limit);
  EXPECT_EQ(mgr_->TestOnlyGetLastHeartbeatTxId(), 30);
}

TEST_F(QuotaManagerSendHeartbeat, SendHeartbeat02) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec())
      .WillRepeatedly(Return(FLAGS_quota_expire_interval_sec_soft_limit));
  mgr_->UpdateQuotasAsync(std::vector<INodeID>{1, 2}, 20);
  {
    testing::InSequence s;
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre(2, 1)),
                          _))
        .Times(1)
        .WillOnce(DoAll(
            SetArgPointee<1>(
                GetINodeStatResponseBuilder()
                    .AddINodeStat(INodeStatBuilder().SetINodeId(1).Build())
                    .SetUpdateTsInSec(
                        FLAGS_quota_expire_interval_sec_soft_limit)
                    .SetSnapshotTxId(20)
                    .Build()),
            Return(true)));
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre(2)),
                          _))
        .Times(2)
        .WillOnce(DoAll(
            SetArgPointee<1>(GetINodeStatResponseBuilder()
                                 .AddMissingINodeId(2)
                                 .SetUpdateTsInSec(
                                     FLAGS_quota_expire_interval_sec_soft_limit)
                                 .SetSnapshotTxId(19)
                                 .Build()),
            Return(true)))
        .WillOnce(DoAll(
            SetArgPointee<1>(GetINodeStatResponseBuilder()
                                 .AddMissingINodeId(2)
                                 .SetUpdateTsInSec(
                                     FLAGS_quota_expire_interval_sec_soft_limit)
                                 .SetSnapshotTxId(20)
                                 .Build()),
            Return(true)));
    EXPECT_CALL(*cli_,
                GetQuotas(Property(&cloudfs::GetINodeStatRequestProto::inodeids,
                                   ElementsAre()),
                          _))
        .Times(1)
        .WillOnce(Return(true));
  }
  mgr_->SendHeartbeat(0);
  mgr_->SendHeartbeat(0);
  mgr_->SendHeartbeat(0);
  mgr_->SendHeartbeat(0);
}

class QuotaManagerCheckRename : public QuotaManagerTest {};

TEST_F(QuotaManagerCheckRename, Test01) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_, GetQuotas(_, _)).Times(0);

  std::vector<INode> dst_ancestors{
      INodeBuilder()
          .SetId(16386)
          .SetName("a")
          .SetType(INode::kDirectory)
          .SetXAttr(kQuotaPolicyXAttr, QuotaPolicyBuilder().Build())
          .Build()};
  Quota a_quota;
  a_quota.last_update_sec = 1;
  a_quota.snapshot_txid = 1;
  a_quota.inode_stat.inode_id = 16386;
  a_quota.inode_stat.inode_num = 50;
  a_quota.inode_stat.dir_num = 30;
  a_quota.inode_stat.file_num = 20;
  a_quota.inode_stat.data_size = 2048;
  mgr_->TestOnlySetQuota(a_quota);

  INode src_inode =
      INodeBuilder().SetId(16387).SetType(INode::kDirectory).Build();
  Quota src_quota;
  src_quota.last_update_sec = 1;
  src_quota.snapshot_txid = 1;
  src_quota.inode_stat.inode_id = 16387;
  src_quota.inode_stat.inode_num = 20;
  src_quota.inode_stat.dir_num = 10;
  src_quota.inode_stat.file_num = 10;
  src_quota.inode_stat.data_size = 2048;
  mgr_->TestOnlySetQuota(src_quota);

  INode old_dst_inode =
      INodeBuilder().SetId(16388).SetType(INode::kDirectory).Build();
  Quota old_dst_quota;
  old_dst_quota.last_update_sec = 1;
  old_dst_quota.snapshot_txid = 1;
  old_dst_quota.inode_stat.inode_id = 16388;
  old_dst_quota.inode_stat.inode_num = 10;
  old_dst_quota.inode_stat.dir_num = 5;
  old_dst_quota.inode_stat.file_num = 5;
  old_dst_quota.inode_stat.data_size = 1024;
  mgr_->TestOnlySetQuota(old_dst_quota);

  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetINodeLimit(61).Build().SerializeAsString();
  Status s = mgr_->CheckRename(dst_ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_TRUE(s.message().empty());
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=inode"));

  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetDirLimit(36).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_TRUE(s.message().empty());
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=dir"));

  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetFileLimit(26).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_TRUE(s.message().empty());
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=file"));

  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetDataSizeLimit(3073).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, &old_dst_inode, 1);
  EXPECT_TRUE(s.IsOK());
  EXPECT_TRUE(s.message().empty());
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=dataSize"));
}

TEST_F(QuotaManagerCheckRename, UseMinimalINodeStatWhenSrcINodeStatNotFound) {
  EXPECT_CALL(*mgr_, GetCurrentTsInSec()).WillRepeatedly(Return(0));
  EXPECT_CALL(*cli_, GetQuotas(_, _)).WillRepeatedly(Return(false));

  std::vector<INode> dst_ancestors{
      INodeBuilder()
          .SetId(16386)
          .SetName("a")
          .SetType(INode::kDirectory)
          .SetXAttr(kQuotaPolicyXAttr, QuotaPolicyBuilder().Build())
          .Build()};
  Quota a_quota;
  a_quota.last_update_sec = 1;
  a_quota.snapshot_txid = 1;
  a_quota.inode_stat.inode_id = 16386;
  a_quota.inode_stat.inode_num = 50;
  a_quota.inode_stat.dir_num = 30;
  a_quota.inode_stat.file_num = 20;
  a_quota.inode_stat.data_size = 2048;
  mgr_->TestOnlySetQuota(a_quota);

  INode src_inode =
      INodeBuilder().SetId(16387).SetType(INode::kDirectory).Build();
  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetINodeLimit(51).Build().SerializeAsString();
  Status s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=inode"));

  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetDirLimit(31).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=dir"));

  src_inode = INodeBuilder().SetId(16387).SetType(INode::kFile).Build();
  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetFileLimit(21).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kNSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=file"));

  src_inode.add_blocks()->set_numbytes(1);
  *dst_ancestors[0].mutable_xattrs(0)->mutable_value() =
      QuotaPolicyBuilder().SetDataSizeLimit(2049).Build().SerializeAsString();
  s = mgr_->CheckRename(dst_ancestors, &src_inode, nullptr, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kDSQuotaExceededException);
  EXPECT_TRUE(absl::StrContains(s.message(), "a is exceeded: policy=dataSize"));
}

}  // namespace dancenn
