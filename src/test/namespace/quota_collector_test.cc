// Copyright 2019 <PERSON><PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "ClientNamenodeProtocol.pb.h"  // NOLINT(build/include)
#include "base/file_utils.h"
#include "namespace/quota_collector.h"
#include "test/namespace/mock_namespace.h"
#include "namespace/replica_policy_cache.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/data_centers.h"
#include "mock_edit_log_context.h"
#include "mock_edit_log_sender.h"
#include "mock_ha_state.h"
#include "mock_safe_mode.h"
#include "base/redis_manager.h"

DECLARE_bool(client_replication_support);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_string(dancenn_quota_team_mapping_file);
DECLARE_string(dancenn_quota_redis_backends);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_int32(namespace_type);


namespace dancenn {

#if 0
namespace {

LogRpcInfo default_rpc_info("", 0);
auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;

}  // namespace

class MockQuotaRedisManager : public IRedisManager {
 public:
  ~MockQuotaRedisManager() {}
  bool Init(const std::string& backends, int timeout_ms) override {
    (void) backends; (void) timeout_ms;
    return true;
  }
  void Stop() override {}
  bool IsInited() const override { return true; }
  bool RedisHGetAll(const std::string& key,
      std::unordered_map<std::string, std::string>* result) override {
      LOG(INFO) << "mock redis HGETALL";
    result->emplace(std::string(".REDIS_START_FLAG"), std::string("123"));
    result->emplace(std::string(".REDIS_DONE_FLAG"), std::string("123"));
    result->emplace(std::string("/a1"), std::string("dev"));
    result->emplace(std::string("/a4"), std::string("ssd_user"));
    result->emplace(std::string("/a5"), std::string("ec_user"));
    return true;
  }

  bool RedisHKeys(const std::string& key,
      std::unordered_set<std::string>* result) override {
    (void) key;
    abort();
    return false;
  }

  bool RedisSMembers(const std::string& key,
      std::unordered_set<std::string>* result) override {
    LOG(INFO) << "mock redis SMEMBERS";
    result->emplace(std::string("/a1/b"));
    return true;
  }

  bool RedisGet(const std::string& key, std::string* result) override {
    (void) key; (void) result;
    abort();
    return false;
  }

  bool RedisHSet(const std::string& key, const std::string& field,
      const std::string& value) override {
    LOG(INFO) << "mock redis HSET key: " << key << " field: " << field
        << " value: " << value;
    auto itr = usage_result_.find(key);
    if (itr == usage_result_.end()) {
      std::unordered_map<std::string, std::string> usage;
      usage.emplace(field, value);
      usage_result_.emplace(key, usage);
    } else {
      auto itr_usage = itr->second.find(field);
      if (itr_usage == itr->second.end()) {
        itr->second.emplace(field, value);
      } else {
        return false;
      }
    }
    return true;
  }

  bool RedisSAdd(const std::string& key, const std::string& value) override {
    ssd_dirs_.emplace(value);
    LOG(INFO) << "mock redis SADD key: " << key << "value: " << value;
    return true;
  }

  bool RedisSet(const std::string& key, const std::string& value) override {
    LOG(INFO) << "mock redis SET key: " << key << "value: " << value;
    timestamp_ = value;
    return true;
  }

  bool GetUsageString(const std::string& key, const std::string& field, 
      std::string& usage_str) {
    auto itr = usage_result_.find(key);
    if (itr == usage_result_.end()) {
      return false;
    } else {
      auto itr_usage = itr->second.find(field);
      if (itr_usage == itr->second.end()) {
        return false;
      }
      usage_str = itr_usage->second;
    }
    return true;
  }

  bool IsSsdDir(const std::string& key) {
    return !(ssd_dirs_.find(key) == ssd_dirs_.end());
  }
      
 private:
  ReadWriteLock rwlock_;
  std::unordered_map<std::string, std::unordered_map<std::string, std::string>> usage_result_;
  std::unordered_set<std::string> ssd_dirs_;
  std::shared_ptr<MockQuotaRedisManager> redis_;
  std::string timestamp_;
  
};

class QuotaCollectorTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_client_replication_support = true;
    FLAGS_lease_expired_soft_limit_ms = 500;
    FLAGS_lease_expired_hard_limit_ms = 1000;
    FLAGS_datanode_keep_alive_timeout_sec = 2000;
    FLAGS_dancenn_quota_redis_backends = "xxx";
    FLAGS_bytecool_feature_enabled = true;
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    //start a namenode
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    datanode_manager_ = std::make_shared<DatanodeManager>();
    block_manager_.reset(new BlockManager());
    auto edit_log_ctx = CreateContext();
    MockFSImageTransfer(db_path_).Transfer();
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                UfsEnv::Create()));
    ha_state_ = std::make_unique<MockHAState>();
    safemode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    block_manager_->set_ha_state(ha_state_.get());
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->Start();
    ns_->StartActive();

    // add a datanode to the cluster
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    auto reg_ssd =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg_ssd.mutable_datanodeid()->set_datanodeuuid("datanode2");
    cnetpp::base::IPAddress ip_ssd("***********");
    datanode_manager_->Register(reg_ssd.datanodeid(), &reg_ssd, ip_ssd);

    StartHeartbeat();

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender =
        std::unique_ptr<EditLogSenderBase>(
            new MockEditLogSender(edit_log_ctx, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));

    //create mapping file
    FLAGS_dancenn_quota_team_mapping_file = "./pattern_team_mapping";
    cnetpp::base::Array configs;
    cnetpp::base::Object config1;
    config1["team"] = "sre";
    config1["patternString"] = "/tmp";
    configs.Append(cnetpp::base::Value(config1));

    cnetpp::base::Object config2;
    config2["team"] = "byte_hive_staging";
    config2["patternString"] = "/user/tiger/warehouse/.hive-staging";
    configs.Append(cnetpp::base::Value(config2));

    cnetpp::base::Parser parser;
    std::string json;
    parser.Serialize(cnetpp::base::Value(std::move(configs)), &json);
    std::ofstream out(FLAGS_dancenn_quota_team_mapping_file);
    out.write(&(json[0]), json.size());
    out.close();
  }

  void TearDown() override {
    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;

    stop_ = true;
    heartbeat_thread_.join();
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
    FileUtils::DeleteFile(FLAGS_dancenn_quota_team_mapping_file);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  CreateRequestProto MakeCreateRequest() {
    CreateRequestProto create_request;
    create_request.set_src("");
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(false);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  void MakeReport(
      uint64_t block_id,
      uint64_t gs,
      uint32_t len,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
  }

  void AddFile(const std::string& path,
               uint64_t len,
               uint32_t replica,
               bool need_complete,
               std::string datanode,
               AddBlockResponseProto* add_response,
               CreateResponseProto* create_response) {
    auto create_request = MakeCreateRequest();
    create_request.set_replication(replica);
    PermissionStatus p;
    ASSERT_TRUE(!ns_->CreateFile(path, p, create_request,
                                 create_response).HasException());

    cnetpp::base::IPAddress client_ip("***********");
    auto add_request = MakeAddBlockRequest();
    auto sss = ns_->AddBlock(path, client_ip, add_request, default_rpc_info,
                               add_response);
    LOG(INFO) << sss.ExceptionStr();
    LOG(INFO) << sss.message();
    ASSERT_TRUE(!sss.HasException());

    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response->block().b().blockid(),
               add_response->block().b().generationstamp(),
               len,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
               &report);
    block_manager_->IncrementalBlockReport(datanode, report);

    if (need_complete) {
      report.Clear();
      MakeReport(
          add_response->block().b().blockid(),
          add_response->block().b().generationstamp(),
          len,
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &report);
      block_manager_->IncrementalBlockReport(datanode, report);

      ExtendedBlockProto last_blk;
      last_blk.set_poolid(add_response->block().b().poolid());
      last_blk.set_blockid(add_response->block().b().blockid());
      last_blk.set_generationstamp(add_response->block().b().generationstamp());
      last_blk.set_numbytes(len);
      CompleteRequestProto complete_request;
      complete_request.set_src(path);
      complete_request.set_clientname("client");
      complete_request.mutable_last()->CopyFrom(last_blk);
      ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
    }
  }

  Status SetAttr(const std::string& path, const XAttrProto& x) {
    return ns_->SetXAttr(path, x, true, false);
  }

  void StartHeartbeat() {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&latch, this]() {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
      cnetpp::base::IPAddress ip("***********");
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);     
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);

      auto reg_ssd =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg_ssd.mutable_datanodeid()->set_datanodeuuid("datanode2");
      cnetpp::base::IPAddress ip_ssd("***********");
      RepeatedStorageReport reports_ssd;
      auto r_ssd = reports_ssd.Add();
      r_ssd->set_storageuuid("storage2_1");
      r_ssd->mutable_storage()->set_storagetype(StorageTypeProto::SSD);
      r_ssd->mutable_storage()->set_storageuuid("storage2_1");
      HeartbeatRequestProto request_ssd;
      request_ssd.mutable_registration()->CopyFrom(reg_ssd);
      request_ssd.mutable_reports()->CopyFrom(reports_ssd);

      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          DatanodeManager::RepeatedCmds cmds;
          datanode_manager_->Heartbeat(request, &cmds);

          DatanodeManager::RepeatedCmds cmds_ssd;
          datanode_manager_->Heartbeat(request_ssd, &cmds_ssd);
          if (!heartbeated) {
            latch.CountDown();
            heartbeated = true;
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  bool stop_;
  bool pause_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::unique_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  std::shared_ptr<MockQuotaRedisManager> redis_;
};

TEST_F(QuotaCollectorTest, Test01) {
  // Mock the following directory tree:
  //
  // /
  //  /a1                     dir of dev(team)
  //     /b                   migrating dir
  //       /c1.txt            len: 10, replica:1
  //     /b1.txt              len: 10, replica:1
  //     /b2.txt              len: 10, replica:1
  //     /ssd_dir             dir of ALL_SSD policy
  //       /ssd_file.txt      len: 20, replica:1 of ALL_SSD policy 
  //  /tmp                    mapping dir, mapping to sre
  //     /b3.txt              len: 150000000, replica:1
  //  /a3                     default dir
  //     /b4.txt              len: 300000000, replica:1
  //  /a4                     dir of ssd_user(team) of ONLY_SSD policy
  //     /b5.txt              len:10, replica:1 if ONLY_SSD policy
  //  /a5                     dir of ec_user(team)
  //     /b6.txt              len:1000, replica:(20+4)/20
  //  /user                   default dir
  //     /tiger
  //       /.Trash
  //         /814800          trash path
  //           /trash.txt        len: 10, replica:1
  
  LOG(INFO) << "make dir";
  ASSERT_TRUE(!ns_->MkDirs("/a1/b", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a1/ssd_dir", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->SetStoragePolicy("/a1/ssd_dir", "ALL_SSD")
      .HasException());
  ASSERT_TRUE(!ns_->MkDirs("/tmp", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a3", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a4", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->SetStoragePolicy("/a4", "ONLY_SSD")
      .HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a5", PermissionStatus(),
      true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/user/tiger/.Trash/814800", PermissionStatus(),
      true).HasException());

  LOG(INFO) << "add files";
  AddBlockResponseProto arsp;
  CreateResponseProto crsp;
  crsp.Clear();
  AddFile("/a1/b/c1.txt", 10, 1, true, "datanode1", &arsp, &crsp);
  crsp.Clear();
  AddFile("/a1/b1.txt", 10, 1, true, "datanode1", &arsp, &crsp);
  crsp.Clear();
  AddFile("/a1/b2.txt", 10, 1, true, "datanode1", &arsp, &crsp);
  crsp.Clear();
  AddFile("/a1/ssd_dir/ssd_file.txt", 20, 1, true, "datanode2", &arsp, &crsp);
  ASSERT_TRUE(!ns_->SetStoragePolicy("/a1/ssd_dir/ssd_file.txt", "ALL_SSD")
      .HasException());
  crsp.Clear();  
  AddFile("/tmp/b3.txt", 150000000, 1, true, "datanode1", &arsp, &crsp);
  crsp.Clear();
  AddFile("/a3/b4.txt", 300000000, 1, true, "datanode1", &arsp, &crsp);
  crsp.Clear();
  AddFile("/a4/b5.txt", 10, 1, true, "datanode2", &arsp, &crsp);
  ASSERT_TRUE(!ns_->SetStoragePolicy("/a4/b5.txt", "ONLY_SSD")
      .HasException());
  AddFile("/a5/b6.txt", 1000, 1, true, "datanode1", &arsp, &crsp);
  auto status = ns_->SetReplication("/a5/b6.txt", 0, true);
  ASSERT_FALSE(status.HasException());
  XAttrProto x;
  x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_TRUSTED);
  x.set_name("ec");
  uint8_t value = 164; // n = 20, m = 4 (20 << 3 + 4)
  x.set_value(&value, 1);
  ASSERT_EQ(SetAttr("/a5/b6.txt", x).code(), Code::kOK);
  AddFile("/user/tiger/.Trash/814800/trash.txt", 10, 1, true, "datanode1", &arsp, &crsp);

  QuotaCollector collector(ns_.get());
  redis_ = std::make_shared<MockQuotaRedisManager>();
  collector.SetupRedisManager(redis_);
  collector.Start();
  std::this_thread::sleep_for(std::chrono::seconds(2));

  // ASSERT_EQ(inode_info_size, inode_info.size());
  // ASSERT_EQ(inode_info["/"].first, 1);
  // ASSERT_EQ(inode_info["/"].second, INode_Type_kDirectory);
  std::string result;
  redis_->GetUsageString("#teamUsage", "dev", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":5,\"sata\":20,\"sataUsage\":268435456,\"ssd\":20,\"ssdUsage\":134217728}");
  redis_->GetUsageString("#teamUsage", "sre", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":2,\"sata\":150000000,\"sataUsage\":268435456,\"ssd\":0,\"ssdUsage\":0}");
  redis_->GetUsageString("#teamUsage", "ssd_user", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":2,\"sata\":0,\"sataUsage\":0,\"ssd\":10,\"ssdUsage\":134217728}"); 
  redis_->GetUsageString("#teamUsage", "#.default", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":5,\"sata\":300000000,\"sataUsage\":300000000,\"ssd\":0,\"ssdUsage\":0}");
  redis_->GetUsageString("#teamUsage", "ec_user", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":2,\"sata\":1200,\"sataUsage\":161061280,\"ssd\":0,\"ssdUsage\":0}");
  redis_->GetUsageString("#pathUsage", "/a1", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":5,\"sata\":20,\"sataUsage\":268435456,\"ssd\":20,\"ssdUsage\":134217728}");
  redis_->GetUsageString("#pathUsage", "/tmp", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":2,\"sata\":150000000,\"sataUsage\":268435456,\"ssd\":0,\"ssdUsage\":0}");
  redis_->GetUsageString("#pathUsage", "/a4", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":2,\"sata\":0,\"sataUsage\":0,\"ssd\":10,\"ssdUsage\":134217728}");
  redis_->GetUsageString("#pathUsage", "#.default", result);
  ASSERT_STREQ(result.c_str(),
      "{\"ns\":5,\"sata\":300000000,\"sataUsage\":300000000,\"ssd\":0,\"ssdUsage\":0}");
  ASSERT_TRUE(redis_->IsSsdDir("/a4"));

  collector.Stop();
}
#endif

}  // namespace dancenn

