// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/01
// Description

#ifndef TEST_NAMESPACE_INODE_H_
#define TEST_NAMESPACE_INODE_H_

#include <google/protobuf/message.h>

#include <cstdint>
#include <string>

#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/inode.pb.h"

namespace dancenn {

class PermissionStatusBuilder {
 public:
  PermissionStatusBuilder& SetUsername(const std::string& username);
  PermissionStatusBuilder& SetGroupname(const std::string& groupname);
  PermissionStatusBuilder& SetPermission(uint32_t permission);
  PermissionStatus Build();

 private:
  PermissionStatus proto_;
};

class FileUnderConstructionFeatureBuilder {
 public:
  FileUnderConstructionFeatureBuilder& SetClientName(
      const std::string& client_name);
  FileUnderConstructionFeatureBuilder& SetClientMachine(
      const std::string& client_machine);
  FileUnderConstructionFeature Build();

 private:
  FileUnderConstructionFeature proto_;
};

class QuotaPolicyBuilder {
 public:
  QuotaPolicyBuilder& SetINodeLimit(uint64_t inode_limit);
  QuotaPolicyBuilder& SetFileLimit(uint64_t file_limit);
  QuotaPolicyBuilder& SetDirLimit(uint64_t dir_limit);
  QuotaPolicyBuilder& SetDataSizeLimit(uint64_t data_size_limit);
  QuotaPolicyProto Build();

 private:
  QuotaPolicyProto proto_;
};

class INodeBuilder {
 public:
  INodeBuilder& SetId(uint64_t id);
  INodeBuilder& SetParentId(uint64_t parent_id);
  INodeBuilder& SetName(const std::string& name);
  INodeBuilder& SetPermission(const PermissionStatus& permission);
  INodeBuilder& SetType(INode::Type type);
  INodeBuilder& SetMtime(uint64_t mtime);
  INodeBuilder& SetAtime(uint64_t atime);
  INodeBuilder& SetStoragePolicyId(uint32_t storage_policy_id);
  INodeBuilder& AddBlock(const cloudfs::BlockProto& block);
  INodeBuilder& SetUc(const FileUnderConstructionFeature& uc);
  INodeBuilder& SetXAttr(const char* name,
                         const google::protobuf::Message& value);
  INodeBuilder& SetStatus(INode::Status status);
  INode Build();

 private:
  INode proto_;
};

class INodeStatBuilder {
 public:
  INodeStatBuilder& SetINodeId(uint64_t inode_id);
  INodeStatBuilder& SetINodeNum(uint64_t inode_num);
  INodeStatBuilder& SetFileNum(uint64_t file_num);
  INodeStatBuilder& SetDirNum(uint64_t dir_num);
  INodeStatBuilder& SetBlockNum(uint64_t block_num);
  INodeStatBuilder& SetDataSize(uint64_t data_size);
  cloudfs::GetINodeStatResponseProto::INodeStat Build();

 private:
  cloudfs::GetINodeStatResponseProto::INodeStat proto_;
};

class GetINodeStatResponseBuilder {
 public:
  GetINodeStatResponseBuilder& AddINodeStat(
      const cloudfs::GetINodeStatResponseProto::INodeStat& stat);
  GetINodeStatResponseBuilder& AddMissingINodeId(uint64_t inode_id);
  GetINodeStatResponseBuilder& SetUpdateTsInSec(int64_t txid);
  GetINodeStatResponseBuilder& SetSnapshotTxId(int64_t txid);
  cloudfs::GetINodeStatResponseProto Build();

 private:
  cloudfs::GetINodeStatResponseProto proto_;
};

}  // namespace dancenn

#endif  // TEST_NAMESPACE_INODE_H_
