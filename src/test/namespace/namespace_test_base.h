#ifndef DANCENN_NAMESPACE_TEST_BASE_H
#define DANCENN_NAMESPACE_TEST_BASE_H

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <memory>
#include <random>

#include "base/file_utils.h"
#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "http/dancenn_admin_handler.h"
#include "http/dancenn_fsck_handler.h"
#include "namespace/create_flag.h"
#include "namespace/lifecycle_policy_util.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_string(all_datacenters);
DECLARE_int32(bg_deletion_process_pending_delete_interval_sec);
DECLARE_uint32(dfs_summary_min_depth);
DECLARE_bool(force_hyperblock_on_diffrent_dn);
DECLARE_bool(namespace_read_full_detail_blocks);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_scanner_enable);
DECLARE_uint32(recycle_bin_scanner_interval_sec);
DECLARE_uint32(recycle_bin_retention_day);
DECLARE_bool(client_replication_support);
DECLARE_bool(permission_enabled);
DECLARE_string(permission_model);
DECLARE_int32(client_slow_rpc_handler_count);
DECLARE_int32(namespace_type);
DECLARE_uint32(lease_monitor_interval_ms);
DECLARE_bool(append_reuse_last_block);
DECLARE_bool(enable_snapshot_feature);
DECLARE_uint32(merge_block_max_candidate_count);
DECLARE_bool(merge_block_support_acc_non_persisted_blocks);
DECLARE_bool(enable_block_info_proto_v2);
DECLARE_bool(placement_ignore_local_az);
DECLARE_bool(placement_ignore_existed_switch);
DECLARE_bool(enable_location_tag);
DECLARE_bool(enable_location_tag_by_rack_aware);
DECLARE_bool(complete_rpc_abandon_last_empty_block);

namespace dancenn {

#define DANCENN_ASSERT_STATUS(_status_expr, _expected_java_exception)     \
  do {                                                                    \
    const Status& _s = (_status_expr);                                    \
    ASSERT_EQ(_expected_java_exception, _s.exception()) << _s.ToString(); \
  } while (0)

#define DANCENN_ASSERT_OK(_status_expr)      \
  do {                                       \
    const Status& _s = (_status_expr);       \
    ASSERT_TRUE(_s.IsOK()) << _s.ToString(); \
  } while (0)

#define DANCENN_EXPECT_OK(_status_expr)      \
  do {                                       \
    const Status& _s = (_status_expr);       \
    EXPECT_TRUE(_s.IsOK()) << _s.ToString(); \
  } while (0)

class NameSpaceTestBase : public testing::Test {
 public:
  void SetUp() override;
  void TearDown() override;

  virtual std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  void AddHyperFile(const std::string& path,
                    const std::vector<int32_t>& hyper_block_size,
                    bool mock_broken = false,
                    int32_t block_num = 1);

  void AddHyperFile(const std::string& path,
                    const std::vector<std::string>& hyper_block_names,
                    uint64_t stripe_width);

  void AddFile(const std::string& path,
               uint64_t len,
               uint32_t replica,
               bool need_complete,
               AddBlockResponseProto* add_response,
               CreateResponseProto* create_response,
               bool create_parent = false,
               uint32_t create_flag = ::cloudfs::CreateFlagProto::CREATE);

  void AppendFile(const std::string& path,
                  uint64_t len,
                  uint32_t replica,
                  bool need_complete,
                  AddBlockResponseProto* add_response,
                  AppendResponseProto* append_response);

  void CreateFileAndVerifyStatus(const std::string& path,
                                 const PermissionStatus& p,
                                 bool is_dir,
                                 const std::string& target_status) {
    if (is_dir) {
      ASSERT_TRUE(!ns_->MkDirs(path, p, /*create parent*/ true).HasException());
      GetFileInfoResponseProto response;
      ASSERT_FALSE(
          ns_->GetFileInfo(
                 path, NetworkLocationInfo(), false, false, &response, ugi_)
              .HasException());
      auto parent_perm = FsPermission(response.fs().permission().perm());
      std::ostringstream ss;
      ss << parent_perm;
      ASSERT_EQ(ss.str(), target_status);
    } else {
      auto create_request = MakeCreateRequest();
      create_request.set_createparent(true);
      CreateResponseProto create_response;
      ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                       .HasException());
      GetFileInfoResponseProto response;
      ASSERT_FALSE(
          ns_->GetFileInfo(
                 path, NetworkLocationInfo(), false, false, &response, ugi_)
              .HasException());
      auto parent_perm = FsPermission(response.fs().permission().perm());
      std::ostringstream ss;
      ss << parent_perm;
      ASSERT_EQ(ss.str(), target_status);
    }
  }

  void VerifyFileWithStatus(const std::string& path,
                            bool target_is_dir,
                            const std::string& target_status,
                            std::string* target_user = nullptr,
                            std::string* target_group = nullptr) {
    GetFileInfoResponseProto response;
    ASSERT_FALSE(
        ns_->GetFileInfo(
               path, NetworkLocationInfo(), false, false, &response, ugi_)
            .HasException());
    auto parent_perm = FsPermission(response.fs().permission().perm());
    std::ostringstream ss;
    ss << parent_perm;
    ASSERT_EQ(ss.str(), target_status);
    ASSERT_EQ(target_is_dir,
              response.fs().filetype() ==
                  cloudfs::HdfsFileStatusProto_FileType_IS_DIR);
  }

  CommitBlockSynchronizationRequestProto GetCommitSyncRequest(
      uint32_t len,
      const AddBlockResponseProto& add_response,
      bool close,
      bool del);

  virtual void HeartbeatOnce(const HeartbeatRequestProto& request) {
    DatanodeManager::RepeatedCmds cmds;
    datanode_manager_->Heartbeat(request, &cmds);
  }

  virtual void StartStandBy() {

  }

  void StartHeartbeat() {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&latch, this]() {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
      cnetpp::base::IPAddress ip("***********");
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);
      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          HeartbeatOnce(request);
          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  void AddDatanode(const std::string& ip, const std::string& uuid) {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(uuid);
    reg.mutable_datanodeid()->set_ipaddr(ip);
    cnetpp::base::IPAddress ip_addr(ip);
    datanode_manager_->Register(reg.datanodeid(), &reg, ip_addr);
    datanode_manager_->RefreshConfig();
    DatanodeManager::RepeatedCmds cmds;
    HeartbeatRequestProto request;
    GetDiskRequest(&request, uuid, reg, 102400);
    datanode_manager_->Heartbeat(request, &cmds);
  }

  void GetDiskRequest(cloudfs::datanode::HeartbeatRequestProto* request,
                      const std::string& uuid,
                      const cloudfs::datanode::DatanodeRegistrationProto& reg,
                      uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid(uuid);
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
    request->mutable_registration()->CopyFrom(reg);
  }

  std::shared_ptr<GetFileInfoResponseProto> GetFileInfoWrp(
      const std::string& path,
      bool has_exp = false) {
    std::shared_ptr<GetFileInfoResponseProto> response =
        std::make_shared<GetFileInfoResponseProto>();
    auto status = ns_->GetFileInfo(path,
                                   NetworkLocationInfo(),
                                   false,
                                   /*resolve_link*/ false,
                                   response.get(),
                                   ugi_);
    CHECK(status.HasException() == has_exp);
    return response;
  }

  void CreateFileWrp(const std::string& path,
                     const std::string& user,
                     const std::string& group,
                     bool parent) {
    auto p = MakePermission();
    p.set_username(user);
    p.set_groupname(group);
    auto create_request = MakeCreateRequest();
    CreateResponseProto response;
    create_request.set_createparent(parent);
    CHECK(!ns_->CreateFile(path, p, create_request, &response).HasException());
  }
  void MkdirsWrp(const std::string& path,
                 const std::string& user,
                 const std::string& group,
                 bool parent) {
    auto p = MakePermission();
    p.set_username(user);
    p.set_groupname(group);
    CHECK(!ns_->MkDirs(path, p, parent).HasException());
  }

  PermissionStatus MakePermission() {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("supergroup");
    p.set_permission(FsPermission::GetFileDefault().ToShort());
    return p;
  }

  AppendRequestProto MakeAppendRequest(const std::string& src,
                                       const std::string& client_name) {
    AppendRequestProto append_request;
    append_request.set_src(src);
    append_request.set_clientname(client_name);
    return append_request;
  }

  CreateRequestProto MakeCreateRequest() {
    CreateRequestProto create_request;
    create_request.set_src("");
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(false);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
  }

  GetBlockLocationsRequestProto MakeGetBlockLocationsRequest(
      const std::string& path) {
    GetBlockLocationsRequestProto req;
    req.set_src(path);
    req.set_offset(0);
    req.set_length(1);
    return req;
  }

  void FillGetAdditionalDatanodeRequest(
      cloudfs::GetAdditionalDatanodeRequestProto* req,
      const std::vector<std::pair<std::string, std::string>> locs,
      uint32_t rep_num) {
    req->set_clientname("client");

    for (auto& it : locs) {
      auto dn = req->mutable_existings()->Add();
      dn->mutable_id()->set_ipaddr(it.first);
      dn->mutable_id()->set_datanodeuuid(it.second);
    }

    req->set_numadditionalnodes(rep_num);
  }

  void MakeUpdatePipelineRequest(
      uint64_t block_id,
      uint64_t old_gs,
      uint64_t new_gs,
      const std::vector<std::pair<std::string, std::string>>& nodes,
      UpdatePipelineRequestProto* req) {
    req->set_clientname("client");
    req->mutable_oldblock()->set_blockid(block_id);
    req->mutable_oldblock()->set_generationstamp(old_gs);
    req->mutable_newblock()->set_blockid(block_id);
    req->mutable_newblock()->set_generationstamp(new_gs);

    for (auto it = nodes.begin(); it != nodes.end(); it++) {
      auto dn = req->mutable_newnodes()->Add();
      dn->set_ipaddr(it->first);
      dn->set_datanodeuuid(it->second);
    }
  }

  void MakeUpdateBlockForPipelineRequest(
      uint64_t block_id,
      UpdateBlockForPipelineRequestProto* req) {
    req->set_clientname("client");
    req->mutable_block()->set_blockid(block_id);
  }

  Status CreateFile(const std::string& src,
                    const PermissionStatus& permission,
                    const CreateRequestProto& request) {
    return Status();
  }

  bool INodeCompare(const INode& src, const INode& dst) {
    if (src.id() != dst.id()) {
      return false;
    }
    if (src.parent_id() != dst.parent_id()) {
      return false;
    }
    if (src.name() != dst.name()) {
      return false;
    }
    if (src.permission().username() != dst.permission().username() ||
        src.permission().groupname() != dst.permission().groupname() ||
        src.permission().permission() != dst.permission().permission()) {
      return false;
    }
    if (src.mtime() != dst.mtime() || src.atime() != dst.atime()) {
      return false;
    }
    if (src.type() != dst.type()) {
      return false;
    }
    if (src.storage_policy_id() != dst.storage_policy_id()) {
      return false;
    }
    return true;
  }

  bool stop_;
  bool pause_;
  bool transited_ = false;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<UfsEnv> ufs_env_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  UserGroupInfo ugi_;
  std::string recycle_bin_dir_;
  std::string recycle_bin_path_;
  std::string recycle_userbin_path_;
  std::string recycle_datebin_path_;

  static LogRpcInfo default_rpc_info;
  static uint64_t default_soft_limit_ms;
  static uint64_t default_hard_limit_ms;
  static int32_t default_dn_keep_alive_timeout_sec;
  static int32_t default_datanode_stale_interval_ms;
  static int32_t default_blockmap_num_bucket_each_slice;
  static int32_t default_blockmap_num_slice;
  static uint32_t default_dfs_replication_min;
};

}  // namespace dancenn

#endif  // DANCENN_NAMESPACE_TEST_BASE_H
