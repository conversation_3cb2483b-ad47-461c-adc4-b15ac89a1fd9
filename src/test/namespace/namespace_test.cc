// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include <gtest/gtest.h>
#include <vector>
#include "test/namespace/namespace_test_base.h"

namespace dancenn {

namespace {

LogRpcInfo default_rpc_info("", 0);

auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
auto default_blockmap_num_bucket_each_slice =
    FLAGS_blockmap_num_bucket_each_slice;
auto default_blockmap_num_slice = FLAGS_blockmap_num_slice;
auto default_dfs_replication_min = FLAGS_dfs_replication_min;

namespace {
}  // namespace

class NameSpaceTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_lease_expired_soft_limit_ms = 200;
    FLAGS_lease_expired_hard_limit_ms = 1000;
    FLAGS_lease_monitor_interval_ms = 1000;
    FLAGS_datanode_keep_alive_timeout_sec = 1000;
    FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
    FLAGS_blockmap_num_bucket_each_slice = 1;
    FLAGS_blockmap_num_slice = 1;
    FLAGS_dfs_replication_min = 1;
    FLAGS_dfs_replication_max = 3;
    FLAGS_bytecool_feature_enabled = true;
    FLAGS_recycle_bin_enable = false;
    FLAGS_recycle_bin_scanner_enable = true;
    FLAGS_recycle_bin_scanner_interval_sec = 1;
    FLAGS_recycle_bin_retention_day = 0;
    FLAGS_client_replication_support = true;
    /*todo setup ugi.*/
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;
    FLAGS_complete_rpc_abandon_last_empty_block = false;

    FLAGS_permission_enabled = true;
    FLAGS_permission_model = "posix";

    FLAGS_enable_location_tag = false;
    FLAGS_enable_location_tag_by_rack_aware = true;
    FLAGS_placement_ignore_local_az = false;
    FLAGS_placement_ignore_existed_switch = false;

    ugi_ = UserGroupInfo("root",  "supergroup");
    recycle_bin_dir_.append("/");
    recycle_bin_path_.append("/" + kRecycleBinDirNameString);
    recycle_userbin_path_.append(recycle_bin_path_ + "/" + ugi_.current_user());
    recycle_datebin_path_.append(recycle_userbin_path_ + "/" + TimeUtil::GetNowYMD());

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    edit_log_ctx_ = CreateContext();
    block_manager_.reset(new BlockManager(edit_log_ctx_));
    block_manager_->TestOnlySetEditLogCtx(edit_log_ctx_);
    MockFSImageTransfer(db_path_).Transfer();
    ufs_env_ = UfsEnv::Create();
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx_,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                ufs_env_));
    ha_state_ = std::make_unique<MockHAState>();
    safemode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    datanode_manager_->set_block_manager(block_manager_.get());

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = std::unique_ptr<EditLogSenderBase>(
        new MockEditLogSender(edit_log_ctx_, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));

    ns_->Start();
    ns_->StartActive();

    // add a datanode to the cluster
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    StartHeartbeat();

    ns_->StopBGDeletionWorker();
    ns_->StopLeaseMonitor();
  }

  void TearDown() override {
    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_blockmap_num_bucket_each_slice =
        default_blockmap_num_bucket_each_slice;
    FLAGS_blockmap_num_slice = default_blockmap_num_slice;
    FLAGS_dfs_replication_min = default_dfs_replication_min;

    stop_ = true;
    if (heartbeat_thread_.joinable()) {
      heartbeat_thread_.join();
    }
    if (ns_) {
      ns_->StopActive();
      ns_->Stop();
      ns_.reset();
    }
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  void AddFile(const std::string& path,
               uint64_t len,
               uint32_t replica,
               bool need_complete,
               AddBlockResponseProto* add_response,
               CreateResponseProto* create_response,
               bool create_parent = false,
               uint32_t create_flag = ::cloudfs::CreateFlagProto::CREATE);

  void CreateFileAndVerifyStatus(const std::string& path,
                                 const PermissionStatus& p,
                                 bool is_dir,
                                 const std::string& target_status) {
    if (is_dir) {
      ASSERT_TRUE(!ns_->MkDirs(path, p, /*create parent*/true).HasException());
      GetFileInfoResponseProto response;
      ASSERT_FALSE(
          ns_->GetFileInfo(
                 path, NetworkLocationInfo(), false, false, &response, ugi_)
              .HasException());
      auto parent_perm = FsPermission(response.fs().permission().perm());
      std::ostringstream ss;
      ss << parent_perm;
      ASSERT_EQ(ss.str(), target_status);
    } else {
      auto create_request = MakeCreateRequest();
      create_request.set_createparent(true);
      CreateResponseProto create_response;
      ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                       .HasException());
      GetFileInfoResponseProto response;
      ASSERT_FALSE(
          ns_->GetFileInfo(
                 path, NetworkLocationInfo(), false, false, &response, ugi_)
              .HasException());
      auto parent_perm = FsPermission(response.fs().permission().perm());
      std::ostringstream ss;
      ss << parent_perm;
      ASSERT_EQ(ss.str(), target_status);
    }
  }

  void VerifyFileWithStatus(const std::string& path,
                            bool target_is_dir,
                            const std::string& target_status,
                            std::string* target_user = nullptr,
                            std::string* target_group = nullptr)  {
      GetFileInfoResponseProto response;
    ASSERT_FALSE(
        ns_->GetFileInfo(
               path, NetworkLocationInfo(), false, false, &response, ugi_)
            .HasException());
    auto parent_perm = FsPermission(response.fs().permission().perm());
    std::ostringstream ss;
    ss << parent_perm;
    ASSERT_EQ(ss.str(), target_status);
    ASSERT_EQ(target_is_dir,
              response.fs().filetype() ==
                  cloudfs::HdfsFileStatusProto_FileType_IS_DIR);
  }

  CommitBlockSynchronizationRequestProto GetCommitSyncRequest(
      uint32_t len,
      const AddBlockResponseProto& add_response,
      bool close,
      bool del);

  void StartHeartbeat() {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&latch, this]() {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
      cnetpp::base::IPAddress ip("***********");
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);
      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          DatanodeManager::RepeatedCmds cmds;
          datanode_manager_->Heartbeat(request, &cmds);
          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  void AddDatanode(const std::string& ip, const std::string& uuid) {
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(uuid);
    reg.mutable_datanodeid()->set_ipaddr(ip);
    cnetpp::base::IPAddress ip_addr(ip);
    datanode_manager_->Register(reg.datanodeid(), &reg, ip_addr);
    datanode_manager_->RefreshConfig();
    DatanodeManager::RepeatedCmds cmds;
    HeartbeatRequestProto request;
    GetDiskRequest(&request, uuid, reg, 102400);
    datanode_manager_->Heartbeat(request, &cmds);
  }

  void GetDiskRequest(
      cloudfs::datanode::HeartbeatRequestProto* request,
      const std::string& uuid,
      const cloudfs::datanode::DatanodeRegistrationProto& reg,
      uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid(uuid);
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
    request->mutable_registration()->CopyFrom(reg);
  }

  std::shared_ptr<GetFileInfoResponseProto> GetFileInfoWrp(
      const std::string& path, bool has_exp = false) {
    std::shared_ptr<GetFileInfoResponseProto> response =
        std::make_shared<GetFileInfoResponseProto>();
    auto status = ns_->GetFileInfo(path,
                                   NetworkLocationInfo(),
                                   /*resolve_link*/ false,
                                   false,
                                   response.get(),
                                   ugi_);
    CHECK(status.HasException() == has_exp);
    return response;
  }

  void CreateFileWrp(const std::string& path,
                     const std::string& user,
                     const std::string& group,
                     bool parent) {
    auto p = MakePermission();
    p.set_username(user);
    p.set_groupname(group);
    auto create_request = MakeCreateRequest();
    CreateResponseProto response;
    create_request.set_createparent(parent);
    CHECK(!ns_->CreateFile(path, p, create_request, &response).HasException());
  }
  void MkdirsWrp(const std::string& path,
                 const std::string& user,
                 const std::string& group,
                 bool parent) {
    auto p = MakePermission();
    p.set_username(user);
    p.set_groupname(group);
    CHECK(!ns_->MkDirs(path, p, parent).HasException());
  }

  PermissionStatus MakePermission() {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("supergroup");
    p.set_permission(FsPermission::GetFileDefault().ToShort());
    return p;
  }

  AppendRequestProto MakeAppendRequest(const std::string& src,
                                       const std::string& client_name) {
    AppendRequestProto append_request;
    append_request.set_src(src);
    append_request.set_clientname(client_name);
    return append_request;
  }

  CreateRequestProto MakeCreateRequest() {
    CreateRequestProto create_request;
    create_request.set_src("");
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(false);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
  }

  GetBlockLocationsRequestProto MakeGetBlockLocationsRequest(
      const std::string& path) {
    GetBlockLocationsRequestProto req;
    req.set_src(path);
    req.set_offset(0);
    req.set_length(1);
    return req;
  }

  void FillGetAdditionalDatanodeRequest(
      cloudfs::GetAdditionalDatanodeRequestProto* req,
      const std::vector<std::pair<std::string, std::string>> locs,
      uint32_t rep_num) {
    req->set_clientname("client");

    for (auto& it : locs) {
      auto dn = req->mutable_existings()->Add();
      dn->mutable_id()->set_ipaddr(it.first);
      dn->mutable_id()->set_datanodeuuid(it.second);
    }

    req->set_numadditionalnodes(rep_num);
  }

  void MakeUpdatePipelineRequest(
      uint64_t block_id,
      uint64_t old_gs,
      uint64_t new_gs,
      const std::vector<std::pair<std::string, std::string>>& nodes,
      UpdatePipelineRequestProto* req) {
    req->set_clientname("client");
    req->mutable_oldblock()->set_blockid(block_id);
    req->mutable_oldblock()->set_generationstamp(old_gs);
    req->mutable_newblock()->set_blockid(block_id);
    req->mutable_newblock()->set_generationstamp(new_gs);

    for (auto it = nodes.begin(); it != nodes.end(); it++) {
      auto dn = req->mutable_newnodes()->Add();
      dn->set_ipaddr(it->first);
      dn->set_datanodeuuid(it->second);
    }
  }

  void MakeUpdateBlockForPipelineRequest(
      uint64_t block_id, UpdateBlockForPipelineRequestProto* req) {
    req->set_clientname("client");
    req->mutable_block()->set_blockid(block_id);
  }

  Status CreateFile(const std::string& src,
                    const PermissionStatus& permission,
                    const CreateRequestProto& request) {
    return Status();
  }

  bool INodeCompare(const INode& src, const INode& dst) {
    if (src.id() != dst.id()) {
      return false;
    }
    if (src.parent_id() != dst.parent_id()) {
      return false;
    }
    if (src.name() != dst.name()) {
      return false;
    }
    if (src.permission().username() != dst.permission().username() ||
        src.permission().groupname() != dst.permission().groupname() ||
        src.permission().permission() != dst.permission().permission()) {
      return false;
    }
    if (src.mtime() != dst.mtime() || src.atime() != dst.atime()) {
      return false;
    }
    if (src.type() != dst.type()) {
      return false;
    }
    if (src.storage_policy_id() != dst.storage_policy_id()) {
      return false;
    }
    return true;
  }

  bool stop_;
  bool pause_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<UfsEnv> ufs_env_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  UserGroupInfo ugi_;
  std::string recycle_bin_dir_;
  std::string recycle_bin_path_;
  std::string recycle_userbin_path_;
  std::string recycle_datebin_path_;
};

void NameSpaceTest::AddFile(const std::string& path,
                            uint64_t len,
                            uint32_t replica,
                            bool need_complete,
                            AddBlockResponseProto* add_response,
                            CreateResponseProto* create_response,
                            bool create_parent,
                            uint32_t create_flag) {
  auto create_request = MakeCreateRequest();
  create_request.set_replication(replica);
  create_request.set_createparent(create_parent);
  create_request.set_createflag(create_flag);
  PermissionStatus p;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, create_response)
                   .HasException());

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, add_response)
           .HasException());

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response->block().b().blockid(),
             add_response->block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);

  if (need_complete) {
    report.Clear();
    MakeReport(add_response->block().b().blockid(),
               add_response->block().b().generationstamp(),
               len,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);
    ns_->GetBlockReportManager()->IncrementalBlockReport(
        1, "datanode1", report);

    CompleteRequestProto complete_request;
    complete_request.set_src(path);
    complete_request.set_clientname("client");
    complete_request.mutable_last()->CopyFrom(add_response->block().b());
    complete_request.mutable_last()->set_numbytes(len);
    ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
  }
}

CommitBlockSynchronizationRequestProto NameSpaceTest::GetCommitSyncRequest(
    uint32_t len,
    const AddBlockResponseProto& add_response,
    bool close,
    bool del) {
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  auto recover_cmd = heartbeat_response.cmds(0).recoverycmd();
  CommitBlockSynchronizationRequestProto commit_request;
  commit_request.mutable_block()->CopyFrom(add_response.block().b());
  commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp());
  commit_request.set_newlength(len);
  commit_request.set_closefile(close);
  commit_request.set_deleteblock(del);
  return commit_request;
}

TEST_F(NameSpaceTest, FastStop) {
  auto start = TimeUtil::GetNowEpochMs();
  VLOG(10) << "FastStop, Stop BG";
  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();

  // Notice: NameSpaceTest::block_manager_ holds meta_storage_.
  // So old meta storag is alive when MockNameSpace creates a new one.
  VLOG(10) << "FastStop, ns_->StopActive()";
  ns_->StopActive();
  VLOG(10) << "FastStop, ns_->Stop()";
  ns_->Stop();
  VLOG(10) << "FastStop, ns_->meta_storage()->Shutdown()";
  ns_->meta_storage()->Shutdown();
  VLOG(10) << "FastStop, ns_.reset()";
  ns_.reset();

  auto end = TimeUtil::GetNowEpochMs();
  ASSERT_TRUE(2000 > end - start);
}

TEST_F(NameSpaceTest, GetListing) {
  std::string file1 = "/file1";
  std::string file2 = "/file2";
  std::string file3 = "/dir/dir1/file";
  std::string file4 = "/dir/dir2/file";
  std::string dir1 = "/dir1";
  std::string dir2 = "/dir2";

  auto p_file = MakePermission();

  CreateFlag::SetOverwrite(CreateFlag::SetCreate(0));

  auto file_status = ::cloudfs::HdfsFileStatusProto();
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;

  ASSERT_TRUE(!ns_->CreateFile(file1, p_file, create_request, &response)
                   .HasException());

  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file2, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  ns_->Delete("/dir", true);
  create_request.set_createparent(true);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file3, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 5);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file4, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);
  PermissionStatus p_dir;
  p_dir.set_username("root");
  p_dir.set_groupname("root");
  p_dir.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(dir1, p_dir, false).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 8);
  ASSERT_TRUE(!ns_->MkDirs(dir2, p_dir, false).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 9);

  GetListingRequestProto get_request;
  get_request.set_src("/");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  auto status = ns_->GetListing(
      "/", NetworkLocationInfo(), get_request, &get_response, ugi_);
  ASSERT_TRUE(!status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 5);

  get_response.clear_dirlist();
  ASSERT_TRUE(
      !ns_->GetListing(
              "/dir", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "type:" << file_status.filetype();
    LOG(INFO) << "path:" << file_status.path();
    LOG(INFO) << "length:" << file_status.length();
    if (file_status.has_block_replication()) {
      LOG(INFO) << "replication:" << file_status.block_replication();
    }
    if (file_status.has_blocksize()) {
      LOG(INFO) << "block size:" << file_status.blocksize();
    }
    LOG(INFO) << "mtime:" << file_status.modification_time();
    LOG(INFO) << "atime:" << file_status.access_time();
    LOG(INFO) << "permission:" << file_status.permission().perm();
    LOG(INFO) << "user:" << file_status.owner();
    LOG(INFO) << "group:" << file_status.group();
    if (file_status.has_childrennum()) {
      LOG(INFO) << "children num:" << file_status.childrennum();
    }
    if (file_status.has_fileencryptioninfo()) {
      std::string fileencrypt;
      file_status.fileencryptioninfo().SerializeToString(&fileencrypt);
      LOG(INFO) << "fileencryptioninfo:" << fileencrypt;
    }
    if (file_status.has_storagepolicy()) {
      LOG(INFO) << "storagepolicy:" << file_status.storagepolicy();
    }
  }

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing("/dir/dir1",
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing("/dir/dir2",
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "type:" << file_status.filetype();
    LOG(INFO) << "path:" << file_status.path();
    LOG(INFO) << "length:" << file_status.length();
    if (file_status.has_block_replication()) {
      LOG(INFO) << "replication:" << file_status.block_replication();
    }
    if (file_status.has_blocksize()) {
      LOG(INFO) << "block size:" << file_status.blocksize();
    }
    LOG(INFO) << "mtime:" << file_status.modification_time();
    LOG(INFO) << "atime:" << file_status.access_time();

    LOG(INFO) << "permission:" << file_status.permission().perm();
    LOG(INFO) << "user:" << file_status.owner();
    LOG(INFO) << "group:" << file_status.group();
    if (file_status.has_childrennum()) {
      LOG(INFO) << "children num:" << file_status.childrennum();
    }
    if (file_status.has_fileencryptioninfo()) {
      std::string fileencrypt;
      file_status.fileencryptioninfo().SerializeToString(&fileencrypt);
      LOG(INFO) << "fileencryptioninfo:" << fileencrypt;
    }
    if (file_status.has_storagepolicy()) {
      LOG(INFO) << "storagepolicy:" << file_status.storagepolicy();
    }
  }
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 9);

  ASSERT_TRUE(get_response.has_dirlist());
  ASSERT_TRUE(!ns_->GetListing("/path/not/found",
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_FALSE(get_response.has_dirlist());
}

/**
 * Note: This test is under a prefix. now root inode is gen by mocking.
 */
TEST_F(NameSpaceTest, GetListingWithDifferentUser) {
  const std::string prefix = "/prefix";
  std::string file1 = prefix + "/file1";
  std::string file2 = prefix + "/file2";
  std::string file3 = prefix + "/dir/dir1/file";
  std::string file4 = prefix + "/dir/dir2/file";
  std::string dir1 =  prefix + "/dir1";
  std::string dir2 = prefix + "/dir2";

  UserGroupInfo other_ugi("other", "other");
  UserGroupInfo group_ugi("other", "normalgroup");
  UserGroupInfo super_ugi("root", "supergroup");

  PermissionStatus p_dir;
  p_dir.set_username("root");
  p_dir.set_groupname("normalgroup");
  p_dir.set_permission(0755);
  ASSERT_TRUE(!ns_->MkDirs(prefix, p_dir, false).HasException());

  auto p_file = MakePermission();

  // 1, prepare files.
  CreateFlag::SetOverwrite(CreateFlag::SetCreate(0));

  auto file_status = ::cloudfs::HdfsFileStatusProto();
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;

  ASSERT_TRUE(!ns_->CreateFile(file1, p_file, create_request, &response)
                   .HasException());

  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 2);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file2, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 3);
  ns_->Delete("/dir", true);
  create_request.set_createparent(true);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file3, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 6);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file4, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 8);

  ASSERT_TRUE(!ns_->MkDirs(dir1, p_dir, false).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 9);
  ASSERT_TRUE(!ns_->MkDirs(dir2, p_dir, false).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kRootINodeId + 10);

  // check 1.a check same group diff user.
  {
    GetListingRequestProto get_request;
    get_request.set_src(prefix);
    get_request.set_needlocation(false);
    get_request.set_startafter("");
    GetListingResponseProto get_response;
    auto status = ns_->GetListing(
        prefix, NetworkLocationInfo(), get_request, &get_response, group_ugi);
    ASSERT_TRUE(!status.HasException()) << status.ToString();

    ASSERT_EQ(5, get_response.dirlist().partiallisting().size());
  }

  // check 1.b check other user other group.
  {
    GetListingRequestProto get_request;
    get_request.set_src(prefix);
    get_request.set_needlocation(false);
    get_request.set_startafter("");
    GetListingResponseProto get_response;
    auto status = ns_->GetListing(
        prefix, NetworkLocationInfo(), get_request, &get_response, other_ugi);
    ASSERT_TRUE(!status.HasException()) << status.ToString();

    ASSERT_EQ(5, get_response.dirlist().partiallisting().size());
  }

  // step 2. remove x permission for other, group.
  {
    auto s = ns_->SetPermission(prefix, 0744);
    ASSERT_TRUE(!s.HasException());
  }

  VerifyFileWithStatus(prefix, true, "rwxr--r--");

  // check 2.a check same group different user. Exception with 744.
  {
    GetListingRequestProto get_request;
    get_request.set_src(prefix);
    get_request.set_needlocation(false);
    get_request.set_startafter("");
    GetListingResponseProto get_response;
    auto status = ns_->GetListing(
        prefix, NetworkLocationInfo(), get_request, &get_response, group_ugi);
    ASSERT_TRUE(status.HasException()) << status.ToString();
    ASSERT_EQ("Permission denied: user = other, access= r-x, "
              "inode=\"/prefix\"", status.message());

    ASSERT_EQ(0, get_response.dirlist().partiallisting().size());
  }

  // check 2.b check other user other group.
  {
    GetListingRequestProto get_request;
    get_request.set_src(prefix);
    get_request.set_needlocation(false);
    get_request.set_startafter("");
    GetListingResponseProto get_response;
    auto status = ns_->GetListing(
        prefix, NetworkLocationInfo(), get_request, &get_response, other_ugi);
    ASSERT_TRUE(status.HasException()) << status.ToString();
    ASSERT_EQ("Permission denied: user = other, access= r-x,"
              " inode=\"/prefix\""
              , status.message());

    ASSERT_EQ(0, get_response.dirlist().partiallisting().size());
  }

  // check 3 super user can delete.
  {
    GetListingRequestProto get_request;
    get_request.set_src(prefix);
    get_request.set_needlocation(false);
    get_request.set_startafter("");
    GetListingResponseProto get_response;
    auto status = ns_->GetListing(
        prefix, NetworkLocationInfo(), get_request, &get_response, super_ugi);
    ASSERT_TRUE(!status.HasException());

    ASSERT_EQ(5, get_response.dirlist().partiallisting().size());
  }
}

TEST_F(NameSpaceTest, GetDirSubINodes) {
  std::string dir = "/testpath/d1/d2/d3";
  PermissionStatus p_dir;
  p_dir.set_username("root");
  p_dir.set_groupname("normalgroup");
  p_dir.set_permission(0755);
  ASSERT_TRUE(!ns_->MkDirs(dir, p_dir, true).HasException());

  auto create_request = MakeCreateRequest();
  auto p_file = MakePermission();
  CreateResponseProto response;
  std::string file = dir + "/file";
  ASSERT_TRUE(
      !ns_->CreateFile(file, p_file, create_request, &response).HasException());

  std::vector<INode> sub_inodes;
  bool has_more = true;
  ns_->GetDirSubINodes(dir, "", 500, &sub_inodes, &has_more);
  LOG(INFO) << "sub_inodes size " << sub_inodes.size();
  ASSERT_TRUE(sub_inodes.size() == 1);
}

TEST_F(NameSpaceTest, GetPreferredBlockSize) {
  std::string test_dir = "/test_dir";
  std::string test_file = test_dir + "/test_file";
  auto p = MakePermission();
  auto create_request = MakeCreateRequest();
  create_request.set_createparent(true);
  CreateResponseProto create_response;
  ASSERT_FALSE(ns_->CreateFile(test_file, p, create_request, &create_response)
                   .HasException());

  GetPreferredBlockSizeResponseProto prefer_response;
  auto status = ns_->GetPreferredBlockSize(test_dir + "foo", &prefer_response);
  ASSERT_EQ(status.exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(status.message(), "File does not exist: " + test_dir + "foo");

  prefer_response.Clear();
  status = ns_->GetPreferredBlockSize(test_dir, &prefer_response);
  ASSERT_EQ(status.exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(status.message(), "Path is not a file: " + test_dir);

  prefer_response.Clear();
  status = ns_->GetPreferredBlockSize(test_file, &prefer_response);
  ASSERT_FALSE(status.HasException());
  ASSERT_EQ(prefer_response.bsize(), 128 * 1024 * 1024);
}

TEST_F(NameSpaceTest, CreateFile) {
  std::string base_path = "/create";
  std::string path1 = "/create/create_file1";
  std::string path2 = "/create/create_file2";
  std::string path3 = "/create/create_file3/ccc/3";
  std::string path4 = "/create/distribute";
  ns_->Delete(base_path, true);
  ASSERT_TRUE(ns_->Delete(path1, true).IsFalse());
  ASSERT_TRUE(ns_->Delete(path2, true).IsFalse());
  ASSERT_TRUE(ns_->Delete(path3, true).IsFalse());
  ASSERT_TRUE(ns_->Delete(path4, true).IsFalse());

  auto p = MakePermission();

  auto file_status = ::cloudfs::HdfsFileStatusProto();

  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  create_request.set_replication(0);
  ASSERT_EQ(ns_->CreateFile("foo", p, create_request, &response).exception(),
            JavaExceptions::kIOException);
  create_request.set_replication(FLAGS_dfs_replication_max + 1);
  ASSERT_EQ(ns_->CreateFile("foo", p, create_request, &response).exception(),
            JavaExceptions::kIOException);
  create_request.set_replication(1);

  ASSERT_EQ(ns_->CreateFile(path1, p, create_request, &response).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId);

  create_request.set_createparent(true);
  ASSERT_TRUE(
      !ns_->CreateFile(path1, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);

  ASSERT_EQ(
      ns_->CreateFile(base_path, p, create_request, &response).exception(),
      JavaExceptions::Exception::kFileAlreadyExistsException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  ASSERT_EQ(ns_->CreateFile(path1, p, create_request, &response).exception(),
            JavaExceptions::Exception::kAlreadyBeingCreatedException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);

  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE |
                                ::cloudfs::CreateFlagProto::OVERWRITE);
  ASSERT_TRUE(
      !ns_->CreateFile(path1, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);

  create_request.set_createflag(::cloudfs::CreateFlagProto::OVERWRITE);
  ASSERT_EQ(ns_->CreateFile(path2, p, create_request, &response).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);

  create_request.set_createparent(false);
  ASSERT_TRUE(
      !ns_->CreateFile(path2, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  create_request.set_createparent(true);
  ASSERT_TRUE(
      !ns_->CreateFile(path3, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);

  std::vector<uint64_t> collected_blocks;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(!ns_->GetListing(base_path,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());

  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 3);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);

  // Test expand replica
  {
    ASSERT_TRUE(!ns_->MkDirs(path4, p, true).HasException());
    ASSERT_FALSE(ns_->SetReplicaPolicy(path4, kDistributePolicy, "LF,HL")
                     .HasException());

    auto dist_file1 = path4 + "/file1";
    create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    ASSERT_TRUE(
        !ns_->CreateFile(dist_file1, p, create_request, &create_response)
             .HasException());
    std::vector<cnetpp::base::StringPiece> dist_file_components;
    ASSERT_TRUE(SplitPath(dist_file1, &dist_file_components));
    INode dist_file_inode1;
    ASSERT_EQ(ns_->GetLastINodeInPath(dist_file_components, &dist_file_inode1),
              kOK);
    ASSERT_EQ(dist_file_inode1.replication(), 2);

    auto dist_file2 = path4 + "/file2";
    create_request = MakeCreateRequest();
    create_request.set_replication(3);
    create_response.Clear();
    ASSERT_TRUE(
        !ns_->CreateFile(dist_file2, p, create_request, &create_response)
             .HasException());
    std::vector<cnetpp::base::StringPiece> dist_file_components2;
    ASSERT_TRUE(SplitPath(dist_file2, &dist_file_components2));
    INode dist_file_inode2;
    ASSERT_EQ(ns_->GetLastINodeInPath(dist_file_components2, &dist_file_inode2),
              kOK);
    ASSERT_EQ(dist_file_inode2.replication(), 4);
  }
}

TEST_F(NameSpaceTest, AddBlock) {
  auto p = MakePermission();
  std::string test_file = "/test_file";
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(test_file, 100, 1, false, &add_response, &create_response);
  ASSERT_FALSE(add_response.block().b().blockpufsname().empty());
  auto retry_add_request = MakeAddBlockRequest();
  AddBlockResponseProto retry_add_response;
  cnetpp::base::IPAddress client_ip("***********");
  ASSERT_TRUE(!ns_->AddBlock(test_file,
                             client_ip,
                             default_rpc_info,
                             retry_add_request,
                             &retry_add_response)
                   .HasException());
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());
  EXPECT_EQ(bip.inode_id(), kLastReservedINodeId + 1);
  EXPECT_EQ(bip.expected_rep(), 1);
  EXPECT_EQ(bip.pufs_name(), add_response.block().b().blockpufsname());
  ASSERT_EQ(retry_add_response.block().b().blockid(),
            add_response.block().b().blockid());
  ASSERT_EQ(retry_add_response.block().b().blockpufsname(),
            add_response.block().b().blockpufsname());
}

TEST_F(NameSpaceTest, SetReplica) {
  auto p = MakePermission();
  std::string test_dir = "/test_dir";
  std::string test_file = "/test_file";
  ASSERT_FALSE(ns_->MkDirs(test_dir, p, true).HasException());
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(test_file, 100, 1, true, &add_response, &create_response);

  auto status = ns_->SetReplication(test_file, 0);
  ASSERT_TRUE(status.HasException());
  ASSERT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  ASSERT_EQ(status.message(),
            "file /test_file requested replica 0 less than minimum 1");

  status = ns_->SetReplication(test_file, FLAGS_dfs_replication_max + 1);
  ASSERT_TRUE(status.HasException());
  ASSERT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  ASSERT_EQ(status.message(),
            "file /test_file requested replica " +
                std::to_string(FLAGS_dfs_replication_max + 1) +
                " exceeds maximum " +
                std::to_string(FLAGS_dfs_replication_max));

  status = ns_->SetReplication(test_file, FLAGS_dfs_replication_max / 2);
  ASSERT_FALSE(status.HasException());
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 100);
  EXPECT_EQ(bip.inode_id(), kLastReservedINodeId + 2);
  EXPECT_EQ(bip.expected_rep(), FLAGS_dfs_replication_max / 2);
  EXPECT_EQ(bip.pufs_name(), add_response.block().b().blockpufsname());
  GetFileInfoResponseProto get_response;
  status = ns_->GetFileInfo(
      test_file, NetworkLocationInfo(), true, false, &get_response, ugi_);
  ASSERT_FALSE(status.HasException());
  ASSERT_EQ(get_response.fs().block_replication(),
            FLAGS_dfs_replication_max / 2);
}

TEST_F(NameSpaceTest, CreateWithLease) {
  std::string test_dir = "/test_dir";
  PermissionStatus p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  ns_->StopLeaseMonitor();
  std::string test_file = test_dir + "/file1";
  cnetpp::base::IPAddress client_ip("***********");

  // create on an existing but not soft expired file
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(test_file, 100, 1, false, &add_response, &create_response);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  auto create_request2 = MakeCreateRequest();
  CreateResponseProto create_response2;
  ASSERT_EQ(ns_->CreateFile(test_file, p, create_request2, &create_response2)
                .exception(),
            JavaExceptions::Exception::kAlreadyBeingCreatedException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);

  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_soft_limit_ms + 1));
  ASSERT_EQ(ns_->CreateFile(test_file, p, create_request2, &create_response2)
                .exception(),
            JavaExceptions::Exception::kAlreadyBeingCreatedException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  create_request2.set_clientname("client2");
  ASSERT_EQ(ns_->CreateFile(test_file, p, create_request2, &create_response2)
                .exception(),
            JavaExceptions::Exception::kRecoveryInProgressException);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());
  EXPECT_EQ(bip.inode_id(), kLastReservedINodeId + 2);
  EXPECT_EQ(bip.expected_rep(), 1);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  ASSERT_EQ(block_manager_->GetBlockUCState(add_response.block().b().blockid()),
            BlockUCState::kUnderRecovery);
  auto commit_request = GetCommitSyncRequest(100, add_response, false, false);
  ASSERT_TRUE(ns_->CommitBlockSynchronization(commit_request).IsOK());
  ASSERT_EQ(block_manager_->GetBlockUCState(add_response.block().b().blockid()),
            BlockUCState::kCommitted);
}

TEST_F(NameSpaceTest, ConcurrentCreate) {
  ns_->StopLeaseMonitor();
  std::vector<std::string> paths;
  paths.emplace_back("/a");
  paths.emplace_back("/a/b1");
  paths.emplace_back("/a/b1/c");
  paths.emplace_back("/a/b2");
  paths.emplace_back("/a/b2/c");

  cnetpp::concurrency::ThreadPool tp("test");
  tp.set_num_threads(10);
  tp.Start();

  for (int i = 0; i < 10; ++i) {
    tp.AddTask([this, &paths, i]() -> bool {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<> dis(0, paths.size() - 1);
      std::uniform_int_distribution<> dis2(0, 9);
      PermissionStatus p;
      std::string client = "client#" + std::to_string(i);
      LOG(INFO) << client;
      for (int r = 0; r < 1000; ++r) {
        int pidx = dis(gen);
        if (dis(gen) == 0) {
          ns_->Delete(paths[pidx], true);
        } else {
          auto request = MakeCreateRequest();
          request.set_createparent(true);
          request.set_clientname(client);
          CreateResponseProto response;
          ns_->CreateFile(paths[pidx], p, request, &response);
        }
      }
      return true;
    });
  }

  tp.Stop(true);
}

TEST_F(NameSpaceTest, MkDir) {
  std::string test_path_1 = "/mkdir";
  std::string test_path_2 = "/mkdir/p1";
  std::string test_path_3 = "/mkdir/p1/p2";
  std::string test_path_4 = "/mkdir/p1/p2/p3";

  ns_->Delete(test_path_1, true);
  ASSERT_TRUE(ns_->Delete(test_path_2, true).IsFalse());
  ASSERT_TRUE(ns_->Delete(test_path_3, true).IsFalse());
  ASSERT_TRUE(ns_->Delete(test_path_4, true).IsFalse());

  auto p = MakePermission();

  ASSERT_TRUE(!ns_->MkDirs(test_path_1, p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);

  // test set replication
  std::vector<cnetpp::base::StringPiece> test_path_1_components;
  ASSERT_TRUE(SplitPath(test_path_1, &test_path_1_components));
  INode path1_inode;
  ASSERT_EQ(ns_->GetLastINodeInPath(test_path_1_components, &path1_inode), kOK);
  auto origin_replica = path1_inode.replication();
  auto new_replica =
      (FLAGS_dfs_replication_min + FLAGS_dfs_replication_max) / 2;
  if (new_replica == origin_replica) {
    new_replica += 1;
  }
  ASSERT_TRUE(!ns_->SetReplication(test_path_1, new_replica).HasException());
  ASSERT_EQ(ns_->GetLastINodeInPath(test_path_1_components, &path1_inode), kOK);
  ASSERT_EQ(path1_inode.replication(), origin_replica);
  // end set replication

  ASSERT_EQ(ns_->MkDirs(test_path_4, p, false).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  ASSERT_TRUE(!ns_->MkDirs(test_path_4, p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  auto directory_listing = ::cloudfs::DirectoryListingProto();
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  auto s = ns_->GetListing(
      test_path_1, NetworkLocationInfo(), get_request, &get_response, ugi_);
  ASSERT_TRUE(!s.HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing(test_path_2,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing(test_path_4,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 0);
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing(test_path_3,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);

  auto file_status = get_response.dirlist().partiallisting().Get(0);
  LOG(INFO) << "type:" << file_status.filetype();
  LOG(INFO) << "path:" << file_status.path();
  LOG(INFO) << "length:" << file_status.length();
  if (file_status.has_block_replication()) {
    LOG(INFO) << "replication:" << file_status.block_replication();
  }
  if (file_status.has_blocksize()) {
    LOG(INFO) << "block size:" << file_status.blocksize();
  }
  LOG(INFO) << "mtime:" << file_status.modification_time();
  LOG(INFO) << "atime:" << file_status.access_time();

  LOG(INFO) << "permission:" << file_status.permission().perm();
  LOG(INFO) << "user:" << file_status.owner();
  LOG(INFO) << "group:" << file_status.group();
  if (file_status.has_childrennum()) {
    LOG(INFO) << "children num:" << file_status.childrennum();
  }
  if (file_status.has_fileencryptioninfo()) {
    std::string fileencrypt;
    file_status.fileencryptioninfo().SerializeToString(&fileencrypt);
    LOG(INFO) << "fileencryptioninfo:" << fileencrypt;
  }
  if (file_status.has_storagepolicy()) {
    LOG(INFO) << "storagepolicy:" << file_status.storagepolicy();
  }
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);
}

TEST_F(NameSpaceTest, GetFileInfo) {
  auto p = MakePermission();
  std::string test_dir = "/test_dir";
  std::string test_file = test_dir + "/test_file";

  ASSERT_FALSE(ns_->MkDirs(test_dir, p, true).HasException());
  GetFileInfoResponseProto response;
  ASSERT_FALSE(
      ns_->GetFileInfo(
             test_dir, NetworkLocationInfo(), false, false, &response, ugi_)
          .HasException());
  ASSERT_EQ(response.fs().filetype(),
            HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_DIR);
  ASSERT_EQ(response.fs().path(), test_dir);
  ASSERT_EQ(response.fs().length(), 0);
  ASSERT_FALSE(response.fs().has_blocksize());
  ASSERT_FALSE(response.fs().has_block_replication());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  auto create_request = MakeCreateRequest();
  create_request.set_replication(1);
  ASSERT_TRUE(!ns_->CreateFile(test_file, p, create_request, &create_response)
                   .HasException());

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  ASSERT_TRUE(!ns_->AddBlock(test_file,
                             client_ip,
                             default_rpc_info,
                             add_request,
                             &add_response)
                   .HasException());

  response.Clear();
  ASSERT_FALSE(
      ns_->GetFileInfo(
             test_file, NetworkLocationInfo(), false, false, &response, ugi_)
          .HasException());
  ASSERT_EQ(
      response.fs().filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(response.fs().path(), test_file);
  ASSERT_EQ(response.fs().blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(response.fs().block_replication(), 1);
  ASSERT_EQ(response.fs().length(), 0);
  ASSERT_EQ(response.fs().fileid(), create_response.fs().fileid());

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);

  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 0);

  response.Clear();
  ASSERT_FALSE(
      ns_->GetFileInfo(
             test_file, NetworkLocationInfo(), false, false, &response, ugi_)
          .HasException());
  ASSERT_EQ(
      response.fs().filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(response.fs().path(), test_file);
  ASSERT_EQ(response.fs().blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(response.fs().block_replication(), 1);
  ASSERT_EQ(response.fs().length(), 0);
  ASSERT_EQ(response.fs().fileid(), create_response.fs().fileid());

  FsyncRequestProto sync_request;
  sync_request.set_src(test_file);
  sync_request.set_client("client");
  sync_request.set_lastblocklength(10);
  sync_request.set_fileid(create_response.fs().fileid());
  ASSERT_FALSE(ns_->Fsync(test_file, sync_request).HasException());

  response.Clear();
  ASSERT_FALSE(
      ns_->GetFileInfo(
             test_file, NetworkLocationInfo(), false, false, &response, ugi_)
          .HasException());
  ASSERT_EQ(
      response.fs().filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(response.fs().path(), test_file);
  ASSERT_EQ(response.fs().blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(response.fs().block_replication(), 1);
  ASSERT_EQ(response.fs().length(), 10);
  ASSERT_EQ(response.fs().fileid(), create_response.fs().fileid());

  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.num_bytes(), 10);

  report.Clear();
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);

  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));

  CompleteRequestProto complete_request;
  complete_request.set_src(test_file);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(100);
  ASSERT_TRUE(!ns_->CompleteFile(test_file, complete_request).HasException());

  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);

  response.Clear();
  ASSERT_FALSE(
      ns_->GetFileInfo(
             test_file, NetworkLocationInfo(), false, false, &response, ugi_)
          .HasException());
  ASSERT_EQ(
      response.fs().filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(response.fs().path(), test_file);
  ASSERT_EQ(response.fs().blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(response.fs().block_replication(), 1);
  ASSERT_EQ(response.fs().length(), 100);
  ASSERT_EQ(response.fs().fileid(), create_response.fs().fileid());
}

TEST_F(NameSpaceTest, DeleteDir) {
  ns_->StopBGDeletionWorker();

  std::string file1 = "/d1/d2/f1";
  std::string file2 = "/d1/d2/f2";
  std::string file3 = "/d1/d2/f3";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs("/d1/d2", ps, true).HasException());
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  create_request.set_createparent(true);
  ASSERT_TRUE(
      !ns_->CreateFile(file1, ps, create_request, &response).HasException());
  ASSERT_TRUE(
      !ns_->CreateFile(file2, ps, create_request, &response).HasException());
  ASSERT_TRUE(
      !ns_->CreateFile(file3, ps, create_request, &response).HasException());
  auto s = ns_->Delete("/d1", true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(5000));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  EXPECT_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, Delete) {
  ns_->StopBGDeletionWorker();

  std::string base_path = "/delete";
  std::string file1 = "/delete/create_file1";
  std::string file2 = "/delete/create_file2";
  std::string dir1 = "/delete/create_dir1";
  std::string dir2 = "/delete/create_dir/dir/2";

  PermissionStatus p_file;
  p_file.set_username("root");
  p_file.set_groupname("root");
  p_file.set_permission(FsPermission::GetDirDefault().ToShort());

  CreateFlag::SetOverwrite(CreateFlag::SetCreate(0));

  auto file_status = ::cloudfs::HdfsFileStatusProto();

  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  create_request.set_createparent(true);
  ASSERT_TRUE(!ns_->CreateFile(file1, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  create_request.set_createparent(false);
  response.clear_fs();
  ASSERT_TRUE(!ns_->CreateFile(file2, p_file, create_request, &response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);

  PermissionStatus p_dir;
  p_dir.set_username("root");
  p_dir.set_groupname("root");
  p_dir.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(dir1, p_dir, false).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);
  ASSERT_TRUE(!ns_->MkDirs(dir2, p_dir, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);

  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(!ns_->GetListing(base_path,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 4);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);
  for (const auto& x : get_response.dirlist().partiallisting()) {
    auto s = ns_->Delete(base_path + "/" + x.path(), true);
    LOG(INFO) << s.message();
    ASSERT_TRUE(!s.HasException());
    ASSERT_TRUE(s.IsOK());
  }
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);

  {
    auto num_deleted_files = 0;
    auto num_deleted_dirs = 0;
    ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      if (inode.type() == INode::kFile) {
        num_deleted_files++;
      } else if (inode.type() == INode::kDirectory) {
        num_deleted_dirs++;
      }
      return true;
    });
    ASSERT_EQ(2, num_deleted_dirs);
    ASSERT_EQ(0, num_deleted_files);

    ns_->StartBGDeletionWorker();

    std::this_thread::sleep_for(std::chrono::milliseconds(1500));

    num_deleted_dirs = 0;
    num_deleted_files = 0;
    ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      if (inode.type() == INode::kFile) {
        num_deleted_files++;
      } else if (inode.type() == INode::kDirectory) {
        num_deleted_dirs++;
      }
      return true;
    });
    ASSERT_EQ(0, num_deleted_dirs);
    ASSERT_EQ(0, num_deleted_files);
  }

  get_response.clear_dirlist();
  ASSERT_TRUE(!ns_->GetListing(base_path,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 0);

  get_response.clear_dirlist();
  ASSERT_TRUE(
      !ns_->GetListing(
              "/", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);
  for (const auto& x : get_response.dirlist().partiallisting()) {
    auto s = ns_->Delete("/" + x.path(), true);
    ASSERT_TRUE(!s.HasException());
    ASSERT_TRUE(s.IsOK());
  }

  get_response.clear_dirlist();
  ASSERT_TRUE(
      !ns_->GetListing(
              "/", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), kNumReservedINode);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);
}

TEST_F(NameSpaceTest, Append) {
  ns_->Delete("/", true);
  ns_->StopBGDeletionWorker();
  auto create_request = MakeCreateRequest();
  auto p = MakePermission();
  std::string path = "/test";
  CreateResponseProto create_response;
  ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());

  std::string client_machine = "mock_client_machine";
  std::string client_name = "mock_client_name";
  auto append_request = MakeAppendRequest(path, client_name);
  AppendResponseProto append_response;
  auto status = ns_->Append(
      path, client_machine, default_rpc_info, append_request, &append_response);
  ASSERT_EQ(status.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  ASSERT_EQ(status.message(), "");
  ASSERT_FALSE(append_response.has_block());

  IsFileClosedResponseProto is_close_reponse;
  ns_->IsFileClosed(path, &is_close_reponse);
  ASSERT_FALSE(is_close_reponse.result());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response2;
  path = "/test2";
  AddFile(path, 100, 1, true, &add_response, &create_response2);
  is_close_reponse.Clear();
  ns_->IsFileClosed(path, &is_close_reponse);
  ASSERT_TRUE(is_close_reponse.result());
  auto append_request2 = MakeAppendRequest(path, client_name);
  AppendResponseProto append_response2;
  ASSERT_FALSE(ns_->Append(path,
                           client_machine,
                           default_rpc_info,
                           append_request2,
                           &append_response2)
                   .HasException());
  ASSERT_FALSE(append_response2.has_block());
  is_close_reponse.Clear();
  ns_->IsFileClosed(path, &is_close_reponse);
  ASSERT_FALSE(is_close_reponse.result());

  path = "/test3";
  create_response.Clear();
  create_request.set_replication(1);
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  add_response.Clear();
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response)
           .HasException());

  // The following code wants to commit last blk but keep file open.
  add_request = MakeAddBlockRequest();
  add_request.mutable_previous()->set_poolid(add_response.block().b().poolid());
  add_request.mutable_previous()->set_blockid(
      add_response.block().b().blockid());
  add_request.mutable_previous()->set_numbytes(1);
  add_request.mutable_previous()->set_generationstamp(
      add_response.block().b().generationstamp());
  AddBlockResponseProto add_response2;
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response2)
           .HasException());
  GetBlockLocationsRequestProto get_request;
  get_request.set_src(path);
  get_request.set_offset(0);
  get_request.set_length(128 * 1024 * 1024);
  GetBlockLocationsResponseProto get_response;
  ASSERT_TRUE(!ns_->GetBlockLocation(path,
                                     NetworkLocationInfo(client_ip),
                                     get_request,
                                     &get_response,
                                     ugi_)
                   .HasException());
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  AbandonBlockRequestProto abandon_block_request;
  abandon_block_request.mutable_b()->set_blockid(
      add_response2.block().b().blockid());
  abandon_block_request.set_src(path);
  abandon_block_request.set_holder("client");
  ASSERT_TRUE(!ns_->AbandonBlock(path, abandon_block_request).HasException());
  get_response.Clear();
  ASSERT_TRUE(!ns_->GetBlockLocation(path,
                                     NetworkLocationInfo(client_ip),
                                     get_request,
                                     &get_response,
                                     ugi_)
                   .HasException());
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            add_response.block().b().blockid());
  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             1,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  is_close_reponse.Clear();
  ns_->IsFileClosed(path, &is_close_reponse);
  ASSERT_FALSE(is_close_reponse.result());

  // append on a block which is not soft expired
  append_response.Clear();
  status = ns_->Append(
      path, client_machine, default_rpc_info, append_request, &append_response);
  ASSERT_EQ(status.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  ASSERT_EQ(status.message(),
            "Failed to create file " + path + " for client " + client_name +
                ", because this file is already being created");

  // append on a block which is soft expired
  uint64_t to_sleep = FLAGS_lease_expired_soft_limit_ms * 1.5;
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  append_response.Clear();
  status = ns_->Append(
      path, client_machine, default_rpc_info, append_request, &append_response);
  LOG(INFO) << status.ToString();
  ASSERT_FALSE(status.HasException());
  ASSERT_FALSE(append_response.has_block());
  is_close_reponse.Clear();
  ns_->IsFileClosed(path, &is_close_reponse);
  ASSERT_FALSE(is_close_reponse.result());
}

TEST_F(NameSpaceTest, TestAddBlock) {
  std::string path = "/test";
  cnetpp::base::IPAddress client_ip("***********");
  {
    // empty file
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    DEFER([&]() { ns_->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;

    add_request.mutable_previous()->set_blockid(10);
    ASSERT_STREQ(
        ns_->AddBlock(
               path, client_ip, default_rpc_info, add_request, &add_response)
            .ExceptionStr()
            .c_str(),
        JavaExceptions::IOException());
    add_request.mutable_previous()->Clear();
    add_response.Clear();

    ASSERT_FALSE(
        ns_->AddBlock(
               path, client_ip, default_rpc_info, add_request, &add_response)
            .HasException());
    ASSERT_EQ(add_response.block().offset(), 0);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
    EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
    EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());

    // retry
    AddBlockResponseProto add_response2;
    ASSERT_FALSE(
        ns_->AddBlock(
               path, client_ip, default_rpc_info, add_request, &add_response2)
            .HasException());
    ASSERT_EQ(add_response.block().b().blockid(),
              add_response2.block().b().blockid());
    ASSERT_EQ(add_response.block().offset(), add_response2.block().offset());
    ASSERT_EQ(add_response.block().locs_size(), 1);
    ASSERT_EQ(add_response.block().locs(0).id().ipaddr(),
              add_response2.block().locs(0).id().ipaddr());
    // retry will not update bxid.
    auto bxid = bip.version();
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.version(), bxid);

    FsyncRequestProto sync_request;
    sync_request.set_src(path);
    sync_request.set_client("client");
    sync_request.set_lastblocklength(100);
    sync_request.set_fileid(create_response.fs().fileid());
    ASSERT_FALSE(ns_->Fsync(path, sync_request).HasException());
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.num_bytes(), 100);
    auto status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response2);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(),
              "Add block on a non-empty block: block "
              "{ B1073741825, num_bytes 100, gs 1001 }");
    // TODO(liyuan) append on a full block
  }
  {
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    DEFER([&]() { ns_->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;
    auto status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_FALSE(status.HasException());
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               100,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);
    ns_->GetBlockReportManager()->IncrementalBlockReport(
        1, "datanode1", report);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
    EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
    EXPECT_EQ(bip.num_bytes(), 0);

    //// second block
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    // gs mismatch
    auto block_id = add_response.block().b().blockid();
    auto gs = add_response.block().b().generationstamp();
    add_response.Clear();
    add_request.mutable_previous()->set_generationstamp(gs + 1);
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(),
              "Reported previous block: block { B" + std::to_string(block_id) +
                  ", num_bytes 0, gs " + std::to_string(gs + 1) +
                  " } does not match stored last block: block { B" +
                  std::to_string(block_id) + ", num_bytes 0, gs " +
                  std::to_string(gs) +
                  " } or penultimate block: block { B0, num_bytes 0, gs 0 }");
    ASSERT_FALSE(add_response.has_block());
    // block id mismatch
    add_request.mutable_previous()->set_generationstamp(gs);
    add_request.mutable_previous()->set_blockid(block_id + 1);
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(),
              "Reported previous block: block { B" +
                  std::to_string(block_id + 1) + ", num_bytes 0, gs " +
                  std::to_string(gs) +
                  " } does not match stored last block: block { B" +
                  std::to_string(block_id) + ", num_bytes 0, gs " +
                  std::to_string(gs) +
                  " } or penultimate block: block { B0, num_bytes 0, gs 0 }");
    ASSERT_FALSE(add_response.has_block());
    // matching block id & gs
    add_request.mutable_previous()->set_generationstamp(gs);
    add_request.mutable_previous()->set_blockid(block_id);
    add_request.mutable_previous()->set_numbytes(100);
    add_response.Clear();
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    LOG(INFO) << add_response.ShortDebugString();
    ASSERT_FALSE(status.HasException());
    ASSERT_EQ(add_response.block().b().blockid(), block_id + 1);
    ASSERT_EQ(add_response.block().b().generationstamp(), gs + 1);
    ASSERT_EQ(add_response.block().offset(), 100);
    ASSERT_EQ(add_response.block().corrupt(), false);
    ASSERT_EQ(add_response.block().locs_size(), 1);
    ASSERT_EQ(add_response.block().storagetypes(0), StorageTypeProto::DISK);
    auto state = block_manager_->GetBlockUCState(block_id);
    ASSERT_EQ(state, BlockUCState::kComplete);
    // first block
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(bip.num_bytes(), 100);
    // second block
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id + 1, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(bip.block_id(), block_id + 1);
    EXPECT_EQ(bip.gen_stamp(), gs + 1);
    EXPECT_EQ(bip.num_bytes(), 0);

    //// third block, first one is complete
    FsyncRequestProto sync_request;
    sync_request.set_src(path);
    sync_request.set_client("client");
    sync_request.set_lastblocklength(100);
    ASSERT_TRUE(!ns_->Fsync(path, sync_request).HasException());
    // second block
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id + 1, &bip));
    EXPECT_EQ(bip.num_bytes(), 100);
    add_request.mutable_previous()->set_blockid(block_id);
    add_request.mutable_previous()->set_generationstamp(gs);
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(), JavaExceptions::IOException());
    ASSERT_EQ(status.message(),
              "Add block on a non-empty block: block { B" +
                  std::to_string(block_id + 1) + ", num_bytes 100, gs " +
                  std::to_string(gs + 1) + " }");
  }
  {
    //// third block, first is not completed
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    DEFER([&]() { ns_->Delete(path, false); });
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;
    auto status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    auto block_id = add_response.block().b().blockid();
    ASSERT_FALSE(status.HasException());
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_FALSE(status.HasException());
    // first block
    auto state = block_manager_->GetBlockUCState(block_id);
    ASSERT_EQ(state, BlockUCState::kCommitted);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(bip.block_id(), block_id);
    EXPECT_GT(bip.gen_stamp(), 0);
    EXPECT_EQ(bip.num_bytes(), 0);
    // second block
    state = block_manager_->GetBlockUCState(block_id + 1);
    ASSERT_EQ(state, BlockUCState::kUnderConstruction);
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id + 1, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(bip.block_id(), block_id + 1);
    EXPECT_GT(bip.gen_stamp(), 0);
    EXPECT_EQ(bip.num_bytes(), 0);
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    status = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, &add_response);
    ASSERT_STREQ(status.ExceptionStr().c_str(),
                 JavaExceptions::NotReplicatedYetException());
    ASSERT_EQ(status.message(), "Not replicated yet: /test");
  }
  {
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    auto p = MakePermission();
    ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    std::string path2 = "/456";
    ASSERT_TRUE(!ns_->RenameTo2(path, path2, true).HasException());
    CompleteRequestProto complete_request;
    complete_request.set_src(path);
    complete_request.set_clientname(create_request.clientname());
    ASSERT_EQ(ns_->CompleteFile(path, complete_request).exception(),
              JavaExceptions::Exception::kFileNotFoundException);
    complete_request.set_fileid(8888);
    ASSERT_EQ(ns_->CompleteFile(path, complete_request).exception(),
              JavaExceptions::Exception::kFileNotFoundException);
    complete_request.set_fileid(create_response.fs().fileid());
    ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
    auto s = ns_->Delete(path2, true);
    ASSERT_TRUE(!s.HasException());
    ASSERT_TRUE(s.IsOK());
  }
}

TEST_F(NameSpaceTest, AddBlockFailTest) {
  AddDatanode("***********", "datanode2");
  cnetpp::base::IPAddress client_ip("***********");
  std::string path = "/test_file_add_block";

  // 1. create a file
  CreateRequestProto create_req = MakeCreateRequest();
  CreateResponseProto create_resp;
  PermissionStatus perm = MakePermission();
  Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
  ASSERT_TRUE(st.IsOK());
  DEFER([&]() { ns_->Delete(path, false); });

  // 2. add first block
  AddBlockRequestProto add_req = MakeAddBlockRequest();
  AddBlockResponseProto add_resp;
  st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
  ASSERT_TRUE(st.IsOK());
  auto prev_exblk = add_resp.block().b();

  // 3. commit first block and acquire second block, fail
  BlockManager::RepeatedIncBlockReport report;
  MakeReport(prev_exblk.blockid(),
             prev_exblk.generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);

  add_req = MakeAddBlockRequest();
  // previous
  ExtendedBlockProto* prev = add_req.mutable_previous();
  prev->CopyFrom(prev_exblk);
  prev->set_numbytes(100);
  // excludednodes
  cloudfs::DatanodeInfoProto* dn = add_req.add_excludenodes();
  dn->set_dfsused(100);
  dn->set_capacity(100);
  dn->mutable_id()->set_ipaddr("***********");
  dn->mutable_id()->set_datanodeuuid("datanode1");
  dn = add_req.add_excludenodes();
  dn->set_dfsused(100);
  dn->set_capacity(100);
  dn->mutable_id()->set_ipaddr("***********");
  dn->mutable_id()->set_datanodeuuid("datanode2");

  // previous block is ready to commit, but new block fail to choose targets
  add_resp.Clear();
  st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
  ASSERT_FALSE(st.IsOK()) << st.ToString();
  ASSERT_EQ(st.exception(), JavaExceptions::kIOException);

  // make sure previous block is NOT updated, both BlockInfo, BIP and INode
  {
    auto& blk_slice = block_manager_->TestOnlyGetSlice(prev_exblk.blockid());
    std::shared_lock<BlockMapSlice> guard(*blk_slice);
    BlockInfo* bi = blk_slice->Locate(prev_exblk.blockid());
    EXPECT_EQ(bi->id(), prev_exblk.blockid());
    EXPECT_EQ(bi->gs(), prev_exblk.generationstamp());
    EXPECT_EQ(bi->num_bytes(), 0);
    EXPECT_FALSE(bi->HasBeenCommitted());

    BlockInfoProto prev_bip;
    EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(prev_exblk.blockid(), &prev_bip));
    EXPECT_EQ(prev_bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(prev_bip.block_id(), prev_exblk.blockid());
    EXPECT_EQ(prev_bip.gen_stamp(), prev_exblk.generationstamp());
    EXPECT_EQ(prev_bip.num_bytes(), 0);

    INode inode;
    EXPECT_TRUE(ns_->GetINode(prev_bip.inode_id(), &inode));
    EXPECT_EQ(inode.blocks_size(), 1);
    const BlockProto& prev_bp = inode.blocks(0);
    EXPECT_EQ(prev_bp.blockid(), prev_exblk.blockid());
    EXPECT_EQ(prev_bp.genstamp(), prev_exblk.generationstamp());
    EXPECT_EQ(prev_bp.numbytes(), 0);
  }

  // 4. commit first block and acquire second block, success
  add_req.clear_excludenodes();
  add_resp.Clear();
  st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
  EXPECT_TRUE(st.IsOK()) << st.ToString();

  // make sure previous block is updated, both BlockInfo, BIP and INode
  {
    auto& blk_slice = block_manager_->TestOnlyGetSlice(prev_exblk.blockid());
    std::shared_lock<BlockMapSlice> guard(*blk_slice);
    BlockInfo* bi = blk_slice->Locate(prev_exblk.blockid());
    EXPECT_EQ(bi->id(), prev_exblk.blockid());
    EXPECT_EQ(bi->gs(), prev_exblk.generationstamp());
    EXPECT_EQ(bi->num_bytes(), 100);
    EXPECT_TRUE(bi->HasBeenCommitted());

    BlockInfoProto prev_bip;
    EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(prev_exblk.blockid(), &prev_bip));
    EXPECT_EQ(prev_bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(prev_bip.block_id(), prev_exblk.blockid());
    EXPECT_EQ(prev_bip.gen_stamp(), prev_exblk.generationstamp());
    EXPECT_EQ(prev_bip.num_bytes(), 100);

    INode inode;
    EXPECT_TRUE(ns_->GetINode(prev_bip.inode_id(), &inode));
    EXPECT_EQ(inode.blocks_size(), 2);
    const BlockProto& prev_bp = inode.blocks(0);
    EXPECT_EQ(prev_bp.blockid(), prev_exblk.blockid());
    EXPECT_EQ(prev_bp.genstamp(), prev_exblk.generationstamp());
    EXPECT_EQ(prev_bp.numbytes(), 100);
  }
}

TEST_F(NameSpaceTest, PathAndIDConsistencyAddBlock) {
  std::string path = "/test_file_1";
  std::string path2 = "/test_file_2";
  cnetpp::base::IPAddress client_ip("***********");

  // 1. @path not exists, @fileid not set -> fail
  {
    AddBlockRequestProto add_req = MakeAddBlockRequest();
    AddBlockResponseProto add_resp;
    Status st =
        ns_->AddBlock(path2, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kFileNotFoundException);
  }

  // 2. @path not exists, @fileid set -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path2, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 3. @path exists, @fileid not set -> use @path
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.clear_fileid();
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 4. @path exists, @fileid set and matched -> use either
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 5. @path exists, @fileid set but mismatched -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    create_req = MakeCreateRequest();
    create_resp.Clear();
    st = ns_->CreateFile(path2, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path2, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());

    // target pointed by @path has no lastblock
    GetBlockLocationsRequestProto get_req;
    get_req.set_src(path);
    get_req.set_offset(0);
    get_req.set_length(100);
    GetBlockLocationsResponseProto get_resp;
    st = ns_->GetBlockLocation(
        path, NetworkLocationInfo(client_ip), get_req, &get_resp, ugi_);
    ASSERT_TRUE(st.IsOK());
    ASSERT_FALSE(get_resp.locations().has_lastblock());

    // target pointed by @fileid has lastblock
    get_req.Clear();
    get_req.set_src(path2);
    get_req.set_offset(0);
    get_req.set_length(100);
    get_resp.Clear();
    st = ns_->GetBlockLocation(
        path2, NetworkLocationInfo(client_ip), get_req, &get_resp, ugi_);
    ASSERT_TRUE(st.IsOK());
    ASSERT_TRUE(get_resp.locations().has_lastblock());
  }
}

TEST_F(NameSpaceTest, PathAndIDConsistencyCompleteFile) {
  std::string path = "/test_file_1";
  std::string path2 = "/test_file_2";
  std::string client = "client_1";
  std::string client2 = "client_2";
  cnetpp::base::IPAddress client_ip("***********");

  // 1. @path not exists, @fileid not set -> fail
  {
    CompleteRequestProto comp_req;
    comp_req.set_src(path2);
    comp_req.clear_fileid();
    Status st = ns_->CompleteFile(path2, comp_req);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kFileNotFoundException);
  }

  // 2. @path not exists, @fileid set -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    CompleteRequestProto comp_req;
    comp_req.set_src(path2);
    comp_req.set_fileid(create_resp.fs().fileid());
    comp_req.set_clientname(create_req.clientname());
    st = ns_->CompleteFile(path2, comp_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 3. @path exists, @fileid not set -> use @path
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    CompleteRequestProto comp_req;
    comp_req.set_src(path);
    comp_req.clear_fileid();
    comp_req.set_clientname(create_req.clientname());
    st = ns_->CompleteFile(path, comp_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 4. @path exists, @fileid set and matched -> use either
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    CompleteRequestProto comp_req;
    comp_req.set_src(path);
    comp_req.set_fileid(create_resp.fs().fileid());
    comp_req.set_clientname(create_req.clientname());
    st = ns_->CompleteFile(path, comp_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 5. @path exists, @fileid set but mismatched -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    create_req.set_clientname(client);
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });
    uint64_t fileid = create_resp.fs().fileid();

    create_req = MakeCreateRequest();
    create_resp.Clear();
    create_req.set_clientname(client2);
    st = ns_->CreateFile(path2, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path2, false); });
    uint64_t fileid2 = create_resp.fs().fileid();

    CompleteRequestProto comp_req;
    comp_req.set_src(path);
    comp_req.set_fileid(fileid2);
    comp_req.set_clientname(client2);
    st = ns_->CompleteFile(path, comp_req);
    ASSERT_TRUE(st.IsOK());

    bool got = ns_->lease_manager()->CheckLease("client_1", fileid);
    ASSERT_TRUE(got);
    got = ns_->lease_manager()->CheckLease("client_2", fileid2);
    ASSERT_FALSE(got);
  }
}

TEST_F(NameSpaceTest, PathAndIDConsistencyFsync) {
  std::string path = "/test_file_1";
  std::string path2 = "/test_file_2";
  std::string client = "client_1";
  std::string client2 = "client_2";
  cnetpp::base::IPAddress client_ip("***********");

  // 1. @path not exists, @fileid not set -> fail
  {
    FsyncRequestProto fsync_req;
    fsync_req.set_src(path);
    fsync_req.set_client(client);
    Status st = ns_->Fsync(path, fsync_req);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kFileNotFoundException);
  }

  // 2. @path not exists, @fileid set -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    FsyncRequestProto fsync_req;
    fsync_req.set_src(path2);
    fsync_req.set_client(create_req.clientname());
    fsync_req.set_fileid(create_resp.fs().fileid());
    st = ns_->Fsync(path2, fsync_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 3. @path exists, @fileid not set -> use @path
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    FsyncRequestProto fsync_req;
    fsync_req.set_src(path);
    fsync_req.set_client(create_req.clientname());
    st = ns_->Fsync(path, fsync_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 4. @path exists, @fileid set and matched -> use either
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    FsyncRequestProto fsync_req;
    fsync_req.set_src(path);
    fsync_req.set_client(create_req.clientname());
    fsync_req.set_fileid(create_resp.fs().fileid());
    st = ns_->Fsync(path, fsync_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 5. @path exists, @fileid set but mismatched -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    CreateResponseProto create_resp;
    create_req.set_src(path);
    create_req.set_clientname(client);
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });
    uint64_t fileid = create_resp.fs().fileid();

    create_req = MakeCreateRequest();
    create_req.set_src(path2);
    create_req.set_clientname(client2);
    create_resp.Clear();
    st = ns_->CreateFile(path2, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path2, false); });
    uint64_t fileid2 = create_resp.fs().fileid();

    FsyncRequestProto fsync_req;
    fsync_req.set_src(path);
    fsync_req.set_client(client);
    fsync_req.set_fileid(fileid2);
    st = ns_->Fsync(path, fsync_req);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kLeaseExpiredException);

    fsync_req.set_src(path);
    fsync_req.set_client(client2);
    fsync_req.set_fileid(fileid2);
    st = ns_->Fsync(path, fsync_req);
    ASSERT_TRUE(st.IsOK());
  }
}

TEST_F(NameSpaceTest, PathAndIDConsistencyGetAdditionalDatanode) {
  std::string path = "/test_file_1";
  std::string path2 = "/test_file_2";
  std::string client = "client_1";
  cnetpp::base::IPAddress client_ip("***********");
  std::vector<std::string> dnips =
      { "***********", "***********0", "***********0" };
  std::vector<std::string> dnnms =
      { "datanode2", "datanode3", "datanode4" };
  for (int i = 0; i < dnips.size(); i++) {
    AddDatanode(dnips[i], dnnms[i]);
  }

  // 1. @path not exists, @fileid not set -> fail
  {
    GetAdditionalDatanodeRequestProto getdn_req;
    FillGetAdditionalDatanodeRequest(
        &getdn_req,
        {{ dnips[0], dnnms[0] },
         { dnips[1], dnnms[1] },
         { dnips[2], dnnms[2] }},
        1);
    getdn_req.set_src(path);
    GetAdditionalDatanodeResponseProto getdn_resp;
    Status st = ns_->SyncGetAdditionalDatanode(
        path, client_ip, &getdn_req, &getdn_resp);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kFileNotFoundException);
  }

  // 2. @path not exists, @fileid set -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(3);
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
    ASSERT_EQ(add_resp.block().locs().size(), 3);

    GetAdditionalDatanodeRequestProto getdn_req;
    FillGetAdditionalDatanodeRequest(
        &getdn_req,
        {{ dnips[0], dnnms[0] },
         { dnips[1], dnnms[1] },
         { dnips[2], dnnms[2] }},
        1);
    getdn_req.set_src(path2);
    getdn_req.set_fileid(create_resp.fs().fileid());
    getdn_req.mutable_blk()->CopyFrom(add_resp.block().b());
    GetAdditionalDatanodeResponseProto getdn_resp;
    st = ns_->SyncGetAdditionalDatanode(
        path2, client_ip, &getdn_req, &getdn_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 3. @path exists, @fileid not set -> use @path
  {
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(3);
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
    ASSERT_EQ(add_resp.block().locs().size(), 3);

    GetAdditionalDatanodeRequestProto getdn_req;
    FillGetAdditionalDatanodeRequest(
        &getdn_req,
        {{ dnips[0], dnnms[0] },
         { dnips[1], dnnms[1] },
         { dnips[2], dnnms[2] }},
        1);
    getdn_req.set_src(path);
    getdn_req.mutable_blk()->CopyFrom(add_resp.block().b());
    GetAdditionalDatanodeResponseProto getdn_resp;
    st = ns_->SyncGetAdditionalDatanode(
        path, client_ip, &getdn_req, &getdn_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 4. @path exists, @fileid set and matched -> use either
  {
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(3);
    CreateResponseProto create_resp;
    PermissionStatus perm = MakePermission();
    Status st = ns_->CreateFile(path, perm, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_fileid(create_resp.fs().fileid());
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip, default_rpc_info, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
    ASSERT_EQ(add_resp.block().locs().size(), 3);

    GetAdditionalDatanodeRequestProto getdn_req;
    FillGetAdditionalDatanodeRequest(
        &getdn_req,
        {{ dnips[0], dnnms[0] },
         { dnips[1], dnnms[1] },
         { dnips[2], dnnms[2] }},
        1);
    getdn_req.set_src(path);
    getdn_req.mutable_blk()->CopyFrom(add_resp.block().b());
    getdn_req.set_fileid(create_resp.fs().fileid());
    GetAdditionalDatanodeResponseProto getdn_resp;
    st = ns_->SyncGetAdditionalDatanode(
        path, client_ip, &getdn_req, &getdn_resp);
    ASSERT_TRUE(st.IsOK());
  }

  // 5. @path exists, @fileid set but mismatched -> use @fileid
  // XXX no testable difference, so skip this case temporarily
}

TEST_F(NameSpaceTest, PathAndIDConsistencyAbandonBlock) {
  std::string path = "/test_file_1";
  std::string path2 = "/test_file_2";
  std::string client = "client_1";
  std::string client2 = "client_2";
  BlockID invalid_blkid = 9999;
  cnetpp::base::IPAddress client_ip("***********");

  // 1. @path not exists, @fileid not set -> fail
  {
    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path);
    Status st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kFileNotFoundException);
  }

  // 2. @path not exists, @fileid set -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    PermissionStatus p = MakePermission();
    CreateResponseProto create_resp;
    Status st = ns_->CreateFile(path, p, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path2);
    abandon_req.set_holder(create_req.clientname());
    abandon_req.set_fileid(create_resp.fs().fileid());
    st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 3. @path exists, @fileid not set -> use @path
  {
    CreateRequestProto create_req = MakeCreateRequest();
    PermissionStatus p = MakePermission();
    CreateResponseProto create_resp;
    Status st = ns_->CreateFile(path, p, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path);
    abandon_req.set_holder(create_req.clientname());
    st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 4. @path exists, @fileid set and matched -> use either
  {
    CreateRequestProto create_req = MakeCreateRequest();
    PermissionStatus p = MakePermission();
    CreateResponseProto create_resp;
    Status st = ns_->CreateFile(path, p, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });

    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path);
    abandon_req.set_holder(create_req.clientname());
    abandon_req.set_fileid(create_resp.fs().fileid());
    st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_TRUE(st.IsOK());
  }

  // 5. @path exists, @fileid set but mismatched -> use @fileid
  {
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_clientname(client);
    PermissionStatus p = MakePermission();
    CreateResponseProto create_resp;
    Status st = ns_->CreateFile(path, p, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path, false); });
    uint64_t fileid = create_resp.fs().fileid();

    create_req = MakeCreateRequest();
    create_req.set_clientname(client2);
    create_resp.Clear();
    st = ns_->CreateFile(path2, p, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());
    DEFER([&]() { ns_->Delete(path2, false); });
    uint64_t fileid2 = create_resp.fs().fileid();

    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path);
    abandon_req.set_holder(client);
    abandon_req.set_fileid(fileid2);
    st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_FALSE(st.IsOK());
    ASSERT_EQ(st.exception(), JavaExceptions::Exception::kLeaseExpiredException);

    abandon_req.Clear();
    abandon_req.mutable_b()->set_blockid(invalid_blkid);
    abandon_req.set_src(path);
    abandon_req.set_holder(client2);
    abandon_req.set_fileid(fileid2);
    st = ns_->AbandonBlock(path, abandon_req);
    ASSERT_TRUE(st.IsOK());
  }
}

TEST_F(NameSpaceTest, LegacyGetBlockLocation) {
  ns_->StopLeaseMonitor();
  gflags::SetCommandLineOption("namespace_read_full_detail_blocks", "true");
  ASSERT_TRUE(FLAGS_namespace_read_full_detail_blocks);

  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  auto add_request = MakeAddBlockRequest();
  auto p = MakePermission();

  std::string path = "/test";
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  AddBlockResponseProto add_response;
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response)
           .HasException());

  GetBlockLocationsRequestProto get_request;
  get_request.set_src(path);
  get_request.set_offset(0);
  get_request.set_length(10);
  GetBlockLocationsResponseProto get_response;
  auto ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  auto& locs = get_response.locations();
  ASSERT_EQ(locs.filelength(), 0);
  ASSERT_EQ(locs.blocks_size(), 0);
  ASSERT_EQ(locs.underconstruction(), true);
  ASSERT_EQ(locs.islastblockcomplete(), false);
  auto& last = locs.lastblock();

  ASSERT_EQ(last.b().blockid(), add_response.block().b().blockid());
  ASSERT_EQ(last.b().generationstamp(),
            add_response.block().b().generationstamp());
  ASSERT_EQ(last.offset(), 0);
  ASSERT_EQ(last.locs_size(), 1);
  ASSERT_EQ(last.locs(0).id().ipaddr(), "***********");
  ASSERT_EQ(last.storagetypes_size(), 1);
  ASSERT_EQ(last.storagetypes(0), StorageTypeProto::DISK);
  ASSERT_EQ(last.storageids_size(), 1);
  ASSERT_EQ(last.has_originallocatedblock(), false);

  CompleteRequestProto complete_request;
  complete_request.set_src(path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(0xdeadbeef);
  // not including block report here,
  // so block could not be committed or complete.
  ret = ns_->CompleteFile(path, complete_request);
  ASSERT_FALSE(ret.HasException());
  ASSERT_TRUE(ret.IsFalse());

  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  auto new_locs = get_response.locations();
  ASSERT_EQ(new_locs.filelength(), 0);
  ASSERT_EQ(new_locs.blocks_size(), 0);
  // TODO(liyuan) add block report here
  // ASSERT_EQ(new_locs.underconstruction(), false);
  // ASSERT_EQ(new_locs.islastblockcomplete(), true);

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             0xdeadbeef,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  ret = ns_->CompleteFile(path, complete_request);
  ASSERT_FALSE(ret.HasException());
  ASSERT_TRUE(!ret.IsFalse());
  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  new_locs = get_response.locations();
  ASSERT_EQ(new_locs.filelength(), 0xdeadbeef);
  ASSERT_EQ(new_locs.blocks_size(), 1);
  ASSERT_EQ(new_locs.blocks(0).b().numbytes(), 0xdeadbeef);

  std::string path2("/test2");
  ASSERT_TRUE(!ns_->CreateFile(path2, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  add_request.clear_previous();
  for (int i = 0; i < 3; ++i) {
    ASSERT_TRUE(
        !ns_->AddBlock(
                path2, client_ip, default_rpc_info, add_request, &add_response)
             .HasException());
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    add_request.mutable_previous()->set_numbytes(100);
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               100,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);
    ns_->GetBlockReportManager()->IncrementalBlockReport(
        1, "datanode1", report);
  }
  auto last_block_id = add_response.block().b().blockid();
  complete_request.Clear();
  complete_request.set_src(path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(100);
  ret = ns_->CompleteFile(path2, complete_request);

  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  get_response.clear_locations();

  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  get_response.clear_locations();

  get_request.set_length(101);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  get_response.clear_locations();

  get_request.set_length(300);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  ASSERT_FALSE(ret.HasException());
  ASSERT_FALSE(ret.IsFalse());
  get_request.set_length(300);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  get_request.set_length(1000);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  get_request.set_offset(30);
  get_request.set_length(10);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 2);
  get_response.clear_locations();

  get_request.set_offset(30);
  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 2);
  ASSERT_EQ(get_response.locations().blocks(1).b().blockid(),
            last_block_id - 1);
  get_response.clear_locations();

  get_request.set_offset(100);
  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 1);
  get_response.clear_locations();

  get_request.set_offset(100);
  get_request.set_length(500);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 1);
  ASSERT_EQ(get_response.locations().blocks(1).b().blockid(), last_block_id);
  get_response.clear_locations();

  auto s = ns_->Delete(path2, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
}

TEST_F(NameSpaceTest, OptimizedGetBlockLocation) {
  ns_->StopLeaseMonitor();
  gflags::SetCommandLineOption("namespace_read_full_detail_blocks", "false");
  ASSERT_FALSE(FLAGS_namespace_read_full_detail_blocks);

  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  auto add_request = MakeAddBlockRequest();
  auto p = MakePermission();

  std::string path = "/test";
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  AddBlockResponseProto add_response;
  ASSERT_TRUE(
      !ns_->AddBlock(
              path, client_ip, default_rpc_info, add_request, &add_response)
           .HasException());

  GetBlockLocationsRequestProto get_request;
  get_request.set_src(path);
  get_request.set_offset(0);
  get_request.set_length(10);
  GetBlockLocationsResponseProto get_response;
  auto ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  auto& locs = get_response.locations();
  ASSERT_EQ(locs.filelength(), 0);
  ASSERT_EQ(locs.blocks_size(), 0);
  ASSERT_EQ(locs.underconstruction(), true);
  ASSERT_EQ(locs.islastblockcomplete(), false);
  auto& last = locs.lastblock();

  ASSERT_EQ(last.b().blockid(), add_response.block().b().blockid());
  ASSERT_EQ(last.b().generationstamp(),
            add_response.block().b().generationstamp());
  ASSERT_EQ(last.offset(), 0);
  ASSERT_EQ(last.locs_size(), 1);
  ASSERT_EQ(last.locs(0).id().ipaddr(), "***********");
  ASSERT_EQ(last.storagetypes_size(), 1);
  ASSERT_EQ(last.storagetypes(0), StorageTypeProto::DISK);
  ASSERT_EQ(last.storageids_size(), 1);
  ASSERT_EQ(last.has_originallocatedblock(), false);

  CompleteRequestProto complete_request;
  complete_request.set_src(path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(0xdeadbeef);
  // not including block report here,
  // so block should not be committed or completed.
  ret = ns_->CompleteFile(path, complete_request);
  ASSERT_FALSE(ret.HasException());
  ASSERT_TRUE(ret.IsFalse());

  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  auto new_locs = get_response.locations();
  ASSERT_EQ(new_locs.filelength(), 0);
  ASSERT_EQ(new_locs.blocks_size(), 0);
  // TODO(liyuan) add block report here
  // ASSERT_EQ(new_locs.underconstruction(), false);
  // ASSERT_EQ(new_locs.islastblockcomplete(), true);

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             0xdeadbeef,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  ret = ns_->CompleteFile(path, complete_request);
  ASSERT_FALSE(ret.HasException());
  ASSERT_TRUE(!ret.IsFalse());
  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  new_locs = get_response.locations();
  ASSERT_EQ(new_locs.filelength(), 0xdeadbeef);
  ASSERT_EQ(new_locs.blocks_size(), 1);
  ASSERT_EQ(new_locs.blocks(0).b().numbytes(), 0xdeadbeef);

  std::string path2("/test2");
  ASSERT_TRUE(!ns_->CreateFile(path2, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  add_request.clear_previous();
  for (int i = 0; i < 3; ++i) {
    ASSERT_TRUE(
        !ns_->AddBlock(
                path2, client_ip, default_rpc_info, add_request, &add_response)
             .HasException());
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    add_request.mutable_previous()->set_numbytes(100);
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               100,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);
    ns_->GetBlockReportManager()->IncrementalBlockReport(
        1, "datanode1", report);
  }
  auto last_block_id = add_response.block().b().blockid();
  complete_request.Clear();
  complete_request.set_src(path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(100);
  ret = ns_->CompleteFile(path2, complete_request);

  get_response.clear_locations();
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  get_response.clear_locations();

  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  get_response.clear_locations();

  get_request.set_length(101);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  get_response.clear_locations();

  get_request.set_length(300);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  ASSERT_FALSE(ret.HasException());
  ASSERT_FALSE(ret.IsFalse());
  get_request.set_length(300);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  get_request.set_length(1000);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 3);
  get_response.clear_locations();

  get_request.set_offset(30);
  get_request.set_length(10);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 2);
  get_response.clear_locations();

  get_request.set_offset(30);
  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 2);
  ASSERT_EQ(get_response.locations().blocks(1).b().blockid(),
            last_block_id - 1);
  get_response.clear_locations();

  get_request.set_offset(100);
  get_request.set_length(100);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 1);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 1);
  get_response.clear_locations();

  get_request.set_offset(100);
  get_request.set_length(500);
  ret = ns_->GetBlockLocation(
      path2, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(ret.HasException(), false);
  ASSERT_EQ(get_response.locations().blocks_size(), 2);
  ASSERT_EQ(get_response.locations().blocks(0).b().blockid(),
            last_block_id - 1);
  ASSERT_EQ(get_response.locations().blocks(1).b().blockid(), last_block_id);
  get_response.clear_locations();

  auto s = ns_->Delete(path2, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
}

TEST_F(NameSpaceTest, GetBlockLocationLocalityDefault) {
  FLAGS_enable_location_tag_by_rack_aware = true;
  FLAGS_enable_location_tag = false;

  auto reg2 =
      cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg2.mutable_datanodeid()->set_datanodeuuid("datanode2");
  cnetpp::base::IPAddress ip2("***********");
  datanode_manager_->Register(reg2.datanodeid(), &reg2, ip2);
  datanode_manager_->RefreshConfig();
  DatanodeManager::RepeatedCmds cmds;
  HeartbeatRequestProto request;
  GetDiskRequest(&request, "datanode2", reg2, 102400);
  datanode_manager_->Heartbeat(request, &cmds);

  auto reg3 =
      cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg3.mutable_datanodeid()->set_datanodeuuid("datanode3");
  cnetpp::base::IPAddress ip3("***********0");
  request.Clear();
  GetDiskRequest(&request, "datanode3", reg3, 102400);
  datanode_manager_->Register(reg3.datanodeid(), &reg3, ip3);
  datanode_manager_->RefreshConfig();
  cmds.Clear();
  datanode_manager_->Heartbeat(request, &cmds);

  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  auto add_request = MakeAddBlockRequest();
  auto p = MakePermission();

  std::string path = "/test";
  DEFER([&]() { ns_->Delete(path, true); });
  CreateResponseProto create_response;
  AddBlockResponseProto add_response;

  // 1 * local, 1 * local rack, 1 local dc

  AddFile(path, 100, 3, false, &add_response, &create_response);
  GetBlockLocationsRequestProto get_request;
  get_request.set_src(path);
  get_request.set_offset(0);
  get_request.set_length(10);
  GetBlockLocationsResponseProto get_response;
  auto ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(get_response.locations().filelength(), 0);
  ASSERT_EQ(get_response.locations().blocks_size(), 0);
  ASSERT_EQ(get_response.locations().underconstruction(), true);
  ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
  auto& locs = get_response.locations().lastblock();
  ASSERT_EQ(locs.locs_size(), 3);
  ASSERT_EQ(locs.locs(0).id().ipaddr(), "***********");
  ASSERT_EQ(locs.locs(1).id().ipaddr(), "***********");
  ASSERT_EQ(locs.locs(2).id().ipaddr(), "***********0");

  // stale datanode
  ns_->Delete(path, false);
  AddFile(path, 100, 3, false, &add_response, &create_response);
  datanode_manager_->GetDatanodeFromId(2)->set_force_stale(true);
  get_response.Clear();
  ret = ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
  ASSERT_EQ(get_response.locations().filelength(), 0);
  ASSERT_EQ(get_response.locations().blocks_size(), 0);
  ASSERT_EQ(get_response.locations().underconstruction(), true);
  ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
  auto& locs1 = get_response.locations().lastblock();
  ASSERT_EQ(locs1.locs_size(), 3);
  ASSERT_EQ(locs1.locs(0).id().ipaddr(), "***********");
  ASSERT_EQ(locs1.locs(1).id().ipaddr(), "***********0");
  ASSERT_EQ(locs1.locs(2).id().ipaddr(), "***********");
}

TEST_F(NameSpaceTest, RenameTo2GC) {
  ns_->StopBGDeletionWorker();

  ASSERT_EQ(block_manager_->GetBlockNum(), 0);

  ASSERT_TRUE(!ns_->MkDirs("/x/1", MakePermission(), true).HasException());
  auto s = ns_->MkDirs("/x/2", MakePermission(), true);
  LOG(INFO) << s.ExceptionStr();
  LOG(INFO) << s.ToString();
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(!ns_->RenameTo2("/x", "/y", true).HasException());

  {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(
                "/y/", NetworkLocationInfo(), get_request, &get_response, ugi_)
             .HasException());
    ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);
    auto listing = get_response.dirlist();
    ASSERT_EQ(listing.partiallisting(0).path(), "1");
    ASSERT_EQ(listing.partiallisting(1).path(), "2");
  }
  {
    auto num_deleted_dirs = 0;
    auto num_deleted_files = 0;
    ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      if (inode.type() == INode::kFile) {
        num_deleted_files++;
      } else if (inode.type() == INode::kDirectory) {
        num_deleted_dirs++;
      }
      return true;
    });
    ASSERT_EQ(0, num_deleted_dirs);
    ASSERT_EQ(0, num_deleted_files);
  }
  {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(
                "/y/", NetworkLocationInfo(), get_request, &get_response, ugi_)
             .HasException());
    ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);
    auto listing = get_response.dirlist();
    ASSERT_EQ(listing.partiallisting(0).path(), "1");
    ASSERT_EQ(listing.partiallisting(1).path(), "2");
  }

  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
}

TEST_F(NameSpaceTest, RenameTo2) {
  ns_->StopBGDeletionWorker();

  ASSERT_EQ(block_manager_->GetBlockNum(), 0);

  ASSERT_EQ(ns_->RenameTo2("/abc", "/cba", true).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId);
  ASSERT_EQ(ns_->RenameTo2("/", "/cba", true).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId);

  CreateResponseProto create_response;
  AddBlockResponseProto add_response;
  AddFile("/abc", 100, 1, true, &add_response, &create_response);
  auto fst_blk = add_response.block().b();
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  ASSERT_EQ(ns_->RenameTo2("/abc", "/abc", true).exception(),
            JavaExceptions::Exception::kFileAlreadyExistsException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  ASSERT_EQ(ns_->RenameTo2("/abc", "/abc", false).exception(),
            JavaExceptions::Exception::kFileAlreadyExistsException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  ASSERT_EQ(ns_->RenameTo2("/abc", "/", false).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);

  PermissionStatus p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs("/dir/sub", p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);
  ASSERT_EQ(ns_->RenameTo2("/dir", "/dir/sub", true).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);

  ASSERT_EQ(ns_->RenameTo2("/abc", "/dir/sub", true).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);
  ASSERT_EQ(ns_->RenameTo2("/dir/sub", "/abc", true).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);

  ASSERT_TRUE(!ns_->RenameTo2("/abc", "/dir/sub/abc", true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 3);
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(!ns_->GetListing("/dir/sub",
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  auto listing = get_response.dirlist();
  ASSERT_EQ(listing.partiallisting(0).path(), "abc");

  AddFile("/another", 100, 1, true, &add_response, &create_response);
  auto snd_blk = add_response.block().b();
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);
  ASSERT_EQ(block_manager_->GetBlockNum(), 2);

  ASSERT_EQ(ns_->RenameTo2("/another", "/dir/sub/abc", false).exception(),
            JavaExceptions::Exception::kFileAlreadyExistsException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);
  ASSERT_TRUE(!ns_->RenameTo2("/another", "/dir/sub/abc", true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  auto num_deleted_dirs = 0;
  auto num_deleted_files = 0;
  ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
    if (inode.type() == INode::kFile) {
      num_deleted_files++;
    } else if (inode.type() == INode::kDirectory) {
      num_deleted_dirs++;
    }
    return true;
  });
  ASSERT_EQ(0, num_deleted_dirs);
  ASSERT_EQ(0, num_deleted_files);

  ASSERT_EQ(block_manager_->GetBlockNum(), 2);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(fst_blk.blockid(), &bip));
  EXPECT_EQ(bip.block_id(), fst_blk.blockid());
  EXPECT_EQ(bip.gen_stamp(), fst_blk.generationstamp());
  EXPECT_EQ(bip.num_bytes(), 100);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(snd_blk.blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), snd_blk.blockid());
  EXPECT_EQ(bip.gen_stamp(), snd_blk.generationstamp());
  EXPECT_EQ(bip.num_bytes(), 100);
  get_response.Clear();
  ASSERT_TRUE(!ns_->GetListing("/dir/sub",
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  listing = get_response.dirlist();
  ASSERT_EQ(listing.partiallisting(0).path(), "abc");

  auto s = ns_->MkDirs("/another_dir", p, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 5);
  get_response.Clear();
  ASSERT_TRUE(
      !ns_->GetListing(
              "/", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  ASSERT_EQ(ns_->RenameTo2("/another_dir", "/dir/sub", true).exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 5);
  s = ns_->Delete("/dir/sub/abc", true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(snd_blk.blockid(), &bip));
  EXPECT_EQ(bip.block_id(), snd_blk.blockid());
  EXPECT_EQ(bip.gen_stamp(), snd_blk.generationstamp());
  EXPECT_EQ(bip.num_bytes(), 100);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 5);
  ASSERT_TRUE(!ns_->RenameTo2("/another_dir", "/dir/sub", true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 5);

  num_deleted_dirs = 0;
  num_deleted_files = 0;
  ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
    if (inode.type() == INode::kFile) {
      num_deleted_files++;
    } else if (inode.type() == INode::kDirectory) {
      LOG(INFO) << "INode::id " << inode.id() << ", INode::name() "
                << inode.name();
      num_deleted_dirs++;
    }
    return true;
  });
  ASSERT_EQ(1, num_deleted_dirs);  // /dir/sub, /another_dir not do delay-delete
  ASSERT_EQ(0, num_deleted_files);

  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));

  num_deleted_dirs = 0;
  num_deleted_files = 0;
  ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
    if (inode.type() == INode::kFile) {
      num_deleted_files++;
    } else if (inode.type() == INode::kDirectory) {
      num_deleted_dirs++;
    }
    return true;
  });
  ASSERT_EQ(0, num_deleted_dirs);
  ASSERT_EQ(0, num_deleted_files);

  auto create_request2 = MakeCreateRequest();
  create_request2.set_createparent(true);
  CreateResponseProto create_response2;
  auto p2 = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs("/x/test", p2, true).HasException());
  ASSERT_TRUE(
      !ns_->CreateFile("/x/test/123", p2, create_request2, &create_response2)
           .HasException());
  uint64_t mtime = create_response2.fs().modification_time();
  uint64_t inode_id = ns_->last_inode_id();
  ASSERT_TRUE(ns_->RenameTo("/x/test/123", "/dir").IsOK());
  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  ASSERT_EQ(inode.mtime(), mtime);
}

TEST_F(NameSpaceTest, RenameToGC) {
  ns_->StopBGDeletionWorker();

  ASSERT_EQ(block_manager_->GetBlockNum(), 0);

  ASSERT_TRUE(!ns_->MkDirs("/xx/1", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/xx/2", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->RenameTo("/xx", "/yy").HasException());

  {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(
                "/yy/", NetworkLocationInfo(), get_request, &get_response, ugi_)
             .HasException());
    ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);
    auto listing = get_response.dirlist();
    ASSERT_EQ(listing.partiallisting(0).path(), "1");
    ASSERT_EQ(listing.partiallisting(1).path(), "2");
  }
  {
    auto num_deleted_dirs = 0;
    auto num_deleted_files = 0;
    ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
      if (inode.type() == INode::kFile) {
        num_deleted_files++;
      } else if (inode.type() == INode::kDirectory) {
        num_deleted_dirs++;
      }
      return true;
    });
    ASSERT_EQ(0, num_deleted_dirs);
    ASSERT_EQ(0, num_deleted_files);
  }
  {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(
                "/yy/", NetworkLocationInfo(), get_request, &get_response, ugi_)
             .HasException());
    ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);
    auto listing = get_response.dirlist();
    ASSERT_EQ(listing.partiallisting(0).path(), "1");
    ASSERT_EQ(listing.partiallisting(1).path(), "2");
  }

  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
}

TEST_F(NameSpaceTest, RenameTo) {
  ASSERT_EQ(ns_->RenameTo("/foo", "/foo").exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(ns_->RenameTo("/", "/foo").exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId);

  std::string a_b = "/a/b";
  std::string a_b_parent = "/a";
  std::string no_parent = "/no/parent";
  std::string file_exists = "/file/exists";
  std::string some_dir = "/somedir";
  auto create_request = MakeCreateRequest();
  create_request.set_createparent(true);
  CreateResponseProto create_response;
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->CreateFile(a_b, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);
  ASSERT_TRUE(!ns_->CreateFile(file_exists, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  ASSERT_FALSE(ns_->RenameTo(a_b, no_parent).IsOK());

  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  ASSERT_FALSE(ns_->RenameTo(a_b, file_exists).IsOK());

  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  std::vector<cnetpp::base::StringPiece> file_exists_path_components;
  SplitPath(file_exists, &file_exists_path_components);
  INode file_exists_inode;
  ASSERT_EQ(
      ns_->GetLastINodeInPath(file_exists_path_components, &file_exists_inode),
      StatusCode::kOK);

  std::string another_file = "/file/another_file";
  std::vector<cnetpp::base::StringPiece> another_file_path_components;
  SplitPath(another_file, &another_file_path_components);
  INode another_file_inode;
  ASSERT_EQ(ns_->GetLastINodeInPath(another_file_path_components,
                                    &another_file_inode),
            StatusCode::kFileNotFound);

  ASSERT_TRUE(ns_->RenameTo(file_exists, another_file).IsOK());

  INode not_exist;
  ASSERT_EQ(ns_->GetLastINodeInPath(file_exists_path_components, &not_exist),
            StatusCode::kFileNotFound);

  ASSERT_EQ(ns_->GetLastINodeInPath(another_file_path_components,
                                    &another_file_inode),
            StatusCode::kOK);
  ASSERT_EQ(file_exists_inode.id(), another_file_inode.id());
  ASSERT_EQ(file_exists_inode.atime(), another_file_inode.atime());
  ASSERT_EQ(file_exists_inode.replication(), another_file_inode.replication());
  CHECK_NE(ns_->meta_storage()->GetINodeParentId(another_file_inode.id()),
           kInvalidINodeId);

  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);
  ASSERT_TRUE(!ns_->RenameTo("/file/another_file", file_exists).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 4);

  // TODO(liyuan) symlink rename test

  ASSERT_TRUE(!ns_->MkDirs("/some/dir", p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  ASSERT_EQ(ns_->RenameTo("/some/dir", "/some/dir/under").exception(),
            JavaExceptions::Exception::kIOException);
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  ASSERT_TRUE(!ns_->Delete("/some", true).HasException());

  ASSERT_TRUE(ns_->RenameTo(a_b, "/c").IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  GetListingRequestProto get_request;
  get_request.set_src("/");
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(
      !ns_->GetListing(
              "/", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  auto& dirlist = get_response.dirlist();
  ASSERT_EQ(dirlist.partiallisting_size(), kNumReservedINode + 3);
  ASSERT_EQ(dirlist.remainingentries(), 0);
  ASSERT_EQ(dirlist.partiallisting(0).filetype(), HdfsFileStatusProto::IS_DIR);
  ASSERT_EQ(dirlist.partiallisting(0).path(), "a");
  ASSERT_EQ(dirlist.partiallisting(1).filetype(), HdfsFileStatusProto::IS_FILE);
  LOG(INFO) << dirlist.ShortDebugString();
  ASSERT_EQ(dirlist.partiallisting(1).path(), "c");

  ASSERT_TRUE(ns_->RenameTo("/c", a_b).IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  ASSERT_TRUE(ns_->RenameTo(a_b_parent, some_dir).IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  get_response.clear_dirlist();
  ASSERT_TRUE(
      !ns_->GetListing(
              "/", NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  ASSERT_EQ(dirlist.partiallisting_size(), kNumReservedINode + 2);

  ASSERT_TRUE(ns_->RenameTo(some_dir, a_b_parent).IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 6);
  ASSERT_TRUE(!ns_->MkDirs(some_dir, p, true).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);
  ASSERT_TRUE(ns_->RenameTo(a_b_parent, some_dir).IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);
  get_response.clear_dirlist();
  ASSERT_TRUE(
      !ns_->GetListing(
              some_dir, NetworkLocationInfo(), get_request, &get_response, ugi_)
           .HasException());
  ASSERT_EQ(dirlist.partiallisting_size(), 1);
  ASSERT_EQ(dirlist.partiallisting(0).path(), "a");

  ASSERT_TRUE(!ns_->GetListing(some_dir + a_b_parent,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  ASSERT_EQ(dirlist.partiallisting_size(), 1);
  ASSERT_EQ(dirlist.partiallisting(0).path(), "b");

  ASSERT_TRUE(ns_->RenameTo(file_exists, "/file/ex").IsOK());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 7);

  ASSERT_TRUE(!ns_->CreateFile("/x/a", p, create_request, &create_response)
                   .HasException());
  ASSERT_TRUE(!ns_->MkDirs("/directory/a", p, true).HasException());
  ASSERT_FALSE(ns_->RenameTo("/x/a", "/directory").IsOK());

  ASSERT_TRUE(!ns_->MkDirs("/x/b", p, true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/directory/b", p, true).HasException());
  ASSERT_FALSE(ns_->RenameTo("/x/b", "/directory").IsOK());

  ASSERT_TRUE(!ns_->CreateFile("/x/c", p, create_request, &create_response)
                   .HasException());
  ASSERT_TRUE(
      !ns_->CreateFile("/directory/c", p, create_request, &create_response)
           .HasException());
  ASSERT_FALSE(ns_->RenameTo("/x/c", "/directory").IsOK());

  ASSERT_TRUE(!ns_->MkDirs("/x/d", p, true).HasException());
  ASSERT_TRUE(
      !ns_->CreateFile("/directory/d", p, create_request, &create_response)
           .HasException());
  ASSERT_FALSE(ns_->RenameTo("/x/d", "/directory").IsOK());

  ASSERT_TRUE(!ns_->MkDirs("/x/test", p, true).HasException());
  ASSERT_TRUE(
      !ns_->CreateFile("/x/test/123", p, create_request, &create_response)
           .HasException());
  uint64_t mtime = create_response.fs().modification_time();
  uint64_t inode_id = ns_->last_inode_id();
  ASSERT_TRUE(ns_->RenameTo("/x/test/123", "/directory").IsOK());
  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  ASSERT_EQ(inode.name(), "123");
  ASSERT_EQ(inode.mtime(), mtime);
}

TEST_F(NameSpaceTest, Complete) {
  ns_->Delete("/", true);

  cnetpp::base::IPAddress ip("***********");
  auto p = MakePermission();
  std::string test_dir = "/test_dir";
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  std::string path = test_dir + "/test";
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());

  CompleteRequestProto complete_request;
  complete_request.set_clientname(create_request.clientname());
  ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
  ns_->Delete(path, true);

  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  complete_request.set_fileid(create_response.fs().fileid());
  complete_request.set_clientname("nobody");
  ASSERT_EQ(ns_->CompleteFile(path, complete_request).exception(),
            JavaExceptions::Exception::kLeaseExpiredException);
  complete_request.set_clientname(create_request.clientname());
  ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
  ns_->Delete(path, true);

  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->CompleteFile(path, complete_request).exception(),
            JavaExceptions::Exception::kFileNotFoundException);  // wrong fileid
  ns_->Delete(path, true);

  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_TRUE(
      !ns_->AddBlock(path, ip, default_rpc_info, add_request, &add_response)
           .HasException());
  complete_request.set_fileid(create_response.fs().fileid());
  auto res = ns_->CompleteFile(path, complete_request);
  ASSERT_FALSE(res.HasException()) << res.ToString();
  ASSERT_TRUE(res.IsFalse());
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());
  ns_->Delete(path, true);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));

  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  add_request.Clear();
  add_request = MakeAddBlockRequest();
  add_response.Clear();
  ASSERT_TRUE(
      !ns_->AddBlock(path, ip, default_rpc_info, add_request, &add_response)
           .HasException());
  complete_request.set_fileid(create_response.fs().fileid());
  complete_request.mutable_last()->set_blockid(10086);  // wrong block id
  ASSERT_FALSE(ns_->CompleteFile(path, complete_request).IsOK());
  ns_->Delete(path, true);

  ASSERT_TRUE(!ns_->MkDirs("/dir", p, true).HasException());
  ASSERT_EQ(ns_->CompleteFile("/dir", complete_request).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ns_->Delete("/dir", true);

  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  ASSERT_TRUE(
      !ns_->AddBlock(path, ip, default_rpc_info, add_request, &add_response)
           .HasException());
  uint64_t len = 100;
  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  complete_request.mutable_last()->set_poolid(ns_->blockpool_id());
  complete_request.mutable_last()->set_blockid(
      add_response.block().b().blockid());
  complete_request.mutable_last()->set_generationstamp(
      add_response.block().b().generationstamp());
  complete_request.mutable_last()->set_numbytes(len);
  complete_request.set_fileid(create_response.fs().fileid());
  ASSERT_TRUE(ns_->CompleteFile(path, complete_request).IsOK());
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), len);

  // 'client' create file -> 'another' overwrite file
  // -> 'client' try  to complete
  auto s = ns_->Delete(path, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  CompleteRequestProto complete_request_1;
  complete_request_1.set_src(path);
  complete_request_1.set_clientname(create_request.clientname());
  complete_request_1.set_fileid(create_response.fs().fileid());
  create_request.set_clientname("another");
  create_request.set_createflag(cloudfs::CreateFlagProto::OVERWRITE);
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  CompleteRequestProto complete_request_2;
  complete_request_2.set_src(path);
  complete_request_2.set_clientname(create_request.clientname());
  complete_request_2.set_fileid(create_response.fs().fileid());
  ASSERT_TRUE(!ns_->CompleteFile(path, complete_request_2).HasException());
  ASSERT_EQ(ns_->CompleteFile(path, complete_request_1).exception(),
            JavaExceptions::Exception::kFileNotFoundException);

  std::string path2 = test_dir + "/123";
  auto create_request2 = MakeCreateRequest();
  CreateResponseProto create_response2;
  ASSERT_TRUE(!ns_->CreateFile(path2, p, create_request2, &create_response2)
                   .HasException());
  std::string path3 = test_dir + "/456";
  ASSERT_TRUE(!ns_->RenameTo2(path2, path3, true).HasException());
  CompleteRequestProto complete_request_3;
  complete_request_3.set_src(path2);
  complete_request_3.set_clientname(create_request2.clientname());
  ASSERT_EQ(ns_->CompleteFile(path2, complete_request_3).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  complete_request_3.set_fileid(8888);
  ASSERT_EQ(ns_->CompleteFile(path2, complete_request_3).exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  complete_request_3.set_fileid(create_response2.fs().fileid());
  ASSERT_TRUE(!ns_->CompleteFile(path2, complete_request_3).HasException());

  std::string path4 = test_dir + "/789";
  auto create_request4 = MakeCreateRequest();
  CreateResponseProto create_response4;
  ASSERT_TRUE(!ns_->CreateFile(path4, p, create_request4, &create_response4)
                   .HasException());
  std::thread another([&]() {
    LOG(INFO) << "Another thread started.";
    auto s = ns_->Delete(path4, true);
    ASSERT_TRUE(!s.HasException());
    ASSERT_TRUE(s.IsOK());
    auto create_request5 = MakeCreateRequest();
    CreateResponseProto create_response5;
    ASSERT_TRUE(!ns_->CreateFile(path4, p, create_request5, &create_response5)
                     .HasException());
    CompleteRequestProto complete_request5;
    complete_request5.set_src(path4);
    complete_request5.set_clientname(create_request5.clientname());
    ASSERT_TRUE(!ns_->CompleteFile(path4, complete_request5).HasException());
  });
  another.join();
  CompleteRequestProto complete_request4;
  complete_request4.set_src(path4);
  complete_request4.set_clientname(create_request4.clientname());
  ASSERT_TRUE(!ns_->CompleteFile(path4, complete_request4).HasException());

  std::string path6 = test_dir + "/889";
  auto create_request6 = MakeCreateRequest();
  CreateResponseProto create_response6;
  ASSERT_TRUE(!ns_->CreateFile(path6, p, create_request6, &create_response6)
                   .HasException());
  auto add_request8 = MakeAddBlockRequest();
  AddBlockResponseProto add_response8;
  ASSERT_TRUE(
      !ns_->AddBlock(path6, ip, default_rpc_info, add_request8, &add_response8)
           .HasException());
  len = 100;
  BlockManager::RepeatedIncBlockReport report8;
  MakeReport(add_response8.block().b().blockid(),
             add_response8.block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report8);
  block_manager_->IncrementalBlockReport("datanode2", report8);
  ns_->GetBlockReportManager()->IncrementalBlockReport(2, "datanode2", report8);
  CompleteRequestProto complete_request8;
  complete_request8.set_src(path6);
  complete_request8.set_clientname(create_request6.clientname());
  complete_request8.mutable_last()->set_poolid(ns_->blockpool_id());
  complete_request8.mutable_last()->set_blockid(
      add_response8.block().b().blockid());
  complete_request8.mutable_last()->set_generationstamp(
      add_response8.block().b().generationstamp());
  complete_request8.mutable_last()->set_numbytes(len);
  std::thread another2([&]() {
    LOG(INFO) << "Another2 thread started.";
    auto s = ns_->Delete(path6, true);
    ASSERT_TRUE(!s.HasException());
    ASSERT_TRUE(s.IsOK());
    auto create_request7 = MakeCreateRequest();
    CreateResponseProto create_response7;
    ASSERT_TRUE(!ns_->CreateFile(path6, p, create_request7, &create_response7)
                     .HasException());
    CompleteRequestProto complete_request7;
    complete_request7.set_src(path6);
    complete_request7.set_clientname(create_request7.clientname());
    ASSERT_TRUE(!ns_->CompleteFile(path6, complete_request7).HasException());
  });
  another2.join();
  s = ns_->CompleteFile(path6, complete_request8);
  LOG(INFO) << s.ToString();
  ASSERT_TRUE(s.HasException());

  s = ns_->Delete(test_dir, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTest, IsFileClosed) {
  std::string test_dir("/test_dir");
  ASSERT_TRUE(!ns_->MkDirs(test_dir, MakePermission(), true).HasException());

  std::string test_path(test_dir + "/test_file");
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(test_path, 100, 1, false, &add_response, &create_response);

  IsFileClosedResponseProto is_close_reponse;
  auto status = ns_->IsFileClosed(test_dir, &is_close_reponse);
  ASSERT_EQ(status.exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(status.message(), "Path is not a file: " + test_dir);

  is_close_reponse.Clear();
  status = ns_->IsFileClosed(test_path + "foo", &is_close_reponse);
  ASSERT_EQ(status.exception(),
            JavaExceptions::Exception::kFileNotFoundException);
  ASSERT_EQ(status.message(), "File does not exist: " + test_path + "foo");

  is_close_reponse.Clear();
  status = ns_->IsFileClosed(test_path, &is_close_reponse);
  ASSERT_FALSE(is_close_reponse.result());

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 0);

  CompleteRequestProto complete_request;
  complete_request.set_src(test_path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  ASSERT_TRUE(!ns_->CompleteFile(test_path, complete_request).HasException());
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.num_bytes(), 0);

  is_close_reponse.Clear();
  status = ns_->IsFileClosed(test_path, &is_close_reponse);
  ASSERT_TRUE(is_close_reponse.result());
}

TEST_F(NameSpaceTest, AbandonBlock) {
  std::string path = "/test_file";
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  auto create_request = MakeCreateRequest();
  create_request.set_replication(1);
  PermissionStatus p = MakePermission();
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());

  // abandon a non-exist block
  AbandonBlockRequestProto abandon_block_request;
  abandon_block_request.mutable_b()->set_blockid(8888);
  abandon_block_request.set_src(path);
  abandon_block_request.set_holder("client");
  abandon_block_request.set_fileid(create_response.fs().fileid());
  auto status = ns_->AbandonBlock(path, abandon_block_request);
  block_manager_->HandleDeprecatingBlocks();
  ASSERT_FALSE(status.HasException());
  block_manager_->UpdateState();
  auto stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 0);
  ASSERT_EQ(stat.num_block, 0);

  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  ASSERT_FALSE(
      ns_->AddBlock(
             path, client_ip, default_rpc_info, add_request, &add_response)
          .HasException());

  // abandon block with wrong block id

  block_manager_->UpdateState();
  stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 1);
  ASSERT_EQ(stat.num_block, 1);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 0);

  status = ns_->AbandonBlock(path, abandon_block_request);
  auto c = std::dynamic_pointer_cast<MockEditLogContext>(edit_log_ctx_);
  c->open_for_read_ = false;
  c->open_for_write_ = true;
  block_manager_->HandleDeprecatingBlocks();
  ASSERT_FALSE(status.HasException());
  block_manager_->UpdateState();
  stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 1);
  ASSERT_EQ(stat.num_block, 1);

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));

  report.Clear();
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  ns_->GetBlockReportManager()->IncrementalBlockReport(1, "datanode1", report);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));

  add_request.mutable_previous()->CopyFrom(add_response.block().b());
  add_request.mutable_previous()->set_numbytes(100);
  AddBlockResponseProto add_response2;
  ASSERT_FALSE(
      ns_->AddBlock(
             path, client_ip, default_rpc_info, add_request, &add_response2)
          .HasException());
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  // abandon a complete block
  abandon_block_request.mutable_b()->set_blockid(
      add_response.block().b().blockid());
  status = ns_->AbandonBlock(path, abandon_block_request);
  block_manager_->HandleDeprecatingBlocks();
  // NameSpace::AsyncAbandonBlock returns true but does nothing.
  ASSERT_FALSE(status.HasException());
  block_manager_->UpdateState();
  stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 1);
  ASSERT_EQ(stat.num_block, 2);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);

  // abandon succuss
  abandon_block_request.mutable_b()->set_blockid(
      add_response2.block().b().blockid());
  status = ns_->AbandonBlock(path, abandon_block_request);
  block_manager_->HandleDeprecatingBlocks();
  ns_->WaitNoPending();
  ASSERT_FALSE(status.HasException());
  block_manager_->UpdateState();
  stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 0);
  ASSERT_EQ(stat.num_block, 1);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response2.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kDeprecated);

  CompleteRequestProto complete_request;
  complete_request.set_src(path);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(100);
  ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());

  GetListingResponseProto get_response;
  GetListingRequestProto get_request;
  get_request.set_src(path);
  get_request.set_startafter("");
  get_request.set_needlocation(true);
  ns_->GetListing(
      path, NetworkLocationInfo(), get_request, &get_response, ugi_);
  LOG(INFO) << get_response.ShortDebugString();
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 1);
  ASSERT_EQ(get_response.dirlist().partiallisting(0).locations().blocks_size(),
            1);
  ASSERT_EQ(get_response.dirlist()
                .partiallisting(0)
                .locations()
                .blocks(0)
                .b()
                .blockid(),
            add_response.block().b().blockid());
  block_manager_->UpdateState();
  stat = block_manager_->stat();
  ASSERT_EQ(stat.num_uc, 0);
  ASSERT_EQ(stat.num_block, 1);
}

TEST_F(NameSpaceTest, Lease) {
  ns_->StopMetaScanner();
  ns_->StartLeaseMonitor();
  std::string test_dir = "/test_dir";
  PermissionStatus p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  DEFER([&]() { ns_->Delete(test_dir, true); });
  std::string test_file = test_dir + "/file1";
  auto to_sleep = FLAGS_lease_expired_hard_limit_ms * 3;
  LOG(INFO) << "to_sleep=" << to_sleep;

  LOG(INFO) << "Test CreateFile";
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(test_file, p, create_request, &create_response)
                   .HasException());
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  ns_->StopLeaseMonitor();
  LOG(INFO) << "- Test GetBlockLocation";
  HdfsFileStatusProto file_status;
  GetBlockLocationsRequestProto get_request;
  get_request.set_src(test_file);
  get_request.set_offset(0);
  get_request.set_length(INT64_MAX);
  GetBlockLocationsResponseProto get_response;
  cnetpp::base::IPAddress client_ip("***********");
  auto ret = ns_->GetBlockLocation(test_file,
                                   NetworkLocationInfo(client_ip),
                                   get_request,
                                   &get_response, ugi_);
  ASSERT_TRUE(!ret.HasException());
  ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
  auto s = ns_->Delete(test_file, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ns_->StartLeaseMonitor();

  LOG(INFO) << "Test AddBlock";
  AddBlockResponseProto add_response;
  create_response.Clear();
  AddFile(test_file, 10, 1, false, &add_response, &create_response);
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  ns_->StopLeaseMonitor();
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  cloudfs::datanode::BlockRecoveryCommandProto* recover_cmd = nullptr;
  for (int32_t i = 0; i < heartbeat_response.cmds_size(); ++i) {
    auto cmd = heartbeat_response.mutable_cmds(i);
    if (cmd->cmdtype() ==
        cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand) {
      recover_cmd = cmd->mutable_recoverycmd();
    }
  }
  ASSERT_TRUE(recover_cmd != nullptr);
  ASSERT_EQ(recover_cmd->blocks_size(), 1);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 0);
  s = ns_->Delete(test_file, true);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ns_->StartLeaseMonitor();

  // The subsequent portion is replaced by
  // NameSpaceTestV2.ReleaseLeaseStartBlockRecovery and
  // BlockManagerTestV2.InitRecoverXXX.
  //
  // LOG(INFO) << "Test re-Register";
  // auto reg2 =
  //     cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  // reg2.mutable_datanodeid()->set_datanodeuuid("datanode2");
  // cnetpp::base::IPAddress ip2("***********0");
  // datanode_manager_->Register(reg2.datanodeid(), &reg2, ip2);
  // datanode_manager_->RefreshConfig();
  // DatanodeManager::RepeatedCmds cmds;
  // cloudfs::datanode::HeartbeatRequestProto request;
  // GetDiskRequest(&request, "datanode2", reg2, 102400);
  // datanode_manager_->Heartbeat(request, &cmds);
  // AddFile(test_file, 100, 2, false, &add_response, &create_response);
  // std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  // ns_->StopLeaseMonitor();
  // // command for datanode1
  // LOG(INFO) << "- Test BlockCommand 1";
  // heartbeat_response.Clear();
  // recover_cmd = nullptr;
  // block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  // for (int32_t i = 0; i < heartbeat_response.cmds_size(); ++i) {
  //   auto cmd = heartbeat_response.mutable_cmds(i);
  //   if (cmd->cmdtype() ==
  //       cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand) {
  //     recover_cmd = cmd->mutable_recoverycmd();
  //   }
  // }
  // ASSERT_TRUE(recover_cmd != nullptr);
  // ASSERT_EQ(recover_cmd->blocks_size(), 1);
  // ASSERT_EQ(recover_cmd->blocks(0).block().locs_size(), 2);
  // bip.Clear();
  // ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
  //     add_response.block().b().blockid(), &bip));
  // EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  // EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  // EXPECT_EQ(bip.num_bytes(), 0);
  // // command for datanode2
  // LOG(INFO) << "- Test BlockCommand 2";
  // heartbeat_response.Clear();
  // recover_cmd = nullptr;
  // block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  // for (int32_t i = 0; i < heartbeat_response.cmds_size(); ++i) {
  //   auto cmd = heartbeat_response.mutable_cmds(i);
  //   if (cmd->cmdtype() ==
  //       cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand) {
  //     recover_cmd = cmd->mutable_recoverycmd();
  //   }
  // }
  // ASSERT_TRUE(recover_cmd == nullptr);
  // LOG(INFO) << "Test Delete";
  // s = ns_->Delete(test_file, true);
  // ASSERT_TRUE(!s.HasException());
  // ASSERT_TRUE(s.IsOK());

  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));

  s = ns_->Delete(test_dir, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  ns_->StartLeaseMonitor();
}

TEST_F(NameSpaceTest, LeaseWithStaleDN) {
  ns_->StartLeaseMonitor();
  std::string test_dir = "/test_dir";
  PermissionStatus p = MakePermission();

  // datanode 2
  auto reg2 =
      cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg2.mutable_datanodeid()->set_datanodeuuid("datanode2");
  cnetpp::base::IPAddress ip2("***********0");
  datanode_manager_->Register(reg2.datanodeid(), &reg2, ip2);
  datanode_manager_->RefreshConfig();
  DatanodeManager::RepeatedCmds cmds;
  HeartbeatRequestProto request;
  GetDiskRequest(&request, "datanode2", reg2, 102400);
  datanode_manager_->Heartbeat(request, &cmds);

  // datanode 3
  auto reg3 =
      cloudfs::datanode::DatanodeRegistrationProto::default_instance();
  reg3.mutable_datanodeid()->set_datanodeuuid("datanode3");
  cnetpp::base::IPAddress ip3("************");
  datanode_manager_->Register(reg3.datanodeid(), &reg3, ip3);
  datanode_manager_->RefreshConfig();
  cmds.Clear();
  request.Clear();
  GetDiskRequest(&request, "storage3", reg3, 102400);
  datanode_manager_->Heartbeat(request, &cmds);

  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  DEFER([&]() { ns_->Delete(test_dir, true); });
  std::string test_file = test_dir + "/file";
  auto to_sleep = FLAGS_lease_expired_hard_limit_ms * 2;
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  AddBlockResponseProto add_response;
  AddFile(test_file, 10, 3, false, &add_response, &create_response);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());

  // two active datanode2 & one stale datanode
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  datanode_manager_->Heartbeat(request, &cmds);
  LOG(INFO) << "cmd size: " << cmds.size();
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  cloudfs::datanode::BlockRecoveryCommandProto* recover_cmd = nullptr;
  block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  for (int32_t i = 0; i < heartbeat_response.cmds_size(); ++i) {
    auto cmd = heartbeat_response.mutable_cmds(i);
    if (cmd->cmdtype() ==
        cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand) {
      recover_cmd = cmd->mutable_recoverycmd();
    }
  }
  ASSERT_TRUE(recover_cmd != nullptr);
  ASSERT_EQ(recover_cmd->blocks_size(), 1);
  ASSERT_EQ(recover_cmd->blocks(0).block().locs_size(), 2);
  std::string ip(recover_cmd->blocks(0).block().locs(0).id().ipaddr());
  ASSERT_TRUE(ip == "***********" || ip == "************");
  ip = recover_cmd->blocks(0).block().locs(1).id().ipaddr();
  ASSERT_TRUE(ip == "***********" || ip == "************");
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  ASSERT_TRUE(ns_->Delete(test_file, false).IsOK());
  add_response.Clear();
  create_response.Clear();

  // one active datanode & two stale datanodes
  datanode_manager_->Heartbeat(request, &cmds);
  request.Clear();
  GetDiskRequest(&request, "datanode2", reg2, 102400);
  datanode_manager_->Heartbeat(request, &cmds);
  AddFile(test_file, 10, 3, false, &add_response, &create_response);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep * 2));
  heartbeat_response.Clear();
  recover_cmd = nullptr;
  block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
  for (int32_t i = 0; i < heartbeat_response.cmds_size(); ++i) {
    auto cmd = heartbeat_response.mutable_cmds(i);
    if (cmd->cmdtype() ==
        cloudfs::datanode::DatanodeCommandProto::BlockRecoveryCommand) {
      recover_cmd = cmd->mutable_recoverycmd();
    }
  }
  ASSERT_TRUE(recover_cmd != nullptr);
  ASSERT_EQ(recover_cmd->blocks_size(), 1);
  ASSERT_EQ(recover_cmd->blocks(0).block().locs_size(), 3);
  ip = recover_cmd->blocks(0).block().locs(0).id().ipaddr();
  ASSERT_TRUE(ip == "***********" || ip == "***********0" ||
              ip == "************");
  ip = recover_cmd->blocks(0).block().locs(1).id().ipaddr();
  ASSERT_TRUE(ip == "***********" || ip == "***********0" ||
              ip == "************");
  ip = recover_cmd->blocks(0).block().locs(2).id().ipaddr();
  ASSERT_TRUE(ip == "***********" || ip == "***********0" ||
              ip == "************");
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
}

// TEST_F(NameSpaceTest, DNReportLeaseRecover) {
//   ns_->StartLeaseMonitor();
//   std::string test_dir = "/test_dir";
//   PermissionStatus p = MakePermission();
//   ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
//   std::string test_file = test_dir + "/file1";
//   auto to_sleep = FLAGS_lease_expired_hard_limit_ms * 3;

//   AddBlockResponseProto add_response;
//   CreateResponseProto create_response;
//   AddFile(test_file, 10, 1, false, &add_response, &create_response);
//   auto fid = create_response.fs().fileid();
//   std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
//   ns_->StopLeaseMonitor();
//   cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
//   block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
//   auto recover_cmd = heartbeat_response.cmds(0).recoverycmd();
//   BlockInfoProto bip;
//   ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
//       add_response.block().b().blockid(), &bip));
//   EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
//   EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
//   EXPECT_EQ(bip.num_bytes(), 0);
//   ns_->StartLeaseMonitor();

//   // report with wrong gs
//   LOG(INFO) << "---- report with wrong gs ----";
//   CommitBlockSynchronizationRequestProto commit_request;
//   commit_request.mutable_block()->CopyFrom(add_response.block().b());
//   commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp() - 1);
//   commit_request.set_newlength(8);
//   commit_request.set_closefile(false);
//   commit_request.set_deleteblock(false);
//   ASSERT_EQ(ns_->CommitBlockSynchronization(commit_request).exception(),
//             JavaExceptions::Exception::kIOException);

//   // report with no instruction
//   LOG(INFO) << "---- report with no instruction ----";
//   std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
//   ns_->StopLeaseMonitor();
//   heartbeat_response.Clear();
//   block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
//   recover_cmd = heartbeat_response.cmds(0).recoverycmd();
//   commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp());
//   // CommitBlockSyn has bug when deleteblock and closefile noth are false.
//   ASSERT_TRUE(ns_->CommitBlockSynchronization(commit_request).HasException());
//   GetBlockLocationsRequestProto get_request;
//   get_request.set_src(test_file);
//   get_request.set_offset(0);
//   get_request.set_length(INT32_MAX);
//   GetBlockLocationsResponseProto get_response;
//   cnetpp::base::IPAddress client_ip("***********");
//   auto ret =
//       ns_->GetBlockLocation(test_file, client_ip, get_request, &get_response,
//       ugi_);
//   ASSERT_TRUE(!ret.HasException());
//   ASSERT_EQ(get_response.locations().filelength(), 0);
//   ASSERT_EQ(ns_->generation_stamp_v2(), recover_cmd.blocks(0).newgenstamp());
//   ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
//   bip.Clear();
//   ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
//       add_response.block().b().blockid(), &bip));
//   EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
//   EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
//   EXPECT_EQ(bip.num_bytes(), add_response.block().b().numbytes());
//   ns_->StartLeaseMonitor();

//   // report with delete
//   LOG(INFO) << "---- report with delete ----";
//   std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
//   ns_->StopLeaseMonitor();
//   heartbeat_response.Clear();
//   block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
//   recover_cmd = heartbeat_response.cmds(0).recoverycmd();
//   commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp());
//   commit_request.set_deleteblock(true);
//   ASSERT_TRUE(!ns_->CommitBlockSynchronization(commit_request).HasException());
//   get_response.Clear();
//   ret = ns_->GetBlockLocation(test_file, client_ip, get_request,
//   &get_response, ugi_); ASSERT_TRUE(!ret.HasException());
//   ASSERT_EQ(get_response.locations().blocks_size(), 0);
//   ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
//   bip.Clear();
//   ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
//       add_response.block().b().blockid(), &bip));
//   EXPECT_EQ(bip.state(), BlockInfoProto::kDeprecated);
//   EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
//   EXPECT_EQ(bip.gen_stamp(), commit_request.newgenstamp());
//   EXPECT_EQ(bip.num_bytes(), commit_request.newlength());
//   ns_->StartLeaseMonitor();

//   // block in wrong state
//   LOG(INFO) << "---- block in wrong state ----";
//   std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
//   ns_->StopLeaseMonitor();
//   ASSERT_TRUE(!ns_->CommitBlockSynchronization(commit_request).HasException());
//   get_response.Clear();
//   ret = ns_->GetBlockLocation(test_file, client_ip, get_request,
//   &get_response, ugi_); ASSERT_TRUE(!ret.HasException());
//   ASSERT_EQ(get_response.locations().blocks_size(), 0);
//   ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
//   ns_->StartLeaseMonitor();

//   // report with close
//   LOG(INFO) << "---- report with close ----";
//   auto s = ns_->Delete(test_file, true);
//   ASSERT_TRUE(!s.HasException());
//   ASSERT_TRUE(s.IsOK());
//   add_response.Clear();
//   create_response.Clear();
//   AddFile(test_file, 10, 1, false, &add_response, &create_response);
//   fid = create_response.fs().fileid();
//   std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
//   ns_->StopLeaseMonitor();
//   heartbeat_response.Clear();
//   block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
//   recover_cmd = heartbeat_response.cmds(0).recoverycmd();
//   commit_request.mutable_block()->CopyFrom(add_response.block().b());
//   commit_request.set_newgenstamp(recover_cmd.blocks(0).newgenstamp());
//   commit_request.set_newlength(8);
//   commit_request.set_closefile(true);
//   commit_request.set_deleteblock(false);
//   auto target = commit_request.add_newtaragets();
//   target->set_ipaddr("***********");
//   target->set_datanodeuuid("datanode1");
//   commit_request.add_newtargetstorages("storage1");
//   ns_->StopLeaseMonitor();
//   ASSERT_TRUE(!ns_->CommitBlockSynchronization(commit_request).HasException());
//   get_response.Clear();
//   ret = ns_->GetBlockLocation(test_file, client_ip, get_request,
//   &get_response, ugi_); ASSERT_TRUE(!ret.HasException());
//   ASSERT_EQ(get_response.locations().filelength(), 8);
//   ASSERT_EQ(get_response.locations().blocks(0).b().generationstamp(),
//             recover_cmd.blocks(0).newgenstamp());
//   ASSERT_EQ(get_response.locations().islastblockcomplete(), true);
//   bip.Clear();
//   ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
//       add_response.block().b().blockid(), &bip));
//   EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
//   EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
//   EXPECT_EQ(bip.gen_stamp(), commit_request.newgenstamp());
//   EXPECT_EQ(bip.num_bytes(), commit_request.newlength());
//   ns_->StartLeaseMonitor();
// }

// TEST_F(NameSpaceTest, RemoveLease) {
//   FLAGS_bg_deletion_interval_in_sec = 1;
//   ns_->StartLeaseMonitor();
//   ns_->StartBGDeletionWorker();
//   std::this_thread::sleep_for(
//       std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms));
//   std::string test_dir = "/test_dir";
//   PermissionStatus p = MakePermission();
//   ASSERT_FALSE(ns_->MkDirs(test_dir, p, true).HasException());
//   std::string test_file = test_dir + "/file1";
//   cnetpp::base::IPAddress client_ip("***********");
//   auto create_request = MakeCreateRequest();
//   create_request.set_replication(1);
//   CreateResponseProto create_response;
//   ASSERT_FALSE(ns_->CreateFile(test_file, p, create_request,
//   &create_response)
//                    .HasException());
//   // Prevent LeaseMonitorV2::DoTask overwrites the result of
//   // LeaseManagerV2::UpdateStats.
//   std::this_thread::sleep_for(std::chrono::milliseconds(50));
//   ns_->lease_manager()->UpdateStats();
//   ASSERT_EQ(ns_->lease_manager()->Stats().path_num, 1);
//   ASSERT_FALSE(ns_->Delete(test_dir, true).HasException());
//   std::this_thread::sleep_for(
//       std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms * 3));
//   ns_->lease_manager()->UpdateStats();
//   ASSERT_EQ(ns_->lease_manager()->Stats().path_num, 0);
//   ns_->StopLeaseMonitor();
// }

TEST_F(NameSpaceTest, RemoveLease) {
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  ns_->StartLeaseMonitor();
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms));
  std::string test_dir = "/test_dir";
  PermissionStatus p = MakePermission();
  ASSERT_FALSE(ns_->MkDirs(test_dir, p, true).HasException());
  std::string test_file = test_dir + "/file1";
  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  create_request.set_replication(1);
  CreateResponseProto create_response;
  ASSERT_FALSE(ns_->CreateFile(test_file, p, create_request, &create_response)
                   .HasException());
  // Prevent LeaseMonitorV2::DoTask overwrites the result of
  // LeaseManagerV2::TestOnlyUpdateStats.
  ns_->WaitNoPending();
  ns_->lease_manager()->UpdateStats();
  ASSERT_EQ(ns_->lease_manager()->Stats().path_num, 1);
  ASSERT_FALSE(ns_->Delete(test_dir, true).HasException());
  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms * 3));
  ns_->lease_manager()->UpdateStats();
  ASSERT_EQ(ns_->lease_manager()->Stats().path_num, 0);
  ns_->StopLeaseMonitor();
}

TEST_F(NameSpaceTest, BGRepeatRecoverLease) {
  ns_->StartLeaseMonitor();
  std::string test_dir = "/test_dir";
  PermissionStatus p;
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  std::string test_file = test_dir + "/file1";
  cnetpp::base::IPAddress client_ip("***********");

  // recover on a zero-sized file
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(test_file, 0, 1, false, &add_response, &create_response);
  uint64_t inode_id = create_response.fs().fileid();
  RecoverLeaseRequestProto recover_lease_request;
  auto ret = ns_->RecoverLease("client", test_file, &recover_lease_request);
  ASSERT_TRUE(!ret.HasException()) << ret.ToString();
  ASSERT_TRUE(ret.IsFalse());

  auto to_sleep = FLAGS_lease_expired_hard_limit_ms * 3;
  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  auto lease_holder_earlier =
      ns_->lease_manager()->GetLeaseHolder(inode_id, false);
  LOG(INFO) << "Current lease holder is: " << lease_holder_earlier;

  std::this_thread::sleep_for(std::chrono::milliseconds(to_sleep));
  auto lease_holder = ns_->lease_manager()->GetLeaseHolder(inode_id, false);
  LOG(INFO) << "Current lease holder is: " << lease_holder;
  ASSERT_NE(lease_holder_earlier, lease_holder);
  ns_->StopLeaseMonitor();
}

TEST_F(NameSpaceTest, RecoverLease) {
  std::string test_dir = "/test_dir";
  PermissionStatus p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  std::string test_file = test_dir + "/file1";
  cnetpp::base::IPAddress client_ip("***********");

  // on a empty file
  auto create_request = MakeCreateRequest();
  create_request.set_replication(1);
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(test_file, p, create_request, &create_response)
                   .HasException());
  RecoverLeaseRequestProto recover_request;
  auto ret = ns_->RecoverLease("client", test_file, &recover_request);
  LOG(INFO) << ret.ToString();
  ASSERT_TRUE(!ret.HasException());
  ASSERT_TRUE(ret.IsFalse());
  auto s = ns_->Delete(test_file, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());

  // recover on a zero-sized file
  AddBlockResponseProto add_response;
  create_response.Clear();
  AddFile(test_file, 0, 1, false, &add_response, &create_response);
  ret = ns_->RecoverLease("client", test_file, &recover_request);
  ASSERT_TRUE(!ret.HasException()) << ret.ToString();
  ASSERT_TRUE(ret.IsFalse());

  GetBlockLocationsResponseProto get_response;
  GetBlockLocationsRequestProto get_request;
  get_request.set_src(test_file);
  get_request.set_offset(0);
  get_request.set_length(INT32_MAX);
  auto r = ns_->GetBlockLocation(test_file,
                                 NetworkLocationInfo(client_ip),
                                 get_request,
                                 &get_response,
                                 ugi_);
  ASSERT_EQ(r.HasException(), false);
  ASSERT_EQ(r.code(), Code::kOK);
  ASSERT_EQ(get_response.locations().blocks_size(), 0);
  ASSERT_EQ(get_response.locations().islastblockcomplete(), false);
  ASSERT_EQ(block_manager_->GetBlockUCState(add_response.block().b().blockid()),
            BlockUCState::kUnderRecovery);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 0);

  auto commit_request = GetCommitSyncRequest(0, add_response, true, false);
  ASSERT_TRUE(!ns_->CommitBlockSynchronization(commit_request).HasException());
  ASSERT_EQ(block_manager_->GetBlockUCState(add_response.block().b().blockid()),
            BlockUCState::kCommitted);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), commit_request.newgenstamp());
  EXPECT_EQ(bip.num_bytes(), commit_request.newlength());

  // recover on a closed file
  ret = ns_->RecoverLease("client", test_file, &recover_request);
  ASSERT_TRUE(!ret.HasException());
  ASSERT_TRUE(ret.IsOK());
  s = ns_->Delete(test_file, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());

  s = ns_->Delete(test_dir, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTest, BuildFullPath) {
  bool enable_snapshot_feature = FLAGS_enable_snapshot_feature;
  DEFER([&]() { FLAGS_enable_snapshot_feature = enable_snapshot_feature; });
  FLAGS_enable_snapshot_feature = true;

  auto p = MakePermission();
  auto path = ns_->BuildFullPath(kRootINodeId);
  ASSERT_EQ(path, "/");
  auto s = ns_->Delete(path, true);
  ASSERT_FALSE(!s.HasException());

  path = "/create";
  s = ns_->Delete(path, true);
  ASSERT_FALSE(s.HasException());
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  std::string file = path + "/123";
  ASSERT_TRUE(
      !ns_->CreateFile(file, p, create_request, &response).HasException());
  CompleteRequestProto complete_request;
  complete_request.set_src(file);
  complete_request.set_clientname("client");
  complete_request.set_fileid(response.fs().fileid());
  ASSERT_TRUE(!ns_->CompleteFile(file, complete_request).HasException());
  INodeInPath iip;
  std::string path2 = ns_->BuildFullPath2(response.fs().fileid(), &iip);
  ASSERT_EQ(path2, "/create/123");
  ASSERT_EQ(iip.Inode().id(), response.fs().fileid());

  auto get_last_inode = [&](const std::string &path) {
    INodeInPath iip2;
    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(path, &path_components);
    EXPECT_EQ(StatusCode::kOK, ns_->GetLastINodeInPath(path_components, &iip2));
    return iip2;
  };
  INodeInPath last_iip = get_last_inode("/create/123");
  ASSERT_EQ(last_iip.Inode().id(), iip.Inode().id());
  ASSERT_EQ(last_iip.Inode().name(), iip.Inode().name());
  ASSERT_EQ(last_iip.SnapshotRootId(), kInvalidINodeId);
  ASSERT_EQ(last_iip.SnapshotRootId(), iip.SnapshotRootId());

  DANCENN_ASSERT_OK(ns_->AllowSnapshot("/create"));
  ns_->BuildFullPath2(response.fs().fileid(), &iip);
  ASSERT_EQ(iip.Inode().id(), response.fs().fileid());
  last_iip = get_last_inode("/create/123");
  ASSERT_EQ(last_iip.Inode().id(), iip.Inode().id());
  ASSERT_EQ(last_iip.Inode().name(), iip.Inode().name());
  ASSERT_NE(last_iip.SnapshotRootId(), kInvalidINodeId);
  ASSERT_EQ(last_iip.SnapshotRootId(), iip.SnapshotRootId());
  DANCENN_ASSERT_OK(ns_->DisAllowSnapshot("/create"));

  std::string path3 = ns_->BuildFullPath2(8888, &iip);
  ASSERT_EQ(path3, "");
  ASSERT_FALSE(iip.Inode().id() == 8888);

  s = ns_->Delete(path, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTest, LoadBlockMap) {
  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /.RECYCLE.BIN           [id: kRecycleBinINodeId (kLastReservedINodeId)]
  //  /a                      [id: kLastReservedINodeId + 1]
  //    /b                    [id: kLastReservedINodeId + 2]
  //      /c1                 [id: kLastReservedINodeId + 3]
  //         /d1              [id: kLastReservedINodeId + 4]
  //            /e1.txt       [id: kLastReservedINodeId + 6]
  //            /e2.txt       [id: kLastReservedINodeId + 7]
  //         /d2              [id: kLastReservedINodeId + 5]
  //            /e1.txt       [id: kLastReservedINodeId + 8]
  //            /e2.txt       [id: kLastReservedINodeId + 9]
  //         /d3.txt          [id: kLastReservedINodeId + 10]
  //      /c2                 [id: kLastReservedINodeId + 11]
  //
  ASSERT_TRUE(
      !ns_->MkDirs("/a/b/c1/d1", MakePermission(), true).HasException());
  ASSERT_TRUE(
      !ns_->MkDirs("/a/b/c1/d2", MakePermission(), true).HasException());
  AddBlockResponseProto arsp;
  CreateResponseProto crsp;
  crsp.Clear();
  AddFile("/a/b/c1/d1/e1.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d1/e2.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d2/e1.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d2/e2.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d3.txt", 0, 1, true, &arsp, &crsp);
  ASSERT_TRUE(!ns_->MkDirs("/a/b/c2", MakePermission(), true).HasException());
  auto s = ns_->Delete("/a/b/c1", true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  int pd_count = 0;
  ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
    auto func = [&]() {
      ASSERT_EQ(inode.id(), kLastReservedINodeId + 3);
      ASSERT_EQ(inode.parent_id(), kLastReservedINodeId + 2);
    };
    func();
    pd_count++;
    return true;
  });
  ASSERT_EQ(pd_count, 1);
  INode inode;
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "d1", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "d2", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 4, "e1.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 4, "e2.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 5, "e1.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 5, "e2.txt", &inode),
            StatusCode::kOK);
  // Notice: NameSpaceTest::block_manager_ holds meta_storage_.
  // So old meta storag is alive when MockNameSpace creates a new one.
  ns_->StopActive();
  ns_->Stop();
  ns_->meta_storage()->Shutdown();
  ns_.reset();
  block_manager_.reset();
  edit_log_ctx_ = CreateContext();
  block_manager_.reset(new BlockManager(edit_log_ctx_));
  block_manager_->TestOnlySetEditLogCtx(edit_log_ctx_);
  ns_.reset(new MockNameSpace(db_path_,
                              edit_log_ctx_,
                              block_manager_,
                              datanode_manager_,
                              std::make_shared<DataCenters>(),
                              UfsEnv::Create()));
  ns_->set_safemode(safemode_.get());
  ns_->set_ha_state(ha_state_.get());
  block_manager_->set_ha_state(ha_state_.get());
  block_manager_->set_safemode(safemode_.get());
  block_manager_->set_ns(ns_.get());
  datanode_manager_->set_block_manager(block_manager_.get());
  // mock edit log sender
  auto last_tx_id = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx_, last_tx_id));
  ns_->TestOnlySetEditLogSender(std::move(sender));

  ns_->Start();
  ns_->StartActive();

  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();
  pd_count = 0;
  ns_->meta_storage()->ScanPendingDeleteCF([&](const INode& inode) -> bool {
    pd_count++;
    return true;
  });
  ASSERT_EQ(pd_count, 1);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "d1", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "d2", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 4, "e1.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 4, "e2.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 5, "e1.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->meta_storage()->GetINode(kLastReservedINodeId + 5, "e2.txt", &inode),
            StatusCode::kOK);
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 1), "/a");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 2), "/a/b");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 3), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 4), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 5), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 6), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 7), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 8), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 9), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 10), "");
  ASSERT_EQ(ns_->BuildFullPath(kLastReservedINodeId + 11), "/a/b/c2");
}

TEST_F(NameSpaceTest, CreateSymlink) {
  FLAGS_dfs_symlinks_enabled = true;
  std::string link1 = "/link1";
  std::string link2 = "/symlink/link2";
  std::string target = "/symlink/target";

  auto ps = MakePermission();

  ASSERT_TRUE(ns_->CreateSymlink(target, link1, ps, false).IsOK());
  std::vector<cnetpp::base::StringPiece> link1_components;
  ASSERT_TRUE(SplitPath(link1, &link1_components));
  INode link1_node;
  ASSERT_EQ(ns_->GetLastINodeInPath(link1_components, &link1_node),
            StatusCode::kOK);
  ASSERT_EQ(link1_node.type(), INode_Type_kSymLink);
  ASSERT_EQ(link1_node.symlink(), target);

  auto s = ns_->CreateSymlink(target, link2, ps, false);
  ASSERT_EQ(s.exception(), JavaExceptions::kFileNotFoundException);

  s = ns_->CreateSymlink(target, link2, ps, true);
  ASSERT_FALSE(s.HasException());
  ASSERT_TRUE(s.IsOK());

  std::vector<cnetpp::base::StringPiece> link2_components;
  ASSERT_TRUE(SplitPath(link2, &link2_components));
  INode link2_node;
  ASSERT_EQ(ns_->GetLastINodeInPath(link2_components, &link2_node),
            StatusCode::kOK);
  ASSERT_EQ(link2_node.type(), INode_Type_kSymLink);
  ASSERT_EQ(link2_node.symlink(), target);
}

TEST_F(NameSpaceTest, SetReplication) {
  std::string path = "/test_file";
  auto p = MakePermission();

  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile(path, 100, 1, true, &add_response, &create_response);

    GetFileInfoResponseProto get_response;
    ns_->GetFileInfo(
        path, NetworkLocationInfo(), false, false, &get_response, ugi_);
    ASSERT_EQ(get_response.fs().block_replication(), 1);
    block_manager_->UpdateState();
    auto stat = block_manager_->stat();
    ASSERT_EQ(stat.num_block, 1);
    ASSERT_EQ(stat.num_under_replicated, 0);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
    EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
    EXPECT_EQ(bip.num_bytes(), 100);
    EXPECT_EQ(bip.expected_rep(), 1);

    ns_->SetReplication(path, 2);
    block_manager_->UpdateState();
    stat = block_manager_->stat();
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.expected_rep(), 1);
    // ASSERT_EQ(stat.num_under_replicated, 1);  // not in active state
    ns_->Delete(path, false);
  }

  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile(path, 100, 1, true, &add_response, &create_response);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
    EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
    EXPECT_EQ(bip.num_bytes(), 100);
    EXPECT_EQ(bip.expected_rep(), 1);

    GetFileInfoResponseProto get_response;
    ns_->GetFileInfo(
        path, NetworkLocationInfo(), false, false, &get_response, ugi_);
    ASSERT_EQ(get_response.fs().block_replication(), 1);
    block_manager_->HandleDeprecatingBlocks();
    ns_->WaitNoPending();
    block_manager_->UpdateState();
    auto stat = block_manager_->stat();
    ASSERT_EQ(stat.num_block, 1);
    ASSERT_EQ(stat.num_under_replicated, 0);

    ns_->TestSetReplicationAttrOnly(path, 2);
    stat = block_manager_->stat();
    ASSERT_EQ(stat.num_under_replicated, 0);
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.expected_rep(), 1);
    ns_->Delete(path, false);
  }

  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile(path, 100, 1, true, &add_response, &create_response);
    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
    EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
    EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
    EXPECT_EQ(bip.num_bytes(), 100);
    EXPECT_EQ(bip.expected_rep(), 1);

    GetFileInfoResponseProto get_response;
    ns_->GetFileInfo(
        path, NetworkLocationInfo(), false, false, &get_response, ugi_);
    ASSERT_EQ(get_response.fs().block_replication(), 1);
    block_manager_->HandleDeprecatingBlocks();
    ns_->WaitNoPending();
    block_manager_->UpdateState();
    auto stat = block_manager_->stat();
    ASSERT_EQ(stat.num_block, 1);
    ASSERT_EQ(stat.num_under_replicated, 0);

    bool allow_zero_replica = true;
    auto status = ns_->SetReplication(path, 0, allow_zero_replica);
    ASSERT_FALSE(status.HasException());

    status = ns_->GetFileInfo(
        path, NetworkLocationInfo(), true, false, &get_response, ugi_);
    ASSERT_FALSE(status.HasException());
    EXPECT_EQ(get_response.fs().block_replication(), 0);
    block_manager_->UpdateState();
    stat = block_manager_->stat();
    // Here we reserve the block because we want to remove all replicas while
    // keeping track of file's original size.
    ASSERT_EQ(stat.num_block, 0);
    ASSERT_EQ(stat.num_zero_replica_block, 1);
    ASSERT_EQ(stat.num_under_replicated, 0);
    ASSERT_EQ(stat.num_postponed_misreplicated, 1);
    bip.Clear();
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(
        add_response.block().b().blockid(), &bip));
    EXPECT_EQ(bip.expected_rep(), 1);
  }
}

TEST_F(NameSpaceTest, XAttr) {
  std::string path = "/xattr_file";
  ns_->Delete(path, true);

  auto p = MakePermission();
  auto file_status = ::cloudfs::HdfsFileStatusProto();
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  ASSERT_TRUE(
      !ns_->CreateFile(path, p, create_request, &response).HasException());

  std::string test_xattr_name = "hdfs.nstest.xattr.mockx";
  XAttrProto x;
  x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
  x.set_name(test_xattr_name);
  x.set_value("1");

  std::string test_xattr_namey = "hdfs.nstest.xattr.mocky";
  XAttrProto y;
  y.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  y.set_name(test_xattr_namey);
  y.set_value("y");
  ASSERT_EQ(ns_->SetXAttr(path, x, false, false).code(), Code::kError);
  ASSERT_EQ(ns_->SetXAttr(path, x, true, false).code(), Code::kOK);
  x.set_value("2");
  ASSERT_EQ(ns_->SetXAttr(path, x, false, false).code(), Code::kError);
  ASSERT_EQ(ns_->SetXAttr(path, x, false, true).code(), Code::kOK);
  ASSERT_EQ(ns_->SetXAttr(path, y, true, false).code(), Code::kOK);

  RepeatedPtrField<XAttrProto> xattrs;
  ASSERT_EQ(ns_->ListXAttrs(path, &xattrs, ugi_).code(), Code::kOK);
  ASSERT_EQ(xattrs.size(), 1);
  ASSERT_EQ(xattrs.Get(0).namespace_(),
            ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  ASSERT_EQ(xattrs.Get(0).name(), test_xattr_namey);
  ASSERT_EQ(xattrs.Get(0).value(), "y");

  y.set_value("0");
  xattrs.Clear();
  xattrs.Add()->CopyFrom(y);
  RepeatedPtrField<XAttrProto> list_resp;
  ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kOK);
  ASSERT_EQ(list_resp.size(), 1);
  ASSERT_EQ(list_resp.Get(0).namespace_(),
            ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  ASSERT_EQ(list_resp.Get(0).name(), test_xattr_namey);
  ASSERT_EQ(list_resp.Get(0).value(), "y");

  xattrs.Add()->CopyFrom(x);
  ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kError);
}

TEST_F(NameSpaceTest, SetOwner) {
  std::string base_path = "/create";
  std::string path1 = "/create/create_file1";
  ns_->Delete(base_path, true);

  auto p = MakePermission();
  auto file_status = ::cloudfs::HdfsFileStatusProto();

  auto create_request = MakeCreateRequest();
  CreateResponseProto response;

  // create file /create/create_file1
  create_request.set_createparent(true);
  ASSERT_TRUE(
      !ns_->CreateFile(path1, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);

  // set owner:group to be bob:group
  ASSERT_TRUE(ns_->SetOwner(path1, "bob", "group").IsOK());

  // list is root:root
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(!ns_->GetListing(base_path,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path: " << file_status.ShortDebugString() << ", permission: "
              << file_status.permission().ShortDebugString();
  }
  auto& owner = get_response.dirlist().partiallisting().Get(0).owner();
  auto& group = get_response.dirlist().partiallisting().Get(0).group();
  ASSERT_EQ(owner, "bob");
  ASSERT_EQ(group, "group");

  VLOG(10) << "SetOwner test finished";
}

TEST_F(NameSpaceTest, CreateFileWithOwner) {
  std::string path1 = "/create/create_file1";
  ns_->Delete(path1, true);

  FLAGS_permission_enabled = true;
  CreateFileWrp(path1, "bob", "group", true);

  auto info_response = GetFileInfoWrp(path1);
  ASSERT_EQ(info_response->fs().owner(), "bob");
  ASSERT_EQ(info_response->fs().group(), "group");
}

TEST_F(NameSpaceTest, CreateDirWithOwner) {
  std::string path1 = "/dir/subdir1";
  ns_->Delete(path1, true);

  FLAGS_permission_enabled = true;
  MkdirsWrp(path1, "bob", "group", true);

  auto info_response = GetFileInfoWrp(path1);
  ASSERT_EQ(info_response->fs().owner(), "bob");
  ASSERT_EQ(info_response->fs().group(), "group");
}

TEST_F(NameSpaceTest, CreateDirWithOwnerDefault) {
  {
    std::string path = "/dir/subdir1";
    ns_->Delete(path, true);
    MkdirsWrp(path, "bob", "bobg", true);

    auto info_response = GetFileInfoWrp(path);
    ASSERT_EQ(info_response->fs().owner(), "bob");
    ASSERT_EQ(info_response->fs().group(), "bobg");
  }

  LOG(INFO) << "---------------------";
  std::string path = "/dir/subdir1/123/456";
  MkdirsWrp(path, "bob", "", true);

  {
    auto info_response = GetFileInfoWrp("/dir/subdir1/123");
    ASSERT_EQ(info_response->fs().owner(), "bob");
    ASSERT_EQ(info_response->fs().group(), "bobg");
  }

  {
    auto info_response = GetFileInfoWrp("/dir/subdir1/123");
    ASSERT_EQ(info_response->fs().owner(), "bob");
    ASSERT_EQ(info_response->fs().group(), "bobg");
  }
}

TEST_F(NameSpaceTest, CreateFileWithAttr) {
  std::string path1 = "/create/create_file_attr";
  ASSERT_TRUE(ns_->Delete(path1, true).IsFalse());

  auto p = MakePermission();

  auto create_request = MakeCreateRequest();
  create_request.set_createparent(true);
  auto attr = create_request.add_attr();
  attr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  attr->set_name("test_attr");
  attr->set_value("true");
  CreateResponseProto response;

  ASSERT_TRUE(
      !ns_->CreateFile(path1, p, create_request, &response).HasException());

  RepeatedPtrField<XAttrProto> xattrs;
  ASSERT_EQ(ns_->ListXAttrs(path1, &xattrs, ugi_).code(), Code::kOK);
  ASSERT_EQ(xattrs.size(), 1);
  ASSERT_EQ(xattrs.Get(0).namespace_(),
            ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  ASSERT_EQ(xattrs.Get(0).name(), "test_attr");
  ASSERT_EQ(xattrs.Get(0).value(), "true");
}

TEST_F(NameSpaceTest, SetPermission) {
  std::string base_path = "/create";
  std::string path1 = "/create/create_file1";
  ns_->Delete(base_path, true);

  auto p = MakePermission();
  auto file_status = ::cloudfs::HdfsFileStatusProto();

  auto create_request = MakeCreateRequest();
  CreateResponseProto response;

  // create file /create/create_file1
  create_request.set_createparent(true);
  ASSERT_TRUE(
      !ns_->CreateFile(path1, p, create_request, &response).HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 2);

  // set permission to 777
  ASSERT_TRUE(ns_->SetPermission(path1, 0777).IsOK());

  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  ASSERT_TRUE(!ns_->GetListing(base_path,
                               NetworkLocationInfo(),
                               get_request,
                               &get_response,
                               ugi_)
                   .HasException());
  auto perm =
      get_response.dirlist().partiallisting().Get(0).permission().perm();
  ASSERT_EQ(perm, 0777);
  ASSERT_NE(perm, 0666);


  VLOG(10) << "SetPermission test finished";
}

TEST_F(NameSpaceTest, RefreshDataNodes) {
  // TODO need redis work properly
  auto status = ns_->RefreshDataNodes();
  LOG(INFO) << "code: " << static_cast<int>(status.code())
            << " str: " << status.CodeStr()
            << " has_exp: " << status.HasException();
}

TEST_F(NameSpaceTest, GetAdditionalDatanode) {
  AddDatanode("***********", "datanode2");
  AddDatanode("***********0", "datanode3");
  AddDatanode("***********0", "datanode4");

  cnetpp::base::IPAddress client_ip("***********");
  std::string path = "/test";

  auto create_request = MakeCreateRequest();
  create_request.set_replication(3);
  CreateResponseProto create_response;
  auto p = MakePermission();
  ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  DEFER([&]() { ns_->Delete(path, false); });
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  auto status = ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, &add_response);
  ASSERT_FALSE(status.HasException());
  ASSERT_EQ(add_response.block().locs().size(), 3);

  {
    cloudfs::GetAdditionalDatanodeRequestProto req;
    FillGetAdditionalDatanodeRequest(&req,
                                     {{"***********", "datanode2"},
                                      {"***********0", "datanode3"},
                                      {"***********0", "datanode4"}},
                                     1);
    req.mutable_blk()->CopyFrom(add_response.block().b());
    cloudfs::GetAdditionalDatanodeResponseProto resp;

    status = ns_->SyncGetAdditionalDatanode(path, client_ip, &req, &resp);
    ASSERT_FALSE(status.HasException());
    ASSERT_EQ(resp.block().locs().size(), 4);

    std::unordered_set<std::string> locs;
    for (auto& it : resp.block().locs()) {
      locs.insert(it.id().datanodeuuid());
    }
    std::unordered_set<std::string> expected = {
        "datanode1", "datanode2", "datanode3", "datanode4"};
    ASSERT_EQ(expected, locs);
  }

  {
    cloudfs::GetAdditionalDatanodeRequestProto req;
    FillGetAdditionalDatanodeRequest(&req,
                                     {{"***********", "datanode2"},
                                      {"***********0", "datanode3"},
                                      {"***********0", "datanode4"}},
                                     1);
    for (int i = 1; i <= 8; i++) {
      char ipaddr[32];
      char uuid[32];
      snprintf(ipaddr, 32, "%d.%d.%d.%d", i, i, i, i);
      snprintf(uuid, 32, "datanode%d%d%d%d", i, i, i, i);
      auto dn = req.mutable_excludes()->Add();
      dn->mutable_id()->set_ipaddr(ipaddr);
      dn->mutable_id()->set_datanodeuuid(uuid);
    }

    req.mutable_blk()->CopyFrom(add_response.block().b());
    cloudfs::GetAdditionalDatanodeResponseProto resp;

    status = ns_->SyncGetAdditionalDatanode(path, client_ip, &req, &resp);
    ASSERT_TRUE(status.HasException());
  }

  AddDatanode("************", "datanode5");

  {
    cloudfs::GetAdditionalDatanodeRequestProto req;
    FillGetAdditionalDatanodeRequest(&req,
                                     {{"***********", "datanode2"},
                                      {"***********0", "datanode3"},
                                      {"***********0", "datanode4"}},
                                     1);
    auto exclude_node = req.mutable_excludes()->Add();
    exclude_node->mutable_id()->set_ipaddr("***********");
    exclude_node->mutable_id()->set_datanodeuuid("datanode1");
    cloudfs::GetAdditionalDatanodeResponseProto resp;
    status = ns_->SyncGetAdditionalDatanode(path, client_ip, &req, &resp);
    ASSERT_FALSE(status.HasException());
    ASSERT_EQ(resp.block().locs().size(), 4);
    std::unordered_set<std::string> locs;
    for (auto& it : resp.block().locs()) {
      locs.insert(it.id().datanodeuuid());
    }
    std::unordered_set<std::string> expected = {
        "datanode2", "datanode3", "datanode4", "datanode5"};
    ASSERT_EQ(expected, locs);
  }

  {
    cloudfs::GetAdditionalDatanodeRequestProto req;
    FillGetAdditionalDatanodeRequest(&req,
                                     {{"***********", "datanode2"},
                                      {"***********0", "datanode3"},
                                      {"***********0", "datanode4"}},
                                     1);
    cloudfs::GetAdditionalDatanodeResponseProto resp;
    req.set_numadditionalnodes(3);
    status = ns_->SyncGetAdditionalDatanode(path, client_ip, &req, &resp);
    ASSERT_TRUE(status.HasException());
    ASSERT_EQ(status.message(),
              "Failed to choose additional 3 targets for file /test");
  }
}

TEST_F(NameSpaceTest, UpdatePipeline) {
  AddDatanode("***********", "datanode2");
  AddDatanode("***********0", "datanode3");
  AddDatanode("***********0", "datanode4");

  cnetpp::base::IPAddress client_ip("***********");
  std::string path = "/test";

  auto create_request = MakeCreateRequest();
  create_request.set_replication(3);
  CreateResponseProto create_response;
  auto p = MakePermission();
  ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  DEFER([&]() { ns_->Delete(path, false); });
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  auto status = ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, &add_response);
  auto block_id = add_response.block().b().blockid();
  auto gs = add_response.block().b().generationstamp();
  ASSERT_FALSE(status.HasException());

  GetBlockLocationsRequestProto get_request =
      MakeGetBlockLocationsRequest(path);
  GetBlockLocationsResponseProto get_response;
  ns_->GetBlockLocation(
      path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);

  auto located_blocks = get_response.locations();
  ASSERT_TRUE(located_blocks.underconstruction());
  ASSERT_EQ(located_blocks.blocks_size(), 0);
  ASSERT_EQ(located_blocks.lastblock().storageids().size(), 3);

  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), block_id);
  EXPECT_EQ(bip.gen_stamp(), gs);
  EXPECT_EQ(bip.num_bytes(), 0);
  EXPECT_EQ(bip.expected_rep(), FLAGS_dfs_replication);

  AddDatanode("************", "datanode5");
  std::vector<std::pair<std::string, std::string>> new_nodes = {
      {"***********0", "datanode3"},
      {"***********0", "datanode4"},
      {"************", "datanode5"}};

  {
    UpdatePipelineRequestProto req;
    MakeUpdatePipelineRequest(block_id, gs, gs + 1, new_nodes, &req);
    auto ret = ns_->UpdatePipeline(req, default_rpc_info);
    ASSERT_FALSE(ret.HasException());
    GetBlockLocationsRequestProto get_request =
        MakeGetBlockLocationsRequest(path);
    GetBlockLocationsResponseProto get_response;
    ns_->GetBlockLocation(
        path, NetworkLocationInfo(client_ip), get_request, &get_response, ugi_);
    auto located_blocks = get_response.locations();
    ASSERT_TRUE(located_blocks.underconstruction());
    ASSERT_EQ(located_blocks.blocks().size(), 0);
    auto storageids = located_blocks.lastblock().storageids();
    std::unordered_set<std::string> id_set;
    id_set.insert(storageids.begin(), storageids.end());
    for (auto it = new_nodes.begin(); it != new_nodes.end(); it++) {
      ASSERT_TRUE(id_set.find(it->second) != id_set.end());
    }

    BlockInfoProto bip;
    ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
    EXPECT_EQ(bip.block_id(), block_id);
    EXPECT_EQ(bip.gen_stamp(), gs + 1);
    EXPECT_EQ(bip.num_bytes(), 0);
    EXPECT_EQ(bip.expected_rep(), FLAGS_dfs_replication);
  }

  {
    UpdatePipelineRequestProto req;
    MakeUpdatePipelineRequest(block_id, gs, gs, new_nodes, &req);
    auto ret = ns_->UpdatePipeline(req, default_rpc_info);
    ASSERT_TRUE(ret.HasException());
    std::ostringstream str_buf;
    // gs is perisisted to meta storage
    // when invoking previous updatePipeline
    str_buf << "Update block (length = 0, gs = " << gs + 1
            << ") to state (length = 0, gs = " << gs << ")";
    ASSERT_EQ(ret.message(), str_buf.str());
  }

  {
    UpdatePipelineRequestProto req;
    MakeUpdatePipelineRequest(block_id, gs, gs + 1, new_nodes, &req);
    req.set_clientname("client-foobar");
    auto ret = ns_->UpdatePipeline(req, default_rpc_info);
    ASSERT_TRUE(ret.HasException());
    ASSERT_EQ(ret.message(), "No lease on /test: lease mismatch.");
  }
}

TEST_F(NameSpaceTest, UpdateBlockForPipeline) {
  AddDatanode("***********", "datanode2");
  AddDatanode("***********0", "datanode3");
  AddDatanode("***********0", "datanode4");

  cnetpp::base::IPAddress client_ip("***********");
  std::string path = "/test";

  auto create_request = MakeCreateRequest();
  create_request.set_replication(3);
  CreateResponseProto create_response;
  auto p = MakePermission();
  ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  DEFER([&]() { ns_->Delete(path, false); });
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  auto status = ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, &add_response);
  ASSERT_FALSE(status.HasException());

  auto gs = add_response.block().b().generationstamp();
  auto block_id = add_response.block().b().blockid();

  {
    UpdateBlockForPipelineRequestProto req;
    MakeUpdateBlockForPipelineRequest(block_id, &req);
    UpdateBlockForPipelineResponseProto response;
    auto ret = ns_->UpdateBlockForPipeline(req, default_rpc_info, &response);
    ASSERT_FALSE(ret.HasException());
    ASSERT_EQ(response.block().b().generationstamp(), gs + 1);
  }

  {
    UpdateBlockForPipelineRequestProto req;
    MakeUpdateBlockForPipelineRequest(block_id, &req);
    req.set_clientname("client-foo");
    UpdateBlockForPipelineResponseProto response;
    auto ret = ns_->UpdateBlockForPipeline(req, default_rpc_info, &response);
    ASSERT_TRUE(ret.HasException());
    ASSERT_EQ(ret.message(), "No lease on /test: lease mismatch.");
  }

  {
    UpdateBlockForPipelineRequestProto req;
    MakeUpdateBlockForPipelineRequest(block_id, &req);
    req.mutable_block()->set_blockid(0);
    UpdateBlockForPipelineResponseProto response;
    auto ret = ns_->UpdateBlockForPipeline(req, default_rpc_info, &response);
    ASSERT_TRUE(ret.HasException());
    ASSERT_EQ(ret.message(), "inode not found for block 0");
  }
}

TEST_F(NameSpaceTest, UpdateBlockForPipelineInCompleted) {
  std::string path = "/test";
  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  auto p = MakePermission();
  ASSERT_FALSE(ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());
  DEFER([&]() { ns_->Delete(path, false); });
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  auto status = ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, &add_response);
  ASSERT_FALSE(status.HasException());
  auto block_id_1st = add_response.block().b().blockid();
  auto gs_1st = add_response.block().b().generationstamp();

  BlockManager::RepeatedIncBlockReport report_1st;
  MakeReport(block_id_1st,
             gs_1st,
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report_1st);
  block_manager_->IncrementalBlockReport("datanode1", report_1st);
  ns_->GetBlockReportManager()->IncrementalBlockReport(
      1, "datanode1", report_1st);

  // Add 2nd block
  add_request.mutable_previous()->CopyFrom(add_response.block().b());
  add_request.mutable_previous()->set_generationstamp(gs_1st);
  add_request.mutable_previous()->set_blockid(block_id_1st);
  add_request.mutable_previous()->set_numbytes(100);
  add_response.Clear();
  status = ns_->AddBlock(
      path, client_ip, default_rpc_info, add_request, &add_response);
  ASSERT_FALSE(status.HasException());
  auto block_id_2nd = add_response.block().b().blockid();
  auto gs_2nd = add_response.block().b().generationstamp();

  // The 1st block should be COMPLETED
  auto state = block_manager_->GetBlockUCState(block_id_1st);
  ASSERT_EQ(state, BlockUCState::kComplete);
  BlockInfoProto bip;
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id_1st, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), block_id_1st);
  EXPECT_EQ(bip.gen_stamp(), gs_1st);
  EXPECT_EQ(bip.num_bytes(), 100);

  {
    UpdateBlockForPipelineRequestProto req;
    MakeUpdateBlockForPipelineRequest(block_id_1st, &req);
    UpdateBlockForPipelineResponseProto response;
    auto ret = ns_->UpdateBlockForPipeline(req, default_rpc_info, &response);
    ASSERT_TRUE(ret.HasException());
    ASSERT_EQ(ret.message(),
              "Block not in UC: " + std::to_string(block_id_1st));
  }

  BlockManager::RepeatedIncBlockReport report_2nd;
  MakeReport(block_id_2nd,
             gs_2nd,
             100,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report_2nd);
  block_manager_->IncrementalBlockReport("datanode1", report_2nd);
  ns_->GetBlockReportManager()->IncrementalBlockReport(
      1, "datanode1", report_2nd);

  CompleteRequestProto complete_request;
  complete_request.set_clientname("client");
  complete_request.mutable_last()->set_poolid(ns_->blockpool_id());
  complete_request.mutable_last()->set_blockid(block_id_2nd);
  complete_request.mutable_last()->set_generationstamp(gs_2nd);
  complete_request.mutable_last()->set_numbytes(100);
  ASSERT_FALSE(ns_->CompleteFile(path, complete_request).HasException());
  state = block_manager_->GetBlockUCState(block_id_2nd);
  ASSERT_EQ(state, BlockUCState::kComplete);
  bip.Clear();
  ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id_2nd, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.block_id(), block_id_2nd);
  EXPECT_EQ(bip.gen_stamp(), gs_2nd);
  EXPECT_EQ(bip.num_bytes(), 100);

  {
    UpdateBlockForPipelineRequestProto req;
    MakeUpdateBlockForPipelineRequest(block_id_1st, &req);
    UpdateBlockForPipelineResponseProto response;
    auto ret = ns_->UpdateBlockForPipeline(req, default_rpc_info, &response);
    ASSERT_TRUE(ret.HasException());
    ASSERT_EQ(ret.message(), "File not in UC: " + path);
  }
}

TEST_F(NameSpaceTest, GetStoragePolicies) {
  GetStoragePoliciesResponseProto resp;
  auto status = ns_->GetStoragePolicies(&resp);
  ASSERT_FALSE(status.HasException());
  ASSERT_EQ(resp.policies().size(), 11);
  std::unordered_set<uint32_t> result;
  for (const auto it : resp.policies()) {
    result.insert(it.policyid());
  }

  uint32_t ids[] = {
      kMemoryStoragePolicy,
      kOnlyDisk3StoragePolicy,
      kOnlySSDStoragePolicy,
      kAllSSDStoragePolicy,
      kOnlyDisk2StoragePolicy,
      kOneSSDStoragePolicy,
      kOneSSD2StoragePolicy,
      kHotStoragePolicy,
      kWarmStoragePolicy,
      kColdStoragePolicy,
  };

  for (auto it : ids) {
    ASSERT_NE(result.find(it), result.end());
  }
}

TEST_F(NameSpaceTest, SetGetRootReplicaPolicy) {
  {
    ReplicaPolicy policy;
    ASSERT_TRUE(ns_->GetReplicaPolicyByINodeId(kRootINodeId, &policy));
    CHECK_EQ(policy.distributed(), false);
    CHECK_EQ(policy.dc_size(), 0);
  }
  std::string root_path = "/";
  auto s = ns_->SetReplicaPolicy(root_path, kDistributePolicy, "LF,HL");
  ASSERT_TRUE(!s.HasException());

  {
    ReplicaPolicy policy;
    ASSERT_TRUE(ns_->GetReplicaPolicyByINodeId(kRootINodeId, &policy));
    CHECK_EQ(policy.distributed(), true);
    CHECK_EQ(policy.dc_size(), 2);
    CHECK_EQ(policy.dc().Get(0), "LF");
    CHECK_EQ(policy.dc().Get(1), "HL");
  }

  {
    auto p = MakePermission();
    std::string test_dir = "/test_dir";
    std::string sub_dir = "/test_dir/sub_dir";
    ASSERT_TRUE(!ns_->MkDirs(sub_dir, p, true).HasException());
    GetReplicaPolicyResponseProto response;
    ns_->GetReplicaPolicy(sub_dir, &response);
    ASSERT_EQ(response.id(), kDistributePolicy);
    ASSERT_EQ(response.dc(), "LF,HL");
  }
}

TEST_F(NameSpaceTest, SetGetReplicaPolicy) {
  ns_->Delete("/", true);
  cnetpp::base::IPAddress ip;
  auto p = MakePermission();
  std::string test_dir = "/test_dir";
  std::string sub_dir = "/test_dir/sub_dir";
  ASSERT_TRUE(!ns_->MkDirs(sub_dir, p, true).HasException());

  {
    GetDistributedResponseProto r;
    ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
    ASSERT_EQ(0, r.paths_size());
  }

  {
    std::string path = sub_dir + "/test1";
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    GetReplicaPolicyResponseProto response;
    ns_->GetReplicaPolicy(path, &response);
    ASSERT_EQ(response.id(), kCentralizePolicy);
    ASSERT_EQ(response.dc(), kEnforceDCUnspecified);

    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(0, r.paths_size());
    }

    auto s = ns_->SetReplicaPolicy(path, kDistributePolicy, "LF,HL");
    ASSERT_TRUE(s.HasException());
    ASSERT_EQ(s.message(), "File-level replica policy is not supported");
  }

  {
    ASSERT_FALSE(ns_->SetReplicaPolicy(test_dir, kDistributePolicy, "LF,HL")
                     .HasException());
    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(1, r.paths_size());
      ASSERT_EQ(test_dir, r.paths(0));
    }

    std::string path = sub_dir + "/test2";
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    GetReplicaPolicyResponseProto response;
    ns_->GetReplicaPolicy(path, &response);
    ASSERT_EQ(response.id(), kDistributePolicy);
    ASSERT_EQ(response.dc(), "LF,HL");
  }

  {
    ASSERT_FALSE(
        ns_->SetReplicaPolicy(sub_dir, kCentralizePolicy, "LF").HasException());
    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(2, r.paths_size());
      ASSERT_EQ(test_dir, r.paths(0));
    }

    std::string path = sub_dir + "/test3";
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                     .HasException());
    GetReplicaPolicyResponseProto response;
    ns_->GetReplicaPolicy(path, &response);
    ASSERT_EQ(response.id(), kCentralizePolicy);
    ASSERT_EQ(response.dc(), "LF");
  }

  {
    ASSERT_FALSE(ns_->SetReplicaPolicy(
                        test_dir, kCentralizePolicy, kEnforceDCUnspecified)
                     .HasException());
    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(2, r.paths_size());
    }
  }

  {
    ASSERT_FALSE(ns_->SetReplicaPolicy(test_dir, kDistributePolicy, "LF,HL")
                     .HasException());
    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(2, r.paths_size());
      ASSERT_EQ(test_dir, r.paths(0));
    }
  }

  {
    ASSERT_FALSE(
        ns_->SetReplicaPolicy(sub_dir, kCentralizePolicy, "").HasException());
    {
      GetDistributedResponseProto r;
      ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
      ASSERT_EQ(2, r.paths_size());
      ASSERT_EQ(test_dir, r.paths(0));
    }

    ASSERT_TRUE(ns_->SetReplicaPolicy(sub_dir, kCentralizePolicy, "LF,HL")
                    .HasException());

    ASSERT_TRUE(ns_->SetReplicaPolicy(sub_dir, kCentralizePolicy, "123")
                    .HasException());

    ASSERT_FALSE(
        ns_->SetReplicaPolicy(sub_dir, kCentralizePolicy, "LF").HasException());

    ASSERT_TRUE(
        ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "").HasException());

    ASSERT_TRUE(
        ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "LF").HasException());

    ASSERT_FALSE(ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "LF,HL,LQ")
                     .HasException());

    ASSERT_TRUE(ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "LF,HL,LQ,BJ")
                    .HasException());

    ASSERT_TRUE(ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "LF,123")
                    .HasException());

    ASSERT_FALSE(ns_->SetReplicaPolicy(sub_dir, kDistributePolicy, "HL,LQ")
                     .HasException());

    ASSERT_TRUE(ns_->SetReplicaPolicy(sub_dir, 3, "HL,LQ").HasException());
  }
}

TEST_F(NameSpaceTest, SetGetReplicaPolicyV2) {
  ns_->Delete("/", true);
  cnetpp::base::IPAddress ip;
  auto p = MakePermission();
  std::string test_dir = "/test_dir";
  std::string sub_dir = "/test_dir/sub_dir";
  ASSERT_TRUE(!ns_->MkDirs(sub_dir, p, true).HasException());

  std::string path = sub_dir + "/a";
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::string dc = "LF,HL";
  auto s = ns_->SetReplicaPolicy(path, kDistributePolicy, dc);
  ASSERT_TRUE(!s.HasException());
  GetReplicaPolicyResponseProto response;
  ns_->GetReplicaPolicy(path, &response);
  ASSERT_EQ(response.id(), kDistributePolicy);
  ASSERT_EQ(response.dc(), dc);

  GetDistributedResponseProto r;
  ASSERT_TRUE(!ns_->GetDistributed(&r).HasException());
  ASSERT_EQ(1, r.paths_size());
  ASSERT_EQ(r.ids(0), kDistributePolicy);
  ASSERT_EQ(r.dcs(0), dc);

  std::string xattr_name_pb = "hdfs.replica.policy.pb";
  XAttrProto x;
  x.set_namespace_(
      XAttrProto::XAttrNamespaceProto::XAttrProto_XAttrNamespaceProto_SYSTEM);
  x.set_name(xattr_name_pb);
  RepeatedPtrField<XAttrProto> xattrs;
  xattrs.Add()->CopyFrom(x);
  RepeatedPtrField<XAttrProto> list_resp;
  ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kOK);
  ASSERT_EQ(list_resp.size(), 1);

  std::string path2 = path + "/b";
  ASSERT_TRUE(!ns_->MkDirs(path2, p, true).HasException());

  dc = "LF";
  s = ns_->SetReplicaPolicy(path2, kCentralizePolicy, dc);
  ASSERT_TRUE(!s.HasException());
  GetReplicaPolicyResponseProto response2;
  ns_->GetReplicaPolicy(path2, &response2);
  ASSERT_EQ(response2.id(), kCentralizePolicy);
  ASSERT_EQ(response2.dc(), dc);

  GetDistributedResponseProto r2;
  ASSERT_TRUE(!ns_->GetDistributed(&r2).HasException());
  ASSERT_EQ(2, r2.paths_size());
  ASSERT_EQ(r2.ids(1), kCentralizePolicy);
  ASSERT_EQ(r2.dcs(1), dc);

  s = ns_->SetReplicaPolicy(path, kNonePolicy, kEnforceDCUnspecified);
  ASSERT_TRUE(!s.HasException());
  GetDistributedResponseProto r3;
  ASSERT_TRUE(!ns_->GetDistributed(&r3).HasException());
  ASSERT_EQ(1, r3.paths_size());
  ASSERT_EQ(r3.ids(0), kCentralizePolicy);
  ASSERT_EQ(r3.dcs(0), dc);

  RepeatedPtrField<XAttrProto> list_resp2;
  ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp2, ugi_).code(), Code::kOK);
  ASSERT_EQ(list_resp2.size(), 0);

  s = ns_->SetReplicaPolicy(path, kCentralizePolicy, dc);
  ASSERT_TRUE(!s.HasException());
  GetDistributedResponseProto r4;
  ASSERT_TRUE(!ns_->GetDistributed(&r4).HasException());
  ASSERT_EQ(2, r4.paths_size());
  ASSERT_EQ(r4.ids(0), kCentralizePolicy);
  ASSERT_EQ(r4.dcs(0), dc);
  ASSERT_EQ(r4.ids(1), kCentralizePolicy);
  ASSERT_EQ(r4.dcs(1), dc);
}

TEST_F(NameSpaceTest, GetContentSummary) {
  auto local = [] {
    auto saved = FLAGS_dfs_symlinks_enabled;
    FLAGS_dfs_symlinks_enabled = true;
    return [saved] { FLAGS_dfs_symlinks_enabled = saved; };
  };
  DEFER(local());

  auto p = MakePermission();
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  ASSERT_TRUE(ns_->MkDirs("/D0/D1/D2", p, true).IsOK());
  ASSERT_TRUE(ns_->MkDirs("/D0/D1/D3", p, true).IsOK());
  ASSERT_TRUE(ns_->MkDirs("/D0/D1/D4", p, true).IsOK());
  AddFile("/D0/D1/D2/F0", 100, 1, true, &add_response, &create_response);
  ASSERT_TRUE(ns_->MkDirs("/D0/D1/D2/D5", p, true).IsOK());
  AddFile("/D0/D1/D2/F1", 99, 3, true, &add_response, &create_response);
  ASSERT_TRUE(ns_->MkDirs("/D0/D1/D2/D6", p, true).IsOK());
  ASSERT_TRUE(
      ns_->CreateSymlink("/D0/D1/D2/F0", "/D0/D1/D3/S0", p, false).IsOK());

  auto ugi = std::make_shared<UserGroupInfo>();
  GetContentSummaryResponseProto response;
  // /D0/D1/D3/S0
  ASSERT_TRUE(ns_->GetContentSummary("/D0/D1/D3/S0", ugi, &response).IsOK());
  LOG(INFO) << "GetContentSummary of '/D0/D1/D3/S0': "
            << response.DebugString();
  ASSERT_EQ(response.summary().length(), 0);
  ASSERT_EQ(response.summary().directorycount(), 0);
  ASSERT_EQ(response.summary().filecount(), 1);

  // /D0/D1/D2/F1
  ASSERT_TRUE(ns_->GetContentSummary("/D0/D1/D2/F1", ugi, &response).IsOK());
  LOG(INFO) << "GetContentSummary of '/D0/D1/D2/F1': "
            << response.DebugString();
  ASSERT_EQ(response.summary().length(), 99);
  ASSERT_EQ(response.summary().spaceconsumed(), 297);
  ASSERT_EQ(response.summary().directorycount(), 0);
  ASSERT_EQ(response.summary().filecount(), 1);

  // /D0/D1/D4
  ASSERT_TRUE(ns_->GetContentSummary("/D0/D1/D4", ugi, &response).IsOK());
  LOG(INFO) << "GetContentSummary of '/D0/D1/D4': " << response.DebugString();
  ASSERT_EQ(response.summary().length(), 0);
  ASSERT_EQ(response.summary().spaceconsumed(), 0);
  ASSERT_EQ(response.summary().directorycount(), 1);
  ASSERT_EQ(response.summary().filecount(), 0);

  // /
  {
    auto s = ns_->GetContentSummary("/", ugi, &response);
    LOG(INFO) << "GetContentSummary of '/': " << response.DebugString();
    LOG(INFO) << s.ToString();
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(response.summary().length(), 199);
    ASSERT_EQ(response.summary().spaceconsumed(), 397);
    ASSERT_EQ(response.summary().directorycount(), kNumReservedINode + 8);
    ASSERT_EQ(response.summary().filecount(), 3);
  }

  // /
  {
    FLAGS_dfs_summary_min_depth = 1;
    auto s = ns_->GetContentSummary("/", ugi, &response);
    LOG(INFO) << "GetContentSummary of '/': " << response.DebugString();
    LOG(INFO) << s.ToString();
    ASSERT_TRUE(!s.IsOK());
    ASSERT_TRUE(s.exception() == JavaExceptions::Exception::kIOException);
  }
}

#if 0
TEST_F(NameSpaceTest, ByteCool) {
  // Create ByteCool test file.
  std::string path = "/bytecool_test_file";
  ns_->Delete(path, true);
  auto p = MakePermission();
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  ASSERT_TRUE(!ns_->CreateFile(path, p, create_request, &create_response)
                   .HasException());

  // Set extended attributes for ByteCool temperature awareness.
  const std::string xattr_ns = "trusted";

  // Set object ID.
  XAttrProto object_id;
  object_id.set_namespace_(
      ::cloudfs::XAttrProto_XAttrNamespaceProto_TRUSTED);
  object_id.set_name(std::string(kObjectId).substr(xattr_ns.size() + 1));
  std::string mock_object_id = "00000000-0000-0000-0000-000000000000";
  object_id.set_value(mock_object_id);
  ASSERT_EQ(ns_->SetXAttr(path, object_id, true, false).code(), Code::kOK);

  // Set cold temperature.
  XAttrProto temperature;
  temperature.set_namespace_(
      ::cloudfs::XAttrProto_XAttrNamespaceProto_TRUSTED);
  temperature.set_name(std::string(kTemperature).substr(xattr_ns.size() + 1));
  temperature.set_value(kTemperatureCool);
  ASSERT_EQ(ns_->SetXAttr(path, temperature, true, false).code(), Code::kOK);

  // Set temperature modification time.
  XAttrProto temperature_mtime;
  temperature_mtime.set_namespace_(
      ::cloudfs::XAttrProto_XAttrNamespaceProto_TRUSTED);
  temperature_mtime.set_name(
      std::string(kTemperatureMtime).substr(xattr_ns.size() + 1));
  std::string mock_temperature_mtime = "1000000000";
  temperature_mtime.set_value(mock_temperature_mtime);
  ASSERT_EQ(ns_->SetXAttr(path, temperature_mtime, true, false).code(),
            Code::kOK);

  // Test RPCs for file in COOL temperature.

  // GetFileInfo
  GetFileInfoResponseProto get_file_info_response;
  ASSERT_FALSE(
      ns_->GetFileInfo(path,NetworkLocationInfo(), false, false, &get_file_info_response, ugi_).HasException());
  ASSERT_EQ(get_file_info_response.fs().object_id(), mock_object_id);
  ASSERT_EQ(get_file_info_response.fs().temperature(), kTemperatureCool);
  ASSERT_EQ(get_file_info_response.fs().temperature_mtime(),
            mock_temperature_mtime);

  // Append
  std::string client_machine = "mock_client_machine";
  std::string client_name = "mock_client_name";
  auto append_request = MakeAppendRequest(path, client_name);
  AppendResponseProto append_response;
  auto status = ns_->Append(
      path, client_machine, default_rpc_info, append_request, &append_response);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // GetBlockLocations
  cnetpp::base::IPAddress client_ip("***********");
  GetBlockLocationsRequestProto get_block_locations_request;
  get_block_locations_request.set_src(path);
  get_block_locations_request.set_offset(0);
  get_block_locations_request.set_length(10);
  GetBlockLocationsResponseProto get_block_locations_response;
  auto ret = ns_->GetBlockLocation(path,
                                   client_ip,
                                   get_block_locations_request,
                                   &get_block_locations_response, ugi_);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // GetListing
  ASSERT_TRUE(
      !ns_->CreateFile(
              "/get_listing_test_file", p, create_request, &create_response)
           .HasException());
  GetListingRequestProto get_listing_request;
  get_listing_request.set_src("/");
  get_listing_request.set_startafter("");
  get_listing_request.set_needlocation(true);
  GetListingResponseProto get_listing_response;
  ASSERT_TRUE(!ns_->GetListing("/", get_listing_request, &get_listing_response, ugi_)
                   .HasException());
  for (const auto& file_status :
       get_listing_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type: " << file_status.filetype()
              << ", length: " << file_status.length()
              << ", object id: " << file_status.object_id()
              << ", temperature: " << file_status.temperature()
              << ", temperature mtime: " << file_status.temperature_mtime();
  }

  // CompleteFile
  CompleteRequestProto complete_request;
  complete_request.set_clientname(create_request.clientname());
  status = ns_->CompleteFile(path, complete_request);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // Fsync
  FsyncRequestProto sync_request;
  sync_request.set_src(path);
  sync_request.set_client("client");
  status = ns_->Fsync(path, sync_request);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // AddBlock
  AddBlockResponseProto add_block_response;
  auto add_request = MakeAddBlockRequest();
  status = ns_->AddBlock(
      path, client_ip, default_rpc_info,add_request,  &add_block_response);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // GetAdditionalDatanode
  GetAdditionalDatanodeRequestProto get_additional_datanode_request;
  FillGetAdditionalDatanodeRequest(&get_additional_datanode_request,
                                   {{"***********", "datanode2"},
                                    {"***********0", "datanode3"},
                                    {"***********0", "datanode4"}},
                                   1);
  GetAdditionalDatanodeResponseProto get_additional_datanode_response;
  status = ns_->SyncGetAdditionalDatanode(path,
                                          client_ip,
                                          &get_additional_datanode_request,
                                          &get_additional_datanode_response);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // AbandonBlock
  AbandonBlockRequestProto abandon_block_request;
  abandon_block_request.set_src(path);
  abandon_block_request.set_holder("client");
  status = ns_->AbandonBlock(path, abandon_block_request);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // SetStoragePolicy
  status = ns_->SetStoragePolicy(path, "ALL_SSD");
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // SetReplicaPolicy
  status = ns_->SetReplicaPolicy(path, kDistributePolicy, "LF,HL");
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);

  // SetTimes
  SetTimesRequestProto set_times_request;
  set_times_request.set_src(path);
  set_times_request.set_mtime(0);
  set_times_request.set_atime(0);
  status = ns_->SetTimes(path, set_times_request);
  ASSERT_EQ(status.exception(), JavaExceptions::kReadOnlyCoolFileException);
  ASSERT_EQ(status.message(), mock_object_id);
}
#endif

TEST_F(NameSpaceTest, DeleteDirUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4/d5";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("supergroup");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  INode inode3;
  INode inode4;
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode4, nullptr, &inode3),
            StatusCode::kOK);

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  // delete d5
  path = "/d1/d2/d3/d4/d5";
  auto s = ns_->Delete(path, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode4, new_inode4));
  ASSERT_TRUE(INodeCompare(inode3, new_inode3));

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  inode3.CopyFrom(new_inode3);
  inode4.CopyFrom(new_inode4);
  path = "/d1/d2/d3/d4/file.txt";
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  ASSERT_TRUE(
      !ns_->CreateFile(path, ps, create_request, &response).HasException());
  // get d3, d4
  new_inode3.Clear();
  new_inode4.Clear();
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode4, new_inode4));
  ASSERT_TRUE(INodeCompare(inode3, new_inode3));

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  // delete file.txt
  inode3.CopyFrom(new_inode3);
  inode4.CopyFrom(new_inode4);
  path = "/d1/d2/d3/d4/file.txt";
  s = ns_->Delete(path, true);
  ASSERT_TRUE(!s.HasException());
  ASSERT_TRUE(s.IsOK());
  // get d3, d4
  new_inode3.Clear();
  new_inode4.Clear();
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode4, new_inode4));
  ASSERT_TRUE(INodeCompare(inode3, new_inode3));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(3000));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  EXPECT_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, InheriStorageolicy) {
  ns_->StopBGDeletionWorker();
  std::string parent_path = "/storage";
  std::string path = "/storage/ssd";
  std::string file_path = "/storage/ssd/file";

  PermissionStatus ps;
  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::string policy_name = "ALL_SSD";
  ASSERT_FALSE(ns_->SetStoragePolicy(parent_path, policy_name).HasException());

  CreateResponseProto create_response;
  auto create_request = MakeCreateRequest();
  // This bug only occurs when createparent is set to false,
  // Which is the situation we need to verify.
  create_request.set_createparent(false);
  PermissionStatus p;
  ASSERT_FALSE(ns_->CreateFile(file_path, p, create_request, &create_response)
                   .HasException());

  GetFileInfoResponseProto get_response;
  auto status = ns_->GetFileInfo(
      file_path, NetworkLocationInfo(), true, false, &get_response, ugi_);
  ASSERT_FALSE(status.HasException());
  ASSERT_TRUE(get_response.has_fs());
  ASSERT_TRUE(get_response.fs().has_storagepolicy());
  auto storage_policy =
      static_cast<StoragePolicyId>(get_response.fs().storagepolicy());
  ASSERT_EQ(storage_policy, StoragePolicyId::kAllSSDStoragePolicy);
}

#if 0 // Disable INode Cache case; INode Cache is buggy, we have disabled it in production
TEST_F(NameSpaceTest, CreateUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  path = "/d1/d2/d3/d4/file.txt";
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  ASSERT_TRUE(
      !ns_->CreateFile(path, ps, create_request, &response).HasException());
  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
  ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, MkDirsUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  path += "/d5";
  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());
  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
  ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, RenameToUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  path = "/d1/d2/d3/d4/file.txt";
  auto create_request = MakeCreateRequest();
  CreateResponseProto response;
  ASSERT_TRUE(
      !ns_->CreateFile(path, ps, create_request, &response).HasException());
  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache & mtime is different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
  ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  tmp_inode.CopyFrom(new_inode4);
  tmp_parent.CopyFrom(new_inode3);
  std::string src = "/d1/d2/d3/d4/file.txt";
  std::string dst = "/d1/d2/d3/file.txt";
  ASSERT_TRUE(ns_->RenameTo(src, dst).IsOK());
  // get d3, d4
  new_inode3.Clear();
  new_inode4.Clear();
  // d3, d4 miss cache & mtime are both different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
  ASSERT_FALSE(INodeCompare(tmp_parent, new_inode3));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, RenameTo2UpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4/d5";
  std::string path2 = "/d11/d22/d33/d44";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());
  ASSERT_TRUE(!ns_->MkDirs(path2, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  // get d3, d4
  INode inode3;
  INode inode4;
  // hit cache
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode4, nullptr, &inode3),
            StatusCode::kOK);
  // get d33, d44
  INode inode33;
  INode inode44;
  // hit cache
  path = "/d11/d22/d33/d44";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &inode44, nullptr, &inode33),
      StatusCode::kOK);

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  std::string src = "/d1/d2/d3/d4/d5";
  std::string dst = "/d11/d22/d33/d44";
  // d44's overwrote by d5, but why its name didn't change to d5 ???
  ASSERT_TRUE(!ns_->RenameTo2(src, dst, true).HasException());

  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d3,d4 miss cache & mtime are both different then before
  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode4, new_inode4));
  ASSERT_TRUE(INodeCompare(inode3, new_inode3));
  // get d33, d44
  INode new_inode33;
  INode new_inode44;
  // d44 miss cache & mtime is different then before
  path = "/d11/d22/d33/d44";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode44, nullptr, &new_inode33),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode44, new_inode44));
  ASSERT_FALSE(INodeCompare(inode33, new_inode33));

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  src = "/d1/d2/d3/d4";
  dst = "/d11/d22/d33/d44/d4";
  ASSERT_TRUE(!ns_->RenameTo2(src, dst, true).HasException());

  // get d3, d4
  new_inode3.Clear();
  // d3 miss cache & mtime is different then before
  path = "/d1/d2/d3";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &new_inode3, nullptr, &parent),
      StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode3, new_inode3));
  // get d33, d44
  inode33.CopyFrom(new_inode33);
  inode44.CopyFrom(new_inode44);
  new_inode33.Clear();
  new_inode44.Clear();
  // d44 miss cache & mtime is different then before
  path = "/d11/d22/d33/d44";
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode44, nullptr, &new_inode33),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(inode44, new_inode44));
  ASSERT_TRUE(INodeCompare(inode33, new_inode33));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, SetStoragePolicyUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  std::string policy_name = "ALL_SSD";
  ASSERT_FALSE(ns_->SetStoragePolicy(path, policy_name).HasException());
  auto policy = datanode_manager_->GetStoragePolicyByName(policy_name);
  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache & mtime,storage_policy_id are different then before
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
  ASSERT_EQ(new_inode4.storage_policy_id(), policy->id());
  ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, SetReplicaPolicyUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  std::string dcs = "LF,HL";
  auto s = ns_->SetReplicaPolicy(path, kDistributePolicy, dcs);
  ASSERT_TRUE(!s.HasException());

  // get d3, d4
  INode new_inode3;
  INode new_inode4;
  // d4 miss cache
  ASSERT_TRUE(SplitPath(path, &path_components));
  ASSERT_EQ(ns_->GetLastINodeInPath(
                path_components, &new_inode4, nullptr, &new_inode3),
            StatusCode::kOK);
  ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));
  ASSERT_TRUE(
      ns_->GetReplicaPolicyByINodeId( new_inode4.id(), &policy));
  CHECK_EQ(policy_id, kDistributePolicy);
  CHECK_EQ(enforce_dc, dcs);
  for (int i = 0; i < 3; i++) {
    new_inode4.Clear();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &new_inode4),
              StatusCode::kOK);
    ASSERT_TRUE(ns_->GetReplicaPolicyByINodeId(
        new_inode4.id(), &policy));
    CHECK_EQ(policy_id, kDistributePolicy);
    CHECK_EQ(enforce_dc, dcs);
  }

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, SetPermissionUpdateCache) {
  std::string path = "/d1/d2/d3/d4";
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  ASSERT_TRUE(ns_->SetPermission(path, 0777).IsOK());

  path = "/d1/d2/d3";
  for (int i = 0; i < 3; i++) {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(path, NetworkLocationInfo(),get_request, &get_response, ugi_).HasException());
    auto perm =
        get_response.dirlist().partiallisting().Get(0).permission().perm();
    ASSERT_EQ(perm, 0777);
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(ns_->SetPermission(path, 0775).IsOK());

  path = "/d1/d2/d3";
  for (int i = 0; i < 3; i++) {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(path, NetworkLocationInfo(),get_request, &get_response, ugi_).HasException());
    auto perm =
        get_response.dirlist().partiallisting().Get(0).permission().perm();
    ASSERT_EQ(perm, 0775);
    ASSERT_NE(perm, 0777);
  }
}

TEST_F(NameSpaceTest, SetOwnerUpdateCache) {
  std::string path = "/d1/d2/d3/d4";
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  ASSERT_TRUE(ns_->SetOwner(path, "bob", "group").IsOK());

  path = "/d1/d2/d3";
  for (int i = 0; i < 3; i++) {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(path, NetworkLocationInfo(),get_request, &get_response, ugi_).HasException());

    for (const auto& file_status : get_response.dirlist().partiallisting()) {
      LOG(INFO) << "path: " << file_status.ShortDebugString()
                << ", permission: "
                << file_status.permission().ShortDebugString();
    }
    auto& owner = get_response.dirlist().partiallisting().Get(0).owner();
    auto& group = get_response.dirlist().partiallisting().Get(0).group();
    ASSERT_EQ(owner, "bob");
    ASSERT_EQ(group, "group");
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  path = "/d1/d2/d3/d4";
  ASSERT_TRUE(ns_->SetOwner(path, "bob123", "group").IsOK());

  path = "/d1/d2/d3";
  for (int i = 0; i < 3; i++) {
    GetListingRequestProto get_request;
    get_request.set_startafter("");
    get_request.set_needlocation(false);
    GetListingResponseProto get_response;
    ASSERT_TRUE(
        !ns_->GetListing(path, NetworkLocationInfo(),get_request, &get_response, ugi_).HasException());

    for (const auto& file_status : get_response.dirlist().partiallisting()) {
      LOG(INFO) << "path: " << file_status.ShortDebugString()
                << ", permission: "
                << file_status.permission().ShortDebugString();
    }
    auto& owner = get_response.dirlist().partiallisting().Get(0).owner();
    auto& group = get_response.dirlist().partiallisting().Get(0).group();
    ASSERT_EQ(owner, "bob123");
    ASSERT_EQ(group, "group");
  }
}

TEST_F(NameSpaceTest, SetTimesUpdateCache) {
  ns_->StopBGDeletionWorker();
  auto bg_deletion_process_pending_delete_interval_sec_backup =
      FLAGS_bg_deletion_process_pending_delete_interval_sec;
  DEFER([&]() {
    FLAGS_bg_deletion_process_pending_delete_interval_sec =
        bg_deletion_process_pending_delete_interval_sec_backup;
  });
  FLAGS_bg_deletion_process_pending_delete_interval_sec = 1;

  std::string path = "/d1/d2/d3/d4";

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  ASSERT_TRUE(!ns_->MkDirs(path, ps, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  uint64_t now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::system_clock::now().time_since_epoch())
                        .count();
  SetTimesRequestProto set_times_request;
  set_times_request.set_mtime(now_ms);
  set_times_request.set_atime(now_ms);
  ASSERT_TRUE(!ns_->SetTimes(path, set_times_request).HasException());
  for (int i = 0; i < 3; i++) {
    // get d3, d4
    INode new_inode3;
    INode new_inode4;
    // d4 miss cache & mtime,atime are different then before
    ASSERT_TRUE(SplitPath(path, &path_components));
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &new_inode4, nullptr, &new_inode3),
              StatusCode::kOK);
    ASSERT_FALSE(INodeCompare(tmp_inode, new_inode4));
    ASSERT_EQ(new_inode4.mtime(), now_ms);
    ASSERT_EQ(new_inode4.atime(), now_ms);
    ASSERT_TRUE(INodeCompare(tmp_parent, new_inode3));
  }

  ASSERT_FALSE(ns_->Delete("/d1", true).HasException());
  ns_->StartBGDeletionWorker();
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  std::vector<INode> inodes;
  bool has_more;
  auto d1_inode_id = kLastReservedINodeId + 1;
  ns_->meta_storage()->GetSubINodes(d1_inode_id, "", 5, &inodes, &has_more);
  CHECK_EQ(inodes.size(), 0);
}

TEST_F(NameSpaceTest, SetXAttrUpdateCache) {
  std::string path = "/d1/d2/d3/d4";
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  std::string test_xattr_name = "hdfs.nstest.xattr.mockx";
  XAttrProto x;
  x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  x.set_name(test_xattr_name);
  x.set_value("1");
  ASSERT_EQ(ns_->SetXAttr(path, x, true, false).code(), Code::kOK);

  for (int i = 0; i < 3; i++) {
    RepeatedPtrField<XAttrProto> xattrs;
    RepeatedPtrField<XAttrProto> list_resp;
    ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kOK);
    ASSERT_EQ(list_resp.size(), 1);
    ASSERT_EQ(list_resp.Get(0).namespace_(),
              ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    ASSERT_EQ(list_resp.Get(0).name(), test_xattr_name);
    ASSERT_EQ(list_resp.Get(0).value(), "1");
  }
}

TEST_F(NameSpaceTest, RemoveXAttrUpdateCache) {
  std::string path = "/d1/d2/d3/d4";
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  std::string test_xattr_name = "hdfs.nstest.xattr.mockx";
  XAttrProto x;
  x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  x.set_name(test_xattr_name);
  x.set_value("1");
  ASSERT_EQ(ns_->SetXAttr(path, x, true, false).code(), Code::kOK);

  for (int i = 0; i < 3; i++) {
    RepeatedPtrField<XAttrProto> xattrs;
    RepeatedPtrField<XAttrProto> list_resp;
    ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kOK);
    ASSERT_EQ(list_resp.size(), 1);
    ASSERT_EQ(list_resp.Get(0).namespace_(),
              ::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    ASSERT_EQ(list_resp.Get(0).name(), test_xattr_name);
    ASSERT_EQ(list_resp.Get(0).value(), "1");
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(300));

  XAttrProto y;
  y.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  y.set_name(test_xattr_name);
  ASSERT_EQ(ns_->RemoveXAttr(path, y).code(), Code::kOK);

  for (int i = 0; i < 3; i++) {
    RepeatedPtrField<XAttrProto> xattrs;
    RepeatedPtrField<XAttrProto> list_resp;
    ASSERT_EQ(ns_->GetXAttrs(path, xattrs, &list_resp, ugi_).code(), Code::kOK);
    ASSERT_EQ(list_resp.size(), 0);
  }
}

#endif

TEST_F(NameSpaceTest, ChmodForDirAndFile) {
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("supergroup");
  // 1. dir tests.
  {
    p.set_permission(0700);
    CreateFileAndVerifyStatus("/d1", p, true, "rwx------");
  }

  {
    p.set_permission(0070);
    CreateFileAndVerifyStatus("/d2", p, true, "---rwx---");
  }

  {
    p.set_permission(0007);
    CreateFileAndVerifyStatus("/d3", p, true, "------rwx");
  }

  {
    p.set_permission(01777);
    CreateFileAndVerifyStatus("/d4", p, true, "rwxrwxrwt");
  }

  {
    p.set_permission(01776);
    CreateFileAndVerifyStatus("/d5", p, true, "rwxrwxrwT");
  }

  // 2. file tests.
  {
    p.set_permission(0700);
    CreateFileAndVerifyStatus("/f1", p, false, "rwx------");
  }

  {
    p.set_permission(0070);
    CreateFileAndVerifyStatus("/f2", p, false, "---rwx---");
  }

  {
    p.set_permission(0007);
    CreateFileAndVerifyStatus("/f3", p, false, "------rwx");
  }

  {
    p.set_permission(01777);
    CreateFileAndVerifyStatus("/f4", p, false, "rwxrwxrwt");
  }

  {
    p.set_permission(01776);
    CreateFileAndVerifyStatus("/f5", p, false, "rwxrwxrwT");
  }
}

TEST_F(NameSpaceTest, MkdirPermImplicitlyCreatedAncestor) {
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("supergroup");
  p.set_permission(0733);
  // 1. create ancestor, with 0733 permission.
  CreateFileAndVerifyStatus("/ancestor", p, true, "rwx-wx-wx");

  p.set_permission(0411);
  // 2. create grand child, its' permisson should be 0411.
  CreateFileAndVerifyStatus("/ancestor/d1/d2", p, true, "r----x--x");

  // 3. verify it's parent should be 0411 + u+wx = 711.
  VerifyFileWithStatus("/ancestor/d1", true, "rwx--x--x");
}

TEST_F(NameSpaceTest, CreateFilePermImplicitlyCreatedAncestor) {
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("supergroup");
  p.set_permission(0733);
  // 1. create ancestor, with 0733 permission.
  CreateFileAndVerifyStatus("/ancestor", p, true, "rwx-wx-wx");

  p.set_permission(0411);
  // 2. create grand child, its' permisson should be 0411.
  CreateFileAndVerifyStatus("/ancestor/d1/d2", p, false, "r----x--x");

  // 3. verify it's parent should inherit grandpas' 0733.
  VerifyFileWithStatus("/ancestor/d1", true, "rwx-wx-wx");
}

TEST_F(NameSpaceTest, PermStickyBitFileProtect) {
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("supergroup");
  p.set_permission(01777);
  // 1. create ancestor, with 1777 permission.
  CreateFileAndVerifyStatus("/ancestor", p, true, "rwxrwxrwt");

  p.set_permission(0777);
  // 2. create child, its' permisson should be 0777.
  CreateFileAndVerifyStatus("/ancestor/f1", p, false, "rwxrwxrwx");

  // 3.a verify a other user can't delete content in the dir.
  {
    UserGroupInfo other_ugi("other", "other");
    auto s = ns_->Delete("/ancestor/f1", true, other_ugi);
    ASSERT_TRUE(s.HasException());
    ASSERT_EQ(s.ToString(),
              "com.bytedance.cloudfs.security.AccessControlException kError. "
              "Permission denied by sticky bit: user=other, "
              "path=\"/ancestor/f1\", "
              "parent=\"/ancestor\""
              );
  }

  // 3.b verify a other user can't delete the dir.
  {
    UserGroupInfo other_ugi("other", "other");
    auto s = ns_->Delete("/ancestor", true, other_ugi);
    ASSERT_TRUE(s.HasException());
    ASSERT_EQ(s.ToString(),
              "com.bytedance.cloudfs.security.AccessControlException kError. "
              "Permission denied: user = other, access= -w-, "
              "inode=\"/ancestor\""
              );
  }

  // 4. verify that owner can delete it.
  {
    UserGroupInfo other_ugi("root", "supergroup");
    auto s = ns_->Delete("/ancestor", true, other_ugi);
    ASSERT_TRUE(!s.HasException());
  }

  // 5. create dir and file again. (delete is async, change a dir for testing.)
  p.set_permission(01777);
  CreateFileAndVerifyStatus("/ancestor1", p, true, "rwxrwxrwt");
  p.set_permission(0777);
  CreateFileAndVerifyStatus("/ancestor1/f1", p, false, "rwxrwxrwx");

  // 6. remove sticky bit and remove by other user.
  {
    ASSERT_TRUE(!ns_->SetPermission("/", 0777).HasException());
    ASSERT_TRUE(!ns_->SetPermission("/", 0757).HasException());
    ASSERT_TRUE(!ns_->SetPermission("/ancestor1", 0777).HasException());
    UserGroupInfo other_ugi("other", "other");
    auto s = ns_->Delete("/ancestor1", true, other_ugi);
    LOG(INFO) << s.ToString();
    ASSERT_FALSE(s.HasException());
  }
}

TEST_F(NameSpaceTest, ReadPolicy) {
  std::string path = "/a/b/c/d";
  auto p = MakePermission();
  ASSERT_TRUE(!ns_->MkDirs(path, p, true).HasException());

  std::vector<cnetpp::base::StringPiece> path_components;
  ASSERT_TRUE(SplitPath(path, &path_components));
  INode inode;
  INode parent;
  // miss cache
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode, nullptr, &parent),
            StatusCode::kOK);
  INode tmp_inode;
  INode tmp_parent;
  for (int i = 0; i < 3; i++) {
    tmp_inode.Clear();
    tmp_parent.Clear();
    // hit cache
    ASSERT_EQ(ns_->GetLastINodeInPath(
                  path_components, &tmp_inode, nullptr, &tmp_parent),
              StatusCode::kOK);
    ASSERT_TRUE(INodeCompare(inode, tmp_inode));
    ASSERT_TRUE(INodeCompare(parent, tmp_parent));
  }

  auto handler = std::make_unique<DancennAdminHandler>(
      nullptr, std::static_pointer_cast<NameSpace>(ns_), nullptr, nullptr);
  // set
  {
    cnetpp::http::HttpRequest request;
    auto url =
        "/admin?cmd=read_policy&action=set&path=/a/b/c/"
        "d&localdconly=1&blacklistdc=LF,HL";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
  }
  {
    cnetpp::http::HttpRequest request;
    auto url =
        "/admin?cmd=read_policy&action=set&path=/a/b/"
        "&localdconly=0&blacklistdc=LF,LQ";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
  }
  // get
  {
    auto response = std::make_unique<GetReadPolicyResponseProto>();
    ASSERT_TRUE(!ns_->GetReadPolicy("/a/b/c/d", response.get()).HasException());
    const auto& policy = response->policy();
    ASSERT_EQ(true, policy.has_localdconly());
    ASSERT_EQ(true, policy.localdconly());
    ASSERT_EQ(2, policy.blacklistdc_size());
    ASSERT_EQ("LF", policy.blacklistdc(0));
    ASSERT_EQ("HL", policy.blacklistdc(1));
  }
  {
    auto response = std::make_unique<GetReadPolicyResponseProto>();
    ASSERT_TRUE(!ns_->GetReadPolicy("/a/b", response.get()).HasException());
    const auto& policy = response->policy();
    ASSERT_TRUE(policy.has_localdconly());
    ASSERT_FALSE(policy.localdconly());
    ASSERT_EQ(2, policy.blacklistdc_size());
    ASSERT_EQ("LF", policy.blacklistdc(0));
    ASSERT_EQ("LQ", policy.blacklistdc(1));
  }
  {
    auto response = std::make_unique<GetReadPolicyResponseProto>();
    ASSERT_TRUE(!ns_->GetReadPolicy("/a", response.get()).HasException());
    ASSERT_EQ(kDefaultReadPolicy.ShortDebugString(),
              response->policy().ShortDebugString());
  }
  {
    auto response = std::make_unique<GetReadPolicyResponseProto>();
    ASSERT_TRUE(!ns_->GetReadPolicy("/", response.get()).HasException());
    ASSERT_EQ(kDefaultReadPolicy.ShortDebugString(),
              response->policy().ShortDebugString());
  }
  // delete
  {
    cnetpp::http::HttpRequest request;
    auto url = "/admin?cmd=read_policy&action=remove&path=/a/b/c/d";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
  }
  // get
  {
    auto response = std::make_unique<GetReadPolicyResponseProto>();
    ASSERT_TRUE(!ns_->GetReadPolicy("/a/b/c/d", response.get()).HasException());
    const auto& policy = response->policy();
    ASSERT_TRUE(policy.has_localdconly());
    ASSERT_FALSE(policy.localdconly());
    ASSERT_EQ(2, policy.blacklistdc_size());
    ASSERT_EQ("LF", policy.blacklistdc(0));
    ASSERT_EQ("LQ", policy.blacklistdc(1));
  }
}

}  // anonymous namespace

TEST_F(NameSpaceTest, WriteLockPath) {
  std::vector<std::string> paths;
  paths.emplace_back("/aaa");
  paths.emplace_back("/bb");
  paths.emplace_back("/aaa/bbb");
  paths.emplace_back("/bb/aaa");
  paths.emplace_back("/bbb/ccc");
  paths.emplace_back("/ccc/abc");
  paths.emplace_back("/ccc/def");

  cnetpp::concurrency::ThreadPool tp("test");
  tp.set_num_threads(10);
  tp.Start();

  for (int t = 0; t < 10; ++t) {
    tp.AddTask([this, &paths]() -> bool {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<> dis(0, paths.size() - 1);
      std::uniform_int_distribution<> dis2(0, 10);
      for (int i = 0; i < 100; ++i) {
        int sidx = dis(gen);
        int didx = dis(gen);
        std::vector<cnetpp::base::StringPiece> path1, path2, path3;
        std::vector<cnetpp::base::StringPiece> lock1, lock2, lock3;

        SplitPath(paths[sidx], &path1);
        GetAllAncestorPaths(paths[sidx], &lock1);
        SplitPath(paths[didx], &path2);
        GetAllAncestorPaths(paths[didx], &lock2);
        SplitPath(paths[didx], &path3);
        GetAllAncestorPaths(paths[didx], &lock3);

        std::map<cnetpp::base::StringPiece,
                 std::unique_ptr<RWLockManager::LockHolder>>
            lock1_holder, lock2_holder, lock3_holder;
        ns_->WriteLockThreePaths(&lock1,
                                 &lock2,
                                 &lock3,
                                 &lock1_holder,
                                 &lock2_holder,
                                 &lock3_holder);
        std::this_thread::sleep_for(std::chrono::milliseconds(dis2(gen)));
      }
      return true;
    });
  }
  tp.Stop(true);
}

// FRIEND_TEST(NameSpaceTest, GetBlocksInRange) is declared in namespace.h
// Which requires the same namespace (no anonymous namespace)
TEST_F(NameSpaceTest, GetBlocksInRange) {
  ns_->StopLeaseMonitor();
  gflags::SetCommandLineOption("namespace_read_full_detail_blocks", "false");
  ASSERT_FALSE(FLAGS_namespace_read_full_detail_blocks);
  cnetpp::base::IPAddress client_ip("***********");
  auto create_request = MakeCreateRequest();
  auto add_request = MakeAddBlockRequest();
  auto p = MakePermission();
  CreateResponseProto create_response;
  AddBlockResponseProto add_response;

  std::string path2("/test2");
  ASSERT_TRUE(!ns_->CreateFile(path2, p, create_request, &create_response)
                   .HasException());
  ASSERT_EQ(ns_->last_inode_id(), kLastReservedINodeId + 1);
  add_request.clear_previous();

  for (int i = 0; i < 3; ++i) {
    ASSERT_TRUE(
        !ns_->AddBlock(
                path2, client_ip, default_rpc_info, add_request, &add_response)
             .HasException());
    add_request.mutable_previous()->CopyFrom(add_response.block().b());
    add_request.mutable_previous()->set_numbytes(100);
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               100,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);
    ns_->GetBlockReportManager()->IncrementalBlockReport(
        1, "datanode1", report);
  }
  CompleteRequestProto complete_request;
  auto last_block_id = add_response.block().b().blockid();
  complete_request.Clear();
  complete_request.set_src(path2);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(100);
  ASSERT_TRUE(!ns_->CompleteFile(path2, complete_request).HasException());

  INode inode;
  ASSERT_TRUE(ns_->GetINode(kLastReservedINodeId + 1, &inode));
  ASSERT_EQ(inode.blocks_size(), 3);

  std::vector<DetailedBlock> detail_blocks;
  uint64_t begin_offset = 0;

  auto res = ns_->GetDetailedBlocksByRangeInternal(
      inode, false, &detail_blocks, 0, 300);
  begin_offset = res.first;
  ASSERT_EQ(begin_offset, 0);
  ASSERT_EQ(detail_blocks.size(), 3);
  auto range_pair =
      ns_->GetBlocksInRange(inode, 0, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 0);
  ASSERT_EQ(range_pair.second.size(), 3);
  range_pair =
      ns_->GetBlocksInRange(inode, 100, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 100);
  ASSERT_EQ(range_pair.second.size(), 2);
  range_pair =
      ns_->GetBlocksInRange(inode, 101, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 100);
  ASSERT_EQ(range_pair.second.size(), 2);
  range_pair =
      ns_->GetBlocksInRange(inode, 201, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 200);
  ASSERT_EQ(range_pair.second.size(), 1);
  range_pair =
      ns_->GetBlocksInRange(inode, 299, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 200);
  ASSERT_EQ(range_pair.second.size(), 1);
  range_pair =
      ns_->GetBlocksInRange(inode, 300, 512L * 1024 * 1024, &detail_blocks);
  ASSERT_EQ(range_pair.first, 300);
  ASSERT_EQ(range_pair.second.size(), 0);
  detail_blocks.clear();
}

TEST_F(NameSpaceTest, RecycleBinDeleteFile) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;
  FLAGS_recycle_bin_scanner_interval_sec = 1;
  FLAGS_recycle_bin_retention_day = 0;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id = create_response.fs().fileid();

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_file",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), "/recycle_bin_test/test_file");
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);
  ASSERT_EQ(old_file_status.fileid(), inode_id);

  // Delete File
  ASSERT_TRUE(ns_->Delete("/recycle_bin_test/test_file", true).IsOK());

  // Check RecycleBin File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_file",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_FALSE(response.has_fs());

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  // GetListing RecycleBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_ + "/recycle_bin_test/test_file");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status =
      ns_->GetListing(recycle_datebin_path_ + "/recycle_bin_test/test_file",
                      NetworkLocationInfo(),
                      get_request,
                      &get_response,
                      ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  auto new_file_status = get_response.dirlist().partiallisting(0);
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 2);
  ASSERT_EQ(new_file_status.length(), 180);
  ASSERT_EQ(new_file_status.fileid(), inode_id);

  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  LOG(INFO) << "inode=" << inode.ShortDebugString();
}

TEST_F(NameSpaceTest, RecycleBinDeleteDir) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;
  FLAGS_recycle_bin_scanner_interval_sec = 3;
  FLAGS_recycle_bin_retention_day = 0;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_dir/test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id = create_response.fs().fileid() - 1;

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_dir/not_test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/test_dir/not_test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_dir/test_file",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), "/recycle_bin_test/test_dir/test_file");
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);

  response.Clear();
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_dir",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_dir_status = response.fs();
  ASSERT_EQ(old_dir_status.filetype(),
            HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_DIR);
  ASSERT_EQ(old_dir_status.path(), "/recycle_bin_test/test_dir");
  ASSERT_EQ(old_dir_status.fileid(), inode_id);

  // Delete File
  ASSERT_TRUE(ns_->Delete("/recycle_bin_test/test_dir", true).IsOK());

  // Check RecycleBin File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_dir/test_file",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_FALSE(response.has_fs());

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  // GetListing RecycleBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_ + "/recycle_bin_test/test_dir");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status = ns_->GetListing(recycle_datebin_path_ + "/recycle_bin_test/test_dir",
                           NetworkLocationInfo(),
                           get_request,
                           &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  auto new_dir_status = get_response.dirlist().partiallisting(0);
  ASSERT_EQ(new_dir_status.filetype(),
            HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_DIR);
  ASSERT_EQ(new_dir_status.fileid(), inode_id);

  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  LOG(INFO) << "inode=" << inode.ShortDebugString();
}

TEST_F(NameSpaceTest, RecycleBinCreateOverwrite) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;
  FLAGS_recycle_bin_scanner_interval_sec = 3;
  FLAGS_recycle_bin_retention_day = 0;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id = create_response.fs().fileid();

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_file",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), "/recycle_bin_test/test_file");
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);
  ASSERT_EQ(old_file_status.fileid(), inode_id);

  // Delete File
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/test_file",
          185,
          1,
          true,
          &add_response,
          &create_response,
          true,
          ::cloudfs::CreateFlagProto::CREATE |
              ::cloudfs::CreateFlagProto::OVERWRITE);

  // Check RecycleBin File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_file",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_TRUE(response.has_fs());
  auto new_file_status = response.fs();
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 1);
  ASSERT_EQ(new_file_status.length(), 185);
  ASSERT_NE(new_file_status.fileid(), inode_id);

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  // GetListing RecycleBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_ + "/recycle_bin_test/test_file");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status =
      ns_->GetListing(recycle_datebin_path_ + "/recycle_bin_test/test_file",
                      NetworkLocationInfo(),
                      get_request,
                      &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  new_file_status = get_response.dirlist().partiallisting(0);
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 2);
  ASSERT_EQ(new_file_status.length(), 180);
  ASSERT_EQ(new_file_status.fileid(), inode_id);

  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  LOG(INFO) << "inode=" << inode.ShortDebugString();
}

TEST_F(NameSpaceTest, RecycleBinRename2Overwrite) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;
  FLAGS_recycle_bin_scanner_interval_sec = 3;
  FLAGS_recycle_bin_retention_day = 0;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_dir/test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id = create_response.fs().fileid();

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_dir/not_test_file",
          185,
          1,
          true,
          &add_response,
          &create_response,
          true);

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_dir/test_file",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), "/recycle_bin_test/test_dir/test_file");
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);
  ASSERT_EQ(old_file_status.fileid(), inode_id);

  // Delete File
  add_response.Clear();
  create_response.Clear();
  ASSERT_TRUE(ns_->RenameTo2("/recycle_bin_test/not_test_dir",
                             "/recycle_bin_test/test_dir",
                             true)
                  .HasException());
  add_response.Clear();
  create_response.Clear();
  ASSERT_TRUE(ns_->RenameTo2("/recycle_bin_test/not_test_dir/not_test_file",
                             "/recycle_bin_test/test_dir",
                             true)
                  .HasException());
  add_response.Clear();
  create_response.Clear();
  ASSERT_TRUE(ns_->RenameTo2("/recycle_bin_test/not_test_dir",
                             "/recycle_bin_test/test_dir/test_file",
                             true)
                  .HasException());
  add_response.Clear();
  create_response.Clear();
  ASSERT_FALSE(ns_->RenameTo2("/recycle_bin_test/not_test_dir/not_test_file",
                              "/recycle_bin_test/test_dir/test_file",
                              true)
                   .HasException());

  // Check RecycleBin File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_dir/test_file",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_TRUE(response.has_fs());
  auto new_file_status = response.fs();
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 1);
  ASSERT_EQ(new_file_status.length(), 185);
  ASSERT_NE(new_file_status.fileid(), inode_id);

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  // GetListing RecycleBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_ + "/recycle_bin_test/test_dir/test_file");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status = ns_->GetListing(
      recycle_datebin_path_ + "/recycle_bin_test/test_dir/test_file",
      NetworkLocationInfo(),
      get_request,
      &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  new_file_status = get_response.dirlist().partiallisting(0);
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 2);
  ASSERT_EQ(new_file_status.length(), 180);
  ASSERT_EQ(new_file_status.fileid(), inode_id);

  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  LOG(INFO) << "inode=" << inode.ShortDebugString();
}

// deprecated in Trash 2.1
#if 0
TEST_F(NameSpaceTest, RecycleBinDefaultPolicy) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_default_policy_enable = true;
  FLAGS_recycle_bin_default_policy_time_sec = 17;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id = create_response.fs().fileid();

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_file",
          185,
          1,
          true,
          &add_response,
          &create_response,
          true);

  // Not Need Set RecycleBin Policy

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_file",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), test_dir);
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);
  ASSERT_EQ(old_file_status.fileid(), inode_id);

  // Delete File
  add_response.Clear();
  create_response.Clear();
  ASSERT_FALSE(ns_->RenameTo2("/recycle_bin_test/not_test_file",
                              "/recycle_bin_test/test_file",
                              true)
                   .HasException());

  // Check RecycleBin File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_file",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_TRUE(response.has_fs());
  auto new_file_status = response.fs();
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 1);
  ASSERT_EQ(new_file_status.length(), 185);
  ASSERT_NE(new_file_status.fileid(), inode_id);

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  // GetListing RecycleBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_userbin_path_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status =
      ns_->GetListing(recycle_userbin_path_, get_request, &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  new_file_status = get_response.dirlist().partiallisting(0);
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 2);
  ASSERT_EQ(new_file_status.length(), 180);
  ASSERT_EQ(new_file_status.fileid(), inode_id);

  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));
  LOG(INFO) << "inode=" << inode.ShortDebugString();

  RecyclePolicy new_policy;
  ASSERT_TRUE(
      XAttrs::GetProtoBufXAttr(inode, kRecyclePolicyXAttr, &new_policy));
  ASSERT_EQ(FLAGS_recycle_bin_default_policy_time_sec,
            new_policy.recycletime());
  ASSERT_EQ(new_policy.recycletime(),
            new_policy.cleanuptimestamp() - new_policy.movetotimestamp());
  ASSERT_EQ(RecyclePolicy::RENAME2_OVERWRITE, new_policy.reason());
}
#endif

TEST_F(NameSpaceTest, RecycleBinDuplicateNameAndRecycle) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;
  FLAGS_recycle_bin_scanner_interval_sec = 3;
  FLAGS_recycle_bin_retention_day = 0;

  // Create File to delete
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile("/recycle_bin_test/test_file1",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id_1 = create_response.fs().fileid();

  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/test_file2",
          185,
          1,
          true,
          &add_response,
          &create_response,
          true);
  auto inode_id_2 = create_response.fs().fileid();

  // Create other file
  add_response.Clear();
  create_response.Clear();
  AddFile("/recycle_bin_test/not_test_file",
          180,
          2,
          true,
          &add_response,
          &create_response,
          true);

  // Check Old File
  GetFileInfoResponseProto response;
  ASSERT_FALSE(ns_->GetFileInfo("/recycle_bin_test/test_file1",
                                NetworkLocationInfo(),
                                false,
                                false,
                                &response,
                                ugi_)
                   .HasException());
  auto old_file_status = response.fs();
  ASSERT_EQ(
      old_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(old_file_status.path(), "/recycle_bin_test/test_file1");
  ASSERT_EQ(old_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(old_file_status.block_replication(), 2);
  ASSERT_EQ(old_file_status.length(), 180);
  ASSERT_EQ(old_file_status.fileid(), inode_id_1);

  // Delete File By Rename
  add_response.Clear();
  create_response.Clear();
  ASSERT_FALSE(ns_->RenameTo2("/recycle_bin_test/test_file2",
                              "/recycle_bin_test/test_file1",
                              true)
                   .HasException());

  // Check Old File
  response.Clear();
  auto status = ns_->GetFileInfo("/recycle_bin_test/test_file1",
                                 NetworkLocationInfo(),
                                 false,
                                 false,
                                 &response,
                                 ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_TRUE(response.has_fs());
  auto new_file_status = response.fs();
  ASSERT_EQ(
      new_file_status.filetype(),
      HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
  ASSERT_EQ(new_file_status.blocksize(), 128 * 1024 * 1024);
  ASSERT_EQ(new_file_status.block_replication(), 1);
  ASSERT_EQ(new_file_status.length(), 185);
  ASSERT_EQ(new_file_status.fileid(), inode_id_2);

  // Delete File
  ASSERT_TRUE(ns_->Delete("/recycle_bin_test/test_file1", true).IsOK());

  // Check Old File
  response.Clear();
  status = ns_->GetFileInfo("/recycle_bin_test/test_file1",
                            NetworkLocationInfo(),
                            false,
                            false,
                            &response,
                            ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_FALSE(response.has_fs());

  // GetListing also found RecycleBin
  GetListingRequestProto get_request;
  get_request.set_src(recycle_bin_dir_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  status = ns_->GetListing(recycle_bin_dir_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response,
                           ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_EQ(get_response.dirlist().partiallisting_size(), 2);

  // GetListing UserBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_userbin_path_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status = ns_->GetListing(recycle_userbin_path_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  // GetListing DateBin
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_);
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status = ns_->GetListing(recycle_datebin_path_,
                           NetworkLocationInfo(),
                           get_request,
                           &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 1);

  // GetListing dir of deleted files
  get_request.Clear();
  get_response.Clear();
  get_request.set_src(recycle_datebin_path_ + "/recycle_bin_test/test_file1");
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  status =
      ns_->GetListing(recycle_datebin_path_ + "/recycle_bin_test/test_file1",
                      NetworkLocationInfo(),
                      get_request,
                      &get_response, ugi_);
  ASSERT_FALSE(status.HasException()) << status.ToString();

  for (const auto& file_status : get_response.dirlist().partiallisting()) {
    LOG(INFO) << "path:" << file_status.path()
              << ", type:" << file_status.filetype();
  }
  ASSERT_EQ(get_response.dirlist().remainingentries(), 0);
  ASSERT_GE(get_response.dirlist().partiallisting_size(), 2);

  for (int i = 0; i < 2; i++) {
    auto fst = get_response.dirlist().partiallisting(i);
    ASSERT_TRUE(fst.fileid() == inode_id_1 || fst.fileid() == inode_id_2);
    if (fst.fileid() == inode_id_1) {
      ASSERT_EQ(fst.filetype(),
                HdfsFileStatusProto::FileType::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(fst.blocksize(), 128 * 1024 * 1024);
      ASSERT_EQ(fst.block_replication(), 2);
      ASSERT_EQ(fst.length(), 180);
    } else {
      ASSERT_EQ(fst.blocksize(), 128 * 1024 * 1024);
      ASSERT_EQ(fst.block_replication(), 1);
      ASSERT_EQ(fst.length(), 185);
    }

    INode inode;
    ASSERT_TRUE(ns_->GetINode(fst.fileid(), &inode));
    LOG(INFO) << "inode=" << inode.ShortDebugString();
  }

  // Do Recycle
  std::string outdated_datebin_path = recycle_userbin_path_ + "/2023-12-06";
  ASSERT_TRUE(!ns_->RenameTo2(recycle_datebin_path_,
                              outdated_datebin_path,
                              false)
      .HasException());
  FLAGS_recycle_bin_scanner_enable = true;
  std::this_thread::sleep_for(std::chrono::seconds(
      FLAGS_recycle_bin_scanner_interval_sec * 2));

  // RecycleBin becomes empty and deleted
  response.Clear();
  status = ns_->GetFileInfo(
      recycle_bin_path_, NetworkLocationInfo(), false, false, &response, ugi_);
  ASSERT_TRUE(status.IsOK());
  ASSERT_FALSE(response.has_fs());
}

TEST_F(NameSpaceTest, CreateOverwrite) {
  auto path = "/test/test_overwrite";

  // First Create by Anonymously
  auto create_request = MakeCreateRequest();
  create_request.set_replication(2);
  create_request.set_createparent(true);
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE |
                                ::cloudfs::CreateFlagProto::OVERWRITE);
  PermissionStatus p;

  CreateResponseProto create_response;
  auto status = ns_->CreateFile(path, p, create_request, &create_response);
  LOG(INFO) << "status=" << status.ToString();
  ASSERT_TRUE(status.IsOK());

  // Overwrite Success
  LogRpcInfo rpc_log_info("test", 1);

  status =
      ns_->CreateFile(path, p, create_request, &create_response, rpc_log_info);
  LOG(INFO) << "status=" << status.ToString();
  LOG(INFO) << "create_response=" << create_response.ShortDebugString();
  ASSERT_TRUE(status.IsOK());
  ASSERT_EQ(0, status.message().size());
  ASSERT_TRUE(create_response.has_fs());

  // Overwrite Failed
  status =
      ns_->CreateFile(path, p, create_request, &create_response, rpc_log_info);
  LOG(INFO) << "status=" << status.ToString();
  LOG(INFO) << "create_response=" << create_response.ShortDebugString();
  ASSERT_TRUE(status.IsOK());
  ASSERT_NE(0, status.message().size());
  ASSERT_TRUE(create_response.has_fs());
}

TEST_F(NameSpaceTest, CreateAndDeleteOldFile) {
  auto recycle_bin_enable = FLAGS_recycle_bin_enable;
  DEFER([&]() { FLAGS_recycle_bin_enable = recycle_bin_enable; });

  CreateResponseProto create_response_1;
  AddBlockResponseProto add_response_1;
  AddFile("/test", 100, 3, true, &add_response_1, &create_response_1);
  EXPECT_TRUE(create_response_1.has_fs());

  PermissionStatus p;
  auto create_request = MakeCreateRequest();
  create_request.set_replication(2);
  create_request.set_createparent(true);
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE |
                                ::cloudfs::CreateFlagProto::OVERWRITE);
  CreateResponseProto create_response_2;
  Status status =
      ns_->CreateFile("/test", p, create_request, &create_response_2);
  EXPECT_TRUE(status.IsOK());
  EXPECT_GT(create_response_2.fs().fileid(), create_response_1.fs().fileid());

  BlockInfoProto bip_1;
  EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response_1.block().b().blockid(), &bip_1));
  EXPECT_EQ(bip_1.block_id(), add_response_1.block().b().blockid());
  EXPECT_EQ(bip_1.gen_stamp(), add_response_1.block().b().generationstamp());
  EXPECT_EQ(bip_1.num_bytes(), 100);
  EXPECT_EQ(bip_1.inode_id(), create_response_1.fs().fileid());
  EXPECT_EQ(bip_1.expected_rep(), 3);
  EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response_1.block().b().blockid(), &bip_1));
}

TEST_F(NameSpaceTest, RenameAndDeleteOldFile) {
  auto recycle_bin_enable = FLAGS_recycle_bin_enable;
  DEFER([&]() { FLAGS_recycle_bin_enable = recycle_bin_enable; });

  CreateResponseProto create_response_1;
  AddBlockResponseProto add_response_1;
  AddFile("/test", 100, 3, true, &add_response_1, &create_response_1);
  EXPECT_TRUE(create_response_1.has_fs());

  CreateResponseProto create_response_2;
  AddBlockResponseProto add_response_2;
  AddFile("/test2", 200, 1, true, &add_response_2, &create_response_2);

  EXPECT_TRUE(ns_->RenameTo2("/test2", "/test", true).IsOK());
  BlockInfoProto bip_1;
  EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response_1.block().b().blockid(), &bip_1));
  EXPECT_EQ(bip_1.block_id(), add_response_1.block().b().blockid());
  EXPECT_EQ(bip_1.gen_stamp(), add_response_1.block().b().generationstamp());
  EXPECT_EQ(bip_1.num_bytes(), 100);
  EXPECT_EQ(bip_1.inode_id(), create_response_1.fs().fileid());
  EXPECT_EQ(bip_1.expected_rep(), 3);
  BlockInfoProto bip_2;
  EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response_2.block().b().blockid(), &bip_2));
  EXPECT_EQ(bip_2.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip_2.block_id(), add_response_2.block().b().blockid());
  EXPECT_EQ(bip_2.gen_stamp(), add_response_2.block().b().generationstamp());
  EXPECT_EQ(bip_2.num_bytes(), 200);
  EXPECT_EQ(bip_2.inode_id(), create_response_2.fs().fileid());
  EXPECT_EQ(bip_2.expected_rep(), 1);
}

TEST_F(NameSpaceTest, DeleteFileDirectly) {
  auto recycle_bin_enable = FLAGS_recycle_bin_enable;
  DEFER([&]() { FLAGS_recycle_bin_enable = recycle_bin_enable; });

  CreateResponseProto create_response;
  AddBlockResponseProto add_response;
  AddFile("/test", 100, 3, true, &add_response, &create_response);
  EXPECT_TRUE(create_response.has_fs());

  EXPECT_TRUE(ns_->Delete("/test", false).IsOK());
  BlockInfoProto bip;
  EXPECT_TRUE(ns_->meta_storage()->GetBlockInfo(
      add_response.block().b().blockid(), &bip));
  EXPECT_EQ(bip.block_id(), add_response.block().b().blockid());
  EXPECT_EQ(bip.gen_stamp(), add_response.block().b().generationstamp());
  EXPECT_EQ(bip.num_bytes(), 100);
  EXPECT_EQ(bip.inode_id(), create_response.fs().fileid());
  EXPECT_EQ(bip.expected_rep(), 3);
}

TEST_F(NameSpaceTest, RecycleBinDeletePerf) {
  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = true;
  FLAGS_recycle_bin_scanner_interval_sec = 30;

  std::string base_dir = "/delete_perf";
  std::string file_path = base_dir + "/test_file_";
  int nthread = 4;
  int nfile = 1000;

  auto perm = MakePermission();
  auto create_request = MakeCreateRequest();
  create_request.set_createparent(true);
  create_request.set_replication(1);

  // Create multiple files
  cnetpp::concurrency::ThreadPool tp("test_recycle_bin");
  tp.set_num_threads(nthread);
  tp.Start();
  for (int fid = 0; fid < nfile; fid++) {
    tp.AddTask([this, file_path, fid, perm, create_request]() -> bool {
      CreateResponseProto create_response;
      auto st = ns_->CreateFile(file_path + std::to_string(fid),
                                perm,
                                create_request,
                                &create_response);
      return true;
    });
  }
  tp.Stop(true);

  // Check file created
  GetListingRequestProto get_request;
  get_request.set_src(base_dir.c_str());
  get_request.set_needlocation(false);
  get_request.set_startafter("");
  GetListingResponseProto get_response;
  auto status = ns_->GetListing(base_dir.c_str(),
                                NetworkLocationInfo(),
                                get_request,
                                &get_response,
                                ugi_);
  ASSERT_TRUE(!status.HasException());
  ASSERT_GT(get_response.dirlist().partiallisting().size(), 100);

  // Delete all files concurrently
  tp.Start();
  auto begin = std::chrono::high_resolution_clock::now();
  for (int fid = 0; fid < nfile; fid++) {
    tp.AddTask([this, file_path, fid]() -> bool {
      auto st = ns_->Delete(file_path + std::to_string(fid), false);
      return true;
    });
  }
  tp.Stop(true);
  auto end = std::chrono::high_resolution_clock::now();
  LOG(INFO) << "Deleted " << nfile << " files in "
            << std::chrono::duration_cast<std::chrono::microseconds>(end - begin).count()
            << " ms";

  // Cleanup
  ASSERT_TRUE(ns_->Delete(base_dir, true).IsOK());
}

TEST_F(NameSpaceTest, SubTxnFail) {
  // Test all procedures that trigger sub transaction but fail:
  // 1. Delete()
  //  - preparing user's recycle bin directory
  //
  // 2. MkDirs()
  //  - preparing common recycle bin directory
  //
  // 3. CreateFile()
  //  - preparing parent directories
  //  - preparing user's recycle bin directory when removing overwritten file
  //
  // 4. CreateSymlink()
  //  - prepare parent directories
  //
  // 5. RenameTo2()
  //  - preparing user's recycle bin directory

  ns_->StopBGDeletionWorker();
  FLAGS_recycle_bin_enable = true;
  std::string rbin_path = kSeparator + kRecycleBinDirNameString;
  std::string base_path = "/test_sub_txn";
  std::string parent_path = base_path + "/parent";
  DEFER([&]() { ns_->Delete(base_path, true); });
  DEFER([&]() { ns_->Delete(rbin_path, true); });

  MkdirsWrp(base_path, ugi_.current_user(), ugi_.current_group(), false);
  PermissionStatus ps;
  ps.set_username(ugi_.current_user());
  ps.set_groupname("");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());

  // create file with recycle bin name and parent directory, to make sub txn fail.
  CreateFileWrp(rbin_path, ugi_.current_user(), ugi_.current_group(), false);
  CreateFileWrp(parent_path, ugi_.current_user(), ugi_.current_group(), false);

  // 1. Delete()
  std::string delete_path = base_path + "/delete_file";
  CreateFileWrp(delete_path, ugi_.current_user(), ugi_.current_group(), false);
  EXPECT_FALSE(ns_->Delete(delete_path, true).IsOK());

  // 2. MkDirs()
  std::string mkdir_path = rbin_path + kSeparator + ugi_.current_user() + "/dir_in_rbin";
  EXPECT_FALSE(ns_->MkDirs(mkdir_path, ps, true).IsOK());

  // 3. CreateFile()
  std::string create_path = parent_path + "/non_exist_parent/create_file";
  auto create_request = MakeCreateRequest();
  create_request.set_createparent(true);
  CreateResponseProto response;
  EXPECT_FALSE(ns_->CreateFile(create_path, ps, create_request, &response).IsOK());

  create_path = base_path + "/create_file";
  CreateFileWrp(create_path, ugi_.current_user(), ugi_.current_group(), false);
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE |
                                ::cloudfs::CreateFlagProto::OVERWRITE);
  EXPECT_FALSE(ns_->CreateFile(create_path, ps, create_request, &response).IsOK());

  // 4. CreateSymlink()
  std::string symlink_path = parent_path + "/non_exist_parent/symlink_file";
  EXPECT_FALSE(ns_->CreateSymlink(base_path, symlink_path, ps, true).IsOK());

  // 5. RenameTo2()
  std::string rename_src_path = base_path + "/rename_src_file";
  std::string rename_dst_path = base_path + "/rename_dst_file";
  CreateFileWrp(rename_src_path, ugi_.current_user(), ugi_.current_group(), false);
  CreateFileWrp(rename_dst_path, ugi_.current_user(), ugi_.current_group(), false);
  EXPECT_FALSE(ns_->RenameTo2(rename_src_path, rename_dst_path, true).IsOK());
}

TEST_F(NameSpaceTest, AsyncCreateFileTxnCases) {
  // 1. submit succeed, @rpc_done will be finished
  // 2. submit fail, check code for reason
  //   2.1 normal fail, @rpc_done is finished
  //   2.2 need to trigger sub-txn, @rpc_done is unfinished

  std::string base_path = "/test_async_create_file_txn";
  std::string parent_path = base_path + "/parent";
  std::string path = parent_path + "/create_file" ;
  MkdirsWrp(parent_path, ugi_.current_user(), ugi_.current_group(), true);
  DEFER([&]() { ns_->Delete(base_path, true); });

  PermissionStatus ps;
  ps.set_username(ugi_.current_user());
  ps.set_groupname("");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());
  LogRpcInfo rpc_info;

  // case 1
  auto req_1 = MakeCreateRequest();
  CreateResponseProto resp_1;
  bool done_finished = false;
  SynchronizedRpcClosure done_1;
  done_1.add_post_callback([&](const Status& s) {
    // XXX sleep in callback to make closure submitted but not finished
    sleep(3);
    EXPECT_TRUE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_1.post_callback().empty());
  bool submitted = ns_->AsyncCreateFileTxn(path,
                                           ps,
                                           NetworkLocationInfo(),
                                           ugi_,
                                           rpc_info,
                                           "",
                                           &req_1,
                                           &resp_1,
                                           nullptr,
                                           &done_1,
                                           true,
                                           nullptr,
                                           nullptr);
  EXPECT_TRUE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_FALSE(done_1.post_callback().empty());
  done_1.Await();
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_1.post_callback().empty());

  // case 2.1
  auto req_2 = MakeCreateRequest();
  CreateResponseProto resp_2;
  done_finished = false;
  SynchronizedRpcClosure done_2;
  done_2.add_post_callback([&](const Status& s) {
    EXPECT_FALSE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_2.post_callback().empty());
  submitted = ns_->AsyncCreateFileTxn(path,
                                      ps,
                                      NetworkLocationInfo(),
                                      ugi_,
                                      rpc_info,
                                      "",
                                      &req_2,
                                      &resp_2,
                                      nullptr,
                                      &done_2,
                                      true,
                                      nullptr,
                                      nullptr);
  done_2.Await();
  EXPECT_FALSE(submitted);
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_2.post_callback().empty());

  // case 2.2
  path = parent_path + "/non_exist_parent/create_file";
  auto req_3 = MakeCreateRequest();
  req_3.set_createparent(true);
  CreateResponseProto resp_3;
  done_finished = false;
  SynchronizedRpcClosure done_3;
  done_3.add_post_callback([&](const Status& s) {
    EXPECT_TRUE(false);
    done_finished = true;
  });
  ASSERT_FALSE(done_3.post_callback().empty());
  submitted = ns_->AsyncCreateFileTxn(path,
                                      ps,
                                      NetworkLocationInfo(),
                                      ugi_,
                                      rpc_info,
                                      "",
                                      &req_3,
                                      &resp_3,
                                      nullptr,
                                      &done_3,
                                      true,
                                      nullptr,
                                      nullptr);
  EXPECT_FALSE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_TRUE(done_3.post_callback().empty());
}

TEST_F(NameSpaceTest, AsyncDeleteTxnCases) {
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = true;
  FLAGS_recycle_bin_scanner_interval_sec = 1;
  FLAGS_recycle_bin_retention_day = 0;

  // 1. submit succeed, @rpc_done will be finished
  // 2. submit fail, check code for reason
  //   2.1 normal fail, @rpc_done is finished
  //   2.2 need to trigger sub-txn, @rpc_done is unfinished

  std::string base_path = "/test_async_delete_txn";
  std::string parent_path = base_path + "/parent";
  std::string path = parent_path + "/delete_file" ;
  MkdirsWrp(parent_path, ugi_.current_user(), ugi_.current_group(), true);
  DEFER([&]() { ns_->Delete(base_path, true); });

  LogRpcInfo rpc_info;

  // case 1
  FLAGS_recycle_bin_enable = false;
  CreateFileWrp(path, ugi_.current_user(), ugi_.current_group(), false);
  bool done_finished = false;
  SynchronizedRpcClosure done_1;
  done_1.add_post_callback([&](const Status& s) {
    // XXX sleep in callback to make closure submitted but not finished
    sleep(3);
    EXPECT_TRUE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_1.post_callback().empty());
  bool submitted = ns_->AsyncDeleteTxn(path, false, ugi_, rpc_info, &done_1);
  EXPECT_TRUE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_FALSE(done_1.post_callback().empty());
  done_1.Await();
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_1.post_callback().empty());

  // case 2.1
  FLAGS_recycle_bin_enable = false;
  done_finished = false;
  SynchronizedRpcClosure done_2;
  done_2.add_post_callback([&](const Status& s) {
    EXPECT_FALSE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_2.post_callback().empty());
  submitted = ns_->AsyncDeleteTxn("/", false, ugi_, rpc_info, &done_2);
  done_2.Await();
  EXPECT_FALSE(submitted);
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_2.post_callback().empty());

  // case 2.2
  FLAGS_recycle_bin_enable = true;
  CreateFileWrp(path, ugi_.current_user(), ugi_.current_group(), false);
  done_finished = false;
  SynchronizedRpcClosure done_3;
  done_3.add_post_callback([&](const Status& s) {
    EXPECT_TRUE(false);
    done_finished = true;
  });
  ASSERT_FALSE(done_3.post_callback().empty());
  submitted = ns_->AsyncDeleteTxn(path, false, ugi_, rpc_info, &done_3);
  EXPECT_FALSE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_TRUE(done_3.post_callback().empty());
}

TEST_F(NameSpaceTest, AsyncRenameTo2TxnCases) {
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = true;
  FLAGS_recycle_bin_scanner_interval_sec = 1;
  FLAGS_recycle_bin_retention_day = 0;

  // 1. submit succeed, @rpc_done will be finished
  // 2. submit fail, check code for reason
  //   2.1 normal fail, @rpc_done is finished
  //   2.2 need to trigger sub-txn, @rpc_done is unfinished

  std::string base_path = "/test_async_rename2_txn";
  std::string parent_path = base_path + "/parent";
  std::string src_path = parent_path + "/rename_src" ;
  std::string dst_path = parent_path + "/rename_dst" ;
  MkdirsWrp(parent_path, ugi_.current_user(), ugi_.current_group(), true);
  DEFER([&]() { ns_->Delete(base_path, true); });

  LogRpcInfo rpc_info;

  // case 1
  FLAGS_recycle_bin_enable = false;
  CreateFileWrp(src_path, ugi_.current_user(), ugi_.current_group(), false);
  bool done_finished = false;
  SynchronizedRpcClosure done_1;
  done_1.add_post_callback([&](const Status& s) {
    // XXX sleep in callback to make closure submitted but not finished
    sleep(2);
    EXPECT_TRUE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_1.post_callback().empty());
  bool submitted = ns_->AsyncRenameTo2Txn(
      src_path, dst_path, false, rpc_info, ugi_, &done_1);
  EXPECT_TRUE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_FALSE(done_1.post_callback().empty());
  done_1.Await();
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_1.post_callback().empty());
  ns_->Delete(dst_path, false);

  // case 2.1
  FLAGS_recycle_bin_enable = false;
  CreateFileWrp(src_path, ugi_.current_user(), ugi_.current_group(), false);
  CreateFileWrp(dst_path, ugi_.current_user(), ugi_.current_group(), false);
  done_finished = false;
  SynchronizedRpcClosure done_2;
  done_2.add_post_callback([&](const Status& s) {
    EXPECT_FALSE(s.IsOK());
    done_finished = true;
  });
  ASSERT_FALSE(done_2.post_callback().empty());
  submitted = ns_->AsyncRenameTo2Txn(
      src_path, dst_path, false, rpc_info, ugi_, &done_2);
  done_2.Await();
  EXPECT_FALSE(submitted);
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_2.post_callback().empty());
  ns_->Delete(src_path, false);
  ns_->Delete(dst_path, false);

  // case 2.2
  FLAGS_recycle_bin_enable = true;
  CreateFileWrp(src_path, ugi_.current_user(), ugi_.current_group(), false);
  CreateFileWrp(dst_path, ugi_.current_user(), ugi_.current_group(), false);
  done_finished = false;
  SynchronizedRpcClosure done_3;
  done_3.add_post_callback([&](const Status& s) {
    EXPECT_TRUE(false);
    done_finished = true;
  });
  ASSERT_FALSE(done_3.post_callback().empty());
  submitted = ns_->AsyncRenameTo2Txn(
      src_path, dst_path, true, rpc_info, ugi_, &done_3);
  EXPECT_FALSE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_TRUE(done_3.post_callback().empty());
  ns_->Delete(src_path, false);
  ns_->Delete(dst_path, false);
}

TEST_F(NameSpaceTest, AsyncMkDirsTxnCases) {
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = true;
  FLAGS_recycle_bin_scanner_interval_sec = 1;
  FLAGS_recycle_bin_retention_day = 0;

  // 1. submit succeed, @rpc_done will be finished
  // 2. submit fail, check code for reason
  //   2.1 normal fail, @rpc_done is finished
  //   2.2 need to trigger sub-txn, @rpc_done is unfinished

  std::string base_path = "/test_async_mkdirs_txn";
  std::string parent_path = base_path + "/parent";
  std::string path = parent_path + "/create_dir" ;
  MkdirsWrp(parent_path, ugi_.current_user(), ugi_.current_group(), true);
  DEFER([&]() { ns_->Delete(base_path, true); });

  PermissionStatus ps;
  ps.set_username(ugi_.current_user());
  ps.set_groupname("");
  ps.set_permission(FsPermission::GetDirDefault().ToShort());
  LogRpcInfo rpc_info;

  // case 1
  FLAGS_recycle_bin_enable = false;
  bool done_finished = false;
  SynchronizedRpcClosure done_1;
  done_1.add_post_callback([&](const Status& s) {
    sleep(3);
    EXPECT_TRUE(s.IsOK());
    done_finished = true;
  });
  ASSERT_EQ(done_1.post_callback().size(), 1);
  bool submitted = ns_->AsyncMkDirsTxn(
      path, ps, ugi_, true, true, nullptr, LogRpcInfo(), &done_1, nullptr, true);
  EXPECT_TRUE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_FALSE(done_1.post_callback().empty());
  done_1.Await();
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_1.post_callback().empty());
  ns_->Delete(path, true);

  // case 2.1
  done_finished = false;
  CreateFileWrp(path, ugi_.current_user(), ugi_.current_group(), false);
  done_finished = false;
  SynchronizedRpcClosure done_2;
  done_2.add_post_callback([&](const Status& s) {
    EXPECT_FALSE(s.IsOK());
    done_finished = true;
  });
  ASSERT_EQ(done_2.post_callback().size(), 1);
  submitted = ns_->AsyncMkDirsTxn(
      path, ps, ugi_, false, true, nullptr, LogRpcInfo(), &done_2, nullptr, true);
  done_2.Await();
  EXPECT_FALSE(submitted);
  EXPECT_TRUE(done_finished);
  EXPECT_TRUE(done_2.post_callback().empty());

  // case 2.2
  path = kSeparator + kRecycleBinDirNameString + kSeparator + ugi_.current_user();
  done_finished = false;
  SynchronizedRpcClosure done_3;
  done_3.add_post_callback([&](const Status& s) {
    EXPECT_TRUE(false);
    done_finished = true;
  });
  ASSERT_EQ(done_3.post_callback().size(), 1);
  submitted = ns_->AsyncMkDirsTxn(
      path, ps, ugi_, true, true, nullptr, LogRpcInfo(), &done_3, nullptr, true);
  EXPECT_FALSE(submitted);
  EXPECT_FALSE(done_finished);
  EXPECT_TRUE(done_3.post_callback().empty());
}

class MountPointTest : public NameSpaceTest {};

TEST_F(MountPointTest, CreateFile) {
  auto p = MakePermission();
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {"a"}, {}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  auto create_request = MakeCreateRequest();
  EXPECT_FALSE(create_request.createparent());
  CreateResponseProto create_response;
  EXPECT_TRUE(
      ns_->CreateFile("/a", p, create_request, &create_response).IsOK());
  auto status = ns_->CreateFile("/b", p, create_request, &create_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /b is denied by mount point of inode{id:16385}, "
            "please contact us");

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"d"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  EXPECT_TRUE(
      ns_->CreateFile("/c", p, create_request, &create_response).IsOK());
  status = ns_->CreateFile("/d", p, create_request, &create_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /d is denied by mount point of inode{id:16385}, "
            "please contact us");

  create_request.set_createparent(true);
  EXPECT_TRUE(
      ns_->CreateFile("/e/f", p, create_request, &create_response).IsOK());
  status = ns_->CreateFile("/d/g", p, create_request, &create_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /d is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, MkDir) {
  auto p = MakePermission();
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {"a"}, {}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  EXPECT_TRUE(ns_->MkDirs("/a", p, false).IsOK());
  auto status = ns_->MkDirs("/b", p, false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /b is denied by mount point of inode{id:16385}, "
            "please contact us");

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"d"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  EXPECT_TRUE(ns_->MkDirs("/c", p, false).IsOK());
  status = ns_->MkDirs("/d", p, false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /d is denied by mount point of inode{id:16385}, "
            "please contact us");

  EXPECT_TRUE(ns_->MkDirs("/e/f", p, true).IsOK());
  status = ns_->MkDirs("/d/g", p, true);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on d is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, Delete) {
  auto p = MakePermission();
  EXPECT_TRUE(ns_->MkDirs("/a/b/c", p, true).IsOK());
  EXPECT_TRUE(ns_->Delete("/a/b/c", false).IsOK());
  EXPECT_TRUE(ns_->Delete("/a", true).IsOK());

  EXPECT_TRUE(ns_->MkDirs("/d/e/f", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/g/h/i", p, true).IsOK());

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {"a"}, {}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  Status status = ns_->Delete("/d/e/f", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /d/e/f is denied by mount point of inode{id:16385}, "
            "please contact us");
  status = ns_->Delete("/d", true);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /d is denied by mount point of inode{id:16385}, "
            "please contact us");

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"g"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  status = ns_->Delete("/g/h/i", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /g/h/i is denied by mount point of inode{id:16385}, "
            "please contact us");
  status = ns_->Delete("/g", true);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /g is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, Rename) {
  auto p = MakePermission();
  EXPECT_TRUE(ns_->MkDirs("/a/b", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/a/c", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/d", p, true).IsOK());
  // Do not omit last inode of destination.
  EXPECT_TRUE(ns_->RenameTo("/a/b", "/d/b").IsOK());
  // Omit last inode of destination.
  EXPECT_TRUE(ns_->RenameTo("/a/c", "/d").IsOK());

  EXPECT_TRUE(ns_->MkDirs("/e/f", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/e/g", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/h", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/i/j", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/i/k", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/l", p, true).IsOK());

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"e"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  // Do not omit last inode of destination.
  Status status = ns_->RenameTo("/e/f", "/h/f");
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /e/f is denied by mount point of inode{id:16385}, "
            "please contact us");
  // Omit last inode of destination.
  status = ns_->RenameTo("/e/g", "/h");
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /e/g is denied by mount point of inode{id:16385}, "
            "please contact us");

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"l"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  // Do not omit last inode of destination.
  status = ns_->RenameTo("/i/j", "/l/j");
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /l/j is denied by mount point of inode{id:16385}, "
            "please contact us");
  // Omit last inode of destination.
  status = ns_->RenameTo("/i/k", "/l");
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /l is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, Rename2) {
  auto p = MakePermission();
  EXPECT_TRUE(ns_->MkDirs("/a/b", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/a/c", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/d", p, true).IsOK());
  // Do not omit last inode of destination.
  EXPECT_TRUE(ns_->RenameTo2("/a/b", "/d/b", false).IsOK());
  // Not supported: omit last inode of destination.
  Status status = ns_->RenameTo2("/a/c", "/d", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(),
            JavaExceptions::Exception::kFileAlreadyExistsException);
  EXPECT_EQ(status.message(), "Rename destination already exists: /d");

  EXPECT_TRUE(ns_->MkDirs("/e/f", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/e/g", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/h", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/i/j", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/i/k", p, true).IsOK());
  EXPECT_TRUE(ns_->MkDirs("/l", p, true).IsOK());

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"e"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  // Do not omit last inode of destination.
  status = ns_->RenameTo2("/e/f", "/h/f", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /e/f is denied by mount point of inode{id:16385}, "
            "please contact us");
  // Not supported: omit last inode of destination.
  status = ns_->RenameTo2("/e/g", "/h", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /e/g is denied by mount point of inode{id:16385}, "
            "please contact us");

  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"l"}, false, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }
  // Do not omit last inode of destination.
  status = ns_->RenameTo2("/i/j", "/l/j", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /l/j is denied by mount point of inode{id:16385}, "
            "please contact us");
  // Not supported: omit last inode of destination.
  status = ns_->RenameTo2("/i/k", "/l", false);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /l is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, Append) {
  auto p = MakePermission();
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  EXPECT_TRUE(
      ns_->CreateFile("/a", p, create_request, &create_response).IsOK());
  CompleteRequestProto complete_request;
  complete_request.set_src("/a");
  complete_request.set_clientname("client");
  EXPECT_TRUE(ns_->CompleteFile("/a", complete_request).IsOK());
  auto append_request = MakeAppendRequest("/a", "client");
  AppendResponseProto append_response;
  EXPECT_TRUE(
      ns_->Append(
             "/a", "client", default_rpc_info, append_request, &append_response)
          .IsOK());

  EXPECT_TRUE(
      ns_->CreateFile("/b", p, create_request, &create_response).IsOK());
  complete_request.set_src("/b");
  EXPECT_TRUE(ns_->CompleteFile("/b", complete_request).IsOK());
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"b"}, false, p, &done);
    done.Await();
  }
  Status status = ns_->Append(
      "/b", "client", default_rpc_info, append_request, &append_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /b is denied by mount point of inode{id:16385}, "
            "please contact us");

  create_request.set_createparent(true);
  EXPECT_TRUE(
      ns_->CreateFile("/c/d/e/f", p, create_request, &create_response).IsOK());
  complete_request.set_src("/c/d/e/f");
  EXPECT_TRUE(ns_->CompleteFile("/c/d/e/f", complete_request).IsOK());
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"c"}, false, p, &done);
    done.Await();
  }
  status = ns_->Append(
      "/c/d/e/f", "client", default_rpc_info, append_request, &append_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(
      status.message(),
      "Operation on /c/d/e/f is denied by mount point of inode{id:16385}, "
      "please contact us");
}

TEST_F(MountPointTest, AddBlock) {
  auto p = MakePermission();
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  EXPECT_TRUE(
      ns_->CreateFile("/a", p, create_request, &create_response).IsOK());
  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  EXPECT_TRUE(
      ns_->AddBlock(
             "/a", client_ip, default_rpc_info, add_request, &add_response)
          .IsOK());

  EXPECT_TRUE(
      ns_->CreateFile("/b", p, create_request, &create_response).IsOK());
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"b"}, false, p, &done);
    done.Await();
  }
  Status status = ns_->AddBlock(
      "/b", client_ip, default_rpc_info, add_request, &add_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /b is denied by mount point of inode{id:16385}, "
            "please contact us");

  create_request.set_createparent(true);
  EXPECT_TRUE(
      ns_->CreateFile("/c/d", p, create_request, &create_response).IsOK());
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr("/", {}, {"c"}, false, p, &done);
    done.Await();
  }
  status = ns_->AddBlock(
      "/c/d", client_ip, default_rpc_info, add_request, &add_response);
  EXPECT_TRUE(status.HasException());
  EXPECT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  EXPECT_EQ(status.message(),
            "Operation on /c/d is denied by mount point of inode{id:16385}, "
            "please contact us");
}

TEST_F(MountPointTest, SetMountPoint) {
  auto p = MakePermission();

  // Set mount point for existing file will fail
  {
    std::string file_existing = "/user/file_existing";
    auto create_request = MakeCreateRequest();
    create_request.set_createparent(true);
    CreateResponseProto create_response;
    ASSERT_TRUE(ns_->CreateFile(file_existing, p, create_request, &create_response).IsOK());

    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr(file_existing, {}, {"*"}, true, p, &done);
    done.Await();
    EXPECT_FALSE(done.status().IsOK());
  }

  // Set mount point for existing non-empty dir will fail
  {
    std::string dir_existing_non_empty = "/user/dir_existing_non_empty";
    std::string file_in_dir = dir_existing_non_empty + "/file";
    auto create_request = MakeCreateRequest();
    create_request.set_createparent(true);
    CreateResponseProto create_response;
    ASSERT_TRUE(ns_->CreateFile(file_in_dir, p, create_request, &create_response).IsOK());

    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr(dir_existing_non_empty, {}, {"*"}, true, p, &done);
    done.Await();
    EXPECT_FALSE(done.status().IsOK());
  }

  // Set mount point for existing empty dir will succeed
  {
    std::string dir_existing_empty = "/user/dir_existing_empty";
    ASSERT_TRUE(ns_->MkDirs(dir_existing_empty, p, true).IsOK());

    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr(dir_existing_empty, {}, {"*"}, true, p, &done);
    done.Await();
    EXPECT_TRUE(done.status().IsOK());
  }

  // Set mount point for non-existing dir will fail
  {
    std::string dir_non_existing = "/user/dir_non_existing";

    SynchronizedRpcClosure done;
    ns_->AsyncSetMountPointAttr(dir_non_existing, {}, {"*"}, true, p, &done);
    done.Await();
    EXPECT_FALSE(done.status().IsOK());
  }
}

SetLifecyclePolicyRequestProto MakeSetLifecyclePolicy(const std::string& src,
                                                      const cloudfs::StorageClassProto storageClass) {
    SetLifecyclePolicyRequestProto req;

    LifecyclePolicyProto policy;
    policy.set_defaultclass(storageClass);
    auto rules = policy.mutable_transrules();

    cloudfs::TransitionRuleProto trule;
    trule.set_days(0L);
    trule.set_targetclass(storageClass);
    policy.add_transrules()->CopyFrom(trule);

    req.set_path(src);
    req.mutable_lifecyclepolicy()->CopyFrom(policy);

    return req;
}

TEST_F(NameSpaceTest, DecideIoMode) {
  PermissionStatus p_dir;
  p_dir.set_username("root");
  p_dir.set_groupname("normalgroup");
  p_dir.set_permission(0755);
  std::string dirPath = "/dir";
  ASSERT_TRUE(!ns_->MkDirs(dirPath, p_dir, false).HasException());

  auto p = MakePermission();
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  std::string test_path = "/dir/file1";
  EXPECT_TRUE(
      ns_->CreateFile(test_path, p, create_request, &create_response).IsOK());

  uint64_t inode_id = ns_->last_inode_id();
  INode inode;
  ASSERT_TRUE(ns_->GetINode(inode_id, &inode));

  cloudfs::IoMode ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::TOS_BLOCK_EXPECTED, false);
  ASSERT_EQ(ioMode, cloudfs::IoMode::TOS_BLOCK);

  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DATANODE_BLOCK_EXPECTED, false);
  ASSERT_EQ(ioMode, cloudfs::IoMode::DATANODE_BLOCK);

  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DEFAULT_MODE, false);
  ASSERT_EQ(ioMode, cloudfs::IoMode::DATANODE_BLOCK);

  // For both GetBlockLocations() and AddBlock(). Check the lifecycle policy of its ancestors inodes.

  // For COLD Storage class
  SetLifecyclePolicyResponseProto response;
  SetLifecyclePolicyRequestProto req = MakeSetLifecyclePolicy(dirPath, cloudfs::StorageClassProto::COLD);
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetLifecyclePolicy(dirPath, req, &response, UserGroupInfo(),
                                LogRpcInfo(),
                                &done);
    done.Await();
  }

  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::TOS_BLOCK_EXPECTED, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::TOS_BLOCK);
  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DATANODE_BLOCK_EXPECTED, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::DATANODE_BLOCK);
  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DEFAULT_MODE, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::TOS_BLOCK);

  // For HOT Storage class
  req = MakeSetLifecyclePolicy(dirPath, cloudfs::StorageClassProto::HOT);
  {
    SynchronizedRpcClosure done;
    ns_->AsyncSetLifecyclePolicy(dirPath, req, &response, UserGroupInfo(),
                                LogRpcInfo(),
                                &done);
    done.Await();
  }


  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::TOS_BLOCK_EXPECTED, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::TOS_BLOCK);
  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DATANODE_BLOCK_EXPECTED, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::DATANODE_BLOCK);
  ioMode = ns_->DecideIoMode(inode, cloudfs::ExpectedIoMode::DEFAULT_MODE, true);
  ASSERT_EQ(ioMode, cloudfs::IoMode::DATANODE_BLOCK);
}

TEST_F(NameSpaceTest, MergeBlockCheckTest) {
  auto p = MakePermission();

  std::string src = "/a";

  auto create_request = MakeCreateRequest();
  EXPECT_FALSE(create_request.createparent());
  CreateResponseProto create_response;
  EXPECT_TRUE(ns_->CreateFile(src, p, create_request, &create_response).IsOK());

  auto file_id = create_response.fs().fileid();
  std::vector<ExtendedBlockProto> blks;

  auto add_request = MakeAddBlockRequest();
  cnetpp::base::IPAddress client_ip("***********");
  add_request.set_src(src);
  add_request.set_fileid(file_id);
  AddBlockResponseProto add_response;

  // Add one small block, no merge task
  {
    int block_size = 4 * 1024 * 1024;
    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();

    ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 0);
  }

  // Add one large block, no merge task
  {
    int block_size = 16 * 1024 * 1024;
    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();

    ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 0);
  }

  // Add one very large block, no merge task
  {
    int block_size = 128 * 1024 * 1024;
    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();

    ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 0);
  }

  // Add one small block, no merge task
  {
    int block_size = 4 * 1024 * 1024;
    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();

    ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 0);
  }

  // Add one large block, no merge task
  {
    int block_size = 16 * 1024 * 1024;
    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();

    ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 0);
  }
}

TEST_F(NameSpaceTest, MergeBlockManyBlocks) {
  auto default_merge_block_max_candidate_count =
      FLAGS_merge_block_max_candidate_count;
  FLAGS_merge_block_max_candidate_count = 16;
  auto p = MakePermission();

  std::string src = "/a";

  auto create_request = MakeCreateRequest();
  EXPECT_FALSE(create_request.createparent());
  CreateResponseProto create_response;
  EXPECT_TRUE(ns_->CreateFile(src, p, create_request, &create_response).IsOK());

  auto file_id = create_response.fs().fileid();
  std::vector<ExtendedBlockProto> blks;

  auto add_request = MakeAddBlockRequest();
  cnetpp::base::IPAddress client_ip("***********");
  add_request.set_src(src);
  add_request.set_fileid(file_id);
  AddBlockResponseProto add_response;

  // 16 + 2 + 10 small blocks, will have two merge task
  for (int i = 0; i < 16 + 2 + 10; i++) {
    int block_size = 32 * 1024;
    if (i > 0) {
      // Add 2nd block
      auto block_id_1st = add_response.block().b().blockid();
      auto gs_1st = add_response.block().b().generationstamp();

      add_request.mutable_previous()->CopyFrom(add_response.block().b());
      add_request.mutable_previous()->set_generationstamp(gs_1st);
      add_request.mutable_previous()->set_blockid(block_id_1st);
      add_request.mutable_previous()->set_numbytes(block_size);
      blks.back().set_numbytes(block_size);
    }

    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();
    blks.emplace_back(add_response.block().b());

    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
               &report);

    auto dn_locs = add_response.block().locs();
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }

    report.Clear();
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }
  }

  ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
  INode inode;
  MergeBlockContext ctx1, ctx2;
  ns_->meta_storage()->GetINode(file_id, &inode);
  ASSERT_EQ(inode.mergingblocks_size(), 2);
  ctx1 = inode.mergingblocks(0);
  ASSERT_EQ(ctx1.block().numbytes(), 16 * 32 * 1024);
  ASSERT_EQ(ctx1.oldblocks_size(), 16);
  for (size_t i = 0; i < 16; i++) {
    ASSERT_EQ(ctx1.oldblocks(i).blockid(), blks[i].blockid());
    ASSERT_EQ(ctx1.oldblocks(i).numbytes(), blks[i].numbytes());
    ASSERT_EQ(ctx1.oldblocks(i).genstamp(), blks[i].generationstamp());
  }
  ctx2 = inode.mergingblocks(1);
  ASSERT_EQ(ctx2.block().numbytes(), 2 * 32 * 1024);
  ASSERT_EQ(ctx2.oldblocks_size(), 2);
  ASSERT_EQ(ctx2.oldblocks(0).blockid(), blks[16].blockid());
  ASSERT_EQ(ctx2.oldblocks(0).numbytes(), blks[16].numbytes());
  ASSERT_EQ(ctx2.oldblocks(0).genstamp(), blks[16].generationstamp());
  ASSERT_EQ(ctx2.oldblocks(1).blockid(), blks[17].blockid());
  ASSERT_EQ(ctx2.oldblocks(1).numbytes(), blks[17].numbytes());
  ASSERT_EQ(ctx2.oldblocks(1).genstamp(), blks[17].generationstamp());

  FLAGS_merge_block_max_candidate_count =
      default_merge_block_max_candidate_count;
}

TEST_F(NameSpaceTest, MergeBlock) {
  auto p = MakePermission();

  std::string src = "/a";

  auto create_request = MakeCreateRequest();
  EXPECT_FALSE(create_request.createparent());
  CreateResponseProto create_response;
  EXPECT_TRUE(ns_->CreateFile(src, p, create_request, &create_response).IsOK());

  auto file_id = create_response.fs().fileid();
  std::vector<ExtendedBlockProto> blks;

  auto add_request = MakeAddBlockRequest();
  cnetpp::base::IPAddress client_ip("***********");
  add_request.set_src(src);
  add_request.set_fileid(file_id);
  AddBlockResponseProto add_response;
  for (int i = 0; i < 13; i++) {
    int block_size = 4 * 1024 * 1024;
    if (i == 3) {
      block_size = 16 * 1024 * 1024 + 1;
    }
    if (i > 0) {
      // Add 2nd block
      auto block_id_1st = add_response.block().b().blockid();
      auto gs_1st = add_response.block().b().generationstamp();

      add_request.mutable_previous()->CopyFrom(add_response.block().b());
      add_request.mutable_previous()->set_generationstamp(gs_1st);
      add_request.mutable_previous()->set_blockid(block_id_1st);
      add_request.mutable_previous()->set_numbytes(block_size);
      blks.back().set_numbytes(block_size);
    }

    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();
    blks.emplace_back(add_response.block().b());

    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
               &report);

    auto dn_locs = add_response.block().locs();
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }

    report.Clear();
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }
  }

  BlockID bid1;
  MergeBlockContext ctx;
  ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
  {
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 1);
    ctx = inode.mergingblocks(0);
    ASSERT_EQ(ctx.block().numbytes(), 2 * 4 * 1024 * 1024);
    ASSERT_EQ(ctx.oldblocks_size(), 2);
    ASSERT_EQ(ctx.oldblocks(0).blockid(), blks[0].blockid());
    ASSERT_EQ(ctx.oldblocks(0).numbytes(), blks[0].numbytes());
    ASSERT_EQ(ctx.oldblocks(0).genstamp(), blks[0].generationstamp());
    ASSERT_EQ(ctx.oldblocks(1).blockid(), blks[1].blockid());
    ASSERT_EQ(ctx.oldblocks(1).numbytes(), blks[1].numbytes());
    ASSERT_EQ(ctx.oldblocks(1).genstamp(), blks[1].generationstamp());
    bid1 = ctx.block().blockid();
  }

  ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
  {
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 1);
    ctx = inode.mergingblocks(0);
    ASSERT_EQ(ctx.block().numbytes(), 2 * 4 * 1024 * 1024);
    ASSERT_EQ(ctx.oldblocks_size(), 2);
    ASSERT_EQ(ctx.oldblocks(0).blockid(), blks[0].blockid());
    ASSERT_EQ(ctx.oldblocks(0).numbytes(), blks[0].numbytes());
    ASSERT_EQ(ctx.oldblocks(0).genstamp(), blks[0].generationstamp());
    ASSERT_EQ(ctx.oldblocks(1).blockid(), blks[1].blockid());
    ASSERT_EQ(ctx.oldblocks(1).numbytes(), blks[1].numbytes());
    ASSERT_EQ(ctx.oldblocks(1).genstamp(), blks[1].generationstamp());
    ASSERT_NE(bid1, ctx.block().blockid());
  }

  std::this_thread::sleep_for(std::chrono::seconds(3));
  cloudfs::ExtendedBlockProto eb;
  {
    cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
    block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
    for (auto& cmd : heartbeat_response.cmds()) {
      if (cmd.cmdtype() ==
          cloudfs::datanode::DatanodeCommandProto::MergeCommand) {
        eb = cmd.mergecmd().block();
        ASSERT_EQ(ctx.block().blockid(), eb.blockid());
        ASSERT_EQ(ctx.block().generationstamp(), eb.generationstamp());
        ASSERT_EQ(ctx.block().numbytes(), eb.numbytes());

        auto oldb0 = cmd.mergecmd().oldblocks(0).b();
        ASSERT_EQ(blks[0].blockid(), oldb0.blockid());
        ASSERT_EQ(blks[0].generationstamp(), oldb0.generationstamp());
        ASSERT_EQ(blks[0].numbytes(), oldb0.numbytes());
        auto oldb1 = cmd.mergecmd().oldblocks(1).b();
        ASSERT_EQ(blks[1].blockid(), oldb1.blockid());
        ASSERT_EQ(blks[1].generationstamp(), oldb1.generationstamp());
        ASSERT_EQ(blks[1].numbytes(), oldb1.numbytes());
        break;
      }
    }
  }
  ASSERT_TRUE(eb.IsInitialized());

  {
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(eb.blockid(),
               eb.generationstamp(),
               2 * 4 * 1024 * 1024,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::MERGED,
               &report);
    report.Mutable(0)->mutable_blocks(0)->set_pufsname(eb.blockpufsname());
    block_manager_->IncrementalBlockReport("datanode1", report);
  }

  // Wait for merge to be down
  std::this_thread::sleep_for(std::chrono::seconds(3));

  {
    INode inode;
    ASSERT_EQ(ns_->meta_storage()->GetINode(file_id, &inode), kOK);
    ASSERT_EQ(inode.blocks_size(), 12);
    auto b = inode.blocks(0);
    ASSERT_EQ(b.blockid(), eb.blockid());
    ASSERT_EQ(b.numbytes(), eb.numbytes());
    ASSERT_EQ(b.genstamp(), eb.generationstamp());
    b = inode.blocks(1);
    ASSERT_EQ(b.blockid(), blks[2].blockid());
    ASSERT_EQ(b.numbytes(), blks[2].numbytes());
    ASSERT_EQ(b.genstamp(), blks[2].generationstamp());
    int i = 3, j = 2;
    while (i < blks.size() - 1) {
      ASSERT_EQ(blks[i].blockid(), inode.blocks(j).blockid());
      ASSERT_EQ(blks[i].numbytes(), inode.blocks(j).numbytes());
      ASSERT_EQ(blks[i].generationstamp(), inode.blocks(j).genstamp());
      i++;
      j++;
    }
    ASSERT_EQ(j, 11);
    ASSERT_EQ(inode.mergedblocks_size(), 2);
    b = inode.mergedblocks(0);
    ASSERT_EQ(b.blockid(), blks[0].blockid());
    ASSERT_EQ(b.numbytes(), blks[0].numbytes());
    ASSERT_EQ(b.genstamp(), blks[0].generationstamp());
    b = inode.mergedblocks(1);
    ASSERT_EQ(b.blockid(), blks[1].blockid());
    ASSERT_EQ(b.numbytes(), blks[1].numbytes());
    ASSERT_EQ(b.genstamp(), blks[1].generationstamp());
  }
}

TEST_F(NameSpaceTest, TwoMergeBlock) {
  auto p = MakePermission();

  std::string src = "/a";

  auto create_request = MakeCreateRequest();
  EXPECT_FALSE(create_request.createparent());
  CreateResponseProto create_response;
  EXPECT_TRUE(ns_->CreateFile(src, p, create_request, &create_response).IsOK());

  auto file_id = create_response.fs().fileid();
  std::vector<ExtendedBlockProto> blks;

  auto add_request = MakeAddBlockRequest();
  cnetpp::base::IPAddress client_ip("***********");
  add_request.set_src(src);
  add_request.set_fileid(file_id);
  AddBlockResponseProto add_response;
  for (int i = 0; i < 23; i++) {
    int block_size = 4 * 1024 * 1024;
    if (i == 11 || i == 14) {
      block_size = 128 * 1024 * 1024;
    }

    if (i > 0) {
      // Add 2nd block
      auto block_id_1st = add_response.block().b().blockid();
      auto gs_1st = add_response.block().b().generationstamp();

      add_request.mutable_previous()->CopyFrom(add_response.block().b());
      add_request.mutable_previous()->set_generationstamp(gs_1st);
      add_request.mutable_previous()->set_blockid(block_id_1st);
      add_request.mutable_previous()->set_numbytes(block_size);
      blks.back().set_numbytes(block_size);
    }

    add_response.Clear();
    auto add_status = ns_->AddBlock(
        src, client_ip, default_rpc_info, add_request, &add_response);
    DLOG(ERROR) << add_status.ToString();

    ASSERT_FALSE(add_status.HasException());
    DLOG(ERROR) << add_response.ShortDebugString();
    blks.emplace_back(add_response.block().b());

    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
               &report);

    auto dn_locs = add_response.block().locs();
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }

    report.Clear();
    MakeReport(add_response.block().b().blockid(),
               add_response.block().b().generationstamp(),
               block_size,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
               &report);
    for (auto& dn_loc : dn_locs) {
      block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(),
                                             report);
    }
  }

  MergeBlockContext ctx1, ctx2;
  ASSERT_TRUE(ns_->MergeBlocks(file_id).IsOK());
  {
    INode inode;
    ns_->meta_storage()->GetINode(file_id, &inode);
    ASSERT_EQ(inode.mergingblocks_size(), 2);
    ctx1 = inode.mergingblocks(0);
    ASSERT_EQ(ctx1.block().numbytes(), 10 * 4 * 1024 * 1024);
    ASSERT_EQ(ctx1.oldblocks_size(), 10);
    for (int i = 0; i < 10; i++) {
      ASSERT_EQ(ctx1.oldblocks(i).blockid(), blks[i].blockid());
      ASSERT_EQ(ctx1.oldblocks(i).numbytes(), blks[i].numbytes());
      ASSERT_EQ(ctx1.oldblocks(i).genstamp(), blks[i].generationstamp());
    }
    ctx2 = inode.mergingblocks(1);
    ASSERT_EQ(ctx2.block().numbytes(), 2 * 4 * 1024 * 1024);
    ASSERT_EQ(ctx2.oldblocks_size(), 2);
    for (int i = 0; i < 2; i++) {
      ASSERT_EQ(ctx2.oldblocks(i).blockid(), blks[i + 11].blockid());
      ASSERT_EQ(ctx2.oldblocks(i).numbytes(), blks[i + 11].numbytes());
      ASSERT_EQ(ctx2.oldblocks(i).genstamp(), blks[i + 11].generationstamp());
    }
  }

  std::this_thread::sleep_for(std::chrono::seconds(3));
  cloudfs::ExtendedBlockProto eb1, eb2;
  {
    int idx = 0;
    cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
    block_manager_->GetCommands(1, "bpid", 0, &heartbeat_response);
    for (auto& cmd : heartbeat_response.cmds()) {
      if (cmd.cmdtype() ==
          cloudfs::datanode::DatanodeCommandProto::MergeCommand) {
        if (idx == 0) {
          eb1 = cmd.mergecmd().block();
          ASSERT_EQ(ctx2.block().blockid(), eb1.blockid());
          ASSERT_EQ(ctx2.block().generationstamp(), eb1.generationstamp());
          ASSERT_EQ(ctx2.block().numbytes(), eb1.numbytes());

          for (int i = 0; i < 2; i++) {
            auto oldb = cmd.mergecmd().oldblocks(i).b();
            ASSERT_EQ(blks[i + 11].blockid(), oldb.blockid());
            ASSERT_EQ(blks[i + 11].generationstamp(), oldb.generationstamp());
            ASSERT_EQ(blks[i + 11].numbytes(), oldb.numbytes());
          }

          idx++;
        } else if (idx == 1) {
          eb2 = cmd.mergecmd().block();
          ASSERT_EQ(ctx1.block().blockid(), eb2.blockid());
          ASSERT_EQ(ctx1.block().generationstamp(), eb2.generationstamp());
          ASSERT_EQ(ctx1.block().numbytes(), eb2.numbytes());

          for (int i = 0; i < 10; i++) {
            auto oldb = cmd.mergecmd().oldblocks(i).b();
            ASSERT_EQ(blks[i].blockid(), oldb.blockid());
            ASSERT_EQ(blks[i].generationstamp(), oldb.generationstamp());
            ASSERT_EQ(blks[i].numbytes(), oldb.numbytes());
          }

          idx++;
        } else if (idx == 2) {
          break;
        }
      }
    }
  }
  ASSERT_TRUE(eb1.IsInitialized());
  ASSERT_TRUE(eb2.IsInitialized());

  {
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(eb1.blockid(),
               eb1.generationstamp(),
               2 * 4 * 1024 * 1024,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::MERGED,
               &report);
    report.Mutable(0)->mutable_blocks(0)->set_pufsname(eb1.blockpufsname());
    block_manager_->IncrementalBlockReport("datanode1", report);
  }
  {
    BlockManager::RepeatedIncBlockReport report;
    MakeReport(eb2.blockid(),
               eb2.generationstamp(),
               10 * 4 * 1024 * 1024,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::MERGED,
               &report);
    report.Mutable(0)->mutable_blocks(0)->set_pufsname(eb2.blockpufsname());
    block_manager_->IncrementalBlockReport("datanode1", report);
  }

  // Wait for merge to be done
  std::this_thread::sleep_for(std::chrono::seconds(3));

  {
    INode inode;
    ASSERT_EQ(ns_->meta_storage()->GetINode(file_id, &inode), kOK);
    ASSERT_EQ(inode.blocks_size(), 13);
    auto b = inode.blocks(0);
    ASSERT_EQ(b.blockid(), eb2.blockid());
    ASSERT_EQ(b.numbytes(), eb2.numbytes());
    ASSERT_EQ(b.genstamp(), eb2.generationstamp());
    b = inode.blocks(1);
    ASSERT_EQ(b.blockid(), blks[10].blockid());
    b = inode.blocks(2);
    ASSERT_EQ(b.blockid(), eb1.blockid());
    ASSERT_EQ(b.numbytes(), eb1.numbytes());
    ASSERT_EQ(b.genstamp(), eb1.generationstamp());
    int i = 13, j = 3;
    while (i < blks.size() - 1) {
      ASSERT_EQ(blks[i].blockid(), inode.blocks(j).blockid());
      ASSERT_EQ(blks[i].numbytes(), inode.blocks(j).numbytes());
      ASSERT_EQ(blks[i].generationstamp(), inode.blocks(j).genstamp());
      i++;
      j++;
    }
    ASSERT_EQ(j, 12);
    ASSERT_EQ(inode.mergedblocks_size(), 12);
    for (int i = 0; i < 10; i++) {
      ASSERT_EQ(inode.mergedblocks(i + 2).blockid(), blks[i].blockid());
      ASSERT_EQ(inode.mergedblocks(i + 2).numbytes(), blks[i].numbytes());
      ASSERT_EQ(inode.mergedblocks(i + 2).genstamp(), blks[i].generationstamp());
    }
    for (int i = 0; i < 2; i++) {
      ASSERT_EQ(inode.mergedblocks(i).blockid(), blks[i + 11].blockid());
      ASSERT_EQ(inode.mergedblocks(i).numbytes(), blks[i + 11].numbytes());
      ASSERT_EQ(inode.mergedblocks(i).genstamp(), blks[i + 11].generationstamp());
    }
  }
}

TEST_F(NameSpaceTest, MergeBlockGenPlan) {
  {
    INode inode;
    for (int i = 0; i < 10; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 10, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 0);
  }

  {
    INode inode;
    for (int i = 0; i < 11; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 10, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 1);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 1024 * 1);
    EXPECT_EQ(merge_block_info[0].count, 1);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
  }

  {
    INode inode;
    for (int i = 0; i < 11; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 0, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 1);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 1024 * 11);
    EXPECT_EQ(merge_block_info[0].count, 11);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
  }

  {
    INode inode;
    for (int i = 0; i < 4; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    for (int i = 0; i < 1; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(16 * 1024 * 1024 + 1);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 0, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 2);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 1024 * 4);
    EXPECT_EQ(merge_block_info[0].count, 4);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
    EXPECT_EQ(merge_block_info[1].offset, 4 * 1024);
    EXPECT_EQ(merge_block_info[1].length, 16 * 1024 * 1024 + 1);
    EXPECT_EQ(merge_block_info[1].count, 1);
    EXPECT_EQ(merge_block_info[1].start_idx, 4);
  }
  {
    INode inode;
    for (int i = 0; i < 1; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(16 * 1024 * 1024 + 1);
    }
    for (int i = 0; i < 4; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 0, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 2);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 16 * 1024 * 1024 + 1);
    EXPECT_EQ(merge_block_info[0].count, 1);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
    EXPECT_EQ(merge_block_info[1].offset, 16 * 1024 * 1024 + 1);
    EXPECT_EQ(merge_block_info[1].length, 1024 * 4);
    EXPECT_EQ(merge_block_info[1].count, 4);
    EXPECT_EQ(merge_block_info[1].start_idx, 1);
  }
  {
    INode inode;
    for (int i = 0; i < 32; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(4 * 1024 * 1024);
    }
    for (int i = 0; i < 4; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 0, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 2);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 32 * 4 * 1024 * 1024);
    EXPECT_EQ(merge_block_info[0].count, 32);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
    EXPECT_EQ(merge_block_info[1].offset, 32 * 4 * 1024 * 1024);
    EXPECT_EQ(merge_block_info[1].length, 1024 * 4);
    EXPECT_EQ(merge_block_info[1].count, 4);
    EXPECT_EQ(merge_block_info[1].start_idx, 32);
  }
  {
    INode inode;
    for (int i = 0; i < 4097; i++) {
      auto* b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(4 * 1024);
    }
    std::vector<NameSpace::MergeTaskInfo> merge_block_info;
    ns_->MergeBlockGenPlan(inode.blocks(), 0, &merge_block_info);
    EXPECT_EQ(merge_block_info.size(), 2);
    EXPECT_EQ(merge_block_info[0].offset, 0);
    EXPECT_EQ(merge_block_info[0].length, 4096 * 4 * 1024);
    EXPECT_EQ(merge_block_info[0].count, 4096);
    EXPECT_EQ(merge_block_info[0].start_idx, 0);
    EXPECT_EQ(merge_block_info[1].offset, 4096 * 4 * 1024);
    EXPECT_EQ(merge_block_info[1].length, 4 * 1024);
    EXPECT_EQ(merge_block_info[1].count, 1);
    EXPECT_EQ(merge_block_info[1].start_idx, 4096);
  }
}

TEST_F(NameSpaceTest, MergeBlockAccCheckBlocks) {
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeInvalid);

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_FALSE(s.IsOK());
  }

  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeNormal);

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_FALSE(s.IsOK());
  }

  // Append file no blocks
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeAppend);

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(persisted_blocks.size(), 0);
    EXPECT_EQ(to_be_persisted_blocks.size(), 0);
  }

  // Append file all persisted blocks
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeAppend);

    for (int i = 0; i < 10; i++) {
      auto b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_inode_id(1);
      bip.set_expected_rep(1);
      bip.set_block_id(i);
      bip.set_gen_stamp(i);
      bip.set_num_bytes(1024);
      bip.set_state(BlockInfoProto::kPersisted);
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
    }

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(persisted_blocks.size(), 10);
    EXPECT_EQ(to_be_persisted_blocks.size(), 0);
    for (int i = 0; i < 10; i++) {
      EXPECT_EQ(persisted_blocks.Get(i).blockid(), i);
      EXPECT_EQ(persisted_blocks.Get(i).genstamp(), i);
      EXPECT_EQ(persisted_blocks.Get(i).numbytes(), 1024);
    }
  }

  // Append file one uploading block
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeAppend);

    auto b = inode.add_blocks();
    b->set_blockid(1);
    b->set_genstamp(1);
    b->set_numbytes(1024);
    BlockInfoProto bip;
    bip.set_inode_id(1);
    bip.set_expected_rep(1);
    bip.set_block_id(1);
    bip.set_gen_stamp(1);
    bip.set_num_bytes(1024);
    bip.set_state(BlockInfoProto::kUploadIssued);
    ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(persisted_blocks.size(), 0);
    EXPECT_EQ(to_be_persisted_blocks.size(), 0);
  }

  // Append file all to be persisted block
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeAppend);

    for (int i = 0; i < 10; i++) {
      auto b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_inode_id(1);
      bip.set_expected_rep(1);
      bip.set_block_id(i);
      bip.set_gen_stamp(i);
      bip.set_num_bytes(1024);
      if (i == 9) {
        bip.set_state(BlockInfoProto::kUnderConstruction);
      } else {
        bip.set_state(BlockInfoProto::kComplete);
      }
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
    }

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(persisted_blocks.size(), 0);
    EXPECT_EQ(to_be_persisted_blocks.size(), 10);
    for (int i = 0; i < 10; i++) {
      EXPECT_EQ(to_be_persisted_blocks.Get(i).blockid(), i);
      EXPECT_EQ(to_be_persisted_blocks.Get(i).genstamp(), i);
      EXPECT_EQ(to_be_persisted_blocks.Get(i).numbytes(), 1024);
    }
  }

  // Append file real file
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.mutable_ufs_file_info()->set_create_type(kUfsFileCreateTypeAppend);

    for (int i = 0; i < 10; i++) {
      auto b = inode.add_blocks();
      b->set_blockid(i);
      b->set_genstamp(i);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_inode_id(1);
      bip.set_expected_rep(1);
      bip.set_block_id(i);
      bip.set_gen_stamp(i);
      bip.set_num_bytes(1024);
      if (i < 5) {
        bip.set_state(BlockInfoProto::kPersisted);
      } else if (i == 5) {
        bip.set_state(BlockInfoProto::kUploadIssued);
      } else if (i == 9) {
        bip.set_state(BlockInfoProto::kUnderConstruction);
      } else {
        bip.set_state(BlockInfoProto::kComplete);
      }
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
    }

    google::protobuf::RepeatedPtrField<cloudfs::BlockProto> persisted_blocks,
        to_be_persisted_blocks;

    auto s = ns_->MergeBlockAccCheckBlocks(
        iip, &persisted_blocks, &to_be_persisted_blocks);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(persisted_blocks.size(), 5);
    EXPECT_EQ(to_be_persisted_blocks.size(), 4);
    for (int i = 0; i < 5; i++) {
      EXPECT_EQ(persisted_blocks.Get(i).blockid(), i);
      EXPECT_EQ(persisted_blocks.Get(i).genstamp(), i);
      EXPECT_EQ(persisted_blocks.Get(i).numbytes(), 1024);
    }
    for (int i = 0; i < 4; i++) {
      EXPECT_EQ(to_be_persisted_blocks.Get(i).blockid(), i + 6);
      EXPECT_EQ(to_be_persisted_blocks.Get(i).genstamp(), i + 6);
      EXPECT_EQ(to_be_persisted_blocks.Get(i).numbytes(), 1024);
    }
  }
}

TEST_F(NameSpaceTest, MergeBlockAcc) {
  auto default_namespace_type = FLAGS_namespace_type;
  FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
  block_manager_->TestOnlyGetBlockPufsInfoMonitor()->Stop();

  // Test only
  FLAGS_merge_block_support_acc_non_persisted_blocks = false;

  // 100 to 1 blocks
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.set_id(16386);
    inode.set_parent_id(16385);
    inode.set_name("a");
    inode.mutable_permission()->set_permission(644);
    inode.mutable_permission()->set_groupname("supergroup");
    inode.mutable_permission()->set_username("root");
    inode.set_type(INode_Type_kFile);
    inode.set_mtime(0);
    inode.set_atime(0);
    auto ufs_file_info = inode.mutable_ufs_file_info();
    ufs_file_info->set_file_state(kUfsFileStatePersisted);
    ufs_file_info->set_create_type(kUfsFileCreateTypeAppend);
    ufs_file_info->set_etag("");
    ufs_file_info->set_size(102400);
    ufs_file_info->set_last_modified_ts(0);
    ufs_file_info->set_sync_ts(0);
    uint64_t max_block_id = 0, max_gsv2 = 0;
    for (int i = 0; i < 100; i++) {
      auto b = inode.add_blocks();
      uint64_t block_id = ns_->NextBlockId();
      uint64_t gsv2 = ns_->NextGenerationStampV2();
      b->set_blockid(block_id);
      b->set_genstamp(gsv2);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_state(BlockInfoProto::kPersisted);
      bip.set_block_id(block_id);
      bip.set_gen_stamp(gsv2);
      bip.set_num_bytes(1024);
      bip.set_inode_id(16386);
      bip.set_expected_rep(1);
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
      max_block_id = block_id;
      max_gsv2 = gsv2;
    }
    std::shared_ptr<INode> inode_ptr = std::make_shared<INode>(inode);
    ns_->meta_storage()->InsertINode(inode_ptr);

    EXPECT_TRUE(ns_->MergeBlocks(16386).IsOK());
    INode target_inode;
    EXPECT_TRUE(ns_->GetINode(16386, &target_inode));
    EXPECT_EQ(target_inode.blocks_size(), 1);
    EXPECT_EQ(target_inode.blocks(0).blockid(), max_block_id + 1);
    EXPECT_EQ(target_inode.blocks(0).genstamp(), max_gsv2 + 1);
    EXPECT_EQ(target_inode.blocks(0).numbytes(), 102400);
    EXPECT_EQ(target_inode.ufs_file_info().SerializeAsString(),
              inode.ufs_file_info().SerializeAsString());
    BlockInfoProto target_bip;
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 1, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kPersisted);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 1);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 1);
    EXPECT_EQ(target_bip.num_bytes(), 102400);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());

    // Wait for deprecated
    block_manager_->HandleDeprecatingBlocks();
    ns_->WaitNoPending();
    block_manager_->HandleDeprecatedBlocks();
    ns_->WaitNoPending();
    for (auto& old_b : inode.blocks()) {
      BlockInfoProto old_bip;
      if (ns_->meta_storage()->GetBlockInfo(old_b.blockid(), &old_bip)) {
        EXPECT_EQ(old_bip.state(), BlockInfoProto::kDeprecated);
        EXPECT_EQ(old_bip.block_id(), old_b.blockid());
        EXPECT_EQ(old_bip.gen_stamp(), old_b.genstamp());
      }
    }
  }

  // 100 to 1 blocks with upload issued and to be persisted
  {
    INodeInPath iip;
    INode& inode = iip.MutableInode();
    inode.set_id(16386);
    inode.set_parent_id(16385);
    inode.set_name("a");
    inode.mutable_permission()->set_permission(644);
    inode.mutable_permission()->set_groupname("supergroup");
    inode.mutable_permission()->set_username("root");
    inode.set_type(INode_Type_kFile);
    inode.set_mtime(0);
    inode.set_atime(0);
    auto ufs_file_info = inode.mutable_ufs_file_info();
    ufs_file_info->set_file_state(kUfsFileStatePersisted);
    ufs_file_info->set_create_type(kUfsFileCreateTypeAppend);
    ufs_file_info->set_etag("");
    ufs_file_info->set_size(102400);
    ufs_file_info->set_last_modified_ts(0);
    ufs_file_info->set_sync_ts(0);
    uint64_t max_block_id = 0, max_gsv2 = 0;
    {
      // Persisted
      for (int i = 0; i < 100; i++) {
        auto b = inode.add_blocks();
        uint64_t block_id = ns_->NextBlockId();
        uint64_t gsv2 = ns_->NextGenerationStampV2();
        b->set_blockid(block_id);
        b->set_genstamp(gsv2);
        b->set_numbytes(1024);
        BlockInfoProto bip;
        bip.set_state(BlockInfoProto::kPersisted);
        bip.set_block_id(block_id);
        bip.set_gen_stamp(gsv2);
        bip.set_num_bytes(1024);
        bip.set_inode_id(16386);
        bip.set_expected_rep(1);
        ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
        max_block_id = block_id;
        max_gsv2 = gsv2;
      }
    }
    {
      // Upload issued
      auto b = inode.add_blocks();
      uint64_t block_id = ns_->NextBlockId();
      uint64_t gsv2 = ns_->NextGenerationStampV2();
      b->set_blockid(block_id);
      b->set_genstamp(gsv2);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_state(BlockInfoProto::kUploadIssued);
      bip.set_block_id(block_id);
      bip.set_gen_stamp(gsv2);
      bip.set_num_bytes(1024);
      bip.set_inode_id(16386);
      bip.set_expected_rep(1);
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
    }
    {
      // To be persisted
      for (int i = 0; i < 100; i++) {
        auto b = inode.add_blocks();
        uint64_t block_id = ns_->NextBlockId();
        uint64_t gsv2 = ns_->NextGenerationStampV2();
        b->set_blockid(block_id);
        b->set_genstamp(gsv2);
        b->set_numbytes(1024);
        BlockInfoProto bip;
        bip.set_state(BlockInfoProto::kComplete);
        bip.set_block_id(block_id);
        bip.set_gen_stamp(gsv2);
        bip.set_num_bytes(1024);
        bip.set_inode_id(16386);
        bip.set_expected_rep(1);
        ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
      }
    }
    std::shared_ptr<INode> inode_ptr = std::make_shared<INode>(inode);
    ns_->meta_storage()->InsertINode(inode_ptr);

    EXPECT_TRUE(ns_->MergeBlocks(16386).IsOK());
    INode target_inode;
    EXPECT_TRUE(ns_->GetINode(16386, &target_inode));
    EXPECT_EQ(target_inode.blocks_size(), 102);
    EXPECT_EQ(target_inode.blocks(0).blockid(), max_block_id + 102);
    EXPECT_EQ(target_inode.blocks(0).genstamp(), max_gsv2 + 102);
    EXPECT_EQ(target_inode.blocks(0).numbytes(), 102400);
    EXPECT_EQ(target_inode.ufs_file_info().SerializeAsString(),
              inode.ufs_file_info().SerializeAsString());
    BlockInfoProto target_bip;
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 102, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kPersisted);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 102);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 102);
    EXPECT_EQ(target_bip.num_bytes(), 102400);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());

    // Upload Issued
    EXPECT_EQ(target_inode.blocks(1).blockid(), max_block_id + 1);
    EXPECT_EQ(target_inode.blocks(1).genstamp(), max_gsv2 + 1);
    EXPECT_EQ(target_inode.blocks(1).numbytes(), 1024);
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 1, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kUploadIssued);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 1);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 1);
    EXPECT_EQ(target_bip.num_bytes(), 1024);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());

    // Completed
    for (int i = 0; i < 100; i++) {
      EXPECT_EQ(target_inode.blocks(2 + i).blockid(), max_block_id + 2 + i);
      EXPECT_EQ(target_inode.blocks(2 + i).genstamp(), max_gsv2 + 2 + i);
      EXPECT_EQ(target_inode.blocks(2 + i).numbytes(), 1024);
      EXPECT_TRUE(
          ns_->meta_storage()->GetBlockInfo(max_block_id + 2 + i, &target_bip));
      EXPECT_EQ(target_bip.state(), BlockInfoProto::kComplete);
      EXPECT_EQ(target_bip.block_id(), max_block_id + 2 + i);
      EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 2 + i);
      EXPECT_EQ(target_bip.num_bytes(), 1024);
      EXPECT_EQ(target_bip.inode_id(), target_inode.id());
    }

    block_manager_->HandleDeprecatingBlocks();
    ns_->WaitNoPending();
    block_manager_->HandleDeprecatedBlocks();
    ns_->WaitNoPending();
    for (int i = 0; i < 100; i++) {
      auto& old_b = inode.blocks(i);
      BlockInfoProto old_bip;
      if (ns_->meta_storage()->GetBlockInfo(old_b.blockid(), &old_bip)) {
        EXPECT_EQ(old_bip.state(), BlockInfoProto::kDeprecated);
        EXPECT_EQ(old_bip.block_id(), old_b.blockid());
        EXPECT_EQ(old_bip.gen_stamp(), old_b.genstamp());
      }
    }
  }

  FLAGS_merge_block_support_acc_non_persisted_blocks = true;
  FLAGS_namespace_type = default_namespace_type;
}

TEST_F(NameSpaceTest, MergeBlockAccManyBlocks) {
  auto default_namespace_type = FLAGS_namespace_type;
  FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;

  // Test only
  FLAGS_merge_block_support_acc_non_persisted_blocks = false;

  {
    std::shared_ptr<INodeInPath> iip = std::make_shared<INodeInPath>();
    INode& inode = iip->MutableInode();
    inode.set_id(16386);
    inode.set_parent_id(16385);
    inode.set_name("a");
    inode.mutable_permission()->set_permission(644);
    inode.mutable_permission()->set_groupname("supergroup");
    inode.mutable_permission()->set_username("root");
    inode.set_type(INode_Type_kFile);
    inode.set_mtime(0);
    inode.set_atime(0);
    auto ufs_file_info = inode.mutable_ufs_file_info();
    ufs_file_info->set_file_state(kUfsFileStatePersisted);
    ufs_file_info->set_create_type(kUfsFileCreateTypeAppend);
    ufs_file_info->set_etag("");
    ufs_file_info->set_size(102400);
    ufs_file_info->set_last_modified_ts(0);
    ufs_file_info->set_sync_ts(0);
    uint64_t max_block_id = 0, max_gsv2 = 0;
    for (int i = 0; i < 10000; i++) {
      auto b = inode.add_blocks();
      uint64_t block_id = ns_->NextBlockId();
      uint64_t gsv2 = ns_->NextGenerationStampV2();
      b->set_blockid(block_id);
      b->set_genstamp(gsv2);
      b->set_numbytes(1024);
      BlockInfoProto bip;
      bip.set_state(BlockInfoProto::kPersisted);
      bip.set_block_id(block_id);
      bip.set_gen_stamp(gsv2);
      bip.set_num_bytes(1024);
      bip.set_inode_id(16386);
      bip.set_expected_rep(1);
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip, false);
      max_block_id = block_id;
      max_gsv2 = gsv2;
    }
    std::shared_ptr<INode> inode_ptr = std::make_shared<INode>(inode);
    ns_->meta_storage()->InsertINode(inode_ptr);

    EXPECT_TRUE(ns_->MergeBlocks(16386).IsOK());
    INode target_inode;
    EXPECT_TRUE(ns_->GetINode(16386, &target_inode));
    EXPECT_EQ(target_inode.blocks_size(), 3);
    EXPECT_EQ(target_inode.blocks(0).blockid(), max_block_id + 1);
    EXPECT_EQ(target_inode.blocks(0).genstamp(), max_gsv2 + 1);
    EXPECT_EQ(target_inode.blocks(0).numbytes(), 4194304);
    EXPECT_EQ(target_inode.blocks(1).blockid(), max_block_id + 2);
    EXPECT_EQ(target_inode.blocks(1).genstamp(), max_gsv2 + 2);
    EXPECT_EQ(target_inode.blocks(1).numbytes(), 4194304);
    EXPECT_EQ(target_inode.blocks(2).blockid(), max_block_id + 3);
    EXPECT_EQ(target_inode.blocks(2).genstamp(), max_gsv2 + 3);
    EXPECT_EQ(target_inode.blocks(2).numbytes(), 1851392);
    EXPECT_EQ(target_inode.ufs_file_info().SerializeAsString(),
              inode.ufs_file_info().SerializeAsString());
    BlockInfoProto target_bip;
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 1, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kPersisted);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 1);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 1);
    EXPECT_EQ(target_bip.num_bytes(), 4194304);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 2, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kPersisted);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 2);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 2);
    EXPECT_EQ(target_bip.num_bytes(), 4194304);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());
    EXPECT_TRUE(
        ns_->meta_storage()->GetBlockInfo(max_block_id + 3, &target_bip));
    EXPECT_EQ(target_bip.state(), BlockInfoProto::kPersisted);
    EXPECT_EQ(target_bip.block_id(), max_block_id + 3);
    EXPECT_EQ(target_bip.gen_stamp(), max_gsv2 + 3);
    EXPECT_EQ(target_bip.num_bytes(), 1851392);
    EXPECT_EQ(target_bip.inode_id(), target_inode.id());

    // Wait for deprecated
    block_manager_->HandleDeprecatingBlocks();
    ns_->WaitNoPending();
    block_manager_->HandleDeprecatedBlocks();
    ns_->WaitNoPending();
    for (auto& old_b : inode.blocks()) {
      BlockInfoProto old_bip;
      if (ns_->meta_storage()->GetBlockInfo(old_b.blockid(), &old_bip)) {
        EXPECT_EQ(old_bip.state(), BlockInfoProto::kDeprecated);
        EXPECT_EQ(old_bip.block_id(), old_b.blockid());
        EXPECT_EQ(old_bip.gen_stamp(), old_b.genstamp());
      }
    }
  }

  FLAGS_merge_block_support_acc_non_persisted_blocks = true;
  FLAGS_namespace_type = default_namespace_type;
}

TEST_F(NameSpaceTest, StoreLoadReplicaPolicy) {
  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();
  ns_->StopMetaScanner();
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /.RECYCLE.BIN           [id: kRecycleBinINodeId (kLastReservedINodeId)]
  //  /a                      [id: kLastReservedINodeId + 1]
  //    /b                    [id: kLastReservedINodeId + 2]
  //    /c                    [id: kLastReservedINodeId + 3]
  //
  ASSERT_TRUE(!ns_->MkDirs("/a/b", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a/c", MakePermission(), true).HasException());
  INode inode;
  ASSERT_EQ(ns_->meta_storage()->GetINode(kRootINodeId, "a", &inode),
            StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "b", &inode),
      StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "c", &inode),
      StatusCode::kOK);
  Status s;
  ReplicaPolicy policy_a;
  policy_a.set_distributed(false);
  ReplicaPolicy policy_b;
  policy_b.set_distributed(true);
  policy_b.add_dc("HL");
  policy_b.add_dc("LF");
  ReplicaPolicy policy_c;
  policy_c.set_distributed(true);
  policy_c.add_dc("LF");
  policy_c.add_dc("LQ");
  s = ns_->SetReplicaPolicy("/a", policy_a);
  ASSERT_TRUE(s.IsOK()) << s.ToString();
  s = ns_->SetReplicaPolicy("/a/b", policy_b);
  ASSERT_TRUE(s.IsOK()) << s.ToString();
  s = ns_->SetReplicaPolicy("/a/c", policy_c);
  ASSERT_TRUE(s.IsOK()) << s.ToString();

  auto replica_policy_manager = ns_->policy_manager()->ReplicaPolicyInstance();

  ReplicaPolicy policy;
  {
    replica_policy_manager->GetPolicy("/a", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_a.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/b", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_b.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/c", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_c.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/e/d", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_a.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/b/c", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_b.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/c/d", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_c.ShortDebugString());
  }
  // Notice: NameSpaceTest::block_manager_ holds meta_storage_.
  // So old meta storag is alive when MockNameSpace creates a new one.
  ns_->StopActive();
  ns_->Stop();
  ns_->meta_storage()->Shutdown();
  ns_.reset();
  block_manager_.reset();
  edit_log_ctx_ = CreateContext();
  block_manager_.reset(new BlockManager(edit_log_ctx_));
  block_manager_->TestOnlySetEditLogCtx(edit_log_ctx_);
  ns_.reset(new MockNameSpace(db_path_,
                              edit_log_ctx_,
                              block_manager_,
                              datanode_manager_,
                              std::make_shared<DataCenters>(),
                              UfsEnv::Create()));
  replica_policy_manager = ns_->policy_manager()->ReplicaPolicyInstance();
  ns_->set_safemode(safemode_.get());
  ns_->set_ha_state(ha_state_.get());
  block_manager_->set_ha_state(ha_state_.get());
  block_manager_->set_safemode(safemode_.get());
  block_manager_->set_ns(ns_.get());
  datanode_manager_->set_block_manager(block_manager_.get());
  // mock edit log sender
  auto last_tx_id = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx_, last_tx_id));
  ns_->TestOnlySetEditLogSender(std::move(sender));

  ns_->Start();
  ns_->StartActive();

  {
    replica_policy_manager->GetPolicy("/a", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_a.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/b", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_b.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/c", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_c.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/e/d", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_a.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/b/c", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_b.ShortDebugString());
  }
  {
    replica_policy_manager->GetPolicy("/a/c/d", &policy);
    ASSERT_EQ(policy.ShortDebugString(), policy_c.ShortDebugString());
  }

  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();
}

TEST_F(NameSpaceTest, GetSetListRemovePolicy) {
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /.RECYCLE.BIN           [id: kRecycleBinINodeId (kLastReservedINodeId)]
  //  /a                      [id: kLastReservedINodeId + 1]
  //    /b                    [id: kLastReservedINodeId + 2]
  //      /bb                 [id: kLastReservedINodeId + 4]
  //    /c                    [id: kLastReservedINodeId + 3]
  //      /cc                 [id: kLastReservedINodeId + 5]
  //
  ASSERT_TRUE(!ns_->MkDirs("/a/b", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a/c", MakePermission(), true).HasException());
  INode inode;
  ASSERT_EQ(ns_->meta_storage()->GetINode(kRootINodeId, "a", &inode),
            StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "b", &inode),
      StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "c", &inode),
      StatusCode::kOK);
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/b/bb", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 2, "bb", &inode),
        StatusCode::kOK);
  }
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/c/cc", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "cc", &inode),
        StatusCode::kOK);
  }
  // Set
  {
    ::cloudfs::SetDirPolicyRequestProto request;
    request.mutable_replica_policy()->set_distributed(true);
    request.mutable_replica_policy()->add_dc("HL");
    request.mutable_replica_policy()->add_dc("LF");

    SynchronizedRpcClosure done;
    ns_->AsyncSetDirPolicy("/a/b", &request, &done);
    done.Await();
    ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
  }

  {
    ::cloudfs::SetDirPolicyRequestProto request;
    request.mutable_replica_policy()->set_distributed(false);
    request.mutable_replica_policy()->add_dc("HL");
    request.mutable_upload_policy()->set_upload_interval_ms(0);

    SynchronizedRpcClosure done;
    ns_->AsyncSetDirPolicy("/a/c", &request, &done);
    done.Await();
    ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
  }

  // Get All Policy
  {
    ::cloudfs::GetDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::GetDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->GetDirPolicy("/a/b/bb", &request, &response).IsOK());

    ASSERT_EQ(response.has_replica_policy(), true);
    ASSERT_EQ(response.replica_policy().distributed(), true);
    ASSERT_EQ(response.replica_policy().dc_size(), 2);
    ASSERT_EQ(response.replica_policy().dc(0), "HL");
    ASSERT_EQ(response.replica_policy().dc(1), "LF");

    ASSERT_EQ(response.has_read_policy(), true);
    ASSERT_EQ(response.read_policy().ShortDebugString(),
              kDefaultReadPolicy.ShortDebugString());

    ASSERT_EQ(response.has_upload_policy(), true);
    ASSERT_EQ(response.upload_policy().ShortDebugString(),
              kDefaultUploadPolicy.ShortDebugString());
  }

  // Get Only one Policy
  {
    ::cloudfs::GetDirPolicyRequestProto request;
    request.set_need_replica_policy(false);
    request.set_need_read_policy(false);
    request.set_need_upload_policy(true);
    ::cloudfs::GetDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->GetDirPolicy("/a/b/bb", &request, &response).IsOK());

    ASSERT_EQ(response.has_replica_policy(), false);

    ASSERT_EQ(response.has_read_policy(), false);

    ASSERT_EQ(response.has_upload_policy(), true);
    ASSERT_EQ(response.upload_policy().upload_interval_ms(), 0);
  }

  // List
  {
    ::cloudfs::ListDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::ListDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->ListDirPolicy(&request, &response).IsOK());

    LOG(INFO) << "List response.ShortDebugString()="
              << response.ShortDebugString();
    ASSERT_EQ(response.policy_list().size(), 3);
  }

  // Remove
  {
    ::cloudfs::RemoveDirPolicyRequestProto request;
    request.set_remove_upload_policy(true);

    SynchronizedRpcClosure done;
    ns_->AsyncRemoveDirPolicy("/a/c", &request, &done);
    done.Await();
    ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
  }

  {
    ::cloudfs::GetDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::GetDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->GetDirPolicy("/a/c/cc", &request, &response).IsOK());

    ASSERT_EQ(response.has_replica_policy(), true);
    ASSERT_EQ(response.replica_policy().distributed(), false);
    ASSERT_EQ(response.replica_policy().dc_size(), 1);
    ASSERT_EQ(response.replica_policy().dc(0), "HL");

    ASSERT_EQ(response.has_read_policy(), true);
    ASSERT_EQ(response.read_policy().ShortDebugString(),
              kDefaultReadPolicy.ShortDebugString());

    ASSERT_EQ(response.has_upload_policy(), true);
    ASSERT_EQ(response.upload_policy().ShortDebugString(),
              kDefaultUploadPolicy.ShortDebugString());
  }

  {
    ::cloudfs::ListDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::ListDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->ListDirPolicy(&request, &response).IsOK());

    // upload add default +1
    ASSERT_EQ(response.policy_list().size(), 3);
    ASSERT_TRUE(response.policy_list().Get(0).has_upload_policy());
    ASSERT_EQ(response.policy_list().Get(0).path(), "/");
    ASSERT_FALSE(response.policy_list().Get(1).has_upload_policy());
    ASSERT_FALSE(response.policy_list().Get(2).has_upload_policy());
    LOG(INFO) << "List response.ShortDebugString()="
              << response.ShortDebugString();
  }
}

TEST_F(NameSpaceTest, SetFileLevelPolicyFailed) {
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /.RECYCLE.BIN           [id: kRecycleBinINodeId (kLastReservedINodeId)]
  //  /a                      [id: kLastReservedINodeId + 1]
  //    /b                    [id: kLastReservedINodeId + 2]
  //      /bb                 [id: kLastReservedINodeId + 4]
  //    /c                    [id: kLastReservedINodeId + 3]
  //      /cc                 [id: kLastReservedINodeId + 5]
  //
  ASSERT_TRUE(!ns_->MkDirs("/a/b", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a/c", MakePermission(), true).HasException());
  INode inode;
  ASSERT_EQ(ns_->meta_storage()->GetINode(kRootINodeId, "a", &inode),
            StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "b", &inode),
      StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "c", &inode),
      StatusCode::kOK);
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/b/bb", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 2, "bb", &inode),
        StatusCode::kOK);
  }
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/c/cc", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "cc", &inode),
        StatusCode::kOK);
  }

  // Set
  {
    ::cloudfs::SetDirPolicyRequestProto request;
    request.mutable_replica_policy()->set_distributed(true);
    request.mutable_replica_policy()->add_dc("HL");
    request.mutable_replica_policy()->add_dc("LF");

    SynchronizedRpcClosure done;
    ns_->AsyncSetDirPolicy("/a/c/cc", &request, &done);
    done.Await();
    ASSERT_TRUE(done.status().HasException()) << done.status().ToString();
    LOG(INFO) << "done.status()=" << done.status().ToString();
  }

  {
    ::cloudfs::GetDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::GetDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->GetDirPolicy("/a/c/cc", &request, &response).IsOK());

    ASSERT_EQ(response.has_replica_policy(), true);
    ASSERT_EQ(response.replica_policy().ShortDebugString(),
              kDefaultReplicaPolicy.ShortDebugString());

    ASSERT_EQ(response.has_read_policy(), true);
    ASSERT_EQ(response.read_policy().ShortDebugString(),
              kDefaultReadPolicy.ShortDebugString());

    ASSERT_EQ(response.has_upload_policy(), true);
    ASSERT_EQ(response.upload_policy().ShortDebugString(),
              kDefaultUploadPolicy.ShortDebugString());
  }

  {
    ::cloudfs::ListDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::ListDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->ListDirPolicy(&request, &response).IsOK());

    // upload add default +1
    ASSERT_EQ(response.policy_list().size(), 1);
    LOG(INFO) << "List response.ShortDebugString()="
              << response.ShortDebugString();
  }
}

TEST_F(NameSpaceTest, SetFileLevelPolicySuccess) {
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /.RECYCLE.BIN           [id: kRecycleBinINodeId (kLastReservedINodeId)]
  //  /a                      [id: kLastReservedINodeId + 1]
  //    /b                    [id: kLastReservedINodeId + 2]
  //      /bb                 [id: kLastReservedINodeId + 4]
  //    /c                    [id: kLastReservedINodeId + 3]
  //      /cc                 [id: kLastReservedINodeId + 5]
  //
  ASSERT_TRUE(!ns_->MkDirs("/a/b", MakePermission(), true).HasException());
  ASSERT_TRUE(!ns_->MkDirs("/a/c", MakePermission(), true).HasException());
  INode inode;
  ASSERT_EQ(ns_->meta_storage()->GetINode(kRootINodeId, "a", &inode),
            StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "b", &inode),
      StatusCode::kOK);
  ASSERT_EQ(
      ns_->meta_storage()->GetINode(kLastReservedINodeId + 1, "c", &inode),
      StatusCode::kOK);
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/b/bb", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 2, "bb", &inode),
        StatusCode::kOK);
  }
  {
    AddBlockResponseProto add_response;
    CreateResponseProto create_response;
    AddFile("/a/c/cc", 100, 1, true, &add_response, &create_response);
    ASSERT_EQ(
        ns_->meta_storage()->GetINode(kLastReservedINodeId + 3, "cc", &inode),
        StatusCode::kOK);
  }

  // Set
  {
    ::cloudfs::SetDirPolicyRequestProto request;
    request.mutable_upload_policy()->set_upload_interval_ms(-1);

    SynchronizedRpcClosure done;
    ns_->AsyncSetDirPolicy("/a/c/cc", &request, &done);
    done.Await();
    ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    LOG(INFO) << "done.status()=" << done.status().ToString();
  }

  {
    ::cloudfs::GetDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::GetDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->GetDirPolicy("/a/c/cc", &request, &response).IsOK());

    ASSERT_EQ(response.has_replica_policy(), true);
    ASSERT_EQ(response.replica_policy().ShortDebugString(),
              kDefaultReplicaPolicy.ShortDebugString());

    ASSERT_EQ(response.has_read_policy(), true);
    ASSERT_EQ(response.read_policy().ShortDebugString(),
              kDefaultReadPolicy.ShortDebugString());

    ASSERT_EQ(response.has_upload_policy(), true);
    ASSERT_EQ(response.upload_policy().upload_interval_ms(), -1);
  }

  {
    ::cloudfs::ListDirPolicyRequestProto request;
    request.set_need_replica_policy(true);
    request.set_need_read_policy(true);
    request.set_need_upload_policy(true);
    ::cloudfs::ListDirPolicyResponseProto response;
    ASSERT_TRUE(ns_->ListDirPolicy(&request, &response).IsOK());

    // upload add default +1
    ASSERT_EQ(response.policy_list().size(), 1);
    LOG(INFO) << "List response.ShortDebugString()="
              << response.ShortDebugString();
  }
}

TEST_F(NameSpaceTest, DancennAdminHandlerSafeMode) {
  auto handler = std::make_unique<DancennAdminHandler>(
      nullptr, std::static_pointer_cast<NameSpace>(ns_), nullptr, nullptr);
  // set
  {
    cnetpp::http::HttpRequest request;
    auto url = "/admin?cmd=leave_safemode";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);
    ASSERT_EQ(safemode_->IsOn(), false);
  }

  {
    cnetpp::http::HttpRequest request;
    auto url = "/admin?cmd=enter_safemode";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);
    ASSERT_EQ(safemode_->IsOn(), true);
    ASSERT_EQ(safemode_->IsManual(), true);
  }

  {
    cnetpp::http::HttpRequest request;
    auto url = "/admin?cmd=leave_safemode";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);
    ASSERT_EQ(safemode_->IsOn(), false);
  }
}

TEST_F(NameSpaceTest, ConcatTest) {
  std::string test_dir = "/concat_dir";
  PermissionStatus p;
  ASSERT_TRUE(!ns_->MkDirs(test_dir, p, true).HasException());
  std::string target_file = test_dir + "/target";

  std::vector<uint64_t> block_ids;

  {
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    AddBlockResponseProto add_response;
    AddFile(target_file, 100, 1, true, &add_response, &create_response);
    block_ids.push_back(add_response.block().b().blockid());
  }

  std::vector<std::string> part_files;

  for (int i = 0; i < 10; i++) {
    std::string part_file = test_dir + "/part" + std::to_string(i);
    auto create_request = MakeCreateRequest();
    CreateResponseProto create_response;
    AddBlockResponseProto add_response;
    AddFile(part_file, 100, 1, true, &add_response, &create_response);
    block_ids.push_back(add_response.block().b().blockid());
    part_files.push_back(part_file);
  }

  auto status = ns_->Concat(target_file, part_files, LogRpcInfo());
  LOG(INFO) << status.ToString();
  CHECK(!status.HasException());

  // fsck
  auto handler = std::make_unique<DancennFsckHandler>(
      std::static_pointer_cast<NameSpace>(ns_), block_manager_);
  {
    cnetpp::http::HttpRequest request;
    auto url = "/fsck?detail=1&path=" + target_file;
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
  }

  // get & check
  GetBlockLocationsRequestProto get_request;
  get_request.set_src(target_file);
  get_request.set_offset(0);
  get_request.set_length(10000);
  GetBlockLocationsResponseProto get_response;
  cnetpp::base::IPAddress client_ip("***********");
  ns_->GetBlockLocation(target_file,
                        NetworkLocationInfo(client_ip),
                        get_request,
                        &get_response,
                        ugi_);

  auto located_blocks = get_response.locations();
  ASSERT_FALSE(located_blocks.underconstruction());
  ASSERT_EQ(located_blocks.blocks_size(), 11);
  for (int i = 0; i < 11; i++) {
    auto bid = located_blocks.blocks(i).b().blockid();
    CHECK_EQ(bid, block_ids[i]);
  }
}

TEST_F(NameSpaceTest, Load) {
  std::string path = "/load";
  std::string ufs_key = "load";
  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(path, 100, 1, true, &add_response, &create_response);

  LoadResponseProto resp;
  {
    LoadRequestProto req;
    req.set_src(path);
    req.set_data(true);
    req.set_replicanum(0);
    SynchronizedRpcClosure done;
    ns_->AsyncLoadData(path, &req, &resp, &done, &ufs_key);
    done.Await();
    // Replica is 0
    ASSERT_TRUE(done.status().HasException()) << done.status().ToString();

    // Replica excced 255
    req.set_replicanum(256);
    SynchronizedRpcClosure done1;
    ns_->AsyncLoadData(path, &req, &resp, &done1, &ufs_key);
    done1.Await();
    ASSERT_TRUE(done1.status().HasException()) << done1.status().ToString();
  }

  {
    LoadRequestProto req;
    req.set_src(path);
    req.set_data(true);
    req.set_replicanum(1);
    SynchronizedRpcClosure done;
    ns_->AsyncLoadData(path, &req, &resp, &done, &ufs_key);
    done.Await();
    ASSERT_TRUE(done.status().IsOK());
  }
}

TEST_F(NameSpaceTest, DancennAdminHandlerAddBlockId) {
  auto handler = std::make_unique<DancennAdminHandler>(
      nullptr, std::static_pointer_cast<NameSpace>(ns_), nullptr, nullptr);
  auto block_id = edit_log_ctx_->GetLastAllocatedBlockId();
  auto gsv2 = edit_log_ctx_->GetLastGenerationStampV2();
  // set
  {
    cnetpp::http::HttpRequest request;
    auto url = "/admin?cmd=add_block_id_and_gs&delta=7";
    LOG(INFO) << "URL: " << url;
    request.set_uri(url);
    auto response = handler->Handle(request);
    LOG(INFO) << "status: "
              << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                     response.status());
    LOG(INFO) << "response body: " << response.http_body();
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);

    auto new_block_id = edit_log_ctx_->GetLastAllocatedBlockId();
    auto new_gsv2 = edit_log_ctx_->GetLastGenerationStampV2();
    ASSERT_EQ(new_block_id, block_id + 7);
    ASSERT_EQ(new_gsv2, gsv2 + 7);
  }
}

}  // namespace dancenn
