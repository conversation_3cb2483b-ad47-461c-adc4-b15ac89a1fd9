// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/05/25
// Description

#ifndef TEST_NAMESPACE_GMOCK_FILE_FINALIZER_H_
#define SRC_TEST_NAMESPACE_GMOCK_FILE_FINALIZER_H_

#include "base/closure.h"                     // For RpcClosure.
#include "base/status.h"                      // For Status.
#include "block_manager/bip_write_manager.h"  // For BIPWriteManagerBase, etc.
#include "block_manager/block.h"              // For Block, BlockID.
#include "block_manager/block_manager.h"      // For BlockManager.
#include "edit/sender_base.h"                 // For EditLogSenderBase.
#include "lease/lease_manager_base.h"         // For LeaseManagerBase.
#include "namespace/file_finalizer.h"         // For FileFinalizerBase.
#include "namespace/meta_storage.h"           // For MetaStorage.

namespace dancenn {

class GMockFileFinalizer : public FileFinalizerBase {
 public:
  MOCK_METHOD7(Start,
               void(NameSpace* name_space,
                    BlockManager* block_manager,
                    BIPWriteManagerBase* bip_write_manager,
                    LeaseManagerBase* lease_manager,
                    EditLogSenderBase* edit_log_sender,
                    MetaStorage* meta_storage,
                    UfsEnv* ufs_env));
  MOCK_METHOD0(Stop, void());

  MOCK_METHOD8(FinalizeFile,
               Status(const std::string& src,
                      const Block& last_committed_blk,
                      BlockID abandoned_blk_id,
                      INodeInPath* iip,
                      const INode& old_inode,
                      const std::vector<INode>& ancestors,
                      RpcClosure* rpc_done,
                      std::shared_ptr<StopWatchContext> rpc_sw_ctx));
};

}  // namespace dancenn

#endif  // SRC_TEST_NAMESPACE_GMOCK_FILE_FINALIZER_H_
