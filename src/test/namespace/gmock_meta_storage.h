// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/17
// Description

#ifndef TEST_NAMESPACE_GMOCK_META_STORAGE_H_
#define TEST_NAMESPACE_GMOCK_META_STORAGE_H_

#include <gmock/gmock.h>
#include <proto/generated/dancenn/inode.pb.h>
#include <rocksdb/db.h>

#include <cstdint>
#include <functional>
#include <memory>
#include <string>

#include "base/closure.h"
#include "base/status.h"
#include "namespace/inode.h"
#include "namespace/meta_storage.h"

#define GMOCK_METHOD12_(tn, constness, ct, Method, ...)                      \
  static_assert(                                                             \
      12 == ::testing::internal::Function<__VA_ARGS__>::ArgumentCount,       \
      "MOCK_METHOD<N> must match argument count.");                          \
  GMOCK_RESULT_(tn, __VA_ARGS__)                                             \
  ct Method(GMOCK_ARG_(tn, 1, __VA_ARGS__) gmock_a1,                         \
            GMOCK_ARG_(tn, 2, __VA_ARGS__) gmock_a2,                         \
            GMOCK_ARG_(tn, 3, __VA_ARGS__) gmock_a3,                         \
            GMOCK_ARG_(tn, 4, __VA_ARGS__) gmock_a4,                         \
            GMOCK_ARG_(tn, 5, __VA_ARGS__) gmock_a5,                         \
            GMOCK_ARG_(tn, 6, __VA_ARGS__) gmock_a6,                         \
            GMOCK_ARG_(tn, 7, __VA_ARGS__) gmock_a7,                         \
            GMOCK_ARG_(tn, 8, __VA_ARGS__) gmock_a8,                         \
            GMOCK_ARG_(tn, 9, __VA_ARGS__) gmock_a9,                         \
            GMOCK_ARG_(tn, 10, __VA_ARGS__) gmock_a10,                       \
            GMOCK_ARG_(tn, 11, __VA_ARGS__) gmock_a11,                       \
            GMOCK_ARG_(tn, 12, __VA_ARGS__) gmock_a12) constness {           \
    GMOCK_MOCKER_(12, constness, Method).SetOwnerAndName(this, #Method);     \
    return GMOCK_MOCKER_(12, constness, Method)                              \
        .Invoke(::std::forward<GMOCK_ARG_(tn, 1, __VA_ARGS__)>(gmock_a1),    \
                ::std::forward<GMOCK_ARG_(tn, 2, __VA_ARGS__)>(gmock_a2),    \
                ::std::forward<GMOCK_ARG_(tn, 3, __VA_ARGS__)>(gmock_a3),    \
                ::std::forward<GMOCK_ARG_(tn, 4, __VA_ARGS__)>(gmock_a4),    \
                ::std::forward<GMOCK_ARG_(tn, 5, __VA_ARGS__)>(gmock_a5),    \
                ::std::forward<GMOCK_ARG_(tn, 6, __VA_ARGS__)>(gmock_a6),    \
                ::std::forward<GMOCK_ARG_(tn, 7, __VA_ARGS__)>(gmock_a7),    \
                ::std::forward<GMOCK_ARG_(tn, 8, __VA_ARGS__)>(gmock_a8),    \
                ::std::forward<GMOCK_ARG_(tn, 9, __VA_ARGS__)>(gmock_a9),    \
                ::std::forward<GMOCK_ARG_(tn, 10, __VA_ARGS__)>(gmock_a10),  \
                ::std::forward<GMOCK_ARG_(tn, 11, __VA_ARGS__)>(gmock_a11),  \
                ::std::forward<GMOCK_ARG_(tn, 12, __VA_ARGS__)>(gmock_a12)); \
  }                                                                          \
  ::testing::MockSpec<__VA_ARGS__> gmock_##Method(                           \
      GMOCK_MATCHER_(tn, 1, __VA_ARGS__) gmock_a1,                           \
      GMOCK_MATCHER_(tn, 2, __VA_ARGS__) gmock_a2,                           \
      GMOCK_MATCHER_(tn, 3, __VA_ARGS__) gmock_a3,                           \
      GMOCK_MATCHER_(tn, 4, __VA_ARGS__) gmock_a4,                           \
      GMOCK_MATCHER_(tn, 5, __VA_ARGS__) gmock_a5,                           \
      GMOCK_MATCHER_(tn, 6, __VA_ARGS__) gmock_a6,                           \
      GMOCK_MATCHER_(tn, 7, __VA_ARGS__) gmock_a7,                           \
      GMOCK_MATCHER_(tn, 8, __VA_ARGS__) gmock_a8,                           \
      GMOCK_MATCHER_(tn, 9, __VA_ARGS__) gmock_a9,                           \
      GMOCK_MATCHER_(tn, 10, __VA_ARGS__) gmock_a10,                         \
      GMOCK_MATCHER_(tn, 11, __VA_ARGS__) gmock_a11,                         \
      GMOCK_MATCHER_(tn, 12, __VA_ARGS__) gmock_a12) constness {             \
    GMOCK_MOCKER_(12, constness, Method).RegisterOwner(this);                \
    return GMOCK_MOCKER_(12, constness, Method)                              \
        .With(gmock_a1,                                                      \
              gmock_a2,                                                      \
              gmock_a3,                                                      \
              gmock_a4,                                                      \
              gmock_a5,                                                      \
              gmock_a6,                                                      \
              gmock_a7,                                                      \
              gmock_a8,                                                      \
              gmock_a9,                                                      \
              gmock_a10,                                                     \
              gmock_a11,                                                     \
              gmock_a12);                                                    \
  }                                                                          \
  ::testing::MockSpec<__VA_ARGS__> gmock_##Method(                           \
      const ::testing::internal::WithoutMatchers&,                           \
      constness ::testing::internal::Function<__VA_ARGS__>*) const {         \
    return ::testing::internal::AdjustConstness_##constness(this)            \
        ->gmock_##Method(::testing::A<GMOCK_ARG_(tn, 1, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 2, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 3, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 4, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 5, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 6, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 7, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 8, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 9, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 10, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 11, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 12, __VA_ARGS__)>());   \
  }                                                                          \
  mutable ::testing::FunctionMocker<__VA_ARGS__> GMOCK_MOCKER_(              \
      12, constness, Method)

#define MOCK_METHOD12(m, ...) GMOCK_METHOD12_(, , , m, __VA_ARGS__)

namespace dancenn {

extern rocksdb::ColumnFamilyHandle* kINodeDefaultCFHandle;
extern rocksdb::ColumnFamilyHandle* kINodePendingDeleteCFHandle;
extern rocksdb::ColumnFamilyHandle* kNameSystemInfoCFHandle;
extern rocksdb::ColumnFamilyHandle* kAccessCounterCFHandle;
extern rocksdb::ColumnFamilyHandle* kINodeIndexCFHandle;
extern rocksdb::ColumnFamilyHandle* kLegacyBlockPufsInfoCFHandle;
extern rocksdb::ColumnFamilyHandle* kLegacyDeprecatedBlockPufsInfoCFHandle;
extern rocksdb::ColumnFamilyHandle* kLegacyBlockInfoProtoCFHandle;
extern rocksdb::ColumnFamilyHandle* kBlockInfoProtoCFHandle;
extern rocksdb::ColumnFamilyHandle* kLocalBlockCFHandle;
extern rocksdb::ColumnFamilyHandle* kDeprecatingBlockCFHandle;
extern rocksdb::ColumnFamilyHandle* kDeprecatedBlockCFHandle;
extern rocksdb::ColumnFamilyHandle* kINodeStatCFHandle;
extern rocksdb::ColumnFamilyHandle* kLeaseCFHandle;
extern const uint32_t kMaxCFIndex;

class GMockWriteBatch : public rocksdb::WriteBatch {
 public:
  MOCK_METHOD3(Put,
               rocksdb::Status(rocksdb::ColumnFamilyHandle* column_family,
                               const rocksdb::Slice& key,
                               const rocksdb::Slice& value));
  MOCK_METHOD2(Delete,
               rocksdb::Status(rocksdb::ColumnFamilyHandle* column_family,
                               const rocksdb::Slice& key));
};

class GMockMetaStorage : public MetaStorage {
 public:
  GMockMetaStorage() : MetaStorage("gmock_meta_storage_db_path") {
    for (uint32_t i = 0; i < kMaxCFIndex; i++) {
      handles_.emplace_back(reinterpret_cast<rocksdb::ColumnFamilyHandle*>(i));
    }
  }
  virtual ~GMockMetaStorage() {
    handles_.clear();
  }

  MOCK_METHOD0(GetRootINode, INode());
  MOCK_METHOD3(GetINode,
               StatusCode(uint64_t id, INode* inode, MetaStorageIterPtr iter));
  MOCK_METHOD4(GetINode,
               StatusCode(uint64_t parent_id,
                          const std::string& name,
                          INode* inode,
                          MetaStorageIterPtr iter));
  MOCK_METHOD2(GetBlockInfo, bool(BlockID block_id, BlockInfoProto* bip));
  MOCK_METHOD8(OrderedCloseFile,
               void(INode* minode,
                    const BlockInfoProto* last_bip,
                    BlockID last_blk_to_be_abandoned,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));
  MOCK_METHOD6(OrderedUpdateINodeAndDeleteLastBlock,
               void(const INode& inode,
                    BlockID last_blk_to_del,
                    const INode* old_inode,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));
  MOCK_METHOD6(OrderedUpdateINode,
               void(INode* minode,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));
  MOCK_METHOD12(OrderedCommitINodes,
                void(std::vector<INode*>* inodes_add,
                     std::vector<INodeAndSnapshot>* inodes_mod,
                     std::vector<INodeAndSnapshot>* inodes_mov_src,
                     std::vector<INode*>* inodes_mov_dst,
                     std::vector<INodeAndSnapshot>* inodes_del,
                     std::vector<INodeAndSnapshot>* parents,
                     std::vector<INode>* inodes_old,
                     const std::vector<BlockInfoProto>& bips_add,
                     const std::vector<BlockInfoProto>& bips_mod,
                     int64_t txid,
                     const std::vector<Closure*>& dones,
                     INodeStatChangeRecorder* recorder));
  MOCK_METHOD7(OrderedCommitLastBlock,
               void(INode* minode,
                    const BlockInfoProto& last_bip_tbuc,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));
  MOCK_METHOD4(OrderedPutNameSystemInfo,
               void(const std::string& key,
                    cnetpp::base::StringPiece value,
                    int64_t txid,
                    Closure* done));
  MOCK_METHOD2(GetLeaseHolder,
               std::string(INodeID inode_id, MetaStorageSnapPtr snapshot));
  MOCK_METHOD3(FlushBlockInfoProtos,
               void(const BlockInfoProtos& bips, int64_t txid, Closure* done));
  MOCK_METHOD0(CreateWriteBatch, std::unique_ptr<rocksdb::WriteBatch>());
  MOCK_METHOD1(ExistSnapshotINode, bool(INodeID inode_id));
  MOCK_METHOD1(ScanPendingDeleteCF,
               void(const std::function<bool(const INode&)>& callback));
  MOCK_METHOD4(
      ForEachDir,
      bool(uint64_t dir_id,
           const std::string& dir_name,
           uint64_t* cache_inodes,
           const std::function<bool(const std::string&, const INode&)>& cb));
  MOCK_METHOD1(GetLastCkptTxId, uint64_t(MetaStorageIterPtr iter));
  MOCK_METHOD0(Sync, void());
  MOCK_METHOD0(GetIterator, MetaStorageIterHolderPtr());
  MOCK_METHOD0(GetSnapshot, MetaStorageSnapHolderPtr());
  MOCK_METHOD3(GetIterator,
               MetaStorageIterHolderPtr(MetaStorageSnapPtr snapshot,
                                        INodeID parent_id,
                                        const std::string& name));
  MOCK_METHOD3(
      GetLifecyclePolicy,
      Status(const INodeID id, uint64_t* ts, LifecyclePolicyProto* policy));
  MOCK_METHOD2(PutNameSystemInfo,
               void(cnetpp::base::StringPiece key,
                    cnetpp::base::StringPiece value));
  MOCK_METHOD3(GetNameSystemInfo,
               bool(cnetpp::base::StringPiece key,
                    std::string* value,
                    MetaStorageIterPtr iter));
  MOCK_METHOD1(DeleteNameSystemInfo,
               void(cnetpp::base::StringPiece key));
  MOCK_METHOD1(CreateCheckpoint,
               Status(const std::string& path));
  MOCK_METHOD3(CreateCheckpoint,
               void(const std::string& path,
                    int64_t txid,
                    RpcClosure* done));
  MOCK_METHOD3(OrderedPutBlockId,
               void(const uint64_t block_id,
                    int64_t txid,
                    Closure* done));
  MOCK_METHOD3(OrderedPutGenerationStampV2,
               void(const uint64_t gs_v2,
                    int64_t txid,
                    Closure* done));

  MOCK_METHOD5(PushINodeTXWriteTasks,
               void(std::unique_ptr<rocksdb::WriteBatch> wb,
                    int64_t txid,
                    const NameSpaceInfoDelta& delta,
                    std::unique_ptr<KVVerifyVec> verify_kvs,
                    const std::vector<Closure*>& dones));
  MOCK_METHOD5(PushINodeTXWriteTask,
               void(std::unique_ptr<rocksdb::WriteBatch> wb,
                    int64_t txid,
                    const NameSpaceInfoDelta& delta,
                    std::unique_ptr<KVVerifyVec> verify_kvs,
                    Closure* done));
  MOCK_METHOD2(TxFinish, void(int64_t start_txid, int n));
  MOCK_METHOD1(WaitNoPending, void(bool include_bg));
  MOCK_METHOD3(
      UpdateStorageClassReportsAsync,
      void(const std::string& dn_uuid,
           std::vector<std::pair<BlockID, StorageClassReportProto>>& replica_report,
           Closure* done));
  MOCK_METHOD8(PinINode,
               void(INode* minode,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    const JobInfoOpBody& job,
                    const ManagedJobId& cancel_job_id,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));
  MOCK_METHOD9(ReconcileINodeAttrs,
               void(INode* minode,
                    const INode* old_inode,
                    SnapshotLog& snaplog,
                    const std::vector<ManagedJobId>& cancel_job_id,
                    const std::set<int64_t>& expired_ttl,
                    const std::set<int64_t>& new_ttl,
                    int64_t txid,
                    Closure* done,
                    INodeStatChangeRecorder* recorder));

  MOCK_METHOD8(OrderedAddBlock,
               void(INode*,
                    const BlockInfoProto*,
                    const BlockInfoProto&,
                    const INode*,
                    SnapshotLog&,
                    int64_t,
                    Closure*,
                    INodeStatChangeRecorder*));
  MOCK_METHOD10(OrderedCommitINodes,
                void(std::vector<INode>*,
                     std::vector<std::pair<INode, INode>>*,
                     std::vector<INode>*,
                     std::vector<INode>*,
                     std::vector<INode>*,
                     const std::vector<BlockInfoProto>&,
                     const std::vector<BlockInfoProto>&,
                     int64_t,
                     const std::vector<Closure*>&,
                     INodeStatChangeRecorder*));

  static std::string EncodeINodeID(INodeID inode_id);
  static std::string EncodeBlockID(BlockID blk_id);
};

}  // namespace dancenn

#endif  // TEST_NAMESPACE_GMOCK_META_STORAGE_H_
