#pragma once

#include <glog/logging.h>
#include <gtest/gtest.h>

#include <memory>

#include "base/file_utils.h"
#include "edit/deserializer.h"
#include "edit/edit_log_context.h"
#include "edit/sender.h"
#include "edit/tailer.h"
#include "namespace/meta_storage_util.h"
#include "test/dancenn_test_base.h"
#include "test/namespace/namespace_test_base.h"

DECLARE_bool(enable_fast_block_id_and_gs_gen);
DECLARE_string(java_classpath);
DECLARE_int32(java_heap_size_mb);

namespace dancenn {

class MockLogSender : public EditLogSender {
 public:
  MockLogSender(int64_t last_txid, std::shared_ptr<EditLogContextBase> ctx)
      : EditLogSender(last_txid, std::move(ctx)) {
  }
  ~MockLogSender() override = default;
};

class MockLogListener : public IEditLogSyncListener {
 public:
  MockLogListener(
      int64_t last_txid,
      std::function<void(int64_t txid, const std::stringstream* ss)>&& log_sink)
      : last_txid_(last_txid), log_sink_(log_sink) {
  }
  ~MockLogListener() = default;

  void SetupListener(std::shared_ptr<IEditLogSyncListener> listener) {
    listener_ = std::move(listener);
  }

  void TxFinish(int64_t start_txid, int n) override {
    VLOG(10) << "TxFinish, start_txid:" << start_txid << ", n:" << n;
    listener_->TxFinish(start_txid, n);
  }

  int64_t PushLog(const std::stringstream* ss) {
    ++last_txid_;
    log_sink_(last_txid_, ss);
    return last_txid_;
  }

 private:
  std::atomic<int64_t> last_txid_;
  std::function<void(int64_t txid, const std::stringstream* ss)> log_sink_;
  std::shared_ptr<IEditLogSyncListener> listener_;
};

class MockLogContext : public EditLogContextBase {
 public:
  MockLogContext() = default;
  ~MockLogContext() override = default;

  void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) override {
    CHECK(listener != nullptr);
    listener_ = listener;
  }

  std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() override {
    return listener_;
  }

  void OpenForRead() override {
  }
  int64_t OpenForWrite() override {
    return -1;
  }

  bool IsOpenForWrite() override {
    return true;
  }
  bool IsOpenForRead() override {
    return true;
  }

  bool IsActiveInLease() const override {
    return true;
  }

  EditLogConf::HAMode GetHAMode() override {
    return EditLogConf::HA;
  }

  bool UpdateConfProperty(const std::string& name,
                          const std::string& value) {
    return true;
  }

  int64_t GetWaitSyncTime() override {
    return -1;
  }

  void LogSync(bool force = false) override {
  }
  void LogSyncAll() override {
  }
  void Close() override {
  }
  void InitJournalsForWrite() override {
  }
  void SetNextTxId(int64_t) override {
  }
  void SetLastAllocatedBlockId(int64_t id) override {
  }
  void SetLastGenerationStampV2(int64_t gsv2) override {
  }

  uint64_t GetLastAllocatedBlockId() override {
    return 0;
  }
  uint64_t GetLastGenerationStampV2() override {
    return 0;
  }
  int64_t GetCurSegmentTxId() override {
    return 0;
  }
  int64_t GetLastWrittenTxId() override {
    return 0;
  }
  int64_t RollEditLog() override {
    return 0;
  }
  void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) override {
  }
  bool GetPeerNNAddr(std::string* addr) override {
    return 0;
  }

  bool GetAllStackTraces(std::string* stack_info) override {
    return 0;
  }

  std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid,
      int64_t to_at_least_txid,
      bool is_progress_ok) override {
    return nullptr;
  }

  EditLogConf::PreviousEditLogConf HASwitchFence() override {
    return EditLogConf::PreviousEditLogConf();
  }

  int64_t LogOpenFile(const std::stringstream* ss,
                      bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogCloseFile(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogAddBlock(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogUpdateBlocks(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogMkDir(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogRenameOld(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogRename(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogSetReplication(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetStoragePolicy(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetReplicaPolicy(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetDirReplicaPolicy(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetQuota(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetPermissions(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetOwner(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogConcat(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogDelete(const std::stringstream* ss,
                    bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogGenerationStampV1(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogGenerationStampV2(const std::stringstream* ss,
                               uint64_t* gsv2) override {
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      *gsv2 = ++gs_;

      return kInvalidTxId;
    }

    CHECK(listener_);
    std::unique_lock<std::mutex> lock(mutex_);
    auto& sink = dynamic_cast<MockLogListener&>(*listener_);
    int64_t txid = sink.PushLog(ss);
    sink.TxFinish(txid, 1);
    *gsv2 = ++gs_;
    return txid;
  }
  int64_t LogAllocateBlockId(const std::stringstream* ss,
                             uint64_t* id) override {
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      *id = ++block_id_;

      return kInvalidTxId;
    }

    CHECK(listener_);
    std::unique_lock<std::mutex> lock(mutex_);
    auto& sink = dynamic_cast<MockLogListener&>(*listener_);
    int64_t txid = sink.PushLog(ss);
    sink.TxFinish(txid, 1);
    *id = ++block_id_;
    return txid;
  }
  int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                    const std::stringstream* gsv2_ss,
                                    uint64_t* blkid,
                                    uint64_t* gsv2) override {
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      *blkid = ++block_id_;
      *gsv2 = ++gs_;

      return kInvalidTxId;
    }

    CHECK(listener_);
    std::unique_lock<std::mutex> lock(mutex_);
    auto& sink = dynamic_cast<MockLogListener&>(*listener_);
    int64_t txid = sink.PushLog(blkid_ss);
    txid = sink.PushLog(gsv2_ss);
    sink.TxFinish(txid - 1, 2);
    *blkid = ++block_id_;
    *gsv2 = ++gs_;
    return txid;
  }
  int64_t LogTimes(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSymlink(const std::stringstream* ss,
                     bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogReassignLease(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetAcl(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetXAttrs(const std::stringstream* ss,
                       bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogRemoveXAttrs(const std::stringstream* ss,
                          bool to_log_rpc_ids = false) override {
    return LogImpl(ss);
  }
  int64_t LogAccessCounterSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogSetBlockPufsInfo(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogDeleteDeprecatedBlockPufsInfo(
      const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogAllowSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogDisallowSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogCreateSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogDeleteSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogRenameSnapshot(const std::stringstream* ss) override {
    return LogImpl(ss);
  }
  int64_t LogCfsOp(const std::stringstream* ss) override {
    return LogImpl(ss);
  }

  Status SwitchNonHAActiveToHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAActiveToNonHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAStandbyToNonHAActive() override {
    return Status(Code::kError);
  }

 private:
  int64_t LogImpl(const std::stringstream* ss) {
    CHECK(listener_);
    std::unique_lock<std::mutex> lock(mutex_);
    auto& sink = dynamic_cast<MockLogListener&>(*listener_);
    int64_t txid = sink.PushLog(ss);
    sink.TxFinish(txid, 1);
    return txid;
  }

  std::mutex mutex_;
  std::atomic<uint64_t> gs_{0};
  std::atomic<uint64_t> block_id_{0};
  std::shared_ptr<IEditLogSyncListener> listener_;
};

class MockEditLogTailer : public EditLogTailer {
 public:
  MockEditLogTailer(
      std::shared_ptr<EditLogContextBase> edit_ctx,
      std::shared_ptr<NameSpace> ns)
    : EditLogTailer(0, nullptr, edit_ctx, ns.get()),
      ops_cnt_(0),
      sync_delay_interval_(0),
      sync_delay_latency_(0) {
  }

  void PushOp(std::string serialized_op) {
    std::unique_lock<std::mutex> lock_guard(ops_mtx_);
    ops_queue_.push(serialized_op);
  }

  bool PopOp(std::string* serialized_op) {
    std::unique_lock<std::mutex> lock_guard(ops_mtx_);
    if (ops_queue_.empty()) {
      return false;
    }
    *serialized_op = ops_queue_.front();
    ops_queue_.pop();

    // XXX simulate txid gap
    if (sync_delay_interval_ > 0) {
      if (ops_cnt_++ % sync_delay_interval_ == 0) {
        std::this_thread::sleep_for(
            std::chrono::milliseconds(sync_delay_latency_));
      }
    }

    return true;
  }

  void DoTail(bool is_inprogress_ok) {
    // XXX simplified implementation of Tailer::DoTail()
    while (is_inprogress_ok || should_run_ || !EditlogAllConsumed()) {
      std::string serialized_op;
      bool got = PopOp(&serialized_op);
      if (!got) {
        break;
      }
      if (serialized_op.length() == 0) {
        // eof
        ns_->TailerCatchupTxid();
        break;
      }
      Apply(serialized_op);
    }
  }

  bool EditlogAllConsumed() {
    if (!ops_queue_.empty()) {
      return false;
    }

    if (apply_assigner_thread_->PendingCount() > 0 ||
        apply_assigner_thread_->NumRunningTasks()) {
      return false;
    }

    return true;
  }

  void Run() override {
    // XXX simplified implementation of Tailer::Run()
    // make sure all editlog are consumed
    while (should_run_ || !EditlogAllConsumed()) {
      DoTail(true);
      {
        std::unique_lock<std::mutex> lock(cv_mutex_);
        cv_.wait_for(lock,
                     std::chrono::milliseconds(3000),
                     [&]() { return !should_run_; });
      }
    }
    ns_->WaitNoPending();

    uint64_t txid_applied = last_applied_txid_;
    uint64_t txid_ondisk = ns_->GetLastCkptTxId();
    CHECK_EQ(txid_applied, txid_ondisk);
  }

  void CatchupDuringFailover() override {
    // XXX simplified implementation of Tailer::CatchupDuringFailover()
    ns_->TailerCatchupTxid();
    DoTail(true);
  }

  void set_sync_delay_interval(uint64_t interval) {
    sync_delay_interval_ = interval;
  }

  void set_sync_delay_latency(uint64_t latency) {
    sync_delay_latency_ = latency;
  }

 private:
  std::mutex ops_mtx_;
  std::queue<std::string> ops_queue_;
  uint64_t ops_cnt_;
  uint64_t sync_delay_interval_;
  uint64_t sync_delay_latency_;
};

class NnClusterTest : public NameSpaceTestBase {
 public:
  void SetUp() override {
    NameSpaceTestBase::SetUp();
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto listener = std::make_shared<MockLogListener>(
        last_tx_id, [this](int64_t txid, const std::stringstream* ss) {
          VLOG(8) << "Forward log to standby, txid: " << txid;
          OpDeSerializer parser{};
          std::string ser_op = ss->str();
          auto des_op = parser.Deserialize(ser_op);
          des_op->SetTxid(txid);
          VLOG(8) << "Forward log to standby, op: "
                  << des_op->SerializeToJsonString();
          std::stringstream ss2;
          des_op->WriteFields(&ss2);
          standby_tailer_->PushOp(ss2.str());
        });
    listener->SetupListener(edit_log_ctx_->TestOnlyGetSyncListener());
    auto x = std::static_pointer_cast<IEditLogSyncListener>(listener);
    edit_log_ctx_->SetupSyncListener(x);

    auto sender = std::make_unique<MockLogSender>(last_tx_id, edit_log_ctx_);
    ns_->TestOnlySetEditLogSender(std::move(sender));
  }

  void StartStandBy() override {
    ns_->StopCheckpointer();
    ASSERT_NE(mkdtemp(&(standby_path_[0])), nullptr);
    standby_datanode_mgr_ = std::make_shared<DatanodeManager>();
    standby_ha_ = std::make_unique<MockHAState>();
    standby_safe_ = std::make_unique<MockSafeMode>();
    standby_ha_->SetState(cloudfs::HAServiceStateProto::STANDBY);
    standby_edit_ctx_ = CreateContext();

    auto block_manager = std::make_shared<BlockManager>();
    MockFSImageTransfer(standby_path_).Transfer();
    ufs_env_ = UfsEnv::Create();
    standby_ =
        std::make_shared<MockNameSpace>(standby_path_,
                                        standby_edit_ctx_,
                                        block_manager,
                                        standby_datanode_mgr_,
                                        std::make_shared<DataCenters>(),
                                        ufs_env_);
    standby_->set_safemode(standby_safe_.get());
    standby_->set_ha_state(standby_ha_.get());
    block_manager->set_ha_state(standby_ha_.get());
    block_manager->set_safemode(standby_safe_.get());
    block_manager->set_ns(standby_.get());
    standby_datanode_mgr_->set_block_manager(block_manager.get());
    standby_->Start();
    standby_->StopCheckpointer();
    standby_->StartStandby();

    std::string cluster_id;
    CHECK(ns_->meta_storage_ptr()->GetNameSystemInfo(kClusterIdKey, &cluster_id));
    standby_->meta_storage_ptr()->PutNameSystemInfo(kClusterIdKey, cluster_id);
    standby_->LoadClusterId();

    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    cnetpp::base::IPAddress ip("***********");
    standby_datanode_mgr_->Register(reg.datanodeid(), &reg, ip);
    standby_datanode_mgr_->RefreshConfig();

    standby_->StopBGDeletionWorker();
    standby_->StopLeaseMonitor();

    standby_tailer_ = std::make_shared<MockEditLogTailer>(standby_edit_ctx_,
                                                          standby_);
    standby_tailer_->Start();

    db_comptr_ = std::make_unique<DBComparator>(db_path_, standby_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() override {
    auto c = std::make_shared<MockLogContext>();
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  void TearDown() override {
    CHECK(!inode_stat_enabled_);
    // stop active first to avoid MockLogListener calling apply
    NameSpaceTestBase::TearDown();
    if (!transited_) {
      standby_tailer_->Stop();
      standby_->StopStandby();
      standby_->Stop();
      standby_ha_.reset();
      standby_safe_.reset();
      standby_datanode_mgr_.reset();
    }
    standby_.reset();
    FileUtils::DeleteDirectoryRecursively(standby_path_);
  }

  void HeartbeatOnce(const HeartbeatRequestProto& request) override {
    DatanodeManager::RepeatedCmds cmds;
    datanode_manager_->Heartbeat(request, &cmds);
    standby_datanode_mgr_->Heartbeat(request, &cmds);
  }

  void WaitSynced();
  bool CheckDBConsistency();
  void EnableINodeStatConsistency();
  bool CheckINodeStatConsistency();
  void DisableINodeStatConsistency();

 public:
  std::string standby_path_ = "rocksdb2_XXXXXX";

  std::shared_ptr<MockNameSpace> standby_;
  std::shared_ptr<DatanodeManager> standby_datanode_mgr_;
  std::unique_ptr<MockHAState> standby_ha_;
  std::unique_ptr<MockSafeMode> standby_safe_;
  std::shared_ptr<UfsEnv> ufs_env_;
  std::shared_ptr<EditLogContextBase> standby_edit_ctx_;
  std::shared_ptr<MockEditLogTailer> standby_tailer_;
  std::unique_ptr<DBComparator> db_comptr_;

  bool inode_stat_enabled_ = false;
};

}  // namespace dancenn
