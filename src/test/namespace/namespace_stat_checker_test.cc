// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/06/18
// Description

#include <cnetpp/base/string_piece.h>  // For StringPiece.
#include <gtest/gtest.h>               // For testing.
#include <nlohmann/json.hpp>           // For parse.

#include <cstdint>            // For uint8_t, uint64_t.
#include <memory>             // For shared_ptr, make_shared.
#include <string>             // For string.

#include "base/constants.h"   // For kLastCkptTxIdKey.
#include "base/file_utils.h"  // For mkdtemp, FileUtils.
#include "base/platform.h"    // For HostToBigEndian.
#include "namespace/inode.h"  // For INode, MakeINode, PermissionStatus.
#include "namespace/namespace_stat_checker.h"     // For CheckINodeStatOp.
#include "namespace/namespace_scrub_inodestat.h"  // For INodeStat.
#include "namespace/meta_storage_constants.h"     // For MetaStorage CFIndex.

namespace dancenn {

class NameSpaceStatCheckerTest : public testing::Test {
 public:
  void SetUp() override {
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_storage_ = std::make_shared<MetaStorage>(db_path_);
    meta_storage_->Launch();
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    meta_storage_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

 protected:
  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MetaStorage> meta_storage_;
};

TEST_F(NameSpaceStatCheckerTest, Test01) {
  auto id = platform::HostToBigEndian(1234567890UL);
  auto v = cnetpp::base::StringPiece(reinterpret_cast<const uint8_t*>(&id),
                                     sizeof(uint64_t));
  meta_storage_->PutNameSystemInfo(kLastCkptTxIdKey, v);

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(777);
  std::shared_ptr<INode> root(new INode);
  MakeINode(16385, 16385, "", ps, INode::kDirectory, root.get());
  std::shared_ptr<INode> a(new INode);
  MakeINode(16386, 16385, "a", ps, INode::kDirectory, a.get());
  std::shared_ptr<INode> b(new INode);
  MakeINode(16387, 16386, "b", ps, INode::kDirectory, b.get());
  std::shared_ptr<INode> c(new INode);
  MakeINode(16388, 16387, "c", ps, INode::kFile, c.get());
  auto blk = c->add_blocks();
  blk->set_blockid(1648576);
  blk->set_genstamp(1001);
  blk->set_numbytes(1024);
  meta_storage_->InsertINode(root);
  meta_storage_->InsertINode(a);
  meta_storage_->InsertINode(b);
  meta_storage_->InsertINode(c);

  INodeStat root_stat(16385);
  root_stat.inode_num = 3;
  root_stat.file_num = 1;
  root_stat.dir_num = 2;
  root_stat.block_num = 1;
  root_stat.data_size = 1024;
  root_stat.txid = 1;
  root_stat.timestamp_sec = 1655633226;
  meta_storage_->PutDirectoryINodeStat(*root, root_stat);

  INodeStat a_stat(16386);
  a_stat.inode_num = 2;
  a_stat.file_num = 1;
  a_stat.dir_num = 1;
  a_stat.block_num = 1;
  a_stat.data_size = 1024;
  a_stat.txid = 1;
  a_stat.timestamp_sec = 1655633226;
  meta_storage_->PutDirectoryINodeStat(*a, a_stat);

  INodeStat b_stat(16387);
  b_stat.inode_num = 1;
  b_stat.file_num = 1;
  b_stat.dir_num = 0;
  b_stat.block_num = 1;
  b_stat.data_size = 1024;
  b_stat.txid = 1;
  b_stat.timestamp_sec = 1655633226;
  meta_storage_->PutDirectoryINodeStat(*b, b_stat);

  auto snapshot_holder = meta_storage_->GetSnapshot();
  {
    // iterator holder scope, release before snapshot.
    auto snap = snapshot_holder->snapshot();
    auto nsinfo_iter_holder = meta_storage_->GetIterator(snap, kNameSystemInfoCFIndex);
    auto inode_iter_holder = meta_storage_->GetIterator(snap, kINodeDefaultCFIndex);
    auto instat_iter_holder = meta_storage_->GetIterator(snap, kINodeStatCFIndex);
    CheckINodeStatOp op1(meta_storage_,
                         snap,
                         nsinfo_iter_holder->iter(),
                         inode_iter_holder->iter(),
                         instat_iter_holder->iter(),
                         "",
                         true);
    auto s = op1.Check();
    EXPECT_TRUE(s.IsOK());
    using json = nlohmann::json;
    EXPECT_EQ(json::parse(s.message())["inode_id"], 16385);
    EXPECT_EQ(json::parse(op1.GetDebugMsg())["parent_inode"]["id"], 16385);

    s = op1.Check();
    EXPECT_TRUE(s.IsOK());
    using json = nlohmann::json;
    EXPECT_EQ(json::parse(s.message())["inode_id"], 16386);
    EXPECT_EQ(json::parse(op1.GetDebugMsg())["parent_inode"]["id"], 16386);

    CheckINodeStatOp op2(meta_storage_,
                         snap,
                         nsinfo_iter_holder->iter(),
                         inode_iter_holder->iter(),
                         instat_iter_holder->iter(),
                         op1.GetNext(),
                         true);
    s = op2.Check();
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(json::parse(s.message())["inode_id"], 16387);
    EXPECT_EQ(json::parse(op2.GetDebugMsg())["parent_inode"]["id"], 16387);

    s = op2.Check();
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(json::parse(s.message())["inode_id"], 16388);

    s = op2.Check();
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ(s.message(), "end");
    EXPECT_TRUE(op2.GetNext().empty());
  }
}

TEST_F(NameSpaceStatCheckerTest, Test02) {
  auto id = platform::HostToBigEndian(1234567890UL);
  auto v = cnetpp::base::StringPiece(reinterpret_cast<const uint8_t*>(&id),
                                     sizeof(uint64_t));
  meta_storage_->PutNameSystemInfo(kLastCkptTxIdKey, v);

  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  ps.set_permission(777);
  std::shared_ptr<INode> root(new INode);
  MakeINode(16385, 16385, "", ps, INode::kDirectory, root.get());
  meta_storage_->InsertINode(root);

  INodeStat root_stat(16385);
  root_stat.inode_num = 3;
  root_stat.file_num = 1;
  root_stat.dir_num = 2;
  root_stat.block_num = 1;
  root_stat.data_size = 1024;
  root_stat.txid = 1;
  root_stat.timestamp_sec = 1655633226;
  meta_storage_->PutDirectoryINodeStat(*root, root_stat);

  auto snapshot_holder = meta_storage_->GetSnapshot();
  {
    // iterator holder scope, release before snapshot.
    auto snap = snapshot_holder->snapshot();
    auto nsinfo_iter_holder = meta_storage_->GetIterator(snap, kNameSystemInfoCFIndex);
    auto inode_iter_holder = meta_storage_->GetIterator(snap, kINodeDefaultCFIndex);
    auto instat_iter_holder = meta_storage_->GetIterator(snap, kINodeStatCFIndex);
    CheckINodeStatOp op(meta_storage_,
                        snap,
                        nsinfo_iter_holder->iter(),
                        inode_iter_holder->iter(),
                        instat_iter_holder->iter(),
                        "",
                        true);

    auto s = op.Check();
    EXPECT_FALSE(s.IsOK());
    using json = nlohmann::json;
    EXPECT_EQ(json::parse(s.message())["inode_id"], 16385);
    EXPECT_EQ(json::parse(op.GetDebugMsg())["parent_inode"]["id"], 16385);
  }
}

}  // namespace dancenn
