#include "namespace/ttl_atime_collector.h"

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "namespace/meta_storage.h"
#include "namespace/namespace.h"

namespace dancenn {
namespace {

using ::testing::_;
using ::testing::DoAll;
using ::testing::Invoke;
using ::testing::NiceMock;
using ::testing::Return;

// Mock classes
class MockNameSpace : public NameSpace {
 public:
  MOCK_METHOD(void,
              BatchUpdateTtlATimes,
              (const std::vector<TtlATimeRecord>& records, Closure* done));
};

class MockMetaStorage : public MetaStorage {
 public:
  MockMetaStorage() : MetaStorage("/mock/path") {
  }

  MOCK_METHOD(void, UpdateTtlATime, (INodeID inode_id, int64_t access_time));
};

class TtlATimeCollectorTest : public ::testing::Test {
 protected:
  void SetUp() override {
    config_.batch_size = 3;  // 使用较小的批量大小便于测试
    config_.timeout_ms = 100;
    collector_ =
        std::make_unique<TtlATimeCollector>(&mock_ns_, &mock_storage_, config_);
  }

  void TearDown() override {
    collector_->Stop();
    collector_.reset();
  }

  NiceMock<MockNameSpace> mock_ns_;
  NiceMock<MockMetaStorage> mock_storage_;
  TtlATimeCollectorConfig config_;
  std::unique_ptr<TtlATimeCollector> collector_;
};

// 测试基本的添加和获取记录功能
TEST_F(TtlATimeCollectorTest, BasicAddAndGetRecords) {
  collector_->AddAccessRecord(1, 100);
  collector_->AddAccessRecord(2, 200);

  std::vector<TtlATimeRecord> records;
  ASSERT_TRUE(collector_->GetAccessRecords(&records));
  ASSERT_EQ(records.size(), 2);
  EXPECT_EQ(records[0].inode_id, 1);
  EXPECT_EQ(records[0].access_time, 100);
  EXPECT_EQ(records[1].inode_id, 2);
  EXPECT_EQ(records[1].access_time, 200);
}

// 测试批量大小触发自动刷新
TEST_F(TtlATimeCollectorTest, AutoFlushOnBatchSize) {
  bool flush_called = false;
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillOnce(DoAll(Invoke(
          [&](const std::vector<TtlATimeRecord>& records, Closure* done) {
            EXPECT_EQ(records.size(), 3);
            flush_called = true;
            done->Run();
          })));

  collector_->AddAccessRecord(1, 100);
  collector_->AddAccessRecord(2, 200);
  EXPECT_FALSE(flush_called);

  // 添加第三条记录应该触发刷新
  collector_->AddAccessRecord(3, 300);
  EXPECT_TRUE(flush_called);
}

// 测试记录合并功能
TEST_F(TtlATimeCollectorTest, MergeRecords) {
  bool flush_called = false;
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillOnce(DoAll(Invoke([&](const std::vector<TtlATimeRecord>& records,
                                 Closure* done) {
        EXPECT_EQ(records.size(), 3);  // 应该只有2条记录，因为有一条被合并了
        for (const auto& record : records) {
          if (record.inode_id == 1) {
            EXPECT_EQ(record.access_time, 200);  // 应该保留较新的时间戳
          }
        }
        flush_called = true;
        done->Run();
      })));

  collector_->AddAccessRecord(1, 100);
  collector_->AddAccessRecord(1, 200);  // 同一个inode_id，较新的时间戳
  collector_->AddAccessRecord(2, 300);
  collector_->AddAccessRecord(3, 300);  // 触发批量大小刷新

  EXPECT_TRUE(flush_called);
}

// 测试手动刷新功能
TEST_F(TtlATimeCollectorTest, ManualFlush) {
  bool flush_called = false;
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillOnce(DoAll(Invoke(
          [&](const std::vector<TtlATimeRecord>& records, Closure* done) {
            EXPECT_EQ(records.size(), 2);
            flush_called = true;
            done->Run();
          })));

  collector_->AddAccessRecord(1, 100);
  collector_->AddAccessRecord(2, 200);

  collector_->Flush();
  EXPECT_TRUE(flush_called);
}

// 测试空记录情况
TEST_F(TtlATimeCollectorTest, EmptyRecords) {
  std::vector<TtlATimeRecord> records;
  EXPECT_FALSE(collector_->GetAccessRecords(&records));
  EXPECT_EQ(records.size(), 0);

  // 空记录刷新不应该触发实际的BatchUpdateTtlATimes调用
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _)).Times(0);

  collector_->Flush();
}

// 测试超时自动刷新
TEST_F(TtlATimeCollectorTest, TimeoutFlush) {
  bool flush_called = false;
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillOnce(DoAll(Invoke(
          [&](const std::vector<TtlATimeRecord>& records, Closure* done) {
            EXPECT_EQ(records.size(), 1);
            flush_called = true;
            done->Run();
          })));

  collector_->Start();
  collector_->AddAccessRecord(1, 100);

  // 等待超过超时时间
  std::this_thread::sleep_for(std::chrono::milliseconds(150));

  EXPECT_TRUE(flush_called);
}

// 测试大量记录的批处理
TEST_F(TtlATimeCollectorTest, LargeBatchProcessing) {
  const size_t total_records = 1000;
  size_t flush_count = 0;

  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillRepeatedly(DoAll(Invoke(
          [&](const std::vector<TtlATimeRecord>& records, Closure* done) {
            EXPECT_EQ(records.size(), config_.batch_size);  // 应该是满批次
            flush_count++;
            done->Run();
          })));

  // 添加大量记录
  for (size_t i = 0; i < total_records; i++) {
    collector_->AddAccessRecord(i, i * 100);
  }

  // 验证预期的刷新次数
  EXPECT_EQ(flush_count, total_records / config_.batch_size);

  // 验证剩余记录
  std::vector<TtlATimeRecord> remaining;
  ASSERT_TRUE(collector_->GetAccessRecords(&remaining));
  EXPECT_EQ(remaining.size(), total_records % config_.batch_size);
}

// 测试并发添加记录
TEST_F(TtlATimeCollectorTest, ConcurrentAddAccessRecords) {
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .Times(::testing::AtLeast(0))
      .WillRepeatedly(
          DoAll(Invoke([](const std::vector<TtlATimeRecord>& records,
                          Closure* done) { done->Run(); })));

  const int num_threads = 4;
  const int records_per_thread = 100;
  std::vector<std::thread> threads;

  // 创建多个线程并发添加记录
  for (int i = 0; i < num_threads; i++) {
    threads.emplace_back([&, i]() {
      for (int j = 0; j < records_per_thread; j++) {
        collector_->AddAccessRecord(i * records_per_thread + j,
                              static_cast<int64_t>(j));
      }
    });
  }

  // 等待所有线程完成
  for (auto& thread : threads) {
    thread.join();
  }

  // 验证所有记录都被正确处理
  std::vector<TtlATimeRecord> remaining;
  collector_->GetAccessRecords(&remaining);

  // 计算已处理的记录总数（通过mock调用次数）和剩余记录数
  size_t total_processed = remaining.size();
  EXPECT_EQ(total_processed <= num_threads * records_per_thread, true);
}

// 测试定时器触发的自动刷新
TEST_F(TtlATimeCollectorTest, TimerTriggeredFlush) {
  std::atomic<int> flush_count{0};
  EXPECT_CALL(mock_ns_, BatchUpdateTtlATimes(_, _))
      .WillRepeatedly(DoAll(Invoke(
          [&](const std::vector<TtlATimeRecord>& records, Closure* done) {
            flush_count++;
            done->Run();
          })));

  collector_->Start();

  // 添加一些记录，数量小于batch_size以确保不会触发批量刷新
  collector_->AddAccessRecord(1, 100);
  collector_->AddAccessRecord(2, 200);

  // 等待超过配置的超时时间
  std::this_thread::sleep_for(
      std::chrono::milliseconds(config_.timeout_ms + 200));

  // 验证是否发生了至少一次刷新
  EXPECT_GE(flush_count, 1);

  // 再添加一些记录
  collector_->AddAccessRecord(3, 300);
  collector_->AddAccessRecord(4, 400);

  // 再次等待超过超时时间
  std::this_thread::sleep_for(
      std::chrono::milliseconds(config_.timeout_ms + 200));

  // 验证是否发生了额外的刷新
  EXPECT_GE(flush_count, 2);

  collector_->Stop();
}

}  // namespace
}  // namespace dancenn