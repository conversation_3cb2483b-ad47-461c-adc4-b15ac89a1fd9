// Copyright 2019 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef TEST_MOCK_NAMESPACE_H
#define TEST_MOCK_NAMESPACE_H

#include <gflags/gflags.h>
#include <gmock/gmock.h>

#include <memory>
#include <string>
#include <utility>

#include "namespace/meta_storage.h"
#include "namespace/namespace.h"
#include "ufs/tos/tos_cred_keeper.h"

DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_int32(namespace_type);
DECLARE_bool(security_block_access_token_enable);
DECLARE_string(tos_endpoint);
DECLARE_string(tos_region);
DECLARE_string(tos_bucket);
DECLARE_string(tos_prefix);
DECLARE_uint32(job_tracker_metastorage_inspect_period_sec);

namespace dancenn {

class MockFSImageTransfer : public NameSpace {
 public:
  explicit MockFSImageTransfer(const std::string& db_path)
      : NameSpace(nullptr,
                  nullptr,
                  std::shared_ptr<BlockManager>(),
                  std::unique_ptr<BlockReportManager>(),
                  std::shared_ptr<DatanodeManager>(),
                  std::unique_ptr<LeaseManager>(),
                  std::make_unique<LeaseMonitor>(
                      nullptr,
                      [](uint64_t inode_id,
                         const std::string& orig_holder,
                         const std::string& new_holder) { return Status(); }),
                  std::shared_ptr<EditLogContextBase>(),
                  std::shared_ptr<EditLogSenderBase>(),
                  std::shared_ptr<MetaStorage>(),
                  std::unique_ptr<RWLockManager>(),
                  std::shared_ptr<KeyManager>(),
                  std::shared_ptr<DataCenters>(),
                  std::shared_ptr<AccessCounterManager>(),
                  std::unique_ptr<FileFinalizerBase>(),
                  std::shared_ptr<JobManager>()) {
    FLAGS_job_tracker_metastorage_inspect_period_sec = 1;
    meta_storage_ = std::make_shared<MetaStorage>(db_path);
    meta_storage_->Launch();
  }

  ~MockFSImageTransfer() override {
    meta_storage_->Shutdown();
  }

  void Transfer() {
    FLAGS_filesystem_id = 1;
    LocalSaveFileSystemId(FLAGS_filesystem_id);
    FLAGS_namespace_id = 2;
    LocalSaveNameSpaceId(FLAGS_namespace_id);
    LocalSaveNameSpaceType(static_cast<cloudfs::NamespaceType>(
      FLAGS_namespace_type));
    LocalSaveBlockPoolId("BP-964799785-10.8.28.221-1497005578788");
    PersistentUfsInfo persistent_ufs_info;
    persistent_ufs_info.protocol = PersistentUfsProtocol::kTos;
    TosInfo& tos_info = persistent_ufs_info.tos_info;
    FLAGS_tos_endpoint = "endpoint";
    tos_info.endpoint = FLAGS_tos_endpoint;
    FLAGS_tos_region = "region";
    tos_info.region = FLAGS_tos_region;
    FLAGS_tos_bucket = "bucket";
    tos_info.bucket = FLAGS_tos_bucket;
    FLAGS_tos_prefix = "prefix";
    tos_info.prefix = FLAGS_tos_prefix;
    LocalSavePersistentUfsInfo(persistent_ufs_info);
  }
};

class MockNameSpace : public NameSpace {
 public:
  MockNameSpace(const std::string& db_path,
                std::shared_ptr<EditLogContextBase> context,
                std::shared_ptr<BlockManager> block_manager,
                std::shared_ptr<DatanodeManager> datanode_manager,
                std::shared_ptr<DataCenters> data_centers,
                std::shared_ptr<UfsEnv> ufs_env,
                RetryCache* rc = nullptr,
                UserGroupInfo ugi = UserGroupInfo("root", "supergroup"),
                std::shared_ptr<JobManager> job_manager = nullptr)
      : NameSpace(
            db_path,
            context,
            block_manager,
            datanode_manager,
            job_manager == nullptr
                ? std::make_shared<JobManager>(datanode_manager, block_manager)
                : job_manager,
            data_centers,
            ufs_env,
            rc),
        ugi_(ugi) {
    FLAGS_job_tracker_metastorage_inspect_period_sec = 1;
    block_manager->set_job_manager(job_manager_);
    blockpool_id_ = "BP-964799785-10.8.28.221-1497005578788";
    key_manager_ =
        dancenn::KeyManagerFactory::Create("local", datanode_manager);
    key_manager_->Start();
    block_token_secret_manager_ =
        std::make_shared<BlockTokenSecretManager>(blockpool_id_, key_manager_);
  }

  ~MockNameSpace() override {
    Stop();
    StopApplyThread();
    key_manager_->Stop();
  }

  static std::shared_ptr<TosCredKeeper> CreateDummyCredKeeper();

  Status CreateFile(const std::string& src,
                    const PermissionStatus& permission,
                    const CreateRequestProto& request,
                    CreateResponseProto* response,
                    LogRpcInfo rpc_info = LogRpcInfo()) {
    SynchronizedRpcClosure rpc_done;
    AsyncCreateFile(src,
                    permission,
                    NetworkLocationInfo(),
                    ugi_,
                    rpc_info,
                    /*client_machine*/ "",
                    &request,
                    response,
                    nullptr,
                    &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status MkDirs(const std::string& path,
                const PermissionStatus& permission,
                bool create_parent) {
    SynchronizedRpcClosure rpc_done;
    AsyncMkDirs(path,
                permission,
                ugi_,
                create_parent,
                false,
                nullptr,
                LogRpcInfo(),
                &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status RenameTo(const std::string& src, const std::string& dst) {
    SynchronizedRpcClosure rpc_done;
    AsyncRenameTo(src, dst, LogRpcInfo(), ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status RenameTo2(const std::string& src,
                   const std::string& dst,
                   bool overwrite) {
    SynchronizedRpcClosure rpc_done;
    AsyncRenameTo2(src, dst, overwrite, LogRpcInfo(), ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status Delete(const std::string& src,
                bool recursive,
                const UserGroupInfo& ugi) {
    SynchronizedRpcClosure rpc_done;
    AsyncDelete(src, recursive, ugi, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status Delete(const std::string& src, bool recursive) {
    SynchronizedRpcClosure rpc_done;
    AsyncDelete(src, recursive, ugi_, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status Concat(const std::string& target,
                const std::vector<std::string>& srcs,
                const LogRpcInfo& rpc_info) {
    SynchronizedRpcClosure rpc_done;
    AsyncConcat(target, srcs, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status AddBlock(const std::string& path,
                  const cnetpp::base::IPAddress& writer,
                  const LogRpcInfo& rpc_info,
                  const AddBlockRequestProto& request,
                  AddBlockResponseProto* response) {
    SynchronizedRpcClosure rpc_done;
    AsyncAddBlock(path,
                  NetworkLocationInfo(writer),
                  rpc_info,
                  ugi_,
                  &request,
                  response,
                  nullptr,
                  &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status UpdateBlockForPipeline(const UpdateBlockForPipelineRequestProto& req,
                                const LogRpcInfo& rpc_info,
                                UpdateBlockForPipelineResponseProto* response) {
    SynchronizedRpcClosure rpc_done;
    AsyncUpdateBlockForPipeline(&req, rpc_info, response, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status UpdatePipeline(const UpdatePipelineRequestProto& request,
                        const LogRpcInfo& rpc_info) {
    SynchronizedRpcClosure rpc_done;
    AsyncUpdatePipeline(&request, rpc_info, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status AbandonBlock(const std::string& path,
                      const AbandonBlockRequestProto& request) {
    SynchronizedRpcClosure rpc_done;
    AsyncAbandonBlock(path, &request, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status CompleteFile(const std::string& path,
                      const CompleteRequestProto& request) {
    SynchronizedRpcClosure rpc_done;
    AsyncCompleteFile(path, &request, nullptr, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status Append(const std::string& path,
                const std::string& client_machine,
                const LogRpcInfo& rpc_info,
                const AppendRequestProto& request,
                AppendResponseProto* response) {
    SynchronizedRpcClosure rpc_done;
    AsyncAppend(
        path, client_machine, rpc_info, ugi_, &request, response, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status Fsync(const std::string& path, const FsyncRequestProto& request) {
    SynchronizedRpcClosure rpc_done;
    AsyncFsync(path, &request, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetTimes(const std::string& path,
                  const SetTimesRequestProto& request) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetTimes(path, &request, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetStoragePolicy(const std::string& src,
                          const std::string& policy_name) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetStoragePolicy(src, policy_name, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetReplicaPolicy(const std::string& path,
                          int32_t id,
                          const std::string& dc) {
    SynchronizedRpcClosure rpc_done;
    if (id == kNonePolicy) {
      AsyncRemoveReplicaPolicy(path, &rpc_done);
    } else {
      ReplicaPolicy policy;
      policy.set_distributed(id == kDistributePolicy);
      auto dcs = StringUtils::SplitByChars(dc, ",");
      for (const auto& d : dcs) {
        policy.add_dc(d);
      }
      AsyncSetReplicaPolicy(path, policy, &rpc_done);
    }
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetReplicaPolicy(const std::string& path,
                          const ReplicaPolicy& policy) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetReplicaPolicy(path, policy, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetReplication(const std::string& path,
                        uint32_t replica,
                        bool allow_zero_replica = false) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetReplication(path, replica, allow_zero_replica, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetReadPolicy(const std::string& path, const ReadPolicy& read_policy) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetReadPolicy(path, read_policy, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status CreateSymlink(const std::string& target,
                       const std::string& link,
                       const PermissionStatus& perm,
                       bool create_parent) {
    SynchronizedRpcClosure rpc_done;
    AsyncCreateSymlink(
        target, link, perm, ugi_, create_parent, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetXAttr(const std::string& path,
                  const XAttrProto& xattr,
                  bool create,
                  bool replace) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetXAttr(path, xattr, create, replace, LogRpcInfo(), &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetTimes(const std::string& path,
                  const SetTimesRequestProto* request,
                  const UserGroupInfo& ugi) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetTimes(path, request, ugi, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status RemoveXAttr(const std::string& path, const XAttrProto& xattr) {
    SynchronizedRpcClosure rpc_done;
    AsyncRemoveXAttr(path, xattr, LogRpcInfo(), ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetOwner(const std::string& path,
                  const std::string& username,
                  const std::string& groupname) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetOwner(path, username, groupname, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetPermission(const std::string& path, uint32_t perm) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetPermission(path, perm, ugi_, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status SetLifecyclePolicy(const std::string& path,
                            const SetLifecyclePolicyRequestProto& req,
                            SetLifecyclePolicyResponseProto* resp,
                            const UserGroupInfo& ugi,
                            const LogRpcInfo& rpc_info) {
    SynchronizedRpcClosure rpc_done;
    AsyncSetLifecyclePolicy(path, req, resp, ugi, rpc_info, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status UnsetLifecyclePolicy(const std::string& path,
                              const UnsetLifecyclePolicyRequestProto& req,
                              UnsetLifecyclePolicyResponseProto* resp,
                              const UserGroupInfo& ugi,
                              const LogRpcInfo& rpc_info) {
    SynchronizedRpcClosure rpc_done;
    AsyncUnsetLifecyclePolicy(path, req, resp, ugi, rpc_info, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status AllowSnapshot(const std::string& snapshot_root) {
    SynchronizedRpcClosure rpc_done;
    AsyncAllowSnapshot(snapshot_root, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status DisAllowSnapshot(const std::string& snapshot_root) {
    SynchronizedRpcClosure rpc_done;
    AsyncDisAllowSnapshot(snapshot_root, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status CreateSnapshot(const std::string& snapshot_root,
                        const std::string& snapshot_name,
                        std::string* snapshot_path = nullptr) {
    SynchronizedRpcClosure rpc_done;
    ::cloudfs::CreateSnapshotResponseProto result;
    AsyncCreateSnapshot(snapshot_root, snapshot_name, &result, &rpc_done);
    rpc_done.Await();
    if (snapshot_path) {
      *snapshot_path = result.snapshotpath();
    }
    return rpc_done.status();
  }

  Status DeleteSnapshot(const std::string& snapshot_root,
                        const std::string& snapshot_name) {
    SynchronizedRpcClosure rpc_done;
    AsyncDeleteSnapshot(snapshot_root, snapshot_name, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  Status RenameSnapshot(const std::string& snapshot_root,
                        const std::string& old_name,
                        const std::string& new_name) {
    SynchronizedRpcClosure rpc_done;
    AsyncRenameSnapshot(snapshot_root, old_name, new_name, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  std::pair<int64_t, int64_t> ManualSnapshotGc(
      bool incremental_gc,
      const std::function<bool(int64_t)>& should_stop = [](int64_t) {
        return false;
      }) {
    // test rpc methods are synced, so ManualSnapshotGc will clean completely
    int64_t snapshot_inodes_deleted_sum = 0;
    int64_t snapshot_inodes_deleted = SnapshotGc(should_stop, incremental_gc);
    while (snapshot_inodes_deleted > 0) {
      snapshot_inodes_deleted_sum += snapshot_inodes_deleted;
      snapshot_inodes_deleted = SnapshotGc(should_stop, true);
    }
    int64_t inodes_deleted = ProcessPendingDeleteCF(should_stop);
    return {snapshot_inodes_deleted_sum, inodes_deleted};
  }

  void StopCheckpointer() {
    LOG(INFO) << "StopCheckpointer";
    if (checkpointer_.get()) {
      checkpointer_->Stop();
    }
  }

  uint64_t GetLastCkptTxId() {
    return NameSpace::GetLastCkptTxId();
  }

  Status SyncGetAdditionalDatanode(
      const std::string& path,
      const cnetpp::base::IPAddress& writer,
      const GetAdditionalDatanodeRequestProto* request,
      GetAdditionalDatanodeResponseProto* response) {
    SynchronizedRpcClosure rpc_done;
    GetAdditionalDatanode(
        path, NetworkLocationInfo(writer), ugi_, request, response, &rpc_done);
    rpc_done.Await();
    return rpc_done.status();
  }

  std::shared_ptr<MetaStorage> meta_storage_ptr() {
    return meta_storage_;
  }

  void LoadClusterId() {
    NameSpace::LoadClusterId();
  }

 private:
  UserGroupInfo ugi_;
};

class GMockNameSpace : public NameSpace {
 public:
  MOCK_METHOD1(SpeedUpRelease, void(INodeID));
  MOCK_METHOD8(PersistUfsFile,
               Status(const std::shared_ptr<Ufs>&,
                      uint64_t,
                      const std::string&,
                      const std::string&,
                      bool,
                      bool,
                      bool*,
                      StopWatchContext* rpc_sw_ctx));
  MOCK_METHOD6(SyncUfsFile,
               Status(const std::string&,
                      const std::string&,
                      const PermissionStatus&,
                      const UserGroupInfo&,
                      const std::shared_ptr<Ufs>&,
                      StopWatchContext* rpc_sw_ctx));
  MOCK_METHOD8(SyncUfsFileOnly,
               Status(const std::string&,
                      const std::string&,
                      const PermissionStatus&,
                      const UserGroupInfo&,
                      const std::shared_ptr<Ufs>&,
                      bool,
                      const std::string&,
                      StopWatchContext* rpc_sw_ctx));
};

}  // namespace dancenn

#endif  // TEST_MOCK_NAMESPACE_H
