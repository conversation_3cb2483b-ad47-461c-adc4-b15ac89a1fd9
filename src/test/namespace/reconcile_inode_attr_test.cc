#include "namespace/inode_attr_ttl_manager.h"

#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "test/namespace/gmock_meta_storage.h"
#include "test/namespace/mock_meta_scanner_v2.h"
#include "test/namespace/mock_namespace.h"
#include "test/mock_ha_state.h"

DECLARE_bool(run_ut);

namespace dancenn {

class INodeAttrTtlManagerTest : public testing::Test {
 public:
  void SetUp() override {
    ha_state_ = std::make_unique<MockHAState>();

    ns_ = std::make_shared<GMockNameSpace>();
    ns_->set_ha_state(ha_state_.get());

    meta_storage_ = std::make_shared<GMockMetaStorage>();
    meta_scanner_ = std::make_shared<GMockMetaScannerV2>();
  }

  void TearDown() override {
    ns_.reset();

    meta_storage_.reset();
    meta_scanner_.reset();
  }

 public:
  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<GMockNameSpace> ns_;

  std::shared_ptr<GMockMetaStorage> meta_storage_;
  std::shared_ptr<GMockMetaScannerV2> meta_scanner_;
};

TEST_F(INodeAttrTtlManagerTest, TestScanner) {
  std::shared_ptr<INodeAttrTtlScanner> scanner =
      std::make_shared<INodeAttrTtlScanner>(ns_.get());

  scanner->SetMetaStorage(meta_storage_.get());
  scanner->SetMetaScanner(meta_scanner_.get());

  ASSERT_TRUE(scanner->PreScan());

  INode inode;
  inode.set_id(1);
  inode.set_name("inode");
  inode.set_parent_id(0);
  auto permission = inode.mutable_permission();
  permission->set_username("user");
  permission->set_groupname("group");
  permission->set_permission(0755);
  inode.set_mtime(1);
  inode.set_atime(1);
  inode.set_type(INode_Type_kFile);
  auto pin_status = inode.mutable_pin_status();
  pin_status->set_pinned(true);
  pin_status->set_ttl(0);
  pin_status->set_recursive(true);
  pin_status->set_txid(1);

  std::string key;
  meta_storage_->EncodeStoreKey(0, "", 1, &key);
  std::string value = inode.SerializeAsString();

  INode saved;
  EXPECT_CALL(*meta_scanner_, AddWorkTask(testing::_))
      .Times(2)
      .WillOnce(testing::DoAll(
          [&saved](std::shared_ptr<MetaScannerWorkTask> tmp) {
            saved = std::static_pointer_cast<INodeAttrTtlTask>(tmp)->GetINode();
          },
          testing::Return(false)))
      .WillOnce(testing::Return(true));

  ASSERT_TRUE(scanner->Handle(nullptr, key, value));
  ASSERT_EQ(saved.SerializeAsString(), inode.SerializeAsString());

  ASSERT_TRUE(scanner->PostScan());
}

TEST_F(INodeAttrTtlManagerTest, TestStat) {
  std::shared_ptr<INodeAttrTtlStat> stat = std::make_shared<INodeAttrTtlStat>();
  ASSERT_TRUE(stat->PreScan());
  ASSERT_TRUE(stat->Handle(nullptr, "", ""));
  ASSERT_TRUE(stat->PostScan());
  ASSERT_EQ(stat->GetTaskCnt(), 1);
}

}  // namespace dancenn
