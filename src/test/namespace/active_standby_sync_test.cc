
// 3rdparty
#include <cnetpp/concurrency/thread_pool.h>
#include <absl/strings/str_format.h>

// protobuf
#include "ClientNamenodeProtocol.pb.h"

// core
#include "base/constants.h"
#include "base/java_exceptions.h"
#include "base/path_util.h"
#include "base/uuid.h"
#include "test/namespace/nn_cluster_test_base.h"

DECLARE_uint32(edit_log_assigner_apply_mode);
DECLARE_int32(block_pufs_info_monitor_interval_ms);

namespace dancenn {

#define CLS_NONE  StorageClassProto::NONE
#define CLS_HOT   StorageClassProto::HOT
#define CLS_WARM  StorageClassProto::WARM
#define CLS_COLD  StorageClassProto::COLD
#define CLS_IA    StorageClassProto::IA
#define CLS_AR    StorageClassProto::AR

class ActiveStandbySyncEditlogTest : public NnClusterTest {
 public:
  void SetUp() override {
    PreSetUp();
    NnClusterTest::SetUp();
    PostSetUp();
  }

  void TearDown() override {
    PreTearDown();
    NnClusterTest::TearDown();
    PostTearDown();
  }

 protected:
  struct DirTreeSpec {
    std::string dir_prefix;
    int nlevel;
    int ndir_per_level;
    int nfile_per_level;
    int nblock;
    int replication;
    StorageClassProto src_stcls;
    StorageClassProto dst_stcls;
    int expire_days;

    // stat
    uint64_t ndir_total;
    uint64_t nfile_total;
    uint64_t nblock_total;    // logical block
    uint64_t nlbyte_total;    // logical byte
    uint64_t nreplica_total;  // physical replica
    uint64_t npbyte_total;    // physical byte
  };

 protected:
  void PreSetUp();
  void PostSetUp();
  void PreTearDown();
  void PostTearDown();

  // Active-Standby failover related
  void TransitToStandby(std::shared_ptr<MockNameSpace> active_ns);
  void TransitToActive(std::shared_ptr<MockNameSpace> standby_ns,
                       std::shared_ptr<MockEditLogTailer> standby_tailer);
  void StartFileWorkload();
  void StopAllWorkload();

  // RPC workload related
  void RegisterDatanodes();
  void StartHeartbeat(bool only_once = false);
  void ConstructDirTrees(std::vector<DirTreeSpec>& specs);
  void ConstructDirTree(DirTreeSpec& spec);
  void ConstructDirTreeImpl(const std::string& path,
                            int cur_level,
                            int max_level,
                            int ndir_per_level,
                            int nfile_per_level,
                            int nblock,
                            int replication,
                            StorageClassProto cls,
                            uint64_t* ndir_total,
                            uint64_t* nfile_total,
                            uint64_t* nblock_total,
                            uint64_t* nlbyte_total,
                            uint64_t* nreplica_total,
                            uint64_t* npbyte_total);
  void AddDir(const std::string& path);
  void AddFile(const std::string& path,
               uint32_t nblock,
               uint32_t replication,
               StorageClassProto cls);
  void MakeReport(BlockID block_id,
                  uint64_t gs,
                  uint32_t len,
                  const std::string& dnuuid,
                  cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
                  StorageClassProto cls,
                  BlockManager::RepeatedIncBlockReport* report);
  void TestDirectoryRPC();
  void TestFileRPC();
  void TestRecoveryRPC();
  void TestAttributeRPC();
  void TestLifecycleRPC();
  void TestSnapshotRPC();

 protected:
  const std::string test_root = "/test_root";
  std::shared_ptr<cnetpp::concurrency::ThreadPool> tpool;
  std::thread heartbeat_thread_;
  bool heartbeat_stop_{ false };
  bool heartbeat_pause_{ false };
  const std::vector<std::string> dnuuids_ =
      { "dn-1", "dn-2", "dn-3", "dn-4", "dn-5" };
  const std::vector<std::string> dnips_ =
      { "**********", "**********", "**********", "**********", "**********" };
  const std::string client_name_ = "test_client_name";
  const std::string client_machine_ = "test_client_machine";
  cnetpp::base::IPAddress client_ip_ = cnetpp::base::IPAddress("***********");
  LogRpcInfo rpc_info_;
  UserGroupInfo ugi_;
  PermissionStatus perm_;
};

void ActiveStandbySyncEditlogTest::PreSetUp() {
  // to trigger block depring->depred manually in TestDirectoryRPC
  FLAGS_block_pufs_info_monitor_interval_ms = 10 * 60 * 1000;
}

void ActiveStandbySyncEditlogTest::PostSetUp() {
  // enable RecycleBin by default
  FLAGS_recycle_bin_enable = true;
  FLAGS_recycle_bin_scanner_enable = false;

  ns_->StopBGDeletionWorker();
  standby_->StopBGDeletionWorker();

  // active-standby failover related
  RegisterDatanodes();
  StartHeartbeat(true);
  tpool = std::make_shared<cnetpp::concurrency::ThreadPool>(
      "mock-client-thread-pool");
  tpool->set_num_threads(16);
  tpool->Start();

  // RPC workload related
  perm_.set_username("root");
  perm_.set_groupname("supergroup");
  perm_.set_permission(FsPermission::GetFileDefault().ToShort());
}

void ActiveStandbySyncEditlogTest::PreTearDown() {
  // active-standby failover related
  StopAllWorkload();
  heartbeat_stop_ = true;
  heartbeat_thread_.join();

  // RPC workload related
  FLAGS_edit_log_assigner_apply_mode = static_cast<uint32_t>(ApplyMode::LOGICAL);
}

void ActiveStandbySyncEditlogTest::PostTearDown() {
}

void ActiveStandbySyncEditlogTest::StartFileWorkload() {
  auto perm = MakePermission();
  for (int i = 0; i < tpool->size(); i++) {
    tpool->AddTask([this, perm] () -> bool {
      int tid = cnetpp::concurrency::ThisThread::GetId();
      std::string root_dir = test_root + "-" + std::to_string(tid);
      Status st = ns_->MkDirs(root_dir, perm, true);
      CHECK(st.IsOK());

      for (int i = 0; i < 100; i++) {
        std::string fullpath = absl::StrFormat("%s/dir-%d", root_dir, i);
        st = ns_->MkDirs(fullpath, perm, true);
        CHECK(st.IsOK());
        st = ns_->Delete(fullpath, true);
        CHECK(st.IsOK());
      }

      st = ns_->Delete(root_dir, false);
      CHECK(st.IsOK());
      return true;
    });
  }
}

void ActiveStandbySyncEditlogTest::StopAllWorkload() {
  tpool->Stop(true);
}

void ActiveStandbySyncEditlogTest::TransitToStandby(
    std::shared_ptr<MockNameSpace> active_ns) {
  transited_ = true;
  active_ns->WaitNoPending();
  active_ns->StopActive();
  active_ns->StartStandby();
}

void ActiveStandbySyncEditlogTest::TransitToActive(
    std::shared_ptr<MockNameSpace> standby_ns,
    std::shared_ptr<MockEditLogTailer> standby_tailer) {
  transited_ = true;
  standby_tailer_->Stop();
  standby_->StopStandby();
  standby_->StartActive();
}


void ActiveStandbySyncEditlogTest::RegisterDatanodes() {
  // add a datanode to the cluster
  for (int i = 0; i < dnuuids_.size(); i++) {
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(dnuuids_.at(i));
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip(dnips_.at(i));
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }
}

void ActiveStandbySyncEditlogTest::StartHeartbeat(bool only_once) {
  heartbeat_stop_ = false;
  heartbeat_pause_ = false;
  CountDownLatch latch(1);
  heartbeat_thread_ = std::thread([&]() {
    bool heartbeated = false;
    do {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      if (heartbeat_pause_) {
        continue;
      }

      HeartbeatRequestProto req;
      for (int i = 0; i < dnuuids_.size(); i++) {
        auto reg =
            cloudfs::datanode::DatanodeRegistrationProto::default_instance();
        reg.mutable_datanodeid()->set_datanodeuuid(dnuuids_.at(i));
        cnetpp::base::IPAddress ip(dnips_.at(i));
        RepeatedStorageReport reports;
        auto rpt = reports.Add();
        rpt->set_storageuuid("storage1");
        rpt->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
        rpt->mutable_storage()->set_storageuuid("storage1");
        req.mutable_registration()->CopyFrom(reg);
        req.mutable_reports()->CopyFrom(reports);

        DatanodeManager::RepeatedCmds cmds;
        datanode_manager_->Heartbeat(req, &cmds);
      }
      if (!heartbeated) {
        heartbeated = true;
        latch.CountDown();
      }
    } while (!heartbeat_stop_ && !only_once);
  });
  latch.Await();
}

void ActiveStandbySyncEditlogTest::ConstructDirTrees(
    std::vector<DirTreeSpec>& specs) {
  for (auto& spec : specs) {
    ConstructDirTree(spec);
  }
}

void ActiveStandbySyncEditlogTest::ConstructDirTree(
    DirTreeSpec& spec) {
  ConstructDirTreeImpl(spec.dir_prefix,
                       0,
                       spec.nlevel,
                       spec.ndir_per_level,
                       spec.nfile_per_level,
                       spec.nblock,
                       spec.replication,
                       spec.src_stcls,
                       &spec.ndir_total,
                       &spec.nfile_total,
                       &spec.nblock_total,
                       &spec.nlbyte_total,
                       &spec.nreplica_total,
                       &spec.npbyte_total);
  CHECK_EQ(spec.nblock_total * spec.replication, spec.nreplica_total);
  CHECK_EQ(spec.nlbyte_total * spec.replication, spec.npbyte_total);
  CHECK_EQ(spec.nblock_total * FLAGS_dfs_block_size, spec.nlbyte_total);
  CHECK_EQ(spec.nreplica_total * FLAGS_dfs_block_size, spec.npbyte_total);
  WaitSynced();
  ASSERT_TRUE(CheckDBConsistency());
}

void ActiveStandbySyncEditlogTest::ConstructDirTreeImpl(
    const std::string& dir_prefix,
    int cur_level,
    int max_level,
    int ndir_per_level,
    int nfile_per_level,
    int nblock,
    int replication,
    StorageClassProto cls,
    uint64_t* ndir_total,
    uint64_t* nfile_total,
    uint64_t* nblock_total,
    uint64_t* nlbyte_total,
    uint64_t* nreplica_total,
    uint64_t* npbyte_total) {
  if (cur_level > max_level) {
    return;
  }

  const std::string prefix = (dir_prefix == "/") ? "" : dir_prefix;
  int dir_idx = 0;
  int file_idx = 0;
  for (int i = 0; i < std::min(ndir_per_level, nfile_per_level); i++) {
    {
      std::string dir = absl::StrFormat(
          "%s/level-%d-dir-%d",
          prefix, cur_level, dir_idx);
      AddDir(dir);
      dir_idx++;

      ConstructDirTreeImpl(dir,
                           cur_level + 1,
                           max_level,
                           ndir_per_level,
                           nfile_per_level,
                           nblock,
                           replication,
                           cls,
                           ndir_total,
                           nfile_total,
                           nblock_total,
                           nlbyte_total,
                           nreplica_total,
                           npbyte_total);
      *ndir_total += 1;
    }
    {
      std::string file = absl::StrFormat(
          "%s/level-%d-file-%d",
          prefix, cur_level, file_idx);
      AddFile(file, nblock, replication, cls);
      file_idx++;

      *nfile_total += 1;
      *nblock_total += 1 * nblock;
      *nlbyte_total += 1 * nblock * FLAGS_dfs_block_size;
      *nreplica_total += 1 * nblock * replication;
      *npbyte_total += 1 * nblock * replication * FLAGS_dfs_block_size;
    }
  }

  if (nfile_per_level < ndir_per_level) {
    for (int i = nfile_per_level; i < ndir_per_level; i++) {
      std::string dir = absl::StrFormat(
          "%s/level-%d-dir-%d",
          prefix, cur_level, dir_idx);
      AddDir(dir);
      dir_idx++;

      ConstructDirTreeImpl(dir,
                           cur_level + 1,
                           max_level,
                           ndir_per_level,
                           nfile_per_level,
                           nblock,
                           replication,
                           cls,
                           ndir_total,
                           nfile_total,
                           nblock_total,
                           nlbyte_total,
                           nreplica_total,
                           npbyte_total);
      *ndir_total += 1;
    }
  }
  if (ndir_per_level < nfile_per_level) {
    for (int i = ndir_per_level; i < nfile_per_level; i++) {
      std::string file = absl::StrFormat(
          "%s/level-%d-file-%d",
          prefix, cur_level, file_idx);
      AddFile(file, nblock, replication, cls);
      file_idx++;

      *nfile_total += 1;
      *nblock_total += 1 * nblock;
      *nlbyte_total += 1 * nblock * FLAGS_dfs_block_size;
      *nreplica_total += 1 * nblock * replication;
      *npbyte_total += 1 * nblock * replication * FLAGS_dfs_block_size;
    }
  }
}

void ActiveStandbySyncEditlogTest::AddDir(const std::string& path) {
  Status st = ns_->MkDirs(path, perm_, true);
  ASSERT_TRUE(st.IsOK()) << st.ToString();
}

void ActiveStandbySyncEditlogTest::AddFile(
    const std::string& path,
    uint32_t nblock,
    uint32_t replication,
    StorageClassProto cls) {
  const uint32_t block_size = FLAGS_dfs_block_size;

  // create
  CreateRequestProto create_req;
  create_req.set_src(path);
  create_req.mutable_masked()->set_perm(0);
  create_req.set_clientname(client_name_);
  create_req.set_createflag(::cloudfs::CreateFlagProto::CREATE);
  create_req.set_replication(replication);
  create_req.set_createparent(true);
  create_req.set_blocksize(block_size);
  CreateResponseProto create_resp;
  Status st = ns_->CreateFile(path, perm_, create_req, &create_resp);
  ASSERT_TRUE(st.IsOK()) << st.ToString();

  AddBlockRequestProto add_req;
  AddBlockResponseProto add_resp;
  ExtendedBlockProto exblk;
  for (int blki = 0; blki < nblock; blki++) {
    // add block
    add_req.set_src(path);
    add_req.set_clientname(client_name_);
    st = ns_->AddBlock(path, client_ip_, rpc_info_, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK()) << st.ToString();
    exblk = add_resp.block().b();

    // report
    int rand_idx = std::rand() % dnuuids_.size();
    for (int i = 0; i < replication; i++) {
      int dnid = (rand_idx + i) % dnuuids_.size();
      const std::string& dnuuid = dnuuids_[dnid];

      BlockManager::RepeatedIncBlockReport report;
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 block_size,
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
                 cls,
                 &report);
      block_manager_->IncrementalBlockReport(dnuuid, report);

      report.Clear();
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 block_size,
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                 cls,
                 &report);
      block_manager_->IncrementalBlockReport(dnuuid, report);
    }

    // fsync
    FsyncRequestProto fsync_req;
    fsync_req.set_src(path);
    fsync_req.set_client(client_name_);
    fsync_req.set_lastblocklength(block_size);
    fsync_req.set_fileid(create_resp.fs().fileid());
    st = ns_->Fsync(path, fsync_req);
    ASSERT_TRUE(st.IsOK()) << st.ToString();

    // upload
    exblk.set_numbytes(block_size);
    bool persisted = false;
    for (int dni = 0; dni < replication; dni++) {
      int dnid = (rand_idx + dni) % dnuuids_.size();
      const std::string& dnuuid = dnuuids_[dnid];

      BlockManager::RepeatedIncBlockReport report;
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 exblk.numbytes(),
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED,
                 cls,
                 &report);
      block_manager_->IncrementalBlockReport(dnuuid, report);

      ns_->WaitNoPending();

      if (!persisted) {
        persisted = true;

        report.Clear();
        MakeReport(exblk.blockid(),
                   exblk.generationstamp(),
                   exblk.numbytes(),
                   dnuuid,
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
                   cls,
                   &report);
        block_manager_->IncrementalBlockReport(dnuuid, report);
      }
    }

    // prepare for next block
    add_req.mutable_previous()->CopyFrom(exblk);
  }

  // complete
  CompleteRequestProto comp_req;
  comp_req.set_src(path);
  comp_req.set_clientname(client_name_);
  comp_req.mutable_last()->CopyFrom(exblk);
  comp_req.mutable_last()->set_numbytes(block_size);
  st = ns_->CompleteFile(path, comp_req);
  ASSERT_TRUE(st.IsOK()) << st.ToString();

}

void ActiveStandbySyncEditlogTest::MakeReport(
    BlockID block_id,
    uint64_t gs,
    uint32_t len,
    const std::string& dnuuid,
    cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
    StorageClassProto cls,
    BlockManager::RepeatedIncBlockReport* report) {
  auto rpt = report->Add();
  rpt->set_storageuuid(dnuuid);
  auto blk = rpt->add_blocks();
  blk->mutable_block()->set_blockid(block_id);
  blk->mutable_block()->set_genstamp(gs);
  blk->mutable_block()->set_numbytes(len);
  blk->set_storageclass(cls);
  blk->set_status(state);
}

#define LOCAL_UPDATE_FLAG(flag_name, local_value)                             \
  auto _##flag_name##_old_value = FLAGS_##flag_name;                          \
  FLAGS_##flag_name = local_value;                                            \
  DEFER([&]() { FLAGS_##flag_name = _##flag_name##_old_value; });

void ActiveStandbySyncEditlogTest::TestDirectoryRPC() {
  std::string dir_prefix = "/dir_test";
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 3, 5, 3, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);
  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  std::string path;
  std::string src, dst;
  Status st;

  // 1. Delete, RecycleBin disabled
  {
    FLAGS_recycle_bin_enable = false;

    // 1.1 delete file
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0/level-2-dir-0/level-3-file-0";
    st = ns_->Delete(path, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 1.2 delete empty dir
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0/level-2-dir-0/level-3-dir-0";
    st = ns_->Delete(path, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 1.3 delete non-empty dir
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0/level-2-dir-1";
    st = ns_->Delete(path, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 2. Delete, RecycleBin enabled
  {
    FLAGS_recycle_bin_enable = true;

    // 2.1 delete file
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0/level-2-dir-2/level-3-file-0";
    st = ns_->Delete(path, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 8 + 1;  // prepare 8 parent directories in RecycleBin

    // 2.2 delete empty dir
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0/level-2-dir-2/level-3-dir-0";
    st = ns_->Delete(path, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 2;

    // 2.3 delete non-empty dir
    path = dir_prefix + "/level-0-dir-0/level-1-dir-0";
    st = ns_->Delete(path, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 3. RenameOld
  {
    // 3.1 rename dir, same parent
    src = dir_prefix + "/level-0-dir-1/level-1-dir-0";
    dst = dir_prefix + "/level-0-dir-1/renameold-dir-dst-0";
    st = ns_->RenameTo(src, dst);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 3.2 rename dir, different parent
    src = dir_prefix + "/level-0-dir-1/level-1-dir-1";
    dst = dir_prefix + "/level-0-dir-1/level-1-dir-2/level-2-dir-0/renameold-dir-dst-1";
    st = ns_->RenameTo(src, dst);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 3.3 rename file, same parent
    src = dir_prefix + "/level-0-dir-1/level-1-dir-3/level-2-dir-0/level-3-file-0";
    dst = dir_prefix + "/level-0-dir-1/level-1-dir-3/level-2-dir-0/renameold-file-dst-0";
    st = ns_->RenameTo(src, dst);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 3.4 rename file, different parent
    src = dir_prefix + "/level-0-dir-1/level-1-file-0";
    dst = dir_prefix + "/level-0-dir-1/level-1-dir-3/renameold-file-dst-1";
    st = ns_->RenameTo(src, dst);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 4. Rename, RecycleBin disabled
  {
    FLAGS_recycle_bin_enable = false;

    // 4.1 rename dir, same parent, no overwrite
    src = dir_prefix + "/level-0-dir-2/level-1-dir-0";
    dst = dir_prefix + "/level-0-dir-2/rename-dir-dst-0";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.2 rename dir, same parent, overwrite
    src = dir_prefix + "/level-0-dir-2/level-1-dir-1/level-2-dir-0/level-3-dir-0";
    dst = dir_prefix + "/level-0-dir-2/level-1-dir-1/level-2-dir-0/level-3-dir-1";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.3 rename dir, different parent, no overwrite
    src = dir_prefix + "/level-0-dir-2/level-1-dir-1/level-2-dir-1/level-3-dir-0";
    dst = dir_prefix + "/level-0-dir-2/level-1-dir-2/rename-dir-dst-1";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.4 rename dir, different parent, overwrite
    src = dir_prefix + "/level-0-dir-2/level-1-dir-2";
    dst = dir_prefix + "/level-0-dir-2/level-1-dir-3/level-2-dir-0/level-3-dir-0";
    st = ns_->RenameTo2(src, dst,true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.5 rename file, same parent, no overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-0/level-2-file-0";
    dst = dir_prefix + "/level-0-dir-3/level-1-dir-0/rename-file-dst-0";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.6 rename file, same parent, overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-0/level-2-file-1";
    dst = dir_prefix + "/level-0-dir-3/level-1-dir-0/level-2-file-2";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.7 rename file, different parent, no overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-1/level-2-dir-0/level-3-file-0";
    dst = dir_prefix + "/level-0-dir-3/rename-file-dst-1";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 4.8 rename file, different parent, overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-2/level-2-dir-0/level-3-file-0";
    dst = dir_prefix + "/level-0-dir-3/level-1-file-0";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 5. Rename, RecycleBin enabled
  {
    FLAGS_recycle_bin_enable = true;

    // 5.1 rename dir, same parent, no overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-0";
    dst = dir_prefix + "/level-0-dir-3/rename-dir-dst-0";
    st = ns_->RenameTo2(src, dst,false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 5.2 rename dir, same parent, overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-1/level-2-dir-0/level-3-dir-0";
    dst = dir_prefix + "/level-0-dir-3/level-1-dir-1/level-2-dir-0/level-3-dir-1";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 4 + 1;

    // 5.3 rename dir, different parent, no overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-1/level-2-dir-1/level-3-dir-0";
    dst = dir_prefix + "/level-0-dir-3/level-1-dir-2/rename-dir-dst-1";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 5.4 rename dir, different parent, overwrite
    src = dir_prefix + "/level-0-dir-3/level-1-dir-2";
    dst = dir_prefix + "/level-0-dir-3/level-1-dir-3/level-2-dir-0/level-3-dir-0";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 3 + 1;

    // 5.5 rename file, same parent, no overwrite
    src = dir_prefix + "/level-0-dir-4/level-1-dir-0/level-2-file-0";
    dst = dir_prefix + "/level-0-dir-4/level-1-dir-0/rename-file-dst-0";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 5.6 rename file, same parent, overwrite
    src = dir_prefix + "/level-0-dir-4/level-1-dir-0/level-2-file-1";
    dst = dir_prefix + "/level-0-dir-4/level-1-dir-0/level-2-file-2";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 3 + 1;

    // 5.7 rename file, different parent, no overwrite
    src = dir_prefix + "/level-0-dir-4/level-1-dir-1/level-2-dir-0/level-3-file-0";
    dst = dir_prefix + "/level-0-dir-4/rename-file-dst-1";
    st = ns_->RenameTo2(src, dst, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 5.8 rename file, different parent, overwrite
    src = dir_prefix + "/level-0-dir-4/level-1-dir-2/level-2-dir-0/level-3-file-0";
    dst = dir_prefix + "/level-0-dir-4/level-1-file-0";
    st = ns_->RenameTo2(src, dst, true);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1 + 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 6. Create, RecycleBin disabled
  {
    FLAGS_recycle_bin_enable = false;

    // 6.1 create, no overwrite
    path = dir_prefix + "/level-0-dir-4/create-file-0";
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_src(path);
    CreateResponseProto create_resp;
    st = ns_->CreateFile(
        path,
        perm_,
        create_req,
        &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 6.2 create, overwrite
    create_req = MakeCreateRequest();
    create_req.set_src(path);
    create_req.set_createflag(cloudfs::CreateFlagProto::OVERWRITE);
    create_resp.Clear();
    st = ns_->CreateFile(
        path,
        perm_,
        create_req,
        &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 7. Create, RecycleBin enabled
  {
    FLAGS_recycle_bin_enable = true;

    // 7.1 create, no overwrite
    path = dir_prefix + "/level-0-dir-4/create-file-1";
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_src(path);
    CreateResponseProto create_resp;
    st = ns_->CreateFile(
        path,
        perm_,
        create_req,
        &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    // 7.2 create, overwrite
    create_req = MakeCreateRequest();
    create_req.set_src(path);
    create_req.set_createflag(cloudfs::CreateFlagProto::OVERWRITE);
    create_resp.Clear();
    st = ns_->CreateFile(
        path,
        perm_,
        create_req,
        &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 2;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 8. block depring->depred procedure
  {
    st = ns_->Delete(dir_prefix, true);
    ASSERT_TRUE(st.IsOK());
    st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
    ASSERT_TRUE(st.IsOK());
    txid_delta += 2;
    auto dummy_cb = [](int64_t) { return false; };
    (void)ns_->ProcessPendingDeleteCF(dummy_cb);
    ns_->WaitNoPending();
    block_manager_->HandleDeprecatingBlocks();
    txid_delta += 1;
    block_manager_->HandleDeprecatedBlocks();
    ns_->WaitNoPending();
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());
}

void ActiveStandbySyncEditlogTest::TestFileRPC() {
  std::string dir_prefix = "/file_test";
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 1, 3, 4, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);

  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  Status st;
  std::string path;
  AppendRequestProto append_req;
  AppendResponseProto append_resp;
  IsFileClosedResponseProto isclosed_resp;

  // 1. append
  {
    path = dir_prefix + "/level-0-dir-0/level-1-file-0";
    ns_->IsFileClosed(path, &isclosed_resp);
    EXPECT_TRUE(isclosed_resp.result());

    append_req = MakeAppendRequest(path, client_name_);
    st = ns_->Append(
        path,
        client_machine_,
        rpc_info_,
        append_req,
        &append_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta++;

    ns_->IsFileClosed(path, &isclosed_resp);
    EXPECT_FALSE(isclosed_resp.result());
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();
}

void ActiveStandbySyncEditlogTest::TestRecoveryRPC() {
  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  Status st;
  std::string dir_prefix = "/recover_test";
  std::string path;
  uint64_t blkid;
  uint64_t gs;

  st = ns_->MkDirs(dir_prefix, perm_, true);
  CHECK(st.IsOK());
  txid_delta += 1;

  // 1. abandonBlock
  {
    path = dir_prefix + "/test-file.abandon-block";
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(1);
    create_req.set_clientname(client_name_);
    CreateResponseProto create_resp;
    st = ns_->CreateFile(path, perm_, create_req, &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    add_req.set_clientname(client_name_);
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip_, rpc_info_, add_req, &add_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += FLAGS_enable_fast_block_id_and_gs_gen ? 1 : 3;

    AbandonBlockRequestProto abandon_req;
    abandon_req.mutable_b()->set_blockid(add_resp.block().b().blockid());
    abandon_req.set_src(path);
    abandon_req.set_holder(client_name_);
    st = ns_->AbandonBlock(path, abandon_req);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 2. updatePipeline
  {
    path = dir_prefix + "/test-file.update-pipeline";
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(1);
    CreateResponseProto create_resp;
    st = ns_->CreateFile(path, perm_, create_req, &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip_, rpc_info_, add_req, &add_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += FLAGS_enable_fast_block_id_and_gs_gen ? 1 : 3;

    blkid = add_resp.block().b().blockid();
    gs = add_resp.block().b().generationstamp();
    std::vector<std::pair<std::string, std::string>> new_dns = {
      { dnips_[0], dnuuids_[0] },
      { dnips_[4], dnuuids_[4] },
    };
    UpdatePipelineRequestProto update_pipeline_req;
    MakeUpdatePipelineRequest(blkid, gs, gs + 1, new_dns, &update_pipeline_req);
    st = ns_->UpdatePipeline(update_pipeline_req, rpc_info_);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  // 3. updateBlocks & reassignLease
  {
    path = dir_prefix + "/test-file.update-blocks";
    CreateRequestProto create_req = MakeCreateRequest();
    create_req.set_replication(1);
    CreateResponseProto create_resp;
    st = ns_->CreateFile(path, perm_, create_req, &create_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    AddBlockRequestProto add_req = MakeAddBlockRequest();
    AddBlockResponseProto add_resp;
    st = ns_->AddBlock(path, client_ip_, rpc_info_, add_req, &add_resp);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += FLAGS_enable_fast_block_id_and_gs_gen ? 1 : 3;

    gs = add_resp.block().b().generationstamp();
    CommitBlockSynchronizationRequestProto commit_req;
    commit_req.mutable_block()->CopyFrom(add_resp.block().b());
    commit_req.set_newgenstamp(kInvalidGenerationStamp);
    commit_req.set_newlength(0);
    commit_req.set_closefile(false);
    commit_req.set_deleteblock(true);
    st = ns_->CommitBlockSynchronization(commit_req);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;

    RecoverLeaseRequestProto request;
    st = ns_->RecoverLease("new-holder", path, &request);
    EXPECT_FALSE(st.HasException()) << st.ToString();
    EXPECT_EQ(st.code(), Code::kFalse) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();
}

void ActiveStandbySyncEditlogTest::TestAttributeRPC() {
  std::string dir_prefix = "/attr_test";
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 1, 10, 1, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);

  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  Status st;
  std::string path;
  // 1. setReplication
  {
    path = dir_prefix + "/level-0-dir-0/level-1-file-0";
    st = ns_->SetReplication(path, 1);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 2. setStoragePolicy
  {
    path = dir_prefix + "/level-0-dir-1/level-1-dir-0";
    st = ns_->SetStoragePolicy(path, "ALL_SSD");
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 3. setReplicaPolicy
  {
    path = dir_prefix + "/level-0-dir-2";
    st = ns_->SetReplicaPolicy(path, kDistributePolicy, "LF,HL");
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 4. setPermissions
  {
    path = dir_prefix + "/level-0-dir-3/level-1-file-0";
    st = ns_->SetPermission(path, 0777);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 5. setOwner
  {
    path = dir_prefix + "/level-0-dir-4/level-1-file-0";
    st = ns_->SetOwner(path, "new-owner-name", "new-group-name");
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 6. setXAttrs & removeXAttrs
  {
    path = dir_prefix + "/level-0-dir-5/level-1-file-0";
    XAttrProto xattr;
    xattr.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    xattr.set_name("cfs.xattr.test");
    xattr.set_value("xattr.value");
    st = ns_->SetXAttr(path, xattr, true, false);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
    st = ns_->RemoveXAttr(path, xattr);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  // 7. setTimes
  {
    path = dir_prefix + "/level-0-dir-6/level-1-file-0";
    SetTimesRequestProto settimes_req;
    settimes_req.set_src(path);
    settimes_req.set_mtime(123);
    settimes_req.set_atime(456);
    st = ns_->SetTimes(path, settimes_req);
    EXPECT_TRUE(st.IsOK()) << st.ToString();
    txid_delta += 1;
  }

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();
}

void ActiveStandbySyncEditlogTest::TestLifecycleRPC() {
  std::string dir_prefix = "/lifecycle_test";
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 2, 2, 2, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);

  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  std::string path;
  Status st;
  // 1. setLifecyclePolicy
  path = dir_prefix + "/level-0-dir-0/level-1-dir-1";
  LifecyclePolicyProto plcy;
  plcy.set_defaultclass(CLS_HOT);
  plcy.mutable_exprule()->set_days(10);
  TransitionRule* trule = plcy.add_transrules();
  trule->set_days(5);
  trule->set_targetclass(CLS_COLD);
  SetLifecyclePolicyRequestProto set_req;
  SetLifecyclePolicyResponseProto set_resp;
  set_req.set_path(path);
  set_req.mutable_lifecyclepolicy()->CopyFrom(plcy);
  st = ns_->SetLifecyclePolicy(path, set_req, &set_resp, ugi_, rpc_info_);
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  // 2. unsetLifecyclePolicy
  UnsetLifecyclePolicyRequestProto unset_req;
  UnsetLifecyclePolicyResponseProto unset_resp;
  unset_req.set_path(path);
  st = ns_->UnsetLifecyclePolicy(path, unset_req, &unset_resp, ugi_, rpc_info_);
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();
}

void ActiveStandbySyncEditlogTest::TestSnapshotRPC() {
  std::string dir_prefix = "/snapshot_test";
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 1, 2, 2, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);

  uint64_t txid_base = ns_->GetLastCkptTxId();
  uint64_t txid_delta = 0;

  Status st;
  // 1. allowSnapshot
  std::string path = dir_prefix + "/level-0-dir-0/level-1-dir-0";
  st = ns_->AllowSnapshot(path);
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  // 2. createSnapshot
  st = ns_->CreateSnapshot(path, "snapshot-name");
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  // 3. renameSnapshot
  st = ns_->RenameSnapshot(path, "snapshot-name", "snapshot-name.new");
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  // 4. deleteSnapshot
  st = ns_->DeleteSnapshot(path, "snapshot-name.new");
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  // 5. disallowSnapshot
  st = ns_->DisAllowSnapshot(path);
  EXPECT_TRUE(st.IsOK()) << st.ToString();
  txid_delta += 1;

  WaitSynced();
  ASSERT_EQ(ns_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_EQ(standby_->GetLastCkptTxId(), txid_base + txid_delta);
  ASSERT_TRUE(CheckDBConsistency());
  ASSERT_TRUE(CheckINodeStatConsistency());

  st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();
}

TEST_F(ActiveStandbySyncEditlogTest, GracefulFailover) {
  standby_tailer_->set_sync_delay_interval(100);
  standby_tailer_->set_sync_delay_latency(1000);
  StartFileWorkload();

  {
    std::this_thread::sleep_for(std::chrono::seconds(20));
    standby_tailer_->set_sync_delay_interval(0);
    standby_tailer_->set_sync_delay_latency(0);

    // 1. Active transit to Standby gracefully
    {
      vunique_lock vlock(
          dynamic_cast<MockHAState*>(ha_state_.get())->barrier_.lock());
      LOG(INFO) << "now transit Active to Standby";
      TransitToStandby(ns_);
      LOG(INFO) << "Active transitted to Standby";
    }

    // 2. Standby transit to Active
    LOG(INFO) << "now transit Standby to Active";
    TransitToActive(standby_, standby_tailer_);
    LOG(INFO) << "Standby transitted to Active";

    // 3. wait Standby applied all editlog, check data consistency
    WaitSynced();
    ASSERT_TRUE(CheckDBConsistency());
    LOG(INFO) << "DB consistency check pass between Active & Standby";

    ns_->StopStandby();
    ns_->StartActive();
  }

  StopAllWorkload();
}

TEST_F(ActiveStandbySyncEditlogTest, ForcefulFailover) {
  standby_tailer_->set_sync_delay_interval(100);
  standby_tailer_->set_sync_delay_latency(1000);
  StartFileWorkload();

  {
    std::this_thread::sleep_for(std::chrono::seconds(10));
    standby_tailer_->set_sync_delay_interval(0);
    standby_tailer_->set_sync_delay_latency(0);

    // 1. Standby transit to Active
    LOG(INFO) << "now transit Standby to Active";
    TransitToActive(standby_, standby_tailer_);
    LOG(INFO) << "Standby transitted to Active";

    // 2. Active transit to Standby, mock BK fenced
    StopAllWorkload();
    auto ha_barrier_lock = ha_state_->LockBarrierForHASwitcher();
    LOG(INFO) << "now transit Active to Standby";
    TransitToStandby(ns_);
    LOG(INFO) << "Active transitted to Standby";

    // 3. wait Standby applied all editlog, check data consistency
    WaitSynced();
    ASSERT_TRUE(CheckDBConsistency());
    LOG(INFO) << "DB consistency check pass between Active & Standby";

    ns_->StopStandby();
    ns_->StartActive();
  }
}

TEST_F(ActiveStandbySyncEditlogTest, DirectoryRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestDirectoryRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, FileRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestFileRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, RecoveryRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestRecoveryRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, AttributeRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestAttributeRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, LifecycleRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestLifecycleRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, SnapshotRPC) {
  EnableINodeStatConsistency();
  for (int i = 0; i < static_cast<uint32_t>(ApplyMode::NUM_MODES); i++) {
    LOCAL_UPDATE_FLAG(edit_log_assigner_apply_mode, i);
    TestSnapshotRPC();
  }
  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, ApplyModeSwitch) {
  EnableINodeStatConsistency();

  auto switcher =
      std::make_shared<cnetpp::concurrency::ThreadPool>("apply-mode-switcher");
  switcher->set_num_threads(1);
  switcher->Start();
  switcher->AddTask(
      [] () -> bool {
    for (int i = 0; i < 9; i++) {
      std::this_thread::sleep_for(std::chrono::seconds(10));
      uint32_t val = rand() % static_cast<int>(ApplyMode::NUM_MODES);
      LOG(INFO) << "apply mode now switched to " << std::to_string(val);
      FLAGS_edit_log_assigner_apply_mode = val;
    }
    return true;
  });


  TestDirectoryRPC();
  TestFileRPC();
  TestRecoveryRPC();
  TestAttributeRPC();
  TestLifecycleRPC();
  TestSnapshotRPC();

  switcher->Stop();

  DisableINodeStatConsistency();
}

TEST_F(ActiveStandbySyncEditlogTest, ProcessPendingDelete) {
  LOCAL_UPDATE_FLAG(recycle_bin_enable, false);
  LOCAL_UPDATE_FLAG(dancenn_observe_mode_on, true);

  const std::vector<std::string> pdirs =
      { "ppdir", "pdir", "pending_delete_test"};
  std::string dir_prefix = kSeparator + absl::StrJoin(pdirs, kSeparator);
  std::vector<DirTreeSpec> dirtree_spec = {
    { dir_prefix, 2, 2, 2, 2, 3, CLS_WARM, CLS_WARM, 999, 0, 0, 0, 0, 0, 0, }
  };
  ConstructDirTrees(dirtree_spec);

  // BGDeletionWorker of Active & Standby are stopped already

  // 1. delete parent inode, waiting to be processed
  // Assertions:
  //   - parent INode moved from default-CF to pending-delete-CF
  //   - parent INodeStat removed, all ancestor INodeStat updated
  //   - children INodes remian in default-CF
  //   - chlidren INodeStat remain in INodeStat-CF
  Status st = ns_->Delete(dir_prefix, true);
  ASSERT_TRUE(st.IsOK());
  WaitSynced();

  auto observer_ns = standby_;
  std::shared_ptr<MetaStorage> act_ms = ns_->TestOnlyGetMetaStorage();
  std::shared_ptr<MetaStorage> obs_ms = observer_ns->TestOnlyGetMetaStorage();
  auto spec = dirtree_spec.front();
  const uint64_t ndir_total =
      1                   // root
      + pdirs.size()      // parent-dirs and test-dir
      + spec.ndir_total;  // all children-dir
  const uint64_t nfile_total = spec.nfile_total;
  const uint64_t ninode_total = ndir_total + nfile_total;
  {
    bool found_in_dft = false;
    bool found_in_pd = false;
    uint64_t nent_in_dft = 0;
    uint64_t nent_in_pd = 0;
    auto snapshot_holder = act_ms->GetSnapshot();
    {
      auto snap = snapshot_holder->snapshot();
      auto inode_iter_holder = act_ms->GetIterator(snap, kINodeDefaultCFIndex);
      auto inode_iter = inode_iter_holder->iter();
      for (inode_iter->SeekToFirst();
           inode_iter->Valid() && inode_iter->status().ok();
           inode_iter->Next()) {
        INode inode;
        bool ok = inode.ParseFromArray(inode_iter->value().data(),
                                       inode_iter->value().size());
        ASSERT_TRUE(ok);
        nent_in_dft++;
        if (inode.name() == pdirs.back()) {
          found_in_dft = true;
        }
      }

      auto pd_iter_holder = act_ms->GetIterator(snap, kINodePendingDeleteCFIndex);
      auto pd_iter = pd_iter_holder->iter();
      for (pd_iter->SeekToFirst();
           pd_iter->Valid() && pd_iter->status().ok();
           pd_iter->Next()) {
        INode inode;
        bool ok = inode.ParseFromArray(pd_iter->value().data(),
                                       pd_iter->value().size());
        ASSERT_TRUE(ok);
        nent_in_pd++;
        if (inode.name() == pdirs.back()) {
          found_in_pd = true;
        }
      }
    }
    ASSERT_FALSE(found_in_dft);
    ASSERT_EQ(nent_in_dft, ninode_total - 1);
    ASSERT_TRUE(found_in_pd);
    ASSERT_EQ(nent_in_pd, 1);

    INodeStat root_instat;
    uint64_t nent_in_instat = 0;
    auto obs_snapshot_holder = obs_ms->GetSnapshot();
    {
      auto snap = obs_snapshot_holder->snapshot();
      auto instat_iter_holder = obs_ms->GetIterator(snap, kINodeStatCFIndex);
      auto instat_iter = instat_iter_holder->iter();
      for (instat_iter->SeekToFirst();
           instat_iter->Valid() && instat_iter->status().ok();
           instat_iter->Next()) {
        nent_in_instat++;
        INodeStat instat;
        INodeStatUtils::Parse(instat_iter->value(), &instat);
        if (instat.inode_id == kRootINodeId) {
          root_instat = instat;
        }
      }
    }
    ASSERT_EQ(nent_in_instat, ndir_total - 1);
    ASSERT_EQ(root_instat.inode_id, kRootINodeId);
    ASSERT_EQ(root_instat.inode_num, pdirs.size() - 1);
    ASSERT_EQ(root_instat.file_num, 0);
    ASSERT_EQ(root_instat.dir_num, pdirs.size() - 1);
    ASSERT_EQ(root_instat.block_num, 0);
    ASSERT_EQ(root_instat.data_size, 0);
  }

  // 2. process pending-delete-CF
  // Assertions:
  //   - empty in default-CF (except for root-inode)
  //   - empty in pending-delete-CF
  //   - empty in inode-stat-CF
  auto dummy_cb = [](int64_t) { return false; };
  uint64_t num_removed = ns_->ProcessPendingDeleteCF(dummy_cb);
  ASSERT_EQ(num_removed, ninode_total - pdirs.size());
  num_removed = observer_ns->ProcessPendingDeleteCF(dummy_cb);
  ASSERT_EQ(num_removed, ninode_total - pdirs.size());
  ns_->WaitNoPending();
  observer_ns->WaitNoPending();

  {
    uint64_t nent_in_dft = 0;
    uint64_t nent_in_pd = 0;
    auto snapshot_holder = act_ms->GetSnapshot();
    {
      auto snap = snapshot_holder->snapshot();
      auto inode_iter_holder = act_ms->GetIterator(snap, kINodeDefaultCFIndex);
      auto inode_iter = inode_iter_holder->iter();
      for (inode_iter->SeekToFirst();
           inode_iter->Valid() && inode_iter->status().ok();
           inode_iter->Next()) {
        nent_in_dft++;
      }

      auto pd_iter_holder = act_ms->GetIterator(snap, kINodePendingDeleteCFIndex);
      auto pd_iter = pd_iter_holder->iter();
      for (pd_iter->SeekToFirst();
           pd_iter->Valid() && pd_iter->status().ok();
           pd_iter->Next()) {
        nent_in_pd++;
      }
    }
    ASSERT_EQ(nent_in_dft, 1 + pdirs.size() - 1);  // +1: root, -1: pd-inode
    ASSERT_EQ(nent_in_pd, 0);

    INodeStat root_instat;
    uint64_t nent_in_instat = 0;
    auto obs_snapshot_holder = obs_ms->GetSnapshot();
    {
      auto snap = obs_snapshot_holder->snapshot();
      auto instat_iter_holder = obs_ms->GetIterator(snap, kINodeStatCFIndex);
      auto instat_iter = instat_iter_holder->iter();
      for (instat_iter->SeekToFirst();
           instat_iter->Valid() && instat_iter->status().ok();
           instat_iter->Next()) {
        nent_in_instat++;
        INodeStat instat;
        INodeStatUtils::Parse(instat_iter->value(), &instat);
        if (instat.inode_id == kRootINodeId) {
          root_instat = instat;
        }
      }
    }
    ASSERT_EQ(nent_in_instat, 1 + pdirs.size() - 1); // +1: root, -1: pd-inode
    ASSERT_EQ(root_instat.inode_id, kRootINodeId);
    ASSERT_EQ(root_instat.inode_num, pdirs.size() - 1);
    ASSERT_EQ(root_instat.file_num, 0);
    ASSERT_EQ(root_instat.dir_num, pdirs.size() - 1);
    ASSERT_EQ(root_instat.block_num, 0);
    ASSERT_EQ(root_instat.data_size, 0);
  }

  st = ns_->Delete(kSeparator + pdirs.front(), true);
  ASSERT_TRUE(st.IsOK());
  st = ns_->Delete(kSeparator + kRecycleBinDirNameString, true);
  ASSERT_FALSE(st.IsOK());  // disabled before
  WaitSynced();
}

}  // namespace dancenn
