// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "ClientNamenodeProtocol.pb.h"  // NOLINT(build/include)
#include "base/file_utils.h"
#include "namespace/meta_scanner.h"
#include "namespace/hyperfile_scanner_listener.h"
#include "test/namespace/mock_namespace.h"
#include "namespace/replica_policy_cache.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/data_centers.h"
#include "mock_edit_log_context.h"
#include "mock_edit_log_sender.h"
#include "mock_ha_state.h"
#include "mock_safe_mode.h"

DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_string(all_datacenters);
DECLARE_int32(namespace_type);
DECLARE_int32(dfs_meta_scan_interval_sec);

DECLARE_int32(hyper_block_leak_threshold_sec);
namespace dancenn {

namespace {

LogRpcInfo default_rpc_info("", 0);
auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
auto default_dfs_meta_scan_interval_sec = FLAGS_dfs_meta_scan_interval_sec;

}  // namespace

class MetaScannerTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_lease_expired_soft_limit_ms = 500;
    FLAGS_lease_expired_hard_limit_ms = 1000;
    FLAGS_datanode_keep_alive_timeout_sec = 2000;
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

    datanode_manager_ = std::make_shared<DatanodeManager>();
    block_manager_.reset(new BlockManager());
    auto edit_log_ctx = CreateContext();
    MockFSImageTransfer(db_path_).Transfer();
    ufs_env_ = UfsEnv::Create();
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                ufs_env_));
    ha_state_ = std::make_unique<MockHAState>();
    safemode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    block_manager_->set_ha_state(ha_state_.get());
    block_manager_->set_datanode_manager(datanode_manager_);
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->Start();

    ns_->StartActive();

    // add a datanode to the cluster
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    StartHeartbeat();

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender =
        std::unique_ptr<EditLogSenderBase>(
            new MockEditLogSender(edit_log_ctx, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));
  }

  void TearDown() override {
    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_dfs_meta_scan_interval_sec = default_dfs_meta_scan_interval_sec;

    stop_ = true;
    heartbeat_thread_.join();
    ns_->StopActive();
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  CreateRequestProto MakeCreateRequest() {
    CreateRequestProto create_request;
    create_request.set_src("");
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(false);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  void MakeReport(
      uint64_t block_id,
      uint64_t gs,
      uint32_t len,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
  }

  void AddFile(const std::string& path,
               uint64_t len,
               uint32_t replica,
               bool need_complete,
               AddBlockResponseProto* add_response,
               CreateResponseProto* create_response) {
    auto create_request = MakeCreateRequest();
    create_request.set_replication(replica);
    PermissionStatus p;
    ASSERT_TRUE(!ns_->CreateFile(path, p, create_request,
                                 create_response).HasException());

    cnetpp::base::IPAddress client_ip("***********");
    auto add_request = MakeAddBlockRequest();
    auto sss = ns_->AddBlock(
        path, client_ip, default_rpc_info, add_request, add_response);
    LOG(INFO) << sss.ExceptionStr();
    LOG(INFO) << sss.message();
    ASSERT_TRUE(!sss.HasException());

    BlockManager::RepeatedIncBlockReport report;
    MakeReport(add_response->block().b().blockid(),
               add_response->block().b().generationstamp(),
               len,
               cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
               &report);
    block_manager_->IncrementalBlockReport("datanode1", report);

    if (need_complete) {
      report.Clear();
      MakeReport(
          add_response->block().b().blockid(),
          add_response->block().b().generationstamp(),
          len,
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &report);
      block_manager_->IncrementalBlockReport("datanode1", report);

      CompleteRequestProto complete_request;
      complete_request.set_src(path);
      complete_request.set_clientname("client");
      complete_request.mutable_last()->CopyFrom(add_response->block().b());
      ASSERT_TRUE(!ns_->CompleteFile(path, complete_request).HasException());
    }
  }

  PermissionStatus MakePermission() {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("root");
    p.set_permission(FsPermission::GetFileDefault().ToShort());
    return p;
  }

  void AddHyperFile(const std::string& path, const std::vector<int32_t> hyper_block_size,
                    bool mock_broken, int32_t block_num) {
    std::vector<std::string> hyper_block_names;
    for (int i=0; i < hyper_block_size.size(); i++) {
      std::string tmp_name = "_part00" + std::to_string(i) + "_";
      for (int j=0; j<8; j++) {
        tmp_name += std::to_string(i);
      }
      hyper_block_names.emplace_back(tmp_name);
    }

    HyperCacheMeta hyper_file_meta;
    hyper_file_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);
    for (auto hyper_block_name : hyper_block_names) {
      hyper_file_meta.add_hyperblock(hyper_block_name);
    }
    hyper_file_meta.set_stripewidth(1024 * 1024);

    CreateResponseProto response;
    auto create_hyper_file_request = MakeCreateRequest();
    create_hyper_file_request.set_createparent(true);
    auto attr_hyper_file = create_hyper_file_request.add_attr();
    attr_hyper_file->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    attr_hyper_file->set_name(kHyperFileKey);
    attr_hyper_file->set_value(hyper_file_meta.SerializeAsString());

    auto p = MakePermission();
    ASSERT_TRUE(
        !ns_->CreateFile(path, p, create_hyper_file_request, &response).HasException());

    int i = 0, j = 0;
    for (auto hyper_block_name : hyper_block_names) {
      std::string hyper_block_full_path = path + hyper_block_name;

      HyperCacheMeta hyper_block_meta;
      hyper_block_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);

      CreateResponseProto create_hyper_block_response;
      auto create_hyper_block_request = MakeCreateRequest();
      create_hyper_block_request.set_src(hyper_block_full_path);
      create_hyper_block_request.set_replication(1);

      auto attr_hyper_block = create_hyper_block_request.add_attr();
      attr_hyper_block->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
      attr_hyper_block->set_name(kHyperBlockKey);
      attr_hyper_block->set_value(hyper_block_meta.SerializeAsString());
      ASSERT_TRUE(!ns_->CreateFile(hyper_block_full_path, p, create_hyper_block_request,
                                   &create_hyper_block_response).HasException());
      j ++;
      if (mock_broken && j + 1 == hyper_block_names.size()) {
        // Skip creating the last hyper block, so that a broken HyperFile is created.
        // Used by test cases which need to verify whether dancenn perform as expected when hyperfile is broken.
        break;
      }
    }

    for (auto hyper_block_name : hyper_block_names) {
      std::string hyper_block_full_path = path + hyper_block_name;
      cnetpp::base::IPAddress client_ip("***********");
      auto add_request = MakeAddBlockRequest();
      AddBlockResponseProto add_response;

      for (int j = 0; j < block_num; j++) {
        if (j > 0) {
          // Add 2nd block
          auto block_id_1st = add_response.block().b().blockid();
          auto gs_1st = add_response.block().b().generationstamp();

          add_request.mutable_previous()->CopyFrom(add_response.block().b());
          add_request.mutable_previous()->set_generationstamp(gs_1st);
          add_request.mutable_previous()->set_blockid(block_id_1st);
          add_request.mutable_previous()->set_numbytes(hyper_block_size[i]);
        }

        add_response.Clear();
        auto add_status = ns_->AddBlock(hyper_block_full_path,
                                        client_ip,
                                        default_rpc_info,
                                        add_request,
                                        &add_response);
        DLOG(ERROR) << add_status.ToString();

        ASSERT_FALSE(add_status.HasException());
        DLOG(ERROR) << add_response.ShortDebugString();

        BlockManager::RepeatedIncBlockReport report;
        MakeReport(add_response.block().b().blockid(),
                   add_response.block().b().generationstamp(), hyper_block_size[i],
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
                   &report);

        auto dn_locs = add_response.block().locs();
        for (auto& dn_loc: dn_locs) {
          block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(), report);
        }

        report.Clear();
        MakeReport(add_response.block().b().blockid(),
                   add_response.block().b().generationstamp(), hyper_block_size[i],
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                   &report);
        for (auto& dn_loc: dn_locs) {
          block_manager_->IncrementalBlockReport(dn_loc.id().datanodeuuid(), report);
        }
      }


      CompleteRequestProto complete_request;
      complete_request.set_src(hyper_block_full_path);
      complete_request.set_clientname("client");
      complete_request.mutable_last()->CopyFrom(add_response.block().b());
      complete_request.mutable_last()->set_numbytes(hyper_block_size[i]);
      DLOG(ERROR) << complete_request.ShortDebugString();
      ASSERT_TRUE(!ns_->CompleteFile(hyper_block_full_path, complete_request).HasException());

      i++;

      if (mock_broken && i + 1 == hyper_block_names.size()) {
        // Skip creating the last hyper block, so that a broken HyperFile is created.
        // Used by test cases which need to verify whether dancenn perform as expected when hyperfile is broken.
        break;
      }
    }

  }

  void StartHeartbeat() {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&latch, this]() {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
      cnetpp::base::IPAddress ip("***********");
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);
      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          DatanodeManager::RepeatedCmds cmds;
          datanode_manager_->Heartbeat(request, &cmds);
          if (!heartbeated) {
            latch.CountDown();
            heartbeated = true;
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  bool stop_;
  bool pause_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::unique_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<UfsEnv> ufs_env_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
};

#if 0
TEST_F(MetaScannerTest, TestRecycleHyperFile) {
  MetaScanner scanner(ns_.get());
  LOG(INFO) << "1";
  ASSERT_TRUE(!ns_->MkDirs("/hyperfile", PermissionStatus(),
                           true).HasException());

  AddHyperFile("/hyperfile/test_hyper1", {10,20,30}, false, 1);
  AddHyperFile("/hyperfile/test_hyper2", {20,30,40}, false, 1);

  ns_->Delete("/hyperfile/test_hyper2", false);

  std::shared_ptr<MetaScanner::Listener> listener =
      std::static_pointer_cast<MetaScanner::Listener>(
          std::make_shared<HyperfileScannerListener>(ns_.get()));
  scanner.RegisterListener(listener);

  FLAGS_hyper_block_leak_threshold_sec = 1;
  std::this_thread::sleep_for(std::chrono::seconds(1));
  AddHyperFile("/hyperfile/test_hyper3", {20,30,40}, false, 1);
  ns_->Delete("/hyperfile/test_hyper3", false);
  scanner.Start();

  std::this_thread::sleep_for(std::chrono::seconds(2));
  // The hyperblock of hyper2 is deleted by gc.
  ASSERT_FALSE(ns_->FileExists("/hyperfile/test_hyper2_part000_00000000"));
  ASSERT_FALSE(ns_->FileExists("/hyperfile/test_hyper2_part001_11111111"));
  ASSERT_FALSE(ns_->FileExists("/hyperfile/test_hyper2_part002_22222222"));

  // The hyperblock of hyper3 is leaked. But not expired yet, which should not be deleted.
  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper3_part000_00000000"));
  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper3_part001_11111111"));
  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper3_part002_22222222"));

  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper1_part000_00000000"));
  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper1_part001_11111111"));
  ASSERT_TRUE(ns_->FileExists("/hyperfile/test_hyper1_part002_22222222"));

  scanner.Stop();
}
#endif

TEST_F(MetaScannerTest, Test01) {
  MetaScanner scanner(ns_.get());
  // Mock the following directory tree:
  //
  // /                        [id: kRootINodeId]
  //  /a                      [id: kRootINodeId + 1]
  //    /b                    [id: kRootINodeId + 2]
  //      /c1                 [id: kRootINodeId + 3]
  //         /d1              [id: kRootINodeId + 4]
  //            /e1.txt       [id: kRootINodeId + 6]
  //            /e2.txt       [id: kRootINodeId + 7]
  //         /d2              [id: kRootINodeId + 5]
  //            /e1.txt       [id: kRootINodeId + 8]
  //            /e2.txt       [id: kRootINodeId + 9]
  //         /d3.txt          [id: kRootINodeId + 10]
  //      /c2                 [id: kRootINodeId + 11]
  //
  //
  LOG(INFO) << "1";
  ASSERT_TRUE(!ns_->MkDirs("/a/b/c1/d1", PermissionStatus(),
                           true).HasException());
  LOG(INFO) << "2";
  ASSERT_TRUE(!ns_->SetReplicaPolicy("/a/b/c1/d1", kCentralizePolicy,
                           kEnforceDCUnspecified).HasException());
  LOG(INFO) << "3";
  ASSERT_TRUE(!ns_->MkDirs("/a/b/c1/d2", PermissionStatus(),
                           true).HasException());
  LOG(INFO) << "4";
  ASSERT_TRUE(!ns_->SetReplicaPolicy("/a/b/c1/d2", kDistributePolicy,
                           "LF,HL").HasException());
  AddBlockResponseProto arsp;
  CreateResponseProto crsp;
  crsp.Clear();
  AddFile("/a/b/c1/d1/e1.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d1/e2.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d2/e1.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d2/e2.txt", 0, 1, true, &arsp, &crsp);
  crsp.Clear();
  AddFile("/a/b/c1/d3.txt", 0, 1, true, &arsp, &crsp);
  ASSERT_TRUE(!ns_->MkDirs("/a/b/c2", PermissionStatus(), true).HasException());
  std::map<std::string, std::pair<int, INode_Type>> inode_info;
  // inode_info.emplace("/",
  //   std::pair<int, INode_Type>(0, INode_Type_kSymLink));
  inode_info.emplace("/a/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c2/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d1/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d2/",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d3.txt",
    std::pair<int, INode_Type>(0, INode_Type_kFile));
  inode_info.emplace("/a/b/c1/d1/e1.txt",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d1/e2.txt",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d2/e1.txt",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  inode_info.emplace("/a/b/c1/d2/e2.txt",
    std::pair<int, INode_Type>(0, INode_Type_kDirectory));
  size_t inode_info_size = inode_info.size();
  class CountListener : public MetaScanner::Listener {
   public:
    CountListener(
        std::map<std::string, std::pair<int, INode_Type>> &inode_info)
        : MetaScanner::Listener("count_listener"), inode_info_(inode_info) {
    }
    std::vector<INodeID> ScanIndexes() override {
      return {kRootINodeId};
    }
    bool Handle(const std::string& full_path, const INode& inode) override {
      inode_info_[full_path].first++;
      inode_info_[full_path].second = inode.type();
      return true;
    }
   private:
    std::map<std::string, std::pair<int, INode_Type>>& inode_info_;
  };

  std::shared_ptr<MetaScanner::Listener> listener =
      std::static_pointer_cast<MetaScanner::Listener>(
          std::make_shared<CountListener>(inode_info));
  scanner.RegisterListener(listener);

  scanner.Start();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  // ASSERT_EQ(inode_info_size, inode_info.size());
  // ASSERT_EQ(inode_info["/"].first, 1);
  // ASSERT_EQ(inode_info["/"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a"].first, 1);
  ASSERT_EQ(inode_info["/a"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b"].first, 1);
  ASSERT_EQ(inode_info["/a/b"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b/c1"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b/c2"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c2"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b/c1/d1"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d1"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b/c1/d2"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d2"].second, INode_Type_kDirectory);
  ASSERT_EQ(inode_info["/a/b/c1/d3.txt"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d3.txt"].second, INode_Type_kFile);
  ASSERT_EQ(inode_info["/a/b/c1/d1/e1.txt"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d1/e1.txt"].second, INode_Type_kFile);
  ASSERT_EQ(inode_info["/a/b/c1/d1/e2.txt"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d1/e2.txt"].second, INode_Type_kFile);
  ASSERT_EQ(inode_info["/a/b/c1/d2/e1.txt"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d2/e1.txt"].second, INode_Type_kFile);
  ASSERT_EQ(inode_info["/a/b/c1/d2/e2.txt"].first, 1);
  ASSERT_EQ(inode_info["/a/b/c1/d2/e2.txt"].second, INode_Type_kFile);

  scanner.Stop();
}

}  // namespace dancenn

