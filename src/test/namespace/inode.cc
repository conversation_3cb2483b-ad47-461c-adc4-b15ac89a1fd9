// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/01
// Description

#include "test/namespace/inode.h"

#include <utility>

#include "namespace/xattr.h"

namespace dancenn {

PermissionStatusBuilder& PermissionStatusBuilder::SetUsername(
    const std::string& username) {
  proto_.set_username(username);
  return *this;
}

PermissionStatusBuilder& PermissionStatusBuilder::SetGroupname(
    const std::string& groupname) {
  proto_.set_groupname(groupname);
  return *this;
}

PermissionStatusBuilder& PermissionStatusBuilder::SetPermission(
    uint32_t permission) {
  proto_.set_permission(permission);
  return *this;
}

PermissionStatus PermissionStatusBuilder::Build() {
  return std::move(proto_);
}

FileUnderConstructionFeatureBuilder& FileUnderConstructionFeatureBuilder::
    SetClientName(const std::string& client_name) {
  proto_.set_client_name(client_name);
  return *this;
}

FileUnderConstructionFeatureBuilder& FileUnderConstructionFeatureBuilder::
    SetClientMachine(const std::string& client_machine) {
  proto_.set_client_machine(client_machine);
  return *this;
}

FileUnderConstructionFeature FileUnderConstructionFeatureBuilder::Build() {
  return proto_;
}

QuotaPolicyBuilder& QuotaPolicyBuilder::SetINodeLimit(uint64_t inode_limit) {
  proto_.set_inode_limit(inode_limit);
  return *this;
}

QuotaPolicyBuilder& QuotaPolicyBuilder::SetFileLimit(uint64_t file_limit) {
  proto_.set_file_limit(file_limit);
  return *this;
}

QuotaPolicyBuilder& QuotaPolicyBuilder::SetDirLimit(uint64_t dir_limit) {
  proto_.set_dir_limit(dir_limit);
  return *this;
}

QuotaPolicyBuilder& QuotaPolicyBuilder::SetDataSizeLimit(
    uint64_t data_size_limit) {
  proto_.set_data_size_limit(data_size_limit);
  return *this;
}

QuotaPolicyProto QuotaPolicyBuilder::Build() {
  return std::move(proto_);
}

INodeBuilder& INodeBuilder::SetId(uint64_t id) {
  proto_.set_id(id);
  return *this;
}

INodeBuilder& INodeBuilder::SetParentId(uint64_t parent_id) {
  proto_.set_parent_id(parent_id);
  return *this;
}

INodeBuilder& INodeBuilder::SetName(const std::string& name) {
  proto_.set_name(name);
  return *this;
}

INodeBuilder& INodeBuilder::SetPermission(const PermissionStatus& permission) {
  proto_.mutable_permission()->CopyFrom(permission);
  return *this;
}

INodeBuilder& INodeBuilder::SetType(INode::Type type) {
  proto_.set_type(type);
  return *this;
}

INodeBuilder& INodeBuilder::SetMtime(uint64_t mtime) {
  proto_.set_mtime(mtime);
  return *this;
}

INodeBuilder& INodeBuilder::SetAtime(uint64_t atime) {
  proto_.set_atime(atime);
  return *this;
}

INodeBuilder& INodeBuilder::SetStoragePolicyId(uint32_t storage_policy_id) {
  proto_.set_storage_policy_id(storage_policy_id);
  return *this;
}

INodeBuilder& INodeBuilder::AddBlock(const cloudfs::BlockProto& block) {
  *proto_.add_blocks() = block;
  return *this;
}

INodeBuilder& INodeBuilder::SetUc(const FileUnderConstructionFeature& uc) {
  *proto_.mutable_uc() = uc;
  return *this;
}

INodeBuilder& INodeBuilder::SetXAttr(const char* name,
                                     const google::protobuf::Message& value) {
  XAttrs::SetProtoBufXAttr(name, &value, &proto_);
  return *this;
}

INodeBuilder& INodeBuilder::SetStatus(INode::Status status) {
  proto_.set_status(status);
  return *this;
}

INode INodeBuilder::Build() {
  return std::move(proto_);
}

INodeStatBuilder& INodeStatBuilder::SetINodeId(uint64_t inode_id) {
  proto_.set_inodeid(inode_id);
  return *this;
}

INodeStatBuilder& INodeStatBuilder::SetINodeNum(uint64_t inode_num) {
  proto_.set_inodenum(inode_num);
  return *this;
}

INodeStatBuilder& INodeStatBuilder::SetFileNum(uint64_t file_num) {
  proto_.set_filenum(file_num);
  return *this;
}

INodeStatBuilder& INodeStatBuilder::SetDirNum(uint64_t dir_num) {
  proto_.set_dirnum(dir_num);
  return *this;
}

INodeStatBuilder& INodeStatBuilder::SetBlockNum(uint64_t block_num) {
  proto_.set_blocknum(block_num);
  return *this;
}

INodeStatBuilder& INodeStatBuilder::SetDataSize(uint64_t data_size) {
  proto_.set_datasize(data_size);
  return *this;
}

cloudfs::GetINodeStatResponseProto::INodeStat INodeStatBuilder::Build() {
  return std::move(proto_);
}

GetINodeStatResponseBuilder& GetINodeStatResponseBuilder::AddINodeStat(
    const cloudfs::GetINodeStatResponseProto::INodeStat& stat) {
  *proto_.add_inodestats() = stat;
  return *this;
}

GetINodeStatResponseBuilder& GetINodeStatResponseBuilder::AddMissingINodeId(
    uint64_t inode_id) {
  proto_.add_missinginodeids(inode_id);
  return *this;
}

GetINodeStatResponseBuilder& GetINodeStatResponseBuilder::SetUpdateTsInSec(
    int64_t tsInSec) {
  proto_.set_updatetsinsec(tsInSec);
  return *this;
}

GetINodeStatResponseBuilder& GetINodeStatResponseBuilder::SetSnapshotTxId(
    int64_t txid) {
  proto_.set_snapshottxid(txid);
  return *this;
}

cloudfs::GetINodeStatResponseProto GetINodeStatResponseBuilder::Build() {
  return std::move(proto_);
}

}  // namespace dancenn
