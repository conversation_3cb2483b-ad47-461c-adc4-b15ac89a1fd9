//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: sunguo<PERSON> <<EMAIL>>
//

#include <gtest/gtest.h>

#include "namespace/xattr.h"

namespace dancenn {

TEST(XAttrs, UpdateINodeXAttrs) {

  std::string mock_name = "mock.test.name";
  std::string mock_value = "2";
  XAttrProto x;
  x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
  x.set_name(mock_name);
  x.set_value(mock_value);

  INode file;
  ASSERT_EQ(XAttrs::UpdateINodeXAttrs(x, true, true, &file).code(), Code::kOK);
  ASSERT_EQ(file.xattrs_size(), 1);
  ASSERT_EQ(file.xattrs(0).namespace_(),
            ::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
  ASSERT_EQ(file.xattrs(0).name(), mock_name);
  ASSERT_EQ(file.xattrs(0).value(), mock_value);
}

}  // namespace dancenn
