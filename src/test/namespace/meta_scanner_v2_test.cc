#include "namespace/meta_scanner_v2.h"

#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "test/namespace/gmock_meta_storage.h"
#include "test/namespace/mock_meta_scanner_v2.h"

DECLARE_bool(run_ut);

namespace dancenn {

class MetaScannerV2Test : public testing::Test {
 public:
  void SetUp() override {
    meta_storage_ = std::make_shared<GMockMetaStorage>();
    meta_scanner_v2_ = std::make_shared<MetaScannerV2>(meta_storage_.get());
    meta_scanner_v2_->Start();
  }

  void TearDown() override {
    meta_scanner_v2_->Stop();
    meta_scanner_v2_.reset();
    meta_storage_.reset();
  }

 public:
  std::shared_ptr<MetaStorage> meta_storage_;
  std::shared_ptr<MetaScannerV2> meta_scanner_v2_;
};

TEST_F(MetaScannerV2Test, RunOneTask) {
  SynchronizedClosure done;
  auto task = std::make_shared<GMockMetaScannerTask>();
  EXPECT_CALL(*task, SetTaskID(1));
  EXPECT_CALL(*task, GetTaskID())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(1));
  EXPECT_CALL(*task, SetMetaScanner(meta_scanner_v2_.get())).Times(1);
  EXPECT_CALL(*task, SetMetaStorage(meta_storage_.get())).Times(1);
  EXPECT_CALL(*task, ToString()).Times(testing::AtLeast(1));
  EXPECT_CALL(*task, Run(testing::_))
      .Times(1)
      .WillOnce([&done](void* arg) -> bool {
        done.Run();
        return true;
      });
  EXPECT_CALL(*task, GetDelayUs()).Times(1).WillOnce(testing::Return(0));
  MetaScannerTaskID id = meta_scanner_v2_->AddScanTask(task);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(task->GetTaskID(), 1);
  auto tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 1);
  ASSERT_EQ(tasks.at(1).get(), task.get());
  done.Await();
  bool ret = meta_scanner_v2_->EraseScanTask(id, true);
  ASSERT_TRUE(ret);
  tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 0);
}

TEST_F(MetaScannerV2Test, RunLoopTask) {
  SynchronizedClosure done1, done2;
  int counter = 0;
  auto task = std::make_shared<GMockMetaScannerTask>();
  EXPECT_CALL(*task, SetTaskID(1));
  EXPECT_CALL(*task, GetTaskID())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(1));
  EXPECT_CALL(*task, SetMetaScanner(meta_scanner_v2_.get())).Times(1);
  EXPECT_CALL(*task, SetMetaStorage(meta_storage_.get())).Times(1);
  EXPECT_CALL(*task, ToString()).Times(testing::AtLeast(1));
  EXPECT_CALL(*task, Run(testing::_))
      .Times(5)
      .WillRepeatedly([&done1, &done2, &counter, this](void* arg) {
        if (counter++ < 4) {
          EXPECT_TRUE(meta_scanner_v2_->AddScanTask(1));
          return true;
        }
        done1.Await();
        EXPECT_TRUE(meta_scanner_v2_->EraseScanTask(1, true));
        done2.Run();
        return true;
      });
  EXPECT_CALL(*task, GetDelayUs())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(0));
  MetaScannerTaskID id = meta_scanner_v2_->AddScanTask(task);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(task->GetTaskID(), 1);
  auto tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 1);
  ASSERT_EQ(tasks.at(1).get(), task.get());
  done1.Run();
  done2.Await();
  bool ret = meta_scanner_v2_->EraseScanTask(id, true);
  ASSERT_TRUE(ret);
  tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 0);
}

TEST_F(MetaScannerV2Test, RunLoopForeverTask) {
  SynchronizedClosure done;
  int counter = 0;
  auto task = std::make_shared<GMockMetaScannerTask>();
  EXPECT_CALL(*task, SetTaskID(1));
  EXPECT_CALL(*task, GetTaskID())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(1));
  EXPECT_CALL(*task, SetMetaScanner(meta_scanner_v2_.get())).Times(1);
  EXPECT_CALL(*task, SetMetaStorage(meta_storage_.get())).Times(1);
  EXPECT_CALL(*task, ToString()).Times(testing::AtLeast(1));
  EXPECT_CALL(*task, Run(testing::_))
      .Times(testing::AtLeast(1))
      .WillRepeatedly([&done, &counter, this](void* arg) {
        if (counter++ == 5) {
          done.Run();
        }
        meta_scanner_v2_->AddScanTask(1);
        return true;
      });
  EXPECT_CALL(*task, GetDelayUs())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(0));
  MetaScannerTaskID id = meta_scanner_v2_->AddScanTask(task);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(task->GetTaskID(), 1);
  auto tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 1);
  ASSERT_EQ(tasks.at(1).get(), task.get());
  done.Await();
  bool ret = meta_scanner_v2_->EraseScanTask(id, true);
  ASSERT_TRUE(true);
  tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 0);
}

TEST_F(MetaScannerV2Test, TaskWithWorkLoad) {
  SynchronizedClosure done;
  SynchronizedClosure work_load_done;
  auto work_load = std::make_shared<GMockMetaScannerWorkTask>();
  EXPECT_CALL(*work_load, Run(testing::_))
      .Times(1)
      .WillOnce([&work_load_done](void* arg) {
        work_load_done.Run();
        return true;
      });
  auto task = std::make_shared<GMockMetaScannerTask>();
  EXPECT_CALL(*task, SetTaskID(1));
  EXPECT_CALL(*task, GetTaskID())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(1));
  EXPECT_CALL(*task, SetMetaScanner(meta_scanner_v2_.get())).Times(1);
  EXPECT_CALL(*task, SetMetaStorage(meta_storage_.get())).Times(1);
  EXPECT_CALL(*task, ToString()).Times(testing::AtLeast(2));
  EXPECT_CALL(*task, Run(testing::_))
      .Times(1)
      .WillOnce([&done, &work_load, this](void* arg) -> bool {
        meta_scanner_v2_->AddWorkTask(work_load);
        done.Run();
        return true;
      });
  EXPECT_CALL(*task, GetDelayUs()).Times(1).WillOnce(testing::Return(0));
  MetaScannerTaskID id = meta_scanner_v2_->AddScanTask(task);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(task->GetTaskID(), 1);
  auto tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 1);
  ASSERT_EQ(tasks.at(1).get(), task.get());
  done.Await();
  work_load_done.Await();
  bool ret = meta_scanner_v2_->EraseScanTask(id, true);
  ASSERT_TRUE(true);
  tasks = meta_scanner_v2_->TestGetAllTasks();
  ASSERT_EQ(tasks.size(), 0);
}

}  // namespace dancenn
