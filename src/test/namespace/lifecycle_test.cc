// Copyright 2023 <PERSON><PERSON> <<EMAIL>>

#include <cnetpp/base/ip_address.h>
#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "ClientNamenodeProtocol.pb.h"

#include "base/defer.h"
#include "base/file_utils.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/edit_log_context.h"
#include "namespace/lifecycle_policy_util.h"
#include "namespace/namespace.h"

#include "test/namespace/mock_namespace.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"

DECLARE_uint32(blkid_cmd_max_num_blocks);
DECLARE_string(default_storage_class);
DECLARE_bool(lifecycle_enable);
DECLARE_bool(lifecycle_scanner_force_start_next_group);
DECLARE_bool(lifecycle_scanner_force_skip_next_group);
DECLARE_uint64(lifecycle_scanner_start_next_group_period_ms);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_scanner_enable);

namespace dancenn {

using cloudfs::LifecyclePolicyProto;
using cloudfs::ExpirationRuleProto;
using cloudfs::TransitionRuleProto;
using cloudfs::StorageClassProto_MIN;
using cloudfs::StorageClassProto_ARRAYSIZE;
using cloudfs::CreateRequestProto;
using cloudfs::CreateResponseProto;
using cloudfs::AddBlockRequestProto;
using cloudfs::AddBlockResponseProto;

#define CLS_NONE  StorageClassProto::NONE
#define CLS_HOT   StorageClassProto::HOT
#define CLS_WARM  StorageClassProto::WARM
#define CLS_COLD  StorageClassProto::COLD
#define CLS_IA    StorageClassProto::IA
#define CLS_AR    StorageClassProto::AR

class LifecycleTest : public testing::Test {
 public:
  void SetUp() override {
    google::SetVLOGLevel("*", 0);
  }
  void TearDown() override {}

  void InitNS() {
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    dn_mgr_ = std::make_shared<dancenn::DatanodeManager>();
    edit_log_ctx_ = CreateContext();
    blk_mgr_.reset(new BlockManager(edit_log_ctx_));
    blk_mgr_->TestOnlySetEditLogCtx(edit_log_ctx_);
    MockFSImageTransfer(db_path_).Transfer();
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx_,
                                blk_mgr_,
                                dn_mgr_,
                                std::make_shared<DataCenters>(),
                                UfsEnv::Create()));
    ha_state_ = std::make_unique<MockHAState>();
    safemode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    blk_mgr_->set_ha_state(ha_state_.get());
    blk_mgr_->set_safemode(safemode_.get());
    blk_mgr_->set_ns(ns_.get());
    dn_mgr_->set_block_manager(blk_mgr_.get());

    ns_->Start();
    ns_->StartActive();

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = std::unique_ptr<EditLogSenderBase>(
        new MockEditLogSender(edit_log_ctx_, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));

    // add a datanode to the cluster
    for (int i = 0; i < dnuuids_.size(); i++) {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid(dnuuids_.at(i));
      reg.mutable_datanodeid()->set_infoport(1234);
      reg.mutable_datanodeid()->set_ipcport(1234);
      reg.mutable_datanodeid()->set_hostname("hostname");
      reg.mutable_datanodeid()->set_xferport(1234);
      cnetpp::base::IPAddress ip(dnips_.at(i));
      dn_mgr_->Register(reg.datanodeid(), &reg, ip);
      dn_mgr_->RefreshConfig();
    }

    StartHeartbeat(true);

    perm_.set_username("root");
    perm_.set_groupname("supergroup");
    perm_.set_permission(FsPermission::GetFileDefault().ToShort());
  }

  void FiniNS() {
    stop_ = true;
    heartbeat_thread_.join();
    ns_->StopActive();
    ns_->Stop();
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  void StartHeartbeat(bool only_once) {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&]() {
        bool heartbeated = false;
        do {
          std::this_thread::sleep_for(std::chrono::seconds(1));
          if (pause_) {
            continue;
          }

          HeartbeatRequestProto req;
          for (int i = 0; i < dnuuids_.size(); i++) {
            auto reg =
                cloudfs::datanode::DatanodeRegistrationProto::default_instance();
            reg.mutable_datanodeid()->set_datanodeuuid(dnuuids_.at(i));
            cnetpp::base::IPAddress ip(dnips_.at(i));
            RepeatedStorageReport reports;
            auto rpt = reports.Add();
            rpt->set_storageuuid("storage1");
            rpt->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
            rpt->mutable_storage()->set_storageuuid("storage1");
            req.mutable_registration()->CopyFrom(reg);
            req.mutable_reports()->CopyFrom(reports);

            DatanodeManager::RepeatedCmds cmds;
            dn_mgr_->Heartbeat(req, &cmds);
          }
          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        } while (!stop_ && !only_once);
    });
    latch.Await();
  }

  void AddDir(const std::string& path) {
    Status st = ns_->MkDirs(path, perm_, true);
    ASSERT_TRUE(st.IsOK());
  }

  void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      const std::string& dnuuid,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      StorageClassProto cls,
      BlockManager::RepeatedIncBlockReport* report) {
    auto rpt = report->Add();
    rpt->set_storageuuid(dnuuid);
    auto blk = rpt->add_blocks();
    blk->mutable_block()->set_blockid(block_id);
    blk->mutable_block()->set_genstamp(gs);
    blk->mutable_block()->set_numbytes(len);
    blk->set_storageclass(cls);
    blk->set_status(state);
  }

  void AddFile(const std::string& path,
               uint32_t replication,
               StorageClassProto cls) {
    const uint32_t block_size = FLAGS_dfs_block_size;

    // create
    CreateRequestProto create_req;
    create_req.set_src(path);
    create_req.mutable_masked()->set_perm(0);
    create_req.set_clientname("client");
    create_req.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_req.set_replication(replication);
    create_req.set_createparent(true);
    create_req.set_blocksize(block_size);
    CreateResponseProto create_resp;
    Status st = ns_->CreateFile(path, perm_, create_req, &create_resp);
    ASSERT_TRUE(st.IsOK());

    // add block
    AddBlockRequestProto add_req;
    add_req.set_src(path);
    add_req.set_clientname(client_name_);
    AddBlockResponseProto add_resp;
    cnetpp::base::IPAddress client_ip("***********");
    st = ns_->AddBlock(path, client_ip, rpc_info_, add_req, &add_resp);
    ASSERT_TRUE(st.IsOK());
    auto& exblk = add_resp.block().b();

    // report
    int rand_idx = std::rand() % dnuuids_.size();
    for (int i = 0; i < replication; i++) {
      int dnid = (rand_idx + i) % dnuuids_.size();
      const std::string& dnuuid = dnuuids_[dnid];

      BlockManager::RepeatedIncBlockReport report;
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 block_size,
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
                 cls,
                 &report);
      blk_mgr_->IncrementalBlockReport(dnuuid, report);
      ns_->GetBlockReportManager()->IncrementalBlockReport(dnid, dnuuid, report);

      report.Clear();
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 block_size,
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                 cls,
                 &report);
      blk_mgr_->IncrementalBlockReport(dnuuid, report);
      ns_->GetBlockReportManager()->IncrementalBlockReport(dnid, dnuuid, report);
    }

    // complete
    CompleteRequestProto comp_req;
    comp_req.set_src(path);
    comp_req.set_clientname(client_name_);
    comp_req.mutable_last()->CopyFrom(exblk);
    comp_req.mutable_last()->set_numbytes(block_size);
    st = ns_->CompleteFile(path, comp_req);
    ASSERT_TRUE(st.IsOK());

    // upload
    bool persisted = false;
    for (int i = 0; i < replication; i++) {
      int dnid = (rand_idx + i) % dnuuids_.size();
      const std::string& dnuuid = dnuuids_[dnid];

      BlockManager::RepeatedIncBlockReport report;
      MakeReport(exblk.blockid(),
                 exblk.generationstamp(),
                 block_size,
                 dnuuid,
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED,
                 cls,
                 &report);
      blk_mgr_->IncrementalBlockReport(dnuuid, report);
      ns_->GetBlockReportManager()->IncrementalBlockReport(dnid, dnuuid, report);

      ns_->WaitNoPending();

      if (!persisted) {
        persisted = true;

        report.Clear();
        MakeReport(exblk.blockid(),
                   exblk.generationstamp(),
                   block_size,
                   dnuuid,
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
                   cls,
                   &report);
        blk_mgr_->IncrementalBlockReport(dnuuid, report);
        ns_->GetBlockReportManager()->IncrementalBlockReport(dnid, dnuuid, report);
      }
    }
  }

  void ConstructDirTree(
      const std::string& path,
      int max_level,
      int ndir_per_level,
      int nfile_per_level,
      int replication,
      StorageClassProto cls,
      uint64_t* ndir_total,
      uint64_t* nfile_total,
      uint64_t* nblock_total,
      uint64_t* nlbyte_total,
      uint64_t* nreplica_total,
      uint64_t* npbyte_total) {
    ConstructDirTreeImpl(path,
                         0,
                         max_level,
                         ndir_per_level,
                         nfile_per_level,
                         replication,
                         cls,
                         ndir_total,
                         nfile_total,
                         nblock_total,
                         nlbyte_total,
                         nreplica_total,
                         npbyte_total);
    CHECK_EQ((*nblock_total) * replication, (*nreplica_total));
    CHECK_EQ((*nlbyte_total) * replication, (*npbyte_total));
    CHECK_EQ((*nblock_total) * FLAGS_dfs_block_size, (*nlbyte_total));
    CHECK_EQ((*nreplica_total) * FLAGS_dfs_block_size, (*npbyte_total));
  }

  void ConstructDirTreeImpl(
      const std::string& path,
      int cur_level,
      int max_level,
      int ndir_per_level,
      int nfile_per_level,
      int replication,
      StorageClassProto cls,
      uint64_t* ndir_total,
      uint64_t* nfile_total,
      uint64_t* nblock_total,
      uint64_t* nlbyte_total,
      uint64_t* nreplica_total,
      uint64_t* npbyte_total) {
    if (cur_level > max_level) {
      return;
    }

    const uint64_t nblk_per_file = 1;
    int child_idx = 0;
    for (int i = 0; i < std::min(ndir_per_level, nfile_per_level); i++) {
      {
        std::string dir = absl::StrFormat(
            "%s/level-%d-child-%d",
            path, cur_level, child_idx);
        AddDir(dir);
        child_idx++;

        ConstructDirTreeImpl(dir,
                             cur_level + 1,
                             max_level,
                             ndir_per_level,
                             nfile_per_level,
                             replication,
                             cls,
                             ndir_total,
                             nfile_total,
                             nblock_total,
                             nlbyte_total,
                             nreplica_total,
                             npbyte_total);
        *ndir_total += 1;
      }
      {
        std::string file = absl::StrFormat(
            "%s/level-%d-child-%d",
            path, cur_level, child_idx);
        AddFile(file, replication, cls);
        child_idx++;

        *nfile_total += 1;
        *nblock_total += 1 * nblk_per_file;
        *nlbyte_total += 1 * nblk_per_file * FLAGS_dfs_block_size;
        *nreplica_total += 1 * nblk_per_file * replication;
        *npbyte_total += 1 * nblk_per_file * replication * FLAGS_dfs_block_size;
      }
    }

    if (nfile_per_level < ndir_per_level) {
      for (int i = nfile_per_level; i < ndir_per_level; i++) {
        std::string dir = absl::StrFormat(
            "%s/level-%d-child-%d",
            path, cur_level, child_idx);
        AddDir(dir);
        child_idx++;

        ConstructDirTreeImpl(dir,
                             cur_level + 1,
                             max_level,
                             ndir_per_level,
                             nfile_per_level,
                             replication,
                             cls,
                             ndir_total,
                             nfile_total,
                             nblock_total,
                             nlbyte_total,
                             nreplica_total,
                             npbyte_total);
        *ndir_total += 1;
      }
    }
    if (ndir_per_level < nfile_per_level) {
      for (int i = ndir_per_level; i < nfile_per_level; i++) {
        std::string file = absl::StrFormat(
            "%s/level-%d-child-%d",
            path, cur_level, child_idx);
        AddFile(file, replication, cls);
        child_idx++;

        *nfile_total += 1;
        *nblock_total += 1 * nblk_per_file;
        *nlbyte_total += 1 * nblk_per_file * FLAGS_dfs_block_size;
        *nreplica_total += 1 * nblk_per_file * replication;
        *npbyte_total += 1 * nblk_per_file * replication * FLAGS_dfs_block_size;
      }
    }
  }

  void ConstructLExpRulePolicy(const std::set<int> exp_days,
                           const bool atime_base_ttl,
                           LifecyclePolicyProto* policy) {
    CHECK_NOTNULL(policy);
    CHECK_LE(exp_days.size(), 1);
    policy->Clear();
    if (!exp_days.empty()) {
      policy->mutable_exprule()->set_days(*exp_days.begin());
      if (atime_base_ttl) {
        policy->mutable_exprule()->set_expiration_type(
            ::cloudfs::ExpirationTypeProto::ATIME_BASED);
      } else {
        policy->mutable_exprule()->set_expiration_type(
            ::cloudfs::ExpirationTypeProto::MTIME_BASED);
      }
    }
  }


  void ConstructLCPolicy(const std::set<StorageClassProto> default_cls,
                         const std::set<int> exp_days,
                         const std::vector<int>& trans_days,
                         const std::vector<StorageClassProto>& trans_cls,
                         LifecyclePolicyProto* policy,
                         bool recycle_whole_dir = false) {
    CHECK_NOTNULL(policy);
    CHECK_LE(default_cls.size(), 1);
    CHECK_LE(exp_days.size(), 1);
    CHECK_EQ(trans_days.size(), trans_cls.size());
    policy->Clear();
    if (!default_cls.empty()) {
      policy->set_defaultclass(*default_cls.begin());
    }
    if (!exp_days.empty()) {
      policy->mutable_exprule()->set_days(*exp_days.begin());
    }
    if (policy->has_exprule()) {
      policy->mutable_exprule()->set_recycle_whole_directory(recycle_whole_dir);
    }
    for (int i = 0; i < trans_days.size(); i++) {
      TransitionRuleProto trule;
      trule.set_days(trans_days.at(i));
      trule.set_targetclass(trans_cls.at(i));
      policy->add_transrules()->CopyFrom(trule);
    }
  }

  void SetLCPolicy(
      const std::string& dir,
      const LifecyclePolicyProto& policy) {
    SetLifecyclePolicyRequestProto req;
    SetLifecyclePolicyResponseProto resp;
    req.set_path(dir);
    req.mutable_lifecyclepolicy()->CopyFrom(policy);
    Status st = ns_->SetLifecyclePolicy(dir, req, &resp, ugi_, rpc_info_);
    ASSERT_TRUE(st.IsOK());
  }

  void UnsetLCPolicy(
      const std::string& dir) {
    UnsetLifecyclePolicyRequestProto req;
    UnsetLifecyclePolicyResponseProto resp;
    req.set_path(dir);
    Status st = ns_->UnsetLifecyclePolicy(dir, req, &resp, ugi_, rpc_info_);
    ASSERT_TRUE(st.IsOK());
  }

 protected:
  bool stop_;
  bool pause_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> blk_mgr_;
  std::shared_ptr<DatanodeManager> dn_mgr_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  const std::string client_name_ = "client";
  const std::vector<std::string> dnuuids_ = {
      "dn-1",
      "dn-2",
      "dn-3",
      "dn-4",
      "dn-5",
  };
  const std::vector<std::string> dnips_ = {
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
  };
  UserGroupInfo ugi_;
  LogRpcInfo rpc_info_;
  PermissionStatus perm_;
  const int64_t ms_per_day = 24 * 60 * 60 * 1000;
};

TEST_F(LifecycleTest, ValidLifecyclePolicy) {
  LifecyclePolicyProto policy;

  // 1. 'defaultclass'
  ConstructLCPolicy({ CLS_HOT }, { 1 }, {}, {}, &policy);
  EXPECT_TRUE(LifecyclePolicyIsValid(policy));

  // 2. 'exprule'
  ConstructLCPolicy({}, { 100 }, {}, {}, &policy);
  EXPECT_TRUE(LifecyclePolicyIsValid(policy));

  // 3. 'transrules'
  ConstructLCPolicy({},
                    {},
                    { 1, 2, 3, 4, 5 },
                    { CLS_HOT, CLS_WARM, CLS_COLD, CLS_IA, CLS_AR },
                    &policy);
  EXPECT_TRUE(LifecyclePolicyIsValid(policy));
}

TEST_F(LifecycleTest, InvalidLifecyclePolicy) {
  LifecyclePolicyProto policy;

  // 1. empty
  policy.Clear();
  EXPECT_FALSE(LifecyclePolicyIsValid(policy));

  // 2. invalid 'defaultclass'
  ConstructLCPolicy({ CLS_NONE }, {}, {}, {}, &policy);
  EXPECT_FALSE(LifecyclePolicyIsValid(policy));

  // 3. invalid 'exprule'
  ConstructLCPolicy({}, { -1 }, {}, {}, &policy);
  EXPECT_FALSE(LifecyclePolicyIsValid(policy));

  // 4. invalid 'transrules'
  ConstructLCPolicy({}, {}, { -1 }, { CLS_HOT }, &policy);
  EXPECT_FALSE(LifecyclePolicyIsValid(policy));
  ConstructLCPolicy({}, {}, { 10 }, { CLS_NONE }, &policy);
  EXPECT_FALSE(LifecyclePolicyIsValid(policy));
}

TEST_F(LifecycleTest, Expiration) {
  LifecyclePolicyProto policy;

  // 1. no 'exprule'
  policy.Clear();
  ConstructLCPolicy({ CLS_WARM }, {}, {}, {}, &policy);
  EXPECT_FALSE(LifecycleNeedExpire(policy, 1));

  // 2. not expired
  ConstructLCPolicy({}, { 10 }, {}, {}, &policy);
  EXPECT_FALSE(LifecycleNeedExpire(policy, 5 * ms_per_day));
  EXPECT_FALSE(LifecycleNeedExpire(policy, 10 * ms_per_day - 1));

  // 3. expired
  ConstructLCPolicy({}, { 10 }, {}, {}, &policy);
  EXPECT_TRUE(LifecycleNeedExpire(policy, 10 * ms_per_day));
  EXPECT_TRUE(LifecycleNeedExpire(policy, 11 * ms_per_day));
}

TEST_F(LifecycleTest, EffectiveStorageClass) {
  // DefaultClass:    COLD
  //                  |
  // Time:            0 ----- 2 ----- 4 ----- 6 ----- 8 ----- 10 -->
  //                  |       |       |       |       |       |
  // TransitionRule:  HOT     HOT     WARM    COLD    IA      AR
  LifecyclePolicyProto policy;
  StorageClassProto cls;

  // 1. neither set, use internal default value
  ConstructLCPolicy({}, { 10 }, {}, {}, &policy);
  GetEffectiveStorageClass(policy, 0 * ms_per_day, &cls);
  EXPECT_EQ(cls, default_storage_class);
  GetEffectiveStorageClass(policy, 10 * ms_per_day, &cls);
  EXPECT_EQ(cls, default_storage_class);
  GetEffectiveStorageClass(policy, 20 * ms_per_day, &cls);
  EXPECT_EQ(cls, default_storage_class);

  // 2. only 'defaultclass' set
  //
  // DefaultClass:    COLD
  //                  |
  // Time:            0 ----->
  ConstructLCPolicy({ CLS_COLD }, {}, {}, {}, &policy);
  EXPECT_NE(policy.defaultclass(), default_storage_class);
  GetEffectiveStorageClass(policy, 0 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.defaultclass());
  GetEffectiveStorageClass(policy, 10 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.defaultclass());
  GetEffectiveStorageClass(policy, 20 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.defaultclass());

  // 3. only 'transrule' set
  //
  // Time:            0 ----- 2 ----- 4 ----- 6 ----- 8 -->
  //                  |       |       |       |       |
  // TransitionRule:  HOT     WARM    COLD    IA      AR
  ConstructLCPolicy(
      {},
      {},
      { 0, 2, 4, 6, 8 },
      { CLS_HOT, CLS_WARM, CLS_COLD, CLS_IA, CLS_AR },
      &policy);
  GetEffectiveStorageClass(policy, 0 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(0).targetclass());
  GetEffectiveStorageClass(policy, 1 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(0).targetclass());
  GetEffectiveStorageClass(policy, 2 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(1).targetclass());
  GetEffectiveStorageClass(policy, 3 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(1).targetclass());
  GetEffectiveStorageClass(policy, 4 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(2).targetclass());
  GetEffectiveStorageClass(policy, 5 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(2).targetclass());
  GetEffectiveStorageClass(policy, 6 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(3).targetclass());
  GetEffectiveStorageClass(policy, 7 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(3).targetclass());
  GetEffectiveStorageClass(policy, 8 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(4).targetclass());
  GetEffectiveStorageClass(policy, 9 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(4).targetclass());

  // 4. both set, prefer 'defaultclass'
  //
  // DefaultClass:    COLD
  //                  |
  // Time:            0 ----- 2 -->
  //                  |       |
  // TransitionRule:  HOT     AR
  ConstructLCPolicy(
      { CLS_COLD },
      {},
      { 0, 2 },
      { CLS_HOT, CLS_AR },
      &policy);
  GetEffectiveStorageClass(policy, 0 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.defaultclass());
  GetEffectiveStorageClass(policy, 1 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.defaultclass());
  GetEffectiveStorageClass(policy, 2 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(1).targetclass());
  GetEffectiveStorageClass(policy, 3 * ms_per_day, &cls);
  EXPECT_EQ(cls, policy.transrules(1).targetclass());

  // 5. duplicated 'transrule' on same ttl, prefer colder one
  //
  // Time:            0 ----- 2 ----- 4 -->
  //                  |       |       |
  // TransitionRule:  HOT     WARM    COLD
  //                  & WARM  & COLD  & HOT
  ConstructLCPolicy(
      {},
      {},
      { 0, 0, 2, 2, 4, 4 },
      { CLS_HOT, CLS_WARM, CLS_WARM, CLS_COLD, CLS_COLD, CLS_HOT },
      &policy);
  EXPECT_TRUE(LifecyclePolicyIsValid(policy));
  GetEffectiveStorageClass(policy, 0 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_WARM);
  GetEffectiveStorageClass(policy, 1 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_WARM);
  GetEffectiveStorageClass(policy, 2 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_COLD);
  GetEffectiveStorageClass(policy, 3 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_COLD);
  GetEffectiveStorageClass(policy, 4 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_COLD);
  GetEffectiveStorageClass(policy, 5 * ms_per_day, &cls);
  EXPECT_EQ(cls, CLS_COLD);
}

TEST_F(LifecycleTest, EffectiveLifecyclePolicy) {
  InitNS();
  DEFER( [&](){ FiniNS(); } );

  std::string target = "/dir1/dir2/dir3/target_dir";
  std::string dir3 = "/dir1/dir2/dir3";
  std::string dir2 = "/dir1/dir2";
  std::string dir1 = "/dir1";
  std::string root = "/";
  MetaStorage* ms = ns_->meta_storage();

  // prepare target dir path
  CreateRequestProto create_req;
  create_req.set_src(target);
  create_req.mutable_masked()->set_perm(0);
  create_req.set_clientname("client_name");
  create_req.set_createflag(::cloudfs::CreateFlagProto::CREATE);
  create_req.set_createparent(true);
  create_req.set_replication(1);
  create_req.set_blocksize(FLAGS_dfs_block_size);
  Status st = ns_->MkDirs(target, perm_, true);
  ASSERT_TRUE(st.IsOK());
  GetFileInfoResponseProto gfi_resp;
  st = ns_->GetFileInfo(
      target, NetworkLocationInfo(), false, false, &gfi_resp, ugi_);
  ASSERT_TRUE(st.IsOK());

  // 1. no valid policy on path, use internal default LifecyclePolicy
  LifecyclePolicyProto policy;
  GetEffectiveLifecyclePolicy(ms, gfi_resp.fs().fileid(), nullptr/* filter */, &policy);
  EXPECT_EQ(policy.defaultclass(), default_lifecycle_policy.defaultclass());
  EXPECT_EQ(policy.has_exprule(), default_lifecycle_policy.has_exprule());
  EXPECT_EQ(policy.transrules_size(), default_lifecycle_policy.transrules_size());

  // 2. current inode has valid policy
  LifecyclePolicyProto expected_policy;
  ConstructLCPolicy(
      { CLS_HOT },
      { 10 },
      { 5 },
      { CLS_COLD },
      &expected_policy);
  SetLCPolicy(target, expected_policy);
  GetEffectiveLifecyclePolicy(ms, gfi_resp.fs().fileid(), nullptr/* filter */, &policy);
  EXPECT_EQ(policy.defaultclass(), expected_policy.defaultclass());
  EXPECT_EQ(policy.exprule().days(), expected_policy.exprule().days());
  EXPECT_EQ(policy.transrules_size(), expected_policy.transrules_size());
  EXPECT_EQ(policy.transrules(0).days(), expected_policy.transrules(0).days());
  EXPECT_EQ(policy.transrules(0).targetclass(),
            expected_policy.transrules(0).targetclass());

  // 3. get nearest parent that has valid policy
  UnsetLCPolicy(target);

  ConstructLCPolicy({ CLS_HOT }, { 1 }, {}, {}, &policy);
  SetLCPolicy(dir1, policy);
  ConstructLCPolicy({ CLS_WARM }, { 2 }, {}, {}, &policy);
  SetLCPolicy(dir2, policy);
  ConstructLCPolicy({ CLS_COLD }, { 3 }, {}, {}, &policy);
  SetLCPolicy(dir3, policy);

  GetEffectiveLifecyclePolicy(ms, gfi_resp.fs().fileid(), nullptr/* filter */, &policy);
  EXPECT_EQ(policy.defaultclass(), CLS_COLD);
  EXPECT_EQ(policy.exprule().days(), 3);

  // 4. feature disabled, use internal default StorageClass
  FLAGS_lifecycle_enable = false;
  StorageClassProto cls, expected_cls;
  GetCurrentStorageClass(ms, gfi_resp.fs().fileid(), &cls);
  StorageClassName2ID(FLAGS_default_storage_class, &expected_cls);
  EXPECT_EQ(cls, expected_cls);
}

TEST_F(LifecycleTest, EffectiveLifecyclePolicyWithFilter) {
  InitNS();
  DEFER([&]() { FiniNS(); });

  std::string target = "/dir1/dir2/dir3/target_dir";
  std::string dir3 = "/dir1/dir2/dir3";
  std::string dir2 = "/dir1/dir2";
  std::string dir1 = "/dir1";
  MetaStorage* ms = ns_->meta_storage();

  // Prepare directory structure
  Status st = ns_->MkDirs(target, perm_, true);
  ASSERT_TRUE(st.IsOK());
  GetFileInfoResponseProto gfi_resp;
  st = ns_->GetFileInfo(
      target, NetworkLocationInfo(), false, false, &gfi_resp, ugi_);
  ASSERT_TRUE(st.IsOK());
  INodeID target_id = gfi_resp.fs().fileid();

  // 1. Test with filter that accepts all policies
  {
    LifecyclePolicyProto policy;
    INodeID effective_id;
    auto accept_all = [](LifecyclePolicyProto* p) { return true; };

    // When no policies exist, should return default policy
    GetEffectiveLifecyclePolicy(
        ms, target_id, accept_all, &policy, &effective_id);
    EXPECT_EQ(policy.defaultclass(), default_lifecycle_policy.defaultclass());
    EXPECT_EQ(policy.has_exprule(), default_lifecycle_policy.has_exprule());
  }

  // 2. Test with filter that rejects all policies
  {
    LifecyclePolicyProto policy;
    INodeID effective_id;
    auto reject_all = [](LifecyclePolicyProto* p) { return false; };

    // Set policies on all directories
    LifecyclePolicyProto dir_policy;
    ConstructLCPolicy({CLS_HOT}, {1}, {}, {}, &dir_policy);
    SetLCPolicy(dir1, dir_policy);
    ConstructLCPolicy({CLS_WARM}, {2}, {}, {}, &dir_policy);
    SetLCPolicy(dir2, dir_policy);
    ConstructLCPolicy({CLS_COLD}, {3}, {}, {}, &dir_policy);
    SetLCPolicy(dir3, dir_policy);

    // Should return default policy since filter rejects all
    GetEffectiveLifecyclePolicy(
        ms, target_id, reject_all, &policy, &effective_id);
    EXPECT_EQ(policy.defaultclass(), default_lifecycle_policy.defaultclass());
  }

  // 3. Test with filter that only accepts specific storage class
  {
    LifecyclePolicyProto policy;
    INodeID effective_id;
    auto accept_warm = [](LifecyclePolicyProto* p) {
      return p->defaultclass() == CLS_WARM;
    };

    // Should find and return the WARM policy from dir2
    GetEffectiveLifecyclePolicy(
        ms, target_id, accept_warm, &policy, &effective_id);
    EXPECT_EQ(policy.defaultclass(), CLS_WARM);
    EXPECT_EQ(policy.exprule().days(), 2);

    // Get dir2's inode ID for comparison
    GetFileInfoResponseProto dir2_resp;
    st = ns_->GetFileInfo(
        dir2, NetworkLocationInfo(), false, false, &dir2_resp, ugi_);
    ASSERT_TRUE(st.IsOK());
    EXPECT_EQ(effective_id, dir2_resp.fs().fileid());
  }
}

CreateRequestProto MakeCreateRequest2() {
  CreateRequestProto create_request;
  create_request.set_src("");
  create_request.mutable_masked()->set_perm(0);
  create_request.set_clientname("client");
  create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
  create_request.set_createparent(true);
  create_request.set_replication(1);
  create_request.set_blocksize(128 * 1024 * 1024);
  return create_request;
}

TEST_F(LifecycleTest, EffectiveLifecyclePolicyWithFilter2) {
  InitNS();
  DEFER([&]() { FiniNS(); });

  std::string target = "/dir_root/dir2/dir3/target_file";
  std::string dir3 = "/dir_root/dir2/dir3";
  std::string dir2 = "/dir_root/dir2";
  std::string dir1 = "/dir_root";
  MetaStorage* ms = ns_->meta_storage();

  // Prepare directory structure
  CreateRequestProto create_request = MakeCreateRequest2();
  create_request.set_src(target);
  CreateResponseProto create_resp;
  Status st = ns_->CreateFile(target, perm_, create_request, &create_resp);
  ASSERT_TRUE(st.IsOK());

  GetFileInfoResponseProto gfi_resp;
  st = ns_->GetFileInfo(
      target, NetworkLocationInfo(), false, false, &gfi_resp, ugi_);
  ASSERT_TRUE(st.IsOK());
  INodeID target_id = gfi_resp.fs().fileid();

  // 2. set atime based expiration rule
  {
    // Set policies on all directories
    LifecyclePolicyProto dir_policy;
    ConstructLExpRulePolicy({1}, false, &dir_policy);
    SetLCPolicy(dir1, dir_policy);
    ConstructLExpRulePolicy({2}, true, &dir_policy);
    SetLCPolicy(dir2, dir_policy);
    ConstructLExpRulePolicy({3}, false, &dir_policy);
    SetLCPolicy(dir3, dir_policy);

    INodeID effective_id;
    auto filter = [this](LifecyclePolicyProto* policy) {
      if (!policy->has_exprule()) {
        return false;
      }
      if (!IsExpRuleValid(policy->exprule())) {
        return false;
      }
      // only atime-based policy is needed to update access time
      if (!policy->exprule().has_expiration_type() ||
          policy->exprule().expiration_type() !=
              ::cloudfs::ExpirationTypeProto::ATIME_BASED) {
        return false;
      }
      return true;
    };

    // Should find and return the WARM policy from dir2
    LifecyclePolicyProto policy;
    GetEffectiveLifecyclePolicy(ms, target_id, filter, &policy, &effective_id);
    EXPECT_EQ(policy.exprule().days(), 2);

    // Get dir2's inode ID for comparison
    GetFileInfoResponseProto dir2_resp;
    st = ns_->GetFileInfo(
        dir2, NetworkLocationInfo(), false, false, &dir2_resp, ugi_);
    ASSERT_TRUE(st.IsOK());
    EXPECT_EQ(effective_id, dir2_resp.fs().fileid());

    TtlATimeCollectorConfig config;
    config.batch_size = 3;
    config.timeout_ms = 100;
    std::unique_ptr<TtlATimeCollector> ttl_access_collector =
        std::make_unique<TtlATimeCollector>(ns_.get(), nullptr, config);

    LOG(INFO) << "start ttl_access_collector";
    ns_->ttl_access_collector_ = std::move(ttl_access_collector);
    ns_->ttl_access_collector_->Start();

    // Prepare directory structure
    GetFileInfoResponseProto gfi_resp2;
    st = ns_->GetFileInfo(
        target, NetworkLocationInfo(), false, false, &gfi_resp2, ugi_);
    ASSERT_TRUE(st.IsOK());

    std::this_thread::sleep_for(std::chrono::seconds(1));

    {
      ATimeToBeUpdate atime;
      ns_->meta_storage()->GetTtlATime(effective_id, &atime);
      LOG(INFO) << "atime: " << atime.DebugString();

      LifecycleScanScrubOp op;
      op.namespace_ = ns_.get();
      op.meta_storage_ = ns_->meta_storage_;

      INode inode;
      op.meta_storage_->GetINode(target_id, &inode);
      auto expired_time = op.GetExpiredTime(inode, effective_id, policy);
      LOG(INFO) << "expired_time: " << expired_time;
      EXPECT_GT(expired_time, 0);
    }

    {
      ns_->ttl_access_collector_->AddAccessRecord(1242132, 1242132);
      ns_->ttl_access_collector_->AddAccessRecord(1242133, 1242132);
      ns_->ttl_access_collector_->Flush();

      ATimeToBeUpdateProtos atimes;
      ATimeToBeUpdate atime1;
      ATimeToBeUpdate atime2;
      ns_->meta_storage_->GetTtlATime(1242132, &atime1);
      LOG(INFO) << "atime1: " << atime1.DebugString();
      ns_->meta_storage_->GetTtlATime(1242133, &atime2);
      LOG(INFO) << "atime2: " << atime2.DebugString();

      ns_->meta_storage_->CleanupOrphanedTtlATimes();
    }

    {
      LOG(INFO) << "start cleanup";
      ns_->ttl_access_collector_->AddAccessRecord(2242132, 1242132);
      ns_->ttl_access_collector_->AddAccessRecord(2242133, 1242132);
      ns_->ttl_access_collector_->Flush();

      ATimeToBeUpdateProtos atimes;
      ATimeToBeUpdate atime1;
      ATimeToBeUpdate atime2;
      ns_->meta_storage_->GetTtlATime(2242132, &atime1);
      LOG(INFO) << "atime1: " << atime1.DebugString();
      ns_->meta_storage_->GetTtlATime(2242133, &atime2);
      LOG(INFO) << "atime2: " << atime2.DebugString();

      auto timer_task_manager =
          std::make_unique<TimerTaskManager>(ns_->thread_pool_);
      ns_->timer_task_manager_ = std::move(timer_task_manager);
      ns_->StartTimerTaskManager();
      ns_->timer_task_manager_->AddPeriodicTask(
          [this]() { ns_->meta_storage_->CleanupOrphanedTtlATimes(); },
          1,
          "CleanupOrphanedTtlATimes2");

      std::this_thread::sleep_for(std::chrono::seconds(5));
      EXPECT_FALSE(ns_->meta_storage_->GetTtlATime(2242132, &atime1).IsOK());
      EXPECT_FALSE(ns_->meta_storage_->GetTtlATime(2242133, &atime2).IsOK());
    }

    {
      ns_->meta_storage()->DeleteTtlATime(effective_id);
    }
  }
}


TEST_F(LifecycleTest, LifecycleScannerScrub) {
  FLAGS_lifecycle_scanner_force_skip_next_group = true;
  FLAGS_lifecycle_scanner_force_start_next_group = false;
  InitNS();
  DEFER( [&](){ FiniNS(); } );
  FLAGS_recycle_bin_scanner_enable = false;

  // clang-format off
  struct {
    std::string dir_path;
    int nlevel;
    int ndir_per_level;
    int nfile_per_level;
    int replication;
    StorageClassProto src_stcls;
    StorageClassProto dst_stcls;
    int expire_days;
    bool expire_whole_dir;

    // stat
    uint64_t ndir_total;
    uint64_t nfile_total;
    uint64_t nblock_total;    // logical block
    uint64_t nlbyte_total;    // logical byte
    uint64_t nreplica_total;  // physical replica
    uint64_t npbyte_total;    // physical byte
  } dir_info[] = {
    { "/hot_dir",       3, 3, 1, 3, CLS_HOT,  CLS_HOT,  999, false, 0, 0, 0, 0, 0, 0, },
    { "/warm_dir",      3, 3, 2, 2, CLS_WARM, CLS_WARM, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/cold_dir",      3, 3, 3, 1, CLS_COLD, CLS_COLD, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/hot2warm_dir",  3, 2, 1, 2, CLS_HOT,  CLS_WARM, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/hot2cold_dir",  3, 2, 2, 3, CLS_HOT,  CLS_COLD, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/warm2hot_dir",  3, 2, 3, 2, CLS_WARM, CLS_HOT,  999, false, 0, 0, 0, 0, 0, 0, },
    { "/warm2cold_dir", 3, 1, 1, 1, CLS_WARM, CLS_COLD, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/cold2hot_dir",  3, 1, 2, 2, CLS_COLD, CLS_HOT,  999, false, 0, 0, 0, 0, 0, 0, },
    { "/cold2warm_dir", 3, 1, 3, 3, CLS_COLD, CLS_WARM, 999, false, 0, 0, 0, 0, 0, 0, },
    { "/expire_dir",    3, 1, 5, 2, CLS_COLD, CLS_COLD, 0, false,   0, 0, 0, 0, 0, 0, },
    { "/expire_dirw",   3, 1, 5, 2, CLS_COLD, CLS_COLD, 0, true,   0, 0, 0, 0, 0, 0, },
  };
  // clang-format on

  for (auto&& dinfo : dir_info) {
    // construct directory tree
    ConstructDirTree(dinfo.dir_path,
                     dinfo.nlevel,
                     dinfo.ndir_per_level,
                     dinfo.nfile_per_level,
                     dinfo.replication,
                     dinfo.src_stcls,
                     &dinfo.ndir_total,
                     &dinfo.nfile_total,
                     &dinfo.nblock_total,
                     &dinfo.nlbyte_total,
                     &dinfo.nreplica_total,
                     &dinfo.npbyte_total);

    // set lifecycle policy
    LifecyclePolicyProto policy;
    ConstructLCPolicy({ dinfo.dst_stcls },
                      { dinfo.expire_days },
                      {},
                      {},
                      &policy,
                      dinfo.expire_whole_dir);
    SetLCPolicy(dinfo.dir_path, policy);

    // debug info
    LOG(INFO) << absl::StrFormat(
        "constructed directory tree '%s':\tndir %lu,\tnfile %lu,\t"
        "nblock %lu,\tnlbyte %lu,\tnreplica %lu,\tnpbyte %lu",
        dinfo.dir_path, dinfo.ndir_total, dinfo.nfile_total,
        dinfo.nblock_total, dinfo.nlbyte_total,
        dinfo.nreplica_total, dinfo.npbyte_total);
  }

  // wait until LifecycleScanner do the scrub
  FLAGS_lifecycle_scanner_force_start_next_group = true;
  FLAGS_lifecycle_scanner_force_skip_next_group = false;
  // XXX ugly
  // wait at least 2 round of scrub, due to effective interval between
  // GenerateStat() & ApplyLifecyclePolicy() in LifecycleScanScrubOp
  std::this_thread::sleep_for(std::chrono::seconds(30));
  FLAGS_lifecycle_scanner_force_start_next_group = false;
  FLAGS_lifecycle_scanner_force_skip_next_group = true;
  std::this_thread::sleep_for(std::chrono::seconds(30));

  LOG(INFO) << "wait until lifecycle scrub done.";

  // check stat result
  for (auto&& dinfo : dir_info) {
    if (dinfo.expire_days == 0) {
      continue;
    }

    StorageClassStatProto stat;
    Status st = ns_->GetStorageClassStat(dinfo.dir_path, &stat);
    ASSERT_TRUE(st.IsOK());

    for (int i = static_cast<int>(StorageClassProto_MIN);
         i < static_cast<int>(StorageClassProto_ARRAYSIZE);
         i++) {
      if (i == dinfo.src_stcls) {
        EXPECT_EQ(stat.numreplica(i), dinfo.nreplica_total);
        EXPECT_EQ(stat.numbyte(i), dinfo.npbyte_total);
      } else {
        EXPECT_EQ(stat.numreplica(i), 0);
        EXPECT_EQ(stat.numbyte(i), 0);
      }
    }
    if (dinfo.src_stcls == dinfo.dst_stcls) {
      EXPECT_EQ(stat.numlogicalblockexpected(dinfo.dst_stcls),
                dinfo.nblock_total);
      EXPECT_EQ(stat.numlogicalbyteexpected(dinfo.dst_stcls),
                dinfo.nlbyte_total);
      EXPECT_EQ(stat.numlogicalblock(dinfo.dst_stcls),
                dinfo.nblock_total);
      EXPECT_EQ(stat.numlogicalbyte(dinfo.dst_stcls),
                dinfo.nlbyte_total);
    } else {
      // skip, a little bit complicated
    }
  }

  // check expiration result
  for (auto&& dinfo : dir_info) {
    GetFileInfoResponseProto gfi_resp;
    Status st = ns_->GetFileInfo(
        dinfo.dir_path, NetworkLocationInfo(), false, false, &gfi_resp, ugi_);
    ASSERT_TRUE(st.IsOK());
    if (dinfo.expire_days == 0) {
      EXPECT_FALSE(gfi_resp.has_fs());
    } else {
      EXPECT_TRUE(gfi_resp.has_fs());
    }
  }

  // check transition result
  uint64_t expected_num_cmds[StorageClassProto_ARRAYSIZE] = {0};
  for (auto&& dinfo : dir_info) {
    if (dinfo.expire_days == 0) {
      // If recycle-bin is disabled, the expired files will be deleted
      // instantly, generating no more trasition commands.
      if (!FLAGS_recycle_bin_enable) {
        continue;
      }
      // Otherwise, they will be moved into RecycleBin and inherit the policy
      // along the path '/.RECYCLE.BIN/username/YYYY-MM-DD'.
    }

    switch (dinfo.dst_stcls) {
      case CLS_COLD: {
        // transit all replica to COLD (only if not COLD)
        if (dinfo.src_stcls != dinfo.dst_stcls) {
          expected_num_cmds[dinfo.dst_stcls] += dinfo.nreplica_total;
        }
        break;
      }
      case CLS_WARM: {
        // fall through
      }
      case CLS_HOT: {
        // transit only one replica to HOT/WARM (only if not match),
        // the other to COLD (only if not COLD)
        if (dinfo.src_stcls != dinfo.dst_stcls) {
          expected_num_cmds[dinfo.dst_stcls] += dinfo.nblock_total;
        }
        if (dinfo.src_stcls != CLS_COLD) {
          expected_num_cmds[CLS_COLD] += dinfo.nreplica_total - dinfo.nblock_total;
        }
        break;
      }
      default:
        CHECK(false) << "unsupported yet";
    }
  }
  // check cmd stored inside
  for (int i = static_cast<int>(StorageClassProto_MIN);
       i < static_cast<int>(StorageClassProto_ARRAYSIZE);
       i++) {
    EXPECT_EQ(ns_->lifecycle_scanner()->num_cmds_staged_[i],
              expected_num_cmds[i]);
    LOG(INFO) << absl::StrFormat(
        "transit to cls %d, got(inner) %lu, expected %lu",
        i, ns_->lifecycle_scanner()->num_cmds_staged_[i],
        expected_num_cmds[i]);
  }
  // check cmd got from GetStorageClassCmd()
  FLAGS_blkid_cmd_max_num_blocks = 99999999;
  uint64_t got_num_cmds[StorageClassProto_ARRAYSIZE] = {0};
  for (auto& dnuuid : dnuuids_) {
    cloudfs::datanode::HeartbeatResponseProto resp;
    resp.Clear();
    ns_->lifecycle_scanner()->GetStorageClassCmd(dnuuid, "bpid-XXX", &resp);
    for (auto dncmd : resp.cmds()) {
      auto cmd = dncmd.blkidcmd();
      switch (cmd.action()) {
        case BlockIdCommandProto_Action_SET_STORAGE_CLASS_HOT: {
          got_num_cmds[CLS_HOT] += cmd.blockids_size();
          break;
        }
        case BlockIdCommandProto_Action_SET_STORAGE_CLASS_WARM: {
          got_num_cmds[CLS_WARM] += cmd.blockids_size();
          break;
        }
        case BlockIdCommandProto_Action_SET_STORAGE_CLASS_COLD: {
          got_num_cmds[CLS_COLD] += cmd.blockids_size();
          break;
        }
        default:
          CHECK("unreachable" == nullptr);
      }
    }
  }
  for (int i = static_cast<int>(StorageClassProto_MIN);
       i < static_cast<int>(StorageClassProto_ARRAYSIZE);
       i++) {
    EXPECT_EQ(got_num_cmds[i], expected_num_cmds[i]);
    LOG(INFO) << absl::StrFormat(
        "transit to stcls %d, got %lu, expected %lu",
        i, got_num_cmds[i], expected_num_cmds[i]);
  }
}

}  // namespace dancenn
