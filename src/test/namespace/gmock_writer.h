// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2021/09/18
// Description

#ifndef TEST_NAMESPACE_GMOCK_WRITER_H_
#define TEST_NAMESPACE_GMOCK_WRITER_H_

#include <gmock/gmock.h>

#include "namespace/meta_storage_writer.h"
#include "namespace/meta_storage_write_task.h"

namespace dancenn {
namespace meta_storage {

class GMockWriter : public Writer {
 public:
  GMockWriter() : Writer(nullptr) {
  }

  MOCK_METHOD1(Push, void(WriteTask* task));
  MOCK_METHOD1(PushBGTask, void(WriteTask* task));
};

}  // namespace meta_storage
}  // namespace dancenn

#endif  // TEST_NAMESPACE_GMOCK_WRITER_H_
