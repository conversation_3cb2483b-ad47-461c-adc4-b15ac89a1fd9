// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/05/25
// Description

#include <cnetpp/base/string_piece.h>  // For StringPiece,
#include <gflags/gflags.h>             // For DECLARE_string, etc.
#include <gmock/gmock.h>               // For StrictMock, etc.
#include <gtest/gtest.h>               // For Test, etc.
#include <proto/generated/cloudfs/ClientNamenodeProtocol.pb.h>  // For AppendRequestProto, CompleteRequestProto, HAServiceStateResponseProto, etc.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For CommitBlockSynchronizationRequestProto.
#include <proto/generated/cloudfs/HAServiceProtocol.pb.h>  // For HAServiceStateProto.
#include <proto/generated/cloudfs/RpcHeader.pb.h>  // For RpcRequestHeaderProto.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For ExtendedBlockProto, NamespaceInfoProto, StorageInfoProto, FsServerDefaultsProto, etc.
#include <proto/generated/cloudfs/lifecycle.pb.h>    // For StorageClassProto.
#include <proto/generated/cloudfs/xattr.pb.h>        // For XAttrProto.
#include <proto/generated/dancenn/inode.pb.h>        // For INode.
#include <proto/generated/dancenn/status_code.pb.h>  // For StatusCode.
#include <rocksdb/slice.h>                           // For Slice.
#include <rocksdb/write_batch.h>                     // For WriteBatch.

#include <cstdint>     // For int32_t.
#include <functional>  // For function.
#include <memory>      // For unique_ptr, shared_ptr, etc.
#include <sstream>     // For ostringstream.
#include <string>      // For string.
#include <utility>     // For pair, make_pair.
#include <vector>      // For vector.

#include "acc/meta_sync_context.h"
#include "base/closure.h"  // For SynchronizedRpcClosure.
#include "base/constants.h"  // For kRootINodeId, kGenerationStampV2Key, kDefaultLayoutVersion, etc.
#include "base/defer.h"            // For DEFER.
#include "base/file_utils.h"       // For FileUtils.
#include "base/java_exceptions.h"  // For JavaExceptions.
#include "base/rwlock_manager.h"   // For RWLockManager.
#include "base/status.h"           // For Code, Status.
#include "base/time_util.h"
#include "base/vlock.h"                // For vshared_lock.
#include "block_manager/block.h"       // For Block, kInvalidBlockID, BlockID.
#include "block_manager/block_info.h"  // For BlockUCState.
#include "block_manager/block_report_handler.h"  // For BlockReportManager.
#include "datanode_manager/datanode_manager.h"   // For DatanodeManager.
#include "datanode_manager/storage_policy.h"     // For StoragePolicyId
#include "edit/edit_log_context.h"               // For EditLogContextBase.
#include "edit/sender_base.h"                    // For EditLogSenderBase.
#include "ha/operations.h"                       // For OperationsCategory.
#include "lease/lease_manager_base.h"            // For LeaseManagerBase.
#include "namespace/access_counter_manager.h"    // For AccessCounterManager.
#include "namespace/file_finalizer.h"            // For FileFinalizerBase.
#include "namespace/inode.h"                     // For INodeInPath.
#include "namespace/meta_storage.h"              // For MetaStorageIterHolder.
#include "namespace/meta_storage_constants.h"    // For kLeaseCFIndex.
#include "namespace/meta_storage_write_task.h"   // For WriteTask.
#include "namespace/namespace.h"             // For NameSpace, BGDeletionTask.
#include "namespace/user_group_info.h"       // For UserGroupInfo.
#include "rpc/rpc_controller.h"              // For RpcController.
#include "security/key_manager.h"            // For KeyManager.
#include "test/base/gmock_rwlock_manager.h"  // For GMockRWLockManager.
#include "test/block_manager/gmock_block_manager.h"  // For GMockBlockManager.
#include "test/dancenn_test_enviroment.h"            // For GetProgramDirectory.
#include "test/datanode_manager/gmock_datanode_manager.h"  // for GMockDatanodeManager.
#include "test/edit/gmock_edit_log_context.h"        // For GMockEditLogContext.
#include "test/gmock_edit_log_sender.h"              // For GMockEditLogSender.
#include "test/gmock_ha_state.h"                     // For GMockHAState.
#include "test/gmock_job_manager.h"                  // For GMockJobManager.
#include "test/lease/gmock_lease_manager.h"          // For GMockLeaseManager.
#include "test/matcher.h"                            // For RocksDBSliceEq.
#include "test/namespace/gmock_file_finalizer.h"     // For GMockFileFinalizer.
#include "test/namespace/gmock_meta_storage.h"  // For GMockWriteBatch, GMockMetaStorage.
#include "test/namespace/gmock_writer.h"  // For GMockWriter.
#include "test/namespace/inode.h"  // For INodeBuilder, FileUnderConstructionFeatureBuilder.
#include "test/proto/generated/cloudfs/hdfs.h"  // For BlockProtoBuilder.
#include "test/proto/generated/dancenn/block_info_proto.h"  // For BlockInfoProtoBuilder.
#include "test/safemode/gmock_safemode.h"   // For GMockSafeMode.
#include "test/tos/mock_tos_cred.h"         // For GMockTosCredentialProvider.
#include "test/ufs/mock_ufs.h"              // For GMockUfs.
#include "test/ufs/tos_ufs/mock_tos_ufs.h"  // For GMockTosUfs.

DECLARE_string(fsimage_dir);
DECLARE_string(fsimage_file_name);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_int32(namespace_type);

// For TEST_F(NameSpaceTestV2, GetServerDefaults).
DECLARE_uint64(dfs_block_size);
DECLARE_int32(dfs_bytes_per_checksum);
DECLARE_int32(dfs_client_write_packet_size);
DECLARE_int32(dfs_replication);
DECLARE_int32(io_file_buffer_size);
DECLARE_bool(dfs_encrypt_data_transfer);
DECLARE_uint64(fs_trash_interval);
DECLARE_string(dfs_checksum_type);

// For TEST_F(NameSpaceTestV2, GetHAServiceStateXXX).
DECLARE_bool(standby_read_enabled);
DECLARE_bool(dancenn_observe_mode_on);
DECLARE_bool(standby_read_enabled_for_main_read);
DECLARE_bool(standby_read_enabled_for_all_read);

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class NameSpaceTestV2 : public testing::Test {
 public:
  void SetUp() override {
    ha_state_ = std::make_unique<testing::StrictMock<GMockHAState>>();
    safemode_ = std::make_unique<testing::StrictMock<GMockSafeMode>>();
    lease_manager_ = new testing::StrictMock<GMockLeaseManager>();
    edit_log_ctx_ =
        std::make_shared<testing::StrictMock<GMockEditLogContext>>();
    edit_log_sender_ =
        std::make_shared<testing::StrictMock<GMockEditLogSender>>();
    meta_storage_writer_ =
        std::make_shared<testing::StrictMock<meta_storage::GMockWriter>>();
    meta_storage_ = std::make_shared<testing::StrictMock<GMockMetaStorage>>();
    meta_storage_->SetWriter(meta_storage_writer_);
    rwlock_manager_ = new testing::StrictMock<GMockRWLockManager>();
    block_manager_ = std::make_shared<testing::StrictMock<GMockBlockManager>>();
    datanode_manager_ = std::make_shared<testing::StrictMock<GMockDatanodeManager>>();
    file_finalizer_ = new testing::StrictMock<GMockFileFinalizer>();
    job_manager_ = std::make_shared<testing::StrictMock<GMockJobManager>>();
    ns_ = std::make_unique<NameSpace>(
        ha_state_.get(),
        safemode_.get(),
        block_manager_,
        std::unique_ptr<BlockReportManager>(),
        datanode_manager_,
        std::unique_ptr<LeaseManagerBase>(lease_manager_),
        std::unique_ptr<LeaseMonitor>(),
        edit_log_ctx_,
        edit_log_sender_,
        meta_storage_,
        std::unique_ptr<RWLockManager>(rwlock_manager_),
        std::shared_ptr<KeyManager>(),
        std::shared_ptr<DataCenters>(),
        std::shared_ptr<AccessCounterManager>(),
        std::unique_ptr<FileFinalizerBase>(file_finalizer_),
        job_manager_);

    EXPECT_CALL(*job_manager_, Stop()).Times(1);
  }

  template <typename InnerMatcher>
  inline decltype(auto) WrapINodeMatcher(const InnerMatcher& inner_matcher) {
    return testing::Pointee(
        testing::Property(&INodeInPath::Inode, inner_matcher));
  }

 protected:
  std::unique_ptr<testing::StrictMock<GMockHAState>> ha_state_;
  std::unique_ptr<testing::StrictMock<GMockSafeMode>> safemode_;
  testing::StrictMock<GMockLeaseManager>* lease_manager_;
  std::shared_ptr<testing::StrictMock<GMockEditLogContext>> edit_log_ctx_;
  std::shared_ptr<testing::StrictMock<GMockEditLogSender>> edit_log_sender_;
  std::shared_ptr<testing::StrictMock<meta_storage::GMockWriter>>
      meta_storage_writer_;
  std::shared_ptr<testing::StrictMock<GMockMetaStorage>> meta_storage_;
  testing::StrictMock<GMockRWLockManager>* rwlock_manager_;
  std::shared_ptr<testing::StrictMock<GMockBlockManager>> block_manager_;
  std::shared_ptr<testing::StrictMock<GMockDatanodeManager>> datanode_manager_;
  testing::StrictMock<GMockFileFinalizer>* file_finalizer_;
  std::shared_ptr<testing::StrictMock<GMockJobManager>> job_manager_;
  std::unique_ptr<NameSpace> ns_;
};

class GMockBGDeletionTask : public BGDeletionTask {
 public:
  explicit GMockBGDeletionTask(NameSpace* ns) : BGDeletionTask(ns) {
  }

  MOCK_METHOD0(IsStopped, bool());
};

TEST_F(NameSpaceTestV2, FormatExtendedBlock) {
  ExtendedBlockProto ebp;
  ebp.set_poolid("bp-1");
  ebp.set_blockid(5);
  ebp.set_generationstamp(1000);
  EXPECT_EQ(FormatExtendedBlock(ebp), "bp-1:blk_5_1000");
}

TEST_F(NameSpaceTestV2, ConstructorUsedByFsimageTransfer) {
  std::string fsimage_dir = FLAGS_fsimage_dir;
  FLAGS_fsimage_dir = GetProgramDirectory() + "/data/fsimage";
  std::string fsimage_file_name = FLAGS_fsimage_file_name;
  FLAGS_fsimage_file_name = "fsimage_0000000006963259775";
  DEFER([&]() {
    FLAGS_fsimage_dir = fsimage_dir;
    FLAGS_fsimage_file_name = fsimage_file_name;
  });
  std::string db_path = "rocksdb_XXXXXX";
  ASSERT_NE(mkdtemp(&(db_path[0])), nullptr);
  {
    NameSpace ns(db_path);
    EXPECT_EQ(ns.layout_version(), kDefaultLayoutVersion);
    EXPECT_TRUE(ns.software_version().empty());
    EXPECT_EQ(ns.filesystem_id(), FLAGS_filesystem_id);
    EXPECT_EQ(ns.namespace_id(), FLAGS_namespace_id);

    NamespaceInfoProto namespace_info_proto;
    ns.GetNamespaceInfo(&namespace_info_proto);
    // EXPECT_EQ(namespace_info_proto.buildversion(), kVcsVersion);
    EXPECT_EQ(namespace_info_proto.unused(), 0);
    EXPECT_EQ(namespace_info_proto.filesystemid(), FLAGS_filesystem_id);
    EXPECT_EQ(namespace_info_proto.namespaceid(), FLAGS_namespace_id);
    EXPECT_EQ(namespace_info_proto.namespacetype(), FLAGS_namespace_type);
    EXPECT_FALSE(namespace_info_proto.blockpoolid().empty());
    EXPECT_EQ(namespace_info_proto.storageinfo().layoutversion(),
              kDefaultLayoutVersion);
    EXPECT_EQ(namespace_info_proto.storageinfo().namespaceid(),
              FLAGS_namespace_id);
    EXPECT_EQ(namespace_info_proto.storageinfo().clusterid(),
              "CID-7537dd9e-3c62-4409-a5f0-23996b030ca4");
    EXPECT_EQ(namespace_info_proto.storageinfo().ctime(), 0);
    EXPECT_TRUE(namespace_info_proto.softwareversion().empty());

    StorageInfoProto storage_info_proto;
    storage_info_proto.set_namespaceid(FLAGS_namespace_id);
    storage_info_proto.set_clusterid(
        "CID-7537dd9e-3c62-4409-a5f0-23996b030ca4");
    storage_info_proto.set_ctime(0);
    EXPECT_TRUE(ns.VerifyRequest(storage_info_proto));
    // NameSpace ID is mismatched.
    storage_info_proto.set_namespaceid(FLAGS_namespace_id - 1);
    EXPECT_FALSE(ns.VerifyRequest(storage_info_proto));
    storage_info_proto.set_namespaceid(FLAGS_namespace_id);
    // Cluster ID is mismatched.
    storage_info_proto.set_clusterid("CID-1");
    EXPECT_FALSE(ns.VerifyRequest(storage_info_proto));
    storage_info_proto.set_clusterid(
        "CID-7537dd9e-3c62-4409-a5f0-23996b030ca4");
    // ctime is mismatched.
    storage_info_proto.set_ctime(1);
    EXPECT_FALSE(ns.VerifyRequest(storage_info_proto));
    storage_info_proto.set_ctime(0);
    // Verify again.
    EXPECT_TRUE(ns.VerifyRequest(storage_info_proto));
  }
  FileUtils::DeleteDirectoryRecursively(db_path);
}

TEST_F(NameSpaceTestV2, PurgeLogsOlderThanFailedInStandby) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(
          testing::Return(testing::ByMove(std::make_pair<Status, vshared_lock>(
              Status(JavaExceptions::kStandbyException), vshared_lock()))));
  EXPECT_EQ(ns_->PurgeLogsOlderThan(1).exception(),
            JavaExceptions::kStandbyException);
}

TEST_F(NameSpaceTestV2, PurgeLogsOlderThanWhenInputArgIsTooLarge) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));
  EXPECT_CALL(*meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(*meta_storage_, Sync()).Times(1);
  EXPECT_CALL(*edit_log_ctx_, PurgeLogsOlderThan(1)).Times(1);
  EXPECT_TRUE(ns_->PurgeLogsOlderThan(2).IsOK());
}

TEST_F(NameSpaceTestV2, PurgeLogsOlderThanWhenInputArgIsEqCurrentTxID) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));
  EXPECT_CALL(*meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(2));
  EXPECT_CALL(*meta_storage_, Sync()).Times(1);
  EXPECT_CALL(*edit_log_ctx_, PurgeLogsOlderThan(2)).Times(1);
  EXPECT_TRUE(ns_->PurgeLogsOlderThan(2).IsOK());
}

TEST_F(NameSpaceTestV2, FileExistsReturnsFalse) {
  EXPECT_CALL(*meta_storage_, GetSnapshot())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageSnapHolder>())));
  EXPECT_CALL(*meta_storage_,
              GetIterator(testing::Not(nullptr), testing::_, testing::_))
      .WillRepeatedly(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageIterHolder>())));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  EXPECT_FALSE(ns_->FileExists("/a"));
}

TEST_F(NameSpaceTestV2, FileExistsReturnsTrue) {
  EXPECT_CALL(*meta_storage_, GetSnapshot())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageSnapHolder>())));
  EXPECT_CALL(*meta_storage_,
              GetIterator(testing::Not(nullptr), testing::_, testing::_))
      .WillRepeatedly(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageIterHolder>())));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetParentId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(INodeBuilder()
                                        .SetId(17123)
                                        .SetParentId(kRootINodeId)
                                        .SetType(INode::kDirectory)
                                        .Build()),
          testing::Return(StatusCode::kOK)));
  EXPECT_TRUE(ns_->FileExists("/a"));
}

TEST_F(NameSpaceTestV2, CheckClientTxIDWhenDisableStandbyRead) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = false;
  EXPECT_TRUE(ClientTxIDChecker(
                  ha_state_.get(), []() { return 0; }, &ns_->metrics())
                  .Check(nullptr)
                  .IsOK());
}

TEST_F(NameSpaceTestV2, CheckClientTxIDInActive) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(true));
  EXPECT_TRUE(ClientTxIDChecker(
                  ha_state_.get(), []() { return 0; }, &ns_->metrics())
                  .Check(nullptr)
                  .IsOK());
}

TEST_F(NameSpaceTestV2, CheckClientTxIDInStandbyWithNullCtx) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(false));
  Status s = ClientTxIDChecker(
                 ha_state_.get(), []() { return 0; }, &ns_->metrics())
                 .Check(nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  EXPECT_EQ(s.message(),
            "Operation is not supported in state STANDBY. No context.");
}

TEST_F(NameSpaceTestV2, CheckClientTxIDWhenCtxAllowStale) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(false));
  int get_txid_times = 0;
  RpcController ctx;
  ctx.AllowStandbyRead();
  cloudfs::RpcRequestHeaderProto header;
  header.set_standbyreadpolicy(cloudfs::RpcStandbyReadPolicy::RPC_ALLOW_STALE);
  ctx.set_rpc_request_header(
      std::make_shared<cloudfs::RpcRequestHeaderProto>(header));
  EXPECT_TRUE(ClientTxIDChecker(
                  ha_state_.get(),
                  [&get_txid_times]() {
                    get_txid_times++;
                    return 0;
                  },
                  &ns_->metrics())
                  .Check(&ctx)
                  .IsOK());
  EXPECT_EQ(get_txid_times, 0);
}

TEST_F(NameSpaceTestV2,
       CheckClientTxIDWhenServerTxIDIsLargerThanClientTxIDBeforeWaiting) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(false));
  int get_txid_times = 0;
  RpcController ctx;
  ctx.AllowStandbyRead();
  cloudfs::RpcRequestHeaderProto header;
  header.set_txid(100);
  header.set_standbyreadpolicy(
      cloudfs::RpcStandbyReadPolicy::RPC_ALLOW_STADNBY);
  ctx.set_rpc_request_header(
      std::make_shared<cloudfs::RpcRequestHeaderProto>(header));
  EXPECT_TRUE(ClientTxIDChecker(
                  ha_state_.get(),
                  [&get_txid_times]() {
                    get_txid_times++;
                    return 150;
                  },
                  &ns_->metrics())
                  .Check(&ctx)
                  .IsOK());
  EXPECT_EQ(get_txid_times, 1);
}

TEST_F(NameSpaceTestV2,
       CheckClientTxIDWhenServerTxIDIsLargerThanClientTxIDAfterWaiting) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(false));
  int get_txid_times = 0;
  RpcController ctx;
  ctx.AllowStandbyRead();
  cloudfs::RpcRequestHeaderProto header;
  header.set_txid(100);
  header.set_standbyreadpolicy(
      cloudfs::RpcStandbyReadPolicy::RPC_ALLOW_STADNBY);
  ctx.set_rpc_request_header(
      std::make_shared<cloudfs::RpcRequestHeaderProto>(header));
  EXPECT_TRUE(ClientTxIDChecker(
                  ha_state_.get(),
                  [&get_txid_times]() {
                    get_txid_times++;
                    return std::vector<int>{50, 150}[get_txid_times - 1];
                  },
                  &ns_->metrics())
                  .Check(&ctx)
                  .IsOK());
  EXPECT_EQ(get_txid_times, 2);
}

TEST_F(NameSpaceTestV2,
       CheckClientTxIDWhenServerTxIDIsSmallerThanClientTxIDAfterWaiting) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  DEFER([&]() { FLAGS_standby_read_enabled = standby_read_enabled; });
  FLAGS_standby_read_enabled = true;
  EXPECT_CALL(*ha_state_, IsActive()).Times(1).WillOnce(testing::Return(false));
  int get_txid_times = 0;
  RpcController ctx;
  ctx.AllowStandbyRead();
  cloudfs::RpcRequestHeaderProto header;
  header.set_txid(100);
  header.set_standbyreadpolicy(
      cloudfs::RpcStandbyReadPolicy::RPC_ALLOW_STADNBY);
  ctx.set_rpc_request_header(
      std::make_shared<cloudfs::RpcRequestHeaderProto>(header));
  Status s = ClientTxIDChecker(
                 ha_state_.get(),
                 [&get_txid_times]() {
                   get_txid_times++;
                   return std::vector<int>(60, 50)[get_txid_times - 1];
                 },
                 &ns_->metrics())
                 .Check(&ctx);
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  EXPECT_THAT(s.message(),
              testing::HasSubstr("Operation is not supported in state STANDBY. "
                                 "Standby stale read is not allowed,  "
                                 "client_txid=100 server_txid_start=50 "));
  EXPECT_GE(get_txid_times, 2);
}

TEST_F(NameSpaceTestV2, GetStoragePolicyByINodeIdWithMissingINode) {
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  EXPECT_FALSE(ns_->GetStoragePolicyByINodeId(17123, kRootINodeId, nullptr));
}

TEST_F(NameSpaceTestV2, GetStoragePolicyByINodeIdWithExistedINode) {
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(
          testing::DoAll(testing::SetArgPointee<1>(
                             INodeBuilder().SetStoragePolicyId(12).Build()),
                         testing::Return(StatusCode::kOK)));
  StoragePolicyId storage_policy_id =
      StoragePolicyId::kBlockStoragePolicyIdUnspecified;
  EXPECT_TRUE(
      ns_->GetStoragePolicyByINodeId(17123, kRootINodeId, &storage_policy_id));
  EXPECT_EQ(storage_policy_id, StoragePolicyId::kAllSSDStoragePolicy);
}

TEST_F(NameSpaceTestV2, SyncMeta) {
  EXPECT_CALL(*meta_storage_, Sync()).Times(1);
  ns_->SyncMeta();
}

TEST_F(NameSpaceTestV2, WriteStartNop) {
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                    nullptr),
                  1,
                  NameSpaceInfoDelta{},
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                    nullptr),
                  1,
                  NameSpaceInfoDelta{},
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  EXPECT_CALL(
      *meta_storage_writer_,
      Push(testing::AllOf(
          testing::Property(&meta_storage::WriteTask::txid, 1),
          testing::Property(
              &meta_storage::WriteTask::wb,
              testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                nullptr)))))
      .Times(1)
      .WillOnce(testing::Invoke([](meta_storage::WriteTask* task) {
        if (task->HasNext()) {
          task->Next();
        }
        delete task;
      }));
  ns_->WriteStartNop(1);
}

TEST_F(NameSpaceTestV2, WriteEndNop) {
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTask(
                  testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                    nullptr),
                  1,
                  NameSpaceInfoDelta{},
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       Closure* done) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTask(
            std::move(wb), txid, delta, std::move(verify_kvs), done);
      }));
  EXPECT_CALL(*meta_storage_,
              PushINodeTXWriteTasks(
                  testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                    nullptr),
                  1,
                  NameSpaceInfoDelta{},
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](std::unique_ptr<rocksdb::WriteBatch> wb,
                                       int64_t txid,
                                       const NameSpaceInfoDelta& delta,
                                       std::unique_ptr<KVVerifyVec> verify_kvs,
                                       const std::vector<Closure*>& dones) {
        return meta_storage_->MetaStorage::PushINodeTXWriteTasks(
            std::move(wb), txid, delta, std::move(verify_kvs), dones);
      }));
  EXPECT_CALL(
      *meta_storage_writer_,
      Push(testing::AllOf(
          testing::Property(&meta_storage::WriteTask::txid, 1),
          testing::Property(
              &meta_storage::WriteTask::wb,
              testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                                nullptr)))))
      .Times(1)
      .WillOnce(testing::Invoke([](meta_storage::WriteTask* task) {
        if (task->HasNext()) {
          task->Next();
        }
        delete task;
      }));
  ns_->WriteEndNop(1);
}

TEST_F(NameSpaceTestV2, GetServerDefaults) {
  std::string dfs_checksum_type = FLAGS_dfs_checksum_type;
  DEFER([&]() { FLAGS_dfs_checksum_type = dfs_checksum_type; });
  FLAGS_dfs_checksum_type = "";
  EXPECT_CALL(*ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kRead))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  cloudfs::FsServerDefaultsProto server_default;
  EXPECT_TRUE(ns_->GetServerDefaults(&server_default, nullptr).IsOK());
  EXPECT_EQ(server_default.blocksize(), FLAGS_dfs_block_size);
  EXPECT_EQ(server_default.bytesperchecksum(), FLAGS_dfs_bytes_per_checksum);
  EXPECT_EQ(server_default.writepacketsize(),
            FLAGS_dfs_client_write_packet_size);
  EXPECT_EQ(server_default.replication(), FLAGS_dfs_replication);
  EXPECT_EQ(server_default.filebuffersize(), FLAGS_io_file_buffer_size);
  EXPECT_EQ(server_default.encryptdatatransfer(),
            FLAGS_dfs_encrypt_data_transfer);
  EXPECT_EQ(server_default.trashinterval(), FLAGS_fs_trash_interval);
  EXPECT_EQ(server_default.checksumtype(),
            cloudfs::ChecksumTypeProto::CHECKSUM_NULL);
}

TEST_F(NameSpaceTestV2, GetHAServiceStateOfActive) {
  EXPECT_CALL(*ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::ACTIVE));
  HAServiceStateResponseProto response;
  EXPECT_TRUE(ns_->GetHAServiceState(&response).IsOK());
  EXPECT_TRUE(response.has_state());
  EXPECT_EQ(response.state(), cloudfs::HAServiceStateProto::ACTIVE);
}

TEST_F(NameSpaceTestV2, GetHAServiceStateOfStandbyWhileEnableStandbyRead) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  FLAGS_standby_read_enabled = true;
  bool dancenn_observe_mode_on = FLAGS_dancenn_observe_mode_on;
  FLAGS_dancenn_observe_mode_on = false;
  bool standby_read_enabled_for_main_read =
      FLAGS_standby_read_enabled_for_main_read;
  FLAGS_standby_read_enabled_for_main_read = true;
  bool standby_read_enabled_for_all_read =
      FLAGS_standby_read_enabled_for_all_read;
  FLAGS_standby_read_enabled_for_all_read = true;
  DEFER([&]() {
    FLAGS_standby_read_enabled = standby_read_enabled;
    FLAGS_dancenn_observe_mode_on = dancenn_observe_mode_on;
    FLAGS_standby_read_enabled_for_main_read =
        standby_read_enabled_for_main_read;
    FLAGS_standby_read_enabled_for_all_read = standby_read_enabled_for_all_read;
  });
  EXPECT_CALL(*ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));
  HAServiceStateResponseProto response;
  EXPECT_TRUE(ns_->GetHAServiceState(&response).IsOK());
  EXPECT_TRUE(response.has_state());
  EXPECT_EQ(response.state(), cloudfs::HAServiceStateProto::OBSERVER);
}

TEST_F(NameSpaceTestV2, GetHAServiceStateOfStandbyWhileDisableStandbyRead) {
  bool standby_read_enabled = FLAGS_standby_read_enabled;
  FLAGS_standby_read_enabled = false;
  bool dancenn_observe_mode_on = FLAGS_dancenn_observe_mode_on;
  FLAGS_dancenn_observe_mode_on = false;
  bool standby_read_enabled_for_main_read =
      FLAGS_standby_read_enabled_for_main_read;
  FLAGS_standby_read_enabled_for_main_read = false;
  bool standby_read_enabled_for_all_read =
      FLAGS_standby_read_enabled_for_all_read;
  FLAGS_standby_read_enabled_for_all_read = false;
  DEFER([&]() {
    FLAGS_standby_read_enabled = standby_read_enabled;
    FLAGS_dancenn_observe_mode_on = dancenn_observe_mode_on;
    FLAGS_standby_read_enabled_for_main_read =
        standby_read_enabled_for_main_read;
    FLAGS_standby_read_enabled_for_all_read = standby_read_enabled_for_all_read;
  });
  EXPECT_CALL(*ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));
  HAServiceStateResponseProto response;
  EXPECT_TRUE(ns_->GetHAServiceState(&response).IsOK());
  EXPECT_TRUE(response.has_state());
  EXPECT_EQ(response.state(), cloudfs::HAServiceStateProto::STANDBY);
}

TEST_F(NameSpaceTestV2, DumpFileInfo) {
  EXPECT_CALL(*ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kRead))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetParentId(kRootINodeId)
                                    .SetName("")
                                    .SetPermission(PermissionStatusBuilder()
                                                       .SetUsername("bird")
                                                       .SetGroupname("cat")
                                                       .SetPermission(644)
                                                       .Build())
                                    .SetType(INode::kDirectory)
                                    .SetMtime(0)
                                    .SetAtime(0)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .WillRepeatedly(testing::DoAll(
          testing::SetArgPointee<2>(INodeBuilder()
                                        .SetId(17123)
                                        .SetParentId(kRootINodeId)
                                        .SetName("a")
                                        .SetPermission(PermissionStatusBuilder()
                                                           .SetUsername("bird")
                                                           .SetGroupname("cat")
                                                           .SetPermission(644)
                                                           .Build())
                                        .SetType(INode::kDirectory)
                                        .SetMtime(0)
                                        .SetAtime(0)
                                        .Build()),
          testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, "b", testing::_, testing::_))
      .WillRepeatedly(testing::DoAll(
          testing::SetArgPointee<2>(INodeBuilder()
                                        .SetId(17124)
                                        .SetParentId(17123)
                                        .SetName("b")
                                        .SetPermission(PermissionStatusBuilder()
                                                           .SetUsername("bird")
                                                           .SetGroupname("cat")
                                                           .SetPermission(644)
                                                           .Build())
                                        .SetType(INode::kFile)
                                        .SetMtime(0)
                                        .SetAtime(0)
                                        .Build()),
          testing::Return(StatusCode::kOK)));
  std::ostringstream ss;
  std::vector<BlockID> blk_ids;
  RpcController ctx;
  EXPECT_TRUE(
      ns_->DumpFileInfo("///a/b", false, true, ss, &blk_ids, &ctx).IsOK());
  EXPECT_FALSE(ss.str().empty());
}

TEST_F(NameSpaceTestV2, GetReplicaPolicyHelperWithoutXAttrs) {
  ReplicaPolicy policy;
  EXPECT_FALSE(PolicyManager::GetReplicaPolicyHelper(INode(), &policy));
  EXPECT_EQ(policy.distributed(), false);
  EXPECT_TRUE(policy.dc().size() == 0);
}

TEST_F(NameSpaceTestV2, GetReplicaPolicyHelperWithAttrs) {
  INode inode;
  int32_t value = 100;
  auto xattr = inode.add_xattrs();
  xattr->set_namespace_(XAttrProto::SYSTEM);
  xattr->set_name("hdfs.replica.policy.type");
  xattr->set_value(reinterpret_cast<char*>(&value));
  xattr = inode.add_xattrs();
  xattr->set_namespace_(XAttrProto::SYSTEM);
  xattr->set_name("hdfs.replica.policy.dc");
  xattr->set_value("lfwg");
  ReplicaPolicy policy;
  EXPECT_TRUE(PolicyManager::GetReplicaPolicyHelper(inode, &policy));
  EXPECT_EQ(policy.distributed(), false);  // 100 != 2
  EXPECT_EQ(policy.dc_size(), 1);
  EXPECT_EQ(policy.dc().Get(0), "lfwg");
}

TEST_F(NameSpaceTestV2, GetReplicaPolicyHelperWithAttrsPB) {
  INode inode;
  int32_t value = 100;
  ReplicaPolicy input;
  input.set_distributed(true);
  input.add_dc("test");
  input.add_dc("test2");
  auto xattr = inode.add_xattrs();
  xattr->set_namespace_(XAttrProto::SYSTEM);
  xattr->set_name("hdfs.replica.policy.pb");
  xattr->set_value(input.SerializeAsString());

  ReplicaPolicy policy;
  EXPECT_TRUE(PolicyManager::GetReplicaPolicyHelper(inode, &policy));
  EXPECT_EQ(policy.ShortDebugString(), input.ShortDebugString());
}

TEST_F(NameSpaceTestV2, GetReadPolicyHelperWithoutXattrs) {
  EXPECT_FALSE(PolicyManager::GetReadPolicyHelper(INode(), nullptr));
}

TEST_F(NameSpaceTestV2, GetReadPolicyHelperWithXattrs) {
  cloudfs::ReadPolicyProto input;
  input.set_localdconly(true);
  input.add_blacklistdc("lfwg-1");
  input.add_blacklistdc("lfwg-2");
  cloudfs::ReadPolicyProto output;
  EXPECT_TRUE(PolicyManager::GetReadPolicyHelper(
      INodeBuilder().SetXAttr(kReadPolicyXAttr, input).Build(), &output));
  EXPECT_TRUE(output.localdconly());
  ASSERT_EQ(output.blacklistdc_size(), 2);
  EXPECT_EQ(output.blacklistdc(0), "lfwg-1");
  EXPECT_EQ(output.blacklistdc(1), "lfwg-2");
}

TEST_F(NameSpaceTestV2, GetRecyclePolicy) {
  EXPECT_CALL(*ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kRead))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*meta_storage_, GetSnapshot())
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageSnapHolder>())));
  EXPECT_CALL(*meta_storage_,
              GetIterator(testing::Not(nullptr), testing::_, testing::_))
      .WillRepeatedly(testing::Return(
          testing::ByMove(std::make_unique<MetaStorageIterHolder>())));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetId(kRootINodeId)
                  .SetName("a")
                  .SetType(INode::kDirectory)
                  .SetXAttr(kRecyclePolicyXAttr, cloudfs::RecyclePolicyProto())
                  .Build()),
          testing::Return(StatusCode::kOK)));
  std::vector<
      std::pair<std::string, std::shared_ptr<cloudfs::RecyclePolicyProto>>>
      recycle_policies;
  EXPECT_TRUE(ns_->GetRecyclePolicy("/a", &recycle_policies, nullptr).IsOK());
  ASSERT_EQ(recycle_policies.size(), 2);
  EXPECT_TRUE(recycle_policies[0].first.empty());
  EXPECT_TRUE(recycle_policies[0].second == nullptr);
  EXPECT_EQ(recycle_policies[1].first, "a");
  EXPECT_TRUE(recycle_policies[1].second != nullptr);
}

TEST_F(NameSpaceTestV2, GetStorageClassFailedWhenPathNotFound) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kRead))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(
      *rwlock_manager_,
      LockPaths(
          testing::ElementsAre(
              testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
              testing::Property(&cnetpp::base::StringPiece::as_string, "/a")),
          false))
      .Times(1);
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  cloudfs::StorageClassProto cls;
  Status s = ns_->GetStorageClass("/a", &cls);
  EXPECT_EQ(s.exception(), JavaExceptions::kIllegalArgumentException);
  EXPECT_EQ(s.code(), Code::kNoEntry);
  EXPECT_EQ(s.message(), "Target not found /a");
}

TEST_F(NameSpaceTestV2, GetStorageClassSucceed) {
}

TEST_F(NameSpaceTestV2, AsyncUnsetDepredLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, AsyncUnsetLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, PrepareUnsetLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, PrepareUnsetDepredLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, CommitUnsetLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, PrepareGetLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, GetLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, ScanLifecyclePolicy) {
}

TEST_F(NameSpaceTestV2, RefreshINodeStatIfNecessaryInObserveMode) {
}

TEST_F(NameSpaceTestV2, SaveAccessCounterSnapshot) {
}

TEST_F(NameSpaceTestV2, AsyncAppendWhenRecoverLeaseCannotCloseFileImmediately) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_,
              CanRecoverLease(17123,
                              "client-name-1",
                              "client-name-2",
                              false,
                              testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::Return(Status()));
  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(false),
                               testing::Return(Status())));

  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(*edit_log_sender_,
              LogReassignLeaseV2("/a",
                                 testing::_,
                                 testing::_,
                                 "client-name-1",
                                 "client-name-2",
                                 testing::_))
      .Times(1)
      .WillOnce(testing::Return(2));

  int64_t expect_txid = 1;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  }
  EXPECT_CALL(*edit_log_sender_, LogSetGenerationStampV2(1001, false))
      .Times(1)
      .WillOnce(testing::Return(expect_txid));
#if 0
  EXPECT_CALL(*meta_storage_,
              OrderedPutGenerationStampV2(testing::_,
                                          1,
                                          testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<2>(
          testing::Invoke([](Closure* done) { done->Run(); })));
#endif

  EXPECT_CALL(
      *block_manager_,
      InitRecover(
          testing::AllOf(testing::Property(&BlockProto::blockid, 1084217910),
                         testing::Property(&BlockProto::genstamp, 1001),
                         testing::Property(&BlockProto::numbytes, 1024)),
          1001,
          true))
      .Times(1);

  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINode(testing::_,
                                 testing::_,
                                 testing::_,
                                 2,
                                 testing::Not(nullptr),
                                 nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  AppendRequestProto request;
  request.set_clientname("client-name-2");
  AppendResponseProto response;
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncAppend("/a",
                   "client-machine-2",
                   LogRpcInfo(),
                   UserGroupInfo(),
                   &request,
                   &response,
                   &rpc_done);
  rpc_done.Await();

  EXPECT_TRUE(rpc_done.status().HasException());
  EXPECT_EQ(rpc_done.status().message(),
            "Failed to close file 17123. "
            "Lease recovery is in progress. "
            "Try again later.");
}

TEST_F(NameSpaceTestV2, AsyncCompleteRenamedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(2);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                                     testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  SynchronizedRpcClosure rpc_done;
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile("/b",
                   Block(kInvalidBlockID, 0, 0),
                   kInvalidBlockID,
                   WrapINodeMatcher(testing::AllOf(
                       testing::Property(&INode::id, 17123),
                       testing::Property(&INode::blocks_size, 0),
                       testing::Property(&INode::has_uc, true),
                       testing::Property(&INode::status,
                                         INode::kFileUnderConstruction))),
                   testing::_,
                   testing::_,
                   &rpc_done,
                   testing::IsNull()))
      .Times(1);

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Run();
  rpc_done.Await();
  EXPECT_TRUE(rpc_done.status().IsOK());
}

TEST_F(NameSpaceTestV2, AsyncCompleteDeletedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(StatusCode::kFileNotFound));

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(),
            JavaExceptions::kFileNotFoundException);
  EXPECT_EQ(rpc_done.status().message(),
            "INode not found: 17123. "
            "The file may have been deleted, "
            "please check if your application "
            "has concurrency issues. src=/a");
}

TEST_F(NameSpaceTestV2, AsyncCompleteDir) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder().SetId(17123).SetType(INode::kDirectory).Build()),
          testing::Return(StatusCode::kOK)));

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(),
            JavaExceptions::kLeaseExpiredException);
  EXPECT_EQ(rpc_done.status().message(),
            "No lease on /a: INode is not a regular file.");
}

TEST_F(NameSpaceTestV2, AsyncCompleteCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("a")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder().SetBlockId(1084217910).Build())
                  .SetStatus(INode::kFileComplete)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(),
            JavaExceptions::kLeaseExpiredException);
  EXPECT_EQ(rpc_done.status().message(),
            "No lease on /a: file is not open for writing.");
}

TEST_F(NameSpaceTestV2, AsyncCompleteLeaseMismatchedFile01) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("a")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder().SetBlockId(1084217910).Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(),
            JavaExceptions::kLeaseExpiredException);
  EXPECT_EQ(rpc_done.status().message(), "No lease on /a: lease mismatch.");
}

TEST_F(NameSpaceTestV2, AsyncCompleteLeaseMismatchedFile02) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(1)
      .WillOnce(testing::Return(INodeBuilder()
                                    .SetId(kRootINodeId)
                                    .SetType(INode::kDirectory)
                                    .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("a")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder().SetBlockId(1084217910).Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(false));

  cloudfs::CompleteRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/a", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(),
            JavaExceptions::kLeaseExpiredException);
  EXPECT_EQ(rpc_done.status().message(), "No lease on /a: lease mismatch.");
}

TEST_F(NameSpaceTestV2, AsyncCompleteFileButLastBlockNotComplete) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(*block_manager_,
              IsLastBlkReadyToComplete(testing::Field(&Block::id, 1084217910)))
      .Times(1)
      .WillOnce(testing::Return(
          Status(Code::kFalse, "The last block is not ready to complete")));

  cloudfs::CompleteRequestProto request;
  request.set_src("/b");
  request.set_clientname("client-name-1");
  request.mutable_last()->set_poolid("pool-1");
  request.mutable_last()->set_blockid(1084217910);
  request.mutable_last()->set_generationstamp(1001);
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/b", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_TRUE(rpc_done.status().IsFalse());
  EXPECT_EQ(rpc_done.status().message(),
            "The last block is not ready to complete");
}

TEST_F(NameSpaceTestV2, AsyncCompleteFileWithInconsistentLastBlock) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(*block_manager_,
              IsLastBlkReadyToComplete(testing::Field(&Block::id, 1084217911)))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  cloudfs::CompleteRequestProto request;
  request.set_src("/b");
  request.set_clientname("client-name-1");
  request.mutable_last()->set_poolid("pool-1");
  request.mutable_last()->set_blockid(1084217911);
  request.mutable_last()->set_generationstamp(1002);
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/b", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_EQ(rpc_done.status().exception(), JavaExceptions::kIOException);
  std::string error_msg = "Trying to commit inconsistent block";
  EXPECT_EQ(rpc_done.status().message().substr(0, error_msg.size()), error_msg);
}

TEST_F(NameSpaceTestV2, AsyncCompleteFileButNotCompleteLastBlock) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(*block_manager_, BlockReadyToComplete(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));

  cloudfs::CompleteRequestProto request;
  request.set_src("/b");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCompleteFile("/b", &request, nullptr, &rpc_done);
  rpc_done.Await();
  EXPECT_TRUE(rpc_done.status().IsFalse());
}

TEST_F(NameSpaceTestV2, AsyncCompleteEmptyFileSucceed) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  SynchronizedRpcClosure rpc_done;
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile("/b",
                   testing::AllOf(testing::Field(&Block::id, kInvalidBlockID),
                                  testing::Field(&Block::gs, 0),
                                  testing::Field(&Block::num_bytes, 0)),
                   kInvalidBlockID,
                   WrapINodeMatcher(testing::AllOf(
                       testing::Property(&INode::id, 17123),
                       testing::Property(&INode::blocks_size, 0),
                       testing::Property(&INode::has_uc, true),
                       testing::Property(&INode::status,
                                         INode::kFileUnderConstruction))),
                   testing::_,
                   testing::_,
                   &rpc_done,
                   testing::IsNull()))
      .Times(1);

  cloudfs::CompleteRequestProto request;
  request.set_src("/b");
  request.set_clientname("client-name-1");
  request.set_fileid(17123);
  ns_->AsyncCompleteFile("/b", &request, nullptr, &rpc_done);
  rpc_done.Run();
  rpc_done.Await();
  EXPECT_TRUE(rpc_done.status().IsOK());
}

TEST_F(NameSpaceTestV2, AsyncCompleteOneBlockFileSucceed) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));

  EXPECT_CALL(*block_manager_,
              IsLastBlkReadyToComplete(
                  testing::AllOf(testing::Field(&Block::id, 1084217910),
                                 testing::Field(&Block::gs, 1002),
                                 testing::Field(&Block::num_bytes, 2048))))
      .Times(1)
      .WillOnce(testing::Return(Status()));
  EXPECT_CALL(*block_manager_,
              CommitOrCompleteOrPersistLastBlock(
                  testing::AllOf(testing::Field(&Block::id, 1084217910),
                                 testing::Field(&Block::gs, 1002),
                                 testing::Field(&Block::num_bytes, 2048)),
                  false))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(*block_manager_, BlockReadyToComplete(1084217910))
      .Times(1)
      .WillOnce(testing::Return(true));

  SynchronizedRpcClosure rpc_done;
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          Block(1084217910, 1002, 2048),
          kInvalidBlockID,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1002),
                      testing::Property(&BlockProto::numbytes, 2048)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
          testing::_,
          testing::_,
          &rpc_done,
          testing::IsNull()))
      .Times(1);

  cloudfs::CompleteRequestProto request;
  request.set_src("/b");
  request.set_clientname("client-name-1");
  request.mutable_last()->set_poolid("pool-1");
  request.mutable_last()->set_blockid(1084217910);
  request.mutable_last()->set_generationstamp(1002);
  request.mutable_last()->set_numbytes(2048);
  request.set_fileid(17123);
  ns_->AsyncCompleteFile("/b", &request, nullptr, &rpc_done);
  rpc_done.Run();
  rpc_done.Await();
  EXPECT_TRUE(rpc_done.status().IsOK());
}

TEST_F(NameSpaceTestV2, CommitBlockSynDelNotExistedBlock1) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(kInvalidINodeId));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(false);
  request.set_deleteblock(true);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "Retry request");
}

TEST_F(NameSpaceTestV2, CommitBlockSynDelNotExistedBlock2) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(false);
  request.set_deleteblock(true);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "Retry request");
}

TEST_F(NameSpaceTestV2, CommitBlockSynCloseFileNotExistedBlock) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(kInvalidINodeId));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Block(=1084217910) not found");
}

TEST_F(NameSpaceTestV2, CommitBlockSynDelBlockOfNotExistedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(false);
  request.set_deleteblock(true);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "Retry request");
}

TEST_F(NameSpaceTestV2, CommitBlockSynCloseNotExistedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kFileNotFoundException);
  EXPECT_EQ(s.message(),
            "INode not found: 17123. "
            "The file may have been deleted, "
            "please check if your application has concurrency issues.");
}

TEST_F(NameSpaceTestV2, CommitBlockSyncWithNonUcFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetStatus(INode::kFileComplete)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(),
            "Unexpected block B1084217910 "
            "since the file /b is not under construction");
}

// = BlockManagerTestV2.CommitBlockSynWithWrongGS
TEST_F(NameSpaceTestV2, CommitBlockSynWithWrongGS) {
}

TEST_F(NameSpaceTestV2,
       CommitBlockSynBlockManagerCommitBlockSynThrowsException) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization(testing::_))
      .Times(1)
      .WillOnce(testing::Return(
          Status(JavaExceptions::kIOException,
                 "BlockManager::CommitBlockSynchronization throws exception")));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "BlockManager::CommitBlockSynchronization throws exception");
}

// = NameSpaceTestV2.CommitBlockSynWithNoInstruction
TEST_F(NameSpaceTestV2, CommitBlockSynCommitBlockNoCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization(testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  EXPECT_CALL(*meta_storage_, GetBlockInfo(1084217910, testing::_))
      .Times(1)
      .WillOnce(
          testing::DoAll(testing::SetArgPointee<1>(
                             BlockInfoProtoBuilder()
                                 .SetState(BlockInfoProto::kUnderConstruction)
                                 .SetBlockId(1084217910)
                                 .SetGenStamp(1001)
                                 .SetNumBytes(1024)
                                 .SetINodeId(17123)
                                 .SetExpectedRep(2)
                                 .Build()),
                         testing::Return(true)));

  auto inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(&INode::blocks,
                        testing::ElementsAre(testing::AllOf(
                            testing::Property(&BlockProto::blockid, 1084217910),
                            testing::Property(&BlockProto::genstamp, 1002),
                            testing::Property(&BlockProto::numbytes, 2048)))),
      testing::Property(&INode::has_uc, true),
      testing::Property(&INode::status, INode::kFileUnderConstruction));
  auto last_bip_matcher = testing::AllOf(
      testing::Property(&BlockInfoProto::block_id, 1084217910),
      testing::Property(&BlockInfoProto::gen_stamp, 1002),
      testing::Property(&BlockInfoProto::num_bytes, 2048),
      testing::Property(&BlockInfoProto::state, BlockInfoProto::kComplete));
  EXPECT_CALL(*edit_log_sender_,
              LogCommitBlockSynchronization("/b",
                                            false,
                                            false,
                                            inode_matcher,
                                            last_bip_matcher,
                                            testing::_,
                                            testing::_,
                                            testing::_))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(
      *meta_storage_,
      OrderedCommitINodes(
          nullptr,
          testing::Pointee(testing::ElementsAre(testing::Field(
              &INodeAndSnapshot::inode, testing::Pointee(inode_matcher)))),
          nullptr,
          nullptr,
          nullptr,
          nullptr,
          testing::Pointee(testing::ElementsAre(
              testing::AllOf(testing::Property(&INode::id, 17123)))),
          //   testing::Property(std::vector<BlockInfoProto>::size, 0),
          testing::_,
          testing::ElementsAre(last_bip_matcher),
          1,
          testing::ElementsAre(testing::Not(nullptr)),
          nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<10>(testing::Invoke(
          [](const std::vector<Closure*>& dones) { dones.at(0)->Run(); })));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, CommitBlockSynDeleteBlockNoCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization(testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  EXPECT_CALL(*edit_log_sender_,
              LogUpdateBlocksV2(
                  "/b",
                  testing::AllOf(testing::Property(&INode::id, 17123),
                                 testing::Property(&INode::blocks_size, 0)),
                  testing::_,
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(
      *meta_storage_,
      OrderedUpdateINodeAndDeleteLastBlock(
          testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(&INode::blocks_size, 0),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status, INode::kFileUnderConstruction)),
          1084217910,
          testing::_,
          1,
          testing::Not(nullptr),
          nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(false);
  request.set_deleteblock(true);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, CommitBlockSynCommitBlockAndCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization(testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          Block(1084217910, 2048, 1002),
          kInvalidBlockID,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
              testing::_,
              testing::_,
              testing::Not(nullptr),
              testing::IsNull()))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<6>(testing::Invoke(
                                   [](RpcClosure* done) { done->Run(); })),
                               testing::Return(Status())));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, CommitBlockSynDeleteBlockAndCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*block_manager_, GetINodeId(1084217910))
      .Times(1)
      .WillOnce(testing::Return(17123));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(false));
  EXPECT_CALL(*block_manager_, CommitBlockSynchronization(testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          Block(kInvalidBlockID, 0, 0),
          1084217910,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
              testing::_,
              testing::_,
              testing::Not(nullptr),
              testing::IsNull()))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<6>(testing::Invoke(
                                   [](RpcClosure* done) { done->Run(); })),
                               testing::Return(Status())));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1084217910);
  request.mutable_block()->set_generationstamp(1001);
  request.mutable_block()->set_numbytes(1024);
  request.set_closefile(true);
  request.set_deleteblock(true);
  Status s = ns_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, CommitLastBlockHappCase) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_block = BlockProtoBuilder()
                     .SetBlockId(1084217910)
                     .SetGenStamp(kBlockProtocolV2GenerationStamp)
                     .SetNumBytes(1024)
                     .Build();
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("a")
                     .SetType(INode::kFile)
                     .AddBlock(b_block)
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);
  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(*block_manager_,
              AnalyzeFileBlocksToCommit(testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status::OK()));

  EXPECT_CALL(*block_manager_, UpdateLength(1084217910, 512))
      .Times(1)
      .WillOnce(testing::Return());
  EXPECT_CALL(*block_manager_, GetBlockUCState(1084217910))
      .Times(1)
      .WillOnce(testing::Return(BlockUCState::kUnderConstruction));
  EXPECT_CALL(*block_manager_, GetBlock(1084217910))
      .Times(1)
      .WillOnce(testing::Return(
          Block(1084217910, 1024, kBlockProtocolV2GenerationStamp)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1084217910, testing::_))
      .Times(1)
      .WillOnce(
          testing::DoAll(testing::SetArgPointee<1>(
                             BlockInfoProtoBuilder()
                                 .SetState(BlockInfoProto::kUnderConstruction)
                                 .SetBlockId(1084217910)
                                 .SetGenStamp(kBlockProtocolV2GenerationStamp)
                                 .SetNumBytes(1024)
                                 .SetINodeId(17123)
                                 .SetExpectedRep(2)
                                 .Build()),
                         testing::Return(true)));

  auto b_inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(&INode::blocks,
                        testing::ElementsAre(testing::AllOf(
                            testing::Property(&BlockProto::blockid, 1084217910),
                            testing::Property(&BlockProto::genstamp,
                                              kBlockProtocolV2GenerationStamp),
                            testing::Property(&BlockProto::numbytes, 512)))),
      testing::Property(&INode::status, INode::kFileUnderConstruction));
  auto b_bip_matcher = testing::AllOf(
      testing::Property(&BlockInfoProto::state, BlockInfoProto::kCommitted));
  EXPECT_CALL(*edit_log_sender_,
              LogFsync("/a",
                       b_inode_matcher,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       LogRpcInfo()))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(*meta_storage_,
              OrderedCommitLastBlock(testing::Pointee(b_inode_matcher),
                                     b_bip_matcher,
                                     testing::_,
                                     testing::_,
                                     1,
                                     testing::Not(nullptr),
                                     nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<5>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  CommitLastBlockRequestProto request;
  request.set_src("/a");
  request.set_clientname("client-name-1");
  request.mutable_lastblock()->set_blockid(1084217910);
  request.mutable_lastblock()->set_numbytes(512);
  request.mutable_lastblock()->set_genstamp(kBlockProtocolV2GenerationStamp);
  request.set_fileid(17123);
  request.set_filelength(512);

  CommitLastBlockResponseProto response;
  cnetpp::base::IPAddress client_ip("***********");
  SynchronizedRpcClosure rpc_done;
  ns_->AsyncCommitLastBlock(request.src(),
                            client_ip,
                            &request,
                            &response,
                            LogRpcInfo(),
                            UserGroupInfo(),
                            &rpc_done);
  rpc_done.Await();

  EXPECT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
}

TEST_F(NameSpaceTestV2, ReleaseLeaseOfDeletedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_EQ(s.exception(), JavaExceptions::kFileNotFoundException);
  EXPECT_EQ(s.message(),
            "INode not found: 17123. "
            "The file may have been deleted, "
            "please check if your application has concurrency issues.");
}

TEST_F(NameSpaceTestV2, ReleaseLeaseLMCheckLeaseThrowsException) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-2", 17123))
      .Times(1)
      .WillOnce(testing::Return(false));
  // ReleaseLeaseInternal: Release lease has already been clear, ...
  EXPECT_CALL(*lease_manager_, GetLeaseHolder(17123, true))
      .Times(1)
      .WillOnce(testing::Return("client-name-1"));

  Status s = ns_->ReleaseLease(17123, "client-name-2", "client-name-3");
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseBMNeedReleaseThrowsException) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::_,
                  testing::_))
      .Times(1)
      .WillOnce(testing::Return(
          Status(JavaExceptions::kAlreadyBeingCreatedException,
                 "Failed to release lease. "
                 "Committed blocks are waiting to be minimally replicated. "
                 "Try again later.")));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(s.message(),
            "Failed to release lease. "
            "Committed blocks are waiting to be minimally replicated. "
            "Try again later.");
}

TEST_F(NameSpaceTestV2, ReleaseLeaseAndRemoveLastBlockThenCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(0)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 0))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(true),
          testing::SetArgPointee<2>(Block(kInvalidBlockID, 0, 0)),
          testing::Return(Status(Code::kFalse))));
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          Block(kInvalidBlockID, 0, 0),
          1084217910,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 0)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
              testing::_,
              testing::_,
              testing::Not(nullptr),
              testing::IsNull()))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<6>(testing::Invoke(
                                   [](RpcClosure* done) { done->Run(); })),
                               testing::Return(Status())));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseAndRemoveLastBlockNoCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(0)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 0))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(true),
          testing::SetArgPointee<2>(Block(kInvalidBlockID, 0, 0)),
          testing::Return(Status(Code::kFalse))));

  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(*edit_log_sender_,
              LogReassignLeaseV2("/b",
                                 testing::_,
                                 testing::_,
                                 "client-name-1",
                                 "client-name-2",
                                 testing::_))
      .Times(1)
      .WillOnce(testing::Return(2));
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINode(testing::_,
                                 testing::_,
                                 testing::_,
                                 2,
                                 testing::Not(nullptr),
                                 nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  auto inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(&INode::has_uc, true),
      testing::Property(
          &INode::uc,
          testing::Property(&FileUnderConstructionFeature::client_name,
                            "client-name-2")),
      testing::Property(&INode::status, INode::kFileUnderConstruction));
  auto old_inode_matcher =
      testing::AllOf(inode_matcher, testing::Property(&INode::blocks_size, 1));
  auto new_inode_matcher =
      testing::AllOf(inode_matcher, testing::Property(&INode::blocks_size, 0));
  EXPECT_CALL(
      *edit_log_sender_,
      LogUpdateBlocksV2(
          "/b", new_inode_matcher, old_inode_matcher, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(3));
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINodeAndDeleteLastBlock(new_inode_matcher,
                                                   1084217910,
                                                   testing::Not(nullptr),
                                                   3,
                                                   testing::Not(nullptr),
                                                   nullptr))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); }))));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2", false);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseAndCommitLastBlockThenCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(false),
          testing::SetArgPointee<2>(Block(1084217910, 2048, 1002)),
          testing::Return(Status(Code::kFalse))));
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          testing::AllOf(testing::Field(&Block::id, 1084217910),
                         testing::Field(&Block::gs, 1002),
                         testing::Field(&Block::num_bytes, 2048)),
          kInvalidBlockID,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
              testing::_,
              testing::_,
              testing::Not(nullptr),
              testing::IsNull()))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<6>(testing::Invoke(
                                   [](RpcClosure* done) { done->Run(); })),
                               testing::Return(Status())));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseAndCommitLastBlockNoCloseFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(false),
          testing::SetArgPointee<2>(Block(1084217910, 2048, 1002)),
          testing::Return(Status(Code::kFalse))));

  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(true));

  auto inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(&INode::blocks_size, 1),
      testing::Property(&INode::has_uc, true),
      testing::Property(&INode::status, INode::kFileUnderConstruction));
  auto old_inode_matcher = testing::AllOf(
      inode_matcher,
      testing::Property(
          &INode::uc,
          testing::Property(&FileUnderConstructionFeature::client_name,
                            "client-name-1")));
  auto new_inode_matcher = testing::AllOf(
      inode_matcher,
      testing::Property(
          &INode::uc,
          testing::Property(&FileUnderConstructionFeature::client_name,
                            "client-name-2")));
  EXPECT_CALL(*edit_log_sender_,
              LogReassignLeaseV2("/b",
                                 new_inode_matcher,
                                 old_inode_matcher,
                                 "client-name-1",
                                 "client-name-2",
                                 testing::_))
      .Times(1)
      .WillOnce(testing::Return(2));
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINode(testing::Pointee(new_inode_matcher),
                                 testing::Pointee(old_inode_matcher),
                                 testing::_,
                                 2,
                                 testing::Not(nullptr),
                                 nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  EXPECT_CALL(*block_manager_, BlockHasBeenCommitted(1084217910))
      .Times(1)
      .WillOnce(testing::Return(true));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2", false);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseLMReassignLeaseThrowsException) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(false),
                               testing::Return(Status(Code::kOK))));
  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(false));

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_TRUE(s.IsFalse());
}

TEST_F(NameSpaceTestV2, ReleaseLeaseStartBlockRecovery) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  auto b_inode = INodeBuilder()
                     .SetId(17123)
                     .SetParentId(kRootINodeId)
                     .SetName("b")
                     .SetType(INode::kFile)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1084217910)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .SetUc(FileUnderConstructionFeatureBuilder()
                                .SetClientName("client-name-1")
                                .SetClientMachine("client-machine-1")
                                .Build())
                     .SetStatus(INode::kFileUnderConstruction)
                     .Build();
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(b_inode),
                               testing::Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetINode(17123, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(b_inode),
                               testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(false),
                               testing::Return(Status(Code::kOK))));

  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(true));
  auto b_inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(&INode::blocks,
                        testing::ElementsAre(testing::AllOf(
                            testing::Property(&BlockProto::blockid, 1084217910),
                            testing::Property(&BlockProto::genstamp, 1001),
                            testing::Property(&BlockProto::numbytes, 1024)))),
      testing::Property(
          &INode::uc,
          testing::AllOf(
              testing::Property(&FileUnderConstructionFeature::client_name,
                                "client-name-2"),
              testing::Property(&FileUnderConstructionFeature::client_machine,
                                "client-machine-1"))),
      testing::Property(&INode::status, INode::kFileUnderConstruction));
  EXPECT_CALL(*edit_log_sender_,
              LogReassignLeaseV2("/b",
                                 b_inode_matcher,
                                 testing::_,
                                 "client-name-1",
                                 "client-name-2",
                                 testing::_))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(
      *meta_storage_,
      OrderedUpdateINode(testing::_,
                         testing::_,
                         testing::_,
                         1,
                         testing::Not(nullptr),
                         nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  int64_t expect_txid = 2;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  }
  EXPECT_CALL(*edit_log_sender_, LogSetGenerationStampV2(1001, false))
      .Times(1)
      .WillOnce(testing::Return(expect_txid));
#if 0
  EXPECT_CALL(*meta_storage_,
              OrderedPutGenerationStampV2(testing::_,
                                          2,
                                          testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<2>(
          testing::Invoke([](Closure* done) { done->Run(); })));
#endif

  EXPECT_CALL(
      *block_manager_,
      InitRecover(
          testing::AllOf(testing::Property(&BlockProto::blockid, 1084217910),
                         testing::Property(&BlockProto::genstamp, 1001),
                         testing::Property(&BlockProto::numbytes, 1024)),
          1001,
          true))
      .Times(1);

  Status s = ns_->ReleaseLease(17123, "client-name-1", "client-name-2");
  EXPECT_TRUE(s.IsFalse());
}

TEST_F(NameSpaceTestV2, RecoverLeaseOfDeletedFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-1", "/b", &recover_request);
  EXPECT_EQ(s.exception(), JavaExceptions::kFileNotFoundException);
}

TEST_F(NameSpaceTestV2, RecoverLeaseOfNonUcFile) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(
                                   INodeBuilder()
                                       .SetId(17123)
                                       .SetParentId(kRootINodeId)
                                       .SetName("b")
                                       .SetType(INode::kFile)
                                       .AddBlock(BlockProtoBuilder()
                                                     .SetBlockId(1084217910)
                                                     .SetGenStamp(1001)
                                                     .SetNumBytes(1024)
                                                     .Build())
                                       .SetStatus(INode::kFileUnderConstruction)
                                       .Build()),
                               testing::Return(StatusCode::kOK)));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-1", "/b", &recover_request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(NameSpaceTestV2, RecoverLeaseLMCanRecoverLeaseThrowsException) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::Return(
          Status(JavaExceptions::kIOException,
                 "LeaseManager::CanRecoverLease throws exception")));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "LeaseManager::CanRecoverLease throws exception");
}

TEST_F(NameSpaceTestV2, RecoverLeaseOfEmptyFileWhichInSoftLimit) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<4>(true),
                               testing::Return(Status(Code::kFalse))));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
}

TEST_F(NameSpaceTestV2, RecoverLeaseOfFileWhichLastBlockIsUrAndInSoftLimit) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<4>(true),
                               testing::Return(Status(Code::kFalse))));
  EXPECT_CALL(*block_manager_, GetBlockUCState(1084217910))
      .Times(1)
      .WillOnce(testing::Return(BlockUCState::kUnderRecovery));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_EQ(s.exception(), JavaExceptions::kRecoveryInProgressException);
  EXPECT_EQ(s.message(), "Recovery in progress for file /b");
}

TEST_F(NameSpaceTestV2,
       RecoverLeaseOfFileWhichLastBlockIsCommittedAndInSoftLimit) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<4>(true),
                               testing::Return(Status(Code::kFalse))));
  EXPECT_CALL(*block_manager_, GetBlockUCState(1084217910))
      .Times(1)
      .WillOnce(testing::Return(BlockUCState::kCommitted));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(s.message(),
            "Failed to create file /b for client client-name-2, "
            "because this file is already being created");
}

TEST_F(NameSpaceTestV2, RecoverLeaseTriggersReleaseLease) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // ReleaseLeaseInternal
  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(false),
          testing::SetArgPointee<2>(Block(1084217910, 2048, 1002)),
          testing::Return(Status(Code::kFalse))));
  EXPECT_CALL(
      *file_finalizer_,
      FinalizeFile(
          "/b",
          testing::AllOf(testing::Field(&Block::id, 1084217910),
                         testing::Field(&Block::gs, 1002),
                         testing::Field(&Block::num_bytes, 2048)),
          kInvalidBlockID,
          WrapINodeMatcher(testing::AllOf(
              testing::Property(&INode::id, 17123),
              testing::Property(
                  &INode::blocks,
                  testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1001),
                      testing::Property(&BlockProto::numbytes, 1024)))),
              testing::Property(&INode::has_uc, true),
              testing::Property(&INode::status,
                                INode::kFileUnderConstruction))),
              testing::_,
              testing::_,
              testing::Not(nullptr),
              testing::IsNull()))
      .Times(1)
      .WillOnce(testing::DoAll(testing::WithArg<6>(testing::Invoke(
                                   [](RpcClosure* done) { done->Run(); })),
                               testing::Return(Status())));

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_TRUE(s.IsFalse());
}

// Refer to the first part of NameSpaceTest.DNReportLeaseRecover.
TEST_F(NameSpaceTestV2, RecoverLeaseTriggerBlockRecovery) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));

  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/b"),
                1))
      .Times(1);

  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, "b", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<2>(
              INodeBuilder()
                  .SetId(17123)
                  .SetParentId(kRootINodeId)
                  .SetName("b")
                  .SetType(INode::kFile)
                  .AddBlock(BlockProtoBuilder()
                                .SetBlockId(1084217910)
                                .SetGenStamp(1000)
                                .SetNumBytes(1024)
                                .Build())
                  .SetUc(FileUnderConstructionFeatureBuilder()
                             .SetClientName("client-name-1")
                             .SetClientMachine("client-machine-1")
                             .Build())
                  .SetStatus(INode::kFileUnderConstruction)
                  .Build()),
          testing::Return(StatusCode::kOK)));

  EXPECT_CALL(
      *lease_manager_,
      CanRecoverLease(
          17123, "client-name-1", "client-name-2", true, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // ReleaseLeaseInternal
  EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 17123))
      .Times(1)
      .WillOnce(testing::Return(true));
  EXPECT_CALL(
      *block_manager_,
      NeedRelease(testing::ElementsAre(testing::AllOf(
                      testing::Property(&BlockProto::blockid, 1084217910),
                      testing::Property(&BlockProto::genstamp, 1000),
                      testing::Property(&BlockProto::numbytes, 1024))),
                  testing::Not(nullptr),
                  testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::DoAll(
          testing::SetArgPointee<1>(false),
          testing::SetArgPointee<2>(Block(kInvalidBlockID, 0, 0)),
          testing::Return(Status())));

  // ReassignLease
  EXPECT_CALL(*lease_manager_,
              ReassignLease("client-name-1", 17123, "client-name-2"))
      .Times(1)
      .WillOnce(testing::Return(true));
  auto inode_matcher = testing::AllOf(
      testing::Property(&INode::id, 17123),
      testing::Property(
          &INode::uc,
          testing::Property(&FileUnderConstructionFeature::client_name,
                            "client-name-2")),
      testing::Property(&INode::blocks,
                        testing::ElementsAre(testing::AllOf(
                            testing::Property(&BlockProto::blockid, 1084217910),
                            testing::Property(&BlockProto::genstamp, 1000),
                            testing::Property(&BlockProto::numbytes, 1024)))));
  EXPECT_CALL(*edit_log_sender_,
              LogReassignLeaseV2("/b",
                                 inode_matcher,
                                 testing::_,
                                 "client-name-1",
                                 "client-name-2",
                                 testing::_))
      .Times(1)
      .WillOnce(testing::Return(1));
  EXPECT_CALL(*meta_storage_,
              OrderedUpdateINode(testing::Pointee(inode_matcher),
                                 testing::_,
                                 testing::_,
                                 1,
                                 testing::Not(nullptr),
                                 nullptr))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));

  // ReleaseLeaseInternal
  int64_t expect_txid = 2;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  }
  EXPECT_CALL(*edit_log_sender_, LogSetGenerationStampV2(1001, false))
      .Times(1)
      .WillOnce(testing::Return(expect_txid));
#if 0
  EXPECT_CALL(*meta_storage_,
              OrderedPutGenerationStampV2(1001, 2, testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<2>(
          testing::Invoke([](Closure* done) { done->Run(); })));
#endif
  EXPECT_CALL(
      *block_manager_,
      InitRecover(
          testing::AllOf(testing::Property(&BlockProto::blockid, 1084217910),
                         testing::Property(&BlockProto::genstamp, 1000),
                         testing::Property(&BlockProto::numbytes, 1024)),
          1001,
          true))
      .Times(1);

  RecoverLeaseRequestProto recover_request;
  Status s = ns_->RecoverLease("client-name-2", "/b", &recover_request);
  EXPECT_TRUE(s.IsFalse());
}

// Ref: NameSpaceTest.RemoveLease
TEST_F(NameSpaceTestV2, RemoveLeaseWhenRecycleDanglingINode) {
  GMockBGDeletionTask bg_deletion_task(ns_.get());
  // /xxx/a is a danling inode.
  EXPECT_CALL(*meta_storage_, ScanPendingDeleteCF(testing::_))
      .Times(1)
      .WillOnce(testing::Invoke(
          [](const std::function<bool(const INode&)>& callback) {
            callback(INodeBuilder()
                         .SetId(17123)
                         .SetParentId(12122)
                         .SetName("a")
                         .SetType(INode::kDirectory)
                         .Build());
          }));
  // BGDeletionTask should recycle sub inodes of /xxx/a, which is /xxx/a/b.
  EXPECT_CALL(*meta_storage_, ForEachDir(17123, "a", testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::WithArg<3>(testing::Invoke(
          [](const std::function<bool(const std::string&, const INode&)>& cb) {
            cb("b",
               INodeBuilder()
                   .SetId(17124)
                   .SetParentId(12123)
                   .SetName("b")
                   .SetType(INode::kFile)
                   .SetUc(FileUnderConstructionFeatureBuilder()
                              .SetClientName("client-name-1")
                              .SetClientMachine("client-machine-1")
                              .Build())
                   .Build());
            return true;
          })));
  EXPECT_CALL(*meta_storage_, ExistSnapshotINode(testing::_))
      .WillRepeatedly(testing::Return(false));

  auto wb_for_delete_dangling = new testing::StrictMock<GMockWriteBatch>();
  auto wb_for_delete_pd = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(*meta_storage_, CreateWriteBatch())
      .Times(2)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<rocksdb::WriteBatch>(wb_for_delete_dangling))))
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<rocksdb::WriteBatch>(wb_for_delete_pd))));

  // Delete Dangling INodes
  EXPECT_CALL(*wb_for_delete_dangling,
              Put(testing::Not(kLeaseCFHandle), testing::_, testing::_))
      .Times(testing::AtLeast(0));
  EXPECT_CALL(*wb_for_delete_dangling,
              Delete(testing::Not(kLeaseCFHandle), testing::_))
      .Times(testing::AtLeast(0));
  EXPECT_CALL(*wb_for_delete_dangling,
              Delete(kLeaseCFHandle,
                     RocksDBSliceEq(GMockMetaStorage::EncodeINodeID(17124))))
      .Times(1);

  // Delete PendingDelete INodes
  EXPECT_CALL(*wb_for_delete_pd, Put(testing::_, testing::_, testing::_))
      .Times(testing::AtLeast(0));
  EXPECT_CALL(*wb_for_delete_pd, Delete(testing::_, testing::_))
      .Times(testing::AtLeast(0));
  EXPECT_CALL(
      *meta_storage_writer_,
      PushBGTask(testing::Property(
          &meta_storage::WriteTask::wb,
          testing::Property(&std::unique_ptr<rocksdb::WriteBatch>::get,
                            testing::AnyOf(wb_for_delete_dangling, wb_for_delete_pd)))))
      .Times(2)
      .WillRepeatedly(testing::Invoke([](meta_storage::WriteTask* task) {
        if (task->HasNext()) {
          task->Next();
        }
        delete task;
      }));

  EXPECT_CALL(bg_deletion_task, IsStopped())
      .Times(testing::AtLeast(2))
      .WillOnce(testing::Return(false))
      .WillOnce(testing::Return(false))
      .WillRepeatedly(testing::Return(true));
  bg_deletion_task();
}

TEST(NameSpaceFuncTest, ComputeFileSyncActionUfsFileOnly) {
  std::string file_name = "test-file";
  std::string file_path = "/" + file_name;

  FileSyncAction action;

  {
    // Local not found, UFS not found -> NONE
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(nullptr, nullptr, &action).IsOK());
    EXPECT_EQ(FILE_ACTION_NONE, action);
  }

  {
    // Local not found, UFS is file -> CREATE
    UfsFileStatus ufs_file_status;
    EXPECT_TRUE(UfsFileStatusBuilder()
                    .SetFileType(UFS_FILE)
                    .SetFullPath(file_path)
                    .Build(&ufs_file_status)
                    .IsOK());
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(nullptr, &ufs_file_status, &action)
            .IsOK());
    EXPECT_EQ(FILE_ACTION_CREATE, action);
  }

  {
    // Local is dir -> NONE
    INode inode;
    inode.set_type(INode_Type_kDirectory);
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, nullptr, &action).IsOK());
    EXPECT_EQ(FILE_ACTION_NONE, action);
  }

  {
    // Local is file to be persisted, UFS not found -> NONE
    INode inode;
    inode.set_type(INode_Type_kFile);
    inode.mutable_ufs_file_info()->set_file_state(kUfsFileStateToBePersisted);
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, nullptr, &action).IsOK());
    EXPECT_EQ(FILE_ACTION_NONE, action);
  }

  {
    // Local is file persisted, UFS not found -> DELETE
    INode inode;
    inode.set_type(INode_Type_kFile);
    inode.mutable_ufs_file_info()->set_file_state(kUfsFileStatePersisted);
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, nullptr, &action).IsOK());
    EXPECT_EQ(FILE_ACTION_DELETE, action);
  }

  {
    // Local is file, UFS is file, etags are equal -> UPDATE_TIME
    std::string etag = "test-etag";
    INode inode;
    inode.set_name(file_name);
    inode.set_type(INode_Type_kFile);
    inode.mutable_ufs_file_info()->set_etag(etag);
    UfsFileStatus ufs_file_status;
    EXPECT_TRUE(UfsFileStatusBuilder()
                    .SetFileType(UFS_FILE)
                    .SetFullPath(file_path)
                    .SetEtag(etag)
                    .Build(&ufs_file_status)
                    .IsOK());
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, &ufs_file_status, &action)
            .IsOK());
    EXPECT_EQ(FILE_ACTION_UPDATE_TIME, action);
  }

  {
    // Local is file, UFS is file, etags are not equal -> OVERWRITE
    INode inode;
    inode.set_name(file_name);
    inode.set_type(INode_Type_kFile);
    inode.mutable_ufs_file_info()->set_etag("test-etag-1");
    UfsFileStatus ufs_file_status;
    EXPECT_TRUE(UfsFileStatusBuilder()
                    .SetFileType(UFS_FILE)
                    .SetFullPath(file_path)
                    .SetEtag("test-etag-2")
                    .Build(&ufs_file_status)
                    .IsOK());
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, &ufs_file_status, &action)
            .IsOK());
    EXPECT_EQ(FILE_ACTION_OVERWRITE, action);
  }

  {
    // Local is file, UFS is file, etags is empty
    // mtime is equal -> UPDATE_TIME
    INode inode;
    inode.set_name(file_name);
    inode.set_type(INode_Type_kFile);
    auto now_ts = TimeUtil::GetNowEpochMs();
    inode.mutable_ufs_file_info()->set_last_modified_ts(now_ts);
    UfsFileStatus ufs_file_status;
    EXPECT_TRUE(UfsFileStatusBuilder()
                    .SetFileType(UFS_FILE)
                    .SetFullPath(file_path)
                    .SetModifiedTs(now_ts)
                    .Build(&ufs_file_status)
                    .IsOK());
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, &ufs_file_status, &action)
            .IsOK());
    EXPECT_EQ(FILE_ACTION_UPDATE_TIME, action);
  }

  {
    // Local is file, UFS is file, etags is empty
    // local_mtime < remote_mtime -> OVERWRITE
    INode inode;
    inode.set_name(file_name);
    inode.set_type(INode_Type_kFile);
    auto now_ts = TimeUtil::GetNowEpochMs();
    inode.mutable_ufs_file_info()->set_last_modified_ts(now_ts - 1000);
    UfsFileStatus ufs_file_status;
    EXPECT_TRUE(UfsFileStatusBuilder()
                    .SetFileType(UFS_FILE)
                    .SetFullPath(file_path)
                    .SetModifiedTs(now_ts)
                    .Build(&ufs_file_status)
                    .IsOK());
    EXPECT_TRUE(
        ComputeFileSyncActionUfsFileOnly(&inode, &ufs_file_status, &action)
            .IsOK());
    EXPECT_EQ(FILE_ACTION_OVERWRITE, action);
  }
}

TEST_F(NameSpaceTestV2, SyncUfsFileOnly) {
  std::string name = "a";
  std::string path = "/" + name;
  // Mock CheckOperationSafeMode
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
  // Mock local not found
  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, name, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Return(StatusCode::kFileNotFound));
  // Mock UFS not found
  std::shared_ptr<GMockUfs> ufs = std::make_shared<GMockUfs>();
  EXPECT_CALL(*ufs, GetFileOnlyStatus(path, testing::_))
      .Times(1)
      .WillOnce(testing::Return(
          Status(Code::kFileNotFound, "mocked file not found")));

  PermissionStatus p;
  UserGroupInfo ugi;
  EXPECT_TRUE(ns_->SyncUfsFileOnly(path, path, p, ugi, ufs, false, "").IsOK());
}

TEST_F(NameSpaceTestV2, SyncUfsFileOnlyEtagEqual) {
  std::string name = "a";
  std::string path = "/" + name;
  std::string etag = "test-etag";
  // Mock CheckOperationSafeMode
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));
  EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
  // Mock local file
  EXPECT_CALL(
      *rwlock_manager_,
      ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
               0))
      .Times(1);
  EXPECT_CALL(
      *rwlock_manager_,
      WriteLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/a"),
                1))
      .Times(1);
  INode inode = INodeBuilder()
                    .SetId(kRootINodeId + 1)
                    .SetParentId(kRootINodeId)
                    .SetType(INode::kFile)
                    .Build();
  inode.mutable_ufs_file_info()->set_etag(etag);
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*meta_storage_,
              GetINode(kRootINodeId, name, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(inode),
                               testing::Return(StatusCode::kOK)));
  // Mock UFS
  std::shared_ptr<GMockUfs> ufs = std::make_shared<GMockUfs>();

  PermissionStatus p;
  UserGroupInfo ugi;
  EXPECT_TRUE(ns_->SyncUfsFileOnly(path, path, p, ugi, ufs, true, etag).IsOK());
}

TEST_F(NameSpaceTestV2, ReconcileINodeAttrs) {
  // Reconcile update pin
  {
    EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
        .Times(1)
        .WillOnce(testing::Return(
            testing::ByMove(std::make_pair(Status(), vshared_lock()))));
    EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
    EXPECT_CALL(
        *rwlock_manager_,
        ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
                 0))
        .Times(1);
    EXPECT_CALL(*rwlock_manager_,
                WriteLock(testing::Property(
                              &cnetpp::base::StringPiece::as_string, "/file"),
                          1))
        .Times(1);

    INode root =
        INodeBuilder().SetId(kRootINodeId).SetType(INode::kDirectory).Build();
    PinStatus* parent_pin = root.mutable_pin_status();
    parent_pin->set_pinned(true);
    parent_pin->set_ttl(-1);
    parent_pin->set_txid(123);
    parent_pin->set_recursive(true);
    parent_pin->set_job_id("JOB_1");
    INode inode = INodeBuilder()
                      .SetId(kRootINodeId + 1)
                      .SetParentId(kRootINodeId)
                      .SetType(INode::kFile)
                      .SetName("file")
                      .Build();
    BlockProto* b = inode.add_blocks();
    b->set_blockid(1);
    b->set_genstamp(1);
    b->set_numbytes(1);
    EXPECT_CALL(
        *block_manager_,
        UpdateBlockPinStatus(testing::Property(&INode::id, inode.id()), 1))
        .Times(1);
    EXPECT_CALL(*meta_storage_, GetRootINode())
        .Times(1)
        .WillOnce(testing::Return(root));
    EXPECT_CALL(*meta_storage_, GetINode(testing::_, testing::_, testing::_))
        .Times(2)
        .WillOnce(testing::DoAll(testing::SetArgPointee<1>(inode),
                                 testing::Return(StatusCode::kOK)))
        .WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(inode),
                                 testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*meta_storage_,
                GetINode(testing::_, testing::_, testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::SetArgPointee<2>(inode),
            testing::Return(StatusCode::kOK)));
    inode.mutable_pin_status()->CopyFrom(*parent_pin);
    EXPECT_CALL(*edit_log_sender_,
                LogReconcileINodeAttrs("/file",
                                       testing::_,
                                       testing::_,
                                       testing::_,
                                       testing::_,
                                       testing::_))
        .Times(1)
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                ReconcileINodeAttrs(testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_))
        .Times(1)
        .WillOnce(testing::WithArg<7>(
            testing::Invoke([](Closure* done) { done->Run(); })));

    ns_->ReconcileINodeAttrs(inode);
  }

  // Reconcile update unpin
  {
    EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
        .Times(1)
        .WillOnce(testing::Return(
            testing::ByMove(std::make_pair(Status(), vshared_lock()))));
    EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
    EXPECT_CALL(
        *rwlock_manager_,
        ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
                 0))
        .Times(1);
    EXPECT_CALL(*rwlock_manager_,
                WriteLock(testing::Property(
                              &cnetpp::base::StringPiece::as_string, "/file"),
                          1))
        .Times(1);

    INode root =
        INodeBuilder().SetId(kRootINodeId).SetType(INode::kDirectory).Build();
    PinStatus* parent_pin = root.mutable_pin_status();
    parent_pin->set_pinned(false);
    parent_pin->set_ttl(-1);
    parent_pin->set_txid(123);
    parent_pin->set_recursive(true);
    parent_pin->set_job_id("JOB_1");
    INode inode = INodeBuilder()
                      .SetId(kRootINodeId + 1)
                      .SetParentId(kRootINodeId)
                      .SetType(INode::kFile)
                      .SetName("file")
                      .Build();
    PinStatus* child_pin = inode.mutable_pin_status();
    child_pin->set_pinned(true);
    child_pin->set_ttl(-1);
    child_pin->set_txid(122);
    child_pin->set_recursive(true);
    BlockProto* b = inode.add_blocks();
    b->set_blockid(1);
    b->set_genstamp(1);
    b->set_numbytes(1);
    EXPECT_CALL(
        *block_manager_,
        UpdateBlockPinStatus(testing::Property(&INode::id, inode.id()), 1))
        .Times(1);
    EXPECT_CALL(*meta_storage_, GetRootINode())
        .Times(1)
        .WillOnce(testing::Return(root));
    EXPECT_CALL(*meta_storage_, GetINode(testing::_, testing::_, testing::_))
        .Times(2)
        .WillOnce(testing::DoAll(testing::SetArgPointee<1>(inode),
                                 testing::Return(StatusCode::kOK)))
        .WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(inode),
                                 testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*meta_storage_,
                GetINode(testing::_, testing::_, testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::SetArgPointee<2>(inode),
            testing::Return(StatusCode::kOK)));
    inode.mutable_pin_status()->CopyFrom(*parent_pin);
    EXPECT_CALL(*edit_log_sender_,
                LogReconcileINodeAttrs("/file",
                                       testing::_,
                                       testing::_,
                                       testing::_,
                                       testing::_,
                                       testing::_))
        .Times(1)
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                ReconcileINodeAttrs(testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_))
        .Times(1)
        .WillOnce(testing::WithArg<7>(
            testing::Invoke([](Closure* done) { done->Run(); })));

    ns_->ReconcileINodeAttrs(inode);
  }
}

TEST_F(NameSpaceTestV2, Pin) {
  // Pin file
  {
    PinRequestProto req;
    req.set_src("/file");
    req.set_ttl(-1);
    req.set_recursive(true);
    req.set_unpin(false);
    PinResponseProto res;
    SynchronizedRpcClosure rpc_done;

    EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
        .Times(1)
        .WillOnce(testing::Return(
            testing::ByMove(std::make_pair(Status(), vshared_lock()))));
    EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
    EXPECT_CALL(
        *rwlock_manager_,
        ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
                 0))
        .Times(1);
    EXPECT_CALL(*rwlock_manager_,
                WriteLock(testing::Property(
                              &cnetpp::base::StringPiece::as_string, "/file"),
                          1))
        .Times(1);

    EXPECT_CALL(*meta_storage_, GetRootINode())
        .Times(1)
        .WillOnce(testing::Return(INodeBuilder()
                                      .SetId(kRootINodeId)
                                      .SetType(INode::kDirectory)
                                      .Build()));
    EXPECT_CALL(*meta_storage_,
                GetINode(testing::_, testing::_, testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::SetArgPointee<2>(INodeBuilder()
                                          .SetId(kRootINodeId + 1)
                                          .SetParentId(kRootINodeId)
                                          .SetType(INode::kFile)
                                          .SetName("file")
                                          .Build()),
            testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*edit_log_sender_,
                LogPin(testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_))
        .Times(1)
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                PinINode(testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_))
        .Times(1)
        .WillOnce(testing::WithArg<6>(
            testing::Invoke([](Closure* done) { done->Run(); })));

    ns_->AsyncPin("/file", &req, &res, LogRpcInfo(), &rpc_done, nullptr);
    rpc_done.Await();
    EXPECT_TRUE(rpc_done.status().IsOK());
  }

  // Pin file
  {
    PinRequestProto req;
    req.set_src("/dir");
    req.set_ttl(-1);
    req.set_recursive(true);
    req.set_unpin(false);
    PinResponseProto res;
    SynchronizedRpcClosure rpc_done;

    EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
        .Times(1)
        .WillOnce(testing::Return(
            testing::ByMove(std::make_pair(Status(), vshared_lock()))));
    EXPECT_CALL(*safemode_, IsOn()).Times(1).WillOnce(testing::Return(false));
    EXPECT_CALL(
        *rwlock_manager_,
        ReadLock(testing::Property(&cnetpp::base::StringPiece::as_string, "/"),
                 0))
        .Times(1);
    EXPECT_CALL(*rwlock_manager_,
                WriteLock(testing::Property(
                              &cnetpp::base::StringPiece::as_string, "/dir"),
                          1))
        .Times(1);

    EXPECT_CALL(*meta_storage_, GetRootINode())
        .Times(1)
        .WillOnce(testing::Return(INodeBuilder()
                                      .SetId(kRootINodeId)
                                      .SetType(INode::kDirectory)
                                      .Build()));
    INode dir = INodeBuilder()
                    .SetId(kRootINodeId + 1)
                    .SetParentId(kRootINodeId)
                    .SetType(INode::kDirectory)
                    .SetName("dir")
                    .Build();
    EXPECT_CALL(*meta_storage_,
                GetINode(testing::_, testing::_, testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::SetArgPointee<2>(dir),
            testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*job_manager_, GenerateJobId())
        .Times(1)
        .WillOnce(testing::Return("JOB_1"));
    EXPECT_CALL(
        *job_manager_,
        SubmitReconcileINodeAttrsJob(testing::Property(&INode::id, dir.id()),
                                     testing::_,
                                     testing::_,
                                     testing::_))
        .Times(1)
        .WillOnce(testing::Return(Status::OK()));
    EXPECT_CALL(*edit_log_sender_,
                LogPin(testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_,
                       testing::_))
        .Times(1)
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                PinINode(testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_))
        .Times(1)
        .WillOnce(testing::WithArg<6>(
            testing::Invoke([](Closure* done) { done->Run(); })));

    ns_->AsyncPin("/dir", &req, &res, LogRpcInfo(), &rpc_done, nullptr);
    rpc_done.Await();
    EXPECT_TRUE(rpc_done.status().IsOK());
  }
}

TEST_F(NameSpaceTestV2, RenameUfsHandleCopyResult) {
  EXPECT_CALL(*meta_storage_, GetSnapshot())
      .WillRepeatedly(::testing::Return(
          ::testing::ByMove(std::make_unique<MetaStorageSnapHolder>())));
  EXPECT_CALL(*meta_storage_,
              GetIterator(::testing::Not(nullptr), ::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(
          ::testing::ByMove(std::make_unique<MetaStorageIterHolder>())));
  TosConfig tos_config("", "", "", "/");
  UfsConfig ufs_config = TosConfig::CreateUfsConfig(tos_config);
  std::shared_ptr<RenameSyncContext> ctx = std::make_shared<RenameSyncContext>(
      std::make_shared<RenameToOption>(),
      std::make_shared<GMockTosUfs>(
          ufs_config,
          tos_config,
          std::make_shared<GMockTosCredentialProvider>()));
  // Simple case, no cache hit, parent is root
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    std::unordered_map<std::string, std::string> result = {
        {"/f1", "etag1"},
    };
    EXPECT_CALL(*meta_storage_, GetRootINode())
        .WillOnce(::testing::Return(INodeBuilder()
                                        .SetId(kRootINodeId)
                                        .SetType(INode::kDirectory)
                                        .Build()));
    INode old_inode =
        INodeBuilder().SetId(kRootINodeId + 1).SetType(INode::kFile).Build();
    old_inode.mutable_ufs_file_info()->set_etag("etag");
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "f1", ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(old_inode),
                                   ::testing::Return(StatusCode::kOK)));
    // pin status is inherited from parent
    old_inode.mutable_pin_status()->set_pinned(false);
    old_inode.mutable_pin_status()->set_ttl(-1);
    old_inode.mutable_pin_status()->set_txid(0);
    old_inode.mutable_pin_status()->set_recursive(false);
    INode new_inode = old_inode;
    new_inode.mutable_ufs_file_info()->set_etag("etag1");
    new_inode.mutable_ufs_file_info()->set_sync_ts(1712635959000L + 1000);
    EXPECT_CALL(*edit_log_sender_,
                LogAccSyncUpdateINode("/f1", ::testing::_, ::testing::_))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<1, 2>(
                [&new_inode, &old_inode](const INode& arg0, const INode& arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0.SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1.SerializeAsString());
                }),
            ::testing::Return(1)));
    EXPECT_CALL(
        *meta_storage_,
        OrderedUpdateINode(
            ::testing::_, ::testing::_, ::testing::_, 1, ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<0, 1>(
                [&new_inode, &old_inode](INode* arg0, const INode* arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0->SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1->SerializeAsString());
                }),
            ::testing::WithArg<4>([](Closure* done) {
              dynamic_cast<SynchronizedRpcClosure*>(done)->Run();
            })));
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 1712635959000L + 1000, result);
    INodeInPath inode_test;
    EXPECT_TRUE(dir_cache.Get("/", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    int ignored;
    EXPECT_FALSE(dir_negative_cache.Get("/", &ignored));
  }

  // Simple case, no cache hit, parent is not root
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    EXPECT_CALL(*meta_storage_, GetRootINode())
        .WillOnce(::testing::Return(INodeBuilder()
                                        .SetId(kRootINodeId)
                                        .SetType(INode::kDirectory)
                                        .Build()));
    std::unordered_map<std::string, std::string> result = {
        {"/d1/f1", "etag1"},
    };
    INode dir_inode = INodeBuilder()
                          .SetId(kRootINodeId + 1)
                          .SetType(INode::kDirectory)
                          .Build();
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "d1", ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dir_inode),
                                   ::testing::Return(StatusCode::kOK)));
    INode old_inode =
        INodeBuilder().SetId(kRootINodeId + 2).SetType(INode::kFile).Build();
    old_inode.mutable_ufs_file_info()->set_etag("etag");
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId + 1, "f1", ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(old_inode),
                                   ::testing::Return(StatusCode::kOK)));
    // pin status is inherited from parent
    old_inode.mutable_pin_status()->set_pinned(false);
    old_inode.mutable_pin_status()->set_ttl(-1);
    old_inode.mutable_pin_status()->set_txid(0);
    old_inode.mutable_pin_status()->set_recursive(false);
    INode new_inode = old_inode;
    new_inode.mutable_ufs_file_info()->set_etag("etag1");
    new_inode.mutable_ufs_file_info()->set_sync_ts(1712635959000L + 1000);
    EXPECT_CALL(*edit_log_sender_,
                LogAccSyncUpdateINode("/d1/f1", ::testing::_, ::testing::_))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<1, 2>(
                [&new_inode, &old_inode](const INode& arg0, const INode& arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0.SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1.SerializeAsString());
                }),
            ::testing::Return(1)));
    EXPECT_CALL(
        *meta_storage_,
        OrderedUpdateINode(
            ::testing::_, ::testing::_, ::testing::_, 1, ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<0, 1>(
                [&new_inode, &old_inode](INode* arg0, const INode* arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0->SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1->SerializeAsString());
                }),
            ::testing::WithArg<4>([](Closure* done) {
              dynamic_cast<SynchronizedRpcClosure*>(done)->Run();
            })));
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 1712635959000L + 1000, result);
    INodeInPath inode_test;
    EXPECT_TRUE(dir_cache.Get("/", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    EXPECT_TRUE(dir_cache.Get("/d1", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId + 1);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    int ignored;
    EXPECT_FALSE(dir_negative_cache.Get("/", &ignored));
    EXPECT_FALSE(dir_negative_cache.Get("/d1", &ignored));
  }

  // Invalid result
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    std::unordered_map<std::string, std::string> result = {
        {"", "etag"},
        {"f", "etag"},
        {"/", "etag"},
    };
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
  }

  // Parent hit negative cache
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    dir_negative_cache.Set("/d1", 1);
    std::unordered_map<std::string, std::string> result = {
        {"/d1/f1", "etag"},
    };
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
  }

  // Parent hit cache, parent is not root
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    INodeInPath parent;
    INode& parent_inode = parent.MutableInodeUnsafe();
    parent_inode = INodeBuilder()
                       .SetId(kRootINodeId + 1)
                       .SetType(INode::kDirectory)
                       .Build();
    // pin status is inherited from parent
    parent_inode.mutable_pin_status()->set_pinned(false);
    parent_inode.mutable_pin_status()->set_ttl(-1);
    parent_inode.mutable_pin_status()->set_txid(0);
    parent_inode.mutable_pin_status()->set_recursive(false);
    dir_cache.Set("/d1", parent);
    std::unordered_map<std::string, std::string> result = {
        {"/d1/f1", "etag1"},
    };
    INode old_inode =
        INodeBuilder().SetId(kRootINodeId + 2).SetType(INode::kFile).Build();
    old_inode.mutable_ufs_file_info()->set_etag("etag");
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId + 1, "f1", ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(old_inode),
                                   ::testing::Return(StatusCode::kOK)));
    // pin status is inherited from parent
    old_inode.mutable_pin_status()->set_pinned(false);
    old_inode.mutable_pin_status()->set_ttl(-1);
    old_inode.mutable_pin_status()->set_txid(0);
    old_inode.mutable_pin_status()->set_recursive(false);
    INode new_inode = old_inode;
    new_inode.mutable_ufs_file_info()->set_etag("etag1");
    new_inode.mutable_ufs_file_info()->set_sync_ts(0);
    EXPECT_CALL(*edit_log_sender_,
                LogAccSyncUpdateINode("/d1/f1", ::testing::_, ::testing::_))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<1, 2>(
                [&new_inode, &old_inode](const INode& arg0, const INode& arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0.SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1.SerializeAsString());
                }),
            ::testing::Return(1)));
    EXPECT_CALL(
        *meta_storage_,
        OrderedUpdateINode(
            ::testing::_, ::testing::_, ::testing::_, 1, ::testing::_, nullptr))
        .WillOnce(::testing::DoAll(
            ::testing::WithArgs<0, 1>(
                [&new_inode, &old_inode](INode* arg0, const INode* arg1) {
                  EXPECT_EQ(new_inode.SerializeAsString(),
                            arg0->SerializeAsString());
                  EXPECT_EQ(old_inode.SerializeAsString(),
                            arg1->SerializeAsString());
                }),
            ::testing::WithArg<4>([](Closure* done) {
              dynamic_cast<SynchronizedRpcClosure*>(done)->Run();
            })));
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
    INodeInPath inode_test;
    EXPECT_FALSE(dir_cache.Get("/", &inode_test));
    EXPECT_TRUE(dir_cache.Get("/d1", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId + 1);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    int ignored;
    EXPECT_FALSE(dir_negative_cache.Get("/", &ignored));
  }

  // Local parent does not exist
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    EXPECT_CALL(*meta_storage_, GetRootINode())
        .WillOnce(::testing::Return(INodeBuilder()
                                        .SetId(kRootINodeId)
                                        .SetType(INode::kDirectory)
                                        .Build()));
    std::unordered_map<std::string, std::string> result = {
        {"/d1/d2/d3/f1", "etag1"},
    };
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "d1", ::testing::_, nullptr))
        .WillOnce(::testing::Return(StatusCode::kFileNotFound));
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
    INodeInPath inode_test;
    EXPECT_TRUE(dir_cache.Get("/", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    int ignored;
    EXPECT_TRUE(dir_negative_cache.Get("/d1", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2/d3", &ignored));
  }

  // Partial local parent exist in cache
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    INodeInPath parent;
    INode& parent_inode = parent.MutableInodeUnsafe();
    parent_inode = INodeBuilder()
                       .SetId(kRootINodeId + 1)
                       .SetType(INode::kDirectory)
                       .Build();
    // pin status is inherited from parent
    parent_inode.mutable_pin_status()->set_pinned(false);
    parent_inode.mutable_pin_status()->set_ttl(-1);
    parent_inode.mutable_pin_status()->set_txid(0);
    parent_inode.mutable_pin_status()->set_recursive(false);
    dir_cache.Set("/d1", parent);
    parent_inode.clear_pin_status();
    std::unordered_map<std::string, std::string> result = {
        {"/d1/d2/d3/f1", "etag1"},
    };
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId + 1, "d2", ::testing::_, nullptr))
        .WillOnce(::testing::Return(StatusCode::kFileNotFound));
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
    INodeInPath inode_test;
    EXPECT_FALSE(dir_cache.Get("/", &inode_test));
    EXPECT_TRUE(dir_cache.Get("/d1", &inode_test));
    EXPECT_EQ(inode_test.Inode().id(), kRootINodeId + 1);
    EXPECT_EQ(inode_test.Inode().type(), INode::kDirectory);
    EXPECT_FALSE(dir_cache.Get("/d1/d2", &inode_test));
    EXPECT_FALSE(dir_cache.Get("/d1/d2/d3", &inode_test));
    int ignored;
    EXPECT_FALSE(dir_negative_cache.Get("/", &ignored));
    EXPECT_FALSE(dir_negative_cache.Get("/d1", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2/d3", &ignored));
  }

  // Partial local parent exist in negative cache
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    dir_negative_cache.Set("/d1", 0);
    std::unordered_map<std::string, std::string> result = {
        {"/d1/d2/d3/f1", "etag1"},
    };
    ns_->RenameUfsHandleCopyResult(
        ctx, dir_cache, dir_negative_cache, 0, result);
    INodeInPath inode_test;
    EXPECT_FALSE(dir_cache.Get("/", &inode_test));
    EXPECT_FALSE(dir_cache.Get("/d1", &inode_test));
    EXPECT_FALSE(dir_cache.Get("/d1/d2", &inode_test));
    EXPECT_FALSE(dir_cache.Get("/d1/d2/d3", &inode_test));
    int ignored;
    EXPECT_FALSE(dir_negative_cache.Get("/", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2", &ignored));
    EXPECT_TRUE(dir_negative_cache.Get("/d1/d2/d3", &ignored));
  }

  // Ufs config has a prefix
  TosConfig tos_config_p("", "", "", "/prefix/a/b/");
  UfsConfig ufs_config_p = TosConfig::CreateUfsConfig(tos_config_p);
  std::shared_ptr<RenameSyncContext> ctx_p =
      std::make_shared<RenameSyncContext>(
          std::make_shared<RenameToOption>(),
          std::make_shared<GMockTosUfs>(
              ufs_config_p,
              tos_config_p,
              std::make_shared<GMockTosCredentialProvider>()));
  // Parent hit negative cache
  {
    LRUCache<std::string, INodeInPath, true> dir_cache(1000);
    LRUCache<std::string, int, true> dir_negative_cache(1000);
    dir_negative_cache.Set("/d1", 1);
    std::unordered_map<std::string, std::string> result = {
        {"/prefix/a/b/d1/f1", "etag"},
    };
    ns_->RenameUfsHandleCopyResult(
        ctx_p, dir_cache, dir_negative_cache, 0, result);
  }
}

TEST_F(NameSpaceTestV2, EditlogWithBlockLocations) {
  EXPECT_CALL(*ha_state_, CheckOperation(OperationsCategory::kWrite))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))))
      .WillOnce(testing::Return(
          testing::ByMove(std::make_pair(Status(), vshared_lock()))));
  EXPECT_CALL(*ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(*safemode_, IsOn()).WillRepeatedly(testing::Return(false));
  EXPECT_CALL(*meta_storage_, GetSnapshot())
      .WillRepeatedly(::testing::Return(
          ::testing::ByMove(std::make_unique<MetaStorageSnapHolder>())));
  EXPECT_CALL(*meta_storage_,
              GetIterator(::testing::Not(nullptr), ::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(
          ::testing::ByMove(std::make_unique<MetaStorageIterHolder>())));
  EXPECT_CALL(*meta_storage_, GetRootINode())
      .WillRepeatedly(testing::Return(INodeBuilder()
                                          .SetId(kRootINodeId)
                                          .SetType(INode::kDirectory)
                                          .Build()));
  EXPECT_CALL(*edit_log_sender_, LogAllocateBlockIdV2(testing::_))
      .WillRepeatedly(testing::Return(kInvalidTxId));
  EXPECT_CALL(*edit_log_sender_, LogSetGenerationStampV2(testing::_, false))
      .WillRepeatedly(testing::Return(kInvalidTxId));
  EXPECT_CALL(*datanode_manager_, HasEnoughWriteableDatanode(testing::_))
      .WillRepeatedly(testing::Return(true));
  EXPECT_CALL(*rwlock_manager_, ReadLock(testing::_, testing::_))
      .Times(testing::AtLeast(1));
  EXPECT_CALL(*rwlock_manager_, WriteLock(testing::_, testing::_))
      .Times(testing::AtLeast(1));

  std::string src = "/a";
  cnetpp::base::IPAddress client_ip("***********");
  cloudfs::LocationTag client_location_tag;
  NetworkLocationInfo client_location(client_ip, client_location_tag);
  LogRpcInfo rpc_info;
  PermissionStatus p;
  UserGroupInfo ugi;
  RpcController ctx;

  std::vector<DatanodeID> dns = {1, 2};
  std::vector<std::string> dn_uuids = {"dn-1", "dn-2"};
  DatanodeIDProto dnid1;
  dnid1.set_datanodeuuid("dn-1");
  cnetpp::base::IPAddress dnip1("***********");
  DatanodeInfo dn1(1, dnid1, dnip1);
  DatanodeIDProto dnid2;
  dnid2.set_datanodeuuid("dn-2");
  cnetpp::base::IPAddress dnip2("***********");
  DatanodeInfo dn2(2, dnid2, dnip2);

  // AddBlock
  {
    INode inode = INodeBuilder()
                      .SetId(16386)
                      .SetType(INode::kFile)
                      .SetStatus(INode_Status_kFileUnderConstruction)
                      .SetUc(FileUnderConstructionFeatureBuilder()
                                 .SetClientName("client-name-1")
                                 .SetClientMachine("client-machine-1")
                                 .Build())
                      .Build();
    AddBlockRequestProto req;
    req.set_fileid(16386);
    req.set_clientname("client-name-1");
    req.set_src(src);
    AddBlockResponseProto resp;
    SynchronizedRpcClosure done;

    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "a", testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(testing::SetArgPointee<2>(inode),
                                 testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*lease_manager_, CheckLease("client-name-1", 16386))
        .Times(1)
        .WillOnce(testing::Return(true));
    EXPECT_CALL(*block_manager_,
                AnalyzeFileBlocks(
                    testing::_, testing::_, testing::_, testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::Return(Status::OK()));
    EXPECT_CALL(*datanode_manager_,
                ChooseTarget4New(testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(testing::SetArgPointee<9>(dns),
                                 testing::Return(true)));
    EXPECT_CALL(*block_manager_,
                CommitOrCompleteOrPersistLastBlock(testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::Return(false));
    EXPECT_CALL(*block_manager_,
                AddBlock(testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         dns,
                         testing::_))
        .Times(1)
        .WillOnce(testing::Return(Block(1, 0, 1)));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(1))
        .Times(2)
        .WillRepeatedly(testing::Return(&dn1));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(2))
        .Times(2)
        .WillRepeatedly(testing::Return(&dn2));
    EXPECT_CALL(*edit_log_sender_,
                LogAddBlockV2(src,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              dn_uuids,
                              testing::_,
                              testing::_))
        .Times(1)
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                OrderedAddBlock(testing::_,
                                testing::_,
                                testing::_,
                                testing::_,
                                testing::_,
                                testing::_,
                                &done,
                                testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(testing::WithArgs<6>([](Closure* done) {
          dynamic_cast<SynchronizedRpcClosure*>(done)->Run();
        })));
    ns_->AsyncAddBlock(
        src, client_location, rpc_info, ugi, &req, &resp, &ctx, &done);
    done.Await();
  }

  // Create
  {
    INode inode = INodeBuilder()
                      .SetId(16386)
                      .SetType(INode::kFile)
                      .SetStatus(INode_Status_kFileUnderConstruction)
                      .SetUc(FileUnderConstructionFeatureBuilder()
                                 .SetClientName("client-name-1")
                                 .SetClientMachine("client-machine-1")
                                 .Build())
                      .Build();
    CreateRequestProto req;
    CreateResponseProto resp;
    req.set_src(src);
    req.set_clientname("client-name-1");
    req.set_replication(2);
    req.set_createflag(cloudfs::CreateFlagProto::CREATE);
    req.set_withaddblock(true);
    req.set_rpc_type(cloudfs::RpcProtocolType::RPC_BYTERPC_MODE);

    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "a", testing::_, testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(testing::Return(StatusCode::kFileNotFound)));
    EXPECT_CALL(*lease_manager_, AddLease("client-name-1", 16386))
        .Times(1)
        .WillOnce(testing::Return(true));
    EXPECT_CALL(*datanode_manager_,
                ChooseTarget4New(testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(testing::SetArgPointee<9>(dns),
                                 testing::Return(true)));
    EXPECT_CALL(*block_manager_, GetBlock(testing::_))
        .WillOnce(testing::Return(Block(0, 0, 0)));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(1))
        .Times(2)
        .WillRepeatedly(testing::Return(&dn1));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(2))
        .Times(2)
        .WillRepeatedly(testing::Return(&dn2));
    EXPECT_CALL(*block_manager_,
                AddBlock(testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         dns,
                         testing::_))
        .Times(1)
        .WillOnce(testing::Return(Block(1, 0, 1)));

    std::vector<std::vector<std::string>> expected_locs_list{dn_uuids};
    EXPECT_CALL(*edit_log_sender_,
                LogOpenFileV2(testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              expected_locs_list,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_,
                              testing::_))
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                OrderedCommitINodes(testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::WithArgs<10>([](const std::vector<Closure*>& dones) {
              dynamic_cast<SynchronizedRpcClosure*>(dones[0])->Run();
            })));

    SynchronizedRpcClosure done;
    ns_->AsyncCreateFile(src,
                         p,
                         client_location,
                         ugi,
                         rpc_info,
                         "client-machine-1",
                         &req,
                         &resp,
                         &ctx,
                         &done,
                         true,
                         nullptr,
                         nullptr);
    done.Await();
  }

  // BatchCreate
  {
    std::vector<std::string> paths{"/x/a", "/x/b"};
    std::vector<std::string> ufs_keys{"x/a", "x/b"};
    BatchCreateFileRequestProto req;
    BatchCreateFileResponseProto resp;
    SynchronizedRpcClosure done;
    req.set_clientname("client-name-1");
    req.set_withaddblock(true);
    req.set_rpc_type(cloudfs::RpcProtocolType::RPC_BYTERPC_MODE);
    auto file1 = req.add_files();
    file1->set_replication(2);
    file1->mutable_masked()->set_perm(0644);
    file1->set_blocksize(128 * 1024 * 1024);
    file1->set_createflag(cloudfs::CreateFlagProto::CREATE);
    auto file2 = req.add_files();
    file2->set_replication(2);
    file2->mutable_masked()->set_perm(0644);
    file2->set_blocksize(128 * 1024 * 1024);
    file2->set_createflag(cloudfs::CreateFlagProto::CREATE);

    INode x = INodeBuilder()
                  .SetId(16386)
                  .SetType(INode::kDirectory)
                  .SetParentId(kRootINodeId)
                  .SetName("x")
                  .Build();

    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId, "x", testing::_, testing::_))
        .Times(2)
        .WillRepeatedly(testing::DoAll(testing::SetArgPointee<2>(x),
                                       testing::Return(StatusCode::kOK)));
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId + 1, "a", testing::_, testing::_))
        .Times(2)
        .WillRepeatedly(
            testing::DoAll(testing::Return(StatusCode::kFileNotFound)));
    EXPECT_CALL(*meta_storage_,
                GetINode(kRootINodeId + 1, "b", testing::_, testing::_))
        .Times(2)
        .WillRepeatedly(
            testing::DoAll(testing::Return(StatusCode::kFileNotFound)));
    EXPECT_CALL(*lease_manager_, AddLease("client-name-1", kRootINodeId + 2))
        .Times(1)
        .WillOnce(testing::Return(true));
    EXPECT_CALL(*lease_manager_, AddLease("client-name-1", kRootINodeId + 3))
        .Times(1)
        .WillOnce(testing::Return(true));
    EXPECT_CALL(*datanode_manager_,
                ChooseTarget4New(testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_,
                                 testing::_))
        .Times(2)
        .WillRepeatedly(testing::DoAll(testing::SetArgPointee<9>(dns),
                                       testing::Return(true)));
    EXPECT_CALL(*block_manager_, GetBlock(testing::_))
        .Times(2)
        .WillRepeatedly(testing::Return(Block(0, 0, 0)));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(1))
        .Times(4)
        .WillRepeatedly(testing::Return(&dn1));
    EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(2))
        .Times(4)
        .WillRepeatedly(testing::Return(&dn2));
    EXPECT_CALL(*block_manager_,
                AddBlock(testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         testing::_,
                         dns,
                         testing::_))
        .Times(2)
        .WillOnce(testing::Return(Block(1, 0, 1)))
        .WillOnce(testing::Return(Block(2, 0, 2)));

    std::vector<std::vector<std::string>> expected_locs_list{dn_uuids,
                                                             dn_uuids};
    EXPECT_CALL(*edit_log_sender_,
                LogBatchCreateFile(testing::_,
                                   testing::_,
                                   testing::_,
                                   testing::_,
                                   testing::_,
                                   expected_locs_list,
                                   testing::_,
                                   testing::_,
                                   testing::_))
        .WillOnce(testing::Return(1));
    EXPECT_CALL(*meta_storage_,
                OrderedCommitINodes(testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_,
                                    testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::WithArgs<8>([](const std::vector<Closure*>& dones) {
              dynamic_cast<SynchronizedRpcClosure*>(dones[0])->Run();
            })));
    ns_->AsyncBatchCreate(paths,
                          ufs_keys,
                          client_location,
                          "client-machine-1",
                          ugi,
                          &req,
                          &resp,
                          rpc_info,
                          &done,
                          &ctx);
    done.Await();
  }
}

}  // namespace dancenn
