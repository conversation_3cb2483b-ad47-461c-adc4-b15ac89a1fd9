// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/05/23
// Description

#include "namespace/file_finalizer.h"  // For FileFinalizer.

#include <absl/types/optional.h>                          // For optional.
#include <gmock/gmock.h>                                  // For HasSubStr.
#include <gtest/gtest.h>                                  // For Test, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>              // For BlockProto.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.
#include <proto/generated/dancenn/inode.pb.h>             // For INode.
#include <rocksdb/db.h>  // For Status, WriteBatch, Slice.

#include <memory>  // For unique_ptr, shared_ptr, etc.
#include <set>     // For set.
#include <string>  // For string.
#include <vector>  // For vector.

#include "base/closure.h"          // For RpcClosure, SynchronizedRpcClosure.
#include "base/java_exceptions.h"  // For JavaExceptions.
#include "base/platform.h"         // For WriteBigEndian.
#include "base/status.h"           // For Status, Code.
#include "block_manager/bip_write_manager.h"  // For BIPWriteManager, etc.
#include "block_manager/block.h"       // For kInvalidBlockID, Block, BlockID.
#include "block_manager/block_info.h"  // For BlockUCState.
#include "datanode_manager/datanode_manager.h"           // For DatanodeManager.
#include "test/block_manager/gmock_bip_write_manager.h"  // For GMockBIPWriteManager.
#include "test/block_manager/gmock_block_manager.h"  // For GMockBlockManager.
#include "test/gmock_edit_log_sender.h"              // For GMockEditLogSender.
#include "test/lease/gmock_lease_manager.h"          // For GMockLeaseManager.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage, GMockWriteBatch.
#include "test/namespace/mock_namespace.h"  // For GMockNameSpace.
#include "test/namespace/inode.h"               // For INodeBuilder, etc.
#include "test/proto/generated/cloudfs/hdfs.h"  // For BlockProtoBuilder.
#include "test/proto/generated/dancenn/block_info_proto.h"  // For BlockInfoProtoBuilder.

DECLARE_bool(complete_rpc_check_replica_in_callback);

namespace dancenn {
namespace {

rocksdb::Slice EncodeBlockID(BlockID blk_id) {
  static std::vector<std::string> slice_holders;
  std::string blk_id_str;
  blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  slice_holders.push_back(blk_id_str);
  return slice_holders.back();
}

}  // namespace

class FileFinalizerTest : public testing::Test {
 public:
  void SetUp() override {
    name_space_ = std::make_unique<testing::StrictMock<GMockNameSpace>>();
    FLAGS_complete_rpc_check_replica_in_callback = true;

    block_manager_ = std::make_unique<testing::StrictMock<GMockBlockManager>>();
    bip_write_manager_ =
        std::make_unique<testing::StrictMock<GMockBIPWriteManager>>();
    lease_manager_ = std::make_unique<testing::StrictMock<GMockLeaseManager>>();
    edit_log_sender_ =
        std::make_unique<testing::StrictMock<GMockEditLogSender>>();
    meta_storage_ = std::make_shared<testing::StrictMock<GMockMetaStorage>>();
    file_finalizer_ = std::make_unique<FileFinalizer>();
    file_finalizer_->Start(name_space_.get(),
                           block_manager_.get(),
                           bip_write_manager_.get(),
                           lease_manager_.get(),
                           edit_log_sender_.get(),
                           meta_storage_.get(),
                           nullptr);
    root_inode_ = meta_storage_->CreateRoot();
  }

  void TearDown() override {
    file_finalizer_->Stop();
  }

  Status TestFinalizeFile(const std::string& src,
                          const Block& last_committed_blk,
                          BlockID abandoned_blk_id,
                          INode* inode,
                          RpcClosure* rpc_done) {
    INodeInPath iip;
    iip.MutableInodeUnsafe().Swap(inode);
    DEFER([&]() { iip.MutableInodeUnsafe().Swap(inode); });
    return file_finalizer_->FinalizeFile(src,
                                         last_committed_blk,
                                         abandoned_blk_id,
                                         &iip,
                                         *inode, // just for test
                                         { root_inode_ },
                                         rpc_done);
  }

 protected:
  std::unique_ptr<testing::StrictMock<GMockNameSpace>> name_space_;
  std::unique_ptr<testing::StrictMock<GMockBlockManager>> block_manager_;
  std::unique_ptr<testing::StrictMock<GMockBIPWriteManager>> bip_write_manager_;
  std::unique_ptr<testing::StrictMock<GMockLeaseManager>> lease_manager_;
  std::unique_ptr<testing::StrictMock<GMockEditLogSender>> edit_log_sender_;
  std::shared_ptr<testing::StrictMock<GMockMetaStorage>> meta_storage_;
  std::unique_ptr<FileFinalizer> file_finalizer_;
  INode root_inode_;
};

TEST_F(FileFinalizerTest, FinalizeNotUcFile) {
  INode inode = INodeBuilder().SetId(17123).Build();
  SynchronizedRpcClosure rpc_done;
  Status s =
      TestFinalizeFile("/a/b", Block(), kInvalidBlockID, &inode, &rpc_done);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Cannot finalize file /a/b with no uc field. inode={id: 17123}");
}

TEST_F(FileFinalizerTest, AbandonBlockOfEmptyFile) {
  INode inode = INodeBuilder()
                    .SetId(17123)
                    .SetUc(FileUnderConstructionFeatureBuilder()
                               .SetClientName("client-name-1")
                               .SetClientMachine("client-machine-1")
                               .Build())
                    .Build();
  SynchronizedRpcClosure rpc_done;
  Status s = TestFinalizeFile("/a/b", Block(), 1084217909, &inode, &rpc_done);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Cannot abandon last block of empty file, "
            "abandoned_blk_id=1084217909, "
            "inode={id: 17123 uc { client_name: \"client-name-1\" "
            "client_machine: \"client-machine-1\" }}");
}

TEST_F(FileFinalizerTest, AbandonMismatchedBlock) {
  INode inode = INodeBuilder()
                    .SetId(17123)
                    .AddBlock(BlockProtoBuilder()
                                  .SetBlockId(1084217909)
                                  .SetGenStamp(1001)
                                  .SetNumBytes(1024)
                                  .Build())
                    .SetUc(FileUnderConstructionFeatureBuilder()
                               .SetClientName("client-name-1")
                               .SetClientMachine("client-machine-1")
                               .Build())
                    .Build();
  SynchronizedRpcClosure rpc_done;
  Status s = TestFinalizeFile("/a/b", Block(), 1084217910, &inode, &rpc_done);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_THAT(s.message(),
              testing::HasSubstr(
                  "Cannot abandon mismatched last block, "
                  "abandoned_blk_id=1084217910, inode={id: 17123 blocks"));
}

TEST_F(FileFinalizerTest, CommitBlockOfEmptyFile) {
  INode inode = INodeBuilder()
                    .SetId(17123)
                    .SetUc(FileUnderConstructionFeatureBuilder()
                               .SetClientName("client-name-1")
                               .SetClientMachine("client-machine-1")
                               .Build())
                    .Build();
  SynchronizedRpcClosure rpc_done;
  Status s = TestFinalizeFile("/a/b",
                              Block(1084217909, 1024, 1001),
                              kInvalidBlockID,
                              &inode,
                              &rpc_done);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_THAT(
      s.message(),
      testing::HasSubstr("Cannot commit last block of empty file, "
                         "last_blk={id:1084217909,gs:1001,num_bytes:1024}, "
                         "inode={id: 17123 uc { client_name"));
}

TEST_F(FileFinalizerTest, CommitMismatchedBlock) {
  INode inode = INodeBuilder()
                    .SetId(17123)
                    .AddBlock(BlockProtoBuilder()
                                  .SetBlockId(1084217909)
                                  .SetGenStamp(1001)
                                  .SetNumBytes(1024)
                                  .Build())
                    .SetUc(FileUnderConstructionFeatureBuilder()
                               .SetClientName("client-name-1")
                               .SetClientMachine("client-machine-1")
                               .Build())
                    .Build();
  SynchronizedRpcClosure rpc_done;
  Status s = TestFinalizeFile("/a/b",
                              Block(1084217910, 2048, 1002),
                              kInvalidBlockID,
                              &inode,
                              &rpc_done);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_THAT(
      s.message(),
      testing::HasSubstr("Cannot commit mismatched last block, "
                         "last_blk={id:1084217910,gs:1002,num_bytes:2048}, "
                         "inode={id: 17123 blocks"));
}

}  // namespace dancenn
