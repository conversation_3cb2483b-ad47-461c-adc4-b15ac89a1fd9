#include <absl/strings/substitute.h>

#include <memory>
#include <regex>
#include <sstream>
#include <random>

#include "base/java_exceptions.h"
#include "base/path_util.h"
#include "base/uuid.h"
#include "snapshot/snapshot_manager_impl.h"
#include "test/namespace/nn_cluster_test_base.h"

DECLARE_int32(snapshot_gc_interval_in_sec);
DEFINE_uint32(test_seed, 0, "if not 0, random use specified seed");
DECLARE_uint32(edit_log_assigner_apply_mode);

namespace dancenn {

struct TableCount {
  int inode_table = 0;
  int snapshot_table = 0;
  void Add(int delta1, int delta2) {
    inode_table += delta1;
    snapshot_table += delta2;
  }
  bool operator==(const TableCount& o) const {
    return inode_table == o.inode_table && snapshot_table == o.snapshot_table;
  }
  friend std::ostream& operator<<(std::ostream& out, const TableCount& entry) {
    return out << entry.inode_table << "," << entry.snapshot_table;
  }
};

// Test reading and writing filesystem with snapshot feature
// Test dimensions:
// - fs operation: Create/Delete/Rename, GetListing/GetFileInfo
// - target: snapshot/not_snapshot dir and file
// - snapshot operation: CreateSnapshot/DeleteSnapshot/RenameSnapshot
class ReadWriteWithSnapshotsTest : public NnClusterTest {
 public:
  void SetUp() override {
    NnClusterTest::SetUp();
    ns_->StopSnapshotGcWorker();
    standby_->StopSnapshotGcWorker();

    FLAGS_edit_log_assigner_apply_mode =
        static_cast<int>(ApplyMode::PHYSICAL_CONCUR);
  }

  void TearDown() override {
    NnClusterTest::TearDown();
    LOG(INFO) << "test use seed: " << used_seed_;
  }

  using PrettyDirTree = std::vector<std::string>;

  PrettyDirTree PrintDirTree(const std::string& path,
                             bool print_detail = true,
                             int print_mod_time = 1) {
    PrettyDirTree result;
    // remove possible .snapshot/<version> components for easy comparing
    std::regex re(R"(/\.snapshot/[^/]+)");
    auto print_path = std::regex_replace(path, re, "");

    GetFileInfoResponseProto get_response1;
    DANCENN_EXPECT_OK(ns_->GetFileInfo(path,
                                       NetworkLocationInfo(),
                                       false /*resolve_link*/,
                                       false,
                                       &get_response1,
                                       ugi_));
    bool is_dir = (get_response1.fs().filetype() ==
                   cloudfs::HdfsFileStatusProto_FileType_IS_DIR);

    auto print_file_info = [&](std::stringstream& ss) {
      ss << print_path;
      ss << " " << get_response1.fs().fileid();
      if (print_mod_time == 0) {
        ss << " " << get_response1.fs().modification_time();
      }
      ss << " " << get_response1.fs().length();
    };
    int new_print_mod_time = (print_mod_time == 0 ? 0 : print_mod_time - 1);

    if (is_dir) {
      GetListingRequestProto get_request;
      get_request.set_startafter("");
      get_request.set_needlocation(false);
      GetListingResponseProto get_response;

      DANCENN_EXPECT_OK(ns_->GetListing(
          path, NetworkLocationInfo(), get_request, &get_response, ugi_));
      if (get_response.has_dirlist()) {
        auto child_num = get_response.dirlist().partiallisting_size();
        if (print_detail) {
          std::stringstream ss;
          print_file_info(ss);
          ss << " children:" << child_num;
          for (const auto& x : get_response.dirlist().partiallisting()) {
            ss << " " << x.fileid();
          }
          result.emplace_back(ss.str());
        } else {
          result.emplace_back(absl::Substitute("$0 $1", print_path, child_num));
        }
      } else {
        result.emplace_back(absl::Substitute("$0 not exist", print_path));
      }

      for (const auto& sub : get_response.dirlist().partiallisting()) {
        auto sub_result = PrintDirTree(
            path + kSeparator + sub.path(), print_detail, new_print_mod_time);
        result.insert(result.end(), sub_result.begin(), sub_result.end());
      }
      return result;
    } else {
      if (!print_detail) {
        result.emplace_back(print_path);
        return result;
      }

      std::stringstream ss;
      print_file_info(ss);

      GetBlockLocationsRequestProto get_request2;
      GetBlockLocationsResponseProto get_response2;
      get_request2.set_src(path);
      get_request2.set_offset(0);
      get_request2.set_length(128 * 1024 * 1024);
      cnetpp::base::IPAddress client_ip("***********");  // from AddFile
      DANCENN_EXPECT_OK(ns_->GetBlockLocation(path,
                                              NetworkLocationInfo(client_ip),
                                              get_request2,
                                              &get_response2,
                                              ugi_));

      const auto& blocks = get_response2.locations().blocks();
      ss << " " << blocks.size();
      for (const auto& block : blocks) {
        ss << "," << block.b().blockid() << "|" << block.b().generationstamp();
      }
      result.emplace_back(ss.str());
    }
    return result;
  }

  bool CompareDirTree(const PrettyDirTree& a, const PrettyDirTree& b) {
    std::stringstream ss;
    bool the_same = true;
    if (a.size() != b.size()) {
      the_same = false;
      ss << absl::Substitute(
          "DirTree size diff, a: $0, b: $1\n", a.size(), b.size());
    }
    for (size_t i = 0; i < std::min(a.size(), b.size()); i++) {
      if (a[i] != b[i]) {
        ss << absl::Substitute(
            "DirTree diff at $0, a: $1, b: $2\n", i, a[i], b[i]);
        the_same = false;
        break;
      }
    }
    if (!the_same) {
      ss << "DirTree a:\n" << absl::StrJoin(a, "\n");
      ss << "\nDirTree b:\n" << absl::StrJoin(b, "\n");
      ss << "\nSnapshotData:\n";
      PrintSnapshotData(ss, db_path_);
      LOG(ERROR) << ss.str();
    }
    return the_same;
  }

  void PrintSnapshotData(std::ostream& out, const std::string& db_path) {
    auto PrintINode = [] (const INode& inode) {
      // ignore some fields so that comparing doesn't fail because of them
      INode temp = inode;
      temp.set_atime(0);
      return PBConverter::ToCompactJsonString(temp);
    };

    auto ms = std::make_unique<ReadOnlyMetaStorage>(db_path);

    out << "===Snapshot Root Path===\n";
    const SnapshotManagerImpl& snapshot_manager =
        dynamic_cast<const SnapshotManagerImpl&>(
            ns_->TestOnlyGetSnapshotManager());
    const auto& snapshot_root_map = snapshot_manager.TestOnlySnapshotRootMap();
    for (const auto& elem : snapshot_root_map) {
      out << "Snapshot Root " << elem.second << std::endl;
      INode inode;
      auto sc = ms->GetINode(elem.first, &inode);
      if (sc != StatusCode::kOK) {
        out << "Error: failed to get inode " << elem.first << std::endl;
        continue;
      }
      if (!(inode.has_is_snapshottable() && inode.is_snapshottable())) {
        out << "Error: path is not snapshottable in inode" << std::endl;
      }
      for (const auto& snapshot : inode.snapshots()) {
        if (snapshot.has_deleted() && snapshot.deleted()) {
          out << "Snapshot " << snapshot.name() << " deleted " << std::endl;
        }
        out << absl::Substitute("Snapshot $0, id=$1, create_txid=$2\n",
                                snapshot.name(),
                                snapshot.snapshot_id(),
                                snapshot.create_txid());
      }
    }

    auto snapshot_holder = ms->GetSnapshot();
    {
      // iterator scope inside snapshot scope
      auto snapshot = snapshot_holder->snapshot();
      {
        out << "===SnapshotInode table===\n";
        auto iter_holder = ms->GetIterator(snapshot, kSnapshotInodeCFIndex);
        auto iter = iter_holder->iter();
        for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
          uint64_t p_id, id, last_update_txid, snapshot_root_id;
          std::string name;
          const auto key_str = StringPiece(iter->key().data(), iter->key().size());
          ms->DecodeSnapshotINodeKey(
              key_str, &p_id, &name, &id, &last_update_txid, &snapshot_root_id);
          out << absl::Substitute("Decoded Key: $0/$1/$2/$3/$4\n",
                                  p_id,
                                  name,
                                  id,
                                  last_update_txid,
                                  snapshot_root_id);
          INode inode;
          if (!inode.ParseFromArray(iter->value().data(), iter->value().size())) {
            out << "Cannot deserialize element: " << iter->key().ToString();
          } else {
            out << "Value: " << PrintINode(inode);
          }
          out << std::endl;
        }
        if (!iter->status().ok()) {
          out << "Error rocksdb::Iterator status: "
              << iter->status().ToString()
              << std::endl;
        }
      }

      {
        out << "===SnapshotInodeIndex table===\n";
        auto iter_holder = ms->GetIterator(snapshot, kSnapshotINodeIndexCFIndex);
        auto iter = iter_holder->iter();
        for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
          uint64_t id, last_update_txid;
          const auto key_str = StringPiece(iter->key().data(), iter->key().size());
          ms->DecodeSnapshotINodeIndexKey(key_str, &id, &last_update_txid);
          auto p_id = MetaStorage::DecodeInteger<INodeID>(iter->value());
          out << absl::Substitute(
              "Decoded Key: $0/$1, Value: $2\n", id, last_update_txid, p_id);
        }
        if (!iter->status().ok()) {
          out << "Error rocksdb::Iterator status: "
              << iter->status().ToString()
              << std::endl;
        }
      }

      {
        out << "===INode table===\n";
        auto iter_holder = ms->GetIterator(snapshot, kINodeDefaultCFIndex);
        auto iter = iter_holder->iter();
        for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
          uint64_t p_id, id;
          std::string name;
          const auto key_str = StringPiece(iter->key().data(), iter->key().size());
          ms->DecodeStoreKey(key_str, &p_id, &name, &id);
          out << absl::Substitute("Decoded Key: $0/$1/$2\n", p_id, name, id);
          INode inode;
          if (!inode.ParseFromArray(iter->value().data(), iter->value().size())) {
            out << "Cannot deserialize element: " << iter->key().ToString();
          } else {
            out << "Value: " << PrintINode(inode);
          }
          out << std::endl;
        }
        if (!iter->status().ok()) {
          out << "Error rocksdb::Iterator status: "
              << iter->status().ToString()
              << std::endl;
        }
      }
    }
  }

  DBStat GetSnapshotTableStat() {
    WaitSynced();
    auto db_comptr = std::make_unique<DBComparator>(db_path_, standby_path_);
    return db_comptr->GetDBStat(
        { kINodeDefaultCFIndex, kSnapshotInodeCFIndex }, false);
  }

  bool VerifySnapshotTableStat(const DBStat& expected) {
    DBStat actual = GetSnapshotTableStat();
    if (!(expected == actual)) {
      std::string msg = "failed to verify snapshot-related stats, expected:";
      for (auto p : expected) {
        msg += absl::StrFormat(" { %d, %s }", p.first, p.second.ToString());
      }
      msg += ", actual:";
      for (auto p : actual) {
        msg += absl::StrFormat(" { %d, %s }", p.first, p.second.ToString());
      }
      LOG(ERROR) << msg;
      return false;
    }
    return true;
  }

  bool WaitStandAppliedAndCompare() {
    WaitSynced();
    return CheckDBConsistency();
  }

  std::pair<int64_t, int64_t> ManualSnapshotGc(
      bool incremental_gc,
      const std::function<bool(int64_t)>& should_stop = [](int64_t) {
        return false;
      }) {
    auto result = ns_->ManualSnapshotGc(incremental_gc, should_stop);
    standby_->ManualSnapshotGc(incremental_gc, should_stop);
    return result;
  }

  unsigned used_seed_;  // print for debug
};

TEST_F(ReadWriteWithSnapshotsTest, CreateAndRead) {
  std::string test_root = "/test";
  std::string test_dir1 = "/test/d1";
  std::string test_dir_empty = "/test/dir_empty";
  std::string test_dir_mkdir_p = "/test/mkdir/p1/p2";
  std::string test_file1 = "/test/f1";
  std::string test_file2 = "/test/d1/f2";
  std::string test_file3 = "/test/d1/f3";
  auto p = MakePermission();
  // Snapshots' data views:
  PrettyDirTree s1 = {"/test 0"};
  PrettyDirTree s2 = {"/test 2", "/test/d1 1", "/test/d1/f2", "/test/f1"};
  PrettyDirTree s5 = {"/test 4",
                      "/test/d1 2",
                      "/test/d1/f2",
                      "/test/d1/f3",
                      "/test/dir_empty 0",
                      "/test/f1",
                      "/test/mkdir 1",
                      "/test/mkdir/p1 1",
                      "/test/mkdir/p1/p2 0"};
  PrettyDirTree not_exist = {"/test not exist"};
  PrettyDirTree s1_gt, s2_gt, s5_gt;

  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
  ASSERT_TRUE(WaitStandAppliedAndCompare());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  {
    // create snapshot s1
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test", false)));

    s1_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    // list snapshot path that does not exist
    auto ls_not_exist = PrintDirTree("/test/.snapshot/s2");
    ASSERT_TRUE(CompareDirTree(not_exist, ls_not_exist));

    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir1, p, true /*create_parent*/));
    AddFile(test_file1, 101, 3, true, &add_response, &create_response);
    AddFile(test_file2, 102, 2, true, &add_response, &create_response);
  }
  {
    // create snapshot s2, s3
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s3"));
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test", false)));

    s2_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s3_actual));

    // test rename snapshot
    DANCENN_ASSERT_OK(ns_->RenameSnapshot(test_root, "s3", "s4"));
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(not_exist, s3_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s4_actual));

    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_empty, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_mkdir_p, p, true /*create_parent*/));
    AddFile(test_file3, 102, 3, true, &add_response, &create_response);
    uint32_t flag = CreateFlag::SetOverwrite(0);
    AddFile(
        test_file2, 1020, 3, true, &add_response, &create_response, true, flag);
  }
  {
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s5"));
    ASSERT_TRUE(CompareDirTree(s5, PrintDirTree("/test", false)));

    s5_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));
  }

  ASSERT_TRUE(WaitStandAppliedAndCompare());

  {
    // ls .snapshot and check result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_EQ(4, get_response.dirlist().partiallisting_size());
    ASSERT_EQ("s1", get_response.dirlist().partiallisting(0).path());
    ASSERT_EQ("s2", get_response.dirlist().partiallisting(1).path());
    ASSERT_EQ("s4", get_response.dirlist().partiallisting(2).path());
    ASSERT_EQ("s5", get_response.dirlist().partiallisting(3).path());

    // delete snapshot s1
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    auto current_dir_tree = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));

    // delete snapshot s5
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s5"));
    current_dir_tree = PrintDirTree("/test");
    s2_actual = PrintDirTree("/test/.snapshot/s2");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(not_exist, s5_actual));

    // delete snapshot s2
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    current_dir_tree = PrintDirTree("/test");
    s2_actual = PrintDirTree("/test/.snapshot/s2");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s2_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s4_actual));

    // delete snapshot s4
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s4"));
    current_dir_tree = PrintDirTree("/test");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s4_actual));

    // ls .snapshot, get empty result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_TRUE(get_response.has_dirlist());
    ASSERT_EQ(0, get_response.dirlist().partiallisting_size());
  }

  ASSERT_TRUE(WaitStandAppliedAndCompare());

  {
    // test creating path with .snapshot component is not allowed
    DANCENN_ASSERT_STATUS(ns_->MkDirs(test_root + "/.snapshot", p, true),
                          JavaExceptions::kIOException);
    auto create_request = MakeCreateRequest();
    create_request.set_createparent(true);
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    auto file_name = test_root + "/.snapshot";
    CreateResponseProto* create_resp_proto;
    DANCENN_ASSERT_STATUS(
        ns_->CreateFile(file_name, p, create_request, create_resp_proto),
        JavaExceptions::kIOException);
    file_name = test_root + "/.snapshot/abc";
    DANCENN_ASSERT_STATUS(
        ns_->CreateFile(file_name, p, create_request, create_resp_proto),
        JavaExceptions::kIOException);
  }
}

TEST_F(ReadWriteWithSnapshotsTest, DeleteAndRead) {
  std::string test_root = "/test";
  std::string test_dir1 = "/test/d1";
  std::string test_dir_empty = "/test/dir_empty";
  std::string test_dir_mkdir_p = "/test/mkdir/p1/p2";
  std::string test_file1 = "/test/f1";
  std::string test_file2 = "/test/d1/f2";
  std::string test_file3 = "/test/d1/f3";
  std::string test_file4 = "/test/d2/d3/f4";
  auto p = MakePermission();
  // Snapshots' data views:
  PrettyDirTree s1 = {"/test 4",
                      "/test/d1 2",
                      "/test/d1/f2",
                      "/test/d1/f3",
                      "/test/dir_empty 0",
                      "/test/f1",
                      "/test/mkdir 1",
                      "/test/mkdir/p1 1",
                      "/test/mkdir/p1/p2 0"};
  PrettyDirTree s2 = {"/test 3",
                      "/test/d1 1",
                      "/test/d1/f2",
                      "/test/d2 1",
                      "/test/d2/d3 1",
                      "/test/d2/d3/f4",
                      "/test/f1"};
  PrettyDirTree s3 = s1;
  PrettyDirTree s4 = {"/test 3",
                      "/test/d1 0",
                      "/test/dir_empty 0",
                      "/test/mkdir 1",
                      "/test/mkdir/p1 1",
                      "/test/mkdir/p1/p2 0"};
  PrettyDirTree s5 = {"/test 0"};
  PrettyDirTree not_exist = {"/test not exist"};
  PrettyDirTree s1_gt, s2_gt, s3_gt, s4_gt, s5_gt;

  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
  ASSERT_TRUE(WaitStandAppliedAndCompare());
  DBStat expected_stat = GetSnapshotTableStat();

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  {
    // create files and directories
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir1, p, true /*create_parent*/));
    AddFile(test_file1, 101, 3, true, &add_response, &create_response);
    AddFile(test_file2, 102, 2, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_empty, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_mkdir_p, p, true /*create_parent*/));
    AddFile(test_file3, 102, 3, true, &add_response, &create_response);
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }

  {
    // create snapshot s1
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test", false)));

    s1_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));

    // delete single file, non-empty directory and empty directory
    DANCENN_ASSERT_OK(ns_->Delete(test_dir_empty, true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete("/test/mkdir", true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete(test_file3, true /*recursive*/));
    // create test_file4 with create_parent
    AddFile(test_file4, 102, 3, true, &add_response, &create_response, true);
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }
  {
    // create snapshot s2
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test", false)));

    s2_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));

    // recreate
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_empty, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_mkdir_p, p, true /*create_parent*/));
    AddFile(test_file3, 102, 3, true, &add_response, &create_response);
    // create a file and delete it, it's not in any snapshot
    AddFile("/test/temp", 100, 3, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->Delete("/test/temp", true /*recursive*/));
    // delete a directory created by create_parent
    DANCENN_ASSERT_OK(ns_->Delete("/test/d2", true /*recursive*/));
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }
  {
    // create snapshot s3
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s3"));
    ASSERT_TRUE(CompareDirTree(s3, PrintDirTree("/test", false)));

    s3_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));

    // delete all files
    DANCENN_ASSERT_OK(ns_->Delete(test_file1, true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete(test_file2, true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete(test_file3, true /*recursive*/));
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }
  {
    // create snapshot s4
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s4"));
    ASSERT_TRUE(CompareDirTree(s4, PrintDirTree("/test", false)));

    s4_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));

    // delete all directories
    DANCENN_ASSERT_OK(ns_->Delete(test_dir1, true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete(test_dir_empty, true /*recursive*/));
    DANCENN_ASSERT_OK(ns_->Delete("/test/mkdir", true /*recursive*/));
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }
  {
    // create snapshot s5
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s5"));
    ASSERT_TRUE(CompareDirTree(s5, PrintDirTree("/test", false)));

    s5_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));
    ManualSnapshotGc(true /*is_incremental_gc*/);
  }

  ASSERT_TRUE(WaitStandAppliedAndCompare());

  {
    // ls .snapshot and check result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_EQ(5, get_response.dirlist().partiallisting_size());
    ASSERT_EQ("s1", get_response.dirlist().partiallisting(0).path());
    ASSERT_EQ("s2", get_response.dirlist().partiallisting(1).path());
    ASSERT_EQ("s3", get_response.dirlist().partiallisting(2).path());
    ASSERT_EQ("s4", get_response.dirlist().partiallisting(3).path());
    ASSERT_EQ("s5", get_response.dirlist().partiallisting(4).path());

    // test can not delete snapshot root path with snapshot exists
    DANCENN_ASSERT_STATUS(ns_->Delete(test_root, true /*recursive*/),
                          JavaExceptions::kSnapshotException);

    // delete snapshot s2
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    auto current_dir_tree = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(not_exist, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));

    // delete snapshot s1
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    current_dir_tree = PrintDirTree("/test");
    s1_actual = PrintDirTree("/test/.snapshot/s1");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s1_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));
    ManualSnapshotGc(true /*is_incremental_gc*/);

    // delete snapshot s5
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s5"));
    current_dir_tree = PrintDirTree("/test");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(not_exist, s5_actual));
    ASSERT_TRUE(WaitStandAppliedAndCompare());

    // delete snapshot s3
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s3"));
    current_dir_tree = PrintDirTree("/test");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));

    // delete snapshot s4
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s4"));
    current_dir_tree = PrintDirTree("/test");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s4_actual));

    // ls .snapshot, get empty result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_TRUE(get_response.has_dirlist());
    ASSERT_EQ(0, get_response.dirlist().partiallisting_size());

    // disallow snapshot and delete snapshot root path
    // wait snapshots gc, verify num_inodes
    DANCENN_ASSERT_STATUS(ns_->Delete(test_root, true /*recursive*/),
                          JavaExceptions::kSnapshotException);
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_root));
    ManualSnapshotGc(true /*is_incremental_gc*/);
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
    DANCENN_ASSERT_OK(ns_->Delete(test_root, true /*recursive*/));
  }
}

TEST_F(ReadWriteWithSnapshotsTest, DeleteRenameFunctionality) {
  std::string test_root = "/test";
  std::string test_dir1 = "/test/d1";
  std::string test_file1 = "/test/d1/f1";
  std::string test_file2 = "/test/d1/f2";
  std::string test_file3 = "/test/d1/f3";
  std::string test_file4 = "/test/d1/f4";
  std::string test_file5 = "/test/d1/f5";
  std::string test_file6 = "/test/d1/f6";
  std::string test_dir2 = "/test/d1/d2";
  auto p = MakePermission();

  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
  ASSERT_TRUE(WaitStandAppliedAndCompare());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  {
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir1, p, true /*create_parent*/));
    AddFile(test_file1, 101, 3, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "b1"));

    AddFile(test_file2, 102, 2, true, &add_response, &create_response);
    AddFile(test_file3, 103, 3, true, &add_response, &create_response);
    AddFile(test_file4, 103, 3, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir2, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "a1"));

    AddFile(test_file5, 103, 3, true, &add_response, &create_response);
  }

  DBStat expected_stat = GetSnapshotTableStat();
  // TODO add rename cases
  {
    // delete path created before the latest snapshot
    DANCENN_ASSERT_OK(ns_->Delete(test_file1, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry += -1;
    expected_stat[kSnapshotInodeCFIndex].num_entry += 1;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  {
    // delete path which was created before the latest snapshot and updated
    // after that
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir2 + "/x", p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->Delete(test_dir2, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry += 0;
    expected_stat[kSnapshotInodeCFIndex].num_entry += 2;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  {
    // delete path created after the latest snapshot
    DANCENN_ASSERT_OK(ns_->Delete(test_file5, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry += -1;
    expected_stat[kSnapshotInodeCFIndex].num_entry += 0;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  {
    // delete path created before the latest snapshot, then we remove all
    // snapshots
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "b1"));
    DANCENN_ASSERT_OK(ns_->Delete(test_file2, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry += -1;
    expected_stat[kSnapshotInodeCFIndex].num_entry += 1;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "a1"));
    DANCENN_ASSERT_OK(ns_->Delete(test_file3, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry += -1;
    expected_stat[kSnapshotInodeCFIndex].num_entry += 0;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  {
    // delete path which was created before the latest snapshot and no longer
    // under snapshottable directory
    ManualSnapshotGc(true);
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_root));
    DANCENN_ASSERT_OK(ns_->Delete(test_file4, true /*recursive*/));
    expected_stat[kINodeDefaultCFIndex].num_entry = 3; // /test, /test/d1
    expected_stat[kSnapshotInodeCFIndex].num_entry = 0;
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  {
    // delete path created under directory which is not snapshottable anymore
    AddFile(test_file6, 103, 3, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->Delete(test_file6, true /*recursive*/));
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
  }
  ASSERT_TRUE(WaitStandAppliedAndCompare());
}

// TODO add rename2 cases
TEST_F(ReadWriteWithSnapshotsTest, RenameAndRead) {
  std::string test_root = "/test";
  std::string test_dir1 = "/test/d1";
  std::string test_dir_empty = "/test/dir_empty";
  std::string test_dir_mkdir_p = "/test/mkdir/p1/p2";
  std::string test_file1 = "/test/f1";
  std::string test_file2 = "/test/d1/f2";
  std::string test_file3 = "/test/d1/f3";
  std::string outside_snapshot = "/outside";
  std::string outside_dir1 = "/outside/d1";
  std::string outside_file1 = "/outside/f1";
  auto p = MakePermission();
  // Snapshots' data views:
  PrettyDirTree s1 = {"/test 4",
                      "/test/d1 2",
                      "/test/d1/f2",
                      "/test/d1/f3",
                      "/test/dir_empty 0",
                      "/test/f1",
                      "/test/mkdir 1",
                      "/test/mkdir/p1 1",
                      "/test/mkdir/p1/p2 0"};
  PrettyDirTree s2 = {"/test 6",
                      "/test/1f1",
                      "/test/dd1 2",
                      "/test/dd1/f3",
                      "/test/dd1/ff2",
                      "/test/dir_empty0 0",
                      "/test/mkdir 1",
                      "/test/mkdir/1p1 1",
                      "/test/mkdir/1p1/p2 0",
                      "/test/outside_dir1 0",
                      "/test/outside_f1"};
  PrettyDirTree s3 = s1;
  PrettyDirTree s4 = {"/test 4",
                      "/test/d1 2",
                      "/test/d1/f1",
                      "/test/d1/f2",
                      "/test/f3",
                      "/test/mkdir 2",
                      "/test/mkdir/dir_empty 0",
                      "/test/mkdir/p1 0",
                      "/test/p2 0"};
  PrettyDirTree s5 = {"/test 2", "/test/d1 1", "/test/d1/f1", "/test/p2 0"};
  PrettyDirTree not_exist = {"/test not exist"};
  PrettyDirTree s1_gt, s2_gt, s3_gt, s4_gt, s5_gt;

  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->MkDirs(outside_snapshot, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
  ASSERT_TRUE(WaitStandAppliedAndCompare());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  {
    // create files and directories
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir1, p, true /*create_parent*/));
    AddFile(test_file1, 101, 3, true, &add_response, &create_response);
    AddFile(test_file2, 102, 2, true, &add_response, &create_response);
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_empty, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->MkDirs(test_dir_mkdir_p, p, true /*create_parent*/));
    AddFile(test_file3, 102, 3, true, &add_response, &create_response);

    DANCENN_ASSERT_OK(ns_->MkDirs(outside_dir1, p, true /*create_parent*/));
    AddFile(outside_file1, 201, 3, true, &add_response, &create_response);
  }
  DBStat expected_stat = GetSnapshotTableStat();
  ASSERT_EQ(expected_stat[kSnapshotInodeCFIndex].num_entry, 0);

  {
    // create snapshot s1
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test", false)));

    s1_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));

    // just change name of file and directory
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/f2", "/test/d1/ff2"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1", "/test/dd1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/dir_empty", "/test/dir_empty0"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/f1", "/test/1f1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/mkdir/p1", "/test/mkdir/1p1"));

    // rename file/directory from outside to snapshot root
    DANCENN_ASSERT_OK(ns_->RenameTo(outside_file1, "/test/outside_f1"));
    DANCENN_ASSERT_OK(ns_->RenameTo(outside_dir1, "/test/outside_dir1"));
  }
  {
    // create snapshot s2
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test", false)));

    s2_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));

    // rename back
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/dd1", "/test/d1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/ff2", "/test/d1/f2"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/dir_empty0", "/test/dir_empty"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/1f1", "/test/f1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/mkdir/1p1", "/test/mkdir/p1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/outside_f1", outside_file1));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/outside_dir1", outside_dir1));
    // delete snapshot(s2) referenced dir, snapshot view stay normal
    DANCENN_ASSERT_OK(ns_->Delete(outside_dir1, true));
    ManualSnapshotGc(false);
    expected_stat[kINodeDefaultCFIndex].num_entry -= 1;
  }
  {
    // create snapshot s3
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s3"));
    ASSERT_TRUE(CompareDirTree(s3, PrintDirTree("/test", false)));

    s3_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));

    // rename to directory at different depth
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/f2", "/test/f3"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/f3", "/test/d1/f2"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/f1", "/test/d1/f1"));
    DANCENN_ASSERT_OK(
        ns_->RenameTo("/test/dir_empty", "/test/mkdir/dir_empty"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/mkdir/p1/p2", "/test/p2"));
  }
  {
    // create snapshot s4
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s4"));
    ASSERT_TRUE(CompareDirTree(s4, PrintDirTree("/test", false)));

    s4_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));

    // rename to outside
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/f2", "/outside/f2"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/f3", "/outside/f3"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/mkdir", "/outside/mkdir"));
  }
  {
    // create snapshot s5
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s5"));
    ASSERT_TRUE(CompareDirTree(s5, PrintDirTree("/test", false)));

    s5_gt = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(s2_gt, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));
  }

  ASSERT_TRUE(WaitStandAppliedAndCompare());

  {
    // ls .snapshot and check result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_EQ(5, get_response.dirlist().partiallisting_size());
    ASSERT_EQ("s1", get_response.dirlist().partiallisting(0).path());
    ASSERT_EQ("s2", get_response.dirlist().partiallisting(1).path());
    ASSERT_EQ("s3", get_response.dirlist().partiallisting(2).path());
    ASSERT_EQ("s4", get_response.dirlist().partiallisting(3).path());
    ASSERT_EQ("s5", get_response.dirlist().partiallisting(4).path());

    // test can not rename snapshot root path with snapshot exists
    DANCENN_ASSERT_STATUS(ns_->RenameTo(test_root, "/xx"),
                          JavaExceptions::kSnapshotException);

    // delete snapshot s2
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    auto current_dir_tree = PrintDirTree("/test");
    auto s1_actual = PrintDirTree("/test/.snapshot/s1");
    auto s2_actual = PrintDirTree("/test/.snapshot/s2");
    auto s3_actual = PrintDirTree("/test/.snapshot/s3");
    auto s4_actual = PrintDirTree("/test/.snapshot/s4");
    auto s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(s1_gt, s1_actual));
    ASSERT_TRUE(CompareDirTree(not_exist, s2_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));

    // delete snapshot s1
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    current_dir_tree = PrintDirTree("/test");
    s1_actual = PrintDirTree("/test/.snapshot/s1");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s1_actual));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(s5_gt, s5_actual));

    // delete snapshot s5
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s5"));
    current_dir_tree = PrintDirTree("/test");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    s5_actual = PrintDirTree("/test/.snapshot/s5");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(s3_gt, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));
    ASSERT_TRUE(CompareDirTree(not_exist, s5_actual));

    // delete snapshot s3
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s3"));
    current_dir_tree = PrintDirTree("/test");
    s3_actual = PrintDirTree("/test/.snapshot/s3");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s3_actual));
    ASSERT_TRUE(CompareDirTree(s4_gt, s4_actual));

    // delete snapshot s4
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s4"));
    current_dir_tree = PrintDirTree("/test");
    s4_actual = PrintDirTree("/test/.snapshot/s4");
    ASSERT_TRUE(CompareDirTree(s5_gt, current_dir_tree));
    ASSERT_TRUE(CompareDirTree(not_exist, s4_actual));

    ASSERT_TRUE(WaitStandAppliedAndCompare());

    // ls .snapshot, get empty result
    DANCENN_ASSERT_OK(ns_->GetListing("/test/.snapshot",
                                      NetworkLocationInfo(),
                                      get_request,
                                      &get_response,
                                      ugi_));
    ASSERT_TRUE(get_response.has_dirlist());
    ASSERT_EQ(0, get_response.dirlist().partiallisting_size());

    // disallow snapshot and delete snapshot root path
    ManualSnapshotGc(true);
    ASSERT_TRUE(VerifySnapshotTableStat(expected_stat));
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_root));
    DANCENN_ASSERT_OK(ns_->RenameTo(test_root, "/xx"));
  }
}

TEST_F(ReadWriteWithSnapshotsTest, RecycleBin) {
  // TODO
}

TEST_F(ReadWriteWithSnapshotsTest, SnapshotGc) {
  unsigned seed =
      FLAGS_test_seed == 0
          ? std::chrono::system_clock::now().time_since_epoch().count()
          : FLAGS_test_seed;
  LOG(INFO) << "[SnapshotGc] seed for debug reproduce: " << seed;
  used_seed_ = seed;

  std::default_random_engine gen(seed);
  std::uniform_int_distribution<int> bool_dist(0, 1);
  int64_t snapshot_inodes_deleted;
  int64_t inodes_deleted;
  auto ManualGc = [&]() {
    bool incremental_gc = bool_dist(gen);
    LOG(INFO) << "[SnapshotGc] incremental_gc: " << incremental_gc;
    auto res = this->ManualSnapshotGc(incremental_gc);
    snapshot_inodes_deleted = res.first;
    inodes_deleted = res.second;
  };

  std::string test_root1 = "/test1";
  std::string test_root2 = "/test2";
  std::string test_root3 = "/test3";
  std::string test_outside = "/test_outside";
  std::string test_file1 = "/d1/f1";
  std::string test_dir2 = "/d2/d1/d2";
  std::string test_file3 = "/d3/d2/d3/f1";
  std::string test_file4 = "/test1/d1/mv_to_out";
  std::string test_file5 = "/test2/d1/mv_to_root1";
  std::vector<std::string> test_roots = {test_root1, test_root2, test_root3};
  auto p = MakePermission();

  for (const auto& path : test_roots) {
    DANCENN_ASSERT_OK(ns_->MkDirs(path, p, true /*create_parent*/));
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(path));
  }
  ASSERT_TRUE(WaitStandAppliedAndCompare());

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  GetListingRequestProto get_request;
  get_request.set_startafter("");
  get_request.set_needlocation(false);
  GetListingResponseProto get_response;
  for (const auto& path : test_roots) {
    AddFile(
        path + test_file1, 101, 1, true, &add_response, &create_response, true);
    DANCENN_ASSERT_OK(ns_->MkDirs(path + test_dir2, p, true /*create_parent*/));
    AddFile(
        path + test_file3, 103, 3, true, &add_response, &create_response, true);
  }
  DANCENN_ASSERT_OK(ns_->MkDirs(test_outside, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root1, "a1"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root2, "b1"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root3, "c1"));
  ManualGc();
  DBStat actual_stat = GetSnapshotTableStat();
  ASSERT_EQ(actual_stat[kSnapshotInodeCFIndex].num_entry, 0);

  // delete file, delete dir with no children, delete dir with children
  // pending delete: test_file1, test_dir2, xxx/d3/d2
  for (const auto& path : test_roots) {
    DANCENN_ASSERT_OK(ns_->Delete(path + test_file1, true));
    DANCENN_ASSERT_OK(ns_->Delete(path + test_dir2, true));
    DANCENN_ASSERT_OK(ns_->Delete(path + "/d3/d2", true));
  }
  // test_file5 is referred by snapshot b2
  AddFile(test_file5, 103, 3, true, &add_response, &create_response, true);
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root1, "a2"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root2, "b2"));

  auto root1_before_gc = PrintDirTree(test_root1);
  ManualGc();
  ASSERT_EQ(snapshot_inodes_deleted, 0);
  ASSERT_EQ(inodes_deleted, 0);
  auto root1_after_gc = PrintDirTree(test_root1);
  ASSERT_TRUE(CompareDirTree(root1_before_gc, root1_after_gc));

  // replay creation
  for (const auto& path : test_roots) {
    AddFile(
        path + test_file1, 101, 1, true, &add_response, &create_response, true);
    DANCENN_ASSERT_OK(ns_->MkDirs(path + test_dir2, p, true /*create_parent*/));
    AddFile(
        path + test_file3, 103, 3, true, &add_response, &create_response, true);
  }
  // test_file4 is referred by snapshot a3
  AddFile(test_file4, 103, 3, true, &add_response, &create_response, true);
  // test_file5 is also referred by snapshot b2 a3
  DANCENN_ASSERT_OK(ns_->RenameTo(test_file5, test_root1 + "/moved_to_root1"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root1, "a3"));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root2, "b3"));

  // rename and delete generate two snapshot inodes, we test two turns gc here
  DANCENN_ASSERT_OK(ns_->RenameTo(test_file4, test_outside + "/moved_to_out"));
  DANCENN_ASSERT_OK(ns_->Delete(test_outside + "/moved_to_out", true));
  DANCENN_ASSERT_OK(ns_->Delete(test_root1 + "/moved_to_root1", true));

  root1_before_gc = PrintDirTree(test_root1);
  ManualGc();
  ASSERT_EQ(snapshot_inodes_deleted, 0);
  ASSERT_EQ(inodes_deleted, 0);
  root1_after_gc = PrintDirTree(test_root1);
  ASSERT_TRUE(CompareDirTree(root1_before_gc, root1_after_gc));

  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root3, "c1"));
  ManualGc();
  ASSERT_EQ(inodes_deleted, 5);  // d1/f1, d2/d1/d2, d2/d3/f1, d2/d3, d2 under /test3/d3

  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root1, "a3"));
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root2, "b2"));
  ManualGc();
  ASSERT_EQ(inodes_deleted, 1);  // moved_to_out

  // xxx/d3/d2/d3 xxx/d3/d2/d3/f1 deleted, moved_to_root1 deleted
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root1, "a1"));
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root2, "b1"));
  ManualGc();
  int inodes_deleted2 = inodes_deleted;
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root1, "a2"));
  DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root2, "b3"));
  ManualGc();
  inodes_deleted2 += inodes_deleted;
  ASSERT_EQ(inodes_deleted2, 2 * 5 + 1);
  actual_stat = GetSnapshotTableStat();
  ASSERT_EQ(actual_stat[kSnapshotInodeCFIndex].num_entry, 0);

  ASSERT_TRUE(WaitStandAppliedAndCompare());

  {
    // test pending-delete when inode/child inode is referred
    AddFile("/test1/1", 103, 3, true, &add_response, &create_response, true);
    AddFile("/test1/2", 103, 3, true, &add_response, &create_response, true);
    DANCENN_ASSERT_OK(ns_->MkDirs("/test1/3/4", p, true));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test1/5", p, true));
    AddFile("/test1/6", 103, 3, true, &add_response, &create_response, true);
    DANCENN_ASSERT_OK(ns_->MkDirs("/test1/7", p, true));

    root1_before_gc = PrintDirTree(test_root1);
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root1, "a1"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test_outside/dir1", p, true));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test_outside/dir2/dir3", p, true));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/1", "/test_outside/dir1/1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/2", "/test_outside/dir2/dir3/1"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/3", "/test_outside/dir1/3"));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/5", "/test_outside/dir2/dir3/5"));
    DANCENN_ASSERT_OK(ns_->Delete("/test_outside/dir1", true));
    DANCENN_ASSERT_OK(ns_->Delete("/test_outside/dir2", true));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/6", "/test_outside/6"));
    DANCENN_ASSERT_OK(ns_->Delete("/test_outside/6", true));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test1/7", "/test_outside/7"));
    DANCENN_ASSERT_OK(ns_->Delete("/test_outside/7", true));
    if (bool_dist(gen)) {
      ns_->LoadBlockMap();  // test delete logic in LoadBlockMap
    }
    ManualGc();
    root1_after_gc = PrintDirTree("/test1/.snapshot/a1");
    ASSERT_TRUE(CompareDirTree(root1_before_gc, root1_after_gc));
    ASSERT_TRUE(WaitStandAppliedAndCompare());
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root1, "a1"));
    ManualGc();
  }

  {
    // test load blocks of snapshot inode subtree
    AddFile("/test1/1", 113, 3, true, &add_response, &create_response, true);
    AddFile("/test1/2/3", 11, 3, true, &add_response, &create_response, true);

    root1_before_gc = PrintDirTree(test_root1);
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root1, "a1"));
    // f1 is snapshot inode
    DANCENN_ASSERT_OK(ns_->Delete("/test1/1", true));
    // f2 is in snapshot inode subtree
    DANCENN_ASSERT_OK(ns_->Delete("/test1/2/3", true));

    ns_->LoadBlockMap();
    root1_after_gc = PrintDirTree("/test1/.snapshot/a1");
    ASSERT_TRUE(CompareDirTree(root1_before_gc, root1_after_gc));
    ASSERT_TRUE(WaitStandAppliedAndCompare());
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root1, "a1"));
    ManualGc();
  }

  for (const auto& path : test_roots) {
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(path));
    DANCENN_ASSERT_OK(ns_->Delete(path, true));
  }
  DANCENN_ASSERT_OK(ns_->Delete(test_outside, true));
  ManualGc();
  actual_stat = GetSnapshotTableStat();
  ASSERT_EQ(actual_stat[kINodeDefaultCFIndex].num_entry, 1);
  ASSERT_EQ(actual_stat[kSnapshotInodeCFIndex].num_entry, 0);
}

TEST_F(ReadWriteWithSnapshotsTest, SnapshotGcFunction) {
  INode inode1, inode2, inode3;
  NameSpace::SnapshotRootIdToGcInfo gc_info;
  inode1.set_id(1);
  inode1.set_is_snapshottable(true);
  inode2.set_id(2);
  inode2.set_is_snapshottable(true);
  inode3.set_id(3);
  inode3.set_is_snapshottable(true);
  auto& snapshots1 = *inode1.mutable_snapshots();
  auto& snapshots2 = *inode2.mutable_snapshots();
  auto& snapshots3 = *inode3.mutable_snapshots();
  auto TestGatherMarkDeletedSnapshots = [&](bool incremantal_gc) {
    std::vector<std::pair<INode, std::string>> snapshot_root_inodes = {
        {inode1, "/1"}, {inode2, "/2"}, {inode3, "/3"}};
    ns_->GatherMarkDeletedSnapshots(
        snapshot_root_inodes, &gc_info, incremantal_gc);
  };
  auto CompareMergeRanges = [](const NameSpace::SnapshotMergeRanges& l,
                               const NameSpace::SnapshotMergeRanges& r) {
    if (l.size() != r.size()) {
      LOG(INFO) << "MergeRanges size not match " << l.size() << " " << r.size();
      return false;
    }
    for (int i = 0; i < l.size(); i++) {
      if (l[i].first != r[i].first || l[i].second != r[i].second) {
        LOG(INFO) << absl::Substitute(
            "MergeRanges not match at $0: ($1, $2) vs ($3, $4)",
            i,
            l[i].first,
            l[i].second,
            r[i].first,
            r[i].second);
        return false;
      }
    }
    return true;
  };

  {
    TestGatherMarkDeletedSnapshots(true);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[1].merge_ranges.size());
    TestGatherMarkDeletedSnapshots(false);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[1].merge_ranges.size());
  }

  {
    // 1: s1 s3 s5, 2: s2, 3: s4 s6 s7
    snapshots1.Add()->set_create_txid(10);
    snapshots2.Add()->set_create_txid(20);
    snapshots1.Add()->set_create_txid(30);
    snapshots3.Add()->set_create_txid(40);
    snapshots1.Add()->set_create_txid(50);
    snapshots3.Add()->set_create_txid(60);
    snapshots3.Add()->set_create_txid(70);

    TestGatherMarkDeletedSnapshots(true);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[2].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[1].merge_ranges.size());
    ASSERT_EQ(0, gc_info[2].merge_ranges.size());
    ASSERT_EQ(0, gc_info[3].merge_ranges.size());

    TestGatherMarkDeletedSnapshots(false);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[2].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_TRUE(CompareMergeRanges({{0, 10}, {10, 30}, {30, 50}},
                                   gc_info[1].merge_ranges));
    ASSERT_TRUE(CompareMergeRanges({{0, 20}}, gc_info[2].merge_ranges));
    ASSERT_TRUE(CompareMergeRanges({{0, 40}, {40, 60}, {60, 70}},
                                   gc_info[3].merge_ranges));
  }

  {
    // 1: s1 s3 s5(deleted), 2: s2(deleted), 3: s4(deleted) s6 s7
    snapshots1.Mutable(2)->set_deleted(true);
    snapshots2.Mutable(0)->set_deleted(true);
    snapshots3.Mutable(0)->set_deleted(true);

    TestGatherMarkDeletedSnapshots(true);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(20, gc_info[2].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_TRUE(CompareMergeRanges({{30, 50}}, gc_info[1].merge_ranges));
    ASSERT_EQ(0, gc_info[2].merge_ranges.size());
    ASSERT_TRUE(CompareMergeRanges({{0, 60}}, gc_info[3].merge_ranges));

    TestGatherMarkDeletedSnapshots(false);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(20, gc_info[2].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_TRUE(CompareMergeRanges({{0, 10}, {10, 30}, {30, 50}},
                                   gc_info[1].merge_ranges));
    ASSERT_EQ(0, gc_info[2].merge_ranges.size());
    ASSERT_TRUE(
        CompareMergeRanges({{0, 60}, {60, 70}}, gc_info[3].merge_ranges));
  }

  {
    // 1: s1(deleted) s3 s5(deleted), 3: s4(deleted) s6(deleted) s7
    snapshots1.Mutable(0)->set_deleted(true);
    snapshots3.Mutable(1)->set_deleted(true);

    TestGatherMarkDeletedSnapshots(true);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_TRUE(
        CompareMergeRanges({{0, 30}, {30, 50}}, gc_info[1].merge_ranges));
    ASSERT_TRUE(CompareMergeRanges({{0, 70}}, gc_info[3].merge_ranges));

    TestGatherMarkDeletedSnapshots(false);
    ASSERT_EQ(0, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_TRUE(
        CompareMergeRanges({{0, 30}, {30, 50}}, gc_info[1].merge_ranges));
    ASSERT_TRUE(CompareMergeRanges({{0, 70}}, gc_info[3].merge_ranges));
  }

  {
    // 1: s1 s3 s5 all deleted, 3: s4 s6 s7 all deleted
    snapshots1.Mutable(1)->set_deleted(true);
    snapshots3.Mutable(2)->set_deleted(true);

    TestGatherMarkDeletedSnapshots(true);
    ASSERT_EQ(50, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(70, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[1].merge_ranges.size());
    ASSERT_EQ(0, gc_info[3].merge_ranges.size());

    TestGatherMarkDeletedSnapshots(false);
    ASSERT_EQ(50, gc_info[1].snapshot_txid_lower_bound);
    ASSERT_EQ(70, gc_info[3].snapshot_txid_lower_bound);
    ASSERT_EQ(0, gc_info[1].merge_ranges.size());
    ASSERT_EQ(0, gc_info[3].merge_ranges.size());
  }
}

TEST_F(ReadWriteWithSnapshotsTest, AddBlock_Fsync_CompleteFile) {
  const std::string test = "/test";
  const std::string test_f = "/test/file";
  auto p = MakePermission();
  PrettyDirTree current;
  DANCENN_ASSERT_OK(ns_->MkDirs(test, p, true));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test));

#define DO_SNAPSHOT(name)                              \
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test, #name)); \
  current = PrintDirTree(test);                        \
  auto name = PrintDirTree("/test/.snapshot/" #name);  \
  ASSERT_TRUE(CompareDirTree(current, name));

  // create test_f, create snapshot s1
  auto create_request = MakeCreateRequest();
  CreateResponseProto create_response;
  DANCENN_ASSERT_OK(
      ns_->CreateFile(test_f, p, create_request, &create_response));
  DO_SNAPSHOT(s1)

  // add block, create snapshot s2
  int len = 30;
  cnetpp::base::IPAddress client_ip("***********");
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  DANCENN_ASSERT_OK(ns_->AddBlock(
      test_f, client_ip, default_rpc_info, add_request, &add_response));
  DO_SNAPSHOT(s2)

  BlockManager::RepeatedIncBlockReport report;
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  report.Clear();
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);

  // complete test_f, create snapshot s3
  CompleteRequestProto complete_request;
  complete_request.set_src(test_f);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(len);
  DANCENN_ASSERT_OK(ns_->CompleteFile(test_f, complete_request));
  DO_SNAPSHOT(s3)

  // append test_f, add block again, create snapshot s4
  len = 40;
  AppendResponseProto append_response;
  AppendFile(test_f, len, 3, false, &add_response, &append_response);
  DO_SNAPSHOT(s4)

  // fsync test_f, create snapshot s5
  report.Clear();
  MakeReport(add_response.block().b().blockid(),
             add_response.block().b().generationstamp(),
             len,
             cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
             &report);
  block_manager_->IncrementalBlockReport("datanode1", report);
  FsyncRequestProto fsync_request;
  fsync_request.set_client("client");
  fsync_request.set_lastblocklength(len);
  DANCENN_ASSERT_OK(ns_->Fsync(test_f, fsync_request));
  DO_SNAPSHOT(s5)

  // complete test_f, create snapshot s6
  complete_request.set_src(test_f);
  complete_request.set_clientname("client");
  complete_request.mutable_last()->CopyFrom(add_response.block().b());
  complete_request.mutable_last()->set_numbytes(len);
  DANCENN_ASSERT_OK(ns_->CompleteFile(test_f, complete_request));
  DO_SNAPSHOT(s6)
  auto s5_without_mtime = PrintDirTree("/test/.snapshot/s5", true, -1);
  auto s6_without_mtime = PrintDirTree("/test/.snapshot/s6", true, -1);
  ASSERT_TRUE(CompareDirTree(s5_without_mtime, s6_without_mtime));

  ASSERT_TRUE(WaitStandAppliedAndCompare());
  ASSERT_TRUE(CompareDirTree(PrintDirTree("/test/.snapshot/s1"), s1));
  ASSERT_TRUE(CompareDirTree(PrintDirTree("/test/.snapshot/s2"), s2));
  ASSERT_TRUE(CompareDirTree(PrintDirTree("/test/.snapshot/s3"), s3));
  ASSERT_TRUE(CompareDirTree(PrintDirTree("/test/.snapshot/s4"), s4));
  ASSERT_TRUE(CompareDirTree(PrintDirTree("/test/.snapshot/s5"), s5));
#undef DO_SNAPSHOT
}

TEST_F(ReadWriteWithSnapshotsTest, GetContentSummary) {
  /**
   * Calculate against a snapshot path.
   * 1. create dirs /foo/bar
   * 2. take snapshot s1 on /foo
   * 3. create a 10 byte file /foo/bar/baz
   * Make sure for "/foo/bar" and "/foo/.snapshot/s1/bar" have correct results:
   * the 1 byte file is not included in snapshot s1.
   * 4. create another snapshot, append to the file /foo/bar/baz,
   * and make sure file count, directory count and file length is good.
   * 5. delete the file, ensure contentSummary output too.
   */
  const std::string foo = "/foo";
  const std::string bar = "/foo/bar";
  const std::string baz = "/foo/bar/baz";
  const std::string qux = "/foo/bar/qux";
  const std::string temp = "/temp";

  auto p = MakePermission();
  DANCENN_ASSERT_OK(ns_->MkDirs(bar, p, true));
  DANCENN_ASSERT_OK(ns_->MkDirs(temp, p, true));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(foo));
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(foo, "s1"));

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  AddFile(baz, 10, 3, true, &add_response, &create_response, true);
  AddFile(qux, 10, 3, true, &add_response, &create_response, true);

  // check /foo/bar
  GetContentSummaryResponseProto get_cs_response;
  auto ugi = std::make_shared<UserGroupInfo>("root", "supergroup");
  DANCENN_ASSERT_OK(ns_->GetContentSummary(bar, ugi, &get_cs_response));
  ASSERT_EQ(1, get_cs_response.summary().directorycount());
  ASSERT_EQ(2, get_cs_response.summary().filecount());
  ASSERT_EQ(20, get_cs_response.summary().length());

  // check /foo/.snapshot/s1/bar
  const std::string bar_s1 = "/foo/.snapshot/s1/bar";
  get_cs_response.Clear();
  DANCENN_ASSERT_OK(ns_->GetContentSummary(bar_s1, ugi, &get_cs_response));
  ASSERT_EQ(1, get_cs_response.summary().directorycount());
  ASSERT_EQ(0, get_cs_response.summary().filecount());
  ASSERT_EQ(0, get_cs_response.summary().length());

  // check /foo
  get_cs_response.Clear();
  DANCENN_ASSERT_OK(ns_->GetContentSummary(foo, ugi, &get_cs_response));
  ASSERT_EQ(2, get_cs_response.summary().directorycount());
  ASSERT_EQ(2, get_cs_response.summary().filecount());
  ASSERT_EQ(20, get_cs_response.summary().length());

  // check /foo/.snapshot/s1
  const std::string foo_s1 = "/foo/.snapshot/s1";
  get_cs_response.Clear();
  DANCENN_ASSERT_OK(ns_->GetContentSummary(foo_s1, ugi, &get_cs_response));
  ASSERT_EQ(2, get_cs_response.summary().directorycount());
  ASSERT_EQ(0, get_cs_response.summary().filecount());
  ASSERT_EQ(0, get_cs_response.summary().length());

  // create a new snapshot s2 and update the file
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(foo, "s2"));
  AppendResponseProto append_response;
  AppendFile(baz, 20, 3, true, &add_response, &append_response);
  get_cs_response.Clear();
  DANCENN_ASSERT_OK(ns_->GetContentSummary(bar, ugi, &get_cs_response));
  ASSERT_EQ(1, get_cs_response.summary().directorycount());
  ASSERT_EQ(2, get_cs_response.summary().filecount());
  ASSERT_EQ(40, get_cs_response.summary().length());

  // check /foo/.snapshot/s2
  const std::string foo_s2 = "/foo/.snapshot/s2";
  get_cs_response.Clear();
  DANCENN_ASSERT_OK(ns_->GetContentSummary(foo_s2, ugi, &get_cs_response));
  ASSERT_EQ(2, get_cs_response.summary().directorycount());
  ASSERT_EQ(2, get_cs_response.summary().filecount());
  ASSERT_EQ(20, get_cs_response.summary().length());

  const std::string baz_s1 = "/foo/.snapshot/s1/bar/baz";
  DANCENN_ASSERT_STATUS(ns_->GetContentSummary(baz_s1, ugi, &get_cs_response),
                        JavaExceptions::kFileNotFoundException);

  // TODO add SnapshotDirectoryCount SnapshotFileCount SnapshotLength to summary
  // and check
  // delete file and check /foo
  // rename file and check /foo
  ASSERT_TRUE(WaitStandAppliedAndCompare());
}

TEST_F(ReadWriteWithSnapshotsTest, RestWriteRpcs) {
  std::string test_root = "/test";
  auto p = MakePermission();
  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  std::string set_pm = "/test/SetPermission";
  AddFile(set_pm, 10, 3, true, &add_response, &create_response, true);
  DANCENN_ASSERT_OK(ns_->SetPermission(set_pm, 1));

  std::string set_owner = "/test/SetOwner";
  AddFile(set_owner, 10, 3, true, &add_response, &create_response, true);
  DANCENN_ASSERT_OK(ns_->SetOwner(set_owner, "u1", "g1"));

  std::string set_xattr = "/test/SetXattr";
  AddFile(set_xattr, 10, 3, true, &add_response, &create_response, true);
  auto mock_xattr = [](const std::string& name, const std::string& value) {
    XAttrProto x;
    x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    x.set_name(name);
    x.set_value(value);
    return x;
  };
  DANCENN_ASSERT_OK(
      ns_->SetXAttr(set_xattr, mock_xattr("n", "v1"), true, false));
  // ReadPolicy, ReplicaPolicy, StoragePolicy seem not used for read, skip them

  std::string set_times = "/test/SetTimes";
  AddFile(set_times, 10, 3, true, &add_response, &create_response, true);
  SetTimesRequestProto st_request;
  st_request.set_atime(1);
  st_request.set_mtime(1);
  DANCENN_ASSERT_OK(ns_->SetTimes(set_times, &st_request, ugi_));

  // create snapshot s0 and call write rpcs
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s0"));
  DANCENN_ASSERT_OK(ns_->SetPermission(set_pm, 2));
  DANCENN_ASSERT_OK(ns_->SetOwner(set_owner, "u2", "g2"));
  DANCENN_ASSERT_OK(
      ns_->SetXAttr(set_xattr, mock_xattr("n", "v2"), false, true));
  st_request.set_atime(1);
  st_request.set_mtime(2);
  DANCENN_ASSERT_OK(ns_->SetTimes(set_times, &st_request, ugi_));

  // create snapshot s1 and call write rpcs
  DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
  DANCENN_ASSERT_OK(ns_->RemoveXAttr(set_xattr, mock_xattr("n", "")));

  // verify current dir tree and snapshot dir tree
  GetFileInfoResponseProto get_response;
  DANCENN_ASSERT_OK(ns_->GetFileInfo("/test/.snapshot/s0/SetPermission",
                                     NetworkLocationInfo(),
                                     false,
                                     false,
                                     &get_response,
                                     ugi_));
  ASSERT_EQ(1, get_response.fs().permission().perm());
  DANCENN_ASSERT_OK(ns_->GetFileInfo(
      set_pm, NetworkLocationInfo(), false, false, &get_response, ugi_));
  ASSERT_EQ(2, get_response.fs().permission().perm());

  DANCENN_ASSERT_OK(ns_->GetFileInfo("/test/.snapshot/s0/SetOwner",
                                     NetworkLocationInfo(),
                                     false,
                                     false,
                                     &get_response,
                                     ugi_));
  ASSERT_EQ("u1", get_response.fs().owner());
  ASSERT_EQ("g1", get_response.fs().group());
  DANCENN_ASSERT_OK(ns_->GetFileInfo(
      set_owner, NetworkLocationInfo(), false, false, &get_response, ugi_));
  ASSERT_EQ("u2", get_response.fs().owner());
  ASSERT_EQ("g2", get_response.fs().group());

  RepeatedPtrField<XAttrProto> xattrs, response;
  *xattrs.Add() = mock_xattr("n", "");
  RepeatedPtrField<XAttrProto> list_resp;
  DANCENN_ASSERT_OK(
      ns_->GetXAttrs("/test/.snapshot/s0/SetXattr", xattrs, &response, ugi_));
  ASSERT_EQ(1, response.size());
  ASSERT_EQ("v1", response.Get(0).value());
  response.Clear();
  DANCENN_ASSERT_OK(
      ns_->GetXAttrs("/test/.snapshot/s1/SetXattr", xattrs, &response, ugi_));
  ASSERT_EQ(1, response.size());
  ASSERT_EQ("v2", response.Get(0).value());
  response.Clear();
  DANCENN_ASSERT_OK(ns_->GetXAttrs(set_xattr, xattrs, &response, ugi_));
  ASSERT_EQ(0, response.size());

  DANCENN_ASSERT_OK(ns_->GetFileInfo("/test/.snapshot/s0/SetTimes",
                                     NetworkLocationInfo(),
                                     false,
                                     false,
                                     &get_response,
                                     ugi_));
  ASSERT_EQ(1, get_response.fs().access_time());
  ASSERT_EQ(1, get_response.fs().modification_time());
  DANCENN_ASSERT_OK(ns_->GetFileInfo(
      set_times, NetworkLocationInfo(), false, false, &get_response, ugi_));
  ASSERT_EQ(1, get_response.fs().access_time());
  ASSERT_EQ(2, get_response.fs().modification_time());
  ASSERT_TRUE(WaitStandAppliedAndCompare());
}

TEST_F(ReadWriteWithSnapshotsTest, CornerCases) {
  // add cases found by random test
  std::string test_root = "/test";
  std::string test_root2 = "/test2";
  std::string outside = "/outside";
  auto p = MakePermission();
  DANCENN_ASSERT_OK(ns_->MkDirs(test_root, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->MkDirs(test_root2, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->MkDirs(outside, p, true /*create_parent*/));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root2));

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  {
    // we should write snapshot inode even if it has been modified after latest
    // snapshot for delete/rename, otherwise we cannot recognize deletion
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/d2", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/d3", p, true));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    PrettyDirTree s2 = {"/test 0"};
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test/.snapshot/s2", false)));

    // test gc interrupted by should_stop
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s3"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/d2", p, true));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "ss"));

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s3"));
    ManualSnapshotGc(true, [](int64_t n) { return n > 0; });
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "ss"));
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }

  {
    // we can not simply delete the deleted record in one merge range
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/d2", p, true));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    ManualSnapshotGc(false);
    PrettyDirTree s2 = {"/test 0"};
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test/.snapshot/s2", false)));
    GetFileInfoResponseProto get_response;
    DANCENN_EXPECT_OK(ns_->GetFileInfo("/test/.snapshot/s2/d1",
                                       NetworkLocationInfo(),
                                       false /*resolve_link*/,
                                       false,
                                       &get_response,
                                       ugi_));
    ASSERT_FALSE(get_response.has_fs());

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    ManualSnapshotGc(true);
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }

  {
    // we can not deduplicate records if they have different parents/names
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/z_mkdir", p, true));
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1/z_mkdir", "/test/d1/a_rename"));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1/a_rename", true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s2"));
    // d1/z_mkdir and d1/a_rename are both in the same merge range of snapshot
    // table, we should keep both of them
    ManualSnapshotGc(false);
    PrettyDirTree s1 = {"/test 1", "/test/d1 1", "/test/d1/a_rename 0"};
    PrettyDirTree s2 = {"/test 1", "/test/d1 0"};
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test/.snapshot/s1", false)));
    ASSERT_TRUE(CompareDirTree(s2, PrintDirTree("/test/.snapshot/s2", false)));

    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s2"));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    ManualSnapshotGc(true);
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }

  {
    // delete after deleting the first snapshot(no deletion record) and there
    // exists snapshot inode not cleared
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/temp", p, true));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));

    PrettyDirTree s1 = {"/test 0"};
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test/.snapshot/s1", false)));

    ManualSnapshotGc(false);
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }

  {
    // delete after disallowSnapshot(no deletion record) and there exists
    // snapshot inode not cleared
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1/temp", p, true));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s0"));
    DANCENN_ASSERT_OK(ns_->DisAllowSnapshot(test_root));
    DANCENN_ASSERT_OK(ns_->Delete("/test/d1", true));
    DANCENN_ASSERT_OK(ns_->AllowSnapshot(test_root));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s1"));

    PrettyDirTree s1 = {"/test 0"};
    ASSERT_TRUE(CompareDirTree(s1, PrintDirTree("/test/.snapshot/s1", false)));

    ManualSnapshotGc(false);
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s1"));
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }

  {
    // inode is referred by two snapshot inodes which have no relationship to
    // each other
    DANCENN_ASSERT_OK(ns_->MkDirs("/test/d1", p, true));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root, "s0"));

    DANCENN_ASSERT_OK(ns_->MkDirs("/test2/d2", p, true));
    // d1 referred by snapshot inode from /test s0
    DANCENN_ASSERT_OK(ns_->RenameTo("/test/d1", "/test2/d2"));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(test_root2, "s0"));

    // d2 and d1 referred by snapshot inode from /test2 s0
    DANCENN_ASSERT_OK(ns_->RenameTo("/test2/d2", "/outside"));
    DANCENN_ASSERT_OK(ns_->Delete("/outside/d2/d1", true));

    // delete the first snapshot inode, another reference still work
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root, "s0"));
    ManualSnapshotGc(true);
    PrettyDirTree test_root2_s0 = {"/test2 1", "/test2/d2 1", "/test2/d2/d1 0"};
    ASSERT_TRUE(CompareDirTree(test_root2_s0,
                               PrintDirTree("/test2/.snapshot/s0", false)));

    DANCENN_ASSERT_OK(ns_->Delete("/outside/d2", true));
    DANCENN_ASSERT_OK(ns_->DeleteSnapshot(test_root2, "s0"));
    ManualSnapshotGc(false);
    auto count = GetSnapshotTableStat();
    ASSERT_EQ(0, count[kSnapshotInodeCFIndex].num_entry);
    ASSERT_TRUE(WaitStandAppliedAndCompare());
  }
}

TEST_F(ReadWriteWithSnapshotsTest, RandomOperation) {
  unsigned seed =
      FLAGS_test_seed == 0
          ? std::chrono::system_clock::now().time_since_epoch().count()
          : FLAGS_test_seed;
  LOG(INFO) << "[RandomTest] seed for debug reproduce: " << seed;
  used_seed_ = seed;

  std::default_random_engine gen(seed);
  std::uniform_int_distribution<int> file_length_dist(0, 100);
  std::uniform_int_distribution<int> fs_op_dist(0, 2);
  std::uniform_int_distribution<int> snapshot_op_dist(0, 1);
  std::uniform_int_distribution<int> sub_dir_depth_dist(0, 4);
  std::uniform_int_distribution<int> bool_dist(0, 1);

  std::string root_dir1 = "/test/snapshot_root1";
  std::string root_dir2 = "/test/snapshot_root2";
  std::string witness_dir = "/witness";  // no snapshot related operation on it
  std::set<std::string> init_dirs = {root_dir1, root_dir2, "/test/normal"};

  AddBlockResponseProto add_response;
  CreateResponseProto create_response;
  auto p = MakePermission();
  for (const auto& path : init_dirs) {
    DANCENN_ASSERT_OK(ns_->MkDirs(path, p, true));
    DANCENN_ASSERT_OK(ns_->MkDirs(witness_dir + path, p, true));
  }
  DANCENN_ASSERT_OK(ns_->AllowSnapshot("/test/snapshot_root1"));
  DANCENN_ASSERT_OK(ns_->AllowSnapshot("/test/snapshot_root2"));

  std::vector<std::string> existing_dirs(init_dirs.begin(), init_dirs.end());
  std::vector<std::string> existing_files;
  std::array<std::vector<std::string>*, 2> existing_paths{&existing_dirs,
                                                          &existing_files};
  auto GetRandomDirIndex = [&]() -> int {
    EXPECT_TRUE(!existing_dirs.empty());
    std::uniform_int_distribution<int> temp_dist(0, existing_dirs.size() - 1);
    return temp_dist(gen);
  };
  auto GetRandomFileIndex = [&]() -> int {
    EXPECT_TRUE(!existing_files.empty());
    EXPECT_TRUE(!existing_dirs.empty());
    std::uniform_int_distribution<int> temp_dist(0, existing_files.size() - 1);
    return temp_dist(gen);
  };
  auto GetRandomAncestor = [&](const std::string& dir) -> std::string {
    StringPieces ancestors;
    EXPECT_TRUE(GetAllAncestorPaths(dir, &ancestors)) << dir;
    return ancestors[std::uniform_int_distribution<int>(
                         2, ancestors.size() - 1)(gen)]
        .as_string();
  };
  auto DeleteDir = [&](const std::string& delete_dir) {
    for (auto vec : existing_paths) {
      vec->erase(std::remove_if(vec->begin(),
                                vec->end(),
                                [&](const std::string& s) {
                                  return s.rfind(delete_dir, 0) !=
                                         std::string::npos;
                                }),
                 vec->end());
    }
  };
  auto RenameDir = [&](const std::string& src_dir, const std::string& dst_dir) {
    std::regex re(src_dir);
    for (auto vec : existing_paths) {
      std::for_each(vec->begin(), vec->end(), [&](std::string& path) {
        path = std::regex_replace(path, re, dst_dir);
      });
    }
  };

  auto CreateTest = [&](int turn, int rank) {
    bool is_create_file = bool_dist(gen);
    int index = GetRandomDirIndex();
    std::string parent_dir = GetRandomAncestor(existing_dirs[index]);

    int sub_dir_depth = sub_dir_depth_dist(gen);
    for (int i = 0; i < sub_dir_depth; i++) {
      parent_dir += absl::Substitute("/$0_$1_$2", turn, rank, i);
    }

    if (is_create_file) {
      std::string new_file = parent_dir + "/create_" + UUID().ToString();
      int file_length = file_length_dist(gen);
      LOG(INFO) << "[RandomTest] CreateFile " << new_file << ", length "
                << file_length;
      AddFile(new_file,
              file_length,
              3,
              true,
              &add_response,
              &create_response,
              true);
      AddFile(witness_dir + new_file,
              file_length,
              3,
              true,
              &add_response,
              &create_response,
              true);
      if (sub_dir_depth > 0) {
        existing_dirs.emplace_back(parent_dir);
      }
      existing_files.emplace_back(new_file);
    } else {
      std::string new_dir = parent_dir + "/mkdir_" + UUID().ToString();
      LOG(INFO) << "[RandomTest] CreateDir " << new_dir;
      DANCENN_ASSERT_OK(ns_->MkDirs(new_dir, p, true));
      DANCENN_ASSERT_OK(ns_->MkDirs(witness_dir + new_dir, p, true));
      existing_dirs.emplace_back(new_dir);
    }
  };
  auto DeleteTest = [&]() {
    bool is_delete_file = bool_dist(gen);
    if (is_delete_file && !existing_files.empty()) {
      int index = GetRandomFileIndex();
      auto delete_file = existing_files[index];
      LOG(INFO) << "[RandomTest] DeleteFile " << delete_file;
      DANCENN_ASSERT_OK(ns_->Delete(delete_file, true));
      DANCENN_ASSERT_OK(ns_->Delete(witness_dir + delete_file, true));
      existing_files.erase(existing_files.begin() + index);
    }
    if (!is_delete_file && !existing_dirs.empty()) {
      int index = GetRandomDirIndex();
      auto delete_dir = existing_dirs[index];
      if (!init_dirs.count(delete_dir)) {
        LOG(INFO) << "[RandomTest] DeleteDir " << delete_dir;
        DANCENN_ASSERT_OK(ns_->Delete(delete_dir, true));
        DANCENN_ASSERT_OK(ns_->Delete(witness_dir + delete_dir, true));
        DeleteDir(delete_dir);
      }
    }
  };
  auto RenameTest = [&]() {
    bool is_rename_file = bool_dist(gen);
    if (is_rename_file && !existing_files.empty()) {
      int src_index = GetRandomFileIndex();
      auto src_file = existing_files[src_index];
      int dst_index = GetRandomDirIndex();
      auto dst_file =
          existing_dirs[dst_index] + "/rn_file_" + UUID().ToString();

      LOG(INFO) << "[RandomTest] RenameFile " << src_file << " to " << dst_file;
      DANCENN_ASSERT_OK(ns_->RenameTo(src_file, dst_file));
      DANCENN_ASSERT_OK(
          ns_->RenameTo(witness_dir + src_file, witness_dir + dst_file));
      existing_files[src_index] = dst_file;
    }
    if (!is_rename_file && !existing_dirs.empty()) {
      int src_index = GetRandomDirIndex();
      auto src_dir = existing_dirs[src_index];
      int dst_index = GetRandomDirIndex();
      auto dst_dir = existing_dirs[dst_index] + "/rn_dir_" + UUID().ToString();

      if (!init_dirs.count(src_dir) &&
          dst_dir.rfind(src_dir, 0) == std::string::npos) {
        LOG(INFO) << "[RandomTest] RenameDir " << src_dir << " to " << dst_dir;
        DANCENN_ASSERT_OK(ns_->RenameTo(src_dir, dst_dir));
        DANCENN_ASSERT_OK(
            ns_->RenameTo(witness_dir + src_dir, witness_dir + dst_dir));
        RenameDir(src_dir, dst_dir);
      }
    }
  };

  std::map<std::string, PrettyDirTree> snapshot_ground_truths;
  auto UpdateSnapshotGT = [&](const std::string& snapshot_name, bool is_del) {
    if (is_del) {
      snapshot_ground_truths.erase(snapshot_name);
    } else {
      PrettyDirTree test_current_detail = PrintDirTree(root_dir1);
      snapshot_ground_truths.emplace(snapshot_name, test_current_detail);
    }
  };
  auto CreateSnapshot = [&](int turn, const std::string& suffix) {
    auto snapshot_name = absl::Substitute("s$0_$1", turn, suffix);
    LOG(INFO) << "[RandomTest] CreateSnapshot " << snapshot_name;
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(root_dir1, snapshot_name));
    DANCENN_ASSERT_OK(ns_->CreateSnapshot(root_dir2, snapshot_name));
    UpdateSnapshotGT(snapshot_name, false);
  };
  auto DeleteSnapshotTest = [&]() {
    GetListingRequestProto ls_request;
    GetListingResponseProto ls_response;
    DANCENN_EXPECT_OK(ns_->GetListing(root_dir1 + "/.snapshot",
                                      NetworkLocationInfo(),
                                      ls_request,
                                      &ls_response,
                                      ugi_));
    const auto& ss_listing = ls_response.dirlist().partiallisting();
    ASSERT_EQ(ss_listing.size(), snapshot_ground_truths.size());
    if (ss_listing.size() > 0) {
      std::uniform_int_distribution<int> idx_dist(0, ss_listing.size() - 1);
      auto snapshot_name = ss_listing.Get(idx_dist(gen)).path();
      LOG(INFO) << "[RandomTest] DeleteSnapshot " << snapshot_name;
      DANCENN_ASSERT_OK(ns_->DeleteSnapshot(root_dir1, snapshot_name));
      DANCENN_ASSERT_OK(ns_->DeleteSnapshot(root_dir2, snapshot_name));
      UpdateSnapshotGT(snapshot_name, true);
    }
  };

  int num_iterations = 10;
  int num_filesystem_ops = 10;
  int num_pre_create_paths = 20;

  for (int i = 0; i < num_pre_create_paths; i++) {
    CreateTest(-1, i);
  }
  for (int test_turn = 0; test_turn < num_iterations; test_turn++) {
    LOG(INFO) << "[RandomTest] Start turn " << test_turn;
    if (test_turn == 1) {
      // let background snapshot gc run in this test case
      std::uniform_int_distribution<int> interval_dist(100, 1000);
      ns_->StartSnapshotGcWorker(interval_dist(gen));
      standby_->StartSnapshotGcWorker(interval_dist(gen));
    }
    if (test_turn == num_iterations / 2) {
      LOG(INFO) << "[RandomTest] Delete all snapshots halfway, "
                   "disallowSnapshot and allowSnapshot";
      for (const auto& elem : snapshot_ground_truths) {
        ns_->DeleteSnapshot(root_dir1, elem.first);
        ns_->DeleteSnapshot(root_dir2, elem.first);
      }
      snapshot_ground_truths.clear();
      ns_->DisAllowSnapshot(root_dir1);
      ns_->DisAllowSnapshot(root_dir2);
      ns_->AllowSnapshot(root_dir1);
      ns_->AllowSnapshot(root_dir2);
    }

    CreateSnapshot(test_turn, "normal");

    // compare test dir with witness dir
    PrettyDirTree witness = PrintDirTree(witness_dir + root_dir1, false);
    std::regex re(R"(/witness)");
    for (auto& str : witness) {
      str = std::regex_replace(str, re, "");
    }
    PrettyDirTree test_current = PrintDirTree(root_dir1, false);
    ASSERT_TRUE(CompareDirTree(witness, test_current));

    // compare snapshot's view with ground truth
    for (const auto& elem : snapshot_ground_truths) {
      std::string snapshot_dir = root_dir1 + "/.snapshot/" + elem.first;
      LOG(INFO) << "[RandomTest] Verify snapshot " << snapshot_dir << ", turn "
                << test_turn;
      PrettyDirTree snapshot_view = PrintDirTree(snapshot_dir);
      ASSERT_TRUE(CompareDirTree(elem.second, snapshot_view));
    }

    if (test_turn == num_iterations - 1) {
      break;
    }
    for (int i = 0; i < num_filesystem_ops; i++) {
      int filesystem_op = fs_op_dist(gen);
      switch (filesystem_op) {
        case 0:
          CreateTest(test_turn, i);
          break;
        case 1:
          DeleteTest();
          break;
        case 2:
          RenameTest();
          break;
        default:
          FAIL() << "invalid filesystem_op " << filesystem_op;
      }
    }

    int snapshot_op = snapshot_op_dist(gen);
    switch (snapshot_op) {
      case 0:
        CreateSnapshot(test_turn, "random");
        break;
      case 1:
        DeleteSnapshotTest();
        break;
      default:
        FAIL() << "invalid snapshot_op " << snapshot_op;
    }
  }
}

}  // namespace dancenn
