#ifndef TEST_NAMESPACE_MOCK_META_SCANNER_V2_H_
#define TEST_NAMESPACE_MOCK_META_SCANNER_V2_H_

#include <gmock/gmock.h>

#include "namespace/meta_scanner_v2.h"

namespace dancenn {

class GMockMetaScannerTask : public MetaScannerTaskBase {
 public:
  MOCK_METHOD1(SetMetaStorage, void(MetaStorage*));
  MOCK_METHOD1(SetMetaScanner, void(MetaScannerBase*));
  MOCK_METHOD0(GetTaskID, MetaScannerTaskID());
  MOCK_METHOD1(SetTaskID, void(MetaScannerTaskID));
  MOCK_METHOD0(GetCfIdx, uint32_t());
  MOCK_METHOD0(PreScan, bool());
  MOCK_METHOD3(Handle,
               bool(MetaStorageSnapPtr,
                    const rocksdb::Slice& key,
                    const rocksdb::Slice& value));
  MOCK_METHOD0(PostScan, bool());
  MOCK_METHOD0(GetDelayUs, int64_t());
  MOCK_METHOD0(ToString, std::string());
  MOCK_METHOD1(Run, bool(void*));

  MOCK_METHOD0(IsStopped, bool());
};

class GMockMetaScannerV2 : public MetaScannerBase {
 public:
  MOCK_METHOD0(Start, void());
  MOCK_METHOD0(Stop, void());
  MOCK_METHOD1(AddScanTask,
               MetaScannerTaskID(std::shared_ptr<MetaScannerTaskBase>));
  MOCK_METHOD1(AddScanTask, bool(MetaScannerTaskID));
  MOCK_METHOD2(EraseScanTask, bool(MetaScannerTaskID, bool));
  MOCK_METHOD1(AddWorkTask, bool(std::shared_ptr<MetaScannerWorkTask>));
};

class GMockMetaScannerWorker : public MetaScannerWorkerBase {
 public:
  MOCK_METHOD0(Start, void());
  MOCK_METHOD0(Stop, void());
  MOCK_METHOD1(AddTask, bool(std::shared_ptr<MetaScannerWorkTask>));
};

class GMockMetaScannerWorkTask : public MetaScannerWorkTask {
 public:
  MOCK_METHOD1(Run, bool(void*));
};

}  // namespace dancenn

#endif /* TEST_NAMESPACE_MOCK_META_SCANNER_V2_H_ */
