// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include "test/namespace/mock_namespace.h"
#include "mock_edit_log_sender.h"
#include "mock_edit_log_context.h"
#include "base/file_utils.h"

DECLARE_string(all_datacenters);
DECLARE_int32(namespace_type);

namespace dancenn {

class AccessCounterManagerTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    racks[0] = racks[1] = "/dc0/RACK0";
    racks[2] = racks[4] = "/dc1/RACK2";
    racks[3] = "/dc1/RACK3";
    std::string hosts[5];
    hosts[0] = "/host0";
    hosts[1] = "/host1";
    hosts[2] = "/host2";
    hosts[3] = "/host3";
    hosts[4] = "/host4";

    LOG(INFO) << "SetUp1";
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    auto context = std::static_pointer_cast<EditLogContextBase>(c);

    LOG(INFO) << "SetUp2";
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

    LOG(INFO) << "SetUp3";
    FLAGS_all_datacenters = "dc0,dc1";
    auto datanode_manager = std::make_shared<dancenn::DatanodeManager>();
    auto bm = std::make_shared<dancenn::BlockManager>();
    auto job_manager =
        std::make_shared<dancenn::JobManager>(datanode_manager, bm);
    MockFSImageTransfer(db_path_).Transfer();
    ns_.reset(new MockNameSpace(db_path_,
                                context,
                                bm,
                                datanode_manager,
                                std::make_shared<DataCenters>(),
                                nullptr));

    LOG(INFO) << "SetUp4";
    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(context, last_tx_id + 1));
    ns_->TestOnlySetEditLogSender(std::move(sender));
    LOG(INFO) << "SetUp5";
  }

  void TearDown() override {
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::unique_ptr<MockNameSpace> ns_;
  std::string db_path_ = "rocksdb_XXXXXX";

  std::string racks[5];
};


TEST_F(AccessCounterManagerTest, TestRecursiveIncrease) {
  LOG(INFO) << "TestRecursiveIncrease1";
  ns_->access_counter_manager()->IncreaseAccessCounter("/qwe/asd/zxc/ert/dfg",
      ns_->data_centers()->GetDataCenterById(0), 1);
  LOG(INFO) << "TestRecursiveIncrease2";
  ASSERT_EQ(4, ns_->access_counter_manager()->counter_map().size());
  LOG(INFO) << "TestRecursiveIncrease4";
  ASSERT_EQ("/", ns_->access_counter_manager()->Get("/")->path_);
  LOG(INFO) << "TestRecursiveIncrease5";
  ASSERT_EQ(1, ns_->access_counter_manager()->Get("/")->count_);
  LOG(INFO) << "TestRecursiveIncrease6";
  ASSERT_EQ("/qwe", ns_->access_counter_manager()->Get("/qwe")->path_);
  LOG(INFO) << "TestRecursiveIncrease7";
  ASSERT_EQ(1, ns_->access_counter_manager()->Get("/qwe")->count_);
  LOG(INFO) << "TestRecursiveIncrease8";
  ASSERT_EQ("/qwe/asd", ns_->access_counter_manager()->Get("/qwe/asd")->path_);
  LOG(INFO) << "TestRecursiveIncrease9";
  ASSERT_EQ(1, ns_->access_counter_manager()->Get("/qwe/asd")->count_);
  LOG(INFO) << "TestRecursiveIncrease10";
  ASSERT_EQ("/qwe/asd/zxc",
      ns_->access_counter_manager()->Get("/qwe/asd/zxc")->path_);
  LOG(INFO) << "TestRecursiveIncrease11";
  ASSERT_EQ(1, ns_->access_counter_manager()->Get("/qwe/asd/zxc")->count_);
  LOG(INFO) << "TestRecursiveIncrease12";
}

TEST_F(AccessCounterManagerTest, TestMergeValue) {
  LOG(INFO) << "TestMergeValue1";
  ns_->access_counter_manager()->IncreaseAccessCounter("/qwe",
      ns_->data_centers()->GetDataCenterById(0), 70);
  ns_->access_counter_manager()->IncreaseAccessCounter("/qwe",
      ns_->data_centers()->GetDataCenterById(1), 30);
  ns_->access_counter_manager()->Get("/")->Flush(0.9);
  ASSERT_EQ(.52, ns_->access_counter_manager()->Get("/")->snapshots_[0]);
  ASSERT_EQ(.48, ns_->access_counter_manager()->Get("/")->snapshots_[1]);
  LOG(INFO) << "TestMergeValue2";
}

TEST_F(AccessCounterManagerTest, TestGetValues) {
  LOG(INFO) << "TestGetValues1";
  ns_->access_counter_manager()->IncreaseAccessCounter("/qwe",
      ns_->data_centers()->GetDataCenterById(0), 1);
  auto v = ns_->access_counter_manager()->GetAccessCounterValues("/qwe/asd");
  ASSERT_EQ(0.5, v[ns_->data_centers()->GetDataCenterById(0)]);
  ASSERT_EQ(0.5, v[ns_->data_centers()->GetDataCenterById(1)]);
  LOG(INFO) << "TestGetValues2";
}

TEST_F(AccessCounterManagerTest, TestTakeSnapshot) {
  LOG(INFO) << "TestTakeSnapshot1";
  ns_->access_counter_manager()->IncreaseAccessCounter("/qwe",
      ns_->data_centers()->GetDataCenterById(0), 1);
  {
    auto snapshot = ns_->access_counter_manager()->Get("/")->TakeSnapshot();
    ASSERT_EQ("/", snapshot.path());
    ASSERT_EQ(1, snapshot.count());
    ASSERT_EQ(2, snapshot.dcvalue_size());
    ASSERT_EQ(0.5, snapshot.dcvalue(0));
    ASSERT_EQ(0.5, snapshot.dcvalue(1));
  }
  LOG(INFO) << "TestTakeSnapshot2";

  {
    ns_->access_counter_manager()->Get("/")->Flush(0.9);
    auto snapshot = ns_->access_counter_manager()->Get("/")->TakeSnapshot();
    ASSERT_EQ("/", snapshot.path());
    ASSERT_EQ(1, snapshot.count());
    ASSERT_EQ(2, snapshot.dcvalue_size());
    ASSERT_EQ(0.55, snapshot.dcvalue(0));
    ASSERT_EQ(0.45, snapshot.dcvalue(1));
  }
  LOG(INFO) << "TestTakeSnapshot3";

  {
    auto snapshot = ns_->access_counter_manager()->Get("/qwe")->TakeSnapshot();
    ASSERT_EQ("/qwe", snapshot.path());
    ASSERT_EQ(1, snapshot.count());
    ASSERT_EQ(2, snapshot.dcvalue_size());
    ASSERT_EQ(0.5, snapshot.dcvalue(0));
    ASSERT_EQ(0.5, snapshot.dcvalue(1));
  }

  {
    ns_->access_counter_manager()->Get("/qwe")->Flush(0.9);
    auto snapshot = ns_->access_counter_manager()->Get("/qwe")->TakeSnapshot();
    ASSERT_EQ("/qwe", snapshot.path());
    ASSERT_EQ(1, snapshot.count());
    ASSERT_EQ(2, snapshot.dcvalue_size());
    ASSERT_EQ(0.55, snapshot.dcvalue(0));
    ASSERT_EQ(0.45, snapshot.dcvalue(1));
  }
  LOG(INFO) << "TestTakeSnapshot4";
}

}  // namespace dancenn

