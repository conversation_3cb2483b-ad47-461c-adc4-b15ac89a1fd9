
//
// Copyright (c) 2021 Bytedance Inc. All rights reserved.
//

#include <glog/logging.h>
#include <google/protobuf/repeated_field.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <atomic>
#include <cstdint>
#include <ctime>
#include <iostream>
#include <random>
#include <sstream>
#include <string>

#include "base/file_utils.h"
#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "base/retry_cache.h"
#include "cnetpp/base/log.h"
#include "edit/tailer.h"
#include "mock_ha_state.h"   // NOLINT(build/include)
#include "mock_namespace.h"  // NOLINT(build/include)
#include "mock_safe_mode.h"  // NOLINT(build/include)
#include "namespace/namespace_scrub.h"
#include "namespace/namespace_usage_reporter.h"
#include "namespace/quota_manager.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "rpc/rpc_server.h"
#include "service/client_namenode_service.h"
#include "test/dancenn_test_base.h"
#include "test/edit/java_const.h"  // NOLINT(build/include)
#include "ufs/tos/tos_cred_keeper.h"

DECLARE_string(all_datacenters);
DECLARE_int32(scrub_list_subdir_batch_size);
DECLARE_bool(usage_report_enable);
DECLARE_string(usage_service_endpoint);
DECLARE_int32(usage_report_interval_seconds);
DECLARE_string(listen_ip_address);
DECLARE_string(observer_endpoint);
DECLARE_int32(client_rpc_port);
DECLARE_bool(dancenn_observe_mode_on);
DECLARE_bool(recycle_bin_enable);

static const uint64_t kBytesPerMB = 1024UL * 1024;
namespace dancenn {

class NamespaceStatTest : public dancenn::test::DanceNNCommonTest {
 public:
  void SetUp() override {
    FLAGS_recycle_bin_enable = false;
    old_observe_mode_on_ = FLAGS_dancenn_observe_mode_on;
    FLAGS_dancenn_observe_mode_on = true;
    FLAGS_scrub_list_subdir_batch_size = 5;

    FLAGS_all_datacenters = "LF,HL,LQ";
    dancenn::test::DanceNNCommonTest::SetUp();
    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    block_manager_->set_datanode_manager(datanode_manager_);
    job_manager_ = std::make_shared<dancenn::JobManager>(datanode_manager_,
                                                         block_manager_);
    auto base = GetBase();
    auto db_path = base->GetDBDir();
    auto ctx = base->GetEditLogContext();
    auto meta_storage = base->GetMetaStorage();
    meta_storage->Shutdown();
    MockFSImageTransfer(db_path).Transfer();
    ns_ = std::make_shared<NameSpace>(db_path,
                                      ctx,
                                      block_manager_,
                                      datanode_manager_,
                                      job_manager_,
                                      std::make_shared<DataCenters>(),
                                      nullptr,
                                      nullptr);
    ha_state_ = std::make_shared<MockHAState>();
    safemode_ = std::make_shared<MockSafeMode>();
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    block_manager_->set_ha_state(ha_state_.get());
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->set_ha_state(ha_state_.get());
    ns_->set_safemode(safemode_.get());
    ns_->StartStandby();
    auto last_applied_txid = ns_->GetLastCkptTxId();
    auto jvm = base->GetJVM();
    tailer_.reset(new EditLogTailer(
      last_applied_txid, jvm, ctx, ns_.get()));
    tailer_->Start();
  }

  void TearDown() override {
    ns_->StopStandby();
    tailer_.reset();
    datanode_manager_.reset();
    block_manager_.reset();
    ns_.reset();
    ha_state_.reset();
    safemode_.reset();
    dancenn::test::DanceNNCommonTest::TearDown();
    FLAGS_dancenn_observe_mode_on = old_observe_mode_on_;
    FLAGS_scrub_list_subdir_batch_size = 100;
  }

  void AddFile(const std::string& file_path, bool overwrite = false) {
    LOG(INFO) << "AddFile path: " << file_path << ", overwrite: " << overwrite;

    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
    op_add->SetOpCode(OP_ADD);
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(tx_id);
    op_add->set_path(file_path);
    op_add->set_replication(2);
    op_add->set_blockSize(kBlockSize);
    op_add->set_clientName("mock_clientname");
    op_add->set_clientMachine("mock_clientmachine");
    op_add->set_overwrite(overwrite);
    op_add->set_clientId("mock_clientid");
    op_add->set_callId(call_id_.fetch_add(1));

    tailer_->Apply(op_add);
    ns_->WaitNoPending();
  }

  uint64_t AddBlock(const std::string& file_path) {
    LOG(INFO) << "AddBlock path: " << file_path;

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(file_path, &path_components);
    std::vector<cloudfs::BlockProto> blocks;
    {
      INode file;
      CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);
      if (file.blocks_size() > 0) {
        blocks.push_back(file.blocks(file.blocks_size() - 1));
      }
    }

    std::shared_ptr<OpAddBlock> op_add_block = std::make_shared<OpAddBlock>();
    op_add_block->SetOpCode(OP_ADD_BLOCK);
    op_add_block->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_add_block->set_path(file_path);
    cloudfs::BlockProto bp0 = cloudfs::BlockProto();
    bp0.set_blockid(block_id_.fetch_add(1));
    bp0.set_genstamp(gs_.fetch_add(1));
    bp0.set_numbytes(0);
    blocks.push_back(bp0);
    op_add_block->set_blocks(blocks);
    op_add_block->set_clientId("mock_clientid");
    op_add_block->set_callId(call_id_.fetch_add(1));
    tailer_->Apply(op_add_block);
    ns_->WaitNoPending();

    return bp0.blockid();
  }

  void UpdateBlock(const std::string& path, uint64_t block_id, uint64_t bytes) {
    LOG(INFO) << "UpdateBlock path: " << path << ", block_id: " << block_id << ", bytes: " << bytes;

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(path, &path_components);

    INode file;
    CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);
    std::vector<cloudfs::BlockProto> blocks;
    for (auto&& b : file.blocks()) {
      blocks.push_back(b);
    }
    auto&& last = blocks.back();
    CHECK(last.blockid() == block_id);
    last.set_numbytes(bytes);

    auto op_update_blocks = std::make_shared<OpUpdateBlocks>();
    op_update_blocks->SetOpCode(OP_UPDATE_BLOCKS);
    op_update_blocks->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_update_blocks->set_path(path);
    op_update_blocks->set_blocks(blocks);
    tailer_->Apply(op_update_blocks);
    ns_->WaitNoPending();
  }

  void AppendFile(const std::string& file_path) {
    LOG(INFO) << "AppendFile file_path: " << file_path;
    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(file_path, &path_components);

    INode file;
    CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);
    std::vector<cloudfs::BlockProto> blocks;
    for (auto&& b : file.blocks()) {
      blocks.push_back(b);
    }
    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
    op_add->SetOpCode(OP_ADD);
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(tx_id);
    op_add->set_path(file_path);
    op_add->set_replication(2);
    op_add->set_blockSize(kBlockSize);
    op_add->set_blocks(blocks);
    op_add->set_clientName("mock_clientname");
    op_add->set_clientMachine("mock_clientmachine");
    op_add->set_clientId("mock_clientid");
    op_add->set_callId(call_id_.fetch_add(1));

    tailer_->Apply(op_add);
    ns_->WaitNoPending();
  }

  void CloseFile(const std::string& file_path) {
    LOG(INFO) << "CloseFile: " << file_path;
    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(file_path, &path_components);

    INode file;
    CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);
    std::vector<cloudfs::BlockProto> blocks;
    for (auto&& b : file.blocks()) {
      blocks.push_back(b);
    }

    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    auto op = std::make_shared<OpClose>();
    op->SetOpCode(OP_CLOSE);
    op->SetTxid(tx_id);
    op->set_path(file_path);
    op->set_replication(2);
    op->set_blockSize(kBlockSize);
    op->set_blocks(blocks);

    tailer_->Apply(op);
    ns_->WaitNoPending();
  }

  uint64_t now_ms() {
    return std::chrono::duration_cast<std::chrono::milliseconds>
          (std::chrono::system_clock::now().time_since_epoch()).count();
  }

  void Delete(const std::string& path) {
    LOG(INFO) << "Delete: " << path;

    auto op_delete = std::make_shared<OpDelete>();
    op_delete->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_delete->SetOpCode(OP_DELETE);
    op_delete->set_path(path);
    op_delete->set_timestamp(now_ms());

    tailer_->Apply(op_delete);
    ns_->WaitNoPending();
  }

  void Mkdir(const std::string& path) {
    LOG(INFO) << "Mkdir: " << path;

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(path, &path_components);

    INode file;
    INode parent;
    CHECK(ns_->GetLastINodeInPath(path_components, &file, nullptr, &parent) == StatusCode::kFileNotFound);
    CHECK(parent.id() != 0 && parent.id() != kInvalidINodeId);

    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    auto op = std::make_shared<OpMkdir>();
    op->SetTxid(tx_id);
    op->SetOpCode(OP_MKDIR);
    op->set_inodeId(inode_id);
    op->set_path(path);
    op->set_mtime(now_ms());
    op->set_atime(now_ms());

    tailer_->Apply(op);
    ns_->WaitNoPending();
  }

  void RenameOld(const std::string& src, const std::string& dst) {
    LOG(INFO) << "RenameOld src: " << src << ", dst: " << dst;

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(src, &path_components);

    INode file;
    CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);

    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    auto op = std::make_shared<OpRenameOld>();
    op->SetOpCode(OP_RENAME_OLD);
    op->SetTxid(tx_id);
    op->set_src(src);
    op->set_dst(dst);
    op->set_timestamp(now_ms());
    op->set_clientId(INVALID_CLIENT_ID);
    op->set_callId(INVALID_CALL_ID);

    tailer_->Apply(op);
    ns_->WaitNoPending();
  }

  void RenameWithOverwrite(const std::string& src, const std::string& dst, bool overwrite = false) {
    LOG(INFO) << "RenameWithOverwrite src: " << src << ", dst: " << dst << ", overwrite: " << overwrite;

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(src, &path_components);

    INode file;
    CHECK(ns_->GetLastINodeInPath(path_components, &file) == StatusCode::kOK);

    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;

    auto op = std::make_shared<OpRename>();
    op->SetOpCode(OP_RENAME);
    op->SetTxid(tx_id);
    op->set_src(src);
    op->set_dst(dst);
    op->set_timestamp(now_ms());
    RenameOptions opts;
    if (overwrite) {
      opts = RenameOptions(RenameOption::kOverwrite);
    } else {
      opts = RenameOptions(RenameOption::kNone);
    }
    op->set_options(opts);
    op->set_clientId(INVALID_CLIENT_ID);
    op->set_callId(INVALID_CALL_ID);

    tailer_->Apply(op);
    ns_->WaitNoPending();
  }

  void CreateINodes(int level, int subdir_num, int subfile_num,
                    uint64_t block_size = 128UL * kBytesPerMB,
                    bool create_file_in_each_dir = false) {
    CreateINodesInner("", 0, level, subdir_num, subfile_num, block_size,
                      create_file_in_each_dir);
  }

  void CreateINodesInner(const std::string& path, int current_level,
                         int max_level, int subdir_num, int subfile_num,
                         uint64_t block_size, bool create_file_in_each_dir) {
    if (current_level == max_level) {
      // Only Create File in last level
      for (int i = 0; i < subfile_num; ++i) {
        std::stringstream ss;
        ss << path << "/level-" << current_level << "-file-" << i;
        auto&& file = ss.str();
        AddFile(file);
        uint64_t block_id = AddBlock(file);
        UpdateBlock(file, block_id, block_size);
      }
      return;
    }

    // Create files mixed with dirs
    int dirs_num = subdir_num;
    int files_num = create_file_in_each_dir ? subfile_num : 0;
    int child_index = 0;
    for (int i = 0; i < std::min(dirs_num, files_num); ++i) {
      {
        std::stringstream ss;
        ss << path << "/level-" << current_level << "-child-" << child_index
           << "-dir";
        auto&& subdir = ss.str();
        Mkdir(subdir);
        CreateINodesInner(subdir, current_level + 1, max_level, subdir_num,
                          subfile_num, block_size, create_file_in_each_dir);
        ++child_index;
      }
      {
        std::stringstream ss;
        ss << path << "/level-" << current_level << "-child-" << child_index
           << "-file";
        auto&& file = ss.str();
        AddFile(file);
        uint64_t block_id = AddBlock(file);
        UpdateBlock(file, block_id, block_size);
        ++child_index;
      }
    }
    if (dirs_num > files_num) {
      for (int i = files_num; i < dirs_num; ++i) {
        std::stringstream ss;
        ss << path << "/level-" << current_level << "-child-" << child_index
           << "-dir";
        auto&& subdir = ss.str();
        Mkdir(subdir);
        CreateINodesInner(subdir, current_level + 1, max_level, subdir_num,
                          subfile_num, block_size, create_file_in_each_dir);
        ++child_index;
      }
    }
    if (files_num > dirs_num) {
      for (int i = dirs_num; i < files_num; ++i) {
        std::stringstream ss;
        ss << path << "/level-" << current_level << "-child-" << child_index
           << "-file";
        auto&& file = ss.str();
        AddFile(file);
        uint64_t block_id = AddBlock(file);
        UpdateBlock(file, block_id, block_size);
        ++child_index;
      }
    }
  }

public:

  const uint64_t kBlockSize = 128 * 1024 * 1024;
  static std::atomic<uint32_t> call_id_;
  static std::atomic<uint64_t> gs_;
  static std::atomic<uint64_t> block_id_;

  std::unique_ptr<EditLogTailer> tailer_;

  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<SafeModeBase> safemode_;
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<JobManager> job_manager_;

  bool old_observe_mode_on_ = false;
};

std::atomic<uint32_t> NamespaceStatTest::call_id_ { 1048576 };
std::atomic<uint64_t> NamespaceStatTest::gs_ { 1 };
std::atomic<uint64_t> NamespaceStatTest::block_id_ { 1048576 * 10 };

TEST_F(NamespaceStatTest, TestAddEmptyFile) {

  std::string file1("/a_file_1");
  AddFile(file1);

  INodeStat stat;
  auto s = ns_->GetDirectoryStat("/", &stat);
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(stat.inode_num, 1);
  ASSERT_EQ(stat.file_num, 1);
  ASSERT_EQ(stat.dir_num, 0);
  ASSERT_EQ(stat.block_num, 0);
  ASSERT_EQ(stat.data_size, 0);

  auto cli_service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<ClientNamenodeService>(
          datanode_manager_, block_manager_, ns_, safemode_.get(), nullptr));
  auto cli_sm = std::make_shared<ServiceMeta>(cli_service, 0);
  std::static_pointer_cast<ClientNamenodeService>(cli_service)
      ->SetMethodMetas(cli_sm);
  RpcServer server;
  server.RegisterService(
      std::make_shared<dancenn::RpcServer::ServiceMap>(RpcServer::ServiceMap{
          {"org.apache.hadoop.hdfs.protocol.ClientProtocol", cli_sm}}));
  server.Launch(
      cnetpp::base::EndPoint(FLAGS_listen_ip_address, FLAGS_client_rpc_port),
      RpcServerOptions());
  FLAGS_observer_endpoint = FLAGS_listen_ip_address;
  QuotaClient quota_client;
  quota_client.Start();
  cloudfs::GetINodeStatRequestProto request;
  cloudfs::GetINodeStatResponseProto response;
  request.add_inodeids(kRootINodeId);
  request.add_inodeids(kRootINodeId + 1);  // Not directory.
  request.add_inodeids(kRootINodeId + 2);  // Not existed inode.
  ASSERT_TRUE(quota_client.GetQuotas(request, &response));
  ASSERT_EQ(response.snapshottxid(), 1);
  ASSERT_EQ(response.inodestats_size(), 1);
  ASSERT_EQ(response.inodestats(0).inodeid(), kRootINodeId);
  ASSERT_EQ(response.inodestats(0).inodenum(), 1);
  ASSERT_EQ(response.inodestats(0).filenum(), 1);
  ASSERT_EQ(response.inodestats(0).dirnum(), 0);
  ASSERT_EQ(response.inodestats(0).blocknum(), 0);
  ASSERT_EQ(response.inodestats(0).datasize(), 0);
  ASSERT_EQ(response.missinginodeids_size(), 2);
  ASSERT_EQ(response.missinginodeids(0), kRootINodeId + 1);
  ASSERT_EQ(response.missinginodeids(1), kRootINodeId + 2);
  quota_client.Stop();
  server.Shutdown();
}

TEST_F(NamespaceStatTest, TestAddBlock) {
  std::string file1("/a_file_1");
  AddFile(file1);
  AddBlock(file1);

  INodeStat stat;
  auto s = ns_->GetDirectoryStat("/", &stat);
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(stat.inode_num, 1);
  ASSERT_EQ(stat.file_num, 1);
  ASSERT_EQ(stat.dir_num, 0);
  ASSERT_EQ(stat.block_num, 1);
  ASSERT_EQ(stat.data_size, 0);
}

TEST_F(NamespaceStatTest, TestUpdateBlock) {
  std::string file1("/a_file_1");
  std::vector<cnetpp::base::StringPiece> path_components;
  SplitPath(file1, &path_components);

  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  uint64_t block2_id = AddBlock(file1);
  uint64_t block2_size = 128 * 1024 * 1024;
  UpdateBlock(file1, block2_id, block2_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 2);
    ASSERT_EQ(stat.data_size, block1_size + block2_size);
  }
}

TEST_F(NamespaceStatTest, TestOverwriteFile) {
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);
  AddFile(file1, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  block_id = AddBlock(file1);
  block1_size = 128 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestAppendFile) {
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  AppendFile(file1);
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  block1_size = 128 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestCloseFile) {
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  CloseFile(file1);

  INodeStat stat;
  auto s = ns_->GetDirectoryStat("/", &stat);
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(stat.inode_num, 1);
  ASSERT_EQ(stat.file_num, 1);
  ASSERT_EQ(stat.dir_num, 0);
  ASSERT_EQ(stat.block_num, 1);
  ASSERT_EQ(stat.data_size, block1_size);
}

TEST_F(NamespaceStatTest, TestDeleteFile) {
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  std::string file2("/a_file_2");
  AddFile(file2);
  block_id = AddBlock(file2);
  uint64_t block2_size = 128 * 1024 * 1024;
  UpdateBlock(file2, block_id, block2_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 2);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 2);
    ASSERT_EQ(stat.data_size, block1_size + block2_size);
  }

  Delete(file2);
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(file2, &path_components);
    INode file;
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file),
            StatusCode::kFileNotFound);
  }

  Delete(file1);
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);

    std::vector<cnetpp::base::StringPiece> path_components;
    SplitPath(file1, &path_components);
    INode file;
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file),
            StatusCode::kFileNotFound);
  }
}

TEST_F(NamespaceStatTest, TestMkdirDeleteDir) {
  std::string dir1("/dir_1");
  Mkdir(dir1);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  INode dir1_inode;
  auto s = ns_->GetINodeByPath(dir1, &dir1_inode);
  ASSERT_TRUE(s.IsOK());

  Delete(dir1);
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_EQ(s.code(), Code::kFileNotFound);

    s = ns_->TestOnlyGetMetaStorage()->GetDirectoryINodeStat(
        dir1_inode.id(), &stat);
    ASSERT_EQ(s.code(), Code::kINodeStatNotFound);
  }
}

TEST_F(NamespaceStatTest, TestRenameFileOld) {

  // Rename file under root
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  std::string file2 ("/a_file_2");
  RenameOld(file1, file2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameFileOld2) {

  // Rename file under level1 directory
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string file2 (dir2 + "/a_file_2");
  RenameOld(file1, file2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameFile) {
  // Rename file under root
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string file2 ("/a_file_2");
  RenameWithOverwrite(file1, file2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameFile2) {

  // Rename file under level1 directory
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string file2 (dir2 + "/a_file_2");
  RenameWithOverwrite(file1, file2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameFileOverwrite) {
  // Rename file under root
  std::string file1("/a_file_1");
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string file2 ("/a_file_2");
  AddFile(file2);
  uint64_t block2_id = AddBlock(file2);
  uint64_t block2_size = 128 * 1024 * 1024;
  UpdateBlock(file2, block2_id, block2_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 2);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 2);
    ASSERT_EQ(stat.data_size, block1_size + block2_size);
  }

  RenameWithOverwrite(file1, file2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameFileOverwrite2) {
  // Rename file under level1 directory
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string file2 = dir2 + "/file_2";
  AddFile(file2);
  uint64_t block2_id = AddBlock(file2);
  uint64_t block2_size = 128 * 1024 * 1024;
  UpdateBlock(file2, block2_id, block2_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 2);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 2);
    ASSERT_EQ(stat.data_size, block1_size + block2_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block2_size);
  }

  RenameWithOverwrite(file1, file2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOld) {
  // Rename empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string dir2("/dir_2");
  RenameOld(dir1, dir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOld2) {

  // Rename non-empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  std::string dir2("/dir_2");
  RenameOld(dir1, dir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOld3) {
  // Rename empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string dir2_subdir2(dir2 + "/subdir_2");
  RenameOld(dir1_subdir1, dir2_subdir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOld4) {
  // Rename non-empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  std::string file1 = dir1_subdir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  std::string dir2_subdir2(dir2 + "/subdir_2");
  RenameOld(dir1_subdir1, dir2_subdir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDir) {
  // Rename empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string dir2("/dir_2");
  RenameWithOverwrite(dir1, dir2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDir2) {

  // Rename non-empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  std::string dir2("/dir_2");
  RenameWithOverwrite(dir1, dir2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDir3) {
  // Rename empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  std::string dir2_subdir2(dir2 + "/subdir_2");
  RenameWithOverwrite(dir1_subdir1, dir2_subdir2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDir4) {
  // Rename non-empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  std::string file1 = dir1_subdir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }

  std::string dir2_subdir2(dir2 + "/subdir_2");
  RenameWithOverwrite(dir1_subdir1, dir2_subdir2, false);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOverwrite) {
  // Rename empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  RenameWithOverwrite(dir1, dir2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_FALSE(s.IsOK());
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOverwrite2) {

  // Rename non-empty dir under root
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string file1 = dir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string dir2("/dir_2");
  Mkdir(dir2);
  // dir2 must be empty for rename overwrite target

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 2);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  RenameWithOverwrite(dir1, dir2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_FALSE(s.IsOK());
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOverwrite3) {
  // Rename empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);
  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  std::string dir2("/dir_2");
  Mkdir(dir2);
  std::string dir2_subdir2(dir2 + "/subdir_2");
  Mkdir(dir2_subdir2);


  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 4);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  RenameWithOverwrite(dir1_subdir1, dir2_subdir2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 3);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
}

TEST_F(NamespaceStatTest, TestRenameDirOverwrite4) {
  // Rename non-empty dir under level1 dir
  std::string dir1("/dir_1");
  Mkdir(dir1);

  std::string dir1_subdir1(dir1 + "/subdir_1");
  Mkdir(dir1_subdir1);

  std::string file1 = dir1_subdir1 + "/file_1";
  AddFile(file1);
  uint64_t block1_id = AddBlock(file1);
  uint64_t block1_size = 64 * 1024 * 1024;
  UpdateBlock(file1, block1_id, block1_size);

  std::string dir2("/dir_2");
  Mkdir(dir2);
  std::string dir2_subdir2(dir2 + "/subdir_2");
  Mkdir(dir2_subdir2);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 5);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 4);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1_subdir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  RenameWithOverwrite(dir1_subdir1, dir2_subdir2, true);

  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat("/", &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 4);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 3);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir1, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 0);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 2);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
  {
    INodeStat stat;
    auto s = ns_->GetDirectoryStat(dir2_subdir2, &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_num, 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.dir_num, 0);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, block1_size);
  }
}

TEST_F(NamespaceStatTest, TestScrubCheck) {
  CreateINodes(3, 10, 5);

  auto meta_storage = ns_->TestOnlyGetMetaStorage();
  INode root = meta_storage->GetRootINode();

  {
    NameSpaceScrub scrub(root,
                         SCRUB_OPTYPE_INODE_STAT,
                         SCRUB_ACTION_CHECK,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    auto stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 6110);
    ASSERT_EQ(stat.dir_num, kNumReservedINode + 1110);
    ASSERT_EQ(stat.file_num, 5000);
    ASSERT_EQ(stat.block_num, 5000);
    ASSERT_EQ(stat.data_size, 5000UL * 128 * 1024 * 1024);
  }
  ASSERT_EQ(ScrubTask::GetObjectCount(), 0);
  ASSERT_EQ(DirScrubInfo::GetObjectCount(), 0);
}

TEST_F(NamespaceStatTest, TestScrubOverwrite) {
  FLAGS_dancenn_observe_mode_on = false;
  CreateINodes(3, 10, 5);
  FLAGS_dancenn_observe_mode_on = true;

  auto meta_storage = ns_->TestOnlyGetMetaStorage();
  INode root = meta_storage->GetRootINode();
  {
    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    ASSERT_FALSE(s.IsOK());
  }

  {
    NameSpaceScrub scrub(root,
                         SCRUB_OPTYPE_INODE_STAT,
                         SCRUB_ACTION_FORCE_OVERWRITE,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    auto stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 6110);
    ASSERT_EQ(stat.dir_num, kNumReservedINode + 1110);
    ASSERT_EQ(stat.file_num, 5000);
    ASSERT_EQ(stat.block_num, 5000);
    ASSERT_EQ(stat.data_size, 5000UL * 128 * 1024 * 1024);
  }
  {
    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 6110);
    ASSERT_EQ(stat.dir_num, kNumReservedINode + 1110);
    ASSERT_EQ(stat.file_num, 5000);
    ASSERT_EQ(stat.block_num, 5000);
    ASSERT_EQ(stat.data_size, 5000UL * 128 * 1024 * 1024);
  }
  ASSERT_EQ(ScrubTask::GetObjectCount(), 0);
  ASSERT_EQ(DirScrubInfo::GetObjectCount(), 0);
}

TEST_F(NamespaceStatTest, TestScrubBig) {
  auto meta_storage = ns_->TestOnlyGetMetaStorage();
  INode root = meta_storage->GetRootINode();
  ScrubOpType optype = SCRUB_OPTYPE_INODE_STAT;

  {
    NameSpaceScrub scrub(root,
                         optype,
                         SCRUB_ACTION_FORCE_OVERWRITE,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    auto result_stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_TRUE(stat == result_stat);
    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode);
    ASSERT_EQ(stat.dir_num, kNumReservedINode);
    ASSERT_EQ(stat.file_num, 0);
    ASSERT_EQ(stat.block_num, 0);
    ASSERT_EQ(stat.data_size, 0);
  }

  {
    NameSpaceScrub scrub(root,
                         optype,
                         SCRUB_ACTION_CHECK,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);
  }

  int64_t total_size = 0;
  const int64_t kBlockSize = 128 * 1024 * 1024;
  {
    std::string file0 = "/file_0";
    AddFile(file0);
    uint64_t block_id = AddBlock(file0);
    UpdateBlock(file0, block_id, kBlockSize);
    total_size += kBlockSize;

    NameSpaceScrub scrub(root,
                         optype,
                         SCRUB_ACTION_CHECK,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    auto result_stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_TRUE(stat == result_stat);
    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 1);
    ASSERT_EQ(stat.dir_num, kNumReservedINode);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, total_size);
  }
  {
    std::string dir0 = "/dir_0";
    Mkdir(dir0);
    NameSpaceScrub scrub(root,
                         optype,
                         SCRUB_ACTION_CHECK,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    auto result_stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_TRUE(stat == result_stat);
    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 2);
    ASSERT_EQ(stat.dir_num, kNumReservedINode + 1);
    ASSERT_EQ(stat.file_num, 1);
    ASSERT_EQ(stat.block_num, 1);
    ASSERT_EQ(stat.data_size, total_size);
  }

  {
    CreateINodes(3, 10, 5, kBlockSize, true);
    int64_t inode_num = 15 + 10 * 15 + 10 * 10 * 15 + 10 * 10 * 10 * 5;
    int64_t dir_num = 10 + 10 * 10 + 10 * 10 * 10;
    int64_t file_num = 5 + 10 * 5 + 10 * 10 * 5 + 10 * 10 * 10 * 5;
    int64_t block_num = file_num;
    int64_t data_size = file_num * kBlockSize;
    total_size += data_size;

    NameSpaceScrub scrub(root,
                         optype,
                         SCRUB_ACTION_CHECK,
                         ns_.get(),
                         meta_storage);
    scrub.Start();
    bool done = scrub.WaitForDone();
    EXPECT_TRUE(done);
    ASSERT_EQ(scrub.GetState(), NameSpaceScrub::SCRUB_STATE_FINISHED);

    INodeStat stat;
    auto s = meta_storage->GetDirectoryINodeStat(root.id(), &stat);
    auto result_stat = std::dynamic_pointer_cast<INodeStatScrubOp>(
        scrub.GetResult())->GetComputedStat();
    LOG(INFO) << "Stat: " << stat.ToString();

    ASSERT_TRUE(stat == result_stat);
    ASSERT_EQ(stat.inode_id, kRootINodeId);
    ASSERT_EQ(stat.inode_num, kNumReservedINode + 2 + inode_num);
    ASSERT_EQ(stat.dir_num, kNumReservedINode + 1 + dir_num);
    ASSERT_EQ(stat.file_num, 1 + file_num);
    ASSERT_EQ(stat.block_num, 1 + block_num);
    ASSERT_EQ(stat.data_size, total_size);
  }
  ASSERT_EQ(ScrubTask::GetObjectCount(), 0);
  ASSERT_EQ(DirScrubInfo::GetObjectCount(), 0);

  // Test scrub runner
  {
    NameSpaceScrubRunner runner;
    std::shared_ptr<MetaStorage> ms = ns_->TestOnlyGetMetaStorage();
    INode root = ms->GetRootINode();
    runner.Start();
    {
      runner.StartScrub(optype, SCRUB_ACTION_CHECK, root, ns_.get(), ms);
      ScrubProgress p;
      auto s = runner.GetScrubProgress(optype, &p);
      ASSERT_TRUE(s.IsOK());
      std::cerr << "Scrub progress: " << p.ToString() << std::endl;
      while (s.IsOK()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        s = runner.GetScrubProgress(optype, &p);
        std::cerr << "Scrub progress: " << p.ToString() << std::endl;
      }
      ScrubResult r;
      s = runner.GetScrubResult(optype, &r);
      ASSERT_TRUE(s.IsOK());
      std::cerr << "Scrub result: " << r.ToString() << std::endl;
    }

    {
      runner.StartScrub(optype, SCRUB_ACTION_FORCE_OVERWRITE, root, ns_.get(), ms);
      ScrubProgress p;
      auto s = runner.GetScrubProgress(optype, &p);
      ASSERT_TRUE(s.IsOK());
      std::cerr << "Scrub progress: " << p.ToString() << std::endl;
      while (s.IsOK()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        s = runner.GetScrubProgress(optype, &p);
        std::cerr << "Scrub progress: " << p.ToString() << std::endl;
      }
      ScrubResult r;
      s = runner.GetScrubResult(optype, &r);
      ASSERT_TRUE(s.IsOK());
      std::cerr << "Scrub result: " << r.ToString() << std::endl;
    }

    runner.StartScrub(optype, SCRUB_ACTION_CHECK, root, ns_.get(), ms);
    runner.Stop();
  }

  // Test usage reporter
  // {
  //   cnetpp::base::LOG.set_level(cnetpp::base::Log::Level::kDebug);
  //   FLAGS_usage_report_enable = true;
  //   FLAGS_usage_service_endpoint = "http://localhost:8090";
  //   FLAGS_usage_report_interval_seconds = 10;
  //   ns_->TEST_StartUsageReporterInObserveMode();
  //   std::this_thread::sleep_for(std::chrono::seconds(600));
  //   FLAGS_usage_service_endpoint = "";
  //   FLAGS_usage_report_enable = false;
  //   FLAGS_usage_report_interval_seconds = 600;
  // }
}

TEST_F(NamespaceStatTest, TestScrubStartStopEmpty) {
  auto ms = ns_->TestOnlyGetMetaStorage();
  INode root = ms->GetRootINode();

  for (int i = 0; i < 100; ++i) {
    LOG(INFO) << "==================================== Round " << i << " ===============================================";
    NameSpaceScrubRunner runner;
    runner.Start();
    runner.StartScrub(SCRUB_OPTYPE_INODE_STAT, SCRUB_ACTION_CHECK, root, ns_.get(), ms);
    runner.Stop();
  }
}
TEST_F(NamespaceStatTest, TestScrubStartStopNonEmpty) {
  auto ms = ns_->TestOnlyGetMetaStorage();
  INode root = ms->GetRootINode();

  CreateINodes(2, 2, 2, kBlockSize, true);

  for (int i = 0; i < 100; ++i) {
    LOG(INFO) << "==================================== Round " << i << " ===============================================";
    NameSpaceScrubRunner runner;
    runner.Start();
    runner.StartScrub(SCRUB_OPTYPE_INODE_STAT, SCRUB_ACTION_CHECK, root, ns_.get(), ms);
    runner.Stop();
  }
}

} // namespace dancenn
