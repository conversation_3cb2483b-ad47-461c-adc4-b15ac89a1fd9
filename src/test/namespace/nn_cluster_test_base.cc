
#include <glog/logging.h>
#include <gtest/gtest.h>

#include "test/namespace/nn_cluster_test_base.h"
#include "namespace/namespace_stat.h"
#include "namespace/meta_storage_util.h"

namespace dancenn {

void NnClusterTest::WaitSynced() {
  uint64_t active_txid = ns_->GetLastCkptTxId();
  uint64_t standby_txid = standby_->GetLastCkptTxId();
  int cost_time_ms = 0;
  constexpr int kInterval = 1000;
  constexpr int kTimeout = 120000;
  while (standby_txid < active_txid) {
    std::this_thread::sleep_for(std::chrono::milliseconds(kInterval));
    cost_time_ms += kInterval;
    active_txid = ns_->GetLastCkptTxId();
    standby_txid = standby_->GetLastCkptTxId();
    LOG(INFO) << absl::Substitute(
        "WaitSynced() cost_time_ms: $0, active_txid = $1, standby_txid = $2",
        cost_time_ms, active_txid, standby_txid);
    if (cost_time_ms > kTimeout) {
      LOG(FATAL) << absl::Substitute(
          "WaitSynced() takes too long! "
          "cost_time_ms: $0, active_txid = $1, standby_txid = $2",
          cost_time_ms, active_txid, standby_txid);
    }
  }
}

bool NnClusterTest::CheckDBConsistency() {
  bool consistent = db_comptr_->CheckDBConsistency();
  if (!consistent) {
    LOG(ERROR) << absl::StrFormat(
        "Found DB inconsistency between active: %s, standby: %s",
        db_path_, standby_path_);
  }
  return consistent;
}

void NnClusterTest::EnableINodeStatConsistency() {
  WaitSynced();

  // XXX make Standby NN apply editlogs like Observer
  FLAGS_dancenn_observe_mode_on = true;
  inode_stat_enabled_ = true;

  auto standby_ms = ns_->TestOnlyGetMetaStorage();
  INode standby_root_inode = standby_ms->GetRootINode();
  NameSpaceScrub standby_scrub(standby_root_inode,
                               SCRUB_OPTYPE_INODE_STAT,
                               SCRUB_ACTION_FORCE_OVERWRITE,
                               ns_.get(),
                               standby_ms);
  standby_scrub.Start();
  standby_scrub.WaitForDone();
}

bool NnClusterTest::CheckINodeStatConsistency() {
  WaitSynced();

  // XXX expect Standby NN apply editlogs like Observer
  CHECK(inode_stat_enabled_);

  // 1. start a scrub to check data consistency of Standby
  auto standby_ms = standby_->TestOnlyGetMetaStorage();
  INode standby_root_inode = standby_ms->GetRootINode();
  NameSpaceScrub standby_scrub(standby_root_inode,
                               SCRUB_OPTYPE_INODE_STAT,
                               SCRUB_ACTION_CHECK,
                               standby_.get(),
                               standby_ms);
  standby_scrub.Start();
  standby_scrub.WaitForDone();

  // 2. check consistency between Active-Standby
  auto active_ms = ns_->TestOnlyGetMetaStorage();
  INode active_root_inode = active_ms->GetRootINode();
  NameSpaceScrub active_scrub(active_root_inode,
                              SCRUB_OPTYPE_INODE_STAT,
                              SCRUB_ACTION_FORCE_OVERWRITE,
                              ns_.get(),
                              active_ms);
  active_scrub.Start();
  active_scrub.WaitForDone();

  INodeStat active_stat, standby_stat;
  Status st = active_ms->GetDirectoryINodeStat(active_root_inode.id(), &active_stat);
  CHECK(st.IsOK());
  st = standby_ms->GetDirectoryINodeStat(standby_root_inode.id(), &standby_stat);
  CHECK(st.IsOK());
  if (active_stat != standby_stat) {
    LOG(ERROR) << absl::StrFormat(
      "INodeStat mismatched, Active: %s, Standby: %s",
      active_stat.ToString(), standby_stat.ToString());
    return false;
  }
  return true;
}

void NnClusterTest::DisableINodeStatConsistency() {
  WaitSynced();

  // XXX to deconstruct modules properly, for example, BlockReportManager
  inode_stat_enabled_ = false;
  FLAGS_dancenn_observe_mode_on = false;
}

}  // namespace dancenn
