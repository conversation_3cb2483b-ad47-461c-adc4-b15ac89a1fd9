//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#include <gtest/gtest.h>

#include <algorithm>

#include "base/constants.h"
#include "namespace/replica_policy_cache.h"

namespace dancenn {

class ReplicaPolicyCacheTest : public testing::Test {
 public:
  void SetUp() override {
  }

  void TearDown() override {
  }
};


TEST_F(ReplicaPolicyCacheTest, Test01) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    ASSERT_EQ(paths.size(), 0);
  }
  {
    int policy_id; std::string policy_dc;
    cache->GetParentOrSelf("/a/b/c", &policy_id, &policy_dc);
    ASSERT_EQ(policy_id, kCentralizePolicy);
    ASSERT_EQ(policy_dc, kEnforceDCUnspecified);
  }
  {
    int policy_id; std::string policy_dc;
    cache->UpdateChildOrSelf("/a/b/c", 1, "a");
    cache->GetParentOrSelf("/a/b/c", &policy_id, &policy_dc);
    ASSERT_EQ(policy_id, 1);
    ASSERT_EQ(policy_dc, "a");
    cache->GetParentOrSelf("/a/b/c/d", &policy_id, &policy_dc);
    ASSERT_EQ(policy_id, 1);
    ASSERT_EQ(policy_dc, "a");

    {
      auto paths = cache->Filter([](const std::string& path,
            int id, const std::string& dc) { return true; });
      ASSERT_EQ(paths.size(), 1);
      ASSERT_EQ(paths[0], "/a/b/c");
    }
    {
      auto paths = cache->Filter([](const std::string& path,
            int id, const std::string& dc) { return false; });
      ASSERT_EQ(paths.size(), 0);
    }
  }
}

TEST_F(ReplicaPolicyCacheTest, Test02) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/", 1, "11");
    cache->UpdateChildOrSelf("/a/b", 2, "22");
    cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b/c/d");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/g");
  }

  // cache->UpdateChildOrSelf("/", 1, "11");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }

  // cache->UpdateChildOrSelf("/a/b", 2, "22");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/d", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g/h", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test03) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/", 1, "11");
    cache->UpdateChildOrSelf("/a/b", 2, "22");
    cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b/c/d");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/g");
  }

  {
    cache->UpdateChildOrSelf("/z", 10, "1010");
    int id; std::string dc;
    cache->GetParentOrSelf("/z", &id, &dc);
    ASSERT_EQ(id, 10);
    ASSERT_EQ(dc, "1010");
  }

  // cache->UpdateChildOrSelf("/", 1, "11");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }

  // cache->UpdateChildOrSelf("/a/b", 2, "22");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/d", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g/h", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test04) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/", 1, "11");
    cache->UpdateChildOrSelf("/a/b", 2, "22");
    cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b/c/d");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/g");
  }

  {
    cache->UpdateChildOrSelf("/z", 10, "1010");
    int id; std::string dc;
    cache->GetParentOrSelf("/z", &id, &dc);
    ASSERT_EQ(id, 10);
    ASSERT_EQ(dc, "1010");
  }

  {
    cache->UpdateChildOrSelf("/a", 11, "1111");
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  // cache->UpdateChildOrSelf("/", 1, "11");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  // cache->UpdateChildOrSelf("/a/b", 2, "22");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/d", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g/h", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test05) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/", 1, "11");
    cache->UpdateChildOrSelf("/a/b", 2, "22");
    cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b/c/d");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/g");
  }
  {
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 44, "444");
    cache->UpdateChildOrSelf("/a/b/c/d", 33, "333");
    cache->UpdateChildOrSelf("/a/b", 22, "222");
    cache->UpdateChildOrSelf("/", 11, "111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b", &id, &dc);
    ASSERT_EQ(id, 22);
    ASSERT_EQ(dc, "222");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
    ASSERT_EQ(id, 33);
    ASSERT_EQ(dc, "333");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g", &id, &dc);
    ASSERT_EQ(id, 44);
    ASSERT_EQ(dc, "444");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test06) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/", 1, "11");
    cache->UpdateChildOrSelf("/a/b", 2, "22");
    cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
  }

  {
    cache->UpdateChildOrSelf("/z", 10, "1010");
    int id; std::string dc;
    cache->GetParentOrSelf("/z", &id, &dc);
    ASSERT_EQ(id, 10);
    ASSERT_EQ(dc, "1010");
  }

  {
    cache->UpdateChildOrSelf("/a", 11, "1111");
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  {
    int id; std::string dc;
    cache->UpdateChildOrSelf("/a/b/c/d/e/f", 9999, "9999");
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 9999);
    ASSERT_EQ(dc, "9999");
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  {
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 6);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a");
    ASSERT_EQ(paths[2], "/a/b/c/d");
    ASSERT_EQ(paths[3], "/a/b/c/d/e/f");
    ASSERT_EQ(paths[4], "/a/b/c/d/e/f/g");
    ASSERT_EQ(paths[5], "/z");
  }

  // cache->UpdateChildOrSelf("/", 1, "11");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/b", &id, &dc);
    ASSERT_EQ(id, 1);
    ASSERT_EQ(dc, "11");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  // cache->UpdateChildOrSelf("/a/b", 2, "22");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/d", &id, &dc);
    ASSERT_EQ(id, 11);
    ASSERT_EQ(dc, "1111");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d", 3, "33");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
    ASSERT_EQ(id, 3);
    ASSERT_EQ(dc, "33");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 9999);
    ASSERT_EQ(dc, "9999");
  }

  // cache->UpdateChildOrSelf("/a/b/c/d/e/f/g", 4, "44");
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/g/h", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test07) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  {
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/i/g", 4, "44");
    cache->UpdateChildOrSelf("/a/b/c/d/e/f/l/g", 5, "55");
  }
  {
    cache->UpdateChildOrSelf("/a/b/c/d/e/f", 9999, "9999");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f", &id, &dc);
    ASSERT_EQ(id, 9999);
    ASSERT_EQ(dc, "9999");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/i/g", &id, &dc);
    ASSERT_EQ(id, 4);
    ASSERT_EQ(dc, "44");
  }
  {
    int id; std::string dc;
    cache->GetParentOrSelf("/a/b/c/d/e/f/l/g", &id, &dc);
    ASSERT_EQ(id, 5);
    ASSERT_EQ(dc, "55");
  }
  {
    auto paths = cache->Filter([](const std::string& path,
          int id, const std::string& dc) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/a/b/c/d/e/f");
    ASSERT_EQ(paths[1], "/a/b/c/d/e/f/i/g");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/l/g");
  }
}

TEST_F(ReplicaPolicyCacheTest, Test08) {
  auto cache = std::make_shared<ReplicaPolicyCache>();
  cache->UpdateChildOrSelf("/", 1, "11");
  cache->UpdateChildOrSelf("/a/b", 1, "22");
  cache->UpdateChildOrSelf("/a/b/c", 1, "33");
  cache->UpdateChildOrSelf("/a/b/c/d", 1, "44");

  int id; std::string dc;
  cache->GetParentOrSelf("/", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "11");

  cache->GetParentOrSelf("/a/b", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "22");

  cache->GetParentOrSelf("/a/b/c", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "33");

  cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "44");

  cache->DeleteNode("/a/b/c/d");
  cache->GetParentOrSelf("/a/b/c/d", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "33");

  cache->DeleteNode("/a/b/c");
  cache->DeleteNode("/a/b/c/d");
  cache->DeleteNode("/");

  cache->GetParentOrSelf("/", &id, &dc);
  ASSERT_EQ(id, 0);
  ASSERT_EQ(dc, "");

  cache->UpdateChildOrSelf("/", 1, "11");
  cache->UpdateChildOrSelf("/a", 1, "22");
  cache->UpdateChildOrSelf("/b", 1, "33");
  cache->UpdateChildOrSelf("/c", 1, "44");

  cache->DeleteNode("/");
  cache->GetParentOrSelf("/", &id, &dc);
  ASSERT_EQ(id, 0);
  ASSERT_EQ(dc, "");
  cache->GetParentOrSelf("/a", &id, &dc);
  ASSERT_EQ(id, 1);
  ASSERT_EQ(dc, "22");
}

}  // namespace dancenn

