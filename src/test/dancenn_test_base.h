// Copyright 2018 Ra<PERSON> <<EMAIL>>

#ifndef TEST_DANCENN_TEST_BASE_H_
#define TEST_DANCENN_TEST_BASE_H_

#include <gtest/gtest.h>
#include <libgen.h>

#include <sstream>
#include <fstream>
#include <string>

#include "base/platform.h"
#include "edit/edit_log_context.h"
#include "edit/sender_base.h"
#include "edit/sender.h"
#include "edit/deserializer.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"
#include "base/java.h"
#include "base/file_utils.h"
#include "test/edit/java_const.h"
#include "test/mock_edit_log_sync_listener.h"

namespace dancenn {
namespace test {

template<typename T>
T GetEnvDefault(std::string const &key, T const &def) {
  const char *sval = getenv(key.c_str());
  if (sval == nullptr) {
    return def;
  }

  T val;
  std::stringstream ss(sval);
  ss >> val;
  return val;
}

inline void VerifyInvocationFromParentDirectory() {
#if defined(OS_LINUX)
  std::ifstream fin("/proc/self/cmdline");
  std::string line;
  CHECK(std::getline(fin, line));
  std::stringstream ss(line);
  std::string cmd_path;
  CHECK(ss >> line);
  auto dir = dirname(&line[0]);
  CHECK_NOTNULL(dir);
  CHECK_LT(strlen(dir), line.size());
  std::string abs_path(512, 0);
  auto p = realpath(dir, &abs_path[0]);
  CHECK_NOTNULL(p);
  auto n  = strlen(p);
  CHECK_LE(n, abs_path.size());
  abs_path.resize(n);
  CHECK_EQ(abs_path, FileUtils::GetCwd());
  fin.close();
#endif
}

inline void CreateHdfsSiteXml(
  std::string const& conf_dir,
  std::string const& name_dir) {
  auto shared_edits_dir = name_dir;
  auto edits_dir = name_dir;

  std::string hdfs_site_xml = std::string()+
  "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
  "<?xml-stylesheet type=\"text/xsl\" href=\"configuration.xsl\"?>\n"
  "<configuration>\n"
  "    <property>\n"
  "        <name>fs.defaultFS</name>\n"
  "        <value>hdfs://test</value>\n"
  "    </property>\n"
  "    <property>\n"
  "        <name>dfs.name.dir</name>\n"
  "        <value>file://" + name_dir + "</value>\n"
  "        <final>true</final>\n"
  "    </property>\n"
  "    <property>\n"
  "        <name>dfs.namenode.shared.edits.dir</name>\n"
  "        <value>file://" + shared_edits_dir + "</value>\n"
  "    </property>\n"
  "    <property>\n"
  "        <name>dfs.namenode.edits.dir</name>\n"
  "        <value>file://" + edits_dir + "</value>\n"
  "    </property>\n"
  "      <property> \n"
  "          <name>dfs.nameservice.id</name>\n"
  "          <value>testbackend</value>\n"
  "       </property>\n"
  "       <property>\n"
  "           <name>dfs.ha.namenode.id</name>\n"
  "           <value>test1</value>\n"
  "        </property>\n"
  "      <property>\n"
  "          <name>dfs.ha.namenodes.testbackend</name>\n"
  "         <value>test1,test2</value>\n"
  "      </property>\n"
  "      <property>\n"
  "          <name>dfs.namenode.http-address.testbackend.test1</name>\n"
  "          <value>********:5070</value>\n"
  "      </property>\n"
  "      <property>\n"
  "          <name>dfs.namenode.http-address.testbackend.test2</name>\n"
  "         <value>********:5070</value>\n"
  "      </property>\n"
  "</configuration>";

  auto edit_log_dir = edits_dir +GTEST_PATH_SEP_ + "current";
  CHECK(FileUtils::Exists(edit_log_dir));
  auto conf_file = conf_dir + GTEST_PATH_SEP_ + "hdfs-site.xml";
  std::ofstream fout(conf_file);
  fout << hdfs_site_xml;
  CHECK(fout);
  fout.close();
  CHECK(FileUtils::Exists(conf_file));
}

inline std::shared_ptr<dancenn::JavaRuntime> CreateVMOnlyOnce() {
  static std::once_flag once;
  static std::shared_ptr<dancenn::JavaRuntime> jvm;
  std::call_once(once, [&] {
    if (jvm) return;
    std::string java_classpath =
        dancenn::test::GetEnvDefault<std::string>("CLASSPATH", "");
    CHECK(!java_classpath.empty());
    java_classpath = FileUtils::GetCwd() + ":" + java_classpath;

    int java_heap_size =
        dancenn::test::GetEnvDefault<int>("JAVA_HEAP_SIZE", 1000);

    jvm =
        std::make_shared<dancenn::JavaRuntime>(java_classpath, java_heap_size);
  });
  return jvm;
}

class DanceNNTestBase {
 public:
  using MetaStorage = dancenn::MetaStorage;
  using EditLogContextBase = dancenn::EditLogContextBase;
  using EditLogSenderBase = dancenn::EditLogSenderBase;
  using EditLogSender = dancenn::EditLogSender;
  using JavaRuntime = dancenn::JavaRuntime;
  using FileUtils = dancenn::FileUtils;

  typedef DanceNNTestBase* Self;

  DanceNNTestBase(std::string const& conf_dir,
    std::string db_dir,
    std::shared_ptr<JavaRuntime> jvm):
    jvm_(jvm), db_dir_(db_dir), conf_dir_(conf_dir) {
  }

  void SetUp() {
    txid_ = 0;
    // never launch unittest in other than its parent directory
    VerifyInvocationFromParentDirectory();

    auto cwd = FileUtils::GetCwd();

    MetaStorage::CreateRoot();
    meta_storage_ = std::make_unique<MetaStorage>(db_dir_);
    meta_storage_->Launch();

    CHECK(jvm_);

    edit_log_ctx_ = std::make_shared<dancenn::HAEditLogContext>(jvm_.get(), 1);
    edit_log_ctx_->SetLastAllocatedBlockId(0);
    edit_log_ctx_->SetLastGenerationStampV2(0);

    listener_ = std::make_shared<MockEditLogSyncListener>(0,
          [](std::atomic<int64_t>& last_txid, int64_t new_txid) {
      int64_t x = last_txid.fetch_add(1);
      CHECK_EQ(x + 1, new_txid);
    });
    auto x = std::static_pointer_cast<IEditLogSyncListener>(listener_);
    edit_log_ctx_->SetupSyncListener(x);

    edit_log_sender_ = std::make_shared<EditLogSender>(
      meta_storage_->GetLastCkptTxId(),
      edit_log_ctx_);
  }

  void TearDown() {
    edit_log_sender_.reset();
    edit_log_ctx_.reset();
    meta_storage_.reset();
  }

  int64_t IncrLastTxid(int n) {
    return listener_->IncrLastTxid(n);
  }

  std::shared_ptr<DanceNNTestBase> CreateCompanionBase(
    std::string const &suffix) {
    auto ya_dir = conf_dir_ + GTEST_PATH_SEP_ + suffix;
    auto ya_db_dir = ya_dir + GTEST_PATH_SEP_ + "db";
    auto ya_name_dir = conf_dir_ + GTEST_PATH_SEP_ + "ndata";

    if (FileUtils::Exists(ya_dir)) {
      CHECK(FileUtils::DeleteDirectoryRecursively(ya_dir));
    }
    CHECK(FileUtils::CreateDirectoryRecursively(ya_dir, S_IRWXU));

    dancenn::test::CreateHdfsSiteXml(conf_dir_, ya_name_dir);
    return std::make_shared<DanceNNTestBase>(conf_dir_, ya_db_dir, jvm_);
  }

  Self InitJournalsForWrite() {
    edit_log_ctx_->InitJournalsForWrite();
    CHECK_JVM_EXCEPTION(jvm_);
    return this;
  }

  Self OpenForWrite() {
    edit_log_ctx_->OpenForWrite();
    CHECK_JVM_EXCEPTION(jvm_);
    CHECK(edit_log_ctx_->IsOpenForWrite());
    return this;
  }

  Self OpenForRead() {
    edit_log_ctx_->OpenForRead();
    CHECK_JVM_EXCEPTION(jvm_);
    CHECK(edit_log_ctx_->IsOpenForRead());
    return this;
  }

  Self Close() {
    if (edit_log_ctx_->IsOpenForWrite()) {
      edit_log_ctx_->Close();
      CHECK_JVM_EXCEPTION(jvm_);
      listener_->IncrLastTxid(1); // OP_END
    } else {
      edit_log_ctx_->Close();
      CHECK_JVM_EXCEPTION(jvm_);
    }
    return this;
  }

  Self Sync() {
    edit_log_sender_->Sync(true);
    CHECK_JVM_EXCEPTION(jvm_);
    return this;
  }

  Self Roll() {
    edit_log_ctx_->RollEditLog();
    CHECK_JVM_EXCEPTION(jvm_);
    // listener_->IncrLastTxid(2); // OP_END OP_START
    return this;
  }

  Self Append(int entries) {
    long base_gs = edit_log_ctx_->GetLastGenerationStampV2();
    for (int e = 0; e < entries; ++e) {
      uint64_t gs = base_gs + e + 1;
      edit_log_sender_->LogSetGenerationStampV2(gs, false);
      CHECK_JVM_EXCEPTION(jvm_);
    }
    return this;
  }

  Self Replay(int64_t fromTxid) {
    long base_gs = edit_log_ctx_->GetLastGenerationStampV2();
    auto last_committed_txid = edit_log_ctx_->GetLastWrittenTxId();
    CHECK_JVM_EXCEPTION(jvm_);
    LOG(INFO)<< "== Replay edit logs from txid#"
      << fromTxid <<" to txid#" <<last_committed_txid;
    auto input_ctx = edit_log_ctx_->CreateInputContext(
      fromTxid, last_committed_txid, true);
    CHECK_JVM_EXCEPTION(jvm_);

    auto actual_gs = dancenn::kGrandfatherGenerationStamp;
    dancenn::OpDeSerializer decoder;
    while (true) {
      std::string op_entry;
      CHECK(input_ctx->ReadOp(&op_entry));
      CHECK_JVM_EXCEPTION(jvm_);
      // last op_entry
      if (op_entry.empty()) {
        actual_gs = base_gs;
        break;
      }
      auto gs_op = std::dynamic_pointer_cast<dancenn::OpSetGenstampV2>(
        decoder.Deserialize(op_entry));

      if (!gs_op) continue;
      actual_gs = gs_op->genStampV2();
      LOG(INFO) << " actual_gs=" <<actual_gs
                << " gs_op->genStampV2=" << gs_op->genStampV2();
    }
    CHECK_EQ(actual_gs, base_gs);
    return this;
  }

  std::shared_ptr<MetaStorage> GetMetaStorage() const {
    return meta_storage_;
  }

  std::shared_ptr<JavaRuntime> GetJVM() const {
    return jvm_;
  }

  std::shared_ptr<EditLogContextBase> GetEditLogContext() const {
    return edit_log_ctx_;
  }

  std::shared_ptr<EditLogSenderBase> GetEditLogSender() const {
    return edit_log_sender_;
  }

  uint64_t GetGenerationStamp()const {
    return edit_log_ctx_->GetLastGenerationStampV2();
  }

  std::string GetDBDir()const {
    return db_dir_;
  }

  std::string GetConfDir()const {
    return conf_dir_;
  }

  void Verify() {
    long base_gs = edit_log_ctx_->GetLastGenerationStampV2();
    CHECK_EQ(base_gs, edit_log_ctx_->GetLastGenerationStampV2());
  }

  int64_t Txid() {
    return txid_;
  }

 private:
  std::shared_ptr<MetaStorage> meta_storage_;
  std::shared_ptr<JavaRuntime> jvm_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<EditLogSenderBase> edit_log_sender_;
  std::shared_ptr<MockEditLogSyncListener> listener_;
  int64_t txid_;
  std::string db_dir_;
  std::string conf_dir_;
};

class DanceNNCommonTest: public testing::Test{
 public:
  using FileUtils = dancenn::FileUtils;
  using JavaRuntime = dancenn::JavaRuntime;
  using DanceNNTestBase = dancenn::test::DanceNNTestBase;

  void SetUp() override {
    auto ti = testing::UnitTest::GetInstance()->current_test_info();

    auto full_test_name = std::string() +
                          ti->test_case_name() + "." + ti->name();

    auto cwd = FileUtils::GetCwd();
    auto test_dir = cwd + GTEST_PATH_SEP_ + full_test_name + ".dir";
    auto db_dir = test_dir + GTEST_PATH_SEP_ + "db";
    auto name_dir = test_dir + GTEST_PATH_SEP_ + "ndata";
    auto edit_log_dir = name_dir + GTEST_PATH_SEP_ + "current";


    if (FileUtils::Exists(test_dir)) {
      CHECK(FileUtils::DeleteDirectoryRecursively(test_dir));
    }

    CHECK(FileUtils::CreateDirectoryRecursively(db_dir, S_IRWXU));
    CHECK(FileUtils::CreateDirectoryRecursively(edit_log_dir, S_IRWXU));
    jvm_ = dancenn::test::CreateVMOnlyOnce();
    dancenn::test::CreateHdfsSiteXml(cwd, name_dir);
    base_ = std::make_shared<dancenn::test::DanceNNTestBase>(
      test_dir, db_dir, jvm_);

    base_->SetUp();
    test_dir_ = test_dir;
  }

  void TearDown() override {
    base_->TearDown();
    jvm_->DetachCurrentThread();
    // CHECK(FileUtils::DeleteDirectoryRecursively(test_dir_));
  }

  std::shared_ptr<DanceNNTestBase> GetBase() {
    return base_;
  }

  std::string GetTestDir() {
    return test_dir_;
  }

  std::shared_ptr<JavaRuntime> GetJvm() {
    return jvm_;
  }

  void OpenCloseSync() {
    // sync after close stream
    GetBase()->InitJournalsForWrite()->Close()->Sync();
  }

  void OpenAppendRollClose(int n) {
    auto x = GetBase()
             ->InitJournalsForWrite()
             ->OpenForWrite()
             ->Append(n)
             ->Roll();
    // aysnc sync may led notify after close,
    // for testcase
    sleep(1);
    x->Close();
  }

  void OpenAppendSyncRollClose(int n) {
    auto x = GetBase()
             ->InitJournalsForWrite()
             ->OpenForWrite()
             ->Append(n)
             ->Sync()
             ->Roll();
    // aysnc sync may led notify after roll close,
    // for testcase
    sleep(1);
    x->Close();
  }

  void OpenReplayClose() {
    GetBase()
      ->OpenForRead()
      ->Replay(1)
      ->Close();
  }

 private:
  std::string test_dir_;
  std::shared_ptr<DanceNNTestBase> base_;
  std::shared_ptr<JavaRuntime>  jvm_;
};

}  // namespace test
}  // namespace dancenn

#endif  // TEST_DANCENN_TEST_BASE_H_
