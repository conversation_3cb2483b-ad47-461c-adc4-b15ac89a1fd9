// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include<fstream>
#include <limits>

#include "proxy/proxy_throttler.h"

// TODO(ruanjunbin): dancenn::Throttler has two implementations:
// 1. src/proxy/throttler.h
// 2. src/proxy/abstract_condition_checker.h
// It is very confused and dangerous, please correct it and recovery uts.

namespace dancenn {

class MockThrottler : public ProxyThrottler, public testing::Test {
 public:
  MockThrottler() : ProxyThrottler::ProxyThrottler(nullptr) {
  }

  explicit MockThrottler(std::shared_ptr<UpstreamManager> upstream_manager)
      : ProxyThrottler::ProxyThrottler(upstream_manager) {
  }

  void SetUp() override {
    std::unordered_map<std::string, TokenBucket> t;
    t.emplace("create", std::move(TokenBucket(0.1, 0.1)));
    token_buckets_.emplace("/user/test", std::move(t));
  }

  void TearDown() override {
  }
};

// TEST_F(MockThrottler, TestEnabled) {
//   enabled_ = false;
//   swch_ = Switch::kOn;
//   for (int i = 0; i < 100; ++i) {
//     auto res = CheckThrottle("/user/test", "create");
//     ASSERT_FALSE(res.HasException());
//   }
//   enabled_ = true;
//   auto res = CheckThrottle("/user/test", "create");
//   ASSERT_TRUE(res.HasException());
//   ASSERT_EQ(res.exception(), JavaExceptions::kStandbyException);
//   ASSERT_STREQ(res.message().c_str(),
//       "Path: /user/test is throttled with 0.100000 permits per second. "
//       "Please control access QPS");
// }

// TEST_F(MockThrottler, TestSwitch) {
//   enabled_ = true;
//   swch_ = Switch::kOff;
//   for (int i = 0; i < 100; ++i) {
//     auto res = CheckThrottle("/user/test", "create");
//     ASSERT_FALSE(res.HasException());
//   }
//   swch_ = Switch::kDebug;
//   for (int i = 0; i < 100; ++i) {
//     auto res = CheckThrottle("/user/test", "create");
//     ASSERT_FALSE(res.HasException());
//   }
//   swch_ = Switch::kOn;
//   auto res = CheckThrottle("/user/test", "create");
//   ASSERT_TRUE(res.HasException());
//   ASSERT_EQ(res.exception(), JavaExceptions::kStandbyException);
//   ASSERT_STREQ(res.message().c_str(),
//       "Path: /user/test is throttled with 0.100000 permits per second. "
//       "Please control access QPS");

//   for (int i = 0; i < 100; ++i) {
//     auto res = CheckThrottle("/user1/test", "create");
//     ASSERT_FALSE(res.HasException());
//   }
// }

// TEST_F(MockThrottler, TestThrottle) {
//   enabled_ = true;
//   swch_ = Switch::kOn;
//   const int kCount = 1000;
//   int passed = 0;
//   for (int i = 0; i < kCount; ++i) {
//     auto res = CheckThrottle("/user/test/file", "create");
//     if (!res.HasException()) {
//       passed++;
//     }
//     res = CheckThrottle("/user/test", "create");
//     if (!res.HasException()) {
//       passed++;
//     }
//   }

//   // RateLimiter allows some sudden raise of query counts,
//   // so passCount may not be 0.
//   ASSERT_LE(passed, 2);

//   // should not block other dirs
//   for (int i = 0; i < kCount; ++i) {
//     auto res = CheckThrottle("/user/anotherTest", "create");
//     ASSERT_FALSE(res.HasException());
//   }

//   // should not block other rpc
//   for (int i = 0; i < kCount; ++i) {
//     auto res = CheckThrottle("/user/anotherTest", "append");
//     ASSERT_FALSE(res.HasException());
//   }

//   // throttle on multiple rpc
//   token_buckets_["/user/test"].clear();
//   token_buckets_["/user/test"].emplace("getFileInfo",
//                                       std::move(TokenBucket(0.1, 0.1)));
//   token_buckets_["/user/test"].emplace("create",
//                                       std::move(TokenBucket(0.1, 0.1)));
//   token_buckets_["/user/test"].emplace("append",
//                                       std::move(TokenBucket(0.1, 0.1)));
//   passed = 0;
//   for (int i = 0; i < kCount; ++i) {
//     auto res = CheckThrottle("/user/test", "create");
//     if (!res.HasException()) {
//       passed++;
//     }
//   }
//   ASSERT_LE(passed, 1);

//   // totally ok
//   token_buckets_["/user/test"].clear();
//   token_buckets_["/user/test"].emplace(
//       "create", std::move(TokenBucket(std::numeric_limits<double>::max(),
//                                       std::numeric_limits<double>::max())));
//   for (int i = 0; i < kCount; ++i) {
//     auto res = CheckThrottle("/user/test", "create");
//     ASSERT_FALSE(res.HasException());
//   }
// }

}  // namespace dancenn

