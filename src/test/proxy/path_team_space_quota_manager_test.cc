// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include<fstream>
#include "proxy/path_team_space_quota_manager.h"

DECLARE_int32(danceproxy_quota_on_start_hour_utc);
DECLARE_int32(danceproxy_quota_on_end_hour_utc);
DECLARE_string(danceproxy_quota_pattern_team_mapping_file);

namespace dancenn {

class MockPathTeamSpaceQuotaManager : public PathTeamSpaceQuotaManager,
                                      public testing::Test {
 public:
  MockPathTeamSpaceQuotaManager()
      : PathTeamSpaceQuotaManager::PathTeamSpaceQuotaManager(nullptr) {
  }

  explicit MockPathTeamSpaceQuotaManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
  : PathTeamSpaceQuotaManager::PathTeamSpaceQuotaManager(upstream_manager) {
  }

  void SetUp() override {
    path_to_team_.emplace("/a/b/c", "team1");
    path_to_team_.emplace("/a/b/c/d", "team2");
    path_to_team_.emplace("/e/f/g", "team3");
    path_to_team_.emplace("/user/tiger/data", "team2");
    path_to_team_.emplace("/user/tiger/data1", "team1");

    ssd_paths_.emplace("/user/tiger/data");
    ssd_paths_.emplace("/user/tiger/data1");
    ssd_paths_.emplace("/user/tiger/data2");

    team_to_quota_.emplace(kDefault, QuotaOrUsage(20L, 10L));
    team_to_quota_.emplace("team1", QuotaOrUsage(20L, 10L));
    team_to_quota_.emplace("team2", QuotaOrUsage(20L, 10L));
    team_to_quota_.emplace("team3", QuotaOrUsage(20L, 10L));
    team_to_quota_.emplace("team4", QuotaOrUsage(20L, 10L));

    team_to_usage_.emplace(kDefault, QuotaOrUsage(20L, 5L));
    team_to_usage_.emplace("team1", QuotaOrUsage(10L, 10L));
    team_to_usage_.emplace("team2", QuotaOrUsage(20L, 6L));
    team_to_usage_.emplace("team3", QuotaOrUsage(10L, 10L));
    team_to_usage_.emplace("team4", QuotaOrUsage(20L, 6L));
    swch_ = Switch::kOff;

    start_hour_backup = FLAGS_danceproxy_quota_on_start_hour_utc;
    end_hour_backup = FLAGS_danceproxy_quota_on_end_hour_utc;
    FLAGS_danceproxy_quota_pattern_team_mapping_file = "./pattern_team_mapping";
  }

  void TearDown() override {
    FLAGS_danceproxy_quota_on_start_hour_utc = start_hour_backup;
    FLAGS_danceproxy_quota_on_end_hour_utc = end_hour_backup;
    if (FileUtils::IsFile(
        FLAGS_danceproxy_quota_pattern_team_mapping_file)) {
          FileUtils::DeleteFile(
              FLAGS_danceproxy_quota_pattern_team_mapping_file);
    }
  }

  void TimeCheckPassed() {
    FLAGS_danceproxy_quota_on_start_hour_utc = 0;
    FLAGS_danceproxy_quota_on_end_hour_utc = 24;
  }
  void TimeCheckFailed() {
    FLAGS_danceproxy_quota_on_start_hour_utc = -1;
  }

  int32_t start_hour_backup = 0;
  int32_t end_hour_backup = 0;
};

TEST_F(MockPathTeamSpaceQuotaManager, TestTimeCheck) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  auto res = CheckSpace("/a/b/c/d/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::DSQuotaExceededException());
  ASSERT_EQ(std::get<2>(res),
            std::string("SATA space quota exceeded occurred, ") +
                "please remove some unused data, team: team2, path: " +
                "/a/b/c/d/test.txt, " + "quota: " +
                QuotaOrUsage(20, 10).SerializeToJsonString() +
                ", usage: " + QuotaOrUsage(20, 6).SerializeToJsonString());
  TimeCheckFailed();
  res = CheckSpace("/a/b/c/d/test.txt");
  ASSERT_TRUE(std::get<0>(res));
}

TEST_F(MockPathTeamSpaceQuotaManager, TestSwitchOff) {
  TimeCheckPassed();
  swch_ = Switch::kOff;
  auto res = CheckSpace("/a/b/c/d/test.txt");
  ASSERT_TRUE(std::get<0>(res));
}

TEST_F(MockPathTeamSpaceQuotaManager, TestBlackList) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  blacklist_teams_.clear();
  whitelist_teams_.clear();
  blacklist_teams_.emplace("team3");
  auto res = CheckSpace("/e/f/g/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::IOException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "Your team: team3 is in blacklist, for more information, "
                   "please refer to https://wiki.bytedance.net/pages/viewpage."
                   "action?pageId=92103943 or contact with administrator");
}

TEST_F(MockPathTeamSpaceQuotaManager, TestWhiteList) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  blacklist_teams_.clear();
  whitelist_teams_.clear();
  whitelist_teams_.emplace("team2");
  auto res = CheckSpace("/a/b/c/d/test.txt");
  ASSERT_TRUE(std::get<0>(res));
}

TEST_F(MockPathTeamSpaceQuotaManager, TestSataQuotaExceeded) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  blacklist_teams_.clear();
  whitelist_teams_.clear();
  auto res = CheckSpace("/a/b/c/test.txt");
  ASSERT_TRUE(std::get<0>(res));
  res = CheckSpace("/a/b/c/d/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::DSQuotaExceededException());
  ASSERT_EQ(std::get<2>(res),
            std::string("SATA space quota exceeded occurred, ") +
                "please remove some unused data, team: team2, path: " +
                "/a/b/c/d/test.txt, " + "quota: " +
                QuotaOrUsage(20, 10).SerializeToJsonString() +
                ", usage: " + QuotaOrUsage(20, 6).SerializeToJsonString());

  res = CheckSpace("/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::DSQuotaExceededException());
  ASSERT_EQ(std::get<2>(res),
            std::string("path: /test.txt is not claimed, please refer to "
                        "https://wiki.bytedance.net/pages/viewpage.action?"
                        "pageId=92103943"));
}

TEST_F(MockPathTeamSpaceQuotaManager, TestPatternTeamMapping) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  cnetpp::base::Array configs;
  cnetpp::base::Object config1;
  config1["team"] = "team4";
  config1["patternString"] = "/user/tiger/d4\\d*/.*log";
  configs.Append(cnetpp::base::Value(config1));

  cnetpp::base::Object config2;
  config2["team"] = "team4";
  config2["patternString"] = "/user/.*/g4/.*";
  configs.Append(cnetpp::base::Value(config2));

  cnetpp::base::Parser parser;
  std::string json;
  parser.Serialize(cnetpp::base::Value(std::move(configs)), &json);

  std::ofstream out(FLAGS_danceproxy_quota_pattern_team_mapping_file);
  out.write(&(json[0]), json.size());
  out.close();

  pattern_team_mappings_.clear();

  ASSERT_TRUE(RefreshMappingFile());
  ASSERT_EQ(pattern_team_mappings_.size(), 2);
  auto res = CheckSpace("/user/tiger/d44/haha.log");
  ASSERT_FALSE(std::get<0>(res));

  res = CheckSpace("/user/tiger/g4/1/2/3.log");
  ASSERT_FALSE(std::get<0>(res));
}

TEST_F(MockPathTeamSpaceQuotaManager, TestSSDQuotaExceeded) {
  TimeCheckPassed();
  swch_ = Switch::kOn;
  blacklist_teams_.clear();
  whitelist_teams_.clear();
  auto res = CheckSpace("/user/tiger/data/test.txt");
  ASSERT_TRUE(std::get<0>(res));
  res = CheckSpace("/user/tiger/data2/test.txt");
  ASSERT_TRUE(std::get<0>(res));
  res = CheckSpace("/user/tiger/data1/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::DSQuotaExceededException());
  ASSERT_EQ(std::get<2>(res),
            std::string("SSD space quota exceeded occurred, ") +
                "please remove some unused data, team: team1, path: " +
                "/user/tiger/data1/test.txt, " + "quota: " +
            QuotaOrUsage(20, 10).SerializeToJsonString() +
            ", usage: " + QuotaOrUsage(10, 10).SerializeToJsonString());
  team_to_usage_[kDefault] = QuotaOrUsage(20, 20);
  res = CheckSpace("/user/tiger/data2/test.txt");
  ASSERT_FALSE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::DSQuotaExceededException());
  ASSERT_EQ(std::get<2>(res),
            std::string("path: /user/tiger/data2/test.txt is not claimed, "
                        "please refer to https://wiki.bytedance.net/pages/"
                        "viewpage.action?pageId=92103943"));
}

}  // namespace dancenn

