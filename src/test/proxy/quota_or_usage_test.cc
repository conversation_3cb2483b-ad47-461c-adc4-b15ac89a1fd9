// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "proxy/quota_or_usage.h"

#include <gtest/gtest.h>

namespace dancenn {

TEST(QuotaOrUsageTest, Test01) {
  QuotaOrUsage qu1;
  ASSERT_EQ(qu1.sata(), 0);
  ASSERT_EQ(qu1.ssd(), 0);
  QuotaOrUsage qu(10, 15);
  ASSERT_EQ(qu.sata(), 10);
  ASSERT_EQ(qu.ssd(), 15);
  qu1.DeserializeFromJson(qu.SerializeToJsonString());
  ASSERT_EQ(qu1.sata(), 10);
  ASSERT_EQ(qu1.ssd(), 15);
  ASSERT_EQ(qu.sata(), 10);
  ASSERT_EQ(qu.ssd(), 15);
}

TEST(QuotaOrUsageTest, Merge) {
  QuotaOrUsage qu1(10, 15);
  QuotaOrUsage qu2(15, 20);
  qu1.Merge(qu2);
  ASSERT_EQ(qu1.sata(), 25);
  ASSERT_EQ(qu1.ssd(), 35);
}

}  // namespace dancenn

