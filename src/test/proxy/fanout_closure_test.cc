// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <google/protobuf/service.h>
#include <gtest/gtest.h>

#include <memory>
#include <vector>

#include "TestProtocol.pb.h"  // NOLINT(build/include)
#include "base/metric.h"
#include "base/metrics.h"
#include "proxy/fanout_closure.h"
#include "rpc/done_closure.h"

namespace dancenn {

namespace {

class MockDoneClosure : public DoneClosure {
 public:
  MockDoneClosure(std::unique_ptr<RpcController> &&controller)
      : DoneClosure(nullptr,
                    std::move(controller),
                    MethodMeta::MethodType::kNormal) {
  }
  ~MockDoneClosure() override = default;

  void Run() override {
    num_executed_++;
    if (rpc_controller_->status() != RpcStatus::kSuccess) {
      failed_ = true;
    }
  }

  int num_executed_ { 0 };
  bool failed_{false};
};

static std::shared_ptr<MethodMetrics> CreateMockMethodMetrics() {
  auto metrics = MetricsCenter::Instance()->RegisterMetrics("test_metrics");
  auto num_ops = metrics->RegisterCounter("num_ops.TestMethod");
  auto num_success_ops = metrics->RegisterCounter("num_success_ops.TestMethod");
  auto time = metrics->RegisterHistogram("time.TestMethod");
  auto success_time = metrics->RegisterHistogram("success_time.TestMethod");
  auto overall_time = metrics->RegisterHistogram("overall.time.TestMethod");
  auto overall_success_time = metrics->RegisterHistogram(
      "overall.success_time.TestMethod");
  return std::make_shared<MethodMetrics>(num_ops,
                                         num_success_ops,
                                         time,
                                         success_time,
                                         overall_time,
                                         overall_success_time);
}

}  // anonymous namespace

TEST(FanoutClosureTest, SuccessAndVoidResult) {
  const int N = 3;
  auto controller = std::make_unique<RpcController>();
  MethodTracer tracer(CreateMockMethodMetrics());
  auto done_closure = new MockDoneClosure(std::move(controller));
  auto tracer_closure = new MethodTracerClosure(
      done_closure->rpc_controller().get(),
      done_closure,
      std::move(tracer));
  std::vector<std::unique_ptr<RpcController>> sub_controllers;
  for (int i = 0; i < N; ++i) {
    sub_controllers.emplace_back(std::make_unique<RpcController>());
  }
  auto fanout_closure =
      new FanoutClosure(tracer_closure, std::move(sub_controllers));
  for (int i = 0; i < N; ++i) {
    fanout_closure->Run();
  }
  ASSERT_EQ(done_closure->num_executed_, 1);
  ASSERT_FALSE(done_closure->failed_);
}

TEST(FanoutClosureTest, FailedAndVoidResult) {
  const int N = 3;
  auto controller = std::make_unique<RpcController>();
  MethodTracer tracer(CreateMockMethodMetrics());
  auto done_closure = new MockDoneClosure(std::move(controller));
  std::vector<std::unique_ptr<RpcController>> sub_controllers;
  for (int i = 0; i < N; ++i) {
    sub_controllers.emplace_back(std::make_unique<RpcController>());
  }
  auto tracer_closure = new MethodTracerClosure(
      done_closure->rpc_controller().get(),
      done_closure,
      std::move(tracer));
  auto fanout_closure =
      new FanoutClosure(tracer_closure, std::move(sub_controllers));
  std::string error_text = "test illegal argument";
  for (int i = 0; i < N; ++i) {
    if (i == N - 1) {
      fanout_closure->GetController(i)->MarkAsFailed(
          JavaExceptions::IllegalArgumentException(), error_text);
    }
    fanout_closure->Run();
  }
  ASSERT_EQ(done_closure->num_executed_, 1);
  ASSERT_TRUE(done_closure->failed_);
  ASSERT_EQ(done_closure->rpc_controller()->status(), RpcStatus::kAppError);
  ASSERT_EQ(done_closure->rpc_controller()->Exception(),
            JavaExceptions::IllegalArgumentException());
  ASSERT_EQ(done_closure->rpc_controller()->ErrorText(), error_text);
}

TEST(FanoutClosureTest, SuccessAndMergeResult) {
  const int N = 3;
  auto controller = std::make_unique<RpcController>();
  MethodTracer tracer(CreateMockMethodMetrics());
  auto req = new cloudfs::EchoRequestProto();
  auto rsp = new cloudfs::EchoResponseProto();
  controller->set_request(req);
  controller->set_response(rsp);
  auto done_closure = new MockDoneClosure(std::move(controller));
  auto tracer_closure = new MethodTracerClosure(
      done_closure->rpc_controller().get(),
      done_closure,
      std::move(tracer));
  std::vector<std::unique_ptr<RpcController>> sub_controllers;
  std::string payload = "test";
  for (int i = 0; i < N; ++i) {
    auto sub_controller = std::make_unique<RpcController>();
    auto sub_rsp = new cloudfs::EchoResponseProto;
    sub_rsp->mutable_payload()->append(payload);
    sub_controller->set_response(sub_rsp);
    sub_controllers.emplace_back(std::move(sub_controller));
  }
  auto fanout_closure =
      new FanoutClosure(tracer_closure,
                        std::move(sub_controllers),
                        [] (google::protobuf::Message* r1,
                            google::protobuf::Message* r2) {
                          auto rs1 =
                              static_cast<cloudfs::EchoResponseProto*>(r1);
                          auto rs2 =
                              static_cast<cloudfs::EchoResponseProto*>(r2);
                          rs1->mutable_payload()->append(rs2->payload());
                        });
  std::string expected_result;
  for (int i = 0; i < N; ++i) {
    fanout_closure->Run();
    expected_result += payload;
  }
  ASSERT_EQ(done_closure->num_executed_, 1);
  ASSERT_FALSE(done_closure->failed_);
  ASSERT_EQ(done_closure->rpc_controller()->status(), RpcStatus::kSuccess);
  ASSERT_EQ(rsp->payload(), expected_result);
}

TEST(FanoutClosureTest, FailedAndMergeResult) {
  const int N = 3;
  auto controller = std::make_unique<RpcController>();
  MethodTracer tracer(CreateMockMethodMetrics());
  auto req = new cloudfs::EchoRequestProto();
  auto rsp = new cloudfs::EchoResponseProto();
  controller->set_request(req);
  controller->set_response(rsp);
  auto done_closure = new MockDoneClosure(std::move(controller));
  auto tracer_closure = new MethodTracerClosure(
      done_closure->rpc_controller().get(),
      done_closure,
      std::move(tracer));
  std::vector<std::unique_ptr<RpcController>> sub_controllers;
  std::string payload = "test";
  for (int i = 0; i < N; ++i) {
    auto sub_controller = std::make_unique<RpcController>();
    auto sub_rsp = new cloudfs::EchoResponseProto;
    sub_rsp->mutable_payload()->append(payload);
    sub_controller->set_response(sub_rsp);
    sub_controllers.emplace_back(std::move(sub_controller));
  }
  auto fanout_closure =
      new FanoutClosure(tracer_closure,
                        std::move(sub_controllers),
                        [] (google::protobuf::Message* r1,
                            google::protobuf::Message* r2) {
                          auto rs1 =
                              static_cast<cloudfs::EchoResponseProto*>(r1);
                          auto rs2 =
                              static_cast<cloudfs::EchoResponseProto*>(r2);
                          rs1->mutable_payload()->append(rs2->payload());
                        });
  std::string error_text = "test illegal argument";
  std::string expected_result;
  for (int i = 0; i < N; ++i) {
    if (i == N - 1) {
      fanout_closure->GetController(i)->MarkAsFailed(
          JavaExceptions::IllegalArgumentException(), error_text);
    }
    fanout_closure->Run();
  }
  ASSERT_EQ(done_closure->num_executed_, 1);
  ASSERT_TRUE(done_closure->failed_);
  ASSERT_EQ(done_closure->rpc_controller()->status(), RpcStatus::kAppError);
  ASSERT_EQ(done_closure->rpc_controller()->Exception(),
            JavaExceptions::IllegalArgumentException());
  ASSERT_EQ(done_closure->rpc_controller()->ErrorText(), error_text);
  ASSERT_EQ(rsp->payload(), expected_result);
}

}   // namespace dancenn

