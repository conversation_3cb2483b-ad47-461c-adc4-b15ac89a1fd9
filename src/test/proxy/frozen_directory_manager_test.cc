// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include "proxy/frozen_directory_manager.h"

namespace dancenn {

class MockFrozenDirectoryManager : public FrozenDirectoryManager,
                                   public testing::Test {
 public:
  MockFrozenDirectoryManager()
      : FrozenDirectoryManager::FrozenDirectoryManager(nullptr) {
  }

  explicit MockFrozenDirectoryManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : FrozenDirectoryManager::FrozenDirectoryManager(upstream_manager) {
  }

  void SetUp() override {
    swch_ = Switch::kOff;
    enabled_ = true;
    {
      std::unordered_map<std::string, std::unordered_set<std::string>> fds_ns;
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("a");
        sub_dirs.emplace("b");
        sub_dirs.emplace("c");
        fds_ns.emplace("/", std::move(sub_dirs));
      }
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("d");
        sub_dirs.emplace("e");
        sub_dirs.emplace("f");
        fds_ns.emplace("/a", std::move(sub_dirs));
      }
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("g");
        sub_dirs.emplace("h");
        sub_dirs.emplace("i");
        fds_ns.emplace("/f", std::move(sub_dirs));
      }
      frozen_directories_.emplace("hdfs://harunabackend", std::move(fds_ns));
    }
    {
      std::unordered_map<std::string, std::unordered_set<std::string>> fds_ns;
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("aa");
        sub_dirs.emplace("bb");
        sub_dirs.emplace("cc");
        fds_ns.emplace("/", std::move(sub_dirs));
      }
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("dd");
        sub_dirs.emplace("ee");
        sub_dirs.emplace("ff");
        fds_ns.emplace("/aa", std::move(sub_dirs));
      }
      {
        std::unordered_set<std::string> sub_dirs;
        sub_dirs.emplace("gg");
        sub_dirs.emplace("hh");
        sub_dirs.emplace("ii");
        fds_ns.emplace("/ff", std::move(sub_dirs));
      }
      frozen_directories_.emplace("hdfs://clojurebackend", std::move(fds_ns));
    }
  }

  void TearDown() override {}
};

TEST_F(MockFrozenDirectoryManager, TestSwitch) {
  swch_ = Switch::kOff;
  auto res = IsFrozen("hdfs://harunabackend", "/a/a", false);
  ASSERT_FALSE(std::get<0>(res));
  swch_ = Switch::kOn;
  res = IsFrozen("hdfs://harunabackend", "/a/a", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory /a is frozen, please change to another directory."
                   " For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
}

TEST_F(MockFrozenDirectoryManager, TestTrash) {
  swch_ = Switch::kOn;
  auto res = IsFrozen("hdfs://harunabackend", "/a/a", true);
  ASSERT_FALSE(std::get<0>(res));
}

TEST_F(MockFrozenDirectoryManager, TestRoot) {
  swch_ = Switch::kOn;
  auto res = IsFrozen("hdfs://rustbackend", "/", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://rustbackend", "/a", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://harunabackend", "/", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory / is frozen, please change to another directory. "
                   "For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
  res = IsFrozen("hdfs://clojurebackend", "/", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory / is frozen, please change to another directory. "
                   "For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
}

TEST_F(MockFrozenDirectoryManager, Test01) {
  swch_ = Switch::kOn;
  auto res = IsFrozen("hdfs://harunabackend", "/a", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://harunabackend", "/d", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  // we don't check the path its self
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory / is frozen, please change to another directory. "
                   "For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
  res = IsFrozen("hdfs://harunabackend", "/a/", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://harunabackend", "/d/", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory / is frozen, please change to another directory. "
                   "For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
  res = IsFrozen("hdfs://clojurebackend", "/aa/bb", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory /aa is frozen, please change to another "
                   "directory. For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
  res = IsFrozen("hdfs://clojurebackend", "/aa/dd/xx/", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://clojurebackend", "/aa/dd/xx/yy.txt", false);
  ASSERT_FALSE(std::get<0>(res));
  res = IsFrozen("hdfs://clojurebackend", "/aa/bb/xx/yy.txt", false);
  ASSERT_TRUE(std::get<0>(res));
  ASSERT_STREQ(std::get<1>(res), JavaExceptions::NSQuotaExceededException());
  ASSERT_STREQ(std::get<2>(res).c_str(),
               "The directory /aa is frozen, please change to another "
                   "directory. For more information, please refer to https://"
                   "wiki.bytedance.net/display/DATA/HDFS+Frozen+Directory "
                   "or contact administrators.");
}

}  // namespace dancenn

