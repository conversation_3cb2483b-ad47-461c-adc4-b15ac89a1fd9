// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include <string>
#include <vector>

#include "proxy/mount_entry.h"

namespace dancenn {

TEST(MountEntryTest, Test01) {
  MountEntry entry;
  entry.set_fs_uri("hdfs://namenode1");
  EXPECT_EQ("hdfs://namenode1", entry.fs_uri());
  entry.set_mount_point("/a/b/c");
  EXPECT_EQ("/a/b/c", entry.mount_point());
  std::vector<std::string> includes;
  includes.emplace_back("/[^/]+/[^/]+/[^/]+");
  entry.set_includes(includes);
  EXPECT_TRUE(entry.IsPathIncluded("/a/b/c"));
  EXPECT_FALSE(entry.IsPathIncluded("//ab/c"));
  std::vector<std::string> excludes;
  excludes.emplace_back("/[^/]+/[^/]+/[^/]+");
  entry.set_excludes(excludes);
  EXPECT_TRUE(entry.IsPathExcluded("/a/b/c"));
  EXPECT_FALSE(entry.IsPathExcluded("//ab/c"));
  entry.set_start(std::make_unique<std::string>(""));  // [unlimited, unlimited)
  EXPECT_TRUE(entry.IsPathInRange(""));
  EXPECT_TRUE(entry.IsPathInRange("a"));
  entry.set_end(std::make_unique<std::string>(""));  // [unlimited, unlimited)
  EXPECT_TRUE(entry.IsPathInRange(""));
  EXPECT_TRUE(entry.IsPathInRange("a"));
  entry.set_start(std::make_unique<std::string>("ab"));
  entry.set_end(std::make_unique<std::string>(""));  // ["ab", unlimited)
  EXPECT_FALSE(entry.IsPathInRange(""));
  EXPECT_FALSE(entry.IsPathInRange("a"));
  EXPECT_TRUE(entry.IsPathInRange("ab"));
  EXPECT_TRUE(entry.IsPathInRange("abc"));
  entry.set_end(std::make_unique<std::string>("abc"));  // ["ab", "abc")
  EXPECT_FALSE(entry.IsPathInRange(""));
  EXPECT_FALSE(entry.IsPathInRange("a"));
  EXPECT_TRUE(entry.IsPathInRange("ab"));
  EXPECT_FALSE(entry.IsPathInRange("abc"));
}

}  // namespace dancenn

