// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <cnetpp/base/string_utils.h>
#include <cnetpp/base/string_piece.h>
#include <zookeeper/zookeeper.h>

#include <string>
#include <vector>

#include "proxy/mounts_manager.h"
#include "base/defer.h"
#include "base/file_utils.h"
#include "dancenn_test_enviroment.h"

DECLARE_string(danceproxy_mount_table_zk_quorum);
DECLARE_string(danceproxy_mount_table_zk_path);
DECLARE_string(danceproxy_read_only_zk_path);
DECLARE_int32(danceproxy_zk_recv_timeout_ms);

namespace dancenn {

class MockMountsManager : public MountsManager, public testing::Test {
 public:
  void SetUp() override {
    after_install_running_ = true;
  }
  void TearDown() override {
  }

  std::string str_mount_table_;
  std::string str_read_only_paths_;
};

TEST_F(MockMountsManager, BasicTest) {
  bool is_trash = false;
  str_mount_table_ = "hdfs://namenode1 /\n";
  str_mount_table_.append("hdfs://namenode2 /oldclojure\n");
  str_mount_table_.append("hdfs://namenode3 /oldlisp\n");
  HandleMountTableChange(str_mount_table_, 0);
  EXPECT_EQ(Resolve("/", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/old/clojure/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/test1", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldlisp/test1", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/user/tiger/.Trash/Current/oldlisp", &is_trash),
            "hdfs://namenode3");
  EXPECT_TRUE(is_trash);
  EXPECT_EQ(Resolve("/user/tiger/.Trash/Current/oldlisp/test1", &is_trash),
            "hdfs://namenode3");
  EXPECT_TRUE(is_trash);
  EXPECT_EQ(Resolve("/user/tiger/.Trash/Current/oldlisp1", &is_trash),
            "hdfs://namenode1");
  EXPECT_TRUE(is_trash);
}

TEST_F(MockMountsManager, JsonMountTableTest) {
  bool is_trash = false;
  str_mount_table_ = "[{\"fsUri\":\"hdfs://namenode1\",\"mountPoint\":\"/\"},";
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode2\",\"mountPoint\":\"/oldclojure\"},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode3\",\"mountPoint\":\"/oldlisp\"}]");
  HandleMountTableChange(str_mount_table_, 0);
  EXPECT_EQ(Resolve("/", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/old/clojure/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/test1", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldlisp/test1", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
}

TEST_F(MockMountsManager, MixedMountTableTest) {
  bool is_trash = false;
  str_mount_table_ = "hdfs://namenode1 /\n";
  str_mount_table_.append("hdfs://namenode2 /oldclojure\n");
  str_mount_table_.append(
      "[{\"fsUri\":\"hdfs://namenode3\",\"mountPoint\":\"/oldlisp\"},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode4\",\"mountPoint\":\"/oldlisp/test1\"}]");

  HandleMountTableChange(str_mount_table_, 0);

  EXPECT_EQ(Resolve("/", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/old/clojure/test1", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/test1", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldlisp/test1", &is_trash), "hdfs://namenode4");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldlisp/test2", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
}

TEST_F(MockMountsManager, TestSegementedResolve) {
  bool is_trash = false;
  str_mount_table_ = "hdfs://namenode1 /\n";
  str_mount_table_.append(
      "[{\"fsUri\":\"hdfs://namenode2\",\"mountPoint\":\"/oldclojure\", "
          "\"includes\":[\"/[^/]+/[^/]*DUMP[^/]*TEMP[^/]*\","
          "\"/[^/]+/[^/]*DUMP[^/]*TEMP[^/]*/(.*)\"]},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode3\",\"mountPoint\":\"/oldclojure\","
          "\"excludes\":[\"/[^/]+/[^/]*DUMP[^/]*TEMP[^/]*\","
          "\"/[^/]+/[^/]*DUMP[^/]*TEMP[^/]*/(.*)\"]},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode4\",\"mountPoint\":\"/oldclojure/test1\","
          "\"excludes\":[\"/oldclojure/test1/[^/]*DUMP[^/]*TEMP(.*)\"]},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode4\",\"mountPoint\":\"/testtest/test1\"}]");

  HandleMountTableChange(str_mount_table_, 0);

  EXPECT_EQ(Resolve("", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/_DUMP_TEMPORARY", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/_DUMP_TEMPORARY/", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/_DUMP_TEMPORARY/abc", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/_DUMP_TEMPORARY/abc/def", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/test1/_DUMP_TEMPORARY/abc/def", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/test1/abc/def", &is_trash), "hdfs://namenode4");
  EXPECT_FALSE(is_trash);

  EXPECT_TRUE(IsMountPoint("/"));
  EXPECT_TRUE(IsMountPoint("/oldclojure"));
  EXPECT_FALSE(IsMountPoint("/oldclojure1"));
  EXPECT_TRUE(IsMountPoint("/oldclojure/test1"));
  EXPECT_FALSE(IsMountPoint("/oldclojure/test2"));
  EXPECT_FALSE(IsUnified(""));
  EXPECT_FALSE(IsUnified("/"));
  EXPECT_FALSE(IsUnified("/oldclojure"));
  EXPECT_TRUE(IsUnified("/oldclojure1"));
  EXPECT_FALSE(IsUnified("/testtest"));
  EXPECT_TRUE(IsUnified("/testtest2"));
  EXPECT_TRUE(IsUnified("/testtest/test1"));
  EXPECT_TRUE(IsUnified("/testtest/test2"));
}

TEST_F(MockMountsManager, TestRange) {
  bool is_trash = false;
  str_mount_table_ = "hdfs://namenode1 /\n";
  str_mount_table_.append(
      "[{\"fsUri\":\"hdfs://namenode2\",\"mountPoint\":\"/oldclojure\", "
          "\"includes\":[\"/oldclojure/[^/]*DUMP[^/]*TEMP(.*)\"],"
          "\"excludes\":[\"/oldclojure/2018(.*)\"],"
          "\"start\":\"/oldclojure/2017\"},");
  str_mount_table_.append(
      "{\"fsUri\":\"hdfs://namenode3\",\"mountPoint\":\"/oldclojure\"}]");

  HandleMountTableChange(str_mount_table_, 0);

  EXPECT_EQ(Resolve("/", &is_trash), "hdfs://namenode1");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/20171024", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/20181024", &is_trash), "hdfs://namenode3");
  EXPECT_FALSE(is_trash);
  EXPECT_EQ(Resolve("/oldclojure/_DUMP_TEMPORARY/cbd", &is_trash), "hdfs://namenode2");
  EXPECT_FALSE(is_trash);
}

TEST_F(MockMountsManager, TestReadOnly) {
  {
    str_read_only_paths_ = "\n/abc/de/fg\n\n/hij/k/lm/n\n/op/q/rst/uvw/xyz\n\n";
    HandleReadOnlyChange(str_read_only_paths_, 0);
    EXPECT_TRUE(IsReadOnly("/abc/de/fg/hii"));
    EXPECT_TRUE(IsReadOnly("/hij/k/lm/n/o"));
    EXPECT_TRUE(IsReadOnly("/op/q/rst/uvw/xyz"));
    EXPECT_TRUE(IsReadOnly("/op/q/rst/uvw/xyz/123"));
    EXPECT_FALSE(IsReadOnly("/abc/de/fgh"));
    EXPECT_FALSE(IsReadOnly("/abc/de"));
  }
  {
    str_read_only_paths_ = "\n/abc\n \n /def\n\n\n";
    HandleReadOnlyChange(str_read_only_paths_, 1);
    EXPECT_FALSE(IsReadOnly("/"));
    EXPECT_TRUE(IsReadOnly("/abc"));
    EXPECT_TRUE(IsReadOnly("/def/"));
    EXPECT_TRUE(IsReadOnly("/def/hehe"));
    EXPECT_FALSE(IsReadOnly("/abcd/hehe"));
    EXPECT_TRUE(IsReadOnly("/abc/hehe"));
  }
  {
    str_read_only_paths_ = "\n\n \n /   \n";
    HandleReadOnlyChange(str_read_only_paths_, 2);
    EXPECT_TRUE(IsReadOnly("/"));
    EXPECT_TRUE(IsReadOnly("/ssml"));
    EXPECT_TRUE(IsReadOnly("/ssml/"));
    EXPECT_TRUE(IsReadOnly("/ssml/hehe"));
    EXPECT_TRUE(IsReadOnly("/abcd/hehe"));
    EXPECT_TRUE(IsReadOnly("/abc/hehe"));
  }
}

TEST_F(MockMountsManager, TestRenameWhiteList) {
  {
    EXPECT_FALSE(InRenameWhiteList("/abc/de/fg/hii"));
    EXPECT_FALSE(InRenameWhiteList("/abc/de"));
  }
  {
    rename_dir_whitelist_.push_back("/abc/");
    EXPECT_TRUE(InRenameWhiteList("/abc/de/fg/hii"));
    EXPECT_FALSE(InRenameWhiteList("/abcde/hii"));
  }
  {
    rename_dir_whitelist_.push_back("/abc/");
    rename_dir_whitelist_.push_back("/de/fg/");
    EXPECT_TRUE(InRenameWhiteList("/abc/de/fg/hii"));
    EXPECT_FALSE(InRenameWhiteList("/abcde/hii"));
    EXPECT_FALSE(InRenameWhiteList("/de/fg/"));
    EXPECT_TRUE(InRenameWhiteList("/de/fg/hdf"));
    EXPECT_FALSE(InRenameWhiteList("/de/fg"));
  }
}

TEST_F(MockMountsManager, TestSampleData) {
  str_mount_table_ = "hdfs://harunabackend /\n";
  str_mount_table_ += "hdfs://clojurebackend /nn2\n"
      "hdfs://clojurebackend /user/tiger/testnn2\n"
      "hdfs://clojurebackend /ssd2\n"
      "hdfs://clojurebackend /data/kafka_dump\n"
      "hdfs://clojurebackend /data/arch\n"
      "hdfs://clojurebackend /data_stats/flag_files\n"
      "hdfs://clojurebackend /yarn/node-labels\n"
      "hdfs://clojurebackend /yarn/timeline\n"
      "hdfs://clojurebackend /user/hetianyi/nn2\n"
      "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_orc\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/impression_stats_daily_orc\n"
      "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
      "hdfs://clojurebackend /user/hetianyi/copytable\n"
      "hdfs://clojurebackend /hdfs/fsimage/haruna\n"
      "hdfs://clojurebackend /hbase2\n"
      "hdfs://clojurebackend /data/query_editor\n"
      "hdfs://clojurebackend /ss_ml/recommend/train_data/batch/proto_instance\n"
      "hdfs://clojurebackend /tmp/hadoop-yarn/staging\n"
      "hdfs://clojurebackend /tmp/hadoop-yarn/fail\n"
      "hdfs://clojurebackend /user/tiger/.sparkStaging\n"
      "hdfs://clojurebackend /tmp/hive-complexity\n"
      "hdfs://clojurebackend /user/tiger/warehouse/video_play_quality\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_play_quality\n"
      "hdfs://clojurebackend /user/tiger/warehouse/all_user_profiles_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/all_user_profiles_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily_json\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily_json\n"
      "hdfs://clojurebackend /data/parameter_server/model_dump\n"
      "hdfs://clojurebackend /spark/shuffle\n"
      "hdfs://clojurebackend /user/tiger/spark_history\n"
      "hdfs://clojurebackend /user/tiger/warehouse/group_profiles_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/group_profiles_daily\n"
      "hdfs://clojurebackend /yarn/checkpoint\n"
      "hdfs://clojurebackend /user/tiger/warehouse/server_refresh_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_refresh_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
      "hdfs://clojurebackend /user/tiger/spark_history_bak\n"
      "hdfs://clojurebackend /user/tiger/warehouse/appmonitor_etl_data_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/appmonitor_etl_data_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/server_impressions_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_impressions_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/video_impression_stats_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_impression_stats_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/client_impressions_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/client_impressions_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/tmp_video_impression_stats_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/tmp_video_impression_stats_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily\n"
      "hdfs://clojurebackend /model_dump\n"
      "hdfs://clojurebackend /recommend/model_dump\n"
      "hdfs://clojurebackend /user/tiger/warehouse/fact_snssdk_nginx_log_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/app_log_hourly\n"
      "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly\n"
      "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly_tmp\n"
      "hdfs://clojurebackend /user/tiger/warehouse/adx_dsp_rsp_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/scratch/adx_dsp_rsp_daily\n"
      "hdfs://clojurebackend /recommend/data/app_log\n"
      "hdfs://clojurebackend /recommend/data/streaming_tera_feature\n"
      "hdfs://clojurebackend /user/tiger/warehouse/video_article_app_log_daily\n"
      "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily\n"
      "hdfs://lispbackend /containers_log/remote\n"
      "hdfs://clojurebackend /warehouse_tmp\n"
      "hdfs://clojurebackend /inf/compression/clojure\n"
      "hdfs://clojurebackend /inf/compression/clojure_parquet\n"
      "hdfs://lispbackend /inf/compression/lisp\n"
      "hdfs://rustbackend /inf/compression/rust_parquet\n"
      "hdfs://lispbackend /inf/hprobe/lisp\n"
      "hdfs://clojurebackend /inf/hprobe/clojure\n"
      "hdfs://rustbackend /inf/hprobe/rust\n"
      "hdfs://teabackend /inf/hprobe/tea\n"
      "hdfs://hbasebackend /inf/hprobe/hbase\n"
      "hdfs://lispbackend /inf/bec\n"
      "hdfs://rustbackend /spark\n"
      "hdfs://rustbackend /flink\n"
      "hdfs://rustbackend /rust_tmp\n"
      "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db/winbid_mins_sum\n"
      "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db/winbid_dt_sum\n"
      "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db/winbid_hour_sum\n"
      "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db/winbid_dt\n"
      "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db/ad_debug_stats\n"
      "hdfs://rustbackend /user/tiger/warehouse/impression_stats_daily\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/bl_video_impression_stats_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/ml_vid_impression_stats_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_autocar.db/bl_impression_stats_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_wenda.db/bl_impression_stats_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_launch_device_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_launch_device_hourly\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_basic_test.db\n"
      "hdfs://rustbackend /user/tiger/warehouse/origin_log_test.db\n"
      "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/output\n"
      "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/kafka_output\n"
      "hdfs://rustbackend /recommend/data/live_app_terminate\n"
      "hdfs://rustbackend /recommend/data/live_app_launch\n"
      "hdfs://rustbackend /recommend/data/app_launch\n"
      "hdfs://rustbackend /recommend/data/aweme_app_terminate\n"
      "hdfs://rustbackend /recommend/data/aweme_app_launch\n"
      "hdfs://rustbackend /recommend/data/client_side_sessions\n"
      "hdfs://rustbackend /recommend/data/user_action_history_kafka\n"
      "hdfs://hbasebackend /ssd2/hbase_growth\n"
      "hdfs://hbasebackend /ssd2/hbase_growth-service\n"
      "hdfs://hbasebackend /ssd2/hbase_instance_debug\n"
      "hdfs://hbasebackend /ssd2/hbase_msp\n"
      "hdfs://hbasebackend /ssd2/hbase_recommend\n"
      "hdfs://hbasebackend /ssd2/hbase_impression-service\n"
      "hdfs://hbasebackend /ssd2/data_arch\n"
      "hdfs://rustbackend /user/tiger/warehouse/origin_log.db\n"
      "hdfs://rustbackend /user/tiger/warehouse/dm_basic.db\n"
      "hdfs://rustbackend /cg\n"
      "hdfs://teabackend /user/tiger/warehouse/origin_log_to_b.db\n"
      "hdfs://teabackend /user/tiger/tea_olap\n"
      "hdfs://teabackend /user/tiger/warehouse/tea.db\n"
      "hdfs://teabackend /user/tiger/warehouse/dm_tea.db\n"
      "hdfs://teabackend /user/tiger/warehouse/dm_tea_test.db\n"
      "hdfs://rustbackend /user/hetianyi/rt\n"
      "hdfs://rustbackend /webarch";

  HandleMountTableChange(str_mount_table_, 0);

  RandomAccessFile raf(GetProgramDirectory() + "/data/proxy/entries.data");
  auto size = raf.Size();
  auto buf = std::unique_ptr<char[]>(new char[size]);
  cnetpp::base::StringPiece res;
  ASSERT_TRUE(raf.Read(0, buf.get(), size, &res));
  ASSERT_EQ(size, res.size());
  std::vector<std::string> lines;
  cnetpp::base::StringUtils::SplitByChars(res, "\n", &lines);
  bool is_trash = false;
  for (auto& line : lines) {
    std::vector<std::string> entry;
    cnetpp::base::StringUtils::SplitByChars(line, " ", &entry);
    if (entry.size() != 2) {
      LOG(ERROR) << "Invalid entry line: " << line;
      continue;
    }
    auto fs = Resolve(entry[0], &is_trash);
    EXPECT_EQ(fs + entry[0], entry[1]);
  }
}

#if 0
TEST(MountsManagerTest, Test01) {
  bool is_trash = false;
  auto danceproxy_mount_table_zk_quorum_backup =
      FLAGS_danceproxy_mount_table_zk_quorum;
  auto danceproxy_mount_table_zk_path_backup =
      FLAGS_danceproxy_mount_table_zk_path;
  auto danceproxy_read_only_zk_path_backup =
      FLAGS_danceproxy_read_only_zk_path;
  DEFER([&] () {
    FLAGS_danceproxy_mount_table_zk_quorum =
        danceproxy_mount_table_zk_quorum_backup;
    FLAGS_danceproxy_mount_table_zk_path =
        danceproxy_mount_table_zk_path_backup;
    FLAGS_danceproxy_read_only_zk_path =
        danceproxy_read_only_zk_path_backup;
  });
  FLAGS_danceproxy_mount_table_zk_quorum =
      "10.6.128.152:2181,10.6.129.12:2181,10.6.128.194:2181,"
          "10.6.129.66:2181,10.6.128.237:2181";
  FLAGS_danceproxy_mount_table_zk_path = "/hadoop/hdfs/mounts";
  FLAGS_danceproxy_read_only_zk_path = "/hadoop/hdfs/readonly";
  MountsManager mm;
  mm.Init(nullptr);
  mm.WaitUntilInstalled();
  for (int i = 0; i < 5000; ++i) {
    EXPECT_EQ(mm.Resolve("/", &is_trash), "hdfs://harunabackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/clojure", &is_trash), "hdfs://clojurebackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/clojure_parquet", &is_trash),
              "hdfs://clojurebackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/haruna_parquet", &is_trash),
              "hdfs://harunabackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/haruna", &is_trash), "hdfs://harunabackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/rust", &is_trash), "hdfs://rustbackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/rust_parquet", &is_trash),
              "hdfs://rustbackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/scala", &is_trash), "hdfs://scalabackend");
    EXPECT_FALSE(is_trash);
    EXPECT_EQ(mm.Resolve("/inf/compression/scala_parquet", &is_trash),
              "hdfs://scalabackend");
    EXPECT_FALSE(is_trash);
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }
  mm.Stop();
}
#endif

#if 0
TEST(MountsManagerTest, Test02) {
  auto danceproxy_mount_table_zk_quorum_backup =
      FLAGS_danceproxy_mount_table_zk_quorum;
  auto danceproxy_mount_table_zk_path_backup =
      FLAGS_danceproxy_mount_table_zk_path;
  auto danceproxy_read_only_zk_path_backup =
      FLAGS_danceproxy_read_only_zk_path;
  DEFER([&] () {
    FLAGS_danceproxy_mount_table_zk_quorum =
        danceproxy_mount_table_zk_quorum_backup;
    FLAGS_danceproxy_mount_table_zk_path =
        danceproxy_mount_table_zk_path_backup;
    FLAGS_danceproxy_read_only_zk_path =
        danceproxy_read_only_zk_path_backup;
  });
  FLAGS_danceproxy_mount_table_zk_quorum =
      "10.6.128.152:2181,10.6.129.12:2181,10.6.128.194:2181,"
          "10.6.129.66:2181,10.6.128.237:2181";
  FLAGS_danceproxy_mount_table_zk_path = "/test/hadoop/hdfs/mounts";
  FLAGS_danceproxy_read_only_zk_path = "/test/hadoop/hdfs/readonly";
  auto zk_updater = std::thread([&] () {
    std::string mount_table1_("hdfs://harunabackend /\n"
                                  "hdfs://clojurebackend /nn2\n"
                                  "hdfs://clojurebackend /user/tiger/testnn2\n"
                                  "hdfs://clojurebackend /ssd2\n"
                                  "hdfs://clojurebackend /data/kafka_dump\n"
                                  "hdfs://clojurebackend /data/arch\n"
                                  "hdfs://clojurebackend /data_stats/flag_files\n"
                                  "hdfs://clojurebackend /yarn/node-labels\n"
                                  "hdfs://clojurebackend /yarn/timeline\n"
                                  "hdfs://clojurebackend /user/hetianyi/nn2\n"
                                  "hdfs://rustbackend /user/yangjinfeng.02\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/impression_stats_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/hetianyi/copytable\n"
                                  "hdfs://clojurebackend /hdfs/fsimage/haruna\n"
                                  "hdfs://clojurebackend /hbase2\n"
                                  "hdfs://clojurebackend /data/query_editor\n"
                                  "hdfs://clojurebackend /ss_ml/recommend/train_data/batch/proto_instance\n"
                                  "hdfs://clojurebackend /tmp/hadoop-yarn/staging\n"
                                  "hdfs://clojurebackend /tmp/hadoop-yarn/fail\n"
                                  "hdfs://clojurebackend /user/tiger/.sparkStaging\n"
                                  "hdfs://clojurebackend /tmp/hive-complexity\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_play_quality\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_play_quality\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/all_user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/all_user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily_json\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily_json\n"
                                  "hdfs://clojurebackend /data/parameter_server/model_dump\n"
                                  "hdfs://clojurebackend /spark/shuffle\n"
                                  "hdfs://clojurebackend /user/tiger/spark_history\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/group_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/group_profiles_daily\n"
                                  "hdfs://clojurebackend /yarn/checkpoint\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/server_refresh_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_refresh_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/spark_history_bak\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/appmonitor_etl_data_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/appmonitor_etl_data_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/server_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/client_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/client_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/tmp_video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/tmp_video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily\n"
                                  "hdfs://clojurebackend /model_dump\n"
                                  "hdfs://clojurebackend /recommend/model_dump\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/fact_snssdk_nginx_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly_tmp\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/adx_dsp_rsp_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/adx_dsp_rsp_daily\n"
                                  "hdfs://clojurebackend /recommend/data/app_log\n"
                                  "hdfs://clojurebackend /recommend/data/streaming_tera_feature\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_article_app_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly_ad\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly_ad_union\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_aggr\n"
                                  "hdfs://lispbackend /containers_log/remote\n"
                                  "hdfs://clojurebackend /warehouse_tmp\n"
                                  "hdfs://clojurebackend /inf/compression/clojure\n"
                                  "hdfs://clojurebackend /inf/compression/clojure_parquet\n"
                                  "hdfs://lispbackend /inf/compression/lisp\n"
                                  "hdfs://rustbackend /inf/compression/rust\n"
                                  "hdfs://rustbackend /inf/compression/rust_parquet\n"
                                  "hdfs://scalabackend /inf/compression/scala\n"
                                  "hdfs://scalabackend /inf/compression/scala_parquet\n"
                                  "hdfs://athenabackend /inf/compression/athena\n"
                                  "hdfs://athenabackend /inf/compression/athena_parquet\n"
                                  "hdfs://lispbackend /inf/hprobe/lisp\n"
                                  "hdfs://clojurebackend /inf/hprobe/clojure\n"
                                  "hdfs://rustbackend /inf/hprobe/rust\n"
                                  "hdfs://teabackend /inf/hprobe/tea\n"
                                  "hdfs://hbasebackend /inf/hprobe/hbase\n"
                                  "hdfs://lispbackend /inf/bec\n"
                                  "hdfs://rustbackend /spark\n"
                                  "hdfs://rustbackend /flink\n"
                                  "hdfs://rustbackend /rust_tmp\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/impression_stats_daily\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/bl_video_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/ml_vid_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_autocar.db/bl_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_wenda.db/bl_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_launch_device_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_launch_device_hourly_20171117\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_launch_device_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_launch_device_hourly_20171117\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/m_device_distinct_fully\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/scratch/m_device_distinct_fully\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/tmp_launch_device_m_device_launch_1d_hourly\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/scratch/tmp_launch_device_m_device_launch_1d_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_basic_test.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/origin_log_test.db\n"
                                  "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/output\n"
                                  "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/kafka_output\n"
                                  "hdfs://rustbackend /recommend/data/live_app_terminate\n"
                                  "hdfs://rustbackend /recommend/data/live_app_launch\n"
                                  "hdfs://rustbackend /recommend/data/app_launch\n"
                                  "hdfs://rustbackend /recommend/data/aweme_app_terminate\n"
                                  "hdfs://rustbackend /recommend/data/aweme_app_launch\n"
                                  "hdfs://rustbackend /recommend/data/client_side_sessions\n"
                                  "hdfs://rustbackend /recommend/data/user_action_history_kafka\n"
                                  "hdfs://rustbackend /recommend/data/server_impression_history_kafka\n"
                                  "hdfs://rustbackend /recommend/data/essay_server_impression_log_history_kafka\n"
                                  "hdfs://hbasebackend /ssd2/hbase_growth\n"
                                  "hdfs://hbasebackend /ssd2/hbase_growth-service\n"
                                  "hdfs://hbasebackend /ssd2/hbase_instance_debug\n"
                                  "hdfs://hbasebackend /ssd2/hbase_msp\n"
                                  "hdfs://hbasebackend /ssd2/hbase_recommend\n"
                                  "hdfs://hbasebackend /ssd2/hbase_impression-service\n"
                                  "hdfs://hbasebackend /ssd2/data_arch\n"
                                  "hdfs://hbasebackend /ssd2/hbase_ad\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/origin_log.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_basic.db\n"
                                  "hdfs://rustbackend /cg\n"
                                  "hdfs://teabackend /user/tiger/warehouse/origin_log_to_b.db\n"
                                  "hdfs://teabackend /user/tiger/tea_olap\n"
                                  "hdfs://teabackend /user/tiger/warehouse/tea.db\n"
                                  "hdfs://teabackend /user/tiger/warehouse/dm_tea.db\n"
                                  "hdfs://teabackend /user/tiger/warehouse/dm_tea_test.db\n"
                                  "hdfs://rustbackend /user/hetianyi/rt\n"
                                  "hdfs://rustbackend /webarch\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_parsed_snssdk_nginx_log_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_parsed_snssdk_nginx_log_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_installation_notify_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_installation_notify_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_app_data_installation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_app_data_installation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_app_data_activation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_app_data_activation_hourly\n"
                                  "hdfs://rustbackend /log/ad\n"
                                  "hdfs://rustbackend /ss_ml/ad/streaming_feature\n"
                                  "hdfs://rustbackend /content\n"
                                  "hdfs://rustbackend /ss_ml/ad/logdata\n"
                                  "hdfs://scalabackend /yoda/model\n"
                                  "hdfs://rustbackend /server_stats/data/server_stats_log_wenda\n"
                                  "hdfs://scalabackend /scala_tmp\n"
                                  "hdfs://scalabackend /recommend/data/hypstar_app_terminate\n"
                                  "hdfs://scalabackend /recommend/data/hypstar_app_launch\n"
                                  "hdfs://scalabackend /recommend/data/essay_app_launch\n"
                                  "hdfs://scalabackend /recommend/data/essay_app_terminate\n"
                                  "hdfs://scalabackend /talk/friend_feed_stat/newui/server_impr\n"
                                  "hdfs://scalabackend /recommend/data/qa/qa_user_weight/pr_user_follow_rst\n"
                                  "hdfs://scalabackend /recommend/data/qa/qa_user_weight/pr_user_follow_tmp\n"
                                  "hdfs://scalabackend /ss_ml/wap_rec/streaming_feature\n"
                                  "hdfs://scalabackend /ssad/ad_track\n"
                                  "hdfs://scalabackend /ssad/ad_user_context\n"
                                  "hdfs://scalabackend /ssad/ad_stats_mini\n"
                                  "hdfs://scalabackend /ssad/ad_request\n"
                                  "hdfs://scalabackend /ssad/ad_send_idfa\n"
                                  "hdfs://scalabackend /ssad/ad_debug\n"
                                  "hdfs://scalabackend /ss_ml/ad/model/model_bk\n"
                                  "hdfs://scalabackend /ssad/ad_delivery_log\n"
                                  "hdfs://scalabackend /ssad/ad_engine_ack_event\n"
                                  "hdfs://scalabackend /ssad/ad_link_monitor\n"
                                  "hdfs://scalabackend /data/screen_lock/gionee_tt/offline_instance_sf_hourly\n"
                                  "hdfs://scalabackend /data/screen_lock/gionee_tt/offline_instance_hourly\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_request_log\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_log_join\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_real_event_log\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_response_log\n"
                                  "hdfs://scalabackend /ssad/flow_control_status_log\n"
                                  "hdfs://scalabackend /ssad/game_event\n"
                                  "hdfs://scalabackend /ssad/game_stat_event\n"
                                  "hdfs://scalabackend /ssad/game_stats\n"
                                  "hdfs://scalabackend /ssad/ad_abtest_union\n"
                                  "hdfs://scalabackend /google_images\n"
                                  "hdfs://scalabackend /system/log/snssdk_nginx_log\n"
                                  "hdfs://scalabackend /recommend/hotsoon/instance\n"
                                  "hdfs://scalabackend /ss_ml/ad/predict_debug\n"
                                  "hdfs://athenabackend /dp/dw_core\n"
                                  "hdfs://athenabackend /caijing\n"
                                  "hdfs://athenabackend /data/kafka_dump/snssdk_app_log_config\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_automobile\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_aweme\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_eyeu\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_wenda\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_video\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_live\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_faceu\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_dirty\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_misc\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_debug\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_staging\n"
                                  "hdfs://athenabackend /data/kafka_dump/ios_source_installation\n"
                                  "hdfs://athenabackend /data/kafka_dump/profile_editlog_data\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_devicedb\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_crawl_groupdb\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_wenda_audit\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_userdb\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_terminate_to_b\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_launch_to_b\n"
                                  "hdfs://athenabackend /data/kafka_dump/faceu_app_terminate\n"
                                  "hdfs://athenabackend /data/kafka_dump/faceu_app_launch\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_terminate\n"
                                  "hdfs://athenabackend /data/kafka_dump/launch_device\n"
                                  "hdfs://athenabackend /recommend/data/trill_app_launch\n"
                                  "hdfs://athenabackend /recommend/data/trill_app_terminate\n"
                                  "hdfs://athenabackend /inf/dumped_metric\n"
                                  "hdfs://scalabackend /recommend/essay/instance\n"
                                  "hdfs://scalabackend /recommend/data/kafka_dump\n"
                                  "hdfs://lispbackend /ssd2/hbase_groupbase");
    std::string mount_table2_("hdfs://harunabackend /\n"
                                  "hdfs://clojurebackend /nn2\n"
                                  "hdfs://clojurebackend /user/tiger/testnn2\n"
                                  "hdfs://clojurebackend /ssd2\n"
                                  "hdfs://clojurebackend /data/kafka_dump\n"
                                  "hdfs://clojurebackend /data/arch\n"
                                  "hdfs://clojurebackend /data_stats/flag_files\n"
                                  "hdfs://clojurebackend /yarn/node-labels\n"
                                  "hdfs://clojurebackend /yarn/timeline\n"
                                  "hdfs://clojurebackend /user/hetianyi/nn2\n"
                                  "hdfs://lispbackend /user/yangjinfeng.02\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/impression_stats_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/hetianyi/copytable\n"
                                  "hdfs://clojurebackend /hdfs/fsimage/haruna\n"
                                  "hdfs://clojurebackend /hbase2\n"
                                  "hdfs://clojurebackend /data/query_editor\n"
                                  "hdfs://clojurebackend /ss_ml/recommend/train_data/batch/proto_instance\n"
                                  "hdfs://clojurebackend /tmp/hadoop-yarn/staging\n"
                                  "hdfs://clojurebackend /tmp/hadoop-yarn/fail\n"
                                  "hdfs://clojurebackend /user/tiger/.sparkStaging\n"
                                  "hdfs://clojurebackend /tmp/hive-complexity\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_play_quality\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_play_quality\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/all_user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/all_user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/user_profiles_daily_json\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/user_profiles_daily_json\n"
                                  "hdfs://clojurebackend /data/parameter_server/model_dump\n"
                                  "hdfs://clojurebackend /spark/shuffle\n"
                                  "hdfs://clojurebackend /user/tiger/spark_history\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/group_profiles_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/group_profiles_daily\n"
                                  "hdfs://clojurebackend /yarn/checkpoint\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/server_refresh_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_refresh_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily_orc\n"
                                  "hdfs://clojurebackend /user/tiger/spark_history_bak\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/appmonitor_etl_data_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/appmonitor_etl_data_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/server_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/server_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/client_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/client_impressions_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/tmp_video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/tmp_video_impression_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/app_log_daily\n"
                                  "hdfs://clojurebackend /model_dump\n"
                                  "hdfs://clojurebackend /recommend/model_dump\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/fact_snssdk_nginx_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/app_log_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/impression_stats_daily_by_hourly_tmp\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/adx_dsp_rsp_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/scratch/adx_dsp_rsp_daily\n"
                                  "hdfs://clojurebackend /recommend/data/app_log\n"
                                  "hdfs://clojurebackend /recommend/data/streaming_tera_feature\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/video_article_app_log_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly_ad\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_hourly_ad_union\n"
                                  "hdfs://clojurebackend /user/tiger/warehouse/ad_stats_daily_aggr\n"
                                  "hdfs://lispbackend /containers_log/remote\n"
                                  "hdfs://clojurebackend /warehouse_tmp\n"
                                  "hdfs://clojurebackend /inf/compression/clojure\n"
                                  "hdfs://clojurebackend /inf/compression/clojure_parquet\n"
                                  "hdfs://lispbackend /inf/compression/lisp\n"
                                  "hdfs://rustbackend /inf/compression/rust\n"
                                  "hdfs://rustbackend /inf/compression/rust_parquet\n"
                                  "hdfs://scalabackend /inf/compression/scala\n"
                                  "hdfs://scalabackend /inf/compression/scala_parquet\n"
                                  "hdfs://athenabackend /inf/compression/athena\n"
                                  "hdfs://athenabackend /inf/compression/athena_parquet\n"
                                  "hdfs://lispbackend /inf/hprobe/lisp\n"
                                  "hdfs://clojurebackend /inf/hprobe/clojure\n"
                                  "hdfs://rustbackend /inf/hprobe/rust\n"
                                  "hdfs://teabackend /inf/hprobe/tea\n"
                                  "hdfs://hbasebackend /inf/hprobe/hbase\n"
                                  "hdfs://lispbackend /inf/bec\n"
                                  "hdfs://rustbackend /spark\n"
                                  "hdfs://rustbackend /flink\n"
                                  "hdfs://rustbackend /rust_tmp\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/ad_engine.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/impression_stats_daily\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/bl_video_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_video.db/ml_vid_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_autocar.db/bl_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_wenda.db/bl_impression_stats_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_launch_device_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_launch_device_hourly_20171117\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_launch_device_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_launch_device_hourly_20171117\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/m_device_distinct_fully\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/scratch/m_device_distinct_fully\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/tmp_launch_device_m_device_launch_1d_hourly\n"
                                  "hdfs://scalabackend /user/tiger/warehouse/growth.db/scratch/tmp_launch_device_m_device_launch_1d_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_basic_test.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/origin_log_test.db\n"
                                  "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/output\n"
                                  "hdfs://rustbackend /ss_ml/recommend/train_data/batch/proto_streamfea/kafka_output\n"
                                  "hdfs://rustbackend /recommend/data/live_app_terminate\n"
                                  "hdfs://rustbackend /recommend/data/live_app_launch\n"
                                  "hdfs://rustbackend /recommend/data/app_launch\n"
                                  "hdfs://rustbackend /recommend/data/aweme_app_terminate\n"
                                  "hdfs://rustbackend /recommend/data/aweme_app_launch\n"
                                  "hdfs://rustbackend /recommend/data/client_side_sessions\n"
                                  "hdfs://rustbackend /recommend/data/user_action_history_kafka\n"
                                  "hdfs://rustbackend /recommend/data/server_impression_history_kafka\n"
                                  "hdfs://rustbackend /recommend/data/essay_server_impression_log_history_kafka\n"
                                  "hdfs://hbasebackend /ssd2/hbase_growth\n"
                                  "hdfs://hbasebackend /ssd2/hbase_growth-service\n"
                                  "hdfs://hbasebackend /ssd2/hbase_instance_debug\n"
                                  "hdfs://hbasebackend /ssd2/hbase_msp\n"
                                  "hdfs://hbasebackend /ssd2/hbase_recommend\n"
                                  "hdfs://hbasebackend /ssd2/hbase_impression-service\n"
                                  "hdfs://hbasebackend /ssd2/data_arch\n"
                                  "hdfs://hbasebackend /ssd2/hbase_ad\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/origin_log.db\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/dm_basic.db\n"
                                  "hdfs://rustbackend /cg\n"
                                  "hdfs://teabackend /user/tiger/warehouse/origin_log_to_b.db\n"
                                  "hdfs://teabackend /user/tiger/tea_olap\n"
                                  "hdfs://teabackend /user/tiger/warehouse/tea.db\n"
                                  "hdfs://teabackend /user/tiger/warehouse/dm_tea.db\n"
                                  "hdfs://teabackend /user/tiger/warehouse/dm_tea_test.db\n"
                                  "hdfs://rustbackend /user/hetianyi/rt\n"
                                  "hdfs://rustbackend /webarch\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_parsed_snssdk_nginx_log_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_parsed_snssdk_nginx_log_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_installation_notify_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_installation_notify_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_app_data_installation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_app_data_installation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/d_app_data_activation_hourly\n"
                                  "hdfs://rustbackend /user/tiger/warehouse/growth.db/scratch/d_app_data_activation_hourly\n"
                                  "hdfs://rustbackend /log/ad\n"
                                  "hdfs://rustbackend /ss_ml/ad/streaming_feature\n"
                                  "hdfs://rustbackend /content\n"
                                  "hdfs://rustbackend /ss_ml/ad/logdata\n"
                                  "hdfs://scalabackend /yoda/model\n"
                                  "hdfs://rustbackend /server_stats/data/server_stats_log_wenda\n"
                                  "hdfs://scalabackend /scala_tmp\n"
                                  "hdfs://scalabackend /recommend/data/hypstar_app_terminate\n"
                                  "hdfs://scalabackend /recommend/data/hypstar_app_launch\n"
                                  "hdfs://scalabackend /recommend/data/essay_app_launch\n"
                                  "hdfs://scalabackend /recommend/data/essay_app_terminate\n"
                                  "hdfs://scalabackend /talk/friend_feed_stat/newui/server_impr\n"
                                  "hdfs://scalabackend /recommend/data/qa/qa_user_weight/pr_user_follow_rst\n"
                                  "hdfs://scalabackend /recommend/data/qa/qa_user_weight/pr_user_follow_tmp\n"
                                  "hdfs://scalabackend /ss_ml/wap_rec/streaming_feature\n"
                                  "hdfs://scalabackend /ssad/ad_track\n"
                                  "hdfs://scalabackend /ssad/ad_user_context\n"
                                  "hdfs://scalabackend /ssad/ad_stats_mini\n"
                                  "hdfs://scalabackend /ssad/ad_request\n"
                                  "hdfs://scalabackend /ssad/ad_send_idfa\n"
                                  "hdfs://scalabackend /ssad/ad_debug\n"
                                  "hdfs://scalabackend /ss_ml/ad/model/model_bk\n"
                                  "hdfs://scalabackend /ssad/ad_delivery_log\n"
                                  "hdfs://scalabackend /ssad/ad_engine_ack_event\n"
                                  "hdfs://scalabackend /ssad/ad_link_monitor\n"
                                  "hdfs://scalabackend /data/screen_lock/gionee_tt/offline_instance_sf_hourly\n"
                                  "hdfs://scalabackend /data/screen_lock/gionee_tt/offline_instance_hourly\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_request_log\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_log_join\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_real_event_log\n"
                                  "hdfs://scalabackend /ssad/dsp_ad_response_log\n"
                                  "hdfs://scalabackend /ssad/flow_control_status_log\n"
                                  "hdfs://scalabackend /ssad/game_event\n"
                                  "hdfs://scalabackend /ssad/game_stat_event\n"
                                  "hdfs://scalabackend /ssad/game_stats\n"
                                  "hdfs://scalabackend /ssad/ad_abtest_union\n"
                                  "hdfs://scalabackend /google_images\n"
                                  "hdfs://scalabackend /system/log/snssdk_nginx_log\n"
                                  "hdfs://scalabackend /recommend/hotsoon/instance\n"
                                  "hdfs://scalabackend /ss_ml/ad/predict_debug\n"
                                  "hdfs://athenabackend /dp/dw_core\n"
                                  "hdfs://athenabackend /caijing\n"
                                  "hdfs://athenabackend /data/kafka_dump/snssdk_app_log_config\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_automobile\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_aweme\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_eyeu\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_tuchong\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_wenda\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_video\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_live\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_faceu\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_dirty\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_misc\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_debug\n"
                                  "hdfs://athenabackend /data/kafka_dump/mario_event_staging\n"
                                  "hdfs://athenabackend /data/kafka_dump/ios_source_installation\n"
                                  "hdfs://athenabackend /data/kafka_dump/profile_editlog_data\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_devicedb\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_crawl_groupdb\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_wenda_audit\n"
                                  "hdfs://athenabackend /data/kafka_dump/binlog_userdb\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_terminate_to_b\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_launch_to_b\n"
                                  "hdfs://athenabackend /data/kafka_dump/faceu_app_terminate\n"
                                  "hdfs://athenabackend /data/kafka_dump/faceu_app_launch\n"
                                  "hdfs://athenabackend /data/kafka_dump/app_terminate\n"
                                  "hdfs://athenabackend /data/kafka_dump/launch_device\n"
                                  "hdfs://athenabackend /recommend/data/trill_app_launch\n"
                                  "hdfs://athenabackend /recommend/data/trill_app_terminate\n"
                                  "hdfs://athenabackend /inf/dumped_metric\n"
                                  "hdfs://scalabackend /recommend/essay/instance\n"
                                  "hdfs://scalabackend /recommend/data/kafka_dump\n"
                                  "hdfs://lispbackend /ssd2/hbase_groupbase");
    auto handle = zookeeper_init2(
        FLAGS_danceproxy_mount_table_zk_quorum.c_str(),
        nullptr,
        600000,
        nullptr,
        nullptr,
        0,
        nullptr);
    for (int i = 0; i < 10000; ++i) {
      const char *buf;
      int buf_len;
      if (i & 1) {
        buf = mount_table1_.c_str();
        buf_len = static_cast<int>(mount_table1_.size());
      } else {
        buf = mount_table2_.c_str();
        buf_len = static_cast<int>(mount_table2_.size());
      }
      auto res = zoo_set(handle,
                         FLAGS_danceproxy_mount_table_zk_path.c_str(),
                         buf,
                         buf_len,
                         -1);
      if (res != ZOK) {
        LOG(FATAL) << "Failed to update mount table to zk: " << res;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1100));
    }
  });
  std::this_thread::sleep_for(std::chrono::seconds(1));
  MountsManager mm;
  mm.Init(nullptr);
  mm.WaitUntilInstalled();
  bool is_trash = false;
  for (int i = 0; i < 0x7fffffff; ++i) {
    auto expected_fs = mm.Resolve(
        "/ssd2/hbase_groupbase/data/default/group_profile", &is_trash);
    if (expected_fs != "hdfs://lispbackend") {
      LOG(ERROR) << "Resolve failed.";
    }
    EXPECT_EQ(expected_fs, "hdfs://lispbackend");
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }
  mm.Stop();
  zk_updater.join();
}
#endif

}  // namespace dancenn

