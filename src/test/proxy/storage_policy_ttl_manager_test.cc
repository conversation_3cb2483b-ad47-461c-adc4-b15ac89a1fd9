// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include "proxy/storage_policy_ttl_manager.h"

namespace dancenn {

class MockStoragePolicyTTLManager : public StoragePolicyTTLManager,
                                    public testing::Test {
 public:
  MockStoragePolicyTTLManager()
      : StoragePolicyTTLManager::StoragePolicyTTLManager(nullptr) {
  }

  explicit MockStoragePolicyTTLManager(
      std::shared_ptr<UpstreamManager> upstream_manager)
      : StoragePolicyTTLManager::StoragePolicyTTLManager(upstream_manager) {
  }

  void SetUp() override {
    enabled_ = true;
    ttls_.emplace("/a", 3);
    ttls_.emplace("/b/c", -1);
    ttls_.emplace("/d/e/f", 0);
  }

  void TearDown() override {}
};

TEST_F(MockStoragePolicyTTLManager, TestEnable) {
  enabled_ = false;
  ASSERT_EQ(this->GetStoragePolicyTTL("/a"), "");
  ASSERT_EQ(this->GetStoragePolicyTTL("/b/c"), "");
  ASSERT_EQ(this->GetStoragePolicyTTL("/d/e/f"), "");
}

TEST_F(MockStoragePolicyTTLManager, TestTTL) {
  enabled_ = true;
  ASSERT_EQ(this->GetStoragePolicyTTL("/a"), "storagePolicyTTL:3");
  ASSERT_EQ(this->GetStoragePolicyTTL("/b/c"), "storagePolicyTTL:-1");
  ASSERT_EQ(this->GetStoragePolicyTTL("/b/c/d"), "storagePolicyTTL:-1");
  ASSERT_EQ(this->GetStoragePolicyTTL("/d/e/f"), "");
}

}  // namespace dancenn

