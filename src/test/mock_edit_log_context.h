//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef TEST_MOCK_EDIT_LOG_CONTEXT_H_
#define TEST_MOCK_EDIT_LOG_CONTEXT_H_

#include <memory>
#include <vector>

#include "edit/edit_log_context.h"
#include "test/mock_edit_log_input_context.h"

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class MockEditLogContext : public EditLogContextBase {
 public:
  MockEditLogContext() {}
  ~MockEditLogContext() {}

  void SetupSyncListener(
      std::shared_ptr<IEditLogSyncListener> listener) override {
    listener_ = listener;
  }

  std::shared_ptr<IEditLogSyncListener> TestOnlyGetSyncListener() override {
    return listener_;
  }

  EditLogConf::HAMode GetHAMode() override {
    return EditLogConf::HA;
  }

  void OpenForRead() override {}
  int64_t OpenForWrite() override { return 0; }

  bool IsOpenForWrite() override {
    return open_for_write_;
  }
  bool IsOpenForRead() override {
    return open_for_read_;
  }

  bool IsActiveInLease() const override {
    return true;
  }

  bool UpdateConfProperty(const std::string& name,
                          const std::string& value) {
    return true;
  }

  std::unique_ptr<EditLogInputContextBase> CreateInputContext(
      int64_t from_txid, int64_t to_at_least_txid,
      bool is_progress_ok) override {
    (void) from_txid;
    (void) to_at_least_txid;
    (void) is_progress_ok;
    return std::unique_ptr<MockEditLogInputContext>(
        new MockEditLogInputContext);
  }

  int64_t GetWaitSyncTime() override { return 0; }

  void LogSync(bool force=false) override { (void) force; }
  void LogSyncAll() override {}
  void Close() override {}
  void InitJournalsForWrite() override {}
  void SetNextTxId(int64_t) override {}

  void SetLastAllocatedBlockId(int64_t id) override {
    block_id_.store(id);
  }

  void SetLastGenerationStampV2(int64_t gsv2) override {
    gs_.store(gsv2);
  }

  uint64_t GetLastAllocatedBlockId() override {
    return block_id_.load();
  }

  uint64_t GetLastGenerationStampV2() override {
    return gs_.load();
  }

  int64_t GetCurSegmentTxId() override { return 0; }
  int64_t GetLastWrittenTxId() override { return 0; }
  int64_t RollEditLog() override { return 0; }
  void PurgeLogsOlderThan(int64_t min_tx_id_to_keep) override { return; }
  bool GetPeerNNAddr(std::string* addr) override { return false; }
  bool GetAllStackTraces(std::string* stack_info) override { return false; }

  EditLogConf::PreviousEditLogConf HASwitchFence() override {
    return EditLogConf::PreviousEditLogConf();
  }

  bool open_for_read_ {true};
  bool open_for_write_ {true};

  // op method
  int64_t LogOpenFile(const std::stringstream *ss,
                      bool to_log_rpc_ids) override { return 0; }
  int64_t LogCloseFile(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogAddBlock(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogUpdateBlocks(const std::stringstream *ss,
                          bool to_log_rpc_ids) override { return 0; }
  int64_t LogMkDir(const std::stringstream *ss) override { return 0; }
  int64_t LogRenameOld(const std::stringstream *ss,
                       bool to_log_rpc_ids) override { return 0; }
  int64_t LogRename(const std::stringstream *ss,
                    bool to_log_rpc_ids) override { return 0; }
  int64_t LogSetReplication(const std::stringstream *ss) override {
    return 0;
  }
  int64_t LogSetStoragePolicy(const std::stringstream *ss) override {
    return 0;
  }
  int64_t LogSetReplicaPolicy(const std::stringstream *ss) override {
    return 0;
  }
  int64_t LogSetDirReplicaPolicy(const std::stringstream *ss) override {
    return 0;
  }
  int64_t LogSetQuota(const std::stringstream *ss) override { return 0; }
  int64_t LogSetPermissions(const std::stringstream *ss) override { return 0; }
  int64_t LogSetOwner(const std::stringstream *ss) override { return 0; }
  int64_t LogConcat(const std::stringstream *ss,
                    bool to_log_rpc_ids) override { return 0; }
  int64_t LogDelete(const std::stringstream *ss,
                    bool to_log_rpc_ids) override { return 0; }
  int64_t LogGenerationStampV1(const std::stringstream *ss) override { return 0; }

  int64_t LogGenerationStampV2(const std::stringstream *ss, uint64_t* gsv2) override {
    *gsv2 = ++gs_;

    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    return 0;
  }

  int64_t LogAllocateBlockId(const std::stringstream *ss, uint64_t* id) override {
    *id = ++block_id_;

    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    return 0;
  }

  int64_t LogAllocateBlockIdAndGSv2(const std::stringstream* blkid_ss,
                                    const std::stringstream* gsv2_ss,
                                    uint64_t* blkid,
                                    uint64_t* gsv2) override {
    *blkid = ++block_id_;
    *gsv2  = ++gs_;

    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    return 0;
  }

  int64_t LogTimes(const std::stringstream *ss) override { return 0; }
  int64_t LogSymlink(const std::stringstream *ss,
                     bool to_log_rpc_ids) override { return 0; }
  int64_t LogReassignLease(const std::stringstream *ss) override { return 0; }
  int64_t LogSetAcl(const std::stringstream *ss) override { return 0; }
  int64_t LogSetXAttrs(const std::stringstream *ss,
                       bool to_log_rpc_ids) override { return 0; }
  int64_t LogRemoveXAttrs(const std::stringstream *ss,
                          bool to_log_rpc_ids) override { return 0; }
  int64_t LogAccessCounterSnapshot(const std::stringstream *ss) override {
    return 0;
  }
  int64_t LogSetBlockPufsInfo(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogDeleteDeprecatedBlockPufsInfo(const std::stringstream* ss)
      override {
    return 0;
  }
  int64_t LogAllowSnapshot(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogDisallowSnapshot(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogCreateSnapshot(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogDeleteSnapshot(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogRenameSnapshot(const std::stringstream* ss) override {
    return 0;
  }
  int64_t LogCfsOp(const std::stringstream *ss) override {
    return 0;
  }

  Status SwitchNonHAActiveToHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAActiveToNonHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAStandbyToNonHAActive() override {
    return Status(Code::kError);
  }

 private:
  std::shared_ptr<IEditLogSyncListener> listener_;
  std::atomic<uint64_t> gs_{0};
  std::atomic<uint64_t> block_id_{0};
};

}  // namespace dancenn
#endif  // TEST_MOCK_EDIT_LOG_CONTEXT_H_
