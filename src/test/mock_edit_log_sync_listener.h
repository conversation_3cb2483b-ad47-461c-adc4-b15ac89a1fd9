// Copyright 2018 livexmm <<EMAIL>>

#ifndef TEST_MOCK_EDIT_LOG_SYNC_LISTENER_H_
#define TEST_MOCK_EDIT_LOG_SYNC_LISTENER_H_

#include <functional>
#include <atomic>

#include "edit/edit_log_sync_listener.h"
#include <glog/logging.h>

namespace dancenn {
  class MockEditLogSyncListener : public IEditLogSyncListener {
   public:
     MockEditLogSyncListener(int64_t last_txid,
         std::function<void(std::atomic<int64_t>&, int64_t)>&& fn)
       : last_txid_(last_txid),
         fn_(fn) {}
     ~MockEditLogSyncListener() {}

     // for future
     void TxFinish(int64_t begin_txid, int n) override {
       for (int i = 0; i < n; i++) {
         fn_(last_txid_, begin_txid + i);
       }
     }

     int64_t IncrLastTxid(int n) {
       int64_t x = last_txid_.fetch_add(n);
       return x + n;
     }
   private:
     std::atomic<int64_t> last_txid_;
     std::function<void(std::atomic<int64_t>&, int64_t)> fn_;
  };
}

#endif
