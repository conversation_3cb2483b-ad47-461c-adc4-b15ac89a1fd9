// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/10
// Description

#ifndef TEST_GMOCK_EDIT_LOG_SENDER_H_
#define TEST_GMOCK_EDIT_LOG_SENDER_H_

#include <gmock/gmock.h>
#include <google/protobuf/repeated_field.h>

#include <cstdint>
#include <string>
#include <vector>

#include "edit/sender_base.h"
#include "proto/generated/cloudfs/acl.pb.h"
#include "proto/generated/cloudfs/fsimage.pb.h"
#include "proto/generated/cloudfs/xattr.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "proto/generated/dancenn/inode.pb.h"

#define GMOCK_METHOD17_(tn, constness, ct, Method, ...)                      \
  static_assert(                                                             \
      17 == ::testing::internal::Function<__VA_ARGS__>::ArgumentCount,       \
      "MOCK_METHOD<N> must match argument count.");                          \
  GMOCK_RESULT_(tn, __VA_ARGS__)                                             \
  ct Method(GMOCK_ARG_(tn, 1, __VA_ARGS__) gmock_a1,                         \
            GMOCK_ARG_(tn, 2, __VA_ARGS__) gmock_a2,                         \
            GMOCK_ARG_(tn, 3, __VA_ARGS__) gmock_a3,                         \
            GMOCK_ARG_(tn, 4, __VA_ARGS__) gmock_a4,                         \
            GMOCK_ARG_(tn, 5, __VA_ARGS__) gmock_a5,                         \
            GMOCK_ARG_(tn, 6, __VA_ARGS__) gmock_a6,                         \
            GMOCK_ARG_(tn, 7, __VA_ARGS__) gmock_a7,                         \
            GMOCK_ARG_(tn, 8, __VA_ARGS__) gmock_a8,                         \
            GMOCK_ARG_(tn, 9, __VA_ARGS__) gmock_a9,                         \
            GMOCK_ARG_(tn, 10, __VA_ARGS__) gmock_a10,                       \
            GMOCK_ARG_(tn, 11, __VA_ARGS__) gmock_a11,                       \
            GMOCK_ARG_(tn, 12, __VA_ARGS__) gmock_a12,                       \
            GMOCK_ARG_(tn, 13, __VA_ARGS__) gmock_a13,                       \
            GMOCK_ARG_(tn, 14, __VA_ARGS__) gmock_a14,                       \
            GMOCK_ARG_(tn, 15, __VA_ARGS__) gmock_a15,                       \
            GMOCK_ARG_(tn, 16, __VA_ARGS__) gmock_a16,                       \
            GMOCK_ARG_(tn, 17, __VA_ARGS__) gmock_a17) constness {           \
    GMOCK_MOCKER_(17, constness, Method).SetOwnerAndName(this, #Method);     \
    return GMOCK_MOCKER_(17, constness, Method)                              \
        .Invoke(::std::forward<GMOCK_ARG_(tn, 1, __VA_ARGS__)>(gmock_a1),    \
                ::std::forward<GMOCK_ARG_(tn, 2, __VA_ARGS__)>(gmock_a2),    \
                ::std::forward<GMOCK_ARG_(tn, 3, __VA_ARGS__)>(gmock_a3),    \
                ::std::forward<GMOCK_ARG_(tn, 4, __VA_ARGS__)>(gmock_a4),    \
                ::std::forward<GMOCK_ARG_(tn, 5, __VA_ARGS__)>(gmock_a5),    \
                ::std::forward<GMOCK_ARG_(tn, 6, __VA_ARGS__)>(gmock_a6),    \
                ::std::forward<GMOCK_ARG_(tn, 7, __VA_ARGS__)>(gmock_a7),    \
                ::std::forward<GMOCK_ARG_(tn, 8, __VA_ARGS__)>(gmock_a8),    \
                ::std::forward<GMOCK_ARG_(tn, 9, __VA_ARGS__)>(gmock_a9),    \
                ::std::forward<GMOCK_ARG_(tn, 10, __VA_ARGS__)>(gmock_a10),  \
                ::std::forward<GMOCK_ARG_(tn, 11, __VA_ARGS__)>(gmock_a11),  \
                ::std::forward<GMOCK_ARG_(tn, 12, __VA_ARGS__)>(gmock_a12),  \
                ::std::forward<GMOCK_ARG_(tn, 13, __VA_ARGS__)>(gmock_a13),  \
                ::std::forward<GMOCK_ARG_(tn, 14, __VA_ARGS__)>(gmock_a14),  \
                ::std::forward<GMOCK_ARG_(tn, 15, __VA_ARGS__)>(gmock_a15),  \
                ::std::forward<GMOCK_ARG_(tn, 16, __VA_ARGS__)>(gmock_a16),  \
                ::std::forward<GMOCK_ARG_(tn, 17, __VA_ARGS__)>(gmock_a17)); \
  }                                                                          \
  ::testing::MockSpec<__VA_ARGS__> gmock_##Method(                           \
      GMOCK_MATCHER_(tn, 1, __VA_ARGS__) gmock_a1,                           \
      GMOCK_MATCHER_(tn, 2, __VA_ARGS__) gmock_a2,                           \
      GMOCK_MATCHER_(tn, 3, __VA_ARGS__) gmock_a3,                           \
      GMOCK_MATCHER_(tn, 4, __VA_ARGS__) gmock_a4,                           \
      GMOCK_MATCHER_(tn, 5, __VA_ARGS__) gmock_a5,                           \
      GMOCK_MATCHER_(tn, 6, __VA_ARGS__) gmock_a6,                           \
      GMOCK_MATCHER_(tn, 7, __VA_ARGS__) gmock_a7,                           \
      GMOCK_MATCHER_(tn, 8, __VA_ARGS__) gmock_a8,                           \
      GMOCK_MATCHER_(tn, 9, __VA_ARGS__) gmock_a9,                           \
      GMOCK_MATCHER_(tn, 10, __VA_ARGS__) gmock_a10,                         \
      GMOCK_MATCHER_(tn, 11, __VA_ARGS__) gmock_a11,                         \
      GMOCK_MATCHER_(tn, 12, __VA_ARGS__) gmock_a12,                         \
      GMOCK_MATCHER_(tn, 13, __VA_ARGS__) gmock_a13,                         \
      GMOCK_MATCHER_(tn, 14, __VA_ARGS__) gmock_a14,                         \
      GMOCK_MATCHER_(tn, 15, __VA_ARGS__) gmock_a15,                         \
      GMOCK_MATCHER_(tn, 16, __VA_ARGS__) gmock_a16,                         \
      GMOCK_MATCHER_(tn, 17, __VA_ARGS__) gmock_a17) constness {             \
    GMOCK_MOCKER_(17, constness, Method).RegisterOwner(this);                \
    return GMOCK_MOCKER_(17, constness, Method)                              \
        .With(gmock_a1,                                                      \
              gmock_a2,                                                      \
              gmock_a3,                                                      \
              gmock_a4,                                                      \
              gmock_a5,                                                      \
              gmock_a6,                                                      \
              gmock_a7,                                                      \
              gmock_a8,                                                      \
              gmock_a9,                                                      \
              gmock_a10,                                                     \
              gmock_a11,                                                     \
              gmock_a12,                                                     \
              gmock_a13,                                                     \
              gmock_a14,                                                     \
              gmock_a15,                                                     \
              gmock_a16,                                                     \
              gmock_a17);                                                    \
  }                                                                          \
  ::testing::MockSpec<__VA_ARGS__> gmock_##Method(                           \
      const ::testing::internal::WithoutMatchers&,                           \
      constness ::testing::internal::Function<__VA_ARGS__>*) const {         \
    return ::testing::internal::AdjustConstness_##constness(this)            \
        ->gmock_##Method(::testing::A<GMOCK_ARG_(tn, 1, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 2, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 3, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 4, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 5, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 6, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 7, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 8, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 9, __VA_ARGS__)>(),     \
                         ::testing::A<GMOCK_ARG_(tn, 10, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 11, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 12, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 13, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 14, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 15, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 16, __VA_ARGS__)>(),    \
                         ::testing::A<GMOCK_ARG_(tn, 17, __VA_ARGS__)>());   \
  }                                                                          \
  mutable ::testing::FunctionMocker<__VA_ARGS__> GMOCK_MOCKER_(              \
      17, constness, Method)

#define MOCK_METHOD17(m, ...) GMOCK_METHOD17_(, , , m, __VA_ARGS__)

namespace dancenn {

// TODO(ruanjunbin): Update gmock to v1.10.0 because it contains a
// new powerful MOCK_METHOD macro.
// https://github.com/google/googletest/releases/tag/release-1.10.0.
class GMockEditLogSender : public EditLogSenderBase {
 public:
  // -------- New Edit Log Begins.
  // File related.
  MOCK_METHOD17(
      LogOpenFileV2,
      int64_t(const std::string& path,
              const INode& inode,
              const INode& parent,
              bool overwrite,
              const INode* overwrite_inode,
              bool move_to_recycle_bin,
              const std::string* rb_path,
              const INode* rb_inode,
              const INode* rb_parent,
              const std::vector<BlockInfoProto>& add_block_bips,
              const std::vector<std::vector<std::string>>& bips_expected_locs,
              const std::vector<INodeID>& ancestors_id,
              const std::vector<INodeID>* rb_ancestors_id,
              const SnapshotLog& old_inode_snaplog,
              const SnapshotLog& parent_snaplog,
              const SnapshotLog& rb_parent_snaplog,
              const LogRpcInfo& log_rpc_info));
  MOCK_METHOD7(LogAppend,
               int64_t(const std::string path,
                       const INode& inode,
                       const INode& parent,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD9(LogAddBlockV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const BlockInfoProto* penultimate_bip_tbc,
                       const BlockInfoProto& last_bip_tbuc,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const std::vector<std::string>& dn_uuids,
                       const SnapshotLog& inode_snaplog,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD6(LogAbandonBlock,
               int64_t(const std::string& path,
                       const INode& inode,
                       BlockID abandoned_blk_id,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD6(LogUpdatePipeline,
               int64_t(const std::string& path,
                       const INode& file,
                       const BlockInfoProto& last_bip_tbuc,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD7(LogFsync,
               int64_t(const std::string& path,
                       const INode& inode,
                       const BlockInfoProto* last_bip_tbuc,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD8(LogCommitBlockSynchronization,
               int64_t(const std::string& path,
                       bool delete_block,
                       bool close_file,
                       const INode& inode,
                       const BlockInfoProto& last_bip,
                       const INode& old_inode,
                       std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD5(LogUpdateBlocksV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD7(LogCloseFileV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const BlockInfoProto* last_bip_tbc,
                       BlockID dropped_blk_id,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD6(LogReassignLeaseV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const std::string& lease_holder,
                       const std::string& new_holder,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD1(LogAllocateBlockIdV2,
               int64_t(uint64_t));
  MOCK_METHOD1(LogSetGenerationStampV1,
               int64_t(uint64_t));
  MOCK_METHOD2(LogSetGenerationStampV2,
               int64_t(uint64_t, bool));

  MOCK_METHOD7(LogPin,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const JobInfoOpBody& job,
                       const ManagedJobId& cancel_job_id,
                       const LogRpcInfo& log_rpc_info,
                       bool update_txid));
  MOCK_METHOD6(LogReconcileINodeAttrs,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const std::set<int64_t>& expired_ttl,
                       const std::set<int64_t>& new_ttl,
                       const std::vector<ManagedJobId>& cancel_job_id));
  MOCK_METHOD1(LogPersistJobInfo, int64_t(const JobInfoOpBody& job_info));

  MOCK_METHOD10(LogConcatV2,
                int64_t(const std::string& parent_path,
                        const std::string& target,
                        const std::vector<std::string>& srcs,
                        const INode& target_inode,
                        const INode& old_target_inode,
                        const std::vector<INode>& src_inodes,
                        const std::vector<BlockInfoProto>& src_bips,
                        const INode& parent,
                        uint64_t timestamp_in_ms,
                        const LogRpcInfo& log_rpc_info));

  MOCK_METHOD1(LogUpdateATimeProtos,
               int64_t(const ATimeToBeUpdateProtos& atimes));

  // Block related.
  MOCK_METHOD2(LogApproveUploadBlk,
               int64_t(const BlockInfoProto& bip,
                       const BlockInfoProto& old_bip));
  MOCK_METHOD2(LogPersistBlk,
               int64_t(const BlockInfoProto& bip,
                       const BlockInfoProto& old_bip));
  MOCK_METHOD1(LogDelDepringBlks, int64_t(const DepringBlksToBeDel& blks));
  MOCK_METHOD1(LogDelDepredBlks, int64_t(const DepredBlksToBeDel& blks));
  MOCK_METHOD1(LogFlushBlockInfoProtos, int64_t(const BlockInfoProtos& bips));

  // Dir tree related.
  MOCK_METHOD6(LogMkdirV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& parent,
                       const LogRpcInfo& log_rpc_info,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& parent_snaplog));
  MOCK_METHOD8(LogDeleteV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& parent,
                       uint64_t timestamp_in_ms,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog,
                       const SnapshotLog& parent_snaplog,
                       const LogRpcInfo& log_rpc_info));
  // XXX gmock cannot mock more than 10 args
  int64_t LogRenameOldV2(const std::string& src_path,
                         const std::string& dst_path,
                         const INode& src_inode,
                         const INode& dst_inode,
                         const INode& src_parent,
                         const INode& dst_parent,
                         uint64_t timestamp_in_ms,
                         const std::vector<INodeID>& src_ancestors_id,
                         const std::vector<INodeID>& dst_ancestors_id,
                         const SnapshotLog& src_inode_snaplog,
                         const SnapshotLog& src_parent_snaplog,
                         const SnapshotLog& dst_parent_snaplog,
                         const LogRpcInfo& log_rpc_info) {
    LOG(FATAL) << "mock function unimplemented";
    return kInvalidTxId;
  }
  int64_t LogRenameV2(const std::string& src_path,
                      const std::string& dst_path,
                      const INode& src_inode,
                      const INode& new_dst_inode,
                      const INode& src_parent,
                      const INode& dst_parent,
                      bool overwrite,
                      const INode* old_dst_inode,
                      bool move_to_recycle_bin,
                      const std::string* rb_path,
                      const INode* rb_inode,
                      const INode* rb_parent,
                      uint64_t timestamp_in_ms,
                      const std::vector<INodeID>& src_ancestors_id,
                      const std::vector<INodeID>& dst_ancestors_id,
                      const std::vector<INodeID>* rb_ancestors_id,
                      const SnapshotLog& src_inode_snaplog,
                      const SnapshotLog& dst_inode_snaplog,
                      const SnapshotLog& src_parent_snaplog,
                      const SnapshotLog& dst_parent_snaplog,
                      const SnapshotLog& rb_parent_snaplog,
                      const LogRpcInfo& log_rpc_info) override {
    LOG(FATAL) << "mock function unimplemented";
    return kInvalidTxId;
  }

  // Set* related.
  MOCK_METHOD5(LogSetReplicationV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       uint32_t replication,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD5(LogSetStoragePolicyV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       uint32_t policy_id,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD5(LogSetReplicaPolicyV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       int32_t policy_id,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD6(LogSetDirReplicaPolicyV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       int32_t policy_id,
                       const std::string& dc,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD4(LogSetQuotaV2,
               int64_t(const std::string& src,
                       uint64_t ns_quota,
                       uint64_t ds_quota,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD5(LogSetPermissionsV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       uint16_t permission,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD6(LogSetOwnerV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const std::string& username,
                       const std::string& groupname,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD6(LogSetTimesV2,
               int64_t(const std::string& src,
                       uint64_t mtime,
                       uint64_t atime,
                       const INode& inode,
                       const INode& old_inode,
                       const SnapshotLog& inode_snaplog));
  MOCK_METHOD3(LogSymlinkV2,
               int64_t(const std::string& path,
                       const INode& node,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD2(LogSetAclV2,
      int64_t(const std::string& src,
              const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
                  entries));
  MOCK_METHOD6(LogSetXAttrsV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const ::google::protobuf::RepeatedPtrField<
                           ::cloudfs::XAttrProto>& xattrs,
                       const SnapshotLog& inode_snaplog,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD6(LogRemoveXAttrsV2,
               int64_t(const std::string& path,
                       const INode& inode,
                       const INode& old_inode,
                       const ::google::protobuf::RepeatedPtrField<
                           ::cloudfs::XAttrProto>& xattrs,
                       const SnapshotLog& inode_snaplog,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD5(LogSetLifecyclePolicy,
               int64_t(const INode& inode,
                       const std::string& path,
                       uint64_t ts,
                       const cloudfs::LifecyclePolicyProto& policy,
                       const LogRpcInfo& rpc_info));
  MOCK_METHOD3(LogUnsetLifecyclePolicy,
               int64_t(const INodeID& id,
                       const std::string* path,
                       const LogRpcInfo& rpc_info));
  MOCK_METHOD1(LogSetAZBlacklist,
               int64_t(const std::string& azs));
  MOCK_METHOD0(LogAccSyncDummy, int64_t());
  MOCK_METHOD3(LogAccSyncListingBatchAdd,
               int64_t(const std::string&,
                       const INode&,
                       const std::vector<INodeWithBlocks>&));
  MOCK_METHOD4(LogAccSyncListingBatchUpdate,
               int64_t(const std::string&,
                       const INode&,
                       const std::vector<INode>&,
                       const std::vector<INode>&));
  MOCK_METHOD3(LogAccSyncUpdateINode,
               int64_t(const std::string&, const INode&, const INode&));
  MOCK_METHOD4(LogAccSyncAddFile,
               int64_t(const std::string&,
                       const INode&,
                       const INodeWithBlocks&,
                       const INode*));
  MOCK_METHOD4(
      LogAccPersistFile,
      int64_t(const std::string&, const INode&, const INode&, const INode&));
  MOCK_METHOD2(LogAccUpdateBlockInfo,
               int64_t(const BlockInfoProto& bip,
                       const BlockInfoProto& old_bip));
  MOCK_METHOD7(LogMergeBlocks,
               int64_t(const std::string& path,
                       const INode& inode,
                       const BlockInfoProto& bip,
                       const std::vector<BlockProto>& depred_blks,
                       const INode& old_inode,
                       const std::vector<INodeID>& ancestors_id,
                       const SnapshotLog& inode_snaplog));

  MOCK_METHOD2(LogAllowSnapshotV2,
               int64_t(const std::string&,
                       const INode&));
  MOCK_METHOD2(LogDisallowSnapshotV2,
               int64_t(const std::string&,
                       const INode&));
  MOCK_METHOD5(LogCreateSnapshotV2,
               int64_t(const std::string&,
                       const std::string&,
                       uint64_t,
                       const INode&,
                       uint64_t));
  MOCK_METHOD4(LogDeleteSnapshotV2,
               int64_t(const std::string&,
                       const std::string&,
                       uint64_t,
                       const INode&));
  MOCK_METHOD6(LogRenameSnapshotV2,
               int64_t(const std::string&,
                       const std::string&,
                       const std::string&,
                       uint64_t,
                       const INode&,
                       const SnapshotLog&));
  // -------- New Edit Log Ends.

  MOCK_METHOD4(LogOpenFile,
               int64_t(const std::string& path,
                       const INode& file,
                       bool overwrite,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD2(LogCloseFile,
               int64_t(const std::string& path, const INode& inode));
  MOCK_METHOD3(LogAddBlock,
               int64_t(const std::string& path,
                       const INode& file,
                       const LogRpcInfo& log_rpc_info));

  MOCK_METHOD3(LogUpdateBlocks,
               int64_t(const std::string& path,
                       const INode& file,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD2(LogMkDir, int64_t(const std::string& path, const INode& inode));
  MOCK_METHOD4(LogRename,
               int64_t(const std::string& src,
                       const std::string& dst,
                       uint64_t timestamp,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD5(LogRename,
               int64_t(const std::string& src,
                       const std::string& dst,
                       uint64_t timestamp,
                       const LogRpcInfo& log_rpc_info,
                       bool overwrite));
  MOCK_METHOD2(LogSetReplication,
               int64_t(const std::string& src, uint32_t replication));
  MOCK_METHOD2(LogSetStoragePolicy,
               int64_t(const std::string& src, uint32_t policy_id));
  MOCK_METHOD2(LogSetReplicaPolicy,
               int64_t(const std::string& src, int32_t id));
  MOCK_METHOD3(LogSetDirReplicaPolicy,
               int64_t(const std::string& src,
                       int32_t id,
                       const std::string& dc));
  MOCK_METHOD3(LogSetQuota,
               int64_t(const std::string& src,
                       uint64_t ns_quota,
                       uint64_t ds_quota));
  MOCK_METHOD2(LogSetPermissions,
               int64_t(const std::string& src, uint16_t permission));
  MOCK_METHOD3(LogSetOwner,
               int64_t(const std::string& src,
                       const std::string& username,
                       const std::string& groupname));
  MOCK_METHOD4(LogConcat,
               int64_t(const std::string& trg,
                       const std::vector<std::string>& srcs,
                       uint64_t timestamp,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD2(LogDelete,
               int64_t(const std::string& src, const LogRpcInfo& log_rpc_info));
  MOCK_METHOD1(LogGenerationStampV1, int64_t(uint64_t genstamp));
  MOCK_METHOD1(LogGenerationStampV2, int64_t(uint64_t genstamp));
  MOCK_METHOD1(LogAllocateBlockId, int64_t(uint64_t blockId));
  MOCK_METHOD2(LogBlockIdAndGSv2,
               int64_t(uint64_t* blockId, uint64_t* genstamp));
  MOCK_METHOD3(LogTimes,
               int64_t(const std::string& src, uint64_t mtime, uint64_t atime));
  MOCK_METHOD3(LogSymlink,
               int64_t(const std::string& path,
                       const INode& node,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD3(LogReassignLease,
               int64_t(const std::string& lease_holder,
                       const std::string& src,
                       const std::string& new_holder));
  MOCK_METHOD2(
      LogSetAcl,
      int64_t(const std::string& src,
              const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
                  entries));
  MOCK_METHOD3(
      LogSetXAttrs,
      int64_t(const std::string& src,
              const google::protobuf::RepeatedPtrField< ::cloudfs::XAttrProto>&
                  xattrs,
              const LogRpcInfo& log_rpc_info));
  MOCK_METHOD3(
      LogRemoveXAttrs,
      int64_t(const std::string& src,
              const google::protobuf::RepeatedPtrField< ::cloudfs::XAttrProto>&
                  xattrs,
              const LogRpcInfo& log_rpc_info));
  MOCK_METHOD1(
      LogAccessCounterSnapshot,
      int64_t(const cloudfs::fsimage::
                  AccessCounterSection_AccessCounterSnapshotProto &snapshot));
  MOCK_METHOD1(LogSetBlockPufsInfo, int64_t(const std::string &s));
  MOCK_METHOD1(LogDeleteDeprecatedBlockPufsInfo, int64_t(uint64_t blk_id));
  MOCK_METHOD1(LogAllowSnapshot,
               int64_t(const std::string&));
  MOCK_METHOD1(LogDisallowSnapshot,
               int64_t(const std::string&));
  MOCK_METHOD2(LogCreateSnapshot,
               int64_t(const std::string&,
                       const std::string&));
  MOCK_METHOD2(LogDeleteSnapshot,
               int64_t(const std::string&,
                       const std::string&));
  MOCK_METHOD3(LogRenameSnapshot,
               int64_t(const std::string&,
                       const std::string&,
                       const std::string&));

  MOCK_METHOD9(
      LogBatchCreateFile,
      int64_t(const std::vector<std::string>& paths,
              const std::vector<INode>& inodes,
              const std::vector<INode>& overwritten_inodes,
              const std::vector<BlockInfoProto>& overwritten_bips,
              const std::vector<BlockInfoProto>& add_block_bips,
              const std::vector<std::vector<std::string>>& bips_expected_locs,
              // other
              const INode& parent,
              uint64_t timestamp_in_ms,
              const LogRpcInfo& log_rpc_info));
  MOCK_METHOD9(LogBatchCompleteFile,
               int64_t(const std::vector<std::string>& paths,
                       const std::vector<INode>& complete_inodes,
                       const std::vector<INode>& deleted_inodes,
                       const std::vector<BlockInfoProto>& bips,
                       const std::vector<BlockInfoProto>& deleted_bips,
                       const std::vector<INode>& original_inodes,
                       // other
                       const INode& parent,
                       uint64_t timestamp_in_ms,
                       const LogRpcInfo& log_rpc_info));
  MOCK_METHOD6(LogBatchDeleteFile,
               int64_t(const std::vector<std::string>& paths,
                       const std::vector<INode>& inodes,
                       const std::vector<BlockInfoProto>& bips,
                       // other
                       const INode& parent,
                       uint64_t timestamp_in_ms,
                       const LogRpcInfo& log_rpc_info));

  MOCK_METHOD1(SyncImpl, void(bool force));
  void Sync(bool force = false) {
    SyncImpl(force);
  }
};

}  // namespace dancenn

#endif  // TEST_GMOCK_EDIT_LOG_SENDER_H_
