//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "ufs/ufs_auth_conf.h"

#include <gtest/gtest.h>

#include "gmock_assume_role_client.h"
#include "hdfs.pb.h"

using namespace testing;  // NOLINT(build/namespaces)

DECLARE_bool(run_ut);
DECLARE_bool(ufs_auth_enabled);
DECLARE_string(ufs_auth_policy);
DECLARE_string(ufs_auth_fixed_ak);
DECLARE_string(ufs_auth_fixed_sk);
DECLARE_string(ufs_auth_fixed_token);
DECLARE_string(ufs_auth_role_info);

namespace dancenn {

class UfsAuthConfTest : public ::testing::Test {};

TEST_F(UfsAuthConfTest, NotEnabled) {
  FLAGS_ufs_auth_enabled = false;

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.<PERSON>());
}

TEST_F(UfsAuthConfTest, InvalidPolicy) {
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "dummy";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_EQ(Code::kBadParameter, s.code());

  UfsAuthConf::Instance().TEST_Reset();
}

TEST_F(UfsAuthConfTest, Fixed) {
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "fixed";

  FLAGS_ufs_auth_fixed_ak = "dummyak";
  FLAGS_ufs_auth_fixed_sk = "dummysk";
  FLAGS_ufs_auth_fixed_token = "dummytoken";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.IsOK());

  auto&& conf = UfsAuthConf::Instance();
  ASSERT_EQ("dummyak", conf.fixed_ak());
  ASSERT_EQ("dummysk", conf.fixed_sk());
  ASSERT_EQ("dummytoken", conf.fixed_token());

  UfsAuthConf::Instance().TEST_Reset();
}

TEST_F(UfsAuthConfTest, RoleOneRole) {
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "role";

  // 原文：[{"AccountId":"**********","Role":"TestRoleForCFS"}]
  FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiMjEwMDA1MDkwMyIsIlJvbGUiOiJUZXN0Um9sZUZvckNGUyJ9XQ==";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.IsOK());

  auto&& conf = UfsAuthConf::Instance();
  auto&& roles = conf.role_infos();
  ASSERT_EQ(1, roles.size());
  ASSERT_EQ(roles[0].account_id, "**********");
  ASSERT_EQ(roles[0].role_name, "TestRoleForCFS");

  UfsAuthConf::Instance().TEST_Reset();
}

TEST_F(UfsAuthConfTest, RoleTwoRoles) {
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "role";

  // 原文：[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"TestRoleForMAAS"}]
  FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6Im1sX21hYXNfYm9lUk9MRU1hYVNCT0VUcnVzdFJvbGVGb3JDRlNQUEUifSx7IkFjY291bnRJZCI6IjIxMDAwNTA5MDMiLCJSb2xlIjoiVGVzdFJvbGVGb3JNQUFTIn1d";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.IsOK());

  auto&& conf = UfsAuthConf::Instance();
  auto&& roles = conf.role_infos();
  ASSERT_EQ(2, roles.size());
  ASSERT_EQ(roles[0].account_id, "");
  ASSERT_EQ(roles[0].role_name, "ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE");
  ASSERT_EQ(roles[1].account_id, "**********");
  ASSERT_EQ(roles[1].role_name, "TestRoleForMAAS");

  UfsAuthConf::Instance().TEST_Reset();
}

}
