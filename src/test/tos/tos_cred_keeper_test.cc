//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/ufs_auth_conf.h"

#include <gtest/gtest.h>

#include "gmock_assume_role_client.h"
#include "hdfs.pb.h"

using namespace testing;  // NOLINT(build/namespaces)
using namespace dancenn::iam; // NOLINT(build/namespaces)

DECLARE_bool(run_ut);
DECLARE_bool(use_fixed_ak);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_int32(namespace_type);
DECLARE_int32(iam_refresh_interval_secs);
DECLARE_int32(iam_refresh_interval_secs_on_error);
DECLARE_int32(internal_use_token_duration_secs);
DECLARE_int32(external_use_token_duration_secs);
DECLARE_string(iam_top_url);
DECLARE_string(cfs_service_region);
DECLARE_string(cfs_service_ak);
DECLARE_string(cfs_service_sk);
DECLARE_bool(ufs_auth_enabled);
DECLARE_string(ufs_auth_policy);
DECLARE_string(ufs_auth_fixed_ak);
DECLARE_string(ufs_auth_fixed_sk);
DECLARE_string(ufs_auth_fixed_token);
DECLARE_string(ufs_auth_role_info);

namespace dancenn {
class TosCredKeeperTest : public ::testing::Test {};

TEST_F(TosCredKeeperTest, DISABLED_FixedAkSK) {
  const std::string AK = "AKabcdefg";
  const std::string SK = "SKabcdefghijklmnopq12356==";

  FLAGS_use_fixed_ak = true;
  FLAGS_tos_access_key_id = AK;
  FLAGS_tos_secret_access_key = SK;

  TosCredKeeper k;
  auto s = k.Start();
  ASSERT_TRUE(s.IsOK());

  ASSERT_TRUE(k.ExternalCredential() == nullptr);

  auto inner = k.InnerCredential();
  ASSERT_EQ(inner->type, kVolcCredFixed);
  ASSERT_EQ(inner->ak, AK);
  ASSERT_EQ(inner->sk, SK);
  ASSERT_EQ(inner->token, "");

  s = k.Stop();
  ASSERT_TRUE(s.IsOK());
}

MATCHER(TrueMatcher, "") {
  return true;
}

TEST_F(TosCredKeeperTest, DISABLED_AssumeRoleInnerOnly) {
  FLAGS_use_fixed_ak = false;
  FLAGS_namespace_type = static_cast<int32_t>(cloudfs::NamespaceType::ACC_TOS);
  FLAGS_iam_refresh_interval_secs = 3;
  FLAGS_internal_use_token_duration_secs = 1000;

  auto raw_client = new GMockAssumeRoleClient();
  std::unique_ptr<dancenn::iam::AssumeRoleClient> aclient(raw_client);

  std::string AK1("AK");
  std::string SK1("SKfdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string TOKEN1("TOKENjlfdjksla");
  std::string AK2("AK2");
  std::string SK2("SK2fdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string TOKEN2("TOKEN2jlfdjksla");

  EXPECT_CALL(*raw_client,
              acquire(std::string(""),
                      FLAGS_internal_use_token_duration_secs,
                      TrueMatcher(),
                      TrueMatcher(),
                      TrueMatcher()))
      .Times(2)
      .WillOnce(DoAll(SetArgPointee<2>(AK1),
                      SetArgPointee<3>(SK1),
                      SetArgPointee<4>(TOKEN1),
                      Return(Status::OK())))
      .WillOnce(DoAll(SetArgPointee<2>(AK2),
                      SetArgPointee<3>(SK2),
                      SetArgPointee<4>(TOKEN2),
                      Return(Status::OK())));
  TosCredKeeper k(std::move(aclient), nullptr);
  auto s = k.Start();
  ASSERT_TRUE(s.IsOK());
  auto inner = k.InnerCredential();
  ASSERT_EQ(inner->type, kVolcCredSts);
  ASSERT_EQ(inner->ak, AK1);
  ASSERT_EQ(inner->sk, SK1);
  ASSERT_EQ(inner->token, TOKEN1);
  std::this_thread::sleep_for(std::chrono::seconds(4));

  inner = k.InnerCredential();
  ASSERT_EQ(inner->type, kVolcCredSts);
  ASSERT_EQ(inner->ak, AK2);
  ASSERT_EQ(inner->sk, SK2);
  ASSERT_EQ(inner->token, TOKEN2);

  s = k.Stop();
  ASSERT_TRUE(s.IsOK());
}

TEST_F(TosCredKeeperTest, DISABLED_AssumeRoleInnerExternal) {
  FLAGS_use_fixed_ak = false;
  FLAGS_namespace_type =
      static_cast<int32_t>(cloudfs::NamespaceType::TOS_MANAGED);
  FLAGS_iam_refresh_interval_secs = 3;
  FLAGS_internal_use_token_duration_secs = 1000;
  FLAGS_external_use_token_duration_secs = 100;

  auto raw_client = new GMockAssumeRoleClient();
  std::unique_ptr<dancenn::iam::AssumeRoleClient> aclient(raw_client);

  std::string AK1("AK");
  std::string SK1("SKfdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string TOKEN1("TOKENjlfdjksla");
  std::string AK2("AK2");
  std::string SK2("SK2fdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string TOKEN2("TOKEN2jlfdjksla");

  std::string EXAK1("EXAK");
  std::string EXSK1("EXSKfdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string EXTOKEN1("EXTOKENjlfdjksla");
  std::string EXAK2("EXAK2");
  std::string EXSK2("EXSK2fdsalkfjlasjfklsajfkldsajklfjdsaklfjklasd");
  std::string EXTOKEN2("EXTOKEN2jlfdjksla");

  EXPECT_CALL(*raw_client,
              acquire(std::string(""),
                      FLAGS_internal_use_token_duration_secs,
                      TrueMatcher(),
                      TrueMatcher(),
                      TrueMatcher()))
      .Times(2)
      .WillOnce(DoAll(SetArgPointee<2>(AK1),
                      SetArgPointee<3>(SK1),
                      SetArgPointee<4>(TOKEN1),
                      Return(Status::OK())))
      .WillOnce(DoAll(SetArgPointee<2>(AK2),
                      SetArgPointee<3>(SK2),
                      SetArgPointee<4>(TOKEN2),
                      Return(Status::OK())));
  EXPECT_CALL(*raw_client,
              acquire(TrueMatcher(),
                      FLAGS_external_use_token_duration_secs,
                      TrueMatcher(),
                      TrueMatcher(),
                      TrueMatcher()))
      .Times(2)
      .WillOnce(DoAll(SetArgPointee<2>(EXAK1),
                      SetArgPointee<3>(EXSK1),
                      SetArgPointee<4>(EXTOKEN1),
                      Return(Status::OK())))
      .WillOnce(DoAll(SetArgPointee<2>(EXAK2),
                      SetArgPointee<3>(EXSK2),
                      SetArgPointee<4>(EXTOKEN2),
                      Return(Status::OK())));

  TosCredKeeper k(std::move(aclient), nullptr);
  auto s = k.Start();
  ASSERT_TRUE(s.IsOK());
  auto inner = k.InnerCredential();
  ASSERT_EQ(inner->type, kVolcCredSts);
  ASSERT_EQ(inner->ak, AK1);
  ASSERT_EQ(inner->sk, SK1);
  ASSERT_EQ(inner->token, TOKEN1);

  auto external = k.ExternalCredential();
  ASSERT_EQ(external->type, kVolcCredSts);
  ASSERT_EQ(external->ak, EXAK1);
  ASSERT_EQ(external->sk, EXSK1);
  ASSERT_EQ(external->token, EXTOKEN1);

  std::this_thread::sleep_for(std::chrono::seconds(4));

  inner = k.InnerCredential();
  ASSERT_EQ(inner->type, kVolcCredSts);
  ASSERT_EQ(inner->ak, AK2);
  ASSERT_EQ(inner->sk, SK2);
  ASSERT_EQ(inner->token, TOKEN2);

  external = k.ExternalCredential();
  ASSERT_EQ(external->type, kVolcCredSts);
  ASSERT_EQ(external->ak, EXAK2);
  ASSERT_EQ(external->sk, EXSK2);
  ASSERT_EQ(external->token, EXTOKEN2);

  s = k.Stop();
  ASSERT_TRUE(s.IsOK());
}

TEST_F(TosCredKeeperTest, DISABLED_RealAssumeTwoRole) {
  FLAGS_iam_top_url = "open.volcengineapi.com";
  FLAGS_cfs_service_region = "cn-beijing";
  FLAGS_cfs_service_ak = "";
  FLAGS_cfs_service_sk = "";
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "role";

  // Not working, why?
  // 原文：[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"TestRoleForMAAS"}]
  // FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6Im1sX21hYXNfYm9lUk9MRU1hYVNCT0VUcnVzdFJvbGVGb3JDRlNQUEUifSx7IkFjY291bnRJZCI6IjIxMDAwNTA5MDMiLCJSb2xlIjoiVGVzdFJvbGVGb3JNQUFTIn1d";

  // MAAS 测试账号
  // 原文：[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"ServiceRoleForMLMaaSBOE"}]
  FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6Im1sX21hYXNfYm9lUk9MRU1hYVNCT0VUcnVzdFJvbGVGb3JDRlNQUEUifSx7IkFjY291bnRJZCI6IjIxMDAwMDA4MjUiLCJSb2xlIjoiU2VydmljZVJvbGVGb3JNTE1hYVNCT0UifV0=";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.IsOK());

  auto assume_client = std::make_unique<AssumeRoleClient>();
  TosCredKeeper k(std::move(assume_client), nullptr);
  s = k.TEST_FetchOnce();
  ASSERT_TRUE(s.IsOK());
  LOG(INFO) << "AK: " << k.InnerCredential()->ak;
  LOG(INFO) << "SK: " << k.InnerCredential()->sk;
  LOG(INFO) << "TOKEN: " << k.InnerCredential()->token;
}

TEST_F(TosCredKeeperTest, DISABLED_RealAssumeTwoRoleInnerTop) {
  FLAGS_iam_top_url = "https://sts.ap-southeast-3.innerapi.commonpcs.com";
  FLAGS_cfs_service_region = "ap-southeast-3";
  FLAGS_cfs_service_ak = "<replace-with-cfs-or-cfs-ppe-service-ak-innertop-or-openapi>";
  FLAGS_cfs_service_sk = "<replace-with-cfs-or-cfs-ppe-service-ak-innertop-or-openapi>";
  FLAGS_ufs_auth_enabled = true;
  FLAGS_ufs_auth_policy = "role";

  // Not working, why?
  // 原文：[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"TestRoleForMAAS"}]
  // FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6Im1sX21hYXNfYm9lUk9MRU1hYVNCT0VUcnVzdFJvbGVGb3JDRlNQUEUifSx7IkFjY291bnRJZCI6IjIxMDAwNTA5MDMiLCJSb2xlIjoiVGVzdFJvbGVGb3JNQUFTIn1d";

  // MAAS 测试账号
  // 原文：[{"AccountId":"","Role":"ml_maas_boeROLEMaaSBOETrustRoleForCFSPPE"},{"AccountId":"**********","Role":"ServiceRoleForMLMaaSBOE"}]
  // FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6Im1sX21hYXNfYm9lUk9MRU1hYVNCT0VUcnVzdFJvbGVGb3JDRlNQUEUifSx7IkFjY291bnRJZCI6IjIxMDAwMDA4MjUiLCJSb2xlIjoiU2VydmljZVJvbGVGb3JNTE1hYVNCT0UifV0=";

  // 原文：[{"AccountId":"","Role":"arkROLEArkTrustRoleForCFS"},{"AccountId":"**********","Role":"ServiceRoleForArk"}]
  FLAGS_ufs_auth_role_info = "W3siQWNjb3VudElkIjoiIiwiUm9sZSI6ImFya1JPTEVBcmtUcnVzdFJvbGVGb3JDRlMifSx7IkFjY291bnRJZCI6IjIxMDA0NDQ5MjIiLCJSb2xlIjoiU2VydmljZVJvbGVGb3JBcmsifV0=";

  auto s = UfsAuthConf::Instance().Init();
  ASSERT_TRUE(s.IsOK());

  TosCredKeeper k;
  s = k.TEST_FetchOnce();
  ASSERT_TRUE(s.IsOK());
  LOG(INFO) << "AK: " << k.InnerCredential()->ak;
  LOG(INFO) << "SK: " << k.InnerCredential()->sk;
  LOG(INFO) << "TOKEN: " << k.InnerCredential()->token;
}

}  // namespace dancenn