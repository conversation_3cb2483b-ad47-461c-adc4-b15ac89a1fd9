//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include <gmock/gmock.h>

#include "iam/assume_role_client.h"

namespace dancenn {
class GMockAssumeRoleClient : public dancenn::iam::AssumeRoleClient {
 public:
  virtual ~GMockAssumeRoleClient() {
  }

  MOCK_METHOD(Status,
              acquire,
              (const std::string& policy,
               int32_t token_duration_secs,
               std::string* ak,
               std::string* sk,
               std::string* token),
              (const override));
};
}  // namespace dancenn