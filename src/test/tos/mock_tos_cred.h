//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#pragma once

#include "ufs/tos/tos_cred.h"

namespace dancenn {

class GMockTosCredentialProvider : public TosCredentialProvider {
 public:
  MOCK_METHOD0(GetCredential, std::shared_ptr<VolcCredential>());
  MOCK_METHOD1(RegisterUpdateCallback, uint64_t(CredentialUpdateCb));
  MOCK_METHOD1(UnregisterUpdateCallback, void(uint64_t));
};

} // namespace dancenn
