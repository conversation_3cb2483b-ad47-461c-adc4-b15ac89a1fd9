// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "service/client_namenode_service.h"

#include <ClientNamenodeProtocol.pb.h>
#include <cnetpp/base/csonpp.h>
#include <glog/logging.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <xattr.pb.h>

#include <chrono>
#include <memory>
#include <random>
#include <string>
#include <thread>

#include "ProtobufRpcEngine.pb.h"
#include "RpcHeader.pb.h"
#include "base/closure.h"
#include "base/file_utils.h"
#include "base/path_util.h"
#include "cnetpp/base/end_point.h"
#include "cnetpp/base/ip_address.h"
#include "cnetpp/tcp/connection_factory.h"
#include "cnetpp/tcp/tcp_connection.h"
#include "google/protobuf/stubs/common.h"
#include "rpc/rpc_client_connection.h"
#include "rpc/rpc_controller.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(run_ut);
DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_string(all_datacenters);
DECLARE_int32(bg_deletion_num_in_batch);
DECLARE_uint32(dfs_summary_min_depth);
DECLARE_bool(force_hyperblock_on_diffrent_dn);
DECLARE_bool(namespace_read_full_detail_blocks);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_default_policy_enable);
DECLARE_uint32(recycle_bin_default_policy_time_sec);
DECLARE_int32(dfs_meta_scan_interval_sec);
DECLARE_bool(audit_log_enabled);
DECLARE_string(nameservice);

namespace dancenn {

namespace {
  auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
  auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
  auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
  auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
  auto default_blockmap_num_bucket_each_slice =
      FLAGS_blockmap_num_bucket_each_slice;
  auto default_blockmap_num_slice = FLAGS_blockmap_num_slice;
  auto default_dfs_replication_min = FLAGS_dfs_replication_min;
}

class ClientNamenodeServiceTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_lease_expired_soft_limit_ms = 200;
    FLAGS_lease_expired_hard_limit_ms = 400;
    FLAGS_datanode_keep_alive_timeout_sec = 1000;
    FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
    FLAGS_blockmap_num_bucket_each_slice = 1;
    FLAGS_blockmap_num_slice = 1;
    FLAGS_dfs_replication_min = 1;
    FLAGS_bytecool_feature_enabled = true;
    FLAGS_dfs_meta_scan_interval_sec = 5;
    FLAGS_recycle_bin_enable = false;
    FLAGS_recycle_bin_default_policy_enable = false;
    FLAGS_recycle_bin_default_policy_time_sec = 10;
    FLAGS_audit_log_enabled = false;

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    auto edit_log_ctx = CreateContext();
    retry_cache_ = std::make_unique<dancenn::RetryCache>();
    MockFSImageTransfer(db_path_).Transfer();
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                UfsEnv::Create(),
                                retry_cache_.get()));
    ha_state_ = std::make_shared<MockHAState>();
    safemode_ = std::make_shared<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->Start();
    ns_->StartActive();

    // add a datanode to the cluster
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = new MockEditLogSender(edit_log_ctx, last_tx_id);
    ns_->TestOnlySetEditLogSender(std::unique_ptr<EditLogSenderBase>(sender));

    ns_->StopBGDeletionWorker();
    ns_->StopLeaseMonitor();

    service_ = std::make_unique<ClientNamenodeService>(datanode_manager_,
                                                       block_manager_,
                                                       ns_,
                                                       safemode_.get(),
                                                       retry_cache_.get());
  }

  void TearDown() override {
    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_blockmap_num_bucket_each_slice =
        default_blockmap_num_bucket_each_slice;
    FLAGS_blockmap_num_slice = default_blockmap_num_slice;
    FLAGS_dfs_replication_min = default_dfs_replication_min;

    if (!skip_stop_ns_) {
      ns_->StopActive();
      ns_.reset();
    }
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  CreateRequestProto MakeCreateRequest(const std::string& path) {
    CreateRequestProto create_request;
    create_request.set_src(path);
    create_request.mutable_masked()->set_perm(0);
    create_request.set_clientname("client");
    create_request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    create_request.set_createparent(true);
    create_request.set_replication(1);
    create_request.set_blocksize(128 * 1024 * 1024);
    return create_request;
  }

  cloudfs::DeleteRequestProto MakeDeleteRequest(const std::string& path) {
    cloudfs::DeleteRequestProto request;
    request.set_src(path);
    request.set_recursive(true);
    return request;
  }

  AppendRequestProto MakeAppendRequest(const std::string& path) {
    AppendRequestProto req;
    req.set_src(path);
    req.set_clientname("client");
    return req;
  }

  CompleteRequestProto MakeCompleteRequest(const std::string& path) {
    CompleteRequestProto req;
    req.set_src(path);
    req.set_clientname("client");
    return req;
  }

  RenameRequestProto MakeRenameRequest(const std::string& src,
                                       const std::string& dst) {
    RenameRequestProto req;
    req.set_src(src);
    req.set_dst(dst);
    return req;
  }

  Rename2RequestProto MakeRename2Request(const std::string& src,
                                         const std::string& dst,
                                         bool overwrite) {
    Rename2RequestProto req;
    req.set_src(src);
    req.set_dst(dst);
    req.set_overwritedest(overwrite);
    return req;
  }

  SetXAttrRequestProto MakeSetXAttrRequest(const std::string& path,
                                           const XAttrProto& xattr,
                                           uint32_t flag) {
    SetXAttrRequestProto req;
    req.set_src(path);
    req.mutable_xattr()->CopyFrom(xattr);
    req.set_flag(flag);
    return req;
  }

  RemoveXAttrRequestProto MakeRemoveXAttrRequest(const std::string& path,
                                                 const XAttrProto& xattr) {
    RemoveXAttrRequestProto req;
    req.set_src(path);
    req.mutable_xattr()->CopyFrom(xattr);
    return req;
  }

  CreateSymlinkRequestProto MakeCreateSymlinkRequest(const std::string& target,
                                                     const std::string& link,
                                                     bool create_parent) {
    CreateSymlinkRequestProto req;
    req.set_target(target);
    req.set_link(link);
    req.set_allocated_dirperm(new cloudfs::FsPermissionProto);
    req.set_createparent(create_parent);
    return req;
  }

  std::shared_ptr<RpcController> MakeRpcController(
      const std::string& client_id,
      int32_t call_id,
      const std::string& client_address,
      const std::string& method) {
    auto controller = std::shared_ptr<RpcController>(new RpcController());
    auto header = std::make_shared<cloudfs::RpcRequestHeaderProto>();
    header->set_clientid(client_id);
    header->set_callid(call_id);
    header->set_clientaddress(client_address);
    controller->set_rpc_request_header(header);
    auto request_header =
        std::make_shared<cloudfs::RequestHeaderProto>();
    request_header->set_methodname(method);
    controller->set_request_header(request_header);
    cnetpp::base::IPAddress ip{client_address};
    cnetpp::base::EndPoint endpoint{ip, 8080};
    cnetpp::tcp::ConnectionFactory cf;
    auto tcp_connection = std::dynamic_pointer_cast<cnetpp::tcp::TcpConnection>(
        cf.CreateConnection(nullptr, 0, false));
    tcp_connection->set_remote_end_point(endpoint);
    auto rpc_connection =
        std::make_shared<RpcClientConnection>(tcp_connection,
                                              "ClientNamenodeProtocol",
                                              1,
                                              client_id,
                                              "tiger",
                                              endpoint);
    controller->set_rpc_connection(rpc_connection);

    return controller;
  }

  std::shared_ptr<EditLogOp> MakeDeleteOp(const std::string& path,
                                          const std::string& client_id,
                                          int32_t call_id,
                                          int64_t txid) {
    auto op = std::make_shared<OpDelete>();
    op->SetOpCode(OP_DELETE);
    op->set_path(path);
    uint64_t time_now_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count();
    op->set_clientId(client_id);
    op->set_callId(call_id);
    op->SetTxid(txid);
    return op;
  }

  AddBlockRequestProto MakeAddBlockRequest() {
    AddBlockRequestProto add_request;
    add_request.set_src("");
    add_request.set_clientname("client");
    return add_request;
  }

  std::unique_ptr<ClientNamenodeService> service_;

  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<SafeModeBase> safemode_;
  std::shared_ptr<RetryCache> retry_cache_;
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  bool skip_stop_ns_ = false;
};

TEST_F(ClientNamenodeServiceTest, TestLocatedBlockProtoToJsonObject) {
  {
    ::cloudfs::LocatedBlockProto blk;
    blk.mutable_b()->set_poolid("This is a pool id");
    blk.mutable_b()->set_blockid(100000);
    blk.mutable_b()->set_numbytes(10);
    blk.mutable_b()->set_generationstamp(1000000);
    blk.set_offset(10);
    auto loc = blk.add_locs();
    loc->set_dfsused(10);
    loc->set_capacity(10);
    loc->mutable_id()->set_ipaddr("***********");
    blk.set_corrupt(false);
    auto res = ClientNamenodeService::LocatedBlockProtoToJsonObject(blk);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsObject());
  }
  {
    ::cloudfs::LocatedBlockProto blk;
    blk.mutable_b()->set_poolid("This is a pool id");
    blk.mutable_b()->set_blockid(100000);
    blk.mutable_b()->set_generationstamp(1000000);
    blk.set_offset(10);
    blk.set_corrupt(false);
    auto res = ClientNamenodeService::LocatedBlockProtoToJsonObject(blk);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsObject());
  }
  {
    ::cloudfs::LocatedBlockProto blk;
    blk.mutable_b()->set_poolid("This is a pool id");
    blk.mutable_b()->set_blockid(100000);
    blk.mutable_b()->set_generationstamp(1000000);
    blk.set_offset(10);
    auto loc = blk.add_locs();
    loc->set_dfsused(10);
    loc->set_capacity(10);
    loc->mutable_id()->set_ipaddr("***********");
    loc = blk.add_locs();
    loc->set_dfsused(10);
    loc->set_capacity(10);
    loc->mutable_id()->set_ipaddr("***********");
    blk.set_corrupt(false);
    auto res = ClientNamenodeService::LocatedBlockProtoToJsonObject(blk);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsObject());
  }
}

TEST_F(ClientNamenodeServiceTest, TestFavoredNodesToJsonArray) {
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    auto n = req.add_excludenodes();
    n->set_dfsused(10);
    n->set_capacity(10);
    n->mutable_id()->set_ipaddr("***********");
    auto res = ClientNamenodeService::ExcludedNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    auto res = ClientNamenodeService::ExcludedNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    auto n = req.add_excludenodes();
    n->set_dfsused(10);
    n->set_capacity(10);
    n->mutable_id()->set_ipaddr("***********");
    n = req.add_excludenodes();
    n->set_dfsused(10);
    n->set_capacity(10);
    n->mutable_id()->set_ipaddr("***********");
    auto res = ClientNamenodeService::ExcludedNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
}

TEST_F(ClientNamenodeServiceTest, TestExcludedNodesToJsonArray) {
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    *(req.add_favorednodes()) = "***********";
    auto res = ClientNamenodeService::FavoredNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    *(req.add_favorednodes()) = "***********";
    *(req.add_favorednodes()) = "***********";
    auto res = ClientNamenodeService::FavoredNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
  {
    ::cloudfs::AddBlockRequestProto req;
    *(req.mutable_src()) = "/a/b/c";
    *(req.mutable_clientname()) = "client-name";
    auto res = ClientNamenodeService::FavoredNodesToJsonArray(&req);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsArray());
  }
}

TEST_F(ClientNamenodeServiceTest, TestHdfsFileStatusProtoToJsonObject) {
  {
    ::cloudfs::HdfsFileStatusProto fs;
    fs.set_filetype(::cloudfs::HdfsFileStatusProto_FileType_IS_DIR);
    fs.set_path("/a/b/c");
    fs.set_length(0);
    fs.mutable_permission()->set_perm(0755);
    fs.set_owner("root");
    fs.set_group("root");
    fs.set_modification_time(1000000000);
    fs.set_access_time(1000000000);
    auto res = ClientNamenodeService::HdfsFileStatusProtoToJsonObject(fs);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsObject());
  }
  {
    ::cloudfs::HdfsFileStatusProto fs;
    fs.set_filetype(::cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
    fs.set_path("/a/b/c");
    fs.set_length(100);
    fs.mutable_permission()->set_perm(0755);
    fs.set_owner("root");
    fs.set_group("root");
    fs.set_modification_time(1000000000);
    fs.set_access_time(1000000000);
    auto res = ClientNamenodeService::HdfsFileStatusProtoToJsonObject(fs);
    LOG(INFO) << "res: " << res;
    cnetpp::base::Value value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(res, &value));
    ASSERT_TRUE(value.IsObject());
  }
}

TEST_F(ClientNamenodeServiceTest, ProtobufShortDebugStringBenchmark) {
  static const uint64_t kBlockSize = 512 * 1024 * 1024;
  static const uint64_t kBlockCount = 100;
  static const uint64_t kFileLength = kBlockCount * kBlockSize;
  cloudfs::HdfsFileStatusProto fs;
  fs.set_filetype(cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
  fs.set_path("/data/kafka_dump/xxxxx_account_original_ban_model_features/"
      "_DUMP_TEMPORARY/********");
  fs.set_length(kFileLength);
  fs.mutable_permission()->set_perm(0755);
  fs.set_owner("tiger");
  fs.set_group("tiger");
  fs.set_modification_time(1000000);
  fs.set_access_time(1000000);
  fs.set_block_replication(3);
  fs.set_blocksize(kBlockSize);
  fs.set_fileid(100000);
  fs.mutable_locations()->set_filelength(kFileLength);
  for (int i = 0; i < kBlockCount; ++i) {
    auto blk = fs.mutable_locations()->add_blocks();
    blk->mutable_b()->set_poolid("19094ue945839ufjaskl;djajf;ladj");
    blk->mutable_b()->set_blockid(************ + i);
    blk->mutable_b()->set_numbytes(kBlockSize);
    blk->mutable_b()->set_generationstamp(100);
    blk->set_offset(i * kBlockSize);
    for (int j = 0; j < 3; ++j) {
      auto loc = blk->add_locs();
      loc->mutable_id()->set_ipaddr("***********");
      loc->mutable_id()->set_hostname("n10-010-010.byted.org");
      loc->mutable_id()->set_datanodeuuid("dklajfldjf;ajf;djf;ad;ad");
      loc->mutable_id()->set_xferport(5060);
      loc->mutable_id()->set_infoport(5061);
      loc->mutable_id()->set_ipcport(5062);
      loc->mutable_id()->set_infosecureport(5063);
      loc->set_dfsused(345928345702375);
      loc->set_capacity(384921385428253);
      loc->set_remaining(94020954280);
      loc->set_blockpoolused(58902850240);
      loc->set_xceivercount(1000000);
      loc->set_nondfsused(58923578409270);
    }
    blk->set_corrupt(false);
    blk->mutable_blocktoken()->set_kind("");
    blk->mutable_blocktoken()->set_service("");
    blk->mutable_blocktoken()->set_password("");
    blk->mutable_blocktoken()->set_identifier("");
    blk->add_iscached(true);
    blk->add_storagetypes(cloudfs::StorageTypeProto::DISK);
    *(blk->add_storageids()) = "w41093784502190eiwhjflkahjfads";
  }
  fs.mutable_locations()->set_underconstruction(true);
  fs.mutable_locations()->mutable_lastblock()->CopyFrom(
      fs.mutable_locations()->blocks(
        fs.mutable_locations()->blocks_size() - 1));
  int64_t avg = 0;
  const int64_t kIterationCount = 100;
  for (int i = 0; i < kIterationCount; ++i) {
    auto start = std::chrono::system_clock::now();
    auto debug = fs.ShortDebugString();
    auto end = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(
        end -start).count();
    VLOG(10) << "ShortDebugString time cost: " << duration << "(ns)";
    debug.append(1, '0');
    VLOG(10) << "string: " << debug;
    avg += duration;
  }
  LOG(INFO) << "avg: " << avg / kIterationCount << "(ns)";
}

TEST_F(ClientNamenodeServiceTest, RetryDeleteTest) {
  using cloudfs::DeleteRequestProto;
  using cloudfs::DeleteResponseProto;

  const std::string path = "/User/tiger/test_file";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // delete file not exist
  {
    DeleteRequestProto delete_request = MakeDeleteRequest(path);
    DeleteResponseProto delete_response;
    delete_response.set_result(true);
    auto controller =
        MakeRpcController(client_id, call_id, client_address, "delete");
    controller->set_request(new DeleteRequestProto(delete_request));
    SynchronizedRpcClosure rpc_done;
    service_->Delete(
        controller.get(), &delete_request, &delete_response, &rpc_done);
    rpc_done.Await();
    ASSERT_TRUE(rpc_done.status().IsOK());
    ASSERT_FALSE(delete_response.result());
    auto cache_entry = retry_cache_->GetCacheEntry(client_id, call_id);
    ASSERT_TRUE(cache_entry != nullptr);
    ASSERT_TRUE(cache_entry->IsCompleted());
    ASSERT_FALSE(cache_entry->GetStatus().IsOK());
    ASSERT_TRUE(cache_entry->GetPayload() != nullptr);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // create file
  {
    int32_t callid = call_id + 1;
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response =
        CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, callid, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry delete
  // this request is expected to failed(hit retry cache) though the file already
  // created.
  {
    DeleteRequestProto delete_request = MakeDeleteRequest(path);
    DeleteResponseProto delete_response;
    delete_response.set_result(true);
    auto controller =
        MakeRpcController(client_id, call_id, client_address, "delete");
    controller->set_request(new DeleteRequestProto(delete_request));
    SynchronizedRpcClosure rpc_done;
    service_->Delete(
        controller.get(), &delete_request, &delete_response, &rpc_done);
    rpc_done.Await();
    ASSERT_TRUE(rpc_done.status().IsOK());
    ASSERT_FALSE(delete_response.result());
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryCreateTest) {
  const std::string path = "/retry_create_test/test_file";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // create file
  int32_t call_id_1 = call_id + 1;
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // create file again
  int32_t call_id_2 = call_id + 2;
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_2, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former creation
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter creation
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_2, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryAppendTest) {
  const std::string path_1 = "/retry_append_test/test_file_1";
  const std::string path_2 = "/retry_append_test/test_file_2";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // prepare
  int32_t call_id_1 = call_id + 1;
  int32_t call_id_2 = call_id + 2;
  {
    CreateRequestProto create_request = MakeCreateRequest(path_1);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_1;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_1);
    rpc_done_1.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    CompleteRequestProto complete_request = MakeCompleteRequest(path_1);
    CompleteResponseProto complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_2, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_2;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_2);
    rpc_done_2.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // append
  int32_t call_id_3 = call_id + 3;
  {
    AppendRequestProto append_request = MakeAppendRequest(path_1);
    AppendResponseProto append_response = AppendResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "append");
    controller->set_request(new AppendRequestProto(append_request));
    SynchronizedRpcClosure rpc_done;
    service_->append(
        controller.get(), &append_request, &append_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // append inexistent file
  int32_t call_id_4 = call_id + 4;
  {
    AppendRequestProto append_request = MakeAppendRequest(path_2);
    AppendResponseProto append_response = AppendResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "append");
    controller->set_request(new AppendRequestProto(append_request));
    SynchronizedRpcClosure rpc_done;
    service_->append(
        controller.get(), &append_request, &append_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former append
  {
    AppendRequestProto append_request = MakeAppendRequest(path_1);
    AppendResponseProto append_response = AppendResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "append");
    controller->set_request(new AppendRequestProto(append_request));
    SynchronizedRpcClosure rpc_done;
    service_->append(
        controller.get(), &append_request, &append_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter append
  {
    AppendRequestProto append_request = MakeAppendRequest(path_1);
    AppendResponseProto append_response = AppendResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "append");
    controller->set_request(new AppendRequestProto(append_request));
    SynchronizedRpcClosure rpc_done;
    service_->append(
        controller.get(), &append_request, &append_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryRenameTest) {
  const std::string path_1 = "/retry_rename_test/test_file_1";
  const std::string path_2 = "/retry_rename_test/test_file_2";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // prepare
  int32_t call_id_1 = call_id + 1;
  int32_t call_id_2 = call_id + 2;
  {
    CreateRequestProto create_request = MakeCreateRequest(path_1);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_1;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_1);
    rpc_done_1.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    CompleteRequestProto complete_request = MakeCompleteRequest(path_1);
    CompleteResponseProto complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_2, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_2;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_2);
    rpc_done_2.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // rename
  int32_t call_id_3 = call_id + 3;
  {
    RenameRequestProto rename_request = MakeRenameRequest(path_1, path_2);
    RenameResponseProto rename_response = RenameResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "rename");
    controller->set_request(new RenameRequestProto(rename_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename(
        controller.get(), &rename_request, &rename_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // rename inexistent file
  int32_t call_id_4 = call_id + 4;
  {
    RenameRequestProto rename_request = MakeRenameRequest(path_1, path_2);
    RenameResponseProto rename_response = RenameResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "rename");
    controller->set_request(new RenameRequestProto(rename_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename(
        controller.get(), &rename_request, &rename_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former renaming
  {
    RenameRequestProto rename_request = MakeRenameRequest(path_1, path_2);
    RenameResponseProto rename_response = RenameResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "rename");
    controller->set_request(new RenameRequestProto(rename_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename(
        controller.get(), &rename_request, &rename_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter renaming
  {
    RenameRequestProto rename_request = MakeRenameRequest(path_1, path_2);
    RenameResponseProto rename_response = RenameResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "rename");
    controller->set_request(new RenameRequestProto(rename_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename(
        controller.get(), &rename_request, &rename_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryRename2Test) {
  const std::string path_1 = "/retry_rename2_test/test_file_1";
  const std::string path_2 = "/retry_rename2_test/test_file_2";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // prepare src & dst
  int32_t call_id_1 = call_id + 1;
  int32_t call_id_2 = call_id + 2;
  int32_t call_id_3 = call_id + 3;
  int32_t call_id_4 = call_id + 4;
  {
    // create src
    CreateRequestProto create_request = MakeCreateRequest(path_1);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_1;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_1);
    rpc_done_1.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    // complete src
    CompleteRequestProto complete_request = MakeCompleteRequest(path_1);
    CompleteResponseProto complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_2, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_2;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_2);
    rpc_done_2.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    // create dst
    create_request = MakeCreateRequest(path_2);
    create_response = CreateResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_3, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_3;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_3);
    rpc_done_3.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    // complete dst
    complete_request = MakeCompleteRequest(path_2);
    complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_4, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_4;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_4);
    rpc_done_4.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // rename2 without overwrite flag
  int32_t call_id_5 = call_id + 5;
  {
    Rename2RequestProto rename2_request = MakeRename2Request(path_1, path_2, false);
    Rename2ResponseProto rename2_response = Rename2ResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_5, client_address, "rename2");
    controller->set_request(new Rename2RequestProto(rename2_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename2(
        controller.get(), &rename2_request, &rename2_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // rename2 with overwrite flag
  int32_t call_id_6 = call_id + 6;
  {
    Rename2RequestProto rename2_request = MakeRename2Request(path_1, path_2, true);
    Rename2ResponseProto rename2_response = Rename2ResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_6, client_address, "rename2");
    controller->set_request(new Rename2RequestProto(rename2_request));
    SynchronizedRpcClosure rpc_done;
    service_->rename2(
        controller.get(), &rename2_request, &rename2_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former renaming
  {
    Rename2RequestProto rename2_request = MakeRename2Request(path_1, path_2, false);
    Rename2ResponseProto rename2_response = Rename2ResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_5, client_address, "rename2");
    controller->set_request(new Rename2RequestProto(rename2_request));
    SynchronizedRpcClosure rpc_done_5;
    service_->rename2(
        controller.get(), &rename2_request, &rename2_response, &rpc_done_5);
    rpc_done_5.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter renaming
  {
    Rename2RequestProto rename2_request = MakeRename2Request(path_1, path_2, false);
    Rename2ResponseProto rename2_response = Rename2ResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_6, client_address, "rename2");
    controller->set_request(new Rename2RequestProto(rename2_request));
    SynchronizedRpcClosure rpc_done_6;
    service_->rename2(
        controller.get(), &rename2_request, &rename2_response, &rpc_done_6);
    rpc_done_6.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryCreateSymlinkTest) {
  const std::string target = "/retry_create_symlink_test/test_target";
  const std::string link = "/retry_create_symlink_test/test_link";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  FLAGS_dfs_symlinks_enabled = true;

  // create symlink without create_parent flag
  int32_t call_id_1 = call_id + 1;
  {
    CreateSymlinkRequestProto symlink_request = MakeCreateSymlinkRequest(target, link, false);
    CreateSymlinkResponseProto symlink_response = CreateSymlinkResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "createSymlink");
    controller->set_request(new CreateSymlinkRequestProto(symlink_request));
    SynchronizedRpcClosure rpc_done;
    service_->createSymlink(
        controller.get(), &symlink_request, &symlink_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // create symlink with create_parent flag
  int32_t call_id_2 = call_id + 2;
  {
    CreateSymlinkRequestProto symlink_request = MakeCreateSymlinkRequest(target, link, true);
    CreateSymlinkResponseProto symlink_response = CreateSymlinkResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_2, client_address, "createSymlink");
    controller->set_request(new CreateSymlinkRequestProto(symlink_request));
    SynchronizedRpcClosure rpc_done;
    service_->createSymlink(
        controller.get(), &symlink_request, &symlink_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former creation
  {
    CreateSymlinkRequestProto symlink_request= MakeCreateSymlinkRequest(target, link, false);
    CreateSymlinkResponseProto symlink_response = CreateSymlinkResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "createSymlink");
    controller->set_request(new CreateSymlinkRequestProto(symlink_request));
    SynchronizedRpcClosure rpc_done;
    service_->createSymlink(
        controller.get(), &symlink_request, &symlink_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter creation
  {
    CreateSymlinkRequestProto symlink_request = MakeCreateSymlinkRequest(target, link, true);
    CreateSymlinkResponseProto symlink_response = CreateSymlinkResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_2, client_address, "createSymlink");
    controller->set_request(new CreateSymlinkRequestProto(symlink_request));
    SynchronizedRpcClosure rpc_done;
    service_->createSymlink(
        controller.get(), &symlink_request, &symlink_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetrySetXAttrTest) {
  const std::string path = "/retry_setxattr_test/test_file";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // prepare
  int32_t call_id_1 = call_id + 1;
  int32_t call_id_2 = call_id + 2;
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_1;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_1);
    rpc_done_1.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    CompleteRequestProto complete_request = MakeCompleteRequest(path);
    CompleteResponseProto complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_2, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_2;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_2);
    rpc_done_2.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // set xattr
  int32_t call_id_3 = call_id + 3;
  const char* xattr_key = "user.hdfs.test.xattr_key";
  const char* xattr_val = "xattr_val";
  XAttrProto xattr;
  {
    auto st = XAttrs::BuildXAttr(xattr_key, xattr_val, &xattr);
    ASSERT_TRUE(st.IsOK());
    SetXAttrRequestProto setxattr_request =
        MakeSetXAttrRequest(path, xattr, cloudfs::XAttrSetFlagProto::XATTR_CREATE);
    SetXAttrResponseProto setxattr_response = SetXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "setxattr");
    controller->set_request(new SetXAttrRequestProto(setxattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->setXAttr(
        controller.get(), &setxattr_request, &setxattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // set same xattr without replace flag
  int32_t call_id_4 = call_id + 4;
  {
    SetXAttrRequestProto setxattr_request =
        MakeSetXAttrRequest(path, xattr, cloudfs::XAttrSetFlagProto::XATTR_CREATE);
    SetXAttrResponseProto setxattr_response = SetXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "setxattr");
    controller->set_request(new SetXAttrRequestProto(setxattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->setXAttr(
        controller.get(), &setxattr_request, &setxattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former setxattr
  {
    SetXAttrRequestProto setxattr_request =
        MakeSetXAttrRequest(path, xattr, cloudfs::XAttrSetFlagProto::XATTR_CREATE);
    SetXAttrResponseProto setxattr_response = SetXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_3, client_address, "setxattr");
    controller->set_request(new SetXAttrRequestProto(setxattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->setXAttr(
        controller.get(), &setxattr_request, &setxattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter setxattr
  {
    SetXAttrRequestProto setxattr_request =
        MakeSetXAttrRequest(path, xattr, cloudfs::XAttrSetFlagProto::XATTR_CREATE);
    SetXAttrResponseProto setxattr_response = SetXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "setxattr");
    controller->set_request(new SetXAttrRequestProto(setxattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->setXAttr(
        controller.get(), &setxattr_request, &setxattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryRemoveXAttrTest) {
  const std::string path_1 = "/retry_removexattr_test/test_file_1";
  const std::string path_2 = "/retry_removexattr_test/test_file_2";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // prepare
  int32_t call_id_1 = call_id + 1;
  int32_t call_id_2 = call_id + 2;
  int32_t call_id_3 = call_id + 3;
  const char* xattr_key = "user.hdfs.test.xattr_key";
  const char* xattr_val = "xattr_val";
  XAttrProto xattr;
  {
    CreateRequestProto create_request = MakeCreateRequest(path_1);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done_1;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done_1);
    rpc_done_1.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    // complete
    CompleteRequestProto complete_request = MakeCompleteRequest(path_1);
    CompleteResponseProto complete_response = CompleteResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_2, client_address, "complete");
    controller->set_request(new CompleteRequestProto(complete_request));
    SynchronizedRpcClosure rpc_done_2;
    service_->complete(
        controller.get(), &complete_request, &complete_response, &rpc_done_2);
    rpc_done_2.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());

    // set xattr
    auto st = XAttrs::BuildXAttr(xattr_key, xattr_val, &xattr);
    ASSERT_TRUE(st.IsOK());
    SetXAttrRequestProto setxattr_request =
        MakeSetXAttrRequest(path_1, xattr, cloudfs::XAttrSetFlagProto::XATTR_CREATE);
    SetXAttrResponseProto setxattr_response = SetXAttrResponseProto::default_instance();
    controller =
        MakeRpcController(client_id, call_id_3, client_address, "setxattr");
    controller->set_request(new SetXAttrRequestProto(setxattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->setXAttr(
        controller.get(), &setxattr_request, &setxattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // remove xattr
  int32_t call_id_4 = call_id + 4;
  {
    RemoveXAttrRequestProto removexattr_request =
        MakeRemoveXAttrRequest(path_1, xattr);
    RemoveXAttrResponseProto removexattr_response =
        RemoveXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "removexattr");
    controller->set_request(new RemoveXAttrRequestProto(removexattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->removeXAttr(
        controller.get(), &removexattr_request, &removexattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // remove xattr of inexistent file
  int32_t call_id_5 = call_id + 5;
  {
    RemoveXAttrRequestProto removexattr_request =
        MakeRemoveXAttrRequest(path_2, xattr);
    RemoveXAttrResponseProto removexattr_response =
        RemoveXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_5, client_address, "removexattr");
    controller->set_request(new RemoveXAttrRequestProto(removexattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->removeXAttr(
        controller.get(), &removexattr_request, &removexattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // retry former remove xattr
  {
    RemoveXAttrRequestProto removexattr_request =
        MakeRemoveXAttrRequest(path_1, xattr);
    RemoveXAttrResponseProto removexattr_response =
        RemoveXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_4, client_address, "removexattr");
    controller->set_request(new RemoveXAttrRequestProto(removexattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->removeXAttr(
        controller.get(), &removexattr_request, &removexattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }

  // retry latter remove xattr
  {
    RemoveXAttrRequestProto removexattr_request =
        MakeRemoveXAttrRequest(path_2, xattr);
    RemoveXAttrResponseProto removexattr_response =
        RemoveXAttrResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_5, client_address, "removexattr");
    controller->set_request(new RemoveXAttrRequestProto(removexattr_request));
    SynchronizedRpcClosure rpc_done;
    service_->removeXAttr(
        controller.get(), &removexattr_request, &removexattr_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_TRUE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest, RetryCacheActive2StandbyTest) {
  const std::string path = "/retry_failover_test/test_file";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;
  // create file first
  {
    int32_t call_id_1 = call_id - 1;
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
  }

  // failover
  ns_->StopActive();
  ns_->StartStandby();

  // generate delete retry cache entry by apply edit log
  auto op = MakeDeleteOp(path, client_id, call_id, 4);
  ns_->Apply(op);
  std::this_thread::sleep_for(std::chrono::seconds(5));
  ns_->meta_storage_ptr()->TxFinish(4, 1);

  // check retry cache entry
  auto cache_entry = retry_cache_->GetCacheEntry(client_id, call_id);
  ASSERT_NE(cache_entry, nullptr);
  ASSERT_TRUE(cache_entry->IsCompleted());
  ASSERT_TRUE(cache_entry->GetStatus().IsOK());
  ASSERT_TRUE(cache_entry->GetPayload() != nullptr);

  skip_stop_ns_ = true;
}

TEST_F(ClientNamenodeServiceTest, RetryCacheStandby2ActiveTest) {
  const std::string path = "/retry_failover_test/test_file";
  const std::string client_id = "0123456789abcdef";
  const std::string client_address = "***********";
  const int32_t call_id = 100;

  // failover to standby
  ns_->StopActive();
  auto ha = std::dynamic_pointer_cast<MockHAState>(ha_state_);
  ha->s_ = Status(JavaExceptions::Exception::kStandbyException,
                  "Operation is not supported in standby.");
  ns_->StartStandby();

  // create file, trigger StandbyException
  int32_t call_id_1 = call_id - 1;
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_NE(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }

  // failover to active
  ns_->StopStandby();
  ha->s_ = Status();
  ns_->StartActive();
  auto edit_log_ctx = CreateContext();
  auto listener = std::static_pointer_cast<IEditLogSyncListener>(ns_->meta_storage_ptr());
  edit_log_ctx->SetupSyncListener(listener);
  auto last_tx_id = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx, last_tx_id));
  ns_->TestOnlySetEditLogSender(std::move(sender));

  // retry creation
  {
    CreateRequestProto create_request = MakeCreateRequest(path);
    CreateResponseProto create_response = CreateResponseProto::default_instance();
    auto controller =
        MakeRpcController(client_id, call_id_1, client_address, "create");
    controller->set_request(new CreateRequestProto(create_request));
    SynchronizedRpcClosure rpc_done;
    service_->create(
        controller.get(), &create_request, &create_response, &rpc_done);
    rpc_done.Await();
    ASSERT_EQ(controller->status(), RpcStatus::kSuccess);
    ASSERT_FALSE(controller->hit_retry_cache());
  }
}

TEST_F(ClientNamenodeServiceTest,
       RequestPreCheckFailedWhenBackendNameNotMatched) {
  std::string nameservice = FLAGS_nameservice;
  FLAGS_nameservice = "nameservice-1";
  DEFER([&]() { FLAGS_nameservice = nameservice; });
  auto controller = MakeRpcController(
      "0123456789abcdef", 100, "***********", "reportBadBlocks");
  controller->set_request(new cloudfs::ReportBadBlocksRequestProto());
  auto header = std::make_shared<cloudfs::RpcRequestHeaderProto>();
  auto baggage = header->add_baggages();
  baggage->set_name("backend");
  baggage->set_value("nameservice-2");
  controller->set_rpc_request_header(header);
  EXPECT_FALSE(service_->TestOnlyRequestPreCheck(controller.get()));
  EXPECT_EQ(controller->status(), RpcStatus::kAppError);
  EXPECT_EQ(controller->Exception(),
            std::string("com.bytedance.cloudfs.fs.InvalidRequestException"));
  EXPECT_EQ(controller->ErrorText(),
            "backend name not match. "
            "request: nameservice-2, expected: nameservice-1");
}

TEST_F(ClientNamenodeServiceTest,
       RequestPreCheckSucceedWhenBackendNameIsEmpty) {
  auto controller =
      MakeRpcController("0123456789abcdef", 100, "***********", "create");
  EXPECT_TRUE(service_->TestOnlyRequestPreCheck(controller.get()));
}

TEST_F(ClientNamenodeServiceTest, RequestPreCheckFailedWhenEmitTimeout) {
  auto controller = MakeRpcController(
      "0123456789abcdef", 100, "***********", "reportBadBlocks");
  controller->set_request(new cloudfs::ReportBadBlocksRequestProto());
  auto baggage = controller->rpc_request_header()->add_baggages();
  baggage->set_name("emitTime");
  baggage->set_value("0");
  EXPECT_FALSE(service_->TestOnlyRequestPreCheck(controller.get()));
  EXPECT_EQ(controller->status(), RpcStatus::kAppError);
  EXPECT_EQ(controller->Exception(),
            std::string("com.bytedance.cloudfs.fs.InvalidRequestException"));
  EXPECT_THAT(controller->ErrorText(),
              testing::HasSubstr("request exceeds ttl, emit time: 0"));
}

TEST_F(ClientNamenodeServiceTest,
       RequestPreCheckSucceedWhenEmitTimeIsNotANumber) {
  auto controller =
      MakeRpcController("0123456789abcdef", 100, "***********", "create");
  auto baggage = controller->rpc_request_header()->add_baggages();
  baggage->set_name("emitTime");
  baggage->set_value("a");
  EXPECT_TRUE(service_->TestOnlyRequestPreCheck(controller.get()));
}

TEST_F(ClientNamenodeServiceTest,
       RequestPreCheckSucceedWhenEmitTimeIsNegativeNumber) {
  auto controller =
      MakeRpcController("0123456789abcdef", 100, "***********", "create");
  auto baggage = controller->rpc_request_header()->add_baggages();
  baggage->set_name("emitTime");
  baggage->set_value("-2");
  EXPECT_TRUE(service_->TestOnlyRequestPreCheck(controller.get()));
}

TEST_F(ClientNamenodeServiceTest, RequestPreCheckFailedWhenClientIdLengthIs15) {
  auto controller =
      MakeRpcController("0123456789abcde", 100, "***********", "create");
  EXPECT_FALSE(service_->TestOnlyRequestPreCheck(controller.get()));
  EXPECT_EQ(controller->status(), RpcStatus::kAppError);
  EXPECT_EQ(controller->Exception(),
            std::string("com.bytedance.cloudfs.fs.InvalidRequestException"));
  EXPECT_EQ(controller->ErrorText(),
            "client id size invalid. client_id=0123456789abcde");
}

TEST_F(ClientNamenodeServiceTest,
       RequestPreCheckSucceedWhenClientIdLengthIs16) {
  auto controller =
      MakeRpcController("0123456789abcdef", 100, "***********", "create");
  EXPECT_TRUE(service_->TestOnlyRequestPreCheck(controller.get()));
  EXPECT_EQ(controller->status(), RpcStatus::kSuccess);
}

}  // namespace dancenn
