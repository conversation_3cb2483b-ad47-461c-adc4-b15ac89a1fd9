// Copyright (c) 2024 Bytedance Inc.
// All right reserved.

#include "ha/ha_mode_transition_manager.h"

#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include <memory>

#include "base/defer.h"
#include "base/file_utils.h"
#include "proto/generated/cloudfs/HAServiceProtocol.pb.h"
#include "proto/generated/dancenn/namesystem_info.pb.h"
#include "test/gmock_ha_state.h"

DECLARE_string(non_ha_mode_trigger_file);

namespace dancenn {

class HAModeTransitionManagerTest : public testing::Test {
 public:
  void SetUp() override {
    ha_mode_transition_manager_ =
        std::make_unique<HAModeTransitionManager>(&ha_state_);
  }

 protected:
  testing::StrictMock<GMockHAState> ha_state_;
  std::unique_ptr<HAModeTransitionManager> ha_mode_transition_manager_;
};

TEST_F(HAModeTransitionManagerTest, OnHAStandbyWithTriggerFileSucceed) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnHAStandbyWithTriggerFileSucceed";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  ASSERT_TRUE(FileUtils::WriteFile(FLAGS_non_ha_mode_trigger_file, ""));
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  EXPECT_CALL(ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));
  EXPECT_CALL(ha_state_, SwitchHAStandbyToNonHAActive())
      .Times(1)
      .WillOnce(testing::Return(Status()));
  ha_mode_transition_manager_->TestOnlyRunOnce();
}

TEST_F(HAModeTransitionManagerTest, OnHAStandbyWithTriggerFileFailed) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnHAStandbyWithTriggerFileFailed";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  ASSERT_TRUE(FileUtils::WriteFile(FLAGS_non_ha_mode_trigger_file, ""));
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  EXPECT_CALL(ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));
  EXPECT_CALL(ha_state_, SwitchHAStandbyToNonHAActive())
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kError, "error")));
  Status s = ha_mode_transition_manager_->TestOnlyRunOnce();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_EQ(s.message(),
            "Failed to switch from standby to non-HA active, error");
}

TEST_F(HAModeTransitionManagerTest, OnNonHAStandbyWithTriggerFile) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAStandbyWithTriggerFile";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  ASSERT_TRUE(FileUtils::WriteFile(FLAGS_non_ha_mode_trigger_file, ""));
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::StandbyNonHA));
  Status s = ha_mode_transition_manager_->TestOnlyRunOnce();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_EQ(s.message(), "The non-HA standby state is invalid.");
}

TEST_F(HAModeTransitionManagerTest, OnNonHAActiveWithTriggerFile01) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAActiveWithTriggerFile01";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  ASSERT_TRUE(FileUtils::WriteFile(FLAGS_non_ha_mode_trigger_file, ""));
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::ActiveNonHA));
  EXPECT_CALL(ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::ACTIVE));
  EXPECT_TRUE(ha_mode_transition_manager_->TestOnlyRunOnce().IsOK());
}

TEST_F(HAModeTransitionManagerTest, OnNonHAActiveWithTriggerFile02) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAActiveWithTriggerFile02";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  ASSERT_TRUE(FileUtils::WriteFile(FLAGS_non_ha_mode_trigger_file, ""));
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::ActiveNonHA));
  EXPECT_CALL(ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));
  EXPECT_CALL(ha_state_, SetState(cloudfs::HAServiceStateProto::ACTIVE))
      .Times(1);
  EXPECT_TRUE(ha_mode_transition_manager_->TestOnlyRunOnce().IsOK());
}

TEST_F(HAModeTransitionManagerTest, OnHAWithoutTriggerFile) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnHAWithoutTriggerFile";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  FileUtils::DeleteFile(FLAGS_non_ha_mode_trigger_file);
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  EXPECT_TRUE(ha_mode_transition_manager_->TestOnlyRunOnce().IsOK());
}

TEST_F(HAModeTransitionManagerTest, OnNonHAStandbyWithoutTriggerFile) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAStandbyWithoutTriggerFile";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  FileUtils::DeleteFile(FLAGS_non_ha_mode_trigger_file);
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::StandbyNonHA));
  Status s = ha_mode_transition_manager_->TestOnlyRunOnce();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_EQ(s.message(), "The non-HA standby state is invalid.");
}

TEST_F(HAModeTransitionManagerTest, OnNonHAActiveWithoutTriggerFileSucceed) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAActiveWithoutTriggerFileSucceed";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  FileUtils::DeleteFile(FLAGS_non_ha_mode_trigger_file);
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::ActiveNonHA));
  EXPECT_CALL(ha_state_, SwitchNonHAActiveToHAActive())
      .Times(1)
      .WillOnce(testing::Return(Status()));
  EXPECT_TRUE(ha_mode_transition_manager_->TestOnlyRunOnce().IsOK());
}

TEST_F(HAModeTransitionManagerTest, OnNonHAActiveWithoutTriggerFileFailed) {
  auto non_ha_mode_trigger_file = FLAGS_non_ha_mode_trigger_file;
  FLAGS_non_ha_mode_trigger_file = "/tmp/OnNonHAActiveWithoutTriggerFileFailed";
  DEFER([&]() { FLAGS_non_ha_mode_trigger_file = non_ha_mode_trigger_file; });
  FileUtils::DeleteFile(FLAGS_non_ha_mode_trigger_file);
  EXPECT_CALL(ha_state_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::ActiveNonHA));
  EXPECT_CALL(ha_state_, SwitchNonHAActiveToHAActive())
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kError, "error")));
  Status s = ha_mode_transition_manager_->TestOnlyRunOnce();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_EQ(s.message(),
            "Failed to transition from non-HA active to HA active, error");
}

}  // namespace dancenn
