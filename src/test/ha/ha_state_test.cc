//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include "ha/ha_state.h"

#include <glog/logging.h>
#include <gtest/gtest.h>

#include "HAServiceProtocol.pb.h"
#include "base/file_utils.h"
#include "ha/operations.h"
#include "test/dancenn_test_enviroment.h"
#include "test/edit/java_const.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"
#include "ufs/tos/tos_cred_keeper.h"

DECLARE_int32(namespace_type);
DECLARE_bool(readonly_mode_on);

namespace dancenn {

class HAStateTest: public testing::Test {
 public:
  void SetUp() override {
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    std::string run_path = GetProgramDirectory();
    std::string specified_path = run_path + "/conf:"
                                 + run_path + "/../jar/bookkeeper-server-4.5.0.jar:"
                                 + run_path + "/../jar/bookkeeper-stats-api-4.5.0.jar:"
                                 + run_path + "/../jar/hadoop-hdfs-2.6.0-cdh5.4.4.jar:"
                                 + run_path + "/../jar/netty-all-4.1.12.Final.jar:"
                                 + run_path + "/../jar/protobuf-java-3.0.0.jar:"
                                 + run_path + "/../jar/guava-23.0.jar:";
    auto java_classpath = specified_path + std::string(kJavaCommonClasspath);

    if (g_java_runtime == nullptr) {
      g_java_runtime.reset(new JavaRuntime(java_classpath, 128));
    }

    barrier_ = std::make_shared<dancenn::VRWLock>(1);
    edit_log_ctx_ = std::make_shared<MockEditLogContext>();
    ha_state_ = std::make_unique<HAState>(
        g_java_runtime, edit_log_ctx_, barrier_, nullptr);
    safemode_ = std::unique_ptr<SafeModeBase>(new MockSafeMode);

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    block_manager_->set_datanode_manager(datanode_manager_);
    block_manager_->set_safemode(safemode_.get());
    auto job_manager = std::make_shared<dancenn::JobManager>(datanode_manager_,
                                                             block_manager_);
    MockFSImageTransfer(db_path_).Transfer();
    ns_ = std::make_unique<NameSpace>(db_path_,
                                      edit_log_ctx_,
                                      block_manager_,
                                      datanode_manager_,
                                      job_manager,
                                      std::make_shared<DataCenters>(),
                                      nullptr,
                                      nullptr);
    ha_state_->set_safemode(safemode_.get());
    ha_state_->set_ns(ns_.get());
    ns_->set_ha_state(ha_state_.get());
    ns_->set_safemode(safemode_.get());
    ns_->Start();
  }

  void TearDown() override {
    edit_log_ctx_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::unique_ptr<HAState> ha_state_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<dancenn::VRWLock> barrier_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::unique_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::string db_path_ = "rocksdb_XXXXXX";
};

TEST_F(HAStateTest, CheckOperation) {
  ASSERT_TRUE(ha_state_->IsInitializing());
  ha_state_->CompleteInitialization();
  ASSERT_TRUE(!ha_state_->IsInitializing());
  ASSERT_EQ(ha_state_->GetState(),
            cloudfs::HAServiceStateProto::STANDBY);
  auto res = ha_state_->CheckOperation(OperationsCategory::kUnchecked).first;
  ASSERT_FALSE(res.HasException());
  // multi-read
  res = ha_state_->CheckOperation(OperationsCategory::kRead).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
  ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);
  res = ha_state_->CheckOperation(OperationsCategory::kJournal).first;
  ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);
  // test standby readonly mode
  FLAGS_readonly_mode_on = true;
  res = ha_state_->CheckOperation(OperationsCategory::kRead).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
  ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);
  FLAGS_readonly_mode_on = false;
  // active mode
  ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE,
                      /*rpc_done=*/nullptr);
  res = ha_state_->CheckOperation(OperationsCategory::kUnchecked).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kRead).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kJournal).first;
  ASSERT_FALSE(res.HasException());
  // test active readonly mode
  FLAGS_readonly_mode_on = true;
  res = ha_state_->CheckOperation(OperationsCategory::kRead).first;
  ASSERT_FALSE(res.HasException());
  res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
  ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);
  FLAGS_readonly_mode_on = false;
}

TEST_F(HAStateTest, DestroyState) {
  ASSERT_FALSE(ha_state_->IsDestroyState());
  ha_state_->EnterDestroyState();
  ASSERT_TRUE(ha_state_->IsDestroyState());
  ha_state_->ExitDestroyState();
  ASSERT_FALSE(ha_state_->IsDestroyState());
}

TEST_F(HAStateTest, LockBarrierForHASwitcher) {
  // Auto release HAState::barrier_.
  { auto l = ha_state_->LockBarrierForHASwitcher(); }
  { auto l = ha_state_->LockBarrierForHASwitcher(); }
}

class Closure4Test : public dancenn::Closure {
 public:
  Closure4Test() : latch_(1) {
  }

  Closure4Test(size_t num_signal) : latch_(num_signal) {
  }

  void Run() override {
    latch_.CountDown();
    called_++;
  }

  // Block the thread until Run() has been called
  void Await() {
    latch_.Await();
  }

  bool IsSlow() const override {
    return false;
  }

  int Called() {
    return called_;
  }

  std::string DebugString() override {
    std::ostringstream oss;
    oss << "[Closure4Test]";
    oss << " ";
    oss << Closure::DebugString();
    return oss.str();
  }

 private:
  CountDownLatch latch_;
  std::atomic<uint64_t> called_{0};
};

TEST_F(HAStateTest, UseAsyncRelease) {
  auto done = std::make_shared<Closure4Test>();

  ASSERT_TRUE(ha_state_->IsInitializing());
  ha_state_->CompleteInitialization();
  ASSERT_TRUE(!ha_state_->IsInitializing());
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::STANDBY);

  ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE,
                      /*rpc_done=*/done.get());
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::ACTIVE);
  ASSERT_EQ(done->Called(), 1);
  done->Await();
  ASSERT_EQ(done->Called(), 1);

  ns_->TestOnlySetEditLogSender(std::make_shared<MockEditLogSender>(
      edit_log_ctx_, ns_->GetLastCkptTxId()));
  done = std::make_shared<Closure4Test>();
  ha_state_->SetState(cloudfs::HAServiceStateProto::STANDBY,
                      /*rpc_done=*/done.get());
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::STANDBY);
  ASSERT_EQ(done->Called(), 1);
  done->Await();
  ASSERT_EQ(done->Called(), 1);
}

TEST_F(HAStateTest, UsePreTransition) {
  ASSERT_TRUE(ha_state_->IsInitializing());
  ha_state_->CompleteInitialization();
  ASSERT_TRUE(!ha_state_->IsInitializing());
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::STANDBY);

  ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE,
                      /*rpc_done=*/nullptr);
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::ACTIVE);

  // run in active
  {
    ASSERT_FALSE(ha_state_->InTransition());
    ha_state_->PreTransition(/*add_lock=*/false);
    ASSERT_TRUE(ha_state_->InTransition());

    auto res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
    ASSERT_FALSE(res.HasException());

    ha_state_->ExitPreTransition();
    ASSERT_FALSE(ha_state_->InTransition());
  }

  {
    ASSERT_FALSE(ha_state_->InTransition());
    ha_state_->PreTransition(/*add_lock=*/true);
    ASSERT_TRUE(ha_state_->InTransition());

    auto res = ha_state_->CheckOperation(OperationsCategory::kWrite).first;
    ASSERT_TRUE(res.HasException());
    ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);

    ha_state_->ExitPreTransition();
    ASSERT_FALSE(ha_state_->InTransition());
  }

  ns_->TestOnlySetEditLogSender(std::make_shared<MockEditLogSender>(
      edit_log_ctx_, ns_->GetLastCkptTxId()));
  ha_state_->SetState(cloudfs::HAServiceStateProto::STANDBY,
                      /*rpc_done=*/nullptr);
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::STANDBY);
  ASSERT_FALSE(ha_state_->InTransition());

  // run in standby
  {
    ASSERT_FALSE(ha_state_->InTransition());
    ha_state_->PreTransition(/*add_lock=*/false);
    ASSERT_TRUE(ha_state_->InTransition());

    auto res = ha_state_->CheckOperation(OperationsCategory::kUnchecked).first;
    ASSERT_FALSE(res.HasException());

    ha_state_->ExitPreTransition();
    ASSERT_FALSE(ha_state_->InTransition());
  }

  {
    ASSERT_FALSE(ha_state_->InTransition());
    ha_state_->PreTransition(/*add_lock=*/true);
    ASSERT_TRUE(ha_state_->InTransition());

    auto res = ha_state_->CheckOperation(OperationsCategory::kUnchecked).first;
    ASSERT_TRUE(res.HasException());
    ASSERT_EQ(res.exception(), JavaExceptions::Exception::kStandbyException);

    ha_state_->ExitPreTransition();
    ASSERT_FALSE(ha_state_->InTransition());
  }

  ha_state_->SetState(cloudfs::HAServiceStateProto::ACTIVE,
                      /*rpc_done=*/nullptr);
  ASSERT_EQ(ha_state_->GetState(), cloudfs::HAServiceStateProto::ACTIVE);
  ASSERT_FALSE(ha_state_->InTransition());
}

}  // namespace dancenn
