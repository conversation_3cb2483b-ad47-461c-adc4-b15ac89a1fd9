// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include <string>

#include "fsimage/fsimage_loader.h"
#include "dancenn_test_enviroment.h"
#include "mock_edit_log_context.h"
#include "mock_edit_log_sender.h"
#include "test/namespace/mock_namespace.h"

DECLARE_string(fsimage_dir);
DECLARE_string(fsimage_file_name);
DECLARE_int64(namespace_id);
DECLARE_int32(namespace_type);

namespace dancenn {

class FSImageLoaderTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    std::string cwd = GetProgramDirectory();
    LOG(INFO) << "Current directory: " << cwd;

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    LOG(INFO) << "RocksDB path: " << db_path_;

    FLAGS_fsimage_dir = cwd + "/data/fsimage";
    FLAGS_fsimage_file_name = "fsimage_0000000006963259775";

    block_manager_ = std::make_shared<dancenn::BlockManager>();
    dnmgr_ = std::make_shared<dancenn::DatanodeManager>();

    context_ = std::shared_ptr<MockEditLogContext>(new MockEditLogContext());
    context_->open_for_read_ = true;
    context_->open_for_write_ = false;
    MockFSImageTransfer(db_path_).Transfer();
    ns_.reset(new MockNameSpace(db_path_,
                                context_,
                                block_manager_,
                                dnmgr_,
                                std::make_shared<DataCenters>(),
                                nullptr,
                                nullptr));
  }

  void TearDown() override {
    ns_.reset();
    dnmgr_.reset();
    context_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> dnmgr_;
  std::unique_ptr<MockNameSpace> ns_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MockEditLogContext> context_;
};

TEST_F(FSImageLoaderTest, Test01) {
  auto loader = std::make_unique<FSImageLoader>(ns_.get());
  auto start = std::chrono::steady_clock::now();
  loader->Load();
  auto cost =
    ((std::chrono::steady_clock::now() - start).count()) / 1000000000.0;
  LOG(INFO) << "Load namespace took "
            << cost
            << " seconds";

  loader->RefreshNameSpaceInfo();
  // ASSERT_EQ(ns_->layout_version(), kDefaultLayoutVersion);
  ASSERT_EQ(ns_->namespace_id(), FLAGS_namespace_id);
  ASSERT_EQ(ns_->ctime(), 0);
  ASSERT_EQ(ns_->cluster_id(), "CID-7537dd9e-3c62-4409-a5f0-23996b030ca4");
  ASSERT_EQ(ns_->blockpool_id(), "BP-964799785-10.8.28.221-1497005578788");
  ASSERT_EQ(ns_->last_allocated_block_id(), 2240055786);
  ASSERT_EQ(ns_->last_inode_id(), 1150474856);
  ASSERT_EQ(ns_->generation_stamp_v1(), 1000);
  ASSERT_EQ(ns_->generation_stamp_v2(), 1166890387);
  ASSERT_EQ(ns_->generation_stamp_v1_limit(), 0);
  ASSERT_EQ(ns_->GetLastCkptTxId(), 6963259775);
  ASSERT_EQ(ns_->num_inodes(), 1394531);

  /*
   * inode id=1140851193 pid=1140840353 name=n6-129-076.byted.org_8052 type=1
   * inode id=1140840353 pid=1137672557 name=application_1504174027147_1056194 type=2
   * inode id=1137672557 pid=1137672556 name=4 type=2
   * inode id=1137672556 pid=985907171 name=14 type=2
   * inode id=985907171 pid=985907170 name=logs type=2
   * inode id=985907170 pid=16392 name=wanghuiqi type=2
   * inode id=16392 pid=16391 name=remote type=2
   * inode id=16391 pid=kRootINodeId name=containers_log type=2
   * */
  ASSERT_EQ(
      ns_->BuildFullPath(1140851193),
      "/containers_log/remote/wanghuiqi"
      "/logs/14/4/application_1504174027147_1056194"
      "/n6-129-076.byted.org_8052");

  ns_.reset();
  block_manager_.reset();
  block_manager_ = std::make_shared<dancenn::BlockManager>();

  context_.reset(new MockEditLogContext());
  context_->open_for_read_ = true;
  context_->open_for_write_ = false;

  ns_.reset(new MockNameSpace(db_path_,
                              context_,
                              block_manager_,
                              dnmgr_,
                              std::make_shared<DataCenters>(),
                              nullptr,
                              nullptr));
  // NOTICE: fsimage loader has some bugs, please don't use it.
  // ASSERT_EQ(block_manager_->GetBlockNum(), 1306932);
  ASSERT_EQ(
      ns_->BuildFullPath(1140851193),
      "/containers_log/remote/wanghuiqi"
      "/logs/14/4/application_1504174027147_1056194"
      "/n6-129-076.byted.org_8052");
}

}  // namespace dancenn

