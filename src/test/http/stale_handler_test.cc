// Copyright 2020 Cheng <PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "block_manager/block_report_handler.h"
#include "http/decommission_handler.h"

namespace dancenn {

class StaleHandlerTest : public testing::Test {
 public:
  void SetUp() override {
    block_manager_ = std::make_shared<BlockManager>();
    datanode_manager_ = std::make_shared<DatanodeManager>();
    block_report_manager_ = std::make_shared<BlockReportManager>();
    datanode_manager_->set_block_manager(block_manager_.get());
    block_manager_->set_datanode_manager(datanode_manager_);
    block_manager_->set_block_report_manager(block_report_manager_.get());
    block_report_manager_->Start(reinterpret_cast<NameSpace*>(0x1),
                                 datanode_manager_.get(),
                                 block_manager_.get());
    block_report_manager_->StartStandby(); // It will core if not started
    handler_ = new StaleHandler(datanode_manager_);
  }

  void TearDown() override {
    block_report_manager_->Stop();
    delete handler_;
  }

  auto Handle(cnetpp::http::HttpRequest& r) {
    return handler_->Handle(r);
  }

  auto GetStaleDnSize() {
    return handler_->GetStaleDnSize();
  }

  cloudfs::datanode::DatanodeRegistrationProto MockDatanode(
      const std::string& ip) {
    cloudfs::datanode::DatanodeRegistrationProto reg;
    reg.mutable_datanodeid()->set_ipaddr(ip);
    reg.mutable_datanodeid()->set_hostname(ip);
    reg.mutable_datanodeid()->set_datanodeuuid(ip);
    reg.mutable_datanodeid()->set_xferport(0);
    reg.mutable_datanodeid()->set_infoport(0);
    reg.mutable_datanodeid()->set_ipcport(0);
  
    reg.mutable_storageinfo()->set_layoutversion(0);
    reg.mutable_storageinfo()->set_namespaceid(0);
    reg.mutable_storageinfo()->set_clusterid("");
    reg.mutable_storageinfo()->set_ctime(0);
  
    reg.mutable_keys()->set_isblocktokenenabled(false);
    reg.mutable_keys()->set_keyupdateinterval(0);
    reg.mutable_keys()->set_tokenlifetime(0);
    reg.mutable_keys()->mutable_currentkey()->set_keyid(0);
    reg.mutable_keys()->mutable_currentkey()->set_expirydate(0);
  
    reg.set_softwareversion("");
    return reg;
  }

 public:
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<BlockReportManager> block_report_manager_;

 private:
  StaleHandler* handler_{ nullptr };
};

TEST_F(StaleHandlerTest, AddDecommissionHost) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/decommission");
  auto r = Handle(request);
  ASSERT_EQ(HttpStatusCode::kBadRequest, r.status());

  request.set_method(cnetpp::http::HttpRequest::MethodType::kPost);

  request.set_http_body("***********");
  r = Handle(request);
  ASSERT_EQ(HttpStatusCode::kBadRequest, r.status());

  auto dn1 = MockDatanode("***********");
  auto dn2 = MockDatanode("***********");
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");

  // register dn1
  datanode_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  datanode_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);

  // register dn2
  datanode_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  datanodes.Clear();
  datanode_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip2), &datanodes);
  ASSERT_EQ(datanodes.size(), 2);

  request.set_http_body("------WebKitFormBoundaryn4urJqgBBlo9l6Ri\r\nContent-Disposition: form-data; name=\"hosts\"; filename=\"hosts\"\r\nContent-Type: application/octet-stream\r\n\r\n***********,***********\r\n\r\n------WebKitFormBoundaryn4urJqgBBlo9l6Ri--");
  r = Handle(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "[{\"code\":0,\"msg\":\"OK\"}]");
  ASSERT_EQ(GetStaleDnSize(), 2);

  request.set_http_body("------WebKitFormBoundaryn4urJqgBBlo9l6Ri\r\nContent-Disposition: form-data; name=\"hosts\"; filename=\"hosts\"\r\nContent-Type: application/octet-stream\r\n\r\n\r\n\r\n------WebKitFormBoundaryn4urJqgBBlo9l6Ri--");
  r = Handle(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "[{\"code\":0,\"msg\":\"OK\"}]");
  ASSERT_EQ(GetStaleDnSize(), 0);

  request.set_http_body("------WebKitFormBoundaryn4urJqgBBlo9l6Ri\r\nContent-Disposition: form-data; name=\"hosts\"; filename=\"hosts\"\r\nContent-Type: application/octet-stream\r\n\r\n***********,***********,,***********,,\r\n\r\n------WebKitFormBoundaryn4urJqgBBlo9l6Ri--");
  r = Handle(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "[{\"code\":0,\"msg\":\"OK\"}]");
  ASSERT_EQ(GetStaleDnSize(), 2);
}

}  // namespace dancenn

