// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/decommission_handler.h"

#include <gtest/gtest.h>

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>
#include <cnetpp/base/csonpp.h>

#include "datanode_manager/gmock_datanode_manager.h"

namespace dancenn {

class DecommissionHandlerTest : public testing::Test {
 public:
void SetUp() override {
    dn_mgr_ = std::make_shared<GMockDatanodeManager>();
    handler_ = std::make_shared<DecommissionHandler>(dn_mgr_);
  }
  void TearDown() override {}

 public:
  std::shared_ptr<GMockDatanodeManager> dn_mgr_;
  std::shared_ptr<DecommissionHandler> handler_;
};

TEST_F(DecommissionHandlerTest, Test01) {
  {
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kBadRequest);
  }

  {
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission?dn=a");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kBadRequest);
  }

  {
    EXPECT_CALL(*dn_mgr_, SetDecommissioned(1, false))
        .WillOnce(testing::Return(Status(Code::kFalse)));
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission?dn=1");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kInternalServerError);
  }

  {
    EXPECT_CALL(*dn_mgr_, SetDecommissioned(1, false))
        .WillOnce(testing::Return(Status::OK()));
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission?dn=1");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);
  }

  {
    EXPECT_CALL(*dn_mgr_, SetDecommissioned(1, true))
        .WillOnce(testing::Return(Status(Code::kFalse)));
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission?dn=1&force=true");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kInternalServerError);
  }

  {
    EXPECT_CALL(*dn_mgr_, SetDecommissioned(1, true))
        .WillOnce(testing::Return(Status::OK()));
    cnetpp::http::HttpRequest request;
    request.set_uri("/decommission?dn=1&force=true");
    auto response = handler_->Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/text");
    ASSERT_EQ(response.status(), HttpStatusCode::kOk);
  }
}

}  // namespace dancenn

