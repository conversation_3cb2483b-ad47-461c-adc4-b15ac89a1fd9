// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "http/metrics_handler.h"

#include <gtest/gtest.h>

#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>
#include <cnetpp/base/csonpp.h>

#include "base/metrics.h"
#include "base/metric.h"

namespace dancenn {

class MetricsHandlerTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(MetricsHandlerTest, Test01) {
  {
    MetricsHandler handler;
    cnetpp::http::HttpRequest request;
    request.set_uri("/metrics");
    auto response = handler.Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/json");
    // DONOT validate content length; 因为新增了 LoggerMetrics::Init() 所以
    // result 长度是不确定的
    // ASSERT_EQ(*content_length, "2");
    auto& body = response.mutable_http_body();
    cnetpp::base::Parser parser;
    cnetpp::base::Value value;
    ASSERT_TRUE(parser.Deserialize(body, &value));
    ASSERT_TRUE(value.IsObject());
    ASSERT_EQ(value.GetObject().Size(), 1);
    auto o = value.AsObject();
    ASSERT_TRUE(o.Find("Logger") != o.End());
  }

  {
    auto center = MetricsCenter::Instance();
    auto metrics = center->RegisterMetrics("NonEmpty");
    auto gauge = metrics->RegisterGauge("gauge1");
    auto counter = metrics->RegisterCounter("counter1");
    auto histogram = metrics->RegisterHistogram("histogram1");
    gauge->Update(2);
    MFC(counter)->Inc(3);
    MFH(histogram)->Update(4);

    cnetpp::http::HttpRequest request;
    request.set_uri("/metrics");
    MetricsHandler handler;
    auto response = handler.Handle(request);
    const auto& headers = response.mutable_http_headers();
    const std::string* content_type = nullptr;
    ASSERT_TRUE(headers.Get("Content-Type", &content_type));
    const std::string* content_length = nullptr;
    ASSERT_TRUE(headers.Get("Content-Length", &content_length));
    ASSERT_EQ(*content_type, "application/json");
    auto& body = response.mutable_http_body();
    ASSERT_EQ(*content_length, std::to_string(body.size()));
    cnetpp::base::Parser parser;
    cnetpp::base::Value value;
    ASSERT_TRUE(parser.Deserialize(body, &value));
    ASSERT_TRUE(value.IsObject());
    ASSERT_EQ(value.GetObject().Size(), 2);
    auto o = value.AsObject();
    ASSERT_TRUE(o.Find("NonEmpty") != o.End());
    ASSERT_TRUE(o["NonEmpty"].IsObject());
    auto oo = o["NonEmpty"].AsObject();

    ASSERT_TRUE(oo.Find("gauges") != oo.End());
    ASSERT_TRUE(oo["gauges"].IsObject());
    auto gs = oo["gauges"].AsObject();
    ASSERT_TRUE(gs.Find("gauge1") != gs.End());
    ASSERT_TRUE(gs["gauge1"].IsDouble());
    ASSERT_DOUBLE_EQ(gs["gauge1"].AsDouble(), 2.);

    ASSERT_TRUE(oo.Find("counters") != oo.End());
    ASSERT_TRUE(oo["counters"].IsObject());
    auto cs = oo["counters"].AsObject();
    ASSERT_TRUE(cs.Find("counter1") != cs.End());
    ASSERT_TRUE(cs["counter1"].IsIntegral());
    ASSERT_EQ(cs["counter1"].AsInteger(), 3ll);

    ASSERT_TRUE(oo.Find("histograms") != oo.End());
    ASSERT_TRUE(oo["histograms"].IsObject());
    auto hs = oo["histograms"].AsObject();
    ASSERT_TRUE(hs.Find("histogram1") != hs.End());
    ASSERT_TRUE(hs["histogram1"].IsObject());
    auto h = hs["histogram1"].AsObject();
    ASSERT_TRUE(h.Find("avg") != h.End());
    ASSERT_TRUE(h.Find("min") != h.End());
    ASSERT_TRUE(h.Find("max") != h.End());
    ASSERT_TRUE(h.Find("pct50") != h.End());
    ASSERT_TRUE(h.Find("pct75") != h.End());
    ASSERT_TRUE(h.Find("pct95") != h.End());
    ASSERT_TRUE(h.Find("pct99") != h.End());
    ASSERT_TRUE(h.Find("pct999") != h.End());
  }

}

}  // namespace dancenn

