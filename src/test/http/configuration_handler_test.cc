// Copyright 2018 livexmm <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "http/configurations_handler.h"

DECLARE_int32(replication_monitor_interval_ms);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(bg_auto_compact_all_deletion_threshold);
DECLARE_int32(bg_auto_compact_interval_in_min);
DECLARE_int32(bg_auto_compact_deletion_enable);
DECLARE_string(blacklist_dc);
DECLARE_string(majority_dc);
DECLARE_string(all_datacenters);

namespace dancenn {

class ConfigUpdateHandlerTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_replication_monitor_interval_ms = 10 * 1000;
    FLAGS_all_datacenters = "LF,HL";
    handle_ = new ConfigurationsHandler();
  }
  void TearDown() override {
    FLAGS_replication_monitor_interval_ms = 10 * 1000;
    delete handle_;
  }

  auto InternalHandleUpdateConfig(const ParamMap& params, std::string* result) {
    return handle_->InternalHandleUpdateConfig(params, result);
  }

  auto HandleUpdateConfig(cnetpp::http::HttpRequest& r) {
    return handle_->Handle(r);
  }
 private:
  ConfigurationsHandler* handle_{nullptr};
};

TEST_F(ConfigUpdateHandlerTest, Test01) {
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 10 * 1000);
}

TEST_F(ConfigUpdateHandlerTest, Test02) {
  std::string result;
  ParamMap params;
  params.emplace("replication_monitor_interval_ms", "20000");
  ASSERT_EQ(HttpStatusCode::kOk, InternalHandleUpdateConfig(params, &result));
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 20000);
  ASSERT_EQ(result, "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test03) {
  std::string result;
  ParamMap params;
  params.emplace("replication_monitor_interval_ms1", "30000");
  ASSERT_EQ(HttpStatusCode::kNotFound, InternalHandleUpdateConfig(params, &result));
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 10 * 1000);
}

TEST_F(ConfigUpdateHandlerTest, Test04) {
  std::string result;
  ParamMap params;
  params.emplace("replication_monitor_interval_ms", "abc");
  ASSERT_EQ(HttpStatusCode::kBadRequest, InternalHandleUpdateConfig(params, &result));
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 10 * 1000);
}

TEST_F(ConfigUpdateHandlerTest, Test05) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?replication_monitor_interval_ms=2000");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 2000);
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test06) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?replication_monitor_interval_ms1=2000");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kNotFound, r.status());
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 10 * 1000);
}

TEST_F(ConfigUpdateHandlerTest, Test07) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?replication_monitor_interval_ms=abc");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kBadRequest, r.status());
  ASSERT_EQ(FLAGS_replication_monitor_interval_ms, 10 * 1000);
}

TEST_F(ConfigUpdateHandlerTest, Test08) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?help=");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());

  std::stringstream ss;
  ss << "{ \"code\": 0, \"flags\": ["
    << "\"replication_monitor_interval_ms\","
    << "]}";
  ASSERT_EQ(r.http_body(), ss.str());
}

TEST_F(ConfigUpdateHandlerTest, Test09) {
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?datanode_keep_alive_timeout_sec=2000");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(FLAGS_datanode_keep_alive_timeout_sec, 2000);
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test10) {
  ASSERT_EQ(FLAGS_bg_auto_compact_all_deletion_threshold, 0);
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?bg_auto_compact_all_deletion_threshold=2000");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(FLAGS_bg_auto_compact_all_deletion_threshold, 2000);
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test11) {
  ASSERT_EQ(FLAGS_bg_auto_compact_interval_in_min, 60);
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?bg_auto_compact_interval_in_min=120");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(FLAGS_bg_auto_compact_interval_in_min, 120);
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test12) {
  ASSERT_EQ(FLAGS_bg_auto_compact_deletion_enable, 0);
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?bg_auto_compact_deletion_enable=1");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(FLAGS_bg_auto_compact_deletion_enable, 1);
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
}

TEST_F(ConfigUpdateHandlerTest, Test13) {
  ASSERT_EQ(FLAGS_blacklist_dc, "");
  ASSERT_EQ(FLAGS_majority_dc, "");
  cnetpp::http::HttpRequest request;
  request.set_uri("/config/update?blacklist_dc=LF");
  auto r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
  ASSERT_EQ(FLAGS_blacklist_dc, "LF");
  request.set_uri("/config/update?blacklist_dc=");
  r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
  ASSERT_EQ(FLAGS_blacklist_dc, "");

  request.set_uri("/config/update?majority_dc=HL");
  r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
  ASSERT_EQ(FLAGS_majority_dc, "HL");
  request.set_uri("/config/update?majority_dc=");
  r = HandleUpdateConfig(request);
  ASSERT_EQ(HttpStatusCode::kOk, r.status());
  ASSERT_EQ(r.http_body(), "{\"code\": 0, \"msg\": \"OK\"}");
  ASSERT_EQ(FLAGS_majority_dc, "");
}

}  // namespace dancenn

