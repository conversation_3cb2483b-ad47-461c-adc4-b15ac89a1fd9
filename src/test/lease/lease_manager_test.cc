// Copyright 2017 Le<PERSON> Li <<EMAIL>>
#define private public // To access private member of LeaseManager
#include "lease/lease_manager.h"
#include "lease/lease_monitor.h"

#include <gtest/gtest.h>

#include <iostream>
#include <thread>
#include <chrono>
#include <random>

#include "base/count_down_latch.h"
#include "base/defer.h"

DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(lease_speed_up_sec);

namespace dancenn {

namespace {

uint64_t inode_id1 = 1;
uint64_t inode_id2 = 2;
uint64_t inode_id3 = 3;

std::string holder1 = "test1";
std::string holder2 = "test2";

std::string ip1 = "ip1";
std::string ip2 = "ip2";


auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;

void SetExpiredTime(uint64_t expired_soft_limit_ms,
                    uint64_t expired_hard_limit_ms) {
  FLAGS_lease_expired_soft_limit_ms = expired_soft_limit_ms;
  FLAGS_lease_expired_hard_limit_ms = expired_hard_limit_ms;
}

class LeaseManagerTest : public testing::Test {
 public:
  void SetUp() override {
    SetExpiredTime(100, 300);
    lease_manager_ = std::make_unique<LeaseManager>();
  }
  void TearDown() override {
    SetExpiredTime(default_soft_limit_ms, default_hard_limit_ms);
    lease_manager_.reset();
  }

  std::shared_ptr<LeaseManager> lease_manager_;
};

TEST_F(LeaseManagerTest, TestMonitorSliceLeases) {
  FLAGS_lease_expired_hard_limit_ms = 60 * 1000 * 60;
  ASSERT_TRUE(lease_manager_->AddLease(holder1, inode_id1));
  ASSERT_TRUE(lease_manager_->AddLease(holder2, inode_id2));
  auto&& lease = lease_manager_->GetLeaseSlice(holder1).GetLease(holder1);
  auto lease_monitor_task = std::make_shared<LeaseMonitor>(
      lease_manager_.get(),
      [](uint64_t inode_id,
         const std::string& orig_holder,
         const std::string& new_holder) { return Status(); });

  auto last_update = lease->GetLastUpdate();
  auto max_expire = lease->GetMaxExpire();
  auto min_max_expire = max_expire;
  auto max = std::chrono::time_point<std::chrono::system_clock>::max();
  // Check Renew
  auto suppose_expire = last_update + std::chrono::milliseconds(
                                          FLAGS_lease_expired_hard_limit_ms);
  ASSERT_EQ(std::chrono::system_clock::to_time_t(max_expire),
            std::chrono::system_clock::to_time_t(suppose_expire));
  // Mointor Lease
  min_max_expire = max;
  lease_monitor_task->MonitorSliceLeases(
      const_cast<LeaseSlice&>(lease_manager_->GetLeaseSlice(holder1)),
      &last_update,
      &min_max_expire);
  ASSERT_EQ(
    std::chrono::system_clock::to_time_t(min_max_expire),
    std::chrono::system_clock::to_time_t(suppose_expire));
  // Mointor Lease After Speed Up
  min_max_expire = max;
  suppose_expire = last_update + std::chrono::seconds(FLAGS_lease_speed_up_sec);
  ASSERT_TRUE(lease_manager_->SpeedUpReLease(inode_id1));
  lease_monitor_task->MonitorSliceLeases(
      const_cast<LeaseSlice&>(lease_manager_->GetLeaseSlice(holder1)),
      &last_update,
      &max_expire);
  ASSERT_EQ(
    std::chrono::system_clock::to_time_t(max_expire),
    std::chrono::system_clock::to_time_t(suppose_expire));
  FLAGS_lease_expired_hard_limit_ms = 300;
}

TEST_F(LeaseManagerTest, TestSpeedUpRelease) {
  FLAGS_lease_expired_hard_limit_ms = 60 * 1000 * 60;
  ASSERT_TRUE(lease_manager_->AddLease(holder1, inode_id1));
  ASSERT_TRUE(lease_manager_->AddLease(holder2, inode_id3));

  ASSERT_TRUE(lease_manager_->SpeedUpReLease(inode_id1));
  auto&& file_lease1 = lease_manager_->GetLeaseSlice(holder1).GetLease(holder1);
  ASSERT_EQ(
    file_lease1->GetLastUpdate() + std::chrono::seconds(FLAGS_lease_speed_up_sec), 
    file_lease1->GetMaxExpire());

  ASSERT_TRUE(lease_manager_->SpeedUpReLease(inode_id3));
  auto&& file_lease2 = lease_manager_->GetLeaseSlice(holder2).GetLease(holder2);
  ASSERT_EQ(
    file_lease2->GetLastUpdate() + std::chrono::seconds(FLAGS_lease_speed_up_sec), 
    file_lease2->GetMaxExpire());

  ASSERT_FALSE(lease_manager_->SpeedUpReLease(inode_id2));
  FLAGS_lease_expired_hard_limit_ms = 300;
}

TEST_F(LeaseManagerTest, Lease) {
  auto lease = std::make_unique<Lease>(holder1);
  lease->Renew(Lease::DEFAULT_HOLDER_IP);
  lease->AddOpenFile(inode_id1);
  lease->AddOpenFile(inode_id2);

  ASSERT_TRUE(lease->HasOpenFile(inode_id1));
  ASSERT_TRUE(lease->HasOpenFile(inode_id2));
  ASSERT_EQ(lease->NumOpenFiles(), 2);

  const auto &open_files = lease->GetOpenFiles();
  ASSERT_EQ(open_files.size(), 2);
  const auto &inode1_iter = open_files.find(inode_id1);
  ASSERT_TRUE(inode1_iter != open_files.end());

  ASSERT_EQ(lease->GetHolder(), holder1);

  ASSERT_EQ(lease->CompareTo(*lease), 0);
  auto now = std::chrono::system_clock::now();
  std::this_thread::sleep_for(std::chrono::milliseconds(1));

  auto lease2 = std::make_unique<Lease>(holder2);
  ASSERT_LT(lease->CompareTo(*lease2), 0);

  std::chrono::microseconds duration1 =
      std::chrono::duration_cast<std::chrono::microseconds>(
          lease->GetLastUpdate() - now);
  ASSERT_LT(duration1.count(), 0);
  lease->Renew(Lease::DEFAULT_HOLDER_IP);
  std::chrono::microseconds duration2 =
      std::chrono::duration_cast<std::chrono::microseconds>(
          lease->GetLastUpdate() - now);
  ASSERT_GT(duration2.count(), 0);
}

TEST_F(LeaseManagerTest, AddLease) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder2, inode_id3);

  ASSERT_FALSE(lease_manager_->CanAddLease(holder2, inode_id1));
  ASSERT_FALSE(lease_manager_->AddLease(holder2, inode_id1));
  ASSERT_TRUE(lease_manager_->CanAddLease(holder1, inode_id2));
  ASSERT_TRUE(lease_manager_->AddLease(holder1, inode_id2));
  ASSERT_EQ(lease_manager_->Stats().lease_num, 2);
  ASSERT_EQ(lease_manager_->Stats().path_num, 3);
  const auto &open_files = lease_manager_->GetOpenFiles();
  ASSERT_EQ(open_files.size(), 3);
  ASSERT_TRUE(open_files.find(inode_id1) != open_files.end());
  ASSERT_TRUE(open_files.find(inode_id2) != open_files.end());
  ASSERT_TRUE(open_files.find(inode_id3) != open_files.end());
}

TEST_F(LeaseManagerTest, CheckLease) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder1, inode_id2);

  ASSERT_TRUE(lease_manager_->CheckLease(holder1, inode_id1));

  ASSERT_FALSE(lease_manager_->CheckLease(holder2, inode_id1));
  ASSERT_FALSE(lease_manager_->CanAddLease(holder2, inode_id1));

  ASSERT_FALSE(lease_manager_->AddLease(holder2, inode_id1));

  ASSERT_FALSE(lease_manager_->CheckLease(holder2, inode_id3));
  ASSERT_TRUE(lease_manager_->CanAddLease(holder2, inode_id3));

  ASSERT_TRUE(lease_manager_->AddLease(holder2, inode_id3));
}

TEST_F(LeaseManagerTest, RemoveLease) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder1, inode_id2);
  lease_manager_->AddLease(holder2, inode_id3);

  ASSERT_FALSE(lease_manager_->CheckLease(holder2, inode_id2));
  ASSERT_FALSE(lease_manager_->CanAddLease(holder2, inode_id2));
  ASSERT_TRUE(lease_manager_->RemoveLease(inode_id2));
  ASSERT_TRUE(lease_manager_->CanAddLease(holder2, inode_id2));

  ASSERT_TRUE(lease_manager_->AddLease(holder2, inode_id2));
  ASSERT_EQ(lease_manager_->Stats().lease_num, 2);

  lease_manager_->RemoveAllLeases();
  ASSERT_EQ(lease_manager_->Stats().lease_num, 0);
}

TEST_F(LeaseManagerTest, RenewLease) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder1, inode_id2);
  lease_manager_->AddLease(holder2, inode_id3);

  ASSERT_FALSE(lease_manager_->IsExpiredSoftLimit(holder1));
  ASSERT_FALSE(lease_manager_->IsExpiredHardLimit(holder1));
  lease_manager_->AddLease(holder2, inode_id2);

  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_soft_limit_ms * 3 / 2));
  ASSERT_TRUE(lease_manager_->IsExpiredSoftLimit(holder1));
  ASSERT_FALSE(lease_manager_->IsExpiredHardLimit(holder1));

  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_soft_limit_ms * 2));
  ASSERT_TRUE(lease_manager_->IsExpiredSoftLimit(holder1));
  ASSERT_TRUE(lease_manager_->IsExpiredHardLimit(holder1));

  lease_manager_->RenewLease(holder1, Lease::DEFAULT_HOLDER_IP);
  ASSERT_FALSE(lease_manager_->IsExpiredSoftLimit(holder1));
  ASSERT_FALSE(lease_manager_->IsExpiredHardLimit(holder1));

  ASSERT_TRUE(lease_manager_->IsExpiredSoftLimit(holder2));
  ASSERT_TRUE(lease_manager_->IsExpiredHardLimit(holder2));

  lease_manager_->RenewAllLeases();
  ASSERT_FALSE(lease_manager_->IsExpiredSoftLimit(holder2));
  ASSERT_FALSE(lease_manager_->IsExpiredHardLimit(holder2));
}

TEST_F(LeaseManagerTest, HolderIp) {
  // init
  lease_manager_->AddLease(holder1, inode_id1);
  {
    auto detail = lease_manager_->TestOnlyDetailStats(inode_id1, "", "");
    ASSERT_EQ(1, detail.leases.size());
    ASSERT_EQ(holder1, detail.leases[0].first);
    ASSERT_EQ(Lease::DEFAULT_HOLDER_IP, detail.leases[0].second.holder_ip);
  }

  // no ip
  lease_manager_->RenewLease(holder1, Lease::DEFAULT_HOLDER_IP);
  {
    auto detail = lease_manager_->TestOnlyDetailStats(inode_id1, "", "");
    ASSERT_EQ(1, detail.leases.size());
    ASSERT_EQ(holder1, detail.leases[0].first);
    ASSERT_EQ(Lease::DEFAULT_HOLDER_IP, detail.leases[0].second.holder_ip);
  }

  // ip1
  lease_manager_->RenewLease(holder1, ip1);
  {
    auto detail = lease_manager_->TestOnlyDetailStats(inode_id1, "", "");
    ASSERT_EQ(1, detail.leases.size());
    ASSERT_EQ(holder1, detail.leases[0].first);
    ASSERT_EQ(ip1, detail.leases[0].second.holder_ip);
  }

  // ip not change
  lease_manager_->RenewLease(holder1, Lease::DEFAULT_HOLDER_IP);
  {
    auto detail = lease_manager_->TestOnlyDetailStats(inode_id1, "", "");
    ASSERT_EQ(1, detail.leases.size());
    ASSERT_EQ(holder1, detail.leases[0].first);
    ASSERT_EQ(ip1, detail.leases[0].second.holder_ip);
  }

  // ip not change
  lease_manager_->RenewLease(holder1, ip2);
  {
    auto detail = lease_manager_->TestOnlyDetailStats(inode_id1, "", "");
    ASSERT_EQ(1, detail.leases.size());
    ASSERT_EQ(holder1, detail.leases[0].first);
    ASSERT_EQ(ip1, detail.leases[0].second.holder_ip);
  }
}

TEST_F(LeaseManagerTest, ReassignLease) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder1, inode_id2);
  ASSERT_TRUE(lease_manager_->ReassignLease(holder1, inode_id1, holder2));

  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms * 2));

  ASSERT_FALSE(lease_manager_->ReassignLease(holder1, inode_id1, holder2));
  ASSERT_TRUE(lease_manager_->ReassignLease(holder2, inode_id1, holder1));
  ASSERT_TRUE(lease_manager_->CheckLease(holder1, inode_id2));
  ASSERT_FALSE(lease_manager_->CheckLease(holder2, inode_id1));
  ASSERT_TRUE(lease_manager_->CheckLease(holder1, inode_id1));
}

TEST_F(LeaseManagerTest, ExpiredHardLimit) {
  lease_manager_->AddLease(holder1, inode_id1);
  lease_manager_->AddLease(holder2, inode_id2);

  {
    std::this_thread::sleep_for(
        std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms * 2));
    LOG(INFO) << "GetExpiredHardLimitLeaseFilesAndHolders";
    auto leases =
        lease_manager_->TestOnlyGetExpiredHardLimitLeaseFilesAndHolders();
    ASSERT_EQ(leases.size(), 2);
    auto lease1 = leases.find(inode_id1);
    ASSERT_NE(lease1, leases.end());
    ASSERT_EQ(lease1->second, holder1);
    auto lease2 = leases.find(inode_id2);
    ASSERT_NE(lease2, leases.end());
    ASSERT_EQ(lease2->second, holder2);
  }

  {
    lease_manager_->AddLease(holder1, inode_id3);
    LOG(INFO) << "GetExpiredHardLimitLeaseFilesAndHolders";
    auto leases =
        lease_manager_->TestOnlyGetExpiredHardLimitLeaseFilesAndHolders();
    ASSERT_EQ(leases.size(), 1);
    auto lease2 = leases.find(inode_id2);
    ASSERT_NE(lease2, leases.end());
    ASSERT_EQ(lease2->second, holder2);
  }

  {
    LOG(INFO) << "GetExpiredHardLimitLeaseFilesAndHolders";
    lease_manager_->RenewLease(holder2, Lease::DEFAULT_HOLDER_IP);
    auto leases =
        lease_manager_->TestOnlyGetExpiredHardLimitLeaseFilesAndHolders();
    ASSERT_EQ(leases.size(), 0);
  }
}

TEST_F(LeaseManagerTest, ConcurrentTest) {
  std::random_device rd;
  std::mt19937 gen(rd());
  std::uniform_int_distribution<> dis(0, 2);
  lease_manager_.reset();
  SetExpiredTime(100, 200);
  size_t num_thread = 10;
  int file_per_lease = 50;
  CountDownLatch latch(num_thread * file_per_lease);
  lease_manager_ = std::make_unique<LeaseManager>();
  auto task = [&](uint64_t inode_id,
                  const std::string& orig_holder,
                  const std::string& new_holder) -> Status {
    if (dis(gen) != 0) {
      return Status(JavaExceptions::kIOException);
    }
    latch.CountDown();
    CHECK(lease_manager_->RemoveLease(inode_id));
    return Status();
  };
  auto lease_monitor =
      std::make_unique<LeaseMonitor>(lease_manager_.get(), task);
  lease_monitor->Start();
  cnetpp::concurrency::ThreadPool tp("test");
  tp.set_num_threads(num_thread);
  tp.Start();

  for (size_t i = 0; i < num_thread; ++i) {
    tp.AddTask([this, i, file_per_lease, num_thread]() -> bool {
      std::string client_name = std::to_string(i) + "#";
      std::this_thread::sleep_for(std::chrono::milliseconds(
          FLAGS_lease_expired_soft_limit_ms / num_thread));
      for (int j = 0; j < file_per_lease; ++j) {
        lease_manager_->AddLease(client_name, i * 100 + j);
      }
      for (int s = 0; s < num_thread / 2; ++s) {
        std::this_thread::sleep_for(std::chrono::milliseconds(
            FLAGS_lease_expired_soft_limit_ms / num_thread));
        lease_manager_->RenewLease(client_name, Lease::DEFAULT_HOLDER_IP);
      }
      return true;
    });
  }
  latch.Await();
  // timing issue: when latch counts down to zero
  // paths just are added to to_remove or to_check list.
  // after a while, then lease_manager clear its leases and paths.
  std::this_thread::sleep_for(
    std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms * 3));
  auto stats = lease_manager_->Stats();
  ASSERT_EQ(stats.lease_num, 0);
  ASSERT_EQ(stats.path_num, 0);
  ASSERT_EQ(stats.holder_num, 0);
  tp.Stop(true);
}

}

}  // namespace dancenn
