// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/07/17
// Description

#include "lease/lease_manager_v2.h"  // For LeaseSliceV2, LeaseManagerV2.

#include <gmock/gmock.h>  // For EXPECT_CALL, etc.
#include <gtest/gtest.h>  // For TEST, etc.

#include <chrono>   // For chrono.
#include <cstdint>  // For uint64_t.
#include <ctime>    // For time_t.
#include <memory>   // For make_shared, etc.

#include "base/java_exceptions.h"               // For JavaExceptions.
#include "base/time_util.h"                     // For TimeUtilV2.
#include "lease/lease.h"                        // For Lease.
#include "test/base/time_util.h"                // For GMockTimeUtill.
#include "test/lease/gmock_lease_manager.h"     // For GMockLeaseManagerV2.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage.

DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);

namespace dancenn {

class TestableLease : public Lease {
 public:
  TestableLease(const std::string& name, uint64_t update_time_ms)
      : Lease(name) {
    last_update_time_ = std::chrono::system_clock::from_time_t(
        static_cast<std::time_t>(update_time_ms / 1000));
  }
};

TEST(LeaseSliceV2Test, GC) {
  EXPECT_LT(FLAGS_lease_expired_soft_limit_ms,
            FLAGS_lease_expired_hard_limit_ms);
  LeaseSliceV2 slice;
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc([]() { return 1689596490000; });
  EXPECT_TRUE(slice.TestOnlyAddLease(std::make_shared<TestableLease>(
      "a", 1689596490000 - FLAGS_lease_expired_soft_limit_ms - 1)));
  EXPECT_TRUE(slice.TestOnlyAddLease(std::make_shared<TestableLease>(
      "b", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  EXPECT_TRUE(slice.TestOnlyAddLease(std::make_shared<TestableLease>(
      "c", 1689596490000 - FLAGS_lease_expired_soft_limit_ms - 1)));
  EXPECT_TRUE(slice.TestOnlyAddLease(std::make_shared<TestableLease>(
      "d", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  slice.GC();
  EXPECT_TRUE(slice.IsLeasePresent("a"));
  EXPECT_FALSE(slice.IsLeasePresent("b"));
  EXPECT_TRUE(slice.IsLeasePresent("c"));
  EXPECT_FALSE(slice.IsLeasePresent("d"));
}

class LeaseManagerV2Test : public testing::Test {
 public:
  void SetUp() override {
    meta_storage_ = std::make_unique<testing::StrictMock<GMockMetaStorage>>();
    lease_manager_ = std::make_unique<LeaseManagerV2>();
    lease_manager_->SetMetaStorage(meta_storage_.get());
    time_util_ = std::make_unique<testing::StrictMock<GMockTimeUtil>>();
    TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
        [this]() { return time_util_->GetNowEpochMs(); });
  }

 protected:
  std::unique_ptr<testing::StrictMock<GMockMetaStorage>> meta_storage_;
  std::unique_ptr<LeaseManagerV2> lease_manager_;
  std::unique_ptr<testing::StrictMock<GMockTimeUtil>> time_util_;
};

TEST_F(LeaseManagerV2Test, GC) {
  EXPECT_LT(FLAGS_lease_expired_soft_limit_ms,
            FLAGS_lease_expired_hard_limit_ms);
  LeaseManagerV2 manager;
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc([]() { return 1689596490000; });
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "a", 1689596490000 - FLAGS_lease_expired_soft_limit_ms - 1)));
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "b", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "c", 1689596490000 - FLAGS_lease_expired_soft_limit_ms - 1)));
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "d", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 4);
  manager.GC();
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 2);
}

TEST_F(LeaseManagerV2Test, InlineGC01) {
  GMockLeaseManagerV2 manager;
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc([]() { return 1689596490000; });
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "b", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "d", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 2);
  manager.SetMetaStorage(meta_storage_.get());
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_)).Times(1);
  EXPECT_CALL(manager, ShouldPerformInlineGC())
      .Times(1)
      .WillOnce(testing::Return(true));
  manager.AddLease("e", 17123);
  manager.TestOnlyResetMetaStorage();
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 0);
}

TEST_F(LeaseManagerV2Test, InlineGC02) {
  GMockLeaseManagerV2 manager;
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc([]() { return 1689596490000; });
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "b", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  EXPECT_TRUE(manager.TestOnlyAddLease(std::make_shared<TestableLease>(
      "d", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 2);
  manager.SetMetaStorage(meta_storage_.get());
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("e"));
  EXPECT_CALL(manager, ShouldPerformInlineGC())
      .Times(1)
      .WillOnce(testing::Return(true));
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
      []() { return TimeUtil::GetNowEpochMs(); });
  manager.AddLease("e", 17123);
  manager.TestOnlyResetMetaStorage();
  manager.UpdateStats();
  EXPECT_EQ(manager.MetricOnlyEstimateLeaseSize(), 1);
}

TEST_F(LeaseManagerV2Test, CanRecoverLeaseWhenNewHolderIsHoldingThatFile) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-2"));
  Status s = lease_manager_->CanRecoverLease(17123,
                                             "client-name-2",
                                             "client-name-2",
                                             /*force=*/false,
                                             /*allow_self_seize_lease=*/false,
                                             /*use_soft_limit=*/false,
                                             nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(
      s.message(),
      "Failed to create file 17123 for client-name-2 for holder client-name-2 "
      "because current holder is trying to recreate file");
}

TEST_F(LeaseManagerV2Test,
       CanRecoverLeaseWithForceWhenNewHolderIsHoldingThatFile) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-2"));
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-2",
                                    "client-name-2",
                                    /*force=*/true,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    nullptr)
                  .IsOK());
}

TEST_F(LeaseManagerV2Test,
       CanRecoverLeaseWithAllowSelfSeizeLeaseWhenNewHolderIsHoldingThatFile) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(2)
      .WillOnce(testing::Return(1689596490000 -
                                FLAGS_lease_expired_hard_limit_ms - 1))
      .WillOnce(testing::Return(1689596490000));
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-2"));
  lease_manager_->Start();
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-2",
                                    "client-name-2",
                                    /*force=*/false,
                                    /*allow_self_seize_lease=*/true,
                                    /*use_soft_limit=*/false,
                                    nullptr)
                  .IsOK());
  lease_manager_->Stop();
}

TEST_F(LeaseManagerV2Test, CanRecoverLeaseOnNotUcFile) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return(""));
  Status s = lease_manager_->CanRecoverLease(17123,
                                             "client-name-1",
                                             "client-name-2",
                                             /*force=*/false,
                                             /*allow_self_seize_lease=*/false,
                                             /*use_soft_limit=*/false,
                                             nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(s.message(),
            "Failed to create file 17123 for client-name-2 "
            "for client client-name-1 because no leases found");
}

TEST_F(LeaseManagerV2Test, CanRecoverLeaseWhenOrigHolderIsNotHoldingThatFile) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-3"));
  Status s = lease_manager_->CanRecoverLease(17123,
                                             "client-name-1",
                                             "client-name-2",
                                             /*force=*/false,
                                             /*allow_self_seize_lease=*/false,
                                             /*use_soft_limit=*/false,
                                             nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Current lease holder client-name-1 does not match file creator, "
            "inode={id=17123,client_name=client-name-3}");
}

TEST_F(LeaseManagerV2Test,
       CanRecoverLeaseWithForceWhenOrigHolderIsNotHoldingThatFile) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-3"));
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-1",
                                    "client-name-2",
                                    /*force=*/true,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    nullptr)
                  .IsOK());
}

TEST_F(LeaseManagerV2Test, CanRecoverLeaseWhenLeaseDoesNotExpireLimit) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-1"));
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(1)
      .WillOnce(testing::Return(1689596490000));
  EXPECT_TRUE(lease_manager_->TestOnlyAddLease(std::make_shared<TestableLease>(
      "client-name-1", 1689596490000 - FLAGS_lease_expired_soft_limit_ms - 1)));
  bool need_check_last_block = false;
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-1",
                                    "client-name-2",
                                    /*force=*/false,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    &need_check_last_block)
                  .IsFalse());
  EXPECT_TRUE(need_check_last_block);
}

TEST_F(LeaseManagerV2Test, CanRecoverLeaseWhenLeaseExpiresLimit) {
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-1"));
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(1)
      .WillOnce(testing::Return(1689596490000));
  EXPECT_TRUE(lease_manager_->TestOnlyAddLease(std::make_shared<TestableLease>(
      "client-name-1", 1689596490000 - FLAGS_lease_expired_hard_limit_ms - 1)));
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-1",
                                    "client-name-2",
                                    /*force=*/false,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    nullptr)
                  .IsOK());
}

TEST_F(LeaseManagerV2Test,
       CanRecoverLeaseWhenLeaseNotPresentAndActiveDurationIsNotSufficient) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(2)
      // For LeaseManagerV2::Start.
      .WillOnce(testing::Return(16895964900000 -
                                FLAGS_lease_expired_soft_limit_ms - 1))
      // For LeaseManagerV2::IsActiveDurationSufficient.
      .WillOnce(testing::Return(16895964900000));
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-1"));
  lease_manager_->Start();
  bool need_check_last_block = true;
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-1",
                                    "client-name-2",
                                    /*force=*/false,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    &need_check_last_block)
                  .IsFalse());
  EXPECT_TRUE(need_check_last_block);
  lease_manager_->Stop();
}

TEST_F(LeaseManagerV2Test,
       CanRecoverLeaseWhenLeaseNotPresentAndActiveDurationIsSufficient) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(2)
      // For LeaseManagerV2::Start.
      .WillOnce(testing::Return(16895964900000 -
                                FLAGS_lease_expired_hard_limit_ms - 1))
      // For LeaseManagerV2::IsActiveDurationSufficient.
      .WillOnce(testing::Return(16895964900000));
  EXPECT_CALL(*meta_storage_, GetLeaseHolder(17123, testing::_))
      .Times(1)
      .WillOnce(testing::Return("client-name-1"));
  lease_manager_->Start();
  EXPECT_TRUE(lease_manager_
                  ->CanRecoverLease(17123,
                                    "client-name-1",
                                    "clientx-name-2",
                                    /*force=*/false,
                                    /*allow_self_seize_lease=*/false,
                                    /*use_soft_limit=*/false,
                                    nullptr)
                  .IsOK());
  lease_manager_->Stop();
}

}  // namespace dancenn