// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/07/17
// Description

#include "lease/lease_monitor_v2.h"  // For LeaseMonitorV2.

#include <gmock/gmock.h>  // For StrictMock, etc.
#include <gtest/gtest.h>  // For Test, etc.

#include <string>  // For string.

#include "base/constants.h"                  // For kNamenodeLeaseHolder.
#include "base/status.h"                     // For Status, Code.
#include "namespace/inode.h"                 // For INodeID.
#include "test/lease/gmock_lease_manager.h"  // For GMockLeaseManagerV2.

namespace dancenn {

TEST(LeaseMonitorV2Test, HandleLeaseCallsReleaseLease) {
  testing::StrictMock<GMockLeaseManagerV2> lease_manager;
  bool release_lease_called = false;
  LeaseMonitorV2 lease_monitor(
      &lease_manager,
      [&release_lease_called](INodeID inode_id,
                              const std::string& orig_holder,
                              const std::string& new_holder) {
        release_lease_called = true;
        return Status();
      });
  EXPECT_CALL(lease_manager,
              CanRecoverLease(17123,
                              "client-name-1",
                              testing::StartsWith(kNamenodeLeaseHolder),
                              /*force=*/false,
                              /*allow_self_seize_lease=*/true,
                              /*use_soft_limit=*/false,
                              testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status()));
  lease_monitor.HandleLease(17123, "client-name-1");
  EXPECT_TRUE(release_lease_called);
}

TEST(LeaseMonitorV2Test, HandleLeaseDoesnotCallReleaseLease) {
  testing::StrictMock<GMockLeaseManagerV2> lease_manager;
  bool release_lease_called = false;
  LeaseMonitorV2 lease_monitor(
      &lease_manager,
      [&release_lease_called](INodeID inode_id,
                              const std::string& orig_holder,
                              const std::string& new_holder) {
        release_lease_called = true;
        return Status();
      });
  EXPECT_CALL(lease_manager,
              CanRecoverLease(17123,
                              "client-name-1",
                              testing::StartsWith(kNamenodeLeaseHolder),
                              /*force=*/false,
                              /*allow_self_seize_lease=*/true,
                              /*use_soft_limit=*/false,
                              testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kFalse)));
  lease_monitor.HandleLease(17123, "client-name-1");
  EXPECT_FALSE(release_lease_called);
}

}  // namespace dancenn