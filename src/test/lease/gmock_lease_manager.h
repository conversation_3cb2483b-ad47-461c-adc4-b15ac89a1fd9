// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/17
// Description

#ifndef TEST_LEASE_GMOCK_LEASE_MANAGER_H_
#define TEST_LEASE_GMOCK_LEASE_MANAGER_H_

#include <gmock/gmock.h>  // For MOCK_METHOD1, etc.

#include <cstdint>  // For uint64_t.
#include <string>   // For string.
#include <vector>   // For vector.

#include "base/status.h"               // For Status.
#include "lease/lease_manager_base.h"  // For LeaseManagerBase, LeaseStats, DetailLeaseStats.
#include "lease/lease_manager_v2.h"    // For LeaseManagerV2.
#include "namespace/inode.h"         // For INodeID.
#include "namespace/meta_storage.h"  // For MetaStorage.

namespace dancenn {

class GMockLeaseManager : public LeaseManagerBase {
 public:
  MOCK_METHOD1(SetMetaStorage, void(MetaStorage* ms));

  MOCK_METHOD0(Stats, LeaseStats());
  MOCK_CONST_METHOD0(MetricOnlyEstimateLeaseSize, uint64_t());
  MOCK_CONST_METHOD0(MetricOnlyEstimateUcFileSize, uint64_t());
  MOCK_METHOD0(UpdateStats, void());
  MOCK_CONST_METHOD0(GetActiveClients, std::vector<std::string>());
  MOCK_METHOD3(TestOnlyDetailStats,
               DetailLeaseStats(INodeID inode_id,
                                const std::string& holder,
                                const std::string& ip));

  MOCK_CONST_METHOD2(GetLeaseHolder,
                     std::string(uint64_t inode_id, bool need_lock));

  MOCK_METHOD2(CheckLease, bool(const std::string& holder, uint64_t inode_id));
  MOCK_CONST_METHOD2(CanAddLease,
                     bool(const std::string& holder, INodeID inode_id));
  MOCK_METHOD5(CanRecoverLease,
               Status(uint64_t inode_id,
                      const std::string& orig_holder,
                      const std::string& new_holder,
                      bool force,
                      bool* need_check_last_block));

  MOCK_METHOD2(AddLease, bool(const std::string& holder, INodeID inode_id));
  MOCK_METHOD1(RemoveLease, bool(INodeID inode_id));
  MOCK_METHOD0(RemoveAllLeases, void());
  MOCK_METHOD2(RenewLease,
               void(const std::string& holder, const std::string& holder_ip));
  MOCK_METHOD0(RenewAllLeases, void());
  MOCK_METHOD3(ReassignLease,
               bool(const std::string& holder,
                    uint64_t inode_id,
                    const std::string& new_holder));

  MOCK_METHOD1(SpeedUpReLease, bool(INodeID inode_id));
};

class GMockLeaseManagerV2 : public LeaseManagerV2 {
 public:
  MOCK_METHOD7(CanRecoverLease,
               Status(INodeID inode_id,
                      const std::string& orig_holder,
                      const std::string& new_holder,
                      bool force,
                      bool allow_self_seize_lease,
                      bool use_soft_limit,
                      bool* need_check_last_block));
  MOCK_CONST_METHOD0(ShouldPerformInlineGC, bool());
};

}  // namespace dancenn

#endif  // TEST_LEASE_GMOCK_LEASE_MANAGER_H_
