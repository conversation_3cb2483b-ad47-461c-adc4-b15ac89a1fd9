// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef TEST_MOCK_EDIT_LOG_SENDER_H_
#define TEST_MOCK_EDIT_LOG_SENDER_H_

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>

#include <string>
#include <vector>
#include <mutex>

#include "base/constants.h"
#include "edit/edit_log_context.h"
#include "edit/sender_base.h"
#include "edit/edit_log_sync_listener.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class MockEditLogSender : public EditLogSenderBase {
 public:
  explicit MockEditLogSender(std::shared_ptr<EditLogContextBase>& ctx,
      int64_t tx_id)
      : edit_log_ctx_(ctx),
        next_transaction_id_(tx_id + 1) {}
  ~MockEditLogSender() {}

  // -------- New Edit Log Begins.
  // File related.
  int64_t LogOpenFileV2(
      const std::string& path,
      const INode& inode,
      const INode& parent,
      bool overwrite,
      const INode* overwrite_inode,
      bool move_to_recycle_bin,
      const std::string* rb_path,
      const INode* rb_inode,
      const INode* rb_parent,
      const std::vector<BlockInfoProto>& add_block_bips,
      const std::vector<std::vector<std::string>>& bips_expected_locs,
      const std::vector<INodeID>& ancestors_id,
      const std::vector<INodeID>* rb_ancestors_id,
      const SnapshotLog& old_inode_snaplog,
      const SnapshotLog& parent_snaplog,
      const SnapshotLog& rb_parent_snaplog,
      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogAppend(const std::string path,
                    const INode& inode,
                    const INode& parent,
                    const INode& old_inode,
                    const std::vector<INodeID>& ancestors_id,
                    const SnapshotLog& inode_snaplog,
                    const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogAddBlockV2(const std::string& path,
                        const INode& inode,
                        const BlockInfoProto* penultimate_bip_tbc,
                        const BlockInfoProto& last_bip_tbuc,
                        const INode& old_inode,
                        const std::vector<INodeID>& ancestors_id,
                        const std::vector<std::string>& dn_uuids,
                        const SnapshotLog& inode_snaplog,
                        const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogAbandonBlock(const std::string& path,
                          const INode& inode,
                          BlockID abandoned_blk_id,
                          const INode& old_inode,
                          const std::vector<INodeID>& ancestors_id,
                          const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogUpdatePipeline(const std::string& path,
                            const INode& file,
                            const BlockInfoProto& last_bip_tbuc,
                            const INode& old_inode,
                            const std::vector<INodeID>& ancestors_id,
                            const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogFsync(const std::string& path,
                   const INode& inode,
                   const BlockInfoProto* last_bip_tbuc,
                   const INode& old_inode,
                   const std::vector<INodeID>& ancestors_id,
                   const SnapshotLog& inode_snaplog,
                   const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogCommitBlockSynchronization(const std::string& path,
                                        bool delete_block,
                                        bool close_file,
                                        const INode& inode,
                                        const BlockInfoProto& last_bip,
                                        const INode& old_inode,
                                        std::vector<INodeID>& ancestors_id,
                                        const SnapshotLog& inode_snaplog) {
    return MockCommitLog();
  }

  int64_t LogUpdateBlocksV2(const std::string& path,
                            const INode& inode,
                            const INode& old_inode,
                            const std::vector<INodeID>& ancestors_id,
                            const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogCloseFileV2(const std::string& path,
                         const INode& inode,
                         const BlockInfoProto* last_bip_tbc,
                         BlockID dropped_blk_id,
                         const INode& old_inode,
                         const std::vector<INodeID>& ancestors_id,
                         const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogReassignLeaseV2(const std::string& path,
                             const INode& inode,
                             const INode& old_inode,
                             const std::string& lease_holder,
                             const std::string& new_holder,
                             const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogConcatV2(const std::string& parent_path,
                      const std::string& target,
                      const std::vector<std::string>& srcs,
                      const INode& target_inode,
                      const INode& old_target_inode,
                      const std::vector<INode>& src_inodes,
                      const std::vector<BlockInfoProto>& src_bips,
                      const INode& parent,
                      uint64_t timestamp_in_ms,
                      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogMergeBlocks(const std::string& path,
                         const INode& inode,
                         const BlockInfoProto& bip,
                         const std::vector<BlockProto>& depred_blks,
                         const INode& old_inode,
                         const std::vector<INodeID>& ancestors_id,
                         const SnapshotLog& inode_snaplog) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAllocateBlockIdV2(uint64_t) override {
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    return MockCommitLog();
  }

  int64_t LogSetGenerationStampV1(uint64_t) {
    return MockCommitLog();
  }

  int64_t LogSetGenerationStampV2(uint64_t, bool force_send) override {
    if (!force_send && FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    return MockCommitLog();
  }

  int64_t LogPin(const std::string& path,
                 const INode& inode,
                 const INode& old_inode,
                 const JobInfoOpBody& job,
                 const ManagedJobId& cancel_job_id,
                 const LogRpcInfo& log_rpc_info,
                 bool update_txid) override {
    return MockCommitLog();
  }

  int64_t LogReconcileINodeAttrs(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const std::set<int64_t>& expired_ttl,
      const std::set<int64_t>& new_ttl,
      const std::vector<ManagedJobId>& cancel_job_id) override {
    return MockCommitLog();
  }

  int64_t LogUpdateATimeProtos(const ATimeToBeUpdateProtos& atimes) override {
    return MockCommitLog();
  }

  int64_t LogPersistJobInfo(const JobInfoOpBody& job) override {
    return MockCommitLog();
  }

  // Block related.
  int64_t LogApproveUploadBlk(const BlockInfoProto& bip,
                              const BlockInfoProto& old_bip) override {
    return MockCommitLog();
  }

  int64_t LogPersistBlk(const BlockInfoProto& bip,
                        const BlockInfoProto& old_bip) override {
    return MockCommitLog();
  }

  int64_t LogDelDepringBlks(const DepringBlksToBeDel& blks) override {
    return MockCommitLog();
  }

  int64_t LogDelDepredBlks(const DepredBlksToBeDel& blks) override {
    return MockCommitLog();
  }

  int64_t LogFlushBlockInfoProtos(const BlockInfoProtos& bips) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  // Dir tree related.
  int64_t LogMkdirV2(const std::string& path,
                     const INode& inode,
                     const INode& parent,
                     const LogRpcInfo& log_rpc_info,
                     const std::vector<INodeID>& ancestors_id,
                     const SnapshotLog& parent_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogDeleteV2(const std::string& path,
                      const INode& inode,
                      const INode& parent,
                      uint64_t timestamp_in_ms,
                      const std::vector<INodeID>& ancestors_id,
                      const SnapshotLog& inode_snaplog,
                      const SnapshotLog& parent_snaplog,
                      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogRenameOldV2(const std::string& src_path,
                         const std::string& dst_path,
                         const INode& src_inode,
                         const INode& dst_inode,
                         const INode& src_parent,
                         const INode& dst_parent,
                         uint64_t timestamp_in_ms,
                         const std::vector<INodeID>& src_ancestors_id,
                         const std::vector<INodeID>& dst_ancestors_id,
                         const SnapshotLog& src_inode_snaplog,
                         const SnapshotLog& src_parent_snaplog,
                         const SnapshotLog& dst_parent_snaplog,
                         const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogRenameV2(const std::string& src_path,
                      const std::string& dst_path,
                      const INode& src_inode,
                      const INode& new_dst_inode,
                      const INode& src_parent,
                      const INode& dst_parent,
                      bool overwrite,
                      const INode* old_dst_inode,
                      bool move_to_recycle_bin,
                      const std::string* rb_path,
                      const INode* rb_inode,
                      const INode* rb_parent,
                      uint64_t timestamp_in_ms,
                      const std::vector<INodeID>& src_ancestors_id,
                      const std::vector<INodeID>& dst_ancestors_id,
                      const std::vector<INodeID>* rb_ancestors_id,
                      const SnapshotLog& src_inode_snaplog,
                      const SnapshotLog& dst_inode_snaplog,
                      const SnapshotLog& src_parent_snaplog,
                      const SnapshotLog& dst_parent_snaplog,
                      const SnapshotLog& rb_parent_snaplog,
                      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  // Set* related.
  int64_t LogSetReplicationV2(const std::string& path,
                              const INode& inode,
                              const INode& old_inode,
                              uint32_t replication,
                              const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetStoragePolicyV2(const std::string& path,
                                const INode& inode,
                                const INode& old_inode,
                                uint32_t policy_id,
                                const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetReplicaPolicyV2(const std::string& path,
                                const INode& inode,
                                const INode& old_inode,
                                int32_t policy_id,
                                const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetDirReplicaPolicyV2(const std::string& path,
                                   const INode& inode,
                                   const INode& old_inode,
                                   int32_t policy_id,
                                   const std::string& dc,
                                   const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetQuotaV2(const std::string& src,
                        uint64_t ns_quota,
                        uint64_t ds_quota,
                        const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetPermissionsV2(const std::string& path,
                              const INode& inode,
                              const INode& old_inode,
                              uint16_t permission,
                              const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetOwnerV2(const std::string& path,
                        const INode& inode,
                        const INode& old_inode,
                        const std::string& username,
                        const std::string& groupname,
                        const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSetTimesV2(const std::string& src,
                        uint64_t mtime,
                        uint64_t atime,
                        const INode& inode,
                        const INode& old_inode,
                        const SnapshotLog& inode_snaplog) override {
    return MockCommitLog();
  }

  int64_t LogSymlinkV2(const std::string& path,
                       const INode& node,
                       const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogSetAclV2(
      const std::string& src,
      const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>& entries)
      override {
    return MockCommitLog();
  }

  int64_t LogSetXAttrsV2(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const SnapshotLog& inode_snaplog,
      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogRemoveXAttrsV2(
      const std::string& path,
      const INode& inode,
      const INode& old_inode,
      const ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const SnapshotLog& inode_snaplog,
      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogSetLifecyclePolicy(
      const INode& inode,
      const std::string& path,
      uint64_t ts,
      const cloudfs::LifecyclePolicyProto& policy,
      const LogRpcInfo& rpc_info) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogUnsetLifecyclePolicy(
      const INodeID& id,
      const std::string* path,
      const LogRpcInfo& rpc_info) {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogSetAZBlacklist(
      const std::string& azs) {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  // Acc related
  int64_t LogAccSyncDummy() override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccSyncListingBatchAdd(
      const std::string& dir_path,
      const INode& dir_inode,
      const std::vector<INodeWithBlocks>& file_blocks) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccSyncListingBatchUpdate(
      const std::string& dir_path,
      const INode& dir_inode,
      const std::vector<INode>& inodes_to_update,
      const std::vector<INode>& old_inodes) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccSyncUpdateINode(const std::string& path,
                                const INode& inode,
                                const INode& old_inode) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccSyncAddFile(const std::string& path,
                            const INode& parent,
                            const INodeWithBlocks& file,
                            const INode* file_to_del) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccPersistFile(const std::string& path,
                            const INode& parent,
                            const INode& inode,
                            const INode& old_inode) override {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAccUpdateBlockInfo(const BlockInfoProto& bip,
                                const BlockInfoProto& old_bip) {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  int64_t LogAllowSnapshotV2(const std::string&,
                             const INode&) override {
    return MockCommitLog();
  }

  int64_t LogDisallowSnapshotV2(const std::string&,
                                const INode&) override {
    return MockCommitLog();
  }

  int64_t LogCreateSnapshotV2(const std::string&,
                              const std::string&,
                              uint64_t,
                              const INode&,
                              uint64_t) override {
    return MockCommitLog();
  }

  int64_t LogDeleteSnapshotV2(const std::string&,
                              const std::string&,
                              uint64_t,
                              const INode&) override {
    return MockCommitLog();
  }

  int64_t LogRenameSnapshotV2(const std::string&,
                              const std::string&,
                              const std::string&,
                              uint64_t,
                              const INode&,
                              const SnapshotLog&) override {
    return MockCommitLog();
  }

  // -------- New Edit Log Ends.

  // log ops:
  int64_t LogOpenFile(const std::string& path, const INode& file,
      bool overwrite, const LogRpcInfo& log_rpc_info) override {
    (void) path;
    (void) file;
    (void) overwrite;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogCloseFile(const std::string& path, const INode& inode) override {
    (void) path;
    (void) inode;
    return MockCommitLog();
  }

  int64_t LogAddBlock(const std::string& path, const INode& file,
                      const LogRpcInfo& log_rpc_info) override {
    (void) path;
    (void) file;
    return MockCommitLog();
  }

  int64_t LogUpdateBlocks(const std::string& path, const INode& file,
                          const LogRpcInfo& log_rpc_info) override {
    (void) path;
    (void) file;
    (void) log_rpc_info;
    return MockCommitLog();
  }


  int64_t LogMkDir(const std::string& path, const INode& inode) override {
    (void) path;
    (void) inode;
    return MockCommitLog();
  }

  int64_t LogRename(const std::string& src,
                    const std::string& dst,
                    uint64_t timestamp,
                    const LogRpcInfo& log_rpc_info) override {
    (void) src;
    (void) dst;
    (void) timestamp;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogRename(const std::string& src, const std::string& dst,
                    uint64_t timestamp, const LogRpcInfo& log_rpc_info,
                    bool overwrite) override {
    (void) src;
    (void) dst;
    (void) timestamp;
    (void) log_rpc_info;
    (void) overwrite;
    return MockCommitLog();
  }

  int64_t LogSetReplication(const std::string& src,
                            uint32_t replication) override {
    (void) src;
    (void) replication;
    return MockCommitLog();
  }

  int64_t LogSetStoragePolicy(const std::string& src,
                              uint32_t policy_id) override {
    (void) src;
    (void) policy_id;
    return MockCommitLog();
  }

  int64_t LogSetReplicaPolicy(const std::string& src, int32_t id) override {
    (void) src;
    (void) id;
    return MockCommitLog();
  }

  int64_t LogSetDirReplicaPolicy(const std::string& src,
                                 int32_t id, const std::string& dc) override {
    (void) src;
    (void) id;
    (void) dc;
    return MockCommitLog();
  }

  int64_t LogSetQuota(const std::string& src, uint64_t ns_quota,
                      uint64_t ds_quota) override {
    (void) src;
    (void) ns_quota;
    (void) ds_quota;
    return MockCommitLog();
  }

  int64_t LogSetPermissions(const std::string& src,
                            uint16_t permission) override {
    (void) src;
    (void) permission;
    return MockCommitLog();
  }

  int64_t LogSetOwner(const std::string& src,
                      const std::string& username,
                      const std::string& groupname) override {
    (void) src;
    (void) username;
    (void) groupname;
    return MockCommitLog();
  }

  int64_t LogConcat(const std::string& trg,
                    const std::vector<std::string>& srcs, uint64_t timestamp,
                    const LogRpcInfo& log_rpc_info) override {
    (void) trg;
    (void) srcs;
    (void) timestamp;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogDelete(const std::string& src,
                    const LogRpcInfo& log_rpc_info) override {
    (void) src;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogGenerationStampV1(uint64_t genstamp) override {
    (void) genstamp;
    return MockCommitLog();
  }

  int64_t LogGenerationStampV2(uint64_t genstamp) override {
    uint64_t gs = genstamp;
    edit_log_ctx_->LogGenerationStampV2(nullptr, &gs);
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    CHECK_EQ(gs, genstamp);
    return MockCommitLog();
  }

  int64_t LogAllocateBlockId(uint64_t block_id) override {
    uint64_t blkid = block_id;
    edit_log_ctx_->LogAllocateBlockId(nullptr, &blkid);
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    CHECK_EQ(blkid, block_id);
    return MockCommitLog();
  }

  int64_t LogBlockIdAndGSv2(uint64_t* blockId, uint64_t* genstamp) override {
    edit_log_ctx_->LogAllocateBlockIdAndGSv2(
        nullptr, nullptr, blockId, genstamp);
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      return kInvalidTxId;
    }
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_ + 1;
    next_transaction_id_ += 2;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid - 1, 2);
    return txid;
  }

  int64_t LogTimes(const std::string& src, uint64_t mtime,
                   uint64_t atime) override {
    (void) src;
    (void) mtime;
    (void) atime;
    return MockCommitLog();
  }

  int64_t LogSymlink(const std::string& path, const INode& node,
                     const LogRpcInfo& log_rpc_info) override {
    (void) path;
    (void) node;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogReassignLease(const std::string& lease_holder,
                           const std::string& src,
                           const std::string& new_holder) override {
    (void) lease_holder;
    (void) src;
    (void) new_holder;
    return MockCommitLog();
  }

  int64_t LogSetAcl(const std::string& src,
      const google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto>&
                    entries) override {
    (void) src;
    (void) entries;
    return MockCommitLog();
  }

  int64_t LogSetXAttrs(const std::string& src,
      const google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const LogRpcInfo& log_rpc_info) override {
    (void) src;
    (void) xattrs;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogRemoveXAttrs(const std::string& src,
      const google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto>& xattrs,
      const LogRpcInfo& log_rpc_info) override {
    (void) src;
    (void) xattrs;
    (void) log_rpc_info;
    return MockCommitLog();
  }

  int64_t LogAccessCounterSnapshot(
      const cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto& snapshot) override {  // NOLINT(whitespace/line_length)
    (void) snapshot;
    return MockCommitLog();
  }

  int64_t LogSetBlockPufsInfo(const std::string& s) {
    return MockCommitLog();
  }

  int64_t LogDeleteDeprecatedBlockPufsInfo(uint64_t blk_id) {
    return MockCommitLog();
  }

  int64_t LogAllowSnapshot(const std::string& path) override {
    return MockCommitLog();
  }

  int64_t LogDisallowSnapshot(const std::string& path) override {
    return MockCommitLog();
  }

  int64_t LogCreateSnapshot(const std::string& path,
                            const std::string& name) override {
    return MockCommitLog();
  }

  int64_t LogDeleteSnapshot(const std::string& path,
                            const std::string& name) override {
    return MockCommitLog();
  }

  int64_t LogRenameSnapshot(const std::string& path,
                            const std::string& old_name,
                            const std::string& new_name) override {
    return MockCommitLog();
  }

  int64_t LogBatchCreateFile(
      const std::vector<std::string>& paths,
      const std::vector<INode>& inodes,
      const std::vector<INode>& overwritten_inodes,
      const std::vector<BlockInfoProto>& overwritten_bips,
      const std::vector<BlockInfoProto>& add_block_bips,
      const std::vector<std::vector<std::string>>& bips_expected_locs,
      // other
      const INode& parent,
      uint64_t timestamp_in_ms,
      const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogBatchCompleteFile(const std::vector<std::string>& paths,
                               const std::vector<INode>& complete_inodes,
                               const std::vector<INode>& deleted_inodes,
                               const std::vector<BlockInfoProto>& bips,
                               const std::vector<BlockInfoProto>& deleted_bips,
                               const std::vector<INode>& original_inodes,
                               // other
                               const INode& parent,
                               uint64_t timestamp_in_ms,
                               const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  int64_t LogBatchDeleteFile(const std::vector<std::string>& paths,
                             const std::vector<INode>& inodes,
                             const std::vector<BlockInfoProto>& bips,
                             // other
                             const INode& parent,
                             uint64_t timestamp_in_ms,
                             const LogRpcInfo& log_rpc_info) override {
    return MockCommitLog();
  }

  void Sync(bool force = false) override {
    (void) force;
  }

private:
  int64_t MockCommitLog() {
    std::unique_lock<std::mutex> lock(mutex_);
    auto txid = next_transaction_id_++;
    edit_log_ctx_->TestOnlyGetSyncListener()->TxFinish(txid, 1);
    return txid;
  }

  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  int64_t next_transaction_id_;
  std::mutex mutex_;
};

}  // namespace dancenn

#endif  // TEST_MOCK_EDIT_LOG_SENDER_H_
