//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "ut_config.h"

#include <stdlib.h>
#include <fstream>
#include <glog/logging.h>
#include <nlohmann/json.hpp>

static const std::string kUTConfigFile = ".cloudfsut.json";
static const std::string kUTConfigAK = "ak";
static const std::string kUTConfigSK = "sk";

namespace dancenn
{

UTConfig& UTConfig::Instance() {
  static UTConfig ut_config;
  return ut_config;
}

UTConfig::UTConfig() {
  CHECK(Load()) << "Failed to load UT config.";
}

bool UTConfig::Load() {
  const char* homeDir = getenv("HOME");
  std::string config_file_path = std::string(homeDir) + "/" + kUTConfigFile;
  std::ifstream f(config_file_path);
  CHECK(f.is_open()) << "config file not found: " << config_file_path;

  nlohmann::json conf;
  f >> conf;

  CHECK(conf.contains(kUTConfigAK)) << "ak not in config: " << config_file_path;
  ak_ = conf[kUTConfigAK];
  CHECK(!ak_.empty()) << "AK cannot be empty in config: " << config_file_path;

  CHECK(conf.contains(kUTConfigSK)) << "sk not in config: " << config_file_path;
  sk_ = conf[kUTConfigSK];
  CHECK(!sk_.empty()) << "SK cannot be empty in config: " << config_file_path;
  return true;
}
    
} // namespace dancenn
