// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/03/25
// Description

#ifndef TEST_PROTO_GENERATED_REPEATED_FIELD_H_
#define TEST_PROTO_GENERATED_REPEATED_FIELD_H_

#include <initializer_list>  // For initializer_list.

#include "google/protobuf/repeated_field.h"  // For RepeatedPtrField.

namespace dancenn {

template <typename T>
google::protobuf::RepeatedPtrField<T> BuildRepeatedPtrField(
    std::initializer_list<T> elements) {
  google::protobuf::RepeatedPtrField<T> field;
  for (const auto& e : elements) {
    *field.Add() = e;
  }
  return field;
}

}  // namespace dancenn

#endif  // TEST_PROTO_GENERATED_REPEATED_FIELD_H_
