// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/25
// Description

#include "test/proto/generated/cloudfs/datanode_protocol.h"

namespace dancenn {

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetBlock(const cloudfs::BlockProto& block) {
  *proto_.mutable_block() = block;
  return *this;
}

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetStatus(
        cloudfs::datanode::ReceivedDeletedBlockInfoProto::BlockStatus status) {
  proto_.set_status(status);
  return *this;
}

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetDeleteHint(const std::string& delete_hint) {
  proto_.set_deletehint(delete_hint);
  return *this;
}

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetUploadId(const std::string& upload_id) {
  proto_.set_uploadid(upload_id);
  return *this;
}

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetPufsName(const std::string& pufs_name) {
  proto_.set_pufsname(pufs_name);
  return *this;
}

ReceivedDeletedBlockInfoProtoBuilder& ReceivedDeletedBlockInfoProtoBuilder::
    SetFiuCmd(const std::string& fiu_cmd) {
  proto_.set_fiucmd(fiu_cmd);
  return *this;
}

cloudfs::datanode::ReceivedDeletedBlockInfoProto
ReceivedDeletedBlockInfoProtoBuilder::Build() {
  return proto_;
}

StorageReceivedDeletedBlocksProtoBuilder&
StorageReceivedDeletedBlocksProtoBuilder::SetStorageUuid(
    const std::string& storage_uuid) {
  proto_.set_storageuuid(storage_uuid);
  return *this;
}

StorageReceivedDeletedBlocksProtoBuilder&
StorageReceivedDeletedBlocksProtoBuilder::AddBlocks(
    const cloudfs::datanode::ReceivedDeletedBlockInfoProto& block) {
  *proto_.add_blocks() = block;
  return *this;
}

StorageReceivedDeletedBlocksProtoBuilder&
StorageReceivedDeletedBlocksProtoBuilder::SetStorage(
    const cloudfs::DatanodeStorageProto& storage) {
  *proto_.mutable_storage() = storage;
  return *this;
}

cloudfs::datanode::StorageReceivedDeletedBlocksProto
StorageReceivedDeletedBlocksProtoBuilder::Build() {
  return proto_;
}

}  // namespace dancenn