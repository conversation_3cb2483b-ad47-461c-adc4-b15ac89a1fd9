// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/25
// Description

#include "test/proto/generated/cloudfs/hdfs.h"

namespace dancenn {

DatanodeStorageProtoBuilder& DatanodeStorageProtoBuilder::SetStorageUuid(
    const std::string& storage_uuid) {
  proto_.set_storageuuid(storage_uuid);
  return *this;
}

DatanodeStorageProtoBuilder& DatanodeStorageProtoBuilder::SetState(
    cloudfs::DatanodeStorageProto::StorageState state) {
  proto_.set_state(state);
  return *this;
}

DatanodeStorageProtoBuilder& DatanodeStorageProtoBuilder::SetStorageType(
    cloudfs::StorageTypeProto storage_type) {
  proto_.set_storagetype(storage_type);
  return *this;
}

cloudfs::DatanodeStorageProto DatanodeStorageProtoBuilder::Build() {
  return proto_;
}

BlockProtoBuilder& BlockProtoBuilder::SetBlockId(uint64_t block_id) {
  proto_.set_blockid(block_id);
  return *this;
}

BlockProtoBuilder& BlockProtoBuilder::SetGenStamp(uint64_t gen_stamp) {
  proto_.set_genstamp(gen_stamp);
  return *this;
}

BlockProtoBuilder& BlockProtoBuilder::SetNumBytes(uint64_t num_bytes) {
  proto_.set_numbytes(num_bytes);
  return *this;
}

cloudfs::BlockProto& BlockProtoBuilder::Build() {
  return proto_;
}

}  // namespace dancenn