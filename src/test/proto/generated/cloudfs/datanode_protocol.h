// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/24
// Description

#ifndef TEST_PROTO_GENERATED_CLOUDFS_DATANODE_PROTOCOL_H_
#define TEST_PROTO_GENERATED_CLOUDFS_DATANODE_PROTOCOL_H_

#include <string>  // For string.

#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"  // For StorageReceivedDeletedBlocksProto.
#include "proto/generated/cloudfs/hdfs.pb.h"  // For BlockProto, DatanodeStorageProto.

namespace dancenn {

class ReceivedDeletedBlockInfoProtoBuilder {
 public:
  ReceivedDeletedBlockInfoProtoBuilder& SetBlock(
      const cloudfs::BlockProto& block);
  ReceivedDeletedBlockInfoProtoBuilder& SetStatus(
      cloudfs::datanode::ReceivedDeletedBlockInfoProto::BlockStatus status);
  ReceivedDeletedBlockInfoProtoBuilder& SetDeleteHint(
      const std::string& delete_hint);
  ReceivedDeletedBlockInfoProtoBuilder& SetUploadId(
      const std::string& upload_id);
  ReceivedDeletedBlockInfoProtoBuilder& SetPufsName(
      const std::string& pufs_name);
  ReceivedDeletedBlockInfoProtoBuilder& SetFiuCmd(const std::string& fiu_cmd);

  cloudfs::datanode::ReceivedDeletedBlockInfoProto Build();

 private:
  cloudfs::datanode::ReceivedDeletedBlockInfoProto proto_;
};

class StorageReceivedDeletedBlocksProtoBuilder {
 public:
  StorageReceivedDeletedBlocksProtoBuilder& SetStorageUuid(
      const std::string& storage_uuid);
  StorageReceivedDeletedBlocksProtoBuilder& AddBlocks(
      const cloudfs::datanode::ReceivedDeletedBlockInfoProto& block);
  StorageReceivedDeletedBlocksProtoBuilder& SetStorage(
      const cloudfs::DatanodeStorageProto& storage);

  cloudfs::datanode::StorageReceivedDeletedBlocksProto Build();

 private:
  cloudfs::datanode::StorageReceivedDeletedBlocksProto proto_;
};

}  // namespace dancenn

#endif  // TEST_PROTO_GENERATED_CLOUDFS_DATANODE_PROTOCOL_H_
