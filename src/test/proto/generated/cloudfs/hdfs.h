// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/25
// Description

#ifndef TEST_PROTO_GENERATED_CLOUDFS_HDFS_H_
#define TEST_PROTO_GENERATED_CLOUDFS_HDFS_H_

#include <cstdint>  // For uint64_t.
#include <string>   // For string.

#include "proto/generated/cloudfs/hdfs.pb.h"  // For DatanodeStorageProto, etc.

namespace dancenn {

class DatanodeStorageProtoBuilder {
 public:
  DatanodeStorageProtoBuilder& SetStorageUuid(const std::string& storage_uuid);
  DatanodeStorageProtoBuilder& SetState(
      cloudfs::DatanodeStorageProto::StorageState state);
  DatanodeStorageProtoBuilder& SetStorageType(
      cloudfs::StorageTypeProto storage_type);

  cloudfs::DatanodeStorageProto Build();

 private:
  cloudfs::DatanodeStorageProto proto_;
};

class BlockProtoBuilder {
 public:
  BlockProtoBuilder& SetBlockId(uint64_t block_id);
  BlockProtoBuilder& SetGenStamp(uint64_t gen_stamp);
  BlockProtoBuilder& SetNumBytes(uint64_t num_bytes);

  cloudfs::BlockProto& Build();

 private:
  cloudfs::BlockProto proto_;
};

}  // namespace dancenn

#endif  // TEST_PROTO_GENERATED_CLOUDFS_HDFS_H_
