// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruan<PERSON><PERSON> <<EMAIL>>
// Created: 2023/03/26
// Description

#ifndef TEST_PROTO_GENERATED_DANCENN_EDIT_LOG_H_
#define TEST_PROTO_GENERATED_DANCENN_EDIT_LOG_H_

#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.
#include <proto/generated/dancenn/edit_log.pb.h>  // For BlockInfoProtos.

#include <string>  // For string.

namespace dancenn {

class BlockInfoProtosBuilder {
 public:
  BlockInfoProtosBuilder& AddContent(const BlockInfoProto& content);
  BlockInfoProtosBuilder& SetTrigger(const std::string& trigger);

  BlockInfoProtos Build();

 private:
  BlockInfoProtos proto_;
};

}  // namespace dancenn

#endif  // TEST_PROTO_GENERATED_DANCENN_EDIT_LOG_H_
