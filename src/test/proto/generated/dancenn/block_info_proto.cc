// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/24
// Description

#include "test/proto/generated/dancenn/block_info_proto.h"

namespace dancenn {

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetReportTs(
    uint32_t report_ts) {
  proto_.set_report_ts(report_ts);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetInvalidateTs(
    uint32_t invalidate_ts) {
  proto_.set_invalidate_ts(invalidate_ts);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetReporter(
    ReplicaInfoProto::Reporter reporter) {
  proto_.set_reporter(reporter);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::
    SetTriedAsPrimary4BlockRecovery(bool tried_as_primary_4_block_recovery) {
  proto_.set_tried_as_primary_4_block_recovery(
      tried_as_primary_4_block_recovery);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetIsBad(bool is_bad) {
  proto_.set_is_bad(is_bad);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetState(
    cloudfs::ReplicaStateProto state) {
  proto_.set_state(state);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetGenStamp(
    uint64_t gen_stamp) {
  proto_.set_gen_stamp(gen_stamp);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetNumBytes(
    uint64_t num_bytes) {
  proto_.set_num_bytes(num_bytes);
  return *this;
}

ReplicaInfoProtoBuilder& ReplicaInfoProtoBuilder::SetDnUuid(
    const std::string& dn_uuid) {
  proto_.set_dn_uuid(dn_uuid);
  return *this;
}

ReplicaInfoProto ReplicaInfoProtoBuilder::Build() {
  return proto_;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetVersion(uint64_t version) {
  proto_.set_version(version);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetState(
    BlockInfoProto::Status state) {
  proto_.set_state(state);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetBlockId(uint64_t block_id) {
  proto_.set_block_id(block_id);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetGenStamp(uint64_t gen_stamp) {
  proto_.set_gen_stamp(gen_stamp);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetNumBytes(uint64_t num_bytes) {
  proto_.set_num_bytes(num_bytes);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetINodeId(uint64_t inode_id) {
  proto_.set_inode_id(inode_id);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetExpectedRep(
    int64_t expected_rep) {
  proto_.set_expected_rep(expected_rep);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetWriteMode(
    cloudfs::IoMode write_mode) {
  proto_.set_write_mode(write_mode);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetRecoveryGenStamp(
    uint64_t recovery_gen_stamp) {
  proto_.set_recovery_gen_stamp(recovery_gen_stamp);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetPrimaryDnUuid4BlockRecovery(
    const std::string& primary_dn_uuid_4_block_recovery) {
  proto_.set_primary_dn_uuid_4_block_recovery(primary_dn_uuid_4_block_recovery);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetPufsName(
    const std::string& pufs_name) {
  proto_.set_pufs_name(pufs_name);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetUploadIssuedTimes(
    uint32_t upload_issued_times) {
  proto_.set_upload_issued_times(upload_issued_times);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::AddAbortedUploadIds(
    const std::string& aborted_upload_id) {
  proto_.add_aborted_upload_ids(aborted_upload_id);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetCurrUploadId(
    const std::string& curr_upload_id) {
  proto_.set_curr_upload_id(curr_upload_id);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetDnUuid(
    const std::string& dn_uuid) {
  proto_.set_dn_uuid(dn_uuid);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetNnExpTs(uint64_t nn_exp_ts) {
  proto_.set_nn_exp_ts(nn_exp_ts);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::SetDnExpTs(uint64_t dn_exp_ts) {
  proto_.set_dn_exp_ts(dn_exp_ts);
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::AddReplicas(
    const ReplicaInfoProto& replica) {
  *proto_.add_replicas() = replica;
  return *this;
}

BlockInfoProtoBuilder& BlockInfoProtoBuilder::AddDeletedDnUuids(
    const std::string& deleted_dn_uuid) {
  *proto_.add_deleted_dn_uuids() = deleted_dn_uuid;
  return *this;
}

BlockInfoProto BlockInfoProtoBuilder::Build() {
  return proto_;
}

}  // namespace dancenn
