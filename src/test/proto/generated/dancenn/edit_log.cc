// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/03/26
// Description

#include "test/proto/generated/dancenn/edit_log.h"

namespace dancenn {

BlockInfoProtosBuilder& BlockInfoProtosBuilder::AddContent(
    const BlockInfoProto& content) {
  *proto_.add_content() = content;
  return *this;
}

BlockInfoProtosBuilder& BlockInfoProtosBuilder::SetTrigger(
    const std::string& trigger) {
  proto_.set_trigger(trigger);
  return *this;
}

BlockInfoProtos BlockInfoProtosBuilder::Build() {
  return proto_;
}

}  // namespace dancenn