// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/24
// Description

#ifndef TEST_PROTO_GENERATED_DANCENN_BLOCK_INFO_PROTO_H_
#define TEST_PROTO_GENERATED_DANCENN_BLOCK_INFO_PROTO_H_

#include <cstdint>  // For uint64_t.
#include <string>   // For string.

#include "proto/generated/cloudfs/hdfs.pb.h"  // For ReplicaStateProto.
#include "proto/generated/cloudfs/hdfs.pb.h"  // For IoMode.
#include "proto/generated/dancenn/block_info_proto.pb.h"  // For BlockInfoProto, ReplicaInfoProto.

namespace dancenn {

class ReplicaInfoProtoBuilder {
 public:
  ReplicaInfoProtoBuilder& SetReportTs(uint32_t report_ts);
  ReplicaInfoProtoBuilder& SetInvalidateTs(uint32_t invalidate_ts);
  ReplicaInfoProtoBuilder& SetReporter(ReplicaInfoProto::Reporter reporter);
  ReplicaInfoProtoBuilder& SetTriedAsPrimary4BlockRecovery(
      bool tried_as_primary_4_block_recovery);
  ReplicaInfoProtoBuilder& SetIsBad(bool is_bad);
  ReplicaInfoProtoBuilder& SetState(cloudfs::ReplicaStateProto state);
  ReplicaInfoProtoBuilder& SetGenStamp(uint64_t gen_stamp);
  ReplicaInfoProtoBuilder& SetNumBytes(uint64_t num_bytes);
  ReplicaInfoProtoBuilder& SetDnUuid(const std::string& dn_uuid);

  ReplicaInfoProto Build();

 private:
  ReplicaInfoProto proto_;
};

class BlockInfoProtoBuilder {
 public:
  BlockInfoProtoBuilder& SetVersion(uint64_t version);
  BlockInfoProtoBuilder& SetState(BlockInfoProto::Status state);
  BlockInfoProtoBuilder& SetBlockId(uint64_t block_id);
  BlockInfoProtoBuilder& SetGenStamp(uint64_t gen_stamp);
  BlockInfoProtoBuilder& SetNumBytes(uint64_t num_bytes);
  BlockInfoProtoBuilder& SetINodeId(uint64_t inode_id);
  BlockInfoProtoBuilder& SetExpectedRep(int64_t expected_rep);
  BlockInfoProtoBuilder& SetWriteMode(cloudfs::IoMode write_mode);

  BlockInfoProtoBuilder& SetRecoveryGenStamp(uint64_t recovery_gen_stamp);
  BlockInfoProtoBuilder& SetPrimaryDnUuid4BlockRecovery(
      const std::string& primary_dn_uuid_4_block_recovery);

  BlockInfoProtoBuilder& SetPufsName(const std::string& pufs_name);
  BlockInfoProtoBuilder& SetUploadIssuedTimes(uint32_t upload_issued_times);
  BlockInfoProtoBuilder& AddAbortedUploadIds(
      const std::string& aborted_upload_id);
  BlockInfoProtoBuilder& SetCurrUploadId(const std::string& curr_upload_id);
  BlockInfoProtoBuilder& SetDnUuid(const std::string& dn_uuid);
  BlockInfoProtoBuilder& SetNnExpTs(uint64_t nn_exp_ts);
  BlockInfoProtoBuilder& SetDnExpTs(uint64_t dn_exp_ts);

  BlockInfoProtoBuilder& AddReplicas(const ReplicaInfoProto& replica);
  BlockInfoProtoBuilder& AddDeletedDnUuids(const std::string& deleted_dn_uuid);

  BlockInfoProto Build();

 private:
  BlockInfoProto proto_;
};

}  // namespace dancenn

#endif  // TEST_PROTO_GENERATED_DANCENN_BLOCK_INFO_PROTO_H_
