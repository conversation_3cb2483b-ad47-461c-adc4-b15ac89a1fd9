// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/07
// Description

#ifndef TEST_MATCHER_H_
#define TEST_MATCHER_H_

#include <gmock/gmock.h>

#include "base/to_json_string.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

MATCHER_P(BlockInfoProtoEq, matcher, "") {
  if (!matcher.IsInitialized() && !arg.IsInitialized()) {
    return true;
  }
  if (matcher.SerializeAsString() == arg.SerializeAsString()) {
    return true;
  }
  *result_listener << ToJsonCompactString(matcher)
                   << " != " << ToJsonCompactString(arg);
  return false;
}

MATCHER_P(RocksDBSliceEq, matcher, "") {
  if (matcher == arg.ToString()) {
    return true;
  }
  *result_listener << matcher << " != " << arg.ToString();
  return false;
}

}  // namespace dancenn

#endif  // TEST_MATCHER_H_
