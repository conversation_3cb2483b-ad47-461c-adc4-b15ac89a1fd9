// Copyright 2019 Xiong Mu <<EMAIL>>

#include "datanode_manager/data_centers_topology.h"

#include <gtest/gtest.h>
#include <chrono>
#include <iostream>
#include <thread>
#include <tuple>
#include <vector>

namespace dancenn {

namespace {

class DataCentersTopologyTest : public testing::Test {
 public:
  void SetUp() override {
    dc_topology_ = std::make_unique<DataCentersTopology>();
  }

  void TearDown() override {}

 protected:
  /**
   * see https://bytedance.feishu.cn/space/file/boxcnxFlF9LSVK8bUqC75vs9Pnb
   */
  bool UsingDefaultConfig() {
    std::string config_string =
        "[\n"
        "   [\"HL\", \"LQ\"],\n"
        "   [\"HL\", \"LF\"],\n"
        "   [\"LQ\", \"LF\"],\n"
        "   [\"WJ\", \"HL\"],\n"
        "   [\"WJ\", \"BJSY\"],\n"
        "   [\"HL\", \"HLSY\"],\n"
        "   [\"LF\", \"alinc2\"]\n"
        "]";
    return dc_topology_->ParseJsonAndUpdateConfig(config_string);
  }

  std::unique_ptr<DataCentersTopology> dc_topology_;
};

TEST_F(DataCentersTopologyTest, TraverseAll) {
  ASSERT_TRUE(UsingDefaultConfig());
  // 0
  EXPECT_EQ(0, dc_topology_->GetDistance("LF", "LF"));
  EXPECT_EQ(0, dc_topology_->GetDistance("HL", "HL"));
  EXPECT_EQ(0, dc_topology_->GetDistance("LQ", "LQ"));
  EXPECT_EQ(0, dc_topology_->GetDistance("alinc2", "alinc2"));
  EXPECT_EQ(0, dc_topology_->GetDistance("WJ", "WJ"));
  EXPECT_EQ(0, dc_topology_->GetDistance("BJSY", "BJSY"));
  EXPECT_EQ(0, dc_topology_->GetDistance("WJ", "WJ"));
  EXPECT_EQ(0, dc_topology_->GetDistance("HLSY", "HLSY"));
  EXPECT_EQ(0, dc_topology_->GetDistance("BJ", "BJ"));
  EXPECT_EQ(0, dc_topology_->GetDistance("SH", "SH"));
  // 1
  EXPECT_EQ(1, dc_topology_->GetDistance("LF", "HL"));
  EXPECT_EQ(1, dc_topology_->GetDistance("LF", "LQ"));
  EXPECT_EQ(1, dc_topology_->GetDistance("LF", "alinc2"));
  EXPECT_EQ(1, dc_topology_->GetDistance("LQ", "LF"));
  EXPECT_EQ(1, dc_topology_->GetDistance("LQ", "HL"));
  EXPECT_EQ(1, dc_topology_->GetDistance("HL", "HLSY"));
  EXPECT_EQ(1, dc_topology_->GetDistance("HL", "LQ"));
  EXPECT_EQ(1, dc_topology_->GetDistance("HL", "LF"));
  EXPECT_EQ(1, dc_topology_->GetDistance("HL", "WJ"));
  EXPECT_EQ(1, dc_topology_->GetDistance("HLSY", "HL"));
  EXPECT_EQ(1, dc_topology_->GetDistance("alinc2", "LF"));
  EXPECT_EQ(1, dc_topology_->GetDistance("WJ", "HL"));
  EXPECT_EQ(1, dc_topology_->GetDistance("WJ", "BJSY"));
  EXPECT_EQ(1, dc_topology_->GetDistance("BJSY", "WJ"));
  // 2
  EXPECT_EQ(2, dc_topology_->GetDistance("alinc2", "HL"));
  EXPECT_EQ(2, dc_topology_->GetDistance("alinc2", "LQ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("HLSY", "WJ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("HLSY", "LQ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("HLSY", "LF"));
  EXPECT_EQ(2, dc_topology_->GetDistance("WJ", "HLSY"));
  EXPECT_EQ(2, dc_topology_->GetDistance("WJ", "LQ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("WJ", "LF"));
  EXPECT_EQ(2, dc_topology_->GetDistance("BJSY", "HL"));
  EXPECT_EQ(2, dc_topology_->GetDistance("HL", "alinc2"));
  EXPECT_EQ(2, dc_topology_->GetDistance("HL", "BJSY"));
  EXPECT_EQ(2, dc_topology_->GetDistance("LF", "WJ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("LF", "HLSY"));
  EXPECT_EQ(2, dc_topology_->GetDistance("LQ", "WJ"));
  EXPECT_EQ(2, dc_topology_->GetDistance("LQ", "HLSY"));
  // 3
  EXPECT_EQ(3, dc_topology_->GetDistance("HLSY", "alinc2"));
  EXPECT_EQ(3, dc_topology_->GetDistance("HLSY", "BJSY"));
  EXPECT_EQ(3, dc_topology_->GetDistance("BJSY", "HLSY"));
  EXPECT_EQ(3, dc_topology_->GetDistance("BJSY", "LF"));
  EXPECT_EQ(3, dc_topology_->GetDistance("BJSY", "LQ"));
  EXPECT_EQ(3, dc_topology_->GetDistance("LF", "BJSY"));
  EXPECT_EQ(3, dc_topology_->GetDistance("LQ", "BJSY"));
  EXPECT_EQ(3, dc_topology_->GetDistance("WJ", "alinc2"));
  EXPECT_EQ(3, dc_topology_->GetDistance("alinc2", "HLSY"));
  EXPECT_EQ(3, dc_topology_->GetDistance("alinc2", "WJ"));
  // 4
  EXPECT_EQ(4, dc_topology_->GetDistance("BJSY", "alinc2"));
  EXPECT_EQ(4, dc_topology_->GetDistance("alinc2", "BJSY"));
  // unknown
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("LF", "BJ"));
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("BJ", "LF"));
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("HL", "SH"));
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("SH", "HL"));
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("BJ", "SH"));
  EXPECT_EQ(UINT_MAX, dc_topology_->GetDistance("SH", "BJ"));
}

}  // namespace

}  // namespace dancenn
