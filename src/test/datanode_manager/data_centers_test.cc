// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "datanode_manager/data_centers.h"

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "base/defer.h"

DECLARE_string(all_datacenters);

namespace dancenn {

TEST(DataCenter, Test01) {
  {
    DataCenter dc {"", -1};
    ASSERT_EQ(dc, kUnknownDataCenter);
    ASSERT_NE(dc, kDefaultDataCenter);
  }
  {
    DataCenter dc {"", 0};
    ASSERT_EQ(dc, kDefaultDataCenter);
    ASSERT_NE(dc, kUnknownDataCenter);
  }
}

TEST(DataCenters, Test01) {
  auto backup = FLAGS_all_datacenters;
  DEFER([&] () {
    FLAGS_all_datacenters = backup;
  });

  {
    FLAGS_all_datacenters = "";
    DataCenters dcs;
    ASSERT_EQ(dcs.size(), 1);
    {
      auto dc = dcs.GetDataCenterById(0);
      ASSERT_EQ(dc, kDefaultDataCenter);
      dc = dcs.GetDataCenterById(1);
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
    {
      auto dc = dcs.GetDataCenterByName(kDefaultDataCenter.name);
      ASSERT_EQ(dc, kDefaultDataCenter);
      dc = dcs.GetDataCenterByName("test_unknown_data_center");
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
  }

  {
    FLAGS_all_datacenters = "lf";
    DataCenters dcs;
    ASSERT_EQ(dcs.size(), 1);
    {
      auto dc = dcs.GetDataCenterById(0);
      ASSERT_EQ(dc.name, "lf");
      ASSERT_EQ(dc.id, 0);
      dc = dcs.GetDataCenterById(1);
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
    {
      auto dc = dcs.GetDataCenterByName("lf");
      ASSERT_EQ(dc.name, "lf");
      ASSERT_EQ(dc.id, 0);
      dc = dcs.GetDataCenterByName("test_unknown_data_center");
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
  }

  {
    FLAGS_all_datacenters = "lf,hy";
    DataCenters dcs;
    ASSERT_EQ(dcs.size(), 2);
    {
      auto dc = dcs.GetDataCenterById(0);
      ASSERT_EQ(dc.name, "lf");
      ASSERT_EQ(dc.id, 0);
      dc = dcs.GetDataCenterById(1);
      ASSERT_EQ(dc.name, "hy");
      ASSERT_EQ(dc.id, 1);
      dc = dcs.GetDataCenterById(2);
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
    {
      auto dc = dcs.GetDataCenterByName("lf");
      ASSERT_EQ(dc.name, "lf");
      ASSERT_EQ(dc.id, 0);
      dc = dcs.GetDataCenterByName("hy");
      ASSERT_EQ(dc.name, "hy");
      ASSERT_EQ(dc.id, 1);
      dc = dcs.GetDataCenterByName("test_unknown_data_center");
      ASSERT_EQ(dc, kUnknownDataCenter);
    }
  }
}

}  // namespace dancenn

