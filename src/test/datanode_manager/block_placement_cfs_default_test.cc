// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/17
// Description

#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <unordered_set>
#include <vector>

#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_pufs_info.h"
#include "cnetpp/base/ip_address.h"
#include "datanode_manager/advice.h"
#include "datanode_manager/block_placement_cfs_default.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/datanode_manager.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"

DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(block_read_cache_refresh_interval_s);
DECLARE_int32(block_read_cache_not_expired_interval_s);
DECLARE_string(block_placement_distribution_type);
DECLARE_uint64(dn_writable_unit_capacity_bytes_unit);
DECLARE_bool(dn_writable_use_total_capacity_factor);
DECLARE_int32(datanode_dying_interval_ms);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_bool(placement_ignore_existed_host);
DECLARE_bool(placement_ignore_existed_switch);
DECLARE_bool(choose_target_shuffle_candidate);
DECLARE_int32(ut_loglevel_v);

namespace dancenn {

constexpr std::size_t kBlockReadCacheCapacity = 3;

class BlockReadCacheTest : public testing::Test {
 protected:
  DatanodeInfoPtr NewDn(DatanodeID dn_id, uint64_t temp_based_remaining = 0) {
    DatanodeInfoPtr dn = new DatanodeInfo(
        dn_id, cloudfs::DatanodeIDProto(), cnetpp::base::IPAddress());
    dn->stat().temp_based_remaining = temp_based_remaining;
    dn_cleaner_.emplace_back(dn);
    return dn;
  }

 protected:
  std::vector<std::unique_ptr<DatanodeInfo>> dn_cleaner_;
};

TEST_F(BlockReadCacheTest, Init) {
  BlockReadCache* cache = new BlockReadCache();
  EXPECT_EQ(cache->blk_id, kInvalidBlockID);
  EXPECT_EQ(cache->refresh_ts, 0);
  EXPECT_EQ(cache->size, 0);
  EXPECT_EQ(cache->dns[0], nullptr);
  EXPECT_EQ(cache->dns[kBlockReadCacheCapacity - 1], nullptr);
  EXPECT_EQ(cache->next, nullptr);
  delete cache;
}

TEST_F(BlockReadCacheTest, Push) {
  BlockReadCache cache;
  for (std::size_t i = 0; i < kBlockReadCacheCapacity; i++) {
    EXPECT_TRUE(cache.Push(NewDn(i)));
  }
  EXPECT_EQ(cache.size, kBlockReadCacheCapacity);
  EXPECT_NE(cache.dns[0], nullptr);
  EXPECT_NE(cache.dns[kBlockReadCacheCapacity - 1], nullptr);
}

TEST_F(BlockReadCacheTest, PushOverflow) {
  BlockReadCache cache;
  for (std::size_t i = 0; i < kBlockReadCacheCapacity; i++) {
    EXPECT_TRUE(cache.Push(NewDn(i)));
  }
  EXPECT_FALSE(cache.Push(NewDn(kBlockReadCacheCapacity)));
}

TEST_F(BlockReadCacheTest, PushExistedDn) {
  BlockReadCache cache;
  EXPECT_TRUE(cache.Push(NewDn(0)));
  EXPECT_FALSE(cache.Push(NewDn(0)));
}

TEST_F(BlockReadCacheTest, Clear) {
  BlockReadCache cache;
  EXPECT_TRUE(cache.Push(NewDn(0)));
  cache.Clear();
  EXPECT_EQ(cache.size, 0);
  EXPECT_EQ(cache.dns[0], nullptr);
  EXPECT_EQ(cache.dns[kBlockReadCacheCapacity - 1], nullptr);
}

TEST_F(BlockReadCacheTest, Sort) {
  BlockReadCache cache;
  EXPECT_TRUE(cache.Push(NewDn(0, 3)));
  EXPECT_TRUE(cache.Push(NewDn(1, 1)));
  EXPECT_TRUE(cache.Push(NewDn(2, 2)));
  cache.Sort(std::vector<DatanodeID>{1, 2});
  EXPECT_EQ(cache.dns[0]->id(), 1);
  EXPECT_EQ(cache.dns[1]->id(), 2);
  EXPECT_EQ(cache.dns[2]->id(), 0);

  cache.Clear();
  EXPECT_TRUE(cache.Push(NewDn(0, 0)));
  EXPECT_TRUE(cache.Push(NewDn(1, 1)));
  cache.Sort(std::vector<DatanodeID>{});
  EXPECT_EQ(cache.dns[0]->id(), 1);
  EXPECT_EQ(cache.dns[1]->id(), 0);
}

class BlockReadCacheSliceTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_blockmap_num_bucket_each_slice = 3;
    FLAGS_choose_target_shuffle_candidate = false;
  }
};

TEST_F(BlockReadCacheSliceTest, GetOrCreateBlock) {
  BlockReadCacheSlice slice;
  std::vector<BlockReadCache*> caches;
  for (std::size_t i = 0; i < 100; i++) {
    BlockReadCache* cache = slice.GetOrCreateBlock(i);
    EXPECT_TRUE(cache);
    caches.push_back(cache);
  }
  for (std::size_t i = 0; i < 100; i++) {
    BlockReadCache* cache = slice.GetOrCreateBlock(i);
    EXPECT_EQ(cache, caches[i]);
  }
}

TEST_F(BlockReadCacheSliceTest, RemoveExpiredBlocks) {
  // We use N for "not expect to remove", E for "expect to remove".
  BlockID k = FLAGS_blockmap_num_bucket_each_slice;
  {
    BlockReadCacheSlice slice;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{});
  }
  {
    // N
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 1;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{0});
  }
  {
    // E
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 0;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{});
  }
  {
    // NE
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 1;
    slice.GetOrCreateBlock(k)->refresh_ts = 0;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{0});
  }
  {
    // EN
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 0;
    slice.GetOrCreateBlock(k)->refresh_ts = 1;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{k});
  }
  {
    // NEN
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 1;
    slice.GetOrCreateBlock(k)->refresh_ts = 0;
    slice.GetOrCreateBlock(2 * k)->refresh_ts = 1;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), (std::vector<BlockID>{2 * k, 0}));
  }
  {
    // ENE
    BlockReadCacheSlice slice;
    slice.GetOrCreateBlock(0)->refresh_ts = 0;
    slice.GetOrCreateBlock(k)->refresh_ts = 1;
    slice.GetOrCreateBlock(2 * k)->refresh_ts = 0;
    slice.RemoveExpiredBlocks(FLAGS_block_read_cache_not_expired_interval_s);
    EXPECT_EQ(slice.GetDebugInfo(0), std::vector<BlockID>{k});
  }
}

class GMockBlockPlacementCfsDefault : public BlockPlacementCfsDefault {
 public:
  GMockBlockPlacementCfsDefault(DatanodeManagerMetrics* m)
    : BlockPlacementCfsDefault(m)
  {}
    
  MOCK_METHOD0(GetCurrentTsInMs, int64_t());
  MOCK_METHOD1(GetIndex, std::size_t(std::size_t size));
};

class BlockPlacementCfsDefaultTest : public testing::Test {
 public:
  BlockPlacementCfsDefaultTest()
  : metrics_(new DatanodeManagerMetrics(nullptr)),
    placement_(metrics_.get()) {
  }
  void SetUp() override {
    for (std::size_t i = 0; i < 5; i++) {
      placement_.AddDatanode(NewDn(i));
    }

    FLAGS_blockmap_num_bucket_each_slice = 3;
    FLAGS_choose_target_shuffle_candidate = false;
  }

 protected:
  DatanodeInfoPtr NewDn(DatanodeID dn_id, uint64_t temp_based_remaining = 0) {
    DatanodeInfoPtr dn = new DatanodeInfo(
        dn_id, cloudfs::DatanodeIDProto(), cnetpp::base::IPAddress());
    dn->stat().temp_based_remaining = temp_based_remaining;
    dn->add_storage_type(StorageTypeProto::DISK);
    dns_.emplace_back(dn);
    return dn;
  }

 protected:
  std::unique_ptr<DatanodeManagerMetrics> metrics_;
  std::vector<std::unique_ptr<DatanodeInfo>> dns_;
  GMockBlockPlacementCfsDefault placement_;
  std::vector<DatanodeInfoPtr> favored_nodes_;
  std::unordered_set<DatanodeInfoPtr> excluded_;
};

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget) {
  dns_[0]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 2;
  EXPECT_CALL(placement_, GetCurrentTsInMs())
      .Times(1)
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  placement_.RefreshConfig();
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(2)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1));
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4New("",
                              2,
                              128,
                              PlacementAdvice(),
                              NetworkLocationInfo(),
                              favored_nodes_,
                              &excluded_,
                              &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[4].get(), dns_[0].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTargetWithRack) {
  dns_[0]->stat().temp_based_remaining = 3;
  dns_[1]->stat().temp_based_remaining = 2;
  dns_[2]->stat().temp_based_remaining = 1;
  cloudfs::LocationTag tag;
  tag.set_host("m1");
  dns_[2]->set_location_tag(tag);
  tag.set_host("m2");
  dns_[0]->set_location_tag(tag);
  dns_[1]->set_location_tag(tag);
  dns_[3]->set_location_tag(tag);
  dns_[4]->set_location_tag(tag);
  EXPECT_CALL(placement_, GetCurrentTsInMs())
      .Times(1)
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  placement_.RefreshConfig();
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(0));
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4New("",
                              3,
                              128,
                              PlacementAdvice(),
                              NetworkLocationInfo(),
                              favored_nodes_,
                              &excluded_,
                              &result);
  EXPECT_EQ(result,
            std::vector<DatanodeInfoPtr>({dns_[0].get(), dns_[2].get(), dns_[1].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, DontChooseDnsInSameVM) {
  dns_[0]->stat().temp_based_remaining = 2;
  dns_[1]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 3;
  cloudfs::DatanodeIDProto address;
  address.set_ipaddr("127.0.0.1");
  address.set_hostname("localhost");
  address.set_datanodeuuid("n1");
  address.set_xferport(5070);
  address.set_infoport(5080);
  address.set_ipcport(5090);
  dns_[0]->set_address(address, cnetpp::base::IPAddress());
  dns_[4]->set_address(address, cnetpp::base::IPAddress());
  cloudfs::LocationTag tag;
  tag.set_host("n1");
  dns_[0]->set_location_tag(tag);
  dns_[4]->set_location_tag(tag);
  EXPECT_CALL(placement_, GetCurrentTsInMs)
      .Times(1)
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  placement_.RefreshConfig();
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(2)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1));
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4New("",
                              2,
                              128,
                              PlacementAdvice(),
                              NetworkLocationInfo(),
                              favored_nodes_,
                              &excluded_,
                              &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[4].get(), dns_[1].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseDnsInSameHostIfNotChoice) {
  dns_[0]->stat().temp_based_remaining = 2;
  dns_[1]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 3;
  cloudfs::LocationTag tag;
  tag.set_host("n1");
  dns_[0]->set_location_tag(tag);
  dns_[1]->set_location_tag(tag);
  dns_[4]->set_location_tag(tag);
  cloudfs::LocationTag tag2;
  tag2.set_host("n2");
  dns_[2]->set_location_tag(tag2);
  dns_[3]->set_location_tag(tag2);
  EXPECT_CALL(placement_, GetCurrentTsInMs)
      .Times(1)
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  placement_.RefreshConfig();

  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(2)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1));
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4New("",
                              2,
                              128,
                              PlacementAdvice(),
                              NetworkLocationInfo(),
                              favored_nodes_,
                              &excluded_,
                              &result);
  EXPECT_EQ(result.size(), 2);
  VLOG(10) << "result[0]=" << result[0]->address().ShortDebugString();
  VLOG(10) << "result[1]=" << result[1]->address().ShortDebugString();
  EXPECT_NE(result[0]->location_tag().host(), result[1]->location_tag().host());
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4ReadWithoutCache) {
  EXPECT_CALL(placement_, GetCurrentTsInMs())
      .Times(2)
      .WillRepeatedly(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(3)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1))
      .WillOnce(testing::Return(2));
  dns_[0]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 2;
  dns_[3]->stat().temp_based_remaining = 3;
  placement_.RefreshConfig();
  DetailedBlock detailed_block;
  detailed_block.blk_.id = 1;
  detailed_block.uc_ = BlockUCState::kPersisted;
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[3].get(), dns_[4].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4ReadWithCache) {
  EXPECT_CALL(placement_, GetCurrentTsInMs())
      .Times(4)
      .WillRepeatedly(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000));
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(3)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1))
      .WillOnce(testing::Return(2));
  dns_[0]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 2;
  dns_[3]->stat().temp_based_remaining = 3;
  placement_.RefreshConfig();
  DetailedBlock detailed_block;
  detailed_block.blk_.id = 1;
  detailed_block.uc_ = BlockUCState::kPersisted;
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);

  dns_[4]->stat().temp_based_remaining = 3;
  dns_[3]->stat().temp_based_remaining = 2;
  result.clear();
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[4].get(), dns_[3].get()}));

  // Sort will base on with cache or not.
  detailed_block.storage_.push_back(0);
  result.clear();
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[0].get(), dns_[4].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4ReadCacheTimeout) {
  EXPECT_CALL(placement_, GetCurrentTsInMs())
      .Times(3)
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000))
      .WillOnce(
          testing::Return(FLAGS_block_read_cache_refresh_interval_s * 1000))
      .WillOnce(testing::Return(2 * FLAGS_block_read_cache_refresh_interval_s *
                                1000));
  EXPECT_CALL(placement_, GetIndex(dns_.size()))
      .Times(6)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(1))
      .WillOnce(testing::Return(2))
      // Next selection.
      .WillOnce(testing::Return(2))
      .WillOnce(testing::Return(1))
      .WillOnce(testing::Return(0));
  dns_[0]->stat().temp_based_remaining = 1;
  dns_[4]->stat().temp_based_remaining = 2;
  dns_[3]->stat().temp_based_remaining = 3;
  placement_.RefreshConfig();
  DetailedBlock detailed_block;
  detailed_block.blk_.id = 1;
  detailed_block.uc_ = BlockUCState::kPersisted;
  std::vector<DatanodeInfoPtr> result;
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[3].get(), dns_[4].get()}));

  result.clear();
  placement_.ChooseTarget4Read(detailed_block,
                               2,
                               ReadAdvice(),
                               NetworkLocationInfo(),
                               favored_nodes_,
                               &excluded_,
                               &result);
  EXPECT_EQ(result,
            (std::vector<DatanodeInfoPtr>{dns_[3].get(), dns_[4].get()}));
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4NewWithWeight1Rep) {
  FLAGS_block_placement_distribution_type = "weight";
  FLAGS_dn_writable_use_total_capacity_factor = true;

  BlockPlacementCfsDefault local_placement(metrics_.get());

  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }

  auto unit = FLAGS_dn_writable_unit_capacity_bytes_unit;
  dns_[0]->stat().capacity = static_cast<uint64_t>(double(unit) * 1.5);
  dns_[1]->stat().capacity = static_cast<uint64_t>(double(unit) * 2);
  dns_[2]->stat().capacity = static_cast<uint64_t>(double(unit) * 4.3);
  dns_[3]->stat().capacity = static_cast<uint64_t>(double(unit) * 7);
  dns_[4]->stat().capacity = static_cast<uint64_t>(double(unit) * 10);
  for (std::size_t i = 0; i < 5; i++) {
    LOG(INFO) << "dn=" << i << " weight=" << dns_[i]->GetWriteableWeight();
  }
  local_placement.RefreshConfig();

  std::vector<int> cnt(5, 0);

  auto start = TimeUtil::GetNowEpochMs();
  while (true) {
    for (int i = 0; i < 10000; ++i) {
      std::vector<DatanodeInfoPtr> result;
      local_placement.ChooseTarget4New("",
                                       1,
                                       128,
                                       PlacementAdvice(),
                                       NetworkLocationInfo(),
                                       favored_nodes_,
                                       &excluded_,
                                       &result);
      EXPECT_EQ(result.size(), 1);
      for (int i = 0; i < 1; ++i) {
        cnt[result[i]->id()]++;
        VLOG(15) << "i=" << i << " dn=" << result[i]->id();
      }
      excluded_.clear();
      result.clear();
    }
    auto now = TimeUtil::GetNowEpochMs();
    if (now - start > 10 * 1000) {
      break;
    }
  }

  for (int i = 0; i < 5; ++i) {
    LOG(INFO) << "i=" << i << " weight=" << dns_[i]->GetWriteableWeight()
              << " cnt=" << cnt[i]
              << " ratio=" << (cnt[i] / dns_[i]->GetWriteableWeight());
  }

  EXPECT_GT(cnt[0], 0);
  EXPECT_LT(abs(double(cnt[1]) / cnt[0] - 2.00), 0.1);
  EXPECT_LT(abs(double(cnt[2]) / cnt[0] - 4.00), 0.1);
  EXPECT_LT(abs(double(cnt[3]) / cnt[0] - 7.00), 0.5);
  EXPECT_LT(abs(double(cnt[4]) / cnt[0] - 10.00), 0.5);
}

TEST_F(BlockPlacementCfsDefaultTest, DISABLED_ChooseTarget4NewWithWeight2Rep) {
  FLAGS_block_placement_distribution_type = "weight";
  FLAGS_dn_writable_use_total_capacity_factor = true;

  BlockPlacementCfsDefault local_placement(metrics_.get());

  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }

  auto unit = FLAGS_dn_writable_unit_capacity_bytes_unit;
  dns_[0]->stat().capacity = static_cast<uint64_t>(double(unit) * 1.5);
  dns_[1]->stat().capacity = static_cast<uint64_t>(double(unit) * 2);
  dns_[2]->stat().capacity = static_cast<uint64_t>(double(unit) * 4.3);
  dns_[3]->stat().capacity = static_cast<uint64_t>(double(unit) * 7);
  dns_[4]->stat().capacity = static_cast<uint64_t>(double(unit) * 10);
  for (std::size_t i = 0; i < 5; i++) {
    LOG(INFO) << "dn=" << i << " weight=" << dns_[i]->GetWriteableWeight();
  }
  local_placement.RefreshConfig();

  std::vector<int> cnt(5, 0);
  auto start = TimeUtil::GetNowEpochMs();
  while (true) {
    for (int i = 0; i < 10000; ++i) {
      std::vector<DatanodeInfoPtr> result;
      local_placement.ChooseTarget4New("",
                                       2,
                                       128,
                                       PlacementAdvice(),
                                       NetworkLocationInfo(),
                                       favored_nodes_,
                                       &excluded_,
                                       &result);
      EXPECT_EQ(result.size(), 2);
      for (int i = 0; i < 2; ++i) {
        cnt[result[i]->id()]++;
        VLOG(15) << "i=" << i << " dn=" << result[i]->id();
      }
      excluded_.clear();
      result.clear();
    }

    auto now = TimeUtil::GetNowEpochMs();
    if (now - start > 10 * 1000) {
      break;
    }
  }

  for (int i = 0; i < 5; ++i) {
    LOG(INFO) << "i=" << i << " weight=" << dns_[i]->GetWriteableWeight()
              << " cnt=" << cnt[i]
              << " ratio=" << (cnt[i] / dns_[i]->GetWriteableWeight());
  }

  EXPECT_GT(cnt[0], 0);
  EXPECT_LT(abs(double(cnt[1]) / cnt[0] - 2.00), 0.1);
  EXPECT_LT(abs(double(cnt[2]) / cnt[0] - 4.00), 0.1);
  EXPECT_LT(abs(double(cnt[3]) / cnt[0] - 7.00), 0.5);
  EXPECT_LT(abs(double(cnt[4]) / cnt[0] - 10.00), 0.5);
}

TEST_F(BlockPlacementCfsDefaultTest, DISABLED_ChooseTarget4NewWithWeight3Rep) {
  FLAGS_block_placement_distribution_type = "weight";
  FLAGS_dn_writable_use_total_capacity_factor = true;

  BlockPlacementCfsDefault local_placement(metrics_.get());

  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }

  auto unit = FLAGS_dn_writable_unit_capacity_bytes_unit;
  dns_[0]->stat().capacity = static_cast<uint64_t>(double(unit) * 1.5);
  dns_[1]->stat().capacity = static_cast<uint64_t>(double(unit) * 2);
  dns_[2]->stat().capacity = static_cast<uint64_t>(double(unit) * 4.3);
  dns_[3]->stat().capacity = static_cast<uint64_t>(double(unit) * 7);
  dns_[4]->stat().capacity = static_cast<uint64_t>(double(unit) * 10);
  for (std::size_t i = 0; i < 5; i++) {
    LOG(INFO) << "dn=" << i << " weight=" << dns_[i]->GetWriteableWeight();
  }
  local_placement.RefreshConfig();

  std::vector<int> cnt(5, 0);
  auto start = TimeUtil::GetNowEpochMs();
  while (true) {
    for (int i = 0; i < 10000; ++i) {
      std::vector<DatanodeInfoPtr> result;
      local_placement.ChooseTarget4New("",
                                       3,
                                       128,
                                       PlacementAdvice(),
                                       NetworkLocationInfo(),
                                       favored_nodes_,
                                       &excluded_,
                                       &result);
      EXPECT_EQ(result.size(), 3);
      for (int i = 0; i < 3; ++i) {
        cnt[result[i]->id()]++;
        VLOG(15) << "i=" << i << " dn=" << result[i]->id();
      }
      excluded_.clear();
      result.clear();
    }

    auto now = TimeUtil::GetNowEpochMs();
    if (now - start > 10 * 1000) {
      break;
    }
  }

  for (int i = 0; i < 5; ++i) {
    LOG(INFO) << "i=" << i << " weight=" << dns_[i]->GetWriteableWeight()
              << " cnt=" << cnt[i]
              << " ratio=" << (cnt[i] / dns_[i]->GetWriteableWeight());
  }

  EXPECT_GT(cnt[0], 0);
  EXPECT_LT(abs(double(cnt[1]) / cnt[0] - 2.00), 0.1);
  EXPECT_LT(abs(double(cnt[2]) / cnt[0] - 4.00), 0.1);
  EXPECT_LT(abs(double(cnt[3]) / cnt[0] - 7.00), 0.5);
  EXPECT_LT(abs(double(cnt[4]) / cnt[0] - 10.00), 0.5);
}

TEST_F(BlockPlacementCfsDefaultTest, DISABLED_ChooseTarget4NewWithWeightOpen) {
  FLAGS_block_placement_distribution_type = "weight";
  FLAGS_dn_writable_use_total_capacity_factor = true;

  BlockPlacementCfsDefault local_placement(metrics_.get());

  int total_dn = 8;
  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }
  for (int i = 5; i < total_dn; ++i) {
    local_placement.AddDatanode(NewDn(i));
  }

  auto unit = FLAGS_dn_writable_unit_capacity_bytes_unit;
  dns_[0]->stat().capacity = static_cast<uint64_t>(double(unit) * 10);
  for (int i = 1; i < total_dn; ++i) {
    int rt = rand() % 100 + 10;
    dns_[i]->stat().capacity = static_cast<uint64_t>(double(unit) * (rt + 0.2));
  }
  dns_[0]->stat().capacity = static_cast<uint64_t>(double(unit) * (1 + 0.2));
  dns_[1]->stat().capacity = static_cast<uint64_t>(double(unit) * (2 + 0.2));
  dns_[2]->stat().capacity = static_cast<uint64_t>(double(unit) * (3 + 0.2));
  dns_[3]->stat().capacity = static_cast<uint64_t>(double(unit) * (4 + 0.2));
  dns_[4]->stat().capacity = static_cast<uint64_t>(double(unit) * (10 + 0.2));
  dns_[5]->stat().capacity = static_cast<uint64_t>(double(unit) * (20 + 0.2));
  dns_[6]->stat().capacity = static_cast<uint64_t>(double(unit) * (30 + 0.2));
  dns_[7]->stat().capacity = static_cast<uint64_t>(double(unit) * (40 + 0.2));

  for (std::size_t i = 0; i < total_dn; i++) {
    LOG(INFO) << "dn=" << i << " weight=" << dns_[i]->GetWriteableWeight();
  }
  local_placement.RefreshConfig();

  std::vector<int> cnt(total_dn, 0);
  auto start = TimeUtil::GetNowEpochMs();
  while (true) {
    for (int i = 0; i < 10000; ++i) {
      std::vector<DatanodeInfoPtr> result;
      local_placement.ChooseTarget4New("",
                                       3,
                                       128,
                                       PlacementAdvice(),
                                       NetworkLocationInfo(),
                                       favored_nodes_,
                                       &excluded_,
                                       &result);
      EXPECT_EQ(result.size(), 3);
      for (int i = 0; i < 3; ++i) {
        cnt[result[i]->id()]++;
        VLOG(15) << "i=" << i << " dn=" << result[i]->id();
      }
      excluded_.clear();
      result.clear();
    }

    auto now = TimeUtil::GetNowEpochMs();
    if (now - start > 10 * 1000) {
      break;
    }
  }

  for (int i = 0; i < total_dn; ++i) {
    LOG(INFO) << "i=" << i << " weight=" << dns_[i]->GetWriteableWeight()
              << " cnt=" << cnt[i]
              << " ratio=" << (cnt[i] / dns_[i]->GetWriteableWeight());
  }

  EXPECT_GT(cnt[0], 0);
  for (int i = 1; i < total_dn; ++i) {
    double r_cnt = double(cnt[i]) / cnt[0];
    double r_weight =
        double(dns_[i]->GetWriteableWeight()) / dns_[0]->GetWriteableWeight();
    EXPECT_LT(abs(r_cnt / r_weight - 1), 0.5);
  }
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4NewDnDying) {
  FLAGS_datanode_dying_interval_ms = 1000 * 1000;
  FLAGS_datanode_stale_interval_ms = 10 * 1000 * 1000;
  auto time_gap =
      std::chrono::milliseconds(FLAGS_datanode_dying_interval_ms * 2);

  BlockPlacementCfsDefault local_placement(metrics_.get());

  int total_dn = 5;
  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }

  dns_[0]->UpdateHeartbeat4Test(std::chrono::system_clock::now());
  dns_[1]->UpdateHeartbeat4Test(std::chrono::system_clock::now());
  dns_[2]->UpdateHeartbeat4Test(std::chrono::system_clock::now());
  dns_[3]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);
  dns_[4]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);

  for (std::size_t i = 0; i < total_dn; i++) {
    LOG(INFO) << "dn=" << i << " last_heartbeat="
              << dns_[i]->last_heartbeat().time_since_epoch().count()
              << " is_alive=" << dns_[i]->IsAlive()
              << " is_stale=" << dns_[i]->IsStale()
              << " is_dying=" << dns_[i]->IsDying();
  }
  local_placement.RefreshConfig();

  EXPECT_FALSE(dns_[0]->IsDying());
  EXPECT_FALSE(dns_[1]->IsDying());
  EXPECT_FALSE(dns_[2]->IsDying());
  EXPECT_TRUE(dns_[3]->IsDying());
  EXPECT_TRUE(dns_[4]->IsDying());

  std::vector<int> cnt(total_dn, 0);
  for (int i = 0; i < 100000; ++i) {
    std::vector<DatanodeInfoPtr> result;
    local_placement.ChooseTarget4New("",
                                     3,
                                     128,
                                     PlacementAdvice(),
                                     NetworkLocationInfo(),
                                     favored_nodes_,
                                     &excluded_,
                                     &result);
    EXPECT_EQ(result.size(), 3);
    for (int i = 0; i < 3; ++i) {
      cnt[result[i]->id()]++;
      VLOG(15) << "i=" << i << " dn=" << result[i]->id();
    }
    excluded_.clear();
    result.clear();
  }

  for (int i = 0; i < total_dn; ++i) {
    LOG(INFO) << "i=" << i << " cnt=" << cnt[i];
  }

  EXPECT_GT(cnt[0], 0);
  EXPECT_GT(cnt[1], 0);
  EXPECT_GT(cnt[2], 0);
  EXPECT_EQ(cnt[3], 0);
  EXPECT_EQ(cnt[4], 0);
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4NewAllDnDying) {
  FLAGS_datanode_dying_interval_ms = 1000 * 1000;
  FLAGS_datanode_stale_interval_ms = 10 * 1000 * 1000;
  auto time_gap =
      std::chrono::milliseconds(FLAGS_datanode_dying_interval_ms * 2);

  BlockPlacementCfsDefault local_placement(metrics_.get());

  int total_dn = 5;
  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }

  dns_[0]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);
  dns_[1]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);
  dns_[2]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);
  dns_[3]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);
  dns_[4]->UpdateHeartbeat4Test(std::chrono::system_clock::now() - time_gap);

  for (std::size_t i = 0; i < total_dn; i++) {
    LOG(INFO) << "dn=" << i << " last_heartbeat="
              << dns_[i]->last_heartbeat().time_since_epoch().count()
              << " is_alive=" << dns_[i]->IsAlive()
              << " is_stale=" << dns_[i]->IsStale()
              << " is_dying=" << dns_[i]->IsDying();
  }
  local_placement.RefreshConfig();

  EXPECT_TRUE(dns_[0]->IsDying());
  EXPECT_TRUE(dns_[1]->IsDying());
  EXPECT_TRUE(dns_[2]->IsDying());
  EXPECT_TRUE(dns_[3]->IsDying());
  EXPECT_TRUE(dns_[4]->IsDying());

  std::vector<int> cnt(total_dn, 0);
  for (int i = 0; i < 100000; ++i) {
    std::vector<DatanodeInfoPtr> result;
    local_placement.ChooseTarget4New("",
                                     3,
                                     128,
                                     PlacementAdvice(),
                                     NetworkLocationInfo(),
                                     favored_nodes_,
                                     &excluded_,
                                     &result);
    EXPECT_EQ(result.size(), 3);
    for (int i = 0; i < 3; ++i) {
      cnt[result[i]->id()]++;
      VLOG(15) << "i=" << i << " dn=" << result[i]->id();
    }
    excluded_.clear();
    result.clear();
  }

  for (int i = 0; i < total_dn; ++i) {
    LOG(INFO) << "i=" << i << " cnt=" << cnt[i];
  }

  EXPECT_GT(cnt[0], 0);
  EXPECT_GT(cnt[1], 0);
  EXPECT_GT(cnt[2], 0);
  EXPECT_GT(cnt[3], 0);
  EXPECT_GT(cnt[4], 0);
}

TEST_F(BlockPlacementCfsDefaultTest, ChooseTarget4NewWithHost3Rep) {
  FLAGS_block_placement_distribution_type = "uniform";
  FLAGS_placement_ignore_existed_host = false;
  FLAGS_placement_ignore_existed_switch = false;
  FLAGS_choose_target_shuffle_candidate = true;

  google::SetVLOGLevel("*", 0);
  DEFER([&]() { google::SetVLOGLevel("*", FLAGS_ut_loglevel_v); });

  BlockPlacementCfsDefault local_placement(metrics_.get());

  int total_dn = 120;
  for (std::size_t i = 0; i < 5; i++) {
    local_placement.AddDatanode(dns_[i].get());
  }
  for (int i = 5; i < total_dn; ++i) {
    local_placement.AddDatanode(NewDn(i));
  }

  for (int i = 0; i < 30; ++i) {
    cloudfs::LocationTag tag;
    tag.set_az("az");
    tag.set_switch_("switch");
    tag.set_host("host-a-" + std::to_string(i / 30));
    dns_[i]->set_location_tag(tag);
  }

  for (int i = 30; i < total_dn; ++i) {
    cloudfs::LocationTag tag;
    tag.set_az("az");
    tag.set_switch_("switch");
    tag.set_host("host-b-" + std::to_string(i / 30));
    dns_[i]->set_location_tag(tag);
  }

  for (std::size_t i = 0; i < total_dn; i++) {
    LOG(INFO) << "dn=" << i
              << " location_tag=" << dns_[i]->location_tag().ShortDebugString();
  }
  local_placement.RefreshConfig();

  // init storage
  auto id = GetGlobalStoragePolicySuite().GetPolicyFromId(
      kBlockStoragePolicyIdUnspecified);

  std::vector<int> cnt(total_dn, 0);
  auto start = TimeUtil::GetNowEpochMs();
  while (true) {
    for (int i = 0; i < 100000; ++i) {
      std::vector<DatanodeInfoPtr> result;
      local_placement.ChooseTarget4New("",
                                       3,
                                       128,
                                       PlacementAdvice(),
                                       NetworkLocationInfo(),
                                       favored_nodes_,
                                       &excluded_,
                                       &result);
      EXPECT_EQ(result.size(), 3);
      for (int i = 0; i < 3; ++i) {
        cnt[result[i]->id()]++;
        VLOG(15) << "i=" << i << " dn=" << result[i]->id();
      }
      excluded_.clear();
      result.clear();

      if (i % 10000 == 0) {
        local_placement.RecalcAllDatanode();
      }
    }

    auto now = TimeUtil::GetNowEpochMs();
    if (now - start > 30 * 1000) {
      break;
    }
  }

  for (int i = 0; i < total_dn; ++i) {
    LOG(INFO) << "i=" << i
              << " location_tag=" << dns_[i]->location_tag().ShortDebugString()
              << " cnt=" << cnt[i];
  }

  EXPECT_GT(cnt[0], 0);
  for (int i = 0; i < total_dn; ++i) {
    EXPECT_LT(abs(double(cnt[i]) / cnt[0] - 1.00), 0.1);
  }
}

} // namespace dancenn
