// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/02/21
// Description

#include "datanode_manager/datanode_info.h"

#include <gtest/gtest.h>

#include <set>

#include "proto/generated/cloudfs/hdfs.pb.h"

namespace dancenn {

TEST(DatanodeInfoTest, DatanodeIDProtoSet) {
  std::set<cloudfs::DatanodeIDProto, datanode_info::DatanodeIDProtoCompare>
      addresses;

  cloudfs::DatanodeIDProto a;
  EXPECT_TRUE(addresses.insert(a).second);
  EXPECT_FALSE(addresses.insert(a).second);
  EXPECT_EQ(addresses.find(a)->SerializeAsString(), a.SerializeAsString());

  cloudfs::DatanodeIDProto b;
  b.set_ipaddr("n1");
  EXPECT_TRUE(addresses.insert(b).second);
  EXPECT_FALSE(addresses.insert(b).second);
  EXPECT_EQ(addresses.find(a)->SerializeAsString(), a.SerializeAsString());
  EXPECT_EQ(addresses.find(b)->SerializeAsString(), b.SerializeAsString());

  cloudfs::DatanodeIDProto c;
  c.set_ipaddr("n2");
  EXPECT_TRUE(addresses.insert(c).second);
  EXPECT_FALSE(addresses.insert(c).second);
  EXPECT_EQ(addresses.find(a)->SerializeAsString(), a.SerializeAsString());
  EXPECT_EQ(addresses.find(b)->SerializeAsString(), b.SerializeAsString());
  EXPECT_EQ(addresses.find(c)->SerializeAsString(), c.SerializeAsString());
}

}  // namespace dancenn
