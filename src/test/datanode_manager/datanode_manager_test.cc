// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "datanode_manager/datanode_manager.h"

#include <base/constants.h>
#include <base/uuid.h>
#include <gtest/gtest.h>

#include "block_manager/block_report_handler.h"
#include "datanode_manager/storage_policy.h"
#include "hdfs.pb.h"
#include "namespace/meta_storage.h"

DECLARE_bool(run_ut);

DECLARE_int32(blockmap_num_slice);
DECLARE_int32(blockmap_num_bucket_each_slice);

DECLARE_string(block_placement_policy);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_string(all_datacenters);
DECLARE_string(datanode_machine_file);
DECLARE_int32(datanode_info_fg_dump_size_threshold);
DECLARE_int32(datanode_info_fg_dump_time_threshold);
DECLARE_int32(sort_located_block_dns_hb_window_ms);
DECLARE_bool(enable_location_tag);
DECLARE_bool(enable_location_tag_by_rack_aware);

namespace dancenn {

namespace {

auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
auto datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
auto default_dfs_replication_min = FLAGS_dfs_replication_min;

class DatanodeManagerTest : public testing::Test {
 public:
  void SetUp() override {
    origin_block_placement_policy_ = FLAGS_block_placement_policy;
    origin_all_datacenters_ = FLAGS_all_datacenters;

    FLAGS_run_ut = true;

    FLAGS_blockmap_num_bucket_each_slice = 1024;
    FLAGS_blockmap_num_slice = 16;
    block_manager_ = std::make_unique<BlockManager>();

    FLAGS_block_placement_policy = "multi-dc";
    FLAGS_all_datacenters = "LF,HL";
    FLAGS_datanode_keep_alive_timeout_sec = 1;
    FLAGS_datanode_stale_interval_ms = 300;
    FLAGS_enable_location_tag = false;
    FLAGS_enable_location_tag_by_rack_aware = true;

    // meta storage
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_storage_.reset(new MetaStorage(db_path_));
    meta_storage_->Launch();
    meta_storage_->SaveNumINodes(0);
    auto be_last_inode_id = platform::HostToBigEndian(static_cast<INodeID>(1));

    meta_storage_->PutNameSystemInfo(
        kLastINodeIdKey,
        cnetpp::base::StringPiece(reinterpret_cast<char*>(&be_last_inode_id),
                                  sizeof(be_last_inode_id)));
    meta_storage_->ResetLastAppliedTxId(0);
    meta_storage_->ResetLastInodeId(1);

    dn_manager_ = std::make_unique<DatanodeManager>(true);
    dn_manager_->set_block_manager(block_manager_.get());
    dn_manager_->set_meta_storage(meta_storage_.get());

    block_report_manager_ = std::make_unique<BlockReportManager>();
    block_manager_->set_block_report_manager(block_report_manager_.get());
    block_manager_->SetMetaStorage(meta_storage_);
    block_report_manager_->Start(reinterpret_cast<NameSpace*>(0x1),
                                 dn_manager_.get(),
                                 block_manager_.get());
    block_report_manager_->StartStandby();
  }

  void TearDown() override {
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_dfs_replication_min = default_dfs_replication_min;

    FLAGS_block_placement_policy = origin_block_placement_policy_;
    FLAGS_all_datacenters = origin_all_datacenters_;

    block_report_manager_->StopStandby();
    block_report_manager_->Stop();
    dn_manager_->Stop();

    FLAGS_block_placement_policy = origin_block_placement_policy_;
  }

  const DatanodeInfoPtr RegisterMockDatanode(const std::string& addr,
                                             StorageTypeProto type);

  // no heartbeat to insert storage
  const DatanodeInfoPtr RegisterMockDatanodeWithoutHB(const std::string& addr);

  cloudfs::datanode::DatanodeRegistrationProto MockDatanode(
      const std::string& ip,
      const std::string& uuid = "");

  std::unique_ptr<DatanodeManager> dn_manager_;
  std::unique_ptr<BlockManager> block_manager_;
  std::unique_ptr<BlockReportManager> block_report_manager_;
  std::string src;

  // Members for restoring global flags
  std::string origin_block_placement_policy_;
  std::string origin_all_datacenters_;

  // db store
  std::shared_ptr<MetaStorage> meta_storage_;
  std::string db_path_ = "rocksdb_XXXXXX";
};

cloudfs::datanode::DatanodeRegistrationProto DatanodeManagerTest::MockDatanode(
    const std::string& ip,
    const std::string& uuid) {
  cloudfs::datanode::DatanodeRegistrationProto reg;
  reg.mutable_datanodeid()->set_ipaddr(ip);
  reg.mutable_datanodeid()->set_hostname(ip);
  reg.mutable_datanodeid()->set_datanodeuuid(uuid == "" ? ip : uuid);
  reg.mutable_datanodeid()->set_xferport(0);
  reg.mutable_datanodeid()->set_infoport(0);
  reg.mutable_datanodeid()->set_ipcport(0);

  reg.mutable_storageinfo()->set_layoutversion(0);
  reg.mutable_storageinfo()->set_namespaceid(0);
  reg.mutable_storageinfo()->set_clusterid("");
  reg.mutable_storageinfo()->set_ctime(0);

  reg.mutable_keys()->set_isblocktokenenabled(false);
  reg.mutable_keys()->set_keyupdateinterval(0);
  reg.mutable_keys()->set_tokenlifetime(0);
  reg.mutable_keys()->mutable_currentkey()->set_keyid(0);
  reg.mutable_keys()->mutable_currentkey()->set_expirydate(0);

  reg.set_softwareversion("");

  // add location tag
  auto location = ResolveNetworkLocation(ip);
  reg.mutable_datanodeid()->mutable_location_tag()->set_az(location.dc);
  reg.mutable_datanodeid()->mutable_location_tag()->set_switch_(location.rack);
  reg.mutable_datanodeid()->mutable_location_tag()->set_host(ip);

  return reg;
}

void GetDiskRequest(cloudfs::datanode::HeartbeatRequestProto* request,
                    const std::string& uuid,
                    const cloudfs::datanode::DatanodeRegistrationProto& reg,
                    uint64_t remaining = 0) {
  RepeatedStorageReport* disk_storage_report = request->mutable_reports();
  auto disk_report = disk_storage_report->Add();
  disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
  disk_report->mutable_storage()->set_storageuuid(uuid);
  request->mutable_reports(0)->set_capacity(remaining * 3);
  request->mutable_reports(0)->set_dfsused(remaining * 2);
  request->mutable_reports(0)->set_remaining(remaining);
  request->mutable_registration()->CopyFrom(reg);
}

const DatanodeInfoPtr DatanodeManagerTest::RegisterMockDatanode(
    const std::string& ip,
    StorageTypeProto type) {
  auto proto = MockDatanode(ip);
  cnetpp::base::IPAddress ip_addr(ip);
  dn_manager_->Register(proto.datanodeid(), &proto, ip_addr);
  RepeatedStorageReport reports;
  auto r = reports.Add();
  r->set_storageuuid(UUID().ToString());
  r->mutable_storage()->set_storagetype(type);
  cloudfs::datanode::HeartbeatRequestProto request;
  request.mutable_reports()->CopyFrom(reports);
  request.mutable_registration()->CopyFrom(proto);
  DatanodeManager::RepeatedCmds cmds;
  dn_manager_->Heartbeat(request, &cmds);

  return dn_manager_->GetDatanodeFromIp(ip_addr);
}

const DatanodeInfoPtr DatanodeManagerTest::RegisterMockDatanodeWithoutHB(
    const std::string& ip) {
  auto proto = MockDatanode(ip);
  cnetpp::base::IPAddress ip_addr(ip);
  dn_manager_->Register(proto.datanodeid(), &proto, ip_addr);

  return dn_manager_->GetDatanodeFromIp(ip_addr);
}

TEST_F(DatanodeManagerTest, TestRegister) {
  auto dn1 = MockDatanode("***********");
  auto dn2 = MockDatanode("***********");
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");

  // register dn1
  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  // re-register dn1
  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  datanodes.Clear();
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  dn_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  datanodes.Clear();
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 2);
  std::set<std::string> ips;
  for (int i = 0; i < datanodes.size(); ++i) {
    ips.emplace(datanodes.Get(i).id().ipaddr());
  }
  int i = 10;
  for (const auto& ip : ips) {
    if (i >= datanodes.size()) break;
    ASSERT_EQ(ip, "10.10.10." + std::to_string(i));
    ++i;
  }
}

TEST_F(DatanodeManagerTest, TestRegisterWithDifferentUuid) {
  std::string uuid1 = UUID().ToString();
  std::string uuid2 = UUID().ToString();
  auto dn1 = MockDatanode("***********", uuid1);
  auto dn2 = MockDatanode("***********", uuid2);
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");

  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  ASSERT_FALSE(dn_manager_->IsDatanodeStale(1));
  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip1));

  dn_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  datanodes.Clear();
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip2), &datanodes);
  ASSERT_EQ(datanodes.size(), 2);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  ASSERT_FALSE(dn_manager_->IsDatanodeStale(2));
  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip2));
}

TEST_F(DatanodeManagerTest, TestRegisterWithDifferentUuidForStale) {
  std::string uuid1 = UUID().ToString();
  std::string uuid2 = UUID().ToString();
  auto dn1 = MockDatanode("***********", uuid1);
  auto dn2 = MockDatanode("***********", uuid2);
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");

  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  ASSERT_FALSE(dn_manager_->IsDatanodeStale(1));
  dn_manager_->SetDatanodeStale(1, true);
  ASSERT_TRUE(dn_manager_->IsDatanodeStale(1));

  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip1));
  dn_manager_->SetDecommissioning(1);
  ASSERT_TRUE(dn_manager_->IsDecommissionInProgress(ip1));

  dn_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  datanodes.Clear();
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip2), &datanodes);
  ASSERT_EQ(datanodes.size(), 2);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  ASSERT_TRUE(dn_manager_->IsDatanodeStale(2));
  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip2));
}

TEST_F(DatanodeManagerTest, TestHeartbeat) {
  auto dn = MockDatanode("***********");
  cnetpp::base::IPAddress ip("***********");

  // heartbeat w/o register
  cloudfs::datanode::HeartbeatRequestProto request;
  request.mutable_registration()->CopyFrom(dn);
  DatanodeManager::RepeatedCmds cmds;
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 1);
  ASSERT_EQ(cmds.Get(0).cmdtype(),
            cloudfs::datanode::DatanodeCommandProto::RegisterCommand);

  // heartbeat w register
  dn_manager_->Register(dn.datanodeid(), &dn, ip);
  cmds.Clear();
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 0);

  // heartbeat after datanode is dead
  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_datanode_keep_alive_timeout_sec * 2));
  cmds.Clear();
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 1);
  ASSERT_EQ(cmds.Get(0).cmdtype(),
            cloudfs::datanode::DatanodeCommandProto::RegisterCommand);
}

TEST_F(DatanodeManagerTest, TestBriefStat) {
  auto stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 0);
  ASSERT_EQ(stat.num_normal_datanode, 0);
  ASSERT_EQ(stat.num_decommissioning_datanode, 0);
  ASSERT_EQ(stat.num_decommissioned_datanode, 0);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 0);
  ASSERT_EQ(stat.num_dead_datanode, 0);
  ASSERT_EQ(stat.num_content_stale_datanode, 0);
  ASSERT_EQ(stat.num_stale_datanode, 0);

  auto dn1 = MockDatanode("***********");
  cnetpp::base::IPAddress ip1("***********");
  cloudfs::datanode::HeartbeatRequestProto request;
  for (uint64_t i = 10; i < 13; ++i) {
    auto report = request.add_reports();
    report->set_capacity(i);
    report->set_dfsused(i - 10);
    report->set_remaining(1);
    report->set_blockpoolused(5);
    report->mutable_storage()->set_storageuuid(std::to_string(i));
  }
  request.set_xceivercount(10);
  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedCmds cmds;
  request.mutable_registration()->CopyFrom(dn1);
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 0);
  stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 1);
  ASSERT_EQ(stat.num_normal_datanode, 1);
  ASSERT_EQ(stat.num_decommissioning_datanode, 0);
  ASSERT_EQ(stat.num_decommissioned_datanode, 0);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 1);
  ASSERT_EQ(stat.num_dead_datanode, 0);
  ASSERT_EQ(stat.num_content_stale_datanode, 1);
  ASSERT_EQ(stat.num_stale_datanode, 0);

  auto info = dn_manager_->GetDatanodeFromId(1);
  info->MarkStaleAfterFailover();

  stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 1);
  ASSERT_EQ(stat.num_normal_datanode, 1);
  ASSERT_EQ(stat.num_decommissioning_datanode, 0);
  ASSERT_EQ(stat.num_decommissioned_datanode, 0);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 1);
  ASSERT_EQ(stat.num_dead_datanode, 0);
  ASSERT_EQ(stat.num_content_stale_datanode, 1);
  ASSERT_EQ(stat.num_stale_datanode, 0);

  info->UpdateHeartbeat(true);
  dn_manager_->SetDecommissioning(1);
  stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 1);
  ASSERT_EQ(stat.num_normal_datanode, 0);
  ASSERT_EQ(stat.num_decommissioning_datanode, 1);
  ASSERT_EQ(stat.num_decommissioned_datanode, 0);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 1);
  ASSERT_EQ(stat.num_dead_datanode, 0);
  ASSERT_EQ(stat.num_content_stale_datanode, 1);
  ASSERT_EQ(stat.num_stale_datanode, 0);

  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_datanode_keep_alive_timeout_sec * 2));
  stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 1);
  ASSERT_EQ(stat.num_normal_datanode, 0);
  ASSERT_EQ(stat.num_decommissioning_datanode, 1);
  ASSERT_EQ(stat.num_decommissioned_datanode, 0);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 0);
  ASSERT_EQ(stat.num_dead_datanode, 1);
  ASSERT_EQ(stat.num_content_stale_datanode, 1);
  ASSERT_EQ(stat.num_stale_datanode, 1);

  dn_manager_->SetDecommissioned(1, false);
  stat = dn_manager_->GetBriefStorageStat();
  ASSERT_EQ(stat.num_total_datanode, 1);
  ASSERT_EQ(stat.num_normal_datanode, 0);
  ASSERT_EQ(stat.num_decommissioning_datanode, 0);
  ASSERT_EQ(stat.num_decommissioned_datanode, 1);
  ASSERT_EQ(stat.num_entering_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_in_maintenance_datanode, 0);
  ASSERT_EQ(stat.num_live_datanode, 0);
  ASSERT_EQ(stat.num_dead_datanode, 0);
  ASSERT_EQ(stat.num_content_stale_datanode, 0);
  ASSERT_EQ(stat.num_stale_datanode, 0);
}

TEST_F(DatanodeManagerTest, TestStat) {
  auto stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 0);
  ASSERT_EQ(stat.dfs_used, 0);
  ASSERT_EQ(stat.remaining, 0);
  ASSERT_EQ(stat.blockpool_used, 0);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 0);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);
  ASSERT_EQ(stat.expired_heartbeats, 0);

  auto dn1 = MockDatanode("***********");
  cnetpp::base::IPAddress ip1("***********");
  cloudfs::datanode::HeartbeatRequestProto request;
  for (uint64_t i = 10; i < 13; ++i) {
    auto report = request.add_reports();
    report->set_capacity(i);
    report->set_dfsused(i - 10);
    report->set_remaining(1);
    report->set_blockpoolused(5);
    report->mutable_storage()->set_storageuuid(std::to_string(i));
  }
  request.set_xceivercount(10);
  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedCmds cmds;
  request.mutable_registration()->CopyFrom(dn1);
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 0);
  stat.Clear();
  stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 33);
  ASSERT_EQ(stat.dfs_used, 3);
  ASSERT_EQ(stat.remaining, 3);
  ASSERT_EQ(stat.blockpool_used, 15);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 10);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);                // TODO(liyuan)
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);  // TODO(liyuan)
  ASSERT_EQ(stat.expired_heartbeats, 0);

  auto dn2 = MockDatanode("***********");
  cnetpp::base::IPAddress ip2("***********");
  request.mutable_registration()->Clear();
  request.mutable_registration()->CopyFrom(dn2);
  cmds.Clear();
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 1);
  // Heartbeat without Register, first time heartbeat will include Register
  // Command
  ASSERT_EQ(cmds.Get(0).cmdtype(),
            cloudfs::datanode::DatanodeCommandProto::RegisterCommand);
  stat.Clear();
  stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 33);
  ASSERT_EQ(stat.dfs_used, 3);
  ASSERT_EQ(stat.remaining, 3);
  ASSERT_EQ(stat.blockpool_used, 15);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 10);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);                // TODO(liyuan)
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);  // TODO(liyuan)
  ASSERT_EQ(stat.expired_heartbeats, 0);

  // register only should increase nodes_in_service
  dn_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  stat.Clear();
  stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 33);
  ASSERT_EQ(stat.dfs_used, 3);
  ASSERT_EQ(stat.remaining, 3);
  ASSERT_EQ(stat.blockpool_used, 15);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 10);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);                // TODO(liyuan)
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);  // TODO(liyuan)
  ASSERT_EQ(stat.expired_heartbeats, 0);

  cmds.Clear();
  dn_manager_->Heartbeat(request, &cmds);
  ASSERT_EQ(cmds.size(), 0);
  stat.Clear();
  stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 66);
  ASSERT_EQ(stat.dfs_used, 6);
  ASSERT_EQ(stat.remaining, 6);
  ASSERT_EQ(stat.blockpool_used, 30);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 20);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);                // TODO(liyuan)
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);  // TODO(liyuan)
  ASSERT_EQ(stat.expired_heartbeats, 0);

  // datanode dead
  stat.Clear();
  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_datanode_keep_alive_timeout_sec * 2));
  stat = dn_manager_->stat();
  ASSERT_EQ(stat.capacity, 0);
  ASSERT_EQ(stat.dfs_used, 0);
  ASSERT_EQ(stat.remaining, 0);
  ASSERT_EQ(stat.blockpool_used, 0);
  ASSERT_EQ(stat.non_dfs_used, 0);
  ASSERT_EQ(stat.xceiver_count, 0);
  ASSERT_EQ(stat.cache_capacity, 0);
  ASSERT_EQ(stat.cache_used, 0);
  ASSERT_EQ(stat.nodes_in_service, 0);                // TODO(liyuan)
  ASSERT_EQ(stat.nodes_in_service_xceiver_count, 0);  // TODO(liyuan)
  ASSERT_EQ(stat.expired_heartbeats, 2);
}

TEST_F(DatanodeManagerTest, DatanodeReport) {
  FLAGS_enable_location_tag_by_rack_aware = true;
  FLAGS_enable_location_tag = false;

  cnetpp::base::IPAddress client_ip1("***********0");  // LF
  cnetpp::base::IPAddress client_ip2("************");  // HL

  cloudfs::GetDatanodeReportRequestProto request;
  cloudfs::GetDatanodeReportResponseProto response;

  auto status = dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::ALL,
                                               NetworkLocationInfo(client_ip1),
                                               response.mutable_di());
  ASSERT_EQ(status.exception(), JavaExceptions::Exception::kIOException);
  ASSERT_EQ(status.message(),
            "Failed to get datanode report for ALL datanodes.");

  cnetpp::base::IPAddress ip1("***********");
  auto proto1 = MockDatanode("***********");
  auto location_tag1 = proto1.mutable_datanodeid()->mutable_location_tag();
  location_tag1->set_az("LF");
  location_tag1->set_switch_("rack1");
  location_tag1->set_host("host1");
  dn_manager_->Register(proto1.datanodeid(), &proto1, ip1);

  cnetpp::base::IPAddress ip2("***********");
  auto proto2 = MockDatanode("***********");
  auto location_tag2 = proto2.mutable_datanodeid()->mutable_location_tag();
  location_tag2->set_az("HL");
  location_tag2->set_switch_("rack2");
  location_tag2->set_host("host2");
  dn_manager_->Register(proto2.datanodeid(), &proto2, ip2);

  HeartbeatRequestProto heart_request;
  DatanodeManager::RepeatedCmds cmds;
  GetDiskRequest(&heart_request, "storage1", proto1, 10);
  dn_manager_->Heartbeat(heart_request, &cmds);

  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::ALL,
                                 NetworkLocationInfo(client_ip2),
                                 response.mutable_di());
  LOG(INFO) << response.ShortDebugString();
  ASSERT_EQ(response.di_size(), 2);
  ASSERT_TRUE(response.di(0).id().ipaddr() == "***********" ||
              response.di(0).id().ipaddr() == "***********");
  ASSERT_TRUE(response.di(1).id().ipaddr() == "***********" ||
              response.di(1).id().ipaddr() == "***********");
  cloudfs::DatanodeInfoProto* dn;
  if (response.di(0).id().ipaddr() == "***********") {
    dn = response.mutable_di(0);
  } else if (response.di(1).id().ipaddr() == "***********") {
    dn = response.mutable_di(1);
  } else {
    dn = nullptr;
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(dn->remaining(), 10);
  ASSERT_EQ(dn->capacity(), 30);
  ASSERT_EQ(dn->dfsused(), 20);

  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::LIVE,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 2);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::DEAD,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::DECOMMISSIONING,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 1);
  ASSERT_EQ(response.di(0).id().ipaddr(), "***********");
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip2),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 1);
  ASSERT_EQ(response.di(0).id().ipaddr(), "***********");

  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_datanode_keep_alive_timeout_sec * 2));
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::DEAD,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 2);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::ALL,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 2);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip2),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);

  cnetpp::base::IPAddress ip3("***********");
  auto proto3 = MockDatanode("***********");
  dn_manager_->Register(proto3.datanodeid(), &proto3, ip3);

  GetDiskRequest(&heart_request, "storage1", proto3, 10);
  dn_manager_->Heartbeat(heart_request, &cmds);

  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::ALL,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 3);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::LIVE,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 1);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::DEAD,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 2);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::DECOMMISSIONING,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip1),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 1);
  ASSERT_EQ(response.di(0).id().ipaddr(), "***********");
  response.Clear();
  dn_manager_->GetDatanodeReport(DatanodeReportTypeProto::CUR_DC_LIVE,
                                 NetworkLocationInfo(client_ip2),
                                 response.mutable_di());
  ASSERT_EQ(response.di_size(), 0);

  LOG(INFO) << "Finished";
}

TEST_F(DatanodeManagerTest, SortLocated) {
  FLAGS_sort_located_block_dns_hb_window_ms = 10;
  FLAGS_datanode_stale_interval_ms =
      30 * FLAGS_sort_located_block_dns_hb_window_ms;
  FLAGS_enable_location_tag_by_rack_aware = true;
  FLAGS_enable_location_tag = false;

  std::vector<std::string> ips;
  ips.emplace_back("***********");   // 1. local
  ips.emplace_back("***********");   // 2. local rack
  ips.emplace_back("***********");   // 3. local rack
  ips.emplace_back("***********0");  // 4. local dc
  ips.emplace_back("***********1");  // 5. local dc
  ips.emplace_back("***********");   // 6. remote dc
  ips.emplace_back("***********");   // 7. remote dc

  auto now = std::chrono::system_clock::now();
  auto hb_time = now - std::chrono::milliseconds(
                           20 * FLAGS_sort_located_block_dns_hb_window_ms);
  for (const auto& ip : ips) {
    auto proto = MockDatanode(ip);
    cnetpp::base::IPAddress ip_addr(ip);
    dn_manager_->Register(proto.datanodeid(), &proto, ip_addr);

    auto dn_info = dn_manager_->GetDatanodeFromIp(ip);
    CHECK_NOTNULL(dn_info);
    dn_info->UpdateHeartbeat4Test(hb_time);
    hb_time += std::chrono::milliseconds(
        2 * FLAGS_sort_located_block_dns_hb_window_ms);
  }

  {
    cnetpp::base::IPAddress client_ip("***********");
    cloudfs::LocatedBlockProto block;
    // local, local_rack * 2
    std::vector<uint32_t> storage{1, 2, 3};
    ReadAdvice advice;
    auto remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(block.storagetypes(0), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(1), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(2), StorageTypeProto::DISK);
    ASSERT_EQ(block.storageids(0), "***********");
    ASSERT_EQ(block.storageids(1), "***********");
    ASSERT_EQ(block.storageids(2), "***********");
    ASSERT_EQ(remote_idx, 3);

    // stale datanode
    block.Clear();
    dn_manager_->SetDatanodeStale(1, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 3);
    dn_manager_->SetDatanodeStale(1, false);

    block.Clear();
    dn_manager_->SetDatanodeStale(2, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 3);
    dn_manager_->SetDatanodeStale(2, false);

    block.Clear();
    dn_manager_->SetDatanodeStale(1, true);
    dn_manager_->SetDatanodeStale(2, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 3);
    dn_manager_->SetDatanodeStale(1, false);
    dn_manager_->SetDatanodeStale(2, false);
  }
  {
    cnetpp::base::IPAddress client_ip("***********");
    cloudfs::LocatedBlockProto block;
    // local_rack * 2, local dc
    std::vector<uint32_t> storage{4, 3, 2};
    ReadAdvice advice;
    auto remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********0");
    ASSERT_EQ(block.storagetypes(0), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(1), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(2), StorageTypeProto::DISK);
    ASSERT_EQ(block.storageids(0), "***********");
    ASSERT_EQ(block.storageids(1), "***********");
    ASSERT_EQ(block.storageids(2), "***********0");
    ASSERT_EQ(remote_idx, 3);

    // stale datanode
    block.Clear();
    dn_manager_->SetDatanodeStale(2, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********0");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 3);
    dn_manager_->SetDatanodeStale(2, false);

    block.Clear();
    dn_manager_->SetDatanodeStale(2, true);
    dn_manager_->SetDatanodeStale(3, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********0");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 3);
    dn_manager_->SetDatanodeStale(2, false);
    dn_manager_->SetDatanodeStale(3, false);
  }
  {
    cnetpp::base::IPAddress client_ip("***********");
    cloudfs::LocatedBlockProto block;
    // local_rack, local dc, remote dc
    std::vector<uint32_t> storage{7, 2, 4};
    ReadAdvice advice;
    auto remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********0");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(block.storagetypes(0), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(1), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(2), StorageTypeProto::DISK);
    ASSERT_EQ(block.storageids(0), "***********");
    ASSERT_EQ(block.storageids(1), "***********0");
    ASSERT_EQ(block.storageids(2), "***********");
    ASSERT_EQ(remote_idx, 2);

    // stale datanode
    block.Clear();
    dn_manager_->SetDatanodeStale(7, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********0");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 2);
    dn_manager_->SetDatanodeStale(7, false);

    block.Clear();
    dn_manager_->SetDatanodeStale(7, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********0");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(remote_idx, 2);
    dn_manager_->SetDatanodeStale(7, false);
  }
  {
    cnetpp::base::IPAddress client_ip("***********");
    cloudfs::LocatedBlockProto block;
    // local, remote dc * 2
    std::vector<uint32_t> storage{6, 1, 7};
    ReadAdvice advice;
    auto remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(block.storagetypes(0), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(1), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(2), StorageTypeProto::DISK);
    ASSERT_EQ(block.storageids(0), "***********");
    ASSERT_EQ(block.storageids(1), "***********");
    ASSERT_EQ(block.storageids(2), "***********");
    ASSERT_EQ(remote_idx, 1);

    block.Clear();
    dn_manager_->SetDatanodeStale(6, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");  // remote not stale
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");  // remote stale
    ASSERT_EQ(remote_idx, 1);
    dn_manager_->SetDatanodeStale(6, false);

    block.Clear();
    dn_manager_->SetDatanodeStale(6, true);
    dn_manager_->SetDatanodeStale(1, true);
    remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");  // local stale
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");  // remote not stale
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");  // remote stale
    ASSERT_EQ(remote_idx, 1);
    dn_manager_->SetDatanodeStale(6, false);
    dn_manager_->SetDatanodeStale(1, false);
  }

  // hb time
  {
    hb_time = now;
    for (const auto& ip : ips) {
      auto dn_info = dn_manager_->GetDatanodeFromIp(ip);
      CHECK_NOTNULL(dn_info);
      dn_info->UpdateHeartbeat4Test(hb_time);
      hb_time -= std::chrono::milliseconds(
          2 * FLAGS_sort_located_block_dns_hb_window_ms);
    }

    cnetpp::base::IPAddress client_ip("***********");
    cloudfs::LocatedBlockProto block;
    // local, local_rack * 2
    std::vector<uint32_t> storage{1, 2, 3};
    ReadAdvice advice;
    auto remote_idx =
        dn_manager_->ConstructSortedLocatedBlock(storage,
                                                 advice,
                                                 kHotStoragePolicy,
                                                 NetworkLocationInfo(client_ip),
                                                 &block);
    ASSERT_EQ(block.locs_size(), 3);
    ASSERT_EQ(block.locs(0).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(1).id().ipaddr(), "***********");
    ASSERT_EQ(block.locs(2).id().ipaddr(), "***********");
    ASSERT_EQ(block.storagetypes(0), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(1), StorageTypeProto::DISK);
    ASSERT_EQ(block.storagetypes(2), StorageTypeProto::DISK);
    ASSERT_EQ(block.storageids(0), "***********");
    ASSERT_EQ(block.storageids(1), "***********");
    ASSERT_EQ(block.storageids(2), "***********");
    ASSERT_EQ(remote_idx, 3);
  }
}

TEST_F(DatanodeManagerTest, ConstructLocatedBlocks) {
  std::vector<std::string> ips;
  ips.emplace_back("***********");   // 1. local
  ips.emplace_back("***********");   // 2. local rack
  ips.emplace_back("***********");   // 3. local rack
  ips.emplace_back("***********0");  // 4. local dc
  ips.emplace_back("***********1");  // 5. local dc
  ips.emplace_back("***********");   // 6. remote dc
  ips.emplace_back("***********");   // 7. remote dc
  ips.emplace_back("***********");   // 8. remote dc

  for (const auto& ip : ips) {
    auto proto = MockDatanode(ip);
    cnetpp::base::IPAddress ip_addr(ip);
    dn_manager_->Register(proto.datanodeid(), &proto, ip_addr);
    RepeatedStorageReport reports;
    auto r = reports.Add();
    r->set_storageuuid("storage1");
    r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    HeartbeatRequestProto request;
    request.mutable_reports()->CopyFrom(reports);
    request.mutable_registration()->CopyFrom(proto);
    DatanodeManager::RepeatedCmds cmds;
    dn_manager_->Heartbeat(request, &cmds);
  }

  {
    cnetpp::base::IPAddress client_ip("***********");
    std::vector<std::vector<uint32_t>> storage;
    storage.emplace_back(std::vector<uint32_t>({1, 2, 3}));
    storage.emplace_back(std::vector<uint32_t>({2, 3, 4}));
    storage.emplace_back(std::vector<uint32_t>({3, 4, 5}));
    ReadAdvice advice;
    cloudfs::LocatedBlocksProto blocks;
    blocks.add_blocks();
    blocks.add_blocks();
    blocks.add_blocks();
    dn_manager_->ConstructLocatedSortedBlocks(storage,
                                              advice,
                                              kHotStoragePolicy,
                                              NetworkLocationInfo(client_ip),
                                              &blocks);
    ASSERT_FALSE(blocks.has_filelength());
    ASSERT_FALSE(blocks.has_underconstruction());
    ASSERT_FALSE(blocks.has_islastblockcomplete());
    ASSERT_EQ(blocks.blocks_size(), 3);
    for (int i = 0; i < 3; ++i) {
      ASSERT_FALSE(blocks.blocks(i).has_originallocatedblock());
      ASSERT_FALSE(blocks.blocks(i).has_replicapipeliner());
      ASSERT_EQ(blocks.blocks(i).locs_size(), 3);
    }
  }
  {
    // test hide remote
    cnetpp::base::IPAddress client_ip("***********");
    std::vector<std::vector<uint32_t>> storage;
    storage.emplace_back(std::vector<uint32_t>({1, 7, 3}));
    ReadAdvice advice;
    cloudfs::LocatedBlocksProto blocks;
    blocks.add_blocks();
    dn_manager_->ConstructLocatedSortedBlocks(storage,
                                              advice,
                                              kHotStoragePolicy,
                                              NetworkLocationInfo(client_ip),
                                              &blocks);
    // ASSERT_FALSE(blocks.has_filelength());
    // ASSERT_FALSE(blocks.has_underconstruction());
    // ASSERT_FALSE(blocks.has_islastblockcomplete());
    // ASSERT_EQ(blocks.blocks_size(), 1);
    // ASSERT_EQ(blocks.blocks(0).locs_size(), 2);
    // ASSERT_EQ(blocks.blocks(0).locs(0).id().ipaddr(), "***********");
    // ASSERT_EQ(blocks.blocks(0).locs(1).id().ipaddr(), "***********");
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs_size(), 3);
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(0).id().ipaddr(),
    //          "***********");
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(1).id().ipaddr(),
    //          "***********");
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(1).id().ipaddr(),
    //          "***********");
  }
  {
    // test pipeliner
    cnetpp::base::IPAddress client_ip("***********");
    std::vector<std::vector<uint32_t>> storage;
    storage.emplace_back(std::vector<uint32_t>({8, 7, 6}));
    ReadAdvice advice;
    cloudfs::LocatedBlocksProto blocks;
    blocks.add_blocks();
    dn_manager_->ConstructLocatedSortedBlocks(storage,
                                              advice,
                                              kHotStoragePolicy,
                                              NetworkLocationInfo(client_ip),
                                              &blocks);
    // ASSERT_FALSE(blocks.has_filelength());
    // ASSERT_FALSE(blocks.has_underconstruction());
    // ASSERT_FALSE(blocks.has_islastblockcomplete());
    // ASSERT_EQ(blocks.blocks_size(), 1);
    // ASSERT_EQ(blocks.blocks(0).locs_size(), 0);
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs_size(), 3);
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(0).id().ipaddr(),
    //          "***********");
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(1).id().ipaddr(),
    //          "***********");
    // ASSERT_EQ(blocks.blocks(0).originallocatedblock().locs(2).id().ipaddr(),
    //          "***********");
    // ASSERT_TRUE(blocks.blocks(0).has_replicapipeliner());
  }
}

TEST_F(DatanodeManagerTest, TestConstructLocatedBlocks) {
  // LF
  for (int i = 1; i <= 5; ++i) {
    auto proto = MockDatanode("10.10." + std::to_string(i) + ".1");
    cnetpp::base::IPAddress ip("10.10." + std::to_string(i) + ".1");
    dn_manager_->Register(proto.datanodeid(), &proto, ip);
  }
  // HY
  for (int i = 6; i <= 10; ++i) {
    auto proto = MockDatanode("10.4." + std::to_string(i) + ".1");
    cnetpp::base::IPAddress ip("10.4." + std::to_string(i) + ".1");
    dn_manager_->Register(proto.datanodeid(), &proto, ip);
  }

  {
    std::vector<std::vector<uint32_t>> storage;
    storage.emplace_back(std::vector<uint32_t>({1, 2, 3}));
    storage.emplace_back(std::vector<uint32_t>({2, 3, 4}));
    storage.emplace_back(std::vector<uint32_t>({3, 4, 5}));
    cloudfs::LocatedBlocksProto blocks_proto;
    blocks_proto.add_blocks();
    blocks_proto.add_blocks();
    blocks_proto.add_blocks();
    dn_manager_->ConstructLocatedBlocks(
        storage, kHotStoragePolicy, &blocks_proto);
    for (int block_id = 0; block_id < 3; ++block_id) {
      ASSERT_EQ(blocks_proto.blocks_size(), 3);
      for (int dn_id = 1; dn_id <= 3; ++dn_id) {
        ASSERT_EQ(blocks_proto.blocks(block_id).locs(dn_id - 1).id().hostname(),
                  "10.10." + std::to_string(block_id + dn_id) + ".1");
      }
    }
  }

  {
    std::vector<std::vector<uint32_t>> storage;
    storage.emplace_back(std::vector<uint32_t>({1, 2, 3}));
    storage.emplace_back(std::vector<uint32_t>({2, 6, 7}));
    storage.emplace_back(std::vector<uint32_t>({3, 8, 1}));
    ReadAdvice advice;
    cnetpp::base::IPAddress ip("**********");
    cloudfs::LocatedBlocksProto blocks_proto;
    blocks_proto.add_blocks();
    blocks_proto.add_blocks();
    blocks_proto.add_blocks();
    dn_manager_->ConstructLocatedSortedBlocks(storage,
                                              advice,
                                              kHotStoragePolicy,
                                              NetworkLocationInfo(ip),
                                              &blocks_proto);
    // LOG(INFO) << blocks_proto.ShortDebugString();
    // ASSERT_EQ(blocks_proto.blocks_size(), 3);
    // ASSERT_EQ(blocks_proto.blocks(0).locs(0).id().hostname(), "*********");
    // ASSERT_EQ(blocks_proto.blocks(0).locs(1).id().hostname(), "*********");
    // ASSERT_EQ(blocks_proto.blocks(0).locs(2).id().hostname(), "*********");

    // ASSERT_EQ(blocks_proto.blocks(1).locs_size(), 1);
    // ASSERT_EQ(blocks_proto.blocks(1).locs(0).id().hostname(), "*********");

    // ASSERT_EQ(blocks_proto.blocks(2).locs_size(), 2);
    // ASSERT_EQ(blocks_proto.blocks(2).locs(0).id().hostname(), "*********");
    // ASSERT_EQ(blocks_proto.blocks(2).locs(1).id().hostname(), "*********");
  }
}

TEST_F(DatanodeManagerTest, TestChooseTarget4NewForFastCopy) {
  auto d1 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d2 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d3 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d4 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d5 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d6 = RegisterMockDatanode("***********", StorageTypeProto::DISK);

  cnetpp::base::IPAddress writer("***********");
  cloudfs::LocatedBlockProto block;

  FLAGS_dfs_replication_min = 2;
  PlacementAdvice advice(kHotStoragePolicy);
  std::vector<std::string> faver_nodes;

  {
    faver_nodes.emplace_back("***********");
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(d1->id(), results[0]);
  }
  {
    faver_nodes.emplace_back("***********");
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(d1->id(), results[0]);
    ASSERT_EQ(d2->id(), results[1]);
  }
  {
    faver_nodes.emplace_back("***********");
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    // ASSERT_EQ(d1->id(), results[0]);
    // ASSERT_EQ(d2->id(), results[1]);
    // ASSERT_EQ(d3->id(), results[2]);
  }
}

TEST_F(DatanodeManagerTest, TestChooseTarget4NewForIgnoreLocal) {
  auto d1 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d2 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto d3 = RegisterMockDatanode("***********9", StorageTypeProto::DISK);
  auto d4 = RegisterMockDatanode("************", StorageTypeProto::DISK);

  FLAGS_dfs_replication_min = 3;
  FLAGS_enable_location_tag_by_rack_aware = true;
  FLAGS_enable_location_tag = false;

  {
    cloudfs::LocatedBlockProto block;
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    cnetpp::base::IPAddress writer("***********");
    std::vector<std::string> faver_nodes{"ignoreLocalRack:1"};
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "LF";
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    std::set<DatanodeID> result_set(results.begin(), results.end());
    ASSERT_TRUE(result_set.find(d2->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d3->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d4->id()) != result_set.end());
  }

  {
    cloudfs::LocatedBlockProto block;
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    cnetpp::base::IPAddress writer("***********");
    std::vector<std::string> faver_nodes{"ignoreLocalRack:1"};
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "LF";
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    std::set<DatanodeID> result_set(results.begin(), results.end());
    ASSERT_TRUE(result_set.find(d1->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d3->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d4->id()) != result_set.end());
  }

  {
    cloudfs::LocatedBlockProto block;
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    cnetpp::base::IPAddress writer("***********9");
    std::vector<std::string> faver_nodes{"ignoreLocalRack:1"};
    PlacementAdvice advice(kHotStoragePolicy);
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    std::set<DatanodeID> result_set(results.begin(), results.end());
    ASSERT_TRUE(result_set.find(d1->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d2->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d4->id()) != result_set.end());
  }

  {
    cloudfs::LocatedBlockProto block;
    DatanodeManager::RepeatedDatanodeInfo excluded;
    std::vector<DatanodeID> results;
    cnetpp::base::IPAddress writer("************");
    std::vector<std::string> faver_nodes{"ignoreLocalRack:1"};
    PlacementAdvice advice(kHotStoragePolicy);
    ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                              3,
                                              FLAGS_dfs_replication_min,
                                              1024,
                                              advice,
                                              NetworkLocationInfo(writer),
                                              faver_nodes,
                                              excluded,
                                              &block,
                                              &results));
    ASSERT_EQ(results.size(), 3);
    std::set<DatanodeID> result_set(results.begin(), results.end());
    ASSERT_TRUE(result_set.find(d1->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d2->id()) != result_set.end());
    ASSERT_TRUE(result_set.find(d3->id()) != result_set.end());
  }
}

TEST_F(DatanodeManagerTest, TestChooseTarget4NewWithInsufficientNodes) {
  RegisterMockDatanode("***********", StorageTypeProto::DISK);
  RegisterMockDatanode("***********", StorageTypeProto::DISK);

  cnetpp::base::IPAddress writer("***********");
  cloudfs::LocatedBlockProto block;
  std::vector<std::string> faver_nodes;
  DatanodeManager::RepeatedDatanodeInfo excluded;
  std::vector<DatanodeID> results;

  FLAGS_dfs_replication_min = 2;
  PlacementAdvice advice(kHotStoragePolicy);
  ASSERT_TRUE(dn_manager_->ChooseTarget4New(src,
                                            3,
                                            FLAGS_dfs_replication_min,
                                            1024,
                                            advice,
                                            NetworkLocationInfo(writer),
                                            faver_nodes,
                                            excluded,
                                            &block,
                                            &results));
  ASSERT_EQ(results.size(), 2);

  FLAGS_dfs_replication_min = 3;
  results.clear();
  excluded.Clear();

  ASSERT_FALSE(dn_manager_->ChooseTarget4New(src,
                                             3,
                                             FLAGS_dfs_replication_min,
                                             1024,
                                             advice,
                                             NetworkLocationInfo(writer),
                                             faver_nodes,
                                             excluded,
                                             &block,
                                             &results));
  ASSERT_EQ(results.size(), 0);

  results.clear();
  excluded.Clear();

  ASSERT_FALSE(dn_manager_->ChooseTarget4New(src,
                                             3,
                                             3,
                                             1024,
                                             advice,
                                             NetworkLocationInfo(writer),
                                             faver_nodes,
                                             excluded,
                                             &block,
                                             &results));
  ASSERT_EQ(results.size(), 0);
}

TEST_F(DatanodeManagerTest, TestChooseTarget4RecoverWithInsufficientNodes) {
  auto dn0 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto dn1 = RegisterMockDatanode("***********", StorageTypeProto::DISK);

  cnetpp::base::IPAddress writer("***********");
  cloudfs::LocatedBlockProto block;
  std::unordered_set<DatanodeInfoPtr> excluded;
  std::unordered_set<DatanodeInfoPtr> included;
  std::vector<DatanodeID> results;

  included.insert(dn0);
  included.insert(dn1);

  PlacementAdvice advice(kHotStoragePolicy);
  ASSERT_FALSE(dn_manager_->ChooseTarget4Recover(src,
                                                 1,
                                                 1024,
                                                 advice,
                                                 NetworkLocationInfo(dn0),
                                                 kDefaultFavored,
                                                 included,
                                                 &excluded,
                                                 &results));
}

TEST_F(DatanodeManagerTest, TestGetStoragePolicy) {
  ASSERT_EQ(dn_manager_->GetStoragePolicyByName("foobar"), nullptr);
  auto default_policy =
      dn_manager_->GetStoragePolicyById(kBlockStoragePolicyIdUnspecified);
  ASSERT_NE(default_policy, nullptr);
  ASSERT_EQ(default_policy->id(), kHotStoragePolicy);
}

TEST_F(DatanodeManagerTest, TestRefreshPhysiclaMachine) {
  FLAGS_datanode_machine_file = "datanode_machine_info";
  auto dn0 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto dn1 = RegisterMockDatanode("***********", StorageTypeProto::DISK);
  auto dn2 = RegisterMockDatanode("***********", StorageTypeProto::DISK);

  // Machine info file does not exist.
  dn_manager_->RefreshDatanodeMachineInfo();
  EXPECT_EQ(dn0->physical_machine(), "***********");
  EXPECT_EQ(dn1->physical_machine(), "***********");
  EXPECT_EQ(dn2->physical_machine(), "***********");

  // Machine info file is empty.
  std::ofstream machine_file(FLAGS_datanode_machine_file.c_str(),
                             std::ostream::app);
  machine_file << "";
  machine_file.flush();
  dn_manager_->RefreshDatanodeMachineInfo();
  EXPECT_EQ(dn0->physical_machine(), "***********");
  EXPECT_EQ(dn1->physical_machine(), "***********");
  EXPECT_EQ(dn2->physical_machine(), "***********");

  // Machine info file is correct.
  machine_file
      << "{\"***********\":\"**********\",\"***********\":\"**********\"}";
  machine_file.flush();
  dn_manager_->RefreshDatanodeMachineInfo();
  EXPECT_EQ(dn0->physical_machine(), "**********");
  EXPECT_EQ(dn1->physical_machine(), "**********");
  EXPECT_EQ(dn2->physical_machine(), "***********");
  remove(FLAGS_datanode_machine_file.c_str());
}
TEST_F(DatanodeManagerTest, TestRegisterLoadOneDB) {
  // prepare
  FLAGS_datanode_info_fg_dump_size_threshold = 0;
  FLAGS_datanode_info_fg_dump_time_threshold = 0;
  FLAGS_run_ut = false;

  // work
  std::string dn_ip = "***********";
  auto dn = RegisterMockDatanodeWithoutHB(dn_ip);
  auto dn_id = dn->id();

  // check
  DatanodeInfoEntryPB pb;
  dn->ToStorePB(&pb);

  DatanodeInfoEntryPB db_pb;
  auto scode = meta_storage_->GetDatanodeInfo(dn_id, &db_pb);

  ASSERT_TRUE(scode == StatusCode::kOK) << " scode=" << scode;
  ASSERT_TRUE(pb.ShortDebugString() == db_pb.ShortDebugString())
      << "\n"
         "pb.ShortDebugString()="
         "\n"
      << pb.ShortDebugString()
      << "\n"
         "db_pb.ShortDebugString()="
         "\n"
      << db_pb.ShortDebugString();
}

TEST_F(DatanodeManagerTest, TestStoreLoadOneDB) {
  // prepare
  std::string dn_ip = "***********";
  auto dn = RegisterMockDatanode(dn_ip, StorageTypeProto::DISK);
  auto dn_id = dn->id();

  DatanodeInfoEntryPB pb;
  dn->ToStorePB(&pb);

  // work
  SynchronizedClosure done;
  meta_storage_->InsertDatanodeInfoAsync(dn_id, pb, &done);
  done.Await();

  // check
  DatanodeInfoEntryPB db_pb;
  auto scode = meta_storage_->GetDatanodeInfo(dn_id, &db_pb);

  ASSERT_TRUE(scode == StatusCode::kOK) << " scode=" << scode;
  ASSERT_TRUE(pb.ShortDebugString() == db_pb.ShortDebugString())
      << "\n"
         "pb.ShortDebugString()="
         "\n"
      << pb.ShortDebugString()
      << "\n"
         "db_pb.ShortDebugString()="
         "\n"
      << db_pb.ShortDebugString();
}

TEST_F(DatanodeManagerTest, TestLoadAllDB) {
  // prepare
  std::vector<DatanodeInfoEntryPB> pb_list;
  for (auto i = 0; i < 10; ++i) {
    std::string ip = "10.10.10." + std::to_string(i);

    DatanodeInfoEntryPB pb;
    pb.set_internal_id(i + 1);
    pb.set_uuid("uuid" + ip);
    if (i == 0) {
      pb.set_admin_state(
          ::cloudfs::DatanodeInfoProto_AdminState_DECOMMISSIONED);
    } else {
      pb.set_admin_state(::cloudfs::DatanodeInfoProto_AdminState_NORMAL);
    }
    DatanodeIDProto address;
    address.set_ipaddr(ip);
    address.set_hostname("host" + ip);
    address.set_datanodeuuid("uuid" + ip);
    address.set_xferport(5080);
    address.set_infoport(5081);
    address.set_ipcport(5082);
    pb.mutable_address()->CopyFrom(address);
    pb.set_ip_address(ip);
    pb.set_nodezone_id("nodezone");
    // TODO(xiong): add storages in PB
    // pb.add_storages_uuid("storage");
    pb.set_is_stale(false);
    pb.set_version(0);
    auto storage = pb.mutable_storages()->Add();
    storage->set_storagetype(::cloudfs::StorageTypeProto::SSD);
    storage->set_storageuuid(UUID().ToString());
    storage->set_state(::cloudfs::DatanodeStorageProto_StorageState::
                           DatanodeStorageProto_StorageState_NORMAL);
    LOG(INFO) << "make pb=" << pb.ShortDebugString();

    pb_list.push_back(pb);
  }

  SynchronizedClosure done;
  meta_storage_->MultiInsertDatanodeInfoAsync(pb_list, &done);
  done.Await();

  // work
  dn_manager_->LoadFromDB();

  // check
  auto dns = dn_manager_->GetAllDatanodeInfo();
  std::reverse(dns.begin(), dns.end());
  ASSERT_EQ(pb_list.size(), dns.size());
  for (auto i = 0; i < dns.size(); ++i) {
    DatanodeInfoEntryPB pb_store;
    dns[i]->ToStorePB(&pb_store);

    ASSERT_EQ(pb_list[i].internal_id(), dns[i]->id());
    ASSERT_EQ(pb_list[i].uuid(), dns[i]->uuid());
    ASSERT_EQ(pb_list[i].address().ShortDebugString(),
              dns[i]->address().ShortDebugString());
    ASSERT_EQ(pb_list[i].ip_address(), dns[i]->ip().ToString());
    ASSERT_EQ(static_cast<uint8_t>(StorageType::SSD), dns[i]->storage_type());
    ASSERT_EQ(pb_list[i].ShortDebugString(), pb_store.ShortDebugString())
        << "\n"
           "pb_list[i].ShortDebugString()="
           "\n"
        << pb_list[i].ShortDebugString()
        << "\n"
           "pb_store.ShortDebugString()="
           "\n"
        << pb_store.ShortDebugString();

    // LoadFromDB will not fill maps like ip_to_id/host_to_id
    ASSERT_EQ(dn_manager_->GetDatanodeFromIp(pb_list[i].ip_address()), nullptr);
    ASSERT_EQ(dn_manager_->GetDatanodeFromSocket(pb_list[i].ip_address()), nullptr);
    ASSERT_EQ(dn_manager_->GetDatanodeFromSocket(pb_list[i].ip_address()), nullptr);

    // LoadFromDB will fill maps like uuid_to_id
    ASSERT_NE(dn_manager_->GetDatanodeFromId(pb_list[i].internal_id()), nullptr);
    ASSERT_NE(dn_manager_->GetDatanodeFromUuid(pb_list[i].uuid()), nullptr);
  }
}

TEST_F(DatanodeManagerTest, TestStoreAllDB) {
  // prepare
  std::vector<DatanodeID> dn_id_list;
  for (auto i = 0; i < 10; ++i) {
    std::string ip = "10.10.10." + std::to_string(i);

    auto dn = RegisterMockDatanodeWithoutHB(ip);
    dn_id_list.push_back(dn->id());
  }

  // work
  dn_manager_->DumpDatanodeInfo2DB();

  // check
  for (auto i = 0; i < dn_id_list.size(); ++i) {
    auto dn_id = dn_id_list[i];

    DatanodeInfoEntryPB pb_store;
    auto scode = meta_storage_->GetDatanodeInfo(dn_id, &pb_store);
    ASSERT_TRUE(scode == kOK) << " scode=" << scode;

    auto dn = dn_manager_->GetDatanodeFromId(dn_id);
    DatanodeInfoEntryPB pb;
    dn->ToStorePB(&pb);
    ASSERT_EQ(dn->id(), pb_store.internal_id());
    ASSERT_EQ(dn->uuid(), pb_store.uuid());
    ASSERT_EQ(dn->address().ShortDebugString(),
              pb_store.address().ShortDebugString());
    ASSERT_EQ(dn->ip().ToString(), pb_store.ip_address());
    ASSERT_EQ(pb.ShortDebugString(), pb_store.ShortDebugString())
        << "\n"
           "pb.ShortDebugString()="
           "\n"
        << pb.ShortDebugString()
        << "\n"
           "pb_store.ShortDebugString()="
           "\n"
        << pb_store.ShortDebugString();
  }
}

TEST_F(DatanodeManagerTest, TestSetDecommissionOnSchedule) {
  std::string uuid1 = UUID().ToString();
  std::string uuid2 = UUID().ToString();
  auto dn1 = MockDatanode("***********", uuid1);
  auto dn2 = MockDatanode("***********", uuid2);
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");

  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");

  ASSERT_FALSE(dn_manager_->IsDatanodeStale(1));
  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip1));

  auto all_dn_info = dn_manager_->GetAllDatanodeInfo();
  ASSERT_EQ(all_dn_info.size(), 1);
  auto dn_info = all_dn_info[0];
  ASSERT_FALSE(dn_info->IsDecommissionInProgress());

  ASSERT_TRUE(dn_info->SetDecommissionOnScheduleOn());
  ASSERT_FALSE(dn_info->SetDecommissionOnScheduleOn());

  ASSERT_TRUE(dn_info->SetDecommissionOnScheduleOff());
  ASSERT_FALSE(dn_info->SetDecommissionOnScheduleOff());
}

TEST_F(DatanodeManagerTest, TestDecommissioned) {
  std::string uuid1 = UUID().ToString();
  auto dn1 = MockDatanode("***********", uuid1);
  cnetpp::base::IPAddress ip1("***********");

  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  DatanodeManager::RepeatedDatanodeInfo datanodes;
  dn_manager_->GetDatanodeReport(
      DatanodeReportTypeProto::ALL, NetworkLocationInfo(ip1), &datanodes);
  ASSERT_EQ(datanodes.size(), 1);
  ASSERT_EQ(datanodes.Get(0).id().ipaddr(), "***********");
  auto info = dn_manager_->GetDatanodeFromId(1);

  ASSERT_FALSE(dn_manager_->IsDatanodeStale(1));
  ASSERT_FALSE(dn_manager_->IsDecommissionInProgress(ip1));

  dn_manager_->SetDecommissioning(1);
  ASSERT_FALSE(dn_manager_->IsDatanodeStale(1));
  ASSERT_TRUE(dn_manager_->IsDecommissionInProgress(ip1));

  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_datanode_keep_alive_timeout_sec * 2));
  ASSERT_TRUE(dn_manager_->SetDecommissioned(1, false).IsOK());

  ASSERT_FALSE(info->IsFunctional());
  ASSERT_FALSE(info->IsAlive());
  ASSERT_TRUE(info->IsDecommissioned());

  // Decommissioned DN is not allowed to register
  ASSERT_FALSE(dn_manager_->Register(dn1.datanodeid(), &dn1, ip1).IsOK());
}

TEST_F(DatanodeManagerTest, TestGetDatanodeInfo) {
  std::string uuid1 = UUID().ToString();
  std::string uuid2 = UUID().ToString();
  std::string uuid3 = UUID().ToString();
  auto dn1 = MockDatanode("***********", uuid1);
  auto dn2 = MockDatanode("***********", uuid2);
  auto dn3 = MockDatanode("***********", uuid3);
  cnetpp::base::IPAddress ip1("***********");
  cnetpp::base::IPAddress ip2("***********");
  cnetpp::base::IPAddress ip3("***********");

  dn_manager_->Register(dn1.datanodeid(), &dn1, ip1);
  dn_manager_->Register(dn2.datanodeid(), &dn2, ip2);
  dn_manager_->Register(dn3.datanodeid(), &dn3, ip3);

  // Get All dn
  std::vector<DatanodeInfoPtr> all_dns =
      dn_manager_->GetDatanodeInfo([](DatanodeInfoPtr dn_ptr) { return true; });
  ASSERT_EQ(3, all_dns.size());
  all_dns[0]->SetDecommissioned();
  all_dns[1]->set_force_stale(true);
  ASSERT_TRUE(all_dns[0]->IsDecommissioned());
  ASSERT_TRUE(all_dns[1]->IsStale());
  for (auto dn : all_dns) {
    dn->CheckWriteable();
  }

  // DN1 decommissioned
  {
    std::vector<DatanodeInfoPtr> decommission_dns =
        dn_manager_->GetDatanodeInfo(
            [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsDecommissioned(); });
    ASSERT_EQ(1, decommission_dns.size());
  }

  // DN2 stale
  {
    std::vector<DatanodeInfoPtr> stale_dns = dn_manager_->GetDatanodeInfo(
        [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsStale(); });
    ASSERT_EQ(1, stale_dns.size());
  }

  // DN2, DN3 alive
  {
    std::vector<DatanodeInfoPtr> live_dns = dn_manager_->GetDatanodeInfo(
        [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsAlive(); });
    ASSERT_EQ(2, live_dns.size());
  }
  // DN2, DN3 Capable writeable
  {
    std::vector<DatanodeInfoPtr> capable_writeable_dns = dn_manager_->GetDatanodeInfo(
        [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsCapableWrite(); });
    ASSERT_EQ(2, capable_writeable_dns.size());
  }

  // DN3 writeable
  {
    std::vector<DatanodeInfoPtr> writeable_dns = dn_manager_->GetDatanodeInfo(
        [](DatanodeInfoPtr dn_ptr) { return dn_ptr->IsWriteable(); });
    ASSERT_EQ(1, writeable_dns.size());
  }
}

TEST_F(DatanodeManagerTest, AddReplicaIsCached) {
  // nullptr
  cloudfs::LocatedBlockProto* null_block = nullptr;
  dn_manager_->AddReplicaIsCached(null_block);

  Block blk = Block(1, 1024, 17104101782323L);
  // block without b
  cloudfs::LocatedBlockProto block;
  dn_manager_->AddReplicaIsCached(&block);
  // block without block id
  auto b = block.mutable_b();
  dn_manager_->AddReplicaIsCached(&block);
  b->set_blockid(blk.id);
  dn_manager_->AddReplicaIsCached(&block);
  block.Clear();

  std::vector<DatanodeID> dn_id = {1};
  block_manager_->AddBlock(blk,
                           kLastReservedINodeId + 1,
                           kRootINodeId,
                           1,
                           cloudfs::DATANODE_BLOCK,
                           dn_id,
                           BlockUCState::kUnderConstruction);

  b = block.mutable_b();
  b->set_blockid(blk.id);
  dn_manager_->AddReplicaIsCached(&block);
  ASSERT_EQ(0, block.iscached_size());
}

}  // namespace
}  // namespace dancenn
