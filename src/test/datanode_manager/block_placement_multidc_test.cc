// Copyright 2017 Liyuan Lei <<EMAIL>>

#include <gtest/gtest.h>

#include <chrono>
#include <iostream>
#include <thread>
#include <tuple>
#include <vector>

#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/storage_policy.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_tolerate_interval_misses_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_string(all_datacenters);
DECLARE_bool(enable_location_tag);
DECLARE_bool(block_manager_allow_transfer_persisted_blocks);

using cloudfs::StorageTypeProto;

namespace dancenn {

namespace {
// tuple to define a datanode: (id, dc, rack, ip)
typedef std::tuple<DatanodeID, std::string, std::string, std::string, uint64_t>
    DNDesc;
typedef std::vector<DNDesc> DNDescList;
typedef std::unordered_map<DatanodeID, DatanodeInfoPtr> Testbed;

class BlockPlacementMultiDCTest : public testing::Test {
 public:
  void SetUp() override {
    metrics_.reset(new DatanodeManagerMetrics(nullptr));
    FLAGS_all_datacenters = "DC1,DC2,DC3";
    FLAGS_run_ut = true;
    FLAGS_datanode_stale_interval_ms = 600 * 1000;
    FLAGS_enable_location_tag = false;

    bp_ = std::make_unique<BlockPlacementMultiDC>(metrics_.get());
  }

  void TearDown() override {
    dn_dc_.clear();
    dn_rack_.clear();
  }

  const Testbed AddNodes(const DNDescList& descs) {
    Testbed testbed;
    for (auto it = descs.begin(); it != descs.end(); it++) {
      DatanodeID id;
      std::string dc, rack, ip;
      uint64_t remaining;
      std::tie(id, dc, rack, ip, remaining) = *it;
      cnetpp::base::IPAddress ip_addr(ip);
      DatanodeIDProto addr;
      DatanodeInfoPtr dn = new DatanodeInfo(id, addr, ip_addr);
      HeartbeatRequestProto request;
      GetDiskRequest(&request, remaining);
      dn->CheckAndUpdateHeartbeat();
      dn->UpdateStorage(request);
      dn->SetLocation4Test(dc, rack);
      bp_->AddDatanode(dn);
      testbed.insert(std::make_pair(id, dn));
      dn_dc_.insert(std::make_pair(id, dc));
      dn_rack_.insert(std::make_pair(id, rack));
      auto itr = rack_node_num_.find(rack);
      if (itr == rack_node_num_.end()) {
        rack_node_num_.emplace(rack, 1);
      } else {
        itr->second++;
      }
    }
    return testbed;
  }

 protected:
  void GetDiskRequest(HeartbeatRequestProto* request, uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid("disk");
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
  }

  const std::string GetDCFromDatanodeID(DatanodeID dn_id) {
    auto it = dn_dc_.find(dn_id);
    if (it == dn_dc_.end()) {
      return "";
    }

    return it->second;
  }

  const std::string GetRackFromDatanodeID(DatanodeID dn_id) {
    auto it = dn_rack_.find(dn_id);
    if (it == dn_rack_.end()) {
      return "";
    }

    return it->second;
  }

  int GetRackNodeNum(const std::string& rack) { return rack_node_num_[rack]; }

  std::unique_ptr<DatanodeManagerMetrics> metrics_;
  std::unique_ptr<BlockPlacement> bp_;
  std::unordered_map<DatanodeID, std::string> dn_dc_;
  std::unordered_map<DatanodeID, std::string> dn_rack_;
  std::unordered_map<std::string, int> rack_node_num_;

  std::string src;
  const std::string DEFAULT_IP_STR = "***********";
};

bool CheckResultMulti(const std::unordered_set<DatanodeInfoPtr>& expected,
                      const std::vector<DatanodeInfoPtr>& results) {
  for (const auto& dn : results) {
    if (expected.find(dn) == expected.end()) {
      return false;
    }
  }
  return true;
}

TEST_F(BlockPlacementMultiDCTest, TestAddDatanode) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC2", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC3", "rack3", DEFAULT_IP_STR, 1024)};
  AddNodes(topology);

  std::string str;
  bp_->ListAll(&str);
  ASSERT_NE(str.find("DC3: rack3: 3"), std::string::npos);
  ASSERT_NE(str.find("DC2: rack2: 2"), std::string::npos);
  ASSERT_NE(str.find("DC1: rack1: 1"), std::string::npos);
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewFromMultipDC) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC2", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC3", "rack5", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  PlacementAdvice advice;
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC1,DC2";

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn1),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn4),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
}

TEST_F(BlockPlacementMultiDCTest,
       TestChooseTarget4NewByCentralizeFromTripleDc) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC2", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC3", "rack5", DEFAULT_IP_STR, 1024),
      std::make_tuple(6, "DC3", "rack6", DEFAULT_IP_STR, 1024),
      std::make_tuple(7, "DC3", "rack7", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn5 = testbed.find(5)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kCentralizePolicy;
  advice.enforce_dc = "DC3";

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn1),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  excluded.clear();
  results.clear();
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn5),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    2,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn3),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 2);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn5),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC3");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn5),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC3");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    2,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(nullptr),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 2);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC3";
  auto multip_dc_policy = dynamic_cast<BlockPlacementMultiDC*>(bp_.get());
  multip_dc_policy->SetDCList("blacklist", "DC3");
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     1,
                                     1024,
                                     advice,
                                     NetworkLocationInfo(dn5),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_EQ(results.size(), 0);
}

TEST_F(BlockPlacementMultiDCTest,
       TestChooseTarget4NewByDistributeFromTripleDc) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC2", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(6, "DC3", "rack5", DEFAULT_IP_STR, 1024),
      std::make_tuple(7, "DC3", "rack6", DEFAULT_IP_STR, 1024),
      std::make_tuple(8, "DC3", "rack7", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn6 = testbed.find(6)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC2,DC3";

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn1),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
  excluded.clear();
  results.clear();
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    2,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn3),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 2);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC1,DC2,DC3";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC2,DC3";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    4,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 4);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC1,DC2,DC3";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    4,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 4);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC2,DC3";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    5,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 5);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[4]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC1,DC2,DC3";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    5,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 5);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[4]->id()), "DC2");

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC2,DC3";
  auto multip_dc_policy = dynamic_cast<BlockPlacementMultiDC*>(bp_.get());
  multip_dc_policy->SetDCList("blacklist", "DC3");
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    2,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 2);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");

  excluded.clear();
  results.clear();
  multip_dc_policy->SetDCList("blacklist", "");

  multip_dc_policy->SetDCList("majority", "DC1");
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  multip_dc_policy->SetDCList("majority", "DC3");
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    5,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn6),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 5);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC3");
  ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");
  ASSERT_EQ(GetDCFromDatanodeID(results[4]->id()), "DC2");
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithOutsideClient) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 1024)};

  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;

  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC1,DC2";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo("DC1"),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo("DC3"),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
  ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");

  excluded.clear();
  results.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(nullptr),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewInsufficientNodesInDC) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC2", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC2", "rack3", DEFAULT_IP_STR, 1024)};

  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn2 = testbed.find(2)->second,
       dn3 = testbed.find(3)->second;

  std::unordered_set<DatanodeInfoPtr> expected = {dn1, dn2, dn3};
  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;

  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC1,DC2";
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     3,
                                     1024,
                                     advice,
                                     NetworkLocationInfo(dn1),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(CheckResultMulti(expected, results));
  ASSERT_TRUE(results[0] == dn1);

  excluded.clear();
  results.clear();

  advice.enforce_dc = "DC2,DC1";
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    advice,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(CheckResultMulti(expected, results));
  ASSERT_TRUE(results[0] == dn2);
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4Recover) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", "***********0", 1024),
      std::make_tuple(2, "DC1", "rack1", "************", 2048),
      std::make_tuple(3, "DC1", "rack4", "************", 102400),
      std::make_tuple(6, "DC1", "rack4", "10.10.10.211", 102400),
      std::make_tuple(4, "DC2", "rack2", "************", 1),
      std::make_tuple(5, "DC2", "rack3", "************", 1024),
  };
  auto testbed = AddNodes(topology);

  auto dn1 = testbed.find(1)->second;
  auto dn2 = testbed.find(2)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn2, dn4};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);

    results.clear();
    excluded.clear();

    write_dn = dn3;
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn4, dn5};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          3,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 3);
    for (auto dn : results) {
      ASSERT_EQ("DC1", GetDCFromDatanodeID(dn->id()));
    }
  }
}

TEST_F(BlockPlacementMultiDCTest,
       TestChooseTarget4RecoverByCentralizeFromTripleDc) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack1", DEFAULT_IP_STR, 2048),
      std::make_tuple(3, "DC1", "rack2", DEFAULT_IP_STR, 102400),
      std::make_tuple(4, "DC1", "rack3", DEFAULT_IP_STR, 102400),
      std::make_tuple(5, "DC1", "rack4", DEFAULT_IP_STR, 102400)};
  auto testbed = AddNodes(topology);

  auto dn1 = testbed.find(1)->second;
  auto dn2 = testbed.find(2)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn2, dn3, dn4};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC1";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);

    results.clear();
    excluded.clear();

    write_dn = dn3;
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn2, dn3};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC1";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          2,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 2);
    for (auto dn : results) {
      ASSERT_EQ("DC1", GetDCFromDatanodeID(dn->id()));
    }
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn4};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC1";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          3,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 3);
    for (auto dn : results) {
      ASSERT_EQ("DC1", GetDCFromDatanodeID(dn->id()));
    }
  }
}

TEST_F(BlockPlacementMultiDCTest,
       TestChooseTarget4RecoverByDistributeFromTripleDc) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack1", DEFAULT_IP_STR, 2048),
      std::make_tuple(3, "DC1", "rack2", DEFAULT_IP_STR, 102400),
      std::make_tuple(4, "DC2", "rack3", DEFAULT_IP_STR, 102400),
      std::make_tuple(5, "DC2", "rack4", DEFAULT_IP_STR, 102400)};
  auto testbed = AddNodes(topology);

  auto dn1 = testbed.find(1)->second;
  auto dn2 = testbed.find(2)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn2, dn3, dn4};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);

    results.clear();
    excluded.clear();

    write_dn = dn3;
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          1,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(results[0], dn5);
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn2, dn3};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          2,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 2);
    for (auto dn : results) {
      ASSERT_EQ("DC2", GetDCFromDatanodeID(dn->id()));
    }
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::unordered_set<DatanodeInfoPtr> included = {dn1, dn4};
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                          3,
                                          1024,
                                          advice,
                                          NetworkLocationInfo(write_dn),
                                          kDefaultFavored,
                                          included,
                                          &excluded,
                                          &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ("DC1", GetDCFromDatanodeID(results[0]->id()));
    ASSERT_EQ("DC1", GetDCFromDatanodeID(results[1]->id()));
    ASSERT_EQ("DC2", GetDCFromDatanodeID(results[2]->id()));
  }
}

TEST_F(BlockPlacementMultiDCTest, TestNumRacks) {
  DatanodeInfoPtr dn1 = new DatanodeInfo(
      1, DatanodeIDProto(), cnetpp::base::IPAddress("***********0"));
  DatanodeInfoPtr dn2 = new DatanodeInfo(
      2, DatanodeIDProto(), cnetpp::base::IPAddress("************"));
  DatanodeInfoPtr dn3 = new DatanodeInfo(
      3, DatanodeIDProto(), cnetpp::base::IPAddress("************"));

  bp_->AddDatanode(dn1);
  ASSERT_EQ(bp_->NumRacks(), 1);
  ASSERT_EQ(bp_->HasBeenMultiRack(), false);

  bp_->AddDatanode(dn2);
  ASSERT_EQ(bp_->NumRacks(), 2);
  ASSERT_EQ(bp_->HasBeenMultiRack(), true);

  bp_.reset();
  bp_ = std::make_unique<BlockPlacementMultiDC>(metrics_.get());

  bp_->AddDatanode(dn1);
  ASSERT_EQ(bp_->NumRacks(), 1);
  ASSERT_EQ(bp_->HasBeenMultiRack(), false);

  bp_->AddDatanode(dn2);
  ASSERT_EQ(bp_->NumRacks(), 2);
  ASSERT_EQ(bp_->HasBeenMultiRack(), true);
}

TEST_F(BlockPlacementMultiDCTest, TestChooseReplicaToDelete) {
  // avoid impact
  FLAGS_block_manager_allow_transfer_persisted_blocks = false;

  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", "***********0", 1024),
      std::make_tuple(2, "DC1", "rack1", "************", 2048),
      std::make_tuple(3, "DC1", "rack4", "************", 102400),
      std::make_tuple(4, "DC2", "rack2", "************", 1),
      std::make_tuple(5, "DC2", "rack3", "************", 1024),
  };
  auto testbed = AddNodes(topology);

  auto dn1 = testbed.find(1)->second;
  auto dn2 = testbed.find(2)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;

  std::unordered_set<DatanodeInfoPtr> more_than_one = {dn1, dn2, dn3};
  std::unordered_set<DatanodeInfoPtr> exactly_one = {dn4};

  PlacementAdvice distribute(kDistributePolicy, "DC2,DC1");
  auto multip_dc_policy = dynamic_cast<BlockPlacementMultiDC*>(bp_.get());
  {
    DatanodeInfoPtr chosen = bp_->ChooseReplicaToDelete(
        Block{0, 1, 14444444}, 3, more_than_one, exactly_one, distribute);
    ASSERT_EQ(chosen, dn1);
  }

  {
    multip_dc_policy->SetDCList("blacklist", "DC1");
    DatanodeInfoPtr chosen = bp_->ChooseReplicaToDelete(
        Block{0, 1, 14444444}, 3, more_than_one, exactly_one, distribute);
    ASSERT_EQ(chosen, dn1);
    multip_dc_policy->SetDCList("blacklist", "");
  }

  {
    multip_dc_policy->SetDCList("majority", "DC1");
    std::unordered_set<DatanodeInfoPtr> more_than_one;
    std::unordered_set<DatanodeInfoPtr> exactly_one = {dn1, dn3, dn4, dn5};
    DatanodeInfoPtr chosen = bp_->ChooseReplicaToDelete(
        Block{0, 1, 14444444}, 3, more_than_one, exactly_one, distribute);
    ASSERT_EQ(GetDCFromDatanodeID(chosen->id()), "DC2");
    multip_dc_policy->SetDCList("majority", "");
  }

  {
    distribute.enforce_dc = "DC1,DC2";
    multip_dc_policy->SetDCList("majority", "DC1");
    std::unordered_set<DatanodeInfoPtr> more_than_one;
    std::unordered_set<DatanodeInfoPtr> exactly_one = {dn1, dn3, dn4, dn5};
    DatanodeInfoPtr chosen = bp_->ChooseReplicaToDelete(
        Block{0, 1, 14444444}, 3, more_than_one, exactly_one, distribute);
    ASSERT_EQ(GetDCFromDatanodeID(chosen->id()), "DC2");
    multip_dc_policy->SetDCList("majority", "");
  }

  {
    auto origin_misses_sec = FLAGS_datanode_tolerate_interval_misses_sec;
    FLAGS_datanode_tolerate_interval_misses_sec = 1;
    dn3->UpdateHeartbeat(true);
    std::this_thread::sleep_for(std::chrono::milliseconds(
        (FLAGS_datanode_tolerate_interval_misses_sec)*1000 + 10));
    dn1->UpdateHeartbeat(true);
    dn2->UpdateHeartbeat(true);
    DatanodeInfoPtr chosen = bp_->ChooseReplicaToDelete(
        Block{0, 1, 14444444}, 3, more_than_one, exactly_one, distribute);
    ASSERT_EQ(chosen, dn3);
    FLAGS_datanode_tolerate_interval_misses_sec = origin_misses_sec;
    dn3->UpdateHeartbeat(true);
    dn4->UpdateHeartbeat(true);
  }
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithCentralizePolicy) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC1";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    for (auto& it : results) {
      ASSERT_EQ(GetDCFromDatanodeID(it->id()), "DC1");
    }
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC2";
    ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                       3,
                                       1024,
                                       advice,
                                       NetworkLocationInfo(write_dn),
                                       kDefaultFavored,
                                       &excluded,
                                       &results));
    ASSERT_EQ(results.size(), 2);
    for (auto& it : results) {
      ASSERT_EQ(GetDCFromDatanodeID(it->id()), "DC2");
    }
  }
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithDistributePolicy) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 1024)};

  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC1";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC1";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      2,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 2);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC1";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      4,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));

    ASSERT_EQ(results.size(), 4);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      5,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 5);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[4]->id()), "DC2");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC1";
    ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                       5,
                                       1024,
                                       advice,
                                       NetworkLocationInfo(write_dn),
                                       kDefaultFavored,
                                       &excluded,
                                       &results));

    ASSERT_EQ(results.size(), 5);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[3]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[4]->id()), "DC1");
  }
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithFavoredNodes) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 10240),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 102400),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024000),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 10240000)};

  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second;
  auto dn2 = testbed.find(2)->second;
  auto dn3 = testbed.find(3)->second;
  auto dn4 = testbed.find(4)->second;
  auto dn5 = testbed.find(5)->second;
  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC1,DC2";

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn3;
    std::vector<DatanodeInfoPtr> favored = {dn1, dn2, dn4};
    sort(favored.begin(), favored.end());

    for (int i = 0; i < 10; i++) {
      excluded.clear();
      results.clear();
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        favored,
                                        &excluded,
                                        &results));
      ASSERT_EQ(results.size(), 3);
      sort(results.begin(), results.end());
      ASSERT_EQ(favored, results);
    }
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    std::vector<DatanodeInfoPtr> favored = {dn1, dn2, dn3};
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      favored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    std::unordered_map<std::string, int> dc_dist;
    for (auto it : results) {
      dc_dist[GetDCFromDatanodeID(it->id())] += 1;
    }
    ASSERT_EQ(dc_dist["DC2"], 1);
    ASSERT_EQ(dc_dist["DC1"], 2);
  }
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithGlobalBlacklist) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 1024),
      std::make_tuple(6, "DC3", "rack6", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  auto multip_dc_policy = dynamic_cast<BlockPlacementMultiDC*>(bp_.get());
  ASSERT_FALSE(multip_dc_policy->SetDCList("blacklist", "3"));
  ASSERT_FALSE(multip_dc_policy->SetDCList("blacklist", "0,3,2"));
  multip_dc_policy->SetDCList("blacklist", "DC1");

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kCentralizePolicy;
    advice.enforce_dc = "DC1";
    ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                       3,
                                       1024,
                                       advice,
                                       NetworkLocationInfo(write_dn),
                                       kDefaultFavored,
                                       &excluded,
                                       &results));
    ASSERT_EQ(results.size(), 0);

    excluded.clear();
    results.clear();

    advice.enforce_dc = "DC2";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      2,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 2);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC3";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC2");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC3");
  }

  multip_dc_policy->SetDCList("blacklist", "DC2");

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC3";
    ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                       3,
                                       1024,
                                       advice,
                                       NetworkLocationInfo(write_dn),
                                       kDefaultFavored,
                                       &excluded,
                                       &results));
    ASSERT_EQ(results.size(), 1);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC3");
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC3";
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(write_dn),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    ASSERT_EQ(GetDCFromDatanodeID(results[0]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[1]->id()), "DC1");
    ASSERT_EQ(GetDCFromDatanodeID(results[2]->id()), "DC3");
  }

  multip_dc_policy->SetDCList("blacklist", "");
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewWithMajorityDC) {
  DNDescList topology = {
      std::make_tuple(1, "DC1", "rack1", DEFAULT_IP_STR, 1024),
      std::make_tuple(2, "DC1", "rack2", DEFAULT_IP_STR, 1024),
      std::make_tuple(3, "DC1", "rack3", DEFAULT_IP_STR, 1024),
      std::make_tuple(4, "DC2", "rack4", DEFAULT_IP_STR, 1024),
      std::make_tuple(5, "DC2", "rack5", DEFAULT_IP_STR, 1024),
      std::make_tuple(6, "DC3", "rack6", DEFAULT_IP_STR, 1024),
      std::make_tuple(7, "DC3", "rack7", DEFAULT_IP_STR, 1024)};
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  auto multip_dc_policy = dynamic_cast<BlockPlacementMultiDC*>(bp_.get());
  ASSERT_FALSE(multip_dc_policy->SetDCList("majority", "3"));
  ASSERT_FALSE(multip_dc_policy->SetDCList("majority", "0,3,2"));

  {
    multip_dc_policy->SetDCList("majority", "DC1");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC1"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC1,DC2,DC3");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC1"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC2,DC3,DC1");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC2"], 2);
      ASSERT_EQ(dc_replicas["DC1"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC3,DC2,DC1");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC3";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC3"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC3");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn1;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC2,DC3";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC3"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC1,DC2,DC3");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 3);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC1"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC1,DC2,DC3");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        2,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 2);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC1"], 1);
      ASSERT_EQ(dc_replicas["DC2"], 1);
      excluded.clear();
      results.clear();
    }
  }

  {
    multip_dc_policy->SetDCList("majority", "DC1,DC2,DC3");
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    auto write_dn = dn4;
    PlacementAdvice advice(kHotStoragePolicy);
    advice.geograph = kDistributePolicy;
    advice.enforce_dc = "DC1,DC2";

    for (int i = 0; i < 10; i++) {
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        4,
                                        1024,
                                        advice,
                                        NetworkLocationInfo(write_dn),
                                        kDefaultFavored,
                                        &excluded,
                                        &results));

      ASSERT_EQ(results.size(), 4);
      std::unordered_map<std::string, int> dc_replicas;
      for (auto& it : results) {
        dc_replicas[GetDCFromDatanodeID(it->id())]++;
      }
      ASSERT_EQ(dc_replicas["DC1"], 2);
      ASSERT_EQ(dc_replicas["DC2"], 2);
      excluded.clear();
      results.clear();
    }
  }
  multip_dc_policy->SetDCList("majority", "");
}

TEST_F(BlockPlacementMultiDCTest, TestChooseTarget4NewUniformDistribution) {
  DNDescList topology = {
      std::make_tuple(0, "DC1", "a908900", "*************", 1024),
      std::make_tuple(1, "DC1", "a908a02", "**************", 1024),
      std::make_tuple(2, "DC1", "a908903", "**************", 1024),
      std::make_tuple(3, "DC1", "a15da03", "*************", 1024),
      std::make_tuple(4, "DC1", "a908a01", "**************", 1024),
      std::make_tuple(5, "DC2", "a0c9402", "*************", 1024),
      std::make_tuple(6, "DC1", "a17ad01", "************", 1024),
      std::make_tuple(7, "DC1", "a15da03", "*************", 1024),
      std::make_tuple(8, "DC2", "a0c9601", "************", 1024),
      std::make_tuple(9, "DC1", "a908803", "**************", 1024),
      std::make_tuple(10, "DC1", "a908803", "**************", 1024),
      std::make_tuple(11, "DC1", "a15d101", "*************", 1024),
      std::make_tuple(12, "DC1", "a17ac02", "10.23.172.155", 1024),
      std::make_tuple(13, "DC1", "a17ad01", "10.23.173.98", 1024),
      std::make_tuple(14, "DC1", "a17ae02", "10.23.174.151", 1024),
      std::make_tuple(15, "DC1", "a908901", "10.144.137.88", 1024),
      std::make_tuple(16, "DC1", "a908a02", "10.144.138.130", 1024),
      std::make_tuple(17, "DC1", "a908903", "10.144.137.197", 1024),
      std::make_tuple(18, "DC1", "a17ac00", "10.23.172.15", 1024),
      std::make_tuple(19, "DC1", "a908903", "10.144.137.195", 1024),
      std::make_tuple(20, "DC1", "a908a01", "10.144.138.100", 1024),
      std::make_tuple(21, "DC1", "a17ae03", "10.23.174.215", 1024),
      std::make_tuple(22, "DC1", "a908901", "10.144.137.93", 1024),
      std::make_tuple(23, "DC1", "a908900", "10.144.137.29", 1024),
      std::make_tuple(24, "DC1", "a17ae00", "10.23.174.34", 1024),
      std::make_tuple(25, "DC1", "a15da03", "10.21.218.229", 1024),
      std::make_tuple(26, "DC1", "a908902", "10.144.137.157", 1024),
      std::make_tuple(27, "DC1", "a15cf03", "10.21.207.212", 1024),
      std::make_tuple(28, "DC1", "a908900", "10.144.137.31", 1024),
      std::make_tuple(29, "DC1", "a908900", "10.144.137.18", 1024),
      std::make_tuple(30, "DC1", "a908902", "10.144.137.158", 1024),
      std::make_tuple(31, "DC1", "a15d001", "10.21.208.86", 1024),
      std::make_tuple(32, "DC1", "a908900", "10.144.137.13", 1024),
      std::make_tuple(33, "DC1", "a908900", "10.144.137.46", 1024),
      std::make_tuple(34, "DC2", "a0c9400", "10.12.148.27", 1024),
      std::make_tuple(35, "DC1", "a908901", "10.144.137.98", 1024),
      std::make_tuple(36, "DC1", "a908903", "10.144.137.214", 1024),
      std::make_tuple(37, "DC2", "a0c9400", "10.12.148.31", 1024),
      std::make_tuple(38, "DC1", "a18ba01", "10.24.186.88", 1024),
      std::make_tuple(39, "DC1", "a17ac02", "10.23.172.146", 1024),
      std::make_tuple(40, "DC1", "a17ad02", "10.23.173.132", 1024),
      std::make_tuple(41, "DC2", "a0cad00", "10.12.173.36", 1024),
      std::make_tuple(42, "DC2", "a0c9901", "10.12.153.78", 1024),
      std::make_tuple(43, "DC2", "a0c9500", "10.12.149.24", 1024),
      std::make_tuple(44, "DC2", "a0c9501", "10.12.149.81", 1024),
      std::make_tuple(45, "DC2", "a0c9902", "10.12.153.167", 1024),
      std::make_tuple(46, "DC2", "a0cab02", "10.12.171.135", 1024),
      std::make_tuple(47, "DC2", "a0cab02", "10.12.171.144", 1024),
      std::make_tuple(48, "DC2", "a0cac02", "10.12.172.136", 1024),
      std::make_tuple(49, "DC2", "a0e9503", "10.14.149.236", 1024),
      std::make_tuple(50, "DC2", "a0c9601", "10.12.150.73", 1024),
      std::make_tuple(51, "DC2", "a0cac01", "10.12.172.77", 1024),
      std::make_tuple(52, "DC2", "a0e9700", "10.14.151.44", 1024),
      std::make_tuple(53, "DC2", "a0ca401", "10.12.164.79", 1024),
      std::make_tuple(54, "DC2", "a0ca503", "10.12.165.227", 1024),
      std::make_tuple(55, "DC2", "a0ca403", "10.12.164.225", 1024),
      std::make_tuple(56, "DC2", "a0ca402", "10.12.164.135", 1024),
      std::make_tuple(57, "DC2", "a0e9102", "10.14.145.143", 1024),
      std::make_tuple(58, "DC2", "a0caa00", "10.12.170.22", 1024),
      std::make_tuple(59, "DC2", "a0cad03", "10.12.173.213", 1024),
      std::make_tuple(60, "DC2", "a0c9701", "10.12.151.75", 1024),
      std::make_tuple(61, "DC2", "a0cac02", "10.12.172.150", 1024),
      std::make_tuple(62, "DC2", "a0c9603", "10.12.150.201", 1024),
      std::make_tuple(63, "DC2", "a0e9103", "10.14.145.216", 1024),
      std::make_tuple(64, "DC2", "a0c9502", "10.12.149.146", 1024),
      std::make_tuple(65, "DC2", "a0cad00", "10.12.173.29", 1024),
      std::make_tuple(66, "DC2", "a0cab02", "10.12.171.143", 1024),
      std::make_tuple(67, "DC2", "a0cab00", "10.12.171.49", 1024),
      std::make_tuple(68, "DC2", "a0c9701", "10.12.151.99", 1024),
      std::make_tuple(69, "DC2", "a0c9301", "10.12.147.98", 1024),
      std::make_tuple(70, "DC2", "a0e9b01", "10.14.155.101", 1024),
      std::make_tuple(71, "DC2", "a0e9502", "10.14.149.161", 1024),
      std::make_tuple(72, "DC2", "a0c9900", "10.12.153.39", 1024),
      std::make_tuple(73, "DC2", "a0e9a03", "10.14.154.211", 1024),
      std::make_tuple(74, "DC2", "a0e9a00", "10.14.154.22", 1024),
      std::make_tuple(75, "DC2", "a0c9802", "10.12.152.151", 1024),
      std::make_tuple(76, "DC2", "a0c9401", "10.12.148.98", 1024),
      std::make_tuple(77, "DC2", "a0c9901", "10.12.153.106", 1024),
      std::make_tuple(78, "DC2", "a0e9000", "10.14.144.40", 1024),
      std::make_tuple(79, "DC2", "a0c9402", "10.12.148.140", 1024),
      std::make_tuple(80, "DC2", "a0e9201", "10.14.146.81", 1024),
      std::make_tuple(81, "DC2", "a0e9902", "10.14.153.135", 1024),
      std::make_tuple(82, "DC2", "a0c9503", "10.12.149.233", 1024),
      std::make_tuple(83, "DC2", "a0c9701", "10.12.151.70", 1024),
      std::make_tuple(84, "DC2", "a0cac01", "10.12.172.81", 1024),
      std::make_tuple(85, "DC2", "a0c9400", "10.12.148.42", 1024),
      std::make_tuple(86, "DC2", "a0c9501", "10.12.149.103", 1024),
      std::make_tuple(87, "DC2", "a0c9602", "10.12.150.132", 1024),
      std::make_tuple(88, "DC2", "a0c9802", "10.12.152.169", 1024),
      std::make_tuple(89, "DC2", "a0c9801", "10.12.152.92", 1024),
      std::make_tuple(90, "DC2", "a0c9900", "10.12.153.15", 1024),
      std::make_tuple(91, "DC2", "a0cac02", "10.12.172.140", 1024),
      std::make_tuple(92, "DC2", "a0e9003", "10.14.144.211", 1024),
      std::make_tuple(93, "DC2", "a0cac00", "10.12.172.28", 1024),
      std::make_tuple(94, "DC2", "a0cac00", "10.12.172.23", 1024),
      std::make_tuple(95, "DC2", "a0e9300", "10.14.147.16", 1024),
      std::make_tuple(96, "DC1", "a908a02", "10.144.138.147", 1024),
      std::make_tuple(97, "DC1", "a18ba03", "10.24.186.229", 1024),
      std::make_tuple(98, "DC1", "a908901", "10.144.137.79", 1024),
      std::make_tuple(99, "DC1", "a908900", "10.144.137.25", 1024),
      std::make_tuple(100, "DC1", "a17ae02", "10.23.174.160", 1024),
      std::make_tuple(101, "DC1", "a19c100", "10.25.193.26", 1024),
      std::make_tuple(102, "DC1", "a908803", "10.144.136.227", 1024),
      std::make_tuple(103, "DC1", "a15da01", "10.21.218.66", 1024),
      std::make_tuple(104, "DC1", "a908803", "10.144.136.222", 1024),
      std::make_tuple(105, "DC1", "a18ba00", "10.24.186.47", 1024),
      std::make_tuple(106, "DC1", "a17ac03", "10.23.172.208", 1024),
      std::make_tuple(107, "DC1", "a17ad01", "10.23.173.68", 1024),
      std::make_tuple(108, "DC1", "a17ac00", "10.23.172.17", 1024),
      std::make_tuple(109, "DC1", "a15d101", "10.21.209.100", 1024),
      std::make_tuple(110, "DC1", "a15d301", "10.21.211.90", 1024),
      std::make_tuple(111, "DC1", "a19c503", "10.25.197.233", 1024),
      std::make_tuple(112, "DC1", "a17ad03", "10.23.173.209", 1024),
      std::make_tuple(113, "DC1", "a17ac01", "10.23.172.78", 1024),
      std::make_tuple(114, "DC1", "a19bf00", "10.25.191.20", 1024),
      std::make_tuple(115, "DC1", "a15da01", "10.21.218.97", 1024),
      std::make_tuple(116, "DC1", "a15cd03", "10.21.205.206", 1024),
      std::make_tuple(117, "DC1", "a17ac03", "10.23.172.203", 1024),
      std::make_tuple(118, "DC1", "a15d102", "10.21.209.165", 1024),
      std::make_tuple(119, "DC1", "a17ac01", "10.23.172.95", 1024),
      std::make_tuple(120, "DC1", "a19be01", "10.25.190.67", 1024),
      std::make_tuple(121, "DC1", "a17aa03", "10.23.170.217", 1024),
      std::make_tuple(122, "DC1", "a17ae02", "10.23.174.155", 1024),
      std::make_tuple(123, "DC1", "a19c103", "10.25.193.205", 1024),
      std::make_tuple(124, "DC1", "a19c600", "10.25.198.40", 1024),
      std::make_tuple(125, "DC1", "a17ad00", "10.23.173.41", 1024),
      std::make_tuple(126, "DC1", "a19c102", "10.25.193.148", 1024),
      std::make_tuple(127, "DC1", "a19be02", "10.25.190.152", 1024),
      std::make_tuple(128, "DC1", "a908903", "10.144.137.230", 1024),
      std::make_tuple(129, "DC1", "a908901", "10.144.137.102", 1024),
      std::make_tuple(130, "DC1", "a17af03", "10.23.175.217", 1024),
      std::make_tuple(131, "DC1", "a19bd03", "10.25.189.214", 1024),
      std::make_tuple(132, "DC1", "a19be00", "10.25.190.53", 1024),
      std::make_tuple(133, "DC1", "a19c200", "10.25.194.54", 1024),
      std::make_tuple(134, "DC1", "a19c400", "10.25.196.15", 1024),
      std::make_tuple(135, "DC1", "a17ae02", "10.23.174.146", 1024),
      std::make_tuple(136, "DC1", "a18ba02", "10.24.186.165", 1024),
      std::make_tuple(137, "DC1", "a17ae02", "10.23.174.154", 1024),
      std::make_tuple(138, "DC1", "a17af02", "10.23.175.163", 1024),
      std::make_tuple(139, "DC1", "a15d202", "10.21.210.154", 1024),
      std::make_tuple(140, "DC1", "a17ae01", "10.23.174.67", 1024),
      std::make_tuple(141, "DC1", "a17ac01", "10.23.172.80", 1024),
      std::make_tuple(142, "DC1", "a908902", "10.144.137.133", 1024),
      std::make_tuple(143, "DC1", "a15da00", "10.21.218.24", 1024),
      std::make_tuple(144, "DC1", "a908900", "10.144.137.37", 1024),
      std::make_tuple(145, "DC1", "a908902", "10.144.137.144", 1024),
      std::make_tuple(146, "DC1", "a908a02", "10.144.138.140", 1024),
      std::make_tuple(147, "DC1", "a908901", "10.144.137.78", 1024),
      std::make_tuple(148, "DC1", "a908903", "10.144.137.225", 1024),
      std::make_tuple(149, "DC1", "a908903", "10.144.137.234", 1024),
      std::make_tuple(150, "DC1", "a17af03", "10.23.175.201", 1024),
      std::make_tuple(151, "DC1", "a908902", "10.144.137.143", 1024),
      std::make_tuple(152, "DC1", "a908902", "10.144.137.141", 1024),
      std::make_tuple(153, "DC1", "a908903", "10.144.137.213", 1024),
      std::make_tuple(154, "DC1", "a908900", "10.144.137.15", 1024),
      std::make_tuple(155, "DC1", "a908903", "10.144.137.229", 1024),
      std::make_tuple(156, "DC1", "a908901", "10.144.137.101", 1024),
      std::make_tuple(157, "DC1", "a908900", "10.144.137.20", 1024),
      std::make_tuple(158, "DC1", "a19c701", "10.25.199.90", 1024),
      std::make_tuple(159, "DC1", "a908902", "10.144.137.138", 1024),
      std::make_tuple(160, "DC1", "a908a02", "10.144.138.154", 1024),
      std::make_tuple(161, "DC1", "a908a01", "10.144.138.101", 1024),
      std::make_tuple(162, "DC1", "a908902", "10.144.137.139", 1024),
      std::make_tuple(163, "DC1", "a908a02", "10.144.138.163", 1024),
      std::make_tuple(164, "DC1", "a908a02", "10.144.138.164", 1024),
      std::make_tuple(165, "DC1", "a908a02", "10.144.138.152", 1024),
      std::make_tuple(166, "DC1", "a17ae03", "10.23.174.205", 1024),
      std::make_tuple(167, "DC1", "a19c402", "10.25.196.162", 1024),
      std::make_tuple(168, "DC1", "a18b902", "10.24.185.165", 1024),
      std::make_tuple(169, "DC1", "a15cb00", "10.21.203.36", 1024),
      std::make_tuple(170, "DC1", "a15cd00", "10.21.205.22", 1024),
      std::make_tuple(171, "DC1", "a15d200", "10.21.210.44", 1024),
      std::make_tuple(172, "DC1", "a19c601", "10.25.198.83", 1024),
      std::make_tuple(173, "DC1", "a908901", "10.144.137.92", 1024),
      std::make_tuple(174, "DC1", "a19c303", "10.25.195.220", 1024),
      std::make_tuple(175, "DC1", "a17ae00", "10.23.174.13", 1024),
      std::make_tuple(176, "DC1", "a17ae03", "10.23.174.199", 1024),
      std::make_tuple(177, "DC1", "a18b903", "10.24.185.229", 1024),
      std::make_tuple(178, "DC1", "a17ad01", "10.23.173.95", 1024),
      std::make_tuple(179, "DC1", "a17ac03", "10.23.172.225", 1024),
      std::make_tuple(180, "DC2", "a0e9101", "10.14.145.66", 1024),
      std::make_tuple(181, "DC2", "a0e9601", "10.14.150.76", 1024),
      std::make_tuple(182, "DC2", "a0e9402", "10.14.148.136", 1024),
      std::make_tuple(183, "DC2", "a0e9401", "10.14.148.92", 1024),
      std::make_tuple(184, "DC2", "a0e9702", "10.14.151.145", 1024),
      std::make_tuple(185, "DC2", "a0e9803", "10.14.152.220", 1024),
      std::make_tuple(186, "DC2", "a0cab02", "10.12.171.159", 1024),
      std::make_tuple(187, "DC2", "a0e9302", "10.14.147.161", 1024),
      std::make_tuple(188, "DC2", "a0c9503", "10.12.149.223", 1024),
      std::make_tuple(189, "DC2", "a0cab00", "10.12.171.43", 1024),
      std::make_tuple(190, "DC2", "a0e9002", "10.14.144.157", 1024),
      std::make_tuple(191, "DC2", "a0c9902", "10.12.153.134", 1024),
  };
  auto testbed = AddNodes(topology);
  auto dn1 = testbed.find(1)->second, dn4 = testbed.find(4)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  PlacementAdvice advice(kHotStoragePolicy);
  advice.geograph = kDistributePolicy;
  advice.enforce_dc = "DC1,DC2";

  using RackChosenNum = std::unordered_map<std::string, int>;
  using DcChosenNum = std::unordered_map<std::string, RackChosenNum>;
  DcChosenNum dc_chosen_num;
  dc_chosen_num["DC2"] = RackChosenNum();
  dc_chosen_num["DC1"] = RackChosenNum();
  for (int i = 0; i < 10000; ++i) {
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      advice,
                                      NetworkLocationInfo(),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    ASSERT_EQ(results.size(), 3);
    for (auto& r : results) {
      auto rack = GetRackFromDatanodeID(r->id());
      auto ip = GetDCFromDatanodeID(r->id());
      auto& rcn = dc_chosen_num[ip];
      auto itr = rcn.find(rack);
      if (itr == rcn.end()) {
        rcn.emplace(std::make_pair(rack, 1));
      } else {
        itr->second++;
      }
    }
    excluded.clear();
    results.clear();
  }
  for (auto& dcn : dc_chosen_num) {
    for (auto& rcn : dcn.second) {
      LOG(INFO) << "DC: " << dcn.first << ", rack: " << rcn.first
                << ", nodes: " << GetRackNodeNum(rcn.first)
                << ",num: " << rcn.second;
    }
  }
}

}  // namespace

}  // namespace dancenn
