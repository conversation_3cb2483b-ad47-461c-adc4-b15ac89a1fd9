#include "datanode_manager/block_index.h"

#include <algorithm>

#include "base/hash.h"
#include "base/rand_number.h"

#include "glog/logging.h"
#include "gtest/gtest.h"

namespace dancenn {

TEST(BlockIndex, Test01) {
  UnorderedBlockIndexImpl index;
  ASSERT_FALSE(index.Contains(123));
  index.AddBlock(123);
  ASSERT_TRUE(index.Contains(123));
  index.RemoveBlock(123);
  ASSERT_FALSE(index.Contains(123));

  for (uint64_t i = 1; i < 1234567L; i++) {
    uint64_t val = hash_uint64(i);
    index.AddBlock(val);
  }
  for (uint64_t i = 1; i < 1234567L; i++) {
    uint64_t val = hash_uint64(i);
    ASSERT_TRUE(index.Contains(val));
  }

  std::set<uint64_t> vals;
  index.ForEach([&] (uint64_t block_id) {
    vals.insert(block_id);
  });
  for (uint64_t i = 1; i < 1234567L; i++) {
    uint64_t val = hash_uint64(i);
    ASSERT_TRUE(vals.find(val) != vals.end());
  }
}

TEST(BlockIndex, Test02) {
  OrderedBlockIndexImpl index;
  ASSERT_FALSE(index.Contains(123));
  index.AddBlock(123);
  ASSERT_TRUE(index.Contains(123));
  index.RemoveBlock(123);
  ASSERT_FALSE(index.Contains(123));

  for (uint64_t i = 1; i < 20001; i++) {
    uint64_t val = i + 1000000;
    index.AddBlock(val);
  }
  ASSERT_FALSE(index.Contains(123));
  index.AddBlock(123);
  ASSERT_TRUE(index.Contains(123));
}

TEST(BlockIndex, Test03) {
  UnorderedBlockIndexImpl index;
  index.AddBlock(5);
  index.AddBlock(1);
  index.AddBlock(3);
  index.RemoveBlock(1);
  ASSERT_FALSE(index.Contains(1));
  ASSERT_TRUE(index.Contains(3));
  ASSERT_TRUE(index.Contains(5));
  ASSERT_FALSE(index.Contains(1));
  ASSERT_TRUE(index.Contains(3));
  ASSERT_TRUE(index.Contains(5));
  index.RemoveBlock(3);
  ASSERT_FALSE(index.Contains(1));
  ASSERT_FALSE(index.Contains(3));
  ASSERT_TRUE(index.Contains(5));
  index.AddBlock(1);
  index.AddBlock(2);
  index.AddBlock(3);
  index.RemoveBlock(2);
  index.RemoveBlock(3);
  index.AddBlock(7);
  ASSERT_TRUE(index.Contains(1));
  ASSERT_FALSE(index.Contains(3));
  ASSERT_TRUE(index.Contains(5));
  ASSERT_TRUE(index.Contains(7));
  ASSERT_FALSE(index.Contains(8));
}

TEST(BlockIndex, ExtractBlocks) {
  OrderedBlockIndexImpl index;
  for (uint64_t i = 1; i <= 100; ++i) {
    index.AddBlock(i);
  }
  index.RemoveBlock(1);
  index.RemoveBlock(10);
  std::vector<uint64_t> blocks;
  index.ExtractBlocks(&blocks);
  ASSERT_TRUE(index.size() == 0);
  ASSERT_TRUE(blocks.size() == 98);
}

TEST(BlockIndex, Test04) {
  UnorderedBlockIndexImpl index;
  index.AddBlocks(std::vector<uint64_t>{100, 101, 102, 103, 104});
  ASSERT_EQ(5, index.size());
  for (int i = 100; i < 105; i++) {
    ASSERT_TRUE(index.Contains(i));
  }
  {
    index.AddBlocks(std::vector<uint64_t>{100});
    ASSERT_EQ(5, index.size());
    for (int i = 100; i < 105; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
  {
    index.AddBlocks(std::vector<uint64_t>{101});
    ASSERT_EQ(5, index.size());
    for (int i = 100; i < 105; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
  {
    index.AddBlocks(std::vector<uint64_t>{104});
    ASSERT_EQ(5, index.size());
    for (int i = 100; i < 105; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
  {
    index.AddBlocks(std::vector<uint64_t>{99, 100});
    ASSERT_EQ(6, index.size());
    for (int i = 99; i < 105; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
  {
    index.AddBlocks(std::vector<uint64_t>{104, 105});
    ASSERT_EQ(7, index.size());
    for (int i = 99; i < 106; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
  {
    index.AddBlocks(std::vector<uint64_t>{106, 107});
    ASSERT_EQ(9, index.size());
    for (int i = 99; i < 108; i++) {
      ASSERT_TRUE(index.Contains(i));
    }
  }
}

}  // namespace dancenn
