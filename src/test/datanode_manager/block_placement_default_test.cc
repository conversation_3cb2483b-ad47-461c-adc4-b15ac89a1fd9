// Copyright 2017 Li<PERSON> Lei <<EMAIL>>

#include <gtest/gtest.h>

#include <chrono>
#include <thread>
#include <vector>

#include "base/rack_aware.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/storage_policy.h"

DECLARE_bool(run_ut);
DECLARE_int32(datanode_tolerate_interval_misses_sec);
DECLARE_int32(datanode_stale_interval_ms);

using cloudfs::StorageTypeProto;

namespace dancenn {

namespace {

auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;

class BlockPlacementDefaultTest : public testing::Test {
 public:
  void SetUp() override {
    metrics_.reset(new DatanodeManagerMetrics(nullptr));
    FLAGS_datanode_stale_interval_ms = 500;
    FLAGS_run_ut = true;
    bp_ = std::make_unique<BlockPlacementDefault>(metrics_.get());
  }

  void TearDown() override {
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
  }

  std::unique_ptr<DatanodeManagerMetrics> metrics_;
  std::unique_ptr<BlockPlacement> bp_;
  const cnetpp::base::IPAddress DEFAULT_IP =
      cnetpp::base::IPAddress("***********");
  std::string src;

 protected:
  std::unordered_map<DatanodeID, DatanodeInfoPtr> AddNode(
      const std::unordered_map<DatanodeID, std::string> topology);
};

bool CheckResult(const std::unordered_set<DatanodeInfoPtr>& expected,
                 const std::vector<DatanodeInfoPtr>& results) {
  for (auto it = results.begin(); it != results.end(); ++it) {
    if (expected.find(*it) == expected.end()) {
      return false;
    }
  }
  return true;
}

void GetDiskRequest(HeartbeatRequestProto* request, int64_t remaining = 0) {
  {
    auto disk_report = request->add_reports();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid("disk1");
    disk_report->set_remaining(remaining);
  }
  {
    auto disk_report = request->add_reports();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid("disk2");
    disk_report->set_remaining(remaining);
  }
}

void GetSSDRequest(HeartbeatRequestProto* request, int64_t remaining = 0) {
  auto ssd_report = request->add_reports();
  ssd_report->mutable_storage()->set_storagetype(StorageTypeProto::SSD);
  ssd_report->mutable_storage()->set_storageuuid("ssd");
  ssd_report->set_remaining(0);
}

std::unordered_map<DatanodeID, DatanodeInfoPtr> BlockPlacementDefaultTest::
    AddNode(  //  NOLINT
        const std::unordered_map<DatanodeID, std::string> topology) {
  HeartbeatRequestProto request;
  GetDiskRequest(&request);
  std::unordered_map<DatanodeID, DatanodeInfoPtr> nodemap;

  for (auto it = topology.begin(); it != topology.end(); it++) {
    DatanodeInfoPtr dn = new DatanodeInfo(
        it->first, DatanodeIDProto(), cnetpp::base::IPAddress(it->second));
    dn->CheckAndUpdateHeartbeat();
    dn->UpdateStorage(request);
    bp_->AddDatanode(dn);

    nodemap.insert(std::make_pair(it->first, dn));
  }

  return nodemap;
}

TEST_F(BlockPlacementDefaultTest, TestAddDatanode) {
  DatanodeIDProto addr;
  DatanodeInfoPtr dn1 = new DatanodeInfo(1, addr, DEFAULT_IP);
  DatanodeInfoPtr dn2 = new DatanodeInfo(2, addr, DEFAULT_IP);
  DatanodeInfoPtr dn3 = new DatanodeInfo(3, addr, DEFAULT_IP);

  dn1->SetLocation4Test("DC1", "rack1");
  dn1->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn1);
  dn2->SetLocation4Test("DC2", "rack2");
  dn2->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn2);
  dn3->SetLocation4Test("DC2", "rack1");
  dn3->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn3);
  std::string str;
  bp_->ListAll(&str);
  ASSERT_TRUE(str.find("rack1: 3 1") != std::string::npos);
  ASSERT_TRUE(str.find("rack2: 2") != std::string::npos);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4New) {
  DatanodeIDProto addr;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  DatanodeInfoPtr dn1 = new DatanodeInfo(1, addr, DEFAULT_IP);

  HeartbeatRequestProto request;
  GetDiskRequest(&request);
  dn1->UpdateStorage(request);
  dn1->SetLocation4Test("DC1", "rack1");
  dn1->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn1);

  // rack1: dn1(DISK)
  std::unordered_set<DatanodeInfoPtr> expected;
  expected.insert(dn1);
  PlacementAdvice disk_type(kHotStoragePolicy);
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     3,
                                     1024,
                                     disk_type,
                                     NetworkLocationInfo(),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    disk_type,
                                    NetworkLocationInfo(),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  // fallback
  PlacementAdvice ssd_type(kAllSSDStoragePolicy);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    ssd_type,
                                    NetworkLocationInfo(),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  // failed
  PlacementAdvice only_ssd_type(kOnlySSDStoragePolicy);
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     1,
                                     1024,
                                     only_ssd_type,
                                     NetworkLocationInfo(),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_TRUE(results.empty());

  results.clear();
  excluded.clear();

  // add SSD
  request.Clear();
  GetDiskRequest(&request);
  GetSSDRequest(&request);
  dn1->UpdateStorage(request);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    ssd_type,
                                    NetworkLocationInfo(),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    1,
                                    1024,
                                    only_ssd_type,
                                    NetworkLocationInfo(),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  // rack1: dn1(DISK+SSD)
  // rack2: dn2(DISK) dn3(DISK)
  DatanodeInfoPtr dn2 = new DatanodeInfo(2, addr, DEFAULT_IP);
  DatanodeInfoPtr dn3 = new DatanodeInfo(3, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn2->UpdateStorage(request);
  dn3->UpdateStorage(request);
  dn2->SetLocation4Test("DC2", "rack2");
  dn2->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn2);
  dn3->SetLocation4Test("DC1", "rack2");
  dn3->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn3);

  expected.insert(dn2);
  expected.insert(dn3);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    disk_type,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(results[0] == dn2 || results[0] == dn3);  // same rack
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  // fallback
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    ssd_type,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(results[0] == dn1);  // ssd first
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  // failed
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     3,
                                     1024,
                                     only_ssd_type,
                                     NetworkLocationInfo(dn2),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_EQ(results.size(), 1);

  results.clear();
  excluded.clear();

  // rack1: dn1(DISK+SSD) dn4(SSD)
  // rack2: dn2(DISK) dn3(DISK) dn5(SSD)
  DatanodeInfoPtr dn4 = new DatanodeInfo(4, addr, DEFAULT_IP);
  DatanodeInfoPtr dn5 = new DatanodeInfo(5, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  GetSSDRequest(&request);
  dn4->UpdateStorage(request);
  dn5->UpdateStorage(request);
  dn4->SetLocation4Test("DC1", "rack1");
  dn4->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn4);
  dn5->SetLocation4Test("DC1", "rack2");
  dn5->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn5);

  expected.clear();
  expected.insert(dn1);
  expected.insert(dn4);
  expected.insert(dn5);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    ssd_type,
                                    NetworkLocationInfo(dn5),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(CheckResult(expected, results));
  ASSERT_TRUE(results[0] == dn5);

  results.clear();
  excluded.clear();

  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    only_ssd_type,
                                    NetworkLocationInfo(dn5),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);
  ASSERT_TRUE(CheckResult(expected, results));
  ASSERT_TRUE(results[0] == dn5);

  results.clear();
  excluded.clear();

  // rack1: dn1(DISK+SSD) dn4(SSD)
  // rack2: dn2(DISK) dn3(DISK)
  std::vector<uint64_t> blocks;
  std::chrono::milliseconds keep_alive{0};
  dn5->MarkDead(&blocks, keep_alive);

  // fallback
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    ssd_type,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);

  results.clear();
  excluded.clear();

  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     3,
                                     1024,
                                     only_ssd_type,
                                     NetworkLocationInfo(dn2),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
  ASSERT_EQ(results.size(), 2);

  results.clear();
  excluded.clear();

  // rack1: -
  // rack2: dn2(DISK) dn3(DISK) dn6(DISK)
  dn1->MarkDead(&blocks, keep_alive);
  dn4->MarkDead(&blocks, keep_alive);
  DatanodeInfoPtr dn6 = new DatanodeInfo(6, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn6->UpdateStorage(request);
  dn6->SetLocation4Test("DC1", "rack2");
  dn6->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn6);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    3,
                                    1024,
                                    disk_type,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));
  ASSERT_EQ(results.size(), 3);

  results.clear();
  excluded.clear();
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithFavoredNodes) {
  std::unordered_map<DatanodeID, std::string> topology({{1, "***********"},
                                                        {2, "************"},
                                                        {3, "************"},
                                                        {4, "10.10.10.224"}});
  const auto nodeset = AddNode(topology);
  const auto dn1 = nodeset.find(1)->second;
  const auto dn2 = nodeset.find(2)->second;
  const auto dn3 = nodeset.find(3)->second;
  const auto dn4 = nodeset.find(4)->second;

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::vector<DatanodeInfoPtr> favored = {dn1, dn2, dn3};
    for (int i = 0; i < 10; i++) {
      excluded.clear();
      results.clear();
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        kDefaultAdvice,
                                        NetworkLocationInfo(dn4),
                                        favored,
                                        &excluded,
                                        &results));
      ASSERT_EQ(results.size(), 3);
      ASSERT_EQ(favored, results);
    }
  }

  {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    std::vector<DatanodeInfoPtr> favored = {dn1, dn2, dn3};
    for (int i = 0; i < 10; i++) {
      excluded.clear();
      results.clear();
      excluded.insert(dn1);
      ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                        3,
                                        1024,
                                        kDefaultAdvice,
                                        NetworkLocationInfo(dn4),
                                        favored,
                                        &excluded,
                                        &results));
      ASSERT_EQ(results.size(), 3);
      std::unordered_set<DatanodeInfoPtr> actual;
      actual.insert(results.begin(), results.end());
      std::unordered_set<DatanodeInfoPtr> expected = {dn2, dn3, dn4};
      ASSERT_EQ(expected, actual);
    }
  }
}

TEST_F(BlockPlacementDefaultTest, ChooseTargetWithStaleDN) {
  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  DatanodeInfoPtr dn1 = new DatanodeInfo(
      1, DatanodeIDProto(), cnetpp::base::IPAddress("***********0"));
  HeartbeatRequestProto request;
  GetDiskRequest(&request);
  dn1->UpdateStorage(request);
  dn1->UpdateHeartbeat(true);
  dn1->SetLocation4Test("DC1", "rack1");
  dn1->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn1);

  DatanodeInfoPtr dn2 = new DatanodeInfo(
      2, DatanodeIDProto(), cnetpp::base::IPAddress("************"));
  dn2->UpdateStorage(request);
  dn2->UpdateHeartbeat(true);
  dn2->SetLocation4Test("DC1", "rack2");
  dn2->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn2);
  PlacementAdvice disk_type(kHotStoragePolicy);
  ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                    2,
                                    1024,
                                    disk_type,
                                    NetworkLocationInfo(dn2),
                                    kDefaultFavored,
                                    &excluded,
                                    &results));

  std::this_thread::sleep_for(
      std::chrono::milliseconds(FLAGS_datanode_stale_interval_ms * 2));
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     2,
                                     1024,
                                     disk_type,
                                     NetworkLocationInfo(dn2),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithStaleDN2) {
  std::unordered_map<DatanodeID, std::string> topology({{1, "***********"},
                                                        {2, "************"},
                                                        {3, "************"},
                                                        {4, "10.10.10.224"}});

  auto nodeset = AddNode(topology);

  std::vector<uint64_t> blocks;
  std::chrono::milliseconds keep_alive{0};

  nodeset.find(1)->second->MarkDead(&blocks, keep_alive);

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(bp_->HasBeenMultiRack(), true);
  ASSERT_EQ(bp_->NumRacks(), 3);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  ASSERT_FALSE(
      bp_->ChooseTarget4New(src,
                            5,
                            1024,
                            kDefaultAdvice,
                            NetworkLocationInfo(nodeset.find(3)->second),
                            kDefaultFavored,
                            &excluded,
                            &results));
  ASSERT_EQ(results.size(), 0);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewInSingleRack) {
  std::unordered_map<DatanodeID, std::string> topology(
      {{1, "***********"}, {2, "***********"}, {3, "10.10.10.12"}});

  auto nodeset = AddNode(topology);

  ASSERT_EQ(bp_->HasBeenMultiRack(), false);
  ASSERT_EQ(bp_->NumRacks(), 1);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  ASSERT_TRUE(
      bp_->ChooseTarget4New(src,
                            3,
                            1024,
                            PlacementAdvice(kHotStoragePolicy),
                            NetworkLocationInfo(nodeset.find(3)->second),
                            kDefaultFavored,
                            &excluded,
                            &results));

  ASSERT_EQ(results.size(), 3);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewRandomly) {
  std::unordered_map<DatanodeID, std::string> topology({{1, "***********"},
                                                        {2, "***********"},
                                                        {3, "10.10.10.12"},
                                                        {4, "10.10.10.13"},
                                                       {5, "10.10.10.14"},
                                                       {6, "10.10.10.15"}});

  auto nodeset = AddNode(topology);

  ASSERT_EQ(bp_->HasBeenMultiRack(), false);
  ASSERT_EQ(bp_->NumRacks(), 1);

  std::unordered_set<DatanodeInfoPtr> storages;

  // here is trick, in ChooseTarget4New use std::shuffle to get real results,
  // before use 12, for passing testcase we use 100
  for (int i = 0; i < 100; i++) {
    std::unordered_set<DatanodeInfoPtr> excluded;
    std::vector<DatanodeInfoPtr> results;
    ASSERT_TRUE(
        bp_->ChooseTarget4New(src,
                              3,
                              1024,
                              PlacementAdvice(kHotStoragePolicy),
                              NetworkLocationInfo(nodeset.find(3)->second),
                              kDefaultFavored,
                              &excluded,
                              &results));
    ASSERT_EQ(results.size(), 3);
    storages.insert(results.begin(), results.end());
  }

  ASSERT_EQ(storages.size(), topology.size());
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithRackAwareness) {
  std::unordered_map<DatanodeID, std::string> topology(
      {{1, "***********"}, {2, "***********"}, {3, "************"}});

  auto nodeset = AddNode(topology);

  ASSERT_TRUE(bp_->HasBeenMultiRack());
  ASSERT_EQ(bp_->NumRacks(), 2);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  bp_->ChooseTarget4New(src,
                        3,
                        1024,
                        PlacementAdvice(kHotStoragePolicy),
                        NetworkLocationInfo(nodeset.find(3)->second),
                        kDefaultFavored,
                        &excluded,
                        &results);

  ASSERT_EQ(results.size(), 3);
  ASSERT_NE(ResolveNetworkLocation(results[0]->ip()).rack,
            ResolveNetworkLocation(results[1]->ip()).rack);
  ASSERT_EQ(ResolveNetworkLocation(results[1]->ip()).rack,
            ResolveNetworkLocation(results[2]->ip()).rack);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithOutsideClient) {
  std::unordered_map<DatanodeID, std::string> topology(
      {{1, "***********"}, {2, "***********"}, {3, "************"}});

  AddNode(topology);

  ASSERT_TRUE(bp_->HasBeenMultiRack());
  ASSERT_EQ(bp_->NumRacks(), 2);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  bp_->ChooseTarget4New(src,
                        3,
                        1024,
                        kDefaultAdvice,
                        NetworkLocationInfo(),
                        kDefaultFavored,
                        &excluded,
                        &results);
  ASSERT_EQ(results.size(), 3);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithInsufficientNodes) {
  std::unordered_map<DatanodeID, std::string> topology({
      {1, "***********"},
      {2, "***********"},
  });

  AddNode(topology);

  ASSERT_EQ(bp_->HasBeenMultiRack(), false);
  ASSERT_EQ(bp_->NumRacks(), 1);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  ASSERT_FALSE(bp_->ChooseTarget4New(src,
                                     3,
                                     1024,
                                     PlacementAdvice(kHotStoragePolicy),
                                     NetworkLocationInfo(),
                                     kDefaultFavored,
                                     &excluded,
                                     &results));

  ASSERT_EQ(results.size(), 2);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewWithInsufficientNodes2) {
  FLAGS_datanode_stale_interval_ms = 60000;

  std::unordered_map<DatanodeID, std::string> topology({{1, "***********"},
                                                        {2, "************"},
                                                        {3, "************"},
                                                        {4, "************"},
                                                       {5, "10.10.10.224"}});

  auto nodeset = AddNode(topology);

  std::vector<uint64_t> blocks;
  std::chrono::milliseconds keep_alive{0};

  nodeset.find(1)->second->MarkDead(&blocks, keep_alive);

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(bp_->HasBeenMultiRack(), true);
  ASSERT_EQ(bp_->NumRacks(), 3);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;

  ASSERT_FALSE(
      bp_->ChooseTarget4New(src,
                            5,
                            1024,
                            PlacementAdvice(kHotStoragePolicy),
                            NetworkLocationInfo(nodeset.find(3)->second),
                            kDefaultFavored,
                            &excluded,
                            &results));

  ASSERT_EQ(results.size(), 4);
}

TEST_F(BlockPlacementDefaultTest, ChooseTarget4NewOfMultipleReplicas) {
  std::unordered_map<DatanodeID, std::string> topology({{1, "***********"},
                                                        {2, "***********"},
                                                        {3, "************"},
                                                        {4, "************"},
                                                        {5, "************"}});

  auto nodeset = AddNode(topology);

  ASSERT_TRUE(bp_->HasBeenMultiRack());
  ASSERT_EQ(bp_->NumRacks(), 4);

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  ASSERT_TRUE(
      bp_->ChooseTarget4New(src,
                            5,
                            1024,
                            PlacementAdvice(kHotStoragePolicy),
                            NetworkLocationInfo(nodeset.find(3)->second),
                            kDefaultFavored,
                            &excluded,
                            &results));

  ASSERT_EQ(results.size(), 5);
  ASSERT_NE(ResolveNetworkLocation(results[0]->ip()).rack,
            ResolveNetworkLocation(results[1]->ip()).rack);
}

TEST_F(BlockPlacementDefaultTest, TestChooseTarget4NewRandom) {
  FLAGS_datanode_stale_interval_ms = 5000;

  DatanodeIDProto addr;

  std::vector<DatanodeInfoPtr> dns;
  for (int rack = 0; rack < 10; ++rack) {
    int start = rack * 100;
    int end = rack * 100 + 100;
    for (int i = start; i < end; ++i) {
      DatanodeInfoPtr dn = new DatanodeInfo(i, addr, DEFAULT_IP);
      HeartbeatRequestProto request;
      GetDiskRequest(&request);
      GetSSDRequest(&request);
      dn->UpdateStorage(request);
      dn->SetLocation4Test("DC1", "rack" + std::to_string(rack));
      dn->CheckWriteableStorageCnt();
      bp_->AddDatanode(dn);
      dns.push_back(dn);
    }
  }

  std::unordered_set<DatanodeInfoPtr> chosen_dn;
  std::vector<DatanodeInfoPtr> results;
  std::unordered_set<DatanodeInfoPtr> excluded;
  auto start = std::chrono::system_clock::now();
  for (int i = 0; i < 100; ++i) {
    results.clear();
    excluded.clear();
    ASSERT_TRUE(bp_->ChooseTarget4New(src,
                                      3,
                                      1024,
                                      PlacementAdvice(kHotStoragePolicy),
                                      NetworkLocationInfo(dns[2]),
                                      kDefaultFavored,
                                      &excluded,
                                      &results));
    chosen_dn.insert(results.begin(), results.end());
  }
  auto end = std::chrono::system_clock::now();
  std::chrono::duration<double> duration = (end - start);
  LOG(INFO) << "AddBlock4New * 100 from 10*100 datanodes took "
            << duration.count();
  LOG(INFO) << "selected " << chosen_dn.size() << "/" << 10 * 100
            << " datanodes";
  ASSERT_GT(chosen_dn.size(), 3);
}

TEST_F(BlockPlacementDefaultTest, TestChooseTarget4Recover) {
  DatanodeIDProto addr;

  std::unordered_set<DatanodeInfoPtr> included;
  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;

  // rack1: dn1(DISK) dn2(DISK)
  DatanodeInfoPtr dn1 = new DatanodeInfo(1, addr, DEFAULT_IP);
  DatanodeInfoPtr dn2 = new DatanodeInfo(2, addr, DEFAULT_IP);
  HeartbeatRequestProto request;
  GetDiskRequest(&request);
  dn1->UpdateStorage(request);
  dn2->UpdateStorage(request);
  dn1->SetLocation4Test("DC1", "rack1");
  dn1->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn1);
  dn2->SetLocation4Test("DC1", "rack1");
  dn2->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn2);
  included.insert(dn1);
  PlacementAdvice disk_type(kHotStoragePolicy);
  ASSERT_FALSE(bp_->ChooseTarget4Recover(src,
                                         2,
                                         1024,
                                         disk_type,
                                         NetworkLocationInfo(dn2),
                                         kDefaultFavored,
                                         included,
                                         &excluded,
                                         &results));
  ASSERT_EQ(results.size(), 1);

  results.clear();
  excluded.clear();

  // rack1: dn1(DISK) dn2(DISK)
  // rack2: dn3(DISK)
  std::unordered_set<DatanodeInfoPtr> expected;
  DatanodeInfoPtr dn3 = new DatanodeInfo(3, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn3->UpdateStorage(request);
  dn3->SetLocation4Test("DC1", "rack2");
  dn3->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn3);
  expected.insert(dn2);
  expected.insert(dn3);
  ASSERT_FALSE(bp_->ChooseTarget4Recover(src,
                                         3,
                                         1024,
                                         disk_type,
                                         NetworkLocationInfo(dn2),
                                         kDefaultFavored,
                                         included,
                                         &excluded,
                                         &results));
  ASSERT_EQ(results.size(), 2);
  ASSERT_TRUE(results[0] == dn2);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  included.insert(dn3);
  expected.erase(dn3);
  ASSERT_FALSE(bp_->ChooseTarget4Recover(src,
                                         3,
                                         1024,
                                         disk_type,
                                         NetworkLocationInfo(dn2),
                                         kDefaultFavored,
                                         included,
                                         &excluded,
                                         &results));
  ASSERT_EQ(results.size(), 1);
  ASSERT_TRUE(results[0] == dn2);
  ASSERT_TRUE(CheckResult(expected, results));

  results.clear();
  excluded.clear();

  std::vector<uint8_t> address_;

  // rack1: dn1(DISK) dn2(DISK)
  // rack2: dn3(DISK) dn4(DISK) dn5(DISK) dn6(DISK)
  DatanodeInfoPtr dn4 = new DatanodeInfo(4, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn4->UpdateStorage(request);
  dn4->SetLocation4Test("DC1", "rack2");
  dn4->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn4);

  DatanodeInfoPtr dn5 = new DatanodeInfo(5, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn5->UpdateStorage(request);
  dn5->SetLocation4Test("DC1", "rack2");
  dn5->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn5);

  DatanodeInfoPtr dn6 = new DatanodeInfo(6, addr, DEFAULT_IP);
  request.Clear();
  GetDiskRequest(&request);
  dn6->UpdateStorage(request);
  dn6->SetLocation4Test("DC1", "rack2");
  dn6->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn6);

  included.clear();
  included.insert(dn3);
  results.clear();
  ASSERT_TRUE(bp_->ChooseTarget4Recover(src,
                                        3,
                                        1024,
                                        disk_type,
                                        NetworkLocationInfo(dn3),
                                        kDefaultFavored,
                                        included,
                                        &excluded,
                                        &results));
  ASSERT_EQ(results.size(), 3);
  auto c = 0;
  for (auto const result : results) {
    if (result == dn1 || result == dn2) {
      ++c;
    }
  }
  ASSERT_EQ(c, 2);
}

TEST_F(BlockPlacementDefaultTest, TestNumRack) {
  DatanodeInfoPtr dn1 = new DatanodeInfo(
      1, DatanodeIDProto(), cnetpp::base::IPAddress("***********0"));
  DatanodeInfoPtr dn2 = new DatanodeInfo(
      2, DatanodeIDProto(), cnetpp::base::IPAddress("************"));

  bp_->AddDatanode(dn1);
  ASSERT_EQ(bp_->HasBeenMultiRack(), false);
  ASSERT_EQ(bp_->NumRacks(), 1);

  bp_->AddDatanode(dn2);
  ASSERT_EQ(bp_->HasBeenMultiRack(), true);
  ASSERT_EQ(bp_->NumRacks(), 2);
}

TEST_F(BlockPlacementDefaultTest, TestChooseReplicaToDelete) {
  DatanodeInfoPtr dn1 = new DatanodeInfo(
      1, DatanodeIDProto(), cnetpp::base::IPAddress("***********0"));
  DatanodeInfoPtr dn2 = new DatanodeInfo(
      2, DatanodeIDProto(), cnetpp::base::IPAddress("************"));
  DatanodeInfoPtr dn3 = new DatanodeInfo(
      3, DatanodeIDProto(), cnetpp::base::IPAddress("************"));
  dn1->CheckAndUpdateHeartbeat();
  dn2->CheckAndUpdateHeartbeat();
  dn3->CheckAndUpdateHeartbeat();

  std::unordered_set<DatanodeInfoPtr> more_than_one = {dn1, dn2};
  std::unordered_set<DatanodeInfoPtr> exactly_one = {dn3};

  HeartbeatRequestProto request;
  GetDiskRequest(&request, 1024);
  dn1->UpdateStorage(request);
  dn1->SetLocation4Test("DC1", "rack1");
  dn1->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn1);

  request.Clear();
  GetDiskRequest(&request, 2048);
  dn2->UpdateStorage(request);
  dn2->SetLocation4Test("DC1", "rack1");
  dn2->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn2);

  request.Clear();
  GetDiskRequest(&request, 102400);
  dn3->UpdateStorage(request);
  dn3->SetLocation4Test("DC1", "rack2");
  dn3->CheckWriteableStorageCnt();
  bp_->AddDatanode(dn3);
  DatanodeInfoPtr chosen1 = bp_->ChooseReplicaToDelete(
      Block{0, 1, 14444444}, 3, more_than_one, exactly_one);
  ASSERT_EQ(chosen1, dn1);

  FLAGS_datanode_tolerate_interval_misses_sec = 2;
  dn2->UpdateHeartbeat(true);
  std::this_thread::sleep_for(std::chrono::milliseconds(
      (FLAGS_datanode_tolerate_interval_misses_sec + 1) * 1000));
  dn1->UpdateHeartbeat(true);

  DatanodeInfoPtr chosen2 = bp_->ChooseReplicaToDelete(
      Block{0, 1, 14444444}, 3, more_than_one, exactly_one);
  ASSERT_EQ(chosen2, dn2);
}

}  // namespace

}  // namespace dancenn
