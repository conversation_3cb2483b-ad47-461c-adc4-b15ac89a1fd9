// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/15
// Description

#ifndef TEST_DATANODE_MANAGER_GMOCK_DATANODE_MANAGER_H_
#define TEST_DATANODE_MANAGER_GMOCK_DATANODE_MANAGER_H_

#include <gmock/gmock.h>

#include <string>
#include <unordered_map>

#include "datanode_manager/datanode_info.h"
#include "datanode_manager/datanode_manager.h"

namespace dancenn {

class GMockDatanodeManager : public DatanodeManager {
 public:
  GMockDatanodeManager() : DatanodeManager(false) {
  }

  MOCK_CONST_METHOD1(GetDatanodeFromId, DatanodeInfoPtr(DatanodeID dn_id));
  MOCK_CONST_METHOD1(GetDatanodeInterId, DatanodeID(const std::string& uuid));
  MOCK_CONST_METHOD1(GetDatanodeFromIdInternal,
                     DatanodeInfoPtr(DatanodeID dn_id));
  MOCK_CONST_METHOD1(GetDatanodeFromUuid, DatanodeInfoPtr(const std::string& uuid));
  MOCK_METHOD1(ChooseBlockRecoverPrimary,
               DatanodeID(std::unordered_map<DatanodeID, bool>* replicas));
  MOCK_METHOD0(GetBriefStorageStat, BriefStorageStat());
  MOCK_METHOD2(SetDecommissioned, Status(DatanodeID, bool));
  MOCK_CONST_METHOD0(NumLiveDatanodes, size_t());
  MOCK_METHOD10(ChooseTarget4New,
                bool(const std::string&,
                     int32_t,
                     int32_t,
                     uint32_t,
                     const PlacementAdvice&,
                     const NetworkLocationInfo&,
                     const std::vector<std::string>&,
                     const RepeatedDatanodeInfo&,
                     cloudfs::LocatedBlockProto*,
                     std::vector<DatanodeID>*));
  MOCK_METHOD1(HasEnoughWriteableDatanode, bool(int));
};

}  // namespace dancenn

#endif  // TEST_DATANODE_MANAGER_GMOCK_DATANODE_MANAGER_H_
