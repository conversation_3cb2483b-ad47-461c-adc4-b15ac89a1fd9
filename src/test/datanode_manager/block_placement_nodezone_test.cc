//
// Copyright (c) 2019 Bytedance Inc. All rights reserved.
// Author: jiangx<PERSON>yang <<EMAIL>>
//

#include "datanode_manager/block_placement_nodezone.h"

#include <gtest/gtest.h>
#include <unistd.h>

#include <chrono>
#include <iostream>
#include <memory>
#include <thread>
#include <tuple>
#include <vector>

#include "base/file_utils.h"
#include "datanode_manager/block_placement.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/storage_policy.h"

DECLARE_string(all_datacenters);

DECLARE_int32(nodezone_map_refresh_period_ms);
DECLARE_string(nodezone_map_config_file_path);
DECLARE_int32(dir_to_nodezone_group_refresh_period_ms);
DECLARE_string(dir_to_nodezone_group_config_file_path);

namespace dancenn {

using BlockPlacementNodeZonePtr = std::shared_ptr<BlockPlacementNodeZone>;

namespace {

using DNDesc =
    std::tuple<DatanodeID, std::string, std::string, std::string, uint64_t>;
using DNDescList = std::vector<DNDesc>;
using Testbed = std::unordered_map<DatanodeID, DatanodeInfoPtr>;

class BlockPlacementNodeZoneTest : public testing::Test {
 public:
  void SetUp() override {
    metrics_ = std::make_unique<DatanodeManagerMetrics>(nullptr);
    FLAGS_all_datacenters = "LF,HL";
    FLAGS_nodezone_map_refresh_period_ms = 1 * 1000;
    FLAGS_nodezone_map_config_file_path =
        "./ittest.block_placement_nodezone.node_zone_map";
    auto nodezone_map_str = R"(
[
{
 "nodePaths":[],
 "remainingRatio":0.9,
 "tag": {"id": "default"},
 "id": "NodeZone0"
},
{
 "nodePaths":[],
 "remainingRatio":0.9,
 "tag": {"id": "testV6"},
 "id": "V6NodeZone"
}
]
)";
    FileUtils::WriteFile(FLAGS_nodezone_map_config_file_path, nodezone_map_str);

    FLAGS_dir_to_nodezone_group_refresh_period_ms = 1 * 1000;
    FLAGS_dir_to_nodezone_group_config_file_path =
        "./ittest.block_placement_nodezone.dir2zonetag";
    FileUtils::WriteFile(FLAGS_dir_to_nodezone_group_config_file_path, "{}");

    nodezone_map_str_[0] = R"(
[
{
 "nodePaths":[
  "/LF/0d0100/*********:5080",
  "/LF/0d0100/*********:5080",
  "/LF/0d0100/*********:5080",

  "/LF/0d0101/**********:5080",
  "/LF/0d0101/**********:5080",
  "/LF/0d0101/**********:5080",

  "/HL/150100/*********:5080",
  "/HL/150100/10.21.1.2:5080",
  "/HL/150100/10.21.1.3:5080",

  "/HL/150101/10.21.1.84:5080",
  "/HL/150101/10.21.1.85:5080",
  "/HL/150101/10.21.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "default"},
 "id": "NodeZone0"
},
{
 "nodePaths":[
  "/LF/0e0100/10.14.1.1:5080",
  "/LF/0e0100/10.14.1.2:5080",
  "/LF/0e0100/10.14.1.3:5080",

  "/LF/0e0101/10.14.1.84:5080",
  "/LF/0e0101/10.14.1.85:5080",
  "/LF/0e0101/10.14.1.86:5080",

  "/HL/160100/10.22.1.1:5080",
  "/HL/160100/10.22.1.2:5080",
  "/HL/160100/10.22.1.3:5080",

  "/HL/160101/10.22.1.84:5080",
  "/HL/160101/10.22.1.85:5080",
  "/HL/160101/10.22.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "default"},
 "id": "NodeZone1"
},
{
 "nodePaths":[
  "/LF/0f0100/10.15.1.1:5080",
  "/LF/0f0100/10.15.1.2:5080",
  "/LF/0f0100/10.15.1.3:5080",

  "/LF/0f0101/10.15.1.84:5080",
  "/LF/0f0101/10.15.1.85:5080",
  "/LF/0f0101/10.15.1.86:5080",

  "/HL/170100/10.23.1.1:5080",
  "/HL/170100/10.23.1.2:5080",
  "/HL/170100/10.23.1.3:5080",

  "/HL/170101/10.23.1.84:5080",
  "/HL/170101/10.23.1.85:5080",
  "/HL/170101/10.23.1.86:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testz"},
 "id": "NodeZone2"
},
{
 "nodePaths":[
  "/LF/102060/[FDBD:DC01:102:60::88]:5080",
  "/HL/321321/[::FFFF:A15:9F58]:5080",
  "/LF/102118/[FDBD:DC01:102:118:0::230]:5080",
  "/LF/102118/[FDBD:DC01:0:0:0:0:0:0]:5080",
  "/HL/123123/[1:2:3:4:5:6:7:230]:5080"
 ],
 "remainingRatio":0.9,
 "tag": {"id": "testV6"},
 "id": "V6NodeZone"
}
]
)";

    dir2zonetag_str_[0] = R"(
{"/user/tiger/warehouse/test.not.exists": "testz",
 "/": "default",
 "/testV6/": "testV6"}
)";

    // add ipv6 subnet list (legacy rack aware fatals if we provide v6 ip)
    std::string config_string = "::/1 HL\r\n"
                                "FDBD:DC01::/32 LF\r\n";

    ConfigBasedRackAware::GetSingleton().ParseAndUpdate(config_string);
  }

  void TearDown() override {}

  void GetDiskRequest(HeartbeatRequestProto* request, uint64_t remaining = 0) {
    RepeatedStorageReport disk_storage_report;
    auto disk_report = disk_storage_report.Add();
    disk_report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    disk_report->mutable_storage()->set_storageuuid("disk");
    request->mutable_reports()->CopyFrom(disk_storage_report);
    request->mutable_reports(0)->set_remaining(remaining);
  }

  const Testbed AddNodes(BlockPlacementNodeZonePtr bp,
                         const DNDescList& descs) {
    Testbed testbed;
    for (auto it = descs.begin(); it != descs.end(); it++) {
      DatanodeID id;
      std::string dc, rack, ip;
      uint64_t remaining;
      std::tie(id, dc, rack, ip, remaining) = *it;
      cnetpp::base::IPAddress ip_addr(ip);
      DatanodeIDProto addr;
      DatanodeInfoPtr dn = new DatanodeInfo(id, addr, ip_addr);
      HeartbeatRequestProto request;
      GetDiskRequest(&request, remaining);
      dn->UpdateStorage(request);
      dn->SetLocation4Test(dc, rack);
      bp->AddDatanode(dn);
      testbed.insert(std::make_pair(id, dn));
      dn_dc_.insert(std::make_pair(id, dc));
    }
    return testbed;
  }

 protected:
  std::unique_ptr<DatanodeManagerMetrics> metrics_;
  std::string nodezone_map_str_[1];
  std::string dir2zonetag_str_[1];
  std::unordered_map<DatanodeID, std::string> dn_dc_;
  std::string src;
};


TEST_F(BlockPlacementNodeZoneTest, TestParseV6Address) {
  std::string data_json_str = R"(
[
{
 "nodePaths":[
  "/LF/102060/[FDBD:DC01:102:60::88]:5080",
  "/LF/000000/[::FFFF:A15:9F58]:5080",
  "/LF/102118/[FDBD:DC01:102:118:0::230]:5080",
  "/LF/102118/[FDBD:DC01:0:0:0:0:0:0]:5080",
  "/LF/102118/[1:2:3:4:5:6:7:230]:5080"
 ],
 "remainingRatio":0.856,
 "tag": {"id": "testV6"},
 "id": "V6NodeZone"
}
]
)";
  std::vector<NodeZonePtr> nodezone_list;
  NodeZoneMapRefresher::DecodeDataJson(data_json_str, &nodezone_list);

  auto nz = nodezone_list[0];
  ASSERT_EQ(nz->id, "V6NodeZone");
  ASSERT_TRUE(nz->dnips.count("FDBD:DC01:102:60::88"));
  ASSERT_TRUE(nz->dnips.count("::FFFF:A15:9F58"));
  ASSERT_TRUE(nz->dnips.count("1:2:3:4:5:6:7:230"));

  // dnips should contains the correct V6 ip format
  ASSERT_TRUE(nz->dnips.count("FDBD:DC01:102:118::230"));
  ASSERT_FALSE(nz->dnips.count("FDBD:DC01:102:118:0::230"));

  ASSERT_TRUE(nz->dnips.count("FDBD:DC01::"));
  ASSERT_FALSE(nz->dnips.count("FDBD:DC01:0:0:0:0:0:0"));
}

TEST_F(BlockPlacementNodeZoneTest, TestChooseTargetNewBase) {
  BlockPlacementNodeZonePtr bp(
      new BlockPlacementNodeZone(metrics_.get(), false));
  bp->Start();
  bp->SetNodeZoneMapRefresherTestData(nodezone_map_str_[0]);
  bp->SetDir2ZoneGroupRefresherTestData(dir2zonetag_str_[0]);
  bp->ForceRefreshNodeZoneMapForTest();
  bp->ForceRefreshDir2ZoneGroupForTest();

  // all dn in NodeZone0
  DNDescList topology = {std::make_tuple(11, "LF", "0d0100", "*********", 1024),
                        std::make_tuple(12, "LF", "0d0100", "*********", 1024),
                        std::make_tuple(13, "LF", "0d0100", "*********", 1024),
                        std::make_tuple(14, "LF", "0d0101", "**********", 1024),
                        std::make_tuple(15, "LF", "0d0101", "**********", 1024),
                        std::make_tuple(16, "LF", "0d0101", "**********", 1024),
                        std::make_tuple(17, "HL", "150100", "*********", 1024),
                        std::make_tuple(18, "HL", "150100", "10.21.1.2", 1024),
                        std::make_tuple(19, "HL", "150100", "10.21.1.3", 1024),
                        std::make_tuple(10, "HL", "150101", "10.21.1.84", 1024),
                        std::make_tuple(21, "HL", "150101", "10.21.1.85", 1024),
                        std::make_tuple(22, "HL", "150101", "10.21.1.86", 1024),

                        std::make_tuple(31, "LF", "0e0100", "10.14.1.1", 1024),
                        std::make_tuple(32, "LF", "0e0100", "10.14.1.2", 1024),
                        std::make_tuple(33, "LF", "0e0100", "10.14.1.3", 1024),
                        std::make_tuple(34, "LF", "0e0101", "10.14.1.84", 1024),
                        std::make_tuple(35, "LF", "0e0101", "10.14.1.85", 1024),
                        std::make_tuple(36, "LF", "0e0101", "10.14.1.86", 1024),
                        std::make_tuple(37, "HL", "160100", "10.22.1.1", 1024),
                        std::make_tuple(38, "HL", "160100", "10.22.1.2", 1024),
                        std::make_tuple(39, "HL", "160100", "10.22.1.3", 1024),
                        std::make_tuple(40, "HL", "160101", "10.22.1.84", 1024),
                        std::make_tuple(41, "HL", "160101", "10.22.1.85", 1024),
                        std::make_tuple(42, "HL", "160101", "10.22.1.86", 1024),

                        std::make_tuple(51, "LF", "0f0100", "10.15.1.1", 1024),
                        std::make_tuple(52, "LF", "0f0100", "10.15.1.2", 1024),
                        std::make_tuple(53, "LF", "0f0100", "10.15.1.3", 1024),
                        std::make_tuple(54, "LF", "0f0101", "10.15.1.84", 1024),
                        std::make_tuple(55, "LF", "0f0101", "10.15.1.85", 1024),
                        std::make_tuple(56, "LF", "0f0101", "10.15.1.86", 1024),
                        std::make_tuple(57, "HL", "170100", "10.23.1.1", 1024),
                        std::make_tuple(58, "HL", "170100", "10.23.1.2", 1024),
                        std::make_tuple(59, "HL", "170100", "10.23.1.3", 1024),
                        std::make_tuple(60, "HL", "170101", "10.23.1.84", 1024),
                        std::make_tuple(61, "HL", "170101", "10.23.1.85", 1024),
                        std::make_tuple(62, "HL", "170101", "10.23.1.86", 1024),

                        std::make_tuple(19, "HL", " ", "10.71.1.1", 1024),
                        std::make_tuple(20, "HL", " ", "10.71.1.2", 1024),

                        std::make_tuple(63, "HL", "102060", "FDBD:DC01:102:60::88", 1024),
                        std::make_tuple(64, "HL", "000000", "::FFFF:A15:9F58", 1024),
                        std::make_tuple(65, "HL", "102118", "FDBD:DC01:102:118:0::230", 1024),
                        std::make_tuple(66, "LF", "102118", "FDBD:DC01:0:0:0:0:0:0", 1024),
                        std::make_tuple(67, "LF", "102118", "1:2:3:4:5:6:7:230", 1024)};

  auto testbed = AddNodes(bp, topology);
  auto dn1 = testbed.find(11)->second, dn14 = testbed.find(14)->second;
  auto dn40 = testbed.find(40)->second;

  std::unordered_set<DatanodeInfoPtr> excluded;
  std::vector<DatanodeInfoPtr> results;
  PlacementAdvice disk_type(kHotStoragePolicy);
  disk_type.geograph = kDistributePolicy;

  std::string nodezone0;
  std::string nodezone1;

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test/test.data.0";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn14),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));
  nodezone0 = bp->GetZoneIdByDnip(results.at(0)->address().ipaddr());

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test/test.data.1";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn14),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));
  nodezone1 = bp->GetZoneIdByDnip(results.at(0)->address().ipaddr());

  ASSERT_TRUE(nodezone0.compare(nodezone1) != 0);

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test.not.exists/test.data.hi";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn40),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));
  ASSERT_EQ(bp->GetZoneIdByDnip(results.at(0)->ip().ToString()), "NodeZone2");

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test/test.data.hi";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn40),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test/test.data.hi";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn40),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));

  excluded.clear();
  results.clear();
  src = "/user/tiger/warehouse/test/test.data.hi";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   3,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn40),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 3);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(2)));

  excluded.clear();
  results.clear();

  src = "/testV6/test/test.data.hi";
  ASSERT_TRUE(bp->ChooseTarget4New(src,
                                   5,
                                   1024,
                                   disk_type,
                                   NetworkLocationInfo(dn40),
                                   kDefaultFavored,
                                   &excluded,
                                   &results));
  ASSERT_EQ(results.size(), 5);
  LOG(INFO) << "result[0] ip: " << results.at(0)->ip().ToString()
            << " nodezone In: " << results.at(0)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(0)->ip().ToString());
  LOG(INFO) << "result[1] ip: " << results.at(1)->ip().ToString()
            << " nodezone In: " << results.at(1)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(1)->ip().ToString());
  LOG(INFO) << "result[2] ip: " << results.at(2)->ip().ToString()
            << " nodezone In: " << results.at(2)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(2)->ip().ToString());
  LOG(INFO) << "result[3] ip: " << results.at(3)->ip().ToString()
            << " nodezone In: " << results.at(3)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(3)->ip().ToString());
  LOG(INFO) << "result[4] ip: " << results.at(4)->ip().ToString()
            << " nodezone In: " << results.at(4)->nodezone_id()
            << " nodezone Out: "
            << bp->GetZoneIdByDnip(results.at(4)->ip().ToString());
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(0), results.at(1)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(1), results.at(2)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(2), results.at(3)));
  ASSERT_TRUE(bp->IsSameNodeZone(results.at(3), results.at(4)));

  std::set<std::string> expected_ips;
  expected_ips.insert("FDBD:DC01:102:60::88");
  expected_ips.insert("::FFFF:A15:9F58");
  expected_ips.insert("1:2:3:4:5:6:7:230");
  expected_ips.insert("FDBD:DC01::");
  expected_ips.insert("FDBD:DC01:102:118::230");

  ASSERT_TRUE(expected_ips.count(results.at(0)->ip().ToString()));
  ASSERT_TRUE(expected_ips.count(results.at(1)->ip().ToString()));
  ASSERT_TRUE(expected_ips.count(results.at(2)->ip().ToString()));
  ASSERT_TRUE(expected_ips.count(results.at(3)->ip().ToString()));
  ASSERT_TRUE(expected_ips.count(results.at(4)->ip().ToString()));

  bp->Stop();
  testbed.clear();
}

TEST_F(BlockPlacementNodeZoneTest, TestLoadFromDisk) {
  BlockPlacementNodeZonePtr bp(
      new BlockPlacementNodeZone(metrics_.get(), false));
  bp->Start();
  bp->SetNodeZoneMapRefresherTestData(nodezone_map_str_[0]);
  bp->SetDir2ZoneGroupRefresherTestData(dir2zonetag_str_[0]);
  bp->ForceRefreshNodeZoneMapForTest();

  std::string data_json_str;
  ASSERT_TRUE(bp->GetNodeZoneMapRefresher()->LoadDataFromLocal(&data_json_str));

  data_json_str.assign("");
  ASSERT_TRUE(
      bp->GetDir2ZoneGroupRefresher()->LoadDataFromLocal(&data_json_str));
  bp->Stop();
}

TEST_F(BlockPlacementNodeZoneTest, TestChooseTargetNewOnRedis) {
  BlockPlacementNodeZonePtr bp(
      new BlockPlacementNodeZone(metrics_.get(), false));
  bp->Start();
  ASSERT_TRUE(bp->ForceRefreshNodeZoneMapForTest());
  bp->Stop();
}

}  // namespace
}  // namespace dancenn
