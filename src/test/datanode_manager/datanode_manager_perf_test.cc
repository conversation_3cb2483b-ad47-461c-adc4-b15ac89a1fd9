// Copyright 2020 Mu <PERSON> <<EMAIL>>

#include <cnetpp/base/ip_address.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <mutex>
#include <shared_mutex>
#include <thread>
#include <unordered_map>

#include "base/count_down_latch.h"
#include "base/data_center_table.h"
#include "base/stop_watch.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_report_handler.h"
#include "datanode_manager/datanode_manager.h"

DECLARE_bool(run_ut);
DECLARE_string(block_placement_policy);
DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);

namespace dancenn {

void MemoryPause() {
#ifdef __aarch64__
          asm volatile("yield" ::: "memory");
#else
          asm volatile("pause" ::: "memory");
#endif
}

class DatanodeManagerPerfTest : public testing::Test {
 public:
  void SetUp() override {
    StopWatch sw_setup;
    sw_setup.Start();

    FLAGS_run_ut = true;
    FLAGS_block_placement_policy = "multi-dc";
    LOG(INFO) << "FLAGS_block_placement_policy="
              << FLAGS_block_placement_policy;

    block_manager_ = std::make_shared<BlockManager>();
    dn_manager_ = std::make_shared<DatanodeManager>(false);
    block_report_manager_ = std::make_shared<BlockReportManager>();
    dn_size_ = 1000;
    // according to datanode manager requirement
    r_thread_num_ = FLAGS_client_normal_rpc_handler_count;
    w_thread_num_ = FLAGS_client_slow_rpc_handler_count;
    loop_num_ = 100000;

    block_manager_->set_datanode_manager(dn_manager_);
    block_manager_->set_block_report_manager(block_report_manager_.get());
    dn_manager_->set_block_manager(block_manager_.get());
    block_report_manager_->Start(reinterpret_cast<NameSpace*>(0x1),
                                 dn_manager_.get(),
                                 block_manager_.get());
    block_report_manager_->StartStandby();

    LOG(INFO) << "Mock DN";

    for (int i = 0; i < dn_size_; ++i) {
      auto dn = MockDatanode(i);
      dn_manager_->Register(dn.first.datanodeid(), &dn.first, dn.second);
    }
    LOG(INFO) << "SetUp cost=" << sw_setup.NextStepTime();
  }

  void TearDown() override {
    block_report_manager_->StopStandby();
    block_report_manager_->Stop();
  }

  std::shared_ptr<BlockManager> block_manager_;

  std::shared_ptr<DatanodeManager> dn_manager_;
  int dn_size_{0};
  int r_thread_num_{0};
  int w_thread_num_{0};
  int loop_num_{0};
  int sleep_us{10};

  std::shared_ptr<BlockReportManager> block_report_manager_;

  CountDownLatch latch_{0};
  std::atomic<int> thread_index_{0};

  DatanodeID GetDnId(int32_t num) {
    return static_cast<DatanodeID>(num % dn_size_ + 1);
  }

  std::pair<cloudfs::datanode::DatanodeRegistrationProto, cnetpp::base::IPAddress>
  MockDatanode(uint32_t id,
               const std::string ip_str = "",
               const std::string uuid = "") {
    cnetpp::base::IPAddress ip;
    if (ip_str.empty()) {
      auto& address = ip.mutable_address();
      address.resize(cnetpp::base::IPAddress::kIPv4AddressSize);
      address[0] = 10;
      address[1] = (id >> 16) & 255;
      address[2] = (id >> 8) & 255;
      address[3] = (id >> 0) & 255;
    } else {
      ip = cnetpp::base::IPAddress(ip_str);
    }

    cloudfs::datanode::DatanodeRegistrationProto reg;
    reg.mutable_datanodeid()->set_ipaddr(ip.ToString());
    reg.mutable_datanodeid()->set_hostname(ip.ToString());
    reg.mutable_datanodeid()->set_datanodeuuid(
        uuid == "" ? ip.ToString() : uuid);
    reg.mutable_datanodeid()->set_xferport(0);
    reg.mutable_datanodeid()->set_infoport(0);
    reg.mutable_datanodeid()->set_ipcport(0);

    reg.mutable_storageinfo()->set_layoutversion(0);
    reg.mutable_storageinfo()->set_namespaceid(0);
    reg.mutable_storageinfo()->set_clusterid("");
    reg.mutable_storageinfo()->set_ctime(0);

    reg.mutable_keys()->set_isblocktokenenabled(false);
    reg.mutable_keys()->set_keyupdateinterval(0);
    reg.mutable_keys()->set_tokenlifetime(0);
    reg.mutable_keys()->mutable_currentkey()->set_keyid(0);
    reg.mutable_keys()->mutable_currentkey()->set_expirydate(0);

    reg.set_softwareversion("");
    return {reg, ip};
  }

  using WorkFunc = std::function<uint64_t(int)>;
  using WrapperFunc = std::function<bool(const WorkFunc &)>;
  WrapperFunc rthread = [&](const WorkFunc& work) {
    auto tid = thread_index_.fetch_add(1);
    LOG(INFO) << "RThread(" << tid << ")" << " Start";

    StopWatch sw_task;
    sw_task.Start();

    uint64_t sleep_time = 0;
    uint64_t ans = 0;
    int cnt = 0;
    while (cnt < loop_num_) {
      ans += work(cnt);
      ++cnt;
      // sleep
      if (sleep_us > 0) {
        StopWatch sw_sleep;
        sw_sleep.Start();
        for (int i = 0; i < sleep_us * 1000; ++i) {
          MemoryPause();
        }
        sleep_time += sw_sleep.NextStepTime();
      }
    }

    auto duration = sw_task.NextStepTime();
    auto cost = duration - sleep_time;
    LOG(INFO) << "RThread(" << tid << ")"
              << " ans=" << ans
              << " cnt=" << cnt
              << " duration=" << duration
              << " sleep_time=" << sleep_time
              << " cost=" << cost
              << " avg=" << cost / cnt;
    latch_.CountDown();
    return true;
  };
  WrapperFunc wthread = [&](const WorkFunc& work) {
    auto tid = thread_index_.fetch_add(1);
    LOG(INFO) << "WThread(" << tid << ")" << " Start";

    StopWatch sw_task;
    sw_task.Start();

    uint64_t sleep_time = 0;
    int64_t ans = 0;
    int cnt = 0;
    while (cnt < loop_num_) {
      ans += work(cnt);
      ++cnt;
      // sleep
      if (sleep_us > 0) {
        StopWatch sw_sleep;
        sw_sleep.Start();
        for (int i = 0; i < sleep_us * 1000; ++i) {
          MemoryPause();
        }
        sleep_time += sw_sleep.NextStepTime();
      }
    }

    auto duration = sw_task.NextStepTime();
    auto cost = duration - cnt * sleep_us;
    LOG(INFO) << "WThread(" << tid << ")"
              << " ans=" << ans
              << " cnt=" << cnt
              << " duration=" << duration
              << " sleep_time=" << sleep_time
              << " cost=" << cost
              << " avg=" << cost / cnt;
    latch_.CountDown();
    return true;
  };

  void Test(const std::string name,
            int rthread_num,
            int wthread_num,
            const WorkFunc& rtask,
            const WorkFunc& wtask) {
    StopWatch sw;
    sw.Start();
    r_thread_num_ = rthread_num;
    w_thread_num_ = wthread_num;
    const auto thread_num = r_thread_num_ + w_thread_num_;

    LOG(INFO) << "dn_size_=" << dn_size_;
    LOG(INFO) << "r_thread_num_=" << r_thread_num_;
    LOG(INFO) << "w_thread_num_=" << w_thread_num_;
    LOG(INFO) << "thread_num=" << thread_num;
    LOG(INFO) << "loop_num_=" << loop_num_;

    latch_.Reset(thread_num);

    auto pool = std::make_unique<cnetpp::concurrency::ThreadPool>(
        "Test-" + name);
    pool->set_num_threads(thread_num);
    pool->Start();

    LOG(INFO) << "Start cost=" << sw.NextStepTime();
    for (int i = 0; i < r_thread_num_; ++i) {
      pool->AddTask([&]() {
        rthread(rtask);
        return true;
      });
    }
    for (int i = 0; i < w_thread_num_; ++i) {
      pool->AddTask([&]() {
        wthread(wtask);
        return true;
      });
    }
    LOG(INFO) << "Committed cost=" << sw.NextStepTime();

    latch_.Await();
    auto duration = sw.NextStepTime();
    auto cost = duration - loop_num_ * sleep_us;
    LOG(INFO) << "latch await duration=" << duration;
    LOG(INFO) << "Result cnt=" << loop_num_ << " cost=" << cost << " avg="
              << cost / loop_num_;

    pool->Stop();
    LOG(INFO) << "stop thread_pool cost=" << sw.NextStepTime();
  }


  WorkFunc NoFunc = [&](const int &cnt) {
    return 1;
  };
  WorkFunc RFuncGetDN = [&](const int &cnt) {
    return reinterpret_cast<uint64_t>(dn_manager_->GetDatanodeFromId(
        GetDnId(cnt)));
  };
  WorkFunc WFuncGetDN = [&](const int &cnt) {
    auto dn = MockDatanode(dn_size_ + cnt % dn_size_);
    dn_manager_->Register(dn.first.datanodeid(), &dn.first, dn.second);
    return 1;
  };
  WorkFunc RFuncIsLocal = [&](const int &cnt) {
    auto dn = dn_manager_->GetDatanodeFromId(GetDnId(cnt));
    NetworkLocation loc;
    loc.dc = "test-dc";
    loc.rack = "test-rack";
    bool same_rack = false;
    bool same_dc = false;
    uint32_t dc_distance = 0;
    auto rtn = dn_manager_->placement()->IsLocal(dn,
                                                 loc,
                                                 &same_rack,
                                                 &same_dc,
                                                 &dc_distance);

    return (rtn + 1) * (same_rack + 1) * (same_dc + 1) * (dc_distance + 1);
  };
  WorkFunc RFuncResolve = [&](const int &cnt) {
    auto dnreg = MockDatanode(cnt);
    auto dn_info = std::make_unique<DatanodeInfo>(cnt,
                                                  dnreg.first.datanodeid(),
                                                  dnreg.second);
    return reinterpret_cast<uint64_t>(dn_info.get());
  };
  WorkFunc RFuncNameDC = [&](const int &cnt) {
    auto dn = dn_manager_->GetDatanodeFromId(GetDnId(cnt));
    auto dc = GetGlobalDataCenterTable().Name(dn->dc());
    uint64_t ret = reinterpret_cast<uint64_t>(&dc);
    return ret;
  };

};

TEST_F(DatanodeManagerPerfTest, GetDNReaderOnly) {
  Test("GetDNReaderOnly",
       r_thread_num_ * 2, 0,
       RFuncGetDN, NoFunc);
}

TEST_F(DatanodeManagerPerfTest, GetDNReaderWithOneWriter) {
  Test("GetDNReaderWithOneWriter",
       r_thread_num_ * 2, 1,
       RFuncGetDN, WFuncGetDN);
}

TEST_F(DatanodeManagerPerfTest, ResolveReaderOnly) {
  Test("ResolveReaderOnly",
       r_thread_num_ * 2, 0,
       RFuncResolve, NoFunc);
}

TEST_F(DatanodeManagerPerfTest, NameDCReaderOnly) {
  Test("NameDCReaderOnly",
       r_thread_num_ * 2, 0,
       RFuncNameDC, NoFunc);
}

TEST_F(DatanodeManagerPerfTest, IsLocalReaderOnly) {
  Test("IsLocalReaderOnly",
       r_thread_num_ * 2, 0,
       RFuncIsLocal, NoFunc);
}

TEST_F(DatanodeManagerPerfTest, IsLocalSingleReader) {
  Test("IsLocalSingleReader",
       1, 0,
       RFuncIsLocal, NoFunc);
}

}  // namespace dancenn

