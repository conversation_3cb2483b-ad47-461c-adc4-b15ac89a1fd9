// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "rpc/test_service.h"

#include <csignal>
#include <memory>
#include <vector>

#include "base/defer.h"
#include "rpc/rpc_client_options.h"
#include "rpc/pooled_rpc_channel.h"
#include "rpc/failovered_rpc_channel.h"
#include "rpc/stateless_rpc_channel.h"

namespace dancenn {

std::shared_ptr<RpcServer> CreateServer(
    int port,
    std::function<void(RpcController*,
                       const ::cloudfs::EchoRequestProto*,
                       ::cloudfs::EchoResponseProto*,
                       ::google::protobuf::Closure*)> runner) {
  if (signal(SIGPIPE, SIG_IGN) == SIG_ERR) {
    LOG(FATAL) << "Failed to ignore SIGPIPE";
    exit(EXIT_FAILURE);
  }

  auto server = std::make_shared<RpcServer>();
  cnetpp::base::EndPoint ep1("0.0.0.0", port);
  RpcServerOptions options;
  options.set_backlog(10000);
  options.set_send_buffer_size(65536);
  options.set_receive_buffer_size(65536);
  options.set_tcp_receive_buffer_size(65536);
  options.set_tcp_send_buffer_size(65536);
  options.set_handler_count(10);
  options.set_network_thread_count(10);
  options.set_rpc_server_name("test");
  options.set_backlog(10000);
  auto service = std::static_pointer_cast<google::protobuf::Service>(
      std::make_shared<TestService>(runner));
  auto sm = std::make_shared<ServiceMeta>(service, 0);
  server->RegisterService(
      "org.apache.hadoop.hdfs.protocol.TestProtocol", sm);
  server->Launch(ep1, options);
  return server;
}

static void ConstructRpcClientOptions(RpcClientOptions* options) {
  assert(options);

  options->set_send_buffer_size(65536);
  options->set_receive_buffer_size(65536);
  options->set_tcp_receive_buffer_size(65536);
  options->set_tcp_send_buffer_size(65536);
  options->set_network_thread_count(4);
  options->set_client_id("rpc_test");
  options->set_user("test");
  options->set_protocol_name("org.apache.hadoop.hdfs.protocol.TestProtocol");
  options->set_protocol_version(1);
}

std::shared_ptr<PooledRpcChannel> CreatePooledRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const cnetpp::base::EndPoint& ep,
    RpcClientOptions* options) {
  bool release = false;
  if (!options) {
    options = new RpcClientOptions();
    release = true;
  }
  DEFER([&] () { if (release) delete options; });

  ConstructRpcClientOptions(options);

  auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("clihndl");
  handlers->set_num_threads(1);
  handlers->Start();
  return std::make_shared<PooledRpcChannel>(tcp_client,
                                            *options,
                                            ep,
                                            handlers);
}

std::shared_ptr<FailoveredRpcChannel> CreateFailoveredRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options) {
  bool release = false;
  if (!options) {
    options = new RpcClientOptions();
    release = true;
  }
  DEFER([&] () { if (release) delete options; });

  ConstructRpcClientOptions(options);

  auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("clihndl");
  handlers->set_num_threads(1);
  handlers->Start();
  return std::make_shared<FailoveredRpcChannel>(tcp_client,
                                                *options,
                                                eps,
                                                handlers);
}

std::unique_ptr<FailoveredRpcChannel> CreateUniqueFailoveredRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options) {
  bool release = false;
  if (!options) {
    options = new RpcClientOptions();
    release = true;
  }
  DEFER([&] () { if (release) delete options; });

  ConstructRpcClientOptions(options);

  auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("clihndl");
  handlers->set_num_threads(1);
  handlers->Start();
  return std::make_unique<FailoveredRpcChannel>(tcp_client,
                                                *options,
                                                eps,
                                                handlers);
}


std::shared_ptr<StatelessRpcChannel> CreateStatelessRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options) {
  bool release = false;
  if (!options) {
    options = new RpcClientOptions();
    release = true;
  }
  DEFER([&] () { if (release) delete options; });

  ConstructRpcClientOptions(options);

  auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("clihndl");
  handlers->set_num_threads(1);
  handlers->Start();
  return std::make_shared<StatelessRpcChannel>(tcp_client,
                                               *options,
                                               eps,
                                               handlers);
}

}  // namespace dancenn

