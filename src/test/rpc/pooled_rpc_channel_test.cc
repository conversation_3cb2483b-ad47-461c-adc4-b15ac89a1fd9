// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include <memory>

#include "TestProtocol.pb.h"
#include "rpc/rpc_server.h"
#include "rpc/pooled_rpc_channel.h"
#include "test_service.h"
#include "base/count_down_latch.h"

namespace dancenn {

class PooledRpcChannelTest : public TestChannelBase {
 public:
  void SetUp() override {
    server_ = CreateServer(server_port_);

    RpcClientOptions options;
    options.set_max_open_connections_per_user_and_fs(10);
    options.set_max_pending_calls_per_user_and_fs(1100);

    tcp_client_ = std::make_shared<cnetpp::tcp::TcpClient>();
    tcp_client_->Launch("tcpcli");

    channel_ = CreatePooledRpcChannel(
        tcp_client_,
        cnetpp::base::EndPoint("127.0.0.1", server_port_),
        &options);

    client_.reset(new TestService::Stub(channel_.get()));
  }

  void TearDown() override {
    server_->Shutdown();
    channel_->Shutdown();
    tcp_client_->Shutdown();
  }

  void ConfigureSyncController(RpcController* controller) override {
    controller->set_retry_count(10);
  }

  int server_port_ { 8545 };
  std::shared_ptr<RpcServer> server_;

  std::shared_ptr<PooledRpcChannel> channel_;
};

TEST_F(PooledRpcChannelTest, SyncSimplePingPongConcurrently) {
  auto workers = std::make_shared<cnetpp::concurrency::ThreadPool>("");
  workers->set_num_threads(20);
  workers->Start();

  int n = 100;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    workers->AddTask([&, i]() -> bool {
      EchoSuccess(i);
      latch.CountDown();
      return true;
    });
  }

  latch.Await();
}

TEST_F(PooledRpcChannelTest, SyncServerFailed) {
  ASSERT_TRUE(server_->Shutdown());

  auto workers = std::make_shared<cnetpp::concurrency::ThreadPool>("");
  workers->set_num_threads(20);
  workers->Start();

  int n = 100;
  CountDownLatch latch(n);

  for (int i = 0; i < n; ++i) {
    workers->AddTask([&, i]() -> bool {
      EchoNetworkError(i);
      latch.CountDown();
      return true;
    });
  }

  latch.Await();
}

TEST_F(PooledRpcChannelTest, AsyncSimplePingPongConcurrently) {
  int n = 1100;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    RpcController *controller = new RpcController();
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    std::string payload = "testpayload" + std::to_string(i);
    request->set_payload(payload);
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, request, response, controller] {
                    // We must count down the latch first, because if ASSERT_XX
                    // failed, there is no chance to count down any more
                    latch.CountDown();
                    ASSERT_EQ(RpcStatus::kSuccess, controller->status());
                    ASSERT_EQ(request->payload(), response->payload());
                  }));
  }

  latch.Await();
  // In case of main thread quits first.
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

TEST_F(PooledRpcChannelTest, AsyncServerFailed) {
  ASSERT_TRUE(server_->Shutdown());

  int n = 1100;
  CountDownLatch latch(n);
  for (int i = 0; i < n; ++i) {
    auto controller = new RpcController();
    controller->set_retry_count(10);
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    request->set_payload("2222");
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, controller, request, response] {
                    // We must count down the latch first, because if ASSERT_XX
                    // failed, there is no chance to count down any more
                    latch.CountDown();
                    ASSERT_EQ(RpcStatus::kNetworkError, controller->status());
                  }));
  }
  latch.Await();
  // In case of main thread quits first.
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

}  // namespace dancenn

