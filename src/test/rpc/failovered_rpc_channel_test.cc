// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>

#include "TestProtocol.pb.h"
#include "base/count_down_latch.h"
#include "base/java_exceptions.h"
#include "rpc/rpc_server.h"
#include "rpc/stateless_rpc_channel.h"
#include "rpc/test_service.h"

namespace dancenn {

class FailoveredRpcChannelTest : public TestChannelBase {
 public:
  void SetUp() override {
    // standby server
    server1_ = CreateServer(
        server_port1_,
        [&] (RpcController* controller,
             const ::cloudfs::EchoRequestProto* request,
             ::cloudfs::EchoResponseProto* response,
             ::google::protobuf::Closure* done) {
          controller->MarkAsFailed(JavaExceptions::StandbyException(),
                                   "Standby server");
          done->Run();
        });

    // active server
    server2_ = CreateServer(server_port2_);

    RpcClientOptions options;
    options.set_max_open_connections_per_user_and_fs(10);
    options.set_max_pending_calls_per_user_and_fs(1100);

    tcp_client_ = std::make_shared<cnetpp::tcp::TcpClient>();
    tcp_client_->Launch("tcpcli");

    std::vector<cnetpp::base::EndPoint> eps;
    eps.emplace_back("127.0.0.1", server_port1_);
    eps.emplace_back("127.0.0.1", server_port2_);

    channel_ = CreateFailoveredRpcChannel(tcp_client_, eps, &options);

    client_.reset(new TestService::Stub(channel_.get()));
  }

  void TearDown() override {
    server1_->Shutdown();
    server2_->Shutdown();
    channel_->Shutdown();
    tcp_client_->Shutdown();
  }

  void ConfigureSyncController(RpcController* controller) override {
    controller->set_retry_count(2);
  }

  int server_port1_ { 8545 };
  int server_port2_ { 8546 };
  std::shared_ptr<RpcServer> server1_;
  std::shared_ptr<RpcServer> server2_;

  std::shared_ptr<FailoveredRpcChannel> channel_;
};

TEST_F(FailoveredRpcChannelTest, AsyncPingPongConcurrently) {
  int n = 1;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    RpcController *controller = new RpcController();
    controller->set_retry_count(2);
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    std::string payload = "testpayload" + std::to_string(i);
    request->set_payload(payload);
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, request, response, controller] {
                    // We must count down the latch first, because if ASSERT_XX
                    // failed, there is no chance to count down any more
                    latch.CountDown();
                    ASSERT_EQ(RpcStatus::kSuccess, controller->status());
                    ASSERT_EQ(request->payload(), response->payload());
                  }));
  }

  latch.Await(std::chrono::milliseconds(5000));
  // In case of main thread quits first.
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

TEST_F(FailoveredRpcChannelTest, AsyncServerFailed) {
  ASSERT_TRUE(server1_->Shutdown());
  ASSERT_TRUE(server2_->Shutdown());

  int n = 1100;
  CountDownLatch latch(n);
  for (int i = 0; i < n; ++i) {
    auto controller = new RpcController();
    controller->set_retry_count(10);
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    request->set_payload("2222");
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, controller, request, response] {
                    // We must count down the latch first, because if ASSERT_XX
                    // failed, there is no chance to count down any more
                    latch.CountDown();
                    if (controller->status() != RpcStatus::kNetworkError) {
                      ASSERT_EQ(RpcStatus::kNetworkError, controller->status());
                    }
                  }));
  }
  latch.Await();
// In case of main thread quits first.
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

TEST_F(FailoveredRpcChannelTest, SyncPingPongConcurrently) {
  auto workers = std::make_shared<cnetpp::concurrency::ThreadPool>("");
  workers->set_num_threads(10);
  workers->Start();

  int n = 10000;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    workers->AddTask([&, i]() -> bool {
      EchoSuccess(i);
      latch.CountDown();
      return true;
    });
  }

  latch.Await();
}

TEST_F(FailoveredRpcChannelTest, SyncServerFailed) {
  ASSERT_TRUE(server1_->Shutdown());
  ASSERT_TRUE(server2_->Shutdown());

  auto workers = std::make_shared<cnetpp::concurrency::ThreadPool>("");
  workers->set_num_threads(10);
  workers->Start();

  int n = 10;
  CountDownLatch latch(n);

  for (int i = 0; i < 10; ++i) {
    workers->AddTask([&, i]() -> bool {
      EchoNetworkError(i);
      latch.CountDown();
      return true;
    });
  }

  latch.Await();
}


TEST_F(FailoveredRpcChannelTest, AsyncPingPongConcurrentlyWithMultipleUser) {
  int n = 10;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    RpcController *controller = new RpcController();
    controller->set_retry_count(2);
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    std::string payload = "testpayload" + std::to_string(i);
    request->set_payload(payload);
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, request, response, controller] {
                    // We must count down the latch first, because if ASSERT_XX
                    // failed, there is no chance to count down any more
                    latch.CountDown();
                    ASSERT_EQ(RpcStatus::kSuccess, controller->status());
                    ASSERT_EQ(request->payload(), response->payload());
                  }));
  }

  latch.Await(std::chrono::milliseconds(5000));
  // In case of main thread quits first.
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}


}  // namespace dancenn


