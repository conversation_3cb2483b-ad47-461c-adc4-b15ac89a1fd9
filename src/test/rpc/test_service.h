// Copyright 2017 He <PERSON> <<EMAIL>>

#ifndef TEST_RPC_TEST_SERVICE_H_
#define TEST_RPC_TEST_SERVICE_H_

#include <cnetpp/base/end_point.h>
#include <google/protobuf/service.h>
#include <gtest/gtest.h>

#include <memory>
#include <utility>
#include <string>
#include <vector>

#include <TestProtocol.pb.h>

#include "rpc/rpc_server_options.h"
#include "rpc/rpc_server.h"
#include "rpc/pooled_rpc_channel.h"
#include "rpc/failovered_rpc_channel.h"
#include "rpc/stateless_rpc_channel.h"

namespace dancenn {

class Closure0 : public google::protobuf::Closure {
 public:
  explicit Closure0(std::function<void()> func) : func_(std::move(func)) {}
  ~Closure0() override = default;

  void Run() override {
    func_();
  }

 private:
  std::function<void()> func_;
};

class TestService : public cloudfs::TestService {
 public:
  explicit TestService(
      std::function<void(RpcController*,
                         const ::cloudfs::EchoRequestProto*,
                         ::cloudfs::EchoResponseProto*,
                         ::google::protobuf::Closure*)> runner = nullptr)
      : runner_(runner) {}
  ~TestService() override {}

  TestService(const TestService&) = delete;
  TestService& operator=(const TestService&) = delete;

  void echo(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::EchoRequestProto* request,
      ::cloudfs::EchoResponseProto* response,
      ::google::protobuf::Closure* done) override {
    if (runner_) {
      runner_(static_cast<RpcController*>(controller), request, response, done);
    } else {
      VLOG(10) << "Active server: " << request->payload();
      response->set_payload(request->payload());
      done->Run();
    }
  }

  std::function<void(RpcController*,
                     const ::cloudfs::EchoRequestProto*,
                     ::cloudfs::EchoResponseProto*,
                     ::google::protobuf::Closure*)> runner_;
};

class TestChannelBase : public testing::Test {
 public:
  void EchoSuccess(int i) {
    RpcController controller;
    ConfigureSyncController(&controller);
    cloudfs::EchoRequestProto request;
    cloudfs::EchoResponseProto response;
    std::string payload = "testpayload" + std::to_string(i);
    request.set_payload(payload);
    response.Clear();
    client_->echo(&controller, &request, &response, nullptr);
    ASSERT_FALSE(controller.Failed());
    ASSERT_EQ(RpcStatus::kSuccess, controller.status());
    ASSERT_EQ(payload, response.payload());
  }

  void EchoNetworkError(int i) {
    RpcController controller;
    ConfigureSyncController(&controller);
    cloudfs::EchoRequestProto request;
    cloudfs::EchoResponseProto response;
    request.set_payload("testpayload" + std::to_string(i));
    client_->echo(&controller, &request, &response, nullptr);
    std::cout << static_cast<int>(controller.status()) << std::endl;
    if (static_cast<int>(controller.status()) == 0) {
      ASSERT_EQ(RpcStatus::kNetworkError, controller.status());
    }
  }

  virtual void ConfigureSyncController(RpcController* controller) {
    (void) controller;
  }

  std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client_;
  std::shared_ptr<TestService::Stub> client_;
};

extern std::shared_ptr<RpcServer> CreateServer(
    int port,
    std::function<void(RpcController*,
                       const ::cloudfs::EchoRequestProto*,
                       ::cloudfs::EchoResponseProto*,
                       ::google::protobuf::Closure*)> runner = nullptr);

extern std::shared_ptr<PooledRpcChannel> CreatePooledRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const cnetpp::base::EndPoint& ep,
    RpcClientOptions* options = nullptr);

extern std::shared_ptr<FailoveredRpcChannel> CreateFailoveredRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options);

extern std::unique_ptr<FailoveredRpcChannel> CreateUniqueFailoveredRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options);

extern std::shared_ptr<StatelessRpcChannel> CreateStatelessRpcChannel(
    std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client,
    const std::vector<cnetpp::base::EndPoint>& eps,
    RpcClientOptions* options);

}  // namespace dancenn

#endif  // TEST_RPC_TEST_SERVICE_H_

