// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>

#include "TestProtocol.pb.h"
#include "base/count_down_latch.h"
#include "base/java_exceptions.h"
#include "rpc/rpc_server.h"
#include "rpc/stateless_rpc_channel.h"
#include "rpc/test_service.h"

namespace dancenn {

class FailoveredRpcChannelTest2 : public TestChannelBase {
 public:
  void SetUp() override {
    // standby server
    server1_ = CreateServer(
        server_port1_,
        [&] (RpcController* controller,
             const ::cloudfs::EchoRequestProto* request,
             ::cloudfs::EchoResponseProto* response,
             ::google::protobuf::Closure* done) {
          controller->MarkAsFailed(JavaExceptions::StandbyException(),
                                   "Standby server");
          done->Run();
        });

    // active server
    server2_ = CreateServer(server_port2_);

    RpcClientOptions options;
    options.set_max_open_connections_per_user_and_fs(10);
    options.set_max_pending_calls_per_user_and_fs(1100);

    tcp_client_ = std::make_shared<cnetpp::tcp::TcpClient>();
    tcp_client_->Launch("tcpcli");

    std::vector<cnetpp::base::EndPoint> eps;
    eps.emplace_back("127.0.0.1", server_port1_);
    eps.emplace_back("127.0.0.1", server_port2_);

    channel_ = CreateUniqueFailoveredRpcChannel(tcp_client_, eps, &options);

    // stub owns channel, we will
    // release it manully to simulate clearing idle upstreams.
    client_.reset(new TestService::Stub(channel_.release(),
        ::google::protobuf::Service::STUB_OWNS_CHANNEL));
  }

  void TearDown() override {
    server1_->Shutdown();
    server2_->Shutdown();
    tcp_client_->Shutdown();
  }

  void ConfigureSyncController(RpcController* controller) override {
    controller->set_retry_count(2);
  }

  int server_port1_ { 8545 };
  int server_port2_ { 8546 };
  std::shared_ptr<RpcServer> server1_;
  std::shared_ptr<RpcServer> server2_;

  std::unique_ptr<FailoveredRpcChannel> channel_;
};


// pure function bug: channel is deleted before when it is used.
// FailoveredRpcChannel delete -> ClientCall Done -> this.func (corrupted)
TEST_F(FailoveredRpcChannelTest2, RpcUpStreamReleaseBugFix) {
  int n = 1;
  CountDownLatch latch(n);
  for (int i = 0; i < n; i++) {
    RpcController *controller = new RpcController();
    controller->set_retry_count(2);
    auto request = new cloudfs::EchoRequestProto();
    auto response = new cloudfs::EchoResponseProto();
    std::string payload = "testpayload" + std::to_string(i);
    request->set_payload(payload);
    response->Clear();
    client_->echo(controller, request, response,
                  new Closure0([&latch, request, response, controller] {
                    latch.CountDown();
                    ASSERT_EQ(RpcStatus::kSuccess, controller->status());
                    ASSERT_EQ(request->payload(), response->payload());
                  }));
  }
  // we simulate to delete the FailoveredRpcChannel
  FailoveredRpcChannel* failoveredRpcChannel =
      static_cast<FailoveredRpcChannel*>(client_->channel());

  // comment out the following line, this will fail
  failoveredRpcChannel->Shutdown();
  client_.reset();

  latch.Await(std::chrono::milliseconds(500000));
}

}  // namespace dancenn


