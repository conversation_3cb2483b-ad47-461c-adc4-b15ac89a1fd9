// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>
#include <cnetpp/tcp/tcp_server.h>
#include <cnetpp/tcp/tcp_client.h>
#include <cnetpp/tcp/tcp_connection.h>

#include <mutex>
#include <memory>
#include <thread>
#include <cnetpp/base/string_utils.h>

#include "base/count_down_latch.h"
#include "hdfs.pb.h"

#if 0
// test case:
// message LocatedBlockProto {
//   required ExtendedBlockProto b  = 1;
//   required uint64 offset = 2;
//   repeated DatanodeInfoProto locs = 3;
//   required bool corrupt = 4;
//   required hadoop.common.TokenProto blockToken = 5;
//   repeated bool isCached = 6 [packed=true];
//   repeated StorageTypeProto storageTypes = 7;
//   repeated string storageIDs = 8;
//   optional DatanodeInfoWithStorageProto replicaPipeliner = 9;
//   optional LocatedBlockProto originalLocatedBlock = 10;
// }
static cloudfs::LocatedBlockProto ConstructMessage(int id,
                                                        int genstamp,
                                                        int num_bytes) {
  cloudfs::LocatedBlockProto msg;
  auto b = msg.mutable_b();
  b->set_poolid("BP-test");
  b->set_blockid(id);
  b->set_generationstamp(genstamp);
  b->set_numbytes(num_bytes);
  msg.set_offset(0);
  auto locs = msg.add_locs();
  auto loc_id = locs->mutable_id();
  loc_id->set_ipaddr("0.0.0.0");
  loc_id->set_hostname("localhost");
  loc_id->set_datanodeuuid("xxxx-xx-x-xxxx-xx");
  loc_id->set_xferport(8888);
  loc_id->set_infoport(8889);
  loc_id->set_ipcport(8890);
  msg.set_corrupt(false);
  auto bt = msg.mutable_blocktoken();
  bt->set_identifier("identifier");
  bt->set_password("\x01\x01\x01\x01identifier");
  bt->set_kind("kind");
  bt->set_service("service");
  msg.add_iscached(true);
  msg.add_iscached(true);
  msg.add_iscached(false);
  msg.add_storagetypes(cloudfs::StorageTypeProto::DISK);
  msg.add_storagetypes(cloudfs::StorageTypeProto::SSD);
  msg.add_storageids()->append("storage1");
  msg.add_storageids()->append("storage2");
  msg.add_storageids()->append("storage3");
  return msg;
}

class ClientServerTest : public testing::Test {
  void SetUp() {
  }
  void TearDown() {
  }
};

TEST_F(ClientServerTest, Test01) {
  cnetpp::tcp::TcpServer server;
  cnetpp::tcp::TcpServerOptions options;
  struct ServerConnectionInfo {
    std::shared_ptr<cnetpp::tcp::TcpConnection> conn_;
    uint32_t body_length_ { 0 };
    int state_ { 0 };  // 0 waiting body length; 1 waiting body
  };

  std::mutex mu;
  std::map<cnetpp::tcp::ConnectionId,
      std::shared_ptr<ServerConnectionInfo>> conns;

  options.set_connected_callback(
      [&] (std::shared_ptr<cnetpp::tcp::TcpConnection> conn) -> bool {
        std::lock_guard<std::mutex> guard(mu);
        CHECK(conns.find(conn->id()) == conns.end())
            << "Connection has already been existed!!";
        auto info = std::make_shared<ServerConnectionInfo>();
        info->conn_ = conn;
        info->body_length_ = 0;
        conns.emplace(conn->id(), info);
        return true;
      });

  options.set_received_callback(
      [&] (std::shared_ptr<cnetpp::tcp::TcpConnection> conn) -> bool {
        std::unique_lock<std::mutex> guard(mu);
        auto itr = conns.find(conn->id());
        CHECK(itr != conns.end()) << "Connection doesn't exist!!";
        auto info = itr->second;
        mu.unlock();
        auto& buf = conn->mutable_recv_buffer();
        while (true) {
          switch (info->state_) {
            case 0: {
              info->body_length_ = 0;
#if 1  // varint length
              uint32_t length;
              auto res = buf.ReadVarint32(&length);
              CHECK(res >= 0) << "Failed to read varint32!!";
              if (res > 0) {
                info->body_length_ = length;
                info->state_ = 1;
                LOG(INFO) << "received body length: " << info->body_length_;
                break;
              } else {
                LOG(INFO) << "Body length no enougth data.";
                return true;
              }
#else  // fixed length
              uint32_t length;
              if (!buf.ReadUint32(&length)) {
                return true;
              } else {
                info->body_length_ = length;
                info->state_ = 1;
                break;
              }
#endif
            }
            case 1: {
              CHECK(info->body_length_ > 0) << "Invalid state!!";
              std::string data;
              if (!buf.Read(&data, info->body_length_)) {
                LOG(INFO) << "Body length no enougth data.";
                return true;
              } else {
                CHECK(data.length() == info->body_length_)
                    << ", body_length: " << info->body_length_
                    << ", data length: " << data.length();
                cloudfs::LocatedBlockProto proto;
                CHECK(proto.ParseFromString(data)) << "Invalid state!!"
                    << ", body_length: " << info->body_length_
                    << ", data length: " << data.length()
                    << ", data: " << data;
                LOG(INFO) << "Finished one call, connection is: " << conn->id();
                info->body_length_ = 0;
                info->state_ = 0;
                LOG(INFO) << "Pending data size: " << buf.Size();
                LOG(INFO) << "data is: " << proto.ShortDebugString();
                break;
              }
            }
            default:
              CHECK(1 == 0) << "Should never happen!!";
          }
        }
        return true;
      });

  options.set_closed_callback(
      [&] (std::shared_ptr<cnetpp::tcp::TcpConnection> conn) -> bool {
        std::unique_lock<std::mutex> guard(mu);
        auto itr = conns.find(conn->id());
        CHECK(itr != conns.end()) << "Connection doesnt' exist!!";
        conns.erase(itr);
        return true;
      });

  options.set_backlog(1024);
  options.set_worker_count(4);

  ASSERT_TRUE(server.Launch(cnetpp::base::EndPoint("0.0.0.0", 8010), options));

  std::vector<std::thread> threads;
  for (int i = 0; i < 1; ++i) {
    threads.emplace_back([i]() {
      cnetpp::base::TcpSocket socket;
      ASSERT_TRUE(socket.Create(false));
      ASSERT_TRUE(socket.Connect(cnetpp::base::EndPoint("0.0.0.0", 8010)));
      for (int j = 0; j < 100000; ++j) {
        auto proto = ConstructMessage(i, j, 100);
        auto str_proto = proto.SerializeAsString();
        char buf[10];
#if 1
        auto buf_len = cnetpp::base::StringUtils::ToVarint32(
            str_proto.length(), buf);
        ASSERT_TRUE(socket.SendAll(buf, buf_len, 0, true));
#else
        cnetpp::base::StringUtils::PutUint32(
            htonl(static_cast<uint32_t>(str_proto.length())), buf);
        ASSERT_TRUE(socket.SendAll(buf, sizeof(uint32_t), 0, true));
#endif
        ASSERT_TRUE(
            socket.SendAll(str_proto.data(), str_proto.length(), 0, true));
        if (j % 100 == 0) {
          std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
      }
      socket.Close();
    });
  }

  for (auto& t : threads) {
    t.join();
  }
  server.Shutdown();
}
#endif

