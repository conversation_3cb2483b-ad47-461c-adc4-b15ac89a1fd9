// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <cnetpp/tcp/tcp_client.h>
#include <chrono>
#include <thread>

#include "service/client_namenode_service.h"
#include "rpc/rpc_client_options.h"
#include "rpc/failovered_rpc_channel.h"
#include "base/count_down_latch.h"

#if 0

class HdfsClientTest : public testing::Test {
 public:
  void SetUp() override {
    dancenn::RpcClientOptions options;
    options.set_max_open_connections_per_user_and_fs(10);
    options.set_max_pending_calls_per_user_and_fs(1100);
    options.set_request_timeout_ms(1000000);
    options.set_send_buffer_size(65536);
    options.set_receive_buffer_size(65536);
    options.set_tcp_receive_buffer_size(65536);
    options.set_tcp_send_buffer_size(65536);
    options.set_network_thread_count(4);
    options.set_client_id("rpc_test_0000001");
    options.set_user("tiger");
    options.set_protocol_name("org.apache.hadoop.hdfs.protocol.ClientProtocol");
    options.set_protocol_version(1);

    tcp_client_ = std::make_shared<cnetpp::tcp::TcpClient>();
    tcp_client_->Launch("tcpcli");

    auto handlers = std::make_shared<cnetpp::concurrency::ThreadPool>("clihndl");
    handlers->set_num_threads(1);
    handlers->Start();

    endpoint_ = std::make_shared<cnetpp::base::EndPoint>("10.26.62.203", 5060);
    channel_ = std::make_shared<dancenn::PooledRpcChannel>(tcp_client_,
                                                           options,
                                                           *endpoint_,
                                                           handlers);

    client_.reset(
        new dancenn::ClientNamenodeService::Stub(channel_.get()));
  }
  void TearDown() override {
    channel_->Shutdown();
    tcp_client_->Shutdown();
  }

  std::shared_ptr<cnetpp::tcp::TcpClient> tcp_client_;
  std::shared_ptr<dancenn::PooledRpcChannel> channel_;
  std::shared_ptr<dancenn::ClientNamenodeService::Stub> client_;
  std::shared_ptr<cnetpp::base::EndPoint> endpoint_;
};


TEST_F(HdfsClientTest, Test01) {
  dancenn::RpcController c;
  cloudfs::GetListingRequestProto req;
  cloudfs::GetListingResponseProto resp;
  req.set_src("/");
  req.set_startafter("");
  req.set_needlocation(true);
  client_->getListing(&c, &req, &resp, nullptr);
  for (int i = 0; i < resp.dirlist().partiallisting_size(); ++i) {
    LOG(INFO) << resp.dirlist().partiallisting(i).path();
  }
  LOG(INFO) << resp.ShortDebugString();
}

TEST_F(HdfsClientTest, Test02) {
  dancenn::RpcController c;
  cloudfs::CreateRequestProto req;
  cloudfs::CreateResponseProto resp;
  req.set_src("/user/yangjinfeng.02/tmp/haha.txt");
  req.mutable_masked()->set_perm(0777);
  req.set_clientname("test_client_name");
  req.set_createflag(cloudfs::CreateFlagProto::CREATE);
  req.set_createparent(false);
  req.set_replication(3);
  req.set_blocksize(512 * 1024 * 1024);
  client_->create(&c, &req, &resp, nullptr);
  LOG(INFO) << resp.ShortDebugString();
}


TEST_F(HdfsClientTest, TestTcpClient) {
  std::shared_ptr<cnetpp::tcp::TcpClient> client = std::make_shared<cnetpp::tcp::TcpClient>();
  cnetpp::tcp::TcpClientOptions *options = new cnetpp::tcp::TcpClientOptions();
  std::shared_ptr<cnetpp::base::EndPoint> ep = std::make_shared<cnetpp::base::EndPoint>("10.6.134.42", 65211);
  options->set_connected_callback([&](std::shared_ptr<cnetpp::tcp::TcpConnection> c) -> bool {
    LOG(INFO) << "connected to " << ep->ToString();
    return true;
  });

  options->set_closed_callback([&](std::shared_ptr<cnetpp::tcp::TcpConnection> c) -> bool {
    LOG(INFO) << "closed " << ep->ToString() << " " << cnetpp::concurrency::ThisThread::GetLastErrorString();
    return true;
  });


  client->Launch("client", *options);
  client->Connect(ep.get(), *options, nullptr);

  std::this_thread::sleep_for(std::chrono::seconds(5));
  LOG(INFO) << "client test finished";
}

TEST_F(HdfsClientTest, TestDanceproxy) {
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_ =
          std::make_shared<cnetpp::concurrency::ThreadPool>("danproxy-test");
  handlers_->set_num_threads(1);
  handlers_->Start();

  int taskNum = 2;
  dancenn::CountDownLatch latch(taskNum);
  for (int i = 0; i < taskNum; ++i) {
    handlers_->AddTask([&, i]() -> bool {
      LOG(INFO) << "create file  " << i;
      dancenn::RpcController c;
      cloudfs::CreateRequestProto req;
      cloudfs::CreateResponseProto resp;
      std::string workingDir = "/inf/hprobe/lisp/danceproxy_test/";

      req.set_src(workingDir + "danceproxy-cplus2" + std::to_string(i));
      req.mutable_masked()->set_perm(0777);
      req.set_clientname("test_client_name");
      req.set_createflag(cloudfs::CreateFlagProto::CREATE);
      req.set_createparent(false);
      req.set_replication(3);
      req.set_blocksize(512 * 1024 * 1024);
      client_->create(&c, &req, &resp, nullptr);
      if (c.status() == dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << "create file successed  " << i;
      } else {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "create file failed  " << i;
      }
      latch.CountDown();
      return true;
    });
  }

  latch.Await();
  std::this_thread::sleep_for(std::chrono::seconds(20));
  handlers_->Stop(true);
}


TEST_F(HdfsClientTest, DanceproxyBenchmark) {
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_ =
          std::make_shared<cnetpp::concurrency::ThreadPool>("danproxy-test");
  handlers_->Start();

  int taskNum = 10;
  dancenn::CountDownLatch latch(taskNum);
  for (int i = 0; i < taskNum; ++i) {
    handlers_->AddTask([&, i]() -> bool {
      LOG(INFO) << "create file  " << i;
      dancenn::RpcController c;
      cloudfs::CreateRequestProto req;
      cloudfs::CreateResponseProto resp;
      std::string workingDir = "/inf/hprobe/lisp/danceproxy_test/";

      req.set_src(workingDir + "danceproxy-cplus3" + std::to_string(i));
      req.mutable_masked()->set_perm(0777);
      req.set_clientname("test_client_name");
      req.set_createflag(cloudfs::CreateFlagProto::CREATE);
      req.set_createparent(false);
      req.set_replication(3);
      req.set_blocksize(512 * 1024 * 1024);
      client_->create(&c, &req, &resp, nullptr);
      if (c.status() == dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << "create file successed  " << i;
      } else {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "create file failed  " << i;
      }
      return true;
    });
  }

  latch.Await();
  std::this_thread::sleep_for(std::chrono::seconds(20));
  handlers_->Stop(true);
}

TEST_F(HdfsClientTest, RenewLeaseBenchmark) {
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_ =
      std::make_shared<cnetpp::concurrency::ThreadPool>("renewlease-test");
  handlers_->Start();

  const int task_num = 5000;
  const int renew_num = 100;
  const std::string base_dir = "/chenwei.kevin/test/";
  dancenn::CountDownLatch latch(task_num);

  for (int i = 0; i < task_num; ++i) {
    handlers_->AddTask([&, i]() -> bool {
      LOG(INFO) << "create file  " << i;
      dancenn::RpcController c;
      cloudfs::CreateRequestProto req;
      cloudfs::CreateResponseProto resp;
      const std::string client_name = "test_client_name_" + std::to_string(i);
      std::string file_name = base_dir + "renewlease-test-" + std::to_string(i);

      req.set_src(file_name);
      req.mutable_masked()->set_perm(0777);
      req.set_clientname(client_name);
      req.set_createflag(cloudfs::CreateFlagProto::CREATE);
      req.set_createparent(false);
      req.set_replication(3);
      req.set_blocksize(512 * 1024 * 1024);
      client_->create(&c, &req, &resp, nullptr);
      if (c.status() != dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "create file failed  " << i;
      }

      LOG(INFO) << "renew file lease " << i;
      for (int j = 0; j < renew_num; ++j) {
        dancenn::RpcController c2;
        cloudfs::RenewLeaseRequestProto renew_req;
        cloudfs::RenewLeaseResponseProto renew_res;

        renew_req.set_clientname(client_name);
        client_->renewLease(&c2, &renew_req, &renew_res, nullptr);
        if (c2.status() != dancenn::RpcStatus::kSuccess) {
          LOG(INFO) << resp.ShortDebugString();
          LOG(INFO) << "renew lease failed  ";
        }

        using namespace std::chrono_literals;
        std::this_thread::sleep_for(1ms);
      }

      dancenn::RpcController c3;
      cloudfs::DeleteRequestProto del_req;
      cloudfs::DeleteResponseProto del_res;
      del_req.set_src(file_name);
      del_req.set_recursive(true);
      client_->Delete(&c3, &del_req, &del_res, nullptr);
      if (c3.status() != dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "delete file failed  " << i;
      }

      latch.CountDown();
      return true;
    });
  }

  latch.Await();
  handlers_->Stop(true);
}

TEST_F(HdfsClientTest, RecoverLeaseTest) {
  std::shared_ptr<cnetpp::concurrency::ThreadPool> handlers_ =
      std::make_shared<cnetpp::concurrency::ThreadPool>("recoverlease-test");
  handlers_->Start();

  const int task_num = 100;
  const std::string base_dir = "/chenwei.kevin/test/";
  dancenn::CountDownLatch latch(task_num);

  for (int i = 0; i < task_num; ++i) {
    handlers_->AddTask([&, i]() -> bool {
      LOG(INFO) << "create file  " << i;
      dancenn::RpcController c;
      cloudfs::CreateRequestProto req;
      cloudfs::CreateResponseProto resp;
      const std::string client_name = "test_client_name_" + std::to_string(i);
      std::string file_name = base_dir + "recoverlease-test-" + std::to_string(i);

      req.set_src(file_name);
      req.mutable_masked()->set_perm(0777);
      req.set_clientname(client_name);
      req.set_createflag(cloudfs::CreateFlagProto::CREATE);
      req.set_createparent(false);
      req.set_replication(3);
      req.set_blocksize(512 * 1024 * 1024);
      client_->create(&c, &req, &resp, nullptr);
      if (c.status() != dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "create file failed  " << i;
      }

      LOG(INFO) << "recovery file lease " << i;
      dancenn::RpcController c2;
      cloudfs::RecoverLeaseRequestProto recover_req;
      cloudfs::RecoverLeaseResponseProto recover_res;

      recover_req.set_src(file_name);
      recover_req.set_clientname(client_name);
      client_->recoverLease(&c2, &recover_req, &recover_res, nullptr);
      if (c2.status() != dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "recover lease failed  ";
      }

      dancenn::RpcController c3;
      cloudfs::DeleteRequestProto del_req;
      cloudfs::DeleteResponseProto del_res;
      del_req.set_src(file_name);
      del_req.set_recursive(true);
      client_->Delete(&c3, &del_req, &del_res, nullptr);
      if (c.status() != dancenn::RpcStatus::kSuccess) {
        LOG(INFO) << resp.ShortDebugString();
        LOG(INFO) << "delete file failed  " << i;
      }

      latch.CountDown();
      return true;
    });
  }

  latch.Await();
  handlers_->Stop(true);
}

#endif

