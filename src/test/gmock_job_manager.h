#ifndef TEST_GMOCK_JOB_MANAGER_H_
#define TEST_GMOCK_JOB_MANAGER_H_

#include <gmock/gmock.h>

#include "job_manager/job_manager.h"

namespace dancenn {

class GMockJobManager : public JobManager {
 public:
  MOCK_METHOD1(SetEditLogContext,
               void(std::shared_ptr<EditLogContextBase> edit_log_ctx));
  MOCK_METHOD1(SetEdit<PERSON>ogSender,
               void(std::shared_ptr<EditLogSenderBase> edit_log_sender));
  MOCK_METHOD1(SetMetaStorage, void(std::shared_ptr<MetaStorage> meta_storage));

  MOCK_METHOD0(GenerateJobId, ManagedJobId());

  MOCK_METHOD3(SubmitLoadDataJob,
               Status(INode* inode,
                      std::shared_ptr<DataJobOption> opt,
                      ManagedJobId* job_id));

  MOCK_METHOD4(SubmitLoadMultiReplicaJob,
               Status(INode* inode,
                      const std::string& path,
                      std::shared_ptr<DataJobOption> opt,
                      ManagedJobId* job_id));

  MOCK_METHOD4(SubmitFreeDataJob,
               Status(INode* inode,
                      const std::string& path,
                      std::shared_ptr<DataJobOption> opt,
                      ManagedJobId* job_id));

  MOCK_METHOD4(SubmitJob,
               Status(INode* inode,
                      std::shared_ptr<DataJobOption> opt,
                      std::shared_ptr<TaskCreator>& task_creator,
                      ManagedJobId* job_id));

  MOCK_METHOD4(SubmitReconcileINodeAttrsJob,
               Status(const INode& inode,
                      const std::shared_ptr<ReconcileINodeAttrsJobOption>& opt,
                      std::shared_ptr<TaskCreator> task_creator,
                      ManagedJobId* job_id));

  MOCK_METHOD1(set_ns, void(std::shared_ptr<NameSpace> ns));
  MOCK_METHOD0(get_ns, std::shared_ptr<NameSpace>());

  MOCK_METHOD0(Start, void());
  MOCK_METHOD0(Stop, void());

  MOCK_METHOD0(GetJobTracker, const std::shared_ptr<JobTracker>&());

  MOCK_CONST_METHOD2(TrackBlockTas, void(uint64_t blk_id, BlockStatus status));
  MOCK_METHOD1(CancelTask, void(uint64_t blk_id));

  MOCK_CONST_METHOD4(UpdateJobStatusHA,
                     void(const std::string& job_id,
                          const ManagedJobType& job_type,
                          const JobStatusOpBody& job_status,
                          bool uncached));

  MOCK_CONST_METHOD2(LookupJobState,
                     Status(const ManagedJobId& job_id,
                            ManagedJobState& job_state));

  MOCK_METHOD1(CancelJob, Status(const ManagedJobId& job_id));

  MOCK_METHOD2(CompleteJob,
               Status(const ManagedJobId& job_id, const WorkflowState state));

  MOCK_METHOD2(ListJob,
               Status(std::vector<ManagedJobId>& jobs,
                      ManagedJobType job_type));

  MOCK_CONST_METHOD0(GetJobInfoIterator, std::unique_ptr<rocksdb::Iterator>());
};

}  // namespace dancenn

#endif  // TEST_GMOCK_JOB_MANAGER_H_
