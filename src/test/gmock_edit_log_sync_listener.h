// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/10
// Description

#ifndef TEST_GMOCK_EDIT_LOG_SYNC_LISTENER_H_
#define TEST_GMOCK_EDIT_LOG_SYNC_LISTENER_H_

#include <gmock/gmock.h>
#include <cstdint>

#include "edit/edit_log_sync_listener.h"

namespace dancenn {

class GMockEditLogSyncListener : public IEditLogSyncListener {
 public:
  MOCK_METHOD2(TxFinish, void(int64_t begin_txid, int n));
};

}  // namespace dancenn

#endif  // TEST_GMOCK_EDIT_LOG_SYNC_LISTENER_H_
