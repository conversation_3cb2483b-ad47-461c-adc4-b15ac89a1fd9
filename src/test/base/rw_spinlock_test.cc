// Copyright 2020 Mu <PERSON> <<EMAIL>>

#include <thread>
#include <shared_mutex>
#include <mutex>
#include <unordered_map>

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <cnetpp/concurrency/thread_pool.h>

#include "base/count_down_latch.h"
#include "base/rw_spinlock.h"
#include "base/stop_watch.h"

DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);

namespace dancenn {

class RwSpinLockTest : public testing::Test {
public:
  void SetUp() override {
    // according to datanode manager requirement
    r_thread_num_ = FLAGS_client_normal_rpc_handler_count;
    w_thread_num_ = FLAGS_client_slow_rpc_handler_count;
    loop_num_ = 100000;
    for (int i = 0; i < table_size_; ++i) {
      table_.insert(std::make_pair(i, i % 5));
    }
  }

  void TearDown() override {
  }

  RWSpinlock lock_;
  std::atomic<int> r_thread_num_;
  std::atomic<int> w_thread_num_;
  std::atomic<int> loop_num_;
  CountDownLatch latch_{0};
  std::atomic<int> thread_index_{0};
  std::atomic<int> sleep_us{0};
  std::atomic<int> work_us{0};
  std::unordered_map<int, int> table_;
  size_t table_size_ = 100000;

  std::function<bool()> rtask = [&]() {
    auto tid = thread_index_.fetch_add(1);
    LOG(INFO) << "RThread(" << tid << ")" << " Start";

    StopWatch sw_task;
    sw_task.Start();

    int64_t ans = 0;
    int cnt = 0;
    while (cnt < loop_num_) {
      std::shared_lock<RWSpinlock> guard(lock_);
      ++cnt;
      // work
      if (sleep_us > 0) {
        std::this_thread::sleep_for(std::chrono::microseconds(sleep_us));
      }
      ans += table_[cnt % table_size_];
    }

    auto duration = sw_task.NextStepTime();
    auto cost = duration - cnt * (sleep_us + work_us) * (1 + w_thread_num_);
    LOG(INFO) << "RThread(" << tid << ")"
              << " ans=" << ans
              << " cnt=" << cnt
              << " duration=" << duration
              << " cost=" << cost
              << " avg=" << cost / cnt;
    latch_.CountDown();
    return true;
  };
  std::function<bool()> wtask = [&]() {
    auto tid = thread_index_.fetch_add(1);
    LOG(INFO) << "WThread(" << tid << ")" << " Start";

    StopWatch sw_task;
    sw_task.Start();

    int64_t ans = 0;
    int cnt = 0;
    while (cnt < loop_num_) {
      std::unique_lock<RWSpinlock> guard(lock_);
      ++cnt;
      // work
      if (sleep_us > 0) {
        std::this_thread::sleep_for(std::chrono::microseconds(sleep_us));
      }
      ans += table_[cnt % table_size_];
    }

    auto duration = sw_task.NextStepTime();
    auto cost = duration - cnt * (sleep_us + work_us) * (1 + w_thread_num_);
    LOG(INFO) << "WThread(" << tid << ")"
              << " ans=" << ans
              << " cnt=" << cnt
              << " duration=" << duration
              << " cost=" << cost
              << " avg=" << cost / cnt;
    latch_.CountDown();
    return true;
  };

  void Test(const std::string name, int rthread_num, int wthread_num) {
    StopWatch sw;
    sw.Start();
    r_thread_num_ = rthread_num;
    w_thread_num_ = wthread_num;
    const auto thread_num = r_thread_num_ + w_thread_num_;

    LOG(INFO) << "r_thread_num_=" << r_thread_num_;
    LOG(INFO) << "w_thread_num_=" << w_thread_num_;
    LOG(INFO) << "thread_num=" << thread_num;
    LOG(INFO) << "loop_num_=" << loop_num_;
    LOG(INFO) << "table_size_=" << table_size_;

    latch_.Reset(thread_num);

    auto pool = std::make_unique<cnetpp::concurrency::ThreadPool>(
        "Test-" + name);
    pool->set_num_threads(thread_num);
    pool->Start();

    LOG(INFO) << "Start cost=" << sw.NextStepTime();
    for (int i = 0; i < r_thread_num_; ++i) {
      pool->AddTask(rtask);
    }
    for (int i = 0; i < w_thread_num_; ++i) {
      pool->AddTask(wtask);
    }
    LOG(INFO) << "Committed cost=" << sw.NextStepTime();

    latch_.Await();
    auto duration = sw.NextStepTime();
    auto cost =
        duration - loop_num_ * (sleep_us + work_us) * (1 + w_thread_num_);
    LOG(INFO) << "latch await duration=" << duration;
    LOG(INFO) << "Result cnt=" << loop_num_ << " cost=" << cost << " avg="
              << cost / loop_num_;

    pool->Stop();
    LOG(INFO) << "stop thread_pool cost=" << sw.NextStepTime();
  }
};

TEST_F(RwSpinLockTest, RLockOnlyPerf) {
  Test("RLockOnlyPerf", r_thread_num_ * 2, 0);
}

TEST_F(RwSpinLockTest, RLockWithOneWriterPerf) {
  Test("RLockWithOneWriterPerf", r_thread_num_ * 2, 1);
}

}  // namespace dancenn

