// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include "base/lru_cache.h"

namespace dancenn {

TEST(LRUCacheTest, Test01) {
  LRUCache<int, int, true> lru(2);
  int v;
  ASSERT_FALSE(lru.Get(0, &v));
  ASSERT_EQ(lru.Size(), 0ul);
}

TEST(LRUCacheTest, Test02) {
  LRUCache<int, int, true> lru(2);
  lru.Set(0, 10);
  ASSERT_EQ(lru.Size(), 1ul);
  int v = 0;
  ASSERT_TRUE(lru.Get(0, &v));
  ASSERT_EQ(v, 10);
  lru.Set(1, 20);
  ASSERT_EQ(lru.Size(), 2ul);
  v = 0;
  ASSERT_TRUE(lru.Get(1, &v));
  ASSERT_EQ(v, 20);
}

TEST(LRUCacheTest, Test03) {
  LRUCache<int, int, true> lru(2);
  lru.Set(0, 10);
  lru.Set(1, 20);
  int v = 0;
  ASSERT_TRUE(lru.Get(0, &v));
  ASSERT_EQ(v, 10);
  ASSERT_EQ(lru.Size(), 2ul);
  lru.Set(2, 30);
  ASSERT_EQ(lru.Size(), 2ul);
  ASSERT_TRUE(lru.Get(0, &v));
  ASSERT_EQ(v, 10);
  ASSERT_FALSE(lru.Get(1, &v));
}

TEST(LRUCacheTest, TestEvict) {
  LRUCache<int, int, true> lru(2);
  lru.Set(0, 10);
  lru.Evict(0);
  ASSERT_EQ(lru.Size(), 0ul);
  lru.Set(0, 10);
  lru.Set(1, 20);
  ASSERT_EQ(lru.Size(), 2ul);
  lru.Evict(1);
  ASSERT_EQ(lru.Size(), 1ul);
  lru.Evict(0);
  ASSERT_EQ(lru.Size(), 0ul);
}

TEST(LRUCacheTest, TestREvictUntil) {
  {
    LRUCache<int, int, true> lru(2);
    lru.REvictUntil([](const int &k, const int &v) {
      return true;
    });
    ASSERT_EQ(lru.Size(), 0ul);
  }
  {
    LRUCache<int, int, true> lru(2);
    lru.Set(0, 10);
    lru.Set(1, 20);
    lru.REvictUntil([](const int &k, const int &v) {
      return true;
    });
    ASSERT_EQ(lru.Size(), 0ul);
  }
  {
    LRUCache<int, int, true> lru(2);
    lru.Set(0, 10);
    lru.Set(1, 20);
    lru.REvictUntil([](const int &k, const int &v) {
      if (k == 0 && v == 10) {
        return true;
      }
      return false;
    });
    ASSERT_EQ(lru.Size(), 1ul);
    int v = 0;
    ASSERT_TRUE(lru.Get(1, &v));
    ASSERT_EQ(v, 20);
  }
}

}  // namespace dancenn

