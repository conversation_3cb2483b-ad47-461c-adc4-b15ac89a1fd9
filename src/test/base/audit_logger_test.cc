// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "base/audit_logger.h"

#include <cnetpp/concurrency/thread.h>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <vector>

#include "base/mock_databus_server_test.h"
#include "base/defer.h"

namespace dancenn {

TEST(AuditLoggerTest, Test01) {
  FLAGS_audit_log_enabled = true;
  std::string channel = "dancenn_auditlog";
  std::unique_ptr<dancenn::AuditLogger> audit_logger(
      new dancenn::DatabusAuditLogger(channel));
  dancenn::AuditLogger::Init(std::move(audit_logger));

  std::atomic<int> received { 0 };
  std::string message = "This is audit log: %d %d";
  std::string complete_message = "This is audit log: 10 20";

  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    received++;
    ASSERT_NE(msg.find(complete_message), std::string::npos);
  });
  DEFER([&] () { server.TearDown(); });

  std::this_thread::sleep_for(std::chrono::milliseconds(10));

  DANCENN_AUDIT_LOG("This is audit log: %d %d", 10, 20);

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(1, received.load());
}

TEST(AuditLoggerTest, Test02) {
  FLAGS_audit_log_enabled = true;
  std::string channel = "dancenn_auditlog";
  std::unique_ptr<dancenn::AuditLogger> audit_logger(
      new dancenn::DatabusAuditLogger(channel));
  dancenn::AuditLogger::Init(std::move(audit_logger));

  std::atomic<int> received { 0 };
  std::string message = "This is audit log: %d %d";
  std::string complete_message = "This is audit log: 10 20";

  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    received++;
    ASSERT_NE(msg.find(complete_message), std::string::npos);
  });
  DEFER([&] () { server.TearDown(); });

  std::this_thread::sleep_for(std::chrono::milliseconds(10));

  for (int i = 0; i < 100; ++i) {
    DANCENN_AUDIT_LOG("This is audit log: %d %d", 10, 20);
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(100, received.load());
}

}  // namespace dancenn
