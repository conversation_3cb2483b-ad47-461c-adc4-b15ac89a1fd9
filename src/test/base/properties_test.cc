// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <base/properties.h>

#include <gtest/gtest.h>

#include <unistd.h>

#include <base/file_utils.h>

namespace dancenn {

namespace {

class PropertiesTest : public ::testing::Test {
 protected:
  void SetUp() override {
    fd_ = mkstemp(&(test_path_[0]));
    ASSERT_GT(fd_, -1);
  }

  void TearDown() override {
    if (fd_ >= 0) {
      close(fd_);
    }
    FileUtils::DeleteFile(test_path_);
  }

  void WriteContent(const std::string& content) {
    const char* p = content.c_str();
    int len = content.length();
    while (len > 0) {
      auto n = write(fd_, p, len);
      ASSERT_GE(n, 0);
      p += n;
      len -= n;
    }
  }

  std::string test_path_ = "test_XXXXXX";
  int fd_ { -1 };
};

}  // namespace

TEST_F(PropertiesTest, Test01) {
  std::string content = "#Thu Sep 07 17:48:11 CST 2017\n"
    "namespaceID=531102824\n"
    "clusterID = CID-7537dd9e-3c62-4409-a5f0-23996b030ca4\n"
    "cTime  =  0\n"
    "storageType  =NAME_NODE\n"
    "blockpoolID   = BP-1059217343-10.6.128.152-1437012745319\n"
    "layoutVersion=-60\n"
    "correctBoolValue=TruE\n"
    "wrongBoolValue=Truee\n"
    "wrongInt64Value=-60Truee\n"
    "wrongDoubleValue=-0.90Truee\n";
  WriteContent(content);
  Properties prop;
  ASSERT_TRUE(prop.Load(test_path_));
  ASSERT_EQ(prop.size(), 10);

  {
    std::string str_v;
    ASSERT_TRUE(prop.GetString("correctBoolValue", &str_v));
    ASSERT_EQ(str_v, "TruE");
    ASSERT_TRUE(prop.GetString("wrongBoolValue", &str_v));
    ASSERT_EQ(str_v, "Truee");
    ASSERT_TRUE(prop.GetString("wrongInt64Value", &str_v));
    ASSERT_EQ(str_v, "-60Truee");
    ASSERT_TRUE(prop.GetString("wrongDoubleValue", &str_v));
    ASSERT_EQ(str_v, "-0.90Truee");
    ASSERT_TRUE(prop.GetString("blockpoolID", &str_v));
    ASSERT_EQ(str_v, "BP-1059217343-10.6.128.152-1437012745319");
    ASSERT_TRUE(prop.GetString("storageType", &str_v));
    ASSERT_EQ(str_v, "NAME_NODE");
    ASSERT_TRUE(prop.GetString("cTime", &str_v));
    ASSERT_EQ(str_v, "0");
    ASSERT_TRUE(prop.GetString("clusterID", &str_v));
    ASSERT_EQ(str_v, "CID-7537dd9e-3c62-4409-a5f0-23996b030ca4");
    ASSERT_TRUE(prop.GetString("namespaceID", &str_v));
    ASSERT_EQ(str_v, "531102824");
    ASSERT_FALSE(prop.GetString("not_exist", &str_v, "default_value"));
    ASSERT_EQ(str_v, "default_value");
  }

  {
    bool bool_v;
    ASSERT_TRUE(prop.GetBool("correctBoolValue", &bool_v));
    ASSERT_TRUE(bool_v);
    bool_v = true;
    ASSERT_FALSE(prop.GetBool("wrongBoolValue", &bool_v, false));
    ASSERT_FALSE(bool_v);
  }

  {
    int32_t int32_v;
    ASSERT_TRUE(prop.GetInt<int32_t>("layoutVersion", &int32_v));
    ASSERT_EQ(int32_v, -60);

    ASSERT_FALSE(prop.GetInt<int32_t>("blockpoolID", &int32_v, 2ll));
    ASSERT_EQ(int32_v, 2ll);

    int64_t int64_v;
    ASSERT_FALSE(prop.GetInt<int64_t>("wrongInt64Value", &int64_v, 2ll));
    ASSERT_EQ(int64_v, 2ll);
  }

  {
    double double_v;
    ASSERT_TRUE(prop.GetDouble("layoutVersion", &double_v, 1.));
    ASSERT_DOUBLE_EQ(double_v, -60.);
    ASSERT_FALSE(prop.GetDouble("blockpoolID", &double_v, 1.));
    ASSERT_DOUBLE_EQ(double_v, 1.);
    ASSERT_FALSE(prop.GetDouble("wrongDoubleValue", &double_v, 1.));
    ASSERT_DOUBLE_EQ(double_v, 1.);
  }
}

}  // namespace dancenn

