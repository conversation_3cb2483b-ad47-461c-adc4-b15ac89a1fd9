// Copyright 2017 <PERSON><PERSON> Yang <<EMAIL>>

#include <base/platform.h>

#include <gtest/gtest.h>

namespace dancenn {

TEST(Platform, Test01) {
  auto hbo = platform::HostByteOrder();
  if (hbo == platform::ByteOrder::kLittleEndian) {
    {
      uint16_t value16 = 0x0102u;
      ASSERT_EQ(value16, platform::HostToLittleEndian(value16));
      uint32_t value32 = 0x01020304u;
      ASSERT_EQ(value32, platform::HostToLittleEndian(value32));
      uint64_t value64 = 0x0102030405060708ull;
      ASSERT_EQ(value64, platform::HostToLittleEndian(value64));
    }

    {
      uint16_t value16 = 0x0102u;
      ASSERT_EQ(0x0201, platform::HostToBigEndian(value16));
      uint32_t value32 = 0x01020304u;
      ASSERT_EQ(0x04030201, platform::HostToBigEndian(value32));
      uint64_t value64 = 0x0102030405060708ull;
      ASSERT_EQ(0x0807060504030201ll, platform::HostToBigEndian(value64));
    }

    {
      char buffer16[2];
      uint16_t value16 = 0x0102u;
      platform::WriteLittleEndian(buffer16, 0, value16);
      ASSERT_EQ(buffer16[0], 0x02);
      ASSERT_EQ(buffer16[1], 0x01);
      ASSERT_EQ(value16, platform::ReadLittleEndian<uint16_t>(buffer16, 0));
      ASSERT_EQ(0x0201u, platform::ReadBigEndian<uint16_t>(buffer16, 0));
      char buffer32[4];
      uint32_t value32 = 0x01020304u;
      platform::WriteLittleEndian(buffer32, 0, value32);
      ASSERT_EQ(buffer32[0], 0x04);
      ASSERT_EQ(buffer32[1], 0x03);
      ASSERT_EQ(buffer32[2], 0x02);
      ASSERT_EQ(buffer32[3], 0x01);
      ASSERT_EQ(value32, platform::ReadLittleEndian<uint32_t>(buffer32, 0));
      ASSERT_EQ(0x04030201u, platform::ReadBigEndian<uint32_t>(buffer32, 0));
      char buffer64[8];
      uint64_t value64 = 0x0102030405060708ull;
      platform::WriteLittleEndian(buffer64, 0, value64);
      ASSERT_EQ(buffer64[0], 0x08);
      ASSERT_EQ(buffer64[1], 0x07);
      ASSERT_EQ(buffer64[2], 0x06);
      ASSERT_EQ(buffer64[3], 0x05);
      ASSERT_EQ(buffer64[4], 0x04);
      ASSERT_EQ(buffer64[5], 0x03);
      ASSERT_EQ(buffer64[6], 0x02);
      ASSERT_EQ(buffer64[7], 0x01);
      ASSERT_EQ(value64, platform::ReadLittleEndian<uint64_t>(buffer64, 0));
      ASSERT_EQ(0x0807060504030201ull,
          platform::ReadBigEndian<uint64_t>(buffer64, 0));
    }
  } else if (hbo == platform::ByteOrder::kBigEndian) {
    {
      uint16_t value16 = 0x0102u;
      ASSERT_EQ(value16, platform::HostToBigEndian(value16));
      uint32_t value32 = 0x01020304u;
      ASSERT_EQ(value32, platform::HostToBigEndian(value32));
      uint64_t value64 = 0x0102030405060708ull;
      ASSERT_EQ(value64, platform::HostToBigEndian(value64));
    }

    {
      uint16_t value16 = 0x0102u;
      ASSERT_EQ(0x0201, platform::HostToLittleEndian(value16));
      uint32_t value32 = 0x01020304u;
      ASSERT_EQ(0x04030201, platform::HostToLittleEndian(value32));
      uint64_t value64 = 0x0102030405060708ull;
      ASSERT_EQ(0x0807060504030201ll, platform::HostToLittleEndian(value64));
    }

    {
      char buffer16[2];
      uint16_t value16 = 0x0102u;
      platform::WriteLittleEndian(buffer16, 0, value16);
      ASSERT_EQ(buffer16[0], 0x01);
      ASSERT_EQ(buffer16[1], 0x02);
      ASSERT_EQ(value16, platform::ReadLittleEndian<uint16_t>(buffer16, 0));
      ASSERT_EQ(0x0201u, platform::ReadBigEndian<uint16_t>(buffer16, 0));
      char buffer32[4];
      uint32_t value32 = 0x01020304u;
      platform::WriteLittleEndian(buffer32, 0, value32);
      ASSERT_EQ(buffer32[0], 0x01);
      ASSERT_EQ(buffer32[1], 0x02);
      ASSERT_EQ(buffer32[2], 0x03);
      ASSERT_EQ(buffer32[3], 0x04);
      ASSERT_EQ(value32, platform::ReadLittleEndian<uint32_t>(buffer32, 0));
      ASSERT_EQ(0x04030201u, platform::ReadBigEndian<uint32_t>(buffer32, 0));
      char buffer64[8];
      uint64_t value64 = 0x0102030405060708ull;
      platform::WriteLittleEndian(buffer64, 0, value64);
      ASSERT_EQ(buffer64[0], 0x01);
      ASSERT_EQ(buffer64[1], 0x02);
      ASSERT_EQ(buffer64[2], 0x03);
      ASSERT_EQ(buffer64[3], 0x04);
      ASSERT_EQ(buffer64[4], 0x05);
      ASSERT_EQ(buffer64[5], 0x06);
      ASSERT_EQ(buffer64[6], 0x07);
      ASSERT_EQ(buffer64[7], 0x08);
      ASSERT_EQ(value64, platform::ReadLittleEndian<uint64_t>(buffer64, 0));
      ASSERT_EQ(0x0807060504030201ull,
          platform::ReadBigEndian<uint64_t>(buffer64, 0));
    }
  } else {
    ASSERT_TRUE(false);
  }
}

}  // namespace dancenn

