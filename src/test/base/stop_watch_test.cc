// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/stop_watch.h"

#include <gtest/gtest.h>

#include <thread>
#include <chrono>

namespace dancenn {

TEST(StopWatchTest, TestSuspend) {
  StopWatch sw;
  sw.Start();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Suspend();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 100);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 110);
  sw.Resume();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Stop();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 200);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 220);
}

TEST(StopWatchTest, TestSplit) {
  StopWatch sw;
  sw.Start();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Split();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetSplitTime()).count(), 100);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetSplitTime()).count(), 110);
  sw.Unsplit();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Stop();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 200);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 220);
}

TEST(StopWatchTest, TestReset) {
  StopWatch sw;
  sw.Start();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Stop();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 100);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 110);
  sw.Reset();
  sw.Start();
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  sw.Stop();
  ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 100);
  EXPECT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(
      sw.GetTime()).count(), 110);
}

}  // namespace dancenn

