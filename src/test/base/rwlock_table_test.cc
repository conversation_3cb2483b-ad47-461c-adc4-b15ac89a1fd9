// Copyright 2019 livexmm <<EMAIL>>

#include <base/rwlock_table.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/base/string_piece.h>

#include <gtest/gtest.h>

#include <thread>
#include <random>

namespace dancenn {

TEST(RWLockTable, Basic) {
  auto table  = std::make_unique<RWLockTable>(2, 1000);
  {
    LockNode* l = table->GetOrCreate("zhangsan", 10, 0);
    ASSERT_EQ(l->ref_, UINT32_MAX);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 1);
    {
      table->GetOrCreate("zhangsan", 10, 0);
      ASSERT_EQ(table->dynamic_count(), 0);
      ASSERT_EQ(table->static_count(), 1);
    }
    table->Recycle(l);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 1);
  }
  {
    LockNode* l = table->GetOrCreate("zhangsan", 10, 0);
    ASSERT_EQ(l->ref_, UINT32_MAX);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 1);
    table->Recycle(l);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 1);
  }
  {
    LockNode* l = table->GetOrCreate("lisi", 10, 0);
    ASSERT_EQ(l->ref_, 1);
    ASSERT_EQ(table->dynamic_count(), 1);
    ASSERT_EQ(table->static_count(), 1);
    table->Recycle(l);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 1);
  }
  {
    LockNode* l = table->GetOrCreate("wangwu", 11, 1);
    ASSERT_EQ(l->ref_, UINT32_MAX);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 2);
    table->Recycle(l);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 2);
  }
  {
    LockNode* l = table->GetOrCreate("xxx", 12, 2);
    ASSERT_EQ(l->ref_, 1);
    ASSERT_EQ(table->dynamic_count(), 1);
    ASSERT_EQ(table->static_count(), 2);
    table->Recycle(l);
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 2);
  }
  {
    std::vector<LockNode*> locks;
    for (int i = 0; i < 10; i++) {
      LockNode* l = table->GetOrCreate("xxx", 12, 2);
      ASSERT_EQ(l->ref_, i + 1);
      ASSERT_EQ(table->dynamic_count(), 1);
      ASSERT_EQ(table->static_count(), 2);
      locks.emplace_back(l);
    }
    for (int i = 0; i < 10; i++) {
      ASSERT_EQ(table->dynamic_count(), 1);
      ASSERT_EQ(table->static_count(), 2);
      table->Recycle(locks[i]);
    }
    ASSERT_EQ(table->dynamic_count(), 0);
    ASSERT_EQ(table->static_count(), 2);
  }
}

void* fn1(void* argv) {
  RWLockTable* table = (RWLockTable *)argv;
  for (int i = 0; i < 100000; i++) {
    LockNode* l = table->GetOrCreate("a", 10, 1);
    table->Recycle(l);
  }
  return nullptr;
}

TEST(RWLockTable, Concurrency1) {
  auto table  = std::make_unique<RWLockTable>(2, 1000);
  pthread_t th[16];
  for (int i = 0; i < 16; i++) {
    pthread_create(th + i, NULL, fn1, table.get());
  }
  for (int i = 0; i < 16; i++) {
    pthread_join(th[i], nullptr);
  }

  ASSERT_EQ(table->dynamic_count(), 0);
  ASSERT_EQ(table->static_count(), 1);
}

void* fn2(void* argv) {
  RWLockTable* table = (RWLockTable *)argv;
  for (int i = 0; i < 100000; i++) {
    LockNode* l = table->GetOrCreate("b", 11, 2);
    table->Recycle(l);
  }
  return nullptr;
}

TEST(RWLockTable, Concurrency2) {
  auto table  = std::make_unique<RWLockTable>(2, 1000);
  pthread_t th[16];
  for (int i = 0; i < 16; i++) {
    pthread_create(th + i, NULL, fn2, table.get());
  }
  for (int i = 0; i < 16; i++) {
    pthread_join(th[i], nullptr);
  }

  ASSERT_EQ(table->dynamic_count(), 0);
  ASSERT_EQ(table->static_count(), 0);
}

}
