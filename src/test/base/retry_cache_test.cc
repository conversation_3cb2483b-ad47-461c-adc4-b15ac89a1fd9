// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <thread>

#include "ClientNamenodeProtocol.pb.h"
#include "RpcHeader.pb.h"

#include "base/closure.h"
#include "base/retry_cache.h"

DECLARE_int32(retry_cache_expiration_time_ms);
DECLARE_int32(retry_cache_slice_count);
DECLARE_int32(retry_cache_size);
DECLARE_bool(run_ut);

namespace dancenn {

using cloudfs::CreateSymlinkResponseProto;
using cloudfs::DeleteRequestProto;
using cloudfs::DeleteResponseProto;
using cloudfs::RpcRequestHeaderProto;

class RetryCacheTest : public testing::Test {
 public:
  void SetUp() override {
    backup_FLAGS_retry_cache_expiration_time_ms =
      FLAGS_retry_cache_expiration_time_ms;
    backup_FLAGS_retry_cache_slice_count = FLAGS_retry_cache_slice_count;
    backup_FLAGS_retry_cache_size = FLAGS_retry_cache_size;
    FLAGS_retry_cache_expiration_time_ms = 50;
    FLAGS_retry_cache_slice_count = 1;
    FLAGS_retry_cache_size = 2;
    FLAGS_run_ut = true;

    std::make_unique<RetryCache>().swap(rc_);
  }
  void TearDown() override {
    rc_.reset();

    FLAGS_retry_cache_expiration_time_ms =
      backup_FLAGS_retry_cache_expiration_time_ms;
    FLAGS_retry_cache_slice_count = backup_FLAGS_retry_cache_slice_count;
    FLAGS_retry_cache_size = backup_FLAGS_retry_cache_size;
  }

 protected:
  int32_t backup_FLAGS_retry_cache_expiration_time_ms;
  int32_t backup_FLAGS_retry_cache_slice_count;
  int32_t backup_FLAGS_retry_cache_size;

  std::unique_ptr<RetryCache> rc_;
};

TEST_F(RetryCacheTest, Test01) {
  std::shared_ptr<RetryCache::CacheEntry> centry0;
  bool hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry0);
  ASSERT_FALSE(hit);
  ASSERT_NE(centry0.get(), nullptr);
  ASSERT_FALSE(centry0->IsCompleted());
  auto dummy_resp = std::make_shared<CreateSymlinkResponseProto>();
  centry0->Complete(Status::OK(), dummy_resp);

  std::shared_ptr<RetryCache::CacheEntry> centry1;
  hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry1);
  ASSERT_TRUE(hit);
  ASSERT_NE(centry1.get(), nullptr);
  ASSERT_TRUE(centry1->IsCompleted());
  ASSERT_EQ(centry0.get(), centry1.get());

  std::shared_ptr<RetryCache::CacheEntry> centry2;
  hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry2);
  ASSERT_TRUE(hit);
  ASSERT_NE(centry2.get(), nullptr);
  ASSERT_TRUE(centry2->IsCompleted());
  ASSERT_EQ(centry0.get(), centry2.get());
}

TEST_F(RetryCacheTest, Test02) {
  std::shared_ptr<RetryCache::CacheEntry> centry0;
  bool hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry0);
  ASSERT_FALSE(hit);
  ASSERT_NE(centry0.get(), nullptr);
  ASSERT_FALSE(centry0->IsCompleted());
  auto dummy_resp = std::make_shared<CreateSymlinkResponseProto>();
  centry0->Complete(Status::OK(), dummy_resp);
  ASSERT_TRUE(centry0->IsCompleted());

  std::shared_ptr<RetryCache::CacheEntry> centry1;
  std::this_thread::sleep_for(std::chrono::milliseconds(60));
  hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 101, "", &centry1);
  ASSERT_FALSE(hit);
  ASSERT_NE(centry1.get(), nullptr);
  ASSERT_FALSE(centry1->IsCompleted());

  std::shared_ptr<RetryCache::CacheEntry> centry2;
  hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry2); // expired
  ASSERT_FALSE(hit);
  ASSERT_NE(centry2.get(), nullptr);
  ASSERT_FALSE(centry2->IsCompleted());
}

TEST_F(RetryCacheTest, Test03) {
  std::shared_ptr<RetryCache::CacheEntry> centry0, centry1, centry2, centry3;
  bool hit0 = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry0);
  bool hit1 = rc_->GetOrCreateCacheEntry("0123456789abcde", 101, "", &centry1);
  // evict entry0
  bool hit2 = rc_->GetOrCreateCacheEntry("0123456789abcde", 102, "", &centry2);

  bool hit3 = rc_->GetOrCreateCacheEntry("0123456789abcde", 103, "", &centry3);
  ASSERT_FALSE(hit3);
  ASSERT_NE(centry3.get(), nullptr);
  ASSERT_NE(centry0.get(), centry3.get());
  ASSERT_FALSE(centry3->IsCompleted());
}

TEST_F(RetryCacheTest, Test04) {
  auto dummy_resp = std::make_shared<CreateSymlinkResponseProto>();
  rc_->AddCacheEntry("0123456789abcde", 100, dummy_resp, "");
  std::shared_ptr<RetryCache::CacheEntry> centry;
  bool hit = rc_->GetOrCreateCacheEntry("0123456789abcde", 100, "", &centry);
  ASSERT_TRUE(hit);
  ASSERT_NE(centry.get(), nullptr);
  ASSERT_TRUE(centry->IsCompleted());
  ASSERT_EQ(centry->GetPayload().get(), dummy_resp.get());
}

TEST_F(RetryCacheTest, TestGetCacheEntry) {
  auto dummy_resp = std::make_shared<CreateSymlinkResponseProto>();
  rc_->AddCacheEntry("0123456789abcde", 100, dummy_resp, "");
  auto centry = rc_->GetCacheEntry("0123456789abcde", 100);
  ASSERT_NE(centry.get(), nullptr);
  ASSERT_TRUE(centry->IsCompleted());
  ASSERT_EQ(centry->GetPayload().get(), dummy_resp.get());
}

TEST_F(RetryCacheTest, RetryWhenFirstReqNotCompleted) {
  auto rpc_req = std::make_shared<DeleteRequestProto>();
  rpc_req->set_src("/path/to/file");
  rpc_req->set_recursive(false);
  DeleteResponseProto rpc_resp;
  rpc_resp.set_result(false);
  RpcController rpc_ctl;
  auto rpc_hdr = std::make_shared<cloudfs::RpcRequestHeaderProto>();
  rpc_hdr->set_clientid("test-client-idxx");
  rpc_hdr->set_callid(11235);
  rpc_ctl.set_rpc_request_header(rpc_hdr);
  RpcClosure* rpc_done = NewRpcCallback(true);
  ClosureGuard done_guard(rpc_done);

  // first-request start
  std::shared_ptr<RetryCache::CacheEntry> first_centry;
  bool hit = CheckRetryCacheEntry(rc_.get(),
                                  &rpc_ctl,
                                  &rpc_resp,
                                  &done_guard,
                                  &first_centry);
  CHECK(!hit && first_centry);

  // retry-request start, the rpc will be handed over to completion of first-req
  DeleteResponseProto retry_resp;
  RpcController retry_ctl;
  retry_ctl.set_rpc_request_header(rpc_hdr);
  RpcClosure* retry_done = NewRpcCallback(true);
  ClosureGuard retry_guard(retry_done);
  std::shared_ptr<RetryCache::CacheEntry> retry_centry;
  bool retry_hit = CheckRetryCacheEntry(rc_.get(),
                                        &retry_ctl,
                                        &retry_resp,
                                        &retry_guard,
                                        &retry_centry);
  CHECK(retry_hit && !retry_centry);
  CHECK(!retry_resp.has_result());
  CHECK(retry_guard.empty());

  // first-request end
  Status st = Status::OK();
  rpc_resp.set_result(true);
  first_centry->Complete(st, std::make_shared<DeleteResponseProto>(rpc_resp));

  // retry-request end
  CHECK(retry_resp.has_result());
  CHECK(retry_resp.result());
}

}  // namespace dancenn
