// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2022/02/25
// Description

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <nlohmann/json.hpp>

#include <memory>
#include <mutex>
#include <regex>
#include <string>
#include <vector>

namespace dancenn {

TEST(Json, Test01) {
  // using namespace nlohmann;
  nlohmann::json json = {{"pi", 3.141},
                         {"happy", true},
                         {"name", "Niels"},
                         {"nothing", nullptr},
                         {"answer", {{"everything", 112589990684262400}}},
                         {"list", {1, 0, 2}},
                         {"object", {{"currency", "USD"}, {"value", 42.99}}}};

  using CompactJsonFmtT =
      absl::ParsedFormat<'d', 's', 'd', 'd', 'd', 's', 's', 's', 'f', 'f'>;
  static std::unique_ptr<CompactJsonFmtT> compact_json_fmt;
  {
    static std::once_flag flag;
    std::call_once(flag, []() {
      const char* s = R"(
      |{
      |"answer":{"everything":%d},
      |"happy":%s,
      |"list":[%d,%d,%d],
      |"name":"%s",
      |"nothing":%s,
      |"object":{
      |"currency":"%s",
      |"value":%.2f
      |},
      |"pi":%.3f
      |})";
      compact_json_fmt = CompactJsonFmtT::New(
          std::regex_replace(s, std::regex(" +\\||\n"), "").c_str());
      CHECK(compact_json_fmt);
    });
  }
  std::string s = absl::StrFormat(*compact_json_fmt,
                                  112589990684262400,
                                  "true",
                                  1,
                                  0,
                                  2,
                                  "Niels",
                                  "null",
                                  "USD",
                                  42.99,
                                  3.141);
  EXPECT_EQ(json.dump(), s);

  using PrettyJsonFmtT =
      absl::ParsedFormat<'d', 's', 'd', 'd', 'd', 's', 's', 's', 'f', 'f'>;
  static std::unique_ptr<PrettyJsonFmtT> pretty_json_fmt;
  {
    static std::once_flag flag;
    // static std::unique_ptr<decltype(pretty_json_fmt)> s2;
    std::call_once(flag, []() {
      const char* s = R"(
      |{
      |  "answer": {
      |    "everything": %d
      |  },
      |  "happy": %s,
      |  "list": [
      |    %d,
      |    %d,
      |    %d
      |  ],
      |  "name": "%s",
      |  "nothing": %s,
      |  "object": {
      |    "currency": "%s",
      |    "value": %.2f
      |  },
      |  "pi": %.3f
      |})";
      pretty_json_fmt = PrettyJsonFmtT::New(
          std::regex_replace(s, std::regex(R"( +\|)"), "").substr(1).c_str());
      CHECK(pretty_json_fmt);
    });
  }
  s = absl::StrFormat(*pretty_json_fmt,
                      112589990684262400,
                      "true",
                      1,
                      0,
                      2,
                      "Niels",
                      "null",
                      "USD",
                      42.99,
                      3.141);
  EXPECT_EQ(json.dump(2), s);
}

TEST(Json, Test02) {
  auto jobj = nlohmann::json::parse(R"(["a", "b", "c"])");
  EXPECT_TRUE(jobj.is_array());
  std::vector<std::string> elements;
  for (const auto& e : jobj) {
    if (e.is_string()) {
      elements.push_back(e);
    }
  }
  EXPECT_EQ(elements.size(), 3);
  EXPECT_EQ(elements[0], "a");
  EXPECT_EQ(elements[1], "b");
  EXPECT_EQ(elements[2], "c");
}

}  // namespace dancenn
