// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/07/18
// Description

#ifndef TEST_BASE_TIME_UTIL_H_
#define TEST_BASE_TIME_UTIL_H_

#include <gmock/gmock.h>  // For Test, etc.

#include <cstdint>  // For uint64_t.

namespace dancenn {

class MockableTimeUtil {
  virtual uint64_t GetNowEpochMs() = 0;
};

class GMockTimeUtil {
 public:
  MOCK_METHOD0(GetNowEpochMs, uint64_t());
};

}  // namespace dancenn

#endif  // TEST_BASE_TIME_UTIL_H_
