#include <base/writable_util.h>

#include <gtest/gtest.h>

#include <string>
#include <sstream>

namespace dancenn {

TEST(WritableUtil, Test01) {
  std::stringstream ss;
  WriteVInt32(&ss, -1);
  WriteVInt32(&ss, 1);
  WriteVInt32(&ss, 16);
  WriteVInt32(&ss, 1000);
  WriteVInt32(&ss, 10000);
  WriteVInt64(&ss, 10000000L);
  WriteVInt64(&ss, 100000000000L);
  WriteVInt64(&ss, 100000000000000L);
  WriteStringWithVIntPrefix(&ss, "h");
  WriteStringWithVIntPrefix(&ss, "hello");
  WriteStringWithVIntPrefix(&ss, "hello world");
  std::string b = ss.str();
  std::stringstream is(b);
  ASSERT_EQ(-1, ReadVInt32(&ss));
  ASSERT_EQ(1, ReadVInt32(&ss));
  ASSERT_EQ(16, ReadVInt32(&ss));
  ASSERT_EQ(1000, ReadVInt32(&ss));
  ASSERT_EQ(10000, ReadVInt32(&ss));
  ASSERT_EQ(10000000L, ReadVInt64(&ss));
  ASSERT_EQ(100000000000L, ReadVInt64(&ss));
  ASSERT_EQ(100000000000000L, ReadVInt64(&ss));
  ASSERT_EQ("h", ReadStringWithVIntPrefix(&ss));
  ASSERT_EQ("hello", ReadStringWithVIntPrefix(&ss));
  ASSERT_EQ("hello world", ReadStringWithVIntPrefix(&ss));
}

}  // namespace dancenn
