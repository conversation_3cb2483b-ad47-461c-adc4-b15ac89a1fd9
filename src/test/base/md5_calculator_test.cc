// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include "base/md5_calculator.h"


namespace dancenn {

TEST(MD5Calculator, Test01) {
  OPENSSL_cleanse(nullptr, 0);
  {
    MD5Calculator c;
    c.Update(" ");
    auto md5 = c.Digest();
    ASSERT_EQ("7215EE9C7D9DC229D2921A40E899EC5F", md5.ToUpperCase());
    ASSERT_EQ("7215ee9c7d9dc229d2921a40e899ec5f", md5.ToLowerCase());

    c.Reset();
    c.Update("this is a test md5 string.");
    md5 = c.Digest();
    ASSERT_EQ("85551BD49775206AD62FBA65D4EEC2DB", md5.ToUpperCase());
    ASSERT_EQ("85551bd49775206ad62fba65d4eec2db", md5.ToLowerCase());
  }

  {
    MD5 md5;
    md5.FromString("85551bd49775206ad62fba65d4eec2db");
    ASSERT_EQ("85551BD49775206AD62FBA65D4EEC2DB", md5.ToUpperCase());
  }
}

}  // namespace dancenn

