#include "base/object_pool.h"

#include "gtest/gtest.h"

namespace dancenn {

static int des_cnt;

struct Obj {
  ~Obj() {
    des_cnt++;
  }
};

class Pool final : public ObjectPool<Obj> {
 public:
  Pool() : ObjectPool<Obj>(3) {
  }

  ObjectPtr Acquire() {
    return Get([this]() { return new Obj(); });
  }

  int GetCurrentSize() {
    return current_size_;
  }
};

TEST(ObjectPoolTest, TestObjectPool) {
  des_cnt = 0;
  {
    Pool pool;
    {
      auto o1 = pool.Acquire();
      ASSERT_EQ(1, pool.GetCurrentSize());
      auto o2 = pool.Acquire();
      ASSERT_EQ(2, pool.GetCurrentSize());
      auto o3 = pool.Acquire();
      ASSERT_EQ(3, pool.GetCurrentSize());
      auto o4 = pool.Acquire();
      ASSERT_EQ(4, pool.GetCurrentSize());
    }
    ASSERT_EQ(3, pool.GetCurrentSize());
    ASSERT_EQ(1, des_cnt);
  }
  ASSERT_EQ(4, des_cnt);
}
}  // namespace dancenn