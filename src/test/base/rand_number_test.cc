#include "base/rand_number.h"

#include <gtest/gtest.h>
#include <glog/logging.h>

#include <unordered_map>

namespace dancenn {

TEST(RandNumberTest, Test01) {
  RandNumber* rand_number = RandNumber::Instance();
  std::unordered_map<int, int> value_map;

  for (int i = 0; i < 10000; ++i) {
    uint32_t v = rand_number->NextInt32(1, 10);
    value_map[v]++;
  }

  // uniform distribution for NextInt
  for (auto& value : value_map) {
    LOG(INFO) << "Value: " << value.first
              << " Count: " << value.second
              << " for i in [1, 10]";
    ASSERT_TRUE(value.second >= 800 && value.second <= 1200);
  }

  // uniform distribution for NextBool
  value_map.clear();
  for (int i = 0; i < 10000; ++i) {
    if (rand_number->NextBool()) {
      value_map[0]++;
    } else {
      value_map[1]++;
    }
  }
  LOG(INFO) << "false: " << value_map[0] << " "
            << "true: " << value_map[1] << " for 10000 bool values";
  ASSERT_LT(std::abs(value_map[0] - value_map[1]), 1000);

  // uniform distribution for NextDouble
  double total_value = 0;
  for (int i = 0; i < 10000; ++i) {
    total_value += rand_number->NextDouble();
  }
  LOG(INFO) << "total: " << total_value
            << " for 10000 double values in [0, 1]";
  ASSERT_TRUE(total_value >= 0.4 * 10000 && total_value <= 0.6 * 10000);
}

static void TimeCostFunc(const std::function<void()>& func,
                         int iterations,
                         const std::string& content_str) {
  auto begin = std::chrono::high_resolution_clock::now();

  func();

  auto end = std::chrono::high_resolution_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(
      end - begin).count();
  LOG(INFO) << duration << "ns total, average : "
            << duration / iterations << "ns."
            << " for " << content_str;
}

TEST(RandNumberTest, PerfTest02) {
  RandNumber* rand_number = RandNumber::Instance();

  int iterations = 100000;
  // basis, empty function
  TimeCostFunc([=] () -> void {
    for (int i = 0; i < iterations; ++i) {
    }
  }, iterations, "empty func");

  uint32_t no_optimized;
  TimeCostFunc([&, iterations] () -> void {
    for (int i = 0; i < iterations; ++i) {
      no_optimized = rand_number->NextInt32();
    }
  }, iterations, "NextInt");

  TimeCostFunc([&, iterations] () -> void {
    for (int i = 0; i < iterations; ++i) {
      no_optimized = rand_number->NextDouble();
    }
  }, iterations, "NextDouble");

  TimeCostFunc([&, iterations] () -> void {
    for (int i = 0; i < iterations; ++i) {
      no_optimized = rand_number->NextBool();
    }
  }, iterations, "NextBool");

  TimeCostFunc([&, iterations] () -> void {
    for (int i = 0; i < iterations; ++i) {
      no_optimized = rand_number->NextInt32(33, 1000);
    }
  }, iterations, "NextInt(33, 1000)");

  std::mt19937 rd(std::random_device {}());
  std::uniform_int_distribution<uint32_t> uni_int_dis;
  TimeCostFunc([&, iterations] () -> void {
    for (int i = 0; i < iterations; ++i) {
      no_optimized = uni_int_dis(rd);
    }
  }, iterations, "NoSpinLockNextInt");

  std::random_device device;
  TimeCostFunc([&] () -> void {
    for (int i = 0; i < 10; ++i) {
      no_optimized = device();
    }
  }, 10, "random_device");

  std::random_device device2;
  TimeCostFunc([&] () -> void {
    for (int i = 0; i < 10; ++i) {
      no_optimized = device2();
    }
  }, 1, "random_device_once");

  ASSERT_GE(no_optimized, 0);
  LOG(INFO) << "RandNumberPerTest finished. ";
}

}  // namespace dancenn

