// Copyright 2018 Liyuan lei <<EMAIL>>

#include "base/rack_aware.h"

#include <gtest/gtest.h>

#include "base/file_utils.h"

namespace dancenn {

TEST(RackAware, ResolveCFSIpMask) {
  // Ip/mask.
  std::string config_string =
      "***********/24 huatai-dc-a\n"
      "***********/24 huatai-dc-b\n";
  ConfigBasedRackAware::GetSingleton().ParseAndUpdate(config_string);

  auto res = ConfigBasedRackAware::GetSingleton().ListSubnet();
  std::cout << "Generated Subnet List:" << std::endl;
  for (auto s : res) {
    std::cout << s << std::endl;
  }
  std::cout << "Subnet List End~" << std::endl;

  NetworkLocation location;

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "huatai-dc-b");
  ASSERT_EQ(location.rack, "a80340");
}

TEST(RackAware, ResolveCFSIpRange) {
  std::string config_string =
      "*********** ************* huatai-dc-a\n"
      "*********** ************* huatai-dc-b\n";
  ConfigBasedRackAware::GetSingleton().ParseAndUpdate(config_string);

  auto res = ConfigBasedRackAware::GetSingleton().ListSubnet();
  std::cout << "Generated Subnet List:" << std::endl;
  for (auto s : res) {
    std::cout << s << std::endl;
  }
  std::cout << "Subnet List End~" << std::endl;

  NetworkLocation location;

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "huatai-dc-b");
  ASSERT_EQ(location.rack, "a80340");
}

TEST(RackAware, ResolveNetworkLocation) {
  std::string config_string =
      "*********/16 LF\r\n"
      "*********/16 LF\r\n"
      "*********/16 HL\r\n"
      "**********/24 HL\r\n"
      "*********/16 BJOFFICE\r\n"
      "*********/18 LQ\r\n"
      "**********/18 HL\r\n"
      "***********/18 HL\r\n"
      "***********/24 ABC\r\n"

      "**********/16 HL\r\n"
      "**********/16 CR\r\n"

      "**********/17 BOEI18N\r\n"
      "**********/16 MALIVA\r\n"
      "**********/16 ALISG\r\n"
      "**********/16 ALISG\r\n"
      "**********/16 MALIVA\r\n"
      "**********/16 SG1\r\n"
      "**********/17 ALISG\r\n"

      // corner test cases for nested subnets
      "*********/14 ROOT_NET\r\n"
      // [0,16) (shaded), [0,8), [8,12), [12,16)
      "*********/20 SUB_NET_A\r\n"
      "*********/21 SUB_NET_AA\r\n"
      "*********/21 SUB_NET_AB\r\n"
      "**********/22 SUB_NET_AC\r\n"

      // [32,48) (split into { [32, 36), [40,48) }), [36,40)
      "**********/20 SUB_NET_B\r\n"
      "**********/22 SUB_NET_BA\r\n"

      // [80, 96)
      "**********/20 SUB_NET_C\r\n"

      // [128, 192) (split into { [128, 160) }), [160. 192)
      "***********/18 SUB_NET_D\r\n"
      "***********/19 SUB_NET_DA\r\n"
      // corner test cases end

      // ipv6 list
      "FDBD:DC01:24::/48 LF\r\n"
      "FDBD:DC01::/32 LF\r\n"
      "FDBD:DC02::/32 HL\r\n"
      "FDBD:DC02:FF:1:1::/80 BOE\r\n"
      "FDBD:DC02:FF:1:2::/80 BOE\r\n"
      "FDBD:DC02:FF:1:3::/80 COF\r\n"
      "FDBD:DC02:FF:1:4::/80 COF\r\n"
      "FDBD:DC02:FF:1:9::/80 BOE\r\n"
      "FDBD:DC03::/32 LQ\r\n"
      "FDBD:DC03:FF:1::/64 DEVBOX\r\n"
      "FDBD:DC51::/32 ALISG\r\n"
      "FDBD:DC51:FF::/48 SG1\r\n"
      "FDBD:DC61::/32 MALIVA\r\n"
      "FDBD:DC61:FF:0:1::/80 DEVBOX\r\n"
      "FDBD:DC61:FF::/80 BOEI18N\r\n";

  ConfigBasedRackAware::GetSingleton().ParseAndUpdate(config_string);

  auto res = ConfigBasedRackAware::GetSingleton().ListSubnet();
  std::cout << "Generated Subnet List:" << std::endl;
  for (auto s : res) {
    std::cout << s << std::endl;
  }
  std::cout << "Subnet List End~" << std::endl;

  NetworkLocation location;

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "0a0a00");
  location = ResolveNetworkLocation("n10-010-010");
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "0a0a00");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "1a0a00");
  location = ResolveNetworkLocation("n26-010-011");
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "1a0a00");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "BOEI18N");
  ASSERT_EQ(location.rack, "e77b00");
  location = ResolveNetworkLocation("n231-123-045");
  ASSERT_EQ(location.dc, "BOEI18N");
  ASSERT_EQ(location.rack, "e77b00");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "MALIVA");
  ASSERT_EQ(location.rack, "e78140");
  location = ResolveNetworkLocation("n231-129-123");
  ASSERT_EQ(location.dc, "MALIVA");
  ASSERT_EQ(location.rack, "e78140");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "ALISG");
  ASSERT_EQ(location.rack, "f0aa80");
  location = ResolveNetworkLocation("n240-170-149");
  ASSERT_EQ(location.dc, "ALISG");
  ASSERT_EQ(location.rack, "f0aa80");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "MALIVA");
  ASSERT_EQ(location.rack, "f4b1c0");
  location = ResolveNetworkLocation("n244-177-229");
  ASSERT_EQ(location.dc, "MALIVA");
  ASSERT_EQ(location.rack, "f4b1c0");

  location =
      ResolveNetworkLocation(cnetpp::base::IPAddress("fdbd:dc01:15:127::23"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "v6015127");

  location = ResolveNetworkLocation(
      cnetpp::base::IPAddress("fdbd:dc02:ff:1:4:225:162:87"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "COF");
  ASSERT_EQ(location.rack, "v60ff001");

  location =
      ResolveNetworkLocation(cnetpp::base::IPAddress("FDBD:DC01:123:998::88"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "v6123998");

  location = ResolveNetworkLocation(
      cnetpp::base::IPAddress("FDBD:DC02:AC:11:4:515:191:64"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "v60ac011");

  // corner test cases
  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*********"));
  ASSERT_EQ(location.dc, "ROOT_NET");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*********"));
  ASSERT_EQ(location.dc, "SUB_NET_AA");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*********"));
  ASSERT_EQ(location.dc, "SUB_NET_AB");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "SUB_NET_AC");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "ROOT_NET");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "SUB_NET_B");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "SUB_NET_BA");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "SUB_NET_B");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "ROOT_NET");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**********"));
  ASSERT_EQ(location.dc, "SUB_NET_C");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  ASSERT_EQ(location.dc, "ROOT_NET");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  ASSERT_EQ(location.dc, "SUB_NET_D");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("************"));
  ASSERT_EQ(location.dc, "SUB_NET_DA");
}

TEST(RackAware, LegacyResolveNetworkLocation) {
  std::string config_string =
      "{\n"
      "  \"default_dc\": \"\",\n"
      "  \"dc_config\": [\n"
      "    [\"10.0.0.0\", \"************\", \"LF\"],\n"
      "    [\"********\", \"************\", \"HY\"],\n"
      "    [\"********\", \"*************\", \"LF\"],\n"
      "    [\"*********\", \"*************\", \"HL\"],\n"
      "    [\"*********\", \"*************\", \"WJ\"],\n"
      "    [\"*********\", \"*************\", \"HL\"],\n"
      "    [\"**********\", \"**************\", \"AWSVA\"],\n"
      "    [\"**********\", \"**************\", \"AWSSG\"],\n"
      "    [\"**********\", \"**************\", \"ALIVA\"],\n"
      "    [\"**********\", \"**************\", \"ALISG\"],\n"
      "    [\"**********\", \"**************\", \"LQ\"],\n"
      "    [\"**********\", \"**************\", \"HL\"],\n"
      "    [\"**********\", \"**************\", \"COF\"],\n"
      "    [\"************\", \"**************\", \"BOE\"],\n"
      "    [\"************\", \"**************\", \"COF\"]\n"
      "  ]\n"
      "}";
  auto rtn =
      ConfigBasedRackAware::GetSingleton().ParseJsonAndUpdate(config_string);
  ASSERT_TRUE(rtn);

  NetworkLocation location =
      ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "0a0a00");
  location = ResolveNetworkLocation("n10-010-010");
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "0a0a00");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "140a00");
  location = ResolveNetworkLocation("n20-010-011");
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "140a00");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("***********"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "08b100");
  location = ResolveNetworkLocation("n8-177-052");
  ASSERT_EQ(location.dc, "LF");
  ASSERT_EQ(location.rack, "08b100");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("**************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "ALIVA");
  ASSERT_EQ(location.rack, "6eb1c0");
  location = ResolveNetworkLocation("n110-177-229");
  ASSERT_EQ(location.dc, "ALIVA");
  ASSERT_EQ(location.rack, "6eb1c0");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "ALISG");
  ASSERT_EQ(location.rack, "7363c0");
  location = ResolveNetworkLocation("n115-099-231");
  ASSERT_EQ(location.dc, "ALISG");
  ASSERT_EQ(location.rack, "7363c0");

  location = ResolveNetworkLocation(cnetpp::base::IPAddress("*************"));
  std::cout << location.dc << " " << location.rack << std::endl;
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "17aa80");
  location = ResolveNetworkLocation("n23-170-149");
  ASSERT_EQ(location.dc, "HL");
  ASSERT_EQ(location.rack, "17aa80");
}

}  // namespace dancenn
