// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/22
// Description

#include "base/two_step_vlock.h"  // For TwoStepVLock.

#include <mutex>    // For try_to_lock.
#include <utility>  // For move.

#include "base/vlock.h"   // For VersionRWLock, VRWLock, vshared_lock.
#include "gtest/gtest.h"  // For TEST.

namespace dancenn {

TEST(TwoStepVLockTest, HappyCase) {
  VRWLock lock(1);
  TwoStepVLock vlock(lock.lock());
  vlock.Prepare();
  vlock.Lock();
  vlock.Unlock();
}

TEST(TwoStepVUniqueLockTest, NotEmptyConstructor) {
  VRWLock lock(1);
  TwoStepVUniqueLock unique_lock(lock.lock());
  EXPECT_TRUE(unique_lock);
}

TEST(TwoStepVUniqueLockTest, MoveConstructor) {
  VRWLock lock(1);
  TwoStepVUniqueLock unique_lock_1(lock.lock());
  EXPECT_TRUE(unique_lock_1);
  TwoStepVUniqueLock unique_lock_2(std::move(unique_lock_1));
  EXPECT_FALSE(unique_lock_1);
  EXPECT_TRUE(unique_lock_2);
}

TEST(TwoStepVUniqueLockTest, MoveAssignment) {
  VRWLock lock(1);
  TwoStepVUniqueLock unique_lock_1(lock.lock());
  EXPECT_TRUE(unique_lock_1);
  TwoStepVUniqueLock unique_lock_2 = std::move(unique_lock_1);
  EXPECT_FALSE(unique_lock_1);
  EXPECT_TRUE(unique_lock_2);
}

TEST(TwoStepVUniqueLockTest, LockSharedFailedAfterPrepare) {
  VRWLock lock(1);
  TwoStepVUniqueLock unique_lock(lock.lock());
  // Refer to HAState::CheckOperation.
  dancenn::vshared_lock shared_lock(lock.lock(), std::try_to_lock);
  EXPECT_FALSE(shared_lock.owns_lock());
}

TEST(TwoStepVUniqueLockTest, LockSharedSucceedAfterUnlock) {
  VRWLock lock(1);
  { TwoStepVUniqueLock unique_lock(lock.lock()); }
  // Refer to HAState::CheckOperation.
  dancenn::vshared_lock shared_lock(lock.lock(), std::try_to_lock);
  EXPECT_TRUE(shared_lock.owns_lock());
}

TEST(TwoStepVUniqueLockTest, AutoRelease) {
  VRWLock lock(1);
  { TwoStepVUniqueLock unique_lock(lock.lock()); }
  { TwoStepVUniqueLock unique_lock(lock.lock()); }
}

}  // namespace dancenn