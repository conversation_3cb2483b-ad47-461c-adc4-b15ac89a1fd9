//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "base/kafka_util.h"

// Third
#include <gtest/gtest.h>

namespace dancenn {

TEST(KafkaUtil, OffsetMapToOffsets) {
  std::string topic = "test-topic";
  uint32_t partition1 = 1;
  uint32_t partition2 = 2;
  uint64_t offset1 = 1001;
  uint64_t offset2 = 1002;
  KafkaOffsetMap offset_map = {
      {topic, {{partition1, offset1}, {partition2, offset2}}}};

  std::vector<RdKafka::TopicPartition*> offsets =
      KafkaUtil::OffsetMapToOffsets(offset_map);
  EXPECT_EQ(offsets.size(), 2);
  for (RdKafka::TopicPartition* offset : offsets) {
    EXPECT_EQ(topic, offset->topic());
    EXPECT_EQ(offset_map[topic][offset->partition()] + 1, offset->offset());
  }

  RdKafka::TopicPartition::destroy(offsets);
}

}  // namespace dancenn
