// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/metric.h"

#include <gtest/gtest.h>
#include <glog/logging.h>

#include <thread>
#include <string>
#include <memory>

#include "base/metrics.h"

namespace dancenn {

TEST(MetricTest, GaugeTest01) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("metric_test");
  Gauge g("Test01");
  ASSERT_EQ(g.name(), "Test01");
  ASSERT_EQ(metrics->GetGaugeFullName(g.name()), "Test01");
  metrics->set_dynamic_tag_injector([] () -> std::string {
    return "k=v";
  });
  ASSERT_EQ(metrics->GetGaugeFullName(g.name()), "Test01#k=v");
  g.Update(10.);
  ASSERT_EQ(g.MakeSnapshot(), 10.);
  g.Update(5.);
  ASSERT_EQ(g.MakeSnapshot(), 5.);
  ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
}

TEST(MetricTest, GaugeTest02) {
  auto g = std::make_unique<Gauge>("test_gauge");
  g->Update(20);
  ASSERT_EQ(20, g->MakeSnapshot());
}

TEST(MetricTest, CounterTest01) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("metric_test");
  auto c_id = metrics->RegisterCounter("Test01");
  Counter c(c_id.ID());
  metrics->set_dynamic_tag_injector([] () -> std::string {
    return "";
  });
  ASSERT_EQ(metrics->GetCounterFullName(c.id()), "Test01");
  ASSERT_EQ(Metrics::GetCounterName(c.id()), "Test01");
  metrics->set_dynamic_tag_injector([] () -> std::string {
    return "k1=v1";
  });
  ASSERT_EQ(metrics->GetCounterFullName(c.id()), "Test01#k1=v1");
  c.Inc();
  ASSERT_EQ(c.GetValue(), 1);
  c.Inc(10);
  ASSERT_EQ(c.GetValue(), 11);
}

TEST(MetricTest, CounterTest02) {
  auto c = std::make_unique<Counter>(10);
  c->Inc();
  c->Inc(20);
  ASSERT_EQ(21, c->GetValue());
}

TEST(MetricTest, HistogramTest01) {
  auto h = std::make_unique<Histogram>(20);
  {
    for (int i = 0; i < 2048; i++) {
      h->Update(i);
    }

    auto snapshot = std::unique_ptr<Histogram::Snapshot>(h->MakeSnapshot());
    ASSERT_EQ(0, snapshot->Min());
    ASSERT_EQ(2047, snapshot->Max());
    ASSERT_EQ((0 + 2047) / 2.0, snapshot->Avg());
  }
  {
    for (int i = 2048; i < 4096; i++) {
      h->Update(i);
    }

    auto snapshot = std::unique_ptr<Histogram::Snapshot>(h->MakeSnapshot());
    ASSERT_EQ(2048, snapshot->Min());
    ASSERT_EQ(4095, snapshot->Max());
    ASSERT_EQ((2048 + 4095) / 2.0, snapshot->Avg());
  }
  {
    int t[2048];
    for (int i = 0; i < 2048; i++) t[i] = i + 4096;
    srand(time(NULL));
    for (int i = 0; i < 2047; i++) {
      int n = rand() % (2048 - i - 1);
      int x = t[i];
      t[i] = t[i + n];
      t[i + n] = x;
    }

    for (int i = 0; i < 2048; i++) {
      h->Update(t[i]);
    }

    auto snapshot = std::unique_ptr<Histogram::Snapshot>(h->MakeSnapshot());
    ASSERT_EQ(4096, snapshot->Min());
    ASSERT_EQ(6143, snapshot->Max());
    ASSERT_EQ((4096 + 6143) / 2.0, snapshot->Avg());
  }
}

TEST(MetricTest, HistogramTest02) {
  auto h = std::make_unique<Histogram>(20);
  {
    for (int i = 0; i < 2048; i++) {
      h->Update(i);
    }

    auto snapshot = std::unique_ptr<Histogram::Snapshot>(h->MakeSnapshot());
    ASSERT_EQ(0., snapshot->GetValue(0));
    ASSERT_EQ(2047., snapshot->GetValue(1));
  }
}

TEST(MetricTest, MetricsTest01) {
  auto metrics = MetricsCenter::Instance()->RegisterMetrics("metrics_test01");
  { // 1 from above testcase
    ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
    ASSERT_EQ(0, metrics->AllCounterSnapshots().size());
    ASSERT_EQ(0, metrics->AllHistogramSnapshots().size());
  }

  ASSERT_EQ("metrics_test01", metrics->prefix());
  {
    uint32_t cid = 0;

    auto id = metrics->RegisterCounter("zhangsan");
    cid = id.ID();
    ASSERT_EQ(cid, id.ID());
    ASSERT_EQ("zhangsan", metrics->GetCounterName(id.ID()));
    ASSERT_EQ("zhangsan", metrics->GetCounterFullName(id.ID()));

    auto id2 = metrics->RegisterCounter("zhangsan");
    ASSERT_EQ(cid, id2.ID());
    ASSERT_EQ("zhangsan", metrics->GetCounterName(id2.ID()));
    ASSERT_EQ("zhangsan", metrics->GetCounterFullName(id2.ID()));

    auto id3 = metrics->RegisterCounter("lisi");
    ASSERT_EQ(cid + 1, id3.ID());
    ASSERT_EQ("lisi", metrics->GetCounterName(id3.ID()));
    ASSERT_EQ("lisi", metrics->GetCounterFullName(id3.ID()));
  }

  {
    ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
    ASSERT_EQ(2, metrics->AllCounterSnapshots().size());
    ASSERT_EQ(0, metrics->AllHistogramSnapshots().size());

    for (auto& item : metrics->AllCounterSnapshots()) {
      if (Metrics::GetCounterName(item.first) == "zhangsan") {
        ASSERT_EQ(0, item.second);
      } else if (Metrics::GetCounterName(item.first) == "zhangsan") {
        ASSERT_EQ(0, item.second);
      }
    }
  }

  {
    uint32_t hid = 0;

    auto id = metrics->RegisterHistogram("lisi");
    hid = id.ID();
    ASSERT_EQ(hid, id.ID());
    ASSERT_EQ("lisi", metrics->GetHistogramName(id.ID()));
    ASSERT_EQ("lisi", metrics->GetHistogramFullName(id.ID()));

    auto id2 = metrics->RegisterHistogram("lisi");
    ASSERT_EQ(hid, id2.ID());
    ASSERT_EQ("lisi", metrics->GetHistogramName(id2.ID()));
    ASSERT_EQ("lisi", metrics->GetHistogramFullName(id2.ID()));

    auto id3 = metrics->RegisterHistogram("zhangsan");
    ASSERT_EQ(hid + 1, id3.ID());
    ASSERT_EQ("zhangsan", metrics->GetHistogramName(id3.ID()));
    ASSERT_EQ("zhangsan", metrics->GetHistogramFullName(id3.ID()));
  }

  {
    ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
    ASSERT_EQ(2, metrics->AllCounterSnapshots().size());
    ASSERT_EQ(2, metrics->AllHistogramSnapshots().size());

    for (auto& item : metrics->AllHistogramSnapshots()) {
      ASSERT_EQ(0, item.second->Min());
      ASSERT_EQ(0, item.second->Max());
      ASSERT_EQ(0, item.second->Avg());
      ASSERT_EQ(0, item.second->GetValue(0));
    }
  }

  {
    auto id = metrics->RegisterCounter("zhangsan");
    auto zhangsan = metrics->GetOrCreateCounter(id);
    zhangsan->Set(20);

    ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
    ASSERT_EQ(2, metrics->AllCounterSnapshots().size());
    ASSERT_EQ(2, metrics->AllHistogramSnapshots().size());

    auto snapshots = metrics->AllCounterSnapshots();
    ASSERT_EQ(20, snapshots.find(id.ID())->second);
  }

  {
    auto id = metrics->RegisterHistogram("zhangsan");
    auto zhangsan = metrics->GetOrCreateHistogram(id);
    zhangsan->Update(100);

    ASSERT_EQ(0, metrics->AllGaugeSnapshots().size());
    ASSERT_EQ(2, metrics->AllCounterSnapshots().size());
    ASSERT_EQ(2, metrics->AllHistogramSnapshots().size());

    auto snapshots = metrics->AllHistogramSnapshots();
    auto snapshot = snapshots.find(id.ID())->second;
    ASSERT_EQ(100, snapshot->Min());
    ASSERT_EQ(100, snapshot->Max());
    ASSERT_EQ(100, snapshot->Avg());
    ASSERT_EQ(100, snapshot->GetValue(0));
    ASSERT_EQ(100, snapshot->GetValue(1));
  }
}

}  // namespace dancenn

