// Copyright 2019 <PERSON> <<EMAIL>>

#include <absl/strings/str_split.h>
#include <base/string_utils.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <string>

namespace dancenn {

namespace {

class StringUtilsTest : public ::testing::Test {
};

}  // namespace

TEST_F(StringUtilsTest, StringToInt32) {
  int32_t ret = 0;
  std::string str = "abc";
  ASSERT_FALSE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  str = "2147483648";
  ASSERT_FALSE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  str = "2147483647";
  ASSERT_TRUE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  str = "0";
  ASSERT_TRUE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  str = "-1";
  ASSERT_TRUE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  // INT32_MIN
  str = "-2147483648";
  ASSERT_TRUE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
  // INT32_MIN - 1
  str = "-2147483649";
  ASSERT_FALSE(StringUtils::StringToInt32(str.c_str(), str.size(), &ret));
}

TEST_F(StringUtilsTest, StringToInt64) {
  int64_t ret = 0;
  std::string str = "abc";
  ASSERT_FALSE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
  str = "-9223372036854775807";
  ASSERT_TRUE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
  str = "9223372036854775808";
  ASSERT_FALSE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
  str = "9223372036854775807";
  ASSERT_TRUE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
  str = "0";
  ASSERT_TRUE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
  str = "-1";
  ASSERT_TRUE(StringUtils::StringToInt64(str.c_str(), str.size(), &ret));
}

TEST_F(StringUtilsTest, ToHexString) {
  ASSERT_EQ("74657374", StringUtils::ToHexString("test"));
  ASSERT_EQ("6279746564616e6365", StringUtils::ToHexString("bytedance"));
  ASSERT_EQ("68646673", StringUtils::ToHexString("hdfs"));
  ASSERT_EQ("6d6565706f", StringUtils::ToHexString("meepo"));
  ASSERT_EQ("212140402323", StringUtils::ToHexString("!!@@##"));
  ASSERT_EQ("", StringUtils::ToHexString(""));
  std::string s = "";
  s += static_cast<char>(128);
  ASSERT_EQ("80", StringUtils::ToHexString(s));
}
TEST_F(StringUtilsTest, HexStringTo) {
  ASSERT_EQ("test", StringUtils::HexStringTo("74657374"));
  ASSERT_EQ("bytedance", StringUtils::HexStringTo("6279746564616e6365"));
  ASSERT_EQ("hdfs", StringUtils::HexStringTo("68646673"));
  ASSERT_EQ("meepo", StringUtils::HexStringTo("6d6565706f"));
  ASSERT_EQ("!!@@##", StringUtils::HexStringTo("212140402323"));
  ASSERT_EQ("", StringUtils::HexStringTo(""));
  ASSERT_EQ("", StringUtils::HexStringTo("aaffoo"));
  std::string s = "";
  s += static_cast<char>(128);
  ASSERT_EQ(s, StringUtils::HexStringTo("80"));
}
TEST_F(StringUtilsTest, IsHexString) {
  ASSERT_TRUE(StringUtils::IsHexString("AAFF00"));
  ASSERT_TRUE(StringUtils::IsHexString("aaFF00"));
  ASSERT_FALSE(StringUtils::IsHexString("aaffoo"));
  ASSERT_FALSE(StringUtils::IsHexString("!!@@##"));
}

TEST_F(StringUtilsTest, UTF8) {
  std::string invalid_utf8;
  invalid_utf8.resize(1);
  invalid_utf8[0] = 0xE0;
  ASSERT_TRUE(StringUtils::IsValidUTF8("abcdefg"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("测试中文"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("åß∂œ∑®†¥"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("👋🌍"));
  ASSERT_FALSE(StringUtils::IsValidUTF8("\xF0\x28\x8C\xBC"));
  ASSERT_FALSE(StringUtils::IsValidUTF8(invalid_utf8));
}

TEST(StringUtil, SplitStringForAddr) {
  std::string str;
  std::vector<std::string> results;

  str = "";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 1);

  str = "a";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 1);
  EXPECT_EQ(results[0], "a");

  str = "a:";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "");

  str = ":b";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "b");

  str = "a:b";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "b");

  str = "[fdbd:dc02:103:73::162]:9209";
  StringUtils::SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "[fdbd:dc02:103:73::162]");
  EXPECT_EQ(results[1], "9209");
}

TEST(StringUtil, SplitStringForLocationTag) {
  std::string str;
  std::vector<std::string> results;

  str = "/cn-beijing-a/aa/192.168.0.20";
  results = StringUtils::SplitByChars(str, "/");
  EXPECT_EQ(results.size(), 3);

  str = "/cn-beijing-a/ /192.168.0.20";
  results = StringUtils::SplitByChars(str, "/");
  EXPECT_EQ(results.size(), 3);

  //  str = "/cn-beijing-a//192.168.0.20";
  //  results = StringUtils::SplitByChars(str, "/");
  //  EXPECT_EQ(results.size(), 3);

  str = "/cn-beijing-a/aa/192.168.0.20";
  results = absl::StrSplit(str, "/");
  LOG(INFO) << results[0];
  LOG(INFO) << results[1];
  LOG(INFO) << results[2];
  LOG(INFO) << results[3];
  EXPECT_EQ(results.size(), 4);

  str = "/cn-beijing-a/ /192.168.0.20";
  results = absl::StrSplit(str, "/");
  EXPECT_EQ(results.size(), 4);

  str = "/cn-beijing-a//192.168.0.20";
  results = absl::StrSplit(str, "/");
  EXPECT_EQ(results.size(), 4);
}

}  // namespace dancenn
