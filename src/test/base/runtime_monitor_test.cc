// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>
#include <glog/logging.h>

#include <atomic>
#include <chrono>
#include <thread>

#include "base/runtime_monitor.h"

DECLARE_string(namespace_meta_storage_path);
DECLARE_string(namespace_meta_storage_ckpt_path);

namespace dancenn {

class RuntimeMonitorTest : public testing::Test {
 public:
  void SetUp() override {
    backup1_ = FLAGS_namespace_meta_storage_path;
    backup2_ = FLAGS_namespace_meta_storage_ckpt_path;
    FLAGS_namespace_meta_storage_path = "/tmp";
    FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  }

  void TearDown() override {
    FLAGS_namespace_meta_storage_path = backup1_;
    FLAGS_namespace_meta_storage_ckpt_path = backup2_;
  }

 private:
  std::string backup1_;
  std::string backup2_;
};

TEST_F(RuntimeMonitorTest, Test01) {
  RuntimeMonitor rm;
  std::this_thread::sleep_for(std::chrono::milliseconds(50));
  uint64_t total = 0;
  uint64_t proc_total = 0;
  uint64_t proc_rss = 0;
  rm.GetMemoryUsage(&total, &proc_total, &proc_rss);
  ASSERT_GT(total, 0u);
  // TODO(ruanjunbin): This assertion will fail randomly.
  ASSERT_GT(proc_total, 0u);
  ASSERT_GT(proc_rss, 0u);
}

TEST_F(RuntimeMonitorTest, Test02) {
  std::atomic<bool> stop {false};
  std::vector<std::thread> ts;
  for (int i = 0; i < 3; ++i) {
    ts.emplace_back([&stop] () {
      int j = 0;
      while (!stop) {
        j++;
      }
    });
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
  RuntimeMonitor rm(10);
  double proc_cpu_usage;
  double load_avg_1;
  double load_avg_5;
  double load_avg_15;
  for (int i = 0; i < 5; ++i) {
    rm.GetCpuUsage(&proc_cpu_usage, &load_avg_1, &load_avg_5, &load_avg_15);
    LOG(INFO) << "cpu_usage: " << proc_cpu_usage
              << ", load_avg_1: " << load_avg_1
              << ", load_avg_5: " << load_avg_5
              << ", load_avg_15: " << load_avg_15;
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
  stop = true;
  for (auto& t : ts) {
    t.join();
  }
}

}  // namespace dancenn

