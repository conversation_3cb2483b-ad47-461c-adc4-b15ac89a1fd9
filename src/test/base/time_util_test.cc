#include "base/time_util.h"

#include <gtest/gtest.h>

#include "base/constants.h"

namespace dancenn {

namespace {

class TimeUtilTest : public ::testing::Test {};

}  // namespace

TEST_F(TimeUtilTest, GetTimeFormatted) {
  auto res = TimeUtil::GetTimeFormatted(1678096708, "%F %T");
  ASSERT_EQ("2023-03-06 09:58:28", res);

  res = TimeUtil::GetTimeFormatted(1678096708, kDefaultSnapshotNamePattern);
  ASSERT_EQ("s20230306-095828", res);
}

TEST_F(TimeUtilTest, GetEpochMsByFormat) {
  ASSERT_EQ(1676465341000,
            TimeUtil::GetEpochMsByFormat("2023-02-15T12:49:01Z",
                                         "%Y-%m-%dT%H:%M:%SZ"));
}

}  // namespace dancenn