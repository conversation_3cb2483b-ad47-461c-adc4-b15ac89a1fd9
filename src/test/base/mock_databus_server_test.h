// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef TEST_BASE_MOCK_DATABUS_SERVER_H_
#define TEST_BASE_MOCK_DATABUS_SERVER_H_

#include <cnetpp/concurrency/thread.h>
#include <cnetpp/base/socket.h>

#include <vector>
#include <atomic>

namespace dancenn {

class UnixListenSocketForDatabus : public cnetpp::base::ListenSocket {
 public:
  UnixListenSocketForDatabus() {}

  bool Create();

  bool Bind(const std::string& socket_path);
};

class MockDatabusServer {
 public:
  void SetUp(const std::string& channel_name,
             std::function<void(const std::string& message)> cb);

  void TearDown();

  void Run();

  void WorkerRun(std::shared_ptr<cnetpp::base::TcpSocket> socket);

  std::string channel_;
  std::atomic<bool> stopped_ { false };
  std::unique_ptr<cnetpp::concurrency::Thread> server_;
  std::vector<std::shared_ptr<cnetpp::concurrency::Thread>> workers_;
  UnixListenSocketForDatabus server_socket_;
  std::string test_path_ = "test_XXXXXX";
  std::string backup_FLAGS_databus_socket_path_;
  std::function<void(const std::string& message)> check_message_ { nullptr };
};

}  // namespace dancenn

#endif  // TEST_BASE_MOCK_DATABUS_SERVER_H_
