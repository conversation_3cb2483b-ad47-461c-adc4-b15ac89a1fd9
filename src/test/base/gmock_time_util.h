// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/02/21
// Description

#ifndef TEST_BASE_GMOCK_TIME_UTIL_H_
#define TEST_BASE_GMOCK_TIME_UTIL_H_

#include <gmock/gmock.h>  // For GMOCK_METHOD0.

#include <cstdint>  // For uint64_t.

class AbstractTimeUtilV2 {
 public:
  virtual uint64_t GetNowEpochMs() = 0;
};

class GMockTimeUtilV2 : public AbstractTimeUtilV2 {
 public:
  MOCK_METHOD0(GetNowEpochMs, uint64_t());
};

#endif  // TEST_BASE_GMOCK_TIME_UTIL_H_
