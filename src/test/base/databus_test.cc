// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/databus.h"

#include <cnetpp/concurrency/thread.h>
#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include <vector>

#include "base/mock_databus_server_test.h"
#include "base/defer.h"

DECLARE_string(databus_socket_path);

namespace dancenn {

TEST(DatabusTester, Test01) {
  std::string channel = "test01_channel";
  std::atomic<int> received_messages { 0 };
  std::string message;

  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    received_messages++;
    ASSERT_EQ(msg, message);
  });
  DEFER([&] () { server.TearDown(); });

  for (int i = 0; i < 1024; ++i) {
    message += "test01";
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
  auto ch = Databus::GetChannel(channel);
  ASSERT_TRUE(ch->Emit(message));
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  ASSERT_EQ(1, received_messages.load());
}

TEST(DatabusTester, Test02) {
  std::string channel = "test02_channel";
  std::atomic<int> received_messages { 0 };
  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    (void) msg;
    received_messages++;
  });
  DEFER([&] () { server.TearDown(); });
  auto ch = Databus::GetChannel(channel);
  std::vector<std::thread> clients;
  static const int kWorkerCount = 3;
  static const int kEmitCount = 100;
  for (int i = 0; i < kWorkerCount; ++i) {
    clients.emplace_back([ch, i] () {
      std::string message;
      for (int k = 0; k < 100; ++k) {
        message += std::to_string(i);
      }
      for (int j = 0; j < kEmitCount; ++j) {
        ASSERT_TRUE(ch->Emit(message));
      }
    });
  }
  for (int i = 0; i < kWorkerCount; ++i) {
    clients[i].join();
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  EXPECT_EQ(kWorkerCount * kEmitCount, received_messages.load());
}

}  // namespace dancenn
