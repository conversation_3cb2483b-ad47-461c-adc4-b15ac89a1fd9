#include "base/mime_type.h"

#include <gtest/gtest.h>

namespace dancenn {

TEST(MimeTypeTest, TestMimeTypeDefault) {
  {
    std::string name = "test.unknown";
    std::string mime_type_expect = "application/octet-stream";
    std::string mime_type_actual = MimeType::GetMimetypeByName(name);
    ASSERT_EQ(mime_type_expect, mime_type_actual);
  }

  {
    std::string name = "test.";
    std::string mime_type_expect = "application/octet-stream";
    std::string mime_type_actual = MimeType::GetMimetypeByName(name);
    ASSERT_EQ(mime_type_expect, mime_type_actual);
  }

  {
    std::string name = "test";
    std::string mime_type_expect = "application/octet-stream";
    std::string mime_type_actual = MimeType::GetMimetypeByName(name);
    ASSERT_EQ(mime_type_expect, mime_type_actual);
  }
}

TEST(MimeTypeTest, TestMimeType) {
  {
    std::string name = "test.txt";
    std::string mime_type_expect = "text/plain";
    std::string mime_type_actual = MimeType::GetMimetypeByName(name);
    ASSERT_EQ(mime_type_expect, mime_type_actual);
  }

  {
    std::string name = "test.test.txt";
    std::string mime_type_expect = "text/plain";
    std::string mime_type_actual = MimeType::GetMimetypeByName(name);
    ASSERT_EQ(mime_type_expect, mime_type_actual);
  }
}

} // namespace dancenn