// Copyright 2021 Xiong Mu <<EMAIL>>

#include "base/throttler.h"

#include <gtest/gtest.h>

#include <atomic>
#include <string>

#include "base/time_util.h"

namespace dancenn {

namespace {

class ThrottlerTest : public ::testing::Test {};

}  // namespace

// cost 10 seconds
TEST_F(ThrottlerTest, BaseThroltterTest) {
  std::atomic<uint32_t> qps{10000};
  auto TIME = 5;

  std::unique_ptr<Throttler> throltter =
      std::make_unique<BaseThrottler>([&]() { return qps.load(); });

  {
    auto start = TimeUtil::GetNowEpochMs();
    for (auto i = 0; i < TIME * qps.load(); ++i) {
      throltter->Acquire();
    }
    auto end = TimeUtil::GetNowEpochMs();
    auto duration = end - start;
    ASSERT_GE(duration, (TIME - 1) * 1000);
  }

  {
    qps = 20000;
    auto start = TimeUtil::GetNowEpochMs();
    for (auto i = 0; i < TIME * qps.load(); ++i) {
      throltter->Acquire();
    }
    auto end = TimeUtil::GetNowEpochMs();
    auto duration = end - start;
    ASSERT_GE(duration, (TIME - 1) * 1000);
  }
}

// cost 10 seconds
TEST_F(ThrottlerTest, ShardThroltterTest) {
  std::atomic<uint32_t> qps{10000};
  auto TIME = 5;
  auto THREAD_NUM = 32;

  std::unique_ptr<Throttler> throltter = std::make_unique<ShardThrottler>(
      [&]() { return qps.load(); }, THREAD_NUM);

  {
    auto start = TimeUtil::GetNowEpochMs();
    for (auto i = 0; i < TIME * qps.load() / THREAD_NUM; ++i) {
      throltter->Acquire();
    }
    auto end = TimeUtil::GetNowEpochMs();
    auto duration = end - start;
    ASSERT_GE(duration, (TIME - 1) * 1000);
  }

  {
    qps = 20000;
    auto start = TimeUtil::GetNowEpochMs();
    for (auto i = 0; i < TIME * qps.load() / THREAD_NUM; ++i) {
      throltter->Acquire();
    }
    auto end = TimeUtil::GetNowEpochMs();
    auto duration = end - start;
    ASSERT_GE(duration, (TIME - 1) * 1000);
  }
}

}  // namespace dancenn
