// Copyright 2017 <PERSON><PERSON> Huang <<EMAIL>>

#include <thread>
#include <shared_mutex>
#include <mutex>

#include <gtest/gtest.h>

#include "base/state_transition_lock.h"

namespace dancenn {

TEST(StateTransitionLock, Test01) {
  StateTransitionLock transition_lock;
  {
    std::shared_lock<StateTransitionLock> lock0(transition_lock);
    ASSERT_EQ(transition_lock.ref_count(), 1);
  }
  ASSERT_EQ(transition_lock.ref_count(), 0);

  volatile bool thread1_done = false;
  volatile bool thread2_done = false;
  std::thread thread1([&]() {
    std::shared_lock<StateTransitionLock> lock1(transition_lock);
    std::this_thread::sleep_for(std::chrono::seconds(6));
    thread1_done = true;
  });

  std::thread thread2([&]() {
    std::shared_lock<StateTransitionLock> lock2(transition_lock);
    std::this_thread::sleep_for(std::chrono::seconds(4));
    thread2_done = true;
  });

  std::this_thread::sleep_for(std::chrono::seconds(1));

  {
    std::unique_lock<StateTransitionLock> lock3(transition_lock);
    ASSERT_TRUE(thread1_done);
    ASSERT_TRUE(thread2_done);
    std::thread thread3([&]() {
      std::shared_lock<StateTransitionLock> lock4(transition_lock,
          std::try_to_lock);
      ASSERT_FALSE(lock4.owns_lock());
    });
    std::this_thread::sleep_for(std::chrono::seconds(3));
    thread3.join();
  }

  {
    std::shared_lock<StateTransitionLock> lock5(transition_lock,
                                            std::try_to_lock);
    ASSERT_TRUE(lock5.owns_lock());
  }


  thread1.join();
  thread2.join();
}

}  // namespace dancenn

