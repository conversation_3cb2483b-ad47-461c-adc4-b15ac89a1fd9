// Copyright 2019 livexmm <<EMAIL>>

#include <thread>
#include <shared_mutex>
#include <mutex>

#include <gtest/gtest.h>
#include <cnetpp/concurrency/thread.h>

#include "base/vlock.h"

namespace dancenn {

TEST(VLock, Test01) {
  VRWLock rwlock(1);
  VLock vlock(rwlock.lock());
  {
    std::shared_lock<VLock> lock0(vlock);
    ASSERT_EQ(vlock.ref_count(), 1);
  }
  ASSERT_EQ(vlock.ref_count(), 0);

  volatile bool thread1_done = false;
  volatile bool thread2_done = false;
  std::thread thread1([&]() {
    std::shared_lock<VLock> lock1(vlock);
    std::this_thread::sleep_for(std::chrono::milliseconds(600));
    thread1_done = true;
  });

  std::thread thread2([&]() {
    std::shared_lock<VLock> lock2(vlock);
    std::this_thread::sleep_for(std::chrono::milliseconds(400));
    thread2_done = true;
  });

  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  {
    std::unique_lock<VLock> lock3(vlock);
    ASSERT_TRUE(thread1_done);
    ASSERT_TRUE(thread2_done);
    std::thread thread3([&]() {
      std::shared_lock<VLock> lock4(vlock, std::try_to_lock);
      ASSERT_FALSE(lock4.owns_lock());
    });
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    thread3.join();
  }

  {
    std::shared_lock<VLock> lock5(vlock, std::try_to_lock);
    ASSERT_TRUE(lock5.owns_lock());
  }

  thread1.join();
  thread2.join();
}

TEST(VLock, Test02) {
  static const int N = 10;
  VRWLock rwlock(N);
  VLock vlock(rwlock.lock());

  volatile int x = 0;
  volatile int y = 0;
  volatile int z = 0;
  volatile bool finish = false;

  using CNetppThread = cnetpp::concurrency::Thread;
  std::vector<std::shared_ptr<cnetpp::concurrency::Thread>> v;
  auto fn = [&]() -> bool {
    while (!finish) {
      std::shared_lock<VLock> guard(vlock);
      int xx = x;
      int yy = y;
      int zz = z;
      [&](){
        ASSERT_TRUE(xx == yy);
        ASSERT_TRUE(xx == zz);
      }();
    }
    return true;
  };
  for (int i = 0; i < N; i++) {
    auto th = std::make_shared<CNetppThread>(fn);
    v.emplace_back(th);
  }
  for (int i = 0; i < N; i++) {
    v[i]->Start();
  }

  sleep(1);
  for (int i = 0; i < 1000000; i++) {
    std::unique_lock<VLock> guard(vlock);
    x = x + 1;
    y = y + 1;
    z = z + 1;
  }

  finish = true;
  for (int i = 0; i < N; i++) {
    v[i]->Stop();
  }
}

TEST(VLock, Test03) {
  static const int N = 10;
  VRWLock rwlock(N);

  volatile int x = 0;
  volatile int y = 0;
  volatile int z = 0;
  volatile bool finish = false;

  using CNetppThread = cnetpp::concurrency::Thread;
  std::vector<std::shared_ptr<cnetpp::concurrency::Thread>> v;
  auto fn = [&]() -> bool {
    while (!finish) {
      dancenn::vshared_lock guard(rwlock.lock());
      volatile int xx = x;
      volatile int yy = y;
      volatile int zz = z;
      [&](){
        ASSERT_TRUE(xx == yy);
        ASSERT_TRUE(xx == zz);
      }();
    }
    return true;
  };
  for (int i = 0; i < N * 2; i++) {
    auto th = std::make_shared<CNetppThread>(fn);
    v.emplace_back(th);
  }
  for (int i = 0; i < N * 2; i++) {
    v[i]->Start();
  }

  sleep(1);
  for (int i = 0; i < 1000000; i++) {
    dancenn::vunique_lock guard(rwlock.lock());
    x = x + 1;
    y = y + 1;
    z = z + 1;
  }

  finish = true;
  for (int i = 0; i < N; i++) {
    v[i]->Stop();
  }
}

std::pair<int, dancenn::vshared_lock> get_vshared_lock(VRWLock* lock) {
  std::pair<int, dancenn::vshared_lock> res {0,
    dancenn::vshared_lock(lock->lock())};
  return res;
}

std::pair<int, dancenn::vshared_lock> get_vshared_try_lock(VRWLock* lock) {
  std::pair<int, dancenn::vshared_lock> res {0,
    dancenn::vshared_lock(lock->lock(), std::try_to_lock)};
  return res;
}

dancenn::vunique_lock get_vunique_lock(VRWLock* lock) {
  return dancenn::vunique_lock(lock->lock());
}

TEST(VLock, Test04) {
  VRWLock lock(40);
  {
    auto x = get_vshared_lock(&lock);
    auto y = get_vshared_lock(&lock);
  }
  {
    auto z = get_vunique_lock(&lock);
  }
  {
    auto z = get_vunique_lock(&lock);
    auto x = get_vshared_try_lock(&lock);
    ASSERT_EQ(x.second.owns_lock(), false);
    auto y = get_vshared_try_lock(&lock);
    ASSERT_EQ(x.second.owns_lock(), false);
  }
}

TEST(VLock, Test05) {
  static const int N = 10;
  VRWLock rwlock(N);

  volatile int x = 0;
  volatile int y = 0;
  volatile int z = 0;
  volatile bool finish = false;

  using CNetppThread = cnetpp::concurrency::Thread;
  std::vector<std::shared_ptr<cnetpp::concurrency::Thread>> v;
  auto fn = [&]() -> bool {
    while (!finish) {
      auto hh = get_vshared_try_lock(&rwlock);
      if (!hh.second.owns_lock()) continue;
      volatile int xx = x;
      volatile int yy = y;
      volatile int zz = z;
      [&](){
        ASSERT_EQ(xx, yy);
        ASSERT_EQ(xx, zz);
      }();
    }
    return true;
  };
  for (int i = 0; i < N * 2; i++) {
    auto th = std::make_shared<CNetppThread>(fn);
    v.emplace_back(th);
  }
  for (int i = 0; i < N * 2; i++) {
    v[i]->Start();
  }

  sleep(1);
  for (int i = 0; i < 1000000; i++) {
    dancenn::vunique_lock vlock(rwlock.lock());
    x = x + 1;
    y = y + 1;
    z = z + 1;
  }

  finish = true;
  for (int i = 0; i < N; i++) {
    v[i]->Stop();
  }
}

}  // namespace dancenn

