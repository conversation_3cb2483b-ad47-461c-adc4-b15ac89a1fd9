// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/net_utils.h"

#include <gtest/gtest.h>

namespace dancenn {

TEST(NetUtilsTest, Test01) {
  auto ipsv4 = net::GetHostAddresses(false);
  ASSERT_GT(ipsv4.size(), 0);
  auto ips1v4 = net::GetHostAddresses(false);
  ASSERT_EQ(ipsv4.size(), ips1v4.size());
  for (auto& ipp : ipsv4) {
    auto itr = ips1v4.find(ipp.first);
    ASSERT_TRUE(itr != ips1v4.end());
    ASSERT_EQ(ipp.second.ToString(), itr->second.ToString());
  }
  auto ipsv6 = net::GetHostAddresses(true);
  auto ips1v6 = net::GetHostAddresses(true);
  ASSERT_EQ(ipsv6.size(), ips1v6.size());
  for (auto& ipp : ipsv6) {
    auto itr = ips1v6.find(ipp.first);
    ASSERT_TRUE(itr != ips1v6.end());
    ASSERT_EQ(ipp.second.ToString(), itr->second.ToString());
  }
}

}  // namespace dancenn

