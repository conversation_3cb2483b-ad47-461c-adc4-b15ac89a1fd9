// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/token_bucket.h"

#include <gtest/gtest.h>

#include <chrono>

namespace dancenn {

TEST(TokenBucketTest, Test01) {
  {
    TokenBucket tb(10, 10);
    ASSERT_DOUBLE_EQ(tb.token_generate_rate(), 10.);
    ASSERT_DOUBLE_EQ(tb.burst(), 10.);
    ASSERT_DOUBLE_EQ(tb.Available(), 10.);
    ASSERT_TRUE(tb.Consume(5));
    auto r = tb.ConsumePartial(10);
    ASSERT_GE(r, 5.);
    ASSERT_LT(r, 10);
    ASSERT_LT(tb.Available(), 1.);
  }
  {
    TokenBucket tb(10, 10);
    ASSERT_TRUE(tb.Consume(5));
    ASSERT_FALSE(tb.Consume(10));
    ASSERT_GE(tb.Available(), 5);
    ASSERT_TRUE(tb.Consume(5));
    ASSERT_LT(tb.Available(), 1.);
  }
}

}  // namespace dancenn

