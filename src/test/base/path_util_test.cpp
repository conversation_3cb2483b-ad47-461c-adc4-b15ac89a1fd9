//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguo<PERSON> <<EMAIL>>
//
#include <gtest/gtest.h>
#include <glog/logging.h>

#include <string>
#include <vector>

#include "base/path_util.h"

namespace dancenn {

TEST(BasePath, SplitPath) {
  std::string path = "";
  std::vector<::cnetpp::base::StringPiece> path_components;
  ASSERT_EQ(SplitPath(path, &path_components), false);

  path = "/";
  ASSERT_EQ(SplitPath(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 0);

  path = "/a";
  ASSERT_EQ(SplitPath(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 1);

  path = "/a/b";
  ASSERT_EQ(SplitPath(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 2);

  path = "//a/b";
  ASSERT_EQ(SplitPath(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 2);
  ASSERT_EQ(path_components[0], "a");
  ASSERT_EQ(path_components[1], "b");

  path = "/a/b/c";
  ASSERT_EQ(SplitPath(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 3);
  LOG(INFO) << "split path " << path << " into:";
  for (const auto &x : path_components) {
    LOG(INFO) << x;
  }
}

TEST(BasePath, GetFileNameFromPath) {
  ASSERT_EQ(GetFileNameFromPath(""), "");
  ASSERT_EQ(GetFileNameFromPath("/a/b/c"), "c");
  ASSERT_EQ(GetFileNameFromPath("/a/b/c/"), "c");
  ASSERT_EQ(GetFileNameFromPath("/a/b////"), "b");
  ASSERT_EQ(GetFileNameFromPath("abc"), "abc");
  ASSERT_EQ(GetFileNameFromPath("a/bc"), "bc");
  ASSERT_EQ(GetFileNameFromPath("a///bc"), "bc");
}

TEST(BasePath, GetAllAncestorPaths) {
  std::string path = "";
  std::vector<::cnetpp::base::StringPiece> path_components;
  ASSERT_EQ(SplitPath(path, &path_components), false);

  path = "/";
  ASSERT_EQ(GetAllAncestorPaths(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 1);

  path = "/a";
  ASSERT_EQ(GetAllAncestorPaths(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 2);

  path = "/a/b";
  ASSERT_EQ(GetAllAncestorPaths(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 3);

  path = "//a/b";
  ASSERT_EQ(GetAllAncestorPaths(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 4);
  ASSERT_EQ(path_components[0], "/");
  ASSERT_EQ(path_components[1], "/");
  ASSERT_EQ(path_components[2], "//a");
  ASSERT_EQ(path_components[3], "//a/b");

  path = "/a/b/c";
  ASSERT_EQ(GetAllAncestorPaths(path, &path_components), true);
  ASSERT_EQ(path_components.size(), 4);
  LOG(INFO) << "incremental split path " << path << " into:";
  for (const auto &x : path_components) {
    LOG(INFO) << x;
  }
}

TEST(BasePath, TransformDotInPath) {
  std::string path = "/../../..";
  ASSERT_EQ(TransformDotInPath(&path), false);

  path = "/.././.";
  ASSERT_EQ(TransformDotInPath(&path), false);

  path = "/a/b/..";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/a");

  path = "/a/b/../..";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/");

  path = "/././.";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/");

  path = "/./..../../.";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/");

  path = "/..../.././.";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/");

  path = "/a/b/.../../././.";
  ASSERT_EQ(TransformDotInPath(&path), true);
  ASSERT_EQ(path, "/a/b");
}

TEST(BasePath, RemoveRedundantSlash) {
  std::string path = "///";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/");

  path = "/";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/");

  path = "/a";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a");

  path = "//a";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a");

  path = "//a/";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a");

  path = "/a/b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b");

  path = "/a//b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b");

  path = "/a//b///";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b");

  path = "///a/b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b");

  path = "/a/b/c/";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b/c");

  path = "//a//b//c//";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "/a/b/c");

  path = "a";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a");

  path = "a//";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a");

  path = "a///";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a");

  path = "a/b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");

  path = "a//b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");

  path = "a///b";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");

  path = "a///b/";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");

  path = "a///b//";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");

  path = "a///b///";
  RemoveRedundantSlash(&path);
  ASSERT_EQ(path, "a/b");
}

TEST(BasePath, NormalizePath) {
  std::string path = "";
  std::string user_name = "root";
  std::string normalized_path;
  ASSERT_EQ(NormalizePath(path, "", &normalized_path), false);

  path = "///";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/");

  path = "///a/..//b";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/b");

  path = "/a/b/c/../../../..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), false);

  path = "//a//b//../././c//../..../.";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/a/....");

  path = "//..../../a//b//c//";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/a/b/c");

  path = "/./././b//c//";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/b/c");

  path = "/../././b//c//";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), false);

  path = "././.";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root");

  path = "./../../";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/");

  path = "./../..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/");

  path = "./..../";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root/....");

  path = "./..../..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root");

  path = "../../..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), false);

  path = "../././.";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user");

  path = "....";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root/....");

  path = ".";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root");

  path = "..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user");

  path = "../..";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/");

  path = "";
  ASSERT_EQ(NormalizePath(path, user_name, &normalized_path), true);
  ASSERT_EQ(normalized_path, "/user/root");
}

TEST(BasePath, GetRecycleBinPath) {
  std::string rbprefix = kSeparator + kRecycleBinDirNameString;
  std::string dirprefix = "/tstdirname";
  std::string filepath = "/tstfile";
  std::string suffix = "-suffix";
  std::string username = "root";
  std::string rb_path;
  std::vector<cnetpp::base::StringPiece> path_comps;
  bool ok;

  std::string testpath = dirprefix + filepath;
  while (true) {
    ASSERT_TRUE(SplitPath(testpath, &path_comps));
    ok = GetRecycleBinPath(path_comps, username, &rb_path);
    ASSERT_TRUE(ok);

    ASSERT_TRUE(SplitPath(rb_path, &path_comps));
    ASSERT_LE(path_comps.back().length(), FLAGS_max_component_length);

    ASSERT_TRUE(SplitPath(rbprefix + testpath, &path_comps));
    ok = GetRecycleBinPath(path_comps, username, &rb_path);
    ASSERT_FALSE(ok);

    if (testpath.length() >= FLAGS_max_path_length) {
      break;
    }
    testpath = dirprefix + testpath;
    testpath = testpath.substr(0, FLAGS_max_path_length);
  }

  testpath = filepath;
  while (true) {
    ASSERT_TRUE(SplitPath(testpath, &path_comps));
    ok = GetRecycleBinPath(path_comps, username, &rb_path);
    ASSERT_TRUE(ok);

    ASSERT_TRUE(SplitPath(rb_path, &path_comps));
    ASSERT_LE(path_comps.back().length(), FLAGS_max_component_length);

    ASSERT_TRUE(SplitPath(rbprefix + testpath, &path_comps));
    ok = GetRecycleBinPath(path_comps, username, &rb_path);
    ASSERT_FALSE(ok);

    if (testpath.length() >= FLAGS_max_component_length) {
      break;
    }
    testpath = testpath + suffix;
    testpath = testpath.substr(0, FLAGS_max_component_length);
  }
}

TEST(BasePath, JoinTwoPath) {
  ASSERT_EQ("", JoinTwoPath("", ""));
  ASSERT_EQ("a", JoinTwoPath("a", ""));
  ASSERT_EQ("b", JoinTwoPath("", "b"));
  ASSERT_EQ("a/b", JoinTwoPath("a", "b"));
  ASSERT_EQ("a/b", JoinTwoPath("a/", "b"));
  ASSERT_EQ("a/b", JoinTwoPath("a", "/b"));
  ASSERT_EQ("a/b", JoinTwoPath("a/", "/b"));
  ASSERT_EQ("/a", JoinTwoPath("/", "a"));
  ASSERT_EQ("/a", JoinTwoPath("/", "/a"));
  ASSERT_EQ("/a/b", JoinTwoPath("/a", "/b"));
  ASSERT_EQ("/a/b", JoinTwoPath("/a", "b"));
}

}  // namespace dancenn
