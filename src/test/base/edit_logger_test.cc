// Copyright 2022 Xin<PERSON> Gao <<EMAIL>>


#include <gtest/gtest.h>

#include <memory>

#include "base/defer.h"
#include "base/edit_logger.h"
#include "base/mock_databus_server_test.h"


namespace dancenn {

TEST(EditLoggerTest, Test01) {
  std::string channel = "dancenn_editlog";
  std::unique_ptr<dancenn::EditLogger> edit_logger(
      new dancenn::DatabusEditLogger(channel));
  dancenn::EditLogger::Init(std::move(edit_logger));

  std::atomic<int> received { 0 };
  std::shared_ptr<EditLogOp> op = std::make_shared<OpWaitNoPending>();

  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    received++;
  });
  DEFER([&] () { server.TearDown(); });

  std::this_thread::sleep_for(std::chrono::milliseconds(10));

  EditLogger::VLog(op);

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(1, received.load());
}

TEST(EditLoggerTest, Test02) {
  std::string channel = "dancenn_editlog";
  std::unique_ptr<dancenn::EditLogger> edit_logger(
      new dancenn::DatabusEditLogger(channel));
  dancenn::EditLogger::Init(std::move(edit_logger));

  std::atomic<int> received { 0 };
  std::shared_ptr<EditLogOp> op = std::make_shared<OpWaitNoPending>();

  MockDatabusServer server;
  server.SetUp(channel, [&] (const std::string& msg) {
    received++;
  });
  DEFER([&] () { server.TearDown(); });

  std::this_thread::sleep_for(std::chrono::milliseconds(10));

  for(int i = 0; i < 100; ++i) {
    EditLogger::VLog(op);
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(1000));

  ASSERT_EQ(100, received.load());
}

}  // namespace dancenn