// Copyright 2017 <PERSON><PERSON> Huang <<EMAIL>>

#include <chrono>

#include <gtest/gtest.h>
#include "base/closure.h"
#include "cnetpp/concurrency/thread.h"

namespace dancenn {

class ClosureTest1 : public dancenn::Closure {
  void Run() override {}
};

TEST(ClosureTest, Closure) {
  VRWLock rwlock(1);
  {
    ClosureTest1 closure1;
    closure1.set_status(Status(JavaExceptions::kIOException));
    dancenn::vshared_lock lock(rwlock.lock());
    ASSERT_TRUE(lock.owns_lock());
    ASSERT_TRUE(closure1.status().HasException());
    closure1.set_barrier(std::move(lock));
    ASSERT_TRUE(closure1.OwnsLock());
  }
}

TEST(ClosureTest, SynchronizedClosure) {
  SynchronizedClosure closure1(1);
  std::thread worker([&]() {
    std::this_thread::sleep_for(std::chrono::seconds(5));
    closure1.set_status(Status(JavaExceptions::kIOException));
    closure1.Run();
  });
  closure1.Await();
  ASSERT_TRUE(closure1.status().HasException());
  worker.join();
}
}  // namespace dancenn
