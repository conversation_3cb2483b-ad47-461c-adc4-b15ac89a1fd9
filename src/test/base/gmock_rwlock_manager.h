// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/05/26
// Description

#ifndef TEST_BASE_GMOCK_RWLOCK_MANAGER_H_
#define TEST_BASE_GMOCK_RWLOCK_MANAGER_H_

#include <cnetpp/base/string_piece.h>  // For StringPiece.
#include <gmock/gmock.h>               // For MOCK_METHOD2.

#include <cstdint>  // For uint32_t.
#include <memory>   // For unique_ptr.
#include <vector>   // For vector.

#include "base/rwlock_manager.h"  // For RWLockManager, LockHolder, PathLockMapPtr.

namespace dancenn {

// TODO: We should write a base class named RWLockManagerBase.
class GMockRWLockManager : public RWLockManager {
 public:
  GMockRWLockManager() : RWLockManager(0) {
  }

  MOCK_METHOD2(
      Lock<PERSON><PERSON><PERSON>,
      PathLockMapPtr(const std::vector<cnetpp::base::StringPiece>& paths,
                     bool last_wlock));
  MOCK_METHOD2(
      ReadLock,
      std::unique_ptr<LockHolder>(const cnetpp::base::StringPiece& token,
                                  uint32_t depth));
  MOCK_METHOD2(
      WriteLock,
      std::unique_ptr<LockHolder>(const cnetpp::base::StringPiece& token,
                                  uint32_t depth));
};

}  // namespace dancenn

#endif  // TEST_BASE_GMOCK_RWLOCK_MANAGER_H_
