// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <glog/logging.h>
#include <gtest/gtest.h>

#include <chrono>

#include "base/pb_converter.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/cloudfs/xattr.pb.h"

namespace dancenn {

// test case:
// message LocatedBlockProto {
//   required ExtendedBlockProto b  = 1;
//   required uint64 offset = 2;
//   repeated DatanodeInfoProto locs = 3;
//   required bool corrupt = 4;
//   required hadoop.common.TokenProto blockToken = 5;
//   repeated bool isCached = 6 [packed=true];
//   repeated StorageTypeProto storageTypes = 7;
//   repeated string storageIDs = 8;
//   optional DatanodeInfoWithStorageProto replicaPipeliner = 9;
//   optional LocatedBlockProto originalLocatedBlock = 10;
// }
TEST(PBConverter, Test01) {
  cloudfs::LocatedBlockProto msg;
  auto b = msg.mutable_b();
  b->set_poolid("BP-test");
  b->set_blockid(10);
  b->set_blockid(10);
  b->set_generationstamp(10);
  b->set_numbytes(512);
  msg.set_offset(0);
  auto locs = msg.add_locs();
  auto id = locs->mutable_id();
  id->set_ipaddr("0.0.0.0");
  id->set_hostname("localhost");
  id->set_datanodeuuid("xxxx-xx-x-xxxx-xx");
  id->set_xferport(8888);
  id->set_infoport(8889);
  msg.set_corrupt(false);
  auto bt = msg.mutable_blocktoken();
  bt->set_identifier("identifier");
  bt->set_password("\x01\x01\x01\x01identifier");
  bt->set_kind("kind");
  bt->set_service("service");
  msg.add_iscached(true);
  msg.add_iscached(true);
  msg.add_iscached(false);
  msg.add_storagetypes(cloudfs::StorageTypeProto::DISK);
  msg.add_storagetypes(cloudfs::StorageTypeProto::SSD);
  msg.add_storageids()->append("storage1");
  msg.add_storageids()->append("storage2");
  msg.add_storageids()->append("storage3");
  auto json_obj = PBConverter::ToJson(msg);
  cnetpp::base::Parser parser;
  std::string res;
  parser.Serialize(cnetpp::base::Value(json_obj), &res);

  LOG(INFO) << res;
}

TEST(PBConverter, TestBytes) {
  cloudfs::XAttrProto proto;
  proto.set_namespace_(cloudfs::XAttrProto::USER);
  proto.set_name("x");
  proto.set_value("y");
  EXPECT_EQ(PBConverter::ToCompactJsonString(proto),
            R"({"name":"x","namespace":0,"value":"eQ=="})");
}

}  // namespace dancenn
