// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <base/rwlock_manager.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <cnetpp/base/string_piece.h>

#include <gtest/gtest.h>

#include <thread>
#include <random>

namespace dancenn {

TEST(RWLockManager, Concurrent) {
  cnetpp::concurrency::ThreadPool tp("test");
  tp.set_num_threads(10);
  tp.Start();

  std::vector<cnetpp::base::StringPiece> tokens;
  tokens.emplace_back("a");
  tokens.emplace_back("b");
  //tokens.emplace_back("c");
  for (const auto& i : tokens) {
    LOG(INFO) << i.as_string();
  }

  RWLockManager manager(0);
  for (int t = 0; t < 10; ++t) {
    tp.AddTask([&manager, &tokens]() -> bool {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<> lock_dis(0, 1);
      std::uniform_int_distribution<> token_dis(0, tokens.size() - 1);
      std::uniform_int_distribution<> sleep_dis(0, 50);
      for (int i = 0; i < 100; ++i) {
        if (lock_dis(gen) == 0) {
          size_t idx = token_dis(gen);
          manager.ReadLock(tokens[idx], 0);
          manager.WriteLock(tokens[(i + 1) % tokens.size()], 0);
        } else {
          manager.WriteLock(tokens[token_dis(gen)], 0);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_dis(gen)));
      }
      return true;
    });
  }
  tp.Stop(true);
  ASSERT_EQ(manager.NumDynamicLocks(), 0);
  ASSERT_EQ(manager.NumStaticLocks(), 0);
}

TEST(RWLockManager, ConcurrentStatic) {
  cnetpp::concurrency::ThreadPool tp("test");
  tp.set_num_threads(10);
  tp.Start();

  std::vector<cnetpp::base::StringPiece> tokens;
  tokens.emplace_back("a");
  tokens.emplace_back("b");
  //tokens.emplace_back("c");
  for (const auto& i : tokens) {
    LOG(INFO) << i.as_string();
  }

  RWLockManager manager(2);
  for (int t = 0; t < 10; ++t) {
    tp.AddTask([&manager, &tokens]() -> bool {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<> lock_dis(0, 1);
      std::uniform_int_distribution<> token_dis(0, tokens.size() - 1);
      std::uniform_int_distribution<> sleep_dis(0, 50);
      for (int i = 0; i < 100; ++i) {
        if (lock_dis(gen) == 0) {
          size_t idx = token_dis(gen);
          manager.ReadLock(tokens[idx], 0);
          manager.WriteLock(tokens[(i + 1) % tokens.size()], 0);
        } else {
          manager.WriteLock(tokens[token_dis(gen)], 0);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_dis(gen)));
      }
      return true;
    });
  }
  tp.Stop(true);
  ASSERT_EQ(manager.NumDynamicLocks(), 0);
  ASSERT_EQ(manager.NumStaticLocks(), 2);
}

TEST(RWLockManager, Test01) {
  RWLockManager manager(2);
  {
    auto rwlock1_holder1 = manager.ReadLock("rwlock1", 2);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), 1);
    ASSERT_TRUE(rwlock1_holder1->IsReadLock());
    auto rwlock1_holder2 = manager.ReadLock("rwlock1", 2);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), 2);
    ASSERT_TRUE(rwlock1_holder1->IsReadLock());
    ASSERT_EQ(rwlock1_holder2->NumHolders(), 2);
    ASSERT_TRUE(rwlock1_holder2->IsReadLock());
  }
  {
    auto rwlock1_holder1 = manager.WriteLock("rwlock1", 2);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), 1);
    ASSERT_FALSE(rwlock1_holder1->IsReadLock());
    auto rwlock2_holder1 = manager.WriteLock("rwlock2", 2);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), 1);
    ASSERT_FALSE(rwlock1_holder1->IsReadLock());
    ASSERT_EQ(rwlock2_holder1->NumHolders(), 1);
    ASSERT_FALSE(rwlock2_holder1->IsReadLock());
  }
  {
    std::vector<cnetpp::base::StringPiece> paths;
    paths.emplace_back("/a");
    paths.emplace_back("/a/b");
    paths.emplace_back("/a/b/c");
    paths.emplace_back("/a/b/c/d");
    auto holders = manager.LockPaths(paths);
    ASSERT_EQ(holders->size(), 4);
    for (size_t i = 0; i < paths.size(); ++i) {
      auto itr = holders->find(paths[i]);
      if (i == paths.size() - 1) {
        ASSERT_FALSE(itr->second->IsReadLock());
      } else {
        ASSERT_TRUE(itr->second->IsReadLock());
      }
    }
    ASSERT_EQ(manager.NumStaticLocks(), 2);
    ASSERT_EQ(manager.NumDynamicLocks(), 2);
  }

  ASSERT_EQ(manager.NumStaticLocks(), 2);
  ASSERT_EQ(manager.NumDynamicLocks(), 0);
}

TEST(RWLockManager, Test02) {
  RWLockManager manager(2);
  {
    auto rwlock1_holder1 = manager.ReadLock("rwlock1", 1);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), UINT32_MAX);
    ASSERT_TRUE(rwlock1_holder1->IsReadLock());
    auto rwlock1_holder2 = manager.ReadLock("rwlock1", 1);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), UINT32_MAX);
    ASSERT_TRUE(rwlock1_holder1->IsReadLock());
    ASSERT_EQ(rwlock1_holder2->NumHolders(), UINT32_MAX);
    ASSERT_TRUE(rwlock1_holder2->IsReadLock());
  }
  {
    auto rwlock1_holder1 = manager.WriteLock("rwlock1", 1);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), UINT32_MAX);
    ASSERT_FALSE(rwlock1_holder1->IsReadLock());
    auto rwlock2_holder1 = manager.WriteLock("rwlock2", 1);
    ASSERT_EQ(rwlock1_holder1->NumHolders(), UINT32_MAX);
    ASSERT_FALSE(rwlock1_holder1->IsReadLock());
    ASSERT_EQ(rwlock2_holder1->NumHolders(), UINT32_MAX);
    ASSERT_FALSE(rwlock2_holder1->IsReadLock());
  }
}

TEST(RWLockManager, Test03) {
  RWLockManager manager(2);

  std::vector<cnetpp::base::StringPiece> paths;
  paths.emplace_back("/");

  {
    auto holders = manager.LockPaths(paths, false);
    for (size_t i = 0; i < paths.size(); i++) {
      auto itr = holders->find(paths[i]);
      ASSERT_TRUE(itr->second->IsReadLock());
    }
    ASSERT_EQ(manager.NumStaticLocks(), 1);
    ASSERT_EQ(manager.NumDynamicLocks(), 0);
    auto holders2 = manager.LockPaths(paths, false);
    for (size_t i = 0; i < paths.size(); i++) {
      auto itr = holders2->find(paths[i]);
      ASSERT_TRUE(itr->second->IsReadLock());
    }
    ASSERT_EQ(manager.NumStaticLocks(), 1);
    ASSERT_EQ(manager.NumDynamicLocks(), 0);
  }

  ASSERT_EQ(manager.NumStaticLocks(), 1);
  ASSERT_EQ(manager.NumDynamicLocks(), 0);

  {
    auto holders = manager.LockPaths(paths, true);
    for (size_t i = 0; i < paths.size(); i++) {
      auto itr = holders->find(paths[i]);
      ASSERT_FALSE(itr->second->IsReadLock());
    }
    ASSERT_EQ(manager.NumStaticLocks(), 1);
    ASSERT_EQ(manager.NumDynamicLocks(), 0);
  }

  ASSERT_EQ(manager.NumStaticLocks(), 1);
  ASSERT_EQ(manager.NumDynamicLocks(), 0);
}

}  // namespace dancenn
