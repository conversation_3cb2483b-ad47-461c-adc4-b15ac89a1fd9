#include <base/dictionary.h>

#include <gtest/gtest.h>

namespace dancenn {

TEST(Dictionary, Test01) {
  Dictionary<std::string, uint32_t> dict;
  ASSERT_EQ(kInvalidId, dict.forward_lookup("asd"));
  ASSERT_EQ(1, dict.Assign("asd"));
  ASSERT_EQ(1, dict.forward_lookup("asd"));
  ASSERT_EQ("asd", dict.reverse_lookup(1));
  ASSERT_EQ("", dict.reverse_lookup(2));

  // test free_list_
  dict.Unassign("asd");
  ASSERT_EQ(kInvalidId, dict.forward_lookup("asd"));
  ASSERT_EQ("", dict.reverse_lookup(1));
  ASSERT_EQ(1, dict.Assign("asd2"));
  ASSERT_EQ(1, dict.forward_lookup("asd2"));
}

TEST(Dictionary, Test02) {
  Dictionary<std::string, uint32_t> dict;
  ASSERT_EQ(1, dict.AssignOrLookup("asd"));
  ASSERT_EQ(1, dict.AssignOrLookup("asd"));
  ASSERT_EQ(2, dict.AssignOrLookup("asd2"));
  ASSERT_EQ(3, dict.AssignOrLookup("asd3"));
  ASSERT_EQ(4, dict.AssignOrLookup("asd4"));
  ASSERT_EQ(3, dict.AssignOrLookup("asd3"));
}

TEST(Dictionary, Test03) {
  Dictionary<std::string, uint32_t> dict;
  ASSERT_EQ(1, dict.AssignTo("asd", 1));
  ASSERT_EQ(0, dict.AssignTo("asd", 1));
  ASSERT_EQ(3, dict.AssignTo("asd2", 3));
  ASSERT_EQ(0, dict.AssignTo("asd2", 2));
  ASSERT_EQ(2, dict.AssignTo("asd3", 2));
  ASSERT_EQ(0, dict.AssignTo("asd3", 2));
  ASSERT_EQ(0, dict.AssignTo("asd4", 1));
}

}  // namespace dancenn
