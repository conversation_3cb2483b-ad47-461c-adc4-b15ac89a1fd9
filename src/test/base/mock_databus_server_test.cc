// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "base/mock_databus_server_test.h"

#include <gtest/gtest.h>
#include <gflags/gflags.h>

#include "collector.pb.h"
#include "base/defer.h"
#include "base/file_utils.h"
#include "base/platform.h"
#include "base/constants.h"

DECLARE_string(databus_socket_path);

namespace dancenn {

bool UnixListenSocketForDatabus::Create() {
  return Socket::Create(AF_UNIX, SOCK_STREAM, 0);
}

bool UnixListenSocketForDatabus::Bind(const std::string& socket_path) {
  struct sockaddr_un socket_addr;
  memset(&socket_addr, 0, sizeof(struct sockaddr_un));
  socket_addr.sun_family = AF_UNIX;
  snprintf(socket_addr.sun_path, UNIX_PATH_MAX, "%s", socket_path.c_str());
  return bind(fd(),
              reinterpret_cast<const struct sockaddr*>(&socket_addr),
              sizeof(socket_addr)) == 0;
}

void MockDatabusServer::SetUp(
    const std::string& channel_name,
    std::function<void(const std::string& message)> cb) {
  channel_ = channel_name;
  check_message_ = cb;
  backup_FLAGS_databus_socket_path_ = FLAGS_databus_socket_path;
  CHECK(mkdtemp(&(test_path_[0])) != nullptr);
  FLAGS_databus_socket_path = test_path_ + "/databus.sock";
  CHECK(server_socket_.Create());
  CHECK(server_socket_.Bind(FLAGS_databus_socket_path));
  CHECK(server_socket_.Listen(10));
  server_ = std::make_unique<cnetpp::concurrency::Thread>([&] () -> bool {
    Run();
    return true;
  }, "dbagt");
  server_->Start();
}

void MockDatabusServer::TearDown() {
  stopped_.store(true);

  for (auto& w : workers_) {
    w->Stop();
  }

  if (server_) {
    server_->Stop();
  }

  FileUtils::DeleteDirectoryRecursively(test_path_);
  FLAGS_databus_socket_path = backup_FLAGS_databus_socket_path_;
}

void MockDatabusServer::Run() {
  while (!stopped_.load()) {
    if (server_socket_.WaitReadable(static_cast<int64_t>(100), true)) {
      auto socket = std::make_shared<cnetpp::base::TcpSocket>();
      CHECK(server_socket_.Accept(socket.get()));
      auto worker = std::make_shared<cnetpp::concurrency::Thread>(
          [this, socket] () -> bool {
            WorkerRun(socket);
            return true;
          });
      worker->Start();
      workers_.emplace_back(worker);
    }
  }
}

void MockDatabusServer::WorkerRun(
    std::shared_ptr<cnetpp::base::TcpSocket> socket) {
  while (!stopped_.load()) {
    static const int kHeaderLength = 64;
    char header[kHeaderLength];
    size_t received_length;
    if (!socket->ReceiveAll(header,
                            kHeaderLength,
                            &received_length,
                            static_cast<int64_t>(100),
                            0,
                            false)) {
      continue;
    }
    CHECK_EQ(static_cast<int>(header[0]), 1);
    auto length = platform::ReadBigEndian<uint32_t>(header, 1);
    std::string payload;
    payload.resize(length);
    if (!socket->ReceiveAll(&(payload[0]),
                            length,
                            &received_length,
                            0,
                            false)) {
      break;
    }
    collector::RequestPayload rpayload;
    CHECK(rpayload.ParseFromString(payload));
    CHECK_EQ(rpayload.channel(), channel_);
    if (check_message_) {
      for (int i = 0; i < rpayload.messages_size(); ++i) {
        check_message_(rpayload.messages(i).value());
      }
    }
  }
}

}  // namespace dancenn
