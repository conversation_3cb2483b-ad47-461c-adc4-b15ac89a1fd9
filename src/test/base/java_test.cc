/*
#include <base/java.h>

#include <gtest/gtest.h>

#include <string>
#include <sstream>

namespace dancenn {

TEST(Java, Test00) {
  JavaRuntime rt(".", 64);
  JavaObject obj = rt.NewInstance("sun/misc/CRC16");
  jmethodID method = obj.env()->GetMethodID(obj.clazz(), "update", "(B)V");
  jfieldID field = obj.env()->GetFieldID(obj.clazz(), "value", "I");
  obj.env()->CallVoidMethod(obj.ref(), method, (jbyte)12);
  obj.env()->CallVoidMethod(obj.ref(), method, (jbyte)13);
  obj.env()->CallVoidMethod(obj.ref(), method, (jbyte)14);
  jint val = obj.env()->GetIntField(obj.ref(), field);
  ASSERT_EQ(52354, val);
}

}  // namespace dancenn
*/
