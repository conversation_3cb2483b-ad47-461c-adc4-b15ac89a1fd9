#include <base/ring_buffer.h>

#include <gtest/gtest.h>
#include "cnetpp/concurrency/thread.h"

namespace dancenn {

TEST(RingBuffer, Test01) {
  {
    dancenn::RingBuffer<int> rb(128);
    ASSERT_EQ(rb.Full(), false);
    ASSERT_EQ(rb.Empty(), true);
    ASSERT_EQ(rb.TryPush(10), true);
    ASSERT_EQ(rb.Full(), false);
    ASSERT_EQ(rb.Empty(), false);
    {
      int item = 0;
      ASSERT_EQ(rb.TryPop(&item), true);
      ASSERT_EQ(item, 10);
      ASSERT_EQ(rb.Full(), false);
      ASSERT_EQ(rb.Empty(), true);
    }
    {
      int item = -1;
      ASSERT_EQ(rb.TryPop(&item), false);
      ASSERT_EQ(item, 0);
    }
  }
  {
    dancenn::RingBuffer<std::string*> rb(128);
    ASSERT_EQ(rb.TryPush(new std::string("hello")), true);
    {
      std::string* item = nullptr;
      ASSERT_EQ(rb.TryPop(&item), true);
      ASSERT_EQ(*item, "hello");
      delete item;
    }
    {
      std::string* item = nullptr;
      ASSERT_EQ(rb.TryPop(&item), false);
      ASSERT_EQ(item, nullptr);
    }
  }
  {
    dancenn::RingBuffer<int> rb(128);
    for (int i = 0; i < 127; i++) {
      ASSERT_TRUE(rb.TryPush(i + 1));
    }
    ASSERT_EQ(rb.Full(), true);
    ASSERT_EQ(rb.TryPush(1000), false);
    for (int i = 0; i < 127; i++) {
      {
        int item = 0;
        ASSERT_EQ(rb.Peek(&item), true);
        ASSERT_EQ(item, i + 1);
      }
      {
        int item = 0;
        ASSERT_EQ(rb.TryPop(&item), true);
        ASSERT_EQ(item, i + 1);
      }
    }

    {
      int item = -1;
      ASSERT_EQ(rb.Peek(&item), false);
      ASSERT_EQ(item, 0);
    }
    {
      int item = -1;
      ASSERT_EQ(rb.TryPop(&item), false);
      ASSERT_EQ(item, 0);
    }
    ASSERT_EQ(rb.Empty(), true);
  }
}

TEST(RingBuffer, Test02) {
  dancenn::RingBuffer<int> rb(128);
  auto producer
    = std::make_unique<cnetpp::concurrency::Thread>([&rb](){
        for (int i = 0; i < 1000000; i++) {
          while(!rb.TryPush(i + 1));
        }
      return true;
      }, "producer");
  auto consumer
    = std::make_unique<cnetpp::concurrency::Thread>([&rb](){
        int item = 0;
        while (item != 1000000) {
          rb.TryPop(&item);
        }
      return true;
      }, "consumer");
  producer->Join();
  consumer->Join();
  ASSERT_EQ(rb.Empty(), true);
}

}  // namespace dancenn
