#include "base/timer_task_manager.h"

#include <gtest/gtest.h>

#include <atomic>
#include <chrono>
#include <thread>

namespace dancenn {
namespace {

class TimerTaskManagerTest : public ::testing::Test {
 protected:
  void SetUp() override {
    thread_pool_ = std::make_shared<cnetpp::concurrency::ThreadPool>(
        "TimerTaskManagerTest", true);
    thread_pool_->set_num_threads(1);
    thread_pool_->Start();
    timer_manager_ = std::make_unique<TimerTaskManager>(thread_pool_);
  }

  void TearDown() override {
    timer_manager_->Stop();
    thread_pool_->Stop();
  }

  std::shared_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
  std::unique_ptr<TimerTaskManager> timer_manager_;
};

// 测试基本的任务添加和执行
TEST_F(TimerTaskManagerTest, BasicTaskExecution) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  // 添加一个每秒执行一次的任务
  int64_t task_id = timer_manager_->AddPeriodicTask([&counter]() { counter++; },
                                                    1,  // 1秒间隔
                                                    "test_task");

  ASSERT_GT(task_id, 0);

  // 等待3.5秒，应该执行3次左右
  std::this_thread::sleep_for(std::chrono::milliseconds(3500));

  timer_manager_->CancelTask(task_id);
  int final_count = counter.load();
  EXPECT_GE(final_count, 2);
  EXPECT_LE(final_count, 4);  // 允许一定的时间误差
}

// 测试多个任务并发执行
TEST_F(TimerTaskManagerTest, MultipleTasks) {
  std::atomic<int> counter1{0};
  std::atomic<int> counter2{0};
  timer_manager_->Start();

  // 添加两个不同间隔的任务
  int64_t task1_id =
      timer_manager_->AddPeriodicTask([&counter1]() { counter1++; },
                                      1,  // 1秒间隔
                                      "task1");

  int64_t task2_id =
      timer_manager_->AddPeriodicTask([&counter2]() { counter2++; },
                                      2,  // 2秒间隔
                                      "task2");

  // 等待5.5秒
  std::this_thread::sleep_for(std::chrono::milliseconds(5500));

  timer_manager_->CancelTask(task1_id);
  timer_manager_->CancelTask(task2_id);

  // task1应该执行约5次，task2应该执行约2-3次
  EXPECT_GE(counter1.load(), 4);
  EXPECT_LE(counter1.load(), 6);

  EXPECT_GE(counter2.load(), 2);
  EXPECT_LE(counter2.load(), 3);
}

// 测试任务取消功能
TEST_F(TimerTaskManagerTest, TaskCancellation) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, 1, "cancel_test_task");

  // 等待1.5秒后取消任务
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  timer_manager_->CancelTask(task_id);

  int count_at_cancel = counter.load();
  // 再等待2秒，确保任务已经被取消
  std::this_thread::sleep_for(std::chrono::milliseconds(2000));

  // 确保计数器在取消后没有继续增加
  EXPECT_EQ(count_at_cancel, counter.load());
}

// 测试异常处理
TEST_F(TimerTaskManagerTest, ExceptionHandling) {
  std::atomic<bool> exception_caught{false};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&exception_caught]() { throw std::runtime_error("Test exception"); },
      1,
      "exception_task");

  // 等待任务执行
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));

  // 任务应该继续运行，不会因为异常而停止
  timer_manager_->CancelTask(task_id);
}

// 测试Start/Stop功能
TEST_F(TimerTaskManagerTest, StartStopBehavior) {
  std::atomic<int> counter{0};

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, 1, "start_stop_task");

  // 等待一段时间，确保没有开始前任务不会执行
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  EXPECT_EQ(counter.load(), 0);

  // 启动定时器
  timer_manager_->Start();

  // 等待任务执行
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  int count_before_stop = counter.load();
  EXPECT_GT(count_before_stop, 0);

  // 停止定时器
  timer_manager_->Stop();

  // 再等待一段时间，确保停止后任务不会继续执行
  std::this_thread::sleep_for(std::chrono::milliseconds(2000));
  EXPECT_EQ(counter.load(), count_before_stop);
}

// 测试零间隔任务
TEST_F(TimerTaskManagerTest, ZeroIntervalTask) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  // 添加间隔为0的任务（应该被视为10ms）
  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, 0, "zero_interval_task");

  std::this_thread::sleep_for(std::chrono::milliseconds(2500));
  timer_manager_->CancelTask(task_id);

  // 应该按1秒间隔执行
  EXPECT_GE(counter.load(), 200);
  EXPECT_LE(counter.load(), 300);
}

// 测试负间隔任务
TEST_F(TimerTaskManagerTest, NegativeIntervalTask) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  // 添加间隔为负数的任务（应该被视为100秒）
  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, -5, "negative_interval_task");

  std::this_thread::sleep_for(std::chrono::milliseconds(2500));
  timer_manager_->CancelTask(task_id);

  // 应该按1秒间隔执行
  EXPECT_GE(counter.load(), 200);
  EXPECT_LE(counter.load(), 300);
}

// 测试长时间运行的任务
TEST_F(TimerTaskManagerTest, LongRunningTask) {
  std::atomic<int> counter{0};
  std::atomic<int64_t> last_execution_time{0};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&]() {
        auto now = std::chrono::system_clock::now().time_since_epoch().count();
        auto last = last_execution_time.exchange(now);
        if (last > 0) {
          // 检查两次执行之间的间隔是否接近预期（1秒）
          auto diff = now - last;
          EXPECT_GE(diff, 900'000'000);  // 至少0.9秒
        }
        // 模拟耗时任务
        std::this_thread::sleep_for(std::chrono::milliseconds(800));
        counter++;
      },
      1,
      "long_running_task");

  // 等待足够长的时间以观察多次执行
  std::this_thread::sleep_for(std::chrono::milliseconds(3500));
  timer_manager_->CancelTask(task_id);

  // 验证执行次数
  EXPECT_GE(counter.load(), 2);
  EXPECT_LE(counter.load(), 4);
}

// 测试高频任务
TEST_F(TimerTaskManagerTest, HighFrequencyTask) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, 1, "high_freq_task");

  // 快速添加和取消任务
  for (int i = 0; i < 5; ++i) {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    timer_manager_->CancelTask(task_id);
    task_id = timer_manager_->AddPeriodicTask(
        [&counter]() { counter++; }, 1, "high_freq_task");
  }

  timer_manager_->CancelTask(task_id);
  EXPECT_GE(counter.load(), 0);  // 至少应该执行过几次
}

// 测试大量任务
TEST_F(TimerTaskManagerTest, MassiveTasks) {
  constexpr int NUM_TASKS = 100;
  std::vector<std::atomic<int>> counters(NUM_TASKS);
  std::vector<int64_t> task_ids;
  timer_manager_->Start();

  // 添加大量任务
  for (int i = 0; i < NUM_TASKS; ++i) {
    int task_index = i;
    task_ids.push_back(timer_manager_->AddPeriodicTask(
        [&counters, task_index]() { counters[task_index]++; },
        1,
        "massive_task_" + std::to_string(i)));
  }

  // 等待一段时间让任务执行
  std::this_thread::sleep_for(std::chrono::milliseconds(2500));

  // 检查所有任务是否都至少执行过一次
  int total_executions = 0;
  for (const auto& counter : counters) {
    total_executions += counter.load();
  }

  // 取消所有任务
  for (auto id : task_ids) {
    timer_manager_->CancelTask(id);
  }

  EXPECT_GT(total_executions, 0);
  LOG(INFO) << "Total executions across " << NUM_TASKS
            << " tasks: " << total_executions;
}

// 测试任务重入
TEST_F(TimerTaskManagerTest, TaskReentrancy) {
  std::atomic<int> counter{0};
  std::atomic<bool> is_running{false};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&]() {
        EXPECT_FALSE(is_running) << "Task should not be reentrant";
        is_running = true;
        std::this_thread::sleep_for(std::chrono::milliseconds(1500));
        counter++;
        is_running = false;
      },
      1,
      "reentrant_task");

  // 等待足够长的时间以观察多次执行
  std::this_thread::sleep_for(std::chrono::milliseconds(3500));
  timer_manager_->CancelTask(task_id);

  EXPECT_GE(counter.load(), 1);
}

// 测试任务名称重复
TEST_F(TimerTaskManagerTest, DuplicateTaskNames) {
  std::atomic<int> counter1{0}, counter2{0};
  timer_manager_->Start();

  // 添加两个同名任务
  int64_t task1_id = timer_manager_->AddPeriodicTask(
      [&counter1]() { counter1++; }, 1, "duplicate_task");
  int64_t task2_id = timer_manager_->AddPeriodicTask(
      [&counter2]() { counter2++; }, 1, "duplicate_task");

  EXPECT_NE(task1_id, task2_id);  // 确保任务ID不同

  std::this_thread::sleep_for(std::chrono::milliseconds(2500));

  timer_manager_->CancelTask(task1_id);
  timer_manager_->CancelTask(task2_id);

  EXPECT_GT(counter1.load(), 0);
  EXPECT_GT(counter2.load(), 0);
}

// 测试重启行为
TEST_F(TimerTaskManagerTest, RestartBehavior) {
  std::atomic<int> counter{0};
  timer_manager_->Start();

  int64_t task_id = timer_manager_->AddPeriodicTask(
      [&counter]() { counter++; }, 1, "restart_task");

  // 运行一段时间
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  int first_count = counter.load();

  // 停止后重启
  timer_manager_->Stop();
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  timer_manager_->Start();

  // 继续运行
  std::this_thread::sleep_for(std::chrono::milliseconds(1500));
  timer_manager_->CancelTask(task_id);

  int final_count = counter.load();
  EXPECT_GT(final_count, first_count);
}

// 测试空任务
TEST_F(TimerTaskManagerTest, EmptyTask) {
  timer_manager_->Start();

  // 添加一个空的lambda作为任务
  int64_t task_id = timer_manager_->AddPeriodicTask([]() {}, 1, "empty_task");

  std::this_thread::sleep_for(std::chrono::milliseconds(2500));
  timer_manager_->CancelTask(task_id);
  // 主要测试是否会崩溃
  SUCCEED();
}

}  // namespace
}  // namespace dancenn