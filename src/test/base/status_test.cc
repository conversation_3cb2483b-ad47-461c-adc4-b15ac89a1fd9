// Copyright 2018 <PERSON>yuan Lei <<EMAIL>>

#include "base/status.h"

#include <gtest/gtest.h>

namespace dancenn {

class StatusTest : public testing::Test {
 public:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(StatusTest, test) {
  Status status;
  ASSERT_EQ(status.code(), Code::kOK);
  ASSERT_EQ(status.exception(), JavaExceptions::kNoException);
  ASSERT_EQ(status.HasException(), false);
  ASSERT_EQ(status.IsOK(), true);
  ASSERT_EQ(status.ToString(), "NoException kOK. ");

  Status status2(JavaExceptions::kFileNotFoundException, Code::kBadParameter, "try again");
  ASSERT_EQ(status2.code(), Code::kBadParameter);
  ASSERT_EQ(status2.exception(), JavaExceptions::kFileNotFoundException);
  ASSERT_EQ(status2.HasException(), true);
  ASSERT_EQ(status2.IsOK(), false);
  ASSERT_EQ(status2.ToString(),"java.io.FileNotFoundException kBadParameter. try again");
}

}  // namespace dancenn
