//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: livexmm <<EMAIL>>
//

#include "base/policy_cache.h"

#include <gtest/gtest.h>

#include <algorithm>

#include "ClientNamenodeProtocol.pb.h"
#include "base/constants.h"

namespace dancenn {

const int kDefaultId = 0;
const std::string kDefaultDc = "";

struct MockPolicy {
  int id;
  std::string dc;

  MockPolicy() {
    id = kDefaultId;
    dc = kDefaultDc;
  }

  MockPolicy(int i, const std::string& d) {
    id = i;
    dc = d;
  }
};

bool operator==(const MockPolicy& a, const MockPolicy& b) {
  return a.id == b.id && a.dc == b.dc;
}

template <>
std::string PolicyCache<MockPolicy>::ToString(const MockPolicy& policy) {
  return "MockPolicy(" + std::to_string(policy.id) + ",\"" + policy.dc + "\")";
}

class PolicyCacheTest : public testing::Test {
 public:
  void SetUp() override {
  }

  void TearDown() override {
  }
};


TEST_F(PolicyCacheTest, Test01) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    ASSERT_EQ(paths.size(), 0);
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, kDefaultId);
    ASSERT_EQ(policy.dc, kDefaultDc);
  }
  {
    MockPolicy policy;
    cache->UpdatePath("/a/b/c", MockPolicy(1, "a"));
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "a");
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "a");

    {
      auto paths = cache->Filter([](const std::string& path,
            const MockPolicy& policy) { return true; });
      ASSERT_EQ(paths.size(), 1);
      ASSERT_EQ(paths[0], "/a/b/c");
    }
    {
      auto paths = cache->Filter([](const std::string& path,
            const MockPolicy& policy) { return false; });
      ASSERT_EQ(paths.size(), 0);
    }
  }
}

TEST_F(PolicyCacheTest, Test02) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/", MockPolicy(1, "11"));
    cache->UpdatePath("/a/b", MockPolicy(2, "22"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33"));
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44"));
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 4);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b");
    ASSERT_EQ(paths[2], "/a/b/c/d");
    ASSERT_EQ(paths[3], "/a/b/c/d/e/f/g");
  }

  // cache->UpdatePath("/", MockPolicy(1, "11")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/b", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }

  // cache->UpdatePath("/a/b", MockPolicy(2, "22")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/d", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }

  // cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }

  // cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g/h", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
}

TEST_F(PolicyCacheTest, Test03) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/", MockPolicy(1, "11"));
    cache->UpdatePath("/a/b", MockPolicy(2, "22"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33"));
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44"));
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 4);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b");
    ASSERT_EQ(paths[2], "/a/b/c/d");
    ASSERT_EQ(paths[3], "/a/b/c/d/e/f/g");
  }

  {
    cache->UpdatePath("/z", MockPolicy(10, "1010"));
    MockPolicy policy;
    cache->GetAncestorOrSelf("/z", &policy);
    ASSERT_EQ(policy.id, 10);
    ASSERT_EQ(policy.dc, "1010");
  }

  // cache->UpdatePath("/", MockPolicy(1, "11")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/b", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }

  // cache->UpdatePath("/a/b", MockPolicy(2, "22")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/d", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }

  // cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }

  // cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g/h", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
}

TEST_F(PolicyCacheTest, Test04) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/", MockPolicy(1, "11"));
    cache->UpdatePath("/a/b", MockPolicy(2, "22"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33"));
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44"));
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 4);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b");
    ASSERT_EQ(paths[2], "/a/b/c/d");
    ASSERT_EQ(paths[3], "/a/b/c/d/e/f/g");
  }

  {
    cache->UpdatePath("/z", MockPolicy(10, "1010"));
    MockPolicy policy;
    cache->GetAncestorOrSelf("/z", &policy);
    ASSERT_EQ(policy.id, 10);
    ASSERT_EQ(policy.dc, "1010");
  }

  {
    cache->UpdatePath("/a", MockPolicy(11, "1111"));
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "1111");
  }

  // cache->UpdatePath("/", MockPolicy(1, "11")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/b", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "1111");
  }

  // cache->UpdatePath("/a/b", MockPolicy(2, "22")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/d", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }

  // cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }

  // cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g/h", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
}

TEST_F(PolicyCacheTest, Test05) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/", MockPolicy(1, "11"));
    cache->UpdatePath("/a/b", MockPolicy(2, "22"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33"));
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44"));
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 4);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a/b");
    ASSERT_EQ(paths[2], "/a/b/c/d");
    ASSERT_EQ(paths[3], "/a/b/c/d/e/f/g");
  }
  {
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(44, "444"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(33, "333"));
    cache->UpdatePath("/a/b", MockPolicy(22, "222"));
    cache->UpdatePath("/", MockPolicy(11, "111"));
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "111");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b", &policy);
    ASSERT_EQ(policy.id, 22);
    ASSERT_EQ(policy.dc, "222");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 33);
    ASSERT_EQ(policy.dc, "333");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g", &policy);
    ASSERT_EQ(policy.id, 44);
    ASSERT_EQ(policy.dc, "444");
  }
}

TEST_F(PolicyCacheTest, Test06) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/", MockPolicy(1, "11"));
    cache->UpdatePath("/a/b", MockPolicy(2, "22"));
    cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33"));
    cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44"));
  }

  {
    cache->UpdatePath("/z", MockPolicy(10, "1010"));
    MockPolicy policy;
    cache->GetAncestorOrSelf("/z", &policy);
    ASSERT_EQ(policy.id, 10);
    ASSERT_EQ(policy.dc, "1010");
  }

  {
    cache->UpdatePath("/a", MockPolicy(11, "1111"));
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "1111");
  }

  {
    MockPolicy policy;
    cache->UpdatePath("/a/b/c/d/e/f", MockPolicy(9999, "9999"));
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 9999);
    ASSERT_EQ(policy.dc, "9999");
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "1111");
  }

  {
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 7);
    ASSERT_EQ(paths[0], "/");
    ASSERT_EQ(paths[1], "/a");
    ASSERT_EQ(paths[2], "/a/b");
    ASSERT_EQ(paths[3], "/a/b/c/d");
    ASSERT_EQ(paths[4], "/a/b/c/d/e/f");
    ASSERT_EQ(paths[5], "/a/b/c/d/e/f/g");
    ASSERT_EQ(paths[6], "/z");
  }

  // cache->UpdatePath("/", MockPolicy(1, "11")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/b", &policy);
    ASSERT_EQ(policy.id, 1);
    ASSERT_EQ(policy.dc, "11");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a", &policy);
    ASSERT_EQ(policy.id, 11);
    ASSERT_EQ(policy.dc, "1111");
  }

  // cache->UpdatePath("/a/b", MockPolicy(2, "22")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/d", &policy);
    ASSERT_EQ(policy.id, 2);
    ASSERT_EQ(policy.dc, "22");
  }

  // cache->UpdatePath("/a/b/c/d", MockPolicy(3, "33")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d", &policy);
    ASSERT_EQ(policy.id, 3);
    ASSERT_EQ(policy.dc, "33");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 9999);
    ASSERT_EQ(policy.dc, "9999");
  }

  // cache->UpdatePath("/a/b/c/d/e/f/g", MockPolicy(4, "44")));
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/g/h", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
}

TEST_F(PolicyCacheTest, Test07) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  {
    cache->UpdatePath("/a/b/c/d/e/f/i/g", MockPolicy(4, "44"));
    cache->UpdatePath("/a/b/c/d/e/f/l/g", MockPolicy(5, "55"));
  }
  {
    cache->UpdatePath("/a/b/c/d/e/f", MockPolicy(9999, "9999"));
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f", &policy);
    ASSERT_EQ(policy.id, 9999);
    ASSERT_EQ(policy.dc, "9999");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/i/g", &policy);
    ASSERT_EQ(policy.id, 4);
    ASSERT_EQ(policy.dc, "44");
  }
  {
    MockPolicy policy;
    cache->GetAncestorOrSelf("/a/b/c/d/e/f/l/g", &policy);
    ASSERT_EQ(policy.id, 5);
    ASSERT_EQ(policy.dc, "55");
  }
  {
    auto paths = cache->Filter([](const std::string& path,
          const MockPolicy& policy) { return true; });
    std::sort(paths.begin(), paths.end());
    ASSERT_EQ(paths.size(), 3);
    ASSERT_EQ(paths[0], "/a/b/c/d/e/f");
    ASSERT_EQ(paths[1], "/a/b/c/d/e/f/i/g");
    ASSERT_EQ(paths[2], "/a/b/c/d/e/f/l/g");
  }
}

TEST_F(PolicyCacheTest, Test08) {
  auto cache = std::make_shared<PolicyCache<MockPolicy>>(MockPolicy());
  cache->UpdatePath("/", MockPolicy(1, "11"));
  cache->UpdatePath("/a/b", MockPolicy(1, "22"));
  cache->UpdatePath("/a/b/c", MockPolicy(1, "33"));
  cache->UpdatePath("/a/b/c/d", MockPolicy(1, "44"));

  MockPolicy policy;
  cache->GetAncestorOrSelf("/", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "11");

  cache->GetAncestorOrSelf("/a/b", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "22");

  cache->GetAncestorOrSelf("/a/b/c", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "33");

  cache->GetAncestorOrSelf("/a/b/c/d", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "44");

  cache->DeletePath("/a/b/c/d");
  cache->GetAncestorOrSelf("/a/b/c/d", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "33");

  cache->DeletePath("/a/b/c");
  cache->DeletePath("/a/b/c/d");
  cache->DeletePath("/");

  cache->GetAncestorOrSelf("/", &policy);
  ASSERT_EQ(policy.id, kDefaultId);
  ASSERT_EQ(policy.dc, kDefaultDc);

  cache->UpdatePath("/", MockPolicy(1, "11"));
  cache->UpdatePath("/a", MockPolicy(1, "22"));
  cache->UpdatePath("/b", MockPolicy(1, "33"));
  cache->UpdatePath("/c", MockPolicy(1, "44"));

  cache->DeletePath("/");
  cache->GetAncestorOrSelf("/", &policy);
  ASSERT_EQ(policy.id, kDefaultId);
  ASSERT_EQ(policy.dc, kDefaultDc);
  cache->GetAncestorOrSelf("/a", &policy);
  ASSERT_EQ(policy.id, 1);
  ASSERT_EQ(policy.dc, "22");
}

TEST_F(PolicyCacheTest, Test09) {
  ::cloudfs::UfsUploadPolicyProto policy;
  ASSERT_EQ(policy.has_upload_interval_ms(), false);
  ASSERT_EQ(policy.upload_interval_ms(), 0);

  PolicyCache<::cloudfs::UfsUploadPolicyProto> policy_cache(policy);

  policy_cache.GetAncestorOrSelf("/", &policy);
  ASSERT_EQ(policy.has_upload_interval_ms(), false);
  ASSERT_EQ(policy.upload_interval_ms(), 0);

  policy_cache.GetAncestorOrSelf("/a/b", &policy);
  ASSERT_EQ(policy.has_upload_interval_ms(), false);
  ASSERT_EQ(policy.upload_interval_ms(), 0);

  policy_cache.GetAncestorOrSelf("/a/b/c", &policy);
  ASSERT_EQ(policy.has_upload_interval_ms(), false);
  ASSERT_EQ(policy.upload_interval_ms(), 0);
}

}  // namespace dancenn

