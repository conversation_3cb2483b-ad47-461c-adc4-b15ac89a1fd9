// Copyright 2019 livexmm <<EMAIL>>

#include "base/actor.h"
#include "base/count_down_latch.h"

#include <gtest/gtest.h>

namespace dancenn {

TEST(ActorTest, Test01) {
  int n = 10;
  CountDownLatch latch(n);
  Actor<int> a([&latch](int y){
      static int x = 0;
      ASSERT_EQ(x, y);
      x = x + 1;
      latch.CountDown();
  }, 10, "hello");
  a.Start();

  for (int i = 0; i < n; i++) {
    a.Tell(i);
  }

  latch.Await();
  a.Stop();
}

TEST(ActorTest, Test02) {
  int n = 11;
  CountDownLatch latch(n);
  Actor<int> a([&latch](int y){
      static int x = 0;
      if (x == 0) sleep(1);

      ASSERT_EQ(x, y);
      x = x + 1;
      latch.CountDown();
  }, 10, "hello");
  a.Start();

  {
    auto s = std::chrono::steady_clock::now();
    for (int i = 0; i < n - 1; i++) {
      a.<PERSON>(i);
    }
    auto e = std::chrono::steady_clock::now();
    ASSERT_LT(std::chrono::duration_cast<std::chrono::milliseconds>(e - s).count(), 100);
  }

  {
    auto s = std::chrono::steady_clock::now();
    a.Tell(n - 1);
    auto e = std::chrono::steady_clock::now();
    ASSERT_GT(std::chrono::duration_cast<std::chrono::milliseconds>(e - s).count(), 200);
  }

  latch.Await();
  a.Stop();
}

TEST(ActorTest, Test03) {
  int n = 10240;
  CountDownLatch latch(n);
  Actor<int> a([&latch](int y){
      (void) y;
      latch.CountDown();
  }, 100, "hello");
  a.Start();

  std::vector<cnetpp::concurrency::Thread*> th;
  for (int i = 0; i < 10; i++) {
    th.emplace_back(new cnetpp::concurrency::Thread([i, &a]() {
      for (int j = i; j < 10240; j += 10) {
        a.Tell(j);
      }
      return true;
    }));
    th[i]->Start();
  }
  for (int i = 0; i < 10; i++) {
    th[i]->Stop();
    delete th[i];
  }

  latch.Await();
  a.Stop();
}

TEST(ActorTest, Test04) {
  CountDownLatch latch(10000);

  int n[10];
  for (int i = 0; i < 10; i++) {
    n[i] = i;
  }

  ActorGroup<int, RoundRobinPicker<int>> group([&latch, &n](int y) {
    int index =
      cnetpp::concurrency::Thread::ThisThread()->ThreadPoolIndex();
    ASSERT_EQ(n[index], y);
    n[index] = n[index] + 10;
    latch.CountDown();
  }, 10, 128, "test");

  group.Start();
  for (int i = 0; i < 10000; i++) {
    group.Tell(i);
  }
  latch.Await();
  group.Stop();
}

}
