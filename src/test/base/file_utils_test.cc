// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <base/file_utils.h>

#include <cnetpp/concurrency/this_thread.h>
#include <gtest/gtest.h>

#include <string>

#include "base/defer.h"

namespace dancenn {

namespace {

class FileUtilsTest : public ::testing::Test {
 protected:
  void SetUp() override {
    ASSERT_NE(mkdtemp(&(test_path_[0])), nullptr);
  }

  void TearDown() override {
    FileUtils::DeleteDirectoryRecursively(test_path_);
  }

  std::string test_path_ = "test_XXXXXX";
};

static void WriteData(const std::string& file_name, int fd,
    cnetpp::base::StringPiece data) {
  CHECK_GT(fd, -1);

  const char* p = data.data();
  size_t s = 0;
  while (s < data.length()) {
    auto r = write(fd, p, data.length() - s);
    CHECK_GT(r, -1) << "Failed to write data to test file: " << file_name
      << ", error: " << cnetpp::concurrency::ThisThread::GetLastErrorString();
    p += r;
    s += r;
  }
}

}  // namespace

TEST(RandomAccessFileTest, test1) {
  RandomAccessFile raf("/tmp");
  ASSERT_LT(raf.fd(), 0);
  // ASSERT_EQ(raf.Size(), -1ll);
  std::unique_ptr<char[]> buffer = std::unique_ptr<char[]>(new char[10]);
  cnetpp::base::StringPiece sp;
  ASSERT_FALSE(raf.Read(0, buffer.get(), 10, &sp));
}

TEST(RandomAccessFileTest, test2) {
  std::string file_name = "test_XXXXXX";
  auto fd = mkstemp(&(file_name[0]));
  ASSERT_GT(fd, -1);

  DEFER(([file_name,fd] () { close(fd); FileUtils::DeleteFile(file_name); }));

  RandomAccessFile raf(file_name, fd);
  ASSERT_EQ(raf.fd(), fd);

  std::string test_data = "Where there is a will, there is a way.";
  WriteData(file_name, fd, cnetpp::base::StringPiece(test_data));

  ASSERT_EQ(raf.Size(), test_data.size());

  {
    std::unique_ptr<char[]> buffer = std::unique_ptr<char[]>(new char[10]);
    cnetpp::base::StringPiece sp;
    ASSERT_TRUE(raf.Read(0, buffer.get(), 10, &sp));
    ASSERT_EQ(sp.as_string(), test_data.substr(0, 10));
  }

  {
    std::unique_ptr<char[]> buffer = std::unique_ptr<char[]>(new char[10]);
    cnetpp::base::StringPiece sp;
    ASSERT_TRUE(raf.Read(test_data.size() - 10, buffer.get(), 10, &sp));
    ASSERT_EQ(sp.as_string(), test_data.substr(test_data.length() - 10, 10));
  }
}

TEST_F(FileUtilsTest, test1) {
  ASSERT_TRUE(FileUtils::Exists(test_path_));
  ASSERT_TRUE(FileUtils::IsDirectory(test_path_));
  ASSERT_FALSE(FileUtils::IsFile(test_path_));
  struct stat info;
  ASSERT_EQ(stat(test_path_.c_str(), &info), 0);
  ASSERT_TRUE(S_ISDIR(info.st_mode));

  std::string test_file1 = test_path_ + "/test1_XXXXXX";
  std::string test_file2 = test_path_ + "/test1_XXXXXX";
  {
    int fd1 = mkstemp(&(test_file1[0]));
    ASSERT_GE(fd1, 0);
    DEFER([&fd1]() {close(fd1);});
    int fd2 = mkstemp(&(test_file2[0]));
    ASSERT_GE(fd2, 0);
    DEFER([&fd2]() {close(fd2);});
  }
  ASSERT_TRUE(FileUtils::Exists(test_file1));
  ASSERT_TRUE(FileUtils::Exists(test_file2));
  ASSERT_FALSE(FileUtils::IsDirectory(test_file1));
  ASSERT_FALSE(FileUtils::IsDirectory(test_file2));
  ASSERT_TRUE(FileUtils::IsFile(test_file1));
  ASSERT_TRUE(FileUtils::IsFile(test_file2));
  ASSERT_EQ(stat(test_file1.c_str(), &info), 0);
  ASSERT_TRUE(S_ISREG(info.st_mode));
  ASSERT_EQ(stat(test_file2.c_str(), &info), 0);
  ASSERT_TRUE(S_ISREG(info.st_mode));

  std::set<std::string> children;
  ASSERT_TRUE(FileUtils::ListDirectory(test_path_, true,
      [&] (struct dirent* child) -> bool {
        children.emplace(child->d_name);
        return true;
      }));

  ASSERT_EQ(children.size(), 2);
  ASSERT_TRUE(
      children.find(test_file1.substr(test_path_.length() + 1)) !=
      children.end());
  ASSERT_TRUE(
      children.find(test_file2.substr(test_path_.length() + 1)) !=
      children.end());

  children.clear();
  ASSERT_TRUE(FileUtils::ListDirectory(test_path_, false,
      [&] (struct dirent* child) -> bool {
        children.emplace(child->d_name);
        return true;
      }));

  ASSERT_EQ(children.size(), 4);
  ASSERT_TRUE(
      children.find(test_file1.substr(test_path_.length() + 1)) !=
      children.end());
  ASSERT_TRUE(
      children.find(test_file2.substr(test_path_.length() + 1)) !=
      children.end());
  ASSERT_TRUE(children.find(".") != children.end());
  ASSERT_TRUE(children.find("..") != children.end());

  ASSERT_TRUE(FileUtils::DeleteFile(test_file1));
  ASSERT_FALSE(FileUtils::Exists(test_file1));
  ASSERT_TRUE(FileUtils::Exists(test_file2));

  children.clear();
  ASSERT_TRUE(FileUtils::ListDirectory(test_path_, true,
      [&] (struct dirent* child) -> bool {
        children.emplace(child->d_name);
        return true;
      }));
  ASSERT_EQ(children.size(), 1);
  ASSERT_TRUE(
      children.find(test_file2.substr(test_path_.length() + 1)) !=
      children.end());

  ASSERT_TRUE(FileUtils::DeleteDirectoryRecursively(test_path_));
  ASSERT_FALSE(FileUtils::Exists(test_file2));
  ASSERT_FALSE(FileUtils::Exists(test_path_));

  children.clear();
  ASSERT_FALSE(FileUtils::ListDirectory(test_path_, true,
      [&] (struct dirent* child) -> bool {
        children.emplace(child->d_name);
        return true;
      }));
  ASSERT_EQ(children.size(), 0);
}

TEST_F(FileUtilsTest, CreateDirectoryRecursively) {
  ASSERT_FALSE(FileUtils::CreateDirectoryRecursively("", S_IRWXU));
  ASSERT_FALSE(FileUtils::CreateDirectoryRecursively("/", S_IRWXU));
  ASSERT_FALSE(FileUtils::CreateDirectoryRecursively("///", S_IRWXU));
  ASSERT_FALSE(FileUtils::CreateDirectoryRecursively(test_path_ + "////", S_IRWXU));

  ASSERT_TRUE(FileUtils::CreateDirectoryRecursively(test_path_ + "/a", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists(test_path_));
  ASSERT_TRUE(FileUtils::CreateDirectoryRecursively(test_path_ + "/a/b/c", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists(test_path_ + "/a/b/c"));
  ASSERT_TRUE(FileUtils::CreateDirectoryRecursively(test_path_ + "/a/c/d/", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists(test_path_ + "/a/c/d/"));
  ASSERT_TRUE(FileUtils::CreateDirectoryRecursively(test_path_ + "/a/d///f/g", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists(test_path_ + "/a/d/f/g"));

  ASSERT_TRUE(FileUtils::CreateDirectoryRecursively("/tmp/" + test_path_, S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists("/tmp/" + test_path_));
  ASSERT_TRUE(
      FileUtils::CreateDirectoryRecursively("/tmp/" + test_path_ + "/a", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists("/tmp/" + test_path_ + "/a"));
  ASSERT_TRUE(
      FileUtils::CreateDirectoryRecursively("/tmp/" + test_path_ + "/a/a/b", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists("/tmp/" + test_path_ + "/a/a/b"));
  ASSERT_TRUE(
      FileUtils::CreateDirectoryRecursively("/tmp/" + test_path_ + "/a/b/c/", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists("/tmp/" + test_path_ + "/a/b/c/"));
  ASSERT_TRUE(
      FileUtils::CreateDirectoryRecursively("/tmp/" + test_path_ + "/a//c//d", S_IRWXU));
  ASSERT_TRUE(FileUtils::Exists("/tmp/" + test_path_ + "/a//c/d"));

  FileUtils::DeleteDirectoryRecursively("/tmp/" + test_path_);
  FileUtils::DeleteDirectoryRecursively(test_path_);
}

}  // namespace dancenn
