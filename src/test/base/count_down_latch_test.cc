// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/count_down_latch.h"

#include <gtest/gtest.h>

#include <thread>

namespace dancenn {

TEST(CountDownLatchTest, Test01) {
  CountDownLatch latch(10);
  ASSERT_FALSE(latch.Await(std::chrono::milliseconds(0)));
  std::vector<std::thread> threads;
  for (int i = 0; i < 9; ++i) {
    threads.emplace_back([&]() {
      latch.CountDown();
    });
  }
  auto s = std::chrono::steady_clock::now();
  ASSERT_FALSE(latch.Await(std::chrono::milliseconds(20)));
  auto e = std::chrono::steady_clock::now();
  ASSERT_GE(e - s, std::chrono::milliseconds(20));

  threads.emplace_back([&]() {
    latch.CountDown();
  });

  ASSERT_TRUE(latch.Await());
  ASSERT_TRUE(latch.Await(std::chrono::milliseconds(0)));

  for (auto &th : threads) {
    th.join();
  }
}

}  // namespace dancenn

