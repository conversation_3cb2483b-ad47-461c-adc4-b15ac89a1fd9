//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "ufs/tos_ufs/tos_ufs.h"

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <regex>
#include <sstream>
#include <unordered_set>

#include "base/string_utils.h"
#include "base/time_util.h"
#include "test/config/ut_config.h"
#include "ufs/tos/tos_constant.h"
#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/tos/tos_s3_def.h"

DECLARE_int32(ufs_worker_thread_num);
DECLARE_bool(use_fixed_ak);
DECLARE_int32(namespace_type);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);

static const std::string kUTTosBucket = "cloudfs-ut";
static const std::string kUTTosRegion = "cn-beijing";
static const std::string kUTTosEndpoint = "https://tos-s3-cn-beijing.volces.com";

namespace dancenn
{

class TosUfsTest : public testing::Test {
 public:
  void SetUp() override {
    ak_ = UTConfig::Instance().Ak();
    sk_ = UTConfig::Instance().Sk();

    // FLAGS_ufs_worker_thread_num = 256;
    FLAGS_use_fixed_ak = true;
    FLAGS_tos_access_key_id = ak_;
    FLAGS_tos_secret_access_key = sk_;

    config_.bucket = kUTTosBucket;
    config_.endpoint = kUTTosEndpoint;
    config_.prefix = "";
    config_.region = kUTTosRegion;

    ufs_config_ = TosConfig::CreateUfsConfig(config_);

    auto cred_keeper = std::make_shared<TosCredKeeper>();
    CHECK(cred_keeper->Start().IsOK());
    CHECK(cred_keeper->InnerCredential()->ak == ak_);
    CHECK(cred_keeper->InnerCredential()->sk == sk_);

    auto cred = std::shared_ptr<TosCredentialProvider>(
        new StaticTosCredentialProvider(cred_keeper));
    ufs_.reset(new TosUfs(ufs_config_, config_, cred));
    ufs_->Init();
  }

  void TearDown() override {
    ufs_.reset();
  }

 protected:
  std::shared_ptr<TosUfs> ufs_;
  UfsConfig ufs_config_;
  TosConfig config_;
  std::string ak_;
  std::string sk_;
};

TEST_F(TosUfsTest, TestUfsFileStatusBuilder) {
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("//").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(!s.IsOK());
  }
  {
    UfsFileStatus f;
    Status s = UfsFileStatus::Builder()
                   .SetFullPath("/a")
                   .SetFileType(UFS_FILE)
                   .SetFileSize(1)
                   .SetEtag("abcdefg")
                   .Build(&f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("a", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/a/").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("a", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/a//").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(!s.IsOK());
  }
}

TEST_F(TosUfsTest, TestGetStatusFile) {
  {
    UfsFileStatus file_status;
    std::string path ("/nn/TosUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.Etag(), "\"6de9439834c9147569741d3c9c9fc010\"");
    ASSERT_EQ(file_status.FileSize(), 4);
    ASSERT_EQ(file_status.FileType(), UFS_FILE);
    ASSERT_EQ(file_status.BlockSize(), TosConstants::kTosBlockSize);
  }
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetStatus/not_existed");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path("nn/TosUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
}

TEST_F(TosUfsTest, TestGetStatusDir) {
  {
    UfsFileStatus file_status;
    std::string path("/");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileName(), "");
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
  {
    UfsFileStatus file_status;
    std::string path("//");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(!s.IsOK());
  }
  {
    UfsFileStatus file_status;
    std::string path ("/nn/TosUfsTest/TestGetStatus/dir");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
  {
    UfsFileStatus file_status;
    std::string path ("/nn/TosUfsTest/TestGetStatus/dir/");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path.substr(0, path.size() - 1));
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
}
TEST_F(TosUfsTest, TestGetStatusDirAndObjectNameConflict) {
  {
    UfsFileStatus file_status;
    std::string path ("/nn/TosUfsTest/TestGetStatus/obj_name_conflict_with_dir");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.Etag(), "\"6de9439834c9147569741d3c9c9fc010\"");
    ASSERT_EQ(file_status.FileSize(), 4);
    ASSERT_EQ(file_status.FileType(), UFS_FILE);
    ASSERT_EQ(file_status.BlockSize(), TosConstants::kTosBlockSize);
  }
}
TEST_F(TosUfsTest, TestGetStatusDirObject) {
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetStatus/dir_object");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetStatus/dir_object/");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path.substr(0, path.size() - 1));
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
}
TEST_F(TosUfsTest, TestGetFileOnlyStatus) {
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetFileOnlyStatus/obj");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.Etag(), "\"f02e326f800ee26f04df7961adbf7c0a\"");
    ASSERT_EQ(file_status.FileSize(), 6);
    ASSERT_EQ(file_status.FileType(), UFS_FILE);
    ASSERT_EQ(file_status.BlockSize(), TosConstants::kTosBlockSize);
  }
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetFileOnlyStatus/not_existed");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path("/nn/TosUfsTest/TestGetFileOnlyStatus/dir/");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path("nn/TosUfsTest/TestGetFileOnlyStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
}
TEST_F(TosUfsTest, TestGetDirectoryStatus) {
  {
    UfsDirStatus dir;
    std::string path("/nn/TosUfsTest/TestGetStatus/nonexisted");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_EQ(Code::kDirNotFound, s.code());
  }
  {
    UfsDirStatus dir;
    std::string path("/nn/TosUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_EQ(Code::kDirNotFound, s.code());
  }
  {
    UfsDirStatus dir;
    std::string path("/nn/TosUfsTest/TestGetStatus/dir_object");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_TRUE(s.IsOK());
    ASSERT_FALSE(dir.HasChildren());
  }
  {
    UfsDirStatus dir;
    std::string path("/nn/TosUfsTest/TestGetStatus/dir");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_TRUE(s.IsOK());
    ASSERT_TRUE(dir.HasChildren());
  }
  {
    UfsDirStatus dir;
    std::string path("/nn/TosUfsTest/TestGetStatus/obj_name_conflict_with_dir");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_TRUE(s.IsOK());
    ASSERT_TRUE(dir.HasChildren());
  }
}

TEST_F(TosUfsTest, TestListFiles) {
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("/", ListFilesOption(), &res);
    ASSERT_EQ(s.code(), Code::kOK);
    ASSERT_TRUE(res.files.size() > 0);
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("nn/TosUfsTest/TestListFiles/dir/", ListFilesOption(), &res);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("/nn/TosUfsTest/TestListFiles/dir", ListFilesOption(), &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), 100);
    ASSERT_EQ(res.has_more, false);
    ASSERT_EQ(res.continue_token, "");
    ASSERT_EQ(res.files[0].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-1");
    ASSERT_EQ(res.files[1].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-10");
    ASSERT_EQ(res.files[2].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-100");
    ASSERT_EQ(res.files[3].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-11");
    ASSERT_EQ(res.files[99].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-99");
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("/nn/TosUfsTest/TestListFiles/dir/", ListFilesOption(), &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), 100);
    ASSERT_EQ(res.has_more, false);
    ASSERT_EQ(res.continue_token, "");
    ASSERT_EQ(res.files[0].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-1");
    ASSERT_EQ(res.files[1].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-10");
    ASSERT_EQ(res.files[2].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-100");
    ASSERT_EQ(res.files[3].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-11");
    ASSERT_EQ(res.files[99].FullPath(), "/nn/TosUfsTest/TestListFiles/dir/obj-99");
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("/nn/TosUfsTest/TestListFiles/dir_with_breadcum/", ListFilesOption(), &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), 10);
    ASSERT_EQ(res.has_more, false);
    ASSERT_EQ(res.continue_token, "");
    ASSERT_EQ(res.files[0].FullPath(), "/nn/TosUfsTest/TestListFiles/dir_with_breadcum/obj-1");
    ASSERT_EQ(res.files[1].FullPath(), "/nn/TosUfsTest/TestListFiles/dir_with_breadcum/obj-10");
    ASSERT_EQ(res.files[9].FullPath(), "/nn/TosUfsTest/TestListFiles/dir_with_breadcum/obj-9");
  }
}

TEST_F(TosUfsTest, TestListFilesPaging) {
  // Test paging
  // /nn/TosUfsTest/TestListFiles/dir/ has 100 children files with naming from obj-1 .. obj-100

  int page_size = 7;
  int total_count = 100;
  int page_count = (total_count / page_size) + 1;

  std::unordered_set<std::string> files;
  std::string continue_token;

  std::string last_file;
  for (int i = 0; i < page_count; ++i)
  {
    ListFilesResult res;
    ListFilesOption opt;
    opt.page_size = page_size;
    if (!continue_token.empty()) {
      opt.continue_token = continue_token;
    }
    size_t expected_count = (i == (page_count - 1)) ? (total_count - i * page_size) : page_size;

    auto s = ufs_->ListFiles("/nn/TosUfsTest/TestListFiles/dir/", opt, &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), expected_count);
    for (auto&& f : res.files) {
      auto&& path = f.FullPath();
      files.insert(path);
      ASSERT_TRUE(last_file.compare(path) < 0);
      ASSERT_EQ(f.FileType(), UFS_FILE);
      last_file = path;
    }
    ASSERT_EQ(res.has_more, i < (page_count - 1));
    continue_token = res.continue_token;
  }
  ASSERT_EQ(files.size(), (size_t)total_count);
  for (int i = 1; i <= 100; ++i) {
    std::string path = "/nn/TosUfsTest/TestListFiles/dir/obj-" + std::to_string(i);
    ASSERT_TRUE(files.find(path) != files.end());
  }
}

TEST_F(TosUfsTest, TestListFilesPagingWithMixedDisorderedFoldersAndFiles) {
  // Test paging
  // /nn/TosUfsTest/TestListFiles/dir_mixed_disordered_sub_file_dir/ has 100 children files and dirs with naming with pattern:
  //    00-file 02-file 04-file .. 98-file
  //    01-dir 03-dir 05-dir .. 99-dir

  {
    int page_size = 17;
    int total_count = 100;
    int page_count = (total_count / page_size) + 1;

    std::string dir_path ("/nn/TosUfsTest/TestListFiles/dir_mixed_disordered_sub_file_dir/");
    std::string continue_token;
    int count = 0;
    for (int i = 0; i < page_count; ++i)
    {
      ListFilesResult res;
      ListFilesOption opt;
      opt.page_size = page_size;
      if (!continue_token.empty()) {
        opt.continue_token = continue_token;
      }
      size_t expected_count = (i == (page_count - 1)) ? (total_count - i * page_size) : page_size;

      auto s = ufs_->ListFiles(dir_path, opt, &res);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(res.files.size(), expected_count);
      for (auto&& f : res.files) {
        auto&& path = f.FullPath();

        // Verify the list result order
        if (count % 2 == 0) {
          // Is's a file
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-file";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_FILE);
          ASSERT_EQ(path, expected_path);
        } else {
          // It's a dir
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-dir";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_DIR);
          ASSERT_EQ(path, expected_path);
        }

        ++count;
      }
      ASSERT_EQ(res.has_more, i < (page_count - 1));
      continue_token = res.continue_token;
    }
    ASSERT_EQ(count ,100);
  }
  {
    int page_size = 1;
    int total_count = 100;
    int page_count = 100;

    std::string dir_path ("/nn/TosUfsTest/TestListFiles/dir_mixed_disordered_sub_file_dir/");
    std::string continue_token;
    int count = 0;
    for (int i = 0; i < page_count; ++i)
    {
      ListFilesResult res;
      ListFilesOption opt;
      opt.page_size = page_size;
      if (!continue_token.empty()) {
        opt.continue_token = continue_token;
      }
      size_t expected_count = (i == (page_count - 1)) ? (total_count - i * page_size) : page_size;

      auto s = ufs_->ListFiles(dir_path, opt, &res);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(res.files.size(), expected_count);
      for (auto&& f : res.files) {
        auto&& path = f.FullPath();

        // Verify the list result order
        if (count % 2 == 0) {
          // Is's a file
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-file";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_FILE);
          ASSERT_EQ(path, expected_path);
        } else {
          // It's a dir
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-dir";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_DIR);
          ASSERT_EQ(path, expected_path);
        }

        ++count;
      }
      ASSERT_EQ(res.has_more, i < (page_count - 1));
      continue_token = res.continue_token;
    }
    ASSERT_EQ(count ,100);
  }
  {
    int page_size = 1000;
    int total_count = 100;
    int page_count = 1;

    std::string dir_path ("/nn/TosUfsTest/TestListFiles/dir_mixed_disordered_sub_file_dir/");
    std::string continue_token;
    int count = 0;
    for (int i = 0; i < page_count; ++i)
    {
      ListFilesResult res;
      ListFilesOption opt;
      opt.page_size = page_size;
      if (!continue_token.empty()) {
        opt.continue_token = continue_token;
      }
      size_t expected_count = (i == (page_count - 1)) ? (total_count - i * page_size) : page_size;

      auto s = ufs_->ListFiles(dir_path, opt, &res);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(res.files.size(), expected_count);
      for (auto&& f : res.files) {
        auto&& path = f.FullPath();

        // Verify the list result order
        if (count % 2 == 0) {
          // Is's a file
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-file";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_FILE);
          ASSERT_EQ(path, expected_path);
        } else {
          // It's a dir
          std::string expected_path;
          {
            std::stringstream ss;
            ss << dir_path << std::setw(2) << std::setfill('0') << count << "-dir";
            expected_path = ss.str();
          }
          ASSERT_EQ(f.FileType(), UFS_DIR);
          ASSERT_EQ(path, expected_path);
        }

        ++count;
      }
      ASSERT_EQ(res.has_more, i < (page_count - 1));
      continue_token = res.continue_token;
    }
    ASSERT_EQ(count ,100);
  }
}

TEST_F(TosUfsTest, TestS3ListObjectsV2API) {

  Aws::Auth::AWSCredentials credentials(ak_, sk_);
  Aws::Client::ClientConfiguration clientCfg;
  clientCfg.endpointOverride = kUTTosEndpoint;
  clientCfg.region = kUTTosRegion;
  Aws::S3::S3Client client_(credentials, clientCfg);


  std::string continue_token;
  while (true) {
    Aws::S3::Model::ListObjectsV2Request req;
    req.SetBucket(kUTTosBucket);
    req.SetMaxKeys(1);
    req.SetPrefix("foo/");
    req.SetDelimiter(kSeparator);
    if (!continue_token.empty()) {
      req.SetContinuationToken(continue_token);
    }

    auto&& out = client_.ListObjectsV2(req);
    ASSERT_TRUE(out.IsSuccess());
    auto&& res = out.GetResult();
    for (auto&& c : res.GetContents()) {
      std::cout << "object: " << c.GetKey() << std::endl;
    }
    for (auto&& p : res.GetCommonPrefixes()) {
      std::cout << "prefix: " << p.GetPrefix() << std::endl;
    }
    std::cout << "Next: " << res.GetNextContinuationToken() << std::endl;
    continue_token = res.GetNextContinuationToken();

    if (!res.GetIsTruncated()) {
      break;
    }
  }
}

TEST_F(TosUfsTest, TestCopyDeleteFile) {
  std::string files[]{"data.1m",
                      "data.10m",
                      "data.100m",
                      "data.800m",
                      "data.1g",
                      "data.1.1g",
                      "data.2g",
                      "data.5g",
                      "data.10g"};
  std::string dir = "/nn/TosUfsTest/TestCopyFile/";
  uint64_t ts = TimeUtil::GetNowEpochMs();
  std::string target_dir = absl::StrFormat("/tmp%s%lu/", dir, ts);

  std::unordered_map<std::string, uint64_t> copy_time;
  for (auto&& f : files) {
    LOG(INFO) << "Start to copy " << f;
    uint64_t start = TimeUtil::GetNowEpochMs();
    std::string src = dir + f;
    std::string dst = target_dir + f;
    UfsCopyResult result;
    auto s = ufs_->CopyFile(dir + f, dst, false, &result);
    ASSERT_TRUE(s.IsOK());
    uint64_t ellapsed = TimeUtil::GetNowEpochMs() - start;
    LOG(INFO) << "Finish to copy " << f;

    UfsFileStatus src_status;
    s = ufs_->GetFileStatus(src, &src_status);
    ASSERT_TRUE(s.IsOK());

    UfsFileStatus dst_status;
    s = ufs_->GetFileStatus(dst, &dst_status);
    ASSERT_TRUE(s.IsOK());

    ASSERT_EQ(src_status.FileSize(), dst_status.FileSize());
    ASSERT_EQ(f, src_status.FileName());
    ASSERT_EQ(f, dst_status.FileName());
    ASSERT_EQ(UFS_FILE, src_status.FileType());
    ASSERT_EQ(UFS_FILE, dst_status.FileType());

    copy_time[f] = ellapsed;

    s = ufs_->DeleteFile(dst);
    ASSERT_TRUE(s.IsOK());
  }

  fprintf(stdout,
          ">>>>>>>>>>>>>>>>>>>> % 20s % 20s <<<<<<<<<<<<<<<<<<<<\n",
          "File",
          "Copy Time(ms)");
  for (auto&& f : files) {
    fprintf(stdout,
            ">>>>>>>>>>>>>>>>>>>> % 20s % 20d <<<<<<<<<<<<<<<<<<<<\n",
            f.c_str(),
            copy_time[f]);
  }
}

TEST_F(TosUfsTest, TestCopyDeleteDir2KMidFiles) {
  // Verify etag
  std::string src_dir = "/nn/TosUfsTest/TestCopyDir/2kmidfiles/";
  uint64_t ts = TimeUtil::GetNowEpochMs();
  std::string target_dir =
      absl::StrFormat("/tmp%s%lu/10kmidfiles/", src_dir, ts);
  UfsCopyResult result;
  auto s = ufs_->CopyDirectory(src_dir, target_dir, &result);
  ASSERT_TRUE(s.IsOK());

  // List and verify by etag
  int count = 0;
  ListFilesOption opt;
  opt.recursive = true;
  while (true) {
    ListFilesResult res;
    s = ufs_->ListFiles(src_dir, opt, &res);
    ASSERT_TRUE(s.IsOK());

    for (auto&& f : res.files) {
      std::string target_path =
          target_dir + f.FullPath().substr(src_dir.size());
      UfsFileStatus target_file;
      s = ufs_->GetFileStatus(target_path, &target_file);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(f.FileSize(), target_file.FileSize());
      ASSERT_EQ(f.Etag(), target_file.Etag());

      ++count;
    }

    if (res.has_more) {
      opt.continue_token = res.continue_token;
    } else {
      break;
    }
  }
  ASSERT_EQ(2000, count);

  s = ufs_->DeleteDirectory(target_dir);
  ASSERT_TRUE(s.IsOK());
}

TEST_F(TosUfsTest, TestCopyDeleteDirLargeFiles) {
  // Verify length, first 64K and last 64K bytes
  std::string src_dir = "/nn/TosUfsTest/TestCopyDir/50glargefiles/";
  uint64_t ts = TimeUtil::GetNowEpochMs();
  std::string target_dir =
      absl::StrFormat("/tmp%s%lu/50glargefiles/", src_dir, ts);
  UfsCopyResult result;
  auto s = ufs_->CopyDirectory(src_dir, target_dir, &result);
  ASSERT_TRUE(s.IsOK());

  // List and verify by first 64k and last 64k bytes
  const uint64_t kVerifyLength = 64 * 1024;
  int count = 0;
  ListFilesOption opt;
  opt.recursive = true;
  while (true) {
    ListFilesResult res;
    s = ufs_->ListFiles(src_dir, opt, &res);
    ASSERT_TRUE(s.IsOK());

    for (auto&& f : res.files) {
      std::string target_path =
          target_dir + f.FullPath().substr(src_dir.size());
      UfsFileStatus target_file;
      s = ufs_->GetFileStatus(target_path, &target_file);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(f.FileSize(), target_file.FileSize());

      {
        std::string src_first_64k;
        s = ufs_->ReadFile(f.FullPath(), 0, kVerifyLength, &src_first_64k);
        ASSERT_TRUE(s.IsOK());

        std::string dst_first_64k;
        s = ufs_->ReadFile(f.FullPath(), 0, kVerifyLength, &dst_first_64k);
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(src_first_64k, dst_first_64k);
      }
      {
        std::string src_last_64k;
        s = ufs_->ReadFile(f.FullPath(),
                           f.FileSize() - kVerifyLength,
                           kVerifyLength,
                           &src_last_64k);
        ASSERT_TRUE(s.IsOK());

        std::string dst_last_64k;
        s = ufs_->ReadFile(f.FullPath(),
                           f.FileSize() - kVerifyLength,
                           kVerifyLength,
                           &dst_last_64k);
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(src_last_64k, dst_last_64k);
      }

      ++count;
    }

    if (res.has_more) {
      opt.continue_token = res.continue_token;
    } else {
      break;
    }
  }
  ASSERT_EQ(50, count);

  s = ufs_->DeleteDirectory(target_dir);
  ASSERT_TRUE(s.IsOK());
}

TEST_F(TosUfsTest, TestMkdirs) {
  // Create a non-existed dir
  {
    std::string src = "/tmp/mkdirs";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_TRUE(ufs_->GetDirectoryStatus(src, &info).IsOK());
    ASSERT_EQ(info.FullPath().compare(src), 0);
    ASSERT_FALSE(info.HasChildren());
  }
  // Recreate, overwrite
  {
    std::string src = "/tmp/mkdirs";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_TRUE(ufs_->GetDirectoryStatus(src, &info).IsOK());
    ASSERT_EQ(info.FullPath().compare(src), 0);
    ASSERT_FALSE(info.HasChildren());
  }
  // Inner Create failed undo
  {
    std::string src = "/tmp/mkdirs-fail";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    ASSERT_TRUE(ufs_->AbortMkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_FALSE(ufs_->GetDirectoryStatus(src, &info).IsOK());
  }
}

TEST_F(TosUfsTest, TestCreateObject) {
  static const std::string placeHolderTagging =
      "Status=Creating&CreateType=Upload&Creator=CloudFS";
  static const std::string emptyObjectTagging =
      "Status=Created&CreateType=Put&Creator=CloudFS";
  static const std::string tagging =
      "Status=Created&CreateType=Upload&Creator=CloudFS";

  // Create a non-existed empty object without placeholder
  {
    std::string src = "/tmp/create/non-exists-without-placeholder";
    std::string key = "tmp/create/non-exists-without-placeholder";
    UfsFileInfoProto info;
    UfsCreateOption opt(true, false);
    ASSERT_TRUE(ufs_->Create(src, opt, &info).IsOK());

    UfsFileStatus info2;
    ASSERT_TRUE(ufs_->GetFileStatus(src, &info2).IsOK());
    ASSERT_EQ(src, info2.FullPath());
    ASSERT_EQ(info.size(), info2.FileSize());
    ASSERT_EQ(info.etag(), info2.Etag());
    ASSERT_EQ(info.tagging(), emptyObjectTagging);
  }

  // Create an existed empty object without placeholder
  {
    std::string src = "/tmp/create/exists-without-placeholder";
    std::string key = "tmp/create/exists-without-placeholder";
    UfsFileInfoProto info;
    UfsCreateOption opt(true, false);
    ASSERT_TRUE(ufs_->Create(src, opt, &info).IsOK());

    UfsFileStatus info2;
    ASSERT_TRUE(ufs_->GetFileStatus(src, &info2).IsOK());
    ASSERT_EQ(src, info2.FullPath());
    ASSERT_EQ(info.size(), info2.FileSize());
    ASSERT_EQ(info.etag(), info2.Etag());
    ASSERT_EQ(info.tagging(), emptyObjectTagging);
  }

  // Complete will be tested in copy
}

TEST_F(TosUfsTest, TestCheckParentReady) {
  {
    std::string src = "/tmp/create/test/is/parent/existing";
    std::string key = "tmp/create/test/is/parent/existing";
    UfsFileInfoProto info;
    UfsCreateOption opt(true, false);
    ASSERT_TRUE(ufs_->Create(src, opt, &info).IsOK());

    UfsFileStatus info2;
    ASSERT_TRUE(ufs_->GetFileStatus(src, &info2).IsOK());
    ASSERT_EQ(src, info2.FullPath());
    ASSERT_EQ(info.size(), info2.FileSize());
    ASSERT_EQ(info.etag(), info2.Etag());
    ASSERT_FALSE(info2.IsDir());
  }

  {
    std::string src;
    src = "/tmp/create/test/is/parent/existing/a";
    ASSERT_FALSE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent/existing";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent/nonexisting";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent/existing/obj";
    ASSERT_FALSE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/abc/def";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
  }

  {
    std::string src;
    src = "/tmp/create/test/is/parent/existing2";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    src = "/tmp/create/test/is/parent/existing2/a";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent/existing2";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/tmp/create/test/is/parent";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/aaa";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
    src = "/";
    ASSERT_TRUE(ufs_->CheckParentReady(src).IsOK());
  }
}

TEST_F(TosUfsTest, TestListFilesDupObjDir) {
  // Test paging
  std::string path("/listing_test/dup_obj_dir");

  ListFilesResult res;
  ListFilesOption opt;
  opt.page_size = 1000;

  auto s = ufs_->ListFiles(path, opt, &res);
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(res.files.size(), 3);
  ASSERT_EQ(res.files[0].FileName(), "aaa");
  ASSERT_EQ(res.files[0].FileType(), UfsFileType::UFS_FILE);
  ASSERT_EQ(res.files[1].FileName(), "dir");
  ASSERT_EQ(res.files[1].FileType(), UfsFileType::UFS_FILE);
  ASSERT_EQ(res.files[2].FileName(), "zzz");
  ASSERT_EQ(res.files[2].FileType(), UfsFileType::UFS_FILE);
}

TEST_F(TosUfsTest, TestIsBreadcrumbObject) {
  // List Root
  ASSERT_EQ(ufs_->IsBreadcrumbObject("", ""), false);
  ASSERT_EQ(ufs_->IsBreadcrumbObject("", "/"), false);
  // List a dir
  ASSERT_EQ(ufs_->IsBreadcrumbObject("dir/", ""), false);
  ASSERT_EQ(ufs_->IsBreadcrumbObject("dir/", "dir"), false);
  ASSERT_EQ(ufs_->IsBreadcrumbObject("dir/", "dir/"), true);
  ASSERT_EQ(ufs_->IsBreadcrumbObject("dir/", "dirx/"), false);
}

// Corner case for object key ends with two slashs: path/to/dir//
TEST_F(TosUfsTest, TestListFilesInDirEndWithTwoSlashes) {
  std::string dir = "/nn/TosUfsTest/TestListFilesInDirEndWithTwoSlashes/";
  ListFilesOption opt;
  ListFilesResult res;
  auto s = ufs_->ListFiles(dir, opt, &res);
  ASSERT_TRUE(s.IsOK());
  for (auto&& f : res.files) {
    std::cout << f.FullPath() << " " << f.FileName() << std::endl;
  }
}

TEST_F(TosUfsTest, TestHash) {
  LOG(INFO) << std::hash<StringPiece>()(StringPiece(""));
  LOG(INFO) << std::hash<StringPiece>()(StringPiece("a", 0));
  LOG(INFO) << std::hash<StringPiece>()(StringPiece("."));
  LOG(INFO) << std::hash<StringPiece>()(StringPiece(".."));
}

} // namespace dancenn
