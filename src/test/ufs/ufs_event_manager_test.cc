//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// Third
#include <gtest/gtest.h>

// Project
#include "test/namespace/mock_namespace.h"
#include "test/ufs/mock_ufs.h"
#include "ufs/ufs_event_manager.h"

DECLARE_bool(ufs_event_manager_enabled);
DECLARE_bool(ufs_event_kafka_consumer_enabled);
DECLARE_string(ufs_event_consumer_kafka_endpoint);
DECLARE_string(ufs_event_consumer_kafka_topic);
DECLARE_string(ufs_event_consumer_kafka_group_id);
DECLARE_bool(ufs_event_rmq_consumer_enabled);
DECLARE_string(ufs_event_rmq_consumer_endpoint);
DECLARE_string(ufs_event_rmq_consumer_topic);
DECLARE_string(ufs_event_rmq_consumer_group_id);
DECLARE_string(ufs_event_rmq_consumer_access_key);
DECLARE_string(ufs_event_rmq_consumer_secret_key);

namespace dancenn {

class UfsEventManagerTest : public testing::Test {
 public:
  void SetUp() override {
    ufs_ = std::make_shared<GMockUfs>();
    ns_ = std::make_shared<GMockNameSpace>();
  }

  std::shared_ptr<GMockUfs> ufs_;
  std::shared_ptr<GMockNameSpace> ns_;
};

TEST_F(UfsEventManagerTest, EnableKafkaConsumer) {
  FLAGS_ufs_event_manager_enabled = true;
  FLAGS_ufs_event_kafka_consumer_enabled = true;
  FLAGS_ufs_event_consumer_kafka_endpoint = "dummy_endpoint";
  FLAGS_ufs_event_consumer_kafka_topic = "dummy_topic";
  FLAGS_ufs_event_consumer_kafka_group_id = "dummy_group_id";
  FLAGS_ufs_event_rmq_consumer_enabled = false;

  std::shared_ptr<UfsEventManager> mgr =
      std::make_shared<UfsEventManager>(ufs_);
  mgr->SetNS(ns_);
  mgr->Start();
  mgr->SetHaState(true);
  EXPECT_TRUE(mgr->IsKafkaConsumerRunning());

  UfsEventManagerStatus status = mgr->GetStatus();
  EXPECT_TRUE(status.enabled);
  EXPECT_TRUE(status.kafka_consumer_running);

  mgr->StopConsumers();
  EXPECT_FALSE(mgr->IsKafkaConsumerRunning());

  mgr->StartConsumers();
  EXPECT_TRUE(mgr->IsKafkaConsumerRunning());

  mgr->SetHaState(false);
  EXPECT_FALSE(mgr->IsKafkaConsumerRunning());

  mgr->Stop();
}

TEST_F(UfsEventManagerTest, EnableRmqConsumer) {
  FLAGS_ufs_event_manager_enabled = true;
  FLAGS_ufs_event_kafka_consumer_enabled = false;
  FLAGS_ufs_event_rmq_consumer_enabled = true;
  FLAGS_ufs_event_rmq_consumer_endpoint = "dummy_endpoint";
  FLAGS_ufs_event_rmq_consumer_topic = "dummy_topic";
  FLAGS_ufs_event_rmq_consumer_group_id = "dummy_group_id";
  FLAGS_ufs_event_rmq_consumer_access_key = "dummy_ak";
  FLAGS_ufs_event_rmq_consumer_secret_key = "dummy_sk";

  std::shared_ptr<UfsEventManager> mgr =
      std::make_shared<UfsEventManager>(ufs_);
  mgr->SetNS(ns_);
  mgr->Start();
  mgr->SetHaState(true);
  EXPECT_TRUE(mgr->IsRmqConsumerRunning());

  UfsEventManagerStatus status = mgr->GetStatus();
  EXPECT_TRUE(status.enabled);
  EXPECT_TRUE(status.rmq_consumer_running);

  mgr->StopConsumers();
  EXPECT_FALSE(mgr->IsRmqConsumerRunning());

  mgr->StartConsumers();
  EXPECT_TRUE(mgr->IsRmqConsumerRunning());

  mgr->SetHaState(false);
  EXPECT_FALSE(mgr->IsRmqConsumerRunning());

  mgr->Stop();
}

TEST_F(UfsEventManagerTest, ClearMetrics) {
  FLAGS_ufs_event_manager_enabled = true;
  FLAGS_ufs_event_kafka_consumer_enabled = false;

  std::shared_ptr<UfsEventManager> mgr =
      std::make_shared<UfsEventManager>(ufs_);
  mgr->SetNS(ns_);
  mgr->Start();
  mgr->metrics().consume_error->Inc();
  EXPECT_GT(mgr->metrics().consume_error->MakeSnapshot(), 0);
  mgr->metrics().handle_error->Inc();
  EXPECT_GT(mgr->metrics().handle_error->MakeSnapshot(), 0);
  mgr->ClearError();
  EXPECT_EQ(0, mgr->metrics().consume_error->MakeSnapshot());
  EXPECT_EQ(0, mgr->metrics().handle_error->MakeSnapshot());
  mgr->Stop();
}

TEST_F(UfsEventManagerTest, TestMethodsWhenNotEnabled) {
  FLAGS_ufs_event_manager_enabled = false;

  std::shared_ptr<UfsEventManager> mgr =
      std::make_shared<UfsEventManager>(ufs_);
  mgr->SetNS(ns_);
  mgr->Start();
  mgr->SetHaState(true);
  mgr->StartConsumers();
  mgr->StopConsumers();
  mgr->IsKafkaConsumerRunning();
  mgr->IsRmqConsumerRunning();
  mgr->GetRmqConsumerMtx();
  mgr->GetRmqConsumerCv();
  mgr->AddSyncTask("", [](Status) { return; });
  mgr->GetTosEventKeyPrefixBlacklist();
  mgr->GetStatus();
  mgr->metrics();
  mgr->ClearError();
  mgr->SetHaState(false);
  mgr->Stop();
}

}  // namespace dancenn
