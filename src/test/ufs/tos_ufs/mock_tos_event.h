//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// The Header
#include "ufs/tos_ufs/tos_event.h"

static const std::string kUTTosEventBucket = "poc-tos-event";
static const std::string kUTTosEventRegion = "cn-beijing";
static const std::string kUTTosEventEndpoint =
    "https://tos-s3-cn-beijing.volces.com";
static const std::string kUTTosEventPrefix = "my-prefix/";

namespace dancenn {

inline std::shared_ptr<TosEvent> TosEventForUT() {
  return std::make_shared<TosEvent>(
      "tos:ObjectCreated:Put",
      "tos",
      "2023-02-15T12:49:01Z",
      "1.0",
      std::make_shared<Tos>(
          std::make_shared<TosBucket>("trn:tos:::" + kUTTosEventBucket,
                                      kUTTosEventBucket,
                                      "2100050900"),
          std::make_shared<TosObject>("\"f2bdbb6d2be86169deed3c6b559c3b3b\"",
                                      kUTTosEventPrefix + "my-key",
                                      187040),
          "1.0",
          kUTTosEventBucket,
          kUTTosEventRegion,
          std::make_shared<RequestParameters>("211.95.47.98:62640"),
          std::make_shared<ResponseElements>(
              "514101ecd4bc56b163ecd4bc-ac15cf25-1pSHDY-PuO-cb-tos-bj-3"),
          std::make_shared<UserIdentity>(
              "trn:iam::2100050900:user/cloudfstest")));
}

}  // namespace dancenn
