//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#pragma once

#include <gmock/gmock.h>

#include "ufs/tos/tos_client.h"

namespace dancenn {

class GMockTosClient : public TosClient {
 public:
  GMockTosClient(const TosConfig& tos_config,
                 const std::shared_ptr<TosCredentialProvider>& tos_cred)
      : TosClient(tos_config, tos_cred) {
  }

  MOCK_METHOD5(ListObjects,
               Status(const std::string&,
                      const std::string&,
                      int32_t,
                      TosListObjectsResult*,
                      const std::string&));
  MOCK_METHOD2(HeadObject, Status(const std::string&, TosHeadObjectResult*));
  MOCK_METHOD3(CreateEmptyObject,
               Status(const std::string&, const std::string&, std::string*));
  MOCK_METHOD3(CreateMpuObject,
               Status(const std::string&, const std::string&, std::string*));
  MOCK_METHOD7(UploadMpuPartCopy,
               Status(const std::string&,
                      const std::string&,
                      uint64_t,
                      uint64_t,
                      const std::string&,
                      int,
                      TosUploadPartCopyResult*));
  MOCK_METHOD3(CopyObjectWithInBucket,
               Status(const std::string&, const std::string&, std::string*));

  MOCK_METHOD1(DeleteObject, Status(const std::string&));
  MOCK_METHOD1(DeleteObjects, Status(const std::vector<std::string>&));
  MOCK_METHOD1(DeleteObjects,
               Status(const std::vector < Aws::S3::Model::Object>&));

  MOCK_METHOD2(AbortCreateMultipartUpload,
               Status(const std::string&, const std::string&));
  MOCK_METHOD2(CheckMpuStatus, Status(const std::string&, const std::string&));
  MOCK_METHOD4(CompleteObject,
               Status(const std::string&,
                      const std::string&,
                      const std::map<int, std::string>&,
                      std::string*));
  MOCK_METHOD4(ReadObject,
               Status(const std::string&, uint64_t, uint64_t, std::string*));

  MOCK_METHOD1(OnCredUpdated, void(std::shared_ptr<VolcCredential>));
};

}  // namespace dancenn
