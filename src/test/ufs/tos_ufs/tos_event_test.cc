//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// System
#include <string>

// Third
#include <gtest/gtest.h>

#include <nlohmann/json.hpp>

// Project
#include "ufs/tos_ufs/tos_event.h"

using json = nlohmann::json;

namespace dancenn {

class TosEventTest : public testing::Test {
 public:
  void SetUp() override {
  }

  static TosEvent::EventType EventTypeFromName(const std::string& name);
};

TosEvent::EventType TosEventTest::EventTypeFromName(const std::string& name) {
  return TosEvent::EventTypeFromName(name);
}

TEST_F(TosEventTest, ParseFromMessage) {
  const char* msg = R"({
  "events": [
    {
      "eventName": "tos:ObjectCreated:Put",
      "eventSource": "tos",
      "eventTime": "2023-02-15T12:49:01Z",
      "eventVersion": "1.0",
      "tos": {
        "bucket": {
          "trn": "trn:tos:::poc-tos-event",
          "name": "poc-tos-event",
          "ownerIdentify": "2100050900"
        },
        "object": {
          "eTag": "\"f2bdbb6d2be86169deed3c6b559c3b3b\"",
          "key": "ls",
          "size": 187040
        },
        "tosSchemaVersion": "1.0",
        "ruleId": "poc-tos-event",
        "region": "cn-beijing",
        "requestParameters": {
          "sourceIPAddress": "************:62640"
        },
        "responseElements": {
          "requestId": "514101ecd4bc56b163ecd4bc-ac15cf25-1pSHDY-PuO-cb-tos-bj-3"
        },
        "userIdentity": {
          "principalId": "trn:iam::2100050900:user/cloudfstest"
        }
      }
    }
  ]
})";

  std::vector<std::shared_ptr<TosEvent>> events;
  EXPECT_TRUE(TosEvent::ParseFromMessage(msg, &events));
  EXPECT_EQ(1, events.size());
  std::shared_ptr<TosEvent> e = events[0];
  EXPECT_EQ("tos:ObjectCreated:Put", e->event_name());
  EXPECT_EQ("tos", e->event_source());
  EXPECT_EQ("2023-02-15T12:49:01Z", e->event_time());
  EXPECT_EQ("1.0", e->event_version());
  EXPECT_EQ("trn:tos:::poc-tos-event", e->tos()->bucket()->trn());
  EXPECT_EQ("poc-tos-event", e->tos()->bucket()->name());
  EXPECT_EQ("2100050900", e->tos()->bucket()->owner_identify());
  EXPECT_EQ("\"f2bdbb6d2be86169deed3c6b559c3b3b\"", e->tos()->object()->etag());
  EXPECT_EQ("ls", e->tos()->object()->key());
  EXPECT_EQ(187040, e->tos()->object()->size());
  EXPECT_EQ("1.0", e->tos()->tos_schema_version());
  EXPECT_EQ("poc-tos-event", e->tos()->rule_id());
  EXPECT_EQ("cn-beijing", e->tos()->region());
  EXPECT_EQ("************:62640",
            e->tos()->request_parameters()->source_ip_address());
  EXPECT_EQ("514101ecd4bc56b163ecd4bc-ac15cf25-1pSHDY-PuO-cb-tos-bj-3",
            e->tos()->response_elements()->request_id());
  EXPECT_EQ("trn:iam::2100050900:user/cloudfstest",
            e->tos()->user_identity()->principal_id());
}

TEST_F(TosEventTest, ParseFromInvalidMessage) {
  {
    // Invalid json format
    const char* msg = "a";
    std::vector<std::shared_ptr<TosEvent>> events;
    EXPECT_FALSE(TosEvent::ParseFromMessage(msg, &events));
  }
}

TEST_F(TosEventTest, EventTypeFromName) {
  {
    std::string event_name = "tos:ObjectCreated:Put";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectCreated:Post";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectCreated:Origin";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectCreated:Fetch";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectCreated:Copy";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectCreated:CompleteUpload";
    EXPECT_EQ(TosEvent::EventType::OBJECT_CREATED,
              EventTypeFromName(event_name));
  }

  {
    std::string event_name = "tos:ObjectRemoved:Delete";
    EXPECT_EQ(TosEvent::EventType::OBJECT_REMOVED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectRemoved:DeleteMarkerCreated";
    EXPECT_EQ(TosEvent::EventType::OBJECT_REMOVED,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:LifecycleExpiration:Delete";
    EXPECT_EQ(TosEvent::EventType::LIFECYCLE_EXPIRATION,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:LifecycleExpiration:DeleteMarkerCreated";
    EXPECT_EQ(TosEvent::EventType::LIFECYCLE_EXPIRATION,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectReplication:ObjectCreated";
    EXPECT_EQ(TosEvent::EventType::OBJECT_REPLICATION,
              EventTypeFromName(event_name));
  }
  {
    std::string event_name = "tos:ObjectReplication:ObjectModified";
    EXPECT_EQ(TosEvent::EventType::OBJECT_REPLICATION,
              EventTypeFromName(event_name));
  }
}

TEST_F(TosEventTest, ToJson) {
  std::string event_str =
      R"({"eventName":"tos:ObjectCreated:Put","eventSource":"tos","eventTime":"2023-02-15T12:49:01Z","eventVersion":"1.0","tos":{"bucket":{"trn":"trn:tos:::poc-tos-event","name":"poc-tos-event","ownerIdentify":"2100050900"},"object":{"eTag":"\"f2bdbb6d2be86169deed3c6b559c3b3b\"","key":"ls","size":187040},"tosSchemaVersion":"1.0","ruleId":"poc-tos-event","region":"cn-beijing","requestParameters":{"sourceIPAddress":"************:62640"},"responseElements":{"requestId":"514101ecd4bc56b163ecd4bc-ac15cf25-1pSHDY-PuO-cb-tos-bj-3"},"userIdentity":{"principalId":"trn:iam::2100050900:user/cloudfstest"}}})";
  std::string msg_str = "{\"events\":[" + event_str + "]}";
  std::vector<std::shared_ptr<TosEvent>> events;
  EXPECT_TRUE(TosEvent::ParseFromMessage(msg_str.c_str(), &events));
  EXPECT_EQ(1, events.size());
  std::shared_ptr<TosEvent> event = events[0];
  EXPECT_EQ(event_str, event->ToJson());
}

}  // namespace dancenn
