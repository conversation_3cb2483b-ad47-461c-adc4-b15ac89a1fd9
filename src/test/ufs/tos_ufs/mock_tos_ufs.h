//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// Third
#include <gmock/gmock.h>

// Project
#include "test/config/ut_config.h"
#include "ufs/tos_ufs/tos_ufs.h"

DECLARE_bool(use_fixed_ak);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);

namespace dancenn {

class GMockTosUfs : public TosUfs {
 public:
  GMockTosUfs(const UfsConfig& ufs_config,
              const TosConfig& tos_config,
              const std::shared_ptr<TosCredentialProvider>& cred_provider)
      : TosUfs(ufs_config, tos_config, cred_provider) {
  }

  MOCK_METHOD2(GetFileOnlyStatus, Status(const std::string&, UfsFileStatus*));
  MOCK_METHOD3(Create,
               Status(const std::string&,
                      const UfsCreateOption&,
                      UfsFileInfoProto*));
  MOCK_METHOD1(DeleteFile, Status(const std::string&));
};

}  // namespace dancenn
