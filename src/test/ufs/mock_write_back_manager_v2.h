#ifndef TEST_ACC_MOCK_WRITE_BACK_MANAGER_V2_H_
#define TEST_ACC_MOCK_WRITE_BACK_MANAGER_V2_H_

#include <gmock/gmock.h>

#include "ufs/upload/write_back_manager_v2.h"

namespace dancenn {

class GMockWriteBackScanner : public WriteBackScanner {
 public:
  MOCK_METHOD0(GetCfIdx, uint32_t());
  MOCK_METHOD0(PreScan, bool());
  MOCK_METHOD3(Handle, bool(MetaStorageSnapPtr, const rocksdb::Slice&, const rocksdb::Slice&));
  MOCK_METHOD0(PostScan, bool());
  MOCK_METHOD0(ToString, std::string());
};

class GMockWriteBackTaskV2 : public WriteBackTaskV2 {
 public:
  MOCK_METHOD1(Run, bool(void*));
};

} // namespace dancenn

#endif /* TEST_ACC_MOCK_WRITE_BACK_MANAGER_V2_H_ */
