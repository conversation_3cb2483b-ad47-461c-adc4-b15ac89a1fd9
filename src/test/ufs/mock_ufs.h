//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <glog/logging.h>
#include <gmock/gmock.h>

#include <algorithm>
#include <atomic>
#include <map>
#include <mutex>
#include <random>
#include <thread>

#include "acc/acc.h"
#include "base/constants.h"
#include "base/path_util.h"

namespace dancenn
{

static const std::string kUTTosBucketDummy = "dummybucket";
static const std::string kUTTosRegion = "cn-beijing";
static const std::string kUTTosEndpoint =
    "https://tos-s3-cn-beijing.volces.com";

class MockUfs : public Ufs {
 public:
  static UfsConfig CreateUfsConf(const std::string& ufs_prefix) {
    CHECK(ufs_prefix[0] == '/');
    UfsConfig conf;
    conf.type = UFS_TYPE_TOS;
    conf.ufs_prefix = ufs_prefix;
    conf.params[dancenn::TosConstants::kConfigBucket] = kUTTosBucketDummy;
    conf.params[dancenn::TosConstants::kConfigPrefix] = ufs_prefix.substr(1);
    conf.params[dancenn::TosConstants::kConfigEndpoint] = kUTTosEndpoint;
    conf.params[dancenn::TosConstants::kConfigRegion] = kUTTosRegion;
    return conf;
  }

  static std::shared_ptr<Ufs> CreateMockUfs(const std::string& ufs_prefix) {
    UfsConfig ufs_config = CreateUfsConf(ufs_prefix);
    return std::shared_ptr<Ufs>(new MockUfs(ufs_config));
  }

  MockUfs(UfsConfig ufs_conf) : Ufs(UFS_TYPE_TEST, ufs_conf) {
    LOG(INFO) << "MockUfs constructed!!!!!!!!!!";
  }
  virtual ~MockUfs() {
    LOG(INFO) << "MockUfs destructed!!!!!!!!!!";
  }

  std::string Name() {
    return "MockUfs";
  }

  virtual Status Init() {
    return Status::OK();
  }

  virtual Status GetFileStatus(const std::string& path, UfsFileStatus* info) override {
    ++get_file_status_count_;
    std::string key;
    Status s = GetObjectKey(path, false, &key);
    if (!s.IsOK()) {
      return s;
    }
    if (key.empty()) { // Root dir
      UfsFileStatus::Builder()
        .SetFileType(UFS_DIR)
        .SetFullPath(path)
        .Build(info);
      return Status::OK();
    }

    // check key directly
    auto itor = objects_.find(key);
    if (itor != objects_.end()) {
      auto&& o = itor->second;
      UfsFileStatus::Builder()
        .SetFullPath("/" + o.key)
        .SetFileSize(o.size)
        .SetBlockSize(128 * 1024 * 1024L)
        .SetFileType(UFS_FILE)
        .SetEtag(o.etag)
        .Build(info);
      return Status::OK();
    }

    std::string dir_prefix =
        key.back() == kSeparatorChar ? key : key + kSeparator;
    bool is_dir = false;
    for (auto it = objects_.begin(); it != objects_.end(); ++it) {
      if (it->first.find(dir_prefix) == 0) {
        is_dir = true;
        break;
      }
    }
    if (is_dir) {
      UfsFileStatus::Builder()
        .SetFullPath("/" + key)
        .SetFileType(UFS_DIR)
        .Build(info);
      return Status::OK();
    }
    return Status(Code::kFileNotFound, "File not found: " + path);
  }

  virtual Status GetFileOnlyStatus(const std::string& path,
                                   UfsFileStatus* info) override {
    ++get_file_only_status_count_;
    std::string key;
    Status s = GetObjectKey(path, false, &key);
    if (!s.IsOK()) {
      return s;
    }
    if (key.empty()) {  // Root dir
      return Status(Code::kFileNotFound, path + " not existed as a file.");
    }

    // check key directly
    auto itor = objects_.find(key);
    if (itor != objects_.end()) {
      auto&& o = itor->second;
      auto build_s = UfsFileStatus::Builder()
                         .SetFullPath("/" + o.key)
                         .SetFileSize(o.size)
                         .SetBlockSize(128 * 1024 * 1024L)
                         .SetFileType(UFS_FILE)
                         .SetEtag(o.etag)
                         .Build(info);
      return Status::OK();
    }

    return Status(Code::kFileNotFound, path + " not existed as a file.");
  }

  bool MatchPrefix(const std::string& key, const std::string& prefix) {
    if (prefix.size() > key.size()) {
      return false;
    }

    return strncmp(key.data(), prefix.data(), prefix.size()) == 0;
  }

  void AddDirSuffix(std::string* prefix) {
    // For root dir, the prefix is "", don't append / as a prefix
    // In other case, directory subfile listing must use a prefix ends with /

    if (prefix->empty()) {  // For root dir, no need to add / prefix to list
      return;
    }
    if (prefix->back() == kSeparatorChar) {
      return;
    }
    prefix->append(kSeparator);
  }

  virtual Status GetDirectoryStatus(const std::string& path,
                                    UfsDirStatus* dir) override {
    std::string prefix;
    Status s = GetObjectKey(path, true, &prefix);
    if (!s.IsOK()) {
      return s;
    }

    auto it = objects_.lower_bound(prefix);
    if (it == objects_.end()) {
      return Status(Code::kDirNotFound,
                    "path not exist not not a dir: " + path);
    }
    if (MatchPrefix(it->first, prefix)) {
      if (it->first.size() > prefix.size()) {
        *dir = UfsDirStatus(path, true);
        return Status::OK();
      }

      ++it;
      if (it == objects_.end()) {
        *dir = UfsDirStatus(path, false);
        return Status::OK();
      }
      if (MatchPrefix(it->first, prefix)) {
        *dir = UfsDirStatus(path, true);
      } else {
        *dir = UfsDirStatus(path, false);
      }
      return Status::OK();
    }
    return Status(Code::kDirNotFound, "path not exist or not a dir: " + path);
  }

  // List files from UFS, Paging is required
  // UFS 目录可能会非常大，因此调用者必须使用 Paging，避免内存溢出;
  // 同时调用者可以根据已经返回的结果的数量，做进一步的细化控制，例如把 list 操作转成后台操作，避免长期阻塞前台 IO
  virtual Status ListFiles(const std::string& path, const ListFilesOption& option, ListFilesResult* result) override {
    ++list_files_count_;

    if (path.empty() || path[0] != '/') {
      return Status(Code::kBadParameter, "Invalid path: " + path);
    }
    const uint32_t page_size = (option.page_size > 0 && option.page_size <= 1000) ? option.page_size : 1000;

    std::string prefix = (path.back() == '/') ? path.substr(1) : (path.substr(1) + "/");
    auto it = objects_.lower_bound(prefix);
    std::string continue_token;
    std::string last_dir;
    for (; it != objects_.end(); ++it) {
      auto&& k = it->first;
      auto&& o = it->second;
      if (k.find(prefix) != 0) {
        break;
      }

      bool is_file = true;
      size_t dir_end_pos = 0;
      for (size_t i = prefix.size(); i < k.size(); ++i) {
        if (k[i] == '/') {
          is_file = false;
          dir_end_pos = i;
          break;
        }
      }

      if (is_file) {
        if (o.key <= option.continue_token) {
          continue;
        }
        {
          UfsFileStatus tmp_info;
          UfsFileStatus::Builder()
              .SetFullPath("/" + o.key)
              .SetEtag(o.etag)
              .SetFileSize(o.size)
              .SetBlockSize(128L * 1024 * 1024)
              .SetFileType(UFS_FILE)
              .Build(&tmp_info);
          result->files.emplace_back(std::move(tmp_info));
        }
        continue_token = o.key;
      } else {
        std::string dir = k.substr(0, dir_end_pos + 1);
        if (dir <= option.continue_token) {
          continue;
        }
        if (dir == last_dir) {
          continue;
        }
        {
          UfsFileStatus tmp_file;
          UfsFileStatus::Builder()
            .SetFullPath("/" + dir.substr(0, dir.size() - 1))
            .SetFileType(UFS_DIR)
            .Build(&tmp_file);
          result->files.emplace_back(std::move(tmp_file));
        }
        last_dir = dir;
        continue_token = dir;
      }
      if (result->files.size() == page_size) {
        result->has_more = true;
        result->continue_token = continue_token;
        break;
      }
    }
    {
      // Remove duplicated dir element in current batch
      auto it = result->files.begin();
      const std::string* last = nullptr;
      while (it != result->files.end()) {
        if (last != nullptr) {
          if (UNLIKELY(*last == it->FileName())) {
            it = result->files.erase(it);
            continue;
          }
        }

        last = &(it->FileName());
        ++it;
      }
    }
    return Status::OK();
  }

  virtual Status Mkdir(const std::string& path) override {
    std::string key;
    Status s = GetObjectKey(path, true, &key);
    if (!s.IsOK()) {
      return s;
    }
    return CreateObject(key, 0, nullptr);
  }

  virtual Status AbortMkdir(const std::string& path) override {
    std::string key;
    Status s = GetObjectKey(path, true, &key);
    if (!s.IsOK()) {
      return s;
    }
    return DeleteObject(key);
  }

  virtual Status Create(const std::string& path,
                        const UfsCreateOption& opt,
                        UfsFileInfoProto* info) override {
    std::string key;
    Status s = GetObjectKey(path, false, &key);
    if (!s.IsOK()) {
      return s;
    }
    if (opt.emptyObject) {
      s = CreateObject(key, 0, info);
      if (!s.IsOK()) {
        return s;
      }
    }
    if (opt.multipartUpload) {
      s = CreateMultipartUpload(key, info->mutable_upload_id());
      if (!s.IsOK()) {
        return s;
      }
    }
    return s;
  }

  // Ensure the dir existed in ufs, create missing parent if necessary
  virtual Status CreateMissingParentIfNecessary(
      const std::string& path) override {
    return Status::OK();
  }

  virtual Status CheckParentReady(const std::string& path) override {
    return Status::OK();
  }

  // UFS create 成功，但是 inner_ns create 失败时，需要 undo UFS 的 create
  // Close 文件时如果长度小于 threshold，需要改用 PUT 上传
  virtual Status AbortCreate(const std::string& path,
                             const UfsFileInfoProto& info,
                             const UfsAbortCreateOption& opt) override {
    std::string key;
    Status s = GetObjectKey(path, false, &key);
    if (!s.IsOK()) {
      return s;
    }
    if (opt.abortMultipartUpload) {
      s = AbortMultipartUpload(key, info.upload_id());
      if (!s.IsOK()) {
        return s;
      }
    }
    return s;
  }

  virtual Status CopyDirectory(const std::string& src,
                               const std::string& dst,
                               UfsCopyResult* result) override {
    UfsDirStatus src_dir;
    Status s = GetDirectoryStatus(src, &src_dir);
    if (!s.IsOK()) {
      return s;
    }

    UfsFileStatus dst_file;
    s = GetFileStatus(dst, &dst_file);
    if (s.IsOK()) {
      return Status(Code::kFileExists, "dst already existed.");
    }
    if (!s.IsOK() && s.code() != Code::kFileNotFound) {
      return s;
    }

    std::string src_prefix, dst_prefix;
    s = GetObjectKey(src, true, &src_prefix);
    if (!s.IsOK()) {
      return s;
    }
    s = GetObjectKey(dst, true, &dst_prefix);
    if (!s.IsOK()) {
      return s;
    }

    std::lock_guard<std::mutex> lg(lock_);
    auto it = objects_.lower_bound(src_prefix);
    while (it != objects_.end()) {
      if (!MatchPrefix(it->first, src_prefix)) {
        break;
      }

      auto&& src_key = it->first;
      auto&& dst_key = dst_prefix + src_key.substr(src_prefix.size());
      std::string name = src_key.substr(src_prefix.size());
      std::string src_full_path = JoinTwoPath(src, name);
      objects_[dst_key] = it->second;
      objects_[dst_key].key = dst_key;
      result->AddResult(src_full_path, it->second.etag);

      LOG(INFO) << "MockUfs copy src: " << src_key << ", dst: " << dst_key;
      ++it;
    }
    result->SetCopyFinished();

    return Status::OK();
  }

  virtual Status CopyFile(const std::string& src,
                          const std::string& dst,
                          bool overwrite,
                          UfsCopyResult* result) override {
    std::string src_key, dst_key;
    Status s = GetObjectKey(src, false, &src_key);
    if (!s.IsOK()) {
      return s;
    }
    s = GetObjectKey(dst, false, &dst_key);
    if (!s.IsOK()) {
      return s;
    }

    std::lock_guard<std::mutex> lg(lock_);

    auto src_itor = objects_.find(src_key);
    if (src_itor == objects_.end()) {
      return Status(Code::kFileNotFound);
    }

    auto dst_itor = objects_.find(dst_key);
    if (dst_itor != objects_.end()) {
      if (!overwrite) {
        return Status(Code::kFileExists, "dst existed.");
      }
    }

    objects_[dst_key] = src_itor->second;
    objects_[dst_key].key = dst_key;
    result->AddResult(src, src_itor->second.etag);
    result->SetCopyFinished();

    return Status::OK();
  }

  virtual Status DeleteFile(const std::string& path) override {
    std::string key;
    Status s = GetObjectKey(path, false, &key);
    if (!s.IsOK()) {
      return s;
    }

    LOG(INFO) << "MockUfs::DeleteFile path: " << path;

    std::lock_guard<std::mutex> lg(lock_);

    auto itor = objects_.find(key);
    if (itor == objects_.end()) {
      return Status(Code::kFileNotFound);
    }
    objects_.erase(itor);
    return Status::OK();
  }

  virtual Status DeleteDirectory(const std::string& path) override {
    std::string prefix;
    Status s = GetObjectKey(path, true, &prefix);
    if (!s.IsOK()) {
      return s;
    }

    LOG(INFO) << "MockUfs::DeleteDirectory path: " << path;

    std::lock_guard<std::mutex> lg(lock_);

    auto it = objects_.lower_bound(prefix);

    while (it != objects_.end()) {
      if (!MatchPrefix(it->first, prefix)) {
        break;
      }

      objects_.erase(it++);
    }

    return Status::OK();
  }

  virtual Status CheckUfsFileState(const std::string& path,
                                   const UfsFileInfoProto& info) override {
    std::string key;
    Status s = GetUfsIdentifier(UfsIdentifierInfo(path), &key);
    if (!s.IsOK()) {
      return s;
    }

    const std::string& uploadId = info.upload_id();
    if (mpus_.find(uploadId) == mpus_.end()) {
      return Status(Code::kBadParameter, "upload id does not exist");
    }
    return Status::OK();
  }

  virtual Status CompleteFile(
      const std::string& path,
      const std::map<int, std::string>& parts,
      UfsFileInfoProto* info,
      uint64_t size) override {
    std::string key;
    Status s = GetUfsIdentifier(UfsIdentifierInfo(path), &key);
    if (!s.IsOK()) {
      return s;
    }

    const std::string& uploadId = info->upload_id();
    if (mpus_.find(uploadId) == mpus_.end()) {
      return Status(Code::kBadParameter, "upload id does not exist");
    }
    auto& mpu = mpus_[uploadId];
    if (mpu.key != key) {
      return Status(Code::kBadParameter, "key mismatch");
    }

    std::ostringstream oss;
    oss << "part[";
    for (auto iter : parts) {
      oss << iter.first << "->" << iter.second;
    }
    oss << "]";
    LOG(INFO) << oss.str();
    oss.str("");

    oss << "mpu.parts[";
    for (auto iter : mpu.parts) {
      oss << iter.first << "->" << iter.second.etag;
    }
    oss << "]";
    LOG(INFO) << oss.str();

    uint64_t totalSize = 0;
    for (auto& p : parts) {
      if (mpu.parts.find(p.first) == mpu.parts.end()) {
        return Status(Code::kBadParameter, "part does not exist");
      }
      if (mpu.parts[p.first].etag != p.second) {
        return Status(Code::kBadParameter, "part etag mismatch");
      }
      totalSize += mpu.parts[p.first].size;
    }
    mpus_.erase(uploadId);
    DeleteObject(key);
    return CreateObject(key, totalSize, info);
  }

  virtual bool SupportAtomicRename() override {
    return support_rename_;
  }

  void SetSupportRename(bool support_rename) {
    support_rename_ = support_rename;
  }

  virtual Status Rename(const std::string& src,
                        const std::string& dst,
                        bool overwrite) override {
    if (!support_rename_) {
      return Status(Code::kNotImplemented);
    }

    std::string src_key, dst_key;
    Status s = GetObjectKey(src, false, &src_key);
    if (!s.IsOK()) {
      return s;
    }
    s = GetObjectKey(dst, false, &dst_key);
    if (!s.IsOK()) {
      return s;
    }

    std::lock_guard<std::mutex> lg(lock_);

    auto src_itor = objects_.lower_bound(src_key);
    if (src_itor == objects_.end()) {
      return Status(Code::kFileNotFound);
    }
    while (src_itor != objects_.end()) {
      if (!MatchPrefix(src_itor->first, src_key)) {
        break;
      }

      auto&& src_path = src_itor->first;
      auto&& dst_path = dst_key + src_path.substr(src_key.size());

      auto dst_itor = objects_.find(dst_path);
      if (dst_itor != objects_.end()) {
        if (!overwrite) {
          return Status(Code::kFileExists, "dst existed.");
        }
      }

      objects_[dst_path] = src_itor->second;
      objects_[dst_path].key = dst_path;
      src_itor = objects_.erase(src_itor);

      LOG(INFO) << "MockUfs rename src: " << src_path << ", dst: " << dst_path;
    }

    return Status::OK();
  }

  virtual Status ReadFile(const std::string& path,
                          uint64_t offset,
                          uint64_t length,
                          std::string* result) override {
    return Status(Code::kNotImplemented);
  }

  virtual Status GetUfsIdentifier(const UfsIdentifierInfo& info,
                                 std::string* key) override {
    const std::string& ufs_path = info.path;
    return GetObjectKey(ufs_path, false, key);
  }

  Status GetObjectKey(const std::string& ufs_path,
                      bool add_trailing_slash,
                      std::string* key) {
    if (ufs_path.empty()) {
      LOG(ERROR) << "Invalid parameter, ufs_path is empty";
      return Status(Code::kBadParameter, "ufs_path is empty.");
    }
    if (ufs_path[0] != kSeparatorChar) {
      LOG(ERROR) << "Invalid parameter, ufs_path not start with / :"
                 << ufs_path;
      return Status(Code::kBadParameter,
                    "ufs_path not start with / : " + ufs_path);
    }

    // root
    if (ufs_path == kSeparator) {
      *key = "";
      return Status::OK();
    }

    if (add_trailing_slash && ufs_path.back() != kSeparatorChar) {
      *key = ufs_path.substr(1) + kSeparator;
    } else {
      *key = ufs_path.substr(1);
    }
    return Status::OK();
  }

  virtual Status GetUfsInfo(const std::string& key, UfsIdentifierInfo* info) override {
    if (!info) {
      LOG(ERROR) << "Invalid parameter, info is null";
      return Status(Code::kBadParameter, "info is null");
    }
    std::string path = key;
    if (path.back() == '/') {
      path.pop_back();
    }
    path.insert(0, "/");
    info->path = path;
    return Status::OK();
  }

  Status CheckUploadId(const std::string& path, const std::string& uploadId) {
    std::string key;
    Status s = GetUfsIdentifier(UfsIdentifierInfo(path), &key);
    if (!s.IsOK()) {
      return s;
    }

    if (mpus_.find(uploadId) == mpus_.end()) {
      LOG(INFO) << "Upload id not found";
      return Status(Code::kError, "Upload id not found");
    }
    if (mpus_[uploadId].key != key) {
      LOG(INFO) << "Upload id path mismatch: " << mpus_[uploadId].key << " "
                << key;
      return Status(
          Code::kError,
          "Upload id path mismatch: " + mpus_[uploadId].key + " " + key);
    }
    return Status::OK();
  }

  Status AddPartToObject(const std::string& path,
                         const std::string& uploadId,
                         int part_num,
                         uint64_t size,
                         std::string* etag) {
    std::string key;
    Status s = GetUfsIdentifier(UfsIdentifierInfo(path), &key);
    if (!s.IsOK()) {
      return s;
    }

    if (mpus_.find(uploadId) == mpus_.end()) {
      return Status(Code::kBadParameter, "upload id does not exist");
    }
    auto& mpu = mpus_[uploadId];
    if (mpu.key != key) {
      return Status(Code::kBadParameter, "key mismatch");
    }
    MockPart new_part;
    new_part.size = size;
    GenerateHash(new_part.etag, sizeof(new_part.etag));
    *etag = new_part.etag;
    mpu.parts.insert({part_num, new_part});
    LOG(INFO) << "AddPartToObject "
              << " upload_id=" << uploadId << " part_num=" << part_num
              << " size=" << size << " etag=" << *etag;
    return Status::OK();
  }

  Status DeleteObject(const std::string& key) {
    if (objects_.find(key) == objects_.end()) {
      return Status(Code::kBadParameter, "key does not exist");
    }
    objects_.erase(key);
    return Status::OK();
  }

  Status CreateObject(const std::string& key,
                      uint64_t size,
                      UfsFileInfoProto* info = nullptr) {
    if (key.empty()) {
        return Status(Code::kBadParameter, "key is empty");
    }
    if (key[0] == '/') {
        return Status(Code::kBadParameter, "key cannot start with /: " + key);
    }
    MockObject o;
    o.key = key;
    o.size = size;
    GenerateHash(o.etag, sizeof(o.etag));
    objects_[key] = o;
    if (info) {
      info->set_etag(o.etag);
    }
    return Status::OK();
  }

  Status CreateMultipartUpload(const std::string& key, std::string* uploadId) {
    char tmp[33];
    GenerateHash(tmp, sizeof(tmp));
    *uploadId = tmp;
    MockMultiPartUpload mock_mpu;
    mock_mpu.key = key;
    mpus_.insert({tmp, mock_mpu});
    return Status::OK();
  }

  Status AbortMultipartUpload(const std::string& key, const std::string& uploadId) {
    if (mpus_.find(uploadId) == mpus_.end()) {
      return Status(Code::kBadParameter);
    }
    mpus_.erase(uploadId);
    return Status::OK();
  }

  uint64_t GetGetFileStatusCount() { return get_file_status_count_.load(); }
  uint64_t GetListFilesCount() { return list_files_count_.load(); }

private:
  void GenerateHash(char* input, int32_t len) {
    static constexpr auto chars =
        "0123456789"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz";
    thread_local std::mt19937 rng{std::random_device{}()};
    thread_local auto dist = std::uniform_int_distribution<std::string::size_type>{{}, 62 - 1};
    std::generate_n(input, len - 1, [&]() { return chars[dist(rng)]; });
    input[len - 1] = '\0';
  }
  std::string FileNameFromPath(const std::string& path) {
      auto pos = path.find_last_of('/');
      if (pos < 0) {
        return path;
      }
      return path.substr(pos + 1);
  }
private:
  struct MockObject {
    std::string key;
    uint64_t size;
    char etag[33];
  };

  struct MockPart {
    uint64_t size;
    char etag[33];
  };

  struct MockMultiPartUpload {
    std::string key;
    std::map<int, MockPart> parts;
  };

  std::mutex lock_;
  std::map<std::string, MockObject> objects_;
  std::map<std::string, MockMultiPartUpload> mpus_;
  std::atomic<uint64_t> get_file_status_count_ {0};
  std::atomic<uint64_t> get_file_only_status_count_{0};
  std::atomic<uint64_t> list_files_count_ {0};

  bool support_rename_ = false;
};

class GMockUfs : public Ufs {
 public:
  GMockUfs() : Ufs(UfsType::UFS_TYPE_TEST, MockUfs::CreateUfsConf("/")) {
  }

  std::string Name() {
    return "GMockUfs";
  }

  MOCK_METHOD0(Init, Status());
  MOCK_METHOD2(GetFileStatus, Status(const std::string&, UfsFileStatus*));
  MOCK_METHOD2(GetFileOnlyStatus, Status(const std::string&, UfsFileStatus*));
  MOCK_METHOD2(GetDirectoryStatus, Status(const std::string&, UfsDirStatus*));
  MOCK_METHOD3(ListFiles,
               Status(const std::string&,
                      const ListFilesOption&,
                      ListFilesResult*));
  MOCK_METHOD1(Mkdir, Status(const std::string&));
  MOCK_METHOD1(AbortMkdir, Status(const std::string&));
  MOCK_METHOD1(CreateMissingParentIfNecessary, Status(const std::string&));
  MOCK_METHOD1(CheckParentReady, Status(const std::string&));
  MOCK_METHOD3(Create,
               Status(const std::string&,
                      const UfsCreateOption&,
                      UfsFileInfoProto*));
  MOCK_METHOD3(AbortCreate,
               Status(const std::string&,
                      const UfsFileInfoProto&,
                      const UfsAbortCreateOption&));
  MOCK_METHOD3(CopyDirectory,
               Status(const std::string&,
                      const std::string&,
                      UfsCopyResult*));
  MOCK_METHOD4(
      CopyFile,
      Status(const std::string&, const std::string&, bool, UfsCopyResult*));
  MOCK_METHOD1(DeleteFile, Status(const std::string&));
  MOCK_METHOD1(DeleteDirectory, Status(const std::string&));
  MOCK_METHOD2(CheckUfsFileState,
               Status(const std::string&, const UfsFileInfoProto&));
  MOCK_METHOD4(CompleteFile,
               Status(const std::string&,
                      const std::map<int, std::string>&,
                      UfsFileInfoProto*,
                      uint64_t));
  MOCK_METHOD2(GetUfsIdentifier,
               Status(const UfsIdentifierInfo&, std::string*));
  MOCK_METHOD2(GetUfsInfo, Status(const std::string&, UfsIdentifierInfo*));
  MOCK_METHOD3(Rename,
               Status(const std::string& src,
                      const std::string& dst,
                      bool overwrite));
  MOCK_METHOD4(ReadFile,
               Status(const std::string&, uint64_t, uint64_t, std::string*));
};

}  // namespace dancenn
