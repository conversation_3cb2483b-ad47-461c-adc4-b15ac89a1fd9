//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <regex>
#include <sstream>
#include <unordered_set>

#include "ufs/tos/tos_constant.h"
#include "ufs/tos/tos_cred_keeper.h"
#include "ufs/tos/tos_s3_def.h"
#include "ufs/tos_ufs/tos_ufs.h"
#include "base/string_utils.h"
#include "base/time_util.h"
#include "test/config/ut_config.h"

DECLARE_int32(ufs_worker_thread_num);
DECLARE_bool(use_fixed_ak);
DECLARE_int32(namespace_type);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);

static const std::string kUTTosBucket = "cloudfs-ut";
static const std::string kUTTosRegion = "cn-beijing";
static const std::string kUTTosEndpoint =
    "https://tos-s3-cn-beijing.volces.com";

namespace dancenn {

class TosUfsPerfTest : public testing::Test {
 public:
  void SetUp() override {
    ak_ = UTConfig::Instance().Ak();
    sk_ = UTConfig::Instance().Sk();

    // FLAGS_ufs_worker_thread_num = 256;
    FLAGS_use_fixed_ak = true;
    FLAGS_tos_access_key_id = ak_;
    FLAGS_tos_secret_access_key = sk_;

    config_.bucket = kUTTosBucket;
    config_.endpoint = kUTTosEndpoint;
    config_.prefix = "";
    config_.region = kUTTosRegion;

    ufs_config_ = TosConfig::CreateUfsConfig(config_);

    auto cred_keeper = std::make_shared<TosCredKeeper>();
    CHECK(cred_keeper->Start().IsOK());
    CHECK(cred_keeper->InnerCredential()->ak == ak_);
    CHECK(cred_keeper->InnerCredential()->sk == sk_);

    auto cred = std::shared_ptr<TosCredentialProvider>(
        new StaticTosCredentialProvider(cred_keeper));
    ufs_.reset(new TosUfs(ufs_config_, config_, cred));
    ufs_->Init();
  }

  void TearDown() override {
    ufs_.reset();
  }

 protected:
  std::shared_ptr<TosUfs> ufs_;
  UfsConfig ufs_config_;
  TosConfig config_;
  std::string ak_;
  std::string sk_;
};

TEST_F(TosUfsPerfTest, TestCopyDirPerf) {
  std::vector<std::string> cases{"1ksmallfiles",
                                 "5ksmallfiles",
                                 "10ksmallfiles",
                                 "1kmidfiles",
                                 "5kmidfiles",
                                 "10kmidfiles",
                                 "50glargefiles",
                                 "200glargefiles"};
  std::unordered_map<std::string, uint64_t> copy_times;
  std::string base_dir = "/nn/TosUfsTest/TestCopyDir";
  uint64_t ts = TimeUtil::GetNowEpochMs();
  for (auto&& c : cases) {
    uint64_t start = TimeUtil::GetNowEpochMs();

    std::string source_dir = absl::StrFormat("%s/%s/", base_dir, c.c_str());
    std::string target_dir =
        absl::StrFormat("/tmp%s/%lu/%s/", base_dir, ts, c.c_str());
    fprintf(stdout,
            ">>>>>>>>>>>>>>>>>>>> Case: %s | Target: %s <<<<<<<<<<<<<<<<<<<<\n",
            c.c_str(),
            target_dir.c_str());
    fflush(stdout);

    UfsCopyResult result;
    auto s = ufs_->CopyDirectory(source_dir, target_dir, &result);
    ASSERT_TRUE(s.IsOK());

    uint64_t ellapsed = TimeUtil::GetNowEpochMs() - start;
    copy_times[c] = ellapsed;
  }

  fprintf(stdout,
          ">>>>>>>>>>>>>>>>>>>> % 20s % 20s <<<<<<<<<<<<<<<<<<<<\n",
          "File",
          "Copy Time(ms)");
  for (auto&& c : cases) {
    fprintf(stdout,
            ">>>>>>>>>>>>>>>>>>>> % 20s % 20d <<<<<<<<<<<<<<<<<<<<\n",
            c.c_str(),
            copy_times[c]);
  }
  fflush(stdout);

  fprintf(stdout, ">>>>>>>>>>>>>>>>>>>> Deleting... <<<<<<<<<<<<<<<<<<<<\n");
  fflush(stdout);
  for (auto&& c : cases) {
    std::string target_dir =
        absl::StrFormat("/tmp%s/%lu/%s/", base_dir, ts, c.c_str());
    auto delete_s = ufs_->DeleteDirectory(target_dir);
    ASSERT_TRUE(delete_s.IsOK());
  }
  fprintf(stdout, ">>>>>>>>>>>>>>>>>>>> All done! <<<<<<<<<<<<<<<<<<<<\n");
  fflush(stdout);
}

TEST_F(TosUfsPerfTest, TestListFilesPerf) {
  // Test paging
  auto list_func = [this](const std::string& path,
                          int expected_cout) -> uint64_t {
    std::string continue_token;
    std::string last_file;
    int count = 0;
    uint64_t start_ts = TimeUtil::GetNowEpochMs();
    while (true) {
      ListFilesResult res;
      ListFilesOption opt;
      opt.page_size = 1000;
      if (!continue_token.empty()) {
        opt.continue_token = continue_token;
      }

      auto s = ufs_->ListFiles(path, opt, &res);
      CHECK(s.IsOK());
      count += res.files.size();
      continue_token = res.continue_token;
      if (!res.has_more) {
        break;
      }
    }
    uint64_t ellapsed = TimeUtil::GetNowEpochMs() - start_ts;
    CHECK_EQ(count, expected_cout);
    return ellapsed;
  };

  std::vector<std::pair<std::string, uint64_t>> lats;
  lats.reserve(8);
  {
    std::string path = "/listing_test/dir_100k_files";
    uint64_t lat = list_func(path, 100000);
    lats.emplace_back(std::make_pair(path, lat));
  }
  {
    std::string path = "/listing_test/dir_1k_subdirs_with_1k_files";
    uint64_t lat = list_func(path, 1000);
    lats.emplace_back(std::make_pair(path, lat));
  }
  {
    std::string path = "/listing_test/dir_1k_subdirs_with_100_files";
    uint64_t lat = list_func(path, 1000);
    lats.emplace_back(std::make_pair(path, lat));
  }

  fprintf(stdout,
          ">>>>>>>>>>>>>>>>>>>> % 30s % 20s <<<<<<<<<<<<<<<<<<<<\n",
          "Path",
          "Lat(ms)");
  for (auto&& l : lats) {
    fprintf(stdout,
            ">>>>>>>>>>>>>>>>>>>> % 30s % 20d <<<<<<<<<<<<<<<<<<<<\n",
            l.first.c_str(),
            l.second);
  }
}

// TEST_F(TosUfsPerfTest, DataSetGen) {
//   std::string src_dir = "/nn/TosUfsTest/TestCopyDir/1kmidfiles/";
//   {
//     std::string target_dir =
//         "/nn/TosUfsTest/TestCopyDir/2kmidfiles/1kmidfiles-001/";
//     UfsCopyResult result;
//     auto s = ufs_->CopyDirectory(src_dir, target_dir, &result);
//     CHECK(s.IsOK());
//   }
//   {
//     std::string target_dir =
//         "/nn/TosUfsTest/TestCopyDir/2kmidfiles/1kmidfiles-002/";
//     UfsCopyResult result;
//     auto s = ufs_->CopyDirectory(src_dir, target_dir, &result);
//     CHECK(s.IsOK());
//   }
// }

}  // namespace dancenn
