//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// Third
#include <gtest/gtest.h>

// Project
#include "ufs/ufs_util.h"

namespace dancenn {

TEST(UfsUtilTest, IsValidUfsPath) {
  EXPECT_TRUE(UfsUtil::IsValidUfsPath("/"));
  EXPECT_TRUE(UfsUtil::IsValidUfsPath("/a"));
  EXPECT_TRUE(UfsUtil::IsValidUfsPath("/a/b/c"));
  EXPECT_TRUE(UfsUtil::IsValidUfsPath("/a/b/c/"));

  EXPECT_FALSE(UfsUtil::IsValidUfsPath(""));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("a"));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("//"));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("/a/b//"));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("/a/b//c"));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("/a/./c"));
  EXPECT_FALSE(UfsUtil::IsValidUfsPath("/a/../c"));
}

TEST(UfsUtilTest, IsListingChildrenValidUfsPath) {
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", ""), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "/"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a//"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/b/"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a//b/"), false);

  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "./"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", ".."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "../"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "..."), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", ".../"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/./"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/.."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/../"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/..."), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("", "a/.../"), true);

  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a//"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "b/"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b//"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/c/"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b//c/"), false);

  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/./"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/.."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/../"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/..."), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/.../"), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/./"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/.."), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/../"), false);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/..."), true);
  ASSERT_EQ(UfsUtil::IsListingChildrenValidUfsPath("a/", "a/b/.../"), true);
}

TEST(UfsUtilTest, ConvertInnerPath) {
  std::string prefix = "/my-prefix/";
  {
    std::string path = "/my-prefix/dir/file";
    std::string inner_path;
    Status s = UfsUtil::ConvertInnerPath(prefix, path, &inner_path);
    EXPECT_TRUE(s.IsOK());
    EXPECT_EQ("/dir/file", inner_path);
  }

  {
    std::string path = "";
    std::string inner_path;
    Status s = UfsUtil::ConvertInnerPath(prefix, path, &inner_path);
    EXPECT_FALSE(s.IsOK());
  }

  {
    std::string path = "/another-long-prefix/file";
    std::string inner_path;
    Status s = UfsUtil::ConvertInnerPath(prefix, path, &inner_path);
    EXPECT_FALSE(s.IsOK());
  }

  {
    std::string path = "/file";
    std::string inner_path;
    Status s = UfsUtil::ConvertInnerPath(prefix, path, &inner_path);
    EXPECT_FALSE(s.IsOK());
  }
}

}  // namespace dancenn
