//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// Third
#include <gtest/gtest.h>

// Project
#include "ufs/ufs_event_manager_status.h"

namespace dancenn {

TEST(UfsEventManagerStatusTest, ToJson) {
  UfsEventManagerStatus status{true, false, 0, true, 2, 8};
  std::string status_str = status.ToJson();
  EXPECT_EQ(
      "{\"enabled\": true, "
      "\"kafka_consumer_running\": false, "
      "\"kafka_consumer_num\": 0, "
      "\"rmq_consumer_running\": true, "
      "\"rmq_consumer_num\": 2, "
      "\"handler_num\": 8}",
      status_str);
}

}  // namespace dancenn
