//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "ufs/ufs_event_rmq_consumer.h"

// Third
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "rocketmq/MQMessageExt.h"

// Project
#include "test/namespace/mock_namespace.h"
#include "test/ufs/tos_ufs/mock_tos_event.h"
#include "test/ufs/tos_ufs/mock_tos_ufs.h"
#include "ufs/ufs_event_manager.h"
#include "ufs/ufs_util.h"

DECLARE_bool(ufs_event_manager_enabled);
DECLARE_bool(ufs_event_kafka_consumer_enabled);
DECLARE_bool(ufs_event_rmq_consumer_enabled);
DECLARE_uint32(ufs_event_handler_count);

namespace dancenn {

class UfsEventRmqConsumerTest : public testing::Test {
 public:
  void SetUp() override {
    tos_config_ = TosConfig(kUTTosEventEndpoint,
                            kUTTosEventRegion,
                            kUTTosEventBucket,
                            kUTTosEventPrefix);
    ufs_config_ = TosConfig::CreateUfsConfig(tos_config_);
    auto cred_keeper = std::make_shared<TosCredKeeper>();
    CHECK(cred_keeper->Start().IsOK());
    auto cred_provider = std::shared_ptr<TosCredentialProvider>(
        new StaticTosCredentialProvider(cred_keeper));
    tos_ufs_ =
        std::make_shared<GMockTosUfs>(ufs_config_, tos_config_, cred_provider);

    ns_ = std::make_shared<GMockNameSpace>();

    FLAGS_ufs_event_manager_enabled = true;
    FLAGS_ufs_event_kafka_consumer_enabled = false;
    FLAGS_ufs_event_rmq_consumer_enabled = false;
    FLAGS_ufs_event_handler_count = 1;
    mgr_ = std::make_shared<UfsEventManager>(tos_ufs_);
    mgr_->SetNS(ns_);
    mgr_->Start();
  }

  void TearDown() override {
    mgr_->Stop();
    mgr_.reset();
    ns_.reset();
    tos_ufs_.reset();
  }

  TosConfig tos_config_;
  UfsConfig ufs_config_;
  std::shared_ptr<GMockTosUfs> tos_ufs_;
  std::shared_ptr<GMockNameSpace> ns_;
  std::shared_ptr<UfsEventManager> mgr_;
};

TEST_F(UfsEventRmqConsumerTest, ListenerConsumeMessageSuccess) {
  std::shared_ptr<TosEvent> event = TosEventForUT();
  std::string msg_str = "{\"events\":[" + event->ToJson() + "]}";
  rocketmq::MQMessageExt msg;
  msg.setBody(msg_str);

  std::string ufs_path = UfsUtil::UfsKeyToPath(event->tos()->object()->key());
  std::string inner_path;
  Status s =
      UfsUtil::ConvertInnerPath("/" + kUTTosEventPrefix, ufs_path, &inner_path);
  EXPECT_TRUE(s.IsOK());
  EXPECT_CALL(*ns_,
              SyncUfsFileOnly(ufs_path,
                              inner_path,
                              testing::_,
                              testing::_,
                              std::dynamic_pointer_cast<Ufs>(tos_ufs_),
                              testing::_,
                              testing::_,
                              testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status::OK()));

  std::shared_ptr<UfsEventRmqMessageListener> listener =
      std::make_shared<UfsEventRmqMessageListener>(mgr_.get());
  rocketmq::ConsumeStatus status = listener->consumeMessage({msg});
  EXPECT_EQ(rocketmq::ConsumeStatus::CONSUME_SUCCESS, status);
}

TEST_F(UfsEventRmqConsumerTest, ListenerConsumeMessageFailed) {
  std::shared_ptr<TosEvent> event = TosEventForUT();
  std::string msg_str = "{\"events\":[" + event->ToJson() + "]}";
  rocketmq::MQMessageExt msg;
  msg.setBody(msg_str);

  std::string ufs_path = UfsUtil::UfsKeyToPath(event->tos()->object()->key());
  std::string inner_path;
  Status s =
      UfsUtil::ConvertInnerPath("/" + kUTTosEventPrefix, ufs_path, &inner_path);
  EXPECT_TRUE(s.IsOK());
  EXPECT_CALL(*ns_,
              SyncUfsFileOnly(ufs_path,
                              inner_path,
                              testing::_,
                              testing::_,
                              std::dynamic_pointer_cast<Ufs>(tos_ufs_),
                              testing::_,
                              testing::_,
                              testing::_))
      .Times(5)
      .WillRepeatedly(testing::Return(Status(Code::kError, "mocked error")));

  std::shared_ptr<UfsEventRmqMessageListener> listener =
      std::make_shared<UfsEventRmqMessageListener>(mgr_.get());
  rocketmq::ConsumeStatus status = listener->consumeMessage({msg});
  EXPECT_EQ(rocketmq::ConsumeStatus::RECONSUME_LATER, status);
}

}  // namespace dancenn
