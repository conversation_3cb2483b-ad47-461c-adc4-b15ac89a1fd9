//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "ufs/tos_ufs/tos_ufs.h"

#include "base/time_util.h"

#include "test/ufs/tos_ufs/mock_tos_client.h"
#include "test/tos/mock_tos_cred.h"

DECLARE_bool(ufs_tos_shallow_copy_enabled);

static const uint64_t kTosCopyObjectSizeLimit = 512UL * 1024 * 1024;

namespace dancenn {

class TosUfsTestV2 : public ::testing::Test {
 public:
  void SetUp() override {
    tos_config_.bucket = "bucket";
    tos_config_.prefix = "prefix";
    tos_config_.endpoint = "https://tos-s3-cn-beijing.volces.com";
    tos_config_.region = "cn-beijing"; 
    ufs_config_ = TosConfig::CreateUfsConfig(tos_config_);

    cred_ = std::make_shared<VolcCredential>();

    cred_provider_ = std::make_shared<GMockTosCredentialProvider>();

    EXPECT_CALL(*cred_provider_, RegisterUpdateCallback(::testing::_));
    EXPECT_CALL(*cred_provider_, UnregisterUpdateCallback(::testing::_));
    EXPECT_CALL(*cred_provider_, GetCredential())
        .WillRepeatedly(::testing::Return(cred_));

    tos_client_ = std::make_shared<GMockTosClient>(tos_config_, cred_provider_);
    ufs_ = std::make_shared<TosUfs>(
        ufs_config_, tos_config_, cred_provider_, tos_client_);
    ufs_->Init();
  }

 protected:
  UfsConfig ufs_config_;
  TosConfig tos_config_;
  std::shared_ptr<VolcCredential> cred_;
  std::shared_ptr<GMockTosCredentialProvider> cred_provider_;
  std::shared_ptr<GMockTosClient> tos_client_;
  std::shared_ptr<TosUfs> ufs_;
};

TEST_F(TosUfsTestV2, TestCopyFileNoShallowCopy) {
  std::string src = "/src";
  std::string src_key = "src";
  std::string dst = "/dst";
  std::string dst_key = "dst";

  {
    UfsCopyResult res;
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kError)));
    EXPECT_FALSE(ufs_->CopyFile(src, dst, false, &res).IsOK());
  }

  {
    UfsCopyResult res;
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::Return(Status::OK()));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kError)));
    EXPECT_FALSE(ufs_->CopyFile(src, dst, false, &res).IsOK());
  }

  {
    UfsCopyResult res;
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::Return(Status::OK()));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status::OK()));
    EXPECT_EQ(ufs_->CopyFile(src, dst, false, &res).code(), Code::kFileExists);
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosHeadObjectResult src_head_res;
    src_head_res.SetContentLength(kTosCopyObjectSizeLimit);
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(src_head_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    std::string res_etag = "new_etag";
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_key, dst_key, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(res_etag),
                                   ::testing::Return(Status::OK())));
    EXPECT_TRUE(ufs_->CopyFile(src, dst, false, &res).IsOK());
    EXPECT_TRUE(res.IsCopyFinished());
    EXPECT_LE(res.GetCopyTimeStamp(), TimeUtilV2::GetNowEpochMs());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 1);
    auto result_detail = copy_result.find(src);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, res_etag);
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosHeadObjectResult src_head_res;
    src_head_res.SetContentLength(kTosCopyObjectSizeLimit + 1);
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(src_head_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    std::string res_upload_id = "new_upload_id";
    std::string res_etag = "new_etag";
    EXPECT_CALL(*tos_client_,
                CreateMpuObject(dst_key, "", ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(res_upload_id),
                                   ::testing::Return(Status::OK())));
    TosUploadPartCopyResult part_1;
    Aws::S3::Model::CopyPartResult res_1;
    res_1.SetETag("part_etag_1");
    part_1.SetCopyPartResult(res_1);
    TosUploadPartCopyResult part_2;
    Aws::S3::Model::CopyPartResult res_2;
    res_2.SetETag("part_etag_2");
    part_2.SetCopyPartResult(res_2);
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_key,
                                  src_key,
                                  0,
                                  kTosCopyObjectSizeLimit,
                                  res_upload_id,
                                  1,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(part_1),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_key,
                                  src_key,
                                  kTosCopyObjectSizeLimit,
                                  1,
                                  res_upload_id,
                                  2,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(part_2),
                                   ::testing::Return(Status::OK())));
    std::map<int, std::string> parts = {{1, "part_etag_1"}, {2, "part_etag_2"}};
    EXPECT_CALL(
        *tos_client_,
        CompleteObject(dst_key, res_upload_id, parts, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(res_etag),
                                   ::testing::Return(Status::OK())));
    EXPECT_TRUE(ufs_->CopyFile(src, dst, false, &res).IsOK());
    EXPECT_TRUE(res.IsCopyFinished());
    EXPECT_LE(res.GetCopyTimeStamp(), TimeUtilV2::GetNowEpochMs());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 1);
    auto result_detail = copy_result.find(src);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, res_etag);
  }
}

TEST_F(TosUfsTestV2, TestCopyFileShallowCopy) {
  FLAGS_ufs_tos_shallow_copy_enabled = true;
  std::string src = "/src";
  std::string src_key = "src";
  std::string dst = "/dst";
  std::string dst_key = "dst";

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosHeadObjectResult src_head_res;
    src_head_res.SetContentLength(kTosCopyObjectSizeLimit + 1);
    src_head_res.SetLastModified(Aws::Utils::DateTime(1717113600000L + 1000));
    EXPECT_CALL(*tos_client_, HeadObject(src_key, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(src_head_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    std::string res_etag = "new_etag";
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_key, dst_key, ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(res_etag),
                                   ::testing::Return(Status::OK())));
    EXPECT_TRUE(ufs_->CopyFile(src, dst, false, &res).IsOK());
    EXPECT_TRUE(res.IsCopyFinished());
    EXPECT_LE(res.GetCopyTimeStamp(), TimeUtilV2::GetNowEpochMs());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 1);
    auto result_detail = copy_result.find(src);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, res_etag);
  }
  FLAGS_ufs_tos_shallow_copy_enabled = false;
}

TEST_F(TosUfsTestV2, TestCopyDirectoryNoShallowCopy) {
  std::string src = "/src";
  std::string src_key = "src";
  std::vector<std::string> src_keys = {"src/file1", "src/file2", "src/file3"};
  std::string dst = "/dst";
  std::string dst_key = "dst";
  std::vector<std::string> dst_keys = {"dst/file1", "dst/file2", "dst/file3"};

  {
    UfsCopyResult res;
    EXPECT_CALL(*tos_client_,
                ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::Return(Status(Code::kError)));
    EXPECT_FALSE(ufs_->CopyDirectory(src, dst, &res).IsOK());
  }

  {
    UfsCopyResult res;
    EXPECT_CALL(*tos_client_,
                ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::Return(Status::OK()));
    EXPECT_FALSE(ufs_->CopyDirectory(src, dst, &res).IsOK());
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosListObjectsResult list_src_res;
    Aws::S3::Model::Object obj1;
    obj1.SetKey(src_keys[0]);
    obj1.SetSize(kTosCopyObjectSizeLimit);
    obj1.SetETag(src_keys[0]);
    list_src_res.AddContents(obj1);
    Aws::S3::Model::Object obj2;
    obj2.SetKey(src_keys[1]);
    obj2.SetSize(kTosCopyObjectSizeLimit);
    obj2.SetETag(src_keys[1]);
    list_src_res.AddContents(obj2);
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kError)));
    EXPECT_FALSE(ufs_->CopyDirectory(src, dst, &res).IsOK());
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosListObjectsResult list_src_res;
    Aws::S3::Model::Object obj1;
    obj1.SetKey(src_keys[0]);
    obj1.SetSize(kTosCopyObjectSizeLimit);
    obj1.SetETag(src_keys[0]);
    list_src_res.AddContents(obj1);
    Aws::S3::Model::Object obj2;
    obj2.SetKey(src_keys[1]);
    obj2.SetSize(kTosCopyObjectSizeLimit);
    obj2.SetETag(src_keys[1]);
    list_src_res.AddContents(obj2);
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(dst_key + "/", "", 1, ::testing::_, ""))
        .WillOnce(::testing::Return(Status(Code::kError)));
    EXPECT_FALSE(ufs_->CopyDirectory(src, dst, &res).IsOK());
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosListObjectsResult list_src_res;
    Aws::S3::Model::Object obj1;
    obj1.SetKey(src_keys[0]);
    obj1.SetSize(kTosCopyObjectSizeLimit);
    obj1.SetETag(src_keys[0]);
    list_src_res.AddContents(obj1);
    Aws::S3::Model::Object obj2;
    obj2.SetKey(src_keys[1]);
    obj2.SetSize(kTosCopyObjectSizeLimit);
    obj2.SetETag(src_keys[1]);
    list_src_res.AddContents(obj2);
    Aws::S3::Model::Object obj3;
    obj3.SetKey(src_keys[2]);
    obj3.SetSize(kTosCopyObjectSizeLimit);
    obj3.SetETag(src_keys[2]);
    list_src_res.AddContents(obj3);
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(dst_key + "/", "", 1, ::testing::_, ""))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 1000, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[0], dst_keys[0], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[0]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[1], dst_keys[1], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[1]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[2], dst_keys[2], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[2]),
                                   ::testing::Return(Status::OK())));
    EXPECT_TRUE(ufs_->CopyDirectory(src, dst, &res).IsOK());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 3);
    auto result_detail = copy_result.find(std::string("/") + src_keys[0]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[0]);
    result_detail = copy_result.find(std::string("/") + src_keys[1]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[1]);
    result_detail = copy_result.find(std::string("/") + src_keys[2]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[2]);
  }

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosListObjectsResult list_src_res;
    Aws::S3::Model::Object obj1;
    obj1.SetKey(src_keys[0]);
    obj1.SetSize(kTosCopyObjectSizeLimit + 1);
    obj1.SetETag(src_keys[0]);
    list_src_res.AddContents(obj1);
    Aws::S3::Model::Object obj2;
    obj2.SetKey(src_keys[1]);
    obj2.SetSize(kTosCopyObjectSizeLimit + 1);
    obj2.SetETag(src_keys[1]);
    list_src_res.AddContents(obj2);
    Aws::S3::Model::Object obj3;
    obj3.SetKey(src_keys[2]);
    obj3.SetSize(kTosCopyObjectSizeLimit + 1);
    obj3.SetETag(src_keys[2]);
    list_src_res.AddContents(obj3);
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(dst_key + "/", "", 1, ::testing::_, ""))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 1000, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    TosUploadPartCopyResult dst_0_part_1;
    Aws::S3::Model::CopyPartResult dst_0_part_res_1;
    dst_0_part_res_1.SetETag(dst_keys[0] + "part_1");
    dst_0_part_1.SetCopyPartResult(dst_0_part_res_1);
    TosUploadPartCopyResult dst_0_part_2;
    Aws::S3::Model::CopyPartResult dst_0_part_res_2;
    dst_0_part_res_2.SetETag(dst_keys[0] + "part_2");
    dst_0_part_2.SetCopyPartResult(dst_0_part_res_2);
    TosUploadPartCopyResult dst_1_part_1;
    Aws::S3::Model::CopyPartResult dst_1_part_res_1;
    dst_1_part_res_1.SetETag(dst_keys[1] + "part_1");
    dst_1_part_1.SetCopyPartResult(dst_1_part_res_1);
    TosUploadPartCopyResult dst_1_part_2;
    Aws::S3::Model::CopyPartResult dst_1_part_res_2;
    dst_1_part_res_2.SetETag(dst_keys[1] + "part_2");
    dst_1_part_2.SetCopyPartResult(dst_1_part_res_2);
    TosUploadPartCopyResult dst_2_part_1;
    Aws::S3::Model::CopyPartResult dst_2_part_res_1;
    dst_2_part_res_1.SetETag(dst_keys[2] + "part_1");
    dst_2_part_1.SetCopyPartResult(dst_2_part_res_1);
    TosUploadPartCopyResult dst_2_part_2;
    Aws::S3::Model::CopyPartResult dst_2_part_res_2;
    dst_2_part_res_2.SetETag(dst_keys[2] + "part_2");
    dst_2_part_2.SetCopyPartResult(dst_2_part_res_2);
    EXPECT_CALL(*tos_client_, CreateMpuObject(dst_keys[0], "", ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[0]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[0],
                                  src_keys[0],
                                  0,
                                  kTosCopyObjectSizeLimit,
                                  dst_keys[0],
                                  1,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_0_part_1),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[0],
                                  src_keys[0],
                                  kTosCopyObjectSizeLimit,
                                  1,
                                  dst_keys[0],
                                  2,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_0_part_2),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CompleteObject(
                    dst_keys[0],
                    dst_keys[0],
                    std::map<int, std::string>({{1, dst_keys[0] + "part_1"},
                                                {2, dst_keys[0] + "part_2"}}),
                    ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(dst_keys[0]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, CreateMpuObject(dst_keys[1], "", ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[1]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[1],
                                  src_keys[1],
                                  0,
                                  kTosCopyObjectSizeLimit,
                                  dst_keys[1],
                                  1,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_1_part_1),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[1],
                                  src_keys[1],
                                  kTosCopyObjectSizeLimit,
                                  1,
                                  dst_keys[1],
                                  2,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_1_part_2),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CompleteObject(
                    dst_keys[1],
                    dst_keys[1],
                    std::map<int, std::string>({{1, dst_keys[1] + "part_1"},
                                                {2, dst_keys[1] + "part_2"}}),
                    ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(dst_keys[1]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, CreateMpuObject(dst_keys[2], "", ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[2]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[2],
                                  src_keys[2],
                                  0,
                                  kTosCopyObjectSizeLimit,
                                  dst_keys[2],
                                  1,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_2_part_1),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                UploadMpuPartCopy(dst_keys[2],
                                  src_keys[2],
                                  kTosCopyObjectSizeLimit,
                                  1,
                                  dst_keys[2],
                                  2,
                                  ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<6>(dst_2_part_2),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CompleteObject(
                    dst_keys[2],
                    dst_keys[2],
                    std::map<int, std::string>({{1, dst_keys[2] + "part_1"},
                                                {2, dst_keys[2] + "part_2"}}),
                    ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(dst_keys[2]),
                                   ::testing::Return(Status::OK())));

    EXPECT_TRUE(ufs_->CopyDirectory(src, dst, &res).IsOK());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 3);
    auto result_detail = copy_result.find(std::string("/") + src_keys[0]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[0]);
    result_detail = copy_result.find(std::string("/") + src_keys[1]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[1]);
    result_detail = copy_result.find(std::string("/") + src_keys[2]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[2]);
  }
}

TEST_F(TosUfsTestV2, TestCopyDirectoryShallowCopy) {
  FLAGS_ufs_tos_shallow_copy_enabled = true;
  std::string src = "/src";
  std::string src_key = "src";
  std::vector<std::string> src_keys = {"src/file1", "src/file2", "src/file3"};
  std::string dst = "/dst";
  std::string dst_key = "dst";
  std::vector<std::string> dst_keys = {"dst/file1", "dst/file2", "dst/file3"};

  {
    UfsCopyResult res;
    res.SetCopyTimeStamp(TimeUtilV2::GetNowEpochMs());
    TosListObjectsResult list_src_res;
    Aws::S3::Model::Object obj1;
    obj1.SetKey(src_keys[0]);
    obj1.SetSize(kTosCopyObjectSizeLimit + 1);
    obj1.SetETag(src_keys[0]);
    obj1.SetLastModified(Aws::Utils::DateTime(1717113600000L + 1000));
    list_src_res.AddContents(obj1);
    Aws::S3::Model::Object obj2;
    obj2.SetKey(src_keys[1]);
    obj2.SetSize(kTosCopyObjectSizeLimit + 1);
    obj2.SetETag(src_keys[1]);
    obj2.SetLastModified(Aws::Utils::DateTime(1717113600000L + 1000));
    list_src_res.AddContents(obj2);
    Aws::S3::Model::Object obj3;
    obj3.SetKey(src_keys[2]);
    obj3.SetSize(kTosCopyObjectSizeLimit + 1);
    obj3.SetETag(src_keys[2]);
    obj3.SetLastModified(Aws::Utils::DateTime(1717113600000L + 1000));
    list_src_res.AddContents(obj3);
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 2, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_, HeadObject(dst_key, ::testing::_))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(dst_key + "/", "", 1, ::testing::_, ""))
        .WillOnce(::testing::Return(Status(Code::kFileNotFound)));
    EXPECT_CALL(*tos_client_, ListObjects(src_key + "/", "", 1000, ::testing::_, ""))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<3>(list_src_res),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[0], dst_keys[0], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[0]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[1], dst_keys[1], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[1]),
                                   ::testing::Return(Status::OK())));
    EXPECT_CALL(*tos_client_,
                CopyObjectWithInBucket(src_keys[2], dst_keys[2], ::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgPointee<2>(dst_keys[2]),
                                   ::testing::Return(Status::OK())));
    EXPECT_TRUE(ufs_->CopyDirectory(src, dst, &res).IsOK());
    auto copy_result = res.ExtractResult();
    EXPECT_EQ(copy_result.size(), 3);
    auto result_detail = copy_result.find(std::string("/") + src_keys[0]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[0]);
    result_detail = copy_result.find(std::string("/") + src_keys[1]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[1]);
    result_detail = copy_result.find(std::string("/") + src_keys[2]);
    EXPECT_TRUE(result_detail != copy_result.end());
    EXPECT_EQ(result_detail->second, dst_keys[2]);
  }

  FLAGS_ufs_tos_shallow_copy_enabled = false;
}

}  // namespace dancenn