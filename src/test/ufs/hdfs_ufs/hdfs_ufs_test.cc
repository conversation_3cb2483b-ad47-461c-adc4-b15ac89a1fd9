#include "ufs/hdfs_ufs/hdfs_ufs.h"

#include <cstdlib>
#include <memory>

#include "block_info_proto.pb.h"
#include "gtest/gtest.h"
#include "inode.pb.h"
#include "ufs/hdfs/hdfs_client.h"
#include "ufs/ufs.h"
#include "ufs/ufs_config.h"
#include "ufs/ufs_file_status.h"

namespace dancenn {
class HdfsUfsTest : public testing::Test {
 public:
  void SetUp() override {
    GTEST_SKIP();

    config_.consul = "nnproxy";

    ufs_config_ = HdfsConfig::CreateUfsConfig(config_);
    hdfs_client_ = std::make_shared<HdfsClient>(config_);
    ufs_ = std::make_shared<HdfsUfs>(ufs_config_, config_, hdfs_client_);
    ufs_->Init();
  }

  void TearDown() override {
    ufs_.reset();
  }

  static void SetUpTestSuite() {
    setenv("CPP_HDFS_LOG_DIR", ".", 1);
    setenv("INFSEC_HADOOP_ENABLED", "1", 1);
  }

 protected:
  std::shared_ptr<HdfsUfs> ufs_;
  UfsConfig ufs_config_;
  HdfsConfig config_;
  std::shared_ptr<HdfsClient> hdfs_client_;
};

TEST_F(HdfsUfsTest, TestUfsFileStatusBuilder) {
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("//").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(!s.IsOK());
  }
  {
    UfsFileStatus f;
    Status s = UfsFileStatus::Builder()
                   .SetFullPath("/a")
                   .SetFileType(UFS_FILE)
                   .SetFileSize(1)
                   .SetEtag("abcdefg")
                   .Build(&f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("a", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/a/").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ("a", f.FileName());
  }
  {
    UfsFileStatus f;
    Status s =
        UfsFileStatus::Builder().SetFullPath("/a//").SetFileType(UFS_DIR).Build(
            &f);
    ASSERT_TRUE(!s.IsOK());
  }
}

TEST_F(HdfsUfsTest, TestGetStatusFile) {
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileSize(), 4);
    ASSERT_EQ(file_status.FileType(), UFS_FILE);
    ASSERT_EQ(file_status.BlockSize(), 4 * TosConstants::kTosBlockSize);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/not_existed");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "home/byte_dp_cnch_lf/dancenn/HdfsUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
}

// not allowed in hdfs
// TEST_F(HdfsUfsTest, TestGetStatusDirAndObjectNameConflict) {
//   {
//     UfsFileStatus file_status;
//     std::string path
//     ("/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/obj_name_conflict_with_dir");
//     auto s = ufs_->GetFileStatus(path, &file_status);
//     ASSERT_TRUE(s.IsOK());
//     ASSERT_EQ(file_status.FullPath(), path);
//     ASSERT_EQ(file_status.FileSize(), 4);
//     ASSERT_EQ(file_status.FileType(), UFS_FILE);
//     ASSERT_EQ(file_status.BlockSize(), 4 * TosConstants::kTosBlockSize);
//   }
// }

TEST_F(HdfsUfsTest, TestGetStatusDirObject) {
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/dir_object");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/dir_object/");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path.substr(0, path.size() - 1));
    ASSERT_EQ(file_status.FileType(), UFS_DIR);
  }
}

TEST_F(HdfsUfsTest, TestGetFileOnlyStatus) {
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetFileOnlyStatus/obj");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(file_status.FullPath(), path);
    ASSERT_EQ(file_status.FileSize(), 6);
    ASSERT_EQ(file_status.FileType(), UFS_FILE);
    ASSERT_EQ(file_status.BlockSize(), 4 * TosConstants::kTosBlockSize);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetFileOnlyStatus/"
        "not_existed");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetFileOnlyStatus/dir/");
    auto s = ufs_->GetFileOnlyStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kFileNotFound);
  }
  {
    UfsFileStatus file_status;
    std::string path(
        "home/byte_dp_cnch_lf/dancenn/HdfsUfsTest/TestGetFileOnlyStatus/obj");
    auto s = ufs_->GetFileStatus(path, &file_status);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
}

TEST_F(HdfsUfsTest, TestGetDirectoryStatus) {
  {
    UfsDirStatus dir;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/nonexisted");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_EQ(Code::kDirNotFound, s.code());
  }
  {
    UfsDirStatus dir;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/obj");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_EQ(Code::kDirNotFound, s.code());
  }
  {
    UfsDirStatus dir;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/dir_object");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_TRUE(s.IsOK());
    ASSERT_FALSE(dir.HasChildren());
  }
  {
    UfsDirStatus dir;
    std::string path(
        "/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/dir");
    auto s = ufs_->GetDirectoryStatus(path, &dir);
    ASSERT_TRUE(s.IsOK());
    ASSERT_TRUE(dir.HasChildren());
  }
  // {
  //   UfsDirStatus dir;
  //   std::string
  //   path("/home/<USER>/dancenn/HdfsUfsTest/TestGetStatus/obj_name_conflict_with_dir");
  //   auto s = ufs_->GetDirectoryStatus(path, &dir);
  //   ASSERT_TRUE(s.IsOK());
  //   ASSERT_TRUE(dir.HasChildren());
  // }
}

TEST_F(HdfsUfsTest, TestListFiles) {
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles("/", ListFilesOption(), &res);
    ASSERT_EQ(s.code(), Code::kOK);
    ASSERT_TRUE(res.files.size() > 0);
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles(
        "home/byte_dp_cnch_lf/dancenn/HdfsUfsTest/TestListFiles/dir/",
        ListFilesOption(),
        &res);
    ASSERT_EQ(s.code(), Code::kBadParameter);
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles(
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir",
        ListFilesOption(),
        &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), 100);
    ASSERT_EQ(res.has_more, false);
    ASSERT_EQ(res.continue_token, "");
    ASSERT_EQ(
        res.files[0].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-1");
    ASSERT_EQ(
        res.files[1].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-10");
    ASSERT_EQ(
        res.files[2].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-100");
    ASSERT_EQ(
        res.files[3].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-11");
    ASSERT_EQ(
        res.files[99].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-99");
  }
  {
    ListFilesResult res;
    auto s = ufs_->ListFiles(
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/",
        ListFilesOption(),
        &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), 100);
    ASSERT_EQ(res.has_more, false);
    ASSERT_EQ(res.continue_token, "");
    ASSERT_EQ(
        res.files[0].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-1");
    ASSERT_EQ(
        res.files[1].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-10");
    ASSERT_EQ(
        res.files[2].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-100");
    ASSERT_EQ(
        res.files[3].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-11");
    ASSERT_EQ(
        res.files[99].FullPath(),
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-99");
  }
}

TEST_F(HdfsUfsTest, TestListFilesPaging) {
  // Test paging
  // /home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/ has 100
  // children files with naming from obj-1 .. obj-100

  int page_size = 7;
  int total_count = 100;
  int page_count = (total_count / page_size) + 1;

  std::unordered_set<std::string> files;
  std::string continue_token;

  std::string last_file;
  for (int i = 0; i < page_count; ++i) {
    ListFilesResult res;
    ListFilesOption opt;
    opt.page_size = page_size;
    if (!continue_token.empty()) {
      opt.continue_token = continue_token;
    }
    size_t expected_count =
        (i == (page_count - 1)) ? (total_count - i * page_size) : page_size;

    auto s = ufs_->ListFiles(
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/",
        opt,
        &res);
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(res.files.size(), expected_count);
    for (auto&& f : res.files) {
      auto&& path = f.FullPath();
      files.insert(path);
      ASSERT_TRUE(last_file.compare(path) < 0);
      ASSERT_EQ(f.FileType(), UFS_FILE);
      last_file = path;
    }
    ASSERT_EQ(res.has_more, i < (page_count - 1));
    continue_token = res.continue_token;
  }
  ASSERT_EQ(files.size(), (size_t)total_count);
  for (int i = 1; i <= 100; ++i) {
    std::string path =
        "/home/<USER>/dancenn/HdfsUfsTest/TestListFiles/dir/obj-" +
        std::to_string(i);
    ASSERT_TRUE(files.find(path) != files.end());
  }
}

TEST_F(HdfsUfsTest, TestMkdirs) {
  // Create a non-existed dir
  {
    std::string src = "/home/<USER>/dancenn/tmp/mkdirs";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_TRUE(ufs_->GetDirectoryStatus(src, &info).IsOK());
    ASSERT_EQ(info.FullPath().compare(src), 0);
    ASSERT_FALSE(info.HasChildren());
  }
  // Recreate, overwrite
  {
    std::string src = "/home/<USER>/dancenn/tmp/mkdirs";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_TRUE(ufs_->GetDirectoryStatus(src, &info).IsOK());
    ASSERT_EQ(info.FullPath().compare(src), 0);
    ASSERT_FALSE(info.HasChildren());
  }
  // Inner Create failed undo
  {
    std::string src = "/home/<USER>/dancenn/tmp/mkdirs-fail";
    ASSERT_TRUE(ufs_->Mkdir(src).IsOK());
    ASSERT_TRUE(ufs_->AbortMkdir(src).IsOK());
    UfsDirStatus info;
    ASSERT_FALSE(ufs_->GetDirectoryStatus(src, &info).IsOK());
  }
}

TEST_F(HdfsUfsTest, TestCreateFile) {
  // Create a non-existed empty object without placeholder
  {
    std::string src =
        "/home/<USER>/dancenn/tmp/create/"
        "non-exists-without-placeholder";
    ufs_->DeleteFile(src);
    UfsFileInfoProto info;
    UfsCreateOption opt(true, false);
    ASSERT_TRUE(ufs_->Create(src, opt, &info).IsOK());

    UfsFileStatus info2;
    ASSERT_TRUE(ufs_->GetFileStatus(src, &info2).IsOK());
    ASSERT_EQ(src, info2.FullPath());
    ASSERT_EQ(info.size(), info2.FileSize());
  }

  // Create an existed empty object without placeholder
  {
    std::string src =
        "/home/<USER>/dancenn/tmp/create/exists-without-placeholder";
    ufs_->DeleteFile(src);
    UfsFileInfoProto info;
    UfsCreateOption opt(true, false);
    ASSERT_TRUE(ufs_->Create(src, opt, &info).IsOK());

    UfsFileStatus info2;
    ASSERT_TRUE(ufs_->GetFileStatus(src, &info2).IsOK());
    ASSERT_EQ(src, info2.FullPath());
    ASSERT_EQ(info.size(), info2.FileSize());
  }
}

TEST_F(HdfsUfsTest, TestGetBlockPufsName) {
  UfsFileInfoProto file_info;
  file_info.set_upload_id("test");
  BlockInfoProto block_info;
  block_info.set_part_num(2);
  const auto& ret = ufs_->GetBlockPufsName(file_info, block_info);
  ASSERT_EQ("/.TMP/upload/test/2", ret);
}

// TEST_F(HdfsUfsTest, TestReadFile) {
//   {
//     std::string res;
//     auto s = ufs_->ReadFile(
//         "/home/<USER>/weixiangwei/test0202.csv", 0, 4, &res);
//     ASSERT_TRUE(s.IsOK());
//     ASSERT_TRUE(res.length() == 4);
//   }
//   {
//     std::string res;
//     auto s = ufs_->ReadFile(
//         "/home/<USER>/weixiangwei/test0202.csv", 36193138, 1024,
//         &res);
//     ASSERT_TRUE(s.IsOK());
//     ASSERT_EQ(res.length(), 1);
//   }
// }

}  // namespace dancenn