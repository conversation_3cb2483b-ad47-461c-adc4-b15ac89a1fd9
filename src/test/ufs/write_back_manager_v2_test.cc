#include "ufs/upload/write_back_manager_v2.h"

#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "test/ufs/mock_ufs.h"
#include "test/ufs/mock_write_back_manager_v2.h"
#include "test/namespace/gmock_meta_storage.h"
#include "test/namespace/mock_meta_scanner_v2.h"
#include "test/namespace/mock_namespace.h"
#include "test/mock_ha_state.h"

DECLARE_bool(run_ut);

namespace dancenn {

class WriteBackTaskV2Test : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;

    ha_state_ = std::make_unique<MockHAState>();

    ns_ = std::make_shared<GMockNameSpace>();
    ns_->set_ha_state(ha_state_.get());
    ufs_ = std::make_shared<GMockUfs>();

    meta_storage_ = std::make_shared<GMockMetaStorage>();
    meta_scanner_ = std::make_shared<GMockMetaScannerV2>();
  }

  void TearDown() override {
    ns_.reset();
    ufs_.reset();

    meta_storage_.reset();
    meta_scanner_.reset();
  }

 public:
  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<GMockNameSpace> ns_;
  std::shared_ptr<GMockUfs> ufs_;

  std::shared_ptr<GMockMetaStorage> meta_storage_;
  std::shared_ptr<GMockMetaScannerV2> meta_scanner_;
};

TEST_F(WriteBackTaskV2Test, TestScanner) {
  std::shared_ptr<WriteBackScanner> scanner =
      std::make_shared<WriteBackScanner>(ns_, ufs_);

  scanner->SetMetaStorage(meta_storage_.get());
  scanner->SetMetaScanner(meta_scanner_.get());

  EXPECT_CALL(*meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(5250));

  ASSERT_TRUE(scanner->PreScan());

  INode inode;
  inode.set_id(1);
  inode.set_name("inode");
  inode.set_parent_id(0);
  auto permission = inode.mutable_permission();
  permission->set_username("user");
  permission->set_groupname("group");
  permission->set_permission(0755);
  inode.set_mtime(1);
  inode.set_atime(1);
  inode.set_type(INode_Type_kFile);
  auto ufs_file_info = inode.mutable_ufs_file_info();
  ufs_file_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
  ufs_file_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_file_info->set_etag("etag");
  ufs_file_info->set_size(0);
  ufs_file_info->set_last_modified_ts(1);
  ufs_file_info->set_sync_ts(1);

  std::string key = MetaStorage::EncodeINodeID(1);
  std::string value = inode.SerializeAsString();

  INode saved;
  EXPECT_CALL(*meta_scanner_, AddWorkTask(testing::_))
      .Times(2)
      .WillOnce(testing::DoAll(
          [&saved](std::shared_ptr<MetaScannerWorkTask> tmp) {
            saved = std::static_pointer_cast<WriteBackTaskV2>(tmp)->GetINode();
          },
          testing::Return(false)))
      .WillOnce(testing::Return(true));

  ASSERT_TRUE(scanner->Handle(nullptr, key, value));
  ASSERT_EQ(saved.SerializeAsString(), inode.SerializeAsString());

  ASSERT_TRUE(scanner->PostScan());
}

TEST_F(WriteBackTaskV2Test, TestStat) {
  std::shared_ptr<WriteBackStat> stat = std::make_shared<WriteBackStat>();
  ASSERT_TRUE(stat->PreScan());
  ASSERT_TRUE(stat->Handle(nullptr, "", ""));
  ASSERT_TRUE(stat->PostScan());
  ASSERT_EQ(stat->GetTaskCnt(), 1);
}

}  // namespace dancenn
