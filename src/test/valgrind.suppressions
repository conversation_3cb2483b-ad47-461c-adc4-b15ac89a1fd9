{
   dancenn-metrics-memory-leak
   Memcheck:Leak
   match-leak-kinds: reachable
   ...
   fun:_ZN7dancenn7Metrics20GetOrCreateHistogramERKNS_8MetricIDEjj
   ...
}
{
   vlog-memory-leak
   Memcheck:Leak
   match-leak-kinds: reachable
   ...
   fun:_ZN6google12SetVLOGLevelEPKci
   fun:main
}
{
   jvm-memory-leak
   Memcheck:Leak
   match-leak-kinds: reachable
   ...
   fun:_GLOBAL__sub_I_*.cpp
   ...
}
{
   tls-memory-leak
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   fun:allocate_dtv
   fun:_dl_allocate_tls
   fun:allocate_stack
   fun:pthread_create@@GLIBC_2.2.5
   fun:je_arrow_private_je_pthread_create_wrapper
   fun:background_thread_create_signals_masked
   fun:background_thread_create_locked
   fun:je_arrow_private_je_background_thread_create
   fun:malloc_init_hard
   fun:__libc_csu_init
   fun:(below main)
}
