// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/07
// Description

#ifndef TEST_SAFEMODE_GMOCK_SAFEMODE_H_
#define TEST_SAFEMODE_GMOCK_SAFEMODE_H_

#include <gmock/gmock.h>

#include <cstdint>
#include <string>

#include "base/status.h"
#include "ha/operations.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "safemode/safemode_base.h"

namespace dancenn {

class GMockSafeMode : public SafeModeBase {
 public:
  MOCK_METHOD0(Start, void());
  MOCK_METHOD0(Stop, void());

  MOCK_METHOD0(IsOn, bool());
  MOCK_METHOD0(IsStartingUp, bool());
  MOCK_METHOD0(Is<PERSON><PERSON>al, bool());
  MOCK_METHOD1(GetSafeModeTip, void(std::string* tip));

  MOCK_METHOD2(SetSafeMode,
               Status(cloudfs::SafeModeActionProto action,
                      OperationsCategory op));
  MOCK_METHOD1(SetBlockTotal, void(int64_t total));

  MOCK_METHOD2(AdjustSafeModeBlockTotals,
               void(int delta_safe, int delta_total));
  MOCK_METHOD1(IncrementSafeBlockCount, void(uint32_t replication));
  MOCK_METHOD1(DecrementSafeBlockCount, void(uint32_t replication));

  MOCK_CONST_METHOD0(last_enter_time, uint64_t());
  MOCK_CONST_METHOD0(last_leave_time, uint64_t());
};

}  // namespace dancenn

#endif  // TEST_SAFEMODE_GMOCK_SAFEMODE_H_
