//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <chrono>
#include <thread>

#include "base/vlock.h"
#include "base/file_utils.h"
#include "safemode/safemode.h"
#include "ha/ha_state.h"
#include "test/datanode_manager/gmock_datanode_manager.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_ha_state.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(namenode_ha_enabled);
DECLARE_double(safemode_threshold_pct);
DECLARE_int32(safemode_extension_ms);
DECLARE_int64(safemode_safe_check_interval_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_int32(namespace_type);

#define WAIT_UNTIL(cond, duration_in_ms)                                       \
  {                                                                            \
    auto end = std::chrono::system_clock::now() +                              \
               std::chrono::milliseconds(duration_in_ms);                      \
    while (!(cond) && std::chrono::system_clock::now() < end) {                \
      std::this_thread::sleep_for(std::chrono::milliseconds(10));              \
    }                                                                          \
  }

namespace dancenn {

class SafeModeTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_safemode_threshold_pct = 0.999;
    FLAGS_safemode_extension_ms = 100;
    FLAGS_safemode_safe_check_interval_ms = 10;
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    edit_log_ctx_ = std::make_shared<MockEditLogContext>();
    barrier_ = std::make_shared<dancenn::VRWLock>(1);
    ha_state_ = std::make_shared<MockHAState>();
    safemode_ = std::make_shared<SafeMode>(edit_log_ctx_, barrier_);
    safemode_->set_ha_state(ha_state_.get());
    // namespace
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    datanode_manager_ = std::make_shared<dancenn::GMockDatanodeManager>();
    block_manager_.reset(new BlockManager());
    block_manager_->set_datanode_manager(datanode_manager_);
    MockFSImageTransfer(db_path_).Transfer();
    ns_ = std::make_shared<MockNameSpace>(db_path_,
                                          edit_log_ctx_,
                                          block_manager_,
                                          datanode_manager_,
                                          std::make_shared<DataCenters>(),
                                          UfsEnv::Create());
    ns_->set_ha_state(ha_state_.get());
    ns_->set_safemode(safemode_.get());
    safemode_->set_ns(ns_.get());
  }

  void TearDown() override {
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  static int32_t GetNumLiveDataNodes() { return 1000; }

  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<GMockDatanodeManager> datanode_manager_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::shared_ptr<dancenn::VRWLock> barrier_;
  std::shared_ptr<MockHAState> ha_state_;
  std::shared_ptr<SafeMode> safemode_;
};

TEST_F(SafeModeTest, Startup) {
  uint32_t block_total = 10000;
  auto replica = FLAGS_dfs_replication_min;
  int32_t extension = FLAGS_safemode_extension_ms;
  auto threshold = static_cast<uint64_t >(block_total
      * FLAGS_safemode_threshold_pct);
  std::string tip;

  BriefStorageStat stat_fail;
  stat_fail.num_total_datanode = 0;
  stat_fail.num_normal_datanode = 0;
  stat_fail.num_live_datanode = 0;
  BriefStorageStat stat_fail2;
  stat_fail2.num_total_datanode = 3;
  stat_fail2.num_normal_datanode = 3;
  stat_fail2.num_live_datanode = 3;
  stat_fail2.num_content_stale_datanode = 2;
  BriefStorageStat stat_success;
  stat_success.num_total_datanode = 3;
  stat_success.num_normal_datanode = 3;
  stat_success.num_live_datanode = 3;
  stat_success.num_content_stale_datanode = 1;
  EXPECT_CALL(*datanode_manager_, GetBriefStorageStat())
      .WillOnce(testing::Return(stat_fail))
      .WillOnce(testing::Return(stat_fail))
      .WillOnce(testing::Return(stat_fail2))
      .WillOnce(testing::Return(stat_fail2))
      .WillOnce(testing::Return(stat_fail2))
      .WillRepeatedly(testing::Return(stat_success));
  EXPECT_CALL(*datanode_manager_, NumLiveDatanodes())
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(0))
      .WillRepeatedly(testing::Return(3));

  // this test case is a trick, depend on FLAGS_safemode_extension_ms
  safemode_->SetBlockTotal(block_total);
  safemode_->Start();
  WAIT_UNTIL(safemode_->IsOn(), 1000);
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsStartingUp());
  ASSERT_FALSE(safemode_->IsManual());

  size_t i = 0;
  for (; i < threshold - 1; i++) {
    safemode_->IncrementSafeBlockCount(replica);
  }
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsStartingUp());
  ASSERT_FALSE(safemode_->IsManual());
  safemode_->GetSafeModeTip(&tip);
  LOG(INFO) << "SafeModeTip: " << tip;
  std::this_thread::sleep_for(std::chrono::milliseconds(extension + 2000));
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsStartingUp());
  ASSERT_FALSE(safemode_->IsManual());

  for (; i < threshold; i++) {
    safemode_->IncrementSafeBlockCount(replica);
  }
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsStartingUp());
  ASSERT_FALSE(safemode_->IsManual());
  std::this_thread::sleep_for(std::chrono::milliseconds(
        FLAGS_safemode_safe_check_interval_ms * 3));
  tip="";
  safemode_->GetSafeModeTip(&tip);
  LOG(INFO) << "SafeModeTip: " << tip;
  std::this_thread::sleep_for(std::chrono::milliseconds(extension + 2000));
  ASSERT_FALSE(safemode_->IsOn());
  ASSERT_FALSE(safemode_->IsStartingUp());
  ASSERT_FALSE(safemode_->IsManual());

  safemode_->SetSafeMode(cloudfs::SafeModeActionProto::SAFEMODE_ENTER,
                          OperationsCategory::kWrite);
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsManual());
  ASSERT_FALSE(safemode_->IsStartingUp());
  tip="";
  safemode_->GetSafeModeTip(&tip);
  LOG(INFO) << "SafeModeTip: " << tip;
  std::this_thread::sleep_for(std::chrono::milliseconds(extension + 2000));
  ASSERT_TRUE(safemode_->IsOn());
  ASSERT_TRUE(safemode_->IsManual());
  ASSERT_FALSE(safemode_->IsStartingUp());

  LOG(INFO)<<"test leave safemode manually.";
  safemode_->SetSafeMode(cloudfs::SafeModeActionProto::SAFEMODE_LEAVE,
                          OperationsCategory::kWrite);
  ASSERT_FALSE(safemode_->IsOn());
  ASSERT_FALSE(safemode_->IsManual());
  ASSERT_FALSE(safemode_->IsStartingUp());
}

}  // namespace dancenn

