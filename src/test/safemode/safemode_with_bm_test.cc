// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/06
// Description

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <memory>
#include <string>

#include "base/file_utils.h"
#include "base/vlock.h"
#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_map_slice.h"
#include "datanode_manager/datanode_info.h"
#include "edit/edit_log_context.h"
#include "ha/ha_state.h"
#include "namespace/meta_storage.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "safemode/safemode.h"
#include "test/datanode_manager/gmock_datanode_manager.h"
#include "test/mock_ha_state.h"
#include "test/safemode/gmock_safemode.h"

DECLARE_uint32(dfs_replication_min);

using testing::_;

namespace dancenn {

class SafeModeWithBMTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_dfs_replication_min = 1;
    ha_state_ = std::make_unique<MockHAState>();
    safemode_.reset(new GMockSafeMode());

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_storage_.reset(new MetaStorage(db_path_));
    meta_storage_->Launch();
    meta_storage_->StartStandby();
    meta_storage_->StartActive();
    gmock_dn_manager_ = new GMockDatanodeManager();
    dn_manager_.reset(gmock_dn_manager_);
    bm_.reset(new BlockManager(std::shared_ptr<EditLogContextBase>()));
    bm_->SetMetaStorage(meta_storage_);
    bm_->set_datanode_manager(dn_manager_);

    block_ = Block(1, 0, 0);
    bp_.set_blockid(1);
    bp_.set_genstamp(0);
    bp_.set_numbytes(0);
    bip_.set_state(BlockInfoProto::kUnderConstruction);
    bip_.set_block_id(1);
    bip_.set_gen_stamp(0);
    bip_.set_num_bytes(0);
    bip_.set_inode_id(0);
    bip_.set_expected_rep(3);

    meta_storage_->TestOnlyPutBlockInfo(bip_);
    bm_->LoadLocalBlocks();
    bm_->set_ha_state(ha_state_.get());
    bm_->set_safemode(safemode_.get());
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::unique_ptr<BlockMapSlice>& slice(BlockID blk_id) {
    return bm_->slice(blk_id);
  }

 protected:
  std::unique_ptr<MockHAState> ha_state_;
  std::unique_ptr<GMockSafeMode> safemode_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MetaStorage> meta_storage_;
  GMockDatanodeManager* gmock_dn_manager_;
  std::shared_ptr<DatanodeManager> dn_manager_;
  std::shared_ptr<BlockManager> bm_;

  Block block_;
  BlockProto bp_;
  BlockInfoProto bip_;
};

TEST_F(SafeModeWithBMTest, BlockNumAfterStartUp) {
  EXPECT_EQ(bm_->GetBlockNum(), 1);
}

TEST_F(SafeModeWithBMTest, BlockTurnFromUCToComplete) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 1)).Times(1);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_, true));
}

TEST_F(SafeModeWithBMTest, BlockTurnFromCommittedToPersisted) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(0);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(_, _)).Times(0);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  EXPECT_TRUE(bm_->PersistBlock(block_));
}

TEST_F(SafeModeWithBMTest, BlockTurnFromCompleteToPersisted01) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 1)).Times(1);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_, true));
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, -1)).Times(1);
  EXPECT_TRUE(bm_->PersistBlock(block_));
}

TEST_F(SafeModeWithBMTest, BlockTurnFromCompleteToPersisted02) {
  EXPECT_CALL(*gmock_dn_manager_, GetDatanodeFromId(0))
      .Times(1)
      .WillOnce(testing::Invoke([](DatanodeID dn_id) {
        return new DatanodeInfo(
            dn_id, cloudfs::DatanodeIDProto(), cnetpp::base::IPAddress());
      }));
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(-1, -1)).Times(1);
  auto& s = slice(1);
  BlockInfoGuard bi_guard(s.get(), 1, true);
  auto bi = bi_guard.GetBlockInfo();
  EXPECT_NE(bi, nullptr);
  EXPECT_TRUE(bi->AddStorage(0));
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  EXPECT_TRUE(bm_->PersistBlock(block_));
}

TEST_F(SafeModeWithBMTest, BlockTurnFromCommittedToUC) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(0);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(_, _)).Times(0);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  INode inode;
  inode.set_id(kInvalidINodeId);
  bm_->ConvertBlockToUnderConstruction("", bp_, inode);
}

TEST_F(SafeModeWithBMTest, BlockTurnFromCompleteToUC) {
  EXPECT_CALL(*gmock_dn_manager_, GetDatanodeFromId(0))
      // .Times(1)
      .WillRepeatedly(testing::Invoke([](DatanodeID dn_id) {
        return new DatanodeInfo(
            dn_id, cloudfs::DatanodeIDProto(), cnetpp::base::IPAddress());
      }));
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 1)).Times(1);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(-1, -1)).Times(1);
  auto& s = slice(1);
  BlockInfoGuard bi_guard(s.get(), 1, true);
  auto bi = bi_guard.GetBlockInfo();
  EXPECT_NE(bi, nullptr);
  EXPECT_TRUE(bi->AddStorage(0));
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  cloudfs::BlockProto bp;
  bp.set_blockid(1);
  bp.set_genstamp(0);
  bp.set_numbytes(0);
  INode inode;
  inode.set_id(kInvalidINodeId);
  bm_->ConvertBlockToUnderConstruction("", bp, inode);
}

TEST_F(SafeModeWithBMTest, BlockTurnFromPersistedToUC) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(_)).Times(0);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(_, _)).Times(0);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  EXPECT_TRUE(bm_->PersistBlock(block_));
  INode inode;
  inode.set_id(kInvalidINodeId);
  bm_->ConvertBlockToUnderConstruction("", bp_, inode);
}

TEST_F(SafeModeWithBMTest, BlockTurnFromUCToPersistedFailed) {
  EXPECT_CALL(*safemode_, IncrementSafeBlockCount(_)).Times(0);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(_, _)).Times(0);
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(_, _)).Times(0);
  EXPECT_FALSE(bm_->PersistBlock(block_));
}

TEST_F(SafeModeWithBMTest, RemoveNonPersistedBlock) {
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 0)).Times(1);
  EXPECT_EQ(bm_->GetBlockNum(), 1);
  bm_->RemoveBlocksAndUpdateSafeMode({bp_});
  EXPECT_EQ(bm_->GetBlockNum(), 0);
}

TEST_F(SafeModeWithBMTest, RemovePersistedBlock) {
  EXPECT_CALL(*safemode_, AdjustSafeModeBlockTotals(0, 0)).Times(1);
  EXPECT_EQ(bm_->GetBlockNum(), 1);
  EXPECT_TRUE(bm_->CommitOrCompleteOrPersistLastBlock(block_));
  EXPECT_EQ(bm_->GetBlockNum(), 1);
  EXPECT_TRUE(bm_->PersistBlock(block_));
  EXPECT_EQ(bm_->GetBlockNum(), 0);
  bm_->RemoveBlocksAndUpdateSafeMode({bp_});
  EXPECT_EQ(bm_->GetBlockNum(), 0);
}

}  // namespace dancenn
