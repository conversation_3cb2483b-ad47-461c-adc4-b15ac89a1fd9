#!/usr/bin/python
# coding=utf-8

# ====================
# setup local dir
# ====================
# dfs.namenode.edits.dir = /tmp/dancenn/nndata
# dfs.journalnode.edits.dir = /tmp/dancenn/qjm/journal
import os
os.system("mkdir -p /tmp/dancenn/nndata/current")
os.system("mkdir -p /tmp/dancenn/qjm/journal")

# ====================
# setup zk
# ====================
# dfs.namenode.shared.edits.dir = bookkeeper://************:2181/bk_nn_dancenn

ZOOKEEPER_HOST = "************:2181"
BOOKKEEPER_HOST = "************:3181"
ZOOKEEPER_ROOT_PATH = "bk_nn_dancenn"
TEST_MACHINE_HOSTNAME = "n8-131-216.byted.org"
TEST_MACHINE_IP_ADDR = "************"

from kazoo.client import KazooClient
zk = KazooClient(hosts=ZOOKEEPER_HOST)
zk.start()

zk.create(ZOOKEEPER_ROOT_PATH, "")
zk.create("/%s/ledgers" % ZOOKEEPER_ROOT_PATH, "0")

inprogress = """path: "/%s/ledgers/inprogress_0"
hostname: "%s/%s"
""" % (ZOOKEEPER_ROOT_PATH, TEST_MACHINE_HOSTNAME, TEST_MACHINE_IP_ADDR)
zk.create("/%s/CurrentInprogress" % ZOOKEEPER_ROOT_PATH, inprogress)

version = """layoutVersion: -1
namespaceInfo {
  buildVersion: "Unknown"
  unused: 0
  blockPoolID: ""
  storageInfo {
    layoutVersion: 1234567890
    namespceID: 0
    clusterID: ""
    cTime: 0
  }
  softwareVersion: "2.6.0-cdh5.4.4-bd1"
}
"""
zk.create("/%s/version" % ZOOKEEPER_ROOT_PATH, version)

maxtxid = """txId: 0
"""
zk.create("/%s/maxtxid" % ZOOKEEPER_ROOT_PATH, maxtxid)

