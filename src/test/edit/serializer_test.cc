#include <gtest/gtest.h>

#include <string>
#include <sstream>

#include "edit/serializer.h"
#include "edit/op/op_set_xattr.h"
#include "edit/op/op_mkdir.h"
#include "namespace/permission.h"

namespace dancenn {

TEST(EditLogSerializer, Test00) {
  std::stringstream ss;
  uint16_t v16 = 9;
  uint32_t v32 = 10;
  uint64_t v64 = 11;
  OpCode mkdir = OP_MKDIR;
  std::string vs = "hello world";
  WriteField(&mkdir, &ss);
  ASSERT_EQ(1, ss.str().size());
  WriteField(&v64, &ss);
  ASSERT_EQ(9, ss.str().size());
  WriteField(&v32, &ss);
  ASSERT_EQ(13, ss.str().size());
  WriteField(&v16, &ss);
  ASSERT_EQ(15, ss.str().size());
  WriteField(&vs, &ss);
  ASSERT_EQ(17 + vs.size(), ss.str().size());

  vs = "";
  std::string serialized = ss.str();
  std::stringstream is(serialized);
  ReadField(&mkdir, &is);
  ASSERT_EQ(OP_MKDIR, mkdir);
  ReadField(&v64, &is);
  ASSERT_EQ(11, v64);
  ReadField(&v32, &is);
  ASSERT_EQ(10, v32);
  ReadField(&v16, &is);
  ASSERT_EQ(9, v16);
  ReadField(&vs, &is);
  ASSERT_EQ("hello world", vs);
}

TEST(EditLogSerializer, OpSetXAttr) {
  ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs;
  auto xattr = xattrs.Add();
  xattr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name("trusted.hdfs.bytecool.temperature.type");
  xattr->set_value("WARM");
  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890911";

  std::stringstream ss;
  {
    OpSetXattr op = OpSetXattr();
    op.SetOpCode(OP_SET_XATTR);
    cloudfs::XAttrEditLogProto edit_xattr;
    edit_xattr.set_src(src);
    edit_xattr.mutable_xattrs()->CopyFrom(xattrs);
    op.set_xAttrs(edit_xattr);
    op.set_clientId("");
    op.set_callId(-2);
    op.WriteFields(&ss);
  }

  std::stringstream is(ss.str());
  OpSetXattr op;
  int8_t op_code_r;
  ReadField(&op_code_r, &ss);
  ASSERT_EQ(OP_SET_XATTR, op_code_r);
  int64_t tx_id_r;
  ReadField(&tx_id_r, &ss);
  op.ReadFields(&ss);
  ASSERT_EQ(src, op.xAttrs().src());
  ASSERT_EQ(1, op.xAttrs().xattrs_size());
  auto attr = op.xAttrs().xattrs(0);
  ASSERT_EQ("trusted.hdfs.bytecool.temperature.type", attr.name());
  ASSERT_EQ("WARM", attr.value());
  ASSERT_EQ(::cloudfs::XAttrProto_XAttrNamespaceProto_USER, attr.namespace_());
}

TEST(EditLogSerializer, OpMkDir) {
  uint64_t inode_id = 12345;
  std::string path = "/test_mkdir/a/b/c";
  uint64_t timestamp = 1234567;
  std::string user_name = "root";
  std::string group_name = "god";
  uint16_t permission = FsPermission::GetFileDefault().ToShort();

  PermissionStatus ps;
  ps.set_username(user_name);
  ps.set_groupname(group_name);
  ps.set_permission(permission);

  std::string acl_entry_name = "test";
  auto acl_entries = std::vector<cloudfs::AclEntryProto>();
  cloudfs::AclEntryProto entry;
  entry.set_type(cloudfs::AclEntryProto_AclEntryTypeProto_USER);
  entry.set_scope(cloudfs::AclEntryProto_AclEntryScopeProto_DEFAULT);
  entry.set_permissions(cloudfs::AclEntryProto_FsActionProto_PERM_ALL);
  entry.set_name(acl_entry_name);
  acl_entries.push_back(entry);

  std::string xattrs_src = "user";
  std::string xattrs_attr_name = "test";
  std::string xattrs_attr_value = "haha";
  cloudfs::XAttrEditLogProto xattrs;
  xattrs.set_src(xattrs_src);
  cloudfs::XAttrProto *xattr = xattrs.add_xattrs();
  xattr->set_namespace_(cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name(xattrs_attr_name);
  xattr->set_value(xattrs_attr_value);

  OpMkdir op;
  op.SetOpCode(OP_MKDIR);
  op.SetTxid(77889900);
  op.set_inodeId(inode_id);
  op.set_path(path);
  op.set_atime(timestamp);
  op.set_mtime(timestamp);
  op.set_permissions(ps);
  op.set_aclEntries(acl_entries);
  op.set_xAttrs(xattrs);

  std::stringstream ss;
  op.WriteFields(&ss);
  ASSERT_GT(ss.str().size(), 0);

  OpMkdir op_de;
  int8_t op_code_r;
  ReadField(&op_code_r, &ss);
  int64_t tx_id_r;
  ReadField(&tx_id_r, &ss);
  op_de.ReadFields(&ss);
  ASSERT_EQ(op_code_r, OP_MKDIR);
  ASSERT_EQ(tx_id_r, 77889900);
  ASSERT_EQ(op_de.inodeId(), inode_id);
  ASSERT_EQ(op_de.path(), path);
  ASSERT_EQ(op_de.atime(), timestamp);
  ASSERT_EQ(op_de.mtime(), timestamp);
  ASSERT_EQ(op_de.permissions().username(), user_name);
  ASSERT_EQ(op_de.permissions().groupname(), group_name);
  ASSERT_EQ(op_de.permissions().permission(),
            FsPermission::GetFileDefault().ToShort());

  auto aclentries_de = op_de.aclEntries();
  ASSERT_EQ(aclentries_de.size(), 1);
  ASSERT_EQ(aclentries_de[0].type(),
            cloudfs::AclEntryProto_AclEntryTypeProto_USER);
  ASSERT_EQ(aclentries_de[0].scope(),
            cloudfs::AclEntryProto_AclEntryScopeProto_DEFAULT);
  ASSERT_EQ(aclentries_de[0].permissions(),
            cloudfs::AclEntryProto_FsActionProto_PERM_ALL);
  ASSERT_EQ(aclentries_de[0].name(), acl_entry_name);

  ASSERT_EQ(op_de.xAttrs().src(), xattrs_src);
  auto xattrs_de = op_de.xAttrs().xattrs();
  ASSERT_EQ(xattrs_de.size(), 1);
  ASSERT_EQ(xattrs_de.Get(0).namespace_(),
            cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  ASSERT_EQ(xattrs_de.Get(0).name(), xattrs_attr_name);
  ASSERT_EQ(xattrs_de.Get(0).value(), xattrs_attr_value);
}

}  // namespace dancenn
