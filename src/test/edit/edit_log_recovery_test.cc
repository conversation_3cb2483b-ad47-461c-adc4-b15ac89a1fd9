//
// Copyright (c) 2018 Bytedance Inc. All rights reserved.
// Author: ranpanfeng <<EMAIL>>
//

#include <gtest/gtest.h>

#include <memory>

#include "edit/deserializer.h"
#include "edit/edit_log_context.h"
#include "edit/sender.h"
#include "edit/sender_base.h"
#include "edit/tailer.h"
#include "test/dancenn_test_base.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"
#include "ufs/tos/tos_cred_keeper.h"

namespace dancenn {

class EditLogRecoveryTest : public dancenn::test::DanceNNCommonTest {
 public:
  void SetUp() override {
    dancenn::test::DanceNNCommonTest::SetUp();
    safemode_ = std::make_shared<MockSafeMode>();
    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    block_manager_->set_datanode_manager(datanode_manager_);
    block_manager_->set_safemode(safemode_.get());
    auto job_manager = std::make_shared<dancenn::JobManager>(datanode_manager_,
                                                             block_manager_);
    auto base = GetBase();
    auto db_path = base->GetDBDir();
    auto ctx = base->GetEditLogContext();
    auto meta_storage = base->GetMetaStorage();
    meta_storage->Shutdown();
    MockFSImageTransfer(db_path).Transfer();
    ns_ = std::make_shared<NameSpace>(db_path,
                                      ctx,
                                      block_manager_,
                                      datanode_manager_,
                                      job_manager,
                                      std::make_shared<DataCenters>(),
                                      nullptr,
                                      nullptr);
    ns_->StartStandby();
  }

  void TearDown() override {
    ns_->StopStandby();
    block_manager_.reset();
    datanode_manager_.reset();
    ns_.reset();
    safemode_.reset();
    dancenn::test::DanceNNCommonTest::TearDown();
  }

  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<SafeModeBase> safemode_;
};

TEST_F(EditLogRecoveryTest, SyncAfterClose) {
  OpenCloseSync();
}

TEST_F(EditLogRecoveryTest, AppendAfterAppendNoSync) {
  OpenAppendRollClose(10);
  OpenAppendRollClose(11);
}

TEST_F(EditLogRecoveryTest, AppendAfterAppend) {
  OpenAppendSyncRollClose(10);
  OpenAppendSyncRollClose(11);
}

TEST_F(EditLogRecoveryTest, ReplayAfterAppendNoSync) {
  OpenAppendRollClose(12);
  OpenReplayClose();
  OpenAppendRollClose(20);
  OpenAppendRollClose(32);
}

TEST_F(EditLogRecoveryTest, ReplayAfterAppend) {
  OpenAppendSyncRollClose(12);
  OpenReplayClose();
  OpenAppendSyncRollClose(20);
  OpenAppendSyncRollClose(32);
}

TEST_F(EditLogRecoveryTest, InitJournalsForWriteThenReadNoSync) {
  OpenAppendRollClose(13);
  auto base = GetBase();
  base->InitJournalsForWrite()->Replay(1)->Close();
}

TEST_F(EditLogRecoveryTest, InitJournalsForWriteThenRead) {
  OpenAppendSyncRollClose(13);
  auto base = GetBase();
  base->InitJournalsForWrite()->Replay(1)->Close();
}

TEST_F(EditLogRecoveryTest, InitJournalsForWriteThenReadFinallyAppendNoSync) {
  OpenAppendSyncRollClose(13);
  auto base = GetBase();
  base -> InitJournalsForWrite()
       -> Replay(1)
       -> OpenForWrite()
       -> Append(14);
  sleep(1);
  base -> Close()
       -> OpenForRead()
       -> Replay(1)
       -> Close();
}

TEST_F(EditLogRecoveryTest, InitJournalsForWriteThenReadFinallyAppend) {
  OpenAppendSyncRollClose(13);
  auto base = GetBase();
  base -> InitJournalsForWrite()
       -> Replay(1)
       -> OpenForWrite()
       -> Append(14);
  sleep(1);
  base -> Sync()
       -> Close()
       -> OpenForRead()
       -> Replay(1)
       -> Close();
}

TEST_F(EditLogRecoveryTest, ManyOpenForRead) {
  OpenAppendSyncRollClose(5);
  GetBase() -> OpenForRead()
            -> OpenForRead()
            -> OpenForRead()
            -> Replay(1);
}

TEST_F(EditLogRecoveryTest, OpenForReadThenInitJournalsForWrite) {
  GetBase()->OpenForRead()->Replay(1)->Close()->InitJournalsForWrite();
}

TEST_F(EditLogRecoveryTest, InitJournalsForWriteOnEmptyEditLogDir) {
  GetBase()->InitJournalsForWrite();
}

TEST_F(EditLogRecoveryTest, ReplayInProgressNoSync) {
  auto base = GetBase();
  auto replayer_base = base->CreateCompanionBase("replayer");
  auto replay_in_progress_txns = [&]() {
    std::thread thd([&] {
      DEFER([&] { GetJvm()->DetachCurrentThread(); });
      replayer_base->SetUp();
      replayer_base->OpenForRead()->Replay(1);
      replayer_base->TearDown();
    });
    thd.join();
  };

  base->InitJournalsForWrite()->OpenForWrite()->Append(10)->Sync();
  replay_in_progress_txns();

  base->Append(11)->Sync();
  replay_in_progress_txns();

  base->Append(12)->Roll();
  sleep(1);
  base->Close();
  replay_in_progress_txns();
}

TEST_F(EditLogRecoveryTest, ReplayInProgress) {
  auto base = GetBase();
  auto replayer_base = base->CreateCompanionBase("replayer");
  auto replay_in_progress_txns = [&]() {
    std::thread thd([&] {
      DEFER([&] { GetJvm()->DetachCurrentThread(); });
      replayer_base->SetUp();
      replayer_base->OpenForRead()->Replay(1);
      replayer_base->TearDown();
    });
    thd.join();
  };

  base->InitJournalsForWrite()->OpenForWrite()->Append(10)->Sync();
  replay_in_progress_txns();

  base->Append(11)->Sync();
  replay_in_progress_txns();

  base->Append(12)->Sync()->Roll();
  sleep(1);
  base->Close();
  replay_in_progress_txns();
}

TEST_F(EditLogRecoveryTest, CatchUpFinalizedLogsOnFailOverNoSync) {
  OpenAppendSyncRollClose(11);
  auto base = GetBase();
  auto tailer = std::make_shared<dancenn::EditLogTailer>(
    0,
    base->GetJVM(),
    base->GetEditLogContext(),
    ns_.get());
  tailer->Start();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  tailer->Stop();
  base->Verify();

  base->InitJournalsForWrite();
  tailer->CatchupDuringFailover();
  base->GetEditLogContext()->SetNextTxId(ns_->GetLastCkptTxId() + 1);
  auto t = base->OpenForWrite()->Append(12);
  sleep(1);
  t->Close();
  base->OpenForRead()->Replay(12)->Close();
}

TEST_F(EditLogRecoveryTest, CatchUpFinalizedLogsOnFailOver) {
  OpenAppendSyncRollClose(11);
  auto base = GetBase();
  auto tailer = std::make_shared<dancenn::EditLogTailer>(
    0,
    base->GetJVM(),
    base->GetEditLogContext(),
    ns_.get());
  tailer->Start();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  tailer->Stop();
  base->Verify();

  base->InitJournalsForWrite();
  tailer->CatchupDuringFailover();
  base->GetEditLogContext()->SetNextTxId(ns_->GetLastCkptTxId() + 1);
  auto t = base->OpenForWrite()->Append(12);
  sleep(1);
  t->Sync()->Close();
  base->OpenForRead()->Replay(12)->Close();
}

TEST_F(EditLogRecoveryTest, TransitionToActive) {
  auto base = GetBase();
  auto tailer_base = base->CreateCompanionBase("tailer");

  tailer_base->SetUp();
  DEFER([&] { tailer_base->TearDown(); });

  auto tailer =
    std::make_shared<dancenn::EditLogTailer>(
      0,
      tailer_base->GetJVM(),
      tailer_base->GetEditLogContext(),
      ns_.get());

  tailer->Start();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  tailer->Stop();

  tailer_base->InitJournalsForWrite();
  tailer->CatchupDuringFailover();

  tailer_base->GetEditLogContext()->SetNextTxId(tailer_base->Txid() + 1);
  auto t = tailer_base->OpenForWrite()->Append(13);
  // aysnc sync may led notify after roll close,
  // for testcase
  sleep(1);
  t->Close();
  tailer_base->OpenForRead()->Replay(1)->Close();
}

}  // namespace dancenn


