// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/19
// Description: Test the following items for every cfs op:
// 1. INode
// 2. INodeStat
// 3. BlockInfoProto & BlockInfo
// 4. RetryCache

#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include <memory>
#include <string>

#include "base/closure.h"
#include "base/constants.h"
#include "base/file_utils.h"
#include "base/logger_metrics.h"
#include "base/platform.h"
#include "base/retry_cache.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/data_centers.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/edit_log_cfs_op.h"
#include "ha/ha_state_base.h"
#include "namespace/namespace.h"
#include "namespace/namespace_metrics.h"
#include "namespace/namespace_scrub.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "proto/generated/dancenn/edit_log.pb.h"
#include "proto/generated/dancenn/inode.pb.h"
#include "safemode/safemode_base.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(dancenn_observe_mode_on);
DECLARE_bool(retry_cache_enabled);
DECLARE_bool(recycle_bin_enable);
DECLARE_int32(namespace_type);
DECLARE_uint32(edit_log_assigner_apply_mode);

namespace dancenn {

class ApplyCfsOpTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_dancenn_observe_mode_on = true;
    FLAGS_retry_cache_enabled = true;
    FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

    datanode_manager_ = std::make_shared<DatanodeManager>();
    block_manager_ = std::make_unique<BlockManager>();
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    MockFSImageTransfer(db_path_).Transfer();
    std::shared_ptr<EditLogContextBase> edit_log_ctx =
        std::static_pointer_cast<EditLogContextBase>(
            std::make_shared<MockEditLogContext>());
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                nullptr,
                                &retry_cache_));
    meta_storage_ = ns_->TestOnlyGetMetaStorage();
    ha_state_ = std::make_unique<MockHAState>();
    safe_mode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safe_mode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safe_mode_.get());
    block_manager_->set_ns(ns_.get());
    block_manager_->SetMetaStorage(meta_storage_);
    block_manager_->set_datanode_manager(datanode_manager_);
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->StartStandby();

    path_ = "/spark/inprogress";

    spark_inode_.set_id(kLastReservedINodeId + 1);
    spark_inode_.set_parent_id(kRootINodeId);
    spark_inode_.set_name("spark");
    auto& spark_permission = *spark_inode_.mutable_permission();
    spark_permission.set_username("tiger");
    spark_permission.set_groupname("root");
    spark_permission.set_permission(600);
    spark_inode_.set_type(INode::kDirectory);
    spark_inode_.set_mtime(1642649452000);
    spark_inode_.set_atime(1642649453000);

    inprogress_inode_.set_id(kLastReservedINodeId + 2);
    inprogress_inode_.set_parent_id(spark_inode_.id());
    inprogress_inode_.set_name("inprogress");
    auto& inode_permission = *inprogress_inode_.mutable_permission();
    inode_permission.set_username("root");
    inode_permission.set_groupname("tiger");
    inode_permission.set_permission(777);
    inprogress_inode_.set_type(INode::kFile);
    inprogress_inode_.set_mtime(1642649454000);
    inprogress_inode_.set_atime(1642649455000);
    inprogress_inode_.mutable_uc()->set_client_name("MapReduce");
    inprogress_inode_.set_status(INode::kFileUnderConstruction);
    PinStatus* pin_status = inprogress_inode_.mutable_pin_status();
    pin_status->set_pinned(false);
    pin_status->set_ttl(-1);
    pin_status->set_txid(0);
    pin_status->set_recursive(false);

    log_rpc_info_.set_rpc_client_id("xxx.xxx.xxx.xxx.");
    log_rpc_info_.set_rpc_call_id(123);

    bip_1st_.set_state(BlockInfoProto::kUnderConstruction);
    bip_1st_.set_block_id(1074664859);
    bip_1st_.set_gen_stamp(924141);
    bip_1st_.set_num_bytes(1024);
    bip_1st_.set_inode_id(inprogress_inode_.id());
    bip_1st_.set_expected_rep(2);
    bip_1st_.set_write_mode(cloudfs::IoMode::TOS_BLOCK);

    bip_2nd_.set_state(BlockInfoProto::kUnderConstruction);
    bip_2nd_.set_block_id(1074664860);
    bip_2nd_.set_gen_stamp(924145);
    bip_2nd_.set_num_bytes(4096);
    bip_2nd_.set_inode_id(inprogress_inode_.id());
    bip_2nd_.set_expected_rep(2);
    bip_2nd_.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);

    bp_1st_.set_blockid(bip_1st_.block_id());
    bp_1st_.set_genstamp(bip_1st_.gen_stamp());
    bp_1st_.set_numbytes(bip_1st_.num_bytes());

    bp_2nd_.set_blockid(bip_2nd_.block_id());
    bp_2nd_.set_genstamp(bip_2nd_.gen_stamp());
    bp_2nd_.set_numbytes(bip_2nd_.num_bytes());

    dir1_path_ = "/test_dir1";
    file1_path_ = dir1_path_ + "/test_file1";
    dir2_path_ = "/test_dir2";
    file2_path_ = dir2_path_ + "/test_file2";
    rb_dir_path_ = "/.RECYCLE.BIN";
    ub_dir_path_ = rb_dir_path_ + "/root";
    rb_file_path_ = ub_dir_path_ + "/removed_file";

    // "/"--+--"test_dir1"----"test_file1"
    //      |
    //      +--"test_dir2"----"test_file2"
    //      |
    //      +-- ".RECYCLE.BIN"---"root"

    PermissionStatus perm;
    perm.set_username("root");
    perm.set_groupname("root");
    perm.set_permission(777);

    dir1_inode_.set_id(kLastReservedINodeId + 3);
    dir1_inode_.set_parent_id(kRootINodeId);
    dir1_inode_.set_name("test_dir1");
    dir1_inode_.mutable_permission()->CopyFrom(perm);
    dir1_inode_.set_type(INode::kDirectory);
    dir1_inode_.set_mtime(1642649452000);
    dir1_inode_.set_atime(1642649453000);

    file1_inode_.set_id(kLastReservedINodeId + 4);
    file1_inode_.set_parent_id(dir1_inode_.id());
    file1_inode_.set_name("test_file1");
    file1_inode_.mutable_permission()->CopyFrom(perm);
    file1_inode_.set_type(INode::kFile);
    file1_inode_.set_mtime(1642649454000);
    file1_inode_.set_atime(1642649455000);
    file1_inode_.set_status(INode::kFileComplete);

    dir2_inode_.set_id(kLastReservedINodeId + 5);
    dir2_inode_.set_parent_id(kRootINodeId);
    dir2_inode_.set_name("test_dir2");
    dir2_inode_.mutable_permission()->CopyFrom(perm);
    dir2_inode_.set_type(INode::kDirectory);
    dir2_inode_.set_mtime(1642649452000);
    dir2_inode_.set_atime(1642649453000);

    file2_inode_.set_id(kLastReservedINodeId + 6);
    file2_inode_.set_parent_id(dir2_inode_.id());
    file2_inode_.set_name("test_file2");
    file2_inode_.mutable_permission()->CopyFrom(perm);
    file2_inode_.set_type(INode::kFile);
    file2_inode_.set_mtime(1642649454000);
    file2_inode_.set_atime(1642649455000);
    file2_inode_.set_status(INode::kFileComplete);

    rb_dir_inode_.set_id(kLastReservedINodeId + 7);
    rb_dir_inode_.set_parent_id(kRootINodeId);
    rb_dir_inode_.set_name(kRecycleBinDirNameString.c_str());
    rb_dir_inode_.mutable_permission()->CopyFrom(perm);
    rb_dir_inode_.set_type(INode::kDirectory);
    rb_dir_inode_.set_mtime(1642649452000);
    rb_dir_inode_.set_atime(1642649453000);

    ub_dir_inode_.set_id(kLastReservedINodeId + 8);
    ub_dir_inode_.set_parent_id(rb_dir_inode_.id());
    ub_dir_inode_.set_name("root");
    ub_dir_inode_.mutable_permission()->CopyFrom(perm);
    ub_dir_inode_.set_type(INode::kDirectory);
    ub_dir_inode_.set_mtime(1642649452000);
    ub_dir_inode_.set_atime(1642649453000);

    rb_file_inode_.set_id(kLastReservedINodeId + 6);
    rb_file_inode_.set_parent_id(ub_dir_inode_.id());
    rb_file_inode_.set_name("removed_file");
    rb_file_inode_.mutable_permission()->CopyFrom(perm);
    rb_file_inode_.set_type(INode::kFile);
    rb_file_inode_.set_mtime(1642649454000);
    rb_file_inode_.set_atime(1642649455000);
    rb_file_inode_.set_status(INode::kFileComplete);
  }

  void TearDown() override {
    ns_->StopStandby();
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  void InitINodeStat() {
    ScrubOpType optype = SCRUB_OPTYPE_INODE_STAT;
    NameSpaceScrubRunner runner;
    runner.Start();
    runner.StartScrub(optype,
                      SCRUB_ACTION_FORCE_OVERWRITE,
                      meta_storage_->GetRootINode(),
                      ns_.get(),
                      meta_storage_);
    ScrubProgress p;
    Status s;
    while (s.IsOK()) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      s = runner.GetScrubProgress(optype, &p);
      if (s.IsOK()) {
        std::cerr << "Scrub progress: " << p.ToString() << std::endl;
      }
    }
    ScrubResult r;
    s = runner.GetScrubResult(optype, &r);
    ASSERT_TRUE(s.IsOK());
    runner.Stop();
  }

  PermissionStatus GetDefaultAccFilePermission() {
    PermissionStatus p;
    p.set_username("root");
    p.set_groupname("");
    p.set_permission(0755);
    return std::move(p);
  }

  void TestOrderedCommitINodes(const std::vector<INode*>& inodes_add,
                               const std::vector<INode*>& parents,
                               int64_t txid) {
    std::vector<INode*> inodes_add2;
    std::vector<INodeAndSnapshot> parents2;
    for (int i = 0; i < inodes_add.size(); i++) {
      inodes_add2.emplace_back(inodes_add[i]);
    }
    SnapshotLog dummy_snaplog;
    for (int i = 0; i < parents.size(); i++) {
      parents2.emplace_back(parents[i], &dummy_snaplog);
    }
    meta_storage_->OrderedCommitINodes(&inodes_add2,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       nullptr,
                                       &parents2,
                                       nullptr,
                                       {},
                                       {},
                                       txid,
                                       {},
                                       nullptr);
  }

  void TestOrderedInsertINode(INode& new_inode,
                              int64_t txid,
                              Closure* done = nullptr,
                              INode* parent = nullptr) {
    std::vector<INode*> inodes_add = {&new_inode};
    if (parent) {
      SnapshotLog dummy_snaplog;
      std::vector<INodeAndSnapshot> parents;
      parents.emplace_back(parent, &dummy_snaplog);
      meta_storage_->OrderedCommitINodes(&inodes_add,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         &parents,
                                         nullptr,
                                         {},
                                         {},
                                         txid,
                                         {done});
    } else {
      meta_storage_->OrderedCommitINodes(&inodes_add,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         nullptr,
                                         {},
                                         {},
                                         txid,
                                         {done});
    }
  }

 protected:
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  MetricID metric_id_;
  RetryCache retry_cache_;
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<MetaStorage> meta_storage_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safe_mode_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::string block_pool_ = "bp";

  std::string path_;
  INode spark_inode_;
  INode inprogress_inode_;
  BlockInfoProto bip_1st_;
  BlockInfoProto bip_2nd_;
  BlockProto bp_1st_;
  BlockProto bp_2nd_;
  RpcInfoPB log_rpc_info_;

  std::string dir1_path_;
  std::string file1_path_;
  std::string dir2_path_;
  std::string file2_path_;
  std::string rb_dir_path_;
  std::string ub_dir_path_;
  std::string rb_file_path_;
  INode dir1_inode_;
  INode file1_inode_;
  INode dir2_inode_;
  INode file2_inode_;
  INode rb_dir_inode_;
  INode ub_dir_inode_;
  INode rb_file_inode_;
};

TEST_F(ApplyCfsOpTest, ApplyOpAddBlockV2FirstBlock) {
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  BlockToBeAdd proto;
  proto.set_path(path_);
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip()->CopyFrom(bip_1st_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpAddBlockV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(3);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(3);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 1);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 1);
  EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.write_mode(), cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kUnderConstruction);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_FALSE(cache_entry);
}

TEST_F(ApplyCfsOpTest, ApplyOpAddBlockV2PenultimateBlockIsTosBlock) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  // Insert BlockMapSlice::uc_states_.
  block_manager_->AddBlock(
      Block(bip_1st_.block_id(), bip_1st_.num_bytes(), bip_1st_.gen_stamp()),
      bip_1st_.inode_id(),
      kInvalidINodeId,
      3,
      bip_1st_.write_mode(),
      std::vector<DatanodeID>{},
      BlockUCState::kUnderConstruction);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  BlockToBeAdd proto;
  proto.set_path(path_);
  inprogress_inode_.mutable_blocks(0)->set_numbytes(10240);
  inprogress_inode_.add_blocks()->CopyFrom(bp_2nd_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  bip_1st_.set_state(BlockInfoProto::kPersisted);
  bip_1st_.set_num_bytes(10240);
  proto.mutable_penultimate_bip()->CopyFrom(bip_1st_);
  proto.mutable_last_bip()->CopyFrom(bip_2nd_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpAddBlockV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 2);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 2);
  EXPECT_EQ(stat.data_size, 10240 + bip_2nd_.num_bytes());

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(bip.num_bytes(), 10240);
  EXPECT_EQ(bip.write_mode(), cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kPersisted);
  EXPECT_EQ(block_manager_->GetBlock(bip_1st_.block_id()).num_bytes, 10240);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::TOS_BLOCK);

  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_2nd_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_2nd_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.write_mode(), cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_2nd_.block_id()),
            cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_2nd_.block_id()),
            BlockUCState::kUnderConstruction);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_FALSE(cache_entry);
}

TEST_F(ApplyCfsOpTest, ApplyOpAddBlockV2PenultimateBlockIsDatanodeBlock) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  bip_1st_.set_write_mode(cloudfs::DATANODE_BLOCK);
  // Insert BlockMapSlice::uc_states_.
  block_manager_->AddBlock(
      Block(bip_1st_.block_id(), bip_1st_.num_bytes(), bip_1st_.gen_stamp()),
      bip_1st_.inode_id(),
      kInvalidINodeId,
      3,
      bip_1st_.write_mode(),
      std::vector<DatanodeID>{},
      BlockUCState::kUnderConstruction);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  BlockToBeAdd proto;
  proto.set_path(path_);
  inprogress_inode_.mutable_blocks(0)->set_numbytes(10240);
  inprogress_inode_.add_blocks()->CopyFrom(bp_2nd_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  bip_1st_.set_state(BlockInfoProto::kComplete);
  bip_1st_.set_num_bytes(10240);
  proto.mutable_penultimate_bip()->CopyFrom(bip_1st_);
  proto.mutable_last_bip()->CopyFrom(bip_2nd_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpAddBlockV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 2);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 2);
  EXPECT_EQ(stat.data_size, 10240 + bip_2nd_.num_bytes());

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.num_bytes(), 10240);
  EXPECT_EQ(bip.write_mode(), cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kComplete);
  EXPECT_EQ(block_manager_->GetBlock(bip_1st_.block_id()).num_bytes, 10240);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::DATANODE_BLOCK);

  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_2nd_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_2nd_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.write_mode(), cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_2nd_.block_id()),
            cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_2nd_.block_id()),
            BlockUCState::kUnderConstruction);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_FALSE(cache_entry);
}

TEST_F(ApplyCfsOpTest, ApplyOpUpdateTtlATime) {
  FLAGS_edit_log_assigner_apply_mode =
        static_cast<int>(ApplyMode::LOGICAL);
  {
    inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
    TestOrderedInsertINode(spark_inode_, 1, nullptr);
    TestOrderedInsertINode(
        inprogress_inode_, 2, nullptr, &spark_inode_);

    ATimeToBeUpdateProtos atimes;
    auto* atime = atimes.add_atime_to_be_updates();
    atime->set_inode_id(spark_inode_.id());
    atime->set_atime(12321);
    atime = atimes.add_atime_to_be_updates();
    atime->set_inode_id(inprogress_inode_.id());
    atime->set_atime(12322);

    meta_storage_->UpdateTtlATimes(atimes, 3, nullptr);
    meta_storage_->WaitNoPending();


    auto op = std::make_shared<OpUpdateATimeProtos>();
    op->SetProto(atimes);
    op->SetTxid(4);
    ns_->Apply(op);
    ns_->WaitNoPending();

    ATimeToBeUpdateProtos atime2;
    ATimeToBeUpdate single_atime;
    EXPECT_TRUE(meta_storage_->GetTtlATime(spark_inode_.id(), &single_atime).IsOK());
    atime2.add_atime_to_be_updates()->CopyFrom(single_atime);
    EXPECT_TRUE(meta_storage_->GetTtlATime(inprogress_inode_.id(), &single_atime).IsOK());
    atime2.add_atime_to_be_updates()->CopyFrom(single_atime);

    EXPECT_EQ(atime2.SerializeAsString(), atimes.SerializeAsString());
  }

  FLAGS_edit_log_assigner_apply_mode =
        static_cast<int>(ApplyMode::PHYSICAL_CONCUR);
  {
    inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
    TestOrderedInsertINode(spark_inode_, 5, nullptr);
    TestOrderedInsertINode(
        inprogress_inode_, 6, nullptr, &spark_inode_);

    ATimeToBeUpdateProtos atimes;
    auto* atime = atimes.add_atime_to_be_updates();
    atime->set_inode_id(spark_inode_.id());
    atime->set_atime(22321);
    atime = atimes.add_atime_to_be_updates();
    atime->set_inode_id(inprogress_inode_.id());
    atime->set_atime(22322);

    meta_storage_->UpdateTtlATimes(atimes, 7, nullptr);
    meta_storage_->WaitNoPending();


    auto op = std::make_shared<OpUpdateATimeProtos>();
    op->SetProto(atimes);
    op->SetTxid(8);
    ns_->Apply(op);
    ns_->WaitNoPending();

    ATimeToBeUpdateProtos atime2;
    ATimeToBeUpdate single_atime;
    EXPECT_TRUE(meta_storage_->GetTtlATime(spark_inode_.id(), &single_atime).IsOK());
    atime2.add_atime_to_be_updates()->CopyFrom(single_atime);
    EXPECT_TRUE(meta_storage_->GetTtlATime(inprogress_inode_.id(), &single_atime).IsOK());
    atime2.add_atime_to_be_updates()->CopyFrom(single_atime);

    EXPECT_EQ(atime2.SerializeAsString(), atimes.SerializeAsString());
  }
  FLAGS_edit_log_assigner_apply_mode =
        static_cast<int>(ApplyMode::LOGICAL);
}

TEST_F(ApplyCfsOpTest, ApplyOpAbandonBlock) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  BlockToBeAbandon proto;
  proto.set_path(path_);
  inprogress_inode_.mutable_blocks()->RemoveLast();
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.set_abandoned_blk_id(bip_1st_.block_id());
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpAbandonBlock>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 0);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 0);
  EXPECT_EQ(stat.data_size, 0);

  // BlockInfoProto bip;
  // EXPECT_FALSE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  // Test BlockInfo doesn't exist in memory.
  // EXPECT_EQ(block_manager_->GetBlockINodeID(bip_1st_.block_id()),
  //           kInvalidINodeId);
  auto it = meta_storage_->GetDeprecatingBlkIterator();
  it->SeekToFirst();
  EXPECT_EQ(platform::ReadBigEndian<BlockID>(it->key().data(), 0),
            bip_1st_.block_id());

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_TRUE(cache_entry);
  EXPECT_TRUE(cache_entry->IsCompleted());
  EXPECT_TRUE(cache_entry->GetStatus().IsOK());
}

TEST_F(ApplyCfsOpTest, ApplyOpUpdatePipeline) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  bip_1st_.set_gen_stamp(928000);
  bip_1st_.set_num_bytes(4096);
  inprogress_inode_.mutable_blocks(0)->set_genstamp(928000);
  inprogress_inode_.mutable_blocks(0)->set_numbytes(4096);
  PipelineToBeUpdate proto;
  proto.set_path(path_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip_tbuc()->CopyFrom(bip_1st_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpUpdatePipeline>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 1);
  EXPECT_EQ(inode.blocks(0).genstamp(), 928000);
  EXPECT_EQ(inode.blocks(0).numbytes(), 4096);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 1);
  EXPECT_EQ(stat.data_size, 4096);

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.write_mode(), cloudfs::TOS_BLOCK);
  EXPECT_EQ(bip.gen_stamp(), 928000);
  EXPECT_EQ(bip.num_bytes(), 4096);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kUnderConstruction);
  auto block = block_manager_->GetBlock(bip_1st_.block_id());
  EXPECT_EQ(block.gs, 928000);
  EXPECT_EQ(block.num_bytes, 4096);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_TRUE(cache_entry);
  EXPECT_TRUE(cache_entry->IsCompleted());
  EXPECT_TRUE(cache_entry->GetStatus().IsOK());
}

TEST_F(ApplyCfsOpTest, ApplyOpFsync) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  bip_1st_.set_num_bytes(4096);
  inprogress_inode_.mutable_blocks(0)->set_numbytes(4096);
  FileToBeSync proto;
  proto.set_path(path_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip_tbuc()->CopyFrom(bip_1st_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpFsync>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 1);
  EXPECT_EQ(inode.blocks(0).numbytes(), 4096);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 1);
  EXPECT_EQ(stat.data_size, 4096);

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.write_mode(), cloudfs::TOS_BLOCK);
  EXPECT_EQ(bip.num_bytes(), 4096);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kUnderConstruction);
  auto block = block_manager_->GetBlock(bip_1st_.block_id());
  EXPECT_EQ(block.num_bytes, 4096);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_TRUE(cache_entry);
  EXPECT_TRUE(cache_entry->IsCompleted());
  EXPECT_TRUE(cache_entry->GetStatus().IsOK());
}

TEST_F(ApplyCfsOpTest, ApplyOpFsyncByCommitLastBlock) {
  block_manager_->AddBlock(
      Block(bip_2nd_.block_id(), bip_2nd_.num_bytes(), bip_2nd_.gen_stamp()),
      bip_2nd_.inode_id(),
      kInvalidINodeId,
      2,
      bip_2nd_.write_mode(),
      std::vector<DatanodeID>{},
      BlockUCState::kUnderConstruction);

  inprogress_inode_.add_blocks()->CopyFrom(bp_2nd_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_2nd_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_2nd_.num_bytes());
  }

  bip_2nd_.set_gen_stamp(kBlockProtocolV2GenerationStamp);
  bip_2nd_.set_num_bytes(4096);
  bip_2nd_.set_state(BlockInfoProto::kCommitted);
  bip_2nd_.set_need_align(true);
  inprogress_inode_.mutable_blocks(0)->set_numbytes(4096);
  FileToBeSync proto;
  proto.set_path(path_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip_tbuc()->CopyFrom(bip_2nd_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpFsync>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);

  EXPECT_EQ(block_manager_->GetBlockUCState(bip_2nd_.block_id()),
            BlockUCState::kUnderConstruction);

  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 1);
  EXPECT_EQ(inode.blocks(0).numbytes(), 4096);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 1);
  EXPECT_EQ(stat.data_size, 4096);

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_2nd_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_2nd_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(bip.write_mode(), cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(bip.num_bytes(), 4096);
  EXPECT_EQ(bip.need_align(), true);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_2nd_.block_id()),
            cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_2nd_.block_id()),
            BlockUCState::kCommitted);
  auto block = block_manager_->GetBlock(bip_2nd_.block_id());
  EXPECT_EQ(block.num_bytes, 4096);

  auto cache_entry = retry_cache_.GetCacheEntry(log_rpc_info_.rpc_client_id(),
                                                log_rpc_info_.rpc_call_id());
  EXPECT_TRUE(cache_entry);
  EXPECT_TRUE(cache_entry->IsCompleted());
  EXPECT_TRUE(cache_entry->GetStatus().IsOK());
}

TEST_F(ApplyCfsOpTest, ApplyOpCommitBlockSynchronization) {
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  inprogress_inode_.add_blocks()->CopyFrom(bp_2nd_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 2);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes() + bip_2nd_.num_bytes());
  }

  BlockToBeCommitSynchronization proto;
  proto.mutable_old_inode()->CopyFrom(inprogress_inode_);
  bip_2nd_.set_num_bytes(8192);
  bip_2nd_.set_state(BlockInfoProto::kComplete);
  inprogress_inode_.mutable_blocks(1)->set_numbytes(8192);
  proto.set_path(path_);
  proto.set_delete_block(false);
  proto.set_close_file(false);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip()->CopyFrom(bip_2nd_);
  proto.add_ancestors_id(kRootINodeId);
  proto.add_ancestors_id(spark_inode_.id());
  auto op = std::make_shared<OpCommitBlockSynchronization>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 2);
  EXPECT_EQ(inode.blocks(1).numbytes(), 8192);
  EXPECT_TRUE(inode.has_uc());

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 2);
  EXPECT_EQ(stat.data_size, bip_1st_.num_bytes() + 8192);

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_2nd_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_2nd_.SerializeAsString());
  EXPECT_EQ(bip.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(bip.write_mode(), cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(bip.num_bytes(), 8192);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_2nd_.block_id()),
            cloudfs::DATANODE_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_2nd_.block_id()),
            BlockUCState::kComplete);
  auto block = block_manager_->GetBlock(bip_2nd_.block_id());
  EXPECT_EQ(block.num_bytes, 8192);
}

TEST_F(ApplyCfsOpTest, ApplyOpCloseFile) {
  // Insert BlockMapSlice::uc_states_.
  block_manager_->AddBlock(
      Block(bip_1st_.block_id(), bip_1st_.num_bytes(), bip_1st_.gen_stamp()),
      bip_1st_.inode_id(),
      kInvalidINodeId,
      3,
      bip_1st_.write_mode(),
      std::vector<DatanodeID>{},
      BlockUCState::kUnderConstruction);
  inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->PutBlockInfo(bip_1st_, nullptr, 3, nullptr);
  meta_storage_->WaitNoPending();
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());
  }

  ns_->lease_manager()->AddLease(inprogress_inode_.uc().client_name(),
                                 inprogress_inode_.id());

  bip_1st_.set_state(BlockInfoProto::kPersisted);
  inprogress_inode_.clear_uc();
  inprogress_inode_.set_status(INode::kFileComplete);
  FileToBeClose proto;
  proto.set_path(path_);
  proto.mutable_inode()->CopyFrom(inprogress_inode_);
  proto.mutable_last_bip()->CopyFrom(bip_1st_);
  auto op = std::make_shared<OpCloseFile>();
  op->SetProto(std::move(proto));
  op->SetTxid(4);
  ns_->Apply(op);
  ns_->WaitNoPending();
  inprogress_inode_.set_last_update_txid(4);

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.blocks_size(), 1);
  EXPECT_FALSE(inode.has_uc());
  EXPECT_EQ(inode.status(), INode::kFileComplete);

  INodeStat stat;
  EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
  EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
  EXPECT_EQ(stat.file_num, 1);
  EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
  EXPECT_EQ(stat.block_num, 1);
  EXPECT_EQ(stat.data_size, bip_1st_.num_bytes());

  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(bip_1st_.block_id(), &bip));
  EXPECT_EQ(bip.SerializeAsString(), bip_1st_.SerializeAsString());
  EXPECT_EQ(bip.write_mode(), cloudfs::TOS_BLOCK);
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(block_manager_->GetBlockIoMode(bip_1st_.block_id()),
            cloudfs::TOS_BLOCK);
  EXPECT_EQ(block_manager_->GetBlockUCState(bip_1st_.block_id()),
            BlockUCState::kPersisted);

  auto cache_entry = retry_cache_.GetCacheEntry(
      log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
  EXPECT_FALSE(cache_entry);
}

TEST_F(ApplyCfsOpTest, ApplyOpOpenFile) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.file_num, 0);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // create file without overwrite
  FileToBeOpen proto;
  proto.set_path(file1_path_);
  proto.mutable_inode()->CopyFrom(file1_inode_);
  proto.set_overwrite(false);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  proto.mutable_parent()->CopyFrom(dir1_inode_);
  std::vector<INodeID> ancs = { kRootINodeId, dir1_inode_.id() };
  *proto.mutable_ancestors_id() = { ancs.begin(), ancs.end() };
  auto op = std::make_shared<OpOpenFile>();
  op->SetProto(std::move(proto));
  op->SetTxid(2);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode exists
  {
    INode tmp;
    EXPECT_TRUE(ns_->GetINodeByPath(file1_path_, &tmp).IsOK());
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpOpenFile_Overwrite) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // create file with overwrite, delete old file directly
  auto new_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  new_file.set_id(kLastReservedINodeId + 123);
  new_file.set_mtime(new_mtime);

  // create file with overwrite
  FileToBeOpen proto;
  proto.set_path(file1_path_);
  proto.mutable_inode()->CopyFrom(new_file);
  proto.set_overwrite(true);
  proto.mutable_parent()->CopyFrom(src_dir);
  proto.set_move_to_recycle_bin(false);
  std::vector<INodeID> ancs = { kRootINodeId, src_dir.id() };
  *proto.mutable_ancestors_id() = { ancs.begin(), ancs.end() };
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpOpenFile>();
  op->SetProto(std::move(proto));
  op->SetTxid(3);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode exists
  {
    INode tmp;
    EXPECT_TRUE(ns_->GetINodeByPath(file1_path_, &tmp).IsOK());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpOpenFile_Overwrite2) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  auto b = file1_inode_.add_blocks();
  b->set_blockid(1076015580);
  b->set_genstamp(1001);
  b->set_numbytes(1024);
  BlockInfoProto file1_bip;
  file1_bip.set_state(BlockInfoProto::kPersisted);
  file1_bip.set_block_id(1076015580);
  file1_bip.set_gen_stamp(1001);
  file1_bip.set_num_bytes(1024);
  file1_bip.set_inode_id(file1_inode_.id());
  file1_bip.set_expected_rep(3);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  meta_storage_->TestOnlyPutBlockInfo(file1_bip, false);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 1);
    EXPECT_EQ(stat.data_size, 1024);
  }

  // create file with overwrite, delete old file directly
  auto new_file = file1_inode_;
  new_file.clear_blocks();
  auto src_dir = dir1_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                       std::chrono::system_clock::now().time_since_epoch())
                       .count();
  new_file.set_id(kLastReservedINodeId + 123);
  new_file.set_mtime(new_mtime);
  src_dir.set_mtime(new_mtime);

  // create file with overwrite
  FileToBeOpen proto;
  proto.set_path(file1_path_);
  proto.mutable_inode()->CopyFrom(new_file);
  proto.set_overwrite(true);
  proto.mutable_parent()->CopyFrom(src_dir);
  proto.set_move_to_recycle_bin(false);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  proto.mutable_overwrite_inode()->CopyFrom(file1_inode_);
  file1_bip.set_state(BlockInfoProto::kDeprecated);
  auto op = std::make_shared<OpOpenFile>();
  op->SetProto(std::move(proto));
  op->SetTxid(3);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode exists
  {
    INode tmp;
    EXPECT_EQ(meta_storage_->GetINode(file1_inode_.id(), &tmp),
              StatusCode::kFileNotFound);
    EXPECT_TRUE(ns_->GetINodeByPath(file1_path_, &tmp).IsOK());
    EXPECT_EQ(tmp.mtime(), new_mtime);
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &tmp).IsOK());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify block info proto
  {
    BlockInfoProto tmp;
    EXPECT_TRUE(meta_storage_->GetBlockInfo(b->blockid(), &tmp));
    EXPECT_EQ(tmp.block_id(), b->blockid());
    EXPECT_EQ(tmp.gen_stamp(), b->genstamp());
    EXPECT_EQ(tmp.num_bytes(), b->numbytes());
    EXPECT_TRUE(meta_storage_->TestOnlyIsDeprecatingBlockExisted(b->blockid()));
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 2);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 1);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(log_rpc_info_.rpc_client_id(),
                                                  log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpOpenFile_OverwriteToRecycleBin) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 4);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 3);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // create file with overwrite, remove old file into recycle bin
  auto new_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto ub_dir = ub_dir_inode_;
  auto rb_file = rb_file_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  new_file.set_id(kLastReservedINodeId + 123);
  new_file.set_mtime(new_mtime);
  rb_file.set_id(file1_inode_.id());
  rb_file.set_mtime(new_mtime);

  FileToBeOpen proto;
  proto.set_path(file1_path_);
  proto.mutable_inode()->CopyFrom(new_file);
  proto.set_overwrite(true);
  proto.mutable_parent()->CopyFrom(src_dir);
  proto.set_move_to_recycle_bin(true);
  proto.set_rb_path(rb_file_path_);
  proto.mutable_rb_inode()->CopyFrom(rb_file);
  proto.mutable_rb_parent()->CopyFrom(ub_dir);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpOpenFile>();
  op->SetProto(std::move(proto));
  op->SetTxid(5);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode exists
  {
    INode tmp;
    EXPECT_TRUE(ns_->GetINodeByPath(file1_path_, &tmp).IsOK());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 2);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 3);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpDeleteV2) {
  FLAGS_recycle_bin_enable = false;
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&dir2_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&file2_inode_}, {&dir2_inode_}, 4);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 5);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 6);
  TestOrderedCommitINodes({&rb_file_inode_}, {&rb_file_inode_}, 7);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 7);
    EXPECT_EQ(stat.file_num, 3);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // delete file
  INodeToBeDelete proto;
  proto.set_path(file1_path_);
  proto.mutable_inode()->CopyFrom(file1_inode_);
  proto.mutable_parent()->CopyFrom(dir1_inode_);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpDeleteV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(8);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode removed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(file1_path_, &tmp).IsOK());
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 6);
    EXPECT_EQ(stat.file_num, 2);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // delete file inside of recycle bin
  INodeToBeDelete proto2;
  proto2.Clear();
  proto2.set_path(rb_file_path_);
  proto2.mutable_inode()->CopyFrom(rb_file_inode_);
  proto2.mutable_parent()->CopyFrom(ub_dir_inode_);
  proto2.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op2 = std::make_shared<OpDeleteV2>();
  op2->SetProto(std::move(proto2));
  op2->SetTxid(9);
  ns_->Apply(op2);
  ns_->WaitNoPending();

  // verify inode removed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(rb_file_path_, &tmp).IsOK());
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpRenameOldV2) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&dir2_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 4);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 5);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // rename
  auto src_file_path = file1_path_;
  auto dst_file_path = file2_path_;
  auto src_dir_path = dir1_path_;
  auto dst_dir_path = dir2_path_;
  auto src_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto dst_file = file2_inode_;
  auto dst_dir = dir2_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRenameOld proto;
  proto.set_src_path(src_file_path);
  proto.set_dst_path(dst_file_path);
  proto.mutable_src_inode()->CopyFrom(src_file);
  proto.mutable_dst_inode()->CopyFrom(dst_file);
  proto.mutable_src_parent()->CopyFrom(src_dir);
  proto.mutable_dst_parent()->CopyFrom(dst_dir);
  proto.set_timestamp_in_ms(new_mtime);
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpRenameOldV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(6);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // rename to recycle bin
  src_file_path = dst_file_path;
  dst_file_path = rb_file_path_;
  src_dir_path = dst_dir_path;
  dst_dir_path = ub_dir_path_;
  src_file = dst_file;
  src_dir = dst_dir;
  dst_file = rb_file_inode_;
  dst_dir = ub_dir_inode_;
  new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                  std::chrono::system_clock::now().time_since_epoch())
                  .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRenameOld proto2;
  proto2.set_src_path(src_file_path);
  proto2.set_dst_path(dst_file_path);
  proto2.mutable_src_inode()->CopyFrom(src_file);
  proto2.mutable_dst_inode()->CopyFrom(dst_file);
  proto2.mutable_src_parent()->CopyFrom(src_dir);
  proto2.mutable_dst_parent()->CopyFrom(dst_dir);
  proto2.set_timestamp_in_ms(new_mtime);
  proto2.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op2 = std::make_shared<OpRenameOldV2>();
  op2->SetProto(std::move(proto2));
  op2->SetTxid(7);
  ns_->Apply(op2);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // rename out of recycle bin
  src_file_path = dst_file_path;
  dst_file_path = file1_path_;
  src_dir_path = dst_dir_path;
  dst_dir_path = dir1_path_;
  src_file = dst_file;
  src_dir = dst_dir;
  dst_file = file1_inode_;
  dst_dir = dir1_inode_;
  new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                  std::chrono::system_clock::now().time_since_epoch())
                  .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRenameOld proto3;
  proto3.set_src_path(src_file_path);
  proto3.set_dst_path(dst_file_path);
  proto3.mutable_src_inode()->CopyFrom(src_file);
  proto3.mutable_dst_inode()->CopyFrom(dst_file);
  proto3.mutable_src_parent()->CopyFrom(src_dir);
  proto3.mutable_dst_parent()->CopyFrom(dst_dir);
  proto3.set_timestamp_in_ms(new_mtime);
  proto3.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op3 = std::make_shared<OpRenameOldV2>();
  op3->SetProto(std::move(proto3));
  op3->SetTxid(8);
  ns_->Apply(op3);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpRenameV2) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&dir2_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 4);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 5);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // rename
  auto src_file_path = file1_path_;
  auto dst_file_path = file2_path_;
  auto src_dir_path = dir1_path_;
  auto dst_dir_path = dir2_path_;
  auto src_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto dst_file = file2_inode_;
  auto dst_dir = dir2_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRename proto;
  proto.set_src_path(src_file_path);
  proto.set_dst_path(dst_file_path);
  proto.mutable_src_inode()->CopyFrom(src_file);
  proto.mutable_dst_inode()->CopyFrom(dst_file);
  proto.mutable_src_parent()->CopyFrom(src_dir);
  proto.mutable_dst_parent()->CopyFrom(dst_dir);
  proto.set_timestamp_in_ms(new_mtime);
  proto.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kNone).value());
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op = std::make_shared<OpRenameV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(6);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // rename to recycle bin
  src_file_path = dst_file_path;
  dst_file_path = rb_file_path_;
  src_dir_path = dst_dir_path;
  dst_dir_path = ub_dir_path_;
  src_file = dst_file;
  src_dir = dst_dir;
  dst_file = rb_file_inode_;
  dst_dir = ub_dir_inode_;
  new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                  std::chrono::system_clock::now().time_since_epoch())
                  .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRename proto2;
  proto2.set_src_path(src_file_path);
  proto2.set_dst_path(dst_file_path);
  proto2.mutable_src_inode()->CopyFrom(src_file);
  proto2.mutable_dst_inode()->CopyFrom(dst_file);
  proto2.mutable_src_parent()->CopyFrom(src_dir);
  proto2.mutable_dst_parent()->CopyFrom(dst_dir);
  proto2.set_timestamp_in_ms(new_mtime);
  proto2.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kNone).value());
  proto2.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op2 = std::make_shared<OpRenameV2>();
  op2->SetProto(std::move(proto2));
  op2->SetTxid(7);
  ns_->Apply(op2);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // rename out of recycle bin
  src_file_path = dst_file_path;
  dst_file_path = file1_path_;
  src_dir_path = dst_dir_path;
  dst_dir_path = dir1_path_;
  src_file = dst_file;
  src_dir = dst_dir;
  dst_file = file1_inode_;
  dst_dir = dir1_inode_;
  new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                  std::chrono::system_clock::now().time_since_epoch())
                  .count();
  dst_file.set_id(src_file.id());
  dst_file.set_mtime(new_mtime);

  INodeToBeRename proto3;
  proto3.set_src_path(src_file_path);
  proto3.set_dst_path(dst_file_path);
  proto3.mutable_src_inode()->CopyFrom(src_file);
  proto3.mutable_dst_inode()->CopyFrom(dst_file);
  proto3.mutable_src_parent()->CopyFrom(src_dir);
  proto3.mutable_dst_parent()->CopyFrom(dst_dir);
  proto3.set_timestamp_in_ms(new_mtime);
  proto3.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kNone).value());
  proto3.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  auto op3 = std::make_shared<OpRenameV2>();
  op3->SetProto(std::move(proto3));
  op3->SetTxid(8);
  ns_->Apply(op3);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpRenameV2_Overwrite) {
  FLAGS_recycle_bin_enable = false;
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&dir2_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&file2_inode_}, {&dir2_inode_}, 4);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 5);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 6);
  TestOrderedCommitINodes({&rb_file_inode_}, {&ub_dir_inode_}, 7);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 7);
    EXPECT_EQ(stat.file_num, 3);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // rename overwrite
  auto src_file_path = file1_path_;
  auto dst_file_path = file2_path_;
  auto src_dir_path = dir1_path_;
  auto dst_dir_path = dir2_path_;
  auto src_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto old_dst_file = file2_inode_;
  auto new_dst_file = file2_inode_;
  auto dst_dir = dir2_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  new_dst_file.set_id(src_file.id());
  new_dst_file.set_mtime(new_mtime);

  INodeToBeRename proto;
  proto.set_src_path(src_file_path);
  proto.set_dst_path(dst_file_path);
  proto.mutable_src_inode()->CopyFrom(src_file);
  proto.mutable_dst_inode()->CopyFrom(new_dst_file);
  proto.mutable_src_parent()->CopyFrom(src_dir);
  proto.mutable_dst_parent()->CopyFrom(dst_dir);
  proto.mutable_overwrite_inode()->CopyFrom(old_dst_file);
  proto.set_timestamp_in_ms(new_mtime);
  proto.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kOverwrite).value());
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  proto.set_move_to_recycle_bin(false);
  auto op = std::make_shared<OpRenameV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(8);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 6);
    EXPECT_EQ(stat.file_num, 2);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }

  // rename overwrite to recycle bin
  src_file_path = dst_file_path;
  dst_file_path = rb_file_path_;
  src_dir_path = dst_dir_path;
  dst_dir_path = ub_dir_path_;
  src_file = new_dst_file;
  src_dir = dst_dir;
  new_dst_file = rb_file_inode_;
  old_dst_file = rb_file_inode_;
  dst_dir = ub_dir_inode_;
  new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                  std::chrono::system_clock::now().time_since_epoch())
                  .count();
  new_dst_file.set_id(src_file.id());
  new_dst_file.set_mtime(new_mtime);

  INodeToBeRename proto2;
  proto2.set_src_path(src_file_path);
  proto2.set_dst_path(dst_file_path);
  proto2.mutable_src_inode()->CopyFrom(src_file);
  proto2.mutable_dst_inode()->CopyFrom(new_dst_file);
  proto2.mutable_src_parent()->CopyFrom(src_dir);
  proto2.mutable_dst_parent()->CopyFrom(dst_dir);
  proto2.mutable_overwrite_inode()->CopyFrom(old_dst_file);
  proto2.set_timestamp_in_ms(new_mtime);
  proto2.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kOverwrite).value());
  proto2.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  proto2.set_move_to_recycle_bin(false);
  auto op2 = std::make_shared<OpRenameV2>();
  op2->SetProto(std::move(proto2));
  op2->SetTxid(9);
  ns_->Apply(op2);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 5);
    EXPECT_EQ(stat.file_num, 1);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpRenameV2_OverwriteToRecycleBin) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  TestOrderedCommitINodes({&file1_inode_}, {&dir1_inode_}, 2);
  TestOrderedCommitINodes({&dir2_inode_}, {&root_inode}, 3);
  TestOrderedCommitINodes({&file2_inode_}, {&dir2_inode_}, 4);
  TestOrderedCommitINodes({&rb_dir_inode_}, {&root_inode}, 5);
  TestOrderedCommitINodes({&ub_dir_inode_}, {&rb_dir_inode_}, 6);
  meta_storage_->WaitNoPending();

  // check initial status
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 6);
    EXPECT_EQ(stat.file_num, 2);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // rename overwrite, old dst moved to recycle bin
  auto src_file_path = file1_path_;
  auto dst_file_path = file2_path_;
  auto src_dir_path = dir1_path_;
  auto dst_dir_path = dir2_path_;
  auto rb_file_path = rb_file_path_;
  auto rb_dir_path = ub_dir_path_;
  auto src_file = file1_inode_;
  auto src_dir = dir1_inode_;
  auto old_dst_file = file2_inode_;
  auto new_dst_file = file2_inode_;
  auto dst_dir = dir2_inode_;
  auto rb_file = rb_file_inode_;
  auto rb_dir = ub_dir_inode_;
  auto new_mtime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      std::chrono::system_clock::now().time_since_epoch())
                      .count();
  new_dst_file.set_id(src_file.id());
  new_dst_file.set_mtime(new_mtime);
  rb_file.set_id(old_dst_file.id());
  rb_file.set_mtime(new_mtime);

  INodeToBeRename proto;
  proto.set_src_path(src_file_path);
  proto.set_dst_path(dst_file_path);
  proto.mutable_src_inode()->CopyFrom(src_file);
  proto.mutable_dst_inode()->CopyFrom(new_dst_file);
  proto.mutable_src_parent()->CopyFrom(src_dir);
  proto.mutable_dst_parent()->CopyFrom(dst_dir);
  proto.mutable_overwrite_inode()->CopyFrom(old_dst_file);
  proto.set_timestamp_in_ms(new_mtime);
  proto.mutable_rename_options()->set_value(
      RenameOptions(RenameOption::kOverwrite).value());
  proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
  proto.set_move_to_recycle_bin(true);
  proto.set_rb_path(rb_file_path);
  proto.mutable_rb_inode()->CopyFrom(rb_file);
  proto.mutable_rb_parent()->CopyFrom(rb_dir);
  auto op = std::make_shared<OpRenameV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(7);
  ns_->Apply(op);
  ns_->WaitNoPending();

  // verify inode renamed
  {
    INode tmp;
    EXPECT_FALSE(ns_->GetINodeByPath(src_file_path, &tmp).IsOK());
    EXPECT_TRUE(ns_->GetINodeByPath(dst_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), src_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
    EXPECT_TRUE(ns_->GetINodeByPath(rb_file_path, &tmp).IsOK());
    EXPECT_EQ(tmp.id(), old_dst_file.id());
    EXPECT_EQ(tmp.mtime(), new_mtime);
  }

  // verify inode stat updated
  {
    InitINodeStat();
    INodeStat stat;
    EXPECT_TRUE(ns_->GetDirectoryStat("/", &stat).IsOK());
    EXPECT_EQ(stat.inode_num, kNumReservedINode + 6);
    EXPECT_EQ(stat.file_num, 2);
    EXPECT_EQ(stat.dir_num, kNumReservedINode + 4);
    EXPECT_EQ(stat.block_num, 0);
    EXPECT_EQ(stat.data_size, 0);
  }

  // verify retry cache valid
  {
    auto cache_entry = retry_cache_.GetCacheEntry(
        log_rpc_info_.rpc_client_id(), log_rpc_info_.rpc_call_id());
    EXPECT_TRUE(cache_entry);
  }
}

TEST_F(ApplyCfsOpTest, ApplyOpSetReplication) {
  auto root_inode = meta_storage_->GetRootINode();
  INodeInPath spark_inode_iip;
  spark_inode_iip.MutableInodeUnsafe() = spark_inode_;
  TestOrderedCommitINodes({&spark_inode_}, {&root_inode}, 1);
  *inprogress_inode_.add_blocks() = bp_2nd_;
  *inprogress_inode_.add_blocks() = bp_1st_;
  TestOrderedCommitINodes({&inprogress_inode_}, {&spark_inode_}, 2);
  meta_storage_->WaitNoPending();

  INodeToSetReplication proto;
  proto.set_path(path_);
  inprogress_inode_.set_replication(2);
  *proto.mutable_inode() = inprogress_inode_;
  proto.set_replication(2);
  auto op = std::make_shared<OpSetReplicationV2>();
  op->SetProto(std::move(proto));
  op->SetTxid(3);
  ns_->Apply(op);
  ns_->WaitNoPending();

  INode inode;
  EXPECT_EQ(meta_storage_->GetINode(
                spark_inode_.id(), inprogress_inode_.name(), &inode),
            kOK);
  inprogress_inode_.set_last_update_txid(3);
  EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
  EXPECT_EQ(inode.replication(), 2);
}

TEST_F(ApplyCfsOpTest, ApplyOpSnapshot) {
  auto root_inode = meta_storage_->GetRootINode();
  TestOrderedCommitINodes({&dir1_inode_}, {&root_inode}, 1);
  meta_storage_->WaitNoPending();
  INode inode;
  int64_t txid = 2;

  {
    SnapshotToAllow proto;
    proto.set_path(dir1_path_);
    auto op = std::make_shared<OpAllowSnapshotV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(txid++);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &inode).IsOK());
    EXPECT_TRUE(inode.is_snapshottable());
  }

  {
    SnapshotToCreate proto;
    proto.set_path(dir1_path_);
    proto.set_name("s1");
    auto op = std::make_shared<OpCreateSnapshotV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(txid++);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &inode).IsOK());
    EXPECT_EQ(1, inode.snapshots_size());
    EXPECT_EQ("s1", inode.snapshots(0).name());
  }

  {
    SnapshotToRename proto;
    proto.set_path(dir1_path_);
    proto.set_old_name("s1");
    proto.set_new_name("s2");
    auto op = std::make_shared<OpRenameSnapshotV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(txid++);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &inode).IsOK());
    EXPECT_EQ(1, inode.snapshots_size());
    EXPECT_EQ("s2", inode.snapshots(0).name());
  }

  {
    SnapshotToDelete proto;
    proto.set_path(dir1_path_);
    proto.set_name("s2");
    auto op = std::make_shared<OpDeleteSnapshotV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(txid++);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &inode).IsOK());
    EXPECT_EQ(1, inode.snapshots_size());
    EXPECT_TRUE(inode.snapshots(0).deleted());
  }

  {
    SnapshotToDisallow proto;
    proto.set_path(dir1_path_);
    auto op = std::make_shared<OpDisallowSnapshotV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(txid++);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(ns_->GetINodeByPath(dir1_path_, &inode).IsOK());
    EXPECT_FALSE(inode.is_snapshottable());
  }
}

TEST_F(ApplyCfsOpTest, MergeBlock) {
  INodeInPath spark_inode_iip;
  spark_inode_iip.MutableInodeUnsafe() = spark_inode_;
  TestOrderedInsertINode(spark_inode_, 1, nullptr);
  TestOrderedInsertINode(inprogress_inode_, 2, nullptr, &spark_inode_);
  meta_storage_->WaitNoPending();

  {
    inprogress_inode_.add_blocks()->CopyFrom(bp_1st_);
    auto merging_block = inprogress_inode_.add_mergingblocks();
    merging_block->add_oldblocks()->CopyFrom(bp_1st_);
    auto merging_eb = merging_block->mutable_block();
    merging_eb->set_poolid("bp1");
    merging_eb->set_blockid(bp_2nd_.blockid());
    merging_eb->set_generationstamp(bp_2nd_.genstamp());
    merging_eb->set_numbytes(bp_2nd_.numbytes());
    merging_eb->set_offset(0);

    FileAndBlockToBeMerge proto;
    proto.set_path(path_);
    proto.mutable_inode()->CopyFrom(inprogress_inode_);
    proto.mutable_merged_bip()->CopyFrom(bip_2nd_);
    auto op = std::make_shared<OpMergeBlock>();
    op->SetProto(std::move(proto));
    op->SetTxid(3);
    ns_->Apply(op);
    ns_->WaitNoPending();
    inprogress_inode_.set_last_update_txid(3);

    INode inode;
    EXPECT_EQ(meta_storage_->GetINode(
                  spark_inode_.id(), inprogress_inode_.name(), &inode),
              kOK);
    inode.set_last_update_txid(3);
    EXPECT_EQ(inode.ShortDebugString(), inprogress_inode_.ShortDebugString());
    EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
    EXPECT_EQ(inode.blocks_size(), 1);
    EXPECT_EQ(inode.blocks(0).SerializeAsString(), bp_1st_.SerializeAsString());
    EXPECT_EQ(inode.mergingblocks_size(), 1);
    EXPECT_EQ(inode.mergingblocks(0).block().blockid(), bp_2nd_.blockid());
    EXPECT_EQ(inode.mergingblocks(0).block().generationstamp(), bp_2nd_.genstamp());
    EXPECT_EQ(inode.mergingblocks(0).block().numbytes(), bp_2nd_.numbytes());
    EXPECT_EQ(inode.mergingblocks(0).block().offset(), 0);
    EXPECT_EQ(inode.mergingblocks(0).oldblocks_size(), 1);
    EXPECT_EQ(inode.mergingblocks(0).oldblocks(0).SerializeAsString(),
              bp_1st_.SerializeAsString());
    EXPECT_FALSE(block_manager_->BlockHasBeenComplete(bp_2nd_.blockid()));
  }

  {
    inprogress_inode_.clear_blocks();
    inprogress_inode_.add_blocks()->CopyFrom(bp_2nd_);
    inprogress_inode_.clear_mergingblocks();

    bip_2nd_.set_state(BlockInfoProto::kPersisted);
    std::vector<BlockID> to_remove = {bp_1st_.blockid()};

    FileAndBlockToBeMerge proto;
    proto.set_path(path_);
    proto.mutable_inode()->CopyFrom(inprogress_inode_);
    proto.mutable_merged_bip()->CopyFrom(bip_2nd_);
    proto.add_depred_blks()->CopyFrom(bp_1st_);
    auto op = std::make_shared<OpMergeBlock>();
    op->SetProto(std::move(proto));
    op->SetTxid(4);
    ns_->Apply(op);
    ns_->WaitNoPending();
    inprogress_inode_.set_last_update_txid(4);

    INode inode;
    EXPECT_EQ(meta_storage_->GetINode(
                  spark_inode_.id(), inprogress_inode_.name(), &inode),
              kOK);
    inode.set_last_update_txid(4);
    EXPECT_EQ(inode.SerializeAsString(), inprogress_inode_.SerializeAsString());
    EXPECT_EQ(inode.blocks_size(), 1);
    EXPECT_EQ(inode.blocks(0).SerializeAsString(), bp_2nd_.SerializeAsString());
    EXPECT_EQ(inode.mergingblocks_size(), 0);
    EXPECT_TRUE(block_manager_->BlockHasBeenComplete(bp_2nd_.blockid()));
  }
}

TEST_F(ApplyCfsOpTest, ConcatV2TosLocal) {
  auto root_inode = meta_storage_->GetRootINode();
  FLAGS_namespace_type = cloudfs::NamespaceType::LOCAL;
  INodeID inode_id = kLastReservedINodeId + 10;
  BlockID block_id = 1074664860;
  uint64_t gsv2 = 924145;
  uint64_t txid = 0;

  std::string path = "/concat_test";
  std::string target_path = path + "/target";
  INode parent_inode;
  INode target_inode;
  std::vector<INode> part_inodes;
  std::vector<BlockInfoProto> target_inode_bips;
  std::vector<BlockProto> target_inode_bp;

  INodeInPath parent_iip;
  {
    inode_id++;
    parent_inode.set_id(inode_id);
    parent_inode.set_parent_id(kRootINodeId);
    parent_inode.set_name("concat_test");
    auto& permission = *parent_inode.mutable_permission();
    permission.set_username("tiger");
    permission.set_groupname("root");
    permission.set_permission(600);
    parent_inode.set_type(INode::kDirectory);
    parent_inode.set_mtime(1642649452000);
    parent_inode.set_atime(1642649453000);
    parent_iip.MutableInodeUnsafe() = parent_inode;
    TestOrderedCommitINodes({&spark_inode_}, {&root_inode}, ++txid);
  }

  {
    inode_id++;

    target_inode.set_id(inode_id);
    target_inode.set_parent_id(parent_inode.id());
    target_inode.set_name("target");
    auto& inode_permission = *target_inode.mutable_permission();
    inode_permission.set_username("root");
    inode_permission.set_groupname("tiger");
    inode_permission.set_permission(777);
    target_inode.set_type(INode::kFile);
    target_inode.set_mtime(1642649454000);
    target_inode.set_atime(1642649455000);
    target_inode.set_status(INode::kFileComplete);

    block_id++;
    gsv2++;
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kComplete);
    bip1.set_block_id(block_id);
    bip1.set_gen_stamp(gsv2);
    bip1.set_num_bytes(4096);
    bip1.set_inode_id(target_inode.id());
    bip1.set_expected_rep(2);
    bip1.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);
    if (NameSpace::IsAccMode()) {
      bip1.set_pufs_name(target_path.substr(1));
      bip1.set_pufs_offset(0);
    }

    BlockProto bp1;
    bp1.set_blockid(bip1.block_id());
    bp1.set_genstamp(bip1.gen_stamp());
    bp1.set_numbytes(bip1.num_bytes());

    target_inode.add_blocks()->CopyFrom(bp1);
    // Insert BlockMapSlice::uc_states_.
    block_manager_->AddBlock(
        Block(bip1.block_id(), bip1.num_bytes(), bip1.gen_stamp()),
        bip1.inode_id(),
        kInvalidINodeId,
        3,
        bip1.write_mode(),
        std::vector<DatanodeID>{},
        BlockUCState::kComplete);

    TestOrderedCommitINodes(
        {&target_inode}, {&parent_iip.MutableInode()}, ++txid);
    meta_storage_->PutBlockInfo(bip1, nullptr, ++txid, nullptr);
    target_inode_bips.push_back(bip1);
    target_inode_bp.push_back(bp1);
  }

  for (int i = 0; i < 10; ++i) {
    INode part_inode;

    inode_id++;
    part_inode.set_id(inode_id);
    part_inode.set_parent_id(parent_inode.id());
    part_inode.set_name("part" + std::to_string(i));
    auto& inode_permission = *part_inode.mutable_permission();
    inode_permission.set_username("root");
    inode_permission.set_groupname("tiger");
    inode_permission.set_permission(777);
    part_inode.set_type(INode::kFile);
    part_inode.set_mtime(1642649454000);
    part_inode.set_atime(1642649455000);
    part_inode.set_status(INode::kFileComplete);

    std::string part_path = path + "/" + part_inode.name();

    block_id++;
    gsv2++;
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kComplete);
    bip1.set_block_id(block_id);
    bip1.set_gen_stamp(gsv2);
    bip1.set_num_bytes(4096);
    bip1.set_inode_id(part_inode.id());
    bip1.set_expected_rep(2);
    bip1.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);
    if (NameSpace::IsAccMode()) {
      bip1.set_pufs_name(part_path.substr(1));
      bip1.set_pufs_offset(0);
    }

    BlockProto bp1;
    bp1.set_blockid(bip1.block_id());
    bp1.set_genstamp(bip1.gen_stamp());
    bp1.set_numbytes(bip1.num_bytes());

    part_inode.add_blocks()->CopyFrom(bp1);
    // Insert BlockMapSlice::uc_states_.
    block_manager_->AddBlock(
        Block(bip1.block_id(), bip1.num_bytes(), bip1.gen_stamp()),
        bip1.inode_id(),
        kInvalidINodeId,
        3,
        bip1.write_mode(),
        std::vector<DatanodeID>{},
        BlockUCState::kComplete);

    TestOrderedCommitINodes(
        {&part_inode}, {&parent_iip.MutableInode()}, ++txid);
    meta_storage_->PutBlockInfo(bip1, nullptr, ++txid, nullptr);

    part_inodes.push_back(part_inode);
    target_inode_bips.push_back(bip1);
    target_inode_bp.push_back(bp1);
  }

  meta_storage_->WaitNoPending();

  NameSpace::DoConcatFileToTarget(
      part_inodes, &target_inode_bips, &target_inode);
  {
    FileToBeConcat proto;
    proto.set_target_path(path + "/" + target_inode.name());
    for (const auto& part_inode : part_inodes) {
      proto.add_src_paths(path + "/" + part_inode.name());
    }
    proto.set_parent_path(path);
    proto.mutable_target_inode()->CopyFrom(target_inode);
    for (const auto& part_inode : part_inodes) {
      proto.add_src_inodes()->CopyFrom(part_inode);
    }
    for (const auto& inode_bip : target_inode_bips) {
      proto.add_target_bips()->CopyFrom(inode_bip);
    }
    proto.mutable_parent()->CopyFrom(parent_inode);
    proto.set_timestamp_in_ms(1642649454000 + 10);

    auto op = std::make_shared<OpConcatV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(++txid);
    ns_->Apply(op);
    ns_->WaitNoPending();

    INode inode;
    EXPECT_EQ(
        meta_storage_->GetINode(parent_inode.id(), target_inode.name(), &inode),
        kOK);
    LOG(INFO) << "inode.ShortDebugString()=" << inode.ShortDebugString();
    EXPECT_EQ(inode.blocks_size(), 11);
    for (int i = 0; i < inode.blocks().size(); ++i) {
      EXPECT_EQ(inode.blocks(i).SerializeAsString(),
                target_inode_bp[i].SerializeAsString());

      BlockInfoProto bip;
      EXPECT_TRUE(meta_storage_->GetBlockInfo(inode.blocks(i).blockid(), &bip));
      EXPECT_EQ(bip.block_id(), inode.blocks(i).blockid());
      EXPECT_EQ(bip.gen_stamp(), inode.blocks(i).genstamp());
      EXPECT_EQ(bip.num_bytes(), inode.blocks(i).numbytes());
      EXPECT_EQ(bip.inode_id(), inode.id());
      if (NameSpace::IsAccMode()) {
        EXPECT_EQ(bip.pufs_name(), target_path.substr(1));
        EXPECT_EQ(bip.pufs_offset(), i * 4096);
      }
    }
  }
}

TEST_F(ApplyCfsOpTest, ConcatV2Acc) {
  FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
  INodeID inode_id = kLastReservedINodeId + 10;
  BlockID block_id = 1074664860;
  uint64_t gsv2 = 924145;
  uint64_t txid = 0;

  std::string path = "/concat_test";
  std::string target_path = path + "/target";
  INode parent_inode;
  INode target_inode;
  std::vector<INode> part_inodes;
  std::vector<BlockInfoProto> target_inode_bips;
  std::vector<BlockProto> target_inode_bp;

  INodeInPath parent_iip;
  {
    inode_id++;
    parent_inode.set_id(inode_id);
    parent_inode.set_parent_id(kRootINodeId);
    parent_inode.set_name("concat_test");
    auto& permission = *parent_inode.mutable_permission();
    permission.set_username("tiger");
    permission.set_groupname("root");
    permission.set_permission(600);
    parent_inode.set_type(INode::kDirectory);
    parent_inode.set_mtime(1642649452000);
    parent_inode.set_atime(1642649453000);
    parent_iip.MutableInodeUnsafe() = parent_inode;
    TestOrderedCommitINodes({&spark_inode_}, {&parent_inode}, ++txid);
  }

  {
    inode_id++;

    target_inode.set_id(inode_id);
    target_inode.set_parent_id(parent_inode.id());
    target_inode.set_name("target");
    auto& inode_permission = *target_inode.mutable_permission();
    inode_permission.set_username("root");
    inode_permission.set_groupname("tiger");
    inode_permission.set_permission(777);
    target_inode.set_type(INode::kFile);
    target_inode.set_mtime(1642649454000);
    target_inode.set_atime(1642649455000);
    target_inode.set_status(INode::kFileComplete);

    auto ufs_info = target_inode.mutable_ufs_file_info();
    ufs_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
    ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
    ufs_info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeAsync);
    ufs_info->set_key(target_path.substr(1));
    ufs_info->set_etag("");
    ufs_info->set_size(0);
    ufs_info->set_last_modified_ts(1642649455000);
    ufs_info->set_sync_ts(1642649455000);
    CHECK(target_inode.IsInitialized())
        << target_inode.InitializationErrorString();

    block_id++;
    gsv2++;
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kComplete);
    bip1.set_block_id(block_id);
    bip1.set_gen_stamp(gsv2);
    bip1.set_num_bytes(4096);
    bip1.set_inode_id(target_inode.id());
    bip1.set_expected_rep(2);
    bip1.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);
    if (NameSpace::IsAccMode()) {
      bip1.set_pufs_name(target_path.substr(1));
      bip1.set_pufs_offset(0);
    }
    CHECK(bip1.IsInitialized()) << bip1.InitializationErrorString();

    BlockProto bp1;
    bp1.set_blockid(bip1.block_id());
    bp1.set_genstamp(bip1.gen_stamp());
    bp1.set_numbytes(bip1.num_bytes());

    target_inode.add_blocks()->CopyFrom(bp1);

    TestOrderedCommitINodes(
        {&target_inode}, {&parent_iip.MutableInode()}, ++txid);
    meta_storage_->PutBlockInfo(bip1, nullptr, ++txid, nullptr);

    target_inode_bips.push_back(bip1);
    target_inode_bp.push_back(bp1);
  }

  for (int i = 0; i < 10; ++i) {
    INode part_inode;

    inode_id++;
    part_inode.set_id(inode_id);
    part_inode.set_parent_id(parent_inode.id());
    part_inode.set_name("part" + std::to_string(i));
    auto& inode_permission = *part_inode.mutable_permission();
    inode_permission.set_username("root");
    inode_permission.set_groupname("tiger");
    inode_permission.set_permission(777);
    part_inode.set_type(INode::kFile);
    part_inode.set_mtime(1642649454000);
    part_inode.set_atime(1642649455000);
    part_inode.set_status(INode::kFileComplete);

    std::string part_path = path + "/" + part_inode.name();

    auto ufs_info = part_inode.mutable_ufs_file_info();
    ufs_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
    ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
    ufs_info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeAsync);
    ufs_info->set_key(target_path.substr(1));
    ufs_info->set_etag("");
    ufs_info->set_size(4096);
    ufs_info->set_last_modified_ts(1642649455000);
    ufs_info->set_sync_ts(1642649455000);
    CHECK(part_inode.IsInitialized()) << part_inode.InitializationErrorString();

    block_id++;
    gsv2++;
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kComplete);
    bip1.set_block_id(block_id);
    bip1.set_gen_stamp(gsv2);
    bip1.set_num_bytes(4096);
    bip1.set_inode_id(part_inode.id());
    bip1.set_expected_rep(2);
    bip1.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);
    if (NameSpace::IsAccMode()) {
      bip1.set_pufs_name(part_path.substr(1));
      bip1.set_pufs_offset(0);
    }
    CHECK(bip1.IsInitialized()) << bip1.InitializationErrorString();

    BlockProto bp1;
    bp1.set_blockid(bip1.block_id());
    bp1.set_genstamp(bip1.gen_stamp());
    bp1.set_numbytes(bip1.num_bytes());

    part_inode.add_blocks()->CopyFrom(bp1);
    // Insert BlockMapSlice::uc_states_.
    block_manager_->AddBlock(
        Block(bip1.block_id(), bip1.num_bytes(), bip1.gen_stamp()),
        bip1.inode_id(),
        kInvalidINodeId,
        3,
        bip1.write_mode(),
        std::vector<DatanodeID>{},
        BlockUCState::kComplete);

    TestOrderedCommitINodes(
        {&part_inode}, {&parent_iip.MutableInode()}, ++txid);
    meta_storage_->PutBlockInfo(bip1, nullptr, ++txid, nullptr);

    part_inodes.push_back(part_inode);
    target_inode_bips.push_back(bip1);
    target_inode_bp.push_back(bp1);
  }

  meta_storage_->WaitNoPending();

  NameSpace::DoConcatFileToTarget(
      part_inodes, &target_inode_bips, &target_inode);
  {
    FileToBeConcat proto;
    proto.set_target_path(path + "/" + target_inode.name());
    for (const auto& part_inode : part_inodes) {
      proto.add_src_paths(path + "/" + part_inode.name());
    }
    proto.set_parent_path(path);
    proto.mutable_target_inode()->CopyFrom(target_inode);
    for (const auto& part_inode : part_inodes) {
      proto.add_src_inodes()->CopyFrom(part_inode);
    }
    for (const auto& part_inode_bip : target_inode_bips) {
      proto.add_target_bips()->CopyFrom(part_inode_bip);
    }
    proto.mutable_parent()->CopyFrom(parent_inode);
    proto.set_timestamp_in_ms(1642649454000 + 10);

    auto op = std::make_shared<OpConcatV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(++txid);
    ns_->Apply(op);
    ns_->WaitNoPending();

    INode inode;
    EXPECT_EQ(
        meta_storage_->GetINode(parent_inode.id(), target_inode.name(), &inode),
        kOK);
    LOG(INFO) << "inode.ShortDebugString()=" << inode.ShortDebugString();
    EXPECT_EQ(inode.blocks_size(), 11);
    EXPECT_EQ(inode.has_ufs_file_info(), true);
    for (int i = 0; i < inode.blocks().size(); ++i) {
      EXPECT_EQ(inode.blocks(i).SerializeAsString(),
                target_inode_bp[i].SerializeAsString());

      BlockInfoProto bip;
      EXPECT_TRUE(meta_storage_->GetBlockInfo(inode.blocks(i).blockid(), &bip));
      EXPECT_EQ(bip.block_id(), inode.blocks(i).blockid());
      EXPECT_EQ(bip.gen_stamp(), inode.blocks(i).genstamp());
      EXPECT_EQ(bip.num_bytes(), inode.blocks(i).numbytes());
      EXPECT_EQ(bip.inode_id(), inode.id());
      if (NameSpace::IsAccMode()) {
        EXPECT_EQ(bip.pufs_name(), target_path.substr(1));
        EXPECT_EQ(bip.pufs_offset(), i * 4096);
      }
    }
  }
}

TEST_F(ApplyCfsOpTest, CreateAddBlockWithLocations1) {
  FLAGS_edit_log_assigner_apply_mode =
      static_cast<int>(ApplyMode::PHYSICAL_CONCUR);

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn1");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname1");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn2");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname2");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  auto root_inode = meta_storage_->GetRootINode();

  {
    // create
    INode file;
    file.set_id(1);
    file.set_parent_id(16385);
    file.set_name("file");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    BlockProto blk;
    blk.set_blockid(1);
    blk.set_genstamp(1);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(1);
    bip.set_gen_stamp(1);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    FileToBeOpen proto;
    proto.set_path("/file");
    proto.mutable_inode()->CopyFrom(file);
    proto.set_overwrite(false);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    proto.mutable_parent()->CopyFrom(root_inode);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.add_add_block_bips()->CopyFrom(bip);
    auto bip_with_locs = proto.add_add_block_bips_with_locs();
    bip_with_locs->mutable_bip()->CopyFrom(bip);
    bip_with_locs->add_dns("dn1");
    bip_with_locs->add_dns("dn2");
    bip_with_locs->add_dns("dn3");
    proto.set_physical_applyable(true);
    auto op = std::make_shared<OpOpenFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(1);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_THAT(detailed_blk.storage_, testing::UnorderedElementsAre(1, 2));
  }

  {
    // addblock
    INode file;
    file.set_id(2);
    file.set_parent_id(16385);
    file.set_name("file2");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    auto pin = file.mutable_pin_status();
    pin->set_pinned(false);
    pin->set_ttl(-1);
    pin->set_txid(0);
    pin->set_recursive(false);
    TestOrderedInsertINode(file, 2, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode old_file = file;
    BlockProto blk;
    blk.set_blockid(2);
    blk.set_genstamp(2);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(2);
    bip.set_gen_stamp(2);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    BlockToBeAdd proto;
    proto.mutable_old_inode()->CopyFrom(old_file);
    proto.set_path("/file2");
    proto.mutable_inode()->CopyFrom(file);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.mutable_last_bip()->CopyFrom(bip);
    auto bip_with_locs = proto.mutable_last_bip_with_locs();
    bip_with_locs->mutable_bip()->CopyFrom(bip);
    bip_with_locs->add_dns("dn1");
    bip_with_locs->add_dns("dn2");
    bip_with_locs->add_dns("dn3");
    proto.set_physical_applyable(true);
    auto op = std::make_shared<OpAddBlockV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(3);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_THAT(detailed_blk.storage_, testing::UnorderedElementsAre(1, 2));
  }

  {
    // batch create
    INode dir;
    dir.set_id(3);
    dir.set_parent_id(16385);
    dir.set_name("dir");
    auto pd = dir.mutable_permission();
    pd->set_username("tiger");
    pd->set_groupname("root");
    pd->set_permission(0755);
    dir.set_type(INode::kDirectory);
    dir.set_mtime(1);
    dir.set_atime(2);
    auto pin_pd = dir.mutable_pin_status();
    pin_pd->set_pinned(false);
    pin_pd->set_ttl(-1);
    pin_pd->set_txid(0);
    pin_pd->set_recursive(false);
    TestOrderedInsertINode(dir, 4, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode file1;
    file1.set_id(4);
    file1.set_parent_id(3);
    file1.set_name("file3");
    auto p1 = file1.mutable_permission();
    p1->set_username("tiger");
    p1->set_groupname("root");
    p1->set_permission(0644);
    file1.set_type(INode::kFile);
    file1.set_mtime(1);
    file1.set_atime(2);
    BlockProto blk1;
    blk1.set_blockid(3);
    blk1.set_genstamp(3);
    blk1.set_numbytes(0);
    file1.add_blocks()->CopyFrom(blk1);
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kUnderConstruction);
    bip1.set_block_id(3);
    bip1.set_gen_stamp(3);
    bip1.set_num_bytes(0);
    bip1.set_inode_id(file1.id());
    bip1.set_expected_rep(2);
    INode file2;
    file2.set_id(5);
    file2.set_parent_id(3);
    file2.set_name("file4");
    auto p2 = file2.mutable_permission();
    p2->set_username("tiger");
    p2->set_groupname("root");
    p2->set_permission(0644);
    file2.set_type(INode::kFile);
    file2.set_mtime(1);
    file2.set_atime(2);
    BlockProto blk2;
    blk2.set_blockid(4);
    blk2.set_genstamp(4);
    blk2.set_numbytes(0);
    file2.add_blocks()->CopyFrom(blk2);
    BlockInfoProto bip2;
    bip2.set_state(BlockInfoProto::kUnderConstruction);
    bip2.set_block_id(4);
    bip2.set_gen_stamp(4);
    bip2.set_num_bytes(0);
    bip2.set_inode_id(file2.id());
    bip2.set_expected_rep(2);
    BatchInodeToCreate proto;
    auto a_file1 = proto.add_files();
    a_file1->set_path("/dir/file3");
    a_file1->mutable_inode()->CopyFrom(file1);
    a_file1->mutable_add_block_bips()->CopyFrom(bip1);
    auto a_file1_bip_with_locs = a_file1->add_add_block_bips_with_locs();
    a_file1_bip_with_locs->mutable_bip()->CopyFrom(bip1);
    a_file1_bip_with_locs->add_dns("dn1");
    a_file1_bip_with_locs->add_dns("dn2");
    a_file1_bip_with_locs->add_dns("dn3");
    auto a_file2 = proto.add_files();
    a_file2->set_path("/dir/file4");
    a_file2->mutable_inode()->CopyFrom(file2);
    a_file2->mutable_add_block_bips()->CopyFrom(bip2);
    auto a_file2_bip_with_locs = a_file2->add_add_block_bips_with_locs();
    a_file2_bip_with_locs->mutable_bip()->CopyFrom(bip2);
    a_file2_bip_with_locs->add_dns("dn1");
    a_file2_bip_with_locs->add_dns("dn2");
    a_file2_bip_with_locs->add_dns("dn3");
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    proto.mutable_parent()->CopyFrom(dir);
    proto.set_timestamp_in_ms(1);
    auto op = std::make_shared<OpBatchCreateFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(5);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_THAT(block_manager_->GetDetailedBlock(blk1).storage_,
                testing::UnorderedElementsAre(1, 2));
    EXPECT_THAT(block_manager_->GetDetailedBlock(blk2).storage_,
                testing::UnorderedElementsAre(1, 2));
  }

  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);
}

TEST_F(ApplyCfsOpTest, CreateAddBlockWithLocations2) {
  FLAGS_edit_log_assigner_apply_mode =
      static_cast<int>(ApplyMode::PHYSICAL_CONCUR);

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn1");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname1");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn2");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname2");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  auto root_inode = meta_storage_->GetRootINode();

  {
    // create
    INode file;
    file.set_id(1);
    file.set_parent_id(16385);
    file.set_name("file");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    BlockProto blk;
    blk.set_blockid(1);
    blk.set_genstamp(1);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(1);
    bip.set_gen_stamp(1);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    FileToBeOpen proto;
    proto.set_path("/file");
    proto.mutable_inode()->CopyFrom(file);
    proto.set_overwrite(false);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    proto.mutable_parent()->CopyFrom(root_inode);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.add_add_block_bips()->CopyFrom(bip);
    proto.set_physical_applyable(true);
    auto op = std::make_shared<OpOpenFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(1);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_TRUE(detailed_blk.storage_.empty());
  }

  {
    // addblock
    INode file;
    file.set_id(2);
    file.set_parent_id(16385);
    file.set_name("file2");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    auto pin = file.mutable_pin_status();
    pin->set_pinned(false);
    pin->set_ttl(-1);
    pin->set_txid(0);
    pin->set_recursive(false);
    TestOrderedInsertINode(file, 2, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode old_file = file;
    BlockProto blk;
    blk.set_blockid(2);
    blk.set_genstamp(2);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(2);
    bip.set_gen_stamp(2);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    BlockToBeAdd proto;
    proto.mutable_old_inode()->CopyFrom(old_file);
    proto.set_path("/file2");
    proto.mutable_inode()->CopyFrom(file);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.mutable_last_bip()->CopyFrom(bip);
    proto.set_physical_applyable(true);
    auto op = std::make_shared<OpAddBlockV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(3);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_TRUE(detailed_blk.storage_.empty());
  }

  {
    // batch create
    INode dir;
    dir.set_id(3);
    dir.set_parent_id(16385);
    dir.set_name("dir");
    auto pd = dir.mutable_permission();
    pd->set_username("tiger");
    pd->set_groupname("root");
    pd->set_permission(0755);
    dir.set_type(INode::kDirectory);
    dir.set_mtime(1);
    dir.set_atime(2);
    auto pin_pd = dir.mutable_pin_status();
    pin_pd->set_pinned(false);
    pin_pd->set_ttl(-1);
    pin_pd->set_txid(0);
    pin_pd->set_recursive(false);
    TestOrderedInsertINode(dir, 4, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode file1;
    file1.set_id(4);
    file1.set_parent_id(3);
    file1.set_name("file3");
    auto p1 = file1.mutable_permission();
    p1->set_username("tiger");
    p1->set_groupname("root");
    p1->set_permission(0644);
    file1.set_type(INode::kFile);
    file1.set_mtime(1);
    file1.set_atime(2);
    BlockProto blk1;
    blk1.set_blockid(3);
    blk1.set_genstamp(3);
    blk1.set_numbytes(0);
    file1.add_blocks()->CopyFrom(blk1);
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kUnderConstruction);
    bip1.set_block_id(3);
    bip1.set_gen_stamp(3);
    bip1.set_num_bytes(0);
    bip1.set_inode_id(file1.id());
    bip1.set_expected_rep(2);
    INode file2;
    file2.set_id(5);
    file2.set_parent_id(3);
    file2.set_name("file4");
    auto p2 = file2.mutable_permission();
    p2->set_username("tiger");
    p2->set_groupname("root");
    p2->set_permission(0644);
    file2.set_type(INode::kFile);
    file2.set_mtime(1);
    file2.set_atime(2);
    BlockProto blk2;
    blk2.set_blockid(4);
    blk2.set_genstamp(4);
    blk2.set_numbytes(0);
    file2.add_blocks()->CopyFrom(blk2);
    BlockInfoProto bip2;
    bip2.set_state(BlockInfoProto::kUnderConstruction);
    bip2.set_block_id(4);
    bip2.set_gen_stamp(4);
    bip2.set_num_bytes(0);
    bip2.set_inode_id(file2.id());
    bip2.set_expected_rep(2);
    BatchInodeToCreate proto;
    auto a_file1 = proto.add_files();
    a_file1->set_path("/dir/file3");
    a_file1->mutable_inode()->CopyFrom(file1);
    a_file1->mutable_add_block_bips()->CopyFrom(bip1);
    auto a_file2 = proto.add_files();
    a_file2->set_path("/dir/file4");
    a_file2->mutable_inode()->CopyFrom(file2);
    a_file2->mutable_add_block_bips()->CopyFrom(bip2);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    proto.mutable_parent()->CopyFrom(dir);
    proto.set_timestamp_in_ms(1);
    auto op = std::make_shared<OpBatchCreateFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(5);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(block_manager_->GetDetailedBlock(blk1).storage_.empty());
    EXPECT_TRUE(block_manager_->GetDetailedBlock(blk2).storage_.empty());
  }

  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);
}

TEST_F(ApplyCfsOpTest, CreateAddBlockWithLocations3) {
  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn1");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname1");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn2");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname2");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  auto root_inode = meta_storage_->GetRootINode();

  {
    // create
    INode file;
    file.set_id(1);
    file.set_parent_id(16385);
    file.set_name("file");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    BlockProto blk;
    blk.set_blockid(1);
    blk.set_genstamp(1);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(1);
    bip.set_gen_stamp(1);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    FileToBeOpen proto;
    proto.set_path("/file");
    proto.mutable_inode()->CopyFrom(file);
    proto.set_overwrite(false);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    proto.mutable_parent()->CopyFrom(root_inode);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.add_add_block_bips()->CopyFrom(bip);
    auto bip_with_locs = proto.add_add_block_bips_with_locs();
    bip_with_locs->mutable_bip()->CopyFrom(bip);
    bip_with_locs->add_dns("dn1");
    bip_with_locs->add_dns("dn2");
    bip_with_locs->add_dns("dn3");
    auto op = std::make_shared<OpOpenFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(1);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_THAT(detailed_blk.storage_, testing::UnorderedElementsAre(1, 2));
  }

  {
    // addblock
    INode file;
    file.set_id(2);
    file.set_parent_id(16385);
    file.set_name("file2");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    auto pin = file.mutable_pin_status();
    pin->set_pinned(false);
    pin->set_ttl(-1);
    pin->set_txid(0);
    pin->set_recursive(false);
    TestOrderedInsertINode(file, 2, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    BlockProto blk;
    blk.set_blockid(2);
    blk.set_genstamp(2);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(2);
    bip.set_gen_stamp(2);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    BlockToBeAdd proto;
    proto.set_path("/file2");
    proto.mutable_inode()->CopyFrom(file);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.mutable_last_bip()->CopyFrom(bip);
    auto bip_with_locs = proto.mutable_last_bip_with_locs();
    bip_with_locs->mutable_bip()->CopyFrom(bip);
    bip_with_locs->add_dns("dn1");
    bip_with_locs->add_dns("dn2");
    bip_with_locs->add_dns("dn3");
    auto op = std::make_shared<OpAddBlockV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(3);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_THAT(detailed_blk.storage_, testing::UnorderedElementsAre(1, 2));
  }

  {
    // batch create
    INode dir;
    dir.set_id(3);
    dir.set_parent_id(16385);
    dir.set_name("dir");
    auto pd = dir.mutable_permission();
    pd->set_username("tiger");
    pd->set_groupname("root");
    pd->set_permission(0755);
    dir.set_type(INode::kDirectory);
    dir.set_mtime(1);
    dir.set_atime(2);
    auto pin_pd = dir.mutable_pin_status();
    pin_pd->set_pinned(false);
    pin_pd->set_ttl(-1);
    pin_pd->set_txid(0);
    pin_pd->set_recursive(false);
    TestOrderedInsertINode(dir, 4, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode file1;
    file1.set_id(4);
    file1.set_parent_id(3);
    file1.set_name("file3");
    auto p1 = file1.mutable_permission();
    p1->set_username("tiger");
    p1->set_groupname("root");
    p1->set_permission(0644);
    file1.set_type(INode::kFile);
    file1.set_mtime(1);
    file1.set_atime(2);
    BlockProto blk1;
    blk1.set_blockid(3);
    blk1.set_genstamp(3);
    blk1.set_numbytes(0);
    file1.add_blocks()->CopyFrom(blk1);
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kUnderConstruction);
    bip1.set_block_id(3);
    bip1.set_gen_stamp(3);
    bip1.set_num_bytes(0);
    bip1.set_inode_id(file1.id());
    bip1.set_expected_rep(2);
    INode file2;
    file2.set_id(5);
    file2.set_parent_id(3);
    file2.set_name("file4");
    auto p2 = file2.mutable_permission();
    p2->set_username("tiger");
    p2->set_groupname("root");
    p2->set_permission(0644);
    file2.set_type(INode::kFile);
    file2.set_mtime(1);
    file2.set_atime(2);
    BlockProto blk2;
    blk2.set_blockid(4);
    blk2.set_genstamp(4);
    blk2.set_numbytes(0);
    file2.add_blocks()->CopyFrom(blk2);
    BlockInfoProto bip2;
    bip2.set_state(BlockInfoProto::kUnderConstruction);
    bip2.set_block_id(4);
    bip2.set_gen_stamp(4);
    bip2.set_num_bytes(0);
    bip2.set_inode_id(file2.id());
    bip2.set_expected_rep(2);
    BatchInodeToCreate proto;
    auto a_file1 = proto.add_files();
    a_file1->set_path("/dir/file3");
    a_file1->mutable_inode()->CopyFrom(file1);
    a_file1->mutable_add_block_bips()->CopyFrom(bip1);
    auto a_file1_bip_with_locs = a_file1->add_add_block_bips_with_locs();
    a_file1_bip_with_locs->mutable_bip()->CopyFrom(bip1);
    a_file1_bip_with_locs->add_dns("dn1");
    a_file1_bip_with_locs->add_dns("dn2");
    a_file1_bip_with_locs->add_dns("dn3");
    auto a_file2 = proto.add_files();
    a_file2->set_path("/dir/file4");
    a_file2->mutable_inode()->CopyFrom(file2);
    a_file2->mutable_add_block_bips()->CopyFrom(bip2);
    auto a_file2_bip_with_locs = a_file2->add_add_block_bips_with_locs();
    a_file2_bip_with_locs->mutable_bip()->CopyFrom(bip2);
    a_file2_bip_with_locs->add_dns("dn1");
    a_file2_bip_with_locs->add_dns("dn2");
    a_file2_bip_with_locs->add_dns("dn3");
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    proto.mutable_parent()->CopyFrom(dir);
    proto.set_timestamp_in_ms(1);
    auto op = std::make_shared<OpBatchCreateFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(5);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_THAT(block_manager_->GetDetailedBlock(blk1).storage_,
                testing::UnorderedElementsAre(1, 2));
    EXPECT_THAT(block_manager_->GetDetailedBlock(blk2).storage_,
                testing::UnorderedElementsAre(1, 2));
  }

  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);
}

TEST_F(ApplyCfsOpTest, CreateAddBlockWithLocations4) {
  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn1");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname1");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid("dn2");
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    reg.mutable_datanodeid()->set_hostname("hostname2");
    reg.mutable_datanodeid()->set_xferport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();
  }

  auto root_inode = meta_storage_->GetRootINode();

  {
    // create
    INode file;
    file.set_id(1);
    file.set_parent_id(16385);
    file.set_name("file");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    BlockProto blk;
    blk.set_blockid(1);
    blk.set_genstamp(1);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(1);
    bip.set_gen_stamp(1);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    FileToBeOpen proto;
    proto.set_path("/file");
    proto.mutable_inode()->CopyFrom(file);
    proto.set_overwrite(false);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    proto.mutable_parent()->CopyFrom(root_inode);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.add_add_block_bips()->CopyFrom(bip);
    auto op = std::make_shared<OpOpenFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(1);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_TRUE(detailed_blk.storage_.empty());
  }

  {
    // addblock
    INode file;
    file.set_id(2);
    file.set_parent_id(16385);
    file.set_name("file2");
    auto p = file.mutable_permission();
    p->set_username("tiger");
    p->set_groupname("root");
    p->set_permission(0644);
    file.set_type(INode::kFile);
    file.set_mtime(1);
    file.set_atime(2);
    auto pin = file.mutable_pin_status();
    pin->set_pinned(false);
    pin->set_ttl(-1);
    pin->set_txid(0);
    pin->set_recursive(false);
    TestOrderedInsertINode(file, 2, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    BlockProto blk;
    blk.set_blockid(2);
    blk.set_genstamp(2);
    blk.set_numbytes(0);
    file.add_blocks()->CopyFrom(blk);
    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kUnderConstruction);
    bip.set_block_id(2);
    bip.set_gen_stamp(2);
    bip.set_num_bytes(0);
    bip.set_inode_id(file.id());
    bip.set_expected_rep(2);
    BlockToBeAdd proto;
    proto.set_path("/file2");
    proto.mutable_inode()->CopyFrom(file);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    *proto.mutable_ancestors_id() = {ancs.begin(), ancs.end()};
    proto.mutable_last_bip()->CopyFrom(bip);
    auto op = std::make_shared<OpAddBlockV2>();
    op->SetProto(std::move(proto));
    op->SetTxid(3);
    ns_->Apply(op);
    ns_->WaitNoPending();
    auto detailed_blk = block_manager_->GetDetailedBlock(blk);
    EXPECT_TRUE(detailed_blk.storage_.empty());
  }

  {
    // batch create
    INode dir;
    dir.set_id(3);
    dir.set_parent_id(16385);
    dir.set_name("dir");
    auto pd = dir.mutable_permission();
    pd->set_username("tiger");
    pd->set_groupname("root");
    pd->set_permission(0755);
    dir.set_type(INode::kDirectory);
    dir.set_mtime(1);
    dir.set_atime(2);
    auto pin_pd = dir.mutable_pin_status();
    pin_pd->set_pinned(false);
    pin_pd->set_ttl(-1);
    pin_pd->set_txid(0);
    pin_pd->set_recursive(false);
    TestOrderedInsertINode(dir, 4, nullptr, &root_inode);
    meta_storage_->WaitNoPending();
    INode file1;
    file1.set_id(4);
    file1.set_parent_id(3);
    file1.set_name("file3");
    auto p1 = file1.mutable_permission();
    p1->set_username("tiger");
    p1->set_groupname("root");
    p1->set_permission(0644);
    file1.set_type(INode::kFile);
    file1.set_mtime(1);
    file1.set_atime(2);
    BlockProto blk1;
    blk1.set_blockid(3);
    blk1.set_genstamp(3);
    blk1.set_numbytes(0);
    file1.add_blocks()->CopyFrom(blk1);
    BlockInfoProto bip1;
    bip1.set_state(BlockInfoProto::kUnderConstruction);
    bip1.set_block_id(3);
    bip1.set_gen_stamp(3);
    bip1.set_num_bytes(0);
    bip1.set_inode_id(file1.id());
    bip1.set_expected_rep(2);
    INode file2;
    file2.set_id(5);
    file2.set_parent_id(3);
    file2.set_name("file4");
    auto p2 = file2.mutable_permission();
    p2->set_username("tiger");
    p2->set_groupname("root");
    p2->set_permission(0644);
    file2.set_type(INode::kFile);
    file2.set_mtime(1);
    file2.set_atime(2);
    BlockProto blk2;
    blk2.set_blockid(4);
    blk2.set_genstamp(4);
    blk2.set_numbytes(0);
    file2.add_blocks()->CopyFrom(blk2);
    BlockInfoProto bip2;
    bip2.set_state(BlockInfoProto::kUnderConstruction);
    bip2.set_block_id(4);
    bip2.set_gen_stamp(4);
    bip2.set_num_bytes(0);
    bip2.set_inode_id(file2.id());
    bip2.set_expected_rep(2);
    BatchInodeToCreate proto;
    auto a_file1 = proto.add_files();
    a_file1->set_path("/dir/file3");
    a_file1->mutable_inode()->CopyFrom(file1);
    a_file1->mutable_add_block_bips()->CopyFrom(bip1);
    auto a_file2 = proto.add_files();
    a_file2->set_path("/dir/file4");
    a_file2->mutable_inode()->CopyFrom(file2);
    a_file2->mutable_add_block_bips()->CopyFrom(bip2);
    proto.mutable_log_rpc_info()->CopyFrom(log_rpc_info_);
    std::vector<INodeID> ancs = {kRootINodeId};
    proto.mutable_parent()->CopyFrom(dir);
    proto.set_timestamp_in_ms(1);
    auto op = std::make_shared<OpBatchCreateFile>();
    op->SetProto(std::move(proto));
    op->SetTxid(5);
    ns_->Apply(op);
    ns_->WaitNoPending();
    EXPECT_TRUE(block_manager_->GetDetailedBlock(blk1).storage_.empty());
    EXPECT_TRUE(block_manager_->GetDetailedBlock(blk2).storage_.empty());
  }

  FLAGS_edit_log_assigner_apply_mode = static_cast<int>(ApplyMode::LOGICAL);
}

}  // namespace dancenn
