// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/27
// Description

#include <gtest/gtest.h>

#include <memory>
#include <sstream>

#include "edit/deserializer.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

bool LogEditLogTooLargeError(const std::string& editlog);

TEST(EditLogCfsOpTest, LogEditLogTooLargeError) {
  OpDelDepringBlks op;
  DepringBlksToBeDel blks;
  for (auto i = 0; i < 4 * 1024 * 1024; i++) {
    blks.add_dangling_blk_ids(i);
  }
  op.SetProto(blks);
  std::stringstream ss;
  op.WriteFields(&ss, true);
  EXPECT_TRUE(LogEditLogTooLargeError(ss.str()));
}

TEST(EditLogCfsOpTest, OpDelDepringBlks) {
  OpDelDepringBlks op;
  DepringBlksToBeDel blks;
  blks.add_dangling_blk_ids(1);
  BlockInfoProto* bip = blks.add_depred_bips();
  bip->set_state(BlockInfoProto::kComplete);
  bip->set_block_id(2);
  bip->set_gen_stamp(1000);
  bip->set_num_bytes(1024);
  bip->set_inode_id(kLastReservedINodeId + 1);
  bip->set_expected_rep(1);
  op.SetProto(blks);
  std::stringstream ss;
  op.WriteFields(&ss, true);
  std::dynamic_pointer_cast<OpDelDepringBlks>(
      OpDeSerializer().Deserialize(ss.str()));
}

}  // namespace dancenn
