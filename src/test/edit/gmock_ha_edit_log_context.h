// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/11
// Description

#ifndef TEST_EDIT_GMOCK_HA_EDIT_LOG_CONTEXT_H_
#define TEST_EDIT_GMOCK_HA_EDIT_LOG_CONTEXT_H_

#include <gmock/gmock.h>  // For MOCK_METHOD1, etc.
#include <jni.h>          // For jmethodID.

#include <cstdint>  // For int64_t, etc.
#include <memory>   // For shared_ptr.

#include "edit/committer.h"            // For BGEditLogCommitter.
#include "edit/ha_edit_log_context.h"  // For HAEditLogContext.
#include "edit/syncer.h"               // For BGEditLogSyncer.

namespace dancenn {

class GMockHAEditLogContext : public HAEditLogContext {
 public:
  GMockHAEditLogContext();

  MOCK_METHOD1(CallJavaOpVoidMethodL, int64_t(jmethodID method));
  MOCK_METHOD4(CallJavaOpMethod,
               int64_t(jmethodID method,
                       const std::string& msg,
                       bool with_rpc_id,
                       bool to_log_rpc_ids));
  MOCK_METHOD3(CallJavaOpMethodLL,
               int64_t(jmethodID method,
                       const std::string& msg,
                       uint64_t* value));

  MOCK_METHOD0(LogSyncAllInternal, void());

 public:
  // Not deprecated.
  const jmethodID method_log_cfs_op_;
  // Block related.
  const jmethodID method_log_allocate_block_id_;
  const jmethodID method_log_generation_stamp_v1_;
  const jmethodID method_log_generation_stamp_v2_;
  // Dir tree related.
  const jmethodID method_log_times_;

  // Deprecated.
  // File related.
  const jmethodID method_log_open_file_;
  const jmethodID method_log_add_block_;
  const jmethodID method_log_update_blocks_;
  const jmethodID method_log_close_file_;
  const jmethodID method_log_reassign_lease_;
  const jmethodID method_log_concat_;
  // Block related.
  const jmethodID method_log_set_cfs_universal_info_;
  // Dir tree related.
  const jmethodID method_log_mk_dir_;
  const jmethodID method_log_delete_;
  const jmethodID method_log_rename_old_;
  const jmethodID method_log_rename_;
  const jmethodID method_log_symlink_;
  // Set* related.
  const jmethodID method_log_set_replication_;
  const jmethodID method_log_set_storage_policy_;
  const jmethodID method_log_set_replica_policy_;
  const jmethodID method_log_set_dir_replica_policy_;
  const jmethodID method_log_set_quota_;
  const jmethodID method_log_set_permission_;
  const jmethodID method_log_set_owner_;
  const jmethodID method_log_set_acl_;
  const jmethodID method_log_set_xttrs_;
  const jmethodID method_log_remove_xattrs_;
  // Snapshot related.
  const jmethodID method_log_access_counters_snapshot_;
};

}  // namespace dancenn

#endif  // TEST_EDIT_GMOCK_HA_EDIT_LOG_CONTEXT_H_
