// Copyright 2017 He <PERSON> <<EMAIL>>

#include "edit/tailer.h"

#include <glog/logging.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <cstdint>
#include <random>
#include <string>

#include "ufs/tos/tos_cred_keeper.h"
#include "base/file_utils.h"
#include "base/path_util.h"
#include "test/dancenn_test_base.h"
#include "test/edit/java_const.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/inode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_string(all_datacenters);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class EditLogTailerTest : public dancenn::test::DanceNNCommonTest {
 public:
  void SetUp() override {
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_dfs_replication_max = 100;
    FLAGS_recycle_bin_enable = false;
    dancenn::test::DanceNNCommonTest::SetUp();
    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    block_manager_->set_datanode_manager(datanode_manager_);
    auto job_manager = std::make_shared<dancenn::JobManager>(datanode_manager_,
                                                             block_manager_);
    auto base = GetBase();
    auto db_path = base->GetDBDir();
    auto ctx = base->GetEditLogContext();
    auto meta_storage = base->GetMetaStorage();
    meta_storage->Shutdown();
    MockFSImageTransfer(db_path).Transfer();
    ns_ = std::make_shared<NameSpace>(db_path,
                                      ctx,
                                      block_manager_,
                                      datanode_manager_,
                                      job_manager,
                                      std::make_shared<DataCenters>(),
                                      nullptr,
                                      nullptr);
    ha_state_ = std::make_shared<MockHAState>();
    safemode_ = std::make_shared<MockSafeMode>();
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    block_manager_->set_ha_state(ha_state_.get());
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->set_ha_state(ha_state_.get());
    ns_->set_safemode(safemode_.get());
    ns_->StartStandby();
    auto last_applied_txid = ns_->GetLastCkptTxId();
    auto jvm = base->GetJVM();
    tailer_.reset(new EditLogTailer(
      last_applied_txid, jvm, ctx, ns_.get()));
    tailer_->Start();
  }

  StoragePolicyId GetRandomStoragePolicy(StoragePolicyId exclude) {
    std::vector<StoragePolicyId> storage_policy_ids;
    storage_policy_ids.push_back(kBlockStoragePolicyIdUnspecified);
    // storage_policy_ids.push_back(kMemoryStoragePolicy);
    storage_policy_ids.push_back(kOnlyDisk3StoragePolicy);
    storage_policy_ids.push_back(kOnlySSDStoragePolicy);
    storage_policy_ids.push_back(kAllSSDStoragePolicy);
    storage_policy_ids.push_back(kOnlyDisk2StoragePolicy);
    storage_policy_ids.push_back(kOneSSD2StoragePolicy);
    storage_policy_ids.push_back(kOneSSDStoragePolicy);
    storage_policy_ids.push_back(kHotStoragePolicy);
    storage_policy_ids.push_back(kWarmStoragePolicy);
    storage_policy_ids.push_back(kColdStoragePolicy);

    std::random_device rd;
    std::mt19937 g(rd());
    std::shuffle(storage_policy_ids.begin(), storage_policy_ids.end(), g);

    for (const auto &spid : storage_policy_ids) {
      if (spid != exclude) {
        return spid;
      }
    }
    return kBlockStoragePolicyIdUnspecified;
  }

  void TearDown() override {
    ns_->StopStandby();
    tailer_.reset();
    datanode_manager_.reset();
    block_manager_.reset();
    ns_.reset();
    ha_state_.reset();
    safemode_.reset();
    dancenn::test::DanceNNCommonTest::TearDown();
  }

  std::unique_ptr<EditLogTailer> tailer_;

  std::shared_ptr<HAStateBase> ha_state_;
  std::shared_ptr<SafeModeBase> safemode_;
  std::shared_ptr<NameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
};

TEST_F(EditLogTailerTest, TestApplyAddWithAttr) {
  std::string path = "/file_with_attr";
  std::vector<cnetpp::base::StringPiece> path_components;
  SplitPath(path, &path_components);
  INode file;
  uint64_t inode_id = ns_->NextINodeId();

  // open file without overwrite
  std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
  op_add->SetOpCode(OP_ADD);
  uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
  op_add->set_inodeId(inode_id);
  op_add->SetTxid(tx_id);
  op_add->set_path(path);
  op_add->set_replication(5);
  op_add->set_mtime(123456);
  op_add->set_atime(123456);
  op_add->set_blockSize(1234567890);
  PermissionStatus ps;
  ps.set_username("root");
  ps.set_groupname("root");
  uint16_t perm = FsPermission::GetFileDefault().ToShort();
  ps.set_permission(perm);
  op_add->set_permissions(ps);
  op_add->set_clientName("mock_clientname");
  op_add->set_clientMachine("mock_clientmachine");
  op_add->set_overwrite(false);
  op_add->set_storagePolicyId(7);
  op_add->set_clientId("mock_clientid");
  op_add->set_callId(1024);

  cloudfs::XAttrEditLogProto editLogProto;
  editLogProto.set_src(path);
  auto attr_hyper_file = editLogProto.add_xattrs();
  attr_hyper_file->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  attr_hyper_file->set_name("TestKey");
  attr_hyper_file->set_value("TestValue");
  op_add->set_xAttrs(editLogProto);

  tailer_->Apply(op_add);
  tailer_->WaitNoPending(true);
  ns_->WaitNoPending();
  ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
  ASSERT_EQ(file.id(), inode_id);
  ASSERT_EQ(file.xattrs_size(), 1);
  ASSERT_EQ(file.xattrs(0).name(), "TestKey");
  ASSERT_EQ(file.xattrs(0).value(), "TestValue");
}

#if 0
TEST_F(EditLogTailerTest, RenameHyperFile) {
  std::string path = "/rename_hyperfile_apply";
  std::vector<cnetpp::base::StringPiece> path_components;
  SplitPath(path, &path_components);
  INode file;

  std::vector<std::string> hyperblock_suffixs = {"_part000_00000000",
                                                 "_part001_11111111"};
  {
    uint64_t inode_id = ns_->NextINodeId();
    CompactBlockArray cb = CompactBlockArray();

    // open file without overwrite
    std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
    op_add->SetOpCode(OP_ADD);
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(tx_id);
    op_add->set_path(path);
    op_add->set_replication(5);
    op_add->set_mtime(123456);
    op_add->set_atime(123456);
    op_add->set_blockSize(1234567890);
    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_add->set_permissions(ps);
    op_add->set_clientName("mock_clientname");
    op_add->set_clientMachine("mock_clientmachine");
    op_add->set_overwrite(false);
    op_add->set_storagePolicyId(7);
    op_add->set_clientId("mock_clientid");
    op_add->set_callId(1024);

    HyperCacheMeta hyper_file_meta;
    hyper_file_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);
    for (auto hyperblock_suffix : hyperblock_suffixs) {
      hyper_file_meta.add_hyperblock(hyperblock_suffix);
    }
    hyper_file_meta.set_stripewidth(1024 * 1024);
    cloudfs::XAttrEditLogProto editLogProto;
    editLogProto.set_src(path);

    auto attr_hyper_file = editLogProto.add_xattrs();
    attr_hyper_file->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
    attr_hyper_file->set_name(kHyperFileKey);
    attr_hyper_file->set_value(hyper_file_meta.SerializeAsString());

    op_add->set_xAttrs(editLogProto);

    tailer_->Apply(op_add);

    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.id(), inode_id);
    ASSERT_EQ(file.parent_id(), kRootINodeId);
  }

  std::vector<std::string> hyperblocks = {"/rename_hyperfile_apply_part000_00000000",
                                          "/rename_hyperfile_apply_part001_11111111"};

  {
    int i=0;
    for (auto hyper_block_name : hyperblocks) {
      uint64_t inode_id = ns_->NextINodeId();
      CompactBlockArray cb = CompactBlockArray();

      std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
      op_add->SetOpCode(OP_ADD);
      uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
      op_add->set_inodeId(inode_id);
      op_add->SetTxid(tx_id);
      op_add->set_path(hyper_block_name);
      op_add->set_replication(5);
      op_add->set_mtime(123456);
      op_add->set_atime(123456);
      op_add->set_blockSize(1234567890);
      PermissionStatus ps;
      ps.set_username("root");
      ps.set_groupname("root");
      uint16_t perm = FsPermission::GetFileDefault().ToShort();
      ps.set_permission(perm);
      op_add->set_permissions(ps);
      op_add->set_clientName("mock_clientname");
      op_add->set_clientMachine("mock_clientmachine");
      op_add->set_overwrite(false);
      op_add->set_storagePolicyId(7);
      op_add->set_clientId("mock_clientid");
      i++;
      op_add->set_callId(1024 + i);

      HyperCacheMeta hyper_block_meta;
      hyper_block_meta.set_mode(cloudfs::HyperFileMode::PIPELINE_MANUAL);

      cloudfs::XAttrEditLogProto editLogProto;
      editLogProto.set_src(hyper_block_name);

      auto attr_hyper_file = editLogProto.add_xattrs();
      attr_hyper_file->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
      attr_hyper_file->set_name(kHyperBlockKey);
      attr_hyper_file->set_value(hyper_block_meta.SerializeAsString());

      DLOG(WARNING) << editLogProto.ShortDebugString();
      op_add->set_xAttrs(editLogProto);
      tailer_->Apply(op_add);
      tailer_->WaitNoPending(true);
      ns_->WaitNoPending();
    }
  }

  // rename
  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);

    std::vector<std::string> hyper_block_names_before = {"/rename_hyperfile_apply_part000_00000000",
                                                         "/rename_hyperfile_apply_part001_11111111"};
    // Verify the hyperblock exists before rename.
    for (auto& expected_hyper_block_name: hyper_block_names_before) {
      std::vector<cnetpp::base::StringPiece> rename_hyperblock_src_path_components;
      SplitPath(expected_hyper_block_name, &rename_hyperblock_src_path_components);
      INode rename_hyperblock_src_inode;
      ASSERT_EQ(ns_->GetLastINodeInPath(rename_hyperblock_src_path_components,
                                        &rename_hyperblock_src_inode), StatusCode::kOK);
      ASSERT_TRUE(ns_->IsHyperBlock(rename_hyperblock_src_inode));
    }

    std::string rename_dst_path = "/rename_hyperfile_dst";
    auto op_rename = std::make_shared<OpRename>();
    op_rename->SetOpCode(OP_RENAME_OLD);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(path);
    op_rename->set_dst(rename_dst_path);
    tailer_->Apply(op_rename);
tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components;
    SplitPath(rename_dst_path, &rename_dst_path_components);
    INode rename_dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components,
                                      &rename_dst_inode), StatusCode::kOK);
    ASSERT_EQ(file.id(), rename_dst_inode.id());

    // Verify the dst_hyperblock exists after rename.
    std::vector<std::string> expected_hyper_block_names = {"/rename_hyperfile_dst_part000_00000000",
                                                           "/rename_hyperfile_dst_part001_11111111"};
    for (auto& expected_hyper_block_name: expected_hyper_block_names) {
      std::vector<cnetpp::base::StringPiece> rename_hyperblock_dst_path_components;
      SplitPath(expected_hyper_block_name, &rename_hyperblock_dst_path_components);
      INode rename_hyperblock_dst_inode;
      ASSERT_EQ(ns_->GetLastINodeInPath(rename_hyperblock_dst_path_components,
                                        &rename_hyperblock_dst_inode), StatusCode::kOK);
      ASSERT_TRUE(ns_->IsHyperBlock(rename_hyperblock_dst_inode));
    }

    // Verify the src_hyperblock doesn't exist after rename.
    for (auto& expected_hyper_block_name: hyper_block_names_before) {
      std::vector<cnetpp::base::StringPiece> rename_hyperblock_src_path_components;
      SplitPath(expected_hyper_block_name, &rename_hyperblock_src_path_components);
      INode rename_hyperblock_src_inode;
      ASSERT_EQ(ns_->GetLastINodeInPath(rename_hyperblock_src_path_components,
                                        &rename_hyperblock_src_inode), StatusCode::kFileNotFound);
    }
  }
}
#endif

TEST_F(EditLogTailerTest, FileRelated) {
  std::string path = "/edit_log_tail_test_op_apply";
  std::vector<cnetpp::base::StringPiece> path_components;
  SplitPath(path, &path_components);
  INode file;

  {
    uint64_t inode_id = ns_->NextINodeId();
    CompactBlockArray cb = CompactBlockArray();

    // open file without overwrite
    std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
    op_add->SetOpCode(OP_ADD);
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(tx_id);
    op_add->set_path(path);
    op_add->set_replication(5);
    op_add->set_mtime(123456);
    op_add->set_atime(123456);
    op_add->set_blockSize(1234567890);
    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_add->set_permissions(ps);
    op_add->set_clientName("mock_clientname");
    op_add->set_clientMachine("mock_clientmachine");
    op_add->set_overwrite(false);
    op_add->set_storagePolicyId(7);
    op_add->set_clientId("mock_clientid");
    op_add->set_callId(1024);
    tailer_->Apply(op_add);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.id(), inode_id);
    ASSERT_EQ(file.parent_id(), kRootINodeId);
    ASSERT_EQ(file.replication(), 5);
    ASSERT_EQ(file.mtime(), 123456);
    ASSERT_EQ(file.atime(), 123456);
    ASSERT_EQ(file.status(), INode_Status_kFileUnderConstruction);
    ASSERT_EQ(file.uc().client_name(), "mock_clientname");
    ASSERT_EQ(file.uc().client_machine(), "mock_clientmachine");
    ASSERT_EQ(file.permission().username(), "root");
    ASSERT_EQ(file.permission().groupname(), "root");
    ASSERT_EQ(file.permission().permission(), perm);
    ASSERT_EQ(file.storage_policy_id(), 7);

    // open file with overwrite
    op_add->set_overwrite(true);
    op_add->set_mtime(1234567);
    op_add->set_atime(1234567);
    op_add->set_blockSize(102400);
    inode_id = ns_->NextINodeId();
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(ns_->GetLastCkptTxId() + 1);
    tailer_->Apply(op_add);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.id(), inode_id);
    ASSERT_EQ(file.mtime(), 1234567);
    ASSERT_EQ(file.atime(), 1234567);
    ASSERT_EQ(file.blocks_size(), 0);
    ASSERT_EQ(file.status(), INode_Status_kFileUnderConstruction);

    // add block to an open file
    std::shared_ptr<OpAddBlock> op_add_block = std::make_shared<OpAddBlock>();
    op_add_block->SetOpCode(OP_ADD_BLOCK);
    op_add_block->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_add_block->set_path(path);
    cloudfs::BlockProto bp0 = cloudfs::BlockProto();
    bp0.set_blockid(1000001);
    bp0.set_genstamp(2000001);
    bp0.set_numbytes(102400);
    cb.push_back(bp0);
    op_add_block->set_blocks(cb);
    op_add_block->set_clientId("mock_clientid");
    op_add_block->set_callId(1001);
    tailer_->Apply(op_add_block);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.blocks().size(), 1);
    ASSERT_EQ(file.blocks().Get(0).blockid(), 1000001);
    ASSERT_EQ(file.blocks().Get(0).genstamp(), 2000001);
    ASSERT_EQ(file.blocks().Get(0).numbytes(), 102400);
    ASSERT_EQ(file.status(), INode_Status_kFileUnderConstruction);

    // add another block
    op_add_block->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_add_block->set_path(path);
    cloudfs::BlockProto bp1 = cloudfs::BlockProto();
    bp1.set_blockid(1000002);
    bp1.set_genstamp(2000002);
    bp1.set_numbytes(102400);
    cb.clear();
    cb.push_back(bp0);
    cb.push_back(bp1);
    op_add_block->set_blocks(cb);
    op_add_block->set_clientId("mock_clientid");
    op_add_block->set_callId(1002);
    tailer_->Apply(op_add_block);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.blocks().size(), 2);
    ASSERT_EQ(file.blocks().Get(0).blockid(), 1000001);
    ASSERT_EQ(file.blocks().Get(0).genstamp(), 2000001);
    ASSERT_EQ(file.blocks().Get(0).numbytes(), 102400);
    ASSERT_EQ(file.blocks().Get(1).blockid(), 1000002);
    ASSERT_EQ(file.blocks().Get(1).genstamp(), 2000002);
    ASSERT_EQ(file.blocks().Get(1).numbytes(), 102400);
    ASSERT_EQ(file.status(), INode_Status_kFileUnderConstruction);

    // close file
    std::shared_ptr<OpClose> op_close = std::make_shared<OpClose>();
    op_close->SetOpCode(OP_CLOSE);
    op_close->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_close->set_inodeId(inode_id);
    op_close->set_path(path);
    op_close->set_atime(1234568);
    op_close->set_mtime(1234568);
    cb.clear();
    cb.push_back(bp0);
    cb.push_back(bp1);
    op_close->set_blocks(cb);
    tailer_->Apply(op_close);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.id(), inode_id);
    ASSERT_EQ(file.mtime(), 1234568);
    ASSERT_EQ(file.atime(), 1234568);
    ASSERT_EQ(file.status(), INode_Status_kFileComplete);

    // append to an existing closed file
    tx_id = ns_->GetLastCkptTxId() + 1;
    op_add->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_add->set_overwrite(false);
    cb.clear();
    cb.push_back(bp0);
    cb.push_back(bp1);
    op_add->set_blocks(cb);
    tailer_->Apply(op_add);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.blocks_size(), 2);
    ASSERT_EQ(file.status(), INode_Status_kFileUnderConstruction);

    // close file
    op_close->SetTxid(ns_->GetLastCkptTxId() + 1);
    cb.clear();
    cb.push_back(bp0);
    cb.push_back(bp1);
    op_close->set_blocks(cb);
    tailer_->Apply(op_close);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.blocks_size(), 2);
    ASSERT_EQ(file.status(), INode_Status_kFileComplete);
  }

  // set replication
  {
    path_components.clear();
    SplitPath(path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    auto origin_replica = file.replication();
    auto new_replica = origin_replica + 10;
    auto op_set_replica = std::make_shared<OpSetReplication>();
    op_set_replica->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_set_replica->SetOpCode(OP_SET_REPLICATION);
    op_set_replica->set_path(path);
    op_set_replica->set_replication(new_replica);
    tailer_->Apply(op_set_replica);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.replication(), new_replica);
  }

  // set permission
  {
    auto origin_permission = file.permission().permission();
    auto op_permission = std::make_shared<OpSetPermissions>();
    op_permission->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_permission->SetOpCode(OP_SET_PERMISSIONS);
    op_permission->set_src(path);
    op_permission->set_permissions(origin_permission + 1);
    tailer_->Apply(op_permission);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.permission().permission(), origin_permission + 1);
  }

  // set owner
  {
    auto origin_user = file.permission().username();
    auto origin_group = file.permission().groupname();
    auto op_owner = std::make_shared<OpSetOwner>();
    op_owner->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_owner->SetOpCode(OP_SET_OWNER);
    op_owner->set_src(path);
    op_owner->set_username("u_" + origin_user);
    op_owner->set_groupname("g_" + origin_group);
    tailer_->Apply(op_owner);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.permission().username(), "u_" + origin_user);
    ASSERT_EQ(file.permission().groupname(), "g_" + origin_group);
  }

  // set times
  {
    auto op_times = std::make_shared<OpTimes>();
    op_times->SetOpCode(OP_TIMES);
    op_times->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_times->set_path(path);
    auto origin_atime = file.atime();
    auto origin_mtime = file.mtime();
    op_times->set_atime(origin_atime);
    op_times->set_mtime(origin_mtime);
    tailer_->Apply(op_times);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.atime(), origin_atime);
    ASSERT_EQ(file.mtime(), origin_mtime);

    op_times->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_times->set_atime(origin_atime + 12345);
    tailer_->Apply(op_times);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.atime(), origin_atime + 12345);
    ASSERT_EQ(file.mtime(), origin_mtime);

    op_times->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_times->set_mtime(origin_mtime + 23456);
    tailer_->Apply(op_times);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.atime(), origin_atime + 12345);
    ASSERT_EQ(file.mtime(), origin_mtime + 23456);

    op_times->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_times->set_atime(origin_atime + 34567);
    op_times->set_mtime(origin_mtime + 45678);
    tailer_->Apply(op_times);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.atime(), origin_atime + 34567);
    ASSERT_EQ(file.mtime(), origin_mtime + 45678);
  }

  // set storage policy on file
  {
    auto op_set_storage_policy = std::make_shared<OpSetStoragePolicy>();
    op_set_storage_policy->SetOpCode(OP_SET_STORAGE_POLICY);
    op_set_storage_policy->set_path(path);

    auto ori_sp_id = file.storage_policy_id();
    auto new_sp_id = static_cast<uint32_t>(GetRandomStoragePolicy(
        static_cast<StoragePolicyId>(ori_sp_id)));
    op_set_storage_policy->set_policyId(new_sp_id);
    op_set_storage_policy->SetTxid(ns_->GetLastCkptTxId() + 1);
    tailer_->Apply(op_set_storage_policy);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);
    ASSERT_EQ(file.storage_policy_id(), new_sp_id);
  }

  // rename
  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);

    std::string rename_dst_path = "/edit_log_tail_test_op_rename";
    auto op_rename = std::make_shared<OpRenameOld>();
    op_rename->SetOpCode(OP_RENAME_OLD);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(path);
    op_rename->set_dst(rename_dst_path);
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components;
    SplitPath(rename_dst_path, &rename_dst_path_components);
    INode rename_dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components,
                                     &rename_dst_inode), StatusCode::kOK);
    ASSERT_EQ(file.id(), rename_dst_inode.id());

    path = rename_dst_path;
    SplitPath(path, &path_components);
  }

  // rename2
  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file), StatusCode::kOK);

    std::string rename2_dst_path = "/edit_log_tail_test_op_rename2";
    auto op_rename = std::make_shared<OpRename>();
    op_rename->SetOpCode(OP_RENAME);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(path);
    op_rename->set_dst(rename2_dst_path);
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components;
    SplitPath(rename2_dst_path, &rename_dst_path_components);
    INode rename_dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components,
                                     &rename_dst_inode), StatusCode::kOK);
    ASSERT_EQ(file.id(), rename_dst_inode.id());

    std::string rename2_dst_path2 = "/edit_log_tail_test_op_rename3";
    std::shared_ptr<INode> rename_dst_inode_2 = std::make_shared<INode>();
    rename_dst_inode_2->CopyFrom(rename_dst_inode);
    rename_dst_inode_2->set_id(ns_->NextINodeId());
    rename_dst_inode_2->set_name("edit_log_tail_test_op_rename3");
    ns_->meta_storage()->InsertINode(rename_dst_inode_2);

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components2;
    SplitPath(rename2_dst_path2, &rename_dst_path_components2);
    INode rename_dst_inode3;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components2,
                                     &rename_dst_inode3), StatusCode::kOK);

    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(rename2_dst_path);
    op_rename->set_dst(rename2_dst_path2);
    op_rename->set_options(RenameOptions(RenameOption::kOverwrite));
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    INode t;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components2,
                                     &t), StatusCode::kOK);
    ASSERT_EQ(t.id(), rename_dst_inode.id());

    path = rename2_dst_path2;
    SplitPath(path, &path_components);
  }

  // symlink
  {
    std::string symlink = "/edit_log_tail_test_op_symlink";
    std::vector<cnetpp::base::StringPiece> symlink_path_components;
    ASSERT_TRUE(SplitPath(symlink, &symlink_path_components));
    INode link;
    ASSERT_TRUE(ns_->GetLastINodeInPath(symlink_path_components, &link)
                    == StatusCode::kFileNotFound);

    auto op_symlink = std::make_shared<OpSymlink>();
    op_symlink->SetOpCode(OP_SYMLINK);
    op_symlink->SetTxid(ns_->GetLastCkptTxId() + 1);

    uint64_t inode_id = ns_->NextINodeId();
    op_symlink->set_inodeId(inode_id);
    op_symlink->set_path(symlink);
    op_symlink->set_value(path);
    op_symlink->set_mtime(9876789);
    op_symlink->set_atime(1234321);
    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_symlink->set_permissions(ps);

    tailer_->Apply(op_symlink);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    ASSERT_TRUE(ns_->GetLastINodeInPath(symlink_path_components, &link)
                    == StatusCode::kOK);
    ASSERT_EQ(link.type(), INode_Type_kSymLink);
    ASSERT_EQ(link.id(), inode_id);
    ASSERT_EQ(link.symlink(), path);
  }

  // ApplyOpReassignLease
  {
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    std::string origin_client = "origin_client";
    std::string mock_client = "new_client";
    file.mutable_uc()->set_client_name(origin_client);
    file.set_status(INode_Status_kFileUnderConstruction);
    std::shared_ptr<INode> shared_inode;
    shared_inode.reset(new INode(file));
    ns_->meta_storage()->InsertINode(shared_inode);
    ns_->lease_manager()->AddLease(origin_client, file.id());

    auto op_reassign_lease = std::make_shared<OpReassignLease>();
    op_reassign_lease->SetOpCode(OP_REASSIGN_LEASE);
    op_reassign_lease->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_reassign_lease->set_path(path);
    op_reassign_lease->set_leaseHolder(origin_client);
    op_reassign_lease->set_newHolder(mock_client);

    tailer_->Apply(op_reassign_lease);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.uc().client_name(), mock_client);
  }

  // ApplyOpUpdateBlocks
  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    LOG(INFO) << "file block size:" << file.blocks_size();
    auto origin_block_size = file.blocks_size();
    std::vector<::cloudfs::BlockProto> block_protos;

    for (size_t x = 0; x < origin_block_size - 1; x++) {
      auto block_info = file.blocks(x);
      block_manager_->AddBlock(block_info.blockid(),
                               0,
                               x,
                               block_info.numbytes(),
                               block_info.genstamp(),
                               3,
                               cloudfs::DATANODE_BLOCK,
                               BlockUCState::kComplete);
      block_protos.push_back(file.blocks(x));
    }
    auto op_update_blocks = std::make_shared<OpUpdateBlocks>();
    op_update_blocks->SetOpCode(OP_UPDATE_BLOCKS);
    op_update_blocks->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_update_blocks->set_path(path);
    op_update_blocks->set_blocks(block_protos);

    tailer_->Apply(op_update_blocks);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.blocks_size(), origin_block_size - 1);

    auto bp3 = ::cloudfs::BlockProto();
    bp3.set_blockid(1000003);
    bp3.set_genstamp(2000003);
    bp3.set_numbytes(102400);

    block_protos.push_back(bp3);
    op_update_blocks->set_blocks(block_protos);
    op_update_blocks->SetTxid(ns_->GetLastCkptTxId() + 1);
    tailer_->Apply(op_update_blocks);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.blocks_size(), origin_block_size);
  }

  // set xattr and remote xattr
  {
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    int origin_xattr_size = file.xattrs_size();
    std::string test_xattr_name = "hdfs.mock.xattr.test";

    auto op_set_xattr = std::make_shared<OpSetXattr>();
    op_set_xattr->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_set_xattr->SetOpCode(OP_SET_XATTR);

    XAttrProto x;
    x.set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
    x.set_name(test_xattr_name);
    x.set_value("1");

    cloudfs::XAttrEditLogProto xattrs;
    xattrs.set_src(path);
    xattrs.add_xattrs()->CopyFrom(x);
    op_set_xattr->set_xAttrs(xattrs);

    tailer_->Apply(op_set_xattr);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.xattrs_size(), origin_xattr_size + 1);
    ASSERT_EQ(file.mutable_xattrs()->Get(origin_xattr_size).namespace_(),
             ::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
    ASSERT_EQ(file.mutable_xattrs()->Get(origin_xattr_size).name(),
             test_xattr_name);
    ASSERT_EQ(file.mutable_xattrs()->Get(origin_xattr_size).value(), "1");

    op_set_xattr->SetTxid(ns_->GetLastCkptTxId() + 1);
    xattrs.clear_xattrs();
    x.set_value("2");
    xattrs.add_xattrs()->CopyFrom(x);
    op_set_xattr->set_xAttrs(xattrs);

    tailer_->Apply(op_set_xattr);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.xattrs_size(), origin_xattr_size + 1);
    ASSERT_EQ(file.mutable_xattrs()->Get(origin_xattr_size).value(), "2");

    // remove xattr
    auto op_rm_xattr = std::make_shared<OpRemoveXattr>();
    op_rm_xattr->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rm_xattr->SetOpCode(OP_REMOVE_XATTR);
    op_rm_xattr->set_xAttrs(xattrs);
    tailer_->Apply(op_rm_xattr);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(
      ns_->GetLastINodeInPath(path_components, &file),
      StatusCode::kOK);
    ASSERT_EQ(file.xattrs_size(), origin_xattr_size);
  }

  // delete
  {
    auto op_delete = std::make_shared<OpDelete>();
    op_delete->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_delete->SetOpCode(OP_DELETE);
    op_delete->set_path(path);
    op_delete->set_timestamp(987654321);
    tailer_->Apply(op_delete);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &file),
             StatusCode::kFileNotFound);
  }
}

TEST_F(EditLogTailerTest, DirectoryRelated) {
  std::string path = "/edit_log_tail_test_op_mkdir";
  std::vector<cnetpp::base::StringPiece> path_components;
  SplitPath(path, &path_components);
  INode dir;

  // open mkdir
  {
    std::shared_ptr<OpMkdir> op_mkdir = std::make_shared<OpMkdir>();
    op_mkdir->SetOpCode(OP_MKDIR);
    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_mkdir->set_inodeId(inode_id);
    op_mkdir->SetTxid(tx_id);
    op_mkdir->set_path(path);
    op_mkdir->set_mtime(123456);
    op_mkdir->set_atime(123457);

    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_mkdir->set_permissions(ps);

    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir),
             StatusCode::kFileNotFound);
    tailer_->Apply(op_mkdir);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir),
             StatusCode::kOK);
  }

  // set storage policy on directory
  {
    auto op_set_storage_policy = std::make_shared<OpSetStoragePolicy>();
    op_set_storage_policy->SetOpCode(OP_SET_STORAGE_POLICY);
    op_set_storage_policy->set_path(path);

    auto ori_sp_id = dir.storage_policy_id();
    auto new_sp_id = static_cast<uint32_t>(GetRandomStoragePolicy(
        static_cast<StoragePolicyId>(ori_sp_id)));
    DLOG(INFO) << "set storage policy on directory, origin storage policy id: "
               << ori_sp_id << ", new storage policy id: " << new_sp_id;
    op_set_storage_policy->set_policyId(new_sp_id);
    op_set_storage_policy->SetTxid(ns_->GetLastCkptTxId() + 1);
    tailer_->Apply(op_set_storage_policy);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir), StatusCode::kOK);
    ASSERT_EQ(dir.storage_policy_id(), new_sp_id);
  }

  // set replica policy
  {
    auto op_set_replica_policy = std::make_shared<OpSetReplicaPolicy>();
    op_set_replica_policy->SetOpCode(OP_SET_REPLICA_POLICY);
    op_set_replica_policy->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_set_replica_policy->set_path(path);
    GetReplicaPolicyResponseProto resp;
    int32_t new_replica_policy = 0;
    op_set_replica_policy->set_id(new_replica_policy);
    tailer_->Apply(op_set_replica_policy);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir), StatusCode::kOK);
    resp.Clear();
    ASSERT_FALSE(ns_->GetReplicaPolicy(path, &resp).HasException());
    ASSERT_EQ(resp.id(), new_replica_policy);
  }

  {
    auto op_set_dir_replica_policy = std::make_shared<OpSetDirReplicaPolicy>();
    op_set_dir_replica_policy->SetOpCode(OP_SET_DIR_REPLICA_POLICY);
    op_set_dir_replica_policy->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_set_dir_replica_policy->set_path(path);
    op_set_dir_replica_policy->set_dc("LF,HL");
    GetReplicaPolicyResponseProto resp;
    int32_t new_replica_policy = 1;
    op_set_dir_replica_policy->set_id(new_replica_policy);
    tailer_->Apply(op_set_dir_replica_policy);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir), StatusCode::kOK);
    resp.Clear();
    ASSERT_FALSE(ns_->GetReplicaPolicy(path, &resp).HasException());
    ASSERT_EQ(resp.id(), new_replica_policy);
    ASSERT_EQ(resp.dc(), "LF,HL");
  }

  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir), StatusCode::kOK);

    std::string rename_dst_dir = "/edit_log_tail_test_op_rename";
    auto op_rename = std::make_shared<OpRenameOld>();
    op_rename->SetOpCode(OP_RENAME_OLD);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(path);
    op_rename->set_dst(rename_dst_dir);
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components;
    SplitPath(rename_dst_dir, &rename_dst_path_components);
    INode rename_dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components,
                                     &rename_dst_inode), StatusCode::kOK);
    ASSERT_EQ(dir.id(), rename_dst_inode.id());
    path = rename_dst_dir;
    SplitPath(path, &path_components);
  }

  // rename2
  {
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &dir), StatusCode::kOK);

    std::string rename2_dst_path = "/edit_log_tail_test_op_rename2";
    auto op_rename = std::make_shared<OpRename>();
    op_rename->SetOpCode(OP_RENAME);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(path);
    op_rename->set_dst(rename2_dst_path);
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components;
    SplitPath(rename2_dst_path, &rename_dst_path_components);
    INode rename_dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components,
                                     &rename_dst_inode), StatusCode::kOK);
    ASSERT_EQ(dir.id(), rename_dst_inode.id());

    std::string rename2_dst_path2 = "/edit_log_tail_test_op_rename3";
    std::shared_ptr<INode> rename_dst_inode_2 = std::make_shared<INode>();
    rename_dst_inode_2->CopyFrom(rename_dst_inode);
    rename_dst_inode_2->set_id(ns_->NextINodeId());
    rename_dst_inode_2->set_name("edit_log_tail_test_op_rename3");
    ns_->meta_storage()->InsertINode(rename_dst_inode_2);

    std::vector<cnetpp::base::StringPiece> rename_dst_path_components2;
    SplitPath(rename2_dst_path2, &rename_dst_path_components2);
    INode rename_dst_inode3;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components2,
                                      &rename_dst_inode3), StatusCode::kOK);

    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(rename2_dst_path);
    op_rename->set_dst(rename2_dst_path2);
    op_rename->set_options(RenameOptions(RenameOption::kOverwrite));
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    INode t;
    ASSERT_EQ(ns_->GetLastINodeInPath(rename_dst_path_components2,
                                     &t), StatusCode::kOK);
    ASSERT_EQ(t.id(), rename_dst_inode.id());

    path = rename2_dst_path2;
    SplitPath(path, &path_components);
  }
}

TEST_F(EditLogTailerTest, NameSpaceRelated) {
  // gen stamp v1
  {
    auto op_gsv1 = std::make_shared<OpSetGenstampV1>();
    op_gsv1->SetOpCode(OP_SET_GENSTAMP_V1);
    op_gsv1->SetTxid(ns_->GetLastCkptTxId() + 1);
    auto origin_gsv1 = ns_->generation_stamp_v1();
    op_gsv1->set_genStampV1(origin_gsv1 + 10000);
    tailer_->Apply(op_gsv1);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->generation_stamp_v1(), origin_gsv1 + 10000);
    // ASSERT_EQ(ns_->NextGenerationStampV1(), origin_gsv1 + 10001);

    op_gsv1->SetTxid(ns_->GetLastCkptTxId() + 1);
    origin_gsv1 = ns_->generation_stamp_v1();
    op_gsv1->set_genStampV1(origin_gsv1 - 1000);
    tailer_->Apply(op_gsv1);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->generation_stamp_v1(), origin_gsv1 - 1000);
    // ASSERT_EQ(ns_->NextGenerationStampV1(), origin_gsv1 + 10001);
  }

  // gen stamp v2
  if (!FLAGS_enable_fast_block_id_and_gs_gen) {
    auto op_gsv2 = std::make_shared<OpSetGenstampV2>();
    op_gsv2->SetOpCode(OP_SET_GENSTAMP_V2);
    op_gsv2->SetTxid(ns_->GetLastCkptTxId() + 1);
    auto origin_gsv2 = ns_->generation_stamp_v2();
    op_gsv2->set_genStampV2(origin_gsv2 + 20000);
    tailer_->Apply(op_gsv2);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ns_->LoadGenerationStampV2();
    ASSERT_EQ(ns_->generation_stamp_v2(), origin_gsv2 + 20000);
    // ASSERT_EQ(ns_->NextGenerationStampV2(), origin_gsv2 + 20001);

    op_gsv2->SetTxid(ns_->GetLastCkptTxId() + 1);
    origin_gsv2 = ns_->generation_stamp_v2();
    op_gsv2->set_genStampV2(origin_gsv2 + 2000);
    tailer_->Apply(op_gsv2);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ns_->LoadGenerationStampV2();
    ASSERT_EQ(ns_->generation_stamp_v2(), origin_gsv2 + 2000);
    // ASSERT_EQ(ns_->NextGenerationStampV2(), origin_gsv2 + 20001);
  }

  // AllocateBlockId
  if (!FLAGS_enable_fast_block_id_and_gs_gen) {
    auto op_allocate_block_id = std::make_shared<OpAllocateBlockId>();
    op_allocate_block_id->SetOpCode(OP_ALLOCATE_BLOCK_ID);
    op_allocate_block_id->SetTxid(ns_->GetLastCkptTxId() + 1);
    auto origin_block_id = ns_->last_allocated_block_id();
    auto op_block_id = origin_block_id + 100000;
    op_allocate_block_id->set_blockId(op_block_id);
    tailer_->Apply(op_allocate_block_id);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ns_->LoadLastAllocatedBlockId();
    ASSERT_EQ(op_block_id, ns_->last_allocated_block_id());
  }
}

TEST_F(EditLogTailerTest, DeletedFile) {
  google::SetVLOGLevel("*", 16);

  ns_->StopBGDeletionWorker();

  std::vector<std::string> ancestor_paths_to_create = {
      "/dir1",
      "/dir1/dir2",
      "/dir1/dir2/parent",
      "/dir1/dir2/parent/20210609",
      "/dir1/dir2/parent/20210609/tmp",
      "/dir1/dir2/parent/20210609/tmp/0",
      "/dir1/dir2/parent/20210609/tmp/0/tmp",
      "/dirA",
      "/dirA/dirB",
      "/dirA/dirB/dirC"
  };

  std::vector<cnetpp::base::StringPiece> path_components;
  INode inode;

  // 1, create parent paths
  LOG(INFO) << "Step 1, create parent paths";
  for (int i = 0; i < ancestor_paths_to_create.size(); i++) {
    std::string path = ancestor_paths_to_create[i];
    std::shared_ptr<OpMkdir> op_mkdir = std::make_shared<OpMkdir>();
    op_mkdir->SetOpCode(OP_MKDIR);
    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_mkdir->set_inodeId(inode_id);
    op_mkdir->set_path(path);
    op_mkdir->SetTxid(tx_id);
    op_mkdir->set_mtime(100000 + inode_id);
    op_mkdir->set_atime(100000 + inode_id);

    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_mkdir->set_permissions(ps);

    SplitPath(path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kFileNotFound);
    tailer_->Apply(op_mkdir);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);
  }

  // 2, add an opening file
  uint64_t file_id;
  LOG(INFO) << "Step 2, add an opening file";
  {
    std::string path = "/dir1/dir2/parent/20210609/tmp/0/tmp/file1";
    std::shared_ptr<OpAdd> op_add = std::make_shared<OpAdd>();
    op_add->SetOpCode(OP_ADD);
    uint64_t inode_id = ns_->NextINodeId();
    file_id = inode_id;
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_add->set_inodeId(inode_id);
    op_add->SetTxid(tx_id);
    op_add->set_path(path);
    op_add->set_replication(3);
    op_add->set_mtime(100000 + inode_id);
    op_add->set_atime(100000 + inode_id);
    op_add->set_blockSize(1234567890);
    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_add->set_permissions(ps);
    op_add->set_clientName("mock_clientname");
    op_add->set_clientMachine("mock_clientmachine");
    op_add->set_overwrite(false);
    op_add->set_storagePolicyId(7);
    op_add->set_clientId("mock_clientid");
    op_add->set_callId(100000 + inode_id);

    SplitPath(path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kFileNotFound);
    tailer_->Apply(op_add);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);
  }

  // 3, rename path
  LOG(INFO) << "Step 3, rename path";
  {
    std::string src_path = "/dir1/dir2/parent";
    SplitPath(src_path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);

    std::string dst_path = "/dirA/dirB/dirC/parent";
    auto op_rename = std::make_shared<OpRename>();
    op_rename->SetOpCode(OP_RENAME);
    op_rename->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_rename->set_src(src_path);
    op_rename->set_dst(dst_path);
    tailer_->Apply(op_rename);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();

    std::vector<cnetpp::base::StringPiece> dst_path_components;
    SplitPath(dst_path, &dst_path_components);
    INode dst_inode;
    ASSERT_EQ(ns_->GetLastINodeInPath(dst_path_components,
                                      &dst_inode), StatusCode::kOK);
    ASSERT_EQ(inode.id(), dst_inode.id());
  }

  // 4, complete file (whose ancestor has just been renamed)
  LOG(INFO) << "Step 4, complete file (whose ancestor has just been renamed)";
  {
    std::string path = "/dirA/dirB/dirC/parent/20210609/tmp/0/tmp/file1";
    std::shared_ptr<OpClose> op_close = std::make_shared<OpClose>();
    op_close->SetOpCode(OP_CLOSE);
    op_close->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_close->set_inodeId(file_id);
    op_close->set_path(path);
    op_close->set_atime(100000 + file_id);
    op_close->set_mtime(100000 + file_id);
    SplitPath(path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);
    tailer_->Apply(op_close);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);
  }

  // 5, delete 0609 path
  LOG(INFO) << "Step 5, delete 0609 path";
  {
    std::string path = "/dirA/dirB/dirC/parent/20210609";
    auto op_delete = std::make_shared<OpDelete>();
    op_delete->SetTxid(ns_->GetLastCkptTxId() + 1);
    op_delete->SetOpCode(OP_DELETE);
    op_delete->set_path(path);
    op_delete->set_timestamp(987654321);
    SplitPath(path, &path_components);
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode),
              StatusCode::kOK);
    tailer_->Apply(op_delete);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode),
              StatusCode::kFileNotFound);
  }

  // 6, mkdir again
  LOG(INFO) << "Step 6, mkdir again";
  std::vector<std::string> new_paths_to_create = {
      "/dirA/dirB/dirC/parent/20210609",
      "/dirA/dirB/dirC/parent/20210609/tmp",
      "/dirA/dirB/dirC/parent/20210609/tmp/0"
  };
  for (int i = 0; i < new_paths_to_create.size(); i++) {
    std::string path = new_paths_to_create[i];
    std::shared_ptr<OpMkdir> op_mkdir = std::make_shared<OpMkdir>();
    op_mkdir->SetOpCode(OP_MKDIR);
    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_mkdir->set_inodeId(inode_id);
    op_mkdir->set_path(path);
    op_mkdir->SetTxid(tx_id);
    op_mkdir->set_mtime(100000 + inode_id);
    op_mkdir->set_atime(100000 + inode_id);

    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_mkdir->set_permissions(ps);

    SplitPath(path, &path_components);
    tailer_->Apply(op_mkdir);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
  }

  // 7, start BGDeleter
  LOG(INFO) << "Step 7, start BGDeleter";
  ns_->StartBGDeletionWorker();
  //  std::this_thread::sleep_for(std::chrono::milliseconds(5000));

  // 8, try to mkdir based on step 6
  LOG(INFO) << "Step 8, try to mkdir based on step 6";
  {
    std::string path = "/dirA/dirB/dirC/parent/20210609/tmp/0/file2";
    std::shared_ptr<OpMkdir> op_mkdir = std::make_shared<OpMkdir>();
    op_mkdir->SetOpCode(OP_MKDIR);
    uint64_t inode_id = ns_->NextINodeId();
    uint64_t tx_id = ns_->GetLastCkptTxId() + 1;
    op_mkdir->set_inodeId(inode_id);
    op_mkdir->set_path(path);
    op_mkdir->SetTxid(tx_id);
    op_mkdir->set_mtime(100000 + inode_id);
    op_mkdir->set_atime(100000 + inode_id);

    PermissionStatus ps;
    ps.set_username("root");
    ps.set_groupname("root");
    uint16_t perm = FsPermission::GetFileDefault().ToShort();
    ps.set_permission(perm);
    op_mkdir->set_permissions(ps);

    SplitPath(path, &path_components);
    tailer_->Apply(op_mkdir);
    tailer_->WaitNoPending(true);
    ns_->WaitNoPending();
    ASSERT_EQ(ns_->GetLastINodeInPath(path_components, &inode), StatusCode::kOK);
  }
}

TEST_F(EditLogTailerTest, NamespaceStatTest) {
}

TEST_F(EditLogTailerTest, AccessCounterSnapshot) {
  cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto snapshot;
  snapshot.set_path("/a/b");
  snapshot.set_count(1);
  snapshot.add_dcvalue(2);
  auto op = std::make_shared<OpAccessCounterSnapshot>();
  op->SetOpCode(OP_ACCESS_COUNTER_SNAPSHOT);
  op->set_snapshot(snapshot);
  op->SetTxid(ns_->GetLastCkptTxId() + 1);
  tailer_->Apply(op);
  ns_->WaitNoPending();
}

TEST_F(EditLogTailerTest, SetAndUnsetLifecyclePolicy) {
  ns_->meta_storage()->InsertINode(
      std::make_shared<INode>(INodeBuilder()
                                  .SetId(17123)
                                  .SetParentId(kRootINodeId)
                                  .SetName("a")
                                  .SetPermission(PermissionStatusBuilder()
                                                     .SetUsername("bird")
                                                     .SetGroupname("cat")
                                                     .SetPermission(644)
                                                     .Build())
                                  .SetType(INode::kDirectory)
                                  .SetMtime(1690893595510)
                                  .SetAtime(1690893595510)
                                  .Build()));
  ns_->meta_storage()->InsertINode(
      std::make_shared<INode>(INodeBuilder()
                                  .SetId(17124)
                                  .SetParentId(17123)
                                  .SetName("b")
                                  .SetPermission(PermissionStatusBuilder()
                                                     .SetUsername("bird")
                                                     .SetGroupname("cat")
                                                     .SetPermission(644)
                                                     .Build())
                                  .SetType(INode::kDirectory)
                                  .SetMtime(1690893595511)
                                  .SetAtime(1690893595511)
                                  .Build()));
  LifecyclePolicyToBeSet proto;
  proto.set_inode_id(17124);
  proto.set_path("/a/b");
  proto.set_timestamp_ms(1690893595517);
  proto.mutable_policy()->set_defaultclass(StorageClassProto::HOT);
  proto.mutable_policy()->mutable_exprule()->set_days(7);
  auto trans_rule = proto.mutable_policy()->add_transrules();
  trans_rule->set_days(14);
  trans_rule->set_targetclass(StorageClassProto::COLD);
  auto op = std::make_shared<OpSetLifecyclePolicy>();
  op->SetProto(std::move(proto));
  auto txid = ns_->GetLastCkptTxId();
  op->SetTxid(txid + 1);
  tailer_->Apply(op);
  tailer_->WaitNoPending(true);
  ns_->WaitNoPending();
  uint64_t timestamp = 0;
  LifecyclePolicyProto policy;
  EXPECT_TRUE(ns_->meta_storage()
                  ->GetLifecyclePolicy(17124, &timestamp, &policy)
                  .IsOK());
  EXPECT_EQ(timestamp, 1690893595517);
  EXPECT_EQ(policy.defaultclass(), StorageClassProto::HOT);
  EXPECT_EQ(policy.exprule().days(), 7);
  ASSERT_EQ(policy.transrules_size(), 1);
  EXPECT_EQ(policy.transrules(0).days(), 14);
  EXPECT_EQ(policy.transrules(0).targetclass(), StorageClassProto::COLD);

  LifecyclePolicyToBeUnset proto2;
  proto2.set_inode_id(17124);
  proto2.set_path("/a/b");
  auto op2 = std::make_shared<OpUnsetLifecyclePolicy>();
  op2->SetProto(std::move(proto2));
  op2->SetTxid(txid + 2);
  tailer_->Apply(op2);
  tailer_->WaitNoPending(true);
  ns_->WaitNoPending();
  EXPECT_EQ(
      ns_->meta_storage()->GetLifecyclePolicy(17124, nullptr, nullptr).code(),
      Code::kNoEntry);
}

}  // namespace dancenn

