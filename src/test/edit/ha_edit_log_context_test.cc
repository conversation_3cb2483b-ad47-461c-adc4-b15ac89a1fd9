// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/11
// Description

#include <cnetpp/concurrency/thread.h>                   // For Thread.
#include <cnetpp/concurrency/thread_pool.h>              // For ThreadPool.
#include <gmock/gmock.h>                                 // For EXPECT_CALL.
#include <gtest/gtest.h>                                 // For Test.
#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <chrono>   // For chrono.
#include <cstdint>  // For int64_t, etc.
#include <memory>   // For shared_ptr, unique_ptr.
#include <mutex>    // For mutex, lock_guard.
#include <sstream>  // For stringstream.
#include <thread>   // For thread.

#include "base/defer.h"                           // For DEFER.
#include "edit/committer.h"                       // For BGEditLogCommitter.
#include "edit/syncer.h"                          // For BGEditLogSyncer.
#include "test/edit/gmock_ha_edit_log_context.h"  // For GMockHAEditLogContext.
#include "test/gmock_edit_log_sync_listener.h"  // For GMockEditLogSyncListener.

namespace dancenn {

class HAEditLogContextTest : public testing::Test {
 public:
  void SetUp() override {
    gmock_ha_ctx_ = std::make_unique<GMockHAEditLogContext>();
    bg_edit_log_syncer_ =
        std::make_shared<BGEditLogSyncer>(gmock_ha_ctx_.get(), 1024);
    bg_edit_log_committer_ = std::make_shared<BGEditLogCommitter>(
        /*max_producers=*/0, gmock_ha_ctx_.get(), bg_edit_log_syncer_);
    listener_ = std::make_shared<GMockEditLogSyncListener>();
    gmock_ha_ctx_->SetupCommitter(bg_edit_log_committer_);
    gmock_ha_ctx_->SetUpSyncer(bg_edit_log_syncer_);
    gmock_ha_ctx_->SetupSyncListener(listener_);
  }

 protected:
  std::shared_ptr<BGEditLogSyncer> bg_edit_log_syncer_;
  std::shared_ptr<BGEditLogCommitter> bg_edit_log_committer_;
  std::shared_ptr<GMockEditLogSyncListener> listener_;
  std::unique_ptr<GMockHAEditLogContext> gmock_ha_ctx_;
};

TEST_F(HAEditLogContextTest, GetHAMode) {
  EXPECT_EQ(gmock_ha_ctx_->GetHAMode(), EditLogConf::HA);
}

TEST_F(HAEditLogContextTest, HASwitchFenceWhenSyncerBGWorkerIsBlocked) {
  bg_edit_log_syncer_->SetNextTxId(1);
  bg_edit_log_committer_->SetNextTxId(1);
  bg_edit_log_committer_->SetLastAllocatedBlockId(1048576);
  bg_edit_log_committer_->SetLastGenerationStampV2(1000);

  cnetpp::concurrency::Thread bg_edit_log_sync_worker(bg_edit_log_syncer_);
  bg_edit_log_sync_worker.Start();
  DEFER([&]() { bg_edit_log_sync_worker.Stop(); });
  cnetpp::concurrency::Thread bg_edit_log_commit_worker(bg_edit_log_committer_);
  bg_edit_log_commit_worker.Start();
  DEFER([&]() { bg_edit_log_commit_worker.Stop(); });

  // Even when the bookkeeper cluster is up and the background worker of the
  // syncer continues to run, the `TxFinish` method of the listener will not be
  // invoked. See `syncer_bg_worker_mtx.unlock()` for more infos.
  EXPECT_CALL(*listener_, TxFinish(testing::_, testing::_)).Times(0);

  auto syncer_channel_capacity = 1024;
  auto committer_channels_capacity = (0 + 1) * 128;
  auto channels_capacity =
      syncer_channel_capacity + committer_channels_capacity;
  auto txid = 0;
  EXPECT_CALL(*gmock_ha_ctx_,
              CallJavaOpMethod(gmock_ha_ctx_->method_log_cfs_op_,
                               /*msg=*/"blocked-syncer-bg-worker",
                               /*with_rpc_id=*/false,
                               /*to_log_rpc_ids=*/false))
      .Times(
          syncer_channel_capacity -
          // Given a `RingBuffer` with a capacity of `cap`, we can only insert
          // `(cap - 1)` elements. After the first element is pushed,
          // `RingBuffer::tail_` equals 1. As we continue to push elements,
          // `RingBuffer::tail_` increases until it equals `(cap - 1)`.
          //   `((RingBuffer::tail_ + 1) % RingBuffer::capacity_)`
          // = `((cap - 1) + 1) % RingBuffer::capacity_`
          // = `cap % RingBuffer::capacity_`
          // =  `RingBuffer::head_`.
          1 +
          // `BGEditLogCommitter::operator()` is blocked when calling
          // `BGEditLogSyncer::Sync` with the next txid.
          1)
      .WillRepeatedly(testing::InvokeWithoutArgs([&txid]() {
        txid++;
        return txid;
      }));
  std::mutex syncer_bg_worker_mtx;
  syncer_bg_worker_mtx.lock();
  EXPECT_CALL(*gmock_ha_ctx_, LogSyncAllInternal())
      .Times(1)
      // We simulate a scenario where the initial `LogSyncAllInternal` call is
      // blocked. This condition causes `BGEditLogSyncer::channel_` to fill up,
      // potentially causing `BGEditLogCommitter::operator()` to halt when
      // `BGEditLogSyncer::Sync` is called. As a result,
      // `BGEditLogCommitter::channels_` also becomes full, which can further
      // lead to a halt in `BGEditLogCommitter::Commit`.
      .WillOnce(testing::Invoke([&syncer_bg_worker_mtx]() {
        std::lock_guard<std::mutex> _(syncer_bg_worker_mtx);
      }));
  cnetpp::concurrency::ThreadPool log_threads("Log");
  log_threads.set_num_threads(channels_capacity);
  log_threads.Start();
  // DEFER([&]() { log_threads.Stop(); });
  for (auto i = 0; i < channels_capacity; i++) {
    log_threads.AddTask([this]() {
      std::stringstream ss;
      ss << "blocked-syncer-bg-worker";
      int64_t txid = gmock_ha_ctx_->LogCfsOp(&ss);
      return true;
    });
  }
  while (!(log_threads.PendingCount() == 0 &&
           bg_edit_log_syncer_->TestOnlyIsChannelFull() &&
           bg_edit_log_committer_->TestOnlyIsChannelsFull())) {
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }
  // At this point, `BGEditLogCommitter::operator()` has already been blocked
  // due to the call to `BGEditLogSyncer::Sync`. Otherwise, the committer's
  // channels won't be full.

  EditLogConf::PreviousEditLogConf previous_conf =
      gmock_ha_ctx_->HASwitchFence();
  // [pending_begin_txid, pending_end_txid)
  EXPECT_EQ(previous_conf.pending_begin_txid(), 1);
  EXPECT_EQ(previous_conf.pending_end_txid(), channels_capacity + 1);
  // [reserved_begin_txid, reserved_end_txid)
  EXPECT_EQ(previous_conf.reserved_begin_txid(),
            previous_conf.pending_end_txid());
  EXPECT_EQ(previous_conf.reserved_end_txid(),
            previous_conf.reserved_begin_txid() + 500000);
  EXPECT_EQ(previous_conf.last_allocated_block_id(),
            /*reserverd_block_id_gap=*/65536 + 1048576 +
                /*reserverd_block_id_gap=*/65536);
  EXPECT_EQ(
      previous_conf.last_generation_stamp_v2(),
      /*reserverd_block_id_gap=*/65536 + 1000 + /*reserverd_gs_gap=*/65536);
  EXPECT_TRUE(previous_conf.has_ha_edit_log_conf());
  // [1, 1 + syncer_channel_capacity)
  EXPECT_EQ(previous_conf.ha_edit_log_conf().syncer_pending_begin_txid(), 1);
  EXPECT_EQ(previous_conf.ha_edit_log_conf().syncer_pending_end_txid(),
            1 + syncer_channel_capacity);
  // There exists an overlapping element between the ranges
  // [syncer_pending_begin_txid, syncer_pending_end_txid) and
  // [committer_pending_begin_txid, syncer_channel_capacity).
  // However, this overlap is acceptable.
  // [syncer_channel_capacity, 1 + channels_capacity)
  EXPECT_EQ(previous_conf.ha_edit_log_conf().committer_pending_begin_txid(),
            syncer_channel_capacity);
  EXPECT_EQ(previous_conf.ha_edit_log_conf().committer_pending_end_txid(),
            1 + channels_capacity);
  EXPECT_FALSE(previous_conf.has_non_ha_edit_log_conf());
  log_threads.Stop();

  syncer_bg_worker_mtx.unlock();
}

TEST_F(HAEditLogContextTest, HASwitchFenceWhenCommitterBGWorkerIsBlocked) {
  bg_edit_log_syncer_->SetNextTxId(1);
  bg_edit_log_committer_->SetNextTxId(1);
  bg_edit_log_committer_->SetLastAllocatedBlockId(1048576);
  bg_edit_log_committer_->SetLastGenerationStampV2(1000);

  cnetpp::concurrency::Thread bg_edit_log_sync_worker(bg_edit_log_syncer_);
  bg_edit_log_sync_worker.Start();
  DEFER([&]() { bg_edit_log_sync_worker.Stop(); });
  cnetpp::concurrency::Thread bg_edit_log_commit_worker(bg_edit_log_committer_);
  bg_edit_log_commit_worker.Start();
  DEFER([&]() { bg_edit_log_commit_worker.Stop(); });

  // Even when the bookkeeper cluster is up and the background worker of the
  // syncer continues to run, the `TxFinish` method of the listener will not be
  // invoked. See `committer_bg_worker_mtx.unlock()` for more infos.
  EXPECT_CALL(*listener_, TxFinish(testing::_, testing::_)).Times(0);

  auto committer_channels_capacity = (0 + 1) * 128;
  volatile bool is_committer_bg_worker_blocked = false;
  std::mutex committer_bg_worker_mtx;
  committer_bg_worker_mtx.lock();
  EXPECT_CALL(*gmock_ha_ctx_,
              CallJavaOpMethod(gmock_ha_ctx_->method_log_cfs_op_,
                               /*msg=*/"blocked-committer-bg-worker",
                               /*with_rpc_id=*/false,
                               /*to_log_rpc_ids=*/false))
      .Times(1)
      .WillOnce(testing::InvokeWithoutArgs(
          [&is_committer_bg_worker_blocked, &committer_bg_worker_mtx]() {
            is_committer_bg_worker_blocked = true;
            std::lock_guard<std::mutex> _(committer_bg_worker_mtx);
            return 1;
          }));
  EXPECT_CALL(*gmock_ha_ctx_, LogSyncAllInternal()).Times(0);

  cnetpp::concurrency::ThreadPool log_threads("Log");
  log_threads.set_num_threads(committer_channels_capacity -
                              // Given a `RingBuffer` with a capacity of
                              // `cap`, we can only insert
                              // `(cap - 1)` elements.
                              1 +  //
                              1);
  log_threads.Start();
  // DEFER([&]() { log_threads.Stop(); });
  for (auto i = 0; i < committer_channels_capacity; i++) {
    log_threads.AddTask([this]() {
      std::stringstream ss;
      ss << "blocked-committer-bg-worker";
      int64_t txid = gmock_ha_ctx_->LogCfsOp(&ss);
      return true;
    });
  }
  while (!(log_threads.PendingCount() == 0 &&
           is_committer_bg_worker_blocked &&
           bg_edit_log_committer_->TestOnlyIsChannelsFull())) {
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }

  EditLogConf::PreviousEditLogConf previous_conf =
      gmock_ha_ctx_->HASwitchFence();
  // [pending_begin_txid, pending_end_txid)
  EXPECT_EQ(previous_conf.pending_begin_txid(), 1);
  EXPECT_EQ(previous_conf.pending_end_txid(), committer_channels_capacity + 1);
  // [reserved_begin_txid, reserved_end_txid)
  EXPECT_EQ(previous_conf.reserved_begin_txid(),
            previous_conf.pending_end_txid());
  EXPECT_EQ(previous_conf.reserved_end_txid(),
            previous_conf.reserved_begin_txid() + 500000);
  EXPECT_EQ(previous_conf.last_allocated_block_id(),
            /*reserverd_block_id_gap=*/65536 + 1048576 +
                /*reserverd_block_id_gap=*/65536);
  EXPECT_EQ(
      previous_conf.last_generation_stamp_v2(),
      /*reserverd_block_id_gap=*/65536 + 1000 + /*reserverd_gs_gap=*/65536);
  EXPECT_TRUE(previous_conf.is_ha_edit_log_conf());
  EXPECT_TRUE(previous_conf.has_ha_edit_log_conf());
  // [1, 1 + syncer_channel_capacity)
  EXPECT_EQ(previous_conf.ha_edit_log_conf().syncer_pending_begin_txid(), 1);
  EXPECT_EQ(previous_conf.ha_edit_log_conf().syncer_pending_end_txid(), 1);
  // [1, 1 + committer_channels_capacity)
  EXPECT_EQ(previous_conf.ha_edit_log_conf().committer_pending_begin_txid(), 1);
  EXPECT_EQ(previous_conf.ha_edit_log_conf().committer_pending_end_txid(),
            1 + committer_channels_capacity);
  EXPECT_FALSE(previous_conf.has_non_ha_edit_log_conf());
  log_threads.Stop();

  committer_bg_worker_mtx.unlock();
}

}  // namespace dancenn
