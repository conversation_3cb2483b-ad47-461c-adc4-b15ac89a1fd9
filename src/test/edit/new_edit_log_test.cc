// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/13
// Description

#include <glog/logging.h>
#include <gtest/gtest.h>

#include <cstdint>
#include <string>

#include "base/java.h"
#include "edit/deserializer.h"
#include "edit/sender.h"
#include "namespace/inode.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/acl.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/cloudfs/xattr.pb.h"
#include "proto/generated/dancenn/inode.pb.h"
#include "test/dancenn_test_base.h"
#include "test/edit/gmock_edit_log_context.h"
#include "test/namespace/inode.h"
#include "test/proto/generated/cloudfs/hdfs.h"
#include "test/proto/generated/dancenn/block_info_proto.h"

namespace dancenn {
namespace test {

PermissionStatus MakePermissionStatus(const std::string& username,
                                      const std::string& groupname,
                                      uint32_t permission) {
  PermissionStatus p;
  p.set_username(username);
  p.set_groupname(groupname);
  p.set_permission(permission);
  return p;
}

using cloudfs::BlockProto;
BlockProto MakeBlockProto(uint64_t id, uint64_t gs, uint64_t num_bytes) {
  BlockProto bp;
  bp.set_blockid(id);
  bp.set_genstamp(gs);
  bp.set_numbytes(num_bytes);
  return bp;
}

using cloudfs::AclEntryProto;
AclEntryProto MakeAclEntryProto(AclEntryProto::AclEntryTypeProto type,
                                AclEntryProto::AclEntryScopeProto scope,
                                AclEntryProto::FsActionProto permissions,
                                const std::string& name) {
  AclEntryProto acl;
  acl.set_type(type);
  acl.set_scope(scope);
  acl.set_permissions(permissions);
  acl.set_name(name);
  return acl;
}

using cloudfs::XAttrProto;
XAttrProto MakeXAttrProto(XAttrProto::XAttrNamespaceProto ns,
                          const std::string& name,
                          const std::string& value) {
  XAttrProto xattr;
  xattr.set_namespace_(ns);
  xattr.set_name(name);
  xattr.set_value(value);
  return xattr;
}

INode MakeINode(uint64_t id,
                uint64_t parent_id,
                const std::string& name,
                const PermissionStatus& permission,
                INode::Type type,
                uint64_t mtime,
                uint64_t atime,
                uint64_t preferred_blk_size,
                uint32_t storage_policy_id,
                uint32_t replication,
                const std::vector<BlockProto>& blocks,
                const std::vector<AclEntryProto>& acls,
                const std::vector<XAttrProto>& xattrs,
                INode::Status status) {
  INode inode;
  inode.set_id(id);
  inode.set_parent_id(parent_id);
  inode.set_name(name);
  inode.mutable_permission()->CopyFrom(permission);
  inode.set_type(type);
  inode.set_mtime(mtime);
  inode.set_atime(atime);
  inode.set_preferred_blk_size(preferred_blk_size);
  inode.set_storage_policy_id(storage_policy_id);
  inode.set_replication(replication);
  for (const auto& block : blocks) {
    inode.add_blocks()->CopyFrom(block);
  }
  for (const auto& acl : acls) {
    inode.add_acls()->CopyFrom(acl);
  }
  for (const auto& xattr : xattrs) {
    inode.add_xattrs()->CopyFrom(xattr);
  }
  inode.set_status(INode::kFileUnderConstruction);
  return inode;
}

class NewEditLogTest : public DanceNNCommonTest {
 public:
  void SetUp() override {
    DanceNNCommonTest::SetUp();
    GetBase()->InitJournalsForWrite();
    GetBase()->OpenForWrite();

    path_ = "/dancenn_common_test/op/text";
    ::dancenn::MakeINode(kLastReservedINodeId + 2,
                         kLastReservedINodeId + 1,
                         "op",
                         MakePermissionStatus("tiger", "root", 777),
                         INode::kDirectory,
                         &parent_);
    inode_ = MakeINode(
        kLastReservedINodeId + 3,
        kLastReservedINodeId + 2,
        "text",
        MakePermissionStatus("root", "tiger", 777),
        INode::kFile,
        1642076199,
        1642076120,
        128 * 1024 * 1024,
        2,
        3,
        std::vector<BlockProto>{MakeBlockProto(1000, 1024, 4096),
                                MakeBlockProto(1001, 1025, 8192)},
        std::vector<AclEntryProto>{MakeAclEntryProto(AclEntryProto::GROUP,
                                                     AclEntryProto::DEFAULT,
                                                     AclEntryProto::READ_WRITE,
                                                     "acl-1"),
                                   MakeAclEntryProto(AclEntryProto::MASK,
                                                     AclEntryProto::ACCESS,
                                                     AclEntryProto::PERM_ALL,
                                                     "acl-2")},
        std::vector<XAttrProto>{
            MakeXAttrProto(XAttrProto::SYSTEM,
                           "system.hdfs.replica.policy.type",
                           "default"),
            MakeXAttrProto(
                XAttrProto::TRUSTED, "trusted.hdfs.bytecool.objectid", "1")},
        INode::kFileUnderConstruction);
    old_inode_.CopyFrom(inode_);
    old_inode_.set_id(kLastReservedINodeId + 4);

    old_dst_inode_.CopyFrom(inode_);
    old_dst_inode_.set_id(kLastReservedINodeId + 5);
    old_dst_inode_.set_name("text.2");
    rb_path_ = "/.RECYCLE.BIN/root/dancenn_common_test_op_text_123";
    ::dancenn::MakeINode(kLastReservedINodeId + 6,
                         kLastReservedINodeId + 1,
                         "root",
                         MakePermissionStatus("tiger", "root", 777),
                         INode::kDirectory,
                         &rb_parent_);
    valid_log_rpc_info_ = LogRpcInfo("010.248.140.004.", 0xFFFF);
  }

  void CompareOldAndNewOp() {
    GetBase()->GetEditLogContext()->Close();
    GetBase()->OpenForRead();
    auto input = GetBase()->GetEditLogContext()->CreateInputContext(2, 0, true);
    CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
    auto converted_old_op = GetConvertedOldOp(input.get());
    auto old_op = GetOldOp(input.get());
    EXPECT_EQ(converted_old_op->txid() + 1, old_op->txid());
    converted_old_op->SetTxid(converted_old_op->txid() + 1);
    EXPECT_EQ(converted_old_op->SerializeToJsonString(),
              old_op->SerializeToJsonString());
  }

  std::shared_ptr<EditLogOp> GetConvertedOldOp(EditLogInputContextBase* input) {
    std::shared_ptr<EditLogOp> converted_old_op;
    {
      std::string serialized_op;
      CHECK(input->ReadOp(&serialized_op));
      auto new_op = OpDeSerializer().Deserialize(serialized_op);
      CHECK_NOTNULL(new_op);
      auto apply_ctx = std::make_shared<ApplyContext>();
      apply_ctx->op = new_op;
      ConvertNewEditLogOpToOld::Op(apply_ctx);
      CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
      converted_old_op = apply_ctx->op;
    }
    return converted_old_op;
  }

  std::shared_ptr<EditLogOp> GetOldOp(EditLogInputContextBase* input) {
    std::shared_ptr<EditLogOp> old_op;
    {
      std::string serialized_op;
      CHECK(input->ReadOp(&serialized_op));
      old_op = OpDeSerializer().Deserialize(serialized_op);
      CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
    }
    return old_op;
  }

  void TearDown() override {
    GetBase()->GetEditLogContext()->Close();
    DanceNNCommonTest::TearDown();
  }

 protected:
  std::string path_;
  INode parent_;
  INode inode_;
  INode old_inode_;
  INode old_dst_inode_;
  std::string rb_path_;
  INode rb_parent_;
  SnapshotLog inode_snaplog_;
  SnapshotLog src_inode_snaplog_;
  SnapshotLog dst_inode_snaplog_;
  SnapshotLog parent_snaplog_;
  SnapshotLog src_parent_snaplog_;
  SnapshotLog dst_parent_snaplog_;
  SnapshotLog rb_parent_snaplog_;
  LogRpcInfo invalid_log_rpc_info_;
  LogRpcInfo valid_log_rpc_info_;
};

TEST_F(NewEditLogTest, OpOpenFileNoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogOpenFileV2(
      path_,
      inode_,
      parent_,
      true,
      nullptr,
      false,
      nullptr,
      nullptr,
      nullptr,
      std::vector<BlockInfoProto>{},
      /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
      {kInvalidINodeId},
      nullptr,
      inode_snaplog_,
      parent_snaplog_,
      rb_parent_snaplog_,
      invalid_log_rpc_info_);
  sender->LogOpenFile(path_, inode_, true, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpOpenFileNoLogRpcInfoOverWritten) {
  INode rb_inode = inode_;
  rb_inode.set_name(rb_inode.name() + "_123");
  rb_parent_.set_mtime(1642131155000);

  std::vector<INodeID> rb_ancestors_id = { kInvalidINodeId };
  auto sender = GetBase()->GetEditLogSender();
  sender->LogOpenFileV2(
      path_,
      inode_,
      parent_,
      true,
      &old_inode_,
      true,
      &rb_path_,
      &rb_inode,
      &rb_parent_,
      std::vector<BlockInfoProto>{},
      /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
      {kInvalidINodeId},
      &rb_ancestors_id,
      inode_snaplog_,
      parent_snaplog_,
      rb_parent_snaplog_,
      invalid_log_rpc_info_);
  sender->LogOpenFile(path_, inode_, true, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpOpenFileWithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogOpenFileV2(
      path_,
      inode_,
      parent_,
      true,
      nullptr,
      false,
      nullptr,
      nullptr,
      nullptr,
      std::vector<BlockInfoProto>{},
      /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
      {kInvalidINodeId},
      nullptr,
      inode_snaplog_,
      parent_snaplog_,
      rb_parent_snaplog_,
      valid_log_rpc_info_);
  sender->LogOpenFile(path_, inode_, true, valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpOpenFileWithLogRpcInfoOverWritten) {
  INode rb_inode = inode_;
  rb_inode.set_name(rb_inode.name() + "_123");
  rb_parent_.set_mtime(1642131155000);

  SnapshotLog dummy_snaplog;
  auto sender = GetBase()->GetEditLogSender();
  sender->LogOpenFileV2(
      path_,
      inode_,
      parent_,
      true,
      &old_inode_,
      false,
      nullptr,
      nullptr,
      nullptr,
      std::vector<BlockInfoProto>{},
      /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
      {kInvalidINodeId},
      nullptr,
      inode_snaplog_,
      parent_snaplog_,
      dummy_snaplog,
      valid_log_rpc_info_);
  // XXX actually not equavalent
  sender->LogOpenFile(path_, inode_, true, valid_log_rpc_info_);
  // CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpAddBlockV2NoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  BlockInfoProto penultimate_bip_tbc;
  penultimate_bip_tbc.set_state(BlockInfoProto::kComplete);
  penultimate_bip_tbc.set_block_id(1000);
  penultimate_bip_tbc.set_gen_stamp(1024);
  penultimate_bip_tbc.set_num_bytes(4096);
  penultimate_bip_tbc.set_inode_id(inode_.id());
  penultimate_bip_tbc.set_expected_rep(2);
  BlockInfoProto last_bip_tbuc;
  last_bip_tbuc.set_state(BlockInfoProto::kComplete);
  last_bip_tbuc.set_block_id(1001);
  last_bip_tbuc.set_gen_stamp(1025);
  last_bip_tbuc.set_num_bytes(8192);
  last_bip_tbuc.set_inode_id(inode_.id());
  last_bip_tbuc.set_expected_rep(2);
  sender->LogAddBlockV2(path_,
                        inode_,
                        &penultimate_bip_tbc,
                        last_bip_tbuc,
                        inode_,
                        {kInvalidINodeId},
                        /*dn_uuids*/std::vector<std::string>(),
                        inode_snaplog_,
                        invalid_log_rpc_info_);
  sender->LogAddBlock(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpAddBlockV2WithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  BlockInfoProto penultimate_bip_tbc;
  penultimate_bip_tbc.set_state(BlockInfoProto::kComplete);
  penultimate_bip_tbc.set_block_id(1000);
  penultimate_bip_tbc.set_gen_stamp(1024);
  penultimate_bip_tbc.set_num_bytes(4096);
  penultimate_bip_tbc.set_inode_id(inode_.id());
  penultimate_bip_tbc.set_expected_rep(2);
  BlockInfoProto last_bip_tbuc;
  last_bip_tbuc.set_state(BlockInfoProto::kComplete);
  last_bip_tbuc.set_block_id(1001);
  last_bip_tbuc.set_gen_stamp(1025);
  last_bip_tbuc.set_num_bytes(8192);
  last_bip_tbuc.set_inode_id(inode_.id());
  last_bip_tbuc.set_expected_rep(2);
  sender->LogAddBlockV2(path_,
                        inode_,
                        &penultimate_bip_tbc,
                        last_bip_tbuc,
                        inode_,
                        { kInvalidINodeId },
                        /*dn_uuids*/std::vector<std::string>(),
                        inode_snaplog_,
                        valid_log_rpc_info_);
  sender->LogAddBlock(path_, inode_, valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpAbandonBlockNoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogAbandonBlock(path_,
                          inode_,
                          1002,
                          inode_,
                          { kInvalidINodeId },
                          invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpAbandonBlockWithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogAbandonBlock(path_,
                          inode_,
                          1002,
                          inode_,
                          { kInvalidINodeId },
                          invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpUpdatePipelineNoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  BlockInfoProto last_bip_tbuc;
  last_bip_tbuc.set_state(BlockInfoProto::kComplete);
  last_bip_tbuc.set_block_id(1001);
  last_bip_tbuc.set_gen_stamp(1025);
  last_bip_tbuc.set_num_bytes(8192);
  last_bip_tbuc.set_inode_id(inode_.id());
  last_bip_tbuc.set_expected_rep(2);
  sender->LogUpdatePipeline(path_,
                            inode_,
                            last_bip_tbuc,
                            inode_,
                            { kInvalidINodeId },
                            invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpUpdatePipelineWithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  BlockInfoProto last_bip_tbuc;
  last_bip_tbuc.set_state(BlockInfoProto::kComplete);
  last_bip_tbuc.set_block_id(1001);
  last_bip_tbuc.set_gen_stamp(1025);
  last_bip_tbuc.set_num_bytes(8192);
  last_bip_tbuc.set_inode_id(inode_.id());
  last_bip_tbuc.set_expected_rep(2);
  sender->LogUpdatePipeline(path_,
                            inode_,
                            last_bip_tbuc,
                            inode_,
                            { kInvalidINodeId },
                            invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpFsyncNoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogFsync(path_,
                   inode_,
                   nullptr,
                   inode_,
                   { kInvalidINodeId },
                   inode_snaplog_,
                   invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpFsyncWithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogFsync(path_,
                   inode_,
                   nullptr,
                   inode_,
                   { kInvalidINodeId },
                   inode_snaplog_,
                   invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpUpdateBlocksV2NoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogUpdateBlocksV2(path_,
                            inode_,
                            inode_,
                            { kInvalidINodeId },
                            invalid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpUpdateBlocksV2WithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogUpdateBlocksV2(path_,
                            inode_,
                            inode_,
                            { kInvalidINodeId },
                            valid_log_rpc_info_);
  sender->LogUpdateBlocks(path_, inode_, valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpCloseFile) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogCloseFileV2(path_,
                         inode_,
                         nullptr,
                         kInvalidBlockID,
                         inode_,
                         { kInvalidINodeId },
                         inode_snaplog_);
  sender->LogCloseFile(path_, inode_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpReassignLeaseV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogReassignLeaseV2(
      path_, inode_, inode_, "holder-1", "holder-2", inode_snaplog_);
  sender->LogReassignLease("holder-1", path_, "holder-2");
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, TestOpMkdirSerAndDeSer) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogMkDir(path_, inode_);
  GetBase()->GetEditLogContext()->Close();
  GetBase()->OpenForRead();
  auto input = GetBase()->GetEditLogContext()->CreateInputContext(2, 0, true);
  CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
  std::string serialized_op;
  CHECK(input->ReadOp(&serialized_op));
  auto op = std::static_pointer_cast<OpMkdir>(
      OpDeSerializer().Deserialize(serialized_op));
  EXPECT_EQ(op->mtime(), inode_.mtime());
  // NOTICE: Actually, we don't support atime, it is just an alias of mtime.
  // Read MkdirOp::writeFields/MkdirOp::readFields at
  // hadoop/hdfs/server/namenode/FSEditLogOp.java for more infos.
  EXPECT_NE(op->atime(), inode_.atime());
  EXPECT_EQ(op->atime(), inode_.mtime());
}

TEST_F(NewEditLogTest, OpMkdirV2) {
  auto sender = GetBase()->GetEditLogSender();
  inode_.set_type(INode::kDirectory);
  inode_.set_status(INode::kFileComplete);
  sender->LogMkdirV2(path_, inode_, parent_, LogRpcInfo(), { kInvalidINodeId }, inode_snaplog_);
  sender->LogMkDir(path_, inode_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpDeleteV2NoLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogDeleteV2(path_,
                      inode_,
                      parent_,
                      1642131155000,
                      { kInvalidINodeId },
                      inode_snaplog_,
                      parent_snaplog_,
                      invalid_log_rpc_info_);
  sender->LogDelete(path_, invalid_log_rpc_info_);

  GetBase()->GetEditLogContext()->Close();
  GetBase()->OpenForRead();
  auto input = GetBase()->GetEditLogContext()->CreateInputContext(2, 0, true);
  CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
  auto converted_old_op =
      std::static_pointer_cast<OpDelete>(GetConvertedOldOp(input.get()));
  auto old_op = std::static_pointer_cast<OpDelete>(GetOldOp(input.get()));

  EXPECT_EQ(converted_old_op->txid() + 1, old_op->txid());
  converted_old_op->SetTxid(converted_old_op->txid() + 1);

  // NOTICE: For OpDelete, timestamp in not correct.
  // It will cause inode on active and inode on standby are not same.
  EXPECT_EQ(converted_old_op->timestamp(), 1642131155000);
  EXPECT_NE(old_op->timestamp(), 1642131155000);
  old_op->set_timestamp(1642131155000);
  EXPECT_EQ(converted_old_op->SerializeToJsonString(),
            old_op->SerializeToJsonString());
}

TEST_F(NewEditLogTest, OpDeleteV2WithLogRpcInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogDeleteV2(path_,
                      inode_,
                      parent_,
                      1642131155000,
                      { kInvalidINodeId },
                      inode_snaplog_,
                      parent_snaplog_,
                      valid_log_rpc_info_);
  sender->LogDelete(path_, valid_log_rpc_info_);

  GetBase()->GetEditLogContext()->Close();
  GetBase()->OpenForRead();
  auto input = GetBase()->GetEditLogContext()->CreateInputContext(2, 0, true);
  CHECK_JVM_EXCEPTION(GetBase()->GetJVM());
  auto converted_old_op =
      std::static_pointer_cast<OpDelete>(GetConvertedOldOp(input.get()));
  auto old_op = std::static_pointer_cast<OpDelete>(GetOldOp(input.get()));

  EXPECT_EQ(converted_old_op->txid() + 1, old_op->txid());
  converted_old_op->SetTxid(converted_old_op->txid() + 1);

  // NOTICE: For OpDelete, timestamp in not correct.
  // It will cause inode on active and inode on standby are not same.
  EXPECT_EQ(converted_old_op->timestamp(), 1642131155000);
  EXPECT_NE(old_op->timestamp(), 1642131155000);
  old_op->set_timestamp(1642131155000);
  EXPECT_EQ(converted_old_op->SerializeToJsonString(),
            old_op->SerializeToJsonString());
}

TEST_F(NewEditLogTest, OpRenameOldV2NoRpcLogInfo) {
  INode dst = inode_;
  dst.set_name(dst.name() + ".2");
  parent_.set_mtime(1642131155000);

  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameOldV2(path_,
                         path_ + ".2",
                         inode_,
                         dst,
                         parent_,
                         parent_,
                         1642131155000,
                         { kInvalidINodeId },
                         { kInvalidINodeId },
                         src_inode_snaplog_,
                         src_parent_snaplog_,
                         dst_parent_snaplog_,
                         invalid_log_rpc_info_);
  sender->LogRename(path_, path_ + ".2", 1642131155000, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRenameOldV2WithRpcLogInfo) {
  INode dst = inode_;
  dst.set_name(dst.name() + ".2");
  parent_.set_mtime(1642131155000);

  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameOldV2(path_,
                         path_ + ".2",
                         inode_,
                         dst,
                         parent_,
                         parent_,
                         1642131155000,
                         { kInvalidINodeId },
                         { kInvalidINodeId },
                         src_inode_snaplog_,
                         src_parent_snaplog_,
                         dst_parent_snaplog_,
                         valid_log_rpc_info_);
  sender->LogRename(path_, path_ + ".2", 1642131155000, valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRenameV2NoLogRpcInfo) {
  INode dst = inode_;
  dst.set_name(dst.name() + ".2");
  parent_.set_mtime(1642131155000);

  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameV2(path_,
                      path_ + ".2",
                      inode_,
                      dst,
                      parent_,
                      parent_,
                      true,
                      nullptr,
                      false,
                      nullptr,
                      nullptr,
                      nullptr,
                      1642131155000,
                      {kInvalidINodeId},
                      {kInvalidINodeId},
                      nullptr,
                      src_inode_snaplog_,
                      dst_inode_snaplog_,
                      src_parent_snaplog_,
                      dst_parent_snaplog_,
                      rb_parent_snaplog_,
                      invalid_log_rpc_info_);
  sender->LogRename(
      path_, path_ + ".2", 1642131155000, invalid_log_rpc_info_, true);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRenameV2NoLogRpcInfoOverWritten) {
  INode new_dst_inode = inode_;
  new_dst_inode.set_name(new_dst_inode.name() + ".2");
  parent_.set_mtime(1642131155000);

  INode rb_inode = old_dst_inode_;
  rb_inode.set_name("dancenn_common_test_op_text_123");
  rb_parent_.set_mtime(1642131155000);

  std::vector<INodeID> rb_ancestors_id = { kInvalidINodeId };
  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameV2(path_,
                      path_ + ".2",
                      inode_,
                      new_dst_inode,
                      parent_,
                      parent_,
                      true,
                      &old_dst_inode_,
                      true,
                      &rb_path_,
                      &rb_inode,
                      &rb_parent_,
                      1642131155000,
                      {kInvalidINodeId},
                      {kInvalidINodeId},
                      &rb_ancestors_id,
                      src_inode_snaplog_,
                      dst_inode_snaplog_,
                      src_parent_snaplog_,
                      dst_parent_snaplog_,
                      rb_parent_snaplog_,
                      invalid_log_rpc_info_);
  // XXX actually not equavalent
  sender->LogRename(
      path_, path_ + ".2", 1642131155000, invalid_log_rpc_info_, true);
  // CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRenameV2WithLogRpcInfo) {
  INode dst = inode_;
  dst.set_name(dst.name() + ".2");
  parent_.set_mtime(1642131155000);

  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameV2(path_,
                      path_ + ".2",
                      inode_,
                      dst,
                      parent_,
                      parent_,
                      true,
                      nullptr,
                      false,
                      nullptr,
                      nullptr,
                      nullptr,
                      1642131155000,
                      {kInvalidINodeId},
                      {kInvalidINodeId},
                      nullptr,
                      src_inode_snaplog_,
                      dst_inode_snaplog_,
                      src_parent_snaplog_,
                      dst_parent_snaplog_,
                      rb_parent_snaplog_,
                      valid_log_rpc_info_);
  sender->LogRename(
      path_, path_ + ".2", 1642131155000, valid_log_rpc_info_, true);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRenameV2WithLogRpcInfoOverWritten) {
  INode new_dst_inode = inode_;
  new_dst_inode.set_name(new_dst_inode.name() + ".2");
  parent_.set_mtime(1642131155000);

  INode rb_inode = old_dst_inode_;
  rb_inode.set_name("dancenn_common_test_op_text_123");
  rb_parent_.set_mtime(1642131155000);

  std::vector<INodeID> rb_ancestors_id = { kInvalidINodeId };
  auto sender = GetBase()->GetEditLogSender();
  sender->LogRenameV2(path_,
                      path_ + ".2",
                      inode_,
                      new_dst_inode,
                      parent_,
                      parent_,
                      true,
                      &old_dst_inode_,
                      true,
                      &rb_path_,
                      &rb_inode,
                      &rb_parent_,
                      1642131155000,
                      {kInvalidINodeId},
                      {kInvalidINodeId},
                      &rb_ancestors_id,
                      src_inode_snaplog_,
                      dst_inode_snaplog_,
                      src_parent_snaplog_,
                      dst_parent_snaplog_,
                      rb_parent_snaplog_,
                      valid_log_rpc_info_);
  // XXX actually not equavalent
  sender->LogRename(
      path_, path_ + ".2", 1642131155000, invalid_log_rpc_info_, true);
  // CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetReplicationV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetReplicationV2(path_, inode_, inode_, 10, inode_snaplog_);
  sender->LogSetReplication(path_, 10);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetStoragePolicyV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetStoragePolicyV2(path_, inode_, inode_, 11, inode_snaplog_);
  sender->LogSetStoragePolicy(path_, 11);
  CompareOldAndNewOp();
}

// TEST_F(NewEditLogTest, OpSetReplicaPolicyV2) {
//   auto sender = GetBase()->GetEditLogSender();
//   sender->LogSetReplicaPolicyV2(path_, inode_, 12);
//   sender->LogSetReplicaPolicy(path_, 12);
//   CompareOldAndNewOp();
// }

TEST_F(NewEditLogTest, OpDirReplicaPolicyV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetDirReplicaPolicyV2(
      path_, inode_, inode_, 12, "dc-1", inode_snaplog_);
  sender->LogSetDirReplicaPolicy(path_, 12, "dc-1");
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetQuotaV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetQuotaV2(path_, 123, 234, inode_snaplog_);
  sender->LogSetQuota(path_, 123, 234);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetPermissionsV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetPermissionsV2(path_, inode_, inode_, 13, inode_snaplog_);
  sender->LogSetPermissions(path_, 13);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetOwnerV2) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSetOwnerV2(
      path_, inode_, inode_, "user-1", "user-2", inode_snaplog_);
  sender->LogSetOwner(path_, "user-1", "user-2");
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpTimesV2) {
  auto sender = GetBase()->GetEditLogSender();
  inode_.set_mtime(std::chrono::system_clock::now().time_since_epoch().count());
  inode_.set_atime(std::chrono::system_clock::now().time_since_epoch().count());
  sender->LogSetTimesV2(
      path_, inode_.mtime(), inode_.atime(), inode_, inode_, inode_snaplog_);
  sender->LogTimes(path_, inode_.mtime(), inode_.atime());
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, SymlinkV2NoRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSymlinkV2(path_, inode_, invalid_log_rpc_info_);
  sender->LogSymlink(path_, inode_, invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, SymlinkV2WithRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  sender->LogSymlinkV2(path_, inode_, valid_log_rpc_info_);
  sender->LogSymlink(path_, inode_, valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetAclV2) {
  auto sender = GetBase()->GetEditLogSender();
  inode_.add_acls()->CopyFrom(MakeAclEntryProto(AclEntryProto::GROUP,
                                                AclEntryProto::DEFAULT,
                                                AclEntryProto::READ_EXECUTE,
                                                "acl-3"));
  sender->LogSetAclV2(path_, inode_.acls());
  sender->LogSetAcl(path_, inode_.acls());
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetXAttrsV2NoRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  auto olde_inode = inode_;
  inode_.add_xattrs()->CopyFrom(
      MakeXAttrProto(XAttrProto::RAW, "raw.name", "text"));
  sender->LogSetXAttrsV2(path_,
                         inode_,
                         olde_inode,
                         inode_.xattrs(),
                         inode_snaplog_,
                         invalid_log_rpc_info_);
  sender->LogSetXAttrs(path_, inode_.xattrs(), invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpSetXAttrsV2WithRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  INode old_inode = inode_;
  inode_.add_xattrs()->CopyFrom(
      MakeXAttrProto(XAttrProto::RAW, "raw.name", "text"));
  sender->LogSetXAttrsV2(path_,
                         inode_,
                         old_inode,
                         inode_.xattrs(),
                         inode_snaplog_,
                         valid_log_rpc_info_);
  sender->LogSetXAttrs(path_, inode_.xattrs(), valid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRemoveXAttrsV2NoRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  INode old_inode = inode_;
  inode_.mutable_xattrs()->RemoveLast();
  sender->LogRemoveXAttrsV2(path_,
                            inode_,
                            old_inode,
                            inode_.xattrs(),
                            inode_snaplog_,
                            invalid_log_rpc_info_);
  sender->LogRemoveXAttrs(path_, inode_.xattrs(), invalid_log_rpc_info_);
  CompareOldAndNewOp();
}

TEST_F(NewEditLogTest, OpRemoveXAttrsV2WithRpcLogInfo) {
  auto sender = GetBase()->GetEditLogSender();
  INode old_inode = inode_;
  inode_.mutable_xattrs()->RemoveLast();
  sender->LogRemoveXAttrsV2(path_,
                            inode_,
                            old_inode,
                            inode_.xattrs(),
                            inode_snaplog_,
                            valid_log_rpc_info_);
  sender->LogRemoveXAttrs(path_, inode_.xattrs(), valid_log_rpc_info_);
  CompareOldAndNewOp();
}

class SendNewEditLogTest : public testing::Test {
 public:
  void SetUp() override {
    context_ = std::make_shared<testing::StrictMock<GMockEditLogContext>>();
    sender_ = std::make_unique<EditLogSender>(
        std::static_pointer_cast<EditLogContextBase>(context_));
  }

 protected:
  std::shared_ptr<testing::StrictMock<GMockEditLogContext>> context_;
  std::unique_ptr<EditLogSender> sender_;
};

TEST_F(SendNewEditLogTest, LogOpenFileV2) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(3)
      .WillRepeatedly(testing::Return(true));
  auto a_dir = INodeBuilder()
                   .SetId(kLastReservedINodeId + 2)
                   .SetParentId(kLastReservedINodeId + 1)
                   .SetName("a")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("bird")
                                      .SetGroupname("cat")
                                      .SetPermission(644)
                                      .Build())
                   .SetType(INode::kDirectory)
                   .SetMtime(3)
                   .SetAtime(4)
                   .Build();
  auto b_file = INodeBuilder()
                    .SetId(kLastReservedINodeId + 3)
                    .SetParentId(kLastReservedINodeId + 2)
                    .SetName("b")
                    .SetPermission(PermissionStatusBuilder()
                                       .SetUsername("tiger")
                                       .SetGroupname("root")
                                       .SetPermission(700)
                                       .Build())
                    .SetType(INode::kFile)
                    .SetMtime(1)
                    .SetAtime(2)
                    .Build();
  {
    // Create file without overwrite.
    EXPECT_CALL(
        *context_,
        LogCfsOp(testing::MatchOpOpenFile(testing::Property(
            &OpOpenFile::GetProto,
            testing::AllOf(
                testing::Property(&FileToBeOpen::path, "/a/b"),
                testing::Property(
                    &FileToBeOpen::inode,
                    testing::Property(&INode::id, kLastReservedINodeId + 3)),
                testing::Property(&FileToBeOpen::overwrite, false),
                testing::Property(
                    &FileToBeOpen::log_rpc_info,
                    testing::AllOf(
                        testing::Property(&RpcInfoPB::rpc_client_id,
                                          "client-id-1"),
                        testing::Property(&RpcInfoPB::rpc_call_id, 1))),
                testing::Property(
                    &FileToBeOpen::parent,
                    testing::Property(&INode::id, kLastReservedINodeId + 2)),
                testing::Property(&FileToBeOpen::move_to_recycle_bin, false),
                testing::Property(&FileToBeOpen::has_rb_path, false),
                testing::Property(&FileToBeOpen::has_rb_inode, false),
                testing::Property(&FileToBeOpen::has_rb_parent, false))))))
        .Times(1)
        .WillOnce(testing::Return(1));
    sender_->LogOpenFileV2(
        "/a/b",
        b_file,
        a_dir,
        false,
        nullptr,
        false,
        nullptr,
        nullptr,
        nullptr,
        std::vector<BlockInfoProto>{},
        /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
        {},
        nullptr,
        {},
        {},
        {},
        LogRpcInfo("client-id-1", 1));
  }
  {
    // Create and overwrite old file directly.
    EXPECT_CALL(
        *context_,
        LogCfsOp(testing::MatchOpOpenFile(testing::Property(
            &OpOpenFile::GetProto,
            testing::AllOf(
                testing::Property(&FileToBeOpen::has_overwrite_inode, true),
                testing::Property(&FileToBeOpen::move_to_recycle_bin,
                                  false))))))
        .Times(1)
        .WillOnce(testing::Return(1));
    auto overwrite_inode =
        INodeBuilder()
            .SetId(kLastReservedINodeId + 4)
            .SetParentId(kLastReservedINodeId + 2)
            .SetName("b")
            .SetPermission(PermissionStatusBuilder()
                               .SetUsername("apple")
                               .SetGroupname("banana")
                               .SetPermission(644)
                               .Build())
            .SetType(INode::kFile)
            .SetMtime(1)
            .SetAtime(2)
            .AddBlock(BlockProtoBuilder()
                          .SetBlockId(kLastReservedBlockId + 1)
                          .SetGenStamp(1001)
                          .SetNumBytes(1024)
                          .Build())
            .Build();
    sender_->LogOpenFileV2(
        "/a/b",
        b_file,
        a_dir,
        true,
        &overwrite_inode,
        false,
        nullptr,
        nullptr,
        nullptr,
        std::vector<BlockInfoProto>{},
        /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
        {},
        nullptr,
        {},
        {},
        {},
        LogRpcInfo("client-id-1", 1));
  }
  {
    // Create file and move old file into recycle bin.
    auto recycle_bin_root_dir = INodeBuilder()
                                    .SetId(kLastReservedINodeId + 5)
                                    .SetParentId(kLastReservedINodeId + 4)
                                    .SetName("root")
                                    .SetPermission(PermissionStatusBuilder()
                                                       .SetUsername("apple")
                                                       .SetGroupname("banana")
                                                       .SetPermission(700)
                                                       .Build())
                                    .SetType(INode::kDirectory)
                                    .SetMtime(1)
                                    .SetAtime(2)
                                    .Build();
    auto removed_file = INodeBuilder()
                            .SetId(kLastReservedINodeId + 6)
                            .SetParentId(kLastReservedINodeId + 5)
                            .SetName("removed")
                            .SetPermission(PermissionStatusBuilder()
                                               .SetUsername("orange")
                                               .SetGroupname("cherry")
                                               .SetPermission(644)
                                               .Build())
                            .SetType(INode::kFile)
                            .SetMtime(1)
                            .SetAtime(2)
                            .Build();
    auto overwrite_file = removed_file;
    overwrite_file.set_parent_id(kLastReservedINodeId + 2);
    std::string rb_path = "/.RECYCLE.BIN/root/removed";
    EXPECT_CALL(
        *context_,
        LogCfsOp(testing::MatchOpOpenFile(testing::Property(
            &OpOpenFile::GetProto,
            testing::AllOf(
                testing::Property(&FileToBeOpen::overwrite, true),
                testing::Property(&FileToBeOpen::move_to_recycle_bin, true),
                testing::Property(&FileToBeOpen::rb_path, rb_path),
                testing::Property(
                    &FileToBeOpen::rb_inode,
                    testing::AllOf(
                        testing::Property(&INode::id, kLastReservedINodeId + 6),
                        testing::Property(&INode::name, "removed"))),
                testing::Property(
                    &FileToBeOpen::rb_parent,
                    testing::AllOf(
                        testing::Property(&INode::id, kLastReservedINodeId + 5),
                        testing::Property(&INode::name, "root"))),
                testing::Property(&FileToBeOpen::has_overwrite_inode, true))))))
        .Times(1)
        .WillOnce(testing::Return(1));
    std::vector<INodeID> rb_ancestors_id = {kInvalidINodeId};
    sender_->LogOpenFileV2(
        "/a/b",
        b_file,
        a_dir,
        true,
        &overwrite_file,
        true,
        &rb_path,
        &removed_file,
        &recycle_bin_root_dir,
        std::vector<BlockInfoProto>{},
        /*bips_expected_locs*/ std::vector<std::vector<std::string>>(),
        {},
        &rb_ancestors_id,
        {},
        {},
        {},
        LogRpcInfo());
  }
}

TEST_F(SendNewEditLogTest, LogAddBlockV2) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(2)
      .WillRepeatedly(testing::Return(true));
  INode file = INodeBuilder()
                   .SetId(kLastReservedINodeId + 3)
                   .SetParentId(kLastReservedINodeId + 2)
                   .SetName("file")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("root")
                                      .SetPermission(700)
                                      .Build())
                   .SetType(INode::kFile)
                   .SetMtime(1)
                   .SetAtime(2)
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744443)
                                 .SetGenStamp(1001)
                                 .SetNumBytes(1024)
                                 .Build())
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744444)
                                 .SetGenStamp(1002)
                                 .SetNumBytes(1025)
                                 .Build())
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744445)
                                 .SetGenStamp(1003)
                                 .SetNumBytes(1026)
                                 .Build())
                   .Build();
  auto old_file = file;
  old_file.mutable_blocks()->RemoveLast();
  BlockInfoProto last_bip = BlockInfoProtoBuilder()
                                .SetState(BlockInfoProto::kUnderConstruction)
                                .SetBlockId(1073744445)
                                .SetGenStamp(1003)
                                .SetNumBytes(1026)
                                .SetINodeId(kLastReservedINodeId + 3)
                                .SetExpectedRep(3)
                                .Build();
  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpAddBlockV2(testing::Property(
          &OpAddBlockV2::GetProto,
          testing::AllOf(
              testing::Property(&BlockToBeAdd::path, "/dir/file"),
              testing::Property(
                  &BlockToBeAdd::inode,
                  testing::AllOf(
                      testing::Property(&INode::id, kLastReservedINodeId + 3),
                      testing::Property(&INode::name, "file"))),
              testing::Property(&BlockToBeAdd::has_penultimate_bip, false),
              testing::Property(
                  &BlockToBeAdd::last_bip,
                  testing::AllOf(
                      testing::Property(&BlockInfoProto::block_id, 1073744445),
                      testing::Property(&BlockInfoProto::gen_stamp, 1003),
                      testing::Property(&BlockInfoProto::num_bytes, 1026))),
              testing::Property(&BlockToBeAdd::has_log_rpc_info, false))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  sender_->LogAddBlockV2("/dir/file",
                         file,
                         nullptr,
                         last_bip,
                         old_file,
                         {},
                         /*dn_uuids*/std::vector<std::string>(),
                         {},
                         LogRpcInfo("client-id-1", 1));

  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpAddBlockV2(testing::Property(
          &OpAddBlockV2::GetProto,
          testing::AllOf(
              testing::Property(
                  &BlockToBeAdd::penultimate_bip,
                  testing::AllOf(
                      testing::Property(&BlockInfoProto::block_id, 1073744444),
                      testing::Property(&BlockInfoProto::gen_stamp, 1002),
                      testing::Property(&BlockInfoProto::num_bytes, 1025))),
              testing::Property(
                  &BlockToBeAdd::last_bip,
                  testing::AllOf(
                      testing::Property(&BlockInfoProto::block_id, 1073744445),
                      testing::Property(&BlockInfoProto::gen_stamp, 1003),
                      testing::Property(&BlockInfoProto::num_bytes, 1026))))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  auto penultimate_bip = BlockInfoProtoBuilder()
                             .SetState(BlockInfoProto::kPersisted)
                             .SetBlockId(1073744444)
                             .SetGenStamp(1002)
                             .SetNumBytes(1025)
                             .SetINodeId(kLastReservedINodeId + 3)
                             .SetExpectedRep(3)
                             .Build();
  sender_->LogAddBlockV2("/dir/file",
                         file,
                         &penultimate_bip,
                         last_bip,
                         old_file,
                         {},
                         /*dn_uuids*/std::vector<std::string>(),
                         {},
                         LogRpcInfo());
}

TEST_F(SendNewEditLogTest, LogAbandonBlock) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(1)
      .WillRepeatedly(testing::Return(true));
  INode file = INodeBuilder()
                   .SetId(kLastReservedINodeId + 3)
                   .SetParentId(kLastReservedINodeId + 2)
                   .SetName("file")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("root")
                                      .SetPermission(700)
                                      .Build())
                   .SetType(INode::kFile)
                   .SetMtime(1)
                   .SetAtime(2)
                   .Build();
  INode old_file = file;
  *old_file.add_blocks() = BlockProtoBuilder()
                               .SetBlockId(1073744443)
                               .SetGenStamp(1001)
                               .SetNumBytes(1024)
                               .Build();
  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpAbandonBlock(testing::Property(
          &OpAbandonBlock::GetProto,
          testing::AllOf(
              testing::Property(&BlockToBeAbandon::path, "/dir/file"),
              testing::Property(
                  &BlockToBeAbandon::inode,
                  testing::AllOf(
                      testing::Property(&INode::id, kLastReservedINodeId + 3),
                      testing::Property(&INode::name, "file"))),
              testing::Property(&BlockToBeAbandon::abandoned_blk_id,
                                1073744443),
              testing::Property(
                  &BlockToBeAbandon::log_rpc_info,
                  testing::AllOf(
                      testing::Property(&RpcInfoPB::rpc_client_id,
                                        "client-id-1"),
                      testing::Property(&RpcInfoPB::rpc_call_id, 1))))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  sender_->LogAbandonBlock("/dir/file",
                           file,
                           1073744443,
                           old_file,
                           {},
                           LogRpcInfo("client-id-1", 1));
}

TEST_F(SendNewEditLogTest, LogFsync) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(2)
      .WillRepeatedly(testing::Return(true));
  INode file = INodeBuilder()
                   .SetId(kLastReservedINodeId + 3)
                   .SetParentId(kLastReservedINodeId + 2)
                   .SetName("file")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("root")
                                      .SetPermission(700)
                                      .Build())
                   .SetType(INode::kFile)
                   .SetMtime(1)
                   .SetAtime(2)
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744443)
                                 .SetGenStamp(1001)
                                 .SetNumBytes(1024)
                                 .Build())
                   .Build();
  auto old_file = file;
  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpFsync(testing::Property(
          &OpFsync::GetProto,
          testing::AllOf(
              testing::Property(&FileToBeSync::path, "/dir/file"),
              testing::Property(
                  &FileToBeSync::inode,
                  testing::AllOf(
                      testing::Property(&INode::id, kLastReservedINodeId + 3),
                      testing::Property(&INode::name, "file"))),
              testing::Property(&FileToBeSync::has_last_bip_tbuc, false))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  sender_->LogFsync("/dir/file",
                    file,
                    nullptr,
                    old_file,
                    {},
                    {},
                    LogRpcInfo("client-id-1", 1));

  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpFsync(testing::Property(
          &OpFsync::GetProto,
          testing::Property(
              &FileToBeSync::last_bip_tbuc,
              testing::AllOf(
                  testing::Property(&BlockInfoProto::state,
                                    BlockInfoProto::kUnderConstruction),
                  testing::Property(&BlockInfoProto::block_id, 1073744443),
                  testing::Property(&BlockInfoProto::gen_stamp, 1001),
                  testing::Property(&BlockInfoProto::num_bytes, 1024)))))));
  auto last_bip_tbuc = BlockInfoProtoBuilder()
                           .SetState(BlockInfoProto::kUnderConstruction)
                           .SetBlockId(1073744443)
                           .SetGenStamp(1001)
                           .SetNumBytes(1024)
                           .SetINodeId(kLastReservedINodeId + 3)
                           .SetExpectedRep(3)
                           .Build();
  sender_->LogFsync(
      "/dir/file", file, &last_bip_tbuc, old_file, {}, {}, LogRpcInfo());
}

TEST_F(SendNewEditLogTest, LogUpdateBlocksV2) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(1)
      .WillRepeatedly(testing::Return(true));
  INode file = INodeBuilder()
                   .SetId(kLastReservedINodeId + 3)
                   .SetParentId(kLastReservedINodeId + 2)
                   .SetName("file")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("root")
                                      .SetPermission(700)
                                      .Build())
                   .SetType(INode::kFile)
                   .SetMtime(1)
                   .SetAtime(2)
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744443)
                                 .SetGenStamp(1001)
                                 .SetNumBytes(1024)
                                 .Build())
                   .Build();
  INode old_file = file;
  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpUpdateBlocksV2(testing::Property(
          &OpUpdateBlocksV2::GetProto,
          testing::AllOf(
              testing::Property(&BlocksToBeUpdate::path, "/dir/file"),
              testing::Property(
                  &BlocksToBeUpdate::inode,
                  testing::AllOf(
                      testing::Property(&INode::id, kLastReservedINodeId + 3),
                      testing::Property(&INode::name, "file"))),
              testing::Property(&BlocksToBeUpdate::has_last_bip, false),
              testing::Property(
                  &BlocksToBeUpdate::log_rpc_info,
                  testing::AllOf(
                      testing::Property(&RpcInfoPB::rpc_client_id,
                                        "client-id-1"),
                      testing::Property(&RpcInfoPB::rpc_call_id, 1))))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  sender_->LogUpdateBlocksV2(
      "/dir/file", file, old_file, {}, LogRpcInfo("client-id-1", 1));
}

TEST_F(SendNewEditLogTest, LogCloseFileV2) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(1)
      .WillRepeatedly(testing::Return(true));
  INode file = INodeBuilder()
                   .SetId(kLastReservedINodeId + 3)
                   .SetParentId(kLastReservedINodeId + 2)
                   .SetName("file")
                   .SetPermission(PermissionStatusBuilder()
                                      .SetUsername("tiger")
                                      .SetGroupname("root")
                                      .SetPermission(700)
                                      .Build())
                   .SetType(INode::kFile)
                   .SetMtime(1)
                   .SetAtime(2)
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744443)
                                 .SetGenStamp(1001)
                                 .SetNumBytes(1024)
                                 .Build())
                   .AddBlock(BlockProtoBuilder()
                                 .SetBlockId(1073744444)
                                 .SetGenStamp(1002)
                                 .SetNumBytes(1025)
                                 .Build())
                   .Build();
  auto old_file = file;
  *old_file.add_blocks() = BlockProtoBuilder()
                               .SetBlockId(1073744445)
                               .SetGenStamp(1003)
                               .SetNumBytes(1026)
                               .Build();
  EXPECT_CALL(
      *context_,
      LogCfsOp(testing::MatchOpCloseFile(testing::Property(
          &OpCloseFile::GetProto,
          testing::AllOf(
              testing::Property(&FileToBeClose::path, "/dir/file"),
              testing::Property(
                  &FileToBeClose::inode,
                  testing::AllOf(
                      testing::Property(&INode::id, kLastReservedINodeId + 3),
                      testing::Property(&INode::name, "file"))),
              testing::Property(&FileToBeClose::has_last_bip, false),
              testing::Property(&FileToBeClose::abandoned_blk_id,
                                1073744445))))))
      .Times(1)
      .WillOnce(testing::Return(1));
  sender_->LogCloseFileV2(
      "/dir/file", file, nullptr, 1073744445, old_file, {}, {});
}

TEST_F(SendNewEditLogTest, LogCreateAndAddBlockWithLocation) {
  EXPECT_CALL(*context_, IsOpenForWrite())
      .Times(testing::AtLeast(1))
      .WillRepeatedly(testing::Return(true));
  {
    // CreateWithAddBlock
    auto a_dir = INodeBuilder()
                     .SetId(kLastReservedINodeId + 2)
                     .SetParentId(kLastReservedINodeId + 1)
                     .SetName("a")
                     .SetPermission(PermissionStatusBuilder()
                                        .SetUsername("bird")
                                        .SetGroupname("cat")
                                        .SetPermission(0755)
                                        .Build())
                     .SetType(INode::kDirectory)
                     .SetMtime(3)
                     .SetAtime(4)
                     .Build();
    auto b_file = INodeBuilder()
                      .SetId(kLastReservedINodeId + 3)
                      .SetParentId(kLastReservedINodeId + 2)
                      .SetName("b")
                      .SetPermission(PermissionStatusBuilder()
                                         .SetUsername("tiger")
                                         .SetGroupname("root")
                                         .SetPermission(0644)
                                         .Build())
                      .SetType(INode::kFile)
                      .SetMtime(1)
                      .SetAtime(2)
                      .Build();
    BlockInfoProto bip;
    bip.set_block_id(1);
    bip.set_num_bytes(0);
    bip.set_gen_stamp(1);
    bip.set_inode_id(kLastReservedINodeId + 3);
    bip.set_expected_rep(2);
    bip.set_state(BlockInfoProto_Status_kUnderConstruction);

    std::stringstream actual_ss;
    EXPECT_CALL(
        *context_,
        LogCfsOp(testing::MatchOpOpenFile(testing::Property(
            &OpOpenFile::GetProto,
            testing::AllOf(
                testing::Property(&FileToBeOpen::path, "/a/b"),
                testing::Property(
                    &FileToBeOpen::inode,
                    testing::Property(&INode::id, kLastReservedINodeId + 3)),
                testing::Property(&FileToBeOpen::overwrite, false),
                testing::Property(
                    &FileToBeOpen::log_rpc_info,
                    testing::AllOf(
                        testing::Property(&RpcInfoPB::rpc_client_id,
                                          "client-id-1"),
                        testing::Property(&RpcInfoPB::rpc_call_id, 1))),
                testing::Property(
                    &FileToBeOpen::parent,
                    testing::Property(&INode::id, kLastReservedINodeId + 2)),
                testing::Property(&FileToBeOpen::move_to_recycle_bin, false),
                testing::Property(&FileToBeOpen::has_rb_path, false),
                testing::Property(&FileToBeOpen::has_rb_inode, false),
                testing::Property(&FileToBeOpen::has_rb_parent, false))))))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::WithArg<0>([&actual_ss](const std::stringstream* ss) {
              actual_ss << ss->rdbuf();
            }),
            testing::Return(1)));
    sender_->LogOpenFileV2(
        "/a/b",
        b_file,
        a_dir,
        false,
        nullptr,
        false,
        nullptr,
        nullptr,
        nullptr,
        std::vector<BlockInfoProto>{bip},
        std::vector<std::vector<std::string>>{{"dn-1", "dn-2"}},
        {},
        nullptr,
        {},
        {},
        {},
        LogRpcInfo("client-id-1", 1));
    std::shared_ptr<EditLogOp> op =
        OpDeSerializer().Deserialize(actual_ss.str());
    FileToBeOpen actual_proto = static_cast<OpOpenFile*>(op.get())->GetProto();
    LOG(INFO) << actual_proto.ShortDebugString();
    EXPECT_EQ(actual_proto.add_block_bips_size(), 1);
    EXPECT_EQ(actual_proto.add_block_bips(0).SerializeAsString(),
              bip.SerializeAsString());
    EXPECT_EQ(actual_proto.add_block_bips_with_locs_size(), 1);
    EXPECT_EQ(
        actual_proto.add_block_bips_with_locs(0).bip().SerializeAsString(),
        bip.SerializeAsString());
    EXPECT_EQ(actual_proto.add_block_bips_with_locs(0).dns(0), "dn-1");
    EXPECT_EQ(actual_proto.add_block_bips_with_locs(0).dns(1), "dn-2");
  }

  {
    // AddBlock
    INode file = INodeBuilder()
                     .SetId(kLastReservedINodeId + 3)
                     .SetParentId(kLastReservedINodeId + 2)
                     .SetName("file")
                     .SetPermission(PermissionStatusBuilder()
                                        .SetUsername("tiger")
                                        .SetGroupname("root")
                                        .SetPermission(0644)
                                        .Build())
                     .SetType(INode::kFile)
                     .SetMtime(1)
                     .SetAtime(2)
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1073744443)
                                   .SetGenStamp(1001)
                                   .SetNumBytes(1024)
                                   .Build())
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1073744444)
                                   .SetGenStamp(1002)
                                   .SetNumBytes(1025)
                                   .Build())
                     .AddBlock(BlockProtoBuilder()
                                   .SetBlockId(1073744445)
                                   .SetGenStamp(1003)
                                   .SetNumBytes(1026)
                                   .Build())
                     .Build();
    auto old_file = file;
    old_file.mutable_blocks()->RemoveLast();
    BlockInfoProto last_bip = BlockInfoProtoBuilder()
                                  .SetState(BlockInfoProto::kUnderConstruction)
                                  .SetBlockId(1073744445)
                                  .SetGenStamp(1003)
                                  .SetNumBytes(1026)
                                  .SetINodeId(kLastReservedINodeId + 3)
                                  .SetExpectedRep(3)
                                  .Build();
    std::stringstream actual_ss;
    EXPECT_CALL(
        *context_,
        LogCfsOp(testing::MatchOpAddBlockV2(testing::Property(
            &OpAddBlockV2::GetProto,
            testing::AllOf(
                testing::Property(&BlockToBeAdd::path, "/dir/file"),
                testing::Property(
                    &BlockToBeAdd::inode,
                    testing::AllOf(
                        testing::Property(&INode::id, kLastReservedINodeId + 3),
                        testing::Property(&INode::name, "file"))),
                testing::Property(&BlockToBeAdd::has_penultimate_bip, false),
                testing::Property(
                    &BlockToBeAdd::last_bip,
                    testing::AllOf(
                        testing::Property(&BlockInfoProto::block_id,
                                          1073744445),
                        testing::Property(&BlockInfoProto::gen_stamp, 1003),
                        testing::Property(&BlockInfoProto::num_bytes, 1026))),
                testing::Property(&BlockToBeAdd::has_log_rpc_info, false))))))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::WithArg<0>([&actual_ss](const std::stringstream* ss) {
              actual_ss << ss->rdbuf();
            }),
            testing::Return(1)));
    sender_->LogAddBlockV2("/dir/file",
                           file,
                           nullptr,
                           last_bip,
                           old_file,
                           {},
                           std::vector<std::string>{"dn-1", "dn-2"},
                           {},
                           LogRpcInfo("client-id-1", 1));
    std::shared_ptr<EditLogOp> op =
        OpDeSerializer().Deserialize(actual_ss.str());
    BlockToBeAdd actual_proto =
        static_cast<OpAddBlockV2*>(op.get())->GetProto();
    auto last_bip_with_locs = actual_proto.last_bip_with_locs();
    EXPECT_EQ(last_bip.SerializeAsString(),
              last_bip_with_locs.bip().SerializeAsString());
    EXPECT_EQ(last_bip_with_locs.dns_size(), 2);
    EXPECT_EQ(last_bip_with_locs.dns(0), "dn-1");
    EXPECT_EQ(last_bip_with_locs.dns(1), "dn-2");
  }

  {
    // BatchCreateWithAddBlock
    INode parent = INodeBuilder()
                       .SetId(kLastReservedINodeId + 2)
                       .SetParentId(kLastReservedINodeId + 1)
                       .SetName("/dir")
                       .SetPermission(PermissionStatusBuilder()
                                          .SetUsername("tiger")
                                          .SetGroupname("root")
                                          .SetPermission(0755)
                                          .Build())
                       .SetType(INode::kDirectory)
                       .SetMtime(1)
                       .SetAtime(2)
                       .Build();
    INode file1 = INodeBuilder()
                      .SetId(kLastReservedINodeId + 3)
                      .SetParentId(kLastReservedINodeId + 2)
                      .SetName("file1")
                      .SetPermission(PermissionStatusBuilder()
                                         .SetUsername("tiger")
                                         .SetGroupname("root")
                                         .SetPermission(0644)
                                         .Build())
                      .SetType(INode::kFile)
                      .SetMtime(1)
                      .SetAtime(2)
                      .AddBlock(BlockProtoBuilder()
                                    .SetBlockId(1073744443)
                                    .SetGenStamp(1001)
                                    .SetNumBytes(1024)
                                    .Build())
                      .Build();
    BlockInfoProto bip1 = BlockInfoProtoBuilder()
                              .SetState(BlockInfoProto::kUnderConstruction)
                              .SetBlockId(1073744443)
                              .SetGenStamp(1001)
                              .SetNumBytes(1024)
                              .SetINodeId(kLastReservedINodeId + 3)
                              .SetExpectedRep(3)
                              .Build();

    INode file2 = INodeBuilder()
                      .SetId(kLastReservedINodeId + 4)
                      .SetParentId(kLastReservedINodeId + 2)
                      .SetName("file2")
                      .SetPermission(PermissionStatusBuilder()
                                         .SetUsername("tiger")
                                         .SetGroupname("root")
                                         .SetPermission(0644)
                                         .Build())
                      .SetType(INode::kFile)
                      .SetMtime(1)
                      .SetAtime(2)
                      .AddBlock(BlockProtoBuilder()
                                    .SetBlockId(1073744444)
                                    .SetGenStamp(1002)
                                    .SetNumBytes(1025)
                                    .Build())
                      .Build();
    BlockInfoProto bip2 = BlockInfoProtoBuilder()
                              .SetState(BlockInfoProto::kUnderConstruction)
                              .SetBlockId(1073744444)
                              .SetGenStamp(1002)
                              .SetNumBytes(1025)
                              .SetINodeId(kLastReservedINodeId + 4)
                              .SetExpectedRep(3)
                              .Build();

    std::stringstream actual_ss;
    EXPECT_CALL(*context_, LogCfsOp(testing::_))
        .Times(1)
        .WillOnce(testing::DoAll(
            testing::WithArg<0>([&actual_ss](const std::stringstream* ss) {
              actual_ss << ss->rdbuf();
            }),
            testing::Return(1)));
    sender_->LogBatchCreateFile({"/dir/file1", "/dir/file2"},
                                {file1, file2},
                                /*overwritten_inodes*/ {},
                                /*overwritten_bips*/ {},
                                {bip1, bip2},
                                {{"dn-1", "dn-2"}, {"dn-1", "dn-2"}},
                                parent,
                                /*timestamp_in_ms*/ 1,
                                LogRpcInfo("client-id-1", 1));
    std::shared_ptr<EditLogOp> op =
        OpDeSerializer().Deserialize(actual_ss.str());
    BatchInodeToCreate actual_proto =
        static_cast<OpBatchCreateFile*>(op.get())->GetProto();
    EXPECT_EQ(actual_proto.parent().SerializeAsString(),
              parent.SerializeAsString());
    EXPECT_EQ(actual_proto.files_size(), 2);
    EXPECT_EQ(actual_proto.files(0).path(), "/dir/file1");
    EXPECT_EQ(actual_proto.files(0).inode().SerializeAsString(),
              file1.SerializeAsString());
    EXPECT_EQ(actual_proto.files(1).path(), "/dir/file2");
    EXPECT_EQ(actual_proto.files(1).inode().SerializeAsString(),
              file2.SerializeAsString());
    EXPECT_EQ(actual_proto.files(0).add_block_bips().SerializeAsString(),
              bip1.SerializeAsString());
    EXPECT_EQ(actual_proto.files(1).add_block_bips().SerializeAsString(),
              bip2.SerializeAsString());
    EXPECT_EQ(actual_proto.files(0).add_block_bips_with_locs_size(), 1);
    EXPECT_EQ(actual_proto.files(0)
                  .add_block_bips_with_locs(0)
                  .bip()
                  .SerializeAsString(),
              bip1.SerializeAsString());
    EXPECT_EQ(actual_proto.files(0).add_block_bips_with_locs(0).dns_size(), 2);
    EXPECT_EQ(actual_proto.files(0).add_block_bips_with_locs(0).dns(0), "dn-1");
    EXPECT_EQ(actual_proto.files(0).add_block_bips_with_locs(0).dns(1), "dn-2");
    EXPECT_EQ(actual_proto.files(1)
                  .add_block_bips_with_locs(0)
                  .bip()
                  .SerializeAsString(),
              bip2.SerializeAsString());
    EXPECT_EQ(actual_proto.files(1).add_block_bips_with_locs(0).dns_size(), 2);
    EXPECT_EQ(actual_proto.files(1).add_block_bips_with_locs(0).dns(0), "dn-1");
    EXPECT_EQ(actual_proto.files(1).add_block_bips_with_locs(0).dns(1), "dn-2");
    EXPECT_EQ(actual_proto.log_rpc_info().rpc_call_id(), 1);
    EXPECT_EQ(actual_proto.log_rpc_info().rpc_client_id(), "client-id-1");
  }
}

}  // namespace test
}  // namespace dancenn
