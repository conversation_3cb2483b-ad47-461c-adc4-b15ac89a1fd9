//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#include <gtest/gtest.h>
#include <glog/logging.h>
#include <namespace/inode.h>

#include "base/file_utils.h"
#include "test/dancenn_test_base.h"

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class EditLogSenderTest: public dancenn::test::DanceNNCommonTest { };

TEST_F(EditLogSenderTest, Send) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();
  auto log_rpc = LogRpcInfo();
  // clientId is generated uniquely by Client.
  // only null, zero-sized or 16-byte-sized clientId is permitted
  auto log_rpc_t = LogRpcInfo("", 1);
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), false);
  LOG(INFO) << "===TEST OpenForWrite===";
  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  INode a;
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());
  MakeINode(10001, 10000, "a", p, INode::kDirectory, &a);
  auto blk = a.add_blocks();
  blk->set_blockid(123);
  blk->set_genstamp(345);
  blk->set_numbytes(128);

  LOG(INFO) << "GetCurSegmentTxId:" << ctx->GetCurSegmentTxId();
  LOG(INFO) << "LogOpenFile:" << sender->LogOpenFile("/a", a, true, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogCloseFile:" << sender->LogCloseFile("/a", a);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogAddBlock:" << sender->LogAddBlock("/a", a, log_rpc_t);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogUpdateBlocks:" << sender->LogUpdateBlocks("/a", a, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogMkDir:" << sender->LogMkDir("/a", a);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogRenameOld:" << sender->LogRename("/a", "/b", 1234, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogRename:"
            << sender->LogRename("/a", "/b", 1234, log_rpc, true);
  LOG(INFO) << "LogSetReplication:" << sender->LogSetReplication("/a", 3);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSetStoragePolicy:" << sender->LogSetStoragePolicy("/a", 7);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSetReplicaPolicy:" << sender->LogSetReplicaPolicy("/a", 1);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSetQuota:" << sender->LogSetQuota("/a", 1, 2);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSetPermissions:" << sender->LogSetPermissions("/a", 111);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSetOwner:" << sender->LogSetOwner("/a", "root", "root");
  CHECK_JVM_EXCEPTION(jvm);

  std::vector<std::string> srcs;
  srcs.emplace_back("/b");
  LOG(INFO) << "LogConcat:" << sender->LogConcat("/a", srcs, 1234, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogDelete:" << sender->LogDelete("/a", log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  if (!FLAGS_enable_fast_block_id_and_gs_gen) {
    LOG(INFO) << "LogGenerationStampV1:" << sender->LogGenerationStampV1(1234);
    CHECK_JVM_EXCEPTION(jvm);
  }

  {
    uint64_t gs = 1;
    auto txid = sender->LogSetGenerationStampV2(gs, false);
    CHECK_JVM_EXCEPTION(jvm);
    int64_t exp_txid = 18;
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      exp_txid = kInvalidTxId;
    }
    ASSERT_EQ(txid, exp_txid);
    ASSERT_EQ(gs, 1);
    LOG(INFO) << "LogGenerationStampV2:" << txid;
  }
  {
    uint64_t block_id = 1;
    auto txid = sender->LogAllocateBlockIdV2(block_id);
    CHECK_JVM_EXCEPTION(jvm);
    int64_t exp_txid = 19;
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      exp_txid = kInvalidTxId;
    }
    ASSERT_EQ(txid, exp_txid);
    ASSERT_EQ(block_id, 1);
    LOG(INFO) << "LogAllocateBlockId:" << txid;
  }
#if 0
  {
    uint64_t gs, block_id;
    auto txid = sender->LogBlockIdAndGSv2(&block_id, &gs);
    CHECK_JVM_EXCEPTION(jvm);
    int64_t exp_txid = 21;
    if (FLAGS_enable_fast_block_id_and_gs_gen) {
      exp_txid = kInvalidTxId;
    }
    ASSERT_EQ(txid, exp_txid);
    ASSERT_EQ(gs, 2);
    ASSERT_EQ(block_id, 2);
    LOG(INFO) << "LogBlockIdAndGSv2:" << txid;
  }
#endif
  LOG(INFO) << "LogTimes:" << sender->LogTimes("/a", 1234, 2345);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogSymlink:" << sender->LogSymlink("/a", a, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
  LOG(INFO) << "LogReassignLease:"
            << sender->LogReassignLease("client1", "/a", "client2");
  CHECK_JVM_EXCEPTION(jvm);

  ::google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto> entries;
  auto e = entries.Add();
  e->set_type(::cloudfs::AclEntryProto_AclEntryTypeProto_USER);
  e->set_scope(::cloudfs::AclEntryProto_AclEntryScopeProto_DEFAULT);
  e->set_permissions(::cloudfs::AclEntryProto_FsActionProto_NONE);
  LOG(INFO) << "LogSetAcl:" << sender->LogSetAcl("/a", entries);
  CHECK_JVM_EXCEPTION(jvm);

  ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs1;
  auto xattr1 = xattrs1.Add();
  xattr1->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr1->set_name("xattr1");
  LOG(INFO) << "LogSetXAttrs:" << sender->LogSetXAttrs("/a", xattrs1, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);

  LOG(INFO) << "LogRemoveXAttrs:"
            << sender->LogRemoveXAttrs("/a", xattrs1, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);

  cloudfs::fsimage::AccessCounterSection_AccessCounterSnapshotProto
      snapshot;  // NOLINT(whitespace/line_length)
  snapshot.set_path("/a");
  snapshot.set_count(1);
  LOG(INFO) << "LogAccessCounterSnapshot:"
            << sender->LogAccessCounterSnapshot(snapshot);

  ctx->LogSyncAll();
  ctx->Close();

  LOG(INFO) << "===TEST OpenForRead===";
  CHECK_JVM_EXCEPTION(jvm);
  ctx->OpenForRead();
  ASSERT_EQ(ctx->IsOpenForRead(), true);
  ASSERT_EQ(ctx->IsOpenForWrite(), false);

//  LOG(INFO) << "LogOpenFile:" << sender->LogOpenFile("/a", a, true, log_rpc);
//  CHECK_JVM_EXCEPTION(jvm);
//  LOG(INFO) << "LogCloseFile:" << sender->LogCloseFile("/a", a);
//  CHECK_JVM_EXCEPTION(jvm);
//  LOG(INFO) << "LogAddBlock:" << sender->LogAddBlock("/a", a, log_rpc_t);
//  CHECK_JVM_EXCEPTION(jvm);
  ctx->Close();

  LOG(INFO) << "===Done===";
}

TEST_F(EditLogSenderTest, PurgeOlderThan) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base ->GetEditLogSender();

  base -> InitJournalsForWrite()
       -> OpenForWrite()
       -> Append(100);
  sleep(1);
  base -> Close();

  int64_t last_transaction_id = ctx->GetLastWrittenTxId();

  base -> InitJournalsForWrite()
       -> OpenForWrite()
       -> Append(100);
  sleep(1);
  base -> Close();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);
  ctx->PurgeLogsOlderThan(last_transaction_id);
  ctx->Close();
}

TEST_F(EditLogSenderTest, GetPeerNNAddr) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  std::string peerAddr;
  ASSERT_TRUE(ctx->GetPeerNNAddr(&peerAddr));
  ASSERT_EQ(peerAddr, "http://10.1.1.3:5070");
  std::string stack_info;
  ctx->GetAllStackTraces(&stack_info);
  LOG(INFO) << "GetAllStackTraces: \n" << stack_info;
  ctx->Close();
}

TEST_F(EditLogSenderTest, LogSetAclLarge128) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  ::google::protobuf::RepeatedPtrField<cloudfs::AclEntryProto> entries;
  auto e = entries.Add();
  e->set_type(::cloudfs::AclEntryProto_AclEntryTypeProto_USER);
  e->set_scope(::cloudfs::AclEntryProto_AclEntryScopeProto_DEFAULT);
  e->set_permissions(::cloudfs::AclEntryProto_FsActionProto_NONE);
  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890911wkdlwkdlwkldwklwkdlkwdlwkdlkwldklkdlkwlkdwlkdwklwkdlwkdwdhwjhd";
  sender->LogSetAcl(src, entries);
  CHECK_JVM_EXCEPTION(jvm);
}

TEST_F(EditLogSenderTest, LogSetXAttrs128) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();
  auto log_rpc = LogRpcInfo();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs;
  auto xattr = xattrs.Add();
  xattr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name("trusted.hdfs.bytecool.temperature.type");
  xattr->set_value("WARM");
  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890911";
  sender->LogSetXAttrs(src, xattrs, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
}

TEST_F(EditLogSenderTest, LogRemoveXAttrs128) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();
  auto log_rpc = LogRpcInfo();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  ::google::protobuf::RepeatedPtrField<::cloudfs::XAttrProto> xattrs;
  auto xattr = xattrs.Add();
  xattr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name("trusted.hdfs.bytecool.temperature.type");
  xattr->set_value("WARM");
  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890911";
  sender->LogRemoveXAttrs(src, xattrs, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
}

TEST_F(EditLogSenderTest, LogOpenFileXAttr128) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();
  auto log_rpc = LogRpcInfo();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  INode a;
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());
  MakeINode(10001, 10000, "a", p, INode::kDirectory, &a);
  auto blk = a.add_blocks();
  blk->set_blockid(123);
  blk->set_genstamp(345);
  blk->set_numbytes(128);

  auto xattr = a.add_xattrs();
  xattr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name("trusted.hdfs.bytecool.temperature.type");
  xattr->set_value("WARM");

  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890911";
  sender->LogOpenFile(src, a, true, log_rpc);
  CHECK_JVM_EXCEPTION(jvm);
}

TEST_F(EditLogSenderTest, LogMkDirXAttr128) {
  auto base = GetBase();
  auto ctx = base->GetEditLogContext();
  auto sender = base->GetEditLogSender();
  auto jvm = base->GetJVM();
  auto log_rpc = LogRpcInfo();

  ctx->OpenForWrite();
  ASSERT_EQ(ctx->IsOpenForRead(), false);
  ASSERT_EQ(ctx->IsOpenForWrite(), true);

  INode a;
  PermissionStatus p;
  p.set_username("root");
  p.set_groupname("root");
  p.set_permission(FsPermission::GetFileDefault().ToShort());
  MakeINode(10001, 10000, "a", p, INode::kDirectory, &a);
  auto blk = a.add_blocks();
  blk->set_blockid(123);
  blk->set_genstamp(345);
  blk->set_numbytes(128);

  auto xattr = a.add_xattrs();
  xattr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_USER);
  xattr->set_name("trusted.hdfs.bytecool.temperature.type");
  xattr->set_value("WARM");

  const std::string src = "/user/yangjinfeng.02/abb866cf-d928-4589-bdc6-86b3787e16e7/713931234567890912";
  sender->LogMkDir(src, a);
  CHECK_JVM_EXCEPTION(jvm);
}

TEST_F(EditLogSenderTest, EDIT_LOG_UTF8) {
  std::string invalid_utf8;
  invalid_utf8.resize(1);
  invalid_utf8[0] = 0xE0;
  ASSERT_TRUE(StringUtils::IsValidUTF8("abcdefg"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("测试中文"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("åß∂œ∑®†¥"));
  ASSERT_TRUE(StringUtils::IsValidUTF8("👋🌍"));

  std::string str = "👋🌍";

  ASSERT_FALSE(StringUtils::IsValidUTF8("\xF0\x28\x8C\xBC"));
  ASSERT_FALSE(StringUtils::IsValidUTF8(invalid_utf8));

  OpTimes op = OpTimes();
  op.SetOpCode(OP_TIMES);
  op.set_path("👋🌍");
  op.set_mtime(0);
  op.set_atime(0);

  std::stringstream ss;
  op.WriteFields(&ss);

  dancenn::OpDeSerializer op_deserializer;
  auto op2 = op_deserializer.Deserialize(ss.str());

  LOG(INFO) << "op=" << op.path();
  LOG(INFO) << "op="
            << CHECK_NOTNULL(dynamic_cast<OpTimes*>(op2.get()))->path();
}

}  // namespace dancenn
