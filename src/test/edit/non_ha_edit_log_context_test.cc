// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/20
// Description

// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/11
// Description

#include "edit/non_ha_edit_log_context.h"  // For NonHAEditLogContext.

#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <gmock/gmock.h>                     // For EXPECT_CALL.
#include <gtest/gtest.h>                     // For Test.

#include <atomic>  // For atomic.
#include <memory>   // For unique_ptr, shared_ptr.
#include <sstream>  // For stringstream.
#include <string>   // For string.
#include <vector>   // For vector.

#include "base/constants.h"                     // For kEditLogConfKey, etc.
#include "block_manager/block.h"                // For BlockID, kInvalidBlockID.
#include "test/gmock_edit_log_sync_listener.h"  // For GMockEditLogSyncListener.

DECLARE_bool(enable_fast_block_id_and_gs_gen);

namespace dancenn {

class NonHAEditLogContextTest : public testing::Test {
 public:
  void SetUp() override {
    listener_ = std::make_shared<GMockEditLogSyncListener>();
    ctx_ = std::make_unique<NonHAEditLogContext>();
    ctx_->SetupSyncListener(listener_);
  }

 protected:
  std::shared_ptr<GMockEditLogSyncListener> listener_;
  std::unique_ptr<NonHAEditLogContext> ctx_;
};

TEST_F(NonHAEditLogContextTest, GetHAMode) {
  EXPECT_EQ(ctx_->GetHAMode(), EditLogConf::NonHA);
}

TEST_F(NonHAEditLogContextTest, UpdateConfProperty) {
  EXPECT_TRUE(ctx_->UpdateConfProperty("name", "value"));
}

TEST_F(NonHAEditLogContextTest, SetupSyncListener) {
  EXPECT_EQ(ctx_->TestOnlyGetSyncListener().get(), listener_.get());
}

TEST_F(NonHAEditLogContextTest, GetPeerNNAddr) {
  std::string peer_nn_addr;
  EXPECT_FALSE(ctx_->GetPeerNNAddr(&peer_nn_addr));
  EXPECT_EQ(peer_nn_addr, "");
}

TEST_F(NonHAEditLogContextTest, GetAllStackTraces) {
  std::string stack_info;
  EXPECT_FALSE(ctx_->GetAllStackTraces(&stack_info));
  EXPECT_EQ(stack_info, "");
}

// TEST_F(NonHAEditLogContextTest, IsOpenForRead)
TEST_F(NonHAEditLogContextTest, OpenForRead) {
  EXPECT_FALSE(ctx_->IsOpenForRead());
  ctx_->OpenForRead();
  EXPECT_FALSE(ctx_->IsOpenForRead());
}

TEST_F(NonHAEditLogContextTest, CreateInputContext) {
  EXPECT_FALSE(ctx_->CreateInputContext(1, 2, false));
  ctx_->OpenForRead();
  EXPECT_FALSE(ctx_->CreateInputContext(1, 2, false));
}

TEST_F(NonHAEditLogContextTest, InitJournalsForWrite) {
  ctx_->InitJournalsForWrite();
}

// TEST_F(NonHAEditLogContextTest, IsOpenForWrite)
TEST_F(NonHAEditLogContextTest, OpenForWrite) {
  EXPECT_FALSE(ctx_->IsOpenForWrite());
  ctx_->SetNextTxId(1335);
  EXPECT_CALL(*listener_, TxFinish(1335, 1)).Times(1);
  EXPECT_EQ(ctx_->OpenForWrite(), 1335);
  EXPECT_TRUE(ctx_->IsOpenForWrite());
}

TEST_F(NonHAEditLogContextTest, Close) {
  ctx_->OpenForWrite();
  EXPECT_TRUE(ctx_->IsOpenForWrite());
  ctx_->Close();
  EXPECT_FALSE(ctx_->IsOpenForWrite());
}

// TEST_F(NonHAEditLogContextTest, GetLastWrittenTxId)
TEST_F(NonHAEditLogContextTest, SetNextTxId) {
  ctx_->SetNextTxId(2932);
  EXPECT_EQ(ctx_->GetLastWrittenTxId(), 2931);
}

// TEST_F(NonHAEditLogContextTest, GetLastAllocatedBlockId)
TEST_F(NonHAEditLogContextTest, SetLastAllocatedBlockId) {
  ctx_->SetLastAllocatedBlockId(8618);
  EXPECT_EQ(ctx_->GetLastAllocatedBlockId(), 8618);
}

// TEST_F(NonHAEditLogContextTest, GetLastGenerationStampV2)
TEST_F(NonHAEditLogContextTest, SetLastGenerationStampV2) {
  ctx_->SetLastGenerationStampV2(5597);
  EXPECT_EQ(ctx_->GetLastGenerationStampV2(), 5597);
}

TEST_F(NonHAEditLogContextTest, GetWaitSyncTime) {
  EXPECT_EQ(ctx_->GetWaitSyncTime(), 0);
}

TEST_F(NonHAEditLogContextTest, LogSync) {
  ctx_->LogSync();
}

TEST_F(NonHAEditLogContextTest, LogSyncAll) {
  ctx_->LogSyncAll();
}

// TEST_F(NonHAEditLogContextTest, GetCurSegmentTxId)
TEST_F(NonHAEditLogContextTest, RollEditLog) {
  EXPECT_EQ(ctx_->GetCurSegmentTxId(), kInvalidTxId);
  ctx_->SetNextTxId(194);
  EXPECT_CALL(*listener_, TxFinish(194, 1)).Times(1);
  EXPECT_CALL(*listener_, TxFinish(195, 1)).Times(1);
  EXPECT_EQ(ctx_->RollEditLog(), 195);
  EXPECT_EQ(ctx_->GetCurSegmentTxId(), 195);
}

TEST_F(NonHAEditLogContextTest, PurgeLogsOlderThan) {
  ctx_->PurgeLogsOlderThan(7701);
}

TEST_F(NonHAEditLogContextTest, LogCfsOp) {
  ctx_->SetNextTxId(1880);
  EXPECT_CALL(*listener_, TxFinish(1880, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogCfsOp(&ss), 1880);
}

TEST_F(NonHAEditLogContextTest, LogAllocateBlockId) {
  ctx_->SetNextTxId(834);
  ctx_->SetLastAllocatedBlockId(3312);
  std::stringstream ss;
  BlockID block_id = kInvalidBlockID;
  int64_t expect_txid = 834;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  } else {
    EXPECT_CALL(*listener_, TxFinish(834, 1)).Times(1);
  }
  EXPECT_EQ(ctx_->LogAllocateBlockId(&ss, &block_id), expect_txid);
  EXPECT_EQ(block_id, 3313);
}

TEST_F(NonHAEditLogContextTest, LogGenerationStampV1) {
  ctx_->SetNextTxId(2226);
  EXPECT_CALL(*listener_, TxFinish(2226, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogGenerationStampV1(&ss), 2226);
}

TEST_F(NonHAEditLogContextTest, LogGenerationStampV2) {
  ctx_->SetNextTxId(8884);
  ctx_->SetLastGenerationStampV2(651);
  std::stringstream ss;
  uint64_t gsv2 = 0;
  int64_t expect_txid = 8884;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  } else {
    EXPECT_CALL(*listener_, TxFinish(8884, 1)).Times(1);
  }
  EXPECT_EQ(ctx_->LogGenerationStampV2(&ss, &gsv2), expect_txid);
  EXPECT_EQ(gsv2, 652);
}

TEST_F(NonHAEditLogContextTest, LogAllocateBlockIdAndGSv2) {
  ctx_->SetNextTxId(783);
  ctx_->SetLastAllocatedBlockId(3844);
  ctx_->SetLastGenerationStampV2(3325);
  std::stringstream blkid_ss;
  std::stringstream gsv2_ss;
  BlockID block_id = kInvalidBlockID;
  uint64_t gsv2 = 0;
  int64_t expect_txid = 784;
  if (FLAGS_enable_fast_block_id_and_gs_gen) {
    expect_txid = kInvalidTxId;
  } else {
    EXPECT_CALL(*listener_, TxFinish(783, 1)).Times(1);
    EXPECT_CALL(*listener_, TxFinish(784, 1)).Times(1);
  }
  EXPECT_EQ(
      ctx_->LogAllocateBlockIdAndGSv2(&blkid_ss, &gsv2_ss, &block_id, &gsv2),
      expect_txid);
  EXPECT_EQ(block_id, 3845);
  EXPECT_EQ(gsv2, 3326);
}

TEST_F(NonHAEditLogContextTest, LogTimes) {
  ctx_->SetNextTxId(5736);
  EXPECT_CALL(*listener_, TxFinish(5736, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogTimes(&ss), 5736);
}

TEST_F(NonHAEditLogContextTest, LogOpenFile) {
  ctx_->SetNextTxId(7427);
  EXPECT_CALL(*listener_, TxFinish(7427, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogOpenFile(&ss), 7427);
}

TEST_F(NonHAEditLogContextTest, LogAddBlock) {
  ctx_->SetNextTxId(2012);
  EXPECT_CALL(*listener_, TxFinish(2012, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogAddBlock(&ss), 2012);
}

TEST_F(NonHAEditLogContextTest, LogUpdateBlocks) {
  ctx_->SetNextTxId(4006);
  EXPECT_CALL(*listener_, TxFinish(4006, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogUpdateBlocks(&ss), 4006);
}

TEST_F(NonHAEditLogContextTest, LogCloseFile) {
  ctx_->SetNextTxId(302);
  EXPECT_CALL(*listener_, TxFinish(302, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogCloseFile(&ss), 302);
}

TEST_F(NonHAEditLogContextTest, LogReassignLease) {
  ctx_->SetNextTxId(6016);
  EXPECT_CALL(*listener_, TxFinish(6016, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogReassignLease(&ss), 6016);
}

TEST_F(NonHAEditLogContextTest, LogConcat) {
  ctx_->SetNextTxId(6198);
  EXPECT_CALL(*listener_, TxFinish(6198, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogConcat(&ss), 6198);
}

TEST_F(NonHAEditLogContextTest, LogSetBlockPufsInfo) {
  ctx_->SetNextTxId(398);
  EXPECT_CALL(*listener_, TxFinish(398, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetBlockPufsInfo(&ss), 398);
}

TEST_F(NonHAEditLogContextTest, LogDeleteDeprecatedBlockPufsInfo) {
  ctx_->SetNextTxId(642);
  EXPECT_CALL(*listener_, TxFinish(642, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogDeleteDeprecatedBlockPufsInfo(&ss), 642);
}

TEST_F(NonHAEditLogContextTest, LogMkDir) {
  ctx_->SetNextTxId(3107);
  EXPECT_CALL(*listener_, TxFinish(3107, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogMkDir(&ss), 3107);
}

TEST_F(NonHAEditLogContextTest, LogDelete) {
  ctx_->SetNextTxId(4134);
  EXPECT_CALL(*listener_, TxFinish(4134, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogDelete(&ss), 4134);
}

TEST_F(NonHAEditLogContextTest, LogRenameOld) {
  ctx_->SetNextTxId(1062);
  EXPECT_CALL(*listener_, TxFinish(1062, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogRenameOld(&ss), 1062);
}

TEST_F(NonHAEditLogContextTest, LogRename) {
  ctx_->SetNextTxId(9704);
  EXPECT_CALL(*listener_, TxFinish(9704, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogRename(&ss), 9704);
}

TEST_F(NonHAEditLogContextTest, LogSymlink) {
  ctx_->SetNextTxId(7827);
  EXPECT_CALL(*listener_, TxFinish(7827, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSymlink(&ss), 7827);
}

TEST_F(NonHAEditLogContextTest, LogSetReplication) {
  ctx_->SetNextTxId(2621);
  EXPECT_CALL(*listener_, TxFinish(2621, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetReplication(&ss), 2621);
}

TEST_F(NonHAEditLogContextTest, LogSetStoragePolicy) {
  ctx_->SetNextTxId(8704);
  EXPECT_CALL(*listener_, TxFinish(8704, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetStoragePolicy(&ss), 8704);
}

TEST_F(NonHAEditLogContextTest, LogSetReplicaPolicy) {
  ctx_->SetNextTxId(7432);
  EXPECT_CALL(*listener_, TxFinish(7432, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetReplicaPolicy(&ss), 7432);
}

TEST_F(NonHAEditLogContextTest, LogSetDirReplicaPolicy) {
  ctx_->SetNextTxId(5623);
  EXPECT_CALL(*listener_, TxFinish(5623, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetDirReplicaPolicy(&ss), 5623);
}

TEST_F(NonHAEditLogContextTest, LogSetQuota) {
  ctx_->SetNextTxId(3891);
  EXPECT_CALL(*listener_, TxFinish(3891, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetQuota(&ss), 3891);
}

TEST_F(NonHAEditLogContextTest, LogSetPermissions) {
  ctx_->SetNextTxId(2976);
  EXPECT_CALL(*listener_, TxFinish(2976, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetPermissions(&ss), 2976);
}

TEST_F(NonHAEditLogContextTest, LogSetOwner) {
  ctx_->SetNextTxId(4217);
  EXPECT_CALL(*listener_, TxFinish(4217, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetOwner(&ss), 4217);
}

TEST_F(NonHAEditLogContextTest, LogSetXAttrs) {
  ctx_->SetNextTxId(4478);
  EXPECT_CALL(*listener_, TxFinish(4478, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogSetXAttrs(&ss), 4478);
}

TEST_F(NonHAEditLogContextTest, LogRemoveXAttrs) {
  ctx_->SetNextTxId(5556);
  EXPECT_CALL(*listener_, TxFinish(5556, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogRemoveXAttrs(&ss), 5556);
}

TEST_F(NonHAEditLogContextTest, LogAllowSnapshot) {
  ctx_->SetNextTxId(3542);
  EXPECT_CALL(*listener_, TxFinish(3542, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogAllowSnapshot(&ss), 3542);
}

TEST_F(NonHAEditLogContextTest, LogDisallowSnapshot) {
  ctx_->SetNextTxId(2987);
  EXPECT_CALL(*listener_, TxFinish(2987, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogDisallowSnapshot(&ss), 2987);
}

TEST_F(NonHAEditLogContextTest, LogCreateSnapshot) {
  ctx_->SetNextTxId(6745);
  EXPECT_CALL(*listener_, TxFinish(6745, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogCreateSnapshot(&ss), 6745);
}

TEST_F(NonHAEditLogContextTest, LogDeleteSnapshot) {
  ctx_->SetNextTxId(8346);
  EXPECT_CALL(*listener_, TxFinish(8346, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogDeleteSnapshot(&ss), 8346);
}

TEST_F(NonHAEditLogContextTest, LogRenameSnapshot) {
  ctx_->SetNextTxId(7542);
  EXPECT_CALL(*listener_, TxFinish(7542, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogRenameSnapshot(&ss), 7542);
}

TEST_F(NonHAEditLogContextTest, LogAccessCounterSnapshot) {
  ctx_->SetNextTxId(1023);
  EXPECT_CALL(*listener_, TxFinish(1023, 1)).Times(1);
  std::stringstream ss;
  EXPECT_EQ(ctx_->LogAccessCounterSnapshot(&ss), 1023);
}

TEST_F(NonHAEditLogContextTest, LogParallelly) {
  std::vector<std::atomic<bool>> are_txids_used(4096);
  ASSERT_EQ(are_txids_used.size(), 4096);
  ctx_->SetNextTxId(0);
  EXPECT_CALL(*listener_, TxFinish(testing::_, 1)).Times(are_txids_used.size());

  cnetpp::concurrency::ThreadPool log_threads("Log");
  log_threads.set_num_threads(are_txids_used.size());
  log_threads.Start();
  for (auto i = 0; i < are_txids_used.size(); i++) {
    log_threads.AddTask([this, &are_txids_used]() {
      std::stringstream ss;
      ss << "blocked-committer-bg-worker";
      int64_t txid = ctx_->LogCfsOp(&ss);
      EXPECT_FALSE(are_txids_used[txid].exchange(true));
      return true;
    });
  }
  while (log_threads.PendingCount() != 0) {
  }
  log_threads.Stop(/*wait=*/true);

  for (auto i = 0; i < are_txids_used.size(); i++) {
    EXPECT_TRUE(are_txids_used[i].load());
  }
}

TEST_F(NonHAEditLogContextTest, HASwitchFence) {
  ctx_->SetNextTxId(2297);
  ctx_->SetLastAllocatedBlockId(2748);
  ctx_->SetLastGenerationStampV2(5324);
  EditLogConf::PreviousEditLogConf previous_conf = ctx_->HASwitchFence();
  EXPECT_EQ(previous_conf.pending_begin_txid(), 2297);
  EXPECT_EQ(previous_conf.pending_end_txid(), 2297);
  EXPECT_EQ(previous_conf.reserved_begin_txid(), 2297);
  EXPECT_EQ(previous_conf.reserved_end_txid(), 2297 + 500000);
  EXPECT_EQ(previous_conf.last_allocated_block_id(), 2748 + 65536);
  EXPECT_EQ(previous_conf.last_generation_stamp_v2(), 5324 + 65536);
  EXPECT_FALSE(previous_conf.is_ha_edit_log_conf());
  EXPECT_TRUE(previous_conf.has_non_ha_edit_log_conf());
}

}  // namespace dancenn
