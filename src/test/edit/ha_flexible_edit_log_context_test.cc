// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/11
// Description

#include "edit/ha_flexible_edit_log_context.h"  // For HAFlexibleEditLogContext.

#include <cnetpp/base/string_piece.h>                    // For StringPiece.
#include <gflags/gflags.h>                               // For DECLARE_string.
#include <gmock/gmock.h>                                 // For EXPECT_CALL.
#include <gtest/gtest.h>                                 // For Test.
#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <chrono>   // For chrono.
#include <cstdint>  // For uint64_t, etc.
#include <memory>   // For unique_ptr, shared_ptr.
#include <sstream>  // For stringstream.
#include <string>   // For string.
#include <thread>   // For this_thread.

#include "base/closure.h"                       // For Closure.
#include "base/constants.h"                     // For kEditLogConfKey, etc.
#include "base/defer.h"                         // For DEFER.
#include "base/platform.h"                      // For ReadBigEndian.
#include "base/status.h"                        // For Code, Status.
#include "base/vlock.h"                         // For VRWLock.
#include "base/vlock.h"                         // For TwoStepVUniqueLock.
#include "block_manager/block.h"                // For BlockID, kInvalidBlockID.
#include "edit/edit_log_context_base.h"         // For EditLogContextBase.
#include "edit/edit_log_sync_listener.h"        // For IEditLogSyncListener.
#include "test/edit/gmock_edit_log_context.h"   // For GMockEditLogContext.
#include "test/gmock_edit_log_input_context.h"  // For GMockEditLogInputContext.
#include "test/gmock_ha_state.h"                // For GMockHAState.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage.

DECLARE_string(namespace_meta_storage_ckpt_path);

namespace dancenn {

class HAFlexibleEditLogContextTest : public testing::Test {
 public:
  void SetUp() override {
    gmock_meta_storage_ =
        std::make_shared<testing::StrictMock<GMockMetaStorage>>();
    NewHAFlexCtx(EditLogConf::HA);
  }

  void NewHAFlexCtx(EditLogConf::HAMode mode) {
    inner_edit_log_ctx_ = new testing::StrictMock<GMockEditLogContext>();
    ha_flex_ctx_ = std::make_unique<HAFlexibleEditLogContext>(
        &gmock_ha_state_, gmock_meta_storage_.get(), mode, nullptr, 0);
    ha_flex_ctx_->TestOnlySetCurrentInnerEditLogCtx(
        std::unique_ptr<EditLogContextBase>(inner_edit_log_ctx_));
    EXPECT_CALL(*inner_edit_log_ctx_, SetupSyncListener(testing::_)).Times(1);
    ha_flex_ctx_->SetupSyncListener(gmock_meta_storage_);
  }

 protected:
  testing::StrictMock<GMockHAState> gmock_ha_state_;
  std::shared_ptr<testing::StrictMock<GMockMetaStorage>> gmock_meta_storage_;
  testing::StrictMock<GMockEditLogContext>* inner_edit_log_ctx_;
  std::unique_ptr<HAFlexibleEditLogContext> ha_flex_ctx_;
};

TEST_F(HAFlexibleEditLogContextTest, InitHAEditLogContextByDefault) {
  EXPECT_CALL(
      *gmock_meta_storage_,
      GetNameSystemInfo(testing::Property(&cnetpp::base::StringPiece::as_string,
                                          kEditLogConfKey),
                        testing::Not(nullptr),
                        nullptr))
      .Times(1)
      .WillOnce(testing::Return(false));
  ha_flex_ctx_->SetMetaStorage(gmock_meta_storage_.get());
  EXPECT_EQ(ha_flex_ctx_->TestOnlyGetCurrentInnerEditLogCtx()->GetHAMode(),
            EditLogConf::HA);
}

TEST_F(HAFlexibleEditLogContextTest, InitHAEditLogContextByConf) {
  EditLogConf conf;
  conf.set_mode(EditLogConf::HA);
  EXPECT_CALL(
      *gmock_meta_storage_,
      GetNameSystemInfo(testing::Property(&cnetpp::base::StringPiece::as_string,
                                          kEditLogConfKey),
                        testing::Not(nullptr),
                        nullptr))
      .Times(1)
      .WillOnce(
          testing::DoAll(testing::SetArgPointee<1>(conf.SerializeAsString()),
                         testing::Return(true)));
  ha_flex_ctx_->SetMetaStorage(gmock_meta_storage_.get());
  EXPECT_EQ(ha_flex_ctx_->TestOnlyGetCurrentInnerEditLogCtx()->GetHAMode(),
            EditLogConf::HA);
}

TEST_F(HAFlexibleEditLogContextTest, InitNonHAEditLogContext) {
  EditLogConf conf;
  conf.set_mode(EditLogConf::ActiveNonHA);
  EXPECT_CALL(
      *gmock_meta_storage_,
      GetNameSystemInfo(testing::Property(&cnetpp::base::StringPiece::as_string,
                                          kEditLogConfKey),
                        testing::Not(nullptr),
                        nullptr))
      .Times(1)
      .WillOnce(
          testing::DoAll(testing::SetArgPointee<1>(conf.SerializeAsString()),
                         testing::Return(true)));
  ha_flex_ctx_->SetMetaStorage(gmock_meta_storage_.get());
  EXPECT_EQ(ha_flex_ctx_->TestOnlyGetCurrentInnerEditLogCtx()->GetHAMode(),
            EditLogConf::NonHA);
}

TEST_F(HAFlexibleEditLogContextTest, GetHAMode) {
  NewHAFlexCtx(EditLogConf::HA);
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode)
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  EXPECT_EQ(ha_flex_ctx_->GetHAMode(), EditLogConf::HA);

  NewHAFlexCtx(EditLogConf::ActiveNonHA);
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode)
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));
  EXPECT_EQ(ha_flex_ctx_->GetHAMode(), EditLogConf::ActiveNonHA);

  NewHAFlexCtx(EditLogConf::StandbyNonHA);
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode)
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));
  EXPECT_EQ(ha_flex_ctx_->GetHAMode(), EditLogConf::StandbyNonHA);
}

TEST_F(HAFlexibleEditLogContextTest, UpdateConfProperty) {
  EXPECT_CALL(*inner_edit_log_ctx_, UpdateConfProperty("name", "value"))
      .Times(2)
      .WillOnce(testing::Return(true))
      .WillOnce(testing::Return(false));
  EXPECT_TRUE(ha_flex_ctx_->UpdateConfProperty("name", "value"));
  EXPECT_FALSE(ha_flex_ctx_->UpdateConfProperty("name", "value"));
}

TEST_F(HAFlexibleEditLogContextTest, SetupSyncListener) {
  std::shared_ptr<IEditLogSyncListener> listener =
      std::make_shared<GMockMetaStorage>();
  EXPECT_CALL(*inner_edit_log_ctx_,
              SetupSyncListener(testing::Property(
                  &std::shared_ptr<IEditLogSyncListener>::get, listener.get())))
      .Times(1);
  ha_flex_ctx_->SetupSyncListener(listener);
}

TEST_F(HAFlexibleEditLogContextTest, GetPeerNNAddr) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetPeerNNAddr(testing::Not(nullptr)))
      .Times(2)
      .WillOnce(testing::DoAll(testing::SetArgPointee<0>("a"),  //
                               testing::Return(true)))
      .WillOnce(testing::DoAll(testing::SetArgPointee<0>("b"),
                               testing::Return(false)));
  std::string peer_nn_addr;
  EXPECT_TRUE(ha_flex_ctx_->GetPeerNNAddr(&peer_nn_addr));
  EXPECT_EQ(peer_nn_addr, "a");
  EXPECT_FALSE(ha_flex_ctx_->GetPeerNNAddr(&peer_nn_addr));
  EXPECT_EQ(peer_nn_addr, "b");
}

TEST_F(HAFlexibleEditLogContextTest, GetAllStackTraces) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetAllStackTraces(testing::Not(nullptr)))
      .Times(2)
      .WillOnce(testing::DoAll(testing::SetArgPointee<0>("a"),  //
                               testing::Return(true)))
      .WillOnce(testing::DoAll(testing::SetArgPointee<0>("b"),
                               testing::Return(false)));
  std::string stack_info;
  EXPECT_TRUE(ha_flex_ctx_->GetAllStackTraces(&stack_info));
  EXPECT_EQ(stack_info, "a");
  EXPECT_FALSE(ha_flex_ctx_->GetAllStackTraces(&stack_info));
  EXPECT_EQ(stack_info, "b");
}

TEST_F(HAFlexibleEditLogContextTest, IsOpenForRead) {
  EXPECT_CALL(*inner_edit_log_ctx_, IsOpenForRead)
      .Times(2)
      .WillOnce(testing::Return(true))
      .WillOnce(testing::Return(false));
  EXPECT_TRUE(ha_flex_ctx_->IsOpenForRead());
  EXPECT_FALSE(ha_flex_ctx_->IsOpenForRead());
}

TEST_F(HAFlexibleEditLogContextTest, OpenForRead) {
  EXPECT_CALL(*inner_edit_log_ctx_, OpenForRead).Times(1);
  ha_flex_ctx_->OpenForRead();
}

TEST_F(HAFlexibleEditLogContextTest, CreateInputContext) {
  GMockEditLogInputContext* gmock_input_ctx = new GMockEditLogInputContext();
  EXPECT_CALL(*inner_edit_log_ctx_, CreateInputContext(1, 2, false))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<EditLogInputContextBase>(gmock_input_ctx))));
  auto input_ctx = ha_flex_ctx_->CreateInputContext(1, 2, false);
  EXPECT_EQ(reinterpret_cast<GMockEditLogInputContext*>(input_ctx.get()),
            gmock_input_ctx);
}

TEST_F(HAFlexibleEditLogContextTest, IsOpenForWrite) {
  EXPECT_CALL(*inner_edit_log_ctx_, IsOpenForWrite)
      .Times(2)
      .WillOnce(testing::Return(true))
      .WillOnce(testing::Return(false));
  EXPECT_TRUE(ha_flex_ctx_->IsOpenForWrite());
  EXPECT_FALSE(ha_flex_ctx_->IsOpenForWrite());
}

TEST_F(HAFlexibleEditLogContextTest, InitJournalsForWrite) {
  EXPECT_CALL(*inner_edit_log_ctx_, InitJournalsForWrite).Times(1);
  ha_flex_ctx_->InitJournalsForWrite();
}

TEST_F(HAFlexibleEditLogContextTest, OpenForWrite) {
  EXPECT_CALL(*inner_edit_log_ctx_, OpenForWrite)
      .Times(1)
      .WillOnce(testing::Return(1335));
  EXPECT_EQ(ha_flex_ctx_->OpenForWrite(), 1335);
}

TEST_F(HAFlexibleEditLogContextTest, Close) {
  EXPECT_CALL(*inner_edit_log_ctx_, Close).Times(1);
  ha_flex_ctx_->Close();
}

TEST_F(HAFlexibleEditLogContextTest, GetLastWrittenTxId) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetLastWrittenTxId)
      .Times(1)
      .WillOnce(testing::Return(8236));
  EXPECT_EQ(ha_flex_ctx_->GetLastWrittenTxId(), 8236);
}

TEST_F(HAFlexibleEditLogContextTest, SetNextTxId) {
  EXPECT_CALL(*inner_edit_log_ctx_, SetNextTxId(2932)).Times(1);
  ha_flex_ctx_->SetNextTxId(2932);
}

TEST_F(HAFlexibleEditLogContextTest, GetLastAllocatedBlockId) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetLastAllocatedBlockId())
      .Times(1)
      .WillOnce(testing::Return(1588));
  EXPECT_EQ(ha_flex_ctx_->GetLastAllocatedBlockId(), 1588);
}

TEST_F(HAFlexibleEditLogContextTest, SetLastAllocatedBlockId) {
  EXPECT_CALL(*inner_edit_log_ctx_, SetLastAllocatedBlockId(8618)).Times(1);
  ha_flex_ctx_->SetLastAllocatedBlockId(8618);
}

TEST_F(HAFlexibleEditLogContextTest, GetLastGenerationStampV2) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetLastGenerationStampV2())
      .Times(1)
      .WillOnce(testing::Return(5597));
  EXPECT_EQ(ha_flex_ctx_->GetLastGenerationStampV2(), 5597);
}

TEST_F(HAFlexibleEditLogContextTest, SetLastGenerationStampV2) {
  EXPECT_CALL(*inner_edit_log_ctx_, SetLastGenerationStampV2(194)).Times(1);
  ha_flex_ctx_->SetLastGenerationStampV2(194);
}

TEST_F(HAFlexibleEditLogContextTest, GetCurSegmentTxId) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetCurSegmentTxId())
      .Times(1)
      .WillOnce(testing::Return(3656));
  EXPECT_EQ(ha_flex_ctx_->GetCurSegmentTxId(), 3656);
}

TEST_F(HAFlexibleEditLogContextTest, GetWaitSyncTime) {
  EXPECT_CALL(*inner_edit_log_ctx_, GetWaitSyncTime())
      .Times(1)
      .WillOnce(testing::Return(7818));
  EXPECT_EQ(ha_flex_ctx_->GetWaitSyncTime(), 7818);
}

TEST_F(HAFlexibleEditLogContextTest, LogSync) {
  testing::InSequence _;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSync(false)).Times(1);
  EXPECT_CALL(*inner_edit_log_ctx_, LogSync(true)).Times(1);
  ha_flex_ctx_->LogSync(false);
  ha_flex_ctx_->LogSync(true);
}

TEST_F(HAFlexibleEditLogContextTest, LogSyncAll) {
  testing::InSequence _;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSyncAll()).Times(1);
  ha_flex_ctx_->LogSyncAll();
}

TEST_F(HAFlexibleEditLogContextTest, RollEditLog) {
  EXPECT_CALL(*inner_edit_log_ctx_, RollEditLog())
      .Times(1)
      .WillOnce(testing::Return(195));
  EXPECT_EQ(ha_flex_ctx_->RollEditLog(), 195);
}

TEST_F(HAFlexibleEditLogContextTest, PurgeLogsOlderThan) {
  testing::InSequence _;
  EXPECT_CALL(*inner_edit_log_ctx_, PurgeLogsOlderThan(7701)).Times(1);
  ha_flex_ctx_->PurgeLogsOlderThan(7701);
}

TEST_F(HAFlexibleEditLogContextTest, LogCfsOp) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogCfsOp(&ss))
      .Times(1)
      .WillOnce(testing::Return(1880));
  EXPECT_EQ(ha_flex_ctx_->LogCfsOp(&ss), 1880);
}

TEST_F(HAFlexibleEditLogContextTest, LogAllocateBlockId) {
  std::stringstream ss;
  BlockID block_id = kInvalidBlockID;
  EXPECT_CALL(*inner_edit_log_ctx_, LogAllocateBlockId(&ss, &block_id))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(3313),
                               testing::Return(834)));
  EXPECT_EQ(ha_flex_ctx_->LogAllocateBlockId(&ss, &block_id), 834);
  EXPECT_EQ(block_id, 3313);
}

TEST_F(HAFlexibleEditLogContextTest, LogGenerationStampV1) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogGenerationStampV1(&ss))
      .Times(1)
      .WillOnce(testing::Return(2226));
  EXPECT_EQ(ha_flex_ctx_->LogGenerationStampV1(&ss), 2226);
}

TEST_F(HAFlexibleEditLogContextTest, LogGenerationStampV2) {
  std::stringstream ss;
  uint64_t gsv2 = 0;
  EXPECT_CALL(*inner_edit_log_ctx_, LogGenerationStampV2(&ss, &gsv2))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(652),
                               testing::Return(8884)));
  EXPECT_EQ(ha_flex_ctx_->LogGenerationStampV2(&ss, &gsv2), 8884);
  EXPECT_EQ(gsv2, 652);
}

TEST_F(HAFlexibleEditLogContextTest, LogAllocateBlockIdAndGSv2) {
  std::stringstream blkid_ss;
  std::stringstream gsv2_ss;
  BlockID block_id = kInvalidBlockID;
  uint64_t gsv2 = 0;
  EXPECT_CALL(*inner_edit_log_ctx_,
              LogAllocateBlockIdAndGSv2(&blkid_ss, &gsv2_ss, &block_id, &gsv2))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<2>(3845),
                               testing::SetArgPointee<3>(3326),
                               testing::Return(784)));
  EXPECT_EQ(ha_flex_ctx_->LogAllocateBlockIdAndGSv2(
                &blkid_ss, &gsv2_ss, &block_id, &gsv2),
            784);
  EXPECT_EQ(block_id, 3845);
  EXPECT_EQ(gsv2, 3326);
}

TEST_F(HAFlexibleEditLogContextTest, LogTimes) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogTimes(&ss))
      .Times(1)
      .WillOnce(testing::Return(5736));
  EXPECT_EQ(ha_flex_ctx_->LogTimes(&ss), 5736);
}

TEST_F(HAFlexibleEditLogContextTest, LogOpenFile) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogOpenFile(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(8927));
  EXPECT_CALL(*inner_edit_log_ctx_, LogOpenFile(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(7427));
  EXPECT_EQ(ha_flex_ctx_->LogOpenFile(&ss, true), 8927);
  EXPECT_EQ(ha_flex_ctx_->LogOpenFile(&ss, false), 7427);
}

TEST_F(HAFlexibleEditLogContextTest, LogAddBlock) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogAddBlock(&ss))
      .Times(1)
      .WillOnce(testing::Return(2012));
  EXPECT_EQ(ha_flex_ctx_->LogAddBlock(&ss), 2012);
}

TEST_F(HAFlexibleEditLogContextTest, LogUpdateBlocks) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogUpdateBlocks(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(4006));
  EXPECT_CALL(*inner_edit_log_ctx_, LogUpdateBlocks(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(1802));
  EXPECT_EQ(ha_flex_ctx_->LogUpdateBlocks(&ss, true), 4006);
  EXPECT_EQ(ha_flex_ctx_->LogUpdateBlocks(&ss, false), 1802);
}

TEST_F(HAFlexibleEditLogContextTest, LogCloseFile) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogCloseFile(&ss))
      .Times(1)
      .WillOnce(testing::Return(302));
  EXPECT_EQ(ha_flex_ctx_->LogCloseFile(&ss), 302);
}

TEST_F(HAFlexibleEditLogContextTest, LogReassignLease) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogReassignLease(&ss))
      .Times(1)
      .WillOnce(testing::Return(6016));
  EXPECT_EQ(ha_flex_ctx_->LogReassignLease(&ss), 6016);
}

TEST_F(HAFlexibleEditLogContextTest, LogConcat) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogConcat(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(6254));
  EXPECT_CALL(*inner_edit_log_ctx_, LogConcat(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(6198));
  EXPECT_EQ(ha_flex_ctx_->LogConcat(&ss, true), 6254);
  EXPECT_EQ(ha_flex_ctx_->LogConcat(&ss, false), 6198);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetBlockPufsInfo) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetBlockPufsInfo(&ss))
      .Times(1)
      .WillOnce(testing::Return(398));
  EXPECT_EQ(ha_flex_ctx_->LogSetBlockPufsInfo(&ss), 398);
}

TEST_F(HAFlexibleEditLogContextTest, LogDeleteDeprecatedBlockPufsInfo) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogDeleteDeprecatedBlockPufsInfo(&ss))
      .Times(1)
      .WillOnce(testing::Return(642));
  EXPECT_EQ(ha_flex_ctx_->LogDeleteDeprecatedBlockPufsInfo(&ss), 642);
}

TEST_F(HAFlexibleEditLogContextTest, LogMkDir) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogMkDir(&ss))
      .Times(1)
      .WillOnce(testing::Return(3107));
  EXPECT_EQ(ha_flex_ctx_->LogMkDir(&ss), 3107);
}

TEST_F(HAFlexibleEditLogContextTest, LogDelete) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogDelete(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(5478));
  EXPECT_CALL(*inner_edit_log_ctx_, LogDelete(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(4134));
  EXPECT_EQ(ha_flex_ctx_->LogDelete(&ss, true), 5478);
  EXPECT_EQ(ha_flex_ctx_->LogDelete(&ss, false), 4134);
}

TEST_F(HAFlexibleEditLogContextTest, LogRenameOld) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogRenameOld(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(1062));
  EXPECT_CALL(*inner_edit_log_ctx_, LogRenameOld(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(1720));
  EXPECT_EQ(ha_flex_ctx_->LogRenameOld(&ss, true), 1062);
  EXPECT_EQ(ha_flex_ctx_->LogRenameOld(&ss, false), 1720);
}

TEST_F(HAFlexibleEditLogContextTest, LogRename) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogRename(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(9704));
  EXPECT_CALL(*inner_edit_log_ctx_, LogRename(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(5376));
  EXPECT_EQ(ha_flex_ctx_->LogRename(&ss, true), 9704);
  EXPECT_EQ(ha_flex_ctx_->LogRename(&ss, false), 5376);
}

TEST_F(HAFlexibleEditLogContextTest, LogSymlink) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSymlink(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(7827));
  EXPECT_CALL(*inner_edit_log_ctx_, LogSymlink(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(1830));
  EXPECT_EQ(ha_flex_ctx_->LogSymlink(&ss, true), 7827);
  EXPECT_EQ(ha_flex_ctx_->LogSymlink(&ss, false), 1830);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetReplication) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetReplication(&ss))
      .Times(1)
      .WillOnce(testing::Return(2621));
  EXPECT_EQ(ha_flex_ctx_->LogSetReplication(&ss), 2621);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetStoragePolicy) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetStoragePolicy(&ss))
      .Times(1)
      .WillOnce(testing::Return(8704));
  EXPECT_EQ(ha_flex_ctx_->LogSetStoragePolicy(&ss), 8704);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetReplicaPolicy) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetReplicaPolicy(&ss))
      .Times(1)
      .WillOnce(testing::Return(7432));
  EXPECT_EQ(ha_flex_ctx_->LogSetReplicaPolicy(&ss), 7432);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetDirReplicaPolicy) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetDirReplicaPolicy(&ss))
      .Times(1)
      .WillOnce(testing::Return(5623));
  EXPECT_EQ(ha_flex_ctx_->LogSetDirReplicaPolicy(&ss), 5623);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetQuota) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetQuota(&ss))
      .Times(1)
      .WillOnce(testing::Return(3891));
  EXPECT_EQ(ha_flex_ctx_->LogSetQuota(&ss), 3891);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetPermissions) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetPermissions(&ss))
      .Times(1)
      .WillOnce(testing::Return(2976));
  EXPECT_EQ(ha_flex_ctx_->LogSetPermissions(&ss), 2976);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetOwner) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetOwner(&ss))
      .Times(1)
      .WillOnce(testing::Return(4217));
  EXPECT_EQ(ha_flex_ctx_->LogSetOwner(&ss), 4217);
}

TEST_F(HAFlexibleEditLogContextTest, LogSetXAttrs) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetXAttrs(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(4478));
  EXPECT_CALL(*inner_edit_log_ctx_, LogSetXAttrs(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(7989));
  EXPECT_EQ(ha_flex_ctx_->LogSetXAttrs(&ss, true), 4478);
  EXPECT_EQ(ha_flex_ctx_->LogSetXAttrs(&ss, false), 7989);
}

TEST_F(HAFlexibleEditLogContextTest, LogRemoveXAttrs) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogRemoveXAttrs(&ss, true))
      .Times(1)
      .WillOnce(testing::Return(5556));
  EXPECT_CALL(*inner_edit_log_ctx_, LogRemoveXAttrs(&ss, false))
      .Times(1)
      .WillOnce(testing::Return(8693));
  EXPECT_EQ(ha_flex_ctx_->LogRemoveXAttrs(&ss, true), 5556);
  EXPECT_EQ(ha_flex_ctx_->LogRemoveXAttrs(&ss, false), 8693);
}

TEST_F(HAFlexibleEditLogContextTest, LogAllowSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogAllowSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(3542));
  EXPECT_EQ(ha_flex_ctx_->LogAllowSnapshot(&ss), 3542);
}

TEST_F(HAFlexibleEditLogContextTest, LogDisallowSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogDisallowSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(2987));
  EXPECT_EQ(ha_flex_ctx_->LogDisallowSnapshot(&ss), 2987);
}

TEST_F(HAFlexibleEditLogContextTest, LogCreateSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogCreateSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(6745));
  EXPECT_EQ(ha_flex_ctx_->LogCreateSnapshot(&ss), 6745);
}

TEST_F(HAFlexibleEditLogContextTest, LogDeleteSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogDeleteSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(8346));
  EXPECT_EQ(ha_flex_ctx_->LogDeleteSnapshot(&ss), 8346);
}

TEST_F(HAFlexibleEditLogContextTest, LogRenameSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogRenameSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(7542));
  EXPECT_EQ(ha_flex_ctx_->LogRenameSnapshot(&ss), 7542);
}

TEST_F(HAFlexibleEditLogContextTest, LogAccessCounterSnapshot) {
  std::stringstream ss;
  EXPECT_CALL(*inner_edit_log_ctx_, LogAccessCounterSnapshot(&ss))
      .Times(1)
      .WillOnce(testing::Return(1023));
  EXPECT_EQ(ha_flex_ctx_->LogAccessCounterSnapshot(&ss), 1023);
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchHAActiveToNonHAActiveFailedIfIsStandby) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(false));

  Status s = ha_flex_ctx_->SwitchHAActiveToNonHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_THAT(
      s.message(),
      testing::HasSubstr("Converting a standby node in High Availability"));
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchHAActiveToNonHAActiveFailedIfCreateCheckpointFailed) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(*gmock_meta_storage_,
              CreateCheckpoint(
                  testing::HasSubstr("/tmp/ckpt_before_switch_to_non_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kError)));

  Status s = ha_flex_ctx_->SwitchHAActiveToNonHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_THAT(s.message(),
              testing::HasSubstr("Create checkpoint failed, path: "
                                 "/tmp/ckpt_before_switch_to_non_ha."));
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchHAActiveToNonHAActiveFailedIfInnerCtxIsNonHAMode) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(*gmock_meta_storage_,
              CreateCheckpoint(
                  testing::HasSubstr("/tmp/ckpt_before_switch_to_non_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // [Step 3]

  // [Step 4]
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));

  EXPECT_EQ(ha_flex_ctx_->SwitchHAActiveToNonHAActive().code(), Code::kIsRetry);
}

class GMockSwitchHAActiveToNonHAActiveOp
    : public SwitchHAActiveToNonHAActiveOp {
 public:
  GMockSwitchHAActiveToNonHAActiveOp(HAFlexibleEditLogContext* flexible_ctx)
      : SwitchHAActiveToNonHAActiveOp(flexible_ctx) {
  }

  MOCK_METHOD0(NewEditLogContext, std::unique_ptr<EditLogContextBase>());
};

TEST_F(HAFlexibleEditLogContextTest, SwitchHAActiveToNonHAActiveSucceed) {
  testing::InSequence _;

  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(*gmock_meta_storage_,
              CreateCheckpoint(
                  testing::HasSubstr("/tmp/ckpt_before_switch_to_non_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // [Step 3]
  // We lack a method for testing whether the
  // HAFlexibleEditLogContext::get_ctx_rwlock_ has been acquired.

  // [Step 4]
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  //
  EditLogConf::PreviousEditLogConf previous_edit_log_conf;
  previous_edit_log_conf.set_pending_begin_txid(2248);
  previous_edit_log_conf.set_pending_end_txid(4248);
  previous_edit_log_conf.set_reserved_begin_txid(4248);
  previous_edit_log_conf.set_reserved_end_txid(5248);
  previous_edit_log_conf.set_last_allocated_block_id(1024);
  previous_edit_log_conf.set_last_generation_stamp_v2(1000);
  previous_edit_log_conf.set_is_ha_edit_log_conf(true);
  EXPECT_CALL(*inner_edit_log_ctx_, HASwitchFence())
      .Times(1)
      .WillOnce(testing::Return(previous_edit_log_conf));
  //
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(2248, 5248 - 2248)).Times(1);
  EXPECT_CALL(
      *gmock_meta_storage_,
      PushINodeTXWriteTask(testing::_,
                           testing::AllOf(testing::Ge(4248), testing::Lt(5248)),
                           NameSpaceInfoDelta(),
                           testing::_,
                           nullptr))
      // [4248, 5248)
      .Times(5248 - 4248);
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  // [Step 5]
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5248, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutNameSystemInfo(
                  kLastAllocatedBlockIdKey,
                  testing::Truly([](cnetpp::base::StringPiece value) {
                    // Refer to NameSpace::LoadLastAllocatedBlockId.
                    return value.length() == sizeof(uint64_t) &&
                           platform::ReadBigEndian<uint64_t>(value.data(), 0) ==
                               1024;
                  }),
                  5248,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5249, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutGenerationStampV2(
                  1000,
                  5249,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5250, 1)).Times(1);
  EXPECT_CALL(
      *gmock_meta_storage_,
      OrderedPutNameSystemInfo(
          kEditLogConfKey,
          testing::Truly([](cnetpp::base::StringPiece value) {
            EditLogConf conf;
            return conf.ParseFromString(value.as_string()) &&
                   conf.IsInitialized() &&
                   conf.mode() == EditLogConf::ActiveNonHA &&
                   conf.previous_edit_log_conf().last_allocated_block_id() ==
                       1024 &&
                   conf.previous_edit_log_conf().last_generation_stamp_v2() ==
                       1000;
          }),
          5250,
          testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<3>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(5250));

  // [Step 6]
  GMockSwitchHAActiveToNonHAActiveOp switch_to_non_ha_op(ha_flex_ctx_.get());
  auto new_edit_log_ctx = new testing::StrictMock<GMockEditLogContext>();
  EXPECT_CALL(switch_to_non_ha_op, NewEditLogContext())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<testing::StrictMock<GMockEditLogContext>>(
              new_edit_log_ctx))));
  EXPECT_CALL(*new_edit_log_ctx, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));
  EXPECT_CALL(*new_edit_log_ctx, SetupSyncListener(testing::_)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, InitJournalsForWrite()).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetNextTxId(5251)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastAllocatedBlockId(1024)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastGenerationStampV2(1000)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, OpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(5251));
  EXPECT_CALL(*new_edit_log_ctx, IsOpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(true));
  // EXPECT_CALL(*gmock_meta_storage_, TxFinish(5251, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              PushINodeTXWriteTask(testing::_,
                                   5251,
                                   NameSpaceInfoDelta(),
                                   testing::_,
                                   testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  EXPECT_TRUE(switch_to_non_ha_op().IsOK());
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchNonHAActiveToHAActiveFailedIfIsStandby) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(false));

  Status s = ha_flex_ctx_->SwitchNonHAActiveToHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_THAT(s.message(),
              testing::HasSubstr("It is not recommended to switch a standby"));
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchNonHAActiveToHAActiveFailedIfCreateCheckpointFailed) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(
      *gmock_meta_storage_,
      CreateCheckpoint(testing::HasSubstr("/tmp/ckpt_before_switch_to_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kError)));

  Status s = ha_flex_ctx_->SwitchNonHAActiveToHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_THAT(s.message(),
              testing::HasSubstr("Create checkpoint failed, path: "
                                 "/tmp/ckpt_before_switch_to_ha."));
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchNonHAActiveToHAActiveFailedIfInnerCtxIsHAMode) {
  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(
      *gmock_meta_storage_,
      CreateCheckpoint(testing::HasSubstr("/tmp/ckpt_before_switch_to_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // [Step 3]

  // [Step 4]
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));

  EXPECT_EQ(ha_flex_ctx_->SwitchNonHAActiveToHAActive().code(), Code::kIsRetry);
}

class GMockSwitchNonHAActiveToHAActiveOp
    : public SwitchNonHAActiveToHAActiveOp {
 public:
  GMockSwitchNonHAActiveToHAActiveOp(HAFlexibleEditLogContext* flexible_ctx)
      : SwitchNonHAActiveToHAActiveOp(flexible_ctx) {
  }

  MOCK_METHOD0(NewEditLogContext, std::unique_ptr<EditLogContextBase>());
};

TEST_F(HAFlexibleEditLogContextTest, SwitchNonHAActiveToHAActiveSucceed) {
  testing::InSequence _;

  // [Step 1]
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(TwoStepVUniqueLock())));
  EXPECT_CALL(gmock_ha_state_, IsActive())
      .Times(1)
      .WillOnce(testing::Return(true));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(
      *gmock_meta_storage_,
      CreateCheckpoint(testing::HasSubstr("/tmp/ckpt_before_switch_to_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // [Step 3]
  // We lack a method for testing whether the
  // HAFlexibleEditLogContext::get_ctx_rwlock_ has been acquired.

  // [Step 4]
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));
  //
  EditLogConf::PreviousEditLogConf previous_edit_log_conf;
  previous_edit_log_conf.set_pending_begin_txid(2248);
  previous_edit_log_conf.set_pending_end_txid(4248);
  previous_edit_log_conf.set_reserved_begin_txid(4248);
  previous_edit_log_conf.set_reserved_end_txid(5248);
  previous_edit_log_conf.set_last_allocated_block_id(1024);
  previous_edit_log_conf.set_last_generation_stamp_v2(1000);
  previous_edit_log_conf.set_is_ha_edit_log_conf(false);
  EXPECT_CALL(*inner_edit_log_ctx_, HASwitchFence())
      .Times(1)
      .WillOnce(testing::Return(previous_edit_log_conf));
  //
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(2248, 5248 - 2248)).Times(1);
  EXPECT_CALL(
      *gmock_meta_storage_,
      PushINodeTXWriteTask(testing::_,
                           testing::AllOf(testing::Ge(4248), testing::Lt(5248)),
                           NameSpaceInfoDelta(),
                           testing::_,
                           nullptr))
      // [4248, 5248)
      .Times(5248 - 4248);
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  // [Step 5]
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5248, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutNameSystemInfo(
                  kLastAllocatedBlockIdKey,
                  testing::Truly([](cnetpp::base::StringPiece value) {
                    // Refer to NameSpace::LoadLastAllocatedBlockId.
                    return value.length() == sizeof(uint64_t) &&
                           platform::ReadBigEndian<uint64_t>(value.data(), 0) ==
                               1024;
                  }),
                  5248,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5249, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutGenerationStampV2(
                  1000,
                  5249,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(5250, 1)).Times(1);
  EXPECT_CALL(
      *gmock_meta_storage_,
      OrderedPutNameSystemInfo(
          kEditLogConfKey,
          testing::Truly([](cnetpp::base::StringPiece value) {
            EditLogConf conf;
            return conf.ParseFromString(value.as_string()) &&
                   conf.IsInitialized() && conf.mode() == EditLogConf::HA &&
                   conf.previous_edit_log_conf().last_allocated_block_id() ==
                       1024 &&
                   conf.previous_edit_log_conf().last_generation_stamp_v2() ==
                       1000;
          }),
          5250,
          testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<3>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(5250));

  // [Step 6]
  GMockSwitchNonHAActiveToHAActiveOp switch_to_ha_op(ha_flex_ctx_.get());
  auto new_edit_log_ctx = new testing::StrictMock<GMockEditLogContext>();
  EXPECT_CALL(switch_to_ha_op, NewEditLogContext())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<testing::StrictMock<GMockEditLogContext>>(
              new_edit_log_ctx))));
  EXPECT_CALL(*new_edit_log_ctx, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  EXPECT_CALL(*new_edit_log_ctx, SetupSyncListener(testing::_)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, InitJournalsForWrite()).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetNextTxId(5251)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastAllocatedBlockId(1024)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastGenerationStampV2(1000)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, OpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(5251));
  EXPECT_CALL(*new_edit_log_ctx, IsOpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(true));
  // EXPECT_CALL(*gmock_meta_storage_, TxFinish(5251, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              PushINodeTXWriteTask(testing::_,
                                   5251,
                                   NameSpaceInfoDelta(),
                                   testing::_,
                                   testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  EXPECT_TRUE(switch_to_ha_op().IsOK());
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchHAStandbyToNonHAActiveFailedIfIsActive) {
  // [Step 1]
  VRWLock vlock(1);
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(
          testing::Return(testing::ByMove(TwoStepVUniqueLock(vlock.lock()))));
  EXPECT_CALL(gmock_ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::ACTIVE));

  Status s = ha_flex_ctx_->SwitchHAStandbyToNonHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_EQ(s.message(), "Current ha state is not standby.");
}

TEST_F(HAFlexibleEditLogContextTest,
       SwitchHAStandbyToNonHAActiveFailedIfCreateCheckpointFailed) {
  // [Step 1]
  VRWLock vlock(1);
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(
          testing::Return(testing::ByMove(TwoStepVUniqueLock(vlock.lock()))));
  EXPECT_CALL(gmock_ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(*gmock_meta_storage_,
              CreateCheckpoint(
                  testing::HasSubstr("/tmp/ckpt_before_switch_to_non_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status(Code::kError)));

  Status s = ha_flex_ctx_->SwitchHAStandbyToNonHAActive();
  EXPECT_EQ(s.code(), Code::kError);
  EXPECT_THAT(s.message(),
              testing::HasSubstr("Create checkpoint failed, path: "
                                 "/tmp/ckpt_before_switch_to_non_ha."));
}

class GMockSwitchHAStandbyToNonHAActiveOp
    : public SwitchHAStandbyToNonHAActiveOp {
 public:
  GMockSwitchHAStandbyToNonHAActiveOp(HAFlexibleEditLogContext* flexible_ctx)
      : SwitchHAStandbyToNonHAActiveOp(flexible_ctx) {
  }

  MOCK_METHOD0(NewEditLogContext, std::unique_ptr<EditLogContextBase>());
};

TEST_F(HAFlexibleEditLogContextTest, SwitchHAStandbyToNonHAActiveSucceed) {
  testing::InSequence _;

  // [Step 1]
  VRWLock vlock(1);
  EXPECT_CALL(gmock_ha_state_, LockBarrierForHASwitcher())
      .Times(1)
      .WillOnce(
          testing::Return(testing::ByMove(TwoStepVUniqueLock(vlock.lock()))));
  EXPECT_CALL(gmock_ha_state_, GetState())
      .Times(1)
      .WillOnce(testing::Return(cloudfs::HAServiceStateProto::STANDBY));

  // [Step 2]
  std::string namespace_meta_storage_ckpt_path =
      FLAGS_namespace_meta_storage_ckpt_path;
  FLAGS_namespace_meta_storage_ckpt_path = "/tmp";
  DEFER([&]() {
    FLAGS_namespace_meta_storage_ckpt_path = namespace_meta_storage_ckpt_path;
  });
  EXPECT_CALL(*gmock_meta_storage_,
              CreateCheckpoint(
                  testing::HasSubstr("/tmp/ckpt_before_switch_to_non_ha.")))
      .Times(1)
      .WillOnce(testing::Return(Status()));

  // [Step 4]
  EXPECT_CALL(*inner_edit_log_ctx_, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::HA));
  //
  EXPECT_CALL(gmock_ha_state_, IsTailerStopped())
      .Times(1)
      .WillOnce(testing::Return(true));
  //
  EXPECT_CALL(*gmock_meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(2248));
  std::string block_id_str;
  block_id_str.resize(sizeof(BlockID));
  platform::WriteBigEndian(&block_id_str[0], 0, 1073741824UL);
  EXPECT_CALL(
      *gmock_meta_storage_,
      GetNameSystemInfo(testing::Property(&cnetpp::base::StringPiece::as_string,
                                          kLastAllocatedBlockIdKey),
                        testing::Not(nullptr),
                        nullptr))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(block_id_str),
                               testing::Return(true)));
  std::string gsv2_str;
  gsv2_str.resize(sizeof(uint64_t));
  platform::WriteBigEndian(&gsv2_str[0], 0, 2000UL);
  EXPECT_CALL(
      *gmock_meta_storage_,
      GetNameSystemInfo(testing::Property(&cnetpp::base::StringPiece::as_string,
                                          kGenerationStampV2Key),
                        testing::Not(nullptr),
                        nullptr))
      .Times(1)
      .WillOnce(testing::DoAll(testing::SetArgPointee<1>(gsv2_str),
                               testing::Return(true)));
  //
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(2249, 18000000)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              PushINodeTXWriteTask(
                  testing::_,
                  testing::AllOf(testing::Ge(2249), testing::Lt(18002249)),
                  NameSpaceInfoDelta(),
                  testing::_,
                  nullptr))
      .Times(18000000);
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  // [Step 5]
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(18002249, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutNameSystemInfo(
                  kLastAllocatedBlockIdKey,
                  testing::Truly([](cnetpp::base::StringPiece value) {
                    // Refer to NameSpace::LoadLastAllocatedBlockId.
                    return value.length() == sizeof(uint64_t) &&
                           platform::ReadBigEndian<uint64_t>(value.data(), 0) ==
                               1073741824 + 65536;
                  }),
                  18002249,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(18002250, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              OrderedPutGenerationStampV2(
                  2000 + 65536,
                  18002250,
                  nullptr))
      .Times(1);
  EXPECT_CALL(*gmock_meta_storage_, TxFinish(18002251, 1)).Times(1);
  EXPECT_CALL(
      *gmock_meta_storage_,
      OrderedPutNameSystemInfo(
          kEditLogConfKey,
          testing::Truly([](cnetpp::base::StringPiece value) {
            EditLogConf conf;
            return conf.ParseFromString(value.as_string()) &&
                   conf.IsInitialized() &&
                   conf.mode() == EditLogConf::ActiveNonHA &&
                   conf.previous_edit_log_conf().last_allocated_block_id() ==
                       1073741824 + 65536 &&
                   conf.previous_edit_log_conf().last_generation_stamp_v2() ==
                       2000 + 65536;
          }),
          18002251,
          testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<3>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_, GetLastCkptTxId(nullptr))
      .Times(1)
      .WillOnce(testing::Return(18002251));

  // [Step 6]
  GMockSwitchHAStandbyToNonHAActiveOp switch_to_non_ha_op(ha_flex_ctx_.get());
  auto new_edit_log_ctx = new testing::StrictMock<GMockEditLogContext>();
  EXPECT_CALL(switch_to_non_ha_op, NewEditLogContext())
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<testing::StrictMock<GMockEditLogContext>>(
              new_edit_log_ctx))));
  EXPECT_CALL(*new_edit_log_ctx, GetHAMode())
      .Times(1)
      .WillOnce(testing::Return(EditLogConf::NonHA));
  EXPECT_CALL(*new_edit_log_ctx, SetupSyncListener(testing::_)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, InitJournalsForWrite()).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetNextTxId(18002252)).Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastAllocatedBlockId(1073741824 + 65536))
      .Times(1);
  EXPECT_CALL(*new_edit_log_ctx, SetLastGenerationStampV2(2000 + 65536))
      .Times(1);
  EXPECT_CALL(*new_edit_log_ctx, OpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(18002252));
  EXPECT_CALL(*new_edit_log_ctx, IsOpenForWrite())
      .Times(1)
      .WillOnce(testing::Return(true));
  // EXPECT_CALL(*gmock_meta_storage_, TxFinish(18002252, 1)).Times(1);
  EXPECT_CALL(*gmock_meta_storage_,
              PushINodeTXWriteTask(testing::_,
                                   18002252,
                                   NameSpaceInfoDelta(),
                                   testing::_,
                                   testing::Not(nullptr)))
      .Times(1)
      .WillOnce(testing::WithArg<4>(
          testing::Invoke([](Closure* done) { done->Run(); })));
  EXPECT_CALL(*gmock_meta_storage_, WaitNoPending(false)).Times(1);

  EXPECT_TRUE(switch_to_non_ha_op().IsOK());
}

}  // namespace dancenn
