// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/12
// Description

#include "test/edit/gmock_ha_edit_log_context.h"

namespace dancenn {

GMockHAEditLogContext::GMockHAEditLogContext()
    : HAEditLogContext(),
      method_log_cfs_op_(reinterpret_cast<jmethodID>(0x1)),
      method_log_allocate_block_id_(reinterpret_cast<jmethodID>(0x2)),
      method_log_generation_stamp_v1_(reinterpret_cast<jmethodID>(0x3)),
      method_log_generation_stamp_v2_(reinterpret_cast<jmethodID>(0x4)),
      method_log_times_(reinterpret_cast<jmethodID>(0x5)),
      method_log_open_file_(reinterpret_cast<jmethodID>(0x6)),
      method_log_add_block_(reinterpret_cast<jmethodID>(0x7)),
      method_log_update_blocks_(reinterpret_cast<jmethodID>(0x8)),
      method_log_close_file_(reinterpret_cast<jmethodID>(0x9)),
      method_log_reassign_lease_(reinterpret_cast<jmethodID>(0x10)),
      method_log_concat_(reinterpret_cast<jmethodID>(0x11)),
      method_log_set_cfs_universal_info_(reinterpret_cast<jmethodID>(0x12)),
      method_log_mk_dir_(reinterpret_cast<jmethodID>(0x13)),
      method_log_delete_(reinterpret_cast<jmethodID>(0x14)),
      method_log_rename_old_(reinterpret_cast<jmethodID>(0x15)),
      method_log_rename_(reinterpret_cast<jmethodID>(0x16)),
      method_log_symlink_(reinterpret_cast<jmethodID>(0x17)),
      method_log_set_replication_(reinterpret_cast<jmethodID>(0x18)),
      method_log_set_storage_policy_(reinterpret_cast<jmethodID>(0x19)),
      method_log_set_replica_policy_(reinterpret_cast<jmethodID>(0x1a)),
      method_log_set_dir_replica_policy_(reinterpret_cast<jmethodID>(0x1b)),
      method_log_set_quota_(reinterpret_cast<jmethodID>(0x1c)),
      method_log_set_permission_(reinterpret_cast<jmethodID>(0x1d)),
      method_log_set_owner_(reinterpret_cast<jmethodID>(0x1e)),
      method_log_set_acl_(reinterpret_cast<jmethodID>(0x1f)),
      method_log_set_xttrs_(reinterpret_cast<jmethodID>(0x20)),
      method_log_remove_xattrs_(reinterpret_cast<jmethodID>(0x21)),
      method_log_access_counters_snapshot_(reinterpret_cast<jmethodID>(0x22)) {
  // Not deprecated.
  HAEditLogContext::method_log_cfs_op_ = this->method_log_cfs_op_;
  // Block related.
  HAEditLogContext::method_log_allocate_block_id_ =
      this->method_log_allocate_block_id_;
  HAEditLogContext::method_log_generation_stamp_v1_ =
      this->method_log_generation_stamp_v1_;
  HAEditLogContext::method_log_generation_stamp_v2_ =
      this->method_log_generation_stamp_v2_;
  // Dir tree related.
  HAEditLogContext::method_log_times_ = this->method_log_times_;

  // Deprecated.
  // File related.
  HAEditLogContext::method_log_open_file_ = this->method_log_open_file_;
  HAEditLogContext::method_log_add_block_ = this->method_log_add_block_;
  HAEditLogContext::method_log_update_blocks_ = this->method_log_update_blocks_;
  HAEditLogContext::method_log_close_file_ = this->method_log_close_file_;
  HAEditLogContext::method_log_reassign_lease_ =
      this->method_log_reassign_lease_;
  HAEditLogContext::method_log_concat_ = this->method_log_concat_;
  // Block related.
  HAEditLogContext::method_log_set_cfs_universal_info_ =
      this->method_log_set_cfs_universal_info_;
  // Dir tree related.
  HAEditLogContext::method_log_mk_dir_ = this->method_log_mk_dir_;
  HAEditLogContext::method_log_delete_ = this->method_log_delete_;
  HAEditLogContext::method_log_rename_old_ = this->method_log_rename_old_;
  HAEditLogContext::method_log_rename_ = this->method_log_rename_;
  HAEditLogContext::method_log_symlink_ = this->method_log_symlink_;
  // Set* related.
  HAEditLogContext::method_log_set_replication_ =
      this->method_log_set_replication_;
  HAEditLogContext::method_log_set_storage_policy_ =
      this->method_log_set_storage_policy_;
  HAEditLogContext::method_log_set_replica_policy_ =
      this->method_log_set_replica_policy_;
  HAEditLogContext::method_log_set_dir_replica_policy_ =
      this->method_log_set_dir_replica_policy_;
  HAEditLogContext::method_log_set_quota_ = this->method_log_set_quota_;
  HAEditLogContext::method_log_set_permission_ =
      this->method_log_set_permission_;
  HAEditLogContext::method_log_set_owner_ = this->method_log_set_owner_;
  HAEditLogContext::method_log_set_acl_ = this->method_log_set_acl_;
  HAEditLogContext::method_log_set_xttrs_ = this->method_log_set_xttrs_;
  HAEditLogContext::method_log_remove_xattrs_ = this->method_log_remove_xattrs_;
  // Snapshot related.
  HAEditLogContext::method_log_access_counters_snapshot_ =
      this->method_log_access_counters_snapshot_;
}

}  // namespace dancenn