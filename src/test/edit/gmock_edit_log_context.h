// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/03/30
// Description

#ifndef TEST_EDIT_GMOCK_EDIT_LOG_CONTEXT_H_
#define TEST_EDIT_GMOCK_EDIT_LOG_CONTEXT_H_

#include <gmock/gmock.h>  // For MOCK_METHOD1, etc.
#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <cstdint>  // For int64_t, etc.
#include <memory>   // For shared_ptr, unique_ptr.
#include <sstream>  // For stringstream.
#include <string>   // For string.

#include "edit/deserializer.h"      // For OpDeSerializer.
#include "edit/edit_log_cfs_op.h"   // For CfsOpCode, AbstractEditLogCfsOp.
#include "edit/edit_log_context.h"  // For EditLogContextBase.
#include "edit/edit_log_op.h"       // For OpCode.
#include "edit/edit_log_sync_listener.h"  // For IEditLogSyncListener.

namespace dancenn {

class GMockEditLogContext : public EditLogContextBase {
 public:
  MOCK_METHOD1(SetupSyncListener,
               void(std::shared_ptr<IEditLogSyncListener> listener));
  MOCK_METHOD0(TestOnlyGetSyncListener,
               std::shared_ptr<IEditLogSyncListener>());

  MOCK_METHOD0(OpenForRead, void());
  MOCK_METHOD0(OpenForWrite, int64_t());

  MOCK_CONST_METHOD0(IsActiveInLease, bool());

  MOCK_METHOD0(IsOpenForWrite, bool());
  MOCK_METHOD0(IsOpenForRead, bool());

  MOCK_METHOD0(GetHAMode, EditLogConf::HAMode());
  MOCK_METHOD2(UpdateConfProperty,
               bool(const std::string& name, const std::string& value));

  MOCK_METHOD0(GetWaitSyncTime, int64_t());

  MOCK_METHOD1(LogSync, void(bool force));
  MOCK_METHOD0(LogSyncAll, void());
  MOCK_METHOD0(Close, void());
  MOCK_METHOD0(InitJournalsForWrite, void());
  MOCK_METHOD1(SetNextTxId, void(int64_t));
  MOCK_METHOD1(SetLastAllocatedBlockId, void(int64_t id));
  MOCK_METHOD1(SetLastGenerationStampV2, void(int64_t gsv2));

  MOCK_METHOD0(GetLastAllocatedBlockId, uint64_t());
  MOCK_METHOD0(GetLastGenerationStampV2, uint64_t());
  MOCK_METHOD0(GetCurSegmentTxId, int64_t());
  MOCK_METHOD0(GetLastWrittenTxId, int64_t());
  MOCK_METHOD0(RollEditLog, int64_t());
  MOCK_METHOD1(PurgeLogsOlderThan, void(int64_t min_tx_id_to_keep));
  MOCK_METHOD1(GetAllStackTraces, bool(std::string* stack_info));

  MOCK_METHOD1(GetPeerNNAddr, bool(std::string* addr));

  MOCK_METHOD3(
      CreateInputContext,
      std::unique_ptr<EditLogInputContextBase>(int64_t from_txid,
                                               int64_t to_at_least_txid,
                                               bool is_progress_ok));

  MOCK_METHOD2(LogOpenFile,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogCloseFile, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogAddBlock, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogUpdateBlocks,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogMkDir, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogRenameOld,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD2(LogRename,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogSetReplication, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetStoragePolicy, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetReplicaPolicy, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetDirReplicaPolicy, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetQuota, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetPermissions, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetOwner, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogConcat,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD2(LogDelete,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogGenerationStampV1, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogGenerationStampV2,
               int64_t(const std::stringstream* ss, uint64_t* gsv2));
  MOCK_METHOD2(LogAllocateBlockId,
               int64_t(const std::stringstream* ss, uint64_t* id));
  MOCK_METHOD4(LogAllocateBlockIdAndGSv2,
               int64_t(const std::stringstream* blkid_ss,
                       const std::stringstream* gsv2_ss,
                       uint64_t* blkid,
                       uint64_t* gsv2));
  MOCK_METHOD1(LogTimes, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogSymlink,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogReassignLease, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetAcl, int64_t(const std::stringstream* ss));
  MOCK_METHOD2(LogSetXAttrs,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD2(LogRemoveXAttrs,
               int64_t(const std::stringstream* ss, bool to_log_rpc_ids));
  MOCK_METHOD1(LogAccessCounterSnapshot, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogSetBlockPufsInfo, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogDeleteDeprecatedBlockPufsInfo,
               int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogCfsOp, int64_t(const std::stringstream* ss));

  MOCK_METHOD1(LogAllowSnapshot, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogDisallowSnapshot, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogCreateSnapshot, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogDeleteSnapshot, int64_t(const std::stringstream* ss));
  MOCK_METHOD1(LogRenameSnapshot, int64_t(const std::stringstream* ss));

  MOCK_METHOD0(HASwitchFence, EditLogConf::PreviousEditLogConf());
  MOCK_METHOD0(SwitchNonHAActiveToHAActive, Status());
  MOCK_METHOD0(SwitchHAActiveToNonHAActive, Status());
  MOCK_METHOD0(SwitchHAStandbyToNonHAActive, Status());
};

}  // namespace dancenn

namespace testing {

// Refer to DEF_CFS_OP in edit/edit_log_cfs_op.h.
#define DEF_CFS_OP_MATCHER(OP_NAME)                                        \
  MATCHER_P(MatchOp##OP_NAME, matcher, "Match Cfs Op") {                   \
    dancenn::OpDeSerializer op_deserializer;                               \
    std::shared_ptr<dancenn::EditLogOp> op =                               \
        op_deserializer.Deserialize(arg->str());                           \
    if (op->op_code() != dancenn::OpCode::OP_CFS) {                        \
      return false;                                                        \
    }                                                                      \
    if (std::static_pointer_cast<dancenn::AbstractEditLogCfsOp>(op)        \
            ->GetCfsOpCode() != (dancenn::Op##OP_NAME()).GetCfsOpCode()) { \
      return false;                                                        \
    }                                                                      \
    testing::Matcher<dancenn::Op##OP_NAME> m = matcher;                    \
    return m.Matches(*std::static_pointer_cast<dancenn::Op##OP_NAME>(op)); \
  }

// File related.
DEF_CFS_OP_MATCHER(OpenFile);
DEF_CFS_OP_MATCHER(AddBlockV2);
DEF_CFS_OP_MATCHER(AbandonBlock);
DEF_CFS_OP_MATCHER(UpdatePipeline);
DEF_CFS_OP_MATCHER(Fsync);
DEF_CFS_OP_MATCHER(UpdateBlocksV2);
DEF_CFS_OP_MATCHER(CloseFile);
DEF_CFS_OP_MATCHER(ReassignLeaseV2);

// Block related.
DEF_CFS_OP_MATCHER(ApproveUploadBlk);
DEF_CFS_OP_MATCHER(PersistBlk);
DEF_CFS_OP_MATCHER(DelDepringBlks);
DEF_CFS_OP_MATCHER(DelDepredBlks);
DEF_CFS_OP_MATCHER(FlushBlockInfoProtos);

// Dir tree related.
DEF_CFS_OP_MATCHER(MkdirV2);
DEF_CFS_OP_MATCHER(DeleteV2);
DEF_CFS_OP_MATCHER(RenameOldV2);
DEF_CFS_OP_MATCHER(RenameV2);

// Set* related.
DEF_CFS_OP_MATCHER(SetReplicationV2);
DEF_CFS_OP_MATCHER(SetStoragePolicyV2);
DEF_CFS_OP_MATCHER(SetReplicaPolicyV2);
DEF_CFS_OP_MATCHER(SetDirReplicaPolicyV2);
DEF_CFS_OP_MATCHER(SetPermissionsV2);
DEF_CFS_OP_MATCHER(SetOwnerV2);
DEF_CFS_OP_MATCHER(SetAclV2);
DEF_CFS_OP_MATCHER(SetXAttrsV2);
DEF_CFS_OP_MATCHER(RemoveXAttrsV2);
DEF_CFS_OP_MATCHER(SetLifecyclePolicy);
DEF_CFS_OP_MATCHER(UnsetLifecyclePolicy);
DEF_CFS_OP_MATCHER(UpdateATimeProtos);

#undef DEF_CFS_OP_MATCHER_MATCHER

}  // namespace testing

#endif  // TEST_EDIT_GMOCK_EDIT_LOG_CONTEXT_H_
