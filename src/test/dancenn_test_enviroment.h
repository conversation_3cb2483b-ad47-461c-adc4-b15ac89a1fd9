// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef TEST_DANCENN_TEST_ENVIROMENT_H_
#define TEST_DANCENN_TEST_ENVIROMENT_H_

#include <gtest/gtest.h>
#include <cnetpp/base/log.h>

#include <string>
#include <memory>

#include "base/java.h"
#include "edit/edit_log_context.h"

namespace dancenn {

extern int g_argc;
extern char** g_argv;

// ref to dfs.namenode.edits.dir(cmake/installations/test/conf/hdfs-site.xml)
extern const char* g_edits_dir;
extern std::shared_ptr<JavaRuntime> g_java_runtime;
extern std::shared_ptr<HAEditLogContext> g_edit_log_ctx;

std::string GetProgramDirectory();


static void CnetppLog(cnetpp::base::Log::Level level, const char* msg) {
  switch (level) {
    case cnetpp::base::Log::Level::kDebug:
      DLOG(INFO) << msg;
      break;
    case cnetpp::base::Log::Level::kInfo:
      LOG(INFO) << msg;
      break;
    case cnetpp::base::Log::Level::kWarn:
      LOG(WARNING) << msg;
      break;
    case cnetpp::base::Log::Level::kError:
      LOG(ERROR) << msg;
      break;
    case cnetpp::base::Log::Level::kFatal:
      LOG(FATAL) << msg;
      break;
    default:
      LOG(FATAL) << "Unknown cnetpp log level: " << static_cast<int>(level)
                 << ", message is: " << msg << ". aborting...";
      abort();
  }
}

static void SetupCnetppLogger() {
  cnetpp::base::LOG.set_func(&CnetppLog);
  // The following line to remove the cnepp log
  // cnetpp::base::LOG.set_func(&cnetpp::base::LOG.DefaultEmptyFunc);
}

class DancennTestEnviroment : public ::testing::Environment {
 public:
  DancennTestEnviroment(int argc, char* argv[]) {
    g_argc = argc;
    g_argv = argv;

    SetupCnetppLogger();
  }
};

}  // namespace dancenn

#endif  // TEST_DANCENN_TEST_ENVIROMENT_H_

