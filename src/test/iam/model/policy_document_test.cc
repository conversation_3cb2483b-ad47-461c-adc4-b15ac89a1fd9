// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/01/06
// Description

#include <gtest/gtest.h>

#include "iam/model/policy_document.h"

namespace dancenn {
namespace iam {

TEST(PolicyDocumentTest, Test01) {
  PolicyDocument policy;
  policy.effect_ = PolicyDocument::Effect::kAllow;
  policy.actions_.push_back(PolicyDocument::Action::kPutObject);
  policy.actions_.push_back(PolicyDocument::Action::kGetObject);
  policy.resources_.push_back("trn:tos:::pool-bucket-cluster-boe-pre-7/a/b");
  EXPECT_EQ(
      policy.ToJsonCompactString(),
      R"({"Statement":[{"Action":["tos:PutObject","tos:GetObject"],"Effect":"Allow","Resource":["trn:tos:::pool-bucket-cluster-boe-pre-7/a/b"]}]})");
  EXPECT_EQ(
      policy.ToUrlEscapedJsonCompactString(),
      R"(%7B%22Statement%22%3A%5B%7B%22Action%22%3A%5B%22tos%3APutObject%22%2C%22tos%3AGetObject%22%5D%2C%22Effect%22%3A%22Allow%22%2C%22Resource%22%3A%5B%22trn%3Atos%3A%3A%3Apool-bucket-cluster-boe-pre-7%2Fa%2Fb%22%5D%7D%5D%7D)");
}

}  // namespace iam
}  // namespace dancenn
