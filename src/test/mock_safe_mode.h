// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef TEST_MOCK_SAFE_MODE_H_
#define TEST_MOCK_SAFE_MODE_H_

#include <glog/logging.h>

#include <string>
#include <vector>
#include <atomic>

#include "safemode/safemode_base.h"

namespace dancenn {

class MockSafeMode : public SafeModeBase {
 public:
  MockSafeMode() {
  }
  ~MockSafeMode() override {}

  void Start() override {}
  void Stop() override {}

  bool IsOn() override {
    return state_ != 0;
  }

  bool IsStartingUp() override {
    return state_ == 1;
  }
  bool IsManual() override {
    return state_ == 2;
  }

  void GetSafeModeTip(std::string *tip) override {
    tip->append(tip_);
  }

  Status SetSafeMode(::cloudfs::SafeModeActionProto action,
                     OperationsCategory op) override {
    if (action == ::cloudfs::SafeModeActionProto::SAFEMODE_ENTER) {
      state_ = 2;
    } else if (action == ::cloudfs::SafeModeActionProto::SAFEMODE_LEAVE) {
      state_ = 0;
    }
    return s_;
  }

  void SetBlockTotal(int64_t total) override {
  }

  void AdjustSafeModeBlockTotals(int delta_safe, int delta_total) override {
  }
  void IncrementSafeBlockCount(uint32_t replication) override {
  }
  void DecrementSafeBlockCount(uint32_t replication) override {
  }
  // 0: off
  // 1: starting up
  // 2: manual
  int state_ { 0 };
  std::string tip_;
  // the return result of SetSafeMode method
  Status s_;
};

}  // namespace dancenn

#endif  // TEST_MOCK_HA_STATE_H_

