// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef TEST_MOCK_HA_STATE_H_
#define TEST_MOCK_HA_STATE_H_

#include <glog/logging.h>

#include <string>
#include <vector>
#include <atomic>

#include "base/vlock.h"
#include "ha/ha_state_base.h"

namespace dancenn {

class MockHAState : public HAStateBase {
 public:
  MockHAState()
      : in_initializing_state_(true),
        state_(cloudfs::HAServiceStateProto::ACTIVE),
        barrier_(1) {
  }
  ~MockHAState() {}

  bool IsInitializing() override {
    return in_initializing_state_;
  }
  void CompleteInitialization() override {
    in_initializing_state_ = false;
  }

  void SetState(cloudfs::HAServiceStateProto next_state) override {
    state_ = next_state;
  }
  void SetState(cloudfs::HAServiceStateProto next_state,
                Closure* done) override {
    state_ = next_state;
    if (done) {
      done->Run();
    }
  }
  cloudfs::HAServiceStateProto GetState() override {
    return state_;
  }

  std::pair<Status, dancenn::vshared_lock> CheckOperation(
      OperationsCategory op) override {
    if (is_only_read_) {
      if (op == OperationsCategory::kRead) {
        return std::make_pair(Status(), dancenn::vshared_lock(barrier_.lock()));
      } else {
        return std::make_pair(Status(JavaExceptions::Exception::kStandbyException,
          "Operation is not supported in this state"), dancenn::vshared_lock(barrier_.lock()));
      }
    } else {
      return std::make_pair(s_, dancenn::vshared_lock(barrier_.lock()));
    }
  }

  Status UnprotectedCheckOperation(OperationsCategory op) override {
    if (is_only_read_) {
      if (op == OperationsCategory::kRead) {
        return Status();
      } else {
        return Status(JavaExceptions::Exception::kStandbyException,
          "Operation is not supported in this state");
      }
    } else {
      return s_;
    }
  }

  TwoStepVUniqueLock LockBarrierForHASwitcher() override {
    return TwoStepVUniqueLock(barrier_.lock());
  }

  bool InTransition() override {
    return in_transition_;
  }

  bool ShouldPopulateReplicationQueues() override {
    return should_populate_replication_queues;
  }

  bool IsActive() const override {
    return state_ == cloudfs::HAServiceStateProto::ACTIVE;
  }

  bool IsActiveInLease() const override {
    return true;
  }

  void EnterDestroyState() override {
    destroy_state_.store(true);
  }
  void ExitDestroyState() override {
    destroy_state_.store(false);
  }
  bool IsDestroyState() override {
    return destroy_state_.load();
  }

  EditLogConf::HAMode GetHAMode() override {
    return EditLogConf::HA;
  }
  bool IsTailerStopped() override {
    return false;
  }
  Status SwitchNonHAActiveToHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAActiveToNonHAActive() override {
    return Status(Code::kError);
  }
  Status SwitchHAStandbyToNonHAActive() override {
    return Status(Code::kError);
  }

  void SetReadOnly(bool enable_readonly) {
    is_only_read_ = enable_readonly;
  }

  bool in_transition_{false};
  bool in_initializing_state_ { true };
  cloudfs::HAServiceStateProto state_{
      cloudfs::HAServiceStateProto::ACTIVE
  };

  Status  s_;
  VRWLock barrier_;
  bool should_populate_replication_queues = false;

  std::atomic<bool> destroy_state_{false};

  bool is_only_read_ = false;
};

}  // namespace dancenn

#endif  // TEST_MOCK_HA_STATE_H_

