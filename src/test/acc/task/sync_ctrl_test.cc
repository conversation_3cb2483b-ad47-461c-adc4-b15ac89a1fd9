#include "acc/task/sync_task.h"
#include "acc/sync_engine.h"
#include "test/ufs/mock_ufs.h"

#include <gmock/gmock.h>
#include <gtest/gtest.h>

namespace dancenn {
class ListingSyncCtrlTest : public testing::Test {

};

TEST_F(ListingSyncCtrlTest, SetSyncProgress) {
  std::shared_ptr<Ufs> ufs = MockUfs::CreateMockUfs("/foo/bar/");
  SyncEngine engine(ufs);
  ListingSyncCtrl ctrl(&engine, "", "");

  auto setProgress = [&ctrl]() {
    for (int i = 0; i < 100; ++i) {
      auto progress = std::make_shared<uint64_t>(i);
      ctrl.SetSyncProgress(progress);
    }
  };

  std::vector<std::thread> threads;
  for (int i = 0; i < 1000; ++i) {
    threads.emplace_back(setProgress);
  }
  for (auto& t : threads) {
    t.join();
  }
  std::cout << "ListingSyncCtrlTest done" << std::endl;
}
}