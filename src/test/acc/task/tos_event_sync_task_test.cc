//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "acc/task/tos_event_sync_task.h"

// Third
#include <gmock/gmock.h>
#include <gtest/gtest.h>

// Project
#include "test/namespace/mock_namespace.h"
#include "test/ufs/tos_ufs/mock_tos_event.h"
#include "test/ufs/tos_ufs/mock_tos_ufs.h"

DECLARE_bool(ufs_event_manager_enabled);
DECLARE_bool(tos_event_key_prefix_blacklist_enabled);
DECLARE_string(tos_event_key_prefix_blacklist);
DECLARE_bool(ufs_event_kafka_consumer_enabled);
DECLARE_bool(ufs_event_rmq_consumer_enabled);

namespace dancenn {

class TosEventSyncTaskTest : public testing::Test {
 public:
  void SetUp() override {
    tos_config_ = TosConfig(kUTTosEventEndpoint,
                            kUTTosEventRegion,
                            kUTTosEventBucket,
                            kUTTosEventPrefix);
    ufs_config_ = TosConfig::CreateUfsConfig(tos_config_);
    auto cred_keeper = std::make_shared<TosCredKeeper>();
    CHECK(cred_keeper->Start().IsOK());
    auto cred_provider = std::shared_ptr<TosCredentialProvider>(
        new StaticTosCredentialProvider(cred_keeper));
    tos_ufs_ =
        std::make_shared<GMockTosUfs>(ufs_config_, tos_config_, cred_provider);

    ns_ = std::make_shared<GMockNameSpace>();

    FLAGS_ufs_event_manager_enabled = true;
    FLAGS_ufs_event_kafka_consumer_enabled = false;
    FLAGS_ufs_event_rmq_consumer_enabled = false;
    FLAGS_tos_event_key_prefix_blacklist_enabled = true;
    FLAGS_tos_event_key_prefix_blacklist =
        "my-prefix/blacklist-1,my-prefix/blacklist-2";
    mgr_ = std::make_shared<UfsEventManager>(tos_ufs_);

    dummy_cb_ = [](const Status& status) {};
  }

  void TearDown() override {
    mgr_.reset();
    ns_.reset();
    tos_ufs_.reset();
  }

  Status Sync(const std::shared_ptr<TosEventSyncTask>& task) {
    return task->Sync();
  }

  Status CheckTosEvent(const std::shared_ptr<TosEventSyncTask>& task,
                       const std::shared_ptr<TosEvent>& event,
                       const UserGroupInfo& ugi,
                       std::string* ufs_path,
                       std::string* inner_path) {
    return task->CheckTosEvent(event, ugi, ufs_path, inner_path);
  }

  TosConfig tos_config_;
  UfsConfig ufs_config_;
  std::shared_ptr<GMockTosUfs> tos_ufs_;
  std::shared_ptr<GMockNameSpace> ns_;
  std::shared_ptr<UfsEventManager> mgr_;
  SyncCallback dummy_cb_;
};

TEST_F(TosEventSyncTaskTest, SyncParseFailed) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "" /* invalid msg */);

  Status s = Sync(task);
  EXPECT_EQ(Code::kError, s.code());
}

TEST_F(TosEventSyncTaskTest, SyncInvalidTosEvent) {
  // msg with invalid region
  std::shared_ptr<TosEvent> event = TosEventForUT();
  event->tos()->set_region("invalid-region");
  std::string msg = "{\"events\":[" + event->ToJson() + "]}";

  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), msg);

  Status s = Sync(task);
  // Sync returns OK for invalid event
  EXPECT_TRUE(s.IsOK());
}

TEST_F(TosEventSyncTaskTest, SyncNsSyncFail) {
  std::shared_ptr<TosEvent> event = TosEventForUT();
  std::string msg = "{\"events\":[" + event->ToJson() + "]}";

  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), msg);

  EXPECT_CALL(*ns_,
              SyncUfsFileOnly("/my-prefix/my-key",
                              "/my-key",
                              testing::_,
                              testing::_,
                              std::dynamic_pointer_cast<Ufs>(tos_ufs_),
                              testing::_,
                              testing::_,
                              testing::_))
      .Times(5)
      .WillRepeatedly(testing::Return(Status(Code::kError, "mocked error")));

  Status s = Sync(task);
  EXPECT_EQ(Code::kError, s.code());
}

TEST_F(TosEventSyncTaskTest, SyncNsSyncSucceed) {
  std::shared_ptr<TosEvent> event = TosEventForUT();
  std::string msg = "{\"events\":[" + event->ToJson() + "]}";

  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), msg);

  EXPECT_CALL(*ns_,
              SyncUfsFileOnly("/my-prefix/my-key",
                              "/my-key",
                              testing::_,
                              testing::_,
                              std::dynamic_pointer_cast<Ufs>(tos_ufs_),
                              testing::_,
                              testing::_,
                              testing::_))
      .Times(1)
      .WillOnce(testing::Return(Status::OK()));

  Status s = Sync(task);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(TosEventSyncTaskTest, CheckTosEventHitBlacklist) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  {
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key("my-prefix/blacklist-1/my-key");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kBadParameter, s.code());
  }

  {
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key("my-prefix/blacklist-2/my-key");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kBadParameter, s.code());
  }
}

TEST_F(TosEventSyncTaskTest, CheckTosEventInvalidRegion) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  std::shared_ptr<TosEvent> event = TosEventForUT();
  event->tos()->set_region("invalid-region");

  UserGroupInfo ugi;
  std::string ufs_path;
  std::string inner_path;
  Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
  EXPECT_EQ(Code::kError, s.code());
}

TEST_F(TosEventSyncTaskTest, CheckTosEventInvalidBucket) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  std::shared_ptr<TosEvent> event = TosEventForUT();
  event->tos()->bucket()->set_name("invalid-bucket");

  UserGroupInfo ugi;
  std::string ufs_path;
  std::string inner_path;
  Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
  EXPECT_EQ(Code::kError, s.code());
}

TEST_F(TosEventSyncTaskTest, CheckTosEventInvalidEventType) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  std::shared_ptr<TosEvent> event = TosEventForUT();
  event->tos()->bucket()->set_name("invalid-event-name");

  UserGroupInfo ugi;
  std::string ufs_path;
  std::string inner_path;
  Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
  EXPECT_EQ(Code::kError, s.code());
}

TEST_F(TosEventSyncTaskTest, CheckTosEventInvalidObjectKey) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  {
    // Empty key is invalid
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key("");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kError, s.code());
  }

  {
    // Key with trailing slash is invalid
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key(tos_config_.prefix + "my-key/");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kError, s.code());
  }

  {
    // Key with consecutive slashs is invalid
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key(tos_config_.prefix + "my//key");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kError, s.code());
  }

  {
    // Key with segment "." is invalid
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key(tos_config_.prefix + "my/./key");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kError, s.code());
  }

  {
    // Key with segment ".." is invalid
    std::shared_ptr<TosEvent> event = TosEventForUT();
    event->tos()->object()->set_key(tos_config_.prefix + "my/../key");

    UserGroupInfo ugi;
    std::string ufs_path;
    std::string inner_path;
    Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
    EXPECT_EQ(Code::kError, s.code());
  }
}

TEST_F(TosEventSyncTaskTest, CheckTosEventPrefixMismatch) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  std::shared_ptr<TosEvent> event = TosEventForUT();
  event->tos()->object()->set_key("my-key");

  UserGroupInfo ugi;
  std::string ufs_path;
  std::string inner_path;
  Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
  EXPECT_EQ(Code::kBadParameter, s.code());
}

TEST_F(TosEventSyncTaskTest, CheckTosEventOK) {
  std::shared_ptr<TosEventSyncTask> task = std::make_shared<TosEventSyncTask>(
      &ufs_config_, tos_ufs_, ns_, dummy_cb_, mgr_.get(), "");

  std::shared_ptr<TosEvent> event = TosEventForUT();

  UserGroupInfo ugi;
  std::string ufs_path;
  std::string inner_path;
  Status s = CheckTosEvent(task, event, ugi, &ufs_path, &inner_path);
  EXPECT_TRUE(s.IsOK());
}

}  // namespace dancenn
