//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "acc/acc_namespace.h"

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <hdfs.pb.h>

#include <memory>

#include "absl/strings/str_format.h"
#include "acc/acc.h"
#include "base/defer.h"
#include "base/file_utils.h"
#include "base/logger_metrics.h"
#include "base/path_util.h"
#include "http/dancenn_admin_handler.h"
#include "http/dancenn_fsck_handler.h"
#include "http/dancenn_status_handler.h"
#include "namespace/create_flag.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "server/dancenn.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"
#include "test/ufs/mock_ufs.h"

DECLARE_bool(run_ut);
DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_int32(bg_deletion_condition_check_interval);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_string(all_datacenters);
DECLARE_int32(bg_deletion_num_in_batch);
DECLARE_uint32(dfs_summary_min_depth);
DECLARE_bool(force_hyperblock_on_diffrent_dn);
DECLARE_bool(namespace_read_full_detail_blocks);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_listener_enable);
DECLARE_bool(recycle_bin_default_policy_enable);
DECLARE_uint32(recycle_bin_default_policy_time_sec);
DECLARE_int32(dfs_meta_scan_interval_sec);
DECLARE_bool(client_replication_support);
DECLARE_bool(permission_enabled);
DECLARE_string(permission_model);
DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_int32(namespace_type);
DECLARE_bool(append_reuse_last_block);
DECLARE_uint32(acc_mpu_max_part);
DECLARE_uint64(acc_mpu_part_threshold);
DECLARE_uint64(block_report_scan_interval_sec);
DECLARE_bool(ufs_support_append);
DECLARE_int64(write_back_task_v2_trigger_interval_us);
DECLARE_bool(lifecycle_enable);
DECLARE_bool(nn_drive_upload);
DECLARE_bool(ufs_sync_listing_prefetch_enabled);
DECLARE_uint32(ufs_sync_listing_page_size);
DECLARE_uint32(ufs_sync_listing_prefetch_max_count);
DECLARE_int32(ufs_sync_min_interval);
DECLARE_bool(placement_ignore_existed_switch);
DECLARE_bool(complete_rpc_abandon_last_empty_block);
DECLARE_bool(enable_ufs_evict_write_cache);
DECLARE_bool(enable_ufs_evict_write_cache_delete_file);
DECLARE_bool(ufs_sync_mkdir_create_in_ufs);
DECLARE_bool(ufs_sync_force_when_parent_not_found);
DECLARE_bool(enable_acc_sync_op_lazycheck);
DECLARE_bool(dfs_ha_allow_stale_reads);
DECLARE_bool(write_back_by_default);
DECLARE_bool(enable_write_back);

using namespace cloudfs;
using namespace cloudfs::datanode;

namespace dancenn {

LogRpcInfo dummy_rpc_info("", 0);

namespace {
auto default_namespace_type = FLAGS_namespace_type;
auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
auto default_blockmap_num_bucket_each_slice =
    FLAGS_blockmap_num_bucket_each_slice;
auto default_blockmap_num_slice = FLAGS_blockmap_num_slice;
auto default_dfs_replication_min = FLAGS_dfs_replication_min;
auto default_dfs_meta_storage_inode_key_v2 =
    FLAGS_dfs_meta_storage_inode_key_v2;
auto default_write_back_manager_scan_task_interval_ms =
    FLAGS_write_back_manager_scan_task_interval_ms;
auto default_append_reuse_last_block = FLAGS_append_reuse_last_block;
auto default_block_report_scan_interval_sec =
    FLAGS_block_report_scan_interval_sec;
auto default_write_back_task_v2_trigger_interval_us =
    FLAGS_write_back_task_v2_trigger_interval_us;
auto default_lifecycle_enable = FLAGS_lifecycle_enable;
auto default_ufs_sync_min_interval = FLAGS_ufs_sync_min_interval;
}  // namespace

std::string MakeObjKey(const std::string& prefix, int seq) {
  return absl::StrFormat("%s-%07d", prefix.c_str(), seq);
}

class TestAccNamespace {
 public:
  void DoSetUp(std::shared_ptr<Ufs> ufs) {
    need_reset_flags_ = false;
    ufs_ = std::move(ufs);
    tos_prefix_ = ufs_->ufs_config_ptr()->ufs_prefix.substr(1);

    ugi_ = UserGroupInfo("root", "supergroup");

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    LOG(INFO) << "db_path_=" << db_path_;
    MockFSImageTransfer(db_path_).Transfer();

    runtime_.datanode_manager = std::make_shared<DatanodeManager>();
    runtime_.block_manager = std::make_shared<BlockManager>();
    runtime_.data_centers = std::make_shared<DataCenters>();
    runtime_.mock_ha_state = std::make_shared<MockHAState>();
    runtime_.mock_safemode = std::make_shared<MockSafeMode>();
    runtime_.edit_log_ctx = CreateContext();
    runtime_.ufs_env = UfsEnv::Create();
    runtime_.ufs_env->AddUfs(ufs_);
    runtime_.acc_namespace =
        std::make_shared<AccNamespace>(runtime_.ufs_env, ufs_);

    runtime_.ns =
        std::shared_ptr<NameSpace>(new MockNameSpace(db_path_,
                                                     runtime_.edit_log_ctx,
                                                     runtime_.block_manager,
                                                     runtime_.datanode_manager,
                                                     runtime_.data_centers,
                                                     runtime_.ufs_env));

    // inject
    runtime_.InjectDependency4Test();
    ns_ = CHECK_NOTNULL(runtime_.ns.get());
    block_manager_ = CHECK_NOTNULL(runtime_.block_manager.get());
    datanode_manager_ = CHECK_NOTNULL(runtime_.datanode_manager.get());
    acc_ns_ = CHECK_NOTNULL(runtime_.acc_namespace.get());

    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = std::shared_ptr<EditLogSenderBase>(
        new MockEditLogSender(runtime_.edit_log_ctx, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));

    // start
    ns_->Start();
    ns_->StartActive();

    // Init after ns_->Start();
    block_report_manager_ = ns_->GetBlockReportManager();

    // add a datanode to the cluster
    auto reg = MakeRegistrationPB("datanode1", "***********");
    reg.mutable_datanodeid()->add_optipaddrs("***********");
    reg.mutable_datanodeid()->add_optiprdmatags("default-tag");
    heartbeat_thread_ = AddDatanode(reg);

    stop_ = false;
    pause_ = false;

    ns_->StopBGDeletionWorker();
    ns_->StopLeaseMonitor();

    runtime_.ufs_env->Start();
    acc_ns_->Start();
  }

  void DoTearDown() {
    stop_ = true;
    heartbeat_thread_.join();
    for (auto& t : threads_) {
      t.join();
    }

    ns_->StopActive();

    runtime_.Stop();

    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  void CreateObject(const std::string& key, uint64_t size) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs_.get());
    mock_ufs->CreateObject(tos_prefix_ + key, size);
  }

  void DeleteObject(const std::string& key) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs_.get());
    mock_ufs->DeleteObject(tos_prefix_ + key);
  }

  std::string JoinUfsPathWithPrefix(const std::string& path) {
    auto&& prefix = ufs_->ufs_config_ptr()->ufs_prefix;
    if (path.empty()) {
      return prefix;
    }
    if (path[0] == kSeparatorChar) {
      return prefix + path.substr(1);
    }
    return prefix + path;
  }

  std::string JoinObjectKeyWithPrefix(const std::string& path) {
    if (path.empty()) {
      return tos_prefix_;
    }
    if (path[0] == kSeparatorChar) {
      return tos_prefix_ + path.substr(1);
    }
    return tos_prefix_ + path;
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  std::thread StartHeartbeat(const DatanodeRegistrationProto& reg) {
    CountDownLatch latch(1);
    auto heartbeat_thread = std::thread([&latch, this, reg = reg]() {
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);
      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          DatanodeManager::RepeatedCmds cmds;
          datanode_manager_->Heartbeat(request, &cmds);
          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();

    return heartbeat_thread;
  }

  void DoEmptyFBR(const DatanodeRegistrationProto& reg) {
    BlockReportRequestProto req;
    SynchronizedRpcClosure done;
    req.set_blockpoolid(ns_->blockpool_id());
    req.mutable_context()->set_currpc(1);
    req.mutable_context()->set_totalrpcs(1);
    req.mutable_context()->set_id(0);
    req.add_reports();
    block_manager_->AsyncBlockReport(
        reg.datanodeid().datanodeuuid(), &req, &done);
    done.Await();
  }

  static cloudfs::datanode::DatanodeRegistrationProto MakeRegistrationPB(
      const std::string& uuid,
      const std::string& ip) {
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_ipaddr(ip);
    reg.mutable_datanodeid()->set_hostname(ip);
    reg.mutable_datanodeid()->set_datanodeuuid(uuid);
    reg.mutable_datanodeid()->set_xferport(1234);
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);

    return reg;
  }

  std::thread AddDatanode(cloudfs::datanode::DatanodeRegistrationProto reg) {
    // add a datanode to the cluster
    cnetpp::base::IPAddress ip(reg.datanodeid().ipaddr());
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    stop_ = false;
    pause_ = false;
    auto thread = StartHeartbeat(reg);
    DoEmptyFBR(reg);

    return thread;
  }

  static CreateRequestProto GetCreateRequest() {
    CreateRequestProto request;
    request.set_src("");
    request.mutable_masked()->set_perm(0);
    request.set_clientname("client");
    request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    request.set_createparent(false);
    request.set_replication(1);
    request.set_blocksize(128 * 1024 * 1024);
    request.set_createparent(false);
    return request;
  }

  static AppendRequestProto GetAppendRequest() {
    AppendRequestProto request;
    request.set_src("");
    request.set_clientname("client");
    return request;
  }

  static AddBlockRequestProto GetAddBlockRequest() {
    AddBlockRequestProto request;
    request.set_src("");
    request.set_clientname("client");
    return request;
  }

  static CompleteRequestProto GetCompleteRequest() {
    CompleteRequestProto request;
    request.set_src("");
    request.set_clientname("client");
    return request;
  }

  static void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      const std::string& etag,
      const std::string& uploadid,
      const std::string& pufs_name,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    MakeReport(block_id,
               gs,
               len,
               etag,
               uploadid,
               pufs_name,
               cloudfs::StorageClassProto::WARM,
               false,
               state,
               report);
  }

  static void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      const std::string& etag,
      const std::string& uploadid,
      const std::string& pufs_name,
      cloudfs::StorageClassProto sc,
      bool pinned,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
    b->set_etag(etag);
    b->set_uploadid(uploadid);
    b->set_pufsname(pufs_name);
    b->set_storageclass(sc);
    b->set_pinned(pinned);
  }

  // -1 never trigger sync
  // 0 always sync
  // positive value : trigger sync if local inode expired
  static void CreateDefaultPermUgiAccInfo(PermissionStatus* p,
                                          UserGroupInfo* u,
                                          AccFsInfo* info,
                                          int32_t sync_interval = 0) {
    p->set_username("root");
    p->set_groupname("");
    p->set_permission(0755);

    *u = UserGroupInfo("root", "");

    info->set_syncinterval(sync_interval);
    {
      auto arg = info->add_args();
      arg->set_argname("TOS_BUCKET");
      arg->set_argvalue(kUTTosBucketDummy);
    }
  }

  void AddFile(const std::string& path,
               uint64_t len,
               uint32_t replica,
               bool need_complete,
               AddBlockResponseProto* add_response,
               CreateResponseProto* create_response,
               bool create_parent = false,
               uint32_t create_flag = ::cloudfs::CreateFlagProto::CREATE) {
    AddFileMultiBlock(path,
                      {len},
                      replica,
                      need_complete,
                      /*need_persist_block=*/false,
                      /*need_persist_file=*/false,
                      add_response,
                      create_response,
                      nullptr,
                      create_parent,
                      create_flag);
  }

  void AddFileMultiBlock(
      const std::string& path,
      std::vector<uint64_t> block_size,
      uint32_t replica,
      bool need_complete,
      bool need_persist_block,
      bool need_persist_file,
      AddBlockResponseProto* add_response,
      CreateResponseProto* create_response,
      std::vector<uint64_t>* output_block_ids = nullptr,
      bool create_parent = false,
      uint32_t create_flag = ::cloudfs::CreateFlagProto::CREATE,
      const std::string& rdma_tag = "") {
    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::vector<uint64_t> block_ids;
    auto create_request = GetCreateRequest();
    create_request.set_replication(replica);
    create_request.set_createparent(create_parent);
    create_request.set_createflag(create_flag);
    {
      SynchronizedRpcClosure rpc_done;
      acc_ns_->AsyncCreateFile(path,
                               p,
                               NetworkLocationInfo(),
                               ugi,
                               LogRpcInfo(),
                               "",
                               &create_request,
                               create_response,
                               &ctrl,
                               &rpc_done,
                               info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    INodeID inode_id = create_response->fs().fileid();

    cnetpp::base::IPAddress client_ip("***********");
    uint64_t block_len = 0;

    ExtendedBlockProto last;
    std::vector<std::string> dn_uuids;
    for (auto i = 0; i < block_size.size(); ++i) {
      dn_uuids.clear();

      block_len = block_size[i];
      auto add_request = GetAddBlockRequest();
      add_request.set_src(path);
      add_request.set_fileid(inode_id);
      add_request.set_rdmatag(rdma_tag);
      if (last.IsInitialized()) {
        add_request.mutable_previous()->CopyFrom(last);
      }
      NetworkLocationInfo client_location(client_ip);
      client_location.rdma_tag = rdma_tag;
      {
        add_response->clear_block();
        SynchronizedRpcClosure rpc_done;
        acc_ns_->AsyncAddBlock(path,
                               client_location,
                               LogRpcInfo(),
                               ugi,
                               &add_request,
                               add_response,
                               &ctrl,
                               &rpc_done,
                               info);
        rpc_done.Await();
        ASSERT_TRUE(!rpc_done.status().HasException());
        last = add_response->block().b();
        last.set_numbytes(block_len);
        ASSERT_GT(add_response->block().locs().size(), 0);
        for (auto lb : add_response->block().locs()) {
          dn_uuids.push_back(lb.id().datanodeuuid());
        }
        LOG(INFO) << "add_response=" << add_response->ShortDebugString();
      }

      if (output_block_ids != nullptr) {
        (*output_block_ids).push_back(add_response->block().b().blockid());
      }
      block_ids.push_back(add_response->block().b().blockid());

      for (const auto& dn_uuid : dn_uuids) {
        BlockManager::RepeatedIncBlockReport report;
        MakeReport(add_response->block().b().blockid(),
                   add_response->block().b().generationstamp(),
                   block_len,
                   "",
                   "",
                   "",
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
                   &report);
        block_manager_->IncrementalBlockReport(dn_uuid, report);
        ns_->GetBlockReportManager()->IncrementalBlockReport(
            1, dn_uuid, report);

        if (i != block_size.size() - 1) {
          report.Clear();
          MakeReport(add_response->block().b().blockid(),
                     add_response->block().b().generationstamp(),
                     block_len,
                     "",
                     "",
                     "",
                     cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                     &report);
          block_manager_->IncrementalBlockReport(dn_uuid, report);
          ns_->GetBlockReportManager()->IncrementalBlockReport(
              1, dn_uuid, report);
        }
      }
    }

    if (!need_complete) {
      return;
    }

    for (const auto& dn_uuid : dn_uuids) {
      BlockManager::RepeatedIncBlockReport report;
      MakeReport(add_response->block().b().blockid(),
                 add_response->block().b().generationstamp(),
                 block_len,
                 "",
                 "",
                 "",
                 cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                 &report);
      block_manager_->IncrementalBlockReport(dn_uuid, report);
      ns_->GetBlockReportManager()->IncrementalBlockReport(1, dn_uuid, report);
    }
    {
      CompleteRequestProto complete_request;
      complete_request.set_src(path);
      complete_request.set_clientname("client");
      complete_request.mutable_last()->CopyFrom(add_response->block().b());
      complete_request.mutable_last()->set_numbytes(block_len);

      CompleteResponseProto response;
      SynchronizedRpcClosure rpc_done;
      acc_ns_->AsyncCompleteFile(
          path, &complete_request, &response, &ctrl, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException());
    }

    if (!need_persist_block) {
      return;
    }

    for (auto block_id : block_ids) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
      ASSERT_NE(bip.state(), BlockInfoProto::kPersisted);

      bip.set_state(BlockInfoProto::kPersisted);
      ns_->meta_storage()->TestOnlyPutBlockInfo(bip);
      ns_->block_manager()->PersistBlock(
          Block(bip.block_id(), bip.num_bytes(), bip.gen_stamp()));
    }

    for (const auto& block_id : block_ids) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns_->meta_storage()->GetBlockInfo(block_id, &bip));
      ASSERT_EQ(bip.state(), BlockInfoProto::kPersisted);

      auto uc_state = ns_->block_manager()->GetBlockUCState(block_id);
      ASSERT_EQ(uc_state, BlockUCState::kPersisted);
    }

    if (!need_persist_file) {
      return;
    }

    INode inode;
    ASSERT_EQ(ns_->meta_storage()->GetINode(inode_id, &inode), StatusCode::kOK);
    inode.mutable_ufs_file_info()->set_file_state(
        UfsFileState::kUfsFileStatePersisted);
    ns_->meta_storage()->UpdateINode(inode);

    ASSERT_EQ(ns_->meta_storage()->GetINode(inode_id, &inode), StatusCode::kOK);
    ASSERT_EQ(inode.ufs_file_info().file_state(),
              UfsFileState::kUfsFileStatePersisted);
    LOG(INFO) << inode.ShortDebugString();
  }

 private:
  bool stop_;
  bool pause_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  UserGroupInfo ugi_;

 public:
  std::vector<std::thread> threads_;

  DanceNNRuntime runtime_;
  std::shared_ptr<Ufs> ufs_{nullptr};

  // no ownership
  NameSpace* ns_{nullptr};
  BlockManager* block_manager_{nullptr};
  DatanodeManager* datanode_manager_{nullptr};
  BlockReportManager* block_report_manager_{nullptr};
  AccNamespace* acc_ns_{nullptr};

  std::string tos_prefix_;
  bool need_reset_flags_ = false;
};

class AccNamespaceTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_lease_expired_soft_limit_ms = 200;
    FLAGS_lease_expired_hard_limit_ms = 400;
    FLAGS_datanode_keep_alive_timeout_sec = 1000;
    FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
    FLAGS_blockmap_num_bucket_each_slice = 1;
    FLAGS_blockmap_num_slice = 1;
    FLAGS_dfs_replication_min = 1;
    FLAGS_dfs_replication_max = 2;
    FLAGS_bytecool_feature_enabled = true;
    FLAGS_dfs_meta_scan_interval_sec = 5;
    FLAGS_recycle_bin_default_policy_enable = false;
    FLAGS_recycle_bin_default_policy_time_sec = 10;
    FLAGS_client_replication_support = true;
    FLAGS_dfs_meta_storage_inode_key_v2 = true;
    FLAGS_write_back_manager_scan_task_interval_ms = 100;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
    FLAGS_append_reuse_last_block = false;
    FLAGS_block_report_scan_interval_sec = 10;
    FLAGS_write_back_task_v2_trigger_interval_us = 1000000;
    FLAGS_lifecycle_enable = true;
    FLAGS_block_placement_policy = "cfs-default";
    FLAGS_ufs_sync_min_interval = 0;
    FLAGS_ufs_sync_mkdir_create_in_ufs = true;
    FLAGS_ufs_sync_force_when_parent_not_found = true;

    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to setup "
                   "TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      auto ns = std::make_unique<TestAccNamespace>();
      auto ufs = MockUfs::CreateMockUfs("/");

      ns->DoSetUp(ufs);
      acc_namespaces_[0] = std::move(ns);
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to setup "
                   "TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to setup "
                   "TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      auto ns = std::make_unique<TestAccNamespace>();

      auto ufs = MockUfs::CreateMockUfs("/foo/bar/");

      ns->DoSetUp(ufs);
      acc_namespaces_[1] = std::move(ns);
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to setup "
                   "TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
  }

  void TearDown() override {
    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to "
                   "teardown TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      acc_namespaces_[0]->DoTearDown();
      acc_namespaces_[0].reset();
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to "
                   "teardown TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to "
                   "teardown TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      acc_namespaces_[1]->DoTearDown();
      if (acc_namespaces_[1]->need_reset_flags_) {
        FLAGS_enable_write_back = true;
      }
      acc_namespaces_[1].reset();
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to "
                   "teardown TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }

    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_blockmap_num_bucket_each_slice =
        default_blockmap_num_bucket_each_slice;
    FLAGS_blockmap_num_slice = default_blockmap_num_slice;
    FLAGS_dfs_replication_min = default_dfs_replication_min;
    FLAGS_dfs_meta_storage_inode_key_v2 = default_dfs_meta_storage_inode_key_v2;
    FLAGS_write_back_manager_scan_task_interval_ms =
        default_write_back_manager_scan_task_interval_ms;
    FLAGS_namespace_type = default_namespace_type;
    FLAGS_block_report_scan_interval_sec =
        default_block_report_scan_interval_sec;
    FLAGS_write_back_task_v2_trigger_interval_us =
        default_write_back_task_v2_trigger_interval_us;
    FLAGS_lifecycle_enable = default_lifecycle_enable;
    FLAGS_ufs_sync_min_interval = default_ufs_sync_min_interval;
  }

 protected:
  void RunCase(std::function<void(TestAccNamespace* ns)> case_func) {
    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to run "
                   "case for TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      case_func(acc_namespaces_[0].get());
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finished to run "
                   "case for TestAccNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to run "
                   "case for TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      case_func(acc_namespaces_[1].get());
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finished to run "
                   "case for TestAccNamespace 1 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
  }

  std::shared_ptr<DeleteOption> NewDeleteOption(const std::string& ufs_path,
                                                const std::string& inner_path) {
    auto opt = std::make_shared<DeleteOption>();
    TestAccNamespace::CreateDefaultPermUgiAccInfo(
        &opt->perm, &opt->ugi, &opt->acc_fs_info);
    opt->path = ufs_path;
    opt->inner_path = inner_path;
    return opt;
  }

  std::shared_ptr<RenameToOption> NewRenameOption(
      const std::string& src_ufs_path,
      const std::string& src_inner_path,
      const std::string& dst_ufs_path,
      const std::string& dst_inner_path) {
    auto opt = std::make_shared<RenameToOption>();
    TestAccNamespace::CreateDefaultPermUgiAccInfo(
        &opt->perm, &opt->ugi, &opt->acc_fs_info);
    opt->src = src_ufs_path;
    opt->inner_src = src_inner_path;
    opt->dst = dst_ufs_path;
    opt->inner_dst = dst_inner_path;
    return opt;
  }

  std::shared_ptr<ListingOption> NewListingOption(const std::string& path) {
    auto opt = std::make_shared<ListingOption>();
    TestAccNamespace::CreateDefaultPermUgiAccInfo(
        &opt->perm, &opt->ugi, &opt->acc_fs_info);
    opt->path = path;
    return opt;
  }

 public:
  // threshold-1M for prev block
  const int kLowerMpuThresholdSize =
      FLAGS_acc_mpu_part_threshold - 1LL * 1024 * 1024;
  // threshold+1M for no prev block
  const int kHigherMpuThresholdSize =
      FLAGS_acc_mpu_part_threshold + 1LL * 1024 * 1024;
  // 31M for put
  const int k31M = 31L * 1024 * 1024;

 private:
  std::unique_ptr<TestAccNamespace> acc_namespaces_[2];
};

TEST_F(AccNamespaceTest, TestUfsFileStatus) {
  {
    UfsFileStatus f;
    UfsFileStatus::Builder().SetFullPath("/").SetFileSize(UFS_DIR).Build(&f);
    ASSERT_EQ(f.FileName(), "");
  }
  {
    UfsFileStatus f;
    UfsFileStatus::Builder().SetFullPath("/hello").SetFileSize(UFS_DIR).Build(
        &f);
    ASSERT_EQ(f.FileName(), "hello");
  }
  {
    UfsFileStatus f;
    UfsFileStatus::Builder().SetFullPath("/hello/").SetFileSize(UFS_DIR).Build(
        &f);
    ASSERT_EQ(f.FileName(), "hello");
  }
}
TEST_F(AccNamespaceTest, TestGetFileInfoRoot) {
  RunCase([this](TestAccNamespace* ns) {
    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix("/"),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_DIR);
    }
    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix("/nonexisted"),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.has_fs(), false);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetFileInfo) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_get_file_info.txt");
    ns->CreateObject(key, 1024);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    uint64_t ufs_get_count = mock_ufs->GetGetFileStatusCount();

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }

    uint64_t ufs_get_count2 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count + 1, ufs_get_count2);

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }

    {
      UfsFileStatus ufs_file;
      auto s =
          mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(key), &ufs_file);
      ASSERT_TRUE(s.IsOK());

      uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
      INode node;
      s = ns->ns_->GetINodeByPath("/" + key, &node);
      ASSERT_TRUE(s.IsOK());
      ASSERT_TRUE(node.has_ufs_file_info());
      ASSERT_TRUE((now_sec - node.ufs_file_info().sync_ts()) < 10);
      ASSERT_EQ(node.ufs_file_info().file_state(), kUfsFileStatePersisted);
      ASSERT_EQ(node.ufs_file_info().etag(), ufs_file.Etag());

      uint64_t size = 0;
      for (auto&& b : node.blocks()) {
        size += b.numbytes();
      }
      ASSERT_EQ(size, ufs_file.FileSize());
    }
  });
}

TEST_F(AccNamespaceTest, TestHttpGetFileInfo) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_get_file_info.txt");
    ns->CreateObject(key, 1024);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    uint64_t ufs_get_count = mock_ufs->GetGetFileStatusCount();

    {
      HdfsFileStatusProto rsp;
      SynchronizedRpcClosure done;
      auto s = ns->acc_ns_->GetFileInfoForHttp(
          ns->JoinUfsPathWithPrefix(key), false, info, &rsp);

      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(rsp.filetype(), cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.length(), 1024);
    }

    uint64_t ufs_get_count2 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count + 1, ufs_get_count2);

    {
      HdfsFileStatusProto rsp;
      SynchronizedRpcClosure done;
      auto s = ns->acc_ns_->GetFileInfoForHttp(
          ns->JoinUfsPathWithPrefix(key), false, info, &rsp);

      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(rsp.filetype(), cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.length(), 1024);
    }
  });
}

TEST_F(AccNamespaceTest, TestStandbyGetFileInfo) {
  RunCase([this](TestAccNamespace* ns) {
    FLAGS_enable_acc_sync_op_lazycheck = true;
    FLAGS_dfs_ha_allow_stale_reads = true;

    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_standby_get_file_info.txt");
    ns->CreateObject(key, 1024);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    uint64_t ufs_get_count = mock_ufs->GetGetFileStatusCount();

    {
      // change to active and get&&sync file
      auto ha = ns->runtime_.mock_ha_state;
      ha->SetReadOnly(false);

      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }
    uint64_t ufs_get_count2 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count + 1, ufs_get_count2);

    {
      // change to active and list&&sync file
      auto ha = ns->runtime_.mock_ha_state;
      ha->SetReadOnly(false);

      GetListingRequestProto req;
      GetListingResponseProto rsp;
      SynchronizedRpcClosure done;
      auto opt = NewListingOption(ns->JoinUfsPathWithPrefix(key));
      opt->acc_fs_info.set_syncinterval(60);

      ns->acc_ns_->AsyncGetListing(
          opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
      done.Await();
      auto s = done.status();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                ns->JoinUfsPathWithPrefix(key));
    }
    uint64_t ufs_get_count3 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count2, ufs_get_count3);

    {
      // change to standby and get file info
      ns->ns_->WaitNoPending();
      ns->ns_->StopActive();
      ns->ns_->StartStandby();
      auto ha = ns->runtime_.mock_ha_state;
      ha->SetReadOnly(true);

      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }
    uint64_t ufs_get_count4 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count3, ufs_get_count4);

    {
      // change to standby and list file
      GetListingRequestProto req;
      GetListingResponseProto rsp;
      SynchronizedRpcClosure done;
      auto opt = NewListingOption(ns->JoinUfsPathWithPrefix(key));
      opt->acc_fs_info.set_syncinterval(60);

      ns->acc_ns_->AsyncGetListing(
          opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
      done.Await();
      auto s = done.status();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                ns->JoinUfsPathWithPrefix(key));
    }
    uint64_t ufs_get_count5 = mock_ufs->GetGetFileStatusCount();
    ASSERT_EQ(ufs_get_count4, ufs_get_count5);

    {
      // change to standby and hit write op
      info.set_syncinterval(0);

      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().HasException()) << done.status().ToString();
    }

    {
      UfsFileStatus ufs_file;
      auto s =
          mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(key), &ufs_file);
      ASSERT_TRUE(s.IsOK());

      uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
      INode node;
      s = ns->ns_->GetINodeByPath("/" + key, &node);
      ASSERT_TRUE(s.IsOK());
      ASSERT_TRUE(node.has_ufs_file_info());
      ASSERT_TRUE((now_sec - node.ufs_file_info().sync_ts()) < 10);
      ASSERT_EQ(node.ufs_file_info().file_state(), kUfsFileStatePersisted);
      ASSERT_EQ(node.ufs_file_info().etag(), ufs_file.Etag());

      uint64_t size = 0;
      for (auto&& b : node.blocks()) {
        size += b.numbytes();
      }
      ASSERT_EQ(size, ufs_file.FileSize());
    }

    // change to active
    ns->ns_->StopStandby();
    ns->block_manager_->TestOnlySetEditLogSender(
      std::make_shared<MockEditLogSender>(ns->runtime_.edit_log_ctx,
                                          ns->ns_->GetLastCkptTxId()));
    ns->ns_->StartActive();
    auto ha = ns->runtime_.mock_ha_state;
    ha->SetReadOnly(false);
    FLAGS_enable_acc_sync_op_lazycheck = false;
    FLAGS_dfs_ha_allow_stale_reads = false;
  });
}

TEST_F(AccNamespaceTest, TestStandbyBatchGet) {
   FLAGS_nn_drive_upload = true;
   FLAGS_complete_rpc_abandon_last_empty_block = true;

  RunCase([this](TestAccNamespace* ns) {
    FLAGS_enable_acc_sync_op_lazycheck = true;
    FLAGS_dfs_ha_allow_stale_reads = true;
    FLAGS_enable_write_back = false;

    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus permission;
    FsPermissionProto fsp;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    XAttrProto no_upload_attr;
    UfsUploadPolicyProto no_upload_pb;
    no_upload_pb.set_upload_interval_ms(-1);
    no_upload_attr.set_namespace_(cloudfs::XAttrProto::SYSTEM);
    no_upload_attr.set_name("hdfs.upload.policy.string");
    no_upload_attr.set_value(no_upload_pb.SerializeAsString());

    LOG(INFO) << "Init";

    // mkdir
    std::string inner_dir = "/standby_batch_api_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(test_dir,
                               permission,
                               ugi,
                               false,
                               &request,
                               &response,
                               &ctrl,
                               &done,
                               info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    LOG(INFO) << "AsyncMkDirs";

    // batch create
    std::string target_file = test_dir + "/file2";
    std::string target_file_inner = inner_dir + "/file2";

    INodeID target_inode_id;
    std::vector<uint64_t> block_ids;

    BatchCreateFileRequestProto batch_create_request;
    BatchCreateFileResponseProto batch_create_response;
    batch_create_request.set_clientname("client_name");
    batch_create_request.set_rpc_type(RPC_BYTERPC_MODE);
    batch_create_request.set_expectediomode(DATANODE_BLOCK_EXPECTED);
    batch_create_request.set_withaddblock(true);
    LOG(INFO) << "Init BatchCreate Proto Start";
    {
      std::vector<std::string> paths;
      // file0
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file0");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file1
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file1");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file2_target
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file2");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }

      // file2_source
      for (int i = 0; i < 10; ++i) {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/.file2.part." + std::to_string(i));
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }
      LOG(INFO) << "Init BatchCreate Proto Finish";

      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = "";
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchCreate(paths,
                                    client_location,
                                    "client_machine-test",
                                    ugi,
                                    &batch_create_request,
                                    &batch_create_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();
      LOG(INFO) << "AsyncBatchCreate batch_create_response="
                << batch_create_response.ShortDebugString();

      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();

      target_inode_id = batch_create_response.files().Get(2).fs().fileid();
    }

    // batch complete
    BatchCompleteFileRequestProto batch_complete_request;
    BatchCompleteFileResponseProto batch_complete_response;
    batch_complete_request.set_clientname("client_name");
    batch_complete_request.set_rpc_type(RPC_BYTERPC_MODE);
    LOG(INFO) << "AsyncBatchComplete";
    {
      std::vector<std::string> single_file_paths;
      std::vector<std::vector<std::string>> concat_file_srcs_paths;
      std::vector<std::string> concat_file_target_paths;
      {
        auto file = batch_complete_request.add_singlefile();
        file->set_src(test_dir + "/file0");
        single_file_paths.push_back(file->src());
        auto block = file->mutable_lastblock();
        block->CopyFrom(batch_create_response.files().Get(0).block().b());
        block->set_numbytes(128 * 1024 * 1024);
        file->set_fileid(batch_create_response.files().Get(0).fs().fileid());
      }

      {
        auto file = batch_complete_request.add_singlefile();
        file->set_src(test_dir + "/file1");
        single_file_paths.push_back(file->src());
        auto block = file->mutable_lastblock();
        block->CopyFrom(batch_create_response.files().Get(1).block().b());
        block->set_numbytes(128 * 1024 * 1024);
        file->set_fileid(batch_create_response.files().Get(1).fs().fileid());
      }

      {
        auto concat_entry = batch_complete_request.add_concatfile();
        {
          auto target_file = concat_entry->mutable_target();
          target_file->set_src(test_dir + "/file2");
          concat_file_target_paths.push_back(target_file->src());
          target_file->set_fileid(
              batch_create_response.files().Get(2).fs().fileid());

          auto block = target_file->mutable_lastblock();
          block->CopyFrom(batch_create_response.files().Get(2).block().b());
          block->set_numbytes(0);
        }

        concat_file_srcs_paths.push_back({});
        for (int i = 0; i < 10; ++i) {
          auto file = concat_entry->add_srcs();
          file->set_src(test_dir + "/.file2.part." + std::to_string(i));
          concat_file_srcs_paths.back().push_back(file->src());

          auto block = file->mutable_lastblock();
          block->CopyFrom(batch_create_response.files().Get(3 + i).block().b());
          block->set_numbytes(128 * 1024 * 1024);
          file->set_fileid(
              batch_create_response.files().Get(3 + i).fs().fileid());

          block_ids.push_back(block->blockid());
        }
      }

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchComplete(single_file_paths,
                                      concat_file_srcs_paths,
                                      concat_file_target_paths,
                                      &batch_complete_request,
                                      &batch_complete_response,
                                      LogRpcInfo(),
                                      &rpc_done,
                                      &ctrl,
                                      info);
      rpc_done.Await();
      LOG(INFO) << "batch_complete_response="
                << batch_complete_response.ShortDebugString();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      ASSERT_EQ(batch_complete_response.result(), 1);
      LOG(INFO) << "AsyncBatchComplete Finish";
    }

    // change to standby
    LOG(INFO) << "***********************Begin to change standby*******************";
    ns->ns_->WaitNoPending();
    ns->ns_->StopActive();
    ns->ns_->StartStandby();
    auto ha = ns->runtime_.mock_ha_state;
    ha->SetState(cloudfs::HAServiceStateProto::STANDBY);
    ha->SetReadOnly(true);
    LOG(INFO) << "***********************End to change standby*******************";

    // check file0
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file0");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(test_dir + "/file0",
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file0, res=" << get_response.ShortDebugString();

      EXPECT_EQ(1, get_response.locations().blocks_size());
    }
    // check file1
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file1");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(test_dir + "/file1",
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file1, res=" << get_response.ShortDebugString();

      EXPECT_EQ(1, get_response.locations().blocks_size());
    }

    // check file2 bip
    bool exp_key_block = false;
    int exp_part_num = 1;
    uint64_t exp_bundle_offset = 0;
    uint64_t exp_bundle_length = 0;
    uint64_t exp_pufs_offset = 0;

    INode target_inode;
    ASSERT_EQ(ns->ns_->meta_storage()->GetINode(target_inode_id, &target_inode),
              StatusCode::kOK);

    BlockInfoProto last_bip;
    last_bip.set_block_id(kInvalidBlockID);
    for (const auto& b : target_inode.blocks()) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip));

      exp_key_block = false;
      uint64_t total_size = bip.num_bytes();
      if (last_bip.block_id() != kInvalidBlockID) {
        if (last_bip.key_block()) {
          exp_part_num = last_bip.part_num() + 1;
          exp_bundle_offset = last_bip.bundle_offset() +
                              last_bip.bundle_length() + last_bip.num_bytes();
          exp_bundle_length = 0;
        } else {
          exp_part_num = last_bip.part_num();
          exp_bundle_offset = last_bip.bundle_offset();
          exp_bundle_length = last_bip.bundle_length() + last_bip.num_bytes();

          total_size += last_bip.bundle_length() + last_bip.num_bytes();
        }
      }

      if (total_size > FLAGS_acc_mpu_part_threshold ||
          b.blockid() ==
              target_inode.blocks(target_inode.blocks_size() - 1).blockid()) {
        exp_key_block = true;
      }
      ASSERT_EQ(bip.key_block(), exp_key_block) << bip.block_id();
      ASSERT_EQ(bip.pufs_offset(), exp_pufs_offset) << bip.block_id();
      ASSERT_EQ(bip.part_num(), exp_part_num) << bip.block_id();
      ASSERT_EQ(bip.bundle_length(), exp_bundle_length) << bip.block_id();
      ASSERT_EQ(bip.bundle_offset(), exp_bundle_offset) << bip.block_id();

      exp_pufs_offset += bip.num_bytes();

      last_bip.CopyFrom(bip);
    }

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + target_file_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // check file2
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file2");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(target_file,
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file2, res=" << get_response.ShortDebugString();

      EXPECT_EQ(10, get_response.locations().blocks_size());

      auto located_blocks = get_response.locations();
      ASSERT_FALSE(located_blocks.underconstruction());
      ASSERT_EQ(located_blocks.blocks_size(), 10);
      for (int i = 0; i < 10; i++) {
        auto bid = located_blocks.blocks(i).b().blockid();
        ASSERT_EQ(bid, block_ids[i]);
      }
    }

    // batch get
    LOG(INFO) << "AsyncBatchGet Start";
    BatchGetFileRequestProto batch_get_request;
    BatchGetFileResponseProto batch_get_response;
    // reordered
    batch_get_request.add_srcs(test_dir + "/file1");
    batch_get_request.add_srcs(test_dir + "/file5");
    batch_get_request.add_srcs(test_dir + "/file2");
    // not exist
    batch_get_request.add_srcs(test_dir + "/file3");
    batch_get_request.add_srcs(test_dir + "/file0");
    batch_get_request.set_needlocation(true);
    {
      std::vector<std::string> paths;
      for (const auto& src : batch_get_request.srcs()) {
        paths.push_back(src);
      }

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchGet(paths,
                                 NetworkLocationInfo(),
                                 true,
                                 permission,
                                 ugi,
                                 &batch_get_request,
                                 &batch_get_response,
                                 &rpc_done,
                                 &ctrl,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      LOG(INFO) << "AsyncBatchGet Finish";

      ASSERT_EQ(batch_get_response.files().size(), 3);
      ASSERT_EQ(batch_get_response.original_index().size(), 3);
      ASSERT_EQ(batch_get_response.original_index().Get(0), 0);
      ASSERT_EQ(batch_get_response.original_index().Get(1), 2);
      ASSERT_EQ(batch_get_response.original_index().Get(2), 4);

      auto response_file0 = batch_get_response.files().Get(2);
      auto response_file1 = batch_get_response.files().Get(0);
      auto response_file2 = batch_get_response.files().Get(1);

      ASSERT_EQ(batch_create_response.files().Get(0).fs().fileid(),
                response_file0.fileid());
      ASSERT_EQ(1, response_file0.locations().blocks_size());

      ASSERT_EQ(batch_create_response.files().Get(1).fs().fileid(),
                response_file1.fileid());
      ASSERT_EQ(1, response_file1.locations().blocks_size());

      ASSERT_EQ(batch_create_response.files().Get(2).fs().fileid(),
                response_file2.fileid());
      ASSERT_EQ(10, response_file2.locations().blocks_size());
      for (int i = 0; i < 10; i++) {
        auto bid = response_file2.locations().blocks(i).b().blockid();
        ASSERT_EQ(bid, block_ids[i]);
      }
    }

    // change to active
    ns->ns_->StopStandby();
    ns->block_manager_->TestOnlySetEditLogSender(
      std::make_shared<MockEditLogSender>(ns->runtime_.edit_log_ctx,
                                          ns->ns_->GetLastCkptTxId()));
    ns->ns_->StartActive();
    ha->SetState(cloudfs::HAServiceStateProto::ACTIVE);
    ha->SetReadOnly(false);
    FLAGS_enable_acc_sync_op_lazycheck = false;
    FLAGS_dfs_ha_allow_stale_reads = false;
    ns->need_reset_flags_ = true;
  });
}

TEST_F(AccNamespaceTest, TestGetFileInfoLevelNFile) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("a/b/c/d/e.txt");
    ns->CreateObject(key, 1024);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    uint64_t ufs_get_count = mock_ufs->GetGetFileStatusCount();

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }

    uint64_t ufs_get_count2 = mock_ufs->GetGetFileStatusCount();
    ASSERT_TRUE((ufs_get_count + 1 == ufs_get_count2) ||
                (ufs_get_count + 2 == ufs_get_count2));

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(key),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      ASSERT_EQ(rsp.fs().length(), 1024);
    }

    {
      // Check file
      UfsFileStatus ufs_file;
      auto s =
          mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(key), &ufs_file);
      ASSERT_TRUE(s.IsOK());

      uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
      INode node;
      s = ns->ns_->GetINodeByPath("/" + key, &node);
      ASSERT_TRUE(s.IsOK());
      ASSERT_TRUE(node.has_ufs_file_info());
      ASSERT_TRUE((now_sec - node.ufs_file_info().sync_ts()) < 10);
      ASSERT_EQ(node.ufs_file_info().file_state(), kUfsFileStatePersisted);
      ASSERT_EQ(node.ufs_file_info().etag(), ufs_file.Etag());

      uint64_t size = 0;
      for (auto&& b : node.blocks()) {
        size += b.numbytes();
      }
      ASSERT_EQ(size, ufs_file.FileSize());
    }
    {
      // Check ancestors folder, should be incomplete;
      // parent folder might be synced because we have triggered parent folder
      // sync in GetFileInfo
      for (auto&& dir : std::vector<std::string>{"/a", "/a/b", "/a/b/c"}) {
        INode node;
        auto s = ns->ns_->GetINodeByPath(dir, &node);
        ASSERT_TRUE(s.IsOK());
        ASSERT_TRUE(node.has_ufs_dir_info());
        ASSERT_EQ(node.ufs_dir_info().sync_ts(), 0);
        ASSERT_EQ(node.ufs_dir_info().children_sync_ts(), 0);
        ASSERT_EQ(node.ufs_dir_info().state(), kUfsDirStateIncomplete);
        ASSERT_EQ(node.ufs_dir_info().type(), kUfsDirTypeUfs);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestGetBlockLocations) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_get_block_locations.txt");
    std::string path = "/" + key;
    uint64_t max_length = 1024 * 1024 * 1024;
    uint64_t length = 200L * 1024 * 1024;
    ns->CreateObject(key, length);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);

    cnetpp::base::IPAddress ip("127.0.0.1");

    {
      cloudfs::GetBlockLocationsRequestProto req;
      req.set_src(path);
      req.set_offset(0);
      req.set_length(max_length);
      cloudfs::GetBlockLocationsResponseProto rsp;
      SynchronizedRpcClosure done;

      info.set_syncinterval(0);
      ns->acc_ns_->AsyncGetBlockLocations(ns->JoinUfsPathWithPrefix(key),
                                          permission,
                                          ugi,
                                          dummy_rpc_info,
                                          NetworkLocationInfo(ip),
                                          info,
                                          &req,
                                          &rsp,
                                          nullptr,
                                          &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.locations().blocks_size(), 2);
      ASSERT_EQ(rsp.locations().blocks(0).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(0).b().offset(), 0);
      ASSERT_EQ(rsp.locations().blocks(0).b().numbytes(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(1).b().offset(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().numbytes(), 72L * 1024 * 1024);
    }

    length = 0;
    ns->CreateObject(key, length);
    {
      cloudfs::GetBlockLocationsRequestProto req;
      req.set_src(path);
      req.set_offset(0);
      req.set_length(max_length);
      cloudfs::GetBlockLocationsResponseProto rsp;
      SynchronizedRpcClosure done;

      info.set_syncinterval(-1);
      ns->acc_ns_->AsyncGetBlockLocations(ns->JoinUfsPathWithPrefix(key),
                                          permission,
                                          ugi,
                                          dummy_rpc_info,
                                          NetworkLocationInfo(ip),
                                          info,
                                          &req,
                                          &rsp,
                                          nullptr,
                                          &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.locations().blocks_size(), 2);
      ASSERT_EQ(rsp.locations().blocks(0).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(0).b().offset(), 0);
      ASSERT_EQ(rsp.locations().blocks(0).b().numbytes(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(1).b().offset(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().numbytes(), 72L * 1024 * 1024);
    }

    length = 300L * 1024 * 1024;
    ns->CreateObject(key, length);
    {
      cloudfs::GetBlockLocationsRequestProto req;
      req.set_src(path);
      req.set_offset(0);
      req.set_length(max_length);
      cloudfs::GetBlockLocationsResponseProto rsp;
      SynchronizedRpcClosure done;

      info.set_syncinterval(0);
      ns->acc_ns_->AsyncGetBlockLocations(ns->JoinUfsPathWithPrefix(key),
                                          permission,
                                          ugi,
                                          dummy_rpc_info,
                                          NetworkLocationInfo(ip),
                                          info,
                                          &req,
                                          &rsp,
                                          nullptr,
                                          &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.locations().blocks_size(), 3);
      ASSERT_EQ(rsp.locations().blocks(0).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(0).b().offset(), 0);
      ASSERT_EQ(rsp.locations().blocks(0).b().numbytes(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(1).b().offset(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(1).b().numbytes(), 128L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(2).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      ASSERT_EQ(rsp.locations().blocks(2).b().offset(), 256L * 1024 * 1024);
      ASSERT_EQ(rsp.locations().blocks(2).b().numbytes(), 44L * 1024 * 1024);
    }
  });
}

TEST_F(AccNamespaceTest, TestCreateFile) {
  RunCase([this](TestAccNamespace* ns) {
    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(0);

    // create ufs non-exists
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCreateFileUfsNonExists");
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }

    // create ufs non-exists missing parent
    {
      std::string src = ns->JoinUfsPathWithPrefix(
          "/dir/TestCreateFileUfsNonExistsMissingParent");
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createparent(true);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }

    // Create overwrite file with ufs exists
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCreateFileOverwriteUfsExists");
      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_createflag(CreateFlag::SetOverwrite(request.createflag()));
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }

    // Create non-overwrite with ufs exists, should fail
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCreateFileNonOverwriteUfsExists");
      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_FALSE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        uint64_t length = 200L * 1024 * 1024;
        ASSERT_EQ(rsp.fs().length(), length);
      }
    }

    // Create append-able object
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCreateFileAppendable");
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(request.createflag() |
                               ::cloudfs::CreateFlagProto::APPEND);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        UfsFileStatus status;
        ASSERT_FALSE(mock_ufs->GetFileStatus(src, &status).IsOK());
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestGetFileInfoTriggerParentDirListingSync) {
  FLAGS_ufs_sync_listing_prefetch_enabled = true;
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(-1);

    std::string dir_inner_path = "/TestGetFileInfoTriggerParentDirListingSync";
    std::string dir_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
    std::string file1_inner_path = dir_inner_path + "/file1";
    std::string file1_path = ns->JoinUfsPathWithPrefix(file1_inner_path);
    std::string file2_inner_path = dir_inner_path + "/file2";
    std::string file2_path = ns->JoinUfsPathWithPrefix(file2_inner_path);

    {
      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(file1_path), &key);
      ASSERT_TRUE(mock_ufs->CreateObject(key, 1024).IsOK());
    }
    {
      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(file2_path), &key);
      ASSERT_TRUE(mock_ufs->CreateObject(key, 1024).IsOK());
    }

    uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
    {
      // GetFileInfo for file1
      {
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file1_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_TRUE((cnt + 1 == cnt2) || (cnt + 2 == cnt2));

        // Wait and verify parent dir listing is triggered async
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt + 1, list_cnt2);
      }
      {
        // Check Dir INode
        INode node;
        auto s = ns->ns_->GetINodeByPath(dir_inner_path, &node);
        ASSERT_TRUE(s.IsOK());
        ASSERT_TRUE(node.has_ufs_dir_info());
        ASSERT_EQ(node.ufs_dir_info().type(), kUfsDirTypeUfs);
        ASSERT_EQ(node.ufs_dir_info().state(), kUfsDirStateSynced);
        ASSERT_TRUE(node.ufs_dir_info().sync_ts() - now_sec < 30);
        ASSERT_TRUE(node.ufs_dir_info().children_sync_ts() - now_sec < 30);
      }

      // GetFileInfo for file2
      {
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file2_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        // file2 UFS sync should not happen
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt, cnt2);

        // Wait and verify parent dir listing is not triggered
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt, list_cnt2);
      }

      // GetFileInfo for file2 again
      {
        info.set_syncinterval(0);
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file2_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        // file2 UFS sync once
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt + 1, cnt2);

        // Wait and verify parent dir listing is not triggered
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt, list_cnt2);
      }
    }
  });
  FLAGS_ufs_sync_listing_prefetch_enabled = false;
}

TEST_F(AccNamespaceTest, TestGetFileInfoTriggerParentDirListingSyncFailed) {
  FLAGS_ufs_sync_listing_prefetch_enabled = true;
  FLAGS_ufs_sync_listing_page_size = 1;
  FLAGS_ufs_sync_listing_prefetch_max_count = 1;
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(-1);

    std::string dir_inner_path =
        "/TestGetFileInfoTriggerParentDirListingSyncFailed";
    std::string dir_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
    std::string file1_inner_path = dir_inner_path + "/file1";
    std::string file1_path = ns->JoinUfsPathWithPrefix(file1_inner_path);
    std::string file2_inner_path = dir_inner_path + "/file2";
    std::string file2_path = ns->JoinUfsPathWithPrefix(file2_inner_path);

    {
      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(file1_path), &key);
      ASSERT_TRUE(mock_ufs->CreateObject(key, 1024).IsOK());
    }
    {
      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(file2_path), &key);
      ASSERT_TRUE(mock_ufs->CreateObject(key, 1024).IsOK());
    }

    uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
    {
      // GetFileInfo for file1
      {
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file1_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_TRUE((cnt + 1 == cnt2) || (cnt + 2 == cnt2));

        // Wait and verify parent dir listing is triggered async
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt + 2, list_cnt2);
      }
      {
        // Check Dir INode
        INode node;
        auto s = ns->ns_->GetINodeByPath(dir_inner_path, &node);
        ASSERT_TRUE(s.IsOK());
        ASSERT_TRUE(node.has_ufs_dir_info());
        ASSERT_EQ(node.ufs_dir_info().type(), kUfsDirTypeUfs);
        ASSERT_EQ(node.ufs_dir_info().state(), kUfsDirStateIncomplete);
        ASSERT_TRUE(node.ufs_dir_info().sync_ts() - now_sec < 30);
        ASSERT_TRUE(node.ufs_dir_info().children_sync_ts() - now_sec >= 30);
      }

      // GetFileInfo for file2
      {
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file2_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        // file2 UFS sync should not happen
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt, cnt2);

        // Wait and verify parent dir listing is not triggered
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt, list_cnt2);
      }

      // GetFileInfo for file2 again
      {
        info.set_syncinterval(0);
        auto cnt = mock_ufs->GetGetFileStatusCount();
        auto list_cnt = mock_ufs->GetListFilesCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file2_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_TRUE(rsp.has_fs());
        }
        // file2 UFS sync once
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt + 1, cnt2);

        // Wait and verify parent dir listing is not triggered
        std::this_thread::sleep_for(std::chrono::seconds(5));
        auto list_cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(list_cnt, list_cnt2);
      }
    }
  });
  FLAGS_ufs_sync_listing_prefetch_enabled = false;
  FLAGS_ufs_sync_listing_page_size = 1000;
  FLAGS_ufs_sync_listing_prefetch_max_count = 10000;
}

TEST_F(AccNamespaceTest, TestGetFileInfoAfterMkdirNoUfsSync) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(-1);

    std::string dir_inner_path = "/TestGetFileInfoAfterMkdirNoUfsSync";
    std::string dir_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
    uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
    {
      {
        MkdirsRequestProto request;
        request.set_src(dir_path);
        request.mutable_masked()->set_perm(0);
        request.set_createparent(false);
        MkdirsResponseProto response;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncMkDirs(
            dir_path, p, ugi, false, &request, &response, &ctrl, &done, info);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        UfsDirStatus dir;
        ASSERT_TRUE(mock_ufs->GetDirectoryStatus(dir_path, &dir).IsOK());
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(dir_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }

      {
        // Check Dir INode
        INode node;
        auto s = ns->ns_->GetINodeByPath(dir_inner_path, &node);
        ASSERT_TRUE(s.IsOK());
        ASSERT_TRUE(node.has_ufs_dir_info());
        ASSERT_EQ(node.ufs_dir_info().type(), kUfsDirTypeUfs);
        ASSERT_EQ(node.ufs_dir_info().state(), kUfsDirStateSynced);
        ASSERT_TRUE(node.ufs_dir_info().sync_ts() - now_sec < 30);
        ASSERT_TRUE(node.ufs_dir_info().children_sync_ts() - now_sec < 30);
      }

      // GetFileInfo for non-existed file, UFS sync should not happen
      {
        std::string file_inner_path = dir_inner_path + "/file";
        std::string file_ufs_path = dir_path + "/file";
        auto cnt = mock_ufs->GetGetFileStatusCount();
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file_ufs_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_FALSE(rsp.has_fs());
        }
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt, cnt2);
      }
    }
  });
}
TEST_F(AccNamespaceTest, TestCreateFileAfterMkdirNoUfsSync) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(-1);

    std::string dir_inner_path = "/TestCreateFileAfterMkdirNoUfsSync";
    std::string dir_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
    uint64_t now_sec = TimeUtil::GetNowEpochMs() / 1000;
    {
      {
        MkdirsRequestProto request;
        request.set_src(dir_path);
        request.mutable_masked()->set_perm(0);
        request.set_createparent(false);
        MkdirsResponseProto response;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncMkDirs(
            dir_path, p, ugi, false, &request, &response, &ctrl, &done, info);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        UfsDirStatus dir;
        ASSERT_TRUE(mock_ufs->GetDirectoryStatus(dir_path, &dir).IsOK());
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(dir_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }

      {
        // Check Dir INode
        INode node;
        auto s = ns->ns_->GetINodeByPath(dir_inner_path, &node);
        ASSERT_TRUE(s.IsOK());
        ASSERT_TRUE(node.has_ufs_dir_info());
        ASSERT_EQ(node.ufs_dir_info().type(), kUfsDirTypeUfs);
        ASSERT_EQ(node.ufs_dir_info().state(), kUfsDirStateSynced);
        ASSERT_TRUE(node.ufs_dir_info().sync_ts() - now_sec < 30);
        ASSERT_TRUE(node.ufs_dir_info().children_sync_ts() - now_sec < 30);
      }

      // Create a file, UFS sync should not happen
      {
        std::string file_inner_path = dir_inner_path + "/file";
        std::string file_ufs_path = dir_path + "/file";
        auto cnt = mock_ufs->GetGetFileStatusCount();
        {
          CreateRequestProto request = TestAccNamespace::GetCreateRequest();
          request.set_src(file_ufs_path);
          CreateResponseProto response;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncCreateFile(file_ufs_path,
                                       p,
                                       NetworkLocationInfo(),
                                       ugi,
                                       LogRpcInfo(),
                                       "",
                                       &request,
                                       &response,
                                       &ctrl,
                                       &rpc_done,
                                       info);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        }
        auto cnt2 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt, cnt2);
        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(file_ufs_path,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_EQ(rsp.fs().length(), 0);
        }
        auto cnt3 = mock_ufs->GetGetFileStatusCount();
        ASSERT_EQ(cnt, cnt3);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestAbandonLastBlock) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    const int k1M = 1000000;

    std::string src = ns->JoinUfsPathWithPrefix("/TestAbandonLastBlock");
    uint64_t fileid = 0;
    {
      CreateRequestProto request = TestAccNamespace::GetCreateRequest();
      request.set_src(src);
      request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
      CreateResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCreateFile(src,
                                   p,
                                   NetworkLocationInfo(),
                                   ugi,
                                   LogRpcInfo(),
                                   "",
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      fileid = response.fs().fileid();
    }

    ExtendedBlockProto last;
    {
      AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      AddBlockResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAddBlock(src,
                                 NetworkLocationInfo(client_ip),
                                 LogRpcInfo(),
                                 ugi,
                                 &request,
                                 &response,
                                 &ctrl,
                                 &rpc_done,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      last = response.block().b();
      last.set_numbytes(k1M);
    }

    {
      BlockManager::RepeatedIncBlockReport received_report;
      TestAccNamespace::MakeReport(
          last.blockid(),
          last.generationstamp(),
          last.numbytes(),
          "",  // etag
          "",  // upload id
          "",  // pufs_name
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &received_report);
      Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                            received_report);
      ASSERT_TRUE(s.IsOK());
    }

    ExtendedBlockProto tobeabandon;
    {
      AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      request.mutable_previous()->CopyFrom(last);
      AddBlockResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAddBlock(src,
                                 NetworkLocationInfo(client_ip),
                                 LogRpcInfo(),
                                 ugi,
                                 &request,
                                 &response,
                                 &ctrl,
                                 &rpc_done,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      tobeabandon = response.block().b();
    }

    {
      cloudfs::AbandonBlockRequestProto request;
      request.mutable_b()->CopyFrom(tobeabandon);
      request.set_src(src);
      request.set_fileid(fileid);
      request.set_holder("client");
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAbandonBlock(src, &request, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
    }

    {
      CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      request.mutable_last()->CopyFrom(last);
      CompleteResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCompleteFile(
          src, &request, &response, &ctrl, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
    }

    std::string upload_id, pufs_name;
    bool wait_upload_cmd = true;
    {
      for (int i = 0; i < 10; i++) {
        BlockManager::RepeatedIncBlockReport nego_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED,
            &nego_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              nego_report);
        ASSERT_TRUE(s.IsOK());

        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
            if (c.uploadcmd().block().blockid() == last.blockid()) {
              ASSERT_EQ(c.uploadcmd().uploadtype(), cloudfs::datanode::UPLOAD);
              ASSERT_TRUE(mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                              .IsOK());
              upload_id = c.uploadcmd().uploadid();
              pufs_name = c.uploadcmd().blockpufsname();
              wait_upload_cmd = false;
              break;
            }
          }
        }
        if (!wait_upload_cmd) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    }
    ASSERT_FALSE(wait_upload_cmd);

    // DN upload success
    std::string etag;
    mock_ufs->AddPartToObject(src, upload_id, 1, k1M, &etag);

    bool wait_evict_cmd = true;
    {
      for (int i = 0; i < 10; i++) {
        BlockManager::RepeatedIncBlockReport upload_success_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            etag,
            upload_id,
            pufs_name,
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
            &upload_success_report);
        Status s = ns->block_manager_->IncrementalBlockReport(
            "datanode1", upload_success_report);
        ASSERT_TRUE(s.IsOK());

        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::NotifyEvictableCommand) {
            if (c.necmd().block().blockid() == last.blockid()) {
              wait_evict_cmd = false;
              break;
            }
          }
        }
        if (!wait_evict_cmd) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    }
    ASSERT_FALSE(wait_evict_cmd);

    {
      bool persisted = false;
      for (int i = 0; i < 10; i++) {
        GetFileInfoResponseProto res;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &res,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        persisted = res.fs().acc_file_status() ==
                    HdfsFileStatusProto_AccFileStatus::
                        HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
        if (persisted) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
      ASSERT_TRUE(persisted);
    }

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(src,
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    p,
                                    ugi,
                                    info,
                                    &rsp,
                                    &ctrl,
                                    &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().length(), k1M);
    }
  });
}

TEST_F(AccNamespaceTest, TestPersistOrder) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    // mkdir
    {
      std::string src = ns->JoinUfsPathWithPrefix("/dir1");
      MkdirsRequestProto request;
      request.set_src(src);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          src, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      UfsDirStatus dir;
      ASSERT_TRUE(mock_ufs->GetDirectoryStatus(src, &dir).IsOK());
    }

    {
      std::string src = ns->JoinUfsPathWithPrefix("/dir1/TestPersistOrder");
      std::string dst = ns->JoinUfsPathWithPrefix("/dir2/TestPersistOrder");
      uint64_t fileid_old = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid_old = response.fs().fileid();
      }

      {
        std::string inner_path = "/dir1";
        std::string ufs_path = ns->JoinUfsPathWithPrefix("/dir1");
        std::string dst_inner_path = "/dir2";
        std::string dst_ufs_path = ns->JoinUfsPathWithPrefix("/dir2");
        auto opt =
            NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);

        cloudfs::Rename2ResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        std::string src = ns->JoinUfsPathWithPrefix("/dir1");
        MkdirsRequestProto request;
        request.set_src(src);
        request.mutable_masked()->set_perm(0);
        request.set_createparent(false);
        MkdirsResponseProto response;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncMkDirs(
            src, p, ugi, false, &request, &response, &ctrl, &done, info);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        UfsDirStatus dir;
        ASSERT_TRUE(mock_ufs->GetDirectoryStatus(src, &dir).IsOK());
      }

      {
        uint64_t fileid = 0;
        {
          CreateRequestProto request = TestAccNamespace::GetCreateRequest();
          request.set_src(src);
          CreateResponseProto response;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncCreateFile(src,
                                       p,
                                       NetworkLocationInfo(),
                                       ugi,
                                       LogRpcInfo(),
                                       "",
                                       &request,
                                       &response,
                                       &ctrl,
                                       &rpc_done,
                                       info);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          fileid = response.fs().fileid();
        }

        {
          CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
          request.set_src(src);
          request.set_fileid(fileid);
          CompleteResponseProto response;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncCompleteFile(
              src, &request, &response, &ctrl, &rpc_done, info);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        }

        {
          bool persisted = false;
          for (int i = 0; i < 10; i++) {
            GetFileInfoResponseProto res;
            SynchronizedRpcClosure rpc_done;
            ns->acc_ns_->AsyncGetFileInfo(src,
                                          NetworkLocationInfo(),
                                          false,
                                          false,
                                          p,
                                          ugi,
                                          info,
                                          &res,
                                          &ctrl,
                                          &rpc_done);
            rpc_done.Await();
            ASSERT_TRUE(rpc_done.status().IsOK())
                << rpc_done.status().ToString();
            persisted = res.fs().acc_file_status() ==
                        HdfsFileStatusProto_AccFileStatus::
                            HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
            if (persisted) {
              break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
          }
          ASSERT_TRUE(persisted);
        }

        {
          cloudfs::GetFileInfoResponseProto rsp;
          SynchronizedRpcClosure done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &rsp,
                                        &ctrl,
                                        &done);
          done.Await();
          ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
          ASSERT_EQ(rsp.fs().length(), 0);
        }
      }

      ExtendedBlockProto last;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid_old);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(k31M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid_old);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      // DN upload success
      std::string src_etag;
      std::string dst_etag;
      std::string src_upload_id;
      std::string dst_upload_id;
      std::string src_pufs_name;
      std::string dst_pufs_name;

      // Wait dst only
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                Status s_src =
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid());
                Status s_dst =
                    mock_ufs->CheckUploadId(dst, c.uploadcmd().uploadid());
                if (s_src.IsOK()) {
                  src_upload_id = c.uploadcmd().uploadid();
                  src_pufs_name = c.uploadcmd().blockpufsname();
                  mock_ufs->AddPartToObject(
                      src, src_upload_id, 1, k31M, &src_etag);
                }
                if (s_dst.IsOK()) {
                  wait_upload_cmd = false;
                  dst_upload_id = c.uploadcmd().uploadid();
                  dst_pufs_name = c.uploadcmd().blockpufsname();
                  mock_ufs->AddPartToObject(
                      dst, dst_upload_id, 1, k31M, &dst_etag);
                }
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              src_etag,
              src_upload_id,
              src_pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              ASSERT_NE(c.necmd().block().blockid(), last.blockid());
            }
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              dst_etag,
              dst_upload_id,
              dst_pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
                break;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(dst,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestMkdirs) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestMkdirs");

      {
        MkdirsRequestProto request;
        request.set_src(src);
        request.mutable_masked()->set_perm(0);
        request.set_createparent(false);
        MkdirsResponseProto response;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncMkDirs(
            src, p, ugi, false, &request, &response, &ctrl, &done, info);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        UfsDirStatus dir;
        ASSERT_TRUE(mock_ufs->GetDirectoryStatus(src, &dir).IsOK());
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }

    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestMkdirsFail");

      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
      ASSERT_TRUE(mock_ufs->CreateObject(key, 0).IsOK());

      MkdirsRequestProto request;
      request.set_src(src);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          src, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_FALSE(done.status().IsOK()) << done.status().ToString();
    }
  });
}

TEST_F(AccNamespaceTest, TestMaxFileSize) {
  uint32_t acc_mpu_max_part = FLAGS_acc_mpu_max_part;
  uint32_t acc_mpu_part_threshold = FLAGS_acc_mpu_part_threshold;
  FLAGS_acc_mpu_max_part = 100;
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    // 128M for no prev block
    const int k128M = 128 * 1024 * 1024;

    std::string src = ns->JoinUfsPathWithPrefix("/TestMaxFileSize");
    uint64_t fileid = 0;
    {
      CreateRequestProto request = TestAccNamespace::GetCreateRequest();
      request.set_src(src);
      request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
      CreateResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCreateFile(src,
                                   p,
                                   NetworkLocationInfo(),
                                   ugi,
                                   LogRpcInfo(),
                                   "",
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      fileid = response.fs().fileid();
    }

    ExtendedBlockProto last;
    for (int i = 0; i < FLAGS_acc_mpu_max_part; i++) {
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_previous()->CopyFrom(last);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(k128M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }
    }

    {
      AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      request.mutable_previous()->CopyFrom(last);
      AddBlockResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAddBlock(src,
                                 NetworkLocationInfo(client_ip),
                                 LogRpcInfo(),
                                 ugi,
                                 &request,
                                 &response,
                                 &ctrl,
                                 &rpc_done,
                                 info);
      rpc_done.Await();
      ASSERT_FALSE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      ASSERT_EQ(rpc_done.status().exception(), JavaExceptions::kIOException);
    }

    {
      CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      request.mutable_last()->CopyFrom(last);
      CompleteResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCompleteFile(
          src, &request, &response, &ctrl, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
    }
  });
  FLAGS_acc_mpu_max_part = acc_mpu_max_part;
  FLAGS_acc_mpu_part_threshold = acc_mpu_part_threshold;
}

TEST_F(AccNamespaceTest, TestCompleteFile1) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete with path
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteWithPath");
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile2) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete empty file async
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCompleteEmptyFileAsync");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile3) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete empty file through
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCompleteEmptyFileThrough");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile4) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete file conflict with ufs
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteUfsConflict");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), 0);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile5) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete file conflict with ufs
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteUfsConflict2");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }

      ExtendedBlockProto last;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(k31M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      std::string upload_id, pufs_name;
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                upload_id = c.uploadcmd().uploadid();
                pufs_name = c.uploadcmd().blockpufsname();
                wait_upload_cmd = false;
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      // DN upload success
      std::string etag;
      mock_ufs->AddPartToObject(src, upload_id, 1, k31M, &etag);

      {
        bool wait_evict_cmd = true;
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              etag,
              upload_id,
              pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
                break;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_FALSE(wait_evict_cmd);
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile6) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete a file less than 32M
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteLessThan32M");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      ExtendedBlockProto last;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(k31M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      std::string upload_id, pufs_name;
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                upload_id = c.uploadcmd().uploadid();
                pufs_name = c.uploadcmd().blockpufsname();
                wait_upload_cmd = false;
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      // DN upload success
      std::string etag;
      mock_ufs->AddPartToObject(src, upload_id, 1, k31M, &etag);

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              etag,
              upload_id,
              pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
                break;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile7) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete a file less than 100M
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteLessThan100M");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      ExtendedBlockProto last;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(kLowerMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      std::string upload_id, pufs_name;
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                upload_id = c.uploadcmd().uploadid();
                pufs_name = c.uploadcmd().blockpufsname();
                wait_upload_cmd = false;
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      // DN upload success
      std::string etag;
      mock_ufs->AddPartToObject(
          src, upload_id, 1, kLowerMpuThresholdSize, &etag);

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              etag,
              upload_id,
              pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
                break;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), kLowerMpuThresholdSize);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile8) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete a file larger than 100M
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCompleteLargerThan101M");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      int success1(0), success2(0), success3(0);
      ExtendedBlockProto eb1;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        eb1 = response.block().b();
        eb1.set_numbytes(kLowerMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            eb1.blockid(),
            eb1.generationstamp(),
            eb1.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      ExtendedBlockProto eb2;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_previous()->CopyFrom(eb1);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        eb2 = response.block().b();
        eb2.set_numbytes(kLowerMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            eb2.blockid(),
            eb2.generationstamp(),
            eb2.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      ExtendedBlockProto last;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_previous()->CopyFrom(eb2);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(kHigherMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      std::string upload_id, pufs_name;
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                eb1.blockid(),
                eb1.generationstamp(),
                eb1.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                eb2.blockid(),
                eb2.generationstamp(),
                eb2.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                last.blockid(),
                last.generationstamp(),
                last.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          std::this_thread::sleep_for(std::chrono::seconds(1));
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == eb1.blockid()) {
                ASSERT_TRUE(false);
              } else if (c.uploadcmd().block().blockid() == eb2.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                success2 = 1;
              } else if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                success3 = 1;
              } else {
                ASSERT_TRUE(false);
              }
              if (success2 == 1 && success3 == 1) {
                upload_id = c.uploadcmd().uploadid();
                pufs_name = c.uploadcmd().blockpufsname();
                success2 = 0;
                success3 = 0;
                wait_upload_cmd = false;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      // DN upload success
      std::string etag1;
      mock_ufs->AddPartToObject(src,
                                upload_id,
                                1,
                                kLowerMpuThresholdSize + kLowerMpuThresholdSize,
                                &etag1);
      std::string etag2;
      mock_ufs->AddPartToObject(
          src, upload_id, 2, kHigherMpuThresholdSize, &etag2);

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                eb1.blockid(),
                eb1.generationstamp(),
                eb1.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                eb2.blockid(),
                eb2.generationstamp(),
                eb2.numbytes(),
                etag1,
                upload_id,
                pufs_name,
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                last.blockid(),
                last.generationstamp(),
                last.numbytes(),
                etag2,
                upload_id,
                pufs_name,
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          std::this_thread::sleep_for(std::chrono::seconds(1));
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == eb1.blockid()) {
                success1 = 1;
              } else if (c.necmd().block().blockid() == eb2.blockid()) {
                success2 = 1;
              } else if (c.necmd().block().blockid() == last.blockid()) {
                success3 = 1;
              } else {
                ASSERT_TRUE(false);
              }
              if (success1 == 1 && success2 == 1 && success3 == 1) {
                wait_evict_cmd = false;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(),
                  kLowerMpuThresholdSize + kLowerMpuThresholdSize +
                      kHigherMpuThresholdSize);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile9) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete a file with two blocks
    // After first block is persisted, try make RBW report
    // After file is persisted, try make RBW report
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCompleteTwoBlockCheckPreviousStatus");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }
      LOG(INFO) << "UT - AsyncCreateFile";

      ExtendedBlockProto eb1;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        eb1 = response.block().b();
        eb1.set_numbytes(kHigherMpuThresholdSize);
      }
      LOG(INFO) << "UT - AsyncAddBlock";

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            eb1.blockid(),
            eb1.generationstamp(),
            eb1.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }
      LOG(INFO) << "UT - IncrementalBlockReport";

      ExtendedBlockProto last;
      bool wait_upload_cmd = true;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_previous()->CopyFrom(eb1);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        last = response.block().b();
        last.set_numbytes(kHigherMpuThresholdSize);
      }
      LOG(INFO) << "UT - AsyncAddBlock";

      std::string upload_id1, pufs_name1;
      for (int i = 0; i < 10; i++) {
        {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              eb1.blockid(),
              eb1.generationstamp(),
              eb1.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);

        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
            if (c.uploadcmd().block().blockid() == eb1.blockid()) {
              ASSERT_EQ(c.uploadcmd().uploadtype(), cloudfs::datanode::UPLOAD);
              ASSERT_TRUE(mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                              .IsOK());
              upload_id1 = c.uploadcmd().uploadid();
              pufs_name1 = c.uploadcmd().blockpufsname();
              wait_upload_cmd = false;
            } else {
              ASSERT_TRUE(false);
            }
          }
        }
        if (!wait_upload_cmd) {
          break;
        }
      }
      ASSERT_FALSE(wait_upload_cmd);
      LOG(INFO) << "UT - wait_upload_cmd";

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }
      LOG(INFO) << "UT - IncrementalBlockReport";

      // DN upload success
      std::string etag1;
      mock_ufs->AddPartToObject(
          src, upload_id1, 1, kHigherMpuThresholdSize, &etag1);

      bool wait_evict_cmd = true;
      for (int i = 0; i < 10; i++) {
        {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              eb1.blockid(),
              eb1.generationstamp(),
              eb1.numbytes(),
              etag1,
              upload_id1,
              pufs_name1,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::NotifyEvictableCommand) {
            if (c.necmd().block().blockid() == eb1.blockid()) {
              wait_evict_cmd = false;
            } else {
              ASSERT_TRUE(false);
            }
          }
        }
        if (!wait_evict_cmd) {
          break;
        }
      }
      ASSERT_TRUE(wait_evict_cmd);
      LOG(INFO) << "UT - wait_evict_cmd";

      wait_evict_cmd = true;
      for (int i = 0; i < 10; i++) {
        {
          BlockManager::RepeatedIncBlockReport receiving_report;
          TestAccNamespace::MakeReport(
              eb1.blockid(),
              eb1.generationstamp(),
              eb1.numbytes(),
              "",  // etag
              "",  // upload_id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
              &receiving_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", receiving_report);
          ASSERT_TRUE(s.IsOK());
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::BlockCommand &&
              c.blkcmd().action() ==
                  cloudfs::datanode::BlockCommandProto_Action_INVALIDATE) {
            for (auto& b : c.blkcmd().blocks()) {
              if (b.blockid() == eb1.blockid()) {
                wait_evict_cmd = false;
              }
            }
          }
        }
        if (!wait_evict_cmd) {
          break;
        }
      }
      ASSERT_TRUE(wait_evict_cmd);
      LOG(INFO) << "UT - wait_evict_cmd";

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }
      LOG(INFO) << "UT - AsyncCompleteFile";

      std::string upload_id2, pufs_name2;
      wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                last.blockid(),
                last.generationstamp(),
                last.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          std::this_thread::sleep_for(std::chrono::seconds(1));
          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                upload_id2 = c.uploadcmd().uploadid();
                pufs_name2 = c.uploadcmd().blockpufsname();
                wait_upload_cmd = false;
              } else {
                ASSERT_TRUE(false);
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);
      LOG(INFO) << "UT - wait_upload_cmd";

      std::string etag2;
      mock_ufs->AddPartToObject(
          src, upload_id2, 2, kHigherMpuThresholdSize, &etag2);
      LOG(INFO) << "UT - AddPartToObject";

      wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                last.blockid(),
                last.generationstamp(),
                last.numbytes(),
                etag2,
                upload_id2,
                pufs_name2,
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          std::this_thread::sleep_for(std::chrono::seconds(1));
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == eb1.blockid()) {
                // Do nothing
              } else if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
              } else {
                ASSERT_TRUE(false);
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);
      LOG(INFO) << "UT - wait_evict_cmd";

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }
      LOG(INFO) << "UT - persisted";

      wait_evict_cmd = true;
      for (int i = 0; i < 10; i++) {
        {
          BlockManager::RepeatedIncBlockReport receiving_report;
          TestAccNamespace::MakeReport(
              eb1.blockid(),
              eb1.generationstamp(),
              eb1.numbytes(),
              "",  // etag
              "",  // upload_id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
              &receiving_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", receiving_report);
          ASSERT_TRUE(s.IsOK());
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::BlockCommand &&
              c.blkcmd().action() ==
                  cloudfs::datanode::BlockCommandProto_Action_INVALIDATE) {
            for (auto& b : c.blkcmd().blocks()) {
              if (b.blockid() == eb1.blockid()) {
                wait_evict_cmd = false;
              }
            }
          }
        }
        if (!wait_evict_cmd) {
          break;
        }
      }
      ASSERT_TRUE(wait_evict_cmd);
      LOG(INFO) << "UT - wait_evict_cmd";

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(),
                  kHigherMpuThresholdSize + kHigherMpuThresholdSize);
      }
      LOG(INFO) << "UT - AsyncGetFileInfo";
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFile10FastCreate) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete file conflict with ufs
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteFastCreate");
      uint64_t fileid = 0;
      ExtendedBlockProto last;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_withaddblock(true);
        request.set_rpc_type(RPC_BYTERPC_MODE);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
        last = response.block().b();
        last.set_numbytes(k31M);
      }

      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      std::string upload_id, pufs_name;
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == last.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                        .IsOK());
                upload_id = c.uploadcmd().uploadid();
                pufs_name = c.uploadcmd().blockpufsname();
                wait_upload_cmd = false;
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      // DN upload success
      std::string etag;
      mock_ufs->AddPartToObject(src, upload_id, 1, k31M, &etag);

      {
        bool wait_evict_cmd = true;
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport upload_success_report;
          TestAccNamespace::MakeReport(
              last.blockid(),
              last.generationstamp(),
              last.numbytes(),
              etag,
              upload_id,
              pufs_name,
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
              &upload_success_report);
          Status s = ns->block_manager_->IncrementalBlockReport(
              "datanode1", upload_success_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                   NotifyEvictableCommand) {
              if (c.necmd().block().blockid() == last.blockid()) {
                wait_evict_cmd = false;
                break;
              }
            }
          }
          if (!wait_evict_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_FALSE(wait_evict_cmd);
      }

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestDisableAppendFile) {
  bool default_ufs_support_append = FLAGS_ufs_support_append;
  FLAGS_ufs_support_append = false;
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    std::string src = ns->JoinUfsPathWithPrefix("/TestDisableAppendFile");
    uint64_t fileid = 0;
    {
      CreateRequestProto request = TestAccNamespace::GetCreateRequest();
      request.set_src(src);
      request.set_createflag(
          CreateFlag::SetAccAppendable(request.createflag()));
      CreateResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCreateFile(src,
                                   p,
                                   NetworkLocationInfo(),
                                   ugi,
                                   LogRpcInfo(),
                                   "",
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      fileid = response.fs().fileid();
    }
    {
      CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      CompleteResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCompleteFile(
          src, &request, &response, &ctrl, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
    }
    {
      AppendRequestProto request = TestAccNamespace::GetAppendRequest();
      request.set_src(src);
      AppendResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAppend(
          src, "", LogRpcInfo(), ugi, &request, &response, &rpc_done, info);
      rpc_done.Await();
      ASSERT_FALSE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      ASSERT_TRUE(rpc_done.status().HasException());
      ASSERT_EQ(rpc_done.status().exception(),
                JavaExceptions::Exception::kUnsupportedOperationException);
    }
    {
      std::string inner_path = "/TestDisableAppendFile";
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
  });
  FLAGS_ufs_support_append = default_ufs_support_append;
}

TEST_F(AccNamespaceTest, TestCompleteAppendFile) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete multi-block append object
    // 1. Open
    // 2. Add block b1
    // 3. Add block b2
    // 4. Apply upload b2 -> deny
    // 5. Apply upload b1 -> approve
    // 6. uploaded b1
    // 7. Apply evict b1 -> approve
    // 8. Apply upload b2 -> approve
    // 9. uploaded b2
    // 10. Apply evict b2 -> approve
    // 11. Persist file
    {
      std::string src = ns->JoinUfsPathWithPrefix("/TestCompleteAppendObject");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(
            CreateFlag::SetAccAppendable(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      ExtendedBlockProto b1;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        b1 = response.block().b();
        b1.set_numbytes(k31M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            b1.blockid(),
            b1.generationstamp(),
            b1.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      ExtendedBlockProto b2;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_previous()->CopyFrom(b1);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        b2 = response.block().b();
        b2.set_numbytes(kLowerMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            b2.blockid(),
            b2.generationstamp(),
            b2.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(b2);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      for (int i = 0; i < 10; i++) {
        {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              b2.blockid(),
              b2.generationstamp(),
              b2.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());
        }

        {
          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              ASSERT_NE(c.uploadcmd().block().blockid(), b2.blockid());
            }
          }
        }
      }

      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                b1.blockid(),
                b1.generationstamp(),
                b1.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
                if (c.uploadcmd().block().blockid() == b1.blockid()) {
                  ASSERT_EQ(c.uploadcmd().appendoffset(), 0);
                  wait_upload_cmd = false;
                  break;
                }
              }
            }
          }

          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
      mock_ufs->CreateObject(key, k31M);

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                b1.blockid(),
                b1.generationstamp(),
                b1.numbytes(),
                "etag",  // etag
                "",      // upload id
                "",      // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                     NotifyEvictableCommand) {
                if (c.necmd().block().blockid() == b1.blockid()) {
                  wait_evict_cmd = false;
                  break;
                }
              }
            }
            if (!wait_evict_cmd) {
              break;
            }
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport receiving_report;
            TestAccNamespace::MakeReport(
                b1.blockid(),
                b1.generationstamp(),
                b1.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING,
                &receiving_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", receiving_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() ==
                      cloudfs::datanode::DatanodeCommandProto::BlockCommand &&
                  c.blkcmd().action() ==
                      cloudfs::datanode::BlockCommandProto_Action_INVALIDATE) {
                for (auto& b : c.blkcmd().blocks()) {
                  if (b.blockid() == b1.blockid()) {
                    wait_evict_cmd = false;
                  }
                }
              }
            }
            if (!wait_evict_cmd) {
              break;
            }
          }
        }
      }
      ASSERT_TRUE(wait_evict_cmd);

      wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                b2.blockid(),
                b2.generationstamp(),
                b2.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
                if (c.uploadcmd().block().blockid() == b2.blockid()) {
                  ASSERT_EQ(c.uploadcmd().appendoffset(), k31M);
                  wait_upload_cmd = false;
                  break;
                }
              }
            }
          }

          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      mock_ufs->CreateObject(key, k31M + kLowerMpuThresholdSize);

      wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                b2.blockid(),
                b2.generationstamp(),
                b2.numbytes(),
                "etag",  // etag
                "",      // upload id
                "",      // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                     NotifyEvictableCommand) {
                if (c.necmd().block().blockid() == b2.blockid()) {
                  wait_evict_cmd = false;
                  break;
                }
              }
            }
            if (!wait_evict_cmd) {
              break;
            }
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M + kLowerMpuThresholdSize);
      }
    }

    // Test append to existing append object
    // 1. Open
    // 2. Add block
    // 3. Complete
    // 4. Append
    // 5. Add block
    // 6. Complete
    {
      std::string src =
          ns->JoinUfsPathWithPrefix("/TestCompleteAppendExistingObject");
      uint64_t fileid = 0;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_createflag(
            CreateFlag::SetAccAppendable(request.createflag()));
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
      }

      ExtendedBlockProto b1;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        b1 = response.block().b();
        b1.set_numbytes(k31M);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            b1.blockid(),
            b1.generationstamp(),
            b1.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(b1);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                b1.blockid(),
                b1.generationstamp(),
                b1.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
                if (c.uploadcmd().block().blockid() == b1.blockid()) {
                  ASSERT_EQ(c.uploadcmd().appendoffset(), 0);
                  wait_upload_cmd = false;
                  break;
                }
              }
            }
          }

          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      std::string key;
      mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
      mock_ufs->CreateObject(key, k31M);

      bool wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                b1.blockid(),
                b1.generationstamp(),
                b1.numbytes(),
                "etag",  // etag
                "",      // upload id
                "",      // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                     NotifyEvictableCommand) {
                if (c.necmd().block().blockid() == b1.blockid()) {
                  wait_evict_cmd = false;
                  break;
                }
              }
            }
            if (!wait_evict_cmd) {
              break;
            }
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }

      {
        AppendRequestProto request = TestAccNamespace::GetAppendRequest();
        request.set_src(src);
        AppendResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAppend(
            src, "", LogRpcInfo(), ugi, &request, &response, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_FALSE(response.has_block());
      }

      ExtendedBlockProto b2;
      {
        AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        AddBlockResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncAddBlock(src,
                                   NetworkLocationInfo(client_ip),
                                   LogRpcInfo(),
                                   ugi,
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        b2 = response.block().b();
        b2.set_numbytes(kLowerMpuThresholdSize);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            b2.blockid(),
            b2.generationstamp(),
            b2.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(b2);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport nego_report;
            TestAccNamespace::MakeReport(
                b2.blockid(),
                b2.generationstamp(),
                b2.numbytes(),
                "",  // etag
                "",  // upload id
                "",  // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_ID_NEGOED,
                &nego_report);
            Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                  nego_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() ==
                  cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
                if (c.uploadcmd().block().blockid() == b2.blockid()) {
                  ASSERT_EQ(c.uploadcmd().appendoffset(), k31M);
                  wait_upload_cmd = false;
                  break;
                }
              }
            }
          }

          if (!wait_upload_cmd) {
            break;
          }
        }
      }
      ASSERT_FALSE(wait_upload_cmd);

      mock_ufs->CreateObject(key, k31M + kLowerMpuThresholdSize);

      wait_evict_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          {
            BlockManager::RepeatedIncBlockReport upload_success_report;
            TestAccNamespace::MakeReport(
                b2.blockid(),
                b2.generationstamp(),
                b2.numbytes(),
                "etag",  // etag
                "",      // upload id
                "",      // pufs_name
                cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                    UPLOAD_SUCCEED,
                &upload_success_report);
            Status s = ns->block_manager_->IncrementalBlockReport(
                "datanode1", upload_success_report);
            ASSERT_TRUE(s.IsOK());
          }

          {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            cloudfs::datanode::HeartbeatResponseProto nego_cmds;
            ns->block_manager_->GetCommands(
                ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
                ns->ns_->blockpool_id(),
                0,
                &nego_cmds);
            for (auto& c : nego_cmds.cmds()) {
              if (c.cmdtype() == cloudfs::datanode::DatanodeCommandProto::
                                     NotifyEvictableCommand) {
                if (c.necmd().block().blockid() == b2.blockid()) {
                  wait_evict_cmd = false;
                  break;
                }
              }
            }
            if (!wait_evict_cmd) {
              break;
            }
          }
        }
      }
      ASSERT_FALSE(wait_evict_cmd);

      {
        bool persisted = false;
        for (int i = 0; i < 10; i++) {
          GetFileInfoResponseProto res;
          SynchronizedRpcClosure rpc_done;
          ns->acc_ns_->AsyncGetFileInfo(src,
                                        NetworkLocationInfo(),
                                        false,
                                        false,
                                        p,
                                        ugi,
                                        info,
                                        &res,
                                        &ctrl,
                                        &rpc_done);
          rpc_done.Await();
          ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
          persisted = res.fs().acc_file_status() ==
                      HdfsFileStatusProto_AccFileStatus::
                          HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
          if (persisted) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ASSERT_TRUE(persisted);
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M + kLowerMpuThresholdSize);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootEmpty) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      GetListingRequestProto req;
      GetListingResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetListing(
          NewListingOption(ns->JoinUfsPathWithPrefix("/")),
          NetworkLocationInfo(),
          req,
          &rsp,
          nullptr,
          &done);
      done.Await();
      auto s = done.status();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(rsp.dirlist().partiallisting_size(), 0);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingForFiles) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("obj1"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("obj1", 1024);

      auto cnt0 = mock_ufs->GetGetFileStatusCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  ns->JoinUfsPathWithPrefix("obj1"));
      }

      auto cnt1 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  ns->JoinUfsPathWithPrefix("obj1"));
      }

      auto cnt2 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestHttpGetListingForFiles) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("obj1"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("obj1", 1024);

      auto cnt0 = mock_ufs->GetGetFileStatusCount();

      {
        GetListingForHttpResponse rsp;
        auto s = ns->acc_ns_->GetListingForHttp(
            opt->path, "", 1000, false, true, opt->acc_fs_info, &rsp);
        ASSERT_TRUE(s.IsOK());
        ASSERT_FALSE(rsp.has_more);
        ASSERT_EQ(rsp.files.size(), 1);
        ASSERT_EQ(rsp.files[0].path(), ns->JoinUfsPathWithPrefix("obj1"));
      }

      auto cnt1 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingForHttpResponse rsp;
        auto s = ns->acc_ns_->GetListingForHttp(
            opt->path, "", 1000, false, true, opt->acc_fs_info, &rsp);
        ASSERT_TRUE(s.IsOK());
        ASSERT_FALSE(rsp.has_more);
        ASSERT_EQ(rsp.files.size(), 1);
        ASSERT_EQ(rsp.files[0].path(), ns->JoinUfsPathWithPrefix("obj1"));
      }

      auto cnt2 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingForManyVeryLongFiles) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    const std::string path_name_255 =
        "1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF123456"
        "7890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890AB"
        "CDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF12"
        "34567890ABCDEF1234567890ABCDEF1234567890ABCDE";
    ASSERT_EQ(path_name_255.length(), 255);

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix(""));
    opt->acc_fs_info.set_syncinterval(60);

    {
      for (int i = 0; i < 1001; i++) {
        ns->CreateObject(path_name_255 + std::to_string(i), 1024);
      }
      ns->CreateObject(path_name_255, 1024);

      auto cnt0 = mock_ufs->GetListFilesCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), path_name_255);
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 2, cnt1);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), path_name_255);
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingForDeepDir) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("d1/d2/d3"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("d1/d2/d3", 1024);

      auto cnt0 = mock_ufs->GetGetFileStatusCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  ns->JoinUfsPathWithPrefix("d1/d2/d3"));
      }

      auto cnt1 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  ns->JoinUfsPathWithPrefix("d1/d2/d3"));
      }

      auto cnt2 = mock_ufs->GetGetFileStatusCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootSimple) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("obj1", 1024);
      ns->CreateObject("obj2", 1024);
      ns->CreateObject("obj3", 1024);

      auto cnt0 = mock_ufs->GetListFilesCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 3);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "obj1");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "obj2");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "obj3");
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 3);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "obj1");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "obj2");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "obj3");
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestHttpGetListingRootSimple) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("obj1", 1024);
      ns->CreateObject("obj2", 1024);
      ns->CreateObject("obj3", 1024);

      auto cnt0 = mock_ufs->GetListFilesCount();

      {
        GetListingForHttpResponse rsp;
        auto s = ns->acc_ns_->GetListingForHttp(
            opt->path, "", 1000, false, true, opt->acc_fs_info, &rsp);
        ASSERT_TRUE(s.IsOK());
        ASSERT_FALSE(rsp.has_more);
        ASSERT_TRUE(rsp.files.empty());
      }

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 3);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "obj1");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "obj2");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "obj3");
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingForHttpResponse rsp;
        auto s = ns->acc_ns_->GetListingForHttp(
            opt->path, "", 1000, false, true, opt->acc_fs_info, &rsp);
        ASSERT_TRUE(s.IsOK());
        ASSERT_FALSE(rsp.has_more);
        ASSERT_EQ(rsp.files.size(), 3);
        ASSERT_EQ(rsp.files[0].path(), "obj1");
        ASSERT_EQ(rsp.files[1].path(), "obj2");
        ASSERT_EQ(rsp.files[2].path(), "obj3");
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

// 测试在 UFS 中出现同名的 Object 和 Dir Object 场景
// GetFileInfo 场景
TEST_F(AccNamespaceTest, TestObjectAndDirObjectConflictGetFileInfo) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string prefix = "somedir";

    ns->CreateObject(prefix, 1024);
    ns->CreateObject(prefix + "/", 1024);
    ns->CreateObject(prefix + "/" + "someobj", 1024);

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(
        &permission, &ugi, &info, 0 /* Always sync */);

    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(prefix),
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    nullptr,
                                    &done);
      done.Await();

      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.fs().filetype(),
                cloudfs::HdfsFileStatusProto_FileType_IS_FILE);

      INode node;
      auto s = ns->ns_->GetINodeByPath("/" + prefix, &node);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(node.type(), INode_Type_kFile);
      ASSERT_TRUE(node.has_ufs_file_info());
    }
    {
      GetListingRequestProto req;
      GetListingResponseProto rsp;
      SynchronizedRpcClosure done;
      auto list_opt = NewListingOption(ns->JoinUfsPathWithPrefix(prefix));
      ns->acc_ns_->AsyncGetListing(
          list_opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);

      INode node;
      auto s = ns->ns_->GetINodeByPath("/" + prefix, &node);
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(node.type(), INode_Type_kFile);
      ASSERT_TRUE(node.has_ufs_file_info());
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootFileDeletedInUFS) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("obj1", 1024);
      ns->CreateObject("obj2", 1024);
      ns->CreateObject("obj3", 1024);

      auto cnt0 = mock_ufs->GetListFilesCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 3);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "obj1");
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), 1024);
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "obj2");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).length(), 1024);
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "obj3");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).length(), 1024);
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      ns->DeleteObject("obj3");
      opt->acc_fs_info.set_syncinterval(0);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 2);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "obj1");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "obj2");
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1 + 1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootLargeDirWithPaging) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);

    std::string obj_prefix = "obj";
    {
      for (int i = 1; i <= 1001; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      auto cnt0 = mock_ufs->GetListFilesCount();

      std::string last;
      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix, 1));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), 1024);
        ASSERT_EQ(rsp.dirlist().partiallisting(499).path(),
                  MakeObjKey(obj_prefix, 500));
        ASSERT_EQ(rsp.dirlist().partiallisting(499).length(), 1024);

        last = MakeObjKey(obj_prefix, 500);
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 2, cnt1);

      {
        GetListingRequestProto req;
        req.set_startafter(last.c_str());
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix, 501));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), 1024);
        ASSERT_EQ(rsp.dirlist().partiallisting(499).path(),
                  MakeObjKey(obj_prefix, 1000));
        ASSERT_EQ(rsp.dirlist().partiallisting(499).length(), 1024);

        last = MakeObjKey(obj_prefix, 1000);
      }

      {
        GetListingRequestProto req;
        req.set_startafter(last.c_str());
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix, 1001));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), 1024);
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootLargeFileDirWithPaging1) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt1 = NewListingOption(ns->JoinUfsPathWithPrefix("/dir1"));
    opt1->acc_fs_info.set_syncinterval(0);
    std::string obj_prefix_1 = "obj";
    {
      const uint64_t file_size_1T = 1ULL * 1024 * 1024 * 1024 * 1024;
      for (int i = 1; i <= 1001; ++i) {
        ns->CreateObject(MakeObjKey("dir1/" + obj_prefix_1, i), file_size_1T);
      }

      auto cnt0 = mock_ufs->GetListFilesCount();

      std::string last;
      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt1, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix_1, 1));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), file_size_1T);
        ASSERT_EQ(rsp.dirlist().partiallisting(499).path(),
                  MakeObjKey(obj_prefix_1, 500));
        ASSERT_EQ(rsp.dirlist().partiallisting(499).length(), file_size_1T);

        last = MakeObjKey(obj_prefix_1, 500);
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 2, cnt1);

      {
        GetListingRequestProto req;
        req.set_startafter(last.c_str());
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt1, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix_1, 501));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), file_size_1T);
        ASSERT_EQ(rsp.dirlist().partiallisting(499).path(),
                  MakeObjKey(obj_prefix_1, 1000));
        ASSERT_EQ(rsp.dirlist().partiallisting(499).length(), file_size_1T);

        last = MakeObjKey(obj_prefix_1, 1000);
      }

      {
        GetListingRequestProto req;
        req.set_startafter(last.c_str());
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt1, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix_1, 1001));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), file_size_1T);
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1 + 4, cnt2);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootLargeFileDirWithPaging2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt2 = NewListingOption(ns->JoinUfsPathWithPrefix("/dir2"));
    opt2->acc_fs_info.set_syncinterval(0);
    std::string obj_prefix_2 = "obj";
    {
      const uint64_t file_size_48T = 48ULL * 1024 * 1024 * 1024 * 1024;
      for (int i = 1; i <= 101; ++i) {
        ns->CreateObject(MakeObjKey("dir2/" + obj_prefix_2, i), file_size_48T);
      }

      auto cnt0 = mock_ufs->GetListFilesCount();

      std::string last;
      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt2, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 101);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(),
                  MakeObjKey(obj_prefix_2, 1));
        ASSERT_EQ(rsp.dirlist().partiallisting(0).length(), file_size_48T);
        ASSERT_EQ(rsp.dirlist().partiallisting(100).path(),
                  MakeObjKey(obj_prefix_2, 101));
        ASSERT_EQ(rsp.dirlist().remainingentries(), 0);
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 1, cnt1);
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootLargeDirLimit) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);
    opt->acc_max_count = 1500;

    std::string obj_prefix = "obj";
    {
      for (int i = 1; i <= 2346; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_EQ(s.code(), Code::kUfsSyncLimitExceeded);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingRootLargeDirDedup) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(0);

    std::string obj_prefix = "obj";
    {
      for (int i = 1; i <= 999; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      auto cnt = mock_ufs->GetListFilesCount();
      {
        GetListingRequestProto req1;
        GetListingResponseProto rsp1;
        SynchronizedRpcClosure done1;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req1, &rsp1, nullptr, &done1);

        GetListingRequestProto req2;
        GetListingResponseProto rsp2;
        SynchronizedRpcClosure done2;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req2, &rsp2, nullptr, &done2);

        done1.Await();
        done2.Await();
        ASSERT_EQ(rsp1.dirlist().partiallisting_size(), 500);
        ASSERT_EQ(rsp2.dirlist().partiallisting_size(), 500);
        ASSERT_TRUE(done1.status().IsOK()) << done1.status().ToString();
        ASSERT_TRUE(done2.status().IsOK()) << done2.status().ToString();

        auto cnt2 = mock_ufs->GetListFilesCount();
        ASSERT_EQ(cnt + 1, cnt2);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestDeleteFile) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string filename = "obj1";
      ns->CreateObject(filename, 1024);

      std::string inner_path = "/" + filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s =
          mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(filename), &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());
    }
    // Delete not existed file return OK
    {
      std::string inner_path = "/not_existed";
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
  });
}

TEST_F(AccNamespaceTest, TestDeleteDirectory) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Cannot delete non-empty dir if recursive == false
    {
      std::string dir = "dir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);
      opt->recursive = false;

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_FALSE(done.status().IsOK()) << done.status().ToString();
    }
    {
      std::string dir = "dir2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);
      opt->recursive = true;

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s =
          mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(obj_key), &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());
    }
  });
}

TEST_F(AccNamespaceTest, TestDeleteDirectoryWithCountLimit) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Bad case
    {
      std::string dir = "dir1";
      std::string obj_prefix = dir + "/obj-";
      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);
      opt->recursive = true;
      opt->acc_max_count = 1233;

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_EQ(done.status().code(), Code::kUfsTooManyFiles);
    }
    // Happy case
    {
      std::string dir = "dir2";
      std::string obj_prefix = dir + "/obj-";
      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);
      opt->recursive = true;
      opt->acc_max_count = 0;

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    // Happy case
    {
      std::string dir = "dir2";
      std::string obj_prefix = dir + "/obj-";
      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      auto opt = NewDeleteOption(ufs_path, inner_path);
      opt->recursive = true;
      opt->acc_max_count = 1235;

      cloudfs::DeleteResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
  });
}

// Not recursive delete
// Test dir's has_children logic
// Sync Interval: -1
TEST_F(AccNamespaceTest, TestDeleteDirectoryPosixRMDIR_DeleteSuccess_1) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string dir = "dir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string dir_inner_path = "/" + dir;
      std::string dir_ufs_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
      std::string obj1_inner_path = "/" + obj_key;
      std::string obj1_ufs_path = ns->JoinUfsPathWithPrefix(obj1_inner_path);

      // ls /dir1
      {
        auto opt = NewListingOption(dir_ufs_path);
        opt->acc_fs_info.set_syncinterval(-1);
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      }

      // Create /dir1/obj2
      std::string obj_key2 = dir + "/obj2";
      ns->CreateObject(obj_key2, 1024);
      std::string obj2_inner_path = "/" + obj_key2;
      std::string obj2_ufs_path = ns->JoinUfsPathWithPrefix(obj2_inner_path);

      // Delete /dir1/obj1
      {
        auto opt = NewDeleteOption(obj1_ufs_path, obj1_inner_path);
        opt->acc_fs_info.set_syncinterval(-1);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
      {
        auto opt = NewDeleteOption(dir_ufs_path, dir_inner_path);
        opt->acc_fs_info.set_syncinterval(-1);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
    }
  });
}

// Sync Interval: 10s
TEST_F(AccNamespaceTest, TestDeleteDirectoryPosixRMDIR_DeleteSuccess_2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string dir = "dir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string dir_inner_path = "/" + dir;
      std::string dir_ufs_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
      std::string obj1_inner_path = "/" + obj_key;
      std::string obj1_ufs_path = ns->JoinUfsPathWithPrefix(obj1_inner_path);

      // ls /dir1
      {
        auto opt = NewListingOption(dir_ufs_path);
        opt->acc_fs_info.set_syncinterval(10);
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      }

      // Create /dir1/obj2
      std::string obj_key2 = dir + "/obj2";
      ns->CreateObject(obj_key2, 1024);
      std::string obj2_inner_path = "/" + obj_key2;
      std::string obj2_ufs_path = ns->JoinUfsPathWithPrefix(obj2_inner_path);

      // Delete /dir1/obj1
      {
        auto opt = NewDeleteOption(obj1_ufs_path, obj1_inner_path);
        opt->acc_fs_info.set_syncinterval(10);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
      {
        auto opt = NewDeleteOption(dir_ufs_path, dir_inner_path);
        opt->acc_fs_info.set_syncinterval(10);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
    }
  });
}

// Sync Interval: 3s
TEST_F(AccNamespaceTest, TestDeleteDirectoryPosixRMDIR_DeleteFailure_1) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string dir = "dir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string dir_inner_path = "/" + dir;
      std::string dir_ufs_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
      std::string obj1_inner_path = "/" + obj_key;
      std::string obj1_ufs_path = ns->JoinUfsPathWithPrefix(obj1_inner_path);

      // ls /dir1
      {
        auto opt = NewListingOption(dir_ufs_path);
        opt->acc_fs_info.set_syncinterval(3);
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      }

      // Create /dir1/obj2
      std::string obj_key2 = dir + "/obj2";
      ns->CreateObject(obj_key2, 1024);
      std::string obj2_inner_path = "/" + obj_key2;
      std::string obj2_ufs_path = ns->JoinUfsPathWithPrefix(obj2_inner_path);

      std::this_thread::sleep_for(std::chrono::seconds(4));

      // Delete /dir1/obj1
      {
        auto opt = NewDeleteOption(obj1_ufs_path, obj1_inner_path);
        opt->acc_fs_info.set_syncinterval(3);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
      {
        auto opt = NewDeleteOption(dir_ufs_path, dir_inner_path);
        opt->acc_fs_info.set_syncinterval(3);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_FALSE(done.status().IsOK()) << done.status().ToString();
      }
    }
  });
}
// Sync Interval: 0
TEST_F(AccNamespaceTest, TestDeleteDirectoryPosixRMDIR_DeleteFailure_2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string dir = "dir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string dir_inner_path = "/" + dir;
      std::string dir_ufs_path = ns->JoinUfsPathWithPrefix(dir_inner_path);
      std::string obj1_inner_path = "/" + obj_key;
      std::string obj1_ufs_path = ns->JoinUfsPathWithPrefix(obj1_inner_path);

      // ls /dir1
      {
        auto opt = NewListingOption(dir_ufs_path);
        opt->acc_fs_info.set_syncinterval(3);
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 1);
      }

      // Create /dir1/obj2
      std::string obj_key2 = dir + "/obj2";
      ns->CreateObject(obj_key2, 1024);
      std::string obj2_inner_path = "/" + obj_key2;
      std::string obj2_ufs_path = ns->JoinUfsPathWithPrefix(obj2_inner_path);

      std::this_thread::sleep_for(std::chrono::seconds(4));

      // Delete /dir1/obj1
      {
        auto opt = NewDeleteOption(obj1_ufs_path, obj1_inner_path);
        opt->acc_fs_info.set_syncinterval(3);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
      {
        auto opt = NewDeleteOption(dir_ufs_path, dir_inner_path);
        opt->acc_fs_info.set_syncinterval(3);
        opt->recursive = false;

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_FALSE(done.status().IsOK()) << done.status().ToString();
      }
    }
  });
}

// 针对 FUSE 的 "rm -r" 操作，会被 FUSE 翻译后序遍历的递归删除
// 在 TOS UFS 上，删除掉 file ，如果 ancestors 没有其他子文件，那么 ancestors
// 也会被自动删除 确保后续删除 ancestors 的请求不会报错
TEST_F(AccNamespaceTest, TestDeletePosixRm) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    {
      std::string dir = "dir1/subdir1";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      {
        std::string inner_path = "/" + obj_key;
        std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
        auto opt = NewDeleteOption(ufs_path, inner_path);

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

        UfsFileStatus file;
        auto file_s =
            mock_ufs->GetFileStatus(ns->JoinUfsPathWithPrefix(obj_key), &file);
        ASSERT_EQ(Code::kFileNotFound, file_s.code());
      }
      {
        std::string inner_path = "/" + dir;
        std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
        auto opt = NewDeleteOption(ufs_path, inner_path);

        cloudfs::DeleteResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncDelete(opt, &rsp, &done);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestRenameFile) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      std::string filename = "obj1";
      ns->CreateObject(filename, 1024);

      std::string inner_path = "/" + filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/obj2";
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
    }
  });

  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    mock_ufs->SetSupportRename(true);

    {
      std::string filename = "obj1";
      ns->CreateObject(filename, 1024);

      std::string inner_path = "/" + filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/obj3";
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
    }

    mock_ufs->SetSupportRename(false);
  });
}

TEST_F(AccNamespaceTest, TestRenameConfict) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Test overwrite false case
    {
      std::string src_filename = "obj1";
      ns->CreateObject(src_filename, 1024);
      std::string dst_dir = "dir1/";
      ns->CreateObject(dst_dir, 1024);

      dst_dir = "dir1";

      std::string inner_path = "/" + src_filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kError, done.status().code());
    }

    {
      std::string src_dir = "dir2/";
      ns->CreateObject(src_dir, 1024);
      std::string dst_file = "obj2/";
      ns->CreateObject(dst_file, 1024);
      dst_file = "obj2";

      std::string inner_path = "/" + src_dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_file;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kError, done.status().code());
    }
  });
}

TEST_F(AccNamespaceTest, TestRenameFileOverwrite) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Test overwrite false case
    {
      std::string src_filename = "obj1";
      ns->CreateObject(src_filename, 1024);
      std::string dst_filename = "obj2";
      ns->CreateObject(dst_filename, 1024);

      std::string inner_path = "/" + src_filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_filename;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileExists, done.status().code());
    }

    // Test overwrite success case
    {
      std::string src_filename = "obj1";
      ns->CreateObject(src_filename, 1024);
      std::string dst_filename = "obj2";
      ns->CreateObject(dst_filename, 1024);

      std::string inner_path = "/" + src_filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_filename;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
    }
  });

  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    mock_ufs->SetSupportRename(true);

    // Test overwrite false case
    {
      std::string src_filename = "obj1";
      ns->CreateObject(src_filename, 1024);
      std::string dst_filename = "obj3";
      ns->CreateObject(dst_filename, 1024);

      std::string inner_path = "/" + src_filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_filename;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileExists, done.status().code());
    }

    // Test overwrite success case
    {
      std::string src_filename = "obj1";
      ns->CreateObject(src_filename, 1024);
      std::string dst_filename = "obj3";
      ns->CreateObject(dst_filename, 1024);

      std::string inner_path = "/" + src_filename;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_filename;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
    }

    mock_ufs->SetSupportRename(false);
  });
}

TEST_F(AccNamespaceTest, TestRenameDirectorySimple) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Simple rename
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir1-2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);
    }
    // Simple rename, many files
    {
      std::string dir = "dir2";
      std::string dst_dir = "dir2-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(NewListingOption(dst_ufs_path),
                                     NetworkLocationInfo(),
                                     req,
                                     &rsp,
                                     nullptr,
                                     &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestRenameDirectorySimple2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    mock_ufs->SetSupportRename(true);

    // Simple rename
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir3";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);
    }
    // Simple rename, many files
    {
      std::string dir = "dir2";
      std::string dst_dir = "dir4";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(NewListingOption(dst_ufs_path),
                                     NetworkLocationInfo(),
                                     req,
                                     &rsp,
                                     nullptr,
                                     &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
      }
    }

    mock_ufs->SetSupportRename(false);
  });
}

TEST_F(AccNamespaceTest, TestRenameDirectoryWithMaxCountOrSize) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Rename, bad case, files limit violation
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir1-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->acc_max_count = 1233;
      opt->acc_max_size = 0;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(done.status().code(), Code::kUfsTooManyFiles);
    }
    // Rename, bad case, files size violation
    {
      std::string dir = "dir2";
      std::string dst_dir = "dir2-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->acc_max_count = 0;
      opt->acc_max_size = 1048577;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(done.status().code(), Code::kUfsTooManyFiles);
    }
    // Rename, bad case, both files num and size violation
    {
      std::string dir = "dir3";
      std::string dst_dir = "dir3-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->acc_max_count = 1233;
      opt->acc_max_size = 1048577;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(done.status().code(), Code::kUfsTooManyFiles);
    }
    // Rename, happy case with num and size limit
    {
      std::string dir = "dir4";
      std::string dst_dir = "dir4-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->acc_max_count = 1235;
      opt->acc_max_size = 1024 * 1234 + 1;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(NewListingOption(dst_ufs_path),
                                     NetworkLocationInfo(),
                                     req,
                                     &rsp,
                                     nullptr,
                                     &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
      }
    }
    // Rename, happy case with num and size limit
    {
      std::string dir = "dir5";
      std::string dst_dir = "dir5-2";
      std::string obj_prefix = dir + "/obj-";

      for (int i = 1; i <= 1234; ++i) {
        ns->CreateObject(MakeObjKey(obj_prefix, i), 1024);
      }

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->acc_max_count = 0;
      opt->acc_max_size = 0;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      UfsFileStatus file;
      auto file_s = mock_ufs->GetFileStatus(ufs_path, &file);
      ASSERT_EQ(Code::kFileNotFound, file_s.code());

      file_s = mock_ufs->GetFileStatus(dst_ufs_path, &file);
      ASSERT_EQ(Code::kOK, file_s.code());
      ASSERT_EQ(file.FileType(), UFS_DIR);
      ASSERT_EQ(file.FileName(), dst_dir);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(NewListingOption(dst_ufs_path),
                                     NetworkLocationInfo(),
                                     req,
                                     &rsp,
                                     nullptr,
                                     &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 500);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestRenameDirectoryOverwrite) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    // Test overwrite false case
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir1-2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);
      std::string dst_obj_key = dst_dir + "/obj1";
      ns->CreateObject(dst_obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileExists, done.status().code());
    }
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir1-2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);
      std::string dst_obj_key = dst_dir + "/obj1";
      ns->CreateObject(dst_obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileStatusError, done.status().code());
    }
  });
}

TEST_F(AccNamespaceTest, TestRenameDirectoryOverwrite2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    mock_ufs->SetSupportRename(true);

    // Test overwrite false case
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);
      std::string dst_obj_key = dst_dir + "/obj1";
      ns->CreateObject(dst_obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = false;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileExists, done.status().code());
    }
    {
      std::string dir = "dir1";
      std::string dst_dir = "dir2";
      std::string obj_key = dir + "/obj1";
      ns->CreateObject(obj_key, 1024);
      std::string dst_obj_key = dst_dir + "/obj1";
      ns->CreateObject(dst_obj_key, 1024);

      std::string inner_path = "/" + dir;
      std::string ufs_path = ns->JoinUfsPathWithPrefix(inner_path);
      std::string dst_inner_path = "/" + dst_dir;
      std::string dst_ufs_path = ns->JoinUfsPathWithPrefix(dst_inner_path);
      auto opt =
          NewRenameOption(ufs_path, inner_path, dst_ufs_path, dst_inner_path);
      opt->overwrite = true;

      cloudfs::Rename2ResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
      done.Await();
      ASSERT_EQ(Code::kFileStatusError, done.status().code());
    }

    mock_ufs->SetSupportRename(false);
  });
}

// Sync 单个文件的时候，Ancestors 路径上有同名的 Object
// 1. UFS 上，Object 的 parent 目录存在同名 Object
// 2. 先 sync Object 成功
// 3. 再 sync Parent，因为 local 的 Parent 已经是 dir 了，会忽略掉 Parent Object
TEST_F(AccNamespaceTest, TestSyncUfsFileWithAncestorsObjectConflict1) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      // obj1 的 parent 目录存在同名的 Object
      std::string obj1_key = "dir1/obj";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(obj1_key), 1024)
              .IsOK());

      std::string dir1_key = "dir1";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(dir1_key), 1024)
              .IsOK());

      PermissionStatus permission;
      UserGroupInfo ugi;
      cloudfs::AccFsInfo info;
      TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
      info.set_syncinterval(0);

      {
        // 直接 sync obj1， 因为 dir1 还没有 sync，obj1 sync 成功
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(obj1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
        ASSERT_EQ(rsp.fs().filetype(),
                  cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
        ASSERT_EQ(rsp.fs().length(), 1024);
      }
      {
        // 接着 sync dir1， dir1 在本地是 DIR，忽略 UFS 上同名的 Object
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(dir1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
        ASSERT_EQ(rsp.fs().filetype(),
                  cloudfs::HdfsFileStatusProto_FileType_IS_DIR);
      }
    }
  });
}

// Sync 单个文件的时候，Ancestors 路径上有同名的 Object
// 1. UFS 上，Object 的 parent 目录存在同名 Object
// 2. 先 sync parent, 存在同名场景下，优先选择 object，这时候 /dir1 sync
// 下来会是 file 类型
// 3. 再 sync Object, 发现 Parent 类型冲突，sync 失败
TEST_F(AccNamespaceTest, TestSyncUfsFileWithAncestorsObjectConflict2) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      // obj1 的 parent 目录存在同名的 Object
      std::string obj1_key = "dir1/obj";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(obj1_key), 1024)
              .IsOK());

      std::string dir1_key = "dir1";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(dir1_key), 1024)
              .IsOK());

      PermissionStatus permission;
      UserGroupInfo ugi;
      cloudfs::AccFsInfo info;
      TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
      info.set_syncinterval(0);

      {
        // 先 sync dir1， UFS 上同名的 Object, sync 完成后的状态是 file
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(dir1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
        ASSERT_EQ(rsp.fs().filetype(),
                  cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      }
      {
        // sync obj1， 因为 dir1 类型是 file, 所以会失败
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(obj1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_EQ(done.status().code(), Code::kFileNotFound);
        ASSERT_EQ(done.status().exception(),
                  JavaExceptions::Exception::kParentNotDirectoryException);
      }
    }
  });
}

// Sync 单个文件的时候，Ancestors 路径上有同名的 Object
// 1. UFS 上，Object 的 parent 目录存在同名 Object
// 2. 先 sync parent, 存在同名场景下，优先选择 object，这时候 /dir1 sync
// 下来会是 file 类型
// 3. 删除 UFS 上的 parent object，这时候 local 还有残留的 file inode
// 4. 再 sync Object, 发现 Parent 类型冲突，sync 失败
TEST_F(AccNamespaceTest, TestSyncUfsFileWithAncestorsObjectConflict3) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    {
      // obj1 的 parent 目录存在同名的 Object
      std::string obj1_key = "dir1/obj";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(obj1_key), 1024)
              .IsOK());

      std::string dir1_key = "dir1";
      ASSERT_TRUE(
          mock_ufs->CreateObject(ns->JoinObjectKeyWithPrefix(dir1_key), 1024)
              .IsOK());

      PermissionStatus permission;
      UserGroupInfo ugi;
      cloudfs::AccFsInfo info;
      TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
      info.set_syncinterval(0);

      {
        // 先 sync dir1， UFS 上同名的 Object, sync 完成后的状态是 file
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(dir1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
        ASSERT_EQ(rsp.fs().filetype(),
                  cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
      }
      {
        // Delete dir1
        std::string dir1_key = "dir1";
        ASSERT_TRUE(
            mock_ufs->DeleteObject(ns->JoinObjectKeyWithPrefix(dir1_key))
                .IsOK());
      }
      {
        // sync obj1， 因为 dir1 类型是 file, 所以会失败
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(ns->JoinUfsPathWithPrefix(obj1_key),
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      permission,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
        ASSERT_EQ(rsp.fs().filetype(),
                  cloudfs::HdfsFileStatusProto_FileType_IS_FILE);
        ASSERT_EQ(rsp.fs().length(), 1024);
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestGetListingByteWiseOrder) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());

    auto opt = NewListingOption(ns->JoinUfsPathWithPrefix("/"));
    opt->acc_fs_info.set_syncinterval(60);

    {
      ns->CreateObject("abc", 1024);
      ns->CreateObject("data.bin", 1024);
      ns->CreateObject("data.bin.xxx", 1024);
      ns->CreateObject("xyz", 1024);

      auto cnt0 = mock_ufs->GetListFilesCount();

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 4);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "abc");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "data.bin");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "data.bin.xxx");
        ASSERT_EQ(rsp.dirlist().partiallisting(3).path(), "xyz");
      }

      auto cnt1 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt0 + 1, cnt1);

      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 4);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "abc");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "data.bin");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "data.bin.xxx");
        ASSERT_EQ(rsp.dirlist().partiallisting(3).path(), "xyz");
      }

      auto cnt2 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt1, cnt2);

      ns->DeleteObject("data.bin");
      ns->CreateObject("data.bin", 1024);
      opt->acc_fs_info.set_syncinterval(0);
      {
        GetListingRequestProto req;
        GetListingResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetListing(
            opt, NetworkLocationInfo(), req, &rsp, nullptr, &done);
        done.Await();
        auto s = done.status();
        ASSERT_TRUE(s.IsOK());
        ASSERT_EQ(rsp.dirlist().partiallisting_size(), 4);
        ASSERT_EQ(rsp.dirlist().partiallisting(0).path(), "abc");
        ASSERT_EQ(rsp.dirlist().partiallisting(1).path(), "data.bin");
        ASSERT_EQ(rsp.dirlist().partiallisting(2).path(), "data.bin.xxx");
        ASSERT_EQ(rsp.dirlist().partiallisting(3).path(), "xyz");
      }

      auto cnt3 = mock_ufs->GetListFilesCount();
      ASSERT_EQ(cnt2 + 1, cnt3);
    }
  });
}

TEST_F(AccNamespaceTest, TestCopyNonPersistFile) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    std::string dir1_path("/TestCopyNonPersistFile/dir1");
    std::string dir1_ufs_path = ns->JoinUfsPathWithPrefix(dir1_path);
    std::string file1_path = dir1_path + "/file1";
    std::string file1_ufs_path = ns->JoinUfsPathWithPrefix(file1_path);
    std::string file2_path = dir1_path + "/file2";
    std::string file2_ufs_path = ns->JoinUfsPathWithPrefix(file2_path);

    // mkdir
    {
      MkdirsRequestProto request;
      request.set_src(dir1_ufs_path);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(true);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          dir1_ufs_path, p, ugi, true, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      UfsDirStatus dir;
      ASSERT_TRUE(mock_ufs->GetDirectoryStatus(dir1_ufs_path, &dir).IsOK());
    }

    {
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(file1_ufs_path);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(file1_ufs_path,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        auto opt = NewRenameOption(
            file1_ufs_path, file1_path, file2_ufs_path, file2_path);

        cloudfs::Rename2ResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(file1_ufs_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_FALSE(rsp.has_fs());
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(file2_ufs_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestCopyDirContainsNonPersistFile) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    std::string dir1_path("/TestCopyNonPersistFile/dir1");
    std::string dir1_ufs_path = ns->JoinUfsPathWithPrefix(dir1_path);
    std::string dir2_path("/TestCopyNonPersistFile/dir2");
    std::string dir2_ufs_path = ns->JoinUfsPathWithPrefix(dir2_path);
    std::string file1_path = dir1_path + "/file";
    std::string file1_ufs_path = ns->JoinUfsPathWithPrefix(file1_path);
    std::string file2_path = dir2_path + "/file";
    std::string file2_ufs_path = ns->JoinUfsPathWithPrefix(file2_path);

    // mkdir
    {
      MkdirsRequestProto request;
      request.set_src(dir1_ufs_path);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(true);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          dir1_ufs_path, p, ugi, true, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      UfsDirStatus dir;
      ASSERT_TRUE(mock_ufs->GetDirectoryStatus(dir1_ufs_path, &dir).IsOK());
    }

    {
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(file1_ufs_path);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(file1_ufs_path,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        auto opt =
            NewRenameOption(dir1_ufs_path, dir1_path, dir2_ufs_path, dir2_path);

        cloudfs::Rename2ResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncRenameTo2(opt, &done, &rsp);
        done.Await();
        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(file1_ufs_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_FALSE(rsp.has_fs());
      }
      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure done;
        ns->acc_ns_->AsyncGetFileInfo(file2_ufs_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      nullptr,
                                      &done);
        done.Await();

        ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
        ASSERT_TRUE(rsp.has_fs());
      }
    }
  });
}

TEST_F(AccNamespaceTest, TestUfsDirSyncActions) {
  {
    UfsDirSyncActions actions;
    ASSERT_EQ(actions.CheckValid(), true);
  }
  {
    UfsDirSyncActions actions;

    for (int i = 0; i < 3; ++i) {
      UfsFileStatus file;
      Status s = UfsFileStatus::Builder()
                     .SetFullPath("/dir/" + std::to_string(i))
                     .SetFileType(UFS_DIR)
                     .Build(&file);
      ASSERT_TRUE(s.IsOK());
      actions.files_to_add.emplace_back(std::move(file));
    }
    for (int i = 3; i < 6; ++i) {
      INode file;
      file.set_name(std::to_string(i));
      actions.files_to_check.emplace_back(std::move(file));
    }

    ASSERT_EQ(actions.CheckValid(), true);
  }
  {
    UfsDirSyncActions actions;

    for (int i = 0; i < 3; ++i) {
      UfsFileStatus file;
      Status s = UfsFileStatus::Builder()
                     .SetFullPath("/dir/" + std::to_string(i))
                     .SetFileType(UFS_DIR)
                     .Build(&file);
      ASSERT_TRUE(s.IsOK());
      actions.files_to_add.emplace_back(std::move(file));
    }
    for (int i = 2; i < 6; ++i) {
      INode file;
      file.set_name(std::to_string(i));
      actions.files_to_check.emplace_back(std::move(file));
    }

    ASSERT_EQ(actions.CheckValid(), false);
  }
}

TEST_F(AccNamespaceTest, TestTriggerBlockUpload) {
  FLAGS_nn_drive_upload = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    cnetpp::base::IPAddress client_ip("***********");

    const int k1M = 1000000;

    std::string src = ns->JoinUfsPathWithPrefix("/TestTriggerBlockUpload");
    uint64_t fileid = 0;
    {
      CreateRequestProto request = TestAccNamespace::GetCreateRequest();
      request.set_src(src);
      request.set_createflag(CreateFlag::SetAccAsync(request.createflag()));
      CreateResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCreateFile(src,
                                   p,
                                   NetworkLocationInfo(),
                                   ugi,
                                   LogRpcInfo(),
                                   "",
                                   &request,
                                   &response,
                                   &ctrl,
                                   &rpc_done,
                                   info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      fileid = response.fs().fileid();
    }

    ExtendedBlockProto last;
    {
      AddBlockRequestProto request = TestAccNamespace::GetAddBlockRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      AddBlockResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncAddBlock(src,
                                 NetworkLocationInfo(client_ip),
                                 LogRpcInfo(),
                                 ugi,
                                 &request,
                                 &response,
                                 &ctrl,
                                 &rpc_done,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      last = response.block().b();
      last.set_numbytes(k1M);
    }

    {
      BlockManager::RepeatedIncBlockReport received_report;
      TestAccNamespace::MakeReport(
          last.blockid(),
          last.generationstamp(),
          last.numbytes(),
          "",  // etag
          "",  // upload id
          "",  // pufs_name
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &received_report);
      Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                            received_report);
      ASSERT_TRUE(s.IsOK());
    }

    {
      CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
      request.set_src(src);
      request.set_fileid(fileid);
      request.mutable_last()->CopyFrom(last);
      CompleteResponseProto response;
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncCompleteFile(
          src, &request, &response, &ctrl, &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
    }

    std::string upload_id, pufs_name;
    bool wait_upload_cmd = true;
    {
      for (int i = 0; i < 10; i++) {
        cloudfs::datanode::HeartbeatResponseProto hb_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &hb_cmds);
        for (auto& c : hb_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
            if (c.uploadcmd().block().blockid() == last.blockid()) {
              ASSERT_EQ(c.uploadcmd().uploadtype(), cloudfs::datanode::UPLOAD);
              ASSERT_TRUE(mock_ufs->CheckUploadId(src, c.uploadcmd().uploadid())
                              .IsOK());
              upload_id = c.uploadcmd().uploadid();
              pufs_name = c.uploadcmd().blockpufsname();
              wait_upload_cmd = false;
              break;
            }
          }
        }
        if (!wait_upload_cmd) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    }
    ASSERT_FALSE(wait_upload_cmd);
  });
}

TEST_F(AccNamespaceTest, TestPin) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_pin");
    std::string path = "/" + key;
    uint64_t max_length = 1024 * 1024 * 1024;
    uint64_t length = 128L * 1024 * 1024;
    ns->CreateObject(key, length);

    std::string src = ns->JoinUfsPathWithPrefix(path);
    std::string root = ns->JoinUfsPathWithPrefix("/");

    RpcController ctrl;
    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(0);

    cloudfs::ExtendedBlockProto b;
    {
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(src,
                                    NetworkLocationInfo(),
                                    false,
                                    true,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    &ctrl,
                                    &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_FALSE(rsp.fs().pinned());
      ASSERT_EQ(rsp.fs().pinned_ttl(), -1);

      b.CopyFrom(rsp.fs().locations().blocks(0).b());
    }

    {
      BlockManager::RepeatedIncBlockReport received_report;
      TestAccNamespace::MakeReport(
          b.blockid(),
          b.generationstamp(),
          b.numbytes(),
          "",  // etag
          "",  // upload id
          "",  // pufs_name
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
          &received_report);
      Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                            received_report);
      ASSERT_TRUE(s.IsOK());
    }

    {
      cnetpp::base::IPAddress ip("127.0.0.1");
      cloudfs::PinRequestProto req;
      req.set_src(root);
      req.set_ttl(1024);
      req.set_recursive(true);
      req.set_unpin(false);
      cloudfs::PinResponseProto rsp;
      SynchronizedRpcClosure done;

      ns->acc_ns_->AsyncPin(root, &req, &rsp, info, LogRpcInfo(), &done);
      done.Await();
      ASSERT_FALSE(done.status().IsOK());
    }

    {
      cnetpp::base::IPAddress ip("127.0.0.1");
      cloudfs::PinRequestProto req;
      req.set_src(root);
      req.set_ttl(-1);
      req.set_recursive(true);
      req.set_unpin(false);
      cloudfs::PinResponseProto rsp;
      SynchronizedRpcClosure done;

      ns->acc_ns_->AsyncPin(root, &req, &rsp, info, LogRpcInfo(), &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK());
    }

    {
      info.set_syncinterval(-1);
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(src,
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    &ctrl,
                                    &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_TRUE(rsp.fs().pinned());
      ASSERT_EQ(rsp.fs().pinned_ttl(), -1);
    }

    {
      bool found = false;
      for (int i = 0; i < 10; i++) {
        cloudfs::datanode::HeartbeatResponseProto cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &cmds);
        for (auto& c : cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::BlockIdCommand) {
            found = true;
            ASSERT_TRUE(c.has_blkidcmd());
            ASSERT_EQ(c.blkidcmd().action(),
                      cloudfs::datanode::BlockIdCommandProto_Action_PIN);
            ASSERT_EQ(c.blkidcmd().blockids_size(), 1);
            ASSERT_EQ(c.blkidcmd().blockids(0), b.blockid());
          }
        }
        if (found) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
      ASSERT_TRUE(found);
    }

    {
      info.set_syncinterval(0);
      cloudfs::GetFileInfoResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetFileInfo(src,
                                    NetworkLocationInfo(),
                                    false,
                                    false,
                                    permission,
                                    ugi,
                                    info,
                                    &rsp,
                                    &ctrl,
                                    &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
      ASSERT_TRUE(rsp.fs().pinned());
      ASSERT_EQ(rsp.fs().pinned_ttl(), -1);
    }
  });
}

TEST_F(AccNamespaceTest, ConcatTestHappyBigFile) {
  FLAGS_nn_drive_upload = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/concat_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          test_dir, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    {
      SetDirPolicyRequestProto request;
      request.set_path(inner_dir);
      request.mutable_upload_policy()->set_upload_interval_ms(-1);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy(inner_dir, &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    std::string target_file = test_dir + "/target";
    std::string target_file_inner = inner_dir + "/target";
    std::vector<uint64_t> block_ids;

    INodeID target_inode_id = 0;
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(target_file,
                  128 * 1024 * 1024,
                  1,
                  true,
                  &add_response,
                  &create_response);
      block_ids.push_back(add_response.block().b().blockid());
      target_inode_id = create_response.fs().fileid();
    }

    std::vector<std::string> part_files;

    for (int i = 0; i < 10; i++) {
      std::string part_file = test_dir + "/part" + std::to_string(i);
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(part_file,
                  128 * 1024 * 1024,
                  1,
                  true,
                  &add_response,
                  &create_response);
      block_ids.push_back(add_response.block().b().blockid());
      part_files.push_back(part_file);
    }

    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncConcat(
          target_file, part_files, LogRpcInfo(), &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    // check bip
    bool exp_key_block = false;
    int exp_part_num = 1;
    uint64_t exp_bundle_offset = 0;
    uint64_t exp_bundle_length = 0;
    uint64_t exp_pufs_offset = 0;

    INode target_inode;
    ASSERT_EQ(ns->ns_->meta_storage()->GetINode(target_inode_id, &target_inode),
              StatusCode::kOK);

    BlockInfoProto last_bip;
    last_bip.set_block_id(kInvalidBlockID);
    for (const auto& b : target_inode.blocks()) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip));

      exp_key_block = false;
      uint64_t total_size = bip.num_bytes();
      if (last_bip.block_id() != kInvalidBlockID) {
        if (last_bip.key_block()) {
          exp_part_num = last_bip.part_num() + 1;
          exp_bundle_offset = last_bip.bundle_offset() +
                              last_bip.bundle_length() + last_bip.num_bytes();
          exp_bundle_length = 0;
        } else {
          exp_part_num = last_bip.part_num();
          exp_bundle_offset = last_bip.bundle_offset();
          exp_bundle_length = last_bip.bundle_length() + last_bip.num_bytes();

          total_size += last_bip.bundle_length() + last_bip.num_bytes();
        }
      }

      if (total_size > FLAGS_acc_mpu_part_threshold ||
          b.blockid() ==
              target_inode.blocks(target_inode.blocks_size() - 1).blockid()) {
        exp_key_block = true;
      }
      ASSERT_EQ(bip.key_block(), exp_key_block) << bip.block_id();
      ASSERT_EQ(bip.pufs_offset(), exp_pufs_offset) << bip.block_id();
      ASSERT_EQ(bip.part_num(), exp_part_num) << bip.block_id();
      ASSERT_EQ(bip.bundle_length(), exp_bundle_length) << bip.block_id();
      ASSERT_EQ(bip.bundle_offset(), exp_bundle_offset) << bip.block_id();

      exp_pufs_offset += bip.num_bytes();

      last_bip.CopyFrom(bip);
    }

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + target_file_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(target_file);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(target_file,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());
    ASSERT_EQ(located_blocks.blocks_size(), 11);
    for (int i = 0; i < 11; i++) {
      auto bid = located_blocks.blocks(i).b().blockid();
      ASSERT_EQ(bid, block_ids[i]);
    }
  });
}

TEST_F(AccNamespaceTest, ConcatTestHappySmallFile) {
  FLAGS_nn_drive_upload = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/concat_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          test_dir, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    {
      SetDirPolicyRequestProto request;
      request.set_path(inner_dir);
      request.mutable_upload_policy()->set_upload_interval_ms(-1);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy(inner_dir, &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    std::string target_file = test_dir + "/target";
    std::string target_file_inner = inner_dir + "/target";
    std::vector<uint64_t> block_ids;

    INodeID target_inode_id = 0;
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(target_file,
                  32 * 1024 * 1024,
                  1,
                  true,
                  &add_response,
                  &create_response);
      block_ids.push_back(add_response.block().b().blockid());
      target_inode_id = create_response.fs().fileid();
    }

    std::vector<std::string> part_files;

    for (int i = 0; i < 10; i++) {
      std::string part_file = test_dir + "/part" + std::to_string(i);
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(part_file,
                  32 * 1024 * 1024,
                  1,
                  true,
                  &add_response,
                  &create_response);
      block_ids.push_back(add_response.block().b().blockid());
      part_files.push_back(part_file);
    }

    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncConcat(
          target_file, part_files, LogRpcInfo(), &rpc_done, info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + target_file_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // check bip
    bool exp_key_block = false;
    int exp_part_num = 1;
    uint64_t exp_bundle_offset = 0;
    uint64_t exp_bundle_length = 0;
    uint64_t exp_pufs_offset = 0;

    INode target_inode;
    ASSERT_EQ(ns->ns_->meta_storage()->GetINode(target_inode_id, &target_inode),
              StatusCode::kOK);

    BlockInfoProto last_bip;
    last_bip.set_block_id(kInvalidBlockID);
    for (const auto& b : target_inode.blocks()) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip));

      exp_key_block = false;
      uint64_t total_size = bip.num_bytes();
      if (last_bip.block_id() != kInvalidBlockID) {
        if (last_bip.key_block()) {
          exp_part_num = last_bip.part_num() + 1;
          exp_bundle_offset = last_bip.bundle_offset() +
                              last_bip.bundle_length() + last_bip.num_bytes();
          exp_bundle_length = 0;
        } else {
          exp_part_num = last_bip.part_num();
          exp_bundle_offset = last_bip.bundle_offset();
          exp_bundle_length = last_bip.bundle_length() + last_bip.num_bytes();

          total_size += last_bip.bundle_length() + last_bip.num_bytes();
        }
      }

      if (total_size > FLAGS_acc_mpu_part_threshold ||
          b.blockid() ==
              target_inode.blocks(target_inode.blocks_size() - 1).blockid()) {
        exp_key_block = true;
      }
      ASSERT_EQ(bip.key_block(), exp_key_block) << bip.block_id();
      ASSERT_EQ(bip.pufs_offset(), exp_pufs_offset) << bip.block_id();
      ASSERT_EQ(bip.part_num(), exp_part_num) << bip.block_id();
      ASSERT_EQ(bip.bundle_length(), exp_bundle_length) << bip.block_id();
      ASSERT_EQ(bip.bundle_offset(), exp_bundle_offset) << bip.block_id();

      exp_pufs_offset += bip.num_bytes();

      last_bip.CopyFrom(bip);
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(target_file);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(target_file,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    LOG(INFO) << "response=" << get_response.ShortDebugString();
    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());
    ASSERT_EQ(located_blocks.blocks_size(), 11);

    for (int i = 0; i < 11; i++) {
      auto bid = located_blocks.blocks(i).b().blockid();
      ASSERT_EQ(bid, block_ids[i]);
    }
  });
}

TEST_F(AccNamespaceTest, ConcatTestUploading) {
  FLAGS_nn_drive_upload = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/concat_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          test_dir, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    std::string target_file = test_dir + "/target";
    std::string target_file_inner = inner_dir + "/target";
    std::vector<uint64_t> block_ids;

    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(target_file, 100, 1, true, &add_response, &create_response);
      block_ids.push_back(add_response.block().b().blockid());
    }

    std::vector<std::string> part_files;

    for (int i = 0; i < 10; i++) {
      std::string part_file = test_dir + "/part" + std::to_string(i);
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(part_file, 100, 1, true, &add_response, &create_response);
      block_ids.push_back(add_response.block().b().blockid());
      part_files.push_back(part_file);
    }

    SynchronizedRpcClosure rpc_done;
    ns->acc_ns_->AsyncConcat(
        target_file, part_files, LogRpcInfo(), &rpc_done, info);
    rpc_done.Await();
    LOG(INFO) << rpc_done.status().ToString();
    ASSERT_TRUE(rpc_done.status().HasException())
        << rpc_done.status().ToString();

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + target_file_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(target_file);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(target_file,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());
    ASSERT_EQ(located_blocks.blocks_size(), 1);
    for (int i = 0; i < 1; i++) {
      auto bid = located_blocks.blocks(i).b().blockid();
      ASSERT_EQ(bid, block_ids[i]);
    }
  });
}

TEST_F(AccNamespaceTest, UploadSmallBlock) {
  FLAGS_nn_drive_upload = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);

    std::string file_path = test_dir + "test_file";
    std::string file_path_inner = inner_dir + "test_file";
    std::vector<uint64_t> block_ids;

    INodeID inode_id = 0;
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFileMultiBlock(file_path,
                            {1 * 1024 * 1024, 128 * 1024 * 1024},
                            1,
                            /*need_complete=*/true,
                            /*need_persist_block=*/false,
                            /*need_persist_file=*/false,
                            &add_response,
                            &create_response,
                            &block_ids);
      inode_id = create_response.fs().fileid();
    }

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    std::vector<cloudfs::ExtendedBlockProto> bp_list;
    {
      cloudfs::GetBlockLocationsRequestProto req;
      req.set_src(file_path);
      req.set_offset(0);
      req.set_length(1024LL * 1024 * 1024 * 1024);
      req.set_fileid(inode_id);
      cloudfs::GetBlockLocationsResponseProto rsp;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(),
                                          info,
                                          &req,
                                          &rsp,
                                          &ctrl,
                                          &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();

      for (const auto& location_b : rsp.locations().blocks()) {
        bp_list.push_back(location_b.b());
      }
    }

    ASSERT_EQ(bp_list.size(), block_ids.size());

    std::vector<BlockInfoProto> bip_list;
    for (int i = 0; i < bp_list.size(); ++i) {
      auto block_id = bp_list[i].blockid();

      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(block_id, &bip));
      bip_list.push_back(bip);
    }

    // persist
    std::string upload_id, pufs_name;
    for (int i = 0; i < bp_list.size(); ++i) {
      auto bp = bp_list[i];
      if (!bip_list[i].key_block()) {
        continue;
      }
      bool wait_upload_cmd = true;
      {
        for (int i = 0; i < 10; i++) {
          BlockManager::RepeatedIncBlockReport nego_report;
          TestAccNamespace::MakeReport(
              bp.blockid(),
              bp.generationstamp(),
              bp.numbytes(),
              "",  // etag
              "",  // upload id
              "",  // pufs_name
              cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                  UPLOAD_ID_NEGOED,
              &nego_report);
          Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                                nego_report);
          ASSERT_TRUE(s.IsOK());

          cloudfs::datanode::HeartbeatResponseProto nego_cmds;
          ns->block_manager_->GetCommands(
              ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
              ns->ns_->blockpool_id(),
              0,
              &nego_cmds);
          for (auto& c : nego_cmds.cmds()) {
            if (c.cmdtype() ==
                cloudfs::datanode::DatanodeCommandProto::UploadCommand) {
              if (c.uploadcmd().block().blockid() == bp.blockid()) {
                ASSERT_EQ(c.uploadcmd().uploadtype(),
                          cloudfs::datanode::UPLOAD);
                ASSERT_TRUE(
                    mock_ufs->CheckUploadId(file_path, c.uploadcmd().uploadid())
                        .IsOK());
                if (upload_id.empty()) {
                  upload_id = c.uploadcmd().uploadid();
                } else {
                  ASSERT_EQ(upload_id, c.uploadcmd().uploadid());
                }
                if (pufs_name.empty()) {
                  pufs_name = c.uploadcmd().blockpufsname();
                } else {
                  ASSERT_EQ(pufs_name, c.uploadcmd().blockpufsname());
                }
                wait_upload_cmd = false;
                break;
              }
            }
          }
          if (!wait_upload_cmd) {
            break;
          }
          std::this_thread::sleep_for(std::chrono::seconds(1));
        }
      }
      ASSERT_FALSE(wait_upload_cmd);
    }

    std::vector<std::string> etag_list(bp_list.size(), "");
    for (int i = 0; i < bp_list.size(); ++i) {
      if (!bip_list[i].key_block()) {
        continue;
      }
      // DN upload success
      std::string etag;
      mock_ufs->AddPartToObject(
          file_path, upload_id, i, bp_list[i].numbytes(), &etag);
      etag_list[i] = etag;
    }

    for (int i = 0; i < bp_list.size(); ++i) {
      const auto& bp = bp_list[i];
      const auto& etag = etag_list[i];

      if (!bip_list[i].key_block()) {
        continue;
      }

      bool wait_evict_cmd = true;
      for (int i = 0; i < 10; i++) {
        BlockManager::RepeatedIncBlockReport upload_success_report;
        TestAccNamespace::MakeReport(
            bp.blockid(),
            bp.generationstamp(),
            bp.numbytes(),
            etag,
            upload_id,
            pufs_name,
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED,
            &upload_success_report);
        Status s = ns->block_manager_->IncrementalBlockReport(
            "datanode1", upload_success_report);
        ASSERT_TRUE(s.IsOK());

        cloudfs::datanode::HeartbeatResponseProto nego_cmds;
        ns->block_manager_->GetCommands(
            ns->datanode_manager_->GetDatanodeFromUuid("datanode1")->id(),
            ns->ns_->blockpool_id(),
            0,
            &nego_cmds);
        for (auto& c : nego_cmds.cmds()) {
          if (c.cmdtype() ==
              cloudfs::datanode::DatanodeCommandProto::NotifyEvictableCommand) {
            if (c.necmd().block().blockid() == bp.blockid()) {
              wait_evict_cmd = false;
              break;
            }
          }
        }
        if (!wait_evict_cmd) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
      ASSERT_FALSE(wait_evict_cmd) << bip_list[i].ShortDebugString();
    }

    {
      bool persisted = false;
      for (int i = 0; i < 10; i++) {
        GetFileInfoResponseProto res;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(file_path,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &res,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        persisted = res.fs().acc_file_status() ==
                    HdfsFileStatusProto_AccFileStatus::
                        HdfsFileStatusProto_AccFileStatus_FILE_PERSISTED;
        if (persisted) {
          break;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
      ASSERT_TRUE(persisted);
    }

    // fsck
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(file_path);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    LOG(INFO) << "response=" << get_response.ShortDebugString();
    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());
    ASSERT_EQ(located_blocks.blocks_size(), bp_list.size());

    for (int i = 0; i < bp_list.size(); ++i) {
      auto block_id = bp_list[i].blockid();

      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(block_id, &bip));
      ASSERT_EQ(bip.state(), BlockInfoProto::kPersisted);

      auto uc_state = ns->ns_->block_manager()->GetBlockUCState(block_id);
      ASSERT_EQ(uc_state, BlockUCState::kPersisted);
    }
  });
}

TEST_F(AccNamespaceTest, HpcDnTag) {
  FLAGS_placement_ignore_existed_switch = false;

  RunCase([this](TestAccNamespace* ns) {
    for (int i = 0; i < 10; ++i) {
      auto reg = TestAccNamespace::MakeRegistrationPB(
          "datanode" + std::to_string(i), "10.10.10." + std::to_string(i));
      reg.mutable_datanodeid()->mutable_location_tag()->set_az("LF");
      reg.mutable_datanodeid()->mutable_location_tag()->set_switch_("switch");
      reg.mutable_datanodeid()->mutable_location_tag()->set_host(
          "10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optipaddrs("10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optiprdmatags("rdma-tag-1");
      ns->threads_.push_back(ns->AddDatanode(reg));
    }

    // status
    {
      auto handler = std::make_unique<DancennStatusHandler>(
          ns->runtime_.ns,
          ns->runtime_.datanode_manager,
          ns->runtime_.block_manager,
          ns->runtime_.mock_ha_state,
          ns->runtime_.mock_safemode,
          ns->runtime_.runtime_monitor.get());

      cnetpp::http::HttpRequest request;
      auto url = "/status?detail=1";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();

      cnetpp::base::Value received_json;
      ASSERT_TRUE(cnetpp::base::Parser::Deserialize(response.http_body(),
                                                    &received_json));
      ASSERT_TRUE(received_json.IsObject());

      auto dns = received_json["datanodes"]["all"].AsArray();
      for (auto iter = dns.Begin(); iter != dns.End(); ++iter) {
        auto dn = iter->AsObject();

        LOG(INFO) << dn["report"]["id"]["datanodeUuid"].AsString();
        LOG(INFO) << dn["report"]["id"]["optIpAddrs"].AsArray()[0].AsString();
        LOG(INFO)
            << dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString();
        ASSERT_EQ(dn["report"]["id"]["optIpRdmaTags"].AsArray().Size(), 1);
        ASSERT_EQ(dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString(),
                  "rdma-tag-1");
      }
    }
  });
}

TEST_F(AccNamespaceTest, HpcWriteSameTagC33) {
  FLAGS_nn_drive_upload = false;
  FLAGS_dfs_replication_max = 3;
  FLAGS_placement_ignore_existed_switch = false;

  RunCase([this](TestAccNamespace* ns) {
    // add dn
    for (int i = 0; i < 10; ++i) {
      auto reg = TestAccNamespace::MakeRegistrationPB(
          "datanode" + std::to_string(i), "10.10.10." + std::to_string(i));
      reg.mutable_datanodeid()->mutable_location_tag()->set_az("LF");
      reg.mutable_datanodeid()->mutable_location_tag()->set_switch_("switch");
      reg.mutable_datanodeid()->mutable_location_tag()->set_host(
          "10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optipaddrs("10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optiprdmatags("rdma-tag-" +
                                                  std::to_string(i % 3));
      ns->threads_.push_back(ns->AddDatanode(reg));
    }

    // status
    {
      auto handler = std::make_unique<DancennStatusHandler>(
          ns->runtime_.ns,
          ns->runtime_.datanode_manager,
          ns->runtime_.block_manager,
          ns->runtime_.mock_ha_state,
          ns->runtime_.mock_safemode,
          ns->runtime_.runtime_monitor.get());
      cnetpp::http::HttpRequest request;
      auto url = "/status?detail=1";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();

      cnetpp::base::Value received_json;
      ASSERT_TRUE(cnetpp::base::Parser::Deserialize(response.http_body(),
                                                    &received_json));
      ASSERT_TRUE(received_json.IsObject());

      auto dns = received_json["datanodes"]["all"].AsArray();
      for (auto iter = dns.Begin(); iter != dns.End(); ++iter) {
        auto dn = iter->AsObject();

        LOG(INFO) << dn["id"].AsInteger();
        LOG(INFO) << dn["report"]["id"]["datanodeUuid"].AsString();
        LOG(INFO) << dn["report"]["id"]["optIpAddrs"].AsArray()[0].AsString();
        LOG(INFO)
            << dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString();
      }
    }

    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_replica_policy()->set_distributed(false);
      request.mutable_replica_policy()->set_local_switch_target(3);
      request.mutable_replica_policy()->set_other_switch_target(1);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    // check
    {
      auto handler = std::make_unique<DancennAdminHandler>(
          nullptr, ns->runtime_.ns, ns->runtime_.block_manager, ns->ufs_);
      cnetpp::http::HttpRequest request;
      auto url = "/admin?cmd=dir_policy&action=ls&path=/";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);

    std::string file_path = test_dir + "test_file";
    std::string file_path_inner = inner_dir + "test_file";
    std::vector<uint64_t> block_ids;
    std::string local_rdma_tag = "rdma-tag-0";

    INodeID inode_id = 0;
    // create
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      std::vector<uint64_t> block_size;
      for (int i = 0; i < 100; ++i) {
        block_size.push_back(1 * 1024 * 1024);
      }
      ns->AddFileMultiBlock(file_path,
                            block_size,
                            3,
                            /*need_complete=*/true,
                            /*need_persist_block=*/false,
                            /*need_persist_file=*/false,
                            &add_response,
                            &create_response,
                            &block_ids,
                            /*create_parent=*/true,
                            /*create_flag=*/::cloudfs::CreateFlagProto::CREATE,
                            /*rdma_tag=*/local_rdma_tag);
      inode_id = create_response.fs().fileid();
    }

    // fsck
    {
      auto handler = std::make_unique<DancennFsckHandler>(
          ns->runtime_.ns, ns->runtime_.block_manager);

      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(file_path);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    LOG(INFO) << "response=" << get_response.ShortDebugString();
    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());

    for (const auto& block : located_blocks.blocks()) {
      for (const auto& dn : block.locs()) {
        ASSERT_EQ(dn.id().optiprdmatags().size(), 1);
        ASSERT_EQ(dn.id().optiprdmatags().Get(0), local_rdma_tag);
      }
    }
  });
}
TEST_F(AccNamespaceTest, HpcWriteSameTagC32) {
  FLAGS_nn_drive_upload = false;
  FLAGS_dfs_replication_max = 3;
  FLAGS_placement_ignore_existed_switch = false;

  RunCase([this](TestAccNamespace* ns) {
    // add dn
    for (int i = 0; i < 10; ++i) {
      auto reg = TestAccNamespace::MakeRegistrationPB(
          "datanode" + std::to_string(i), "10.10.10." + std::to_string(i));
      reg.mutable_datanodeid()->mutable_location_tag()->set_az("LF");
      reg.mutable_datanodeid()->mutable_location_tag()->set_switch_("switch");
      reg.mutable_datanodeid()->mutable_location_tag()->set_host(
          "10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optipaddrs("10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optiprdmatags("rdma-tag-" +
                                                  std::to_string(i % 3));
      ns->threads_.push_back(ns->AddDatanode(reg));
    }

    // status
    {
      auto handler = std::make_unique<DancennStatusHandler>(
          ns->runtime_.ns,
          ns->runtime_.datanode_manager,
          ns->runtime_.block_manager,
          ns->runtime_.mock_ha_state,
          ns->runtime_.mock_safemode,
          ns->runtime_.runtime_monitor.get());
      cnetpp::http::HttpRequest request;
      auto url = "/status?detail=1";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();

      cnetpp::base::Value received_json;
      ASSERT_TRUE(cnetpp::base::Parser::Deserialize(response.http_body(),
                                                    &received_json));
      ASSERT_TRUE(received_json.IsObject());

      auto dns = received_json["datanodes"]["all"].AsArray();
      for (auto iter = dns.Begin(); iter != dns.End(); ++iter) {
        auto dn = iter->AsObject();

        LOG(INFO) << dn["id"].AsInteger();
        LOG(INFO) << dn["report"]["id"]["datanodeUuid"].AsString();
        LOG(INFO) << dn["report"]["id"]["optIpAddrs"].AsArray()[0].AsString();
        LOG(INFO)
            << dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString();
      }
    }

    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_replica_policy()->set_distributed(false);
      request.mutable_replica_policy()->set_local_switch_target(2);
      request.mutable_replica_policy()->set_other_switch_target(1);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    // check
    {
      auto handler = std::make_unique<DancennAdminHandler>(
          nullptr, ns->runtime_.ns, ns->runtime_.block_manager, ns->ufs_);
      cnetpp::http::HttpRequest request;
      auto url = "/admin?cmd=dir_policy&action=ls&path=/";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);

    std::string file_path = test_dir + "test_file";
    std::string file_path_inner = inner_dir + "test_file";
    std::vector<uint64_t> block_ids;
    std::string local_rdma_tag = "rdma-tag-0";

    INodeID inode_id = 0;
    // create
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      std::vector<uint64_t> block_size;
      for (int i = 0; i < 100; ++i) {
        block_size.push_back(1 * 1024 * 1024);
      }
      ns->AddFileMultiBlock(file_path,
                            block_size,
                            3,
                            /*need_complete=*/true,
                            /*need_persist_block=*/false,
                            /*need_persist_file=*/false,
                            &add_response,
                            &create_response,
                            &block_ids,
                            /*create_parent=*/true,
                            /*create_flag=*/::cloudfs::CreateFlagProto::CREATE,
                            /*rdma_tag=*/local_rdma_tag);
      inode_id = create_response.fs().fileid();
    }

    // fsck
    {
      auto handler = std::make_unique<DancennFsckHandler>(
          ns->runtime_.ns, ns->runtime_.block_manager);

      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // get & check
    GetBlockLocationsRequestProto get_request;
    get_request.set_src(file_path);
    get_request.set_offset(0);
    get_request.set_length(1024LL * 1024 * 1024 * 1024);
    GetBlockLocationsResponseProto get_response;
    cnetpp::base::IPAddress client_ip("***********");
    {
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
    }

    LOG(INFO) << "response=" << get_response.ShortDebugString();
    auto located_blocks = get_response.locations();
    ASSERT_FALSE(located_blocks.underconstruction());

    for (const auto& block : located_blocks.blocks()) {
      int local = 0;
      int other = 0;
      for (const auto& dn : block.locs()) {
        ASSERT_EQ(dn.id().optiprdmatags().size(), 1);
        auto same = dn.id().optiprdmatags().Get(0) == local_rdma_tag;
        if (same) {
          local++;
        } else {
          other++;
        }
      }
      ASSERT_EQ(local, 2);
    }
  });
}

TEST_F(AccNamespaceTest, HpcReadPreferC33) {
  FLAGS_nn_drive_upload = false;
  FLAGS_dfs_replication_max = 3;
  FLAGS_placement_ignore_existed_switch = false;

  RunCase([this](TestAccNamespace* ns) {
    // add dn
    for (int i = 0; i < 10; ++i) {
      auto reg = TestAccNamespace::MakeRegistrationPB(
          "datanode" + std::to_string(i), "10.10.10." + std::to_string(i));
      reg.mutable_datanodeid()->mutable_location_tag()->set_az("LF");
      reg.mutable_datanodeid()->mutable_location_tag()->set_switch_("switch");
      reg.mutable_datanodeid()->mutable_location_tag()->set_host(
          "10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optipaddrs("10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optiprdmatags("rdma-tag-" +
                                                  std::to_string(i % 3));
      ns->threads_.push_back(ns->AddDatanode(reg));
    }

    // status
    {
      auto handler = std::make_unique<DancennStatusHandler>(
          ns->runtime_.ns,
          ns->runtime_.datanode_manager,
          ns->runtime_.block_manager,
          ns->runtime_.mock_ha_state,
          ns->runtime_.mock_safemode,
          ns->runtime_.runtime_monitor.get());
      cnetpp::http::HttpRequest request;
      auto url = "/status?detail=1";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();

      cnetpp::base::Value received_json;
      ASSERT_TRUE(cnetpp::base::Parser::Deserialize(response.http_body(),
                                                    &received_json));
      ASSERT_TRUE(received_json.IsObject());

      auto dns = received_json["datanodes"]["all"].AsArray();
      for (auto iter = dns.Begin(); iter != dns.End(); ++iter) {
        auto dn = iter->AsObject();

        LOG(INFO) << dn["id"].AsInteger();
        LOG(INFO) << dn["report"]["id"]["datanodeUuid"].AsString();
        LOG(INFO) << dn["report"]["id"]["optIpAddrs"].AsArray()[0].AsString();
        LOG(INFO)
            << dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString();
      }
    }

    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_replica_policy()->set_distributed(false);
      request.mutable_replica_policy()->set_local_switch_target(3);
      request.mutable_replica_policy()->set_other_switch_target(0);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    // check
    {
      auto handler = std::make_unique<DancennAdminHandler>(
          nullptr, ns->runtime_.ns, ns->runtime_.block_manager, ns->ufs_);
      cnetpp::http::HttpRequest request;
      auto url = "/admin?cmd=dir_policy&action=ls&path=/";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);

    std::string file_path = test_dir + "test_file";
    std::string file_path_inner = inner_dir + "test_file";
    std::vector<uint64_t> block_ids;

    std::string local_rdma_tag = "rdma-tag-0";
    std::string other_rdma_tag = "rdma-tag-1";

    INodeID inode_id = 0;
    // create
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      std::vector<uint64_t> block_size;
      for (int i = 0; i < 100; ++i) {
        block_size.push_back(1 * 1024 * 1024);
      }
      ns->AddFileMultiBlock(file_path,
                            block_size,
                            3,
                            /*need_complete=*/true,
                            /*need_persist_block=*/true,
                            /*need_persist_file=*/true,
                            &add_response,
                            &create_response,
                            &block_ids,
                            /*create_parent=*/true,
                            /*create_flag=*/::cloudfs::CreateFlagProto::CREATE,
                            /*rdma_tag=*/local_rdma_tag);
      inode_id = create_response.fs().fileid();
    }

    // fsck
    {
      auto handler = std::make_unique<DancennFsckHandler>(
          ns->runtime_.ns, ns->runtime_.block_manager);

      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // PREFER_LOCAL
    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_read_policy()->set_read_switch_policy(
          ReadPolicy::PREFER_LOCAL);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    auto get_func =
        [&](const std::string& rdma_tag,
            const std::function<void(int local, int other)>& check_func)
        -> std::pair<int, int> {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(file_path);
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = rdma_tag;

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          client_location,
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      EXPECT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();

      LOG(INFO) << "response=" << get_response.ShortDebugString();
      auto located_blocks = get_response.locations();
      EXPECT_FALSE(located_blocks.underconstruction());

      int total_local = 0;
      int total_other = 0;
      for (const auto& block : located_blocks.blocks()) {
        int local = 0;
        int other = 0;
        bool has_other = false;
        for (const auto& dn : block.locs()) {
          EXPECT_EQ(dn.id().optiprdmatags().size(), 1);
          auto same = dn.id().optiprdmatags().Get(0) == rdma_tag;
          if (same) {
            local++;
            total_local++;
          } else {
            other++;
            total_other++;
            has_other = true;
          }
        }

        check_func(local, other);
      }
      return {total_local, total_other};
    };

    // PREFER_LOCAL from local: get & check
    get_func(local_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 3);
      EXPECT_EQ(other, 0);
    });

    // PREFER_LOCAL from remote: get & check
    get_func(other_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 3);
      EXPECT_EQ(other, 3);
    });

    // PREFER_CACHED
    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_read_policy()->set_read_switch_policy(
          ReadPolicy::PREFER_CACHED);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    // PREFER_CACHED from local: get & check
    get_func(local_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 3);
      EXPECT_EQ(other, 0);
    });
    // PREFER_CACHED from remote: get & check
    get_func(other_rdma_tag, [](int local, int other) {
      EXPECT_GE(local, 0);
      EXPECT_EQ(other, 3);
    });
  });
}

TEST_F(AccNamespaceTest, HpcReadPreferC32) {
  FLAGS_nn_drive_upload = false;
  FLAGS_dfs_replication_max = 3;
  FLAGS_placement_ignore_existed_switch = false;

  RunCase([this](TestAccNamespace* ns) {
    // add dn
    for (int i = 0; i < 10; ++i) {
      auto reg = TestAccNamespace::MakeRegistrationPB(
          "datanode" + std::to_string(i), "10.10.10." + std::to_string(i));
      reg.mutable_datanodeid()->mutable_location_tag()->set_az("LF");
      reg.mutable_datanodeid()->mutable_location_tag()->set_switch_("switch");
      reg.mutable_datanodeid()->mutable_location_tag()->set_host(
          "10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optipaddrs("10.10.20." + std::to_string(i));
      reg.mutable_datanodeid()->add_optiprdmatags("rdma-tag-" +
                                                  std::to_string(i % 3));
      ns->threads_.push_back(ns->AddDatanode(reg));
    }

    // status
    {
      auto handler = std::make_unique<DancennStatusHandler>(
          ns->runtime_.ns,
          ns->runtime_.datanode_manager,
          ns->runtime_.block_manager,
          ns->runtime_.mock_ha_state,
          ns->runtime_.mock_safemode,
          ns->runtime_.runtime_monitor.get());
      cnetpp::http::HttpRequest request;
      auto url = "/status?detail=1";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();

      cnetpp::base::Value received_json;
      ASSERT_TRUE(cnetpp::base::Parser::Deserialize(response.http_body(),
                                                    &received_json));
      ASSERT_TRUE(received_json.IsObject());

      auto dns = received_json["datanodes"]["all"].AsArray();
      for (auto iter = dns.Begin(); iter != dns.End(); ++iter) {
        auto dn = iter->AsObject();

        LOG(INFO) << dn["id"].AsInteger();
        LOG(INFO) << dn["report"]["id"]["datanodeUuid"].AsString();
        LOG(INFO) << dn["report"]["id"]["optIpAddrs"].AsArray()[0].AsString();
        LOG(INFO)
            << dn["report"]["id"]["optIpRdmaTags"].AsArray()[0].AsString();
      }
    }

    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_replica_policy()->set_distributed(false);
      request.mutable_replica_policy()->set_local_switch_target(2);
      request.mutable_replica_policy()->set_other_switch_target(1);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    // check
    {
      auto handler = std::make_unique<DancennAdminHandler>(
          nullptr, ns->runtime_.ns, ns->runtime_.block_manager, ns->ufs_);
      cnetpp::http::HttpRequest request;
      auto url = "/admin?cmd=dir_policy&action=ls&path=/";
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(60);

    std::string inner_dir = "/";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);

    std::string file_path = test_dir + "test_file";
    std::string file_path_inner = inner_dir + "test_file";
    std::vector<uint64_t> block_ids;

    std::string local_rdma_tag = "rdma-tag-0";
    std::string other_rdma_tag = "rdma-tag-1";

    INodeID inode_id = 0;
    // create
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      std::vector<uint64_t> block_size;
      for (int i = 0; i < 100; ++i) {
        block_size.push_back(1 * 1024 * 1024);
      }
      ns->AddFileMultiBlock(file_path,
                            block_size,
                            3,
                            /*need_complete=*/true,
                            /*need_persist_block=*/true,
                            /*need_persist_file=*/true,
                            &add_response,
                            &create_response,
                            &block_ids,
                            /*create_parent=*/true,
                            /*create_flag=*/::cloudfs::CreateFlagProto::CREATE,
                            /*rdma_tag=*/local_rdma_tag);
      inode_id = create_response.fs().fileid();
    }

    // fsck
    {
      auto handler = std::make_unique<DancennFsckHandler>(
          ns->runtime_.ns, ns->runtime_.block_manager);

      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + file_path_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // PREFER_LOCAL
    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_read_policy()->set_read_switch_policy(
          ReadPolicy::PREFER_LOCAL);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    auto get_func =
        [&](const std::string& rdma_tag,
            const std::function<void(int local, int other)>& check_func)
        -> std::pair<int, int> {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(file_path);
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = rdma_tag;

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(file_path,
                                          p,
                                          ugi,
                                          LogRpcInfo(),
                                          client_location,
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      EXPECT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();

      LOG(INFO) << "response=" << get_response.ShortDebugString();
      auto located_blocks = get_response.locations();
      EXPECT_FALSE(located_blocks.underconstruction());

      int total_local = 0;
      int total_other = 0;
      for (const auto& block : located_blocks.blocks()) {
        int local = 0;
        int other = 0;
        bool has_other = false;
        for (const auto& dn : block.locs()) {
          EXPECT_EQ(dn.id().optiprdmatags().size(), 1);
          auto same = dn.id().optiprdmatags().Get(0) == rdma_tag;
          if (same) {
            local++;
            total_local++;
            EXPECT_FALSE(has_other);
          } else {
            other++;
            total_other++;
            has_other = true;
          }
        }

        check_func(local, other);
      }
      return {total_local, total_other};
    };

    // PREFER_LOCAL from local: get & check
    get_func(local_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 3);
      EXPECT_GE(other, 0);
    });

    // PREFER_LOCAL from remote: get & check
    get_func(other_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 3);
      EXPECT_GE(other, 2);
    });

    // PREFER_CACHED
    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_read_policy()->set_read_switch_policy(
          ReadPolicy::PREFER_CACHED);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    // PREFER_CACHED from local: get & check
    get_func(local_rdma_tag, [](int local, int other) {
      EXPECT_EQ(local, 2);
      EXPECT_EQ(other, 1);
    });
    // PREFER_CACHED from remote: get & check
    get_func(other_rdma_tag, [](int local, int other) {
      EXPECT_GE(local, 0);
      EXPECT_GE(other, 2);
    });
  });
}

TEST_F(AccNamespaceTest, GetBlockLocationsAfterRename) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("test_get_block_locations_after_rename");
    std::string path = "/" + key;
    ns->CreateObject(key, 1024);
    uint64_t fileid = 0;

    cnetpp::base::IPAddress ip("127.0.0.1");

    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    {
      cloudfs::GetBlockLocationsRequestProto request;
      request.set_src(path);
      request.set_offset(0);
      request.set_length(1024 * 1024 * 1024);
      cloudfs::GetBlockLocationsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetBlockLocations(ns->JoinUfsPathWithPrefix(key),
                                          permission,
                                          ugi,
                                          dummy_rpc_info,
                                          NetworkLocationInfo(ip),
                                          info,
                                          &request,
                                          &response,
                                          nullptr,
                                          &done);
      done.Await();

      ASSERT_EQ(response.locations().blocks(0).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(key));
      fileid = response.locations().fileid();
      ASSERT_GT(fileid, 0);
    }

    std::string new_key("test_get_block_locations_after_rename_new");
    std::string new_path = "/" + key;

    {
      std::string ufs_path = ns->JoinUfsPathWithPrefix(key);
      std::string new_ufs_path = ns->JoinUfsPathWithPrefix(new_key);

      auto opt = NewRenameOption(ufs_path, path, new_ufs_path, new_path);

      cloudfs::Rename2ResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncRenameTo2(opt, &done, &response);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    {
      cloudfs::GetBlockLocationsRequestProto request;
      request.set_src(path);
      request.set_offset(0);
      request.set_length(1024 * 1024 * 1024);
      request.set_fileid(fileid);
      cloudfs::GetBlockLocationsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncGetBlockLocations(ns->JoinUfsPathWithPrefix(key),
                                          permission,
                                          ugi,
                                          dummy_rpc_info,
                                          NetworkLocationInfo(ip),
                                          info,
                                          &request,
                                          &response,
                                          nullptr,
                                          &done);
      done.Await();

      ASSERT_EQ(response.locations().blocks(0).b().blockpufsname(),
                ns->JoinObjectKeyWithPrefix(new_key));
    }
  });
}

TEST_F(AccNamespaceTest, NsIdToPufsName) {
  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());
    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;

    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    std::string inner_dir = "/test_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(
          test_dir, p, ugi, false, &request, &response, &ctrl, &done, info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    std::string target_file = test_dir + "/target";
    std::string target_file_inner = inner_dir + "/target";
    INodeID inode_id = 0;
    {
      auto create_request = TestAccNamespace::GetCreateRequest();
      CreateResponseProto create_response;
      AddBlockResponseProto add_response;
      ns->AddFile(target_file,
                  128 * 1024 * 1024,
                  1,
                  true,
                  &add_response,
                  &create_response);
      inode_id = create_response.fs().fileid();
    }

    auto cb = ns->acc_ns_->NsIdToPufsNameCB();
    // Root Inode
    std::string root_key = cb(kRootINodeId);
    std::string root_ufs_path;
    ns->acc_ns_->ConvertUfsPath("/", &root_ufs_path);
    std::string real_root_key;
    ns->ufs_->GetUfsIdentifier(UfsIdentifierInfo(root_ufs_path),
                               &real_root_key);

    ASSERT_EQ(real_root_key, root_key);

    // Inode not exist
    auto not_exist_key = cb(kInvalidINodeId);
    ASSERT_EQ("", not_exist_key);

    auto test_file_key = cb(inode_id);
    std::string test_file_ufs_path;
    ns->acc_ns_->ConvertUfsPath(target_file_inner, &test_file_ufs_path);
    std::string real_test_file_key;
    ns->ufs_->GetUfsIdentifier(UfsIdentifierInfo(test_file_ufs_path),
                               &real_test_file_key);
    ASSERT_EQ(real_test_file_key, test_file_key);
  });
}

TEST_F(AccNamespaceTest, BatchAPITestHappyCase) {
  FLAGS_nn_drive_upload = true;
  FLAGS_complete_rpc_abandon_last_empty_block = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus permission;
    FsPermissionProto fsp;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    XAttrProto no_upload_attr;
    UfsUploadPolicyProto no_upload_pb;
    no_upload_pb.set_upload_interval_ms(-1);
    no_upload_attr.set_namespace_(cloudfs::XAttrProto::SYSTEM);
    no_upload_attr.set_name("hdfs.upload.policy.string");
    no_upload_attr.set_value(no_upload_pb.SerializeAsString());

    LOG(INFO) << "Init";

    // mkdir
    std::string inner_dir = "/batch_api_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(test_dir,
                               permission,
                               ugi,
                               false,
                               &request,
                               &response,
                               &ctrl,
                               &done,
                               info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    LOG(INFO) << "AsyncMkDirs";

    // batch create
    std::string target_file = test_dir + "/file2";
    std::string target_file_inner = inner_dir + "/file2";

    INodeID target_inode_id;
    std::vector<uint64_t> block_ids;

    BatchCreateFileRequestProto batch_create_request;
    BatchCreateFileResponseProto batch_create_response;
    batch_create_request.set_clientname("client_name");
    batch_create_request.set_rpc_type(RPC_BYTERPC_MODE);
    batch_create_request.set_expectediomode(DATANODE_BLOCK_EXPECTED);
    batch_create_request.set_withaddblock(true);
    LOG(INFO) << "Init BatchCreate Proto Start";
    {
      std::vector<std::string> paths;
      // file0
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file0");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file1
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file1");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file2_target
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file2");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }

      // file2_source
      for (int i = 0; i < 10; ++i) {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/.file2.part." + std::to_string(i));
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }
      LOG(INFO) << "Init BatchCreate Proto Finish";

      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = "";
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchCreate(paths,
                                    client_location,
                                    "client_machine-test",
                                    ugi,
                                    &batch_create_request,
                                    &batch_create_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();
      LOG(INFO) << "AsyncBatchCreate batch_create_response="
                << batch_create_response.ShortDebugString();

      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();

      target_inode_id = batch_create_response.files().Get(2).fs().fileid();
    }

    // batch complete
    BatchCompleteFileRequestProto batch_complete_request;
    BatchCompleteFileResponseProto batch_complete_response;
    batch_complete_request.set_clientname("client_name");
    batch_complete_request.set_rpc_type(RPC_BYTERPC_MODE);
    LOG(INFO) << "AsyncBatchComplete";
    {
      std::vector<std::string> single_file_paths;
      std::vector<std::vector<std::string>> concat_file_srcs_paths;
      std::vector<std::string> concat_file_target_paths;
      {
        auto file = batch_complete_request.add_singlefile();
        file->set_src(test_dir + "/file0");
        single_file_paths.push_back(file->src());
        auto block = file->mutable_lastblock();
        block->CopyFrom(batch_create_response.files().Get(0).block().b());
        block->set_numbytes(128 * 1024 * 1024);
        file->set_fileid(batch_create_response.files().Get(0).fs().fileid());
      }

      {
        auto file = batch_complete_request.add_singlefile();
        file->set_src(test_dir + "/file1");
        single_file_paths.push_back(file->src());
        auto block = file->mutable_lastblock();
        block->CopyFrom(batch_create_response.files().Get(1).block().b());
        block->set_numbytes(128 * 1024 * 1024);
        file->set_fileid(batch_create_response.files().Get(1).fs().fileid());
      }

      {
        auto concat_entry = batch_complete_request.add_concatfile();
        {
          auto target_file = concat_entry->mutable_target();
          target_file->set_src(test_dir + "/file2");
          concat_file_target_paths.push_back(target_file->src());
          target_file->set_fileid(
              batch_create_response.files().Get(2).fs().fileid());

          auto block = target_file->mutable_lastblock();
          block->CopyFrom(batch_create_response.files().Get(2).block().b());
          block->set_numbytes(0);
        }

        concat_file_srcs_paths.push_back({});
        for (int i = 0; i < 10; ++i) {
          auto file = concat_entry->add_srcs();
          file->set_src(test_dir + "/.file2.part." + std::to_string(i));
          concat_file_srcs_paths.back().push_back(file->src());

          auto block = file->mutable_lastblock();
          block->CopyFrom(batch_create_response.files().Get(3 + i).block().b());
          block->set_numbytes(128 * 1024 * 1024);
          file->set_fileid(
              batch_create_response.files().Get(3 + i).fs().fileid());

          block_ids.push_back(block->blockid());
        }
      }

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchComplete(single_file_paths,
                                      concat_file_srcs_paths,
                                      concat_file_target_paths,
                                      &batch_complete_request,
                                      &batch_complete_response,
                                      LogRpcInfo(),
                                      &rpc_done,
                                      &ctrl,
                                      info);
      rpc_done.Await();
      LOG(INFO) << "batch_complete_response="
                << batch_complete_response.ShortDebugString();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      ASSERT_EQ(batch_complete_response.result(), 1);
      LOG(INFO) << "AsyncBatchComplete Finish";
    }

    // check file0
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file0");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(test_dir + "/file0",
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file0, res=" << get_response.ShortDebugString();

      EXPECT_EQ(1, get_response.locations().blocks_size());
    }
    // check file1
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file1");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(test_dir + "/file1",
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file1, res=" << get_response.ShortDebugString();

      EXPECT_EQ(1, get_response.locations().blocks_size());
    }

    // check file2 bip
    bool exp_key_block = false;
    int exp_part_num = 1;
    uint64_t exp_bundle_offset = 0;
    uint64_t exp_bundle_length = 0;
    uint64_t exp_pufs_offset = 0;

    INode target_inode;
    ASSERT_EQ(ns->ns_->meta_storage()->GetINode(target_inode_id, &target_inode),
              StatusCode::kOK);

    BlockInfoProto last_bip;
    last_bip.set_block_id(kInvalidBlockID);
    for (const auto& b : target_inode.blocks()) {
      BlockInfoProto bip;
      ASSERT_TRUE(ns->ns_->meta_storage()->GetBlockInfo(b.blockid(), &bip));

      exp_key_block = false;
      uint64_t total_size = bip.num_bytes();
      if (last_bip.block_id() != kInvalidBlockID) {
        if (last_bip.key_block()) {
          exp_part_num = last_bip.part_num() + 1;
          exp_bundle_offset = last_bip.bundle_offset() +
                              last_bip.bundle_length() + last_bip.num_bytes();
          exp_bundle_length = 0;
        } else {
          exp_part_num = last_bip.part_num();
          exp_bundle_offset = last_bip.bundle_offset();
          exp_bundle_length = last_bip.bundle_length() + last_bip.num_bytes();

          total_size += last_bip.bundle_length() + last_bip.num_bytes();
        }
      }

      if (total_size > FLAGS_acc_mpu_part_threshold ||
          b.blockid() ==
              target_inode.blocks(target_inode.blocks_size() - 1).blockid()) {
        exp_key_block = true;
      }
      ASSERT_EQ(bip.key_block(), exp_key_block) << bip.block_id();
      ASSERT_EQ(bip.pufs_offset(), exp_pufs_offset) << bip.block_id();
      ASSERT_EQ(bip.part_num(), exp_part_num) << bip.block_id();
      ASSERT_EQ(bip.bundle_length(), exp_bundle_length) << bip.block_id();
      ASSERT_EQ(bip.bundle_offset(), exp_bundle_offset) << bip.block_id();

      exp_pufs_offset += bip.num_bytes();

      last_bip.CopyFrom(bip);
    }

    // fsck
    auto handler = std::make_unique<DancennFsckHandler>(
        ns->runtime_.ns, ns->runtime_.block_manager);
    {
      cnetpp::http::HttpRequest request;
      auto url = "/fsck?detail=1&path=" + target_file_inner;
      LOG(INFO) << "URL: " << url;
      request.set_uri(url);
      auto response = handler->Handle(request);
      LOG(INFO) << "status: "
                << cnetpp::http::HttpResponse::StatusCodeToReasonPhrase(
                       response.status());
      LOG(INFO) << "response body: " << response.http_body();
    }

    // check file2
    {
      GetBlockLocationsRequestProto get_request;
      get_request.set_src(test_dir + "/file2");
      get_request.set_offset(0);
      get_request.set_length(1024LL * 1024 * 1024 * 1024);
      GetBlockLocationsResponseProto get_response;
      cnetpp::base::IPAddress client_ip("***********");

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncGetBlockLocations(target_file,
                                          permission,
                                          ugi,
                                          LogRpcInfo(),
                                          NetworkLocationInfo(client_ip),
                                          info,
                                          &get_request,
                                          &get_response,
                                          &ctrl,
                                          &rpc_done);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      LOG(INFO) << "file2, res=" << get_response.ShortDebugString();

      EXPECT_EQ(10, get_response.locations().blocks_size());

      auto located_blocks = get_response.locations();
      ASSERT_FALSE(located_blocks.underconstruction());
      ASSERT_EQ(located_blocks.blocks_size(), 10);
      for (int i = 0; i < 10; i++) {
        auto bid = located_blocks.blocks(i).b().blockid();
        ASSERT_EQ(bid, block_ids[i]);
      }
    }

    // batch get
    LOG(INFO) << "AsyncBatchGet Start";
    BatchGetFileRequestProto batch_get_request;
    BatchGetFileResponseProto batch_get_response;
    // reordered
    batch_get_request.add_srcs(test_dir + "/file1");
    batch_get_request.add_srcs(test_dir + "/file5");
    batch_get_request.add_srcs(test_dir + "/file2");
    // not exist
    batch_get_request.add_srcs(test_dir + "/file3");
    batch_get_request.add_srcs(test_dir + "/file0");
    batch_get_request.set_needlocation(true);
    {
      std::vector<std::string> paths;
      for (const auto& src : batch_get_request.srcs()) {
        paths.push_back(src);
      }

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchGet(paths,
                                 NetworkLocationInfo(),
                                 /*need_location=*/true,
                                 permission,
                                 ugi,
                                 &batch_get_request,
                                 &batch_get_response,
                                 &rpc_done,
                                 &ctrl,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      LOG(INFO) << "AsyncBatchGet Finish";

      ASSERT_EQ(batch_get_response.files().size(), 3);
      ASSERT_EQ(batch_get_response.original_index().size(), 3);
      ASSERT_EQ(batch_get_response.original_index().Get(0), 0);
      ASSERT_EQ(batch_get_response.original_index().Get(1), 2);
      ASSERT_EQ(batch_get_response.original_index().Get(2), 4);

      auto response_file0 = batch_get_response.files().Get(2);
      auto response_file1 = batch_get_response.files().Get(0);
      auto response_file2 = batch_get_response.files().Get(1);

      ASSERT_EQ(batch_create_response.files().Get(0).fs().fileid(),
                response_file0.fileid());
      ASSERT_EQ(1, response_file0.locations().blocks_size());

      ASSERT_EQ(batch_create_response.files().Get(1).fs().fileid(),
                response_file1.fileid());
      ASSERT_EQ(1, response_file1.locations().blocks_size());

      ASSERT_EQ(batch_create_response.files().Get(2).fs().fileid(),
                response_file2.fileid());
      ASSERT_EQ(10, response_file2.locations().blocks_size());
      for (int i = 0; i < 10; i++) {
        auto bid = response_file2.locations().blocks(i).b().blockid();
        ASSERT_EQ(bid, block_ids[i]);
      }
    }

    // batch delete

    BatchDeleteFileRequestProto batch_delete_request;
    BatchDeleteFileResponseProto batch_delete_response;
    // reordered
    batch_delete_request.add_srcs(test_dir + "/file2");
    batch_delete_request.add_srcs(test_dir + "/file1");
    batch_delete_request.add_srcs(test_dir + "/file0");
    {
      LOG(INFO) << "AsyncBatchDelete Start";
      std::vector<std::string> paths;
      for (const auto& src : batch_delete_request.srcs()) {
        paths.push_back(src);
      }

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchDelete(paths,
                                    &batch_delete_request,
                                    &batch_delete_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();

      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      LOG(INFO) << "AsyncBatchDelete Finish";

      ASSERT_EQ(batch_delete_response.result(), 1);
    }

    // retry batch get
    {
      LOG(INFO) << "AsyncBatchGet Start";
      std::vector<std::string> paths;
      for (const auto& src : batch_get_request.srcs()) {
        paths.push_back(src);
      }
      batch_get_response.Clear();

      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchGet(paths,
                                 NetworkLocationInfo(),
                                 /*need_location=*/true,
                                 permission,
                                 ugi,
                                 &batch_get_request,
                                 &batch_get_response,
                                 &rpc_done,
                                 &ctrl,
                                 info);
      rpc_done.Await();
      ASSERT_TRUE(!rpc_done.status().HasException())
          << rpc_done.status().ToString();
      ASSERT_TRUE(!rpc_done.status().IsFalse()) << rpc_done.status().ToString();
      LOG(INFO) << "AsyncBatchGet Finish";

      ASSERT_EQ(batch_get_response.files().size(), 0);
    }
  });
}

TEST_F(AccNamespaceTest, SetDirPolicyAndGetFsStats) {
  FLAGS_write_back_manager_scan_task_interval_ms = 1000000;

  RunCase([this](TestAccNamespace* ns) {
    // get fs stat
    {
      RpcController ctrl;
      GetFsStatsResponseProto response;

      auto s = ns->ns_->GetFsStats(&response, &ctrl);
      LOG(INFO) << "response=" << response.ShortDebugString();
      ASSERT_TRUE(s.IsOK()) << s.ToString();
      ASSERT_EQ(response.inode_uploading(), 0);
    }

    // set policy
    {
      SetDirPolicyRequestProto request;
      request.set_path("/");
      request.mutable_replica_policy()->set_distributed(false);
      request.mutable_replica_policy()->set_local_switch_target(3);
      request.mutable_replica_policy()->set_other_switch_target(0);
      SynchronizedRpcClosure done;
      ns->ns_->AsyncSetDirPolicy("/", &request, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }

    // get fs stat
    {
      RpcController ctrl;
      GetFsStatsResponseProto response;

      auto s = ns->ns_->GetFsStats(&response, &ctrl);
      LOG(INFO) << "response=" << response.ShortDebugString();
      ASSERT_TRUE(s.IsOK()) << s.ToString();
      ASSERT_EQ(response.inode_uploading(), 1);
    }
  });
}

TEST_F(AccNamespaceTest, TestCompleteFileAndEvictFile) {
  FLAGS_enable_ufs_evict_write_cache = true;
  FLAGS_enable_ufs_evict_write_cache_delete_file = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus p;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi, &info);
    info.set_syncinterval(kSyncIntervalNever);

    cnetpp::base::IPAddress client_ip("***********");

    // Test complete file to evict
    {
      std::string inner_src = "/TestCompleteEvict";
      std::string src = ns->JoinUfsPathWithPrefix(inner_src);
      uint64_t fileid = 0;
      ExtendedBlockProto last;
      {
        CreateRequestProto request = TestAccNamespace::GetCreateRequest();
        request.set_src(src);
        request.set_withaddblock(true);
        request.set_rpc_type(RPC_BYTERPC_MODE);
        CreateResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCreateFile(src,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "",
                                     &request,
                                     &response,
                                     &ctrl,
                                     &rpc_done,
                                     info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        fileid = response.fs().fileid();
        last = response.block().b();
        last.set_numbytes(k31M);
      }

      {
        std::shared_ptr<Ufs> ufs = ns->ufs_;
        MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
        std::string key;
        mock_ufs->GetUfsIdentifier(UfsIdentifierInfo(src), &key);
        uint64_t length = 200L * 1024 * 1024;
        mock_ufs->CreateObject(key, length);
      }

      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      {
        CompleteRequestProto request = TestAccNamespace::GetCompleteRequest();
        request.set_src(src);
        request.set_fileid(fileid);
        request.mutable_last()->CopyFrom(last);
        CompleteResponseProto response;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncCompleteFile(
            src, &request, &response, &ctrl, &rpc_done, info);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      }

      {
        cloudfs::GetFileInfoResponseProto rsp;
        SynchronizedRpcClosure rpc_done;
        ns->acc_ns_->AsyncGetFileInfo(src,
                                      NetworkLocationInfo(),
                                      false,
                                      false,
                                      p,
                                      ugi,
                                      info,
                                      &rsp,
                                      &ctrl,
                                      &rpc_done);
        rpc_done.Await();
        ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
        ASSERT_EQ(rsp.fs().length(), k31M);
      }

      // evict
      {
        BlockManager::RepeatedIncBlockReport received_report;
        TestAccNamespace::MakeReport(
            last.blockid(),
            last.generationstamp(),
            last.numbytes(),
            "",  // etag
            "",  // upload id
            "",  // pufs_name
            cloudfs::datanode::ReceivedDeletedBlockInfoProto::EVICT_BLOCK,
            &received_report);
        Status s = ns->block_manager_->IncrementalBlockReport("datanode1",
                                                              received_report);
        ASSERT_TRUE(s.IsOK());
      }

      // deleted
      {
        bool deleted = false;
        for (int i = 0; i < 100; ++i) {
          cloudfs::GetFileInfoResponseProto rsp;
          auto s = ns->ns_->GetFileInfo(
              inner_src, NetworkLocationInfo(), false, false, &rsp, ugi);

          LOG(INFO) << "s=" << s.ToString()
                    << " rsp=" << rsp.ShortDebugString();

          if (s.exception() == JavaExceptions::kFileNotFoundException) {
            deleted = true;
            break;
          }

          if (!rsp.has_fs()) {
            deleted = true;
            break;
          }

          std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        ASSERT_TRUE(deleted);
      }
    }
  });
}

TEST_F(AccNamespaceTest, BatchCreateOverwrite) {
  FLAGS_nn_drive_upload = true;
  FLAGS_complete_rpc_abandon_last_empty_block = true;

  RunCase([this](TestAccNamespace* ns) {
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ns->ufs_.get());

    RpcController ctrl;
    PermissionStatus permission;
    FsPermissionProto fsp;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(60);

    XAttrProto no_upload_attr;
    UfsUploadPolicyProto no_upload_pb;
    no_upload_pb.set_upload_interval_ms(-1);
    no_upload_attr.set_namespace_(cloudfs::XAttrProto::SYSTEM);
    no_upload_attr.set_name("hdfs.upload.policy.string");
    no_upload_attr.set_value(no_upload_pb.SerializeAsString());

    LOG(INFO) << "Init";

    // mkdir
    std::string inner_dir = "/batch_api_dir";
    std::string test_dir = ns->JoinUfsPathWithPrefix(inner_dir);
    {
      MkdirsRequestProto request;
      request.set_src(test_dir);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(false);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      ns->acc_ns_->AsyncMkDirs(test_dir,
                               permission,
                               ugi,
                               false,
                               &request,
                               &response,
                               &ctrl,
                               &done,
                               info);
      done.Await();
      ASSERT_TRUE(done.status().IsOK()) << done.status().ToString();
    }
    LOG(INFO) << "AsyncMkDirs";

    // batch create
    std::string target_file = test_dir + "/file2";
    std::string target_file_inner = inner_dir + "/file2";

    INodeID target_inode_id;
    std::vector<uint64_t> block_ids;

    BatchCreateFileRequestProto batch_create_request;
    BatchCreateFileResponseProto batch_create_response;
    batch_create_request.set_clientname("client_name");
    batch_create_request.set_rpc_type(RPC_BYTERPC_MODE);
    batch_create_request.set_expectediomode(DATANODE_BLOCK_EXPECTED);
    batch_create_request.set_withaddblock(true);
    LOG(INFO) << "Init BatchCreate Proto Start";
    std::vector<std::string> paths;
    {
      // file0
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file0");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file1
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file1");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
      }

      // file2_target
      {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/file2");
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }

      // file2_source
      for (int i = 0; i < 10; ++i) {
        auto file = batch_create_request.add_files();
        file->set_src(test_dir + "/.file2.part." + std::to_string(i));
        paths.push_back(file->src());
        file->mutable_masked()->CopyFrom(fsp);
        file->set_replication(1);
        file->set_blocksize(128 * 1024 * 1024);
        file->add_attr()->CopyFrom(no_upload_attr);
      }
      LOG(INFO) << "Init BatchCreate Proto Finish"
                << " pb=" << batch_create_request.ShortDebugString();

      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = "";
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchCreate(paths,
                                    client_location,
                                    "client_machine-test",
                                    ugi,
                                    &batch_create_request,
                                    &batch_create_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();
      LOG(INFO) << "AsyncBatchCreate batch_create_response="
                << batch_create_response.ShortDebugString();

      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();

      target_inode_id = batch_create_response.files().Get(2).fs().fileid();
    }

    // file3
    {
      auto file = batch_create_request.add_files();
      file->set_src(test_dir + "/file3");
      paths.push_back(file->src());
      file->mutable_masked()->CopyFrom(fsp);
      file->set_replication(1);
      file->set_blocksize(128 * 1024 * 1024);
    }

    // atomic failed
    {
      batch_create_request.set_atomic_flag(true);
      batch_create_request.set_overwrite_flag(
          BatchCreateFileRequestProto::NO_OVERWRITE_UC);
      batch_create_response.Clear();
      LOG(INFO) << "Init BatchCreate Proto Finish"
                << " pb=" << batch_create_request.ShortDebugString();

      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = "";
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchCreate(paths,
                                    client_location,
                                    "client_machine-test",
                                    ugi,
                                    &batch_create_request,
                                    &batch_create_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();
      LOG(INFO) << "AsyncBatchCreate batch_create_response="
                << batch_create_response.ShortDebugString();

      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      ASSERT_EQ(batch_create_response.result(), 0);
      ASSERT_EQ(batch_create_response.files().size(), 0);
    }

    // no atomic, partial success
    {
      batch_create_request.set_atomic_flag(false);
      batch_create_request.set_overwrite_flag(
          BatchCreateFileRequestProto::NO_OVERWRITE_UC);
      batch_create_response.Clear();
      LOG(INFO) << "Init BatchCreate Proto Finish"
                << " pb=" << batch_create_request.ShortDebugString();

      cnetpp::base::IPAddress client_ip("***********");
      auto client_location = NetworkLocationInfo(client_ip);
      client_location.rdma_tag = "";
      SynchronizedRpcClosure rpc_done;
      ns->acc_ns_->AsyncBatchCreate(paths,
                                    client_location,
                                    "client_machine-test",
                                    ugi,
                                    &batch_create_request,
                                    &batch_create_response,
                                    LogRpcInfo(),
                                    &rpc_done,
                                    &ctrl,
                                    info);
      rpc_done.Await();
      LOG(INFO) << "AsyncBatchCreate batch_create_response="
                << batch_create_response.ShortDebugString();

      ASSERT_TRUE(rpc_done.status().IsOK()) << rpc_done.status().ToString();
      ASSERT_EQ(batch_create_response.result(), 1);
      ASSERT_EQ(batch_create_response.files().size(), 1);
    }
  });
}

TEST_F(AccNamespaceTest, AsyncPin) {
  RunCase([this](TestAccNamespace* ns) {
    std::shared_ptr<Ufs> ufs = ns->ufs_;
    MockUfs* mock_ufs = dynamic_cast<MockUfs*>(ufs.get());
    std::string key("testpin.txt");
    ns->CreateObject(key, 1024);

    RpcController ctrl;
    PermissionStatus permission;
    UserGroupInfo ugi;
    cloudfs::AccFsInfo info;
    TestAccNamespace::CreateDefaultPermUgiAccInfo(&permission, &ugi, &info);
    info.set_syncinterval(0);

    std::string ufs_path = ns->JoinUfsPathWithPrefix(key);

    cloudfs::PinRequestProto request;
    request.set_src(ufs_path);
    request.set_recursive(true);
    request.set_unpin(false);
    cloudfs::PinResponseProto response;

    SynchronizedRpcClosure done;
    ns->acc_ns_->AsyncPin(
        ufs_path, &request, &response, info, LogRpcInfo(), &done);
    done.Await();

    Status status = done.status();
    LOG(INFO) << " status:" << status.ToString();
    ASSERT_TRUE(status.IsOK());
  });
}

}  // namespace dancenn
