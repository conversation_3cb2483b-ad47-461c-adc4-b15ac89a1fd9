//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "namespace/locked_path.h"

#include <ClientNamenodeProtocol.pb.h>
#include <glog/logging.h>
#include <gtest/gtest.h>

#include <memory>
#include <random>

#include "absl/strings/str_format.h"
#include "acc/acc.h"
#include "base/file_utils.h"
#include "base/path_util.h"
#include "http/dancenn_admin_handler.h"
#include "namespace/create_flag.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"
#include "test/ufs/mock_ufs.h"

DECLARE_bool(run_ut);
DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_int32(bg_deletion_condition_check_interval);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_string(all_datacenters);
DECLARE_int32(bg_deletion_num_in_batch);
DECLARE_uint32(dfs_summary_min_depth);
DECLARE_bool(force_hyperblock_on_diffrent_dn);
DECLARE_bool(namespace_read_full_detail_blocks);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_listener_enable);
DECLARE_bool(recycle_bin_default_policy_enable);
DECLARE_uint32(recycle_bin_default_policy_time_sec);
DECLARE_int32(dfs_meta_scan_interval_sec);
DECLARE_bool(client_replication_support);
DECLARE_bool(permission_enabled);
DECLARE_string(permission_model);
DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_int32(namespace_type);
DECLARE_bool(append_reuse_last_block);

using namespace cloudfs;

namespace {
auto default_namespace_type = FLAGS_namespace_type;
auto default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
auto default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
auto default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
auto default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
auto default_blockmap_num_bucket_each_slice =
    FLAGS_blockmap_num_bucket_each_slice;
auto default_blockmap_num_slice = FLAGS_blockmap_num_slice;
auto default_dfs_replication_min = FLAGS_dfs_replication_min;
auto default_dfs_meta_storage_inode_key_v2 =
    FLAGS_dfs_meta_storage_inode_key_v2;
auto default_write_back_manager_scan_task_interval_ms =
    FLAGS_write_back_manager_scan_task_interval_ms;
auto default_append_reuse_last_block = FLAGS_append_reuse_last_block;

std::string MakeObjKey(const std::string& prefix, int seq) {
  return absl::StrFormat("%s-%07d", prefix.c_str(), seq);
}

}  // namespace

namespace dancenn {

class LockedPathTestNamespace {
 public:
  void DoSetUp() {
    ugi_ = UserGroupInfo("root", "supergroup");

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

    datanode_manager_ = std::make_shared<dancenn::DatanodeManager>();
    block_manager_.reset(new BlockManager());
    edit_log_ctx_ = CreateContext();
    block_manager_->TestOnlySetEditLogCtx(edit_log_ctx_);
    MockFSImageTransfer(db_path_).Transfer();
    ufs_env_ = UfsEnv::Create();
    acc_ns_ =
        std::make_shared<AccNamespace>(ufs_env_, MockUfs::CreateMockUfs("/"));
    ns_.reset(new MockNameSpace(db_path_,
                                edit_log_ctx_,
                                block_manager_,
                                datanode_manager_,
                                std::make_shared<DataCenters>(),
                                ufs_env_));
    acc_ns_->SetNS(ns_);
    ha_state_ = std::make_unique<MockHAState>();
    safemode_ = std::make_unique<MockSafeMode>();
    ns_->set_safemode(safemode_.get());
    ns_->set_ha_state(ha_state_.get());
    block_manager_->set_ha_state(ha_state_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ns(ns_.get());
    datanode_manager_->set_block_manager(block_manager_.get());
    ns_->Start();
    ns_->StartActive();

    // add a datanode to the cluster
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_ipaddr("***********");
    reg.mutable_datanodeid()->set_hostname("***********");
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    reg.mutable_datanodeid()->set_xferport(1234);
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    StartHeartbeat();
    // mock edit log sender
    auto last_tx_id = ns_->GetLastCkptTxId();
    auto sender = std::unique_ptr<EditLogSenderBase>(
        new MockEditLogSender(edit_log_ctx_, last_tx_id));
    ns_->TestOnlySetEditLogSender(std::move(sender));

    ns_->StopBGDeletionWorker();
    ns_->StopLeaseMonitor();
    ns_->StopMetaScanner();
  }

  void DoTearDown() {
    stop_ = true;
    heartbeat_thread_.join();
    ns_->StopActive();
    ns_->Stop();
    ns_.reset();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  std::shared_ptr<EditLogContextBase> CreateContext() {
    auto c = std::shared_ptr<MockEditLogContext>(new MockEditLogContext);
    c->open_for_read_ = true;
    c->open_for_write_ = false;
    return std::static_pointer_cast<EditLogContextBase>(c);
  }

  void StartHeartbeat() {
    stop_ = false;
    pause_ = false;
    CountDownLatch latch(1);
    heartbeat_thread_ = std::thread([&latch, this]() {
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
      cnetpp::base::IPAddress ip("***********");
      RepeatedStorageReport reports;
      auto r = reports.Add();
      r->set_storageuuid("storage1");
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid("storage1");
      HeartbeatRequestProto request;
      request.mutable_registration()->CopyFrom(reg);
      request.mutable_reports()->CopyFrom(reports);
      bool heartbeated = false;
      while (!stop_) {
        if (!pause_) {
          DatanodeManager::RepeatedCmds cmds;
          datanode_manager_->Heartbeat(request, &cmds);
          if (!heartbeated) {
            heartbeated = true;
            latch.CountDown();
          }
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
      }
    });
    latch.Await();
  }

  static void CreateDefaultPermUgiAccInfo(PermissionStatus* p,
                                          UserGroupInfo* u) {
    p->set_username("root");
    p->set_groupname("");
    p->set_permission(0755);

    *u = UserGroupInfo("root", "");
  }

 private:
  bool stop_;
  bool pause_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safemode_;
  std::shared_ptr<EditLogContextBase> edit_log_ctx_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::thread heartbeat_thread_;
  UserGroupInfo ugi_;

 public:
  std::shared_ptr<MockNameSpace> ns_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<UfsEnv> ufs_env_;
  std::shared_ptr<AccNamespace> acc_ns_;
};

class LockedPathTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_run_ut = true;
    FLAGS_all_datacenters = "LF,HL,LQ";
    FLAGS_lease_expired_soft_limit_ms = 200;
    FLAGS_lease_expired_hard_limit_ms = 400;
    FLAGS_datanode_keep_alive_timeout_sec = 1000;
    FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 3;
    FLAGS_blockmap_num_bucket_each_slice = 1;
    FLAGS_blockmap_num_slice = 1;
    FLAGS_dfs_replication_min = 1;
    FLAGS_dfs_replication_max = 2;
    FLAGS_bytecool_feature_enabled = true;
    FLAGS_dfs_meta_scan_interval_sec = 5;
    FLAGS_recycle_bin_default_policy_enable = false;
    FLAGS_recycle_bin_default_policy_time_sec = 10;
    FLAGS_client_replication_support = true;
    FLAGS_dfs_meta_storage_inode_key_v2 = true;
    FLAGS_write_back_manager_scan_task_interval_ms = 100;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
    FLAGS_append_reuse_last_block = false;

    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to setup "
                   "LockedPathTestNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      auto ns = std::make_unique<LockedPathTestNamespace>();

      ns->DoSetUp();
      test_ns_ = std::move(ns);
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to setup "
                   "LockedPathTestNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
  }

  void TearDown() override {
    FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
    FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
    FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
    FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
    FLAGS_blockmap_num_bucket_each_slice =
        default_blockmap_num_bucket_each_slice;
    FLAGS_blockmap_num_slice = default_blockmap_num_slice;
    FLAGS_dfs_replication_min = default_dfs_replication_min;
    FLAGS_dfs_meta_storage_inode_key_v2 = default_dfs_meta_storage_inode_key_v2;
    FLAGS_write_back_manager_scan_task_interval_ms =
        default_write_back_manager_scan_task_interval_ms;
    FLAGS_namespace_type = default_namespace_type;

    {
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Start to "
                   "teardown LockedPathTestNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
      test_ns_->DoTearDown();
      test_ns_.reset();
      LOG(INFO) << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Finish to "
                   "teardown LockedPathTestNamespace 0 "
                   "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<";
    }
  }

  std::shared_ptr<NameSpace> InnerNs() {
    return test_ns_->ns_;
  }

  void Mkdir(const std::string& path) {
    PermissionStatus p;
    UserGroupInfo ugi;
    LockedPathTestNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi);

    // mkdir
    {
      MkdirsRequestProto request;
      request.set_src(path);
      request.mutable_masked()->set_perm(0);
      request.set_createparent(true);
      MkdirsResponseProto response;
      SynchronizedRpcClosure done;
      test_ns_->ns_->AsyncMkDirs(
          path, p, ugi, true, false, nullptr, LogRpcInfo{}, &done);
      done.Await();
      ASSERT_TRUE(done.status().IsOK());
    }
  }

  static CreateRequestProto GetCreateRequest() {
    CreateRequestProto request;
    request.set_src("");
    request.mutable_masked()->set_perm(0);
    request.set_clientname("client");
    request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
    request.set_replication(1);
    request.set_blocksize(128 * 1024 * 1024);
    request.set_createparent(true);
    return request;
  }

  void CreateFile(const std::string& path) {
    PermissionStatus p;
    UserGroupInfo ugi;
    LockedPathTestNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi);

    // create file
    {
      CreateRequestProto request = GetCreateRequest();
      request.set_src(path);
      CreateResponseProto response;
      SynchronizedRpcClosure done;
      test_ns_->ns_->AsyncCreateFile(path,
                                     p,
                                     NetworkLocationInfo(),
                                     ugi,
                                     LogRpcInfo(),
                                     "xxx",
                                     &request,
                                     &response,
                                     nullptr,
                                     &done);
      done.Await();
      LOG(INFO) << done.status().ToString();
      ASSERT_TRUE(done.status().IsOK());
    }
  }

  void DeleteFile(const std::string& path) {
    PermissionStatus p;
    UserGroupInfo ugi;
    LockedPathTestNamespace::CreateDefaultPermUgiAccInfo(&p, &ugi);

    // delete file
    {
      SynchronizedRpcClosure done;
      test_ns_->ns_->AsyncDelete(path, true, ugi, LogRpcInfo(), &done);
      done.Await();
      LOG(INFO) << done.status().ToString();
      ASSERT_TRUE(done.status().IsOK());
    }
  }

 private:
  std::unique_ptr<LockedPathTestNamespace> test_ns_;
};

TEST_F(LockedPathTest, TestLockedPathRootPathResolve) {
  auto ns = InnerNs();
  // {
  //   LockedPath lp(kPathLockTypeNone, "/", ns.get());
  //   auto s = lp.ResolveAndLock();
  //   ASSERT_TRUE(s.IsOK());
  //   ASSERT_EQ(lp.state(), kResolveOK);
  //   ASSERT_TRUE(lp.path().ancestors.empty());
  //   ASSERT_EQ(lp.GetLastLockedNodePath(), "/");
  //   ASSERT_EQ(lp.locks()->size(), 1);
  //   ASSERT_TRUE(lp.locks()->find("/")->second == nullptr);
  // }
  {
    LockedPath lp(PathLockType::kPathLockTypeRead, "/", ns.get());
    auto s = lp.ResolveAndLock();
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
    ASSERT_TRUE(lp.path().ancestors.empty());
    ASSERT_EQ(lp.GetLastLockedNodePath(), "/");
    ASSERT_EQ(lp.locks()->size(), 1);
    ASSERT_TRUE(lp.locks()->find("/")->second->IsReadLock());
  }
  {
    LockedPath lp(PathLockType::kPathLockTypeWrite, "/", ns.get());
    auto s = lp.ResolveAndLock();
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
    ASSERT_TRUE(lp.path().ancestors.empty());
    ASSERT_EQ(lp.GetLastLockedNodePath(), "/");
    ASSERT_EQ(lp.locks()->size(), 1);
    ASSERT_FALSE(lp.locks()->find("/")->second->IsReadLock());
  }
}
TEST_F(LockedPathTest, TestLockedPathNonRootPathResolveOK) {
  auto ns = InnerNs();
  {
    std::string dir("/TestLockedPathNonRootPathResolveOK/dir");
    Mkdir(dir);
    // {
    //   LockedPath lp(kPathLockTypeNone, dir, ns.get());
    //   auto s = lp.ResolveAndLock();
    //   ASSERT_TRUE(s.IsOK());
    //   ASSERT_EQ(lp.state(), kResolveOK);
    //   ASSERT_EQ(lp.path().ancestors.size(), 2);
    //   ASSERT_EQ(lp.GetLastLockedNodePath(), dir);
    //   ASSERT_EQ(lp.locks()->size(), 3);
    //   for (auto&& p : *(lp.locks())) {
    //     ASSERT_EQ(p.second, nullptr);
    //   }
    // }
    {
      LockedPath lp(PathLockType::kPathLockTypeRead, dir, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size(); ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      LockedPath lp(PathLockType::kPathLockTypeWrite, dir, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size(); ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        if (i == (lock_comps.size() - 1)) {
          ASSERT_FALSE(it->second->IsReadLock());
        } else {
          ASSERT_TRUE(it->second->IsReadLock());
        }
      }
    }
  }
  {
    std::string dir0("/TestLockedPathNonRootPathResolveOK/");
    Mkdir(dir0);
    std::string dir1("/TestLockedPathNonRootPathResolveOK/dir");
    Mkdir(dir1);
    std::string file("/TestLockedPathNonRootPathResolveOK/dir/file");
    CreateFile(file);
    // {
    //   LockedPath lp(kPathLockTypeNone, file, ns.get());
    //   auto s = lp.ResolveAndLock();
    //   ASSERT_TRUE(s.IsOK());
    //   ASSERT_EQ(lp.state(), kResolveOK);
    //   ASSERT_EQ(lp.path().ancestors.size(), 3);
    //   ASSERT_EQ(lp.GetLastLockedNodePath(), file);
    //   ASSERT_EQ(lp.locks()->size(), 4);
    //   for (auto&& p : *(lp.locks())) {
    //     ASSERT_EQ(p.second, nullptr);
    //   }
    // }
    {
      LockedPath lp(PathLockType::kPathLockTypeRead, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 4);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size(); ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 4);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size(); ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        if (i == (lock_comps.size() - 1)) {
          ASSERT_FALSE(it->second->IsReadLock());
        } else {
          ASSERT_TRUE(it->second->IsReadLock());
        }
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathNonRootPathResolveAncestorNotDir) {
  auto ns = InnerNs();
  {
    std::string dir("/TestLockedPathNonRootPathResolveAncestorNotDir");
    Mkdir(dir);
    std::string tmp_file(
        "/TestLockedPathNonRootPathResolveAncestorNotDir/file");
    CreateFile(tmp_file);
    std::string file = tmp_file + "/file2";
    // {
    //   LockedPath lp(kPathLockTypeNone, file, ns.get());
    //   auto s = lp.ResolveAndLock();
    //   ASSERT_TRUE(s.IsOK());
    //   ASSERT_EQ(lp.state(), kResolveAncestorNotDir);
    //   ASSERT_EQ(lp.path().ancestors.size(), 2);
    //   ASSERT_EQ(lp.GetLastLockedNodePath(), tmp_file);
    //   ASSERT_EQ(lp.locks()->size(), 3);
    //   for (auto&& p : *(lp.locks())) {
    //     ASSERT_EQ(p.second, nullptr);
    //   }
    // }
    {
      LockedPath lp(PathLockType::kPathLockTypeRead, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotDir);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), tmp_file);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size() - 1; ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotDir);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), tmp_file);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size() - 1; ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathNonRootPathResolveAncestorNotFound) {
  auto ns = InnerNs();
  {
    std::string dir("/TestLockedPathNonRootPathResolveAncestorNotFound");
    Mkdir(dir);
    std::string non_existed_dir = dir + "/non-existed";
    std::string file = non_existed_dir + "/file";
    // {
    //   LockedPath lp(kPathLockTypeNone, file, ns.get());
    //   auto s = lp.ResolveAndLock();
    //   ASSERT_TRUE(s.IsOK());
    //   ASSERT_EQ(lp.state(), kResolveAncestorNotFound);
    //   ASSERT_EQ(lp.path().ancestors.size(), 2);
    //   ASSERT_EQ(lp.GetLastLockedNodePath(), non_existed_dir);
    //   ASSERT_EQ(lp.locks()->size(), 3);
    //   for (auto&& p : *(lp.locks())) {
    //     ASSERT_EQ(p.second, nullptr);
    //   }
    // }
    {
      LockedPath lp(PathLockType::kPathLockTypeRead, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), non_existed_dir);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < lock_comps.size() - 1; ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 2);
      ASSERT_EQ(lp.GetLastLockedNodePath(), non_existed_dir);
      ASSERT_EQ(lp.locks()->size(), 3);
      auto&& lock_comps = lp.path().lock_comps;
      for (size_t i = 0; i < 2; ++i) {
        auto it = lp.locks()->find(lock_comps[i]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto it = lp.locks()->find(lock_comps[2]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathDowngradeUpgradeLastLock) {
  auto ns = InnerNs();
  {
    std::string dir0("/TestLockedPathNonRootPathResolveOK");
    Mkdir(dir0);
    std::string dir1("/TestLockedPathNonRootPathResolveOK/dir");
    Mkdir(dir1);
    std::string file("/TestLockedPathNonRootPathResolveOK/dir/file");
    CreateFile(file);
    {
      LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveOK);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 4);
      auto&& lock_comps = lp.path().lock_comps;
      {
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }

      s = lp.DowngradeToReadLock();
      ASSERT_TRUE(s.IsOK());
      {
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      s = lp.UpgradeToWriteLock();
      ASSERT_TRUE(s.IsOK());
      {
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathDowngradeUpgradeLock) {
  auto ns = InnerNs();
  {
    std::string dir1("/TestLockedPathDowngradeUpgradeLock/dir1");
    std::string dir2("/TestLockedPathDowngradeUpgradeLock/dir1/dir2");
    std::string file("/TestLockedPathDowngradeUpgradeLock/dir1/dir2/file");
    Mkdir(dir1);
    LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
    {
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir2);
      ASSERT_EQ(lp.locks()->size(), 4);
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
    {
      std::shared_ptr<INode> inode_dir2 = std::make_shared<INode>();
      inode_dir2->set_id(16388);
      inode_dir2->set_type(INode_Type_kDirectory);
      inode_dir2->set_parent_id(16387);
      inode_dir2->set_name("dir2");
      inode_dir2->mutable_permission()->set_permission(644);
      inode_dir2->mutable_permission()->set_groupname("");
      inode_dir2->mutable_permission()->set_username("");
      inode_dir2->set_mtime(0);
      inode_dir2->set_atime(0);
      ns->meta_storage()->InsertINode(inode_dir2);
    }
    {
      auto s = lp.DowngradeToReadLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      s = lp.UpgradeToWriteLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathDowngradeUpgradeLock2) {
  auto ns = InnerNs();
  {
    std::string dir1("/TestLockedPathDowngradeUpgradeLock/dir1");
    std::string dir2("/TestLockedPathDowngradeUpgradeLock/dir1/dir2");
    std::string file("/TestLockedPathDowngradeUpgradeLock/dir1/dir2/file");
    Mkdir(dir1);
    LockedPath lp(PathLockType::kPathLockTypeRead, file, ns.get());
    {
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir2);
      ASSERT_EQ(lp.locks()->size(), 4);
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      std::shared_ptr<INode> inode_dir2 = std::make_shared<INode>();
      inode_dir2->set_id(16388);
      inode_dir2->set_type(INode_Type_kDirectory);
      inode_dir2->set_parent_id(16387);
      inode_dir2->set_name("dir2");
      inode_dir2->mutable_permission()->set_permission(644);
      inode_dir2->mutable_permission()->set_groupname("");
      inode_dir2->mutable_permission()->set_username("");
      inode_dir2->set_mtime(0);
      inode_dir2->set_atime(0);
      ns->meta_storage()->InsertINode(inode_dir2);
    }
    {
      auto s = lp.UpgradeToWriteLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
      s = lp.DowngradeToReadLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathDowngradeUpgradeLock3) {
  auto ns = InnerNs();
  {
    std::string dir1("/TestLockedPathDowngradeUpgradeLock/dir1");
    std::string dir2("/TestLockedPathDowngradeUpgradeLock/dir1/dir2");
    std::string file("/TestLockedPathDowngradeUpgradeLock/dir1/dir2/file");
    Mkdir(dir1);
    LockedPath lp(PathLockType::kPathLockTypeRead, file, ns.get());
    {
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir2);
      ASSERT_EQ(lp.locks()->size(), 4);
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    {
      std::shared_ptr<INode> inode_dir2 = std::make_shared<INode>();
      inode_dir2->set_id(16388);
      inode_dir2->set_type(INode_Type_kDirectory);
      inode_dir2->set_parent_id(16387);
      inode_dir2->set_name("dir2");
      inode_dir2->mutable_permission()->set_permission(644);
      inode_dir2->mutable_permission()->set_groupname("");
      inode_dir2->mutable_permission()->set_username("");
      inode_dir2->set_mtime(0);
      inode_dir2->set_atime(0);
      ns->meta_storage()->InsertINode(inode_dir2);
    }
    {
      auto s = lp.DowngradeToReadLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      s = lp.UpgradeToWriteLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathDowngradeUpgradeLock4) {
  auto ns = InnerNs();
  {
    std::string dir1("/TestLockedPathDowngradeUpgradeLock/dir1");
    std::string dir2("/TestLockedPathDowngradeUpgradeLock/dir1/dir2");
    std::string file("/TestLockedPathDowngradeUpgradeLock/dir1/dir2/file");
    Mkdir(dir1);
    LockedPath lp(PathLockType::kPathLockTypeWrite, file, ns.get());
    {
      auto s = lp.ResolveAndLock();
      ASSERT_TRUE(s.IsOK());
      ASSERT_EQ(lp.state(), ResolveState::kResolveAncestorNotFound);
      ASSERT_EQ(lp.path().ancestors.size(), 3);
      ASSERT_EQ(lp.GetLastLockedNodePath(), dir2);
      ASSERT_EQ(lp.locks()->size(), 4);
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
    {
      std::shared_ptr<INode> inode_dir2 = std::make_shared<INode>();
      inode_dir2->set_id(16388);
      inode_dir2->set_type(INode_Type_kDirectory);
      inode_dir2->set_parent_id(16387);
      inode_dir2->set_name("dir2");
      inode_dir2->mutable_permission()->set_permission(644);
      inode_dir2->mutable_permission()->set_groupname("");
      inode_dir2->mutable_permission()->set_username("");
      inode_dir2->set_mtime(0);
      inode_dir2->set_atime(0);
      ns->meta_storage()->InsertINode(inode_dir2);
    }
    {
      auto s = lp.UpgradeToWriteLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
      s = lp.DowngradeToReadLock();
      ASSERT_EQ(lp.path().ancestors.size(), 4);
      ASSERT_EQ(lp.GetLastLockedNodePath(), file);
      ASSERT_EQ(lp.locks()->size(), 5);
      ASSERT_TRUE(s.IsOK());
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[3]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto&& lock_comps = lp.path().lock_comps;
        auto it = lp.locks()->find(lock_comps[4]);
        ASSERT_TRUE(it != lp.locks()->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
  }
}

TEST_F(LockedPathTest, TestLockedPathVectorResolveOK) {
  auto ns = InnerNs();
  std::string dir0("/TestLockedPathVector");
  Mkdir(dir0);
  std::string dir1("/TestLockedPathVector/dir1");
  Mkdir(dir1);
  std::string dir2("/TestLockedPathVector/dir2");
  Mkdir(dir2);
  std::string file1("/TestLockedPathVector/dir1/file1");
  CreateFile(file1);
  std::string file2("/TestLockedPathVector/dir2/file2");
  CreateFile(file2);
  std::vector<std::string> files{file1, file2};
  {
    LockedPathVector lp(ns.get(), files, PathLockType::kPathLockTypeWrite);
    auto s = lp.ResolveAndLock();
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(lp.GetRichPath(0).path, file1);
    ASSERT_EQ(lp.GetRichPath(0).ancestors.size(), 3);
    ASSERT_EQ(lp.GetResolveState(0), ResolveState::kResolveOK);
    auto&& first_locks = lp.GetLocks(0);
    {
      auto lock_comps = lp.GetRichPath(0).lock_comps;
      ASSERT_EQ(first_locks->size(), 4);
      for (size_t i = 0; i < first_locks->size() - 1; ++i) {
        auto it = first_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != first_locks->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto it = first_locks->find(lock_comps[first_locks->size() - 1]);
        ASSERT_TRUE(it != first_locks->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
    ASSERT_EQ(lp.GetRichPath(1).path, file2);
    ASSERT_EQ(lp.GetRichPath(1).ancestors.size(), 3);
    ASSERT_EQ(lp.GetResolveState(1), ResolveState::kResolveOK);
    {
      auto lock_comps = lp.GetRichPath(1).lock_comps;
      auto&& second_locks = lp.GetLocks(1);
      ASSERT_EQ(second_locks->size(), 4);
      for (size_t i = 0; i < 2; ++i) {
        auto it = second_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != second_locks->end());
        ASSERT_TRUE(it->second == nullptr);
      }
      for (size_t i = 2; i < second_locks->size() - 1; ++i) {
        auto it = second_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != second_locks->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
      {
        auto it = second_locks->find(lock_comps[second_locks->size() - 1]);
        ASSERT_TRUE(it != second_locks->end());
        ASSERT_FALSE(it->second->IsReadLock());
      }
    }
  }
  {
    LockedPathVector lp(ns.get(), files, PathLockType::kPathLockTypeRead);
    auto s = lp.ResolveAndLock();
    ASSERT_TRUE(s.IsOK());
    ASSERT_EQ(lp.GetRichPath(0).path, file1);
    ASSERT_EQ(lp.GetRichPath(0).ancestors.size(), 3);
    ASSERT_EQ(lp.GetResolveState(0), ResolveState::kResolveOK);
    auto&& first_locks = lp.GetLocks(0);
    {
      auto lock_comps = lp.GetRichPath(0).lock_comps;
      ASSERT_EQ(first_locks->size(), 4);
      for (size_t i = 0; i < first_locks->size(); ++i) {
        auto it = first_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != first_locks->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
    ASSERT_EQ(lp.GetRichPath(1).path, file2);
    ASSERT_EQ(lp.GetRichPath(1).ancestors.size(), 3);
    ASSERT_EQ(lp.GetResolveState(1), ResolveState::kResolveOK);
    {
      auto lock_comps = lp.GetRichPath(1).lock_comps;
      auto&& second_locks = lp.GetLocks(1);
      ASSERT_EQ(second_locks->size(), 4);
      for (size_t i = 0; i < 2; ++i) {
        auto it = second_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != second_locks->end());
        ASSERT_TRUE(it->second == nullptr);
      }
      for (size_t i = 2; i < second_locks->size(); ++i) {
        auto it = second_locks->find(lock_comps[i]);
        ASSERT_TRUE(it != second_locks->end());
        ASSERT_TRUE(it->second->IsReadLock());
      }
    }
  }
  // {
  //   LockedPathVector lp(ns.get(), files, kPathLockTypeNone);
  //   auto s = lp.ResolveAndLock();
  //   ASSERT_TRUE(s.IsOK());
  //   ASSERT_EQ(lp.GetRichPath(0).path, file1);
  //   ASSERT_EQ(lp.GetRichPath(0).ancestors.size(), 3);
  //   ASSERT_EQ(lp.GetResolveState(0), kResolveOK);
  //   auto&& first_locks = lp.GetLocks(0);
  //   {
  //     auto lock_comps = lp.GetRichPath(0).lock_comps;
  //     ASSERT_EQ(first_locks->size(), 4);
  //     for (size_t i = 0; i < first_locks->size(); ++i) {
  //       auto it = first_locks->find(lock_comps[i]);
  //       ASSERT_TRUE(it != first_locks->end());
  //       ASSERT_TRUE(it->second == nullptr);
  //     }
  //   }
  //   ASSERT_EQ(lp.GetRichPath(1).path, file2);
  //   ASSERT_EQ(lp.GetRichPath(1).ancestors.size(), 3);
  //   ASSERT_EQ(lp.GetResolveState(1), kResolveOK);
  //   {
  //     auto lock_comps = lp.GetRichPath(1).lock_comps;
  //     auto&& second_locks = lp.GetLocks(1);
  //     ASSERT_EQ(second_locks->size(), 4);
  //     for (size_t i = 0; i < second_locks->size(); ++i) {
  //       auto it = second_locks->find(lock_comps[i]);
  //       ASSERT_TRUE(it != second_locks->end());
  //       ASSERT_TRUE(it->second == nullptr);
  //     }
  //   }
  // }
}

TEST_F(LockedPathTest, LockedPathTestThreeWay) {
  auto ns = InnerNs();
  std::string dir0("/TestLockedPathVector");
  Mkdir(dir0);
  std::string dir1("/TestLockedPathVector/dir1");
  Mkdir(dir1);
  std::string dir2("/TestLockedPathVector/dir2");
  Mkdir(dir2);
  std::string dir3("/.RECYCLE_BIN");
  Mkdir(dir3);
  std::string dir4("/.RECYCLE_BIN/user");
  Mkdir(dir4);
  std::string file1("/TestLockedPathVector/dir1/file1");
  CreateFile(file1);
  std::string file2("/TestLockedPathVector/dir2/file2");
  CreateFile(file2);
  std::string file3("/.RECYCLE_BIN/user/z-uuid-TestLockedPathVector-dir2-file2");
  CreateFile(file3);
  std::vector<std::string> files{file1, file2, file3};

  LockedPathVector lp(ns.get(), files, PathLockType::kPathLockTypeWrite);
  auto s = lp.ResolveAndLock();
  ASSERT_TRUE(s.IsOK());
  ASSERT_EQ(lp.GetRichPath(0).path, file3);
  ASSERT_EQ(lp.GetRichPath(0).ancestors.size(), 3);
  ASSERT_EQ(lp.GetResolveState(0), ResolveState::kResolveOK);
  auto&& first_locks = lp.GetLocks(0);
  {
    auto lock_comps = lp.GetRichPath(0).lock_comps;
    ASSERT_EQ(first_locks->size(), 4);
    for (size_t i = 0; i < first_locks->size() - 1; ++i) {
      auto it = first_locks->find(lock_comps[i]);
      ASSERT_TRUE(it != first_locks->end());
      ASSERT_TRUE(it->second->IsReadLock());
    }
    {
      auto it = first_locks->find(lock_comps[first_locks->size() - 1]);
      ASSERT_TRUE(it != first_locks->end());
      ASSERT_FALSE(it->second->IsReadLock());
    }
  }
  ASSERT_EQ(lp.GetRichPath(1).path, file1);
  ASSERT_EQ(lp.GetRichPath(1).ancestors.size(), 3);
  ASSERT_EQ(lp.GetResolveState(1), ResolveState::kResolveOK);
  {
    auto lock_comps = lp.GetRichPath(1).lock_comps;
    auto&& second_locks = lp.GetLocks(1);
    ASSERT_EQ(second_locks->size(), 4);
    for (size_t i = 0; i < 1; ++i) {
      auto it = second_locks->find(lock_comps[i]);
      ASSERT_TRUE(it != second_locks->end());
      ASSERT_TRUE(it->second == nullptr);
    }
    for (size_t i = 1; i < second_locks->size() - 1; ++i) {
      auto it = second_locks->find(lock_comps[i]);
      ASSERT_TRUE(it != second_locks->end());
      ASSERT_TRUE(it->second->IsReadLock());
    }
    {
      auto it = second_locks->find(lock_comps[second_locks->size() - 1]);
      ASSERT_TRUE(it != second_locks->end());
      ASSERT_FALSE(it->second->IsReadLock());
    }
  }
  ASSERT_EQ(lp.GetRichPath(2).path, file2);
  ASSERT_EQ(lp.GetRichPath(2).ancestors.size(), 3);
  ASSERT_EQ(lp.GetResolveState(2), ResolveState::kResolveOK);
  {
    auto lock_comps = lp.GetRichPath(2).lock_comps;
    auto&& third_locks = lp.GetLocks(2);
    ASSERT_EQ(third_locks->size(), 4);
    for (size_t i = 0; i < 2; ++i) {
      auto it = third_locks->find(lock_comps[i]);
      ASSERT_TRUE(it != third_locks->end());
      ASSERT_TRUE(it->second == nullptr);
    }
    for (size_t i = 2; i < third_locks->size() - 1; ++i) {
      auto it = third_locks->find(lock_comps[i]);
      ASSERT_TRUE(it != third_locks->end());
      ASSERT_TRUE(it->second->IsReadLock());
    }
    {
      auto it = third_locks->find(lock_comps[third_locks->size() - 1]);
      ASSERT_TRUE(it != third_locks->end());
      ASSERT_FALSE(it->second->IsReadLock());
    }
  }
}

TEST_F(LockedPathTest, LockedPathTestMissing) {
  auto ns = InnerNs();
  std::string file1("/TestLockedPathVector/dir1/file1");
  std::string file2("/TestLockedPathVector/dir1/file2");
  std::string file3("/TestLockedPathVector/dir1/file3");
  std::vector<std::string> files{file1, file2, file3};

  LockedPathVector lp(ns.get(), files, PathLockType::kPathLockTypeWrite);
  lp.ResolveAndLock();
}

}  // namespace dancenn
