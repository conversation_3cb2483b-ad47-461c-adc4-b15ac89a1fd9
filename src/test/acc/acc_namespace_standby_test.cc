//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include <absl/strings/str_format.h>
#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include <memory>
#include <string>

#include "base/closure.h"
#include "base/file_utils.h"
#include "base/retry_cache.h"
#include "block_manager/block_manager.h"
#include "datanode_manager/data_centers.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/edit_log_cfs_op.h"
#include "ha/ha_state_base.h"
#include "namespace/namespace.h"
#include "namespace/namespace_metrics.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "proto/generated/dancenn/edit_log.pb.h"
#include "proto/generated/dancenn/inode.pb.h"
#include "server/dancenn.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"
#include "test/ufs/mock_ufs.h"

DECLARE_bool(dancenn_observe_mode_on);
DECLARE_bool(retry_cache_enabled);
DECLARE_int32(namespace_type);
DECLARE_bool(enable_write_back_task_persistence);

namespace dancenn {

using cloudfs::BlockProto;

class AccNamespaceStandbyTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_dancenn_observe_mode_on = false;
    FLAGS_retry_cache_enabled = true;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;

    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    MockFSImageTransfer(db_path_).Transfer();

    ufs_ = MockUfs::CreateMockUfs("/");

    runtime_.datanode_manager = std::make_shared<DatanodeManager>();
    runtime_.block_manager = std::make_unique<BlockManager>();
    runtime_.data_centers = std::make_shared<DataCenters>();
    runtime_.mock_ha_state = std::make_shared<MockHAState>();
    runtime_.mock_safemode = std::make_shared<MockSafeMode>();
    runtime_.edit_log_ctx = std::make_shared<MockEditLogContext>();
    runtime_.rc = std::make_unique<RetryCache>();
    runtime_.ufs_env = UfsEnv::Create();

    runtime_.ufs_env->AddUfs(ufs_);

    runtime_.acc_namespace =
        std::make_shared<AccNamespace>(runtime_.ufs_env, ufs_);
    runtime_.ns =
        std::shared_ptr<NameSpace>(new MockNameSpace(db_path_,
                                                     runtime_.edit_log_ctx,
                                                     runtime_.block_manager,
                                                     runtime_.datanode_manager,
                                                     runtime_.data_centers,
                                                     runtime_.ufs_env,
                                                     runtime_.rc.get()));

    acc_ns_ = CHECK_NOTNULL(runtime_.acc_namespace.get());
    block_manager_ = CHECK_NOTNULL(runtime_.block_manager.get());
    ns_ = CHECK_NOTNULL(runtime_.ns.get());
    datanode_manager_ = CHECK_NOTNULL(runtime_.datanode_manager.get());
    ufs_env_ = CHECK_NOTNULL(runtime_.ufs_env.get());

    meta_storage_ = ns_->TestOnlyGetMetaStorage();
    block_manager_->SetMetaStorage(meta_storage_);

    runtime_.InjectDependency4Test();

    ns_->Start();
    ns_->StartStandby();
    ufs_env_->Start();

    // add a datanode to the cluster
    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_ipaddr("***********");
    reg.mutable_datanodeid()->set_hostname("***********");
    reg.mutable_datanodeid()->set_datanodeuuid("datanode1");
    reg.mutable_datanodeid()->set_xferport(1234);
    reg.mutable_datanodeid()->set_infoport(1234);
    reg.mutable_datanodeid()->set_ipcport(1234);
    cnetpp::base::IPAddress ip("***********");
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
    datanode_manager_->RefreshConfig();

    last_inode_id_ = ns_->NextINodeId();
    LOG(INFO) << "AccNamespaceStandbyTest last_inode_id_: " << last_inode_id_;
  }

  void TearDown() override {
    FLAGS_namespace_type = 1;

    ns_->StopStandby();

    runtime_.Stop();

    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  PermissionStatus GetDefaultAccFilePermission() {
    return MakePermissionStatus("root", "supergroup", 0755);
  }

  PermissionStatus MakePermissionStatus(const std::string& username,
                                        const std::string& groupname,
                                        uint32_t permission) {
    PermissionStatus p;
    p.set_username(username);
    p.set_groupname(groupname);
    p.set_permission(permission);
    return p;
  }

  BlockProto MakeBlockProto(uint64_t id, uint64_t gs, uint64_t num_bytes) {
    BlockProto bp;
    bp.set_blockid(id);
    bp.set_genstamp(gs);
    bp.set_numbytes(num_bytes);
    return std::move(bp);
  }

  BlockInfoProto MakeBlockInfoProto(uint64_t id,
                                    uint64_t gs,
                                    uint64_t num_bytes,
                                    uint64_t inode_id) {
    BlockInfoProto bp;
    bp.set_state(BlockInfoProto::kPersisted);
    bp.set_block_id(id);
    bp.set_gen_stamp(gs);
    bp.set_num_bytes(num_bytes);
    bp.set_inode_id(inode_id);
    bp.set_expected_rep(1);
    return std::move(bp);
  }

  uint64_t NextINodeId() {
    return ++last_inode_id_;
  }
  uint64_t NextBlockId() {
    return ++last_block_id_;
  }
  uint64_t NextGS() {
    return ++last_gs_;
  }
  uint64_t NextTxId() {
    return ++last_txid_;
  }

  static void MakeReport(
      BlockID block_id,
      uint64_t gs,
      uint32_t len,
      const std::string& etag,
      const std::string& uploadid,
      cloudfs::datanode::ReceivedDeletedBlockInfoProto_BlockStatus state,
      BlockManager::RepeatedIncBlockReport* report) {
    auto r = report->Add();
    r->set_storageuuid("storage1");
    auto b = r->add_blocks();
    b->mutable_block()->set_blockid(block_id);
    b->mutable_block()->set_genstamp(gs);
    b->mutable_block()->set_numbytes(len);
    b->set_status(state);
    b->set_etag(etag);
    b->set_uploadid(uploadid);
  }

 protected:
  DanceNNRuntime runtime_;
  std::shared_ptr<MetaStorage> meta_storage_{nullptr};
  std::shared_ptr<Ufs> ufs_{nullptr};

  AccNamespace* acc_ns_{nullptr};
  BlockManager* block_manager_{nullptr};
  NameSpace* ns_{nullptr};
  DatanodeManager* datanode_manager_{nullptr};
  UfsEnv* ufs_env_{nullptr};

  std::string db_path_ = "rocksdb_XXXXXX";

  std::atomic<uint64_t> last_inode_id_{0};
  std::atomic<uint64_t> last_block_id_{12345678};
  std::atomic<uint64_t> last_txid_{0};
  std::atomic<uint64_t> last_gs_{1};
};

TEST_F(AccNamespaceStandbyTest, TestBatchAddRootDir) {
  uint64_t block_size = 127UL * 1024 * 1024;
  auto op = std::make_shared<OpAccSyncListingBatchAdd>();
  uint64_t last_id = 0;
  {
    op->SetTxid(NextTxId());

    INode root = meta_storage_->GetRootINode();
    AccSyncListingBatchAddOpBody body;
    body.set_dir_path("/");
    body.set_allocated_dir_inode(new INode(root));
    for (int i = 0; i < 1000; ++i) {
      uint64_t inode_id = NextINodeId();
      last_id = inode_id;
      INode inode;
      std::string filename = absl::StrFormat("file-%05d", i);
      MakeINode(inode_id,
                root.id(),
                filename,
                GetDefaultAccFilePermission(),
                INode_Type_kFile,
                &inode);

      auto subfile = body.add_files_to_add();
      subfile->set_allocated_inode(new INode(inode));
      for (int k = 0; k < 2; ++k) {
        auto block_info = subfile->add_block_info();
        *block_info =
            MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
      }
    }
    op->SetProto(std::move(body));
  }

  ns_->Apply(op);
  ns_->WaitNoPending();

  // Verify inodes in standby
  {
    bool has_more = true;
    std::vector<INode> subfiles;
    meta_storage_->GetSubINodes(kRootINodeId, "", 1001, &subfiles, &has_more);
    ASSERT_EQ(has_more, false);
    ASSERT_EQ(subfiles.size(), 1000);
    for (int i = 0; i < 1000; ++i) {
      std::string filename = absl::StrFormat("file-%05d", i);
      ASSERT_EQ(filename, subfiles[i].name());
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}

TEST_F(AccNamespaceStandbyTest, TestBatchAddNonRootDir) {
  // Mkdir
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  const uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t dir_inode_id = NextINodeId();
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);

  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }
  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();

    INode dir_inode_on_disk;
    ASSERT_EQ(
        ns_->GetMetaStorage()->GetINode(dir_inode.id(), &dir_inode_on_disk),
        kOK);
    INode tmp_dir_inode = dir_inode_on_disk;
    tmp_dir_inode.clear_create_txid();
    tmp_dir_inode.clear_last_update_txid();
    tmp_dir_inode.clear_pin_status();
    EXPECT_EQ(tmp_dir_inode.ShortDebugString(), dir_inode.ShortDebugString());
    dir_inode = dir_inode_on_disk;
  }
  uint64_t last_id = 0;
  {
    auto op = std::make_shared<OpAccSyncListingBatchAdd>();
    {
      op->SetTxid(NextTxId());

      AccSyncListingBatchAddOpBody body;
      body.set_dir_path("/a");
      body.set_allocated_dir_inode(new INode(dir_inode));
      for (int i = 0; i < 1000; ++i) {
        uint64_t inode_id = NextINodeId();
        last_id = inode_id;
        INode inode;
        std::string filename = absl::StrFormat("file-%05d", i);
        MakeINode(inode_id,
                  dir_inode.id(),
                  filename,
                  GetDefaultAccFilePermission(),
                  INode_Type_kFile,
                  &inode);

        auto subfile = body.add_files_to_add();
        subfile->set_allocated_inode(new INode(inode));
        for (int k = 0; k < 2; ++k) {
          auto block_info = subfile->add_block_info();
          *block_info =
              MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
        }
      }
      op->SetProto(std::move(body));
    }

    ns_->Apply(op);
    ns_->WaitNoPending();

    // Verify inodes in standby
    {
      bool has_more = true;
      std::vector<INode> subfiles;
      meta_storage_->GetSubINodes(
          dir_inode.id(), "", 1001, &subfiles, &has_more);
      ASSERT_EQ(has_more, false);
      ASSERT_EQ(subfiles.size(), 1000);
      for (int i = 0; i < 1000; ++i) {
        std::string filename = absl::StrFormat("file-%05d", i);
        ASSERT_EQ(filename, subfiles[i].name());
      }
      ASSERT_EQ(last_id, ns_->LastInodeId());
    }
  }
}

TEST_F(AccNamespaceStandbyTest, TestBatchUpdateRootDir) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  std::vector<INode> files;
  files.reserve(1024);
  {
    // First add some files
    auto op = std::make_shared<OpAccSyncListingBatchAdd>();
    {
      op->SetTxid(NextTxId());
      AccSyncListingBatchAddOpBody body;
      body.set_dir_path("/");
      body.set_allocated_dir_inode(new INode(root));
      for (int i = 0; i < 1000; ++i) {
        uint64_t inode_id = NextINodeId();
        last_id = inode_id;
        INode inode;
        std::string filename = absl::StrFormat("file-%05d", i);
        MakeINode(inode_id,
                  root.id(),
                  filename,
                  GetDefaultAccFilePermission(),
                  INode_Type_kFile,
                  &inode);
        {
          auto info = inode.mutable_ufs_file_info();
          info->set_file_state(UfsFileState::kUfsFileStatePersisted);
          info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeInvalid);
          info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeInvalid);
          info->set_etag("dummy");
          info->set_size(block_size * 2);
          info->set_last_modified_ts(now_ts);
          info->set_sync_ts(now_ts);
        }

        auto subfile = body.add_files_to_add();
        subfile->set_allocated_inode(new INode(inode));
        for (int k = 0; k < 2; ++k) {
          auto block_info = subfile->add_block_info();
          *block_info =
              MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
        }
        files.emplace_back(inode);
      }
      op->SetProto(std::move(body));
    }

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // Second, update these files
    for (auto&& inode : files) {
      inode.mutable_ufs_file_info()->set_sync_ts(now_ts + 1);
    }

    auto op = std::make_shared<OpAccSyncListingBatchUpdate>();
    op->SetTxid(NextTxId());
    {
      AccSyncListingBatchUpdateOpBody body;
      body.set_dir_path("/");
      body.set_allocated_dir_inode(new INode(root));
      for (auto&& inode : files) {
        *(body.add_files_to_update()) = inode;
      }
      op->SetProto(std::move(body));
    }

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  // Verify inodes in standby
  {
    bool has_more = true;
    std::vector<INode> subfiles;
    meta_storage_->GetSubINodes(kRootINodeId, "", 1001, &subfiles, &has_more);
    ASSERT_EQ(has_more, false);
    ASSERT_EQ(subfiles.size(), 1000);
    for (int i = 0; i < 1000; ++i) {
      std::string filename = absl::StrFormat("file-%05d", i);
      ASSERT_EQ(filename, subfiles[i].name());
      ASSERT_EQ("dummy", subfiles[i].ufs_file_info().etag());
      ASSERT_EQ(block_size * 2, subfiles[i].ufs_file_info().size());
      ASSERT_EQ(now_ts + 1, subfiles[i].ufs_file_info().sync_ts());
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}
TEST_F(AccNamespaceStandbyTest, TestBatchUpdateNonRootDir) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  // Mkdir
  uint64_t dir_inode_id = NextINodeId();
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);

  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }
  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  std::vector<INode> files;
  files.reserve(1024);
  {
    // First add some files
    auto op = std::make_shared<OpAccSyncListingBatchAdd>();
    {
      op->SetTxid(NextTxId());
      AccSyncListingBatchAddOpBody body;
      body.set_dir_path("/");
      body.set_allocated_dir_inode(new INode(root));
      for (int i = 0; i < 1000; ++i) {
        uint64_t inode_id = NextINodeId();
        last_id = inode_id;
        INode inode;
        std::string filename = absl::StrFormat("file-%05d", i);
        MakeINode(inode_id,
                  dir_inode.id(),
                  filename,
                  GetDefaultAccFilePermission(),
                  INode_Type_kFile,
                  &inode);
        {
          auto info = inode.mutable_ufs_file_info();
          info->set_file_state(UfsFileState::kUfsFileStatePersisted);
          info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeInvalid);
          info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeInvalid);
          info->set_etag("dummy");
          info->set_size(block_size * 2);
          info->set_last_modified_ts(now_ts);
          info->set_sync_ts(now_ts);
        }

        auto subfile = body.add_files_to_add();
        subfile->set_allocated_inode(new INode(inode));
        for (int k = 0; k < 2; ++k) {
          auto block_info = subfile->add_block_info();
          *block_info =
              MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
        }
        files.emplace_back(inode);
      }
      op->SetProto(std::move(body));
    }

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // Second, update these files
    for (auto&& inode : files) {
      inode.mutable_ufs_file_info()->set_sync_ts(now_ts + 1);
    }

    auto op = std::make_shared<OpAccSyncListingBatchUpdate>();
    op->SetTxid(NextTxId());
    {
      AccSyncListingBatchUpdateOpBody body;
      body.set_dir_path("/");
      body.set_allocated_dir_inode(new INode(root));
      for (auto&& inode : files) {
        *(body.add_files_to_update()) = inode;
      }
      op->SetProto(std::move(body));
    }

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  // Verify inodes in standby
  {
    bool has_more = true;
    std::vector<INode> subfiles;
    meta_storage_->GetSubINodes(dir_inode.id(), "", 1001, &subfiles, &has_more);
    ASSERT_EQ(has_more, false);
    ASSERT_EQ(subfiles.size(), 1000);
    for (int i = 0; i < 1000; ++i) {
      std::string filename = absl::StrFormat("file-%05d", i);
      ASSERT_EQ(filename, subfiles[i].name());
      ASSERT_EQ("dummy", subfiles[i].ufs_file_info().etag());
      ASSERT_EQ(block_size * 2, subfiles[i].ufs_file_info().size());
      ASSERT_EQ(now_ts + 1, subfiles[i].ufs_file_info().sync_ts());
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}

TEST_F(AccNamespaceStandbyTest, TestUpdateINode) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  // Mkdir
  uint64_t dir_inode_id = NextINodeId();
  last_id = dir_inode_id;
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateIncomplete);
    ASSERT_EQ(dir.ufs_dir_info().type(), UfsDirType::kUfsDirTypeUfs);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), 0);
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }

  // Update it
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateSynced);
    info->set_sync_ts(now_ts + 1);
    info->set_children_sync_ts(now_ts + 1);
  }

  {
    auto op = std::make_shared<OpAccSyncUpdateINode>();
    op->SetTxid(NextTxId());

    AccSyncUpdateINodeOpBody body;
    body.set_path("/a");
    body.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(body));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateSynced);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts + 1);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), now_ts + 1);
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}

TEST_F(AccNamespaceStandbyTest, TestAddFile) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  // Mkdir
  uint64_t dir_inode_id = NextINodeId();
  last_id = dir_inode_id;
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateIncomplete);
    ASSERT_EQ(dir.ufs_dir_info().type(), UfsDirType::kUfsDirTypeUfs);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), 0);
    ASSERT_EQ(ns_->LastInodeId(), last_id);
  }

  // Sync a file
  {
    auto op = std::make_shared<OpAccSyncAddFile>();
    op->SetTxid(NextTxId());

    AccSyncAddFileOpBody body;
    body.set_path("/a/dummy.txt");
    body.set_allocated_parent(new INode(dir_inode));

    {
      uint64_t inode_id = NextINodeId();
      last_id = inode_id;
      INode inode;
      MakeINode(inode_id,
                dir_inode.id(),
                "dummy.txt",
                GetDefaultAccFilePermission(),
                INode_Type_kFile,
                &inode);
      {
        auto info = inode.mutable_ufs_file_info();
        info->set_file_state(UfsFileState::kUfsFileStatePersisted);
        info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeInvalid);
        info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeInvalid);
        info->set_etag("dummy");
        info->set_size(block_size * 2);
        info->set_last_modified_ts(now_ts);
        info->set_sync_ts(now_ts);
      }
      FileWithBlocks fb;
      fb.set_allocated_inode(new INode(std::move(inode)));
      *fb.add_block_info() =
          MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
      *fb.add_block_info() =
          MakeBlockInfoProto(NextBlockId(), 1, block_size, inode_id);
      body.set_allocated_file_blocks(new FileWithBlocks(std::move(fb)));
    }
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // Verify blocks
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(ns_->LastInodeId(), last_id);
  }
}

TEST_F(AccNamespaceStandbyTest, TestAddFileWithFutureBlocks) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  // Mkdir
  uint64_t dir_inode_id = NextINodeId();
  last_id = dir_inode_id;
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateIncomplete);
    ASSERT_EQ(dir.ufs_dir_info().type(), UfsDirType::kUfsDirTypeUfs);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), 0);
    ASSERT_EQ(ns_->LastInodeId(), last_id);
  }

  // Sync a file
  {
    auto op = std::make_shared<OpAccSyncAddFile>();
    op->SetTxid(NextTxId());

    AccSyncAddFileOpBody body;
    body.set_path("/a/dummy.txt");
    body.set_allocated_parent(new INode(dir_inode));

    {
      uint64_t inode_id = NextINodeId();
      last_id = inode_id;
      INode inode;
      MakeINode(inode_id,
                dir_inode.id(),
                "dummy.txt",
                GetDefaultAccFilePermission(),
                INode_Type_kFile,
                &inode);
      {
        auto info = inode.mutable_ufs_file_info();
        info->set_file_state(UfsFileState::kUfsFileStatePersisted);
        info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeInvalid);
        info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeInvalid);
        info->set_etag("dummy");
        info->set_size(block_size * 2);
        info->set_last_modified_ts(now_ts);
        info->set_sync_ts(now_ts);
      }
      {
        auto bp = inode.add_blocks();
        bp->set_blockid(NextBlockId());
        bp->set_genstamp(NextGS());
        bp->set_numbytes(block_size);
      }
      FileWithBlocks fb;
      fb.set_allocated_inode(new INode(std::move(inode)));
      *fb.add_block_info() = MakeBlockInfoProto(inode.blocks(0).blockid(),
                                                inode.blocks(0).genstamp(),
                                                inode.blocks(0).numbytes(),
                                                inode_id);
      body.set_allocated_file_blocks(new FileWithBlocks(std::move(fb)));
      {
        BlockManager::RepeatedIncBlockReport received_report;
        MakeReport(fb.block_info(0).block_id(),
                   fb.block_info(0).gen_stamp(),
                   fb.block_info(0).num_bytes(),
                   "",  // etag
                   "",  // upload id
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                   &received_report);
        Status s = block_manager_->IncrementalBlockReport("datanode1",
                                                          received_report);
        ASSERT_TRUE(s.IsOK());
      }
    }
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // Verify blocks
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(ns_->LastInodeId(), last_id);
  }
}

TEST_F(AccNamespaceStandbyTest, TestPersistFile) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  uint64_t dir_inode_id = NextINodeId();
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateIncomplete);
    ASSERT_EQ(dir.ufs_dir_info().type(), UfsDirType::kUfsDirTypeUfs);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), 0);
  }

  // Create a file
  uint64_t inode_id = NextINodeId();
  last_id = inode_id;
  INode inode;
  {
    MakeINode(inode_id,
              dir_inode.id(),
              "dummy.txt",
              GetDefaultAccFilePermission(),
              INode_Type_kFile,
              &inode);
    {
      auto info = inode.mutable_ufs_file_info();
      info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
      info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
      info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeAsync);
      info->set_etag("dummy");
      info->set_size(block_size * 2);
      info->set_last_modified_ts(now_ts);
      info->set_sync_ts(now_ts);
      info->set_upload_id("dummy_upload_id");
    }

    auto op = std::make_shared<OpOpenFile>();
    op->SetTxid(NextTxId());
    FileToBeOpen body;
    body.set_path("/a/dummy.txt");
    body.set_allocated_inode(new INode(inode));
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(file.ufs_file_info().file_state(), kUfsFileStateToBePersisted);
    if (!FLAGS_enable_write_back_task_persistence) {
      WriteBackTask* task =
          acc_ns_->ufs_env()->upload_monitor()->GetTask(file.id());
      ASSERT_NE(task, nullptr);
      ASSERT_EQ(task->upload_id_, "dummy_upload_id");
    } else {
      INode inode;
      ASSERT_TRUE(meta_storage_->GetWriteBackTask(inode_id, &inode).IsOK());
      ASSERT_EQ(file.SerializeAsString(), inode.SerializeAsString());
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }

  // Persist
  {
    inode.mutable_ufs_file_info()->set_file_state(
        UfsFileState::kUfsFileStatePersisted);
    {
      auto op = std::make_shared<OpAccPersistFile>();
      op->SetTxid(NextTxId());
      AccPersistFileOpBody body;
      body.set_path("/a/dummy.txt");
      body.set_allocated_parent(new INode(dir_inode));
      body.set_allocated_inode(new INode(inode));
      op->SetProto(std::move(body));
      ns_->Apply(op);
      ns_->WaitNoPending();
    }
  }

  {
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(file.ufs_file_info().file_state(), kUfsFileStatePersisted);
    if (!FLAGS_enable_write_back_task_persistence) {
      WriteBackTask* task =
          acc_ns_->ufs_env()->upload_monitor()->GetTask(file.id());
      ASSERT_EQ(task, nullptr);
    } else {
      ASSERT_EQ(meta_storage_->GetWriteBackTask(inode_id, &inode).code(),
                Code::kFileNotFound);
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}

TEST_F(AccNamespaceStandbyTest, TestUpdateBlockInfoProto) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  BlockInfoProto bip;
  {
    auto op = std::make_shared<OpAccSyncAddFile>();
    op->SetTxid(NextTxId());

    AccSyncAddFileOpBody body;
    body.set_path("/dummy.txt");
    body.mutable_parent()->CopyFrom(root);
    {
      uint64_t inode_id = NextINodeId();
      last_id = inode_id;
      INode inode;
      MakeINode(inode_id,
                root.id(),
                "dummy.txt",
                GetDefaultAccFilePermission(),
                INode_Type_kFile,
                &inode);
      {
        auto info = inode.mutable_ufs_file_info();
        info->set_file_state(UfsFileState::kUfsFileStatePersisted);
        info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeInvalid);
        info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeInvalid);
        info->set_etag("dummy");
        info->set_size(block_size * 2);
        info->set_last_modified_ts(now_ts);
        info->set_sync_ts(now_ts);
      }
      {
        auto bp = inode.add_blocks();
        bp->set_blockid(NextBlockId());
        bp->set_genstamp(NextGS());
        bp->set_numbytes(block_size);
      }
      FileWithBlocks fb;
      fb.set_allocated_inode(new INode(std::move(inode)));
      bip = MakeBlockInfoProto(inode.blocks(0).blockid(),
                               inode.blocks(0).genstamp(),
                               inode.blocks(0).numbytes(),
                               inode_id);
      bip.set_pufs_name("pufs1");
      bip.set_curr_upload_id("dummy1");
      *fb.add_block_info() = bip;
      body.set_allocated_file_blocks(new FileWithBlocks(std::move(fb)));
      {
        BlockManager::RepeatedIncBlockReport received_report;
        MakeReport(fb.block_info(0).block_id(),
                   fb.block_info(0).gen_stamp(),
                   fb.block_info(0).num_bytes(),
                   "",  // etag
                   "",  // upload id
                   cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED,
                   &received_report);
        Status s = block_manager_->IncrementalBlockReport("datanode1",
                                                          received_report);
        ASSERT_TRUE(s.IsOK());
      }
    }
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  // update upload id
  bip.set_pufs_name("pufs2");
  bip.set_curr_upload_id("dummy2");
  {
    auto op = std::make_shared<OpAccUpdateBlockInfo>();
    op->SetTxid(NextTxId());

    AccUpdateBlockInfoOpBody body;
    body.mutable_bip()->CopyFrom(bip);
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  // verify
  {
    BlockInfoProto bip2;
    ASSERT_TRUE(meta_storage_->GetBlockInfo(bip.block_id(), &bip2));
    ASSERT_EQ(bip2.pufs_name(), "pufs2");
    ASSERT_EQ(bip2.curr_upload_id(), "dummy2");
  }
}

TEST_F(AccNamespaceStandbyTest, TestPin) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  uint64_t dir_inode_id = NextINodeId();
  last_id = dir_inode_id;
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto info = dir_inode.mutable_pin_status();
    info->set_pinned(true);
    info->set_ttl(123);
    info->set_recursive(true);
    info->set_job_id("JOB_1");
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  int64_t txid = NextTxId();
  {
    auto op = std::make_shared<OpPin>();
    op->SetTxid(txid);

    INodeToPin i;
    i.set_path("/a");
    i.mutable_inode()->CopyFrom(dir_inode);
    i.set_cancel_job_id("JOB_0");
    op->SetProto(std::move(i));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }
  dir_inode.mutable_pin_status()->set_txid(txid);

  // verify
  {
    INode i;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &i), kOK);
    ASSERT_TRUE(dir_inode.has_pin_status());
    ASSERT_EQ(dir_inode.pin_status().pinned(), true);
    ASSERT_EQ(dir_inode.pin_status().ttl(), 123);
    ASSERT_EQ(dir_inode.pin_status().recursive(), true);
    ASSERT_EQ(dir_inode.pin_status().txid(), txid);
    ASSERT_EQ(dir_inode.pin_status().job_id(), "JOB_1");
  }
}

TEST_F(AccNamespaceStandbyTest, TestReconcileINodeAttrs) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  uint64_t dir_inode_id = NextINodeId();
  last_id = dir_inode_id;
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }
  {
    auto info = dir_inode.mutable_pin_status();
    info->set_pinned(true);
    info->set_ttl(123);
    info->set_recursive(true);
    info->set_txid(1);
    info->set_job_id("JOB_1");
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  auto txid = NextTxId();
  {
    auto op = std::make_shared<OpReconcileINodeAttrs>();
    op->SetTxid(txid);

    INodeToReconcile i;
    i.set_path("/a");
    i.mutable_inode()->CopyFrom(dir_inode);
    i.add_expired_ttl(1);
    i.add_new_ttl(123);
    i.add_cancel_job_id("JOB_0");
    op->SetProto(std::move(i));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }
  dir_inode.mutable_pin_status()->set_txid(txid);

  // verify
  {
    INode i;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &i), kOK);
    ASSERT_TRUE(dir_inode.has_pin_status());
    ASSERT_EQ(dir_inode.pin_status().pinned(), true);
    ASSERT_EQ(dir_inode.pin_status().ttl(), 123);
    ASSERT_EQ(dir_inode.pin_status().recursive(), true);
    ASSERT_EQ(dir_inode.pin_status().txid(), txid);
    ASSERT_EQ(dir_inode.pin_status().job_id(), "JOB_1");
  }
}

TEST_F(AccNamespaceStandbyTest, TestOpenAppendFile) {
  uint64_t now_ts = TimeUtil::GetNowEpochMs() / 1000;
  uint64_t block_size = 127UL * 1024 * 1024;
  INode root = meta_storage_->GetRootINode();
  uint64_t last_id = 0;

  uint64_t dir_inode_id = NextINodeId();
  INode dir_inode;
  MakeINode(dir_inode_id,
            root.id(),
            "a",
            GetDefaultAccFilePermission(),
            INode_Type_kDirectory,
            &dir_inode);
  {
    auto info = dir_inode.mutable_ufs_dir_info();
    info->set_state(UfsDirState::kUfsDirStateIncomplete);
    info->set_type(UfsDirType::kUfsDirTypeUfs);
    info->set_sync_ts(now_ts);
    info->set_children_sync_ts(0);
  }

  {
    auto op = std::make_shared<OpMkdirV2>();
    op->SetTxid(NextTxId());

    DirToBeMake d;
    d.set_path("/a");
    d.set_allocated_inode(new INode(dir_inode));
    op->SetProto(std::move(d));

    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode dir;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), &dir), kOK);
    ASSERT_TRUE(dir.has_ufs_dir_info());
    ASSERT_EQ(dir.ufs_dir_info().state(), UfsDirState::kUfsDirStateIncomplete);
    ASSERT_EQ(dir.ufs_dir_info().type(), UfsDirType::kUfsDirTypeUfs);
    ASSERT_EQ(dir.ufs_dir_info().sync_ts(), now_ts);
    ASSERT_EQ(dir.ufs_dir_info().children_sync_ts(), 0);
  }

  // Create a file
  uint64_t inode_id = NextINodeId();
  last_id = inode_id;
  INode inode;
  {
    MakeINode(inode_id,
              dir_inode.id(),
              "dummy.txt",
              GetDefaultAccFilePermission(),
              INode_Type_kFile,
              &inode);
    {
      auto info = inode.mutable_ufs_file_info();
      info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
      info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
      info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeAsync);
      info->set_etag("dummy");
      info->set_size(block_size * 2);
      info->set_last_modified_ts(now_ts);
      info->set_sync_ts(now_ts);
      info->set_upload_id("dummy_upload_id");
    }

    auto op = std::make_shared<OpOpenFile>();
    op->SetTxid(NextTxId());
    FileToBeOpen body;
    body.set_path("/a/dummy.txt");
    body.set_allocated_inode(new INode(inode));
    body.set_overwrite(false);
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(file.ufs_file_info().file_state(), kUfsFileStateToBePersisted);
    if (!FLAGS_enable_write_back_task_persistence) {
      WriteBackTask* task =
          acc_ns_->ufs_env()->upload_monitor()->GetTask(file.id());
      ASSERT_NE(task, nullptr);
      ASSERT_EQ(task->upload_id_, "dummy_upload_id");
    } else {
      INode inode;
      ASSERT_TRUE(meta_storage_->GetWriteBackTask(inode_id, &inode).IsOK());
      ASSERT_EQ(file.SerializeAsString(), inode.SerializeAsString());
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }

  {
    // Close and persist
    {
      inode.set_status(INode_Status_kFileComplete);
      inode.mutable_ufs_file_info()->set_file_state(
          UfsFileState::kUfsFileStatePersisted);
      {
        auto op = std::make_shared<OpAccPersistFile>();
        op->SetTxid(NextTxId());
        AccPersistFileOpBody body;
        body.set_path("/a/dummy.txt");
        body.set_allocated_parent(new INode(dir_inode));
        body.set_allocated_inode(new INode(inode));
        op->SetProto(std::move(body));
        ns_->Apply(op);
        ns_->WaitNoPending();
      }
    }
  }

  {
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(file.ufs_file_info().file_state(), kUfsFileStatePersisted);
    if (!FLAGS_enable_write_back_task_persistence) {
      WriteBackTask* task =
          acc_ns_->ufs_env()->upload_monitor()->GetTask(file.id());
      ASSERT_EQ(task, nullptr);
    } else {
      ASSERT_EQ(meta_storage_->GetWriteBackTask(inode_id, &inode).code(),
                Code::kFileNotFound);
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }

  {
    // append
    {
      auto info = inode.mutable_ufs_file_info();
      info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);
      info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
      info->set_write_type(UfsFileWriteType::kUfsFileWriteTypeAsync);
      info->set_etag("dummy");
      info->set_size(block_size * 2);
      info->set_last_modified_ts(now_ts);
      info->set_sync_ts(now_ts);
      info->set_upload_id("dummy_upload_id");
    }

    auto op = std::make_shared<OpOpenFile>();
    op->SetTxid(NextTxId());
    FileToBeOpen body;
    body.set_path("/a/dummy.txt");
    body.set_allocated_inode(new INode(inode));
    body.set_overwrite(false);
    op->SetProto(std::move(body));
    ns_->Apply(op);
    ns_->WaitNoPending();
  }

  {
    // verify
    INode file;
    ASSERT_EQ(meta_storage_->GetINode(dir_inode.id(), "dummy.txt", &file), kOK);
    ASSERT_TRUE(file.has_ufs_file_info());
    ASSERT_EQ(file.ufs_file_info().etag(), "dummy");
    ASSERT_EQ(file.ufs_file_info().sync_ts(), now_ts);
    ASSERT_EQ(file.ufs_file_info().file_state(), kUfsFileStateToBePersisted);
    if (!FLAGS_enable_write_back_task_persistence) {
      WriteBackTask* task =
          acc_ns_->ufs_env()->upload_monitor()->GetTask(file.id());
      ASSERT_NE(task, nullptr);
    } else {
      ASSERT_NE(meta_storage_->GetWriteBackTask(inode_id, &inode).code(),
                Code::kFileNotFound);
    }
    ASSERT_EQ(last_id, ns_->LastInodeId());
  }
}

}  // namespace dancenn