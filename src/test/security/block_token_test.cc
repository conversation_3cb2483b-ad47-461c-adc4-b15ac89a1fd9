// Copyright 2023
#include "security/block_token.h"

#include <glog/logging.h>
#include <gtest/gtest.h>

#include <memory>
#include <string>
#include <vector>

#include "security/block_token_identifier.h"
#include "security/block_token_secret_manager.h"
#include "security/key_manager.h"
#include "security/writable_utils.h"

namespace dancenn {

class BlockTokenTest : public testing::Test {
 public:
  void SetUp() override {
    InitBlockTokenTest();
  }
  void TearDown() override {
  }

  void InitBlockTokenTest() {
    dn_manager_ = std::make_shared<DatanodeManager>(true);
    key_manager_ = KeyManagerFactory::Create("local", dn_manager_);
    key_manager_->Start();
    std::string blockpool_id_ = "test_blockpool";

    mts_mgr_ =
        std::make_shared<BlockTokenSecretManager>(blockpool_id_, key_manager_);
  }

  std::shared_ptr<DatanodeManager> dn_manager_;
  std::shared_ptr<BlockTokenSecretManager> mts_mgr_;
  std::shared_ptr<KeyManager> key_manager_;
};

TEST_F(BlockTokenTest, GenerateTokenWithMutex) {
  EXPECT_NE(key_manager_->GetTokenLifeTimeMs(), 0);
  EXPECT_NE(key_manager_->GetCurrentKey().expiry_date(), 0);

  std::string user0 = "user0";
  uint64_t blk_id0 = 201234812;
  std::vector<AccessMode> modes0{AccessMode::READ};
  auto token0 = mts_mgr_->GenerateToken(user0, blk_id0, modes0);
  ASSERT_TRUE(token0);
  auto token1 = mts_mgr_->GenerateToken("", blk_id0, modes0);
  ASSERT_TRUE(token1);
  EXPECT_NE(token0, token1);
  EXPECT_FALSE(token0->IsEmpty());
  LOG(INFO) << token0->ToString();

  EXPECT_NE(token0->GetIdentifier(), token1->GetIdentifier());
  EXPECT_NE(token0->GetPassword(), token1->GetPassword());
  EXPECT_EQ(token0->GetKind(), token1->GetKind());
  EXPECT_EQ(token0->GetKind(), token1->GetKind());
  EXPECT_EQ(token0->GetService(), token1->GetService());
  EXPECT_EQ(token0->GetService(), "");

  auto token2 = mts_mgr_->GenerateToken("", blk_id0, modes0);
  // different expiry date
  EXPECT_NE(token1, token2);
}

TEST_F(BlockTokenTest, WriteUtils) {
  int64_t expiry_date = 1689786515350;
  int32_t key_id = 1828368023;
  std::string user = "cloudfstest";

  std::string stream = WriteVLong(expiry_date);
  stream += WriteVInt(key_id);
  stream += WriteString(user);
  stream += WriteString(user);
  stream += WriteVInt(key_id + 1);

  int64_t expiry_date_d;
  int32_t key_id_d0, key_id_d1;
  std::string user_d0, user_d1;
  int position = 0;

  bool success = ReadVLong(stream, &position, &expiry_date_d) &&
                 ReadVInt(stream, &position, &key_id_d0) &&
                 ReadString(stream, &position, &user_d0) &&
                 ReadString(stream, &position, &user_d1) &&
                 ReadVInt(stream, &position, &key_id_d1);

  EXPECT_TRUE(success);
  EXPECT_EQ(expiry_date, expiry_date_d);
  EXPECT_EQ(key_id, key_id_d0);
  EXPECT_EQ(user, user_d0);
  EXPECT_EQ(user, user_d1);
  EXPECT_LE(key_id, key_id_d1);
  EXPECT_EQ(key_id, key_id_d1 - 1);
}

TEST_F(BlockTokenTest, IdentifierDecode) {
  std::string user_id = "cloudfstest";
  std::string blockPoolId = "BP-18014398509483110-1688973570367";
  uint64_t blockId = 1073741825;
  std::vector<AccessMode> modes{AccessMode::READ};

  std::shared_ptr<BlockTokenIdentifier> btid =
      std::make_shared<BlockTokenIdentifier>(
          user_id, blockPoolId, blockId, modes);
  btid->SetExpiryDate(1689771390491);
  btid->SetKeyId(1763499672);
  LOG(INFO) << "Original Identifier: " << btid->ToString();

  std::string stream = btid->GetBytes();
  std::shared_ptr<BlockTokenIdentifier> btid1 =
      std::make_shared<BlockTokenIdentifier>();
  btid1->ReadFields(stream);
  LOG(INFO) << "New Identifier: " << btid1->ToString();

  EXPECT_FALSE(btid->IsEmpty());
  EXPECT_EQ(btid->GetExpiryDate(), btid1->GetExpiryDate());
  EXPECT_EQ(btid->GetKeyId(), btid1->GetKeyId());
  EXPECT_EQ(btid->GetUser(), btid1->GetUser());
  EXPECT_EQ(btid->GetBlockpoolId(), btid1->GetBlockpoolId());
  EXPECT_EQ(btid->GetBlockId(), btid1->GetBlockId());
  EXPECT_EQ(btid->GetAccessModesStr(), btid1->GetAccessModesStr());
  EXPECT_TRUE(btid->Equals(*btid1));
}

TEST_F(BlockTokenTest, TokenDecode) {
  std::string user_id = "TokenTestUser";
  uint64_t blockId = 1073741825;
  std::vector<AccessMode> modes{AccessMode::READ};

  std::shared_ptr<BlockToken> token0 =
      mts_mgr_->GenerateToken(user_id, blockId, modes);

  BlockTokenIdentifier identifier;
  bool success = identifier.ReadFields(token0->GetIdentifier());
  LOG(INFO) << "Construct new BlockTokenIdentifier " << identifier.ToString();
  EXPECT_EQ(identifier.GetBlockId(), blockId);
  EXPECT_EQ(identifier.GetUser(), user_id);
  EXPECT_NE(identifier.GetExpiryDate(), 0);
}

TEST_F(BlockTokenTest, HmacEncode) {
  std::vector<std::string> algos{"sha512", "sha1", "md5", "sha224", "sha384"};
  std::string key = "1828368023";
  std::string input = "This is an example input";

  for (std::string& algo : algos) {
    std::string res = mts_mgr_->HmacEncode(algo, key, input);
    EXPECT_NE(res, "");
  }
}

}  // namespace dancenn