// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/10
// Description

#ifndef TEST_GMOCK_EDIT_LOG_INPUT_CONTEXT_H_
#define TEST_GMOCK_EDIT_LOG_INPUT_CONTEXT_H_

#include <gmock/gmock.h>
#include <string>

#include "edit/edit_log_context.h"
#include "test/mock_edit_log_context.h"

namespace dancenn {

class GMockEditLogInputContext : public EditLogInputContextBase {
 public:
  MOCK_METHOD1(ReadOp, bool(std::string* serialized_op));
};

}  // namespace dancenn

#endif  // TEST_GMOCK_EDIT_LOG_INPUT_CONTEXT_H_
