//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguo<PERSON> <<EMAIL>>
//

#ifndef TEST_MOCK_EDIT_LOG_INPUT_CONTEXT_H_
#define TEST_MOCK_EDIT_LOG_INPUT_CONTEXT_H_

#include <string>

namespace dancenn {

class MockEditLogInputContext : public EditLogInputContextBase {
 public:
  MockEditLogInputContext() {}
  ~MockEditLogInputContext() {}

  bool ReadOp(std::string* serialized_op) override {
    *serialized_op = op_;
    return true;
  }

  std::string op_;
};

}  // namespace dancenn

#endif  // TEST_MOCK_EDIT_LOG_INPUT_CONTEXT_H_
