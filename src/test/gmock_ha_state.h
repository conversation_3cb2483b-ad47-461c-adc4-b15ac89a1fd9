// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/08
// Description

#ifndef TEST_GMOCK_HA_STATE_H_
#define TEST_GMOCK_HA_STATE_H_

#include <gmock/gmock.h>
#include <proto/generated/cloudfs/HAServiceProtocol.pb.h>

#include <utility>

#include "base/status.h"
#include "base/two_step_vlock.h"
#include "base/vlock.h"
#include "ha/operations.h"
#include "test/mock_ha_state.h"

namespace dancenn {

class GMockHAState : public MockHAState {
 public:
  MOCK_METHOD1(CheckOperation,
               std::pair<Status, vshared_lock>(OperationsCategory op));
  MOCK_CONST_METHOD0(IsActive, bool());
  MOCK_METHOD0(GetHAMode, EditLogConf::HAMode());
  MOCK_METHOD0(IsTailerStopped, bool());
  MOCK_METHOD0(GetState, cloudfs::HAServiceStateProto());
  MOCK_METHOD1(SetState, void(cloudfs::HAServiceStateProto next_state));
  MOCK_METHOD0(ShouldPopulateReplicationQueues, bool());
  MOCK_METHOD0(LockBarrierForHASwitcher, TwoStepVUniqueLock());
  MOCK_METHOD0(SwitchHAStandbyToNonHAActive, Status());
  MOCK_METHOD0(SwitchNonHAActiveToHAActive, Status());
};

}  // namespace dancenn

#endif  // TEST_GMOCK_HA_STATE_H_
