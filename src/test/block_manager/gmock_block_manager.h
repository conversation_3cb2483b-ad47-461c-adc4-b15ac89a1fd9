// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/17
// Description

#ifndef TEST_BLOCK_MANAGER_GMOCK_BLOCK_MANAGER_H_
#define TEST_BLOCK_MANAGER_GMOCK_BLOCK_MANAGER_H_

#include <gmock/gmock.h>

#include <cstdint>
#include <vector>

#include "block_manager/block.h"
#include "block_manager/block_manager.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"

namespace dancenn {

class GMockBlockManager : public BlockManager {
 public:
  MOCK_METHOD7(AddBlock,
               Block(const Block&,
                     uint64_t,
                     uint64_t,
                     uint8_t,
                     cloudfs::IoMode,
                     const std::vector<DatanodeID>&,
                     BlockUCState));
  MOCK_METHOD1(GetBlockUCState, BlockUCState(BlockID blk_id));
  MOCK_METHOD1(RemoveBlocksAndUpdateSafeMode,
               void(const std::vector<cloudfs::BlockProto>& blocks));
  MOCK_METHOD3(CheckReplica,
               void(const std::vector<cloudfs::BlockProto>& blocks,
                    const INode* inode,
                    StopWatchContext* rpc_sw_ctx));
  MOCK_METHOD1(IsLastBlkReadyToComplete, Status(const Block& blk_from_client));
  MOCK_METHOD2(CommitOrCompleteOrPersistLastBlock,
               bool(const Block& blk_from_client, bool force));
  MOCK_METHOD1(BlockHasBeenCommitted, bool(BlockID block_id));
  MOCK_METHOD1(BlockHasBeenComplete, bool(BlockID block_id));
  MOCK_METHOD1(BlockReadyToComplete, bool(BlockID block_id));
  MOCK_METHOD1(GetINodeId, uint64_t(BlockID blk_id));
  MOCK_METHOD1(
      CommitBlockSynchronization,
      Status(const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
                 request));
  MOCK_METHOD3(NeedRelease,
               Status(const std::vector<BlockProto>& blocks,
                      bool* remove_last_block,
                      Block* last_blk));
  MOCK_METHOD3(InitRecover,
               void(const cloudfs::BlockProto& block,
                    uint64_t recovery_id,
                    bool close_file));
  MOCK_METHOD0(GetCurrentTsInSec, int64_t());
  MOCK_METHOD5(AnalyzeFileBlocks,
               Status(const Block&,
                      const Block&,
                      const Block&,
                      uint32_t,
                      std::vector<DatanodeID>*));
  MOCK_METHOD2(AnalyzeFileBlocksToCommit,
               Status(const Block& previous, const Block& stored_last));
  MOCK_METHOD2(UpdateLength, void(BlockID blk_id, uint64_t len));
  MOCK_METHOD1(GetBlock, Block(BlockID blk_id));
  MOCK_METHOD2(UpdateBlockPinStatus,
               void(const INode& inode, const BlockID bl));
};

}  // namespace dancenn

#endif  // TEST_BLOCK_MANAGER_GMOCK_BLOCK_MANAGER_H_
