// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/02/24
// Description

#include "block_manager/block_report_handler.h"  // For BlockReportHandler.

#include <absl/strings/str_format.h>         // For StrFormat.
#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <gflags/gflags.h>                   // For DECLARE_int32.
#include <gmock/gmock.h>                     // For EXPECT_CALL, StrictMock.
#include <google/protobuf/io/gzip_stream.h>  // For GzipOutputStream.
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>  // StringOutputStream.
#include <google/protobuf/repeated_field.h>  // For RepeatedPtrField.
#include <gtest/gtest.h>                     // For TEST, etc.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For StorageReceivedDeletedBlocksProto, UploadCommandProto, etc.
#include <proto/generated/cloudfs/HAServiceProtocol.pb.h>  // For HAServiceStateProto.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For ReplicaStateProto, ReplicaStateProto_ARRAYSIZE.
#include <proto/generated/cloudfs/lifecycle.pb.h>  // For StorageClassProto.
#include <proto/generated/dancenn/edit_log.pb.h>   // BlockInfoProtos.
#include <rocksdb/slice.h>                         // For Slice.
#include <rocksdb/status.h>                        // For Status.

#include <algorithm>  // For copy.
#include <cstdint>    // For uint64_t.
#include <memory>     // For shared_ptr.
#include <numeric>    // For accumulate.
#include <string>     // For string.
#include <utility>    // For move, make_pair.
#include <vector>     // For vector.

#include "base/closure.h"                       // For Closure.
#include "base/java_exceptions.h"               // For JavaExceptions.
#include "base/platform.h"                      // For WriteBigEndian.
#include "base/status.h"                        // For Status.
#include "base/time_util.h"                     // For TimeUtilV2.
#include "base/vlock.h"                         // For vshared_lock.
#include "block_manager/bip_write_manager.h"    // For BIPWriteManager.
#include "block_manager/block_manager.h"        // For BlkInfo.
#include "edit/sender_base.h"                   // For EditLogSenderBase.
#include "ha/operations.h"                      // OperationsCategory.
#include "namespace/meta_storage_write_task.h"  // WriteTask.
#include "test/base/gmock_time_util.h"          // For GMockTimeUtilV2.
#include "test/gmock_edit_log_sender.h"         // For GMockEditLogSender.
#include "test/gmock_ha_state.h"                // For GMockHAState.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage, GMockWriteBatch.
#include "test/namespace/gmock_writer.h"                     // For GMockWriter.
#include "test/proto/generated/cloudfs/datanode_protocol.h"  // For ReceivedDeletedBlockInfoProtoBuilder, StorageReceivedDeletedBlocksProtoBuilder.
#include "test/proto/generated/cloudfs/hdfs.h"  // For BlockProtoBuilder.
#include "test/proto/generated/dancenn/block_info_proto.h"  // For BlockInfoProtoBuilder.
#include "test/proto/generated/repeated_field.h"  // For BuildRepeatedPtrField.

using namespace testing;  // NOLINT(build/namespaces)

DECLARE_int32(dirty_block_info_proto_bucket_num);
DECLARE_int32(process_block_report_batch_size);

namespace dancenn {
namespace {

rocksdb::Slice EncodeBlockID(BlockID blk_id) {
  static std::vector<std::string> slice_holders;
  std::string blk_id_str;
  blk_id_str.resize(sizeof(BlockID) / sizeof(uint8_t));
  platform::WriteBigEndian(const_cast<char*>(blk_id_str.c_str()), 0, blk_id);
  slice_holders.push_back(blk_id_str);
  return slice_holders.back();
}

}  // namespace

class BlockReportHandlerTest : public Test {
 public:
  void SetUp() override {
    TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
        [this]() { return time_util_.GetNowEpochMs(); });
    gmock_edit_log_sender_ =
        std::make_shared<testing::StrictMock<GMockEditLogSender>>();
    gmock_meta_storage_ =
        std::make_shared<testing::StrictMock<GMockMetaStorage>>();
    gmock_meta_storage_writer_ =
        std::make_shared<testing::StrictMock<meta_storage::GMockWriter>>();
    gmock_meta_storage_->SetWriter(
        std::shared_ptr<meta_storage::Writer>(gmock_meta_storage_writer_));
    bip_write_manager_.reset(new BIPWriteManager(
        "bp-1",
        std::static_pointer_cast<MetaStorage>(gmock_meta_storage_),
        nullptr));
    block_report_handler_.reset(new BlockReportHandler(
        &ha_state_,
        std::static_pointer_cast<EditLogSenderBase>(gmock_edit_log_sender_),
        std::static_pointer_cast<MetaStorage>(gmock_meta_storage_),
        nullptr,
        bip_write_manager_.get()));
  }

 protected:
  GMockTimeUtilV2 time_util_;
  GMockHAState ha_state_;
  std::shared_ptr<testing::StrictMock<GMockEditLogSender>>
      gmock_edit_log_sender_;
  std::shared_ptr<testing::StrictMock<GMockMetaStorage>> gmock_meta_storage_;
  std::shared_ptr<testing::StrictMock<meta_storage::GMockWriter>>
      gmock_meta_storage_writer_;
  std::unique_ptr<BIPWriteManager> bip_write_manager_;
  std::unique_ptr<BlockReportHandler> block_report_handler_;
};

class BlockReportHandlerIBRTest : public BlockReportHandlerTest {};

TEST_F(BlockReportHandlerIBRTest, HappyCase) {
  EXPECT_CALL(time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(1676971558000));

  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015580, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                           .SetState(BlockInfoProto::kCommitted)
                                           .SetBlockId(1076015580)
                                           .SetGenStamp(1001)
                                           .SetNumBytes(1024)
                                           .SetINodeId(17327)
                                           .SetExpectedRep(3)
                                           .Build()),
                      Return(true)));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015581, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                           .SetState(BlockInfoProto::kCommitted)
                                           .SetBlockId(1076015581)
                                           .SetGenStamp(1002)
                                           .SetNumBytes(1025)
                                           .SetINodeId(17327)
                                           .SetExpectedRep(3)
                                           .Build()),
                      Return(true)));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015582, _))
      .Times(1)
      .WillOnce(DoAll(
          SetArgPointee<1>(
              BlockInfoProtoBuilder()
                  .SetState(BlockInfoProto::kCommitted)
                  .SetBlockId(1076015582)
                  .SetGenStamp(1003)
                  .SetNumBytes(1026)
                  .SetINodeId(17327)
                  .SetExpectedRep(3)
                  .AddReplicas(
                      ReplicaInfoProtoBuilder().SetDnUuid("datanode-1").Build())
                  .Build()),
          Return(true)));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015583, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                           .SetState(BlockInfoProto::kComplete)
                                           .SetBlockId(1076015583)
                                           .SetGenStamp(1004)
                                           .SetNumBytes(1027)
                                           .SetINodeId(17327)
                                           .SetExpectedRep(3)
                                           .Build()),
                      Return(true)));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015584, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                           .SetState(BlockInfoProto::kComplete)
                                           .SetBlockId(1076015584)
                                           .SetGenStamp(1005)
                                           .SetNumBytes(1028)
                                           .SetINodeId(17327)
                                           .SetExpectedRep(3)
                                           .Build()),
                      Return(true)));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015585, _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                     .SetState(BlockInfoProto::kDeprecated)
                                     .SetBlockId(1076015585)
                                     .SetGenStamp(1006)
                                     .SetNumBytes(1029)
                                     .SetINodeId(17327)
                                     .SetExpectedRep(3)
                                     .Build()),
                Return(true)));

  FLAGS_process_block_report_batch_size = 5;
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1 + ((6 + /*partition_size=*/5 - 1) / /*partition_size=*/5))
      .WillRepeatedly(Invoke([](OperationsCategory op) {
        return std::make_pair<Status, vshared_lock>(Status(), vshared_lock());
      }));

  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogFlushBlockInfoProtos(AllOf(
          Property(&BlockInfoProtos::content,
                   ElementsAre(
                       AllOf(Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015580)),
                       AllOf(Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015581)),
                       AllOf(Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015582)),
                       AllOf(Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015583)),
                       AllOf(Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015584)))),
          Property(&BlockInfoProtos::trigger, "datanode-1"))))
      .Times(1)
      .WillOnce(Invoke([this](const BlockInfoProtos& bips) {
        for (const BlockInfoProto& bip : bips.content()) {
          EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(bip.block_id())
                          .TestOnlyIsLocked());
        }
        return 1;
      }));
  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogFlushBlockInfoProtos(
          AllOf(Property(&BlockInfoProtos::content,
                         ElementsAre(AllOf(
                             Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015585)))),
                Property(&BlockInfoProtos::trigger, "datanode-1"))))
      .Times(1)
      .WillOnce(Invoke([this](const BlockInfoProtos& bips) {
        for (const BlockInfoProto& bip : bips.content()) {
          EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(bip.block_id())
                          .TestOnlyIsLocked());
        }
        return 2;
      }));

  EXPECT_CALL(*gmock_meta_storage_,
              FlushBlockInfoProtos(testing::_, testing::_, testing::_))
      .Times(2)
      .WillRepeatedly(testing::Invoke(
          [this](const BlockInfoProtos& bips, int64_t txid, Closure* done) {
            gmock_meta_storage_->MetaStorage::FlushBlockInfoProtos(
                bips, txid, done);
          }));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(_))
      .Times(2)
      .WillRepeatedly([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });
  auto wb_1 = new testing::StrictMock<GMockWriteBatch>();
  auto wb_2 = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(*gmock_meta_storage_, CreateWriteBatch)
      .Times(2)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb_1))))
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb_2))));
  EXPECT_CALL(*wb_1,
              Put(kBlockInfoProtoCFHandle,
                  AnyOf(EncodeBlockID(1076015580),
                        EncodeBlockID(1076015581),
                        EncodeBlockID(1076015582),
                        EncodeBlockID(1076015583),
                        EncodeBlockID(1076015584)),
                  _))
      .Times(5)
      .WillRepeatedly(Return(rocksdb::Status()));
  EXPECT_CALL(*wb_1,
              Put(kLocalBlockCFHandle,
                  AnyOf(EncodeBlockID(1076015580),
                        EncodeBlockID(1076015581),
                        EncodeBlockID(1076015582),
                        EncodeBlockID(1076015583)),
                  _))
      .Times(4)
      .WillRepeatedly(Return(rocksdb::Status()));
  EXPECT_CALL(*wb_1, Delete(kLocalBlockCFHandle, EncodeBlockID(1076015584)))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb_2, Delete(kBlockInfoProtoCFHandle, EncodeBlockID(1076015585)))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb_2, Delete(kLocalBlockCFHandle, EncodeBlockID(1076015585)))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb_2,
              Delete(kDeprecatedBlockCFHandle, EncodeBlockID(1076015585)))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));

  std::vector<cloudfs::datanode::UploadCommandProto> upload_cmds;
  std::vector<cloudfs::datanode::NotifyEvictableCommandProto> ne_cmds;
  Status s = block_report_handler_->IncrementalBlockReport(
      "datanode-1",
      BuildRepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>(
          {StorageReceivedDeletedBlocksProtoBuilder()
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(cloudfs::datanode::
                                      ReceivedDeletedBlockInfoProto::RECEIVING)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015580)
                                     .SetGenStamp(1001)
                                     .SetNumBytes(1024)
                                     .Build())
                       .Build())
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(cloudfs::datanode::
                                      ReceivedDeletedBlockInfoProto::RECEIVED)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015581)
                                     .SetGenStamp(1002)
                                     .SetNumBytes(1025)
                                     .Build())
                       .Build())
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(cloudfs::datanode::
                                      ReceivedDeletedBlockInfoProto::DELETED)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015582)
                                     .SetGenStamp(1003)
                                     .SetNumBytes(1026)
                                     .Build())
                       .Build())
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(
                           cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                               UPLOAD_ID_NEGOED)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015583)
                                     .SetGenStamp(1004)
                                     .SetNumBytes(1027)
                                     .Build())
                       .SetPufsName("18014398509482046/block/1076015583.block")
                       .SetUploadId("upload-id-1")
                       .Build())
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(
                           cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                               UPLOAD_SUCCEED)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015584)
                                     .SetGenStamp(1005)
                                     .SetNumBytes(1028)
                                     .Build())
                       .SetPufsName("18014398509482046/block/1076015584.block")
                       .SetUploadId("upload-id-1")
                       .Build())
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetStatus(
                           cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                               PUFS_DELETED)
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015585)
                                     .SetGenStamp(1006)
                                     .SetNumBytes(1029)
                                     .Build())
                       .Build())
               .Build()}),
      [&upload_cmds](
          std::vector<cloudfs::datanode::UploadCommandProto>&& cmds) {
        upload_cmds = std::move(cmds);
      },
      [&ne_cmds](
          std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&& cmds) {
        ne_cmds = std::move(cmds);
      });
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(upload_cmds.size(), 1);
  EXPECT_EQ(ne_cmds.size(), 1);
}

TEST_F(BlockReportHandlerIBRTest, RetryUploadIdNegoed) {
  EXPECT_CALL(time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(1676971558000));
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(2)
      .WillRepeatedly(Invoke([](OperationsCategory op) {
        return std::make_pair<Status, vshared_lock>(Status(), vshared_lock());
      }));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015583, _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(
                    BlockInfoProtoBuilder()
                        .SetState(BlockInfoProto::kUploadIssued)
                        .SetBlockId(1076015583)
                        .SetGenStamp(1004)
                        .SetNumBytes(1027)
                        .SetINodeId(17327)
                        .SetExpectedRep(3)
                        .SetPufsName("18014398509482046/block/1076015583.block")
                        .SetCurrUploadId("upload-id-1")
                        .SetDnUuid("datanode-1")
                        .SetNnExpTs(1676971558 + 10)
                        .Build()),
                Return(true)));
  std::vector<cloudfs::datanode::UploadCommandProto> upload_cmds;
  Status s = block_report_handler_->IncrementalBlockReport(
      "datanode-1",
      BuildRepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>(
          {StorageReceivedDeletedBlocksProtoBuilder()
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015583)
                                     .SetGenStamp(1004)
                                     .SetNumBytes(1027)
                                     .Build())
                       .SetStatus(
                           cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                               UPLOAD_ID_NEGOED)
                       .SetPufsName("18014398509482046/block/1076015583.block")
                       .SetUploadId("upload-id-1")
                       .Build())
               .Build()}),
      [&upload_cmds](
          std::vector<cloudfs::datanode::UploadCommandProto>&& cmds) {
        upload_cmds = std::move(cmds);
      },
      [](std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&) {});
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(upload_cmds.size(), 1);
}

TEST_F(BlockReportHandlerIBRTest, RetryUploadSucceed) {
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(2)
      .WillRepeatedly(Invoke([](OperationsCategory op) {
        return std::make_pair<Status, vshared_lock>(Status(), vshared_lock());
      }));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015583, _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(
                    BlockInfoProtoBuilder()
                        .SetState(BlockInfoProto::kPersisted)
                        .SetBlockId(1076015583)
                        .SetGenStamp(1004)
                        .SetNumBytes(1027)
                        .SetINodeId(17327)
                        .SetExpectedRep(3)
                        .SetPufsName("18014398509482046/block/1076015583.block")
                        .Build()),
                Return(true)));
  std::vector<cloudfs::datanode::NotifyEvictableCommandProto> ne_cmds;
  Status s = block_report_handler_->IncrementalBlockReport(
      "datanode-1",
      BuildRepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>(
          {StorageReceivedDeletedBlocksProtoBuilder()
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015583)
                                     .SetGenStamp(1004)
                                     .SetNumBytes(1027)
                                     .Build())
                       .SetStatus(
                           cloudfs::datanode::ReceivedDeletedBlockInfoProto::
                               UPLOAD_SUCCEED)
                       .SetPufsName("18014398509482046/block/1076015583.block")
                       .Build())
               .Build()}),
      [](std::vector<cloudfs::datanode::UploadCommandProto>&&) {},
      [&ne_cmds](
          std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&& cmds) {
        ne_cmds = std::move(cmds);
      });
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(ne_cmds.size(), 1);
}

TEST_F(BlockReportHandlerIBRTest, OmitEmptyLogFlushBlockInfoProtos) {
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(2)
      .WillRepeatedly(Invoke([](OperationsCategory op) {
        return std::make_pair<Status, vshared_lock>(Status(), vshared_lock());
      }));
  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015580, _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(
                    BlockInfoProtoBuilder()
                        .SetState(BlockInfoProto::kCommitted)
                        .SetBlockId(1076015580)
                        .SetGenStamp(1001)
                        .SetNumBytes(1024)
                        .SetINodeId(17327)
                        .SetExpectedRep(3)
                        .AddReplicas(
                            // ShouldReplicaReportTsFollowFBRTs(replica) == true
                            ReplicaInfoProtoBuilder()
                                .SetReporter(ReplicaInfoProto::kDatanode)
                                .SetState(cloudfs::ReplicaStateProto::FINALIZED)
                                .SetGenStamp(1001)
                                .SetNumBytes(1024)
                                .SetDnUuid("datanode-1")
                                .Build())
                        .Build()),
                Return(true)));
  Status s = block_report_handler_->IncrementalBlockReport(
      "datanode-1",
      BuildRepeatedPtrField<
          cloudfs::datanode::StorageReceivedDeletedBlocksProto>(
          {StorageReceivedDeletedBlocksProtoBuilder()
               .AddBlocks(
                   ReceivedDeletedBlockInfoProtoBuilder()
                       .SetBlock(BlockProtoBuilder()
                                     .SetBlockId(1076015580)
                                     .SetGenStamp(1001)
                                     .SetNumBytes(1024)
                                     .Build())
                       .SetStatus(cloudfs::datanode::
                                      ReceivedDeletedBlockInfoProto::RECEIVED)
                       .Build())
               .Build()}),
      [](std::vector<cloudfs::datanode::UploadCommandProto>&&) {},
      [](std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&) {});
  EXPECT_TRUE(s.IsOK());
}

TEST_F(BlockReportHandlerIBRTest, ReportToStandby) {
  ha_state_.SetState(cloudfs::HAServiceStateProto::STANDBY);
  google::protobuf::RepeatedPtrField<
      cloudfs::datanode::StorageReceivedDeletedBlocksProto>
      report;
  Status s =
      BlockReportHandler(&ha_state_,
                         std::shared_ptr<EditLogSenderBase>(),
                         std::shared_ptr<MetaStorage>(gmock_meta_storage_),
                         nullptr,
                         nullptr)
          .IncrementalBlockReport(
              "datanode-1",
              report,
              [](std::vector<cloudfs::datanode::UploadCommandProto>&& cmds) {},
              [](std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&
                     cmds) {});
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  EXPECT_EQ(s.message(), "Skip IncrementalBlockReport due to standby");
}

TEST_F(BlockReportHandlerIBRTest, ReportWhenNameNodeIsInTransitionToStandby) {
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillRepeatedly(Invoke([](OperationsCategory op) {
        return std::make_pair<Status, vshared_lock>(
            Status(JavaExceptions::kStandbyException), vshared_lock());
      }));
  google::protobuf::RepeatedPtrField<
      cloudfs::datanode::StorageReceivedDeletedBlocksProto>
      report;
  Status s =
      BlockReportHandler(&ha_state_,
                         std::shared_ptr<EditLogSenderBase>(),
                         std::shared_ptr<MetaStorage>(gmock_meta_storage_),
                         nullptr,
                         nullptr)
          .IncrementalBlockReport(
              "datanode-1",
              report,
              [](std::vector<cloudfs::datanode::UploadCommandProto>&& cmds) {},
              [](std::vector<cloudfs::datanode::NotifyEvictableCommandProto>&&
                     cmds) {});
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  EXPECT_EQ(s.message(),
            "Skip IncrementalBlockReport due to in transition to standby");
}

class BlockReportHandlerFBRTest : public BlockReportHandlerTest {};

class BlockReportHandlerDecodeStorageBlockReportProtoV1Test
    : public BlockReportHandlerFBRTest {};

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test, NullReport) {
  cloudfs::datanode::StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);
  std::vector<BlkInfo> decoded_report;
  EXPECT_TRUE(block_report_handler_
                  ->DecodeStorageBlockReportProto(
                      "datanode-1", raw_report, &decoded_report)
                  .IsOK());
  EXPECT_EQ(decoded_report.size(), 0);
}

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test, EmptyReport) {
  cloudfs::datanode::StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);
  raw_report.add_blocks(/*finalized_num=*/0);
  raw_report.add_blocks(/*uc_num=*/0);
  raw_report.add_blocks(static_cast<uint64_t>(-1));
  raw_report.add_blocks(static_cast<uint64_t>(-1));
  raw_report.add_blocks(static_cast<uint64_t>(-1));
  std::vector<BlkInfo> decoded_report;
  EXPECT_TRUE(block_report_handler_
                  ->DecodeStorageBlockReportProto(
                      "datanode-1", raw_report, &decoded_report)
                  .IsOK());
  EXPECT_EQ(decoded_report.size(), 0);
}

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test, ReportSizeIsOne) {
  cloudfs::datanode::StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);
  raw_report.add_blocks(0);
  std::vector<BlkInfo> decoded_report;
  Status s = block_report_handler_->DecodeStorageBlockReportProto(
      "datanode-1", raw_report, &decoded_report);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Size of raw_report from datanode-1 is 1");
}

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test,
       ReportSizeIsSmallerThanExpected) {
  cloudfs::datanode::StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);
  raw_report.add_blocks(1);
  raw_report.add_blocks(2);
  std::vector<BlkInfo> decoded_report;
  Status s = block_report_handler_->DecodeStorageBlockReportProto(
      "datanode-1", raw_report, &decoded_report);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Invalid full block report from datanode-1, "
            "report.blocks_size():2, finalize_num:1, uc_num:2, "
            "delimiter:{0,0,0}");
}

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test,
       InvalidDelimiter) {
  cloudfs::datanode::StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);
  raw_report.add_blocks(0);
  raw_report.add_blocks(0);
  raw_report.add_blocks(1);
  raw_report.add_blocks(2);
  raw_report.add_blocks(3);
  std::vector<BlkInfo> decoded_report;
  Status s = block_report_handler_->DecodeStorageBlockReportProto(
      "datanode-1", raw_report, &decoded_report);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Invalid full block report from datanode-1, "
            "report.blocks_size():5, finalize_num:0, uc_num:0, "
            "delimiter:{1,2,3}");
}

// Copy from TEST_F(BlockManagerTest, DecodeRepotedBlockTestV1).
TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV1Test, HappyCase) {
  const int nstate = cloudfs::ReplicaStateProto_ARRAYSIZE;
  const int nblock_counter[nstate] = {/*FINALIZED=*/200000,
                                      /*RBW=*/200000,
                                      /*RWR=*/200000,
                                      /*RUR=*/200000,
                                      /*TEMPORARY=*/200000};
  const int nblock =
      std::accumulate(nblock_counter, nblock_counter + nstate, 0);
  std::vector<BlkInfo> blocks_expected;

  // Generate random blocks in v1 format.
  cloudfs::datanode::StorageBlockReportProto proto;
  {
    int nblk_cnt[nstate];
    std::copy(std::begin(nblock_counter),
              std::end(nblock_counter),
              std::begin(nblk_cnt));

    // Part 1: fin_num, uc_num.
    proto.add_blocks(nblk_cnt[ReplicaStateProto::FINALIZED]);
    proto.add_blocks(nblock - nblk_cnt[ReplicaStateProto::FINALIZED]);

    // Part 2: { id, gs, len } * fin_num.
    while (nblk_cnt[ReplicaStateProto::FINALIZED] > 0) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      proto.add_blocks(blkid);
      proto.add_blocks(nbyte);
      proto.add_blocks(gs);
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      blocks_expected.emplace_back(
          BlkInfo{.id = blkid,
                  .gs = gs,
                  .len = nbyte,
                  .replica_state = ReplicaStateProto::FINALIZED,
                  .storage_class_report = scr});
      nblk_cnt[ReplicaStateProto::FINALIZED]--;
    }

    // Part 3: delimiter, -1 * 3.
    proto.add_blocks(static_cast<uint64_t>(-1));
    proto.add_blocks(static_cast<uint64_t>(-1));
    proto.add_blocks(static_cast<uint64_t>(-1));

    // Part 4: { id, gs, len, state } * uc_num.
    while (std::accumulate(nblk_cnt, nblk_cnt + nstate, 0) > 0) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      uint64_t state;
      do {
        state = rand() % nstate;
      } while (nblk_cnt[state] == 0);
      proto.add_blocks(blkid);
      proto.add_blocks(nbyte);
      proto.add_blocks(gs);
      proto.add_blocks(state);
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      blocks_expected.emplace_back(
          BlkInfo{.id = blkid,
                  .gs = gs,
                  .len = nbyte,
                  .replica_state = static_cast<ReplicaStateProto>(state),
                  .storage_class_report = scr});

      nblk_cnt[state]--;
    }
  }

  // Decode.
  std::vector<BlkInfo> blocks_decoded;
  {
    uint64_t fin_num, uc_num;
    EXPECT_TRUE(block_report_handler_
                    ->DecodeStorageBlockReportProto(
                        "datanode-1", proto, &blocks_decoded)
                    .IsOK());

    size_t sum = 0;
    LOG(INFO) << absl::StrFormat(
        "memory consumption of decoded blocks: %d byte"
        " for %d reported entries.",
        blocks_decoded.size() * sizeof(blocks_decoded[0]),
        blocks_decoded.size());
  }

  // Check if equal.
  {
    ASSERT_EQ(blocks_expected.size(), blocks_decoded.size());
    for (int i = 0; i < blocks_decoded.size(); i++) {
      auto& blk_before = blocks_expected[i];
      auto& blk_after = blocks_decoded[i];
      EXPECT_EQ(blk_before.id, blk_after.id);
      EXPECT_EQ(blk_before.gs, blk_after.gs);
      EXPECT_EQ(blk_before.len, blk_after.len);
      EXPECT_EQ(blk_before.replica_state, blk_after.replica_state);
      EXPECT_EQ(blk_before.storage_class_report.SerializeAsString(), blk_after.storage_class_report.SerializeAsString());
    }
  }
}

class BlockReportHandlerDecodeStorageBlockReportProtoV2Test
    : public BlockReportHandlerFBRTest {};

TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV2Test, InvalidReport) {
  StorageBlockReportProto raw_report;
  raw_report.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V2);
  raw_report.set_blocksv2("abd");
  std::vector<BlkInfo> decoded_report;
  Status s = block_report_handler_->DecodeStorageBlockReportProto(
      "datanode-1", raw_report, &decoded_report);
  // TODO: unexpected
  // EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  // EXPECT_EQ(s.message(),
  //           "Failed to decode BlockReportBlockInfoProtoV2 from datanode-1");
}

// Copy from TEST_F(BlockManagerTest, DecodeRepotedBlockTestV2).
TEST_F(BlockReportHandlerDecodeStorageBlockReportProtoV2Test, HappyCase) {
  const int nstate = cloudfs::ReplicaStateProto_ARRAYSIZE;
  const int nblock_counter[nstate] = {200000, 200000, 200000, 200000, 200000};
  const int nblock =
      std::accumulate(nblock_counter, nblock_counter + nstate, 0);

  // Generate random blocks in v2 format.
  StorageBlockReportProto proto;
  proto.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V2);

  cloudfs::datanode::BlockReportBlockInfoProtoV2 blocksv2;
  {
    int nblk_cnt[nstate];
    std::copy(std::begin(nblock_counter),
              std::end(nblock_counter),
              std::begin(nblk_cnt));

    for (int i = 0; i < nblock; i++) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      uint64_t state;
      do {
        state = rand() % nstate;
      } while (nblk_cnt[state] == 0);
      nblk_cnt[state]--;

      BlockProto blk;
      blk.set_blockid(blkid);
      blk.set_genstamp(gs);
      blk.set_numbytes(nbyte);
      cloudfs::datanode::BlockReportBlockInfoProtoV2Entry ent;
      ent.mutable_block()->CopyFrom(blk);
      ent.set_replicastate(static_cast<ReplicaStateProto>(state));
      if (state == ReplicaStateProto::FINALIZED) {
        uint64_t cls = rand() % cloudfs::StorageClassProto_ARRAYSIZE;
        ent.set_storageclass(static_cast<StorageClassProto>(cls));
      }
      blocksv2.add_blocks()->CopyFrom(ent);
    }
  }

  // Compress.
  {
    std::string blocksv2_compressed;
    google::protobuf::io::GzipOutputStream::Options options;
    options.format = google::protobuf::io::GzipOutputStream::GZIP;
    options.compression_level = 9;
    google::protobuf::io::StringOutputStream outputStream(&blocksv2_compressed);
    google::protobuf::io::GzipOutputStream gzipStream(&outputStream, options);
    blocksv2.SerializeToZeroCopyStream(&gzipStream);
    gzipStream.Flush();

    proto.set_blocksv2(blocksv2_compressed);
  }

  // Decompress.
  std::vector<BlkInfo> blocks_decoded;
  {
    uint64_t fin_num, uc_num;
    Status s = block_report_handler_->DecodeStorageBlockReportProto(
        "datanode-1", proto, &blocks_decoded);
    EXPECT_TRUE(s.IsOK());
    LOG(INFO) << absl::StrFormat(
        "memory consumption of decoded blocks: %d bytes "
        "for %d reported entries.",
        blocks_decoded.size() * sizeof(blocks_decoded[0]),
        blocks_decoded.size());
  }

  // Check if equal.
  {
    ASSERT_EQ(blocks_decoded.size(), blocksv2.blocks_size());
    for (int i = 0; i < blocks_decoded.size(); i++) {
      auto& blk_before = blocksv2.blocks(i);
      auto& blk_after = blocks_decoded[i];
      EXPECT_EQ(blk_before.block().blockid(), blk_after.id);
      EXPECT_EQ(blk_before.block().genstamp(), blk_after.gs);
      EXPECT_EQ(blk_before.block().numbytes(), blk_after.len);
      EXPECT_EQ(blk_before.replicastate(), blk_after.replica_state);
      EXPECT_EQ(blk_before.storageclass(), blk_after.storage_class_report.stcls());
    }
  }
}

class BlockReportHandlerFBRMakeFullBlockReportTasksTest
    : public BlockReportHandlerFBRTest {};

TEST_F(BlockReportHandlerFBRMakeFullBlockReportTasksTest, EmptyReport) {
  std::vector<FullBlockReportTask> tasks;
  EXPECT_TRUE(
      block_report_handler_->MakeFullBlockReportTasks("datanode-1", {}, &tasks)
          .IsOK());
  EXPECT_EQ(tasks.size(), 0);
}

TEST_F(BlockReportHandlerFBRMakeFullBlockReportTasksTest,
       ReportSizeIsDivisibleByBatchSize) {
  FLAGS_process_block_report_batch_size = 2;
  StorageClassReportProto scr_none, scr_cold, scr_hot;
  scr_none.set_stcls(cloudfs::StorageClassProto::NONE);
  scr_none.set_pinned(false);
  scr_cold.set_stcls(cloudfs::StorageClassProto::COLD);
  scr_cold.set_pinned(false);
  scr_hot.set_stcls(cloudfs::StorageClassProto::HOT);
  scr_hot.set_pinned(false);
  std::vector<BlkInfo> decoded_report{
      {.id = 1076015581,
       .gs = 1001,
       .len = 1024,
       .replica_state = cloudfs::ReplicaStateProto::RBW,
       .storage_class_report = scr_none},
      {.id = 1076015582,
       .gs = 1002,
       .len = 1025,
       .replica_state = cloudfs::ReplicaStateProto::RUR,
       .storage_class_report = scr_none},
      {.id = 1076015583,
       .gs = 1003,
       .len = 1026,
       .replica_state = cloudfs::ReplicaStateProto::FINALIZED,
       .storage_class_report = scr_cold},
      {.id = 1076015584,
       .gs = 1004,
       .len = 1027,
       .replica_state = cloudfs::ReplicaStateProto::FINALIZED,
       .storage_class_report = scr_hot}};
  std::vector<FullBlockReportTask> tasks;
  EXPECT_TRUE(
      block_report_handler_
          ->MakeFullBlockReportTasks(
              "datanode-1", std::vector<BlkInfo>(decoded_report), &tasks)
          .IsOK());
  ASSERT_GT(decoded_report.size(), 0);
  auto context = tasks[0].context;
  ASSERT_EQ(context->report.size(), decoded_report.size());
  for (auto i = 0; i < context->report.size(); i++) {
    EXPECT_EQ(context->report[i].id, decoded_report[i].id);
    EXPECT_EQ(context->report[i].gs, decoded_report[i].gs);
    EXPECT_EQ(context->report[i].len, decoded_report[i].len);
    EXPECT_EQ(context->report[i].replica_state,
              decoded_report[i].replica_state);
    EXPECT_EQ(context->report[i].storage_class_report.SerializeAsString(),
              decoded_report[i].storage_class_report.SerializeAsString());
  }
  EXPECT_EQ(tasks.size(), 2);
  EXPECT_EQ(tasks[0].from_idx, 0);
  EXPECT_EQ(tasks[0].to_idx, 2);
  EXPECT_EQ(tasks[1].from_idx, 2);
  EXPECT_EQ(tasks[1].to_idx, 4);
}

TEST_F(BlockReportHandlerFBRMakeFullBlockReportTasksTest,
       ReportSizeIsNotDivisibleByBatchSize) {
  FLAGS_process_block_report_batch_size = 2;
  std::vector<FullBlockReportTask> tasks;
  StorageClassReportProto scr;
  scr.set_stcls(cloudfs::StorageClassProto::NONE);
  scr.set_pinned(false);
  EXPECT_TRUE(block_report_handler_
                  ->MakeFullBlockReportTasks(
                      "datanode-1",
                      {{.id = 1076015581,
                        .gs = 1001,
                        .len = 1024,
                        .replica_state = cloudfs::ReplicaStateProto::RBW,
                        .storage_class_report = scr},
                       {.id = 1076015582,
                        .gs = 1002,
                        .len = 1025,
                        .replica_state = cloudfs::ReplicaStateProto::RBW,
                        .storage_class_report = scr},
                       {.id = 1076015583,
                        .gs = 1003,
                        .len = 1026,
                        .replica_state = cloudfs::ReplicaStateProto::RBW,
                        .storage_class_report = scr}},
                      &tasks)
                  .IsOK());
  EXPECT_EQ(tasks.size(), 2);
  EXPECT_EQ(tasks[0].from_idx, 0);
  EXPECT_EQ(tasks[0].to_idx, 2);
  EXPECT_EQ(tasks[1].from_idx, 2);
  EXPECT_EQ(tasks[1].to_idx, 3);
}

class BlockReportHandlerFBRExecuteFullBlockReportTaskTest
    : public BlockReportHandlerFBRTest {};

TEST_F(BlockReportHandlerFBRExecuteFullBlockReportTaskTest,
       ExecuteWhenStandby) {
  ha_state_.SetState(cloudfs::HAServiceStateProto::STANDBY);
  FullBlockReportTask task;
  task.context = std::make_shared<FullBlockReportContext>();
  task.context->ongoing_task_cnt = 1;
  SynchronizedClosure done;
  task.context->done = &done;
  Status s = block_report_handler_->ExecuteFullBlockReportTask(task);
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  // Make sure done has been executed.
  done.Await();
}

TEST_F(BlockReportHandlerFBRExecuteFullBlockReportTaskTest,
       ExecuteWhenTransitionToActive) {
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(Return(ByMove(std::make_pair<Status, vshared_lock>(
          Status(JavaExceptions::kStandbyException), vshared_lock()))));
  FullBlockReportTask task;
  task.context = std::make_shared<FullBlockReportContext>();
  task.context->ongoing_task_cnt = 1;
  SynchronizedClosure done;
  task.context->done = &done;
  Status s = block_report_handler_->ExecuteFullBlockReportTask(task);
  EXPECT_EQ(s.exception(), JavaExceptions::kStandbyException);
  // Make sure done has been executed.
  done.Await();
}

TEST_F(BlockReportHandlerFBRExecuteFullBlockReportTaskTest, HappyCase) {
  EXPECT_CALL(time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(1676971558000));
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(Return(ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));

  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015583, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(BlockInfoProtoBuilder()
                                           .SetState(BlockInfoProto::kCommitted)
                                           .SetBlockId(1076015583)
                                           .SetGenStamp(1003)
                                           .SetNumBytes(1026)
                                           .SetINodeId(17327)
                                           .SetExpectedRep(3)
                                           .Build()),
                      Return(true)));

  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogFlushBlockInfoProtos(
          AllOf(Property(&BlockInfoProtos::content,
                         ElementsAre(AllOf(
                             Property(&BlockInfoProto::version, 1676971558000),
                             Property(&BlockInfoProto::block_id, 1076015583),
                             Property(&BlockInfoProto::replicas_size, 1)))),
                Property(&BlockInfoProtos::trigger, "datanode-1"))))
      .Times(1)
      .WillOnce(Invoke([this](const BlockInfoProtos& bips) {
        for (const BlockInfoProto& bip : bips.content()) {
          EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(bip.block_id())
                          .TestOnlyIsLocked());
        }
        return 1;
      }));

  EXPECT_CALL(*gmock_meta_storage_,
              FlushBlockInfoProtos(testing::_, testing::_, testing::_))
      .Times(1)
      .WillOnce(testing::Invoke(
          [this](const BlockInfoProtos& bips, int64_t txid, Closure* done) {
            gmock_meta_storage_->MetaStorage::FlushBlockInfoProtos(
                bips, txid, done);
          }));
  auto wb = new testing::StrictMock<GMockWriteBatch>();
  EXPECT_CALL(*gmock_meta_storage_, CreateWriteBatch)
      .Times(1)
      .WillOnce(Return(ByMove(std::unique_ptr<rocksdb::WriteBatch>(wb))));
  EXPECT_CALL(*wb, Put(kBlockInfoProtoCFHandle, EncodeBlockID(1076015583), _))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*wb, Put(kLocalBlockCFHandle, EncodeBlockID(1076015583), _))
      .Times(1)
      .WillOnce(Return(rocksdb::Status()));
  EXPECT_CALL(*gmock_meta_storage_writer_, Push(_))
      .Times(1)
      .WillRepeatedly([](meta_storage::WriteTask* task) {
        task->Next();
        delete task;
      });

  FullBlockReportTask task;
  task.context = std::make_shared<FullBlockReportContext>();
  task.context->dn_uuid = "datanode-1";
  StorageClassReportProto scr;
  scr.set_stcls(cloudfs::StorageClassProto::NONE);
  scr.set_pinned(false);
  task.context->report = {{.id = 1076015583,
                           .gs = 1003,
                           .len = 1026,
                           .replica_state = cloudfs::ReplicaStateProto::RBW,
                           .storage_class_report = scr}};
  task.context->ongoing_task_cnt = 1;
  SynchronizedClosure done;
  task.context->done = &done;
  task.from_idx = 0;
  task.to_idx = 1;
  EXPECT_TRUE(block_report_handler_->ExecuteFullBlockReportTask(task).IsOK());
  // Make sure done has been executed.
  done.Await();
}

TEST_F(BlockReportHandlerFBRExecuteFullBlockReportTaskTest,
       OmitEmptyLogFlushBlockInfoProtos) {
  EXPECT_CALL(ha_state_, IsActive()).WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, CheckOperation(OperationsCategory::kWrite))
      .Times(1)
      .WillOnce(Return(ByMove(
          std::make_pair<Status, vshared_lock>(Status(), vshared_lock()))));

  EXPECT_CALL(*gmock_meta_storage_, GetBlockInfo(1076015583, _))
      .Times(1)
      .WillOnce(
          DoAll(SetArgPointee<1>(
                    BlockInfoProtoBuilder()
                        .SetState(BlockInfoProto::kCommitted)
                        .SetBlockId(1076015583)
                        .SetGenStamp(1003)
                        .SetNumBytes(1026)
                        .SetINodeId(17327)
                        .SetExpectedRep(3)
                        .AddReplicas(
                            // ShouldReplicaReportTsFollowFBRTs(replica) ==true
                            ReplicaInfoProtoBuilder()
                                .SetReporter(ReplicaInfoProto::kDatanode)
                                .SetState(cloudfs::ReplicaStateProto::FINALIZED)
                                .SetGenStamp(1003)
                                .SetNumBytes(1026)
                                .SetDnUuid("datanode-1")
                                .Build())
                        .Build()),
                Return(true)));

  FullBlockReportTask task;
  task.context = std::make_shared<FullBlockReportContext>();
  task.context->dn_uuid = "datanode-1";
  StorageClassReportProto scr;
  scr.set_stcls(cloudfs::StorageClassProto::NONE);
  scr.set_pinned(false);
  task.context->report = {
      {.id = 1076015583,
       .gs = 1003,
       .len = 1026,
       .replica_state = cloudfs::ReplicaStateProto::FINALIZED,
       .storage_class_report = scr}};
  task.context->ongoing_task_cnt = 1;
  SynchronizedClosure done;
  task.context->done = &done;
  task.from_idx = 0;
  task.to_idx = 1;
  EXPECT_TRUE(block_report_handler_->ExecuteFullBlockReportTask(task).IsOK());
  // Make sure done has been executed.
  done.Await();
}

}  // namespace dancenn
