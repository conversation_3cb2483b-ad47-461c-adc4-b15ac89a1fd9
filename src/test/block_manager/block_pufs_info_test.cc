// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/06
// Description

#include <cnetpp/base/csonpp.h>
#include <gtest/gtest.h>
#include <regex>
#include <set>

#include "block_manager/block_pufs_info.h"

namespace dancenn {

class BlockPufsInfoTest : public testing::Test {
 public:
  void SetUp() override {
    json_str_ = std::regex_replace(json_str_, std::regex("\\|"), "");
    cnetpp::base::Value json_value;
    ASSERT_TRUE(cnetpp::base::Parser::Deserialize(json_str_, &json_value));
    json_str_ = cnetpp::base::Parser::Serialize(json_value);
  }

 protected:
  std::string json_str_ = R"({
    |  "block_pool_id": "BP-3-1622453889434",
    |  "block_id": 1073750578,
    |  "gen_stamp": 1001,
    |  "num_bytes": 1024,
    |  "inode_id": 2,
    |  "state": 2,
    |  "pufs_name": "BP-3-1622453889434:1073750578",
    |  "upload_issued_times_": 1,
    |  "dn_uuid": "b3830e6f-1437-4c45-a599-71901adf33b0",
    |  "aborted_upload_ids": [
    |    "907ba902095a0ab8aa548dfb"
    |  ],
    |  "upload_id": "34f5ff5174f80bb8aa54cf61",
    |  "nn_exp_ts": 1622715085,
    |  "dn_exp_ts": 1622715080
    })";
};

TEST_F(BlockPufsInfoTest, Serialize) {
  BlockPufsInfo info;
  info.block_pool_id_ = "BP-3-1622453889434";
  info.block_id_ = 1073750578;
  info.gen_stamp_ = 1001;
  info.num_bytes_ = 1024;
  info.inode_id_ = 2;
  info.state_ = BlockPufsState::kUploadIssued;
  info.pufs_name_ = "BP-3-1622453889434:1073750578";
  info.upload_issued_times_ = 1;
  info.dn_uuid_ = "b3830e6f-1437-4c45-a599-71901adf33b0";
  info.aborted_upload_ids_ = std::set<std::string>{"907ba902095a0ab8aa548dfb"};
  info.upload_id_ = "34f5ff5174f80bb8aa54cf61";
  info.nn_exp_ts_ = 1622715085;
  info.dn_exp_ts_ = 1622715080;
  EXPECT_EQ(info.SerializeToJsonString(), json_str_);
}

TEST_F(BlockPufsInfoTest, DeserializeSucceed) {
  BlockPufsInfo info;
  EXPECT_TRUE(info.DeserializeFromJsonString(json_str_));
  EXPECT_EQ(info.block_pool_id_, "BP-3-1622453889434");
  EXPECT_EQ(info.block_id_, 1073750578);
  EXPECT_EQ(info.gen_stamp_, 1001);
  EXPECT_EQ(info.num_bytes_, 1024);
  EXPECT_EQ(info.inode_id_, 2);
  EXPECT_EQ(info.state_, BlockPufsState::kUploadIssued);
  EXPECT_EQ(info.pufs_name_, "BP-3-1622453889434:1073750578");
  EXPECT_EQ(info.upload_issued_times_, 1);
  EXPECT_EQ(info.dn_uuid_, "b3830e6f-1437-4c45-a599-71901adf33b0");
  EXPECT_EQ(info.aborted_upload_ids_,
            std::set<std::string>{"907ba902095a0ab8aa548dfb"});
  EXPECT_EQ(info.upload_id_, "34f5ff5174f80bb8aa54cf61");
  EXPECT_EQ(info.nn_exp_ts_, 1622715085);
  EXPECT_EQ(info.dn_exp_ts_, 1622715080);
}

TEST_F(BlockPufsInfoTest, DeserializeFailed) {
  BlockPufsInfo info;
  EXPECT_FALSE(info.DeserializeFromJsonString(""));
  EXPECT_FALSE(info.DeserializeFromJsonString("{}"));
}

TEST_F(BlockPufsInfoTest, ConvertToAndFromBlockInfoProto) {
  BlockPufsInfo info;
  info.block_pool_id_ = "BP-3-1622453889434";
  info.block_id_ = 1073750578;
  info.gen_stamp_ = 1001;
  info.num_bytes_ = 1024;
  info.inode_id_ = 2;
  info.state_ = BlockPufsState::kUploadIssued;
  info.pufs_name_ = "BP-3-1622453889434:1073750578";
  info.upload_issued_times_ = 1;
  info.dn_uuid_ = "b3830e6f-1437-4c45-a599-71901adf33b0";
  info.aborted_upload_ids_ = std::set<std::string>{"907ba902095a0ab8aa548dfb",
                                                   "907ba902095a0ab8aa548dfx"};
  info.upload_id_ = "34f5ff5174f80bb8aa54cf61";
  info.nn_exp_ts_ = 1622715085;
  info.dn_exp_ts_ = 1622715080;

  BlockInfoProto bip;
  EXPECT_TRUE(info.GetBlockInfoProto(&bip));
  BlockPufsInfo info2;
  EXPECT_TRUE(info2.FromBlockInfoProto("BP-3-1622453889434", bip));
  EXPECT_EQ(info.SerializeToJsonString(), info2.SerializeToJsonString());

  info.state_ = BlockPufsState::kPersisted;
  EXPECT_TRUE(info.GetBlockInfoProto(&bip));
  EXPECT_TRUE(info2.FromBlockInfoProto("BP-3-1622453889434", bip));
  EXPECT_EQ(info.SerializeToJsonString(), info2.SerializeToJsonString());

  info.state_ = BlockPufsState::kDeprecated;
  EXPECT_TRUE(info.GetBlockInfoProto(&bip));
  EXPECT_TRUE(info2.FromBlockInfoProto("BP-3-1622453889434", bip));
  EXPECT_EQ(info.SerializeToJsonString(), info2.SerializeToJsonString());

  info.state_ = BlockPufsState::kLocal;
  EXPECT_FALSE(info.GetBlockInfoProto(&bip));
  info.state_ = BlockPufsState::kUploadFailed;
  EXPECT_FALSE(info.GetBlockInfoProto(&bip));
  info.state_ = BlockPufsState::kDeleted;
  EXPECT_FALSE(info.GetBlockInfoProto(&bip));
}

}  // namespace dancenn
