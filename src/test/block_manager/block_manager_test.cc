#include "block_manager/block_manager.h"

#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>

#include <cstdint>
#include <cstdio>
#include <cstdlib>
#include <ctime>
#include <exception>
#include <memory>
#include <sstream>
#include <vector>

#include "ClientNamenodeProtocol.pb.h"
#include "DatanodeProtocol.pb.h"
#include "base/closure.h"
#include "base/constants.h"
#include "base/file_utils.h"
#include "block_manager/block_manager_metrics.h"
#include "datanode_manager/datanode_manager.h"
#include "inode.pb.h"
#include "lifecycle.pb.h"
#include "test/mock_edit_log_context.h"
#include "test/mock_edit_log_sender.h"
#include "test/mock_ha_state.h"
#include "test/mock_safe_mode.h"
#include "test/namespace/mock_namespace.h"

DECLARE_bool(run_ut);
DECLARE_int32(bg_deletion_interval_in_sec);
DECLARE_int32(datanode_keep_alive_timeout_sec);
DECLARE_int32(datanode_stale_interval_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint32(dfs_replication_min);
DECLARE_uint32(dfs_replication_max);
DECLARE_bool(dfs_symlinks_enabled);
DECLARE_string(block_placement_policy);
DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_int32(blockmap_num_slice);
DECLARE_bool(bytecool_feature_enabled);
DECLARE_string(all_datacenters);
DECLARE_int32(bg_deletion_num_in_batch);
DECLARE_uint32(dfs_summary_min_depth);
DECLARE_bool(force_hyperblock_on_diffrent_dn);
DECLARE_bool(namespace_read_full_detail_blocks);
DECLARE_bool(recycle_bin_enable);
DECLARE_bool(recycle_bin_default_policy_enable);
DECLARE_uint32(recycle_bin_default_policy_time_sec);
DECLARE_int32(dfs_meta_scan_interval_sec);
DECLARE_int32(namespace_type);
DECLARE_int32(max_ongoing_block_report_req_count);
DECLARE_int32(max_ongoing_block_report_req_count_hard_limit);
DECLARE_uint64(block_report_scan_interval_sec);
DECLARE_bool(enable_load_for_complete_replica);
DECLARE_bool(client_replication_support);

namespace dancenn {
class BlockManagerTest : public testing::Test {
 public:
  void SetUp() override;
  void TearDown() override;
  void StartHeartbeat();
  bool RemoveCachedBlockInfo(BlockID blk_id);
  std::shared_ptr<MetaStorage> GetMetaStorage();
  CreateRequestProto MakeCreateRequest();
  AddBlockRequestProto MakeAddBlockRequest();
  CommitBlockSynchronizationRequestProto GetCommitSyncRequest(
      uint32_t len,
      const AddBlockResponseProto& add_response,
      bool close);
  bool IsNeededReplication(const BlockInfo* bi,
                           int expected_replication,
                           int num_live_replica);
  bool LoadBlockForZeroReplicaEnable(BlockInfo* bi, size_t pending_replica);

  const std::vector<std::string> DN{"***********",
                                    "***********",
                                    "***********",
                                    "***********"};
  const std::vector<std::string> DN_UUID = {"datanode1",
                                            "datanode2",
                                            "datanode3",
                                            "datanode4"};
  const std::string CLIENT_IP = "***********";
  const std::string CLIENT_NAME = "test_client";
  const std::string STORAGE_UUID = "storage";

  // std::shared_ptr<MetaStorage> meta_storage_;
  std::shared_ptr<BlockManager> block_manager_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::shared_ptr<MockNameSpace> ns_;
  std::unique_ptr<HAStateBase> ha_state_;
  std::unique_ptr<SafeModeBase> safe_mode_;
  std::thread heartbeat_thread_;
  bool stop_;
  std::string db_path_ = "rocksdb_XXXXXX";
  std::string block_pool_ = "bp";

  LogRpcInfo default_rpc_info_ = LogRpcInfo("", 0);
  int default_soft_limit_ms = FLAGS_lease_expired_soft_limit_ms;
  int default_hard_limit_ms = FLAGS_lease_expired_hard_limit_ms;
  int default_dn_keep_alive_timeout_sec = FLAGS_datanode_keep_alive_timeout_sec;
  int default_datanode_stale_interval_ms = FLAGS_datanode_stale_interval_ms;
  int default_blockmap_num_bucket_each_slice =
      FLAGS_blockmap_num_bucket_each_slice;
  int default_blockmap_num_slice = FLAGS_blockmap_num_slice;
  int default_dfs_replication_min = FLAGS_dfs_replication_min;
  int default_max_ongoing_block_report_req_count = FLAGS_max_ongoing_block_report_req_count;
  int default_max_ongoing_block_report_req_count_hard_limit =
      FLAGS_max_ongoing_block_report_req_count_hard_limit;

  UnderReplicatedBlocks& GetNeedReplication() {
    return block_manager_->needed_replications_;
  }
};

void BlockManagerTest::SetUp() {
  FLAGS_run_ut = true;
  FLAGS_all_datacenters = "LF,HL,LQ";
  FLAGS_lease_expired_soft_limit_ms = 200;
  FLAGS_lease_expired_hard_limit_ms = 400;
  FLAGS_datanode_keep_alive_timeout_sec = 1000;
  FLAGS_datanode_stale_interval_ms = FLAGS_lease_expired_hard_limit_ms * 30;
  FLAGS_blockmap_num_bucket_each_slice = 1;
  FLAGS_blockmap_num_slice = 1;
  FLAGS_dfs_replication_min = 1;
  FLAGS_bytecool_feature_enabled = true;
  FLAGS_dfs_meta_scan_interval_sec = 5;
  FLAGS_recycle_bin_default_policy_enable = false;
  FLAGS_recycle_bin_default_policy_time_sec = 10;
  FLAGS_max_ongoing_block_report_req_count = 100;
  FLAGS_max_ongoing_block_report_req_count_hard_limit = 100;
  FLAGS_block_report_scan_interval_sec = 1;
  FLAGS_namespace_type = cloudfs::NamespaceType::TOS_MANAGED;

  ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);

  // start namespace as active
  datanode_manager_ = std::make_shared<DatanodeManager>();
  block_manager_ = std::make_unique<BlockManager>();
  std::shared_ptr<EditLogContextBase> edit_log_ctx =
      std::static_pointer_cast<EditLogContextBase>(
          std::make_shared<MockEditLogContext>());
  MockFSImageTransfer(db_path_).Transfer();
  ns_.reset(new MockNameSpace(db_path_,
                              edit_log_ctx,
                              block_manager_,
                              datanode_manager_,
                              std::make_shared<DataCenters>(),
                              UfsEnv::Create()));
  auto ha_state = std::make_unique<MockHAState>();
  ha_state->should_populate_replication_queues = true;
  ha_state_ = move(ha_state);
  safe_mode_ = std::make_unique<MockSafeMode>();
  ns_->set_safemode(safe_mode_.get());
  ns_->set_ha_state(ha_state_.get());
  block_manager_->set_datanode_manager(datanode_manager_);
  block_manager_->set_ha_state(ha_state_.get());
  block_manager_->set_safemode(safe_mode_.get());
  block_manager_->set_ns(ns_.get());
  datanode_manager_->set_block_manager(block_manager_.get());
  ns_->Start();
  ns_->StartActive();

  // register datanode
  for (int i = 0; i < DN.size(); i++) {
    auto reg =
        cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(DN_UUID[i]);
    cnetpp::base::IPAddress ip(DN[i]);
    datanode_manager_->Register(reg.datanodeid(), &reg, ip);
  }

  StartHeartbeat();

  // mock edit log sender
  auto last_txid = ns_->GetLastCkptTxId();
  auto sender = std::unique_ptr<EditLogSenderBase>(
      new MockEditLogSender(edit_log_ctx, last_txid));
  ns_->TestOnlySetEditLogSender(std::move(sender));
  ns_->StopBGDeletionWorker();
  ns_->StopLeaseMonitor();

  // first report
  while (true) {
    bool done = true;
    for (int i = 0; i < DN.size(); i++) {
      auto dn = datanode_manager_->GetDatanodeFromUuid(DN_UUID[i]);
      if (dn->content_stale()) {
        done = false;
        break;
      }
      dn->UpdateLastNormalFbrTimepoint();
    }
    if (done) {
      break;
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
}

void BlockManagerTest::TearDown() {
  FLAGS_lease_expired_soft_limit_ms = default_soft_limit_ms;
  FLAGS_lease_expired_hard_limit_ms = default_hard_limit_ms;
  FLAGS_datanode_keep_alive_timeout_sec = default_dn_keep_alive_timeout_sec;
  FLAGS_datanode_stale_interval_ms = default_datanode_stale_interval_ms;
  FLAGS_blockmap_num_bucket_each_slice = default_blockmap_num_bucket_each_slice;
  FLAGS_blockmap_num_slice = default_blockmap_num_slice;
  FLAGS_dfs_replication_min = default_dfs_replication_min;
  FLAGS_max_ongoing_block_report_req_count = default_max_ongoing_block_report_req_count;
  FLAGS_max_ongoing_block_report_req_count_hard_limit =
      default_max_ongoing_block_report_req_count_hard_limit;

  stop_ = true;
  heartbeat_thread_.join();
  ns_->StopActive();
  ns_.reset();
  FileUtils::DeleteDirectoryRecursively(db_path_);
}

void BlockManagerTest::StartHeartbeat() {
  stop_ = false;
  CountDownLatch latch(1);
  heartbeat_thread_ = std::thread([&latch, this]() {
    std::vector<HeartbeatRequestProto> requests(4);
    std::vector<bool> heartbeated(4);
    std::vector<cloudfs::datanode::BlockReportRequestProto> br_req(4);
    for (int i = 0; i < DN.size(); i++) {
      RepeatedStorageReport reports;
      auto reg =
          cloudfs::datanode::DatanodeRegistrationProto::default_instance();
      reg.mutable_datanodeid()->set_datanodeuuid(DN_UUID[i]);
      cnetpp::base::IPAddress ip(DN[i]);
      auto r = reports.Add();
      r->set_storageuuid(STORAGE_UUID);
      r->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      r->mutable_storage()->set_storageuuid(STORAGE_UUID);
      requests[i].mutable_registration()->CopyFrom(reg);
      requests[i].mutable_reports()->CopyFrom(reports);

      br_req[i].mutable_registration()->CopyFrom(reg);
      br_req[i].set_blockpoolid(ns_->blockpool_id());
      br_req[i].mutable_context()->set_currpc(0);
      br_req[i].mutable_context()->set_totalrpcs(1);
      br_req[i].mutable_context()->set_id(0);
      auto report = br_req[i].add_reports();
      report->mutable_storage()->set_storageuuid(STORAGE_UUID);
      report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
      report->add_blocks(0);
      report->add_blocks(0);
    }
    bool done = false;
    while (!stop_) {
      DatanodeManager::RepeatedCmds cmds;
      for (int i = 0; i < requests.size(); i++) {
        datanode_manager_->Heartbeat(requests[i], &cmds);
        cloudfs::datanode::HeartbeatResponseProto res;
        int idx = datanode_manager_->GetDatanodeInterId(DN_UUID[i]);
        block_manager_->GetBlockReportCommand(idx, &res);
        if (res.cmds_size() == 1 &&
            res.cmds(0).cmdtype() ==
                cloudfs::datanode::
                    DatanodeCommandProto_Type_BlockReportCommand) {
          SynchronizedRpcClosure done;
          block_manager_->AsyncBlockReport(DN_UUID[i], &br_req[i], &done);
          done.Await();
          EXPECT_TRUE(done.status().IsOK());
          heartbeated[i] = true;
        }
      }

      if (!done) {
        bool check = true;
        for (bool d : heartbeated) {
          check &= d;
        }
        done = check;
        if (done) {
          latch.CountDown();
        }
      }
      std::this_thread::sleep_for(std::chrono::seconds(1));
    }
  });
  latch.Await();
}

bool BlockManagerTest::RemoveCachedBlockInfo(BlockID blk_id) {
  return block_manager_->RemoveCachedBlockInfo(blk_id);
}

std::shared_ptr<MetaStorage> BlockManagerTest::GetMetaStorage() {
  return block_manager_->meta_storage_;
}

CreateRequestProto BlockManagerTest::MakeCreateRequest() {
  CreateRequestProto request;
  request.set_src("");
  request.mutable_masked()->set_perm(0);
  request.set_clientname(CLIENT_NAME);
  request.set_createflag(::cloudfs::CreateFlagProto::CREATE);
  request.set_createparent(false);
  request.set_replication(1);
  request.set_blocksize(128 * 1024 * 1024);
  return request;
}

AddBlockRequestProto BlockManagerTest::MakeAddBlockRequest() {
  AddBlockRequestProto add_request;
  add_request.set_src("");
  add_request.set_clientname(CLIENT_NAME);
  return add_request;
}

CommitBlockSynchronizationRequestProto BlockManagerTest::GetCommitSyncRequest(
    uint32_t len,
    const AddBlockResponseProto& add_response,
    bool close) {
  CommitBlockSynchronizationRequestProto commit_request;
  commit_request.mutable_block()->CopyFrom(add_response.block().b());
  commit_request.set_newlength(len);
  commit_request.set_newgenstamp(add_response.block().b().generationstamp());
  commit_request.set_closefile(close);
  return commit_request;
}

bool BlockManagerTest::IsNeededReplication(const BlockInfo* bi,
                                           int expected_replication,
                                           int num_live_replica) {
  INode inode;
  if (GetMetaStorage()->GetINode(bi->inode_id(), &inode) != kOK) {
    inode.set_id(kInvalidINodeId);
  }
  return block_manager_->IsNeededReplication(
      bi, &inode, expected_replication, num_live_replica);
}

  bool BlockManagerTest::LoadBlockForZeroReplicaEnable(BlockInfo* bi, size_t pending_replica) {
    return block_manager_->LoadBlockForZeroReplicaEnable(bi, pending_replica);
  }

TEST_F(BlockManagerTest, corruptFINBlockReport) {
  const int replication = 3;
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  // 3. report three replica
  for (int i = 0; i < replication; i++) {
    BlockManager::RepeatedIncBlockReport reports;
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    blk->mutable_block()->set_numbytes(file_len);
    blk->set_status(
        cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 4. complete file
  CompleteRequestProto complete_request;
  complete_request.set_clientname(CLIENT_NAME);
  complete_request.mutable_last()->set_blockid(
      add_response.block().b().blockid());
  complete_request.mutable_last()->set_generationstamp(
      add_response.block().b().generationstamp());
  complete_request.mutable_last()->set_numbytes(file_len);
  ASSERT_TRUE(!ns_->CompleteFile(file_path, complete_request).HasException());

  // 5. report a block whose gs < normal gs
  // it will be recognized as a corrupt block
  BlockManager::RepeatedIncBlockReport reports;
  int gs_delta = 5;
  auto report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  auto blk = report->add_blocks();
  blk->mutable_block()->set_blockid(add_response.block().b().blockid());
  int corrupt_gs = add_response.block().b().generationstamp() - gs_delta;
  blk->mutable_block()->set_genstamp(corrupt_gs);
  blk->mutable_block()->set_numbytes(file_len);
  blk->set_status(
      cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING);
  block_manager_->IncrementalBlockReport(DN_UUID[3], reports);

  // 6. check GetCommands interface of block manager there should be a command
  //     which aims to delete the last corrupt block on DN
  auto corrupt_blocks = block_manager_->GetCorruptBlockIDs(0, 10).second;
  ASSERT_EQ(corrupt_blocks.size(), 1);
  ASSERT_EQ(corrupt_blocks.at(0), add_response.block().b().blockid());

  std::this_thread::sleep_for(std::chrono::seconds(10));
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  block_manager_->GetCommands(
      datanode_manager_->GetDatanodeFromUuid(DN_UUID[3])->id(),
      "bpid",
      0,
      &heartbeat_response);
  // ASSERT_EQ(heartbeat_response.cmds_size(), 1);
  auto cmd = heartbeat_response.cmds(1);
  ASSERT_EQ(cmd.cmdtype(),
            cloudfs::datanode::DatanodeCommandProto::Type::
                DatanodeCommandProto_Type_BlockCommand);
  ASSERT_EQ(cmd.blkcmd().action(),
            cloudfs::datanode::BlockCommandProto::Action::
                BlockCommandProto_Action_INVALIDATE);
  ASSERT_EQ(cmd.blkcmd().blocks().size(), 1);
  ASSERT_EQ(cmd.blkcmd().blocks(0).blockid(),
            add_response.block().b().blockid());
  ASSERT_EQ(cmd.blkcmd().blocks(0).genstamp(),
            add_response.block().b().generationstamp() - gs_delta);
  LOG(ERROR) << "Start check";

  // 7. check if there's a corrupt replica in block info
  uint64_t block_id = add_response.block().b().blockid();
  std::stringstream ss;
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  std::string dumped = ss.str();
  ASSERT_NE(dumped.find("live=3 corrupt=1"), dumped.npos) << dumped;

  // 8. delete corrupt block
  reports.Clear();
  report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  blk = report->add_blocks();
  blk->mutable_block()->set_blockid(add_response.block().b().blockid());
  blk->mutable_block()->set_genstamp(corrupt_gs);
  blk->mutable_block()->set_numbytes(file_len);
  blk->set_status(
      cloudfs::datanode::ReceivedDeletedBlockInfoProto::DELETED);
  block_manager_->IncrementalBlockReport(DN_UUID[3], reports);

  // 9. check if corrupt is removed from block info
  ss.clear();
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  dumped = ss.str();
  ASSERT_NE(dumped.find("live=3 corrupt=0"), dumped.npos) << dumped;
}

TEST_F(BlockManagerTest, corruptUCBlockReport) {
  const int replication = 3;
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  // 3. report three replica
  BlockManager::RepeatedIncBlockReport reports;
  for (int i = 0; i < replication; i++) {
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    blk->mutable_block()->set_numbytes(file_len);
    blk->set_status(
        cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 4. decommission the 4th DN
  datanode_manager_->SetDecommissioning(4);

  // 5. report a UC block with smaller gs and len, on a decommissioning DN
  // it will be recognized as a corrupt block
  int gs_delta = 5;
  int len_delta = 5;
  reports.Clear();
  auto report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  auto blk = report->add_blocks();
  blk->mutable_block()->set_blockid(add_response.block().b().blockid());
  int corrupt_gs = add_response.block().b().generationstamp() - gs_delta;
  int corrupt_len = file_len - len_delta;
  blk->mutable_block()->set_genstamp(corrupt_gs);
  blk->mutable_block()->set_numbytes(corrupt_len);
  blk->set_status(
      cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING);
  block_manager_->IncrementalBlockReport(DN_UUID[3], reports);

  // 6. check GetCommands interface of block manager there should be a command
  //     which aims to delete the last corrupt block on DN
  auto corrupt_blocks = block_manager_->GetCorruptBlockIDs(0, 10).second;
  ASSERT_EQ(corrupt_blocks.size(), 1);
  ASSERT_EQ(corrupt_blocks.at(0), add_response.block().b().blockid());

  std::this_thread::sleep_for(std::chrono::seconds(10));
  cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
  block_manager_->GetCommands(
      datanode_manager_->GetDatanodeFromUuid(DN_UUID[3])->id(),
      "bpid",
      0,
      &heartbeat_response);
  // ASSERT_EQ(heartbeat_response.cmds_size(), 1);
  auto cmd = heartbeat_response.cmds(1);
  ASSERT_EQ(cmd.cmdtype(),
            cloudfs::datanode::DatanodeCommandProto::Type::
                DatanodeCommandProto_Type_BlockCommand);
  ASSERT_EQ(cmd.blkcmd().action(),
            cloudfs::datanode::BlockCommandProto::Action::
                BlockCommandProto_Action_INVALIDATE);
  ASSERT_EQ(cmd.blkcmd().blocks().size(), 1);
  ASSERT_EQ(cmd.blkcmd().blocks(0).blockid(),
            add_response.block().b().blockid());
  ASSERT_EQ(cmd.blkcmd().blocks(0).genstamp(),
            add_response.block().b().generationstamp() - gs_delta);

  // 7. check if there's a corrupt replica in block info
  uint64_t block_id = add_response.block().b().blockid();
  std::stringstream ss;
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  std::string dumped = ss.str();
  ASSERT_NE(dumped.find("live=3 corrupt=1"), dumped.npos) << dumped;

  // 8. delete corrupt block
  reports.Clear();
  report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  blk = report->add_blocks();
  blk->mutable_block()->set_blockid(add_response.block().b().blockid());
  blk->mutable_block()->set_genstamp(corrupt_gs);
  blk->mutable_block()->set_numbytes(file_len);
  blk->set_status(
      cloudfs::datanode::ReceivedDeletedBlockInfoProto::DELETED);
  block_manager_->IncrementalBlockReport(DN_UUID[3], reports);

  // 9. check if corrupt is removed from block info
  ss.clear();
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  dumped = ss.str();
  ASSERT_NE(dumped.find("live=3 corrupt=0"), dumped.npos) << dumped;
}

TEST_F(BlockManagerTest, TestSpeedUpUCBlockRelease) {
  using ::testing::_;
  using ::testing::AtLeast;

  int default_blockmap_num_bucket_each_slice =
      FLAGS_blockmap_num_bucket_each_slice;
  int default_blockmap_num_slice = FLAGS_blockmap_num_slice;
  FLAGS_blockmap_num_bucket_each_slice = 16;
  FLAGS_blockmap_num_slice = 16;
  auto block_manager_ = std::make_unique<BlockManager>();

  GMockNameSpace mock_ns;
  EXPECT_CALL(mock_ns, SpeedUpRelease(kLastReservedINodeId + 1)).Times(AtLeast(1));
  EXPECT_CALL(mock_ns, SpeedUpRelease(kLastReservedINodeId + 2)).Times(AtLeast(1));
  EXPECT_CALL(mock_ns, SpeedUpRelease(kLastReservedINodeId + 3)).Times(AtLeast(1));

  block_manager_->set_ns(&mock_ns);
  DEFER([&]() {
    block_manager_->set_ns(ns_.get());
    return true;
  });
  block_manager_->AddBlock(1,
                           kLastReservedINodeId + 1,
                           kRootINodeId,
                           123,
                           123,
                           123,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kUnderConstruction);
  block_manager_->AddBlock(2,
                           kLastReservedINodeId + 2,
                           kRootINodeId,
                           123,
                           123,
                           123,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kUnderConstruction);
  block_manager_->AddBlock(3,
                           kLastReservedINodeId + 3,
                           kRootINodeId,
                           123,
                           123,
                           123,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kUnderConstruction);
  block_manager_->AddBlock(4,
                           kLastReservedINodeId + 3,
                           kRootINodeId,
                           123,
                           123,
                           123,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kUnderConstruction);
  block_manager_->AddBlock(5,
                           kLastReservedINodeId + 5,
                           kRootINodeId,
                           123,
                           123,
                           123,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kCommitted);
  std::vector<BlockID> blk_ids{1, 2, 3, 4, 5};
  ASSERT_EQ(3, block_manager_->SpeedUpUCBlockRelease(blk_ids));

  FLAGS_blockmap_num_bucket_each_slice = default_blockmap_num_bucket_each_slice;
  FLAGS_blockmap_num_slice = default_blockmap_num_slice;
}

TEST_F(BlockManagerTest, IsLastBlkReadyToComplete) {
  FLAGS_dfs_replication_min = 1;
  block_manager_->AddBlock(1075174965L,
                           kRootINodeId + 1,
                           kRootINodeId,
                           2L * 1024 * 1024 * 1024,  // 64GiB
                           17104101782323L,
                           3,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kUnderRecovery);

  auto s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 2L * 1024 * 1024 * 1024 + 1, 17104101782323L));
  EXPECT_FALSE(s.IsOK());
  EXPECT_EQ(s.message(),
            "Try to commit/complete under recovery last block, "
            "bi: {id: 1075174965, gs: 17104101782323, len: 2147483648}");

  block_manager_->TestOnlyGetSlice(1075174965L)
      ->Locate(1075174965L)
      ->set_uc_state(BlockUCState::kUnderConstruction);
  s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 2L * 1024 * 1024 * 1024, 17104101782323L + 1));
  EXPECT_FALSE(s.IsOK());
  EXPECT_EQ(
      s.message(),
      "Try to commit/complete corrupt last block, "
      "blk_from_client: {id: 1075174965, gs: 17104101782324, len: 2147483648}, "
      "bi: {id: 1075174965, gs: 17104101782323, len: 2147483648}");

  s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 1, 17104101782323L));
  EXPECT_FALSE(s.IsOK());
  EXPECT_EQ(s.message(),
            "Try to commit/complete corrupt last block, "
            "blk_from_client: {id: 1075174965, gs: 17104101782323, len: 1}, "
            "bi: {id: 1075174965, gs: 17104101782323, len: 2147483648}");

  s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 2L * 1024 * 1024 * 1024, 17104101782323L));
  EXPECT_FALSE(s.IsOK());
  EXPECT_EQ(
      s.message(),
      "Try to commit/complete last block without enough replica, "
      "blk_from_client: {id: 1075174965, gs: 17104101782323, len: 2147483648}, "
      "replica_num: {live: 0, corrupt: 0}");

  BlockManager::RepeatedIncBlockReport reports;
  auto report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  auto blk = report->add_blocks();
  blk->mutable_block()->set_blockid(1075174965L);
  blk->mutable_block()->set_genstamp(17104101782323L - 1);
  blk->mutable_block()->set_numbytes(2L * 1024 * 1024 * 1024);
  blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
  block_manager_->IncrementalBlockReport(DN_UUID[0], reports);
  s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 2L * 1024 * 1024 * 1024, 17104101782323L));
  EXPECT_FALSE(s.IsOK());
  EXPECT_EQ(
      s.message(),
      "Try to commit/complete last block without enough replica, "
      "blk_from_client: {id: 1075174965, gs: 17104101782323, len: 2147483648}, "
      "replica_num: {live: 0, corrupt: 1}");

  reports.Clear();
  report = reports.Add();
  report->set_storageuuid(STORAGE_UUID);
  blk = report->add_blocks();
  blk->mutable_block()->set_blockid(1075174965L);
  blk->mutable_block()->set_genstamp(17104101782323L);
  blk->mutable_block()->set_numbytes(2L * 1024 * 1024 * 1024 + 1);
  blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
  block_manager_->IncrementalBlockReport(DN_UUID[0], reports);
  s = block_manager_->IsLastBlkReadyToComplete(
      Block(1075174965L, 2L * 1024 * 1024 * 1024 + 1, 17104101782323L));
  EXPECT_TRUE(s.IsOK());
}

using cloudfs::datanode::StorageBlockReportProto;
using cloudfs::datanode::BlockReportBlockInfoProtoV2;
using cloudfs::datanode::BlockReportBlockInfoProtoV2Entry;
using cloudfs::StorageClassProto;
using cloudfs::ReplicaStateProto;
using cloudfs::BlockProto;

TEST_F(BlockManagerTest, DecodeReportedBlockTestV1) {
  const int nstate = cloudfs::ReplicaStateProto_ARRAYSIZE;
  const int nblock_counter[nstate] =
      { 1000000, 1000000, 1000000, 1000000, 1000000 };
  const int nblock = std::accumulate(nblock_counter, nblock_counter + nstate, 0);
  std::vector<BlkInfo> blocks_expected;

  // generate random blocks in v1 format
  StorageBlockReportProto proto;
  // proto.set_blocksformatversion(
  //     cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V1);

  {
    int nblk_cnt[nstate];
    std::copy(std::begin(nblock_counter),
              std::end(nblock_counter),
              std::begin(nblk_cnt));

    // part 1: fin_num, uc_num
    proto.add_blocks(nblk_cnt[ReplicaStateProto::FINALIZED]);
    proto.add_blocks(nblock - nblk_cnt[ReplicaStateProto::FINALIZED]);

    // part 2: { id, gs, len } * fin_num
    while (nblk_cnt[ReplicaStateProto::FINALIZED] > 0) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      proto.add_blocks(blkid);
      proto.add_blocks(nbyte);
      proto.add_blocks(gs);
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      blocks_expected.emplace_back(
          BlkInfo { blkid,
                    gs,
                    nbyte,
                    ReplicaStateProto::FINALIZED,
                    scr });

      nblk_cnt[ReplicaStateProto::FINALIZED]--;
    }

    // part 3: delimiter -1 * 3
    proto.add_blocks(static_cast<uint64_t>(-1));
    proto.add_blocks(static_cast<uint64_t>(-1));
    proto.add_blocks(static_cast<uint64_t>(-1));

    // part 4: { id, gs, len, state } * uc_num
    while (std::accumulate(nblk_cnt, nblk_cnt + nstate, 0) > 0) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      uint64_t state;
      do {
        state = rand() % nstate;
      } while (nblk_cnt[state] == 0);
      proto.add_blocks(blkid);
      proto.add_blocks(nbyte);
      proto.add_blocks(gs);
      proto.add_blocks(state);
      StorageClassReportProto scr;
      scr.set_stcls(StorageClassProto::NONE);
      scr.set_pinned(false);
      blocks_expected.emplace_back(
          BlkInfo { blkid,
                    gs,
                    nbyte,
                    static_cast<ReplicaStateProto>(state),
                    scr });

      nblk_cnt[state]--;
    }
  }

  // decode
  std::vector<std::vector<BlkInfo>> blocks_decoded;
  {
    uint64_t fin_num, uc_num;
    Status st = block_manager_->DecodeBlocks(proto,
                                             1,
                                             &blocks_decoded,
                                             &fin_num,
                                             &uc_num);
    ASSERT_TRUE(st.IsOK());

    size_t sum = 0;
    for (auto& blks : blocks_decoded) {
      sum += blks.size() * sizeof(blks[0]);
    }
    LOG(INFO) << "memory consumption of decoded blocks: " << sum << " byte"
              << " for " << nblock << " reported entries.";
  }

  // check equal
  {
    auto& blks_decoded = blocks_decoded.at(0);
    ASSERT_EQ(blocks_expected.size(), blks_decoded.size());
    for (int i = 0; i < blks_decoded.size(); i++) {
      auto& blk_before = blocks_expected[i];
      auto& blk_after = blks_decoded[i];

      ASSERT_EQ(blk_before.id, blk_after.id);
      ASSERT_EQ(blk_before.gs, blk_after.gs);
      ASSERT_EQ(blk_before.len, blk_after.len);
      ASSERT_EQ(blk_before.replica_state, blk_after.replica_state);
      ASSERT_EQ(blk_before.storage_class_report.SerializeAsString(),
                blk_after.storage_class_report.SerializeAsString());
    }
  }
}

TEST_F(BlockManagerTest, DecodeReportedBlockTestV2) {
  const int nstate = cloudfs::ReplicaStateProto_ARRAYSIZE;
  const int nblock_counter[nstate] =
      { 1000000, 1000000, 1000000, 1000000, 1000000 };
  const int nblock = std::accumulate(nblock_counter, nblock_counter + nstate, 0);

  // generate random blocks in v2 format
  StorageBlockReportProto proto;
  proto.set_blocksformatversion(
      cloudfs::datanode::StorageBlockReportProto_BlocksFormat_V2);

  BlockReportBlockInfoProtoV2 blocksv2;
  {
    int nblk_cnt[nstate];
    std::copy(std::begin(nblock_counter),
              std::end(nblock_counter),
              std::begin(nblk_cnt));

    for (int i = 0; i < nblock; i++) {
      uint64_t blkid = rand();
      uint64_t gs = rand();
      uint64_t nbyte = rand();
      uint64_t state;
      do {
        state = rand() % nstate;
      } while (nblk_cnt[state] == 0);
      nblk_cnt[state]--;

      BlockProto blk;
      blk.set_blockid(blkid);
      blk.set_genstamp(gs);
      blk.set_numbytes(nbyte);
      BlockReportBlockInfoProtoV2Entry ent;
      ent.mutable_block()->CopyFrom(blk);
      ent.set_replicastate(static_cast<ReplicaStateProto>(state));
      if (state == ReplicaStateProto::FINALIZED) {
        uint64_t cls = rand() % cloudfs::StorageClassProto_ARRAYSIZE;
        ent.set_storageclass(static_cast<StorageClassProto>(cls));
      }
      blocksv2.add_blocks()->CopyFrom(ent);
    }
  }

  // compress
  {
    std::string blocksv2_compressed;
    google::protobuf::io::GzipOutputStream::Options options;
    options.format = google::protobuf::io::GzipOutputStream::GZIP;
    options.compression_level = 9;
    google::protobuf::io::StringOutputStream outputStream(&blocksv2_compressed);
    google::protobuf::io::GzipOutputStream gzipStream(&outputStream, options);
    blocksv2.SerializeToZeroCopyStream(&gzipStream);
    gzipStream.Flush();

    proto.set_blocksv2(blocksv2_compressed);
  }
  // by default, limit size of CodedInputStream is 64MB (kDefaultTotalBytesLimit)
  ASSERT_GT(proto.ByteSize(), 64UL << 20);

  // decompress
  std::vector<std::vector<BlkInfo>> blocks_decoded;
  {
    uint64_t fin_num, uc_num;
    int n = 1;
    blocks_decoded.resize(n);
    Status st = block_manager_->DecodeBlocks(proto,
                                             n,
                                             &blocks_decoded,
                                             &fin_num,
                                             &uc_num);
    ASSERT_TRUE(st.IsOK());

    size_t sum = 0;
    for (auto& blks : blocks_decoded) {
      sum += blks.size() * sizeof(blks[0]);
    }
    LOG(INFO) << "memory consumption of decoded blocks: " << sum << " byte"
              << " for " << nblock << " reported entries.";
  }

  // check equal
  {
    auto& blks_decoded = blocks_decoded.at(0);
    ASSERT_EQ(blks_decoded.size(), blocksv2.blocks_size());
    for (int i = 0; i < blks_decoded.size(); i++) {
      auto& blk_before = blocksv2.blocks(i);
      auto& blk_after = blks_decoded[i];

      ASSERT_EQ(blk_before.block().blockid(), blk_after.id);
      ASSERT_EQ(blk_before.block().genstamp(), blk_after.gs);
      ASSERT_EQ(blk_before.block().numbytes(), blk_after.len);
      ASSERT_EQ(blk_before.replicastate(), blk_after.replica_state);
      ASSERT_EQ(blk_before.storageclass(), blk_after.storage_class_report.stcls());
      ASSERT_EQ(blk_before.pinned(), blk_after.storage_class_report.pinned());
    }
  }
}

TEST_F(BlockManagerTest, SplittedFullBlockReport) {
  // 1. create file
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(1);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_TRUE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .IsOK());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_TRUE(ns_->AddBlock(file_path,
                            client_ip,
                            default_rpc_info_,
                            add_request,
                            &add_response)
                  .IsOK());
}

TEST_F(BlockManagerTest, IsNeededReplication) {
  const int replication = 3;
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  PermissionStatus permission;
  const std::string file_path = "/User/IsNeededReplication/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  try {
    ASSERT_FALSE(
        ns_->CreateFile(file_path, permission, create_request, &create_response)
            .HasException());

  } catch (std::exception& e) {
    LOG(INFO) << e.what();
  }

  uint64_t inode_id = create_response.fs().fileid();

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  BlockInfo blk_info;
  blk_info.UpdateINode(inode_id);
  blk_info.set_uc_state(BlockUCState::kUnderConstruction);

  // blk is UC
  ASSERT_FALSE(IsNeededReplication(&blk_info, 1, 3));
  ASSERT_FALSE(IsNeededReplication(&blk_info, 3, 1));

  // 3. complete file
  CompleteRequestProto complete_request;
  complete_request.set_clientname(CLIENT_NAME);
  complete_request.mutable_last()->set_blockid(
      add_response.block().b().blockid());
  complete_request.mutable_last()->set_generationstamp(
      add_response.block().b().generationstamp());
  complete_request.mutable_last()->set_numbytes(file_len);
  ASSERT_TRUE(!ns_->CompleteFile(file_path, complete_request).HasException());

  // blk is completed
  blk_info.set_uc_state(BlockUCState::kComplete);
  ASSERT_FALSE(IsNeededReplication(&blk_info, 1, 3));
  ASSERT_TRUE(IsNeededReplication(&blk_info, 3, 1));

  // blk is persisted , file not pinned
  blk_info.set_uc_state(BlockUCState::kPersisted);
  ASSERT_FALSE(IsNeededReplication(&blk_info, 1, 3));
  ASSERT_FALSE(IsNeededReplication(&blk_info, 3, 1));

  // 4. pin file
  {
    PinRequestProto req;
    req.set_src(file_path);
    req.set_ttl(-1);
    req.set_recursive(false);
    req.set_unpin(false);
    PinResponseProto res;
    SynchronizedRpcClosure rpc_done;
    ns_->AsyncPin(file_path, &req, &res, LogRpcInfo(), &rpc_done, nullptr);
    rpc_done.Await();
    ASSERT_TRUE(rpc_done.status().IsOK());
    // blk is persisted , file is pinned, data is not resident
    blk_info.set_uc_state(BlockUCState::kPersisted);
    ASSERT_FALSE(IsNeededReplication(&blk_info, 1, 3));
    ASSERT_FALSE(IsNeededReplication(&blk_info, 3, 1));
  }

  // 5. mark resident cache
  {
    INode targetInode;
    targetInode.set_id(inode_id);
    SynchronizedRpcClosure rpc_done;
    ns_->UpdatePinDataResident(file_path, targetInode, true, &rpc_done);
    rpc_done.Await();
    ASSERT_TRUE(rpc_done.status().IsOK());
    ASSERT_FALSE(IsNeededReplication(&blk_info, 1, 3));
    ASSERT_TRUE(IsNeededReplication(&blk_info, 3, 1));
    ASSERT_FALSE(IsNeededReplication(&blk_info, 3, 3));
  }
}

TEST_F(BlockManagerTest, AddTransferBlocks) {
  BlockID blk_id = 1;
  bool res = block_manager_->AddTransferBlocks(
      blk_id, 1, UnderReplicatedBlocks::Priority::kUnderReplicated);
  // Block not exist
  ASSERT_FALSE(res);

  Block blk =
      Block(1, 1024, 17104101782323L);
  std::vector<DatanodeID> dn_id = {1};
  GMockNameSpace mock_ns;
  block_manager_->set_ns(&mock_ns);
  DEFER([&]() {
    block_manager_->set_ns(ns_.get());
    return true;
  });
  block_manager_->AddBlock(blk,
                           kLastReservedINodeId + 1,
                           kRootINodeId,
                           1,
                           cloudfs::DATANODE_BLOCK,
                           dn_id,
                           BlockUCState::kUnderConstruction);

  // Expected replica is 0
  res = block_manager_->AddTransferBlocks(
      blk_id, 0, UnderReplicatedBlocks::Priority::kUnderReplicated);
  ASSERT_FALSE(res);

  res = block_manager_->AddTransferBlocks(
      blk_id, 2, UnderReplicatedBlocks::Priority::kUnderReplicated);
  ASSERT_TRUE(res);
  ASSERT_EQ(1, GetNeedReplication().size());
  ASSERT_TRUE(GetNeedReplication().Contains(blk));
}

TEST_F(BlockManagerTest, RemoveCachedBlockInfo) {
  BlockID not_exist_blk_id = 1;
  BlockID committed_blk_id = 2;
  BlockID persisted_blk_id = 3;
  long num_bytes = 2L * 1024 * 1024 * 1024;

  BlockInfoProto committed_blk;
  committed_blk.set_state(BlockInfoProto::kCommitted);
  committed_blk.set_block_id(committed_blk_id);
  committed_blk.set_gen_stamp(2);
  committed_blk.set_num_bytes(num_bytes);
  committed_blk.set_inode_id(1);
  committed_blk.set_expected_rep(3);
  committed_blk.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);

  BlockInfoProto persisted_blk;
  persisted_blk.set_state(BlockInfoProto::kPersisted);
  persisted_blk.set_block_id(persisted_blk_id);
  persisted_blk.set_gen_stamp(3);
  persisted_blk.set_num_bytes(num_bytes);
  persisted_blk.set_inode_id(1);
  persisted_blk.set_expected_rep(3);
  persisted_blk.set_write_mode(cloudfs::IoMode::DATANODE_BLOCK);

  GetMetaStorage()->PutBlockInfo(committed_blk, nullptr, 1L, nullptr);
  GetMetaStorage()->PutBlockInfo(persisted_blk, nullptr, 2L, nullptr);
  GetMetaStorage()->TxFinish(1, 3);
  GetMetaStorage()->WaitNoPending(true);

  block_manager_->AddBlock(committed_blk_id,
                           kRootINodeId + 1,
                           kRootINodeId,
                           num_bytes,  // 64GiB
                           2,
                           3,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kCommitted);
  block_manager_->AddBlock(persisted_blk_id,
                           kRootINodeId + 1,
                           kRootINodeId,
                           num_bytes,  // 64GiB
                           3,
                           3,
                           cloudfs::DATANODE_BLOCK,
                           BlockUCState::kPersisted);

  bool res = RemoveCachedBlockInfo(not_exist_blk_id);
  ASSERT_FALSE(res);

  res = RemoveCachedBlockInfo(committed_blk_id);
  ASSERT_FALSE(res);

  res = RemoveCachedBlockInfo(persisted_blk_id);
  ASSERT_TRUE(res);
}

TEST_F(BlockManagerTest, LoadBlockForZeroReplicaEnable) {
  BlockInfo* bi_null = nullptr;
  bool res = LoadBlockForZeroReplicaEnable(bi_null, 0);
  ASSERT_FALSE(res);

  BlockInfo bi;
  bi.set_uc_state(BlockUCState::kPersisted);
  FLAGS_enable_load_for_complete_replica = false;
  res = LoadBlockForZeroReplicaEnable(&bi, 0);
  ASSERT_FALSE(res);

  FLAGS_enable_load_for_complete_replica = true;
  res = LoadBlockForZeroReplicaEnable(&bi, 0);
  ASSERT_FALSE(res);

  FLAGS_namespace_type = static_cast<int32_t>(cloudfs::NamespaceType::ACC_TOS);
  res = LoadBlockForZeroReplicaEnable(&bi, 0);
  ASSERT_TRUE(res);
  res = LoadBlockForZeroReplicaEnable(&bi, 1);
  ASSERT_FALSE(res);

  BlockInfo unpersist_bi;
  unpersist_bi.set_uc_state(BlockUCState::kCommitted);
  res = LoadBlockForZeroReplicaEnable(&unpersist_bi, 0);
  ASSERT_FALSE(res);
}

TEST_F(BlockManagerTest, DeleteHint) {
  for (int i = 0; i < 1000; ++i) {
    const int replication = 3;
    ASSERT_TRUE(replication <= DN_UUID.size());
    // 1. create a file with 3 replicas
    CreateRequestProto create_request = MakeCreateRequest();
    create_request.set_replication(replication);
    create_request.set_createparent(true);
    create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
    PermissionStatus permission;
    const std::string file_path = "/User/test/test_file" + std::to_string(i);
    const int file_len = 100;
    CreateResponseProto create_response;
    ASSERT_FALSE(
        ns_->CreateFile(file_path, permission, create_request, &create_response)
            .HasException());

    // 2. add block
    cnetpp::base::IPAddress client_ip(CLIENT_IP);
    auto add_request = MakeAddBlockRequest();
    AddBlockResponseProto add_response;
    ASSERT_FALSE(ns_->AddBlock(file_path,
                               client_ip,
                               default_rpc_info_,
                               add_request,
                               &add_response)
                     .HasException());

    // 3. report three replica
    BlockManager::RepeatedIncBlockReport reports;
    for (int i = 0; i < replication; i++) {
      auto report = reports.Add();
      report->set_storageuuid(STORAGE_UUID);
      auto blk = report->add_blocks();
      blk->mutable_block()->set_blockid(add_response.block().b().blockid());
      blk->mutable_block()->set_genstamp(
          add_response.block().b().generationstamp());
      blk->mutable_block()->set_numbytes(file_len);
      blk->set_status(
          cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
      block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
    }

    // 4. complete file
    CompleteRequestProto complete_request;
    complete_request.set_clientname(CLIENT_NAME);
    complete_request.mutable_last()->set_blockid(
        add_response.block().b().blockid());
    complete_request.mutable_last()->set_generationstamp(
        add_response.block().b().generationstamp());
    complete_request.mutable_last()->set_numbytes(file_len);
    ASSERT_TRUE(!ns_->CompleteFile(file_path, complete_request).HasException());

    // 5. report a RECEIVED
    int gs_delta = 5;
    reports.Clear();
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    blk->mutable_block()->set_numbytes(file_len);
    blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVED);
    // delete hint to dn 0
    blk->set_deletehint(DN_UUID[0]);
    block_manager_->IncrementalBlockReport(DN_UUID[replication], reports);

    // 6. bg work
    block_manager_->ComputeInvalidateBlockWork();

    // 7. dn 0 get cmd
    cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
    block_manager_->GetCommands(
        datanode_manager_->GetDatanodeFromUuid(DN_UUID[0])->id(),
        "bpid",
        0,
        &heartbeat_response);
    // ASSERT_EQ(heartbeat_response.cmds_size(), 1);
    auto cmd = heartbeat_response.cmds(1);
    ASSERT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto::Type::
                  DatanodeCommandProto_Type_BlockCommand);
    ASSERT_EQ(cmd.blkcmd().action(),
              cloudfs::datanode::BlockCommandProto::Action::
                  BlockCommandProto_Action_INVALIDATE);
    ASSERT_EQ(cmd.blkcmd().blocks().size(), 1);
    ASSERT_EQ(cmd.blkcmd().blocks(0).blockid(),
              add_response.block().b().blockid());
    ASSERT_EQ(cmd.blkcmd().blocks(0).genstamp(),
              add_response.block().b().generationstamp());
  }
}

TEST_F(BlockManagerTest, BlockProtocolV2Corrupt) {
  const int replication = 3;
  ASSERT_TRUE(replication <= DN_UUID.size());
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  // use V2
  create_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  // use V2
  add_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  // 3. complete
  CompleteRequestProto complete_request;
  complete_request.set_clientname(CLIENT_NAME);
  complete_request.mutable_last()->set_blockid(
      add_response.block().b().blockid());
  complete_request.mutable_last()->set_generationstamp(
      add_response.block().b().generationstamp());
  complete_request.mutable_last()->set_numbytes(file_len);
  ASSERT_TRUE(!ns_->CompleteFile(file_path, complete_request).HasException());

  // 4. report replica 2 live + 2 corrupt
  BlockManager::RepeatedIncBlockReport reports;
  for (int i = 0; i < 4; i++) {
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    auto block_len = file_len;
    if (i == 1) {
      block_len += 1;
    } else if (i == 2 || i == 3) {
      block_len -= 1;
    }
    blk->mutable_block()->set_numbytes(block_len);
    blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::SEALED);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 5. check corrupt
  auto corrupt_blocks = block_manager_->GetCorruptBlockIDs(0, 10).second;
  ASSERT_EQ(corrupt_blocks.size(), 1);
  ASSERT_EQ(corrupt_blocks.at(0), add_response.block().b().blockid());

  // 6. check if there's a corrupt replica in block info
  uint64_t block_id = add_response.block().b().blockid();
  std::stringstream ss;
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  std::string dumped = ss.str();
  LOG(INFO) << "dumped=" << dumped;
  ASSERT_NE(dumped.find("live=2 corrupt=2"), dumped.npos) << dumped;
}

TEST_F(BlockManagerTest, AddSealedToUc) {
  const int replication = 3;
  ASSERT_TRUE(replication <= DN_UUID.size());
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  // use V2
  create_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  // use V2
  add_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  // 3. report replica 4 sealed
  BlockManager::RepeatedIncBlockReport reports;
  for (int i = 0; i < 4; i++) {
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    blk->mutable_block()->set_numbytes(file_len);
    blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::SEALED);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 5. check uc state
  uint64_t block_id = add_response.block().b().blockid();
  BlockProto bp;
  bp.set_blockid(block_id);
  auto detail_block = block_manager_->GetDetailedBlock(bp);
  ASSERT_EQ(detail_block.storage_.size(), 4);

  // 6. check if there's a corrupt replica in block info
  std::stringstream ss;
  block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
  std::string dumped = ss.str();
  LOG(INFO) << "dumped=" << dumped;
  ASSERT_NE(dumped.find("live=4 corrupt=0"), dumped.npos) << dumped;
}

TEST_F(BlockManagerTest, BlockProtocolV1UcRemoveCorrupt) {
  FLAGS_client_replication_support = true;

  const int replication = 2;
  ASSERT_TRUE(replication <= DN_UUID.size());
  // 1. create a file with 2 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());
  LOG(INFO) << "add_response=" << add_response.ShortDebugString();
  std::set<std::string> add_dn_uuid_list;
  for (const auto& dn : add_response.block().locs()) {
    add_dn_uuid_list.insert(dn.id().datanodeuuid());
    LOG(INFO) << "add_dn_uuid " << dn.id().datanodeuuid();
  }

  // 2.1 check fsck
  {
    uint64_t block_id = add_response.block().b().blockid();
    std::stringstream ss;
    block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
    std::string dumped = ss.str();
    LOG(INFO) << "dumped=" << dumped;
  }

  // 3. report replica 2 live + 2 corrupt
  for (int i = 0; i < 4; i++) {
    BlockManager::RepeatedIncBlockReport reports;
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());

    auto block_gs = add_response.block().b().generationstamp();
    auto block_len = file_len;
    if (add_dn_uuid_list.count(DN_UUID[i]) == 0) {
      datanode_manager_->SetDecommissioning(
          datanode_manager_->GetDatanodeFromUuid(DN_UUID[i])->id());

      block_len -= 1;
      block_gs -= 1;
    }
    blk->mutable_block()->set_genstamp(block_gs);
    blk->mutable_block()->set_numbytes(block_len);
    blk->set_status(
        cloudfs::datanode::ReceivedDeletedBlockInfoProto::RECEIVING);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 4. bg work
  {
    uint64_t block_id = add_response.block().b().blockid();
    std::stringstream ss;
    block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
    std::string dumped = ss.str();
    LOG(INFO) << "dumped=" << dumped;
  }
  block_manager_->ComputeInvalidateBlockWork();

  // 5. check corrupt
  auto corrupt_blocks = block_manager_->GetCorruptBlockIDs(0, 10).second;
  ASSERT_EQ(corrupt_blocks.size(), 1);
  ASSERT_EQ(corrupt_blocks.at(0), add_response.block().b().blockid());

  // 6. check if there's a corrupt replica in block info
  {
    uint64_t block_id = add_response.block().b().blockid();
    std::stringstream ss;
    block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
    std::string dumped = ss.str();
    LOG(INFO) << "dumped=" << dumped;
    ASSERT_NE(dumped.find("live=0 corrupt=2"), dumped.npos) << dumped;
  }

  // 7. dn 2/3 get cmd
  for (int i = 0; i < 4; i++) {
    if (add_dn_uuid_list.count(DN_UUID[i])) {
      continue;
    }

    cloudfs::datanode::HeartbeatResponseProto heartbeat_response;
    block_manager_->GetCommands(
        datanode_manager_->GetDatanodeFromUuid(DN_UUID[i])->id(),
        "bpid",
        0,
        &heartbeat_response);
    ASSERT_GT(heartbeat_response.cmds_size(), 1);
    auto cmd = heartbeat_response.cmds(1);
    ASSERT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto::Type::
                  DatanodeCommandProto_Type_BlockCommand);
    ASSERT_EQ(cmd.blkcmd().action(),
              cloudfs::datanode::BlockCommandProto::Action::
                  BlockCommandProto_Action_INVALIDATE);
    ASSERT_EQ(cmd.blkcmd().blocks().size(), 1);
    ASSERT_EQ(cmd.blkcmd().blocks(0).blockid(),
              add_response.block().b().blockid());
  }
}

TEST_F(BlockManagerTest, BlockProtocolV2UcRemoveCorrupt) {
  const int replication = 2;
  ASSERT_TRUE(replication <= DN_UUID.size());
  // 1. create a file with 3 replicas
  CreateRequestProto create_request = MakeCreateRequest();
  create_request.set_replication(replication);
  create_request.set_createparent(true);
  create_request.set_createflag(cloudfs::CreateFlagProto::CREATE);
  // use V2
  create_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  PermissionStatus permission;
  const std::string file_path = "/User/test/test_file";
  const int file_len = 100;
  CreateResponseProto create_response;
  ASSERT_FALSE(
      ns_->CreateFile(file_path, permission, create_request, &create_response)
          .HasException());

  // 2. add block
  cnetpp::base::IPAddress client_ip(CLIENT_IP);
  auto add_request = MakeAddBlockRequest();
  // use V2
  add_request.set_rpc_type(cloudfs::RPC_BYTERPC_MODE);
  AddBlockResponseProto add_response;
  ASSERT_FALSE(ns_->AddBlock(file_path,
                             client_ip,
                             default_rpc_info_,
                             add_request,
                             &add_response)
                   .HasException());

  // 3. report replica 2 live + 2 corrupt
  BlockManager::RepeatedIncBlockReport reports;
  for (int i = 0; i < 4; i++) {
    auto report = reports.Add();
    report->set_storageuuid(STORAGE_UUID);
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(add_response.block().b().blockid());
    blk->mutable_block()->set_genstamp(
        add_response.block().b().generationstamp());
    auto block_len = file_len;
    if (i == 1) {
      block_len += 1;
    } else if (i == 2 || i == 3) {
      block_len -= 1;
    }
    blk->mutable_block()->set_numbytes(block_len);
    blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::SEALED);
    block_manager_->IncrementalBlockReport(DN_UUID[i], reports);
  }

  // 4. bg work
  {
    uint64_t block_id = add_response.block().b().blockid();
    std::stringstream ss;
    block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
    std::string dumped = ss.str();
    LOG(INFO) << "dumped=" << dumped;
  }

  // 5. trigger bg work
  // in v2, client MUST commit the lowest length in all replicas, UC block will
  // never changed to corrupt block
  block_manager_->ComputeInvalidateBlockWork();

  // 6. check corrupt
  auto corrupt_blocks = block_manager_->GetCorruptBlockIDs(0, 10).second;
  ASSERT_EQ(corrupt_blocks.size(), 0);

  // 7. check if there's a corrupt replica in block info
  {
    uint64_t block_id = add_response.block().b().blockid();
    std::stringstream ss;
    block_manager_->DumpBlockInfo(block_id, nullptr, false, ss);
    std::string dumped = ss.str();
    LOG(INFO) << "dumped=" << dumped;
    ASSERT_NE(dumped.find("live=4 corrupt=0"), dumped.npos) << dumped;
  }
}

} // namespace dancenn
