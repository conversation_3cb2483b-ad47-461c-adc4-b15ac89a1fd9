// Copyright 2021

#include <gtest/gtest.h>
#include <sys/types.h>
#include <unistd.h>
#include <cstdint>
#include <ctime>

#include <utility>
#include <vector>
#include <sstream>
#include <chrono>

#include "base/file_utils.h"
#include "base/constants.h"
#include "block_manager/block_map_slice.h"
#include "datanode_manager/datanode_manager.h"
#include "block_manager/block_manager_metrics.h"
// TODO(ruanjunbin): Delete?
#include "block_manager/block_pufs_info.h"
#include "block_manager/block_info_proto.h"
#include "namespace/meta_storage.h"

DECLARE_int32(blockmap_num_bucket_each_slice);
DECLARE_uint32(dfs_replication_max);

namespace dancenn {

class BlockMapSliceTest : public testing::Test {
 public:
  void SetUp() override {
    InitBlockManager();
  }
  void TearDown() override {}

  void InitSlice(int32_t num_bucket) {
    FLAGS_blockmap_num_bucket_each_slice = num_bucket;
    s_ = std::make_unique<BlockMapSlice>(0, bm_->metrics());
  }
  void InitBlockManager() {
    bm_ = std::make_unique<BlockManager>();
  }

  std::unique_ptr<BlockManager> bm_;
  std::unique_ptr<BlockMapSlice> s_;
};

TEST_F(BlockMapSliceTest, Test01) {
  InitSlice(4096);
  {
    BlockInfoGuard bi_guard(s_.get(), 1234, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
    s_->AddBlock(1,
                0,
                {1234, 256 * 1024, 1243992934},
                3,
                cloudfs::DATANODE_BLOCK,
                {},
                BlockUCState::kComplete);
    ASSERT_EQ(s_->GetUCSize(), 0);
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 1234, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_NE(bi, nullptr);
    ASSERT_EQ(bi->id(), 1234);
    ASSERT_EQ(bi->inode_id(), 1);
    ASSERT_EQ(256 * 1024, bi->num_bytes());
    ASSERT_EQ(1243992934, bi->gs());
    ASSERT_EQ(bi->size(), 0);
    LOG(INFO) << "FLAGS_dfs_replication_max=" << FLAGS_dfs_replication_max;
    LOG(INFO) << "sizeof(BlockInfo)=" << static_cast<int>(sizeof(BlockInfo));
    for (int i = 1; i < FLAGS_dfs_replication_max * 2; i++) {
      bi = s_->AddStorage(bi, i);
      auto ids = s_->GetBlockStorages(bi->id());
      ASSERT_EQ(i, ids.size());
      ASSERT_TRUE(std::find(ids.begin(), ids.end(), 1) != ids.end());
      ASSERT_TRUE(std::find(ids.begin(), ids.end(), i) != ids.end());
    }
    int num_rep = bi->size();
    s_->AddStorage(bi, FLAGS_dfs_replication_max * 2);
    int now_num_rep = bi->size();
    ASSERT_EQ(num_rep, now_num_rep);
    auto ids = s_->GetBlockStorages(bi->id());
    ASSERT_EQ(FLAGS_dfs_replication_max * 2 - 1, ids.size());
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 1235, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 1236, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 1237, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 1238, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
  {
    BlockInfoGuard bi_guard(s_.get(), 12349, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
  {
    s_->RemoveBlock(1234);
    BlockInfoGuard bi_guard(s_.get(), 1234, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
}

TEST_F(BlockMapSliceTest, TestAddBlockAndAddStorage) {
  const BlockID kOperationTimes = 10000;
  InitSlice(4096);
  for (BlockID i = 1; i < kOperationTimes; i++) {
    std::vector<DatanodeID> dns;
    if ((i & 1) == 1) {
      dns.emplace_back(i * 1);
      dns.emplace_back(i * 2);
      dns.emplace_back(i * 3);
    }
    s_->AddBlock(1,
                 0,
                 {i, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 dns,
                 BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_NE(bi, nullptr);
    if ((i & 1) == 0) {
      if ((i & 3) == 0) {
        s_->AddStorage(bi, i * 1);
        s_->AddStorage(bi, i * 2);
        s_->AddStorage(bi, i * 3);
      } else {
        dns.emplace_back(i * 1);
        dns.emplace_back(i * 2);
        dns.emplace_back(i * 3);
        s_->AddStorages(bi, dns);
      }
    }
  }

  {
    BlockInfoGuard bi_guard(s_.get(), 0, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }

  for (BlockID i = 1; i < kOperationTimes; i++) {
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    auto dns = bi->storage_ids();
    ASSERT_EQ(bi->size(), 3);
    ASSERT_EQ((i * 1), dns[0]);
    ASSERT_EQ((i * 2), dns[1]);
    ASSERT_EQ((i * 3), dns[2]);
  }
  for (BlockID i = 1; i < kOperationTimes; ++i) {
    auto dns = s_->GetBlockStorages(i);
    ASSERT_EQ(dns.size(), 3);
    ASSERT_TRUE(std::find(dns.begin(), dns.end(), i * 1) != dns.end());
    ASSERT_TRUE(std::find(dns.begin(), dns.end(), i * 2) != dns.end());
    ASSERT_TRUE(std::find(dns.begin(), dns.end(), i * 3) != dns.end());
  }
}

TEST_F(BlockMapSliceTest, TestLoadBlock) {
  const int kOperationTimes = 10000;
  InitSlice(4096);
  for (uint64_t i = 1; i < kOperationTimes; i++) {
    s_->LoadBlock(1,
                  0,
                  {i, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(1, 2),
                  BlockUCState::kUnderConstruction);
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_NE(bi, nullptr);
  }

  {
    BlockInfoGuard bi_guard(s_.get(), 0, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_EQ(bi, nullptr);
  }
}

TEST_F(BlockMapSliceTest, TestRemoveBlock) {
  InitSlice(4096);
  const BlockID kOperationTimes = 10000;
  for (BlockID i = 0; i < kOperationTimes; ++i) {
    BlockUCState state = BlockUCState::kComplete;
    if ((i % 2) == 0) {
      state = BlockUCState::kUnderConstruction;
    }
    s_->LoadBlock(1,
                  0,
                  {i, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(1, 2),
                  state);
  }
  ASSERT_FALSE(s_->RemoveBlock(kOperationTimes + 1));
  for (BlockID i = 0; i < kOperationTimes; ++i) {
    {
      BlockInfoGuard bi_guard(s_.get(), i, false);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      ASSERT_NE(bi, nullptr);
    }
    if ((i % 2) == 0) {
      ASSERT_NE(s_->GetUcInternal(i), nullptr);
    } else {
      ASSERT_EQ(s_->GetUcInternal(i), nullptr);
    }
    ASSERT_TRUE(s_->RemoveBlock(i));
    {
      BlockInfoGuard bi_guard(s_.get(), i, true);
      BlockInfo* bi = bi_guard.GetBlockInfo();
      ASSERT_EQ(bi, nullptr);
    }
    ASSERT_EQ(s_->GetUcInternal(i), nullptr);
  }
}

TEST_F(BlockMapSliceTest, TestProcessReportedBlock) {
  InitSlice(4096);
  {
    auto res = s_->ProcessReportedBlock(1,
                                        {1, 256 * 1024, 1243992934},
                                        ReplicaStateProto::FINALIZED);
    ASSERT_EQ(res, 2);  // to invalidate
  }
  {
    s_->AddBlock(1,
                 0,
                 {1, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {1, 2, 3},
                 BlockUCState::kComplete);
    auto res = s_->ProcessReportedBlock(4,
                                        {1, 256 * 1023, 1243992934},
                                        ReplicaStateProto::FINALIZED);
    ASSERT_EQ(res, 3);  // corrupt due to num_bytes mismatch
    res = s_->ProcessReportedBlock(4,
                                   {1, 256 * 1024, 1243992933},
                                   ReplicaStateProto::FINALIZED);
    ASSERT_EQ(res, 3);  // corrupt due to genstamp mismatch
    res = s_->ProcessReportedBlock(4,
                                   {1, 256 * 1024, 1243992934},
                                   ReplicaStateProto::FINALIZED);
    ASSERT_EQ(res, 1);  // corrupt due to genstamp mismatch
    auto ucb = s_->GetUcInternal(1);
    ASSERT_EQ(ucb, nullptr);
  }
  {
    s_->AddBlock(1,
                 0,
                 {2, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {1, 2, 3},
                 BlockUCState::kUnderConstruction);
    auto res = s_->ProcessReportedBlock(4,
                                        {2, 256 * 1025, 1243992935},
                                        ReplicaStateProto::FINALIZED);
    ASSERT_EQ(res, 1);
    auto ucb = s_->GetUcInternal(2);
    ASSERT_NE(ucb, nullptr);
    auto locs = ucb->expected_locations();
    ASSERT_EQ(locs.size(), 4);
  }
  {
    s_->AddBlock(1,
                 0,
                 {3, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {1, 2, 3},
                 BlockUCState::kUnderConstruction);
    auto res = s_->ProcessReportedBlock(4,
                                        {3, 256 * 1025, 1243992935},
                                        ReplicaStateProto::RBW);
    ASSERT_EQ(res, 4);
    auto ucb = s_->GetUcInternal(3);
    ASSERT_NE(ucb, nullptr);
    auto locs = ucb->expected_locations();
    ASSERT_EQ(locs.size(), 4);
  }
  {
    s_->AddBlock(1,
                 0,
                 {4, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {1, 2, 3},
                 BlockUCState::kComplete);
    auto res = s_->ProcessReportedBlock(4,
                                        {4, 256 * 1024, 1243992933},
                                        ReplicaStateProto::RBW);
    ASSERT_EQ(res, 3);  // to corrupt
    res = s_->ProcessReportedBlock(4,
                                   {4, 256 * 1024, 1243992934},
                                   ReplicaStateProto::RBW);
    ASSERT_EQ(res, 0);
    res = s_->ProcessReportedBlock(4,
                                   {4, 256 * 1024, 1243992934},
                                   ReplicaStateProto::RBW);
    ASSERT_EQ(res, 0);
  }
}

TEST_F(BlockMapSliceTest, TestCompleteBlock) {
  InitSlice(4096);
  {
    s_->LoadBlock(1,
                  0,
                  {1, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(),
                  BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), 1, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    BlockUCState state = BlockUCState::kUnderConstruction;
    ASSERT_FALSE(s_->CompleteBlock(bi, false, &state));
    ASSERT_TRUE(state == BlockUCState::kComplete);
  }
  {
    s_->LoadBlock(1,
                  0,
                  {2, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(1, 2),
                  BlockUCState::kUnderConstruction);
    BlockInfoGuard bi_guard(s_.get(), 2, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    BlockUCState state = BlockUCState::kComplete;
    ASSERT_FALSE(s_->CompleteBlock(bi, false, &state));
    ASSERT_TRUE(state == BlockUCState::kUnderConstruction);
    ASSERT_TRUE(s_->CompleteBlock(bi, true, &state));
    ASSERT_TRUE(state == BlockUCState::kUnderConstruction);
  }
  {
    s_->LoadBlock(1,
                  0,
                  {3, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(1, 2),
                  BlockUCState::kUnderConstruction);
    BlockInfoGuard bi_guard(s_.get(), 3, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    bi->AddStorage(1);
    BlockUCState state = BlockUCState::kComplete;
    ASSERT_FALSE(s_->CompleteBlock(bi, false, &state));
    ASSERT_TRUE(state == BlockUCState::kUnderConstruction);
    ASSERT_TRUE(s_->CompleteBlock(bi, true, &state));
    ASSERT_TRUE(state == BlockUCState::kUnderConstruction);
  }
  {
    s_->LoadBlock(1,
                  0,
                  {4, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(),
                  BlockUCState::kCommitted);
    BlockInfoGuard bi_guard(s_.get(), 4, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    bi->AddStorage(1);
    BlockUCState state = BlockUCState::kUnderConstruction;
    ASSERT_TRUE(s_->CompleteBlock(bi, false, &state));
    ASSERT_TRUE(state == BlockUCState::kCommitted);
  }
}

TEST_F(BlockMapSliceTest, TestCommitBlock) {
  InitSlice(4096);
  {
    s_->LoadBlock(1,
                  0,
                  {1, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(),
                  BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), 1, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_FALSE(s_->CommitBlock(bi, {1, 256 * 1024, 1243992934}));
  }
  {
    s_->LoadBlock(1,
                  0,
                  {2, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(),
                  BlockUCState::kCommitted);
    BlockInfoGuard bi_guard(s_.get(), 2, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_FALSE(s_->CommitBlock(bi, {2, 256 * 1024, 1243992934}));
  }
  {
    s_->LoadBlock(1,
                  0,
                  {3, 256 * 1024, 1243992934},
                  3,
                  cloudfs::DATANODE_BLOCK,
                  std::vector<DatanodeID>(1, 2),
                  BlockUCState::kUnderConstruction);
    BlockInfoGuard bi_guard(s_.get(), 3, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_TRUE(s_->CommitBlock(bi, {3, 256 * 1025, 1243992934}));
    ASSERT_TRUE(bi->IsCommitted());
    ASSERT_EQ(bi->num_bytes(), 256 * 1025);
  }
}

TEST_F(BlockMapSliceTest, TestMemoryConsumption) {
  const int kOperationTimes = 5000;
  InitSlice(1024);
  for (uint64_t i = 0; i < kOperationTimes; i++) {
    s_->AddBlock(1,
                 0,
                 {i, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {},
                 BlockUCState::kUnderConstruction);
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    ASSERT_NE(bi, nullptr);
    s_->AddStorage(bi, 1);
    s_->AddStorage(bi, 2);
    s_->AddStorage(bi, 3);
  }
  pid_t pid = getpid();
  char command[255];
  snprintf(command, sizeof(command), "/bin/cat /proc/%d/status", pid);
  system(command);
}

TEST_F(BlockMapSliceTest, TestPerf) {
  const int kOperationTimes = 1000;
  InitSlice(262144);
  auto t0 = std::chrono::high_resolution_clock::now();
  for (uint64_t i = 0; i < kOperationTimes; i++) {
    s_->AddBlock(1,
                 0,
                 {i, 256 * 1024, 1243992934},
                 3,
                 cloudfs::DATANODE_BLOCK,
                 {},
                 BlockUCState::kUnderConstruction);
  }
  auto t1 = std::chrono::high_resolution_clock::now();
  double elapsed = std::chrono::duration_cast<std::chrono::nanoseconds>(
      t1 - t0).count() / static_cast<double>(1000000000);
  LOG(INFO) << "AddBlock: " << kOperationTimes / elapsed << " writeop/sec";

  t0 = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < kOperationTimes; i++) {
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
  }
  t1 = std::chrono::high_resolution_clock::now();
  elapsed = std::chrono::duration_cast<std::chrono::nanoseconds>(
      t1 - t0).count() / static_cast<double>(1000000000);
  LOG(INFO) << "Locate: " << kOperationTimes / elapsed << " readop/sec";

  t0 = std::chrono::high_resolution_clock::now();
  for (int i = 0; i < kOperationTimes; i++) {
    BlockInfoGuard bi_guard(s_.get(), i, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    s_->AddStorage(bi, 1);
    s_->AddStorage(bi, 2);
    s_->AddStorage(bi, 3);
  }
  t1 = std::chrono::high_resolution_clock::now();
  elapsed = std::chrono::duration_cast<std::chrono::nanoseconds>(
      t1 - t0).count() / static_cast<double>(1000000000);
  LOG(INFO) << "AddStorage: " << kOperationTimes / elapsed * 3 << " writeop/sec";
}

TEST_F(BlockMapSliceTest, TestProcessRBWBlock) {
  InitSlice(1);
  // Process corrupt block
  s_->AddBlock(1,
               0,
               {1234, 256 * 1024, 1243992934},
               3,
               cloudfs::DATANODE_BLOCK,
               {1, 2},
               BlockUCState::kComplete);
  Block reported_block {1234, 255 * 1024, 1243992933};
  ASSERT_EQ(s_->ProcessReportedBlock(1, reported_block, ReplicaStateProto::RBW), 3);

  // Process rbw block. Note: the rbw block reported should be added to uc and datanode block index
  s_->AddBlock(1,
               0,
               {1235, 256 * 1024, 1243992934},
               3,
               cloudfs::DATANODE_BLOCK,
               {1, 2},
               BlockUCState::kUnderConstruction);
  Block rbw_block_not_in_slice {1235, 255 * 1024, 1243992934};
  ASSERT_EQ(s_->ProcessReportedBlock(1, rbw_block_not_in_slice, ReplicaStateProto::RBW), 4);
}

class BlockMapSliceReleaseBlocksTest : public BlockMapSliceTest {
 public:
  void SetUp() override {
    BlockMapSliceTest::SetUp();
    InitSlice(1);
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_storage_.reset(new MetaStorage(db_path_));
    meta_storage_->Launch();
    meta_storage_->StartStandby();
    meta_storage_->StartActive();
    // meta_storage_->ResetLastAppliedTxId(0);
    s_->SetMetaStorage(meta_storage_);

    for (std::size_t i = 1; i < 8; i++) {
      BlockPufsInfo info;
      // Make sure all blocks_ are in the same bucket.
      info.block_id_ = i * s_->num_bucket();
      info.gen_stamp_ = 1000;
      info.num_bytes_ = 1024;
      info.inode_id_ = 2;
      info.state_ = BlockPufsState::kPersisted;
      BlockInfoProto bip = info.GetBlockInfoProto();
      blocks_.emplace_back(
          bip, Block(info.block_id_, info.num_bytes_, info.gen_stamp_));
    }
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

  uint32_t BlockNumInMem() {
    uint32_t num = 0;
    s_->TraverseAllBlock([&num](BlockInfo** buckets_, size_t num_buckets) {
      for (std::size_t i = 0; i < num_buckets; i++) {
        for (BlockInfo* curr = buckets_[i]; curr != nullptr;
             curr = curr->next()) {
          num++;
        }
      }
      return true;
    });
    return num;
  }

  std::string db_path_ = "rocksdb_XXXXXX";
  std::shared_ptr<MetaStorage> meta_storage_;
  std::vector<std::pair<BlockInfoProto, Block>> blocks_;
};

// We use N for "not expect to release", E for "expect to release".
// N
TEST_F(BlockMapSliceReleaseBlocksTest, Test01) {
  s_->AddBlock(2,
               1,
               blocks_[0].second,
               3,
               cloudfs::DATANODE_BLOCK,
               std::vector<DatanodeID>{},
               BlockUCState::kComplete);
  {
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  s_->SetMetaStorage(nullptr);
  {
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }
}

// E
TEST_F(BlockMapSliceReleaseBlocksTest, Test02) {
  meta_storage_->PutBlockInfo(blocks_[0].first, nullptr, 0, nullptr);
  meta_storage_->TxFinish(1, 1);
  meta_storage_->WaitNoPending(true);
  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(blocks_[0].second.id, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);

  s_->AddBlock(2,
               1,
               blocks_[0].second,
               3,
               cloudfs::DATANODE_BLOCK,
               std::vector<DatanodeID>{},
               BlockUCState::kComplete);
  {
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  s_->SetMetaStorage(nullptr);
  {
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_EQ(bi, nullptr);
  }
}

// NNN
TEST_F(BlockMapSliceReleaseBlocksTest, Test03) {
  for (std::size_t i = 0; i < 3; i++) {
    s_->AddBlock(2,
                 1,
                 blocks_[i].second,
                 3,
                 cloudfs::DATANODE_BLOCK,
                 std::vector<DatanodeID>{},
                 BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  s_->SetMetaStorage(nullptr);
  for (std::size_t i = 0; i < 3; i++) {
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }
}

// EEE
TEST_F(BlockMapSliceReleaseBlocksTest, Test04) {
  for (std::size_t i = 0; i < 3; i++) {
    meta_storage_->PutBlockInfo(blocks_[i].first, nullptr, i, nullptr);
  }
  meta_storage_->TxFinish(1, 3);
  meta_storage_->WaitNoPending(true);
  BlockInfoProto bip;
  for (std::size_t i = 0; i < 3; i++) {
    EXPECT_TRUE(meta_storage_->GetBlockInfo(blocks_[i].second.id, &bip));
    EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);
    s_->AddBlock(2,
                 1,
                 blocks_[i].second,
                 3,
                 cloudfs::DATANODE_BLOCK,
                 std::vector<DatanodeID>{},
                 BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  s_->SetMetaStorage(nullptr);
  for (std::size_t i = 0; i < 3; i++) {
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_EQ(bi, nullptr);
  }
}

// ENENE
TEST_F(BlockMapSliceReleaseBlocksTest, Test05) {
  meta_storage_->PutBlockInfo(blocks_[0].first, nullptr, 0, nullptr);
  meta_storage_->PutBlockInfo(blocks_[2].first, nullptr, 1, nullptr);
  meta_storage_->PutBlockInfo(blocks_[4].first, nullptr, 2, nullptr);
  meta_storage_->TxFinish(1, 3);
  meta_storage_->WaitNoPending(true);
  BlockInfoProto bip;
  EXPECT_TRUE(meta_storage_->GetBlockInfo(blocks_[0].second.id, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);
  EXPECT_TRUE(meta_storage_->GetBlockInfo(blocks_[2].second.id, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);
  EXPECT_TRUE(meta_storage_->GetBlockInfo(blocks_[4].second.id, &bip));
  EXPECT_EQ(bip.state(), BlockInfoProto::kPersisted);

  for (std::size_t i = 0; i < 5; i++) {
    s_->AddBlock(2,
                 1,
                 blocks_[i].second,
                 3,
                 cloudfs::DATANODE_BLOCK,
                 std::vector<DatanodeID>{},
                 BlockUCState::kComplete);
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  s_->SetMetaStorage(nullptr);
  for (std::size_t i = 0; i < 5; i++) {
    BlockInfoGuard bi_guard(s_.get(), blocks_[i].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    if (i % 2 == 0) {
      EXPECT_EQ(bi, nullptr);
    } else {
      EXPECT_NE(bi, nullptr);
    }
  }
}

TEST_F(BlockMapSliceReleaseBlocksTest, Test06) {
  meta_storage_->PutBlockInfo(blocks_[0].first, nullptr, 0, nullptr);
  meta_storage_->TxFinish(1, 3);
  meta_storage_->WaitNoPending(true);
  s_->AddBlock(2,
               1,
               blocks_[0].second,
               3,
               cloudfs::DATANODE_BLOCK,
               std::vector<DatanodeID>{},
               BlockUCState::kPersisted);
  {
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
  }
  EXPECT_EQ(BlockNumInMem(), 1);
  s_->ReleasePersistedAndCacheFreeBlocks();
  EXPECT_EQ(BlockNumInMem(), 0);

  {
    // Block should not be loaded to memory
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
    EXPECT_EQ(BlockNumInMem(), 0);
    EXPECT_EQ(bi->id(), blocks_[0].second.id);
  }

  {
    // Block should be loaded to memory
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
    EXPECT_EQ(BlockNumInMem(), 1);
    EXPECT_EQ(bi->id(), blocks_[0].second.id);
  }

  s_->ReleasePersistedAndCacheFreeBlocks();
  EXPECT_EQ(BlockNumInMem(), 0);

  {
    // Block should be loaded to memory
    BlockInfoGuard bi_guard(s_.get(), blocks_[0].second.id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_NE(bi, nullptr);
    EXPECT_EQ(BlockNumInMem(), 1);
    EXPECT_EQ(bi->id(), blocks_[0].second.id);
  }
}

}  // namespace dancenn
