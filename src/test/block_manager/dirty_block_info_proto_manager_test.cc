// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/02/16
// Description

#include <absl/strings/str_format.h>         // For StrFormat.
#include <absl/types/optional.h>             // For optional.
#include <cnetpp/base/ip_address.h>          // For IPAddress.
#include <gflags/gflags.h>                   // For DECLARE_int32.
#include <google/protobuf/repeated_field.h>  // For RepeatedPtrField.
#include <gtest/gtest.h>                     // For TEST.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For UploadCommandProto, NotifyEvictableCommandProto, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For IoMode, ReplicaStateProto, DatanodeIDProto, etc.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.
#include <proto/generated/dancenn/inode.pb.h>        // For UfsFileCreateType.
#include <proto/generated/dancenn/status_code.pb.h>  // For StatusCode.

#include <chrono>         // For chrono.
#include <cstdint>        // For int32_t.
#include <memory>         // For shared_ptr, unique_ptr.
#include <set>            // For set.
#include <string>         // For string.
#include <thread>         // For this_thread.
#include <unordered_set>  // For unordered_set.
#include <utility>        // For move.
#include <vector>         // For vector.

#include "base/java_exceptions.h"             // For JavaExceptions.
#include "base/status.h"                      // For Code, Status.
#include "base/time_util.h"                   // For TimeUtilV2.
#include "block_manager/bip_write_manager.h"  // For BLockComponents, etc.
#include "block_manager/block.h"              // For BlockID, kInvalidBlockID.
#include "block_manager/block_info.h"         // For BlockUCState.
#include "datanode_manager/datanode_info.h"  // For DatanodeInfo, DatanodeInfoPtr.
#include "datanode_manager/datanode_manager.h"  // For DatanodeManager.
#include "namespace/meta_storage.h"             // For MetaStorage.
#include "test/base/gmock_time_util.h"          // For GMockTimeUtilV2.
#include "test/datanode_manager/gmock_datanode_manager.h"  // For GMockDatanodeManager.
#include "test/block_manager/gmock_block_manager.h"  // For GMockBlockManager.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage.

using namespace testing;  // NOLINT(build/namespaces)

DECLARE_int32(block_report_hard_limit_ms);
DECLARE_int32(min_upload_timeout_s);
DECLARE_int32(max_upload_timeout_s);
DECLARE_int32(nn_dn_clock_drift_s);
DECLARE_int32(namespace_type);

namespace dancenn {

TEST(ReplicaNumV2Test, HappyCase) {
  GMockTimeUtilV2 time_util;
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
      [&time_util]() { return time_util.GetNowEpochMs(); });
  EXPECT_CALL(time_util, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  GMockDatanodeManager datanode_manager;
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  // stale: dn_uuid is empty.
  ReplicaInfoProto* replica = bip.add_replicas();
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid(""))
      .Times(1)
      .WillOnce(Return(nullptr));
  // invalidating
  replica = bip.add_replicas();
  replica->set_invalidate_ts(**********);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  DatanodeInfo dn_1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn_1));
  // stale: dn is not alive.
  replica = bip.add_replicas();
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-2");
  std::this_thread::sleep_for(std::chrono::milliseconds(1));
  DatanodeInfo dn_2(2, DatanodeIDProto(), cnetpp::base::IPAddress());
  dn_2.MarkDead(nullptr, std::chrono::milliseconds(0));
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-2"))
      .Times(1)
      .WillOnce(Return(&dn_2));
  // stale: should not follow fbr timestamp && timeout
  replica = bip.add_replicas();
  replica->set_report_ts(********** - FLAGS_block_report_hard_limit_ms);
  replica->set_reporter(ReplicaInfoProto::kClient);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-3");
  DatanodeInfo dn_3(3, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-3"))
      .Times(1)
      .WillOnce(Return(&dn_3));
  // healthy: should follow fbr timestamp
  replica = bip.add_replicas();
  replica->set_report_ts(********** - FLAGS_block_report_hard_limit_ms);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-4");
  DatanodeIDProto dn_id_4;
  dn_id_4.set_datanodeuuid("datanode-4");
  DatanodeInfo dn_4(4, dn_id_4, cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-4"))
      .Times(1)
      .WillOnce(Return(&dn_4));
  // healthy: not timeout
  replica = bip.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kClient);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-5");
  DatanodeInfo dn_5(5, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-5"))
      .Times(1)
      .WillOnce(Return(&dn_5));
  // replicating
  replica = bip.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kNamenode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-6");
  DatanodeInfo dn_6(6, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-6"))
      .Times(1)
      .WillOnce(Return(&dn_6));
  // decommission
  replica = bip.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-7");
  DatanodeInfo dn_7(7, DatanodeIDProto(), cnetpp::base::IPAddress());
  dn_7.SetDecommissioning();
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-7"))
      .Times(1)
      .WillOnce(Return(&dn_7));
  // corrupt
  replica = bip.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::RBW);
  replica->set_gen_stamp(1000);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-8");
  DatanodeInfo dn_8(8, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-8"))
      .Times(1)
      .WillOnce(Return(&dn_8));
  std::unordered_set<DatanodeInfoPtr> containing_dns;
  std::unordered_set<DatanodeInfoPtr> decommission_dns;
  std::vector<ReplicaInfoProto*> healthy_replicas;
  std::unordered_set<DatanodeInfoPtr> healthy_dns;
  ReplicaNumV2 r = ReplicaNumV2::CountReplica(&datanode_manager,
                                              &bip,
                                              &containing_dns,
                                              &decommission_dns,
                                              &healthy_replicas,
                                              &healthy_dns);
  EXPECT_EQ(r.invalidating, 1);
  EXPECT_EQ(r.corrupt, 1);
  EXPECT_EQ(r.stale, 3);
  EXPECT_EQ(r.replicating, 1);
  EXPECT_EQ(r.decommission, 1);
  EXPECT_EQ(r.healthy, 2);
  EXPECT_EQ(containing_dns,
            (std::unordered_set<DatanodeInfoPtr>{
                &dn_1, &dn_2, &dn_3, &dn_4, &dn_5, &dn_6, &dn_7, &dn_8}));
  EXPECT_EQ(decommission_dns, (std::unordered_set<DatanodeInfoPtr>{&dn_7}));
  EXPECT_EQ(healthy_replicas.size(), 2);
  EXPECT_EQ(healthy_dns, (std::unordered_set<DatanodeInfoPtr>{&dn_4, &dn_5}));
}

TEST(ReplicaNumV2Test, WithDeprecatedBlock) {
  TimeUtilV2::TestOnlySetGetNowEpochMsFunc([]() { return 0; });
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kDeprecated);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  ReplicaInfoProto* replica = bip.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  GMockDatanodeManager datanode_manager;
  DatanodeInfo dn_1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(datanode_manager, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn_1));
  ReplicaNumV2 r = ReplicaNumV2::CountReplica(
      &datanode_manager, &bip, nullptr, nullptr, nullptr, nullptr);
  EXPECT_EQ(r.invalidating, 0);
  EXPECT_EQ(r.corrupt, 0);
  EXPECT_EQ(r.stale, 0);
  EXPECT_EQ(r.replicating, 0);
  EXPECT_EQ(r.decommission, 0);
  EXPECT_EQ(r.healthy, 1);
}

TEST(BUniqueLockTest, LockAndUnlock) {
  DirtyBlockInfoProto dbip(1);
  {
    dbip.PreLock();
    BUniqueLock<DirtyBlockInfoProto> lock(&dbip, {__FILE__, __LINE__, 1});
    EXPECT_TRUE(dbip.TestOnlyIsLocked());
  }
  EXPECT_FALSE(dbip.TestOnlyIsLocked());
  // move constructor
  dbip.PreLock();
  BUniqueLock<DirtyBlockInfoProto> lock(&dbip, {__FILE__, __LINE__, 1});
  {
    BUniqueLock<DirtyBlockInfoProto> lock2(std::move(lock));
    EXPECT_TRUE(dbip.TestOnlyIsLocked());
  }
  EXPECT_FALSE(dbip.TestOnlyIsLocked());
  // move assignment
  dbip.PreLock();
  lock = BUniqueLock<DirtyBlockInfoProto>(&dbip, {__FILE__, __LINE__, 1});
  EXPECT_TRUE(dbip.TestOnlyIsLocked());
  lock = std::move(lock);
  EXPECT_TRUE(dbip.TestOnlyIsLocked());
  lock = BUniqueLock<DirtyBlockInfoProto>();
  EXPECT_FALSE(dbip.TestOnlyIsLocked());
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, 1});
  dbip.ReleaseLock()->unlock();
}

TEST(BLockComponentsTest, MoveConstructor) {
  // empty <- empty
  BIPLockComponents c1;
  BIPLockComponents c2(std::move(c1));
  EXPECT_EQ(c1.TestOnlySize(), 0);
  EXPECT_EQ(c2.TestOnlySize(), 0);
  // empty <- non_empty
  BIPLockComponents c3;
  c3.Add(1,
         reinterpret_cast<DirtyBlockInfoProto*>(0x1),
         BUniqueLock<DirtyBlockInfoProto>());
  BIPLockComponents c4(std::move(c3));
  EXPECT_EQ(c3.TestOnlySize(), 0);
  EXPECT_EQ(c4.TestOnlySize(), 1);
}

TEST(BLockComponentsTest, MoveAssignment) {
  // empty <- empty
  BIPLockComponents c1;
  c1 = BIPLockComponents();
  EXPECT_EQ(c1.TestOnlySize(), 0);
  // non_empty <- empty
  c1.Add(1,
         reinterpret_cast<DirtyBlockInfoProto*>(0x1),
         BUniqueLock<DirtyBlockInfoProto>());
  c1 = BIPLockComponents();
  EXPECT_EQ(c1.TestOnlySize(), 0);
  // empty <- non_empty
  BIPLockComponents c2;
  c2.Add(1,
         reinterpret_cast<DirtyBlockInfoProto*>(0x1),
         BUniqueLock<DirtyBlockInfoProto>());
  c1 = std::move(c2);
  EXPECT_EQ(c1.TestOnlySize(), 1);
  EXPECT_EQ(c2.TestOnlySize(), 0);
  // non_empty <- non_empty
  c2.Add(1,
         reinterpret_cast<DirtyBlockInfoProto*>(0x1),
         BUniqueLock<DirtyBlockInfoProto>());
  c2.Add(2,
         reinterpret_cast<DirtyBlockInfoProto*>(0x2),
         BUniqueLock<DirtyBlockInfoProto>());
  c1 = std::move(c2);
  EXPECT_EQ(c1.TestOnlySize(), 2);
  EXPECT_EQ(c2.TestOnlySize(), 0);

  c1 = []() {
    BIPLockComponents c3;
    c3.Add(1,
           reinterpret_cast<DirtyBlockInfoProto*>(0x1),
           BUniqueLock<DirtyBlockInfoProto>());
    return std::move(c3);
  }();
  EXPECT_EQ(c1.TestOnlySize(), 1);
}

TEST(DirtyBlockInfoProtoTest, LoadAndGet) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  EXPECT_FALSE(dbip.IsInitialized());
  EXPECT_EQ(dbip.Get().block_id(), **********);

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  EXPECT_TRUE(dbip.IsInitialized());
  EXPECT_EQ(dbip.ToString(),
            "bip_:{\"aborted_upload_ids\":[],\"block_id\":**********,"
            "\"deleted_dn_uuids\":[],\"expected_rep\":3,\"gen_stamp\":1001,"
            "\"inode_id\":17390,\"num_bytes\":1024,"
            "\"replicas\":[],\"state\":0},"
            "unstarted_tx_num_:0,"
            "ongoing_tx_num_:0");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, TryLock4GC) {
  DirtyBlockInfoProto dbip(1);
  EXPECT_TRUE(dbip.TryLock4GC({__FILE__, __LINE__, 1}));
  EXPECT_FALSE(dbip.TryLock4GC({__FILE__, __LINE__, 1}));
  dbip.Unlock();
  EXPECT_TRUE(dbip.TryLock4GC({__FILE__, __LINE__, 1}));
  dbip.Unlock();
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, 1});
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, WriteAndCallback) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, 0});
  BlockInfoProto r;
  Status s = dbip.PreCommit(&r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to write uninitialized B**********");

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  s = dbip.PreCommit(&r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to write B********** with unstarted_tx_num_: 0");

  dbip.IncVersion();
  s = dbip.PreCommit(&r);
  EXPECT_TRUE(s.IsOK());
  bip.set_version(r.version());
  EXPECT_EQ(r.SerializeAsString(), bip.SerializeAsString());
  EXPECT_FALSE(dbip.IsFlushed());

  s = dbip.PostCommit(r);
  EXPECT_TRUE(s.IsOK());
  EXPECT_TRUE(dbip.IsFlushed());

  s = dbip.PostCommit(r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to callback B********** with ongoing_tx_num_: 0");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, IsEqualToWithCompleteBlock) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  Status s = dbip.IsEqualTo(Block(**********, 1024, 1001), "datanode-1");
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare non-initialized B********** "
            "with wrong replica{id:**********,gs:1001,num_bytes:1024}");

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  s = dbip.IsEqualTo(Block(**********, 1024, 1001), "datanode-1");
  EXPECT_TRUE(s.IsOK());

  s = dbip.IsEqualTo(Block(**********, 1025, 1002), "datanode-1");
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare block "
            "{id:**********,gs:1001,num_bytes:1024} with "
            "wrong replica{id:**********,gs:1002,num_bytes:1025}");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, IsEqualToWithUnderConstructionBlock) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(0);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  Status s = dbip.IsEqualTo(Block(**********, 1024, 1001), "datanode-1");
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Block is not committed");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, IsEqualToWithUnderRecoveryBlock) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderRecovery);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(0);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  Status s = dbip.IsEqualTo(Block(**********, 1024, 1001), "datanode-1");
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Block is not committed");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, GetUploadCommandInHdfsMode) {
  int32_t namespace_type = FLAGS_namespace_type;
  FLAGS_namespace_type =
      static_cast<int32_t>(cloudfs::NamespaceType::TOS_MANAGED);
  DEFER([&]() { namespace_type = FLAGS_namespace_type; });

  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  cloudfs::datanode::UploadCommandProto upload_cmd;
  Status s = dbip.GetUploadCommand("bp-1", &upload_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to GetUploadCommand from uninitialized B**********");

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  s = dbip.GetUploadCommand("bp-1", &upload_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to GetUploadCommand from not-upload-issued "
            "B{id:**********,gs:1001,num_bytes:1024,state:0}");

  dbip.Get().set_state(BlockInfoProto::kUploadIssued);
  dbip.Get().set_pufs_name("18014398509482046/block/**********.block");
  dbip.Get().set_dn_exp_ts(1676891975);
  dbip.Get().set_curr_upload_id("upload-id-3");
  dbip.Get().set_dn_uuid("datanode-1");
  dbip.Get().add_aborted_upload_ids("upload-id-1");
  dbip.Get().add_aborted_upload_ids("upload-id-2");
  s = dbip.GetUploadCommand("bp-1", &upload_cmd);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), 1024);
  EXPECT_EQ(upload_cmd.blockpufsname(),
            "18014398509482046/block/**********.block");
  EXPECT_EQ(upload_cmd.expts(), 1676891975);
  EXPECT_EQ(upload_cmd.uploadid(), "upload-id-3");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 2);
  EXPECT_EQ(upload_cmd.aborteduploadids(0), "upload-id-1");
  EXPECT_EQ(upload_cmd.aborteduploadids(1), "upload-id-2");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest,
     GetUploadCommandInAccModeOfUnderConstructionBlockFailed) {
  int32_t namespace_type = FLAGS_namespace_type;
  FLAGS_namespace_type = static_cast<int32_t>(cloudfs::NamespaceType::ACC_TOS);
  DEFER([&]() { namespace_type = FLAGS_namespace_type; });

  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  Status s = dbip.GetUploadCommand("bp-1", &upload_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to GetUploadCommand from not-upload-issued "
            "B{id:**********,gs:1001,num_bytes:1024,state:1}");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, GetUploadCommandInAccModeWithAbortedUploadIds) {
  int32_t namespace_type = FLAGS_namespace_type;
  FLAGS_namespace_type = static_cast<int32_t>(cloudfs::NamespaceType::ACC_TOS);
  DEFER([&]() { namespace_type = FLAGS_namespace_type; });

  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kPersisted);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  bip.add_aborted_upload_ids("upload-id-1");
  bip.add_aborted_upload_ids("upload-id-2");
  dbip.Load(bip);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  Status s = dbip.GetUploadCommand("bp-1", &upload_cmd);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 2);
  EXPECT_EQ(upload_cmd.aborteduploadids(0), "upload-id-1");
  EXPECT_EQ(upload_cmd.aborteduploadids(1), "upload-id-2");
  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoTest, GetNotifyEvictableCommand) {
  DirtyBlockInfoProto dbip(**********);
  dbip.PreLock();
  dbip.Lock({__FILE__, __LINE__, **********});
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = dbip.GetNotifyEvictableCommand("bp-1", &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to GetNECommand from uninitialized B**********");

  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  dbip.Load(bip);
  s = dbip.GetNotifyEvictableCommand("bp-1", &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to GetNECommand from not-persisted "
            "B{id:**********,gs:1001,num_bytes:1024,state:0}");

  dbip.Get().set_state(BlockInfoProto::kPersisted);
  s = dbip.GetNotifyEvictableCommand("bp-1", &ne_cmd);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);

  dbip.Get().set_state(BlockInfoProto::kDeprecated);
  s = dbip.GetNotifyEvictableCommand("bp-1", &ne_cmd);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);

  dbip.Get().set_state(BlockInfoProto::kDeleted);
  s = dbip.GetNotifyEvictableCommand("bp-1", &ne_cmd);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);

  dbip.ReleaseLock()->unlock();
}

TEST(DirtyBlockInfoProtoBucketTest, GetOrCreate) {
  DirtyBlockInfoProtoBucket bkt;
  bkt.Lock(BDebugMsg());
  DirtyBlockInfoProto* b1 = bkt.GetOrCreate(**********);
  EXPECT_NE(b1, nullptr);
  DirtyBlockInfoProto* b2 = bkt.GetOrCreate(**********);
  EXPECT_EQ(b2, b1);
  DirtyBlockInfoProto* b3 = bkt.GetOrCreate(1076015590);
  EXPECT_NE(b3, nullptr);
  EXPECT_NE(b3, b1);
  bkt.Unlock();
}

class DirtyBlockInfoProtoManagerTest : public Test {
 public:
  void SetUp() override {
    meta_storage_ = new GMockMetaStorage();
    datanode_manager_ = new GMockDatanodeManager();
    bip_write_manager_.reset(new BIPWriteManager(
        "bp-1",
        std::shared_ptr<MetaStorage>(meta_storage_),
        std::shared_ptr<DatanodeManager>(datanode_manager_)));
    time_util_.reset(new GMockTimeUtilV2);
    TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
        [this]() { return time_util_->GetNowEpochMs(); });
  }

 protected:
  GMockMetaStorage* meta_storage_;
  GMockDatanodeManager* datanode_manager_;
  GMockBlockManager* block_manager_;
  std::unique_ptr<BIPWriteManager> bip_write_manager_;
  std::unique_ptr<GMockTimeUtilV2> time_util_;
};

TEST_F(DirtyBlockInfoProtoManagerTest, Lock) {
  BIPLockComponents blk_lck_comps;
  {
    blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{0, kInvalidBlockID, 1, 2, 3}, __FILE__, __LINE__);
  }
  EXPECT_EQ(blk_lck_comps.TestOnlySize(), 3);
  EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(1).TestOnlyIsLocked());
  EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(2).TestOnlyIsLocked());
  EXPECT_TRUE(bip_write_manager_->TestOnlyGetBlock(3).TestOnlyIsLocked());
  blk_lck_comps = BIPLockComponents();
  EXPECT_FALSE(bip_write_manager_->TestOnlyGetBlock(1).TestOnlyIsLocked());
  EXPECT_FALSE(bip_write_manager_->TestOnlyGetBlock(2).TestOnlyIsLocked());
  EXPECT_FALSE(bip_write_manager_->TestOnlyGetBlock(3).TestOnlyIsLocked());
}

TEST_F(DirtyBlockInfoProtoManagerTest,
       GetOrLoadDirtyBlockInfoProtoIfMissingOfInvalidBlkID) {
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{kInvalidBlockID}, __FILE__, __LINE__);
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = bip_write_manager_->TestOnlyGetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, kInvalidBlockID, false, &dbip);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
}

TEST_F(DirtyBlockInfoProtoManagerTest,
       GetOrLoadDirtyBlockInfoProtoIfMissingUsingWrongBlkLckComps) {
  auto blk_lck_comps =
      bip_write_manager_->Lock(std::set<BlockID>(), __FILE__, __LINE__);
  DirtyBlockInfoProto* dbip = nullptr;
  Status s = bip_write_manager_->TestOnlyGetOrLoadDirtyBlockInfoProtoIfMissing(
      blk_lck_comps, **********, false, &dbip);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "********** not found in blk_lck_comps");
}

TEST_F(DirtyBlockInfoProtoManagerTest, AddBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  INode inode;
  inode.set_id(17390);
  inode.set_replication(2);
  bip_write_manager_->AddBlock(blk_lck_comps,
                               Block(**********, 1024, 1001),
                               inode,
                               cloudfs::IoMode::DATANODE_BLOCK,
                               "18014398509482046/block/**********.block",
                               {"datanode-1", "datanode-2"});
  BlockInfoProto bip;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &bip).IsOK());
  EXPECT_EQ(bip.version(), **********000);
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), **********);
  EXPECT_EQ(bip.gen_stamp(), 1001);
  EXPECT_EQ(bip.num_bytes(), 1024);
  EXPECT_EQ(bip.inode_id(), 17390);
  EXPECT_EQ(bip.expected_rep(), 2);
  EXPECT_EQ(bip.pufs_name(), "18014398509482046/block/**********.block");
  EXPECT_EQ(bip.replicas_size(), 2);
  EXPECT_GT(bip.replicas(0).report_ts(), 0);
  EXPECT_EQ(bip.replicas(0).reporter(), ReplicaInfoProto::kNamenode);
  EXPECT_EQ(bip.replicas(0).state(), cloudfs::ReplicaStateProto::TEMPORARY);
  EXPECT_EQ(bip.replicas(0).gen_stamp(), 1001);
  EXPECT_EQ(bip.replicas(0).num_bytes(), 0);
  EXPECT_EQ(bip.replicas(0).dn_uuid(), "datanode-1");
  EXPECT_EQ(bip.replicas(1).report_ts(), **********);
  EXPECT_EQ(bip.replicas(1).reporter(), ReplicaInfoProto::kNamenode);
  EXPECT_EQ(bip.replicas(1).state(), cloudfs::ReplicaStateProto::TEMPORARY);
  EXPECT_EQ(bip.replicas(1).gen_stamp(), 1001);
  EXPECT_EQ(bip.replicas(1).num_bytes(), 0);
  EXPECT_EQ(bip.replicas(1).dn_uuid(), "datanode-2");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, bip).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerTest, WriteAndCallbackNotExistedBlock) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(2)
      .WillRepeatedly(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  BlockInfoProto bip;
  Status s = bip_write_manager_->PreCommit(blk_lck_comps, **********, &bip);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when PreCommit");
  EXPECT_FALSE(bip.IsInitialized());

  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  s = bip_write_manager_->PostCommit(blk_lck_comps, bip);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when PostCommit");
}

class DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();

    penult_bip_.set_state(BlockInfoProto::kCommitted);
    penult_bip_.set_block_id(**********);
    penult_bip_.set_gen_stamp(1001);
    penult_bip_.set_num_bytes(1024);
    penult_bip_.set_inode_id(17390);
    penult_bip_.set_expected_rep(3);

    last_bip_.set_state(BlockInfoProto::kUnderConstruction);
    last_bip_.set_block_id(1076015590);
    last_bip_.set_gen_stamp(1012);
    last_bip_.set_num_bytes(0);
    last_bip_.set_inode_id(17390);
    last_bip_.set_expected_rep(2);
  }

 protected:
  BlockInfoProto penult_bip_;
  BlockInfoProto last_bip_;
};

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       HappyCaseOfOneBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  ReplicaInfoProto* replica = penult_bip_.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  DatanodeInfo dn_1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn_1));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(
      bip_write_manager_
          ->CompletePenultBlkAndCommitLastBlk(blk_lck_comps,
                                              "/test",
                                              INode(),
                                              Block(0, 0, 0),
                                              Block(**********, 1024, 1001),
                                              Block(**********, 0, 1001),
                                              false,
                                              absl::optional<BlockUCState>(),
                                              absl::optional<BlockUCState>())
          .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       HappyCaseOfTwoBlocks) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  ReplicaInfoProto* replica = penult_bip_.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  DatanodeInfo dn_1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn_1));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  EXPECT_TRUE(
      bip_write_manager_
          ->CompletePenultBlkAndCommitLastBlk(blk_lck_comps,
                                              "/test",
                                              INode(),
                                              Block(**********, 1024, 1001),
                                              Block(1076015590, 2048, 1012),
                                              Block(1076015590, 0, 1012),
                                              false,
                                              absl::optional<BlockUCState>(),
                                              absl::optional<BlockUCState>())
          .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.num_bytes(), 2048);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       ForceSetPenultBlkStateIsCommittedWhenRealStateIsUploadIssued) {
  // Refer to HappyCaseOfTwoBlocks.
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  penult_bip_.set_state(BlockInfoProto::kUploadIssued);  //
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  EXPECT_TRUE(
      bip_write_manager_
          ->CompletePenultBlkAndCommitLastBlk(
              blk_lck_comps,
              "/test",
              INode(),
              Block(**********, 1024, 1001),
              Block(1076015590, 2048, 1012),
              Block(1076015590, 0, 1012),
              false,
              absl::optional<BlockUCState>(BlockUCState::kCommitted),  //
              absl::optional<BlockUCState>())
          .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);  //
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.num_bytes(), 2048);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       PenultBlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015581}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015581, 1025, 1002),
      Block(1076015581, 0, 1002),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Found missing penult B********** when "
            "CompletePenultBlkAndCommitLastBlk");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       LastBlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015581, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015581}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(0, 0, 0),
      Block(1076015581, 1025, 1002),
      Block(1076015581, 0, 1002),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Found missing last B1076015581 when "
            "CompletePenultBlkAndCommitLastBlk");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       PenultBlkIsUnderConstruction) {
  penult_bip_.set_state(BlockInfoProto::kUnderConstruction);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to complete B********** with inconsistent state 1");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       PenultBlkDoesntHaveEnougReplicas) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kNotReplicatedYetException);
  EXPECT_EQ(s.message(),
            "Not replicated yet: /test, "
            "B********** does not have enough replicas");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       ForceSetPenultBlkStateIsIllegal) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  ReplicaInfoProto* replica = penult_bip_.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  DatanodeInfo dn_1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn_1));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      false,
      absl::optional<BlockUCState>(BlockUCState::kPersisted),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "State of penult_bip_c is not equal to force_set_penult_blk_state, "
            "penult_bip_c:{id:**********,gs:1001,num_bytes:1024,state:0}, "
            "force_set_penult_blk_state:4");
}

// Almost copy from
// DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest.HappyCaseOfTwoBlocks.
TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       ForceSetPenultBlkStateIsCompleteAndPenultBlkIsPersisted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  // Minor change 1
  penult_bip_.set_state(BlockInfoProto::kPersisted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->CompletePenultBlkAndCommitLastBlk(
                      blk_lck_comps,
                      "/test",
                      INode(),
                      Block(**********, 1024, 1001),
                      Block(1076015590, 2048, 1012),
                      Block(1076015590, 0, 1012),
                      false,
                      // Minor change 2
                      absl::optional<BlockUCState>(BlockUCState::kComplete),
                      absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.num_bytes(), 2048);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       LastBlkIsUnderRecovery) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  last_bip_.set_state(BlockInfoProto::kUnderRecovery);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      false,
      absl::optional<BlockUCState>(BlockUCState::kComplete),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Client commit under recovery block: "
            "{id:1076015590,gs:1012,num_bytes:0,recovery_gen_stamp:0}");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       GsOrLenOfLastBlkIsIllegal) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      /*last_blk_from_cli=*/Block(1076015590, 2048, 1012),
      /*last_blk_from_inode=*/Block(1076015590, 2049, 1013),
      false,
      absl::optional<BlockUCState>(BlockUCState::kComplete),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to commit corrupt last block, "
            "last_blk_from_cli: {id:1076015590,gs:1012,len:2048}, "
            "last_bp: {id:1076015590,gs:1013,len:2049}");

  s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      /*last_blk_from_cli=*/Block(1076015590, 2048, 1012),
      /*last_blk_from_inode=*/Block(1076015590, 1, 1012),
      false,
      absl::optional<BlockUCState>(BlockUCState::kComplete),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to commit inconsistent block B1076015590, "
            "last_bip_c: {id:1076015590,gs:1012,num_bytes:0}, "
            "last_blk_from_inode: {id:1076015590,gs:1012,num_bytes:1}");

  s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      /*last_blk_from_cli=*/Block(1076015590, 4096, 1013),
      /*last_blk_from_inode=*/Block(1076015590, 2048, 1013),
      false,
      absl::optional<BlockUCState>(BlockUCState::kComplete),
      absl::optional<BlockUCState>());
  EXPECT_TRUE(s.IsOK());
  BlockInfoProto bip;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &bip).IsOK());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, bip).IsOK());
  bip.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &bip).IsOK());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, bip).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       LastBlkDoesntHaveEnougReplicas) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(**********, 1024, 1001),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      true,
      absl::optional<BlockUCState>(BlockUCState::kComplete),
      absl::optional<BlockUCState>());
  EXPECT_TRUE(s.IsFalse());
  EXPECT_EQ(s.message(),
            "Try to complete last block without enough replica, "
            "blk_from_cli: {id:1076015590,gs:1012,num_bytes:2048}");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       ForceSetLastBlkStateIsIllegal) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(0, 0, 0),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 0, 1012),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>(BlockUCState::kPersisted));
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "State of last_bip_c is not equal to force_set_last_blk_state, "
            "last_bip_c:{id:1076015590,gs:1012,num_bytes:2048,state:3}, "
            "force_set_last_blk_state:4");
}

TEST_F(DirtyBlockInfoProtoManagerCompletePenultBlkAndCommitLastBlkTest,
       LastBlkIsDeprecated) {
  last_bip_.set_state(BlockInfoProto::kDeprecated);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CompletePenultBlkAndCommitLastBlk(
      blk_lck_comps,
      "/test",
      INode(),
      Block(0, 0, 0),
      Block(1076015590, 2048, 1012),
      Block(1076015590, 2048, 1012),
      false,
      absl::optional<BlockUCState>(),
      absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to commit last block B1076015590 with inconsistent state 6");
}

class DirtyBlockInfoProtoManagerUpdatePipelineTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kUnderConstruction);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    auto replica = bip_.add_replicas();
    replica->set_gen_stamp(1001);
    replica->set_num_bytes(0);
    replica->set_dn_uuid("datanode-1");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerUpdatePipelineTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::ExtendedBlockProto new_blk_from_cli;
  new_blk_from_cli.set_blockid(**********);
  new_blk_from_cli.set_generationstamp(1002);
  new_blk_from_cli.set_numbytes(2048);
  google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto> new_nodes;
  new_nodes.Add()->set_datanodeuuid("datanode-1");
  new_nodes.Add()->set_datanodeuuid("datanode-2");
  // By design, leave datanodeuuid empty.
  // BIPWriteManager::UpdatePipeline will ignore this datanode.
  new_nodes.Add();
  EXPECT_TRUE(bip_write_manager_
                  ->UpdatePipeline(blk_lck_comps, new_blk_from_cli, new_nodes)
                  .IsOK());
  BlockInfoProto bip;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &bip).IsOK());
  EXPECT_EQ(bip.version(), **********000);
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), **********);
  EXPECT_EQ(bip.gen_stamp(), 1002);
  EXPECT_EQ(bip.num_bytes(), 2048);
  EXPECT_EQ(bip.replicas_size(), 2);
  for (auto i = 0; i <= 1; i++) {
    EXPECT_EQ(bip.replicas(i).report_ts(), **********);
    EXPECT_EQ(bip.replicas(i).reporter(), ReplicaInfoProto::kClient);
    EXPECT_EQ(bip.replicas(i).state(), cloudfs::ReplicaStateProto::RBW);
    EXPECT_EQ(bip.replicas(i).gen_stamp(), 1002);
    EXPECT_EQ(bip.replicas(i).num_bytes(), 2048);
    EXPECT_EQ(bip.replicas(i).dn_uuid(), absl::StrFormat("datanode-%d", i + 1));
  }
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, bip).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerUpdatePipelineTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::ExtendedBlockProto new_blk_from_cli;
  new_blk_from_cli.set_blockid(**********);
  Status s = bip_write_manager_->UpdatePipeline(
      blk_lck_comps,
      new_blk_from_cli,
      google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when UpdatePipeline");
}

TEST_F(DirtyBlockInfoProtoManagerUpdatePipelineTest,
       BlockIsNotUnderConstruction) {
  bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::ExtendedBlockProto new_blk_from_cli;
  new_blk_from_cli.set_blockid(**********);
  new_blk_from_cli.set_generationstamp(1002);
  new_blk_from_cli.set_numbytes(1);
  google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto> new_nodes;
  Status s = bip_write_manager_->UpdatePipeline(
      blk_lck_comps, new_blk_from_cli, new_nodes);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to update pipeline not-under-construction block "
            "{id:**********,gs:1001,num_bytes:1024,state:3}");
}

TEST_F(DirtyBlockInfoProtoManagerUpdatePipelineTest, IllegalNewGsOrLen) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::ExtendedBlockProto new_blk_from_cli;
  new_blk_from_cli.set_blockid(**********);
  new_blk_from_cli.set_generationstamp(1001);
  new_blk_from_cli.set_numbytes(1024);
  google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto> new_nodes;
  Status s = bip_write_manager_->UpdatePipeline(
      blk_lck_comps, new_blk_from_cli, new_nodes);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Update block {id:**********,gs:1001,num_bytes:1024} "
            "to {gs:1001,num_bytes:1024} is forbidden");

  new_blk_from_cli.set_generationstamp(1002);
  new_blk_from_cli.set_numbytes(8);
  s = bip_write_manager_->UpdatePipeline(
      blk_lck_comps, new_blk_from_cli, new_nodes);
  EXPECT_EQ(s.message(),
            "Update block {id:**********,gs:1001,num_bytes:1024} "
            "to {gs:1002,num_bytes:8} is forbidden");
}

class DirtyBlockInfoProtoManagerAbandonBlockTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kUnderConstruction);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerAbandonBlockTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(
      bip_write_manager_->AbandonBlock(blk_lck_comps, **********).IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAbandonBlockTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->AbandonBlock(blk_lck_comps, **********);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
}

TEST_F(DirtyBlockInfoProtoManagerAbandonBlockTest,
       CannotAbandonDeprecatedBlock) {
  bip_.set_state(BlockInfoProto::kDeprecated);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->AbandonBlock(blk_lck_comps, **********);
  EXPECT_EQ(s.code(), Code::kIsRetry);
}

class DirtyBlockInfoProtoManagerFsyncTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kUnderConstruction);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerFsyncTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::BlockProto bp;
  bp.set_blockid(**********);
  bp.set_genstamp(1001);
  bp.set_numbytes(1024);
  EXPECT_TRUE(bip_write_manager_->Fsync(blk_lck_comps, bp, 2048).IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 2048);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerFsyncTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::BlockProto bp;
  bp.set_blockid(**********);
  Status s = bip_write_manager_->Fsync(blk_lck_comps, bp, 0);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when Fsync");
}

TEST_F(DirtyBlockInfoProtoManagerFsyncTest, BlockIsNotUnderConstruction) {
  bip_.set_state(BlockInfoProto::kUnderRecovery);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::BlockProto bp;
  bp.set_blockid(**********);
  bp.set_genstamp(1001);
  bp.set_numbytes(1024);
  Status s = bip_write_manager_->Fsync(blk_lck_comps, bp, 2048);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Update block length on a not-under-construction block "
            "{id:**********,gs:1001,num_bytes:1024,state:2}");
}

TEST_F(DirtyBlockInfoProtoManagerFsyncTest, IllegalGs) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::BlockProto bp;
  bp.set_blockid(**********);
  bp.set_genstamp(1000);
  bp.set_numbytes(2048);
  Status s = bip_write_manager_->Fsync(blk_lck_comps, bp, 2048);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to fsync a inconsistent block "
            "{id:**********,gs:1001,num_bytes:1024} with gs 1000");
}

class DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();

    bip_.set_state(BlockInfoProto::kUnderRecovery);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.set_recovery_gen_stamp(1002);

    request_.mutable_block()->set_blockid(**********);
    request_.set_newgenstamp(1002);
    request_.set_newlength(10);
    request_.add_newtaragets()->set_datanodeuuid("datanode-1");
    request_.set_closefile(false);
  }

 protected:
  BlockInfoProto bip_;
  cloudfs::datanode::CommitBlockSynchronizationRequestProto request_;
};

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps, request_, absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       CloseFileOfDnBlockWithEnoughReplicas) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  DatanodeInfo dn(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_closefile(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps, request_, absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       CloseFileOfDnBlockWithoutEnoughReplicas) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.clear_newtaragets();
  request_.set_closefile(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps, request_, absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       CloseFileOTosBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_write_mode(cloudfs::IoMode::TOS_BLOCK);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.clear_newtaragets();
  request_.set_closefile(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps, request_, absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       CloseFileForceSetCommitted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_closefile(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps,
                      request_,
                      absl::optional<BlockUCState>(BlockUCState::kCommitted))
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       CloseFileForceSetComplete) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.mutable_replicas()->Clear();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_closefile(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps,
                      request_,
                      absl::optional<BlockUCState>(BlockUCState::kComplete))
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest, DeleteBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_deleteblock(true);
  EXPECT_TRUE(bip_write_manager_
                  ->CommitBlockSynchronization(
                      blk_lck_comps, request_, absl::optional<BlockUCState>())
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       DeleteDeprecatedBlock) {
  bip_.set_state(BlockInfoProto::kDeprecated);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_deleteblock(true);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps, request_, absl::optional<BlockUCState>());
  EXPECT_EQ(s.code(), Code::kIsRetry);
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       DeleteMissingBlock01) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_deleteblock(true);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps,
      request_,
      absl::optional<BlockUCState>(BlockUCState::kPersisted));
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(
      s.message(),
      "request.deleteblock() is true but force_set_blk_state.value() is 4");
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       DeleteMissingBlock02) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_deleteblock(true);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps, request_, absl::optional<BlockUCState>());
  EXPECT_EQ(s.code(), Code::kIsRetry);
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest, IllegalNewGs) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_newgenstamp(1003);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps, request_, absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "The recovery id 1003 does not match current "
            "recovery id 1002 for block B**********");
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  request_.set_deleteblock(false);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps, request_, absl::optional<BlockUCState>());
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Found missing B********** when CommitBlockSynchronization");
}

TEST_F(DirtyBlockInfoProtoManagerCommitBlockSynchronizationTest,
       IllegalForceSetState) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CommitBlockSynchronization(
      blk_lck_comps,
      request_,
      absl::optional<BlockUCState>(BlockUCState::kCommitted));
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "State of last_bip_c is not equal to force_set_last_blk_state, "
            "last_bip_c:{id:**********,gs:1002,num_bytes:10,state:1}, "
            "force_set_last_blk_state:3");
}

class DirtyBlockInfoProtoManagerSealedBlockTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();

    last_bip_.set_state(BlockInfoProto::kUnderConstruction);
    last_bip_.set_block_id(1076015590);
    last_bip_.set_gen_stamp(kBlockProtocolV2GenerationStamp);
    last_bip_.set_num_bytes(1024);
    last_bip_.set_inode_id(17390);
    last_bip_.set_expected_rep(2);
  }

 protected:
  BlockInfoProto last_bip_;
};

TEST_F(DirtyBlockInfoProtoManagerSealedBlockTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  auto block_id = last_bip_.block_id();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps =
      bip_write_manager_->Lock(std::set<BlockID>{block_id}, __FILE__, __LINE__);
  BlockProto bp;
  bp.set_blockid(block_id);
  uint32_t commit_length = 512;
  EXPECT_TRUE(
      bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length)
          .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, block_id, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), block_id);
  EXPECT_EQ(r.gen_stamp(), kBlockProtocolV2GenerationStamp);
  EXPECT_EQ(r.num_bytes(), commit_length);
  EXPECT_EQ(r.expected_rep(), 2);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerSealedBlockTest, HappyCaseBlockUnderRecovery) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  last_bip_.set_state(BlockInfoProto::kUnderRecovery);
  auto block_id = last_bip_.block_id();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps =
      bip_write_manager_->Lock(std::set<BlockID>{block_id}, __FILE__, __LINE__);
  BlockProto bp;
  bp.set_blockid(block_id);
  uint32_t commit_length = 512;
  EXPECT_TRUE(
      bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length)
          .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, block_id, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), block_id);
  EXPECT_EQ(r.gen_stamp(), kBlockProtocolV2GenerationStamp);
  EXPECT_EQ(r.num_bytes(), commit_length);
  EXPECT_EQ(r.expected_rep(), 2);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerSealedBlockTest, BlockIsMissing) {
  auto block_id = last_bip_.block_id();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps =
      bip_write_manager_->Lock(std::set<BlockID>{block_id}, __FILE__, __LINE__);
  BlockProto bp;
  bp.set_blockid(block_id);
  uint32_t commit_length = 512;
  Status s =
      bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B1076015590 when CommitSealedBlock");
}

TEST_F(DirtyBlockInfoProtoManagerSealedBlockTest, BlockStateIsMismatched1) {
  {
    auto block_id = last_bip_.block_id();
    last_bip_.set_state(BlockInfoProto::kCommitted);
    last_bip_.set_block_id(block_id);
    EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
        .Times(1)
        .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
    auto blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{block_id}, __FILE__, __LINE__);
    BlockProto bp;
    bp.set_blockid(block_id);
    uint32_t commit_length = 512;
    auto s =
        bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
    EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  }
  {
    auto block_id = last_bip_.block_id() + 1;
    last_bip_.set_state(BlockInfoProto::kComplete);
    last_bip_.set_block_id(block_id);
    EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
        .Times(1)
        .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
    auto blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{block_id}, __FILE__, __LINE__);
    BlockProto bp;
    bp.set_blockid(block_id);
    uint32_t commit_length = 512;
    auto s =
        bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
    EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  }
  {
    auto block_id = last_bip_.block_id() + 2;
    last_bip_.set_state(BlockInfoProto::kPersisted);
    last_bip_.set_block_id(block_id);
    EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
        .Times(1)
        .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
    auto blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{block_id}, __FILE__, __LINE__);
    BlockProto bp;
    bp.set_blockid(block_id);
    uint32_t commit_length = 512;
    auto s =
        bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
    EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  }
}

TEST_F(DirtyBlockInfoProtoManagerSealedBlockTest, BlockGSIsMismatched1) {
  {
    auto block_id = last_bip_.block_id() + 1;
    last_bip_.set_block_id(block_id);
    last_bip_.set_gen_stamp(1);
    EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
        .Times(1)
        .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
    auto blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{block_id}, __FILE__, __LINE__);
    BlockProto bp;
    bp.set_blockid(block_id);
    uint32_t commit_length = 512;
    auto s =
        bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
    EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  }
  {
    auto block_id = last_bip_.block_id() + 2;
    last_bip_.set_block_id(block_id);
    EXPECT_CALL(*meta_storage_, GetBlockInfo(block_id, _))
        .Times(1)
        .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
    auto blk_lck_comps = bip_write_manager_->Lock(
        std::set<BlockID>{block_id}, __FILE__, __LINE__);
    BlockProto bp;
    bp.set_blockid(block_id);
    bp.set_genstamp(1);
    uint32_t commit_length = 512;
    auto s =
        bip_write_manager_->CommitSealedBlock(blk_lck_comps, bp, commit_length);
    EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  }
}

class DirtyBlockInfoProtoManagerSetReplicationTest
    : public DirtyBlockInfoProtoManagerTest {};

TEST_F(DirtyBlockInfoProtoManagerSetReplicationTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderRecovery);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(1024);
  bip.set_inode_id(17390);
  bip.set_expected_rep(3);
  bip.set_recovery_gen_stamp(1002);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  BlockProto bp;
  bp.set_blockid(**********);
  EXPECT_TRUE(bip_write_manager_->SetReplication(blk_lck_comps, bp, 1).IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUnderRecovery);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.expected_rep(), 1);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerSetReplicationTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  BlockProto bp;
  bp.set_blockid(**********);
  Status s = bip_write_manager_->SetReplication(blk_lck_comps, bp, 1);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when SetReplication");
}

class DirtyBlockInfoProtoManagerReleaseLeaseTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();

    penult_bip_.set_state(BlockInfoProto::kComplete);
    penult_bip_.set_block_id(**********);
    penult_bip_.set_gen_stamp(1001);
    penult_bip_.set_num_bytes(1024);
    penult_bip_.set_inode_id(17390);
    penult_bip_.set_expected_rep(3);
    penult_bp_.set_blockid(**********);
    penult_bp_.set_genstamp(1001);
    penult_bp_.set_numbytes(1024);

    last_bip_.set_state(BlockInfoProto::kComplete);
    last_bip_.set_block_id(1076015590);
    last_bip_.set_gen_stamp(1021);
    last_bip_.set_num_bytes(2048);
    last_bip_.set_inode_id(17390);
    last_bip_.set_expected_rep(3);
    last_bp_.set_blockid(1076015590);
    last_bp_.set_genstamp(1021);
    last_bp_.set_numbytes(2048);
  }

 protected:
  BlockInfoProto penult_bip_;
  cloudfs::BlockProto penult_bp_;
  BlockInfoProto last_bip_;
  cloudfs::BlockProto last_bp_;
};

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, OnlyLastBlockAndIsMinRepl) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015590}, __FILE__, __LINE__);
  Status s = bip_write_manager_->ReleaseLease(
      blk_lck_comps, cloudfs::BlockProto(), last_bp_);
  EXPECT_TRUE(s.IsFalse());
  EXPECT_EQ(s.message(), "The last two blocks are both min-repl");
  BlockInfoProto r;
  s = bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to write B1076015590 with unstarted_tx_num_: 0");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, TwoBlocksAreBothMinRepl) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_TRUE(s.IsFalse());
  EXPECT_EQ(s.message(), "The last two blocks are both min-repl");
  BlockInfoProto r;
  s = bip_write_manager_->PreCommit(blk_lck_comps, **********, &r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to write B********** with unstarted_tx_num_: 0");
  s = bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to write B1076015590 with unstarted_tx_num_: 0");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, PenultBlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing penult B********** when ReleaseLease");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       PenultBlockIsUnderConstruction) {
  penult_bip_.set_state(BlockInfoProto::kUnderConstruction);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to release lease on illegal penultimate block "
            "{id:**********,gs:1001,num_bytes:1024,state:1}");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, PenultBlockIsNotMinRepl) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  penult_bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(
      s.message(),
      "Failed to release lease. "
      "Committed penultimate block {id:**********,gs:1001,num_bytes:1024} "
      "are waiting to be minimally replicated. "
      "Please try again later.");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, LastBlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing last B1076015590 when ReleaseLease");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       LastBlockIsUnderConstrcutionAndZeroRepl01) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  last_bip_.set_state(BlockInfoProto::kUnderConstruction);
  last_bip_.set_num_bytes(0);
  last_bip_.clear_replicas();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.code(), Code::kAbandonLastBlock);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1021);
  EXPECT_EQ(r.num_bytes(), 0);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       LastBlockIsUnderConstrcutionAndZeroRepl02) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  last_bip_.set_state(BlockInfoProto::kUnderConstruction);
  last_bip_.set_num_bytes(0);
  auto replica = last_bip_.add_replicas();
  replica->set_reporter(ReplicaInfoProto::kNamenode);
  replica->set_gen_stamp(1021);
  replica->set_num_bytes(0);
  replica = last_bip_.add_replicas();
  replica->set_dn_uuid("datanode-1");
  replica->set_reporter(ReplicaInfoProto::kNamenode);
  replica->set_gen_stamp(1021);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-2");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.code(), Code::kAbandonLastBlock);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kDeprecated);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1021);
  EXPECT_EQ(r.num_bytes(), 0);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       LastBlockIsUnderConstructionButNotZeroRepl01) {
  last_bip_.set_state(BlockInfoProto::kUnderConstruction);
  last_bip_.set_num_bytes(0);
  auto replica = last_bip_.add_replicas();
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_gen_stamp(1021);
  replica->set_num_bytes(0);
  replica = last_bip_.add_replicas();
  replica->set_dn_uuid("datanode-1");
  replica->set_reporter(ReplicaInfoProto::kNamenode);
  replica->set_gen_stamp(1021);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-2");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(bip_write_manager_->TestOnlyGetBlock(1076015590).Get().version(),
            0);
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       LastBlockIsUnderConstructionButNotZeroRepl02) {
  last_bip_.set_state(BlockInfoProto::kUnderConstruction);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(bip_write_manager_->TestOnlyGetBlock(1076015590).Get().version(),
            0);
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest,
       LastBlockIsCommittedButNotMinRepl) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  last_bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(s.message(),
            "Failed to release lease. "
            "Committed last block {id:1076015590,gs:1021,num_bytes:2048} "
            "are waiting to be minimally replicated. "
            "Please try again later.");
}

TEST_F(DirtyBlockInfoProtoManagerReleaseLeaseTest, LastBlockIsDeprecated) {
  last_bip_.set_state(BlockInfoProto::kDeprecated);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(penult_bip_), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(last_bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->ReleaseLease(blk_lck_comps, penult_bp_, last_bp_);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "The unexpected state of last block B1076015590 is 6");
}

class DirtyBlockInfoProtoManagerInitRecoverTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kUnderConstruction);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(10);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.set_pufs_name("18014398509482046/block/**********.block");
    bp_.set_blockid(**********);
  }

 protected:
  BlockInfoProto bip_;
  BlockProto bp_;
};

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  auto replica = bip_.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::RBW);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-1");
  replica = bip_.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::RBW);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-2");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  DatanodeIDProto datanode_id_1;
  datanode_id_1.set_ipaddr("***********");
  datanode_id_1.set_hostname("hostname-1");
  datanode_id_1.set_datanodeuuid("datanode-1");
  datanode_id_1.set_xferport(5060);
  datanode_id_1.set_infoport(5070);
  datanode_id_1.set_ipcport(5080);
  DatanodeInfo dn_1(1, datanode_id_1, cnetpp::base::IPAddress("***********"));
  EXPECT_TRUE(dn_1.CheckAndUpdateHeartbeat());
  DatanodeIDProto datanode_id_2;
  datanode_id_2.set_ipaddr("***********");
  datanode_id_2.set_hostname("hostname-2");
  datanode_id_2.set_datanodeuuid("datanode-2");
  datanode_id_2.set_xferport(5060);
  datanode_id_2.set_infoport(5070);
  datanode_id_2.set_ipcport(5080);
  // TODO: Make DatanodeInfo use TimeUtilV2.
  // Sleep 1 ms to make last_heartbeat_ of datanode-2 bigger.
  std::this_thread::sleep_for(std::chrono::milliseconds(1));
  DatanodeInfo dn_2(2, datanode_id_2, cnetpp::base::IPAddress("***********"));
  EXPECT_TRUE(dn_2.CheckAndUpdateHeartbeat());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .WillRepeatedly(Return(&dn_1));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromIdInternal(1))
      .WillRepeatedly(Return(&dn_1));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-2"))
      .WillRepeatedly(Return(&dn_2));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromIdInternal(2))
      .WillRepeatedly(Return(&dn_2));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  std::string primary_dn_uuid;
  cloudfs::RecoveringBlockProto recovering_block;

  meta_storage_ = new GMockMetaStorage();
  block_manager_ = new GMockBlockManager();
  block_manager_->SetMetaStorage(std::shared_ptr<MetaStorage>(meta_storage_));
  datanode_manager_->set_block_manager(block_manager_);

  EXPECT_TRUE(bip_write_manager_
                  ->InitRecover(blk_lck_comps,
                                bp_,
                                1002,
                                absl::optional<std::string>(),
                                &primary_dn_uuid,
                                &recovering_block)
                  .IsOK());
  EXPECT_EQ(primary_dn_uuid, "datanode-2");
  EXPECT_TRUE(recovering_block.IsInitialized());
  EXPECT_EQ(recovering_block.newgenstamp(), 1002);
  EXPECT_EQ(recovering_block.block().b().poolid(), "bp-1");
  EXPECT_EQ(recovering_block.block().b().blockid(), **********);
  EXPECT_EQ(recovering_block.block().b().generationstamp(), 1001);
  EXPECT_EQ(recovering_block.block().b().numbytes(), 10);
  EXPECT_EQ(recovering_block.block().b().blockpufsname(),
            "18014398509482046/block/**********.block");
  EXPECT_EQ(recovering_block.block().offset(), -1);
  EXPECT_EQ(recovering_block.block().locs_size(), 2);
  EXPECT_EQ(recovering_block.block().locs(0).id().ipaddr(), "***********");
  EXPECT_EQ(recovering_block.block().locs(0).id().hostname(), "hostname-1");
  EXPECT_EQ(recovering_block.block().locs(0).id().datanodeuuid(), "datanode-1");
  EXPECT_EQ(recovering_block.block().locs(0).id().xferport(), 5060);
  EXPECT_EQ(recovering_block.block().locs(0).id().infoport(), 5070);
  EXPECT_EQ(recovering_block.block().locs(0).id().ipcport(), 5080);
  EXPECT_EQ(recovering_block.block().locs(1).id().ipaddr(), "***********");
  EXPECT_EQ(recovering_block.block().locs(1).id().hostname(), "hostname-2");
  EXPECT_EQ(recovering_block.block().locs(1).id().datanodeuuid(), "datanode-2");
  EXPECT_EQ(recovering_block.block().locs(1).id().xferport(), 5060);
  EXPECT_EQ(recovering_block.block().locs(1).id().infoport(), 5070);
  EXPECT_EQ(recovering_block.block().locs(1).id().ipcport(), 5080);
  EXPECT_EQ(recovering_block.block().storagetypes_size(), 2);
  EXPECT_EQ(recovering_block.block().storageids_size(), 2);
  EXPECT_FALSE(recovering_block.block().corrupt());
  EXPECT_TRUE(recovering_block.block().blocktoken().identifier().empty());
  EXPECT_TRUE(recovering_block.block().blocktoken().password().empty());
  EXPECT_TRUE(recovering_block.block().blocktoken().kind().empty());
  EXPECT_TRUE(recovering_block.block().blocktoken().service().empty());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUnderRecovery);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 10);
  EXPECT_EQ(r.recovery_gen_stamp(), 1002);
  EXPECT_EQ(r.replicas_size(), 2);
  EXPECT_FALSE(r.replicas(0).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(r.replicas(1).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());

  primary_dn_uuid.clear();
  recovering_block.Clear();
  EXPECT_TRUE(bip_write_manager_
                  ->InitRecover(blk_lck_comps,
                                bp_,
                                1003,
                                absl::optional<std::string>(),
                                &primary_dn_uuid,
                                &recovering_block)
                  .IsOK());
  EXPECT_EQ(primary_dn_uuid, "datanode-1");
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.replicas_size(), 2);
  EXPECT_TRUE(r.replicas(0).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(r.replicas(1).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());

  primary_dn_uuid.clear();
  recovering_block.Clear();
  EXPECT_TRUE(bip_write_manager_
                  ->InitRecover(blk_lck_comps,
                                bp_,
                                1004,
                                absl::optional<std::string>(),
                                &primary_dn_uuid,
                                &recovering_block)
                  .IsOK());
  EXPECT_EQ(primary_dn_uuid, "datanode-2");
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.replicas_size(), 2);
  EXPECT_FALSE(r.replicas(0).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(r.replicas(1).tried_as_primary_4_block_recovery());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, ForceSetPrimaryDnUuid) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::RecoveringBlockProto recovering_block;
  EXPECT_TRUE(bip_write_manager_
                  ->InitRecover(blk_lck_comps,
                                bp_,
                                1002,
                                absl::optional<std::string>("datanode-1"),
                                nullptr,
                                &recovering_block)
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, TosBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_write_mode(cloudfs::IoMode::TOS_BLOCK);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::RecoveringBlockProto recovering_block;
  EXPECT_TRUE(bip_write_manager_
                  ->InitRecover(blk_lck_comps,
                                bp_,
                                1002,
                                absl::optional<std::string>("datanode-1"),
                                nullptr,
                                &recovering_block)
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUnderRecovery);
  EXPECT_EQ(r.recovery_gen_stamp(), 1001);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->InitRecover(
      blk_lck_comps, bp_, 1002, absl::optional<std::string>("datanode-1"));
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when InitRecover");
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, BlockIsCommitted) {
  bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->InitRecover(blk_lck_comps,
                                      bp_,
                                      1002,
                                      absl::optional<std::string>("datanode-1"),
                                      nullptr,
                                      nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to recover not-uc/ur block "
            "{id:**********,gs:1001,num_bytes:10,state:3}");
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest, NewGsIsSmallerThanOriginGs) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s =
      bip_write_manager_->InitRecover(blk_lck_comps,
                                      bp_,
                                      1000,
                                      absl::optional<std::string>("datanode-1"),
                                      nullptr,
                                      nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Try to recover block "
            "{id:**********,gen_stamp:1001,recovery_gen_stamp:0} "
            "with a smaller recovery_gen_stamp 1000");
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest,
       CannotChoosePrimaryDn4BlockRecoveryBecauseNoReplicas) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->InitRecover(blk_lck_comps,
                                             bp_,
                                             1002,
                                             absl::optional<std::string>(),
                                             nullptr,
                                             nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "No primary datanode for block recovery for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerInitRecoverTest,
       CannotChoosePrimaryDn4BlockRecoveryBecauseAllDNsAreUnhealthy) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  auto replica = bip_.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::RBW);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-1");
  replica = bip_.add_replicas();
  replica->set_report_ts(**********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::RBW);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(0);
  replica->set_dn_uuid("datanode-2");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  // DN1 is dead.
  DatanodeIDProto datanode_id_1;
  datanode_id_1.set_ipaddr("***********");
  datanode_id_1.set_hostname("hostname-1");
  datanode_id_1.set_datanodeuuid("datanode-1");
  datanode_id_1.set_xferport(5060);
  datanode_id_1.set_infoport(5070);
  datanode_id_1.set_ipcport(5080);
  DatanodeInfo dn_1(1, datanode_id_1, cnetpp::base::IPAddress("***********"));
  EXPECT_TRUE(dn_1.MarkDead(nullptr, std::chrono::hours(0)));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .WillRepeatedly(Return(&dn_1));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromIdInternal(1))
      .WillRepeatedly(Return(&dn_1));
  // DN2 is missing.
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-2"))
      .WillRepeatedly(Return(nullptr));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->InitRecover(blk_lck_comps,
                                             bp_,
                                             1002,
                                             absl::optional<std::string>(),
                                             nullptr,
                                             nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "No primary datanode for block recovery for B**********");
}

class DirtyBlockInfoProtoManagerUploadBlockTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kComplete);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when UploadBlock");
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, DnUuidOrPufsNameIsEmpty) {
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(blk_lck_comps,
                                             Block(**********, 1024, 1001),
                                             "datanode-1",
                                             "",
                                             "upload-id-1",
                                             &upload_cmd,
                                             &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid/pufs_name is empty");

  s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid/pufs_name is empty");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, BlockFromDnIsNotEqual) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1002),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare block "
            "{id:**********,gs:1001,num_bytes:1024} with "
            "wrong replica{id:**********,gs:1002,num_bytes:1024}");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block(**********, 1024, 1001),
                                "datanode-1",
                                "18014398509482046/block/**********.block",
                                "upload-id-1",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), 1024);
  EXPECT_EQ(upload_cmd.blockpufsname(),
            "18014398509482046/block/**********.block");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload-id-1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.pufs_name(), "18014398509482046/block/**********.block");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload-id-1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);

  // Retry before BlockInfoProto is flushed.
  cloudfs::datanode::UploadCommandProto upload_cmd_2;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd_2,
      &ne_cmd);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_FALSE(upload_cmd_2.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());

  // Retry after BlockInfoProto is flushed.
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd_2,
      &ne_cmd);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_EQ(upload_cmd_2.SerializeAsString(), upload_cmd.SerializeAsString());
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_EQ(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).exception(),
      JavaExceptions::kIOException);
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, UploadTooManyTimes) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_state(BlockInfoProto::kUploadIssued);
  bip_.set_upload_issued_times(1025);
  for (int i = 1; i <= 1024; i++) {
    *bip_.add_aborted_upload_ids() = absl::StrFormat("upload-id-%d", i);
  }
  bip_.set_curr_upload_id("upload-id-1025");
  bip_.set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block(**********, 1024, 1001),
                                "datanode-2",
                                "18014398509482046/block/**********.block",
                                "upload-id-1026",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-2");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), 1024);
  EXPECT_EQ(upload_cmd.blockpufsname(),
            "18014398509482046/block/**********.block");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_max_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload-id-1026");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 1024);
  EXPECT_EQ(upload_cmd.aborteduploadids(0), "upload-id-2");
  EXPECT_EQ(upload_cmd.aborteduploadids(1023), "upload-id-1025");
  EXPECT_FALSE(ne_cmd.IsInitialized());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.pufs_name(), "18014398509482046/block/**********.block");
  EXPECT_EQ(r.upload_issued_times(), 1026);
  EXPECT_EQ(r.aborted_upload_ids_size(), 1024);
  EXPECT_EQ(r.aborted_upload_ids(0), "upload-id-2");
  EXPECT_EQ(r.aborted_upload_ids(1023), "upload-id-1025");
  EXPECT_EQ(r.curr_upload_id(), "upload-id-1026");
  EXPECT_EQ(r.dn_uuid(), "datanode-2");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_max_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest,
       BlockIsBeingUploadedByOtherDn) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_state(BlockInfoProto::kUploadIssued);
  bip_.set_dn_uuid("datanode-1");
  bip_.set_nn_exp_ts(********** + 1);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-2",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-2} due to "
            "B********** is being uploaded by DN{uuid:datanode-1}");
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, BlockIsPersisted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_state(BlockInfoProto::kPersisted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockTest, BlockIsCommitted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->UploadBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      &upload_cmd,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Try to upload B********** with inconsistent state 3");
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

class DirtyBlockInfoProtoManagerPersistBlockTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kUploadIssued);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.set_pufs_name("18014398509482046/block/**********.block");
    bip_.set_curr_upload_id("upload-id-1");
    bip_.set_dn_uuid("datanode-1");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, DnUuidOrPufsNameIsEmpty) {
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid/pufs_name is empty");

  s = bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block(**********, 1024, 1001),
                                       "datanode-1",
                                       "",
                                       "upload-id-1",
                                       absl::nullopt,
                                       nullptr,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid/pufs_name is empty");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, BlockFromDnIsNotEqual) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1002),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare block "
            "{id:**********,gs:1001,num_bytes:1024} with "
            "wrong replica{id:**********,gs:1002,num_bytes:1024}");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block(**********, 1024, 1001),
                                 "datanode-1",
                                 "18014398509482046/block/**********.block",
                                 "upload-id-1",
                                 absl::nullopt,
                                 nullptr,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.pufs_name(), "18014398509482046/block/**********.block");

  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd_2;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd_2);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_FALSE(ne_cmd_2.IsInitialized());
  // Retry before BlockInfoProto is flushed.

  // Retry after BlockInfoProto is flushed.
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd_2);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_EQ(ne_cmd_2.SerializeAsString(), ne_cmd.SerializeAsString());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, DnPutObjectWithoutUploadId) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  bip_.set_state(BlockInfoProto::kComplete);
  bip_.clear_curr_upload_id();
  bip_.clear_dn_uuid();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block(**********, 1024, 1001),
                                 "datanode-1",
                                 "18014398509482046/block/**********.block",
                                 "",
                                 absl::nullopt,
                                 nullptr,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), 1024);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.pufs_name(), "18014398509482046/block/**********.block");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when PersistBlock");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, BlockIsCommitted) {
  bip_.set_state(BlockInfoProto::kCommitted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to persist non-complete B**********");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockTest, BlockIsDeprecated) {
  bip_.set_state(BlockInfoProto::kDeprecated);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s = bip_write_manager_->PersistBlock(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      "18014398509482046/block/**********.block",
      "upload-id-1",
      absl::nullopt,
      nullptr,
      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to persist B********** with "
            "inconsistent state 6");
}

class DirtyBlockInfoProtoManagerDeleteBlockTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kDeprecated);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.set_pufs_name("18014398509482046/block/**********.block");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerDeleteBlockTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->DeleteBlock(blk_lck_comps,
                                             Block(**********, 1024, 1001));
  EXPECT_EQ(s.code(), Code::kIsRetry);
}

TEST_F(DirtyBlockInfoProtoManagerDeleteBlockTest, BlockIsAlreadyDeleted) {
  bip_.set_state(BlockInfoProto::kDeleted);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->DeleteBlock(blk_lck_comps,
                                             Block(**********, 1024, 1001));
  EXPECT_EQ(s.code(), Code::kIsRetry);
}

TEST_F(DirtyBlockInfoProtoManagerDeleteBlockTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->DeleteBlock(blk_lck_comps, Block(**********, 1024, 1001))
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kDeleted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerDeleteBlockTest, BlockIsUnderConstruction) {
  bip_.set_state(BlockInfoProto::kUnderConstruction);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->DeleteBlock(blk_lck_comps,
                                             Block(**********, 1024, 1001));
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Delete non-deprecated B**********, whose state is 1");
}

class DirtyBlockInfoProtoManagerReportDeletedReplicaTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kCommitted);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.add_replicas()->set_dn_uuid("datanode-1");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerReportDeletedReplicaTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->ReportDeletedReplica(
      blk_lck_comps, Block(**********, 1024, 1001), "datanode-1");
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when ReportDeletedReplica");
}

TEST_F(DirtyBlockInfoProtoManagerReportDeletedReplicaTest, ReplicaIsExisted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->ReportDeletedReplica(blk_lck_comps,
                                         Block(**********, 1024, 1001),
                                         "datanode-1")
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.replicas_size(), 0);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerReportDeletedReplicaTest,
       ReplicaIsNotExisted) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->ReportDeletedReplica(blk_lck_comps,
                                         Block(**********, 1024, 1001),
                                         "datanode-2")
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.replicas_size(), 1);
  EXPECT_EQ(r.replicas(0).dn_uuid(), "datanode-1");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

class DirtyBlockInfoProtoManagerReportExistedReplicaTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kCommitted);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.add_replicas()->set_dn_uuid("datanode-1");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerReportExistedReplicaTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->ReportExistedReplica(blk_lck_comps,
                                         Block(**********, 2048, 1002),
                                         "datanode-1",
                                         cloudfs::ReplicaStateProto::RBW)
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.replicas_size(), 1);
  ReplicaInfoProto replica = r.replicas(0);
  EXPECT_EQ(replica.report_ts(), **********);
  EXPECT_FALSE(replica.has_invalidate_ts());
  EXPECT_EQ(replica.reporter(), ReplicaInfoProto::kDatanode);
  EXPECT_FALSE(replica.tried_as_primary_4_block_recovery());
  EXPECT_FALSE(replica.is_bad());
  EXPECT_EQ(replica.state(), cloudfs::ReplicaStateProto::RBW);
  EXPECT_EQ(replica.gen_stamp(), 1002);
  EXPECT_EQ(replica.num_bytes(), 2048);
  EXPECT_EQ(replica.dn_uuid(), "datanode-1");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());

  EXPECT_TRUE(bip_write_manager_
                  ->ReportExistedReplica(blk_lck_comps,
                                         Block(**********, 1024, 1001),
                                         "datanode-1",
                                         cloudfs::ReplicaStateProto::FINALIZED)
                  .IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.replicas_size(), 1);
  replica.Clear();
  replica = r.replicas(0);
  EXPECT_EQ(replica.state(), cloudfs::ReplicaStateProto::FINALIZED);
  EXPECT_EQ(replica.gen_stamp(), 1001);
  EXPECT_EQ(replica.num_bytes(), 1024);
  EXPECT_EQ(replica.dn_uuid(), "datanode-1");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerReportExistedReplicaTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->ReportExistedReplica(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      cloudfs::ReplicaStateProto::FINALIZED);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when ReportExistedReplica");
}

TEST_F(DirtyBlockInfoProtoManagerReportExistedReplicaTest, Retry) {
  ReplicaInfoProto* replica = bip_.add_replicas();
  replica->set_report_ts(1);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(1024);
  replica->set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->ReportExistedReplica(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      "datanode-1",
      cloudfs::ReplicaStateProto::FINALIZED);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_EQ(bip_write_manager_->TestOnlyGetBlock(**********).Get().version(),
            0);
}

class DirtyBlockInfoProtoManagerReportBadReplicasTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();
    bip_.set_state(BlockInfoProto::kCommitted);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1024);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(3);
    bip_.add_replicas()->set_dn_uuid("datanode-1");
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerReportBadReplicasTest, HappyCase) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->ReportBadReplicas(
                      blk_lck_comps,
                      Block(**********, 2048, 1002),
                      std::vector<std::string>{"datanode-1", "datanode-2"})
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), 1024);
  EXPECT_EQ(r.replicas_size(), 2);
  for (const auto& replica : r.replicas()) {
    EXPECT_EQ(replica.report_ts(), **********);
    EXPECT_EQ(replica.reporter(), ReplicaInfoProto::kDatanode);
    EXPECT_TRUE(replica.is_bad());
    EXPECT_EQ(replica.state(), cloudfs::ReplicaStateProto::TEMPORARY);
    EXPECT_EQ(replica.gen_stamp(), 1002);
    EXPECT_EQ(replica.num_bytes(), 2048);
  }
  EXPECT_EQ(r.replicas(0).dn_uuid(), "datanode-1");
  EXPECT_EQ(r.replicas(1).dn_uuid(), "datanode-2");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerReportBadReplicasTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->ReportBadReplicas(
      blk_lck_comps,
      Block(**********, 1024, 1001),
      std::vector<std::string>{"datanode-1"});
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when ReportBadReplicas");
}

/**
 * ACC Related Tests begin
 */
class DirtyBlockInfoProtoManagerAccTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    default_namespate_type = FLAGS_namespace_type;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
    DirtyBlockInfoProtoManagerTest::SetUp();

    block_size_49M = 50000000UL - 1;
    block_size_99M = 100000000UL - 1;
    block_size_128M = 128UL * 1024 * 1024;

    inode.set_id(17390);
    inode.set_parent_id(16385);
    inode.set_name("123");
  }

  void TearDown() override {
    FLAGS_namespace_type = default_namespate_type;
  }

 protected:
  int32_t default_namespate_type;
  uint32_t block_size_49M;
  uint32_t block_size_99M;
  uint32_t block_size_128M;

  INode inode;
};

TEST_F(DirtyBlockInfoProtoManagerAccTest, TestAddBlock) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  INode inode;
  inode.set_id(17390);
  inode.set_replication(2);
  inode.mutable_ufs_file_info()->set_key("object/123");
  inode.mutable_ufs_file_info()->set_create_type(
      UfsFileCreateType::kUfsFileCreateTypeNormal);
  bip_write_manager_->AddBlock(blk_lck_comps,
                               Block(**********, 1024, 1001),
                               inode,
                               cloudfs::IoMode::DATANODE_BLOCK,
                               "18014398509482046/block/**********.block",
                               {"datanode-1", "datanode-2"});
  BlockInfoProto bip;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &bip).IsOK());
  EXPECT_EQ(bip.version(), **********000);
  EXPECT_EQ(bip.state(), BlockInfoProto::kUnderConstruction);
  EXPECT_EQ(bip.block_id(), **********);
  EXPECT_EQ(bip.gen_stamp(), 1001);
  EXPECT_EQ(bip.num_bytes(), 1024);
  EXPECT_EQ(bip.inode_id(), 17390);
  EXPECT_EQ(bip.expected_rep(), 2);
  EXPECT_EQ(bip.pufs_name(), "object/123");
  EXPECT_EQ(bip.type(), BlockInfoProto::kACCBlock);
  EXPECT_EQ(bip.part_num(), 1);
  EXPECT_TRUE(bip.has_bundle_offset());
  EXPECT_EQ(bip.bundle_offset(), 0);
  EXPECT_TRUE(bip.has_bundle_length());
  EXPECT_EQ(bip.bundle_length(), 0);
  EXPECT_EQ(bip.replicas_size(), 2);
  EXPECT_GT(bip.replicas(0).report_ts(), 0);
  EXPECT_EQ(bip.replicas(0).reporter(), ReplicaInfoProto::kNamenode);
  EXPECT_EQ(bip.replicas(0).state(), cloudfs::ReplicaStateProto::TEMPORARY);
  EXPECT_EQ(bip.replicas(0).gen_stamp(), 1001);
  EXPECT_EQ(bip.replicas(0).num_bytes(), 0);
  EXPECT_EQ(bip.replicas(0).dn_uuid(), "datanode-1");
  EXPECT_EQ(bip.replicas(1).report_ts(), **********);
  EXPECT_EQ(bip.replicas(1).reporter(), ReplicaInfoProto::kNamenode);
  EXPECT_EQ(bip.replicas(1).state(), cloudfs::ReplicaStateProto::TEMPORARY);
  EXPECT_EQ(bip.replicas(1).gen_stamp(), 1001);
  EXPECT_EQ(bip.replicas(1).num_bytes(), 0);
  EXPECT_EQ(bip.replicas(1).dn_uuid(), "datanode-2");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, bip).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAccTest,
       TestCompletePenultBlkAndCommitLastBlkTwoBlk) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");

  BlockInfoProto bip1;  // 128M
  BlockInfoProto bip2;  // 128M

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);
  auto b2 = inode.add_blocks();
  b2->set_blockid(1076015590);
  b2->set_genstamp(1012);
  b2->set_numbytes(0);

  bip1.set_state(BlockInfoProto::kCommitted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_curr_upload_id("upload_id_1");
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1012);
  bip2.set_num_bytes(0);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));
  DatanodeInfo dn1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_TRUE(dn1.CheckAndUpdateHeartbeat());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn1));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);

  EXPECT_TRUE(bip_write_manager_
                  ->CompletePenultBlkAndCommitLastBlk(
                      blk_lck_comps,
                      "/123",
                      inode,
                      Block{**********, block_size_128M, 1001},
                      Block{1076015590, block_size_128M, 1012},
                      Block{1076015590, 0, 1012},
                      false,
                      absl::nullopt,
                      absl::nullopt)
                  .IsOK());

  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_EQ(r.bundle_length(), 0);
  EXPECT_EQ(r.bundle_offset(), 0);
  EXPECT_EQ(r.part_num(), 1);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_EQ(r.bundle_length(), 0);
  EXPECT_EQ(r.bundle_offset(), block_size_128M);
  EXPECT_EQ(r.part_num(), 2);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAccTest,
       TestCompletePenultBlkAndCommitLastBlkTwoBlkAppend) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
  ufs_info->set_key("object/123");

  BlockInfoProto bip1;  // 128M
  BlockInfoProto bip2;  // 128M

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);
  auto b2 = inode.add_blocks();
  b2->set_blockid(1076015590);
  b2->set_genstamp(1012);
  b2->set_numbytes(0);

  bip1.set_state(BlockInfoProto::kCommitted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1012);
  bip2.set_num_bytes(0);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));
  DatanodeInfo dn1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_TRUE(dn1.CheckAndUpdateHeartbeat());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(1)
      .WillOnce(Return(&dn1));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);

  EXPECT_TRUE(bip_write_manager_
                  ->CompletePenultBlkAndCommitLastBlk(
                      blk_lck_comps,
                      "/123",
                      inode,
                      Block{**********, block_size_128M, 1001},
                      Block{1076015590, block_size_128M, 1012},
                      Block{1076015590, 0, 1012},
                      false,
                      absl::nullopt,
                      absl::nullopt)
                  .IsOK());

  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_FALSE(r.has_bundle_length());
  EXPECT_FALSE(r.has_bundle_offset());
  EXPECT_FALSE(r.has_part_num());
  EXPECT_FALSE(r.has_curr_upload_id());
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_FALSE(r.has_bundle_length());
  EXPECT_FALSE(r.has_bundle_offset());
  EXPECT_FALSE(r.has_part_num());
  EXPECT_FALSE(r.has_curr_upload_id());
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAccTest,
       TestCompletePenultBlkAndCommitLastBlkThreeBlkBundle) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));

  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");

  BlockInfoProto bip0;  // 49M
  BlockInfoProto bip1;  // 99M
  BlockInfoProto bip2;  // 128M
  BlockInfoProto r;

  DatanodeInfo dn1(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_TRUE(dn1.CheckAndUpdateHeartbeat());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromUuid("datanode-1"))
      .Times(AtLeast(1))
      .WillRepeatedly(Return(&dn1));

  BIPLockComponents blk_lck_comps;

  auto b0 = inode.add_blocks();
  b0->set_blockid(1076015570);
  b0->set_genstamp(1000);
  b0->set_numbytes(block_size_49M);
  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(0);

  bip0.set_state(BlockInfoProto::kCommitted);
  bip0.set_block_id(1076015570);
  bip0.set_gen_stamp(1000);
  bip0.set_num_bytes(block_size_49M);
  bip0.set_inode_id(17390);
  bip0.set_expected_rep(1);
  bip0.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip0.set_key_block(false);
  bip0.set_bundle_length(0);
  bip0.set_bundle_offset(0);
  bip0.set_part_num(1);
  bip0.set_pufs_offset(0);
  bip0.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica0 = bip0.add_replicas();
  replica0->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica0->set_reporter(ReplicaInfoProto::kDatanode);
  replica0->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica0->set_gen_stamp(1000);
  replica0->set_num_bytes(block_size_49M);
  replica0->set_dn_uuid("datanode-1");

  bip1.set_state(BlockInfoProto::kUnderConstruction);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(0);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_pufs_offset(block_size_49M);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015570, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip0), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015570, **********}, __FILE__, __LINE__);

  EXPECT_TRUE(bip_write_manager_
                  ->CompletePenultBlkAndCommitLastBlk(
                      blk_lck_comps,
                      "/123",
                      inode,
                      Block{1076015570, block_size_49M, 1000},
                      Block{**********, block_size_99M, 1001},
                      Block{**********, 0, 1001},
                      false,
                      absl::nullopt,
                      absl::nullopt)
                  .IsOK());

  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015570, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), 1076015570);
  EXPECT_EQ(r.gen_stamp(), 1000);
  EXPECT_EQ(r.num_bytes(), block_size_49M);
  EXPECT_EQ(r.key_block(), false);
  EXPECT_EQ(r.bundle_length(), 0);
  EXPECT_EQ(r.bundle_offset(), 0);
  EXPECT_EQ(r.part_num(), 1);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_EQ(r.num_bytes(), block_size_99M);
  EXPECT_EQ(r.bundle_length(), block_size_49M);
  EXPECT_EQ(r.bundle_offset(), 0);
  EXPECT_EQ(r.part_num(), 1);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  inode.mutable_blocks(1)->set_numbytes(block_size_99M);

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  auto& dbip1 = bip_write_manager_->TestOnlyGetBlock(**********);
  ReplicaInfoProto* replica1 = dbip1.Get().add_replicas();
  replica1->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica1->set_reporter(ReplicaInfoProto::kDatanode);
  replica1->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica1->set_gen_stamp(1001);
  replica1->set_num_bytes(block_size_99M);
  replica1->set_dn_uuid("datanode-1");

  blk_lck_comps.Reset();

  auto b2 = inode.add_blocks();
  b2->set_blockid(**********);
  b2->set_genstamp(1001);
  b2->set_numbytes(0);

  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1012);
  bip2.set_num_bytes(0);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));

  EXPECT_TRUE(bip_write_manager_
                  ->CompletePenultBlkAndCommitLastBlk(
                      blk_lck_comps,
                      "/123",
                      inode,
                      Block{**********, block_size_99M, 1001},
                      Block{1076015590, block_size_128M, 1012},
                      Block{1076015590, 0, 1012},
                      false,
                      absl::nullopt,
                      absl::nullopt)
                  .IsOK());

  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********001);
  EXPECT_EQ(r.state(), BlockInfoProto::kComplete);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_99M);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_EQ(r.bundle_length(), block_size_49M);
  EXPECT_EQ(r.bundle_offset(), 0);
  EXPECT_EQ(r.part_num(), 1);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  r.Clear();
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kCommitted);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1012);
  EXPECT_EQ(r.key_block(), true);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.bundle_length(), 0);
  EXPECT_EQ(r.bundle_offset(), block_size_49M + block_size_99M);
  EXPECT_EQ(r.part_num(), 2);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAccTest,
       TestCompletePenultBlkAndCommitLastBlkMaxPart) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");

  BlockInfoProto bip1;  // 128M
  BlockInfoProto bip2;  // 128M

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);
  auto b2 = inode.add_blocks();
  b2->set_blockid(1076015590);
  b2->set_genstamp(1012);
  b2->set_numbytes(0);

  bip1.set_state(BlockInfoProto::kCommitted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(9999);
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1012);
  bip2.set_num_bytes(0);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);

  EXPECT_FALSE(bip_write_manager_
                   ->CompletePenultBlkAndCommitLastBlk(
                       blk_lck_comps,
                       "/123",
                       inode,
                       Block{**********, block_size_128M, 1001},
                       Block{1076015590, block_size_128M, 1012},
                       Block{1076015590, 0, 1012},
                       false,
                       absl::nullopt,
                       absl::nullopt)
                   .IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerAccTest,
       TestCompletePenultBlkAndCommitLastBlkMaxAppend) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
  ufs_info->set_key("object/123");

  BlockInfoProto bip1;  // 128M
  BlockInfoProto bip2;  // 128M

  for (size_t i = 0; i < 33; i++) {
    auto b = inode.add_blocks();
    b->set_blockid(i);
    b->set_numbytes(block_size_128M);
    b->set_genstamp(i);
  }

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);
  auto b2 = inode.add_blocks();
  b2->set_blockid(1076015590);
  b2->set_genstamp(1012);
  b2->set_numbytes(0);

  bip1.set_state(BlockInfoProto::kCommitted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::APPEND);

  bip2.set_state(BlockInfoProto::kUnderConstruction);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1012);
  bip2.set_num_bytes(0);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********, 1076015590}, __FILE__, __LINE__);

  EXPECT_FALSE(bip_write_manager_
                   ->CompletePenultBlkAndCommitLastBlk(
                       blk_lck_comps,
                       "/123",
                       inode,
                       Block{**********, block_size_128M, 1001},
                       Block{1076015590, block_size_128M, 1012},
                       Block{1076015590, 0, 1012},
                       false,
                       absl::nullopt,
                       absl::nullopt)
                   .IsOK());
}

class DirtyBlockInfoProtoManagerUploadBlockAccTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    default_namespate_type = FLAGS_namespace_type;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
    DirtyBlockInfoProtoManagerTest::SetUp();

    block_size_49M = 50000000UL - 1;
    block_size_99M = 100000000UL - 1;
    block_size_128M = 128UL * 1024 * 1024;

    inode.set_id(17390);
    inode.set_parent_id(16385);
    inode.set_name("123");
  }

 protected:
  int32_t default_namespate_type;
  uint32_t block_size_49M;
  uint32_t block_size_99M;
  uint32_t block_size_128M;

  INode inode;
};

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, DnUuidIsEmpty) {
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      /*dn_uuid=*/"",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid is empty");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when UploadBlock");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DnReplicaIsNotEqualToBlock) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1002},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare "
            "block {id:**********,gs:1001,num_bytes:134217728} with "
            "wrong replica{id:**********,gs:1002,num_bytes:134217728}");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DoNotUploadAppendBlockIfINodeNotFound) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_pufs_offset(1);
  bip.set_type(BlockInfoProto::kACCBlock);
  bip.set_upload_type(cloudfs::datanode::UploadType::APPEND);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(Return(StatusCode::kFileNotFound));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-1} "
            "due to INode{id:17390} is not found for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DoNotUploadAppendBlockIfPrevBlockProtokNotFound) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_pufs_offset(1);
  bip.set_type(BlockInfoProto::kACCBlock);
  bip.set_upload_type(cloudfs::datanode::UploadType::APPEND);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(INode()), Return(StatusCode::kOK)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-1} "
            "due to previous block is not found for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DoNotUploadAppendBlockIfPrevBlockInfoProtokNotFound) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_pufs_offset(1);
  bip.set_type(BlockInfoProto::kACCBlock);
  bip.set_upload_type(cloudfs::datanode::UploadType::APPEND);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  INode inode;
  inode.add_blocks()->set_blockid(1076015570);
  inode.add_blocks()->set_blockid(**********);
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(StatusCode::kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015570, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-1} "
            "due to previous bip is not found for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DoNotUploadMpuBlockIfCurrUploadIdIsEmpty) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_pufs_offset(1);
  bip.set_type(BlockInfoProto::kACCBlock);
  bip.set_key_block(true);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-1} "
            "due to B********** does not have upload id");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       DoNotUploadMPUBlockIfINodeNotFound) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_pufs_offset(1);
  bip.set_type(BlockInfoProto::kACCBlock);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(Return(StatusCode::kFileNotFound));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->UploadBlock(blk_lck_comps,
                                      Block{**********, block_size_128M, 1001},
                                      "datanode-1",
                                      "object/123",
                                      "upload_id_1",
                                      &upload_cmd,
                                      &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not approve upload request from DN{uuid:datanode-1} "
            "due to INode{id:17390} is not found for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadMpuBlock) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;  // 128M
  bip1.set_state(BlockInfoProto::kComplete);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_curr_upload_id("upload_id_1");
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{**********, block_size_128M, 1001},
                                "datanode-1",
                                "object/123",
                                "upload_id_1",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());

  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::UPLOAD);
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());

  blk_lck_comps.Reset();
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_FALSE(bip_write_manager_
                   ->UploadBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 &upload_cmd,
                                 &ne_cmd)
                   .IsOK());

  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::UPLOAD);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  r = bip_write_manager_->TestOnlyGetBlock(**********).Get();
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadMpuBlockBundle) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;  // 128M
  bip1.set_state(BlockInfoProto::kComplete);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(block_size_99M);
  bip1.set_bundle_offset(block_size_49M);
  bip1.set_part_num(1);
  bip1.set_curr_upload_id("upload_id_1");
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(block_size_49M);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{**********, block_size_128M, 1001},
                                "datanode-1",
                                "object/123",
                                "upload_id_1",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());

  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::UPLOAD);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_EQ(upload_cmd.prevblocks_size(), 1);
  auto prev = upload_cmd.prevblocks(0);
  EXPECT_EQ(prev.offset(), 0);
  EXPECT_EQ(prev.corrupt(), false);
  auto prev_eb = prev.b();
  EXPECT_EQ(prev_eb.poolid(), "");
  EXPECT_EQ(prev_eb.blockid(), kInvalidBlockID);
  EXPECT_EQ(prev_eb.generationstamp(), 0);
  EXPECT_EQ(prev_eb.offset(), block_size_49M);
  EXPECT_EQ(prev_eb.numbytes(), block_size_99M);
  auto token = prev.blocktoken();
  EXPECT_TRUE(token.identifier().empty());
  EXPECT_TRUE(token.password().empty());
  EXPECT_TRUE(token.kind().empty());
  EXPECT_TRUE(token.service().empty());
  EXPECT_FALSE(ne_cmd.IsInitialized());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadMpuBlockBundleDeny) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_49M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kComplete);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_49M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(false);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_49M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_FALSE(bip_write_manager_
                   ->UploadBlock(blk_lck_comps,
                                 Block{**********, block_size_49M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 &upload_cmd,
                                 &ne_cmd)
                   .IsOK());

  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest,
       UploadMpuBlockBundleEvict) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStatePersisted);

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_49M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kComplete);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_49M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(false);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_49M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{**********, block_size_49M, 1001},
                                "datanode-1",
                                "object/123",
                                "upload_id_1",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());

  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), block_size_49M);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_49M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 0);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_FALSE(r.has_curr_upload_id());
  EXPECT_FALSE(r.has_dn_uuid());
  EXPECT_FALSE(r.has_nn_exp_ts());
  EXPECT_FALSE(r.has_dn_exp_ts());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadMpuBlockEvictDeny) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kPersisted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_dn_uuid("datanode-1");
  bip1.set_curr_upload_id("upload_id_1");
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_49M);
  replica->set_dn_uuid("datanode-1");

  BIPLockComponents blk_lck_comps;
  BlockInfoProto r;
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(2)
      .WillRepeatedly(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{**********, block_size_128M, 1001},
                                "datanode-1",
                                "object/123",
                                "upload_id_1",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::UPLOAD);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  auto& dbip1 = bip_write_manager_->TestOnlyGetBlock(**********);
  dbip1.Get().set_etag("etag1");
  blk_lck_comps.Reset();

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_FALSE(bip_write_manager_
                   ->UploadBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 &upload_cmd,
                                 &ne_cmd)
                   .IsOK());

  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadMpuBlockEvict) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStatePersisted);

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kPersisted);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::UPLOAD);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  EXPECT_FALSE(bip_write_manager_
                   ->UploadBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 &upload_cmd,
                                 &ne_cmd)
                   .IsOK());

  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_EQ(ne_cmd.block().numbytes(), block_size_128M);
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, UploadAppendBlock) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
  ufs_info->set_key("object/123");

  BIPLockComponents blk_lck_comps;
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  BlockInfoProto r;

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);
  auto b2 = inode.add_blocks();
  b2->set_blockid(1076015590);
  b2->set_genstamp(1002);
  b2->set_numbytes(block_size_128M);

  BlockInfoProto bip1;  // 128M
  bip1.set_state(BlockInfoProto::kComplete);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_upload_type(cloudfs::datanode::APPEND);
  bip1.set_dn_uuid("datanode-1");

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  BlockInfoProto bip2;
  bip2.set_state(BlockInfoProto::kComplete);
  bip2.set_block_id(1076015590);
  bip2.set_gen_stamp(1002);
  bip2.set_num_bytes(block_size_128M);
  bip2.set_inode_id(17390);
  bip2.set_expected_rep(1);
  bip2.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip2.set_upload_type(cloudfs::datanode::APPEND);
  bip2.set_key_block(true);
  bip2.set_pufs_name("object/123");
  bip2.set_pufs_offset(block_size_128M);
  bip2.set_dn_uuid("datanode-1");

  ReplicaInfoProto* replica2 = bip2.add_replicas();
  replica2->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica2->set_reporter(ReplicaInfoProto::kDatanode);
  replica2->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica2->set_gen_stamp(1002);
  replica2->set_num_bytes(block_size_128M);
  replica2->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);

  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{**********, block_size_128M, 1001},
                                "datanode-1",
                                "object/123",
                                "",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());

  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::APPEND);
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_EQ(upload_cmd.appendoffset(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  upload_cmd.Clear();
  ne_cmd.Clear();

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1076015590, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip2), Return(true)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015590}, __FILE__, __LINE__);
  EXPECT_FALSE(bip_write_manager_
                   ->UploadBlock(blk_lck_comps,
                                 Block{1076015590, block_size_128M, 1002},
                                 "datanode-1",
                                 "object/123",
                                 "",
                                 &upload_cmd,
                                 &ne_cmd)
                   .IsOK());
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
  blk_lck_comps.Reset();

  bip1.set_state(BlockInfoProto::kPersisted);
  upload_cmd.Clear();
  ne_cmd.Clear();

  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{1076015590}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->UploadBlock(blk_lck_comps,
                                Block{1076015590, block_size_128M, 1002},
                                "datanode-1",
                                "object/123",
                                "",
                                &upload_cmd,
                                &ne_cmd)
                  .IsOK());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.dnuuid(), "datanode-1");
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::APPEND);
  EXPECT_EQ(upload_cmd.block().blockid(), 1076015590);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1002);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/123");
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_EQ(upload_cmd.appendoffset(), block_size_128M);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, 1076015590, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kUploadIssued);
  EXPECT_EQ(r.block_id(), 1076015590);
  EXPECT_EQ(r.gen_stamp(), 1002);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

class DirtyBlockInfoProtoManagerPersistBlockAccTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    default_namespate_type = FLAGS_namespace_type;
    FLAGS_namespace_type = cloudfs::NamespaceType::ACC_TOS;
    DirtyBlockInfoProtoManagerTest::SetUp();

    block_size_49M = 50000000UL - 1;
    block_size_99M = 100000000UL - 1;
    block_size_128M = 128UL * 1024 * 1024;

    inode.set_id(17390);
    inode.set_parent_id(16385);
    inode.set_name("123");
  }

 protected:
  int32_t default_namespate_type;
  uint32_t block_size_49M;
  uint32_t block_size_99M;
  uint32_t block_size_128M;

  INode inode;
};

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, DnUuidIsEmpty) {
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "dn_uuid is empty");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Found missing B********** when PersistBlock");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, BlockFromDnIsNotEqual) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUploadIssued);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1002},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to compare block "
            "{id:**********,gs:1001,num_bytes:134217728} with "
            "wrong replica{id:**********,gs:1002,num_bytes:134217728}");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, BlockIsCommitted) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kCommitted);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to persist non-complete B**********");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest,
       PersistBlockWhichIsUploadedByAnotherDN) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-2",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-2} persists B********** "
            "uploaded by DN{uuid:datanode-1}");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, PersistNonKeyBlock) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(
      s.message(),
      "Upload success from DN{uuid:datanode-1} for non-key block B**********");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest,
       PersistBlockWithMismatcedPufsNameAndUploadId) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  bip.set_key_block(true);
  bip.set_pufs_name("object/123");
  bip.set_curr_upload_id("upload_id_1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  // By Design: Rename.
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/124",
                                 "upload_id_2",
                                 "etag",
                                 &upload_cmd,
                                 &ne_cmd)
                  .IsOK());
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, PersistBlockWithoutEtag) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  bip.set_key_block(true);
  bip.set_pufs_name("object/123");
  bip.set_curr_upload_id("upload_id_1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       absl::optional<std::string>(),
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "MPU does not have etag from DN{uuid:datanode-1} for B**********");
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest,
       DoNotEvictReplicaIfIFileIsMissing) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kPersisted);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  bip.set_key_block(true);
  bip.set_pufs_name("object/123");
  bip.set_curr_upload_id("upload_id_1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(Return(StatusCode::kFileNotFound));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not issue evict request to DN{uuid:datanode-1} due to "
            "INode{id:17390} is not found for B**********");
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest,
       DoNotEvictReplicaIfFileIsNotPersisted) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kPersisted);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  bip.set_key_block(true);
  bip.set_etag("etag");
  bip.set_pufs_name("object/123");
  bip.set_curr_upload_id("upload_id_1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  EXPECT_CALL(*meta_storage_, GetINode(17390, testing::_, testing::_))
      .Times(1)
      .WillOnce(Return(StatusCode::kOK));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Do not issue evict to DN{uuid:datanode-1} for B********** "
            "due to file is not persisted");
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest,
       EvictReplicaOfPersistedAppendBlock) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kPersisted);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_upload_type(cloudfs::datanode::APPEND);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, PersistDeprecatedBlock) {
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kDeprecated);
  bip.set_block_id(**********);
  bip.set_gen_stamp(1001);
  bip.set_num_bytes(block_size_128M);
  bip.set_inode_id(17390);
  bip.set_expected_rep(1);
  bip.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip.set_dn_uuid("datanode-1");
  bip.set_key_block(true);
  bip.set_pufs_name("object/123");
  bip.set_curr_upload_id("upload_id_1");
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  Status s =
      bip_write_manager_->PersistBlock(blk_lck_comps,
                                       Block{**********, block_size_128M, 1001},
                                       "datanode-1",
                                       "object/123",
                                       "upload_id_1",
                                       "etag",
                                       &upload_cmd,
                                       &ne_cmd);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "DN{uuid:datanode-1} tries to persist B********** "
            "with inconsistent state 6");
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
}

TEST_F(DirtyBlockInfoProtoManagerPersistBlockAccTest, PersistMpuBlock) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeNormal);
  ufs_info->set_upload_id("upload_id_1");
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStateToBePersisted);

  BIPLockComponents blk_lck_comps;
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  BlockInfoProto r;

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kUploadIssued);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_key_block(true);
  bip1.set_bundle_length(0);
  bip1.set_bundle_offset(0);
  bip1.set_part_num(1);
  bip1.set_pufs_name("object/123");
  bip1.set_curr_upload_id("upload_id_1");
  bip1.set_pufs_offset(0);
  bip1.set_dn_uuid("datanode-1");
  bip1.set_upload_issued_times(1);

  ReplicaInfoProto* replica = bip1.add_replicas();
  replica->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica->set_reporter(ReplicaInfoProto::kDatanode);
  replica->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica->set_gen_stamp(1001);
  replica->set_num_bytes(block_size_128M);
  replica->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 "etag",
                                 &upload_cmd,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.upload_issued_times(), 1);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_1");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.etag(), "etag");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  ufs_info->set_key("object/1234");
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  auto& dbip = bip_write_manager_->TestOnlyGetBlock(**********);
  dbip.Get().set_pufs_name("object/1234");
  dbip.Get().set_curr_upload_id("upload_id_2");
  blk_lck_comps.Reset();

  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "upload_id_1",
                                 "etag",
                                 &upload_cmd,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  EXPECT_EQ(upload_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(upload_cmd.block().genstamp(), 1001);
  EXPECT_EQ(upload_cmd.uploadtype(), cloudfs::datanode::UPLOAD);
  EXPECT_EQ(upload_cmd.blockpufsname(), "object/1234");
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_2");
  EXPECT_EQ(upload_cmd.prevblocks_size(), 0);
  EXPECT_EQ(upload_cmd.expts(), ********** + FLAGS_min_upload_timeout_s * 2);
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********001);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/1234");
  EXPECT_EQ(r.upload_issued_times(), 2);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_2");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.etag(), "etag");
  EXPECT_EQ(r.nn_exp_ts(), r.dn_exp_ts() + FLAGS_nn_dn_clock_drift_s);
  EXPECT_EQ(r.dn_exp_ts(), ********** + FLAGS_min_upload_timeout_s * 2);
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/1234",
                                 "upload_id_2",
                                 "etag2",
                                 &upload_cmd,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_FALSE(ne_cmd.IsInitialized());
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********002);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/1234");
  EXPECT_EQ(r.upload_issued_times(), 2);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_2");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.etag(), "etag2");
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
  blk_lck_comps.Reset();

  ufs_info->set_file_state(UfsFileState::kUfsFileStatePersisted);
  upload_cmd.Clear();
  ne_cmd.Clear();
  EXPECT_CALL(*meta_storage_, GetINode(17390, _, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(inode), Return(kOK)));

  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_FALSE(bip_write_manager_
                   ->PersistBlock(blk_lck_comps,
                                  Block{**********, block_size_128M, 1001},
                                  "datanode-1",
                                  "object/1234",
                                  "upload_id_2",
                                  "etag2",
                                  &upload_cmd,
                                  &ne_cmd)
                   .IsOK());
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  r = bip_write_manager_->TestOnlyGetBlock(**********).Get();
  EXPECT_EQ(r.version(), **********002);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/1234");
  EXPECT_EQ(r.upload_issued_times(), 2);
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.curr_upload_id(), "upload_id_2");
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_EQ(r.etag(), "etag2");
}

TEST_F(DirtyBlockInfoProtoManagerUploadBlockAccTest, PersistAppendBlock) {
  auto ufs_info = inode.mutable_ufs_file_info();
  ufs_info->set_create_type(UfsFileCreateType::kUfsFileCreateTypeAppend);
  ufs_info->set_key("object/123");
  ufs_info->set_file_state(UfsFileState::kUfsFileStatePersisted);

  BIPLockComponents blk_lck_comps;
  cloudfs::datanode::UploadCommandProto upload_cmd;
  cloudfs::datanode::NotifyEvictableCommandProto ne_cmd;
  BlockInfoProto r;

  auto b1 = inode.add_blocks();
  b1->set_blockid(**********);
  b1->set_genstamp(1001);
  b1->set_numbytes(block_size_128M);

  BlockInfoProto bip1;
  bip1.set_state(BlockInfoProto::kUploadIssued);
  bip1.set_block_id(**********);
  bip1.set_gen_stamp(1001);
  bip1.set_num_bytes(block_size_128M);
  bip1.set_inode_id(17390);
  bip1.set_expected_rep(1);
  bip1.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
  bip1.set_upload_type(cloudfs::datanode::APPEND);
  bip1.set_key_block(true);
  bip1.set_pufs_name("object/123");
  bip1.set_pufs_offset(0);
  bip1.set_dn_uuid("datanode-1");

  ReplicaInfoProto* replica1 = bip1.add_replicas();
  replica1->set_report_ts(FLAGS_block_report_hard_limit_ms / 1000 + **********);
  replica1->set_reporter(ReplicaInfoProto::kDatanode);
  replica1->set_state(cloudfs::ReplicaStateProto::FINALIZED);
  replica1->set_gen_stamp(1001);
  replica1->set_num_bytes(block_size_128M);
  replica1->set_dn_uuid("datanode-1");

  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip1), Return(true)));

  upload_cmd.Clear();
  ne_cmd.Clear();
  blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  EXPECT_TRUE(bip_write_manager_
                  ->PersistBlock(blk_lck_comps,
                                 Block{**********, block_size_128M, 1001},
                                 "datanode-1",
                                 "object/123",
                                 "",
                                 "",
                                 &upload_cmd,
                                 &ne_cmd)
                  .IsOK());
  EXPECT_FALSE(upload_cmd.IsInitialized());
  EXPECT_TRUE(ne_cmd.IsInitialized());
  EXPECT_EQ(ne_cmd.blockpoolid(), "bp-1");
  EXPECT_EQ(ne_cmd.block().blockid(), **********);
  EXPECT_EQ(ne_cmd.block().numbytes(), block_size_128M);
  EXPECT_EQ(ne_cmd.block().genstamp(), 1001);
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_EQ(r.version(), **********000);
  EXPECT_EQ(r.state(), BlockInfoProto::kPersisted);
  EXPECT_EQ(r.block_id(), **********);
  EXPECT_EQ(r.gen_stamp(), 1001);
  EXPECT_EQ(r.num_bytes(), block_size_128M);
  EXPECT_EQ(r.pufs_name(), "object/123");
  EXPECT_EQ(r.aborted_upload_ids_size(), 0);
  EXPECT_EQ(r.dn_uuid(), "datanode-1");
  EXPECT_FALSE(r.has_etag());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

class DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest
    : public DirtyBlockInfoProtoManagerTest {
 public:
  void SetUp() override {
    DirtyBlockInfoProtoManagerTest::SetUp();

    bip_.set_state(BlockInfoProto::kUploadIssued);
    bip_.set_block_id(**********);
    bip_.set_gen_stamp(1001);
    bip_.set_num_bytes(1);
    bip_.set_inode_id(17390);
    bip_.set_expected_rep(1);
    bip_.set_type(BlockInfoProto::Type::BlockInfoProto_Type_kACCBlock);
    bip_.set_key_block(true);
    bip_.set_etag("etag1");
    bip_.set_bundle_length(0);
    bip_.set_bundle_offset(0);
    bip_.set_part_num(1);
    bip_.set_pufs_name("object/123");
    bip_.set_curr_upload_id("upload_id_1");
    bip_.set_pufs_offset(0);
    bip_.set_dn_uuid("datanode-1");
    bip_.set_upload_issued_times(1);
  }

 protected:
  BlockInfoProto bip_;
};

TEST_F(DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest,
       BlockIsMissing) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(Return(false));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CheckAccPersistedBlocks(
      blk_lck_comps, **********, "object/123", "upload_id_1", nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "Found missing penult B********** "
            "when CompletePenultBlkAndCommitLastBlk");
}

TEST_F(DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest, NonKeyBlock) {
  bip_.set_key_block(false);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CheckAccPersistedBlocks(
      blk_lck_comps, **********, "object/123", "upload_id_1", nullptr);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "NonKeyBlock");
}

TEST_F(DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest,
       BlockHasBeenUploadedWithCorrectUploadId) {
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  Status s = bip_write_manager_->CheckAccPersistedBlocks(
      blk_lck_comps, **********, "object/123", "upload_id_1", nullptr);
  EXPECT_TRUE(s.IsOK());
  EXPECT_EQ(s.message(), "HasBeenUploaded");
}

TEST_F(DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest, Retry) {
  bip_.clear_etag();
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  Status s = bip_write_manager_->CheckAccPersistedBlocks(
      blk_lck_comps, **********, "object/123", "upload_id_1", &upload_cmd);
  EXPECT_EQ(s.code(), Code::kIsRetry);
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
}

TEST_F(DirtyBlockInfoProtoManagerCheckAccPersistedBlocksAccTest,
       UploadIdOrPufsNameIsMismatched) {
  EXPECT_CALL(*time_util_, GetNowEpochMs())
      .Times(AtLeast(1))
      .WillRepeatedly(Return(**********000));
  EXPECT_CALL(*meta_storage_, GetBlockInfo(**********, _))
      .Times(1)
      .WillOnce(DoAll(SetArgPointee<1>(bip_), Return(true)));
  auto blk_lck_comps = bip_write_manager_->Lock(
      std::set<BlockID>{**********}, __FILE__, __LINE__);
  cloudfs::datanode::UploadCommandProto upload_cmd;
  Status s = bip_write_manager_->CheckAccPersistedBlocks(
      blk_lck_comps, **********, "object/124", "upload_id_2", &upload_cmd);
  EXPECT_TRUE(s.IsFalse());
  EXPECT_TRUE(upload_cmd.IsInitialized());
  EXPECT_EQ(upload_cmd.block().blockid(), **********);
  BlockInfoProto r;
  EXPECT_TRUE(
      bip_write_manager_->PreCommit(blk_lck_comps, **********, &r).IsOK());
  EXPECT_TRUE(bip_write_manager_->PostCommit(blk_lck_comps, r).IsOK());
}

}  // namespace dancenn
