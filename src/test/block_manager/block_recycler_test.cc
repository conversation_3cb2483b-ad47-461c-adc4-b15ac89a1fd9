// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/12/05
// Description

#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <memory>
#include <string>

// GMock matchers need this.
#define private public
#include "proto/generated/dancenn/block_info_proto.pb.h"
#undef public

#include "base/file_utils.h"
#include "base/java_exceptions.h"
#include "base/vlock.h"
#include "block_manager/bip_write_manager.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_recycler.h"
#include "edit/sender_base.h"
#include "ha/operations.h"
#include "namespace/meta_storage.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "test/block_manager/gmock_block_manager.h"
#include "test/gmock_edit_log_sender.h"
#include "test/gmock_ha_state.h"

using testing::AllOf;
using testing::AtLeast;
using testing::ByMove;
using testing::Contains;
using testing::ElementsAre;
using testing::Field;
using testing::InSequence;
using testing::Invoke;
using testing::Return;
using testing::SizeIs;

DECLARE_int32(ongoing_op_del_depring_blks_max_size);
DECLARE_int32(del_depring_blks_batch_size);
DECLARE_int32(del_depred_blks_batch_size);
DECLARE_int32(cached_invalidate_pufs_cmd_max_size);
DECLARE_int32(invalidate_pufs_cmd_batch_size);
DECLARE_int32(wait_age_before_seek_depred_blks_iter_to_first);
DECLARE_int32(ongoing_op_del_depred_blks_max_size);
DECLARE_int32(op_del_depred_blks_batch_size);
DECLARE_int32(not_full_op_del_depred_blks_mature_age);

namespace dancenn {

class BlockRecyclerTest : public testing::Test {
 public:
  void SetUp() override {
    gmock_edit_log_sender_ = new GMockEditLogSender();
    db_path_ = "rocksdb_XXXXXX";
    ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    meta_storage_.reset(new MetaStorage(db_path_));
    meta_storage_->Launch();
    meta_storage_->StartActive();
    meta_storage_->ResetLastAppliedTxId(0);
    gmock_block_manager_.SetMetaStorage(meta_storage_);
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

 protected:
  GMockHAState ha_state_;
  testing::StrictMock<GMockBlockManager> gmock_block_manager_;
  std::unique_ptr<BIPWriteManager> bip_write_manager_;
  GMockEditLogSender* gmock_edit_log_sender_;
  std::string db_path_;
  std::shared_ptr<MetaStorage> meta_storage_;
};

class GMockDepringBlockRecycler : public DepringBlockRecycler {
 public:
  GMockDepringBlockRecycler(HAStateBase* ha_state,
                            BlockManager* block_manager,
                            std::shared_ptr<MetaStorage> meta_storage)
      : DepringBlockRecycler(ha_state, block_manager, nullptr, meta_storage) {
  }

  MOCK_METHOD0(CreateEditLogSender, std::unique_ptr<EditLogSenderBase>());
};

class DepringBlockRecyclerTest : public BlockRecyclerTest {
 public:
  void SetUp() override {
    BlockRecyclerTest::SetUp();

    recycler_.reset(new GMockDepringBlockRecycler(
        &ha_state_, &gmock_block_manager_, meta_storage_));

    bip_.set_state(BlockInfoProto::kComplete);
    bip_.set_block_id(10240);
    bip_.set_gen_stamp(1000);
    bip_.set_num_bytes(4096);
    bip_.set_inode_id(kLastReservedINodeId + 1);
    bip_.set_expected_rep(2);
  }

 protected:
  std::unique_ptr<GMockDepringBlockRecycler> recycler_;
  BlockInfoProto bip_;
};

TEST_F(DepringBlockRecyclerTest, DontRecycle) {
  EXPECT_FALSE(recycler_->Recycle());

  EXPECT_CALL(*recycler_, CreateEditLogSender())
      .Times(1)
      .WillOnce(Return(
          ByMove(std::unique_ptr<EditLogSenderBase>(gmock_edit_log_sender_))));
  recycler_->StartActive(bip_write_manager_.get());

  meta_storage_->TestOnlyPutBlockInfo(bip_, true);
  EXPECT_CALL(ha_state_, CheckOperation(testing::_))
      .Times(2)
      .WillOnce(Return(ByMove(
          std::make_pair(Status(JavaExceptions::Exception::kStandbyException),
                         dancenn::vshared_lock()))))
      .WillOnce(Return(ByMove(std::make_pair(
          Status(), dancenn::vshared_lock(ha_state_.barrier_.lock())))));
  EXPECT_FALSE(recycler_->Recycle());

  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogDelDepringBlks(Field(
          &DepringBlksToBeDel::depred_bips_,
          ElementsAre(Field(&BlockInfoProto::block_id_, bip_.block_id())))))
      .Times(1)
      .WillOnce(Return(1));
  EXPECT_TRUE(recycler_->Recycle());
}

TEST_F(DepringBlockRecyclerTest, Recycle) {
  EXPECT_CALL(*recycler_, CreateEditLogSender())
      .Times(1)
      .WillOnce(Return(
          ByMove(std::unique_ptr<EditLogSenderBase>(gmock_edit_log_sender_))));
  EXPECT_CALL(ha_state_, CheckOperation(testing::_))
      .Times(AtLeast(1))
      .WillRepeatedly(Invoke([this](OperationsCategory _) {
        return std::make_pair(Status(),
                              dancenn::vshared_lock(ha_state_.barrier_.lock()));
      }));
  recycler_->StartActive(bip_write_manager_.get());

  // InSequence s;
  FLAGS_del_depring_blks_batch_size = 2;
  FLAGS_ongoing_op_del_depring_blks_max_size = 2;
  auto blk_id = bip_.block_id();
  for (int i = 0; i < 6; i++) {
    BlockInfoProto bip = bip_;
    bip.set_block_id(blk_id + i);
    meta_storage_->TestOnlyPutBlockInfo(bip, true);
  }

  // 1. Don't recycle block if cannot acquire in transaction lock.
  {
    auto& s = recycler_->TestOnlySlice(blk_id);
    BlockInfoGuard bi_guard(s.get(), blk_id, true);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_TRUE(bi->TryLockInTransaction(__FILE__, __LINE__));
  }
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepringBlks(Field(
                  &DepringBlksToBeDel::depred_bips_,
                  ElementsAre(Field(&BlockInfoProto::block_id_, blk_id + 1),
                              Field(&BlockInfoProto::block_id_, blk_id + 2)))))
      .Times(1)
      .WillOnce(Return(1));
  EXPECT_CALL(gmock_block_manager_,
              RemoveBlocksAndUpdateSafeMode(
                  ElementsAre(Field(&BlockProto::blockid_, blk_id + 1),
                              Field(&BlockProto::blockid_, blk_id + 2))))
      .Times(1);
  EXPECT_TRUE(recycler_->Recycle());

  // 2. Recycle the next 2 deprecating blocks.
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepringBlks(Field(
                  &DepringBlksToBeDel::depred_bips_,
                  ElementsAre(Field(&BlockInfoProto::block_id_, blk_id + 3),
                              Field(&BlockInfoProto::block_id_, blk_id + 4)))))
      .Times(1)
      .WillOnce(Return(2));
  EXPECT_CALL(gmock_block_manager_,
              RemoveBlocksAndUpdateSafeMode(
                  ElementsAre(Field(&BlockProto::blockid_, blk_id + 3),
                              Field(&BlockProto::blockid_, blk_id + 4))))
      .Times(1);
  EXPECT_TRUE(recycler_->Recycle());

  // 3. Don't recycle because there are too many ongoing ops.
  EXPECT_FALSE(recycler_->Recycle());

  // 4. Recycle after cleaning buffer.
  meta_storage_->TxFinish(1, 2);
  meta_storage_->WaitNoPending();
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepringBlks(Field(
                  &DepringBlksToBeDel::depred_bips_,
                  ElementsAre(Field(&BlockInfoProto::block_id_, blk_id + 5)))))
      .Times(1)
      .WillOnce(Return(3));
  EXPECT_CALL(gmock_block_manager_,
              RemoveBlocksAndUpdateSafeMode(
                  ElementsAre(Field(&BlockProto::blockid_, blk_id + 5))))
      .Times(1);
  EXPECT_TRUE(recycler_->Recycle());

  // 5. Don't seek to first because buffer is not empty.
  {
    auto& s = recycler_->TestOnlySlice(blk_id);
    BlockInfoGuard bi_guard(s.get(), blk_id, false);
    BlockInfo* bi = bi_guard.GetBlockInfo();
    EXPECT_TRUE(bi->TryUnlockInTransaction(__FILE__, __LINE__));
  }
  EXPECT_FALSE(recycler_->Recycle());

  // 6. Seek to first and recycle the first 1 block after buffer is empty.
  meta_storage_->TxFinish(3, 1);
  meta_storage_->WaitNoPending();
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepringBlks(Field(
                  &DepringBlksToBeDel::depred_bips_,
                  ElementsAre(Field(&BlockInfoProto::block_id_, blk_id)))))
      .Times(1)
      .WillOnce(Return(4));
  EXPECT_CALL(gmock_block_manager_,
              RemoveBlocksAndUpdateSafeMode(
                  ElementsAre(Field(&BlockProto::blockid_, blk_id))))
      .Times(1);
  EXPECT_TRUE(recycler_->Recycle());
  meta_storage_->TxFinish(4, 1);
  meta_storage_->WaitNoPending();

  // 7. All deprecating blocks is moved to deprecated cf.
  {
    auto it = meta_storage_->GetDeprecatingBlkIterator();
    it->SeekToFirst();
    EXPECT_TRUE(it->status().ok());
    EXPECT_FALSE(it->Valid());
  }
  {
    auto it = meta_storage_->GetDeprecatedBlkIterator();
    it->SeekToFirst();
    for (int i = 0; i < 6; i++) {
      EXPECT_EQ(recycler_->DecodeBlockID(it->key()), blk_id + i);
      BlockInfoProto bip;
      EXPECT_TRUE(meta_storage_->GetBlockInfo(blk_id + i, &bip));
      EXPECT_EQ(bip.state(), BlockInfoProto::kDeprecated);
      EXPECT_EQ(bip.block_id(), blk_id + i);
      it->Next();
    }
    EXPECT_TRUE(it->status().ok());
    EXPECT_FALSE(it->Valid());
  }

  // 8. Workers release barrier, so I can get it.
  dancenn::vunique_lock(ha_state_.barrier_.lock());
}

class GMockDepredBlockRecycler : public DepredBlockRecycler {
 public:
  GMockDepredBlockRecycler(HAStateBase* ha_state,
                           BlockManager* block_manager,
                           std::shared_ptr<MetaStorage> meta_storage)
      : DepredBlockRecycler(ha_state, block_manager, nullptr, meta_storage) {
  }

  MOCK_METHOD0(CreateEditLogSender, std::unique_ptr<EditLogSenderBase>());
};

class DepredBlockRecyclerTest : public BlockRecyclerTest {
 public:
  void SetUp() override {
    BlockRecyclerTest::SetUp();

    blockpool_id_ = "bp_1";
    recycler_.reset(new GMockDepredBlockRecycler(
        &ha_state_, &gmock_block_manager_, meta_storage_));

    non_uploaded_bip_.set_state(BlockInfoProto::kDeprecated);
    non_uploaded_bip_.set_block_id(10240);
    non_uploaded_bip_.set_gen_stamp(1000);
    non_uploaded_bip_.set_num_bytes(4096);
    non_uploaded_bip_.set_inode_id(kLastReservedINodeId + 1);
    non_uploaded_bip_.set_expected_rep(2);

    uploaded_bip_ = non_uploaded_bip_;
    uploaded_bip_.set_block_id(40960);
    uploaded_bip_.set_pufs_name("1025.block");
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

 protected:
  std::string blockpool_id_;
  std::unique_ptr<GMockDepredBlockRecycler> recycler_;

  BlockInfoProto non_uploaded_bip_;
  BlockInfoProto uploaded_bip_;
};

TEST_F(DepredBlockRecyclerTest, DontRecycle) {
  EXPECT_FALSE(recycler_->Recycle());

  FLAGS_wait_age_before_seek_depred_blks_iter_to_first = 1;
  EXPECT_CALL(*recycler_, CreateEditLogSender())
      .Times(1)
      .WillOnce(Return(
          ByMove(std::unique_ptr<EditLogSenderBase>(gmock_edit_log_sender_))));
  recycler_->StartActive(bip_write_manager_.get());
  EXPECT_FALSE(recycler_->Recycle());

  FLAGS_del_depred_blks_batch_size = 1;
  meta_storage_->TestOnlyPutBlockInfo(non_uploaded_bip_, false);
  EXPECT_TRUE(recycler_->Recycle());
}

TEST_F(DepredBlockRecyclerTest, RecycleUploadedBlks) {
  EXPECT_CALL(*recycler_, CreateEditLogSender())
      .Times(1)
      .WillOnce(Return(
          ByMove(std::unique_ptr<EditLogSenderBase>(gmock_edit_log_sender_))));
  recycler_->StartActive(bip_write_manager_.get());

  // 1. Recycle the first 16 deprecated blocks.
  FLAGS_del_depred_blks_batch_size = 10;
  FLAGS_cached_invalidate_pufs_cmd_max_size = 2;
  FLAGS_invalidate_pufs_cmd_batch_size = 8;
  for (int i = 0; i < 20; i++) {
    BlockInfoProto bip = uploaded_bip_;
    bip.set_block_id(bip.block_id() + i);
    meta_storage_->TestOnlyPutBlockInfo(bip, false);
  }
  EXPECT_TRUE(recycler_->Recycle());
  EXPECT_TRUE(recycler_->Recycle());

  // 2. Don't recycle because buffer is full.
  EXPECT_FALSE(recycler_->Recycle());

  // 3. Recycle after cleaning buffer.
  cloudfs::datanode::HeartbeatResponseProto response;
  auto type = cloudfs::datanode::DatanodeCommandProto::InvalidatePufsCommand;
  for (int i = 0; i < 2; i++) {
    response.Clear();
    EXPECT_TRUE(recycler_->GetInvalidatePufsCmds(&response));
    EXPECT_EQ(response.cmds_size(), 1);
    EXPECT_EQ(response.cmds(0).cmdtype(), type);
    EXPECT_EQ(response.cmds(0).invalidatepufscmd().blocks_size(),
              FLAGS_invalidate_pufs_cmd_batch_size);
    auto cmd = response.cmds(0).invalidatepufscmd();
    for (int j = 0; j < FLAGS_invalidate_pufs_cmd_batch_size; j++) {
      EXPECT_EQ(cmd.blocks(j).blockid(),
                uploaded_bip_.block_id() +
                    i * FLAGS_invalidate_pufs_cmd_batch_size + j);
    }
  }
  // Recycle the next 4 deprecated blocks.
  EXPECT_TRUE(recycler_->Recycle());

  // 4. Don't seek to first because buffer is not empty.
  EXPECT_FALSE(recycler_->Recycle());

  // 5. Recycle again after cleaning buffer.
  response.Clear();
  EXPECT_FALSE(recycler_->Recycle());
  EXPECT_TRUE(recycler_->GetInvalidatePufsCmds(&response));
  EXPECT_EQ(response.cmds_size(), 1);
  EXPECT_EQ(response.cmds(0).cmdtype(), type);
  auto cmd = response.cmds(0).invalidatepufscmd();
  EXPECT_EQ(cmd.blocks_size(), 4);
  EXPECT_EQ(cmd.blocks(0).blockid(), uploaded_bip_.block_id() + 16);
  // Recycle the first 10 deprecated blocks again.
  response.Clear();
  FLAGS_wait_age_before_seek_depred_blks_iter_to_first = 1;
  EXPECT_FALSE(recycler_->GetInvalidatePufsCmds(&response));
  EXPECT_TRUE(recycler_->Recycle());
  EXPECT_TRUE(recycler_->GetInvalidatePufsCmds(&response));
  EXPECT_EQ(response.cmds_size(), 1);
  EXPECT_EQ(response.cmds(0).cmdtype(), type);
  cmd = response.cmds(0).invalidatepufscmd();
  EXPECT_EQ(cmd.blocks_size(), FLAGS_invalidate_pufs_cmd_batch_size);
  EXPECT_EQ(cmd.blocks(0).blockid(), uploaded_bip_.block_id());
}

TEST_F(DepredBlockRecyclerTest, RecycleNotUploadedBlks) {
  EXPECT_CALL(*recycler_, CreateEditLogSender())
      .Times(1)
      .WillOnce(Return(
          ByMove(std::unique_ptr<EditLogSenderBase>(gmock_edit_log_sender_))));
  EXPECT_CALL(ha_state_, CheckOperation(testing::_))
      .Times(AtLeast(1))
      .WillRepeatedly(Invoke([this](OperationsCategory _) {
        return std::make_pair(Status(),
                              dancenn::vshared_lock(ha_state_.barrier_.lock()));
      }));
  recycler_->StartActive(bip_write_manager_.get());

  // 1. Recycle the first 16 deprecated blocks.
  FLAGS_del_depred_blks_batch_size = 10;
  FLAGS_ongoing_op_del_depred_blks_max_size = 2;
  FLAGS_op_del_depred_blks_batch_size = 8;
  for (int i = 0; i < 20; i++) {
    BlockInfoProto bip = non_uploaded_bip_;
    bip.set_block_id(bip.block_id() + i);
    meta_storage_->TestOnlyPutBlockInfo(bip, false);
  }
  EXPECT_TRUE(recycler_->Recycle());
  EXPECT_TRUE(recycler_->Recycle());

  // 2. Don't recycle because buffer is full.
  EXPECT_FALSE(recycler_->Recycle());
  InSequence s;
  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogDelDepredBlks(Field(&DepredBlksToBeDel::blk_ids_,
                             AllOf(SizeIs(FLAGS_op_del_depred_blks_batch_size),
                                   Contains(non_uploaded_bip_.block_id())))))
      .Times(1)
      .WillOnce(Return(1));
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepredBlks(
                  Field(&DepredBlksToBeDel::blk_ids_,
                        AllOf(SizeIs(FLAGS_op_del_depred_blks_batch_size),
                              Contains(non_uploaded_bip_.block_id() +
                                       FLAGS_op_del_depred_blks_batch_size)))))
      .Times(1)
      .WillOnce(Return(2));
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 2);
  EXPECT_FALSE(recycler_->Recycle());

  // 3. Recycle after cleaning buffer.
  meta_storage_->TxFinish(1, 2);
  meta_storage_->WaitNoPending();
  // Recycle the next 4 deprecated blocks.
  EXPECT_TRUE(recycler_->Recycle());

  // 4. Don't seek to first because buffer is not empty.
  FLAGS_wait_age_before_seek_depred_blks_iter_to_first = 1;
  BlockInfoProto bip = non_uploaded_bip_;
  bip.set_block_id(bip.block_id() - 1);
  meta_storage_->TestOnlyPutBlockInfo(bip, false);
  EXPECT_FALSE(recycler_->Recycle());
  EXPECT_FALSE(recycler_->Recycle());

  // 5. Wait not full op become mature.
  FLAGS_not_full_op_del_depred_blks_mature_age = 3;
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 0);
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 0);
  EXPECT_CALL(*gmock_edit_log_sender_,
              LogDelDepredBlks(Field(
                  &DepredBlksToBeDel::blk_ids_,
                  AllOf(SizeIs(4),
                        Contains(non_uploaded_bip_.block_id() +
                                 FLAGS_op_del_depred_blks_batch_size * 2)))))
      .Times(1)
      .WillOnce(Return(3));
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 1);
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 0);

  // 6. Don't recycle because there is not enough time
  //    since the last time it met an invalid iterator.
  meta_storage_->TxFinish(3, 1);
  meta_storage_->WaitNoPending();
  FLAGS_wait_age_before_seek_depred_blks_iter_to_first = 5;
  EXPECT_FALSE(recycler_->Recycle());
  EXPECT_FALSE(recycler_->Recycle());

  // 7. Recycle the first 1 block after timeout.
  EXPECT_TRUE(recycler_->Recycle());
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 0);
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 0);
  EXPECT_CALL(
      *gmock_edit_log_sender_,
      LogDelDepredBlks(Field(&DepredBlksToBeDel::blk_ids_,
                             ElementsAre(non_uploaded_bip_.block_id() - 1))))
      .Times(1)
      .WillOnce(Return(4));
  EXPECT_EQ(recycler_->ConsumeOpDelDepredBlksQueue(), 1);

  // 8. All deprecated blocks is recycled.
  meta_storage_->TxFinish(4, 1);
  meta_storage_->WaitNoPending();
  {
    auto it = meta_storage_->GetDeprecatedBlkIterator();
    it->SeekToFirst();
    EXPECT_TRUE(it->status().ok());
    EXPECT_FALSE(it->Valid());
  }
  for (int i = 0; i < 20; i++) {
    BlockInfoProto bip;
    EXPECT_FALSE(
        meta_storage_->GetBlockInfo(non_uploaded_bip_.block_id() + i, &bip));
  }

  // 9. Workers release barrier, so I can get it.
  dancenn::vunique_lock(ha_state_.barrier_.lock());
}

}  // namespace dancenn
