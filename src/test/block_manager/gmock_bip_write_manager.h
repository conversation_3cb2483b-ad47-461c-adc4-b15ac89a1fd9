// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/05/24
// Description

#ifndef TEST_BLOCK_MANAGER_GMOCK_BIP_WRITE_MANAGER_H_
#define TEST_BLOCK_MANAGER_GMOCK_BIP_WRITE_MANAGER_H_

#include <absl/types/optional.h>             // For optional.
#include <gmock/gmock.h>                     // For MOCK_METHOD3, etc.
#include <google/protobuf/repeated_field.h>  // For RepeatedPtrField.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For CommitBlockSynchronizationRequestProto, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For IoMode, ExtendedBlockProto, BlockProto, RecoveringBlockProto.
#include <proto/generated/dancenn/block_info_proto.pb.h>  // For BlockInfoProto.
#include <proto/generated/dancenn/inode.pb.h>             // For INode.

#include <cstdint>  // For uint32_t, uint64_t.
#include <set>      // For set.
#include <string>   // For string.
#include <vector>   // For vector.

#include "block_manager/bip_write_manager.h"  // For BIPWriteManagerBase, etc.
#include "block_manager/block.h"              // For Block, BlockID.
#include "block_manager/block_info.h"         // For BlockUCState.
#include "namespace/inode.h"                  // For INodeID.

namespace dancenn {

class GMockBIPWriteManager : public BIPWriteManagerBase {
 public:
  MOCK_METHOD3(Lock,
               BIPLockComponents(const std::set<BlockID>& blk_ids,
                                 const char* filename,
                                 uint32_t line_number));
  MOCK_METHOD3(Lock,
               BIPLockComponents(const INode& inode,
                                 const char* filename,
                                 uint32_t line_number));
  MOCK_METHOD3(Lock,
               BIPLockComponents(const std::vector<BlockInfoProto>& bips,
                                 const char* filename,
                                 uint32_t line_number));

  MOCK_METHOD3(PreCommit,
               Status(const BIPLockComponents& blk_lck_comps,
                      BlockID blk_id,
                      BlockInfoProto* bip));
  MOCK_METHOD2(PostCommit,
               Status(const BIPLockComponents& blk_lck_comps,
                      const BlockInfoProto& bip));

  MOCK_METHOD6(AddBlock,
               void(const BIPLockComponents& blk_lck_comps,
                    const Block& blk,
                    const INode& inode,
                    cloudfs::IoMode write_mode,
                    const std::string& pufs_name,
                    const std::vector<std::string>& dn_uuids));
  MOCK_METHOD9(CompletePenultBlkAndCommitLastBlk,
               Status(const BIPLockComponents& blk_lck_comps,
                      const std::string& src,
                      const INode& inode,
                      const Block& penult_blk,
                      const Block& last_blk_from_cli,
                      const Block& last_blk_from_inode,
                      bool complete_last_blk,
                      absl::optional<BlockUCState> force_set_penult_blk_state,
                      absl::optional<BlockUCState> force_set_last_blk_state));
  MOCK_METHOD3(
      UpdatePipeline,
      Status(const BIPLockComponents& blk_lck_comps,
             const cloudfs::ExtendedBlockProto& new_blk_from_cli,
             const google::protobuf::RepeatedPtrField<cloudfs::DatanodeIDProto>&
                 new_nodes));
  MOCK_METHOD2(AbandonBlock,
               Status(const BIPLockComponents& blk_lck_comps, BlockID blk_id));
  MOCK_METHOD3(Fsync,
               Status(const BIPLockComponents& blk_lck_comps,
                      const cloudfs::BlockProto& bp,
                      uint64_t new_num_bytes_from_cli));
  MOCK_METHOD6(ConcatUpdateBlockINodeId,
               Status(const BIPLockComponents& blk_lck_comps,
                      const cloudfs::BlockProto& bp,
                      const INode& target_inode,
                      uint64_t pufs_offset,
                      BlockInfoProto* last_bip,
                      bool is_last));

  MOCK_METHOD3(
      CommitBlockSynchronization,
      Status(const BIPLockComponents& blk_lck_comps,
             const cloudfs::datanode::CommitBlockSynchronizationRequestProto&
                 request,
             absl::optional<BlockUCState> force_set_blk_state));
  MOCK_METHOD3(SetReplication,
               Status(const BIPLockComponents& blk_lck_comps,
                      const BlockProto& bp,
                      uint32_t replica_num));
  MOCK_METHOD3(ReleaseLease,
               Status(const BIPLockComponents& blk_lck_comps,
                      const cloudfs::BlockProto& penult_bp,
                      const cloudfs::BlockProto& last_bp));
  MOCK_METHOD6(InitRecover,
               Status(const BIPLockComponents& blk_lck_comps,
                      const BlockProto& bp,
                      uint64_t recovery_gen_stamp,
                      absl::optional<std::string> force_set_primary_dn_uuid,
                      std::string* primary_dn_uuid,
                      cloudfs::RecoveringBlockProto* recovering_block));

  MOCK_METHOD7(UploadBlock,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk_from_dn,
                      const std::string& dn_uuid,
                      const std::string& pufs_name,
                      const std::string& upload_id,
                      cloudfs::datanode::UploadCommandProto*,
                      cloudfs::datanode::NotifyEvictableCommandProto*));
  MOCK_METHOD8(PersistBlock,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk_from_dn,
                      const std::string& dn_uuid,
                      const std::string& pufs_name,
                      const std::string& upload_id,
                      const absl::optional<std::string>& etag,
                      cloudfs::datanode::UploadCommandProto* upload_cmd,
                      cloudfs::datanode::NotifyEvictableCommandProto*));
  MOCK_METHOD2(DeleteBlock,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk_from_dn));
  MOCK_METHOD3(ReportDeletedReplica,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk,
                      const std::string& dn_uuid));
  MOCK_METHOD4(ReportExistedReplica,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk,
                      const std::string& dn_uuid,
                      cloudfs::ReplicaStateProto state));
  MOCK_METHOD3(ReportBadReplicas,
               Status(const BIPLockComponents& blk_lck_comps,
                      const Block& blk,
                      const std::vector<std::string>& dn_uuids));
};

}  // namespace dancenn

#endif  // TEST_BLOCK_MANAGER_GMOCK_BIP_WRITE_MANAGER_H_
