// Copyright (c) @ 2022.
// All right reserved.
//
// Author: qip<PERSON><PERSON> <<EMAIL>>
// Created: 2022/09/27
// Description
#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/core/http/HttpResponse.h>
#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <map>
#include <memory>
#include <regex>
#include <string>

#include "aws/s3/S3Errors.h"
#include "aws/s3/model/DeleteObjectRequest.h"
#include "aws/s3/model/PutObjectRequest.h"
#include "test/config/ut_config.h"
#include "ufs/tos/tos_checksum_utils.h"

DECLARE_string(tos_endpoint);
DECLARE_string(tos_bucket);
DECLARE_string(tos_access_key_id);
DECLARE_string(tos_secret_access_key);
DECLARE_string(tos_region);

namespace dancenn {

namespace {
const char* kBlockSuffix = ".block";
const char* kCrcSuffix = ".crc";
const int kCrcRecordNum = 8;  // 每个 record 最多保存8 个 checksum
const int kBytesPerChecksum = 512;

}  // namespace
class TosReadBlockCrcTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_tos_endpoint = "https://tos-s3-cn-beijing.volces.com";
    FLAGS_tos_access_key_id = UTConfig::Instance().Ak();
    FLAGS_tos_secret_access_key = UTConfig::Instance().Sk();
    FLAGS_tos_region = "cn-beijing";
    FLAGS_tos_bucket = "cloudfs";
  }

  void TearDown() override {
  }
};

struct TestTosConfig {
  std::string endpoint;
  std::string region;
  std::string bucket;
};

struct TestTosCredentials {
  std::string ak;
  std::string sk;
};

struct TestTosWriteInfo {
  std::string data_key;
  int data_len;
  Aws::Map<Aws::String, Aws::String> meta_data;
};

static std::shared_ptr<Aws::S3::S3Client> TestCreateS3Client(
    const TestTosConfig& tos_config,
    const TestTosCredentials& tos_cred) {
  Aws::Auth::AWSCredentials credentials(tos_cred.ak, tos_cred.sk);

  Aws::Client::ClientConfiguration clientCfg;
  clientCfg.endpointOverride = tos_config.endpoint;
  clientCfg.region = tos_config.region;
  return std::make_shared<Aws::S3::S3Client>(credentials, clientCfg);
}

static void PrepareCrcWriteData(std::shared_ptr<Aws::IOStream> inputData,
                                int data_length) {
  // 只需要关注crc 文件格式:
  // offset in block + length + checksum
  int32_t pos = 0;
  int max_data_length_in_record =
      kCrcRecordNum * kBytesPerChecksum;  // 8 * 512 = 4096
  while (pos < data_length) {
    inputData->write((char*)&pos, sizeof(int32_t));
    int32_t remain_len = data_length - pos < max_data_length_in_record
                             ? data_length - pos
                             : max_data_length_in_record;
    inputData->write((char*)&remain_len, sizeof(int32_t));

    int checksum_num = (remain_len - 1) / kBytesPerChecksum + 1;  // 向上取整
    for (int i = 0; i < checksum_num; ++i) {
      int32_t data_tmp = (int32_t)(i + 1);
      inputData->write((char*)&data_tmp, sizeof(int32_t));
    }
    pos += remain_len;
  }
}

static void PrepareBlockWriteData(std::shared_ptr<Aws::IOStream> inputData,
                                  int data_length) {
  std::string base_data_str = "0123456789";  //  10 个字节长度
  int loop_num = data_length / base_data_str.size();
  for (int i = 0; i < loop_num; ++i) {
    *inputData << base_data_str.c_str();
  }
  for (int i = 0; i < data_length % base_data_str.size(); ++i) {
    *inputData << "a";
  }
}

static Aws::Map<Aws::String, Aws::String> InitMetaData(std::string block_key,
                                                       bool is_crc_enable) {
  Aws::Map<Aws::String, Aws::String> meta_data;
  if (!is_crc_enable) {
    meta_data["crc-enable"] = "false";
    return meta_data;
  }
  meta_data["crc-enable"] = "true";
  meta_data["crc-bytes-per-checksum"] = std::to_string(kBytesPerChecksum);
  std::string crc_key(block_key);
  crc_key.replace(
      crc_key.rfind(kBlockSuffix), sizeof(kBlockSuffix), kCrcSuffix);
  meta_data["crc-path"] = crc_key;
  meta_data["crc-type"] = "CRC32C";
  return meta_data;
}

class TestTosClient {
 public:
  explicit TestTosClient(const TestTosConfig& tos_config,
                         const TestTosCredentials& tos_cred);
  ~TestTosClient() = default;

  void WriteDataToTos(
      const TestTosWriteInfo& write_info,
      std::function<void(std::shared_ptr<Aws::IOStream> inputData,
                         int data_length)> prepare_data_func);

  void ReadAndCheckLengthFromTos(std::string data_key, int expect_length);

  bool ShouldRetry(Aws::Http::HttpResponseCode code) {
    switch (code) {
      case Aws::Http::HttpResponseCode::TOO_MANY_REQUESTS:
      case Aws::Http::HttpResponseCode::INTERNAL_SERVER_ERROR:
        return true;
      default:
        break;
    }
    return false;
  }

  template <class RES>
  Aws::Utils::Outcome<RES, Aws::S3::S3Error> CallS3Api(
      std::function<Aws::Utils::Outcome<RES, Aws::S3::S3Error>()> func,
      const char* api_name) {
    int retry = 0;
    Aws::Utils::Outcome<RES, Aws::S3::S3Error> ret;
    while (true) {
      ret = func();
      if (ret.IsSuccess()) {
        return ret;
      }

      auto&& error = ret.GetError();
      if (ShouldRetry(error.GetResponseCode())) {
        if (retry < 10) {
          ++retry;
          std::this_thread::sleep_for(std::chrono::milliseconds(500));
          continue;
        }
      }
      break;
    }
    return ret;
  }

 private:
  std::shared_ptr<Aws::S3::S3Client> TestGetS3Client();

 private:
  std::shared_ptr<Aws::S3::S3Client> s3client_;
  TestTosConfig tos_config_;
  TestTosCredentials tos_cred_;
};

TestTosClient::TestTosClient(const TestTosConfig& tos_config,
                             const TestTosCredentials& tos_cred)
    : tos_config_(tos_config), tos_cred_(tos_cred) {
  s3client_ = TestCreateS3Client(tos_config, tos_cred);
}

std::shared_ptr<Aws::S3::S3Client> TestTosClient::TestGetS3Client() {
  if (s3client_ == nullptr) {
    s3client_ = TestCreateS3Client(tos_config_, tos_cred_);
  }
  return s3client_;
}

void TestTosClient::WriteDataToTos(
    const TestTosWriteInfo& write_info,
    std::function<void(std::shared_ptr<Aws::IOStream> inputData,
                       int data_length)> prepare_data_func) {
  // 调用删除接口，因为 aws 是标记删除，如果文件不存在也会返回成功
  std::string bucket = tos_config_.bucket;
  std::string key = write_info.data_key;
  Aws::S3::Model::DeleteObjectRequest request;
  request.SetBucket(bucket);
  request.SetKey(key);
  std::function<Aws::S3::Model::DeleteObjectOutcome()> del_func =
      [this, &request]() { return TestGetS3Client()->DeleteObject(request); };

  auto del_ret = CallS3Api(del_func, "DeleteObject");
  if (del_ret.IsSuccess()) {
    std::cout << "DeleteObject success, key: " << key << std::endl;
  } else {
    std::cout << "Unable to DeleteObject, tos bucket : " << bucket
              << " key: " << key << " error " << del_ret.GetError().GetMessage()
              << std::endl;
  }
  EXPECT_TRUE(del_ret.IsSuccess());
  // 准备数据
  const std::shared_ptr<Aws::IOStream> inputData =
      Aws::MakeShared<Aws::StringStream>("");

  prepare_data_func(inputData, write_info.data_len);
  // 开始写入数据
  Aws::S3::Model::PutObjectRequest req;
  req.SetBucket(bucket);
  req.SetBody(inputData);
  req.SetKey(key);
  if (!write_info.meta_data.empty()) {
    req.SetMetadata(write_info.meta_data);
  }

  std::function<Aws::S3::Model::PutObjectOutcome()> put_func = [this, &req]() {
    return TestGetS3Client()->PutObject(req);
  };
  auto put_ret = CallS3Api(put_func, "PutObject");
  if (!put_ret.IsSuccess()) {
    const auto& error = put_ret.GetError();
    std::cout << "put block object failed, bucket: " << bucket
              << ", key: " << key << ", msg: " << error.GetMessage()
              << ", exception: " << error.GetExceptionName()
              << ", code: " << static_cast<int>(error.GetResponseCode())
              << std::endl;
  }
  EXPECT_TRUE(put_ret.IsSuccess());
}

void TestTosClient::ReadAndCheckLengthFromTos(std::string data_key,
                                          int expect_length) {
  auto client = TestGetS3Client();
  int64_t length_from_tos = 0;
  Status ret = dancenn::ObjectStorageChecksumUtils::GetBlockLengthFromTos(
      client.get(), tos_config_.bucket, data_key, &length_from_tos);
  std::cout << "block length read from tos: " << length_from_tos << std::endl;
  EXPECT_TRUE(ret.IsOK());
  EXPECT_EQ((int)length_from_tos, expect_length);
}

TEST_F(TosReadBlockCrcTest, tosWriteAndReadBlockLength) {
  // 只向 tos 写入 block 数据，然后读取其长度, 没有 crc 文件，没有 meta 信息
  // 初始化 client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  std::string block_key = "test/nn_block_crc_test.block";
  // 如果文件存在则删除并重新写入
  int data_len = 1024;
  Aws::Map<Aws::String, Aws::String> meta_data = InitMetaData(block_key, false);
  TestTosWriteInfo write_info{
      .data_key = block_key, .data_len = data_len, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_info, PrepareBlockWriteData);

  // 调用 checksum 接口获取 block 长度
  tos_client->ReadAndCheckLengthFromTos(block_key, data_len);
  Aws::ShutdownAPI(options);
}

TEST_F(TosReadBlockCrcTest, tosWriteAndReadBlockLengthWithMeta) {
  // 只向tos 写入block 数据，然后读取其长度, 没有crc 文件，有meta 信息
  // 初始化client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  // 如果文件存在则删除并重新写入
  std::string block_key = "test/nn_block_crc_test.block";
  int data_len = 4096;
  Aws::Map<Aws::String, Aws::String> meta_data = InitMetaData(block_key, false);

  TestTosWriteInfo write_info{
      .data_key = block_key, .data_len = data_len, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_info, PrepareBlockWriteData);
  // 调用 checksum 接口获取block 长度
  tos_client->ReadAndCheckLengthFromTos(block_key, data_len);

  Aws::ShutdownAPI(options);
}

TEST_F(TosReadBlockCrcTest, tosWriteAndReadBlockLengthWitCrcFile) {
  // 向tos 写入block 和 crc 数据，然后读取其长度
  // 初始化client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  // 如果文件存在则删除并重新写入
  std::string block_key = "test/nn_block_crc_test.block";
  int data_len = 4096;
  int crc_length = 4096 + 1024;
  Aws::Map<Aws::String, Aws::String> meta_data = InitMetaData(block_key, true);
  TestTosWriteInfo write_info{
      .data_key = block_key, .data_len = data_len, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_info, PrepareBlockWriteData);
  // 写入crc
  std::string crc_key = meta_data["crc-path"];
  meta_data.clear();
  TestTosWriteInfo write_crc_info{
      .data_key = crc_key, .data_len = crc_length, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_crc_info, PrepareCrcWriteData);
  // 读取数据并校验长度
  int expect_length = std::min(data_len, crc_length);
  tos_client->ReadAndCheckLengthFromTos(block_key, expect_length);
  Aws::ShutdownAPI(options);
}

TEST_F(TosReadBlockCrcTest, tosWriteAndReadBlockLengthWitCrcFile2) {
  // 向tos 写入block 和 crc 数据，然后读取其长度
  // 初始化client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  // 如果文件存在则删除并重新写入
  std::string block_key = "test/nn_block_crc_test.block";
  int data_len = 10240;
  int crc_length = 8192;
  Aws::Map<Aws::String, Aws::String> meta_data = InitMetaData(block_key, true);
  TestTosWriteInfo write_info{
      .data_key = block_key, .data_len = data_len, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_info, PrepareBlockWriteData);
  // 写入crc
  std::string crc_key = meta_data["crc-path"];
  meta_data.clear();
  TestTosWriteInfo write_crc_info{
      .data_key = crc_key, .data_len = crc_length, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_crc_info, PrepareCrcWriteData);
  // 读取数据并校验长度
  int expect_length = std::min(data_len, crc_length);
  tos_client->ReadAndCheckLengthFromTos(block_key, expect_length);
  Aws::ShutdownAPI(options);
}

TEST_F(TosReadBlockCrcTest, tosWriteAndReadBlockLengthWitCrcFile3) {
  // 向tos 写入block 和 crc 数据，然后读取其长度
  // 初始化client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  // 如果文件存在则删除并重新写入
  std::string block_key = "test/nn_block_crc_test.block";
  int data_len = 20480 + 20480;
  int crc_length = 20480 + 345;
  Aws::Map<Aws::String, Aws::String> meta_data = InitMetaData(block_key, true);
  TestTosWriteInfo write_info{
      .data_key = block_key, .data_len = data_len, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_info, PrepareBlockWriteData);
  // 写入crc
  std::string crc_key = meta_data["crc-path"];
  meta_data.clear();
  TestTosWriteInfo write_crc_info{
      .data_key = crc_key, .data_len = crc_length, .meta_data = meta_data};
  tos_client->WriteDataToTos(write_crc_info, PrepareCrcWriteData);
  // 读取数据并校验长度
  int expect_length = std::min(data_len, crc_length);
  tos_client->ReadAndCheckLengthFromTos(block_key, expect_length);
  Aws::ShutdownAPI(options);
}

TEST_F(TosReadBlockCrcTest, tosReadNoBlockReturnSuccessLengthZero) {
  // 读取一个不存在的文件，返回成功，长度为0
  // 初始化client
  Aws::SDKOptions options;
  Aws::InitAPI(options);
  TestTosConfig tos_config{.endpoint = FLAGS_tos_endpoint,
                           .region = FLAGS_tos_region,
                           .bucket = FLAGS_tos_bucket};
  TestTosCredentials tos_cred{.ak = FLAGS_tos_access_key_id,
                              .sk = FLAGS_tos_secret_access_key};

  std::shared_ptr<TestTosClient> tos_client =
      std::make_shared<TestTosClient>(tos_config, tos_cred);

  // 读取一个不存在的文件
  std::string block_key = "test/nn_block_crc_test_no_exists.block";
  // 读取数据并校验长度
  int expect_length = 0;
  tos_client->ReadAndCheckLengthFromTos(block_key, expect_length);
  Aws::ShutdownAPI(options);
}
}  // namespace dancenn