// Copyright 2021

#include <gtest/gtest.h>

#include <cstring>
#include <iostream>

#include "block_manager/block_info.h"
#include "block_manager/block_info_proto.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"

namespace dancenn {

class BlockInfoTest : public testing::Test {};

TEST_F(BlockInfoTest, Test01) {
  size_t size = sizeof(BlockInfo) + sizeof(DatanodeID) * 3;
  BlockInfo* bi = reinterpret_cast<BlockInfo*>(std::malloc(size));
  memset(bi, 0, size);
  bi->set_capacity(3);
  bi->Init(1,
           0,
           {1024, 1001, 10000000},
           3,
           cloudfs::DATANODE_BLOCK,
           BlockUCState::kUnderConstruction);
  ASSERT_EQ(bi->size(), 0);
  ASSERT_EQ(bi->gs(), 10000000);
  ASSERT_EQ(bi->num_bytes(), 1001);
  ASSERT_EQ(bi->id(), 1024);
  ASSERT_EQ(bi->inode_id(), 1);
  ASSERT_EQ(bi->parent_id(), 0);
  ASSERT_TRUE(!bi->HasBeenComplete());
  ASSERT_FALSE(bi->IsCommitted());
  ASSERT_FALSE(bi->IsComplete());
  bi->UpdateLength(1002);
  ASSERT_EQ(bi->num_bytes(), 1002);
  bi->UpdateGs(10000001);
  ASSERT_EQ(bi->gs(), 10000001);
  ASSERT_TRUE(bi->AddStorage(1));
  ASSERT_EQ(bi->size(), 1);
  ASSERT_TRUE(bi->AddStorage(1));
  ASSERT_EQ(bi->size(), 1);
  ASSERT_TRUE(bi->AddStorage(2));
  ASSERT_EQ(bi->size(), 2);
  ASSERT_EQ(bi->storage_id(0), 1);
  ASSERT_EQ(bi->storage_id(1), 2);
  ASSERT_TRUE(bi->AddStorage(2));
  ASSERT_EQ(bi->size(), 2);
  ASSERT_EQ(bi->IndexOf(2), 1);
  ASSERT_TRUE(bi->RemoveStorage(1));
  ASSERT_EQ(bi->size(), 1);
  ASSERT_EQ(bi->storage_id(0), 2);
  ASSERT_FALSE(bi->RemoveStorage(1));
  ASSERT_EQ(bi->size(), 1);
  ASSERT_EQ(bi->storage_id(0), 2);
  ASSERT_EQ(bi->IndexOf(2), 0);
  bi->ClearStorages();
  ASSERT_EQ(bi->size(), 0);

  ASSERT_TRUE(bi->Commit({1024, 1003, 10000002}));
  ASSERT_FALSE(bi->Commit({1024, 1004, 10000003}));
  ASSERT_EQ(bi->num_bytes(), 1003);
  ASSERT_EQ(bi->gs(), 10000002);
  ASSERT_TRUE(bi->IsCommitted());
  bi->Complete();
  ASSERT_TRUE(bi->IsComplete());
  free(bi);
}

TEST_F(BlockInfoTest, InTransaction) {
  BlockInfo bi;
  EXPECT_TRUE(bi.TryLockInTransaction(__FILE__, __LINE__));
  EXPECT_FALSE(bi.TryLockInTransaction(__FILE__, __LINE__));
  EXPECT_TRUE(bi.TryUnlockInTransaction(__FILE__, __LINE__));
  EXPECT_FALSE(bi.TryUnlockInTransaction(__FILE__, __LINE__));
}

TEST_F(BlockInfoTest, CompareWithBlockInfoProto01) {
  std::size_t size = sizeof(BlockInfo) + sizeof(DatanodeID) * 3;
  BlockInfo* bi = reinterpret_cast<BlockInfo*>(std::malloc(size));
  std::memset(bi, 0, size);
  bi->Init(/*inode_id=*/2,
           /*parent_id=*/1,
           Block(/*id=*/1024, /*len=*/1001, /*gs=*/10000000),
           3,
           cloudfs::DATANODE_BLOCK,
           BlockUCState::kUnderConstruction);
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kUnderConstruction);
  bip.set_block_id(1024);
  bip.set_gen_stamp(10000000);
  bip.set_num_bytes(1001);
  bip.set_inode_id(2);
  EXPECT_EQ(*bi, bip);
  BlockInfoProto bip2 = bip;
  bip2.set_block_id(0);
  EXPECT_NE(*bi, bip2);
  bip2 = bip;
  bip2.set_gen_stamp(0);
  EXPECT_NE(*bi, bip2);
  bip2 = bip;
  bip2.set_num_bytes(0);
  EXPECT_NE(*bi, bip2);
  bip2 = bip;
  bip2.set_inode_id(0);
  EXPECT_NE(*bi, bip2);
  std::free(bi);
}

TEST_F(BlockInfoTest, CompareWithBlockInfoProto02) {
  std::size_t size = sizeof(BlockInfo) + sizeof(DatanodeID) * 3;
  BlockInfo* bi = reinterpret_cast<BlockInfo*>(std::malloc(size));
  std::memset(bi, 0, size);
  bi->Init(/*inode_id=*/2,
           /*parent_id=*/1,
           Block(/*id=*/1024, /*len=*/1001, /*gs=*/10000000),
           3,
           cloudfs::DATANODE_BLOCK,
           BlockUCState::kCommitted);
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kComplete);
  bip.set_block_id(1024);
  bip.set_gen_stamp(10000000);
  bip.set_num_bytes(1001);
  bip.set_inode_id(2);
  EXPECT_EQ(*bi, bip);
  bi->set_uc_state(BlockUCState::kComplete);
  EXPECT_EQ(*bi, bip);
  std::free(bi);
}

TEST_F(BlockInfoTest, IsSafeToRelease) {
  std::size_t size = sizeof(BlockInfo) + sizeof(DatanodeID) * 3;
  BlockInfo* bi = reinterpret_cast<BlockInfo*>(std::malloc(size));
  std::memset(bi, 0, size);
  bi->Init(/*inode_id=*/2,
           /*parent_id=*/1,
           Block(/*id=*/1024, /*len=*/1001, /*gs=*/10000000),
           3,
           cloudfs::DATANODE_BLOCK,
           BlockUCState::kPersisted);
  BlockInfoProto bip;
  bip.set_state(BlockInfoProto::kPersisted);
  bip.set_block_id(1024);
  bip.set_gen_stamp(10000000);
  bip.set_num_bytes(1001);
  bip.set_inode_id(2);
  EXPECT_TRUE(bi->IsSafeToRelease(bip));
  BlockInfoProto bip2 = bip;
  bip2.set_inode_id(0);
  EXPECT_FALSE(bi->IsSafeToRelease(bip2));
  bip2 = bip;
  bi->set_uc_state(BlockUCState::kUnderConstruction);
  bip2.set_state(BlockInfoProto::kUnderConstruction);
  EXPECT_FALSE(bi->IsSafeToRelease(bip2));
}

}  // namespace dancenn
