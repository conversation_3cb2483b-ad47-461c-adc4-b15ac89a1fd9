// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/07/17
// Description

#include <absl/types/optional.h>     // For optional.
#include <cnetpp/base/ip_address.h>  // For IPAddress.
#include <gflags/gflags.h>           // For DECLARE_bool.
#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
#include <gtest/gtest.h>  // For Test, etc.
#include <proto/generated/cloudfs/DatanodeProtocol.pb.h>  // For HeartbeatResponseProto, DatanodeCommandProto, etc.
#include <proto/generated/cloudfs/hdfs.pb.h>  // For IoMode, DatanodeIDProto, RecoveringBlockProto, etc.
#include <proto/generated/dancenn/edit_log.pb.h>         // For BlockInfoProtos.
#include <proto/generated/dancenn/namesystem_info.pb.h>  // For EditLogConf.

#include <memory>  // For unique_ptr.
#include <set>     // For set.
#include <string>  // For string.
#include <vector>  // For vector.

#include "DatanodeProtocol.pb.h"
#include "base/closure.h"                     // For Closure.
#include "base/constants.h"                   // For kRootINodeId.
#include "base/defer.h"                       // For DEFER.
#include "base/java_exceptions.h"             // For JavaExceptions.
#include "base/status.h"                      // For Status, Code, etc.
#include "block_manager/bip_write_manager.h"  // For BIPLockComponents.
#include "block_manager/block.h"  // For kInvalidBlockID, Block, BlockID, etc.
#include "block_manager/block_info.h"     // For BlockUCState.
#include "block_manager/block_manager.h"  // For BlockManager.
#include "datanode_manager/datanode_info.h"  // For DatanodeID, DatanodeInfo, kInvalidDatanodeID.
#include "namespace/namespace.h"                         // For NameSpace.
#include "test/block_manager/gmock_bip_write_manager.h"  // For GMockBIPWriteManager.
#include "test/block_manager/gmock_block_manager.h"  // For GMockBlockManager.
#include "test/datanode_manager/gmock_datanode_manager.h"  // For GMockDatanodeManager.
#include "test/gmock_edit_log_sender.h"         // For GMockEditLogSender.
#include "test/gmock_ha_state.h"                // For GMockHAState.
#include "test/namespace/gmock_meta_storage.h"  // For GMockMetaStorage.
#include "test/proto/generated/cloudfs/hdfs.h"  // For BlockProtoBuilder.
#include "test/proto/generated/dancenn/block_info_proto.h"  // For BlockInfoProtoBuilder.
#include "test/safemode/gmock_safemode.h"  // For GMockSafeMode.

DECLARE_bool(security_key_enable);
DECLARE_bool(lifecycle_enable);

DECLARE_uint64(block_report_scan_interval_sec);

namespace dancenn {

class BlockManagerTestV2 : public testing::Test {
 public:
  void SetUp() override {
    edit_log_sender_ =
        std::make_shared<testing::StrictMock<GMockEditLogSender>>();
    meta_storage_ = std::make_shared<testing::StrictMock<GMockMetaStorage>>();
    bip_write_manager_ =
        std::make_unique<testing::StrictMock<GMockBIPWriteManager>>();
    datanode_manager_ = std::make_shared<GMockDatanodeManager>();
    block_manager_ = std::make_unique<BlockManager>();
    block_manager_->set_ha_state(&ha_state_);
    block_manager_->set_safemode(&safemode_);
    block_manager_->TestOnlySetEditLogSender(edit_log_sender_);
    // block_manager_->SetMetaStorage(meta_storage_);
    block_manager_->TestOnlySetBIPWriteManager(bip_write_manager_.get());
    block_manager_->set_datanode_manager(datanode_manager_);

    block_report_manager_ = std::make_unique<BlockReportManager>();
    block_manager_->set_block_report_manager(block_report_manager_.get());
    block_report_manager_->Start(reinterpret_cast<NameSpace*>(0x1),
                                 datanode_manager_.get(),
                                 block_manager_.get());
  }

  void TearDown() override {
    block_report_manager_->StopActive();
    block_report_manager_->Stop();

    block_manager_.reset();
  }

 protected:
  testing::StrictMock<GMockHAState> ha_state_;
  testing::StrictMock<GMockSafeMode> safemode_;
  std::shared_ptr<testing::StrictMock<GMockEditLogSender>> edit_log_sender_;
  std::shared_ptr<testing::StrictMock<GMockMetaStorage>> meta_storage_;
  std::unique_ptr<testing::StrictMock<GMockBIPWriteManager>> bip_write_manager_;
  std::shared_ptr<GMockDatanodeManager> datanode_manager_;
  std::unique_ptr<BlockManager> block_manager_;
  std::unique_ptr<BlockReportManager> block_report_manager_;
};

// The potential return values for the BlockManager::NeedRelease function are:
// @return        @remove_last_block  @last_blk
// IsFalse()      true/false          InvalidBlk/last_bp
// IsOK()         false               InvalidBlk
// HasException() undefined           undefined

TEST_F(BlockManagerTestV2, NeedReleaseWithEmptyBlocks) {
  bool remove_last_block = true;
  Block last_blk(0, 0, 0);
  EXPECT_TRUE(
      block_manager_->NeedRelease({}, &remove_last_block, &last_blk).IsFalse());
  EXPECT_FALSE(remove_last_block);
  EXPECT_EQ(last_blk.id, kInvalidBlockID);
  EXPECT_EQ(last_blk.gs, 0);
  EXPECT_EQ(last_blk.num_bytes, 0);
}

TEST_F(BlockManagerTestV2, NeedReleaseWhenTheThirdToLastBlockIsUc) {
  block_manager_->AddBlock(Block(1, 1024, 1024),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kUnderConstruction);
  Status s = block_manager_->NeedRelease({BlockProtoBuilder()
                                              .SetBlockId(1)
                                              .SetGenStamp(1000)
                                              .SetNumBytes(1024)
                                              .Build(),
                                          BlockProtoBuilder()
                                              .SetBlockId(2)
                                              .SetGenStamp(1001)
                                              .SetNumBytes(2048)
                                              .Build(),
                                          BlockProtoBuilder()
                                              .SetBlockId(3)
                                              .SetGenStamp(1002)
                                              .SetNumBytes(4096)
                                              .Build()},
                                         nullptr,
                                         nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "The blocks has unexpected state");
}

TEST_F(BlockManagerTestV2, NeedReleaseWhenThePenultBlockIsUc) {
  block_manager_->AddBlock(Block(1, 1024, 1000),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kUnderConstruction);
  block_manager_->AddBlock(Block(2, 2048, 1001),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kUnderConstruction);
  Status s = block_manager_->NeedRelease({BlockProtoBuilder()
                                              .SetBlockId(1)
                                              .SetGenStamp(1000)
                                              .SetNumBytes(1024)
                                              .Build(),
                                          BlockProtoBuilder()
                                              .SetBlockId(2)
                                              .SetGenStamp(1001)
                                              .SetNumBytes(2048)
                                              .Build()},
                                         nullptr,
                                         nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(
      s.message(),
      "The penultimate block {id:1,gs:1000,num_bytes:1024} is not committed");
}

TEST_F(BlockManagerTestV2,
       NeedReleaseWhenThePenultAndLastBlockBothAreComplete) {
  block_manager_->AddBlock(Block(1, 1024, 1000),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kComplete);
  block_manager_->AddBlock(Block(2, 2048, 1001),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kComplete);
  bool remove_last_block = true;
  Block last_blk(0, 0, 0);
  EXPECT_TRUE(block_manager_
                  ->NeedRelease({BlockProtoBuilder()
                                     .SetBlockId(1)
                                     .SetGenStamp(1000)
                                     .SetNumBytes(1024)
                                     .Build(),
                                 BlockProtoBuilder()
                                     .SetBlockId(2)
                                     .SetGenStamp(1001)
                                     .SetNumBytes(2048)
                                     .Build()},
                                &remove_last_block,
                                &last_blk)
                  .IsFalse());
  EXPECT_FALSE(remove_last_block);
  EXPECT_EQ(last_blk.id, 2);
  EXPECT_EQ(last_blk.gs, 1001);
  EXPECT_EQ(last_blk.num_bytes, 2048);
}

TEST_F(BlockManagerTestV2, NeedReleaseWhenTheLastBlockIsCommitted) {
  block_manager_->AddBlock(Block(1, 1024, 1000),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kCommitted);
  Status s = block_manager_->NeedRelease({BlockProtoBuilder()
                                              .SetBlockId(1)
                                              .SetGenStamp(1000)
                                              .SetNumBytes(1024)
                                              .Build()},
                                         nullptr,
                                         nullptr);
  EXPECT_EQ(s.exception(), JavaExceptions::kAlreadyBeingCreatedException);
  EXPECT_EQ(s.message(),
            "Failed to release lease. "
            "Committed blocks are waiting to be minimally replicated. "
            "Try again later.");
}

TEST_F(BlockManagerTestV2, NeedReleaseWhenTheLastBlockIsUcAndLenIsNotZero) {
  block_manager_->AddBlock(Block(1, 1024, 1000),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {},
                           BlockUCState::kUnderConstruction);
  bool remove_last_block = true;
  Block last_blk(0, 0, 0);
  EXPECT_TRUE(block_manager_
                  ->NeedRelease({BlockProtoBuilder()
                                     .SetBlockId(1)
                                     .SetGenStamp(1000)
                                     .SetNumBytes(1024)
                                     .Build()},
                                &remove_last_block,
                                &last_blk)
                  .IsOK());
  EXPECT_FALSE(remove_last_block);
  EXPECT_EQ(last_blk.id, kInvalidBlockID);
  EXPECT_EQ(last_blk.gs, 0);
  EXPECT_EQ(last_blk.num_bytes, 0);
}

TEST_F(BlockManagerTestV2, NeedReleaseWhenTheLastBlockIsUcAndHasReplicas) {
  block_manager_->AddBlock(Block(1, 0, 1000),
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {1},
                           BlockUCState::kUnderConstruction);
  bool remove_last_block = true;
  Block last_blk(0, 0, 0);
  EXPECT_TRUE(block_manager_
                  ->NeedRelease({BlockProtoBuilder()
                                     .SetBlockId(1)
                                     .SetGenStamp(0)
                                     .SetNumBytes(1024)
                                     .Build()},
                                &remove_last_block,
                                &last_blk)
                  .IsOK());
  EXPECT_FALSE(remove_last_block);
  EXPECT_EQ(last_blk.id, kInvalidBlockID);
  EXPECT_EQ(last_blk.gs, 0);
  EXPECT_EQ(last_blk.num_bytes, 0);
}

TEST_F(BlockManagerTestV2, RemoveBlocksAndUpdateSafeOfBlockInfoProto) {
  GMockBlockManager block_manager;
  EXPECT_CALL(
      block_manager,
      RemoveBlocksAndUpdateSafeMode(testing::ElementsAre(
          testing::AllOf(testing::Property(&BlockProto::blockid, 1),
                         testing::Property(&BlockProto::genstamp, 1000),
                         testing::Property(&BlockProto::numbytes, 1024)),
          testing::AllOf(testing::Property(&BlockProto::blockid, 2),
                         testing::Property(&BlockProto::genstamp, 1001),
                         testing::Property(&BlockProto::numbytes, 2048)))))
      .Times(1);
  block_manager.BlockManager::RemoveBlocksAndUpdateSafeMode(
      std::vector<BlockInfoProto>{BlockInfoProtoBuilder()
                                      .SetBlockId(1)
                                      .SetGenStamp(1000)
                                      .SetNumBytes(1024)
                                      .Build(),
                                  BlockInfoProtoBuilder()
                                      .SetBlockId(2)
                                      .SetGenStamp(1001)
                                      .SetNumBytes(2048)
                                      .Build()});
}

TEST_F(BlockManagerTestV2,
       RemoveBlocksAndUpdateSafeModeWillGenerateInvalidateCmds) {
  block_manager_->AddBlock(Block(1, 1024, 1000),
                           17123,
                           17122,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {4, 5, 6},
                           BlockUCState::kComplete);

  EXPECT_CALL(ha_state_, ShouldPopulateReplicationQueues())
      .WillRepeatedly(testing::Return(true));
  EXPECT_CALL(safemode_, IsOn()).WillRepeatedly(testing::Return(false));
  EXPECT_CALL(safemode_, AdjustSafeModeBlockTotals(testing::_, testing::_));
  DatanodeInfo dn_4(4, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(4))
      .WillRepeatedly(testing::Return(&dn_4));
  DatanodeInfo dn_5(5, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(5))
      .WillRepeatedly(testing::Return(&dn_5));
  DatanodeInfo dn_6(6, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(6))
      .WillRepeatedly(testing::Return(&dn_6));
  block_manager_->TestOnlySetIsActive(true);
  FLAGS_security_key_enable = false;

  // https://bytedance.feishu.cn/docx/doxcnG7fMCSybMLKjccbuQBDgcd#doxcnIw8muYowW6m8P9G2082dkW
  block_manager_->RemoveBlocksAndUpdateSafeMode({BlockProtoBuilder()
                                                     .SetBlockId(1)
                                                     .SetGenStamp(1000)
                                                     .SetNumBytes(1024)
                                                     .Build()});
  block_manager_->ComputeInvalidateBlockWork();
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    cloudfs::datanode::HeartbeatResponseProto response;
    block_manager_->GetCommands(dn_id, "bp-1", 0, &response);
    // The first command is TRANSFER.
    ASSERT_EQ(response.cmds_size(), 2);
    const cloudfs::datanode::DatanodeCommandProto& cmd = response.cmds(1);
    EXPECT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto_Type_BlockCommand);
    const cloudfs::datanode::BlockCommandProto& blk_cmd = cmd.blkcmd();
    EXPECT_EQ(blk_cmd.action(),
              cloudfs::datanode::BlockCommandProto_Action_INVALIDATE);
    ASSERT_EQ(blk_cmd.blocks_size(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).blockid(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).genstamp(), 1000);
    EXPECT_EQ(blk_cmd.blocks(0).numbytes(), 1024);
  }
}

TEST_F(BlockManagerTestV2, InitRecoverOfCompleteBlock) {
}

TEST_F(BlockManagerTestV2,
       InitRecoverDnBlockAndChooseBlockRecoverPrimaryFailed) {
}

TEST_F(BlockManagerTestV2, InitRecoverDnBlockAndBWMInitRecoverThrowsException) {
}

TEST_F(BlockManagerTestV2, CommitBlockSynCloseFileDeleteBlockHappyCase) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(true);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(BlockManagerTestV2, CommitBlockSynCloseFileDeleteNotExistedBlock) {
  block_manager_->SetMetaStorage(meta_storage_);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1079812287, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(true);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(BlockManagerTestV2, CommitBlockSynCloseFileCommitBlockHappyCase) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  auto bi = block_manager_->TestOnlyGetSlice(1079812287)->Locate(1079812287);
  EXPECT_EQ(bi->uc_state(), BlockUCState::kCommitted);
  EXPECT_EQ(bi->blk().gs, 1001);
  EXPECT_EQ(bi->blk().num_bytes, 2048);
}

TEST_F(BlockManagerTestV2, CommitBlockSynCloseFileCommitNotExistedBlock) {
  block_manager_->SetMetaStorage(meta_storage_);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1079812287, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Block 1079812287 not found");
}

TEST_F(BlockManagerTestV2, CommitBlockSynCloseFileCommitBlockWithWrongGS) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(true);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "The recovery id 1002 does not match "
            "current recovery id 1001 for block 1079812287");
}

TEST_F(BlockManagerTestV2, CommitBlockSynNoCloseFileDeleteBlockHappyCase) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(true);

  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(BlockManagerTestV2, CommitBlockSynNoCloseFileDeleteNotExistedBlock) {
  block_manager_->SetMetaStorage(meta_storage_);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1079812287, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(true);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
}

TEST_F(BlockManagerTestV2, CommitBlockSynNoCloseFileCommitBlockHappyCase) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1001);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_TRUE(s.IsOK());
  auto bi = block_manager_->TestOnlyGetSlice(1079812287)->Locate(1079812287);
  EXPECT_EQ(bi->uc_state(), BlockUCState::kCommitted);
  EXPECT_EQ(bi->blk().gs, 1001);
  EXPECT_EQ(bi->blk().num_bytes, 2048);
}

TEST_F(BlockManagerTestV2, CommitBlockSynNoCloseFileCommitNotExistedBlock) {
  block_manager_->SetMetaStorage(meta_storage_);
  EXPECT_CALL(*meta_storage_, GetBlockInfo(1079812287, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(), "Block 1079812287 not found");
}

TEST_F(BlockManagerTestV2, CommitBlockSynNoCloseFileCommitBlockWithWrongGS) {
  block_manager_->AddBlock(Block(1079812287, 1024, 1000),
                           17123,
                           kRootINodeId,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           std::vector<DatanodeID>{},
                           BlockUCState::kUnderRecovery);
  block_manager_->TestOnlyGetSlice(1079812287)
      ->GetUcInternal(1079812287)
      ->set_recovery_id(1001);

  CommitBlockSynchronizationRequestProto request;
  request.mutable_block()->set_blockid(1079812287);
  request.mutable_block()->set_generationstamp(1000);
  request.mutable_block()->set_numbytes(1024);
  request.set_newgenstamp(1002);
  request.set_newlength(2048);
  request.set_closefile(false);
  request.set_deleteblock(false);
  Status s = block_manager_->CommitBlockSynchronization(request);
  EXPECT_EQ(s.exception(), JavaExceptions::kIOException);
  EXPECT_EQ(s.message(),
            "The recovery id 1002 does not match "
            "current recovery id 1001 for block 1079812287");
}

TEST_F(BlockManagerTestV2, AnalyzeFileBlocksToCommitHappyCase) {
  Block request_block(1, 1024, kGrandfatherGenerationStamp);
  Block stored_last(1, 1024, kGrandfatherGenerationStamp);

  block_manager_->AddBlock(stored_last,
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {1},
                           BlockUCState::kUnderConstruction);
  EXPECT_TRUE(
      block_manager_->AnalyzeFileBlocksToCommit(request_block, stored_last)
          .IsOK());
}

TEST_F(BlockManagerTestV2, AnalyzeFileBlocksToCommitFailed) {
  Block request_block(1, kGrandfatherGenerationStamp, 1000);
  Block stored_last(1, kGrandfatherGenerationStamp, 1000);

  block_manager_->AddBlock(stored_last,
                           17123,
                           17122,
                           0,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {1},
                           BlockUCState::kUnderConstruction);
  {
    Block tmp_request(request_block);
    tmp_request.gs = 1;
    EXPECT_FALSE(
        block_manager_->AnalyzeFileBlocksToCommit(tmp_request, stored_last)
            .IsOK());
  }
  {
    Block tmp_request(request_block);
    tmp_request.id = 1;
    EXPECT_FALSE(
        block_manager_->AnalyzeFileBlocksToCommit(tmp_request, stored_last)
            .IsOK());
  }
  {
    EXPECT_TRUE(block_manager_->CommitOrCompleteOrPersistLastBlock(
        request_block, /*force=*/false));
    EXPECT_TRUE(block_manager_->BlockHasBeenCommitted(request_block.id));
    EXPECT_FALSE(block_manager_->BlockHasBeenComplete(request_block.id));
    EXPECT_FALSE(
        block_manager_->AnalyzeFileBlocksToCommit(request_block, stored_last)
            .IsOK());
  }
}

TEST_F(BlockManagerTestV2, IBRGenerateFinalizeCmdsHappyCase) {
  block_manager_->SetMetaStorage(meta_storage_);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(1, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  block_manager_->AddBlock(Block(1, 512, kBlockProtocolV2GenerationStamp),
                           17123,
                           17122,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {4, 5, 6},
                           BlockUCState::kCommitted);

  EXPECT_CALL(*meta_storage_,
              UpdateStorageClassReportsAsync(testing::_, testing::_, testing::_))
      .WillRepeatedly(testing::Return());

  EXPECT_CALL(ha_state_, GetHAMode())
      .WillRepeatedly(testing::Return(EditLogConf::HA));
  EXPECT_CALL(ha_state_, ShouldPopulateReplicationQueues())
      .WillRepeatedly(testing::Return(true));
  EXPECT_CALL(safemode_, IsOn()).WillRepeatedly(testing::Return(false));
  //  EXPECT_CALL(safemode_, AdjustSafeModeBlockTotals(testing::_, testing::_));
  EXPECT_CALL(safemode_, IncrementSafeBlockCount(testing::_))
      .WillRepeatedly(testing::Return());

  DatanodeInfo dn_4(4, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-4"))
      .WillRepeatedly(testing::Return(4));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(4))
      .WillRepeatedly(testing::Return(&dn_4));

  DatanodeInfo dn_5(5, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-5"))
      .WillRepeatedly(testing::Return(5));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(5))
      .WillRepeatedly(testing::Return(&dn_5));

  DatanodeInfo dn_6(6, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-6"))
      .WillRepeatedly(testing::Return(6));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(6))
      .WillRepeatedly(testing::Return(&dn_6));
  block_manager_->TestOnlySetIsActive(true);
  FLAGS_security_key_enable = false;
  FLAGS_lifecycle_enable = false;

  // IBR
  BlockManager::RepeatedIncBlockReport reports;
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    auto report = reports.Add();
    report->set_storageuuid("storage-1");
    auto blk = report->add_blocks();
    blk->mutable_block()->set_blockid(1);
    blk->mutable_block()->set_genstamp(kBlockProtocolV2GenerationStamp);
    blk->mutable_block()->set_numbytes(1024);
    blk->set_status(cloudfs::datanode::ReceivedDeletedBlockInfoProto::SEALED);
    block_manager_->IncrementalBlockReport("datanode-" + std::to_string(dn_id),
                                           reports);
  }

  // heartbeat
  block_manager_->ComputeSealedBlockWork();
  block_manager_->ComputeTruncatableBlockWork();
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    cloudfs::datanode::HeartbeatResponseProto response;
    block_manager_->GetCommands(dn_id, "bp-1", 0, &response);
    // The first command is TRANSFER. The second command is INVALID.
    ASSERT_EQ(response.cmds_size(), 3);
    const cloudfs::datanode::DatanodeCommandProto& cmd = response.cmds(2);
    EXPECT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto_Type_BlockCommand);
    const cloudfs::datanode::BlockCommandProto& blk_cmd = cmd.blkcmd();
    EXPECT_EQ(blk_cmd.action(),
              cloudfs::datanode::BlockCommandProto_Action_FINALIZED);
    ASSERT_EQ(blk_cmd.blocks_size(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).blockid(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).genstamp(), kBlockProtocolV2GenerationStamp);
    EXPECT_EQ(blk_cmd.blocks(0).numbytes(), 512);
  }

  // clean
  block_manager_->ComputeTruncatableBlockWork();
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    cloudfs::datanode::HeartbeatResponseProto response;
    block_manager_->GetCommands(dn_id, "bp-1", 0, &response);
    // The first command is TRANSFER. The second command is INVALID.
    ASSERT_EQ(response.cmds_size(), 2);
  }

  // re-generated
  block_manager_->ComputeSealedBlockWork();
  block_manager_->ComputeTruncatableBlockWork();
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    cloudfs::datanode::HeartbeatResponseProto response;
    block_manager_->GetCommands(dn_id, "bp-1", 0, &response);
    // The first command is TRANSFER. The second command is INVALID.
    ASSERT_EQ(response.cmds_size(), 3);
    const cloudfs::datanode::DatanodeCommandProto& cmd = response.cmds(2);
    EXPECT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto_Type_BlockCommand);
    const cloudfs::datanode::BlockCommandProto& blk_cmd = cmd.blkcmd();
    EXPECT_EQ(blk_cmd.action(),
              cloudfs::datanode::BlockCommandProto_Action_FINALIZED);
    ASSERT_EQ(blk_cmd.blocks_size(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).blockid(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).genstamp(), kBlockProtocolV2GenerationStamp);
    EXPECT_EQ(blk_cmd.blocks(0).numbytes(), 512);
  }
}

TEST_F(BlockManagerTestV2, IBRGenerateFinalizeCmdsBadCase) {
}

TEST_F(BlockManagerTestV2, FBRGenerateFinalizeCmdsHappyCase) {
  block_manager_->SetMetaStorage(meta_storage_);

  EXPECT_CALL(*meta_storage_, GetBlockInfo(1, testing::_))
      .Times(1)
      .WillOnce(testing::Return(false));

  block_manager_->AddBlock(Block(1, 512, kBlockProtocolV2GenerationStamp),
                           17123,
                           17122,
                           3,
                           cloudfs::IoMode::DATANODE_BLOCK,
                           {4, 5, 6},
                           BlockUCState::kComplete);

  EXPECT_CALL(
      *meta_storage_,
      UpdateStorageClassReportsAsync(testing::_, testing::_, testing::_))
      .WillRepeatedly(testing::WithArg<2>([](Closure* done) {
        if (done) {
          done->Run();
        }
      }));

  EXPECT_CALL(ha_state_, ShouldPopulateReplicationQueues())
      .WillRepeatedly(testing::Return(true));
  EXPECT_CALL(ha_state_, GetHAMode())
      .WillRepeatedly(testing::Return(EditLogConf::HA));
  EXPECT_CALL(safemode_, IsOn()).WillRepeatedly(testing::Return(false));
  EXPECT_CALL(safemode_, IsStartingUp()).WillRepeatedly(testing::Return(false));

  DatanodeInfo dn_4(4, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-4"))
      .WillRepeatedly(testing::Return(4));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(4))
      .WillRepeatedly(testing::Return(&dn_4));

  DatanodeInfo dn_5(5, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-5"))
      .WillRepeatedly(testing::Return(5));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(5))
      .WillRepeatedly(testing::Return(&dn_5));

  DatanodeInfo dn_6(6, DatanodeIDProto(), cnetpp::base::IPAddress());
  EXPECT_CALL(*datanode_manager_, GetDatanodeInterId("datanode-6"))
      .WillRepeatedly(testing::Return(6));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(6))
      .WillRepeatedly(testing::Return(&dn_6));

  block_manager_->TestOnlySetIsActive(true);
  FLAGS_security_key_enable = false;
  FLAGS_lifecycle_enable = false;

  // FBR
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    std::string dn_uuid = "datanode-" + std::to_string(dn_id);

    block_manager_->InitBlockReport4Test(dn_id);

    auto reg = cloudfs::datanode::DatanodeRegistrationProto::default_instance();
    reg.mutable_datanodeid()->set_datanodeuuid(dn_uuid);

    cloudfs::datanode::BlockReportRequestProto req;
    req.mutable_registration()->CopyFrom(reg);
    req.set_blockpoolid("bp-1");
    req.mutable_context()->set_currpc(1);
    req.mutable_context()->set_totalrpcs(1);
    req.mutable_context()->set_id(0);

    auto report = req.add_reports();
    report->mutable_storage()->set_storageuuid("storage-uuid");
    report->mutable_storage()->set_storagetype(StorageTypeProto::DISK);
    report->set_blocksformatversion(StorageBlockReportProto::V2);

    cloudfs::datanode::BlockReportBlockInfoProtoV2 blocksv2;
    {
      cloudfs::datanode::BlockReportBlockInfoProtoV2Entry entry;
      entry.mutable_block()->set_blockid(1);
      entry.mutable_block()->set_numbytes(1024);
      entry.mutable_block()->set_genstamp(kBlockProtocolV2GenerationStamp);
      entry.set_replicastate(cloudfs::ReplicaStateProto::SEALED);
      blocksv2.add_blocks()->CopyFrom(entry);
    }
    {
      std::string res;
      google::protobuf::io::GzipOutputStream::Options options;
      options.format = google::protobuf::io::GzipOutputStream::GZIP;
      options.compression_level = 9;
      google::protobuf::io::StringOutputStream outputStream(&res);
      google::protobuf::io::GzipOutputStream gzipStream(&outputStream, options);
      blocksv2.SerializeToZeroCopyStream(&gzipStream);
      gzipStream.Flush();
      report->set_blocksv2(res);
    }

    SynchronizedRpcClosure done;
    block_manager_->AsyncBlockReport(dn_uuid, &req, &done);
    done.Await();
  }

  // heartbeat
  block_manager_->ComputeSealedBlockWork();
  block_manager_->ComputeTruncatableBlockWork();
  for (DatanodeID dn_id = 4; dn_id <= 6; dn_id++) {
    cloudfs::datanode::HeartbeatResponseProto response;
    block_manager_->GetCommands(dn_id, "bp-1", 0, &response);
    // The first command is TRANSFER. The second command is INVALID.
    ASSERT_EQ(response.cmds_size(), 3);
    const cloudfs::datanode::DatanodeCommandProto& cmd = response.cmds(2);
    EXPECT_EQ(cmd.cmdtype(),
              cloudfs::datanode::DatanodeCommandProto_Type_BlockCommand);
    const cloudfs::datanode::BlockCommandProto& blk_cmd = cmd.blkcmd();
    EXPECT_EQ(blk_cmd.action(),
              cloudfs::datanode::BlockCommandProto_Action_FINALIZED);
    ASSERT_EQ(blk_cmd.blocks_size(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).blockid(), 1);
    EXPECT_EQ(blk_cmd.blocks(0).genstamp(), kBlockProtocolV2GenerationStamp);
    EXPECT_EQ(blk_cmd.blocks(0).numbytes(), 512);
  }
}

TEST_F(BlockManagerTestV2, FBRGenerateFinalizeCmdsBadCase) {
}

class BlockReportSchedulerTest : public testing::Test {
 public:
  void SetUp() override {
    FLAGS_block_report_scan_interval_sec = 1;
    block_manager_ = std::make_shared<testing::StrictMock<GMockBlockManager>>();
    datanode_manager_ = std::make_shared<GMockDatanodeManager>();

    scheduler_ = std::make_unique<BlockReportSchedulerImpl>();
    scheduler_->SetDatanodeManager(datanode_manager_.get());
    scheduler_->SetBlockManager(block_manager_.get());

    scheduler_->Run();
  }

  void TearDown() override {
    scheduler_->Stop();
    FLAGS_block_report_scan_interval_sec = 60;
  }

 protected:
  std::shared_ptr<testing::StrictMock<GMockBlockManager>> block_manager_;
  std::shared_ptr<GMockDatanodeManager> datanode_manager_;

  std::unique_ptr<BlockReportScheduler> scheduler_;
};

TEST_F(BlockReportSchedulerTest, FBRCmdSuccessTest) {
  DatanodeInfoPtr dn =
      new DatanodeInfo(1, DatanodeIDProto(), cnetpp::base::IPAddress());
  dn->SetRegistered();
  dn->UpdateHeartbeat(true);
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromIdInternal(1))
      .WillRepeatedly(testing::Return(dn));
  EXPECT_CALL(*datanode_manager_, GetDatanodeFromId(1))
      .WillRepeatedly(testing::Return(dn));
  scheduler_->AddDatanode(1);
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  {
    cloudfs::datanode::HeartbeatResponseProto resp;
    EXPECT_EQ(scheduler_->GetCommand(1, &resp), 1);
    EXPECT_EQ(resp.cmds(0).blockreportcmd().type(),
              cloudfs::datanode::BlockReportCommandProto_Type_FAST);
    scheduler_->UpdateBlockReportRecord(1, true, true);
    dn->unset_content_stale();
    auto ctx = scheduler_->GetBlockReportContext(1);
    ctx->block_report_id = 1;
    ctx->total_rpc = 1;
    ctx->cur_rpc = 0;
    scheduler_->FreeSlot(1);
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(2000));
  {
    cloudfs::datanode::HeartbeatResponseProto resp;
    EXPECT_EQ(scheduler_->GetCommand(1, &resp), 1);
    EXPECT_EQ(resp.cmds(0).blockreportcmd().type(),
              cloudfs::datanode::BlockReportCommandProto_Type_NORMAL);
    scheduler_->UpdateBlockReportRecord(1, true, false);
    auto ctx = scheduler_->GetBlockReportContext(1);
    ctx->block_report_id = 1;
    ctx->total_rpc = 1;
    ctx->cur_rpc = 0;
    scheduler_->FreeSlot(1);
  }
  std::this_thread::sleep_for(std::chrono::milliseconds(2000));
  {
    cloudfs::datanode::HeartbeatResponseProto resp;
    EXPECT_EQ(scheduler_->GetCommand(1, &resp), 0);
  }
}

}  // namespace dancenn
