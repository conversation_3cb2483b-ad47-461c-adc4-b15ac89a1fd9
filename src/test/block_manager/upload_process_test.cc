// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/06
// Description

#include <cnetpp/base/ip_address.h>
#include <cnetpp/concurrency/thread_pool.h>
#include <fiu-control.h>
#include <fiu.h>
#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <stdlib.h>

#include <array>
#include <chrono>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <shared_mutex>
#include <sstream>
#include <string>
#include <thread>

#include "base/file_utils.h"
#include "block_manager/block.h"
#include "block_manager/block_info.h"
#include "block_manager/block_manager.h"
#include "block_manager/block_map_slice.h"
#include "block_manager/block_pufs_info.h"
#include "datanode_manager/datanode_info.h"
#include "datanode_manager/datanode_manager.h"
#include "edit/edit_log_context.h"
#include "edit/edit_log_op.h"
#include "edit/edit_log_sync_listener.h"
#include "edit/op/op_set_cfs_universal_info.h"
#include "edit/tailer.h"
#include "namespace/meta_storage.h"
#include "namespace/namespace.h"
#include "proto/generated/cloudfs/DatanodeProtocol.pb.h"
#include "proto/generated/cloudfs/hdfs.pb.h"
#include "proto/generated/dancenn/block_info_proto.pb.h"
#include "safemode/safemode.h"
#include "test/block_manager/gmock_block_manager.h"
#include "test/dancenn_test_base.h"
#include "test/edit/gmock_edit_log_context.h"
#include "test/gmock_edit_log_input_context.h"
#include "test/gmock_edit_log_sender.h"
#include "test/gmock_edit_log_sync_listener.h"
#include "test/mock_ha_state.h"
#include "test/namespace/mock_namespace.h"
#include "test/safemode/gmock_safemode.h"

using cloudfs::BlockProto;
using cloudfs::StorageInfoProto;
using cloudfs::datanode::DatanodeCommandProto;
using cloudfs::datanode::DatanodeRegistrationProto;
using cloudfs::datanode::HeartbeatResponseProto;
using cloudfs::datanode::NotifyEvictableCommandProto;
using cloudfs::datanode::ReceivedDeletedBlockInfoProto;
using cloudfs::datanode::StorageReceivedDeletedBlocksProto;
using cloudfs::datanode::UploadCommandProto;

using namespace std::chrono_literals;

DECLARE_int32(nn_dn_clock_drift_s);
DECLARE_int32(min_upload_timeout_s);

namespace dancenn {

class UploadProcessTest : public testing::Test {
 public:
  static void SetUpTestCase() {
    fiu_init(0);
  }

  void SetUp() override {
    InitDataNodes();
    InitBlock();
    StartNameNode(false);
  }

  void TearDown() override {
    meta_storage_->Shutdown();
    FileUtils::DeleteDirectoryRecursively(db_path_);
  }

 protected:
  void StartNameNode(bool is_restart) {
    edit_log_sender_ = new GMockEditLogSender();
    edit_log_sync_listener_.reset(new GMockEditLogSyncListener());

    if (!is_restart) {
      ASSERT_NE(mkdtemp(&(db_path_[0])), nullptr);
    } else {
      meta_storage_->Shutdown();
    }
    meta_storage_.reset(new MetaStorage(db_path_));
    meta_storage_->Launch();
    meta_storage_->StartStandby();
    // Meta storage should be standby mode when tail edit logs.
    if (!is_restart) {
      meta_storage_->StartActive();
    }

    datanode_manager_.reset(new DatanodeManager(/*with_start=*/false));
    for (auto reg : dn_regs_) {
      DatanodeIDProto* datanode_id = reg.mutable_datanodeid();
      datanode_manager_->Register(
          *datanode_id, &reg, cnetpp::base::IPAddress(datanode_id->ipaddr()));
    }

    FileFinalizer* file_finalizer = new FileFinalizer();
    ns_.reset(new NameSpace(
        nullptr,
        nullptr,
        std::shared_ptr<BlockManager>(),
        std::unique_ptr<BlockReportManager>(),
        std::shared_ptr<DatanodeManager>(),
        std::unique_ptr<LeaseManager>(),
        std::make_unique<LeaseMonitor>(
            nullptr,
            [](uint64_t inode_id,
               const std::string& orig_holder,
               const std::string& new_holder) { return Status(); }),
        std::shared_ptr<EditLogContextBase>(),
        std::shared_ptr<EditLogSenderBase>(),
        std::shared_ptr<MetaStorage>(),
        std::unique_ptr<RWLockManager>(),
        std::shared_ptr<KeyManager>(),
        std::shared_ptr<DataCenters>(),
        std::shared_ptr<AccessCounterManager>(),
        std::unique_ptr<FileFinalizerBase>(file_finalizer),
        std::shared_ptr<JobManager>()));
    ns_->blockpool_id_ = block_pool_id_;
    ns_->meta_storage_ = meta_storage_;
    ns_->apply_threads_.emplace_back(
        std::make_shared<cnetpp::concurrency::ThreadPool>("apply-0"));
    ns_->apply_threads_.back()->set_num_threads(1);
    ns_->apply_threads_.back()->Start();

    safemode_.reset(new GMockSafeMode());

    block_manager_.reset(new GMockBlockManager());
    block_manager_->is_active_ = true;
    block_manager_->edit_log_sender_.reset(edit_log_sender_);
    block_manager_->SetMetaStorage(meta_storage_);
    block_manager_->set_datanode_manager(datanode_manager_);
    block_manager_->set_ns(ns_.get());
    block_manager_->set_safemode(safemode_.get());
    block_manager_->set_ha_state(&ha_state_);
    block_manager_->AddBlock(Block(block_proto_),
                             /*inode_id=*/0UL,
                             /*parent_id=*/0UL,
                             (uint8_t)3,
                             cloudfs::DATANODE_BLOCK,
                             std::vector<DatanodeID>{1, 2},
                             BlockUCState::kComplete);

    file_finalizer->Start(ns_.get(),
                          block_manager_.get(),
                          nullptr,
                          nullptr,
                          edit_log_sender_,
                          meta_storage_.get(),
                          nullptr);

    BlockInfoProto bip;
    bip.set_state(BlockInfoProto::kComplete);
    bip.set_block_id(block_proto_.blockid());
    bip.set_gen_stamp(block_proto_.genstamp());
    bip.set_num_bytes(block_proto_.numbytes());
    bip.set_inode_id(0);
    bip.set_expected_rep(0);
    meta_storage_->TestOnlyPutBlockInfo(bip);

    uint64_t last_ckpt_txid = ns_->GetLastCkptTxId();
    ns_->ResetLastAppliedTxId(last_ckpt_txid);
  }

  void InitBlock() {
    block_proto_.set_blockid(1073750578);
    block_proto_.set_genstamp(1001);
    block_proto_.set_numbytes(1024);

    block_pufs_name_ =
        block_pool_id_ + ":" + std::to_string(block_proto_.blockid());

    block_pufs_info_.block_pool_id_ = block_pool_id_;
    block_pufs_info_.block_id_ = block_proto_.blockid();
    block_pufs_info_.gen_stamp_ = block_proto_.genstamp();
    block_pufs_info_.num_bytes_ = block_proto_.numbytes();
    block_pufs_info_.state_ = BlockPufsState::kUploadIssued;
    block_pufs_info_.pufs_name_ = block_pufs_name_;
    block_pufs_info_.upload_issued_times_ = 1;
    block_pufs_info_.dn_uuid_ = dn_regs_[0].datanodeid().datanodeuuid();
    block_pufs_info_.upload_id_ = upload_ids_[0];

    StorageReceivedDeletedBlocksProto* received_deleted_blocks =
        upload_id_negoed_report_.Add();
    const char* kUnusedStorageUuid = "unused_storage_uuid";
    received_deleted_blocks->set_storageuuid(kUnusedStorageUuid);
    ReceivedDeletedBlockInfoProto* upload_id_negoed_block =
        received_deleted_blocks->add_blocks();
    *(upload_id_negoed_block->mutable_block()) = block_proto_;
    upload_id_negoed_block->set_status(
        ReceivedDeletedBlockInfoProto::UPLOAD_ID_NEGOED);
    upload_id_negoed_block->set_uploadid(upload_ids_[0]);
    upload_id_negoed_block->set_pufsname(block_pufs_name_);

    received_deleted_blocks = upload_succeed_report_.Add();
    received_deleted_blocks->set_storageuuid(kUnusedStorageUuid);
    ReceivedDeletedBlockInfoProto* upload_succeed_block =
        received_deleted_blocks->add_blocks();
    *(upload_succeed_block->mutable_block()) = block_proto_;
    upload_succeed_block->set_status(
        ReceivedDeletedBlockInfoProto::UPLOAD_SUCCEED);
    upload_succeed_block->set_uploadid(upload_ids_[0]);
    upload_succeed_block->set_pufsname(block_pufs_name_);
  }

  void InitDataNodes() {
    for (int i = 0; i < 2; i++) {
      std::string i_str = std::to_string(i);
      DatanodeRegistrationProto reg;
      DatanodeIDProto* datanode_id = reg.mutable_datanodeid();
      datanode_id->set_ipaddr("10.42.0." + i_str);
      datanode_id->set_hostname("dngroup-" + i_str);
      datanode_id->set_datanodeuuid("uuid-" + i_str);
      datanode_id->set_xferport(5060);
      datanode_id->set_infoport(5070);
      datanode_id->set_ipcport(5080);
      StorageInfoProto* storage_info = reg.mutable_storageinfo();
      storage_info->set_layoutversion(-60);
      storage_info->set_namespaceid(2);
      storage_info->set_clusterid("CID-" + i_str);
      storage_info->set_ctime(0);
      // Leave ExportedBlockKeysProto unset.
      reg.set_softwareversion("2.6.0");
      dn_regs_.push_back(reg);
    }
  }

  bool GetInTransaction() {
    auto& s = block_manager_->slice(block_proto_.blockid());
    std::shared_lock<BlockMapSlice> guard(*s);
    BlockInfoGuard bi_guard(s.get(), block_proto_.blockid(), false);
    BlockInfo* block_info = bi_guard.GetBlockInfo();
    return block_info->GetInTransaction();
  }

  DatanodeInfoPtr GetDatanodeInfo(std::size_t i) {
    DatanodeInfoPtr dn = datanode_manager_->GetDatanodeFromUuid(
        dn_regs_[i].datanodeid().datanodeuuid());
    return dn;
  }

  void ComputeCmdsWork() {
  }

 protected:
  // Minimum value of Writer::notified_txid_start_ is 1.
  int64_t startup_txid_ = 1;
  std::string block_pool_id_ = "BP-3-1623158058839";
  std::string db_path_ = "rocksdb_XXXXXX";
  std::vector<std::string> upload_ids_{"upload_id_1", "upload_id_2"};

  std::shared_ptr<MetaStorage> meta_storage_;
  GMockEditLogSender* edit_log_sender_;
  std::unique_ptr<GMockEditLogSyncListener> edit_log_sync_listener_;
  std::shared_ptr<DatanodeManager> datanode_manager_;
  std::unique_ptr<NameSpace> ns_;
  std::unique_ptr<GMockSafeMode> safemode_;
  MockHAState ha_state_;
  std::shared_ptr<GMockBlockManager> block_manager_;

  std::vector<DatanodeRegistrationProto> dn_regs_;

  BlockProto block_proto_;
  std::string block_pufs_name_;
  BlockPufsInfo block_pufs_info_;
  BlockManager::RepeatedIncBlockReport upload_id_negoed_report_;
  BlockManager::RepeatedIncBlockReport upload_succeed_report_;
};

TEST_F(UploadProcessTest, UploadSucceed) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(1)
      .WillOnce(testing::Return(0));
  EXPECT_CALL(*edit_log_sender_, LogSetBlockPufsInfo(testing::_))
      .Times(2)
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(1, 1);
        return 1;
      }))
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(2, 1);
        return 2;
      }));
  EXPECT_CALL(*edit_log_sync_listener_, TxFinish(testing::AnyOf(1, 2), 1))
      .Times(2)
      .WillRepeatedly(testing::Invoke([this](int64_t begin_txid, int n) {
        meta_storage_->TxFinish(begin_txid, n);
      }));

  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();

  HeartbeatResponseProto response;
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 1);
  DatanodeCommandProto dn_cmd = response.cmds(0);
  EXPECT_EQ(dn_cmd.cmdtype(), DatanodeCommandProto::UploadCommand);
  UploadCommandProto upload_cmd = dn_cmd.uploadcmd();
  EXPECT_EQ(upload_cmd.dnuuid(), dn_regs_[0].datanodeid().datanodeuuid());
  EXPECT_EQ(upload_cmd.blockpoolid(), ns_->blockpool_id());
  EXPECT_EQ(upload_cmd.block().blockid(), block_proto_.blockid());
  EXPECT_EQ(upload_cmd.block().genstamp(), block_proto_.genstamp());
  EXPECT_EQ(upload_cmd.block().numbytes(), block_proto_.numbytes());
  EXPECT_EQ(upload_cmd.blockpufsname(), block_pufs_name_);
  EXPECT_EQ(upload_cmd.expts(), FLAGS_min_upload_timeout_s);
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_1");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 0);

  block_manager_->IncrementalBlockReport(dn->uuid(), upload_succeed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  response.Clear();
  dn->GetNotifyEvictableCmds(&response);
  EXPECT_EQ(response.cmds_size(), 1);
  dn_cmd = response.cmds(0);
  EXPECT_EQ(dn_cmd.cmdtype(), DatanodeCommandProto::NotifyEvictableCommand);
  NotifyEvictableCommandProto ne_cmd = dn_cmd.necmd();
  EXPECT_EQ(ne_cmd.blockpoolid(), ns_->blockpool_id());
  EXPECT_EQ(ne_cmd.block().blockid(), block_proto_.blockid());
  EXPECT_EQ(ne_cmd.block().genstamp(), block_proto_.genstamp());
  EXPECT_EQ(ne_cmd.block().numbytes(), block_proto_.numbytes());
}

TEST_F(UploadProcessTest, DatanodesRaceUploadCmd) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(2)
      .WillRepeatedly(testing::Return(0));
  EXPECT_CALL(*edit_log_sender_, LogSetBlockPufsInfo(testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(1, 1);
        return 1;
      }));
  EXPECT_CALL(*edit_log_sync_listener_, TxFinish(1, 1))
      .Times(1)
      .WillOnce(testing::Invoke([this](int64_t begin_txid, int n) {
        meta_storage_->TxFinish(begin_txid, n);
      }));

  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  HeartbeatResponseProto response;
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 1);

  dn = GetDatanodeInfo(1);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  response.Clear();
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 0);
}

// Name node failovers when processing upload id negoed request.
class NnFoProcessUnTest : public UploadProcessTest {};

TEST_F(NnFoProcessUnTest, BeforeAppendEditLogBuffer) {
}

TEST_F(NnFoProcessUnTest, CantFlushEditLogBuffer) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(1)
      .WillOnce(testing::Return(0));
  EXPECT_CALL(*edit_log_sender_, LogSetBlockPufsInfo(testing::_))
      .Times(1)
      // Meta storage can't receive txid finish msg,
      // so BlockInfo::in_transaction_ won't be false.
      .WillOnce(testing::Return(2));

  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  std::this_thread::sleep_for(2s);
  EXPECT_TRUE(GetInTransaction());
  ComputeCmdsWork();
  HeartbeatResponseProto response;
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 0);

  dn = GetDatanodeInfo(1);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  ComputeCmdsWork();
  response.Clear();
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 0);
}

TEST_F(NnFoProcessUnTest, AfterFlushEditLogBeforeWriteMetaStorage) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(1)
      .WillOnce(testing::Return(0));
  block_pufs_info_.state_ = BlockPufsState::kUploadIssued;
  block_pufs_info_.nn_exp_ts_ =
      FLAGS_min_upload_timeout_s + FLAGS_nn_dn_clock_drift_s;
  block_pufs_info_.dn_exp_ts_ = FLAGS_min_upload_timeout_s;
  EXPECT_CALL(*edit_log_sender_,
              LogSetBlockPufsInfo(block_pufs_info_.SerializeToJsonString()))
      .Times(1)
      .WillOnce(testing::Return(2));
  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);

  StartNameNode(true);
  GMockEditLogContext* context = new GMockEditLogContext();
  GMockEditLogInputContext* input_context = new GMockEditLogInputContext();
  EXPECT_CALL(*context, CreateInputContext(1, 0, true))
      .Times(1)
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<EditLogInputContextBase>(input_context))));
  {
    OpSetCfsUniversalInfo op;
    op.SetOpCode(OP_SET_BLOCK_PUFS_INFO);
    op.SetTxid(1);
    op.set_s(block_pufs_info_.SerializeToJsonString());
    std::stringstream ss;
    op.WriteFields(&ss);
    EXPECT_CALL(*input_context, ReadOp(testing::_))
        .Times(2)
        .WillOnce(testing::DoAll(testing::SetArgPointee<0>(ss.str()),
                                 testing::Return(true)))
        .WillOnce(testing::Return(true));
  }
  input_context = new GMockEditLogInputContext();
  EXPECT_CALL(*context, CreateInputContext(2, 0, true))
      .WillOnce(testing::Return(testing::ByMove(
          std::unique_ptr<EditLogInputContextBase>(input_context))));
  EXPECT_CALL(*input_context, ReadOp(testing::_))
      .WillOnce(testing::Return(true));
  ASSERT_EQ(setenv("CLASSPATH", ".", true), 0);
  EditLogTailer edit_log_tailer(0,
                                test::CreateVMOnlyOnce(),
                                std::shared_ptr<EditLogContextBase>(context),
                                ns_.get());
  edit_log_tailer.Start();
  edit_log_tailer.Stop(/*to_last_txid=*/true);
  edit_log_tailer.StopApplyAssignerThread();
  meta_storage_->WaitNoPending(true);

  auto last_applied_txid = edit_log_tailer.last_applied_txid();
  EXPECT_EQ(last_applied_txid, 1);
  ns_->ResetLastAppliedTxId(last_applied_txid);
  meta_storage_->StartActive();
  // It it okay to process upload succeed request after catching up
  // meta storage.
  block_pufs_info_.state_ = BlockPufsState::kPersisted;
  EXPECT_CALL(*edit_log_sender_,
              LogSetBlockPufsInfo(block_pufs_info_.SerializeToJsonString()))
      .Times(1)
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(2, 1);
        return 2;
      }));
  EXPECT_CALL(*edit_log_sync_listener_, TxFinish(testing::_, 1))
      .Times(1)
      .WillRepeatedly(testing::Invoke([this](int64_t begin_txid, int n) {
        meta_storage_->TxFinish(begin_txid, n);
      }));
  dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_succeed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  HeartbeatResponseProto response;
  dn->GetNotifyEvictableCmds(&response);
  EXPECT_EQ(response.cmds_size(), 1);
}

TEST_F(NnFoProcessUnTest, AfterWriteMetaStorageBeforeRunCallback) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(1)
      .WillRepeatedly(testing::Return(0));
  EXPECT_CALL(*edit_log_sender_, LogSetBlockPufsInfo(testing::_))
      .Times(1)
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(1, 1);
        return 1;
      }));
  EXPECT_CALL(*edit_log_sync_listener_, TxFinish(testing::AnyOf(1, 2), 1))
      .Times(1)
      .WillOnce(testing::Invoke([this](int64_t begin_txid, int n) {
        meta_storage_->TxFinish(begin_txid, n);
      }));

  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  fiu_enable("ut.after_ms_before_callback", 1, nullptr, 0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  ComputeCmdsWork();
  HeartbeatResponseProto response;
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 0);

  StartNameNode(true);
  dn = GetDatanodeInfo(0);
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(1)
      .WillRepeatedly(testing::Return(0));
  fiu_disable("ut.after_ms_before_callback");
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  ComputeCmdsWork();
  response.Clear();
  dn->GetUploadCmds(&response);
  EXPECT_EQ(response.cmds_size(), 1);
}

// Data node failovers when uploading.
class DnFoProcessUnTest : public UploadProcessTest {};

TEST_F(DnFoProcessUnTest, ReapproveUploadAfterTimeout) {
  EXPECT_CALL(*block_manager_, GetCurrentTsInSec())
      .Times(2)
      .WillOnce(testing::Return(0))
      .WillOnce(testing::Return(FLAGS_min_upload_timeout_s +
                                FLAGS_nn_dn_clock_drift_s + 1));
  EXPECT_CALL(*edit_log_sender_, LogSetBlockPufsInfo(testing::_))
      .Times(2)
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(1, 1);
        return 1;
      }))
      .WillOnce(testing::Invoke([this](const std::string& s) {
        edit_log_sync_listener_->TxFinish(2, 1);
        return 2;
      }));
  EXPECT_CALL(*edit_log_sync_listener_, TxFinish(testing::_, 1))
      .Times(2)
      .WillRepeatedly(testing::Invoke([this](int64_t begin_txid, int n) {
        meta_storage_->TxFinish(begin_txid, n);
      }));

  DatanodeInfoPtr dn = GetDatanodeInfo(0);
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  HeartbeatResponseProto response;
  dn->GetUploadCmds(&response);
  block_manager_->GetCommands(1, block_pool_id_, 0, &response);

  dn = GetDatanodeInfo(1);
  upload_id_negoed_report_.Mutable(0)->mutable_blocks(0)->set_uploadid(
      "upload_id_2");
  block_manager_->IncrementalBlockReport(dn->uuid(), upload_id_negoed_report_);
  meta_storage_->WaitNoPending(true);
  EXPECT_FALSE(GetInTransaction());
  ComputeCmdsWork();
  response.Clear();
  block_manager_->GetCommands(2, block_pool_id_, 0, &response);
  EXPECT_EQ(response.cmds_size(), 1);
  DatanodeCommandProto dn_cmd = response.cmds(0);
  EXPECT_EQ(dn_cmd.cmdtype(), DatanodeCommandProto::UploadCommand);
  UploadCommandProto upload_cmd = dn_cmd.uploadcmd();
  EXPECT_EQ(upload_cmd.uploadid(), "upload_id_2");
  EXPECT_EQ(upload_cmd.aborteduploadids_size(), 1);
  EXPECT_EQ(upload_cmd.aborteduploadids(0), "upload_id_1");
}

}  // namespace dancenn
