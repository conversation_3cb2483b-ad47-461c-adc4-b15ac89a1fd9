// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/03/17
// Description

#include "block_manager/block_pufs_name_gen.h"

#include <gtest/gtest.h>
#include <gflags/gflags.h>

DECLARE_uint64(tos_suffix_salt);

namespace dancenn {

TEST(BlockPufsNameGenTest, TosPrefixEndsWithSlash) {
  BlockPufsNameGen gen("pool-bucket-cluster-boe-pre-7", "abc/", 87654321);
  // hashlib.md5(int.to_bytes(104723742347541 + FLAGS_tos_suffix_salt,
  //                          8,
  //                          'little')).hexdigest()
  EXPECT_EQ(gen.Get(104723742347541),
            "abc/87654321/block/"
            "104723742347541-24d2ce65fab210189a8157df585e3635.block");
  // hashlib.md5(int.to_bytes(104723742347542 + FLAGS_tos_suffix_salt,
  //                          8,
  //                          'little')).hexdigest()
  // DanceNN and DanceDN employ identical test cases to ensure that
  // the implemented algorithms in both systems produce equivalent results.
  // https://code.byted.org/storage/bytestore/blob/b3dc01291b596cb34ba07b9d4d536ee536f006fa/bytestore/chunkserver/hdfs/cfs/upload_tos_mgr_test.cc#L338
  EXPECT_EQ(gen.Get(104723742347542),
            "abc/87654321/block/"
            "104723742347542-fbec6fa54966d7dc5ba204283c682503.block");
  // Test idempotence.
  EXPECT_EQ(gen.Get(104723742347541),
            "abc/87654321/block/"
            "104723742347541-24d2ce65fab210189a8157df585e3635.block");
  EXPECT_EQ(gen.Get(104723742347542),
            "abc/87654321/block/"
            "104723742347542-fbec6fa54966d7dc5ba204283c682503.block");
  EXPECT_EQ(gen.GetTosResource(),
            "trn:tos:::pool-bucket-cluster-boe-pre-7/abc/87654321/block/*");
}

TEST(BlockPufsNameGenTest, TosPrefixNotEndsWithSlash) {
  BlockPufsNameGen gen("pool-bucket-cluster-boe-pre-7", "abc", 87654321);
  EXPECT_EQ(gen.Get(104723742347542),
            "abc/87654321/block/"
            "104723742347542-fbec6fa54966d7dc5ba204283c682503.block");
  EXPECT_EQ(gen.GetTosResource(),
            "trn:tos:::pool-bucket-cluster-boe-pre-7/abc/87654321/block/*");
}

TEST(BlockPufsNameGenTest, TosPrefixIsEmpty) {
  BlockPufsNameGen gen(
      "pool-bucket-cluster-boe-pre-7", /*tos_prefix=*/"", 87654321);
  EXPECT_EQ(
      gen.Get(104723742347542),
      "87654321/block/104723742347542-fbec6fa54966d7dc5ba204283c682503.block");
  EXPECT_EQ(gen.GetTosResource(),
            "trn:tos:::pool-bucket-cluster-boe-pre-7/87654321/block/*");
}

TEST(BlockPufsNameGenTest, MD5ResultZeroPaddingPrefix) {
  BlockPufsNameGen gen("pool-bucket-cluster-boe-pre-7", "abc/", 87654321);
  FLAGS_tos_suffix_salt = 7085592899479740416;
  EXPECT_EQ(gen.Get(1335653275),
            "abc/87654321/block/"
            "1335653275-fbdf2c7bf8b6b0190c0bf229a99fab44.block");
}

}  // namespace dancenn
