// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/09/13
// Description

#include <gtest/gtest.h>

#include "block_manager/block_info.h"
#include "block_manager/block_info_proto.h"

namespace dancenn {

class BlockUCStateTest : public testing::Test {};

TEST_F(BlockUCStateTest, CompareWithBlockInfoProtoState) {
  EXPECT_EQ(static_cast<int>(BlockUCState::kUnderConstruction),
            static_cast<int>(BlockInfoProto::kUnderConstruction));
  EXPECT_EQ(static_cast<int>(BlockUCState::kComplete),
            static_cast<int>(BlockInfoProto::kComplete));
  EXPECT_EQ(static_cast<int>(BlockUCState::kPersisted),
            static_cast<int>(BlockInfoProto::kPersisted));
}

}  // namespace dancenn
