// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <gtest/gtest.h>

#include <sys/types.h>
#include <unistd.h>
#include <ctime>

#include <vector>
#include <sstream>

#include "block_manager/under_replicated_blocks.h"

namespace dancenn {

TEST(UnderReplicatedBlocks, Add) {
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 3, 3));
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
    ASSERT_EQ(urb.GetUnderReplicatedBlockCount(), 1);
    ASSERT_TRUE(urb.Contains(bi.blk()));
    ASSERT_EQ(urb.size(), 1);
    auto sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 1);
    ASSERT_EQ(sep[4], 0);
    ASSERT_TRUE(urb.Add(&bi, 3, 3));
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
    ASSERT_EQ(urb.GetUnderReplicatedBlockCount(), 1);
    ASSERT_TRUE(urb.Contains(bi.blk()));
    ASSERT_EQ(urb.size(), 1);
    sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 1);
    ASSERT_EQ(sep[4], 0);
    urb.Clear();
    ASSERT_EQ(urb.size(), 0);
    sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 0);
    auto sep_p =
        urb.SizePriority(UnderReplicatedBlocks::Priority::kHighestPriority);
    ASSERT_EQ(sep[0], sep_p);
    sep_p =
        urb.SizePriority(UnderReplicatedBlocks::Priority::kVeryUnderReplicated);
    ASSERT_EQ(sep[1], sep_p);
    sep_p = urb.SizePriority(UnderReplicatedBlocks::Priority::kUnderReplicated);
    ASSERT_EQ(sep[2], sep_p);
    sep_p = urb.SizePriority(
        UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed);
    ASSERT_EQ(sep[3], sep_p);
    sep_p =
        urb.SizePriority(UnderReplicatedBlocks::Priority::kWithCorruptBlocks);
    ASSERT_EQ(sep[4], sep_p);
    sep_p = urb.SizePriority(UnderReplicatedBlocks::Priority::kLast);
    ASSERT_EQ(0, sep_p);
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);

    ASSERT_TRUE(urb.Add(&bi, 0, 1));
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    ASSERT_EQ(urb.GetUnderReplicatedBlockCount(), 0);
    ASSERT_TRUE(urb.Contains(bi.blk()));
    auto sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 1);
    ASSERT_TRUE(urb.Add(&bi, 0, 1));
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    ASSERT_EQ(urb.GetUnderReplicatedBlockCount(), 0);
    ASSERT_TRUE(urb.Contains(bi.blk()));
    sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 1);
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 0, 3));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetUnderReplicatedBlockCount(), 0);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 1);
    auto sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 1);
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 0, 3));
    auto sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 1);
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(1);
    ASSERT_EQ(urbs_to_process[4].size(), 1);  // Priority::kWithCorruptBlocks
    ASSERT_TRUE(urbs_to_process[0].empty());
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    sep = urb.SizeEachPriority();
    ASSERT_EQ(sep[0], 0);
    ASSERT_EQ(sep[1], 0);
    ASSERT_EQ(sep[2], 0);
    ASSERT_EQ(sep[3], 0);
    ASSERT_EQ(sep[4], 1); // Corrupt block need to be reserved
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 3, 3));
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(1);
    ASSERT_TRUE(urbs_to_process[0].empty());
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_EQ(urbs_to_process[3].size(), 1);  // Priority::kReplicasBadlyDistributed
    ASSERT_TRUE(urbs_to_process[4].empty());
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 1, 3));
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(1);
    ASSERT_EQ(urbs_to_process[0].size(), 1);  // Priority::kHighestPriority
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_TRUE(urbs_to_process[4].empty());
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 2, 7));
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(1);
    ASSERT_TRUE(urbs_to_process[0].empty());
    ASSERT_EQ(urbs_to_process[1].size(), 1);  // Priority::kVeryUnderReplicated
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_TRUE(urbs_to_process[4].empty());
    Block bb{2, 100, 10000001};
    ASSERT_TRUE(urb.Add(&bi, 3, 7));
    auto urbs_to_process_1 = urb.ChooseUnderReplicatedBlocks(2);
    ASSERT_TRUE(urbs_to_process_1[0].empty());
    ASSERT_TRUE(urbs_to_process_1[1].empty());
    ASSERT_EQ(urbs_to_process_1[2].size(), 1);  // Priority::kUnderReplicated
    ASSERT_TRUE(urbs_to_process_1[3].empty());
    ASSERT_TRUE(urbs_to_process_1[4].empty());
  }
}

TEST(UnderReplicatedBlocks, Remove) {
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 0, 1));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 1);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    ASSERT_TRUE(urb.Remove(bi.blk(), 0, 1));
    ASSERT_EQ(urb.size(), 0);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 0);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 1, 3));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_TRUE(urb.Remove(bi.blk(), 2, 3));  // hint is not right
    ASSERT_EQ(urb.size(), 0);
  }
}

TEST(UnderReplicatedBlocks, Update) {
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 0, 1));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 1);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    urb.Update(&bi, 1, 3, 1, 2);
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 0);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(2);
    ASSERT_EQ(urbs_to_process[0].size(), 1);  // Priority::kHighestPriority
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_TRUE(urbs_to_process[4].empty());
  }
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 1, 3));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 0);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);
    urb.Update(&bi, 0, 1, -1, -2);
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 1);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(2);
    ASSERT_TRUE(urbs_to_process[0].empty());
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_EQ(urbs_to_process[4].size(), 1);  // Priority::kWithCorruptBlocks
  }
}

TEST(UnderReplicatedBlocks, ChooseUnderReplicatedBlocks) {
  {
    UnderReplicatedBlocks urb;
    BlockInfo bi;
    bi.Init(1,
            0,
            {1, 100, 100000000},
            3,
            cloudfs::DATANODE_BLOCK,
            BlockUCState::kComplete);
    BlockInfo bi2;
    bi2.Init(1,
             0,
             {2, 100, 100000000},
             3,
             cloudfs::DATANODE_BLOCK,
             BlockUCState::kComplete);
    ASSERT_TRUE(urb.Add(&bi, 0, 1, "Decommission"));
    ASSERT_TRUE(urb.Add(&bi2, 0, 1, "Decommission"));
    ASSERT_EQ(urb.size(), 2);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 2);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 2);
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(2, 1);
    ASSERT_TRUE(urbs_to_process[0].empty());  // Priority::kHighestPriority
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_EQ(urbs_to_process[4].size(), 1);
    uint32_t total = 0;
    std::vector<UnderReplicatedBlocks::BlockCorruptHint> result;
    urb.ListCorruptBlocks(0, 5, &total, &result);
    ASSERT_EQ(total, 2);
    ASSERT_EQ(result.size(), 2);

    urb.Update(&bi, 1, 3, 1, 2);
    ASSERT_EQ(urb.size(), 2);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 1);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 1);
    urbs_to_process = urb.ChooseUnderReplicatedBlocks(2, 1);
    ASSERT_EQ(urbs_to_process[0].size(), 1);  // Priority::kHighestPriority
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_TRUE(urbs_to_process[3].empty());
    ASSERT_EQ(urbs_to_process[4].size(), 1);
  }
}

TEST(UnderReplicatedBlocks, MoveBack) {
  {
    UnderReplicatedBlocks urb;
    Block b{1, 100, 100000000};
    ASSERT_TRUE(urb.MoveBack(
        b, UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed));
    ASSERT_EQ(urb.size(), 1);
    ASSERT_EQ(urb.GetCorruptBlockSize(), 0);
    ASSERT_EQ(urb.GetCorruptReplOneBlockSize(), 0);

    ASSERT_FALSE(urb.MoveBack(
        b, UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed));
    ASSERT_FALSE(
        urb.MoveBack(b, UnderReplicatedBlocks::Priority::kWithCorruptBlocks));
    auto urbs_to_process = urb.ChooseUnderReplicatedBlocks(2);
    ASSERT_EQ(urbs_to_process[0].size(), 0);  // Priority::kHighestPriority
    ASSERT_TRUE(urbs_to_process[1].empty());
    ASSERT_TRUE(urbs_to_process[2].empty());
    ASSERT_EQ(urbs_to_process[3].size(), 1);
    ASSERT_TRUE(urbs_to_process[4].empty());
  }
}

TEST(UnderReplicatedBlocks, ConvertIOPriority) {
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_CRITICAL,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kHighestPriority));
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_REAL_TIME,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kVeryUnderReplicated));
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_ELASTIC,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kUnderReplicated));
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_ELASTIC,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kReplicasBadlyDistributed));
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_BEST_EFFORT,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kWithCorruptBlocks));
  ASSERT_EQ(cloudfs::IOPriority::PRIORITY_SCAVENGER,
            UnderReplicatedBlocks::ToIOPriority(
                UnderReplicatedBlocks::Priority::kLast));
}

TEST(UnderReplicatedBlocks, DISABLED_BlockHash) {
  std::unordered_set<Block, BlockHash> queue;
  for (uint64_t i = 0; i < 1000000; ++i) {
    queue.insert(Block{i, static_cast<uint32_t>(i), i});
  }
  for (auto iter = queue.begin(); iter != queue.end(); ++iter) {
    LOG(INFO) << *iter;
  }
}

TEST(UnderReplicatedBlocks, DISABLED_BlockHashUnordered) {
  std::unordered_set<Block, BlockHashUnordered> queue;
  for (uint64_t i = 0; i < 1000000; ++i) {
    queue.insert(Block{i, static_cast<uint32_t>(i), i});
  }
  for (auto iter = queue.begin(); iter != queue.end(); ++iter) {
    LOG(INFO) << *iter;
  }
}

}  // namespace dancenn

