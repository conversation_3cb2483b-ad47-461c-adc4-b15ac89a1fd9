// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "test/dancenn_test_enviroment.h"

#include <gtest/gtest.h>  // NOLINT(build/include_subdir)
#include <glog/logging.h>  // NOLINT(build/include_subdir)

#include <string>

namespace dancenn {

int g_argc = 0;
char** g_argv = nullptr;

const char* g_edits_dir = "/tmp/dancenn/nndata/current";
std::shared_ptr<JavaRuntime> g_java_runtime = nullptr;
std::shared_ptr<HAEditLogContext> g_edit_log_ctx = nullptr;

std::string GetProgramDirectory() {
  CHECK_GT(g_argc, 0) << "g_argc is less than 0!";
  char buf[1024];
  CHECK_NOTNULL(getcwd(buf, 1024));
  std::string res = buf;
  if (g_argv[0][0] != '/') {
    res.append(1, '/');
    res.append(g_argv[0]);
  }
  auto pos = res.rfind("/");
  return res.substr(0, pos);
}

}  // namespace dancenn
