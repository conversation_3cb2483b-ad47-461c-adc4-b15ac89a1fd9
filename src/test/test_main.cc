// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <aws/core/Aws.h>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <google/protobuf/stubs/common.h>
#include <gtest/gtest.h>
#include <openssl/crypto.h>
#include <openssl/err.h>
#include <openssl/evp.h>

#include "base/logger_metrics.h"
#include "base/metrics.h"
#include "test/dancenn_test_enviroment.h"

DECLARE_bool(run_ut);
DECLARE_bool(dfs_meta_storage_inode_key_v2);
DECLARE_int32(ut_loglevel_v);

int main(int argc, char* argv[]) {
  auto writer = [](const char* data, int size) {
    if (write(STDERR_FILENO, data, size) < 0) {
    }
  };

  // google::InitGoogleLogging(argv[0]);
  google::InstallFailureSignalHandler();
  google::InstallFailureWriter(writer);
  google::SetVLOGLevel("*", FLAGS_ut_loglevel_v);
  testing::InitGoogleTest(&argc, argv);
  // google::ParseCommandLineFlags(&argc, &argv, false);
  testing::AddGlobalTestEnvironment(
      new dancenn::DancennTestEnviroment(argc, argv));
  FLAGS_run_ut = true;
  FLAGS_dfs_meta_storage_inode_key_v2 = true;

  dancenn::LoggerMetrics::Instance();

  Aws::SDKOptions aws_sdk_options;
  aws_sdk_options.loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Debug;
  aws_sdk_options.httpOptions.installSigPipeHandler = true;
  Aws::InitAPI(aws_sdk_options);

  int r = RUN_ALL_TESTS();

  google::protobuf::ShutdownProtobufLibrary();
  // https://github.com/gflags/gflags/issues/51
  google::ShutDownCommandLineFlags();
  // Skip it to avoid wierd coredump in ByteCycle CI environment.
  // However, it's just a guess.
  // Aws::ShutdownAPI(aws_sdk_options);
  // https://stackoverflow.com/questions/29008145/valgrind-shows-memory-leak-in-ssl-after-closing-the-connection
  CRYPTO_cleanup_all_ex_data();
  ERR_free_strings();
  ERR_remove_state(0);
  EVP_cleanup();

  return r;
}

