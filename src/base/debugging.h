#pragma once

#include <cxxabi.h>
#include <execinfo.h>
#include <glog/logging.h>

#include <iostream>

namespace dancenn {
namespace debugging {

void PrintStackTrace() {
  const int max_frames = 64;
  void* frame[max_frames];
  int frame_count = backtrace(frame, max_frames);
  char** symbols = backtrace_symbols(frame, frame_count);

  std::ostringstream oss;
  for (int i = 0; i < frame_count; ++i) {
    std::string symbol(symbols[i]);
    size_t demangle_start = symbol.find("(") + 1;
    size_t demangle_end = symbol.find("+");
    if (demangle_start != std::string::npos &&
        demangle_end != std::string::npos) {
      std::string to_demangle =
          symbol.substr(demangle_start, demangle_end - demangle_start);
      int status = 0;
      char* demangled_name =
          abi::__cxa_demangle(to_demangle.c_str(), NULL, NULL, &status);
      if (status == 0 && demangled_name != NULL) {
        symbol = symbol.replace(
            demangle_start, demangle_end - demangle_start, demangled_name);
        free(demangled_name);
      }
    }

    oss << "\n[frame " << i << "] " << symbol;
  }

  free(symbols);
  LOG(INFO) << oss.str();
}

}  // namespace debugging
}  // namespace dancenn