//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#pragma once

#include <string>

#include "status.h"

namespace dancenn {

class Base64 {
 public:
  static Status Encode(const std::string& input, std::string* output);
  static Status Decode(const std::string& b64input, std::string* output);
  static size_t CalculateBase64DecodedLength(const std::string& b64input);
  static size_t CalculateBase64EncodedLength(const std::string& buffer);
};

}  // namespace dancenn
