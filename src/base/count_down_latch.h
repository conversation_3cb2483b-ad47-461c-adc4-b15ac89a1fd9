// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_COUNT_DOWN_LATCH_H_
#define BASE_COUNT_DOWN_LATCH_H_

#include <cstdint>
#include <chrono>
#include <mutex>
#include <condition_variable>

namespace dancenn {

class CountDownLatch {
 public:
  explicit CountDownLatch(size_t count) : count_(count) {
  }

  void Reset(size_t count) {
    count_ = count;
  }

  void CountDown();

  bool Await(std::chrono::milliseconds timeout = std::chrono::milliseconds(-1));

 private:
  size_t count_ { 0 };
  std::mutex mutex_;
  std::condition_variable cv_;
};

}  // namespace dancenn

#endif  // BASE_COUNT_DOWN_LATCH_H_

