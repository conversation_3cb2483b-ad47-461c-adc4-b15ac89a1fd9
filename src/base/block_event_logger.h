// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/05/27
// Description

#ifndef BASE_BLOCK_EVENT_LOGGER_H_
#define BASE_BLOCK_EVENT_LOGGER_H_

#include <gflags/gflags.h>
#include <string>
#include <utility>
#include <vector>

#include "block_manager/block.h"
#include "datanode_manager/datanode_info.h"

namespace dancenn {

class BlockEventLogger {
 public:
  static void ChooseTarget4Recover(const std::string& src_path,
                                   const std::vector<DatanodeID>& dn_ids);
  static void ChooseTarget4New(const std::string& src_path,
                               const std::vector<DatanodeID>& dn_ids);
  static void ChooseTarget4Read(BlockID blk_id,
                                const std::vector<DatanodeID>& dn_ids);
  static void DeleteReplica(BlockID blk_id, DatanodeID dn_id);
};

}  // namespace dancenn

#endif  // BASE_BLOCK_EVENT_LOGGER_H_
