// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/11/22
// Description
//
// This class is solely utilized by HAState::LockBarrierForHASwitcher. Please
// refrain from employing it in any other context or location.
//
// The SwitchOp::operator()() function requires a write lock on
// VersionRWLock to deny additional client or datanode write requests that aim
// to write editlogs. However, invoking vunique_lock::vunique_lock ->
// VLock::lock may result in a deadlock if there are ongoing write requests that
// have taken a read lock on VersionRWLock.
//
// Given the implementation of VersionRWLock, acquiring a write lock can be
// achieved in two steps. The initial step denies further attempts to acquire a
// read lock, while the second step completes the process of acquiring the write
// lock, ensuring no other read locks are present. This functionality has been
// implemented as TwoStepVLock and TwoStepVUniqueLock.

#ifndef BASE_TWO_STEP_VLOCK_H_
#define BASE_TWO_STEP_VLOCK_H_

#include <memory>  // For unique_ptr, make_unique.

#include "base/vlock.h"  // For VersionRWLock.

namespace dancenn {

// Refer to VLock.
class TwoStepVLock {
 public:
  explicit TwoStepVLock(VersionRWLock* lock);
  TwoStepVLock(const TwoStepVLock& other) = delete;
  TwoStepVLock(TwoStepVLock&& other) = delete;
  TwoStepVLock& operator=(const TwoStepVLock& other) = delete;
  TwoStepVLock& operator=(TwoStepVLock&& other) = delete;

  void Prepare();
  void Lock();
  void Unlock();

 private:
  VersionRWLock* lock_{nullptr};
};

// Refer to vunique_lock.
class TwoStepVUniqueLock {
 public:
  TwoStepVUniqueLock() = default;
  explicit TwoStepVUniqueLock(VersionRWLock* lock);
  TwoStepVUniqueLock(const TwoStepVUniqueLock& other) = delete;
  TwoStepVUniqueLock(TwoStepVUniqueLock&& other) = default;
  TwoStepVUniqueLock& operator=(const TwoStepVUniqueLock& other) = delete;
  TwoStepVUniqueLock& operator=(TwoStepVUniqueLock&& other) = default;
  ~TwoStepVUniqueLock();

  void Lock();
  operator bool() const;

 private:
  std::unique_ptr<TwoStepVLock> vlock_;
  bool is_locked_;
};

}  // namespace dancenn

#endif  // BASE_TWO_STEP_VLOCK_H_
