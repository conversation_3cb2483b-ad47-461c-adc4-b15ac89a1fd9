// Copyright 2017 T<PERSON>yi He <<EMAIL>>

#ifndef BASE_JAVA_H_
#define BASE_JAVA_H_

#include <glog/logging.h>
#include <jni.h>
#include <cstdint>
#include <cstring>

#include <string>
#include <sstream>
#include <stdexcept>
#include <memory>

namespace dancenn {

void AppendExceptionTraceMessages(JNIEnv* env,
                                  jthrowable exception,
                                  jmethodID throwable_get_cause,
                                  jmethodID throwable_get_stacktrace,
                                  jmethodID throwable_to_string,
                                  jmethodID frame_to_string,
                                  std::string* err_msg);

#define CHECK_JVM_EXCEPTION(jvm)                                               \
  do {                                                                         \
    auto env = (jvm)->env();                                                   \
    if (env->ExceptionCheck() == JNI_TRUE) {                                   \
      jthrowable exception(env->ExceptionOccurred());                          \
      env->ExceptionClear();                                                   \
      if (exception) {                                                         \
        jclass throwable_class = env->FindClass("java/lang/Throwable");        \
        jmethodID throwable_get_cause = env->GetMethodID(                      \
            throwable_class, "getCause", "()Ljava/lang/Throwable;");           \
        jmethodID throwable_get_stacktrace =                                   \
            env->GetMethodID(throwable_class,                                  \
                             "getStackTrace",                                  \
                             "()[Ljava/lang/StackTraceElement;");              \
        jmethodID throwable_to_string = env->GetMethodID(                      \
            throwable_class, "toString", "()Ljava/lang/String;");              \
        jclass frame_class = env->FindClass("java/lang/StackTraceElement");    \
        jmethodID frame_to_string =                                            \
            env->GetMethodID(frame_class, "toString", "()Ljava/lang/String;"); \
        std::string err_msg;                                                   \
        dancenn::AppendExceptionTraceMessages(env,                             \
                                              exception,                       \
                                              throwable_get_cause,             \
                                              throwable_get_stacktrace,        \
                                              throwable_to_string,             \
                                              frame_to_string,                 \
                                              &err_msg);                       \
        LOG(FATAL) << err_msg;                                                 \
      }                                                                        \
    }                                                                          \
  } while (0)

// Refer to CHECK_JVM_EXCEPTION.
#define THROW_JVM_EXCEPTION(jvm)                                               \
  do {                                                                         \
    auto env = (jvm)->env();                                                   \
    if (env->ExceptionCheck() == JNI_TRUE) {                                   \
      jthrowable exception(env->ExceptionOccurred());                          \
      env->ExceptionClear();                                                   \
      if (exception) {                                                         \
        jclass throwable_class = env->FindClass("java/lang/Throwable");        \
        jmethodID throwable_get_cause = env->GetMethodID(                      \
            throwable_class, "getCause", "()Ljava/lang/Throwable;");           \
        jmethodID throwable_get_stacktrace =                                   \
            env->GetMethodID(throwable_class,                                  \
                             "getStackTrace",                                  \
                             "()[Ljava/lang/StackTraceElement;");              \
        jmethodID throwable_to_string = env->GetMethodID(                      \
            throwable_class, "toString", "()Ljava/lang/String;");              \
        jclass frame_class = env->FindClass("java/lang/StackTraceElement");    \
        jmethodID frame_to_string =                                            \
            env->GetMethodID(frame_class, "toString", "()Ljava/lang/String;"); \
        std::string err_msg;                                                   \
        dancenn::AppendExceptionTraceMessages(env,                             \
                                              exception,                       \
                                              throwable_get_cause,             \
                                              throwable_get_stacktrace,        \
                                              throwable_to_string,             \
                                              frame_to_string,                 \
                                              &err_msg);                       \
        throw std::runtime_error(err_msg);                                     \
      }                                                                        \
    }                                                                          \
  } while (false)

class JavaRuntime;

// return true if has exception
bool CatchJVMException(JavaRuntime* jvm);

class JavaObject {
 public:
  JavaObject(JavaRuntime *jvm, jclass clazz, jobject obj);
  ~JavaObject();

  jobject ref() const {
    return obj_ref_;
  }

  jclass clazz() const {
    return clazz_;
  }

 private:
  JavaRuntime* jvm_{nullptr};
  jclass clazz_;
  jobject obj_ref_;
};

class JavaRuntime {
 public:
  JavaRuntime(const std::string &classpath, int heap_size_mb);
  ~JavaRuntime();

  void DetachCurrentThread();

  JavaObject NewInstance(const std::string &class_name);

  JNIEnv *env() {
    if (env_ == nullptr) {
      AttachCurrentThread();
    }
    return env_;
  }

 private:
  JavaVM *jvm_;
  thread_local static JNIEnv *env_;

  void AttachCurrentThread();
};

}  // namespace dancenn

#endif  // BASE_JAVA_H_

