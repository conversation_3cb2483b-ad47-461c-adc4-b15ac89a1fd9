// Copyright 2019 liaocheng <<EMAIL>>

#ifndef BASE_STRING_UTILS_H_
#define BASE_STRING_UTILS_H_

#include <iostream>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "base/murmur_hash.h"
#include "cnetpp/base/string_piece.h"

namespace dancenn {

using StringPiece = cnetpp::base::StringPiece;

class StringUtils {
 public:
  static bool StringToInt32(const char* s, size_t length, int32_t* l);
  static bool StringToInt64(const char* s, size_t length, int64_t* l);
  static std::string ToHexString(const std::string& str);
  static std::string HexStringTo(const std::string& str);
  static bool IsHexString(const std::string& str);
  static bool IsValidUTF8(const std::string& str);

  static std::vector<std::string> SplitByChars(String<PERSON>iece str,
                                               StringPiece separators);
  static void SplitByChars(StringPiece str,
                           StringPiece separators,
                           std::vector<std::string>* result);
  static void PutFixed16(std::string* dst, uint16_t value);
  static void PutFixed32(std::string* dst, uint32_t value);
  static void PutFixed64(std::string* dst, uint64_t value);

  static bool GetFixed16(StringPiece* input, uint16_t* value);
  static bool GetFixed32(StringPiece* input, uint32_t* value);
  static bool GetFixed64(StringPiece* input, uint64_t* value);

  static std::string BoolToString(bool b);

  // "*************:9208" -> ":" got "*************" and "9208"
  // "[fdbd:dc02:103:73::162]:9209" -> ":" got "[fdbd:dc02:103:73::162]" and
  // "9209" "fdbd:dc02:103:73::162:9209" -> ":" got "fdbd:dc02:103:73::162" and
  // "9209"
  static void SplitStringForAddr(const std::string& str,
                                 const char* delimiter,
                                 std::vector<std::string>* result);

  // trim '[' and ']'
  static void TrimIPString(std::string* ip_str);

  class comma_numpunct : public std::numpunct<char> {
   protected:
    char do_thousands_sep() const override {
      return ',';
    }

    std::string do_grouping() const override {
      return "\03";
    }
  };

  template <class T>
  static std::string FormatWithCommas(T value) {
    static std::locale comma_locale(std::locale(), new comma_numpunct());

    std::stringstream ss;
    ss.imbue(comma_locale);
    ss << std::fixed << value;
    return ss.str();
  }
};

}  // namespace dancenn

namespace std {
template <>
struct hash<dancenn::StringPiece> {
  size_t operator()(const dancenn::StringPiece& sp) const {
    static const uint32_t kSeed = static_cast<uint32_t>(0xc70f6907UL);
    return static_cast<size_t>(
        dancenn::murmur_hash_32(sp.data(), sp.length(), kSeed));
  }
};
}  // namespace std

#endif  // BASE_STRING_UTILS_H_

