// Copyright 2020 Mu <PERSON> <<EMAIL>>

#include "trusted_ip_table.h"

#include <cstdio>
#include <cstdlib>
#include <string>
#include <vector>
#include <algorithm>

#include <cnetpp/base/string_utils.h>
#include <glog/logging.h>

#include "base/file_utils.h"

DECLARE_string(trusted_ip_file);
DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);

namespace dancenn {
namespace security {

bool IsTrustedIp(const std::string& ip) {
  return TrustedIpTable::GetSingleton().IsTrustedIp(ip);
}

std::vector<std::string> ListTrustedIp() {
  return TrustedIpTable::GetSingleton().ListTrustedIp();
}

std::string ListTrustedIpJsonString() {
  auto trusted_ip_list = ListTrustedIp();

  std::ostringstream os;
  os << "[\n";
  for (auto iter = trusted_ip_list.begin();
       iter != trusted_ip_list.end(); ++iter) {
    if (iter != trusted_ip_list.begin()) {
      os << ",";
    }
    os << "\"" << *iter << "\"";
  }
  os << "\n]";
  return os.str();
}

bool UpdatedTrustedIp() {
  auto config_string = FileUtils::TryReadConfig(FLAGS_trusted_ip_file);

  if (config_string.empty()) {
    return false;
  }

  return TrustedIpTable::GetSingleton().ParseAndUpdate(config_string);
}

TrustedIpTable& TrustedIpTable::GetSingleton() {
  static TrustedIpTable trusted_ip_table;
  return trusted_ip_table;
}

TrustedIpTable::TrustedIpTable()
    : rwlock_(FLAGS_client_normal_rpc_handler_count +
              FLAGS_client_slow_rpc_handler_count + 1) {
  if (!refresher_) {
    refresher_ = std::make_unique<Refresher>("trusted-ip-refresher",
                                             [&]() { UpdatedTrustedIp(); });
    refresher_->Start();
  }
}

TrustedIpTable::~TrustedIpTable() {
  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

bool TrustedIpTable::IsTrustedIp(const std::string& ip) {
  vshared_lock guard(rwlock_.lock());

  return trusted_ips_.count(ip) != 0;
}

std::vector<std::string> TrustedIpTable::ListTrustedIp() {
    vshared_lock guard(rwlock_.lock());

    std::vector<std::string> result;

    result.reserve(trusted_ips_.size());
    result.insert(result.end(), trusted_ips_.begin(), trusted_ips_.end());
    sort(result.begin(), result.end());
    return result;
}


bool TrustedIpTable::ParseAndUpdate(const std::string& config_string) {
  // check
  {
    vshared_lock guard(rwlock_.lock());

    if (config_string == config_string_) {
      return true;
    }
  }

  // update
  {
    vunique_lock guard(rwlock_.lock());

    if (config_string == config_string_) {
      return true;
    }

    config_string_ = config_string;
    LOG(INFO) << "New Trusted Ip config_string: " << config_string;

    // Deserialize
    auto origin_v = cnetpp::base::StringUtils::SplitByChars(config_string, "\n");

    // Filter
    std::vector<std::string> v;
    std::copy_if(
        origin_v.begin(), origin_v.end(), std::back_inserter(v),
        [](const std::string& s) { return s.size() > 0 && s[0] != '#'; });

    return UpdateConfig(std::move(v));
  }
}

bool TrustedIpTable::UpdateConfig(std::vector<std::string> v) {
  trusted_ips_.clear();

  sort(v.begin(), v.end());

  std::stringstream ss;
  for (auto& ip : v) {
    ss << ip << " ";
    trusted_ips_.insert(ip);
  }
  LOG(INFO) << "Trusted Ip: " << ss.str();

  return true;
}

}  // namespace security
}  // namespace dancenn
