// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_JAVA_EXCEPTIONS_H_
#define BASE_JAVA_EXCEPTIONS_H_

#include <glog/logging.h>
#include <cnetpp/base/string_piece.h>

#include <map>
#include <string>

namespace dancenn {

class JavaExceptions {
 public:
  enum Exception {
    kIOException = 0,
    kRpcServerException,
    kSaslException,
    kVersionMismatch,
    kIncorrectVersionException,
    kInconsistentFSStateException,
    kAccessControlException,
    kFileNotFoundException,
    kFileAlreadyExistsException,
    kParentNotDirectoryException,
    kUnresolvedLinkException,
    kSafeModeException,
    kRpcNoSuchMethodException,
    kStandbyException,
    kIllegalArgumentException,
    kInvalidTopologyException,
    kUnsupportedOperationException,
    kAlreadyBeingCreatedException,
    kRecoveryInProgressException,
    kInvalidPathException,
    kHadoopIllegalArgumentException,
    kPathIsNotEmptyDirectoryException,
    kLeaseExpiredException,
    kNotReplicatedYetException,
    kInvalidRequestException,
    kReadOnlyCoolFileException,
    kNoException,
    kHyperFileException,
    kRangerAccessControlException,
    kThrottlerException,
    kSnapshotException,

    // https://hadoop.apache.org/docs/stable/hadoop-project-dist/hadoop-hdfs/HdfsQuotaAdminGuide.html
    // There are 3 kinds of quotas in hdfs:
    // 1. Name Quotas
    //    The name quota is a hard limit on the number of file and directory
    //    names in the tree rooted at that directory.
    //    File and directory creations fail if the quota would be exceeded.
    // 2. Space Quotas
    //    The space quota is a hard limit on the number of bytes used by files
    //    in the tree rooted at that directory.  Block allocations fail
    //    if the quota would not allow a full block to be written.
    //    Quotas are charged at the intended replication factor for the file;
    //    changing the replication factor for a file will credit or debit
    //    quotas.
    // 3. Storage Type Quotas
    //    The storage type quota is a hard limit on the usage of specific
    //    storage type (SSD, DISK, ARCHIVE) by files in the tree rooted at the
    //    directory.
    // NOTICE: All the above quotas stick with renamed directories.
    //
    // There are 2 kinds of quotas in cfs:
    // 1. Name Quotas: Same as hdfs.
    // 2. Logical Space Quotas: Unlike Space Quotas in hdfs, cfs doesn't
    //    consider replication factor. This is why we call them Logical Space
    //    Quotas.
    // NOTICE: All the above quotas stick with the renamed directories.
    //
    // hadoop/hdfs/protocol/QuotaExceededException.java
    kQuotaExceededException,
    kDSQuotaExceededException,
    kNSQuotaExceededException,
    kMaxDirectoryItemsExceededException,
  };

  static constexpr const char *IOException() {
    return "java.io.IOException";
  }

  static constexpr const char *RpcServerException() {
    return "com.bytedance.cloudfs.ipc.RpcServerException";
  }

  static constexpr const char *SaslException() {
    return "javax.security.sasl.SaslException";
  }

  static constexpr const char *VersionMismatch() {
    return "com.bytedance.cloudfs.ipc.RPC$VersionMismatch";
  }

  static constexpr const char *IncorrectVersionException() {
    return "com.bytedance.cloudfs.server.common.IncorrectVersionException";
  }

  static constexpr const char *InconsistentFSStateException() {
    return "com.bytedance.cloudfs.server.common.InconsistentFSStateException";
  }

  static constexpr const char *AccessControlException() {
    return "com.bytedance.cloudfs.security.AccessControlException";
  }

  static constexpr const char *FileNotFoundException() {
    return "java.io.FileNotFoundException";
  }

  static constexpr const char *FileAlreadyExistsException() {
    return "com.bytedance.cloudfs.fs.FileAlreadyExistsException";
  }

  static constexpr const char *ParentNotDirectoryException() {
    return "com.bytedance.cloudfs.fs.ParentNotDirectoryException";
  }

  static constexpr const char *UnresolvedLinkException() {
    return "com.bytedance.cloudfs.fs.UnresolvedLinkException";
  }

  static constexpr const char *SafeModeException() {
    return "com.bytedance.cloudfs.server.namenode.SafeModeException";
  }

  static constexpr const char *RpcNoSuchMethodException() {
    return "com.bytedance.cloudfs.ipc.RpcNoSuchMethodException";
  }

  static constexpr const char *RpcNoSuchProtocolException() {
    return "com.bytedance.cloudfs.ipc.RpcNoSuchProtocolException";
  }

  static constexpr const char *StandbyException() {
    return "com.bytedance.cloudfs.ipc.StandbyException";
  }

  static constexpr const char* IllegalArgumentException() {
    return "java.lang.IllegalArgumentException";
  }

  static constexpr const char* InvalidTopologyException() {
    return "com.bytedance.cloudfs.net.InvalidTopologyException";
  }

  static constexpr const char* AlreadyBeingCreatedException() {
    return "com.bytedance.cloudfs.protocol.AlreadyBeingCreatedException";
  }

  static constexpr const char* RecoveryInProgressException() {
    return "com.bytedance.cloudfs.protocol.RecoveryInProgressException";
  }

  static constexpr const char* InvalidPathException() {
    return "com.bytedance.cloudfs.fs.InvalidPathException";
  }

  static constexpr const char* HadoopIllegalArgumentException() {
    return "com.bytedance.cloudfs.HadoopIllegalArgumentException";
  }

  static constexpr const char* PathIsNotEmptyDirectoryException() {
    return "com.bytedance.cloudfs.fs.PathIsNotEmptyDirectoryException";
  }

  static constexpr const char* LeaseExpiredException() {
    return "com.bytedance.cloudfs.server.namenode.LeaseExpiredException";
  }

  static constexpr const char* NotReplicatedYetException() {
    return "com.bytedance.cloudfs.server.namenode.NotReplicatedYetException";
  }

  static constexpr const char* InvalidRequestException() {
    return "com.bytedance.cloudfs.fs.InvalidRequestException";
  }

  static constexpr const char* ReadOnlyCoolFileException() {
    return "com.bytedance.cloudfs.server.namenode.ReadOnlyCoolFileException";
  }

  static  constexpr const char* HyperFileException() {
    return "com.bytedance.cloudfs.server.namenode.HyperFileException";
  }

  static constexpr const char* QuotaExceededException() {
    return "org.apache.hadoop.hdfs.protocol.QuotaExceededException";
  }

  static constexpr const char* DSQuotaExceededException() {
    return "com.bytedance.cloudfs.protocol.DSQuotaExceededException";
  }

  static constexpr const char* NSQuotaExceededException() {
    return "com.bytedance.cloudfs.protocol.NSQuotaExceededException";
  }

  static constexpr const char* MaxDirectoryItemsExceededException() {
    return "org.apache.hadoop.hdfs.protocol.MaxDirectoryItemsExceededException";
  }

  static constexpr const char* NoException() {
    return "NoException";
  }

  static constexpr const char* UnsupportedOperationException() {
    return "java.lang.UnsupportedOperationException";
  }

  static constexpr const char* RangerAccessControlException() {
    return "org.apache.ranger.authorization.hadoop.exceptions.RangerAccessControlException";
  }

  static constexpr const char* ThrottlerException() {
    return "org.apache.hadoop.ipc.NamenodeThrottlerException";
  }

  static constexpr const char* SnapshotException() {
    return "com.bytedance.cloudfs.protocol.SnapshotException";
  }

  static const std::map<cnetpp::base::StringPiece, Exception> exceptions_;
  static const std::map<Exception, cnetpp::base::StringPiece> exception2str_;

  static const Exception ExceptionType(const char* e) {
    auto itr = exceptions_.find(e);
    if (itr == exceptions_.end()) {
      return kIOException;
    }
    return itr->second;
  }

  static const char* ExceptionStr(Exception e) {
    auto it = exception2str_.find(e);
    if (it == exception2str_.end()) {
      return IOException();
    }
    return it->second.data();
  }
};

}  // namespace dancenn

#endif  // BASE_JAVA_EXCEPTIONS_H_

