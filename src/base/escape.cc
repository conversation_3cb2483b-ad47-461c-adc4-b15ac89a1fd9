//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "escape.h"

#include <curl/curl.h>
#include <glog/logging.h>

#include <memory>

namespace dancenn {

std::string Escape::UriEscape(const std::string& input) {
  // https://curl.se/libcurl/c/threadsafe.html
  // You must never share the same handle in multiple threads.
  // You can pass the handles around among threads, but you must never
  // use a single handle from more than one thread at any given time.
  thread_local std::unique_ptr<CURL, std::function<void(CURL*)>> curl_handler(
      curl_easy_init(), [](CURL* curl_handler) {
        if (curl_handler) {
          curl_easy_cleanup(curl_handler);
        }
      });
  CHECK(curl_handler) << "curl_handler not initialized.";
  std::unique_ptr<char, std::function<void(char*)>> output(
      curl_easy_escape(curl_handler.get(),
                       input.c_str(),
                       // If length is set to 0 (zero), curl_easy_escape uses
                       // strlen() on the input string to find out the size.
                       0),
      [](char* output) {
        if (output) {
          curl_free(output);
        }
      });
  return output.get();
}

}  // namespace dancenn
