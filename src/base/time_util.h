// Copyright 2021 xiongmu <<EMAIL>>

#ifndef BASE_TIME_UTILS_H_
#define BASE_TIME_UTILS_H_

#include <chrono>
#include <ctime>
#include <functional>
#include <iomanip>
#include <iostream>
#include <string>
#include <utility>

namespace dancenn {

class TimeUtil {
 public:
  static uint64_t GetNowEpochMs();

  static int64_t GetSecondsSince(
      const std::chrono::steady_clock::time_point& start);

  // A small range stop watch, return the deadline from now with the desired
  // milliseconds.
  static uint64_t GetNowEpochMsFor(int mills);

  static std::string EpochMsToString(uint64_t epoch_ms);

  static std::string EpochToString(uint64_t epoch);

  static uint64_t ToSeconds(const std::chrono::system_clock::time_point& s,
                            const std::chrono::system_clock::time_point& e);

  static uint64_t ToMilliSeconds(
      const std::chrono::system_clock::time_point& s,
      const std::chrono::system_clock::time_point& e);

  static uint64_t ToMicroSeconds(
      const std::chrono::system_clock::time_point& s,
      const std::chrono::system_clock::time_point& e);

  static uint64_t ToNanoSeconds(const std::chrono::system_clock::time_point& s,
                                const std::chrono::system_clock::time_point& e);

  // format time `epoch` second to string with give `patten`, extreme long
  // `patten` may cause buffer overflow
  static std::string GetTimeFormatted(uint64_t epoch,
                                      const std::string& patten);

  static std::string GetNowYMD();
  static uint64_t YMDToSec(const std::string& ymd);

  static uint64_t GetEpochMsByFormat(const std::string& time,
                                     const std::string& format);
};

// Testable TimeUtil.
class TimeUtilV2 {
 public:
  static std::function<uint64_t()> GetNowEpochMs;
  static std::function<uint64_t()> GetNowEpochMs4Log;

  static void TestOnlySetGetNowEpochMsFunc(const std::function<uint64_t()>& f);
};

}  // namespace dancenn

#endif  // BASE_TIME_UTILS_H_
