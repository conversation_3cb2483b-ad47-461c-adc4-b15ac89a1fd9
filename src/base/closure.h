// Copyright 2017 <PERSON><PERSON> Huang <<EMAIL>>

#ifndef BASE_CLOSURE_H_
#define BASE_CLOSURE_H_

#include <google/protobuf/stubs/common.h>  // Closure

#include <list>
#include <shared_mutex>
#include <string>

#include "base/retry_cache.h"
#include "base/rwlock_manager.h"
#include "base/status.h"
#include "base/vlock.h"
#include "service/method_recorder.h"

namespace dancenn {

class Closure : public google::protobuf::Closure {
 public:
  void set_status(Status&& status) { status_ = std::move(status); }

  const Status& status() const { return status_; }

  void set_barrier(dancenn::vshared_lock&& barrier) {
    barrier_ = std::move(barrier);
  }

  bool OwnsLock() {
    return barrier_.owns_lock();
  }

  virtual void Run() = 0;

  virtual bool IsSlow() const { return true; }

  virtual std::string DebugString() {
    std::ostringstream oss;
    oss << "[Closure]";
    oss << " slow=" << (IsSlow() ? "true" : "false");
    oss << " st=" << status_.ToString();
    return oss.str();
  }

  // If partition_id >= 0, closures with the same partition_id should be run in
  // sequence.
  virtual int64_t partition_id() const { return -1; }

 private:
  Status status_;
  dancenn::vshared_lock barrier_;
};

class SynchronizedClosure : public dancenn::Closure {
 public:
  SynchronizedClosure() : latch_(1) {}

  SynchronizedClosure(size_t num_signal) : latch_(num_signal) {}

  void Run() override {
    latch_.CountDown();
  }

  // Block the thread until Run() has been called
  void Await() {
    latch_.Await();
  }

  bool IsSlow() const override { return false; }

  std::string DebugString() override {
    std::ostringstream oss;
    oss << "[SynchronizedClosure]";
    oss << " ";
    oss << Closure::DebugString();
    return oss.str();
  }

 private:
  CountDownLatch latch_;
};

class RpcClosure : public dancenn::Closure {
 public:

  using FunctionType = std::function<void(const Status& status)>;

  RpcClosure(FunctionType func, bool self_deleting)
      : function_(std::move(func)), self_deleting_(self_deleting) {
  }

  void Run() override {
    RPC_SW_CTX_INIT_BY_CTX(rpc_sw_ctx, ctl());
    RPC_SW_CTX_LOG(rpc_sw_ctx, "before RpcClosure");

    bool needs_delete = this->self_deleting();
    auto func = this->callback();
    if (func != nullptr) {
      func(this->status());
    }
    set_callback(nullptr);

    // Trigger post callbacks
    for (auto it = post_functions_.rbegin(); it != post_functions_.rend();
         ++it) {
      FunctionType& post_func = *it;
      if (post_func != nullptr) {
        post_func(this->status());
        post_func = nullptr;
      }
    }
    clear_post_callback();
    ReleaseLocks();
    if (needs_delete) {
      delete this;
    }
    // reset all resources

    RPC_SW_CTX_LOG(rpc_sw_ctx, "after RpcClosure");
  }

  virtual dancenn::RpcController* ctl() {
    return nullptr;
  }

  void set_locks(RWLockManager::DirTreeLockPtr&& locks) {
    locks_ = std::move(locks);
  }

  void ReleaseLocks() {
    if (locks_) {
      locks_->clear();
      locks_.reset(nullptr);
    }
  }

  void set_callback(FunctionType func) {
    function_ = std::move(func);
  }

  FunctionType callback() const {
    return function_;
  }

  void add_post_callback(FunctionType func) {
    post_functions_.emplace_back(std::move(func));
  }

  void set_post_callback(std::list<FunctionType> funcs) {
    post_functions_.swap(funcs);
  }

  std::list<FunctionType> post_callback() {
    return post_functions_;
  }

  void clear_post_callback() {
    post_functions_.clear();
  }

  void set_self_deleting(bool self_deleting) {
    self_deleting_ = self_deleting;
  }

  bool self_deleting() const {
    return self_deleting_;
  }

  std::string DebugString() override {
    std::ostringstream oss;
    oss << "[RpcClosure]";
    oss << " has_callback" << (callback() != nullptr ? "true" : "false");
    oss << " ";
    oss << Closure::DebugString();
    return oss.str();
  }

 private:
  RWLockManager::DirTreeLockPtr locks_;

  FunctionType function_{ nullptr };
  std::list<FunctionType> post_functions_;
  bool self_deleting_{ true };
};

class ApplyClosure : public dancenn::RpcClosure {
 public:
  ApplyClosure(bool self_deleting = true)
      : RpcClosure(nullptr, self_deleting) {}

  void set_partition_id(int64_t partition_id) {
    partition_id_ = partition_id;
  }
  int64_t partition_id() const override {
    return partition_id_;
  }
  void Run() override {
    RpcClosure::Run();
  }

 private:
  int64_t partition_id_{-1};
};

class SyncApplyClosure : public ApplyClosure {
 public:
  SyncApplyClosure()
      : ApplyClosure(false),
        latch_(1) {}

  void Run() override {
    ApplyClosure::Run();
    latch_.CountDown();
  }
  void Await() {
    latch_.Await();
  }

 private:
  CountDownLatch latch_;
};

class SynchronizedRpcClosure : public dancenn::RpcClosure {
 public:
  SynchronizedRpcClosure():
      RpcClosure(nullptr, false),
      latch_(1) {}

  void Run() override {
    RpcClosure::Run();
    latch_.CountDown();
  }

  // Block the thread until Run() has been called
  void Await() {
    latch_.Await();
  }

  std::string DebugString() override {
    std::ostringstream oss;
    oss << "[SynchronizedRpcClosure]";
    oss << " ";
    oss << RpcClosure::DebugString();
    return oss.str();
  }

 private:
  CountDownLatch latch_;
};

class FunctionClosure : public dancenn::Closure {
 public:
  FunctionClosure(std::function<void(const Status&)> function,
                  bool self_deleting)
      : function_(std::move(function)), self_deleting_(self_deleting) {}

  void Run() override {
    bool needs_delete = self_deleting_;
    if (function_ != nullptr) {
      function_(status());
    }
    if (needs_delete) {
      delete this;
    }
  }

  void set_callback(std::function<void(const Status&)> func) {
    function_ = func;
  }
 private:
  std::function<void(const Status&)> function_;
  bool self_deleting_;
};

template<class T, class R, class S>
class ServiceRpcClosure : public dancenn::RpcClosure {
 public:
  ServiceRpcClosure(T* service,
                    const std::string& method_name,
                    dancenn::RpcController* ctl,
                    const R* request,
                    S* response,
                    google::protobuf::Closure* done,
                    bool self_deleting = true)
      : request_(request),
        response_(response),
        ctl_(ctl),
        dancenn::RpcClosure(nullptr, self_deleting),
        mr_(service, method_name, ctl, done) {}

  const R* request() const {
    return request_;
  }

  dancenn::RpcController* ctl() override {
    return ctl_;
  }

  S* response() {
    return response_;
  }

  std::string DebugString() override {
    std::ostringstream oss;
    oss << "[ServiceRpcClosure]";
    oss << " ctl=" << (ctl_ == nullptr ? "null" : ctl_->DebugString());
    oss << " ";
    oss << RpcClosure::DebugString();
    return oss.str();
  }

 private:
  dancenn::RpcController* ctl_{nullptr};
  const R* request_{nullptr};
  S* response_{nullptr};

  MethodRecorder<T> mr_;
};

inline Closure* NewCallback(std::function<void(const Status&)> function,
                            bool self_deleting = true) {
  return new FunctionClosure(function, self_deleting);
}

inline RpcClosure* NewRpcCallback(bool self_deleting = true) {
  return new RpcClosure(nullptr, self_deleting);
}

inline RpcClosure* NewRpcCallback(RpcClosure::FunctionType func, bool self_deleting = true) {
  return new RpcClosure(std::move(func), self_deleting);
}

template<class T, class R, class S>
inline ServiceRpcClosure<T, R, S>* NewRpcCallback(
    T* service, const std::string& method_name,
    ::google::protobuf::RpcController* ctl, const R* request, S* response,
    google::protobuf::Closure* done,
    bool self_deleting = true) {
  return new ServiceRpcClosure<T, R, S>(service, method_name,
      dynamic_cast<dancenn::RpcController*>(ctl), request,
      response, done, self_deleting);
}

class ClosureGuard {
 public:
  ClosureGuard() : _done(nullptr) {}

  explicit ClosureGuard(Closure* done) : _done(done) {}

  ~ClosureGuard() {
    if (_done) {
      _done->Run();
    }
  }
  void reset(Closure* done) {
    if (_done) {
      _done->Run();
    }
    _done = done;
  }

  Closure* release() {
    Closure* const prev_done = _done;
    _done = nullptr;
    return prev_done;
  }

  bool empty() const { return _done == nullptr; }

 private:
  ClosureGuard(const ClosureGuard&) = delete;
  void operator=(const ClosureGuard&) = delete;

  Closure* _done{nullptr};
};
}  // namespace dancenn


#endif //BASE_CLOSURE_H_
