//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//

#include "committer_channel_context.h"

#include <cnetpp/concurrency/this_thread.h>
#include <cnetpp/concurrency/thread.h>

namespace dancenn {

bool CommitterChannelContext::GetChannelIndex(int* out_channel_index) const {
  auto&& this_thread = cnetpp::concurrency::Thread::ThisThread();
  if (this_thread == nullptr) {
    return false;
  }
  int thread_pool_index = this_thread->ThreadPoolIndex();
  if (thread_pool_index >= thread_cnt_) {
    return false;
  }
  if (thread_pool_index < 0) {
    *out_channel_index = committer_channel_base_index_;
    return true;
  }
  *out_channel_index = committer_channel_base_index_ + thread_pool_index;
  return true;
}

}  // namespace dancenn
