#include "vlock.h"

#include <stdlib.h>
#include <string.h>
#include <sched.h>
#include <chrono>
#include <thread>

#include <glog/logging.h>
#include <cnetpp/concurrency/thread.h>
#include "base/constants.h"

DECLARE_bool(log_ha_lock_detail);

namespace dancenn {
  inline static int PowerOf2(int n) {
    int x = 1;
    while (x < n) {
      x = x << 1;
    }
    return x;
  }

  VersionRWLock* ver_rwlock_new(int threads) {
    threads = PowerOf2(threads);
    int len = sizeof(VersionRWLock) + threads * sizeof(VersionRWLockTLS);
    VersionRWLock* vlock = (VersionRWLock *)malloc(len);
    memset(vlock, 0, len);
    vlock->threads_ = threads;
    vlock->mask_    = threads - 1;
    pthread_mutex_init(&vlock->wlock_, NULL);
    return vlock;
  }

  void ver_rwlock_del(VersionRWLock* vlock) {
    pthread_mutex_destroy(&vlock->wlock_);
    free(vlock);
  }

  void ver_rwlock_wr_lock(VersionRWLock* vlock) {
    pthread_mutex_lock(&vlock->wlock_);
    vlock->current_.version_ = 1;
    // may led to StoreLoad re-order, need mfence
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
    while (true) {
      int x = 0;
      for (int i = 0; i < vlock->threads_; i++) {
        if (vlock->tls_[i].version_ == 0) x++;
      }
      if (x == vlock->threads_) break;
      sched_yield();
    }
    pthread_mutex_unlock(&vlock->wlock_);
  }

  void ver_rwlock_wr_unlock(VersionRWLock* vlock) {
    // asm volatile("mfence" ::: "memory");
    // on x86/x64 only happen StoreLoad re-order,
    // here we just disable compiler re-order
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("" ::: "memory");
#endif
    vlock->current_.version_ = 0;
  }

  bool ver_rwlock_rd_try_lock(VersionRWLock* vlock, int index) {
    if (vlock->current_.version_ > 0) return false;
    // may led to StoreLoad re-order, need mfence
    // maybe we can use `lock addl 0x01 address` to get better
    // performance
    // __sync_add_and_fetch(&vlock->tls_[index].version_, 1);
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
    if (vlock->current_.version_ > 0) {
      vlock->tls_[index].version_--;
      return false;
    }
    return true;
  }

  void ver_rwlock_rd_unlock(VersionRWLock* vlock, int index) {
    // asm volatile("mfence" ::: "memory");
    // on x86/x64 only happen StoreLoad re-order,
    // here we just disable compiler re-order
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("" ::: "memory");
#endif
    vlock->tls_[index].version_ = 0;
  }

  VRWLock::VRWLock(uint32_t threads) {
    lock_ = ver_rwlock_new(threads);
  }

  VRWLock::~VRWLock() {
    ver_rwlock_del(lock_);
  }

  int VRWLock::ref_count() const {
    int n = 0;
    for (int i = 0; i < lock_->threads_; i++) {
      n += lock_->tls_[i].version_;
    }
    return n;
  }

  VLock::VLock(VersionRWLock* lock) : lock_(lock) {
    auto this_thread = cnetpp::concurrency::Thread::ThisThread();
    if (LIKELY(this_thread)) {
      index_ = this_thread->ThreadIndex();
    } else {
      index_ = 0;
    }
  }

  void VLock::lock_shared() {
    for (int i = 0; i < 200; i++) {
      if (try_lock_shared()) return;
#ifdef __aarch64__
      asm volatile("yield" ::: "memory");
#else
      asm volatile("pause" ::: "memory");
#endif

    }
    for (int i = 0; i < 200; i++) {
      if (try_lock_shared()) return;
      sched_yield();
    }
    while (!try_lock_shared()) {
      usleep(10);
    }
  }

  bool VLock::try_lock_shared() {
    if (lock_->current_.version_ > 0) return false;
    auto tls = &lock_->tls_[index_ & lock_->mask_];
    __sync_add_and_fetch(&tls->version_, 1);
    if (lock_->current_.version_ > 0) {
      auto x = __sync_add_and_fetch(&tls->version_, -1);
      CHECK(x >= 0) << "Not Negative";
      return false;
    }
    return true;
  }

  void VLock::unlock_shared() {
    auto tls = &lock_->tls_[index_ & lock_->mask_];
    auto x = __sync_add_and_fetch(&tls->version_, -1);
    CHECK(x >= 0) << "Not Negative";
  }

  void VLock::lock() {
    pthread_mutex_lock(&lock_->wlock_);
    lock_->current_.version_ = 1;
    // may led to StoreLoad re-order, need mfence
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
    while (true) {
      int x = 0;
      for (int i = 0; i < lock_->threads_; i++) {
        if (lock_->tls_[i].version_ == 0) x++;
      }
      if (x == lock_->threads_) break;
      sched_yield();
    }
    pthread_mutex_unlock(&lock_->wlock_);
  }

  bool VLock::try_lock() {
    if (lock_->current_.version_ == 0) return false;
    pthread_mutex_lock(&lock_->wlock_);
    lock_->current_.version_ = 1;
    // may led to StoreLoad re-order, need mfence
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
    for (int i = 0; i < lock_->threads_; i++) {
      if (lock_->tls_[i].version_ != 0) {
        lock_->current_.version_ = 0;
        pthread_mutex_unlock(&lock_->wlock_);
        return false;
      }
    }
    pthread_mutex_unlock(&lock_->wlock_);
    return true;
  }

  void VLock::unlock() {
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("" ::: "memory");
#endif
    lock_->current_.version_ = 0;
  }

  int VLock::ref_count() const {
    int n = 0;
    for (int i = 0; i < lock_->threads_; i++) {
      n += lock_->tls_[i].version_;
    }
    return n;
  }

  void VLock::swap(VLock& lock) noexcept {
    std::swap(lock_, lock.lock_);
    std::swap(index_, lock.index_);
  }

  // ==========================================

  vshared_lock::vshared_lock(VersionRWLock* lock, const std::string& name)
      : vlock_(lock), owns_(true), name_(name) {
    LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
        << "lock_shared " << name_;

    vlock_.lock_shared();

    LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
        << "lock_shared finish " << name_;
  }

  vshared_lock::vshared_lock(VersionRWLock* lock,
                             std::try_to_lock_t,
                             const std::string& name)
      : vlock_(lock), name_(name) {
    LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
        << "lock_shared " << name_;

    owns_ = vlock_.try_lock_shared();

    if (owns_) {
      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "lock_shared finish " << name_;
    } else {
      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "lock_shared failed " << name_;
    }
  }

  vshared_lock::~vshared_lock() {
    if (owns_) {
      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "unlock_shared " << name_;

      vlock_.unlock_shared();

      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "unlock_shared finish " << name_;
    }
  }

  vshared_lock::vshared_lock(vshared_lock&& lock) noexcept
    : vshared_lock() {
      swap(lock);
  }

  vshared_lock& vshared_lock::operator=(vshared_lock&& lock) noexcept {
    vshared_lock(std::move(lock)).swap(*this);
    return *this;
  }

  void vshared_lock::swap(vshared_lock& lock) noexcept {
    vlock_.swap(lock.vlock_);
    std::swap(owns_, lock.owns_);

    if (owns_) {
      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "lock_shared swap " << name_;
    }
  }

  // ====================================================

  vunique_lock::vunique_lock(VersionRWLock* lock, const std::string& name)
      : vlock_(lock), owns_(true), name_(name) {
    LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
        << "lock " << name_;

    vlock_.lock();

    LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
        << "lock finish " << name_;
  }

//  vunique_lock::vunique_lock(VersionRWLock* lock,
//      std::try_to_lock_t) : lock_(lock) {
//    VLock vlock(lock_);
//    owns_ = vlock.try_lock();
//  }

  vunique_lock::~vunique_lock() {
    if (owns_) {
      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "unlock " << name_;

      vlock_.unlock();

      LOG_IF(INFO, UNLIKELY(!name_.empty() && FLAGS_log_ha_lock_detail))
          << "unlock finish " << name_;
    }
  }

  vunique_lock::vunique_lock(vunique_lock&& lock) noexcept
    : vunique_lock() {
    swap(lock);
  }

  vunique_lock& vunique_lock::operator=(vunique_lock&& lock) noexcept {
    vunique_lock(std::move(lock)).swap(*this);
    return *this;
  }

  void vunique_lock::swap(vunique_lock& lock) noexcept {
    vlock_.swap(lock.vlock_);
    std::swap(owns_, lock.owns_);
  }
}
