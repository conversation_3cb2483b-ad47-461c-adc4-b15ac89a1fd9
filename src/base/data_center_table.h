// Copyright 2020 Mu Xiong <<EMAIL>>

#ifndef BASE_DATA_CENTER_TABLE_H_
#define BASE_DATA_CENTER_TABLE_H_

#include <limits>
#include <mutex>
#include <string>
#include <unordered_set>

#include "base/read_write_lock.h"
#include "base/refresher.h"
#include "base/vlock.h"

namespace dancenn {
static std::string UNKNOWN_LOCATION_STR{"UNKNOWN"};

template <typename T>
class SequentialAccessTable {
 public:
  explicit SequentialAccessTable(size_t capacity) : capacity_(capacity) {
    if (capacity_ > 32) {
      LOG(ERROR) << "capacity_ > 32, bad performance";
    }
    vec_ = new T[capacity_];
  };

  ~SequentialAccessTable() = default;

  SequentialAccessTable(const SequentialAccessTable&) = delete;
  SequentialAccessTable& operator=(const SequentialAccessTable&) = delete;

  T& GetFromId(size_t id) const {
    if (id < size_.load()) {
      return vec_[id];
    } else {
      return UNKNOWN_LOCATION_STR;
    }
  }

  size_t GetByObject(const T& obj) const {
    for (int i = static_cast<int>(size_.load() - 1); i >= 0; --i) {
      if (vec_[i] == obj) {
        return static_cast<size_t>(i);
      }
    }
    return std::numeric_limits<size_t>::max();
  }

  size_t GetOrPutObject(const T& obj) {
    {
      auto idx = GetByObject(obj);
      if (idx != std::numeric_limits<size_t>::max()) {
        return idx;
      }
    }

    std::lock_guard<std::mutex> guard(mutex_);
    {
      auto idx = GetByObject(obj);
      if (idx != std::numeric_limits<size_t>::max()) {
        return idx;
      }
    }

    if (size_ < capacity_) {
      vec_[size_] = obj;
      size_++;
      return size_ - 1;
    } else {
      LOG(FATAL) << "overflow";
      return std::numeric_limits<size_t>::max();
    }
  }

 private:
  std::mutex mutex_;

  const size_t capacity_;
  std::atomic<size_t> size_{0};
  T* vec_{nullptr};
};

// dc_name table
class DataCenterTable {
 public:
  using DCName = std::string;

  explicit DataCenterTable(size_t capacity) : table_(capacity){};

  ~DataCenterTable() = default;

  DataCenterTable(const DataCenterTable&) = delete;
  DataCenterTable& operator=(const DataCenterTable&) = delete;

  DCName& Name(size_t id) {
    return table_.GetFromId(id);
  }

  size_t ID(const DCName& dc_name) { return table_.GetOrPutObject(dc_name); }

  std::string ToString(const size_t& id) {
    return Name(id) + "(" + std::to_string(id) + ")";
  }

 private:
  SequentialAccessTable<DCName> table_;
};

DataCenterTable& GetGlobalDataCenterTable();

}  // namespace dancenn

#endif  // BASE_DATA_CENTER_TABLE_H_
