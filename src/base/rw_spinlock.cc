// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/rw_spinlock.h"

#include <glog/logging.h>

#include <thread>

namespace dancenn {

RWSpinlock::~RWSpinlock() {
  CHECK(!lock_);
}

#define HAVE_REST() {\
  i++;\
  if (i < 16) {\
    continue;\
  } else if (i < 32) {\
    dummy = i;\
    dummy++;\
  } else {\
    std::this_thread::yield();\
  }\
}

void RWSpinlock::lock_shared() {
  int i = 0;
  int dummy = 0;
  while (true) {
    Value old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
    CHECK(!old_v.a.w_flag || !old_v.a.r_count);
    Value new_v = old_v;
    ++new_v.a.r_count;
    if (old_v.a.w_flag || old_v.a.w_pending) {
      old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
      goto have_rest;
    }
    if (lock_.compare_exchange_strong(old_v.v, new_v.v)) {
      break;
    }
have_rest:
    HAVE_REST();
  }
}

void RWSpinlock::unlock_shared() {
  auto res =
      static_cast<Value>(lock_.fetch_sub(1));
  (void)res;  //  silence compiler
  CHECK(!res.a.w_flag && res.a.r_count);
}

void RWSpinlock::lock() {
  int i = 0;
  int dummy = 0;
  while (true) {
    Value old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
    CHECK(!old_v.a.w_flag || !old_v.a.r_count);
    Value new_v = old_v;
    bool pending = false;
    if (old_v.a.r_count || old_v.a.w_flag) {
      if (old_v.a.w_pending) {
        old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
        goto have_rest;
      }
      new_v.a.w_pending = 1;
      pending = true;
    } else {
      new_v.a.w_flag = 1;
      new_v.a.w_pending = 0;
    }
    if (lock_.compare_exchange_strong(old_v.v, new_v.v)) {
      if (!pending) {
        break;
      }
    }
have_rest:
    HAVE_REST();
  }
}

bool RWSpinlock::try_lock() {
  Value old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
  CHECK(!old_v.a.w_flag || !old_v.a.r_count);
  if (old_v.a.r_count || old_v.a.w_flag || old_v.a.w_pending) {
    return false;
  }
  Value new_v = old_v;
  new_v.a.w_flag = 1;
  if (lock_.compare_exchange_strong(old_v.v, new_v.v)) {
    return true;
  }
  return false;
}

void RWSpinlock::unlock() {
  int i = 0;
  int dummy = 0;
  while (true) {
    Value old_v = static_cast<Value>(lock_.load(std::memory_order_relaxed));
    CHECK(old_v.a.w_flag && !old_v.a.r_count);
    Value new_v = old_v;
    new_v.a.w_flag = 0;
    if (lock_.compare_exchange_strong(old_v.v, new_v.v)) {
      break;
    }
    HAVE_REST();
  }
}

}  // namespace dancenn

