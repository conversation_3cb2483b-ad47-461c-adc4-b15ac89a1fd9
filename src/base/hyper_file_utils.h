#ifndef DANCENN_HYPER_FILE_UTILS_H
#define DANCENN_HYPER_FILE_UTILS_H

#include "status.h"
#include "constants.h"

namespace dancenn {

class HyperFileHelper {
public:
  static Status ComputeHyperBlockOffset(int32_t hyper_block_num, uint64_t file_offset, uint64_t file_length,
                                        uint64_t& block_offset, uint64_t& block_length) {
    // The request pass logic offset and logic length of hyperfile.
    // It should be transferred to real offset and real length in hyperblock
    // after hyperfile is scanned out.
    auto hyper_file_stripe_size = hyper_block_num * kHyperBlockStripeSize;
    if (hyper_file_stripe_size <= 0) {
      return Status(JavaExceptions::kIOException, "Invalid hyper_file_stripe_size. Hyper_file_stripe_size is 0");
    }

    block_offset = file_offset / hyper_file_stripe_size * kHyperBlockStripeSize;
    auto upper_bound_offset = ((file_offset + file_length) / hyper_file_stripe_size + 1 )* kHyperBlockStripeSize;
    block_length = upper_bound_offset - block_offset;

    return Status();
  }

  static Status GetHyperFileName(const std::string& hyper_block_name,
                                 const std::string& hyper_block_path,
                                 std::string& hyper_file_name,
                                 std::string& hyper_file_path) {
    // Verify the hyper_block_name.
    if (hyper_block_name.length() <= kHyperBlockSuffixLength) {
      return Status(JavaExceptions::kIOException, "Invalid hyper_block_name: " + hyper_block_name);
    } else {
      // The relative hyper_file name under parent-dir.
      hyper_file_name = hyper_block_name.substr(0, hyper_block_name.length() - kHyperBlockSuffixLength);
      hyper_file_path = hyper_block_path.substr(0, hyper_block_path.length() - kHyperBlockSuffixLength);
    }

    return Status();
  }
};

}

#endif //DANCENN_HYPER_FILE_UTILS_H
