#pragma once

#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <vector>
#include <glog/logging.h>
#include "cnetpp/concurrency/thread_pool.h"

namespace dancenn {

class TimerTaskManager {
public:
  using TaskCallback = std::function<void()>;

  explicit TimerTaskManager(std::shared_ptr<cnetpp::concurrency::ThreadPool> thread_pool)
      : thread_pool_(thread_pool),
        running_(false) {}

  ~TimerTaskManager() {
    Stop();
  }

  // Start the timer manager
  void Start();

  // Stop the timer manager
  void Stop();

  // Add a periodic task that runs every 'interval_seconds'
  // Returns task_id that can be used to cancel the task
  int64_t AddPeriodicTask(const TaskCallback& callback,
                         int interval_seconds,
                         const std::string& task_name = "");

  // Cancel a periodic task
  void CancelTask(int64_t task_id);

private:
  struct TimerTask {
    TaskCallback callback;
    int interval_seconds;
    int64_t next_run_time;
    std::string task_name;
  };

  std::shared_ptr<cnetpp::concurrency::ThreadPool> thread_pool_;
  std::mutex mutex_;
  std::atomic<bool> running_;
  std::map<int64_t, TimerTask> tasks_;  // task_id -> TimerTask
  std::atomic<int64_t> next_task_id_{0};

  void ScheduleNextRun();
  void CheckAndRunTasks();
};

} // namespace dancenn