// Copyright 2019 livexmm <<EMAIL>>
// copy from rocksdb, thank rocksdb

#ifndef BASE_ZLIB_COMPRESS_H_
#define BASE_ZLIB_COMPRESS_H_

#include <stdint.h>
#include <string>

// if no header use -14
#define ZLIB_ZLIB_WBITS     14
#define ZLIB_GZIP_WBITS     (ZLIB_ZLIB_WBITS | 16)
#define ZLIB_UNZIP_GZ_WBITS ZLIB_GZIP_WBITS

namespace dancenn {

// zlib format = zlib-header + deflate + zlib-tailer
// gzip format = gzip-header + deflate + zlib-tailer

// Compression options for different compression algorithms like Zlib
struct CompressionOptions {
  int window_bits;
  int level;
  int strategy;
  // Maximum size of dictionary used to prime the compression library. Currently
  // this dictionary will be constructed by sampling the first output file in a
  // subcompaction when the target level is bottommost. This dictionary will be
  // loaded into the compression library before compressing/uncompressing each
  // data block of subsequent files in the subcompaction. Effectively, this
  // improves compression ratios when there are repetitions across data blocks.
  // A value of 0 indicates the feature is disabled.
  // Default: 0.
  uint32_t max_dict_bytes;

  CompressionOptions()
      : window_bits(-14), level(-1), strategy(0), max_dict_bytes(0) {}
  CompressionOptions(int wbits, int _lev, int _strategy, int _max_dict_bytes)
      : window_bits(wbits),
        level(_lev),
        strategy(_strategy),
        max_dict_bytes(_max_dict_bytes) {}
};

// compress_format_version == 1 -- decompressed size is not included in the
// block header
// compress_format_version == 2 -- decompressed size is included in the block
// header in uint32 format
bool Zlib_Compress(const CompressionOptions& opts,
                   uint32_t compress_format_version, const char* input,
                   size_t length, ::std::string* output);


// compress_format_version == 1 -- decompressed size is not included in the
// block header
// compress_format_version == 2 -- decompressed size is included in the block
// header in varint32 format
// @param compression_dict Data for presetting the compression library's
//    dictionary.
char* Zlib_Uncompress(const char* input_data, size_t input_length,
                      int* decompress_size,
                      uint32_t compress_format_version,
                      int windowBits = -14);

}

#endif  // BASE_ZLIB_COMPRESS_H_
