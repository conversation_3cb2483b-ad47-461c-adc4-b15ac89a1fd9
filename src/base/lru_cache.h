// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_LRU_CACHE_H_
#define BASE_LRU_CACHE_H_

#include <glog/logging.h>

#include <atomic>
#include <functional>
#include <list>
#include <unordered_map>
#include <utility>

namespace dancenn {

template <class K,
          class V,
          bool UpdateLRUWhenSetExistedEle,
          class Hash = std::hash<K>,
          class Pred = std::equal_to<K>>
class LRUCache {
 public:
  explicit LRUCache(size_t capacity) : count_(0), capacity_(capacity) {
    CHECK_GT(capacity_, 1);
  }
  ~LRUCache() = default;

  bool Get(const K& k, V* v) {
    auto itr = table_.find(k);
    if (itr == table_.end()) {
      return false;
    }
    list_.splice(list_.begin(), list_, itr->second);
    *v = itr->second->second;
    return true;
  }

  void Set(const K& k, const V& v) {
    auto itr = table_.find(k);
    if (itr != table_.end()) {
      itr->second->second = v;
      if (UpdateLRUWhenSetExistedEle) {
        list_.splice(list_.begin(), list_, itr->second);
      }
    } else {
      list_.emplace_front(std::make_pair(k, v));
      table_.emplace(k, list_.begin());
      if (table_.size() > capacity_) {
        table_.erase(list_.back().first);
        list_.pop_back();
      } else {
        count_.fetch_add(1, std::memory_order_relaxed);
      }
    }
  }

  void Evict(const K& k) {
    auto itr = table_.find(k);
    if (itr == table_.end()) {
      return;
    }
    list_.erase(itr->second);
    table_.erase(itr);
    count_.fetch_add(-1, std::memory_order_relaxed);
  }

  void REvictUntil(const std::function<bool(const K&, const V&)>& cb) {
    auto itr = list_.end();
    while (itr != list_.begin()) {
      itr--;
      if (!cb(itr->first, itr->second)) {
        break;
      }
      table_.erase(itr->first);
      itr = list_.erase(itr);
      count_.fetch_add(-1, std::memory_order_relaxed);
    }
  }

  bool ForEach(const std::function<bool(const K&, const V&)>& cb) {
    for (const auto& kv : list_) {
      if (!cb(kv.first, kv.second)) {
        return false;
      }
    }
    return true;
  }

  // lock free
  size_t Size() const {
    return count_.load(std::memory_order_relaxed);
  }

 private:
  std::atomic<size_t> count_;

  std::list<typename std::pair<K, V>> list_;
  std::unordered_map<K,
                     typename std::list<typename std::pair<K, V>>::iterator,
                     Hash,
                     Pred> table_;
  const size_t capacity_;
};

}  // namespace dancenn

#endif  // BASE_LRU_CACHE_H_
