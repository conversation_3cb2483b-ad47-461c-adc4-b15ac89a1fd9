//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <cnetpp/concurrency/spin_lock.h>
#include <cnetpp/concurrency/thread_pool.h>

namespace dancenn {
using Thread = cnetpp::concurrency::Thread;
using ThreadPool = cnetpp::concurrency::ThreadPool;
using ThreadPoolTask = cnetpp::concurrency::Task;
using SpinLock = cnetpp::concurrency::SpinLock;
using SpinLockGuard = cnetpp::concurrency::SpinLock::ScopeGuard;
}  // namespace dancenn
