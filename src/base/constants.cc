// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/constants.h"

#include <cstdint>

namespace dancenn {

const int32_t kDefaultLayoutVersion = -60;

const uint64_t kReservedINodeIdBase = 2 << (14 - 1);
const uint64_t kRootINodeId = kReservedINodeIdBase + 1; // not persisted on disk
const uint64_t kLastReservedINodeId = kRootINodeId;
const uint64_t kNumReservedINode = kLastReservedINodeId - kRootINodeId;
const uint64_t kGrandfatherINodeId = 0;
const uint64_t kInvalidINodeId = UINT64_MAX;

const uint64_t kLastReservedGenerationStamp = 1000;
const uint64_t kGrandfatherGenerationStamp = 0;
const uint64_t kBlockProtocolV2GenerationStamp = 0;
const uint64_t kInvalidGenerationStamp = UINT64_MAX;

const uint64_t kLastReservedBlockId = 1024L * 1024 * 1024;

const uint64_t kDefaultLastCkptTxId = 0;

const char* kLayoutVersionKey = "layoutVersion";
const char* kCTimeKey = "cTime";
const char* kClusterIdKey = "clusterID";
const char* kFileSystemIdKey = "filesystemID";
const char* kNameSpaceIdKey = "namespaceID";
const char* kNameSpaceTypeKey = "namespaceType";
const char* kPersistentUfsInfoKey = "persistentUfsInfo";
const char* kPersistentFlagsKey = "persistentFlags";
const char* kBlockPoolIdKey = "blockpoolID";

const char* kGenerationStampV1Key = "generation_stamp_v1";
const char* kGenerationStampV2Key = "generation_stamp_v2";
const char* kGenerationStampV1LimitKey = "generation_stamp_v1_limit";
const char* kLastAllocatedBlockIdKey = "last_allocated_block_id";
const char* kLastINodeIdKey = "last_inode_id";
const char* kNumINodesKey = "num_inodes";
const char* kLastCkptTxIdKey = "last_ckpt_txid";
const char* kLastSnapshotIdKey = "last_snapshot_id";
const char* kNumSnapshotsKey = "num_snapshots";
const char* kAZBlacklistKey = "az_blacklist";
const char* kEditLogConfKey = "edit_log_conf";

const char* kBlockInfoVersion = "block_info_version";
const char* kBlockInfoVersionV2 = "v2";

const char* kLeaseVersion = "lease_version";
const char* kLeaseVersionV2 = "v2";

const char* kWriteBackTaskVersion = "write_back_task_versioin";  // typo
const char* kWriteBackTaskVersionV2 = "v2";

const char* kDirPolicyPersistedKey = "flag_dir_policy";
const char* kDirPolicyVersionV2 = "v2";

const char* kSeparator = "/";
const char kSeparatorChar = '/';
const char* kCurrentDirectory = ".";
const char* kInitStartAfter = "";

const char* kNamenodeLeaseHolder = "HDFS_NameNode";

const char* kRootName = "";
// 0x4001//0x4001
const char kRootINodeKey[18] = {0x00, 0x00, 0x00, 0x00,
                                0x00, 0x00, 0x40, 0x01,
                                '/', '/',
                                0x00, 0x00, 0x00, 0x00,
                                0x00, 0x00, 0x40, 0x01};
const int kRootINodeKeyLen = 8 + 2 + 8;

// 0x4001/0x00/0x4001
const char kRootINodeKeyV2[19] = {0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x40,
                                  0x01,
                                  '/',
                                  0x00,
                                  '/',
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x00,
                                  0x40,
                                  0x01};
const int kRootINodeKeyLenV2 = 8 + 3 + 8;

const char* kAllRAMStoragePolicyName = "ALL_RAM";
const char* kMemoryStoragePolicyName = "LAZY_PERSIST";
const char* kOnlyDisk3StoragePolicyName = "ONLY_DISK3";
const char* kOnlySSDStoragePolicyName = "ONLY_SSD";
const char* kAllSSDStoragePolicyName = "ALL_SSD";
const char* kOnlyDisk2StoragePolicyName = "ONLY_DISK2";
const char* kOneSSD2StoragePolicyName = "ONE_SSD2";
const char* kOneSSDStoragePolicyName = "ONE_SSD";
const char* kHotStoragePolicyName = "HOT";
const char* kWarmStoragePolicyName = "WARM";
const char* kColdStoragePolicyName = "COLD";
const char* kBlockStoragePolicyUnspecifiedName = "UNSPECIFIED";

const char* kFsActionNone = "---";
const char* kFsActionExecute = "--x";
const char* kFsActionWrite = "-w-";
const char* kFsActionWriteExecute = "-wx";
const char* kFsActionRead = "r--";
const char* kFsActionReadExecute = "r-x";
const char* kFsActionReadWrite = "rw-";
const char* kFsActionPermAll = "rwx";

const int64_t kInvalidTxId = -12345;

const char* kReplicaPolicyXAttr = "system.hdfs.replica.policy.pb";
const char* kReplicaPolicyTypeXAttr = "system.hdfs.replica.policy.type";
const char* kReplicaPolicyDCXAttr = "system.hdfs.replica.policy.dc";
const char* kEnforceDCUnspecified = "";
const int32_t kCentralizePolicy = 0;
const int32_t kDistributePolicy = 1;
const int32_t kNonePolicy = 2;

const char* kReadPolicyXAttr = "system.hdfs.read.policy.string";

const char* kUploadPolicyXAttr = "system.hdfs.upload.policy.string";
const char* kForceUploadXAttr = "system.hdfs.upload.policy.force";

const char* kQuotaPolicyXAttr = "system.hdfs.quota.policy";

const char* kTrashDirName = ".Trash";
const char* kRecycleBinDirName = ".RECYCLE.BIN";
const char* kRecyclePolicyXAttr = "system.hdfs.recycle.policy";
const std::string kTrashDirNameString(kTrashDirName);
const std::string kRecycleBinDirNameString(kRecycleBinDirName);
const char* kMountPointXAttr = "system.hdfs.mount.point";
const char* kCreateRpcInfoXAttr = "system.hdfs.rpc.create.hash";

const char* kObjectId = "trusted.hdfs.bytecool.objectid";
const char* kTemperature = "trusted.hdfs.bytecool.temperature.type";
const char* kTemperatureMtime = "trusted.hdfs.bytecool.temperature.mtime";
const char* kTemperatureWarm = "WARM";
const char* kTemperatureCoolingDown = "COOLING_DOWN";
const char* kTemperatureCool = "COOL";

const char* kSecurityXAttrUnreadableBySuperUser =
    "security.hdfs.unreadable.by.superuser";

const char* kDfsPermissionsSuperUserGroupDefault = "supergroup";

// HyperBlock Related
const std::string kHyperBlockKey("hB");
const std::string kHyperFileKey("hF");
const int64_t kHyperBlockStripeSize = 1 * 1024 * 1024;
const int32_t kHyperBlockSuffixLength = 17;

const std::string kDefaultSnapshotNamePattern("s%Y%m%d-%H%M%S");
const int32_t kSnapshotIdBitWidth = 28;
const int32_t kSnapshotMaxLimitDefault = 65536;

const char* kStorageClassXAttr = "system.cfs.storage.class";
const uint32_t kBytesPerKB = 1024U;
const uint32_t kBytesPerMB = 1024U * kBytesPerKB;
const uint32_t kBytesPerGB = 1024U * kBytesPerMB;

// ACC
const int32_t kSyncIntervalNever = -1;
const int32_t kSyncIntervalAlways = 0;

const char* kMallocConf =
    "prof_leak:true,"
    "prof_prefix:jeprof.out,"
    "lg_prof_sample:0,"
    "prof_accum:true,"
    "prof_final:true";

}  // namespace dancenn
