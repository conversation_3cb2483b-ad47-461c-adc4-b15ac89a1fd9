// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "base/script_metric_emitter_plugin.h"

#include <cnetpp/concurrency/this_thread.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <signal.h>
#include <sys/prctl.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>

#include <memory>
#include <string>

#include "base/net_utils.h"

DECLARE_string(metric_emitter_script_type);
DECLARE_string(metric_emitter_script_path);
DECLARE_string(metric_emitter_script_env);
DECLARE_string(metric_emitter_script_remote_addr);
DECLARE_int32(metric_emitter_script_remote_port);
DECLARE_int32(metric_emitter_script_pull_port);
DECLARE_string(default_ethernet_card);
DECLARE_int32(http_port);
DECLARE_string(metrics_prefix);
DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);
DECLARE_string(log_dir);

extern char** environ;

namespace dancenn {

bool ScriptMetricEmitterPlugin::Start() {
  if (FLAGS_metric_emitter_script_path.empty()) {
    LOG(ERROR) << "Empty metric emitter script path!";
    return false;
  }

  std::string addr =
      dancenn::net::GetHostAddresses()[FLAGS_default_ethernet_card].ToString() +
      ":" + std::to_string(FLAGS_http_port);
  LOG(INFO) << "Starting metric emitter script: "
            << FLAGS_metric_emitter_script_path << " -H " << addr << " -p "
            << FLAGS_metrics_prefix;

  ScriptExecutor se(FLAGS_metric_emitter_script_path);
  se.AddArg("-H", addr);
  se.AddArg("-p", FLAGS_metrics_prefix);
  se.AddArg("-r", FLAGS_cfs_region);
  se.AddArg("-e", FLAGS_cfs_env);
  se.AddArg("-c", FLAGS_cfs_cluster);

  if (FLAGS_metric_emitter_script_type == "push") {
    if (FLAGS_metric_emitter_script_env == "inner") {
      se.AddArg("-R", "False");
      se.AddArg("-l", FLAGS_log_dir);
    } else if (FLAGS_metric_emitter_script_env == "vpc") {
      se.AddArg("-R", "True");
      se.AddArg("-a", FLAGS_metric_emitter_script_remote_addr);
      se.AddArg("-P", std::to_string(FLAGS_metric_emitter_script_remote_port));
      se.AddArg("-l", FLAGS_log_dir);
    }
  } else if (FLAGS_metric_emitter_script_type == "pull") {
    se.AddArg("-P", std::to_string(FLAGS_metric_emitter_script_pull_port));
  }

  pid_ = se.Run();
  return pid_ > 0;
}

void ScriptMetricEmitterPlugin::Stop() {
  if (pid_ > 0) {
    kill(pid_, SIGKILL);
    waitpid(pid_, nullptr, 0);
    pid_ = 0;
  }
  google::FlushLogFiles(google::INFO);
}

void ScriptExecutor::AddArg(const std::string& flag, const std::string& value) {
  arg_vector_.emplace_back(flag);
  arg_vector_.emplace_back(value);
}

int ScriptExecutor::Run() {
  auto ppid_before_fork = getpid();
  auto pid = fork();
  if (pid > 0) {  // parent
    LOG(INFO) << "Starting metric emitter script, pid: " << pid;
    google::FlushLogFiles(google::INFO);
    return pid;
  } else if (pid == 0) {  // child
    // When parent process (DanceNN) exits, sub process (metric process) should
    // exits too. Otherwise, systemd won't start DanceNN again.
    // https://stackoverflow.com/questions/284325/how-to-make-child-process-die-after-parent-exits/17589555#17589555
    auto r = prctl(PR_SET_PDEATHSIG, SIGKILL);
    if (r != 0) {
      LOG(FATAL) << "Failed to prctl: "
                 << cnetpp::concurrency::ThisThread::GetLastErrorString();
    }
    if (getppid() != ppid_before_fork) {
      LOG(FATAL) << "DanceNN exits during prctl";
    }

    const char** argv = new const char*[arg_vector_.size() + 2];
    int i = 0;
    argv[i++] = command_.c_str();
    for (auto& arg : arg_vector_) {
      argv[i++] = arg.c_str();
    }
    argv[i] = nullptr;

    // e.g. tools/danceproxy_metrics.py -H ***********:5070 -p inf.hdfs.dancenn
    // danceproxy_metrics.py needs:
    // requests, msgpack
    // pyutil: https://review.byted.org/admin/repos/pyutil
    r = execve(command_.c_str(), const_cast<char**>(argv), environ);
    if (r != 0) {
      LOG(FATAL) << "Failed to execve: "
                 << cnetpp::concurrency::ThisThread::GetLastErrorString();
      delete[] argv;
    }
  } else {  // error
    LOG(ERROR) << "Failed to fork: "
               << cnetpp::concurrency::ThisThread::GetLastErrorString();
  }
  return 0;
}

}  // namespace dancenn
