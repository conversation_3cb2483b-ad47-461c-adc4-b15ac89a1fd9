// Copyright 2018 <PERSON><PERSON><PERSON> <zhao<PERSON><EMAIL>>

#ifndef BASE_RW_SPINLOCK_H_
#define BASE_RW_SPINLOCK_H_

#include <atomic>
#include <cstddef>

namespace dancenn {

class RWSpinlock {
 private:
  struct Atomic {
    uint64_t r_count :62;
    uint64_t w_pending :1;
    uint64_t w_flag :1;
  }__attribute__((packed));

  union Value {
    Atomic a;
    uint64_t v;

    explicit Value(uint64_t val) {
      v = val;
    }
  };


 public:
  RWSpinlock() {
    static_assert(sizeof(Atomic) == sizeof(uint64_t),
        "Atomic is not 8 byte");
    static_assert(sizeof(Value) == sizeof(uint64_t),
        "Value is not 8 byte");
  }

  ~RWSpinlock();

  RWSpinlock(const RWSpinlock &) = delete;
  RWSpinlock& operator=(const RWSpinlock &) = delete;

  void lock_shared();

  void unlock_shared();

  void lock();

  bool try_lock();

  void unlock();

 private:
  std::atomic<uint64_t> lock_ {0};
};

}  // namespace dancenn

#endif  // BASE_RW_SPINLOCK_H_

