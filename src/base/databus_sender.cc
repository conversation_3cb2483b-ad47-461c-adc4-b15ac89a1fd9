// Copyright 2022 <PERSON><PERSON><PERSON> Gao <<EMAIL>>

#include "base/databus_sender.h"
#include "glog/logging.h"

namespace dancenn {

DatabusSender::DatabusSender(std::string&& log_name, const std::string& channel_name) {
  channel_ = Databus::GetChannel(channel_name);
  log_name_ = std::move(log_name);
  task_.reset(new Task(this));
  worker_.reset(new cnetpp::concurrency::Thread(task_, "Databus" + log_name_));
  worker_->Start();
}

DatabusSender::~DatabusSender() {
  task_->Stop();
  worker_->Stop();
}

void DatabusSender::Push(std::string&& msg) {
  std::lock_guard<std::mutex> guard(mu_);
  // https://bytedance.feishu.cn/docx/NxwAdhS1yoxxtJx8TZrcHNqAn7c
  if (pending_logs_.size() < 100000) {
    pending_logs_.emplace_back(std::move(msg));
    cv_.notify_one();
  } else {
    VLOG(8) << "Too many pending " << log_name_ << " logs, abandon the log: " << msg;
  }
}

void DatabusSender::Task::Stop() {
  std::unique_lock<std::mutex> guard(sender_->mu_);
  cnetpp::concurrency::Task::Stop();
  sender_->cv_.notify_all();
}

bool DatabusSender::Task::operator()(void* arg) {
  (void)arg;
  std::unique_lock<std::mutex> guard(sender_->mu_);
  while (!IsStopped()) {
    if (!sender_->pending_logs_.empty()) {
      std::vector<std::string> b;
      b.swap(sender_->pending_logs_);
      guard.unlock();
      if (!sender_->channel_->Emit(b)) {
        LOG(WARNING) << "Failed to emit " << sender_->log_name_ << " to databus: "
                     << cnetpp::concurrency::ThisThread::GetLastErrorString();
      }
      guard.lock();
    } else {
      sender_->cv_.wait_for(guard, std::chrono::seconds(1), [this]() -> bool {
        return IsStopped() || !sender_->pending_logs_.empty();
      });
    }
  }
  return true;
}

} // namespace dancenn
