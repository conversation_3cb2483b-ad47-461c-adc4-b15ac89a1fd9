// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/properties.h"

#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>

#include <fstream>
#include <string>
#include <limits>

namespace dancenn {

bool Properties::Load(const std::string& file_name) {
  std::ifstream fs(file_name, std::ifstream::binary);
  if (!fs) {
    LOG(ERROR) << "Failed to open properties file: " << file_name << ", error: "
      << cnetpp::concurrency::ThisThread::GetLastErrorString();
    return false;
  }

  for (std::string line; std::getline(fs, line); ) {
    if (!LoadOneLine(line)) {
      return false;
    }
  }
  return true;
}

bool Properties::LoadOneLine(cnetpp::base::StringPiece line) {
  char c = 0;
  size_t key_length = 0;
  size_t value_start = 0;
  bool has_sep = false;
  bool preceding_backslash = false;
  while (key_length < line.length() && isspace(line[key_length])) {
    key_length++;
  }

  if (key_length < line.length() &&
      (line[key_length] == '#' || line[key_length] == '!')) {
    // comment line
    return true;
  }

  while (key_length < line.length()) {
    c = line[key_length];
    if ((c == '=' || c == ':') && !preceding_backslash) {
      value_start = key_length + 1;
      has_sep = true;
      break;
    } else if ((c == ' ' || c == '\t' || c == '\f') &&
        !preceding_backslash) {
      value_start = key_length + 1;
      break;
    }
    if (c == '\\') {
      preceding_backslash = !preceding_backslash;
    } else {
      preceding_backslash = false;
    }
    key_length++;
  }
  while (value_start < line.length()) {
    c = line[value_start];
    if (c != ' ' && c != '\t' &&  c != '\f') {
      if (!has_sep && (c == '=' ||  c == ':')) {
        has_sep = true;
      } else {
        break;
      }
    }
    value_start++;
  }

  std::string key;
  cnetpp::base::StringPiece k(line.data(), key_length);
  if (!Convert(k, &key)) {
    LOG(ERROR) << "Invalid key: " << k.data();
    return false;
  }
  std::string value;
  cnetpp::base::StringPiece v(line.data() + value_start,
      line.length() - value_start);
  if (!Convert(v, &value)) {
    LOG(ERROR) << "Invalid value: " << v.data();
    return false;
  }
  kvs_.emplace(key, value);
  return true;
}

bool Properties::Convert(cnetpp::base::StringPiece data, std::string* res) {
  CHECK_NOTNULL(res);

  res->reserve(data.length());
  char c;
  size_t cur = 0;

  while (cur < data.length()) {
    c = data[cur++];
    if (c == '\\') {
      c = data[cur++];
      if (c == 'u') {
        // Read the xxxx
        int value = 0;
        for (int i = 0; i < 4; i++) {
          c = data[cur++];
          switch (c) {
            case '0': case '1': case '2': case '3': case '4':
            case '5': case '6': case '7': case '8': case '9':
              value = (value << 4) + c - '0';
              break;
            case 'a': case 'b': case 'c':
            case 'd': case 'e': case 'f':
              value = (value << 4) + 10 + c - 'a';
              break;
            case 'A': case 'B': case 'C':
            case 'D': case 'E': case 'F':
              value = (value << 4) + 10 + c - 'A';
              break;
            default:
              LOG(ERROR) << "Malformed \\uxxxx encoding.";
              return false;
          }
        }
        res->append(1, static_cast<char>(value));
      } else {
        if (c == 't') {
          c = '\t';
        } else if (c == 'r') {
          c = '\r';
        } else if (c == 'n') {
          c = '\n';
        } else if (c == 'f') {
          c = '\f';
        }
        res->append(1, c);
      }
    } else {
      res->append(1, c);
    }
  }
  return true;
}

bool Properties::GetBool(const std::string& key, bool* res, bool def) {
  CHECK_NOTNULL(res);

  auto itr = kvs_.find(key);
  if (itr == kvs_.end()) {
    *res = def;
    return false;
  }
  cnetpp::base::StringPiece s(itr->second);
  if (s.length() == 4 && s.ignore_case_equal("true")) {
    *res = true;
  } else if (s.length() == 5 && s.ignore_case_equal("false")) {
    *res = false;
  } else {
    *res = def;
    return false;
  }
  return true;
}

bool Properties::GetDouble(const std::string& key, double* res, double def) {
  CHECK_NOTNULL(res);

  auto itr = kvs_.find(key);
  if (itr == kvs_.end()) {
    *res = def;
    return false;
  }
  char* end = nullptr;
  auto s = std::strtod(itr->second.c_str(), &end);
  if (cnetpp::concurrency::ThisThread::GetLastError() == ERANGE ||
      end != itr->second.c_str() + itr->second.length()) {
    *res = def;
    return false;
  }
  *res = s;
  return true;
}

bool Properties::GetString(const std::string& key, std::string* res,
    const std::string& def) {
  CHECK_NOTNULL(res);

  auto itr = kvs_.find(key);
  if (itr == kvs_.end()) {
    *res = def;
    return false;
  }
  *res = itr->second;
  return true;
}

void Properties::Set(const std::string& key, const std::string& value) {
  kvs_[key] = value;
}

void Properties::Store(const std::string& file_name) {
  std::ofstream ofs(file_name, std::ofstream::binary);
  if (!ofs) {
    LOG(FATAL) << "Failed to open properties file " << file_name
               << " for writing.";
  }
  // TODO(ruanjunbin): Make clear why LoadOneLine is so complicated?
  for (const auto& kv : kvs_) {
    ofs << kv.first << "=" << kv.second << std::endl;
  }
}

}  // namespace dancenn

