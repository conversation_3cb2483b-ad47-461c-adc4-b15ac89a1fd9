// Copyright 2019 livexmm <<EMAIL>>

#ifndef BASE_RWLOCK_TABLE_H_
#define BASE_RWLOCK_TABLE_H_

#include <cnetpp/concurrency/spin_lock.h>

#include <string>

#include "base/read_write_lock.h"

namespace dancenn {

class LockNode;

typedef struct LockLink {
  LockNode*         prev_;
  LockNode*         next_;
} LockLink;

class LockNode final {
 public:
  LockNode(const std::string& name, uint32_t h)
    : h_(h), ref_(1), name_(name), lock_() {
    link_.prev_ = nullptr;
    link_.next_ = nullptr;
  }

  LockNode(const std::string& name, uint32_t h, uint32_t ref)
    : h_(h), ref_(ref), name_(name), lock_() {
    link_.prev_ = nullptr;
    link_.next_ = nullptr;
  }

  ~LockNode() {}

  LockLink          link_;
  uint32_t          h_;
  // ref == UINT32_MAX, mean in static_table
  volatile uint32_t ref_;
  std::string       name_;
  ReadWriteLock     lock_;
};

class RWLockTable {
 public:
  RWLockTable(uint32_t max_depth, uint32_t size);
  ~RWLockTable();

  LockNode* GetOrCreate(const std::string& name, uint32_t h, uint32_t depth);
  void Recycle(LockNode* l);
  uint32_t static_count() const { return static_count_; }
  uint32_t dynamic_count() const { return dynamic_count_; }

 private:
  LockNode* GetOrCreateSP(const std::string& name, uint32_t h);
  LockNode* GetOrCreateFromStaticTable(const std::string& name, uint32_t h);

  constexpr static int N = 1087; // primer number is best
  LockNode*          static_table_[N];
  cnetpp::concurrency::SpinLock lock_;
  uint32_t           static_count_;
  uint32_t           dynamic_count_;
  uint32_t           length_;
  uint32_t           max_depth_;
  LockLink*          slots_;
};

}  // namespace dancenn

#endif  // BASE_RWLOCK_TABLE_H_
