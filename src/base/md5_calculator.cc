// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/md5_calculator.h"

#include <glog/logging.h>

#include <algorithm>
#include <string>
#include <memory>

extern void OPENSSL_cleanse(void *ptr, size_t len);

namespace dancenn {

const char MD5::kLowerCaseHex[16] = {
  '0', '1', '2', '3', '4', '5', '6', '7',
  '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'
};
const char MD5::kUpperCaseHex[16] = {
  '0', '1', '2', '3', '4', '5', '6', '7',
  '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'
};

std::string MD5::ToLowerCase() {
  return ToString(kLowerCaseHex);
}

std::string MD5::ToUpperCase() {
  return ToString(kUpperCaseHex);
}

std::string MD5::ToString(const char* table) {
  std::string res;
  res.reserve(MD5_DIGEST_LENGTH * 2);

  for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
    uint8_t h = data_[i] / 16;
    uint8_t l = data_[i] % 16;
    res.append(1, table[h]);
    res.append(1, table[l]);
  }
  return res;
}

bool MD5::FromString(cnetpp::base::StringPiece str) {
  if (str.size() < MD5_DIGEST_LENGTH * 2) {
    LOG(ERROR) << "Too short md5 string.";
    return false;
  }

  auto hex_string_to_int = [] (char c) -> int {
    if (c >= '0' && c <= '9') {
      return c - '0';
    } else if (c >= 'A' && c <= 'F') {
      return c - 'A' + 10;
    } else if (c >= 'a' && c <= 'f') {
      return c - 'a' + 10;
    }
    return -1;
  };

  for (int i = 0; i < MD5_DIGEST_LENGTH * 2; i += 2) {
    int h = hex_string_to_int(str[i]);
    int l = hex_string_to_int(str[i + 1]);
    if (l < 0 || h < 0) {
      LOG(ERROR) << "Invalid md5 string.";
      return false;
    }
    data_[i / 2] = (h & 0xf) << 4 | (l & 0xf);
  }

  return true;
}

void MD5Calculator::Update(cnetpp::base::StringPiece data) {
  if (init_) {
    EVP_DigestUpdate(md_ctx_, data.data(), data.size());
  }
}

bool MD5Calculator::Update(const std::string& file, size_t block_size) {
  RandomAccessFile raf(file);
  int64_t s = raf.Size();
  if (s < 0) {
    LOG(ERROR) << "Invalid file: " << file;
    return false;
  }

  uint64_t size = static_cast<uint64_t>(s);

  auto buf = std::unique_ptr<char[]>(new char[block_size]);
  uint64_t offset = 0;
  cnetpp::base::StringPiece res;
  while (offset < size) {
    if (!raf.Read(offset, buf.get(),
          std::min(size - offset, static_cast<uint64_t>(block_size)), &res)) {
      LOG(ERROR) << "Failed to read data from file: " << file
                 << " offset " << offset << " err" << errno;
      return false;
    }
    offset += res.length();
    Update(res);
  }
  return true;
}

MD5 MD5Calculator::Digest() {
  Final();
  return md5_;
}

void MD5Calculator::Reset() {
  finished_ = false;
  if (init_) {
    EVP_MD_CTX_destroy(md_ctx_);
    init_ = false;
  }

  md_ctx_ = EVP_MD_CTX_create();
  EVP_DigestInit_ex(md_ctx_, EVP_md5(), NULL);
  init_ = true;
}

void MD5Calculator::Final() {
  if (!finished_) {
    if (init_) {
      EVP_DigestFinal_ex(md_ctx_, md5_.mutable_data(), &md_len_);
    }
    finished_ = true;
  }
}

}  // namespace dancenn

