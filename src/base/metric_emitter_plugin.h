// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_METRIC_EMITTER_PLUGIN_H_
#define BASE_METRIC_EMITTER_PLUGIN_H_

#include <gflags/gflags.h>

#include <memory>

namespace dancenn {

class MetricEmitterPlugin {
 public:
  static std::unique_ptr<MetricEmitterPlugin> CreateMetricEmitter(
      const std::string& type);

  virtual bool Start() = 0;
  virtual void Stop() = 0;
};

class DummyMetricEmitterPlugin : public MetricEmitterPlugin {
 public:
  DummyMetricEmitterPlugin() = default;

  bool Start() override { return true; }
  void Stop() override {}
};

}  // namespace dancenn

#endif  // BASE_METRIC_EMITTER_PLUGIN_H_
