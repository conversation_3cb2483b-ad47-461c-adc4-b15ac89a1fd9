#include "base/network_location_info.h"

#include "datanode_manager/datanode_info.h"

DECLARE_bool(enable_location_tag);
DECLARE_bool(enable_location_tag_by_rack_aware);

namespace dancenn {

// TEST only
NetworkLocationInfo::NetworkLocationInfo(const cnetpp::base::IPAddress& _ip)
    : ip(_ip) {
  Init();
}

// TEST only
NetworkLocationInfo::NetworkLocationInfo(const std::string& dc) {
  location.dc = dc;
  location.rack = "000000";
}

NetworkLocationInfo::NetworkLocationInfo(
    const cnetpp::base::IPAddress& _ip,
    const cloudfs::LocationTag& _location_tag)
    : ip(_ip), location_tag(_location_tag) {
  Init();
}

NetworkLocationInfo::NetworkLocationInfo(DatanodeInfo* _dn) : dn(_dn) {
  if (dn != nullptr) {
    ip = dn->ip();
    location_tag = dn->location_tag();
    Init();
  }
}

void NetworkLocationInfo::Init() {
  if (FLAGS_enable_location_tag) {
    location = NetworkLocation(location_tag);
  } else {
    if (FLAGS_enable_location_tag_by_rack_aware) {
      if (location.dc.empty() || location.rack.empty()) {
        // back compatibility
        auto loc = ResolveNetworkLocation(ip);
        if (loc.dc != "UNKNOWN") {
          // resolve success
          location = loc;
          location_tag.set_az(location.dc);
          location_tag.set_switch_(location.rack);
          location_tag.set_host(location.host);
        }
      }
    }
  }


  VLOG(10) << "Init " << ToString();
}

std::string NetworkLocationInfo::ToString() const {
  std::ostringstream os;
  os << "[ip=" << ip.ToString()
     << " location_tag=" << location_tag.ShortDebugString()
     << " rdma_tag=" << rdma_tag << "]";
  return os.str();
}

}  // namespace dancenn