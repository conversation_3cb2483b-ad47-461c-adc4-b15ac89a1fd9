// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include <base/rwlock_manager.h>

#include <glog/logging.h>
#include <base/hash.h>
#include <gflags/gflags.h>

namespace dancenn {

RWLockManager::RWLockManager(uint32_t depth) {
  for (int i = 0; i < RWLockTableSliceN; i++) {
    rwlocks_[i] = new RWLockTable(depth, RWLockTableSlots);
  }
}

RWLockManager::~RWLockManager() {
  for (int i = 0; i < RWLockTableSliceN; i++) {
    delete rwlocks_[i];
  }
}

RWLockManager::PathLockMapPtr RWLockManager::LockPaths(const
    std::vector<cnetpp::base::StringPiece> &paths,
    bool last_wlock) {
  auto res = std::make_unique<std::map<cnetpp::base::StringPiece,
                            std::unique_ptr<RWLockManager::LockHolder>>>();
  if (paths.empty()) {
    LOG(WARNING) << "Acquiring locks on empty paths!";
    return res;
  }

  for (size_t i = 0; i < paths.size() - 1; i++) {
    res->emplace(std::make_pair(paths[i], ReadLock(paths[i], i)));
  }

  if (last_wlock) {
    DLOG(INFO) << "acquire write lock: " << paths.back();
    res->emplace(std::make_pair(paths.back(), WriteLock(paths.back(), paths.size() - 1)));
  } else {
    res->emplace(std::make_pair(paths.back(), ReadLock(paths.back(), paths.size() - 1)));
  }
  return res;
}

void RWLockManager::MaybeErase(LockMeta* lock_meta) {
  rwlocks_[lock_meta->l_->h_ % RWLockTableSliceN]->Recycle(lock_meta->l_);
}

RWLockManager::LockMeta RWLockManager::GetOrInsert(
    const cnetpp::base::StringPiece& token, uint32_t depth) {
  LockMeta lock_meta;

  std::string name = token.as_string();
  uint32_t h       = mur_mur_hash2(name.data(), name.size());
  RWLockTable* rwlocks = rwlocks_[h % RWLockTableSliceN];
  lock_meta.l_ = rwlocks->GetOrCreate(name, h, depth);

  return lock_meta;
}

}  // namespace dancenn

