// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/net_utils.h"

#include <cnetpp/base/ip_address.h>
#include <glog/logging.h>

#include <stdio.h>
#include <sys/types.h>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <string.h>
#include <arpa/inet.h>

#include <vector>
#include <memory>
#include <utility>
#include <string>

#include "base/defer.h"

namespace dancenn {
namespace net {

thread_local std::shared_ptr<IPAddressList> g_host_addresses_v4;
thread_local std::shared_ptr<IPAddressList> g_host_addresses_v6;

IPAddressList GetHostAddresses(bool v6) {
  if (g_host_addresses_v4) {
    if (v6) {
      return *g_host_addresses_v6;
    } else {
      return *g_host_addresses_v4;
    }
  }

  auto resv4 = std::make_shared<IPAddressList>();
  auto resv6 = std::make_shared<IPAddressList>();

  struct ifaddrs* if_addr_struct = nullptr;
  struct ifaddrs* ifa = nullptr;
  void *tmp_addr_ptr = nullptr;

  getifaddrs(&if_addr_struct);
  DEFER([&] () {
    if (if_addr_struct != nullptr) {
      freeifaddrs(if_addr_struct);
    }
  });

  for (ifa = if_addr_struct; ifa != nullptr; ifa = ifa->ifa_next) {
    if (!ifa->ifa_addr) {
      continue;
    }
    cnetpp::base::IPAddress ipaddr;
    if (ifa->ifa_addr->sa_family == AF_INET) {
      // is a valid IP4 Address
      tmp_addr_ptr =
          &(reinterpret_cast<struct sockaddr_in*>(ifa->ifa_addr))->sin_addr;
      char addr_buf[INET_ADDRSTRLEN];
      inet_ntop(AF_INET, tmp_addr_ptr, addr_buf, INET_ADDRSTRLEN);
      if (!cnetpp::base::IPAddress::LiteralToNumber(addr_buf, &ipaddr)) {
        LOG(WARNING) << "Invalid IPv4 address: " << addr_buf;
      } else {
        resv4->emplace(std::pair<std::string, cnetpp::base::IPAddress>(
            ifa->ifa_name, ipaddr));
      }
    } else if (ifa->ifa_addr->sa_family == AF_INET6) {
      // is a valid IP6 Address
      tmp_addr_ptr =
          &(reinterpret_cast<struct sockaddr_in6*>(ifa->ifa_addr))->sin6_addr;
      char addr_buf[INET6_ADDRSTRLEN];
      inet_ntop(AF_INET6, tmp_addr_ptr, addr_buf, INET6_ADDRSTRLEN);
      if (!cnetpp::base::IPAddress::LiteralToNumber(addr_buf, &ipaddr)) {
        LOG(WARNING) << "Invalid IPv6 address: " << addr_buf;
      } else {
        resv6->emplace(std::pair<std::string, cnetpp::base::IPAddress>(
            ifa->ifa_name, ipaddr));
      }
    }
  }
  resv4.swap(g_host_addresses_v4);
  resv6.swap(g_host_addresses_v6);
  if (v6) {
    return *g_host_addresses_v6;
  } else {
    return *g_host_addresses_v4;
  }
}

}  // namespace net
}  // namespace dancenn

