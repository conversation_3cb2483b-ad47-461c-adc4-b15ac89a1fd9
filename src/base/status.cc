// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/status.h"

namespace dancenn {

Status::Status() : code_(Code::kOK), exception_(JavaExceptions::kNoException) {}

Status::Status(JavaExceptions::Exception e,
               const std::string& msg,
               bool retryable)
    : exception_(e), message_(msg), retryable_(retryable) {
  if (exception_ == JavaExceptions::kNoException) {
    code_ = Code::kOK;
  } else {
    code_ = Code::kError;
  }
}

Status::Status(Code code, const std::string& msg)
    : exception_(JavaExceptions::kNoException), code_(code), message_(msg) {}

Status::Status(JavaExceptions::Exception e, Code code, const std::string& msg)
    : exception_(e), code_(code), message_(msg) {}

std::string Status::CodeStr() const {
  switch (code_) {
    case Code::kOK:
      return "kOK";
    case Code::kFalse:
      return "kFalse";
    case Code::kError:
      return "kError";
    case Code::kFileExists:
      return "kFileExists";
    case Code::kFileNotFound:
      return "kFileNotFound";
    case Code::kDirExists:
      return "kDirExists";
    case Code::kDirNotFound:
      return "kDirNotFound";
    case Code::kBadParameter:
      return "kBadParameter";
    case Code::kUpdateError:
      return "kUpdateError";
    case Code::kFileStatusError:
      return "kFileStatusError";
    case Code::kFileTypeError:
      return "kFileTypeError";
    case Code::kTimeout:
      return "kTimeout";
    case Code::kNoEntry:
      return "kNoEntry";
    case Code::kNotMinReplicated:
      return "kNotMinReplicated";
    case Code::kIsRetry:
      return "kIsRetry";
    case Code::kLeaseError:
      return "kLeaseError";
    case Code::kNotEnoughDN:
      return "kNotEnoughDN";
    case Code::kPrepareParentDir:
      return "kPrepareParentDir";
    case Code::kPrepareRecycleBin:
      return "kPrepareRecycleBin";
    case Code::kPrepareRecycleBinParentDir:
      return "kPrepareRecycleBinParentDir";
    case Code::kINodeStatCorrupt:
      return "kINodeStatCorrupt";
    case Code::kINodeStatNotFound:
      return "kINodeStatNotFound";
    case Code::kScrubRunning:
      return "kScrubRunning";
    case Code::kScrubNotRunning:
      return "kScrubNotRunning";
    case Code::kScrubFailed:
      return "kScrubFailed";
    case Code::kUsageError:
      return "kUsageError";
    case Code::kAbandonLastBlock:
      return "kAbandonLastBlock";
    case Code::kJobNotFound:
      return "kJobNotFound";
    case Code::kTaskNotFound:
      return "kTaskNotFound";
    case Code::kUfsUploadNotReady:
      return "kUfsUploadNotReady";
    default:
      return "Unknown code " +
          std::to_string(static_cast<std::underlying_type<Code>::type>(code_));
  }
}

std::string Status::ToString() const {
  return std::string(ExceptionStr()) +  " " + CodeStr() + ". " + message_;
}

}  // namespace dancenn

