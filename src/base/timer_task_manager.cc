#include "timer_task_manager.h"

#include <chrono>

namespace dancenn {

void TimerTaskManager::Start() {
  running_ = true;
  ScheduleNextRun();
}

void TimerTaskManager::Stop() {
  running_ = false;
}

int64_t TimerTaskManager::AddPeriodicTask(const TaskCallback& callback,
                                          int interval_seconds,
                                          const std::string& task_name) {
  std::lock_guard<std::mutex> lock(mutex_);
  int64_t task_id = ++next_task_id_;

  auto now = std::chrono::system_clock::now();
  auto next_run = std::chrono::system_clock::to_time_t(now) + interval_seconds;

  tasks_[task_id] = TimerTask{
      callback,
      interval_seconds,
      next_run,
      task_name.empty() ? "Task-" + std::to_string(task_id) : task_name};

  LOG(INFO) << "Added periodic task: " << tasks_[task_id].task_name
            << ", interval: " << interval_seconds << "s"
            << ", task_id: " << task_id;
  return task_id;
}

void TimerTaskManager::CancelTask(int64_t task_id) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = tasks_.find(task_id);
  if (it != tasks_.end()) {
    LOG(INFO) << "Cancelled periodic task: " << it->second.task_name;
    tasks_.erase(it);
  }
}

void TimerTaskManager::ScheduleNextRun() {
  if (!running_) return;

  // Schedule the check after 1 second
  thread_pool_->AddTask([this]() -> bool {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    CheckAndRunTasks();
    return true;
  });
}

void TimerTaskManager::CheckAndRunTasks() {
  if (!running_) return;

  auto now = std::chrono::system_clock::now();
  auto current_time = std::chrono::system_clock::to_time_t(now);

  std::vector<int64_t> tasks_to_run;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = tasks_.begin();
    while (it != tasks_.end()) {
      int64_t task_id = it->first;
      TimerTask& task = it->second;
      if (current_time >= task.next_run_time) {
        tasks_to_run.push_back(task_id);
        // Schedule next run
        task.next_run_time = current_time + task.interval_seconds;
      }
      ++it;
    }
  }

  // Run due tasks
  for (auto task_id : tasks_to_run) {
    TimerTask task;
    {
      std::lock_guard<std::mutex> lock(mutex_);
      task = tasks_[task_id];
    }

    thread_pool_->AddTask([task, task_id]() -> bool {
      try {
        task.callback();
      } catch (const std::exception& e) {
        LOG(ERROR) << "Error in periodic task " << task.task_name << ": "
                   << e.what();
      }
      return true;
    });
  }

  // Schedule next check
  ScheduleNextRun();
}

}  // namespace dancenn