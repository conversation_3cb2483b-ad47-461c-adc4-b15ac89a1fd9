//
// Created by re<PERSON><PERSON> on 2020-08-24.
//

#include "parquet_writer.h"

#include <glog/logging.h>

namespace dancenn {

using FileClass = ::arrow::io::FileOutputStream;

ParquetWriter::~ParquetWriter() {
  if (rg_writer_) {
    LOG(ERROR) << "Close rg writer";
    rg_writer_->Close();
  }

  if (file_writer_) {
      LOG(ERROR) << "Close file writer";
      file_writer_->Close();
  }
}

void ParquetWriter::WriteRow(uint64_t inode_id, uint64_t blk_id, uint64_t gs,
                             int32_t bytes, char* ip_array_buffer, int32_t ip_array_size) {
  if (!rg_writer_) {
    rg_writer_ = file_writer_->AppendBufferedRowGroup();
  }

  dns_writer_ = static_cast<parquet::ByteArrayWriter*>(rg_writer_->column(0));
  parquet::ByteArray value;
  value.ptr = reinterpret_cast<const uint8_t*>(ip_array_buffer);
  value.len = ip_array_size;
  dns_writer_->WriteBatch(1, nullptr, nullptr, &value);
  dns_writer_ = nullptr;

  inode_id_writer_ = static_cast<parquet::Int64Writer*>(rg_writer_->column(1));
  inode_id_writer_->WriteBatch(1, nullptr, nullptr, (int64_t*)(&inode_id));
  inode_id_writer_ = nullptr;

  block_id_writer_ = static_cast<parquet::Int64Writer*>(rg_writer_->column(2));
  block_id_writer_->WriteBatch(1, nullptr, nullptr, (int64_t*)(&blk_id));
  block_id_writer_ = nullptr;

  gs_writer_ = static_cast<parquet::Int64Writer*>(rg_writer_->column(3));
  gs_writer_->WriteBatch(1, nullptr, nullptr, (int64_t*)(&gs));
  gs_writer_ = nullptr;

  bytes_writer_ = static_cast<parquet::Int32Writer*>(rg_writer_->column(4));
  bytes_writer_->WriteBatch(1, nullptr, nullptr, &bytes);
  bytes_writer_ = nullptr;

  current_row_index_in_rowgroup_ ++;
  if (current_row_index_in_rowgroup_ == kRowsPerRowGroup) {
    current_row_index_in_rowgroup_ = 0;
    rg_writer_->Close();
    rg_writer_ = nullptr;
  }
}

bool ParquetWriter::Init() {
  std::shared_ptr<FileClass> out_file;
  auto status = FileClass::Open(file_path_, &out_file);
  if (!status.ok()) {
    return false;
  }

  // Setup the parquet schema
  std::shared_ptr<GroupNode> schema = SetupSchema();

  // Add writer properties
  parquet::WriterProperties::Builder builder;
  if (compress_type_ == "SNAPPY") {
    builder.compression(parquet::Compression::SNAPPY);
  } else if (compress_type_ == "GZIP") {
    builder.compression(parquet::Compression::GZIP);
  } else if  (compress_type_ == "ZSTD") {
    builder.compression(parquet::Compression::ZSTD);
  } else {
    builder.compression(parquet::Compression::UNCOMPRESSED);
  }

  std::shared_ptr<parquet::WriterProperties> props = builder.build();

  // Create a ParquetFileWriter instance
  file_writer_ = parquet::ParquetFileWriter::Open(out_file, schema, props);
  LOG(ERROR) << "Init file_writer success.";

  return true;
}

std::shared_ptr<GroupNode> ParquetWriter::SetupSchema() {
  parquet::schema::NodeVector fields;

  fields.push_back(PrimitiveNode::Make("dns", Repetition::REQUIRED, Type::BYTE_ARRAY));
  fields.push_back(PrimitiveNode::Make(
      "inode_id", Repetition::REQUIRED, LogicalType::None(), Type::INT64));
  fields.push_back(PrimitiveNode::Make(
      "block_id", Repetition::REQUIRED, LogicalType::None(), Type::INT64));
  fields.push_back(PrimitiveNode::Make(
      "gs", Repetition::REQUIRED, LogicalType::None(), Type::INT64));
  fields.push_back(PrimitiveNode::Make(
      "bytes", Repetition::REQUIRED, LogicalType::None(), Type::INT32));

  // Create a GroupNode named 'schema' using the primitive nodes defined above
  // This GroupNode is the root node of the schema tree
  return std::static_pointer_cast<GroupNode>(
      GroupNode::Make("schema", Repetition::REQUIRED, fields));
}


}