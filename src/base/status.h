// Copyright 2017 Liyuan Lei <<EMAIL>>

#ifndef BASE_STATUS_H_
#define BASE_STATUS_H_

#include <string>

#include "base/java_exceptions.h"
#include "base/platform.h"
#include "status_code.pb.h"  // NOLINT(build/include)

namespace dancenn {

// NOTICE: remember to adjust Status::CodeStr() if Code is modified
enum class Code {
  kOK = 0,
  kFalse = 1,
  kError = 2,
  kFileExists = 3,
  kFileNotFound = 4,
  kDirExists = 5,
  kDirNotFound = 6,
  kBadParameter = 7,
  kUpdateError = 8,
  kFileStatusError = 9,
  kFileTypeError = 10,
  kTimeout = 11,
  kNoEntry = 12,
  kNotImplemented = 13,
  kInvalidPath = 14,
  kCanceled = 15,
  kThrottled = 16,

  kNotMinReplicated = 20,
  kIsRetry = 21,

  kLeaseError = 30,

  kNotEnoughDN = 40,

  // code for triggering sub transactions
  // take "/path/to/file" as example
  kPrepareParentDir = 50,           // prepare "/path/to"
  kPrepareRecycleBin = 51,          // prepare "/.RECYCLE.BIN"
  kPrepareRecycleBinParentDir = 52, // prepare "/.RECYCLE.BIN/user/date/path/to"

  kINodeStatCorrupt = 100,
  kINodeStatNotFound = 101,
  kScrubRunning = 102,
  kScrubNotRunning = 103,
  kScrubFailed = 104,

  kUsageError = 200,

  kLockedPathResolveFailed = 300,

  // code for BIPWriteManager.
  kAbandonLastBlock = 301,

  kUfsSyncNeeded = 1000,
  kUfsSyncError = 1001,
  kUfsApiError = 1002,
  kUfsDirSyncRecheck = 1003,
  kUfsTooManyFiles = 1004,
  kUfsSyncLimitExceeded = 1005,
  kUfsAlreadySynced = 1006,

  kUfsUploadNotReady = 1010,

  kJobNotFound = 1100,
  kTaskNotFound = 1101,
  kTaskHasSubmitted = 1102,
};

class Status {
 public:
  Status();
  explicit Status(JavaExceptions::Exception e,
                  const std::string& msg = "",
                  bool retryable = false);
  explicit Status(Code code, const std::string& msg = "");
  explicit Status(JavaExceptions::Exception e,
                  Code code,
                  const std::string& msg = "");

  Code code() const { return code_; }

  JavaExceptions::Exception exception() const { return exception_; }

  const std::string& message() const { return message_; }

  std::string& mutable_message() { return message_; }

  bool IsOK() const { return code_ == Code::kOK; }

  bool IsFalse() const { return code_ == Code:: kFalse; }

  bool IsRetryable() const {
    return retryable_;
  }

  bool HasException() const {
    return exception_ != JavaExceptions::kNoException;
  }

  std::string CodeStr() const;

  std::string ExceptionStr() const {
    return std::string(JavaExceptions::ExceptionStr(exception_));
  }

  std::string ToString() const;

  static Status OK() { return Status(); }

 private:
  Code code_;
  JavaExceptions::Exception exception_;
  std::string message_;
  bool retryable_{false};
};

/// @brief Return the given status if it is not @c OK.
#define RETURN_NOT_OK(s) do { \
    const Status& _s = (s);             \
    if (PREDICT_FALSE(!_s.IsOK())) return _s;     \
  } while (0)

#define RETURN_NOT_OK_LOG(s)         \
  do {                               \
    const Status& _s = (s);          \
    if (PREDICT_FALSE(!_s.IsOK())) { \
      LOG(INFO) << _s.ToString();    \
      return _s;                     \
    }                                \
  } while (0)

#define RETURN_NOT_OK_LOG_MSG(s, msg) \
  do {                                \
    const Status& _s = (s);           \
    if (PREDICT_FALSE(!_s.IsOK())) {  \
      LOG(INFO) << (msg);             \
      return _s;                      \
    }                                 \
  } while (0)

}  // namespace dancenn

#endif  // BASE_STATUS_H_
