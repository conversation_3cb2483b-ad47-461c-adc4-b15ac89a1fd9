// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/path_util.h"

#include <gflags/gflags.h>
#include "acc/acc_namespace_def.h"

DECLARE_int32(namespace_type);

namespace dancenn {

const char* kDotReservedString = ".reserved";
const char* kDotReservedPathPrefix = "/.reserved";
const uint32_t kMaxPathLength = 8000;
const uint32_t kMaxPathDepth = 1000;
const char* kDotSnapshotDir = ".snapshot";

bool IsSnapshotComponent(const cnetpp::base::StringPiece& component) {
  static cnetpp::base::StringPiece snapshot_piece(kDotSnapshotDir);
  return component == snapshot_piece;
}

bool IsReservedPathComponent(const cnetpp::base::StringPiece& component) {
  static cnetpp::base::StringPiece snapshot_piece(kDotSnapshotDir);
  static cnetpp::base::StringPiece reserved_piece(kDotReservedString);
  return (!IsAccType(FLAGS_namespace_type) && component == snapshot_piece) ||
         component == reserved_piece;
}

}  // namespace dancenn

