// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/10/22
// Description

#include <memory>

#include "base/logger_metrics.h"

namespace dancenn {
namespace {

std::unique_ptr<LoggerMetrics> logger_metrics;

}  // namespace

LoggerMetrics& LoggerMetrics::Instance() {
  static std::once_flag once_flag;
  std::call_once(once_flag, []() { logger_metrics.reset(new LoggerMetrics); });
  return *logger_metrics;
}

void LoggerMetrics::Warn() {
  MFC(Instance().warn_)->Inc();
}

void LoggerMetrics::Error() {
  MFC(Instance().error_)->Inc();
}

LoggerMetrics::LoggerMetrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("Logger");

  warn_ = metrics->RegisterCounter("Warn");
  error_ = metrics->RegisterCounter("Error");

  compare_inode_failed_ =
      metrics->RegisterCounter("CompareINodeFromMetaStorageAndEditLogFailed");
  compare_block_failed_ =
      metrics->RegisterCounter("CompareBlockFromMetaStorageAndEditLogFailed");
  missing_block_error_ = metrics->RegisterCounter("DError#detail=MissingBlock");
  try_to_delete_not_deprecated_block_error_ =
      metrics->RegisterCounter("DError#detail=TryToDeleteNotDeprecatedBlock");
  persist_block_fail_error_ =
      metrics->RegisterCounter("DError#detail=PersistBlockFail");
  unlock_block_info_in_transaction_lock_failed_ = metrics->RegisterCounter(
      "DError#detail=UnlockBlockInfoInTransactionLockFailed");
}

}  // namespace dancenn
