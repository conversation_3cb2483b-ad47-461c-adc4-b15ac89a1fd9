// Copyright 2019 livexmm <<EMAIL>>

#include "base/rwlock_table.h"

#include <string.h>
#include <pthread.h>
#include <assert.h>

#include <memory>

namespace dancenn {

RWLockTable::RWLockTable(uint32_t max_depth, uint32_t size) {
  slots_  = (LockLink *)malloc(sizeof(LockLink) * size);
  for (int i = 0; i < size; i++) {
    slots_[i].next_ = (LockNode *)&slots_[i];
    slots_[i].prev_ = (LockNode *)&slots_[i];
  }
  memset(static_table_, 0, sizeof(LockNode *) * N);
  dynamic_count_ = 0;
  static_count_  = 0;
  max_depth_     = max_depth;
  length_        = size;
}

RWLockTable::~RWLockTable() {
  for (int i = 0; i < length_; i++) {
    LockNode* root = (LockNode *)&slots_[i];
    while (slots_[i].next_ != root) {
      LockNode* t = slots_[i].next_;
      t->link_.prev_->link_.next_ = t->link_.next_;
      t->link_.next_->link_.prev_ = t->link_.prev_;
      delete t;
    }
  }
  free(slots_);

  for (int i = 0; i < N; i++) {
    if (static_table_[i] != nullptr) {
      delete static_table_[i];
    }
  }
}

LockNode* RWLockTable::GetOrCreateSP(const std::string& name, uint32_t h) {
  LockNode* new_node = new LockNode(name, h);

  uint32_t index = h % length_;
  LockNode* root = (LockNode *)&slots_[index];
  lock_.Lock();
  LockNode* node = root->link_.next_;
  while (node != root) {
    if (node->h_ == h && node->name_ == name) {
      node->ref_ = node->ref_ + 1;
      break;
    }
    node = node->link_.next_;
  }
  if (node == root) {
    new_node->link_.next_ = slots_[index].next_;
    new_node->link_.prev_ = (LockNode *)&slots_[index];
    slots_[index].next_->link_.prev_ = new_node;
    slots_[index].next_              = new_node;
    dynamic_count_ = dynamic_count_ + 1;

    node     = new_node;
    new_node = nullptr;
  }
  lock_.Unlock();

  delete new_node;
  return node;
}

LockNode* RWLockTable::GetOrCreateFromStaticTable(const std::string& name, uint32_t h) {
  uint32_t index = h % N;
  if (static_table_[index] != nullptr) {
    if (static_table_[index]->h_ == h
        && static_table_[index]->name_ == name) {
      return static_table_[index];
    }
    return nullptr;
  }

  LockNode* t = new LockNode(name, h, UINT32_MAX);
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
  LockNode* new_node = t;
  if (__sync_bool_compare_and_swap(&static_table_[index], nullptr, new_node)) {
    __sync_fetch_and_add(&static_count_, 1);
    return new_node;
  }
  delete new_node;

  if (static_table_[index] != nullptr) {
    if (static_table_[index]->h_ == h
        && static_table_[index]->name_ == name) {
      return static_table_[index];
    }
  }
  return nullptr;
}

LockNode* RWLockTable::GetOrCreate(const std::string& name,
    uint32_t h, uint32_t depth) {
  if (depth < max_depth_) {
    LockNode* node = GetOrCreateFromStaticTable(name, h);
    if (node != nullptr) return node;
  }

  uint32_t index = h % length_;
  LockNode* root = (LockNode *)&slots_[index];
  lock_.Lock();
  LockNode* node = root->link_.next_;
  while (node != root) {
    if (node->h_ == h) {
      node->ref_ = node->ref_ + 1;
      break;
    }
    node = node->link_.next_;
  }
  lock_.Unlock();

  if (node != root) {
    if (node->name_ == name) return node;
    Recycle(node);
  }

  return GetOrCreateSP(name, h);
}

void RWLockTable::Recycle(LockNode* l) {
  assert(l->ref_ > 0);
  if (l->ref_ == UINT32_MAX) {
    // LockNode in static table not need any release operation
    return;
  }
  assert((LockLink *)l < slots_ || (LockLink *)l >= &slots_[length_]);
  lock_.Lock();
  l->ref_ = l->ref_ - 1;
  if (l->ref_ == 0) {
    l->link_.next_->link_.prev_ = l->link_.prev_;
    l->link_.prev_->link_.next_ = l->link_.next_;
    dynamic_count_ = dynamic_count_ - 1;
  } else {
    l = nullptr;
  }
  lock_.Unlock();
  delete l;
}

}  // namespace dancenn
