// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/java_exceptions.h"

namespace dancenn {

const std::map<cnetpp::base::StringPiece, JavaExceptions::Exception>
JavaExceptions::exceptions_ {
    {IOException(), kIOException},
    {RpcServerException(), kRpcServerException},
    {SaslException(), kSaslException},
    {VersionMismatch(), kVersionMismatch},
    {IncorrectVersionException(), kIncorrectVersionException},
    {InconsistentFSStateException(), kInconsistentFSStateException},
    {AccessControlException(), kAccessControlException},
    {FileNotFoundException(), kFileNotFoundException},
    {FileAlreadyExistsException(), kFileAlreadyExistsException},
    {ParentNotDirectoryException(), kParentNotDirectoryException},
    {UnresolvedLinkException(), kUnresolvedLinkException},
    {SafeModeException(), kSafeModeException},
    {RpcNoSuchMethodException(), kRpcNoSuchMethodException},
    {StandbyException(), kStandbyException},
    {IllegalArgumentException(), kIllegalArgumentException},
    {InvalidTopologyException(), kInvalidTopologyException},
    {AlreadyBeingCreatedException(), kAlreadyBeingCreatedException},
    {RecoveryInProgressException(), kRecoveryInProgressException},
    {InvalidPathException(), kInvalidPathException},
    {UnsupportedOperationException(), kUnsupportedOperationException},
    {HadoopIllegalArgumentException(), kHadoopIllegalArgumentException},
    {PathIsNotEmptyDirectoryException(), kPathIsNotEmptyDirectoryException},
    {LeaseExpiredException(), kLeaseExpiredException},
    {NotReplicatedYetException(), kNotReplicatedYetException},
    {InvalidRequestException(), kInvalidRequestException},
    {ReadOnlyCoolFileException(), kReadOnlyCoolFileException},
    {HyperFileException(), kHyperFileException},
    {QuotaExceededException(), kQuotaExceededException},
    {DSQuotaExceededException(), kDSQuotaExceededException},
    {NSQuotaExceededException(), kNSQuotaExceededException},
    {MaxDirectoryItemsExceededException(), kMaxDirectoryItemsExceededException},
    {NoException(), kNoException},
    {RangerAccessControlException(), kRangerAccessControlException},
    {ThrottlerException(), kThrottlerException},
    {SnapshotException(), kSnapshotException},
};

const std::map<JavaExceptions::Exception, cnetpp::base::StringPiece>
JavaExceptions::exception2str_ {
    {kIOException, IOException()},
    {kRpcServerException, RpcServerException()},
    {kSaslException, SaslException()},
    {kVersionMismatch, VersionMismatch()},
    {kIncorrectVersionException, IncorrectVersionException()},
    {kInconsistentFSStateException, InconsistentFSStateException()},
    {kAccessControlException, AccessControlException()},
    {kFileNotFoundException, FileNotFoundException()},
    {kFileAlreadyExistsException, FileAlreadyExistsException()},
    {kParentNotDirectoryException, ParentNotDirectoryException()},
    {kUnresolvedLinkException, UnresolvedLinkException()},
    {kSafeModeException, SafeModeException()},
    {kRpcNoSuchMethodException, RpcNoSuchMethodException()},
    {kStandbyException, StandbyException()},
    {kIllegalArgumentException, IllegalArgumentException()},
    {kInvalidTopologyException, InvalidTopologyException()},
    {kAlreadyBeingCreatedException, AlreadyBeingCreatedException()},
    {kRecoveryInProgressException, RecoveryInProgressException()},
    {kInvalidPathException, InvalidPathException()},
    {kUnsupportedOperationException, UnsupportedOperationException()},
    {kHadoopIllegalArgumentException, HadoopIllegalArgumentException()},
    {kPathIsNotEmptyDirectoryException, PathIsNotEmptyDirectoryException()},
    {kLeaseExpiredException, LeaseExpiredException()},
    {kNotReplicatedYetException, NotReplicatedYetException()},
    {kInvalidRequestException, InvalidRequestException()},
    {kReadOnlyCoolFileException, ReadOnlyCoolFileException()},
    {kHyperFileException, HyperFileException()},
    {kQuotaExceededException, QuotaExceededException()},
    {kDSQuotaExceededException, DSQuotaExceededException()},
    {kNSQuotaExceededException, NSQuotaExceededException()},
    {kMaxDirectoryItemsExceededException, MaxDirectoryItemsExceededException()},
    {kNoException, NoException()},
    {kRangerAccessControlException, RangerAccessControlException()},
    {kThrottlerException, ThrottlerException()},
    {kSnapshotException, SnapshotException()},
};

}  // namespace dancenn

