// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/redis_manager.h"

#include <glog/logging.h>
#include <hiredis/hiredis.h>
#include <cnetpp/base/string_utils.h>

#include <algorithm>
#include <random>
#include <string>
#include <utility>

#include "base/defer.h"

namespace dancenn {

bool RedisManager::Init(const std::string& backends, int timeout_ms) {
  if (context_) {
    LOG(INFO) << "Redis manager is already initialized.";
    return true;
  }

  auto all_backends = cnetpp::base::StringUtils::SplitByChars(backends, ",");
  if (all_backends.empty()) {
    LOG(ERROR) << "Invalid argument backends: " << backends;
    return false;
  }

  std::random_device rd;
  std::mt19937 g(rd());
  std::shuffle(all_backends.begin(), all_backends.end(), g);
  auto endpoint = cnetpp::base::StringUtils::SplitByChars(all_backends[0], ":");
  std::string host;
  int port = 6379;  // redis default port
  if (endpoint.size() == 1) {
    host = endpoint[0];
  } else if (endpoint.size() == 2) {
    host = endpoint[0];
    auto p = std::strtol(endpoint[1].c_str(), nullptr, 10);
    if (p > 65535) {
      LOG(ERROR) << "Invalid argument backends: " << backends;
      return false;
    }
    port = static_cast<int>(p);
  } else {
    LOG(ERROR) << "Invalid argument backends: " << backends;
    return false;
  }

  struct timeval timeout { timeout_ms / 1000, (timeout_ms % 1000) * 1000 };
  context_ = redisConnectWithTimeout(host.c_str(), port, timeout);
  if (!context_) {
    LOG(ERROR) << "Failed to connect to redis: cannot allocate redis context.";
    return false;
  }
  if (context_->err) {
    LOG(ERROR) << "Failed to connect to redis: " << context_->errstr;
    return false;
  }
  return true;
}

void RedisManager::Stop() {
  if (context_) {
    redisFree(context_);
    context_ = nullptr;
  }
}

bool RedisManager::IsInited() const {
  return context_ != nullptr;
}

bool RedisManager::RedisHGetAll(
    const std::string& key,
    std::unordered_map<std::string, std::string>* result) {
  CHECK_NOTNULL(result);

  redisReply* reply = static_cast<redisReply*>(
      redisCommand(context_, "HGETALL %s", key.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to HGETALL, key: " << key
      << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply] () { freeReplyObject(reply); });
    if (reply->type == REDIS_REPLY_NIL) {
      // no data
      return true;
    }
    if (reply->type != REDIS_REPLY_ARRAY) {
      LOG(ERROR) << "Invalid redis data for " << key;
      return false;
    }
    for (size_t i = 0; i < reply->elements; i += 2) {
      if (reply->element[i]->type != REDIS_REPLY_STRING) {
        LOG(ERROR) << "Invalid redis data for " << key;
        return false;
      }
      result->emplace(reply->element[i]->str, reply->element[i + 1]->str);
    }
  }
  return true;
}

bool RedisManager::RedisHKeys(
    const std::string& key, std::unordered_set<std::string>* result) {
  CHECK_NOTNULL(result);

  redisReply *reply = static_cast<redisReply*>(
      redisCommand(context_, "HKEYS %s", key.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to HKEYS, key: " << key
      << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply]() { freeReplyObject(reply); });
    if (reply->type == REDIS_REPLY_NIL) {
      // no data
      return true;
    }
    if (reply->type != REDIS_REPLY_ARRAY) {
      LOG(ERROR) << "Invalid redis data for " << key;
      return false;
    }
    for (size_t i = 0; i < reply->elements; ++i) {
      auto sub_reply = reply->element[i];
      if (sub_reply->type != REDIS_REPLY_STRING) {
        LOG(ERROR) << "Invalid redis data for " << key;
        return false;
      }
      result->emplace(sub_reply->str);
    }
  }
  return true;
}

bool RedisManager::RedisSMembers(
    const std::string& key, std::unordered_set<std::string>* result) {
  CHECK_NOTNULL(result);

  redisReply *reply = static_cast<redisReply*>(
      redisCommand(context_, "SMEMBERS %s", key.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to SMEMBERS, key: " << key
      << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply]() { freeReplyObject(reply); });
    if (reply->type == REDIS_REPLY_NIL) {
      // no data
      return true;
    }
    if (reply->type != REDIS_REPLY_ARRAY) {
      LOG(ERROR) << "Invalid redis data for " << key;
      return false;
    }
    for (size_t i = 0; i < reply->elements; ++i) {
      auto sub_reply = reply->element[i];
      if (sub_reply->type != REDIS_REPLY_STRING) {
        LOG(ERROR) << "Invalid redis data for " << key;
        return false;
      }
      result->emplace(sub_reply->str);
    }
  }
  return true;
}

bool RedisManager::RedisGet(const std::string& key, std::string* result) {
  CHECK_NOTNULL(result);

  redisReply* reply = static_cast<redisReply*>(
      redisCommand(context_, "GET %s", key.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to GET, key: " << key
      << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply] () { freeReplyObject(reply); });
    if (reply->type == REDIS_REPLY_NIL) {
      // no data
      return true;
    }
    if (reply->type != REDIS_REPLY_STRING) {
      LOG(ERROR) << "Invalid redis data for " << key;
      return false;
    }
    result->append(reply->str);
  }
  return true;
}

bool RedisManager::RedisHSet(const std::string& key, const std::string& field,
    const std::string& value) {

  redisReply* reply = static_cast<redisReply*>(redisCommand(context_, 
      "HSET %s %s %s", key.c_str(), field.c_str(), value.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to HSET, key: " << key << "field: " << field 
        << "value: " << value << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply] () { freeReplyObject(reply); });
    if (reply->type != REDIS_REPLY_INTEGER) {
      LOG(ERROR) << "Invalid redis reply type: " << reply->type;
      return false;
    }
    if (reply->integer != 1 && reply->integer != 0) {
      LOG(ERROR) << "Invalid redis hset result: " << reply->integer
          << " key: " << key;
      return false;
    }
  }
  return true;
}

bool RedisManager::RedisSAdd(const std::string& key,
      const std::string& value) {

  redisReply* reply = static_cast<redisReply*>(
    redisCommand(context_, "SADD %s %s", key.c_str(), value.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to SADD, key: " << key << "value: " << value
        << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply] () { freeReplyObject(reply); });
    if (reply->type != REDIS_REPLY_INTEGER) {
      LOG(ERROR) << "Invalid redis reply type: " << reply->type;
      return false;
    }
    if (reply->integer != 1 && reply->integer != 0) {
      LOG(ERROR) << "Invalid redis sadd result: " << reply->integer
          << " key: " << key;
      return false;
    }
  }
  return true;
}

bool RedisManager::RedisSet(const std::string& key, const std::string& value) {

  redisReply* reply = static_cast<redisReply*>(
      redisCommand(context_, "SET %s %s", key.c_str(), value.c_str()));
  if (!reply) {
    LOG(ERROR) << "Failed to SET, key: " << key << "value: " << value
      << ", err: " << context_->errstr;
    return false;
  }
  {
    DEFER([reply] () { freeReplyObject(reply); });
    if (reply->type != REDIS_REPLY_STATUS) {
      LOG(ERROR) << "Invalid redis reply type: " << reply->type;
      return false;
    }
    if (strcmp(reply->str, "OK") != 0) {
      LOG(ERROR) << "Invalid redis set result: " << reply->str
          << " key: " << key;
      return false;
    }
  }
  return true;
}

}  // namespace dancenn

