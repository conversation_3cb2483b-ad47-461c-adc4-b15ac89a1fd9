// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_NET_UTILS_H_
#define BASE_NET_UTILS_H_

#include <cnetpp/base/ip_address.h>

#include <map>
#include <memory>
#include <string>

namespace dancenn {
namespace net {

using IPAddressList = std::map<std::string, cnetpp::base::IPAddress>;
extern thread_local std::shared_ptr<IPAddressList> g_host_addresses_v4;
extern thread_local std::shared_ptr<IPAddressList> g_host_addresses_v6;

IPAddressList GetHostAddresses(bool v6 = false);

}  // namespace net
}  // namespace dancenn

#endif  // BASE_NET_UTILS_H_

