// Copyright (c) @ 2022.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2022/08/04
// Description

#ifndef BASE_RPC_SERVER_METRICS_H_
#define BASE_RPC_SERVER_METRICS_H_

#include <absl/strings/str_format.h>         // For StrFormat.
#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.
#include <glog/logging.h>                    // For CHECK, LOG.

#include <map>          // For map.
#include <memory>       // For shared_ptr.
#include <string>       // For string.
#include <type_traits>  // For is_same.
#include <vector>       // For vector.

#include "base/metric.h"     // For Gauge.
#include "base/metrics.h"    // For MetricsCenter, Metrics.
#include "rpc/rpc_server.h"  // For RpcServer, HandlerGroup.

namespace dancenn {

class RpcServerMetrics {
 public:
  RpcServerMetrics(const RpcServer& client_server,
                   const RpcServer& datanode_server,
                   const RpcServer& ha_server) {
    for (const RpcServer* rpc_server : std::vector<const RpcServer*>{
             &client_server, &datanode_server, &ha_server}) {
      std::string rpc_server_name =
          rpc_server->GetRpcServerOptions().rpc_server_name();
      CHECK(!rpc_server_name.empty());
      auto metrics = MetricsCenter::Instance()->RegisterMetrics(
          absl::StrFormat("RpcServer.%s", rpc_server_name));
      CHECK_EQ(metrics_groups_[metrics].size(), 0);

      for (const auto& kv : rpc_server->handler_groups()) {
        int method_type = static_cast<int>(kv.first);
        static_assert(
            std::is_same<
                RpcServer::HandlerGroup,
                std::shared_ptr<cnetpp::concurrency::ThreadPool>>::value,
            "HandlerGroup is a shared_ptr, we just copy it by value.");
        RpcServer::HandlerGroup handler_group = kv.second;
        metrics_groups_[metrics].push_back(metrics->RegisterGauge(
            absl::StrFormat("HandlerGroup.NumRunningTasks#method_type=%d",
                            method_type),
            [handler_group]() -> double {
              return handler_group->NumRunningTasks();
            }));
        metrics_groups_[metrics].push_back(metrics->RegisterGauge(
            absl::StrFormat("HandlerGroup.NumPendingTasks#method_type=%d",
                            method_type),
            [handler_group]() -> double {
              return handler_group->PendingCount();
            }));
        CHECK_NE(metrics_groups_[metrics].size(), 0);
      }
    }
  }

  ~RpcServerMetrics() {
    for (const auto& kv : metrics_groups_) {
      std::shared_ptr<Metrics> metrics = kv.first;
      for (const auto& gauge : kv.second) {
        metrics->DeregisterGauge(gauge);
      }
    }
    LOG(INFO) << "Release RpcServerMetrics Done";
  }

 private:
  std::map<std::shared_ptr<Metrics>, std::vector<std::shared_ptr<Gauge>>>
      metrics_groups_;
};

};  // namespace dancenn

#endif  // BASE_RPC_SERVER_METRICS_H_
