#include "time_util.h"

namespace dancenn {

uint64_t TimeUtil::GetNowEpochMs() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

int64_t TimeUtil::GetSecondsSince(
    const std::chrono::steady_clock::time_point& start) {
  auto now = std::chrono::steady_clock::now();
  return std::chrono::duration_cast<std::chrono::seconds>(now - start).count();
}

uint64_t TimeUtil::GetNowEpochMsFor(int millis) {
  auto ts = std::chrono::system_clock::now().time_since_epoch() +
            std::chrono::milliseconds(millis);
  return std::chrono::duration_cast<std::chrono::milliseconds>(ts).count();
}

std::string TimeUtil::EpochMsToString(uint64_t epoch_ms) {
  auto result = EpochToString(epoch_ms / 1000);
  result += ".";
  result += std::to_string(epoch_ms % 1000);
  return result;
}

std::string TimeUtil::EpochToString(uint64_t epoch) {
  auto duration = std::chrono::seconds(epoch);
  auto time_point =
      std::chrono::time_point<std::chrono::system_clock>(duration);
  auto tt = std::chrono::system_clock::to_time_t(time_point);

  std::tm tm{};
  gmtime_r(&tt, &tm);
  std::string result(255, 0);
  auto len = std::strftime(&result[0], result.size(), "%F %T", &tm);
  result.resize(len);
  return result;
}

uint64_t TimeUtil::ToSeconds(const std::chrono::system_clock::time_point& s,
                             const std::chrono::system_clock::time_point& e) {
  return std::chrono::duration_cast<std::chrono::seconds>(e - s).count();
}

uint64_t TimeUtil::ToMilliSeconds(
    const std::chrono::system_clock::time_point& s,
    const std::chrono::system_clock::time_point& e) {
  return std::chrono::duration_cast<std::chrono::milliseconds>(e - s).count();
}

uint64_t TimeUtil::ToMicroSeconds(
    const std::chrono::system_clock::time_point& s,
    const std::chrono::system_clock::time_point& e) {
  return std::chrono::duration_cast<std::chrono::microseconds>(e - s).count();
}

uint64_t TimeUtil::ToNanoSeconds(
    const std::chrono::system_clock::time_point& s,
    const std::chrono::system_clock::time_point& e) {
  return std::chrono::duration_cast<std::chrono::nanoseconds>(e - s).count();
}

std::string TimeUtil::GetTimeFormatted(uint64_t epoch,
                                       const std::string& patten) {
  auto duration = std::chrono::seconds(epoch);
  auto time_point =
      std::chrono::time_point<std::chrono::system_clock>(duration);
  auto tt = std::chrono::system_clock::to_time_t(time_point);

  std::tm tm{};
  gmtime_r(&tt, &tm);
  std::string result(255, 0);
  auto len = std::strftime(&result[0], result.size(), patten.c_str(), &tm);
  result.resize(len);
  return result;
}

std::string TimeUtil::GetNowYMD() {
  std::time_t ts_sec = std::time(nullptr);
  std::tm* timeinfo = std::localtime(&ts_sec);
  char buf[16];
  (void)std::strftime(buf, sizeof(buf), "%Y-%m-%d", timeinfo);
  return std::string(buf);
}

uint64_t TimeUtil::YMDToSec(const std::string& ymd) {
  uint32_t year, month, day;
  int num = sscanf(ymd.c_str(), "%u-%u-%u", &year, &month, &day);
  if (num != 3) {
    return 0;
  }
  std::tm timeinfo = {0};
  timeinfo.tm_year = year - 1900;
  timeinfo.tm_mon = month - 1;
  timeinfo.tm_mday = day;
  return static_cast<uint64_t>(std::mktime(&timeinfo));
}

uint64_t TimeUtil::GetEpochMsByFormat(const std::string& time,
                                      const std::string& format) {
  std::tm tm;
  strptime(time.c_str(), format.c_str(), &tm);
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::from_time_t(timegm(&tm))
                 .time_since_epoch())
      .count();
}

std::function<uint64_t()> TimeUtilV2::GetNowEpochMs = TimeUtil::GetNowEpochMs;
std::function<uint64_t()> TimeUtilV2::GetNowEpochMs4Log =
    TimeUtil::GetNowEpochMs;

void TimeUtilV2::TestOnlySetGetNowEpochMsFunc(
    const std::function<uint64_t()>& f) {
  GetNowEpochMs = f;
}

}  // namespace dancenn
