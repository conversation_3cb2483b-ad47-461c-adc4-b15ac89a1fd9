// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_FILE_UTILS_H_
#define BASE_FILE_UTILS_H_

#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>
#include <cnetpp/base/string_piece.h>

#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>

#include <string>

#include "base/defer.h"
#include "base/constants.h"

namespace dancenn {

class RandomAccessFile {
 public:
  explicit RandomAccessFile(const std::string& file_name, int fd = -1)
      : file_name_(file_name), fd_(fd) {
  }
  ~RandomAccessFile() {
    if (fd_ >= 0) {
      close(fd_);
    }
  }

  const std::string& file_name() const {
    return file_name_;
  }

  int fd() const {
    return fd_;
  }
  void set_fd(int fd) {
    fd_ = fd;
  }

  int64_t Size() {
    struct stat info;
    if (stat(file_name_.c_str(), &info) != 0) {
        LOG(ERROR) << "Failed to stat file: " << file_name_ << ", error: "
          << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return -1;
    }
    return static_cast<int64_t>(info.st_size);
  }

  bool Read(uint64_t offset, char* buffer, size_t n,
      cnetpp::base::StringPiece* result) {
    CHECK_NOTNULL(result);
    CHECK_NOTNULL(buffer);
    CHECK_GT(n, 0);

    if (fd_ < 0) {
      fd_ = open(file_name_.c_str(), O_RDONLY);
      if (fd_ < 0) {
        LOG(ERROR) << "Failed to open file: " << file_name_ << ", error: "
          << cnetpp::concurrency::ThisThread::GetLastErrorString();
        return false;
      }
    }
    char* p = buffer;
    size_t remain = n;
    off_t pos = static_cast<off_t>(offset);
    while (remain > 0) {
      auto r = pread(fd_, p, remain, pos);
      if (r < 0) {
        if (cnetpp::concurrency::ThisThread::GetLastError() == EINTR) {
          continue;
        }
        return false;
      } else if (r == 0) {
        break;
      }
      remain -= r;
      p += r;
      pos += r;
    }
    *result = cnetpp::base::StringPiece(buffer, n - remain);
    return true;
  }

 private:
  std::string file_name_;
  int fd_ { -1 };
};

class FileUtils final {
 public:
  static std::string GetCwd() {
    std::string path(512, 0);
    auto p = getcwd(&path[0], path.size());
    if (p == nullptr) {
      LOG(FATAL)
        << "Failed to get current working directory, "
        << "error: "
        << cnetpp::concurrency::ThisThread::GetLastErrorString();
      path.resize(0);
    } else {
      auto n = strlen(p);
      CHECK_LT(n, path.size());
      path.resize(n);
    }
    return path;
  }

  static bool IsDirectory(const std::string& path) {
    struct stat info;
    if (stat(path.c_str(), &info) != 0) {
      return false;
    }
    if (S_ISDIR(info.st_mode)) {
      return true;
    }
    return false;
  }

  static bool IsFile(const std::string& path) {
    struct stat info;
    if (stat(path.c_str(), &info) != 0) {
      return false;
    }
    if (S_ISREG(info.st_mode)) {
      return true;
    }
    return false;
  }

  static bool Exists(const std::string& path) {
    if (access(path.c_str(), F_OK)) {
      if (cnetpp::concurrency::ThisThread::GetLastError() == ENOENT) {
        return false;
      }
      LOG(FATAL) << "Failed to access: " << path << ", error: "
                 << cnetpp::concurrency::ThisThread::GetLastErrorString();
    }
    return true;
  }

  static bool DeleteDirectoryRecursively(const std::string& path) {
    if (!IsDirectory(path)) {
      LOG(ERROR) << "Not a directory: " << path;
      return false;
    }

    {
      DIR* d = opendir(path.c_str());
      if (!d) {
        LOG(ERROR) << "Failed to open directory: " << path << ", error: "
          << cnetpp::concurrency::ThisThread::GetLastErrorString();
        return false;
      }
      DEFER([d]() {closedir(d);});

      struct dirent* entry;
      while ((entry = readdir(d)) != nullptr) {
        if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, "..")) {
          continue;
        }
        std::string ename = path + "/" + entry->d_name;
        if (IsDirectory(ename)) {
          if (!DeleteDirectoryRecursively(ename)) {
            LOG(ERROR) << "Failed to delete directory: " << ename;
            return false;
          }
        } else {
          if (!DeleteFile(ename)) {
            LOG(ERROR) << "Failed to delete file: " << ename;
            return false;
          }
        }
      }
    }
    if (rmdir(path.c_str()) != 0) {
      LOG(ERROR) << "Failed to delete directory: " << path << ", error: "
        <<cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
    return true;
  }

  static bool DeleteFile(const std::string& file) {
    if (unlink(file.c_str()) != 0) {
      LOG(ERROR) << "Failed to delete file: " << file << ", error: "
        <<cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
    return true;
  }

  static bool CreateDirectoryRecursively(const std::string &path, mode_t mode) {
    if (path.empty() || path.size() >= PATH_MAX) {
      return false;
    }
    size_t len = path.size();
    auto tmp = new char[len + 1];
    DEFER([&]() {
      delete[] tmp;
    });

    snprintf(tmp, len + 1, "%s", path.c_str());
    while (len && tmp[len - 1] == '/') {
      tmp[len - 1] = 0;
      len--;
    }
    if (len <= 0) {
      return false;
    }
    int last_splitter = len - 1;
    while (last_splitter >= 0) {
      if (tmp[last_splitter] == '/') {
        break;
      }
      last_splitter--;
    }
    if (last_splitter <= 0) {
      if (mkdir(tmp, mode) != 0) {
        LOG(ERROR) << "Failed to create directory: " << tmp << ", error: "
                   << cnetpp::concurrency::ThisThread::GetLastErrorString();
        return false;
      }
      return true;
    }
    tmp[last_splitter] = 0;
    if (!Exists(tmp)) {
      if (!CreateDirectoryRecursively(tmp, mode)) {
        return false;
      }
    }
    tmp[last_splitter] = '/';
    if (mkdir(tmp, S_IRWXU) != 0) {
      LOG(ERROR) << "Failed to create directory: " << tmp << ", error: "
                 << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
    return true;
  }

  static bool ListDirectory(const std::string& path,
                            bool ignore_parent,
                            std::function<bool(struct dirent* child)> cb) {
    if (!IsDirectory(path)) {
      LOG(ERROR) << "Not a directory: " << path;
      return false;
    }

    DIR* d = opendir(path.c_str());
    if (!d) {
      LOG(ERROR) << "Failed to open directory: " << path << ", error: "
        << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
    DEFER([d]() {closedir(d);});

    struct dirent* entry;
    while ((entry = readdir(d)) != nullptr) {
      if (ignore_parent &&
          (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, ".."))) {
        continue;
      }

      if (!cb(entry)) {
        return true;
      }
    }
    return true;
  }

  static bool WriteFile(const std::string& path, const std::string& content) {
    FILE *fp = fopen(path.c_str(), "w+");
    if (fp == nullptr) {
       return false;
    }
    DEFER([&] () { fclose(fp); });
  
    int writen;
    for (int offset = 0; offset < content.length();) {
      writen = fwrite(content.c_str() + offset, 1, 
          content.length() - offset, fp);
      if (writen <= 0) {
        LOG(ERROR) << "Failed to Write file: " << path << " error: "
          << cnetpp::concurrency::ThisThread::GetLastErrorString();
        return false;
      }
      offset += writen;
    }
  
    if (fflush(fp) != 0) {
      LOG(ERROR) << "Failed to flush file: " << path << " error: "
        << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
  
    return true;
  }

  static bool ReadFile(std::string& path, std::string& content) {
    FILE *fp = fopen(path.c_str(), "rb");
    if (fp == nullptr) {
      LOG(ERROR) << "Failed to open file: " << path << ", error: "
        << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
    DEFER([&] () { fclose(fp); });
  
    if (fseek(fp, 0, SEEK_END) != 0) {
      LOG(ERROR) << "Failed to seek file: " << path << ", error: "
        << cnetpp::concurrency::ThisThread::GetLastErrorString();
      return false;
    }
  
    size_t size = ftell(fp);
    if (size < 0) {
      return false;
    }
  
    content.resize(size);
  
    rewind(fp);
    int read = 0;
    for (int offset = 0; offset < size;) {
      read = fread(&content.at(offset), 1, size - offset, fp);
      if (read <= 0) {
        LOG(ERROR) << "Failed read file: " << path << ", error: "
          << cnetpp::concurrency::ThisThread::GetLastErrorString();
        return false;
      }
      offset += read;
    }
  
    return true;
  }

  static std::string TryReadConfig(const std::string& path) {
    RandomAccessFile raf(path);
    auto size = raf.Size();
    if (size <= 0) {
      LOG(ERROR) << "Failed to get size of file: " << path;
      return "";
    }

    auto buffer = std::unique_ptr<char[]>(new char[size]);
    cnetpp::base::StringPiece result;
    if (!raf.Read(0, buffer.get(), size, &result)) {
      LOG(ERROR) << "Failed to read contents from file: " << path;
      return "";
    }
    return result.as_string();
  }


};

}  // namespace dancenn

#endif  // BASE_FILE_UTILS_H_
