//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

// System
#include <unordered_map>

// Third
#include <glog/logging.h>
#include <librdkafka/rdkafkacpp.h>

#define SET_KAFKA_CONF(conf, name, value, err)                             \
  CHECK(conf->set(name, value, err) == RdKafka::Conf::ConfResult::CONF_OK) \
      << "Failed to load kafka conf. error: " << err

#define LOG_KMSG(msg)                                                   \
  "topic: " << msg->topic_name() << ", partition: " << msg->partition() \
            << ", offset: " << msg->offset()

#define LOG_KMSG_DETAIL(msg)                                      \
  LOG_KMSG(msg) << ", key: " << msg->key()                        \
                << ", payload: " << (char*)msg->payload()         \
                << ", err code: " << RdKafka::err2str(msg->err()) \
                << ", err msg: " << msg->errstr()

#define LOG_KOFFSET(offset)                                              \
  "topic: " << offset->topic() << ", partition: " << offset->partition() \
            << ", offset: " << offset->offset()

namespace dancenn {

// Map of: topic -> partition -> offset
using KafkaOffsetMap =
    std::unordered_map<std::string, std::unordered_map<uint32_t, uint64_t>>;

class KafkaUtil {
 public:
  static std::vector<RdKafka::TopicPartition*> OffsetMapToOffsets(
      const KafkaOffsetMap&);
};

}  // namespace dancenn
