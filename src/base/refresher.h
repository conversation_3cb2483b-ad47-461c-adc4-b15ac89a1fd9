// Copyright 2020 Mu <PERSON>ong <<EMAIL>>

#ifndef BASE_REFRESHER_H_
#define BASE_REFRESHER_H_

#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <condition_variable>
#include <chrono>
#include <memory>
#include <mutex>

namespace dancenn {

class Refresher {
public:
  Refresher(const std::string& name, std::function<void()> call)
      : name_(name), call_(std::move(call)) {}

  ~Refresher() {
    Stop();
  }

  // set before Start()
  void set_period(std::chrono::milliseconds period) {
    period_ = period;
  }

  void Start() {
    CHECK(!worker_);
    worker_ = std::make_unique<cnetpp::concurrency::Thread>(
        std::shared_ptr<cnetpp::concurrency::Task>(
            new Task(this, period_)), name_);
    worker_->Start();
  }

  void Stop() {
    if (worker_) {
      worker_->Stop();
      worker_.reset();
    }
  }

  void Do() {
    call_();
  }

private:
  std::string name_;
  std::function<void()> call_;
  std::chrono::milliseconds period_{60 * 1000};
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;

  friend class Task;

  class Task : public cnetpp::concurrency::Task {
  public:
    Task(Refresher *refresher, std::chrono::milliseconds period)
        : refresher_(refresher), period_(period) {
    }

    bool operator()(void *arg) override {
      (void) arg;
      while (!stop_) {
        refresher_->Do();
        std::unique_lock<std::mutex> lock(mu_);
        cond_.wait_for(lock, period_, [this]() -> bool { return stop_; });
      }
      return true;
    }

    void Stop() override {
      std::unique_lock<std::mutex> lock(mu_);
      stop_ = true;
      cond_.notify_all();
    }

  private:
    Refresher *refresher_;
    std::chrono::milliseconds period_;
    std::mutex mu_;
    std::condition_variable cond_;
  };
};

}  // namespace dancenn

#endif  // BASE_REFRESHER_H_