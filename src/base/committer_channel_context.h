
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#pragma once

#include <glog/logging.h>

#include <atomic>
#include <string>

namespace dancenn {

class CommitterChannelContext {
 public:
  CommitterChannelContext() = delete;
  CommitterChannelContext(const std::string& name, int thread_cnt) {
    static std::atomic<int> next_channel_index{0};
    committer_channel_base_index_ = next_channel_index.fetch_add(thread_cnt);
    thread_cnt_ = thread_cnt;

    LOG(INFO) << "CommitterChannelContext Init, name=" << name
              << ", thread_cnt=" << thread_cnt
              << ", base_index=" << committer_channel_base_index_;
  }

  bool GetChannelIndex(int* out_channel_index) const;

 private:
  int committer_channel_base_index_{-1};
  int thread_cnt_{1};
};
}  // namespace dancenn
