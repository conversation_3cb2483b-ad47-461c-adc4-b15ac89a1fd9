// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_DATABUS_H_
#define BASE_DATABUS_H_

#include <cnetpp/base/socket.h>

#include <sys/socket.h>
#include <sys/un.h>

#include <memory>
#include <utility>
#include <string>
#include <vector>

#include "collector.pb.h"  // NOLINT(build/include)

namespace dancenn {

class UnixStreamSocket : public cnetpp::base::DataSocket {
 public:
  bool Create();

  UnixStreamSocket() {}

  bool Connect(const std::string& socket_path,
      std::chrono::milliseconds timeout = std::chrono::milliseconds(0));

  bool SendAll(const void* buffer,
               size_t buffer_size,
               size_t* sent_size,
               int flags,
               bool auto_restart);

 private:
  sockaddr_un socket_addr_;
};

struct DatabusConnection {
  bool Init();

  std::unique_ptr<UnixStreamSocket> socket_;
};

extern thread_local std::shared_ptr<DatabusConnection> g_conn;

class DatabusChannel {
 public:
  explicit DatabusChannel(const std::string& channel) : channel_(channel) {
  }

  bool Emit(const std::string& message, int codec = 0);

  bool Emit(const std::string& key, const std::string& message, int codec = 0);

  bool Emit(const std::vector<std::string>& messages,
            int codec = 0,
            int partition = -1);

  bool Emit(const std::vector<std::pair<std::string, std::string>>& messages,
            int codec = 0);

 private:
  bool InitConnection();

  bool SendPayload(const collector::RequestPayload& payload);

  std::string channel_;
};

class Databus {
 public:
  // DatabusChannel is a thread safe channel instance
  static std::shared_ptr<DatabusChannel> GetChannel(
      const std::string& channel) {
    return std::make_shared<DatabusChannel>(channel);
  }
};

}  // namespace dancenn

#endif  // BASE_DATABUS_H_

