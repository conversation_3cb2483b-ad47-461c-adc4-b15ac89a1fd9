// Copyright 2017 Tianyi He <<EMAIL>>

#ifndef BASE_DICTIONARY_H_
#define BASE_DICTIONARY_H_

#include <base/read_write_lock.h>

#include <shared_mutex>
#include <cstdint>
#include <cassert>
#include <list>
#include <mutex>
#include <unordered_map>

namespace dancenn {

static const uint32_t kInvalidId = 0;
static const uint32_t kInitialId = 1;

template <class T, class U>
class Dictionary {
 public:
  Dictionary() : next_(kInitialId) {}

  U Assign(T obj) {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    return AssignInternal(obj);
  }

  // return 0 means failed
  U AssignTo(T obj, U uid) {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    return AssignToInternal(obj, uid);
  }

  U AssignOrLookup(T obj) {
    U id = forward_lookup(obj);
    if (id == kInvalidId) {
      std::unique_lock<ReadWriteLock> lock(rwlock_);
      id = forward_lookup_internal(obj);
      if (id != kInvalidId) {
        return id;
      }
      id = AssignInternal(obj);
      return id;
    }
    return id;
  }

  bool Unassign(T obj) {
    std::unique_lock<ReadWriteLock> lock(rwlock_);
    const auto &it = forward_.find(obj);
    if (it == forward_.end()) {
      return false;
    }
    U id = it->second;
    free_list_.push_back(id);
    forward_.erase(obj);
    reverse_.erase(id);
    return true;
  }

  T reverse_lookup(U id) {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    const auto &it = reverse_.find(id);
    if (it == reverse_.end()) {
      return T();
    }
    return it->second;
  }

  U forward_lookup(T obj) const {
    std::shared_lock<ReadWriteLock> lock(rwlock_);
    return forward_lookup_internal(obj);
  }

 private:
  mutable ReadWriteLock rwlock_;
  // obj -> id
  std::unordered_map<T, U> forward_;
  // id -> obj
  std::unordered_map<U, T> reverse_;
  std::list<U> free_list_;
  U next_;

  U forward_lookup_internal(T obj) const {
    const auto &it = forward_.find(obj);
    if (it == forward_.end()) {
      return kInvalidId;
    }
    return it->second;
  }

  U AssignInternal(T obj) {
    const auto &it = forward_.find(obj);
    if (it != forward_.end()) {
      return it->second;
    }
    U id;
    if (free_list_.size() == 0) {
      assert(next_ + 1 > next_);
      id = next_++;
    } else {
      id = free_list_.front();
      free_list_.pop_front();
    }
    forward_[obj] = id;
    reverse_[id] = obj;
    return id;
  }

  U AssignToInternal(T obj, U uid) {
    const auto& it = forward_.find(obj);
    if (it != forward_.end()) {
      LOG(ERROR) << "duplicate obj"
                 << " uid=" << uid << " obj=" << obj;
      return kInvalidId;
    }
    if (reverse_.count(uid)) {
      LOG(ERROR) << "duplicate uid"
                 << " uid=" << uid << " obj=" << obj;
      return kInvalidId;
    }
    if (next_ <= uid) {
      for (U id = next_; id < uid; ++id) {
        free_list_.push_back(id);
      }
      next_ = uid + 1;
    } else {
      // slow path
      free_list_.remove(uid);
    }

    forward_[obj] = uid;
    reverse_[uid] = obj;
    return uid;
  }
};

}  // namespace dancenn

#endif  // BASE_DICTIONARY_H_
