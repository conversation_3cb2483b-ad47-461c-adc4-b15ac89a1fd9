#ifndef PARQUET_WRITER_H
#define PARQUET_WRITER_H

#include <arrow/io/file.h>
#include <cnetpp/base/ip_address.h>
#include <parquet/api/reader.h>
#include <parquet/api/writer.h>

#include <cstdint>
#include <string>

#include "constants.h"

using parquet::LogicalType;
using parquet::Repetition;
using parquet::Type;
using parquet::schema::GroupNode;
using parquet::schema::PrimitiveNode;

static const uint32_t kRowsPerRowGroup = 2000000;

namespace dancenn {
class ParquetWriter {
 public:
   ParquetWriter(std::string& file_path, std::string& compress_type_):
       file_path_(file_path),
       compress_type_(compress_type_) {};

   ~ParquetWriter();

   void WriteRow(uint64_t inode_id, uint64_t blk_id, uint64_t gs,
                 int32_t bytes, char* byte_array, int32_t array_size);
   bool Init();

 private:
   // Init schema for parquet.
   std::shared_ptr<GroupNode> SetupSchema();

   bool closed{true};
   std::string file_path_;
   std::string compress_type_;

   int32_t current_row_index_in_rowgroup_{0};

    // writer for a RowGroup with a specific number of rows.
    std::shared_ptr<parquet::ParquetFileWriter> file_writer_;
    // All the lifecycle of these writer is managed by <ParquetFileWriter>.
    // Need not to manage their lifecycle
    parquet::RowGroupWriter* rg_writer_{nullptr};
    parquet::ByteArrayWriter* dns_writer_{nullptr};
    parquet::Int64Writer* inode_id_writer_{nullptr};
    parquet::Int64Writer* block_id_writer_{nullptr};
    parquet::Int64Writer* gs_writer_{nullptr};
    parquet::Int32Writer* bytes_writer_{nullptr};
};
}

#endif //PARQUET_WRITER_H
