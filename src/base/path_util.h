//
// Copyright (c) 2017 Bytedance Inc. All rights reserved.
// Author: sunguoli <<EMAIL>>
//

#ifndef BASE_PATH_UTIL_H_
#define BASE_PATH_UTIL_H_

#include <absl/strings/str_join.h>
#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>

#include <sstream>
#include <stack>
#include <string>
#include <vector>
#include <algorithm>

#include "inode.pb.h"
#include "base/constants.h"
#include "base/status.h"
#include "base/time_util.h"
#include "base/uuid.h"

DECLARE_string(dfs_user_home_dir_prefix);
DECLARE_uint32(max_component_length);
DECLARE_uint32(max_path_length);
DECLARE_uint32(max_path_depth);

namespace dancenn {

extern const char* kDotReservedString;
extern const char* kDotReservedPathPrefix;
extern const std::string kRecycleBinDirNameString;
extern const char* kDotSnapshotDir;

// split path into piece:
// /a/b/c => "a", "b", "c"
inline bool SplitPath(const std::string &path,
                      std::vector<cnetpp::base::StringPiece> *component) {
  CHECK_NOTNULL(component);
  component->clear();
  component->reserve(64);
  if (path.empty() || path[0] != '/') {
    return false;
  }
  size_t last_pos = 0;
  for (size_t i = 1; i <= path.length(); i++) {
    if (i == path.length() || path[i] == '/') {
      if (i - last_pos - 1 > 0) {
        component->emplace_back(&path[last_pos + 1], i - last_pos - 1);
      }
      last_pos = i;
    }
  }
  return true;
}

inline std::string JoinPath(const std::vector<cnetpp::base::StringPiece>& path_components,
                            int start_idx,
                            int end_idx) {
  CHECK(path_components.size() >= end_idx);

  std::ostringstream ss;

  // Root.
  if (end_idx == 0) {
    return "/";
  }

  for (size_t i = start_idx; i < end_idx; ++i) {
    ss << "/";
    ss << path_components[i].as_string();
  }
  return ss.str();
}

inline std::string JoinPathFromRoot(const std::vector<cnetpp::base::StringPiece>& path_components,
                                    int end_idx = -1) {
  if (end_idx < 0) {
    end_idx = path_components.size();
  }
  return JoinPath(path_components, 0, end_idx);
}

inline std::string JoinPath(const std::vector<std::string>& paths) {
  std::ostringstream ss;
  for (const auto& path : paths) {
    ss << "/" << path;
  }
  return ss.str();
}

// Join two path without unnecessary /
// ("", "") -> ""
// ("a", "") -> "a"
// ("", "b") -> "b"
// ("a", "b") -> "a/b"
// ("a/", "b") -> "a/b"
// ("a", "/b") -> "a/b"
// ("a/", "/b") -> "a/b"
inline std::string JoinTwoPath(const std::string& a, const std::string& b) {
  std::ostringstream ss;
  if (!a.empty() && (a.back() == '/')) {
    ss << cnetpp::base::StringPiece(a.data(), a.size() - 1);
  } else {
    ss << a;
  }

  if (!a.empty() && !b.empty()) {
    ss  << '/';
  }

  if (!b.empty() && (b.front() == '/')) {
    ss << cnetpp::base::StringPiece(b.data() + 1, b.size() - 1);
  } else {
    ss << b;
  }
  return ss.str();
}

// /a/b/c => c,  /a/b/c/ => c,  /a/b//// => b
// abc => abc, a/bc => bc, "" => ""
inline std::string GetFileNameFromPath(const std::string& path) {
  std::string file_name = path;
  while (!file_name.empty() && file_name.back() == '/') {
    file_name.pop_back();
  }
  const size_t last_slash_idx = file_name.find_last_of('/');
  if (std::string::npos != last_slash_idx) {
    file_name.erase(0, last_slash_idx + 1);
  }
  return file_name;
}

// TODO(ruanjunbin): Discuss how to handle //a/b.
// https://bytedance.feishu.cn/docs/doccnYLyC5hgBnojaqQKwV8vL8f#
// /a/b/c => "/", "/a", "/a/b", "/a/b/c"
inline bool GetAllAncestorPaths(const std::string &path,
    std::vector<cnetpp::base::StringPiece> *ancestors) {
  CHECK_NOTNULL(ancestors);
  ancestors->clear();
  ancestors->reserve(64);
  if (path.empty() || path[0] != '/') {
    return false;
  }
  for (size_t i = 1; i <= path.length(); i++) {
    if (i == path.length() || path[i - 1] == '/') {
      if (i == 1 || i == path.length()) {
        ancestors->emplace_back(&path[0], i);
      } else {
        ancestors->emplace_back(&path[0], i - 1);
      }
    }
  }
  return true;
}

// "a","b","c" => /a/b
inline std::string GetParentPath(
    const std::vector<cnetpp::base::StringPiece>& path_components) {
  std::string parent;

  auto len = path_components.size();
  for (int i = 0; i < len - 1; ++i) {
    parent += "/";
    parent += path_components[i].as_string();
  }

  if (parent.empty()) {
    return "/";
  }

  return parent;
}

inline std::string GetParentPath(const std::string& path) {
  std::vector<cnetpp::base::StringPiece> components;
  if (!SplitPath(path, &components)) {
    return "";
  }
  return GetParentPath(components);
}

// remove ".." and "." in path.
// ignore multi "."
inline bool TransformDotInPath(std::string* path) {
  if (path->find('.') == std::string::npos) {
    return true;
  }
  std::vector<cnetpp::base::StringPiece> component;
  std::string tmp_path = *path;
  SplitPath(tmp_path, &component);
  size_t pre = 0;
  for (size_t i = 0; i < component.size(); i++) {
    if (component[i] == ".") {
      continue;
    }
    if (component[i] == "..") {
      if (pre == 0) {
        return false;
      }
      pre--;
      continue;
    }
    if (pre != i) {
      component[pre] = component[i];
    }
    pre++;
  }
  component.resize(pre);
  path->clear();

  if (component.empty()) {
    *path = "/";
    return true;
  }
  for (const auto &piece : component) {
    path->append("/");
    path->append(piece.as_string());
  }
  return true;
}

// "/" => "/"
// "//a//b" => "/a/b"
// "/a//b/c/" => "/a/b/c"
inline void RemoveRedundantSlash(std::string *path) {
  CHECK_NOTNULL(path);
  size_t pre = 0;
  bool is_pre_slash = false;
  for (size_t i = 0; i < path->size(); i++) {
    if ((*path)[i] != '/' || !is_pre_slash) {
      if (pre != i) {
        (*path)[pre] = (*path)[i];
      }
      pre++;
      if ((*path)[i] == '/') {
        is_pre_slash = true;
      } else {
        is_pre_slash = false;
      }
    }
  }
  path->resize(pre);
  if (path->size() > 1 && path->back() == '/') {
    path->pop_back();
  }
}

bool IsSnapshotComponent(const cnetpp::base::StringPiece& component);

// reference to:
// org.apache.hadoop.hdfs.DFSUtil#isReservedPathComponent
inline bool IsReservedPathComponent(const std::string& component) {
  return component == kDotReservedString || component == kDotSnapshotDir;
}
bool IsReservedPathComponent(const cnetpp::base::StringPiece& component);

inline Status VerifyPath(const std::string &path,
    const std::vector<cnetpp::base::StringPiece>& components) {
  if (path.length() > FLAGS_max_path_length || components.size() > FLAGS_max_path_depth) {
    return Status(JavaExceptions::kIOException, Code::kBadParameter,
        "PathName too long. Limit " + std::to_string(FLAGS_max_path_length)
        + " characters, " + std::to_string(FLAGS_max_path_depth) + " levels."
        + " path: " + path);
  }
  for (auto& c : components) {
    if (c.length() > FLAGS_max_component_length) {
      return Status(
          JavaExceptions::kIOException,
          Code::kBadParameter,
          "PathComponentName too long. Limit " +
              std::to_string(FLAGS_max_component_length) + " characters, " +
              " length: " + std::to_string(c.length()) + ", path: " + path);
    }
    // prevent writing .snapshot path
    if (IsReservedPathComponent(c)) {
      return Status(JavaExceptions::kIOException,
                    Code::kBadParameter,
                    "PathComponentName is reserved " + c.as_string());
    }
  }
  return Status();
}

// reference to:
// org.apache.hadoop.hdfs.DFSUtil#isValidName
// FSDirectory#getPathComponentsForReservedPath
inline bool NormalizePath(const std::string &path,
                          const std::string &username,
                          std::string *normalized_path) {
  CHECK_NOTNULL(normalized_path);
  normalized_path->clear();
  if (path.find(kDotReservedPathPrefix) != std::string::npos) {
    return false;
  }
  if (path.empty()) {
    if (username.empty()) {
      return false;
    }
    normalized_path->assign(FLAGS_dfs_user_home_dir_prefix + "/" + username);
    return true;
  }

  if (path[0] != '/') {
    if (username.empty()) {
      return false;
    }
    normalized_path->assign(FLAGS_dfs_user_home_dir_prefix);
    normalized_path->append("/").append(username).append("/");
  }
  normalized_path->append(path);

  // TODO(sunguoli):
  // org.apache.hadoop.hdfs.server.namenode.FSNamesystem#resolvePath
  // 处理一大堆可能用不到的保留文件路径

  RemoveRedundantSlash(normalized_path);
  return TransformDotInPath(normalized_path);
}

// reference to:
// org.apache.hadoop.hdfs.DFSUtil#isValidNameForComponent
inline Status IsValidNameForComponent(const std::string& component) {
  if (component.size() > FLAGS_max_component_length) {
    return Status(JavaExceptions::kInvalidPathException,
                  "Component name too long " + component + ", limit is " +
                      std::to_string(FLAGS_max_component_length));
  }
  if (component.empty() || component == "." || component == ".." ||
      component.find(':') != std::string::npos ||
      component.find('/') != std::string::npos) {
    return Status(JavaExceptions::kInvalidPathException,
                  "Invalid component name " + component);
  }
  if (IsReservedPathComponent(component)) {
    return Status(JavaExceptions::kInvalidPathException,
                  "Component name is reserved " + component);
  }
  return Status::OK();
}

inline bool CheckPathInRecycleBin(
    const std::vector<cnetpp::base::StringPiece>& path_components) {
  for (auto& c : path_components) {
    if (c.as_string() == kRecycleBinDirNameString) {
      return true;
    }
  }
  return false;
}

// "/path/to/file"
// "/.RECYCLE.BIN/username/YYYY-MM-DD/path/to/file.{epoch}.{uuid}"
inline bool GetRecycleBinPath(
    const std::vector<cnetpp::base::StringPiece>& path_components,
    const std::string username,
    std::string *rb_path) {
  CHECK_NOTNULL(rb_path);
  if (path_components.empty()) {
    // root has no valid recycle_bin_path
    return false;
  }
  for (auto& c : path_components) {
    if (c.as_string() == kRecycleBinDirNameString) {
      // file that already in recycle bin should be deleted instantly,
      // instead of moving to another path in recycle bin.
      return false;
    }
  }

  // 1. the topmost parents "/.RECYCLE.BIN/username/YYYY-MM-DD"
  std::vector<std::string> rb_path_comps =
      { kRecycleBinDirNameString,
        username,
        TimeUtil::GetNowYMD() };
  // 2. the middle parts "/path/to/file"
  for (int i = 0; i < path_components.size(); i++) {
    rb_path_comps.push_back(path_components.at(i).as_string());
  }
  // 3. the new filename "filename.timestamp.uuid"
  std::string rb_filename =
      std::to_string(TimeUtil::GetNowEpochMs())
      + "." + UUID().ToString().substr(0, 8);
  CHECK_LT(rb_filename.length(), FLAGS_max_component_length);
  rb_path_comps.push_back(rb_filename);
  // 4. join them together to new path
  *rb_path = kSeparator + absl::StrJoin(rb_path_comps, kSeparator);
  return true;
}

}  // namespace dancenn

#endif  // BASE_PATH_UTIL_H_
