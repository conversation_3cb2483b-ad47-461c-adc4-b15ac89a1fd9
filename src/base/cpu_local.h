// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_CPU_LOCAL_H_
#define BASE_CPU_LOCAL_H_

#include <glog/logging.h>

#include <sys/param.h>

#include <thread>

#include "base/platform.h"

#ifdef OS_DARWIN
#include <cpuid.h>
#endif

namespace dancenn {

template <class T>
class CpuLocal final {
 public:
  CpuLocal() {
    auto e = posix_memalign(reinterpret_cast<void**>(&ptr_), 128, 
                            kSizeAligned * GetNumCpus());
    if (e) {
      if (e == ENOMEM) {
        throw std::bad_alloc();
      }
      LOG(FATAL) << "posix_memalign failed " << e;
    }

    for (std::size_t i = 0; i < GetNumCpus(); ++i) {
      try {
        new (At(i)) T();
      } catch (...) {
        while (i > 0) {
          At(--i)->~T();
        }
        free(ptr_);
        throw;
      }
    }
  }

  ~CpuLocal() {
    for (std::size_t i = 0; i < GetNumCpus(); ++i) {
      At(i)->~T();
    }
    free(ptr_);
  }

  CpuLocal(const CpuLocal&) = delete;
  CpuLocal& operator=(const CpuLocal&) = delete;

  // N.B.: The returned pointer must be protected from concurrent accesses,
  // because the caller maybe preempted, or migrated to other CPUs. Of course,
  // if you bind each thread to a dedicated CPU, no additional lock is needed
  // any more.
  T* At() { return At(GetCpuId()); }

  T* At(std::size_t id) {
    // NOTICE: If enable CPU Hyper-Threading, sched_getcpu() <
    // std::thread::hardware_concurrency() is possible.
    CHECK_LT(id, GetNumCpus());
    return reinterpret_cast<T*>(ptr_ + kSizeAligned * id);
  }

  void Each(const std::function<void(T*)>& cb) {
    for (std::size_t i = 0; i < GetNumCpus(); ++i) {
      cb(At(i));
    }
  }

  void Each(const std::function<void(T*)>& cb,
            const std::function<void(T*)>& rollback) {
    for (std::size_t i = 0; i < GetNumCpus(); ++i) {
      try {
        cb(At(i));
      } catch (...) {
        while (i > 0) {
          rollback(At(--i));
        }
        throw;
      }
    }
  }

 private:
  static constexpr const std::size_t kSizeAligned{roundup(sizeof(T), 128)};

  char* ptr_{nullptr};
      
  size_t GetNumCpus() {
    static size_t size = std::thread::hardware_concurrency();
    return size;
  }

  size_t GetCpuId() {
#ifdef OS_DARWIN
    uint32_t CpuInfo[4];
    __cpuid_count(1, 0, CpuInfo[0], CpuInfo[1], CpuInfo[2], CpuInfo[3]);
    // CpuInfo[1] is EBX, bits 24-31 are APIC ID
    if ((CpuInfo[3] & (1 << 9)) == 0) {
      LOG(FATAL) << "No APIC on chip!";
    }
    return (unsigned)CpuInfo[1] >> 24;
#else
    return sched_getcpu();
#endif
  }
};

}  // namespace dancenn

#endif  // BASE_CPU_LOCAL_H_
