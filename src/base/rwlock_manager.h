// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_RWLOCK_MANAGER_H_
#define BASE_RWLOCK_MANAGER_H_

#include <cnetpp/base/string_piece.h>
#include <cnetpp/concurrency/spin_lock.h>

#include <memory>
#include <map>
#include <tuple>
#include <utility>
#include <vector>
#include <string>

#include "base/read_write_lock.h"
#include "base/rwlock_table.h"

DECLARE_bool(dir_lock_log_detail);

namespace dancenn {

class RWLockManager {
 public:
  using RWLockMap = std::map<std::string, std::shared_ptr<ReadWriteLock>>;

  typedef struct LockMeta {
    LockNode* l_{nullptr}; /* real lock */
    bool      is_read_lock_; /* is read lock */
  } LockMeta;

  RWLockManager(uint32_t depth);
  virtual ~RWLockManager();
  RWLockManager(const RWLockManager&) = delete;
  RWLockManager& operator=(const RWLockManager&) = delete;

  class LockHolder {
   public:
    LockHolder(RWLockManager* manager,
               LockMeta&& lock_meta)
        : manager_(manager),
          lock_meta_(std::move(lock_meta)) {
    }
    ~LockHolder() {
      LOG_IF(INFO, FLAGS_dir_lock_log_detail)
          << "unlocked token=" << lock_meta_.l_->name_;
      manager_->Unlock(&lock_meta_);
    }

    LockHolder(const LockHolder&) = delete;
    LockHolder& operator=(const LockHolder&) = delete;

    size_t NumHolders() const { // used for test
      return lock_meta_.l_->ref_;
    }

    bool IsReadLock() const {
      return lock_meta_.is_read_lock_;
    }

   private:
    RWLockManager* manager_{nullptr};
    LockMeta lock_meta_;
  };

  using PathLockMap = std::map<cnetpp::base::StringPiece,
               std::unique_ptr<RWLockManager::LockHolder>>;

  using PathLockMapPtr = std::unique_ptr<PathLockMap>;

  using DirTreeLock = std::vector<PathLockMapPtr>;

  using DirTreeLockPtr = std::unique_ptr<DirTreeLock>;

  size_t NumDynamicLocks() const {
    size_t n = 0;
    for (int i = 0; i < RWLockTableSliceN; i++) {
      n += rwlocks_[i]->dynamic_count();
    }
    return n;
  }

  size_t NumStaticLocks() const {
    size_t n = 0;
    for (int i = 0; i < RWLockTableSliceN; i++) {
      n += rwlocks_[i]->static_count();
    }
    return n;
  }

  // All paths except the last one will just acquire read locks
  virtual PathLockMapPtr LockPaths(
      const std::vector<cnetpp::base::StringPiece>& paths,
      bool last_wlock = true);

  virtual std::unique_ptr<LockHolder> ReadLock(
      const cnetpp::base::StringPiece& token,
      uint32_t depth) {
    LOG_IF(INFO, FLAGS_dir_lock_log_detail)
        << "acquire lock token=" << token.as_string() << " type="
        << "R"
        << " depth=" << depth;

    auto lock_meta = GetOrInsert(token, depth);
    lock_meta.l_->lock_.lock_shared();
    lock_meta.is_read_lock_ = true;
    auto ret = std::make_unique<LockHolder>(this, std::move(lock_meta));
    LOG_IF(INFO, FLAGS_dir_lock_log_detail) << "locked";

    return ret;
  }

  virtual std::unique_ptr<LockHolder> WriteLock(
      const cnetpp::base::StringPiece& token,
      uint32_t depth) {
    LOG_IF(INFO, FLAGS_dir_lock_log_detail)
        << "acquire lock token=" << token.as_string() << " type="
        << "W"
        << " depth=" << depth;

    auto lock_meta = GetOrInsert(token, depth);
    lock_meta.l_->lock_.lock();
    lock_meta.is_read_lock_ = false;
    auto ret = std::make_unique<LockHolder>(this, std::move(lock_meta));

    LOG_IF(INFO, FLAGS_dir_lock_log_detail) << "locked";

    return ret;
  }

 private:
  constexpr static int RWLockTableSliceN = 256;
  constexpr static int RWLockTableSlots  = 2017;

  void Unlock(LockMeta* lock_meta) {
    if (lock_meta->is_read_lock_) {
      lock_meta->l_->lock_.unlock_shared();
    } else {
      lock_meta->l_->lock_.unlock();
    }
    MaybeErase(lock_meta);
  }

  void MaybeErase(LockMeta* lock_meta);
  LockMeta GetOrInsert(const cnetpp::base::StringPiece& token, uint32_t depth);

  RWLockTable* rwlocks_[RWLockTableSliceN];
};

}  // namespace dancenn

#endif  // BASE_RWLOCK_MANAGER_H_
