// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_DEFER_H_
#define BASE_DEFER_H_

#include <functional>

namespace dancenn {

class Defer {
 public:
  explicit Defer(std::function<void()> run) : run_(run) {
  }
  ~Defer() {
    run_();
  }

  Defer(const Defer &) = delete;
  Defer& operator=(const Defer &) = delete;

 private:
  std::function<void()> run_;
};

}  // namespace dancenn
#define __DEFER(salt, func) dancenn::Defer __##salt##_defer((func))
#define _DEFER(salt, func) __DEFER(salt, (func))
#define DEFER(func) _DEFER(__LINE__, (func))

#endif  // BASE_DEFER_H_

