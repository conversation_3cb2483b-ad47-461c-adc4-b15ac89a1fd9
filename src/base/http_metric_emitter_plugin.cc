// Copyright 2019 livexmm <<EMAIL>>

#include "base/http_metric_emitter_plugin.h"

#include <glog/logging.h>
#include <cnetpp/http/http_request.h>
#include <cnetpp/http/http_response.h>
#include <cnetpp/http/http_options.h>
#include <cnetpp/base/uri.h>

#include "base/metrics.h"
#include "base/zlib_compress.h"

DECLARE_int32(metrics_emit_interval_ms);
DECLARE_string(metrics_emit_address);
DECLARE_string(nameservice);

namespace dancenn {

bool HttpMetricEmitterPlugin::ReportTask::operator()(void*) {
  LOG(INFO) << "Http metric emitter starting...";

  using HttpConnectionPtr = std::shared_ptr<cnetpp::http::HttpConnection>;

  std::unique_lock<std::mutex> lock(mutex_);
  while (!IsStopped()) {
    int interval = FLAGS_metrics_emit_interval_ms;
    if (interval < 1000) {
      interval = 10 * 1000;
    }
    cond_var_.wait_for(lock, std::chrono::milliseconds(interval), [&]() {
      return IsStopped();
    });
    if (IsStopped()) {
      break;
    }

    std::string address;
    if (!gflags::GetCommandLineOption("metrics_emit_address", &address) ||
        address.empty()) {
      continue;
    }

    std::string result;
    std::string compressed;
    MetricsCenter::Instance()->ToJson(&result);
    {
      CompressionOptions options;
      options.window_bits = ZLIB_GZIP_WBITS;
      if (!Zlib_Compress(options,
                         1,
                         result.data(),
                         result.size(),
                         &compressed)) {
        LOG(ERROR) << "zlib compress failed...";
        continue;
      }
    }

    cnetpp::http::HttpClientOptions options;
    options.set_connected_callback(
        [&address, &compressed] (HttpConnectionPtr c) -> bool {
      auto r = std::make_unique<cnetpp::http::HttpRequest>();
      cnetpp::base::Uri uri;
      if (!uri.Parse(address)) {
        return false;
      }
      r->set_method(cnetpp::http::HttpRequest::MethodType::kPost);
      r->set_uri(uri.Path());
      r->SetHttpHeader("User-Agent", "cnetpp/1.0");
      // r->SetHttpHeader("Content-Encoding", "gzip");
      r->SetHttpHeader("Host", address);
      r->SetHttpHeader("Content-Type", "text/plain");
      r->SetHttpHeader("Content-Length", std::to_string(compressed.size()));
      r->set_http_body(compressed);
      return c->SendPacket(r->ToString());
    });
    options.set_received_callback([] (HttpConnectionPtr c) -> bool {
      auto r = std::static_pointer_cast<cnetpp::http::HttpResponse>(
          c->http_packet());
      if (r->status() != cnetpp::http::HttpResponse::StatusCode::kOk) {
        LOG(ERROR) << "Failed to emit, status: "
                   << static_cast<int>(r->status())
                   << ", body: " << r->http_body();
      }
      c->MarkAsClosed();
      return true;
    });

    auto connection_id = client_->Connect(address, options);
    if (connection_id == cnetpp::tcp::kInvalidConnectionId) {
      LOG(ERROR) << "Failed to connect to " << address;
      continue;
    }

    // FIXME(livexmm) wait report finish
  }

  client_->Shutdown();
  LOG(INFO) << "Emitter plugin exiting...";
  return true;
}

bool HttpMetricEmitterPlugin::Start() {
  report_task_ = std::make_shared<ReportTask>();
  if (!report_task_->Init()) {
    return false;
  }
  reporter_ = std::make_unique<cnetpp::concurrency::Thread>(
      std::static_pointer_cast<cnetpp::concurrency::Task>(report_task_),
      "MEmitter");
  reporter_->Start();
  return true;
}

void HttpMetricEmitterPlugin::Stop() {
  if (reporter_) {
    reporter_->Stop();
    reporter_.reset();
  }
}

}  // namespace dancenn
