// Copyright 2017 He <PERSON> <<EMAIL>>

#ifndef BASE_WRITABLE_UTIL_H_
#define BASE_WRITABLE_UTIL_H_

#include <cstdint>

#include <sstream>
#include <utility>
#include <string>

namespace dancenn {

inline void WriteByte(std::ostream* ss, int8_t b) {
  ss->write(reinterpret_cast<const char*>(&b), sizeof(int8_t));
}

inline int8_t ReadByte(std::istream* ss) {
  int8_t b;
  ss->read(reinterpret_cast<char*>(&b), sizeof(int8_t));
  return b;
}

// See org/apache/hadoop/io/WritableUtils.java
inline void WriteVInt64(std::ostream* ss, int64_t i) {
  if (i >= -112 && i <= 127) {
    int8_t b = static_cast<int8_t>(i);
    WriteByte(ss, b);
    return;
  }
  int32_t len = -112;
  if (i < 0) {
    i ^= -1L;  // take one's complement
    len = -120;
  }
  int64_t tmp = i;
  while (tmp != 0) {
    tmp = tmp >> 8;
    len--;
  }
  WriteByte(ss, static_cast<int8_t>(len));
  len = (len < -120) ? -(len + 120) : -(len + 112);
  for (int32_t idx = len; idx != 0; idx--) {
    int32_t shiftbits = (idx - 1) * 8;
    int64_t mask = 0xFFL << shiftbits;
    WriteByte(ss, static_cast<int8_t>((i & mask) >> shiftbits));
  }
}

inline static int32_t DecodeVIntSize(int8_t value) {
  if (value >= -112) {
    return 1;
  } else if (value < -120) {
    return -119 - value;
  }
  return -111 - value;
}

inline static bool negative_vint(int8_t value) {
  return value < -120 || (value >= -112 && value < 0);
}

inline int64_t ReadVInt64(std::istream* ss) {
  int8_t first_byte = ReadByte(ss);
  int32_t len = DecodeVIntSize(first_byte);
  if (len == 1) {
    return first_byte;
  }
  int64_t i = 0;
  for (int32_t idx = 0; idx < len-1; idx++) {
    int8_t b = ReadByte(ss);
    i = i << 8;
    i = i | (b & 0xFF);
  }
  return (negative_vint(first_byte) ? (i ^ -1L) : i);
}

inline void WriteStringWithVIntPrefix(std::ostream* ss, std::string s) {
  WriteVInt64(ss, s.size());
  ss->write(&s.front(), s.size());
}

inline void ReadStringWithVIntPrefix(std::istream* ss, std::string* s) {
  int64_t len = ReadVInt64(ss);
  s->resize(len);
  ss->read(&s->front(), static_cast<size_t>(len));
}

inline std::string ReadStringWithVIntPrefix(std::istream* ss) {
  std::string s;
  ReadStringWithVIntPrefix(ss, &s);
  return s;
}

inline void WriteVInt32(std::ostream* ss, int32_t i) {
  WriteVInt64(ss, static_cast<int64_t>(i));
}

inline int32_t ReadVInt32(std::istream* ss) {
  return static_cast<int32_t>(ReadVInt64(ss));
}

}  // namespace dancenn

#endif  // BASE_WRITABLE_UTIL_H_
