//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

// The Header
#include "base/kafka_util.h"

namespace dancenn {

std::vector<RdKafka::TopicPartition*> KafkaUtil::OffsetMapToOffsets(
    const KafkaOffsetMap& offset_map) {
  std::vector<RdKafka::TopicPartition*> offsets;
  for (auto&& iter1 = offset_map.begin(); iter1 != offset_map.end(); ++iter1) {
    std::unordered_map<uint32_t, uint64_t> p_o_map = iter1->second;
    for (auto&& iter2 = p_o_map.begin(); iter2 != p_o_map.end(); ++iter2) {
      offsets.emplace_back(RdKafka::TopicPartition::create(
          /* topic */ iter1->first,
          /* partition */ iter2->first,
          /* offset where consumption will resume, i.e., the last processed
           * offset + 1 */
          iter2->second + 1));
    }
  }
  return std::move(offsets);
}

}  // namespace dancenn
