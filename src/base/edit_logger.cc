// Copyright 2022 Xinlong Gao <<EMAIL>>


#include <memory>
#include <mutex>
#include <string>
#include <utility>
#include "base/edit_logger.h"

namespace dancenn {

std::unique_ptr<EditLogger> EditLogger::edit_logger_;

DatabusEditLogger::DatabusEditLogger(const std::string& channel_name) {
  databus_sender_ = std::make_unique<DatabusSender>("Editlog", channel_name);
  task_.reset(new FlushTask(this));
  flusher_.reset(new cnetpp::concurrency::Thread(
        task_, "DatabusEditLoggerFlusher_" + channel_name));
  flusher_->Start();
}

std::string EditLogger::EncodeEditLog(std::shared_ptr<EditLogOp> op) {
  cnetpp::base::Value val = op->SerializeToJson();
  val["cfs_region"] = FLAGS_cfs_region;
  val["cfs_env"] = FLAGS_cfs_env;
  val["cfs_cluster"] = FLAGS_cfs_cluster;
  val["filesystem_id"] = std::to_string(FLAGS_filesystem_id);
  val["namespace_id"] = std::to_string(FLAGS_namespace_id);
  val["nn_ip"] = FLAGS_nn_local_ip;

  return cnetpp::base::Parser::Serialize(val);
}

void EditLogger::Init(std::unique_ptr<EditLogger>&& edit_logger) {
  if (edit_logger_) {
    LOG(WARNING) << "The edit logger is already initialized, "
                 << "will override it.";
  }
  edit_logger_ = std::move(edit_logger);
}

void DatabusEditLogger::Log(std::shared_ptr<EditLogOp> op) {
  std::unique_lock<std::mutex> guard(mtx_);
  editlog_ops_.push_back(op);
  cond_.notify_one();
}

void DatabusEditLogger::FlushTask::Stop() {
  std::unique_lock<std::mutex> guard(logger_->mtx_);
  cnetpp::concurrency::Task::Stop();
  logger_->cond_.notify_all();
}

bool DatabusEditLogger::FlushTask::operator()(void* args) {
  (void)args;
  std::unique_lock<std::mutex> guard(logger_->mtx_);
  while (!IsStopped()) {
    if (!logger_->editlog_ops_.empty()) {
      std::vector<std::shared_ptr<EditLogOp>> ops;
      ops.swap(logger_->editlog_ops_);
      guard.unlock();

      for (auto op : ops) {
        logger_->databus_sender_->Push(logger_->EncodeEditLog(op));
      }

      guard.lock();
    }
    logger_->cond_.wait_for(guard, std::chrono::seconds(1), [this]() -> bool {
      return IsStopped() || !logger_->editlog_ops_.empty();
    });
  }
  return true;
}

} // namespace dancenn
