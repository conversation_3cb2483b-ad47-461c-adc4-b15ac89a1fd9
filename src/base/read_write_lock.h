// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_READ_WRITE_LOCK_H_
#define BASE_READ_WRITE_LOCK_H_

#include <glog/logging.h>

#include <pthread.h>

#include <thread>
#include <system_error>
#include <shared_mutex>

#include "base/defer.h"
#include "base/platform.h"

namespace dancenn {

// // it's performance is not good, similar with pthread_rwlock_t,
// // but it can pass to any thread, never dead-lock in pthread_rwlock_t
// class FairRWMutexLock {
//   public:
//     FairRWMutexLock() {
//       pthread_mutex_init(&m_, NULL);
//       pthread_mutex_init(&writer_, NULL);
//       readers_ = 0;
//     }
//
//     ~FairRWMutexLock() {
//       pthread_mutex_destroy(&m_);
//       pthread_mutex_destroy(&writer_);
//     }
//
//     FairRWMutexLock(const FairRWMutexLock &) = delete;
//     FairRWMutexLock& operator=(const FairRWMutexLock &) = delete;
//
//     void lock() {
//       int r;
//       if ((r = pthread_mutex_lock(&m_)) != 0) {
//         LOG(FATAL) << "lock: pthread_mutex_lock(&m_) = " << r;
//       }
//       if ((r = pthread_mutex_lock(&writer_)) != 0) {
//         LOG(FATAL) << "lock: pthread_mutex_lock(&writer_) = " << r;
//       }
//       if ((r = pthread_mutex_unlock(&m_)) != 0) {
//         LOG(FATAL) << "lock: pthread_mutex_unlock(&m_) = " << r;
//       }
//     }
//
//     void unlock() {
//       int r;
//       if ((r = pthread_mutex_unlock(&writer_)) != 0) {
//         LOG(FATAL) << "unlock: pthread_mutex_unlock(&writer_) = " << r;
//       }
//     }
//
//     void lock_shared() {
//       int r;
//       if ((r = pthread_mutex_lock(&m_)) != 0) {
//         LOG(FATAL) << "lock_shared: pthread_mutex_lock(&m_) = " << r;
//       }
//       if (__sync_fetch_and_add(&readers_, 1) == 0) {
//         if ((r = pthread_mutex_lock(&writer_)) != 0) {
//           LOG(FATAL) << "lock_shared: pthread_mutex_lock(&writer_) = " << r;
//         }
//       }
//       if ((r = pthread_mutex_unlock(&m_)) != 0) {
//         LOG(FATAL) << "lock_shared: pthread_mutex_unlock(&m_) = " << r;
//       }
//     }
//
//     void unlock_shared() {
//       if (__sync_fetch_and_add(&readers_, -1) == 1) {
//         int r;
//         if ((r = pthread_mutex_unlock(&writer_)) != 0) {
//           LOG(FATAL) << "unlock_shared: pthread_mutex_unlock(&writer_) = " << r;
//         }
//       }
//     }
//
//   private:
//     pthread_mutex_t m_;
//     pthread_mutex_t writer_;
//     uint32_t        readers_;
// };

using ReadWriteLock = std::shared_timed_mutex;

class ReadWriteLockLight {
 public:
  ReadWriteLockLight(bool prefer_writer = false) {
    pthread_rwlockattr_t attr;
    pthread_rwlockattr_init(&attr);
    if (prefer_writer) {
#if defined(OS_LINUX)
      pthread_rwlockattr_setkind_np(
          &attr, PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP);
#endif
    }
    auto rc = pthread_rwlock_init(&lock_, &attr);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_init: " << rc;
    }
    pthread_rwlockattr_destroy(&attr);
  }

  ~ReadWriteLockLight() {
    int rc = pthread_rwlock_destroy(&lock_);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_destroy: " << rc;
    }
  }

  ReadWriteLockLight(const ReadWriteLockLight &) = delete;
  ReadWriteLockLight& operator=(const ReadWriteLockLight &) = delete;

  void lock_shared() {
    int rc = pthread_rwlock_rdlock(&lock_);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_rdlock: " << rc;
    }
  }

  void unlock_shared() {
    int rc = pthread_rwlock_unlock(&lock_);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_unlock: " << rc;
    }
  }

  void lock() {
    int rc = pthread_rwlock_wrlock(&lock_);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_wrlock: " << rc;
    }
  }

  void unlock() {
    int rc = pthread_rwlock_unlock(&lock_);
    if (rc != 0) {
      LOG(FATAL) << "pthread_rwlock_unlock: " << rc;
    }
  }

 private:
  pthread_rwlock_t lock_;
};

}  // namespace dancenn

#endif  // BASE_READ_WRITE_LOCK_H_
