// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_UUID_H_
#define BASE_UUID_H_

#include <string>

namespace dancenn {

class UUID {
 public:
  UUID();
  ~UUID() = default;

  std::string ToString() const {
    return (Digits(most_sig_bits_ >> 32, 8) + "-" +
            Digits(most_sig_bits_ >> 16, 4) + "-" +
            Digits(most_sig_bits_, 4) + "-" +
            Digits(least_sig_bits_ >> 48, 4) + "-" +
            Digits(least_sig_bits_, 12));
  }

 private:
  static std::string Digits(int64_t val, int digits);

  uint64_t most_sig_bits_ { 0 };
  uint64_t least_sig_bits_ { 0 };
};

}  // namespace dancenn

#endif  // BASE_UUID_H_

