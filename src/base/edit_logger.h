// Copyright 2022 Xinlong Gao <<EMAIL>>


#ifndef BASE_EDIT_LOGGER_H_
#define BASE_EDIT_LOGGER_H_

#include <cnetpp/concurrency/task.h>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include "base/databus.h"
#include "base/databus_sender.h"
#include "base/platform.h"
#include "base/status.h"
#include "cnetpp/concurrency/thread.h"
#include "edit/edit_log_op.h"


DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_string(nn_local_ip);

namespace dancenn {

class EditLogger;
class DatabusEditLogger;

class EditLogger {
 public:
  EditLogger() = default;
  virtual ~EditLogger() = default;

  EditLogger(const EditLogger&) = delete;
  EditLogger& operator=(const EditLogger&) = delete;

  virtual void Log(std::shared_ptr<EditLogOp> op) = 0;

  static void Init(std::unique_ptr<EditLogger>&& edit_logger);

  static void VLog(std::shared_ptr<EditLogOp> op) {
    if(UNLIKELY(edit_logger_ == nullptr)) {
      LOG(WARNING) << "EditLogger has not init yet, please EditLogger::Init first.";
      return;
    }
    edit_logger_->Log(op);
  }

protected:
  std::string EncodeEditLog(std::shared_ptr<EditLogOp> op);

 private:
  static std::unique_ptr<EditLogger> edit_logger_;
};

class DatabusEditLogger : public EditLogger {
 public:
  explicit DatabusEditLogger(const std::string& channel_name);
  virtual ~DatabusEditLogger() = default;

  DatabusEditLogger(const DatabusEditLogger&) = delete;
  DatabusEditLogger& operator=(const DatabusEditLogger&) = delete;

  void Log(std::shared_ptr<EditLogOp> op) override;

 private:
  class FlushTask : public cnetpp::concurrency::Task {
   public:
    explicit FlushTask(DatabusEditLogger* logger) : logger_(logger) {}
    ~FlushTask() {}
    void Stop() override;
    bool operator()(void* args = nullptr) override;
   private:
    DatabusEditLogger* logger_{nullptr};
  };

 private:
  std::mutex mtx_;
  std::condition_variable cond_;
  std::vector<std::shared_ptr<EditLogOp>> editlog_ops_;
  std::unique_ptr<cnetpp::concurrency::Thread> flusher_;
  std::shared_ptr<cnetpp::concurrency::Task> task_;
  std::unique_ptr<DatabusSender> databus_sender_;
};

} // namespace dancenn

#endif // BASE_EDIT_LOGGER_H_
