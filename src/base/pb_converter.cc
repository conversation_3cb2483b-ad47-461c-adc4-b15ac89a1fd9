// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/pb_converter.h"

#include <aws/core/utils/Array.h>
#include <aws/core/utils/base64/Base64.h>
#include <cnetpp/base/csonpp.h>
#include <glog/logging.h>
#include <google/protobuf/message.h>

namespace dancenn {

cnetpp::base::Object PBConverter::ToJson(
    const google::protobuf::Message& message) {
  const auto md = message.GetDescriptor();
  const auto ref = message.GetReflection();
  CHECK_NOTNULL(md);
  CHECK_NOTNULL(ref);

  cnetpp::base::Object res;

  for (size_t i = 0; i < md->field_count(); ++i) {
    auto fd = md->field(i);
    if (fd->is_repeated()) {
      cnetpp::base::Array repeats;
      auto length = ref->FieldSize(message, fd);
      for (size_t j = 0; j < length; ++j) {
        switch (fd->cpp_type()) {
          case google::protobuf::FieldDescriptor::CPPTYPE_INT32: {
            auto value = ref->GetRepeatedInt32(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_INT64: {
            auto value = ref->GetRepeatedInt64(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_UINT32: {
            auto value = ref->GetRepeatedUInt32(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_UINT64: {
            auto value = ref->GetRepeatedUInt64(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_FLOAT: {
            auto value = ref->GetRepeatedFloat(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_DOUBLE: {
            auto value = ref->GetRepeatedDouble(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_BOOL: {
            auto value = ref->GetRepeatedBool(message, fd, j);
            repeats.Append(cnetpp::base::Value(value));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_ENUM: {
            auto value = ref->GetRepeatedEnum(message, fd, j);
            repeats.Append(cnetpp::base::Value(value->number()));
            // repeats.Append(cnetpp::base::Value(value->name()));
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_STRING: {
            auto value = ref->GetRepeatedString(message, fd, j);
            // https://github.com/protocolbuffers/protobuf/blob/2.6.1-artifacts/src/google/protobuf/descriptor.cc#L82
            if (fd->type() == google::protobuf::FieldDescriptor::TYPE_BYTES) {
              repeats.Append(cnetpp::base::Value(
                  Aws::Utils::Base64::Base64().Encode(Aws::Utils::ByteBuffer(
                      reinterpret_cast<const unsigned char*>(value.c_str()),
                      value.size()))));
            } else {
              repeats.Append(cnetpp::base::Value(value));
            }
            break;
          }
          case google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE: {
            const auto& value = ref->GetRepeatedMessage(message, fd, j);
            // recursively convert
            repeats.Append(cnetpp::base::Value(ToJson(value)));
            break;
          }
          default:
            LOG(FATAL) << "Unknown FieldDescriptor type.";
        }
      }
      res[fd->name()] = repeats;
    } else if (ref->HasField(message, fd)) {
      switch (fd->cpp_type()) {
        case google::protobuf::FieldDescriptor::CPPTYPE_INT32: {
          auto value = ref->GetInt32(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_INT64: {
          auto value = ref->GetInt64(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_UINT32: {
          auto value = ref->GetUInt32(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_UINT64: {
          auto value = ref->GetUInt64(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_FLOAT: {
          auto value = ref->GetFloat(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_DOUBLE: {
          auto value = ref->GetDouble(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_BOOL: {
          auto value = ref->GetBool(message, fd);
          res[fd->name()] = value;
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_ENUM: {
          auto value = ref->GetEnum(message, fd);
          res[fd->name()] = value->number();
          // res[fd->name()] = value->name();
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_STRING: {
          auto value = ref->GetString(message, fd);
          // https://github.com/protocolbuffers/protobuf/blob/2.6.1-artifacts/src/google/protobuf/descriptor.cc#L82
          if (fd->type() == google::protobuf::FieldDescriptor::TYPE_BYTES) {
            res[fd->name()] =
                Aws::Utils::Base64::Base64().Encode(Aws::Utils::ByteBuffer(
                    reinterpret_cast<const unsigned char*>(value.c_str()),
                    value.size()));
          } else {
            res[fd->name()] = value;
          }
          break;
        }
        case google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE: {
          const auto& value = ref->GetMessage(message, fd);
          // recursively convert
          res[fd->name()] = ToJson(value);
          break;
        }
        default:
          LOG(FATAL) << "Unknown FieldDescriptor type.";
      }
    }
  }
  return res;
}

std::string PBConverter::ToCompactJsonString(
    const google::protobuf::Message& message) {
  return cnetpp::base::Parser::Serialize(
      cnetpp::base::Value(dancenn::PBConverter::ToJson(message)));
}

}  // namespace dancenn
