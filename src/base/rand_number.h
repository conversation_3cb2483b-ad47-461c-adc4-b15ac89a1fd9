#ifndef BASE_RAND_NUMBER_H_
#define BASE_RAND_NUMBER_H_

#include <cnetpp/concurrency/spin_lock.h>

#include <mutex>
#include <random>

namespace dancenn {

class RandNumber {
 public:
  static RandNumber* Instance() {
    std::call_once(flag_, [&] { rand_ = new RandNumber(); });
    return rand_;
  }

  RandNumber(const RandNumber&) = delete;
  RandNumber& operator=(const RandNumber&) = delete;

  bool NextBool();
  uint32_t NextInt32(uint32_t left, uint32_t right);  // in [left, right]
  uint32_t NextInt32();
  double NextDouble();  // in [0, 1]

 private:
  RandNumber() : rand_gen_(std::random_device{}()) {
  }
  ~RandNumber() = default;

  std::mt19937 rand_gen_;
  std::uniform_int_distribution<uint32_t> uni_dist_;
  cnetpp::concurrency::SpinLock spinlock_;

  static RandNumber* rand_;
  static std::once_flag flag_;
};

/*
 * Usage:
 *  SingleRandGenerator()->NextBool()
 *  SingleRandGenerator()->NextInt()
 */
RandNumber* SingleRandGenerator();

}  // namespace dancenn

#endif  // BASE_RAND_NUMBER_H_
