// Copyright 2018 <PERSON><PERSON> <<EMAIL>>
// From org.apache.commons.lang.time.StopWatch

#ifndef BASE_STOP_WATCH_H_
#define BASE_STOP_WATCH_H_

#include <absl/strings/str_format.h>
#include <cnetpp/concurrency/this_thread.h>
#include <glog/logging.h>

#include <chrono>
#include <memory>
#include <random>
#include <string>
#include <thread>

#include "base/metrics.h"
#include "base/string_utils.h"
#include "base/time_util.h"

DECLARE_bool(trace_rpc_log);
DECLARE_uint32(trace_rpc_log_every);

namespace dancenn {

class StopWatch {
 public:
  explicit StopWatch() : timer_() {}

  explicit StopWatch(const MetricID& timer) : timer_(timer) {}

  ~StopWatch() {
    if (timer_.ID() != UINT32_MAX) {
      DoStop();
      MFH(timer_)->Update(std::chrono::duration_cast<std::chrono::microseconds>(
          GetTime()).count());
    }
  }

  void NextStep() {
    Stop();
    if (timer_.ID() != UINT32_MAX) {
      MFH(timer_)->Update(std::chrono::duration_cast<std::chrono::microseconds>(
          GetTime()).count());
    }
    Reset();
    timer_ = MetricID();
    Start();
  }

  void NextStep(const MetricID& new_timer) {
    Stop();
    if (timer_.ID() != UINT32_MAX) {
      MFH(timer_)->Update(std::chrono::duration_cast<std::chrono::microseconds>(
          GetTime()).count());
    }
    Reset();
    timer_ = new_timer;
    Start();
  }

  size_t NextStepTime() {
    Stop();
    auto elapse = std::chrono::duration_cast<std::chrono::microseconds>(
        GetTime()).count();
    Reset();
    Start();
    return elapse;
  }

  // Start the stopwatch.
  // This method starts a new timing session, clearing any previous values.
  void Start() {
    CHECK(running_state_ != State::kStopped)
    << "Stopwatch must be reset before being restarted.";
    CHECK(running_state_ == State::kUnstarted) << "Stopwatch already started.";
    start_time_ = stop_time_ = std::chrono::steady_clock::now();
    running_state_ = State::kRunning;
  }

  // NOTICE: do not call this function manually, use NextStep()
  // Stop the stopwatch.
  // This method ends a new timing session, allowing the time to be retrieved.
  void Stop() {
    CHECK(running_state_ == State::kRunning ||
          running_state_ == State::kSuspended) << "Stopwatch is not running.";
    DoStop();
  }

  // Resets the stopwatch. Stops it if need be.
  // This method clears the internal values to allow the object to be reused.
  void Reset() {
    running_state_ = State::kUnstarted;
    split_state_ = State::kUnsplit;
    start_time_ = std::chrono::steady_clock::now();
    stop_time_ = std::chrono::steady_clock::now();
  }

  // Split the time.
  // This method sets the stop time of the watch to allow a time to be
  // extracted. The start time is unaffected, enabling unsplit() to continue
  // the timing from the original start point.
  void Split() {
    CHECK(running_state_ == State::kRunning) << "Stopwatch is not running.";
    stop_time_ = std::chrono::steady_clock::now();
    split_state_ = State::kSplit;
  }

  // Remove a split.
  // This method clears the stop time. The start time is unaffected,
  // enabling timing from the original start point to continue.
  void Unsplit() {
    CHECK(split_state_ == State::kSplit) << "Stopwatch has not been split.";
    stop_time_ = std::chrono::steady_clock::now();
    split_state_ = State::kUnsplit;
  }

  // Suspend the stopwatch for later resumption.
  // This method suspends the watch until it is resumed. The watch will not
  // include time between the suspend and resume calls in the total time.
  void Suspend() {
    CHECK(running_state_ == State::kRunning)
    << "Stopwatch must be running to suspend.";
    stop_time_ = std::chrono::steady_clock::now();
    running_state_ = State::kSuspended;
  }

  // Resume the stopwatch after a suspend.
  // This method resumes the watch after it was suspended. The watch will not
  // include time between the suspend and resume calls in the total time.
  void Resume() {
    CHECK(running_state_ == State::kSuspended)
    << "Stopwatch must be suspended to resume.";
    start_time_ += std::chrono::steady_clock::now() - stop_time_;
    stop_time_ = std::chrono::steady_clock::now();
    running_state_ = State::kRunning;
  }

  // Get the time on the stopwatch.
  // This is either the time between the start and the moment this method is
  // called, or the amount of time between start and stop.
  std::chrono::steady_clock::duration GetTime() {
    if (running_state_ == State::kStopped ||
        running_state_ == State::kSuspended) {
      return stop_time_ - start_time_;
    } else if (running_state_ == State::kUnstarted) {
      return std::chrono::steady_clock::duration::min();
    } else if (running_state_ == State::kRunning) {
      return std::chrono::steady_clock::now() - start_time_;
    }
    LOG(FATAL) << "Illegal running state has occurred.";
    return std::chrono::steady_clock::duration::min();  // omit compile warning
  }

  // Get the split time on the stopwatch.
  // This is the time between start and latest split.
  std::chrono::steady_clock::duration GetSplitTime() {
    CHECK(split_state_ == State::kSplit)
    << "Stopwatch must be split to get the split time.";
    return stop_time_ - start_time_;
  }

  // Returns the time this stopwatch was started.
  std::chrono::steady_clock::time_point GetStartTime() {
    CHECK(running_state_ != State::kUnstarted)
    << "Stopwatch has not been started";
    return start_time_;
  }

 private:
  enum class State : uint8_t {
    // running state
    kUnstarted = 0,
    kRunning,
    kStopped,
    kSuspended,

    // split state
    kUnsplit = 10,
    kSplit = 11,
  };
  // The current running state of the StopWatch.
  State running_state_ = {State::kUnstarted};
  // Whether the stopwatch has a split time recorded.
  State split_state_ = {State::kUnsplit};

  // The start time.
  std::chrono::steady_clock::time_point start_time_;
  // The stop time.
  std::chrono::steady_clock::time_point stop_time_;

  MetricID timer_;

  void DoStop() {
    if (running_state_ == State::kRunning) {
      stop_time_ = std::chrono::steady_clock::now();
    }
    running_state_ = State::kStopped;
  }
};

class LightweightStopWatch {
 public:
  void RecordAndNextStep(MetricID current_timer = MetricID()) {
    uint64_t next_ms = TimeUtilV2::GetNowEpochMs4Log();
    if (current_timer.ID() != UINT32_MAX) {
      MFH(current_timer)->Update((next_ms - current_ms_) * 1000);
    }
    current_ms_ = next_ms;
  }

 private:
  uint64_t current_ms_;
};

class StopWatchContext {
 public:
  static std::shared_ptr<StopWatchContext> MakeStopWatchContext(
      const std::string& ctx_name,
      const std::string& ctx_data,
      bool force = false) {
    if (FLAGS_trace_rpc_log) {
      auto seed =
          std::chrono::high_resolution_clock::now().time_since_epoch().count();
      std::mt19937 gen(seed);
      std::uniform_int_distribution<> distrib(1, FLAGS_trace_rpc_log_every);
      auto num = distrib(gen);

      if (force || (FLAGS_trace_rpc_log_every > 0 &&
                    num % FLAGS_trace_rpc_log_every == 0)) {
        return std::make_shared<StopWatchContext>(ctx_name, ctx_data);
      }
    }

    return nullptr;
  }

 public:
  StopWatchContext(const std::string& ctx_name, const std::string& ctx_data)
      : ctx_name_(ctx_name), ctx_data_(ctx_data) {
    sw_.Start();
    start_ =
        std::chrono::duration_cast<std::chrono::microseconds>(sw_.GetTime())
            .count();
    last_ = start_;

    std::ostringstream oss;
    oss << cnetpp::concurrency::ThisThread::GetId() << "."
        << std::chrono::duration_cast<std::chrono::microseconds>(
               sw_.GetStartTime() -
               std::chrono::steady_clock::time_point::min())
               .count();
    id_ = oss.str();
  }

  ~StopWatchContext() {
    sw_.Stop();
  }

  size_t DuringStart() {
    return std::chrono::duration_cast<std::chrono::microseconds>(sw_.GetTime())
        .count();
  }
  size_t NextStepTime() {
    auto now =
        std::chrono::duration_cast<std::chrono::microseconds>(sw_.GetTime())
            .count();
    auto elapse = now - last_;
    last_ = now;
    return elapse;
  }

  std::string TraceLog(const std::string& step_name) {
    std::ostringstream oss;
    oss << ctx_name_ << " " << step_name << " | " << ctx_data_ << " [id=" << id_
        << " during=" << StringUtils::FormatWithCommas(DuringStart())
        << " gap=" << StringUtils::FormatWithCommas(NextStepTime()) << "]";
    return oss.str();
  }

 private:
  StopWatch sw_;
  const std::string ctx_name_;
  const std::string ctx_data_;
  std::string id_;
  size_t start_ = 0;
  size_t last_ = 0;
};

#define RPC_SW_CTX_LOG(ctx, step_name)                  \
  LOG_IF(INFO, FLAGS_trace_rpc_log && (ctx) != nullptr) \
      << (ctx)->TraceLog(step_name);

#define RPC_SW_CTX_LOG_FUNCNAME(ctx)                    \
  LOG_IF(INFO, FLAGS_trace_rpc_log && (ctx) != nullptr) \
      << (ctx)->TraceLog(absl::StrFormat("in %s", __PRETTY_FUNCTION__));

#define RPC_SW_CTX_INIT_BY_CTX(rpc_sw_ctx, ctx)           \
  std::shared_ptr<StopWatchContext> rpc_sw_ctx = nullptr; \
  if ((ctx)) {                                            \
    rpc_sw_ctx = (ctx)->GetRpcSwCtx();                    \
  }

#define RPC_SW_CTX_INIT_WITH_CTX(rpc_sw_ctx, ctx, ctx_name, ctx_data)        \
  std::shared_ptr<StopWatchContext> rpc_sw_ctx = nullptr;                    \
  if ((ctx)) {                                                               \
    rpc_sw_ctx = (ctx)->GetRpcSwCtx();                                       \
  }                                                                          \
  if (rpc_sw_ctx == nullptr) {                                               \
    rpc_sw_ctx = StopWatchContext::MakeStopWatchContext(ctx_name, ctx_data); \
  }

#define RPC_SW_CTX_INIT(rpc_sw_ctx, ctx_name, ctx_data) \
  auto rpc_sw_ctx = StopWatchContext::MakeStopWatchContext(ctx_name, ctx_data);

#define RPC_SW_CTX_INIT_FORCE(rpc_sw_ctx, ctx_name, ctx_data) \
  auto rpc_sw_ctx =                                           \
      StopWatchContext::MakeStopWatchContext(ctx_name, ctx_data, true);

}  // namespace dancenn

#endif  // BASE_STOP_WATCH_H_

