// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/count_down_latch.h"

#include <glog/logging.h>

namespace dancenn {

void CountDownLatch::CountDown() {
    std::lock_guard<std::mutex> guard(mutex_);
    size_t old_value = count_--;
    if (count_ >= old_value) {  // overflowed
      LOG(FATAL) << "CountDownLatch entered invalid state!";
    }
    if (count_ == 0) {
      cv_.notify_all();
    }
}

bool CountDownLatch::Await(std::chrono::milliseconds timeout) {
  std::unique_lock<std::mutex> guard(mutex_);
  while (count_ > 0) {
    if (timeout.count() > 0) {  // timed wait
      if (cv_.wait_for(guard, timeout) == std::cv_status::timeout) {
        return false;
      }
    } else if (timeout.count() == 0) {  // do not wait
      return false;
    } else {  // wait forever
      cv_.wait(guard);
    }
  }
  return true;
}

}  // namespace dancenn

