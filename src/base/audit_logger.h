// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_AUDIT_LOGGER_H_
#define BASE_AUDIT_LOGGER_H_

#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <gflags/gflags.h>

#include <condition_variable>
#include <cstdarg>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <utility>

#include "base/constants.h"
#include "base/databus.h"
#include "base/databus_sender.h"
#include "base/platform.h"
#include "base/status.h"
#include "proto/generated/cloudfs/ClientNamenodeProtocol.pb.h"
#include "proto/generated/dancenn/audit_log.pb.h"

DECLARE_bool(audit_log_enabled);
DECLARE_string(cfs_region);
DECLARE_string(cfs_env);
DECLARE_string(cfs_cluster);
DECLARE_int64(filesystem_id);
DECLARE_int64(namespace_id);
DECLARE_string(nameservice);

namespace dancenn {

class AuditLogger;
class DatabusAuditLogger;

class AuditLogger {
 public:
  AuditLogger() = default;
  virtual ~AuditLogger() = default;

  AuditLogger(const AuditLogger&) = delete;
  AuditLogger& operator=(const AuditLogger&) = delete;

  virtual void Log(const char* fmt, va_list ap) = 0;
  virtual void Log(std::string&& s) = 0;

  static void Init(std::unique_ptr<AuditLogger>&& audit_logger);
  static void Finalize();

#if defined(OS_LINUX)
  __attribute__((format(printf, 1, 2)))
#endif
  static void
  VLog(const char* fmt, ...) {
    if(UNLIKELY(audit_logger_ == nullptr)) {
      LOG(WARNING) << "AuditLogger has not init yet, please AuditLogger::Init first.";
      return;
    }
    va_list ap;
    va_start(ap, fmt);
    audit_logger_->Log(fmt, ap);
    va_end(ap);
  }

  static void VLog(std::string&& s) {
    if(UNLIKELY(audit_logger_ == nullptr)) {
      LOG(WARNING) << "AuditLogger has not init yet, please AuditLogger::Init first.";
      return;
    }
    audit_logger_->Log(std::move(s));
  }

 private:
  static std::unique_ptr<AuditLogger> audit_logger_;
};

class DatabusAuditLogger : public AuditLogger {
 public:
  explicit DatabusAuditLogger(const std::string& channel_name);
  virtual ~DatabusAuditLogger() = default;

  DatabusAuditLogger(const DatabusAuditLogger&) = delete;
  DatabusAuditLogger& operator=(const DatabusAuditLogger&) = delete;

  void Log(const char* fmt, va_list ap) override;
  void Log(std::string&& s) override;

 private:
  std::unique_ptr<DatabusSender> databus_sender_;
};

namespace detail {

template <typename REQ, typename RESP>
inline void SetReqAndResp(AuditLog* audit_log,
                          const REQ& req,
                          const RESP& resp);

// Dir tree related.
template <>
inline void SetReqAndResp<cloudfs::MkdirsRequestProto,
                          cloudfs::MkdirsResponseProto>(
    AuditLog* audit_log,
    const cloudfs::MkdirsRequestProto& req,
    const cloudfs::MkdirsResponseProto& resp) {
  *audit_log->mutable_request()->mutable_mkdirs() = req;
  *audit_log->mutable_response()->mutable_mkdirs() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::DeleteRequestProto,
                          cloudfs::DeleteResponseProto>(
    AuditLog* audit_log,
    const cloudfs::DeleteRequestProto& req,
    const cloudfs::DeleteResponseProto& resp) {
  *audit_log->mutable_request()->mutable_delete_() = req;
  *audit_log->mutable_response()->mutable_delete_() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::ConcatRequestProto,
                          cloudfs::ConcatResponseProto>(
    AuditLog* audit_log,
    const cloudfs::ConcatRequestProto& req,
    const cloudfs::ConcatResponseProto& resp) {
  *audit_log->mutable_request()->mutable_concat() = req;
  *audit_log->mutable_response()->mutable_concat() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::RenameRequestProto,
                          cloudfs::RenameResponseProto>(
    AuditLog* audit_log,
    const cloudfs::RenameRequestProto& req,
    const cloudfs::RenameResponseProto& resp) {
  *audit_log->mutable_request()->mutable_rename() = req;
  *audit_log->mutable_response()->mutable_rename() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::Rename2RequestProto,
                          cloudfs::Rename2ResponseProto>(
    AuditLog* audit_log,
    const cloudfs::Rename2RequestProto& req,
    const cloudfs::Rename2ResponseProto& resp) {
  *audit_log->mutable_request()->mutable_rename2() = req;
  *audit_log->mutable_response()->mutable_rename2() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetListingRequestProto,
                          cloudfs::GetListingResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetListingRequestProto& req,
    const cloudfs::GetListingResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_listing() = req;
  *audit_log->mutable_response()->mutable_get_listing() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetContentSummaryRequestProto,
                          cloudfs::GetContentSummaryResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetContentSummaryRequestProto& req,
    const cloudfs::GetContentSummaryResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_content_summary() = req;
  *audit_log->mutable_response()->mutable_get_content_summary() = resp;
}

// File related.
template <>
inline void SetReqAndResp<cloudfs::CreateRequestProto,
                          cloudfs::CreateResponseProto>(
    AuditLog* audit_log,
    const cloudfs::CreateRequestProto& req,
    const cloudfs::CreateResponseProto& resp) {
  *audit_log->mutable_request()->mutable_create() = req;
  *audit_log->mutable_response()->mutable_create() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::AppendRequestProto,
                          cloudfs::AppendResponseProto>(
    AuditLog* audit_log,
    const cloudfs::AppendRequestProto& req,
    const cloudfs::AppendResponseProto& resp) {
  *audit_log->mutable_request()->mutable_append() = req;
  *audit_log->mutable_response()->mutable_append() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::MsyncRequestProto& req,
                          const cloudfs::MsyncResponseProto& resp) {
  *audit_log->mutable_request()->mutable_msync() = req;
  *audit_log->mutable_response()->mutable_msync() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::FsyncRequestProto& req,
                          const cloudfs::FsyncResponseProto& resp) {
  *audit_log->mutable_request()->mutable_fsync() = req;
  *audit_log->mutable_response()->mutable_fsync() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::CompleteRequestProto,
                          cloudfs::CompleteResponseProto>(
    AuditLog* audit_log,
    const cloudfs::CompleteRequestProto& req,
    const cloudfs::CompleteResponseProto& resp) {
  *audit_log->mutable_request()->mutable_complete() = req;
  *audit_log->mutable_response()->mutable_complete() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::GetFileInfoRequestProto& req,
                          const cloudfs::GetFileInfoResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_file_info() = req;
  *audit_log->mutable_response()->mutable_get_file_info() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::IsFileClosedRequestProto& req,
                          const cloudfs::IsFileClosedResponseProto& resp) {
  *audit_log->mutable_request()->mutable_is_file_close() = req;
  *audit_log->mutable_response()->mutable_is_file_close() = resp;
}

// Lease related.
template <>
inline void SetReqAndResp<cloudfs::RenewLeaseRequestProto,
                          cloudfs::RenewLeaseResponseProto>(
    AuditLog* audit_log,
    const cloudfs::RenewLeaseRequestProto& req,
    const cloudfs::RenewLeaseResponseProto& resp) {
  *audit_log->mutable_request()->mutable_renew_lease() = req;
  *audit_log->mutable_response()->mutable_renew_lease() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::RecoverLeaseRequestProto,
                          cloudfs::RecoverLeaseResponseProto>(
    AuditLog* audit_log,
    const cloudfs::RecoverLeaseRequestProto& req,
    const cloudfs::RecoverLeaseResponseProto& resp) {
  *audit_log->mutable_request()->mutable_recover_lease() = req;
  *audit_log->mutable_response()->mutable_recover_lease() = resp;
}

// Block related.
template <>
inline void SetReqAndResp<cloudfs::AddBlockRequestProto,
                          cloudfs::AddBlockResponseProto>(
    AuditLog* audit_log,
    const cloudfs::AddBlockRequestProto& req,
    const cloudfs::AddBlockResponseProto& resp) {
  *audit_log->mutable_request()->mutable_add_block() = req;
  *audit_log->mutable_response()->mutable_add_block() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::CommitLastBlockRequestProto,
                          cloudfs::CommitLastBlockResponseProto>(
    AuditLog* audit_log,
    const cloudfs::CommitLastBlockRequestProto& req,
    const cloudfs::CommitLastBlockResponseProto& resp) {
  *audit_log->mutable_request()->mutable_commit_last_block() = req;
  *audit_log->mutable_response()->mutable_commit_last_block() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::AbandonBlockRequestProto,
                          cloudfs::AbandonBlockResponseProto>(
    AuditLog* audit_log,
    const cloudfs::AbandonBlockRequestProto& req,
    const cloudfs::AbandonBlockResponseProto& resp) {
  *audit_log->mutable_request()->mutable_abandon_block() = req;
  *audit_log->mutable_response()->mutable_abandon_block() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::ReportBadBlocksRequestProto,
                          cloudfs::ReportBadBlocksResponseProto>(
    AuditLog* audit_log,
    const cloudfs::ReportBadBlocksRequestProto& req,
    const cloudfs::ReportBadBlocksResponseProto& resp) {
  *audit_log->mutable_request()->mutable_report_bad_blocks() = req;
  *audit_log->mutable_response()->mutable_report_bad_blocks() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetBlockLocationsRequestProto,
                          cloudfs::GetBlockLocationsResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetBlockLocationsRequestProto& req,
    const cloudfs::GetBlockLocationsResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_block_locations() = req;
  *audit_log->mutable_response()->mutable_get_block_locations() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetHyperBlockLocationsRequestProto,
                          cloudfs::GetHyperBlockLocationsResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetHyperBlockLocationsRequestProto& req,
    const cloudfs::GetHyperBlockLocationsResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_hyper_block_locations() = req;
  *audit_log->mutable_response()->mutable_get_hyper_block_locations() = resp;
}

// Set*/Get* related.
template <>
inline void SetReqAndResp<cloudfs::SetReplicationRequestProto,
                          cloudfs::SetReplicationResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetReplicationRequestProto& req,
    const cloudfs::SetReplicationResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_replication() = req;
  *audit_log->mutable_response()->mutable_set_replication() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetStoragePolicyRequestProto,
                          cloudfs::SetStoragePolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetStoragePolicyRequestProto& req,
    const cloudfs::SetStoragePolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_storage_policy() = req;
  *audit_log->mutable_response()->mutable_set_storage_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetStoragePoliciesRequestProto,
                          cloudfs::GetStoragePoliciesResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetStoragePoliciesRequestProto& req,
    const cloudfs::GetStoragePoliciesResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_storage_policies() = req;
  *audit_log->mutable_response()->mutable_get_storage_policies() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetDirPolicyRequestProto,
                          cloudfs::SetDirPolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetDirPolicyRequestProto& req,
    const cloudfs::SetDirPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_dir_policy() = req;
  *audit_log->mutable_response()->mutable_set_dir_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::RemoveDirPolicyRequestProto,
                          cloudfs::RemoveDirPolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::RemoveDirPolicyRequestProto& req,
    const cloudfs::RemoveDirPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_remove_dir_policy() = req;
  *audit_log->mutable_response()->mutable_remove_dir_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetDirPolicyRequestProto,
                          cloudfs::GetDirPolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetDirPolicyRequestProto& req,
    const cloudfs::GetDirPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_dir_policy() = req;
  *audit_log->mutable_response()->mutable_get_dir_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetReplicaPolicyRequestProto,
                          cloudfs::SetReplicaPolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetReplicaPolicyRequestProto& req,
    const cloudfs::SetReplicaPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_replica_policy() = req;
  *audit_log->mutable_response()->mutable_set_replica_policy() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::GetReplicaPolicyRequestProto& req,
                          const cloudfs::GetReplicaPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_replica_policy() = req;
  *audit_log->mutable_response()->mutable_get_replica_policy() = resp;
}

template <>
inline void SetReqAndResp(AuditLog* audit_log,
                          const cloudfs::SetReadPolicyRequestProto& req,
                          const cloudfs::SetReadPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_read_policy() = req;
  *audit_log->mutable_response()->mutable_set_read_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetReadPolicyRequestProto,
                          cloudfs::GetReadPolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetReadPolicyRequestProto& req,
    const cloudfs::GetReadPolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_read_policy() = req;
  *audit_log->mutable_response()->mutable_get_read_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetPermissionRequestProto,
                          cloudfs::SetPermissionResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetPermissionRequestProto& req,
    const cloudfs::SetPermissionResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_permission() = req;
  *audit_log->mutable_response()->mutable_set_permission() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetOwnerRequestProto,
                          cloudfs::SetOwnerResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetOwnerRequestProto& req,
    const cloudfs::SetOwnerResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_owner() = req;
  *audit_log->mutable_response()->mutable_set_owner() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::AllowSnapshotRequestProto,
                          cloudfs::AllowSnapshotResponseProto>(
    AuditLog* audit_log,
    const cloudfs::AllowSnapshotRequestProto& req,
    const cloudfs::AllowSnapshotResponseProto& resp) {
  *audit_log->mutable_request()->mutable_allow_snapshot() = req;
  *audit_log->mutable_response()->mutable_allow_snapshot() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::DisallowSnapshotRequestProto,
                          cloudfs::DisallowSnapshotResponseProto>(
    AuditLog* audit_log,
    const cloudfs::DisallowSnapshotRequestProto& req,
    const cloudfs::DisallowSnapshotResponseProto& resp) {
  *audit_log->mutable_request()->mutable_disallow_snapshot() = req;
  *audit_log->mutable_response()->mutable_disallow_snapshot() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::CreateSnapshotRequestProto,
                          cloudfs::CreateSnapshotResponseProto>(
    AuditLog* audit_log,
    const cloudfs::CreateSnapshotRequestProto& req,
    const cloudfs::CreateSnapshotResponseProto& resp) {
  *audit_log->mutable_request()->mutable_create_snapshot() = req;
  *audit_log->mutable_response()->mutable_create_snapshot() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::DeleteSnapshotRequestProto,
                          cloudfs::DeleteSnapshotResponseProto>(
    AuditLog* audit_log,
    const cloudfs::DeleteSnapshotRequestProto& req,
    const cloudfs::DeleteSnapshotResponseProto& resp) {
  *audit_log->mutable_request()->mutable_delete_snapshot() = req;
  *audit_log->mutable_response()->mutable_delete_snapshot() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::RenameSnapshotRequestProto,
                          cloudfs::RenameSnapshotResponseProto>(
    AuditLog* audit_log,
    const cloudfs::RenameSnapshotRequestProto& req,
    const cloudfs::RenameSnapshotResponseProto& resp) {
  *audit_log->mutable_request()->mutable_rename_snapshot() = req;
  *audit_log->mutable_response()->mutable_rename_snapshot() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetSnapshottableDirListingRequestProto,
                          cloudfs::GetSnapshottableDirListingResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetSnapshottableDirListingRequestProto& req,
    const cloudfs::GetSnapshottableDirListingResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_snapshottable_dir_listing() = req;
  *audit_log->mutable_response()->mutable_get_snapshottable_dir_listing() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetSnapshotDiffReportRequestProto,
                          cloudfs::GetSnapshotDiffReportResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetSnapshotDiffReportRequestProto& req,
    const cloudfs::GetSnapshotDiffReportResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_snapshot_diff_report() = req;
  *audit_log->mutable_response()->mutable_get_snapshot_diff_report() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::SetLifecyclePolicyRequestProto,
                          cloudfs::SetLifecyclePolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::SetLifecyclePolicyRequestProto& req,
    const cloudfs::SetLifecyclePolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_set_lifecycle_policy() = req;
  *audit_log->mutable_response()->mutable_set_lifecycle_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::UnsetLifecyclePolicyRequestProto,
                          cloudfs::UnsetLifecyclePolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::UnsetLifecyclePolicyRequestProto& req,
    const cloudfs::UnsetLifecyclePolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_unset_lifecycle_policy() = req;
  *audit_log->mutable_response()->mutable_unset_lifecycle_policy() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::GetLifecyclePolicyRequestProto,
                          cloudfs::GetLifecyclePolicyResponseProto>(
    AuditLog* audit_log,
    const cloudfs::GetLifecyclePolicyRequestProto& req,
    const cloudfs::GetLifecyclePolicyResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_lifecycle_policy() = req;
  *audit_log->mutable_response()->mutable_get_lifecycle_policy() = resp;
}

// Others.
template <>
inline void SetReqAndResp<cloudfs::HAServiceStateRequestProto,
                          cloudfs::HAServiceStateResponseProto>(
    AuditLog* audit_log,
    const cloudfs::HAServiceStateRequestProto& req,
    const cloudfs::HAServiceStateResponseProto& resp) {
  *audit_log->mutable_request()->mutable_get_ha_service_state() = req;
  *audit_log->mutable_response()->mutable_get_ha_service_state() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::LoadRequestProto,
                          cloudfs::LoadResponseProto>(
    AuditLog* audit_log,
    const cloudfs::LoadRequestProto& req,
    const cloudfs::LoadResponseProto& resp) {
  *audit_log->mutable_request()->mutable_load() = req;
  *audit_log->mutable_response()->mutable_load() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::FreeRequestProto,
                          cloudfs::FreeResponseProto>(
    AuditLog* audit_log,
    const cloudfs::FreeRequestProto& req,
    const cloudfs::FreeResponseProto& resp) {
  *audit_log->mutable_request()->mutable_free() = req;
  *audit_log->mutable_response()->mutable_free() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::PinRequestProto,
                          cloudfs::PinResponseProto>(
    AuditLog* audit_log,
    const cloudfs::PinRequestProto& req,
    const cloudfs::PinResponseProto& resp) {
  *audit_log->mutable_request()->mutable_pin() = req;
  *audit_log->mutable_response()->mutable_pin() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::LookupJobRequestProto,
                          cloudfs::LookupJobResponseProto>(
    AuditLog* audit_log,
    const cloudfs::LookupJobRequestProto& req,
    const cloudfs::LookupJobResponseProto& resp) {
  *audit_log->mutable_request()->mutable_lookup_job() = req;
  *audit_log->mutable_response()->mutable_lookup_job() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::CancelJobRequestProto,
                          cloudfs::CancelJobResponseProto>(
    AuditLog* audit_log,
    const cloudfs::CancelJobRequestProto& req,
    const cloudfs::CancelJobResponseProto& resp) {
  *audit_log->mutable_request()->mutable_cancel_job() = req;
  *audit_log->mutable_response()->mutable_cancel_job() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::BatchCreateFileRequestProto,
                          cloudfs::BatchCreateFileResponseProto>(
    AuditLog* audit_log,
    const cloudfs::BatchCreateFileRequestProto& req,
    const cloudfs::BatchCreateFileResponseProto& resp) {
  *audit_log->mutable_request()->mutable_batch_create() = req;
  *audit_log->mutable_response()->mutable_batch_create() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::BatchCompleteFileRequestProto,
                          cloudfs::BatchCompleteFileResponseProto>(
    AuditLog* audit_log,
    const cloudfs::BatchCompleteFileRequestProto& req,
    const cloudfs::BatchCompleteFileResponseProto& resp) {
  *audit_log->mutable_request()->mutable_batch_complete() = req;
  *audit_log->mutable_response()->mutable_batch_complete() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::BatchDeleteFileRequestProto,
                          cloudfs::BatchDeleteFileResponseProto>(
    AuditLog* audit_log,
    const cloudfs::BatchDeleteFileRequestProto& req,
    const cloudfs::BatchDeleteFileResponseProto& resp) {
  *audit_log->mutable_request()->mutable_batch_delete() = req;
  *audit_log->mutable_response()->mutable_batch_delete() = resp;
}

template <>
inline void SetReqAndResp<cloudfs::BatchGetFileRequestProto,
                          cloudfs::BatchGetFileResponseProto>(
    AuditLog* audit_log,
    const cloudfs::BatchGetFileRequestProto& req,
    const cloudfs::BatchGetFileResponseProto& resp) {
  *audit_log->mutable_request()->mutable_batch_get() = req;
  *audit_log->mutable_response()->mutable_batch_get() = resp;
}

}  // namespace detail

class AuditLogBuilder {
 public:
  AuditLogBuilder(AuditLog::Method method,
                  const std::string& user,
                  const std::string& grp,
                  const std::string& ip_addr,
                  const Status& status) {
    audit_log_.set_cfs_region(FLAGS_cfs_region);
    audit_log_.set_cfs_env(FLAGS_cfs_env);
    audit_log_.set_cfs_cluster(FLAGS_cfs_cluster);
    audit_log_.set_filesystem_id(FLAGS_filesystem_id);
    audit_log_.set_namespace_id(FLAGS_namespace_id);
    audit_log_.set_ts_nanoseconds(
        std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch())
            .count());
    thread_local int64_t tid =
        std::hash<std::thread::id>{}(std::this_thread::get_id());
    audit_log_.set_thread_id(tid);

    audit_log_.set_method(method);
    audit_log_.set_user(user);
    audit_log_.set_grp(grp);
    audit_log_.set_ip_addr(ip_addr);
    audit_log_.set_is_successful(status.IsOK());
    audit_log_.set_status(status.ExceptionStr());
  }

  template <typename REQ, typename RESP>
  AuditLogBuilder& SetReqAndResp(const REQ* req, const RESP* resp) {
    detail::SetReqAndResp<REQ, RESP>(&audit_log_, *req, *resp);
    return *this;
  }

  AuditLog&& Build() {
    return std::move(audit_log_);
  }

 private:
  AuditLog audit_log_;
};

#define DANCENN_AUDIT_LOG(fmt, ...)                                \
  do {                                                             \
    if (FLAGS_audit_log_enabled) {                                 \
      AuditLogger::VLog(                                           \
          ("time: %" PRIu64 ", nameservice: %s, " fmt),            \
          std::chrono::duration_cast<std::chrono::nanoseconds>(    \
              std::chrono::system_clock::now().time_since_epoch()) \
              .count(),                                            \
          FLAGS_nameservice.c_str(),                               \
          ##__VA_ARGS__);                                          \
    }                                                              \
  } while (0)

#define DANCENN_AUDIT_LOG2(                                                 \
    method, user, grp, ip_addr, status, request, response)                  \
  do {                                                                      \
    if (FLAGS_audit_log_enabled) {                                          \
      AuditLogger::VLog(AuditLogBuilder(method, user, grp, ip_addr, status) \
                            .SetReqAndResp(request, response)               \
                            .Build()                                        \
                            .SerializeAsString());                          \
    }                                                                       \
  } while (0)

}  // namespace dancenn

#endif  // BASE_AUDIT_LOGGER_H_
