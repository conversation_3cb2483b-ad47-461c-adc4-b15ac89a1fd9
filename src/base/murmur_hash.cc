//
// Copyright (c) 2022 Bytedance Inc. All rights reserved.
//

#include "murmur_hash.h"

#include "third/murmur_hash_3.h"

namespace dancenn {

uint32_t murmur_hash_32(const char* data, uint32_t len, uint32_t seed) {
  uint32_t result = 0;
  MurmurHash3_x86_32(reinterpret_cast<const void*>(data),
                     static_cast<int>(len),
                     seed,
                     reinterpret_cast<void*>(&result));
  return result;
}

}  // namespace dancenn
