// Copyright 2019 livexmm <<EMAIL>>

#ifndef BASE_METRIC_H_
#define BASE_METRIC_H_

#include <glog/logging.h>

#include <stdint.h>
#include <string.h>

#include <vector>
#include <string>
#include <atomic>
#include <chrono>
#include <algorithm>
#include <functional>

namespace dancenn {

class Gauge final {
 public:
  explicit Gauge(const std::string& name,
                 std::function<double()> getter = nullptr)
      : name_(name), getter_(std::move(getter)) {}
  ~Gauge() = default;

  const std::string& name() const {
    return name_;
  }

  void Update(double value) {
    value_.store(value, std::memory_order_release);
  }

  double MakeSnapshot() const {
    if (!getter_) {
      return value_.load(std::memory_order_acquire);
    } else {
      return getter_();
    }
  }

 private:
  const std::string name_;
  std::atomic<double> value_ { 0. };
  std::function<double()> getter_ { nullptr };
};

class AtomicCounter final {
 public:
  explicit AtomicCounter(const std::string& name)
      : name_(name), value_(0) {}
  ~AtomicCounter() {};

  void Inc() {
    Inc(1);
  }

  void Inc(int64_t value) {
    value_.fetch_add(value, std::memory_order_release);
  }

  void Set(int64_t value) {
    value_.store(value, std::memory_order_release);
  }

  int64_t MakeSnapshot() const {
    return value_.load(std::memory_order_acquire);
  }

  std::string name() const { return name_; }

 private:
  std::string          name_;
  std::atomic<int64_t> value_;
};

class Counter final {
 public:
  explicit Counter(uint32_t id) : id_(id), value_(0) {}
  ~Counter() {};

  void Inc() {
    Inc(1);
  }

  void Inc(int64_t value) {
    value_ += value;
  }

  void Set(int64_t value) {
    value_ = value;
  }

  int64_t GetValue() const { return value_; }

  uint32_t id() const { return id_; }

 private:
  uint32_t id_;
  int64_t  value_;
};

template<class T>
class GenericHistogram final {
  static_assert(std::is_floating_point<T>::value || std::is_integral<T>::value,
      "T must be numeric type!");
 public:
  typedef struct Item {
    T first;
    int64_t second;
  } Item;

  class Snapshot {
   public:
    Snapshot()
        : min_(std::numeric_limits<T>::max()),
          max_(std::numeric_limits<T>::min()),
          sum_(0),
          sorted_(false) {
    }
    Snapshot(uint32_t expires, const Item* items, uint32_t length)
        : min_(std::numeric_limits<T>::max()),
          max_(std::numeric_limits<T>::min()),
          sum_(0),
          sorted_(false) {
      items_.reserve(length);
      auto now = std::chrono::duration_cast<std::chrono::seconds>(
          std::chrono::system_clock::now().time_since_epoch()).count();
      for (uint32_t i = 0; i < length; i++) {
        if (now - items[i].second <= expires) {
          items_.emplace_back(items[i].first);
          if (items[i].first < min_) {
            min_ = items[i].first;
          }
          if (items[i].first > max_) {
            max_ = items[i].first;
          }
          sum_ += items[i].first;
        }
      }
    }
    ~Snapshot() {};

    void Merge(const GenericHistogram::Snapshot* snapshot) {
      items_.insert(items_.end(),
                    snapshot->items_.begin(),
                    snapshot->items_.end());
      if (min_ > snapshot->min_) {
        min_ = snapshot->min_;
      }
      if (max_ < snapshot->max_) {
        max_ = snapshot->max_;
      }
      sum_ += snapshot->sum_;
      sorted_ = false;
    }

    T Max() {
      return items_.size() == 0 ? 0 : max_;
    }
    T Min() {
      return items_.size() == 0 ? 0 : min_;
    }
    double Avg() {
      return items_.size() == 0 ? 0. : 1.0 * sum_ / items_.size();
    }
    double GetValue(double quantile) {
      if (quantile < 0 || quantile > 1.0) {
        LOG(FATAL) << "overflow: 0 <= quantile <= 1.0";
      }

      if (items_.size() == 0) {
        return 0.;
      }

      if (!sorted_) {
        std::sort(items_.begin(), items_.end());
        sorted_ = true;
      }

      double pos = quantile * (items_.size() + 1);
      size_t idx = static_cast<size_t>(pos);
      if (idx < 1) {
        return items_.front();
      } else if (idx >= items_.size()) {
        return items_.back();
      } else {
        double lower = items_[idx - 1];
        double upper = items_[idx];
        return lower + (pos - idx) * (upper - lower);
      }
    }

   private:
    T min_;
    T max_;
    T sum_;
    bool sorted_;
    std::vector<T> items_;
  };

  GenericHistogram(uint32_t id,
                   uint32_t window_size = 2048,
                   uint32_t expires = 60)
      : id_(id),
        expires_(expires),
        index_(0),
        items_(nullptr) {
    window_size_ = 1;
    while (window_size_ < window_size) {
      window_size_ <<= 1;
    }
    window_mask_ = window_size_ - 1;
    items_ = reinterpret_cast<Item*>(malloc(sizeof(Item) * window_size_));
    memset(items_, 0, sizeof(Item) * window_size_);
  }
  ~GenericHistogram() {
    free(items_);
  }

  void Update(T item) {
    uint32_t idx = index_ & window_mask_;
    items_[idx].first  = item;
    items_[idx].second = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    index_ = index_ + 1;
  }

  Snapshot* MakeSnapshot() {
    return new GenericHistogram<T>::Snapshot(expires_, items_, window_size_);
  }

  uint32_t id() const { return id_; }

 private:
  uint32_t id_;
  uint32_t window_size_;
  uint32_t window_mask_;
  uint32_t expires_;
  uint32_t index_;
  Item*    items_;
};

using Histogram = GenericHistogram<int64_t>;

}  // namespace dancenn

#endif  // BASE_METRIC_H_
