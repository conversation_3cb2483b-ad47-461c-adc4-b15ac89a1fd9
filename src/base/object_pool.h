#pragma once

#include <cstdint>
#include <memory>
#include <mutex>
#include <queue>

namespace dancenn {
template <typename T>
class ObjectPool {
 private:
  std::mutex lock_;
  std::queue<std::unique_ptr<T>> reuse_queue_;
  uint64_t reuse_limit_;

 protected:
  uint64_t current_size_;

  struct Deleter {
    ObjectPool<T>* parent_{nullptr};

    Deleter(ObjectPool<T>* parent = nullptr) : parent_{parent} {
    }

    void operator()(T* ptr) const {
      std::unique_lock<std::mutex> guard(parent_->lock_);
      if (parent_->reuse_queue_.size() >= parent_->reuse_limit_) {
        parent_->current_size_--;
        guard.unlock();
        delete ptr;
      } else {
        parent_->reuse_queue_.emplace(ptr);
      }
    }
  };

 public:
  using ObjectPtr = std::unique_ptr<T, Deleter>;

  ObjectPool(uint64_t reuse_limit)
      : reuse_limit_(reuse_limit), current_size_(0) {
  }

  ~ObjectPool() {
    while (!reuse_queue_.empty()) {
      reuse_queue_.pop();
    }
  }

 protected:
  template <typename Factory>
  ObjectPtr Get(Factory&& f) {
    std::unique_lock<std::mutex> guard(lock_);
    if (reuse_queue_.empty()) {
      current_size_++;
      guard.unlock();
      return {f(), this};
    }

    auto ptr = reuse_queue_.front().release();
    reuse_queue_.pop();

    return {ptr, this};
  }
};
}  // namespace dancenn
