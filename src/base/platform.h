// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_PLATFORM_H_
#define BASE_PLATFORM_H_

#if defined(linux) || defined(__linux) || defined(__linux__)
#if !defined(OS_LINUX)
#define OS_LINUX
#endif
#elif defined(macintosh) || defined(__APPLE__) || defined(__APPLE_CC__)
#if !defined(OS_DARWIN)
#define OS_DARWIN
#endif
#else
#error "Unknown Operating System!"
#endif

#if defined(OS_LINUX)
#include <endian.h>
#elif defined(OS_DARWIN)
#include <libkern/OSByteOrder.h>
#endif

#include <cstdint>

namespace dancenn {
namespace platform {

// Keep the order
enum class ByteOrder {
  kUnknown,
  kLittleEndian,
  kBigEndian,
};

inline ByteOrder HostByteOrder() {
#if defined(OS_LINUX)
#if __BYTE_ORDER == __LITTLE_ENDIAN
  return ByteOrder::kLittleEndian;
#else
  return ByteOrder::kBigEndian;
#endif
#elif defined(OS_DARWIN)
  return static_cast<ByteOrder>(OSHostByteOrder());
#endif
}

template<class T>
inline T HostToLittleEndian(T value);
template<class T>
inline T LittleEndianToHost(T value) {
  return HostToLittleEndian(value);
}

template<class T>
inline T HostToBigEndian(T value);
template<class T>
inline T BigEndianToHost(T value) {
  return HostToBigEndian<T>(value);
}

template <>
inline uint16_t HostToLittleEndian<uint16_t>(uint16_t value) {
#if defined(OS_LINUX)
  return htole16(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToLittleInt16(value);
#endif
}
template <>
inline uint32_t HostToLittleEndian<uint32_t>(uint32_t value) {
#if defined(OS_LINUX)
  return htole32(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToLittleInt32(value);
#endif
}
template <>
inline uint64_t HostToLittleEndian<uint64_t>(uint64_t value) {
#if defined(OS_LINUX)
  return htole64(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToLittleInt64(value);
#endif
}

template <>
inline uint16_t HostToBigEndian<uint16_t>(uint16_t value) {
#if defined(OS_LINUX)
  return htobe16(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToBigInt16(value);
#endif
}

template <>
inline uint32_t HostToBigEndian<uint32_t>(uint32_t value) {
#if defined(OS_LINUX)
  return htobe32(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToBigInt32(value);
#endif
}

template <>
inline uint64_t HostToBigEndian<uint64_t>(uint64_t value) {
#if defined(OS_LINUX)
  return htobe64(value);
#elif defined(OS_DARWIN)
  return OSSwapHostToBigInt64(value);
#endif
}

template<class T>
inline void WriteLittleEndian(volatile void* data, uintptr_t offset, T value);

template<class T>
inline T ReadLittleEndian(const volatile void* data, uintptr_t offset);

template<class T>
inline void WriteBigEndian(volatile void* data, uintptr_t offset, T value);

template<class T>
inline T ReadBigEndian(const volatile void* data, uintptr_t offset);

template<>
inline void WriteLittleEndian<uint16_t>(volatile void* data,
    uintptr_t offset, uint16_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint16_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToLittleEndian(value);
#elif defined(OS_DARWIN)
  OSWriteLittleInt16(data, offset, value);
#endif
}

template<>
inline void WriteLittleEndian<uint32_t>(volatile void* data,
    uintptr_t offset, uint32_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint32_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToLittleEndian(value);
#elif defined(OS_DARWIN)
  OSWriteLittleInt32(data, offset, value);
#endif
}

template<>
inline void WriteLittleEndian<uint64_t>(volatile void* data,
    uintptr_t offset, uint64_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint64_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToLittleEndian(value);
#elif defined(OS_DARWIN)
  OSWriteLittleInt64(data, offset, value);
#endif
}

template<>
inline uint16_t ReadLittleEndian<uint16_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return LittleEndianToHost(*reinterpret_cast<volatile uint16_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadLittleInt16(data, offset);
#endif
}

template<>
inline uint32_t ReadLittleEndian<uint32_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return LittleEndianToHost(*reinterpret_cast<volatile uint32_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadLittleInt32(data, offset);
#endif
}

template<>
inline uint64_t ReadLittleEndian<uint64_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return LittleEndianToHost(*reinterpret_cast<volatile uint64_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadLittleInt64(data, offset);
#endif
}

template<>
inline void WriteBigEndian<uint16_t>(volatile void* data,
    uintptr_t offset, uint16_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint16_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToBigEndian(value);
#elif defined(OS_DARWIN)
  OSWriteBigInt16(data, offset, value);
#endif
}

template<>
inline void WriteBigEndian<uint32_t>(volatile void* data,
    uintptr_t offset, uint32_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint32_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToBigEndian(value);
#elif defined(OS_DARWIN)
  OSWriteBigInt32(data, offset, value);
#endif
}

template<>
inline void WriteBigEndian<uint64_t>(volatile void* data,
    uintptr_t offset, uint64_t value) {
#if defined(OS_LINUX)
  *reinterpret_cast<volatile uint64_t*>(
      reinterpret_cast<uintptr_t>(data) + offset) = HostToBigEndian(value);
#elif defined(OS_DARWIN)
  OSWriteBigInt64(data, offset, value);
#endif
}

template<>
inline uint16_t ReadBigEndian<uint16_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return BigEndianToHost(*reinterpret_cast<volatile uint16_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadBigInt16(data, offset);
#endif
}

template<>
inline uint32_t ReadBigEndian<uint32_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return BigEndianToHost(*reinterpret_cast<volatile uint32_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadBigInt32(data, offset);
#endif
}

template<>
inline uint64_t ReadBigEndian<uint64_t>(const volatile void* data,
    uintptr_t offset) {
#if defined(OS_LINUX)
  return BigEndianToHost(*reinterpret_cast<volatile uint64_t*>(
        reinterpret_cast<uintptr_t>(data) + offset));
#elif defined(OS_DARWIN)
  return OSReadBigInt64(data, offset);
#endif
}

template<>
inline int64_t ReadBigEndian<int64_t>(const volatile void* data,
    uintptr_t offset) {
  return static_cast<int64_t>(ReadBigEndian<uint64_t>(data, offset));
}

#if defined(__GNUC__)
#define PREDICT_FALSE(x) (__builtin_expect(!!(x), 0))
#define PREDICT_TRUE(x) (__builtin_expect(!!(x), 1))
#else
#define PREDICT_FALSE(x) x
#define PREDICT_TRUE(x) x
#endif

}  // namespace platform
}  // namespace dancenn

#endif  // BASE_PLATFORM_H_

