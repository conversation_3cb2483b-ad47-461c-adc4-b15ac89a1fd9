// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_RETRY_CACHE_H_
#define BASE_RETRY_CACHE_H_

#include <google/protobuf/message.h>

#include <atomic>
#include <memory>
#include <mutex>
#include <queue>
#include <sstream>
#include <string>
#include <unordered_map>

#include "base/count_down_latch.h"
#include "base/lru_cache.h"
#include "base/metrics.h"
#include "base/status.h"

#define RETRY_CACHE_ENTER(retry_cache, rpc_ctl, rpc_resp, done_guard)   \
  std::shared_ptr<RetryCache::CacheEntry> _centry;                      \
  bool _hit = CheckRetryCacheEntry((retry_cache),                       \
                                   (rpc_ctl),                           \
                                   (rpc_resp),                          \
                                   &(done_guard),                       \
                                   &_centry);                           \
  if (!_hit && !_centry) {                                              \
    /* feature disabled */                                              \
  } else if (!_hit && _centry) {                                        \
    /* first-req, will be completed by myself */                        \
  } else if (_hit && !_centry) {                                        \
    /* retry-req, has been handed over to completer of first-req */     \
    CHECK(done_guard.empty());                                          \
    return;                                                             \
  } else {                                                              \
    /* retry-req, got cached result of completed first-req */           \
    auto _st = _centry->GetStatus();                                    \
    auto _cached_resp = _centry->GetPayload();                          \
    CompleteRetryRPC(rpc_ctl, rpc_resp, _st, _cached_resp);             \
    return;                                                             \
  }                                                                     \

#define RETRY_CACHE_EXIT(st, rpc_resp)                                  \
  if (_centry) {                                                        \
    _centry->Complete(                                                  \
        st,                                                             \
        std::make_shared<                                               \
            typename std::remove_reference<decltype(*rpc_resp)>::type>( \
            *rpc_resp));                                                \
  }

namespace dancenn {

class RpcController;
class Closure;
class ClosureGuard;

using PayloadType = ::google::protobuf::Message;
using PayloadTypePtr = std::shared_ptr<PayloadType>;

// RetryCache is a cache layer for Client-NN RPC requests, which may be retried
// and shall get the same result as the first execution.
//
// It maintains a set of CacheEntry, identified by 'client_id' and 'call_id',
// and provides procedures to access it properly:
// - for Active
//    Try to get a CacheEntry by
//              RetryCache::GetOrCreateCacheEntry()
//    1. For first-req, handle this request to get result, record it by
//              CacheEntry::Complete()
//    2. For retry-req, wait until the first-req completes by
//              CacheEntry::WaitForCompletion()
//       then get result.
// - for Standby
//    Insert or update cache entry by
//              RetryCache::AddCacheEntry()
//
// For more details, see:
// https://bytedance.feishu.cn/wiki/wikcnqibUPQtECzkU4EQNSSDmIe
class RetryCache {
 public:
  RetryCache();
  ~RetryCache();
  RetryCache(const RetryCache& other) = delete;
  RetryCache& operator=(const RetryCache& other) = delete;

  struct PendingRetryRPC {
    PendingRetryRPC(RpcController* rpc_ctl,
                    PayloadType* rpc_resp,
                    Closure* rpc_done)
      : ctl_(rpc_ctl),
        resp_(rpc_resp),
        done_(rpc_done) {}

    RpcController* ctl_;
    PayloadType* resp_;
    Closure* done_;
  };

  class CacheEntry {
   public:
    CacheEntry(RetryCache* retry_cache,
               const std::string& client_id,
               uint32_t call_id,
               std::chrono::steady_clock::time_point expiration_time,
               const std::string& debug_info)
        : retry_cache_(retry_cache),
          client_id_(std::move(client_id)),
          call_id_(call_id),
          completed_(false),
          expiration_time_(expiration_time),
          debug_info_(debug_info),
          latch_(1) {}

    // actions
    void Complete(const Status& st, PayloadTypePtr payload);
    bool CheckCompletedOrHandOver(RpcController* rpc_ctl,
                                  PayloadType* rpc_resp,
                                  ClosureGuard* done_guard);

    // status & data
    bool IsCompleted();
    Status GetStatus();
    PayloadTypePtr GetPayload();

    // others
    std::string ToString();

    std::string client_id() const {
      return client_id_;
    }
    uint32_t call_id() const {
      return call_id_;
    }
    std::chrono::steady_clock::time_point expiration_time() const {
      return expiration_time_;
    }

   private:
    std::string ToStringInternal();

    RetryCache* retry_cache_{nullptr};
    std::string client_id_;
    uint32_t call_id_;
    std::atomic<bool> completed_;
    Status status_;
    PayloadTypePtr payload_;
    std::chrono::steady_clock::time_point expiration_time_;
    std::string debug_info_;
    CountDownLatch latch_; // waiting for completion
    std::mutex mutex_; // protect integrity of inner fields
    std::queue<PendingRetryRPC> pending_retry_rpcs_;
  };

  bool GetOrCreateCacheEntry(const std::string& client_id,
                             uint32_t call_id,
                             const std::string& debug_info,
                             std::shared_ptr<CacheEntry>* centry);
  void RemoveCacheEntry(std::shared_ptr<CacheEntry>& entry);

  void AddCacheEntry(const std::string& client_id,
                     uint32_t call_id,
                     PayloadTypePtr payload,
                     const std::string& debug_info);

  std::shared_ptr<CacheEntry> GetCacheEntry(const std::string& client_id,
                                            uint32_t call_id);

 private:
  void InitMetrics();
  void FiniMetrics();
  void IncPayloadByte(int64_t delta);

  struct Key {
    std::string client_id_;
    uint32_t call_id_;

    Key(std::string client_id, uint32_t call_id)
        : client_id_(std::move(client_id)),
          call_id_(call_id) {
    }
    ~Key() = default;
    Key(const Key& other) = default;
    Key& operator=(const Key& other) = default;
    Key(Key&& other) = default;
    Key& operator=(Key&& other) = default;

    bool operator==(const Key& o) const {
      return call_id_ == o.call_id_ && client_id_ == o.client_id_;
    }

    struct Hasher {
      std::size_t operator()(const Key& k) const {
        return (std::hash<std::string>()(k.client_id_) ^
            (std::hash<uint32_t>()(k.call_id_)));
      }
    };
  };

  std::shared_ptr<Metrics> metrics_;
  MetricID hit_;
  MetricID miss_;
  MetricID added_;
  MetricID removed_;
  MetricID expired_;
  MetricID gc_cost_;
  MetricID payload_byte_;
  std::shared_ptr<Gauge> num_entries_;

  struct Slice {
    std::mutex mutex;
    std::unique_ptr<
        LRUCache<Key, std::shared_ptr<CacheEntry>, true, Key::Hasher>>
        entries;
  };
  struct CachelinePaddedSlice {
    static constexpr size_t kPaddingSize{128 - (alignof(Slice) % 128)};

    char prev_padding[kPaddingSize];
    Slice slice;
    char post_padding[kPaddingSize];
  };
  std::vector<CachelinePaddedSlice> slices_;

  void Gc(Slice& slice);
};


// Possible return cases:
//
// |      |  centry == nullptr     |  centry != nullptr               |
// | ---- | ---------------------- | -------------------------------- |
// | miss |  feature disabled      |  first-req, to be complete       |
// | hit  |  retry-req, queued up  |  retry-req, got cached response  |
//
//  The special exceptions are wrapped in this function.
bool CheckRetryCacheEntry(
    RetryCache* retry_cache,
    RpcController* ctl,
    PayloadType* resp,
    ClosureGuard* done_guard,
    std::shared_ptr<RetryCache::CacheEntry>* centry);

void CompleteRetryRPC(
    RpcController* ctl,
    PayloadType* resp,
    const Status& cached_status,
    PayloadTypePtr cached_resp);

}  // namespace dancenn

#endif  // BASE_RETRY_CACHE_H_
