//
// Copyright (c) 2024 Bytedance Inc. All rights reserved.
//

#include "base64.h"

#include <aws/core/utils/base64/Base64.h>

namespace dancenn {

Status Base64::Encode(const std::string& input, std::string* output) {
  if (input.length() == 0) {
    return Status(Code::kBadParameter, "input is empty");
  }
  try {
    std::string res =
        Aws::Utils::Base64::Base64().Encode(Aws::Utils::ByteBuffer(
            reinterpret_cast<const unsigned char*>(input.data()),
            input.length()));
    output->assign(std::move(res));
    return Status::OK();
  } catch (...) {
  }
  return Status(Code::kBadParameter,
                "Base64 encode failed, input data might be bad.");
}

Status Base64::Decode(const std::string& b64input, std::string* output) {
  if (b64input.length() == 0) {
    return Status(Code::kBadParameter, "input is empty");
  }
  try {
    auto&& res = Aws::Utils::Base64::Base64().Decode(b64input);
    output->assign(std::string(reinterpret_cast<char*>(res.GetUnderlyingData()),
                               res.GetLength()));
    return Status::OK();
  } catch (...) {
  }
  return Status(Code::kBadParameter,
                "Base64 decode failed, input data might be bad.");
}

size_t Base64::CalculateBase64DecodedLength(const std::string& b64input) {
  return Aws::Utils::Base64::Base64::CalculateBase64DecodedLength(b64input);
}

size_t Base64::CalculateBase64EncodedLength(const std::string& input) {
  return Aws::Utils::Base64::Base64::CalculateBase64EncodedLength(
      Aws::Utils::ByteBuffer(
          reinterpret_cast<const unsigned char*>(input.data()),
          input.length()));
}

}  // namespace dancenn
