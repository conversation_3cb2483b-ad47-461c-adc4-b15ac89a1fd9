#pragma once

#include <cnetpp/base/ip_address.h>
#include <hdfs.pb.h>

#include "base/rack_aware.h"

namespace dancenn {

class DatanodeInfo;

struct NetworkLocationInfo {
 public:
  // @param dn The writer's machine, null if not in the cluster.
  // @param location_tag The location info from client env
  cnetpp::base::IPAddress ip;
  cloudfs::LocationTag location_tag;
  DatanodeInfo* dn{nullptr};
  NetworkLocation location;
  std::string rdma_tag;

  explicit NetworkLocationInfo() = default;

  // TEST only
  explicit NetworkLocationInfo(const cnetpp::base::IPAddress& _ip);

  // TEST only
  explicit NetworkLocationInfo(const std::string& dc);

  explicit NetworkLocationInfo(const cnetpp::base::IPAddress& _ip,
                               const cloudfs::LocationTag& _location_tag);

  explicit NetworkLocationInfo(DatanodeInfo* _dn);

  std::string ToString() const;

 private:
  void Init();
};

}  // namespace dancenn