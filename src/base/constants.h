// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_CONSTANTS_H_
#define BASE_CONSTANTS_H_

#include <climits>
#include <cstdint>
#include <string>

#ifndef DANCENN_MAJOR_VERSION
#define DANCENN_MAJOR_VERSION 0
#endif

#ifndef DANCENN_MINOR_VERSION
#define DANCENN_MINOR_VERSION 0
#endif

#ifndef DANCENN_PATCH_VERSION
#define DANCENN_PATCH_VERSION 0
#endif

#define DANCENN_VERSION                                          \
  (DANCENN_MAJOR_VERSION * 10000 + DANCENN_MINOR_VERSION * 100 + \
   DANCENN_PATCH_VERSION)

typedef int32_t ProductVersion;

#ifndef UNLIKELY
#define UNLIKELY(x) __builtin_expect(!!(x), 0)
#endif

#ifndef LIKELY
#define LIKELY(x) __builtin_expect(!!(x), 1)
#endif

#define VLOG_OR_IF(verboselevel, condition) \
  LOG_IF(INFO, (condition) || VLOG_IS_ON(verboselevel))

#define LOG_WITH_LEVEL(level) LOG(level) << "[" << #level << "] "

namespace dancenn {
#if !defined(UNIX_PATH_MAX)
#define UNIX_PATH_MAX 256
#endif
#if !defined(PATH_MAX)
#define PATH_MAX 1024
#endif

extern const int32_t kDefaultLayoutVersion;

extern const uint64_t kRootINodeId;
extern const uint64_t kLastReservedINodeId;
extern const uint64_t kNumReservedINode;
extern const uint64_t kGrandfatherINodeId;
extern const uint64_t kInvalidINodeId;

extern const uint64_t kLastReservedGenerationStamp;
extern const uint64_t kGrandfatherGenerationStamp;
extern const uint64_t kBlockProtocolV2GenerationStamp;
extern const uint64_t kInvalidGenerationStamp;

extern const uint64_t kLastReservedBlockId;

extern const uint64_t kDefaultLastCkptTxId;

extern const char* kLayoutVersionKey;
extern const char* kCTimeKey;
extern const char* kClusterIdKey;
extern const char* kFileSystemIdKey;
extern const char* kNameSpaceIdKey;
extern const char* kNameSpaceTypeKey;
extern const char* kPersistentUfsInfoKey;
extern const char* kPersistentFlagsKey;
extern const char* kBlockPoolIdKey;
extern const char* kGenerationStampV1Key;
extern const char* kGenerationStampV2Key;
extern const char* kGenerationStampV1LimitKey;
extern const char* kLastAllocatedBlockIdKey;
extern const char* kLastINodeIdKey;
extern const char* kBlockInfoVersion;
extern const char* kBlockInfoVersionV2;
extern const char* kLeaseVersion;
extern const char* kLeaseVersionV2;
extern const char* kWriteBackTaskVersion;
extern const char* kWriteBackTaskVersionV2;
extern const char* kNumINodesKey;
extern const char* kLastCkptTxIdKey;
extern const char* kLastSnapshotIdKey;
extern const char* kNumSnapshotsKey;
extern const char* kAZBlacklistKey;
extern const char* kEditLogConfKey;
extern const char* kDirPolicyPersistedKey;
extern const char* kDirPolicyVersionV2;

extern const char* kSeparator;
extern const char kSeparatorChar;
extern const char* kCurrentDirectory;
extern const char* kInitStartAfter;

extern const char* kNamenodeLeaseHolder;

extern const char* kRootName;
extern const char kRootINodeKey[];
extern const int kRootINodeKeyLen;
extern const char kRootINodeKeyV2[];
extern const int kRootINodeKeyLenV2;

extern const char* kBlockStoragePolicyUnspecifiedName;
extern const char* kAllRAMStoragePolicyName;
extern const char* kMemoryStoragePolicyName;
extern const char* kOnlyDisk3StoragePolicyName;
extern const char* kOnlySSDStoragePolicyName;
extern const char* kAllSSDStoragePolicyName;
extern const char* kOnlyDisk2StoragePolicyName;
extern const char* kOneSSD2StoragePolicyName;
extern const char* kOneSSDStoragePolicyName;
extern const char* kHotStoragePolicyName;
extern const char* kWarmStoragePolicyName;
extern const char* kColdStoragePolicyName;

extern const char* kFsActionNone;
extern const char* kFsActionExecute;
extern const char* kFsActionWrite;
extern const char* kFsActionWriteExecute;
extern const char* kFsActionRead;
extern const char* kFsActionReadExecute;
extern const char* kFsActionReadWrite;
extern const char* kFsActionPermAll;

using TxID = int64_t;
extern const TxID kInvalidTxId;

extern const char* kReplicaPolicyXAttr;
extern const char* kReplicaPolicyTypeXAttr;  // Deprecate
extern const char* kReplicaPolicyDCXAttr;    // Deprecate
extern const char* kEnforceDCUnspecified;    // Deprecate
extern const int32_t kCentralizePolicy;      // Deprecate
extern const int32_t kDistributePolicy;      // Deprecate
extern const int32_t kNonePolicy;            // Deprecate

extern const char* kReadPolicyXAttr;

extern const char* kUploadPolicyXAttr;
extern const char* kForceUploadXAttr;

extern const char* kQuotaPolicyXAttr;

// extern const char* kTrashDirName;
// extern const char* kRecycleBinDirName;
extern const char* kRecyclePolicyXAttr;
extern const std::string kTrashDirNameString;
extern const std::string kRecycleBinDirNameString;

extern const char* kMountPointXAttr;

extern const char* kCreateRpcInfoXAttr;

extern const char* kObjectId;
extern const char* kTemperature;
extern const char* kTemperatureMtime;
extern const char* kTemperatureWarm;
extern const char* kTemperatureCoolingDown;
extern const char* kTemperatureCool;

extern const char* kSecurityXAttrUnreadableBySuperUser;

extern const char* kDfsPermissionsSuperUserGroupDefault;

extern const std::string kHyperBlockKey;
extern const std::string kHyperFileKey;
extern const int64_t kHyperBlockStripeSize;
extern const int32_t kHyperBlockSuffixLength;

// Snapshot Related
// only part of hdfs community pattern here, limited by std::strftime ability
extern const std::string kDefaultSnapshotNamePattern;
// from hdfs community, limit snapshot max id
extern const int32_t kSnapshotIdBitWidth;
// from hdfs community, limit snapshot num on each snapshot_root
extern const int32_t kSnapshotMaxLimitDefault;

extern const char* kStorageClassXAttr;
extern const uint32_t kBytesPerKB;
extern const uint32_t kBytesPerMB;
extern const uint32_t kBytesPerGB;

// ACC
extern const int32_t kSyncIntervalNever;
extern const int32_t kSyncIntervalAlways;

extern const char* kMallocConf;

}  // namespace dancenn

#endif  // BASE_CONSTANTS_H_
