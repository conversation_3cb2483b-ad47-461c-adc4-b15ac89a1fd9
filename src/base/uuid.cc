// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/uuid.h"

#include <algorithm>
#include <random>

namespace dancenn {

UUID::UUID() {
  std::mt19937_64 random_engine;
  random_engine.seed(std::random_device()());
  std::uniform_int_distribution<std::mt19937_64::result_type> distribution;
  for (size_t i = 0; i < 8; ++i) {
    auto t = distribution(random_engine) & 0xff;
    if (i == 6) {
      t &= 0x0f;
      t |= 0x40;
    }
    most_sig_bits_ = (most_sig_bits_ << 8) | t;
  }
  for (size_t i = 8; i < 16; ++i) {
    auto t = distribution(random_engine) & 0xff;
    if (i == 8) {
      t &= 0x3f;
      t |= 0x80;
    }
    least_sig_bits_ = (least_sig_bits_ << 8) | t;
  }
}

std::string UUID::Digits(int64_t val, int digits) {
  std::string res;
  int64_t hi = 1L << (digits * 4);
  hi |= (val & (hi - 1));
  while (hi > 0) {
    auto i = hi & 0xf;
    if (i >= 0 && i < 10) {
      res.append(1, static_cast<char>(i + '0'));
    } else {
      res.append(1, static_cast<char>(i - 10 + 'a'));
    }
    hi >>= 4;
  }
  res.pop_back();
  std::reverse(res.begin(), res.end() - 1);
  return res;
}

}  // namespace dancenn

