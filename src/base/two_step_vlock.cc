// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/11/22
// Description

#include "base/two_step_vlock.h"

#include <glog/logging.h>  // For CHECK_NOTNULL.
#include <pthread.h>       // For pthread_mutex_lock, pthread_mutex_unlock.
#include <sched.h>         // For sched_yield.

#include <utility>  // For move.

namespace dancenn {

TwoStepVLock::TwoStepVLock(VersionRWLock* lock) : lock_(lock) {
}

// Refer to VLock::lock.
// TwoStepVLock::Prepare + TwoStepVLock::Lock = VLock::lock.
void TwoStepVLock::Prepare() {
  pthread_mutex_lock(&lock_->wlock_);
  lock_->current_.version_ = 1;
  // may led to StoreLoad re-order, need mfence
#ifdef __arm__
  asm volatile("dsb sy" ::: "memory");
#elif __x86_64__
  asm volatile("mfence" ::: "memory");
#endif
}

// Refer to VLock::lock.
// TwoStepVLock::Prepare + TwoStepVLock::Lock = VLock::lock.
void TwoStepVLock::Lock() {
  while (true) {
    int x = 0;
    for (int i = 0; i < lock_->threads_; i++) {
      if (lock_->tls_[i].version_ == 0) {
        x++;
      }
    }
    if (x == lock_->threads_) {
      break;
    }
    sched_yield();
  }
  pthread_mutex_unlock(&lock_->wlock_);
}

// Refer to VLock::unlock.
void TwoStepVLock::Unlock() {
  asm volatile("" ::: "memory");
  lock_->current_.version_ = 0;
}

TwoStepVUniqueLock::TwoStepVUniqueLock(VersionRWLock* lock)
    : vlock_(std::make_unique<TwoStepVLock>(lock)), is_locked_(false) {
  vlock_->Prepare();
}

TwoStepVUniqueLock::~TwoStepVUniqueLock() {
  if (vlock_) {
    vlock_->Lock();
    vlock_->Unlock();
  }
}

void TwoStepVUniqueLock::Lock() {
  CHECK_NOTNULL(vlock_);
  if (!is_locked_) {
    vlock_->Lock();
    is_locked_ = true;
  }
}

TwoStepVUniqueLock::operator bool() const {
  return !!vlock_;
}

}  // namespace dancenn