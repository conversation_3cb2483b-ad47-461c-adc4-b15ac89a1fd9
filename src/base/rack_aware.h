// Copyright 2017 Li<PERSON> Lei <<EMAIL>>

#ifndef BASE_RACK_AWARE_H_
#define BASE_RACK_AWARE_H_

#include <cnetpp/base/csonpp.h>
#include <cnetpp/base/ip_address.h>
#include <cnetpp/base/string_utils.h>
#include <hdfs.pb.h>

#include <limits>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <tuple>

#include "base/metrics.h"
#include "base/read_write_lock.h"
#include "base/refresher.h"
#include "base/vlock.h"

namespace dancenn {

// legacy for old version
struct NetworkLocation {
  std::string dc;
  std::string rack;
  std::string host;

  // for faster compare
  size_t dc_id{std::numeric_limits<size_t>::max()};

  NetworkLocation() = default;
  NetworkLocation(const std::string d, const std::string r, const std::string h)
      : dc(d), rack(r), host(h) {
  }
  NetworkLocation(const cloudfs::LocationTag& tag)
      : dc(tag.az()), rack(tag.switch_()), host(tag.host()) {
  }

  std::string ToString() {
    return ToString(dc, rack, host);
  }

  static std::string ToString(const cloudfs::LocationTag& tag) {
    return ToString(tag.az(), tag.switch_(), tag.host());
  }

  static std::string ToString(const std::string& d,
                              const std::string& r,
                              const std::string& h) {
    std::ostringstream oss;
    oss << "/" << d << "/" << r << "/" << h;
    return oss.str();
  }
};

NetworkLocation ResolveNetworkLocation(const std::string& host);
NetworkLocation ResolveNetworkLocation(const cnetpp::base::IPAddress& ip);
bool ReloadNetworkLocationConfig(const std::string& config_filepath);

// [start, end] dc
struct DcIntervalEntry {
 public:
  // closed interval
  std::string ipv4_start;
  std::string ipv4_end;

  uint32_t number_start{0};
  uint32_t number_end{0};

  std::string dc;
};

struct Subnet {
public:
  // for new net work location conf
  Subnet(cnetpp::base::IPAddress i, int m, std::string d);

  // for legacy json conf
  Subnet(cnetpp::base::IPAddress start, cnetpp::base::IPAddress end, std::string d);

  cnetpp::base::IPAddress start_ip;
  cnetpp::base::IPAddress end_ip;
  std::string start_ip_str;
  std::string end_ip_str;

  int mask{0};
  std::string dc;

  std::string ToJson() const;

  bool Contains(const cnetpp::base::IPAddress &other);

  void UpdateStartIp(const cnetpp::base::IPAddress &other);

  void UpdateEndIp(const cnetpp::base::IPAddress &other);
};

class ConfigBasedRackAware {
 public:
  ~ConfigBasedRackAware();

  ConfigBasedRackAware(const ConfigBasedRackAware&) = delete;
  ConfigBasedRackAware& operator=(const ConfigBasedRackAware&) = delete;

  static ConfigBasedRackAware& GetSingleton();

  // example:
  //
  //  10.0.0.0/16 BJOFFICE
  //  ********/16 BJOFFICE
  //  *********/16 LF
  //  **********/16 AWS-US-EAST-1-SYS
  //  **********/24 VA
  //  FDBD:DC00:A997::/48 AGSXXA
  //  FDBD:DC00:A999::/48 QDTOB
  //  FDBD:DC01:24::/48 LF
  //  FDBD:DC01::/32 LF
  //  FDBD:DC02::/32 HL
  //  FDBD:DC02:FF:1:1::/80 BOE
  //  FDBD:DC02:FF:1:2::/80 BOE

  bool ParseAndUpdate(const std::string& config_string);

  bool ParseJsonAndUpdate(const std::string& config_string);


  NetworkLocation ResolveNetworkLocation(const std::string& host);
  NetworkLocation ResolveNetworkLocation(const cnetpp::base::IPAddress& ip);

  uint32_t GetConfigVersion() { return version_.load(); }

  std::vector<std::string> ListSubnet();

 private:
  ConfigBasedRackAware();

  bool UpdateConfig(std::map<std::string , Subnet> &src_table, std::vector<Subnet> &new_list);
  bool CheckConfigVersionStale(const std::string& config_string);

  bool ResolveRackV4(const cnetpp::base::IPAddress& ip, NetworkLocation* location);
  bool ResolveRackV6(const cnetpp::base::IPAddress& ip, NetworkLocation* location);
  bool ResolveDC(const cnetpp::base::IPAddress& ip, NetworkLocation* location,
                 std::map<std::string, Subnet>& ip_table);

 private:
  std::atomic<uint32_t> version_{1};
  std::string config_string_;

  std::map<std::string, Subnet> ipv4_subnet_table_;
  std::map<std::string, Subnet> ipv6_subnet_table_;

  std::string default_dc_{"UNKNOWN"};
  VRWLock rwlock_;

  std::unique_ptr<Refresher> refresher_{nullptr};

  MetricID rack_aware_resolve_host_time_;
  MetricID rack_aware_resolve_ip_time_;
};

}  // namespace dancenn

#endif  // BASE_RACK_AWARE_H_
