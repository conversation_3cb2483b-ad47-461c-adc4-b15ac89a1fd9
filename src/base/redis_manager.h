// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_REDIS_MANAGER_H_
#define BASE_REDIS_MANAGER_H_

#include <string>
#include <unordered_map>
#include <unordered_set>

struct redisContext;

namespace dancenn {

class IRedisManager {
  public:
    virtual ~IRedisManager() {}
    virtual bool Init(const std::string& backends, int timeout_ms) = 0;
    virtual void Stop() = 0;
    virtual bool IsInited() const = 0;

    virtual bool RedisHGetAll(const std::string& key,
        std::unordered_map<std::string, std::string>* result) = 0;

    virtual bool RedisHKeys(const std::string& key,
        std::unordered_set<std::string>* result) = 0;

    virtual bool RedisSMembers(const std::string& key,
        std::unordered_set<std::string>* result) = 0;

    virtual bool RedisGet(const std::string& key, std::string* result) = 0;

    virtual bool RedisHSet(const std::string& key, const std::string& field,
        const std::string& value) = 0;

    virtual bool RedisSAdd(const std::string& key, 
        const std::string& value) = 0;

    virtual bool RedisSet(const std::string& key, 
        const std::string& value) = 0;

};

class RedisManager : public IRedisManager {
 public:
  RedisManager() = default;
  ~RedisManager() {
    Stop();
  }

  bool Init(const std::string& backends, int timeout_ms);
  void Stop();
  bool IsInited() const;

  bool RedisHGetAll(const std::string& key,
      std::unordered_map<std::string, std::string>* result);

  bool RedisHKeys(const std::string& key,
      std::unordered_set<std::string>* result);

  bool RedisSMembers(const std::string& key,
      std::unordered_set<std::string>* result);

  bool RedisGet(const std::string& key, std::string* result);

  bool RedisHSet(const std::string& key, const std::string& field,
      const std::string& value);

  bool RedisSAdd(const std::string& key, const std::string& value);

  bool RedisSet(const std::string& key, const std::string& value);

 private:
  redisContext* context_ { nullptr };
};

}  // namespace dancenn

#endif  // BASE_REDIS_MANAGER_H_

