// Copyright 2019 livexmm <<EMAIL>>

#ifndef BASE_ACTOR_H_
#define BASE_ACTOR_H_

#include <cstdint>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <vector>

#include <glog/logging.h>
#include <cnetpp/concurrency/thread.h>
#include "base/ring_buffer.h"

namespace dancenn {
  template<class T>
  class Actor {
    static_assert(!std::is_class<T>::value,
        "Actor<T>, only support basic type and pointer");
    public:
      explicit Actor(const std::function<void(T)>& closure,
          int mailbox, const std::string& name = "");
      ~Actor();

      void Start();
      void Stop();

      int Length() { return length_; }

      void Tell(T message);

      void ThreadPoolIndex(int index) {
        thread_.SetThreadPoolIndex(index);
      }
    private:
      bool Pick(T* message);
      bool Run();

      template<typename TT = void,
        typename std::enable_if<!std::is_pointer<T>::value, TT>::type* = nullptr>
      void Cleanup() {}

      template<typename TT = void,
        typename std::enable_if<std::is_pointer<T>::value, TT>::type* = nullptr>
      void Cleanup() {
        T item;
        while (mailbox_.TryPop(&item)) {
          delete item;
        }
      }

    private:
      RingBuffer<T>               mailbox_;
      std::function<void(T)>      closure_;
      cnetpp::concurrency::Thread thread_;
      std::mutex                  tell_mutex_;
      std::condition_variable     tell_cond_;
      std::mutex                  pick_mutex_;
      std::condition_variable     pick_cond_;
      std::atomic<bool>           is_stop_;
      int                         length_;
  };

  template<class T>
  Actor<T>::Actor(const std::function<void(T)>& closure,
      int mailbox, const std::string& name)
  : mailbox_(mailbox),
    closure_(closure),
    thread_(std::bind(&Actor<T>::Run, this), name),
    is_stop_(true),
    length_(0) {}

  template<class T>
  Actor<T>::~Actor() {
    Stop();
    Cleanup();
  }

  template<class T>
  bool Actor<T>::Run() {
    T item;
    while (Pick(&item)) {
      closure_(item);
      __sync_fetch_and_add(&length_, -1);
    }
    return true;
  }

  template<class T>
  void Actor<T>::Start() {
    is_stop_.store(false, std::memory_order_release);
    thread_.Start();
  }

  template<class T>
  void Actor<T>::Stop() {
    is_stop_.store(true, std::memory_order_release);
    {
      std::unique_lock<std::mutex> lock(tell_mutex_);
      tell_cond_.notify_all();
    }
    {
      std::unique_lock<std::mutex> lock(pick_mutex_);
      pick_cond_.notify_all();
    }
    thread_.Stop();
  }

  template<class T>
  void Actor<T>::Tell(T message) {
    {
      std::unique_lock<std::mutex> lock(tell_mutex_);
      while (!mailbox_.TryPush(message)) {
        tell_cond_.wait(lock, [this](){
          return is_stop_.load(std::memory_order_acquire) || !mailbox_.Full();
        });
        if (is_stop_.load(std::memory_order_acquire)) return;
      }
      __sync_fetch_and_add(&length_, 1);
    }
    {
      std::unique_lock<std::mutex> lock(pick_mutex_);
      pick_cond_.notify_one();
    }
  }

  template<class T>
  bool Actor<T>::Pick(T* message) {
    {
      std::unique_lock<std::mutex> lock(pick_mutex_);
      while (!mailbox_.TryPop(message)) {
        pick_cond_.wait(lock, [this](){
          return is_stop_.load(std::memory_order_acquire) || !mailbox_.Empty();
        });
        if (is_stop_.load(std::memory_order_acquire)) return false;
      }
    }
    {
      std::unique_lock<std::mutex> lock(tell_mutex_);
      tell_cond_.notify_one();
    }

    return true;
  }

  // ==================== ActorGroup =====================

  template<class T, class Picker>
  class ActorGroup {
    static_assert(!std::is_class<T>::value,
        "ActorGroup<T>, only support basic type and pointer");
    public:
      explicit ActorGroup(const std::function<void(T)>& fn,
          int n, int mailbox, const std::string& name);
      ~ActorGroup();

      void Start();
      void Stop();

      void Tell(T message);
      void Tell(T message, int64_t id);
      int NumPending() const;
    private:
      Picker                 picker_;
      std::vector<Actor<T>*> actors_;
  };

  template<class T, class Picker>
  ActorGroup<T, Picker>::ActorGroup(const std::function<void(T)>& fn,
          int n, int mailbox, const std::string& name) : picker_() {
    actors_.reserve(n);
    for (int i = 0; i < n; i++) {
      auto actor = new Actor<T>(fn, mailbox,
          name + "-" + std::to_string(i));
      actor->ThreadPoolIndex(i);
      actors_.emplace_back(actor);
    }
  }

  template<class T, class Picker>
  ActorGroup<T, Picker>::~ActorGroup() {
    Stop();
    for (size_t i = 0; i < actors_.size(); i++) {
      delete actors_[i];
    }
  }

  template<class T, class Picker>
  void ActorGroup<T, Picker>::Start() {
    for (size_t i = 0; i < actors_.size(); i++) {
      actors_[i]->Start();
    }
  }

  template<class T, class Picker>
  void ActorGroup<T, Picker>::Stop() {
    for (size_t i = 0; i < actors_.size(); i++) {
      actors_[i]->Stop();
    }
  }

  template<class T, class Picker>
  void ActorGroup<T, Picker>::Tell(T message) {
    picker_.Next(actors_, 0)->Tell(message);
  }

  template<class T, class Picker>
  void ActorGroup<T, Picker>::Tell(T message, int64_t id) {
    picker_.Next(actors_, id)->Tell(message);
  }

  template<class T, class Picker>
  int ActorGroup<T, Picker>::NumPending() const {
    int n = 0;
    for (size_t i = 0; i <actors_.size(); i++) {
      n += actors_[i]->Length();
    }
    return n;
  }

  template<class T>
  class RoundRobinPicker {
    public:
      RoundRobinPicker() : index_(0) {}
      Actor<T>* Next(const std::vector<Actor<T>*>& actors, int64_t id) {
        (void)id;
        uint32_t index = index_.fetch_add(1);
        return actors[index % actors.size()];
      }
    private:
      std::atomic<uint32_t> index_;
  };

  template<class T>
  class BalancePicker {
    // in multi-producer, maybe performance is worse than round-robin
    public:
      BalancePicker() : last_index_(0) {}
      Actor<T>* Next(const std::vector<Actor<T>*>& actors, int64_t id) {
        (void)id;
        int index      = last_index_;
        int min_index  = last_index_;
        int min_length = actors[last_index_]->Length();
        do {
          int length = actors[index]->Length();
          if (length < min_length) {
            min_index  = index;
            min_length = length;
          }
          index = (index + 1) % actors.size();
        } while (index != last_index_);

        last_index_ = (min_index + 1) % actors.size();
        return actors[min_index];
      }
    private:
      int last_index_;
  };

  // For standby, we partition the WriteTask to specific executor if needed(id
  // != 0) so that tasks related to the same inode are executed in the order of
  // insertion.
  // Previously we use path lock to achieve this.
  // For active, PartitionedPicker fallbacks to BalancePicker.
  template <class T>
  class PartitionedPicker {
  public:
    PartitionedPicker() = default;
    Actor<T>* Next(const std::vector<Actor<T>*>& actors, int64_t id) {
      CHECK_GE(id, -1);
      if (id < 0) {
        return balance_picker_.Next(actors, 0);
      }
      return actors[id % actors.size()];
    }

  private:
    BalancePicker<T> balance_picker_;
  };
}

#endif
