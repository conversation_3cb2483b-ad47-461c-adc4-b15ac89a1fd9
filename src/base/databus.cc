// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/databus.h"

#include <cnetpp/base/socket.h>
#include <gflags/gflags.h>
#include <glog/logging.h>

#include <sys/socket.h>
#include <sys/un.h>

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "collector.pb.h"  // NOLINT(build/include)
#include "base/platform.h"
#include "base/constants.h"

DECLARE_int32(databus_protocol_version);
DECLARE_int32(databus_send_retry_count);
DECLARE_uint64(databus_connect_timeout_ms);
DECLARE_string(databus_socket_path);

namespace dancenn {

thread_local std::shared_ptr<DatabusConnection> g_conn;

static const int kHeaderLength = 64;

bool UnixStreamSocket::Create() {
  return Socket::Create(AF_UNIX, SOCK_STREAM, 0);
}

bool UnixStreamSocket::Connect(const std::string& socket_path,
                               std::chrono::milliseconds timeout) {
  memset(&socket_addr_, 0, sizeof(struct sockaddr_un));
  socket_addr_.sun_family = AF_UNIX;
  snprintf(socket_addr_.sun_path, UNIX_PATH_MAX, "%s", socket_path.c_str());

  if (timeout.count() != 0) {
    SetBlocking(false);
  }
  while (true) {
    if (connect(fd(),
                reinterpret_cast<const struct sockaddr*>(&socket_addr_),
                sizeof(socket_addr_)) == 0) {
      return true;
    }
    switch (cnetpp::concurrency::ThisThread::GetLastError()) {
      case EINTR:
        break;
      case EWOULDBLOCK:
        return false;
      case EINPROGRESS: {
        if (timeout.count() > 0) {
          auto start = std::chrono::system_clock::now();
          if (!WaitReadable(static_cast<int64_t>(timeout.count()))) {
            return false;
          }
          auto end = std::chrono::system_clock::now();
          int error = 0;
          socklen_t error_length = sizeof(error);
          if (GetOption(SOL_SOCKET, SO_ERROR, &error, &error_length)) {
            return true;
          }
          if (error && error != EINPROGRESS) {
            return false;
          }
          timeout -= std::chrono::duration_cast<std::chrono::milliseconds>(
              end - start);
          break;
        } else {
          return false;
        }
      }
      default:
        return false;
    }
  }
}

bool UnixStreamSocket::SendAll(const void* buffer,
                               size_t buffer_size,
                               size_t* sent_size,
                               int flags,
                               bool auto_restart) {
  *sent_size = 0;
  while (buffer_size > 0) {
    size_t current_sent_size;
    if (Send(buffer, buffer_size, &current_sent_size, flags, auto_restart)) {
      buffer = reinterpret_cast<const char*>(buffer) + current_sent_size;
      buffer_size -= current_sent_size;
      *sent_size += current_sent_size;
    } else {
      if (cnetpp::concurrency::ThisThread::GetLastError() != EAGAIN) {
        return false;
      }
    }
  }
  return true;
}

bool DatabusConnection::Init() {
  socket_ = std::make_unique<UnixStreamSocket>();
  if (!socket_->Create() ||
      !socket_->Connect(FLAGS_databus_socket_path,
          std::chrono::milliseconds(FLAGS_databus_connect_timeout_ms))) {
    return false;
  }
  return true;
}

bool DatabusChannel::Emit(const std::string& message, int codec) {
  collector::RequestPayload payload;
  payload.set_channel(channel_);
  collector::ApplicationMessage *msg = payload.add_messages();
  msg->set_value(message);
  msg->set_codec(codec);
  msg->set_partition(-1);
  return SendPayload(payload);
}

bool DatabusChannel::Emit(const std::string& key,
                          const std::string& message,
                          int codec) {
  collector::RequestPayload payload;
  payload.set_channel(channel_);
  collector::ApplicationMessage* msg = payload.add_messages();
  msg->set_key(key);
  msg->set_value(message);
  msg->set_codec(codec);
  msg->set_partition(-1);
  return SendPayload(payload);
}

bool DatabusChannel::Emit(const std::vector<std::string>& messages,
                          int codec,
                          int partition) {
  collector::RequestPayload payload;
  payload.set_channel(channel_);
  for (auto& m : messages) {
    collector::ApplicationMessage* msg = payload.add_messages();
    msg->set_value(m);
    msg->set_codec(codec);
    msg->set_partition(partition);
  }
  return SendPayload(payload);
}

bool DatabusChannel::Emit(
    const std::vector<std::pair<std::string, std::string>>& messages,
    int codec) {
  collector::RequestPayload payload;
  payload.set_channel(channel_);
  for (auto& m : messages) {
    collector::ApplicationMessage* msg = payload.add_messages();
    msg->set_key(m.first);
    msg->set_value(m.second);
    msg->set_codec(codec);
    msg->set_partition(-1);
  }
  return SendPayload(payload);
}

bool DatabusChannel::InitConnection() {
  if (!g_conn) {
    auto conn = std::make_shared<DatabusConnection>();
    if (conn->Init()) {
      g_conn.swap(conn);
    }
  }
  return g_conn != nullptr;
}

bool DatabusChannel::SendPayload(const collector::RequestPayload& payload) {
  auto pl = payload.SerializeAsString();
  char p[kHeaderLength];
  p[0] = static_cast<char>(FLAGS_databus_protocol_version);
  platform::WriteBigEndian<uint32_t>(p, 1, pl.size());

  for (int i = 0; i < FLAGS_databus_send_retry_count; ++i) {
    if (!InitConnection()) {
      g_conn.reset();
      continue;
    }
    size_t sent_size = 0;
    if (!g_conn->socket_->SendAll(p, kHeaderLength, &sent_size, 0, true)) {
      LOG(WARNING) << "Send databus header failed: "
                   << cnetpp::concurrency::ThisThread::GetLastErrorString();
      g_conn.reset();
      continue;
    }
    if (!g_conn->socket_->SendAll(pl.c_str(), pl.size(), &sent_size, 0, true)) {
      LOG(WARNING) << "Send databus body failed: "
                   << cnetpp::concurrency::ThisThread::GetLastErrorString();
      g_conn.reset();
      continue;
    }
    return true;
  }
  return false;
}

}  // namespace dancenn
