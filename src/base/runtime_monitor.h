// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_RUNTIME_MONITOR_H_
#define BASE_RUNTIME_MONITOR_H_

#include <glog/logging.h>
#include <cnetpp/concurrency/thread.h>

#include <atomic>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <unordered_map>
#include <deque>

#include "base/metric.h"
#include "base/metrics.h"

namespace dancenn {

class RuntimeMonitor {
 public:
  explicit RuntimeMonitor(int check_interval_in_ms = 60000);
  virtual ~RuntimeMonitor();

  RuntimeMonitor(const RuntimeMonitor&) = delete;
  RuntimeMonitor& operator=(const RuntimeMonitor&) = delete;

  virtual void GetMemoryUsage(uint64_t *total, uint64_t *proc_total,
      uint64_t *proc_rss) const {
    CHECK_NOTNULL(total);
    CHECK_NOTNULL(proc_total);
    CHECK_NOTNULL(proc_rss);

    *total = ram_total_metric_->MakeSnapshot();
    *proc_total = proc_memory_total_metric_->MakeSnapshot();
    *proc_rss = proc_memory_rss_metric_->MakeSnapshot();
  }

  virtual void GetCpuUsage(double* proc_usage,
                           double* load_avg_1,
                           double* load_avg_5,
                           double* load_avg_15) {
    CHECK_NOTNULL(proc_usage);
    CHECK_NOTNULL(load_avg_1);
    CHECK_NOTNULL(load_avg_5);
    CHECK_NOTNULL(load_avg_15);
    *proc_usage = proc_cpu_usage_metric_->MakeSnapshot();
    *load_avg_1 = load_avg_1_metric_->MakeSnapshot();
    *load_avg_5 = load_avg_5_metric_->MakeSnapshot();
    *load_avg_15 = load_avg_15_metric_->MakeSnapshot();
  }

  virtual void GetMetaStorageDiskUsage(uint64_t* total,
                                       uint64_t* used,
                                       double* utilization) {
    CHECK_NOTNULL(total);
    CHECK_NOTNULL(used);
    CHECK_NOTNULL(utilization);
    *total = meta_storage_path_capacity_metric_->MakeSnapshot();
    *used = meta_storage_path_used_metric_->MakeSnapshot();
    *utilization = meta_storage_path_utilization_metric_->MakeSnapshot();
  }

 private:
  void RunLoop();
  void GetProcessCpuUsage();
  void GetProcessMemoryUsage();
  void GetTotalMemory();
  void GetDisk();

  struct DiskStatSummary {
    std::vector<std::string> dirs;
    int32_t major_number;
    int32_t minor_number;
    int64_t reads;
    int64_t reads_merged;
    int64_t sectors_read;
    int64_t time_spent_reading_ms;
    int64_t writes;
    int64_t writes_merged;
    int64_t sectors_written;
    int64_t time_spent_writing_ms;
    int64_t io_in_progress;
    // currently only use this field to calculate io util
    int64_t time_spent_io_ms;
    int64_t weighted_time_spent_io_ms;
    int64_t current_time_ms;
    int64_t num_devices;

    std::string ToString() {
      std::string str = "(major_number=" + std::to_string(major_number) +
        ", minor_number=" + std::to_string(minor_number) +
        ", reads=" + std::to_string(reads) +
        ", reads_merged=" + std::to_string(reads_merged) +
        ", sectors_read=" + std::to_string(sectors_read) +
        ", time_spent_reading_ms=" + std::to_string(time_spent_reading_ms) +
        ", writes=" + std::to_string(writes) +
        ", writes_merged=" + std::to_string(writes_merged) +
        ", sectors_written=" + std::to_string(sectors_written) +
        ", time_spent_writing_ms=" + std::to_string(time_spent_writing_ms) +
        ", io_in_progress=" + std::to_string(io_in_progress) +
        ", time_spent_io_ms=" + std::to_string(time_spent_io_ms) +
        ", weighted_time_spent_io_ms=" +
        std::to_string(weighted_time_spent_io_ms) +
        ", current_time_ms=" + std::to_string(current_time_ms) +
        ", num_devices=" + std::to_string(num_devices) + ", dirs=[";
      for (auto& dir : dirs) {
        str += dir + ",";
      }
      if (str.back() == ',') {
        str.pop_back();
      }
      str += "])";
      return str;
    }
  };

  std::unordered_map<std::string, std::vector<std::string>> GetMTab();
  std::vector<DiskStatSummary> GatherDiskStat();

  int check_interval_ms_ {60000};

  std::shared_ptr<Metrics> metrics_;

  std::shared_ptr<Gauge> vcs_version_metric_;
  std::shared_ptr<Gauge> bvc_version_metric_;

  std::shared_ptr<Gauge> ram_total_metric_;
  std::shared_ptr<Gauge> proc_memory_total_metric_;
  std::shared_ptr<Gauge> proc_memory_rss_metric_;
  std::shared_ptr<Gauge> proc_cpu_usage_metric_;
  std::shared_ptr<Gauge> load_avg_1_metric_;
  std::shared_ptr<Gauge> load_avg_5_metric_;
  std::shared_ptr<Gauge> load_avg_15_metric_;

  std::shared_ptr<Gauge> meta_storage_path_capacity_metric_;
  std::shared_ptr<Gauge> meta_storage_path_used_metric_;
  std::shared_ptr<Gauge> meta_storage_path_utilization_metric_;
  std::shared_ptr<Gauge> meta_storage_ckpt_path_capacity_metric_;
  std::shared_ptr<Gauge> meta_storage_ckpt_path_used_metric_;
  std::shared_ptr<Gauge> meta_storage_ckpt_path_utilization_metric_;
  std::shared_ptr<Gauge> log_path_capacity_metric_;
  std::shared_ptr<Gauge> log_path_used_metric_;
  std::shared_ptr<Gauge> log_path_utilization_metric_;

  std::shared_ptr<Gauge> meta_storage_path_io_util_metric_;
  std::shared_ptr<Gauge> meta_storage_ckpt_path_io_util_metric_;
  std::unordered_map<std::string, std::vector<std::string>> device_to_dir_map_;
  std::unordered_map<std::string, DiskStatSummary> dir_to_last_stat_map_;
  std::unordered_map<std::string, std::shared_ptr<GenericHistogram<double>>>
    dir_to_histo_map_;

  uint64_t cpu_time_proc_ {0};
  uint64_t cpu_time_overall_ {0};

  std::unique_ptr<cnetpp::concurrency::Thread> runner_;
  mutable std::mutex mutex_;
  mutable std::condition_variable cv_;
  bool is_stopping_ {false};
};

}  // namespace dancenn

#endif  // BASE_RUNTIME_MONITOR_H_

