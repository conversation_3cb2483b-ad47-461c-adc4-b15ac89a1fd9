// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/retry_cache.h"

#include <absl/strings/str_format.h>
#include <gflags/gflags.h>
#include <google/protobuf/message.h>

#include <memory>

#include "base/closure.h"
#include "base/metric.h"
#include "base/metrics.h"
#include "base/stop_watch.h"
#include "rpc/rpc_controller.h"
#include "edit/log_rpc_info.h"
#include "service/service_util.h"

DECLARE_bool(retry_cache_enabled);
DECLARE_int32(retry_cache_expiration_time_ms);
DECLARE_int32(retry_cache_slice_count);
DECLARE_int32(retry_cache_size);
DECLARE_bool(dancenn_observe_mode_on);
DECLARE_bool(run_ut);

namespace dancenn {

void RetryCache::CacheEntry::Complete(const Status& st,
                                      PayloadTypePtr payload) {
  CHECK_NOTNULL(payload);
  {
    std::lock_guard<std::mutex> guard(mutex_);
    status_ = st;
    payload_ = std::move(payload);
    retry_cache_->IncPayloadByte(payload_->ByteSize());
    completed_ = true;
    if (VLOG_IS_ON(10)) {
      VLOG(10) << "RetryCacheEntry completed, " << ToStringInternal();
    }
  }
  // XXX out of lock protection, but it's ok since retry-request will no longer
  // queue up for completion of first-request from now on.

  // complete all retry-requests that waiting on this first-request
  // XXX special exceptions are not handled.
  while (!pending_retry_rpcs_.empty()) {
    PendingRetryRPC& retry_rpc = pending_retry_rpcs_.front();
    CompleteRetryRPC(retry_rpc.ctl_, retry_rpc.resp_, status_, payload_);
    retry_rpc.done_->Run();
    pending_retry_rpcs_.pop();
  }
}

bool RetryCache::CacheEntry::CheckCompletedOrHandOver(RpcController* rpc_ctl,
                                                      PayloadType* rpc_resp,
                                                      ClosureGuard* done_guard) {
  // fast check without lock
  if (IsCompleted()) {
    return true;
  }
  std::lock_guard<std::mutex> guard(mutex_);
  if (IsCompleted()) {
    return true;
  }
  // retry-request will queue up only when first-request not completed
  pending_retry_rpcs_.push(PendingRetryRPC(rpc_ctl, rpc_resp, done_guard->release()));
  return false;
}

bool RetryCache::CacheEntry::IsCompleted() {
  return completed_;
}

Status RetryCache::CacheEntry::GetStatus() {
  CHECK(completed_);
  return status_;
}

PayloadTypePtr RetryCache::CacheEntry::GetPayload() {
  return payload_;
}

std::string RetryCache::CacheEntry::ToString() {
  std::lock_guard<std::mutex> guard(mutex_);
  return ToStringInternal();
}

std::string RetryCache::CacheEntry::ToStringInternal() {
  return absl::StrFormat(
      "[RetryCacheEntry] client_id: %s, call_id: %u, completed: %s, "
      "status: %s, payload: %s, debug_info: %s", client_id_, call_id_,
      (completed_ ? "true" : "false"), status_.ToString(),
      (payload_ ? payload_->ShortDebugString() : "null"), debug_info_);
}

RetryCache::RetryCache()
    : slices_(FLAGS_retry_cache_slice_count) {
  for (auto& s : slices_) {
    s.slice.entries = std::make_unique<
        LRUCache<Key, std::shared_ptr<CacheEntry>, true, Key::Hasher>>(
        FLAGS_retry_cache_size);
  }
  InitMetrics();
}

RetryCache::~RetryCache() {
  FiniMetrics();
}

void RetryCache::InitMetrics() {
  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("RetryCache");
  hit_ = metrics_->RegisterCounter("Hit");
  miss_ = metrics_->RegisterCounter("Miss");
  added_ = metrics_->RegisterCounter("Added");
  removed_ = metrics_->RegisterCounter("Removed");
  expired_ = metrics_->RegisterCounter("Expired");
  gc_cost_ = metrics_->RegisterHistogram("GcCost");
  payload_byte_ = metrics_->RegisterCounter("PayloadByte");
  num_entries_ = metrics_->RegisterGauge("NumEntries", [this]() -> double {
    size_t res = 0;
    for (auto& s : slices_) {
      // no lock
      res += s.slice.entries->Size();
    }
    return static_cast<double>(res);
  });
}

void RetryCache::FiniMetrics() {
  metrics_->DeregisterGauge(num_entries_);
}

void RetryCache::IncPayloadByte(int64_t delta) {
  MFC(payload_byte_)->Inc(delta);
}

bool RetryCache::GetOrCreateCacheEntry(
    const std::string& client_id,
    uint32_t call_id,
    const std::string& debug_info,
    std::shared_ptr<RetryCache::CacheEntry>* centry) {
  // invalid cache entry key shall be checked outside,
  // those requests shall bypass retry-cache.
  CHECK(!client_id.empty());
  CHECK_GE(call_id, 0);

  auto k = Key(client_id, call_id);
  auto& s = slices_[Key::Hasher()(k) % slices_.size()].slice;
  std::shared_ptr<CacheEntry> cent;
  {
    std::lock_guard<std::mutex> guard(s.mutex);
    // GC before search, incase found deprecated cache entry
    Gc(s);

    bool got = s.entries->Get(k, &cent);
    if (got) {
      if (VLOG_IS_ON(10)) {
        VLOG(10) << absl::StrFormat(
            "retry cache HIT for client_id %s call_id %u.",
            client_id.c_str(), call_id);
      }
      MFC(hit_)->Inc();
      *centry = cent;
      return true;
    }

    if (VLOG_IS_ON(10)) {
      VLOG(10) << absl::StrFormat(
          "retry cache MISS for client_id %s call_id %u.",
          client_id.c_str(), call_id);
    }
    MFC(miss_)->Inc();
    auto et = std::chrono::steady_clock::now() +
              std::chrono::milliseconds(FLAGS_retry_cache_expiration_time_ms);
    *centry = std::make_shared<CacheEntry>(this, client_id, call_id, et, debug_info);
    s.entries->Set(k, *centry);
    MFC(added_)->Inc();
    return false;
  }
}

void RetryCache::RemoveCacheEntry(
    std::shared_ptr<RetryCache::CacheEntry>& centry) {
  auto k = Key(centry->client_id(), centry->call_id());
  auto& s = slices_[Key::Hasher()(k) % slices_.size()].slice;
  std::shared_ptr<CacheEntry> cent;
  {
    std::lock_guard<std::mutex> guard(s.mutex);
    Gc(s);

    bool got = s.entries->Get(k, &cent);
    if (got && cent == centry) {
      // evict the specified cache entry
      s.entries->Evict(k);
      MFC(removed_)->Inc();
      auto payload = centry->GetPayload();
      if (payload) {
        IncPayloadByte((int64_t)0 - payload->ByteSize());
      }
    }
  }
}

void RetryCache::AddCacheEntry(const std::string& client_id,
                               uint32_t call_id,
                               PayloadTypePtr payload,
                               const std::string& debug_info) {
  CHECK_NOTNULL(payload);
  std::shared_ptr<CacheEntry> centry;
  auto k = Key(client_id, call_id);
  auto& s = slices_[Key::Hasher()(k) % slices_.size()].slice;
  {
    std::lock_guard<std::mutex> guard(s.mutex);
    Gc(s);

    auto et = std::chrono::steady_clock::now() +
              std::chrono::milliseconds(FLAGS_retry_cache_expiration_time_ms);
    centry = std::make_shared<CacheEntry>(this, client_id, call_id, et, debug_info);
    s.entries->Set(k, centry);
    MFC(added_)->Inc();
  }
  // complete cache entry outside of lock, align to Active logic
  centry->Complete(Status::OK(), payload);
}

std::shared_ptr<RetryCache::CacheEntry> RetryCache::GetCacheEntry(
    const std::string& client_id,
    uint32_t call_id) {
  if (call_id < 0 || client_id.empty()) {
    return nullptr;
  }

  auto k = Key(client_id, call_id);
  auto& s = slices_[Key::Hasher()(k) % slices_.size()].slice;
  std::shared_ptr<CacheEntry> centry;
  {
    std::lock_guard<std::mutex> guard(s.mutex);
    if (!s.entries->Get(k, &centry)) {
      return nullptr;
    } else {
      return centry;
    }
  }
}

void RetryCache::Gc(Slice& s) {
  StopWatch sw(gc_cost_);
  sw.Start();

  int64_t num_expired = 0;
  int64_t payload_byte = 0;
  auto now = std::chrono::steady_clock::now();
  s.entries->REvictUntil(
      [&now, &num_expired, &payload_byte, this]
      (const Key& k, const std::shared_ptr<CacheEntry>& v) {
    (void)k;
    bool res = v->IsCompleted() && v->expiration_time() < now;
    if (res) {
      num_expired += 1;
      auto payload = v->GetPayload();
      if (payload) {
        payload_byte += payload->ByteSize();
      }
    }
    return res;
  });

  MFC(expired_)->Inc(num_expired);
  IncPayloadByte((int64_t)0 - payload_byte);
}

bool CheckRetryCacheEntry(
    RetryCache* retry_cache,
    RpcController* ctl,
    PayloadType* resp,
    ClosureGuard* done_guard,
    std::shared_ptr<RetryCache::CacheEntry>* centry_out) {
  CHECK_NOTNULL(centry_out);
  *centry_out = nullptr;
  auto rpc_info = ServiceUtil::GetLogRpcInfo(ctl);
  const std::string& client_id = rpc_info.rpc_client_id_;
  uint32_t call_id = rpc_info.rpc_call_id_;
  if (!retry_cache || !FLAGS_retry_cache_enabled ||
      client_id == INVALID_CLIENT_ID || call_id == INVALID_CALL_ID) {
    // out case 1: retry cache disabled
    return false;
  }

  std::string debug_info;
  if (!FLAGS_run_ut && VLOG_IS_ON(10)) {
    auto proxy_address = ctl->rpc_connection()
                            ->tcp_connection()
                            ->remote_end_point()
                            .address()
                            .ToString();
    auto method_name = ctl->request_header()->methodname();
    auto rpc_header = ctl->rpc_request_header()->ShortDebugString();
    auto req_header = ctl->request_header()->ShortDebugString();
    debug_info = absl::StrFormat(
        "Entry added by Active processing request, "
        "proxy_address: %s, method_name: %s, rpc_header: %s, req_header: %s.",
        proxy_address, method_name, rpc_header, req_header);
  }

  bool hit = false;
  std::shared_ptr<RetryCache::CacheEntry> centry;
  while (true) {
    hit = retry_cache->GetOrCreateCacheEntry(client_id,
                                             call_id,
                                             debug_info,
                                             &centry);
    if (!hit) {
      // out case 2: retry cache miss, created a un-completed cache entry.
      CHECK(!centry->IsCompleted());
      *centry_out = centry;
      break;
    }

    bool completed = centry->CheckCompletedOrHandOver(ctl, resp, done_guard);
    if (!completed) {
      // out case 3: retry cache hit, but not completed, the retry one is handed
      // over to completer of first-request.
      break;
    }

    auto st = centry->GetStatus();
    if (st.exception() != JavaExceptions::kSafeModeException &&
        st.exception() != JavaExceptions::kStandbyException &&
        st.exception() != JavaExceptions::kThrottlerException) {
      // out case 4: retry cache hit, return a completed cache entry.
      ctl->set_hit_retry_cache(true);
      CHECK(centry->IsCompleted());
      *centry_out = centry;
      break;
    }

    // XXX
    // Special cases that retry-requests shall be re-execute actually:
    // 1. StandbyException
    //      current NN may be promoted from standby to active.
    // 2. SafemodeException
    //      Client may expect the retry-request to get executed by re-sending
    //      request with same client-id and call-id specified manually.
    CHECK(st.exception() == JavaExceptions::kStandbyException ||
          st.exception() == JavaExceptions::kSafeModeException ||
          st.exception() == JavaExceptions::kThrottlerException);

    // by removing those cache entries and go next loop, the retry-requests
    // will be executed again like there's no former first-request.
    retry_cache->RemoveCacheEntry(centry);
    centry.reset();
  }

  return hit;
}

void CompleteRetryRPC(RpcController* ctl,
                      PayloadType* resp,
                      const Status& cached_status,
                      PayloadTypePtr cached_resp) {
  if (cached_status.HasException()) {
    ctl->MarkAsFailed(cached_status);
  } else {
    resp->CopyFrom(*cached_resp);
  }
}

}  // namespace dancenn
