// Copyright 2022 Xinlong Gao <<EMAIL>>


#ifndef BASE_DATABUS_SENDER_H_
#define BASE_DATABUS_SENDER_H_

#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>

#include "base/databus.h"
#include "cnetpp/concurrency/thread.h"
#include <cnetpp/concurrency/task.h>


namespace dancenn {

class DatabusSender {
 public:
  explicit DatabusSender(std::string&& log_name, const std::string& channel_name);
  virtual ~DatabusSender();

  DatabusSender(const DatabusSender&) = delete;
  DatabusSender& operator=(const DatabusSender&) = delete;

  void Push(std::string&& msg);

 private:
  std::shared_ptr<DatabusChannel> channel_;

  class Task : public cnetpp::concurrency::Task {
   public:
    explicit Task(DatabusSender* sender_) : sender_(sender_) {
    }

    void Stop() override;

    bool operator()(void* arg = nullptr) override;

   private:
    DatabusSender* sender_{nullptr};
  };

  friend class Task;

  std::string log_name_;
  std::mutex mu_;
  std::condition_variable cv_;
  std::vector<std::string> pending_logs_;
  std::unique_ptr<cnetpp::concurrency::Thread> worker_;
  std::shared_ptr<cnetpp::concurrency::Task> task_;
};

} // namespace dancenn

#endif // BASE_DATABUS_SENDER_H_

