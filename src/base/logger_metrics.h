// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/10/22
// Description

#ifndef BASE_LOGGER_METRICS_H_
#define BASE_LOGGER_METRICS_H_

#include "base/metric.h"
#include "base/metrics.h"

namespace dancenn {

class LoggerMetrics {
 public:
  static LoggerMetrics& Instance();
  static void Warn();
  static void Error();

 private:
  LoggerMetrics();
  LoggerMetrics(const LoggerMetrics& other) = delete;
  LoggerMetrics(LoggerMetrics&& other) = delete;
  LoggerMetrics& operator=(const LoggerMetrics& other) = delete;
  LoggerMetrics& operator=(LoggerMetrics&& other) = delete;

 public:
  ~LoggerMetrics() = default;

 public:
  MetricID warn_;
  MetricID error_;

  // Adding metrics below will NOT add corresponding alarm in Argos
  // automatically! Use warn_ or error_ if you can.
  MetricID compare_inode_failed_;
  MetricID compare_block_failed_;
  MetricID missing_block_error_;
  MetricID try_to_delete_not_deprecated_block_error_;
  MetricID persist_block_fail_error_;
  MetricID unlock_block_info_in_transaction_lock_failed_;
};

}  // namespace dancenn

#endif  // BASE_LOGGER_METRICS_H_
