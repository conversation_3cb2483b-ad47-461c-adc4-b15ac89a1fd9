// Copyright 2017 Liyuan Lei <<EMAIL>>

#include "base/rack_aware.h"

#include <sys/socket.h>

#include <cstdio>
#include <cstdlib>
#include <string>
#include <vector>
#include <stack>

#include <glog/logging.h>

#include "base/data_center_table.h"
#include "base/file_utils.h"
#include "base/stop_watch.h"

DECLARE_int32(client_normal_rpc_handler_count);
DECLARE_int32(client_slow_rpc_handler_count);
DECLARE_bool(rack_aware);
DECLARE_string(network_location_file);
DECLARE_uint32(rack_mask);
DECLARE_bool(enable_location_tag);

namespace dancenn {

bool ResolveRack(const std::vector<uint8_t>& addr, NetworkLocation* location) {
  CHECK_EQ(addr.size(), 4);
  location->rack.resize(7);
  int len = std::snprintf(&location->rack[0], location->rack.size(),
                          "%02x%02x%02x", addr[1], addr[2], addr[3] / 64);
  if (len < 0) {
    location->rack = "UNK";
    return false;
  } else {
    location->rack.pop_back();
  }
  if (addr[1] == 4) {
    location->dc = "HY";
  } else if (addr[1] == 100) {
    location->dc = "AWSVA";
  } else if (addr[1] == 110 || (addr[1] == 188 && addr[2] >= 128)) {
    location->dc = "ALIVA";
  } else if (addr[1] == 115) {
    location->dc = "ALISG";
  } else if (addr[1] == 101) {
    location->dc = "AWSSG";
  } else if (addr[1] >= 31 && addr[1] <= 40) {
    location->dc = "WJ";
  } else if ((addr[1] >= 20 && addr[1] < 100) || addr[1] == 144 ||
             addr[1] == 145) {
    location->dc = "HL";
  } else if (addr[1] < 20) {
    location->dc = "LF";
  } else if (addr[1] >= 128 && addr[1] <= 131) {
    location->dc = "LQ";
  } else if (addr[1] == 225) {
    if (addr[2] >= 128 && addr[2] <= 159) {
      location->dc = "BOE";
    } else {
      location->dc = "COF";
    }
  } else {
    location->dc = "UNKNOWN";
  }
  return true;
}

NetworkLocation LegacyResolveNetworkLocation(const std::string& host) {
  NetworkLocation location;
  if (host.find("n6-19") != std::string::npos && host[5] != '-') {
    location.rack = "TMP2";
  }
  location.rack.resize(7);
  std::vector<uint8_t> addr;
  addr.resize(4);
  if (host[0] == 'n') {
    addr[0] = 10;
    size_t start = 1;
    for (uint32_t i = 1; i < 4; ++i) {
      size_t pos = host.find('-', start);
      addr[i] = std::atoi(host.substr(start, pos - 1).c_str());
      start = pos + 1;
    }
  } else {
    location.rack = "UNK";
    return location;
  }
  ResolveRack(addr, &location);
  return location;
}

NetworkLocation LegacyResolveNetworkLocation(
    const cnetpp::base::IPAddress& ip) {
  NetworkLocation location;
  ResolveRack(ip.address(), &location);
  return location;
}

NetworkLocation ResolveNetworkLocation(const std::string& host) {
  if (FLAGS_enable_location_tag) {
    VLOG(10) << "Enable DN Location Tag, so this function should not be called";
  }
  return ConfigBasedRackAware::GetSingleton().ResolveNetworkLocation(host);
}

NetworkLocation ResolveNetworkLocation(const cnetpp::base::IPAddress& ip) {
  if (FLAGS_enable_location_tag) {
    VLOG(10) << "Enable DN Location Tag, so this function should not be called";
  }
  return ConfigBasedRackAware::GetSingleton().ResolveNetworkLocation(ip);
}

bool ReloadNetworkLocationConfig(const std::string& config_filepath) {
  std::string config_string = FileUtils::TryReadConfig(config_filepath);

  if (config_string.empty()) {
    return false;
  }

  if (config_string[0] == '{') {
    // JSON version
    return ConfigBasedRackAware::GetSingleton().ParseJsonAndUpdate(config_string);
  }

  return ConfigBasedRackAware::GetSingleton().ParseAndUpdate(config_string);
}

cnetpp::base::IPAddress Host2IpAddress(const std::string& host) {
  if (host.empty()) {
    LOG(ERROR) << "Can't Resolve Empty Host host=" << host;
    return cnetpp::base::IPAddress();
  }
  if (host[0] != 'n') {
    cnetpp::base::IPAddress ip;
    if (cnetpp::base::IPAddress::LiteralToNumber(host, &ip)) {
      return ip;
    }
    LOG(ERROR) << "Can't Resolve Host host=" << host;
    return cnetpp::base::IPAddress();
  }

  std::vector<uint8_t> addr;
  addr.resize(4);
  addr[0] = 10;

  size_t start = 1;
  for (uint32_t i = 1; i < 4; ++i) {
    size_t pos = host.find('-', start);
    auto fragment = host.substr(start, pos - 1);
    addr[i] = static_cast<uint8_t>(std::atoi(fragment.c_str()));
    start = pos + 1;
  }

  cnetpp::base::IPAddress ip;
  ip.mutable_address() = std::move(addr);
  return ip;
}

ConfigBasedRackAware::ConfigBasedRackAware()
  : rwlock_(FLAGS_client_normal_rpc_handler_count + 
        FLAGS_client_slow_rpc_handler_count + 1) {

  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("GlobalConfig");

  rack_aware_resolve_host_time_ = metrics->RegisterHistogram(
      "RackAwareResolveTime#type=host");
  rack_aware_resolve_ip_time_ = metrics->RegisterHistogram(
      "RackAwareResolveTime#type=ip");

  refresher_ = std::make_unique<Refresher>("rack-aware-refresher",
                                           [&]() { ReloadNetworkLocationConfig(FLAGS_network_location_file); });
  refresher_->Start();

}

ConfigBasedRackAware::~ConfigBasedRackAware() {
  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

ConfigBasedRackAware& ConfigBasedRackAware::GetSingleton() {
  static ConfigBasedRackAware rackAware;
  return rackAware;
}

std::ostream& operator<<(std::ostream& out, const DcIntervalEntry& entry) {
  return out << entry.ipv4_start << " ~ " << entry.ipv4_end << " = "
             << entry.number_start << " ~ " << entry.number_end << " "
             << entry.dc;
}

void Ip2FullString(cnetpp::base::IPAddress ip, std::string &str) {
  str.resize(ip.Size() * 2 + 1);
  for (int i = 0; i < ip.Size(); i++) {
    int x = (uint8_t)ip.address()[i];
    std::snprintf(&str[i * 2], 3, "%02X", x);
  }
}

Subnet::Subnet(cnetpp::base::IPAddress start, int m, std::string d)
    : start_ip(std::move(start)), mask(m), dc(std::move(d)) {
  end_ip = start_ip;
  if (mask > 0 && mask <= start_ip.address().size() * 8) {
    int addr = mask / 8;
    int res = mask % 8;
    end_ip.mutable_address()[addr] |= (1 << (8 - res)) - 1;
    for (; addr > 0; addr--) {
      if (end_ip.mutable_address()[addr] == 255) {
        end_ip.mutable_address()[addr] = 0;
      } else {
        end_ip.mutable_address()[addr] += 1;
        break;
      }
    }
  }
  Ip2FullString(start_ip, start_ip_str);
  Ip2FullString(end_ip, end_ip_str);
}

Subnet::Subnet(cnetpp::base::IPAddress start, cnetpp::base::IPAddress end, std::string d)
    : start_ip(std::move(start)), end_ip(std::move(end)), dc(std::move(d)) {
  for(int addr = start_ip.address().size() - 1; addr >= 0; addr--){
    if (end_ip.mutable_address()[addr] == 255) {
      end_ip.mutable_address()[addr] = 0;
    } else {
      end_ip.mutable_address()[addr] += 1;
      break;
    }
  }
  Ip2FullString(start_ip, start_ip_str);
  Ip2FullString(end_ip, end_ip_str);
}

std::string Subnet::ToJson() const {
  std::ostringstream oss;
  oss << "{"
      << "\"subnet\": \"" << start_ip.ToString() << "/" << mask << "\","
      << "\"interval\": \"[ " << start_ip.ToString() << " , " << end_ip.ToString()
      << " )\","
      << "\"dc\": \"" << dc << "\""
      << "}";
  return oss.str();
}

bool Subnet::Contains(
    const cnetpp::base::IPAddress& other) {
  if (start_ip.address().size() != other.address().size()) {
    VLOG(9) << start_ip.address().size() << " " << other.address().size();
    return false;
  }
  std::string ip_str;
  Ip2FullString(other, ip_str);
  return (ip_str >= start_ip_str) && (ip_str < end_ip_str);
}

void Subnet::UpdateStartIp(const cnetpp::base::IPAddress &other) {
  if (!Contains(other)) return;
  start_ip = std::move(other);
  Ip2FullString(start_ip, start_ip_str);
}

void Subnet::UpdateEndIp(const cnetpp::base::IPAddress &other) {
  if (!Contains(other)) return;
  end_ip = std::move(other);
  Ip2FullString(end_ip, end_ip_str);
}

bool operator>(const Subnet& a, const Subnet& b) {
  return a.start_ip_str > b.start_ip_str || (a.start_ip_str == b.start_ip_str && a.end_ip_str < b.end_ip_str);
}

bool operator<(const Subnet& a, const Subnet& b) {
  return a.start_ip_str < b.start_ip_str || (a.start_ip_str == b.start_ip_str && a.end_ip_str > b.end_ip_str);
}

bool operator==(const Subnet& a, const Subnet& b) {
  return a.start_ip_str == b.start_ip_str && a.end_ip_str == b.end_ip_str;
}

std::ostream& operator<<(std::ostream& out, const Subnet& subnet) {
  return out << subnet.start_ip.ToString() << "/" << subnet.mask
             << " = " << "[ "
             << subnet.start_ip.ToString() << " , "
             << subnet.end_ip.ToString() << " ) "
             << subnet.dc ;
}

std::vector<std::string> ConfigBasedRackAware::ListSubnet() {
  vshared_lock guard(rwlock_.lock());

  std::vector<std::string> result;
  result.reserve(ipv6_subnet_table_.size() + ipv4_subnet_table_.size());

  for (auto& entry : ipv4_subnet_table_) {
    result.push_back(entry.second.ToJson());
  }
  for (auto& entry : ipv6_subnet_table_) {
    result.push_back(entry.second.ToJson());
  }

  return result;
}

bool ConfigBasedRackAware::CheckConfigVersionStale(const std::string& config_string) {
  if (config_string == config_string_) {
    return false;
  }

  config_string_ = config_string;
  version_.fetch_add(1);
  LOG(INFO) << "New Rack Aware config_string: \n" << config_string;

  return true;
}

bool ConfigBasedRackAware::ParseJsonAndUpdate(const std::string& config_string) {
  vunique_lock guard(rwlock_.lock());
  if (!CheckConfigVersionStale(config_string)) {
    return true;
  }

  // Deserialize
  std::vector<Subnet> new_ipv4_subnet_list;
  std::vector<Subnet> new_ipv6_subnet_list;

  cnetpp::base::Value received_json;
  if (!cnetpp::base::Parser::Deserialize(config_string, &received_json) ||
      !received_json.IsObject()) {
    LOG(ERROR) << "Failed to deserialize config_string.";
    return false;
  }
  auto config_obj = received_json.AsObject();

  auto dc_config = config_obj.Find("dc_config");
  if (dc_config == config_obj.End() || !dc_config->second.IsArray()) {
    LOG(ERROR) << "dc_config field is not json array.";
    return false;
  }
  auto subnet_list = dc_config->second.AsArray();

  for (auto iter = subnet_list.Begin(); iter != subnet_list.End(); ++iter) {
    auto subnet_arr = iter->AsArray();
    if (subnet_arr.Size() != 3) {
      LOG(ERROR) << "subnet_arr size not 3. " << subnet_arr.Size();
      return false;
    }
    if (!(subnet_arr[0].IsString() && subnet_arr[1].IsString() && subnet_arr[2].IsString())) {
      LOG(ERROR) << "subnet_arr elements not string. " << subnet_arr[0].AsString() << " _ " << subnet_arr[1].AsString() << " _ " << subnet_arr[2].AsString();
      return false;
    }

    cnetpp::base::IPAddress start_ip(subnet_arr[0].AsString());
    cnetpp::base::IPAddress end_ip(subnet_arr[1].AsString());
    if (end_ip.address().empty() || start_ip.address().empty() || start_ip.Family() != end_ip.Family()) {
      return false;
    }
    auto dc = subnet_arr[2].AsString();

    if (start_ip.Family() == AF_INET) {
      new_ipv4_subnet_list.emplace_back(std::move(start_ip), std::move(end_ip), std::move(dc));
      LOG(INFO) << "IPV4 subnet entry: " << new_ipv4_subnet_list.back();
    } else if (start_ip.Family() == AF_INET6) {
      new_ipv4_subnet_list.emplace_back(std::move(start_ip), std::move(end_ip), std::move(dc));
      LOG(INFO) << "IPV6 subnet entry: " << new_ipv6_subnet_list.back();
    } else {
      LOG(ERROR) << "IP subnet json error, will not update network config: "
                 << start_ip.ToString() << ", " << end_ip.ToString();
      return false;
    }
  }

  UpdateConfig(ipv4_subnet_table_, new_ipv4_subnet_list);
  UpdateConfig(ipv6_subnet_table_, new_ipv6_subnet_list);

  LOG(INFO) << "Successfully update ip table by json config:"
            << " ipv4 size: " << ipv4_subnet_table_.size()
            << " ipv6 size: " << ipv6_subnet_table_.size();

  return true;
}

bool ConfigBasedRackAware::ParseAndUpdate(const std::string &config_string) {
  vunique_lock guard(rwlock_.lock());
  if (!CheckConfigVersionStale(config_string)) {
    return true;
  }

  // Deserialize
  std::vector<Subnet> new_ipv4_subnet_list;
  std::vector<Subnet> new_ipv6_subnet_list;

  auto data = cnetpp::base::StringUtils::SplitByChars(config_string_, "\n");
  LOG(INFO) << "New Rack Aware config_string len: \n" << data.size();
  for (auto& line : data) {
    LOG(INFO) << "line=" << line;
    if (line.empty() || line[0] == '#') {
      continue;
    }

    // erase \r,\0 .etc
    while (!line.empty()
           // DC names must end with an upper case char or number
           && (line.back() < '0' || line.back() > '9') &&
           (line.back() < 'A' || line.back() > 'Z') &&
           (line.back() < 'a' || line.back() > 'z')) {
      line.erase(line.end() - 1);
    }
    LOG(INFO) << "line=" << line;

    // "**********/17 LF" -> ["**********", "17 LF"]
    auto subnet_maskdc = cnetpp::base::StringUtils::SplitByChars(line, "/");
    if (subnet_maskdc.size() == 2) {
      // ["17","LF"]
      auto mask_dc =
          cnetpp::base::StringUtils::SplitByChars(subnet_maskdc[1], " ");
      LOG(INFO) << "mask_dc.size()=" << mask_dc.size();
      if (mask_dc.size() != 2) {
        return false;
      }

      cnetpp::base::IPAddress ip(subnet_maskdc[0]);
      LOG(INFO) << "ip=" << ip.ToString();
      if (ip.address().size() == 0) {
        return false;
      }

      char* pos = nullptr;
      auto mask =
          static_cast<uint16_t>(::strtoull(mask_dc[0].c_str(), &pos, 10));
      if (pos == mask_dc[0].data()) {
        return false;
      }

      if (ip.Family() == AF_INET && mask <= 32) {
        new_ipv4_subnet_list.emplace_back(
            std::move(ip), mask, std::move(mask_dc[1]));
        LOG(INFO) << "IPV4 subnet entry: " << new_ipv4_subnet_list.back();
      } else if (ip.Family() == AF_INET6 && mask <= 128) {
        new_ipv6_subnet_list.emplace_back(
            std::move(ip), mask, std::move(mask_dc[1]));
        LOG(INFO) << "IPV6 subnet entry: " << new_ipv6_subnet_list.back();
      } else {
        LOG(ERROR) << "IP subnet error, will not update network config: "
                   << ip.ToString() << "/" << mask
                   << " ip.Family: " << ip.Family();
        return false;
      }
    } else if (subnet_maskdc.size() == 1) {
      // "********** ************ LF" -> ["**********", "************", "LF"]
      auto tokens =
          cnetpp::base::StringUtils::SplitByChars(subnet_maskdc[0], " ");
      if (tokens.size() != 3) {
        LOG(INFO) << "Tokens number error. line=" << line;
        return false;
      }

      cnetpp::base::IPAddress start_ip(tokens[0]);
      cnetpp::base::IPAddress end_ip(tokens[1]);
      if (start_ip.address().size() == 0 || end_ip.address().size() == 0) {
        LOG(INFO) << "IP can not parsed. line=" << line;
        return false;
      }

      if (start_ip.Family() != end_ip.Family()) {
        LOG(INFO) << "IP Family not match. line=" << line;
        return false;
      }

      if (start_ip.Family() == AF_INET) {
        new_ipv4_subnet_list.emplace_back(
            std::move(start_ip), std::move(end_ip), std::move(tokens[2]));
        LOG(INFO) << "IPV4 subnet entry: " << new_ipv4_subnet_list.back();
      } else if (start_ip.Family() == AF_INET6) {
        new_ipv6_subnet_list.emplace_back(
            std::move(start_ip), std::move(end_ip), std::move(tokens[2]));
        LOG(INFO) << "IPV6 subnet entry: " << new_ipv6_subnet_list.back();
      } else {
        LOG(ERROR) << "IP subnet error, will not update network config. "
                   << " start_ip.Family: " << start_ip.Family()
                   << " line=" << line;
        return false;
      }
    } else {
      return false;
    }
  }

  UpdateConfig(ipv4_subnet_table_, new_ipv4_subnet_list);
  UpdateConfig(ipv6_subnet_table_, new_ipv6_subnet_list);

  LOG(INFO) << "Successfully update ip table:"
            << " ipv4 size: " << ipv4_subnet_table_.size()
            << " ipv6 size: " << ipv6_subnet_table_.size();

  return true;
}

bool ConfigBasedRackAware::UpdateConfig(std::map<std::string , Subnet> &src_table, std::vector<Subnet> &new_list) {
  sort(new_list.begin(), new_list.end(), std::less<Subnet>());

  std::stack<Subnet> stack;
  std::map<std::string , Subnet> new_table;

  for (int index = 0; index < new_list.size(); ) {
    auto& subnet = new_list[index];

    if (stack.empty()) {
      stack.push(subnet);
      index++;
    } else {
      Subnet& pre_subnet = stack.top();
      if (pre_subnet.end_ip_str <= subnet.start_ip_str) {
        // [pre], [cur], then insert [pre]
        new_table.insert(std::make_pair(pre_subnet.start_ip_str, pre_subnet));
        stack.pop();

        // do not switch to next subnet, continue comparing stack.top() with current subnet
        continue;
      }

      index++;

      if (pre_subnet.start_ip_str <= subnet.start_ip_str) {
        if (pre_subnet.end_ip_str < subnet.start_ip_str) {
          LOG(WARNING) << "Overlap subnets! Skipping current subnet: " << subnet.ToJson() << "\n"
                       << "Previous Subnet is: " << pre_subnet.ToJson();
          continue;
        }

        // [pre.start...[cur.start, cur.end]...pre.end]
        if (pre_subnet.start_ip_str < subnet.start_ip_str) {
          // insert [pre.start, cur.start]
          Subnet subnet1(pre_subnet.start_ip, pre_subnet.mask, pre_subnet.dc);
          subnet1.UpdateEndIp(subnet.start_ip);
          new_table.insert(std::make_pair(subnet1.start_ip_str, subnet1));
        }

        if (subnet.end_ip_str == pre_subnet.end_ip_str) {
          stack.pop();
        } else {
          // Stack: pre.start = cur.end
          pre_subnet.UpdateStartIp(subnet.end_ip);
        }

        // push cur into stack
        // Stack(from top -> bottom): {[cur.start, cur.end], [cur.end, pre.end]....}
        stack.push(subnet);
      }
    }
  }

  while(!stack.empty()) {
    new_table.insert(std::make_pair(stack.top().start_ip_str, stack.top()));
    stack.pop();
  }

  src_table.swap(new_table);

  return true;
}

NetworkLocation ConfigBasedRackAware::ResolveNetworkLocation(
    const std::string& host) {
  StopWatch sw(rack_aware_resolve_host_time_);
  sw.Start();

  auto ip = Host2IpAddress(host);
  return ResolveNetworkLocation(ip);
}

NetworkLocation ConfigBasedRackAware::ResolveNetworkLocation(
    const cnetpp::base::IPAddress& ip) {
  vshared_lock guard(rwlock_.lock());

  StopWatch sw(rack_aware_resolve_ip_time_);
  sw.Start();

  NetworkLocation location;

  if (ip.Family() == AF_INET) {
    if (ipv4_subnet_table_.empty()) {
      DLOG(INFO) << "IPV4 Subnet table empty, LegacyResolveNetworkLocation. ip=" << ip.ToString();
      location = LegacyResolveNetworkLocation(ip);
    } else {
      if (!ResolveRackV4(ip, &location)) {
        DLOG(INFO) << "Not belong to any Rack. ip=" << ip.ToString();
        location.rack = "UNK";
      }

      if (!ResolveDC(ip, &location, ipv4_subnet_table_)) {
        DLOG(INFO) << "Not belong to any DC. ip=" << ip.ToString();
        location.dc = default_dc_;
      }
    }
  } else if (ip.Family() == AF_INET6) {
    if (!ResolveRackV6(ip, &location)) {
      DLOG(INFO) << "Not belong to any Rack. ip=" << ip.ToString();
      location.rack = "UNK";
    }

    if (!ResolveDC(ip, &location, ipv6_subnet_table_)) {
      DLOG(INFO) << "Not belong to any DC. ip=" << ip.ToString();
      location.dc = default_dc_;
    }
  } else {
    DLOG(INFO) << "Invalid Address";
    location.rack = "UNK";
    location.dc = default_dc_;
  }

  location.dc_id = GetGlobalDataCenterTable().ID(location.dc);

  return location;
}

bool ConfigBasedRackAware::ResolveRackV4(const cnetpp::base::IPAddress& ip,
                                       NetworkLocation* location) {
  const auto& addr = ip.address();

  if (FLAGS_rack_aware) {
    uint32_t mask = ~((1 << FLAGS_rack_mask) - 1);
    uint32_t segment = ((1 << 8) - 1);
    uint32_t m1 = (mask >> 16) & segment;
    uint32_t m2 = (mask >> 8) & segment;
    uint32_t m3 = mask & segment;
    location->rack.resize(7);
    int len = std::snprintf(&location->rack[0], location->rack.size(),
                            "%02x%02x%02x", addr[1] & m1, addr[2] & m2,
                            addr[3] & m3);
    if (len < 0) {
      return false;
    }

    location->rack.pop_back();
  } else {
    location->rack = "000000";
  }

  return true;
}

bool ConfigBasedRackAware::ResolveRackV6(const cnetpp::base::IPAddress& ip, NetworkLocation* location){
  const auto &addr = ip.address();

  if (FLAGS_rack_aware) {
    // https://bytedance.feishu.cn/docs/jnysBhuHOfeDFKqjjlHmye
    location->rack.resize(9);
    int buz = addr[2 * 2 + 0] & 0x01u;  // online/offline
    int pod = addr[2 * 2 + 1];
    int tor_h = addr[2 * 3 + 0] & 0x0fu;
    int tor_l = addr[2 * 3 + 1];
    int len = std::snprintf(&location->rack[0], location->rack.size(),
                            "v6%01x%02x%01x%02x", buz, pod, tor_h, tor_l);
    if (len < 0) {
      return false;
    }

    location->rack.pop_back();
  } else {
    location->rack = "000000";
  }

  return true;
}

bool ConfigBasedRackAware::ResolveDC(const cnetpp::base::IPAddress& ip, NetworkLocation* location,
                                     std::map<std::string, Subnet>& ip_table){
  // get first interval *bigger* than target ip
  std::string ip_str;
  Ip2FullString(ip, ip_str);
  auto iter = ip_table.upper_bound(ip_str);
  if (iter == ip_table.begin()) {
    return false;
  }
  iter--;

  auto& entry = iter->second;
  if (entry.Contains(ip)) {
    location->dc = entry.dc;
    return true;
  }

  return false;
}

}  // namespace dancenn
