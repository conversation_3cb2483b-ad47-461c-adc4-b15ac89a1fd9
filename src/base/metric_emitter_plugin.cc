// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "base/metric_emitter_plugin.h"

#include <glog/logging.h>

#include <memory>

#include "base/script_metric_emitter_plugin.h"
#include "base/http_metric_emitter_plugin.h"

namespace dancenn {

std::unique_ptr<MetricEmitterPlugin> MetricEmitterPlugin::CreateMetricEmitter(
    const std::string& type) {
  if (type.empty()) {
    LOG(WARNING) << "Creating dummy metric emitter plugin.";
    return std::unique_ptr<MetricEmitterPlugin>(new DummyMetricEmitterPlugin);
  }

  if (type == "script") {
    LOG(INFO) << "Creating script based metric emitter plugin.";
    return std::unique_ptr<MetricEmitterPlugin>(new ScriptMetricEmitterPlugin);
  } else if (type == "http") {
    LOG(INFO) << "Creating http based metric emitter plugin.";
    return std::unique_ptr<MetricEmitterPlugin>(new HttpMetricEmitterPlugin);
  }
  LOG(ERROR) << "Unknown metric emitter plugin type: " << type;
  return nullptr;
}

}  // namespace dancenn
