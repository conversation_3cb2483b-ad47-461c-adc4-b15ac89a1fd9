#include "throttler.h"

#include "base/constants.h"
#include "base/time_util.h"
#include "cnetpp/concurrency/this_thread.h"

namespace dancenn {

bool BaseThrottler::Acquire(uint32_t max_sleep_ms) {
  uint32_t sleep_time = 10;
  uint32_t remain_sleep_time = max_sleep_ms;
  do {
    if (LIKELY(TryAcquire())) {
      return true;
    }
    if (LIKELY(remain_sleep_time > 0)) {
      std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
      if (LIKELY(remain_sleep_time > sleep_time)) {
        remain_sleep_time -= sleep_time;
      } else {
        remain_sleep_time = 0;
      }
    }
  } while (remain_sleep_time > 0);
  return false;
}

bool BaseThrottler::TryAcquire() {
  auto now = TimeUtil::GetNowEpochMs();
  auto q = qps();

  if (q == 0) {
    return true;
  }

  std::lock_guard<std::mutex> guard(mutex_);

  if (last_update_timestamp_ == 0) {
    last_update_timestamp_ = now;
  }

  const uint64_t TIME_WINDOW = 100;
  const uint64_t TIME_WINDOW_CNT = 5;
  auto time_pass = now - last_update_timestamp_;
  if (UNLIKELY(used_quota_ >= q || time_pass >= TIME_WINDOW)) {
    auto aligned_time_pass = (time_pass / TIME_WINDOW) * TIME_WINDOW;
    auto calc_time_pass =
        std::min(aligned_time_pass, TIME_WINDOW_CNT * TIME_WINDOW);
    auto release_quota = static_cast<uint32_t>(calc_time_pass * qps() / 1000);
    VLOG(2) << "qps=" << q << " used_quota=" << used_quota_ << " now=" << now
            << " last=" << last_update_timestamp_ << " time_pass=" << time_pass
            << " aligned_time_pass=" << aligned_time_pass
            << " calc_time_pass=" << calc_time_pass
            << " release_quota=" << release_quota;
    if (release_quota != 0) {
      last_update_timestamp_ += aligned_time_pass;
      if (release_quota >= used_quota_) {
        used_quota_ = 0;
      } else {
        used_quota_ -= release_quota;
      }
    }
  }

  if (LIKELY(used_quota_ < q)) {
    used_quota_++;
    return true;
  } else {
    return false;
  }
}

bool ShardThrottler::Acquire(uint32_t max_sleep_ms) {
  uint32_t tid = cnetpp::concurrency::ThisThread::GetId();
  return throttlers_[tid % shard_]->Acquire(max_sleep_ms);
}

bool ShardThrottler::TryAcquire() {
  uint32_t tid = cnetpp::concurrency::ThisThread::GetId();
  return throttlers_[tid % shard_]->TryAcquire();
}

}  // namespace dancenn