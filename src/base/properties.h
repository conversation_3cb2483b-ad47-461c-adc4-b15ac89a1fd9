// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_PROPERTIES_H_
#define BASE_PROPERTIES_H_

#include <cnetpp/base/string_piece.h>
#include <cnetpp/concurrency/this_thread.h>
#include <glog/logging.h>

#include <string>
#include <map>
#include <limits>


namespace dancenn {

class Properties {
 public:
  bool Load(const std::string& file_name);

  bool GetBool(const std::string& key, bool* res, bool def = false);

  template <typename T>
  bool GetInt(
    const std::string& key,
    typename std::enable_if<std::is_integral<T>::value, T>::type* res,
    T def = T(0)) {
    CHECK_NOTNULL(res);

    auto itr = kvs_.find(key);
    if (itr == kvs_.end()) {
      *res = def;
      return false;
    }
    char *end = nullptr;
    auto s = std::strtoll(itr->second.c_str(), &end, 10);
    if (cnetpp::concurrency::ThisThread::GetLastError() == ERANGE ||
        end != itr->second.c_str() + itr->second.length() ||
        s > std::numeric_limits<T>::max() ||
        s < std::numeric_limits<T>::min()) {
      *res = def;
      return false;
    }
    *res = static_cast<T>(s);
    return true;
  }

  bool GetDouble(const std::string& key, double* res, double def = 0.);
  bool GetString(const std::string& key, std::string* res,
      const std::string& def = std::string());

  // Notice: "hdfs namenode -initializeSharedEdits" will get infos from VERSION
  // file, and I don't want to modify legacy hdfs code.
  // So I modify VERSION file, and user should execute "fsimage_transfer" before
  // "hdfs namenode -initializeSharedEdits".
  void Set(const std::string& key, const std::string& value);
  void Store(const std::string& file_name);

  size_t size() const {
    return kvs_.size();
  }

 private:
  bool LoadOneLine(cnetpp::base::StringPiece line);

  bool Convert(cnetpp::base::StringPiece data, std::string* res);

  std::map<std::string, std::string> kvs_;
};

}  // namespace dancenn

#endif  // BASE_PROPERTIES_H_

