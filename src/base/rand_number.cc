#include <glog/logging.h>
#include "base/rand_number.h"

namespace dancenn {

RandNumber* RandNumber::rand_ = nullptr;
std::once_flag RandNumber::flag_ {};

bool RandNumber::NextBool() {
  cnetpp::concurrency::SpinLock::ScopeGuard guard(spinlock_);
  uint32_t v = uni_dist_(rand_gen_);
  return ((v & 0x01) == 0);
}

uint32_t RandNumber::NextInt32(uint32_t left, uint32_t right) {
  CHECK(left <= right);
  cnetpp::concurrency::SpinLock::ScopeGuard guard(spinlock_);
  uint32_t v = uni_dist_(rand_gen_);
  return v % (right - left + 1) + left;
}

uint32_t RandNumber::NextInt32() {
  cnetpp::concurrency::SpinLock::ScopeGuard guard(spinlock_);
  return uni_dist_(rand_gen_);
}

double RandNumber::NextDouble() {
  cnetpp::concurrency::SpinLock::ScopeGuard guard(spinlock_);
  return static_cast<double>(uni_dist_(rand_gen_)) / rand_gen_.max();
}

RandNumber* SingleRandGenerator() {
  return RandNumber::Instance();
}

}  // namespace dancenn

