// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include "base/token_bucket.h"

#include <glog/logging.h>

namespace dancenn {

TokenBucket::TokenBucket(double token_generate_rate, double burst)
    : bucket_empty_time_(0),
      token_generate_rate_(token_generate_rate),
      burst_(burst) {
  CHECK_GT(token_generate_rate_, 0.);
  CHECK_GT(burst_, 0.);
}

bool TokenBucket::Consume(double count) {
  auto empty_time_old = bucket_empty_time_.load();
  double empty_time_new;
  do {
    auto now = std::chrono::duration_cast<std::chrono::duration<double>>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    auto tokens =
        std::min((now - empty_time_old) * token_generate_rate_, burst_);
    if (tokens < count) {
      return false;
    }
    tokens -= count;
    empty_time_new = now - tokens / token_generate_rate_;
  } while (!bucket_empty_time_.compare_exchange_weak(
      empty_time_old, empty_time_new));
  return true;
}

double TokenBucket::ConsumePartial(double count) {
  double res = 0.;
  auto empty_time_old = bucket_empty_time_.load();
  double empty_time_new;
  do {
    auto now = std::chrono::duration_cast<std::chrono::duration<double>>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    auto tokens =
        std::min((now - empty_time_old) * token_generate_rate_, burst_);
    if (tokens < count) {
      res = tokens;
      tokens = 0.;
    } else {
      res = count;
      tokens -= count;
    }
    empty_time_new = now - tokens / token_generate_rate_;
  } while (!bucket_empty_time_.compare_exchange_weak(
      empty_time_old, empty_time_new));

  return res;
}

}  // namespace dancenn

