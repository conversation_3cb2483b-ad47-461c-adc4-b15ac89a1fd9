// Copyright 2021 xiongmu <<EMAIL>>

#ifndef BASE_THROTTLER_H_
#define BASE_THROTTLER_H_

#include <glog/logging.h>

#include <atomic>
#include <climits>
#include <cstdint>
#include <functional>
#include <mutex>
#include <thread>

namespace dancenn {

class Throttler {
 public:
  explicit Throttler(uint32_t qps) {
    func_ = [&]() { return qps; };
  }
  explicit Throttler(std::function<uint32_t()> func) : func_(std::move(func)) {}
  ~Throttler() = default;

  Throttler(const Throttler& other) = delete;
  Throttler& operator=(const Throttler& other) = delete;

  uint32_t qps() { return func_(); }

  virtual bool Acquire(uint32_t max_sleep_ms = UINT_MAX) = 0;

  virtual bool TryAcquire() = 0;

 protected:
  std::function<uint32_t()> func_;
};

class BaseThrottler : public Throttler {
 public:
  explicit BaseThrottler(std::function<uint32_t()> func)
      : Throttler(std::move(func)) {
    used_quota_ = qps();
  }
  ~BaseThrottler() = default;

  BaseThrottler(const BaseThrottler& other) = delete;
  BaseThrottler& operator=(const BaseThrottler& other) = delete;

  bool Acquire(uint32_t max_sleep_ms) override;

  bool TryAcquire() override;

 private:
  std::mutex mutex_;
  uint64_t last_update_timestamp_{0};
  uint32_t used_quota_{0};
};

class ShardThrottler : public Throttler {
 public:
  explicit ShardThrottler(std::function<uint32_t()> func, uint32_t shard)
      : Throttler(std::move(func)), shard_(shard) {
    CHECK_GT(shard_, 0);
    shard_func_ = [&]() { return func_() / shard_; };
    for (int i = 0; i < shard_; ++i) {
      throttlers_.emplace_back(std::make_unique<BaseThrottler>(shard_func_));
    }
  }
  ~ShardThrottler() = default;

  ShardThrottler(const ShardThrottler& other) = delete;
  ShardThrottler& operator=(const ShardThrottler& other) = delete;

  bool Acquire(uint32_t max_sleep_ms) override;

  bool TryAcquire() override;

 private:
  const uint32_t shard_;
  std::function<uint32_t()> shard_func_;
  std::vector<std::unique_ptr<BaseThrottler>> throttlers_;
};

}  // namespace dancenn

#endif  // BASE_THROTTLER_H_
