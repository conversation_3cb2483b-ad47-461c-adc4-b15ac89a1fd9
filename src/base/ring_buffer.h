#ifndef BASE_RING_BUFFER_H_
#define BASE_RING_BUFFER_H_

#include <stdlib.h>
#include <stdint.h>
#include <type_traits>
#include <mutex>
#include <condition_variable>

namespace dancenn {

// one reader + one writer is thread-safe,
// LockedTryPush is multi-writers is thread-safe
template<class T>
class RingBuffer {
  static_assert(!std::is_class<T>::value,
      "RingBuffer<T>, only support basic type and pointer");
  public:
   RingBuffer(int n);
   ~RingBuffer();

   RingBuffer(const RingBuffer&) = delete;
   RingBuffer(RingBuffer&&) = delete;

   uint32_t NewPopWindow();  // only called at consume thread
   uint32_t WindowLength();  // only called at consume thread
   bool WindowPop(T* item);  // only called at consume thread
   void WindowSkip();        // only called at consume thread
   bool Peek(T* item);
   bool TryPop(T* item);
   bool TryPush(T item);
   bool LockedTryPush(T item);

   uint32_t Capacity() const { return capacity_; }
   uint32_t Length() const { return (tail_ + capacity_ - head_) % capacity_; }
   bool Empty() const { return head_ == tail_; }
   bool Full() const {
     return (tail_ + 1) % capacity_ == head_;
   }
  private:
   uint32_t          window_;
   volatile uint32_t head_;
   // ensure head_ and tail_ in different cache line
   char padding_[64 - sizeof(head_)];
   volatile uint32_t tail_;
   uint32_t          capacity_;
   T*                data_;
   std::mutex        mutex_;
};

template<class T>
RingBuffer<T>::RingBuffer(int n) {
  data_     = new T[n + 1];
  head_     = 0;
  tail_     = 0;
  capacity_ = n;
}

template<class T>
RingBuffer<T>::~RingBuffer() {
  delete []data_;
}

template<class T>
uint32_t RingBuffer<T>::NewPopWindow() {
  window_ = tail_;
  return (window_ + capacity_ - head_) % capacity_;
}

template <class T>
uint32_t RingBuffer<T>::WindowLength() {
  return (window_ + capacity_ - head_) % capacity_;
}

template<class T>
bool RingBuffer<T>::WindowPop(T* item) {
  if (head_ != window_) {
    return TryPop(item);
  }
  return false;
}

template<class T>
void RingBuffer<T>::WindowSkip() {
  head_ = window_;
}

template<class T>
bool RingBuffer<T>::Peek(T* item) {
  if (Empty()) {
    *item = T{};
    return false;
  }
  *item = data_[head_];
  return true;
}

template<class T>
bool RingBuffer<T>::TryPop(T* item) {
  if (Empty()) {
    *item = T{};
    return false;
  }

  *item = data_[head_];
#ifdef __aarch64__
  asm volatile("dsb sy" ::: "memory");
#else
  asm volatile("" ::: "memory");
#endif
  head_ = (head_ + 1) % capacity_;
  return true;
}

template<class T>
bool RingBuffer<T>::TryPush(T item) {
  if (Full()) return false;
  data_[tail_] = item;
#ifdef __aarch64__
  asm volatile("dsb sy" ::: "memory");
#else
  asm volatile("" ::: "memory");
#endif
  tail_ = (tail_ + 1) % capacity_;
  return true;
}

template<class T>
bool RingBuffer<T>::LockedTryPush(T item) {
  std::lock_guard<std::mutex> lock_(mutex_);
  return TryPush(item);
}

}

#endif
