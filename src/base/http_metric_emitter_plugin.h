// Copyright 2019 livexmm <<EMAIL>>

#ifndef BASE_HTTP_METRIC_EMITTER_PLUGIN_H_
#define BASE_HTTP_METRIC_EMITTER_PLUGIN_H_

#include "base/metric_emitter_plugin.h"

#include <mutex>
#include <condition_variable>

#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <cnetpp/http/http_client.h>

namespace dancenn {

class ReportTask;

class HttpMetricEmitterPlugin : public MetricEmitterPlugin {
 public:
  HttpMetricEmitterPlugin() = default;
  ~HttpMetricEmitterPlugin() = default;

  virtual bool Start() override;
  virtual void Stop() override;

 private:
  class ReportTask : public cnetpp::concurrency::Task {
   public:
    ReportTask() {
      client_ = std::make_unique<cnetpp::http::HttpClient>();
    }

    bool Init() {
      cnetpp::http::HttpClientOptions options;
      options.set_worker_count(1);
      return client_->Launch(options);
    }

    void Stop() override {
      std::lock_guard<std::mutex> guard(mutex_);
      cnetpp::concurrency::Task::Stop();
      cond_var_.notify_all();
    }

    bool operator()(void* arg = nullptr) override;

   private:
    std::mutex mutex_;
    std::condition_variable cond_var_;
    std::unique_ptr<cnetpp::http::HttpClient> client_;
  };

  std::unique_ptr<cnetpp::concurrency::Thread> reporter_;
  std::shared_ptr<ReportTask> report_task_;
};

}  // namespace dancenn

#endif  // BASE_HTTP_METRIC_EMITTER_PLUGIN_H_
