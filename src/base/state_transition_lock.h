#ifndef BASE_STATE_TRANSITION_LOCK_H_
#define BASE_STATE_TRANSITION_LOCK_H_

#include <glog/logging.h>

#include <thread>
#include <atomic>

namespace dancenn {

class StateTransitionLock {

 public:
  StateTransitionLock() = default;
  StateTransitionLock(const StateTransitionLock&) = delete;
  StateTransitionLock& operator=(const StateTransitionLock&) = delete;

  void lock_shared() {
    while (!try_lock_shared()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
  }

  bool try_lock_shared() {
    while (true) {
      if (!switching_) {
        ref_count_++;
        if (switching_) {
          ref_count_--;
          continue;
        }
        return true;
      }
      return false;
    }
  }

  void unlock_shared() {
    CHECK(ref_count_ > 0) << "Not Negative";
    ref_count_--;
  }

  void lock() {
    while (!try_lock()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
  }

  bool try_lock() {
    if (switching_) {
      return false;
    }
    bool switching = false;
    if (!switching_.compare_exchange_strong(switching, true)) {
      return false;
    }
    while (ref_count_ > 0) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    return true;
  }

  void unlock() {
    CHECK(switching_) << "Should first lock";
    switching_ = false;
  }

  // for test
  uint64_t ref_count() const {
    return ref_count_.load();
  }

 private:
  std::atomic<bool> switching_{ false };
  std::atomic<uint64_t> ref_count_{ 0 };
};

}  // namespace dancenn

#endif  // BASE_STATE_TRANSITION_LOCK_H_
