// Copyright 2019 liaocheng <<EMAIL>>

#include "base/string_utils.h"

#include <cnetpp/base/string_utils.h>
#include <cnetpp/concurrency/this_thread.h>

#include <algorithm>
#include <climits>
#include <iomanip>
#include <sstream>

namespace dancenn {

bool StringUtils::StringToInt32(const char* s, size_t length, int32_t* l) {
  int64_t ret = 0;
  if (!StringToInt64(s, length, &ret)) {
    return false;
  }

  if (ret < INT32_MIN || ret > INT32_MAX) {
    return false;
  }

  *l = static_cast<int32_t>(ret);
  return true;
}

bool StringUtils::StringToInt64(const char* s, size_t length, int64_t* l) {
  cnetpp::concurrency::ThisThread::SetLastError(0);
  char* endptr = NULL;
  int64_t x = strtoll(s, &endptr, 10);
  int err = cnetpp::concurrency::ThisThread::GetLastError();
  if (endptr != s + length ||
      (err == ERANGE && (x == LLONG_MAX || x == LLONG_MIN)) ||
      (err != 0 && x == 0)) return false;

  *l = x;
  return true;
}

std::string StringUtils::ToHexString(const std::string& str) {
  std::ostringstream oss;

  oss << std::hex << std::setfill('0') << std::nouppercase;
  for (const unsigned char& c : str) {
    oss << std::setw(2) << static_cast<unsigned int>(c);
  }
  return oss.str();
}

std::string StringUtils::HexStringTo(const std::string& str) {
  if (str.size() % 2 != 0) {
    return "";
  }
  std::stringstream oss;
  for (int i = 0; i < str.size() / 2; ++i) {
    int num = 0;
    {
      auto c = str[2 * i];
      if (c >= '0' && c <= '9') {
        num += c - '0';
      } else if (c >= 'a' && c <= 'f') {
        num += c - 'a' + 10;
      } else if (c >= 'A' && c <= 'F') {
        num += c - 'A' + 10;
      } else {
        return "";
      }
    }
    num *= 16;
    {
      auto c = str[2 * i + 1];
      if (c >= '0' && c <= '9') {
        num += c - '0';
      } else if (c >= 'a' && c <= 'f') {
        num += c - 'a' + 10;
      } else if (c >= 'A' && c <= 'F') {
        num += c - 'A' + 10;
      } else {
        return "";
      }
    }
    oss << static_cast<char>(num);
  }

  return oss.str();
}

bool StringUtils::IsHexString(const std::string& str) {
  if (str.size() % 2 != 0) {
    return false;
  }
  for (const char& c : str) {
    if (c >= '0' && c <= '9') continue;
    if (c >= 'a' && c <= 'f') continue;
    if (c >= 'A' && c <= 'F') continue;
    return false;
  }
  return true;
}

std::vector<std::string> StringUtils::SplitByChars(StringPiece str,
                                              StringPiece separators) {
  return cnetpp::base::StringUtils::SplitByChars(str, separators);
}

void StringUtils::SplitByChars(StringPiece str,
                          StringPiece separators,
                          std::vector<std::string>* result) {
  return cnetpp::base::StringUtils::SplitByChars(str, separators, result);
}

void StringUtils::PutFixed16(std::string* dst, uint16_t value) {
  dst->append(const_cast<const char*>(reinterpret_cast<char*>(&value)),
    sizeof(value));
}

void StringUtils::PutFixed32(std::string* dst, uint32_t value) {
  dst->append(const_cast<const char*>(reinterpret_cast<char*>(&value)),
    sizeof(value));
}

void StringUtils::PutFixed64(std::string* dst, uint64_t value) {
  dst->append(const_cast<const char*>(reinterpret_cast<char*>(&value)),
    sizeof(value));
}

inline uint16_t DecodeFixed16(const char* ptr) {
  uint16_t result;
  memcpy(&result, ptr, sizeof(result));
  return result;
}

inline uint32_t DecodeFixed32(const char* ptr) {
  uint32_t result;
  memcpy(&result, ptr, sizeof(result));
  return result;
}

inline uint64_t DecodeFixed64(const char* ptr) {
  uint64_t result;
  memcpy(&result, ptr, sizeof(result));
  return result;
}

bool StringUtils::IsValidUTF8(const std::string& str) {
  int num_bytes = 0;  // number of bytes current character needs
  for (char c : str) {
    if (num_bytes == 0) {
      // determine the number of bytes the current character needs
      if ((c & 0x80) == 0) {
        num_bytes = 1;
      } else if ((c & 0xE0) == 0xC0) {
        num_bytes = 2;
      } else if ((c & 0xF0) == 0xE0) {
        num_bytes = 3;
      } else if ((c & 0xF8) == 0xF0) {
        num_bytes = 4;
      } else {
        return false;  // illegal UTF-8 character
      }
      num_bytes--;
    } else {
      // check if the current character is a valid UTF-8 character
      if ((c & 0xC0) != 0x80) {
        return false;  // illegal UTF-8 character
      }
      num_bytes--;
    }
  }
  return num_bytes == 0;
}

bool StringUtils::GetFixed16(StringPiece* input, uint16_t* value) {
  if (input->size() < sizeof(uint16_t)) {
    return false;
  }
  *value = DecodeFixed16(input->data());
  input->remove_prefix(sizeof(uint16_t));
  return true;
}

bool StringUtils::GetFixed32(StringPiece* input, uint32_t* value) {
  if (input->size() < sizeof(uint32_t)) {
    return false;
  }
  *value = DecodeFixed32(input->data());
  input->remove_prefix(sizeof(uint32_t));
  return true;
}

bool StringUtils::GetFixed64(StringPiece* input, uint64_t* value) {
  if (input->size() < sizeof(uint64_t)) {
    return false;
  }
  *value = DecodeFixed64(input->data());
  input->remove_prefix(sizeof(uint64_t));
  return true;
}

std::string StringUtils::BoolToString(bool b) {
  return b ? "true" : "false";
}

void StringUtils::SplitStringForAddr(const std::string& str,
                                     const char* delimiter,
                                     std::vector<std::string>* result) {
  result->clear();
  auto reverse_pos = str.find_last_of(delimiter);
  if (reverse_pos == std::string::npos) {
    result->push_back(str);
    return;
  }

  if (reverse_pos == 0) {
    result->push_back("");
  } else {
    result->push_back(str.substr(0, reverse_pos - 0));
  }

  if (reverse_pos == str.size() - 1) {
    result->push_back("");
  } else {
    result->push_back(str.substr(reverse_pos + 1));
  }
}

void StringUtils::TrimIPString(std::string* ip_str) {
  ip_str->erase(std::remove(ip_str->begin(), ip_str->end(), '['),
                ip_str->end());
  ip_str->erase(std::remove(ip_str->begin(), ip_str->end(), ']'),
                ip_str->end());
}

}  // namespace dancenn
