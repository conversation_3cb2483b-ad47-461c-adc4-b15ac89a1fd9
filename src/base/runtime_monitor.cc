// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#include "base/runtime_monitor.h"
#include "base/platform.h"

#include <glog/logging.h>
#include <cnetpp/concurrency/this_thread.h>
#include <cnetpp/base/string_utils.h>

#if defined(OS_DARWIN)
#include <mach/mach.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <sys/mount.h>
#else
#include <sys/vfs.h>
#endif

#include <unistd.h>

#include <cinttypes>
#include <chrono>
#include <utility>
#include <algorithm>

#include "version.h"  // NOLINT(build/include)
#include "base/defer.h"
#include "base/metrics.h"
#include "base/file_utils.h"

DECLARE_string(namespace_meta_storage_path);
DECLARE_string(namespace_meta_storage_ckpt_path);
DECLARE_string(log_dir);
DECLARE_string(bvc_version);

namespace dancenn {

namespace {

const char* kMTabPath = "/etc/mtab";
const char* kDiskStats = "/proc/diskstats";
const size_t kBufferSize = 16 * 1024;

void FindMountPoint(
    const std::string& path,
    const std::vector<std::vector<std::string>>& candidate_lines,
    std::unordered_map<std::string, std::vector<std::string>>* ret) {
  auto itr = std::find_if(
      candidate_lines.begin(),
      candidate_lines.end(),
      [&] (const std::vector<std::string>& comps) -> bool {
        std::string npath = path;
        if (npath.back() != '/') {
          npath += "/";
        }
        if (cnetpp::base::StringPiece(npath).starts_with(comps[1] + "/")) {
          return true;
        }
        return false;
      });
  if (itr != candidate_lines.end()) {
    auto t = cnetpp::base::StringUtils::SplitByString((*itr)[0], "/");
    auto itr2 = ret->find(t.back());
    if (itr2 == ret->end()) {
      itr2 = ret->emplace(t.back(), std::vector<std::string>()).first;
    }
    itr2->second.emplace_back(path);
  }
}

}  // anonymous namespace

RuntimeMonitor::RuntimeMonitor(int check_interval_in_ms)
    : check_interval_ms_(check_interval_in_ms) {
  dir_to_histo_map_.emplace(
      FLAGS_namespace_meta_storage_path,
      std::make_shared<GenericHistogram<double>>(-1, 10, 30));
  dir_to_histo_map_.emplace(
      FLAGS_namespace_meta_storage_ckpt_path,
      std::make_shared<GenericHistogram<double>>(-1, 10, 30));

  auto center = MetricsCenter::Instance();
  metrics_ = center->RegisterMetrics("RuntimeMonitor");
  vcs_version_metric_ = metrics_->RegisterGauge(
      "VcsVersion#version=" + kVcsVersion,
      [] () -> double { return 1.; });
  bvc_version_metric_ = metrics_->RegisterGauge(
      "BvcVersion#version=" + FLAGS_bvc_version,
      [] () -> double { return 1.; });
  ram_total_metric_ = metrics_->RegisterGauge("RAMTotal");
  proc_memory_total_metric_ = metrics_->RegisterGauge("ProcessMemoryTotal");
  proc_memory_rss_metric_ = metrics_->RegisterGauge("ProcessMemoryRSS");
  proc_cpu_usage_metric_ = metrics_->RegisterGauge("ProcessCpuUsage");
  load_avg_1_metric_ = metrics_->RegisterGauge("LoadAvg1Min");
  load_avg_5_metric_ = metrics_->RegisterGauge("LoadAvg5Min");
  load_avg_15_metric_ = metrics_->RegisterGauge("LoadAvg15Min");

  meta_storage_path_capacity_metric_ =
    metrics_->RegisterGauge("MetaStorageDiskTotal");
  meta_storage_path_used_metric_ =
    metrics_->RegisterGauge("MetaStorageDiskUsed");
  meta_storage_path_utilization_metric_ =
      metrics_->RegisterGauge("MetaStorageDiskUtilization");
  meta_storage_ckpt_path_capacity_metric_ =
    metrics_->RegisterGauge("MetaStorageCkptDiskTotal");
  meta_storage_ckpt_path_used_metric_ =
      metrics_->RegisterGauge("MetaStorageCkptDiskUsed");
  meta_storage_ckpt_path_utilization_metric_ =
    metrics_->RegisterGauge("MetaStorageCkptDiskUtilization");
  log_path_capacity_metric_ = metrics_->RegisterGauge("LogDiskTotal");
  log_path_used_metric_ = metrics_->RegisterGauge("LogDiskUsed");
  log_path_utilization_metric_ = metrics_->RegisterGauge("LogDiskUtilization");

  meta_storage_path_io_util_metric_ =
    metrics_->RegisterGauge("MetaStorageDiskIOUtil", [this] () -> double {
      auto itr = dir_to_histo_map_.find(FLAGS_namespace_meta_storage_path);
      CHECK(itr != dir_to_histo_map_.end());
      auto snapshot = std::unique_ptr<GenericHistogram<double>::Snapshot>(
          itr->second->MakeSnapshot());
      auto res = snapshot->Avg();
      return res > 1.0 ? 1.0 : res;
    });
  meta_storage_ckpt_path_io_util_metric_ =
    metrics_->RegisterGauge("MetaStorageCkptDiskIOUtil", [this] () -> double {
      auto itr = dir_to_histo_map_.find(FLAGS_namespace_meta_storage_ckpt_path);
      CHECK(itr != dir_to_histo_map_.end());
      auto snapshot = std::unique_ptr<GenericHistogram<double>::Snapshot>(
          itr->second->MakeSnapshot());
      auto res = snapshot->Avg();
      return res > 1.0 ? 1.0 : res;
    });

  device_to_dir_map_ = GetMTab();

  GetProcessCpuUsage();
  GetTotalMemory();
  GetProcessMemoryUsage();
  GetDisk();

  runner_ = std::make_unique<cnetpp::concurrency::Thread>([&] () -> bool {
    this->RunLoop();
    return true;
  }, "RTMonitor");

  runner_->Start();
}

RuntimeMonitor::~RuntimeMonitor() {
  LOG(INFO) << "Destructing RuntimeMonitor...";
  {
    std::lock_guard<std::mutex> guard(mutex_);
    is_stopping_ = true;
    cv_.notify_all();
  }
  if (runner_) {
    runner_->Stop();
  }

  metrics_->DeregisterGauge(ram_total_metric_);
  metrics_->DeregisterGauge(proc_memory_total_metric_);
  metrics_->DeregisterGauge(proc_memory_rss_metric_);
  metrics_->DeregisterGauge(proc_cpu_usage_metric_);
  metrics_->DeregisterGauge(load_avg_1_metric_);
  metrics_->DeregisterGauge(load_avg_5_metric_);
  metrics_->DeregisterGauge(load_avg_15_metric_);

  metrics_->DeregisterGauge(meta_storage_path_capacity_metric_);
  metrics_->DeregisterGauge(meta_storage_path_used_metric_);
  metrics_->DeregisterGauge(meta_storage_path_utilization_metric_);
  metrics_->DeregisterGauge(meta_storage_ckpt_path_capacity_metric_);
  metrics_->DeregisterGauge(meta_storage_ckpt_path_used_metric_);
  metrics_->DeregisterGauge(meta_storage_ckpt_path_utilization_metric_);
  metrics_->DeregisterGauge(log_path_capacity_metric_);
  metrics_->DeregisterGauge(log_path_used_metric_);
  metrics_->DeregisterGauge(log_path_utilization_metric_);

  metrics_->DeregisterGauge(meta_storage_path_io_util_metric_);
  metrics_->DeregisterGauge(meta_storage_ckpt_path_io_util_metric_);
}

void RuntimeMonitor::RunLoop() {
  for (;;) {
    std::unique_lock<std::mutex> guard(mutex_);
    if (is_stopping_) {
      break;
    }
    GetProcessCpuUsage();
    GetProcessMemoryUsage();
    GetDisk();
    cv_.wait_for(guard, std::chrono::milliseconds(check_interval_ms_));
  }
}

void RuntimeMonitor::GetProcessCpuUsage() {
  int64_t cores = sysconf(_SC_NPROCESSORS_ONLN);
#if defined(OS_DARWIN)
  LOG(INFO) << "Do not support cpu usage on mac";
#else
  uint64_t cpu_time_proc = 0;
  uint64_t cpu_time_overall = 0;

  {
    auto path = "/proc/" + std::to_string(getpid()) + "/stat";
    FILE *fp = fopen(path.c_str(), "r");
    if (!fp) {
      LOG(ERROR) << "Could not open file: " << path.c_str();
      return;
    }
    DEFER([fp]() { fclose(fp); });

    unsigned long ctime = 0;
    unsigned long stime = 0;
    if (fscanf(fp, "%*d %*s %*c %*d"  // pid, command, state, ppid
          "%*d %*d %*d %*d %*u %*u %*u %*u %*u"
          "%lu %lu",  // usertime, systemtime
          &ctime, &stime) != 2) {
      LOG(ERROR) << "Failed to read " << path;
      return;
    }
    cpu_time_proc =
      static_cast<uint64_t>(ctime) + static_cast<uint64_t>(stime);
  }

  {
    FILE *fp = fopen("/proc/stat", "r");
    if (!fp) {
      LOG(ERROR) << "Could not open file: /proc/stat";
      return;
    }
    DEFER([fp]() { fclose(fp); });

    uint64_t syscpu[10] = {0};
    if (fscanf(fp,
          "%*s %" PRIu64 " %" PRIu64 " %" PRIu64 " %" PRIu64 " %" PRIu64
          " %" PRIu64 " %" PRIu64 " %" PRIu64 " %" PRIu64 " %" PRIu64,
          &syscpu[0], &syscpu[1], &syscpu[2], &syscpu[3], &syscpu[4],
          &syscpu[5], &syscpu[6], &syscpu[7], &syscpu[8], &syscpu[9]) != 10) {
      LOG(ERROR) << "Failed to read /proc/stat";
      return;
    }

    for (int i = 0; i < 10; ++i) {
      cpu_time_overall += syscpu[i];
    }
  }

  double tmp =
    static_cast<double>(cpu_time_proc - cpu_time_proc_) *
    static_cast<double>(cores) /
    static_cast<double>(cpu_time_overall - cpu_time_overall_);
  proc_cpu_usage_metric_->Update(tmp * 100);

  cpu_time_proc_ = cpu_time_proc;
  cpu_time_overall_ = cpu_time_overall;

  {
    FILE* fp = fopen("/proc/loadavg", "r");
    if (!fp) {
      LOG(ERROR) << "Could not open file: /proc/loadavg";
      return;
    }
    DEFER([fp]() { fclose(fp); });

    double load_avg_1 = 0.;
    double load_avg_5 = 0.;
    double load_avg_15 = 0.;
    if (fscanf(fp,
               "%lf %lf %lf",
               &load_avg_1,
               &load_avg_5,
               &load_avg_15) != 3) {
      LOG(ERROR) << "Failed to read /proc/loadavg";
      return;
    }
    load_avg_1_metric_->Update(load_avg_1);
    load_avg_5_metric_->Update(load_avg_5);
    load_avg_15_metric_->Update(load_avg_15);
  }
#endif
}

void RuntimeMonitor::GetProcessMemoryUsage() {
#if defined(OS_DARWIN)
  struct task_basic_info t_info;
  mach_msg_type_number_t t_info_count = TASK_BASIC_INFO_COUNT;

  if (task_info(mach_task_self(),
                TASK_BASIC_INFO,
                (task_info_t)&t_info,
                &t_info_count) != KERN_SUCCESS) {
    LOG(FATAL) << "Could not get process memory stats";
    return;
  }
  proc_memory_total_metric_->Update(t_info.resident_size);
  proc_memory_rss_metric_->Update(t_info.virtual_size);
#else
  uint64_t total = 0, rss = 0;
  std::string path = "/proc/" + std::to_string(getpid()) + "/statm";
  FILE *fp = fopen(path.c_str(), "r");
  if (!fp) {
    LOG(ERROR) << "Could not open file: " << path;
    return;
  }

  DEFER([fp] () {
    fclose(fp);
  });

  if (fscanf(fp, "%" PRIu64 " %" PRIu64, &total, &rss) != 2) {
    LOG(ERROR) << "Failed to read file: " << path;
    return;
  }
  auto page_size = sysconf(_SC_PAGESIZE);
  proc_memory_total_metric_->Update(total * page_size);
  proc_memory_rss_metric_->Update(rss * page_size);
#endif
}

void RuntimeMonitor::GetTotalMemory() {
  int64_t total_ram;
#if defined(OS_DARWIN)
  int mib[2];
  mib[0] = CTL_HW;
  mib[1] = HW_MEMSIZE;
  size_t length = sizeof(int64_t);
  if (sysctl(mib, 2, &total_ram, &length, nullptr, 0) != 0) {
    LOG(FATAL) << "Could not get physical memory.";
  }
#else
  auto pages = sysconf(_SC_PHYS_PAGES);
  auto page_size = sysconf(_SC_PAGESIZE);
  total_ram = static_cast<int64_t>(pages) * page_size;
#endif
  LOG(INFO) << "Total physical memory: " << total_ram;
  ram_total_metric_->Update(total_ram);
}

void RuntimeMonitor::GetDisk() {
  struct statfs buf{};
  if (statfs(FLAGS_namespace_meta_storage_path.c_str(), &buf) < 0) {
    LOG(FATAL) << "Could not get disk information for "
               << FLAGS_namespace_meta_storage_path << ": "
               << cnetpp::concurrency::ThisThread::GetLastErrorString();
    return;
  }
  auto block_size = static_cast<uint64_t>(buf.f_bsize);
  auto disk_total = block_size * static_cast<uint64_t>(buf.f_blocks);
  auto disk_free = block_size * static_cast<uint64_t>(buf.f_bfree);
  meta_storage_path_capacity_metric_->Update(disk_total);
  meta_storage_path_used_metric_->Update(disk_total - disk_free);
  meta_storage_path_utilization_metric_->Update(
      (disk_total - disk_free) / static_cast<double>(disk_total));

  if (statfs(FLAGS_namespace_meta_storage_ckpt_path.c_str(), &buf) < 0) {
    DLOG(WARNING) << "Could not get disk information for "
                  << FLAGS_namespace_meta_storage_ckpt_path << ": "
                  << cnetpp::concurrency::ThisThread::GetLastErrorString();
    return;
  }
  block_size = static_cast<uint64_t>(buf.f_bsize);
  disk_total = block_size * static_cast<uint64_t>(buf.f_blocks);
  disk_free = block_size * static_cast<uint64_t>(buf.f_bfree);
  meta_storage_ckpt_path_capacity_metric_->Update(disk_total);
  meta_storage_ckpt_path_used_metric_->Update(disk_total - disk_free);
  meta_storage_ckpt_path_utilization_metric_->Update(
      (disk_total - disk_free) / static_cast<double>(disk_total));

  if (statfs(FLAGS_log_dir.c_str(), &buf) < 0) {
    LOG(WARNING) << "Could not get disk information for " << FLAGS_log_dir
                 << ":"
                 << cnetpp::concurrency::ThisThread::GetLastErrorString();
    return;
  }
  block_size = static_cast<uint64_t>(buf.f_bsize);
  disk_total = block_size * static_cast<uint64_t>(buf.f_blocks);
  disk_free = block_size * static_cast<uint64_t>(buf.f_bfree);
  log_path_capacity_metric_->Update(disk_total);
  log_path_used_metric_->Update(disk_total - disk_free);
  log_path_utilization_metric_->Update((disk_total - disk_free) /
                                       static_cast<double>(disk_total));

#if !defined(OS_DARWIN)
  auto disk_stats = GatherDiskStat();
  for (auto& ds : disk_stats) {
    for (auto& dir : ds.dirs) {
      auto itr = dir_to_last_stat_map_.find(dir);
      if (itr != dir_to_last_stat_map_.end()) {
        auto itr2 = dir_to_histo_map_.find(dir);
        CHECK(itr2 != dir_to_histo_map_.end());
        auto interval = ds.current_time_ms - itr->second.current_time_ms;
        auto took = static_cast<double>(
            ds.time_spent_io_ms - itr->second.time_spent_io_ms);
        itr2->second->Update(took / interval);
      }
      dir_to_last_stat_map_[dir] = ds;
    }
  }
#endif
}

std::unordered_map<std::string, std::vector<std::string>>
RuntimeMonitor::GetMTab() {
  std::unordered_map<std::string, std::vector<std::string>> ret;
  RandomAccessFile raf(kMTabPath);
  std::string buffer;
  buffer.resize(kBufferSize);
  cnetpp::base::StringPiece str;
  if (!raf.Read(0, &(buffer[0]), kBufferSize, &str)) {
    LOG(ERROR) << "Failed to read file " << kMTabPath << ".";
    return ret;
  }
  auto lines = cnetpp::base::StringUtils::SplitByChars(str, "\r\n");
  std::vector<std::vector<std::string>> candidate_lines;
  for (auto& line : lines) {
    auto components = cnetpp::base::StringUtils::SplitByChars(line, "\t \b");
    if (components.size() != 6) {
      continue;
    }
    candidate_lines.emplace_back(std::move(components));
  }
  FindMountPoint(FLAGS_namespace_meta_storage_path, candidate_lines, &ret);
  FindMountPoint(FLAGS_namespace_meta_storage_ckpt_path, candidate_lines, &ret);
  return ret;
}

std::vector<RuntimeMonitor::DiskStatSummary> RuntimeMonitor::GatherDiskStat() {
  std::vector<DiskStatSummary> ret;
  auto start = std::chrono::steady_clock::now();

  RandomAccessFile raf(kDiskStats);
  std::string buffer;
  buffer.resize(kBufferSize);
  cnetpp::base::StringPiece str;
  if (!raf.Read(0, &(buffer[0]), kBufferSize, &str)) {
    LOG(ERROR) << "Failed to read file " << kDiskStats << ".";
    return ret;
  }

  auto lines = cnetpp::base::StringUtils::SplitByChars(str, "\r\n");
  for (auto& line : lines) {
    auto cols = cnetpp::base::StringUtils::SplitByChars(line, "\t \b");
    if (cols.size() != 14) {
      continue;
    }
    auto itr = device_to_dir_map_.find(cols[2]);
    if (itr == device_to_dir_map_.end()) {
      continue;
    }
    DiskStatSummary dss;
    dss.dirs = itr->second;

#define DANCENN_TO_INT_ELSE_CONTINUE(l, s) {\
  l = std::strtol(s.c_str(), nullptr, 10);\
  if (cnetpp::concurrency::ThisThread::GetLastError() == EINVAL ||\
      cnetpp::concurrency::ThisThread::GetLastError() == ERANGE) {\
    continue;\
  }\
}

    DANCENN_TO_INT_ELSE_CONTINUE(dss.major_number, cols[0]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.minor_number, cols[1]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.reads, cols[3]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.reads_merged, cols[4]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.sectors_read, cols[5]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.time_spent_reading_ms, cols[6]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.writes, cols[7]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.writes_merged, cols[8]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.sectors_written, cols[9]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.time_spent_writing_ms, cols[10]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.io_in_progress, cols[11]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.time_spent_io_ms, cols[12]);
    DANCENN_TO_INT_ELSE_CONTINUE(dss.weighted_time_spent_io_ms, cols[13]);
    dss.current_time_ms =
      std::chrono::duration_cast<std::chrono::milliseconds>(
          start.time_since_epoch()).count();
    dss.num_devices = 1;
    ret.emplace_back(dss);
  }

  return ret;
}

}  // namespace dancenn
