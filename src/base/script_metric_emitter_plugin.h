// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_SCRIPT_METRIC_EMITTER_PLUGIN_H_
#define BASE_SCRIPT_METRIC_EMITTER_PLUGIN_H_

#include "base/metric_emitter_plugin.h"

#include <gflags/gflags.h>

#include <memory>
#include <vector>

namespace dancenn {

class ScriptMetricEmitterPlugin : public MetricEmitterPlugin {
 public:
  virtual bool Start() override;
  virtual void Stop() override;

 private:
  pid_t pid_{0};
};

class ScriptExecutor {
 public:
  ScriptExecutor() = delete;
  ScriptExecutor(const std::string& cmd): command_(cmd) {}
  ~ScriptExecutor() = default;

  void AddArg(const std::string& flag, const std::string& value);
  int Run();

 private:
  std::string command_;
  std::vector<std::string> arg_vector_;
};

}  // namespace dancenn

#endif  // BASE_SCRIPT_METRIC_EMITTER_PLUGIN_H_
