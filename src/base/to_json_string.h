// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/06/20
// Description
// TODO(ruanjunbin): Import a third-party json library is a better idea.

#ifndef BASE_TO_JSON_STRING_H_
#define BASE_TO_JSON_STRING_H_

#include <cnetpp/base/csonpp.h>
#include <glog/logging.h>
#include <google/protobuf/message.h>
#include <cstdint>
#include <string>
#include <type_traits>
#include <utility>
#include <vector>

#include "base/pb_converter.h"

namespace dancenn {

template <typename T>
inline void ToJson(
    const T& t,
    cnetpp::base::Value* value,
    typename std::enable_if<std::is_integral<T>::value>::type* = 0) {
  CHECK_NOTNULL(value);
  *value = t;
}

inline void ToJson(const std::string& t, cnetpp::base::Value* value) {
  CHECK_NOTNULL(value);
  *value = t;
}

template <typename T>
inline void ToJson(
    const T& t,
    cnetpp::base::Value* value,
    typename std::enable_if<
        std::is_base_of<google::protobuf::Message, T>::value>::type* = 0) {
  CHECK_NOTNULL(value);
  *value = dancenn::PBConverter::ToJson(t);
}

template <typename T>
inline void ToJson(const std::vector<T>& ts, cnetpp::base::Value* value) {
  CHECK_NOTNULL(value);
  cnetpp::base::Array arr;
  for (const T& t : ts) {
    cnetpp::base::Value v;
    ToJson(t, &v);
    arr.Append(v);
  }
  *value = std::move(arr);
}

template <typename T>
inline std::string ToJsonCompactString(const T& t) {
  cnetpp::base::Value value;
  ToJson(t, &value);
  return cnetpp::base::Parser::Serialize(std::move(value));
}

}  // namespace dancenn

#endif  // BASE_TO_JSON_STRING_H_
