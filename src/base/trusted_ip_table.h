// Copyright 2020 Mu Xiong <<EMAIL>>

#ifndef BASE_TRUSTED_IP_TABLE_H_
#define BASE_TRUSTED_IP_TABLE_H_

#include <string>
#include <unordered_set>
#include <vector>

#include "base/refresher.h"
#include "base/read_write_lock.h"
#include "base/vlock.h"

namespace dancenn {
namespace security {

bool IsTrustedIp(const std::string& ip);

std::vector<std::string> ListTrustedIp();

std::string ListTrustedIpJsonString();

bool UpdatedTrustedIp();

class TrustedIpTable {
 public:
  ~TrustedIpTable();

  TrustedIpTable(const TrustedIpTable&) = delete;
  TrustedIpTable& operator=(const TrustedIpTable&) = delete;

  static TrustedIpTable& GetSingleton();

  bool ParseAndUpdate(const std::string& json_string);

  bool IsTrustedIp(const std::string& ip);

  std::vector<std::string> ListTrustedIp();

 private:
  TrustedIpTable();

  bool UpdateConfig(std::vector<std::string> v);

 private:
  std::string config_string_;

  std::unordered_set<std::string> trusted_ips_;

  VRWLock rwlock_;

  std::unique_ptr<Refresher> refresher_{nullptr};
};

}  // namespace security
}  // namespace dancenn

#endif  // BASE_TRUSTED_IP_TABLE_H_
