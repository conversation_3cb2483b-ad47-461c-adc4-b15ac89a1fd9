// Copyright 2019 livexmm <<EMAIL>>

#ifndef BASE_METRICS_H_
#define BASE_METRICS_H_

#include <glog/logging.h>
#include <cnetpp/base/csonpp.h>

#include <string.h>
#include <pthread.h>

#include <string>
#include <vector>
#include <deque>
#include <unordered_map>
#include <mutex>
#include <memory>

#include "base/constants.h"
#include "base/metric.h"

namespace dancenn {

class MetricID {
 public:
  explicit MetricID() : id_(UINT32_MAX) {}
  explicit MetricID(uint32_t id) : id_(id) {}
  MetricID(const MetricID& id) { id_ = id.ID(); }
  MetricID& operator=(const MetricID& id) {
    id_ = id.ID();
    return *this;
  }
  uint32_t ID() const { return id_; }

 private:
  uint32_t id_;
};

class MetricsNameTable {
 public:
  MetricsNameTable() : size_(0) {}
  ~MetricsNameTable() {}

  uint32_t GetOrCreate(const std::string& name);
  std::string GetName(uint32_t id);
  int size() const { return size_; }

 private:
  std::mutex mtx_;
  int        size_;
  std::unordered_map<std::string, int> name_to_id_;
  std::deque<std::string>              id_to_name_;
};

template<class T>
class Collections {
 public:
  Collections(int n) {
    length_     = (n < 4 ? 4 : n);
    objects_ = (T **)malloc(sizeof(T*) * length_);
    memset(objects_, 0, sizeof(T*) * length_);
    prev_ = nullptr;
    next_ = nullptr;
  }

  ~Collections() {
    for (uint32_t i = 0; i < length_; i++) {
      if (objects_[i] != nullptr) {
        delete objects_[i];
      }
    }
    free(objects_);
  }

  T* Get(uint32_t index) {
    if (UNLIKELY(index >= length_)) {
      return nullptr;
    }
    return objects_[index];
  }

  void Insert(uint32_t index, T* object) {
    if (index >= length_) {
      CHECK(index != UINT32_MAX) << "metric is not registered";
      uint32_t new_length = length_ * 2;
      if (new_length <= index) new_length = index + 1;
      auto t = (T **)malloc(sizeof(T*) * new_length);
      memcpy(t, objects_, sizeof(T*) * length_);
      memset(((char *)t) + sizeof(T*) * length_,
             0,
             sizeof(T*) * (new_length - length_));
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
      auto old = objects_;
      objects_ = t;
#ifdef __aarch64__
  asm volatile("dsb sy" ::: "memory");
#else
  asm volatile("" ::: "memory");
#endif
      length_  = new_length;
      free(old);
    }
#ifdef __aarch64__
    asm volatile("dsb sy" ::: "memory");
#else
    asm volatile("mfence" ::: "memory");
#endif
    objects_[index] = object;
  }

  Collections<T>* prev() const { return prev_; }
  Collections<T>* next() const { return next_; }
  void prev(Collections<T>* p) { prev_ = p; }
  void next(Collections<T>* n) { next_ = n; }

 private:
   volatile uint32_t length_;
   T** volatile      objects_;
   Collections<T>*   prev_;
   Collections<T>*   next_;
};

template<class T>
class LinkedCollections {
 public:
  LinkedCollections() : tail_(nullptr) {
    pthread_rwlock_init(&rwlock_, nullptr);
  };

  ~LinkedCollections() {
    pthread_rwlock_destroy(&rwlock_);
  }

  void Register(Collections<T>* h) {
    pthread_rwlock_wrlock(&rwlock_);
    if (tail_ != nullptr) {
      h->prev(tail_);
      tail_->next(h);
    }
    tail_ = h;
    pthread_rwlock_unlock(&rwlock_);
  }

  void UnRegister(Collections<T>* h) {
    pthread_rwlock_wrlock(&rwlock_);
    auto p = h->prev();
    auto n = h->next();
    if (p != nullptr) {
      p->next(n);
    }
    if (n != nullptr) {
      n->prev(p);
    }
    if (tail_ == h) {
      tail_ = p;
    }
    pthread_rwlock_unlock(&rwlock_);
  }

  template<class TT = void,
    typename std::enable_if<std::is_same<T, Counter>::value,
                            TT>::type* = nullptr>
  int64_t Snapshot(int index) {
    // opt(livexmm) less lock better parallel when process
    // often create/destroy thread
    int64_t value = 0;
    pthread_rwlock_rdlock(&rwlock_);
    auto current = tail_;
    while (current != nullptr) {
      auto h = current->Get(index);
      if (h != nullptr) {
        value += h->GetValue();
      }
      current = current->prev();
    }
    pthread_rwlock_unlock(&rwlock_);
    return value;
  }

  template<class TT = void,
    typename std::enable_if<std::is_same<T, Histogram>::value,
                            TT>::type* = nullptr>
  std::shared_ptr<Histogram::Snapshot> Snapshot(int index) {
    // opt(livexmm) less lock better parallel when process
    // often create/destroy thread
    std::vector<Histogram::Snapshot*> snapshots;
    pthread_rwlock_rdlock(&rwlock_);
    auto current = tail_;
    while (current != nullptr) {
      auto h = current->Get(index);
      if (h != nullptr) {
        snapshots.emplace_back(h->MakeSnapshot());
      }
      current = current->prev();
    }
    pthread_rwlock_unlock(&rwlock_);

    if (snapshots.size() > 0) {
      for (size_t i = 1; i < snapshots.size(); i++) {
        snapshots[0]->Merge(snapshots[i]);
        delete snapshots[i];
      }
      return std::shared_ptr<Histogram::Snapshot>(snapshots[0]);
    }
    return std::make_shared<Histogram::Snapshot>();
  }

 private:
  pthread_rwlock_t rwlock_;
  Collections<T>*  tail_;
};

class Metrics {
 public:
  static std::shared_ptr<Metrics> Create(const std::string& prefix) {
    return std::shared_ptr<Metrics>(new Metrics(prefix));
  }

  static Histogram* GetOrCreateHistogram(const MetricID& id,
      uint32_t window_size = 2048,
      uint32_t expires = 60) {
    if (UNLIKELY(histogram_tls_ == nullptr)) {
      histogram_tls_ = new Collections<Histogram>(histogram_names_.size());
      histograms_.Register(histogram_tls_);
      PrepareCleanupTLSAtThreadExit();
    }

    auto h = histogram_tls_->Get(id.ID());
    if (UNLIKELY(h == nullptr)) {
      h = new Histogram(id.ID(), window_size, expires);
      histogram_tls_->Insert(id.ID(), h);
    }
    return h;
  }

  static Counter* GetOrCreateCounter(const MetricID& id) {
    if (UNLIKELY(counter_tls_ == nullptr)) {
      counter_tls_ = new Collections<Counter>(counter_names_.size());
      counters_.Register(counter_tls_);
      PrepareCleanupTLSAtThreadExit();
    }

    auto c = counter_tls_->Get(id.ID());
    if (UNLIKELY(c == nullptr)) {
      c = new Counter(id.ID());
      counter_tls_->Insert(id.ID(), c);
    }
    return c;
  }

  static std::string GetCounterName(uint32_t id);
  static std::string GetHistogramName(uint32_t id);

  ~Metrics() {}

  std::shared_ptr<Gauge> RegisterGauge(
      const std::string& name, std::function<double()> getter = nullptr);
  void DeregisterGauge(const std::string& name);
  void DeregisterGauge(std::shared_ptr<Gauge> gauge);
  std::shared_ptr<AtomicCounter> RegisterAtomicCounter(
      const std::string& name);
  void DeregisterAtomicCounter(const std::string& name);
  void DeregisterAtomicCounter(std::shared_ptr<AtomicCounter> counter);

  MetricID RegisterCounter(const std::string& name);
  MetricID RegisterHistogram(const std::string& name);

  std::unordered_map<std::string, double> AllGaugeSnapshots();
  std::unordered_map<std::string, int64_t> AllAtomicCounterSnapshots();
  std::unordered_map<uint32_t, int64_t> AllCounterSnapshots();
  std::unordered_map<uint32_t, std::shared_ptr<Histogram::Snapshot>>
    AllHistogramSnapshots();

  std::string GetGaugeFullName(const std::string& name);
  std::string GetAtomicCounterFullName(const std::string& name);
  std::string GetCounterFullName(uint32_t id);
  std::string GetHistogramFullName(uint32_t id);

  const std::string& prefix() const { return prefix_; }

  void set_dynamic_tag_injector(const std::function<std::string()>& injector) {
    dynamic_tag_injector_ = injector;
  }

 private:
  static void CleanupAtThreadExit(void* argv __attribute__((unused))) {
    if (Metrics::histogram_tls_ != nullptr) {
      Metrics::histograms_.UnRegister(Metrics::histogram_tls_);
      delete Metrics::histogram_tls_;
      Metrics::histogram_tls_ = nullptr;
    }
    if (Metrics::counter_tls_ != nullptr) {
      Metrics::counters_.UnRegister(Metrics::counter_tls_);
      delete Metrics::counter_tls_;
      Metrics::counter_tls_ = nullptr;
    }
  }

  explicit Metrics(const std::string& prefix)
      : prefix_(prefix),
        dynamic_tag_injector_(nullptr) {
    std::call_once(once_flag_, [] () {
      if (pthread_key_create(&tls_key_, CleanupAtThreadExit) != 0) {
        LOG(FATAL) << "metric create thread key fail: " << errno;
      }
    });
  }

  std::function<std::string()> dynamic_tag_injector();

  static void PrepareCleanupTLSAtThreadExit() {
    if (pthread_getspecific(tls_key_) == nullptr) {
      void* flag = (void *)(uintptr_t)0x1;
      if (pthread_setspecific(tls_key_, flag) != 0) {
        LOG(FATAL) << "metric set thread specific fail: " << errno;
      }
    }
  }

  std::string                             prefix_;
  std::function<std::string()>            dynamic_tag_injector_;

  static std::once_flag                   once_flag_;
  static pthread_key_t                    tls_key_;

  // FIXME may memory allocate but no free at main thread
  std::vector<uint32_t>                   histogram_ids_;
  static MetricsNameTable                 histogram_names_;
  static LinkedCollections<Histogram>     histograms_;
  static __thread Collections<Histogram>* histogram_tls_;

  std::vector<uint32_t>                   counter_ids_;
  static MetricsNameTable                 counter_names_;
  static LinkedCollections<Counter>       counters_;
  static __thread Collections<Counter>*   counter_tls_;

  std::mutex mtx_;
  std::unordered_map<std::string, std::shared_ptr<Gauge>> gauges_;
  std::unordered_map<std::string,
    std::shared_ptr<AtomicCounter>> atomic_counters_;
};

class MetricsCenter {
 public:
  static MetricsCenter* Instance() { return &center_; }

  static void set_global_dynamic_tag_injector(
      std::function<std::string()> injector) {
    global_dynamic_tag_injector_ = std::move(injector);
  }

  static std::function<std::string()> global_dynamic_tag_injector() {
    return global_dynamic_tag_injector_;
  }

  MetricsCenter(const MetricsCenter&) = delete;
  MetricsCenter& operator=(const MetricsCenter&) = delete;

  std::shared_ptr<Metrics> RegisterMetrics(const std::string& prefix);

  std::unordered_map<std::string, std::shared_ptr<Metrics>> all_metrics() const;

  void ToJson(std::string* result) const;
  void ToJson(cnetpp::base::Object* result) const;

 private:
  MetricsCenter() = default;
  ~MetricsCenter() = default;

  mutable std::mutex mtx_;
  std::unordered_map<std::string, std::shared_ptr<Metrics>> all_metrics_;

  static MetricsCenter center_;
  static std::function<std::string()> global_dynamic_tag_injector_;
};

static inline Histogram* MFH(const MetricID& id) {
  return Metrics::GetOrCreateHistogram(id);
}

static inline Counter* MFC(const MetricID& id) {
  return Metrics::GetOrCreateCounter(id);
}

}  // namespace dancenn

#endif  // BASE_METRICS_H_
