// Copyright (c) @ 2021.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2021/05/27
// Description

#include "base/block_event_logger.h"

#include "base/to_json_string.h"
#include "cnetpp/base/csonpp.h"
#include "glog/logging.h"

namespace dancenn {
namespace {

const char* kDnIdKey = "dn_id";
const char* kTempBasedRemainingKey = "temperature_based_remaining";

}  // namespace

void BlockEventLogger::ChooseTarget4Recover(
    const std::string& src_path, const std::vector<uint32_t>& dn_ids) {
  VLOG(8) << "BlockEvent:ChooseTarget4Recover "
          << "src:" << src_path << " dns:" << ToJsonCompactString(dn_ids);
}

void BlockEventLogger::ChooseTarget4New(const std::string& src_path,
                                        const std::vector<uint32_t>& dn_ids) {
  VLOG(8) << "BlockEvent:ChooseTarget4New "
          << "src:" << src_path << " dns:" << ToJsonCompactString(dn_ids);
}

void BlockEventLogger::ChooseTarget4Read(
    BlockID blk_id, const std::vector<DatanodeID>& dn_ids) {
  VLOG(10) << "BlockEvent:ChooseTarget4Read "
           << "blk:" << blk_id << " dns:" << ToJsonCompactString(dn_ids);
}

void BlockEventLogger::DeleteReplica(BlockID blk_id, DatanodeID dn_id) {
  VLOG(8) << "BlockEvent:DeleteReplica "
          << "blk:" << blk_id << " dn:" << dn_id;
}

}  // namespace dancenn
