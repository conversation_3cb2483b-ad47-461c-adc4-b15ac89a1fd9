// Copyright 2019 livexmm <<EMAIL>>

#include "base/metrics.h"

#include <cnetpp/base/csonpp.h>

namespace dancenn {

uint32_t MetricsNameTable::GetOrCreate(const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  auto itr = name_to_id_.find(name);
  if (itr != name_to_id_.end()) {
    return itr->second;
  }
  id_to_name_.emplace_back(name);
  size_ = id_to_name_.size();
  uint32_t id = size_ - 1;
  name_to_id_.emplace(name, id);
  return id;
}

std::string MetricsNameTable::GetName(uint32_t id) {
  std::lock_guard<std::mutex> guard(mtx_);
  return id < id_to_name_.size() ? id_to_name_[id] : "";
}

std::once_flag                   Metrics::once_flag_;
pthread_key_t                    Metrics::tls_key_;

MetricsNameTable                 Metrics::histogram_names_;
LinkedCollections<Histogram>     Metrics::histograms_;
__thread Collections<Histogram>* Metrics::histogram_tls_;

MetricsNameTable                 Metrics::counter_names_;
LinkedCollections<Counter>       Metrics::counters_;
__thread Collections<Counter>*   Metrics::counter_tls_;

std::function<std::string()> Metrics::dynamic_tag_injector() {
  if (!dynamic_tag_injector_) {
    return MetricsCenter::global_dynamic_tag_injector();
  }
  return dynamic_tag_injector_;
}

std::string Metrics::GetCounterName(uint32_t id) {
  auto res = counter_names_.GetName(id);
  if (!res.empty()) {
    auto pos = res.find("#");
    if (pos != std::string::npos) {
      res = res.substr(pos + 1);
    }
  }
  return res;
}

std::string Metrics::GetHistogramName(uint32_t id) {
  auto res = histogram_names_.GetName(id);
  if (!res.empty()) {
    auto pos = res.find("#");
    if (pos != std::string::npos) {
      res = res.substr(pos + 1);
    }
  }
  return res;
}

std::string Metrics::GetGaugeFullName(const std::string& name) {
  std::string injected_tags = dynamic_tag_injector()();
  if (!injected_tags.empty()) {
    return name + "#" + injected_tags;
  } else {
    return name;
  }
}

std::string Metrics::GetAtomicCounterFullName(const std::string& name) {
  std::string injected_tags = dynamic_tag_injector()();
  if (!injected_tags.empty()) {
    return name + "#" + injected_tags;
  } else {
    return name;
  }
}

std::string Metrics::GetCounterFullName(uint32_t id) {
  std::string injected_tags = dynamic_tag_injector()();
  std::string name          = GetCounterName(id);
  if (!injected_tags.empty()) {
    return name + "#" + injected_tags;
  }
  return name;
}

std::string Metrics::GetHistogramFullName(uint32_t id) {
  std::string injected_tags = dynamic_tag_injector()();
  std::string name          = GetHistogramName(id);
  if (!injected_tags.empty()) {
    return name + "#" + injected_tags;
  }
  return name;
}

std::shared_ptr<Gauge> Metrics::RegisterGauge(const std::string& name,
    std::function<double()> getter) {
  std::lock_guard<std::mutex> guard(mtx_);
  auto itr = gauges_.find(name);
  if (itr != gauges_.end()) {
    return itr->second;
  }
  auto res = std::make_shared<Gauge>(name, getter);
  gauges_.emplace(name, res);
  return res;
}

void Metrics::DeregisterGauge(const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  gauges_.erase(name);
}

void Metrics::DeregisterGauge(std::shared_ptr<Gauge> gauge) {
  std::lock_guard<std::mutex> guard(mtx_);
  gauges_.erase(gauge->name());
}

std::shared_ptr<AtomicCounter> Metrics::RegisterAtomicCounter(
    const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  auto itr = atomic_counters_.find(name);
  if (itr != atomic_counters_.end()) {
    return itr->second;
  }
  auto res = std::make_shared<AtomicCounter>(name);
  atomic_counters_.emplace(name, res);
  return res;
}

void Metrics::DeregisterAtomicCounter(const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  atomic_counters_.erase(name);
}

void Metrics::DeregisterAtomicCounter(std::shared_ptr<AtomicCounter> counter) {
  std::lock_guard<std::mutex> guard(mtx_);
  atomic_counters_.erase(counter->name());
}

MetricID Metrics::RegisterHistogram(const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  uint32_t id = histogram_names_.GetOrCreate(prefix_ + "#" + name);
  histogram_ids_.emplace_back(id);
  return MetricID(id);
}

MetricID Metrics::RegisterCounter(const std::string& name) {
  std::lock_guard<std::mutex> guard(mtx_);
  uint32_t id = counter_names_.GetOrCreate(prefix_ + "#" + name);
  counter_ids_.emplace_back(id);
  return MetricID(id);
}

std::unordered_map<std::string, double> Metrics::AllGaugeSnapshots() {
  std::unordered_map<std::string, std::shared_ptr<Gauge>> t;
  {
    std::lock_guard<std::mutex> guard(mtx_);
    t = gauges_;
  }
  std::unordered_map<std::string, double> snapshots;
  for (auto& item : t) {
    snapshots.emplace(item.second->name(),
                      item.second->MakeSnapshot());
  }
  return snapshots;
}

std::unordered_map<std::string, int64_t> Metrics::AllAtomicCounterSnapshots() {
  std::unordered_map<std::string, std::shared_ptr<AtomicCounter>> t;
  {
    std::lock_guard<std::mutex> guard(mtx_);
    t = atomic_counters_;
  }
  std::unordered_map<std::string, int64_t> snapshots;
  for (auto& item : t) {
    snapshots.emplace(item.second->name(),
                      item.second->MakeSnapshot());
  }
  return snapshots;
}

std::unordered_map<uint32_t, int64_t> Metrics::AllCounterSnapshots() {
  std::unordered_map<uint32_t, int64_t> snapshots;
  for (uint32_t id : counter_ids_) {
    snapshots.emplace(id, counters_.Snapshot(id));
  }
  return snapshots;
}

std::unordered_map<uint32_t, std::shared_ptr<Histogram::Snapshot>>
Metrics::AllHistogramSnapshots() {
  std::unordered_map<uint32_t, std::shared_ptr<Histogram::Snapshot>> snapshots;
  for (uint32_t id : histogram_ids_) {
    snapshots.emplace(id, histograms_.Snapshot(id));
  }
  return snapshots;
}

std::shared_ptr<Metrics> MetricsCenter::RegisterMetrics(
    const std::string& prefix) {
  std::lock_guard<std::mutex> guard(mtx_);
  auto itr = all_metrics_.find(prefix);
  if (itr != all_metrics_.end()) {
    return itr->second;
  }
  auto res = Metrics::Create(prefix);
  all_metrics_[prefix] = res;
  return res;
}

std::unordered_map<std::string, std::shared_ptr<Metrics>>
  MetricsCenter::all_metrics() const {
    std::unordered_map<std::string, std::shared_ptr<Metrics>> res;
    std::lock_guard<std::mutex> guard(mtx_);
    res = all_metrics_;
    return res;
  }

MetricsCenter MetricsCenter::center_;
std::function<std::string()>
  MetricsCenter::global_dynamic_tag_injector_ = []() -> std::string {
    return "";
  };

void MetricsCenter::ToJson(std::string* result) const {
  CHECK_NOTNULL(result);
  cnetpp::base::Object results;
  ToJson(&results);

  cnetpp::base::Parser parser;
  parser.Serialize(cnetpp::base::Value(std::move(results)), result);
}

void MetricsCenter::ToJson(cnetpp::base::Object* results) const {
  CHECK_NOTNULL(results);
  auto allmetrics = all_metrics();
  for (auto& metrics : allmetrics) {
    cnetpp::base::Object sub_results;

    // gauges
    auto all_gauges = metrics.second->AllGaugeSnapshots();
    cnetpp::base::Object gauge_results;
    for (auto& gauge : all_gauges) {
      auto key = metrics.second->GetGaugeFullName(gauge.first);
      gauge_results[key] = gauge.second;
    }
    sub_results["gauges"] = gauge_results;

    // counters
    cnetpp::base::Object counter_results;
    auto all_atomic_counters = metrics.second->AllAtomicCounterSnapshots();
    for (auto& counter : all_atomic_counters) {
      auto key = metrics.second->GetAtomicCounterFullName(counter.first);
      counter_results[key] = counter.second;
    }
    auto all_counters = metrics.second->AllCounterSnapshots();
    for (auto& counter : all_counters) {
      auto key = metrics.second->GetCounterFullName(counter.first);
      counter_results[key] = counter.second;
    }
    sub_results["counters"] = counter_results;

    // histograms
    auto all_histograms = metrics.second->AllHistogramSnapshots();
    cnetpp::base::Object histogram_results;
    for (auto& histogram : all_histograms) {
      auto& s = histogram.second;
      cnetpp::base::Object h;
      h["max"] = s->Max();
      h["min"] = s->Min();
      h["avg"] = s->Avg();
      h["pct50"] = s->GetValue(0.5);
      h["pct75"] = s->GetValue(0.75);
      h["pct95"] = s->GetValue(0.95);
      h["pct99"] = s->GetValue(0.99);
      h["pct999"] = s->GetValue(0.999);
      auto key = metrics.second->GetHistogramFullName(histogram.first);
      histogram_results[key] = h;
    }
    sub_results["histograms"] = histogram_results;

    (*results)[metrics.first] = sub_results;
  }
}

}  // namespace dancenn
