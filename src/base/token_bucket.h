// Copyright 2018 <PERSON><PERSON> <<EMAIL>>

#include <algorithm>
#include <atomic>
#include <chrono>

namespace dancenn {

class TokenBucket {
 public:
  TokenBucket(double token_generate_rate, double burst);

  TokenBucket(TokenBucket&& that)
      : token_generate_rate_(that.token_generate_rate_),
        burst_(that.burst_),
        bucket_empty_time_(that.bucket_empty_time_.load()) {
  }

  bool Consume(double count);
  double ConsumePartial(double count);

  double Available() const {
    auto now = std::chrono::duration_cast<std::chrono::duration<double>>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    return std::min((now - bucket_empty_time_) * token_generate_rate_, burst_);
  }

  double token_generate_rate() const {
    return token_generate_rate_;
  }

  double burst() const {
    return burst_;
  }

 private:
  std::atomic<double> bucket_empty_time_;
  double token_generate_rate_;
  double burst_;
};

}  // namespace dancenn
