// Copyright 2019 <PERSON><PERSON> <<EMAIL>>

#include "base/audit_logger.h"

#include <gflags/gflags.h>
#include <glog/logging.h>
#include <stdarg.h>
#include <stdio.h>

#include <chrono>
#include <memory>
#include "base/databus_sender.h"

namespace dancenn {

std::unique_ptr<AuditLogger> AuditLogger::audit_logger_;

DatabusAuditLogger::DatabusAuditLogger(const std::string& channel_name) {
  databus_sender_ = std::make_unique<DatabusSender>("Auditlog", channel_name);
}

void AuditLogger::Init(std::unique_ptr<AuditLogger>&& audit_logger) {
  if (audit_logger_) {
    LOG(INFO) << "The audit logger is already initialized, "
              << "will override it.";
  }
  audit_logger_ = std::move(audit_logger);
}

void DatabusAuditLogger::Log(const char* fmt, va_list ap) {
  static const int kBufferLength = 2048;
  std::string buf(kBufferLength, 0);
  va_list dups_ap;
  va_copy(dups_ap, ap);
  int need = vsnprintf(&(buf[0]), kBufferLength, fmt, dups_ap);
  if (need > kBufferLength) {
    buf.resize(need + 1, 0);
    va_list dups2_ap;
    va_copy(dups2_ap, ap);
    need = vsnprintf(&(buf[0]), need + 1, fmt, dups2_ap);
    va_end(dups2_ap);
  }
  va_end(dups_ap);
  if (need != -1) {
    buf.resize(need);
    databus_sender_->Push(std::move(buf));
  }
}

void DatabusAuditLogger::Log(std::string&& s) {
  CHECK(!s.empty());
  databus_sender_->Push(std::move(s));
}


}  // namespace dancenn
