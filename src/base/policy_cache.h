// Copyright 2020 Xiong Mu <<EMAIL>>

#ifndef SRC_BASE_POLICY_CACHE_H_
#define SRC_BASE_POLICY_CACHE_H_

#include <algorithm>
#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "base/read_write_lock.h"

namespace dancenn {

template <typename PolicyType>
class PolicyCache {
 public:
  static std::string ToString(const PolicyType& policy) { return ""; }
  struct Node {
   public:
    Node(std::string path, PolicyType policy);

    ~Node();

    bool ParentOf(const std::string& path);
    bool ParentOf(Node* node);

   public:
    Node* parent_{nullptr};
    const std::string path_;
    std::vector<Node*> children_;
    PolicyType policy_;
  };
  using FilterFn = std::function<bool(const std::string, const PolicyType&)>;

 public:
  explicit PolicyCache(PolicyType default_policy);
  ~PolicyCache();

  void GetAncestorOrSelf(const std::string& path, PolicyType* policy);

  void UpdatePath(const std::string& path, PolicyType policy);

  void DeletePath(const std::string& path);

  // all path end with "/"
  std::vector<std::string> Filter(const FilterFn& fn);

 private:
  static std::string NormalizePath(const std::string& path);

  static std::string NoEndSlashPath(const std::string& path);

  Node* GetLowestAncestor(Node* root, const std::string& path);

  void FilterInternal(const Node* root, const FilterFn& fn,
                      std::vector<std::string>* v);

 private:
  ReadWriteLock rwlock_;
  Node* root_{nullptr};
  PolicyType default_policy_;
};
}  // namespace dancenn

namespace dancenn {

template <typename PolicyType>
PolicyCache<PolicyType>::Node::Node(std::string path, PolicyType policy)
    : parent_(nullptr), path_(std::move(path)), policy_(std::move(policy)) {}

template <typename PolicyType>
PolicyCache<PolicyType>::Node::~Node() {
  for (auto& ptr : children_) {
    delete ptr;
    ptr = nullptr;
  }
  children_.clear();
}

template <typename PolicyType>
bool PolicyCache<PolicyType>::Node::ParentOf(const std::string& path) {
  return path.find(path_) == 0;
}

template <typename PolicyType>
bool PolicyCache<PolicyType>::Node::ParentOf(Node* node) {
  return node->path_.find(path_) == 0;
}

template <typename PolicyType>
PolicyCache<PolicyType>::PolicyCache(PolicyType default_policy)
    : default_policy_(std::move(default_policy)) {
  root_ = new Node("", default_policy_);
}

template <typename PolicyType>
PolicyCache<PolicyType>::~PolicyCache() {
  delete root_;
}

template <typename PolicyType>
void PolicyCache<PolicyType>::GetAncestorOrSelf(const std::string& path,
                                                PolicyType* policy) {
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  auto node = GetLowestAncestor(root_, NormalizePath(path));
  *policy = node->policy_;
}

template <typename PolicyType>
void PolicyCache<PolicyType>::UpdatePath(const std::string& path,
                                         PolicyType policy) {
  auto n_path = NormalizePath(path);

  std::unique_lock<ReadWriteLock> guard(rwlock_);
  auto parent = GetLowestAncestor(root_, n_path);
  if (parent->path_ == n_path) {
    parent->policy_ = std::move(policy);
    return;
  }

  auto node = new Node(n_path, std::move(policy));
  node->parent_ = parent;

  for (auto& child : parent->children_) {
    if (node->ParentOf(child)) {
      child->parent_ = node;
      node->children_.emplace_back(child);
    }
  }

  parent->children_.erase(
      std::remove_if(
          parent->children_.begin(), parent->children_.end(),
          [&](const Node* child) { return child->parent_ != parent; }),
      parent->children_.end());

  parent->children_.emplace_back(node);
}

template <typename PolicyType>
void PolicyCache<PolicyType>::DeletePath(const std::string& path) {
  auto n_path = NormalizePath(path);
  std::unique_lock<ReadWriteLock> guard(rwlock_);

  Node* node = GetLowestAncestor(root_, n_path);
  if (node->path_ != n_path) {
    return;
  }

  auto parent = node->parent_;
  parent->children_.erase(
      std::remove_if(parent->children_.begin(), parent->children_.end(),
                     [&](const Node* n) { return n->path_ == n_path; }),
      parent->children_.end());

  for (auto& child : node->children_) {
    child->parent_ = parent;
    parent->children_.emplace_back(child);
    child = nullptr;
  }

  delete node;
}

template <typename PolicyType>
void PolicyCache<PolicyType>::FilterInternal(const Node* root,
                                             const FilterFn& fn,
                                             std::vector<std::string>* v) {
  auto path = NoEndSlashPath(root->path_);
  if (fn(path, root->policy_)) {
    v->emplace_back(path);
  }
  for (size_t i = 0; i < root->children_.size(); i++) {
    FilterInternal(root->children_[i], fn, v);
  }
}

template <typename PolicyType>
std::vector<std::string> PolicyCache<PolicyType>::Filter(const FilterFn& fn) {
  std::vector<std::string> v;
  std::shared_lock<ReadWriteLock> guard(rwlock_);
  for (size_t i = 0; i < root_->children_.size(); i++) {
    FilterInternal(root_->children_[i], fn, &v);
  }
  return v;
}

template <typename PolicyType>
std::string PolicyCache<PolicyType>::NormalizePath(const std::string& path) {
  if (path.empty() || path[path.size() - 1] == '/') {
    return path;
  }
  return path + "/";
}

template <typename PolicyType>
std::string PolicyCache<PolicyType>::NoEndSlashPath(const std::string& path) {
  if (path.size() <= 1) {
    return path;
  }
  return path.substr(0, path.size() - 1);
}

template <typename PolicyType>
typename PolicyCache<PolicyType>::Node*
PolicyCache<PolicyType>::GetLowestAncestor(Node* root,
                                           const std::string& path) {
  if (root->path_ == path) {
    return root;
  }

  for (auto child : root->children_) {
    if (child->ParentOf(path)) {
      if (child->path_ == root->path_) {
        // debug
        abort();
      }
      return GetLowestAncestor(child, path);
    }
  }

  return root;
}
}

#endif  // SRC_BASE_POLICY_CACHE_H_
