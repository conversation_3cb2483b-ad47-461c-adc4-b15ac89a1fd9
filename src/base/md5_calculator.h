// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_MD5_CALCULATOR_H_
#define BASE_MD5_CALCULATOR_H_

#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>
#include <openssl/md5.h>
#include <openssl/evp.h>

#include <string>

#include "base/file_utils.h"

namespace dancenn {

class MD5 {
 public:
  MD5() {
    memset(data_, 0, MD5_DIGEST_LENGTH);
  }

  explicit MD5(cnetpp::base::StringPiece data) {
    CHECK_EQ(data.size(), MD5_DIGEST_LENGTH);
    memcpy(data_, data.data(), data.size());
  }

  bool EqualTo(const MD5& that) const {
    return !memcmp(data_, that.data_, MD5_DIGEST_LENGTH);
  }

  std::string ToLowerCase();
  std::string ToUpperCase();

  bool FromString(cnetpp::base::StringPiece str);

  const uint8_t* data() const {
    return data_;
  }

  uint8_t* mutable_data() {
    return data_;
  }

 private:
  std::string ToString(const char* table);

  static const char kLowerCaseHex[MD5_DIGEST_LENGTH];
  static const char kUpperCaseHex[MD5_DIGEST_LENGTH];

  uint8_t data_[MD5_DIGEST_LENGTH];
};

inline bool operator==(const MD5& lhs, const MD5& rhs) {
  return lhs.EqualTo(rhs);
}

class MD5Calculator {
 public:
  MD5Calculator() {
    Reset();
  }
  explicit MD5Calculator(cnetpp::base::StringPiece data) {
    Reset();
    Update(data);
  }
  ~MD5Calculator() {
    if (init_) {
      EVP_MD_CTX_destroy(md_ctx_);
    }
  }

  void Update(cnetpp::base::StringPiece data);
  bool Update(const std::string& file, size_t block_size);

  MD5 Digest();

  void Reset();

 private:
  void Final();

  // the context
  EVP_MD_CTX *md_ctx_;

  // calculate finished ?
  bool finished_;

  // message digest
  MD5 md5_;

  unsigned int md_len_;
  bool init_ = false;
};

}  // namespace dancenn

#endif  // BASE_MD5_CALCULATOR_H_

