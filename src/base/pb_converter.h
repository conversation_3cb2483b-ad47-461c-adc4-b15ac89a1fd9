// Copyright 2017 <PERSON><PERSON> <<EMAIL>>

#ifndef BASE_PB_CONVERTER_H_
#define BASE_PB_CONVERTER_H_

#include <cnetpp/base/csonpp.h>
#include <google/protobuf/message.h>

namespace dancenn {

class PBConverter {
 public:
  static cnetpp::base::Object ToJson(const google::protobuf::Message& message);
  static std::string ToCompactJsonString(
      const google::protobuf::Message& message);
};

}  // namespace dancenn

#endif  // BASE_PB_CONVERTER_H_
