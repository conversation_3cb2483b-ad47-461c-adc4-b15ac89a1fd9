//
// Created by bytedance on 2020/5/9.
//
#ifndef SECURITY_KEY_MANAGER_LOCAL_H
#define SECURITY_KEY_MANAGER_LOCAL_H

#include "key_manager.h"

namespace dancenn {

class KeyManagerLocal : public KeyManager {
 public:
  explicit KeyManagerLocal(std::shared_ptr<DatanodeManager> dn_manager)
      : KeyManager(std::move(dn_manager)) {
    LOG(INFO) << "Construct KeyManagerLocal";
  }

  std::string GenerateSecret() override {
    auto now = std::chrono::system_clock::now();
    auto epoch = static_cast<uint64_t>(
        std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch())
            .count());
    auto key_id_ = (epoch << 32U) + key_seq_num_.fetch_add(1);

    return "ThisIsATestKeyValueForBlockAccessToken" + std::to_string(key_id_);
  }

 private:
  std::atomic<uint32_t> key_seq_num_{0};
};

}  // namespace dancenn

#endif  // SECURITY_KEY_MANAGER_LOCAL_H