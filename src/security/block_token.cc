//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#include "block_token.h"

#include "block_token_identifier.h"
#include "block_token_secret_manager.h"

namespace dancenn {

BlockToken::BlockToken(
    const std::shared_ptr<BlockTokenIdentifier>& block_token_identifier,
    const std::shared_ptr<BlockTokenSecretManager>&
        block_token_secret_manager) {
  password_ =
      block_token_secret_manager->CreatePassword(block_token_identifier);
  // @note set identifier after CreatePassword, expiry date and key id
  // will be added when creating password
  identifier_ = block_token_identifier->GetBytes();
  kind_ = block_token_identifier->GetKind();
  service_ = "";
}

bool BlockToken::IsEmpty() const {
  return identifier_.empty() && password_.empty() && kind_.empty() &&
         service_.empty();
}

std::string BlockToken::ToString() const {
  std::stringstream ss;
  ss << "Token(identifier:" << identifier_ << ", password:" << password_
     << ", kind:" << kind_ << ", service:" << service_ << ')';
  return ss.str();
}

}  // namespace dancenn