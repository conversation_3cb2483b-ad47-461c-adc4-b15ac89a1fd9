//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#ifndef SECURITY_BLOCK_TOKEN_H
#define SECURITY_BLOCK_TOKEN_H

#pragma once

#include <memory>
#include <string>

namespace dancenn {

class BlockTokenSecretManager;
class BlockTokenIdentifier;

class BlockToken {
 public:
  BlockToken() = default;

  BlockToken(
      const std::shared_ptr<BlockTokenIdentifier>& block_token_identifier,
      const std::shared_ptr<BlockTokenSecretManager>&
          block_token_secret_manager);

  const std::string& GetIdentifier() const {
    return identifier_;
  }
  const std::string& GetPassword() const {
    return password_;
  }
  const std::string& GetKind() const {
    return kind_;
  }
  const std::string& GetService() const {
    return service_;
  }

  bool IsEmpty() const;

  std::string ToString() const;

 private:
  std::string identifier_{""};
  std::string password_{""};
  std::string kind_{""};
  std::string service_{""};
};

}  // namespace dancenn

#endif  // SECURITY_BLOCK_TOKEN_H