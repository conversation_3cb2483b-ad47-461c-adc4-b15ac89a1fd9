//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#ifndef SECURITY_BLOCK_TOKEN_SECRET_MANAGER_H
#define SECURITY_BLOCK_TOKEN_SECRET_MANAGER_H

#pragma once

#include <memory>
#include <string>

#include "key_manager.h"

namespace dancenn {

class Block;
class BlockToken;
class BlockTokenIdentifier;
enum class AccessMode;

/**
 * In DanceNN, active namenode and standby namenode shares the same block
 * key, thus BlockTokenSecretManager no longer needs to be instantiated in 2
 * modes. BlockTokenSecretManager is always used in slave mode.
 */
class BlockTokenSecretManager
    : public std::enable_shared_from_this<BlockTokenSecretManager> {
 public:
  explicit BlockTokenSecretManager(const std::string& blockpool_id,
                                   std::shared_ptr<KeyManager> key_manager);

  std::shared_ptr<BlockToken> GenerateToken(
      const std::string& user,
      uint64_t block_id,
      const std::vector<AccessMode>& modes);

  std::string CreatePassword(
      const std::shared_ptr<BlockTokenIdentifier>& identifier);

  std::shared_ptr<BlockTokenSecretManager> GetSelfSharedPtr() {
    return shared_from_this();
  }

  std::string HmacEncode(const std::string& algo,
                         const std::string& key,
                         const std::string& input);

 protected:
  // May add new encryption algorithm by inheritance
  std::string CreatePasswordInternal(const std::string& identifier_str,
                                     const std::string& key);

 private:
  std::shared_ptr<KeyManager> key_manager_;
  int64_t key_update_interval_ms_{0};
  int64_t token_life_time_ms_{0};
  // get blockpool_id_ from namespace
  std::string blockpool_id_{""};
  std::string encryption_algorithm_{""};

  std::mutex mutex_;

  int HmacEncode(const char* algo,
                 const char* key,
                 unsigned int key_length,
                 const char* input,
                 unsigned int input_length,
                 unsigned char** output,
                 unsigned int* output_length);
};
}  // namespace dancenn

#endif  // SECURITY_BLOCK_TOKEN_SECRET_MANAGER_H
