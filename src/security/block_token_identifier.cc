//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#include "block_token_identifier.h"

#include <glog/logging.h>

#include "writable_utils.h"

namespace dancenn {

const std::string BlockTokenIdentifier::KIND_NAME = "HDFS_BLOCK_TOKEN";

const std::string& AccessModeToStr(const AccessMode& access_mode) {
  size_t index = static_cast<size_t>(access_mode);
  if (index < AccessModeStrs.size()) {
    return AccessModeStrs[index];
  } else {
    return AccessModeStrs[4];
  }
}

BlockTokenIdentifier::BlockTokenIdentifier(const std::string& user,
                                           const std::string& blockpool_id,
                                           uint64_t block_id,
                                           const std::vector<AccessMode>& modes)
    : user_(user),
      blockpool_id_(blockpool_id),
      block_id_(block_id) {
  for (const auto& mode : modes) {
    access_modes_str_.emplace_back(AccessModeToStr(mode));
  }
}

std::string BlockTokenIdentifier::ToString() const {
  std::stringstream ss;
  ss << "block_token_identifier (expiryDate="
     << std::to_string(GetExpiryDate()) + ", keyId="
     << std::to_string(GetKeyId()) << ", user=" << GetUser()
     << ", blockPoolId=" << GetBlockpoolId()
     << ", blockId=" << std::to_string(GetBlockId());

  ss << ", access modes=";
  for (size_t i = 0; i < access_modes_str_.size(); i++) {
    if (i != 0) {
      ss << ',';
    }
    ss << access_modes_str_[i];
  }
  ss << ')';

  return ss.str();
}

bool BlockTokenIdentifier::Equals(
    const BlockTokenIdentifier& block_token_identifier) {
  if (&block_token_identifier == this) {
    return true;
  }
  return block_token_identifier.GetExpiryDate() == expiry_date_ &&
         block_token_identifier.GetKeyId() == key_id_ &&
         block_token_identifier.GetUser() == user_ &&
         block_token_identifier.GetBlockpoolId() == blockpool_id_ &&
         block_token_identifier.GetBlockId() == block_id_ &&
         block_token_identifier.GetAccessModesStr() == access_modes_str_;
}

bool BlockTokenIdentifier::IsEmpty() {
  return expiry_date_ == 0 && key_id_ == 0 && user_.empty() &&
         blockpool_id_.empty() && block_id_ == 0 && access_modes_str_.empty();
}

std::string BlockTokenIdentifier::GetBytes() const {
  std::string res = WriteVLong(expiry_date_);
  res += WriteVInt(key_id_);
  res += WriteString(user_);
  res += WriteString(blockpool_id_);
  res += WriteVLong(block_id_);
  res += WriteVInt(access_modes_str_.size());
  for (const std::string& mode_str : access_modes_str_) {
    res += WriteVString(mode_str);
  }
  return res;
}

// copy from dancedn, for test only
bool BlockTokenIdentifier::ReadFields(const std::string& stream) {
    LOG(INFO) << "receive token identifier bytes:" << StrToHexString(stream);
    int position = 0;
    auto success = ReadVLong(stream, &position, &expiry_date_);
    if (!success) {
        LOG(WARNING) << "failed to call ReadVLong for expiry_data,"
                << "current status:" << ToString();
        return false;
    }
    success = ReadVInt(stream, &position, &key_id_);
    if (!success) {
        LOG(WARNING) << "failed to call ReadVInt for key_id,"
                << "current status:" << ToString();
        return false;
    }
    success = ReadString(stream, &position, &user_);
    if (!success) {
        LOG(WARNING) << "failed to call ReadString for user,"
                << "current status:" << ToString();
        return false;
    }
    success = ReadString(stream, &position, &blockpool_id_);
    if (!success) {
        LOG(WARNING) << "failed to call ReadString for bp_id,"
                << "current status:" << ToString();
        return false;
    }
    int64_t temp_block_id = 0;
    success = ReadVLong(stream, &position, &temp_block_id);
    if (!success) {
        LOG(WARNING) << "failed to call ReadVLong for block_id,"
                << "current status:" << ToString();
        return false;
    }
    if (temp_block_id < 0) {
        LOG(WARNING) << "error! get minus block_id,"
                << "current status:" << ToString();
        return false;
    }
    block_id_ = static_cast<uint64_t>(temp_block_id);
    int32_t len = 0;
    // maybe ReadVIntInRange?
    success = ReadVInt(stream, &position, &len);
    if (!success) {
        LOG(WARNING) << "failed to call ReadVInt for access_modes length,"
                     << "current status:" << ToString();
        return false;
    }
    /// simplified from java version hdfs, so maybe problem?
    /// (lwj)byte array unit test passed
    for (int32_t i = 0; i < len; ++i) {
        std::string temp_result;
        success = ReadVString(stream, &position, &temp_result);
        if (!success) {
            LOG(WARNING) << "failed to call ReadString for access_mode,"
                    << "current status:" << ToString();
            return false;
        }
        access_modes_str_.emplace_back(temp_result);
    }
    return true;
}

}  // namespace dancenn