//
// Created by bytedance on 2020/5/7.
//
#ifndef SECURITY_KEY_MANAGER_H
#define SECURITY_KEY_MANAGER_H

#include <ClientNamenodeProtocol.pb.h>
#include <cnetpp/concurrency/task.h>
#include <cnetpp/concurrency/thread.h>
#include <glog/logging.h>

#include <memory>
#include <shared_mutex>
#include <string>

#include "base/read_write_lock.h"
#include "base/refresher.h"
#include "datanode_manager/datanode_manager.h"

namespace dancenn {

struct BlockKey {
 public:
  BlockKey() = default;

  BlockKey(uint64_t id, uint64_t expiry_date, std::string value)
      : id_(id), expiry_date_(expiry_date), value_(std::move(value)) {}

  uint64_t id() const { return id_; }
  uint64_t expiry_date() const { return expiry_date_; }
  std::string value() const { return value_; }
  bool IsEmpty() const {
    return id_ == 0 && expiry_date_ == 0 && value_ == "";
  }

  void ToProto(cloudfs::BlockKeyProto* key_proto) const;

 private:
  uint64_t id_{0};
  uint64_t expiry_date_{0};
  std::string value_{""};
};

class KeyManager {
 public:
  explicit KeyManager(std::shared_ptr<DatanodeManager> dn_manager);
  virtual ~KeyManager();

  void Start();
  void Stop();

  void RefreshKeys();

  std::unordered_map<int32_t, BlockKey> GetKeys();
  void GetExportKeys(cloudfs::ExportedBlockKeysProto* keys_proto);
  const BlockKey& GetCurrentKey();
  int64_t GetTokenLifeTimeMs();

 protected:
  virtual std::string GenerateSecret() = 0;

 private:
  void InitKeys(int64_t now);

  void RefreshKeysInternal(int64_t now);
  void RemoveExpiredKeys(int64_t now);
  void SetSerialNumber(int32_t serial_number);

  std::mutex mutex_;

  std::unique_ptr<Refresher> refresher_;
  int64_t last_block_key_update_ms_;
  int64_t last_datanode_update_key_ms_;

  int64_t key_update_interval_ms_;
  int64_t token_life_time_ms_;
  int64_t datanode_update_interval_ms_;

 private:
  int32_t serial_number_;
  std::unordered_map<int32_t, BlockKey> all_keys_;
  BlockKey current_key_;
  BlockKey next_key_;

 private:
  std::shared_ptr<DatanodeManager> dn_manager_;
};

class KeyManagerFactory {
 public:
  KeyManagerFactory() = delete;
  ~KeyManagerFactory() = delete;

  static std::shared_ptr<KeyManager> Create(const std::string& policy,
                                            std::shared_ptr<DatanodeManager> dn_manager_);
};

}  // namespace dancenn

#endif  // SECURITY_KEY_MANAGER_H
