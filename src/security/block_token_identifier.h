//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#ifndef SECURITY_BLOCK_TOKEN_IDENTIFIER_H
#define SECURITY_BLOCK_TOKEN_IDENTIFIER_H

#pragma once

#include <string>
#include <vector>

namespace dancenn {

enum class AccessMode {
  READ = 0,
  WRITE = 1,
  COPY = 2,
  REPLACE = 3,
  UNKNOWN = 4
};
const std::vector<std::string> AccessModeStrs = {"READ",
                                                 "WRITE",
                                                 "COPY",
                                                 "REPLACE",
                                                 "UNKNOWN"};
const std::string& AccessModeToStr(const AccessMode& access_mode);

class BlockTokenIdentifier {
 public:
  BlockTokenIdentifier() = default;
  BlockTokenIdentifier(const std::string& user,
                       const std::string& blockpool_id,
                       uint64_t block_id,
                       const std::vector<AccessMode>& modes);

  const std::string& GetKind() const {
    return KIND_NAME;
  }
  int64_t GetExpiryDate() const {
    return expiry_date_;
  }
  int32_t GetKeyId() const {
    return key_id_;
  }
  const std::string& GetUser() const {
    return user_;
  }
  const std::string& GetBlockpoolId() const {
    return blockpool_id_;
  }
  uint64_t GetBlockId() const {
    return block_id_;
  }
  std::vector<std::string> GetAccessModesStr() const {
    return access_modes_str_;
  }

  void SetExpiryDate(int64_t expiry_date) {
    expiry_date_ = expiry_date;
  }
  void SetKeyId(const uint64_t& key_id) {
    key_id_ = static_cast<int32_t>(key_id);
  }
  void SetUser(const std::string& user) {
    user_ = user;
  }

  std::string ToString() const;
  bool Equals(const BlockTokenIdentifier& block_token_identifier);
  bool IsEmpty();
  // @note: The function will be used to generate tokens, and the format
  // needs to be strictly consistent.
  std::string GetBytes() const;
  bool ReadFields(const std::string& stream);

 private:
  // @note: Must be sure to keep the variable type exactly the same as
  // the one in dn. Different variable types may lead to wrong decoding.
  int64_t expiry_date_{0};
  int32_t key_id_{0};
  std::string user_{""};
  std::string blockpool_id_{""};
  uint64_t block_id_{0};
  std::vector<std::string> access_modes_str_{};

  static const std::string KIND_NAME;
};

}  // namespace dancenn

#endif  // SECURITY_BLOCK_TOKEN_IDENTIFIER_H