//
// Copyright (c) 2023 Bytedance Inc. All rights reserved.
//
#include "block_token_secret_manager.h"

#include <cassert>
#include <glog/logging.h>

#include "block_manager/block.h"
#include "block_token.h"
#include "block_token_identifier.h"
#include "key_manager.cc"
#include "openssl/hmac.h"

DECLARE_int64(security_key_update_interval_ms);
DECLARE_int64(security_key_token_life_time);
DECLARE_string(security_block_token_encryption_algorithm);

namespace dancenn {

BlockTokenSecretManager::BlockTokenSecretManager(
    const std::string& blockpool_id,
    std::shared_ptr<KeyManager> key_manager)
    : blockpool_id_(blockpool_id), key_manager_(key_manager) {
  key_update_interval_ms_ = FLAGS_security_key_update_interval_ms;
  token_life_time_ms_ = FLAGS_security_key_token_life_time;
  encryption_algorithm_ = FLAGS_security_block_token_encryption_algorithm;
}

std::shared_ptr<BlockToken> BlockTokenSecretManager::GenerateToken(
    const std::string& user,
    uint64_t block_id,
    const std::vector<AccessMode>& modes) {
  VLOG(10) << "Creating Block Token for block: " << block_id;

  std::shared_ptr<BlockTokenIdentifier> identifier =
      std::make_shared<BlockTokenIdentifier>(
          user, blockpool_id_, block_id, modes);
  return std::make_shared<BlockToken>(identifier, GetSelfSharedPtr());
}

std::string BlockTokenSecretManager::CreatePassword(
    const std::shared_ptr<BlockTokenIdentifier>& identifier) {
  std::lock_guard<std::mutex> guard(mutex_);

  const BlockKey& key = key_manager_->GetCurrentKey();
  if (key.IsEmpty()) {
    LOG(ERROR) << "Creating Block Token password with an invalid current block key";
    return "";
  }
  identifier->SetExpiryDate(GetNow() + token_life_time_ms_);
  identifier->SetKeyId(key.id());
  VLOG(10) << "Generating block token password for " << identifier->ToString();
  return CreatePasswordInternal(identifier->GetBytes(), key.value());
}

std::string BlockTokenSecretManager::CreatePasswordInternal(
    const std::string& identifier_str,
    const std::string& key) {
  return HmacEncode(encryption_algorithm_, key, identifier_str);
}

std::string BlockTokenSecretManager::HmacEncode(const std::string& algo,
                                                const std::string& key,
                                                const std::string& input) {
  const char* calgo = algo.c_str();
  const char* ckey = key.c_str();
  unsigned int key_len = key.size();
  const char* cinput = input.c_str();
  unsigned int input_len = input.size();
  unsigned char* output = nullptr;
  unsigned int output_length = 0;

  output = (unsigned char*)malloc(EVP_MAX_MD_SIZE);

  auto res = HmacEncode(
      calgo, ckey, key_len, cinput, input_len, &output, &output_length);
  if (res == -1) {
    free(output);
    return "";
  }
  std::string ans;
  for (unsigned int i = 0; i < output_length; ++i) {
    ans += output[i];
  }
  free(output);
  return ans;
}

int BlockTokenSecretManager::HmacEncode(const char* algo,
                                        const char* key,
                                        unsigned int key_length,
                                        const char* input,
                                        unsigned int input_length,
                                        unsigned char** output,
                                        unsigned int* output_length) {
  const EVP_MD* engine = nullptr;
  if (strcasecmp("sha512", algo) == 0) {
    engine = EVP_sha512();
  } else if (strcasecmp("sha256", algo) == 0) {
    engine = EVP_sha256();
  } else if (strcasecmp("sha1", algo) == 0) {
    engine = EVP_sha1();
  } else if (strcasecmp("md5", algo) == 0) {
    engine = EVP_md5();
  } else if (strcasecmp("sha224", algo) == 0) {
    engine = EVP_sha224();
  } else if (strcasecmp("sha384", algo) == 0) {
    engine = EVP_sha384();
  } else {
    return -1;
  }

  HMAC_CTX ctx;
  HMAC_CTX_init(&ctx);
  HMAC_Init_ex(&ctx, key, strlen(key), engine, nullptr);
  HMAC_Update(&ctx, (unsigned char*)input, input_length);
  HMAC_Final(&ctx, *output, output_length);
  HMAC_CTX_cleanup(&ctx);
  return 0;
}

}  // namespace dancenn
