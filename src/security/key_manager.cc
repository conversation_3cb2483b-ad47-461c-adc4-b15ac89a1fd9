//
// Created by bytedance on 2020/5/7.
//
#include "key_manager.h"

#include <gflags/gflags.h>
#include <glog/logging.h>

#include "key_manager_local.h"

DECLARE_bool(security_key_enable);
DECLARE_int64(security_key_update_interval_ms);
DECLARE_int64(security_key_token_life_time);
DECLARE_int64(security_key_datanode_update_interval_ms);

namespace dancenn {

int64_t GetNow() {
  return static_cast<int64_t>(
      std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now().time_since_epoch())
          .count());
}

std::shared_ptr<KeyManager> KeyManagerFactory::Create(const std::string& model,
                                                      std::shared_ptr<DatanodeManager> dn_manager_) {
  if (model == "local") {
    return std::shared_ptr<KeyManager>(
        new KeyManagerLocal(std::move(dn_manager_)));
  } else {
    LOG(ERROR) << "Unknown key manager model: " << model;
    return nullptr;
  }
}

void BlockKey::ToProto(cloudfs::BlockKeyProto* key_proto) const {
  key_proto->set_keyid(this->id());
  key_proto->set_expirydate(this->expiry_date());
  key_proto->set_keybytes(this->value());
}

KeyManager::KeyManager(std::shared_ptr<DatanodeManager> dn_manager)
    : dn_manager_(std::move(dn_manager)) {
  CHECK_GE(FLAGS_security_key_update_interval_ms, 0);
  CHECK_GE(FLAGS_security_key_token_life_time, 0);
  key_update_interval_ms_ = FLAGS_security_key_update_interval_ms;
  token_life_time_ms_ = FLAGS_security_key_token_life_time;
  datanode_update_interval_ms_ = FLAGS_security_key_datanode_update_interval_ms;
  LOG(INFO) << "key_update_interval_ms_=" << key_update_interval_ms_;
  LOG(INFO) << "token_life_time_ms_=" << token_life_time_ms_;
  LOG(INFO) << "datanode_update_interval_ms=" << datanode_update_interval_ms_;
}

KeyManager::~KeyManager() {
  LOG(INFO) << "Stop KeyManager";
  Stop();
}

void KeyManager::Start() {
  auto now = GetNow();

  // ad-hoc use timestamp
  serial_number_ = static_cast<int32_t>(now);
  LOG(INFO) << "serial_number_=" << serial_number_;

  InitKeys(now);
  last_block_key_update_ms_ = now;
  last_datanode_update_key_ms_ = now;

  refresher_ = std::make_unique<Refresher>("block-key-refresher",
                                           [&]() { RefreshKeys(); });

  CHECK(refresher_);
  refresher_->Start();
}

void KeyManager::Stop() {
  if (refresher_) {
    refresher_->Stop();
    refresher_.reset();
  }
}

void KeyManager::RefreshKeys() {
  if (!FLAGS_security_key_enable) {
    return;
  }
  auto now = GetNow();
  if (now - last_block_key_update_ms_ >= key_update_interval_ms_) {
    RefreshKeysInternal(now);
    dn_manager_->MarkKeyUpdate();
    last_datanode_update_key_ms_ = now;
  }
  if (now - last_datanode_update_key_ms_ >= datanode_update_interval_ms_) {
    dn_manager_->MarkKeyUpdate();
    last_datanode_update_key_ms_ = now;
  }
}

void KeyManager::RefreshKeysInternal(int64_t now) {
  std::lock_guard<std::mutex> guard(mutex_);

  LOG(INFO) << "Updating block keys";
  RemoveExpiredKeys(now);

  // set final expiry date of retiring currentKey
  all_keys_.emplace(current_key_.id(), BlockKey(current_key_.id(),
                                                now + key_update_interval_ms_ +
                                                    token_life_time_ms_,
                                                current_key_.value()));
  // update the estimated expiry date of new currentKey
  current_key_ = BlockKey(
      next_key_.id(), now + 2 * key_update_interval_ms_ + token_life_time_ms_,
      next_key_.value());
  all_keys_.emplace(current_key_.id(), current_key_);
  // generate a new nextKey
  SetSerialNumber(serial_number_ + 1);
  next_key_ = BlockKey(serial_number_,
                       now + 3 * key_update_interval_ms_ + token_life_time_ms_,
                       GenerateSecret());
  all_keys_.emplace(next_key_.id(), next_key_);
  LOG(INFO) << "next serial_number_=" << serial_number_;

  last_block_key_update_ms_ = now;
}

void KeyManager::InitKeys(int64_t now) {
  std::lock_guard<std::mutex> guard(mutex_);

  SetSerialNumber(serial_number_ + 1);
  current_key_ =
      BlockKey(serial_number_,
               now + 2 * key_update_interval_ms_ + token_life_time_ms_,
               GenerateSecret());
  SetSerialNumber(serial_number_ + 1);
  next_key_ = BlockKey(serial_number_,
                       now + 3 * key_update_interval_ms_ + token_life_time_ms_,
                       GenerateSecret());
  all_keys_.emplace(current_key_.id(), current_key_);
  all_keys_.emplace(next_key_.id(), next_key_);

  LOG(INFO) << "next serial_number_=" << serial_number_;
}

void KeyManager::RemoveExpiredKeys(int64_t now) {
  for (auto iter = all_keys_.begin(); iter != all_keys_.end();) {
    auto& key = iter->second;
    if (key.expiry_date() > now) {
      iter++;
    } else {
      iter = all_keys_.erase(iter);
    }
  }
}

void KeyManager::SetSerialNumber(int32_t serial_number) {
  serial_number_ = serial_number;
}

void KeyManager::GetExportKeys(
    cloudfs::ExportedBlockKeysProto* keys_proto) {
  std::lock_guard<std::mutex> guard(mutex_);

  keys_proto->set_isblocktokenenabled(true);
  keys_proto->set_keyupdateinterval(key_update_interval_ms_);
  keys_proto->set_tokenlifetime(token_life_time_ms_);
  current_key_.ToProto(keys_proto->mutable_currentkey());
  for (auto& pair:all_keys_) {
    auto& key = pair.second;
    key.ToProto(keys_proto->add_allkeys());
  }
}

std::unordered_map<int32_t, BlockKey> KeyManager::GetKeys() {
  std::lock_guard<std::mutex> guard(mutex_);

  return all_keys_;
}

const BlockKey& KeyManager::GetCurrentKey() {
  return current_key_;
}

int64_t KeyManager::GetTokenLifeTimeMs() {
  return token_life_time_ms_;
}

}  // namespace dancenn