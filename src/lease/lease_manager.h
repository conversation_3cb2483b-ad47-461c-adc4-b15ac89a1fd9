// Copyright 2017 Lei Li <<EMAIL>>

#ifndef LEASE_LEASE_MANAGER_H_
#define LEASE_LEASE_MANAGER_H_

#include <cnetpp/concurrency/thread_pool.h>
#include <glog/logging.h>

#include <chrono>
#include <cstdint>
#include <ctime>
#include <functional>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/read_write_lock.h"
#include "base/status.h"
#include "lease/lease.h"
#include "lease/lease_manager_base.h"
#include "lease/lease_metrics.h"
#include "namespace/inode.h"

DECLARE_uint64(lease_expired_hard_limit_ms);
namespace dancenn {

class Lease;
class MetaStorage;

// LeaseManager only maintain the in-memory heartbeat about client name, and the
// lease monitor scan all lease according to persistence data.

const int CACHELINE_SIZE = 64;

class __attribute__((aligned(CACHELINE_SIZE))) LeaseSlice {
 public:
  LeaseSlice() : id_(0) {
  }
  ~LeaseSlice() = default;

  LeaseSlice(const LeaseSlice&) = delete;
  LeaseSlice& operator=(const LeaseSlice&) = delete;

  void SetId(int id) {
    id_ = id;
  }

  friend bool operator<(const LeaseSlice& left, const LeaseSlice& right) {
    return left.id_ < right.id_;
  }

  friend bool operator==(const LeaseSlice& left, const LeaseSlice& right) {
    return left.id_ == right.id_;
  }

  void lock_shared() {
    rwlock_.lock_shared();
  }
  void unlock_shared() {
    rwlock_.unlock_shared();
  }
  void lock() {
    rwlock_.lock();
  }
  void unlock() {
    rwlock_.unlock();
  }

  void lock_shared() const {
    rwlock_.lock_shared();
  }
  void unlock_shared() const {
    rwlock_.unlock_shared();
  }
  void lock() const {
    rwlock_.lock();
  }
  void unlock() const {
    rwlock_.unlock();
  }

  ReadWriteLock& GetLock() const {
    return rwlock_;
  }

  void ClearLeases() {
    holder_leases_.clear();
    sorted_leases_.clear();
  }

  std::shared_ptr<Lease> AddLease(const std::string& holder, uint64_t inode_id);

  std::shared_ptr<Lease> GetLease(const std::string& holder) const;

  void RenewLease(const std::shared_ptr<Lease>& lease,
                  const std::string& holder_ip);

  bool RemoveLease(const std::shared_ptr<Lease>& lease, uint64_t inode_id);

  void RenewAllLeases();

  bool CheckLease(const std::string& holder, uint64_t inode_id);

  const std::set<std::shared_ptr<Lease>, Lease::LeaseLessComparator>&
  GetSortedLeases() const {
    return sorted_leases_;
  }

  const std::unordered_map<std::string, std::shared_ptr<Lease>>&
  GetHolderLeases() const {
    return holder_leases_;
  }

  void UpdateEstimateSize() {
    holder_leases_size_.store(holder_leases_.size());
    sorted_leases_size_.store(sorted_leases_.size());
  }

  size_t EstimateHolderLeasesSize() const {
    return holder_leases_size_.load(std::memory_order::memory_order_relaxed);
  }

  size_t EstimateSortedLeasesSize() const {
    return sorted_leases_size_.load(std::memory_order::memory_order_relaxed);
  }

 private:
  mutable ReadWriteLock rwlock_;

  // identifier used to sort slice
  int id_;

  // Holders are generated by clients:
  // https://code.byted.org/inf/native_dfs_client/blob/e5d8a38d322480bcfd1fc55e71f5bf48a09460e2/src/client/FileSystemImpl.cpp#L121
  // e.g., libhdfs_client_random_20324_count_1_pid_1458_tid_1458
  std::unordered_map<std::string, std::shared_ptr<Lease>> holder_leases_;
  std::set<std::shared_ptr<Lease>, Lease::LeaseLessComparator> sorted_leases_;

  std::atomic<size_t> holder_leases_size_{0};
  std::atomic<size_t> sorted_leases_size_{0};
};

class __attribute__((aligned(CACHELINE_SIZE))) IndexSlice {
 public:
  IndexSlice() : id_(0) {
  }
  ~IndexSlice() = default;

  IndexSlice(const IndexSlice&) = delete;
  IndexSlice& operator=(const IndexSlice&) = delete;

  void lock_shared() {
    rwlock_.lock_shared();
  }
  void unlock_shared() {
    rwlock_.unlock_shared();
  }
  void lock() {
    rwlock_.lock();
  }
  void unlock() {
    rwlock_.unlock();
  }

  void lock_shared() const {
    rwlock_.lock_shared();
  }
  void unlock_shared() const {
    rwlock_.unlock_shared();
  }
  void lock() const {
    rwlock_.lock();
  }
  void unlock() const {
    rwlock_.unlock();
  }

  void SetId(int id) {
    id_ = id;
  }

  friend bool operator<(const IndexSlice& left, const IndexSlice& right) {
    return left.id_ < right.id_;
  }

  friend bool operator==(const IndexSlice& left, const IndexSlice& right) {
    return left.id_ == right.id_;
  }

  ReadWriteLock& GetLock() const {
    return rwlock_;
  }

  std::string GetLeaseHolder(uint64_t inode_id) const {
    const auto iter = files_leases_.find(inode_id);
    if (iter == files_leases_.end()) {
      return std::string();
    }
    return iter->second;
  }

  void ClearIndices() {
    files_leases_.clear();
  }

  void AddIndex(uint64_t inode_id, const std::string& holder) {
    files_leases_[inode_id] = holder;
  }

  void RemoveIndex(uint64_t inode_id) {
    files_leases_.erase(inode_id);
  }

  size_t GetIndexSize() const {
    return files_leases_.size();
  }

  void UpdateEstimateSize() {
    files_leases_size_.store(files_leases_.size());
  }
  // no need for slice lock
  size_t EstimateIndexSize() const {
    return files_leases_size_.load(std::memory_order::memory_order_relaxed);
  }

 private:
  mutable ReadWriteLock rwlock_;

  // identifier used to sort slice
  int id_;

  // index inode_id -> holder
  std::unordered_map<uint64_t, std::string> files_leases_;
  std::atomic<size_t> files_leases_size_{0};
};

class LeaseManager : public LeaseManagerBase {
 public:
  LeaseManager();
  virtual ~LeaseManager();

  LeaseManager(const LeaseManager&) = delete;
  LeaseManager& operator=(const LeaseManager&) = delete;

  void SetMetaStorage(MetaStorage* ms);

  // [Section] Status
  LeaseStats Stats() override;

  std::vector<std::string> GetActiveClients() const override;

  DetailLeaseStats TestOnlyDetailStats(INodeID inode_id,
                                       const std::string& holder,
                                       const std::string& ip) override;

  // no mutex lock version of GetLeasesSize(), for metric
  uint64_t MetricOnlyEstimateLeaseSize() const override;

  // no mutex lock version of GetAllFileLeasesSize(), for metric
  uint64_t MetricOnlyEstimateUcFileSize() const override;

  void UpdateStats() override;

  // [Section] Lease Biz
  std::string GetLeaseHolder(uint64_t inode_id,
                             bool need_lock = true) const override;

  // deprecated: we can simply check lease info in the inode
  // NOTICE:
  // if lease valid, it will renew this lease to guarantee its validation.
  // [[nodiscard]]
  bool CheckLease(const std::string& holder, uint64_t inode_id) override;

  // [[nodiscard]]
  bool AddLease(const std::string& holder, uint64_t inode_id) override;

  // [[nodiscard]]
  bool RemoveLease(uint64_t inode_id) override;

  void RenewLease(const std::string& holder,
                  const std::string& holder_ip) override;
  void RenewAllLeases() override;

  bool ReassignLease(const std::string& holder,
                     uint64_t inode_id,
                     const std::string& new_holder) override;

  bool SpeedUpReLease(INodeID inode_id) override;

  Status CanRecoverLease(uint64_t inode_id,
                         const std::string& orig_holder,
                         const std::string& new_holder,
                         bool force,
                         bool* need_check_last_block) override;

  // [SECTION] expired
  std::chrono::milliseconds GetExpiredHardLimitMs() {
    return expired_hard_limit_ms_;
  }

  bool IsExpiredHardLimit(const std::string& holder) const;
  bool IsExpiredSoftLimit(const std::string& holder) const;

  // without lock
  bool IsExpiredHardLimitInternal(const std::shared_ptr<Lease>& lease) const;
  bool IsExpiredSoftLimitInternal(const std::shared_ptr<Lease>& lease) const;

  void LockAllIndex();
  void UnlockAllIndex();

  std::vector<LeaseSlice>& GetLeaseSlices() {
    return lease_slices_;
  }

  LeaseMetrics& metrics() {
    return metrics_;
  }

  // [SECTION] test only
  bool CanAddLease(const std::string& holder, uint64_t inode_id) const override;

  void RemoveAllLeases() override;

  // NOTICE: return file -> holder
  std::unordered_map<uint64_t, std::string>
  TestOnlyGetExpiredHardLimitLeaseFilesAndHolders() const;

  std::unordered_set<uint64_t> GetOpenFiles() const;

  // Should only be accessed by DoTask in lease_monitor.cc
  // DoTask and its executor should ensure access to GetInternalLeaseName is thread safe.
  std::string GetInternalLeaseName();

 private:
  std::vector<LeaseSlice> lease_slices_;
  std::vector<IndexSlice> index_slices_;

  std::chrono::milliseconds expired_hard_limit_ms_;
  std::chrono::milliseconds expired_soft_limit_ms_;

  LeaseMetrics metrics_;

  // Should only be accessed by DoTask in lease_monitor.cc
  // DoTask and its executor should ensure that [access to internal_lease] is thread safe.
  // last_internal_lease_update_time expect to be 1970-01-01 by defalut
  std::chrono::system_clock::time_point last_internal_lease_update_time_;
  std::string internal_lease_holder_;

  std::shared_ptr<Lease> AddLeaseInternal(LeaseSlice& slice,
                                          const std::string& holder,
                                          uint64_t inode_id);

  bool RemoveLeaseInternal(LeaseSlice& slice,
                           const std::string& holder,
                           uint64_t inode_id);

  std::string GetLeaseHolderInternal(uint64_t inode_id) const;

  // [Section] Slice & Lock
  LeaseSlice& GetLeaseSlice(const std::string& holder);
  const LeaseSlice& GetLeaseSlice(const std::string& holder) const;

  IndexSlice& GetIndexSlice(uint64_t inode_id);
  const IndexSlice& GetIndexSlice(uint64_t inode_id) const;

  void OrderedLockSlices(LeaseSlice& s1, LeaseSlice& s2);
  void OrderedUnlockSlices(LeaseSlice& s1, LeaseSlice& s2);

  // [Section] Status Internal
  size_t NumOpenFilesInternal() const;

  // <holder_lease_size, sorted_leases_size>
  std::pair<size_t, size_t> GetLeasesSize() const;

  size_t GetAllFileLeasesSize() const;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MANAGER_H_
