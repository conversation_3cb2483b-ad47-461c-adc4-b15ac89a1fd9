// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjun<PERSON> <<EMAIL>>
// Created: 2023/06/06
// Description

#ifndef LEASE_LEASE_MONITOR_BASE_H_
#define LEASE_LEASE_MONITOR_BASE_H_

#include <functional>  // For function.
#include <string>      // For string.

#include "base/status.h"             // For Status.
#include "namespace/inode.h"         // For INodeID.
#include "namespace/meta_storage.h"  // For MetaStorage.

namespace dancenn {

class LeaseMonitorBase {
 public:
  using ReleaseCallbackType =
      std::function<Status(INodeID inode_id,
                           const std::string& orig_holder,
                           const std::string& new_holder)>;

 public:
  virtual ~LeaseMonitorBase() = default;

  virtual void SetMetaStorage(MetaStorage* ms) = 0;
  virtual void Start() = 0;
  virtual void Stop() = 0;
  virtual bool DoTask() = 0;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MONITOR_BASE_H_
