// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/06/01
// Description

#ifndef LEASE_LEASE_MANAGER_BASE_H_
#define LEASE_LEASE_MANAGER_BASE_H_

#include <chrono>   // For chrono.
#include <cstdint>  // For uint64_t.
#include <set>      // For set.
#include <string>   // For string.
#include <utility>  // For pair.
#include <vector>   // For vector.

#include "base/status.h"      // For Status.
#include "namespace/inode.h"  // For INodeID.

namespace dancenn {

struct LeaseStats {
  uint64_t holder_num{0};
  uint64_t lease_num{0};
  uint64_t path_num{0};

  LeaseStats() = default;
  LeaseStats(uint64_t h, uint64_t l, uint64_t p)
      : holder_num(h), lease_num(l), path_num(p) {
  }
};

struct LeaseInfo {
  std::string holder_ip;
  std::set<INodeID> inode_ids;
  // Set by NameSpace.
  std::set<std::string> paths;
  std::chrono::system_clock::time_point last_update;
};

struct DetailLeaseStats {
  std::vector<std::pair<std::string, LeaseInfo>> leases;
};

class MetaStorage;
class LeaseManagerBase {
 public:
  virtual ~LeaseManagerBase() = default;

  virtual void SetMetaStorage(MetaStorage* ms) = 0;

  virtual LeaseStats Stats() = 0;
  virtual uint64_t MetricOnlyEstimateLeaseSize() const = 0;
  virtual uint64_t MetricOnlyEstimateUcFileSize() const = 0;
  virtual void UpdateStats() = 0;
  virtual std::vector<std::string> GetActiveClients() const = 0;
  virtual DetailLeaseStats TestOnlyDetailStats(INodeID inode_id,
                                               const std::string& holder,
                                               const std::string& ip) = 0;

  virtual std::string GetLeaseHolder(INodeID inode_id,
                                     bool need_lock = true) const = 0;

  // NOTICE:
  // If lease valid, it will renew this lease to guarantee its validation.
  virtual bool CheckLease(const std::string& holder, INodeID inode_id) = 0;
  virtual bool CanAddLease(const std::string& holder,
                           INodeID inode_id) const = 0;
  virtual Status CanRecoverLease(INodeID inode_id,
                                 const std::string& orig_holder,
                                 const std::string& new_holder,
                                 bool force,
                                 bool* need_check_last_block) = 0;

  virtual bool AddLease(const std::string& holder, INodeID inode_id) = 0;
  virtual bool RemoveLease(INodeID inode_id) = 0;
  virtual void RemoveAllLeases() = 0;
  virtual void RenewLease(const std::string& holder,
                          const std::string& holder_ip) = 0;
  virtual void RenewAllLeases() = 0;
  virtual bool ReassignLease(const std::string& holder,
                             INodeID inode_id,
                             const std::string& new_holder) = 0;

  virtual bool SpeedUpReLease(INodeID inode_id) = 0;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MANAGER_BASE_H_
