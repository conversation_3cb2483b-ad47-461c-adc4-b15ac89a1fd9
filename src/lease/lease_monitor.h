// Copyright 2023 Mu <PERSON> <<EMAIL>>

#ifndef LEASE_LEASE_MONITOR_H_
#define LEASE_LEASE_MONITOR_H_

#include <memory>
#include <mutex>

#include "lease/lease.h"
#include "lease/lease_manager.h"
#include "lease/lease_monitor_base.h"
#include "namespace/meta_storage.h"

namespace dancenn {

class LeaseMonitor : public LeaseMonitorBase {
 public:
  LeaseMonitor(LeaseManager* manager, const ReleaseCallbackType& release_cb);
  ~LeaseMonitor();

  LeaseMonitor(const LeaseMonitor&) = delete;
  LeaseMonitor& operator=(const LeaseMonitor&) = delete;

  void SetMetaStorage(MetaStorage* ms) override;
  void Start() override;
  void Stop() override;

  bool DoTask() override;

 private:
  void MonitorSliceLeases(LeaseSlice& slice,
                          std::chrono::system_clock::time_point* last_update,
                          std::chrono::system_clock::time_point* max_expire);

 private:
  LeaseManager* manager_{nullptr};

  std::mutex mutex_;
  std::unique_ptr<cnetpp::concurrency::ThreadPool> worker_;

  std::atomic<bool> stopped_{false};

  ReleaseCallbackType release_cb_;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MONITOR_H_
