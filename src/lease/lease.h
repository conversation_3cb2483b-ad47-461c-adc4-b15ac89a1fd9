// Copyright 2023 Mu <PERSON> <<EMAIL>>

#ifndef LEASE_LEASE_H_
#define LEASE_LEASE_H_

#include <gflags/gflags.h>

#include <chrono>
#include <set>
#include <sstream>
#include <string>

DECLARE_uint64(lease_expired_hard_limit_ms);

namespace dancenn {

class Lease {
 public:
  const static std::string DEFAULT_HOLDER_IP;

  explicit Lease(std::string holder) : holder_(std::move(holder)) {
    Renew(Lease::DEFAULT_HOLDER_IP);
  }

  bool HasOpenFiles() const { return !open_files_.empty(); }

  bool HasOpenFile(uint64_t inode_id) {
    return open_files_.find(inode_id) != open_files_.end();
  }

  const std::set<uint64_t>& GetOpenFiles() const { return open_files_; }

  std::set<uint64_t> CopyOpenFiles() const { return open_files_; }

  size_t NumOpenFiles() const { return open_files_.size(); }

  const std::string& GetHolder() const { return holder_; }

  std::string GetHolderIp() const {
    std::lock_guard<std::mutex> guard(mutex_);

    return holder_ip_;
  }

  void SetHolderIp(const std::string& ip) {
    if (ip.empty() || ip == Lease::DEFAULT_HOLDER_IP) {
      return;
    }

    std::lock_guard<std::mutex> guard(mutex_);

    holder_ip_ = ip;
  }

  void SetHolderIpNX(const std::string& ip) {
    if (ip.empty() || ip == Lease::DEFAULT_HOLDER_IP) {
      return;
    }

    std::lock_guard<std::mutex> guard(mutex_);

    if (holder_ip_.empty() || holder_ip_ == Lease::DEFAULT_HOLDER_IP) {
      holder_ip_ = ip;
    }
  }

  std::string ToString() const {
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                         last_update_time_.time_since_epoch())
                         .count();
    std::lock_guard<std::mutex> guard(mutex_);

    std::time_t last_update_time =
        std::chrono::system_clock::to_time_t(last_update_time_);
    char time_cstr[30];
    std::string time_str(::ctime_r(&last_update_time, time_cstr));
    std::ostringstream oss;
    oss << "[Lease. Holder: " << holder_ << ", IP: " << holder_ip_
        << ", PendingCreates: " << open_files_.size()
        << ", LastUpdateTime: " << time_str.substr(0, time_str.length() - 1)
        << "]";
    return oss.str();
  }

  const std::chrono::system_clock::time_point& GetLastUpdate() const {
    return last_update_time_;
  }

  const std::chrono::system_clock::time_point& GetMaxExpire() const {
    return max_expire_time_;
  }

  void SetMaxExpire(const std::chrono::system_clock::time_point& expire) {
    max_expire_time_ = expire;
  }

  bool AddOpenFile(uint64_t inode_id) {
    return (open_files_.insert(inode_id)).second;
  }

  bool RemoveOpenFile(uint64_t inode_id) {
    return open_files_.erase(inode_id) > 0;
  }

  void Renew(const std::string& ip) {
    last_update_time_ = std::chrono::system_clock::now();
    max_expire_time_ =
        last_update_time_ +
        std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms);
    SetHolderIpNX(ip);
  }

  int64_t CompareTo(const Lease& lease) const {
    int64_t t = static_cast<int64_t>(
        std::chrono::duration_cast<std::chrono::nanoseconds>(
            last_update_time_ - lease.last_update_time_)
            .count());
    return t == 0 ? holder_.compare(lease.holder_) : t;
  }

  struct LeaseLessComparator {
    bool operator()(const std::shared_ptr<Lease>& lhs,
                    const std::shared_ptr<Lease>& rhs) const {
      return (*lhs).CompareTo(*rhs) < 0;
    }
  };

 protected:
  std::string holder_;
  std::string holder_ip_;
  std::set<uint64_t> open_files_;
  std::chrono::system_clock::time_point last_update_time_;
  std::chrono::system_clock::time_point max_expire_time_;

  mutable std::mutex mutex_;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_H_
