// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruan<PERSON><PERSON> <<EMAIL>>
// Created: 2023/06/05
// Description

#include "lease/lease_manager_v2.h"

#include <absl/random/random.h>       // For BitGen, Uniform, IntervalClosed.
#include <absl/strings/match.h>       // For StartsWith.
#include <absl/strings/str_format.h>  // For ParsedFormat, StrFormat.
#include <gflags/gflags.h>            // For DECLARE_uint32, etc.

#include <chrono>        // For chrono.
#include <functional>    // For hash.
#include <mutex>         // For mutex, once_flag, call_once.
#include <shared_mutex>  // For shared_lock.
#include <utility>       // For move.

#include "base/defer.h"                              // For DEFER.
#include "base/java_exceptions.h"                    // For JavaExceptions.
#include "base/logger_metrics.h"                     // For LoggerMetrics.
#include "base/metrics.h"                            // For MFC.
#include "base/stop_watch.h"                         // For StopWatch.
#include "base/time_util.h"                          // For TimeUtilV2.
#include "glog/logging.h"                            // For CHECK_NOTNULL, etc.
#include "namespace/meta_storage_constants.h"        // For kLeaseCFIndex.
#include "proto/generated/dancenn/status_code.pb.h"  // For StatusCode.

DECLARE_uint32(lease_slice_size);
DECLARE_uint32(lease_speed_up_sec);
DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);

namespace dancenn {

LeaseSliceV2::LeaseSliceV2(LeaseSliceV2&& other) {
  LOG(FATAL) << "LeaseSliceV2::LeaseSliceV2(LeaseSliceV2&&) is not implemented";
}

uint64_t LeaseSliceV2::GetLeaseSize() const {
  std::shared_lock<ReadWriteLock> _(rwlock_);
  return leases_.size();
}

void LeaseSliceV2::GetActiveClients(std::vector<std::string>* out) const {
  CHECK(out);
  std::shared_lock<ReadWriteLock> _(rwlock_);
  for (const auto& kv : leases_) {
    out->emplace_back(kv.first);
  }
}

bool LeaseSliceV2::IsLeasePresent(const std::string& holder) {
  std::shared_lock<ReadWriteLock> _(rwlock_);
  return leases_.find(holder) != leases_.end();
}

bool LeaseSliceV2::IsExpiredLimit(const std::string& holder,
                                  bool use_soft_limit) {
  std::shared_lock<ReadWriteLock> _(rwlock_);
  auto it = leases_.find(holder);
  if (it == leases_.end()) {
    return false;
  }
  return IsExpiredLimit(*it->second, use_soft_limit);
}

std::shared_ptr<Lease> LeaseSliceV2::AddLease(const std::string& holder) {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  auto it = leases_.find(holder);
  if (it == leases_.end()) {
    auto r = leases_.insert(
        std::make_pair<>(holder, std::make_shared<Lease>(holder)));
    CHECK(r.second);
    it = r.first;
  } else {
    it->second->Renew(Lease::DEFAULT_HOLDER_IP);
  }
  return it->second;
}

bool LeaseSliceV2::TestOnlyAddLease(std::shared_ptr<Lease> lease) {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  auto it = leases_.find(lease->GetHolder());
  if (it != leases_.end()) {
    return false;
  }
  leases_[lease->GetHolder()] = lease;
  return true;
}

void LeaseSliceV2::RenewLease(const std::string& holder,
                              const std::string& holder_ip) {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  auto it = leases_.find(holder);
  if (it == leases_.end()) {
    auto r = leases_.insert(
        std::make_pair<>(holder, std::make_shared<Lease>(holder)));
    CHECK(r.second);
    it = r.first;
  } else {
    it->second->Renew(holder_ip);
  }
}

void LeaseSliceV2::RenewAllLeases() {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  for (const auto& kv : leases_) {
    kv.second->Renew(Lease::DEFAULT_HOLDER_IP);
  }
}

bool LeaseSliceV2::SpeedUpReLease(const std::string& holder) {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  auto it = leases_.find(holder);
  if (it == leases_.end()) {
    LOG(INFO) << "Fail to find lease, holder: " << holder;
    return false;
  }
  // Lease recovery will happen in FLAGS_lease_speed_up_sec seconds
  auto expire_at = it->second->GetLastUpdate() +
                   std::chrono::seconds(FLAGS_lease_speed_up_sec);
  it->second->SetMaxExpire(expire_at);
  LOG(INFO) << "Speed up recovery of lease " << it->second->ToString();
  return true;
}

void LeaseSliceV2::RemoveLease(INodeID inode_id, const std::string& holder) {
  // Unlike LeaseSlice, LeaseSliceV2 does not keep track of open files,
  // so the LeaseSliceV2::RemoveLease method will not
  // remove items from LeaseSliceV2::leases_.
  // Instead, the responsibility of garbage collection
  // is delegated to LeaseMonitorV2.
  //
  // Moreover, the eviction criteria for a lease should be based on
  // the absence of renewal requests for an extended period,
  // rather than the lack of any files associated with the lease.
  // When LeaseMonitorV2 is unable to locate a lease in LeaseSliceV2,
  // it can determine that the lease has not been renewed for a long duration,
  // indicating that it has exceeded the hard limit for expiration.
}

void LeaseSliceV2::GC() {
  StopWatch sw(LeaseSliceV2Metrics::Instance().gc_lease_slice_wait_lock_time_);
  sw.Start();
  std::unique_lock<ReadWriteLock> _(rwlock_);

  sw.NextStep(LeaseSliceV2Metrics::Instance().gc_lease_slice_hold_lock_time_);
  MFH(LeaseSliceV2Metrics::Instance().gc_lease_slice_count_)
      ->Update(leases_.size());
  // https://en.cppreference.com/w/cpp/container/unordered_map/erase
  for (auto it = leases_.begin(); it != leases_.end();) {
    if (IsExpiredLimit(*it->second, /*use_soft_limit=*/false)) {
      it = leases_.erase(it);
    } else {
      it++;
    }
  }
}

void LeaseSliceV2::Clear() {
  std::unique_lock<ReadWriteLock> _(rwlock_);
  leases_.clear();
}

bool LeaseSliceV2::IsExpiredLimit(const Lease& lease,
                                  bool use_soft_limit) const {
  // https://en.cppreference.com/w/cpp/chrono/system_clock/now
  // std::chrono::system_clock::now
  // Returns a time point representing the current point in time.
  // auto time_passed = std::chrono::duration_cast<std::chrono::milliseconds>(
  //     std::chrono::system_clock::now() - lease.GetLastUpdate());
  auto time_passed = TimeUtilV2::GetNowEpochMs() -
                     std::chrono::duration_cast<std::chrono::milliseconds>(
                         lease.GetLastUpdate().time_since_epoch())
                         .count();
  return time_passed > (use_soft_limit ? FLAGS_lease_expired_soft_limit_ms
                                       : FLAGS_lease_expired_hard_limit_ms);
}

LeaseManagerV2::LeaseManagerV2()
    : metrics_(this),
      meta_storage_(nullptr),
      start_ts_in_ms_(0),
      last_internal_lease_update_ms_(0) {
  lease_slices_.resize(FLAGS_lease_slice_size);
}

void LeaseManagerV2::SetMetaStorage(MetaStorage* ms) {
  CHECK_NOTNULL(ms);
  meta_storage_ = ms;
}

void LeaseManagerV2::TestOnlyResetMetaStorage() {
  meta_storage_ = nullptr;
}

void LeaseManagerV2::Start() {
  start_ts_in_ms_ = TimeUtilV2::GetNowEpochMs();
  LOG(INFO) << "LeaseManagerV2 start";
}

void LeaseManagerV2::Stop() {
  start_ts_in_ms_ = 0;
  LOG(INFO) << "LeaseManagerV2 stop";
}

LeaseMetrics& LeaseManagerV2::metrics() {
  return metrics_;
}

LeaseStats LeaseManagerV2::Stats() {
  return lease_stats_;
}

uint64_t LeaseManagerV2::MetricOnlyEstimateLeaseSize() const {
  return lease_stats_.lease_num;
}

uint64_t LeaseManagerV2::MetricOnlyEstimateUcFileSize() const {
  return lease_stats_.path_num;
}

void LeaseManagerV2::UpdateStats(uint64_t uc_file_cnt) {
  lease_stats_.holder_num = 0;
  for (const auto& lease_slice : lease_slices_) {
    lease_stats_.holder_num += lease_slice.GetLeaseSize();
  }
  lease_stats_.lease_num = lease_stats_.holder_num;
  lease_stats_.path_num = uc_file_cnt;
}

void LeaseManagerV2::UpdateStats() {
  std::lock_guard<decltype(lease_state_mutex_)> guard(lease_state_mutex_);

  uint64_t uc_file_cnt = 0;
  if (meta_storage_) {
    auto snapshot_holder = meta_storage_->GetSnapshot();
    {
      // Iterator holder scope within snapshot holder scope.
      auto snapshot = snapshot_holder->snapshot();
      auto iter_holder = meta_storage_->GetIterator(snapshot, kLeaseCFIndex);
      meta_storage_->ScanLease(
          [this, &uc_file_cnt](INodeID inode_id,
                               const std::string& client_name) {
            uc_file_cnt++;
          },
          iter_holder->iter());
    }
  }
  UpdateStats(uc_file_cnt);
}

std::vector<std::string> LeaseManagerV2::GetActiveClients() const {
  std::vector<std::string> active_clients;
  for (auto& lease_slice : lease_slices_) {
    lease_slice.GetActiveClients(&active_clients);
  }
  return std::move(active_clients);
}

DetailLeaseStats LeaseManagerV2::TestOnlyDetailStats(INodeID inode_id,
                                                     const std::string& holder,
                                                     const std::string& ip) {
  return DetailLeaseStats();
}

std::string LeaseManagerV2::GetInternalLeaseName() {
  uint64_t current_ts_in_ms = TimeUtilV2::GetNowEpochMs();
  if (current_ts_in_ms > last_internal_lease_update_ms_ &&
      current_ts_in_ms - last_internal_lease_update_ms_ >
          FLAGS_lease_expired_hard_limit_ms) {
    last_internal_lease_update_ms_ = current_ts_in_ms;
    internal_lease_holder_ = std::string(kNamenodeLeaseHolder) + "_" +
                             std::to_string(current_ts_in_ms / 1000);
  }
  return internal_lease_holder_;
}

std::string LeaseManagerV2::GetLeaseHolder(INodeID inode_id,
                                           bool need_lock) const {
  // NameSpace::WriteLockThreePaths already protects me.
  return meta_storage_->GetLeaseHolder(inode_id);
}

bool LeaseManagerV2::CheckLease(const std::string& holder, INodeID inode_id) {
  std::string lease_holder = meta_storage_->GetLeaseHolder(inode_id);
  INode inode;
  if (meta_storage_->GetINode(inode_id, &inode) != StatusCode::kOK) {
    LOG_WITH_LEVEL(ERROR) << "CheckLease on not existed file: " << inode_id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  }
  if (!IsEqualTo(lease_holder, inode)) {
    return false;
  }
  return lease_holder == holder;
}

bool LeaseManagerV2::CanAddLease(const std::string& holder,
                                 INodeID inode_id) const {
  // Keep compatible with LeaseManager::CanAddLease.
  return meta_storage_->GetLeaseHolder(inode_id) != holder;
}

Status LeaseManagerV2::CanRecoverLease(INodeID inode_id,
                                       const std::string& orig_holder,
                                       const std::string& new_holder,
                                       bool force,
                                       bool allow_self_seize_lease,
                                       bool use_soft_limit,
                                       bool* need_check_last_block) {
  // Keep compatible with LeaseManager::CanRecoverLease.
  auto lease_holder = meta_storage_->GetLeaseHolder(inode_id);
  if (lease_holder == new_holder && !force && !allow_self_seize_lease) {
    std::string msg = absl::StrFormat(
        "Failed to create file %d for %s for holder %s "
        "because current holder is trying to recreate file",
        inode_id,
        new_holder,
        orig_holder);
    // The incoming request originates from AsyncAppend.
    LOG(INFO) << msg;
    return Status(JavaExceptions::kAlreadyBeingCreatedException, msg);
  }
  if (lease_holder.empty()) {
    std::string msg = absl::StrFormat(
        "Failed to create file %d for %s "
        "for client %s because no leases found",
        inode_id,
        new_holder,
        orig_holder);
    if (absl::StartsWith(new_holder, kNamenodeLeaseHolder)) {
      // The incoming request originates from LeaseMonitorV2.
      // LeaseMonitorV2 doesn't invoke the GetLastINodeInPath function
      // to validate the existence of the inode.
      // Therefore, it's expected behavior
      // to discover that the lease doesn't exist in this context.
      // https://bytedance.feishu.cn/docx/Gw8PdnvweosRifx8NCkcTuWBnJc#UDoLdMK48oDtvzxunsXchrC3nsd
      LOG(INFO) << msg;
    } else {
      LOG_WITH_LEVEL(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }
    return Status(JavaExceptions::kAlreadyBeingCreatedException, msg);
  }
  if (force) {
    LOG(INFO) << "Recover lease " << orig_holder << " for file " << inode_id;
    return Status();
  }
  if (lease_holder != orig_holder) {
    std::string msg = absl::StrFormat(
        "Current lease holder %s does not match file creator, "
        "inode={id=%d,client_name=%s}",
        orig_holder,
        inode_id,
        lease_holder);
    if (absl::StartsWith(new_holder, kNamenodeLeaseHolder)) {
      LOG(INFO) << msg;
    } else {
      LOG_WITH_LEVEL(ERROR) << msg;
      MFC(LoggerMetrics::Instance().error_)->Inc();
    }
    return Status(JavaExceptions::kIOException, msg);
  }
  {
    LeaseSliceV2& slice = GetLeaseSlice(orig_holder);
    bool is_lease_present = slice.IsLeasePresent(orig_holder);
    if ((!is_lease_present && IsActiveDurationSufficient(use_soft_limit)) ||
        (is_lease_present &&
         slice.IsExpiredLimit(orig_holder, use_soft_limit))) {
      LOG(INFO) << "Recover lease " << orig_holder << " for file " << inode_id;
      return Status();
    } else {
      if (need_check_last_block) {
        *need_check_last_block = true;
      }
      return Status(Code::kFalse);
    }
  }
}

Status LeaseManagerV2::CanRecoverLease(INodeID inode_id,
                                       const std::string& orig_holder,
                                       const std::string& new_holder,
                                       bool force,
                                       bool* need_check_last_block) {
  return CanRecoverLease(inode_id,
                         orig_holder,
                         new_holder,
                         force,
                         /*allow_self_seize_lease=*/false,
                         /*use_soft_limit=*/true,
                         need_check_last_block);
}

bool LeaseManagerV2::AddLease(const std::string& holder, INodeID inode_id) {
  DEFER([this]() {
    // https://bytedance.feishu.cn/docx/RjZadtN12oYR92xoXJFco8OQnuh#part-PIGGdGZPDoSzUExk3vlcD6GNntc
    // LeaseMonitorV2 will execute GC while in the active state, but not in the
    // standby or observer states. Inline garbage collection should be performed
    // when it is not in the active state.
    if (!IsActive() && ShouldPerformInlineGC()) {
      VLOG(8) << "Perform inline gc";
      GC();
    }
  });
  std::string lease_holder = meta_storage_->GetLeaseHolder(inode_id);
  // The file is currently governed by a lease.
  if (!lease_holder.empty() && lease_holder != holder) {
    LOG_WITH_LEVEL(ERROR) << "Lease is holded by other holder, file: "
                          << inode_id << ", applyed_holder: " << holder
               << ", lease_holder: " << lease_holder;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  } else {
    // The file is not governed by a lease.
    GetLeaseSlice(holder).AddLease(holder);
    return true;
  }
}

bool LeaseManagerV2::TestOnlyAddLease(std::shared_ptr<Lease> lease) {
  return GetLeaseSlice(lease->GetHolder()).TestOnlyAddLease(lease);
}

bool LeaseManagerV2::RemoveLease(INodeID inode_id) {
  std::string lease_holder = meta_storage_->GetLeaseHolder(inode_id);
  if (!lease_holder.empty()) {
    // In most cases, callers first remove the lease from MetaStorage and then
    // invoke this function through the callback. As a result, it is expected
    // that no information will be found about that inode.
    LOG_WITH_LEVEL(WARNING)
        << "RemoveLease with lease_holder not empty, " << lease_holder;
    MFC(LoggerMetrics::Instance().warn_)->Inc();
  }
  GetLeaseSlice(lease_holder).RemoveLease(inode_id, lease_holder);
  return true;
}

void LeaseManagerV2::RemoveAllLeases() {
  for (auto& lease_slice : lease_slices_) {
    lease_slice.Clear();
  }
}

void LeaseManagerV2::RenewLease(const std::string& holder,
                                const std::string& holder_ip) {
  GetLeaseSlice(holder).RenewLease(holder, holder_ip);
}

void LeaseManagerV2::RenewAllLeases() {
  for (auto& slice : lease_slices_) {
    slice.RenewAllLeases();
  }
}

bool LeaseManagerV2::ReassignLease(const std::string& orig_holder,
                                   INodeID inode_id,
                                   const std::string& new_holder) {
  {
    auto& slice = GetLeaseSlice(orig_holder);
    slice.RemoveLease(inode_id, orig_holder);
  }
  {
    auto& slice = GetLeaseSlice(new_holder);
    slice.AddLease(new_holder);
  }
  LOG(INFO) << "Lease on file " << inode_id << " reassigned from "
            << orig_holder << " to " << new_holder;
  return true;
}

bool LeaseManagerV2::SpeedUpReLease(INodeID inode_id) {
  std::string lease_holder = meta_storage_->GetLeaseHolder(inode_id);
  if (lease_holder.empty()) {
    LOG_WITH_LEVEL(ERROR) << "Fail to find lease of inode " << inode_id;
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  }
  return GetLeaseSlice(lease_holder).SpeedUpReLease(lease_holder);
}

void LeaseManagerV2::GC() {
  for (LeaseSliceV2& lease_slice : lease_slices_) {
    lease_slice.GC();
  }
}

bool LeaseManagerV2::IsActive() const {
  return start_ts_in_ms_ != 0;
}

bool LeaseManagerV2::IsActiveDurationSufficient(bool use_soft_limit) const {
  uint64_t current_ts_in_ms = TimeUtilV2::GetNowEpochMs();
  return (start_ts_in_ms_ != 0) &&  //
         (current_ts_in_ms > start_ts_in_ms_) &&
         (current_ts_in_ms - start_ts_in_ms_ >
          (use_soft_limit ? FLAGS_lease_expired_soft_limit_ms
                          : FLAGS_lease_expired_hard_limit_ms));
}

bool LeaseManagerV2::ShouldPerformInlineGC() const {
  // https://abseil.io/docs/cpp/guides/random#how-are-the-abseil-random-generator-types-seeded
  // Like the C++ standard library random engines,
  // neither absl::BitGen, nor absl::InsecureBitGen are thread safe.
  thread_local absl::BitGen bitgen;
  int n = absl::Uniform(absl::IntervalClosed, bitgen, 0u, 512u);
  return n % 512 == 0;
}

bool LeaseManagerV2::IsEqualTo(const std::string& lease_holder,
                               const INode& inode) const {
  if (lease_holder != inode.uc().client_name()) {
    using LogMsgFmtT = absl::ParsedFormat<'s', 'd', 's', 's'>;
    static std::once_flag flag;
    static std::unique_ptr<LogMsgFmtT> log_msg_fmt;
    std::call_once(flag, []() {
      log_msg_fmt = LogMsgFmtT::New(
          "%s is not matched with "
          "inode{id:%d}'s uc{client_name:%s,client_machine:%s}");
      CHECK(log_msg_fmt);
    });
    LOG_WITH_LEVEL(ERROR) << absl::StrFormat(*log_msg_fmt,
                                             lease_holder,
                                  inode.id(),
                                  inode.uc().client_name(),
                                  inode.uc().client_machine());
    MFC(LoggerMetrics::Instance().error_)->Inc();
    return false;
  }
  return true;
}

LeaseSliceV2& LeaseManagerV2::GetLeaseSlice(const std::string& holder) {
  return lease_slices_[std::hash<std::string>()(holder) %
                       FLAGS_lease_slice_size];
}

}  // namespace dancenn
