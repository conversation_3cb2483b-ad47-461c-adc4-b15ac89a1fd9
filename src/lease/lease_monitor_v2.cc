// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/06/06
// Description

#include "lease/lease_monitor_v2.h"

#include <gflags/gflags.h>  // For DECLARE_uint64.
#include <glog/logging.h>   // For LOG.

#include <chrono>   // For chrono.
#include <cstdint>  // For uint64_t.

#include "base/java_exceptions.h"              // For JavaExceptions.
#include "base/logger_metrics.h"               // For LoggerMetrics.
#include "base/metrics.h"                      // For MFC.
#include "base/status.h"                       // For Status.
#include "base/stop_watch.h"                   // For StopWatch.
#include "namespace/inode.h"                   // For INodeID.
#include "namespace/meta_storage_constants.h"  // For kUcCFIndex.

DECLARE_uint32(lease_monitor_interval_ms);

namespace dancenn {

LeaseMonitorV2::LeaseMonitorV2(LeaseManagerV2* lease_manager,
                               const ReleaseCallbackType& release_callback)
    : is_running_(false),
      meta_storage_(nullptr),
      lease_manager_(lease_manager),
      release_callback_(release_callback) {
}

LeaseMonitorV2::~LeaseMonitorV2() {
  Stop();
}

void LeaseMonitorV2::SetMetaStorage(MetaStorage* ms) {
  CHECK_NOTNULL(ms);
  meta_storage_ = ms;
}

void LeaseMonitorV2::Start() {
  // The return value of exchange is
  // the value of the atomic variable before the call.
  if (!is_running_.exchange(true)) {
    CHECK(lease_manager_);
    lease_manager_->Start();
    CHECK(!worker_);
    worker_ =
        std::make_unique<cnetpp::concurrency::ThreadPool>("LeaseMonitor", true);
    // internal_lease_holder_ in lease_manager is not safe.
    // We must keep num thread = 1;
    worker_->set_num_threads(1);
    worker_->Start();
    worker_->AddTask([this]() { return DoTask(); });
  } else {
    LOG(ERROR) << "LeaseMonitorV2 is already running";
    MFC(LoggerMetrics::Instance().error_)->Inc();
  }
}

void LeaseMonitorV2::Stop() {
  if (is_running_.exchange(false)) {
    lease_manager_->Stop();
    LOG(INFO) << "LeaseMonitorV2 is stopping";
    CHECK(worker_);
    worker_->Stop();
    worker_.reset();
    LOG(INFO) << "LeaseMonitorV2 is stopped";
  } else {
    LOG(ERROR) << "LeaseMonitorV2 is already stopped";
    // Cause fsimage coredump.
    // MFC(LoggerMetrics::Instance().error_)->Inc();
  }
}

bool LeaseMonitorV2::DoTask() {
  LOG(INFO) << "Lease check starts";
  if (!is_running_) {
    return true;
  }

  StopWatch sw(lease_manager_->metrics().scan_lease_time_);
  sw.Start();
  uint64_t uc_file_cnt = 0;

  auto snapshot_holder = meta_storage_->GetSnapshot();
  {
    // Iterator holder scope within snapshot holder scope.
    auto snapshot = snapshot_holder->snapshot();
    auto iter_holder = meta_storage_->GetIterator(snapshot, kLeaseCFIndex);
    meta_storage_->ScanLease(
        [this, &uc_file_cnt](INodeID inode_id, const std::string& client_name) {
          uc_file_cnt++;
          HandleLease(inode_id, client_name);
        },
        iter_holder->iter());
  }
  lease_manager_->UpdateStats(uc_file_cnt);
  lease_manager_->GC();

  sw.NextStep();
  uint64_t elapse_time =
      std::chrono::duration_cast<std::chrono::milliseconds>(sw.GetTime())
          .count();
  // internal_lease_holder_ in lease_manager is not safe.
  // We must keep num thread = 1;
  if (FLAGS_lease_monitor_interval_ms < elapse_time) {
    worker_->AddTask([this]() { return DoTask(); });
  } else {
    worker_->AddDelayTask([this]() { return DoTask(); },
                          std::chrono::milliseconds(
                              FLAGS_lease_monitor_interval_ms - elapse_time));
  }
  LOG(INFO) << "LeaseMonitorV2 DoTask spent " << elapse_time << " ms";

  return true;
}

void LeaseMonitorV2::HandleLease(INodeID inode_id,
                                 const std::string& client_name) {
  std::string internal_lease_name = lease_manager_->GetInternalLeaseName();
  Status can_recover_s =
      lease_manager_->CanRecoverLease(inode_id,
                                      client_name,
                                      internal_lease_name,
                                      /*force=*/false,
                                      /*allow_self_seize_lease=*/true,
                                      /*use_soft_limit=*/false,
                                      nullptr);
  DLOG(INFO) << "HandleLease, inode_id: " << inode_id
             << ", client_name: " << client_name
             << ", can_recover_s: " << can_recover_s.ToString();
  if (can_recover_s.IsOK()) {
    Status release_s =
        release_callback_(inode_id, client_name, internal_lease_name);
    if (release_s.HasException()) {
      if (release_s.exception() == JavaExceptions::kStandbyException ||
          release_s.exception() == JavaExceptions::kSafeModeException) {
        // Do nothing.
      } else if (release_s.exception() ==
                 JavaExceptions::kFileNotFoundException) {
        LOG(INFO) << "file " << inode_id << " has been removed";
      }
      VLOG(8) << "lease recover on " << inode_id << " for " << client_name
              << ", result: " << release_s.ToString();
      MFC(lease_manager_->metrics().release_lease_fail_count_)->Inc();
    } else {
      MFC(lease_manager_->metrics().release_lease_succ_count_)->Inc();
    }
  }
}

}  // namespace dancenn
