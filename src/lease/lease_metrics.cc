#include "lease_metrics.h"

#include <memory>
#include <mutex>

#include "lease/lease_manager_base.h"

namespace dancenn {

LeaseMetrics::LeaseMetrics(LeaseManagerBase* lease_manager) {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("LeaseManager");

  lease_count_ =
      metrics->RegisterGauge("LeaseNum", [lease_manager]() -> double {
        return lease_manager->MetricOnlyEstimateLeaseSize();
      });
  lease_path_count_ =
      metrics->RegisterGauge("LeasePathNum", [lease_manager]() -> double {
        return lease_manager->MetricOnlyEstimateUcFileSize();
      });

  scan_lease_time_ = metrics->RegisterHistogram("ScanLeaseTime");

  release_lease_fail_count_ =
      metrics->RegisterCounter("ReleaseLease#status=fail");
  release_lease_succ_count_ =
      metrics->RegisterCounter("ReleaseLease#status=succ");
}

LeaseSliceV2Metrics& LeaseSliceV2Metrics::Instance() {
  static std::unique_ptr<LeaseSliceV2Metrics> lease_slice_v2_metrics;
  static std::once_flag once_flag;
  std::call_once(once_flag, []() {
    lease_slice_v2_metrics =
        std::unique_ptr<LeaseSliceV2Metrics>(new LeaseSliceV2Metrics());
  });
  return *lease_slice_v2_metrics;
}

LeaseSliceV2Metrics::LeaseSliceV2Metrics() {
  auto center = MetricsCenter::Instance();
  auto metrics = center->RegisterMetrics("LeaseSliceV2");

  gc_lease_slice_wait_lock_time_ =
      metrics->RegisterHistogram("GCLeaseSliceTime#step=WaitLock");
  gc_lease_slice_hold_lock_time_ =
      metrics->RegisterHistogram("GCLeaseSliceTime#step=HoldLock");
  gc_lease_slice_count_ = metrics->RegisterHistogram("GCLeaseSliceCount");
}

}  // namespace dancenn
