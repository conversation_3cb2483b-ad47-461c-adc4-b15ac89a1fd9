// Copyright 2017 Le<PERSON> <<EMAIL>>

#include "lease/lease_manager.h"

#include <cnetpp/base/string_piece.h>
#include <glog/logging.h>

#include <algorithm>
#include <shared_mutex>
#include <unordered_set>
#include <utility>

#include "base/constants.h"
#include "base/defer.h"
#include "base/stop_watch.h"

DECLARE_uint64(lease_expired_soft_limit_ms);
DECLARE_uint64(lease_expired_hard_limit_ms);
DECLARE_uint32(lease_slice_size);
DECLARE_uint32(index_slice_size);
DECLARE_uint32(lease_speed_up_sec);

namespace dancenn {

const std::string Lease::DEFAULT_HOLDER_IP = "";

LeaseManager::LeaseManager()
    : lease_slices_(FLAGS_lease_slice_size),
      index_slices_(FLAGS_index_slice_size),
      metrics_(this) {
  expired_hard_limit_ms_ =
      std::chrono::milliseconds(FLAGS_lease_expired_hard_limit_ms);
  expired_soft_limit_ms_ =
      std::chrono::milliseconds(FLAGS_lease_expired_soft_limit_ms);

  for (int i = 0; i < FLAGS_lease_slice_size; ++i) {
    lease_slices_[i].SetId(i);
  }

  for (int i = 0; i < FLAGS_index_slice_size; ++i) {
    index_slices_[i].SetId(i);
  }
}

LeaseManager::~LeaseManager() {
  RemoveAllLeases();
}

void LeaseManager::SetMetaStorage(MetaStorage* ms) {
}

bool LeaseManager::IsExpiredHardLimitInternal(
    const std::shared_ptr<Lease>& lease) const {
  return lease->GetMaxExpire() < std::chrono::system_clock::now();
}

bool LeaseManager::IsExpiredHardLimit(const std::string& holder) const {
  auto& slice = GetLeaseSlice(holder);
  std::shared_lock<ReadWriteLock> lock(slice.GetLock());
  auto&& lease = slice.GetLease(holder);
  if (!lease) {
    return false;
  }
  return IsExpiredHardLimitInternal(lease);
}

bool LeaseManager::IsExpiredSoftLimitInternal(
    const std::shared_ptr<Lease>& lease) const {
  auto time_passed = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now() - lease->GetLastUpdate());
  return time_passed > expired_soft_limit_ms_;
}

bool LeaseManager::IsExpiredSoftLimit(const std::string& holder) const {
  auto& slice = GetLeaseSlice(holder);
  std::shared_lock<ReadWriteLock> lock(slice.GetLock());
  auto&& lease = slice.GetLease(holder);
  if (!lease) {
    return false;
  }
  return IsExpiredSoftLimitInternal(lease);
}

std::string LeaseManager::GetLeaseHolder(uint64_t inode_id,
                                         bool need_lock) const {
  const auto& index_slice = GetIndexSlice(inode_id);
  if (need_lock) {
    index_slice.lock();
  }
  DEFER([&]() {
    if (need_lock) {
      index_slice.unlock();
    }
  });
  return GetLeaseHolderInternal(inode_id);
}

LeaseStats LeaseManager::Stats() {
  auto lease_sizes = GetLeasesSize();
  return LeaseStats{
      lease_sizes.first, lease_sizes.second, NumOpenFilesInternal()};
}

std::string LeaseManager::GetLeaseHolderInternal(uint64_t inode_id) const {
  return GetIndexSlice(inode_id).GetLeaseHolder(inode_id);
}

size_t LeaseManager::NumOpenFilesInternal() const {
  size_t num_open_files = 0;

  for (auto iter = lease_slices_.begin(); iter != lease_slices_.end(); ++iter) {
    std::shared_lock<ReadWriteLock> lock(iter->GetLock());
    for (const auto& lease : iter->GetSortedLeases()) {
      num_open_files += lease->NumOpenFiles();
    }
  }
  return num_open_files;
}

std::shared_ptr<Lease> LeaseManager::AddLeaseInternal(LeaseSlice& slice,
                                                      const std::string& holder,
                                                      uint64_t inode_id) {
  GetIndexSlice(inode_id).AddIndex(inode_id, holder);
  return slice.AddLease(holder, inode_id);
}

bool LeaseManager::AddLease(const std::string& holder, uint64_t inode_id) {
  // lock inode slice
  auto& index_slice = GetIndexSlice(inode_id);
  std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  std::string file_holder = GetLeaseHolderInternal(inode_id);
  if (!file_holder.empty()) {
    // TODO(xiong): maybe not need to add lock to check for same leases
    // lock lease slice
    auto& file_slice = GetLeaseSlice(file_holder);
    auto& now_slice = GetLeaseSlice(holder);

    OrderedLockSlices(file_slice, now_slice);
    DEFER([&]() { OrderedUnlockSlices(file_slice, now_slice); });

    auto&& file_lease = file_slice.GetLease(file_holder);
    auto&& now_lease = now_slice.GetLease(holder);

    if (now_lease == nullptr || now_lease != file_lease) {
      LOG(ERROR) << "Lease is holded by other holder, file: " << inode_id
                 << ", applyed_holder: " << holder
                 << ", holder: " << file_holder;
      return false;
    }

    file_slice.RenewLease(file_lease, Lease::DEFAULT_HOLDER_IP);
    return true;
  }

  // lock lease slice
  auto& now_slice = GetLeaseSlice(holder);
  std::unique_lock<ReadWriteLock> now_lock(now_slice.GetLock());
  AddLeaseInternal(now_slice, holder, inode_id);
  return true;
}

bool LeaseManager::RemoveLeaseInternal(LeaseSlice& slice,
                                       const std::string& holder,
                                       uint64_t inode_id) {
  auto&& lease = slice.GetLease(holder);
  if (lease == nullptr || !lease->HasOpenFile(inode_id)) {
    LOG(ERROR) << "Cannot remove non-exist lease, file: " << inode_id
               << ", holder: " << holder;
    return false;
  }

  GetIndexSlice(inode_id).RemoveIndex(inode_id);
  return slice.RemoveLease(lease, inode_id);
}

void LeaseManager::RemoveAllLeases() {
  for (auto iter = index_slices_.begin(); iter != index_slices_.end(); ++iter) {
    std::unique_lock<ReadWriteLock> index_lock(iter->GetLock());
    iter->ClearIndices();
  }

  for (auto iter = lease_slices_.begin(); iter != lease_slices_.end(); ++iter) {
    std::unique_lock<ReadWriteLock> lock(iter->GetLock());
    iter->ClearLeases();
  }
}

bool LeaseManager::RemoveLease(uint64_t inode_id) {
  // lock inode slice
  auto& index_slice = GetIndexSlice(inode_id);
  std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  std::string file_holder = GetLeaseHolderInternal(inode_id);
  if (file_holder.empty()) {
    return false;
  }

  // lock lease slice
  auto& slice = GetLeaseSlice(file_holder);
  std::unique_lock<ReadWriteLock> lock(slice.GetLock());

  return RemoveLeaseInternal(slice, file_holder, inode_id);
}

bool LeaseManager::SpeedUpReLease(INodeID inode_id) {
  auto& index_slice = GetIndexSlice(inode_id);
  std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  std::string file_holder = GetLeaseHolderInternal(inode_id);
  if (file_holder.empty()) {
    LOG(WARNING) << "Fail to find lease of inode " << inode_id;
    return false;
  }
  auto& file_slice = GetLeaseSlice(file_holder);
  std::unique_lock<ReadWriteLock> file_lock(file_slice.GetLock());
  auto&& file_lease = file_slice.GetLease(file_holder);
  if (file_lease == nullptr) {
    return false;
  }
  // Lease recovery will happen in FLAGS_lease_speed_up_sec seconds
  auto expire_at = file_lease->GetLastUpdate() +
                   std::chrono::seconds(FLAGS_lease_speed_up_sec);
  file_lease->SetMaxExpire(expire_at);
  LOG(INFO) << "Speed up recovery of lease " << file_lease->ToString();
  return true;
}

bool LeaseManager::ReassignLease(const std::string& holder,
                                 uint64_t inode_id,
                                 const std::string& new_holder) {
  // lock inode slice
  auto& index_slice = GetIndexSlice(inode_id);
  std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  // lock lease slice
  auto& slice = GetLeaseSlice(holder);
  auto& new_slice = GetLeaseSlice(new_holder);
  OrderedLockSlices(slice, new_slice);
  DEFER([&]() { OrderedUnlockSlices(slice, new_slice); });

  if (!RemoveLeaseInternal(slice, holder, inode_id)) {
    return false;
  }
  auto lease = AddLeaseInternal(new_slice, new_holder, inode_id);

  LOG(INFO) << "Lease on file " << inode_id << " reassigned from " << holder
            << " to " << new_holder << " lease" << lease->ToString();
  return true;
}

void LeaseManager::RenewLease(const std::string& holder,
                              const std::string& holder_ip) {
  auto& slice = GetLeaseSlice(holder);
  std::unique_lock<ReadWriteLock> lock(slice.GetLock());
  auto&& lease = slice.GetLease(holder);
  if (lease == nullptr) {
    return;
  }
  slice.RenewLease(lease, holder_ip);
}

void LeaseManager::RenewAllLeases() {
  for (auto iter = lease_slices_.begin(); iter != lease_slices_.end(); ++iter) {
    std::unique_lock<ReadWriteLock> lock(iter->GetLock());
    iter->RenewAllLeases();
  }
}

bool LeaseManager::CheckLease(const std::string& holder, uint64_t inode_id) {
  auto& slice = GetLeaseSlice(holder);
  std::shared_lock<ReadWriteLock> lock(slice.GetLock());
  return slice.CheckLease(holder, inode_id);
}

bool LeaseManager::CanAddLease(const std::string& holder,
                               uint64_t inode_id) const {
  auto& index_slice = GetIndexSlice(inode_id);
  std::shared_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  std::string file_holder = GetLeaseHolderInternal(inode_id);
  if (!file_holder.empty()) {
    auto& lease_slice = GetLeaseSlice(file_holder);
    std::shared_lock<ReadWriteLock> lease_lock(lease_slice.GetLock());
    auto&& lease = lease_slice.GetLease(file_holder);
    return !lease;
  }
  return true;
}

Status LeaseManager::CanRecoverLease(uint64_t inode_id,
                                     const std::string& orig_holder,
                                     const std::string& new_holder,
                                     bool force,
                                     bool* need_check_last_block) {
  auto& index_slice = GetIndexSlice(inode_id);
  std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());

  // new holder
  {
    auto& new_slice = GetLeaseSlice(new_holder);
    std::shared_lock<ReadWriteLock> lock(new_slice.GetLock());

    auto&& new_lease = new_slice.GetLease(new_holder);
    if (!force && new_lease != nullptr) {
      if (new_lease->HasOpenFile(inode_id)) {
        LOG(INFO) << inode_id << " has already been created by " << new_holder;
        return Status(
            JavaExceptions::kAlreadyBeingCreatedException,
            "failed to create file " + std::to_string(inode_id) + " for " +
                new_holder + " for holder " + orig_holder +
                " because current holder is trying to recreate file.");
      }
    }
  }

  // origin holder
  {
    auto& orig_slice = GetLeaseSlice(orig_holder);
    std::shared_lock<ReadWriteLock> lock(orig_slice.GetLock());

    auto&& orig_lease = orig_slice.GetLease(orig_holder);
    if (orig_lease == nullptr) {
      LOG(INFO) << inode_id << " is incomplete but holder " << orig_holder
                << " does not have a lease";
      return Status(JavaExceptions::kAlreadyBeingCreatedException,
                    "failed to create file " + std::to_string(inode_id) +
                        " for " + new_holder + " for client " + orig_holder +
                        " because no leases found.");
    }

    if (force) {
      // Do RecoverLease outside
      LOG(INFO) << "recover lease " << orig_holder << " for file " << inode_id;
      return {};
    } else {
      if (!orig_lease->HasOpenFile(inode_id)) {
        return Status(JavaExceptions::kIOException,
                      "Current lease holder " + orig_holder +
                          " does not match file creator inode_id=" +
                          std::to_string(inode_id));
      }
      if (IsExpiredSoftLimitInternal(orig_lease)) {
        LOG(INFO) << "recover lease " << orig_holder << " for file "
                  << inode_id;
        // Do RecoverLease outside
        return {};
      } else {
        *need_check_last_block = true;
        return Status(Code::kFalse, "");
      }
    }
  }
}

LeaseSlice& LeaseManager::GetLeaseSlice(const std::string& holder) {
  return const_cast<LeaseSlice&>(
      static_cast<const LeaseManager&>(*this).GetLeaseSlice(holder));
}

const LeaseSlice& LeaseManager::GetLeaseSlice(const std::string& holder) const {
  size_t slot = std::hash<std::string>()(holder) % FLAGS_lease_slice_size;
  return lease_slices_[slot];
}

IndexSlice& LeaseManager::GetIndexSlice(uint64_t inode_id) {
  return const_cast<IndexSlice&>(
      static_cast<const LeaseManager&>(*this).GetIndexSlice(inode_id));
}

const IndexSlice& LeaseManager::GetIndexSlice(uint64_t inode_id) const {
  return index_slices_[inode_id % FLAGS_index_slice_size];
}

// here we don't have big lock for the whole lease slices, which
// means we may not get a consistent snapshot of all of them.
// but I think it's ok for metrics.
std::pair<size_t, size_t> LeaseManager::GetLeasesSize() const {
  size_t holder_size = 0, sorted_size = 0;
  for (auto iter = lease_slices_.begin(); iter != lease_slices_.end(); ++iter) {
    std::shared_lock<ReadWriteLock> lock(iter->GetLock());
    holder_size += iter->GetHolderLeases().size();
    sorted_size += iter->GetSortedLeases().size();
  }
  return std::make_pair(holder_size, sorted_size);
}

void LeaseManager::OrderedLockSlices(LeaseSlice& s1, LeaseSlice& s2) {
  if (s1 < s2) {
    s1.lock();
    s2.lock();
  } else if (s1 == s2) {
    s1.lock();
  } else {
    s2.lock();
    s1.lock();
  }
}

void LeaseManager::OrderedUnlockSlices(LeaseSlice& s1, LeaseSlice& s2) {
  if (s1 < s2) {
    s2.unlock();
    s1.unlock();
  } else if (s1 == s2) {
    s1.unlock();
  } else {
    s1.unlock();
    s2.unlock();
  }
}

// we don't care the order of locks here as there's no place where
// multiple index locks are used.
void LeaseManager::LockAllIndex() {
  for (auto iter = index_slices_.begin(); iter != index_slices_.end(); ++iter) {
    iter->lock();
  }
}

void LeaseManager::UnlockAllIndex() {
  for (auto iter = index_slices_.begin(); iter != index_slices_.end(); ++iter) {
    iter->unlock();
  }
}

size_t LeaseManager::GetAllFileLeasesSize() const {
  size_t result = 0;
  for (auto iter = index_slices_.begin(); iter != index_slices_.end(); ++iter) {
    std::shared_lock<ReadWriteLock> lock(iter->GetLock());
    result += iter->GetIndexSize();
  }
  return result;
}

std::shared_ptr<Lease> LeaseSlice::GetLease(const std::string& holder) const {
  const auto lease_iter = holder_leases_.find(holder);
  if (lease_iter == holder_leases_.end()) {
    return nullptr;
  }
  return lease_iter->second;
}

void LeaseSlice::RenewLease(const std::shared_ptr<Lease>& lease,
                            const std::string& holder_ip) {
  sorted_leases_.erase(lease);
  lease->Renew(holder_ip);
  sorted_leases_.insert(lease);
}

std::shared_ptr<Lease> LeaseSlice::AddLease(const std::string& holder,
                                            uint64_t inode_id) {
  auto&& lease = GetLease(holder);
  if (lease == nullptr) {
    lease = std::make_shared<Lease>(holder);
    holder_leases_[holder] = lease;
    sorted_leases_.insert(lease);
  } else {
    RenewLease(lease, Lease::DEFAULT_HOLDER_IP);
  }
  CHECK(lease->AddOpenFile(inode_id));
  return lease;
}

bool LeaseSlice::RemoveLease(const std::shared_ptr<Lease>& lease,
                             uint64_t inode_id) {
  CHECK(lease->RemoveOpenFile(inode_id));
  if (!lease->HasOpenFiles()) {
    holder_leases_.erase(lease->GetHolder());
    sorted_leases_.erase(lease);
    DLOG(INFO) << "Remove lease " << lease->GetHolder();
  }
  return true;
}

void LeaseSlice::RenewAllLeases() {
  for (const auto& pair : holder_leases_) {
    RenewLease(pair.second, Lease::DEFAULT_HOLDER_IP);
  }
}

bool LeaseSlice::CheckLease(const std::string& holder, uint64_t inode_id) {
  auto&& lease = GetLease(holder);

  return lease != nullptr && lease->HasOpenFile(inode_id);
}

std::unordered_map<uint64_t, std::string> LeaseManager::
    TestOnlyGetExpiredHardLimitLeaseFilesAndHolders() const {
  std::unordered_map<uint64_t, std::string> holder_open_files;

  for (const auto& lease_slice : lease_slices_) {
    std::shared_lock<ReadWriteLock> lock(lease_slice.GetLock());
    for (const auto& lease : lease_slice.GetSortedLeases()) {
      if (!IsExpiredHardLimitInternal(lease)) {
        break;
      }
      for (const auto open_file : lease->GetOpenFiles()) {
        holder_open_files.insert(std::make_pair(open_file, lease->GetHolder()));
      }
    }
  }
  return holder_open_files;
}

std::unordered_set<uint64_t> LeaseManager::GetOpenFiles() const {
  std::unordered_set<uint64_t> all_open_files;

  for (auto iter = lease_slices_.begin(); iter != lease_slices_.end(); ++iter) {
    std::shared_lock<ReadWriteLock> lock(iter->GetLock());
    for (const auto& lease : iter->GetSortedLeases()) {
      const auto& open_files = lease->GetOpenFiles();
      all_open_files.insert(open_files.begin(), open_files.end());
    }
  }
  return all_open_files;
}

std::vector<std::string> LeaseManager::GetActiveClients() const {
  // Not supported.
  return {};
}

DetailLeaseStats LeaseManager::TestOnlyDetailStats(INodeID inode_id,
                                                   const std::string& holder,
                                                   const std::string& ip) {
  DetailLeaseStats stats;

  if (inode_id != kInvalidINodeId) {
    auto& index_slice = GetIndexSlice(inode_id);
    std::unique_lock<ReadWriteLock> index_lock(index_slice.GetLock());
    std::string holder_i = index_slice.GetLeaseHolder(inode_id);

    if (!holder_i.empty()) {
      auto& slice = GetLeaseSlice(holder_i);
      std::shared_lock<ReadWriteLock> lock(slice.GetLock());
      auto&& lease = slice.GetLease(holder_i);

      LeaseInfo info;
      info.holder_ip = lease->GetHolderIp();
      info.last_update = lease->GetLastUpdate();
      info.inode_ids = lease->CopyOpenFiles();
      stats.leases.emplace_back(lease->GetHolder(), std::move(info));
    }

    return stats;
  }

  for (auto& lease_slice : lease_slices_) {
    std::shared_lock<ReadWriteLock> lock(lease_slice.GetLock());
    for (const auto& lease : lease_slice.GetSortedLeases()) {
      if (holder.empty() && lease->GetHolder() != holder) {
        continue;
      }
      if (ip.empty() && lease->GetHolderIp() != ip) {
        continue;
      }

      LeaseInfo info;
      info.holder_ip = lease->GetHolderIp();
      info.last_update = lease->GetLastUpdate();
      info.inode_ids = lease->CopyOpenFiles();
      stats.leases.emplace_back(lease->GetHolder(), std::move(info));
    }
  }

  return stats;
}

uint64_t LeaseManager::MetricOnlyEstimateLeaseSize() const {
  uint64_t sorted_size = 0;
  for (const auto& slice : lease_slices_) {
    sorted_size += slice.EstimateSortedLeasesSize();
  }
  return sorted_size;
}

uint64_t LeaseManager::MetricOnlyEstimateUcFileSize() const {
  uint64_t result = 0;
  for (const auto& slice : index_slices_) {
    result += slice.EstimateIndexSize();
  }
  return result;
}

void LeaseManager::UpdateStats() {
}

std::string LeaseManager::GetInternalLeaseName() {
  auto time_passed = std::chrono::duration_cast<std::chrono::milliseconds>(
    std::chrono::system_clock::now() - last_internal_lease_update_time_);
  if (time_passed > expired_hard_limit_ms_) {
    last_internal_lease_update_time_ = std::chrono::system_clock::now();
    uint64_t now_in_sec = std::chrono::duration_cast<std::chrono::seconds>(
        last_internal_lease_update_time_.time_since_epoch()).count();
    internal_lease_holder_ = std::string(kNamenodeLeaseHolder) + "_" + std::to_string(now_in_sec);
  }

  return internal_lease_holder_;
}

}  // namespace dancenn
