#include "lease_monitor.h"

DECLARE_bool(run_ut);

namespace dancenn {

LeaseMonitor::LeaseMonitor(LeaseManager* manager,
                           const ReleaseCallbackType& release_cb)
    : manager_(manager), release_cb_(release_cb) {}

LeaseMonitor::~LeaseMonitor() { Stop(); }

void LeaseMonitor::Start() {
  stopped_.store(false);

  if (!worker_) {
    worker_ =
        std::make_unique<cnetpp::concurrency::ThreadPool>("LeaseMonitor", true);
    // internal_lease_holder_ in lease_manager is not safe.
    // We must keep num thread = 1;
    worker_->set_num_threads(1);
    worker_->Start();
    worker_->AddTask([this]() { return DoTask(); });
  } else {
    LOG(WARNING) << "Already Started";
  }
}

void LeaseMonitor::Stop() {
  stopped_.store(true);

  if (worker_) {
    LOG_IF(INFO, !FLAGS_run_ut) << "Stopping";
    worker_->Stop();
    worker_.reset();
    LOG_IF(INFO, !FLAGS_run_ut) << "Stopped";
  }
}

void LeaseMonitor::SetMetaStorage(MetaStorage* ms) {
}

bool LeaseMonitor::DoTask() {
  LOG_IF(INFO, !FLAGS_run_ut) << "Lease check starts";

  if (stopped_) {
    return true;
  }
  // TODO(xiong): use snapshot to avoid lock
  // here lock all indices instead of locking the index locks in given slice
  // , because we should lock index first and then slice. so it's hard to
  // coordinate lock order and ensure there's no change in inodes in slice
  // during locking indices.
  manager_->LockAllIndex();
  auto last_update = std::chrono::system_clock::now();
  auto max_expire = std::chrono::time_point<std::chrono::system_clock>::max();
  for (auto& lease_slice : manager_->GetLeaseSlices()) {
    MonitorSliceLeases(lease_slice, &last_update, &max_expire);
  }
  manager_->UnlockAllIndex();

  if (stopped_) {
    return true;
  }

  auto now = std::chrono::system_clock::now();
  auto check_time_point = last_update + manager_->GetExpiredHardLimitMs();
  if (check_time_point <= now) {
    worker_->AddTask([this]() { return DoTask(); });
    LOG(INFO) << "Most remote lease refreshed at "
              << last_update.time_since_epoch().count()  //
              << " expired at "                          //
              << max_expire.time_since_epoch().count()
              << ", schedule lease check now";
  } else {
    auto wait_for = std::chrono::duration_cast<std::chrono::milliseconds>(
        check_time_point - now);
    worker_->AddDelayTask([this]() { return DoTask(); }, wait_for);
    LOG(INFO) << "Schedule next lease check in " << wait_for.count() << " ms";
  }
  return true;
}

// Already lock all index lock ( manager_->LockAllIndex(); )
void LeaseMonitor::MonitorSliceLeases(
    LeaseSlice& slice,
    std::chrono::system_clock::time_point* last_update,
    std::chrono::system_clock::time_point* max_expire) {
  std::unique_lock<ReadWriteLock> lock(slice.GetLock());

  auto& sorted_leases = slice.GetSortedLeases();
  if (sorted_leases.empty()) {
    return;
  }

  std::set<std::string> processed;
  auto cur_lease = *(sorted_leases.begin());

  std::string internal_lease_name = manager_->GetInternalLeaseName();
  while (!stopped_.load()) {
    VLOG(8) << "current lease " << cur_lease->ToString();

    if (!manager_->IsExpiredHardLimitInternal(cur_lease)) {
      *last_update = std::min(*last_update, cur_lease->GetLastUpdate());
      *max_expire = std::min(*max_expire, cur_lease->GetMaxExpire());
      break;
    }

    auto holder = cur_lease->GetHolder();
    LOG(INFO) << "lease holder " << cur_lease->ToString() << " expired";
    processed.emplace(holder);

    auto open_files = cur_lease->CopyOpenFiles();
    lock.unlock();
    manager_->UnlockAllIndex();
    for (const auto& open_file : open_files) {
      auto ret = release_cb_(open_file, holder, internal_lease_name);
      if (ret.HasException()) {
        if (ret.exception() == JavaExceptions::Exception::kStandbyException ||
            ret.exception() == JavaExceptions::kSafeModeException) {
          continue;
        }
        if (ret.exception() == JavaExceptions::kFileNotFoundException) {
          LOG(INFO) << "file " << open_file << " has been removed";
        }
        MFC(manager_->metrics().release_lease_fail_count_)->Inc();
        VLOG(8) << "lease recover on " << open_file << " for "
                << cur_lease->GetHolder() << ". ret: " << ret.ToString();
      } else {
        MFC(manager_->metrics().release_lease_succ_count_)->Inc();
      }
    }
    manager_->LockAllIndex();
    lock.lock();

    if (sorted_leases.empty()) {
      break;
    }
    // adjust iterator for sorted_leases
    auto it = sorted_leases.begin();
    for (; it != sorted_leases.end(); ++it) {
      if (processed.find((*it)->GetHolder()) == processed.end()) {
        cur_lease = *it;
        break;
      }
    }
    if (it == sorted_leases.end()) {
      break;
    }
  }
}

}  // namespace dancenn
