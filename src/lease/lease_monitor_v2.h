// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruan<PERSON><PERSON> <<EMAIL>>
// Created: 2023/06/06
// Description:
//
// LeaseManager sorts leases by last update time from smallest to largest key.
// LeaseMonitor scans leases in that order to:
// 1. Process the most possible expired hard limit lease first.
// 2. It can stop the scan process if it encounters a lease that
//    has not expired the hard limit yet.
// 3. Find the most efficient time to wake up again.
// However, I prefer not to copy these optimizations
// to LeaseManagerV2 and LeaseMonitorV2, because a trivial implementation
// (scanning the uc table one by one) is sufficient.
// According to the following metrics, we have at most 170504 uc files and
// 1377 leases. Assuming we will have at most 200 million uc files and
// 20 thousand leases in the future, scanning them one by one is acceptable.
// We need to scan about 145MiB data from RocksDB when having 200w uc files:
// key=16386
// val={"libhdfs_client_random_20324_count_1_pid_1458_tid_1458",
//      "***************"}
// size=(8+53+15)(Byte/item)*200w(item)/1024(KiB/Byte)/1024(MiB/KiB)=145MiB
//
// Currently, we use INodeID as the RocksDB key format for the uc table.
// The advantage of this approach is that it is easy to implement.
// We simply detect if there is a uc field in the inode.
// If the uc field does not exist,
// we delete the corresponding uc key in the uc table.
// If the uc field exists,
// we write the corresponding uc key to the uc table.
//
// However, in the future, we plan to use ClientName-INodeID
// as the RocksDB key format for better efficiency.
// This has the advantage that LeaseMonitorV2 can scan the uc table
// in lease order and skip unnecessary keys.
// The disadvantage is that this approach is more complex to implement.
//
// For now, a simple implementation is sufficient given our modest data volumes.
// Once we have 200w uc files, we will migrate to
// the improved RocksDB key format.
//
// > SELECT TOP("max_value", 10), "nameservice" FROM (
//     SELECT MAX("value") as "max_value", "nameservice"
//     FROM "inf.cfs.dancenn.LeaseManager.gauges.LeasePathNum"
//     WHERE "time" > '2023-05-01T00:00:00+08:00'
//     AND "time" < '2023-06-01T00:00:00+08:00'
//     GROUP BY "nameservice"
//   ) TZ('Asia/Shanghai');
// top    nameservice
// ---    -----------
// 572    cfs-preonline-bytehousefs-root-18014398509482104
// 7333   openstudio-internal-root-18014398509482768
// 40624  bmq-xfl-root-18014398509482558
// 2850   bytelog-ms-001-sinfboe-root-18014398509485016
// 2002   beijixing-root-18014398509487299
// 170504 bmq-product-root-18014398509482549
// 1377   lasfs-pressure-bz-root-18014398509487280
// 36240  bytelog-ms-001-sinfonline-root-18014398509485015
// 4196   bytelog-xfl-online-root-18014398509482457
// 1747   bytelog-ms-001-root-18014398509484519
//
// > SELECT TOP("max_value", 10), "nameservice" FROM (
//     SELECT MAX("value") as "max_value", "nameservice"
//     FROM "inf.cfs.dancenn.LeaseManager.gauges.LeaseNum"
//     WHERE "time" > '2023-05-01T00:00:00+08:00'
//     AND "time" < '2023-06-01T00:00:00+08:00'
//     GROUP BY "nameservice"
//   ) TZ('Asia/Shanghai');
// top  nameservice
// ---  -----------
// 404  bmq-product-root-18014398509482549
// 572  cfs-preonline-bytehousefs-root-18014398509482104
// 152  cfs-preonline-lasfs-root-18014398509482115
// 431  las-pressure-test-ppe-b-root-18014398509483015
// 200  las-pressure-test-ppe-b-ns-18014398509483048
// 248  lasfs-root-18014398509482046
// 144  bytehousefs-root-18014398509482091
// 183  lasfs-ns-18014398509486661
// 362  xingfuli-recommend-root-18014398509482450
// 1377 lasfs-pressure-bz-root-18014398509487280

#ifndef LEASE_LEASE_MONITOR_V2_H_
#define LEASE_LEASE_MONITOR_V2_H_

#include <cnetpp/concurrency/thread_pool.h>  // For ThreadPool.

#include <atomic>  // For atomic.
#include <memory>  // For unique_ptr, make_unique.

#include "lease/lease_manager_v2.h"    // For LeaseManagerV2.
#include "lease/lease_monitor_base.h"  // For LeaseMonitorBase, LeaseStats.
#include "namespace/meta_storage.h"    // For MetaStorage.

namespace dancenn {

class LeaseMonitorV2 : public LeaseMonitorBase {
 public:
  LeaseMonitorV2(LeaseManagerV2* lease_manager,
                 const ReleaseCallbackType& release_callback);
  LeaseMonitorV2(const LeaseMonitorV2& other) = delete;
  LeaseMonitorV2(const LeaseMonitorV2&& other) = delete;
  LeaseMonitorV2& operator=(const LeaseMonitorV2& other) = delete;
  LeaseMonitorV2& operator=(const LeaseMonitorV2&& other) = delete;
  ~LeaseMonitorV2();

  void SetMetaStorage(MetaStorage* ms) override;
  void Start() override;
  void Stop() override;

  bool DoTask() override;

  // public for UT.
  void HandleLease(INodeID inode_id, const std::string& client_name);

 private:
  std::atomic<bool> is_running_;
  MetaStorage* meta_storage_{nullptr};
  LeaseManagerV2* lease_manager_{nullptr};
  ReleaseCallbackType release_callback_;
  std::unique_ptr<cnetpp::concurrency::ThreadPool> worker_;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MONITOR_V2_H_
