// Copyright (c) @ 2023.
// All right reserved.
//
// Author: ruanjunbin <<EMAIL>>
// Created: 2023/06/05
// Description

#ifndef LEASE_LEASE_MANAGER_V2_H_
#define LEASE_LEASE_MANAGER_V2_H_

#include <proto/generated/dancenn/inode.pb.h>  // For FileUnderConstructionFeature, INode.

#include <cstdint>        // For uint64_t.
#include <memory>         // For unique_ptr, shared_ptr, make_unique.
#include <string>         // For string.
#include <unordered_map>  // For unordered_map.
#include <vector>         // For vector.

#include "base/read_write_lock.h"      // For ReadWriteLock.
#include "base/status.h"               // For Status.
#include "lease/lease.h"               // For Lease.
#include "lease/lease_manager_base.h"  // For LeaseManagerBase, LeaseStats, etc.
#include "lease/lease_metrics.h"       // For LeaseMetrics.
#include "namespace/inode.h"           // For INodeID.
#include "namespace/meta_storage.h"    // For MetaStorage.

namespace dancenn {

class LeaseSliceV2 {
 public:
  LeaseSliceV2() = default;
  LeaseSliceV2(const LeaseSliceV2& other) = delete;
  // For LeaseManagerV2::lease_slices_.
  LeaseSliceV2(LeaseSliceV2&& other);
  LeaseSliceV2& operator=(const LeaseSliceV2& other) = delete;
  LeaseSliceV2& operator=(LeaseSliceV2&& other) = delete;

  uint64_t GetLeaseSize() const;
  void GetActiveClients(std::vector<std::string>* out) const;

  bool IsLeasePresent(const std::string& holder);
  bool IsExpiredLimit(const std::string& holder, bool use_soft_limit);
  std::shared_ptr<Lease> AddLease(const std::string& holder);
  bool TestOnlyAddLease(std::shared_ptr<Lease> lease);
  void RenewLease(const std::string& holder, const std::string& holder_ip);
  void RenewAllLeases();
  bool SpeedUpReLease(const std::string& holder);
  void RemoveLease(INodeID inode_id, const std::string& holder);
  void GC();
  void Clear();

 private:
  bool IsExpiredLimit(const Lease& lease, bool use_soft_limit) const;

 private:
  mutable ReadWriteLock rwlock_;
  // We does not use Lease::open_files_.
  std::unordered_map<std::string, std::shared_ptr<Lease>> leases_;
};

class LeaseManagerV2 : public LeaseManagerBase {
 public:
  LeaseManagerV2();

  void SetMetaStorage(MetaStorage* ms);
  void TestOnlyResetMetaStorage();
  void Start();
  void Stop();

  LeaseMetrics& metrics();
  LeaseStats Stats();
  uint64_t MetricOnlyEstimateLeaseSize() const override;
  uint64_t MetricOnlyEstimateUcFileSize() const override;
  void UpdateStats(uint64_t uc_file_cnt);
  void UpdateStats() override;
  std::vector<std::string> GetActiveClients() const override;
  DetailLeaseStats TestOnlyDetailStats(INodeID inode_id,
                                       const std::string& holder,
                                       const std::string& ip) override;

  // Should only be accessed by DoTask in lease_monitor_v2.cc.
  // DoTask and its executor should ensure
  // access to GetInternalLeaseName is thread safe.
  std::string GetInternalLeaseName();

  std::string GetLeaseHolder(INodeID inode_id,
                             bool need_lock = true) const override;

  bool CheckLease(const std::string& holder, INodeID inode_id) override;
  bool CanAddLease(const std::string& holder, INodeID inode_id) const override;
  virtual Status CanRecoverLease(INodeID inode_id,
                                 const std::string& orig_holder,
                                 const std::string& new_holder,
                                 bool force,
                                 bool allow_self_seize_lease,
                                 bool use_soft_limit,
                                 bool* need_check_last_block);
  Status CanRecoverLease(INodeID inode_id,
                         const std::string& orig_holder,
                         const std::string& new_holder,
                         bool force,
                         bool* need_check_last_block) override;

  bool AddLease(const std::string& holder, INodeID inode_id) override;
  bool TestOnlyAddLease(std::shared_ptr<Lease> lease);
  bool RemoveLease(INodeID inode_id) override;
  void RemoveAllLeases() override;
  void RenewLease(const std::string& holder,
                  const std::string& holder_ip) override;
  void RenewAllLeases() override;
  bool ReassignLease(const std::string& orig_holder,
                     INodeID inode_id,
                     const std::string& new_holder) override;

  bool SpeedUpReLease(INodeID inode_id) override;

  void GC();

 private:
  virtual bool IsActive() const;
  virtual bool IsActiveDurationSufficient(bool use_soft_limit) const;
  virtual bool ShouldPerformInlineGC() const;
  bool IsEqualTo(const std::string& lease_holder, const INode& inode) const;
  LeaseSliceV2& GetLeaseSlice(const std::string& holder);

 private:
  LeaseMetrics metrics_;
  LeaseStats lease_stats_;
  std::mutex lease_state_mutex_;

  MetaStorage* meta_storage_{nullptr};
  uint64_t start_ts_in_ms_;
  // Should only be accessed by DoTask in lease_monitor_v2.cc.
  // DoTask and its executor should ensure that
  // [access to internal_lease] is thread safe.
  // last_internal_lease_update_time expect to be 1970-01-01 by defalut.
  uint64_t last_internal_lease_update_ms_;
  std::string internal_lease_holder_;

  std::vector<LeaseSliceV2> lease_slices_;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_MANAGER_V2_H_
