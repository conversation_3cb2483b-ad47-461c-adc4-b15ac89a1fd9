// Copyright 2023 Mu <PERSON> <<EMAIL>>

#ifndef LEASE_LEASE_METRICS_H_
#define LEASE_LEASE_METRICS_H_

#include <memory>

#include "base/metric.h"
#include "base/metrics.h"
#include "lease/lease_manager_base.h"

namespace dancenn {

struct LeaseMetrics {
  explicit LeaseMetrics(LeaseManagerBase* lease_manager);

  ~LeaseMetrics() = default;
  LeaseMetrics(const LeaseMetrics&) = delete;
  LeaseMetrics& operator=(const LeaseMetrics&) = delete;

  std::shared_ptr<Gauge> lease_count_;
  std::shared_ptr<Gauge> lease_path_count_;
  MetricID scan_lease_time_;
  MetricID release_lease_succ_count_;
  MetricID release_lease_fail_count_;
};

class LeaseSliceV2Metrics {
 public:
  static LeaseSliceV2Metrics& Instance();

 private:
  LeaseSliceV2Metrics();

 public:
  MetricID gc_lease_slice_wait_lock_time_;
  MetricID gc_lease_slice_hold_lock_time_;
  MetricID gc_lease_slice_count_;
};

}  // namespace dancenn

#endif  // LEASE_LEASE_METRICS_H_