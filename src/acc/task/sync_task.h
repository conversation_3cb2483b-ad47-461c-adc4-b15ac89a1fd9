#pragma once

#include <atomic>
#include <cstdint>
#include <list>
#include <memory>

// Third
#include "cnetpp/concurrency/queue_base.h"
#include "cnetpp/concurrency/spin_lock.h"
#include "cnetpp/concurrency/thread_pool.h"

// Project
#include "acc/meta_sync_context.h"
#include "base/closure.h"
#include "base/network_location_info.h"
#include "base/status.h"
#include "base/stop_watch.h"
#include "inode.pb.h"

namespace dancenn {

using Thread = cnetpp::concurrency::Thread;
using ThreadPool = cnetpp::concurrency::ThreadPool;
using ThreadPoolTask = cnetpp::concurrency::Task;
using SpinLock = cnetpp::concurrency::SpinLock;
using SpinLockGuard = cnetpp::concurrency::SpinLock::ScopeGuard;
using QueueBase = cnetpp::concurrency::QueueBase;
using SyncCallback = std::function<void (const Status &)>;

class NameSpace;
class UfsConfig;
class Ufs;
class PermissionStatus;
class UserGroupInfo;
class MetaSyncContext;
class SyncEngine;
class ListingSyncCtrl;

class SyncTask : public ThreadPoolTask {
 public:
  SyncTask(const UfsConfig* config,
           const std::shared_ptr<Ufs>& ufs,
           const std::shared_ptr<NameSpace>& ns,
           SyncCallback cb,
           std::shared_ptr<StopWatchContext> rpc_sw_ctx = nullptr)
      : config_(config),
        ufs_(ufs),
        ns_(ns),
        cb_(std::move(cb)),
        rpc_sw_ctx_(std::move(rpc_sw_ctx)) {
  }
  ~SyncTask() override = default;

  bool operator()(void* /*arg*/ = nullptr) override {
    Status s = Sync();
    TriggerCb(s);
    return true;
  }

  void TriggerCb(const Status& s) {
    if (cb_triggered_) {
      return;
    }
    if (cb_) {
      cb_(s);
    }
    cb_triggered_ = true;
  }

  SyncCallback ReleaseCb() {
    auto cb = cb_;
    cb_ = nullptr;
    return cb;
  }

 protected:
  virtual Status Sync() = 0;

 protected:
  const UfsConfig* config_{nullptr};
  std::shared_ptr<Ufs> ufs_;
  std::shared_ptr<NameSpace> ns_;

  bool cb_triggered_{false};
  SyncCallback cb_;

  std::shared_ptr<StopWatchContext> rpc_sw_ctx_{nullptr};
};

struct ListingSyncReq {
  std::weak_ptr<ListingSyncCtrl> ctrl;
  std::shared_ptr<ListingOption> opt;
  uint64_t target_sync_ts{0};
  SyncCallback sync_cb{nullptr};
  uint32_t req_id;
  std::shared_ptr<bool> cancel_flag;

  // epoch ms
  uint64_t created_ts{0};
  uint64_t synced_file_count_on_create{0};
  std::shared_ptr<StopWatchContext> rpc_sw_ctx;

  ListingSyncReq(uint32_t id,
                 std::shared_ptr<ListingSyncCtrl> c,
                 std::shared_ptr<ListingOption> o,
                 uint64_t sync_ts,
                 std::shared_ptr<StopWatchContext> rpc_sw_ctx_in,
                 SyncCallback cb);

  bool IsCanceled() {
    return cancel_flag != nullptr && *cancel_flag;
  }
};

class ListingSyncTask : public ThreadPoolTask {
 public:
  ListingSyncTask(const UfsConfig* config,
                  const std::shared_ptr<Ufs>& ufs,
                  const std::shared_ptr<NameSpace>& ns,
                  const std::shared_ptr<ListingSyncReq>& req);

  const std::string& UfsPath() const;
  bool operator()(void* /*arg*/ = nullptr) override;

  Status Sync();

  bool IsCanceled();
  void ReportProgress(uint64_t delta_count);
  void CheckProgress();
  uint64_t GetSyncedProgressCount() const {
    return progress_count_;
  }
  const std::shared_ptr<ListingSyncReq>& GetReq() const { return req_; }
  const std::weak_ptr<ListingSyncCtrl>& GetCtrl() const { return ctrl_; }

  auto GetRpcStopWatchCtx() {
    return req_->rpc_sw_ctx;
  }

 private:
  const UfsConfig* config_{nullptr};
  std::shared_ptr<Ufs> ufs_;
  std::shared_ptr<NameSpace> ns_;

  std::shared_ptr<ListingSyncReq> req_;

  uint64_t sync_start_ts_{0};
  std::atomic<uint64_t> progress_count_{0};

  std::weak_ptr<ListingSyncCtrl> ctrl_;
};

class ListingSyncCtrl {
 public:
  ListingSyncCtrl(SyncEngine* e,
                  const std::string& ufs_path,
                  const std::string& inner_path);
  void SubmitReq(const std::shared_ptr<ListingSyncReq>& req);
  void ReportSyncTaskProgress();
  void CheckSyncTaskProgress();
  void FinishSyncTask(ListingSyncTask* task, const Status& status);

  bool IsEmpty() const {
    return req_list_.empty();
  }
  const std::string& UfsPath() const {
    return ufs_path_;
  }
  const std::string& InnerPath() const {
    return inner_path_;
  }
  void SetCancelFlag(uint32_t sync_req_id, std::shared_ptr<bool> flag) {
    std::lock_guard<std::mutex> lg(lock_);
    for (auto& req : req_list_) {
      if (req->req_id == sync_req_id) {
        req->cancel_flag = flag;
        break;
      }
    }
  }
  void SetSyncProgress(std::shared_ptr<uint64_t> sync_cnt) {
    std::lock_guard<std::mutex> lg(sync_count_lock_);
    sync_count_ = sync_cnt;
  }
  bool IsCanceled() {
    std::lock_guard<std::mutex> lg(lock_);
    for (auto& req : req_list_) {
      if (!req->IsCanceled()) {
        return false;
      }
    }
    return true;
  }
  uint32_t GenerateSyncReqId() {
    return sync_req_id_.fetch_add(1);
  }

 private:
  SyncEngine* engine_{nullptr};
  std::string ufs_path_;
  std::string inner_path_;
  std::shared_ptr<uint64_t> sync_count_;
  std::mutex sync_count_lock_;
  std::atomic<uint32_t> sync_req_id_{0};

  std::mutex lock_;
  std::list<std::shared_ptr<ListingSyncReq>> req_list_;
  std::shared_ptr<ListingSyncTask> executing_task_;
};

struct RecursiveListingSyncCtl {
    std::shared_ptr<bool> sync_done;
    std::shared_ptr<Status> status;
    std::shared_ptr<std::mutex> cv_mutex;
    std::shared_ptr<std::condition_variable> cv;
};

class FileStatusSyncTask : public SyncTask {
 public:
  FileStatusSyncTask(const UfsConfig* config,
                     const std::shared_ptr<Ufs>& ufs,
                     const std::shared_ptr<NameSpace>& ns,
                     const PermissionStatus& p,
                     const UserGroupInfo& u,
                     SyncCallback cb,
                     std::string ufs_path,
                     std::string inner_path,
                     uint64_t target_sync_ts,
                     TriggerSyncReason trigger_reason,
                     std::shared_ptr<StopWatchContext> rpc_sw_ctx);

 protected:
  Status Sync() override;

 private:
  std::string ufs_path_;
  std::string inner_path_;
  PermissionStatus perm_;
  UserGroupInfo ugi_;
  uint64_t target_sync_ts_{UINT64_MAX};
  TriggerSyncReason trigger_reason_;
};

class MkdirTask : public SyncTask {
 public:
  MkdirTask(const UfsConfig* config,
            const std::shared_ptr<Ufs>& ufs,
            const std::shared_ptr<NameSpace>& ns,
            const std::string& ufs_path,
            const std::string& inner_path,
            const PermissionStatus& p,
            const UserGroupInfo& ugi,
            bool create_parent,
            RpcController* ctx,
            SyncCallback cb);

  virtual ~MkdirTask() = default;

 protected:
  Status Sync() override;

 private:
  std::string ufs_path_;
  std::string inner_path_;
  const PermissionStatus p_;
  const UserGroupInfo ugi_;
  bool create_parent_;

  RpcController* ctx_{nullptr};
};

class CreateFileTask : public SyncTask {
 public:
  CreateFileTask(const UfsConfig* config,
                 const std::shared_ptr<Ufs>& ufs,
                 const std::shared_ptr<NameSpace>& ns,
                 const std::string& ufs_path,
                 const std::string& inner_path,
                 const PermissionStatus& p,
                 const NetworkLocationInfo& client_location,
                 const UserGroupInfo& ugi,
                 const LogRpcInfo& rpc_info,
                 const std::string& client_machine,
                 const cloudfs::CreateRequestProto* request,
                 cloudfs::CreateResponseProto* response,
                 RpcController* ctx,
                 SyncCallback cb);

  ~CreateFileTask() override = default;

 protected:
  Status Sync() override;

 private:
  std::string ufs_path_;
  std::string inner_path_;

  const PermissionStatus p_;
  const NetworkLocationInfo client_location_;
  const UserGroupInfo ugi_;
  const LogRpcInfo rpc_info_;
  const std::string client_machine_;

  const cloudfs::CreateRequestProto* request_{nullptr};
  cloudfs::CreateResponseProto* response_{nullptr};
  RpcController* ctx_{nullptr};
};

class RenameTask: public SyncTask {

 public:
  RenameTask(const UfsConfig* config,
             const std::shared_ptr<Ufs>& ufs,
             const std::shared_ptr<NameSpace>& ns,
             const std::shared_ptr<RenameToOption>& opt,
             SyncCallback cb);

 protected:
  Status Sync() override;

 private:
  std::shared_ptr<RenameToOption> opt_;
  INode src_inode_;
  INode dst_inode_;
};

class DeleteTask: public SyncTask {

 public:
  DeleteTask(const UfsConfig* config,
             const std::shared_ptr<Ufs>& ufs,
             const std::shared_ptr<NameSpace>& ns,
             const std::shared_ptr<DeleteOption>& opt,
             SyncCallback cb);

 protected:
  Status Sync() override;

 private:
  std::shared_ptr<DeleteOption> opt_;
};

}  // namespace dancenn