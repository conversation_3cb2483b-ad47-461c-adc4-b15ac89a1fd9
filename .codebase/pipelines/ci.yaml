# https://bytedance.feishu.cn/wiki/wikcnJKHXWsAcHqYzLw6XIfRGIc
# https://bytedance.feishu.cn/wiki/wikcnclt1VL6j2z0I0fFE6cdzwc
# https://bytedance.feishu.cn/wiki/wikcnrklzQNcacqHyzt8KfQAIgg
name: CI
trigger:
  change:
    branches:
      - cfs_master
      - cfs_develop
      - cnch_master
      - cfs-rel-1.5.6
  cron:
    notification:
      when:
        - success
        - failure
      to:
        - "7208514285478281220"
  manual:
    notification:
      when:
        - success
        - failure
      to:
        - "7208514285478281220"
jobs:
  test-build:
    name: Test Build
    image: &image
      hub.byted.org/compile/cfs.build.cfs_dancenn_thirdparty:1.0.0.38
    steps:
      - name: Execute build.sh
        commands:
          - ./build.sh Test
      - name: Upload third_party directory
        uses: actions/upload-artifact
        inputs:
          name: third-party-of-test-build
          path: third_party
      - name: Upload Test directory
        uses: actions/upload-artifact
        inputs:
          name: test-of-test-build
          path: Test
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-test-build
          path: builds
  release-build:
    name: Release Build
    image: *image
    steps:
      - name: Execute build.sh
        commands:
          - ./build.sh Release
      - name: Upload third_party directory
        uses: actions/upload-artifact
        inputs:
          name: third-party-of-release-build
          path: third_party
      - name: Upload Release directory
        uses: actions/upload-artifact
        inputs:
          name: release-of-release-build
          path: Release
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-release-build
          path: builds
  run-test-build-ut-1: &run-test-build-ut-1
    image: *image
    depends:
      - test-build
    name: Run Test Build UT-1
    steps:
      - &download-third-party-of-test-build
        name: Download third_party directory
        uses: actions/download-artifact
        inputs:
          name: third-party-of-test-build
          path: ./
      - &download-test-of-test-build
        name: Download Test directory
        uses: actions/download-artifact
        inputs:
          name: test-of-test-build
          path: ./
      - &download-builds-of-test-build
        name: Download builds directory
        uses: actions/download-artifact
        inputs:
          name: builds-of-test-build
          path: ./
      - name: Execute ut.sh
        commands:
          - ./ut.sh Test
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-run-test-build-ut-1
          path: builds
  run-release-build-ut-1: &run-release-build-ut-1
    image: *image
    depends:
      - release-build
    name: Run Release Build UT-1
    steps:
      - &download-third-party-of-release-build
        name: Download third_party directory
        uses: actions/download-artifact
        inputs:
          name: third-party-of-release-build
          path: ./
      - &download-release-of-release-build
        name: Download Release directory
        uses: actions/download-artifact
        inputs:
          name: release-of-release-build
          path: ./
      - &download-builds-of-release-build
        name: Download builds directory
        uses: actions/download-artifact
        inputs:
          name: builds-of-release-build
          path: ./
      - name: Execute ut.sh
        commands:
          - ./ut.sh Release
  run-test-build-ut-2:
    <<: *run-test-build-ut-1
    name: Run Test Build UT-2
    steps:
      - *download-third-party-of-test-build
      - *download-test-of-test-build
      - *download-builds-of-test-build
      - name: Execute ut2.sh
        commands:
          - ./ut2.sh Test
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-run-test-build-ut-2
          path: builds
  run-release-build-ut-2:
    <<: *run-release-build-ut-1
    name: Run Release Build UT-2
    steps:
      - *download-third-party-of-release-build
      - *download-release-of-release-build
      - *download-builds-of-release-build
      - name: Execute ut2.sh
        commands:
          - ./ut2.sh Release
  run-test-build-ut-3:
    <<: *run-test-build-ut-1
    name: Run Test Build UT-3
    steps:
      - *download-third-party-of-test-build
      - *download-test-of-test-build
      - *download-builds-of-test-build
      - name: Execute ut3.sh
        commands:
          - ./ut3.sh Test
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-run-test-build-ut-3
          path: builds
  run-release-build-ut-3:
    <<: *run-release-build-ut-1
    name: Run Release Build UT-3
    steps:
      - *download-third-party-of-release-build
      - *download-release-of-release-build
      - *download-builds-of-release-build
      - name: Execute ut3.sh
        commands:
          - ./ut3.sh Release
  run-test-build-ut-acc:
    <<: *run-test-build-ut-1
    name: Run Test Build UT-ACC
    steps:
      - *download-third-party-of-test-build
      - *download-test-of-test-build
      - *download-builds-of-test-build
      - name: Execute ./ut_acc.sh
        commands:
          - ./ut_acc.sh Test
      - name: Upload builds directory
        uses: actions/upload-artifact
        inputs:
          name: builds-of-run-test-build-ut-acc
          path: builds
  run-release-build-ut-acc:
    <<: *run-release-build-ut-1
    name: Run Release Build UT-ACC
    steps:
      - *download-third-party-of-release-build
      - *download-release-of-release-build
      - *download-builds-of-release-build
      - name: Execute ut_acc.sh
        commands:
          - ./ut_acc.sh Release
  analysis:
    name: Static Analysis
    image: *image
    depends:
      - run-test-build-ut-1
      - run-test-build-ut-2
      - run-test-build-ut-3
      - run-test-build-ut-acc
    steps:
      - name: Download builds-of-run-test-build-ut-1
        uses: actions/download-artifact
        inputs:
          name: builds-of-run-test-build-ut-1
          path: builds-of-run-test-build-ut-1
      - name: Download builds-of-run-test-build-ut-2
        uses: actions/download-artifact
        inputs:
          name: builds-of-run-test-build-ut-2
          path: builds-of-run-test-build-ut-2
      - name: Download builds-of-run-test-build-ut-3
        uses: actions/download-artifact
        inputs:
          name: builds-of-run-test-build-ut-3
          path: builds-of-run-test-build-ut-3
      - name: Download builds-of-run-test-build-ut-acc
        uses: actions/download-artifact
        inputs:
          name: builds-of-run-test-build-ut-acc
          path: builds-of-run-test-build-ut-acc
      - name: Generate coverage info
        commands:
          - lcov
              -c
              --directory builds-of-run-test-build-ut-1
              --directory builds-of-run-test-build-ut-2
              --directory builds-of-run-test-build-ut-3
              --directory builds-of-run-test-build-ut-acc
              --output-file main_coverage.info
              --rc lcov_branch_coverage=1
          - lcov
              --remove main_coverage.info
              '*third_party/*'
              '*opt/*'
              '*usr/*'
              '*.pb.h'
              '*.pb.cc'
              '*src/test/*'
              -o main_coverage.info
              --rc lcov_branch_coverage=1
      - name: codecov
        uses: actions/codecov
        inputs:
          fail_ci_if_error: true
          file: main_coverage.info
      - name: Sonar janalysis
        uses: actions/sonar@v1
        inputs:
          disable_quality_gates: true
          exclusions_list:
            - package/**
            - third_party/**
