#!/bin/bash

set -e -o pipefail
set -x

SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [ -d "$SCRIPT_DIR/.ccache" ]; then
  export CCACHE_BASEDIR=$SCRIPT_DIR
  export CCACHE_DIR=$SCRIPT_DIR/.ccache
fi

if [ "$#" -ne 1 ]; then
  echo "Usage: $0 [Test|Debug|Release]" >&2
  echo "Test: build with CMake Debug Mode and GCOV enabled." >&2
  echo "Debug: build with CMake Debug mode" >&2
  echo "Release: build with CMake RelWithDebInfo mode" >&2
  exit 1
fi

BYTECYCLE_CI=$(echo ${BYTECYCLE_CI} | tr '[:lower:]' '[:upper:]')
if [ "x${BYTECYCLE_CI}x" == "xTRUEx" ] ; then
  sed -i 's/******************:/https:\/\/code.byted.org\//' $SCRIPT_DIR/.gitmodules
  if [ -z "$TEST_CONCUR"  ]; then
    build_concur=4
  else
    build_concur=2
  fi
  echo "This is CI. concur: ${build_concur}"
else
  build_concur=$(grep -c ^processor /proc/cpuinfo)
  echo "concur: ${build_concur}"
fi

echo "Updating submodule..."
git submodule update --init --recursive
git submodule
echo "Updating submodule done!!"

if [[ -z $JAVA_HOME ]]; then
  if [[ -z $JAVA_HOME ]] && [ -x /opt/tiger/jdk/jdk1.8 ]; then
      export JAVA_HOME=(/opt/tiger/jdk/jdk1.8)
  fi

  if [[ -z $JAVA_HOME ]]; then
    echo "Error: JAVA_HOME is not set and could not be found."
    exit 1
  fi
fi

HDFS_DEPLOY="/opt/tiger/hdfs_deploy"
YARN_DEPLOY="/opt/tiger/yarn_deploy"
if [[ -d $HDFS_DEPLOY ]]; then
  echo "Use ${HDFS_DEPLOY}"
else
  echo "Link ${YARN_DEPLOY} as ${HDFS_DEPLOY}"
  ln -s ${YARN_DEPLOY} ${HDFS_DEPLOY}
fi

/opt/tiger/bvc/bin/bvc clone \
  inf/hdfs/cfs_bk_deploy \
  package/dancenn_package/jar/cfs_bk_deploy \
  --version $(cat package/dancenn_package/jar/bookkeeper-server-shaded.scm)
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/bookkeeper-server-shaded-4.6.2.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/bookkeeper-stats-api-4.6.2.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/api-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-codec-formatter-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-config-service-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-sdk-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/core-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/exporter-common-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/structured-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/unix-socket-exporter-1.0.22.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-jnr-unixsock-shade-1.0.9.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-vendor-1.0.9.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-yml-shade-1.0.9.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/config-loader-1.0.9.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/utils-1.0.9.jar package/dancenn_package/jar
mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/vendor-1.0.9.jar package/dancenn_package/jar
rm -f -r package/dancenn_package/jar/cfs_bk_deploy

/opt/tiger/bvc/bin/bvc clone \
  inf/hdfs/cfs_dancenn_thirdparty \
  third_party/cfs_dancenn_thirdparty \
  --version $(cat third_party/thirdparty.scm)

/opt/tiger/bvc/bin/bvc clone \
  data/inf/hdfs_client_debian9 \
  third_party/hdfs_client \
  --version $(cat third_party/hdfs_client.scm)
mkdir -p third_party/hdfs_client/include/hdfs
[ -f third_party/hdfs_client/include/hdfs.h ] && mv third_party/hdfs_client/include/hdfs.h third_party/hdfs_client/include/hdfs
[ -f third_party/hdfs_client/include/hdfsAsyncContext.h ] && mv third_party/hdfs_client/include/hdfsAsyncContext.h third_party/hdfs_client/include/hdfs
rm -rf third_party/hdfs_client/bin
rm -rf third_party/hdfs_client/lib/glibc-bundle-minimal

BUILD_VERSION="${BUILD_VERSION:-0.0.0}"
declare -a VERSION_ARR=($(awk -F. '{printf "%d %d %d\n", $1, $2, $3}' <<< ${BUILD_VERSION}))
MAJOR_VERSION=${VERSION_ARR[0]}
MINOR_VERSION=${VERSION_ARR[1]}
PATCH_VERSION=${VERSION_ARR[2]}

echo "Build version is ${BUILD_VERSION}, major version is ${MAJOR_VERSION}, minor version is ${MINOR_VERSION}, patch version is ${PATCH_VERSION}"

CUR_PATH=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)

BUILD_TYPE=${1:-Test}
if [ "x${BUILD_TYPE}x" != "xTestx" -a "x${BUILD_TYPE}x" != "xDebugx" -a  "x${BUILD_TYPE}x" != "xReleasex" ]; then
  echo "Only support three build type[Test|Debug|Release]: illegal build type '${BUILD_TYPE}'" >&2
  echo "Test: build with CMake Debug Mode and GCOV enabled." >&2
  echo "Debug: build with CMake Debug mode" >&2
  echo "Release: build with CMake RelWithDebInfo mode" >&2
  exit 1
else
  echo "build type: ${BUILD_TYPE}"
fi

if [ "x${BUILD_TYPE}x" == "xTestx" ]; then
    CMAKE_EXTRA_OPTIONS=" -DENABLE_GCOV=true "
    CMAKE_BUILD_TYPE="Debug"
elif [ "x${BUILD_TYPE}x" == "xDebugx" ]; then
    CMAKE_BUILD_TYPE="Debug"
else
    CMAKE_BUILD_TYPE="RelWithDebInfo"
fi

mkdir -p $BUILD_TYPE
cd ${CUR_PATH}

echo "Will make. concur: $build_concur"

echo JAVA_HOME=${JAVA_HOME}
env JAVA_HOME=${JAVA_HOME} \
  cmake -H. \
    -Bbuilds \
    -DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE ${CMAKE_EXTRA_OPTIONS} \
    -DCMAKE_CXX_COMPILER_LAUNCHER=ccache -DCMAKE_INSTALL_PREFIX=${CUR_PATH}/${BUILD_TYPE} \
    -DBUILD_VERSION=${BUILD_VERSION} \
    -DDANCENN_MAJOR_VERSION=${MAJOR_VERSION} \
    -DDANCENN_MINOR_VERSION=${MINOR_VERSION} \
    -DDANCENN_PATCH_VERSION=${PATCH_VERSION} \
    && \
  cd builds &&  \
make install -j$build_concur

pushd ../java/cfs-ranger-bridger
mvn assembly:assembly
popd
