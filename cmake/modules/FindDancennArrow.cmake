# - Find parquet
# Find the bz2 library and includes
#
# BZ2_INCLUDE_DIR - where to find header fils
# BZ2_LIBRARY - where to find libbz2.a

find_path(ARROW_INCLUDE_DIR
        NAMES arrow/buffer.h
        PATHS ${CMAKE_SOURCE_DIR}/third_party/arrow/include NO_DEFAULT_PATH)
IF(NOT ARROW_INCLUDE_DIR)
    MESSAGE(FATAL_ERROR "could not find buffer.h file")
ELSE()
    MESSAGE(STATUS "buffer.h: " ${ARROW_INCLUDE_DIR})
ENDIF()

find_library(ARROW_LIBRARY
        NAMES libarrow.a
        PATHS ${CMAKE_SOURCE_DIR}/third_party/arrow/lib NO_DEFAULT_PATH)
IF(NOT ARROW_LIBRARY)
    MESSAGE(STATUS "could not find arrow library")
ELSE()
    MESSAGE(STATUS "arrow library: " ${ARROW_LIBRARY})
ENDIF()

mark_as_advanced(ARROW_LIBRARY ARROW_INCLUDE_DIR)
