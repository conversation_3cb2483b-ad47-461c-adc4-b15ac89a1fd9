# - Find glog
# Find the glog library and includes
#
# GLOG_INCLUDE_DIR - where to find header fils
# GLOG_LIBRARY - where to find libglog.a

find_path(GLOG_INCLUDE_DIR
  NAMES glog/logging.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/glog/output/include NO_DEFAULT_PATH)
IF(NOT GLOG_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find glog/logging.h file")
ELSE()
  MESSAGE(STATUS "glog/logging.h: " ${GLOG_INCLUDE_DIR})
ENDIF()

find_library(GLOG_LIBRARY
  NAMES libglog.a glog
  PATHS ${CMAKE_SOURCE_DIR}/third_party/glog/output/lib NO_DEFAULT_PATH)
IF(NOT GLOG_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find glog library")
ELSE()
  MESSAGE(STATUS "glog library: " ${GLOG_LIBRARY})
ENDIF()

mark_as_advanced(GLOG_INCLUDE_DIR GLOG_LIBRARY)
