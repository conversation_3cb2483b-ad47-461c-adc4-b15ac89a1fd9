# - Find parquet
# Find the bz2 library and includes
#
# BZ2_INCLUDE_DIR - where to find header fils
# BZ2_LIBRARY - where to find libbz2.a

find_path(PARQUET_INCLUDE_DIR
          NAMES parquet/api/reader.h
          PATHS ${CMAKE_SOURCE_DIR}/third_party/parquet-cpp/include NO_DEFAULT_PATH)
IF(NOT PARQUET_INCLUDE_DIR)
    MESSAGE(FATAL_ERROR "could not find reader.h file")
ELSE()
    MESSAGE(STATUS "reader.h: " ${PARQUET_INCLUDE_DIR})
ENDIF()


find_library(PARQUET_LIBRARY
    NAMES libparquet.a parquet
    PATHS ${CMAKE_SOURCE_DIR}/third_party/parquet-cpp/lib NO_DEFAULT_PATH)
IF(NOT PARQUET_LIBRARY)
    MESSAGE(FATAL "could not find parquet library")
ELSE()
    MESSAGE(STATUS "parquet library: " ${PARQUET_LIBRARY})
ENDIF()

mark_as_advanced(PARQUET_INCLUDE_DIR PARQUET_LIBRARY)
