# - Find jemallo<PERSON>
# Find the jemalloc library and includes
#
# JEMALLOC_INCLUDE_DIR - where to find header fils
# JEMALLOC_LIBRARY - where to find libjemalloc.a

find_path(JEMALLOC_INCLUDE_DIR
  NAMES jemalloc/jemalloc.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/jemalloc/output/include NO_DEFAULT_PATH)
IF(NOT JEMALLOC_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find jcmalloc.h file")
ELSE()
	MESSAGE(STATUS "jcmalloc.h: " ${JEMALLOC_INCLUDE_DIR})
ENDIF()

find_library(JEMALLOC_LIBRARY
  NAMES libjemalloc.a
  PATHS ${CMAKE_SOURCE_DIR}/third_party/jemalloc/output/lib NO_DEFAULT_PATH)
IF(NOT JEMALLOC_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find jemalloc library")
ELSE()
	MESSAGE(STATUS "jemalloc library: " ${JEMALLOC_LIBRARY})
ENDIF()

mark_as_advanced(JEMALLOC_INCLUDE_DIR JEMALLOC_LIBRARY)
