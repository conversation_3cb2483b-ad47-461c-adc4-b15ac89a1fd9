# - Find protobuf
# Find the protobuf library and includes
#
# PROTOBUF_INCLUDE_DIR - where to find header fils
# PROTOBUF_LIBRARY - where to find libprotobuf.a

find_path(PROTOBUF_INCLUDE_DIR
  NAMES google/protobuf/message.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/protobuf/output/include NO_DEFAULT_PATH)
IF(NOT PROTOBUF_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find google/protobuf/message.h file")
ELSE()
  MESSAGE(STATUS "google/protobuf/message.h: " ${PROTOBUF_INCLUDE_DIR})
ENDIF()

find_library(PROTOBUF_LIBRARY
  NAMES libprotobuf.a protobuf
  PATHS ${CMAKE_SOURCE_DIR}/third_party/protobuf/output/lib NO_DEFAULT_PATH)
IF(NOT PROTOBUF_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find protobuf library")
ELSE()
  MESSAGE(STATUS "protobuf library: " ${PROTOBUF_LIBRARY})
ENDIF()

mark_as_advanced(PROTOBUF_INCLUDE_DIR PROTOBUF_LIBRARY)
