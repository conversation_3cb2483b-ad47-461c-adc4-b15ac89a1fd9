if ("${ARCH}" MATCHES "aarch64")
  message("ARM disable HDFS client")
else ()
find_path(HDFSCLIENT_INCLUDE_DIR
  NAMES hdfs/hdfs.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/hdfs_client/include NO_DEFAULT_PATH)

find_library(HDFSCLIENT_LIBRARY
  NAMES libhdfs_client.so
  PATHS ${CMAKE_SOURCE_DIR}/third_party/hdfs_client/lib NO_DEFAULT_PATH)

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(
  Hdfsclient DEFAULT_MSG
  HDFSCLIENT_LIBRARY HDFSCLIENT_INCLUDE_DIR
)

mark_as_advanced(HDFSCLIENT_INCLUDE_DIR HDFSCLIENT_LIBRARY)

endif ()