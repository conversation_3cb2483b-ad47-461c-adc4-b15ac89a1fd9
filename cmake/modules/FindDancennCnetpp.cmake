# - Find cnetpp
# Find the cnetpp library and includes
#
# CNETPP_INCLUDE_DIR - where to find header fils
# CNETPP_LIBRARY - where to find libcnetpp.a

find_path(CNETPP_INCLUDE_DIR
  NAMES cnetpp/tcp/tcp_server.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/cnetpp/output/include NO_DEFAULT_PATH)
IF(NOT CNETPP_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find cnetpp/tcp/tcp_server.h file")
ELSE()
  MESSAGE(STATUS "cnetpp/tcp/tcp_server.h: " ${CNETPP_INCLUDE_DIR})
ENDIF()

find_library(CNETPP_LIBRARY
  NAMES libcnetpp.a cnetpp
  PATHS ${CMAKE_SOURCE_DIR}/third_party/cnetpp/output/lib NO_DEFAULT_PATH)
IF(NOT CNETPP_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find cnetpp library")
ELSE()
  MESSAGE(STATUS "cnetpp library: " ${CNETPP_LIBRARY})
ENDIF()

mark_as_advanced(CNETPP_INCLUDE_DIR CNETPP_LIBRARY)
