# - Find jni
# Find the jni library and includes
#
# J<PERSON>_INCLUDE_DIR - where to find jni.h
# JNI_MD_INCLUDE_DIR - where to find jni_md.h
# JVM_LIBRARY - the path of library libjvm.so(dylib)
# JVM_LIBRARY_DIR - where to find libjvm.so(dylib)
# JAVA_LIBRARY - the path of library libjava.so(dylib)
# JAVA_LIBRARY_DIR - where to find libjava.so(dylib)

find_path(JNI_INCLUDE_DIR NAMES jni.h PATHS $ENV{JAVA_HOME}/include NO_DEFAULT_PATH)
IF (NOT JNI_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "Can not find jni.h")
ELSE()
  MESSAGE(STATUS "Found jni.h: " ${JNI_INCLUDE_DIR})
ENDIF()
IF (${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
  find_path(J<PERSON>_MD_INCLUDE_DIR NAMES jni_md.h PATHS $ENV{JAVA_HOME}/include/linux NO_DEFAULT_PATH)
  IF ("${ARCH}" MATCHES "aarch64")
    find_path(JVM_LIBRARY_DIR NAMES libjvm.so PATHS $ENV{JAVA_HOME}/jre/lib/aarch64/server NO_DEFAULT_PATH)
    find_path(JAVA_LIBRARY_DIR NAMES libjava.so PATHS $ENV{JAVA_HOME}/jre/lib/aarch64 NO_DEFAULT_PATH)
  ELSE()
    find_path(JVM_LIBRARY_DIR NAMES libjvm.so PATHS $ENV{JAVA_HOME}/jre/lib/amd64/server NO_DEFAULT_PATH)
    find_path(JAVA_LIBRARY_DIR NAMES libjava.so PATHS $ENV{JAVA_HOME}/jre/lib/amd64 NO_DEFAULT_PATH)
  ENDIF()
ELSEIF(${CMAKE_SYSTEM_NAME} STREQUAL "Darwin")
  find_path(JNI_MD_INCLUDE_DIR NAMES jni_md.h PATHS $ENV{JAVA_HOME}/include/darwin NO_DEFAULT_PATH)
  find_path(JVM_LIBRARY_DIR NAMES libjvm.dylib PATHS $ENV{JAVA_HOME}/jre/lib/server NO_DEFAULT_PATH)
  find_path(JAVA_LIBRARY_DIR NAMES libjava.dylib PATHS $ENV{JAVA_HOME}/jre/lib NO_DEFAULT_PATH)
ELSE()
  MESSAGE(FATAL_ERROR "Unsupported Operating System: " ${CMAKE_SYSTEM_NAME})
ENDIF()
IF (NOT JNI_MD_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "Can not find jni_md.h")
ELSE()
  MESSAGE(STATUS "Found jni_md.h: " ${JNI_MD_INCLUDE_DIR})
ENDIF()
IF (NOT JVM_LIBRARY_DIR)
  IF(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
    MESSAGE(FATAL_ERROR "Can not find libjvm.so")
  ELSE()
    MESSAGE(FATAL_ERROR "Can not find libjvm.dylib")
  ENDIF()
ELSE()
  IF(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
    set(JVM_LIBRARY ${JVM_LIBRARY_DIR}/libjvm.so)
    MESSAGE(STATUS "Found libjvm.so: " ${JVM_LIBRARY})
  ELSE()
    set(JVM_LIBRARY ${JVM_LIBRARY_DIR}/libjvm.dylib)
    MESSAGE(STATUS "Found libjvm.dylib: " ${JVM_LIBRARY})
  ENDIF()
ENDIF()
IF (NOT JAVA_LIBRARY_DIR)
  IF(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
    MESSAGE(FATAL_ERROR "Can not find libjava.so")
  ELSE()
    MESSAGE(FATAL_ERROR "Can not find libjava.dylib")
  ENDIF()
ELSE()
  IF(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
    set(JAVA_LIBRARY ${JAVA_LIBRARY_DIR}/libjava.so)
    MESSAGE(STATUS "Found libjava.so: " ${JAVA_LIBRARY})
  ELSE()
    set(JAVA_LIBRARY ${JAVA_LIBRARY_DIR}/libjava.dylib)
    MESSAGE(STATUS "Found libjava.dylib: " ${JAVA_LIBRARY})
  ENDIF()
ENDIF()
mark_as_advanced(JNI_INCLUDE_DIR JNI_MD_INCLUDE_DIR JVM_LIBRARY JAVA_LIBRARY JVM_LIBRARY_DIR JAVA_LIBRARY_DIR)
