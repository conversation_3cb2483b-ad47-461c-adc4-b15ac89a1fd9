# - Find sasl
# Find the sasl library and includes
#
# SASL_INCLUDE_DIR - where to find header fils
# SASL_LIBRARY - where to find libsasl2.a

find_path(SASL_INCLUDE_DIR
  NAMES sasl/sasl.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/cyrus-sasl-2.1.21/output/include NO_DEFAULT_PATH)
IF(NOT SASL_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find sasl/sasl.h file")
ELSE()
  MESSAGE(STATUS "sasl/sasl.h: " ${SASL_INCLUDE_DIR})
ENDIF()

find_library(SASL_LIBRARY
  NAMES libsasl2.a sasl
  PATHS ${CMAKE_SOURCE_DIR}/third_party/cyrus-sasl-2.1.21/output/lib NO_DEFAULT_PATH)
IF(NOT SASL_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find sasl library")
ELSE()
  MESSAGE(STATUS "sasl library: " ${SASL_LIBRARY})
ENDIF()

mark_as_advanced(SASL_INCLUDE_DIR SASL_LIBRARY)

