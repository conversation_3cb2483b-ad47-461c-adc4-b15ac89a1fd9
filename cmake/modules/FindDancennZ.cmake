# - Find zlib
# Find the zlib library and includes
#
# Z_INCLUDE_DIR - where to find header fils
# Z_LIBRARY - where to find libz.a

find_path(Z_INCLUDE_DIR
  NAMES zlib.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zlib/output/include NO_DEFAULT_PATH)
IF(NOT Z_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find zlib.h file")
ELSE()
  MESSAGE(STATUS "zlib.h: " ${Z_INCLUDE_DIR})
ENDIF()

find_library(Z_LIBRARY
  NAMES libz.a z
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zlib/output/lib NO_DEFAULT_PATH)
IF(NOT Z_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find z library")
ELSE()
  MESSAGE(STATUS "z library: " ${Z_LIBRARY})
ENDIF()

mark_as_advanced(Z_INCLUDE_DIR Z_LIBRARY)
