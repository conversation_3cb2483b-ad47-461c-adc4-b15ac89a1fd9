# - Find snappy
# Find the snappy library and includes
#
# SNAPPY_INCLUDE_DIR - where to find header fils
# SNAPPY_LIBRARY - where to find libsnappy.a

find_path(THRIFT_INCLUDE_DIR
        NAMES thrift/Thrift.h
        PATHS ${CMAKE_SOURCE_DIR}/third_party/thrift/include NO_DEFAULT_PATH)
IF(NOT THRIFT_INCLUDE_DIR)
    MESSAGE(FATAL_ERROR "could not find thrift.h file")
ELSE()
    MESSAGE(STATUS "thrift.h: " ${THRIFT_INCLUDE_DIR})
ENDIF()

find_library(THRIFT_LIBRARY
        NAMES libthrift.a
        PATHS ${CMAKE_SOURCE_DIR}/third_party/thrift/lib NO_DEFAULT_PATH)
IF(NOT SNAPPY_LIBRARY)
    MESSAGE(FATAL_ERROR "could not find snappy library")
ELSE()
    MESSAGE(STATUS "snappy library: " ${SNAPPY_LIBRARY})
ENDIF()

mark_as_advanced(SNAPPY_INCLUDE_DIR SNAPPY_LIBRARY)
