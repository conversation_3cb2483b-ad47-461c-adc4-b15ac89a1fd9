# - Find snappy
# Find the snappy library and includes
#
# SNAPPY_INCLUDE_DIR - where to find header fils
# SNAPPY_LIBRARY - where to find libsnappy.a

find_path(SNAPPY_INCLUDE_DIR
  NAMES snappy.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/snappy/output/include NO_DEFAULT_PATH)
IF(NOT SNAPPY_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find snappy.h file")
ELSE()
  MESSAGE(STATUS "snappy.h: " ${SNAPPY_INCLUDE_DIR})
ENDIF()

find_library(SNAPPY_LIBRARY
  NAMES libsnappy.a snappy
  PATHS ${CMAKE_SOURCE_DIR}/third_party/snappy/output/lib NO_DEFAULT_PATH)
IF(NOT SNAPPY_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find snappy library")
ELSE()
  MESSAGE(STATUS "snappy library: " ${SNAPPY_LIBRARY})
ENDIF()

mark_as_advanced(SNAPPY_INCLUDE_DIR SNAPPY_LIBRARY)
