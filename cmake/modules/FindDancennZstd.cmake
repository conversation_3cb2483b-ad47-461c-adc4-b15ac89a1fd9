# - Find zstd
# Find the zstd library and includes
#
# ZSTD_INCLUDE_DIR - where to find header fils
# ZSTD_LIBRARY - where to find libzstd.a

find_path(ZSTD_INCLUDE_DIR
  NAMES zstd.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zstd/output/include NO_DEFAULT_PATH)
IF(NOT ZSTD_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find zstd.h file")
ELSE()
  MESSAGE(STATUS "zstd.h: " ${ZSTD_INCLUDE_DIR})
ENDIF()

find_library(ZSTD_LIBRARY
  NAMES libzstd.a zstd
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zstd/output/lib NO_DEFAULT_PATH)
IF(NOT ZSTD_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find zstd library")
ELSE()
  MESSAGE(STATUS "zstd library: " ${ZSTD_LIBRARY})
ENDIF()

mark_as_advanced(ZSTD_INCLUDE_DIR ZSTD_LIBRARY)
