# - Find parquet
# Find the bz2 library and includes
#
# BZ2_INCLUDE_DIR - where to find header fils
# BZ2_LIBRARY - where to find libbz2.a

find_path(BOOST_INCLUDE_DIR
        NAMES boost/regex.h
        PATHS ${CMAKE_SOURCE_DIR}/third_party/boost/include NO_DEFAULT_PATH)
IF(NOT BOOST_INCLUDE_DIR)
    MESSAGE(FATAL_ERROR "could not find align.hpp file")
ELSE()
    MESSAGE(STATUS "align.hpp: " ${BOOST_INCLUDE_DIR})
ENDIF()


find_library(BOOST_REGEX_LIBRARY
        NAMES libboost_regex.a
        PATHS ${CMAKE_SOURCE_DIR}/third_party/boost/lib NO_DEFAULT_PATH)
IF(NOT BOOST_REGEX_LIBRARY)
    MESSAGE(FATAL_ERROR "could not find boost regex library")
ELSE()
    MESSAGE(STATUS "boost library: " ${BOOST_REGEX_LIBRARY})
ENDIF()


mark_as_advanced(BOOST_INCLUDE_DIR BOOST_REGEX_LIBRARY)
