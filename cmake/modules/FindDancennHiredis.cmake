# - Find hiredis
# Find the hiredis library and includes
#
# HIREDIS_INCLUDE_DIR - where to find header fils
# HIREDIS_LIBRARY - where to find libhire<PERSON>.a

find_path(HIREDIS_INCLUDE_DIR
  NAMES hiredis/hiredis.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/hiredis/output/include NO_DEFAULT_PATH)
IF(NOT HIREDIS_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find hiredis/hiredis.h file")
ELSE()
  MESSAGE(STATUS "hiredis/hiredis.h: " ${HIREDIS_INCLUDE_DIR})
ENDIF()

find_library(HIREDIS_LIBRARY
  NAMES libhiredis.a hiredis
  PATHS ${CMAKE_SOURCE_DIR}/third_party/hiredis/output/lib NO_DEFAULT_PATH)
IF(NOT HIREDIS_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find libhire<PERSON>.a library")
ELSE()
  MESSAGE(STATUS "hiredis library: " ${HIREDIS_LIBRARY})
ENDIF()

mark_as_advanced(HIREDIS_INCLUDE_DIR HIREDIS_LIBRARY)

