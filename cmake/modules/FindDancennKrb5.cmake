# - Find krb5
# Find the krb5 library and includes
#
# KRB5_INCLUDE_DIR - where to find header fils
# KRB5_LIBRARY - where to find libkrb5.a

find_path(KRB5_INCLUDE_DIR
  NAMES krb5.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/krb5/output/include NO_DEFAULT_PATH)
IF(NOT KRB5_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find krb5.h file")
ELSE()
  MESSAGE(STATUS "krb5.h: " ${KRB5_INCLUDE_DIR})
ENDIF()

find_library(KRB5_LIBRARY
  NAMES libkrb5.a krb5
  PATHS ${CMAKE_SOURCE_DIR}/third_party/krb5/output/lib NO_DEFAULT_PATH)
IF(NOT KRB5_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find krb5 library")
ELSE()
  MESSAGE(STATUS "krb5 library: " ${KRB5_LIBRARY})
ENDIF()

mark_as_advanced(KRB5_INCLUDE_DIR KRB5_LIBRARY)

