# - Find lz<PERSON>
# Find the lzma library and includes
#
# LZMA_INCLUDE_DIR - where to find header fils
# LZMA_LIBRARY - where to find liblzma.a

find_path(LZMA_INCLUDE_DIR
  NAMES lzma.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/xz-5.2.3/output/include NO_DEFAULT_PATH)
IF(NOT LZMA_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find lzma.h file")
ELSE()
  MESSAGE(STATUS "lzma.h: " ${LZMA_INCLUDE_DIR})
ENDIF()

find_library(LZMA_LIBRARY
  NAMES liblzma.a lzma
  PATHS ${CMAKE_SOURCE_DIR}/third_party/xz-5.2.3/output/lib NO_DEFAULT_PATH)
IF(NOT LZMA_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find lzma library")
ELSE()
  MESSAGE(STATUS "lzma library: " ${LZMA_LIBRARY})
ENDIF()

mark_as_advanced(LZMA_INCLUDE_DIR LZMA_LIBRARY)
