# - Find gflags
# Find the gflags library and includes
#
# GFLAGS_INCLUDE_DIR - where to find header fils
# GFLAGS_LIBRARY - where to find libgflags.a

find_path(GFLAGS_INCLUDE_DIR
  NAMES gflags/gflags.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/gflags/output/include NO_DEFAULT_PATH)
IF(NOT GFLAGS_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find gflags/gflags.h file")
ELSE()
  MESSAGE(STATUS "gflags/gflags.h: " ${GFLAGS_INCLUDE_DIR})
ENDIF()

find_library(GFLAGS_LIBRARY
  NAMES libgflags.a gflags
  PATHS ${CMAKE_SOURCE_DIR}/third_party/gflags/output/lib NO_DEFAULT_PATH)
IF(NOT GFLAGS_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find gflags library")
ELSE()
  MESSAGE(STATUS "gflags library: " ${GFLAGS_LIBRARY})
ENDIF()

mark_as_advanced(GFLAGS_INCLUDE_DIR GFLAGS_LIBRARY)
