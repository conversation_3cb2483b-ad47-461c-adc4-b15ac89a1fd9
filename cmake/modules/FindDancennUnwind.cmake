# - Find unwind
# Find the unwind library and includes
#
# UN<PERSON><PERSON>_INCLUDE_DIR - where to find header fils
# UNWIND_LIBRARY - where to find libunwind.a

IF (${CMAKE_SYSTEM_NAME} MATCHES "Linux")
  find_path(UNWIND_INCLUDE_DIR
    NAMES libunwind.h
    PATHS ${CMAKE_SOURCE_DIR}/third_party/libunwind/output/include NO_DEFAULT_PATH)
  IF(NOT UNWIND_INCLUDE_DIR)
    MESSAGE(FATAL_ERROR "could not find libunwind.h file")
  ELSE()
    MESSAGE(STATUS "libunwind.h: " ${UNWIND_INCLUDE_DIR})
  ENDIF()

  find_library(UNWIND_LIBRARY
    NAMES libunwind.a unwind
    PATHS ${CMAKE_SOURCE_DIR}/third_party/libunwind/output/lib NO_DEFAULT_PATH)
  IF(NOT UNWIND_LIBRARY)
    MESSAGE(FATAL_ERROR "could not find unwind library")
  ELSE()
    MESSAGE(STATUS "unwind library: " ${UNWIND_LIBRARY})
  ENDIF()
ELSE()
  set(UNWIND_INCLUDE_DIR "")
  set(UNWIND_LIBRARY "")
ENDIF()

mark_as_advanced(UNWIND_INCLUDE_DIR UNWIND_LIBRARY)
