# - Find rocksdb
# Find the rocksdb library and includes
#
# ROCKSDB_INCLUDE_DIR - where to find header fils
# ROCKSDB_LIBRARY - where to find librocksdb.a

find_path(ROCKSDB_INCLUDE_DIR
  NAMES rocksdb/db.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/rocksdb/output/include NO_DEFAULT_PATH)
IF(NOT ROCKSDB_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find rocksdb/db.h file")
ELSE()
  MESSAGE(STATUS "rocksdb/db.h: " ${ROCKSDB_INCLUDE_DIR})
ENDIF()

find_library(ROCKSDB_LIBRARY
  NAMES librocksdb.a rocksdb
  PATHS ${CMAKE_SOURCE_DIR}/third_party/rocksdb/output/lib NO_DEFAULT_PATH)
IF(NOT ROCKSDB_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find rocksdb library")
ELSE()
  MESSAGE(STATUS "rocksdb library: " ${ROCKSDB_LIBRARY})
ENDIF()

mark_as_advanced(ROCKSDB_INCLUDE_DIR ROCKSDB_LIBRARY)
