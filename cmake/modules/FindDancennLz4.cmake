# - Find lz4
# Find the lz4 library and includes
#
# LZ4_INCLUDE_DIR - where to find header fils
# LZ4_LIBRARY - where to find liblz4.a

find_path(LZ4_INCLUDE_DIR
  NAMES lz4.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/lz4/output/include NO_DEFAULT_PATH)
IF(NOT LZ4_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find lz4.h file")
ELSE()
  MESSAGE(STATUS "lz4.h: " ${LZ4_INCLUDE_DIR})
ENDIF()

find_library(LZ4_LIBRARY
  NAMES liblz4.a lz4
  PATHS ${CMAKE_SOURCE_DIR}/third_party/lz4/output/lib NO_DEFAULT_PATH)
IF(NOT LZ4_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find lz4 library")
ELSE()
  MESSAGE(STATUS "lz4 library: " ${LZ4_LIBRARY})
ENDIF()

mark_as_advanced(LZ4_INCLUDE_DIR LZ4_LIBRARY)
