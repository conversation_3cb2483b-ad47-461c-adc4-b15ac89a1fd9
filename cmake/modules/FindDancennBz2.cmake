# - Find bz2
# Find the bz2 library and includes
#
# BZ2_INCLUDE_DIR - where to find header fils
# BZ2_LIBRARY - where to find libbz2.a

find_path(BZ2_INCLUDE_DIR
  NAMES bzlib.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/bzip2/output/include NO_DEFAULT_PATH)
IF(NOT BZ2_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find bzlib.h file")
ELSE()
  MESSAGE(STATUS "bzlib.h: " ${BZ2_INCLUDE_DIR})
ENDIF()

find_library(BZ2_LIBRARY
  NAMES libbz2.a bz2
  PATHS ${CMAKE_SOURCE_DIR}/third_party/bzip2/output/lib NO_DEFAULT_PATH)
IF(NOT BZ2_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find bz2 library")
ELSE()
  MESSAGE(STATUS "bz2 library: " ${BZ2_LIBRARY})
ENDIF()

mark_as_advanced(BZ2_INCLUDE_DIR BZ2_LIBRARY)
