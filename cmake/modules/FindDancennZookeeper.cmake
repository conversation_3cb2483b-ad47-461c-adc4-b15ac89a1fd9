# - Find zookeeper
# Find the zookeeperlibrary and includes
#
# Z<PERSON><PERSON>EEPER_INCLUDE_DIR - where to find header fils
# ZOOKEEPER_LIBRARY - where to find libzookeeper.a

find_path(ZOOKEEPER_INCLUDE_DIR
  NAMES zookeeper/zookeeper.h
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zookeeper/output/include NO_DEFAULT_PATH)
IF(NOT ZOOKEEPER_INCLUDE_DIR)
  MESSAGE(FATAL_ERROR "could not find zookeeper/zookeeper.h file")
ELSE()
  MESSAGE(STATUS "zookeeper/zookeeper.h: " ${ZOOKEEPER_INCLUDE_DIR})
ENDIF()

find_library(ZOOKEEPER_LIBRARY
  NAMES libzookeeper_mt.a zookeeper
  PATHS ${CMAKE_SOURCE_DIR}/third_party/zookeeper/output/lib NO_DEFAULT_PATH)
IF(NOT ZOOKEEPER_LIBRARY)
  MESSAGE(FATAL_ERROR "could not find zookeeper library")
ELSE()
  MESSAGE(STATUS "zookeeper library: " ${<PERSON><PERSON><PERSON>EEPER_LIBRARY})
ENDIF()

mark_as_advanced(<PERSON>O<PERSON><PERSON><PERSON>ER_INCLUDE_DIR ZOOKEEPER_LIBRARY)

