#!/bin/bash
BASE_DIR=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
CONF_DIR=$(cd ${BASE_DIR}/../conf;pwd)

source ${CONF_DIR}/dancenn_env
cd ${BASE_DIR}

data_dir="${BASE_DIR}/dancenn_run_local.dir"
rocksdb_dir=${data_dir}/rocksdb
name_dir=${data_dir}/nndata
edit_dir=${name_dir}/current

[ -d ${data_dir} ] && rm -fr ${data_dir:?"undefined!!"}
mkdir -p ${rocksdb_dir}
mkdir -p ${edit_dir}

subst_next dfs.namenode.shared.edits.dir ${name_dir} ${CONF_DIR}/hdfs-site.xml
subst_next dfs.namenode.edits.dir ${name_dir} ${CONF_DIR}/hdfs-site.xml
subst_next dfs.name.dir ${name_dir} ${CONF_DIR}/hdfs-site.xml

subst_right --namespace_meta_storage_path ${rocksdb_dir} ${CONF_DIR}/dancenn_flags_local.conf
subst_right --fsimage_dir ${edit_dir} ${CONF_DIR}/dancenn_flags_local.conf

$BASE_DIR/dancenn --flagfile=${CONF_DIR}/dancenn_flags_local.conf --java_classpath=${CLASSPATH}
