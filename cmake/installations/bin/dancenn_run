#!/usr/bin/env python

import sys
import os
import stat

with open(sys.argv[3], "w") as f:
  f.write("#!/bin/sh\n")
  f.write("ulimit -c 500000000000\n")
  f.write("BIN_PATH=\"`dirname $0`\"\n")
  f.write("CONF_PATH=\"`dirname $0`/../conf\"\n")
  f.write("export LD_LIBRARY_PATH="+sys.argv[1]+":"+sys.argv[2]+":$LD_LIBRARY_PATH\n")
  f.write("$BIN_PATH/dancenn --flagfile=$CONF_PATH/dancenn_flags.conf\n")

os.chmod(sys.argv[3], stat.S_IRUSR | stat.S_IWUSR | stat.S_IXUSR | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
