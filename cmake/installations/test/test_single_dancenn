#!/bin/bash

set -e -o pipefail

BASE_DIR=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
cd ${BASE_DIR}

kill_all(){
	set +e +o pipefail
	local name=${1:?"undefined name"};shift
	if which killall; then
		killall -9 ${name}
	fi
	local pid=$(ps h -C ${name} -o pid)
	[ -n "${pid}" ] && kill -9 ${pid} 
	set -e -o pipefail
}

cleanup(){
	set +e +o pipefail
	kill_all dancenn
	data_dir=${BASE_DIR:?"undefined"}/../bin/dancenn_run_local.dir
	[ -d $data_dir ] && rm -fr ${data_dir:?"undefined"}
	set -e -o pipefail
}

finally(){
	local last_status=$?
	trap "" EXIT
	cleanup
	if [ $last_status -ne 0 ];then
		echo "ERROR"
		exit 1
	else
		echo "OK"
		exit 0
	fi  
	set -e -o pipefail
}

check(){
	local cmd=${1:?"undefind 'cmd'"}; shift
	local kw=${1:?"undefined 'kw'"}; shift
	local n=${1:?"undefined 'occurrence'"};shift
	local nn=$(${cmd} | grep -c ${kw})
	test ${nn} -eq ${n}
}

cleanup
trap finally EXIT


nohup ${BASE_DIR}/../bin/dancenn_run_local >dancenn.log 2>&1 &
sleep 10

pid=$(ps h -C dancenn -o pid)
test -n "${pid}"
kill -0 ${pid}

${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToActive
sleep 5
${BASE_DIR}/hdfs_local -mkdir -p hdfs://127.0.0.1:5060/a/b/c/d/e/f/g/0xdeadbeef
${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/a/b/c/d/e/f/g/ |grep 0xdeadbeef
${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToStandby
sleep 2
${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToActive
${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/a/b/c/d/e/f/g/ |grep 0xdeadbeef
${BASE_DIR}/hdfs_local -rm -r hdfs://127.0.0.1:5060/a
for i in {0..2};do
	${BASE_DIR}/hdfs_local -mkdir -p hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef$i
done

check "${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/x/y/z/"   0xdeadbeef 3

for i in {0..2};do
	${BASE_DIR}/hdfs_local -rm -r hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef$i 
	check "${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/x/y/z/" 0xdeadbeef $((2-$i))
done

${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToStandby
sleep 10
${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToActive
sleep 5
${BASE_DIR}/hdfs_local -mkdir -p hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef4
${BASE_DIR}/hdfs_local -rm -r hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef4
check "${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/x/y/z/" 0xdeadbeef4 0

${BASE_DIR}/hdfs_local -mkdir -p hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef5
${BASE_DIR}/hdfs_local -mv  hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef5 hdfs://127.0.0.1:5060/x/y/z/0xdeadbeef6
check "${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/x/y/z/" 0xdeadbeef5 0
check "${BASE_DIR}/hdfs_local -ls hdfs://127.0.0.1:5060/x/y/z/" 0xdeadbeef6 1

${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToStandby
sleep 10
${BASE_DIR}/dancenn_haadmin_local --nn_host=127.0.0.1 --nn_port=5061 --transitionToActive
sleep 5

