#!/bin/bash
BASE_DIR=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
CONF_DIR=$(cd ${BASE_DIR}/../conf;pwd)
source ${CONF_DIR}/dancenn_env

port=$(echo "x$1x" |perl -ne 'print $1 if /^x(\d+)x$/')
jdb_opt=""
if [ -n "${port}" ];then
jdb_opt="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=${port}"
shift;
fi

cd ${BASE_DIR}

java ${jdb_opt} -Dproc_dfs -Xmx4000m -Djava.net.preferIPv4Stack=true -Xms512m -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+UseNUMA -XX:-UseGCOverheadLimit -Dhadoop.log.dir=${PWD} -Dhadoop.log.file=hadoop.log -Dhadoop.home.dir=${HDFS_DEPLOY}/hadoop -Dhadoop.id.str=tiger -Dhadoop.root.logger=INFO,console,databus -Djava.library.path=${HDFS_DEPLOY}/hadoop/lib/native:/lib/native::${HDFS_DEPLOY}/hadoop/lib/native -Dhadoop.policy.file=hadoop-policy.xml -Djava.net.preferIPv4Stack=true -Dhadoop.root.logger=WARN,console -Dhadoop.security.logger=INFO,NullAppender org.apache.hadoop.fs.FsShell $@
