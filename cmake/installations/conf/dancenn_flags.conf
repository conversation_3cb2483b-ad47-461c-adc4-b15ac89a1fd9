--client_rpc_handler_count=32
--client_rpc_network_thread_count=0
--client_rpc_port=5060
--ha_rpc_port=5061
--datanode_rpc_port=5062
--http_port=5070
--client_rpc_recv_buffer_size=0
--client_rpc_send_buffer_size=0
--client_rpc_tcp_recv_buffer_size=32768
--client_rpc_tcp_send_buffer_size=32768
--datanode_rpc_handler_count=32
--datanode_rpc_network_thread_count=0
--datanode_rpc_recv_buffer_size=0
--datanode_rpc_send_buffer_size=0
--datanode_rpc_tcp_recv_buffer_size=32768
--datanode_rpc_tcp_send_buffer_size=32768
--datanode_keep_alive_timeout_sec=60
--datanode_manager_thread_num=5
--block_placement_policy=default
--alsologtostderr
--namespace_meta_storage_path=/tmp/test/rocksdb
--dfs_block_size=536870912
--dfs_replication=3
--dfs_bytes_per_checksum=4096
--dfs_client_write_packet_size=65536
--dfs_encrypt_data_transfer=false
--fs_trash_interval=10080
--dfs_checksum_type=CRC32C
--v=9
--fsimage_dir=/data01/yarn/nndata/current
--java_classpath=/opt/tiger/hdfs_deploy/hadoop/conf:/opt/tiger/dancenn_deploy/jar/bookkeeper-server-4.5.0.jar:/opt/tiger/dancenn_deploy/jar/bookkeeper-stats-api-4.5.0.jar:/opt/tiger/dancenn_deploy/jar/netty-all-4.1.12.Final.jar:/opt/tiger/dancenn_deploy/jar/protobuf-java-3.0.0.jar:/opt/tiger/dancenn_deploy/jar/guava-23.0.jar:/opt/tiger/hdfs_deploy/hadoop-2.6.0-cdh5.4.4/share/hadoop/hdfs/hadoop-hdfs-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/activation-1.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/apacheds-i18n-2.0.0-M15.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/apacheds-kerberos-codec-2.0.0-M15.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/api-asn1-api-1.0.0-M20.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/api-util-1.0.0-M20.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/asm-3.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/avro-1.7.6-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-beanutils-1.7.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-beanutils-core-1.8.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-cli-1.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-codec-1.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-collections-3.2.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-compress-1.4.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-configuration-1.6.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-digester-1.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-el-1.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-httpclient-3.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-io-2.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-lang-2.6.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-logging-1.1.3.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-math3-3.1.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/commons-net-3.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/curator-client-2.7.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/curator-framework-2.7.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/curator-recipes-2.7.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/gson-2.2.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/guava-11.0.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/hadoop-annotations-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/hadoop-auth-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/hamcrest-core-1.3.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/htrace-core-3.0.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/httpclient-4.2.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/httpcore-4.2.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-core-asl-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-jaxrs-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-mapper-asl-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-xc-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jasper-compiler-5.5.23.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jasper-runtime-5.5.23.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/java-xmlbuilder-0.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jaxb-api-2.2.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jaxb-impl-2.2.3-1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-core-1.9.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-json-1.9.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-server-1.9.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jets3t-0.9.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jettison-1.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jetty-6.1.26.cloudera.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jetty-util-6.1.26.cloudera.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jsch-0.1.54.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jsp-api-2.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/jsr305-3.0.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/junit-4.11.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/log4j-1.2.17.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/logredactor-1.0.3.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/mockito-all-1.8.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/netty-3.6.10.Final.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/paranamer-2.3.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/protobuf-java-2.5.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/servlet-api-2.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/slf4j-api-1.7.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/slf4j-log4j12-1.7.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/snappy-java-1.1.2.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/stax-api-1.0-2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/xmlenc-0.52.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/xz-1.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/lib/zookeeper-3.4.5-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/databus4j-1.0.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-brotli-0.0.1-SNAPSHOT.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-common-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-common-2.6.0-cdh5.4.4-tests.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-lzo-0.4.20-SNAPSHOT.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-nfs-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-xz-1.5-byted.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/hadoop-zstd-1.0.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/common/json-serde-1.3-jar-with-dependencies.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/bec.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-aws-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-2.6.0-cdh5.4.4-tests.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-bkjournal-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-nfs-2.6.0-cdh5.4.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/asm-3.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-cli-1.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-codec-1.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-daemon-1.0.13.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-el-1.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-io-2.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-lang-2.6.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-logging-1.1.3.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/guava-11.0.2.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/htrace-core-3.0.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jackson-core-asl-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jackson-mapper-asl-1.8.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jasper-runtime-5.5.23.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jersey-core-1.9.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jersey-server-1.9.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jetty-6.1.26.cloudera.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jetty-util-6.1.26.cloudera.4.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jsp-api-2.1.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jsr305-3.0.0.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/leveldbjni-all-1.8.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/log4j-1.2.17.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/servlet-api-2.5.jar:/opt/tiger/hdfs_deploy/hadoop/share/hadoop/hdfs/lib/xmlenc-0.52.jar
--dc_topology_file=/opt/tiger/dancenn_deploy/conf/datacenters_topology.conf

