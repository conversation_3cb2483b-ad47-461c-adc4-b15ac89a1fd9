#!/bin/bash
if [ -z "${BYTECYCLE_CI}" ]; then
    ulimit -c 500000000000
fi

colon_join(){
   	perl -ne 'chomp;push @_,$_}{print join ":",@_'
}

CONF_DIR=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)

CLASSPATH=${CONF_DIR}:${CONF_DIR}/conf
CLASSPATH=${CLASSPATH}:$(cd ${CONF_DIR}/../conf;pwd)

JAR_DIR=$(cd ${CONF_DIR}/../jar;pwd)
jars=$(ls ${JAR_DIR}/*.jar|colon_join)
CLASSPATH=${CLASSPATH}:${jars}

DANCENN_PARENT_DIR=$(cd ${CONF_DIR}/../../../;pwd)
DANCENN_INSTALL_DIR=$(cd ${CONF_DIR}/../;pwd)
HDFS_DEPLOY=${DANCENN_PARENT_DIR}/cfs_hdfs_deploy

if [ ! -d ${HDFS_DEPLOY} ];then
	[ -d /opt/tiger/cfs_hdfs_deploy ] && ln -sf /opt/tiger/cfs_hdfs_deploy ${HDFS_DEPLOY}
fi

for dir in $(cat);do
	jars=$(ls ${HDFS_DEPLOY}/${dir}/*.jar|colon_join)
	CLASSPATH=${CLASSPATH}:${jars}
done <<-'DONE'
hadoop/share/hadoop/common
hadoop/share/hadoop/common/lib
hadoop/share/hadoop/hdfs
hadoop/share/hadoop/hdfs/lib
DONE

export CLASSPATH
DEFAULT_JAVA_HOME="/opt/tiger/jdk/jdk1.8"
[ -d ${DEFAULT_JAVA_HOME} ] && JAVA_HOME=${DEFAULT_JAVA_HOME}

export JAVA_HOME=${JAVA_HOME:?"undefined JAVA_HOME"}
export PATH=${JAVA_HOME}/bin:${PATH}
export LD_LIBRARY_PATH=${DANCENN_INSTALL_DIR}/lib:${JAVA_HOME}/jre/lib/amd64/server:${JAVA_HOME}/jre/lib/amd64:${JAVA_HOME}/jre/lib/server:${JAVA_HOME}/jre/lib/:$LD_LIBRARY_PATH

subst_next() {
  local key=${1:?"undefined key"};shift
  local value=${1:?"undefined value"};shift
  local file=${1:?"undefined hdfs-site-xml"};shift
  perl -i -pe "\$v=q{${value}};s{<value>.*</value>}{<value>\$v</value>}g if \$pre =~ m/${key}/; \$pre=\$_" ${file}
}

subst_right() {
  local key=${1:?"undefined key"};shift
  local value=${1:?"undefined value"};shift
  local file=${1:?"undefined hdfs-site-xml"};shift
  perl -i -pe "\$v=q{${value}};s{^${key}=(.*)\$}{${key}=\$v}g if /^${key}/" ${file}
}
